const express = require('express');
const path = require('path');
const { createProxyMiddleware } = require('http-proxy-middleware');
const config = require('../config');

const app = express();
const port = 8082;

// 设置静态文件路径
app.use('/static', express.static(path.join(__dirname, '../dist/static')));

// 注册代理中间件
Object.keys(config?.dev?.proxyTable).forEach(context => {
    app.use(context, createProxyMiddleware(config?.dev?.proxyTable[context]));
});

// 配置 HTML 文件的路由
app.get('/module/:page', (req, res) => {
  const page = req.params.page;
  const filePath = path.join(__dirname, '../dist/module', page);
  res.sendFile(filePath);
});

// 处理 404 错误，返回主页面
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/module/index.html'));
});

app.listen(port, () => {
    console.log(`Server is running on http://localhost:${port}`);
});
