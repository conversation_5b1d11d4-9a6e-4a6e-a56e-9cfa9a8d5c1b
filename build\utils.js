const path = require('path');
const config = require('../config');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

exports.assetsPath = function (_path) {
  const assetsSubDirectory = process.env.NODE_ENV === 'production'
    ? config.build.assetsSubDirectory
    : config.dev.assetsSubDirectory;
  return path.posix.join(assetsSubDirectory, _path);
};

exports.cssLoaders = function (options = {}) {
  const cssLoader = {
    loader: 'css-loader',
    options: {
      sourceMap: options.sourceMap,
      esModule: false,
      modules: {
        localIdentName: '[local]__[name]___[contenthash:base64:5]',
        auto: (__resourcePath, resourceQuery, __resourceFragment) =>{
          return /module/.test(resourceQuery)
        }
      },
      importLoaders: 2, // Ensures that @imported files are processed by both postcss-loader and sass-loader, etc.
      ...options?.cssLoadersOptions?options.cssLoadersOptions:{}
    }
  };

  function generateLoaders(loader, loaderOptions) {
    const loaders = [cssLoader];

    if (loader) {
      loaders.push({
        loader: `${loader}-loader`,
        options: Object.assign({}, loaderOptions, {
          sourceMap: options.sourceMap
        })
      });
    }

    if(options.extract) {
      return [{
        loader:MiniCssExtractPlugin.loader
      }].concat(loaders);
    }
    return ['vue-style-loader'].concat(loaders);
  }

  return {
    css: generateLoaders(),
    postcss: generateLoaders(),
    less: generateLoaders('less'),
    sass: generateLoaders('sass', { indentedSyntax: true }),
    scss: generateLoaders('sass'),
    stylus: generateLoaders('stylus'),
    styl: generateLoaders('stylus')
  };
};

exports.styleLoaders = function (options) {
  const loaders = exports.cssLoaders(options);
  return Object.keys(loaders).map(extension => ({
    test: new RegExp('\\.' + extension + '$'),
    use: loaders[extension]
  }));
};
