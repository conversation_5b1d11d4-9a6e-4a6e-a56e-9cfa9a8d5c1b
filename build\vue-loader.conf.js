const utils = require('./utils');
const config = require('../config');
const isProduction = process.env.NODE_ENV === 'production';

const loaders = utils.cssLoaders({
  sourceMap: isProduction
    ? config.build.productionSourceMap
    : config.dev.cssSourceMap,
  extract: isProduction
})

module.exports = {
  loaders,
  cssSourceMap: isProduction
    ? config.build.productionSourceMap
    : config.dev.cssSourceMap,
  cacheBusting: config.dev.cacheBusting,
  transformToRequire: {
    video: ['src', 'poster'],
    source: 'src',
    img: 'src',
    image: 'xlink:href'
  },
  compilerOptions: {
    preserveWhitespace: false
  }
};
