const path = require('path');
const utils = require('./utils');
const config = require('../config');
const vueLoaderConfig = require('./vue-loader.conf');
const glob = require('glob');
const CopyWebpackPlugin = require('copy-webpack-plugin');
// Function to resolve directory paths
function resolve(dir) {
  return path.join(__dirname, '..', dir);
}

// Get entries for multiple entry points
const entries = getEntry(resolve('./src/module/**/*.js'));
// Object.keys(entries).forEach((name) => {
//   entries[name] = [
//     'eventsource-polyfill',
//     ...entries[name]
//   ];
// });

module.exports = {
  entry: entries,
  output: {
    path: config.build.assetsRoot,
    filename: '[name].js',
    publicPath: process.env.NODE_ENV === 'production'
      ? config.build.assetsPublicPath
      : config.dev.assetsPublicPath,
    clean: true // Automatically clean the output directory before each build
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: {
      'vue$': 'vue/dist/vue.esm.js',
      '@': resolve('src'),
      '@components': resolve('src/components'),
      '@module': resolve('src/module'),
      '@stylus': resolve('src/assets/stylus'),
      '@common': resolve('src/common'),
      '@router': resolve('src/router')
    },
    fallback: {
      "fs": false,
      "path": false,
      "os": false,
      "zlib": require.resolve("browserify-zlib"),
      "assert": false,
      "util": false,
      "stream": require.resolve("stream-browserify"),
      "querystring": false,
      "async_hooks": false,
      // Add other fallbacks if necessary
    }
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: vueLoaderConfig
      },
      {
        test: /\.js$/,
        loader: 'babel-loader',
        include: [resolve('src')],
        exclude: /node_modules/
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 10 * 1024 // 10 KB
          }
        },
        generator: {
          filename: utils.assetsPath('images/[name].[hash:7][ext]')
        }
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        type: 'asset/resource',
        generator: {
          filename: utils.assetsPath('fonts/[name].[hash:7][ext]')
        }
      },
    ]
  },
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, '../static'),
          to: path.resolve(__dirname, '../dist/static'),
          globOptions: {
            ignore: ['.*']
          }
        }
      ]
    })
  ]
}

// Function to retrieve entry points
function getEntry(globPath) {
  const entries = {};
  glob.sync(globPath).forEach(function (entry) {
    const basename = path.basename(entry, path.extname(entry));
    const tmp = entry.split('/').splice(-3);
    const pathname = `${tmp.splice(0, 1)}/${basename}`; // Correct output for js and html paths
    entries[pathname] = ['eventsource-polyfill',entry];
  });
  return entries;
}
