const path = require('path');
const utils = require('./utils');
const webpack = require('webpack');
const { merge } = require('webpack-merge');
const baseWebpackConfig = require('./webpack.base.conf');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const glob = require('glob');
const config = require('../config');
const VueLoaderPlugin = require('vue-loader/lib/plugin');

// 获取 HTML 入口点
const htmlEntries = getEntry(path.join(__dirname, '../src/module/**/*.html'));

// 为每个入口点添加热更新相关的代码
Object.keys(baseWebpackConfig.entry).forEach((name) => {
  if (!Array.isArray(baseWebpackConfig.entry[name])) {
    baseWebpackConfig.entry[name] = [baseWebpackConfig.entry[name]];
  }

  // 检查是否已经包含热加载模块，避免重复添加
  if (!baseWebpackConfig.entry[name].includes('webpack-hot-middleware/client?noInfo=true&reload=true')) {
    baseWebpackConfig.entry[name].unshift('webpack-hot-middleware/client?noInfo=true&reload=true');
  }
});

module.exports = merge(baseWebpackConfig, {
  mode: 'development',
  module: {
    rules: utils.styleLoaders({ 
      sourceMap: config.dev.cssSourceMap, 
      ...config?.cssLoadersOptions ? {cssLoadersOptions:config.cssLoadersOptions}:{},
      extract: false 
    })
  },
  devtool: 'eval-source-map',
  output: {
    path: config.build.assetsRoot,
    filename: utils.assetsPath('js/[name].js'),
    chunkFilename: utils.assetsPath('js/[name].js'), // 与生产环境保持一致的chunk命名
    publicPath: config.dev.assetsPublicPath
  },
  optimization: {
    runtimeChunk: 'single',
    splitChunks: {
      chunks: 'all',
      minSize: 20000, // 与生产环境保持一致
      maxSize: 244000, // 与生产环境保持一致
      cacheGroups: {
        // 与生产环境完全一致的代码分割策略
        elementUI: {
          test: /[\\/]node_modules[\\/]element-ui[\\/]/,
          name: 'element-ui',
          chunks: 'all',
          priority: 20
        },
        echarts: {
          test: /[\\/]node_modules[\\/]echarts[\\/]/,
          name: 'echarts',
          chunks: 'all',
          priority: 20
        },
        moment: {
          test: /[\\/]node_modules[\\/]moment[\\/]/,
          name: 'moment',
          chunks: 'all',
          priority: 20
        },
        utils: {
          test: /[\\/]node_modules[\\/](xlsx|jszip|file-saver|ali-oss)[\\/]/,
          name: 'utils',
          chunks: 'all',
          priority: 15
        },
        vue: {
          test: /[\\/]node_modules[\\/](vue|vuex|vue-router|vue-resource|vue-axios)[\\/]/,
          name: 'vue-libs',
          chunks: 'all',
          priority: 15
        },
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10
        },
        styles: {
          name: 'styles',
          test: /\.css$/,
          chunks: 'all',
          enforce: true,
          priority: 5
        }
      }
    }
  },
  devServer: {
    port: config.dev.port,
    hot: true,
    open: config.dev.autoOpenBrowser, // 自动打开浏览器
    historyApiFallback: true, // 支持HTML5 History API
    client: {
      logging: 'info', // 控制台显示信息
      overlay: true, // 编译出现错误时，全屏显示错误
    },
    proxy: config.dev.proxyTable, // 代理设置
    static: {
      directory: path.join(__dirname, '../static'), // 配置 static 目录
      publicPath: config.dev.assetsPublicPath,
    },
  },
  plugins: [
    new VueLoaderPlugin(),
    new webpack.DefinePlugin({
      'process.env': JSON.stringify(config.dev.env)
    }),
    // 与生产环境一致：忽略moment.js的语言包
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/
    }),
    new webpack.HotModuleReplacementPlugin(),
    new webpack.NoEmitOnErrorsPlugin(),
    ...Object.keys(htmlEntries).map(name => new HtmlWebpackPlugin({
      filename: `${name}.html`,
      template: htmlEntries[name],
      inject: 'body',
      // 与生产环境完全一致的chunks配置
      chunks: [name, 'runtime', 'vue-libs', 'element-ui', 'echarts', 'moment', 'utils', 'vendors', 'styles'],
      minify: {
        removeComments: true,
        collapseWhitespace: false // 开发环境保持可读性
      }
    }))
  ]
});

// 动态获取入口文件
function getEntry(globPath) {
  const entries = {};

  glob.sync(globPath).forEach((filePath) => {
    const basename = path.basename(filePath, path.extname(filePath));
    const tmp = filePath.split('/').splice(-3);
    const pathname = tmp.splice(0, 1) + '/' + basename; // 正确输出js和html的路径
    entries[pathname] = filePath;
  });

  return entries;
}