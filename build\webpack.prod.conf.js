const path = require('path');
const utils = require('./utils');
const webpack = require('webpack');
const config = require('../config');
const { merge } = require('webpack-merge');
const baseWebpackConfig = require('./webpack.base.conf');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const glob = require('glob');
const VueLoaderPlugin = require('vue-loader/lib/plugin');

// const env = config.prod.env;

const webpackConfig = (env) => {
  const mergeConfig = merge(baseWebpackConfig, {
    mode: env.development ? "development" : "production",
    module: {
      rules: utils.styleLoaders({
        sourceMap: config.build.productionSourceMap,
        ...config?.build?.cssLoadersOptions ? { cssLoadersOptions: config.build.cssLoadersOptions}:{},
        extract: true,
      })
    },
    devtool: config.build.productionSourceMap ? 'source-map' : false,
    output: {
      path: config.build.assetsRoot,
      filename: utils.assetsPath('js/[name].[contenthash].js'),
      chunkFilename: utils.assetsPath('js/[id].[contenthash].js')
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          },
          styles: {
            name: 'styles',
            test: /\.css$/,
            chunks: 'all',
            enforce: true
          }
        }
      },
      runtimeChunk: 'single',
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true, // 去除 console 语句
            },
          },
          parallel: true, // 开启多线程构建提高性能
        }),
        new OptimizeCSSAssetsPlugin({
          cssProcessorOptions: {
            safe: true,
            map: { inline: false }
          }
        })
      ]
    },
    plugins: [
      new VueLoaderPlugin(),
      new webpack.DefinePlugin({
        'process.env': env.development ? JSON.stringify(config.dev.env) : JSON.stringify(config.build.env)
      }),
      new MiniCssExtractPlugin({
        filename: utils.assetsPath('css/[name].[contenthash].css'),
        // chunkFilename: utils.assetsPath('css/[id].[contenthash].css')
      }),
      new CopyWebpackPlugin({
        patterns: [
          {
            from: path.resolve(__dirname, '../static'),
            to: config.build.assetsSubDirectory,
            globOptions: {
              ignore: ['.*']
            }
          }
        ]
      }),
      ...generateHtmlPlugins('./src/module/**/*.html')
    ]
  });

  if (config.build.productionGzip) {
    const CompressionWebpackPlugin = require('compression-webpack-plugin');

    mergeConfig.plugins.push(
      new CompressionWebpackPlugin({
        filename: '[path][base].gz',
        algorithm: 'gzip',
        test: new RegExp(
          '\\.(' +
          config.build.productionGzipExtensions.join('|') +
          ')$'
        ),
        threshold: 10240,
        minRatio: 0.8
      })
    );
  }
  return mergeConfig;
}

// 动态获取入口文件
function getEntry(globPath) {
  const entries = {};

  glob.sync(globPath).forEach((entry) => {
    const basename = path.basename(entry, path.extname(entry));
    const tmp = entry.split('/').splice(-3);
    const pathname = `${tmp.splice(0, 1)}/${basename}`; // 正确输出 js 和 html 的路径
    entries[pathname] = entry;
  });
  return entries;
}

// 生成 HTML 插件
function generateHtmlPlugins(globPath) {
  const pages = getEntry(globPath);
  const plugins = [];
  for (const pathname in pages) {
    const conf = {
      filename: `${pathname}.html`,
      template: pages[pathname],
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeAttributeQuotes: true
      },
      inject: 'body',
      chunks: [pathname, 'vendors', 'manifest']
    };

    plugins.push(new HtmlWebpackPlugin(conf));
  }
  
  return plugins;
}

module.exports = webpackConfig;
