// see http://vuejs-templates.github.io/webpack for documentation.
var path = require('path')

module.exports = {
  build: {
    env: require('./prod.env'),
    index: path.resolve(__dirname, '../dist/index.html'),
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    productionSourceMap: false,
    cssLoadersOptions: {
        url: true
    },
    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: true,
    productionGzipExtensions: ['js', 'css'],
    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  },
  dev: {
    env: require('./dev.env'),
    port: 8081,
    autoOpenBrowser: true,
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
        '/order-web/': {
        //target: 'http://************:38090',
            // target: 'http://************:40020',
            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/dealer-web/': {
            // target: 'http://************:40020',
            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/afterSale-web/': {
            // target: 'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/report-web/api/report': {
            // target: 'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/permission-web':{
            // target:'http://************:40000',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin:true,
            pathRewrite:{}
        },
        '/user-web':{//用户
            // target:'http://************:40000',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin:true,
            pathRewrite:{

            }
        },
        '/customer-web': {
            // target: 'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {
                '/customer-web':'/order-web'
            }
        },
        '/login-web/sso/login':{
            // target: 'http://************:40000',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/api/file':{
            // target: 'http://10.10.14.159:40098',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/file-web/':{
            // target:'http://10.10.14.159:40098',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/material-web/':{
            // target:'http://************:40000',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/price-web/':{
            // target:'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/kingdee-web/':{
            // target:'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/after-web/':{
            // target:'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/afterSale-web/':{
        //   target:'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
          changeOrigin: true,
          pathRewrite: {}
        },
        '/file-iweb/':{
            // target:'http://************:40100',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/afterSale-web/':{
            // target:'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
		'/reports-web/':{
            // target:'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/plan-web/':{
            // target:'http://************:40020',

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        "/mdm-web/": {

            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        "/app-web/": {
            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        "/external-web/": {
            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        "/custom-web/": {
          target: 'http://salesit.linshimuye.com:83',
          changeOrigin: true,
          pathRewrite: {}
        },
        "/callcenter-web/": {
            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        "/encryption-web/": {
            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        "/crm-web/": {
            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        "/fund-web/": {
            target: 'http://salesit.linshimuye.com:83',
            changeOrigin: true,
            pathRewrite: {}
        },
        '/sys-amcs': {
          target: 'https://agent.xinyaoai.com:1443',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/sys-amcs/, '/sys-amcs'),
        },
        '^/janus': {
          target: 'wss://agent.xinyaoai.com:1443/janus',
          changeOrigin: true,
          secure: false,
          ws: true,
          rewrite: (path) => path.replace(/^\/janus/, '/janus'),
        },
    },
    // CSS Sourcemaps off by default because relative paths are "buggy"
    // with this option, according to the CSS-Loader README
    // (https://github.com/webpack/css-loader#sourcemaps)
    // In our experience, they generally work as expected,
    // just be aware of this issue when enabling this option.
    cssSourceMap: true
  }
}
