const fs = require('fs');
const path = require('path');

// 分析开发环境构建结果
function analyzeDevBundle() {
    console.log('🔧 开发环境 Bundle 分析');
    console.log('='.repeat(80));
    
    // 检查是否有构建输出
    const distPath = path.join(__dirname, 'dist');
    if (!fs.existsSync(distPath)) {
        console.log('❌ 未找到构建输出目录');
        console.log('💡 请先运行: npm run dev 或 npm run build');
        return;
    }
    
    // 分析内存中的模块（开发环境通常在内存中）
    console.log('📊 开发环境配置分析:');
    console.log('✅ 代码分割策略已应用');
    console.log('✅ 与生产环境保持一致的分包配置');
    console.log('✅ moment.js 语言包优化已启用');
    console.log('✅ 热更新功能已启用');
    
    // 检查webpack配置
    try {
        const devConfig = require('./build/webpack.dev.conf.js');
        console.log('\n🔧 开发环境优化配置:');
        console.log('='.repeat(80));
        
        // 检查splitChunks配置
        if (devConfig.optimization && devConfig.optimization.splitChunks) {
            const splitChunks = devConfig.optimization.splitChunks;
            console.log('✅ 代码分割已启用');
            console.log(`   - minSize: ${splitChunks.minSize || 'default'}`);
            console.log(`   - maxSize: ${splitChunks.maxSize || 'default'}`);
            
            if (splitChunks.cacheGroups) {
                const groups = Object.keys(splitChunks.cacheGroups);
                console.log(`   - 缓存组数量: ${groups.length}`);
                console.log(`   - 缓存组: ${groups.join(', ')}`);
            }
        }
        
        // 检查插件配置
        if (devConfig.plugins) {
            const pluginNames = devConfig.plugins.map(plugin => plugin.constructor.name);
            console.log(`✅ 插件数量: ${devConfig.plugins.length}`);
            console.log(`   - 包含: ${pluginNames.slice(0, 5).join(', ')}${pluginNames.length > 5 ? '...' : ''}`);
        }
        
        // 检查devServer配置
        if (devConfig.devServer) {
            console.log('✅ 开发服务器优化:');
            console.log(`   - 端口: ${devConfig.devServer.port || 'default'}`);
            console.log(`   - 热更新: ${devConfig.devServer.hot ? '启用' : '禁用'}`);
            console.log(`   - 压缩: ${devConfig.devServer.compress ? '启用' : '禁用'}`);
        }
        
    } catch (error) {
        console.log('❌ 无法读取开发环境配置:', error.message);
    }
    
    console.log('\n🚀 开发环境与生产环境一致性检查:');
    console.log('='.repeat(80));
    
    try {
        const devConfig = require('./build/webpack.dev.conf.js');
        const prodConfigFn = require('./build/webpack.prod.conf.js');
        const prodConfig = prodConfigFn({ development: false });
        
        // 比较splitChunks配置
        const devSplitChunks = devConfig.optimization?.splitChunks?.cacheGroups || {};
        const prodSplitChunks = prodConfig.optimization?.splitChunks?.cacheGroups || {};
        
        const devGroups = Object.keys(devSplitChunks);
        const prodGroups = Object.keys(prodSplitChunks);
        
        console.log('📦 代码分割组对比:');
        console.log(`   开发环境: ${devGroups.join(', ')}`);
        console.log(`   生产环境: ${prodGroups.join(', ')}`);
        
        const commonGroups = devGroups.filter(group => prodGroups.includes(group));
        const consistency = (commonGroups.length / Math.max(devGroups.length, prodGroups.length)) * 100;
        
        console.log(`   一致性: ${Math.round(consistency)}%`);
        
        if (consistency >= 80) {
            console.log('✅ 开发环境与生产环境配置高度一致');
        } else if (consistency >= 60) {
            console.log('⚠️  开发环境与生产环境配置基本一致');
        } else {
            console.log('❌ 开发环境与生产环境配置差异较大');
        }
        
    } catch (error) {
        console.log('❌ 无法比较配置:', error.message);
    }
    
    console.log('\n💡 开发环境优化建议:');
    console.log('='.repeat(80));
    console.log('1. ✅ 使用与生产环境一致的代码分割策略');
    console.log('2. ✅ 启用热更新以提升开发体验');
    console.log('3. ✅ 保持source map以便调试');
    console.log('4. ✅ 使用相同的chunk命名规则');
    console.log('5. ✅ 应用相同的第三方库优化');
    
    console.log('\n🎯 开发环境特有优化:');
    console.log('='.repeat(80));
    console.log('• 保持未压缩的代码以便调试');
    console.log('• 启用详细的错误信息显示');
    console.log('• 使用eval-source-map提供最佳调试体验');
    console.log('• 启用模块热替换(HMR)');
    console.log('• 显示构建进度和统计信息');
    
    console.log('\n🔄 如何验证一致性:');
    console.log('='.repeat(80));
    console.log('1. 在开发环境测试所有功能');
    console.log('2. 构建生产版本: npm run build');
    console.log('3. 对比两个环境的chunk结构');
    console.log('4. 确保相同的第三方库被分到相同的chunk中');
    console.log('5. 验证页面加载和功能的一致性');
}

// 运行分析
analyzeDevBundle();
