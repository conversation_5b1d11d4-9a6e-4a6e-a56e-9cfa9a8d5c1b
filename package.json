{"name": "xpt", "version": "1.0.0", "description": "A Vue.js project", "author": "Bellzzy <<EMAIL>>", "private": true, "scripts": {"dev": "webpack serve --config build/webpack.dev.conf.js --stats-error-details --env development", "start": "webpack serve --config build/webpack.dev.conf.js --env development", "build:sit": "webpack --config build/webpack.prod.conf.js --stats-error-details --env development", "build": "webpack --config build/webpack.prod.conf.js --stats-error-details --env production", "preview": "node build/server.js", "postinstall": "patch-package"}, "dependencies": {"address-smart-parse": "^2.0.17", "ali-oss": "^6.7.0", "async-validator": "1.7.1", "awe-dnd": "^0.3.4", "axios": "^0.19.2", "echarts": "^5.2.1", "element-ui": "1.4.13", "file-saver": "^2.0.2", "js-md5": "^0.7.3", "jszip": "^3.4.0", "l-watermark": "^2.1.2", "loadsh": "0.0.4", "moment": "^2.24.0", "nodemon": "^1.18.8", "patch-package": "^8.0.0", "sa-sdk-javascript": "^1.26.14", "stylus": "0.54.5", "stylus-loader": "^3.0.2", "vue": "^2.6.10", "vue-axios": "^2.1.5", "vue-json-viewer": "^2.2.11", "vue-resource": "1.3.1", "vuex": "^2.5.0", "vxe-table": "3.2.9", "wangeditor": "^4.5.2", "xe-utils": "^3.0.3", "xinyao-call-sdk": "1.1.64", "xlsx": "^0.16.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.25.3", "autoprefixer": "^10.4.0", "babel-core": "^6.22.1", "babel-loader": "^9.1.3", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.2.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "browserify-zlib": "^0.2.0", "chalk": "^4.1.2", "compression-webpack-plugin": "^11.1.0", "connect-history-api-fallback": "^2.0.0", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.33.3", "cors": "^2.8.5", "css-loader": "^6.10", "cssnano": "^4.1.11", "eventsource-polyfill": "^0.9.6", "express": "^4.17.1", "file-loader": "^6.2.0", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^5.3.2", "http-proxy-middleware": "^2.0.0", "less-loader": "^7.1.0", "mini-css-extract-plugin": "^0.8.2", "node-glob": "^1.2.0", "opn": "^6.0.0", "optimize-css-assets-webpack-plugin": "^5.0.4", "ora": "^5.4.1", "postcss-loader": "^3.0.0", "postcss-preset-env": "^10.0.2", "postcss-url": "^10.1.3", "resolve-url-loader": "^5.0.0", "rimraf": "^3.0.2", "sass-loader": "^13.3.2", "semver": "^7.3.5", "shelljs": "^0.8.4", "stream-browserify": "^2.0.2", "style-loader": "^4.0.0", "url-loader": "^4.1.1", "vue-loader": "15", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.7.15", "vxe-table-plugin-element": "^4.0.4", "webpack": "^5.88.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-middleware": "^5.3.3", "webpack-dev-server": "^4.15.1", "webpack-hot-middleware": "^2.25.0", "webpack-merge": "5.10.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "keywords": ["林氏木业新平台"], "license": "ISC"}