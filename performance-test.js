const fs = require('fs');
const path = require('path');

// 分析dist目录中的文件大小
function analyzeBundle() {
    const distPath = path.join(__dirname, 'dist/static/js');
    
    if (!fs.existsSync(distPath)) {
        console.log('❌ dist目录不存在，请先运行构建命令');
        return;
    }
    
    const files = fs.readdirSync(distPath)
        .filter(file => file.endsWith('.js') && !file.endsWith('.gz'))
        .map(file => {
            const filePath = path.join(distPath, file);
            const stats = fs.statSync(filePath);
            return {
                name: file,
                size: stats.size,
                sizeKB: Math.round(stats.size / 1024 * 100) / 100,
                sizeMB: Math.round(stats.size / 1024 / 1024 * 100) / 100
            };
        })
        .sort((a, b) => b.size - a.size);
    
    console.log('📊 Bundle 分析结果:');
    console.log('='.repeat(80));
    
    let totalSize = 0;
    let vendorFiles = [];
    let appFiles = [];
    
    files.forEach((file, index) => {
        totalSize += file.size;
        
        if (file.name.includes('vendors') || file.name.includes('element-ui') || 
            file.name.includes('utils') || file.name.includes('echarts') || 
            file.name.includes('moment') || file.name.includes('vue-libs')) {
            vendorFiles.push(file);
        } else {
            appFiles.push(file);
        }
        
        if (index < 10) { // 只显示前10个最大的文件
            const sizeStr = file.sizeMB > 0 ? `${file.sizeMB}MB` : `${file.sizeKB}KB`;
            console.log(`${(index + 1).toString().padStart(2)}. ${file.name.padEnd(35)} ${sizeStr.padStart(10)}`);
        }
    });
    
    console.log('='.repeat(80));
    console.log(`📦 总文件数: ${files.length}`);
    console.log(`📏 总大小: ${Math.round(totalSize / 1024 / 1024 * 100) / 100}MB`);
    console.log(`🏪 第三方库文件: ${vendorFiles.length} 个`);
    console.log(`📱 应用代码文件: ${appFiles.length} 个`);
    
    const vendorSize = vendorFiles.reduce((sum, file) => sum + file.size, 0);
    const appSize = appFiles.reduce((sum, file) => sum + file.size, 0);
    
    console.log(`🏪 第三方库总大小: ${Math.round(vendorSize / 1024 / 1024 * 100) / 100}MB`);
    console.log(`📱 应用代码总大小: ${Math.round(appSize / 1024 / 1024 * 100) / 100}MB`);
    
    // 检查gzip文件
    const gzipFiles = fs.readdirSync(distPath)
        .filter(file => file.endsWith('.js.gz'))
        .map(file => {
            const filePath = path.join(distPath, file);
            const stats = fs.statSync(filePath);
            const originalFile = file.replace('.gz', '');
            const originalStats = fs.existsSync(path.join(distPath, originalFile)) 
                ? fs.statSync(path.join(distPath, originalFile))
                : null;
            
            return {
                name: file,
                size: stats.size,
                sizeKB: Math.round(stats.size / 1024 * 100) / 100,
                originalSize: originalStats ? originalStats.size : 0,
                compressionRatio: originalStats ? Math.round((1 - stats.size / originalStats.size) * 100) : 0
            };
        })
        .sort((a, b) => b.size - a.size);
    
    if (gzipFiles.length > 0) {
        console.log('\n🗜️  Gzip 压缩效果:');
        console.log('='.repeat(80));
        
        gzipFiles.slice(0, 5).forEach((file, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. ${file.name.replace('.gz', '').padEnd(35)} ${file.sizeKB.toString().padStart(8)}KB (压缩率: ${file.compressionRatio}%)`);
        });
        
        const totalGzipSize = gzipFiles.reduce((sum, file) => sum + file.size, 0);
        const totalOriginalSize = gzipFiles.reduce((sum, file) => sum + file.originalSize, 0);
        const overallCompressionRatio = Math.round((1 - totalGzipSize / totalOriginalSize) * 100);
        
        console.log('='.repeat(80));
        console.log(`🗜️  总压缩效果: ${Math.round(totalGzipSize / 1024 / 1024 * 100) / 100}MB (压缩率: ${overallCompressionRatio}%)`);
    }
    
    // 性能建议
    console.log('\n💡 性能建议:');
    console.log('='.repeat(80));
    
    const largeFiles = files.filter(file => file.sizeMB > 1);
    if (largeFiles.length > 0) {
        console.log('⚠️  发现大文件 (>1MB):');
        largeFiles.forEach(file => {
            console.log(`   - ${file.name}: ${file.sizeMB}MB`);
        });
        console.log('   建议: 考虑进一步拆分或使用CDN');
    } else {
        console.log('✅ 所有文件大小都在合理范围内 (<1MB)');
    }
    
    if (files.length > 50) {
        console.log('⚠️  文件数量较多，可能影响HTTP/1.1性能');
        console.log('   建议: 在HTTP/2环境下使用，或适当合并小文件');
    } else {
        console.log('✅ 文件数量合理');
    }
    
    console.log('\n🎉 优化完成！主要改进:');
    console.log('   ✅ 实施了精细的代码分割');
    console.log('   ✅ 分离了大型第三方库');
    console.log('   ✅ 启用了gzip压缩');
    console.log('   ✅ 优化了代码压缩配置');
    console.log('   ✅ 减少了moment.js语言包');
}

// 运行分析
analyzeBundle();
