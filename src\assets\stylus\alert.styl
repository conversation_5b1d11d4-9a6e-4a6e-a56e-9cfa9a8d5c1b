.xpt-alert
	position: fixed
	top: 0
	left: 0
	right: 0
	bottom: 0
	z-index: 12
	.alert-body
		position: absolute
		background-color: #fff
		box-shadow: 0 0 20px rgba(0, 0, 0, .5)
		font-size: 14px
		top:50%
		left: 50%
		border-radius: 4px
		-webkit-transform:translate(-50%,-50%)
		-moz-transform:translate(-50%,-50%)
		-ms-transform:translate(-50%,-50%)
		-o-transform:translate(-50%,-50%)
		transform:translate(-50%,-50%)
		.alert-body_title
			border-radius: 4px 0 0 0
			background-color:#4f5f6f
			color:#fff
			padding:0 15px
			position:absolute
			top:0
			left:0
			right: 60px
			height:30px
			line-height:30px
			z-index:2
			font-weight: 500
		.alert-body_close
			border-radius: 0 4px 0 0
			background-color:#4f5f6f
			color: #fff
			float: right
			width: 30px
			height: 30px
			line-height: 30px
			text-align:center
			z-index: 2
			i
				cursor:pointer
				margin-top:10px
		.alert-body_zoom
			border-radius: 0
		.alert-body_main
			padding: 0 15px 35px 15px
			position: absolute
			z-index: 2
			top: 30px
			left: 0
			bottom: 0
			width: 100%
