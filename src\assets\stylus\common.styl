.xpt-search__btn
	height:36px
	text-align:right
	line-height:36px
	button
		width:100px
/*头部栏*/
.xpt-top
	padding:2px 10px
	border-bottom:2px solid #d1dbe5
	margin-bottom:10px
	margin-left: -10px !important
	margin-right: -10px !important
	line-height:24px
	.el-col
		padding:0 !important
	.el-col-4
		padding-right:0 !important
	.el-button
		vertical-align:middle
	.xpt-close
		float:right
		margin-top:3px
	.xpt-top__search
		width:100px
		float:right
		cursor:pointer
		span
			&:after
				content:''
				width:0
				height:0
				display:inline-block
				border-top:6px solid #bfcbd9
				border-left:6px solid transparent
				border-right:6px solid transparent
				margin-left:5px
			&.active
				color:#20a0ff
				&:after
					border-bottom:6px solid #bfcbd9
					border-top:none
/*
按钮组样式
*/ 
.xpt-btngroup
	position:relative
	display:inline-block
	// height:22px
	vertical-align:top
	.el-icon-caret-bottom
		transition:all .3s
	.xpt-btngroup__caret
		width:20px
		min-width:inherit
	.xpt-btngroup__list
		width:100%
		position:absolute
		top:100%
		left:0
		width:100%
		z-index:10
		margin-top:-2px
		.el-button
			display:block
			width:100%
			margin:0
			border-radius:0
		button:last-child
			border-bottom-left-radius:4px
			border-bottom-right-radius:4px
	&.active
		.el-button-group
			button:first-child
				border-bottom-left-radius:0
			button:last-child
				border-bottom-right-radius:0
		.el-icon-caret-bottom
			transform:rotate(180deg)
.xpt-main
	min-width:1200px
	margin:5px auto 0 auto
	position:absolute
	top:50px
	left:0
	width:100%
	bottom:0
.xpt-right
	position: absolute !important
	right: 10px
	top: 2px

.xpt-tabcard
	width:100%
	>.el-tabs__header
		>.el-tabs__nav-wrap
			margin: 0 100px
	.xpt-search
		margin-bottom:10px
	/*
	卡片内容面板	
	*/ 
	>.el-tabs__content
		position:absolute
		top:25px
		left:0
		right:0
		bottom:0
		padding-bottom:32px
		overflow:hidden
		min-width:100%
		&.scroll
			overflow:auto
		>.el-tab-pane
			height:100%
			>div
				padding:0 10px 0 10px
/*
分页，底部浮动，靠右对齐
*/ 
.xpt-pagation
	position:fixed
	bottom:0
	left:0
	width:100%
	height:32px
	padding:4px 0
	background:#fff
	z-index:9
	.el-pagination
		float:right
		margin-right:10px
		*
			height:24px !important
			line-height:24px
/*
用于垂直布局，
xpt-flex__bottom部分自适应
*/
.xpt-flex
	width:100%
.xpt-flex,.el-table
	height:99% !important
	display:flex
	min-width:100%
	box-orient:vertical
	flex-direction:column
	overflow: hidden;
	.el-table__body-wrapper
		flex:1
		.el-table__empty-block
			height:auto
	.xpt-flex__bottom
		flex:1
		position:relative
		min-width:100%
		overflow:hidden
		.xpt-top
			margin-left: 0 !important
			margin-right: 0 !important
		&.scroll
			overflow:auto;
		.el-tabs
			height:100%
			display:flex
			box-orient:vertical
			flex-direction:column
			.el-tabs__content
				flex:1
				overflow:auto

/*
右对齐
*/ 
.xpt-align__right
	text-align:right
/*
	表单校验错误提示框样式
*/ 
.xpt-form__error.el-tooltip__popper 
	background:#ff4949 !important
	.popper__arrow
		top:10px !important
		border-right-color:#ff4949 !important
		border-left-color:#ff4949 !important
		&::after,&::before
			border-right-color:#ff4949 !important
			border-left-color:#ff4949 !important
	&.is-dark
		transform:translateY(-10px)	
/*
隐藏表格单选的label字符
*/ 
.xpt-table__radio .el-radio__label
	display:none
#merge_goodList 
	.xpt-flex__bottom
		.el-input
			width:90px
/*右侧关闭所有样式*/
.xpt-openTabs-list
	position:absolute !important
	right:10px
	top:1px
	width:83px
	.el-input
		width:100% !important
	input
		color:#fff
/*
	左侧首页选项样式
*/
.xpt-home
	position:absolute
	top:0
	left:10px
	width:80px
	height:24px
	z-index:2
	line-height:24px
	padding:0 5px
	text-align:center
	border-radius:4px 4px 0 0
	border:1px solid transparent
	transition: all .3s cubic-bezier(.645,.045,.355,1);
	color:#8391a5
	cursor:pointer
	&:hover
		color:#1f2d3d
	&.active
		border-top:1px solid #d1dbe5
		border-left:1px solid #d1dbe5
		border-right:1px solid #d1dbe5
		color:#20a0ff
.mgt10
	margin-top:10px
.mgb10
	margin-bottom:10px
.mgb20
	margin-bottom:20px
.mgl10
	margin-left:10px
.mgr10
	margin-right:10px
.mgt5
	margin-top:5px
.mgb5
	margin-bottom:5px
.mgl5
	margin-left:5px
.mgr5
	margin-right:5px
.w120
	width:120px
	& .el-input
		width:120px
.w200
	width:200px
	& .el-input
		width:200px
	&.el-date-editor.el-input
		width:200px !important
.w150
	width:150px
	& .el-input
		width:150px
	&.el-date-editor.el-input
		width:150px !important
.w100
	width:100% !important

/*input框充当a连接时的样式*/
.xpt-link
	.el-input__inner
		cursor:pointer
		color:blue
#authorize .el-checkbox .el-checkbox__label
	display:none

/*自定义查询结构*/
.xpt-selfdenfined__search
	max-height:160px
	overflow:auto
	margin-bottom:10px

/**数字往右*/
.input_text input
	text-align: right;	

.input_text .el-form-item__content
	height:auto;	

/*兼容列表多tab的滚动条*/
.xpt-el-tab-pane
	.el-tabs
		height:100%
		.el-tabs__content
			height:100%
			.el-tab-pane
				height:100%
.el-form
	.el-tabs__content
		height:auto !important
/*窗口折叠*/
#xpt-folding
	position: absolute
	top: 6px
	right: 0
	width: 40px
	cursor: pointer
	z-index: 2
	.el-icon-caret-top
		margin-right: 6px
	i
		transition: color .3s
		color:#1f2d3d
		&:hover
			color:#20a0ff

.el-input.fileInput
	width:100%
//.el-table:hover
//  ::-webkit-scrollbar
//    width:10px
//    overflow:scroll

.el-table
	::-webkit-scrollbar
		width: 10px
	// 内层滚动条底色
	::-webkit-scrollbar-track-piece
		width: 0px
		//height: 6px
		background:#CCC
		// -webkit-border-radius:6px
	// 滚动条滑块
	::-webkit-scrollbar-thumb:vertical
		height:0px
		background:#999
		-webkit-border-radius:6px
	::-webkit-scrollbar-thumb:horizontal
		height: 10px
		background:#999
		//-webkit-border-radius:6px 
	.el-textarea
		::-webkit-scrollbar
			width:10px
		// 内层滚动条底色
		::-webkit-scrollbar-track-piece
			width: 10px
			//height: 6px
			background:#CCC
			// -webkit-border-radius:6px
		// 滚动条滑块
		::-webkit-scrollbar-thumb:vertical
			height:10px
			background:#999
			-webkit-border-radius:6px
		::-webkit-scrollbar-thumb:horizontal
			height: 10px
			background:#999
		//-webkit-border-radius:6px 
// 加载中
#loading
	position: fixed
	bottom: 50%
	right: 50%
	z-index: 9
	display: none
	i 
		color: #4e5e6e
		font-size: 16px !important
// 文本溢出
.xpt-text_ellipsis
	display:block
	text-overflow: ellipsis
	white-space: nowrap
	overflow: hidden
	color: inherit
	position: relative
	cursor: pointer
#xpt-tooltip
	position: fixed
	z-index: 99
	white-space: nowrap
	// white-space: normal
	// word-wrap: break-word
	visibility: hidden
	display: inline-block
	background: #1f2d3d
	color: #fff
	padding: 10px
	border-radius: 4px
	line-height: 1.2
	margin: 0
	i
		position: fixed
		color: #1f2d3d
		z-index: 99
*:disabled
	color: #4e5e6e !important
*:disabled[type="button"]
	color: #bfcbd9 !important	
tr.xpt-select td
	background-color: #d8e4bc !important
td.xpt-td__row
	//height: 80px !important
	height:auto !important
	position: relative
	padding: 5px 0
	
.el-table .el-table__body-wrapper td.xpt-td__row .cell, .el-table .el-table__fixed-body-wrapper td.xpt-td__row .cell	
	height:auto !important
.el-table .el-table__body-wrapper td .cell, .el-table .el-table__fixed-body-wrapper td .cell 
	width:100% !important
// 页面加载中
.xpt-page__loading
	padding: 20px
	color: #5e7382
	text-align: center
	&.error
		color: #FF4949


table.el-table__header,table.el-table__body
	min-width:100%

//element-ui新版本样式
.el-table th>.cell
	line-height: 24px;

.scroll-tab
	.el-table
		::-webkit-scrollbar
			width:10px
		// 内层滚动条底色
		::-webkit-scrollbar-track-piece
			width: 10px
			//height: 6px
			background:#CCC
			// -webkit-border-radius:6px
		// 滚动条滑块
		::-webkit-scrollbar-thumb:vertical
			height:30px
			background:#999
			-webkit-border-radius:6px
		::-webkit-scrollbar-thumb:horizontal
			height: 10px
			background:#999
			//-webkit-border-radius:6px 
// 收入单据表格中的收缩
.el-table__expanded-cell {
	padding: 0px 0px 0px 100px;
}
// 优惠弹窗中的样式调整
.el-row-search
	padding: 4px;
	border: 1px solid #ddd;
.el-row-filter
	padding: 10px;
	border: 1px solid #ddd;
.couponActionOfflineDialog
	.el-form-item .el-form-item__label
		width: 66px;
.couponActionDialog
	.el-form-item .el-form-item__label
		width: 79px;
		
.vxe-table .vxe-body--column.col--ellipsis, .vxe-table.vxe-editable .vxe-body--column, .vxe-table .vxe-footer--column.col--ellipsis, .vxe-table .vxe-header--column.col--ellipsis {
	height: 24px !important;
}
.vxe-header--column .vxe-cell--title {
	line-height: 24px;
}
.vxe-table.vxe-editable .vxe-body--column.radioCenter {
	text-align: center;
}
.vxe-table .vxe-body--column.col--ellipsis:not(.col--actived)>.vxe-cell>.el-date-editor.el-input {
	/* .el-input, .el-select, .el-date-editor.el-input { */
		width: 100%;
	/* } */
}
.vxe-table .vxe-body--column.col--ellipsis:not(.col--actived)>.vxe-cell>.el-date-editor.el-input .el-input__icon+.el-input__inner {
	height: 100%;
}
.vxe-table .vxe-cell--radio .vxe-radio--checked-icon:after {
	height: .5em !important;
	width: .5em !important;
	top: .48em !important;
	left: 0.48em !important;
}
.vxe-table .vxe-cell--radio .vxe-radio--icon:before {
	height: 1.5em !important;
	width: 1.5em !important;
}
.vxe-table .vxe-cell--checkbox .vxe-checkbox--icon, .vxe-table .vxe-cell--radio .vxe-radio--icon {
	top: 0em !important
}
.vxe-table .vxe-body--row.row--checked, .vxe-table .vxe-body--row.row--radio{
	background-color: #d8e4bc !important;
}
.vxe-header--column .vxe-cell--title {
	line-height: 24px !important;
	height: 24px;
	vertical-align: middle !important;
	font-size: 12px !important;
	font-weight: lighter;
	color: #1f2d3d;
}
.vxe-table .vxe-cell {
	color: #1f2d3d;
	font-family: 'Microsoft YaHei';
}
.vxe-table .vxe-cell--checkbox .vxe-checkbox--icon {
	height: 1.5em !important;
	width: 1.5em !important;
}
.vxe-table .vxe-cell--checkbox .vxe-checkbox--icon:before {
	height: 1.5em !important;
	width: 1.5em !important;
	border-radius: 4px !important;
}
.vxe-table .vxe-cell--tree-node {
	padding-left: 0px !important;
}

.el-textarea__inner {
	font-size: 12px
}