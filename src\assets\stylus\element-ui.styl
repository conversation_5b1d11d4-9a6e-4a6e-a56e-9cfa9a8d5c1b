/*表单样式修改*/
.el-form-item
	margin-bottom:0
	.el-form-item__label,.el-form-item__content
		height:28px
		line-height:28px
	.el-form-item__label
		padding:0 3px
		// width: 100px !important
	.el-form-item__error
		display:none
	.el-tooltip
		color:#ff4949
.el-tabs
	.el-tabs__header
		margin:0
	.xpt-top
		border-bottom:none
		background:#eee
.el-menu-item.is-active
	color:#48576a;
.el-row
	margin-left:0 !important
	margin-right:0 !important
.el-submenu__title,.el-menu-item
	height:40px !important
	line-height:40px !important
.el-time-spinner__list::after,
.el-time-spinner__list::before
	height:72px
.el-input,.el-select,.el-date-editor.el-input
	width:180px
.el-select .el-input
	width:100%
	&.is-disabled .el-input__inner
		// background-color:#fff
.el-button
	min-width:40px
.el-button--mini
	padding: 2px !important
.el-input.is-disabled .el-input__inner
	cursor:inherit !important
.test
	.el-form-item__label
		width: 100px !important
#discount_dialog
	.el-form-item__content
		width: 100px;
	.el-input,.el-select,.el-date-editor.el-input
		width: 100px;
	.specialItem
		.el-form-item__content
			width: 190px;
		.el-input,.el-select,.el-date-editor.el-input
			width: 170px;
	.timeItem
		.el-form-item__content
			width: auto;
			.el-input, .el-select, .el-date-editor.el-input
				width: 180px;
			.timeSelect
				.el-input, .el-select, .el-date-editor.el-input
					width: 120px;

// 下拉框样式
.el-select-dropdown__list li
	height:20px
	padding:0 5px
	line-height:20px

.el-tabs__header
	>.el-tabs__nav-wrap
		>.el-tabs__nav-scroll>.el-tabs__nav
			>div:first-child
				width:0
				border:none
				padding:0
			.el-tabs__item
				vertical-align:top
				height:24px
				line-height:24px
				padding:0 10px
	.el-tabs__nav-next, .el-tabs__nav-prev
		line-height: 30px;
/* 表格样式 */
.el-table
	border:none
	&:before,&:after
		height:0
	.cell
		padding:0 5px
	.el-table__header-wrapper th,.el-table__fixed-header-wrapper th
		height:24px
		line-height:24px
	.el-table__body-wrapper td,.el-table__fixed-body-wrapper td
		height:24px
		line-height:24px
		.cell
			line-height:24px
			height:24px
			overflow: hidden
			text-overflow: ellipsis
			white-space: nowrap
			word-break:normal
		    .el-table__expand-icon
				height:24px
		a
			text-decoration:none
.el-table--border
	.el-table__header-wrapper
		border-top:1px solid #dfe6ec
	tr
		td:first-child,th:first-child
			position:relative
			&:before
				position:absolute
				top:0
				bottom:0
				left:0
				width:1px
				background #dfe6ec
				content:''
				z-index:2
		td:last-child,th:last-child
			border-right:none
			&:after
				position:absolute
				top:0
				right:0
				bottom:0
				width:1px
				content:''
				background #dfe6ec

.el-select-dropdown__item
	.el-icon-close
		float:right
		width:14px
		&:before
			-ms-transform: scale(.7,.7)
			transform: scale(.7,.7)
			display:inline-block

.el-dialog__wrapper
	background-color: rgba(0,0,0,.5)
	z-index:9
	&.show
		display: block !important
	.el-dialog--small
		width:400px !important
/* 菜单树 */
.el-tree-header
	height:24px
	background-color:#eef1f6
	ul
		height:24px
		li
			line-height:24px
			color:#1f2d3d
.el-tree-header,.el-tree-node
	position:relative
	ul
		display:flex
		position:absolute
		top:0
		left:100px
		right:0
		height:24px
		// z-index:999
		li
			flex:1
			text-overflow:ellipsis
			overflow:hidden
			padding:0 5px
.el-tree-node
	border-top:1px solid #d1dbe5
	.el-tree-node__content
		height:24px
		line-height:24px
		&:hover
			background-color:transparent
.el-tree
	border:none
	border-bottom:1px solid #d1dbe5
	.el-tree-table
		position:absolute
		top:0
		left:100px
		right:0
		height:24px
		td
			border-bottom:none
		tr:hover td
			background-color:transparent !important
.el-tooltip__popper
	max-width:600px
	word-break:break-all
	position: fixed
a
	text-decoration:none !important

/*日期选择器里面的宽度设置*/
.el-picker-panel__body-wrapper
	.el-input
		width:100%
/*switch开关禁用状态下字体颜色*/
.el-switch.is-disabled .el-switch__core~.el-switch__label *
	color:#bbb !important
.el-message__group p
	margin: 0
.el-select-dropdown
	margin: 0 !important
	padding: 5px 0 !important
.el-select-dropdown__list
	padding: 0 !important
.el-input__inner,.el-button,.el-picker-panel__btn{
   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,'Helvetica Neue', 'PingFang SC', 'Noto Sans', 'Noto Sans CJK SC', 'Microsoft YaHei', '\\5FAE软雅黑', sans-serif;
}
