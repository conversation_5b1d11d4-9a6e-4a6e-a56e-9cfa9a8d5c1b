import { Message } from 'element-ui';

export function getCurrentUserLogin(key) {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", "/user-web/api/userLogin/getCurrentUserLogin", false); // false 表示同步请求
    xhr.send(null);
    if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);
        const data = response;

        if (data.content) {
            // 成功获取用户信息，存储到 sessionStorage
            const userInfo = {
                user: data.content.loginUser,
                employeeInfo: data.content.principalUse
            };
            sessionStorage.setItem('userInfo', window.JSON.stringify(userInfo));
            sessionStorage.setItem('employeeInfo',window.JSON.stringify(data.content.principalUse))
            return  key ? userInfo['user'][key] : userInfo['user'];
        } else {
            console.error("获取用户信息失败:", data.msg);
            return { user: {}, employeeInfo: {} };
        }
    } else {
        console.error("请求失败，状态码:", xhr.status);
        return { user: {}, employeeInfo: {} };
    }
}

export default {
	//格式化日期的方法
	dateFormat:function(val,fm){
		if(!val) return;
		var date = new Date(val),
			o = {
				"M+" : date.getMonth()+1, //month
				"d+" : date.getDate(), //day
				"h+" : date.getHours(), //hour
				"m+" : date.getMinutes(), //minute
				"s+" : date.getSeconds(), //second
				"q+" : Math.floor((date.getMonth()+3)/3), //quarter
				"S" : date.getMilliseconds() //millisecond
			},
			format = fm || 'yyyy-MM-dd';
		if(/(y+)/.test(format)) format=format.replace(RegExp.$1,
		(date.getFullYear()+"").substr(4- RegExp.$1.length));
		for(var k in o)if(new RegExp("("+ k +")").test(format))
		format = format.replace(RegExp.$1,
		RegExp.$1.length==1? o[k] :
		("00"+ o[k]).substr((""+ o[k]).length));
		return format;
	},

	/*大于等于零的整数，保留两位小数点*/
	number(num){
		var re = /^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/;
        var re1 = /^[1-9]\d*|0$/
		if(re.test(parseFloat(num)) || re1.test(parseFloat(num))){

			//return parseFloat(Math.floor(num*100)/100);
            //进行四舍五入
            return parseFloat(Math.round(num*100)/100);
		}else{
			return '0.00';
		}
	},

	numberFixed(num, digit=2) {
  	// 这样处理主要是有些字段的统计值不需要显示
	  if (num === undefined || num === null || num === '' || isNaN(num)) {
	    return '';
	  }
	  let str = Number(num).toFixed(digit);
	  // 解决-0.00的问题
	  let arr = str.split('.');
	  // 不是以-0开头或者
	  if (arr[0] !== '-0') {
	    return str;
	  }
	  if (arr[1] === undefined) {
	    return str;
	  }
    if (/^0+$/.test(arr[1])) {
    // 将-0.00 转成0.00
      return '0.' + arr[1];
    }
    return str;
	},

	toNumber(num) {
	  if(num) {
	    return Number(num);
	  } else {
	    return 0;
	  }
	},
    /**
     * 获取用户信息，
     * **/
    getUserInfo(key){
        let useInfoStr = sessionStorage.getItem('userInfo') || '';
        var userInfo = undefined
        try {
            userInfo = window.JSON.parse(useInfoStr)
        } catch (error) {
            userInfo = undefined
        }
        if(userInfo && userInfo.user) {
            return key ? userInfo['user'][key] : userInfo['user'];
        }
        getCurrentUserLogin(key)
        // this.ajax.get('/user-web/api/userLogin/getCurrentUserLogin',(d)=>{
        //     var data = d.body;
        //     if(!data.result) return {
        //         user: {},
        //         employeeInfo: {}
        //     };
        //     //success code
        //     sessionStorage.setItem('userInfo',window.JSON.stringify({
        //         user:data.content.loginUser
        //     }))
        //     sessionStorage.setItem('employeeInfo',window.JSON.stringify(data.content.principalUse))
        //     userInfo = {
        //         user: data.content.loginUser,
        //         employeeInfo: data.content.principalUse
        //     }
        // })
    },

    /**
     * 判断对像是否为空
     * **/
    isEmptyObject(data){
        for(var key in data){
            return false;
        }
        return true;
    },

    /**
     * 获取当前时间
     * fm,form
     * {data} fm-要转换成什么样的格式
     * {data} form,是否有指定的时间段
     * **/
    getNowDate:function(data){
        var form = data.form;
        var format = typeof form != 'undefined'?'yyyy-MM-dd':data.fm || 'yyyy-MM-dd';
        var date = new Date(),
            o = {
                "M+" : date.getMonth()+1, //month
                "d+" : date.getDate(), //day
                "h+" : date.getHours(), //hour
                "m+" : date.getMinutes(), //minute
                "s+" : date.getSeconds(), //second
                "q+" : Math.floor((date.getMonth()+3)/3), //quarter
                "S" : date.getMilliseconds() //millisecond
            };


        if(/(y+)/.test(format)) format=format.replace(RegExp.$1,
            (date.getFullYear()+"").substr(4- RegExp.$1.length));
        for(var k in o)if(new RegExp("("+ k +")").test(format))
            format = format.replace(RegExp.$1,
                RegExp.$1.length==1? o[k] :
                    ("00"+ o[k]).substr((""+ o[k]).length));
        typeof form != 'undefined'?format += ' ' + form:'';
        console.log('formasfasdft',format);
        return format;
    },

     /**
     * 初始化生效时间和失效时间
     * **/
    defaultStartDate:function () {
        var date = new Date();
        return  new Date(date.getFullYear(), date.getMonth(), date.getDate());
    },

    defaultEndDate:function (date=new Date()) {
        return   new Date(date.getFullYear(), date.getMonth(), date.getDate(),23,59,59);
    },

    //收款单单据状态
    getStatus:function(){
        var status = {
            CREATE:"创建",
            SUBMITTED:"提交审核",
            APPROVED:"已审核",
            RETRIAL:"重新审核",
            CANCELED:"已作废",
            WITHDRAWED:"已撤回",
            LOCKED:"锁定",
            INVALID :"已作废"
        }
        return status;
    },

  //充值单核对状态
  getCheckStatus:function(){
    var status = {
      CHECKED:"已核对",
      UNCHECK:"待核对",
      NOCHECK:"无需核对"
    }
    return status;
  },

    //获取币种
    getCurrency:function(){
        var currency = {
            'CNY':"人民币",
            'USD':"美元",
            'EUR':"欧元",
            'HKD':'港币',
            'GBP':"英镑"
        }
        return currency;
    },
    //收款单类型
    /*getReceiptType:function(){
        return {
            ON_LINE:"线上收款单",
            OFF_LINE:'线下收款单'
        }
    },*/

    //支付方式
    /*getPayType:function(){

        var type = {
            ALIPAY:'支付宝',
            WECHAT:'微信',
            BANK_TRANSFER:'银行转账',
            POS:'POS刷卡',
            CASH:'现金'
        }
        return type;
    },*/

    //支付渠道
    paymentChannel:function(){
        var payment_channel_options = {
            TAOBAO:'淘宝',
            //B2C:'B2C商城',
            EMERGING_PROJECT:'新兴项目',
            JOINED:'加盟',
            O2O:'O2O体验店',
        }
        return payment_channel_options;
    },

    //收款单的同步状态
    synStatus:function(){
        var synStatus = {
            RUNNING:'同步中',
            WAIT:'等待',
            SUCCESS:'成功',
            FAILED:'失败'
        }
        return synStatus;
    },

    //基础资料，店铺分类
    /*shopType:function(){
        var type = {
            ON_LINE:'线上店铺',
            O2O:'O2O线下店铺',
            EMERGING_PROJECT:'新兴项目店铺',
            B2C:'B2C商城'
        }
        return type;
    },*/

    //店铺状态
    shopStatus:function(){
        var status = {
            OPEN:'营业中',
            CLOSED:'已关闭'
        }
        return status;
    },

    //退货方案里面的退换方式
    returnGoodsType:function(){
        var type = {
            'RETURN':'退货',
            'CHANGE':'换货'
        }
        return type;
    },

    //送货方式
    deliverMethod:function(){
        return {
            THREES:'三包',
            LOGISTICS:'物流',
            PICK_UP:'仓库自提',
            ZTO:'中通小件',
            ZTO_FREE:'中通免费',
            SF:'顺丰'
        }
    },

    //运费类型
    postFeeType:function(){
        return {
            "NOW_PAY" : '现付',
            "ARRIVE_PAY" : '到付'
        }
    },

    /*
     * 我们知道计算机编程语言里浮点数计算会存在精度丢失问题（或称舍入误差），其根本原因是二进制和实现位数限制有些数无法有限表示
     * 以下是十进制小数对应的二进制表示
     *      0.1 >> 0.0001 1001 1001 1001…（1001无限循环）
     *      0.2 >> 0.0011 0011 0011 0011…（0011无限循环）
     * 计算机里每种数据类型的存储是一个有限宽度，比如 JavaScript 使用 64 位存储数字类型，因此超出的会舍去。舍去的部分就是精度丢失的部分。
     *
     * ** method **
     *  add / subtract / multiply /divide
     *
     * ** explame **
     *  0.1 + 0.2 == 0.30000000000000004 （多了 0.00000000000004）
     *  0.2 + 0.4 == 0.6000000000000001  （多了 0.0000000000001）
     *  19.9 * 100 == 1989.9999999999998 （少了 0.0000000000002）
     *
     * add(0.1, 0.2) >> 0.3
     * multiply(19.9, 100) >> 1990
     *
     */

    // 判断obj是否为一个整数
    isInteger: function(obj) {
        return Math.floor(obj) === obj;
    },

    /*
     * 将一个浮点数转成整数，返回整数和倍数。如 3.14 >> 314，倍数是 100
     * @param floatNum {number} 小数
     * @return {object}
     *   {times:100, num: 314}
     */
    toInteger: function(floatNum) {
        var self = this;

        var ret = {times: 1, num: 0};
        var isNegative = floatNum < 0;

        if (self.isInteger(floatNum)) {
            ret.num = floatNum;
            return ret;
        }
        var strfi  = floatNum + '';
        var dotPos = strfi.indexOf('.');
        var len    = strfi.substr(dotPos+1).length;
        var times  = Math.pow(10, len);
        var intNum = parseInt(Math.abs(floatNum) * times + 0.5, 10);

        ret.times  = times;
        if (isNegative) { intNum = -intNum; }
        ret.num = intNum;
        return ret;
    },

    /*
     * 核心方法，实现加减乘除运算，确保不丢失精度
     * 思路：把小数放大为整数（乘），进行算术运算，再缩小为小数（除）
     *
     * @param a {number} 运算数1
     * @param b {number} 运算数2
     * @param digits {number} 精度，保留的小数点数，比如 2, 即保留为两位小数
     * @param op {string} 运算类型，有加减乘除（add/subtract/multiply/divide）
     *
     */
    operation: function(a, b, digits, op) {
        var o1 = this.toInteger(a);
        var o2 = this.toInteger(b);
        var n1 = o1.num;
        var n2 = o2.num;
        var t1 = o1.times;
        var t2 = o2.times;
        var max = t1 > t2 ? t1 : t2;
        var result = null;

        switch (op) {
            case 'add':
                if (t1 === t2) { // 两个小数位数相同
                    result = n1 + n2;
                } else if (t1 > t2) { // o1 小数位 大于 o2
                    result = n1 + n2 * (t1 / t2);
                } else { // o1 小数位 小于 o2
                    result = n1 * (t2 / t1) + n2;
                }
                return result / max;
            case 'subtract':
                if (t1 === t2) {
                    result = n1 - n2;
                } else if (t1 > t2) {
                    result = n1 - n2 * (t1 / t2);
                } else {
                    result = n1 * (t2 / t1) - n2;
                }
                return result / max;
            case 'multiply':
                result = (n1 * n2) / (t1 * t2);
                return result;
            case 'divide':
                result = (n1 / n2) * (t2 / t1);
                return result;
        }
    },

    // 加减乘除的四个接口
    add: function(a, b, digits) {
        return this.operation(a, b, digits, 'add');
    },
    subtract: function(a, b, digits) {
        return this.operation(a, b, digits, 'subtract');
    },
    multiply: function(a, b, digits) {
        return this.operation(a, b, digits, 'multiply');
    },
    divide: function(a, b, digits) {
        return this.operation(a, b, digits, 'divide');
    },
    // 获取辅助资料类型
    getAuxType(type){
        var payType = __AUX.get(type);
        var obj = {};
        for(let i in payType){
            obj[payType[i].code] = payType[i].name;
        }
        //var type = Fn.getPayType();
        return obj;
    },
    // 获取未失效辅助资料类型
    getAuxType2(type){
        var payType = __AUX.get(type);
        var obj = {};
        for(let i in payType){
            if(payType[i].status == 1){
                obj[payType[i].code] = payType[i].name;
            }
        }
        //var type = Fn.getPayType();
        return obj;
    },
    getAuxType3(type){
      var payType = __AUX.get(type);
      return payType;
  },
    //格式化助资料类型
    formatAux(type,val){
        var payType = __AUX.get(type);
        let auxtype = ''
        for(let i in payType){
            // obj[payType[i].code] = payType[i].name;
            if(payType[i].code == val){
                auxtype = payType[i].name;
            }
        }
        return auxtype;
        //var type = Fn.getPayType();
        // return obj[val] || val;
    },
    //隐藏号码
    hidePhoneNumber(val){
        if(/^0\d{2,3}-?\d{7,8}$/.test(val)||/^1\d{10}$/.test(val)){
            return String(val).includes('-')?`${String(val).substr(0, 4)}****${String(val).substr(8)}`:`${String(val).substr(0, 3)}****${String(val).substr(7)}`;
        }else{
            return val
        }
    },
    //邮箱校验
    isEmail(val){
        val = val||""
        return /[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+/.test(val.toString())
    },
    //命名转换中文
    download(url, filename) {
        if (!fetch) {
            window.location.href = url;
            return;
        }
        return fetch(url).then((res) => {
            console.log(res);
            res.blob().then((blob) => {
                let a = document.createElement("a");
                let url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = filename;
                a.click();
                window.URL.revokeObjectURL(url);
            });
        });
    },
    changeOssUrl(url){
        let reg = 'https://ls-tms-images.oss-cn-shenzhen.aliyuncs.com';
        // console.log(url.replace(reg,'http://4pl-img.linshimuye.com'))
        return url.replace(reg,'http://4pl-img.linshimuye.com');
    },
    //验证是否手机还是电话号码
    isPhoneNumber(num){
        return /^0\d{2,3}-?\d{7,8}$/.test(num)||/^1\d{10}$/.test(num)
    },
    encryptPhoneNumber(num) {
        if(this.isPhoneNumber(num)){
          return String(num).includes('-')?`${String(num).substr(0, 4)}****${String(num).substr(8)}`:`${String(num).substr(0, 3)}****${String(num).substr(7)}`;
        }else{
          return num
        }
    },
    encryptPhoneNumberShort(num) {
        if(this.isPhoneNumber(num)){
          let length=String(num).length
          let leftnum=String(num).substr(length-4)
          return leftnum.padStart(length,'*');
        }else{
          return num
        }
    },
    encryptName(val) {
        if(val){
          return `${String(val).substr(0,1).padEnd(val.length,'*')}`
        }else{
          return val
        }
    },
    encryptAddress(val) {
      let length=val&&String(val).length;
      if(length>=3&&length<=10){
        let headStr=val.substr(0,1)
        let endStr=val.substr(-1)
        let midStr=''.padEnd(length-2,'*')
        return `${headStr}${midStr}${endStr}`
      }else if(length>10){
          let headStr=val.substr(0,2)
          let endStr=val.substr(length-3)
          let midStr=''.padEnd(length-5,'*')
          return `${headStr}${midStr}${endStr}`
      }else{
          return val
      }
    },
    //自定义随机id
    guid(prefix)
    {
        return (prefix || '') + Math.round(Math.random() * 1000000).toString();
    },
    copyToClipboard(text) {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
      Message.success('内容已复制到剪贴板');
    },

    getEnv () {
      const host = location.host;
      if (host.indexOf('linshimuye') < 0) {
        return 'dev';
      }

      if (host.indexOf('sit') > -1) {
        return 'sit';
      }

      if (host.indexOf('uat') > -1) {
        return 'uat';
      }

      return 'prod';
    },

    getStoreDomain() {
      const env = this.getEnv();
      return {
        dev: 'https://storesit.linshimuye.com',
        sit: 'https://storesit.linshimuye.com',
        uat: 'https://storeuat.linshimuye.com',
        prod: 'https://store.linshimuye.com',
      }[env];
    },
}
