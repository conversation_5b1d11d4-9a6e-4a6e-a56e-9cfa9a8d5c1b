import Vue from 'vue'
import parse from '@common/parse'
import LoginFloat from '@components/login/loginFloat';
import md5 from 'js-md5'
var conf = {
    default: 'http://passport.lsmy.com', //登录
    default2: 'http://permission.lsmy.com', //提取数据
    // default3:jsHost
}
// 无需显示加载中的url集合
let rejectUrl = [
    '/order-web/api/mergetrade/addEditToken',
    '/afterSale-web/api/aftersale/order/bill/allstatuslist',//售后单获取方案进度
    '/order-web/api/stroreMateral/synchroData'//同步所有智慧门店库存
];
var getCookie = function (name) {
    var arr;
    var reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
    if (arr = document.cookie.match(reg))
    return unescape(arr[2]);
    else
    return null;
}('lsmyToken')||'';
let salt =  '9979df3d-6ab7-43ff-b4f2-3520885bf2d3';
// console.log(getCookie.replace(/"/g,''));
let sign = md5(getCookie.replace(/"/g,'')+salt).toLocaleUpperCase();
document.cookie = 'sign="'+sign+'";path=/';

export default {
    requestList: {},
    tabName: '',
    http(options, succ, err, id) {

        var getNewlsmyToken = function (name) {
            var arr;
            var reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
            return unescape(arr[2]);
            else
            return null;
        }('lsmyToken')||'';

        if(getCookie !== getNewlsmyToken){
            window.location.reload();
            return false;
        }
        // console.log('ajax['+ options.method +']请求：', options.url);
        let $loading = document.querySelector('#loading');
        let rejectUrlSet = new Set(rejectUrl);
        $loading && !rejectUrlSet.has(options.url) && ($loading.style.display = 'inherit');
        //alert('参数:'+JSON.stringify(options))

        /*
        根据请求地址记录ajax请求，再请求成功之前可以终止。请求完成之后删除记录,
        2018.3.17
        zz
        */

        let self = this;
        if (id) {
            if (!self.requestList[id]) {
                self.requestList[id] = {};
            }
            // 如果相同ID相同请求url下有还没有完成的请求，则中断该请求
            if(self.requestList[id][options.url]) {
                self.requestList[id][options.url].abort();
            }
            options.before = (req) => {
                self.requestList[id][options.url] = req;
            }
        }
        Vue.http(options).then(function(d) {
            if(id && self.requestList[id]) {
                delete self.requestList[id][options.url];
            }
           // alert(JSON.stringify(d));
             /*d.body = parse(d.bodyText)
             succ && succ(d);*/

             /*
            sso对接信息服务部,在ajax提交的时候添加taken，taken从URL上面拿
            2019.4.9
            zz
            */

            if (d.body.action != undefined && !d.body.result) {
                // console.log(d);
                window.location.href = d.body.targetUrl;
            }

            // if (d.body.action === 'login' && !d.body.result) {
            //     window.location.href = d.body.loginUrl;
            // }
            else {
                // console.log('dd',d.data.msg);
              let url = options.url;
              if (Vue.prototype.activeTab === "合并订单详情") {
                if (url.indexOf("order-web") !== -1) {
                  if (d.data.msg && !d.data.result) {
                    Vue.prototype.tabErr[Vue.prototype.activeTab] = d.data.msg;
                  }
                }
              } else {
                if (d.data.msg && !d.data.result) {
                  Vue.prototype.tabErr[Vue.prototype.activeTab] = d.data.msg;
                }
              }
              d.body = parse(d.bodyText)
              succ && succ(d);
            }
            setTimeout(()=> {
                $loading && !rejectUrlSet.has(options.url) && ($loading.style.display = 'none');
            }, 500)
        }, function(d) {
            $loading && !rejectUrlSet.has(options.url) && ($loading.style.display = 'none');
            if(0 == d.status) {
                err && err('服务没有响应。');
            } else {
                if(id && self.requestList[id]) {
                    delete self.requestList[id][options.url];
                }
                // console.log("err3",err,err(d.status + ' ' + d.statusText));
				Vue.prototype.tabErr[Vue.prototype.activeTab]=d.status + ' ' + d.statusText;
                err && err(d.status + ' ' + d.statusText);
            }
        })
    },
    // 不显示图标的ajax请求
    setTimeoutHttp(options, succ, err, id) {
        // console.log('ajax['+ options.method +']请求：', options.url);
        // let $loading = document.querySelector('#loading');
        let rejectUrlSet = new Set(rejectUrl);
        // $loading && !rejectUrlSet.has(options.url) && ($loading.style.display = 'inherit');
        //alert('参数:'+JSON.stringify(options))
        /*
        sso对接信息服务部,在ajax提交的时候添加taken，taken从URL上面拿
        2019.4.9
        zz
        */
        // let urlArr = window.location.href.split('?');
        // if(urlArr.length >1){
        //     options.url = options.url + '?' + urlArr[1]
        // }
        /*
        根据请求地址记录ajax请求，再请求成功之前可以终止。请求完成之后删除记录,
        2018.3.17
        zz
        */

        let self = this;
        if (id) {
            if (!self.requestList[id]) {
                self.requestList[id] = {};
            }
            // 如果相同ID相同请求url下有还没有完成的请求，则中断该请求
            if(self.requestList[id][options.url]) {
                self.requestList[id][options.url].abort();
            }
            options.before = (req) => {
                self.requestList[id][options.url] = req;
            }
        }
        Vue.http(options).then(function(d) {
            if(id && self.requestList[id]) {
                delete self.requestList[id][options.url];
            }
           // alert(JSON.stringify(d));
             /*d.body = parse(d.bodyText)
             succ && succ(d);*/


             /*
            sso对接信息服务部,在ajax提交的时候添加taken，taken从URL上面拿
            2019.4.9
            zz
            */
            if (d.body.action != undefined && !d.body.result) {
                window.location.href = d.body.targetUrl;
            }

            // if (d.body.action === 'login' && !d.body.result) {
            //     window.location.href = d.body.loginUrl;

            // }
            else {
                d.body = parse(d.bodyText)
                succ && succ(d);
            }
            setTimeout(()=> {
                // $loading && !rejectUrlSet.has(options.url) && ($loading.style.display = 'none');
            }, 500)
        }, function(d) {
            // $loading && !rejectUrlSet.has(options.url) && ($loading.style.display = 'none');
            if(0 == d.status) {
                // err && err('服务没有响应。');
            } else {
                if(id && self.requestList[id]) {
                    delete self.requestList[id][options.url];
                }
                err && err(d.status + ' ' + d.statusText)

            }
        })
    },
    jsonp(url, data, succ, err) {
        var apiUrl = conf.default+url;
        this.http({
            url: apiUrl,
            method: 'jsonp',
            params: data,
            jsonp: 'callback',
            jsonpCallback: 'callback'
        }, succ, err);
    },
    jsonp2(url, data, succ, err) { //提取数据
        // console.log(conf)
        var apiUrl = conf.default2 + url;
        this.http({
            url: apiUrl,
            method: 'jsonp',
            params: data,
            jsonp: 'callback',
            jsonpCallback: 'callback'
        }, succ, err);
    },
    jsonp3(url, data, succ, err) { //提取数据
        // console.log(conf)
        var apiUrl = url;
        this.http({
            url: apiUrl,
            method: 'jsonp',
            params: data,
            jsonp: 'callback',
            jsonpCallback: 'callback'
        }, succ, err);
    },
    //转换时间
    check(data) {
        for (var d in data) {
            if (typeof data[d] === 'object') {
                if (data[d] instanceof Date) {
                    data[d] = data[d].getTime();
                } else {
                    this.check(data[d]);
                }
            }
        }
    },
    //数据流
    /*
    onlyId为创建页签的params.tabName或弹窗的params.alertId,
    用于记录已经发出的请求，请求完成后删除该记录
    2018.4.1 zz
    */
    postStream(url, data, succ, err, onlyId) {
        this.check(data);

        this.http({
            url: url,
            headers: { "Content-Type": 'application/json;charset=utf-8', },
            body: JSON.stringify(data),
            method: "post",
            async: true,
        }, succ, err, onlyId);
    },
    postStreamOutTime(url, data, succ, err, _timeout, onTimeout) {
        this.check(data);

        this.http({
            url: url,
            headers: { "Content-Type": 'application/json;charset=utf-8', },
            body: JSON.stringify(data),
            method: "post",
            async: true,
            _timeout: _timeout,//超时单间
            onTimeout:onTimeout//超时回调方法
        }, succ, err);
    },
    postStream1(url, data, succ, err) {
        this.check(data);
        this.http({
            url: url,
            headers: { "Content-Type": 'application/json;charset=utf-8', },
            body: data,
            method: "post",
            async: true,
        }, succ, err);
    },
    //get方式
    get(url, succ, err) {
        this.http({
            url: url,
            method: "get",
        }, succ, err);
    },
    setTimeoutGet(url, succ, err){
        this.setTimeoutHttp({
            url: url,
            method: "get",
        }, succ, err);
    },
    // post表单
    post(url, data, succ, err) {
        this.http({
            url: url,
            body: data,
            method: 'post',
            emulateJSON: true
        }, succ, err)
    },
    postFile(url, data, succ, err) {
        this.http({
            url: url,
            headers: { "Content-Type": 'multipart/form-data;charset=utf-8', },
            body: JSON.stringify(data),
            method: "post",
            async: true,
        }, succ, err)
    }
}
