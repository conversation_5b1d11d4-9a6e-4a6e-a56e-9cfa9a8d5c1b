import Vue from 'vue'

/* 
  窗口内容折叠
  当前指令元素A与上一个同级元素B内容的折叠
  A与B必须是Flex布局
 */
function getContentOffset(el) {
  let e = el.offsetParent,
    h = 0;
  while(e) {
    h += e.offsetTop;
    e = e.offsetParent;
  }
  return h;
}
Vue.directive('fold', {
	inserted(el, bindingm, vnode) {
    let p = document.createElement('div'),
			i1 = document.createElement('i'),
			i2 = document.createElement('i');
		i1.setAttribute('class', 'el-icon-caret-top')
		i1.setAttribute('title', '向上折叠窗口')
		i2.setAttribute('class', 'el-icon-caret-bottom')
		i2.setAttribute('title', '向下折叠窗口')
		p.appendChild(i1)
		p.appendChild(i2)
		el.insertBefore(p, el.firstChild)
		p.setAttribute('id', 'xpt-folding')
    p.setAttribute('data-pos', 'center')
		i1.addEventListener('click', foldEvent)
		i2.addEventListener('click', foldEvent)

    let $box = el.parentNode,
      $top = el.previousElementSibling;
    // 设置比例
    el.setAttribute('data-ratio', $top.clientHeight + ':' + el.clientHeight)

    function foldEvent(e) {
      let $this = e.target,
        isUp = $this.className.indexOf('el-icon-caret-top') > -1 ? true : false,
        $fold = $this.parentNode,
        $foot = $fold.parentNode,
        $prev = $foot.previousElementSibling,
        ratio = $foot.getAttribute('data-ratio');
      if(!ratio) return;
      let topFlex = ratio.split(':')[0] * 1,
        footFlex = ratio.split(':')[1] * 1,
        pos = $fold.getAttribute('data-pos');
      if(isUp) {
        // 向上折叠
        if(pos === 'center') {
          $prev.style.flex = 0;
          $prev.style.overflow = 'hidden'
          $fold.setAttribute('data-pos', 'top')
          $foot.removeAttribute('style')
        }
        if(pos === 'bottom') {
          $prev.removeAttribute('style')
          $foot.removeAttribute('style')
          $fold.setAttribute('data-pos', 'center')
        }
      } else {
        // 向下折叠
        if(pos === 'top') {
          $prev.removeAttribute('style')
          $foot.removeAttribute('style')
          $fold.setAttribute('data-pos', 'center')
        }
        if(pos === 'center') {
          $prev.style.flex = topFlex + footFlex - 24;
          $foot.style.flex = 24
          $foot.style.overflow = 'hidden';
          $fold.setAttribute('data-pos', 'bottom')
        }
      }
    }
	},
  update(el, binding) {
    // 重新设置比例，去除样式
    let $box = el.parentNode,
      $top = el.previousElementSibling;
    $top.removeAttribute('style');
    el.removeAttribute('style');
    el.firstChild.setAttribute('data-pos', 'center')
    el.setAttribute('data-ratio', $top.clientHeight + ':' + el.clientHeight);
  }
})

/*
  窗口拖动
  binding.arg：触发拖动事件子元素位置的下标，默认为0
*/
Vue.directive('drag', {
  inserted(el, binding) {
    // 拖动句柄下标的位置
    let index = binding.arg || 0
    // 获取拖动句柄，及盒子的宽高。
    let box = el.children[index];
    box.setAttribute('title', '按住拖动窗口')
    //0.声明一个开关变量        
    let off = 0
    //0.1声明一个变量一保存鼠标与盒子之间的距离        
    let cur = {}        
    //1.1 计算鼠标位置-盒子到页面的位置，得到一个差，永远不变
    box.onmousedown = e => {
      off = 1
      cur = {
        x: e.clientX - el.offsetLeft,
        y: e.clientY - el.offsetTop
      }
      box.style.cursor = 'move'
    }        
    //2.添加鼠标移动事件        
    box.onmousemove = e => {       
      let $parentW = el.clientWidth / 2,
        $parentH = el.clientHeight / 2;  
      //2.1判断按下的开关状态  如果是真再运行            
      if(!off) return
      var left = e.clientX - cur.x        
      var tops = e.clientY - cur.y
      //限制box不超出浏览器
      left = left < 0 ? 0 : left
      tops = tops < 0 ? 0 : tops
      left = left >= window.innerWidth-$parentW ? window.innerWidth-$parentW : (left > $parentW ? left : $parentW)
      tops = tops >= window.innerHeight-$parentH ? window.innerHeight-$parentH : (tops > $parentH ? tops : $parentH)
      el.style.left = left+'px'
      el.style.top = tops+'px'            
    }        
    //3.添加鼠标抬起事件
    box.onmouseup = box.onmouseout = () => {                      
      off = 0
      box.style.cursor = 'default'      
    }
  },
  update(el, binding) {
    !el.style.left && (el.style.left = '50%')
    !el.style.top && (el.style.top = '50%')
  }
})

/*
文本溢出提示
*/
function txtBreak(txt) {
  // 把字符串拆分成多行
  let txtArr = [];
  while(txt.length > 60) {
    txtArr.push(txt.substring(0, 60));
    txt = txt.slice(60)
    if(!(txt.length > 60)) {
      txtArr.push(txt);
      break;
    }
  }
  return txtArr.length ? txtArr.join('<br>') : txt;
}
Vue.directive('tooltip', {
  inserted(el, binding) {
    // 创建提示盒子
    let $tooltip = document.getElementById('xpt-tooltip');
    if(!$tooltip) {
      let $div = document.createElement('div');
      $div.setAttribute('id', 'xpt-tooltip');
      document.body.appendChild($div);
    }
    // 判断文本是否溢出
    el.setAttribute('class', el.className + ' xpt-text_ellipsis');
    el.onmouseenter = function(e) {
      if(el.scrollWidth === el.offsetWidth) return;
      let $tip = document.getElementById('xpt-tooltip'),
        $target = e.target,
        targetObj = {
          // 左上角x坐标
          x: e.clientX - e.offsetX,
          // 左上角y坐标
          y: e.clientY - e.offsetY,
          w: $target.clientWidth,
          h: $target.clientHeight
        },
        tipObj = {
          x: '',
          y: '',
          w: '',
          h: '',
        },
        docObj = {
          w: document.body.clientWidth,
          h: document.body.clientHeight
        },
        iObj = {
          x: '',
          y: ''
        },
        cur_x, cur_y;
      // 设置文本内容并获得宽高
      // $tip.innerHTML = $target.innerText + '<i class="el-icon-caret-bottom"></i>';
      $tip.innerHTML = txtBreak($target.innerText) + '<i class="el-icon-caret-bottom"></i>';
      tipObj.w = $tip.clientWidth;
      // tipObj.w = tipObj.w > 600 ? 600 : tipObj.w;
      $tip.style.width = tipObj.w + 'px';
      tipObj.h = $tip.clientHeight;
      // 设置提示文本的x,y坐标
      tipObj.x = targetObj.x + targetObj.w/2 - tipObj.w/2 - 6;
      tipObj.y = targetObj.y - tipObj.h - 7;
      if(tipObj.x < 10) {
        tipObj.x = 10;
      }
      if(tipObj.x + tipObj.w + 10 > docObj.w) {
        tipObj.x = docObj.w - 10 - tipObj.w;
      }
      // 是否向下提示
      let isDown = false;
      if(tipObj.y < 0) {
        isDown = true;
        tipObj.y = targetObj.y + targetObj.h + 8;
      }
      $tip.style.left = tipObj.x + 'px';
      $tip.style.top = tipObj.y + 'px';

      let $i = $tip.getElementsByTagName('i')[0];
      iObj.x = targetObj.x + targetObj.w/2 - 12;
      iObj.x = iObj.x < 15 ? 15 : iObj.x;
      iObj.x = iObj.x < (docObj.w - 30) ? iObj.x : (docObj.w - 30);
      if(isDown) {
        $i.setAttribute('class', 'el-icon-caret-top');
        iObj.y = targetObj.y + targetObj.h;
      } else {
        iObj.y = targetObj.y - 10;
      }
      $i.style.left = iObj.x + 'px'
      $i.style.top = iObj.y + 'px'
      $tip.style.visibility = 'inherit';
    };
    el.onmouseleave = el.onclick = function(e) {
      if(el.scrollWidth === el.offsetWidth) return;
      let $tip = document.getElementById('xpt-tooltip');
      $tip.removeAttribute('style')
    };
  }
})
