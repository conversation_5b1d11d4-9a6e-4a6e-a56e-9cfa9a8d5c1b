import axios from "axios";

export default{
  downFile(data, url, type,fileName) {
    return new Promise((resolve, reject) => {
      axios({
        url: url,
        responseType: 'blob',
        data: data,
        method: 'post'
      })
        .then(function (response) {
          let data = response.data
          console.log(response)
          let blob = new Blob([data], { type: type });
          let url = window.URL.createObjectURL(blob);
          const link = document.createElement("a"); // 创建a标签
          link.href = url;
          link.download = (fileName?`${fileName}-`:"File-") + new Date().getTime(); // 重命名文件
          link.click();
          URL.revokeObjectURL(url); // 释放内存
          resolve()
        }).catch(err => reject(err))
    })
  },
  //同步
  // data:参数
  // url:导出路径
  // type:文件类型
  // fileName:文件名称
  exportSync(data, url, type,fileName){
    return this.downFile(data, url, type,fileName)
  },
  //异步
  exportAsync(){
    //待补充
  }
}
