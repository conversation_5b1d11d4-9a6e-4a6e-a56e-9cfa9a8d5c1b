var helpFile = {
    //售后单
    'AFTER_ORDER': {
        //问题商品
        'QUESTION_GOODS': [
            //商品编码
            {
                name: 'material_code',
                alias: '商品编码',
                value: null
            },
            {
                name: 'batch_trade_no',
                alias: '批次单号',
                value: null
            },
            {
                name: 'bom_version',
                alias: '商品BOM版本',
                value: null
            },
            {
                name: 'question_description',
                alias: '问题描述',
                value: null
            }
        ],
        //退换货申请单
        'RETURNANDEXCHANGE':[
            {
                name: 'bill_returns_no',
                alias: '单据编号',
                value: null
            },
            /*{
                name: 'question_goods_code',
                alias: '商品编码',
                value: null
            },*/
            {
                name: 'merge_trade_no',
                alias: '合并订单号',
                value: null
            },
            {
                name: 'after_order_no',
                alias: '售后单号',
                value: null
            }
        ],
        //咨询单
        'CONSULTION':[
            {
                name: 'consultion_no',
                alias: '单据编号',
                value: null
            },
            {
                name: 'merge_trade_no',
                alias: '合并订单号',
                value: null
            },
            {
                name: 'after_order_no',
                alias: '售后单号',
                value: null
            },
            {
                name: 'batch_trade_no',
                alias: '批次单号',
                value: null
            }
        ],
        //退款单
        'REFUNDBILLDETAIL':[
            {
                name: 'bill_no',
                alias: '单据编号',
                value: null
            },
            {
                name: 'merge_trade_no',
                alias: '合并订单号',
                value: null
            },
            {
                name: 'after_order_no',
                alias: '售后单号',
                value: null
            }
        ],
        //退款申请单
        'REFUNDAPPLYBILLDETAIL':[
            {
                name: 'bill_no',
                alias: '单据编号',
                value: null
            },
            {
                name: 'merge_trade_no',
                alias: '合并订单号',
                value: null
            },
            {
                name: 'after_order_no',
                alias: '售后单号',
                value: null
            }
        ],
        //责任分析单
        'ZRFXDETAIL':[
            {
                name: 'sub_bill_no',
                alias: '子单单据编号',
                value: null
            },
            {
                name: 'merge_trade_no',
                alias: '合并订单号',
                value: null
            },
            {
                name: 'after_order_no',
                alias: '售后单号',
                value: null
            }
        ],
        //换货
        'RETURN': [],
        //退款结算单的退款明细
        "REFUND_DETAIL": [
            {
                name: 'refund_type_string',
                alias: '退款类型',
                value: null
            },
            {
                name: 'refund_reason',
                alias: '退款原因',
                value: null
            }
        ],
        // 服务单
        'SERVICE' : [
            {
                name: 'after_ticket_no',
                alias: '单据编号',
                value: null
            },
            {
                name: 'schedule_finish_status',
                alias: '进度完结状态',
                value: null
            },
            {
                name: 'pulled_sign',
                alias: '已拉货标识',
                value: null
            },
            {
                name: 'confirm_status',
                alias: '确认状态',
                value: null
            }
        ],
        // 补件申请单
        'BATCH' : [
            {
                name: 'after_ticket_no',
                alias: '单据编号',
                value: null
            },
            {
                name: 'merge_trade_no',
                alias: '合并订单号',
                value: null
            },
            {
                name: 'after_order_no',
                alias: '售后单号',
                value: null
            },
            {
                name: 'type',
                alias: '补件类型',
                value: null
            },
            {
                name: 'buyer_name',
                alias: '买家昵称',
                value: null
            }
        ]

    },

    //售前售中
    'ORDER': {
        //发票
        'INVOICE': [{
            name: 'style',
            alias: '发票类型',
            value: null
        }],
        'RECEIPT':[{
            name:'receiptNo',
            alias: '收款单编号',
            value: null
        }],
        'DEALERFUNDSMANAGE':[{
            name:'order_no',
            alias: '货款充值编号',
            value: null
        }],
    },
};

export default helpFile;
