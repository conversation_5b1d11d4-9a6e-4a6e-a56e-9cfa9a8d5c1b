import ajax from './ajax.js'
export default{
	// 获取用户信息
	getUserInfo:function(){
		var usi = sessionStorage.getItem('userInfo')||'';
		if(usi){
			usi = window.JSON.parse(usi)
		}
		return usi;
	},
	// 判断是否登录
	isLogin:function(){
		if(this.getUserInfo()) return true;
		return false;
	},
	// 登录
	login:function(data,succ,err){
		sessionStorage.setItem('userInfo',window.JSON.stringify({
			usn:data.employeeNumber
		}))
		// var apiUrl = ;
		// Vue.http.jsonp(apiUrl,{
		// 	params:data,jsonp:'callback',jsonpCallback:'callback'
		// }).then(succ,err)
		ajax.postStream('/login-web/sso/login',data,succ,err)
	},
	logout:function(){
		sessionStorage.clear();
	}
}