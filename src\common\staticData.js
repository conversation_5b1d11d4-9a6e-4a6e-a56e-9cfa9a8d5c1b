var staticData = {
    //优惠类型对应的子分类
    discountClassObj: {
        //线上优惠
        'ONLINE_DISCOUNT': {
            //子类
            childClass: [{ value: 'COUPON', label: '优惠券' }, { value: 'ADJUSTED_COUPON', label: '调整优惠券' }],
            isChild: false, //子类是否可选，false为可选
            'COUPON': {
                discountItem: 'ORDER', //优惠项目
                needAudit: false, //是否审核
                effectPrice: true, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: false, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            },
            'ADJUSTED_COUPON': {
                discountItem: 'ORDER', //优惠项目
                needAudit: true, //是否审核
                effectPrice: true, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: false, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            },
        },
        //线下优惠券
        'OFFLINE_DISCOUNT': {
            childClass: [],
            isChild: true,
            other: {
                discountItem: 'ORDER', //优惠项目
                needAudit: false, //是否审核
                effectPrice: true, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: false, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            }
        },
        //返现
        'REFUND': {
            childClass: [],
            isChild: true,
            other: {
                discountItem: 'ORDER', //优惠项目
                needAudit: true, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            }
        },
        //活动包邮
        'FREE_SERVICE': {
            childClass: [{ value: 'CITY', label: '地级市包邮' }, { value: 'STATE', label: '自治区包邮' }],
            isChild: false,
            'CITY': {
                discountItem: 'FREIGHT', //优惠项目
                needAudit: false, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            },
            'STATE': {
                discountItem: 'FREIGHT', //优惠项目
                needAudit: false, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            }
        },
        //拆单免运费
        'FREE_BATCHES_SERVICE': {
            childClass: [],
            isChild: true,
            other: {
                discountItem: 'FREIGHT', //优惠项目
                needAudit: false, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            }
        },
        //差价减免
        'REDUCTION': {
            childClass: [],
            isChild: true,
            other: {
                discountItem: 'GOODS', //优惠项目
                needAudit: true, //是否审核
                effectPrice: true, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            }
        },
        //商品活动
        'PRESENT_GOODS': {
            childClass: [{ value: 'GIFTS', label: '赠品' }, { value: 'TRADE_UP', label: '换购' }],
            isChild: false,
            'GIFTS': {
                discountItem: 'GOODS', //优惠项目
                needAudit: false, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: false, //金额变更
                editGoodsDetail: true, //是否可以操作商品明细
                //商品 明细
                goodsDetail: {
                    isEditMateriel: true, //是否可编辑物料
                    isPresent: true, //是否是赠品
                    isEditGift: true, //是否可编辑礼品
                    isGift: true, //是否是礼品
                    isEditPrice: true, //是否可编辑价格
                    price: 0, //价格
                    isEditCost: true, //是否可编辑成本
                    cost: 0, //成本
                    isEditLimit: true, //是否可编辑限量
                    limit: 0, //限量
                },
            },
            'TRADE_UP': {
                discountItem: 'GOODS', //优惠项目
                needAudit: false, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: false, //金额变更
                editGoodsDetail: true, //是否可以操作商品明细
                goodsDetail: {
                    isEditMateriel: false, //是否可编辑物料
                    isPresent: false, //是否是赠品
                    isEditGift: false, //是否可编辑礼品
                    isGift: false, //是否是礼品
                    isEditPrice: true, //是否可编辑价格
                    price: '', //价格
                    isEditCost: true, //是否可编辑成本
                    cost: 0, //成本
                    isEditLimit: true, //是否可编辑限量
                    limit: 0, //限量
                },
            }
        },
        //特殊优惠
        'SPECIAL_DISCOUNT': {
            childClass: [{ value: 'BUSINESS_DISCOUNT', label: '商务折扣' }, { value: 'DELAY_REDUCTION', label: '延误换货' }],
            isChild: false,
            'BUSINESS_DISCOUNT': {
                discountItem: 'ORDER', //优惠项目
                needAudit: true, //是否审核
                effectPrice: true, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            },
            'DELAY_REDUCTION': {
                discountItem: 'ORDER', //优惠项目
                needAudit: true, //是否审核
                effectPrice: true, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            }
        },
        //责任金
        'COMPENSATION': {
            childClass: [],
            isChild: true,
            other: {
                discountItem: 'ORDER', //优惠项目
                needAudit: true, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            }
        },
        //店长基金
        'SHOP_FOUNDATION': {
            childClass: [{ value: 'SHOP_GIFTS', label: '赠品' }, { value: 'FREIGHT_COMPENSATION', label: '运费' }, { value: 'EXCHANGE_REWARD', label: '多拍奖励' }],
            isChild: false,
            'SHOP_GIFTS': {
                discountItem: 'GOODS', //优惠项目
                needAudit: false, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: false, //金额变更
                editGoodsDetail: true, //是否可以操作商品明细
                //商品 明细
                goodsDetail: {
                    isEditMateriel: true, //是否可编辑物料
                    isPresent: true, //是否是赠品
                    isEditGift: true, //是否可编辑礼品
                    isGift: true, //是否是礼品
                    isEditPrice: true, //是否可编辑价格
                    price: 0, //价格
                    isEditCost: true, //是否可编辑成本
                    cost: 0, //成本
                    isEditLimit: true, //是否可编辑限量
                    limit: 0, //限量
                },
            },
            'FREIGHT_COMPENSATION': {
                discountItem: 'ORDER', //优惠项目
                needAudit: false, //是否审核
                effectPrice: false, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 5.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            },
            'EXCHANGE_REWARD': {
                discountItem: 'ORDER', //优惠项目
                needAudit: false, //是否审核
                effectPrice: true, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: false, //金额变更
                editGoodsDetail: true, //是否可以操作商品明细
                //商品 明细
                goodsDetail: {
                    isEditMateriel: false, //是否可编辑物料
                    isPresent: false, //是否是赠品
                    isEditGift: false, //是否可编辑礼品
                    isGift: false, //是否是礼品
                    isEditPrice: true, //是否可编辑价格
                    price: 0, //价格
                    isEditCost: true, //是否可编辑成本
                    cost: 0, //成本
                    isEditLimit: true, //是否可编辑限量
                    limit: 0, //限量
                },
            },
        },
        // 订金膨胀
        'DEPOSIT_RISE': {
            childClass: [],
            isChild: true,
            other: {
                discountItem: 'ORDER', //优惠项目
                needAudit: true, //是否审核
                effectPrice: true, //是否影响售价
                editDiscoutMoney: true, //是否可编辑优惠金额
                // discountMoney: 0.00, //优惠金额
                edituserThreshold: true, //是否可编辑使用门槛
                // userThreshold: 0.00, //使用门槛
                moneyChange: true, //金额变更
                editGoodsDetail: false, //是否可以操作商品明细
            }
        }
    }

};

export default staticData;