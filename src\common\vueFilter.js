import Vue from 'vue';
import Fn from '@common/Fn.js'

/*
	日期过滤
	val:要过滤的时间，
	fm:'匹配的格式'
*/
Vue.filter('dataFormat', function(val, fm) {
    var format = Fn.dateFormat(val, fm);
    return format;
})

Vue.filter('dataFormat1', function(val) {
    var format = Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
    return format;
})

Vue.filter('dataFormat2', function(val) {
    var format = Fn.dateFormat(val, 'yyyy年MM月dd日');
    return format;
})
Vue.filter('dataFormat3', function(val) {
  var format = Fn.dateFormat(val, 'yyyy-MM-dd');
  return format;
});

/*
状态（根据批次订单处理流程反写：A=未货审、B=以预货审、C=已货审、D=已财审、E=出库作业中、F=已发货)
*/
Vue.filter('statusChangs', function(val) {
    var status_options = {
        WAITING: "等待发运",
        RESERVED: "已保留",
        PASS_GOODS_AUDIT: "已货审",
        PASS_FINANCIAL_AUDIT: "已财审",
        SENT_TO_ZD: "已发送",
        DELIVERED: "已发运",
        CANCEL: '取消'
    }
    return status_options[val];
});


/*
批次订单货审状态：
*/
Vue.filter('auditStatusChange', function(val) {
    var goods_audit_status_obj = {
        WAITING: "未货审",
        PART_RESERVED: "部分保留",
        RESERVED: "已保留",
        REVIEWED: "已反审",
        PASS_GOODS_AUDIT: "已货审"
    }
    return goods_audit_status_obj[val];
});

/*
显示item的长度，只显示一行，超过一行的长度则title显示
*/
Vue.filter('cutString', function(str) {
    if (!str) return
    var val
    var width = document.body.clientWidth / 3 - 168
    if (width <= 0) return
    var theWidth = (Math.ceil(width / 12) + 1) * 2
    var length = str.length
    var count = 0
    var han = 0
    for (var i = 0; i < length && han * 2 + count < theWidth; i++) {
        if (str.charCodeAt(i) > 255) {
            han += 1
        } else {
            count += 1
        }
    }
    val = han * 2 + count >= theWidth ? str.substring(0, han + count - 4) + '...' : str.substring(0, han + count)
    return val
})

/*
批次订单财审状态：
*/
Vue.filter('financialStatusChange', function(val) {
    var financial_audit_status_obj = {
        WAITING: "未财审",
        PASS_FINANCIAL_AUDIT: "已财审"
    }
    return financial_audit_status_obj[val];
});


/*大于零的整数，保留两位小数点*/
Vue.filter('number', function(num) {
        if (num == 'undefined' || num == null) return '';
        return Fn.number(num);
    })
Vue.filter('numberFixed', Fn.numberFixed);
    /*根据辅助资料code返回辅助资料name*/
Vue.filter('auxFormat', (val, type) => {
    const values = (val ?? '').split(',');
    let _data = __AUX.get(type);
    const result = [];
    values.forEach(val => {
      const obj = _data.find(d => d.code === val);
      result.push(obj ? obj.name : val);
    });

    return result.join(',');
})
Vue.filter('statusFilter', (val) => {
    return val ? '生效' : '失效'
})
Vue.filter('systemFilter', val => {
        return val ? '系统' : '自定义'
    })
    // 代理原因
Vue.filter('reasonFilter', val => {
        switch (val) {
            case 'HOLIDAY':
                return '休假'
            case 'LEAVE':
                return '离职'
            case 'OTHERS':
                return '其它'
            default:
                return val
        }
    })
    //对是否的格式化，1：是，0：否，null:空
Vue.filter('ifFilter', val => {
        return val === 1 ? '是' : val === 0 ? '否' : ''
    })
    // 订单来源
// Vue.filter('orderSourceFilter', val => {
//     let obj = __ORSERSOURCE.find(d => d.code === val) || {}
//     return obj.name
// })

//文件大小格式化
Vue.filter('fileSize', val => {
    if (isNaN(val) || val < 0) return "0B";
    var val = parseFloat(val);
    if (val >= 1024 * 1012) return parseFloat(val / (1024 * 1024)).toFixed(2) + "M";
    if (val >= 1024) return parseFloat(val / (1024)).toFixed(2) + "K";
    if (val < 1024) return parseFloat(val) + "B";

});

/*
*追责单的单据状态
*/
Vue.filter('orderStatus', function(val) {
   var orderStatus = {
        CREATE:'创建',
        CLOSED:'已关闭',
        SUBMITTED:'提交审核',
        APPROVED:'已审核',
        RETRIAL:'重新审核',
        REJECTED:'已拒绝',
        CANCELED:'已取消' ,
        WITHDRAWED:'已撤回'
    }
    return orderStatus[val];
});

Vue.filter('yesOrNo', val => {
    // console.log('yesOrNo',typeof val);
    if(typeof val != 'number' && typeof val != 'string' && typeof val !== 'boolean') return val;
    if(val === '0' || val === 'N' || val === 0 || val === false) return '否';
    if(val === '1' || val === 'Y' || val === 1 || val === true) return '是';
    return val;
    //return val ? '是': '否' ;
})

// 价格格式化
Vue.filter('priceFilter', val => {
    if(!val) return val;
    return val.toFixed(2)
})

// 客户生、失效状态
Vue.filter('customerStatusFilter', val => {
    if(val === 'A'){
        return '失效'
    } else if(val === 'B') {
        return '生效'
    } else {
        return val
    }
})

// 客户类型
Vue.filter('customerTypeFilter', val => {
    let str = ''
    switch(val) {
        case 'TB': str = '淘宝客户';break;
        case 'B2C': str = '商城客户'; break;
        case 'APP': str = '导购App客户'; break;
        case 'none': str = '其它客户'; break;
        case 'PDD': str = '拼多多'; break;
        default: str = val; break;
    }
    return str
})

// 支付渠道
Vue.filter('paymentChannelFilter', val => {
    if(val === 'TAOBAO') {
        return '淘宝'
    } else if(val === 'B2C') {
        return '商城'
    } else {
        return val
    }
})

// 支付方式
Vue.filter('payTypeFilter', val => {
    var payType = __AUX.get('payType');
    var obj = {};
    for(let i in payType){
        obj[payType[i].code] = payType[i].name;
    }
    //var type = Fn.getPayType();
    return obj[val] || val;

})

// 回访类型单据状态
Vue.filter('returnVistTypeStatusFilter', val => {
    if(val === 'ENABLE') {
        return '生效'
    } else if(val === 'DISABLE') {
        return '失效'
    } else if(val === 'CREATE') {
        return '保存'
    } else {
        return val
    }
})

// 收款单的单据类型
/*Vue.filter('receiptType', val => {

    var receiptType = Fn.getReceiptType();
    return receiptType[val] || val;

})*/

// 销售订单单据类型
Vue.filter('orderTypeFilter', val => {
    if(val === 'TAOBAO') {
        return '淘宝订单'
    } else if(val === 'B2C') {
        return 'B2C商城订单'
    } else if(val === 'O2O') {
        return 'O2O实体店'
    } else if(val === 'PRESENT') {
        return '赠品订单'
    } else if(val === 'PDD') {
        return '拼多多'
    }else{
        return val;
    }
})

//收款单的单据状态
Vue.filter('receiptStatus', val => {
    var status = Fn.getStatus();
    return status[val] || val;

})

//收款单的支付渠道
Vue.filter('paymentChannel', val => {
    var payment = Fn.paymentChannel();
    return payment[val] || val;
})

//收款单的同步状态
Vue.filter('synStatus', val => {
    var synStatus = Fn.synStatus();
    return synStatus[val] || val;
});

//店铺状态
Vue.filter('shopStatus', val => {
    var status = Fn.shopStatus();
    return status[val] || val;
});

//退换货单据退换货方式
Vue.filter('returnsType', val => {
    var status = Fn.returnGoodsType();
    return status[val] || val;
});

//行状态

Vue.filter('rowStatus', val => {
    return val == 'Y'?'已关闭':val == 'N'?'未关闭':val;
});

//送货方式
Vue.filter('deliverMethod', val => {
    let data = Fn.deliverMethod();
    return data[val];
});
//运费类型
Vue.filter('postFeeType', val => {
    let data = Fn.postFeeType();
    return data[val];
});
//原单类型
Vue.filter('sourceOrderType', val => {
    var data = {
        'RETURNS':'退换货',
        'REFUND':'退款',
        'REPAIR':'维修',
        'SUPPLY':'补件'
    }
    return data[val] || val;
});
// 服务单支付类型
Vue.filter('expenseType', val => {
    switch(val) {
        case 'BD_OwnerOrg': return '业务组织'; break;
        case 'BD_Customer': return '客户'; return; break;
        case 'BD_Supplier': return '供应商'; return; break;
        case 'BD_Empinfo': return '员工'; break;
        default : return val; break;
    }
})
// 服务单收入类型
Vue.filter('incomeType', val => {
    switch(val) {
        case 'BD_OwnerOrg': return '业务组织'; break;
        case 'BD_Customer': return '客户'; return; break;
        case 'BD_Supplier': return '供应商'; return; break;
        default : return val; break;
    }
})

// 业务类型
Vue.filter('ddywlx', val => {
    var payType = __AUX.get('ddywlx');
    var obj = {};
    for(let i in payType){
        obj[payType[i].code] = payType[i].name;
    }
    //var type = Fn.getPayType();
    return obj[val] || val;

})



// 取消商品原因
Vue.filter('qxspyy', val => {
    var payType = __AUX.get('qxspyy');
    var obj = {};
    for(let i in payType){
        obj[payType[i].code] = payType[i].name;
    }
    //var type = Fn.getPayType();
    return obj[val] || val;

})


// 淘宝退款申请单确认退款原因
Vue.filter('qrtkyy', val => {
    var payType = __AUX.get('qrtkyy');
    var obj = {};
    for(let i in payType){
        obj[payType[i].code] = payType[i].name;
    }
    //var type = Fn.getPayType();
    return obj[val] || val;

})


// AG退款类型
Vue.filter('ag_type', val => {
    var payType = __AUX.get('ag_type');
    var obj = {};
    for(let i in payType){
        obj[payType[i].code] = payType[i].name;
    }
    //var type = Fn.getPayType();
    return obj[val] || val;

})

//隐藏电话号码
Vue.filter('hidePhoneNumber',(val)=>{
    if(/^0\d{2,3}-?\d{7,8}$/.test(val)||/^1\d{10}$/.test(val)){
        return String(val).includes('-')?`${String(val).substr(0, 4)}****${String(val).substr(8)}`:`${String(val).substr(0, 3)}****${String(val).substr(7)}`;
    }else{
        return val
    }
})

//隐藏地址
Vue.filter('hideAddress',(val)=>{
    let length=val&&String(val).length;
    if(length>=3&&length<=10){
        let headStr=val.substr(0,1)
        let endStr=val.substr(-1)
        let midStr=''.padEnd(length-2,'*')
        return `${headStr}${midStr}${endStr}`
    }else if(length>10){
        let headStr=val.substr(0,2)
        let endStr=val.substr(length-3)
        let midStr=''.padEnd(length-5,'*')
        return `${headStr}${midStr}${endStr}`
    }else{
        return val
    }
    // if (val) {
    //     let newValue="";
    //     let valLength = val.toString().length;
    //     switch (valLength % 3) {
    //       case 0:
    //         newValue=val
    //           .toString()
    //           .split("")
    //           .map((item, index) => {
    //             return index < valLength - 3
    //               ? index % 3
    //                 ? "*"
    //                 : item
    //               : index == valLength - 1
    //               ? item
    //               : "*";
    //           })
    //           .join("");
    //         break;
    //       case 1:
    //         newValue=val
    //           .toString()
    //           .split("")
    //           .map((item, index) => {
    //             return index < valLength - 1 ? (index % 3 ? "*" : item) : item;
    //           })
    //           .join("");
    //         break;
    //       case 2:
    //         newValue=val
    //           .toString()
    //           .split("")
    //           .map((item, index) => {
    //             return index < valLength - 2
    //               ? index % 3
    //                 ? "*"
    //                 : item
    //               : index == valLength - 1
    //               ? item
    //               : "*";
    //           })
    //           .join("");
    //         break;
    //       default:
    //         newValue=val;
    //     }
    //     return newValue
    //   }else{
    //     return val
    //   }
})
//隐藏姓名
Vue.filter('hideName',(val)=>{
    if(val){
        return `${String(val).substr(0,1).padEnd(val.length,'*')}`
      }else{
        return val
      }
})

// 保留百分比
Vue.filter('percent',(val,num=2)=>{
  if ((!val && val !== 0) || Number.isNaN(val)) return '--';
  return `${(val * 100).toFixed(num)}%`;
})
