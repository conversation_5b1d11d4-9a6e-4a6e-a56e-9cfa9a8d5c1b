<!-- 新增、编辑地址对照 -->
<template>
    <div>
        <xpt-headbar>
            <el-button size='mini' type="primary" @click="submit('menu')" slot='left' :disabled='menuBtnStatu'
                :loading='menuBtnStatu'>保存</el-button>
            <el-button type='danger' class='xpt-close' size='mini' @click="closeComponent" slot='right'>关闭</el-button>
        </xpt-headbar>
        <el-form label-position="right" label-width="100px" :model="menu" :rules="rules" ref="menu">

            <el-row :gutter='40'>
                <el-col :span='20'>
                    <el-form-item label="对照类型" prop="mapping_type">
                        <el-radio-group v-model="menu.mapping_type" @change="mappingTypeChange">
                            <el-radio label="PLATFORM">平台</el-radio>
                            <el-radio label="SHOP">店铺</el-radio>
                        </el-radio-group>
                        <!-- <el-switch v-model="menu.mapping_type" on-text="平台" off-text="店铺" on-value="PLATFORM"  off-value="SHOP" @change="mappingTypeChange"    >
                        </el-switch> -->
                    </el-form-item>

                    <el-form-item label="是否生效" prop="effective">
                        <el-switch v-model="menu.effective" on-text="生效" off-text="失效" on-value="1" off-value="0">
                        </el-switch>
                    </el-form-item>

                    <el-form-item label="选择店铺" prop="mapping_entity_shop_name"  v-if="willShow">
                        <xpt-input size='mini' v-model="menu.mapping_entity_shop_name" readonly icon='search'
                            :on-icon-click="selectShop" @change='shopChange' :disabled="false"></xpt-input>
                        <el-tooltip v-if='rules.mapping_entity_shop_name[0].isShow' class="item" effect="dark"
                            :content="rules.mapping_entity_shop_name[0].message" placement="right-start"
                            popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>


                    <el-form-item label='选择平台' prop="mapping_entity" v-else>
						<xpt-select-aux v-model='menu.mapping_entity' aux_name='orderSource'  	@change="changeBillType"  :disabled='false'></xpt-select-aux>
                        <el-tooltip v-if='rules.mapping_entity[0].isShow' class="item" effect="dark"
                            :content="rules.mapping_entity[0].message" placement="right-start"
                            popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>

                    <el-form-item label="平台地址" prop="map_area_name">
                        <el-input v-model='menu.map_area_name' size='mini' :maxlength='20'></el-input>
                    </el-form-item>

                    <el-form-item label="系统地址" prop="system_area_name">
                        <el-input v-model='system_area_name' size='mini' :maxlength='20' :disabled="true"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <!-- 控件end -->
        </el-form>
    </div>
</template>
<script>

    import validate from '@common/validate.js';
    export default {
        props: ['params'],
        data() {
            var self = this;
            return {//默认进入新增菜单,设置默认值
                menu: {
                    id: '',
                    area_id: "",
                    area_level: "",
                    map_area_name: "",
                    mapping_entity: "",
                    mapping_entity_shop_name: "",
                    //mapping_entity_platform_name: "",
                    // 是否生效/失效 默认生效
                    effective: "1",
                    // 对照类型  平台或店铺   默认店铺
                    mapping_type: "SHOP",
                    creator: "",
                    create_time: "",
                    modifier: "",
                    modify_time: "",
                },
                menuList: [],
                loading: false,
                menuBtnStatu: false,
                isUpdate: true,
                isUpdateData:"",
                willShow: true,
                system_area_name:"",//系统地址
                // 数据校验
                rules: {
                    mapping_entity: validate.isNotBlank({
                        required: true,
                        self: self,
                        msg: '平台不能为空',
                        trigger: 'change'
                    }),
                    mapping_entity_shop_name: validate.isNotBlank({
                        required: true,
                        self: self,
                        msg: '店铺不能为空',
                        trigger: 'change'
                    }),
                    map_area_name: validate.isNotBlank({
                        required: true,
                        self: self,
                        msg: '对照名称不能为空',
                    }),

                    type: [{
                        isShow: false,
                        message: '请选择类型',
                        trigger: 'change',
                        validator: (rule, value, callback) => {
                            console.log("rules.rule" + rule);
                            console.log("rules.value" + value);
                            console.log("rules.callback" + callback);

                        }
                    }],
                },

            }
        },
        methods: {
            closeComponent() {
                let self = this;
                // 数据拼接对比（仅适用少量数量）
                if(this.isUpdateData){
                     var comparisonData=  this.menu.mapping_type + this.menu.effective + this.menu.map_area_name + this.menu.mapping_entity;
                     if(this.isUpdateData == comparisonData){
                        this.isUpdate = false;     
                     }
                }

                // 关闭提示    
                if (this.isUpdate) {
                    this.$root.eventHandle.$emit('openDialog', {
                        ok() {
                            self.submit('menu', () => {
                                self.$root.eventHandle.$emit("removeAlert", self.params.alertId);})
                        },
                        no() {
                            self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
                        }
                    })
                } else {
                    self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
                }
            },

            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.indexOf(queryString.toLowerCase()) === 0);
                };
            },
            // 保存数据到数据库
            storeData(callback) {
                if(!this.menu.mapping_entity){
                    this.$message.warning("平台、店铺不能为空");
                    return;
                }
                var url = this.menu.id ? '/order-web/api/cloudAreaMappingSets/updateByPrimaryKey' : '/order-web/api/cloudAreaMappingSets/insertList';
                var dataList = [];
                var data = this.menu;
                var _this = this;
                // 设置控件为加载状态
                this.menuBtnStatu = true
                console.log("保存新增数据：" + data);
                dataList.push(data);
                // 保存新增数据
                this.ajax.postStream(url, dataList, function (data) {
                    data = data.body;
                    _this.$message({
                        message:  data.result ? data.msg : data.msg+",请检查是否存在重复数据",
                        type: data.result ? 'success' : 'error'
                    });
                    if (data.result) {
                        _this.menuBtnStatu = false;
                        _this.isUpdate = false;
                        // _this.$root.eventHandle.$emit('updataMenu');
                        // _this.$root.eventHandle.$emit('refreshMenu');
                        _this.resetData(data.content);
                        //_this.$root.eventHandle.$emit('removeAlert',_this.params.tabName);
                    }
                    callback && callback()
                    _this.params.callback(dataList[0].area_id)
                    _this.menuBtnStatu = false;
                }, function (data) {
                    console.log('失败的回调函数')
                    _this.menuBtnStatu = false;
                })
            },
            /*
            * 重置数据
            * **/
            resetData(id) {
                if (!id) return;
                this.menu.id = id;
                var _this = this;
                this.params.__data = JSON.stringify(this._data);
            },
            //保存数据
            submit(formName, callback) {
                this.$refs[formName].validate((valid) => {
                    if (!valid) return false;
                    this.storeData(callback);
                });
            },

            mappingTypeChange(val){
                // 判断是否店铺
                if("SHOP" == val){
                    this.willShow = true;
                }else{
                    this.willShow = false;
                }
                this.menu.mapping_entity = "";
                this.menu.mapping_entity_shop_name = "";
            },

			/**
			*单据类型改变
			**/
			changeBillType(val, data){
                console.log(val,data)
                this.menu.mapping_entity = data.code;
                // this.menu.mapping_entity_platform_name = data.name;
                this.menu.mapping_entity_shop_name = data.name;
			},

            /**选择店铺列表**/
            selectShop() {
                this.$root.eventHandle.$emit('alert', {
                    params: {
                        selection: 'radio',
                        shop_status: 'OPEN',
                        callback: data => {
                            console.log("data:" + data);
                            console.log("JSON.stringify(data):" + JSON.stringify(data));
                            this.menu.mapping_entity = data.shop_code;
                            this.menu.mapping_entity_shop_name = data.shop_name;
                        },
                    },
                    component: () => import('@components/shop/list'),
                    style: 'width:800px;height:500px',
                    title: '店铺列表',
                })
            },

            shopChange(val) {
                console.log("shopChange:" + val);
                this.menu.mapping_entity = "";
                this.menu.mapping_entity_shop_name = "";
            },
        },
        created(){
            if(this.params&&this.params.id){
                //编辑时，初始化店铺类型，防止类型改变置空店铺或平台
                this.menu.mapping_type=this.params.mapping_type
            }
        },
        // 进入页面后触发
        mounted() {
            console.log(this.params)
            // 获取参数并赋值
            if (this.params) {
                //this.menu = this.params;
                this.menu.area_level = this.params.area_level;
                this.menu.cloud_area_id = this.params.cloud_area_id;
                this.menu.map_area_name = this.params.area_name;
                this.menu.area_id = this.params.cloud_area_id;
                this.system_area_name=this.params.area_name;
            }

            // 编辑页面赋值 在此处理   根据id来判断是编辑还是新增
            if (this.params.id) {
                console.log("编辑");

                this.menu.id = this.params.id;
                this.menu.area_id = this.params.area_id;
                this.menu.mapping_entity = this.params.mapping_entity;
                this.menu.mapping_entity_shop_name = this.params.mapping_entity_shop_name;
                
                // 获取平台对照
                if(this.params.mapping_entity){
                    __AUX.get('orderSource').forEach( item => {
                        if(item.status == 1 && item.code == this.params.mapping_entity ){
                            //this.menu.mapping_entity_platform_name = item.name;
                            this.menu.mapping_entity_shop_name = item.name;
                        }
                    });
                }
                console.log(this.menu.mapping_entity)
                // 根据类型判断 隐藏平台或者店铺控件
                this.menu.mapping_type = this.params.mapping_type;
                if("SHOP" == this.params.mapping_type){
                    this.willShow = true;
                }else{
                    this.willShow = false;
                }
                this.menu.map_area_name = this.params.map_area_name;
                this.system_area_name=this.params.system_area_name;
                // 设置控件是否生效
                if (1 == this.params.effective) {
                    this.menu.effective = "1";
                } else {
                    this.menu.effective = "0";
                }
                this.menu.creator = this.params.creator;
                this.menu.create_time = this.params.create_time;
                this.menu.modifier = this.getUserInfo('employeeNumber');
                this.menu.modify_time = new Date();

                this.isUpdateData = this.menu.mapping_type + this.menu.effective + this.menu.map_area_name + this.menu.mapping_entity;

            } else {
                this.menu.creator = this.getUserInfo('employeeNumber');
                this.menu.create_time = new Date();
            }

            this.params.__data = JSON.stringify(this._data);

            console.log("this.params.id:" + this.params.id);
        }
    }
</script>