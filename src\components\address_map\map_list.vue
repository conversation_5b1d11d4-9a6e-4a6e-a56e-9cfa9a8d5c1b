<!-- 销售订单导入 与 退款单 地址对照-->
<template>
	<div class='xpt-flex'>
		<el-row>
			<xpt-headbar>
				<el-button type='primary' size='mini' @click="addAreaMappingSets" slot='left'>添加对照</el-button>
				<el-button type='success' size='mini' @click='_getData()' slot='left' :loading='refreshBtnStatus'
					:disabled='refreshBtnStatus'>刷新</el-button>
				<el-input placeholder="请输入地址名称" icon="search" size='mini' v-model="search.area_name"
					:on-icon-click="searchData" slot='right'>
				</el-input>
			</xpt-headbar>
			<el-tabs v-model="firstTab" style="height:300px; margin-bottom: 30px;">
				<el-tab-pane label='省市区基础信息' name='detail' class='xpt-flex'>
					<div class='el-tree-header'>
						<ul>
							<li>名称</li>
							<li>级别</li>
							<li>编码</li>
						</ul>
					</div>
					<div class='xpt-flex__bottom scroll'>
						<tree :data="addressMapList" :props="props" :load="loadNode"  :defaultExpandAll='false' :expand-on-click-node = "false"  :lazy="false" node-key='cloud_area_id' show-checkbox
							check-strictly @check-change="handleCheckChange" @user-click='userClick' ref='menuTree'>
						</tree>
					</div>
				</el-tab-pane>
			</el-tabs>
		</el-row>

		<el-row class='xpt-flex__bottom'  v-fold>
			<xpt-headbar>
				<!-- <el-button type='primary' size='mini' @click="updateAreaMappingSetsFun" slot='left'>编辑</el-button> -->
				<el-button type='success' size='mini' @click='upd(1)' slot='left' :loading='false' :disabled='false'>生效
				</el-button>
				<el-button type='warning' size='mini' @click='upd(0)' slot='left' :loading='false' :disabled='false'>失效
				</el-button>
				<el-button type='danger' size='mini' @click='del()' slot='left' :loading='false' :disabled='false'>删除
				</el-button>
			</xpt-headbar>
			<el-tabs v-model="secondTab" style="padding-bottom: 50px !important;">
				<!-- <el-tab-pane label='省市区对照明细' name='config' class='xpt-flex xpt-flex__bottom'> -->
				<el-tab-pane label='省市区对照明细' name='config' class='xpt-flex__bottom'>
					<!--  在这里添加list -->
					<el-table :data="addressMapTableData" @selection-change="_selectArr => selectArr = _selectArr"
						:highlight-current-row="true" style="flex-grow: 1" class="table-view" v-loading="false"
						ref="areaMappingSetsList">
						<!-- <el-table-column   type="selection" width="55" :selectable="selectableFun"></el-table-column>  :selectable="selectableFun 导致无法勾选？ -->
						<el-table-column type="selection"></el-table-column>
						<el-table-column :show-overflow-tooltip="true" type="index" label="序号" />
						<el-table-column :show-overflow-tooltip="true" prop="mapping_type" label="对照类型"
							:formatter="isMappingType" />

						<el-table-column :show-overflow-tooltip="true" prop="mapping_entity_shop_name"  :formatter="ismappingEntityType" label="店铺名称" />

						<!-- <el-table-column :show-overflow-tooltip="true" prop="mapping_entity" :formatter="ismappingEntityType" label="平台名称" /> -->
                        <el-table-column :show-overflow-tooltip="true" prop="area_level" label="地区级别"
							:formatter="isAreaLevel" />
						<el-table-column :show-overflow-tooltip="true" prop="map_area_name" label="平台地址">
							<template slot-scope="scope">
								<a href="javascript:void(0)"
									@click="updateAreaMappingSets(scope.row)">{{scope.row.map_area_name}}</a>
							</template>
						</el-table-column>
                        <el-table-column :show-overflow-tooltip="true" prop="system_area_name" label="系统地址" />
						<el-table-column :show-overflow-tooltip="true" prop="effective" label="生效/失效"
							:formatter="isEffective" />
						<el-table-column :show-overflow-tooltip="true" prop="create_name" label="创建人" />
						<el-table-column :show-overflow-tooltip="true" prop="create_time" label="创建时间"
							:formatter="formatTime" />
						<el-table-column :show-overflow-tooltip="true" prop="modify_name" label="修改人" />
						<el-table-column :show-overflow-tooltip="true" prop="modify_time" label="修改时间"
							:formatter="formatTime" />
					</el-table>

				</el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
	import Fn from "@common/Fn.js";
	import tree from '@components/common/tree'
	export default {
		data() {
			let self = this;
			return {
				// 查询条件
				search: {
					area_name: ''
				},
				selectArr: [],
				addressMapList: [],
				selectMenu: [],
				addressMapTableData: [],
				checkedId: "",
				props: {
					// 列明细
					label: [
						{
							name: 'area_name',
							childNode: 'a',
							event(d) {
								console.log("点击地址名称获取对照信息:" + d);
								self.getAreaMappingSetsList(d.cloud_area_id);
							}
						},
						{
							name: 'area_level_name',
							// 地区匹配 地区级别(PROVINCE（省市)、PREFECTURE（地市）、COUNTY（区县）)
						},
						{
							name: 'area_number',
						},
					],
					children: 'children'
				},

				checkData: '',
				date: '',
				time: new Date(),
				refreshBtnStatus: false,
				firstTab: 'detail',
				secondTab: 'config',
			}
		},
		methods: {

			formatter(row, col) {
				if (!!col.formattor) {
					return col.formattor(row[col.prop]);
				}
				return row[col.prop];
			},
			formatTime(row, col, val) {
				return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
			},
			formatDate(row, col, val) {
				return Fn.dateFormat(val)
			},

			isEffective(row, col, val) {
				switch (row.effective) {
					case 1:
						return "生效";
						break;
					case 0:
						return "失效";
						break;
					default:
						return row.effective;
						break;
				}
			},
			isAreaLevel(row, col, val) {
				switch (row.area_level) {
					case "COUNTY":
						return "区";
						break;
					case "PREFECTURE":
						return "市";
						break;
					case "PROVINCE":
						return "省";
						break;
					default:
						return row.area_level;
						break;
				}
			},
			isMappingType(row, col, val) {
				switch (row.mapping_type) {
					case "SHOP":
						return "店铺";
						break;
					case "PLATFORM":
						return "平台";
						break;
					default:
						return row.mapping_type;
						break;
				}
			},
			// 店铺名称/平台名称转换
			ismappingEntityType(row, col, val){
				if("SHOP" == row.mapping_type ){
					return val;
				}else{
					var result = val;
					__AUX.get('orderSource').forEach( item => {
                        if(item.status == 1 && item.code == row.mapping_entity ){
							result = item.name;
                        }
					});
					return  result;
				}
			},

			addAreaMappingSets() {//新增菜单
				//let data = this.selectMenu[0];
                // 把data 作为参数传递到新增页面
                let self=this
				var data = this.checkData;
				if ("undefined" == data || "" == data || null == data) {
					this.$message.warning("请勾选对应地址信息");
					return;
                }
                data.callback=(areaId)=>{
                    self.getAreaMappingSetsList(areaId);
                }
				this.$root.eventHandle.$emit('alert', {
                    title: '添加地址对照',
                    style: "width:600px;height:300px",
					params: data,
					component: () => import('@components/address_map/add'),
					// 传递参数
					// params: {
					// 	callbackEventName: 'refreshMenu',
					// 	select: data
					// }
				})
			},

			//编辑
			updateAreaMappingSets(data) {
                let self=this
				console.log(data);
				if ("" == data || null == data || "undefined" == data) {
					return;
                }
                data.callback=(areaId)=>{
                    self.getAreaMappingSetsList(areaId);
                }
				this.$root.eventHandle.$emit('alert', {
                    title: '编辑地址对照',
                    style: "width:600px;height:300px",
					params: data,
					component: () => import('@components/address_map/add'),
				})
			},

			//编辑
			// updateAreaMappingSetsFun() {				
			// 	if (this.selectArr.length < 1) {
			// 		self.$message({ message: '请选择要编辑的地址对照信息', type: "warning" })
			// 		return;
			// 	}
			// 	var data = this.selectArr[0];
			// 	this.$root.eventHandle.$emit('creatTab', {
			// 		name: '编辑地址对照',
			// 		params: data,
			// 		component: () => import('@components/address_map/add'),
			// 	})

			// },

			//删除对照
			del() {				
				if (this.selectArr.length < 1) {
					this.$message.error("请选择要删除的数据！");
					return;
				}
				const ids = this.selectArr.map((item) => item.id);
				var  area_id= this.selectArr[0].area_id;
				this.$confirm(
					"当前操作会导致选中的  对照明细数据 被删除，是否继续？",
					"提示",
					{
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "danger",
					}
				).then(() => {
					// 请求后台delete 接口 -- 待修改
					this.ajax.postStream(
						"/order-web/api/cloudAreaMappingSets/batch_delete",
						ids,
						(res) => {
							if (res.body.result) {
								this.$message.success(res.body.msg || "删除成功！");
								this.getAreaMappingSetsList(area_id);
							} else {
								this.$message.error(res.body.msg || "删除失败！");
							}
						},
						(err) => {
							this.$message.error(err);
						}
					);
				}).catch(() => {
					return false;
				});

			},

			// 修改对照明细生效 与 失效
			upd(status) {
				// let userInfo = this.getUserInfo();
				let selectArrValue = this.selectArr;
				if (selectArrValue.length < 1) {
					self.$message({ message: '请选择要更新的地址对照信息', type: "warning" })
					return;
				}
				var cloudAreaId = "";
				var dataList = [];
				// 筛选生效 or 失效
				this.selectArr.map((obj, index) => {
					let data = {
						id: obj.id,
						area_id: obj.area_id,
						area_level: obj.area_level,
						mapping_entity: obj.mapping_entity,
                        map_area_name: obj.map_area_name,
                        // system_area_name:obj.system_area_name,
						effective: status,
						mapping_type: obj.mapping_type,
						creator: obj.creator,
						create_time: obj.create_time,
						// 工号
						modifier: this.getUserInfo('employeeNumber'),
						modify_time: new Date(),
					};
					cloudAreaId = obj.area_id;
					console.log("data:" + data);
					dataList.push(data);
				});
				if (dataList.length < 1) {
					return;
				}
				this.ajax.postStream('/order-web/api/cloudAreaMappingSets/updateByPrimaryKey', dataList, (d) => {
					this.$message({
						message: d.body.msg,
						type: d.body.result ? 'success' : 'error'
					})
					if (d.body.result) {
						this.$message.success("修改成功");
					}
					this.selectArrValue = [];
					// 请求后刷新明细
					this.getAreaMappingSetsList(cloudAreaId);

				}, err => {
					this.$message.error(err);
				})
			},

			// 加载节点
			loadNode(node, resolve) {
				// 是否父级
				if (node.level === 0) {
					//	return resolve([{ name: 'region1' }, { name: 'region2' }]);
				} else {
					// 当前节点  级别为区市终止请求
					if("COUNTY" == node.data.area_level){
						return resolve([])
					}else{
						setTimeout(() => {
							var data = { "cloud_area_parent_id": node.data.cloud_area_id };
							//var data = { "area_level": node.data.area_level ,"cloud_area_parent_id": node.data.cloud_area_id };
							var _this = this;
							// 获取列表数据   /permission-web/api/menu/getMenuTree
							this.ajax.postStream('/order-web/api/cloudAreaMappingSets/getAddressMapTree', data || {}, (data) => {
								if (data.body.result && (null !=  data.body.content || "" != data.body.content) ) {								
									resolve(data.body.content);
									// 判断是否叶子节点 去除小三角
									if("COUNTY" == data.body.content[0].area_level){
										node.childNodes.forEach(item=>{
											item.isLeaf=true
                                        })
                                        console.log(node.childNodes)
									}
								}else{
									return resolve([]);
								}
							}, (err) => {
								_this.$message.error("节点加载失败");
								return resolve([]);
							});
							console.log(data);				
						}, 100);
					}
				}
            },
            // 根据搜索结果设置展开层级，省-》不展开，市-》展开省，区-》展开市
            _controlExpand(content){
                if(!content){
                    return []
                }
                let _this=this;
                _this.$refs.menuTree.store.defaultExpandedKeys=[]
                let list=content;
                list.forEach((val,index)=>{
                    if(!!val.children&&val.children.length>0){
                        if(!!val.children[0].children&&val.children[0].children.length>0){
                            // _this.$refs.menuTree.store.defaultExpandAll=false;
                            _this.$refs.menuTree.store.defaultExpandedKeys.push(val.children[0].cloud_area_id)
                        }else{
                            // _this.$refs.menuTree.store.defaultExpandAll=false;
                            _this.$refs.menuTree.store.defaultExpandedKeys.push(val.children[0].cloud_area_parent_id)
                            val.children[0].children=[{}]
                        }
                    }else{
                        // _this.$refs.menuTree.store.defaultExpandAll=false;
                        val.children=[{}]
                    }
                })
                console.log(list,_this.$refs.menuTree.store.defaultExpandAll,_this.$refs.menuTree.store.defaultExpandedKeys)
                return list
            },
			// 获取树列表
			_getData(data) {
				if(!data){
                    this.$refs.menuTree.store.lazy=true;
					data = {"area_level": "PROVINCE"}
				}				
				var _this = this;
                this.refreshBtnStatus = true;
				// 获取列表数据   /permission-web/api/menu/getMenuTree
				this.ajax.postStream('/order-web/api/cloudAreaMappingSets/getAddressMap', data || {}, (data) => {
					if (data.body.result) {
                        _this.addressMapList =  _this._controlExpand(data.body.content) || [];
                        // _this.addressMapList = data.body.content || [];
					} else {
						_this.$message.error(data.body.msg || '');
					}
					_this.selectMenu = [];
                    this.refreshBtnStatus = false;
                    window.setTimeout(()=>{_this.$refs.menuTree.store.lazy=true;},0)
				}, (err) => {
					this.$message.error(err);
                    this.refreshBtnStatus = false;
                    window.setTimeout(()=>{_this.$refs.menuTree.store.lazy=true;},0)
				});
			},
			// 查询
			searchData() {
				//判断收拾条件是否为空
				if (this.search.area_name) {
                        this.$refs.menuTree.store.lazy=false;			
						this._getData({
							"area_name": this.search.area_name
						})					
				} else {
					this._getData();
				}
			},
			// 树列表 手动勾选处理
			handleCheckChange(data, isChecked, childChecked) {

				if(isChecked == false && childChecked == false && this.checkedId == data.cloud_area_id){
					this.checkedId = "";
					this.$refs.menuTree.setCheckedKeys([]);
					return ;
				}

				// 修改为单选
				if(isChecked === true) {
					this.checkedId = data.cloud_area_id;
					this.$refs.menuTree.setCheckedKeys([data.cloud_area_id]);
				} else {
					if (this.checkedId == data.cloud_area_id) {
						this.$refs.menuTree.setCheckedKeys([data.cloud_area_id]);
					}
				}

				// if (isChecked) {
				// 	this.selectMenu.unshift(data);
				// } else {
				// 	let i = this.selectMenu.length;
				// 	while (i--) {
				// 		if (this.selectMenu[i].id === data.id) {
				// 			this.selectMenu.splice(i, 1);
				// 			break;
				// 		}
				// 	}
				// }
			},

			// 勾选触发
			userClick(d) {
				this.checkData = d;
				console.log("勾选触发" + JSON.stringify(d));
			},
			// 如何把数据展现   获取对照明细列表
			async getAreaMappingSetsList(cloudAreaId) {
				if (null == cloudAreaId || "" == cloudAreaId) {
					this.$message.warning("请求参数为空");
					return;
				}
				this.ajax.postStream(
					"/order-web/api/cloudAreaMappingSets/getAreaMappingSetsList",
					{ "area_id": cloudAreaId },
					res => {
						// console.log("getAreaMappingSetsList:" + JSON.stringify(res));
						if (res.body.result) {
							this.addressMapTableData = res.body.content;
						} else {
							if (null != res.body.msg) {
								this.$message.error(res.body.msg);
							} else {
								this.$message.error("操作失败");
							}
						}
						// 刷新数据列
						//this.getDataList();
					},
					err => {
						this.$message.error(err);
					}
				);
			},

		},
		// 属性
		props: ['params'],
		// 页面加载
		mounted: async function () {
			var self = this;
			this._getData();
			// 点击刷新是获取列表
			// this.$root.eventHandle.$on('refreshMenu', function () {
			// 	self._getData();
			// })
		},
		destroyed() {
			// 销毁根节点的所有监听事件 （监控add 页面）
			this.$root.offEvents('refreshMenu');
		},

		components: {
			'tree': tree
		}
	}
</script>