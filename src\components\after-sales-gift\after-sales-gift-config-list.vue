<!--
* @description:
* @author: bin
* @date: 2025/3/9
-->
<template>
  <div>
    <custom-list-dynamic
      :data='dataSource'
      :btns='btns'
      :colData='cols'
      :pageTotal='pageTotal'
      :showCount='showCount'
      :searchPage='page_name'
      selection='checkbox'
      @search-click='searchData'
      @current-page-change='pageSizeChange'
      @selection-change="onSelectChange"
      @count-off="getTotalCount"
      @page-size-change="pageChange"
      ref="selectSupplierGood"
      rowKey="id"
    >

      <template #comp_amount="{row}">
        <template v-if="row.isEdit">
          <div style="display: flex; justify-content: flex-start">
            <div >
              <el-input-number style="width: 70px" class="input-number-class" size="small" :precision="0"  :controls="false" v-model="row.min_comp_amount"  :min="1"  label="最小值"></el-input-number>
            </div>

            <div>~</div>

            <div >
              <el-input-number  style="width: 70px;height: 90%" size="small" class="input-number-class"  :precision="0" :controls="false" controls-position v-model="row.max_comp_amount"  :min="1" label="最大值"></el-input-number>
            </div>
          </div>
        </template>
        <template v-else>
          <div>{{row.min_comp_amount}}~{{row.max_comp_amount}}</div>
        </template>
      </template>
      <template #inventory="{row}">
        <template v-if="!row.isEdit">
          <div class="cursor-pointer" @click="onCheck(row)">查看</div>
        </template>
      </template>
      <template #picture="{row}">
        <template v-if="!row.isEdit">
          <div class="cursor-pointer" @click="onShowImg(row)">查看</div>
        </template>
      </template>
      <template #status="{row}">
        <div>
          <template v-if="row.isEdit">
            <el-select v-model="row.status" :disabled="!row.isEdit" size="mini" style="width: 80%">
              <template v-for="(item,index) in Object.keys(STATUS_MAP)" >
                <el-option :label="STATUS_MAP[item]" :value="Number(item)">{{ STATUS_MAP[item] }}</el-option>
              </template>
            </el-select>
          </template>
          <template v-else>
            <span :style="{color:row.status===2?'red':'inherit'}">{{ STATUS_MAP[row.status] }}</span>
          </template>
        </div>

      </template>
    </custom-list-dynamic>
  </div>
</template>
<script>
import CustomListDynamic from '@components/common/list-dynamic-other-new/list-dynamic'
export default {
  name: 'after-sales-gift-config-list',
  components: {
    CustomListDynamic
  },
  data() {
    let _this = this
    return {
      showCount:false,
      search: {
        length: this.pageSize,
        pageNo: 1,
      },
      STATUS_MAP:{
        1: '生效',
        2: '失效'
      },
      dataSource:[],
      selectCode:'',
      pageTotal:0,
      page_name: 'aftersale_gift_config',
      where: [],
      selectList:[],
      cols: [
        {
          label: '物料编码',
          prop: 'material_code',
          bool: true,
          iconClick:_this.goodsChoose,
          disabled(row) {
            return !row.isEdit
          },
          blur: _this.goodsCopy,
          width:180
        }, {
          label: '物料名称',
          prop: 'material_name',
          width:160
        }, {
          label: '规格描述',
          prop: 'specification_description',
          minWidth: 300
        }, {
          label: '单位',
          prop: 'unit',
          width:60
        },
        {
          label: '状态',
          prop: 'status',
          slot:'status',
          width:100
        },
        {
          label: '库存',
          prop: 'inventory',
          slot:'inventory',
          width:60
        },
        {
          label: '图片',
          prop: 'picture',
          slot:'picture',
          width:60
        },
        {
          label: '赔付金额区间',
          prop: 'comp_amount',
          slot:'comp_amount',
          width:200
        },
        {
          label: '创建人',
          prop: 'create_name',
          width:120
        }, {
          label: '创建时间',
          prop: 'create_time',
          format: "dataFormat1",
          width:160
        }, {
          label: '更新人',
          prop: 'modify_name',
          width:120
        }, {
          label: '最新更新时间',
          prop: 'modify_time',
          format: "dataFormat1",
          width:160
        }
      ],
      btns: [
        {
          type: 'success',
          txt: '刷新',
          click: () => {
            _this.getDataList();
          },
          loading: false
        },
        {
          type: 'primary',
          txt: '新增',
          click: _this.onAdd
        },
        {
          type: 'primary',
          txt: '保存',
          click: _this.onSave
        },
        {
          type: 'success',
          txt: '生效',
          click:() => {
            _this.onSetDataStatus(1);
          },
        },
        {
          type: 'danger',
          txt: '失效',
          click: () => {
            _this.onSetDataStatus(2);
          },
        },
      ],
    }
  },
  methods: {
    onShowImg(row) {
        this.$root.eventHandle.$emit('alert', {
          title: '查看图片',
          style: "width:800px;height:600px;",
					params: {
            bom_material_num: row.material_code,
            material_name: row.material_name,
          },
					component: () => import('@components/after_solutions/materialPicture'),
				})
    },
    radioChange(obj) {
      this.selectCode = obj
    },
    rowDblclick(obj) {
      this.selectCode = obj
      this.close()
    },
    searchData(obj, resolve){
      this.where = obj
      this.search.pageNo = 1
      this.getDataList(resolve);
      this.showCount =false
    },
    changeStatus(row){
      this.$set(row,'status',row,status)
    },
    // 查看内容
    onCheck(row){
      this.$root.eventHandle.$emit('alert', {
        params: {
          value:row?.material_code
        },
        component: () => import('./components/after-sales-gift-config-list/check-after-sales-gift-modal.vue'),
        style: 'width:900px;height:600px',
        title: '查看',
      })
    },
    // 复制
    goodsCopy(row){
      if(!row.material_code||!row.material_code?.trim()){
        return
      }
      let where= [{
        condition: "AND",
        field: "5abf79c56641dadb3a110dc46099614c",
        listWhere: [],
        operator: "=",
        table: "0f1447a0f3e2e9aa5a13ef93c6e4ae38",
        value: row.material_code
      }]
      let data = {
        page_no: 1,
        page_size: 1,
        page_name: 'cloud_express_list_status',
        where: where,
      }
      this.ajax.postStream('/order-web/api/expressList/listStatus',data,d=>{
        if (d.body.result && d.body.content) {
            // this.goodsList = d.body.content.list
            // this.pageTotal = d.body.content.count
          const list = d.body.content.list
          if(list.length>0){
            row.material_code = list[0].material_number
            row.material_name = list[0].material_name
            row.specification_description = list[0].material_specification
            row.unit = list[0].material_unit
          }else{
            row.material_code = ''
            this.$message.error("未查询到数据");
          }
        }else{
          this.$message.error("未查询到数据");
        }

      }, err => {
        this.$message.error(err);

      })
    },
    getDataList(resolve){
     const searchDataObj = {
        page_no: this.search.pageNo,
        page_size: this.search.length,
        page_name: this.page_name,
        where: this.where||[],
      }
      this.getList(searchDataObj).then(()=>{
        resolve?.()
      })
    },
    getTotalCount(){
      if(!this.dataSource.length){
        this.$message.error("当前列表为空，先搜索内容");
        return;
      }
      if(!!this.countOffFlag){
        this.$message.error("请勿重复点击");
        return;
      }
      this.countOffFlag = true
       this.ajax.postStream('/afterSale-web/api/gift/config/count',{
         page_no: this.search.pageNo,
         page_size: this.search.length,
         page_name: this.page_name,
         where: this.where||[],
       },d=>{
         if(d.body.result&&d.body.content){
           this.pageTotal = d.body.content.count;
           this.showCount = true;
           this.countOffFlag = false;
         }else {
           this.$message.error(e.body.msg);
         }

       }, err => {
         this.$message.error(err);
         this.countOffFlag = false
       })

    },
    getList(data){
      return new Promise(resolve=>{
        this.ajax.postStream('/afterSale-web/api/gift/config/list?permissionCode=GIFT_CONFIG_QUERY',data,d=>{
          if(d.body.result&&d.body.content){
            let dataList = JSON.parse(JSON.stringify(d.body.content.list||[]));
            if(dataList.length == (this.search.length+1)&&dataList.length>0){
              dataList.pop();
            }
            let totalCount = this.search.pageNo * this.search.length;
            if(!this.showCount){
              this.pageTotal =d.body.content.count == (this.search.length+1)? totalCount+1:totalCount
            }
            this.dataSource = dataList||[];
          }else {
            this.$message.error(d.body.msg);
          }
          resolve();
        }, err => {
          this.$message.error(err);
           resolve();
        })
      })
    },

    pageSizeChange(pageSize){
      this.search.pageNo = pageSize
      this.getDataList();
    },
    pageChange(page){
      this.search.length = page
      this.search.pageNo = 1
      this.getDataList();
    },
    onSelectChange(row){
      console.log(row,'444')
      this.selectList = row

    },
    // onCellClick(row){
    //   const index = this.selectList.findIndex(item=>item.id===row.id)
    //   const list = [...this.selectList]
    //   if(index!==-1){
    //     list.splice(index,1)
    //   }else{
    //     list.push(row)
    //   }
    //   this.$refs.selectSupplierGood.setSelection(list)
    // },
    /**
     *  新增
     */
    onAdd(){
      // /api/gift/config/addCheck                 对应code:GIFT_CONFIG_ADD/api/gift/config/addCheck                 对应code:GIFT_CONFIG_ADD
      this.ajax.postStream('/afterSale-web/api/gift/config/addCheck?permissionCode=GIFT_CONFIG_ADD',undefined,(res)=>{
        if(res.body.result){
          const obj = {
            material_code:'',
            material_name:'',
            unit:'',
            material_id:'',
            isEdit:true,
            status:  1,
            min_comp_amount:null,
            max_comp_amount:null,
            customId:new Date().getTime()
          }
          this.dataSource.unshift(obj)
        }else{
          this.$message.error(res.body.msg)
        }
      })
    },
    // 保存
    onSave(){
      let  data = this.selectList
      // const data = this.$refs.selectSupplierGood.
      if(!data||data.length===0){
        this.$message.error('请选数据')
        return
      }
      let list = []
      for(let item of data){
        if(!item.isEdit){
          this.$message.error('请选择未保存的数据')
          return
        }
        if(!item.material_code){
          this.$message.error('请选择物料编码')
          return
        }
        if(!item.status){
          this.$message.error('请选择状态')
          return
        }
        list.push({
          material_code:item.material_code||'',
          status:item.status||'',
          material_name:item.material_name||'',
          specification_description:item.specification_description||'',
          unit:item.unit||'',
          id:item.id||null,
          min_comp_amount:item.min_comp_amount||null,
          max_comp_amount:item.max_comp_amount||null,
        })
      }

      this.ajax.postStream('/afterSale-web/api/gift/config/insertAndUdate?permissionCode=GIFT_CONFIG_SAVE', {list},d=>{
        if(d.body.result){
          this.$refs.selectSupplierGood.searchClick?.()
          this.$message.success(d.body.msg)
        }else{
          this.$message.error(d.body.msg);
        }
      }, err => {
        this.$message.error(err);
      })
    },

    onSetDataStatus(type){
      let selectRows = this.selectList;
      const params = {
        ids: (selectRows||[])?.map(e => e.id),
        type
      }
      this.ajax.postStream('/afterSale-web/api/gift/config/birthFailure?permissionCode=GIFT_CONFIG_ENABLE',params,(res)=>{
          if(res.body.result){
            this.$message.success('操作成功')
            this.searchData();
          }else{
            this.$message.error(res.body.msg)
          }
      })
    },
    //
    goodsChoose(e,row){

      new Promise((resolve) => {
        setTimeout(resolve, 10)
      }).then(() => {
        let params = {}
        // 选择商品
        params.callback = d => {

          if(d) {
            // const data = this.dataSource.find(e => e.customId===row.customId)
            row.material_code = d.material_number
            row.material_name = d.material_name
            row.specification_description = d.material_specification
            row.unit = d.material_unit

          }
          console.log(row)
        }

        this.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('./components/after-sales-gift-config-list/add-after-sales-gift-modal.vue'),
          style: 'width:800px;height:500px',
          title: '商品列表'
        });
      })
    },
  },
  mounted() {
    this.getDataList();
  }
}
</script>
<style lang="stylus" scoped>
.cursor-pointer{
  cursor: pointer;
  color: #1876c9;
}
.input-number-class{
  /deep/ .el-input{
    width: 70px;
  }
  /deep/ .el-input--small .el-input__inner{
    height: 25px;
  }
}
</style>
