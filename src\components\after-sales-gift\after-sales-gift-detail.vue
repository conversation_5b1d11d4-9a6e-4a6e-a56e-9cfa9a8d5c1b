<!--
* @description:
* @author: bin
* @date: 2025/3/12
-->
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <template    v-for="(item,index) in btnGroup" >
          <el-tooltip v-if="item.tip" :content="item.tip" placement="top">
            <el-button
              :type='item.type'
              size='mini'
              :key="index"
              @click="item.click"
              :disabled="item.disabled"
              >
              {{item.txt}}
            </el-button>
          </el-tooltip>
          <el-button
            v-else
            :type='item.type'
            size='mini'
            :key="index"
            @click="item.click"
            :disabled="item.disabled"
            >
            {{item.txt}}
          </el-button>
        </template>

      </el-col>
    </el-row>
    <div>
      <el-tabs value="baseInfo" >
        <el-tab-pane label="基本信息" name="baseInfo" class='xpt-flex'>
          <base-info :data-source="dataSource.billGift"/>
        </el-tab-pane>
        <el-tab-pane label="其他信息" name="payment" class='xpt-flex'>
          <other-info :data-source="dataSource.billGift"/>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div style="margin-top:8px">
      <el-tabs value="baseInfo" @tab-click="tabChange">
        <el-tab-pane label="赠品商品信息" name="baseInfo" class='xpt-flex'>
          <el-row	class='xpt-top'	:gutter='40'>
            <el-col :span='20'>
              <template v-for="(item,index) in tabBtnGroup">
                <el-tooltip v-if="item.tip" :content="item.tip" placement="top">
                  <el-button
                    :type='item.type'
                    size='mini'
                    @click="item.click"
                    :disabled="item.disabled"
                    :key="index">
                    {{item.txt}}
                  </el-button>
                </el-tooltip>
                <el-button
                  v-else
                  :type='item.type'
                  size='mini'
                  @click="item.click"
                  :disabled="item.disabled"
                  :key="index">
                  {{item.txt}}
                </el-button>
              </template>
            </el-col>
          </el-row>
          <div style="height: calc(100vh - 394px);min-height: 10px;overflow: auto">
            <after-sale-info-table  :data-source="dataSource.billGiftGoods" ref="afterSalesTable"/>
          </div>

        </el-tab-pane>
        <el-tab-pane label="操作记录" name="logInfo" class='xpt-flex' >
          <div style="height: calc(100vh - 345px);min-height: 10px;overflow: auto">
            <log-info-table :id="params.id" ref="logInfoTable"/>
          </div>

        </el-tab-pane>
        <el-tab-pane label="运费信息" name="freight" class='xpt-flex' >
          <div style="height: calc(100vh - 345px);min-height: 10px;overflow: auto">
          <freight-info-table :id="params.id" ref="freightTable"/>
          </div>
        </el-tab-pane>
        <el-tab-pane label="接口信息" name="interface" class='xpt-flex' >
          <div style="height: calc(100vh - 345px);min-height: 10px;overflow: auto">
          <interface-table :id="params.id" ref="interfaceTable"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>.
  </div>

</template>
<script>
import measureOrderProgress from "../order/components/measureOrderProgress.vue";
import BaseInfo from "./components/after-sales-detail/base-info.vue";
import OtherInfo from "./components/after-sales-detail/other-info.vue";
import LogInfoTable from "./components/after-sales-detail/log-info-table.vue";
import InterfaceTable from "./components/after-sales-detail/interface-table.vue";
import LogisticsInfoTable from "./components/after-sales-detail/logistics-info-table.vue";
import AfterSaleInfoTable from "./components/after-sales-detail/after-sale-info-table.vue";
import FreightInfoTable from "./components/after-sales-detail/freight-info-table.vue";

export default {
  name: 'after-sales-gift-detail',
  props:['params'],
  components: {
    AfterSaleInfoTable,
    FreightInfoTable,
    LogisticsInfoTable,
    InterfaceTable,
    LogInfoTable,
    OtherInfo,
    BaseInfo,
    measureOrderProgress
  },
  data() {
    return {
      dataSource:{
        billGift:{},
        billGiftGoods:[]
      },
    }
  },
  computed:{
    btnGroup(){
      let btn =[
        {
          txt: '刷新',
          icon: 'el-icon-check',
          type: 'primary',
          click: () => {
            this.init()
          }
        },

        {
          txt: '取消售后赠品单',
          icon: 'el-icon-check',
          type: 'danger',
          click: () => {
            this.onCancel()
          }
        },

        {
          txt: '查看附件',
          icon: 'el-icon-check',
          type: 'primary',
          click: () => {
            this.watchUploadFile()
          }
        },

      ]
      if(this.dataSource?.billGift?.order_status === 'CANCEL'){
        btn.forEach(e=>{
          if(e.txt==='取消售后赠品单'||e.txt==='行关闭'){
            e.disabled = true
          }
        })
      }
      return btn
    },

    tabBtnGroup(){
      let btn =  [
        {
          txt: '下推代发采购',
          icon: 'el-icon-check',
          tip: '请勾选 工厂代发=是 且 无采购单号的物料行操作',
          type: 'primary',
          click: () => {
            this.onSendScm()
          }
        },
        {
          txt: '下推仓库发货',
          icon: 'el-icon-check',
          type: 'primary',
          tip: '请勾选 工厂代发=否 且 无发货单号的物料行操作',
          click: () => {
            this.onSend4PL()
          }
        },
        {
          txt: '占用库存',
          icon: 'el-icon-check',
          type: 'primary',
          click: () => {
            this.onIsLock()
          }
        },
        {
          txt: '刷新可用库存',
          icon: 'el-icon-check',
          type: 'primary',
          click: () => {
            let list = this.$refs.afterSalesTable?.getSelection?.()
            if(!list||list.length===0){
              this.$message.error('请选择商品')
              return
            }
            this.updateStore(list,true)
          }
        },
        {
          txt: '取消代发采购',
          icon: 'el-icon-check',
          type: 'danger',
          tip: '请勾选 工厂代发=是 且有采购单号的物料行操作',
          click: () => {
            this.onCancelScm()
          }
        },
        {
          txt: '取消仓库发货',
          icon: 'el-icon-check',
          type: 'danger',
          tip: '请勾选 工厂代发=否 且 有发货单号的物料行操作',
          click: () => {
            this.onCancel4PL()
          }
        },
        {
          txt: '释放库存',
          icon: 'el-icon-check',
          type: 'danger',
          click: () => {
            this.onOccupancyByLine()
          }
        },
        {
          txt: '行关闭',
          icon: 'el-icon-check',
          type: 'danger',
          click: () => {
            this.onCancelByLine()
          }
        },
      ]
      if(this.dataSource?.billGift?.order_status === 'CANCEL'){
        btn.forEach(e=>{
          e.disabled = true
        })
      }
      return btn
    }
  },
  methods: {
    // 初始化
    init(){
      this.ajax.postStream('/afterSale-web/api/gift/bill/getDetail',this.params.id,res=>{
        if(res.body.result){
          const {billGift={},billGiftGoods=[],expire_time,info_from,oaid,areaName,cityName,streetName,priName} = res.body.content || {};
          billGift.expire_time = expire_time;
          billGift.info_from = info_from;
          billGift.areaName = areaName
          billGift.cityName = cityName
          billGift.streetName = streetName
          billGift.priName = priName
          billGift.oaid = oaid;
          this.dataSource = {
            billGift,
            billGiftGoods
          }
          this.updateStore(billGiftGoods)
        }else{
          this.$message.error(res.body.msg)
        }
      }, err => {
        this.$message.error(err);
      })
    },
    // 查看4PL客户上传图片
    watchUploadFile (){
      this.$root.eventHandle.$emit('alert', {
        params: {
          parent_name_txt: '售后单号',
          parent_no : this.dataSource.billGift.after_order_no,
          child_name : 'QUESTION_GOODS',
          child_no : null,
          ext_data : null,
          parent_name : 'AFTER_ORDER',
          notNeedDelBtn: true,
          nickname: this.dataSource.billGift.buyer_name,
          mergeTradeId: this.dataSource.billGift.merge_trade_id,
          isFromAfterSaleIndex: true,
        },
        component: ()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
        style: 'width:80%;height:600px',
        title: '下载列表',
      })
    },
    //
    getParams(name='id',needList=false){
      let list = this.$refs.afterSalesTable?.getSelection?.()
      if((!list||list.length===0)&&!needList){
        return  false
      }
      return {
        goods: (list || []).map(e => e[name]),
        giftId: this.dataSource?.billGift?.id
      }
    },

    // 取消售后赠品单
    onCancel(){
      const alertId =  new Date().getTime()
      const data = {
        alertId:alertId,
        title:'取消售后赠品单',
        callBack:(val)=>{
          return new Promise((resolve,reject)=>{
            const value = {
              giftId: this.dataSource?.billGift?.id,
              cancelReason:val
            }
            this.ajax.postStream('/afterSale-web/api/gift/bill/cancelALL',value,res=>{
              if(res.body.result){
                this.$message.success('操作成功')
                this.init()
                resolve()
                this.init()
              }else{
                this.$message.error(res.body.msg)
                resolve()
              }
            })
          })

        },
        tipContent: '取消售后赠品单后，该单状态为已取消，如还需要下赠品单，请从售后单下推赠品方案'
      }

      this.$root.eventHandle.$emit('alert', {
        params:data,
        component: () => import('@/components/after-sales-gift/components/after-sales-detail/cancel-modal.vue'),
        style: 'width:500px;height:200px',
        title: '取消售后赠品单'
      })
    },
    // 取消仓库发货单
    onCancel4PL(){
      // let list = this.$refs.afterSalesTable?.getSelection?.()
      // if(!list||list.length===0){
      //   return this.$message.error('请选择商品')
      // }
      // for(let item of list){
      //   if(item.third_party_fulfilled==='Y'){
      //     return this.$message.error(`请勾选工厂代发=否的行，进行操作`)
      //   }
      //   if(item.line_status!=='OVER'){
      //      return this.$message.error(`已处理状态的订单才可以取消`)
      //   }
      //   if(item.print_status_locked==='Y'){
      //     return this.$message.error(`4PL打印锁定状态=未打印才可以取消`)
      //   }
      // }

      const value = this.getParams('id',true)

      let _this = this
      const data = {
        alertId: new Date().getTime(),
        callBack:(val)=>{
          return new Promise((resolve,reject)=>{
            const params = {
              ...value,
              cancelReason:val,

            }
            _this.ajax.postStream('/afterSale-web/api/gift/bill/cancelFpl',params,res=>{
              if(res.body.result){
                _this.$message.success('操作成功')
                this.init()
                resolve()
              }else{
                _this.$message.error(res.body.msg)
                resolve()
              }
            })
          })
        },
        title:'取消仓库发货单'
      }

      this.$root.eventHandle.$emit('alert', {
        params: {...data,tipContent:'取消仓库发货单后，会保留库存，如客户不需要继续发货，取消仓库发货单后请继续操作【释放库存】；如果客户需要晚点发货，可以再操作【下推仓库发货单】',},
        component: () => import('@/components/after-sales-gift/components/after-sales-detail/cancel-modal.vue'),
        style: 'width:500px;height:200px',
        title: '取消仓库发货单'
      })
    },
    // 行关闭
    onCancelByLine(){
      // const alertId =  new Date().getTime()
      // const data = {
      //   alertId:alertId,
      //   title:'行关闭',
      //   callBack:(val)=>{
      //     return new Promise((resolve,reject)=>{
      //       const getListMap = this.getParams()
      //       const value = {
      //         ...getListMap,
      //         cancelReason:val
      //       }
      //       this.ajax.postStream('/afterSale-web/api/gift/bill/cancelALL',value,res=>{
      //         if(res.body.result){
      //           this.$message.success('操作成功')
      //           this.init()
      //           resolve()
      //         }else{
      //           this.$message.error(res.body.msg)
      //           resolve()
      //         }
      //       },err=>{
      //         this.$message.error(err)
      //         resolve()
      //       })
      //     })
      //
      //   }
      // }
      // this.$root.eventHandle.$emit('alert', {
      //   params:data,
      //   component: () => import('@/components/after-sales-gift/components/after-sales-detail/cancel-modal.vue'),
      //   style: 'width:500px;height:200px',
      //   title: '行关闭'
      // })
        return new Promise((resolve,reject)=>{
          const value = this.getParams()
          if(!value){
            this.$message.error('请选择商品')
            return
          }
          this.ajax.postStream('/afterSale-web/api/gift/bill/goodslineCancel',value,res=>{
            if(res.body.result){
              this.$message.success('操作成功')
              this.init()
              resolve()
            }else{
              this.$message.error(res.body.msg)
              resolve()
            }
          },err=>{
            this.$message.error(err)
            resolve()
          })
        })
    },

    // tab改变时候触发
    tabChange(val){
      switch (val.name){
        case "interface":
          this.$refs.interfaceTable?.getOperateLog?.()
          break;
        case "logInfo":
          this.$refs.logInfoTable?.getOperateList?.()
          break;
        case "freight":
          this.$refs.freightTable?.getOperateList?.()
          break;
      }
    },

    // 下推4Pl
    onSend4PL(){
      const params =  this.getParams('id',true)
      // if(!params){
      //   return this.$message.error('请选择商品')
      // }
      this.ajax.postStream('/afterSale-web/api/gift/bill/sendFpl',params,res=>{
        if(res.body.result){
          this.$message.success(res.body.msg)
          this.init()
        }else{
          this.$message.error(res.body.msg)
        }
      })
    },
    // 释放库存
    onOccupancyByLine(){
     const data = this.getParams()
      if(!data){
        return this.$message.error('请选择商品')
      }
      this.ajax.postStream('/afterSale-web/api/gift/bill/realseOccupancyByLine',data,res=>{
        if(res.body.result){
          this.$message.success('操作成功')
          this.init()
        }else{
          this.$message.error(res.body.msg)
        }
      }, err => {
        this.$message.error(err);
      })
    },
    // 占用库存
    onIsLock(){
      const data = this.getParams()
      if(!data){
        return this.$message.error('请选择商品')
      }
      this.ajax.postStream('/afterSale-web/api/gift/bill/inventoryOccupancy',data,res=>{
        if(res.body.result){
          this.$message.success('操作成功')
          this.init()
        }else{
          this.$message.error(res.body.msg)
        }
      }, err => {
        this.$message.error(err);
      })
    },
    // 刷新可用库存
    updateStore(list,neeTip){
      return new Promise((resolve,reject) =>{
        let params = list?.map(e=>{
          return {
            material_code: e.material_code,
            supply_code:e.supplier_code,
            source_of_supply:e.supply_source,
          }
        })

          this.ajax.postStream(
            "/afterSale-web/api/gift/config/getQuantity",
            params,
            (res) => {
              if (res.body.result) {
                if (neeTip) {
                  this.$message.success(res.body.msg);
                }
                const data = res.body.content;
                const dataList = JSON.parse(JSON.stringify(this.dataSource.billGiftGoods||[]))
                data?.forEach(e=>{
                  dataList.forEach(item =>{
                    if(item.material_code===e.material_code){
                      item.quantity = e.quantity
                      item.inventorySurplus = e.inventorySurplus
                    }
                  })
                })
                this.dataSource.billGiftGoods = dataList
              }
            }
          )
      })
    },
    // 取消代发采购单
    onCancelScm(){
      const value = this.getParams('df_ods_task_number')
      if(!value){
        return this.$message.error('请选择商品')
      }
      let list = this.$refs.afterSalesTable?.getSelection?.()

      for(let item of list){
        if(item.third_party_fulfilled==='N'){
          return this.$message.error(`非代发不可操作`)
        }
        if(!item.df_ods_task_number){
          return this.$message.error(`请勾选已下采购单的行进行取消`)
        }

      }

      let _this = this
      const data = {
        alertId: new Date().getTime(),
        callBack:(val)=>{

          return new Promise((resolve)=>{
            const params = {
              dfOdsTaskNumberList:value?.goods?.filter(e=>e),
              cancelReason:val,
            }
            _this.ajax.postStream('/afterSale-web/api/gift/bill/cancelScm',params,res=>{
              if(res.body.result){
                _this.$message.success('操作成功')
                this.init()
                resolve()
              }else{
                _this.$message.error(res.body.msg)
                resolve()
              }
            })
          })
        },
        tipContent:'取消代发采购单后如果没有操作【释放库存】，库存保留超48小时后会自动释放库存；如果客户需要晚点发货，可以再操作【下推代发采购单】',
        title:'取消代发采购单'
      }

      this.$root.eventHandle.$emit('alert', {
        params:data,
        component: () => import('@/components/after-sales-gift/components/after-sales-detail/cancel-modal.vue'),
        style: 'width:500px;height:200px',
        title: '取消代发采购单'
      })
    },
    // 下推代发采购单
    onSendScm(){
      const data = this.getParams()
      if(!data){
        return this.$message.error('请选择商品')
      }
      this.ajax.postStream('/afterSale-web/api/gift/bill/sendScm',data,res=>{
        if(res.body.result){
          this.$message.success(res.body.msg)
          this.init()
        }else{
          this.$message.error(res.body.msg)
        }
      })
    },
  },
  created() {
  },
  mounted() {
    this.init()
  }
}
</script>
