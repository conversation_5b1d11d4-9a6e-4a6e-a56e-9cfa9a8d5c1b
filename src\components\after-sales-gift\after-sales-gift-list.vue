<!--
* @description:
* @author: bin
* @date: 2025/3/9
-->
<template>
  <div>
    <custom-list-dynamic
      :data='dataSource'
      :btns='btns'
      :colData='cols'
      :showCount='showCount'
      :pageTotal='pageTotal'
      :searchPage='search.page_name'
      selection='checkbox'
      @search-click='searchData'
      @count-off="getTotalCount"
      @current-page-change='pageSizeChange'
      @page-size-change="pageChange"
      ref="selectSupplierGood"
    >
      <template #bill_gift_no="{row}">
        <div>

          <a href='javascript:;' @click="jumpRoute(row)">
            {{row.bill_gift_no}}

          </a>
          <i style="margin-left: 2px;cursor: pointer;color: #3c97f6" title="复制" class="el-icon-document" @click="copyText(row.bill_gift_no)"></i>
        </div>
      </template>
      <template #line_status="{row}">
        <span :style="{color: row.line_status === 'WAIT' || row.line_status === 'SENDFAIL' ? 'red' : ''}">{{LINE_STATUS[row.line_status]}} </span>
      </template>
      <template #if_lock="{row}">
        <span :style="{color: (row.line_status === 'WAIT' || row.line_status === 'SENDFAIL')&&row.if_lock === 'Y'  ? 'red' : ''}">{{{Y:'是',N:'否'}[row.if_lock]}} </span>
      </template>
    </custom-list-dynamic>
  </div>
</template>
<script>
import {GOODS_RECEIPT, LINE_STATUS,SHIPPING_STATUS} from "./enum";
import CustomListDynamic from '@components/common/list-dynamic-other-new/list-dynamic'
export default {
  name: 'after-sales-gift-list',
  components: {
    CustomListDynamic
  },
  data() {
    let _this = this

    return {
      LINE_STATUS,
      showCount:false,
      cols: [
        {
          label: '单据编号',
          prop: 'bill_gift_no',
          width:150,
          slot: 'bill_gift_no',
          // redirectClick: function (row) {
          //   _this.jumpRoute(row)
          // },
          // formatterSavePropOneTime:(scope)=>{
          //   return scope.row.bill_gift_no
          // }
        }, {
          label: '单据状态',
          prop: 'order_status',
          formatter:(val)=>{
            return GOODS_RECEIPT[val]
          }
        }, {
          label: '发货状态',
          prop: 'shipment_status',
          formatter:(val)=>{
            return SHIPPING_STATUS[val]
          }
        }, {
          label: '买家呢称',
          prop: 'buyer_name'
        }, {
          label: '收货人',
          prop: 'receiver_name'
        }, {
          label: '物料编码',
          prop: 'material_code'
        }, {
          label: '物料名称',
          prop: 'material_name'
        }, {
          label: '规格描述',
          prop: 'specification_description'
        }, {
          label: '赠品数量',
          prop: 'gift_quantity'
        }, {
          label: '单位',
          prop: 'unit'
        },
        {
          label: '行状态',
          prop: 'line_status',
          // formatter:(val)=>{
          //   return LINE_STATUS[val]
          // }
          slot:'line_status'
        }, {
          label: '行发货状态',
          prop: 'shipping_status',
          formatter:(val)=>{
            return SHIPPING_STATUS[val]
          }
        }, {
          label: '售后单号',
          prop: 'after_order_no'
        }, {
          label: '是否代发',
          prop: 'third_party_fulfilled',
          format: 'yesOrNo'
        }, {
          label: '采购单号',
          prop: 'purchase_order_number'
        },
        {
          label: '出库单号',
          prop: 'shipment_number'
        }, {
          label: '运输单号',
          prop: 'shipment_tracking_number'
        }, {
          label: '创建时间',
          prop: 'create_time',
          format:"dataFormat1"
        }, {
          label: '创建人',
          prop: 'creator_name'
        }, {
          label: '创建人分组',
          prop: 'creator_group'
        },
        {
          label: '业务员',
          prop: 'staff_name'
        }, {
          label: '业务分组',
          prop: 'staff_gruop'
        }, {
          label: '是否经销商订单',
          prop: 'if_dealer',
          format: 'yesOrNo',
          width: 150
        },
        {
          label: '是否锁库',
          prop: 'if_lock',
          slot: 'if_lock',
          // format: 'yesOrNo',
        },
      ],
      btns: [{
        type: 'success',
        txt: '刷新',
        click: () => {
          _this.onSearch();
        },
        loading: false
      },
        {
        type: 'primary',
        txt: '导出',
        click: this.onExport
      },
      ],
      search: {
        page_size: this.pageSize,
        page_name: 'aftersale_bill_gift',
        page_no: 1,
        where:[],
      },

      dataSource:[],
      selectCode:'',
      pageTotal:0,
    }
  },
  methods: {
    radioChange(obj) {
      this.selectCode = obj
    },
    // 跳转
    jumpRoute(row){
      this.$root.eventHandle.$emit('creatTab', {
        name: "售后赠品详情",
        params: row,
        component: () => import('@components/after-sales-gift/after-sales-gift-detail.vue')
      });
    },
    // 复制
    copyText(val){
      // if( navigator.clipboard.writeText(copy_text).then(() => {
      //   alert("复制成功！")
      // }).catch((err)=>{
      //   alert("复制失败！")
      // }))
      if(navigator.clipboard?.writeText){
        navigator.clipboard.writeText(val).then(()=>{
          this.$message({
            message: '复制成功',
            type: 'success'
          })
        }).catch((err)=>{
          this.$message({
            message: '复制失败！',
            type: 'error'
          })
        })
        return
      }
      let input = document.createElement('input');
      document.body.appendChild(input)
      input.value = val
      input.select();
      input.setSelectionRange(0, val.length); /* 为移动设备设置 */
      document.execCommand('Copy')
      this.$message({
        message: '复制成功',
        type: 'success'
      })
      document.body.removeChild(input);
    },
    rowDblclick(obj) {
      this.selectCode = obj
      this.close()
    },
    searchData(obj, resolve){
      this.search.where = obj
      this.showCount =false
      this.search.page_no = 1
      this.getDataList(resolve);
    },
    getDataList(resolve){
      let self = this
      let data = {
        page_no: this.search.page_no,
        page_size: this.search.page_size,
        page_name: this.search.page_name,
        where: this.search.where,
      }

      this.ajax.postStream('/afterSale-web/api/gift/bill/list',data,d=> {
        if (d.body.result && d.body.content) {
              // self.pageTotal = d.body.content.count;
              let dataList = JSON.parse(JSON.stringify(d.body.content.list||[]));
              if(dataList.length == (this.search.page_size+1)&&dataList.length>0){
                dataList.pop();
              }
              let totalCount = this.search.page_size * this.search.page_no;
              if(!this.showCount){
                this.pageTotal =d.body.content.count == (this.search.page_size+1)? totalCount+1:totalCount
              }
              self.dataSource = d.body.content.list || [];
              resolve?.()
            }else{
          resolve?.()
        }
      },err=>{
        this.$message.error(err);
        resolve?.()
      })

    },
    getTotalCount(){
      if(!this.dataSource.length){
        this.$message.error("当前列表为空，先搜索内容");
        return;
      }
      if(!!this.countOffFlag){
        this.$message.error("请勿重复点击");
        return;
      }
      this.countOffFlag = true
      this.ajax.postStream('/afterSale-web/api/gift/bill/count',{
        page_no: this.search.page_no,
        page_size: this.search.page_size,
        page_name: this.search.page_name,
        where: this.search.where,
      },d=>{
        if(d.body.result&&d.body.content){
          // self.pageTotal = d.body.content.count;
          this.pageTotal = d.body.content.count;
          this.showCount = true;
          this.countOffFlag = false;
        }
      })
    },
    pageSizeChange(pageSize){
      this.search.page_no = pageSize
      this.getDataList();
    },
    pageChange(page){
      this.search.page_size = page
      this.search.page_no = 1
      this.getDataList();
    },
    /**
     *  导出
     */
    onExport(){
      if(this.search.where.length < 1){
        this.$message.error('导出功能至少要有一个查询条件')
        return
      }
      const params  = {
        ...this.search,
      }

      this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportAftersaleGiftBill', Object.assign({}, params, {
        page_size: this.pageTotal,     //页数
        page_no:1   //页码
      }), d => {
        this.$message({
          type: d.body.result ? 'success' : 'error',
          message: d.body.msg,
        })
      },err=>{
        this.$message.error(err)
      })
    },
    //
    goodsChoose(row){
      new Promise((resolve) => {
        setTimeout(resolve, 10)
      }).then(() => {
        let params = {},
          self = this;
        // 选择商品
        params.callback = d => {
          if(d) {
            // console.log()
            self.checkMaterialNumber(d,data =>{
              // console.log
              console.log('self.goodsSelect', row)
              row.material_no = d.material_no
              row.material_id = d.material_id
              row.material_name = d.material_name
              row.material_unit = d.material_unit
              row.material_spec = d.material_spec
              row.material_id = d.material_id
              // self.getcusMateriaCode(d.materialNumber)
              if (d.groupNumber === 'XSP'){
                row.stock_name = self.goodXspStore.name
                row.stock_number = self.goodXspStore.number
                row.stock_id = self.goodXspStore.number
              }
            });
          }
        }
        self.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('./components/after-sales-gift-config-list/add-after-sales-gift-modal.vue'),
          style: 'width:800px;height:500px',
          title: '商品列表'
        });
      })
    },
    onSearch(){
      this.getDataList()
    }
  },
  created() {
  },
  mounted() {
  }
}
</script>
<style lang="stylus" scoped>
.cursor-pointer{
  cursor: pointer;
  color: #409EFF;
}
</style>
