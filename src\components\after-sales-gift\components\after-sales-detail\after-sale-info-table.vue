<!--
* @description: 赠品商品信息
* @author: bin
* @date: 2025/3/12
-->
<template>
  <xpt-list
    :pageTotal="dataSource.length||0"
    :data="dataSource"
    :colData="columns"
    :showHead="false"
    :orderNo="true"
    ref="list"
    selection="checkbox"
    @selection-change="selectChange"
    @current-page-change="getDataSource"
  >
    <template #line_status="{row}">
      <span :style="{color: row.line_status === 'WAIT' || row.line_status === 'SENDFAIL' ? 'red' : ''}">{{LINE_STATUS[row.line_status]}} </span>
    </template>
    <template #if_lock="{row}">
      <span :style="{color:( row.line_status === 'WAIT' || row.line_status === 'SENDFAIL') && row.if_lock === 'Y' ? 'red' : ''}">{{{Y:'是',N:'否'}[row.if_lock]}}</span>
    </template>
  </xpt-list>
</template>
<script>
import {LINE_STATUS, SHIPPING_STATUS, SHIPPING_TYPE,} from "../../enum";

export default {
  name: 'after-sale-info-table',
  props:{
    dataSource:{
      type: Array,
      default() {
        return [];
      },
    }
  },
  data() {
    return {
      LINE_STATUS,
      columns:[
        {
          label: '物料编码',
          prop: 'material_code',
          width: 150,
        },
        {
          label: '物料名称',
          prop: 'material_name',
          width: 150,
        },
        {
          label: '规格描述',
          prop: 'specification_description',
          width: 200,
        },
        {
          label: '赠品数量',
          prop: 'gift_quantity',
        },
        {
          label: '单位',
          prop: 'unit',
        },
        {
          label: '是否锁库',
          prop: 'if_lock',
          slot:'if_lock'
        },
        {
          label: '工厂代发',
          prop: 'third_party_fulfilled',
          format: 'yesOrNo'
        },
        {
          label: '行状态',
          prop: 'line_status',
          slot:'line_status'
          // formatter:(val)=>{
          //   return LINE_STATUS[val]
          // }
        },
        {
          label: '行发货状态',
          prop: 'shipping_status',
          formatter:(val)=>{
            return SHIPPING_STATUS[val]
          }
        },
        {
          label: '是否打印锁定',
          prop: 'print_status_locked',
          format: 'yesOrNo',
          width: 150
        },
        {
          label: '采购员',
          prop: 'purchaser',
        },
        {
          label: 'ODS履约订单号',
          prop: 'buying_leads_number',
          width: 200
        },
        {
          label: 'ODS发货任务号',
          prop: 'df_ods_task_number',
          width: 200
        },
        {
          label: '采购单号',
          prop: 'purchase_order_number',
        },
        {
          label: '供应商',
          prop: 'supplier_name',
        },
        {
          label: '采购日期',
          prop: 'purchase_date',
          format:"dataFormat3",
          width: 150
        },

        {
          label: '计划交货日期',
          prop: 'planned_delivery_date',
          format:"dataFormat3",
          width: 150
        },
        {
          label: '发货单号',
          prop: 'shipment_number',
        },

        {
          label: '运单号',
          prop: 'shipment_tracking_number',
        },
        {
          label: '发货物流',
          prop: 'shipping_logistics',
        },
        {
          label: '销售出库单号',
          prop: 'sale_out_stock_bill_no',
          width: 150,
        },
        {
          label: '销售出库单审核时间',
          prop: 'sale_out_stock_audit_time',
          format:"dataFormat1",
          width: 180,
        },
        {
          label: '销售出库单已审核',
          prop: 'if_sale_out_stock_audit',
          format: 'yesOrNo',
          width: 150,
        },
        {
          label: '采购成本',
          prop: 'purchase_cost',
        },

        {
          label: '可用库存',
          prop: 'quantity',
        },
        {
          label: '冗余库存数',
          prop: 'inventorySurplus',
        },

        // 货运方式
        {
          label: '货运方式',
          prop: 'shipping_method',
          formatter:(val)=>{
            return SHIPPING_TYPE[val]
          }
        },
        // 入库时间
        {
          label: '入库时间',
          prop: 'inbound_time',
          format:"dataFormat1",
        },
        // 发货时间
        {
          label: '发货时间',
          prop: 'delivery_time',
          format:"dataFormat1",
        },
        {
          label: '取消代发采购单原因',
          prop: 'cancel_scm_reason',
          width: 150
        },
        {
          label: '取消仓库发货单原因',
          prop: 'cancel_delivery_reason',
          width: 150
        },
        {
          label: '来源方案',
          prop: 'after_plan_no',
        },
        {
          label: '问题商品编码',
          prop: 'question_goods_code',
          width: 150
        },
        {
          label: '问题商品名称',
          prop: 'question_goods_name',
          width: 150
        },
      ],
      // dataSource:[],
      total:0
    }
  },

  methods: {
    // 选择改变
    selectChange(e,list){
      console.log(e,list)
    },
    getSelection(){
      return this.$refs.list.selectRows;
    },

    // 获取列表数据
    getDataSource(resolve){
      console.log(resolve,'555')
      // this.dataSource = [{},{}];
    },
  },
  created() {
  },
  mounted() {
    this.getDataSource()
  }
}
</script>
