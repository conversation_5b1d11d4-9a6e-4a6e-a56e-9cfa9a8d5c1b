<!--
* @description:
* @author: bin
* @date: 2025/3/12
-->
<template>
  <el-form label-position="right" >
    <el-col :span="8">
      <el-form-item label="单据编号：">{{dataSource.bill_gift_no}}</el-form-item>
      <el-form-item label="合并单号：">
        <span class="primary-color" @click="routerJumpToVal">
          {{dataSource.merge_trade_no}}
        </span>
      </el-form-item>
      <el-form-item label="单据状态："><span :style="{color:dataSource&&dataSource.order_status==='WAIT'? 'red':''}">{{GOODS_RECEIPT[dataSource.order_status]}}</span></el-form-item>
      <el-form-item label="是否经销商订单：">{{IsYesNo[dataSource.if_dealer]}}</el-form-item>

      <el-form-item label="备注：">
        <div class="text-overflow-content-base-info">
        <el-tooltip placement="top-start" effect="dark" :content="dataSource.remark" >
          <span style="color: #000">{{dataSource.remark}}</span>
        </el-tooltip>
        </div>
      </el-form-item>
    </el-col>
    <el-col :span="8">
      <el-form-item label="售后单：" @click.native="routerJump"> <span class="primary-color">{{dataSource.after_order_no}}</span></el-form-item>
      <el-form-item label="收货人名称：">{{dataSource.receiver_name}}</el-form-item>
      <el-form-item label="收货人电话：">
        <xpt-eye-switch
          v-model="dataSource.reveiver_phone"
          :readonly="true"
          :isCallback="true"
          :aboutNumber="dataSource.bill_gift_no"
          @onOpenHideData="item=>{decryptionByTaoBao(item,dataSource);}"
        ></xpt-eye-switch>
      </el-form-item>
      <el-form-item label="买家昵称：">{{dataSource.buyer_name}}</el-form-item>

      <el-form-item label="取消赠品单原因：">
        <div class="text-overflow-content-base-info">
        <el-tooltip placement="top-start" effect="dark" :content="dataSource.cancel_reason" >
          <span style="color: #000">{{dataSource.cancel_reason}}</span>
        </el-tooltip>
        </div>
      </el-form-item>
    </el-col>

    <el-col :span="8">
<!--      <el-form-item label="发货状态：">{{SHIPPING_STATUS[dataSource.shipment_status]}}</el-form-item>-->

      <el-form-item label="省：">{{dataSource.priName}}</el-form-item>
      <el-form-item label="市：">{{dataSource.cityName}}</el-form-item>
      <el-form-item label="区：">{{dataSource.areaName}}</el-form-item>
      <el-form-item label="街道 ：">{{dataSource.streetName}}</el-form-item>
      <el-form-item label="收货人地址：">{{dataSource.receiver_addr}}</el-form-item>
    </el-col>
  </el-form>
</template>
<script>
import {GOODS_RECEIPT, SHIPPING_STATUS} from "../../enum";

export default {
  name: 'base-info',
  props:{
    dataSource:{
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      GOODS_RECEIPT,
      SHIPPING_STATUS,
      IsYesNo:{
        Y:'是',
        N:'否'
      }
    }
  },
  methods: {
    //
    routerJump(){
      this.$root.eventHandle.$emit('creatTab', {
        name:"售后单详情",
        params:{
          id: this.dataSource.after_order_id,
          idList: [],//用于售后单详情的上一页/下一页
        },
        component: () => import('@components/after_sales/index')
      });
    },
    //
    routerJumpToVal(){
      this.$root.eventHandle.$emit('creatTab', {
        name:"合并订单详情",
        params:{
          merge_trade_id: this.dataSource.merge_trade_id,

        },
        component: () => import('@components/order/merge.vue')
      });
    },
    // 淘宝
    decryptionByTaoBao(params,row){
      let self = this
      if (!params.isOpen) {
        params.callback(true);
      }
      new Promise((resolve,reject)=>{
        this.ifDecryption(resolve,row, reject);
      }).then(result=>{
        row.decryptionByInterface = result.content
        if(!result.content){
          //不需要调接口解密，直接原字段展示，无需前端加密
          row.ifFEDecryption = false;
          self.decryption(params,row)
          return;
        }
        let data ={
          "addressId": this.dataSource.encryption_add_id,
          "mergeTradeNo": this.dataSource.merge_trade_id
        }
        this.ajax.postStream("/kingdee-web/api/end/decryption", data	, res => {
          let obj = res.body;
          // console.log(obj)
          if(obj.result){
            //已经调接口解密，解密字段展示，无需前端加密
            row.ifFEDecryption = false;
            row.reveiver_phone = obj.content.receiverPhone;
            params.callback(true);
          }else{
            params.callback(false,obj.msg);
          }
        });
      }).catch(obj=> {
        params.callback(true, obj.msg);
      })
    },
    ifDecryption(callback,row,reject){
      let self =this;
      let data ={
        "addressId": row.encryption_add_id,
        "mergeTradeId": row.merge_trade_id,
        "currentUserId": self.getEmployeeInfo('id')
      }
      self.ajax.postStream("/kingdee-web/api/end/ifDecryption", data	, res => {
        var obj = res.body;
        self.canIuseEye = obj.result
        if(obj.result){
          callback && callback(obj);
        }else{
          reject&&reject(obj)
        }
      });
    },
  },
  created() {
  },
  mounted() {
  }
}
</script>
<style  scoped>
.primary-color{
 cursor: pointer;
  color: #409EFF;
}
.text-overflow-content-base-info{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  color: #000;
}
.mt-base-info{
  margin-top: 8px;
}
</style>
