<!--
* @description: 取消方式
* @author: bin
* @date: 2025/3/12
-->
<template>
  <div class="form mt-8">
<!--    <div class="mb-8">{{params.title}}</div>-->
    <div class="mb-8 mt-8 " style="color: #b30606">{{params.tipContent}}</div>
    <el-input
      type="textarea"
      :rows="2"
      placeholder="取消原因，必填，200字内"
      :maxlength="200"
      v-model="textarea">
    </el-input>
    <div class="mb-8 mt-8">
      <el-button size="small" type="primary" @click="onSure"  :loading="loadingBtn">确定</el-button>
      <el-button  size="small" @click="onCancel">取消</el-button>
    </div>


  </div>
</template>
<script>
export default {
  name: 'cancel-modal',
  props:['params'],
  data() {
    return {
      title: '',
      textarea:'',
      loadingBtn:false
    }
  },
  methods: {
    onSure(){
      if(!this.textarea||!this.textarea.trim()){
        this.$message.error('请输入取消原因');
        return;
      }
      if(this.params.callBack){
        this.loadingBtn = true
        this.params.callBack(this.textarea).then(()=>{
          this.onCancel();
        }).finally(()=>{
          this.loadingBtn = false
        })
      }

    },
    // 取消
    onCancel(){
      this.textarea = '';
      this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
    }
  },
  created() {
  },
  mounted() {
  }
}
</script>
<style scoped>
.mb-8{
  margin:  8px 0;
}
.mt-8{
  margin-top: 8px;
}
</style>
