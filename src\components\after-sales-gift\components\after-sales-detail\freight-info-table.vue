<!--
* @description: 操作记录
* @author: bin
* @date: 2025/3/12
-->
<template>
  <xpt-list
    :pageTotal="operateTotal"
    :data='operateList'
    :colData='operateCol'
    :showHead="false"
    :orderNo="true"
    selection=''
  ></xpt-list>
</template>
<script>
export default {
  name: 'log-info-table',
  props:['id'],
  data() {
    return {
      operateTotal:0,
      operateList:[],
      operateCol: [
        {
          label: '外部单号',
          prop: 'out_sys_bill_no',
        },
        // {
        //   label: '业务操作',
        //   prop: 'aftersale_bill_no'
        // },
        {
          label: '运费',
          prop: 'transportation_cost',
        },
      ],
    }
  },
  methods: {
    getOperateList() {
      this.ajax.postStream('/afterSale-web/api/gift/bill/getBillOutboudInfo', this.id, res => {
        if (res.body.result) {
          this.operateList = res.body.content || []
        }
      })
    },
  },
  created() {
  },
  mounted() {
  }
}
</script>
