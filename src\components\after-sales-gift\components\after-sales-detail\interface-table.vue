<!--
* @description: 接口信息
* @author: bin
* @date: 2025/3/12
-->
<template>
  <xpt-list
    :data="operateList"
    :colData="apiCols"
    :showHead="false"
    :orderNo="true"
    selection=""
    @current-page-change="getOperateLog"
  ></xpt-list>
</template>
<script>
import {ISSUE_STATUS, SUPPLIER_TYPE} from "../../enum";

export default {
  name: 'interface-table',
  props:['id'],
  data() {
    return {
      operateList:[],
      apiCols: [
        {
          label: '方式',
          prop: 'operation_type',
          formatter:(e)=>{
            return SUPPLIER_TYPE[e]
          }
        },
        {
          label: '接口状态',
          prop: 'status',
          formatter:(e)=>{
            return ISSUE_STATUS[e]
          }
        },
        {
          label: '单号',
          prop: 'out_sys_bill_no',
        },
        {
          label: '接口提示信息',
          prop:'interface_info',
        },
        {
          label: '生成时间',
          prop: 'create_time',
          format: "dataFormat1",
        },
        {
          label: '传送参数',
          prop:'parameter_info',

        }
      ]
    }
  },
  methods: {
    getOperateLog() {
      this.ajax.postStream('/afterSale-web/api/gift/bill/getBillExternalInfo', this.id, res => {
        if (!res.body.result) {
          this.$message.error(res.body.msg);
          return;
        }
          this.operateList = res.body.content
      }, err => {
        this.$message.error(err);
      });
    },
  },
  created() {
  },
  mounted() {
  }
}
</script>
