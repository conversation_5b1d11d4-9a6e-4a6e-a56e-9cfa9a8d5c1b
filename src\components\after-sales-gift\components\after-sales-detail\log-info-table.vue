<!--
* @description: 操作记录
* @author: bin
* @date: 2025/3/12
-->
<template>
  <xpt-list
    :pageTotal="operateTotal"
    :data='operateList'
    :colData='operateCol'
    :showHead="false"
    :orderNo="true"
    selection=''
  ></xpt-list>
</template>
<script>
export default {
  name: 'log-info-table',
  props:['id'],
  data() {
    return {
      operateTotal:0,
      operateList:[],
      operateCol: [
        {
          label: '用户',
          prop: 'operator_name',
        },
        {
          label: '业务操作',
          prop: 'operate_type'
        },
        {
          label: '操作描述',
          prop: 'description',
        },
        {
          label: '操作时间',
          prop: 'operate_time',
          format: 'dataFormat1',
        }
      ],
    }
  },
  methods: {
    getOperateList() {
        this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.id}, res => {
          if (res.body.result) {
            this.operateList = res.body.content || []
            this.operateTotal = res.body.content?.length || 0
          }
        })
    },
  },
  created() {
  },
  mounted() {
  }
}
</script>
