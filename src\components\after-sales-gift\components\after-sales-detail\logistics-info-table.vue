<!--
* @description: 物流信息
* @author: bin
* @date: 2025/3/12
-->
<template>
  <xpt-list
    ref="logisticsListRef"
    :data="logisticsList"
    :colData="logisticsCol"
    :showHead="false"
    :orderNo="true"
    selection=''
    @current-page-change="selectChange"
  >
  </xpt-list>
</template>
<script>
export default {
  name: 'logistics-info-table',
  data() {
    return {
      logisticsList:[],
      logisticsCol:[
        {
          label: '来源单据号',
          prop: 'operator_name',
        },
        {
          label: '运输单号',
          prop: 'operation_name'
        },
        {
          label: '发货物流',
          prop: 'operation_desc',
        },
        {
          label: '发运方式',
          prop: 'operation_time',
          format: 'dataFormat1',
          width: 150,
        },
        {
          label: '是否与供应商结算',
          prop: 'operation_name'
        },
        {
          label: '发货时间',
          prop: 'operation_desc',
        },
        {
          label: '结算方式',
          prop: 'operation_time',
          format: 'dataFormat1',
          width: 150,
        },
        {
          label: '运输成本',
          prop: 'operation_time',
          format: 'dataFormat1',
          width: 150,
        }
      ],

    }
  },
  methods: {
    //
    selectChange(){
      const data = {"page":{"length":50,"pageNo":pageNo},"source_id": this.id};
      this.ajax.postStream('/order-web/api/receipt/v2/logList', data, res => {
        if (!res.body.result || !res.body.content) {
          this.$message.error(res.body.msg);
          return;
        }
        if (Array.isArray(res.body.content.list)) {
          this.operateList = res.body.content.list;
          this.operateTotal = res.body.content.count;
        } else {
          this.operateList = [];
        }
      }, err => {
        this.$message.error(err);
      });
    },
  },
  created() {
  },
  mounted() {
  }
}
</script>
