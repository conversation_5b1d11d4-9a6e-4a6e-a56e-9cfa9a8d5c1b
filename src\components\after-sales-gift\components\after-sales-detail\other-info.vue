<!--
* @description: 其他信息
* @author: bin
* @date: 2025/3/12
-->
<template>
  <el-form label-position="right" >
    <el-col :span="6">
      <el-form-item label="创建人：">{{dataSource.creator_name}}</el-form-item>
      <el-form-item label="业务员：">{{dataSource.staff_name}}</el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="创建时间：">{{dataSource.create_time&&moment(dataSource.create_time).format('YYYY-MM-DD HH:mm:ss')}}</el-form-item>
      <el-form-item label="业务员分组：">{{dataSource.staff_gruop}}</el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="创建人分组：">{{dataSource.creator_group}}</el-form-item>
      <el-form-item label="取消时间：">{{dataSource.cancel_time&&moment(dataSource.cancel_time).format('YYYY-MM-DD HH:mm:ss')}}</el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="创建人大分组：">{{dataSource.creator_big_group}}</el-form-item>
    </el-col>
  </el-form>
</template>
<script>
import moment from 'moment'
export default {
  name: 'other-info',
  props:{
    dataSource:{
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data(){
    return {
      moment
    }
  },
}
</script>
