<!-- 批准供应商列表选择物料编码 -->
<template>
	<xpt-list
		:data='goodsList'
		:btns='btns'
		:colData='cols'
		:pageTotal='pageTotal'
		:searchPage='page_name'
		selection='radio'
		@row-dblclick='rowDblclick'
		@radio-change='radioChange'
		@search-click='searchData'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
    ref="selectSupplierGood"
	></xpt-list>
</template>
<script>
export default {
  name:"add-after-sales-gift-modal",
	data(){
		let self = this
		return {
			cols: [
				{
					label: '商品编码',
					prop: 'material_number'
				}, {
					label: '商品名称',
					prop: 'material_name'
				}, {
					label: '商品规格',
					prop: 'material_specification'
				},
        // {
				// 	label: '物料分组',
				// 	prop: 'material_group'
				// }, {
				// 	label: '单位',
				// 	prop: 'material_unit'
				// }
			],
			btns: [{
				type: 'primary',
				txt: '确认',
				click: self.close
			}],
			search: {
				length: self.pageSize,
				pageNo: 1,
			},
			goodsList:[],
			selectCode:'',
			pageTotal:0,
			page_name: 'cloud_express_list_status',
			where: []
		}
	},
	props:['params'],
	methods:{
		close(){
			if(!this.selectCode) {
				this.$message.error('请先选择商品');
				return;
			}
			this.params.callback(this.selectCode)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		searchData(obj, resolve){
			this.where = obj
			this._getGoodsList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.length = pageSize
			this._getGoodsList();
		},
		pageChange(page){
			this.search.pageNo = page
			this._getGoodsList();
		},
		_getGoodsList(resolve){
			let data = {
				page_no: this.search.pageNo,
				page_size: this.search.length,
				page_name: this.page_name,
				where: this.where,
      }
			this.ajax.postStream('/order-web/api/expressList/listStatus',data,d=>{
        if (d.body.result && d.body.content) {
          this.goodsList = d.body.content.list
          this.pageTotal = d.body.content.count
        }
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve();
			})
		},
		radioChange(obj) {
			this.selectCode = obj
		},
		rowDblclick(obj) {
			this.selectCode = obj
			this.close()
		}
    },
  mounted(){
        this.$refs.selectSupplierGood.$refs.xptSearchEx.filterFields = fields => fields.filter(o => !/(供应来源)/.test(o.comment))
        this._getGoodsList()
    }
}
</script>
