<!-- 批准供应商列表选择物料编码 -->
<template>
	<xpt-list
		:data='goodsList'
		:btns='btns'
		:colData='stockCols'
		:pageTotal='pageTotal'
    :showHead='false'
		:searchPage='page_name'
		selection=''
		@row-dblclick='rowDblclick'
		@radio-change='radioChange'
		@search-click='searchData'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
    ref="selectSupplierGood"
	></xpt-list>
</template>
<script>
export default {
  name:"check-after-sales-gift-modal",
	data(){
		let self = this
		return {
      stockCols: [
        {
          label: '物料编码',
          prop: 'materialNumber',
          width: 120
        }, {
          label: '物料名称',
          prop: 'materialName',
          width: 100
        }, {
          label: '物料规格',
          prop: 'spec',
          width: 180
        }, {
          label: '仓库名称',
          prop: 'stockName'
        }, {
          label: '可用库存',
          prop: 'availableStockQty'
        }, {
          label: '仓库分组',
          prop: 'stocGroupCode'
        },{
          label: '库存冗余数',
          prop: 'inventorySurplus'
        }, {
          label: '货期',
          prop: 'deliveryTime',
          formatter (val) {
            return val ? val : '暂无货期信息'
          }
        },
      ],
			btns: [{
				type: 'primary',
				txt: '确认',
				click: self.close
			}],
			search: {
				length: self.pageSize,
				pageNo: 1,
			},
			goodsList:[],
			selectCode:'',
			pageTotal:0,
			page_name: 'cloud_express_list',
			where: []
		}
	},
	props:['params'],
	methods:{
		close(){
			if(!this.selectCode) {
				this.$message.error('请先选择商品');
				return;
			}
			this.params.callback(this.selectCode)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		searchData(resolve){

			this._getGoodsList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.length = pageSize
			this._getGoodsList();
		},
		pageChange(page){
			this.search.pageNo = page
			this._getGoodsList();
		},
		_getGoodsList(resolve){
     const  data = {
        "page_name":"lskf_goods_query",
        "where":[{
          "condition":"OR",
          "field":"aadc25491dc04c3317c86f7084eb8b5b",
          "operator":"=",
          "table":"5d05ebd2b4644a2fbb9a8d41b95722bf",
          "value":this.params.value
        }]
      }
			this.ajax.postStream('/external-web/api/lskfWorkbench/getGoodsStocks',data,d=>{
        if (d.body.result && d.body.content) {
          this.goodsList = d.body.content || []
          this.goodsCount = d.body.content.count
        }
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve();
			})
		},
		radioChange(obj) {
			this.selectCode = obj
		},
		rowDblclick(obj) {
			this.selectCode = obj
			this.close()
		}
    },
  mounted(){
    console.log('你啊说的话代打 阿杜',this.params)
        // this.$refs.selectSupplierGood.$refs.xptSearchEx.filterFields = fields => fields.filter(o => !/(供应来源)/.test(o.comment))
        this._getGoodsList()
    }
}
</script>
