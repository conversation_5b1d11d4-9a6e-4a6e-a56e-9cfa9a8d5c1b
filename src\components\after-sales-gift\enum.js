export const GOODS_RECEIPT = {
  WAIT: "待处理",
  ING: "处理中",
  PARTIALPROCESS: "部分处理",
  PROCESSED: "已处理",
  CANCEL: "已取消",
};
// 行状态
export const LINE_STATUS = {
  WAIT: '待处理 ',
  ING: "处理中",
  CANCEL: "已取消",
  SENDFAIL:'发送失败',
  CLOSE: "关闭",
  OVER: "已处理",
};
// 发货状态：
export const SHIPPING_STATUS = {
  NOTSHIPPING: "未发货",
  SHIPPINGED: "已发货",
};
// 供应商来源

export const SUPPLIER_SOURCE = {
  SUPPLIER: "供应商",
  INNER: "内部",
};
//货运方式 SUPPLIER：供应商直发
//
//
// EXPRESS：快递
export const SHIPPING_TYPE = {
  SUPPLIER: "供应商直发",
  EXPRESS: "快递",
};
// 方式：  GENERATPURCHASE("生成SCM采购单"),
//
//   GENERATSHIPPINGNOTICE("生成4PL发货通知单"),
//
//   CANCELPURCHASE("取消SCM采购单"),
//
//   CANCELSHIPPINGNOTICE("取消4PL发货通知单"),
//
//   GENERATK3SaleOutStock("生成K3销售出库单"),
//
//   CANCELK3SaleOutStock("取消K3销售出库单"),
//
//   GENERATANALYSISSUBORDER("生成责任分析单"),
//
//   CANCELANALYSISSUBORDER("取消责任分析单");
export const SUPPLIER_TYPE = {
  GENERATPURCHASE: "生成SCM采购单",
  GENERATSHIPPINGNOTICE: "生成4PL发货通知单",
  CANCELPURCHASE: "取消SCM采购单",
  CANCELSHIPPINGNOTICE: "取消4PL发货通知单",
  GENERATK3SaleOutStock: "生成K3销售出库单",
  CANCELK3SaleOutStock: "取消K3销售出库单",
  GENERATANALYSISSUBORDER: "生成责任分析单",
  CANCELANALYSISSUBORDER: "取消责任分析单",
  GIFTGENERATCGXQ: "生成ODS履约订单",
}
// ISSUEDING("下达中"),
//
//   ISSUED("已下达"),
//
//   EXECFAILED("失败")
export const ISSUE_STATUS = {
  ISSUEDING: "下达中",
  ISSUED: "已下达",
  EXECFAILED: "失败",
}
