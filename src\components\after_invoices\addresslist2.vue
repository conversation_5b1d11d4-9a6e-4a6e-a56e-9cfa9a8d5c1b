<!-- 客户地址列表(整合，弹窗和列表都可以用),默认弹窗 -->
<template>
	<div class='xpt-flex'>
		<xpt-headbar>
			<el-button type='primary' size='mini'  slot='left'  @click="confirmAddGoods">确认</el-button>
			<el-button type='primary' size='mini'  slot='left'  @click="copyAddress">复制新增</el-button>
		</xpt-headbar>
		<xpt-list
		:data='list'
		:colData='col'
		:pageTotal='count'
		ref="list"
		:showHead='false'
		:selection='type'
		@row-dblclick="rowDblclick"
		:searchPage='search.page_name'
		@selection-change='select'
		@radio-change='radioChange'
		@search-click='searchClick'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
	></xpt-list>
	</div>

</template>
<script>
export default {
	props: ['params'],
	data() {
		let self = this
		return {
			list: [],
			selectedData:[],//选择的数据
			deleLoding:false,
			refreshLoding:false,
			deleDisabled:false,
			refreshDisabled:false,
			type:'radio',
			/*btns:[{
	          type: 'primary',
	          txt: '确认',
	          click: ()=>{
	          	self.confirmAddGoods();
	          }
	        }],*/

			col: [
				{
					label: '地址编码',
					prop: 'number'
				}, {
					label: '客户',
					prop: 'cust_name'
				}, {
					label: '收货人',//
					prop: 'receiver_name'
				}, {
					label: '送货方式',
					format:'deliverMethod',
					prop: 'deliver_method'
				}, {
					label: '运费类型',
					format:'postFeeType',
					prop: 'post_fee_type'
				}, {
					label: '省',
					prop: 'receiver_state_name'
				}, {
					label: '市',
					prop: 'receiver_city_name'
				}, {
					label: '区',

					prop: 'receiver_district_name'
				}, {
					label: '街道',
					prop: 'receiver_street_name'
				}, {
					label: '详细地址',
					prop: 'receiver_address'
				}, {
					label: '收货人手机',//

					prop: 'receiver_mobile'
				}, {
					label: '收货人固话',//
					prop: 'receiver_phone'
				}, {
					label: '原始地址',//
					prop: 'is_original',
					format:'yesOrNo'
				}
			],


			search: {
				page: {
					pageNo: 1,
					length: self.pageSize,
					cust_id : null
					//cust_id: "9110858900980000"
				},
				page_name: 'return_list',
				where: []
			},
			count: 0
		}
	},
	methods: {
		copyAddress(){
			let params = {};
			if(this.selectedData.length>0){
				params.ifCopy=true;
				params.addressObj={
				receiver_name:this.selectedData[0].receiver_name,
				receiver_phone:this.selectedData[0].receiver_phone,
				receiver_mobile:this.selectedData[0].receiver_mobile,
				receiver_address:this.selectedData[0].receiver_address,
				receiver_state:this.selectedData[0].receiver_state,
				receiver_city:this.selectedData[0].receiver_city,
				receiver_district:this.selectedData[0].receiver_district,
				receiver_street:this.selectedData[0].receiver_street,
				fid:this.selectedData[0].fid,
          oaid:this.selectedData[0].oaid,
          expire_time:this.selectedData[0].expire_time
				}
			}else{
				params.ifCopy=false;
			}
					params.cust_id = this.params.cust_id
			params.merge_trade_no=this.params.merge_trade_no
					params.ifCallback = false;
					params.callback = (d)=>{
						console.log('d', d)
					}
					this.$root.eventHandle.$emit('alert', {
						params:params,
						component: () => import('@components/after_solutions/addAddress.vue'),
						close:function(){},
						style:'width:1100px;height:400px',
						title:'新增地址'
					});
    	},
		searchClick(where) {
			this.search.where = where
			this.getList()
		},
		pageSizeChange(ps) {
			this.search.page.length = ps
			this.getList()
		},
		pageChange(page) {
			this.search.page.pageNo = page
			this.getList()
		},
		/**
		*双击事件
		**/
		rowDblclick(data){
			console.log(data);
			if(!this.selectedData){
				this.selectedData = [];
			}
			this.selectedData.push(data);
			this.selectedData = Array.from(new Set(this.selectedData));
			this.confirmAddGoods();

		},
		ifDecryption(resolve){
			let searchParams = {
			"addressId": this.selectedData[0].fid,
			}
			// console.log(this.selectedData[0])
			this.ajax.postStream('/kingdee-web/api/end/ifDecryption',searchParams, res => {
				let data =res.body;

				resolve && resolve(data);
			})
		},
		/**
		*确认回调函数
		**/
		confirmAddGoods(){
			let self = this;
			var selectedData = this.selectedData;
			if(!selectedData || !selectedData.length){
					this.$message.error('请选择数据');
					return;
				}

			new Promise((resolve,reject)=>{
				this.ifDecryption(resolve)
			}).then(data=>{
				console.log(data);
				if(data.content && !self.params.merge_trade_no){
					self.$message.error('无合并单号不能选择密文地址');
			  		return false;
				}
				self.ajax.postStream('/kingdee-web/api/levelFourAddress/getCorrectionStatus', {addressId: self.selectedData[0].fid}, d => {
					if (d.body.result) {
						self.closeCurrentComponent();
						self.params.callback && self.params.callback({data:selectedData,ifDecryption:data});
					} else {
						self.$message.error(d.body.msg)
					}
				}, err => {
					self.$message.error(err)
				})

			})
			console.log('lajsldfkjaldfjalsdkfjslkdfjalsdfjalsdfjasldkfj');

		},

		/**
		*关闭当前弹出框组件
		*/
        closeCurrentComponent(){
            this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},

		/**
		*获取列表数据
		**/
		getList(initAfterAdd){
			console.log('获取列表数据');
			if(!this.search.page.cust_id) return;
			var searchParams = {
				cust_id : this.search.page.cust_id,
				page_no : this.search.page.pageNo,
				page_size : this.search.page.length
			}
			this.ajax.postStream('/order-web/api/customer/receiverInfo/getReceiverInfoList',searchParams, res => {
				let data =res.body;
				//需要清空已选择的数据
				this.selectedData = [];
				if(!data.result){
					this.$message.error(data.msg);
				}
				/*this.$message({
					type:data.result?'success':'error',
					message:data.msg
				});*/
				if(data.result && data.content) {
					this.list = data.content.list || [];
					if(initAfterAdd){
						this.$refs.list.setRadioSelect(this.list[0])
					}
					this.count = data.content.count;
				}
			})
		},
		/**
		*初使化数据
		**/
		initData(){
			this.type = this.params.type;
			this.search.page.cust_id = this.params.cust_id;

		},

		/**
		*选择--多选
		**/
		select(selects){
			if(this.type == 'radio') return;
			this.selectedData = selects.length?selects:[];
		},
		/**
		*选择--单选
		**/
		radioChange(data){
			if(this.type == 'checkbox') return;
			this.selectedData = [];
			data?this.selectedData.push(data):'';
		}
	},
	mounted() {
		this.initData();
		this.getList();
		this.$root.eventHandle.$on('close_copy_address', () => {
			this.getList(1);
    	})
	}
}
</script>

