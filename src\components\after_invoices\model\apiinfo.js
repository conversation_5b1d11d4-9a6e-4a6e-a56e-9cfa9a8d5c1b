// 退换跟踪单详情--接口信息
export default {
	data() {
		return {
			apiList: [],
			apiCols: [
				{
					label: '下游单类型',
					prop: 'push_bill_type',
          width: 280,
				}, {
					label: '单据编号',
					prop: 'interface_respone_billno',
          width: 180
				}, {
					label: '方式',
					prop: 'business_type',
          width: 160
				}, {
					label: '接口状态',
					prop: 'interface_status',
					formatter(val){
						return val == 1? '处理成功' : val == 2?'已取消':val == -1?'处理失败':val == 0?'处理中':val;
					},
          width: 80,
				}, {
					label: '接口提示信息',
					prop: 'interface_respone_errormsg'
				}, {
					label: '生成时间',
					width:150,
					prop: 'create_time',
					format:'dataFormat1'
				},
        {
          label: '操作',
          slot: 'operate',
          width: 180,
        },
			]
		}
	},
	methods:{
		getApiList(){
			this.apiList=[];
			var bill_no = this.form.bill_returns_no;
			if(!bill_no) return;
			let url = '/afterSale-web/api/aftersale/bill/queryExtInterfaceByBillNo';
			this.ajax.postStream(url,{bill_no:bill_no,bill_type:'RETURNS'},(res)=>{
				var data = res.body
				if(!data.result) {
					this.$message.error(data.msg);
					return;
				}
				this.apiList = data.content || [];
			})
		},
    reSendApiSale(row){
			let url = '/afterSale-web/api/aftersale/bill/returnsResaleGoods/reSendSale';
			this.ajax.postStream(url,row.id,(res)=>{
				var data = res.body
				if(!data.result) {
					this.$message.error(data.msg);
					return;
				} else {
          this.$message.success(data.msg);
        }
			})
		},
		reSendApiShipment(row){
			let url = '/afterSale-web/api/aftersale/bill/returnsResaleGoods/reSendShipment';
			this.ajax.postStream(url,row.id,(res)=>{
				var data = res.body
				if(!data.result) {
					this.$message.error(data.msg);
					return;
				} else {
          this.$message.success(data.msg);
        }
			})
		},
	},
	watch:{
		bottomTabs:function(newval,oldval){
			if(newval == 'three'){
				this.getApiList();
			}else{
				this.reSendDisabled=true;
				this.apiInfoMultipleSelection={};
			}
		}
	}

}
