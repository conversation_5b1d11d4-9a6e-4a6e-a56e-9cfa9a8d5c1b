// 退换跟踪单详情--商品信息
export default {
	data() {
		var self = this
		return {
			goodsBtns:[{
				type: 'primary',
				txt: '退件',
				disabled:true,
				click: ()=>{
					self.addGoods();
				}
			},{
				type: 'danger',
				txt: '删除',
				disabled:true,
				click: ()=>{
					self.delGoods();
				}
			}],
			typeObject:{},
			goodsList: [],
			selectGoods:[],//选择的商品数据
			goodsCols:[],
			feeObj:{}//运费对像
			
		}
	},

	methods:{
		/**
		*设置列表标题
		**/
		setColData(){
			//var obj = this.goodsInit();
			/*var obj = {
				'ROW_NORMAL':'正常退货',
				'ROW_RETURN':'返货退件',
				'ROW_SCRAP':'返货报废',
				'ROW_WAIT':'返货待定',
				'ROW_LOST':'返货丢件',
				
				'ROW_ESCRAP':'拉货前报废',
				
				'ROW_CANCEL':'取消退货',
			}*/
			//需要禁掉哪些选项,默认禁掉已拉货的选项(返货退件,返货待定,返货丢件)
			//value
			//var disableOption = ['ROW_RETURN','ROW_WAIT','ROW_LOST'];
			var colData = [
				{
					label: '客户',
					width:150,
					prop: 'custom_name'
				}, {
					label: '问题商品编码',
					width:120,
					prop: 'question_goods_code'
				}, {
					label: '问题商品名称',
					width:120,
					prop: 'question_goods_name'
					
				}, {
					label: '规格描述',
					width:150,
					prop: 'description'
				}, {
					label: '收入店铺',
					prop: 'user_shop_name'
					
				}, {
					label: '买家昵称',
					prop: 'custom_name'
				}, {
					label: '业务员',
					prop: 'staff_name'
				}, {
					label: '业务员分组',
					prop: 'staff_group'
				}, {
					label: '物料编码',
					width:200,
					prop: 'material_code'
				}, {
					label: '物料名称',
					width:200,
					prop: 'material_name'
				},{
					label: '物料规格描述',
					width:200,
					prop: 'material_desc'
				},{
					label: '是否为特裁商品',
					prop: 'if_special_cut',
					width:100,
					formatter(val){
						return val == 'Y' ? '是' : val == 'N'?'否':val
					}
				},{
					label: '是否停产',
					prop: 'if_stop_produce',
					formatter(val){
						return val == 'Y' ? '是' : val == 'N'?'否':val
					}
				},  {
					label: '单位',
					prop: 'units'
				}, {
					label: '数量',
					bool:true,
					isInput:true,
					isThis:true,
					conditionDisabled:'id',
					prop: 'number'
				}, {
					label: '包件数',
					prop: 'packet_count',//
				}, {
					label: '退货描述',
					prop: 'reason',//
					width:200,
					bool:true,
					isThis:true,
					conditionDisabled:'isNotEdit',
					isInput:true
				}, {
					label: '退货方式',
					prop: 'returns_type',
					format:'returnsType'
				}, {
					label: '退货仓库',//仓库不可变更
					prop: 'returns_storage',
					width:200,
					/*bool:true,
					//isInput:true,
					isThis:true,
					conditionDisabled:'canNotEditStorage',
					isInitInput:true,
					change:(value,obj)=>{
						this.clearStorage(value,obj);
					},
					redirectClick:(d)=>{
						this.returnsStorage(d);
						console.log('sdlfkjalskdfjkadsjflkasdjfaksdfjsd');
					}*/
					/*redirectChange:self.returnsStorage*/

				},/*{
					label: '是否报废',//
					prop: 'if_scrap',
					bool:true,
					isThis:true,
					conditionDisabled:'isNotEdit',
					isSelect:true,
					obj:{
						N:'否',
						Y:'是'
					}
				}, */ {
					label: '问题描述',
					prop: 'question_description',
					width:200
					
				}, {
					label: '备注信息',
					prop: 'remark',
					width:200,
					bool:true,
					isThis:true,
					conditionDisabled:'isNotEdit',
					isInput:true
				}, {
					label: '存货类别',
					prop: 'inventory_category'
				}, {
					label: '批次单号',
					prop: 'batch_trade_no',
					width:200
				}, {
					label: '批号',
					width:200,
					prop: 'branch_no'
				}, {
					label: '供应商',
					prop: 'supplier_company'
				}, {
					label: '商品BOM版本',
					width:200,
					prop: 'question_goods_bom_version'//
				}, {
					label: '物料BOM版本',
					width:200,
					prop: 'bom_version'
				}, {
					label: '实际售价',
					
					prop: 'act_price'
				}, {
					label: '退货费用',
					prop: 'returns_delivery_fee'
					
				},{
					label: '取货费用',
					prop: 'fetch_goods_fee'
					
				}, {
					label: 'BOM体积',
					width:80,
					prop: 'volume'//
				}, {
					label: '是否启用条码',
					prop: 'if_user_barcode',
					width:100,
					format:'yesOrNo'
				},  {
					label: '是否安装',
					prop: 'if_installed',
					bool:true,
					isThis:true,
					conditionDisabled:'isNotEdit',
					isSelect:true,
					obj:{
						N:'否',
						Y:'是'
					}

				}, {
					label: '异常类型',
					prop: 'exception_type',
					formatter(val){
						return val == 'MISS' ? '少件' : val == 'LOST'?'丢件':val == 'NULL'?'':val
					}
				}, {
					label: '异常商品金额',
					width:120,
					prop: 'exception_amount'
				}, {
					label: '仓储回传量',
					
					prop: 'passback_count'
				}, {
					label: '货损备注',
					width:200,
					prop: 'damage_remark'
				}, {
					label: '下推数',
					prop: 'pushdown_count'
				}, {
					label: '源单类型',
					width:200,
					prop: 'source_order_type',
					format:'sourceOrderType'
				}, {
					label: '源单编号',
					width:200,
					prop: 'source_order_code'
				},/*{
					label: '原退货跟踪单',
					width:200,
					prop: 'source_order_code'
				},*/ {
					label: '行类型',
					prop: 'row_type',
					bool:true,
					width:150,
					isThis:true,
					conditionDisabled:'conditionDisabled',
					isSelect:true,
					obj:{
						'ROW_NORMAL':'正常退货',
						'ROW_RETURN':'返货退件',
						'ROW_SCRAP':'返货报废',
						'ROW_WAIT':'返货待定',
						'ROW_LOST':'返货丢件',
						
						'ROW_ESCRAP':'拉货前报废',
						
						'ROW_CANCEL':'取消退货'
					},
					//需要禁掉哪些选项,默认禁掉已拉货的选项
					disableOption:['ROW_RETURN','ROW_WAIT','ROW_LOST']
				}, {
					label: '行状态',
					prop: 'row_status',
					format:'rowStatus'
				},{
					label: '销售退货单号',
					width:200,
					prop: 'bill_salesreturn_no'
				},{
					label: '其他出库单',
					width:200,
					prop: 'bill_othergodownenty_no'
				},{
					label: '直接调拔单',
					width:200,
					prop: 'bill_adjustment_no'
				},{
					label: '入库单',
					width:200,
					prop: 'bill_godownenty_no',
					initID:'initID_JS'
				}
			]
			if (this.ifPurchaseDealer) {
				colData = colData.reduce((arr, item) => {
					if (item.prop != 'supplier_company') {
						arr.push(item)
					}
					return arr
				}, [])

			}
			this.goodsCols = colData;

		},
		/*
		*清除仓库
		**/
		clearStorage(value,data){
			if(value) return;
			console.log('data',data);
			data.returns_storage = null;
			data.storage_id = null;
			
		},
		/***
		*设置产品是否可选择
		**/
		selectableFun(data){
			if(data.row_status == 'Y') return false;
			return true;
		},
		/**
		*设置仓库
		***/
		setStorageOfAllGoods(data){
			let i = this.goodsList.length;
			for(let a = 0;a<i;a++){
				let d = this.goodsList[a];
				if(d.row_status == 'Y') continue;
				//不是返货待定或者返货待定是有仓库的，都需要统一变更仓库
				if(d.row_type != 'ROW_WAIT' || (d.row_type == 'ROW_WAIT' && d.storage_id) ){
					d.storage_id = data.stock_id;
					d.returns_storage = data.stock_name;
				}
			}
			/*this.goodsList.map((a,b)=>{
				
				if(a.row_type != 'ROW_WAIT' || (a.row_type == 'ROW_WAIT' && a.storage_id) ){
					a.storage_id = data.stock_id;
					a.returns_storage = data.stock_name;
				}
			});*/
		},
		//改变退换货仓库
		returnsStorage(row){
			console.log('row',row);
			if(row.row_status == 'Y' || row.canNotEditStorage) return;
			let params = {
				material_id : row.material_id,
				selectType:1
			}
			params.callback = (d)=>{
				console.log('d',d);
				let data = d.data;
				/*row.storage_id = data.stock_id;
				row.returns_storage = data.stock_name;*/
				this.form.if_deposit = data.if_deposit?'Y':'N';
				this.setStorageOfAllGoods(data);
			}

			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_solutions/warehouselist'),
				style:'width:800px;height:560px',
				title:'选择退货仓库'
			});
		},

		/**
		*删除商品明细
		***/
		delGoods(){
			if(!this.selectGoods || !this.selectGoods.length){
				this.$message.error('请选择要删除的明细行');
				return;
			}
			//var errorTip = "";
			
			for(let kk = 0,nn = 0;kk < this.selectGoods.length;kk = nn){
				let sGood = this.selectGoods[kk];
				if(sGood.id){
					//errorTip += sGood.material_code + ',';
					nn++;
					continue;
				}
				for(let aa = 0;aa < this.goodsList.length;aa++){
					let bGood = this.goodsList[aa];
					if(bGood.id) continue;
					if(JSON.stringify(sGood) == JSON.stringify(bGood)){
						this.goodsList.splice(aa,1);
						break;
					}
				}
				this.selectGoods.splice(kk,1);
				nn = 0;
			}
			

			//对应明细行添加的商品是否全部删除，如果是全部删除，则需要恢复它的行类型为正常退款，并且是可编辑的
			//拿已存数据表的明细和新增的明细进行对比
			
			/*var i = this.goodsList.length;
			for(let n = 0;n < i;n++){//已存表的数据
				let g = this.goodsList[n];
				if(!g.id || g.row_status == 'Y') continue;
				let id = g.id;
				for(let f=0;f<i;f++){//没有存表的数据
					let o = this.goodsList[f];
					let originalId = o.originalId;
					if(id == originalId) break;//找到了
					if(f == i-1){
						//没有找到
						g.conditionDisabled = false;
						g.row_type = 'ROW_NORMAL';
						//this.goodsList.splice(1,n,g);
						break;
					}
				}
				
			}*/
			var errorTip = '';
			this.selectGoods.map((a,b)=>{
				errorTip += a.material_code + ',';
			});
			if(errorTip){
				this.$message.error('物料编码为'+errorTip+'的明细行不能删除');
			}
		},
		/**
		*根据明细证明主键ID去查换明细
		***/
		getGoodById(id){
			if(!id) return '';
			var obj;
			this.goodsList.map((a,b)=>{
				if(a.id == id){
					obj = a;
					return;
				}
			})
			return obj;
		},
		/**
		*添加商品明细
		**/
		addGoods(){
			if(!this.selectGoods || !this.selectGoods.length){
				this.$message.error('请选择要添加商品的明细行');
				return;
			}
			var list = [];
			this.selectGoods.map((a,b)=>{
				//已关闭的行不能再进行变更
				if(a.id && a.row_status == 'N'){
					let copyA = JSON.parse(JSON.stringify(a));
					let question_sub_id = copyA.question_sub_id;
					copyA.isReturnGoodsOfBill = true;
					//ID和question_sub_id互换
					copyA.question_sub_id = copyA.id;
					copyA.id = question_sub_id;

					list.push(copyA);
					/*list.push(a);*/
				}
			});
			if(!list.length){
				this.$message.error('请选择原有的明细行进行操作');
				return;
			}
			//添加一个参数，退货跟踪单过去的
			let params = {
				questionList:list,
				info:{isReturnGoods:true,merge_trade_id:this.form.merge_trade_id},
				callback:(d)=>{
					this.addGoodsOfPop(d);
				}
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_solutions/bomgoods'),
				style:'width:80%;height:560px',
				title:'添加商品'
			});
		},
		/**
		*计算总的退货费用
		**/
		/*calcTotalReturnGoodFee(){
			var totalFee = 0;
			this.goodsList.map((a,b)=>{
				let fee = parseFloat(a.returns_delivery_fee || 0);
				if(a.row_status == 'N'){
					totalFee += fee;
				}
			});
			this.form.predicted_delivery_fee = totalFee.toFixed(2);
		},*/
		/**
		*计算退货费用
		*退货费用=体积*运费单价*返货倍数
		*1、体积：优先是由已发运明细的商品带出来。如果没有，则取值物料基础资料档中的体积。2、运费单价和返货倍数 提货点列表的运费字段和返货倍数字段
		**/
		/*calcReturnGoodFee(returnGood){
			if(!returnGood) return;
			//var volume = isNaN(returnGood.volume)?0:parseFloat(returnGood.volume);
			var volume = !returnGood.volume?0:parseFloat(returnGood.volume);
			var fee = parseFloat(this.feeObj.fee || 0);//运费单价
			var multiple = parseFloat(this.feeObj.multiple || 0);//返货倍数
			var p = volume * fee * multiple;
			returnGood.returns_delivery_fee = p.toFixed(2);
		},*/
		/**
		*计算取货费用
		*取货费用 = 实际单价*结算费率*0.9
		*1、优先取实际单价*区对应的三包点的不超区费率*0.9，其次取值：实际单价*区对应的三包点的超区费率*0.9
		**/
		/*calcPickGoodFee(returnGood){
			if(!returnGood) return;
			var act_price = !returnGood.act_price?0:parseFloat(returnGood.act_price);
			var clearrates = parseFloat(this.feeObj.clearrates || 0);//结算率
			var p = act_price * clearrates * 0.9;
			returnGood.fetch_goods_fee = p.toFixed(2);
		},*/
		/**
		*计算单个退货商品里面的相关费用
		**/
		/*calcSingleReturnGoodFee(returnGood){
			this.calcReturnGoodFee(returnGood);
			this.calcPickGoodFee(returnGood);
		},*/

		fillReturnGoodFeeAndPickGoodsFee(data){

			if(!data || !data.length){
				//重置费用数据
				this.goodsList.map((a,b)=>{
					a.returns_delivery_fee = null;
					a.fetch_goods_fee = null;
				})
				return;
			}
			let goods = this.goodsList;
			let i = goods.length;
			let f = data.length;
			for(let aa = 0;aa < f;aa++){
				let good = data[aa];
				let material_id = good.material_id;
				let question_sub_id = good.question_sub_id;
				for(let a = 0;a < i;a++){
					let good2 = goods[a];
					let material_id2 = good2.material_id;
					let question_sub_id2 = good2.question_sub_id;
					if(material_id2 != material_id || question_sub_id != question_sub_id2) continue;
					good2.returns_delivery_fee = good.delivery_fee;
					good2.fetch_goods_fee = good.fetch_goods_fee;
				}
			}

			
		},
		/**
		*退货，换货费用所需要的参数值
		**/
		getReturnFeeAndPickFeeParams(){
			let list = [];
			let goods = this.goodsList;
			goods.map((a,b)=>{
				if(a.id){
					let obj = {
						material_id : a.material_id,
						question_sub_id : a.question_sub_id
					
					}
					list.push(obj);
				}
				
			});
			return list;
		},

		/**
		*根据商品的改变退货，换货，运费费用
		**/
		calcFeeByChangeGoods(){
			let info = this.form;
			let data = {
				province : info.province,
				city : info.city,
				area : info.area,
				street : info.street,
				logistics_supplier_class: info.logistics_supplier_class,

			}
			let getReturnGoodsParams = this.getReturnFeeAndPickFeeParams();
			
			data.list_return = getReturnGoodsParams;
			
			this.ajax.postStream('/afterSale-web/api/aftersale/plan/getFee',data,(res)=>{
				let d = res.body;
				if(!d.result){
					this.$message.error(d.msg);
				}
				let content = d.content || {};
				this.fillReturnGoodFeeAndPickGoodsFee(content.return_material || []);

			});
		},


		/**
		*获取运费
		**/
		/*getAddressAndFee(){
			let params = {
				province : this.form.province,
				city : this.form.city,
				area : this.form.area
				
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/getThreeInfo',params,res=>{
				let data = res.body;
				this.feeObj = data.content || {};
				console.log('dafasdf',data);
			});
		},*/
		
		/**
		*把成品添加到退货商品页签
		*全部物料接口
		**/
		setReturnGoodsOfGoods(a,b){
			a.material_code = b.materialNumber;
			a.material_name = b.materialName;
			a.material_desc = b.materialSpecification;
			a.material_id = b.sourceMaterialId;
			a.act_price = b.act_price || 0;
			a.bom_version = b.bom_version;
			a.volume = b.volume;
			a.units = b.materialUnit;
			a.inventory_category = b.inventoryCategory;
			a.if_stop_produce = b.isStop?'Y':'N';

			b.stock_id?a.storage_id = b.stock_id:'';
			b.stock_id?a.returns_storage = b.stock_name:'';

		},

		/**
		*把拆分物料添加到退货商品页签
		**/
		setReturnGoodsOfBom(a,b){
			if ('零部件' === b.inventoryCategory) {
				a.branch_no = '';  // 批次订单号设为空
			}

			a.material_code = b.bom_material_num;
			a.material_name = b.bom_material_name;
			a.material_desc = b.bom_material_desc;
			a.material_id = b.bom_material_id;//用bomID

			a.act_price = b.act_price || 0;
			
			a.bom_version = b.bom_version;
			a.volume = b.volume;
			a.units = b.bom_version_unit;
			b.stock_id?a.storage_id = b.stock_id:'';
			b.stock_id?a.returns_storage = b.stock_name:'';
			
			a.inventory_category = b.inventoryCategory;
			a.if_stop_produce = b.isStop?'Y':'N';
		},
		/**
		*发运明细的商品添加到退货商品当中
		*成品退货
		*/
		setRetrunGoodsOfSaleGoods(a,b){
			a.material_code = b.material_number;
			a.material_name = b.material_name;
			a.material_id = b.materia_id;
			a.material_desc = b.material_specification;
			a.bom_version = b.bom_version;
			
			a.units = b.material_unit;
			a.volume = b.volume;
			a.if_stop_produce = b.is_stop?'Y':'N';
			a.inventory_category = b.inventory_category;

			a.act_price = b.act_price;

			//TODO,这边还需要取值
			a.stand_price = b.stand_price;
			a.price_list = b.price_list;
			a.price_list_id = b.price_list_name;
			a.sale_order_no = b.sys_trade_no;
			a.sys_trade_id = b.sys_trade_id;

			a.custom_id = b.customer_id;//客户ID
			a.custom_name = b.customer_name;//客户名称
			a.nick_name = b.customer_name;//买家昵称（冗余字段）

			a.shop_name = b.shop_name;//店铺名称（冗余字段）

			a.staff = b.user_id;//业务员id（冗余字段）

			a.staff_name = b.real_name;//业务员姓名（冗余字段）
			a.staff_group = b.group_name;//业务员分组（冗余字段）

			a.batch_trade_no = b.batch_trade_no;//批次订单号
			a.batch_trade_id = b.batch_trade_id;//批次订单id

			a.logistics_supplier = b.logistics_supplier;//物流运输商ID
			a.logistics_supplier_name = b.logistics_supplier_name;//物流运输商名称

			a.merge_material_id = b.merge_material_id;//发运明细ID
			a.branch_no = b.lot; //商品批号
			a.supplier_id = b.suppiler_id;//供应商id
			a.supplier_company = b.supplier_company;//供应商名称
			a.user_shop_name = b.user_shop_name;
			a.original_shop_name = b.original_shop_name;
		},

		/**
		*把选中的物料添加到明细行
		*type == 1（成品），type == 2(拆分物料),type == 3(发运明细)
		{
					label: '销售退货单号',
					width:200,
					prop: 'bill_salesreturn_no'
				},{
					label: '其他出库单',
					width:200,
					prop: 'bill_othergodownenty_no'
				},{
					label: '直接调拔单',
					width:200,
					prop: 'bill_adjustment_no'
				},{
					label: '入库单',
					width:200,
					prop: 'bill_godownenty_no',
					initID:'initID_JS'
				}
		*
		**/
		addGoodsOfPop(d){
			console.log('点击确认');
			var id = d.questionId;
			var data = d.data;
			var type = d.type;
			if(!data) return;
			var good, i = 0;
			for(; i<this.goodsList.length;i++){
				let g = this.goodsList[i];
				if(g.id == id){
					good = g;
					break;
				}
				/*if(g.question_sub_id == id){
					good = g;
					break;
				}*/
			}
			if(!good) return;
			let fn = this[type == 3?'setRetrunGoodsOfSaleGoods':type == 2?'setReturnGoodsOfBom':'setReturnGoodsOfGoods'];

			for(let c = 0;c < data.length; c++){
				let f = data[c];
				let copyGood = JSON.parse(JSON.stringify(good));
				copyGood.id = null;
				copyGood.bill_salesreturn_no = null;
				copyGood.bill_othergodownenty_no = null;
				copyGood.bill_adjustment_no = null;
				copyGood.bill_godownenty_no = null;

				/*copyGood.material_code = f.bom_material_num;
				copyGood.material_desc = f.bom_material_id;
				copyGood.material_id = f.bom_material_id;
				copyGood.material_name = f.bom_material_name;
				copyGood.act_price = f.act_price;//
				copyGood.volume = f.volume;//*/
				fn(copyGood,f);
				copyGood.originalId = good.id;//储存对应的明细行ID
				copyGood.row_type = 'ROW_NORMAL';
				copyGood.source_order_code = this.form.bill_returns_no;
				copyGood.branch_no = (f.lot_control == 'Y' || f.lot_control == 1)?f.lot || '888888':null;//批号
				
				
				copyGood.conditionDisabled = true;
				
				//this.calcSingleReturnGoodFee(copyGood);
				this.goodsList.splice(i+1,0,copyGood);
			}
			
			/*if(!good.originalType){
				good.originalType = good.row_type;
			}
			good.conditionDisabled = true;
			good.row_type = 'ROW_RETURN';*/
			this.calcFeeByChangeGoods();
			//this.calcTotalReturnGoodFee();

		},

		goodsSelects(selections){
			this.selectGoods = selections.length?selections:[];
		},
		/**
		*根据已拉货未拉货禁掉一些行类型的选项的操作
		***/
		setDisabledOption(){
			console.log('看看行禁掉的数据');
			let disableOption = [];
			if(this.form.if_pickup == 'Y'){//已拉货，禁掉未拉货的选项,
				//拉货前报废,取消退货
				disableOption = ['ROW_ESCRAP','ROW_CANCEL'];
				//如果是客户自返，则还需要禁掉返货待定
				this.form.shipping_method == 'CUSTOMER'?disableOption.push('ROW_WAIT'):'';

			}else{
				//未拉货，禁掉已拉货选项 返货退件,返货待定,返货丢件
				disableOption = ['ROW_RETURN','ROW_WAIT','ROW_LOST','ROW_SCRAP'];
			}

			// 退货行未拉货变更类型允许为：取消退货、拉货前报废、返货报废，其余置灰；
			if(this.form.shipping_method == 'RESALE'){
				disableOption = ['ROW_NORMAL', 'ROW_RETURN', 'ROW_WAIT', 'ROW_LOST']
				if(this.form.if_pickup == 'N'){
					disableOption.push('ROW_SCRAP')
				}
			}
			
			let i = this.goodsCols.length;
			for(let a = 0;a < i;a++){
				let data = this.goodsCols[a];
				if(data.prop == 'row_type'){
					data.disableOption = disableOption;
					break;
				}
			}

		},
		/**
		*设置明细行的行类型
		*退货跟踪单，退货组操作锁定时:正常退货,拉货前报废
		*已拉货只能是：正常退货，返货退件，返货报废，返货待定，返货丢件
		*未拉货：正常退货，返货报废，取消退货
		**/
		/*setRowType(){
			return;
			let rowType = this.goodsInit();
			let i = this.goodsCols.length;
			for(let a = 0;a < i;a++){
				let data = this.goodsCols[a];
				if(data.prop == 'row_type'){
					data.obj = rowType;
					break;
				}
			}
			
		}*//*,goodsInit(){
			if(this.form.if_pickup == 'Y'){
				return {
					'ROW_NORMAL':'正常退货',
					'ROW_RETURN':'返货退件',
					'ROW_SCRAP':'返货报废',
					'ROW_WAIT':'返货待定',
					'ROW_LOST':'返货丢件',
				}

			}
			return {
				'ROW_NORMAL':'正常退货',
				'ROW_ESCRAP':'拉货前报废',
				//'ROW_RETURN':'返货退件',
				'ROW_SCRAP':'返货报废',
				'ROW_CANCEL':'取消退货',
			}
		}*/
	},
	watch:{
		confirmChangeDisabled:function(newval,oldval){
			//变更按钮改变的时候，控制问题商品的两个按钮
			var goodsBtns = [];
			this.goodsBtns.map((a,b)=>{
				var d = Object.assign({},a);
				d.disabled = newval || this.form.staff_locker !== this.getEmployeeInfo('id')/*业务锁定人=代理人才能操作*/;
				goodsBtns.push(d);
			});
			this.goodsBtns = [];
			this.goodsBtns = goodsBtns;
		},
		ifPurchaseDealer(newVal,oldVal) {
			if (newVal != oldVal) {
				this.setColData();
			}
		}
	}/*,
	mounted() {
		this.setColData();
	}*/
}