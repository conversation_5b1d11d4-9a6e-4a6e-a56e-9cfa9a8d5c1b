// 退换跟踪单详情--商品信息
import { debounce  } from 'lodash'

export default {
	data() {
		var self = this
		return {
			goodPriceText:'添加包件价格',
			rowType:[{
					type: 'primary',
					txt: '退货异常',
				},
				{
					type: 'primary',
					txt: '返货退件',
					isDisabled: true,
					click: () => {
						if(this.selectGoods[0].shipping_method === 'SCRAP'){
							this.$message.error('报废类型的退货商品行不允许操作退货异常')
							return
						}
						this.checkIsSameReturnsType(this.addGoods)
						// this.goodsBtnsAction('returnGooodsException', { operition_type: 'GOODSRETURNS' })
					}
				},{
					type: 'primary',
					txt: '返货丢件',
					click:()=>{
						this.checkIsSameReturnsType(() => {
							this.$root.eventHandle.$emit('openDialog', {
			    				ok: () => {
									this.goodsBtnsAction('returnGooodsException?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM', { operition_type: 'GOODSLOST' })
			    				},
			    				okTxt: '是',
			    				no() {

			    				},
			    				noTxt: '否',
			    				txt: '修改为返货丢件，整个商品都丢件哦，确认修改？',
			    				noCancelBtn: true
			    			})
						})
					}
				}, {
					type: 'primary',
					txt: '经销丢件',
					click:()=>{
						this.checkIsSameReturnsType(() => {
							this.$root.eventHandle.$emit('openDialog', {
			    				ok: () => {
									this.goodsBtnsAction('returnGooodsException?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM', { operition_type: 'DEALERGOODSLOST' })
			    				},
			    				okTxt: '是',
			    				no() {

			    				},
			    				noTxt: '否',
			    				txt: '修改为经销丢件，整个商品都丢件哦，确认修改？',
			    				noCancelBtn: true
			    			})
						})
					}
				}
			],
			returnType:[
				{
					type: 'primary',
					txt: '返货类型变更',
				},
				{
					type: 'primary',
					txt: '退货再售',
					click: () => this.returnTypeChange('RESALE'),
				},{
					type: 'primary',
					txt: '三包返货',
					click: () => this.returnTypeChange('THREE_BAG'),
				},{
					type: 'primary',
					txt: '客户自返',
					click: () => this.returnTypeChange('CUSTOMER'),
				},{
					type: 'primary',
					txt: '报废',
					click: () => this.returnTypeChange('SCRAP'),
				},{
					type: 'primary',
					txt: '截货',
					click: () => this.returnTypeChange('INTERCEPT'),
				},{
					type: 'primary',
					txt: '快递返货',
					click: () => this.returnTypeChangeOfExpressReturn('EXPRESS_RETURN'),
				}
			],
			dealerType:[
				{
					type: 'primary',
					txt: '经销采购操作',
					click: () =>{ return false;},

				},
				{
					type: 'primary',
					txt: '经销采购退货申请',
					click: () => this.goodsBtnsAction('purchaseDealerRrturnApply?permissionCode=PURCHAS_RETURN_APPLY'),
				},{
					type: 'primary',
					txt: '经销采购退货审核',
					click: () => this.goodsBtnsAction('purchaseDealerRrturnAudit?permissionCode=PURCHASE_RETURN_AUDIT'),
				},{
					type: 'primary',
					txt: '经销采购退货驳回',
					click: () => this.goodsBtnsAction('purchaseDealerRrturnReject?permissionCode=PURCHASE_RETURN_REJECT'),
				},{
					type: 'primary',
					txt: '取消经销采购退货',
					click: () => this.goodsBtnsAction('purchaseDealerRrturnCancle?permissionCode=PURCHASE_RETURN_CANCLE'),
				}
			],
		}
	},
	methods:{
		/***
		*问题商品信息是否可以勾选
		*已下推应收单的报废，行状态关闭且不等于退货再售、取消退货，异常类型不为空时不可勾选
        *返货类型为经销退货、经销自提的可勾选-》XPT-15797 生活馆经销采购订单的售后模式调整
		**/
		selectableFun(data){
			if(
				(data.shipping_method === 'SCRAP' && data.bill_receivables_no)
				|| (data.row_status == 'CLOSE' && !(/^(DEALER|DEALER_PICK|RESALE|INTERCEPT)$/.test(data.shipping_method)))
				|| data.row_status == 'END'
				|| data.cargo_status === 'END'
				|| data.warehouse_status === 'END'
				|| /^(GOODSRETURNS|GOODSLOST)$/.test(data.exception_control)
				|| data.act_price&&String(data.act_price).includes("合计")
			) return false;
			return true;
		},
		// 点击一行时背景色加深
		rowClickHighlight (data, e){
			e.path.some($dom => {
				if(/^tr$/i.test($dom.nodeName) && /el-table__row/.test($dom.className)){
					if($dom.classList.contains(this.$style['which-row-click'])){
						$dom.classList.remove(this.$style['which-row-click'])
					}else {
						if($dom.parentElement.querySelector('.' + this.$style['which-row-click'])){
							$dom.parentElement.querySelector('.' + this.$style['which-row-click']).classList.remove(this.$style['which-row-click'])
						}
						$dom.classList.add(this.$style['which-row-click'])
					}
					return true
				}
			})
		},
		// 退货仓库弹框
		selectWarehouse (e, row){
			this.$root.eventHandle.$emit('alert',{
				params: {
					material_id : row.material_id,
					callback: d => {
						row.storage_id = d.data.stock_id
						row.returns_storage = d.data.stock_name
						row.if_deposit = d.data.if_deposit==1?'Y':'N'
					},
				},
				component:()=>import('@components/after_solutions/warehouselist'),
				style:'width:800px;height:560px',
				title:'选择退货仓库'
			});
		},
		// 作废服务单
		cancelActionByNo (api/*作废服务单 or 撤回入库单*/){
			if(/^cancelService/.test(api)){
				if( this.selectGoods.some(obj => !obj.bill_service_no || obj.bill_service_no !== this.selectGoods[0].bill_service_no) ){
					this.$message.error('所选项需要服务单号一致且不能为空')
				}else {
					this.goodsBtnsAction(api)
				}
			}else {
				if( this.selectGoods.some(obj => !obj.bill_godownenty_no || obj.bill_godownenty_no !== this.selectGoods[0].bill_godownenty_no) ){
					this.$message.error('所选项需要入库单号一致且不能为空')
				}else {
					this.goodsBtnsAction(api)
				}
			}
		},

    onReturnToExchange: debounce.call(this, function() {
      this.goodsBtnsAction('goods/returnToExchange');
    }, 1500, {
      leading: true,
      trailing: false,
    }),

		// 商品行按钮操作合集
		goodsBtnsAction (api, other, cb, changeParams){
			let params = this.selectGoods.map(o =>
				Object.assign(
					{ id: o.id },
					typeof other === 'function'
					? other(o)
					: other
				)
            )
					let purchaseDealerIds = [], postData = null
					if (api === 'returnTypeChange?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM') {
						postData = changeParams
					} else if (/^((purchaseDealerRrturnApply\?permissionCode=PURCHAS_RETURN_APPLY)|(purchaseDealerRrturnAudit\?permissionCode=PURCHASE_RETURN_AUDIT)|(purchaseDealerRrturnReject\?permissionCode=PURCHASE_RETURN_REJECT)|(purchaseDealerRrturnCancle\?permissionCode=PURCHASE_RETURN_CANCLE)|(goods\/returnToExchange))$/.test(api)){
						this.selectGoods.forEach(item => {
								purchaseDealerIds.push(item.id)
						})
						postData = {
								id: this.form.id,
								ids: purchaseDealerIds
						}
					} else {
						postData = params
					}
			// let postData = api === 'returnTypeChange?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM' ? changeParams : params
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/return/' + api, postData, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
					cb && cb()
					this.getDetail()
					this.getOperateList()
					this.getScheduleList()
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		returnTypeChangeOfExpressReturn(shipping_method){
			let self = this
			let materialList = this.selectGoods.reduce((arr, item) => {
				arr.push(item.material_code)
				return arr
			}, [])
			self.ajax.postStream('/material-web/api/material/getMaterialListByNumbers',materialList,(res)=>{
				if (!res.body.result || !res.body.content.length) {
					self.$message.error("没有找到该物料对应分组");
					return;
				}
				let list = res.body.content
				let stockObj = list.reduce((obj, item)=> {
					let returns_storage = res.groupNumber == "XSP"?"小商品退货仓":"退货仓库"
					let storage_id = res.groupNumber == "XSP"?"25061367":"9363594"
					obj[item.materialNumber] = {
						storage_id,
						returns_storage
					}
					return obj
				}, {})
				self.returnTypeChange(shipping_method, stockObj)
			});
		},
		// 退货商品行--返货类型变更、退货再售、三包返货、客户自返、报废、截货
		returnTypeChange (shipping_method, stockObj){
			let self = this;
			var CKName = {
				CUSTOMER: 'TH',
				THREE_BAG: 'CP',
				RESALE: 'THXNC',
				INTERCEPT: 'CP',
				SCRAP: 'THXNC',
			}[shipping_method]
			,	ifCargoVal = {
				CUSTOMER: 'N',
				THREE_BAG: 'Y',
				RESALE: 'Y',
				INTERCEPT: 'N',
				SCRAP: 'N',
				EXPRESS_RETURN: 'Y',
			}[shipping_method]
			,	CKObj = __AUX.get('CK').filter(obj => obj.name === CKName)[0] || {}
			let params = [], returns_storage = '', storage_id = '';
			let ifTypeChange = false;

			// 判断是否快递发货商品不允许建退货在售类型

			// ifTypeChange = this.checkTypeChange(shipping_method);
			// console.log(ifTypeChange);
			// if(!!ifTypeChange){
			// 	return false;
			// }
			// new Promise((callback,rej)=>{
			// 	callback(this.checkTypeChange(shipping_method))
			// });


			new Promise((resolve,reject)=>{
			this.checkTypeChange(shipping_method,resolve);
			}).then(res=>{
				console.log(res);
				if(!res){
					return false;
				}
				this.selectGoods.forEach(item => {
					this.refundingPaymentList.forEach(storage => {
						if (item.merge_material_id === storage.merge_material_id) {
							returns_storage = storage.stockName
							storage_id = storage.stockId
						}
					})
					if(shipping_method == "CUSTOMER"){
						let storage_id = shipping_method === 'RESALE' ? '23678858' : (/^(SCRAP)$/.test(shipping_method) ? '' : (/^(INTERCEPT|THREE_BAG)$/.test(shipping_method) ? storage_id : CKObj.code)),
							returns_storage = shipping_method === 'RESALE' ? '退货虚拟仓' : (/^(SCRAP)$/.test(shipping_method) ? '' : (/^(INTERCEPT|THREE_BAG)$/.test(shipping_method) ? returns_storage : CKObj.remark));

						self.refundingPaymentList.forEach(storage=>{

								if (item.merge_material_id === storage.merge_material_id && storage.stockId == "24094763") {
									storage_id = "25061367";
									returns_storage = "小商品退货仓";

								}
								if (item.merge_material_id === storage.merge_material_id && storage.stockId == "27700974") {
									self.refundingMaterialList.forEach(material=>{
										if(item.material_code === material.materialNumber){
											let ifXSP = material.groupNumber == "XSP";
											returns_storage = ifXSP?"小商品退货仓":"退货仓库";
											storage_id = ifXSP?"25061367":"9363594";
										}
									})



								}

						})

						params.push({
							shipping_method,
							storage_id: storage_id,
							returns_storage:returns_storage,
							if_cargo: ifCargoVal,
							if_deposit:CKObj.tag||'N',
							id: item.id
						})
					} else if (shipping_method == "EXPRESS_RETURN") {
						params.push({
							shipping_method,
							storage_id: stockObj[item.material_code].storage_id,
							returns_storage:stockObj[item.material_code].returns_storage,
							if_cargo: ifCargoVal,
							if_deposit:CKObj.tag||'N',
							id: item.id
						})
					} else{
						params.push({
							shipping_method,
							storage_id: shipping_method === 'RESALE' ? '23678858' : (/^(SCRAP)$/.test(shipping_method) ? '' : (/^(INTERCEPT|THREE_BAG)$/.test(shipping_method) ? storage_id : CKObj.code)),
							returns_storage:shipping_method === 'RESALE' ? '退货虚拟仓' : (/^(SCRAP)$/.test(shipping_method) ? '' : (/^(INTERCEPT|THREE_BAG)$/.test(shipping_method) ? returns_storage : CKObj.remark)),
							if_cargo: ifCargoVal,
							if_deposit:CKObj.tag||'N',
							id: item.id
						})
					}

				})
				// params.push({
				// 	shipping_method,
				// 	storage_id: shipping_method === 'RESALE' ? '23678858' : (/^(SCRAP)$/.test(shipping_method) ? '' : (/^(INTERCEPT|THREE_BAG)$/.test(shipping_method) ? storage_id : CKObj.code)),
				// 	returns_storage:shipping_method === 'RESALE' ? '退货虚拟仓' : (/^(SCRAP)$/.test(shipping_method) ? '' : (/^(INTERCEPT|THREE_BAG)$/.test(shipping_method) ? returns_storage : CKObj.remark)),
				// 	if_cargo: ifCargoVal,
				// 	if_deposit:CKObj.tag||'N',
				// 	id: item.id
				// })
				this.goodsBtnsAction(
					'returnTypeChange?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM',
					o => ({
						shipping_method,
						storage_id: /^(RESALE|SCRAP)$/.test(shipping_method) ? '' : CKObj.code,
						returns_storage: /^(RESALE|SCRAP)$/.test(shipping_method) ? '' : CKObj.remark,
						if_cargo: ifCargoVal,
						if_deposit:CKObj.tag||'N'
					}), '',params)
			})

		},

		/**
		*设置列表标题
		**/
		setColData(){
			//var obj = this.goodsInit();
			/*var obj = {
				'ROW_NORMAL':'正常退货',
				'ROW_RETURN':'返货退件',
				'ROW_SCRAP':'返货报废',
				'ROW_WAIT':'返货待定',
				'ROW_LOST':'返货丢件',

				'ROW_ESCRAP':'拉货前报废',

				'ROW_CANCEL':'取消退货',
			}*/
			//需要禁掉哪些选项,默认禁掉已拉货的选项(返货退件,返货待定,返货丢件)
			//value
			//var disableOption = ['ROW_RETURN','ROW_WAIT','ROW_LOST'];
			var colData = [
        {
          label: "问题商品编码",
          width: 120,
          prop: "question_goods_code"
        },
        {
          label: "问题商品名称",
          width:85,
          prop: "question_goods_name"
        },
        {
          label: "物料编码",
          width: 120,
          prop: "material_code"
        },
        {
          label: "物料名称",
          width: 100,
          prop: "material_name"
        },
        {
          label: "实际售价",
          bool: false,
          elInput: false,
		  width: 110,
          disabled: row =>
            row.question_goods_code == row.material_code ||
            (row.source_row_id && row.source_row_id != row.id) ||//成品不能修改价格
            row.shipping_method == 'PUR_DEALER', //经销采购退货不可修改价格
          // isThis:true,
          // conditionDisabled:'baojia_price',
          prop: "act_price",
          blur: (row, e) => {
            if (row.act_price) {
              row.act_price = e.target.value = Number(
                Number(row.act_price).toFixed(2)
              );
            }
          }
        },
        {
          label: "返货类型",
          prop: "shipping_method",
          formatter: prop =>
            ({
              THREE_BAG: "三包返货",
              CUSTOMER: "客户自返",
              RESALE: "退货再售",
              DEALER: "经销退货",
              INTERCEPT: "截货",
              SCRAP: "报废",
              DEALER_PICK: "经销自提",
              CG_ESCRAP: "拉货前报废",
              CG_SCRAP: "返货报废",
              CG_LOST: "返货丢件",
              CG_RETURN: "返货退件",
              CG_WAIT: "返货待定",
              CG_CANCEL: "取消退货",
              PUR_DEALER: '经销采购退货',
              EXPRESS_RETURN: '快递返货'
            }[prop] || prop)
        },
        {
          label: "退货方式",
          prop: "returns_type",
          format: "returnsType"
        },
        {
          label: "退货仓库", //仓库不可变更
          prop: "returns_storage",
          width: 100,
          bool: true,
          iconClick: this.selectWarehouse,
          // 仓库变更条件：返货类型为客户自返，入库状态为等待或撤回，且不等于审核中和已审核，且已锁定
          disabled: row =>
            !(
               ( /^(CUSTOMER|THREE_BAG)$/.test(row.shipping_method) &&
			   /^(WAIT|RETRACTED)$/.test(row.warehouse_status) &&
			   !/^(APPROVING|APPROVED)$/.test(this.form.status) &&
			   this.form.locker
			   )||
			   ( /^(PUR_DEALER)$/.test(row.shipping_method) &&
			   /^(APPROVING|CREATE)$/.test(this.form.status) &&
			   this.form.locker
			   )
            )
        },
        {
          label: "是否物流商拉货",
		  prop: "if_cargo",
		  width: 97,
          bool: true,
          isThis: true,
          disabled: row =>
			row.shipping_method=="EXPRESS_RETURN" ? true :
            (row.__is_END ||
            !(
              this.form.locker &&
              row.shipping_method === "RESALE" &&
              !row.bill_service_no
            )), //是否物流商拉货---锁定&&退货再售&&服务单为空才能改
          // conditionDisabled:'isNotEdit',
          isSelect: true,
          obj: {
            N: "否",
            Y: "是"
          }

          // format: 'yesOrNo',
        },
        {
          label: "是否算退货率",
		  prop: "return_rate",
		  width: 86,
          bool: true,
          isThis: true,
          disabled: row =>
            row.__is_END ||
            !(this.form.locker && this.form.status === "CREATE"), //是否算退货率---锁定&&创建才能改
          // conditionDisabled:'isNotEdit',
          isSelect: true,
          obj: ["否", "是"]

          // format: 'yesOrNo',
        },
		{
			label: "客户是否反馈产品问题",
			prop: "if_customer_feedback",
			width: 140,
			bool: true,
			isThis: true,
			//客户是否反馈产品问题---锁定&&单据状态为创建或者已审核&&行入库状态为已撤回、等待  可修改
			disabled: row =>{
				return row.shipping_method=="EXPRESS_RETURN" ? true : !(this.form.locker&&(row.warehouse_status=="WAIT"||row.warehouse_status=="RETRACTED") && (this.form.status == "CREATE"||this.form.status == "APPROVED")&&row.shipping_method=="CUSTOMER")
			},
			isSelect: true,
			obj: {N: "否",Y: "是",W:"未选择"}
		  },
        {
          label: "异常类型",
          prop: "exception_control",
          formatter: val =>
            ({
              GOODSRETURNS: "退件",
              DEALERGOODSLOST: "经销丢件",
              GOODSLOST: "丢件"
            }[val] || "")
          // formatter(val){
          // 	return val == 'MISS' ? '少件' : val == 'LOST'?'丢件':val == 'NULL'?'':val
          // }
        } /*, {
					label: '异常商品金额',
					width:120,
					prop: 'exception_amount'
				}*/,
		{
			label: "存货类别",
			prop: "inventory_category"
		},
		{
			label: "货物状态",
			prop: "shipment_status"
		},
		{
			label: "批次单号",
			prop: "batch_trade_no",
			width: 180
		  },
		{
			label: "快递单号",
			prop: "track_no",
			width: 180
		},
        {
          label: "业务员",
          prop: "staff_name"
        },
        {
          label: "业务员分组",
          prop: "staff_group"
        },
        {
          label: "是否为特裁商品",
          prop: "if_special_cut",
          width: 100,
          formatter(val) {
            return val == "Y" ? "是" : val == "N" ? "否" : val;
          }
        },
        {
          label: "是否停产",
          prop: "if_stop_produce",
          formatter(val) {
            return val == "Y" ? "是" : val == "N" ? "否" : val;
          }
        },
        {
          label: "物料规格描述",
          width: 200,
          prop: "material_desc"
        },
        {
          label: "单位",
          prop: "units"
        },
        {
          label: "数量",
          bool: true,
          isInput: true,
          isThis: true,
          conditionDisabled: "id",
          prop: "number"
        },
        {
          label: "包件数",
          width: 60,
          prop: "packet_count" //
        },
        {
          label: "退货描述",
          prop: "reason", //
          width: 200,
          bool: true,
          isThis: true,
          conditionDisabled: "isNotEdit",
          isInput: true
        },
        /*{
					label: '是否报废',//
					prop: 'if_scrap',
					bool:true,
					isThis:true,
					conditionDisabled:'isNotEdit',
					isSelect:true,
					obj:{
						N:'否',
						Y:'是'
					}
				}, */ {
          label: "问题描述",
          prop: "question_description",
          width: 200
        },
        {
          label: "备注信息",
          prop: "remark",
          width: 200,
          bool: true,
          isThis: true,
          conditionDisabled: "isNotEdit",
          isInput: true
        },
        {
          label: "批号",
          width: 180,
          prop: "branch_no"
        },
        {
          label: "供应商",
          width: 100,
          prop: "supplier_company"
        },
        {
          label: "商品BOM版本",
          width: 150,
          prop: "question_goods_bom_version" //
        },
        {
          label: "物料BOM版本",
          width: 150,
          prop: "bom_version"
        },
        {
          label: "规格描述",
          width: 200,
          prop: "description"
        } /*, {
					label: '客户',
					width:150,
					prop: 'custom_name'
				}*/,
        {
          label: "客服店铺",
          prop: "user_shop_name"
        },
        {
          label: "原始店铺",
          prop: "original_shop_name"
        },
        {
          label: "退款状态",
          prop: "refund_status",
          formatter: prop =>
            ({
              UN_REFUND: "未退款",
              PART_REFUND: "部分退款",
              ALL_REFUND: "已退款"
            }[prop] || prop)
        },
        {
          label: "退款操作",
          prop: "refund_operation",
          formatter: prop =>
            ({
              NONREFUNDABLE: "不可退款",
              TRANSFERABLE: "可结转",
              REFUNDABLE: "可退款"
            }[prop] || prop)
        },
        {
          label: "是否赠品",
          prop: "if_gift",
          formatter: prop =>
            ({
              1: "是",
              0: "否"
            }[prop] || prop)
        },
        {
          label: "拉货状态",
          prop: "cargo_status",
          formatter: prop =>
            ({
              NONEED: "无需",
              WAIT: "等待",
              SENDING: "发送中",
              SENDED: "已发送",
              RETRACTING: "撤回中",
              RETRACTED: "已撤回",
              END: "终止",
              FINISH: "完成"
            }[prop] || prop)
        },
        {
          label: "入库状态",
          prop: "warehouse_status",
          formatter: prop =>
            ({
              NONEED: "无需",
              WAIT: "等待",
              SENDING: "发送中",
              SENDED: "已发送",
              RETRACTING: "撤回中",
              RETRACTED: "已撤回",
              END: "终止",
              FINISH: "完成"
            }[prop] || prop)
        },
        {
          label: "行状态",
          prop: "row_status",
          formatter: prop =>
            ({
              WAIT: "等待",
              AUDIT: "审核",
              PULLING: "拉货中",
              PULLED: "已拉货",
              INBOUNDING: "入库中",
              INBOUNDED: "已入库",
              CLOSE: "关闭",
              END: "终止"
            }[prop] || prop)
        } /*, {
					label: '买家昵称',
					prop: 'custom_name'
				}*/,
        {
          label: "退货费用",
          prop: "returns_delivery_fee"
        },
        {
          label: "取货费用",
          prop: "fetch_goods_fee"
        },
        {
          label: "BOM体积",
          width: 80,
          prop: "volume" //
        } /*, {
					label: '是否启用条码',
					prop: 'if_user_barcode',
					width:100,
					format:'yesOrNo'
				}*/,
        {
          label: "是否安装",
          prop: "if_installed",
          bool: true,
          isThis: true,
          conditionDisabled: "isNotEdit",
          isSelect: true,
          obj: {
            N: "否",
            Y: "是"
          }
        } /*, {
					label: '仓储回传量',

					prop: 'passback_count'
				}*/ /*, {
					label: '货损备注',
					width:200,
					prop: 'damage_remark'
				}*/,
        {
          label: "实际运费",
          prop: "actual_delivery_fee"
        },
        {
          label: "拉货到货时间",
					prop: "pickup_time",
					width: 85,
          format: "dataFormat1"
        },
        {
          label: "实际到货时间",
          prop: "actual_reach_time",
					width: 85,
          format: "dataFormat1"
        },
        {
          label: "服务单号",
          width: 150,
          prop: "bill_service_no"
		},
		{
			label: "三包供应商名称",
			width: 150,
			prop: "three_supplier"
		  },
		  {
			label: "三包商编码",
			width: 150,
			prop: "three_supplier_code"
		  },
        {
          label: "入库单号",
          width: 150,
          prop: "bill_godownenty_no",
          initID: "initID_JS"
        },
        {
          label: "应收单号",
          width: 150,
          prop: "bill_receivables_no"
        },{
          label:'经销采购退货申请状态',
					prop: 'pur_dealer_status',
					width: 130,
          formatter(val) {
            switch (val) {
							case "WAIT": return "未申请"; break;
							case "WAITAUDIT": return "待审核"; break;
							case "REJECT": return "驳回"; break;
							case "AUDIT": return "已审核"; break;
							case "DEPOSITTIN": return "充值中"; break;
							case "DEPOSITED": return "已充值"; break;
							case "CANCLE": return "已取消"; break;
              default: return val; break;
            }
          }
        },{
					label:'是否生成了经销采购退货',
					prop: 'if_do_pur',
					width: 145,
					formatter(val) {
            switch (val) {
							case "Y": return "是"; break;
							case "N": return "否"; break;
              default: return val; break;
            }
          }
				},
        {
          label: "销售退货单号",
          width: 150,
          prop: "bill_salesreturn_no"
        },
        // {
        //   label: "是否可再售",
        //   width: 150,
        //   prop: "new_resale",
        //   formatter(val) {
        //     switch (val) {
        //       case "Y":
        //         return "是";
        //         break;
        //       case "N":
        //         return "否";
        //         break;
        //       case "D":
        //         return "处理中";
        //       default:
        //         return val;
        //         break;
        //     }
        //   }
        // },
        // {
        //   label: "是否再售",
        //   width: 110,
        //   prop: "isIt_onSale",
        //   formatter(val) {
        //     switch (val) {
        //       case "Y":
        //         return "是";
        //         break;
        //       case "N":
        //         return "否";
        //         break;
        //       case "D":
        //         return "处理中";
        //       default:
        //         return val;
        //         break;
        //     }
        //   }
        // },
        // {
        //   label: "是否再发",
        //   width: 110,
        //   prop: "if_re_shipment",
        //   formatter(val) {
        //     switch (val) {
        //       case "Y":
        //         return "是";
        //         break;
        //       case "N":
        //         return "否";
        //         break;
        //       case "D":
        //         return "处理中";
        //         break;
        //       default:
        //         return val;
        //         break;
        //     }
        //   }
        // },
        // {
        //   label: "再发销售退货单号",
        //   width: 150,
        //   prop: "bill_salesreturn_repalce_no"
        // },
        {
          label: "来源单号",
          width: 150,
          prop: "billNo"
        },{
			label: "下推财务中台销售退货单时间",
			width: 150,
			prop: "salereturn_send_time",
			format: "dataFormat1"
		},{
			label: "下推财务中台应收单时间",
          format: "dataFormat1",
		  width: 150,
			prop: "receivables_send_time"
		  }, /*,{
					label: '直接调拔单',
					width:200,
					prop: 'bill_adjustment_no'
				}*/
      ];
			if (this.ifPurchaseDealer) {
				colData = colData.reduce((arr, item) => {
					if (item.prop != 'supplier_company') {
						arr.push(item)
					}
					return arr
				}, [])

			}
			this.goodsCols = colData;

		},
		checkIsSameReturnsType (cb){
			if(
				this.selectGoods.some(obj => {
					if(obj.shipping_method !== this.selectGoods[0].shipping_method){
						return true
					}
					//仓库为非执鼎的客户自返，不能操作
					/*if(obj.shipping_method == 'CUSTOMER' && obj.if_deposit=='N'){
						return true
					}*/
				})
			){
				this.$message.error('返货类型不一致')
			}else {
				cb()
			}
		},

		/**
		*添加商品明细
		**/
		addGoods(){

			var list = [];
			this.selectGoods.map((a,b)=>{
				//已关闭的行不能再进行变更
				if(a.id ){
					let copyA = JSON.parse(JSON.stringify(a));
					let question_sub_id = copyA.question_sub_id;
					copyA.isReturnGoodsOfBill = true;
					//ID和question_sub_id互换
					copyA.question_sub_id = copyA.id;
					copyA.id = question_sub_id;

					list.push(copyA);
					/*list.push(a);*/
				}
			});
			if(!list.length){
				this.$message.error('请选择原有的明细行进行操作');
				return;
			}
			//添加一个参数，退货跟踪单过去的
			let params = {
				questionList:list,
				info:{isReturnGoods:true,merge_trade_id:this.form.merge_trade_id},
				callback: d => {
					this.$root.eventHandle.$emit('openDialog', {
	    				ok: () => {
	    					var newReturnGoods = d.data.map(o => Object.assign({}, this.getGoodsOfSave(this.selectGoods)[0], {
								id: null,
								source_row_id: this.selectGoods[0].id,
								material_code: o.bom_material_num,
								material_desc: o.bom_material_desc,
								material_id: o.bom_material_id,
								material_name: o.bom_material_name,
								bom_version: o.bom_version,
								volume: o.volume,
								unit_number: o.materialUnit,
								units: o.bom_version_unit,
								inventory_category: o.inventoryCategory,

								operition_type: 'GOODSRETURNS',
							}))

	    					this.ajax.postStream(
	    						'/afterSale-web/api/aftersale/plan/getFee',//更新退货费用，参考新退换货方案做法
	    						{
	    							street: this.form.street,
	    							area: this.form.area,
	    							city: this.form.city,
	    							province: this.form.province,
	    							logistics_supplier_class: this.form.logistics_supplier_class,
	    							list_change: [],
	    							list_return: newReturnGoods.map(o => ({
	    								material_id : o.material_id,
										question_sub_id : o.question_sub_id,
	    							})),
	    						},
	    						res => {
	    							if(!res.body.result){
		    							this.$message.error(res.body.msg)
		    							res.body.content = {}
		    						}

	    							var materialIdAndQuestionSubIdMapDeliveryFee = (res.body.content.return_material || []).reduce((a, b) => {
										a[b.material_id + '_' + b.question_sub_id] = {
											delivery_fee: b.delivery_fee,
											fetch_goods_fee: b.fetch_goods_fee,
										}
										return a
	    							}, {})

	    							newReturnGoods.forEach(obj => {
	    								var newFeeObj = materialIdAndQuestionSubIdMapDeliveryFee[obj.material_id + '_' + obj.question_sub_id] || {}

										obj.returns_delivery_fee = newFeeObj.delivery_fee || null/*重置费用数据*/
										obj.fetch_goods_fee = newFeeObj.fetch_goods_fee || null/*重置费用数据*/
	    							})

			    					this.ajax.postStream('/afterSale-web/api/aftersale/bill/return/returnGooodsException?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM', [
										{
											operition_type: 'GOODSRETURNS',
											id: this.selectGoods[0].id,
										},
										...newReturnGoods
									], res => {
										if(res.body.result){
											this.$message.success(res.body.msg)
											d.removeAlert()
											this.getDetail()
											this.getOperateList()
											this.getScheduleList()
										}else {
											this.$message.error(res.body.msg)
										}
									})
								}
							)
	    				},
	    				okTxt: '是',
	    				no() {},
	    				noTxt: '否',
	    				txt: '修改为返货退件，只能退一部分包件回来哦，确认修改？',
	    				noCancelBtn: true,
	    			})
				}
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_solutions/bomgoods'),
				style:'width:80%;height:560px',
				title:'添加商品'
			});
		},
		// 确认收货
		confirmCollectGoods (){
			let bool = true;
			this.selectGoods.some(obj => {
				//仓库为执鼎的客户自返，不能操作
				if(obj.shipping_method == 'CUSTOMER' && obj.if_deposit=='Y'){
					bool = false;
					return;
				}
			})
			if(!bool){
				this.$message.error('4PL平台托管的客户自返请操作退货入库按钮');
				return;
			}

			this.$root.eventHandle.$emit('openDialog', {
				ok: () => this.goodsBtnsAction('confirmCollectGoods?permissionCode=RETURN_TRACKING_ORDER_CONFIRM_RECEIPT'),
				no() {},
				okTxt: '是',
				noTxt: '否',
				txt: '是否确认货已经到仓库了，可以直接入库？',
				noCancelBtn: true,
			})
		},
		/**
		*取消退货
		**/
		cancelReturn(){
			if(!/^(APPROVED|FINISH)$/.test(this.form.status) && this.selectGoods.some(obj => obj.row_status !== 'WAIT')){
				this.$message.error('未审核或完结前，选中的商品行都要行状态为等待')
				return
			}

			this.$root.eventHandle.$emit('openDialog', {
				ok: () => this.goodsBtnsAction('cancelReturnGoods?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM'),
				no() {},
				okTxt: '是',
				noTxt: '否',
				txt: '操作该功能直接取消退货哦，确认？？',
				noCancelBtn: true
			})
		},
		/**
		*添加价格
		**/
		priceChange(){
			let startModify = this.goodPriceText=='添加包件价格'?true:false;
			startModify?this.goodPriceText='确认修改':'';

			if(!startModify){
				this.$root.eventHandle.$emit('openDialog', {
					ok: () => {
						if(
							this.selectGoods.some(obj => {
								if(!(obj.act_price >= 0)){
									this.$message.error('序号' + (this.goodsList.indexOf(obj) + 1) + '实际售价必须≥0')
									return true
								}
							})
						){
							/*empty*/
						}else {
							this.goodsBtnsAction('priceChange?permissionCode=RETURN_TRACKING_ORDER_ADD_PACKAGE_PRICE', o => ({ act_price: o.act_price }), () => {
								this.goodPriceText = '添加包件价格'
								this.goodsCols.some(obj => {
									if(obj.prop === 'act_price'){
										obj.bool = false
										obj.elInput = false
										return true
									}
								})
							})
						}
					},
					okTxt: '是',
					noTxt: '否',
					no() {},
					cancel: () => {
						var oldGoods = this.getOriginalGoods()
						this.goodsList.forEach((obj, index) => {
							obj.act_price = oldGoods[index].act_price
						})
						this.goodPriceText = '添加包件价格'
						this.goodsCols.some(obj => {
							if(obj.prop === 'act_price'){
								obj.bool = false
								obj.elInput = false
								return true
							}
						})
					},
					txt: '确认更改包件价格？',
					// noCancelBtn: true,
				});
			}else{
				this.goodsCols.some(obj => {
					if(obj.prop === 'act_price'){
						obj.bool = true
						obj.elInput = true
						return true
					}
				})
			}
		},
		// 退货再发
		reShipment(cb){
			let postData  = [];
			this.selectGoods.forEach(item=>{
				postData.push(item.id);
			})
			this.ajax.postStream(
				"/afterSale-web/api/aftersale/bill/returnsResaleGoods/reShipmentNew?permissionCode=RE_SHIPMENT_ORDER",
				{ids:postData},
				res => {
				if (res.body.result) {
					this.$message.success(res.body.msg);
					this.getDetail();
					this.getOperateList();
					this.getScheduleList();
				} else {
					this.$message.error(res.body.msg);
				}
				}
			);
		},

		checkTypeChange(type,resolve){
			let postData  = [];
			this.selectGoods.forEach(item=>{
				postData.push(item.id);
			})
			if(type != 'RESALE'){
				resolve && resolve(true)

			}else{
				let flag = true;
				this.ajax.postStream(
					"/afterSale-web/api/aftersale/bill/return/checkTypeChange",
					postData,
					res => {
					if (res.body.result) {

					} else {
						flag = false;
						this.$message.error(res.body.msg);
					}
					resolve && resolve(flag)

				});
			}
		}
	},
	watch: {
		ifPurchaseDealer(newVal,oldVal) {
			if (newVal != oldVal) {
				this.setColData();
			}
		}
	}
}
