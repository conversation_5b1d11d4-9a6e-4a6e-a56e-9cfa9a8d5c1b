// 退换跟踪单详情--操作记录
export default {
	data() {
		let self = this
		return {
			operaterList: [],//单据操作记录
			jectList:[],//历史驳回记录
			//allOperaterList:[],//当前操作记录和历史驳回记录总合
			operaterCols: [
				{
					label: '用户',
					prop: 'operator_name',
					width:200,
					

				}, {
					label: '业务操作',
					prop: 'operate_type',
					width:100,
					formatter(val){
						return val == 'REJECT'?'驳回':val;
					}
					
				}, {
					label: '操作描述',
					prop: 'description',
					width:400,
					
				}, {
					label: '操作时间',
					width:200,
					prop: 'operate_time',
					format:'dataFormat1'
				}
			]
		}
	},

	methods:{
		/**
		*获取当前操作记录
		**/
		getOperateListOfCurrentBill(){
			var id = this.form.id;//
			if(!id) return;
			let url = '/afterSale-web/api/aftersale/bill/queryOperateLogByBillId';
			this.ajax.postStream(url,{after_bill_id:id},(res)=>{
				var data = res.body
				if(!data.result) {
					this.$message.error(data.msg);
					return;
				}
				//this.operaterList = data.content || [];
				let list = data.content || [];
				list.map((a,b)=>{
					this.operaterList.push(a);
				})
			})
		},
		/***
		*获取历史驳回记录
		**/
		getrejectList(callback){
			var id = this.form.id;
			if(!id) return;
			let url = '/afterSale-web/api/aftersale/bill/returns/rejectRecord/listByBillId';
			this.ajax.postStream(url,{after_bill_id:id},(res)=>{
				var data = res.body
				if(!data.result) {
					this.$message.error(data.msg);
					return;
				}
				let d = data.content.list || [];
				let list = [];
				d.map((a,b)=>{
					list.push({
						operator_name:a.rejector_name,
						operate_type:a.operate_type,
						description:a.description,
						operate_time:a.rejector_time
					})
				})
				this.operaterList = list;
				callback && callback();
			})
		},
		/**
		*获取历史与当前单据的操作记录
		**/
		getOperateList(){
			/*this.allOperaterList = [];
			this.getOperateListOfCurrentBill();
			this.getrejectList();*/
			this.getrejectList(()=>{
				this.getOperateListOfCurrentBill();
			})

		}
	},
	/*watch:{
		jectList:{
			handler:function(newVal,oldVal){

			},
			deep:true
		},
		operaterList:{
			handler:function(newVal,oldVal){

			},
			deep:true
		}
	},*/
	
	mounted(){
		this.getOperateList();
	}
}