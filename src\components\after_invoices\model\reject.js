// 退换跟踪单详情--驳回信息
export default {
	data() {
		let self = this;
		return {
			rejectBtns:[],
			rejectCols:[
				{
					label: '驳回人',
					prop: 'rejector_name'
				},
				{
					label: '用户',
					prop: 'creator_name'
				},
				{
					label: '驳回日期',
					prop: 'reject_time',
					width:150
				},
				{
					label: '驳回备注',
					width:300,
					prop: 'reject_remark',
					bool:true,
					isInput:'true'
				},
				{
					label: '业务备注',
					width:300,
					prop: 'biz_note',
					bool:true,
					isInput:'true'
				}
			],
			rejectList: [],
			//复选框选择的数据
			rejectSelectData:[]
		}
	},
	methods:{
		/**
		*选择
		*/
		rejectSelects(selections){
			this.rejectSelectData = selections.length?selections:[];
		},
		/**
		*新增行的but按钮信息
		**/
		getBtnInfoOfAdd(){
			return {
				type: 'primary',
				txt: '新增行',
				disabled : this.rejectBtnStatus,
				click: ()=>{
					this.addRejectInfo();
				}
			}
		},
		/**
		*新增行的but按钮信息
		**/
		getBtnInfoOfDel(){
			return {
				type: 'danger',
				txt: '删除行',
				disabled : this.rejectBtnStatus,
				click: ()=>{
					this.delRejectInfo();	
				}
			}
		},
		/**
		*获取所有的按钮
		**/
		getBtns(){
			var addBtn = this.getBtnInfoOfAdd();
			var delBtn = this.getBtnInfoOfDel();
			this.rejectBtns = [];
			this.rejectBtns.push(addBtn);
			this.rejectBtns.push(delBtn);
		},
		/*
		*驳回信息明细行的对像信息
		**/
		getRejectObject(){
			return {
				id:null,
				after_bill_id:null,
				rejector:null,
				rejector_name:null,
				reject_time:null,
				reject_remark:null,
				biz_note:null,
				creator:null,
				creator_name:null,
				create_time:null
			}
		},
		/**
		*新增行
		*/
		addRejectInfo(){
			var obj = this.getRejectObject();
			this.rejectList.push(obj);
		},
		/**
		*删除行
		**/
		delRejectInfo(){
			var data = this.rejectSelectData;
			if(!data || !data.length){
				this.$message.error('请选择行');
				return;
			}
			data.map((a,b)=>{
				if(data.id || !a) return;
				this.rejectList.map((c,d)=>{
					if(JSON.stringify(a) == JSON.stringify(c)){
						this.rejectList.splice(d,1);
						return;
					}
				})
			})
		},
		/**
		*获取驳回信息列表
		**/
		getRejectList(){
			var id = this.form.id;
			if(!id) return;
			let url = '/afterSale-web/api/aftersale/bill/queryRejectLogByBillId';
			this.ajax.postStream(url,{after_bill_id:id},(res)=>{
				var data = res.body
				if(!data.result) {
					this.$message.error(data.msg);
					return;
				}
				this.rejectList = data.content || [];
			})
		}

	},
	/*watch:{
		'form.id':function(newval,oldval){
			this.getRejectList();
		}
	},*/
	mounted() {
		this.getBtns();
		this.getRejectList();
	}
}