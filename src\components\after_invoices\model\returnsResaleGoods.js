// 退换跟踪单详情--再售再发信息
export default {
	data() {
	let self = this;
	return {
			returnsResaleGoods: [],
			returnsResaleGoodsBtns: [],
			returnsResaleGoodsCols: [
				{
					label: '物料编码',
					prop: 'material_code'
				}, {
					label: '实际售价',
					prop: 'act_price',
				}, {
					label: '是否可再售',
					prop: "new_resale",
          formatter(val) {
            switch (val) {
              case "Y":
              case 'Z':
                return "是";
              case "N":
                return "否";
              case "D":
                return "处理中";

              default:
                return val;
            }
          }
				}, {
					label: '销售退货单号',
					prop: 'bill_salesreturn_no'
				}, {
					label: '是否已锁库',
					prop: 'if_lock_library',
					format:'yesOrNo'
				}, {
					label: '再售客户提货状态',
					prop: 'pickup_status',
          formatter: (val) => {
            let status = {
							Delivering:'提货中',
							Delivered:'已提货',
							Assembled:'已组装',
							Completed:'已完结',
						}
						return status[val] || val;
          },
				}, {
					label: '再售客户提货时间',
					format:'dataFormat1',
					prop: 'pickip_time'
				}, {
					label: '再售客户',
					prop: 'resale_cust_name'
				}, {
					label: '再售折扣',
					prop: 'sales_discount'
				}, {
					label: '再售新旧模式',
					prop: 'newAndOld_modes',
          formatter(val) {
            switch (val) {
              case "New":
                return "新版";
              case "Old":
                return "旧版";
              default:
                return val;
            }
          }
				}, {
					label: '再售服务类型',
					prop: 'sales_service_type',
          formatter(val) {
            switch (val) {
              case "HZWLD":
                return "货在物流点";
              case "CARGO_NEW":
                return "上门拉货";
              default:
                return val;
            }
          }
				}, {
					label: '再售实际售价',
					prop: 'resale_act_price'
				}, {
					label: '是否再售',
					prop: 'isIt_onSale',
					formatter(val) {
            switch (val) {
              case "Y":
                return "是";
              case "N":
                return "否";
              case "D":
                return "处理中";
              default:
                return val;
            }
          }
				}, {
					label: '再售销售出库单',
					prop: 'resale_sales_outbound_no'
				}, {
					label: '是否再发',
					prop: 'if_re_shipment',
					formatter(val) {
            switch (val) {
              case "Y":
                return "是";
              case "N":
                return "否";
              case "D":
                return "处理中";
              default:
                return val;
            }
          }
				}, {
					label: '再发实际售价',
					prop: 'reShipment_act_price'
				}, {
					label: '再发销售出库单',
					prop: 'reShipment_sales_outbound_no'
				}
			]
		}
	},
	methods:{
    processData(content) {
      if (Array.isArray(content) && Array.isArray(this.goodsList)) {
        content.forEach((row) => {
          const target = this.goodsList.find(item => item.id === row.bill_return_goods_id);
          if (target) {
            row.material_code = target.material_code;
            row.act_price = target.act_price;
            row.new_resale = target.new_resale;
            row.bill_salesreturn_no = target.bill_salesreturn_no;
            row.isIt_onSale = target.isIt_onSale;
            row.if_lock_library = target.if_lock_library;
            row.if_re_shipment = target.if_re_shipment;
          }
        });
      }
    },
		getReturnsResaleGoods(){
			var id = this.form.id;
			let url = '/afterSale-web/api/aftersale/bill/returnsResaleGoods/getList';
			this.ajax.postStream(url,id,(res)=>{
				var data = res.body;
				if(!data.result) {
					this.$message.error(data.msg);
					return;
				}
        this.processData(data.content);
				this.returnsResaleGoods = data.content || [];
			})
		},
	},
}
