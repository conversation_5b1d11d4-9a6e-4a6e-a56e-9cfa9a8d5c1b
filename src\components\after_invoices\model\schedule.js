// 退换跟踪单详情--三包处理进度
export default {
	data() {
		return {
			scheduleList: [],
			scheduleCols: [
				{
					label: '处理人',
					prop: 'handle_user'
				}, {
					label: '处理时间',
					prop: 'handler_time',
					width:150,
					format:'dataFormat1'
				}, {
					label: '处理内容',

					prop: 'handle_content'
				}
			]
		}
	},
	methods:{
		getScheduleList(){
			console.log('看看三包处理进度');
			var bill_returns_no = this.form.bill_returns_no;
			if(!bill_returns_no) return;
			let url = '/afterSale-web/api/aftersale/bill/returns/queryThreeScheduleByBillNo';
			this.ajax.postStream(url,{bill_returns_no:bill_returns_no},(res)=>{
				var data = res.body
				if(!data.result) {
					this.$message.error(data.msg);
					return;
				}
				//this.scheduleList = [{handler_time:1517655920000}];
				this.scheduleList = data.content || [];
			})
		},
	},
	/*watch:{
		'form.id':function(newval,oldval){
			this.getScheduleList();
		}
	},*/
	/*mounted() {
	
		this.getScheduleList();
	}*/
}