<!-- 退货再售客户设置 -->
<template>
	<div>
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type='primary' size='mini' @click="preSave('submitData')" :loading="isLoading" :disabled='isLoading'>保存</el-button >
			</el-col>
		</el-row>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="退货再售客户设置" name="orderInfo">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="submitData" :rules="rules" ref="submitData">
					
						<el-col :span="12">
							<el-form-item label="省"  prop="province_id">
								<el-select v-model="submitData.province_id" size="mini" style="width:150px;" @change="changeAddress(submitData.province_id,1)" :disabled="addressDisabled">
									<el-option v-for="(value, key) in province" :label="value" :value="parseInt(key)" :key="key" ></el-option>
								</el-select>
								<el-tooltip v-if='rules.province_id[0].isShow' class="item" effect="dark" :content="rules.province_id[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="市"  prop="city_id">
								<el-select v-model="submitData.city_id" size="mini" style="width:150px;" @change="changeAddress(submitData.city_id,2)" :disabled="addressDisabled">
									<el-option v-for="(value, key) in city" :label="value" :value="parseInt(key)" :key="key"   ></el-option>
								</el-select>
								<el-tooltip v-if='rules.city_id[0].isShow' class="item" effect="dark" :content="rules.city_id[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="区"  prop="area_id">
								<el-select v-model="submitData.area_id" size="mini" style="width:150px;" @change="changeAddress(submitData.area_id,3)" :disabled="addressDisabled">
									<el-option v-for="(value, key) in area" :label="value" :value="parseInt(key)" :key="key" ></el-option>
								</el-select>
								<el-tooltip v-if='rules.area_id[0].isShow' class="item" effect="dark" :content="rules.area_id[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>

						<el-col :span='12'>
							
						<el-form-item label="再售客户"  prop="customer">
                              <xpt-input v-model='submitData.customer'  size='mini'  icon="search"  :on-icon-click="selectSbsupplier" readonly placeholder="请选择客户" @change='changeSbsupplier'></xpt-input>
								<el-tooltip v-if='rules.customer[0].isShow' class="item" effect="dark" :content="rules.customer[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="再售折扣"  prop="sales_discount">
                                <el-input type="text" size="mini" v-model="submitData.sales_discount" :maxlength="4"></el-input>
								<el-tooltip v-if='rules.sales_discount[0].isShow' class="item" effect="dark" :content="rules.sales_discount[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>	
						
			    </el-form>
			  </el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js';
	import Fn from '@common/Fn.js';
	export default {
		props:["params"],
		data() {
			var _this = this;
			var self = this;
			return{
				firstTab:"orderInfo",
				bill_type_id_tag: '',
				provinceObj:{},
				cityObj:{},
				areaObj:{},
				streetObj:{},
				submitData:{
					province:"",//省id
					city:'',//市id
					areas:'',//区id
					customer_no:'',
					customer:'',
					province_id:'',
					city_id:'',
					area_id:'',
                    sales_discount:''
				},
				rules:{
				
					
					
					province_id:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					city_id:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					area_id:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					customer:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入客户",
						trigger:"change"
					}),
                    sales_discount:validate.discount({
						required:true,
						self:self,
						msg:"请输入折扣",
						trigger:"change"
					}),
					
				},
				province: {},//省所有选项
				city: {},//市
				area: {},//区
				addressDisabled:false,
				initialData:{},
				isLoading:false,
				businessTypeTradeDisabledOption: [],
				DDYWLX_AUX: __AUX.get('ddywlx').reduce((a, b) => {
					a[b.code] = b.tag
					return a
				}, {}),
			}
		},
		methods : {
			changeSbsupplier(){
				let self = this;
				self.submitData.customer_no = '';
				self.submitData.customer = '';
			},

			selectSbsupplier(){
				let params = {},
                self = this;
				params.ifResale = true;
				params.close= d => {
							self.submitData.customer = d.name;
							self.submitData.customer_no = d.number;
						}
				
				self.$root.eventHandle.$emit("alert", {
					params: params,
					component: () => import('@components/customers/list'),
					style: "width:800px;height:500px",
					title: "客户列表",
				});
			},
			
			preSave(formName){
				var self = this;
				// if(this.submitData.bill_type_id=='PRESENT' && !this.submitData.arm_mager_id){
				// 	this.$message.error('赠品订单请选择合并订单号');
				// 	return;
				// }	
				self.$refs[formName].validate((valid) => {
					if(!valid) return

					this.isLoading=true
					self.save()
				})
			},
			save(callback){	
				var self = this,
				params={};
				var url = "/afterSale-web/api/aftersale/resaleCustomer/save";
				
				let saveData = JSON.parse(JSON.stringify(this.submitData));
				this.ajax.postStream(url,saveData,(response)=>{
					if(response.body.result){
						this.$message({message: '操作成功',type: 'success'})
						this.params.callback();
						this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
					}else{
						this.$message.error(response.body.msg)
					}
					this.isLoading=false
				},e=>{
					this.isLoading=false
					this.$message.error(e)
				})
			},
			
			/*要查询下游地址code,
			*type==1为city,2为area,3为street
			**/
			changeAddress(code,type){
                let typeArray=['city','area','street']
				var key = typeArray[type-1]
				if(!code) return;
				this.getAddress(code,(data)=>{
					console.log(data);
					this[key] = '';
					this[key] = data || {};
					// var value = this.submitData[key+'_id'];
					if(!data || !data.hasOwnProperty(this.submitData[key+'_id'])){
						if(type == 1){
							this.submitData.city_id = null;
							this.submitData.area_id = null;
						}else if(type == 2){
							this.submitData.area_id = null;
						}
					}
				});
				// 插入省市区名称
				this.submitData.province = this.province[this.submitData.province_id]
				this.submitData.city = this.city[this.submitData.city_id]
				this.submitData.areas = this.area[this.submitData.area_id]
			},
			
			
		},
		mounted:function(){
			var self = this;
			this.getAddress((data)=>{
				// this.province = '';
				this.province = data;
			});
			self.params.__close = self.closeTab
		}
	}
</script>