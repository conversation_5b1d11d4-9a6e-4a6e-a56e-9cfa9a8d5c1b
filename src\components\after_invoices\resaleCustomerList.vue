<!-- 退货再售客户设置列表 -->
<template>
	<xpt-list-dynamic
		:data='list'
		:btns='btns'
		:colData='col'
		:pageTotal='count'
		selection='radio'
		:searchPage='search.page_name'
		@radio-change='select'
		@search-click='searchClick'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
		ref='xptList'
	>
         <el-button
            slot="btns"
            size="mini"
            type="info"
            @click="downloadTemplate()"
            :disabled="false"
            >模板下载</el-button
        >
        <xpt-upload-v3
            slot="btns"
            uploadBtnText="导入"
            :uploadSize="20"
            acceptTypeStr=".xlsx,.xls"
            :dataObj="uploadDataObj"
            :disabled="false"
            :ifMultiple="false"
            :showSuccessMsg="false"
            @uploadSuccess="uploadSuccess"
            btnType="success"
            style="display: inline-block; margin: 0px 10px"
        ></xpt-upload-v3>
        <el-button
            slot="btns"
            size="mini"
            type="warning"
            @click="showUploadResult()"
            :disabled="false"
            >导入结果</el-button
        >
	</xpt-list-dynamic>
</template>
<script>
export default {
	props: ['params'],
	data() {
		let self = this
		return {
			list: [],
			selectedData:{},//选择的数据
			deleLoding:false,
			refreshLoding:false,
			deleDisabled:false,
			refreshDisabled:false,
			btns: [{
				type: 'primary',
				txt: '刷新',
				disabled:false,
				loading:false,
				click: ()=>{
					self.refresh();
				}
			},{
				type: 'primary',
				txt: '新增',

				click: ()=>{
					self.add();
				}
			},{
				type: 'success',
				txt: '生效',

				click: ()=>{
					self.changeStatus(1);
				}
			},{
				type: 'danger',
				txt: '失效',

				click: ()=>{
					self.changeStatus(0);
				}
			}],
			col: [
				{
					label: '省',
					prop: 'province',
					width:'150',
				},{
					label: '市',
					prop: 'city',
					width:'180',
				}, {
					label: '区 ',
					width:'120',
					prop: 'areas'
				},{
					label: '再售客户编码',
					width:'80',
					prop: 'customer_no',
				},{
					label: '再售客户名称 ',
					width:'100',
					prop: 'customer',
				},
                {
                    prop: "sales_discount",
                    label: "再售折扣",
                    width:'100',
                },{
					label: '生效状态 ',
					width:'100',
					prop: 'status',
					formatter(val){
						return val == 1?'生效':'失效';
					}
				},{
					label: '失效人',
					width:'100',
					prop: 'disable_name'
				},{
					label: '失效时间',
                    prop: 'disable_time',
                    format:'dataFormat1'
				},{
					label: '创建人',
					width:'80',
					prop: 'create_name'
				},{
					label: '创建时间',
					width:'80',
                    prop: 'create_time',
                    format:'dataFormat1'
				}, {
					label: '修改人',
					width:'150',
					prop: 'modify_name'
				}, {
                    label:'修改时间',
                    prop: 'modify_time',
                    format:'dataFormat1'
                }
               
			],

			
			search: {
	          page_no: 1,
	          page_size: self.pageSize,
	          page_name: 'aftersale_bill_resale_customers',
	          where: []
	        },
		
			count: 0,
            uploadDataObj: {
                parent_name: "退货再售客户列表",
                parent_no: `RESALE_CUSTOMER_LIST`, //主要通过该参数获取附件列表
                child_name: null,
                child_no: null,
                content: {},
            },
		}
	},
	methods: {
		changeStatus(status){
			if(!this.selectedData){
				this.$message.error('请选择退货再售客户');
				return;
			}
			let url = '/afterSale-web/api/aftersale/resaleCustomer/inable';
			if(!status){
				url = '/afterSale-web/api/aftersale/resaleCustomer/disable'
			}
            this.ajax.postStream(url, {id:this.selectedData.id}, res => {
				if(res.body.result){
                    this.$message.success(res.body.msg)
                    
				}else {
					this.$message.error(res.body.msg)
                }
                this.getList()
			});

		},
		
     
		searchClick(where,reslove) {
			this.search.where = where
			this.getList(true,reslove)
		},
		pageSizeChange(ps) {
			this.search.page_size = ps
			this.getList()
		},
		pageChange(page) {
			this.search.page_no = page
			this.getList()
		},
		add() {
			// let params = JSON.parse(JSON.stringify(row))
			// delete params.merge_trade_id
			let self = this;
			this.$root.eventHandle.$emit('alert', {
				title: '新增退货再售客户',
				params: {
					callback(){
						self.getList();
					}
				},
				style:'width:750px;height:300px',
				component: () => import('@components/after_invoices/resaleCustomerDetail')
			})
		},
	
		/***
		*重新刷新数据
		**/
		refresh(){
			//this.setLoadingOfBtn(1,!0);
			this.refreshDisabled = true;
			this.refreshLoding = true;
			this.getList();
		},
		/**
		*获取列表数据
		**/
		getList(bool,callback){

			let self = this;
			let url = '/afterSale-web/api/aftersale/resaleCustomer/list'
			this.ajax.postStream(url,this.search, res => {
				
				this.selectedData = [];
				if(res.body.result && res.body.content) {
					

					this.list = res.body.content.list || [];
					this.count = res.body.content.count;
					self.$refs.xptList.$refs.list.clearRadioSelect();
				}
				if(!res.body.result && bool){
					this.$message({
						type:'error',
						message:res.body.msg
					});
				}
				callback && callback();

			}, null, this.params.tabName)
		},
		/**
		*所有按钮恢复原来的设置
		*/
		setAllBtn(){
			this.refreshDisabled = false;
			this.refreshLoding = false;
			this.deleLoding = false;
			this.deleDisabled = false;
		},
		/**
		*删除
		**/
		del(){
			var data = this.selectedData;
			if(!data || !data.length){
				this.$message.error('请选择要删除的行数据');
				return;
			}
			this.deleLoding = true;
			this.deleDisabled = true;
			//this.setLoadingOfBtn(0,!0);
			var deleteList = [];
			var canNotDelete = '';
			data.map((a,b)=>{
				//TODO,条件判断的删除
				let id = a.id;
				deleteList.push(id);
			});
			if(canNotDelete){
				this.$message.error(canNotDelete.question_goods_code + '不能删除');
				return;
			}
			deleteList = Array.from(new Set(deleteList));
			let url = '/afterSale-web/api/aftersale/bill/returns/delete?permissionCode=RETURN_TRACKING_ORDER_DELETE'
			this.ajax.postStream(url/*'/afterSale-web/api/aftersale/bill/returns/delete?permissionCode=RETURN_TRACKING_ORDER_DELETE'*/,deleteList,res=>{
				let d = res.body;
				this.deleLoding = false;
				this.deleDisabled = false;
				this.$message({
					type:d.result?'success':'error',
					message:d.msg
				});
				if(!d.result) return;
				this.getList();

			});

		},
		
		/**
		*选择
		**/
		select(selects){
			this.selectedData = selects;
		},
         //上传成功返回结果
        uploadSuccess(result) {
            if (result.length > 0 && !!result[0].path) {
                this.importFileUrl(result[0].path);
            }
        },
        //模板下载
        downloadTemplate() {
            let url =
                "http://lsmy-devfile.oss-cn-shenzhen.aliyuncs.com/doc/2021-05-13ffb25290648c4d2cbc8ca14d04e6db46.xlsx";
            let filename = "退货再售客户设置导入模板";
            this.download(url, filename);
        },
        //命名转换中文
        download(url, filename) {
            if (!fetch) {
                window.location.href = url;
                return;
            }
            return fetch(url).then((res) => {
                console.log(res);
                res.blob().then((blob) => {
                    let a = document.createElement("a");
                    let url = window.URL.createObjectURL(blob);
                    a.href = url;
                    a.download = filename;
                    a.click();
                    window.URL.revokeObjectURL(url);
                });
            });
        },
        //导入
        importFileUrl(fileUrl) {
            let params = {
                fileUrl: fileUrl,
            };
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/resaleCustomer/importResaleCustomer",
                params,
                (res) => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg);
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
        //导入结果
        showUploadResult() {
            this.$root.eventHandle.$emit("alert", {
                style: "width:900px;height:600px",
                title: "导入结果",
                params: {
                    url: "/afterSale-web/api/aftersale/resaleCustomer/import/list",
                    data: {},
                    showDownload: true,
                },
                component: () => import("@components/common/eximport"),
            });
        },
	},
	created () {
	
	},
	mounted() {
		this.getList();
	},
}
</script>
