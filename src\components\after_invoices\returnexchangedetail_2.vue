<!-- 退换跟踪单详情2 -->
<template>
	<div class='xpt-flex'>
		<el-row class='xpt-top' :gutter="40">
			<el-col :span="18">
				<el-button type='primary' size='mini' :loading="lockLoding" :disabled="lockDisabled || btnStatus" @click="lockOrDeblockEvent(1)">锁定</el-button>
				<el-button type='primary' size='mini' :loading="deblockLoding" :disabled="deblockDisabled || btnStatus" @click="lockOrDeblockEvent(2)">解锁</el-button>
				<el-button type='danger' size='mini' :loading="reauditLoding" :disabled="reauditDisabled" @click="reauditEvent">驳回</el-button>
				<el-button type='primary' size='mini' :loading="saveLoding" :disabled="saveDisabled" @click="saveEvent()">保存</el-button>
				<el-button type='primary' size='mini' :loading="submitLoding" :disabled="submitDisabled || btnStatus" @click="submitOrRecallEvent(1)">提交</el-button>
				<el-button type='primary' size='mini' :loading="recallLoding" :disabled="recallDisabled || btnStatus" @click="submitOrRecallEvent(2)">撤回</el-button>
				<el-button type='primary' size='mini' :loading="auditLoding" :disabled="auditDisabled || btnStatus" @click="auditEvent">审核</el-button>
				<el-button type='primary' size='mini' :loading="refreshLoding" :disabled="refreshDisabled" @click="refreshEvent">刷新</el-button>

				<el-button type="primary" size="mini" :loading="upFileLoding"  @click='uploadFun' :disabled="upFileDisabled">上传附件<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload></el-button>

				<el-button type='primary' size='mini' @click='pictureFun'>查看附件</el-button>
				<el-button type='primary' size='mini' :loading="reSendLoading" @click='reSendWarehouse' :disabled="reSendDisabled">4PL入库单重推</el-button>
			</el-col>
			<el-col :span="6" style="text-align: right;">
				<el-button type="primary" size="mini" @click="nextOrPrevOrder('prev')" :disabled="!params.orderList">上一页</el-button>
				<el-button type="primary" size="mini" @click="nextOrPrevOrder('next')" :disabled="!params.orderList">下一页</el-button>
			</el-col>
		</el-row>
		<div>

			<el-form :model='form' :rules='rules' ref='form' label-width='120px'>
				<el-tabs v-model="topTabs">
					<el-tab-pane label="基本信息" name="first">
						<el-row :gutter='40'>
							<el-col :span='6'>
								<el-form-item label='单据编号'>
									<el-input size='mini' v-model='form.bill_returns_no' disabled></el-input>
								</el-form-item>
								<el-form-item label='合并单号'>
									<el-input size='mini' v-model='form.merge_trade_no' disabled></el-input>
								</el-form-item>
								<el-form-item label='售后单号'>
									<a href="javascript:;" @click="toAfterOrderDetail(form.after_order_id)">{{ form.after_order_no }}</a>
									<!-- <el-input size='mini' v-model='form.after_order_no' disabled @click.native="toAfterOrderDetail(form.after_order_id)"></el-input> -->
								</el-form-item>
								<el-form-item label='买家昵称'>
									<el-input size='mini' v-model='form.buyer_name' disabled></el-input>
								</el-form-item>
								<el-form-item label='结算方式'>
									<!-- <el-input size='mini' v-model='form.settle_method' disabled></el-input> -->
									<el-select
									v-model="form.settle_method"
									size="mini"
									placeholder="请选择"
									disabled
									class="js_pmm"
									>
										<el-option key="WANGPAI_DEALER" label="经销网拍" value="WANGPAI_DEALER"></el-option>
										<el-option key="DEALER" label="经销" value="DEALER"></el-option>
										<el-option key="OTHER" label="其它" value="OTHER"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span='6'>
								<el-form-item label='物流运输商'>
									<el-input size='mini' v-model='form.logistics_company_name' disabled></el-input>
									<!-- <el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="form.logistics_company_name"  v-else @click="searchLogisticsCompany" :disabled="saveDisabled"></el-input> -->
								</el-form-item>
								<el-form-item label="是否经销商订单">
									<el-switch on-text="是" off-text="否" :value="{'Y':true,'N':false,'':false,'null': false,}[form.if_dealer]"  disabled></el-switch>
								</el-form-item>
								<el-form-item label="经销商名称" v-show="!ifPurchaseDealer">
									<el-input size='mini' disabled :value="form.dealer_customer_name"></el-input>
								</el-form-item>
								<el-form-item label="经销商编码">
									<el-input size='mini' disabled :value="form.dealer_customer_number"></el-input>
								</el-form-item>
								<el-form-item label="结算主体" >
									<el-input size='mini'  disabled :value="form.settle_entity "></el-input>
								</el-form-item>
							</el-col>
							<el-col :span='6'>
								<el-form-item label='单据状态'>
									<el-input size='mini' v-model='status[form.status]' disabled></el-input>
								</el-form-item>
								<el-form-item label='实际运费'>
									<el-input size='mini' v-model='form.actual_delivery_fee' disabled></el-input>
								</el-form-item>
								<el-form-item label='运费方式'>
									<el-select  size='mini'  placeholder="类型" v-model='form.delivery_fee_pay_method' disabled>
										<el-option label="现付" value="NOW_PAY"></el-option>
										<el-option label="到付" value="PICK_PAY"></el-option>
									</el-select>
								</el-form-item>
								<el-form-item label='' style="margin-left:-50px">
									 <el-checkbox v-model='form.if_three_age' disabled> 是否三个月售后</el-checkbox>
								</el-form-item>

							</el-col>
							<el-col :span='6'>
								<el-form-item label='是否锁定'>
									<el-input size='mini' :value="form.locker ? '是' : '否'" disabled></el-input>
								</el-form-item>
								<!-- <el-form-item label='新退货再售'>
									<el-input size='mini' :value="form.new_resale=='Y' ? '是' : '否'" disabled></el-input>
								</el-form-item> -->
							</el-col>
						</el-row>
						<el-row :gutter='40'>
							<el-col :span='12'>
								<el-form-item label='业务备注' class="pmm_textareaInput">
									<el-input size='mini' type="textarea" :autosize="{ minRows: 1, maxRows: 2}" v-model='form.business_remark' disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span='12'>
								<el-form-item label='退货跟踪备注' class="pmm_textareaInput">
									<el-input size='mini' type="textarea" :autosize="{ minRows: 1, maxRows: 2}" v-model='form.return_remark' :disabled="submitDisabled"></el-input>
								</el-form-item>
							</el-col>
						</el-row>

					</el-tab-pane>
					<el-tab-pane label="其他信息" name="second">
						<el-row :gutter='40'>
							<el-col :span='8'>


								<!-- <el-form-item label='提货点' prop="delivery_name">
									<el-input size='mini' v-model='form.delivery_name' disabled></el-input>
									<el-tooltip v-if='rules.delivery_name[0].isShow' class="item" effect="dark" :content="rules.delivery_name[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item> -->
								<el-form-item label='提货点' >
									<el-input size='mini' v-model='form.delivery_name' disabled></el-input>
									<!-- <el-tooltip v-if='rules.delivery_name[0].isShow' class="item" effect="dark" :content="rules.delivery_name[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip> -->
								</el-form-item>

								<el-form-item label='三包点'>
									<el-input size='mini' v-model='form.three_name' disabled></el-input>
									<!-- <el-tooltip v-if='rules.three_name[0].isShow' class="item" effect="dark" :content="rules.three_name[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip> -->

								</el-form-item>

								<el-form-item label='线路区域' >
									<el-input size='mini' v-model='form.line_area' disabled></el-input>
									<!-- <el-tooltip v-if='rules.line_area[0].isShow' class="item" effect="dark" :content="rules.line_area[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip> -->

								</el-form-item>

								<el-form-item label='收货人' prop="receiver_name">
									<el-input size='mini'  v-model='form.receiver_name' disabled></el-input>
									<el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip>

								</el-form-item>
								<el-form-item label='电话'>
									<!-- <el-input size='mini' v-model='form.reveiver_phone' disabled ></el-input> -->
									<xpt-eye-switch v-model="form.reveiver_phone" :disabled="true" :aboutNumber="form.bill_returns_no"></xpt-eye-switch>
									<!-- <el-tooltip v-if='rules.reveiver_phone[0].isShow' class="item" effect="dark" :content="rules.reveiver_phone[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip> -->

								</el-form-item>


							</el-col>
							<el-col :span='8'>

								<el-form-item label='创建人'>
									<el-input size='mini' v-model='form.creator_name' disabled></el-input>
								</el-form-item>
								<el-form-item label='创建日期'>
									<el-date-picker v-model="form.create_time" type="date" placeholder="选择日期"  size='mini' disabled></el-date-picker>
								</el-form-item>
								<el-form-item label='创建人分组'>
									<el-input size='mini' v-model='form.creator_group_name' disabled></el-input>
								</el-form-item>
								<el-form-item label='创建大分组'>
									<el-input size='mini' v-model='form.creator_big_group_name' disabled></el-input>
								</el-form-item>
								<el-form-item label='业务锁定人'>
									<el-input size='mini'  disabled v-model='form.staff_locker_name'></el-input>
								</el-form-item>



							</el-col>
							<el-col :span='8'>

								<el-form-item label='业务锁定人分组'>
									<el-input size='mini' disabled v-model='form.staff_locker_group_name'></el-input>
								</el-form-item>
								<el-form-item label='退货组锁定人'>
									<el-input size='mini' disabled v-model='form.locker_name'></el-input>
								</el-form-item>
								<el-form-item label='退货组锁定人分组'>
									<el-input size='mini' disabled v-model='form.locker_group_name'></el-input>
								</el-form-item>
								<el-form-item label='审核人'>
									<el-input size='mini' v-model='form.auditor_name' disabled></el-input>
								</el-form-item>
								<el-form-item label='审核日期'>
									<el-date-picker v-model="form.audit_time" type="date" placeholder="选择日期"  size='mini' disabled></el-date-picker>
								</el-form-item>
								<!-- <el-form-item label="是否经销商订单">
									<el-switch on-text="是" off-text="否" :value="{'Y':true,'N':false,'':false,'null': false,}[form.if_dealer]"  disabled></el-switch>
								</el-form-item> -->

							</el-col>
						</el-row>
						<el-row :gutter='40'>
							<el-col :span='24'>

								<el-form-item label='地址' style="float:left;width:auto;" prop="province">
									<el-select placeholder="请选择" v-model="form.province" size='mini'  disabled @change="changeProvice">
									    <el-option
									      v-for="(value,key) in provice"
									      :key="key"
									      :label="value"
									      :value="parseInt(key)">
									    </el-option>
									 </el-select>


								</el-form-item>

								<el-form-item label=""  style="float:left;width:auto;margin-left:-110px;" prop="city">
									 <el-select placeholder="请选择" v-model="form.city" size='mini'  disabled @change="changeCity">
									    <el-option
									      v-for="(value,key) in city"
									      :key="key"
									      :label="value"
									      :value="parseInt(key)">
									    </el-option>
									 </el-select>


								</el-form-item>
								<el-form-item label=""  style="float:left;width:auto;margin-left:-110px;" >
									 <el-select  placeholder="请选择"  v-model="form.area" size='mini'  disabled @change="changeArea">
									    <el-option
									      v-for="(value,key) in area"
									      :key="key"
									      :label="value"
									      :value="parseInt(key)">
									    </el-option>
									 </el-select>

								</el-form-item>
								<el-form-item label=""  style="float:left;width:auto;margin-left:-110px;">
									 <el-select  placeholder="请选择"  v-model="form.street" size='mini' disabled>
									    <el-option
									      v-for="(value,key) in street"
									      :key="key"
									      :label="value"
									      :value="parseInt(key)">
									    </el-option>
									 </el-select>

								</el-form-item>
								<el-form-item label=""  style="float:left;width:auto;margin-left:-110px;">
									<!-- <span>{{form.receiver_addr}}</span> -->
									 <el-input size='mini' v-model='form.receiver_addr' disabled style="width:280px;"></el-input>
								</el-form-item>
								<!-- <el-button type='primary'  size='mini' style="margin-top:5px;margin-left:5px;" @click="choiceAddress" :disabled="saveDisabled">选择客户地址</el-button> -->
							</el-col>
						</el-row>
					</el-tab-pane>
				</el-tabs>
			</el-form>
		</div>
		<div class='xpt-flex__bottom' v-fold>
			<el-tabs v-model="bottomTabs" @tab-click="onTabClickHandler">
				<el-tab-pane label="问题商品信息" name="first">
					<xpt-list2
						:data='goodsList'
						:colData='goodsCols'
						:btns='[]'
						:orderNo="true"
						:taggelClassName="tableRowClassName"
						@row-click="rowClickHighlight"
						:selectable="selectableFun"
						@selection-change='s => {
							rowType[1].isDisabled = s.length !== 1
							goodsSelects(s)
						}'

						selection='checkbox'
					>
            <el-button
							slot='btns'
							@click="onReturnToExchange"
							size='mini'
              style="margin-right:5px;"
							type='primary'
							:disabled="
								!selectGoods.length
								|| !form.locker
								|| form.status !== 'FINISH'
                || selectGoods.some(obj => obj.shipping_method !== 'INTERCEPT')
							"
						>退/换货类型变更</el-button>
						<!-- 返货类型变更 -->
						<xpt-btngroup
							:btngroup='returnType'
							slot='btns'
							style="margin-right:5px;"
							:disabled="
								!selectGoods.length
								|| form.if_dealer === 'Y'
								|| !form.locker
								|| form.status === 'APPROVING'
								|| selectGoods.some(obj => obj.shipping_method === 'DEALER')
							"
						></xpt-btngroup>

						<el-button
							slot='btns'
							@click="goodsBtnsAction('returnGoodsScrap?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM')"
							size='mini'
							type='danger'
							:disabled="
								!selectGoods.length
								|| form.if_dealer === 'Y'
								|| !form.locker
								|| form.status === 'APPROVING'
								|| selectGoods.some(obj => obj.shipping_method === 'DEALER')
							"
						>报废</el-button>

						<span slot='btns' style="padding:0 5px">|</span>

						<el-button
							slot='btns'
							@click="checkIsSameReturnsType(() => goodsBtnsAction('pullGoods?permissionCode=RETURN_TRACKING_ORDER_EXECUTION_TRACKING'))"
							size='mini'
							type='primary'
							:disabled="form.status !== 'APPROVED' || !selectGoods.length || !form.locker"
						>退货拉货</el-button>

						<el-button
							slot='btns'
							@click="checkIsSameReturnsType(() => goodsBtnsAction('warehouse?permissionCode=RETURN_TRACKING_ORDER_EXECUTION_TRACKING'))"
							size='mini'
							type='primary'
							:disabled="form.status !== 'APPROVED' || !selectGoods.length || !form.locker"
						>退货入库</el-button>

						<el-button
							slot='btns'
							@click="cancelActionByNo('cancelService')"
							size='mini'
							type='danger'
							:disabled="form.status !== 'APPROVED' || !selectGoods.length || !form.locker"
						>作废服务单</el-button>

						<el-button
							slot='btns'
							@click="cancelActionByNo('cancelWarehouse?permissionCode=RETURN_TRACKING_ORDER_WITHDRAW_INWAREHOUSE_ORDER')"
							size='mini'
							type='danger'
							:disabled="form.status !== 'APPROVED' || !selectGoods.length || !form.locker"
						>撤回入库单</el-button>

						<span slot='btns' style="padding:0 5px">|</span>

						<el-button
							slot='btns'
							@click="checkIsSameReturnsType(cancelReturn)"
							size='mini'
							type='danger'
							:disabled="!selectGoods.length || !form.locker || form.status === 'APPROVING'"
						>取消退货</el-button>

						<xpt-btngroup
							:btngroup='rowType'
							slot='btns'
							style="margin-left:5px;"
							:disabled="
								form.status !== 'APPROVED'
								|| !selectGoods.length
								|| (form.if_dealer === 'Y' && selectGoods.every(obj => obj.shipping_method != 'PUR_DEALER'))
								|| !form.locker
								|| selectGoods.some(obj => obj.shipping_method === 'DEALER' || obj.source_row_id)
							"
						></xpt-btngroup><!-- 如果obj.source_row_id存在，则是通过返货退件添加进来的明细行。这时不能点击退货异常和添加包件价格 -->

						<span slot='btns' style="padding:0 5px">|</span>

						<el-button
							slot='btns'
							@click="checkIsSameReturnsType(confirmCollectGoods)"
							size='mini'
							type='primary'
							:disabled="
								form.status !== 'APPROVED'
								|| !selectGoods.length
								|| !form.locker
								|| form.if_dealer === 'Y'
								|| selectGoods.some(obj => !(obj.shipping_method === 'CUSTOMER' && obj.if_deposit === 'N'))
							"
						>确认收货</el-button>

						<span slot='btns' style="padding:0 5px">|</span>

						<el-button
							slot='btns'
							size='mini'
							type='primary'
							@click="priceChange"
							:disabled="
								!selectGoods.length
								|| !form.locker
								|| form.status === 'APPROVING'
								|| selectGoods.some(obj => obj.source_row_id)
							"
						>{{goodPriceText}}</el-button>
						<el-button
							slot='btns'
							size='mini'
							type='primary'
							:disabled="
								!selectGoods.length
								|| !form.locker
								|| form.status === 'APPROVING'
								|| selectGoods.some(obj => obj.source_row_id)
							"
							@click="reShipment()"

						>退货再发</el-button>
						<!-- 如果obj.source_row_id存在，则是通过返货退件添加进来的明细行。这时不能点击退货异常和添加包件价格 -->
						<!-- <el-button slot='btns'  size='mini' type='primary'>撤回应收单</el-button> -->
                        <el-button
							slot='btns'
							size='mini'
							type='primary'
							:disabled="
								!selectGoods.length
								|| !form.locker
								|| selectGoods.some(obj => obj.source_row_id)
							"
							@click="goodsBtnsAction('purchaseDealerRrturnApply?permissionCode=PURCHAS_RETURN_APPLY')"

						>经销采购退货申请</el-button>
                        <el-button
							slot='btns'
							size='mini'
							type='primary'
							:disabled="
								!selectGoods.length
								|| !form.locker
								|| selectGoods.some(obj => obj.source_row_id)
							"
							@click="goodsBtnsAction('purchaseDealerRrturnAudit?permissionCode=PURCHASE_RETURN_AUDIT')"

						>经销采购退货审核</el-button>
                        <el-button
							slot='btns'
							size='mini'
							type='primary'
							:disabled="
								!selectGoods.length
								|| !form.locker
								|| selectGoods.some(obj => obj.source_row_id)
							"
							@click="goodsBtnsAction('purchaseDealerRrturnReject?permissionCode=PURCHASE_RETURN_REJECT')"

						>经销采购退货驳回</el-button>
                        <el-button
							slot='btns'
							size='mini'
							type='primary'
							:disabled="
								!selectGoods.length
								|| !form.locker
								|| selectGoods.some(obj => obj.source_row_id)
							"
							@click="goodsBtnsAction('purchaseDealerRrturnCancle?permissionCode=PURCHASE_RETURN_CANCLE')"

						 >取消经销采购退货</el-button>
					</xpt-list2>
				</el-tab-pane>
				<el-tab-pane label="退货再售/再发信息" name="second">
					<xpt-list
						:data='returnsResaleGoods'
						:colData='returnsResaleGoodsCols'
						:btns='[]'
            :show-head="false"
						selection=''
					></xpt-list>
				</el-tab-pane>
				<el-tab-pane label="三包处理进度" name="seven">
					<xpt-list
						:data='scheduleList'
						:colData='scheduleCols'
						:showHead='false'
						selection=''
					></xpt-list>
				</el-tab-pane>

				<!-- <el-tab-pane label="驳回信息" name="seven">
					<xpt-list
						:data='rejectList'
						:colData='rejectCols'
						:btns='rejectBtns'
						:orderNo="true"
						@selection-change='rejectSelects'
						selection='checkbox'
					></xpt-list>


				</el-tab-pane> -->


				<el-tab-pane label="操作记录" name="six">
					<xpt-list
						:data='operaterList'
						:colData='operaterCols'
						:showHead='false'
						:orderNo="true"
						selection=''
					></xpt-list>
				</el-tab-pane>
				<el-tab-pane label="接口信息" name="three">
					<xpt-list
						:data='apiList'
						:colData='apiCols'
            :show-head="false"
						:btns="[]"
						:orderNo="true"
						selection='radio'
						@radio-change="radioChange"
						ref="apiMessage"
					>
            <template v-slot:operate="{ row }">
              <el-button
                v-if="row.push_bill_type === '财务中台再售销售出库单'"
                type="primary"
                size="mini"
                key="saleAgain"
                @click="reSendApiSale(row)"
              >生成财务中台再售销售出库单</el-button>
              <el-button
                v-if="row.push_bill_type === '财务中台再发销售出库单'"
                type="primary"
                size="mini"
                key="sendAgain"
                @click="reSendApiShipment(row)"
              >生成财务中台再发销售出库单</el-button>
            </template>
          </xpt-list>
				</el-tab-pane>
			</el-tabs>

		</div>
	</div>
</template>
<script>
import Vue from 'vue'
import goods from './model/goods';
import goods2 from './model/goods_2';
import schedule from './model/schedule';//三包处理进度
import returnsResaleGoods from './model/returnsResaleGoods';//再售再发
//import reject from './model/reject';//驳回信息进度
import operat from './model/operat';//操作记录
import api from './model/apiinfo';//接口信息
import validate from '@common/validate.js';
import Fn from '@common/Fn.js';

export default {
	props: ['params'],
	mixins: [goods,goods2,schedule/*,reject*/,operat,api,returnsResaleGoods],
	data() {
		var self = this;
		return {
			btnStatus: false, // 针对锁定，提交，审核三个按钮做控制，同一时间只能点击一个
			saveLoding:false,//保存按钮
			submitLoding:false,//提交按钮
			recallLoding:false,//撤回按钮
			lockLoding:false,//锁定按钮
			deblockLoding:false,//解锁按钮
			auditLoding:false,//审核按钮
			reauditLoding:false,//驳回
			refreshLoding:false,//刷新按钮
			tailLoding:false,//执行跟踪按钮
			recallWarehouseLoding:false,//撤回仓库按钮
			getGoodsLoding:false,//拉货按钮
			closeLoding:false,//关闭按钮
			uncloseLoding:false,//返关闭
			upFileLoding:false,//上传附件按钮
			reSendLoading:false,//4PL重推按钮
			//changeLoding:false,//变更
			confirmChangeLoding:false,//确认变更
			cancelChangeLoding:false,//取消变更

			//searchLoding:false,//查看附件按钮

			saveDisabled:false,//保存按钮
			submitDisabled:false,//提交按钮
			recallDisabled:false,//撤回按钮
			lockDisabled:false,//锁定按钮
			deblockDisabled:false,//解锁按钮
			auditDisabled:false,//审核按钮
			reauditDisabled:false,//驳回
			refreshDisabled:false,//刷新按钮
			tailDisabled:false,//执行跟踪按钮
			recallWarehouseDisabled:false,//撤回仓库按钮
			getGoodsDisabled:false,//拉货按钮
			closeDisabled:false,//关闭按钮
			//uncloseDisabled:false,//返关闭
			upFileDisabled:true,//上传附件按钮
			reSendDisabled:true,//4PL重推按钮
			changeDisabled:false,//变更
			confirmChangeDisabled:true,
			cancelChangeDisabled:true,
			//searchDisabled:false,//查看附件按钮
			//notClick:false,//是否是第一次点击变更按钮
			//changeFields:false,//点击变更的时候可编辑的字段，默认不可编辑


			// repairDisabled:true,//补推退款申请单，用于历史数据


			// returnType:{"NORMAL":'标准退货',"CHANGE":'退货变更'},

			topTabs: 'first',
			bottomTabs: 'first',
			rejectBtnStatus:false,//驳回信息的按钮控制
			provice:{},
			city:{},
			area:{},
			street:{},
			deliveryFeePayMethod:Fn.postFeeType(),
			ifClickUpload:false,
			closStatus:{
				'Y':'是',
				'N':'否'
			},




			// 基本信息、其它信息
			form: {
				id:null,
				bill_returns_no:null,
				//plan_returns_id : null,
				process_node:null,//记录单据按钮最后一次操作节点
				if_lock:null,//是否上门取货
				delivery_fee_pay_method:null,
				merge_trade_id:null,
				merge_trade_no:null,
				after_order_no:null,
				after_order_id:null,
				buyer_name:null,
				cargo_deposit:null,
				predicted_delivery_fee:null,
				actual_delivery_fee:null,
				shipping_method:null,
				if_three_age:null,
				return_rate:null,
				return_type:null,
				//delivery_fee_pay_method:null,
				logistics_company_id:null,
				logistics_company_name:null,
				if_pickup:null,
				pickup_time:null,
				business_status:null,
				business_remark:null,
				return_remark:null,
				province:null,
				city:null,
				area:null,
				street:null,
				receiver_addr:null,
				reveiver_phone:null,
				delivery_id:null,
				delivery_name:null,
				three_id:null,
				three_name:null,
				line_area:null,
				status:null,
				transmit_time:null,
				//api_status:null,
				api_transfer_result:null,
				close_status:null,
				locker:null,
				locker_name:null,
				locker_group:null,
				locker_group_name:null,
				staff_locker:null,
				staff_locker_name:null,
				staff_locker_group:null,
				staff_locker_group_name:null,
				locker_name:null,
				locker_group_name:null,

				if_deposit:null,//是否执鼎托管
				if_pickup:null,
				if_refund:null,
				if_scrap:null,

				//aftersale_id:null,
				//aftersale_name:null,
				//aftersale_group_id:null,
				//aftersale_group_name:null,
				auditor:null,
				auditor_name:null,
				audit_time:null,
				closer:null,
				closer_name:null,
				close_time:null,
				creator:null,
				creator_name:null,
				create_time:null,
				creator_group_name:null,
				creator_big_group_name:null,
				last_modifier:null,
				last_modifier_name:null,
				last_modify_time:null,
				custom_id:null,//客户ID
				if_dealer:null,
				dealer_customer_id:null,
				dealer_customer_name:null,
				dealer_customer_number:null,
				settle_method:null,
				settle_entity:null,
			},
			//业务状态
			businessStatus:{
				UN_LOCK:'未锁定',
				LOCKED:'已锁定',
				ESTIMATE:'押金估算',
				EXECUTE:'执行跟踪中',
				EXECUTED:'执行跟踪完成',
				REJECT:'驳回'

			},
			//单据状态
			status:{
				CREATE:'创建',
				APPROVING:'审核中',
				EXECUTING:'执行跟踪',
				APPROVED:'已审核',
				CANCEL:'作废',
				FINISH: '完结',
			},
			//接口状态
			apiStauts:{
				WAIT:'等待',
				NONEED:'无需下达',
				TRANSMITED:'已下达',
				TRANSMITING:'下达中',
				CANCLE:'已取消',
				FINISH:'已完成'
			},

			uploadData:{},
			originalForm:'',//原始表头数据
			originalGoods:'',//原始
			rules:{
				cargo_deposit:validate.maxTwoDecimals({
					trigger:'change',
					self:self
				}),
				delivery_name:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				/*three_name:validate.isNotBlank({
					trigger:'change',
					self:self
				}),*/
				line_area:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				receiver_name:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				/*reveiver_phone:validate.mobile({
					trigger:'change',
					self:self
				}),*/
				province:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				city:validate.isNotBlank({
					trigger:'change',
					self:self
				})

			},
			refundingPaymentList: [],
			refundingMaterialList: [],
			apiInfoMultipleSelection:{},//接口信息选择对象
			ifPurchaseDealer: false, // 当前代理人是否是采购经销商客户
		}
	},
	created () {
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
		this.ifPurchaseDealerOfProxyId()
	},
	beforeDestroy(){
		//解除监听切换业务代理事件
		this.$root.eventHandle.$off('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
	},
	methods: {
		ifPurchaseDealerOfProxyId() {
			let id = this.getEmployeeInfo('personId'), self = this
			this.ajax.get('/user-web/api/userPerson/getUserPerson/'+id,function(data){
				data = data.body;
				if(data.result){
					if (data.content && data.content.type == 'PURCHASE_DEALER') {
						self.ifPurchaseDealer = true
					} else {
						self.ifPurchaseDealer = false
					}
				}
			},function(error){
				self.ifPurchaseDealer = false
			})
		},
		toAfterOrderDetail (id){
			var params = {
	          id,
	          idList: [],//用于售后单详情的上一页/下一页
	        };

	        if(document.querySelector('[data-symbol="after_order_id_' + id + '"]')){
	          this.$message.error('该售后单已经打开')
	        }else {
	          this.$root.eventHandle.$emit('creatTab', {
	            name:"售后单详情",
	            params:params,
	            component: () => import('@components/after_sales/index')
	          });
	        }
		},
		/**
		*补推退款申请单按钮
		**/
		repairEvent(){
			let id = this.form.id;
			let bill_returns_no  = this.form.bill_returns_no ;
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/return/pushBillRFForHistoryBillRT',{id:id,bill_returns_no:bill_returns_no},(d)=>{
				this.$message({
					type:d.body.result?'success':'error',
					message:d.body.msg
				})
			});
		},
		/**
		*测试数据
		***/
		testData(){

			/***变更****/
			//单据状态=已审核、业务状态=已锁定
			//三包返货=>单据状态=已审核、业务状态=执行跟踪中，是否拉货=未拉货
			//三包返货=>单据状态=已审核、业务状态=执行跟踪中，是否拉货=已拉货，（退货行无 入库单）
			//客户自返=>单据状态=已审核、业务状态=已锁定，是否拉货=未拉货，（无入库单号）

			/*this.form.status = 'APPROVED';
			this.form.business_status = 'LOCKED';
			this.goodsList.map((a,b)=>{
				a.bill_godownenty_no = null;
			})*/

			/*this.form.shipping_method = 'THREE_BAG';
			this.form.status = 'APPROVED';
			this.form.business_status = 'EXECUTE';
			this.form.if_pickup = 'N';*/

			/*this.form.shipping_method = 'THREE_BAG';
			this.form.status = 'APPROVED';
			this.form.business_status = 'EXECUTE';
			this.form.if_pickup = 'Y';
			this.goodsList.map((a,b)=>{
				a.bill_godownenty_no = null;
			})*/

			/*this.form.shipping_method = 'CUSTOMER';
			this.form.status = 'APPROVED';
			this.form.business_status = 'LOCKED';
			this.form.if_pickup = 'N';
			this.goodsList.map((a,b)=>{
				a.bill_godownenty_no = null;
			})*/

			/***执行更踪****/
			//单据状态=已审核、业务状态=已锁定
			//客户自返=>单据状态=已审核、业务状态=已锁定，是否拉货=未拉货，（无入库单号）


			/*this.form.status = 'APPROVED';
			this.form.business_status = 'LOCKED';
			this.goodsList.map((a,b)=>{
				a.bill_godownenty_no = null;
			})*/

			/*this.form.shipping_method = 'CUSTOMER';
			this.form.status = 'APPROVED';
			this.form.business_status = 'LOCKED';
			this.form.if_pickup = 'N';
			this.goodsList.map((a,b)=>{
				a.bill_godownenty_no = null;
			})*/


			/***撤回仓储****/
			//客户自返=>单据状态=已审核、业务状态=已锁定，是否拉货=未拉货，（有入库单号）
			//客户自返=>单据状态=已审核、业务状态=执行跟踪中，是否拉货=未拉货，（有入库单号）
			//三包返货=>单据状态=已审核、业务状态=执行跟踪中，是否拉货=已拉货，（退货行有入库单）

			/*this.form.shipping_method = 'CUSTOMER';
			this.form.status = 'APPROVED';
			this.form.business_status = 'LOCKED';
			this.form.if_pickup = 'N';
			let i = this.goodsList.length;
			let good = i?i!=1?this.goodsList[1]:this.goodsList[0]:{};
			good.bill_godownenty_no = 1111;*/

			/*this.form.shipping_method = 'CUSTOMER';
			this.form.status = 'APPROVED';
			this.form.business_status = 'EXECUTE';
			this.form.if_pickup = 'N';
			let i = this.goodsList.length;
			let good = i?i!=1?this.goodsList[1]:this.goodsList[0]:{};
			good.bill_godownenty_no = 1111;*/

			/*this.form.shipping_method = 'THREE_BAG';
			this.form.status = 'APPROVED';
			this.form.business_status = 'EXECUTE';
			this.form.if_pickup = 'Y';
			let i = this.goodsList.length;
			let good = i?i!=1?this.goodsList[1]:this.goodsList[0]:{};
			good.bill_godownenty_no = 1111;*/

		},
		/**
		*初使化按钮的状态
		***/
		initBtnStatus(){
			//业务状态，单据状态，接口状态
			var bussinessStatus = this.form.business_status;
			var status = this.form.status;
			var aipStatus = this.form.api_status;

			var IsClose = this.form.close_status == 'Y' || status == 'CANCEL'?true:false;
			var isEnd = bussinessStatus == 'EXECUTED'?true:false;
			var shipping_method = this.form.shipping_method;

			// var empolyeeObj = this.getEmployeeInfo();

			//
			var disabledBtn = this.disabledBtnByRoleAndReturnType();
			//var currentUserId = Fn.getUserInfo('id');
			// var currentUserId = empolyeeObj.id;
			//var isCurrentUser = this.form.locker?this.form.locker == currentUserId:true;
			//先不做人员的一个控制
			var isCurrentUser = true;
			//var isCurrentUser = true;
			//var isCurrentUser = 1/*先去掉此判断*/ || (this.form.locker?this.form.locker == currentUserId:true);
			let hasStorageCode = this.judgeHasStorage();
			var NotcanOperationBills = disabledBtn || !isCurrentUser || IsClose || isEnd ?true:false;

			//如果客户自返的为现付()。三包物流返货默认为到付(ARRIVALPAY)
			//!this.form.delivery_fee_pay_method?this.form.shipping_method == 'THREE_BAG'?'ARRIVE_PAY':'NOWPAY':'';
			//是否拉货（默认为未拉货）
			!this.form.if_pickup?this.form.if_pickup = 'N':'';

			//just test
			//this.form.if_pickup = 'N';

			var if_pickup = this.form.if_pickup;
			//补推退款申请单按钮
			// this.repairDisabled = !(this.getUserInfo('id') == '1309' && if_pickup == 'Y');
			//this.repairDisabled = !(if_pickup == 'Y');

			//单据状态=已审核、业务状态=已锁定、可以操作：执行跟踪、变更、确认变更、取消变更,关闭（前提是没有没有入库单）
			let APPROVED = NotcanOperationBills?true:(status == 'APPROVED' && bussinessStatus == 'LOCKED' && !hasStorageCode)?false:true;


			//三包返货：1单据状态=已审核、业务状态=执行跟踪中，是否拉货=未拉货，可以操作：变更、确认变更、取消变更、关闭、
						//2单据状态=已审核、业务状态=执行跟踪中，是否拉货=已拉货，（退货行有入库单）只允许操作：撤回仓库、

						//3单据状态=已审核、业务状态=执行跟踪中，是否拉货=已拉货，（退货行无 入库单）只允许操作：变更
			let THREE_BAG1 =  NotcanOperationBills?true:status == 'APPROVED' && bussinessStatus == 'EXECUTE' && if_pickup == 'N' && shipping_method == 'THREE_BAG'?false:true;
			let THREE_BAG2 =  NotcanOperationBills?true:status == 'APPROVED' && bussinessStatus == 'EXECUTE' && if_pickup == 'Y' && hasStorageCode && shipping_method == 'THREE_BAG'?false:true;

			let THREE_BAG3 = NotcanOperationBills?true:status == 'APPROVED' && bussinessStatus == 'EXECUTE' && if_pickup == 'Y' && !hasStorageCode && shipping_method == 'THREE_BAG'?false:true;



			//客户自返：
			//0:单据状态=已审核、业务状态=执行跟踪中，是否拉货=已拉货、可以操作刷新、查看附件

			//1:单据状态=已审核、业务状态=已锁定，是否拉货=未拉货，（无入库单号）可以操作：执行跟踪、变更、确认变更、取消变更、关闭、刷新、查看附件

			//2:单据状态=已审核、业务状态=已锁定，是否拉货=未拉货，（有入库单号）只允许操作：撤回仓库、刷新、查看附件

			//3:单据状态=已审核、业务状态=执行跟踪中，是否拉货=未拉货，（有入库单号）只允许操作：撤回仓库、刷新、查看附件

			let CUSTOMER1 = NotcanOperationBills?true:status == 'APPROVED' && bussinessStatus == 'LOCKED' && if_pickup == 'N' && !hasStorageCode && shipping_method == 'CUSTOMER'?false:true;
			let CUSTOMER2 = NotcanOperationBills?true:status == 'APPROVED' && bussinessStatus == 'LOCKED' && if_pickup == 'N' && hasStorageCode && shipping_method == 'CUSTOMER'?false:true;
			let CUSTOMER3 = NotcanOperationBills?true:status == 'APPROVED' && bussinessStatus == 'EXECUTE' && if_pickup == 'N' && hasStorageCode && shipping_method == 'CUSTOMER'?false:true


			//2-10需求
			//锁定按钮(单据状态 == 创建 ,业务状态 == 未锁定)
			this.lockDisabled = NotcanOperationBills?true:status == 'CREATE' &&  bussinessStatus == 'UN_LOCK'?false:true;

			//解锁(单据状态=创建&&业务状态=已锁定)
			this.deblockDisabled = NotcanOperationBills?true:status == 'CREATE' &&  bussinessStatus == 'LOCKED'?false:true;

			//保存,(单据状态 == 创建/已审核 ,业务状态 == 已锁定)
			this.saveDisabled = NotcanOperationBills?true:(/^(CREATE|APPROVED)$/.test(status) && bussinessStatus == 'LOCKED')?false:true;
			//提交,(单据状态 == 创建 ,业务状态 == 已锁定)
			this.submitDisabled = NotcanOperationBills?true:(status =='CREATE' && bussinessStatus == 'LOCKED')?false:true;

			//撤回/审核（单据状态=审核中&&业务状态=已锁定）
			this.recallDisabled =  NotcanOperationBills?true:(status =='APPROVING' && bussinessStatus == 'LOCKED')?false:true;
			this.auditDisabled = this.recallDisabled;

			//驳回(单据状态=创建&&业务状态=已锁定)
			//this.reauditDisabled = NotcanOperationBills?true:(status =='CREATE' && bussinessStatus == 'LOCKED')?false:true;
			this.reauditDisabled = disabledBtn?true:bussinessStatus == 'UN_LOCK'?false:true;

			//关闭(单据状态=创建&&业务状态=已锁定 )
			//this.closeDisabled = NotcanOperationBills?true:(status =='CREATE' && bussinessStatus == 'LOCKED') || !APPROVED || !THREE_BAG1 || !CUSTOMER1?false:true;
			this.closeDisabled = disabledBtn;

			//撤回仓储，执行跟踪，变更三者之间的关系

			//撤回仓储()
			this.recallWarehouseDisabled =  !CUSTOMER2 || !CUSTOMER3 || !THREE_BAG2?false:true;

			let RESALE = NotcanOperationBills?true:status == 'APPROVED' && bussinessStatus != 'EXECUTED' && if_pickup == 'N' && shipping_method == 'RESALE'?false:true;
			let DEALER = NotcanOperationBills?true:status == 'APPROVED' && bussinessStatus != 'EXECUTED' && if_pickup == 'N' && shipping_method == 'DEALER'?false:true;
			//执行跟踪()
			//this.tailDisabled = !APPROVED || !CUSTOMER1?false:true;
			this.tailDisabled = !APPROVED || !CUSTOMER1 || !THREE_BAG1 || !RESALE || !DEALER?false:true;

			//变更
			//this.form.status == 'APPROVED' && this.form.bussinessStatus == 'LOCKED'
			//返货再售
			if(shipping_method == 'RESALE'){
				//bussinessStatus !=（EXECUTE）&& bussinessStatus !=（EXECUTED） && !IsClose
				this.changeDisabled = if_pickup == 'N' && !IsClose && status == 'APPROVED'?false:true;
				// 已拉货不能点击变更
			}else if(shipping_method=='DEALER'){
				//经销退货
				this.changeDisabled = true;
			}
			else{//正常退货
				this.changeDisabled = !APPROVED || !THREE_BAG1 || !CUSTOMER1 || !THREE_BAG3?false:true;
			}

			this.upFileDisabled = !(['CREATE','APPROVED','APPROVING','FINISH'].includes(status));

			//确认变更
			this.confirmChangeDisabled = this.cancelChangeDisabled = true;



			//重置加载标识
			this.saveLoding=false;
			this.submitLoding=false;
			this.recallLoding=false;
			this.lockLoding=false;
			this.deblockLoding=false;
			this.auditLoding=false;
			this.refreshLoding=false;
			this.tailLoding=false;
			this.recallWarehouseLoding=false;
			this.getGoodsLoding=false;
			this.closeLoding=false;
			this.uncloseLoding = false;
			this.upFileLoding=false;
			this.confirmChangeLoding=false;//确认变更
			this.cancelChangeLoding=false;//取消变更
			this.reauditLoding = false;

		},
		/***
		*按钮不需要禁用的情况
		*1，经销商进入退货再售的退货跟踪单，禁掉按钮
		*2，审核中也不能编辑
		***/
		disabledBtnByRoleAndReturnType(){
			//let d = window.userInfo;
			//let d = this.getUserInfo();
			// let isDealerUser = this.personBusinessAttribute.attributeValue;
			// let isDealer = /^(DEALER_PICK|DEALER)$/.test((this.goodsList[0] || {}).shipping_method)?true:false;
			// //if(isDealerUser && isDealer) return false;
			// if(isDealerUser && !isDealer) return true;
			return false;

		},
		/**
		*初使化表头数据
		*/
		initData(data){
			//TODO
			if(!data) return;
			for(var key in this.form){
				this.form[key] = data[key] || null;
			}
			this.form.receiver_name = data.receiver_name;
			this.form.if_three_age = this.form.if_three_age?true:false;
			this.form.return_rate = this.form.return_rate?true:false;

			//测试数据
			//this.testData();





			//用单据状态以及业务状态去判断按钮的操作情况
			//首先是否是关闭的，然后再去判断

			//锁定按钮(单据状态 == 创建 ,业务状态 == 未锁定,接口状态== 等待下达)
			//this.lockDisabled = !isCurrentUser || IsClose?true:status == 'CREATE' &&  bussinessStatus == 'UN_LOCK' && aipStatus == 'WAIT'?false:true;

			//解锁(关闭)，有两种情况可以解锁
			//1锁定未提交(单据状态 == 创建 ,业务状态 == 已锁定,接口状态== 等待下达)
			//2锁定并且撤回仓储(单据状态 == 已审核 ,业务状态 == 已锁定,接口状态== 已取消)
			//this.deblockDisabled = this.closeDisabled = !isCurrentUser || IsClose?true:(status =='CREATE' && bussinessStatus == 'LOCKED'  && aipStatus == 'WAIT') || (status =='APPROVED' && bussinessStatus == 'LOCKED'  && aipStatus == 'CANCLE')?false:true;

			//反关闭
			//this.uncloseDisabled = IsClose?false:true;

			//保存,提交,有两种情况，1是开始的一个保存
			//(单据状态 == 创建 ,业务状态 == 已锁定,接口状态== 等待下达)
			//
			//this.saveDisabled = this.submitDisabled = this.closeDisabled = !isCurrentUser || IsClose?true:(status =='CREATE' && bussinessStatus == 'LOCKED'  && aipStatus == 'WAIT')?false:true;



			//撤回(单据状态 == 创建 ,业务状态 == 已锁定,接口状态== 等待下达)
			//this.recallDisabled = /*this.auditDisabled =*/ !isCurrentUser || IsClose?true:status =='APPROVING' && bussinessStatus == 'LOCKED' && aipStatus == 'WAIT'?false:true;

			//审核
			//1:(单据状态 == 创建 ,业务状态 == 已锁定,接口状态== 等待下达)
			//2:(单据状态 == 审核中 ,业务状态 == 已锁定,接口状态== 已完成)
			//this.auditDisabled = !this.recallDisabled?false:status =='APPROVING' && bussinessStatus == 'LOCKED' && aipStatus == 'FINISH'?false:true;


			//执行跟踪(单据状态 == 已审核 ,业务状态 == 已锁定,接口状态== 等待下达)
			//this.tailDisabled = !isCurrentUser || IsClose?true:status =='APPROVED' && bussinessStatus == 'LOCKED' && aipStatus == 'WAIT'?false:true;
			//撤回仓储(单据状态 == 已审核 ,业务状态 == 执行跟踪中,接口状态== 下达中)
			//this.recallWarehouseDisabled = !isCurrentUser || IsClose?true:status =='APPROVED' && bussinessStatus == 'EXECUTE' && aipStatus == 'TRANSMITING'?false:true;

			//变更按钮
			//只有接口状态为：待等下达/已取消/无需下达状态，单据状态为已审核，返货类型为："三包返货/客户自返"的才允许做变更操作
			//let bool = (aipStatus == 'WAIT' || aipStatus == 'CANCLE' || aipStatus == 'NONEED') && status == 'APPROVED' && (shipping_method == 'THREE_BAG' || shipping_method == 'CUSTOMER')?false:true;

			//this.changeDisabled = !isCurrentUser || /*this.notClick ||*/ IsClose?true:bool;

			/*let process_node = this.form.process_node ;//记录单据按钮最后一次操作节点
			//锁定(未锁定或已解锁)
			this.lockDisabled = !isCurrentUser || IsClose || isEnd?true:process_node== 'SAVE'  || process_node == 'UNLOCK'?false:true;

			//解锁(锁定,撤回)
			this.deblockDisabled = this.closeDisabled = !isCurrentUser || IsClose || isEnd ?true: process_node == 'LOCK' || process_node == 'CANCEL'?false:true;

			//关闭(三包 && 未拉货) || (自返 && 业务状态为锁定)
			this.closeDisabled = !isCurrentUser || IsClose || isEnd?true:(shipping_method == 'THREE_BAG'  && if_pickup == 'N') || (process_node == 'LOCK' && shipping_method == 'CUSTOMER')?false:true

			//保存/提交(锁定，保存，撤回)
			this.saveDisabled = this.submitDisabled = !isCurrentUser || IsClose || isEnd?true:process_node == 'LOCK' || process_node == 'CANCEL'?false:true;

			//撤回（提交）
			this.recallDisabled = !isCurrentUser || IsClose || isEnd?true:process_node == 'SUBMIT'?false:true;
			//审核(提交)
			this.auditDisabled = this.recallDisabled;

			//执行跟踪((审核||撤回仓储||确认变更) && 未拉货)
			this.tailDisabled = !isCurrentUser || IsClose || isEnd?true:(process_node == 'AUDIT' || process_node == 'RETRACK' || process_node == 'CHANGE') && if_pickup == 'N'?false:true;

			//撤回仓储(执行跟踪 && 已拉货)
			this.recallWarehouseDisabled = !isCurrentUser || IsClose?true:process_node == 'TRACK' && if_pickup == 'Y'?false:true;


			//变更按钮
			//返货类型为：三包返货=>（单据状态=已审核、业务状态=执行跟踪中，是否拉货=未拉货)
			//返货类型为：客户自返=>1:当返货类型为客户自返，则单据为已审核，业务状态=已锁定，可以直接变更
			//						2:  b)  单据为已审核且仓库为执鼎托管，业务状态=执行跟踪中，表体有入库单号字段为空，才可以点变更

			this.changeDisabled = !isCurrentUser ||  IsClose || isEnd?true:false;
			if(!this.changeDisabled){
				//三包返货
				if(shipping_method == 'THREE_BAG'){
					this.changeDisabled = this.form.status == 'APPROVED' &&　this.form.business_status == 'EXECUTE'　&& if_pickup == 'N';
				}else if(shipping_method == 'CUSTOMER'){
					this.changeDisabled = !(this.form.status == 'APPROVED' && this.form.if_deposit == 'Y' && this.form.business_status == 'EXECUTE' && !hasStorageCode) || !(this.form.status == 'APPROVED' &&  this.form.business_status == 'LOCKED')
				}else {
					this.changeDisabled = true;
				}
			}

			//撤回(表体有入库单号字段，则可撤回)
			this.recallWarehouseDisabled = !isCurrentUser || IsClose || isEnd?true:hasStorageCode && this.form.status == 'APPROVED'?false:true;	*/


			/*this.changeDisabled = !isCurrentUser ||  IsClose?true:(process_node == 'TRACK' || process_node == 'RETRACK' || process_node == 'AUDIT' || process_node == 'CHANGE')  && (shipping_method == 'THREE_BAG' || shipping_method == 'CUSTOMER')?false:true;*/


			this.initBtnStatus();

			this.setDisabledOption();


			this.setOriginalForm();
		},
		validateGoodsIsPass(){
			//var originalIds = this.goodsList.map(obj=>obj.originalId);
			var originalIds = this.goodsList.map(obj=>obj.originalId || '');
			originalIds = JSON.stringify(Array.from(new Set(originalIds)));
			let bool = true;
			let error = '第';
			this.goodsList.map((a,b)=>{
				if(originalIds.indexOf(a.id) != -1 && a.row_type == 'ROW_NORMAL'){
					error+=(b+1+',');
					bool = false;

				}
			})
			if(!bool){
				this.$message.error(error+'行商品已做变更，请编辑行类型');
			}
			return bool;
		},

		/**
		*确认变更
		**/
		confirmChange(){
			let bool = this.validateIsPass();
			if(!bool) return;
			if(!this.validateGoodsIsPass()) return;
			//当原商品做了变更时，行类型必须不能是“正常退货”
			var data = this.getDataOfSave();
			this.confirmChangeDisabled = this.confirmChangeLoding = this.cancelChangeDisabled = true;

			this.ajax.postStream('/afterSale-web/api/aftersale/bill/return/change?permissionCode=RETURN_TRACKING_ORDER_CHANGE_CONFIRM',data,res=>{
				var d = res.body;
				this.$message({
					type:d.result?'success':'error',
					message:d.msg
				});
				if(!d.result){
					this.confirmChangeDisabled = this.confirmChangeLoding = this.cancelChangeDisabled = false;
					return;
				}
				this.getDetail();
				this.getOperateList();
				this.getScheduleList();
			});
		},
		/**
		*绑定关闭行的背景颜色
		**/
		tableRowClassName(data){
			// 控制合计行样式
			if (data.act_price&&String(data.act_price).includes("合计")) {
				return 'sum-row';
			}
			if(data.row_status == 'Y' || data.shipping_method === 'SCRAP') return 'pmm_red';
			else if (data.row_status == 'END' || data.cargo_status === 'END' || data.warehouse_status === 'END'/*只要有一个终止就是取消退货*/){
				if(this.form.status === 'CREATE'){
					data.isNotEdit = true
					Object.defineProperties(data, {
					    __is_END: {
					        configurable: true,
					        writable: true,
					        enumerable: false,
					        value: true
					    },
					})
				}
				return 'pmm_gray'
			}else if (/^(GOODSRETURNS|GOODSLOST)$/.test(data.exception_control)) return 'pmm_gray'
			return '';
		},
		/**
		*取消变更
		**/
		cancelChange(){
			this.confirmChangeDisabled = this.cancelChangeDisabled = true;
			this.changeDisabled = false;

			var originalGoods = this.getOriginalGoods();
			originalGoods.map((a,b)=>{
				a.conditionDisabled = true;
			});
			this.goodsList = originalGoods;
		},
		/**
		*变更单据
		**/
		changeBills(){
			//this.notClick = true;
			this.bottomTabs = 'first';
			this.changeDisabled = true;
			this.confirmChangeDisabled = this.cancelChangeDisabled = false;
			//this.changeFields = false;
			this.goodsList.map((a,b)=>{
				if(a.row_status == 'Y') return;
				a.conditionDisabled = a.canNotEditStorage = false;


				//var c = Object.assign({},a);
				this.goodsList.splice(b,1,a);
			});
		},
		/**
		*选择物流运输商
		**/
		searchLogisticsCompany(){
			if(this.submitDisabled) return;
			let params = {};
			params.callback = (d)=>{
				var data = d.data;
				if(!data) return;
				this.form.logistics_company_id = data.supplier_id;
				this.form.logistics_company_name = data.name;
			}
			this.$root.eventHandle.$emit('alert',{
		        params:params,
		        component:()=>import('@components/after_sales_supply/supplier'),
		        style:'width:80%;height:80%',
		        title:'选择供应商'
		     });

		},

		/**
		*根据省市区获取三包点和提货点
		***/
		/*getPathInfo(){
			var url = '/afterSale-web/api/aftersale/ticketSupply/getThreeInfo';
			var params = {
				"province" : this.form.province,
    	 		"city" : this.form.city,
     			"area" : this.form.area
			}
			this.ajax.postStream(url,params,res=>{
				let data = res.body;
				if(!data.result){
					this.$message.error(data.msg);
				}
				let a = data.content || {};
				this.form.three_id = a.three_id;
				this.form.three_name = a.sb_name;
				this.form.line_area = a.line_area;
				this.form.delivery_id = a.th_point;
				this.form.delivery_name = a.th_name;
				//TODO,还有运费需要计算
			});
		},*/
		/**
		*根据省市区去计算运费
		**/
		calcFee(){
			this.goodsList.map((a,b)=>{

			});
		},
		/**
		*需要判断有没有供应商，没有供应商是不可以进行审核的
		**/
		judgeHasSupplier(){
			var bool = true;
			this.goodsList.map((a,b)=>{
				if(a.branch_no && !a.supplier_id){//商品行有批号branch_no才检查有没有供应商
					bool = false;
				}
			});
			if(!bool){
				this.$message.error('供应商不能为空');
			}
			return bool;
		},
		/*
		*
		驳回
		**/
		reauditEvent(){
			let params = {};
			params.callback = (data)=>{
				let rejectData = {
					id:this.form.id,
					reject_reason:data.data
				}
				if(data.data == ''){
					this.$message.error('请输入驳回理由');
					return;
				}
				this.reauditDisabled = this.reauditLoding = true;
				//let url = '/afterSale-web/api/aftersale/bill/return/reject?permissionCode=RETURN_TRACKING_ORDER_REJECT';
				let url = this.setBusinessPrower().reject;
				this.ajax.postStream(url,rejectData,res=>{
					let data = res.body;
					this.$message({
						type:data.result?'success':'error',
						message:data.msg
					});
					if(!data.result) {
						this.initData(this.form);
						return;
					}
					//this.refreshAllData();
					this.$root.eventHandle.$emit('removeTab',this.params.tabName);
					this.upDateData();

				});
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/receipt/reject_receipt'),
				style:'width:600px;height:200px',
				title:'请输入驳回理由'
			});

		},
		/**
		*审核
		*/
		auditEvent(){
			this.btnStatus = true
			this.auditDisabled = true;
			this.auditLoding = true;
			//let url = '/afterSale-web/api/aftersale/bill/return/audit?permissionCode=RETURN_TRACKING_ORDER_AUDIT';
			let url = this.setBusinessPrower().approve;
			let params = {
				id : this.form.id
			}
			this.ajax.postStream(url,params,res=>{
				this.btnStatus = false
				let data = res.body;
				this.$message({
					type:data.result?'success':'error',
					message:data.msg
				});
				if(!data.result) {
					this.initData(this.form);
					return;
				}
				this.refreshAllData();
				this.upDateData();

			});
		},
    onTabClickHandler(tab) {
      if (tab.name === 'second') {
        this.getReturnsResaleGoods();
      }
    },
		/**
		*刷新
		****/
		refreshEvent() {
			this.refreshAllData(true);
		},
		/**
		*执行跟踪,撤回仓储
		*type=1,跟踪，type=2，撤回
		*/
		tailOrRecallEvent(type){
			if(type == 1){
				//执行跟踪的时候，如果有在变更，则需要取消货确认变更之后才能做执行跟踪
				if(!this.cancelChangeDisabled || !this.confirmChangeDisabled){
					this.$message.error('正在编辑变更，请取消或确认变更再执行跟');
					return;
				}
				//执行跟踪的时候需要校验数据
				var dataIsPass = this.validateIsPass();
				if(!dataIsPass) return;
			}

			if(type == 1){
				this.tailDisabled = true;
				this.tailLoding = true;
			}else{
				this.recallWarehouseLoding = true;
				this.recallWarehouseDisabled = true;
			}
			//let url = '/afterSale-web/api/aftersale/bill/return/trackOrRecall?permissionCode='+(type == 1?'RETURN_TRACKING_ORDER_EXECUTION_TRACKING':'RETURN_TRACKING_ORDER_WITHDRAW_WAREHOUSE');
			let url = this.setBusinessPrower()[type==1?'execete':'recall'];
			let bool = type == 1?false:true
			let params = {
				id : this.form.id,
				oper_type : bool
			}

			this.ajax.postStream(url,params,res=>{
				let data = res.body;
				this.$message({
					type:data.result?'success':'error',
					message:data.msg
				});
				if(!data.result) {
					this.initData(this.form);
					return;
				}
				this.refreshAllData();
				this.upDateData();

			});
		},

		/**
		*关闭
		*type = 1为关闭，type=2 反关闭
		*/
		closeOrOpenEvent(type){
			let bool = type == 1?false:true;
			if(type == 1){
				this.closeDisabled = true;
				this.closeLoding = true;
			}else{
				//this.uncloseDisabled = true;
				this.uncloseLoding = true;
			}

			//let url = '/afterSale-web/api/aftersale/bill/return/closeOrOpen?permissionCode=RETURN_TRACKING_ORDER_CLOSE';
			let url = this.setBusinessPrower().close;
			let params = {
				id : this.form.id,
				oper_type : bool
			}
			this.ajax.postStream(url,params,res=>{
				let data = res.body;
				this.$message({
					type:data.result?'success':'error',
					message:data.msg
				});
				if(!data.result) {
					this.initData(this.form);
					return;
				}
				this.refreshAllData();
				this.upDateData();

			});
		},
		/**
		*保存校验
		*/
		validateIsPass(){
			let bool;
			this.$refs.form.validate((valid) => {
				bool = valid;
			});
			if(!bool) {
				//this.$message.error('提货点,收货人,地址为必填项');
				this.$message.error('收货人,地址为必填项');
				return false;
			}
			//当返货类型不为“客户自返”的时候，提货点为必填
			if(this.form.shipping_method !='CUSTOMER' && !this.form.delivery_name){
				this.$message.error('提货点,地址为必填项');
				return false;
			}

			bool = this.validateGoods();
			return bool;
		},
		/**
		*验证问题商品的编辑
		**/
		validateGoods(){
			let bool = true;
			//当返货类型经销退货,经销自提，退货再售，报废时，仓库可不验证
			if(this.form.shipping_method == 'DEALER' || this.form.shipping_method == 'DEALER_PICK' || this.form.shipping_method == 'RESALE' || this.form.shipping_method == 'SCRAP') return true;
			if(this.goodPriceText === '确认修改'){
				this.$message.error('请先确认修改添加包件价格')
				return false
			}
			// this.goodsList.map((a,b)=>{
			// 	let type = a.row_type;
			// 	let storage_id = a.storage_id
			// 	//
			// 	if(type != 'ROW_WAIT' && !storage_id){
			// 		bool = false;
			// 	}
			// });
			// if(!bool){
			// 	this.$message.error('当行类型不为返货待定时，请选择仓库');
			// }
			return bool;

		},

		/**
		*判断整个页面的数据是否有修改
		**/
		judgeIsChange(){
			var originalForm = this.getOriginalForm();
			var infoIsChange = this.compareData(this.form,originalForm);
			if(infoIsChange) return true;
			var originalGoods = this.getOriginalGoods();
			var goodsIsChange = this.compareData(this.goodsList,originalGoods);
			return goodsIsChange;

		},

		/**
		*设置表头数据
		**/
		setOriginalForm(){
			this.originalForm = JSON.stringify(this.form);
		},
		/**
		*设置商品信息
		*/
		setOriginalGoods(){
			this.originalGoods = JSON.stringify(this.goodsList || []);
		},
		/**
		*获取表头数据
		*/
		getOriginalForm(){
			var info = JSON.parse(this.originalForm);
			return info;
		},

		/**
		*获取商品信息
		*/
		getOriginalGoods(){
			var info = JSON.parse(this.originalGoods);
			return info;
		},

		/**
		*提交(撤回)单据
		*type = 1 提交,type=2 撤回
		*/
		submitOrRecallEvent(type){
			this.btnStatus = true
			if(type == 1){
				var bool = this.judgeHasSupplier();
				this.btnStatus = false
				if(!bool) return;
			}
			if(type == 2){
				//撤回
				this.submitOrRecall(type);
				return;
			}
			var isChange = this.judgeIsChange();
			if(!isChange){
				this.submitOrRecall(type);
				return;
			}
			this.saveEvent(()=>{
				this.submitOrRecall(type);
			})
		},
		/**
		**提交(撤回)单据
		*type = 1 提交,type=2 撤回
		**/
		submitOrRecall(type){
			//let url = '/afterSale-web/api/aftersale/bill/return/submitOrRetract?permissionCode='+(type == 1?'RETURN_TRACKING_ORDER_SUBMIT':'RETURN_TRACKING_ORDER_WITHDRAW');
			let url = this.setBusinessPrower()[type==1?'submit':'withdraw'];
			let bool = type == 1?false:true;
			let params = {
				id : this.form.id,
				oper_type : bool
			}

			this.ajax.postStream(url,params,res=>{
				this.btnStatus = false
				let data = res.body;
				this.$message({
					type:data.result?'success':'error',
					message:data.msg
				});
				if(!data.result) {
					this.initData(this.form);
					return;
				}
				this.refreshAllData();
				this.upDateData();

			});
		},
		/***
		*锁定或解锁单据
		*type = 1 锁定，type=2 解锁
		**/
		lockOrDeblockEvent(type){
			if(type == 1){
				this.lockDisabled = true;
				this.lockLoding = true;
			}else{
				this.deblockDisabled = true;
				this.deblockLoding = true;
			}
			this.btnStatus = true
			//let url = '/afterSale-web/api/aftersale/bill/return/lockOrUnlock?permissionCode='+(type== 1?'RETURN_TRACKING_ORDER_LOCK':'RETURN_TRACKING_ORDER_UNLOCK');
			let url = this.setBusinessPrower()[type==1?'lock':'unLock'];
			let bool = type == 1?false:true;
			let params = {
				id : this.form.id,
				oper_type : bool
			}
			this.ajax.postStream(url,params,res=>{
				this.btnStatus = false
				let data = res.body;
				this.$message({
					type:data.result?'success':'error',
					message:data.msg
				});
				if(!data.result) {
					this.initData(this.form);
					return;
				}
				this.refreshAllData();
				this.upDateData();

			});
		},
		/**
		*刷新所有数据
		**/
		refreshAllData(isTip){
			this.getDetail(isTip?true:false);
			this.getOperateList();//操作记录
			this.getScheduleList();//三包处理进度
		},
		/**
		*选择客户地址
		**/
		/*choiceAddress(){
			let params = {
				isAlert:true,
				type:'radio',
				cust_id:this.form.custom_id
			};
			params.callback = (d)=>{
				this.fillAddressInfo(d.data[0]);
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_invoices/addresslist'),
				style:'width:80%;height:560px',
				title:'选择客户地址'
			});
		},*/
		/**
		*填充地址信息
		**/
		fillAddressInfo(data){
			if(!data) return;
			this.form.province = data.receiver_state;
			this.form.city = data.receiver_city;
			this.form.area = data.receiver_district;
			this.form.street = data.receiver_street;
			this.form.receiver_addr = data.receiver_address;
			this.form.receiver_name = data.receiver_name;

			this.form.reveiver_phone = data.receiver_mobile ;//手机号是必填项
			//TODO,还有一些东西需要填充
		},
		/**
		*上传附件
		*/

		uploadFun() {
			this.ifClickUpload = true;

			this.uploadData = {

				parent_no: this.form.after_order_no,//售后单号
				child_no:this.form.bill_returns_no,
				parent_name : 'AFTER_ORDER',
				child_name:'RETURNANDEXCHANGE',
				parent_name_txt: '售后单',
				child_name_txt:'退换货申请单',
				content: JSON.parse(JSON.stringify(this.form || {})),
				//permissionCode:'RETURN_TRACKING_ORDER_ATTACHMENT_UPLOAD'//权限控制的

			}
			//重置，防止第二次不能触发
			setTimeout(() => {
				this.ifClickUpload = false;
			},100)

		},
		/**
		*查看附件
		*/
		pictureFun() {
			var params = {
				parent_no : this.form.after_order_no,//售后单号
				//child_no:this.form.bill_returns_no,
				child_no:null,
				ext_data : null,
				parent_name : 'AFTER_ORDER',
				child_name:'RETURNANDEXCHANGE',
				//child_name:null,

				parent_name_txt: '售后单',
				//child_name_txt:'退换货申请单',
				notNeedDelBtn:this.submitDisabled,
				nickname: this.form.buyer_name,
				mergeTradeId: this.form.merge_trade_id,
				//permissionCode:'RETURN_TRACKING_ORDER_ATTACHMENT_VIEW'//权限控制的
			};
			params.callback = d=>{
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
				style:'width:1000px;height:600px',
				title:'下载列表'
			});
		},
		/**
		 * 获取接口信息-选择的行
		 */
		radioChange(val) {
            this.apiInfoMultipleSelection = val;
			let canIreSend=val.push_bill_type=='4PL入库单'&&val.interface_status==-1
			this.reSendDisabled=!canIreSend;
        },
		/**
		 * 4PL重推
		 */
		reSendWarehouse(){
			let id=this.apiInfoMultipleSelection.id
			this.reSendLoading=true
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/return/reSendWarehouse?permissionCode=AFTER_RESEND_WAREHOUSE',id,res=>{
				if(res.body.result){
					this.$message.success(res.body.msg)
				}else{
					this.$message.error(res.body.msg)
				}
				this.reSendLoading=false
				this.apiInfoMultipleSelection={}
				this.$refs.apiMessage.clearRadioSelect();
			},err=>{
				this.$message.error(err)
				this.reSendLoading=false
				this.apiInfoMultipleSelection={}
				this.$refs.apiMessage.clearRadioSelect();
			})
		},
		// 上一页 or 下一页
		nextOrPrevOrder (type){
			let paramslist=[];
			let obj = {};
			paramslist = this.params.orderList.reduce(function (item, next) {
				obj[next.id] ? '' : next.id&&(obj[next.id] = true && item.push(next));
				return item;
			}, []);
			paramslist.some((obj, index) => {
				if(obj.id === this.params.id){
					let newOrder = paramslist[index + (type === 'next' ? 1 : -1)]
					if(newOrder){
						this.params.id=newOrder.id
						this.initData(newOrder || {})
						this.refreshAllData(true)
					}else {
						this.$message.error('没有更多')
					}
					return true
				}
			})
		},
		/**
		*获取表头信息
		**/
		getDetail(bool){
			var id = this.form.id;
			if(!id) return;
			var url = this.setBusinessPrower().getDetailUrl;
			//var url = '/afterSale-web/api/aftersale/bill/return/detail?permissionCode=RETURN_TRACKING_ORDER_QUERY';
			var params = {
				id:id
			};
			this.ajax.postStream(url,params,res=>{
				var data = res.body;
				var errorMsg = bool?data.msg:!data.result?data.msg:'';
				if(errorMsg){
					this.$message({
						type:data.result?'success':'error',
						message:data.msg
					})
				}
				if(!data.result) return;
				this.selectGoods = [];
				//重新赋值
				let goods = data.content.listAftersalePlanReturnsGoods || [];
				//问题反馈单号获取
        		Vue.prototype.tabNo[Vue.prototype.activeTab] = data.content.bill_returns_no || '';
                let pageParams = {
                    id: data.content.id
                }
                Vue.prototype.tabId[Vue.prototype.activeTab] = JSON.stringify(pageParams);
				this.goodsList = goods; this.getGoodsItem(goods);
				//添加合计
				const actPriceSum=this.goodsList.reduce((acc,item)=>{
					return acc+item.act_price
				},0).toFixed(2)
				this.goodsList.push({
					act_price:`合计：${actPriceSum}`
				})
				this.initData(data.content||{});
				this.getRefundingPaymentList();
				this.getMaterialListByNumbers();
				this.getPurDealerOrgRrturnNo();

				//是否安装设置默认值
				this.goodsList.map((a,c)=>{
					a.if_installed?'':a.if_installed = 'N';

					//行类型只有在变更的时候才可以编辑
					a.conditionDisabled = true;
					//还得判断是否已关闭，如果是已关闭，整行明细都不能编辑
					a.isNotEdit = a.row_status == 'Y'?true:!this.submitDisabled?false:true;
					//仓库行类型，数据可编辑的情况
					//1:在单据状态为保存，提交，撤回的时候
					//2:变更的时候
					a.canNotEditStorage = a.isNotEdit;//仓库
					//a.conditionDisabled = a.row_status == 'Y'?true:!this.saveDisabled || !this.submitDisabled || !this.changeDisabled?false:true;//行类型

				});

				this.setOriginalGoods();

			})
		},
		getPurDealerOrgRrturnNo(){
			let self = this;
			let list = [];
			self.goodsList.forEach(item=>{
				if(item.shipping_method == 'PUR_DEALER'){
					list.push(item.cause_good_id)
				}
			});
			if(!!list.length){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/return/getPurDealerOrgRrturnNo',{id:this.form.id,ids:list},res=>{
					if(res.body.result){
						let newList = JSON.parse(JSON.stringify(self.goodsList))
						res.body.content.forEach(item=>{
							newList.forEach(bItem=>{
								if(item.rowId == bItem.cause_good_id){
									bItem.billNo = item.billNo;
								}
							})
						})
						self.goodsList = newList;
					}
				})
			}
		},
		// 根据售后单号获取三包商名称以及三包商编码嵌入到问题商品列表
		getGoodsItem(goods){
			let self = this;
			let list = [];
			goods.forEach(item=>{
				if(!item.bill_service_no||new Set(list).has(item.bill_service_no)){
					return false;
				}
				list.push(item.bill_service_no);
			});
			if(!list.length){
				return;
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/service/getThreeSupplyByNo',list,res=>{
				if(res.body.result&&!!res.body.content.length){
				let goodsList = JSON.parse(JSON.stringify(self.goodsList))
				res.body.content.forEach(item=>{
						goodsList.forEach(goodItem=>{
							if(goodItem.bill_service_no == item.after_ticket_no){
								goodItem.three_supplier = item.three_supplier;
								goodItem.three_supplier_code = item.three_supplier_code;
							}
						});
					})
				self.$nextTick(()=>{
					self.goodsList = goodsList;
				});
				}
			})

		},
		// 获取商品对应的支付明细
		getRefundingPaymentList () {
			let mergeMaterialIds = [], self = this
			this.goodsList.forEach(refunding => {
				mergeMaterialIds.push(refunding.merge_material_id)
			})
			let params = {
				mergeMaterialIds: mergeMaterialIds
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/plan/getRealStcok',params,(res)=>{
				if (!res.body.result) {
					self.$message.error(res.body.msg)
					return
				}
				if (res.body.content) {
					self.refundingPaymentList = res.body.content
				} else {
					self.refundingPaymentList = []
				}
			}, err => {
				self.$message.error(err)
			})
		},
		getMaterialListByNumbers(){
			let mergeMaterial = [], self = this
			this.goodsList.forEach(refunding => {
				mergeMaterial.push(refunding.material_code)
			})
			let params =  mergeMaterial;

			this.ajax.postStream('/material-web/api/material/getMaterialListByNumbers',params,(res)=>{
				if (!res.body.result) {
					self.$message.error(res.body.msg)
					return
				}
				if (res.body.content) {
					self.refundingMaterialList = res.body.content
				} else {
					self.refundingMaterialList = []
				}
			}, err => {
				self.$message.error(err)
			})


			// self.ajax.postStream('/material-web/api/material/getMaterialListByNumbers',[data.material_code],(res)=>{
			// if (!res.body.result || !res.body.content.length) {
			// 	self.$message.error("没有找到该物料对应分组");
			// 	return;
			// }
			// reslove&&reslove(res.body.content[0]);
			// });
		},
		/**
		*判断是否有入库单号
		***/
		judgeHasStorage(){
			let goods = this.goodsList || [];
			if(!goods.length) return false;
			let bool = false;

			goods.map((a,b)=>{
				//有一个有明细行，则有入库单
				if(a.bill_godownenty_no &&　a.row_status == 'N'){//未关闭行的入库单
					bool = true;
				}
			});
			return bool;
		},
		/**
		*更新列表数据
		**/
		upDateData(){
			this.$root.eventHandle.$emit('updateAfterSaleList');
		},

		/**
		*获取保存商品明细行
		**/
		getGoodsOfSave(goodsList){
			var list = [];

			;(goodsList || this.goodsList).map((a,b)=>{
				var c = JSON.parse(JSON.stringify(a));
				if(c.hasOwnProperty('originalId')){
					delete c.originalId;
				}
				if(c.hasOwnProperty('originalType')){
					delete c.originalType;
				}
				if(c.hasOwnProperty('conditionDisabled')){
					delete c.conditionDisabled;
				}
				if(c.hasOwnProperty('isNotEdit')){
					delete c.isNotEdit;
				}
				if(c.hasOwnProperty('canNotEditStorage')){
					delete c.canNotEditStorage;
				}
				if(c.hasOwnProperty('popName')){
					delete c.popName;
				}
				if(c.hasOwnProperty('billNo')){
					delete c.billNo;
                }
                if(c.hasOwnProperty('three_supplier')){
					delete c.three_supplier;
                }
                if(c.hasOwnProperty('three_supplier_code')){
					delete c.three_supplier_code;
				}
				list.push(c);
			});
			list=list.filter(item=>item.question_goods_code)
			return list;
		},
		/**
		*获取要保存的数据
		**/
		getDataOfSave(){
			var data = this.getInfo();
			data.listAftersalePlanReturnsGoods = this.getGoodsOfSave();
			return data;
		},
		/***
		*1：当经销商用户，且返货类型为“经销退货”时，所有按钮的权限去掉
		*2：林氏用户，且返货类型不为“经销退货”，按钮权限不能去掉
		***/
		setBusinessPrower(){
			//let isDealer = this.getUserInfo().isDealerUser;
			let isDealer = /*this.personBusinessAttribute.attributeValue?true:*/false;
			//获取详情
			let getDetailUrl = '/afterSale-web/api/aftersale/bill/return/detail'+(!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_QUERY':'');
			//保存接口
			let save = '/afterSale-web/api/aftersale/bill/return/update' + (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_SAVE':'');
			//锁定
			let lock = '/afterSale-web/api/aftersale/bill/return/lockOrUnlock' + (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_LOCK':'');

			//解锁
			let unLock = '/afterSale-web/api/aftersale/bill/return/lockOrUnlock' + (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_UNLOCK':'');

			//驳回
			let reject = '/afterSale-web/api/aftersale/bill/return/reject'+ (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_REJECT':'');
			//提交
			let submit = '/afterSale-web/api/aftersale/bill/return/submitOrRetract'+ (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_SUBMIT':'');
			//撤回
			let withdraw = '/afterSale-web/api/aftersale/bill/return/submitOrRetract'+ (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_WITHDRAW':'');
			//审核
			let approve = '/afterSale-web/api/aftersale/bill/return/audit'+ (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_AUDIT':'');
			//执行跟踪
			let execete = '/afterSale-web/api/aftersale/bill/return/trackOrRecall'+ (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_EXECUTION_TRACKING':'');
			//关闭
			let close = '/afterSale-web/api/aftersale/bill/return/closeOrOpen'+ (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_CLOSE':'');
			//撤回仓储
			let recall =  '/afterSale-web/api/aftersale/bill/return/trackOrRecall'+ (!isDealer?'?permissionCode=RETURN_TRACKING_ORDER_WITHDRAW_WAREHOUSE':'');

			return {
				save:save,//保存接口
				getDetailUrl:getDetailUrl,
				unLock:unLock,
				lock:lock,
				reject:reject,
				submit:submit,
				withdraw:withdraw,
				approve:approve,
				execete:execete,
				close:close,
				recall:recall
			}

		},
		/**
		*保存单据
		**/
		saveEvent(callback,isTip){
			//TODO
			let bool = this.validateIsPass();
			if(!bool) return;
			var isChange = this.judgeIsChange();
			if(!isChange){
				callback && callback();
				if(!callback){
					this.$message.error('没有修改，不需要进行保存哦');
				}
				return;
			}
			this.saveLoding = true;
			this.saveDisabled = true;
			/*var data = this.getInfo();
			data.listAftersalePlanReturnsGoods = this.getGoodsOfSave();*/
			var data = this.getDataOfSave();
			let i = arguments.length;
			let tipBool = !i?true:i == 2?isTip:false;
			let url = this.setBusinessPrower().save;

			this.ajax.postStream(url/*'/afterSale-web/api/aftersale/bill/return/update?permissionCode=RETURN_TRACKING_ORDER_SAVE'*/,data,res=>{
				var d = res.body;
				this.saveLoding = false;
				this.saveDisabled = false;

				if(tipBool){
					//没有回调函数，或是指明了需要提示
					this.$message({
						type:d.result?'success':'error',
						message:d.msg
					});
				}

				if(!d.result) return;
				this.getDetail();
				this.getOperateList();
				this.getScheduleList();
				callback && callback();

			})
		},

		/**
		*关闭标签页面
		***/
		closeComponent:function(){

           let bool = this.judgeIsChange();
           var _this = this;
           if(this.submitDisabled || !bool){
               this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
               return;
		   }

            this.$root.eventHandle.$emit('openDialog',{
                ok(){
                    _this.saveEvent(()=>{
                        _this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
                    },!0)
                },
                no(){
                    _this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
                }
            });
		},
		/**
		*获取表头信息
		**/
		getInfo(){
			var data = Object.assign({},this.form);
			data.if_three_age = data.if_three_age?1:0;
			data.return_rate = data.return_rate?1:0;
			return data;
		},
		/**
		*选择省份，改变对应的城市
		*/
		changeProvice(){
			let code = this.form.province;
			if(!code) return;
			this.getAddress(code,(data)=>{
				this.city = '';
				this.city = data || {};
				let city = this.form.city;
				if(!data ||  !data.hasOwnProperty(city)){
					this.form.city = null;
					this.form.area = null;
					this.form.street = null;
				}
			});
		},
		/**
		*选择城市，改变对应的区域
		*/
		changeCity(){
			let code = this.form.city;
			if(!code) return;
			this.getAddress(code,(data)=>{
				this.area = '';
				this.area = data || {};
				let area = this.form.area;
				if(!data || this.isEmptyObject(data)){
					//没有第三级
					this.form.area = null;
					this.form.street = null;
					//this.getPathInfo();
				}else{
					//有第三级
					if(!data.hasOwnProperty(area)){//有，但是不是这个查询里面
						this.form.area = null;
						this.form.street = null;
					}
				}

			});
		},
		/**
		*选择区域，改变对应的街道
		*/
		changeArea(){
			let code = this.form.area;
			if(!code) return;

			this.getAddress(code,(data)=>{
				this.street = '';
				this.street = data || {};
				let street = this.form.street;
				//this.getPathInfo();
				if(!data || this.isEmptyObject(data)){
					this.form.street = null;

				}else{
					if(!data.hasOwnProperty(street)){//第四级地址已存在
						this.form.street = null;
					}
				}
			});
		}

	},
	mounted() {
		this.initData(this.params || {});
		// this.form.id='11972107698177'
		this.setColData();
		this.refreshAllData(true);

		//this.getDisabledInfoByRoleAndReturnType();

		//this.otherInfoByUrl();
		//获取省份城市
		this.getAddress((data)=>{
			this.provice = '';
			this.provice = data;
		});
		this.params.__close = this.closeComponent;

		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
			this.initBtnStatus();
		});
	}
}
</script>
<style type="text/css">
	/**
	*自定义的全居css
	*/
	.el-table tr.pmm_red, .el-table--striped .el-table__body tr:nth-child(2n).pmm_red td{color: red;background: antiquewhite;}
	.el-table tr.pmm_gray, .el-table--striped .el-table__body tr:nth-child(2n).pmm_gray td{color: gray;background: #abbdd2;}
	.el-button--primary a{color: #fff;}
	*:disabled[type="button"] a{color: #bfcbd9 !important}
	.pmm_textareaInput{height: auto;}
	.pmm_textareaInput.el-form-item .el-form-item__label, .pmm_textareaInput.el-form-item .el-form-item__content{height: auto;}
	.pmm_textareaInput .el-textarea .el-textarea__inner{overflow-x: hidden;}
	.el-table .sum-row {
		background: #c9e5f5;
	}
	.el-table .sum-row .pmm_input {
		display: none;
	}
	.el-table .sum-row .el-checkbox {
		display: none;
	}
</style>

<style module>
.which-row-click td,
:global(.el-table__body) tr:global(.el-table__row--striped).which-row-click td,
:global(.el-table--enable-row-hover) tr.which-row-click:hover>td {
	background-color: #abbdd2;
}
</style>


