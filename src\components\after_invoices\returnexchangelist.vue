<!-- 退换货列表 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='col' 
		:pageTotal='count' 
		selection='checkbox'
		@row-dblclick='rowDblclick'
		:searchPage='search.page_name' 
		:selectable="row => row.id"
		@selection-change='select'
		@search-click='searchClick' 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange'
		ref='xptList'
	></xpt-list>
</template>
<script>
export default {
	props: ['params'],
	data() {
		let self = this
		return {
			list: [],
			selectedData:[],//选择的数据
			deleLoding:false,
			refreshLoding:false,
			deleDisabled:false,
			refreshDisabled:false,
			btns: [{
				type: 'danger',
				txt: '删除',
				disabled:false,
				loading:false,
				click: ()=>{
					self.del();
				}
			},{
				type: 'primary',
				txt: '刷新',
				disabled:false,
				loading:false,
				click: ()=>{
					self.refresh();
				}
			},{
				type: 'primary',
				txt: '查看附件',
				
				click: ()=>{
					self.searchFile();
				}
			}],
			col: [
				{
					label: '单据编号',
					prop: 'bill_returns_no',
					width:'150',
					redirectClick(row) {
						self.openDetail(row);
					}
				},{
					label: '合并单号',
					prop: 'merge_trade_no',
					width:'180',
				}, {
					label: '买家昵称',
					width:'120',
					prop: 'buyer_name'
				},{
					label: '单据状态',
					width:'80',
					prop: 'status',
					formatter:(val)=>{
						let status = {
							CREATE:'创建',
							APPROVING:'审核中',
							EXECUTING:'执行跟踪',
							APPROVED:'已审核'
						}
						return status[val] || val;
					}
				},{
					label: '是否经销商订单',
					width:'100',
					prop: 'if_dealer',
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
				},{
					label: '创建人',
					width:'80',
					prop: 'creator_name'
				},{
					label: '创建人分组',
					width:'80',
					prop: 'creator_group_name'
				}, {
					label: '实际售价',
					width:'150',
					prop: 'act_price'
				},{
					label: '业务状态',
					width:'80',
					prop: 'business_status',
					formatter:(val)=>{
						let status = {
							UN_LOCK:'未锁定',
							LOCKED:'已锁定',
							ESTIMATE:'押金估算',
							EXECUTE:'执行跟踪中',
							EXECUTED:'执行跟踪完成'
						}
						return status[val] || val;
					}
				},{
					label: '创建日期',//
					width:'150',
					prop: 'create_time',
					format:'dataFormat1'
				},/*{
					label: '提交时间',
					width:'150',
					prop: '',
					format:'dataFormat1'
				},{
					label: '单据执行跟踪时间',
					width:'150',
					prop: '',
					format:'dataFormat1'
				},*/{
					label: '关闭状态',
					width:'100',
					prop: 'close_status',
					formatter:(val)=>{
						return val == 'Y'?'已关闭':val == 'N'?'未关闭':val;	
					}
				},{
					label: '关闭时间',
					width:'100',
					prop: 'close_time',
					format:'dataFormat1'
				}, {
					label: '是否拉货',
					width:'100',
					prop: 'if_pickup',

					formatter:(val)=>{
						return val == 'Y'?'已拉货':val == 'N'?'未拉货':val;	
					}
				},{
					label: '实际到货时间',
					width:'100',
					prop: 'actual_reach_time',
					format:'dataFormat1'
				},{
					label: '是否算退货率',
					width:'100',
					prop: 'return_rate',
					formatter:(val)=>{
						return val == 1?'是':'否';
					}
				},{
					label: '商品编码',//
					width:'150',
					prop: 'question_goods_code'
				}, {
					label: '商品名称',//
					width:'150',
					prop: 'question_goods_name'
				}, {
					label: '规格描述',//
					width:'200',
					prop: 'question_goods_desc'
				}, {
					label: '物料编码',
					width:'150',
					prop: 'material_code'
				}, {
					label: '物料名称',
					width:'150',
					prop: 'material_name'
				},{
					label: '物料规格描述',
					prop: 'material_desc',
					width: 100
				},{
					label: '单位',
					width:'80',
					prop: 'units'
				},{
					label: '退货数量',
					width:'80',
					prop: 'number'
				},{
					label: '退货仓库',
					width:'100',
					prop: 'returns_storage'
				}
			],

			/*searchList:{
				buyer_name:'买家昵称',
				bill_returns_no:'单据编号',
				after_order_no:'售后单号',
				merge_trade_no:'合并订单号',
				create_time:'日期',
				staff_name:'业务员',
				returns_type:'退货方式',
				//if_scrap:'是否报废',
				inventory_category:'存货类别',
				material_code:'物料编码',
				material_name:'物料名称',
				material_desc:'规格描述',
				question_goods_code:'商品编码',
				question_goods_name:'商品名称',
				exception_type:'异常类型',
				status:'行状态',
				return_rate:'是否算退货率'
			},*/
			//operators:{'=':"等于",'%':"包含"},
			search: {
	          page_no: 1,
	          page_size: self.pageSize,
	          page_name: 'aftersale_bill_returns',
	          where: []
	        },
			/*search: {
				page: {
					pageNo: 1,
					length: self.pageSize,
					field_value:null,
					limit_field:null,
					operator:null
					
				}
			},*/
			count: 0
		}
	},
	methods: {
		/**
		*重置参数
		**/
		/*resetClick(){
			var d = this.search.page;
			d.pageNo = 1;
			d.field_value = null;
			d.limit_field = null;
			d.operator = null;
		},*/
		searchClick(where,reslove) {
			this.search.where = where
			this.getList(true,reslove)
		},
		pageSizeChange(ps) {
			this.search.page_size = ps
			this.getList()
		},
		pageChange(page) {
			this.search.page_no = page
			this.getList()
		},
		openDetail(row) {
			let params = row;
			this.$root.eventHandle.$emit('creatTab', {
				name: '退货跟踪单详情',
				params: params,
				component: () => import('@components/after_invoices/returnexchangedetail')
			})
		},
		/**
		*
		***/
		rowDblclick(row){
			if(row.id){
				this.openDetail(row);
				console.log('row',row);
			}
		},
		/**
		*请求设置转动
		*index为btns里面元素的下标
		*bool, 是否需要转动
		***/
		/*setLoadingOfBtn(index,bool){
			var btn = this.btns[index];
			btn.disabled = bool;
			if(!btn || !btn.hasOwnProperty('loading')) return;
			btn.loading = bool;
		},*/
		/***
		*重新刷新数据
		**/
		refresh(){
			//this.setLoadingOfBtn(1,!0);
			this.refreshDisabled = true;
			this.refreshLoding = true;
			this.getList();
		},
		/**
		*获取列表数据
		**/
		getList(bool,callback){
			console.log('6666');
			
			/*var searchParams = {
				"field_value": this.search.page.field_value || null, 
			    "limit_field": this.search.page.limit_field || null, 
			    "operator": this.search.page.operator || null, 
			    "page_no": this.search.page.pageNo, 
			    "page_size": this.search.page.length
			}*/
			
			let url = '/afterSale-web/api/aftersale/bill/returns/billReturnsList?permissionCode=RETURN_TRACKING_ORDER_QUERY'
			this.ajax.postStream(url/*'/afterSale-web/api/aftersale/bill/returns/billReturnsList?permissionCode=RETURN_TRACKING_ORDER_QUERY'*/,this.search, res => {
				//this.setLoadingOfBtn(1,!1);
				//需要清空已选择的数据
				this.selectedData = [];
				this.setAllBtn();
				if(res.body.result && res.body.content) {
					if((res.body.content.list || []).length){
						res.body.content.list.push({
							act_price: '合计' + res.body.content.list.reduce((a, b) => a + (b.act_price || 0), 0).toFixed(2)
						})
					}

					this.list = res.body.content.list || [];
					this.count = res.body.content.count;
				}
				if(!res.body.result && bool){
					this.$message({
						type:'error',
						message:res.body.msg
					});
				}
				callback && callback();
				
			}, null, this.params.tabName)
		},
		/**
		*所有按钮恢复原来的设置
		*/
		setAllBtn(){
			this.refreshDisabled = false;
			this.refreshLoding = false;
			this.deleLoding = false;
			this.deleDisabled = false;
		},
		/**
		*删除
		**/
		del(){
			var data = this.selectedData;
			if(!data || !data.length){
				this.$message.error('请选择要删除的行数据');
				return;
			}
			this.deleLoding = true;
			this.deleDisabled = true;
			//this.setLoadingOfBtn(0,!0);
			var deleteList = [];
			var canNotDelete = '';
			data.map((a,b)=>{
				//TODO,条件判断的删除
				let id = a.id;
				deleteList.push(id);
			});
			if(canNotDelete){
				this.$message.error(canNotDelete.question_goods_code + '不能删除');
				return;
			}
			deleteList = Array.from(new Set(deleteList));
			let url = '/afterSale-web/api/aftersale/bill/returns/delete?permissionCode=RETURN_TRACKING_ORDER_DELETE'
			this.ajax.postStream(url/*'/afterSale-web/api/aftersale/bill/returns/delete?permissionCode=RETURN_TRACKING_ORDER_DELETE'*/,deleteList,res=>{
				let d = res.body;
				this.deleLoding = false;
				this.deleDisabled = false;
				this.$message({
					type:d.result?'success':'error',
					message:d.msg
				});
				if(!d.result) return;
				this.getList();

			});

		},
		/**
		*查看附件
		**/
		searchFile(){
			var data = this.selectedData;
			if(!data  || data.length != 1){
				let error = data.length?'最多只能选择一行数据':'请选择数据';
				this.$message.error(error);
				return;
			}
			var params = {
				parent_no : data[0].after_order_no,//售后单号
				//child_no:data[0].bill_returns_no,
				child_no:null,
				
				parent_name : 'AFTER_ORDER',
				child_name:'RETURNANDEXCHANGE',
				parent_name_txt: '售后单',
				//child_name_txt:'退换货申请单',
				ext_data : null,
				notNeedDelBtn:true,
				permissionCode:'RETURN_TRACKING_LIST_ATTACHMENT_VIEW'//权限控制的
			};
			params.callback = d=>{
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/common/download.vue'),
				style:'width:1000px;height:600px',
				title:'下载列表'
			});

		},
		/**
		*选择
		**/
		select(selects){
			this.selectedData = selects.length?selects:[];
		}
	},
	mounted() {
		this.$refs.xptList.$refs.xptSearchEx.filterFields = fields => fields.filter(o => !/(业务状态|关闭状态)/.test(o.comment))
		this.getList();
		this.$root.eventHandle.$on('updateAfterSaleList',()=>{
            this.getList(!0);
        });
	},
}
</script>
<style type="text/css">
	.pmm_select{width: 80px;}
	.pmm_select .el-select .el-input{width: 100%;}
</style>
