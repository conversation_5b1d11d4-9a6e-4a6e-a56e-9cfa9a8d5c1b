<!-- 退换货列表 -->
<template>
	<xpt-list-dynamic
		:data='list'
		:btns='btns'
		:colData='col'
		:pageTotal='count'
		selection='checkbox'
		@row-dblclick='rowDblclick'
		:searchPage='search.page_name'
		:selectable="row => row.id"
		:taggelClassName='tableRowClassName'
		@selection-change='select'
		@search-click='searchClick'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
		@count-off="countOff"
		:showCount ='showCount'
		ref='xptList'
		:filterList="filterList"
	>
  <template slot="btns">
    <xpt-upload-v3
      uploadBtnText="导入"
      :uploadSize="20"
      acceptTypeStr=".xlsx,.xls"
      :dataObj="uploadDataObj"
      :disabled="false"
      :ifMultiple="false"
      :showSuccessMsg="false"
      @uploadSuccess="uploadSuccess"
      btnType="warning" style="display: inline-block; margin: 0px 10px">
    </xpt-upload-v3>
    <el-button size="mini" type="warning" @click="downloadTemplate()" :disabled="false">导入模版下载</el-button>
    <el-button size="mini" type="warning" @click="showUploadResult()" :disabled="false">查看导入结果</el-button>
  </template>
	</xpt-list-dynamic>
</template>
<script>
export default {
	props: ['params'],
	data() {
		let self = this
		return {
			countOffFlag:false,
			showCount:false,
			list: [],
			selectedData:[],//选择的数据
			deleLoding:false,
			refreshLoding:false,
			deleDisabled:false,
			refreshDisabled:false,
			uploadUrl:'/reports-web/api/reports/afterSaleExport/exportAftersaleReturn',
      uploadDataObj: {
        parent_name: "售后单退换货方案导入",
        parent_no: `EXCEL_TYPE_AFTERSALE_RETURNS_IMPORT`, //主要通过该参数获取附件列表
        child_name: null,
        child_no: null,
        content: {},
      },
			btns: [/*{
				type: 'danger',
				txt: '删除',
				disabled:false,
				loading:false,
				click: ()=>{
					self.del();
				}
			},*/{
				type: 'primary',
				txt: '刷新',
				disabled:false,
				loading:false,
				click: ()=>{
					self.refresh();
				}
			},{
				type: 'primary',
				txt: '查看附件',

				click: ()=>{
					self.searchFile();
				}
			},{
				type: 'primary',
				txt: '生成经销采购退货总单',

				click: ()=>{
					self.savePurDealer();
				}
			},{
				type: 'primary',
				txt: '审单加急',

				click: ()=>{
					self.urgentRerurnsOrder();
				}
			},{
					type: "success",
					txt: "导出",
					click: this.export
				},
				{
					type: "success",
					txt: "导出文件下载",
					disable(){
						return self.params.menuInfo.code === "REFUND_APPLY_DETAIL_LIST"
					},
					click: this.showExportList
				},
      ],
			col: [
				{
					label: '单据编号',
					prop: 'bill_returns_no',
					width:'150',
					redirectClick(row) {
						self.openDetail(row);
					}
				},{
					label: '合并单号',
					prop: 'merge_trade_no',
					width:'180',
				}, {
					label: '买家昵称',
					width:'120',
					prop: 'buyer_name'
				},{
					label: '单据状态',
					width:'80',
					prop: 'status',
					formatter:(val)=>{
						let status = {
							CREATE:'创建',
							APPROVING:'审核中',
							EXECUTING:'执行跟踪',
							APPROVED:'已审核'
						}
						return status[val] || val;
					}
				},{
					label: '是否经销商订单',
					width:'100',
					prop: 'if_dealer',
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
				},{
					label: '是否加急',
					width:'100',
					prop: 'if_audit_urgent',
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
				},{
					label: '经销商名称',
					width:'100',
					prop: 'dealer_customer_name'
				},{
					label: '店铺地区',
					format: 'auxFormat',
					prop: 'shop_area',
					formatParams: 'shopArea'
				},{
					label: '创建人',
					width:'80',
					prop: 'creator_name'
				},{
					label: '创建人分组',
					width:'80',
					prop: 'creator_group_name'
				}, {
					label: '实际售价',
					width:'150',
					prop: 'act_price'
				}, {
                    label:'经销采购退货申请状态',
                    prop: 'pur_dealer_status',
                    formatter(val) {
                        switch (val) {
                                        case "WAIT": return "未申请";                                        case "WAITAUDIT": return "待审核";                                        case "REJECT": return "驳回";                                        case "AUDIT": return "已审核";                                        case "DEPOSITTIN": return "充值中";                                        case "DEPOSITED": return "已充值";                                        case "CANCLE": return "已取消";                        default: return val;                        }
                    }
                }/*,{
					label: '业务状态',
					width:'80',
					prop: 'business_status',
					formatter:(val)=>{
						let status = {
							UN_LOCK:'未锁定',
							LOCKED:'已锁定',
							ESTIMATE:'押金估算',
							EXECUTE:'执行跟踪中',
							EXECUTED:'执行跟踪完成'
						}
						return status[val] || val;
					}
				}*/,{
					label: '创建日期',//
					width:'150',
					prop: 'create_time',
					format:'dataFormat1'
				},
        {
					label: '业务锁定人',//
					width:'150',
					prop: 'staff_locker_name',
				},
        {
					label: '业务锁定人分组',//
					width:'150',
					prop: 'staff_locker_group_name',
				},

        /*{
					label: '提交时间',
					width:'150',
					prop: '',
					format:'dataFormat1'
				},{
					label: '单据执行跟踪时间',
					width:'150',
					prop: '',
					format:'dataFormat1'
				},*//*{
					label: '关闭状态',
					width:'100',
					prop: 'close_status',
					formatter:(val)=>{
						return val == 'Y'?'已关闭':val == 'N'?'未关闭':val;
					}
				},{
					label: '关闭时间',
					width:'100',
					prop: 'close_time',
					format:'dataFormat1'
				},*/ {
					label: '是否拉货',
					width:'100',
					prop: 'if_pickup',

					formatter:(val)=>{
						return val == 'Y'?'是':val == 'N'?'否':val;
					}
				},{
					label: '拉货到货时间',
					width:'100',
					prop: 'received_time',
					format:'dataFormat1'
				},{
					label: '实际到货时间',
					width:'100',
					prop: 'actual_reach_time',
					format:'dataFormat1'
				},{
					label: '是否算退货率',
					width:'100',
					prop: 'return_rate',
					formatter:(val)=>{
						return val == 1?'是':val == 0?'否':'';
					}
				},{
					label: '商品编码',//
					width:'150',
					prop: 'question_goods_code'
				}, {
					label: '商品名称',//
					width:'150',
					prop: 'question_goods_name'
				}, {
					label: '规格描述',//
					width:'200',
					prop: 'question_goods_desc'
				}, {
					label: '物料编码',
					width:'150',
					prop: 'material_code'
				}, {
					label: '物料名称',
					width:'150',
					prop: 'material_name'
				},{
					label: '物料规格描述',
					prop: 'material_desc',
					width: 100
				},{
					label: '单位',
					width:'80',
					prop: 'units'
				},{
					label: '退货数量',
					width:'80',
					prop: 'number'
				},{
					label: '退货仓库',
					width:'100',
					prop: 'returns_storage'
				},{
					label: '业务员分组',
					width:'100',
					prop: 'staff_group'
				},{
					label: '业务员',
					width:'100',
					prop: 'staff_name'
				},{
					label: '完结时间',
					format:'dataFormat1',
					width:'100',
					prop: 'finish_time'
				},{
					label: '返货类型',
					width:'100',
					prop: 'shipping_method'
				},{
					label: '审核人',
					width:'100',
					prop: 'auditor_name'
				}
			],

			search: {
	          page_no: 1,
	          page_size: self.pageSize,
	          page_name: 'aftersale_bill_returns',
	          where: []
	        },
			count: 0,
			filterList: []
		}
	},
	beforeDestroy(){
		//解除监听切换业务代理事件
		this.$root.eventHandle.$off('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
	},
	methods: {
		ifPurchaseDealerOfProxyId() {
			let id = this.getEmployeeInfo('personId'), self = this
			this.ajax.get('/user-web/api/userPerson/getUserPerson/'+id,function(data){
				data = data.body;
				if(data.result){
					if (data.content && data.content.type == 'PURCHASE_DEALER') {
						self.filterList = ['dealer_customer_name']
					} else {
						self.filterList = []
					}
				}
			},function(error){
				self.filterList = []
			})
		},
		export() {
			let url = this.uploadUrl;
			// let postData = JSON.parse(JSON.stringify(this.from));
			if(this.search.where.length==0){
				this.$message.error('请添加查询条件');
				return false;
			}
            let postData = Object.assign({}, this.search);




			this.ajax.postStream(url, postData, res => {
				if (res.body.result) {
					this.$message.success(res.body.msg);
				} else {
					this.$message.error(res.body.msg);
				}
			});
		},
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
					type: 'EXCEL_TYPE_AFTERSALE_RETURN',
					},
				},
			})
		},
		tableRowClassName(row,index) {
			// console.log(row)
			// return  'green';
			/*return 'red';*/
			if (row.status == '创建' && row.if_audit_urgent =='Y'){
				 return  'green';
			}
		},
        savePurDealer(){
            if(this.selectedData.length == 0){
				this.$message.error('请选择要生成经销采购退货的行数据');
				return;
			}
            let ids = []
            this.selectedData.forEach(item => {
                ids.push(item.gId)
            })
            let params = {
                id: '', ids
            }, self = this
            this.ajax.postStream('/afterSale-web/api/aftersale/bill/return/savePurDealer?permissionCode=PURCHAS_RETURN_SAVE', params, res => {
				if(res.body.result){
                    this.$message.success(res.body.msg)
                    if (res.body.content) {
                        let alertParams = {
                            id: res.body.content
                        }
                        self.$root.eventHandle.$emit('creatTab', {
                            name: '新退货跟踪单详情',
                            params: alertParams,
                            component: () => import('@components/after_invoices/returnexchangedetail_2')
                        })
                    }
				}else {
					this.$message.error(res.body.msg)
                }
                this.getList()
			}, err => {
                this.$message.error(err)
                this.getList()
            })
        },
		urgentRerurnsOrder(){
            if(this.selectedData.length == 0){
				this.$message.error('请选择要审单加急的行数据');
				return;
			}
            let ids = []
            this.selectedData.forEach(item => {
                ids.push(item.id)
            })
            let params = {
                ids
            }, self = this
            this.ajax.postStream('/afterSale-web/api/aftersale/bill/return/urgentRerurnsOrder', params, res => {
				if(res.body.result){
                    this.$message.success(res.body.msg)
                    if (res.body.result) {
                        // let alertParams = {
                        //     id: res.body.content
                        // }
                        // self.$root.eventHandle.$emit('creatTab', {
                        //     name: '新退货跟踪单详情',
                        //     params: alertParams,
                        //     component: () => import('@components/after_invoices/returnexchangedetail_2')
                        // })
                		this.getList()
                    }
				}else {
					this.$message.error(res.body.msg)
                }
			}, err => {
                this.$message.error(err)
                this.getList()
            })
        },
		searchClick(where,reslove) {
			let self = this;
			this.search.where = where
			// this.getList(true,reslove)
			new Promise((res,rej)=>{
					this.getList(true,reslove,res);
				}).then(()=>{
					if(self.search.page_no != 1){
						self.count = 0;
					}
					self.showCount = false;
				})
		},
		pageSizeChange(ps) {
			this.search.page_size = ps
			this.getList()
		},
		pageChange(page) {
			this.search.page_no = page
			this.getList()
		},
		openDetail(row) {
			let params = JSON.parse(JSON.stringify(row))
			params.orderList=JSON.parse(JSON.stringify(this.list))
			delete params.merge_trade_id
			this.$root.eventHandle.$emit('creatTab', {
				name: '新退货跟踪单详情',
				params: params,
				component: () => import('@components/after_invoices/returnexchangedetail_2')
			})
		},
		/**
		*
		***/
		rowDblclick(row){
			if(row.id){
				this.openDetail(row);
			}
		},
		refresh(){
			//this.setLoadingOfBtn(1,!0);
			this.refreshDisabled = true;
			this.refreshLoding = true;
			this.getList();
		},
		countOff(){

			let self = this,
			url = "/afterSale-web/api/aftersale/bill/return/billReturnsListCount?permissionCode=RETURN_TRACKING_ORDER_QUERY";
			if(!self.list.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;

			self.ajax.postStream(url,self.search,function(response){
					if(response.body.result){

						self.count = response.body.content;
						self.showCount = true;
						self.countOffFlag = false;

					}else{
						self.$message.error(response.body.msg);
					}
				});
		},
		/**
		*获取列表数据
		**/
		getList(bool,callback,resolve){


			let self = this;
			let url = '/afterSale-web/api/aftersale/bill/return/billReturnsList?permissionCode=RETURN_TRACKING_ORDER_QUERY'
			this.ajax.postStream(url/*'/afterSale-web/api/aftersale/bill/return/billReturnsList?permissionCode=RETURN_TRACKING_ORDER_QUERY'*/,this.search, res => {
				//this.setLoadingOfBtn(1,!1);
				//需要清空已选择的数据
				this.selectedData = [];
				this.setAllBtn();
				if(res.body.result && res.body.content) {

					// this.showCount = false;
					let dataList = JSON.parse(JSON.stringify(res.body.content.list));
					if(res.body.content.count == (self.search.page_size+1)&&dataList.length>0){
						dataList.pop();
						// dataList.slice(0,self.search.page_size-1);
						// dataList.push({
						// 	act_price: '合计' + dataList.reduce((a, b) => a + (b.act_price || 0), 0).toFixed(2)
						// })
					}
					if((res.body.content.list || []).length){
						dataList.push({
							act_price: '合计' + dataList.reduce((a, b) => a + (b.act_price || 0), 0).toFixed(2)
						})
					}
					this.list = dataList || [];
					if(!self.showCount){
						self.count = res.body.content.count == (self.search.page_size+1)? (self.search.page_no*self.search.page_size)+1:(self.search.page_no*self.search.page_size);
					}
				}
				if(!res.body.result && bool){
					this.$message({
						type:'error',
						message:res.body.msg
					});
				}
				callback && callback();
				resolve && resolve();

			}, null, this.params.tabName)
		},
		/**
		*所有按钮恢复原来的设置
		*/
		setAllBtn(){
			this.refreshDisabled = false;
			this.refreshLoding = false;
			this.deleLoding = false;
			this.deleDisabled = false;
		},
		/**
		*删除
		**/
		del(){
			var data = this.selectedData;
			if(!data || !data.length){
				this.$message.error('请选择要删除的行数据');
				return;
			}
			this.deleLoding = true;
			this.deleDisabled = true;
			//this.setLoadingOfBtn(0,!0);
			var deleteList = [];
			var canNotDelete = '';
			data.map((a,b)=>{
				//TODO,条件判断的删除
				let id = a.id;
				deleteList.push(id);
			});
			if(canNotDelete){
				this.$message.error(canNotDelete.question_goods_code + '不能删除');
				return;
			}
			deleteList = Array.from(new Set(deleteList));
			let url = '/afterSale-web/api/aftersale/bill/returns/delete?permissionCode=RETURN_TRACKING_ORDER_DELETE'
			this.ajax.postStream(url/*'/afterSale-web/api/aftersale/bill/returns/delete?permissionCode=RETURN_TRACKING_ORDER_DELETE'*/,deleteList,res=>{
				let d = res.body;
				this.deleLoding = false;
				this.deleDisabled = false;
				this.$message({
					type:d.result?'success':'error',
					message:d.msg
				});
				if(!d.result) return;
				this.getList();

			});

		},
		/**
		*查看附件
		**/
		searchFile(){
			var data = this.selectedData;
			if(!data  || data.length != 1){
				let error = data.length?'最多只能选择一行数据':'请选择数据';
				this.$message.error(error);
				return;
			}
			var params = {
				parent_no : data[0].after_order_no,//售后单号
				//child_no:data[0].bill_returns_no,
				child_no:null,

				parent_name : 'AFTER_ORDER',
				child_name:'RETURNANDEXCHANGE',
				parent_name_txt: '售后单',
				//child_name_txt:'退换货申请单',
				ext_data : null,
				notNeedDelBtn:true,
				nickname: data[0].buyer_name,
				mergeTradeId: data[0].merge_trade_id,
				permissionCode:'RETURN_TRACKING_LIST_ATTACHMENT_VIEW'//权限控制的
			};
			params.callback = d=>{
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
				style:'width:1000px;height:600px',
				title:'下载列表'
			});

		},
		/**
		*选择
		**/
		select(selects){
			this.selectedData = selects.length?selects:[];
		},
     //   模板下载
     downloadTemplate(callback) {
        let data = {
            "templateName": "售后单退换货方案导入模板"
        };
        !callback && (this.downloadLoading = true);
        this.ajax.postStream(
            "/reports-web/api/template/getTemplateByName",
            data,
            (res) => {
                if (res.body.result) {
                    if (!callback) {
                        this.download(res.body.content);
                    } else {
                        callback && callback(res.body.content);
                    }
                    this.downloadLoading = false;
                } else {
                    this.$message.error(res.body.msg);
                }
            },
            () => {
                this.downloadLoading = false;
            }
        );
    },
    download(content) {
        let url = content.url;

        let filename = content.template_name;
        if (!fetch) {
            window.location.href = url;
            return;
        }
        return fetch(url).then((res) => {
            res.blob().then((blob) => {
                let a = document.createElement("a");
                let url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = filename;
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            });
        });
    },
    //上传成功返回结果
    uploadSuccess(result) {
        if (result.length > 0 && !!result[0].path) {
            this.importSub(result[0].path);
        }
    },
    //导入
    importSub(fileUrl) {
        let templateUrl = ""
        this.downloadTemplate(content => {
            templateUrl = content.url || ''
            let params = {
                fileUrl: fileUrl,
            };
            this.ajax.postStream(
                "/reports-web/api/reports/aftersale/order/returnsExcelImport",
                params,
                (res) => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg);
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        })
    },
    //导入结果
    showUploadResult() {
        this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/after_sales_report/export'),
            style: 'width:900px;height:600px',
            title: '导入结果',
            params: {
                query: {
                    type: 'EXCEL_TYPE_AFTERSALE_RETURNS_IMPORT',
                },
            },
        })
    },
	},
	created () {
		if (this.params.menuInfo.code === 'RETURN_GOODS_LIST_JXS') {
			this.search.page_name = 'aftersale_bill_returns_dealer'
		} else {
			this.search.page_name = 'aftersale_bill_returns'
		}
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
		this.ifPurchaseDealerOfProxyId()
	},
	mounted() {
		// this.$refs.xptList.$refs.xptSearchEx.filterFields = fields => fields.filter(o => !/(业务状态|关闭状态)/.test(o.comment))
		// this.getList();
		this.$root.eventHandle.$on('updateAfterSaleList',()=>{
            this.getList(!0);
        });
	},
}
</script>
<style type="text/css">
	.pmm_select{width: 80px;}
	.pmm_select .el-select .el-input{width: 100%;}
	.green{
		background:#13CE66 !important;
	}
	.green td {
		background:#13CE66 !important;
	}

</style>
