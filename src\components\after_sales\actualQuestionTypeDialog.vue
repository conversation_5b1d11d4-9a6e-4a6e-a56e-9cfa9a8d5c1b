<template>
<div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
        <el-col :span='20'>
            <el-button type='primary' size='mini' @click="confirm" :disabled="saveStatus">确定</el-button>
        </el-col>
    </el-row>
    <el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px"> 
        <el-col :span="12" style="width: 100%">
            <el-form-item label="实际问题类型">
                <el-select size='mini' 	v-model="form.question">
                    <el-option
                        v-for="item in questionOption"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        >
                    </el-option>
                </el-select>
            </el-form-item>
        </el-col>
    </el-form>
</div>
</template>

<script>
export default {
    props:["params"],
    data(){
        let self = this;
        return {
            form: {
                question: ''
            },
            rules: {
                question:[{
                    required:true,
                    message:'请选择实际问题类型',
                    isShow:false,
                }],
            },
            saveStatus: false,
            questionOption: [
                {
                    label: '商品问题',
                    value: 'CommodityProblems',
                },{
                    label: '物流问题',
                    value: 'LogisticsProblems',
                },{
                    label: '安装问题',
                    value: 'InstallationProblems',
                },{
                    label: '门店服务问题',
                    value: 'StoreServiceProblems',
                }
            ]
        }
    },
    methods:{
        confirm(){
            this.$refs.form.validate((valid) => {
                if(!valid) return
                this.saveStatus = true
                let postData = {
                    id: this.params.afterOrderId,
                    currentQuestiontype: this.form.question
                }
                this.ajax.postStream('/afterSale-web/api/deal/coupleBack/action/updateQuestionType', postData, (res)=> {
                    if (res.body.result) {
                        this.$message.success(res.body.msg)
                        this.params.callback()
                        this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
                    } else {
                        this.$message.error(res.body.msg)
                    }
                    this.saveStatus = false
                })
            })
        }
    },
    mounted(){
        this.form.after_order_id = this.params.afterOrderId
    }
}
</script>

<style scoped>

</style>
