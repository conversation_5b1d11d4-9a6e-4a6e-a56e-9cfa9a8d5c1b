<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="6">
        <el-button type='success' size='mini' @click="submit" >确认</el-button >
      </el-col>
    </el-row>
    <el-row class="xpt-flex__bottom mgb20" >
      <el-tabs v-model="tabKey" @tab-click="tabChange" >
        <el-tab-pane label="按级联选择" name="first">
          <xpt-list
            class="mt-4 table-height"
            ref='list'
            :showHead='false'
            :data='firstList'
            :colData='cols'
            selection='hidden'
            :pageTotal="firstList.length"
          >
            <template slot="material_name" slot-scope="scope">
              <el-cascader
                :options="params.options"
                expand-trigger="hover"
                class="custom-cascader"
                v-model="selectedOptions"
                @change="e=>handleChange(e,scope)">
              </el-cascader>
            </template>
            <template slot="option" slot-scope="scope">
              <el-button type="text" @click="checkView(scope.row)">查看更多</el-button>
            </template>
          </xpt-list>
        </el-tab-pane>
        <el-tab-pane label="按搜索选择" name="second">
          <div class="mt-4">
            <el-input @keyup.native.enter="onSearch" v-model="search.keyword" size="mini" placeholder="请输入搜索内容"> </el-input>
            <el-button type="primary" @click="onSearch" size="mini" style="margin-left: 8px;">搜索</el-button>
          </div>
          <xpt-list
            class="mt-4 table-height"
            ref='list'
            :showHead='false'
            :data='secondList'
            :colData='secondCol'
            selection='radio'
            @radio-change='radioChange'
            @page-size-change='pageSizeChange'
            @current-page-change='currentPageChange'
            :pageTotal="secondTotal"
          >
            <template v-for="name in ['firstCategory', 'secondCategory', 'thirdCategory','secondChoiceScheme','preferredSolution','happenReason']" slot-scope="scope" :slot="name">
              <div v-html="formatRed(scope.row[name])"></div>
            </template>
            <template slot="option" slot-scope="scope">
              <el-button type="text" @click="checkView(scope.row)">查看更多</el-button>
            </template>
          </xpt-list>
        </el-tab-pane>
      </el-tabs>

    </el-row>

  </div>

</template>
<script>

export default {
  name: "add-question-class-modal",
  props: ['params'],
  data(){
    return {
      tabKey:'first',
      firstList: [],
      selectedOptions:[],
      cols:[
        {
          label: '问题分类',
          prop: 'material_name',
          slot: 'material_name',
          width: 450
        },
        {
          label: '发生原因',
          prop: 'happenReason',
        },
        {
          label: '优选方案',
          prop: 'preferredSolution',
          width: 100
        },
        {
          label: '次选方案',
          prop: 'secondChoiceScheme',
          width: 80
        },
        {
          label: '操作',
          prop: 'option',
          slot: 'option',
          width: 80
        },
      ],
      //
      secondCol:[
        {
          label: '一级分类',
          prop: 'firstCategory',
          slot: 'firstCategory',
        },
        {
          label: '二级分类',
          prop: 'secondCategory',
          slot: 'secondCategory',
        },{
          label: '三级分类',
          prop: 'thirdCategory',
          slot: 'thirdCategory',
        },
        {
          label: '发生原因',
          prop: 'happenReason',
          slot: 'happenReason',
        },
        {
          label: '优选方案',
          prop: 'preferredSolution',
          slot: 'preferredSolution',
        },
        {
          label: '次选方案',
          prop: 'secondChoiceScheme',
          slot: 'secondChoiceScheme',
        },
        {
          label: '操作',
          prop: 'option',
          slot: 'option'
        },
      ],
      secondList:[],
      radioData: {},
      secondTotal: 0,
      search:{
        page_size: this.pageSize,
        page_no: 1,
        keyword: '',
      }
    }
  },
  methods:{
    tabChange(){
      if(this.tabKey==='first'){
        this.secondList = []
        this.radioData = {}
      }
      if(this.tabKey==='second'){
        this.selectedOptions = []
        this.firstList = [{}]
        this.search={
          page_size: this.pageSize,
          page_no: 1,
          keyword: '',
        }
      }
    },
    formatRed(val) {
      const keyword = this.search.keyword.trim(); // 获取搜索关键词并去除前后空格
      // 如果关键词或值为空，直接返回原值
      if (!keyword || !val) {
        return val;
      }

      const regex = new RegExp(`(${keyword})`, 'gi'); // 创建不区分大小写的正则表达式
      return val.replace(regex, '<span class="red-color">$1</span>'); // 替换匹配项
    },

    // 级联选择时候改变
    handleChange(val,data) {
      if(val && val.length){
        const dataSource = this.getDataListByTree(val,0,this.params.options)
        this.firstList = [
          {
            ...data.row,
            ...(dataSource||{})
          }
        ]
      }
    },
    getDataListByTree(valList,index,dataSource){
      for(let item of dataSource){
        if(item.value === valList[index]){
          if(index === valList.length-1){
            return item
          }
          if(item.children && item.children.length){
            return this.getDataListByTree(valList,index+1,item.children)
          }

        }
      }
    },
    init(){
        if(this.params.value){
          this.selectedOptions = this.params.value
          const dataSource = this.getDataListByTree(this.params.value,0,this.params.options)

          this.firstList = [{...(dataSource || {})}]
        }else{
          this.firstList = [
            {
              happenReason: undefined,
              preferredSolution: undefined,
              secondChoiceScheme: undefined,
            }
          ]
        }
    },
    // 查看
    checkView(val){
      if( !val.id){
        this.$message.error('请先选择问题分类')
        return
      }
      this.$root.eventHandle.$emit('alert', {
        title: '查看问题分类',
        style: 'width:1000px;height:400px',
        component: () => import('@components/after_sales/check-question-modal.vue'),
        params: {
          id: val.id
        }
      })
    },
    // 搜索
    onSearch(){
      this.ajax.postStream('/afterSale-web/api/aftersale/knowledge/page',{
        ...this.search,
        keyword: this.search.keyword?.trim?.()?this.search.keyword.trim():undefined,
      },(res)=>{
        if(res.body.content){
          this.secondList = res.body.content.list
          this.secondTotal = res.body.content.count
        }else{
          this.$message.error(res.body.msg)
        }
      })
    },
    // 翻页
    pageSizeChange(pageSize){
      this.search.page_size = pageSize
      this.onSearch();
    },
    //
    currentPageChange(page) {
      this.search.page_no = page
      this.onSearch()
    },
    //
    radioChange(val){
      this.radioData = val
    },

    // 确认
    submit(){
      const data = this.tabKey === 'second'? this.radioData : this.firstList[0]
      if(!data|| Object.keys(data).length===0){
        this.$message.error('请选择问题分类')
        return
      }
      if(this.params.callback){
        this.params.callback(data)
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      }
    },
  },
  mounted() {
    this.init()
  }
}
</script>
<style scoped lang="stylus">
.mt-4{
  margin-top: 4px;
}
/deep/ .el-table  .el-table__body-wrapper td .cell{
  height: auto;
}
/deep/ .red-color{
  color: red;
}
.custom-cascader{
 /deep/ .el-input{
   min-width: 400px !important;
 }
}
</style>
