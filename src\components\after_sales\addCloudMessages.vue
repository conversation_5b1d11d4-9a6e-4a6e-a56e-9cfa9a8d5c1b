<!-- 补充咨询内容弹框 -->
<template>
	<div>
		<el-row>
			<el-form :model="form" class="mgt30" :rules="rules" ref="form">
				<el-form-item label="" label-width="10px" prop="reason" :class="$style['textarea-style']">
					<el-input v-model="form.reason" size="mini" type="textarea" :autosize="{ minRows: 3, maxRows: 4 }" @blur="e => form.reason = e.target.value = (e.target.value || '').trim()"></el-input>
					<el-tooltip v-if='rules.reason[0].isShow' effect="dark" :content="rules.reason[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-form>
		</el-row>
		<el-row class="mgt30">
			<el-col :span="24" class="txt-r" style="text-align: right;">
				<el-button class="mgr60" type="primary" size="mini" @click="ensureToMerge">确定</el-button> 
			</el-col>
		</el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js'
	export default {
		props:["params"],
		data(){
			var self = this;
			return {
				form:{
					reason:null
				},
				rules:{
					reason:validate.isNotBlank({required:true,self:self})
				}
			}
		},
		methods:{
			ensureToMerge(){
				var self = this
				self.$refs.form.validate((valid) => {
					if(!valid) return
					self.params.callback&&self.params.callback(self.form.reason)
					self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
				})
			}
		},
		
		mounted:function(){
			let self = this;
		}
	}
</script>
<style module>
.textarea-style :global(.el-form-item__content) {
    height: auto;
    margin: 30px 0 20px;
    overflow: hidden;
}
</style>