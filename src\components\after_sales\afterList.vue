<!-- 售后单列表和售后单列表(退货再售) -->
<template>
    <xpt-list-dynamic
        :data='list'
        :colData='cols'
        :btns='btns'
        :selection='selection'
        :pageTotal='pageTotal'
        :searchPage='serchData.page_name'
		    :showCount ='showCount'
        :taggelClassName="tableRowClassName"
        @count-off="countOff"
        @row-dblclick="rowDblclick"
        @search-click='search'
        @selection-change='selects'
        @page-size-change='pageChange'
        @current-page-change='currentPageChange'
    >
      <el-select
        slot="btns"
        placeholder="标签"
        v-model="defaultUrgentVal"
        size='mini'
        style="width:100px; margin-left: 8px;"
        :class="!!urgentSignErrorTip ?'urgent-sign-error' : ''"
      >
        <el-option
          v-for="item in urgentSignOptions"
          :key="item.code"
          :label="item.name"
          @click.native="setUrgentSigns"
          :value="item.code">
        </el-option>
      </el-select>
      <el-tooltip v-if='urgentSignErrorTip' slot="btns" class="error-tip" effect="dark" :content="urgentSignErrorTip" placement="right-start" popper-class='xpt-form__error'>
				<i class='el-icon-warning'></i>
			</el-tooltip>
    </xpt-list-dynamic>
</template>
<script>
  import Fn from '@common/Fn.js'
  export default{
    props: ['params'],
    data(){
      let self=this;
      return{
        countOffFlag:false,
			  showCount:false,
        serchData:{
          page_name:"aftersale_order",
          where:[],
          page_size: self.pageSize,     //页数
          page_no:1   //页码
        },
        isReSaleOrderList: false,
        showHead:false,
        pageNow:1,
        pageTotal:0,
        btns:[{
          isBtnGroup:false,
          type: 'primary',
          txt: '新增',
          click: self.add,
        },{
          isBtnGroup:false,
          type: 'danger',
          txt: '删除',
          // disabled: !!self.personBusinessAttribute.attributeValue,
          click: self.del,
        },{
          isBtnGroup:false,
          type: 'success',
          txt: '修改推荐处理人',
          click: self.updateAfterHandler,
        },{
          isBtnGroup:false,
          type: 'primary',
          txt: '强制完结申请',
          click: self.applyClose,
        },{
          isBtnGroup:false,
          type: 'success',
          txt: '刷新',
          click: self.refresh,
        }],
        list:[],
        cols:[
          {
              label: '销售标签',
              prop: 'sales_label',
			  width: 70,
              html(val) {
                  return val ? '<span style="color:red;">'+val+'</span>' : val;
              },
          },
          {
            label: '单据编号',
            width:'120',
            prop: 'after_order_no',
            redirectClick(row) {
              self.viewDetail(row.id)
            }
          },{
            label: '合并单号',
            width:'180',
            prop: 'merge_trade_no'
          },{
            label: '买家昵称',
            width:'140',
            prop: 'buyer_nick'
          },{
            label: '业务员',
            width:'90',
            prop: 'staff_name'
          },
          {
            label: '业务员分组',
            width:'120',
            prop: 'staff_group_name'
          },{
            label: '提交售后人',
            // width:'140',
            prop: 'submit_staff_name'
          },{
            label: '创建时间',
            width:'150',
            prop: 'create_time',
            format:"dataFormat1"
          },{
            label: '提交售后日期',
            width:'150',
            prop: 'submit_time',
            format:"dataFormat1"
          },{
            label: '是否经销商订单',
            width:'100',
            prop: 'if_dealer',
            formatter: prop => ({
              Y: '是',
              N: '否',
            }[prop] || prop),
          },{
            label: '经销商名称',
            width:'100',
            prop: 'dealer_customer_name'
          },{
            label: '推荐处理人',
            width:'90',
            prop: 'recommend_handler_name'
          },{
            label: '是否三个月前售后',
            width:'110',
            prop: 'if_three_age',
            formatter: prop => prop ? '是' : '否',
          },{
            label: '业务锁定人',
            // width:'140',
            prop: 'locker_name'
          },{
            label: '业务锁定日期',
            width:'90',
            prop: 'lock_time',
            format: 'dataFormat',
          },{
            label: '业务状态',
            prop: 'status',
            formatter:function(row){
              return self.statusList[row];
            }
          }, {
            label: '业务员大分组',
            width:'130',
            prop: 'staff_big_group_name'
          },{
            label: '预约时间',
            prop: 'pre_order_time',
            width:'130',
            format: 'dataFormat',
          },{
            label: '处理人姓名',
            prop: 'handler_name'
          },{
            label: '处理内容',
            prop: 'handle_content'
          },{
            label: '处理优先等级',
            prop: 'handle_level',
            width:'90',
          },{
            label: '处理时间',
            prop: 'handle_time',
            width:'100',
            format: 'dataFormat',
          },{
            label: '收货人手机',
            prop: 'receiver_mobile',
            format: 'hidePhoneNumber'
          },{
            label: '收货人姓名',
            prop: 'receiver_name'
          },{
            label: '订单店铺',
            prop: 'shop_name'
          },{
            label: '业务备注',
            prop: 'remark'
          },{
            label: '业务锁定人分组',
            width:'130',
            prop: 'locker_group_name'
          },{
            label: '推荐处理人分组',
            width:'130',
            prop: 'recommend_handler_group_name'
          },{
            label: '关闭状态',
            prop: 'close_status',
            formatter:function(row){
              return row=='FINISH'?"已关闭":"未关闭";
            }
          }, {
            label: '是否国补订单',
            width: 110,
            prop: 'if_gov_subsidy',
            format: 'yesOrNo'
          }, {
            label: '标签',
            prop: 'urgent_sign',
            format: 'auxFormat',
            formatParams: 'SHDBQ'
          }, {
            label: '标签添加者',
            width: 110,
            prop: 'urgent_signer_name'
          },
          {
            label: '是否协商一致',
            width: 110,
            prop: 'if_consensus',
            formatter: prop => ({
              Y: '是',
              N: '否',
            }[prop] || prop),
          }, {
            label: '单据来源',
            width: 80,
            prop: 'source',
            format: 'auxFormat',
            formatParams: 'AFTERSALE_SOURCE_TYPE'
          }
        ],
        selectsData:[],
        selection: 'checkbox',
        statusList:{CREATE:"待办",PRE_HANDLE:"售前处理",WAIT_HANDLE:"售后待办",AFTER_HANDLE:"售后处理",FINISH:"完结",CANCELLATION:"作废"},
        staffTypeList :{SALES_PERSON:"客服",CUSTOMER_SERVICE_EXECUTIVE:"售后专员",ISUUE_ANALYST:"售后分析员",SHIP_ASSITANT:"售中专员",RECEIVABLES_AUDITOR:"财审专员",REFUND_AUDITOR:"退款审核员"},
        /**
         * 标签默认值
         */
        defaultUrgentVal:'',
        urgentSignOptions:[],
        urgentSignErrorTip: ""
      }
    },
    methods:{
      tableRowClassName(row) {
        if (row.handle_level === '重点') {
          return 'import-row';
        }
        return '';
      },
      applyClose(){
        let self =this;
			let ids = [];
			let reson = '';
			if(this.selectsData.length === 0){
				this.$message.error('请选择数据');
				return;
			}
			this.selectsData.forEach(item=>{
				ids.push(item.id)
			})

			var params = {
        itemtext:'强制完结原因',
				callback(res){
          console.log(res)
						reson = res
            self.ajax.postStream('/afterSale-web/api/forced/closure/action/apply', {
              orderIds:ids,forceReason:reson
            }, res => {
                    if(res.body.result) {
                      self.$message.success(res.body.msg);

                    }else{
                    self.$message.error(res.body.msg);

                    }
                  }, err => {
                    self.$message.error(err);
                  })
				}
			};
			self.$root.eventHandle.$emit('alert', {
				params:params,
				component: () => import('@components/after_sales/alert/forceReason.vue'),
				close:function(res){

				},
				style:'width:500px;height:350px',
				title:'强制完结申请',
			});

		},
      // 只对售后单列表显示，售后单列表(退货再售)不显示
      exportAftersaleOrder (){
        if(this.serchData.where.length < 1){
          this.$message.error('导出功能至少要有一个查询条件')
          return
        }

        this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportAftersaleOrder', Object.assign({}, this.serchData, {
          page_size: this.pageTotal,     //页数
          page_no:1   //页码
        }), d => {
          this.$message({
            type: d.body.result ? 'success' : 'error',
            message: d.body.msg,
          })
        })
      },
      /**
       *双击事件
       **/
      rowDblclick(data){
        this.viewDetail(data.id);
      },

      // 多选数据
      selects(val) {
        this.selectsData = val
      },
      // 刷新
      refresh() {
        var self = this;
        self._getList();
      },
      //新增
      add(){
        var params = {};
        this.$root.eventHandle.$emit('creatTab', {
          name:"新增售后单" + (this.isReSaleOrderList ? '(退货再售)' : ''),
          params:params,
          component: () => import('@components/after_sales/index')
        });
      },
      viewDetail (id){
        var params = {
          id,
          idList: Array.from(new Set/*去重*/(this.list.map(obj => obj.id))),//用于售后单详情的上一页/下一页
          code:this.params.menuInfo.code//查看售后单类型
        };

        if(document.querySelector('[data-symbol="after_order_id_' + id + '"]')){
          this.$message.error('该售后单已经打开')
        }else {
          this.$root.eventHandle.$emit('creatTab', {
            name:"售后单详情" + (this.isReSaleOrderList ? '(退货再售)' : ''),
            params:params,
            component: () => import('@components/after_sales/index')
          });
        }
      },
      //获取参数的type_id
      getIds (){
        var self=this;
        var resList=[];
        if(self.selectsData&&self.selectsData.length>0){
          for(var i=0;i<self.selectsData.length;i++){
            resList.push(self.selectsData[i].id);
          }
        }
        return resList;
      },
      // 删除
      del() {
        var self = this;
        var postData=self.getIds();
        if(!(postData&&postData.length>0)){
          self.$message.error("请至少选择一行数据");
          return ;
        }
        let url = '/afterSale-web/api/aftersale/order/delete?permissionCode=AFTERSALE_DELETE'
        self.ajax.postStream(url/*'/afterSale-web/api/aftersale/order/delete?permissionCode=AFTERSALE_DELETE'*/, postData, function(d){
          if(d.body.result){
            self.$message.success(d.body.msg);
            self._getList();
          }else{
            self.$message.error(d.body.msg);
            //self._getList();
          }
        })
      },


      updateAfterHandler(){
         var self = this;
        var postData=self.getIds();
        if(!(postData&&postData.length>0)){
          self.$message.error("请至少选择一行数据");
          return ;
        }
          let params = {};
          params.isSelect = true;
          params.callback=(d)=>{
              console.log(d);
              this.ajax.postStream('/afterSale-web/api/aftersale/order/updateAfterHandler?permissionCode=UPDATE_AFTERSALE_HANDER', {
                userId:d.userId,
                userName:d.fullName,
                afterOrderIds:postData
              }, d => {
                      if (d.body.result) {
                          this.$message.success(d.body.msg);
                          self.refresh()
                      }else{
                          this.$message.error(d.body.msg);

                      }
                  }, (e) => {
                      this.$message.error(e);
                      resolve && resolve();
                  })
          }
          self.$root.eventHandle.$emit('alert',{
              close: function(){},
        			style:'width:900px;height:600px',
                  title:"业务员列表",
                  params:params,
                  component: () => import('@components/after_sales/distribute/selectRecommendList.vue')
              });

      },
      // 监听每页显示数更改事件
      pageChange(pageSize){
        this.serchData.page_size = pageSize
        this.serchData.page_no = 1
        this._getList();
      },
      // 监听页数更改事件
      currentPageChange(page){
        this.pageNow = page;
        this.serchData.page_no = page;
        this._getList();
      },
      // 搜索
      search(list, resolve) {
        var self = this;
        self.serchData.where=list;
        new Promise((res,rej)=>{
				  this._getList(resolve,null,res);
        }).then(()=>{
          if(self.serchData.page_no != 1){
            self.pageTotal = 0;
          }
            self.showCount = false;
        })
      },
      countOff(){

        let self = this;
        let url = this.isReSaleOrderList ? '/afterSale-web/api/aftersale/order/resale/listCount' : '/afterSale-web/api/aftersale/order/listCount'

        if(!self.list.length){
          self.$message.error("当前列表为空，先搜索内容");
          return;
        }
        if(!!self.countOffFlag){
          self.$message.error("请勿重复点击");
          return;
        }
        self.countOffFlag = true;

        self.ajax.postStream(url,self.serchData,function(response){
            if(response.body.result){

              self.pageTotal = response.body.content;
              self.showCount = true;
              self.countOffFlag = false;

            }else{
              self.$message.error(response.body.msg);
            }
          });
      },
      //查询列表
      _getList(resolve, msg,resolve2){
        this.urgentSignErrorTip = '';
        if (this.params.menuInfo.code === 'AFTERSALE_ORDER_LIST') { // 售后单列表
            this.serchData.page_name = 'aftersale_order'
        } else if (this.params.menuInfo.code === 'AFTERSALE_ORDER_RESALE_LIST') { // 售后单列表退货再售
          this.serchData.page_name = 'aftersale_order_resale'
        } else if (this.params.menuInfo.code === 'AFTERSALE_ORDER_LIST_JXS'){// 售后单列表经销商
          this.serchData.page_name = 'aftersale_order_resale_jxs'
        }
        var self=this;
        let url = this.isReSaleOrderList ? '/afterSale-web/api/aftersale/order/resale/list?permissionCode=AFTERSALE_QUERY' : '/afterSale-web/api/aftersale/order/list?permissionCode=AFTERSALE_QUERY'

        self.ajax.postStream(url, this.serchData, function(d){
          if(d.body.result){
             let dataList = JSON.parse(JSON.stringify(d.body.content.list));
              if(dataList.length == (self.serchData.page_size+1)&&dataList.length>0){
                dataList.pop();
              }
              self.list = dataList;
              let totalCount = self.serchData.page_no*self.serchData.page_size;
              if(!self.showCount){
                self.pageTotal = d.body.content.count == (self.serchData.page_size+1)? totalCount+1:totalCount;
              }

            self.$message.success(msg || d.body.msg)
          }else{
            self.list=[];
            self.pageTotal=0;
            self.$message.error(d.body.msg)
          }
          resolve && resolve();
          resolve2 && resolve2();
          self.selectRole = [];
        }, err => {
          resolve && resolve();
          resolve2 && resolve2();
        }, self.params.tabName)
      },
      batchFinish (){
        if(this.selectsData.length === 0){
          this.$message.error("请至少选择一行数据")
          return ;
        }

        this.ajax.postStream('/afterSale-web/api/aftersale/order/batchFinish', this.selectsData.map(o => o.id), res => {
          if(res.body.result){
            this._getList(null, res.body.msg)
          }else {
            this.$message.error(res.body.msg)
          }
        })
      },
      forceFinish(){
        if(this.selectsData.length < 1){
          this.$message.error("请至少选择一行数据")
          return ;
        }
        if(this.selectsData.length > 50){
          this.$message.error("不能超过【50】条数据")
          return ;
        }
        let ids=this.selectsData.map(item => item.id)
        let currentBtnIndex=this.btns.findIndex(item=>item.txt=="强制完结")
        this.btns[currentBtnIndex].loading=true;
        this.ajax.postStream('/afterSale-web/api/aftersale/order/forcedFinish', ids, res => {
          if(res.body.result){
            this._getList(null, res.body.msg)
          }else {
            this.$message.error(res.body.msg)
          }
          this.btns[currentBtnIndex].loading=false;
        },err=>{
          this.$message.error(err)
          this.btns[currentBtnIndex].loading=false;
        })
      },
      /*
      key 要标记的值
      */
      setUrgentSigns() {
        if (!this.defaultUrgentVal) return;
        if (!this.selectsData.length) {
          this.$message.error('请选择售后订单');
          this.defaultUrgentVal = '';
          return
        }
        let data = {
            id_list: [],
            sign: ''
          },
          // 要清标记的数据
          clearData = {
            id_list: [],
            sign: ''
          },
          // 所选批次订单数据
          i = this.selectsData.length,
          key = this.defaultUrgentVal;
        while (i--) {
          if (this.selectsData[i]) {
            clearData.id_list.push(this.selectsData[i].id);
          }
          data.id_list.push(this.selectsData[i].id);
        }
        // 清除标记
        if (data.id_list.length) {
          data.sign = key
          this.changeSign(data)
        } else {
          this.$message.error('所选售后订单无需要清除标记');
        }
      },
      changeSign(data, resolve) {
        var self = this;
        this.ajax.postStream('/afterSale-web/api/aftersale/order/updateBatchUrgentSign?permissionCode=AFTERSALE_QUERY' , data, (res) => {
          if (res.body.result) {
            if (!resolve) {
              this._getList();
              this.$message({
                message: data.sign ? "标签添加成功！" : "标签清除成功！",
                type:'success'
              })
              this.defaultUrgentVal = '';
            }
          } else {
            this.defaultUrgentVal = '';
            const errorMsg = res.body.msg;
            this.$message.error(errorMsg)
            if (errorMsg) {
              this.urgentSignErrorTip = errorMsg
            }
          }
          resolve && resolve();
        })
      },
      submitAfterSale(type='') {
        if(!this.selectsData.length) {
          this.$message.error("请选择数据");
          return false;
        }

        let invalidData = type ==='B' ? this.selectsData.filter((item)=>item.if_three_age === 0): this.selectsData.filter((item)=>item.if_three_age === 1);
        if(invalidData.length) {
          this.$message.error(`售后单${invalidData[0].after_order_no}，是否三个月前售后不等于${type==='B'?'是': '否'}，不可提交售后${type ==='B' ?'ABC': ''}`);
          return false;
        }

        invalidData = this.selectsData.filter((item)=> item.creator_name !== this.getEmployeeInfo('fullName') && item.locker_name !== this.getEmployeeInfo('fullName'));
        if(invalidData.length) {
          this.$message.error(`售后单${invalidData[0].after_order_no}，业务代理人非售后单创建人或业务锁定人，不可操作提交售后${type ==='B' ?'ABC': ''}`);
          return false;
        }
        let url = type === 'B' ? '/afterSale-web/api/aftersale/order/submitAbc/batch?permissionCode=AFTERSALE_SUBMIT_AFTERSALE_ABC' : '/afterSale-web/api/aftersale/order/submit/batch?permissionCode=AFTERSALE_SUBMIT_AFTERSALE';
        this.ajax.postStream(url,this.selectsData.map((item)=> {
          return {
            id: item.id,
            type: type,
            after_order_no: item.after_order_no
          }
        }), res => {
          if(res.body.result) {
            this.$message.success(res.body.msg);
            this.refresh();
          } else {
            const failedData = res.body.content;
            if(failedData && failedData.length) {
              this.$root.eventHandle.$emit('alert', {
                close: function () { },
                style: 'width:900px;height:600px',
                title: "执行结果",
                params: {
                  data: failedData,
                },
                component: () => import('@components/after_sales_refund/components/execution-result-table.vue')
              });

              failedData.some((failedRes) => {
                const errorMsg = failedRes.msg;

                if (errorMsg.indexOf('直转') > -1 && errorMsg.indexOf('提交') > -1) {
                  this.urgentSignErrorTip = errorMsg;
                  return true;
                }
                return false;
              })
            } else {
              this.$message.error(res.body.msg)
            }
          }
        })

      },
    },
    mounted:function(){
      // 过滤 status == 1 的
      this.urgentSignOptions = __AUX.get("SHDBQ")
        .filter(item => item.status === 1)
        .map(item => {
          return {
            name: item.name,
            code: item.code
          }
        });
      if (this.params.menuInfo.code === 'AFTERSALE_ORDER_LIST') { // 售后单列表
        this.serchData.page_name = 'aftersale_order'
      } else if (this.params.menuInfo.code === 'AFTERSALE_ORDER_RESALE_LIST') { // 售后单列表退货再售
        this.serchData.page_name = 'aftersale_order_resale'
      } else if (this.params.menuInfo.code === 'AFTERSALE_ORDER_LIST_JXS'){// 售后单列表经销商
        this.serchData.page_name = 'aftersale_order_resale_jxs'
      }
      this.isReSaleOrderList = /退货再售/.test($('.el-tabs__item.is-active').text())
      if(!this.isReSaleOrderList/* && !this.personBusinessAttribute.attributeValue*/){
        this.btns.push({
          isBtnGroup:false,
          type: 'primary',
          txt: '导出',
          click: this.exportAftersaleOrder
        })

        this.btns.push({
          type: 'danger',
          txt: '批量完结',
          click: this.batchFinish
        })

        this.btns.push({
          type: 'primary',
          txt: '提交售后',
          click: ()=> this.submitAfterSale('A')
        })

        this.btns.push({
          type: 'success',
          txt: '提交售后ABC',
          click: ()=> this.submitAfterSale('B')
        })
      }
    },
  }
</script>
<style lang="stylus" scoped>
/deep/ .import-row {
  td {
    background: #ee000066 !important;
  }

}

.error-tip {
  color: red;
}
.urgent-sign-error /deep/ input {
  border-color: red;
}
</style>
