<!--附件下载 -->
<template>
    <div id="download" class="download-main" :class="hideDelImgBtn ? $style['download-img-del-hide'] : ''">
        <div v-if="!params.isFromBatchOrder" class="download-detial">
            <div class="download-txt">
                <div >
                    {{params.parent_name_txt}}：
                </div>
                <div class="download-txt-right" v-tooltip='params.child_no'>
                   <a v-if="params.aftersaleOrderId" class="view" @click="viewDetail(params.aftersaleOrderId)"> {{params.parent_no}}</a>
                   <a v-else> {{params.parent_no}}</a>
                </div>
            </div>
            <div v-if="params.child_name_txt && params.child_no" class="download-txt">
                <div >
                    {{params.child_name_txt}}：
                </div>
                <div class="download-txt-right" v-tooltip='params.child_no'>
                    {{params.child_no}}
                </div>
            </div>
        </div>
        <div class="download-content">
            <div class="download-title">
                <!-- <el-button type="danger" size="mini" @click='delFun()' v-if="!params.notNeedDelBtn">删除</el-button> -->
                <!-- <el-button type="primary" size="mini" @click='zipDownload()'>批量下载</el-button> -->
                <el-button v-if="!params.isFromBatchOrder" type="primary" size="mini" @click='getData'>查看业务上传图片</el-button>
                <el-button v-if="params.source == 'refundDetails' ? false : !params.isFromBatchOrder" type="primary" size="mini" @click='getAttachmentFromZD'>查看4PL平台客户上传图片</el-button>
                <el-button v-if="params.source == 'refundDetails' ? false : !params.isFromBatchOrder" type="primary" size="mini" @click="zipDownloads">一键下载</el-button>
            </div>
            <div class="download-middle">
                <el-button :type="item.active ? 'primary': ''" size="mini" @click='clickFun(item)' :key="item.index" v-for="item in conditonList">{{item.alias}}</el-button>
            </div>
            <div class="download-group">
                <el-table ref="multipleTable" :data="list"  tooltip-effect="dark" width='100%' style="width: 100%" @selection-change="parentSelect" @select="parentSelectFun" @expand="expandFun" @select-all="selectAll" :default-expand-all="true" class="pmm_div6" :row-class-name="taggelClassName">
                    <el-table-column type="expand">
                        <template slot-scope="scope">
                            <el-table :ref="scope.row.group" :data="scope.row.content"  tooltip-effect="dark" width='100%' style="width: 100%" :show-header="false" class="download-child" @select="childSelectFun(scope.row)" @selection-change="childSelect"  :row-class-name="taggelClassName">
                                <el-table-column width="25" align='center' type="selection"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="name" label="文件名称" >
                                    <template slot-scope="scope" >
                                        <span v-if="!scope.row.isPucture">{{scope.row.name}}</span>
                                        <div  v-else>
                                             <a  @click="showPicture(scope.row)" alt="点击可预览哦" href="javascript:;" class="pmm_a5"><img :src="scope.row.path" width="60" /></a>
                                        </div>
                                       
                                        
                                    </template>
                                </el-table-column>
                                <el-table-column show-overflow-tooltip prop="creator_nick" label="创建人" width="80"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="create_time" label="创建时间" width="150">
                                    <template slot-scope="scope">{{scope.row.create_time | dataFormat1}}</template>
                                </el-table-column>
                                <el-table-column show-overflow-tooltip prop="size" label="文件大小" width="60">
                                    <template slot-scope="scope">{{scope.row.size | fileSize}}</template>
                                </el-table-column>
                                <el-table-column label="图片预览" width="60">
                                    <template slot-scope="scope">
                                        <el-button type="primary" size="mini" @click="showPicture(scope.row)" v-if="scope.row.isPucture">
                                            预览
                                        </el-button>
                                    </template>
                                </el-table-column>
                                <el-table-column label="文件下载" width="60">
                                    <template slot-scope="scope">
                                        <el-button type="primary" size="mini" >
                                            <a :href="scope.row.path" target="_blank" :download="scope.row.path">下载</a>
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-table-column>
                    <el-table-column width="25" align='center' type="selection"></el-table-column>
                    <el-table-column prop="group" label="文件名称" ></el-table-column>
                    <el-table-column label="创建人" width="80"></el-table-column>
                    <el-table-column label="创建时间" width="150"></el-table-column>
                    <el-table-column label="文件大小" width="60"></el-table-column>
                    <el-table-column label="文件预览" width="60"></el-table-column>
                    <el-table-column label="文件下载" width="60"></el-table-column>
                </el-table>
            </div>
        </div>
        <!-- <div v-if='showTotal' class='download-total'>总共{{params.total}}条记录</div> -->
        <div  class='download-total'>总共{{totalNum}}条记录</div>
        <xpt-image
            :images="imagesList"
            :show="ifShow"
            :ifUpload="false"
            @close="closeFun">
        </xpt-image>
    </div>
</template>
<script>
import Fn from '@common/Fn.js'
import HelpFile  from '@common/helpFile.js'
import { downloadZip } from "@components/dz_customer/common/api"
export default {
    data(){
        return {
            whichBtnClick: '',
            hideDelImgBtn: this.params.cantDel,
            conditonList: [],
            groupList: [],
            list: [],
            parentSelectList: [],
            groupSelectActive: null,
            imagesList: [],
            ifShow: false,
            host: null,
            totalNum:null,
            emptyList: []
        }
    },
    props:['params'],
    methods:{
         viewDetail (id){
        var params = {
          id,
        };

      
          this.$root.eventHandle.$emit('creatTab', {
            name:"售后单详情",
            params:params,
            component: () => import('@components/after_sales/index')
          });
      },
        zipDownloads() {
          let list = [], curtentList = this.childSelectList && this.childSelectList.length > 0 ? this.childSelectList : this.groupList
          if (curtentList.length == 0) {
              this.$message.error('未有文件可下载')
              return
          }
          curtentList.forEach(item => {
            list.push({
              name: item.name,
              path: item.path
            })
          })
          downloadZip(list)
        },
        clickFun(obj) {
            this.getTree(obj || {});
            this.conditonList.forEach(v => {
                if(v.name === obj.name){
                    v.active = true;
                    this.groupSelectActive = Object.assign(v);
                }else {
                    v.active = false;
                }
            })
        },
        delFun() {
            if(this.parentSelectList.length === 0){
                this.$message.error('请选择要删除的附件');
                return;
            }
            //要删除的流水号集合
            var list_cloud_file_id = [];
            this.parentSelectList.forEach(v => {
                if(v.content && v.content.length > 0) {
                    v.content.forEach(v1 => {
                        list_cloud_file_id.push(v1.cloud_file_id);
                    })
                }
            })

            this.ajax.postStream('/file-iweb/api/cloud/file/delete',{list_cloud_file_id: list_cloud_file_id},res => {
                if(res.body.result) {
                    this.$message.success('删除成功');
                    this.getData();
                }else{
                    this.$message.error(res.body.msg);
                }
            })

        },
        zipDownload() {
            if(!this.host) {
                this.$message.error('获取域名出错，请重新打开');
            }
            if(this.parentSelectList.length === 0){
                this.$message.error('请选择要下载的附件');
                return;
            }
            //路径，名称集合
            var list_path = [];
            this.parentSelectList.forEach(v => {
                if(v.content && v.content.length > 0) {
                    v.content.forEach(v1 => {
                        list_path.push(v1.path+'='+v1.name);
                    })
                }
            })
            //定义下载的名称；
            var name = "批量下载";
            var form = document.createElement("form");   //定义一个form表单
            form.setAttribute('style','display:none');   //在form表单中添加查询参数
            form.setAttribute('method','post');//设置或返回将数据发送到服务器的HTTP方法
            form.setAttribute('action', this.host+'/file-web/api/file/download/'+name+'.zip');
            form.setAttribute('target','');
            form.setAttribute('enctype','application/x-www-form-urlencoded');

            var input1 =document.createElement('input');
            input1.setAttribute('type','hidden');
            input1.setAttribute('name','path');
            input1.setAttribute('value',list_path.toString());

            document.getElementById('download').append(form);  //将表单放置在web中
            form.append(input1);   //将查询参数控件提交到表单上
            form.submit();   //表单提交
            form.remove();

        },
        showPicture(row) {
            this.ifShow = true;
            var list = [];
            this.groupList.forEach(v => {
                if(v.isPucture) {
                    var obj = Object.assign({},v);
                    obj.src =  obj.path;
                    obj.size =  obj.size;
                    obj.name =  obj.name;
                    obj.date =  Fn.dateFormat(obj.create_time,'yyyy-MM-dd hh:mm:ss');
                    obj.creatName =  obj.creator_nick;
                    obj.isPucture =  obj.isPucture;
                    //确定要预览那张图片
                    if(obj.cloud_file_id === row.cloud_file_id) {
                        obj.active = true;
                    }else {
                        obj.active = false;
                    }

                    list.push(obj);
                }
            })
            this.imagesList = list;
        },
        closeFun() {
            this.ifShow = false;
            //预览支持删除，关闭再获取一次数据
            if(this.whichBtnClick === '查看4PL平台客户上传图片'){
                this.getAttachmentFromZD()
            }else {
                this.getData();
            }
        },
        //判断是否为图片
        isPucture(str) {
            str = str.toString();
            var strFilter=".jpeg|.gif|.jpg|.png|.bmp|.pic|"
            if(str.indexOf(".")>-1)
            {
                var p = str.lastIndexOf(".");
                var strPostfix=str.substring(p,str.length) + '|';
                strPostfix = strPostfix.toLowerCase();
                if(strFilter.indexOf(strPostfix)>-1)
                {
                    return true;
                }
            }
            return false;
        },
        //展开时触发的函数
        expandFun(row,expand) {
            if(expand){
                //有延迟
                setTimeout(()=>{
                    this.selectChild()
                },20);
            }

        },
        //全选清空
        selectAll() {
            this.parentSelectList = [];
        },
        parentSelectFun() {
            this.parentSelectList = [];
        },
        parentSelect(selectArr){
            setTimeout(()=> {
                selectArr.forEach(v => {
                    //深度复制
                    var obj = JSON.parse(JSON.stringify(v));
                    //用来判断是否同一分组，同分组进行覆盖，不同分组添加
                    var bool = true;
                    this.parentSelectList.forEach(v1 => {
                        if(v1.group === v.group) {
                            Object.assign(v1,obj);
                            bool = false;
                        }
                    })
                    if(bool){
                        this.parentSelectList.push(obj);
                    }
                })
                this.selectChild();
            },50)
        },
        selectChild() {
            var selectChildList = [];
            this.list.forEach(v => {
                //先清空所有
                this.toggleSelection(v,false);
            })
            if(this.parentSelectList && this.parentSelectList.length > 0){
                this.parentSelectList.forEach(v => {
                    //获取所有子类list
                    selectChildList = [...selectChildList,...v.content];
                    this.toggleSelection(v,true);

                })
            }
        },
        //判断子类是否勾选，bool 为false，清空
        toggleSelection(v,bool) {
            this.list.forEach(v1 => {
                if(v1.group === v.group){
                    if (bool) {
                      v1.content.forEach(row => {
                          v.content.forEach(row1 => {
                              if(row.cloud_file_id === row1.cloud_file_id) {
                                this.$refs[v1.group] && this.$refs[v1.group].toggleRowSelection(row,true);
                              }
                          })
                      });
                    } else {
                      this.$refs[v1.group] && this.$refs[v1.group].clearSelection();
                    }
                }
            })
        },
        //判断父类是否勾选
        parentToggleSelection(v) {
            this.list.forEach(item => {
                if(item.group === v.group) {
                    if(item.content.length === v.content.length){
                        this.$refs.multipleTable && this.$refs.multipleTable.toggleRowSelection(item,true);

                    }else{
                        this.$refs.multipleTable && this.$refs.multipleTable.toggleRowSelection(item,false);
                    }

                }
            })
        },
        //点击子类获取子类的所有已勾选列
        childSelect(selectArr) {
            this.childSelectList = selectArr;
        },
        //点击子类获取该子类父类的信息，
        childSelectFun(obj) {
            var newObj = {
                group: obj.group,
                content: this.childSelectList
            }
            //用来判断是否同一分组，同分组进行覆盖，不同分组添加
            var bool = true;
            this.parentSelectList.forEach(v => {
                if(v.group === newObj.group){
                    v.content = newObj.content;
                    bool = false;
                }
            })
            if(bool) {
                this.parentSelectList.push(newObj);
            }
            this.parentToggleSelection(newObj);

        },
        //组合成分组Tree
        getTree(obj) {
            if(JSON.stringify(obj) == "{}"){return;}
            //用来装载组合好的列表
            var list = [];
            //找不到该分类则分配到其他
            var other ={
                group: '其他',
                content: []
            }
            this.groupList.forEach((v,index) => {
                //判断是否有该分组，没有则放入其他
                if(v[obj.name]){
                    //遍历已有的分组，相同则加入，不同则再添加一个分组
                    var bool = true;
                    list.forEach(v1 => {
                        if(v1.group === v[obj.name]){
                            v1.content.push(v);
                            bool = false;
                        }
                    })
                    if(bool){
                        var item ={
                            group : v[obj.name],
                            content : []
                        }
                        item.content.push(Object.assign(v));
                        list.push(item);
                    }


                }else {
                    other.content.push(v);
                }
            })
            if(other.content.length > 0){
                list.push(other);
            }

            this.list = list;
        },
        // 如果存在other_parent_nos参数，则也连同other_parent_nos一起查询附件
        getParentNo2FileList (url, data, res, cb){
            var _count = 0
            ,   _list = []
            ,   _other_parent_nos = Array.from(new Set(this.params.other_parent_nos)).filter(Boolean)//去重
            ,   loopFunc = loopCb => {
                var order_no = _other_parent_nos.shift()

                if(order_no){
                    this.ajax.postStream(url, Object.assign({}, data, { order_no }), res2 => {
                        if(res2.body.result){
                            if(!res2.body.content) res2.body.content = {}

                            _count += (res2.body.content.count || 0)
                            _list = (res2.body.content.list || []).concat(_list)
                        }
                        loopFunc(loopCb)
                    }, () => {
                        loopFunc(loopCb)
                    })
                }else {
                    loopCb()
                }
            }

            if(_other_parent_nos.length){
                loopFunc(() => {
                    if(!res.body.content) res.body.content = {}
                    if(_count.length) res.body.result = true
                    res.body.content.count = _count + (res.body.content.count || 0)
                    res.body.content.list = _list.concat(res.body.content.list || [])
                    cb(res)
                })
            }else {
                cb(res)
            }
        },
        taggelClassName(row, index) {
            if (row.ifShowRed) {
                return 'fileDanger';
            }
            if ((row.path === undefined || row.path === null || row.path === '') && row.name) {
                return 'fileDanger';
            } else {
                return '';
            }
        },
        // 获取执鼎上传的图片
        getAttachmentFromZD (){
            var data = {
                nickname: this.params.nickname,//单据编号 ： String
                batch_trade_no: this.params.batch_trade_no || '',//子单据编号： String
                merge_trade_id: this.params.mergeTradeId || undefined,//子单据编号：    String
            }

            let url = '/afterSale-web/api/aftersale/order/getAttachmentFromZD';

            this.hideDelImgBtn = true
            this.whichBtnClick = "查看4PL平台客户上传图片"
            this.ajax.postStream(url,data,res => {
                // res.body = {"result":true,"code":null,"msg":"获取执鼎附件成功","content":[{"FID":"f1859387-182d-476b-b800-35167b62adee","FEntryID":"1ee923b7-c537-4256-86e7-1378b925f14f","FSBILLID":575415,"FATTACHMENTNAME":"沈阳武楠-20190114181415001","FATTACHMENTURL":"http://file.linshimuye.com/FileUpload/20190114/jpg/8370e6a7-30ca-43f4-bad6-1128e5a0582b.jpg","FFILESIZE":197328,"FCREATEDATE":"/Date(1547460855750+0800)/","FUSERID":"6ebe2e5d-c81c-487b-b72c-4756d76cc70e","FUSERNAME":null,"FTYPE":"ZLGZ","FNICKNAME":"沈阳武楠","FSBillNo":"ZLGD190106000855","FTRADEBATCHNO":"HB20181114144932860846-006"}]}


                ;(res.body.content || []).forEach(obj => {
                    obj.path = obj.FATTACHMENTURL
                    obj.size = obj.FFILESIZE
                    obj.creator_nick = obj.FNICKNAME
                    obj.name = obj.FATTACHMENTNAME
                    obj.create_time = Number(obj.FCREATEDATE.replace(/(\/Date\(|\+0800\)\/)/g, ''))

                    if(obj.name.match(/\.(jpeg|jpg|gif|png)$/) === null){
                        obj.name += (obj.FATTACHMENTURL.match(/\.(jpeg|jpg|gif|png)$/) || [''])[0]
                    }
                })

                if(res.body.result){
                    var list = res.body.content || [];
                    this.totalNum = list.length;
                    list.forEach(v => {
                        if ( v.path === undefined || v.path === null || v.path === '') {
                                if (v.name === undefined || v.name === null || v.name === '') {
                                    list.splice(idx, 1)
                                    this.totalNum = this.totalNum - 1
                                } else {
                                    v.ifShowRed = true
                                    v.path = ''
                                    v.name = '(此文件上传失败)' + v.name 
                                }
                        } else {
                            //判断是否为图片，用来控制是否要预览
                            v.isPucture = this.isPucture(v.path);
                            var group_value_list = [];
                            if(v.group_value_json){
                                //拿分组的数据
                                group_value_list = JSON.parse(v.group_value_json)['content'] || [];
                            }
                            //判断是否为数组，不是数组过滤掉
                            if(!(Object.prototype.toString.call(group_value_list)=='[object Array]')){
                                group_value_list = [];
                            }

                            var obj = {
                                active: true,
                                alias: "全部",
                                name: "all",
                                value: "全部"
                            }
                            //添加全部这个分组
                            group_value_list.unshift(obj);
                            group_value_list.forEach(v3 => {
                                v[v3.name] = v3.value;
                            })
                        }
                    })

                    var obj ={};
                    if(this.conditonList.length > 0){
                        if(this.groupSelectActive){
                            this.conditonList.forEach(v => {
                                if(v.name === this.groupSelectActive.name) {
                                    //控制当前分组高亮
                                    v.active = true;
                                    obj = Object.assign({},v);
                                }

                            })
                        }else{
                            obj = this.conditonList[0];
                        }
                    }

                    this.groupList = list;
                    this.getTree(obj || {});
                    this.parentSelectList = [];
                    this.params.callback&&this.params.callback(list)
                }else {
                    this.$message.error(res.body.msg)
                }
            })
        },
        //获取原数据
        getData(){
            var data = {
                order_no: this.params.parent_no,//单据编号 ：    String
                sub_order_no: this.params.child_no,//子单据编号： String
                ext_data: this.params.ext_data,//扩展字段：      String
                cloud_file_id: null//流水主键：      Long
            }
            /*if(this.params.total) {
                data.page_size = this.params.total;
                data.page_no = 1;
            }*/
            //TODO,暂时把所有数据加载过来
             data.page_size = 10000;
             data.page_no = 1;
            let url = '/file-iweb/api/cloud/file/list';
            this.params.permissionCode?url+=('?permissionCode='+this.params.permissionCode):'';
            this.whichBtnClick = "查看业务上传图片"

            this.hideDelImgBtn = this.params.cantDel
            this.ajax.postStream(url/*'/file-iweb/api/cloud/file/list'*/,data,res => {
                this.getParentNo2FileList(url, data, res, res => {
                    if(res.body.result){
                        var list = (res.body.content && res.body.content.list) || [];
                        this.totalNum = res.body.content.count || 0;
                        list.forEach((v,idx) => {
                            if ( v.path === undefined || v.path === null || v.path === '') {
                                if (v.name === undefined || v.name === null || v.name === '') {
                                    list.splice(idx, 1)
                                    this.totalNum = this.totalNum - 1
                                } else {
                                    v.ifShowRed = true
                                    v.path = ''
                                    v.name = '(此文件上传失败)' + v.name 
                                }
                            } else {
                                //判断是否为图片，用来控制是否要预览
                                v.isPucture = this.isPucture(v.path);
                                var group_value_list = [];
                                if(v.group_value_json){
                                    //拿分组的数据
                                    group_value_list = JSON.parse(v.group_value_json)['content'] || [];
                                }
                                //判断是否为数组，不是数组过滤掉
                                if(!(Object.prototype.toString.call(group_value_list)=='[object Array]')){
                                    group_value_list = [];
                                }

                                var obj = {
                                    active: true,
                                    alias: "全部",
                                    name: "all",
                                    value: "全部"
                                }
                                //添加全部这个分组
                                group_value_list.unshift(obj);
                                group_value_list.forEach(v3 => {
                                    v[v3.name] = v3.value;
                                })
                            }
                        })

                        var obj ={};
                        if(this.conditonList.length > 0){
                            if(this.groupSelectActive){
                                this.conditonList.forEach(v => {
                                    if(v.name === this.groupSelectActive.name) {
                                        //控制当前分组高亮
                                        v.active = true;
                                        obj = Object.assign({},v);
                                    }

                                })
                            }else{
                                obj = this.conditonList[0];
                            }
                        }

                        this.groupList = list;
                        this.getTree(obj || {});
                        this.parentSelectList = [];
                        this.params.callback&&this.params.callback(list)
                    }else {
                        this.$message.error(res.body.msg)
                    }
                })
            })
        }
    },

    mounted() {
        //添加第一个分组按钮
        var list = [{
            active: true,
            alias: "全部",
            name: "all",
            value: "全部"
        }];
        //添加对应的全部分组
        this.conditonList = [];
        if(this.params.parent_name && this.params.child_name) {
            if(HelpFile[this.params.parent_name] && HelpFile[this.params.parent_name][this.params.child_name]){
                list = [...list,...HelpFile[this.params.parent_name][this.params.child_name]];
            }
        }
        list.forEach(v => {
            v.active = v.active || false;
            var obj = Object.assign({},v);
            this.conditonList.push(obj);
        })

        if(this.params.isFromBatchOrder){
            this.getData = this.getAttachmentFromZD            
        }

        if(this.params.isFromAfterSaleIndex){
            this.getAttachmentFromZD()
        }else {
            this.getData();
        }
        //获取域名
        this.ajax.postStream('/file-iweb/api/cloud/fileConfig/get',{},res => {
            if(res.body.result){
                this.host = res.body.content.host;
            }else {
                this.$message.error(res.body.msg);
            }
        })

    },
    computed: {
        showTotal() {
            return 'number' == typeof(this.params.total);
        }
    }
}
</script>
<style lang="css">
    .view{
        color:#0000EE !important;
    }
    .download-main{ height: 100% }
    .download-detial{width:15%;float:left;border-right: 1px solid #000;height:100%;}
    .download-detial + .download-content{width:85%;float:right;height:100%; position: relative;}
    .download-title{padding: 5px 10px;border-bottom: 1px solid #000;}
    .download-middle{padding: 5px 10px;border-bottom: 1px solid #000;}
    .download-group .el-table__expanded-cell{padding: 0px 0px 0px 60px}
    .download-group .el-table__body-wrapper{height: 407px;}
    .download-child .el-table__body-wrapper{height: 100%;}
    .download-txt {padding: 10px 10px 10px 0;}
    .download-txt-right{text-align: right;margin-top: 10px;}
    .pmm_a5{display: inline-block;vertical-align: middle;border: 1px solid #dbdbdb;margin: 5px 0;}
    .download-child .el-table__body-wrapper .cell{height: auto;}
    .download-main:after{
        clear: both;
        content: '';
        display: block;
    }
    .download-total{
        position: fixed;
        right: 10px;
        bottom: 10px
    }
    .download-group {
        position: absolute;
        top: 57px;
        bottom: 0;
        left: 0;
        right: 0
    }
    .el-table .fileDanger {
        background: pink;
    }
</style>
<style module>
.download-img-del-hide :global(.xpt-image__thumbnail .el-icon-close) {
    display: none;
}
</style>