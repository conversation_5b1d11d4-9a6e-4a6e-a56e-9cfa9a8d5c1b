<!-- 售后专员设置列表 -->
<template>
	<div class='xpt-flex'>
		<el-row :gutter='10' class='xpt-top'>
		<el-form :model='search' label-position="right" label-width="120px">

			<el-col :span='3'>
				<el-button type='primary' size='mini' @click='() => checkUser("")' >售后专员设置</el-button>
				<el-button type='primary' size='mini' @click='() => checkUser("ABC")' >售后ABC专员设置</el-button>
			</el-col>
			<el-col :span='4'>
				<el-form-item label="售后专员工号：">
					<el-input
						v-model="search.afterEmployeeNumber"
						size='mini'
					></el-input>
				</el-form-item>
			</el-col>
			<el-col :span='4'>
				<el-form-item label="售后ABC专员工号：">
					<el-input
						v-model="search.abcEmployeeNumber"
						size='mini'
					></el-input>
				</el-form-item>
			</el-col>
			<el-col :span='4'>
				<el-form-item label="分组名称：">
					<el-input
						v-model="search.saleGroupName"
						size='mini'
					></el-input>
				</el-form-item>
			</el-col>
			<el-col :span='4'>
				<el-form-item label="售后专员分组：">
					<el-input
						v-model="search.afterDefaultGroupName"
						size='mini'
					></el-input>
				</el-form-item>
			</el-col>
			<el-col :span='4'>
				<el-form-item label="售后ABC专员分组：">
					<el-input
						v-model="search.abcDefaultGroupName"
						size='mini'
					></el-input>
				</el-form-item>
			</el-col>
			<el-col :span="1">
				<el-button type='success' size='mini' @click='preSearch' :disabled='refreshBtnStatus' :loading='refreshBtnStatus'>查询</el-button>
			</el-col>
		</el-form>
	</el-row>
		<xpt-list
			:data='list'
			:btns='btns'
			:colData='cols'
			:searchPage='search.page_name'
			:pageTotal='count'
			:selection='selection'
			:showCount ='showCount'
			:showHead='false'
			:pageSizes='[10, 20, 50, 100, 200,1000]'
			@search-click='searchData'
			@selection-change='selectionChange'
			@page-size-change='pageSizeChange'
			@current-page-change='pageChange'
			@count-off="countOff"
		></xpt-list>
	</div>
</template>
<script>
export default {
	data(){
		let self = this;
		return {
			btns: [],
			showCount:false,
			countOffFlag:false,
			cols: [ {
					label: '工号',
					prop: 'sale_employee_number'
				}, {
					label: '名字',
					prop: 'sale_name'
				}, {
					label: '分组',
					prop: 'sale_group_name'
				},  {
					label: '在职状态',
					prop: 'sale_incumbency'
				}, {
					label: '售后专员名字',
					prop: 'afterName'
				}, {
					label: '售后专员工号',
					prop: 'after_employee_number'
				},{
					label: '售后专员分组',
					prop: 'afterDefaultGroupName'
				},{
					label: '售后ABC专员名字',
					prop: 'abcName'
				},{
					label: '售后ABC专员分组',
					prop: 'abcDefaultGroupName'
				},{
					label: '售后ABC专员工号',
					prop: 'abc_employee_number'
				}
			],
			search:{
				afterEmployeeNumber: '',
				saleGroupName: '',
				page_no:1,
				page_size:50,
				abcEmployeeNumber: ''
			},
			list:[],
			selectData:[],
			count:0,
			selection: 'checkbox',
			showHead: false,
			refreshBtnStatus:false,
		}
	},
	props:['params'],
	methods:{
		preSearch(obj, resolve){
			let self = this;
			self.showCount = false;
			new Promise((res,rej)=>{
				this.getList(resolve,res);
			}).then(()=>{
				if(self.search.page_no != 1){
					self.count = 0;
				}
				self.showCount = false;

			})
		},
		checkUser(type){
			this.ajax.postStream('/user-web/api/customer_service_executive/checkAuth', {}, res=>{
            if(res.body.result){
				if (type == 'ABC') {
					this.checkABCUser();
				} else {
					this.updateAfterSaleStaff();
				}
            } else {
              this.$message.error(res.body.msg || '')
            }
          }, err => {

          })
		},
		updateAfterSaleStaff(){
			let self = this;
			if(!self.selectData.length){
				this.$message.error('请选择要操作的数据');
				return;
			}
			var params = {
				ids:self.selectData.map(item=>{return {personId:item.id, id: item.afterId}}),
				callback(res){
					self.getList();
				}
			};
			self.$root.eventHandle.$emit('alert', {
				params:params,
				component: () => import('@components/after_sales/alert/afterSaleStaffReset.vue'),
				close:function(res){

				},
				style:'width:500px;height:350px',
				title:'售后专员设置',
			});
		},
		checkABCUser(){
			let self = this;
			if(!self.selectData.length){
				this.$message.error('请选择要操作的数据');
				return;
			}
			var params = {
				ids:self.selectData.map(item=>{return {personId:item.id, id: item.abcId}}),
				callback(res){
					self.getList();
				}
			};
			self.$root.eventHandle.$emit('alert', {
				params:params,
				component: () => import('@components/after_sales/alert/after_abc.vue'),
				close:function(res){

				},
				style:'width:500px;height:350px',
				title:'售后ABC专员设置',
			});
		},
		selectionChange(data) {
			this.selectData = data;
		},
		searchData(obj, resolve){
			this.search.where = obj;
			this.selectData = [];
			this.getList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.page_size = pageSize;
			this.selectData = [];
			this.getList();
		},
		pageChange(page){
			this.search.page_no = page;
			this.selectData = [];
			this.getList();
		},
		countOff(){

			let self = this,
			url = "/user-web/api/customer_service_executive/count";
			if(!self.list.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;

			self.ajax.postStream(url,self.search,function(response){
				if(response.body.result){


					self.count = response.body.content.count;
					self.showCount = true;
					self.countOffFlag = false;

				}else{
					self.$message.error(response.body.msg);
				}
			});
		},
		getList(resolve){
			let self = this;
			this.refreshBtnStatus = true;

			var postData = JSON.parse(JSON.stringify(this.search))



			this.ajax.postStream('/user-web/api/customer_service_executive/list', postData, d=>{
				if(d.body.result&&d.body.content){
					this.list = d.body.content.list||[];
					console.log(self.showCount)
					if(!self.showCount){
						self.count = d.body.content.list.length == (self.search.page_size+1)? (self.search.page_no*self.search.page_size)+1:(self.search.page_no*self.search.page_size);
					}

					resolve&&resolve();
				} else {
					this.$message.error(d.body.msg || '')
				}
				this.refreshBtnStatus = false;
				// resolve && resolve();
			}, err => {
				// resolve && resolve();
				this.$message.error(err);
				this.refreshBtnStatus = false;
			})
		},


	},
	mounted() {
	}
}
</script>
