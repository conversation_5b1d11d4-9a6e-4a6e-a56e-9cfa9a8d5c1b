// 修改设置
<template>
<div class='xpt-flex'>
	<xpt-headbar>
		<el-button type='primary' slot='left' size='mini' @click="save">保存</el-button>
		
	</xpt-headbar>
  <el-form label-width='100px' :model='query'  ref="query" :rules='rules'>
    <el-row :gutter='40' style='height:50px'>
      <el-col :span='40'  class="xpt_col">
        <el-form-item label='售后专员工号'  prop="after_employee_number">
          <el-input v-model="query.after_employee_number" size="mini" ></el-input>
          <el-tooltip v-if='rules.after_employee_number[0].isShow' effect="dark" :content="rules.after_employee_number[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</div>
</template>
<script>
import validate from '@common/validate.js';
  export default {
    data() {
      let self = this;
      return {
        oldGoodsList: [],
        query:{
          after_employee_number:'',
        },
        rules:{
          after_employee_number:validate.isNotBlank({
            trigger:'change',
            self:self
          }),
        }
      }
    },
	  props:['params'],
    methods: {
      close(){
        this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
      },
      save(){

        this.$refs.query.validate((valid) => {
          if(!valid) return
          let postData ={
            after_employee_number:this.query.after_employee_number,
            list:this.params.ids
          }
          // this.params.callback(this.query.after_employee_number);
          this.ajax.postStream('/user-web/api/customer_service_executive/update', postData, res=>{
            if(res.body.result){
                this.$message.success(res.body.msg)
                this.params.callback()
                this.close();
            } else {
              this.$message.error(res.body.msg || '')
            }
          }, err => {
            
          })
        })
      },
     
    },
    mounted(){
    }
  }
</script>
