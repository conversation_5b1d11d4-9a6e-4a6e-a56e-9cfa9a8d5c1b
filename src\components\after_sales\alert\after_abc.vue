// 修改设置
<template>
<div class='xpt-flex'>
	<xpt-headbar>
		<el-button type='primary' slot='left' size='mini' @click="save">确认</el-button>
	</xpt-headbar>
    <el-form label-width='120px' :model='query'  ref="query" :rules='rules'>
        <el-row :gutter='40' style='height:50px'>
        <el-col :span='40'  class="xpt_col">
            <el-form-item label='售后ABC专员工号'  prop="abc_employee_number">
                <el-input v-model="query.abc_employee_number" size="mini" ></el-input>
                <el-tooltip v-if='rules.abc_employee_number[0].isShow' effect="dark" :content="rules.abc_employee_number[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip>
            </el-form-item>
        </el-col>
        </el-row>
    </el-form>
</div>
</template>
<script>
import validate from '@common/validate.js';
  export default {
    data() {
      let self = this;
      return {
        oldGoodsList: [],
        query:{
          abc_employee_number:'',
        },
        rules:{
          abc_employee_number:validate.isNotBlank({
            trigger:'change',
            self:self
          }),
        }
      }
    },
	  props:['params'],
    methods: {
      close(){
        this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
      },
      save(){

        this.$refs.query.validate((valid) => {
          if(!valid) return
          let postData ={
            abc_employee_number:this.query.abc_employee_number,
            list:this.params.ids
          }
          this.ajax.postStream('/user-web/api/customer_service_executive/updateABC', postData, res=>{
            if(res.body.result){
                this.$message.success(res.body.msg)
                this.params.callback()
                this.close();
            } else {
              this.$message.error(res.body.msg || '')
            }
          }, err => {
            this.$message.error(err)
          })
        })
      },
     
    },
    mounted(){
    }
  }
</script>
