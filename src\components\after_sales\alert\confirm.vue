<!-- 服务单--选单 确认功能 -->
<template>
  <div>
    <div class="content">
      <i class="el-icon-warning"></i> <span>{{ html1 }}</span
      ><span
        @click="goToDetail"
        style="color: #20a0ff; font-weight: bold; cursor: pointer"
        >{{ consultionNo }}</span
      >
      <span>{{ html2 }}</span>
    </div>
    <div style="padding: 10px 0">
      <el-button style="margin-right: 10px; margin-left: 45%" @click="cancel">{{
        btn2
      }}</el-button
      ><el-button type="primary" @click="confirm">{{ btn1 }}</el-button>
    </div>
  </div>
</template> 
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      html1: "",
      html2: "",
      btn1: "",
      btn2: "",
      consultionNo: "",
    };
  },
  methods: {
    cancel() {
      // 关闭提示框并创建咨询单
      this.params.callback({ type: "0" });
      this.close();
    },
    confirm() {
      let { warnType } = this.params.result;
      if (warnType == "NO_SATISFACTION") {
        // 跳转到咨询单详情
        this.params.callback({ type: "2" });
        this.close();
        return;
      }
      // 关闭外层弹框关闭咨询单
      this.params.callback({ type: "1" });
      this.close();
    },
    close() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    goToDetail() {
      let { consultionId } = this.params.result;
      this.params.callback({ type: "2" });
      this.close();
      this.$root.eventHandle.$emit("creatTab", {
        name: "咨询单详情",
        component: () => import("@components/after_sales/consultation"),
        params: { id: consultionId, orderList: this.params.orderList },
      });
    },
    checkHis(content) {
      console.log(content);
      if (content.warnType == "DEALING") {
        this.html1 = `当前批次单1小时内您已咨询过，咨询单号是`;
        this.html2 = "请您耐心等待服务商回复！";
      }
      if (content.warnType == "UN_REPLY") {
        this.html1 = `当前批次单您已咨询过，咨询单号是`;
        this.html2 = "如紧急可选择专员介入";
      }
      if (content.warnType == "NO_SATISFACTION") {
        this.html1 = `当前批次单3天内您已咨询过，咨询单号是`;
        this.html2 = "且服务商也已回复，请您对上次的回复进行评价";
      }
      content.warnType == "NO_SATISFACTION"
        ? (this.btn1 = "马上去评价")
        : (this.btn1 = "好的");

      content.warnType == "NO_SATISFACTION"
        ? (this.btn2 = "稍后评价")
        : (this.btn2 = "需继续提交咨询");
    },
  },
  created() {
    let { consultionNo } = this.params.result;
    this.checkHis(this.params.result);
    this.consultionNo = consultionNo;
  },
};
</script>
<style scoped>
.content {
  height: 116px;
  border-bottom: 1px solid #ddd;
  padding-top: 20px;
}
.content span {
  display: inline-block;
  line-height: 20px;
  font-size: 16px;
}
</style>
