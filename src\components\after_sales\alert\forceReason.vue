<template>
<div class='xpt-flex'>
	<xpt-headbar>
		<el-button type='primary' slot='left' size='mini' @click="save">保存</el-button>
		
	</xpt-headbar>
  <el-form label-width='100px' :model='query'  ref="query" :rules='rules'>
    <el-row :gutter='40' style='height:50px'>
      <el-col :span='40'  class="xpt_col">
        <el-form-item :label='params.itemtext'  prop="forceReason">
          <el-input v-model="query.forceReason" type="textarea" size="mini"  class="repo-input" :maxlength="300"></el-input>
          <el-tooltip v-if='rules.forceReason[0].isShow' effect="dark" :content="rules.forceReason[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</div>
</template>
<script>
import validate from '@common/validate.js';
  export default {
    data() {
      let self = this;
      return {
        oldGoodsList: [],
        query:{
          forceReason:'',
        },
        rules:{
          forceReason:validate.isNotBlank({
            trigger:'change',
            self:self
          }),
        }
      }
    },
	  props:['params'],
    methods: {
      close(){
        this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
      },
      save(){
        this.$refs.query.validate((valid) => {
          if(!valid) return
          this.params.callback(this.query.forceReason);
          this.close();
        })
      },
     
    },
    mounted(){
    }
  }
</script>
<style>
	.repo-input{
    float: right;
    /* margin-right: 50px; */
	}
  .xpt_col{
    margin-bottom: 10px;
  }
</style>
