<!-- 服务单--选单功能 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='cols' 
		:searchPage='search.page_name' 
		:pageTotal='count' 
		:selection="params.isFromRefundRequest ? 'checkbox' : 'radio'" 
		:isNeedClickEvent="true"
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@search-click='searchClick' 
		@radio-change='radioChange'
		@selection-change='radioChange'
		@row-dblclick='rowDblclick'
		ref="xptList"
	></xpt-list>	
</template>
<script>
export default {
    props:['params'],
	data() {
		let self = this;
		return {
			list: [],
			btns: [{
				type: 'primary',
				txt: '确认',
				click: self.close,
				loading:false
			}],
			cols: [
				{
					label: '批次单号',
					prop: 'batch_trade_no',
					width: 180
				}, {
					label: '合并单号',
					prop: 'merge_trade_no',
					width: 180
				}, {
					label: '买家昵称',
					prop: 'master_cust_name',
					// formatter(obj) {
					// 	return obj ? obj.cust_name : '';
					// }
				}, {
					label: '收货地址',
					prop: 'receiver_address_name',
					// formatter(obj) {
					// 	return obj ? obj.address_name : '';
					// }
				}, {
					label: '收货人',
					prop: 'receiver_name',
					// formatter(obj) {
					// 	return obj ? obj.receiver_name : '';
					// }
				}, {
					label: '手机',
					prop: 'receiver_mobile',
					// formatter(obj) {
					// 	return obj ? obj.receiver_mobile : '';
					// }
				}, {
					label: '承诺发货日期',
					prop: 'commit_time_str',
					width: 100
				}, {
					label: '仓库',
					prop: 'stock_name'
				}, {
					label: '货审锁定人',
					prop: 'goods_audit_locker_name'
				}, {
					label: '货审状态',
					prop: 'goods_audit_status',
					format: 'auditStatusChange'
				}, {
					label: '财审锁定人',
					prop: 'financial_audit_locker_name'
				}, {
					label: '财审状态',
					prop: 'financial_audit_status',
					format: 'financialStatusChange'
				}, {
					label: '打印锁定状态',
					prop: 'zd_print_lock_status',
					width: 100,
					formatter(val) {
						if(val === 'Y') {
							return '已锁定';
						} else {
							return '未锁定';
						}
					}
				}, {
					label: '发货状态',
					prop: 'zd_delivery_status',
					formatter(val) {
						if(val === 'Y') {
							return '已发货'
						}
						if(val === 'N') {
							return '未发货'
						}
					}
				}, {
					label: '提货点',
					prop: 'logistics_point_name'
				}, {
					label: '三包点',
					prop: 'three_guarantees_point_name'
				}
			],
			search: {
				page_no:1,
				page_size: self.pageSize,
				page_name: 'scm_batch_trade',
				where: []
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			if(this.params.isFromRefundRequest){
				obj.forEach((o, i) => {
					if(o.comment === '是否发货' || o.comment === '合并单号'){
						obj.splice(i, 1)
					}
				})
				obj = obj.concat(this.setDefaultDelivery.where)
			}

			this.search.where = obj;
			this.getList(resolve);
		},
		radioChange(obj) {
			this.selectData = obj;
		},
		rowDblclick(obj) {
			// this.selectData = obj;
			this.close();
		},
		getList(resolve) {
			var postData = JSON.parse(JSON.stringify(this.search))

			if(this.params.setSearchObj){
				this.params.setSearchObj(postData)
			}

			this.ajax.postStream("/order-web/api/batchtrade/list", postData, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve()
			});
		},
		checkHis(batch_trade_id){
			let self=this;
			let data={
				batch_trade_id
			}
			return new Promise((resolve,reject)=>{
				self.ajax.postStream('/afterSale-web/api/aftersale/consultion/checkHis',data,res=>{
					resolve(res.body)
				},err=>{
					self.$message.error(err)
					reject(false)
				})
			})
		},
		goToDetail(id){
			this.$root.eventHandle.$emit('creatTab',{
				name:'咨询单详情',
				component: ()=>import('@components/after_sales/consultation'),
				params: {id,orderList: this.params.orderList,
				},
			})
		},
		async close() {
			if(!this.selectData || this.selectData.length === 0) {
				this.$message.error('请选择单据');
				return;
			}
			if(this.params.fromPage=="consultation"&&this.selectData.deliver_method=="SUPPLIER"){
				let result=false;
				this.btns[0].loading=true;
				try{
					result=await this.canISaveBySupplier(this.selectData.batch_trade_id)
				}catch(e){
					result=false;
				}
				this.btns[0].loading=false;
				if(!result){
					return
				}
			}
			// 查询是否已经有咨询单
			let checkResult=await this.checkHis(this.selectData.batch_trade_id)
			if(!checkResult.result){
				let self=this
				this.$root.eventHandle.$emit('alert',{
				title: '提示',
				style:'width:400px;height:200px',
				component: () => import('@components/after_sales/alert/confirm'),
				params: {
					orderList:this.params.orderList,
					result:checkResult.content,
					callback:(result)=>{
						if(result.type=="2"){
							self.goToDetail(checkResult.content.consultionId)
						}
						if(result.type=="1"){
							// _type==1需关闭新增咨询单页面
							self.params.callback({
								...self.selectData,
								_type:"1"
							});
						}
						this.params.callback(this.selectData);
						self.$root.eventHandle.$emit('removeAlert', self.params.alertId);
					}
					}
				})
			}else{
				this.params.callback(this.selectData);
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
			}
		},
		// 设置过滤条件总是包含已发货状态
		setDefaultDelivery (cb){
			this.ajax.postStream("/user-web/api/sql/listFields", { page: this.search.page_name }, res => {
				if(res.body.result){
					this.setDefaultDelivery.where = []
					res.body.content.fields.forEach(obj => {
						if(obj.comment === '是否发货' || obj.comment === '合并单号'){
							this.setDefaultDelivery.where.push({
								condition: 'AND',
								field: obj.field,
								listWhere: [],
								operator: '=',
								table: obj.table,
								value: obj.comment === '是否发货' ? 'Y' : this.params.mergeTradeNo,
							})
						}
					})
					this.search.where = this.search.where.concat(this.setDefaultDelivery.where)
					cb()
				}
			})
		},
		//供应商直发订单，只有工厂结算方式为与合作商结算时，才可创建咨询单
		canISaveBySupplier(batch_trade_id){
			let self=this;
			return new Promise((resolve,reject)=>{
				self.ajax.postStream('/afterSale-web/api/aftersale/consultion/checkDeliverMethod',batch_trade_id,res=>{
					if(res.body.result){
						resolve(res.body.result)
					}else{
						self.$message.error(res.body.msg)
						resolve(false)
					}
				},err=>{
					self.$message.error(err)
					reject(false)
				})
			})
		}
	},
	mounted() {
		if(this.params.merge_trade_id){
			this.search.merge_trade_id = this.params.merge_trade_id
		}

		if(this.params.isFromRefundRequest){
			setTimeout(() => {
				this.$refs.xptList.$refs.xptSearchEx.params.fields.some((obj, index) => {
					if(obj.comment === '合并单号'){
						this.$refs.xptList.$refs.xptSearchEx.params.fields.splice(index, 1)
						return true
					}
				})
			}, 1000)
			this.setDefaultDelivery(() => {
				this.getList();
			})
		}else {
			// this.getList();
		}
	}
}
</script>
