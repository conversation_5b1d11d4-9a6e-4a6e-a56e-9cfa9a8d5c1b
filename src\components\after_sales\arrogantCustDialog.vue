<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='primary' size='mini' @click="confirm" :disabled="saveStatus">确定</el-button>
        <el-button type='primary' size='mini' @click="cancel">取消</el-button>
      </el-col>
    </el-row>
    <el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px"> 
      <el-col :span="12" style="width: 100%">
        <el-form-item label="请选择：" required>
          <el-switch
            on-text="是"
            off-text="否"
            on-value="Y"
            off-value="N"
            v-model="form.if_arrogant_cust"
          ></el-switch>
        </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>

<script>
export default {
  props:["params"],
  data(){
    let self = this;
    return {
      form: {
        if_arrogant_cust: '',
        after_order_id: ''
      },
      rules: {},
      saveStatus: false
    }
  },
  methods:{
    confirm(){
        this.$refs.form.validate((valid) => {
          if(!valid) return
          this.saveStatus = true
          let postData = {
            id: this.params.afterOrderId,
            if_arrogant_cust: this.form.if_arrogant_cust,
            buyer: this.params.buyer
          }
          this.ajax.postStream('/afterSale-web/api/aftersale/order/stampArrogantCust?permissionCode=AFERSALE_ARROGANT_CUST', postData, (res)=> {
            if (res.body.result) {
              this.$message.success(res.body.msg)
              this.params.callback()
              this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
            } else {
              this.$message.error(res.body.msg)
            }
            this.saveStatus = false
          })
        })
    },
    cancel(){
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    }
  },
  mounted(){
    this.form.after_order_id = this.params.afterOrderId
    this.form.if_arrogant_cust = this.params.ifArrogantCust
  }
}
</script>

<style scoped>

</style>
