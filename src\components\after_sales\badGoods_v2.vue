<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-col :span="24">
			<el-button
				type="primary"
				size="mini"
				@click='addGoodsFun'
				:disabled="params.isClosed || !params.isHasRights2Edit"
			>添加商品问题</el-button>
			<el-button
				type="primary"
				size="mini"
				@click="addNoGoodsFun"
				:disabled="
					params.isClosed
					|| !params.isHasRights2Edit
					|| params.isReSaleOrder
					|| params.form.if_dealer === 'Y'
				"
			>添加非商品问题</el-button><!-- 经销商角色不能添加非商品问题 -->
			<el-button type="danger" size="mini" @click='delFun' :disabled="!tableSelect2Delete.length || params.isClosed || !params.isHasRights2Edit">删除行</el-button>
<!--			<el-button type="primary" size="mini" @click='() => copyQuestionsContent2()' :disabled="params.isClosed || !params.isHasRights2Edit">批量填充</el-button>-->
			<el-button type="primary" size="mini" @click="pushConsultation">下推咨询单</el-button>
            <el-button
                type="warning"
                size="mini"
                @click="qualityFeedback"
                :disabled="!params.isSubmitQualityFeedback||tableSelect2Delete.length!=1">品质反馈</el-button>
			<el-button type="primary" size="mini" @click="logisticsReport">物流报备</el-button>
			<el-button type="primary" size="mini" @click="addProductConsulting">产品咨询</el-button>
      <el-button type="primary" size="mini" @click="onLogistics">物流投诉索赔</el-button>
		</el-col>
	</el-row>
	<el-table
		border
		:data='listAfterMaterialVO'
		tooltip-effect="dark"
		:row-class-name="$style['row-height']"
		@selection-change="handleSelectionChange"
		ref="$table"
	>
		<el-table-column type="selection" width="40"></el-table-column>
		<el-table-column type="index" width="40" label="序号"></el-table-column>
		<el-table-column label="商品信息" show-overflow-tooltip>
			<template slot-scope="scope">
				<p v-if="scope.row.material_specification">规格型号：{{ scope.row.material_specification }}</p>
				<p><span v-if="scope.row.material_name">商品名称：{{ scope.row.material_name }}&nbsp;&nbsp;</span><span v-if="scope.row.material_number">商品编码：{{ scope.row.material_number }}</span></p>
        <p><span v-if="scope.row.source_material_name">来源商品名称：{{ scope.row.source_material_name }}&nbsp;&nbsp;</span>
        <span v-if="scope.row.source_material_number">来源销售商品编码：{{ scope.row.source_material_number }}</span></p>

				<span
					v-if="_checkiFCanEdit(scope.row, 'number')"
				>数量：x {{ scope.row.number }} {{ scope.row.material_unit }}&nbsp;&nbsp;</span>
				<div
					v-else-if="scope.row.if_goods_question === 'Y'"
				>数量：<el-input type="number" v-model="scope.row.number" min="1" size='mini' @blur="() => !(scope.row.number > 0) && (scope.row.number = '')"></el-input>{{ scope.row.material_unit }}</div>

				<span
					v-if="_checkiFCanEdit(scope.row, 'stand_price')"
				>标准售价：{{ scope.row.stand_price ? scope.row.stand_price : '无' }}</span>
				<div
					v-else-if="scope.row.if_goods_question === 'Y'"
				>标准售价：<el-input type="number" v-model="scope.row.stand_price" min="1" size='mini'></el-input>
				</div>
				<span v-if="scope.row.if_goods_question === 'Y'">实际售价：{{ scope.row.act_price ?  scope.row.act_price : '无' }}<br></span>

				<p>订单店铺：
				<span
					v-if="_checkiFCanEdit(scope.row, 'shop_name')"
				>{{ scope.row.shop_name }}</span>
				<el-input
					v-else
					size='mini'
					v-model="scope.row.shop_name"
					icon="search"
					:on-icon-click="e => selectShop(e, scope)"
					@change.native="e => searchAfterChange(e, scope)"
				></el-input>
				</p>
				<p v-if="scope.row.batch_trade_no">批次单号：
						<a style="text-decoration:none;" href="javascript:;" @click="viewdOrder(scope.row.batch_trade_id,scope.row.merge_trade_id)" title="点击进入合并订单信息页面">{{ scope.row.batch_trade_no }}</a>

				</p>
			</template>
		</el-table-column>
<!--		<el-table-column label="业务员" width="180" show-overflow-tooltip>-->
<!--			<template slot-scope="scope">-->
<!--				<p>业务员：-->
<!--				<span v-if="_checkiFCanEdit(scope.row, 'real_name')">{{ scope.row.real_name }}</span>-->
<!--				<el-input-->
<!--					v-else-->
<!--					size='mini'-->
<!--					v-model="scope.row.real_name"-->
<!--					icon="search"-->
<!--					:on-icon-click="() => selectRealName(scope.row)"-->
<!--					@change.native="e => searchAfterChange2(e, scope.row)"-->
<!--				></el-input>-->
<!--				</p>-->
<!--				<p v-if="scope.row.group_name">业务员分组：{{ scope.row.group_name }}</p>-->
<!--			</template>-->
<!--		</el-table-column>-->

<!--		<el-table-column label="问题描述信息" width="540" show-overflow-tooltip :class-name="$style['input100']">-->
<!--			<template slot-scope="scope">-->
<!--				<template v-if="scope.row._oldQuestions_des">-->
<!--                   <p style="margin-bottom: 5px;   overflow: auto;" v-for="(q,i) in scope.row._oldQuestions_des" :key="scope.row.id+'-'+i">-->
<!--					<span>问题{{ i + 1 }}</span>-->
<!--					&lt;!&ndash; <el-input-->
<!--						v-model="scope.row._oldQuestions[i].des"-->
<!--						size='mini'-->
<!--						:icon="'search'"-->
<!--						readonly-->
<!--						style="width:100%;margin-bottom: 5px;"-->
<!--						:class="$style['readonlyDisabled']"-->
<!--					></el-input>-->
<!--					<el-input type="textarea" autosize :class="$style['readonlyDisabled']" :value="scope.row._oldQuestions[i].question_description" readonly></el-input> &ndash;&gt;-->
<!--					<el-input-->
<!--						v-model="scope.row._oldQuestions[i].des"-->
<!--						size='mini'-->
<!--						:icon="'search'"-->
<!--						:disabled="params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(params.form.status) || !params.form.status)"-->
<!--						readonly-->
<!--						:on-icon-click="() => selectDuty(scope.row, scope.$index, i,'_oldQuestions')"-->
<!--						style="width:100%;margin-bottom: 5px;height: 25px; "-->
<!--					></el-input>-->

<!--					&lt;!&ndash; <el-input-->
<!--						type="textarea"-->
<!--						:maxlength="255"-->
<!--						autosize-->
<!--						v-model="scope.row._oldQuestions[i].question_description"-->
<!--						:disabled="params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(params.form.status) || !params.form.status)"-->
<!--					></el-input> &ndash;&gt;-->

<!--					<el-input disabled  type="textarea" autosize size='mini' style=" margin-left:4px;" :value="scope.row._oldQuestions[i].question_description" readonly></el-input>-->
<!--				 </p>-->
<!--                </template>-->
<!--				<p style="margin-bottom: 5px;    overflow: auto;" v-for="(q,i) in scope.row._questions_des" :key="i">-->
<!--					<span>问题{{ i + 1 + (scope.row._oldQuestions_des || []).length }}</span>-->
<!--					<el-input-->
<!--						v-model="scope.row._questions_des[i].des"-->
<!--						size='mini'-->
<!--						:icon="'search'"-->
<!--						readonly-->
<!--						:disabled="params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(params.form.status) || !params.form.status)"-->
<!--						:on-icon-click="() => selectDuty(scope.row, scope.$index, i,'_questions_des')"-->
<!--						style="width:100%;margin-bottom: 5px;height: 25px; "-->
<!--						@keyup.native="makeNewQuestionDes($event, scope.$index, i)"-->
<!--					></el-input>-->
<!--					<el-input-->
<!--						:maxlength="255"-->
<!--						autosize-->
<!--						type="textarea"-->
<!--						size='mini' style="margin-left:4px;"-->
<!--						v-model="scope.row._questions_des[i].question_description"-->
<!--						@keyup.native="makeNewQuestionDes($event, scope.$index, i)"-->
<!--						:disabled="params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(params.form.status) || !params.form.status)"-->
<!--					></el-input>&lt;!&ndash; 业务状态 = (售前处理 or 售后处理 or 创建)时能编辑问题描述 &ndash;&gt;-->
<!--				</p>-->
<!--				<div v-if="scope.row.id">附件数：<span :class="$style['add-icon']" :disabled="params.isClosed" @click="uploadAction('get', scope.row.id, scope.row)">{{ scope.row.attachment_count || 0 }}</span>-->
<!--					<span-->
<!--						:class="$style['add-icon']"-->
<!--						:style="{-->
<!--							float: 'right',-->
<!--							'font-size': '20px!important',-->
<!--							cursor: params.isClosed ? 'no-drop' : 'pointer',-->
<!--						}"-->
<!--						@click="() => params.isClosed  ? '' : uploadAction('post', scope.row.id, scope.row)"-->
<!--					>+</span>-->
<!--				</div>-->
<!--			</template>-->
<!--		</el-table-column>-->
    <el-table-column label="问题分类" width="540" show-overflow-tooltip :class-name="$style['input100']" :render-header="headerQuestionDes">
      <template slot-scope="scope">
      <template v-if="scope.row._oldQuestions_des">
        <p style="margin-bottom: 5px;   overflow: auto;" v-for="(q,i) in scope.row._oldQuestions_des" :key="scope.row.id+'-'+i">
          <span>问题{{ i + 1 }}</span>
          <el-input
            :value="(q.first_category|| '')+'-'+(q.second_category||'')"
            size='mini'
            :icon="'search'"
            readonly
            disabled
            style="width:100%;margin-bottom: 5px;height: 25px; "
          ></el-input>
          <el-button type="text" @click="checkView(q)" >查看</el-button>
        </p>
      </template>
      <p style="margin-bottom: 5px;    overflow: auto;" v-for="(q,i) in scope.row._questions_des" :key="i">
        <span>问题{{ i + 1 + (scope.row._oldQuestions_des || []).length }}</span>
        <el-input
          :value="q.knowledge_scheme_id?(q.first_category|| '')+'-'+(q.second_category||''):''"
          size='mini'
          :icon="'search'"
          readonly
          @click.native="newSelectDuty(scope.row, scope.$index, i,q,'_questions_des')"
          :disabled="params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(params.form.status) || !params.form.status)"

          style="width:100%;margin-bottom: 5px;height: 25px; "
        ></el-input>
        <el-button type="text" @click="checkView(q)" >查看</el-button>
      </p>
      <div v-if="scope.row.id">附件数：<span :class="$style['add-icon']" :disabled="params.isClosed" @click="uploadAction('get', scope.row.id, scope.row)">{{ scope.row.attachment_count || 0 }}</span>
        <span
          :class="$style['add-icon']"
          :style="{
							float: 'right',
							'font-size': '20px!important',
							cursor: params.isClosed ? 'no-drop' : 'pointer',
						}"
          @click="() => params.isClosed  ? '' : uploadAction('post', scope.row.id, scope.row)"
        >+</span>
      </div>

    </template>

<!--      <template slot-scope="scope">-->
<!--        <p style="margin-bottom: 5px; display: flex; align-items: center; overflow: auto;" v-for="(q,i) in scope.row.listAftersaleOrderQuestionSub" :key="i">-->
<!--          <span>问题{{ i + 1 }}</span>-->
<!--          <el-input-->
<!--            :value="(q.first_category|| '')+'-'+(q.second_category||'')"-->
<!--            size='mini'-->
<!--            :icon="'search'"-->
<!--            readonly-->
<!--            :disabled="params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(params.form.status) || !params.form.status)"-->
<!--            :on-icon-click="() => newSelectDuty(scope.row, scope.$index, i,q)"-->
<!--            style="width:100%;margin-bottom: 5px;height: 25px; "-->
<!--          ></el-input>-->
<!--          <el-button type="text" @click="checkView(q)" >查看</el-button>-->
<!--        </p>-->
<!--        <div v-if="scope.row.id">附件数：<span :class="$style['add-icon']" :disabled="params.isClosed" @click="uploadAction('get', scope.row.id, scope.row)">{{ scope.row.attachment_count || 0 }}</span>-->
<!--          <span-->
<!--            :class="$style['add-icon']"-->
<!--            :style="{-->
<!--							float: 'right',-->
<!--							'font-size': '20px!important',-->
<!--							cursor: params.isClosed ? 'no-drop' : 'pointer',-->
<!--						}"-->
<!--            @click="() => params.isClosed  ? '' : uploadAction('post', scope.row.id, scope.row)"-->
<!--          >+</span>-->
<!--        </div>-->
<!--      </template>-->
    </el-table-column>
		<el-table-column label="解决方案" width="340" :render-header="renderSolutionBtn">
			<template slot-scope="scope">
				<a
					href="javascript:;"
					:class="$style['plan-btn']"
					@click="toPlanDetail(obj.after_plan_id, obj.after_plan_type,obj)"
					v-for="(obj, i) in scope.row.listAftersaleOrderPlan"
					:style="(obj.after_plan_status === 'SUBMIT' || obj.after_plan_status === 'REASON') ? 'color:gray;' : ''"
				>
					{{ {SUPPLY: '补件',REPAIR: '服务',REFUND: '退款',RETURNS: '退/换货',GIFT:'赠品'}[obj.after_plan_type] }}{{ obj.after_plan_no }}<span style="margin:0 8px"></span>{{ obj.create_time | dataFormat1 }}<span v-if="obj.after_plan_status !== 'SUBMIT'" @click.stop="delPlan(obj)" class="el-icon-close" :style="{ cursor: params.isClosed || !params.isHasRights2Edit ? 'no-drop' : 'pointer' }"></span><br>

					<el-tooltip class="item" effect="dark" :key="i" placement="top" v-for="(val, i) in planStatus[obj.after_plan_id]">
						<div slot="content" @click="openBill(val.bill_id, val.bill_type, val.bill_returns_no)" style="cursor: pointer;">{{ val.bill_returns_no }}</div>
						<span>{{ val.status }}<br></span>
				    </el-tooltip>
				</a>
                <a
					href="javascript:;"
					:class="$style['plan-btn']"
                    style="color:gray"
                    @click="toQualityFeedbackDetail(scope.row._quality_feedback_id)"
                    v-if="scope.row._quality_feedback_id"
				>
					品质反馈{{scope.row._quality_feedback_no}}<span style="margin:0 8px"></span>{{scope.row._quality_feedback_create_timeStr}}
				</a>
			</template>
		</el-table-column>
	</el-table>
	<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData" :callback="() => $emit('updateAfterUpload')"></xpt-upload>
</div>
</template>

<script>
	export default {
		props: ['params'/*{
			merge_trade_id,
			merge_trade_no,
			after_order_id,
			after_order_no,
			isClosed,
		}*/, 'goodsDataListVO'],
		data (){
			return {
				ifClickUpload: false,
				copyQuestionsIndex: '',
				uploadData: {},
				planStatus: {},
				onlySeeSolution:false,//林氏用户查看经销商
        hasVerification:false,
				listAfterMaterialVO: [/*{
		            "sys_trade_no": "LS20170616180553001494",
		            "merge_trade_no": "HB20170615141035001170",
		            "real_name": null,
		            "material_number": null,
		            "customer_name": "m18782206113",
		            "material_name": null,
		            "material_specification": null,
		            "shop_name": "B2C商城",
		            "number": 1,
		            "commit_time": 1498262400000,
		            "zd_delivery_time": 1505443607000,
		            "batch_trade_no": "HB20170615141035001170-9",
		            "material_unit": null,
		            "is_stop": null,
		            "stand_price": 100,
		            "_questions_des": [],//问题对应描述
		        }*/
        ],
		        tableSelect2Delete: [],
          treeQuestionClassData:[],
			}
		},
		methods: {
      newSelectDuty(row, parentIdx, currentIdx,currentData,nameList){
        if(this.params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(this.params.form.status) || !this.params.form.status)){
          return;
        }
        this.$root.eventHandle.$emit('alert', {
          title: '添加问题分类',
          style: 'width:1000px;height:400px',
          component: () => import('@components/after_sales/add-question-class-modal.vue'),
          params: {
            callback: (d) => {
              console.log(d,'d',row)
              const value = this.listAfterMaterialVO[parentIdx][nameList][currentIdx]
              value.first_category = d.firstCategory
              value.second_category = d.secondCategory
              value.third_category = d.thirdCategory
              value.knowledge_scheme_id = d.id
              if(currentIdx===this.listAfterMaterialVO[parentIdx][nameList].length-1){
                this.listAfterMaterialVO[parentIdx][nameList].push({
                  knowledge_scheme_id:undefined,
                  first_category: undefined,
                  second_category: undefined,
                  third_category: undefined,
                })
              }
              this.$set(this.listAfterMaterialVO[parentIdx][nameList], currentIdx, value)
            },
            options: this.treeQuestionClassData,
            alertId: new Date().getTime(),
            value: currentData.knowledge_scheme_id ? [currentData.first_category, currentData.second_category, currentData.third_category] : undefined,
            type:'edit'
          }
        })
      },
      newMakeNewQuestionDes(){},
      getTreeData(){
        this.ajax.postStream(`/afterSale-web/api/aftersale/knowledge/list`,{},(res)=>{
          if(!res.body.result) {
            this.$message.error(res.body.msg)
            return;
          }
          const tree = res.body.content || []
          const IndexKey=['firstCategory','secondCategory','thirdCategory']
          function treeInit(tree,index){
            tree.forEach(item=>{
              item.children = item.childList.length?item.childList:null
              item.value = item[IndexKey[index]]
              item.label = item[IndexKey[index]]
              if(item.children && item.children.length){
                treeInit(item.children,index+1)
              }
            })
          }
          treeInit(tree,0)
          this.treeQuestionClassData = tree
        })
      },
      // 查看
      checkView(val){
        if( !val.knowledge_scheme_id){
          this.$message.error('请先选择问题分类')
          return
        }
        this.$root.eventHandle.$emit('alert', {
          title: '查看问题分类',
          style: 'width:1000px;height:400px',
          component: () => import('@components/after_sales/check-question-modal.vue'),
          params: {
            id: val.knowledge_scheme_id
          }
        })
      },
			selectDuty(row, parentIdx, currentIdx,type) {
				let self = this
				if(self.params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(self.params.form.status) || !self.params.form.status)){
					return;
				};

				this.$root.eventHandle.$emit('alert',{
				params: {
					callback: d => { console.log('售后单选择责任', row, parentIdx, currentIdx)
						self.listAfterMaterialVO[parentIdx][type][currentIdx].des = `${d.parentName} - ${d.name}`
						self.listAfterMaterialVO[parentIdx][type][currentIdx].question_detail_id = d.id
						if(type == '_questions_des'){
							self.makeNewQuestionDes('', parentIdx, currentIdx)
						}
					},
				},
				component:()=>import('./selectQuestion.vue'),
					style:'width:900px;height:600px',
					title:'选择责任问题'
				})
			},
      //
      headerQuestionDes(h, {column, $index}){
        return h('div', {
          style: {
            display: 'flex',
            justifyContent: 'space-between',
          },
        },[
          h('span', column.label),
          h('el-button',{
            props:{
              type: 'text',
              size: 'mini',
              disabled: this.params.isClosed || !(/(PRE_HANDLE|AFTER_HANDLE)/.test(this.params.form.status) || !this.params.form.status)
            },
            on:{
              click: () => {
                this.addNewQuestionDes()
              }
            }
          },'批量添加')
        ])
      },
      // 批量添加
      addNewQuestionDes(){
        this.$root.eventHandle.$emit('alert', {
          title: '添加问题分类',
          style: 'width:1000px;height:400px',
          component: () => import('@components/after_sales/add-question-class-modal.vue'),
          params: {
            callback: (d) => {
              const list = this.listAfterMaterialVO || []
              list.forEach(parent=>{
                if(parent._questions_des && parent._questions_des.length>0){
                  // parent._questions_des.forEach(children=>{
                  //   if(!children.knowledge_scheme_id){
                  //     children.first_category = d.firstCategory
                  //     children.second_category = d.secondCategory
                  //     children.third_category = d.thirdCategory
                  //     children.knowledge_scheme_id = d.id
                  //   }
                  //
                  // })
                  parent._questions_des.splice(parent._questions_des.length-1,1,{
                    first_category: d.firstCategory,
                    second_category: d.secondCategory,
                    third_category: d.thirdCategory,
                    knowledge_scheme_id: d.id
                  })
                  parent._questions_des.push({
                    knowledge_scheme_id:undefined,
                    first_category: undefined,
                    second_category: undefined,
                    third_category: undefined,
                  })
                }
              })
              this.listAfterMaterialVO = list
            },
            options: this.treeQuestionClassData,
            alertId: new Date().getTime(),
          }
        })
      },
			addProductConsulting() {
				if (this.tableSelect2Delete.length != 1) {
					let msg = this.tableSelect2Delete.length > 1 ? '只可选择1个问题商品' : '请选择问题商品'
					this.$message.error(msg)
					return
				}
				// 限制线下导购直接咨询产品咨询单
				this.ajax.get(`/order-web/api/batchtrade/checkProductConsultationPermission?sysTradeId=${this.tableSelect2Delete[0].sys_trade_id}`,(res)=>{
					if(!res.body.result) {
							this.$message.error(res.body.msg)
							return;
					}
					let self = this;
					this.$root.eventHandle.$emit("alert", {
						params: {
							defaultParams: {
								from_no: self.params.form.after_order_no,
								source_order_id: self.params.form.after_order_id,
								scm_materiel_id: self.tableSelect2Delete[0].merge_material_id,
								material_number: self.tableSelect2Delete[0].material_number,
								buyer_name: self.params.form.buyer_nick,
								question_plate: 'AFTERSALE',
								org_material_number: self.tableSelect2Delete[0].source_material_number,
								sys_trade_id: self.tableSelect2Delete[0].sys_trade_id
							},
							callback: (d) => {},
							__close: () => {}
						},
						component: () =>
							import(
								"@components/product_consulting/components/add_product_consulting.vue"
							),
						style: "width:800px;height:600px",
						title: "产品咨询",
					});
				})
			},
      // 物流投诉索赔
      onLogistics(){
        console.log(this.params,this.tableSelect2Delete,'this.params.after_order_no')
        if(this.tableSelect2Delete.length===0){
          this.$message.error('请选择问题商品')
          return
        }
        const  alertId = new Date().getTime()
        let flag = false
        const questionId = this.tableSelect2Delete.map(e=>{
          const list = [...(e._oldQuestions||[]),...(e._questions_des||[])]
          console.log(list,'4555',e)
          if(!list|| list.length==0){
            flag = true
          }else{
            if(!flag){
              for(let item of list){
                if(item.knowledge_scheme_id ){
                  flag = false
                 break
                }else {
                  flag = true
                }
              }
            }
          }
          return e.id
        })
        if( flag) {
          this.$message.error('请先选择问题分类')
          return
        }
        this.$root.eventHandle.$emit('alert',{
          component: () => import('./submit-onLogistics-modal.vue'),
          style: 'width:800px;height:500px',
          title: '物流投诉索赔',
          params:{
            afterOrderNo:this.params.after_order_no,
            alertId,
            afterOrderId: this.params.after_order_id,
            questionId,
            callBack: () => {
              // this.$root.eventHandle.$emit('removeAlert',alertId)
              this.$emit('updateAfterUpload')
            }
          }
        })
      },
			viewdOrder(id,mid){
				var params = {batch_trade_id:id,merge_trade_id:mid}
				this.$root.eventHandle.$emit('creatTab',{name:"批次订单详情",
					params:params,
					component:() => import('@components/order/batch_order.vue')
				})
			},
			goToDetail(id){
				this.$root.eventHandle.$emit('creatTab',{
					name:'咨询单详情',
					component: ()=>import('@components/after_sales/consultation'),
					params: {id,orderList: this.params.orderList
					},
				})
			},
			checkHis(batch_trade_id){
				let self=this;
				let data={
					batch_trade_id
				}
				return new Promise((resolve,reject)=>{
					self.ajax.postStream('/afterSale-web/api/aftersale/consultion/checkHis',data,res=>{
						resolve(res.body)
					},err=>{
						self.$message.error(err)
						reject(false)
					})
				})
			},
      getFlagAdd(){
        let  payType = __AUX.get('SHZSK');
        this.hasVerification = payType?.[0]?.tag === 'Y'
      },
			push(data){
				console.log(data)
				let consultionType = data ? data.consultionType:null
        let parentConsultionType = data ? data.parentConsultionType:null
				this.$root.eventHandle.$emit('creatTab',{
					name: '咨询单详情',
					component: ()=>import('@components/after_sales/consultationNew'),
					params: {
						isFromAfterSales: {
							after_order_id: this.params.after_order_id,
							after_order_no: this.params.after_order_no,
							merge_trade_id: this.params.merge_trade_id,
							merge_trade_no: this.params.merge_trade_no,
							question_goods_ids: this.tableSelect2Delete.map(o => o.id),
							batch_trade_id: (this.tableSelect2Delete[0] || {}).batch_trade_id,
							batch_trade_no: (this.tableSelect2Delete[0] || {}).batch_trade_no,
							consultionType:consultionType,
              parentConsultionType: parentConsultionType
						},
					},
				})
			},
			// 下推咨询单
			async pushConsultation (){
				if(this.tableSelect2Delete.some(o => o.if_goods_question === 'N' || o.batch_trade_id !== this.tableSelect2Delete[0].batch_trade_id)){
					this.$message.error('只能选相同批次单号且商品问题下推咨询单')
				}else if(!!this.tableSelect2Delete.length) {
					// 新增判断是否能继续咨询
					let checkResult=await this.checkHis(this.tableSelect2Delete[0].batch_trade_id)
					if(!checkResult.result){
						let self=this
						this.$root.eventHandle.$emit('alert',{
						title: '提示',
						style:'width:400px;height:200px',
						component: () => import('@components/after_sales/alert/confirm'),
						params: {
							orderList:this.params.orderList,
							result:checkResult.content,
							callback:(result)=>{
									if(result.type=="2"){
										self.goToDetail(checkResult.content.consultionId,)
									}
									if(result.type=="0"){
										self.push()
									}
								}
							}
						})
					}else{
						this.push()
					}
				}else{
					this.push()
				}
            },
			// 物流报备
			async logisticsReport (){
				if(this.tableSelect2Delete.some(o => o.if_goods_question === 'N' || o.batch_trade_id !== this.tableSelect2Delete[0].batch_trade_id)){
					this.$message.error('只能选相同批次单号且商品问题下推咨询单')
				}else if(!!this.tableSelect2Delete.length) {
					// 新增判断是否能继续咨询
					let checkResult=await this.checkHis(this.tableSelect2Delete[0].batch_trade_id)
					if(!checkResult.result){
						let self=this
						this.$root.eventHandle.$emit('alert',{
						title: '提示',
						style:'width:400px;height:200px',
						component: () => import('@components/after_sales/alert/confirm'),
						params: {
							orderList:this.params.orderList,
							result:checkResult.content,
							callback:(result)=>{
									if(result.type=="2"){
										self.goToDetail(checkResult.content.consultionId,)
									}
									if(result.type=="0"){
										self.push({consultionType:'WLBB',parentConsultionType: 'SH'})
									}
								}
							}
						})
					}else{
						this.push({consultionType:'WLBB',parentConsultionType: 'SH'})
					}
				}else{
					this.push({consultionType:'WLBB',parentConsultionType: 'SH'})
				}
            },
            // 品质反馈
            qualityFeedback(){
                if(this.tableSelect2Delete[0].if_quality_feedback=='Y'){
                    this.$message.error("当前商品已提交品质反馈，不能重复提交")
                    return
                }
                this.$root.eventHandle.$emit('alert',{
                    title: '提交品质反馈',
                    style:'width:900px;height:650px',
                    component: () => import('@components/after_sales/submitQualityFeedback'),
                    params: {
                            after_order_id: this.params.after_order_id,
                            after_order_no: this.params.after_order_no,
                            tableSelect:this.tableSelect2Delete[0]||{},
                            callback: () => {
                                //退出提交品质反馈时更新售后单详情
                                this.$emit('updateAfterUpload')
                            }
                    },
                })
            },
			openBill (id, type, billNo){
				var componentUrl = {
					THBG: 'after_invoices/returnexchangedetail_2',//退换货跟踪单
					THGZ: 'after_invoices/returnexchangedetail_2',//退换货跟踪单(历史)
					FWD: 'after_sales_service/detail.vue',//服务单
					JF: 'after_sales_service/detail.vue',//服务单(历史)
					BJSQ: 'after_sales_supply/supplyOrder_2.vue',//补件申请单
					BJ: 'after_sales_supply/supplyOrder_2.vue',//补件申请单(历史)
					TKD: 'after_sales_refund/refundOrder_2',//退款单
					TK: 'after_sales_refund/refundOrder_2',//退款单(历史)
					TKSQ: 'after_sales_refund/refundRequest_2',//退款申请单
				}[billNo.replace(/\d/g, '')]

				this.$root.eventHandle.$emit('creatTab', {
					name: {
						RETURNS: '退换货跟踪单',
						SUPPLY: '补件单',
						OUTBOUNDSUPPLY: '补件其他出库单',
						REFUND: '退款单',
						AFTERSALES: '售后单',
						REPAIR: '服务单',
						ANALYSIS: '责任分析单',
						SERVICE: '服务方案',
					}[type] + '详情',
					params: { id },
					component: () => import('@components/' + componentUrl)
		        })
			},
			// 附件操作
			uploadAction (method, child_no, row){
				if(method === 'post'){
					this.ifClickUpload = true
					this.uploadData = {
						parent_name: 'AFTER_ORDER',
						parent_no: this.params.after_order_no,
						child_name: 'QUESTION_GOODS',
						child_no: String(child_no),
						content: {
							material_code: row.material_number,
							batch_trade_no: row.batch_trade_no,
							question_description: row._oldQuestions_des.slice(-1)[0],
						},
					}
					setTimeout(() => {
						this.ifClickUpload = false
					}, 100)
				}else {
					this.$root.eventHandle.$emit('alert', {
						params: {
							parent_name_txt: '售后单号',
							parent_no : this.params.after_order_no,
							child_name : 'QUESTION_GOODS',
							child_no : null,
							// child_name_txt: '商品问题id',
							ext_data : null,
							parent_name : 'AFTER_ORDER',
							notNeedDelBtn: this.params.isClosed || !this.params.isHasRights2Edit,
							// callback: files => {
							// 	this.listAfterMaterialVO[index].attachment_count = files.length
							// },
							total: row.attachment_count,
							nickname: this.params.form.buyer_nick,
							mergeTradeId: this.params.form.merge_trade_id,
						},
						component: ()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
						style: 'width:80%;height:600px',
						title: '下载列表',
					})
				}
			},
			// 选择业务员
			selectRealName (row){
				this.$root.eventHandle.$emit('alert',{
					title: '业务员列表',
					style:'width:800px;height:600px',
					component:()=>import('@components/after_sales_report/select_personel'),
					params: {
						callback: d => {
							this.$set(row, 'real_name', d.realName)
							this.$set(row, 'group_name', d.groupName)
						}
					},
				})
			},
			// 添加商品问题
			addGoodsFun (){
				if(this.params.isReSaleOrder && !this.params.merge_trade_id){
					this.$message.error('请先选单！')
					return
				}

				this.$root.eventHandle.$emit('alert', {
			        title: '添加问题商品',
			        style: 'width: 80%; height: 600px',
			        component: () => import('@components/after_sales/selectGoods'),
			        params: {
			        	id: this.params.merge_trade_id,//有源、无源区分
			        	isReSaleOrder: this.params.isReSaleOrder,
						callback: data => {
							data.forEach(obj => {
								obj.if_goods_question = 'Y'
								// this.$set(obj, '_questions_des', ['']);
								this.$set(obj, '_questions_des', [{des:'',question_detail_id:'',question_description:''}]);
								if(!obj.act_price) obj.act_price = 0
								if(!this.params.merge_trade_id){
									if(obj.number === undefined) this.$set(obj, 'number', 1)
									this.$set(obj, 'real_name', this.params.staff_name)
									this.$set(obj, 'group_name', this.params.staff_group_name)
								}
							})
							this.listAfterMaterialVO = this.listAfterMaterialVO.concat(data)

							if(this.params.merge_trade_id){
								this.$emit('sendOrderDetailId', this.listAfterMaterialVO.map(obj => obj.material_number))
							} else {
								this.$emit('selectMergeOrderBtnStatus', true)//当添加了无源商品时，锁定选单按钮
							}
							setTimeout(() => {
								this.$refs.$table.$el.querySelector('.el-table__body-wrapper').scrollTop = 999999//滚动到底部
							})
						}
			        }
			    })
			},
			// 添加非商品问题
			addNoGoodsFun (){
				this.listAfterMaterialVO.push({
					_questions_des: [{dec:'',question_detail_id:'',question_description:''}],
					if_goods_question: 'N',
					real_name: this.params.staff_name,
					group_name: this.params.staff_group_name,
					// shop_name: this.params.merge_trade_id ? this.params.shop_name : '',
				})
				setTimeout(() => {
					this.$refs.$table.$el.querySelector('.el-table__body-wrapper').scrollTop = 999999//滚动到底部
				})
			},
			delFun (){
				var parent_question_ids = this.tableSelect2Delete.map(obj => obj.id).filter(Boolean)
				,	_arrayDel = () => {
					this.tableSelect2Delete.forEach(obj => {
						this.listAfterMaterialVO.splice(this.listAfterMaterialVO.indexOf(obj), 1)
					})

					if(!this.params.merge_trade_id && !this.listAfterMaterialVO.length){
						// 解锁选单按钮
						this.$emit('selectMergeOrderBtnStatus', false)
					}
				}

				if(this.params.after_order_id && parent_question_ids.length){
					// 判断 -> 所选问题商品行已有关联方案，不允许删除
					this.ajax.postStream('/afterSale-web/api/aftersale/order/question/delete', parent_question_ids, res => {
						if(res.body.result){
							this.$message.success(res.body.msg)
							_arrayDel()
							this.$emit('updateAfterUpload')
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}else {
					_arrayDel()
				}
			},
			handleSelectionChange (data){
                this.tableSelect2Delete = data
                console.warn(data)
			},
			makeNewQuestionDes (e, parentQuestionIndex, questionDesIndex){ console.log('触发增加一行数据', e, parentQuestionIndex, questionDesIndex)
				let self = this;
				var _questions_desList = this.listAfterMaterialVO[parentQuestionIndex]._questions_des.map(item=>{return item.question_description})
				var _questions_List = this.listAfterMaterialVO[parentQuestionIndex]._questions_des.map(item=>{return item.dec})
				// self.$set(, '', [{des:'',question_detail_id:'',question_description:''}]);
				// e.target.setAttribute('rows', (e.target.value.match(/\r?\n/g)||[]).length + 1)
				// this.copyQuestionsContent(parentQuestionIndex)

				if(e.keyCode === 8){
					// console.log(questionDesIndex === (_questions_List.length - 2 ),self.listAfterMaterialVO[parentQuestionIndex]._questions_des.question_description,self.listAfterMaterialVO._questions_des[questionDesIndex].des)
					if(questionDesIndex === (_questions_List.length - 2 )&& self.listAfterMaterialVO[parentQuestionIndex]._questions_des[questionDesIndex].question_description == ''&&self.listAfterMaterialVO[parentQuestionIndex]._questions_des[questionDesIndex].des == ''){
						// _questions_desList.pop('')//删除一个
						// _questions_List.pop()
						self.listAfterMaterialVO[parentQuestionIndex]._questions_des.pop()
					}
				}

				if(questionDesIndex === _questions_List.length - 1 &&e.keyCode !== 8){
					// _questions_desList.push('')//添加一个
					// _questions_List.push({des: '', id: 2})
					self.listAfterMaterialVO[parentQuestionIndex]._questions_des.push({des:'',question_detail_id:'',question_description:''})

				}
				// this.$set(this.listAfterMaterialVO[parentQuestionIndex]._questions, _questions_desList.length, {des: '', id: 2})


			},
			renderSolutionBtn (createElement, { column }){
				return createElement(
		            'div',
		            [column.label, createElement(
		            	'span',
		            	{
							attrs:{
								class: this.$style['add-icon'],
								style: 'margin-left:10px;font-size:20px!important;',
							},
							on: {
								click: () => {
									var msg
									if(!this.params.after_order_id || this.listAfterMaterialVO.length !== this.goodsDataListVO.length) msg = '请先保存售后单'
									else if (this.params.isClosed) msg = '售后单已完结'
									else if (!this.listAfterMaterialVO.length) msg = '请添加一个问题'
									else if(!this.params.hasRightsToEidtSolution) msg = '当前操作人不能操作方案'
									else if(this.params.form.status === 'WAIT_HANDLE') msg = '售后待办不能新增方案'
									else if (this.params.form.status !== 'PRE_HANDLE' && this.params.isReSaleOrder) msg = '请进行业务锁定！'

									if(msg){
										this.$message.error(msg)
										return
									}
                  //校验是否问题分类有填写
                  // if(this.hasVerification){
                  //   let flag = false;
                  //   outerLoop:
                  //   for(let parent of this.listAfterMaterialVO){
                  //     // if(parent.listAftersaleOrderQuestionSub&& parent.listAftersaleOrderQuestionSub.length>0){
                  //     //   for (let child of parent.listAftersaleOrderQuestionSub){
                  //     //     if(child.knowledge_scheme_id){
                  //     //       flag = true
                  //     //       break outerLoop;
                  //     //     }
                  //     //   }
                  //     // }
                  //     if(parent._oldQuestions_des&&parent._oldQuestions_des.length>0){
                  //       flag = true
                  //       break outerLoop;
                  //     }
                  //     if(parent._questions_des&&parent._questions_des.length>0){
                  //       for (let child of parent._questions_des){
                  //             if(child.knowledge_scheme_id){
                  //               flag = true
                  //               break outerLoop;
                  //             }
                  //           }
                  //     }
                  //   }
                  //   if(!flag){
                  //     this.$message.error('请先在问题商品信息添加问题分类后，【保存】售后单再添加解决方案!')
                  //     return;
                  //   }
                  // }


									//检验：点击售后锁定后，进行保存或添加方案时必须要填至少一个处理进度信息
									this.$emit('testAfterSaleLock', () => {
										this.$root.eventHandle.$emit('creatTab',{
							                name: '售后方案',
							                url: 'after_solutions/solutions',
							                params: {
							                	isReSaleOrder: this.params.isReSaleOrder,
							                	type: this.params.isReSaleOrder ? 'SUPPLY' : '',
							                	afterOrderInfo: {
							                		after_order_id: this.params.after_order_id,
													after_order_no: this.params.after_order_no,
													merge_trade_id: this.params.merge_trade_id,
													merge_trade_no: this.params.merge_trade_no,
													canEditSolutions:!this.onlySeeSolution,
													//canEditSolutions:true,
													form:this.params.form
							                	},
							                },
							                component: () => import('@components/after_solutions/solutions')
							            })
									})
								},
							}
						},
						['+']
					)]
		        )
			},
			_dataMap (data, type){
				var reduceFunc =
					type === 'left'
					? (obj, key) => { obj[key] = data[map[key] || key] }
					: (obj, key) => { obj[map[key] || key] = data[key] }
				,	map = {
					id: '',
					if_has_source: '',
					goods_code: 'material_number',
					goods_name: 'material_name',
					merge_trade_id: '',
					merge_trade_no: '',
					units: 'material_unit',
					num: 'number',
					price: 'stand_price',
					act_price: '',
					specification: 'material_specification',
					saleman_name: 'real_name',
					saleman_group: 'group_name',
					batch_trade_id: '',
					batch_trade_no: '',
					merge_material_id: '',
					if_goods_question: '',
					shop_name: '',
					shop_id: '',
					material_id: '',
					attachment_count: '',
					user_shop_id: '',
					user_shop_name: '',
					volume: '',
					source_material_number: '',
					source_material_name: '',
					sys_trade_id: '',
                    sys_trade_no: '',
                    if_quality_feedback:'',//是否已提交品质反馈
					question_type: ''
				}

				return Object.keys(map).reduce((obj, key) => {
					reduceFunc(obj, key)
					return obj
				}, {})
			},
			_doLayout (){
				this.$refs.$table.doLayout()
			},
			// 给父组件保存数据时调用
			returnSaveData (){
				try {
				return {
					aftersaleOrderQuestionRowVO: this.listAfterMaterialVO.map((obj, idx) => {
						// let listAftersaleOrderQuestionSub = obj._questions_des.concat(obj._oldQuestions?obj._oldQuestions:[]);
						// if(!obj._oldQuestions){
						// 	obj._questions_des[0].question_description = obj._questions_des[0].question_description?obj._questions_des[0].question_description:'无';
						// }
						// listAftersaleOrderQuestionSub = listAftersaleOrderQuestionSub.filter(item=>{return !(!item.question_description && !item.question_detail_id)})
						// listAftersaleOrderQuestionSub.forEach(item=>{
						// 	item.question_description = item.question_description.trim()
						// 	if(!item.question_description.trim()){
						// 		item.question_description = '无'
						// 	}
						// })
            console.log(obj,'555656')
            let listAftersaleOrderQuestionSub = obj._questions_des.concat(obj._oldQuestions?obj._oldQuestions:[]);
            if(!obj._oldQuestions){
            	obj._questions_des[0].question_description = obj._questions_des[0].question_description?obj._questions_des[0].question_description:'无';
            }
            listAftersaleOrderQuestionSub = listAftersaleOrderQuestionSub.filter(item=>{return (item.question_description || item.knowledge_scheme_id)})
						var returnObj = {
							aftersaleOrderQuestion: {
								...this._dataMap(obj, 'left'),
								if_has_source: this.params.merge_trade_id ? 'Y' : 'N',
								material_id: obj.source_material_id || obj.materia_id || obj.material_id,
							},
							listAftersaleOrderQuestionSub:listAftersaleOrderQuestionSub.map(val => ({ question_description: val.question_description||'无', question_detail_id:val.question_detail_id,id:val.id,knowledge_scheme_id:val.knowledge_scheme_id,first_category:val.first_category,second_category:val.second_category,third_category:val.third_category,})),

								//如果有一个子问题，则对于空的input过滤掉不校验‘子问题描述为空’
								// .filter((val, index) => {
								// 	val = val.dec.trim()
								// 	// if(!obj._oldQuestions_des && index === 0 && !val && obj.question_type != 'SUPPLY') throw('子问题描述')
								// 	return val//对输入空格的也过滤
								// })

								// 组装保存数据
							// listAftersaleOrderLiabilitySub:
							// 	obj._questions.filter((val, index) => {
							// 		let dess = val.des.trim()
							// 		// 用辅助资料做个开关，开则校验必填
							// 		let ifOpen = true
							// 		if(!obj._oldQuestions && index === 0 && !dess && ifOpen) throw(`序号${Number(idx) + Number(1)}问题商品信息-子问题${Number(index) + Number(1)}责任问题`)
							// 		return dess//对输入空格的也过滤
							// 	}).map(val => ({ question_description: val }))
							}

						Object.defineProperties(returnObj.aftersaleOrderQuestion, {
						    _dealer_customer_id: {
						        configurable: true,
						        writable: true,
						        enumerable: false,
						        value: obj.dealer_customer_id,
						    }
						})
						if(!returnObj.aftersaleOrderQuestion.saleman_name && returnObj.aftersaleOrderQuestion.question_type != 'SUPPLY') throw('业务员')
						else if(obj.if_goods_question === 'Y' && !returnObj.aftersaleOrderQuestion.num && returnObj.aftersaleOrderQuestion.question_type != 'SUPPLY') throw('数量')
						else if(!returnObj.aftersaleOrderQuestion.shop_name && returnObj.aftersaleOrderQuestion.question_type != 'SUPPLY') throw('订单店铺')
						// else if(obj.if_goods_question === 'Y' && !returnObj.aftersaleOrderQuestion.price) throw('标准售价')
console.log('哈哈哈，售后单保存数据', returnObj, this.listAfterMaterialVO)
						return returnObj
					})
				}
				}catch (msg){
					this.$message.error(msg + '不能为空')
					throw(msg)
				}
			},
			/**
			*刷新的时候没有拿到最新的枚举
			*{id,plan_type,after_plan_status}分别为方案id,方案的枚举类型，方案的状态
			***/
			_getPlanStatus (getPlanStatuList){
				var _loop = () => {
					var plan = getPlanStatuList.shift()
					if(plan){
						var [id, plan_type,after_plan_status] = plan
						this.ajax.postStream('/afterSale-web/api/aftersale/order/bill/allstatuslist', {
							aftersale_plan_id: id,
							aftersale_plan_type: plan_type.toLocaleLowerCase(),
						}, res => {
							if(res.body.result){
								let list = res.body.content.list;
								//list[0].currentStatus = after_plan_status;
								this.$set(this.planStatus, id, res.body.content.list)
							}
							_loop()
						}, err => {
							_loop()
						})
					}
				}
				_loop()

				//应该是状态改变就应该要重新拿
				/*if(!this.planStatus[id]){
					this.ajax.postStream('/afterSale-web/api/aftersale/order/bill/allstatuslist', {
						aftersale_plan_id: id,
						aftersale_plan_type: plan_type.toLocaleLowerCase(),
					}, res => {
						if(res.body.result){
							let list = res.body.content.list;
							list[0].currentStatus = after_plan_status;
							console.log('list',list);
							console.log("this.planStatus",this.planStatus);
							this.$set(this.planStatus, id, res.body.content.list)
							console.log("this.planStatus",this.planStatus)
						}
					})
				}*/
				/*var data = this.planStatus[id];
				//如果是存在的话，则需要判断方案状态是否有修改
				if(!data || data[0].after_plan_status != after_plan_status){
					this.ajax.postStream('/afterSale-web/api/aftersale/order/bill/allstatuslist', {
						aftersale_plan_id: id,
						aftersale_plan_type: plan_type.toLocaleLowerCase(),
					}, res => {
						if(res.body.result){
							let list = res.body.content.list;
							if(list[0]){
								list[0].currentStatus = after_plan_status;
							}

							console.log('list',list);
							console.log("this.planStatus",this.planStatus);
							this.$set(this.planStatus, id, res.body.content.list)
							console.log("this.planStatus",this.planStatus)
						}
					})
				}*/

			},
			translate (){
				console.log('子问题字段');

                var getPlanStatusList = []
                var quality_feedback_no=''
                var quality_feedback_id=''
                var quality_feedback_create_timeStr=''
				this.listAfterMaterialVO = this.goodsDataListVO.map(obj => {
					// console.log(JSON.stringify(this._dataMap(obj.aftersaleOrderQuestion, 'right')))
					// for(var key in obj.listAftersaleOrderPlan){
					// 	obj.listAftersaleOrderPlan[key].questionDetail = obj.listAftersaleOrderQuestionSub[key];
					// }

					;(obj.listAftersaleOrderPlan || []).forEach(planObj => {
						getPlanStatusList.push([planObj.after_plan_id, planObj.after_plan_type,planObj.after_plan_status])
					})
					console.log('看看所说的枚举');
                    console.log("this.planStatus1111",this.planStatus);

                    if(obj.quality_feedback_id&&obj.quality_feedback_no&&obj.quality_feedback_create_timeStr){
                        quality_feedback_no=obj.quality_feedback_no
                        quality_feedback_id=obj.quality_feedback_id
                        quality_feedback_create_timeStr=obj.quality_feedback_create_timeStr
                    }else{
                        quality_feedback_no=""
                        quality_feedback_id=""
                        quality_feedback_create_timeStr=""
                    }

					return {
						...this._dataMap(obj.aftersaleOrderQuestion, 'right'),
						listAftersaleOrderPlan: obj.listAftersaleOrderPlan || [],
            listAftersaleOrderQuestionSub: obj.listAftersaleOrderQuestionSub,
						// _oldQuestions_des: obj.listAftersaleOrderQuestionSub.map(obj => obj.question_description),//旧问题不能修改
            _oldQuestions_des: obj.listAftersaleOrderQuestionSub.filter(obj => obj.knowledge_scheme_id),
						_oldQuestions: obj.listAftersaleOrderQuestionSub.map(obj => {
							return {
								id: obj.id,
								question_description: obj.question_description,
								question_detail_id: obj.question_detail_id,
                knowledge_scheme_id: obj.knowledge_scheme_id,
                first_category: obj.first_category,
                second_category: obj.second_category,
                third_category: obj.third_category,
								des:obj.questionOtherDetailVO? (obj.questionOtherDetailVO.liability_type?obj.questionOtherDetailVO.liability_type+'-':'')+(obj.questionOtherDetailVO.firstLevelName?obj.questionOtherDetailVO.firstLevelName+'-':'')+obj.questionOtherDetailVO.secondLevelName+'-'+obj.questionOtherDetailVO.threeLevelName:''
								}
						}),//旧问题不能修改
                        _questions_des: [{id:null, des: '', question_detail_id: null,question_description:''}],
                        _quality_feedback_no:quality_feedback_no,
                        _quality_feedback_id:quality_feedback_id,
                        _quality_feedback_create_timeStr:quality_feedback_create_timeStr
					}
				})
				this._getPlanStatus(getPlanStatusList)
				console.log('this.listAfterMaterialVO',this.listAfterMaterialVO,this.goodsDataListVO)
				!this.listAfterMaterialVO.length && this.$emit('selectMergeOrderBtnStatus', false)
			},
			_listenRemoveTabEvent (tabName){
				this._listenRemoveTabEvent.paramsList.some((obj, index) => {
					if(obj.tabName == tabName){
						this._listenRemoveTabEvent.paramsList.splice(index, 1)
						return true
					}
				})
			},
			// 方案详情
			toPlanDetail (id, type,obj){
				//还需要拿到子问题ID;
				/*var canEditSolutions = this.params.isClosed || !this.params.hasRightsToEidtSolution?false:true*/
				var canEditSolutions = this.onlySeeSolution?false:this.params.isClosed || !this.params.hasRightsToEidtSolution?false:true
				,	params = {
					type,
					isReSaleOrder: this.params.isReSaleOrder,
					afterOrderInfo: {
                		after_order_id: this.params.after_order_id,
						after_order_no: this.params.after_order_no,
						merge_trade_id: this.params.merge_trade_id,
						merge_trade_no: this.params.merge_trade_no,
						after_plan_id: id,
						after_question_id:obj.after_question_sub_id,
						after_plan_group_id:obj.after_plan_group_id,//
						canEditSolutions:canEditSolutions,
						form:this.params.form
	                },
				}

				if(!this._listenRemoveTabEvent.paramsList){
					this._listenRemoveTabEvent.paramsList = []
					this.$root.eventHandle.$on('removeTab', this._listenRemoveTabEvent)
		        }

		        //TODO 方案的唯一性校验有问题，先不做这个校验，
		        this._listenRemoveTabEvent.paramsList.push(params);
				this.$root.eventHandle.$emit('creatTab',{
	                name: '售后方案详情',
	                params,
	                component: () => import('@components/after_solutions/solutions')
	            })


				/*if(this._listenRemoveTabEvent.paramsList.some(obj => obj.afterOrderInfo.after_plan_id === id)){
					this.$message.error('该方案已经打开')
		        }else {
		        	this._listenRemoveTabEvent.paramsList.push(params)
					this.$root.eventHandle.$emit('creatTab',{
		                name: '售后方案详情',
		                params,
		                component: () => import('@components/after_solutions/solutions')
		            })
		        }*/
			},
			//业务员改变后立即搜索
			searchAfterChange2 (e, row){
				if(!e.target.value) return
				this.ajax.postStream('/user-web/api/userPerson/getUserPersonSalesmanList', {
					page: {"length":10,"pageNo":1},
					realName: e.target.value,
				}, res => {
					var d = ((res.body.content && res.body.content.list) || [])[0]

					if(d){
						this.$set(row, 'real_name', d.realName)
						this.$set(row, 'group_name', d.groupName)
						// this.copyQuestionsContent(scope.$index)
					}else {
						this.$message.error('业务员查找匹配失败')
						this.$set(row, 'real_name', '')
						this.$set(row, 'group_name', '')
					}
				})
			},
			//无源或非商品问题订单店铺发生改变后立即搜索
			searchAfterChange (e, scope){
				var self = this
				if(!this.searchAfterChange.params){
					this.ajax.postStream('/user-web/api/sql/listFields', {"page":"cloud_shop_v2"}, res => {
						this.searchAfterChange.params = res.body.content.fields.filter(obj => /店铺名称/.test(obj.comment))[0]
						_searchFunc()
					})
				}else {
					_searchFunc()
				}

				function _searchFunc (){
					if(!e.target.value) return

					self.ajax.postStream('/material-web/api/shopv2/list', {
					  "page_name": "cloud_shop_v2",
					  "where": [{
					    "field": self.searchAfterChange.params.field,
					    "table": self.searchAfterChange.params.table,
					    "value": e.target.value,
					    "operator": "%",
					    "condition": "AND",
					    "listWhere": []
					  }],
					}, res => {
						var d = ((res.body.content && res.body.content.list) || [])[0]

						if(d){
							scope.row.shop_id = d.shop_id
							e.target.value = scope.row.shop_name = d.shop_name
							self.copyQuestionsContent(scope.$index)
						}else {
							self.$message.error('订单店铺查找匹配失败')
							e.target.value = scope.row.shop_name = ''
						}
					})
				}
			},
			// 删除方案
			delPlan (obj){
				if(!this.params.isHasRights2Edit) return

				// var postData = Object.assign({}, obj)
				// delete postData.questionDetail
        if (obj.after_plan_status === 'REASON') {
          this.$message.error('说服成功的退货方案不允许删除');
          return;
        }
				this.$root.eventHandle.$emit('openDialog', {
					txt: '是否删除该方案？',
					okTxt: '确认',
					noTxt: '取消',
					noShowNoTxt: true,
					ok: () => {
						this.ajax.postStream('/afterSale-web/api/aftersale/order/plan/delete', obj, res => {
							if(res.body.result){
								this.$message.success(res.body.msg)
								this.$emit('updateAfterUpload')
							}else {
								this.$message.error(res.body.msg)
							}
						})
					},
				})
			},
			// 订单店铺选择
			selectShop (e, scope){
				this.$root.eventHandle.$emit('alert',{
					title: '店铺列表',
					style:'width:800px;height:600px',
					component:()=>import('@components/shop/list'),
					params: {
						selection: 'radio',
						callback: d => {
							scope.row.shop_id = d.shop_id
							e.target.nextElementSibling.value = scope.row.shop_name = d.shop_name
							this.copyQuestionsContent(scope.$index)
						}
					},
				})
			},
			// 业务员、数量、标准售价、订单店铺等是否可继续编辑判断
			_checkiFCanEdit (row, key){
				if(this.params.merge_trade_id && !row[key] && !row.id){//标记下有源商品问题但个别字段没数据时，未保存前都能修改
					row['_' + key] = true
				}

				// 无源商品问题，没有方案时业务员、数量、标准售价、订单店铺都能编辑
				return (row.id && row[key] && (row.listAftersaleOrderPlan || []).length)

				|| (this.params.merge_trade_id && row[key]/* !== undefined*/ && !row['_' + key]/* && row.if_goods_question === 'Y'*/)

				//无源商品问题，有方案后标准售价不能再编辑
				|| (!this.params.merge_trade_id && key === 'stand_price' && !row[key] && (row.listAftersaleOrderPlan || []).length)
			},
			copyQuestionsContent2(rowIndex){
				if(this.tableSelect2Delete.length !== 1){//记录当前操作是第几行
					// this.copyQuestionsIndex = rowIndex
					this.$message.error('请选择一行数据');
					return;
				}else {

					var copyTargetObj = JSON.parse(JSON.stringify(this.tableSelect2Delete[0])),
						copyContent = []
					let _oldQuestions = copyTargetObj._oldQuestions?copyTargetObj._oldQuestions:[]
					_oldQuestions.concat(copyTargetObj._questions_des).forEach(item=>{
						if(item.question_detail_id||item.question_description){
							copyContent.push({id:null, des: item.des, question_detail_id: item.question_detail_id,question_description:item.question_description})
						}

					})
					console.log(copyContent,'copyContent');

					if(copyContent.length){
						// copyContent.push({id:null, des:'', question_detail_id: null,question_description:''});
						this.listAfterMaterialVO.forEach((obj, index) => {

							if(!obj._oldQuestions){
								let hasNewQuestion = obj._questions_des.some(item=>{
									return !!item.question_detail_id
								})
								if(!hasNewQuestion){
									// if(copyContent.length>=obj._questions_des.length){

										copyContent.forEach((item,copyIndex)=>{
											//当要复制的内容小于等于目标，直接赋值，若大于则新建一个对象，再进行赋值
											if(copyContent.length<=obj._questions_des.length){
												if(obj._questions_des[copyIndex].question_description){
													if(!obj._questions_des[copyIndex].question_detail_id){
														obj._questions_des[copyIndex].question_description = item.question_description;
													}else{
														obj._questions_des[copyIndex].question_detail_id = item.question_detail_id;
														obj._questions_des[copyIndex].des = item.des;
													}

												}else{
													obj._questions_des[copyIndex].question_detail_id = item.question_detail_id;
													obj._questions_des[copyIndex].des = item.des
													obj._questions_des[copyIndex].question_description = item.question_description;
												}
											}else{
												if(copyIndex<=obj._questions_des.length-1){
													obj._questions_des[copyIndex].question_detail_id = item.question_detail_id;
													obj._questions_des[copyIndex].des = item.des
													obj._questions_des[copyIndex].question_description = item.question_description;
												}else{
													let data ={
														question_detail_id:item.question_detail_id,
														des:item.des,
														question_description:item.question_description,
													}
													obj._questions_des[copyIndex] = data
												}

											}

										})

									// }
								}

							}else{
								let hasQuestion = obj._oldQuestions.some(item=>{
									return !!item.question_detail_id
								})
								if(!hasQuestion){
									// 当要批量填充的内容数量比已保存但是没有问题的行多时，将copyContent的内容赋值到_oldQuestions里，剩下的放在_questions_des里
									if(copyContent.length>=obj._oldQuestions.length){
										copyContent.forEach((item,copyIndex)=>{
											if(copyIndex<obj._oldQuestions.length){
												obj._oldQuestions[copyIndex].question_detail_id = item.question_detail_id
												obj._oldQuestions[copyIndex].des = item.des
											}else{
												if(obj._questions_des[copyIndex-obj._oldQuestions.length].question_description){
													obj._questions_des[copyIndex-obj._oldQuestions.length].question_detail_id = item.question_detail_id
													obj._questions_des[copyIndex-obj._oldQuestions.length].des = item.des
												}else{
													obj._questions_des.unshift({
														question_detail_id:item.question_detail_id,
														des:item.des,
														id:null,
														question_description:item.question_description,
													})
												}

											}
										})
										//存在_questions_des长度大于1情况，说明批量填充进来了，要改变数组顺序按回原来的顺序
										if(obj._questions_des.length>1){
											obj._questions_des.splice(obj._questions_des.length-1,1)
											obj._questions_des.reverse();
											obj._questions_des.push({id:null, des:'', question_detail_id: null,question_description:'',first_category: undefined,second_category: undefined,third_category: undefined});
										}
									}

								}
							}
						})
						// if(isUsedCopy){
						// 	this.$message.success('以第' + index + '行商品问题为目标填充成功')
						// }
					}
				}
			},
			// 批量填充非商品问题 or 无源商品问题(订单店铺、问题描述)
			copyQuestionsContent (rowIndex){
				if(rowIndex !== undefined){//记录当前操作是第几行
					this.copyQuestionsIndex = rowIndex
				}else {
					var copyTargetObj = this.listAfterMaterialVO[this.copyQuestionsIndex]
					,	copyContent = copyTargetObj._questions_des[copyTargetObj._questions_des.length - 2]
					,	isUsedCopy

					if(copyTargetObj.shop_name || copyContent){
						this.listAfterMaterialVO.forEach((obj, index) => {
							if(index > this.copyQuestionsIndex){
								isUsedCopy = true
								if(!(obj.if_goods_question === 'Y' && this.params.merge_trade_id)){//排除有源商品问题，无须填充订单店铺
									obj.shop_name = copyTargetObj.shop_name
									obj.shop_id = copyTargetObj.shop_id
								}
								obj._questions_des.splice(obj._questions_des.length - 1, 0, copyContent)
							}
						})
						if(isUsedCopy){
							this.$message.success('以第' + (this.copyQuestionsIndex + 1) + '行商品问题为目标填充成功')
						}
					}
				}
			},
            toQualityFeedbackDetail(id){
                this.$root.eventHandle.$emit("creatTab", {
                    name: "品质反馈单详情",
                    params: {
                        quality_feedback_id:id
                    },
                    component: () =>
                        import("@components/after_sales/qualityFeedbackDetail.vue"),
                });
            },
			/**
			*初使化数据
			**/
			initData(if_dealer){
				//let user = this.getUserInfo();
				//let isDealer = this.params.form.if_dealer=='Y'?true:false;
				let personBusinessInfo = this.personBusinessAttribute;
				let isDealer = if_dealer=='Y'?true:false;
				//this.onlySeeSolution = !personBusinessInfo.attributeValue && isDealer?true:false;
			}
		},
		mounted (){
			/**
			*初使化数据
			**/
			//this.initData();
			/*
			element-ui el-table的方法：对 Table 进行重新布局。当 Table 或其祖先元素由隐藏切换为显示时，可能需要调用此方法
			当处理进度页签新增并保存一行时，再点击售后单保存按钮会导致问题商品信息页签变异，所以先去掉此方法
			 */
      this.getTreeData()
      // this.getFlagAdd()
			this.$refs.$table.doLayout = f => f
		},
		watch: {
			'params.merge_trade_id': function (val){
				// this.merge_trade_id = val.merge_trade_id
				// this.after_order_id = val.after_order_id
				this.listAfterMaterialVO = [];

				console.log('merge_trade_id',this.listAfterMaterialVO);
			},
			'params.staff_name': function (val){
				if(!this.params.merge_trade_id) {
					this.listAfterMaterialVO.forEach(obj => {
						this.$set(obj, 'real_name', this.params.staff_name)
						this.$set(obj, 'group_name', this.params.staff_group_name)
					})

				}
				console.log('staff_name',this.listAfterMaterialVO);
			},
			goodsDataListVO: function (val){
				console.log('看看有没有监听到这里');
				// if(!val.length) return;
				this.translate();
				this.copyQuestionsIndex = ''
			},
			'params.form.if_dealer':function(val){
				this.initData(val);
			}
		},
		destroyed (){
			this.$root.eventHandle.$off('removeTab', this._listenRemoveTabEvent)
	    },
	}

</script>

<style module>
.row-height :global(.cell) {
	height: auto!important;
}
.add-icon {
	font-weight: bold;
	cursor: pointer;
	color: red;
}
.input100 :global(.cell) > p {
	display: -webkit-flex; /* Safari */
	display: flex;
}
.input100 :global(.cell) > p span {
	width: 42px;
}
.input100 :global(.cell) > p :global(.el-textarea) {
	flex-grow: 1;
	margin-bottom: 5px;
}

.input100 :global(.cell) > p :global(.el-textarea) > textarea {
	height: auto;
}
.plan-btn {
	display: block;
}
.plan-btn :global(.el-icon-close) {
	margin-left: 11px;
    opacity: 0;
}
.plan-btn :global(.el-icon-close):hover {
	color: red;
}
.plan-btn:hover :global(.el-icon-close) {
	opacity: 1;
}
.readonlyDisabled > textarea, .readonlyDisabled > input {
    background-color: #eef1f6;
    border-color: #d1dbe5;
    color: #bbb;
    cursor: not-allowed;
}
</style>
