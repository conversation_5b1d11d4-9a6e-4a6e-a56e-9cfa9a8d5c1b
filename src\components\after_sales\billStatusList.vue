<!-- 下游单据进度 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-flex__bottom">
			<el-table border :data='list' tooltip-effect="dark" width='100%' style="width: 100%;" >
			    <!-- <el-table-column type="index" width="50" label="序号"></el-table-column> -->
				<el-table-column label="单据类型" prop="bill_type" show-overflow-tooltip></el-table-column>
				<el-table-column label="单据编号" prop="bill_returns_no" show-overflow-tooltip>
					<template slot-scope="scope">
						<a href="javascript:;" @click="openBill(scope.row.bill_id, scope.row.bill_type, scope.row.bill_returns_no)">{{ scope.row.bill_returns_no }}</a>
					</template>
				</el-table-column>
				<el-table-column label="单据状态" prop="status" show-overflow-tooltip></el-table-column>
				<el-table-column label="业务状态" prop="business_status" show-overflow-tooltip></el-table-column>
				<el-table-column label="审核人" prop="auditor_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="审核时间" prop="audit_time" show-overflow-tooltip>
					<template slot-scope="scope"><span>{{scope.row.audit_time | dataFormat1}}</span></template>
				</el-table-column>
				<el-table-column label="创建时间" prop="create_time" show-overflow-tooltip>
					<template slot-scope="scope"><span>{{scope.row.create_time | dataFormat1}}</span></template>
				</el-table-column>
				<el-table-column label="关闭状态" prop="close_status" show-overflow-tooltip></el-table-column>
				<el-table-column label="满意度" prop="satisfy" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.bill_type==='服务单'">
              <el-button type="primary" size="mini" @click="handleValidateSatisfy(scope.row.bill_id,'Y')" :loading="scope.row.satisfyLoading">满意</el-button>
              <el-button type="primary" size="mini" @click="handleValidateSatisfy(scope.row.bill_id,'N')" :loading="scope.row.unSatisfyLoading">不满意</el-button>
            </div>
					</template>
        </el-table-column>
			</el-table>
		</el-row>
	</div>
</template>
<script>
	export default {
		props: ['params'],
		data() {
			return {
				list: []
			}
		},
		methods: {
			init (orderId){
				if(orderId){
					this.ajax.postStream('/afterSale-web/api/aftersale/order/bill/statuslist', { id: orderId }, res => {
						this.list = res.body.content.list.map(item=>{
              return{
                ...item,
                satisfyLoading:false,
                unSatisfyLoading:false,
              }
            })
					})
				}else {
					this.list = []
				}
			},
			openBill (id, type, billNo){
				var componentUrl = {
					THBG: 'after_invoices/returnexchangedetail_2',//退换货跟踪单
					THGZ: 'after_invoices/returnexchangedetail_2',//退换货跟踪单(历史)
					FWD: 'after_sales_service/detail.vue',//服务单
					JF: 'after_sales_service/detail.vue',//服务单(历史)
					BJSQ: 'after_sales_supply/supplyOrder_2.vue',//补件申请单
					BJ: 'after_sales_supply/supplyOrder_2.vue',//补件申请单(历史)
					TKD: 'after_sales_refund/refundOrder_2',//退款单
					TK: 'after_sales_refund/refundOrder_2',//退款单(历史)
					TKSQ: 'after_sales_refund/refundRequest_3',//退款申请单
          JHSQD: 'after_sales_refund/interceptGoodsRequestDetail',// 截货申请单详情
          SHZP:'after-sales-gift/after-sales-gift-detail'
				}[billNo.replace(/\d/g, '')]

				this.$root.eventHandle.$emit('creatTab', {
					name: type + '详情',
					params: { id, request_id: id },
					component: () => import('@components/' + componentUrl)
		        })
			},

      handleSatisfy(params){
        const postUrl = "/afterSale-web/api/aftersale/service/saveSatisfaction"
        return new Promise((resolve,reject)=>{
          this.ajax.postStream(postUrl, params, res => {
            if(res.data.result){
              resolve()
            }else{
              this.$message.error(res.body.msg||'操作失败，请稍候再试')
              reject(res.body.msg)
            }
          })
        })
      },
      openUnSatisfyDialog(id){
        let self = this;
        const index = this.list.findIndex(item => item.bill_id === id)
        this.$root.eventHandle.$emit('alert', {
                component:()=>import('@components/after_sales_service/popUnSatisfy'),
                style:'width:800px;height:400px',
                title: '不满意评价',
                params: {
                    async callback (data) {
                       try{
                        self.list[index].unSatisfyLoading=true;
                        await self.handleSatisfy({
                          service_satisfaction: 'DISSATISFIED',
                          dissatisfied_tag: data.dissatisfied_tag,
                          dissatisfied_remark: data.dissatisfied_remark,
                          id,
                        })
                        self.$message({
                          type: 'success',
                          message: "操作成功"
                        });
                       }finally{
                        self.list[index].unSatisfyLoading=false;
                       }
                    }
                }
            })
      },
       handleValidateSatisfy(id,type){
        const index = this.list.findIndex(item => item.bill_id === id)
        let postUrl = "/afterSale-web/api/aftersale/service/authSaveSatisfactionForWeb"
        // 校验权限
        this.ajax.postStream(postUrl, {
          id
        }, async res => {
          if(res.body.result){
            if(type==='Y'){
             try{
              this.list[index].satisfyLoading=true;
              await this.handleSatisfy({
                id,
                service_satisfaction: 'SATISFIED'
              })
              this.$message({
                type: 'success',
                message: "操作成功"
              });
             }finally{
              this.list[index].satisfyLoading=false;
             }
            }else{
              this.openUnSatisfyDialog(id);
            }
          }else{
            this.$message({
              type: 'error',
              message: res.body.msg
            });
          }
        }, err => {
            this.$message({
              type: 'error',
              message: err.body.msg
            });
          })
      },
		},
	}
</script>
