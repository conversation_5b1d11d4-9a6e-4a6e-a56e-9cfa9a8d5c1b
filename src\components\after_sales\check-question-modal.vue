
<template>
  <div class="xpt-flex">
    <div style="margin-top: 8px;overflow: auto;">
      <div><span class="fw">问题分类：</span>{{dataSource.firstCategory}}-{{dataSource.secondCategory}}-{{dataSource.thirdCategory}}</div>
      <div class="mt-4 flex"><span class="fw">发生原因：</span><div style="flex: 1;white-space: pre-wrap;">{{dataSource.happenReason}}</div></div>
      <div class="mt-4 flex"><span class="fw">处理思路&步骤</span>：<div style="flex: 1;white-space: pre-wrap;" v-text="dataSource.processIdeasSteps ||'-'"></div></div>
      <div class="mt-4"><span class="fw">优选方案：</span>{{dataSource.preferredSolution}}</div>
      <div class="mt-4"><span class="fw">次选方案：</span>{{dataSource.secondChoiceScheme}}</div>
    </div>
  </div>
</template>
<script lang="ts">

export default {
  name: "check-question-modal",
  props: ['params'],
  data(){
    return {
      dataSource: {}
    }
  },
  methods:{
    init(){
      this.ajax.get(`/afterSale-web/api/aftersale/knowledge/get/${this.params.id}`,(res)=>{
        if(!res.body.result){
          this.$message.error(res.body.msg)
          return
        }
        this.dataSource = res.body.content || {}
      })
    },
  },
  mounted() {
    this.init()
  }
}
</script>



<style scoped>
.mt-4{
  margin-top: 8px;
  line-height: 18px;
}
.fw{
  font-weight: 700;
  font-size: 13px;
}
.flex{
  display: flex;
}
</style>
