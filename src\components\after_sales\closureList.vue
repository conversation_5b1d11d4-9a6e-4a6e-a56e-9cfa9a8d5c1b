<!-- 售后单强制完结申请列表 -->
<template>
  <div class="xpt-flex">
    <el-row :gutter="10" class="xpt-top">
      <el-form
        ref="search"
        :model="search"
        label-position="right"
        label-width="120px"
      >
        <el-col :span="6">
          <el-form-item label="提交申请开始日期：">
            <el-date-picker
              v-model="search.startCreateTime"
              type="datetime"
              placeholder="选择日期"
              size="mini"
              :editable="false"
              :picker-options='beginDateOptions'
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="提交申请结束日期：">
            <el-date-picker
              v-model="search.endCreateTime"
              type="datetime"
              placeholder="选择日期"
              size="mini"
              :editable="false"
              :picker-options='endDateOptions'
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="售后单号：">
            <xpt-input v-model="search.orderNo" size="mini"></xpt-input>
          </el-form-item>
          <el-form-item label="提交申请人：">
            <xpt-input
              v-model="search.submit_person_name"
              size="mini"
            ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单据状态：">
            <el-select
              v-model="search.orderStatus"
              size="mini"
              placeholder="请选择"
            >
              <el-option key="SUBMIT" label="提交" value="SUBMIT"></el-option>
              <el-option key="AUDIT" label="审核" value="AUDIT"></el-option>
              <el-option key="REJECT" label="驳回" value="REJECT"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="提交申请人分组：">
            <xpt-input
              v-model="search.submit_person_group_name"
              size="mini"
            ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <div style="text-align: left">
            <el-button
              type="success"
              size="mini"
              @click="preQueryData"
              :loading="isSearching"
              >查询</el-button
            >
            <el-button type="success" size="mini" @click="reset"
              >重置</el-button
            >
          </div>
        </el-col>
        <el-col :span="24">
          <div style="padding: 10px 120px">
            <el-button
              type="primary"
              size="mini"
              @click="doLock('LOCK')"
              :loading="isLocking"
              >锁定</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="doLock('UNLOCK')"
              :loading="isUnLocking"
              >解锁</el-button
            >
            <el-button
              type="warning"
              size="mini"
              @click="audit"
              :loading="isAuditing"
              >审核</el-button
            >
            <el-button
              type="danger"
              size="mini"
              @click="reject"
              :loading="isRejecting"
              >驳回</el-button
            >
          </div>
        </el-col>
      </el-form>
    </el-row>
    <count-list
      class='xpt-flex__bottom'
      :showHead="false"
      :data="list"
      :colData="cols"
      :pageTotal="count"
      :pageLength="search.page_size"
      @selection-change="selectChange"
      selection="checkbox"
      countUrl="/afterSale-web/api/forced/closure/query/count"
      :showCount="showCount"
      @page-size-change="pageSizeChange"
      @current-page-change="currentPageChange"
    ></count-list>
  </div>
</template>
<script>
import countList from "@components/common/list-count";
export default {
  props: ["params"],
  components: {
    countList,
  },
  data() {
    let self = this;
    return {
      list: [],
      count: 0,
      showCount: false,
      select: [],
      search: {
        // 页码
        page_no: 1,
        // 页数
        page_size: 50,
        startCreateTime: null,
        endCreateTime: null,
        orderNo: null,
        submit_person_name: null,
        orderStatus: null,
        submit_person_group_name: null,
      },
      cols: [
        {
          label: "售后单号",
          prop: "order_no",
          width: "120",
          redirectClick(row) {
            self.viewDetail(row.order_id);
          },
        },
        {
          label: "提交申请人",
          width: "100",
          prop: "submit_person_name",
        },
        {
          label: "提交申请人分组",
          width: "100",
          prop: "submit_person_group_name",
        },
        {
          label: "提交申请时间",
          width: "90",
          prop: "create_time",
          format: "dataFormat",
        },
        {
          label: "强制完结原因",
          width: "150",
          prop: "force_reason",
        },
        {
          label: "状态",
          width: "90",
          prop: "order_status",
          formatter: (prop) =>
            ({
              SUBMIT: "提交",
              AUDIT: "审核",
              REJECT: "驳回",
            }[prop] || prop),
        },
        {
          label: "锁定人",
          width: "100",
          prop: "locker_name",
        },
        // {
        //   label: "锁定时间",
        //   width: "90",
        //   format: "dataFormat",
        //   prop: "lock_time",
        // },
        {
          label: "驳回人",
          width: "100",
          prop: "reject_person_name",
        },
        {
          label: "驳回时间",
          width: "90",
          format: "dataFormat",
          prop: "reject_time",
        },
        {
          label: "驳回原因",
          width: "150",
          prop: "reject_reason",
        },
        {
          label: "审核人",
          width: "100",
          prop: "audit_person_name",
        },
        {
          label: "审核时间",
          width: "90",
          prop: "audit_time",
          format: "dataFormat",
        },
        {
          label: "合并单号",
          width: "180",
          prop: "merge_trade_no",
        },
        {
          label: "业务员",
          width: "100",
          prop: "staff_name",
        },
        {
          label: "业务员分组",
          width: "100",
          prop: "staff_group_name",
        },
        {
          label: "提交售后人",
          width: "100",
          prop: "submit_staff_name",
        },
        {
          label: "提交售后时间",
          width: "90",
          format: "dataFormat",
          prop: "submit_time",
        },
        {
          label: "售后单创建时间",
          width: "100",
          format: "dataFormat",
          prop: "aftersale_create_time",
        },
        {
          label: "售后单据状态",
          width: "110",
          prop: "status",
          formatter: (prop) => {
            return (
              {
                CREATE: "待办",
                PRE_HANDLE: "售前处理",
                WAIT_HANDLE: "售后待办",
                AFTER_HANDLE: "售后处理",
                FINISH: "完结",
                CANCELLATION: "作废",
              }[prop] || prop
            );
          },
        },
        {
          label: "是否三个月售后",
          width: "110",
          prop: "if_three_age",
          formatter: (prop) => {
            return (
              {
                1: "是",
                0: "否",
              }[prop] || prop
            );
          },
        },
        {
          label: "售后锁定人",
          width: "100",
          prop: "aftersale_locker_name",
        },
        {
          label: "售后锁定人分组",
          width: "100",
          prop: "locker_group_name",
        },
        {
          label: "是否经销订单",
          width: "110",
          prop: "if_dealer",
          formatter: (prop) => {
            return (
              {
                Y: "是",
                N: "否",
              }[prop] || prop
            );
          },
        },
      ],
      beginDateOptions: {
        date:(function(){
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            let time = year + "-" + month + "-" + day + " " + "00:00:00";
            return new Date(time);
        })(),
        disabledDate(time) {
          if (self.search.endCreateTime) {
            return (
              time.getTime() > new Date(self.search.endCreateTime).getTime()
            );
          }
        },
      },
      endDateOptions: {
        date:(function(){
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            let time = year + "-" + month + "-" + day + " " + "00:00:00";
            return new Date(time);
        })(),
        disabledDate(time) {
          if (self.search.startCreateTime) {
            return (
              time.getTime() < new Date(self.search.startCreateTime).getTime()
            );
          }
        },
      },
      isSearching: false,
      isLocking: false,
      isUnLocking: false,
      isAuditing: false,
      isRejecting: false,
    };
  },
  methods: {
    pageSizeChange(ps) {
      this.search.page_size = ps;
      this.queryData();
    },
    currentPageChange(page) {
      this.search.page_no = page;
      this.queryData();
    },
    viewDetail(id) {
      if (!id) {
        this.$message.warning("非法的售后单号！");
        return;
      }
      let params = {
        id,
        idList: Array.from(
          new Set(/*去重*/ this.list.map((obj) => obj.order_id))
        ), //用于售后单详情的上一页/下一页
        code: "AFTERSALE_ORDER_LIST", //查看售后单类型
      };

      if (document.querySelector('[data-symbol="after_order_id_' + id + '"]')) {
        this.$message.warning("该售后单已经打开");
      } else {
        this.$root.eventHandle.$emit("creatTab", {
          name: "售后单详情",
          params: params,
          component: () => import("@components/after_sales/index"),
        });
      }
    },
    selectChange(list) {
      this.select = list;
    },
    audit() {
      if (this.select.length === 0) {
        this.$message.warning("请选择需要操作的单号");
        return;
      }
      if (this.select.some((item) => item.order_status !== "SUBMIT")) {
        this.$message.warning("请选择提交状态的申请单，进行审核");
        return;
      }
      let ids = this.select.map((item) => item.id);
      let params = { ids: ids };
      this.isAuditing = true;
      this.ajax.postStream(
        "/afterSale-web/api/forced/closure/action/audit",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            this.preQueryData();
          } else {
            this.$message.error(res.body.msg || "审核失败");
          }
          this.isAuditing = false;
        },
        (err) => {
          this.$message.error(err);
          this.isAuditing = false;
        }
      );
    },
    doLock(type) {
      if (this.select.length === 0) {
        this.$message.warning("请选择需要操作的单号");
        return;
      }
      let ids = this.select.map((item) => item.id);
      let params = { ids: ids, type: type };
      let loadingObj = {
        LOCK: "isLocking",
        UNLOCK: "isUnLocking",
      };
      let typeLabel = {
        LOCK: "锁定",
        UNLOCK: "解锁",
      };
      let loadingStr = loadingObj[type];
      this[loadingStr] = true;
      this.ajax.postStream(
        "/afterSale-web/api/forced/closure/action/doLock",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            this.preQueryData();
          } else {
            this.$message.error(res.body.msg || `${typeLabel[type]}失败`);
          }
          this[loadingStr] = false;
        },
        (err) => {
          this.$message.error(err);
          this[loadingStr] = false;
        }
      );
    },
    reject() {
      let self = this;
      if (this.select.length === 0) {
        this.$message.warning("请选择需要操作的单号");
        return;
      }
      if (this.select.some((item) => item.order_status !== "SUBMIT")) {
        this.$message.warning("请选择提交状态的申请单，进行驳回");
        return;
      }
      let ids = this.select.map((item) => item.id);

      let params = {
        itemtext: "驳回原因",
        callback(res) {
          if (res) {
            self.isRejecting = true;
            self.ajax.postStream(
              "/afterSale-web/api/forced/closure/action/reject",
              {
                ids: ids,
                optionReason: res,
              },
              (res) => {
                if (res.body.result) {
                  self.$message.success(res.body.msg);
                  self.isRejecting = false;
                  self.preQueryData();
                } else {
                  self.$message.error(res.body.msg || "驳回失败");
                  self.isRejecting = false;
                }
              },
              (err) => {
                self.$message.error(err);
                self.isRejecting = false;
              }
            );
          }
        },
      };
      self.$root.eventHandle.$emit("alert", {
        params: params,
        component: () =>
          import("@components/after_sales/alert/forceReason.vue"),
        style: "width:500px;height:350px",
        title: "驳回原因",
      });
    },
    preQueryData(){
      this.search.page_no=1;
      this.search.page_size=50;
      this.count=0;
      this.showCount=false;
      this.queryData();
    },
    queryData() {
      this.$refs.search.validate((valid) => {
        if (valid) {
          let data = {
            ...this.search,
            startCreateTime:new Date(this.search.startCreateTime).getTime(),
            endCreateTime:new Date(this.search.endCreateTime).getTime(),
          }
          this.isSearching = true;
          this.ajax.postStream(
            "/afterSale-web/api/forced/closure/query/list",
            data,
            (res) => {
              if (res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                // this.count = content.count || 0;
              } else {
                this.$message.error(res.body.msg);
              }
              this.isSearching = false;
            },
            (err) => {
              this.$message.error(err);
              this.isSearching = false;
            }
          );
        }
      });
    },
    reset() {
      this.search = {
        // 页码
        page_no: 1,
        // 页数
        page_size: 50,
        startCreateTime: null,
        endCreateTime: null,
        orderNo: null,
        submit_person_name: null,
        orderStatus: null,
      };
    },
  },

  mounted() {},
};
</script>
<style type="text/css" scoped>
.el-input {
  width: 150px;
}
</style>
