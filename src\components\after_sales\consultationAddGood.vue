<!-- 咨询单新增商品 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='cols' 
		:searchPage='search.page_name' 
		:pageTotal='count' 
		selection="checkbox" 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@search-click='searchClick' 
		@selection-change='selectionChange'
		@row-dblclick='rowDblclick'
		ref="xptList"
	></xpt-list>	
</template>
<script>
export default {
    props:['params'],
	data() {
		return {
			list: [],
			btns: [{
				type: 'primary',
				txt: '确认',
				click: this.close
			}],
			cols: [{
				label: '买家昵称',
				prop: 'buyer_name',
			}, {
				label: '物料编码',
				prop: 'material_number',
				width: 180
			}, {
				label: '物料名称',
				prop: 'material_name',
			}, {
				label: '物料规格描述',
				prop: 'material_specification',
			}, {
				label: '数量',
				prop: 'count',
			}, {
				label: '单位',
				prop: 'unit',
			}, {
				label: '批号',
				prop: 'lot_number',
				width: 100
			}, {
				label: '体积',
				prop: 'volume'
			}, {
				label: '销售单号',
				prop: 'sys_trade_no',
				width: 180
			}, {
				label: '批次子单号',
				prop: 'outbound_notice_no',
				width: 180
			}, {
				label: '中转承运商',
				prop: 'zd_change_trains_logistics_company',
				width: 180
			}, {
				label: '发货仓库',
				prop: 'outbound_warehouse_name'
			}],
			search: {
				page_no:1,
				page_size: this.pageSize,
				page_name: 'scm_merge_materiel_consultion',
				where: [],
				batch_trade_id: this.params.batch_trade_id,
			},
			count: 0,
			selectData: []
		}
	},
	methods: {
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			this.search.where = obj;
			this.getList(resolve);
		},
		selectionChange(obj) {
			this.selectData = obj;
		},
		rowDblclick(obj) {
			this.close();
		},
		getList(resolve) {
			this.ajax.postStream("/afterSale-web/api/aftersale/consultion/batchGoodsList", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve()
			});
		},
		close() {
			if(!this.selectData || this.selectData.length === 0) {
				this.$message.error('请选择单据');
				return;
			}
			this.params.callback(this.selectData);
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
		},
	},
	mounted() {
		this.getList()
	}
}
</script>
