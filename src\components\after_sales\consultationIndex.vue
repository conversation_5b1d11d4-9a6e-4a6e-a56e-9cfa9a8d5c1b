<!--责任分析单列表-->
<template>
  <el-tabs v-model="selectTab" @tab-click="selectTabClick">
      <el-tab-pane label='正向咨询' name='FORWARD_COUNSELING'>
        <consultationList class="list_2_table_class" v-if="selectTab==='FORWARD_COUNSELING'"></consultationList>
      </el-tab-pane>
      <el-tab-pane label='逆向咨询' name='REVERSE_CONSULTATION'>
        <reverseConsultationList class="list_2_table_class"  v-if="selectTab==='REVERSE_CONSULTATION'"></reverseConsultationList>
      </el-tab-pane>
  </el-tabs>
</template>

<script>
import consultationList from './consultationList.vue';
import reverseConsultationList from './reverseConsultationList/index.vue';
export default {
  components:{
    consultationList,
    reverseConsultationList,
  },
props:['params'],
data (){
  return {
    selectTab:'FORWARD_COUNSELING',
  }
},
methods: {
  selectTabClick(e){
    this.selectTab = e.name
  }
},
mounted (){},
}
</script>
<style lang="stylus" >
  .list_2_table_class .body{
      height: calc(100vh - 162px) !important;
  }
</style>
