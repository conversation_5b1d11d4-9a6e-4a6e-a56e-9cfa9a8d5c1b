<!-- 咨询单列表 -->
<template>
	<count-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:searchPage='search.page_name'
		:pageTotal='count'
		selection="checkbox"
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
		@search-click='searchClick'
		@selection-change='selectionChange'
		:dynamic='true'
		:taggelClassName='taggelClassName'
		countUrl="/afterSale-web/api/aftersale/consultion/count"
    	:showCount ='showCount'
		ref="xptList"
	>
          <el-button
              v-if="isShowSatisfy"
							slot='btns'
							@click="setmany('Y')"
							size='mini'
							type='primary'
							:disabled="false"
						>满意</el-button>
            <el-button
            v-if="isShowDisSatisfy"
							slot='btns'
							@click="setmany('N')"
							size='mini'
							type='primary'
							:disabled="false"
						>不满意</el-button>
  </count-list>
</template>
<script>
import countList from '@components/common/list-count'
export default {
    props:['params'],
	components: {
        countList,
      },
	data() {
		return {
			showCount:false,
			isShowSatisfy:false,
			isShowDisSatisfy:false,
			list: [],
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: () => this.getList(),
			}, {
				type: 'primary',
				txt: '新增',
				click: () => this.openConsultation(),
			}],
			cols: [{
				label: '单据编号',
				prop: 'consultion_no',
				redirectClick: d => this.openConsultation(d.consultion_id),
				width: 180
			}, {
				label: '批次单号',
				prop: 'batch_trade_no',
				width: 180
			}, {
				label: '买家昵称',
				prop: 'buyer_name',
			}, {
				label: '单据状态',
				prop: 'document_status',
				formatter: prop => ({
					CREATE: '创建',
					CONSULTING: '咨询中',
					CLOSE: '关闭',
					REPLIED: '已回复',
				}[prop] || prop),
			}, {
				label: '咨询内容',
				prop: 'newContent',
			}, {
				label: '咨询回复内容',
				prop: 'newReplyContent',
			}, {
				label: '创建人',
				prop: 'creator_name',
				width: 100
			}, {
				label: '创建人分组',
				prop: 'creator_group_name',
				width: 100
			}, {
				label: '创建时间',
				prop: 'create_time',
				format: 'dataFormat1',
				width: 150,
			}, {
				label: '最新回复时间',
				prop: 'reply_max_date',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '是否介入',
				prop: 'if_involved',
				format: 'yesOrNo',
				width: 100
			},{
				label: '介入是否回复',
				prop: 'if_reple',
				format: 'yesOrNo',
				width: 100
			},{
				label: '满意度',
				prop: 'consultion_satisfaction',
				formatter:prop => ({
					Y: '满意',
					N: '不满意',
				}[prop] || prop),
				width: 100
			},{
				label: '回复状态',
				prop: 'reply_status',
				formatter:prop => ({
					NOREPLAY : '未回复',
					FIREPLAY: '首次回复',
					SECREPLAY: '二次回复',
				}[prop] || prop),
				width: 100
			},{
				label: '咨询类型',
				prop: 'type_of_consultion',
				width: 150,
        formatter: (val,index,row)=> {
          const {type_of_parent_consultion, type_of_consultion} = row;
          if(type_of_parent_consultion) {
            return this.auxFormat('consultionZXLX',type_of_parent_consultion) + (type_of_consultion ? `/${this.auxFormat('consultionZXLX2',type_of_consultion)}` : '')
          } else {
            return this.auxFormat('consultionZXLX',type_of_consultion)
          }
        },
      },
        {
            label: '单据来源',
            width: 80,
            prop: 'source',
            format: 'auxFormat',
            formatParams: 'AFTERSALE_SOURCE_TYPE'
			}],
			search: {
				page_no:1,
				page_size: this.pageSize,
				page_name: 'aftersale_bill_consultion',
				where: []
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
		taggelClassName (row){
			if(row.reply_status == 'SECREPLAY') return this.$style['second-read-tag'];
			if(row.if_answer_message_read === '0') return this.$style['unread-tag'];
		},
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			let self = this;
			this.search.where = obj;
			new Promise((res,rej)=>{
					self.getList(resolve,res);
				}).then(()=>{
					if(this.search.page_no != 1){
						self.count = 0;
					}
					self.showCount = false;
				})
			},
		selectionChange(obj) {
			this.selectData = obj;
		},
		getList(resolve,callback) {
			this.ajax.postStream("/afterSale-web/api/aftersale/consultion/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					// this.count = res.body.content.count;
					if(!this.showCount){
						let total = this.search.page_no * this.search.page_size
						this.count = res.body.content.list.length == (this.search.page_size+1)? total+1:total;
              			this.list = res.body.content.list.splice(0,res.body.content.list.length == (this.search.page_size+1)?total:res.body.content.list.length);

					}
				} else {
					this.$message.error(res.body.msg)
				}
				callback && callback();
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				callback && callback();
				resolve && resolve()
			});
		},
		openConsultation (id){
			this.$root.eventHandle.$emit('creatTab',{
				name: id ? '咨询单详情' : '新增咨询单',
				component: ()=>import('@components/after_sales/consultationNew'),
				params: {
          id,
          orderList: JSON.parse(JSON.stringify(this._getOrderList())),
				},
			})
    },
    _getOrderList() {
			let newSetBillNo = [],	newSetOrder = []
			this.list.forEach(obj => {
				if(newSetBillNo.indexOf(obj.consultion_id) === -1){//去掉重复责任分析单号
					newSetBillNo.push(obj.consultion_id)
					newSetOrder.push({
							id: obj.consultion_id
					})
				}
			})
			return newSetOrder
    },
    dissatisfyDialog() {
      let self = this;
      this.$root.eventHandle.$emit("alert", {
        title: "不满意评价",
        style: "width:400px;height:300px",
        component: () =>
          import("@components/after_sales/dissatisfyDialog.vue"),
        params: {
          consultionId: self.selectData.map(item=>item.consultion_id),
          callback: (d) => {
            console.log('不满意评价', d)
            self.getList();
          },
        },
      });
    },
	// 满意或者不满意 批量操作
	setmany(status){
		if(!this.selectData.length){
			return this.$message.error("请先至少选择一条数据！")
		}
    if (status === 'N') {
      this.dissatisfyDialog()
    } else {
      this.ajax.postStream('/afterSale-web/api/aftersale/consultion/manyCommitSatisfactionToZd',
      { consultion_satisfaction : status ,consultion_ids: this.selectData.map(item=>item.consultion_id)},
      res => {
        if(res.body.result){
          this.$message.success(res.body.msg)
          this.getList()
        }else {
          this.$message.error(res.body.msg)
        }
      })
    }
	},
  getSatisfySwitch() {
      return new Promise((resolve, reject) => {
        this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryDataList', { categoryCode: 'CONSULT_SATISFY_SWITCH', isEnable: 1, status: 1 }, res => {
          if (res.body.result) {
            resolve(res.body.content.list)
          } else {
            reject(res.body.msg);
          }
        });
      });
    },
    auxFormat(type,val){
        let _data = __AUX.get(type),
        obj = _data.find(d => d.code === val);
        return obj ? obj.name : val;
      }
  },
	mounted() {
		this.getList()
    this.getSatisfySwitch().then((res)=>{
      this.isShowSatisfy=res.some(item=>item.code==='SATISFIED'&&item.tag==='Y');
      this.isShowDisSatisfy=res.some(item=>item.code==='DISSATISFIED'&&item.tag==='Y');
    })
	},
};
</script>
<style module>
.unread-tag td:nth-child(2):after {
	content: '';
	position: absolute;
    right: 4px;
    top: 9px;
    border: 4px solid red;
    border-radius: 100%;
}
.second-read-tag{
	background: #ff50e9!important;
}
</style>
