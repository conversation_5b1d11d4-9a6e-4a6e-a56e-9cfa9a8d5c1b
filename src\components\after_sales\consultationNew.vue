<!-- 咨询单详情 -->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-col :span="18">
			<el-button
				type="primary"
				size="mini"
				@click="preSave"
				:loading="saveLoading"
				:disabled="
					form.aftersaleBillConsultion.document_status === 'CLOSE'
					|| saveLoading
					|| commitConsultBillToZdLoading
				"
			>保存</el-button>
			<el-button
				type="primary"
				size="mini"
				@click="selectOrder"
				:disabled="
					(form.aftersaleBillConsultion.after_order_id && !form.aftersaleBillConsultion.merge_trade_id)
					|| form.aftersaleBillConsultion.document_status === 'CLOSE'
					|| form.aftersaleBillConsultion.if_send_zd === 'Y'
					|| !!params.isConsultation
					|| commitConsultBillToZdLoading
				"
			>选单</el-button><!-- 无源售后单下推不能选单 -->
			<el-button type="primary" size="mini" @click="getDetail()" :disabled="!form.aftersaleBillConsultion.consultion_id|| commitConsultBillToZdLoading">刷新</el-button>
			<el-button
				type="primary"
				size="mini"
				@click="actionHelper('commitConsultBillToZd')"
				:loading="commitConsultBillToZdLoading"
				:disabled="
					!form.aftersaleBillConsultion.consultion_id
					|| form.aftersaleBillConsultion.if_send_zd === 'Y'
					|| form.aftersaleBillConsultion.document_status === 'CLOSE'
					|| commitConsultBillToZdLoading
				"
			>提交至4PL平台</el-button>
			<el-button
				type="primary"
				size="mini"
				@click="actionHelper('manyCommitConsultBillToZd')"
				:loading="manyCommitConsultBillToZdLoading"
				:disabled="
					!form.aftersaleBillConsultion.consultion_id
					|| form.aftersaleBillConsultion.document_status === 'CLOSE'
					|| form.aftersaleBillConsultion.if_send_zd === 'N'
					|| manyCommitConsultBillToZdLoading
					|| hideBtnByHost
					|| commitConsultBillToZdLoading
				"
			>提交咨询</el-button>
			<el-button-group>
				<el-button
					type="primary"
					@click="uploadAction('post')"
					size="mini"
					:disabled="
						!form.aftersaleBillConsultion.consultion_id
						|| ['CONSULTING','CLOSE','REPLIED'].includes(form.aftersaleBillConsultion.document_status)
						|| getEmployeeInfo('id') !== form.aftersaleBillConsultion.creator
						|| commitConsultBillToZdLoading
					"
				>上传</el-button>
				<el-button
					type="primary"
					@click="uploadAction('get')"
					size="mini"
					:disabled="
						!form.aftersaleBillConsultion.consultion_id
						|| commitConsultBillToZdLoading
					"
				>查看附件</el-button>
			</el-button-group>
			<el-button
				type="primary"
				size="mini"
				@click="addCloudMessages"
				:disabled="
					!form.aftersaleBillConsultion.consultion_id
					|| form.aftersaleBillConsultion.document_status === 'CLOSE'
					|| hideBtnByHost
					|| commitConsultBillToZdLoading
				"
			>补充咨询内容</el-button>
			<el-button
				type="primary"
				size="mini"
				@click="actionHelper('close')"
				:loading="closeLoading"
				:disabled="
					!form.aftersaleBillConsultion.consultion_id
					|| form.aftersaleBillConsultion.document_status === 'CLOSE'
					|| closeLoading
					|| commitConsultBillToZdLoading
				"
			>关闭</el-button>
			<el-button
				type="primary"
				size="mini"
				@click="consultionOpen()"
				:disabled="commitConsultBillToZdLoading"
			>专员介入</el-button>
		</el-col>
		<el-col :span="6" style="text-align: right;">
			<el-button type="primary" size="mini" @click="nextOrPrevOrder('prev')" :disabled="commitConsultBillToZdLoading">上一页</el-button>
			<el-button type="primary" size="mini" @click="nextOrPrevOrder('next')" :disabled="commitConsultBillToZdLoading">下一页</el-button>
		</el-col>
	</el-row>
	<el-form :model="form.aftersaleBillConsultion" :rules="rules" ref='form' label-position="right" label-width="120px">
		<el-tabs v-model="selectTab1">
			<el-tab-pane label='基本信息' name='basicInformation'>
				<el-row :gutter="20">
					<el-col :span="6">
						<el-form-item label="单据编号">
							<el-input size='mini' :value="form.aftersaleBillConsultion.consultion_no" disabled placeholder="系统生成" style="width: 220px;"></el-input>
						</el-form-item>
						<el-form-item label="批次单号">
							<el-input size='mini' :value="form.aftersaleBillConsultion.batch_trade_no" style="width: 220px;" disabled></el-input>
						</el-form-item>
						<el-form-item label="合并单号">
							<el-input size='mini' :value="form.aftersaleBillConsultion.merge_trade_no" style="width: 220px;" disabled></el-input>
						</el-form-item>
						<el-form-item label="买家昵称">
							<el-input size='mini' :value="form.aftersaleBillConsultion.buyer_name" style="width: 220px;" disabled></el-input>
						</el-form-item>
						<el-form-item label="预约安装时间">
							<el-date-picker size='mini' type="datetime" :value="form.aftersaleBillConsultion.booked_install_time" style="width: 220px;" disabled></el-date-picker>
						</el-form-item>
            <el-form-item label="是否经销商订单">
							<el-switch :value="form.if_dealer" on-value="Y" off-value="N" on-text="是" off-text="否"  disabled></el-switch>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="单据状态">
							<el-input size='mini' :value="document_status_options[form.aftersaleBillConsultion.document_status]" disabled></el-input>
						</el-form-item>
						<el-form-item label="创建时间">
							<el-date-picker
								:value="form.aftersaleBillConsultion.create_time"
							    type="datetime"
							    size="mini"
							    disabled
							></el-date-picker>
						</el-form-item>
						<el-form-item label="创建人">
							<el-input size='mini' :value="form.aftersaleBillConsultion.creator_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="创建人分组">
							<el-input size='mini' :value="form.aftersaleBillConsultion.creator_group_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="安装师傅">
							<el-input size='mini' :value="form.aftersaleBillConsultion.installer" disabled></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="咨询对象">
							<el-input size='mini' :value="consultion_object_options[form.aftersaleBillConsultion.consultion_object]" disabled></el-input>
						</el-form-item>
						<!-- <el-form-item label="接口状态">
							<el-input size='mini' value="" disabled></el-input>
						</el-form-item>
						<el-form-item label="接口下达时间">
							<el-date-picker
								value=""
							    type="datetime"
							    size="mini"
							    disabled
							></el-date-picker>
						</el-form-item> -->
						<el-form-item label="批次发货时间">
							<el-date-picker
								:value="form.aftersaleBillConsultion.deliver_date"
							    type="datetime"
							    size="mini"
							    disabled
							></el-date-picker>
						</el-form-item>
						<!-- <el-form-item label="4PL作废时间">
							<el-date-picker
								:value="form.aftersaleBillConsultion.zd_cancel_date"
							    type="datetime"
							    size="mini"
							    disabled
							></el-date-picker>
						</el-form-item> -->
						<el-form-item label="来源单号">
							<el-input size='mini' :value="form.aftersaleBillConsultion.source_bill_no" disabled placeholder="系统生成"></el-input>
						</el-form-item>
						<el-form-item label="来源单类型">
							<el-input size='mini' :value="source_bill_type_options[form.aftersaleBillConsultion.source_bill_type]" disabled></el-input>
						</el-form-item>
						<el-form-item label="预约时段">
							<el-input size='mini' :value="form.aftersaleBillConsultion.appointment_resvs"  disabled></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="咨询类型" prop="type_of_parent_consultion" v-if="form.aftersaleBillConsultion.type_of_parent_consultion || !params.id">
              <xpt-aux-cascader :auxNames="['consultionZXLX','consultionZXLX2']" @change="handleTypeOfConsultionChange"
                :value="[form.aftersaleBillConsultion.type_of_parent_consultion, form.aftersaleBillConsultion.type_of_consultion]"
                :filter="typeOfConsultionFilter"
                :disabled="form.aftersaleBillConsultion.if_send_zd === 'Y'||params.isConsultation ||!!consultionType"
                showRemark
              />
              <el-tooltip v-if='rules.type_of_parent_consultion[0].isShow && !!params.isConsultation' class="item" effect="dark" :content="rules.type_of_parent_consultion[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
            </el-form-item>
            <el-form-item label="咨询类型" prop="type_of_consultion" v-else>
							<xpt-select-aux
								v-model="form.aftersaleBillConsultion.type_of_consultion"
								aux_name='consultionZXLX'
								:disabledOption="[{code:'JFZX0015'},{code:'JFZX0021'},{code:'JFZX0022'}]"
								ref="typeOfConsultionSelect"
								:disabled="form.aftersaleBillConsultion.if_send_zd === 'Y'||params.isConsultation ||!!consultionType"
							></xpt-select-aux>
							<el-tooltip v-if='rules.type_of_consultion[0].isShow && !!params.isConsultation' class="item" effect="dark" :content="rules.type_of_consultion[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
            </el-form-item>
						<el-form-item label="满意度">
							<el-input size='mini' :value="consultionFormat(form.aftersaleBillConsultion.consultion_satisfaction)" disabled></el-input>
						</el-form-item>
						<el-form-item label="">
							<el-button type="primary" @click="setmany('Y')" size="mini" :disabled="commitConsultBillToZdLoading">满意</el-button>
							<el-button type="primary" @click="setmany('N')" size="mini" :disabled="commitConsultBillToZdLoading">不满意</el-button>
						</el-form-item>
            <el-form-item label="不满意标签">
							<xpt-select-aux
								v-model="form.aftersaleBillConsultion.consultion_no_satisfaction_tag"
								aux_name='ZXDPJBMYBQ'
								:disabledOption="[]"
								ref="discontentTagSelect"
								disabled
							></xpt-select-aux>
						</el-form-item>
            <el-form-item label="不满意备注">
							<el-input size='mini' v-model="form.aftersaleBillConsultion.consultion_no_satisfaction_remark" disabled></el-input>
						</el-form-item>
						<el-form-item label="合作商本月评价率">
							<el-input size='mini' v-if="!!form.analysisVO" v-model="form.analysisVO.evaluationPercentage" disabled></el-input>
							<el-input size='mini' v-else  disabled></el-input>
						</el-form-item>
						<el-form-item label="合作商本月满意度">
							<el-input size='mini' v-if="!!form.analysisVO" v-model="form.analysisVO.satisfactionPercentage" disabled></el-input>
							<el-input size='mini' v-else  disabled></el-input>
						</el-form-item>

					</el-col>
				</el-row>
				<el-row :gutter="20" style="max-height: 300px;overflow: auto;padding-top:7px;">
					<el-col :span="24" v-if="!params.isConsultation">
						<el-form-item v-for="(msg,index) in form.cloudMessages" :key="index" :label="msg.if_reply === 'Y' ? '咨询回复：':  msg.if_reply === 'S'?'咨询二次回复：': '咨询内容'" :class="$style['textarea-style'] + (msg.if_reply === 'Y' ? ' Y' : '')">
							<span v-if="msg.msg_date" class="msg-date">{{ msg.msg_date | dataFormat1 }}</span>
							<!-- <span v-else-if="msg.if_commit_finish === 'N'" class="msg-date">(未发送4PL)</span> -->
							<el-input
								type="textarea"
								size='mini'
								v-model="msg.content"
								:autosize="{ minRows: 1 }"
								:disabled="
									msg.if_reply === 'Y'
									|| msg.if_commit_finish === 'Y'
									|| form.aftersaleBillConsultion.document_status === 'CLOSE'
								"
								@blur="e => cloudMessagesChange(e, msg)"
							></el-input>
							<i
								v-if="
									!!form.aftersaleBillConsultion.consultion_id
									&& !(msg.if_reply === 'Y' || msg.if_commit_finish === 'Y')
									&& form.aftersaleBillConsultion.document_status !== 'CLOSE'
								"
								class="el-icon-circle-close"
								@click="delCloudMessages(msg.msg_id)"
							></i>
						</el-form-item>
					</el-col>
				</el-row>
			</el-tab-pane>
			<el-tab-pane label='其他信息' name='otherInformation'>
				<el-row :gutter="20">
					<el-col :span="5">
						<el-form-item label="物流供应商">
							<el-input size='mini' :value="form.aftersaleBillConsultion.logistics_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="三包供应商">
							<el-input size='mini' :value="form.aftersaleBillConsultion.three_supplier_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="线路区域">
							<el-input size='mini' :value="form.aftersaleBillConsultion.area_route" disabled></el-input>
						</el-form-item>
            <el-form-item label="来源类型">
                  <xpt-select-aux
                    v-model="form.aftersaleBillConsultion.source"
                    aux_name='AFTERSALE_SOURCE_TYPE'
                    :disabled="true"
                  ></xpt-select-aux>
                </el-form-item>
					</el-col>
					<el-col :span="5">
						<el-form-item label="提货点">
							<el-input size='mini' :value="form.aftersaleBillConsultion.point_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="三包点">
							<el-input size='mini' :value="form.aftersaleBillConsultion.three_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="提货点电话">
							<!-- <el-input size='mini' :value="form.aftersaleBillConsultion.point_phone" disabled></el-input> -->
							<xpt-eye-switch v-model="form.aftersaleBillConsultion.point_phone" :disabled="true" :aboutNumber="form.aftersaleBillConsultion.consultion_no"></xpt-eye-switch>
						</el-form-item>
						<el-form-item label="三包供应商电话">
							<!-- <el-input size='mini' :value="form.aftersaleBillConsultion.three_supplier_phone" disabled></el-input> -->
							<xpt-eye-switch v-model="form.aftersaleBillConsultion.three_supplier_phone" :disabled="true" :aboutNumber="form.aftersaleBillConsultion.consultion_no"></xpt-eye-switch>
						</el-form-item>
						<el-form-item label="物流单号" class="maxHeight">
							<el-input size='mini' type="textarea" :autosize="{ minRows: 1, maxRows: 3}" :value="form.aftersaleBillConsultion.logistics_no" disabled></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="收货人">
							<el-input size='mini' :value="form.aftersaleBillConsultion.receiver_name" disabled></el-input>
                            <el-button type="primary" size="mini" @click="choiceAddress" :disabled="!(form.aftersaleBillConsultion.buyer_id && form.aftersaleBillConsultion.document_status === 'CREATE')">选择客户地址</el-button>
						</el-form-item>
						<el-form-item label="收货人电话">
							<!-- <el-input size='mini' :value="form.aftersaleBillConsultion.receiver_phone" disabled></el-input> -->
							<xpt-eye-switch v-model="form.aftersaleBillConsultion.receiver_phone" :disabled="true" :aboutNumber="form.aftersaleBillConsultion.consultion_no"></xpt-eye-switch>
						</el-form-item>
						<el-form-item label="省" prop="province">
							<el-select
								v-model="form.aftersaleBillConsultion.province"
								size="mini"
								style="width:150px;"
								@change="changeAddress(form.aftersaleBillConsultion.province,1)"
								:disabled="true"
							>
								<el-option v-for="(value, key) in provinceOptions" :label="value" :value="parseInt(key)" :key="key"></el-option>
							</el-select>
							<el-tooltip v-if='rules.province[0].isShow' class="item" effect="dark" :content="rules.province[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="市" prop="city">
							<el-select
								v-model="form.aftersaleBillConsultion.city"
								size="mini"
								style="width:150px;"
								@change="changeAddress(form.aftersaleBillConsultion.city,2)"
								:disabled="true"
							>
								<el-option v-for="(value, key) in cityOptions" :label="value" :value="parseInt(key)" :key="key"></el-option>
							</el-select>
							<el-tooltip v-if='rules.city[0].isShow' class="item" effect="dark" :content="rules.city[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="区" prop="area">
							<el-select
								v-model="form.aftersaleBillConsultion.area"
								size="mini"
								style="width:150px;"
								@change="changeAddress(form.aftersaleBillConsultion.area,3)"
								:disabled="true"
							>
								<el-option v-for="(value, key) in areaOptions" :label="value" :value="parseInt(key)" :key="key"></el-option>
							</el-select>
							<el-tooltip v-if='rules.area[0].isShow' class="item" effect="dark" :content="rules.area[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="街道">
							 <el-select
							 	placeholder="请选择"
							 	v-model="form.aftersaleBillConsultion.street"
							 	size='mini'
							 	:disabled="true"
							 >
							    <el-option
							      v-for="(value,key) in streetOptions"
							      :key="key"
							      :label="value"
							      :value="parseInt(key)">
							    </el-option>
							 </el-select>

						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="详细地址">
							<el-input
								size='mini'
								v-model="form.aftersaleBillConsultion.receiver_detail_address"
								:disabled="
									form.aftersaleBillConsultion.document_status === 'CLOSE'
									|| form.aftersaleBillConsultion.if_send_zd === 'Y'
								"
							></el-input>
						</el-form-item>

						<el-form-item label="关闭人">
							<el-input size='mini' :value="form.aftersaleBillConsultion.close_man_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="关闭人分组">
							<el-input size='mini' :value="form.aftersaleBillConsultion.close_man_group_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="关闭时间">
							<el-date-picker
								:value="form.aftersaleBillConsultion.close_time"
							    type="datetime"
							    size="mini"
							    disabled
							></el-date-picker>
						</el-form-item>
						<el-form-item label="客户收货地址">
							<el-input
								v-if="form.aftersaleBillConsultion.after_order_id && !form.aftersaleBillConsultion.merge_trade_id"
								size='mini'
								:value="
									form.aftersaleBillConsultion.customer_receiver_address =
										(provinceOptions[form.aftersaleBillConsultion.province] || '') +
										(cityOptions[form.aftersaleBillConsultion.city] || '') +
										(areaOptions[form.aftersaleBillConsultion.area] || '') +
										(streetOptions[form.aftersaleBillConsultion.street] || '') +
										(form.aftersaleBillConsultion.receiver_detail_address || '')
								"
								disabled
							></el-input>
							<el-input v-else size='mini' :value="form.aftersaleBillConsultion.customer_receiver_address" disabled></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-tab-pane>
		</el-tabs>
	</el-form>
	<el-row class='xpt-flex__bottom' v-fold>
		<el-tabs v-model="selectTab2" @tab-click="selectTab2Click">
			<el-tab-pane label='问题商品' name='goodsInfo' class='xpt-flex'>
				<xpt-list
					:data="form.billConsultionGoods"
					@selection-change="selectGoodsChange"
					:colData="goodsCol"
					:btns="goodsBtns"
	          		selection='checkbox'
	          	>
				</xpt-list>
			</el-tab-pane>
			<el-tab-pane label='操作记录' name='operatInfo' class='xpt-flex'>
				<xpt-list
					:data='operatList'
					:colData='operatCol'
					:showHead="false"
	          		selection=''
	          	>
				</xpt-list>
			</el-tab-pane>
			<el-tab-pane label='介入记录' name='intervene' class='xpt-flex'>
				<xpt-list
					:data='interveneList'
					:colData='intervenecols'
					:showHead="true"
					@radio-change="interveneSelection"
					:btns="interveneBtns"
					ref="interveneList"
	          		selection='radio'
	          	>
				  <template slot='if_solve' slot-scope='scope'>
						<el-switch v-model="scope.row.if_solve" on-text="是" off-text="否" on-value="Y" off-value="N" @change="rowChange(scope.row)"></el-switch>

					</template>
				</xpt-list>
			</el-tab-pane>
      <el-tab-pane label='逆向咨询' name='reverseConsultation' class='xpt-flex'>
				<xpt-list
					:data='reverseConsultationList'
					:colData='reverseConsultationCol'
					:showHead="false"
          selection=''
          :taggelClassName="taggelClassName"
        >
          <template slot='type' slot-scope='scope'>
            <span>逆向咨询单</span>
          </template>
          <template slot="creatorName" slot-scope='scope'>
            <span>AUTO.AUTO</span>
          </template>
          <template slot='con_content' slot-scope='scope'>
            <span>{{scope.row.con_content}}</span>
            <el-button type='primary' size="mini" @click='showAttachmentFile(scope.row, "FOURPL")' style="margin-left: 10px;">查看附件</el-button>
          </template>
          <template slot='first_con_cotent' slot-scope='scope'>
            <p v-if="scope.row.first_con_cotent">首次：{{scope.row.first_con_cotent}}</p>
            <p v-if="scope.row.second_con_cotent">二次：{{scope.row.second_con_cotent}}</p>
            <el-button type='primary' size="mini" @click='showAttachmentFile(scope.row, "NBO")'  style="margin-left: 10px;">查看附件</el-button>
          </template>
				</xpt-list>
			</el-tab-pane>
		</el-tabs>
	</el-row>
	<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload>
</div>
</template>

<script>
import Vue from 'vue';
import cloneDeep from 'loadsh/cloneDeep'
export default {
	props: ['params'],
	data (){
		let self = this;
		return {
			selectTab1: 'basicInformation',
			selectTab2: 'goodsInfo',
			form: {
				aftersaleBillConsultion: {
					province: '',
					city: '',
					area: '',
					street: '',
					type_of_consultion: '',
          type_of_parent_consultion: ''
				},
				analysisVO:{
					evaluation: "",
					evaluationPercentage: "",
					satisfaction: "",
					satisfactionPercentage: "",
				},
				billConsultionGoods: [],
				cloudMessages: [{}],
			},
			document_status_options: {
				CREATE: '创建',
				CONSULTING: '咨询中',
				CLOSE: '关闭',
				REPLIED: '已回复',
			},
			consultion_object_options: {
				A: '物流咨询',
				B: '三包咨询',
				C: '专员处理',
			},
			source_bill_type_options: {
				AFTERSALE: '售后',
				BATCH: '新增',
				SERVICE: '服务单',
				SOT: '门店工作台',
			},
			uploadData: {},
			saveLoading: false,
			commitConsultBillToZdLoading: false,
			manyCommitConsultBillToZdLoading: false,
			closeLoading: false,
			ifClickUpload: false,
			provinceOptions: [],
			cityOptions: [],
			areaOptions: [],
			streetOptions: [],
			selectedGoods: [],
			consultionType:false,
			hideBtnByHost: /(saleuat|sale).linshimuye.com/.test(window.location.host),
			rules: {
				province: [{
					required: this.params.isFromAfterSales && !this.params.isFromAfterSales.merge_trade_id && this.params.isFromAfterSales.after_order_id,//无源售后单下推省市区必填
					validator: (rule, value, callback) => {
						if(!this.form.aftersaleBillConsultion.merge_trade_id && this.form.aftersaleBillConsultion.after_order_id && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请选择省',
				}],
				city: [{
					required: this.params.isFromAfterSales && !this.params.isFromAfterSales.merge_trade_id && this.params.isFromAfterSales.after_order_id,//无源售后单下推省市区必填
					validator: (rule, value, callback) => {
						if(!this.form.aftersaleBillConsultion.merge_trade_id && this.form.aftersaleBillConsultion.after_order_id && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请选择市',
				}],
				area: [{
					required: this.params.isFromAfterSales && !this.params.isFromAfterSales.merge_trade_id && this.params.isFromAfterSales.after_order_id,//无源售后单下推省市区必填
					validator: (rule, value, callback) => {
						if(!this.form.aftersaleBillConsultion.merge_trade_id && this.form.aftersaleBillConsultion.after_order_id && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请选择区',
				}],
				type_of_consultion: [{
					required: self.params.isConsultation?false:true,
					message: '请选择咨询类型',
          isShow: false,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if(!this.form.aftersaleBillConsultion.type_of_parent_consultion && !value) {
                this.rules[rule.field][0].isShow = true
                callback(new Error(''))
              } else {
                this.rules[rule.field][0].isShow = false
                callback()
              }
            }
          }
        ],
        type_of_parent_consultion: [{
					required: self.params.isConsultation?false:true,
					message: '请选择咨询类型',
          isShow: false,
          trigger: 'blur',
          validator: (rule, value, callback) => {
              if(!value && !this.form.aftersaleBillConsultion.type_of_consultion) {
               this.rules[rule.field][0].isShow = true
               callback(new Error(''))
              } else {
                this.rules[rule.field][0].isShow = false
                callback()
              }
          }
        }]
			},
			goodsBtns: [{
				type: 'primary',
				txt: '新增商品',
				click: this.addGoods,
				disabled: () => this.form.aftersaleBillConsultion.document_status === 'CLOSE' || this.form.aftersaleBillConsultion.if_send_zd === 'Y' || this.commitConsultBillToZdLoading
	        }, {
				type: 'danger',
				txt: '删除',
				click: this.delGood,
				disabled: () => this.form.aftersaleBillConsultion.document_status === 'CLOSE' || this.form.aftersaleBillConsultion.if_send_zd === 'Y' || this.commitConsultBillToZdLoading
	        }],
	        goodsCol: [{
				label: '买家昵称',
				prop: 'buyer_name',
			},  {
				label: '物料编码',
				prop: 'material_number',
			}, {
				label: '物料名称',
				prop: 'material_name',
			}, {
				label: '物料规格描述',
				prop: 'material_specification',
			}, {
				label: '数量',
				prop: 'count',
			}, {
				label: '单位',
				prop: 'unit',
			}, {
				label: '批号',
				prop: 'lot_number',
			}, {
				label: '体积',
				prop: 'volume',
			}, {
				label: '包件',
				prop: 'package_qty',
			}, {
				label: '发货仓库',
				prop: 'outbound_warehouse_name'
			}, {
				label: '批次子单号',
				prop: 'outbound_notice_no',
				width: 180
			}, {
				label: '中转承运商',
				prop: 'zd_change_trains_logistics_company',
				width: 180
			}, {
				label: '销售单号',
				prop: 'sys_trade_no',
				width: 200,
			}],
			operatList: [],
			operatCol: [{
				label: '操作人',
				prop: 'operator_name',
			}, {
				label: '业务操作',
				prop: 'operate_type',
				formatter: prop => ({
					CREATE: '新增',
					SAVE: '保存',
					LOCK: '锁定',
					UNLOCK: '解锁',
					SUBMIT: '提交',
					RETRACT: '撤回',
					AUDIT: '审核',
					CHANGE: '确认变更',
					REVERSE_AUDIT: '反审核',
					SUBMIT_APPROVE: '提交审批',
					PASS: '审批通过',
					NOT_PASS: '审批不通过',
					TRACK: '执行跟踪',
					RECALL: '撤回仓储',
					CLOSE: '关闭',
					OPEN: '反关闭',
					CANCEL: '已取消',
					//extra
					ADD_REFUND_ITEM: '引入退款明细',
					DELETE_REFUND_ITEM: '删除退款明细',
					ADD_TAOBAO_REFUND: '引入淘宝退款申请',
					DELETE_TAOBAO_REFUND: '删除淘宝退款申请',
					SUBMITPURCHASE: '提交采购',
					PLAN_RETRACT: '方案撤回',
					REJECT: '驳回',
					LOCKER: '锁定',
					REVOCATION: '撤回',
					VERIFY: '审核',
					OPPOSITE_VERIFY: '反审核',
					SUBMIT_EXAMINE: '提交审批',
					PASS_EXAMINE: '审批通过',
					UNPASS_EXAMINE: '审批不通过',
				}[prop] || prop),
			}, {
				label: '操作描述',
				prop: 'description',
			}, {
				label: '操作时间',
				prop: 'operate_time',
				format: 'dataFormat1',
				width: 150,
			}],
			resultdata:[],
			interveneList:[],
			interveneSelect:null,
			interveneBtns:[
				{
					type: 'primary',
					txt: '提交',
					click:this.sendInterveneToZD,
					disabled:()=>{
						return self.commitConsultBillToZdLoading
					}
				},
				{
					type: 'danger',
					txt: '删除',
					click:this.interveneDelete,
					disabled:()=>{
						return self.commitConsultBillToZdLoading
					}
				},
			],
			// interveneBtns:[],
			intervenecols:[
				{
					label: '介入单号',
					prop: 'intervene_no',
				},
				{
					label: '创建人',
					prop: 'creater',
				},
				{
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
				},
				{
					label: '介入类型',
					prop: 'intervene_type',
					format: 'auxFormat',
					formatParams: 'jrlx',
				},
				{
					label: '需介入原因',
					prop: 'reason',
				},
				{
					label: '状态',
					prop: 'status',
					formatter :prop => ({
						CREATE: '创建',
						ING: '介入中',
						FINISH: '已回复',
					}[prop] || prop),
				},
				{
					label: '介入时间',
					prop: 'intervene_time',
					format: 'dataFormat1',
				},
				{
					label: '介入处理人',
					prop: 'commissioner',
				},
				{
					label: '回复',
					prop: 'reply',
				},
				{
					label: '完结时间',
					prop: 'finish_time',
					format: 'dataFormat1',
				},
				{
					label: '是否解决问题',
					slot: 'if_solve',
				},
			],
      reverseConsultationList: [],
      reverseConsultationCol: [
        {
          label: '单据类型',
          prop: 'type',
          slot: 'type'
        },
        {
          label: '咨询单号',
          prop: 'consultation_no',
        },
        {
          label: '来源单号',
          prop: 'batch_trade_no',
        },
        {
          label: '咨询内容',
          prop: 'con_content',
          slot: 'con_content',
        },
        {
          label: '回复内容',
          prop: 'first_con_cotent',
          slot: 'first_con_cotent'
        },
        {
          label: '咨询单创建人',
          prop: 'creator',
          slot: 'creatorName'
        },
        {
          label: '咨询单创建时间',
          prop: 'create_time',
          format: 'dataFormat1',
        },
      ],
      attachmentFileLoading: false,
		}
	},
	methods: {
    handleTypeOfConsultionChange(val) {
      this.form.aftersaleBillConsultion.type_of_parent_consultion = val[0];
      this.form.aftersaleBillConsultion.type_of_consultion = val[1] || '';
    },
    typeOfConsultionFilter(item) {
      if(item.categoryCode == 'ZXLX') {
        return item.ext_field1 == 'Y';
      }
      return true;
    },
    showAttachmentFile(row, fileType) {
      let self = this
      if (this.attachmentFileLoading) return
      this.attachmentFileLoading = true
      let params = {
        id: row.id,
        fileType: fileType,
        _close: () => {
            self.attachmentFileLoading = false
          }
      };
      this.$root.eventHandle.$emit('alert', {
        params: params,
        component: () => import('@components/after_sales/reverseConsultationList/showAttachmentFile.vue'),
        style: 'width:800px;height:560px',
        title: '附件'
      })
    },
    taggelClassName (row){
      return this.$style['row-height']
		},
        /**
         *选择客户地址
        **/
        choiceAddress(){
            let self = this;
        if(!this.form.aftersaleBillConsultion.buyer_id){
            this.$message.error('买家昵称为空，请先选择买家');
            return;
        }
        let params = {
            isAlert:true,
            type:'radio',
            cust_id:this.form.aftersaleBillConsultion.buyer_id,
            merge_trade_no:this.form.aftersaleBillConsultion.merge_trade_no,
          oaid:this.form.aftersaleBillConsultion.oaid,
          expire_time:this.form.aftersaleBillConsultion.expire_time
        };

        params.callback = (d)=>{
            this.setAdressInfo(d.data[0])
        }
        this.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('@components/after_invoices/addresslist'),
            style:'width:80%;height:560px',
            title:'选择客户地址'
        });
        },
        //地址信息参数赋值
        setAdressInfo(data){
            this.form.aftersaleBillConsultion.buyer_id=data.cust_id;//用户
            this.form.aftersaleBillConsultion.buyer_name=data.cust_name;//用户
            this.form.aftersaleBillConsultion.receiver_name=data.receiver_name;//  '收货人',
            this.form.aftersaleBillConsultion.receiver_phone=data.receiver_mobile;//  '收货人电话',
            this.form.aftersaleBillConsultion.receiver_detail_address=data.receiver_address;//  '收货人地址',
            this.form.aftersaleBillConsultion.province=data.receiver_state;//  '省id',
            this.form.aftersaleBillConsultion.city=data.receiver_city;//  '市id',
            this.form.aftersaleBillConsultion.street=data.receiver_street;//  '市id',
            this.form.aftersaleBillConsultion.area=data.receiver_district//  '区id',
            this.form.aftersaleBillConsultion.encryption_add_id = data.fid;
            this.form.aftersaleBillConsultion.customer_receiver_address = (this.provinceOptions[data.receiver_state] || '') +
										(this.cityOptions[data.receiver_city] || '') +
										(this.areaOptions[data.receiver_district] || '') +
										(this.streetOptions[this.form.aftersaleBillConsultion.street] || '') +
										(data.receiver_address || '')
        },
        // 是否解密订单
		ifDecryption(callback,reject){
            if(!this.form.aftersaleBillConsultion.document_status){
                callback()
                return
            }
			let self = this;
			let data ={
				"addressId": this.form.aftersaleBillConsultion.encryption_add_id,
                "mergeTradeNo": this.form.aftersaleBillConsultion.merge_trade_no
			  }
			this.ajax.postStream("/kingdee-web/api/end/ifDecryption", data	, async res => {
				var obj = res.body;
				if(obj.result){
					if(!obj.content){
                        callback()
					}else{
                        let isOk = await self.decryption()
                        isOk ? callback() : reject()
                    }
				}
			});
		},
        // 解密
        decryption(){
		    return new Promise((resolve)=>{
				let data ={
                    "addressId": this.form.aftersaleBillConsultion.encryption_add_id,
                    "mergeTradeNo": this.form.aftersaleBillConsultion.merge_trade_no
				  }
				this.ajax.postStream("/kingdee-web/api/end/decryption", data, res => {
					let obj = res.body;
                    if(obj.result && obj.content.addressId){
                        resolve(true)
                    }else{
                        resolve(false)
                    }
				});
			})


		},
		// 上一页 or 下一页
		nextOrPrevOrder (type){
			this.params.orderList.some((obj, index) => {
				if(obj.id === this.params.id){
					let newOrder = this.params.orderList[index + (type === 'next' ? 1 : -1)]

					if(newOrder){
						this.params.id = newOrder.id
						this.selectTab2 = 'goodsInfo'
						this.getDetail(newOrder.id)
					}else {
						this.$message.error('没有更多')
					}
					return true
				}
			})
		},

		consultionDisableOption(){
			let data = __AUX.get('consultionZXLX');
			this.resultdata=[];
			// console.log(data);
			data.forEach(item=>{
				if(item.code == "JFZX0015"){
					this.resultdata.push(item);
				};
			});
			console.log('this.resultdata',this.resultdata)
			// return resultdata;
		},
		cloudMessagesChange (e, msgObj){
			msgObj.content = e.target.value = (e.target.value || '').trim()
		},
		consultionFormat(val){
			switch (val){
				case 'Y' : return '满意';				case 'N' : return '不满意';			}
		},
		// 附件操作
		uploadAction (method){
			if(method === 'post'){
				this.ifClickUpload = true
				this.uploadData = {
					// parent_name: 'AFTER_ORDER',
					parent_no: this.form.aftersaleBillConsultion.consultion_no,
					// child_name : 'CONSULTION',
					child_no : this.form.aftersaleBillConsultion.consultion_no,
				}
				setTimeout(() => {
					this.ifClickUpload = false
				}, 100)
			}else {
				this.$root.eventHandle.$emit('alert', {
					params: {
						className: this.form.aftersaleBillConsultion.if_send_zd === 'Y' && this.$style['download-img-del-hide'],
						// parent_name : 'AFTER_ORDER',
						parent_name_txt: '咨询单号',
						parent_no : this.form.aftersaleBillConsultion.consultion_no,
						// other_parent_nos : [this.form.aftersaleBillConsultion.consultion_no],
						// child_name : 'CONSULTION',
						child_no : this.form.aftersaleBillConsultion.consultion_no,
						// child_name_txt: '商品问题id',
						ext_data : 'NBO',
						url: '/afterSale-web/api/aftersale/consultion/getConsultFileList',
						// callback: files => {
						// 	this.listAfterMaterialVO[index].attachment_count = files.length
						// },
					},
					component: ()=>import('@components/after_sales/consultationDownload.vue'),
					style: 'width:1000px;height:600px;',
					title: '下载列表',
				})
			}
		},
		// 提交咨询单至4PL
		actionHelper (api){
			var consultion_id = this.form.aftersaleBillConsultion.consultion_id

			this[api + 'Loading'] = true
            if(api === 'commitConsultBillToZd'){
                this.ifDecryption(()=>{
                    this.ajax.postStream('/afterSale-web/api/aftersale/consultion/' + api,
                        api === 'close' ? { consultion_id } : consultion_id,
                        res => {
                            if(res.body.result){
                                this.getDetail(null, res.body.msg)
                            }else {
                                this.$message.error(res.body.msg)
                            }
                            this[api + 'Loading'] = false
                        }
                    )
                },()=>{
                    this.$message.error('当前客户地址淘宝不支持解密，请重新选择其他客户地址')
                    this[api + 'Loading'] = false
                    this.selectTab1 = 'otherInformation'
                });
            }else{
                this.ajax.postStream('/afterSale-web/api/aftersale/consultion/' + api,
                    api === 'close' ? { consultion_id } : consultion_id,
                    res => {
                        if(res.body.result){
                            this.getDetail(null, res.body.msg)
                        }else {
                            this.$message.error(res.body.msg)
                        }
                        this[api + 'Loading'] = false
                    }
                )

            }


		},
		// 补充咨询内容
		addCloudMessages (){
			this.$root.eventHandle.$emit('alert',{
				params: {
					callback: d => {
						this.ajax.postStream('/afterSale-web/api/aftersale/consultion/supplyConsultion', {
							source_bill_id: this.form.aftersaleBillConsultion.consultion_id,
							source_bill_no: this.form.aftersaleBillConsultion.consultion_no,
							content: d,
						}, res => {
							if(res.body.result){
								this.getDetail(null, res.body.msg)
							}else {
								this.$message.error(res.body.msg)
							}
						})
					}
				},
				component:()=>import('@components/after_sales/addCloudMessages'),
				style:'width:420px;height:200px',
				title:'补充咨询内容'
			})
		},
		// 删除咨询内容
		delCloudMessages (msg_id){
			if(this.commitConsultBillToZdLoading){
				return
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/consultion/deleteMsg', { msg_id }, res => {
				if(res.body.result){
					this.getDetail(null, res.body.msg)
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		selectTab2Click (){
			if(this.selectTab2 === 'operatInfo' && this.form.aftersaleBillConsultion.consultion_id){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.form.aftersaleBillConsultion.consultion_id }, res => {
					if(res.body.result){
						this.operatList = res.body.content || []
					}
				})
			}
			if(this.selectTab2 === 'intervene' ){
				this.getInterveneList();
			}
      if (this.selectTab2 === 'reverseConsultation') {
        this.ajax.postStream('/afterSale-web/api/aftersale/reverseCon/getListforAftersaleOrder', this.form.aftersaleBillConsultion.merge_trade_id, res => {
					if(res.body.result){
						this.reverseConsultationList = res.body.content || []
					} else {
            this.reverseConsultationList = []
          }
				}).catch(() => {
          this.reverseConsultationList = []
        })
      }
		},
		changeAddress (code,type){
			var key = {
				1: 'city',
				2: 'area',
				3: 'street',
			}[type]

			if(!code) return;
			this.getAddress(code,(data)=>{
			    this[key + 'Options'] = data || {};
			    var value = this.form.aftersaleBillConsultion[key];
			    if(!data || !data.hasOwnProperty(this.form.aftersaleBillConsultion[key])){
				      if(type == 1){
				        this.form.aftersaleBillConsultion.city = null;
				        this.form.aftersaleBillConsultion.area = null;
				        this.form.aftersaleBillConsultion.street = null;
				      }else if(type == 2){
				        this.form.aftersaleBillConsultion.area = null;
				        this.form.aftersaleBillConsultion.street = null;
				      } else if(type == 3) {
						  this.form.aftersaleBillConsultion.street = null;
					  }

		    	}
			})
		},
		// 选中商品
		selectGoodsChange (s){
			this.selectedGoods = s
		},
		// 新增商品
		addGoods (){
			if(
				(!this.form.aftersaleBillConsultion.merge_trade_id && !this.form.aftersaleBillConsultion.after_order_id)//手工新增
				|| (this.form.aftersaleBillConsultion.merge_trade_id && !this.form.aftersaleBillConsultion.batch_trade_id && (this.form.aftersaleBillConsultion.after_order_id))//有源售后单没有勾选商品直接下推
			){
				this.$message.error('请先选单')
			}else if(this.form.aftersaleBillConsultion.batch_trade_id){

				this.$root.eventHandle.$emit('alert',{
					title: '发运商品信息',
					style: 'width:1000px;height:600px',
					component: ()=>import('@components/after_sales/consultationAddGood'),
					params: {
						batch_trade_id: this.form.aftersaleBillConsultion.batch_trade_id,
						callback: d => {
							this.form.billConsultionGoods = this.form.billConsultionGoods.concat(d)
						}
					},
				})
			}else {
				this.$root.eventHandle.$emit('alert',{
					params: {
						callback: d => {
							d = d.data

							console.log(9999,d)
							this.form.billConsultionGoods.push({
								// material_id: d.sourceMaterialId || d.bom_material_id,
								material_number: d.materialNumber||d.bom_material_num,
								material_name: d.materialName || d.bom_material_name,
								material_specification: d.materialSpecification||d.bom_material_desc,
								volume: d.volume,
								unit: d.materialUnit,
							})
						}
					},
					component:()=>import('@components/after_sales_supply/bomgoods'),
					style: 'width:80%;height:80%',
					title: '选择物料',
				})
			}
		},
		// 删除商品
		delGood (){
			if(this.selectedGoods.length === 0){
				this.$message.error('请选中至少一行商品行')
			}else {
				this.selectedGoods.forEach(obj => {
					this.form.billConsultionGoods.splice(this.form.billConsultionGoods.indexOf(obj), 1)
				})
				this.$message.success('操作成功，点击保存后生效')
			}
		},
		consultionOpen(){
			let self = this;
			this.$root.eventHandle.$emit('alert',{
				title: '专员介入',
				style:'width:500px;height:360px',
				component: () => import('@components/after_sales/intervene'),
				params: {
					parentsId:self.form.aftersaleBillConsultion.consultion_id,
					callback: d => {
						self.getInterveneListAndsendInterveneToZD();
					}
				},
			})
		},

		getInterveneList(){
			this.ajax.postStream('/afterSale-web/api/aftersale/consultion/getInterveneList',  this.form.aftersaleBillConsultion.consultion_id , res => {
				if(res.body.result){
					this.interveneList = res.body.content || [];
					this.$refs.interveneList.clearRadioSelect();
				}else{
					this.$message.error(res.body.msg);
				}
			})
		},
		//合并专员介入里的确定和提交到4PL
		getInterveneListAndsetInterveneSelect(){
			return new Promise((resolve,reject)=>{
				this.ajax.postStream('/afterSale-web/api/aftersale/consultion/getInterveneList',  this.form.aftersaleBillConsultion.consultion_id , res => {
				if(res.body.result){
					this.interveneList = res.body.content || [];
					this.$refs.interveneList.clearRadioSelect();
					let interveneSelectIndex=this.interveneList.length-1
					this.interveneSelect=this.interveneList[interveneSelectIndex]
					console.log(this.interveneSelect)
					resolve('success');
				}else{
					this.$message.error(res.body.msg);
					reject('error');
				}
			})
			})
		},
		//合并专员介入里的确定和提交到4PL
		async getInterveneListAndsendInterveneToZD(){
			let result=await this.getInterveneListAndsetInterveneSelect()
			console.log(result)
			if(result=='success'){
				this.sendInterveneToZD()
			}
		},
		rowChange(row){
			this.ajax.postStream('/afterSale-web/api/aftersale/consultion/interveneIfSolve',  {id:row.id,if_solve:row.if_solve}, res => {
				if(res.body.result){
					this.$message.success(res.body.msg);
				}else{
					this.$message.error(res.body.msg);
					this.getInterveneList();
				}
			})
		},
		interveneSelection(list){
			this.interveneSelect = list;
			// console.log(list,this.interveneSelect);
		},
		interveneDelete(){
			if(!this.interveneSelect){
				this.$message.error('请选择介入单号')
				return
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/consultion/interveneDelete',  [this.interveneSelect.id], res => {
				if(res.body.result){
					this.getInterveneList();
					this.$message.success(res.body.msg);
				}else{
					this.$message.error(res.body.msg);
				}
			})
		},
		sendInterveneToZD(){
			console.log(this.interveneSelect)
			if(!this.interveneSelect){
				this.$message.error('提交失败，请选择介入单号')
				return
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/consultion/sendInterveneToZD',  this.interveneSelect.id, res => {
				if(res.body.result){
					// this.interveneList = res.body.content || []
					this.$message.success(res.body.msg);
					this.interveneSelect=null
					this.$refs.interveneList.clearRadioSelect();
					this.getInterveneList();
				}else{
					this.$message.error(res.body.msg);
				}
			},err=>{
				this.$message.error(err)
			})
		},
		selectOrder (){
			this.$root.eventHandle.$emit('alert',{
				title: '批次订单列表',
				style:'width:900px;height:560px',
				component: () => import('@components/after_sales/alert/select_order'),
				params: {
					fromPage:"consultation",
					setSearchObj: searchObj => {
						searchObj.zd_delivery_status = 'Y'
					},
					orderList:this.params.orderList,
					merge_trade_id: this.form.aftersaleBillConsultion.after_order_id ? this.form.aftersaleBillConsultion.merge_trade_id : '',
					callback: d => {
						if(d._type=="1"){
							this.$root.eventHandle.$emit('removeTab',this.params.tabName)
						}
						this.form.aftersaleBillConsultion.merge_trade_id = d.merge_trade_id; //合并订单id
						this.form.aftersaleBillConsultion.merge_trade_no = d.merge_trade_no; //合并订单号
						this.form.aftersaleBillConsultion.batch_trade_id = d.batch_trade_id;
						this.form.aftersaleBillConsultion.batch_trade_no = d.batch_trade_no; //批次订单号
						this.form.aftersaleBillConsultion.buyer_name = d.master_cust_name; //买家昵称（冗余字段）
						this.form.aftersaleBillConsultion.creator_group_name = d.creator_group_name; //买家昵称（冗余字段）
						// this.form.saleman = d.; //业务员ID
						//this.form.creator_name = d.creator_name; //业务员
						// this.form.saleman_group = d.; //业务员分组
						// this.form.delivery_time = d.commit_time; //发货时间
						this.form.aftersaleBillConsultion.logistics_name = d.zd_delivery_logistics_company; //物流供应商
						this.form.aftersaleBillConsultion.three_supplier_name = d.three_guarantees_supplier; //三包供应商
						this.form.aftersaleBillConsultion.logistics_no = d.zd_logistics_number; //物流单号
						this.form.aftersaleBillConsultion.point_id = d.logistics_point; //提货点ID
						this.form.aftersaleBillConsultion.point_name = d.logistics_point_name; //提货点
						this.form.aftersaleBillConsultion.three_name = d.three_name; //三包点
						// this.form.delivery_phone = d.; //提货点电话
						// this.form.three_phone = d.; //三包公司电话
						this.form.aftersaleBillConsultion.receiver_name = d.receiver_name;  //  收货人
						this.form.aftersaleBillConsultion.receiver_phone = d.receiver_mobile;  //  收货人手机
						this.form.aftersaleBillConsultion.customer_receiver_address = d.receiver_address_name;  //  收地址

						this.form.aftersaleBillConsultion.receiver_detail_address = ''//清空详细地址
						this.form.aftersaleBillConsultion.province = '';  //  省
						this.form.aftersaleBillConsultion.city = '';  //  市
						this.form.aftersaleBillConsultion.area = '';  //  区street
						this.form.aftersaleBillConsultion.street = '';  //  区street
						// this.form.street = d.receiverInfoVO.receiver_street_name;  //  收货人
						// self.getSalemanInfo(d.batch_trade_id);
						this.$set(this.form, 'scmBatchTrade', {deliver_method: d.deliver_method})
						this.form.billConsultionGoods = []//选单后情况商品行
					}
				},
			})
		},
		getInfoFromAfterSales (){
			var postData = {...this.params.isFromAfterSales};
			if(!!this.params.isFromAfterSales.consultionType){
					this.consultionType = true;
				}
			if(postData.question_goods_ids.length === 0){
				delete postData.question_goods_ids
				delete postData.consultionType
				Object.assign(this.form.aftersaleBillConsultion, postData, {
					source_bill_id: postData.after_order_id,
					source_bill_no: postData.after_order_no,
				})

				if(!!this.params.isConsultation){
					console.log(this.params.isConsultation,1111111)
					Object.assign(this.form.aftersaleBillConsultion, postData, {
						batch_trade_id: postData.batch_trade_id,
					})
					this.form.cloudMessages = [];
					this.form.aftersaleBillConsultion.type_of_consultion = 'JFZX0015';
					this.form.aftersaleBillConsultion.buyer_name = postData.buyer_name;
					// this.form.aftersaleBillConsultion.type_of_consultion = postData.consultionType;
				}
				if(!!this.params.isFromAfterSales.consultionType){
					this.form.aftersaleBillConsultion.type_of_consultion = this.params.isFromAfterSales.consultionType;
				}

        if(!!this.params.isFromAfterSales.parentConsultionType){
					this.form.aftersaleBillConsultion.type_of_parent_consultion = this.params.isFromAfterSales.parentConsultionType;
				}
				// console.log(this.form.aftersaleBillConsultion.batch_trade_id,postData);

			}else {
				this.ajax.postStream('/afterSale-web/api/aftersale/consultion/getBatchInfo', {
					after_order_id: postData.after_order_id,
					after_order_no: postData.after_order_no,
					question_goods_ids: postData.question_goods_ids,
					batch_trade_no: postData.batch_trade_no,
				}, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
						this.form.billConsultionGoods = res.body.content.billConsultionGoods//执行顺序1
						delete res.body.content.billConsultionGoods//执行顺序2
						delete res.body.content.address_id//执行顺序2
						delete res.body.content.volume_total//执行顺序2
						delete res.body.content.zd_delivery_time//执行顺序2
						delete res.body.content.zd_logistics_number//执行顺序2
						delete res.body.content.package_total//执行顺序2
						Object.assign(this.form.aftersaleBillConsultion, res.body.content, {//执行顺序3
							source_bill_id: postData.after_order_id,
							source_bill_no: postData.after_order_no,
							type_of_consultion: postData.consultionType,
              type_of_parent_consultion: postData.parentConsultionType,
							merge_trade_no: postData.merge_trade_no,
						})
						this.$set(this.form, 'scmBatchTrade', res.body.content.scmBatchTrade)
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}
		},
		preSave(){
			let msgArr = [];
			this.form.billConsultionGoods.forEach(item=>{
				if(!!item.zd_change_trains_logistics_number){
					msgArr.push(item.zd_change_trains_logistics_number);
				}
			})
			if(Array.from(new Set(msgArr)).length>1){
				 this.$confirm('因您当前添加了不同中转承运商的问题商品，点击【保存】后系统会自动拆分成多个咨询单，请确认是否继续保存咨询？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
					}).then(() => {
						this.checkSave()
					}).catch(() => {

					});
			} else{
				this.checkSave()
			}
		},
		checkSave () {
			let self = this
			if (!((this.form.cloudMessages[0]&&this.form.cloudMessages[0].content) ||!!this.params.isConsultation)){
				this.$message.error('请添加至少一条咨询内容')
			}else if (!(!this.form.cloudMessages.some(o => !o.content)||!!this.params.isConsultation)){
				this.$message.error('每一条咨询内容都必填')
			}else if(this.form.billConsultionGoods.length === 0){
				this.$message.error('请添加至少一条商品行')
			} else {
				let stock = this.form.billConsultionGoods[0].outbound_warehouse_id
				// THREES 快递，LOGISTICS 物流 JFZX0013 咨询到货
				if ((/^(THREES|LOGISTICS)$/).test(this.form.scmBatchTrade.deliver_method) && (['JFZX0013','EJDH'].includes(this.form.aftersaleBillConsultion.type_of_consultion)) && !(this.form.billConsultionGoods.every(item => item.outbound_warehouse_id === stock))) {
					this.$confirm(
						"批次单送货方式=物流或三包，因您当前添加了不同发货仓库的问题商品，点击【保存】后系统会自动拆分成多个咨询单，请确认是否继续保存咨询？",
						"提示",
						{
							confirmButtonText: "确定",
							cancelButtonText: "取消",
							type: "danger",
						}
					).then(() => {
						self.save()
					}).catch(() => {

					});
				} else {
					self.save()
				}
			}
		},
		save (){
			let self = this
			self.$refs.form.validate(valid => {
				if(valid){
					self.saveLoading = true
					let params= cloneDeep(self.form)
					delete params.analysisVO
          delete params.aftersaleBillConsultion.consultion_no_satisfaction_remark
          delete params.aftersaleBillConsultion.consultion_no_satisfaction_tag
          delete params.aftersaleBillConsultion.parentConsultionType
					self.ajax.postStream('/afterSale-web/api/aftersale/consultion/save', params, res => {
						if(res.body.result){
							self.getDetail(res.body.content, res.body.msg)
						}else {
							self.$message.error(res.body.msg || '保存失败')
						}
						self.saveLoading = false
					})
				}else if (!self.form.aftersaleBillConsultion.type_of_parent_consultion){
					self.selectTab1 = 'basicInformation'
				}else {
					self.selectTab1 = 'otherInformation'
				}
			})
		},
		getDetail (id, msg){
			var postDataID = id || this.form.aftersaleBillConsultion.consultion_id

			postDataID && this.ajax.postStream('/afterSale-web/api/aftersale/consultion/getDetail', { consultion_id: postDataID }, res => {
				if(res.body.result){
					this.$message.success(msg || res.body.msg)
					this.form = res.body.content;
					this.getInterveneList();
					delete this.form.listOperateLog
                    delete this.form.listSupplyInterfaceVo
                    let pageParams = {
                        id: this.params.id
                    }
                    Vue.prototype.tabId[this.params.tabName] = JSON.stringify(pageParams);
				}else {
                    this.$message.error(res.body.msg)
                    Vue.prototype.tabId[this.params.tabName] = '';
				}
            Vue.prototype.tabNo[Vue.prototype.activeTab] = res.body.content.aftersaleBillConsultion.consultion_no;
        // console.log("Vue",Vue.prototype.tabNo);
			})
		},
    dissatisfyDialog() {
      let self = this;
      this.$root.eventHandle.$emit("alert", {
				title: "不满意评价",
				style: "width:400px;height:300px",
				component: () =>
					import("@components/after_sales/dissatisfyDialog.vue"),
				params: {
          consultionId: self.form.aftersaleBillConsultion.consultion_id,
					callback: (d) => {
						console.log('不满意评价', d)
						self.getDetail();
					},
				},
			});
    },
		setmany(data){
      if (data === 'N') {
        this.dissatisfyDialog()
      } else {
        let self = this;
        var postDataID =this.form.aftersaleBillConsultion.consultion_id
        this.ajax.postStream('/afterSale-web/api/aftersale/consultion/manyCommitSatisfactionToZd', { consultion_satisfaction : data ,consultion_id: postDataID}, res => {
          if(res.body.result){
            this.getDetail(null, res.body.msg)
          }else {
            this.$message.error(res.body.msg)
          }
          })
      }
		}
	},
	mounted (){
		this.consultionDisableOption();

		this.getAddress(data => {
			this.provinceOptions = data
	    })

    if(this.$refs.typeOfConsultionSelect) {
      this.$refs.typeOfConsultionSelect.visibleChange = arg => {
          var self = this.$refs.typeOfConsultionSelect
        clearTimeout(self.timer)
        if(arg) {
          self.list = __AUX.get(self.aux_name)
        } else {
          // 收起下拉列表，延时处理
          self.timer = setTimeout(() => {
            self.list = __AUX.get(self.aux_name)
          }, 200)
        }
      }
    }


		if(this.params.id){
			this.getDetail(this.params.id)
		}

		if(this.params.isFromAfterSales){
      console.log('form from after sales')
			this.getInfoFromAfterSales()
		}

		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
			this.getDetail()
		})
	},
}
</script>

<style module>
.row-height :global(.cell) {
	height: auto!important;
}
.textarea-style :global(.el-form-item__content) {
	position: relative;
    height: auto;
    margin-bottom: 10px;
}
.textarea-style :global(.msg-date) {
	position: absolute;
	left: 5px;
    color: #bfbaba;
}
.textarea-style :global(.msg-date + .el-textarea) textarea {
	text-indent: 130px;
}
.textarea-style :global(.el-form-item__label) {
	font-weight: bold;
}
.textarea-style :global(.el-form-item__content .el-icon-circle-close) {
	position: absolute;
    right: -4px;
    top: -7px;
    font-size: 17px!important;
    cursor: pointer;
}
.textarea-style :global(.el-textarea.is-disabled) textarea {
	border-color: #fff;
	background-color: #fff;
}
.textarea-style:global(.Y) :global(.el-textarea.is-disabled) textarea {
	border-color: #fff;
    background-color: #eef1f6;
}
.download-img-del-hide :global(.xpt-image__thumbnail .el-icon-close) {
	display: none;
}
</style>
<style>
.maxHeight .el-form-item__content .el-textarea .el-textarea__inner {
  max-height: 60px !important;
}
</style>
