<template>
<div class="xpt-flex">
	<el-row class="xpt-flex__bottom">
		<el-table :data="list" border tooltip-effect="dark" style="width: 100%;" width='100%'>
			<el-table-column label="合并单号" prop="merge_trade_no" width='170' show-overflow-tooltip></el-table-column>
			<el-table-column label="销售单号" prop="sys_trade_no" width='170' show-overflow-tooltip></el-table-column>
			<el-table-column label="批次单号" prop="group_trade_no" width='190' show-overflow-tooltip></el-table-column>
			<el-table-column label="优惠分类" prop="activity_type_name" width='130'></el-table-column>
			<el-table-column label="优惠活动" prop="activity_name" width='130'></el-table-column>
			<el-table-column label="优惠金额" prop="discount" width='130'></el-table-column>
			<el-table-column label="已使用金额" prop="share_discount" width='120'></el-table-column>
			<el-table-column label="影响售价" prop="if_effect_price" width='150'>
				<template slot-scope="scope">
					{{ { Y: '是', 'N': '否' }[scope.row.if_effect_price] }}
				</template>
			</el-table-column>
			<el-table-column label="业务员" prop="creator_name" width='100'></el-table-column>
			<el-table-column label="分组" prop="group_name" width='100'></el-table-column>

			<el-table-column label="状态" prop="audit_status" width='100' :formatter="audit_status_transform"></el-table-column>
			<el-table-column label="审核人" prop="auditor_name" width='100'></el-table-column>
			<el-table-column label="审核时间" prop="audit_time_str" width='150' show-overflow-tooltip></el-table-column>
			<el-table-column label="创建时间" prop="create_time_str" width='150' show-overflow-tooltip></el-table-column>
			<el-table-column label="取消人" prop="canceler_name" width='100'></el-table-column>
			<el-table-column label="取消时间" prop="cancel_time_str" width='150' show-overflow-tooltip></el-table-column>
		</el-table>
	</el-row>
</div>
</template>

<script>
export default {
	// props: ['params'],
	data (){
		return {
			orderId: '',
			list: [],
		}
	},
	methods: {
		init (_orderId){
			if(_orderId){
				this.orderId = _orderId
				this.ajax.postStream('/order-web/api/mergetrade/discount/list', {merge_trade_id: _orderId}, res => {
					if(res.body.result){
						this.list = res.body.content
						if(this.list.length){
							this.list.push({
								discount: '合计' + this.list.reduce((num, obj) => num + Number(obj.discount), 0)
							})
						}
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}else {
				this.orderId = ''
				this.list = []
			}
		},
		audit_status_transform(row,column){
			var self = this;
			if(row.cancel_status=="Y") return "已取消"
			return {
				"Y":"已审核",
				"N":"未审核"
			}[row[column.property]];
		},
	},
}
</script>