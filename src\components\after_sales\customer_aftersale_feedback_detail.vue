<!-- 客户售后反馈单详情-->
<template>
    <div class="xpt-flex quality-feedback">
        <xpt-headbar>
            <el-button
                type="success"
                size="mini"
                :loading="false"
                @click="initDetail"
                slot='left'
                >刷新</el-button
            >
            <el-button
                type="warning"
                size="mini"
                @click="doLock('LOCK')"
                slot='left'
                >锁定</el-button
            >
            <el-button
                type="danger"
                size="mini"
                @click="doLock('UNLOCK')"
                slot='left'
                >解锁</el-button
            >
            <el-button
                type="primary"
                size="mini"
                @click="deliver"
                slot='left'
                >转交</el-button
            >
            <el-button type="success" size="mini" @click="getFileList" slot='left'>查看附件</el-button>
            <xpt-btngroup class="mgl10" :btngroup='callBtnGroups' :beforeClick="beforeClick" :disabled="ifLock" width='60px' slot='left'></xpt-btngroup>
            <el-button type="primary" size="mini" @click="openActualQuestionTypeDialog" slot='left'>实际问题类型</el-button>
        </xpt-headbar>
       
        <el-row class="xpt-flex__bottom" id='top'>
            <el-form
                :model="submitData"
                :rules="rules"
                ref="submitData"
                class="detail-form"
            >
            <el-tabs v-model="firstTab" style="min-height: 270px" @tab-click="() =>tabChangeClick('firstTab')">
                <el-tab-pane label="基础信息" name="basicInfo" class="xpt-flex">
                    <el-row :gutter="40" class="mgt10">
                        <el-col :span="8">
                            <el-form-item
                                label="单据编号："
                                prop="order_no"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.order_no"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="单据状态："
                                prop="order_status"
                                label-width="110px"
                            >
                                <el-input
                                    size="mini"
                                    disabled
                                    :value="{
                                    WAIT: '待办',
                                    ING: '处理中',
                                    OVER: '处理完成',
                                    FINISH: '已完结',
                                    CLOSED: '已关闭'
                                }[submitData.order_status] "
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="批次单号："
                                prop="batch_trade_no"
                                label-width="110px"
                            >
                            <el-input
                                    v-model="submitData.batch_trade_no"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="业务员："
                                prop="staff_name"
                                label-width="110px"
                            >
                            <el-input
                                    v-model="submitData.staff_name"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="店铺："
                                prop="original_shop_name"
                                label-width="110px"
                            >
                            <el-input
                                    v-model="submitData.original_shop_name"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="所属经销商："
                                prop="dealer_customer_name"
                                label-width="110px"
                            >
                            <el-input
                                    v-model="submitData.dealer_customer_name"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="事业部："
                                prop="business_department"
                                label-width="110px"
                            >
                            <el-input
                                    v-model="businessDivisionObj[submitData.business_department]"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    <el-col :span="8">
                            <el-form-item
                                label="问题商品编码："
                                prop="material_code"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.material_code"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="问题商品名称："
                                prop="material_name"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.material_name"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="问题商品规格："
                                prop="material_spec"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.material_spec"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="问题商品项目组："
                                prop="material_spec"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.project_team"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="问题类型："
                                prop="question_type"
                                label-width="110px"
                            >
                                <el-input
                                    :value="{
                                    StoreServiceProblems: '门店服务问题',
                                    CommodityProblems: '商品问题',
                                    LogisticsProblems: '物流问题',
                                    InstallationProblems: '安装问题',
                                }[submitData.question_type] "
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                label="买家昵称："
                                prop="buyer_name"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.buyer_name"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="联系电话："
                                prop="phone"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.phone"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="锁定人："
                                prop="locker_name"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.locker_name"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="锁定时间："
                                prop="locker_time"
                                label-width="110px"
                            >
                                <el-date-picker
                                    type="datetime"
                                    v-model="submitData.locker_time"
                                    size="mini"
                                    disabled
                                ></el-date-picker>
                            </el-form-item>
                            
                        </el-col>
                        <el-col :span="16">
                            <el-form-item
                                label="问题描述："
                                prop="question_desp"
                                label-width="110px"
                            >
                                <el-input 
                                    v-model="submitData.question_desp"
                                    size="mini"
                                    type="textarea"
                                    :rows="2"
                                    disabled
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                
                </el-tab-pane>
                <el-tab-pane label="其它信息" name="otherInfo" class="xpt-flex">
                    <el-row :gutter="40" class="mgt10">
                    <el-col :span="8">
                            <el-form-item
                                label="提交时间："
                                prop="question_submit_time"
                                label-width="110px"
                            >
                                <el-date-picker
                                    type="datetime"
                                    v-model="submitData.question_submit_time"
                                    size="mini"
                                    disabled
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item
                                label="超时处理时间："
                                prop="timeout_process_time"
                                label-width="110px"
                            >
                                <el-date-picker
                                    v-model="submitData.timeout_process_time"
                                    type="datetime"
                                    size="mini"
                                    disabled
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item
                                label="是否超时处理："
                                prop="if_timeout"
                                label-width="110px"
                            >
                                <el-input
                                    :value="{'Y':'是','N':'否'}[submitData.if_timeout]"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="关闭时间："
                                prop="close_time"
                                label-width="110px"
                            >
                                <el-date-picker
                                    v-model="submitData.close_time"
                                    type="datetime"
                                    size="mini"
                                    disabled
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item
                                label="完结时间："
                                prop="finish_time"
                                label-width="110px"
                            >
                                <el-date-picker
                                    v-model="submitData.finish_time"
                                    type="datetime"
                                    size="mini"
                                    disabled
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                label="受理人："
                                prop="representative_nmae"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.representative_nmae"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="受理时间："
                                prop="representative_time"
                                label-width="110px"
                            >
                                <el-date-picker
                                    v-model="submitData.representative_time"
                                    type="datetime"
                                    size="mini"
                                    disabled
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item
                                label="转交人："
                                prop="transmit_name"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.transmit_name"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="转交时间："
                                prop="transmit_time"
                                label-width="110px"
                            >
                                <el-date-picker
                                    v-model="submitData.transmit_time"
                                    type="datetime"
                                    size="mini"
                                    disabled
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item
                                label="评价："
                                prop="appraise_score"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.appraise_score"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                label="处理完成人："
                                prop="creator_name"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.processed_name"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="处理完成时间："
                                prop="processed_time"
                                label-width="110px"
                            >
                                <el-date-picker
                                    v-model="submitData.processed_time"
                                    type="datetime"
                                    size="mini"
                                    disabled
                                ></el-date-picker>
                            </el-form-item>
                            <el-form-item
                                label="解决方案："
                                prop="total_solution"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.total_solution"
                                    size="mini"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="评价反馈："
                                prop="appraise_content"
                                label-width="110px"
                            >
                                <el-input
                                    v-model="submitData.appraise_content"
                                    size="mini"
                                    type="textarea"
                                    :rows="2"
                                    disabled
                                ></el-input>
                            </el-form-item>
                            
                        </el-col>
                    </el-row>
                
                </el-tab-pane>
                <el-tab-pane label='操作记录' name='operateLogList' class='xpt-flex' >
                    <xpt-list
                        :data='operateLogList'
                        :showHead='false'
                        :colData='operateLogCols'
                        :btns='[]'
                        selection=''
                        orderNo
                    ></xpt-list>
                    
			</el-tab-pane>
            </el-tabs>
            </el-form>
        </el-row >
        <el-row class="xpt-flex__bottom" id='bottom' v-fold >
            <el-tabs v-model="secondTab" class="record-list"  @tab-click="() => tabChangeClick('secondTab')">
                <el-tab-pane
                    label="跟进记录"
                    name="messageRecord"
                    class="message-record"
                >
                    <div class="xpt-top">
                        <el-button
                            type="primary"
                            size="mini"
                            :disabled="ifLock"
                            @click="questionSolve('representative','受理')"
                            >受理</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            :disabled="ifLock"
                            @click="questionSolve('reply','回复')"
                            >回复</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            :disabled="ifLock"
                            @click="questionSolve('processed','处理完成')"
                            >处理完成</el-button
                        >
                    </div>
                    <qualityFeedbackFollowReplyList
                        :infoList="followRecordExtraInfoList"
                        :list="currentMessageList"
                        :total="messageRecordTotal"
                        :currentPage="messagePageNow"
                        :fileList="fileList"
                        :followList="followList"
                        :buyObj="{buyer_name:submitData.buyer_name,question_submit_time:submitData.question_submit_time,appraise_content:submitData.question_desp}"
                        @current-page-change="currentPageChange"
                    ></qualityFeedbackFollowReplyList>
                </el-tab-pane>
                <el-tab-pane label="处理进度" name="InRecord">
                    <handle-log-list
                    :params="{
                        isEdit:true,
                        id:params.id
                    }"
                    @updateFormPreOrderTime="time => {}"
                    ref="handleLogList">
                    </handle-log-list>
                </el-tab-pane>
                <el-tab-pane label="下游单据信息" name="downstreamDocument">
                    <xpt-list
						:data='downstreamDocument'
						:showHead='false'
						:colData='colOfDownstreamDocument'
						:btns='[]'
						selection=''
                        orderNo
					></xpt-list>
                </el-tab-pane>
            </el-tabs>
        </el-row >
            
    </div>
   
</template>
<script>
import validate from "@common/validate.js";
import Fn  from '@common/Fn.js'
import qualityFeedbackFollowReplyList from "./qualityFeedbackFollowReplyList";
import handleLogList from '@components/after_sales/handleLogList.vue'
import qs from "qs";
import page_seven from '@components/call_system/six_page_callFun/page_seven_customer_feedback.js'
import HandleLogList from './handleLogList.vue';
import afterSaleIndex from '@components/after_sales/index'
import refundRequest from '@components/after_sales_refund/refundRequest_3'
import supplyOrder from '@components/after_sales_supply/supplyOrder_2.vue'
import returnExchangeDetail from '@components/after_invoices/returnexchangedetail_2'
import afterSalesService from '@components/after_sales_service/detail.vue'
import afterSaleConsultation from '@components/after_sales/consultation'
export default {
    props: ["params"],
	mixins: [page_seven],

    data() {
        let self = this;
        return {
            firstTab: "basicInfo",
            secondTab: "messageRecord",
            submitData: {},
            fileList:[],
            createFileList:[],
            plugFileList:[],
            followRecordExtraInfoList:[],
           currentMessageList:[],
           messageRecordTotal:0,
            orderId:'',
            rules: {},
            handleLogBtn:[
               {
                type: "primary",
                txt: "新增",
                loading: false,
                click() {
                  self.addHandleLog();
                }
              },{
                type: "primary",
                txt: "删除",
                loading: false,
                click() {
                  self.delHandleLog();
                }
              },{
                type: "primary",
                txt: "保存",
                loading: false,
                click() {
                  self.addHandleLog();
                }
              },
              
            ],
            handleLoglist: [],
            handleLogCols: [
               
                {
                    label: "处理人",
                    prop: "handler_name",
                },
                {
                    label: "处理内容",
                    prop: "handle_content",
                },
                {
                    label: "处理时间",
                    prop: "handle_time",
                    format: "dataFormat1",
                },
                {
                    label: "预约时间",
                    prop: "pre_time",
                    format: "dataFormat1",
                },
                {
                    label: "处理优先级",
                    prop: "handle_level",
                },
            ],
            messagePageSize: 10,
            messagePageNow: 1,
            operateLogList: [],
            ifPurchaseDealer: false,
            followList: [],
            ifLock: true,
            operateLogCols: [
                {
                    label: "用户",
                    prop: "operator_name",
                },
                {
                    label: "业务操作",
                    prop: "operate_type",
                    formatter:(val)=>{
                        let option = {
                            CREATE: '新增',
                            SAVE: '保存',
                            LOCK: '锁定',
                            UNLOCK: '解锁',
                            SUBMIT: '提交',
                            RETRACT: '撤回',
                            AUDIT: '审核',
                            CHANGE: '确认变更',
                            REVERSE_AUDIT: '反审核',
                            SUBMIT_APPROVE: '提交审批',
                            PASS: '审批通过',
                            NOT_PASS: '审批不通过',
                            TRACK: '执行跟踪',
                            RECALL: '撤回仓储',
                            CLOSE: '关闭',
                            OPEN: '反关闭',
                            CANCEL: '已取消',
                            //extra
                            ADD_REFUND_ITEM: '引入退款明细',
                            DELETE_REFUND_ITEM: '删除退款明细',
                            ADD_TAOBAO_REFUND: '引入复核退款申请',
                            DELETE_TAOBAO_REFUND: '删除复核退款申请',
                            SUBMITPURCHASE: '提交采购',
                            PLAN_RETRACT: '方案撤回',
                            REJECT: '驳回',
                            LOCKER: '锁定',
                            REVOCATION: '撤回',
                            VERIFY: '审核',
                            OPPOSITE_VERIFY: '反审核',
                            SUBMIT_EXAMINE: '提交审批',
                            PASS_EXAMINE: '审批通过',
                            UNPASS_EXAMINE: '审批不通过',
                        }
                        return option[val] || val
                    }
                },
                {
                    label: "操作描述",
                    prop: "description"
                },
                {
                    label: "操作时间",
                    prop: "operate_time",
                    format: "dataFormat1",
                }
            ],
            ifOrderSupport: false,
            downstreamDocument: [],
            colOfDownstreamDocument: [
                {
                    label: '单据类型',
                    prop: 'bill_type',
                }, {
                    label: '单据编号',
                    prop: 'bill_returns_no',
                    width: 200,
                    redirectClick(row) {
						self.viewOrderDetail(row)
					}
                }, {
                    label: '创建时间',
                    prop: 'cre_time',
                    format: 'dataFormat1',
                },
                {
                    label: '创建人',
                    prop: 'cre_name',
                },
            ],
            businessDivisionObj:__AUX.get('business_division').reduce((a, b) => {
                a[b.code] = b.name
                return a
            }, {})
        };
    },
    mounted() {
        let self = this;
        self.initDetail();
    },
    created () {
		this.$root.eventHandle.$on('resetAllBtnStatus',this.removeBtnStatus);
    },
	beforeDestroy(){
        this.$root.eventHandle.$off('resetAllBtnStatus',this.removeBtnStatus);
	},
    methods: {
        removeBtnStatus(){
            this.ifLock = this.submitData.locker_id !== this.getEmployeeInfo('id')
            this.getEmployeeInfoGroupList()
        },
      getOperateLogList (){
			if(this.params.id){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.params.id }, res => {
					if(res.body.result){
						this.operateLogList = res.body.content || []
					}
				})
			}
		},
        tabChangeClick(tab) {
			if (this.firstTab == 'operateLogList') {
                this.getOperateLogList()
            }
			if (this.secondTab === 'downstreamDocument') {
                this.getRelevantOrders()
            }
		},
        questionSolve(type,title) { 
            let self = this;
            
            let url ="/afterSale-web/api/deal/coupleBack/action/"+type;
            this.$root.eventHandle.$emit("alert", {
                title: title,
                style: "width:600px;height:330px",
                component: () =>
                    import("@components/after_sales/replyAlert"),
                params: {
                    
                    type: type,
                    business_division: self.submitData.business_department,
                    callback: (d,reslove) => {
                        let data =Object.assign(d,{
                          orderId:self.orderId,
                          id:self.submitData.id,
                          last_update_time:self.submitData.last_update_time,
                        });
                        self.ajax.postStream(
                            url,
                            data,
                            (res) => {
                                if (res.body.result ) {
                                    self.$message.success(res.body.msg);
                                    self.initDetail();
                                    reslove && reslove();
                                } else {
                                    res.body.msg &&
                                        self.$message.error(res.body.msg);
                                }
                            },
                            (err) => {
                                self.$message.error(err);
                            }
                        );
                    },
                },
            });
        },
        ifPurchaseDealerOfProxyId() {
			let id = this.getEmployeeInfo('personId'), self = this
			this.ajax.get('/user-web/api/userPerson/getUserPerson/'+id,function(data){
				data = data.body;
				if(data.result){
					if (data.content && data.content.type == 'PURCHASE_DEALER') {
                        self.ifPurchaseDealer = true
					} else {
                        self.ifPurchaseDealer = false
					}
				}
			},function(error){
                self.ifPurchaseDealer = false
			})
		},
        initDetail() {
            this.followList = []
            let p1 = this.getDetail();
            let p2 = this.getFollowReplyDetail();
            Promise.all([p1, p2]).then(() => {
                this.getFollowDetail();
                this.$refs.handleLogList.init(this.params.id)
            });
            this.getOperateLogList()
        },
      
        
        getFollowDetail() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/deal/coupleBack/query/getFollowDetail",
                   self.params.id ,
                    (res) => {
                        if (res.body.result && res.body.content) {
                            // self.submitData = res.body.content;
                            let data = res.body.content;
                            self.submitData = Object.assign(self.submitData , data);
                             self.getFile();
                            if(!!data.representative_time){
                              self.currentMessageList.push({
                                reply_type:'representative',
                                create_time:data.representative_time,
                                reply_content:data.representative_content,
                                reply_name:data.representative_nmae,
                              })
                            }
                            if(!!data.processed_time){
                              self.currentMessageList.unshift({
                              reply_type:'processed',
                              create_time:data.processed_time,
                              reply_content:data.processed_content,
                              reply_name:data.processed_name,
                            })
                            }
                            self.getFollowList()
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        }, 
        
         getFile() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/deal/coupleBack/query/getFileList",
                   {"orderNo": self.submitData.order_no},
                    (res) => {
                        if (res.body.result && res.body.content) {
                            // self.submitData = res.body.content;
                            let list = res.body.content, fileList = []
                            this.createFileList = []
                            list.forEach(item=>{
                                item.path =  Fn.changeOssUrl(item.path);
                                if (item.from_type==='CREATE') {
                                    fileList.push(item)
                                }
                            })
                            this.fileList = list;
                            this.createFileList.push({
                                reply_type: 'submitWorkOrder',
                                reply_name: self.submitData.buyer_name,
                                create_time: self.submitData.question_submit_time,
                                reply_content: self.submitData.question_desp,
                                fileList: fileList
                            })
                            this.plugFileList=res.body.content.filter(item=>item.from_type==='PLUG')
                            this.formatFollowRecordInfoList();
                            this.getFollowList()
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        }, 
        formatFollowRecordInfoList(){
            let self = this;
            this.followRecordExtraInfoList = [];
            this.plugFileList.forEach(item => {
                let hasItem = this.followRecordExtraInfoList.find(sub => sub.create_time == item.create_time)
                if (hasItem) {
                    hasItem.fileList&&hasItem.fileList.push(item)
                } else {
                    let obj = {
                        reply_name: self.submitData.buyer_name,
                        create_time: item.create_time,
                        fileList: [item],
                        reply_type: 'furtherInformation'
                    }
                    this.followRecordExtraInfoList.push(obj)
                }
            })
            this.getFollowList()
        },
        getDetail() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/deal/coupleBack/query/getDetail",
                   self.params.id ,
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.submitData = res.body.content;
                            self.orderId = res.body.content.id;
                            self.removeBtnStatus()
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        //消息列表
        getFollowReplyDetail() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/deal/coupleBack/query/getFollowReplyDetail",
                    self.params.id ,
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.messageRecordList = res.body.content;
                            self.messageRecordTotal = res.body.content.length;
                            res.body.content.forEach(item=>{
                              item.reply_type = 'reply';
                            })
                            self.currentMessageList = res.body.content;
                            self.getFollowList()
                            self.messagePageNow = 1;
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        //处理进度
        getHandleLogList() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/deal/coupleBack/query/getHandleLogList",
                    self.params.id ,
                    (res) => {
                        if (res.body.result) {
                            self.handleLoglist = res.body.content;
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        deliver() {
            let self = this
            this.ajax.postStream(
                "/afterSale-web/api/deal/coupleBack/query/getPrincipalUserGropup", {},
                (res) => {
                    if (res.body.result) {
                        this.$root.eventHandle.$emit("alert", {
                            component:() => import('@components/per_sales_report/select_personel'),
                            style: 'width:800px;height:500px',
                            title: '人员列表',
                            params: {
                                callback: (data) => {
                                    self.confirmDeliver(data)
                                },
                                content: res.body.content
                            },
                        });
                    } else {
                        res.body.msg && self.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
        },
        confirmDeliver(data) {
            let self = this
            self.ajax.postStream(
                "/afterSale-web/api/deal/coupleBack/action/deliver?permissionCode=COUPLE_BACK_DELIVER",
                {ids:[self.params.id] ,type:'DELIVER',userId: data.userId,  userFullName: data.fullName},
                (res) => {
                    if (res.body.result) {
                        self.initDetail();
                        self.$message.success(res.body.msg);
                    } else {
                        res.body.msg && self.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
        },
         //锁定、解锁
        doLock(type) {
            let self = this;
            let code = type == 'UNLOCK'?'COUPLE_BACK_UNLOCK':'COUPLE_BACK_LOCK'
                this.ajax.postStream(
                    "/afterSale-web/api/deal/coupleBack/action/doLock?permissionCode="+code,
                    {ids:[self.params.id] ,type:type},
                    (res) => {
                        if (res.body.result) {
                            self.$message.success(res.body.msg);
                            self.initDetail();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                    }
                );
        },
      
       
       
       
        getFileList() {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                title: "查看附件",
                style: "width:900px;height:600px;",
                component: () =>
                    import("@components/after_sales/replyFeedbackAlert"),
                params: {
                    order_no: self.submitData.order_no,
                    fromBtn: "getFileList",
                    callback: () => {
                        self.$message.success("获取附件列表成功");
                    },
                },
            });
        },
        
    
        currentPageChange(p) {
            let allList = this.messageRecordList;
            let pageSize = this.messagePageSize;
            let messageStartIndex = (p - 1) * pageSize;
            let messageEndIndex = p * pageSize;
            this.messagePageNow = p;
            this.currentMessageList = allList.slice(
                messageStartIndex,
                messageEndIndex
            );
            this.getFollowList()
        },
        getFollowList() {
            let emptyArray = []
            let list = emptyArray.concat(this.currentMessageList, this.followRecordExtraInfoList, this.createFileList)
            
            list.sort((a,b) => {
                return b.create_time - a.create_time
            })
            this.followList = list
        },
        getEmployeeInfoGroupList(){
            //获取当前处理人的业务分组列表
            this.ajax.postStream('/user-web/api/userPerson/getUserPersonGroupList',{personId:this.getEmployeeInfo('personId')}, res=>{
                if(res.body.result && res.body.content){
                    let list = res.body.content.list||[];
                    this.ifOrderSupport = list.some(item => {
                        let ifDisabled = item.disableTime ? item.disableTime > new Date().getTime() : true
                        return item.salesmanType === 'ORDER_SUPPORT' && item.enableTime < new Date().getTime() && ifDisabled
                    })
                } else {
                    this.ifOrderSupport = false
                    res.body.msg && this.$message.error(res.body.msg);
                }
            }, err => {
                this.ifOrderSupport = false
                this.$message.error(err);
            });
        },
        openActualQuestionTypeDialog () {
            let self = this
            if (/^(FINISH)$/.test(this.submitData.order_status)) {
                this.$message.error('单据已完结不可修改实际问题类型')
                return
            }
            if (!this.ifOrderSupport) {
                this.$message.error('含有订单支持专员业务员类型的业务代理才有权限操作')
                return
            }
            let params = {
                afterOrderId: this.params.id,
                callback: data => {
                    self.initDetail()
                }
            }
            this.$root.eventHandle.$emit('alert', { 
                params,
                component: ()=>import('@components/after_sales/actualQuestionTypeDialog'),
                style: 'width:450px;height:250px',
                title: '实际问题类型',
            })
        },
        getRelevantOrders (){
			if(this.params.id){
				this.ajax.postStream('/afterSale-web/api/deal/coupleBack/query/getRelevantOrders', this.params.id, res => {
					if(res.body.result){
						this.downstreamDocument = res.body.content || []
					} else {
                        this.downstreamDocument = []
                    }
                }, err => {
                    this.downstreamDocument = []
                })
			}
		},
        viewOrderDetail(row) {
            let pageOtherObj = {
                '售后单': {'component': afterSaleIndex, 'current_tab_name': '售后单详情', 'id_name': 'after_order_id_'},
                '补件': {'component': supplyOrder, 'current_tab_name': '补件单详情'},
                '服务单': {'component': afterSalesService, 'current_tab_name': '服务单详情'},
                '退货单': {'component': returnExchangeDetail, 'current_tab_name': '新退货跟踪单详情'},
                '咨询单': {'component': afterSaleConsultation, 'current_tab_name': '咨询单详情'},
                '退款': {'component': refundRequest, 'current_tab_name': '新退款申请单', 'id_name': 'billRefundApplication_'}
            }
            if (/^(退款|售后单)$/.test(pageOtherObj[row.bill_type])) {
                this.blockSameIdOpenTab(row.bill_id, () => {
                    this.$root.eventHandle.$emit('creatTab', {
                        name: pageOtherObj[row.bill_type].current_tab_name,
                        params: {id: row.bill_id},
                        component: pageOtherObj[row.bill_type].component
                    });
                }, {
                    "tabName": pageOtherObj[row.bill_type].current_tab_name,
                    "idName": pageOtherObj[row.bill_type].id_name
                })
            } else {
                this.$root.eventHandle.$emit('creatTab', {
                    name: pageOtherObj[row.bill_type].current_tab_name,
                    params: {id: row.bill_id},
                    component: pageOtherObj[row.bill_type].component
                });
            }
            
        },
        blockSameIdOpenTab (id, cb, tabObj){// 打开窗口订单
			if(!cb){//set className
				this.$el.setAttribute('data-symbol', tabObj.idName + id)
			}else {//检测是否已打开相同退款申请单
                
				if(document.querySelector(`[data-symbol="${tabObj.idName}${id}"]`)){
					this.$message.error(`该${tabObj.tabName}已经打开`)
				}else {
					cb()
				}
			}
		},
    },
    components: {
        qualityFeedbackFollowReplyList,
        handleLogList,
    },
};
</script>
<style scoped>
.el-input,
.el-select,
.el-date-editor.el-input {
    width: 196px;
}
.record-list {
    flex: 1;
}
.mgt10 {
    margin-top: 10px;
}
.ptb2 {
    padding: 10px 0px;
}
.detail-form {
    height: 100%;
    display: flex;
    flex-direction: column;
}
.message-record {
    display: flex;
    flex-direction: column;
}
.message-list {
    position: absolute;
    top: 30px;
    bottom: 0;
    left: 0;
    right: 0;
}
.nowrap {
    white-space: nowrap;
}
</style>
<style>
.quality-feedback .xpt-flex__bottom .el-tabs .el-tabs__content {
    overflow: hidden !important;
}
</style>