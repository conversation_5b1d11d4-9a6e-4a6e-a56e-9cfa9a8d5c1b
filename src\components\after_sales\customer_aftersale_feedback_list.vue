<!-- 客户售后反馈单列表 -->
<template>
    <xpt-list-dynamic
        :data='list'
        :colData='cols'
        :btns='btns'
        :selection='selection'
        :pageTotal='pageTotal'
        :searchPage='serchData.page_name'
		    :showCount ='showCount'
        @count-off="countOff"
        @search-click='search'
        @selection-change='selects'
        @page-size-change='pageChange'
        @current-page-change='currentPageChange'
    ></xpt-list-dynamic>
   
</template>
<script>
  import Fn from '@common/Fn.js'
  export default{
    props: ['params'],
    data(){
      let self=this;
      return{
        countOffFlag:false,
			  showCount:false,
        serchData:{
          page_name:"aftersale_deal_couple_back",
          where:[],
          page_size: self.pageSize,     //页数
          page_no:1   //页码
        },
        isReSaleOrderList: false,
        showHead:false,
        pageNow:1,
        pageTotal:0,
        btns:[{
          isBtnGroup:false,
          type: 'success',
          txt: '刷新',
          click: self.refresh,
        },{
          
          type: 'primary',
          txt: '锁定',
          click:  ()=>{
            self.doLock('LOCK')
          },
        },{
         
          type: 'primary',
          txt: '解锁',
          click: ()=>{
            self.doLock('UNLOCK')
          },
        },{
          isBtnGroup:false,
          type: 'primary',
          txt: '转交',
          click: self.deliver,
        },
        {
            type: "success",
            txt: "导出",
            disable(){
              return false
            },
            click: self.exportBillRefundApp
        }, {
            type: "success",
            txt: "报表导出文件下载",
            click: self.showExportList
        }
        ],
        list:[],
        cols:[
          /*{
            label: '序号',
            prop: '__index',
            width: 50,
          },*/
          {
            label: '单据编号',
            width:'180',
            prop: 'order_no',
            redirectClick(row) {
              self.viewDetail(row.id)
            }
          },{
            label: '店铺名称',
            width:'180',
            prop: 'original_shop_name'
          },{
            label: '所属经销商',
            width:180,
            prop: 'dealer_customer_name'
          },{
            label: '事业部',
            width: 100,
            prop: 'business_department',
            format: "auxFormat",
            formatParams: "business_division",
          },{
            label: '批次单号',
            width:200,
            prop: 'batch_trade_no'
          },
          {
            label: '问题商品编码',
            width:'120',
            prop: 'material_code'
          },{
            label: '问题商品名称',
            width:'140',
            prop: 'material_name'
          },{
            label: '问题商品规格',
            width:'150',
            prop: 'material_spec',
          },{
            label: '问题商品项目组', 
            width:'100',
            prop: 'project_team',
          },{
            label: '售后问题类型',
            width:'150',
            prop: 'question_type',
            formatter(val){
              switch(val){
                case 'StoreServiceProblems':return '门店服务问题' ; break;
                case 'CommodityProblems':return '商品问题' ; break;
                case 'LogisticsProblems':return '物流问题' ; break;
                case 'InstallationProblems':return '安装问题' ; break;
              }
            }
            
          },{
            label: '问题描述',
            width:'100',
            prop: 'question_desp',
          },{
            label: '买家昵称',
            width:'100',
            prop: 'buyer_name'
          },{
            label: '联系电话',
            width:'90',
            prop: 'phone'
          },{
            label: '提交时间',
            width:130,
            prop: 'question_submit_time',
            format: 'dataFormat1',
          },{
            label: '单据状态',
            // width:'140',
            prop: 'order_status',
            formatter(val){
              switch(val){
                case 'WAIT':return '待处理' ; break;
                case 'ING':return '处理中' ; break;
                case 'OVER':return '处理完成' ; break;
                case 'FINISH':return '已完结' ; break;
                case 'CLOSED':return '已关闭' ; break;
              }
            }
          },{
            label: '业务员',
            width:'90',
            prop: 'staff_name',
          },{
            label: '锁定人',
            width:'130',
            prop: 'locker_name'
          },{
            label: '锁定时间',
            prop: 'locker_time',
            width:130,
            format: 'dataFormat1',
          },{
            label: '超时处理时间',
            width:130,
            format: 'dataFormat1',
            prop: 'timeout_process_time'
          },{
            label: '是否超时未处理',
            prop: 'if_timeout',
            formatter(val){
              switch(val){
                case 'Y':return '是' ; break;
                case 'N':return '否' ; break;
              }
            }
          },{
            label: '受理时间',
            prop: 'representative_time',
            format: 'dataFormat',
            width:130,
            format: 'dataFormat1',
          },{
            label: '处理完成时间',
            prop: 'processed_time',
            width:130,
            format: 'dataFormat1',
          },{
            label: '完结时间',
            prop: 'finish_time',
            width:130,
            format: 'dataFormat1',
          },{
            label: '关闭时间',
            width:130,
            format: 'dataFormat1',
            prop: 'close_time'
          },{
            label: '资料状态',
            prop: 'if_need_plug',
            formatter(val){
              switch(val){
                case 'NONEED':return '无需补充' ; break;
                case 'NEED':return '需要补充' ; break;
                case 'PLUGOVER':return '补充完毕' ; break;
              }
            }
          },{
            label: '评价状态',
            width:'160',
            prop: 'appraise_staus',
            formatter(val){
              switch(val){
                case 'NOAPPRAISE':return '未评价' ; break;
                case 'APPRAISEED':return '已评价' ; break;
              }
            }
          },{
            label: '评价',
            width:'200',
            prop: 'appraise_score'
          }
        ],
        // searchObj: {
        //   page_size: self.pageSize,
        //   page_no: 1,
        //   page_name: 'aftersale_order',
        //   where: []
        // },
        selectsData:[],
        selection: 'checkbox', 
      }
    },
    methods:{
        exportBillRefundApp() {
          if (this.serchData.where.length == 0) {
            this.$message.error('导出必须添加查询条件')
            return
          }
            let url = "/reports-web/api/reports/afterSaleExport/exportAftersaleDealCoupleBack";
            let postData = Object.assign({}, this.serchData);
            this.ajax.postStream(url, postData, res => {
                if (res.body.result) {
                    this.$message.success(res.body.msg);
                } else {
                    this.$message.error(res.body.msg);
                }
            });
        },
        showExportList (){
            this.$root.eventHandle.$emit('alert', {
                component: () => import('@components/after_sales_report/export'),
                style:'width:900px;height:600px',
                title: '报表导出列表',
                params: {
                    query: {
                        type: 'EXCEL_TYPE_AFTER_DEAL_COUPLE_BACK', 
                    },
                },
            })
        },
     
      // 多选数据
      selects(val) {
        this.selectsData = val
      },
      // 刷新
      refresh() {
        var self = this;
        self._getList();
      },
      
      viewDetail (id){
        var params = {
          id,
        };
          this.$root.eventHandle.$emit('creatTab', {
            name:"客户售后反馈单详情",
            params:params,
            component: () => import('@components/after_sales/customer_aftersale_feedback_detail')
          });
        
      },
      //获取参数的type_id
      getIds (){
        var self=this;
        var resList=[];
        if(self.selectsData&&self.selectsData.length>0){
          for(var i=0;i<self.selectsData.length;i++){
            resList.push(self.selectsData[i].id);
          }
        }
        return resList;
      },
        deliver() {
          let self = this
            let arr = self.getIds();
            if(!arr.length){
              self.$message.error('请选择要操作的数据！')
              return;
            }
          let params = {
            callback(data) {
              // self.query.staff_id = data.userId;
              // self.query.staff = data.fullName;
              self.ajax.postStream(
                   "/afterSale-web/api/deal/coupleBack/action/deliver?permissionCode=COUPLE_BACK_DELIVER",
                    {ids:arr ,type:'DELIVER',userId: data.userId,  userFullName: data.fullName},
                    (res) => {
                        if (res.body.result) {
                            self.refresh();
                            self.$message.success(res.body.msg);
                        } else {
                            res.body.msg && self.$message.error(res.body.msg);
                        }
                    },
                    (err) => {
                        self.$message.error(err);
                    }
                );
            },
            // salesmanType:  'DELIVER',
          }
          this.$root.eventHandle.$emit('alert', {
            params: params,
            component:() => import('@components/per_sales_report/select_personel'),
            style: 'width:800px;height:500px',
            title: '人员列表'
          })
        },
         //锁定、解锁
        doLock(type) {
            let self = this;
            let arr = self.getIds();
            if(!arr.length){
              self.$message.error('请选择要操作的数据！')
              return;
            }
            let code = type == 'UNLOCK'?'COUPLE_BACK_UNLOCK':'COUPLE_BACK_LOCK';
            this.ajax.postStream(
               "/afterSale-web/api/deal/coupleBack/action/doLock?permissionCode="+code,
                {ids:arr ,type:type},
                (res) => {
                    if (res.body.result) {
                      self.$message.success(res.body.msg);
                      self.refresh();
                    } else {
                        res.body.msg && this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
      

      // 监听每页显示数更改事件
      pageChange(pageSize){
        this.serchData.page_size = pageSize
        this.serchData.page_no = 1
        this._getList();
      },
      // 监听页数更改事件
      currentPageChange(page){
        this.pageNow = page;

        this.serchData.page_no = page;
        this._getList();
      },
      // 搜索
      search(list, resolve) {
        // do something..
        var self = this;
        self.serchData.where=list;
        // self._getList(resolve);
        new Promise((res,rej)=>{
				  this._getList(resolve,null,res);
        }).then(()=>{
          if(self.serchData.page_no != 1){
            self.pageTotal = 0;
          }
            self.showCount = false;
        })
      },
      countOff(){

        let self = this;
        let url = '/afterSale-web/api/deal/coupleBack/query/count'

        if(!self.list.length){
          self.$message.error("当前列表为空，先搜索内容");
          return;
        }
        if(!!self.countOffFlag){
          self.$message.error("请勿重复点击");
          return;
        }
        self.countOffFlag = true;

        self.ajax.postStream(url,self.serchData,function(response){
            if(response.body.result){
              
              self.pageTotal = response.body.content.count;
              self.showCount = true;
              self.countOffFlag = false;

            }else{
              self.$message.error(response.body.msg);
            }
          });
      },
      //查询列表
      _getList(resolve, msg,resolve2){
      
        var self=this;
        // console.log('看看是什么东西');
        // let isDealerMenu =this.__judgeIsDealerMenu(this.params.menuInfo);
        let url =  '/afterSale-web/api/deal/coupleBack/query/list'

        self.ajax.postStream(url, this.serchData, function(d){
          if(d.body.result){
             let dataList = JSON.parse(JSON.stringify(d.body.content.list));
              if(dataList.length == (self.serchData.page_size+1)&&dataList.length>0){
                dataList.pop();
              }
              self.list = dataList;
              let totalCount = self.serchData.page_no*self.serchData.page_size;
              if(!self.showCount){
                self.pageTotal = d.body.content.count == (self.serchData.page_size+1)? totalCount+1:totalCount;
              }



            self.$message.success(msg || d.body.msg)
          }else{
            self.list=[];
            self.pageTotal=0;
            self.$message.error(d.body.msg)
          }
          resolve && resolve();
          resolve2 && resolve2();
          self.selectRole = [];
        }, err => {
          resolve && resolve();
          resolve2 && resolve2();
        }, self.params.tabName)
      },
     
      
    },
    mounted:function(){
     
     
      // this._getList()
    },
  }
</script>
