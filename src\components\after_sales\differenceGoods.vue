<!-- 差价商品 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type="primary" size="mini" @click='addFun' :disabled="!params.isEdit">新增商品</el-button>
				<el-button type="danger" size="mini" @click='delFun' :disabled="!params.isEdit">删除行</el-button>
				<el-button type="success" size="mini" @click='combination' :disabled="!params.isEdit">组合</el-button>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom">
			<el-table 
				border 
				:data="list.length ? list.concat('') : list" 
				tooltip-effect="dark" 
				width='100%' 
				style="width: 100%;"
				@selection-change="handleSelectionChange"
				:row-class-name="(row, index) => index < list.length ? tableRowClassNameRepair(row, index) : $style['only-show-total']"

				@row-click="itemRowClick"
			>
			    <el-table-column type="selection" width="50" :selectable="(row, index) => index < list.length"></el-table-column>
				<el-table-column type="index" width="50" label="序号">
					<template slot-scope="scope">{{ scope.$index < list.length ? scope.$index + 1 : '' }}</template>
				</el-table-column>
				<el-table-column label="价格区间" width="200">
					<template slot-scope="scope">
						<el-input v-if="scope.$index < list.length" size="mini" :value="scope.row.price_list_name" style="width:100%" icon="search" @click.native="() => params.isEdit ? priceFun(scope.row) : f => f" readonly></el-input>
					</template>
				</el-table-column>
				<el-table-column label="店铺" prop="price_shop_name" width="230" show-overflow-tooltip></el-table-column>
				<el-table-column label="活动商品编码" prop="price_material_no" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="活动商品名称" prop="price_material_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="活动规格描述" prop="price_material_desc" width="200" align="right" header-align="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="商品编码" prop="material_number" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="商品名称" prop="material_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="规格描述" prop="material_desc" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="拍下价格" prop="buying_price">
					<template slot-scope="scope">
						<span v-if="scope.$index < list.length">{{ scope.row.buying_price }}</span>
						<span v-else>合计 {{ allBuying_price }}</span>
					</template>
				</el-table-column>
				<el-table-column label="活动价" prop="act_price">
					<template slot-scope="scope">
						<span v-if="scope.$index < list.length">{{ scope.row.act_price }}</span>
						<span v-else>合计 {{ allAct_price }}</span>
					</template>
				</el-table-column>
				<el-table-column label="淘宝单号" prop="tid" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="订单状态" prop="order_status">
					<template slot-scope="scope">
						<span>{{status[scope.row.order_status]}}</span>
					</template>
				</el-table-column>
				<el-table-column label="差价" prop="price_spread">
					<template slot-scope="scope">
						<span v-if="scope.$index < list.length">{{ scope.row.price_spread }}</span>
						<span v-else>合计 {{ allPrice_spread }}</span>
					</template>
				</el-table-column>
				<el-table-column label="购买时间" prop="buy_time" width="170" show-overflow-tooltip>
					<template slot-scope="scope">
						<span>{{scope.row.buy_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<el-table-column label="单位" prop="unit_name"></el-table-column>
				<el-table-column label="生效日期" width="170" show-overflow-tooltip>
					<template slot-scope="scope">
						<span>{{scope.row.enable_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<el-table-column label="失效日期" width="170" show-overflow-tooltip>
					<template slot-scope="scope">
						<span>{{scope.row.disable_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<!-- <el-table-column label="备注" >
					<template slot-scope="scope">
						<el-input size="mini" v-model="scope.row.description"  style="width:100%"></el-input>
					</template>
				</el-table-column> -->
			</el-table>
			<!-- <table style="margin-top:10px;width:100%;" v-show="form.return_amount">
				<tr>
					<td width="55"></td>
					<td width="150"></td>
					<td width="350"></td>
					<td></td>
					<td width="300">合计 {{ form.return_amount }}</td>
				</tr>
			</table> -->
		</el-row>
	</div>
</template>
<script>
	import Fn  from '@common/Fn.js'
	export default {
		props: ['params', 'initData'],
		data() {
			return {
				list: [],
				selectArr: [],
				list_material_id: [],
				allBuying_price: 0,//拍下价格合计
				allAct_price: 0,//活动价合计
				allPrice_spread: 0,//差价合计
				keys: [],
				plan_time: null,
				status:{
					'CANCEL': '取消',
					'NORMAL': '生效',
					'WAITING': '等待发运',
					'PART_DELIVERED': '部分发运中',
					'DELIVERED': '已发运'
				}

			}
		},
		methods: {
			// 计算所有合计
			_calcAllPrice (){
				var _allAct_price = 0
				,	_allBuying_price = 0
				,	_allPrice_spread = 0

				this.list.forEach(obj => {
					_allAct_price += Number(obj.act_price)
					_allPrice_spread += Number(obj.price_spread)
					_allBuying_price += Number(obj.buying_price)
				})

				this.allAct_price = _allAct_price
				this.allPrice_spread = _allPrice_spread
				this.allBuying_price = _allBuying_price

				this.$emit('getDifferenceGoods', this.list, Number(this.allPrice_spread.toFixed(2)))//返回父组件差价合计和新增商品
			},
			tableRowClassNameRepair(row, rowIndex) {
				let id = this.params.copySelectDataId
				if(!id) return '';

				let question_sub_id = row.question_sub_id;

				if(question_sub_id == id) return 'pmm_blue22';
				return '';
			},
			addFun() {
				this.$emit('getMergeTradeNo', merge_trade_no => {
					var params = {
						merge_trade_no,
						// merge_trade_no: 'HB20170615141035001170',
					};
					params.callback = d => {
						console.log(d,"d")
						d.forEach(v => {
							var obj = {
								key: new Date().getTime() + Math.random() * 100,
								price_list_id: null,//价目表id		Long（需要传）
								price_list_name: null,//价目表名称		String（需要传）
								price_shop_id: null,//活动店铺id	Long（需要传）
								price_material_id: null,//活动商品id	Long（需要传）
								price_material_no: null,//活动商品编码		String（需要传）
								price_material_name: null,//活动商品名称	String（需要传）
								price_material_desc: null,//活动商品描述	String（需要传）
								material_id: v.materia_id,//商品id		Long（需要传）
								material_number: v.materia_number,//商品编码	String（需要传）
								material_name: v.materia_name,//商品名字		String（需要传）
								material_desc: v.materia_specifications,//规格描述		String（需要传）
								// unit: v.unit_id,//单位		String（需要传）
								unit_name: v.unit_name,//单位		String（需要传）
								act_price: null,//活动价	BigDecimal（需要传）
								buying_price: v.act_price,//拍下价格	BigDecimal（需要传）
								price_spread: null,//差价		BigDecimal（需要传）
								description: null,//备注		String（需要传）
								tid: v.tid,//淘宝单号	String（需要传）
								order_status: v.status,//订单状态	String（需要传）
								buy_time: v.created,//购买时间	Date（需要传）
								enable_time: null,//生效日期	Date（需要传）
								disable_time: null,//失效日期	Date（需要传）
								combo_price: null,//组合价	BigDecimal（需要传）
							}
							this.list.push(obj);
						})
					};
					this.$root.eventHandle.$emit('alert',{
						params:params,
						component:()=>import('@components/after_sales_common/selectDifferencePriceGoods'),
						style:'width:1000px;height:600px',
						title:'差价商品列表'
					});
				})
			},
			delFun() {
				if(this.selectArr == 0){
					this.$message.error('请选择删除的行');
					return;
				}
				this.list = this.list.filter((v) => {
					var bool = true;
					this.selectArr.forEach((v1) => {
						if(v.key === v1.key) {
							bool = false;
						}
					})
					return bool;
				})
			},
			combination() {
				if(this.selectArr && this.selectArr.length  == 0) {
					this.$message.error('请选择要组合的商品');
					return;
				}
				var params = {
					singleShotDate: this.plan_time,
					list_material_id: this.list_material_id
				}
				console.log('params',params,this.list_material_id)
				params.callback = d => {
					var obj = {};
					obj.price_list_id= d.price_list_id;//价目表id		Long（需要传）
					obj.price_list_name= d.price_list_name;//价目表名称		String（需要传）
					obj.price_shop_id= d.shop_id;//活动店铺id	Long（需要传）
					obj.price_shop_name= d.shop_name;//活动店铺名称	Long（需要传）
					obj.price_material_id= d.material_id;//活动商品id	Long（需要传）
					obj.price_material_no= d.material_code;//活动商品编码		String（需要传）
					obj.price_material_name= d.material_name;//活动商品名称	String（需要传）
					obj.price_material_desc= d.material_specification;//活动商品描述	String（需要传）
					obj.enable_time= d.enable_date;//生效日期	Date（需要传）
					obj.disable_time= d.disable_date;//失效日期	Date（需要传
					obj.combo_price = d.price;

					var groupPrice = 0;
					var groupList = [];
					var icon = new Date().getTime();
					this.selectArr.forEach(v => {
						groupPrice = parseFloat(groupPrice) + parseFloat(v.buying_price);
					})
					//组合成功后，对列表进行合并
					this.selectArr.forEach(v => {
						var groupObj = Object.assign(v,obj);
						groupObj.combo_no_string = icon;
						//价格计算，计算规则看文档
						groupObj.act_price = Fn.number((parseFloat(groupObj.combo_price) * parseFloat(v.buying_price))/parseFloat(groupPrice));
						groupObj.price_spread = Fn.number(parseFloat(groupObj.buying_price) - parseFloat(groupObj.act_price));
						groupList.push(groupObj);
					})

					var otherList = this.list.filter(v => {
						return this.keys.toString().indexOf(v.key.toString()) < 0
					})

					this.list = [...groupList,...otherList];

				}
				this.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/after_sales/groupGoodsPrice'),
					style:'width:1000px;height:600px',
					title:'组合商品价目表列表'
				});

			},
			itemRowClick(row, event, column){
				this.$emit('selectGoodsmatchQuestions', row)
			},
			handleSelectionChange(selectArr){
				this.selectArr = selectArr;
				this.list_material_id = [];
				this.keys = [];
				selectArr.forEach(v => {
					console.log('v.material_id',v.material_id)
					this.list_material_id.push(v.material_id);
					this.keys.push(v.key);
				})
			},
			priceFun(row) {
				var params = {
					material_id: row.material_id,
					singleShotDate: row.buy_time
				}
				params.callback = d => {

					row.price_list_id= d.price_list_id;//价目表id		Long（需要传）
					row.price_list_name= d.price_list_name;//价目表名称		String（需要传）
					row.price_shop_id= d.shop_id;//活动店铺id	Long（需要传）
					row.price_shop_name= d.shop_name;//活动店铺名称	Long（需要传）
					row.price_material_id= d.material_id;//活动商品id	Long（需要传）
					row.price_material_no= d.material_code;//活动商品编码		String（需要传）
					row.price_material_name= d.material_name;//活动商品名称	String（需要传）
					row.price_material_desc= d.material_specification;//活动商品描述	String（需要传）
					row.enable_time= d.enable_date;//生效日期	Date（需要传）
					row.disable_time= d.disable_date;//失效日期	Date（需要传
					row.act_price = d.price;//活动价	BigDecimal（需要传
					row.price_spread = parseFloat(row.buying_price) - parseFloat(d.price);//差价		BigDecimal（需要传）

					this._calcAllPrice()
				}
				this.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/after_sales_common/selectListPrice'),
					style:'width:1000px;height:600px',
					title:'差价商品价目表列表'
				});
			},
			returnData (){

			},
		},
		watch: {
			list (){
				this._calcAllPrice()
			},
			initData: function (val){
				this.list = [].concat(val)
				// this._calcAllPrice(true)
			},
			// 'params.refundPriceDiffList': function (val){
			// 	console.log()
			// 	this.list = val
			// },
			// 'params.plan_time':function(val){
			// 	this.plan_time = val || new Date().getTime();
			// },
			// 'params.ifSave':function(){
			// 	var list =[];
			// 	this.list.forEach((v) => {
			// 		if(v.key){
			// 			delete v.ifEdit;
			// 			delete v.key;
			// 			delete v.price_shop_name;
			// 		}
			// 		list.push(v);

			// 	});
			// 	this.$emit("getDifferenceGoods", list);
			// },
			// 'data':{
			//         handler: function (list) {this.list = list},
			//         deep: true
			//     }


		},
		mounted() {
			this.plan_time = this.params.plan_time;
		},
		destroyed() {
		}
	}
</script>

<style module>
	.only-show-total :global(.el-checkbox) {
		display: none;
	}
</style>

