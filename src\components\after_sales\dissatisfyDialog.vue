<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='primary' size='mini' @click="confirm" :loading="isLoading">提交</el-button>
      </el-col>
    </el-row>
    <el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px">
      <el-col :span="12" style="width: 100%">
        <el-form-item label="不满意标签" prop="consultion_no_satisfaction_tag">
          <xpt-select-aux
            showRemark
            v-model="form.consultion_no_satisfaction_tag"
            aux_name='ZXDPJBMYBQ'
            :disabledOption="[]"
            ref="discontentTagSelect"
          ></xpt-select-aux>
          <el-tooltip v-if='rules.consultion_no_satisfaction_tag[0].isShow' class="item" effect="dark" :content="rules.consultion_no_satisfaction_tag[0].message" placement="right-start" popper-class='xpt-form__error'>
             <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="不满意备注" prop="consultion_no_satisfaction_remark">
          <el-input size='mini' v-model="form.consultion_no_satisfaction_remark" :maxlength="200"></el-input>
        </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>

<script>
import VL from '@common/validate.js'
export default {
  props:["params"],
  data(){
    let self = this;
    return {
      isLoading: false,
      form: {
        consultion_no_satisfaction_tag: '',
        consultionId: '',
        consultion_no_satisfaction_remark: ''
      },
      rules: {
        consultion_no_satisfaction_tag: VL.isNotBlank({
					required: true,
					self: this,
					msg: '请选择不满意标签',
				}),
      },
      isRequiredObj: __AUX.get('ZXDPJBMYBQ').reduce((obj, item) => {
        obj[item.code] = {
          isRequired: item.tag === 'Y',
          name: item.name
        }
        return obj
      }, {}),
    }
  },
  methods:{
    confirm(){
      if (this.isRequiredObj[this.form.consultion_no_satisfaction_tag].isRequired && !this.form.consultion_no_satisfaction_remark) {
        this.$message.error(`不满意标签为【${this.isRequiredObj[this.form.consultion_no_satisfaction_tag].name}】时，不满意备注为必填项`)
        return
      }
      if (this.isLoading) return
      this.$refs.form.validate((valid) => {
        if(!valid) return
        let postData = {}
        if (Array.isArray(this.params.consultionId)&&this.params.consultionId.length > 0) {
          postData = {
            consultion_ids: this.params.consultionId,
            consultion_no_satisfaction_tag: this.form.consultion_no_satisfaction_tag,
            consultion_no_satisfaction_remark: this.form.consultion_no_satisfaction_remark,
            consultion_satisfaction: 'N'
          }
        } else {
          postData = {
            consultion_id: this.params.consultionId,
            consultion_no_satisfaction_tag: this.form.consultion_no_satisfaction_tag,
            consultion_no_satisfaction_remark: this.form.consultion_no_satisfaction_remark,
            consultion_satisfaction: 'N'
          }
        }


        this.isLoading = true
        this.ajax.postStream('/afterSale-web/api/aftersale/consultion/manyCommitSatisfactionToZd', postData, (res)=> {
          if (res.body.result) {
            this.$message.success(res.body.msg)
            this.params.callback()
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
          } else {
            this.$message.error(res.body.msg)
          }
          this.isLoading = false
        })
      })
    },
    cancel(){
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    },
    changeCondition(){
      this.form.receiver_no = ''
      this.form.receiver_names = ''
    },
  },
  mounted(){
    this.form.consultionId = this.params.consultionId
  }
}
</script>

<style scoped>

</style>
