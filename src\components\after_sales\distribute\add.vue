<!-- 新增ABC配置人员 -->
<template>
	<div>
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type='primary' size='mini' @click="save('submitData')">保存</el-button >
			</el-col>
		</el-row>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基础信息" name="orderInfo">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="submitData"  ref="submitData">


						<el-col :span='8'>

							<el-form-item label="售后人员" prop="customer_service_abc">
								<xpt-input size='mini' v-if="!submitData.id"  v-model="submitData.customer_service_abc" @change="changeName" readonly	:on-icon-click="getName" :disabled="!!submitData.id" icon='search'></xpt-input>
								<el-input size='mini' v-else v-model="submitData.customer_service_abc" disabled icon='search'></el-input>

							</el-form-item>
                            <el-form-item label="是否生效" prop="status">
								<el-select placeholder="请选择" v-model="submitData.status"  size='mini' style="width:100%;">
                                    <el-option  label="是" :value="1"></el-option>
                                    <el-option  label="否" :value='0'></el-option>
                                </el-select>
							</el-form-item>

						</el-col>
                        <el-col :span='8'>

							<el-form-item label="生效时间" prop="enable_time">
								<el-date-picker
                                    v-model="submitData.enable_time"
									:picker-options='beginDateOptions'
                                    type="datetime"
                                    size="mini"
                                ></el-date-picker>
							</el-form-item>
                            <el-form-item label="失效时间" prop="disable_time">
								<el-date-picker
                                    v-model="submitData.disable_time"
									:picker-options='endDateOptions'
                                    type="datetime"
                                    size="mini"
                                ></el-date-picker>
							</el-form-item>

						</el-col>


			    </el-form>
			  </el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js';
	import Fn from '@common/Fn.js';
	export default {
		props:["params"],
		data() {
			var _this = this;
			var self = this;
			return{
				firstTab:"orderInfo",
				bill_type_id_tag: '',
				provinceObj:{},
				cityObj:{},
				areaObj:{},
				streetObj:{},
				submitData:{
					disable_time:"",//
					enable_time:"",//
					user_id:"",//
					customer_service_abc:"",//
					status:"",//
					id:null
				},
				endDateOptions:{
					disabledDate(time) {
						// 设置结束时间的失效时间为小于开始时间
						return time.getTime() < Date.now() - 8.64e7;
					}
				},
               beginDateOptions : {
					disabledDate(time) {
						return time.getTime() < Date.now() - 8.64e7;
					}
				},


			}
		},
		methods : {

           changeName(){
			    if(!!this.submitData.id){
				   return false;
			   }
			   	this.submitData.user_id = ''
				this.submitData.customer_service_abc = '';
		   },
           getName(){
			   if(!!this.submitData.id){
				   return false;
			   }
			   this.$root.eventHandle.$emit('alert',{
					title: '业务员列表',
					style:'width:800px;height:600px',
				  component: () => import('@components/after_sales/distribute/selectRecommendList.vue'),
					params: {
            isSelect: true,
						callback: d => {
							this.submitData.user_id = d.userId
							this.submitData.customer_service_abc = d.fullName;
						}
					},
				})
		   },

            save() {
                let data = {},
					self = this;
					// console.log(this.submitData);
					if(!self.submitData.disable_time){
						self.submitData.disable_time = '';
					}
					if(!self.submitData.enable_time){
						self.submitData.enable_time = '';
					}
					let url = self.submitData.id?'/afterSale-web/api/aftersale/distribute/update?permissionCode=UPDATE_ABC_USER': '/afterSale-web/api/aftersale/distribute/save?permissionCode=ADD_ABC_USER'
                this.ajax.postStream(url ,self.submitData.id?[self.submitData]:self.submitData, (d) => {
                    if (d.body.result) {
                        this.$message.success(d.body.msg)
                        self.close();
                    } else {
                        this.$message.error(d.body.msg)
                    }

                }, err => {
                    this.$message.error(err);
                });
            },
            close(){
                this.params.callback();
			    this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
            }
		},
		mounted:function(){
			var self = this;
			if(self.params.row){
				self.submitData = self.params.row;
			}
		},


	}
</script>
