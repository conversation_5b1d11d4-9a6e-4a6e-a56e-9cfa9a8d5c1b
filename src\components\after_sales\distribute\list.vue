<!-- ABC自动分单设置列表 -->
<template>
    <!-- <xpt-list3 ref='list'
        id='distributeList'
        :data='distributeList'
        :btns='distributeBtns'
        :colData='distributeCols'
        orderNo
        isNeedClickEvent
        :pageTotal='distributeCount'
        searchPage='distributeQuery.page_name'
        :selection='selection'
        @selection-change='select'
        @radio-change="select"
        @search-click='distributeListSearch'
        @page-size-change='distributePageSizeChange'
        @current-page-change='distributePageChange'
       >
    </xpt-list3> -->

    <xpt-list-dynamic ref='list'
		id='distributeList'
        :data='distributeList'
        :btns='distributeBtns'
        :colData='distributeCols'
        orderNo
        isNeedClickEvent
        :pageTotal='distributeCount'
        :searchPage='distributeQuery.page_name'
        :selection='selection'
        @selection-change='select'
        @radio-change="select"
        @search-click='distributeListSearch'
        @page-size-change='distributePageSizeChange'
        @current-page-change='distributePageChange'
	></xpt-list-dynamic>
</template>
<script>
    import fn from '@common/Fn.js'

    export default {
		props:["params"],
        data() {
            let self = this;
            return {
                selection:self.params.isSelect?'radio':'checkbox',
                // selection:'radio',
                distributeList: [],
                distributeBtns: [{
                    type: 'success',
                    txt: '刷新',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.refresh()
                    }
                    }, {
                        type: 'primary',
                        txt: '新增',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.add()
                        }
                    }, {
                        type: 'danger',
                        txt: '删除',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.delete()
                        }
                    }],
                distributeCols: [{
                        label: '售后ABC人员姓名',
                        width: 190,
                        prop: 'customer_service_abc',
                        redirectClick(row) {
                            if(self.params.isSelect) return false;
                            self.viewDetail(row)
                        },
                    }, {
                        label: '生效时间',
                        width: 190,
                        prop: 'enable_time',
                        bool: true,
                        elDate: true,
                        date: 'date',

                        disabled(row) {
                            return true;
                            let flag = true;
                            self.selectList.forEach(item=>{
                                if(row.id == item.id){
                                    flag = false;
                                };
                            });
                            return flag;
                        },
                        change(row) {},

                    },


                    {
                        label: '失效时间',
                        prop: 'disable_time',
                        bool: true,
                        elDate: true,
                        date: 'date',
                        disabled(row) {
                            return true;
                             let flag = true;
                            self.selectList.forEach(item=>{
                                if(row.id == item.id){
                                    flag = false;
                                };
                            });
                            return flag;
                        },
                        change(row) {
                            console.log(self.selectList);
                            return false;
                        },

                        width: 190,
                    }, {
                        label: '是否生效',
                        prop: 'status',
                        formatter(val){
                            switch(val){
                                case 0:return '否'; break;
                                case 1:return '是'; break;

                            }
                        },
                        width: 120
                    }, {
                        label: '创建人',
                        prop: 'create_name'
                    }, {
                        label: '创建时间',
                        prop: 'create_time',
                        format: 'dataFormat1',
                        width: 130
                    }, {
                        label: '修改人姓名',
                        prop: 'modify_name'
                    }, {
                        label: '修改时间',
                        prop: 'modify_time',
                        format: 'dataFormat1',
                        width: 130
                    }
                ],
                distributeCount: 0,
                distributeQuery: {
                    page_name: "aftersale_distribute",
                    where: [],
                   page_size:50,
                   page_no:1
                },
                ifSave: false,
                selectList:[],
            }
        },
        methods: {
            save(){
                if(!this.selectList){
                    this.$message.error('请选择业务员')
                    return false;
                }
                console.log(this.selectList);
                this.params.callback(this.selectList);
            },
            select(list){
                console.log(list);
                this.selectList = list;
            },
            delete(){
                let self = this;
                let data = [];
                self.selectList.map((item)=>{data.push(item.id) })
                console.log(data);
                let url = '/afterSale-web/api/aftersale/distribute/delete?permissionCode=DELETE_ABC_USER'
                this.ajax.postStream(url, data, d => {
                    if (d.body.result) {
                        this.$message.success(d.body.msg);
                        self.refresh()
                    }else{
                        this.$message.error(d.body.msg);

                    }
                }, (e) => {
                    this.$message.error(e);
                    resolve && resolve();
                })
            },
            viewDetail(row){
               let self = this;
                let params = {};
                params.row = row;
                params.callback=(d)=>{
            		self.refresh();
                }
                this.$root.eventHandle.$emit('alert',{
                    close: function(){},
        			style:'width:900px;height:600px',
                    title:"修改售后人员",
                    params:params,
                    component: () => import('@components/after_sales/distribute/add.vue')
                });
            },
            add(row,resolve){
               let self = this;
               var data = {type:1};
          		data.callback=(d)=>{
            		self.refresh();
                }
                this.$root.eventHandle.$emit('alert', {
            		component: () => import('@components/after_sales/distribute/add.vue'),
            		close: function(){},
        			style:'width:900px;height:600px',
            		title:'新增售后人员',
            		params:data
				});
           },


            refresh() {
                this.getDistributeList()
            },

            // 获取列表
            getDistributeList(resolve, ifPromiss) {
                let self = this;
                let data = JSON.parse(JSON.stringify(this.distributeQuery));
                let url = '/afterSale-web/api/aftersale/distribute/list'
                this.ajax.postStream(url, data, d => {
                    if (d.body.result && d.body.content) {
                        this.distributeList = d.body.content.list
                        this.distributeCount = d.body.content.count
                    }else{
                        this.$message.error(d.body.msg);

                    }
                    resolve && resolve();
                }, (e) => {
                    this.$message.error(e);
                    resolve && resolve();
                })
            },
            //通用查询搜索
            distributeListSearch(where, reslove) {
                this.distributeQuery.where = where
                this.getDistributeList(reslove)
            },
            // 当前页改变
            distributePageSizeChange(ps) {
                this.distributeQuery.page_size = ps;
                this.getDistributeList();
            },
            // 当前页面显示行数改变
            distributePageChange(page) {
                this.distributeQuery.page_no = page;
                this.getDistributeList();
            },






        },
        computed: {},
        watch: {},
        mounted() {
            console.log(this.params);
            let self = this;

            if(!!this.params.isSelect){
                this.distributeBtns = [{
                        type: 'primary',
                        txt: '保存',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.save()
                        }
                    }, ]
            }
            this.getDistributeList()
        }
    }
</script>
