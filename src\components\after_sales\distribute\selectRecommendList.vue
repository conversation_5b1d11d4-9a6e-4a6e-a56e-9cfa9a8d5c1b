<!-- 修改推荐处理人列表 -->
<template>
  <xpt-list3 ref='list' id='recommendList' :data='recommendList' :btns='btns' :colData='cols' orderNo isNeedClickEvent
    :pageTotal='pageTotal' :searchPage='pageQuery.page_name' :selection='selection' @selection-change='select'
    @radio-change="select" @search-click='recommendListSearch' @page-size-change='pageSizeChange'
    @current-page-change='pageChange'>
  </xpt-list3>
</template>
<script>
import fn from '@common/Fn.js'

export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      selection: self.params.isSelect ? 'radio' : 'checkbox',
      // selection:'radio',
      recommendList: [],
      btns: [{
        type: 'success',
        txt: '刷新',
        disabled: () => {
          return false;
        },
        click() {
          self.refresh()
        }
      },
      ],
      cols: [
        {
          label: '工号',
          prop: 'employeeNumber'
        },
        {
          label: '姓名',
          prop: 'realName',
        },
        {
          label: '昵称',
          prop: 'nickName'
        },
        {
          label: '业务员',
          prop: 'salesmanType',
          format: 'auxFormat',
          formatParams: 'businessType'
        },
        {
          label: '分组',
          prop: 'groupName'
        },
      ],
      pageTotal: 0,
      pageQuery: {
        page_name: "user_person_salesman",
        salesmanTypeList: ["CUSTOMER_SERVICE_ABC","CUSTOMER_SERVICE_EXECUTIVE"],
        isEnable: 1,
        status:1,
        where: [],
        page: {
					length: self.pageSize,
					pageNo: 1
				},
      },
      ifSave: false,
      selectList: [],
    }
  },
  methods: {
    save() {
      if (!this.selectList || this.selectList.length === 0) {
        this.$message.error('请选择业务员')
        return false;
      }
      // console.log(this.selectList);
      this.params.callback(this.selectList);
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId);

    },
    select(list) {
      console.log(list);
      this.selectList = list;
    },


    refresh() {
      this.getList()
    },

    // 获取列表
    getList(resolve, ifPromiss) {
      let self = this;
      let data = JSON.parse(JSON.stringify(this.pageQuery))
      if(this.params.salesmanTypeList) {
        if(this.params.salesmanTypeList.length===0) { 
          delete data.salesmanTypeList;
        } else { 
          data = {
            ...data,
            salesmanTypeList: this.params.salesmanTypeList
          }
        }
      } else {
        data.salesmanTypeList = ["CUSTOMER_SERVICE_ABC","CUSTOMER_SERVICE_EXECUTIVE"]
      }
      // data.where.push({
      //   "field": "ea38744ec93d683c28c4df5fc0b2ca59",
      //   "table": "cfcadaec5fdc3edaa2afea4ac01cfdf2",
      //   "value": "CUSTOMER_SERVICE_EXECUTIVE",
      //   "operator": "=",
      //   "condition": "AND",
      //   "listWhere": []
      // },
      //   {
      //     "field": "ea38744ec93d683c28c4df5fc0b2ca59",
      //     "table": "cfcadaec5fdc3edaa2afea4ac01cfdf2",
      //     "value": "CUSTOMER_SERVICE_ABC",
      //     "operator": "=",
      //     "condition": "OR",
      //     "listWhere": []
      //   })
      let url = '/user-web/api/userPerson/getUserPersonSalesmanList'
      this.ajax.postStream(url, data, d => {
        if (d.body.result && d.body.content) {
          this.recommendList = d.body.content.list || []
          this.pageTotal = d.body.content.count
        } else {
          this.$message.error(d.body.msg);

        }
        resolve && resolve();
      }, (e) => {
        this.$message.error(e);
        resolve && resolve();
      })
    },
    //通用查询搜索
    recommendListSearch(where, reslove) {
      this.pageQuery.where = where
      this.getList(reslove)
    },
    // 当前页改变
    pageSizeChange(ps) {
      this.pageQuery.page.length = ps;
      this.getList();
    },
    // 当前页面显示行数改变
    pageChange(page) {
      this.pageQuery.page.pageNo = page;
      this.getList();
    },






  },
  computed: {},
  watch: {},
  mounted() {
    console.log(this.params);
    let self = this;

    if (!!this.params.isSelect) {
      this.btns = [{
        type: 'primary',
        txt: '保存',
        disabled: () => {
          return false;
        },
        click() {
          self.save()
        }
      },]
    }
    this.getList()
  }
}
</script>
