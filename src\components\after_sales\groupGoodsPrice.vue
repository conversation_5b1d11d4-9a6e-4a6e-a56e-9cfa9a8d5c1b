<!-- 组合商品价目表列表 -->
<template>
	<div class="xpt-flex">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span='16'>
				<el-button type='warning' size='mini' @click='close'>确认</el-button>
			</el-col>
			<el-col :span='8' class='xpt-align__right'>
				<el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="searchString" :on-icon-click="searchData">
				</el-input>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom mgb20" >
			<el-table :data="list" border tooltip-effect="dark" width='100%' style="width: 100%" highlight-current-row @current-change="handleCurrentChange">
			    <el-table-column width="55" align='center' >
					<template slot-scope='scope'>
						<el-radio-group v-model="selectCode">
						    <el-radio :label="scope.row.key" class='xpt-table__radio'></el-radio>
						</el-radio-group>
					</template>
			   	</el-table-column>
			    <el-table-column prop="price_list_code" label="价目表编码" ></el-table-column>
			    <el-table-column prop="price_list_name" label="价目表名称" ></el-table-column>
			    <el-table-column prop="price_list_type_string" label="价目表类型" ></el-table-column>
			    <el-table-column prop="store_area_string" label="状态（已审核）全局标志" ></el-table-column>
			    <el-table-column prop="material_code" label="物料编码" ></el-table-column>
			    <el-table-column prop="material_name" label="物料名称" ></el-table-column>
			    <el-table-column prop="material_specification" label="规格描述" ></el-table-column>
			    <el-table-column prop="enable_date" label="生效日期" >
			    	<template slot-scope="scope">{{scope.row.enable_date | dataFormat1}}</template>
			    </el-table-column>
			    <el-table-column prop="disable_date" label="失效日期" >
			    	<template slot-scope="scope">{{scope.row.disable_date | dataFormat1}}</template>
			    </el-table-column>
		  	</el-table>
		</el-row>
	  	<el-row class='xpt-pagation'>
		  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
			  	:current-page="pageNow" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
			  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
			</el-pagination>
		</el-row>
	</div>
</template>
<script>

export default {
	data(){
		return {
			searchString:'',
			list:[],
			selectCode:'',
			pageNow:1,
			pageTotal:0,
			pageSize:10,
			selectObj:null
		}
	},
	props:['params'],
	methods:{
		handleCurrentChange(data){
			if(data){
				this.selectCode=data.key;
				this.selectObj = data;
			}
		},
		close(){

			if(this.selectObj === undefined || this.selectObj === null){
				this.$message.error('请先选择一行');
				return;
			}
			var data = {
				group_material_id: this.selectObj.material_id,// 组合商品id	Long
				list_material_id: this.params.list_material_id,// 单品id集合	List

			}
			this.ajax.postStream('/after-web/api/after/plan/checkGroup', data, res => {
				if(res.body.result) {
					this.$message.success('组合成功');
					this.params.callback(this.selectObj);
					this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
				}else {
					this.$message.error(res.body.msg)
				}
			})

		},
		searchData(){
			this._getlist();
		},
		pageSizeChange(pageSize){
			this.pageSize = pageSize;
			this._getlist();
		},
		pageChange(page){
			this.pageNow = page
			this._getlist();
		},
		_getlist(){
			let self = this,
			data = {
				singleShotDate: this.params.singleShotDate,
				material_code: this.searchString,
				page: {
					pageNo: this.pageNow,
					length: this.pageSize
				}
			};
			this.ajax.postStream('/after-web/api/after/plan/group',data,(res => {
				if(res.body.result){

					this.list = res.body.content;
					this.list.map( v => {
						v.key = new Date().getTime() + Math.random()* 100;
					})
				}
			}))
		}
	},
	mounted() {
		this._getlist();
	}
}
</script>
