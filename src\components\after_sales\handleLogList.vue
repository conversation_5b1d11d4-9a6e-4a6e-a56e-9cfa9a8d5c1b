<!-- 处理进度 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type="primary" size="mini" @click='addFun' :disabled="btnStatus || !orderId || cannotAdd ">新增行</el-button>
				<el-button type="danger" size="mini" @click='delFun' :disabled="btnStatus || !selectArr.length ">删除行</el-button>
				<el-button type="danger" size="mini" @click='saveFun' :disabled="btnStatus || !cannotAdd ">保存</el-button>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom">
			<el-table border tooltip-effect="dark" style="width: 100%;"
				@selection-change="_selectArr => selectArr = _selectArr"
				:data='list'
				:row-class-name="row => /(重点|高)/.test(row.handle_level) ? $style['red-row'] : ''"
			>
				<el-table-column  type="selection"  width="50" :selectable="row => row.ifEdit"></el-table-column>
			    <el-table-column  type="index" width="50" label="序号"></el-table-column>
				<el-table-column show-overflow-tooltip  label="处理人" prop="handler_name" width="100"></el-table-column>
				<el-table-column show-overflow-tooltip :class-name="$style['th-required']" label="处理内容">
					<template slot-scope="scope">
						<el-input size="mini" v-model="scope.row.handle_content" :maxlength="255" v-if="scope.row.ifEdit" style="width:100%"></el-input>
						<span v-else style="width:100%">{{scope.row.handle_content}}</span>
					</template>
				</el-table-column>
				<el-table-column show-overflow-tooltip  label="处理时间" width="140">
					<template slot-scope="scope">
						<span>{{scope.row.handle_time | dataFormat('yyyy-MM-dd hh:mm:ss')}}</span>
					</template>
				</el-table-column>
				<el-table-column show-overflow-tooltip :class-name="$style['th-required']" label="预约时间" width="180">
					<template slot-scope="scope">
						<el-date-picker
						    v-model="scope.row.pre_time"
						    type="datetime"
						    placeholder="选择日期"
						    size="mini"
						    style="width:100%;"
						    :picker-options="pickerOptions0"
						    @change="$emit('updateFormPreOrderTime', scope.row.pre_time)"
						    v-if="scope.row.ifEdit">
						</el-date-picker>
						<!-- <el-tooltip v-if='scope.row.ifEdit' class="item" effect="dark" content="必填" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip> -->
						<span v-else>{{scope.row.pre_time | dataFormat('yyyy-MM-dd hh:mm:ss')}}</span>
					</template>
				</el-table-column>
				<el-table-column show-overflow-tooltip  label="处理优先等级" width="100">
					<template slot-scope="scope">
						<el-select v-model="scope.row.handle_level" size="mini" placeholder="请选择"  v-if="scope.row.ifEdit" style="width:100%;">
						    <el-option label="普通" value="普通"></el-option>
						    <el-option label="重点" value="重点"></el-option>
						</el-select>
						<span v-else>{{ scope.row.handle_level }}</span>
					</template>
				</el-table-column>
			</el-table>
		</el-row>
	</div>
</template>
<script>
	import Fn  from '@common/Fn.js'
	export default {
		props: ['params'],
		data() {
			return {
				orderId: '',
				pickerOptions0: {
					disabledDate: time => time.getTime() < Date.now() - 8.64e7
		        },
		        btnStatus:true,//默认还未加载完成，按钮都不能操作
				// ifLock:false,
				// hideHead:true,
				list: [],
				cannotAdd: false,//只能新增一行
				selectArr: [],
				// options: [{
				// 	value: 'UNIMPORTANCE',
				// 	label: '普通'
				// },{
				// 	value: 'IMPORTANCE',
				// 	label: '重点'
				// }]
			}
		},
		methods: {
			addFun() {
				let obj = {
					ifEdit: true,
					// key: new Date().getTime(),
					// after_handle_log: '',// 流水号
					handle_content: '',// 处理内容
					handle_time: new Date().getTime(),// 处理时间
					pre_time: '',// 预约时间
					handle_level: '普通',// 处理优先级 IMPORTANCE 重要   UNIMPORTANCE不重要
					handler_name: Fn.getUserInfo('fullName') ,//处理人
					handler: Fn.getUserInfo('id')
				};
				this.cannotAdd = true
				this.list.push(obj)
				this.$set(obj, 'pre_time', '')
			},
			delFun() {
				this.selectArr.forEach(obj => {
					this.list.splice(this.list.indexOf(obj), 1)
				})
				this.cannotAdd = false
			},
			saveFun (e){
				var msg
				,	$btn = e.currentTarget
				,	postData = this.list.filter(obj => obj.ifEdit).map(obj => {
					if(!obj.pre_time) {
						msg = '预约时间不能为空'
					}else if (obj.pre_time < +new Date){
						msg = '预约时间不能小于或者等于当前时间'
					}else if (!obj.handle_content){
						msg = '处理内容不能为空'
					}

					return {
						back_id: this.params.id,
						handler:obj.handler,
						handler_name:obj.handler_name,
						handle_content: obj.handle_content,
						pre_time: obj.pre_time,
						handle_time: obj.handle_time,
						handle_level: obj.handle_level
					}
				})

				if(!postData.length){
					msg = '请新增一条处理进度'
				}
				if(msg){
					this.$message.error(msg)
					return
				}

				$btn.disabled = true
				this.ajax.postStream('/afterSale-web/api/deal/coupleBack/action/savehandleLog', postData[0], res => {
					if(res.body.result){
						this.$message.success('保存成功')
						setTimeout(() => {
							// 数据库操作有延迟
							this.init(this.params.id, () => {
								$btn.disabled = false
							})
						}, 300)
					}else {
						this.$message.error(res.body.msg)
						$btn.disabled = false
					}
				}, () => {
					$btn.disabled = false
				})
			},
		
			init (_orderId, cb) {
				this.btnStatus = _orderId?true:false;
				if(_orderId){
					this.orderId = _orderId
					this.ajax.postStream('/afterSale-web/api/deal/coupleBack/query/getHandleLogList', _orderId, res => {
						this.list = res.body.content
						this.cannotAdd = false
						cb && cb();
						this.btnStatus = false;
					}, () => {
						cb && cb();
						this.btnStatus = false;
					})
				}else {
					this.list = [];
					//this.btnStatus = false;
				}
			},
		},
		watch: {
			
		},
		mounted(){
		}
	}
</script>
<style lang="stylus" module>
.red-row {
	color: red;
	:global(.el-input__inner){
		color:red!important;
	}
}
th.th-required :global(.cell:before) {
	content: '*';
	color: red;
}
</style>