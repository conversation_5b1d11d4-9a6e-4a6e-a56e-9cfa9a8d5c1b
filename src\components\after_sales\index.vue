<!-- 售后单主体 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="18">
				<el-button type="primary" size="mini" @click="addNewOrder">新增</el-button>
				<el-button
					type="success"
					size="mini"
					@click="submit"
					:disabled="
						form.isClosed
						|| !isHasRights2Edit
						|| (!form.otherInfo.locker_name && form.isFromRead)
						|| form.status === 'WAIT_HANDLE'
					"
				>保存</el-button><!-- 业务状态=售后待办，不能点击(by 肥东)! -->
				<el-button type="primary" size="mini" @click="refresh">刷新</el-button>
				<el-button type="primary" size="mini" @click="selectMergeOrder" :disabled="form.isFromRead || isSelectNoIdGoods">选单</el-button>
				<el-button
					type="primary"
					size="mini"
					@click="changeStatus('lock?permissionCode=AFTERSALE_BUSINESS_LOCK', { type: 'A' })"
					:disabled="
						!form.isFromRead
						|| form.isClosed
						|| form.status !== 'CREATE'
						|| ifDataStatusChange
					"
				>业务锁定</el-button><!-- 业务解锁，业务锁定，提交售后三者按钮状态互斥 -->
				<el-button type="primary" size="mini" @click="changeStatus('lock?permissionCode=AFTERSALE_BUSINESS_UNLOCK', { type: 'B' })"
					:disabled="
						!form.isFromRead
						|| form.isClosed
						|| !(getEmployeeInfo('id') == form.otherInfo.locker && !form.otherInfo.submit_staff_name)
						|| ifDataStatusChange
					">业务解锁</el-button><!-- 业务锁定人 = 操作人 && 提交售后人为空是才能操作业务解锁 -->
				<el-button
					type="primary"
					size="mini"
					@click="changeStatus('submit?permissionCode=AFTERSALE_SUBMIT_AFTERSALE', { type: 'A' })"
					:disabled="
						!form.isFromRead
						|| form.isClosed
						|| form.status !== 'PRE_HANDLE'
						|| !isHasRights2Edit
						|| !form.goodsDataListVO.length
						|| form.if_three_age
						|| isReSaleOrder
						|| ifDataStatusChange
					"
				>提交售后</el-button><!-- 商品为空时不能提交售后,“是否三个月前售后”为是，不可提交售后 -->
				<el-button
					type="primary"
					size="mini"
					@click="changeStatus('submitAbc?permissionCode=AFTERSALE_SUBMIT_AFTERSALE_ABC', { type: 'B' })"
					:disabled="
						!form.isFromRead
						|| form.isClosed
						|| form.status !== 'PRE_HANDLE'
						|| !isHasRights2Edit
						|| !form.goodsDataListVO.length
						|| !form.if_three_age
						|| isReSaleOrder
						|| ifDataStatusChange
					"
				>提交售后ABC</el-button><!-- 商品为空时不能提交售后,“是否三个月前售后”为否，才可提交售后ABC -->
				<el-button
					type="success"
					size="mini"
					@click="updateAfterHandler"
				>修改推荐处理人</el-button><!-- 商品为空时不能提交售后,“是否三个月前售后”为否，才可提交售后ABC -->
				<el-button
					type="primary"
					size="mini"
					:disabled="
						!(
							form.status === 'WAIT_HANDLE' &&
							form.otherInfo.locker_name &&
							(form.otherInfo.submit_staff === getEmployeeInfo('id') || form.otherInfo.submit_staff === getUserInfo('id'))
						)
						|| isReSaleOrder
					"
					@click="changeStatus('retract?permissionCode=AFTERSALE_WITHDRAW', {})"
				>撤回</el-button><!-- 售后待办 && 业务锁定人不为空 && 提交售后人 = 代理人才能操作撤回 -->
				<el-button
					type="danger"
					size="mini"
					:disabled="
						form.isClosed
						|| !/(WAIT_HANDLE|AFTER_HANDLE)/.test(form.status)
						|| isReSaleOrder
					"
					@click="rejectRecord"
				>驳回</el-button>
				<el-button
					type="primary"
					size="mini"
					@click="() => changeStatus('lock?permissionCode=AFTERSALE_AFTERSALE_LOCK', { type: 'C' })"
					:disabled="
						!form.isFromRead
						|| form.isClosed
						|| form.status !== 'WAIT_HANDLE'
						|| isReSaleOrder
					"
				>售后锁定</el-button>
				<el-button
					type="primary"
					size="mini"
					@click="changeStatus('lock?permissionCode=AFTERSALE_AFTERSALE_UNLOCK', { type: 'D' })"
					:disabled="
						!form.isFromRead
						|| form.isClosed
						|| form.status !== 'AFTER_HANDLE'
						|| isReSaleOrder
						|| !hasRightsToEidtSolution
					"
				>售后解锁</el-button>
				<el-button
					type="primary"
					size="mini"
					@click="updateThreeAge"
				>是否三个月前售后</el-button>
				<el-button type="primary" size="mini" @click="watchUploadFile" :disabled="!form.after_order_no">查看附件</el-button>
        <div style='display: inline-block'>
          <xpt-btngroup type="success" :btngroup='callBtnGroups' :beforeClick="beforeClick" :disabled="callBtnGroupsDisabled" width='60px' class="mgl10"  size="mini" slot='left'></xpt-btngroup>
        </div>
        <el-button type="primary" size="mini" @click="outboundReminderSetting()"> 外投提醒设置</el-button>
        <el-button type="primary" size="mini" @click="arrogantCust()"> 是否嚣客</el-button>
				<el-button
					type="danger"
					size="mini"
					@click="checkAllAfterPlanIsSubmit(() => changeStatus('finish?permissionCode=AFTERSALE_FINISHED', { status: form.isClosed ? 'UNFINISH' : 'FINISH' }))"
					:disabled="
						_closeBtnStatus()
						|| ifDataStatusChange
					"
				>
				完结
				</el-button>
        <el-select
          placeholder="标签"
          :disabled="!form.after_order_id"
          v-model="defaultUrgentSign"
          size='mini'
          style="width:100px;margin-left: 8px"
          :class="!!urgentSignErrorTip ? $style['urgent-sign-error'] : ''"
        >
						<el-option
							v-for="item in urgentSignOptions"
							:key="item.code"
							:label="item.name"
							@click.native="onUrgentSignChange"
							:value="item.code"
            >
						</el-option>
					</el-select>
          <el-tooltip v-if='urgentSignErrorTip' :class="$style['error-tip']" effect="dark" :content="urgentSignErrorTip" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
			</el-col>
			<el-col :span="6" style="text-align: right;">
				<el-button type="primary" size="mini" @click="nextOrPrevOrder('prev')">上一页</el-button>
				<el-button type="primary" size="mini" @click="nextOrPrevOrder('next')">下一页</el-button>
			</el-col>
		</el-row>
		<el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px">
			<el-row class='xpt-flex__bottom'>
				<el-tabs v-model="selectTab">
					<el-tab-pane label='基本信息' name='basicInformation' class='xpt-flex'>
						<el-row>
							<el-col :span="6">
								<el-form-item label="售后单号">
									<el-input size='mini' v-model="form.after_order_no" disabled placeholder="系统自动生成"></el-input>
								</el-form-item>

								<el-form-item label="业务员" prop="staff_name">
									<el-input
										v-if="form.staff_name && form.isFromRead || form.merge_trade_id"
										v-model="form.staff_name"
										size='mini'
										disabled
									></el-input>
									<el-input
										v-else
										v-model="form.staff_name"
										size='mini'
										icon="search"
										:on-icon-click="selectBestStaff2"
										@change.native="e => searchAfterChange(e, '业务员')"
									></el-input>
									<el-tooltip v-if='rules.staff_name[0].isShow' class="item" effect="dark" :content="rules.staff_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>

								<el-form-item label="业务员分组" prop="staff_group_name">
									<el-input size='mini' v-model="form.staff_group_name" disabled></el-input>
								</el-form-item>
								<el-form-item label="业务员大分组" prop="staff_big_group_name">
									<el-input size='mini' v-model="form.staff_big_group_name" disabled></el-input>
								</el-form-item>
								<el-form-item label="日期" prop="after_order_time">
									<el-date-picker
										v-model="form.after_order_time"
										:disabled="!!form.after_order_id"
									    type="date"
									    :editable="false"
									    placeholder="选择日期" size="mini" style="width:200px;">
									</el-date-picker>
									<el-tooltip v-if='rules.after_order_time[0].isShow' class="item" effect="dark" :content="rules.after_order_time[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
								<el-form-item label="预约处理日期" prop="pre_order_time">
									<el-date-picker
										v-model="form.pre_order_time"
										:disabled="!isHasRights2Edit || form.status === 'WAIT_HANDLE'"
										:editable="false"
									    type="date"
									    :picker-options="{
										    disabledDate: time => time.getTime() < Date.now() - 8.64e7 || time.getTime() > Date.now() + 8.64e7 * 19
									    }"
									    placeholder="选择日期" size="mini" style="width:200px;">
									</el-date-picker><!-- 业务状态=售后待办，不能编辑(by 肥东) -->
									<el-tooltip v-if='rules.pre_order_time[0].isShow' class="item" effect="dark" :content="rules.pre_order_time[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
								<el-form-item label='结算方式'>
									<el-select
									v-model="form.settle_method"
									size="mini"
									disabled
									placeholder="请选择"
									class="js_pmm"
									>
										<el-option key="WANGPAI_DEALER" label="经销网拍" value="WANGPAI_DEALER"></el-option>
										<el-option key="DEALER" label="经销" value="DEALER"></el-option>
										<el-option key="OTHER" label="其它" value="OTHER"></el-option>
									</el-select>
								</el-form-item>
                <el-form-item label="销售标签">
                  <span style="color:red;">{{ form.sales_label }}</span>
                </el-form-item>
                <el-form-item label="来源类型">
                  <xpt-select-aux
                    v-model="form.source"
                    aux_name='AFTERSALE_SOURCE_TYPE'
                    :disabled="true"
                  ></xpt-select-aux>
                </el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="合并订单号">
	    						<a style="text-decoration:none;" href="javascript:;" @click="viewMergedOrder(form.merge_trade_id)" title="点击进入合并订单信息页面">{{form.merge_trade_no}}</a>
								</el-form-item>
								<el-form-item label="推荐处理人" prop="best_staff_name">
									<el-input
										v-if="form.best_staff_name && form.isFromRead || form.merge_trade_id"
										v-model="form.best_staff_name"
										size='mini'
										disabled
									></el-input>
									<el-input
										v-else
										v-model="form.best_staff_name"
										size='mini'
										icon="search"
										:on-icon-click="selectBestStaff"
										readonly
									></el-input>
									<el-tooltip v-if='rules.best_staff_name[0].isShow' class="item" effect="dark" :content="rules.best_staff_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
								<el-form-item label="推荐处理人分组">
									<el-input size='mini' v-model="form.recommend_handler_group_name" disabled></el-input>
								</el-form-item>
								<el-form-item label="关闭状态">
									<el-input size='mini' disabled :value="form.close_status === 'Y' ? '已关闭' : '未关闭'"></el-input>
								</el-form-item>
								<el-form-item label="是否三个月前售后">
									<el-switch
										on-text="是"
										off-text="否"
										v-model="form.if_three_age"
										:disabled="true"
									></el-switch>
								</el-form-item>
								<el-form-item label="是否有效售后">
									<el-input size='mini' :value="form.if_valid_duty == 'Y' ? '是' :'否'"  :disabled="true"></el-input>
								</el-form-item>
                <el-form-item label="是否快递只需安装">
                  <el-select
                    v-model="form.if_express_install"
                    :disabled="!!form.status && !['CREATE', 'PRE_HANDLE'].includes(form.status)"
                    clearable
                    size="mini"
                  >
                    <el-option value="Y" label="是" />
                    <el-option value="N" label="否" />
                  </el-select>
                </el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="业务备注" prop="service_note" :class="$style['row-height']">
									<el-input v-model="form.service_note" type="textarea" :autosize="{ minRows: 2, maxRows: 2}" :maxlength="255" :disabled="!isHasRights2Edit || form.status === 'WAIT_HANDLE'"></el-input><!-- 业务状态=售后待办，不能编辑(by 肥东) -->
									<el-tooltip v-if='rules.service_note[0].isShow' class="item" effect="dark" :content="rules.service_note[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
								<el-form-item label="买家昵称" prop="buyer_nick">
									<el-input
										v-if="form.buyer_nick && form.isFromRead || form.merge_trade_id || !buyerNickEdit"
										v-model="form.buyer_nick"
										size='mini'
										disabled
									></el-input>
									<el-input
										v-else
										v-model="form.buyer_nick"
										size='mini'
										icon="search"
										:on-icon-click="selectBuyersName"
										@change.native="e => searchAfterChange(e, '买家昵称')"
									></el-input>
									<el-tooltip v-if='rules.buyer_nick[0].isShow' class="item" effect="dark" :content="rules.buyer_nick[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
									<span v-if="!(form.buyer_nick && form.isFromRead || form.merge_trade_id || !buyerNickEdit)" :class="$style['addBuyerBtn']" @click="addBuyer">+</span>
								</el-form-item>
								<el-form-item label="收货人姓名">
									<el-input size='mini' disabled :value="form.receiver_name"></el-input>
								</el-form-item>
								<el-form-item label="收货人电话">
									<!-- <el-input size='mini' disabled :value="form.receiver_mobile"></el-input> -->
									<xpt-eye-switch v-model="form.receiver_mobile" :disabled="true" :aboutNumber="form.after_order_no"></xpt-eye-switch>
								</el-form-item>
								<el-form-item label="收货人地址" :class="$style['row-height']">
									<el-input type="textarea" autosize disabled :value="form.address_name"></el-input>
								</el-form-item>
								<el-form-item label="结算主体" >
									<el-input size='mini'  disabled :value="form.settle_entity "></el-input>
								</el-form-item>
								<el-form-item label="币别" >
									<el-input :value="currency_options[form.currency_code]" size='mini' disabled></el-input>
								</el-form-item>
							</el-col>

							<el-col :span="6">
								<el-form-item label="业务状态" >
									<el-input size='mini' disabled :value="{
										CREATE: '待办',
										PRE_HANDLE: '售前处理',
										WAIT_HANDLE: '售后待办',
										AFTER_HANDLE: '售后处理',
										FINISH: '完结',
										CANCELLATION: '作废',
									}[form.status] || '创建'"></el-input>
								</el-form-item>
								<el-form-item label="是否经销商订单">
									<el-switch v-if="params.code !== 'AFTERSALE_ORDER_RESALE_LIST'" on-text="是" off-text="否" v-model="boolObject[form.if_dealer]"  disabled></el-switch>
                  					<el-switch v-else on-text="是" off-text="否" off-value="N" on-value="Y" disabled></el-switch>
								</el-form-item>
								<el-form-item label="经销商名称">
									<el-input size='mini' disabled :value="form.dealer_customer_name"></el-input>
								</el-form-item>
								<el-form-item label="经销商编码">
									<el-input size='mini' disabled :value="form.dealer_customer_number"></el-input>
								</el-form-item>
								<el-form-item label="业绩代理人">
									<el-input size='mini' disabled :value="form.duty_name"></el-input>
								</el-form-item>
								<el-form-item label="业绩代理人分组">
									<el-input size='mini' disabled :value="form.duty_group_name"></el-input>
								</el-form-item>
                <el-form-item label="是否嚣客">
                  <el-switch on-text="是" off-text="否" on-value="Y" off-value="N" v-model="form.if_arrogant_cust" :disabled="true"></el-switch>
								</el-form-item>
								<el-form-item label="是否批量事件">
                  					<el-switch on-text="是" off-text="否" on-value="Y" off-value="N" v-model="form.if_batch_event" :disabled="true"></el-switch>
								</el-form-item>
                <el-form-item label="是否国补订单">
                  <el-switch on-text="是" off-text="否" on-value="Y" off-value="N" v-model="form.if_gov_subsidy" :disabled="true"></el-switch>
                </el-form-item>
							</el-col>
						</el-row>
					</el-tab-pane>
					<el-tab-pane label='其他信息' name='otherInformation' class='xpt-flex'>
						<el-row>
							<el-col :span="6">
								<el-form-item label="创建人" >
									<el-input size='mini' disabled :value="form.otherInfo.creator_name"></el-input>
								</el-form-item>

								<el-form-item label="创建时间" >
									<el-date-picker
									    type="datetime"
									    placeholder="" size="mini" style="width:200px;" disabled
									    :value="form.otherInfo.create_time"
									>
									</el-date-picker>
								</el-form-item>

								<el-form-item label="业务锁定人" >
									<el-input size='mini' disabled :value="form.otherInfo.locker_name"></el-input>
								</el-form-item>
								<el-form-item label="业务锁定人分组" >
									<el-input size='mini' disabled :value="form.otherInfo.locker_group_name"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="提交售后日期">
									<el-date-picker
									    type="datetime"
									    placeholder="" size="mini" style="width:200px;" disabled
									    :value="form.otherInfo.submit_time"
									>
									</el-date-picker>
								</el-form-item>

								<el-form-item label="提交售后人" >
									<el-input size='mini' disabled :value="form.otherInfo.submit_staff_name"></el-input>
								</el-form-item>
								<el-form-item label="业务锁定角色" >
									<el-input size='mini' disabled :value="{
										SALES_PERSON: '客服',
										CUSTOMER_SERVICE_EXECUTIVE: '售后专员',
										ISUUE_ANALYST: '售后分析员',
										SHIP_ASSITANT: '售中专员',
										RECEIVABLES_AUDITOR: '财审专员',
										REFUND_AUDITOR: '退款审核员',
									}[form.otherInfo.locker_role_name]"></el-input>
								</el-form-item>
								<el-form-item label="业务锁定日期" >
									<el-date-picker
									    type="datetime"
									    placeholder="" size="mini" style="width:200px;" disabled
									    :value="form.otherInfo.lock_time"
									>
									</el-date-picker>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="驳回人" >
									<el-input size='mini' disabled :value="form.otherInfo.reject_user_name"></el-input>
								</el-form-item>
								<el-form-item label="驳回日期" >
									<el-date-picker
									    type="datetime"
									    placeholder="" size="mini" style="width:200px;" disabled
									    :value="form.otherInfo.reject_time"
									>
									</el-date-picker>
								</el-form-item>
								<!-- <el-form-item label="售后锁定人分组" >
									<el-input size='mini' disabled :value="form.otherInfo.sale_locker_group_name"></el-input>
								</el-form-item> -->
								<el-form-item label="售后锁定大分组" >
									<el-input size='mini' :value="form.otherInfo.sale_locker_big_group_name" disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="完结人" >
									<el-input size='mini' disabled :value="form.otherInfo.finisher_name"></el-input>
								</el-form-item>
								<el-form-item label="完结人员分组" >
									<el-input size='mini' disabled :value="form.otherInfo.finisher_group_name"></el-input>
								</el-form-item>
								<el-form-item label="完结日期" >
									<el-date-picker
									    type="datetime"
									    placeholder="" size="mini" style="width:200px;" disabled
									    :value="form.otherInfo.finish_time"
									>
									</el-date-picker>
								</el-form-item>
							</el-col>
						</el-row>
					</el-tab-pane>
				</el-tabs>
			</el-row>
		</el-form>
		<el-row class='xpt-flex__bottom' v-fold>
			<el-tabs v-model="selectTabTwo" @tab-click="callComponentUpdate">
				<el-tab-pane label='问题商品信息' name='badGoods' class='xpt-flex'>
					<component
						is='badGoods'
						ref="$badGoods"
						v-on:sendOrderDetailId="ids => _$orderDetailIds = ids"
						v-on:selectMergeOrderBtnStatus="bool => isSelectNoIdGoods = bool"
						v-on:updateAfterUpload="_initRenderByOrderId(form.after_order_id, 'noMsg')"
						@testAfterSaleLock="cb => testAfterSaleLock(cb)"
						:params="{
							isReSaleOrder: isReSaleOrder,
							merge_trade_id: form.merge_trade_id,
							merge_trade_no: form.merge_trade_no,
							after_order_id: form.after_order_id,
							after_order_no: form.after_order_no,
							isClosed: form.isClosed,
							staff_name: form.staff_name,//有源非商品问题，自动带出选单时的业务员和分组和
							staff_group_name: form.staff_group_name,
							isHasRights2Edit: isHasRights2Edit,
							hasRightsToEidtSolution:hasRightsToEidtSolution,
                            isSubmitQualityFeedback:isSubmitQualityFeedback,//是否提交品质反馈权限
							form:form//整个表单信息传过去
						}"
						:goodsDataListVO="form.copyGoodsDataListVO"
					></component>
				</el-tab-pane>
				<el-tab-pane label='处理进度' name='handleQrogress' class='xpt-flex'>
					<component
						is='handleQrogress'
						:params="{
							isEdit: form.after_order_no && form.status != 'FINISH',
						}"
						@updateFormPreOrderTime="time => form.pre_order_time = time"
						@updateAftersale="res =>_initRenderByOrderId(form.after_order_id) "
						ref="$handleQrogress"
					></component>
				</el-tab-pane>
				<el-tab-pane label='订单信息' name='orderDetail' class='xpt-flex'>
					<component is='orderDetail'></component>
				</el-tab-pane>
				<el-tab-pane label='驳回信息' name='rejectRemark' class='xpt-flex'>
					<component is='rejectRemark' ref="$rejectRemark" :params="{
						businessRemark: form.service_note,
						isEdit: !form.isClosed && isHasRights2Edit && /(WAIT_HANDLE|AFTER_HANDLE)/.test(form.status),
					}"></component>
					<!-- 只有售后待办或售后处理才能点击驳回和驳回信息页签中的新增和删除按钮 -->
				</el-tab-pane>
				<el-tab-pane label='操作记录' name='operationRecord' class='xpt-flex'>
					<component is='operationRecord'></component>
				</el-tab-pane>
				<el-tab-pane label='优惠' name='couponTable' class='xpt-flex'>
					<component is='couponTable'></component>
				</el-tab-pane>

				<el-tab-pane label='下游单据进度' name='billStatusList' class='xpt-flex'>
					<component is='billStatusList'></component>
				</el-tab-pane>
				<el-tab-pane label='咨询详情' name='consultionList' class='xpt-flex'>
          <span slot="label">
            <div :class="$style['tab-pane-item']" >
              <div>咨询详情</div>
              <el-badge is-dot v-if="ifConsultReplyTag"></el-badge>
            </div>
          </span>
					<xpt-list
						:data="consultionList"
						:colData='consultionCols'
						:showHead='false'
						selection='hidden'
					>
          <template  v-slot:satisfy="{ row }">
            <div v-if="row.bill_type==='物流咨询'">
              <el-button type="primary" size="mini" @click="handleLogicalSatisfy(row.consultion_id,'Y')" :loading="row.satisfyLoading">满意</el-button>
              <el-button type="primary" size="mini" @click="handleLogicalSatisfy(row.consultion_id,'N')">不满意</el-button>
            </div>
            <div v-if="row.bill_type==='产品咨询'">
              <el-button type="primary" size="mini" @click="handleProductSatisfy(row.consultion_id)"  :disabled="!canSatisfaction(row)">满意度评价</el-button>
            </div>
          </template>
        </xpt-list>
				</el-tab-pane>
				<el-tab-pane label='业绩' name='performanceList' class='xpt-flex'>
					<component is='performanceList'></component>
				</el-tab-pane>
				<el-tab-pane label='内部便签' name='insideType' class='xpt-flex'>
					<xpt-list
						:data="insideTypelist"
						:colData='insideTypeCols'
						:showHead='false'
						selection='hidden'
					></xpt-list>
				</el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
	import fn from '@common/Fn.js'
  import Vue, { nextTick } from 'vue'
	import VL from '@common/validate.js'
	import badGoods from '@components/after_sales/badGoods_v2.vue'
	import orderDetail from '@components/after_sales/orderDetail.vue'
	import handleQrogress from '@components/after_sales/handleQrogress.vue'
	import rejectRemark from '@components/after_sales/rejectRemark.vue'
	import operationRecord from '@components/after_sales/operationRecord.vue'
	import couponTable from '@components/after_sales/couponTable.vue'
	import billStatusList from '@components/after_sales/billStatusList.vue'
	import performanceList from '@components/after_sales/performanceList'

  // 混入呼叫按钮相关信息
  import page_four from '@components/call_system/six_page_callFun/page_four_afterSales.js'
  import afterSaleConsult from './mixins/afterSaleConsult.js'

	export default {
    props: ['params'],
    mixins: [ page_four,afterSaleConsult ],
		data() {
			var self = this;
			return {
				ifDataStatusChange: false, // 【业务解锁】【业务锁定】【提交售后】【提交售后ABC】按钮的控制
				insideTypelist:[],
		        //ifDealer:"N",//是否经销单默认为否
				insideTypeCols:[{
					label: '用户',
					prop: 'creator_name',
					width: 150,
				},{
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 150,
				},{
					label: '内容',
					prop: 'content',
          bool: true,
          isTextarea: true,
          autosize: true,
          disabled(row) {
            return !row.isEdit
          }
				}
				],
				selectTab: 'basicInformation',
				selectTabTwo: 'badGoods',
				isReSaleOrder: /退货再售/.test($('.el-tabs__item.is-active').text()),


				isSelectNoIdGoods: false,//是否选择了无源商品，true => disabled选单按钮
				dealerOrderCantEdit: false,//林氏员工在经销商订单不能有任何编辑
				/*
				XPT-3861:业务锁定人=当前操作人 && (业务状态：(PRE_HANDLE: '售前处理') or (FTER_HANDLE: '售后处理')),可以对售后单编辑，否则不能对售后单进行修改包括不能添加、删除方案，不能上传附件，不能添加、删除商品问题，不能操作提交售后、转交、完结按钮。处理进度和驳回可以新增并保存，锁定按钮无须禁用
				 */
				isHasRights2Edit: true,
				//是否有权限编辑方案
				hasRightsToEidtSolution:true,
				// 是否有编辑买家昵称权限
                buyerNickEdit: false,
                // 是否提交品质反馈权限
                isSubmitQualityFeedback:false,
                //当前业务员类型编码列表
                employeeGroupCodeList:[],
				boolObject:{
					'Y':true,
					'N':false,
					'':false,
					'null': false,
				},

				consultionList: [],
				consultionCols: [
          {
            label: '单据类型',
            prop: 'bill_type',
            align: 'center',
            width: 100,
          },
          {
					label: '咨询单号',
					prop: 'consultion_no',
					redirectClick: d => this.openConsultion(d),
          align: 'center',
					width: 180,
				}, {
					label: '来源单号',
					prop: 'source_bill_no',
					width: 200,
          align: 'center',
          formatter: (_val, _index, row) => row.bill_type === '物流咨询' ? row.batch_trade_no : row.source_bill_no,
				}, {
					label: '咨询内容',
					prop: 'content',
					// width: 200,
          align: 'center',
				}, {
					label: '回复内容',
					prop: 'reply_message',
				}, {
					label: '咨询单创建人',
					prop: 'creator_name',
          align: 'center',
					width: 150,
				}, {
					label: '咨询单创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
          align: 'center',
					width: 150,
				},{
					label: '满意度',
					prop: 'satisfy',
          align: 'center',
					width: 200,
          slot:'satisfy'
				}],
				currency_options: Object.assign(fn.getCurrency(), { RMB: '人民币' }),//币别选项

				form: { ...this.init() },
				_$orderDetailIds: [],//给订单详情用的，一组material_number

				originalData:null,//保存原始数据


				rules: {
					after_order_time:self.VLFun(true,'请选择日期'),
					pre_order_time:self.VLFun(true,'请选择预约处理日期'),
					service_note:self.VLFun(true,'请填写备注'),
					staff_name:self.VLFun(true,'请选择业务员'),
					// buyer_nick: self.VLFun(true,'请选择昵称'),
					buyer_nick: [{
						required: true,
						validator: (rule, value, callback) => {
							if(!value || !this.form.buyer){
								this.rules[rule.field][0].message = !value ? '请选择买家昵称' : '买家昵称id不能为空'
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else {
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请选择买家昵称',
					}],
					best_staff_name:self.VLFun(true,'请选择推荐人'),
				},
        urgentSignOptions: [],
        defaultUrgentSign: '',

        urgentSignErrorTip: ""
			}
		},
		components: {
			badGoods,
			orderDetail,
			handleQrogress,
			rejectRemark,
			operationRecord,
			couponTable,
			billStatusList,
			performanceList,
		},
		methods: {
      onUrgentSignChange() {
        if (this.defaultUrgentSign) {
          const postData = {
            id_list: [this.form.after_order_id],
            sign: this.defaultUrgentSign,
          };
          this.ajax.postStream('/afterSale-web/api/aftersale/order/updateBatchUrgentSign?permissionCode=AFTERSALE_QUERY', postData, (res) => {
            if (res.body.result) {
              this._initRenderByOrderId(this.form.after_order_id, 'noMsg');
              this.$message({
                message: "标签添加成功！",
                type:'success'
              });
          } else {
            this.defaultUrgentSign = '';
            const errorMsg = res.body.msg;
            this.$message.error(errorMsg)
            if (errorMsg) {
              this.urgentSignErrorTip = errorMsg
            }
          }
          })
        }
      },
      // 是否嚣客
      arrogantCust () {
        if (!this.form['after_order_id']) {
          return
        }
        let self = this, postData = {}
        function callbackFn () {
          let params = {
            ifArrogantCust: self.form['if_arrogant_cust'],
            afterOrderId: self.form['after_order_id'],
            buyer: self.form.buyer,
            callback: data => {
              self._initRenderByOrderId (self.form['after_order_id'], 'noMsg')
            }
          }
          self.$root.eventHandle.$emit('alert', {
            params,
            component: ()=>import('@components/after_sales/arrogantCustDialog'),
            style: 'width:450px;height:250px',
            title: '是否嚣客',
          })
        }
        self._initRenderByOrderId (self.form['after_order_id'], 'noMsg', callbackFn)
      },
      // 外投提醒设置
      outboundReminderSetting () {
        if (!this.form['after_order_id']) {
          return
        }
        let postData = {
          after_order_id: this.form['after_order_id']
        }
        this.ajax.postStream('/afterSale-web/api/aftersale/order/outsideReminderDetail?permissionCode=AFERSALE_OUTSIDE_REMINDER', postData, (res)=> {
          if (res.body.result && res.body.content) {
            this.$message.success(res.body.msg)
            let params = {
              dataForm: res.body.content,
              afterOrderId: this.form['after_order_id'],
              callback: data => {}
            }
            this.$root.eventHandle.$emit('alert', {
              params,
              component: ()=>import('@components/after_sales/outboundReminderDialog'),
              style: 'width:450px;height:250px',
              title: '外投提醒设置',
            })
          } else {
            this.$message.error(res.body.msg)
          }
        })
      },
			init: function (data) {
				var newData = this.refresh(null, {
					isFromRead: data && data.after_order_no ? true : false,//是否读取详情
					isClosed: data && data.status === 'FINISH' ? true : false,//是否已完结

		  			after_order_id: data ? data.id : '', //售后单id
		  			best_staff: data ? data.recommend_handler : this.getEmployeeInfo('id'), //推荐处理人id
		  			best_staff_name: data ? data.recommend_handler_name : this.getEmployeeInfo('fullName'), //推荐处理人
		   			service_note: data ? data.remark : null, //预处理时间

		   			after_order_no: data ? data.after_order_no : null, //售后单号
		  			merge_trade_id: data ? data.merge_trade_id : '', //合并单号id
		  			merge_trade_no: data ? data.merge_trade_no : '', //合并单号
					after_order_time: data ? data.create_time : new Date, //日期
		   			pre_order_time: data ? data.pre_order_time : new Date, //预处理时间
		   			buyer: data ? data.buyer : null, //买家昵称id
		   			buyer_nick: data ? data.buyer_nick : null, //买家昵称
		   			staff: data ? data.staff : null, //业务员id
		   			staff_name: data ? data.staff_name : null, //业务员
		   			staff_group: data ? data.staff_group : null, //业务员分组id
		   			staff_group_name: data ? data.staff_group_name : null, //业务员分组
		   			recommend_handler_group: data ? data.recommend_handler_group : null, //推荐处理人分组ID
		   			recommend_handler_group_name: data ? data.recommend_handler_group_name : null, //推荐处理人分组名称
		   			if_three_age: Boolean(data ? data.if_three_age : null), //是否三个月前售后
		   			status: data ? data.status : '',
		  			order_status: data ? data.order_status : '',
		  			close_status: data ? data.close_status : 'N',

            address_id: data ? data.address_id : '',
		  			receiver_name: data ? data.receiver_name : '',//收货人名称
		  			receiver_mobile: data ? data.receiver_mobile : '',//收货人手机
		  			address_name: data ? data.address_name : '',//收货人完整地址名称

		  			if_dealer : data?data.if_dealer:null,//  是否经销商订单(Y是/N否)
					dealer_customer_id :data?data.dealer_customer_id:null,//  经销商Id
					dealer_customer_number :data?data.dealer_customer_number:null,//  经销商编码
					dealer_customer_name :data?data.dealer_customer_name:null,//  经销商名称
					duty_name :data?data.duty_name:null,
					duty_group_name :data?data.duty_group_name:null,
					if_valid_duty :data?data.if_valid_duty:'N',
                    sales_label: data?data.sales_label:null,
					staff_big_group_name :data?data.staff_big_group_name:null,
					settle_method :data?data.settle_method:null,
					settle_entity :data?data.settle_entity:null,
					currency_code:data?data.currency_code:null,
					if_arrogant_cust :data?data.if_arrogant_cust:'N',
					if_batch_event:data?data.if_batch_event:'N',
					last_update_time :data?data.last_update_time:'',
          if_gov_subsidy: data?data.if_gov_subsidy:'N',
          if_express_install: data?data.if_express_install:null,
          source: data?data.source:null,
		   			// 其它信息页签
		   			otherInfo: !data ? {} : data,

					goodsDataListVO: data ? data.aftersaleOrderQuestionRowVO : [],//问题商品页签数据
					copyGoodsDataListVO: data ? JSON.parse(JSON.stringify(data.aftersaleOrderQuestionRowVO)) : [],
				})

				if(!data && !newData.staff_name && !this.params.id){//新建售后单时设置业务员默认值
					this.searchAfterChange({ target: { value: this.getEmployeeInfo('realName') ,nickName:this.getEmployeeInfo('nickName') } }, '业务员')
				}

				if(data !== undefined){
					this.form = newData;
          this.defaultUrgentSign = data.urgent_sign;
				}else {
					return newData
				}
			},

			//对页面需要认证的内容认证，required：是否必填，msg：提示信息
			VLFun(required, msg){
				return VL.isNotBlank({
					required:required,
					self:this,
					msg:msg,
				})
			},
			// 刷新按钮
			refresh: function (e, oldData, callback){
				if(oldData){
					this.refresh._oldData = JSON.parse(JSON.stringify(oldData))
					console.log('看看是不是进入到这里了');
					return oldData
				}else {
					var _closeOrRefresh = () => {
						if(e) {
							if(e === 'init' || !this.form.after_order_id){
								this.init('')
								this.isHasRights2Edit = true
							}else if (e === 'onlyCompareData'){
								callback()
							}else {
								this._initRenderByOrderId(this.form.after_order_id)
							}
						}else {
							this.$root.eventHandle.$emit('removeTab', this.params.tabName)
						}
					}

					// console.log('this.compareData(this.form, this.refresh._oldData)',this.form,this.refresh._oldData)
					if(this.compareData(this.form, this.refresh._oldData)){
						this.$root.eventHandle.$emit('openDialog', {
							ok: e => {
								if(this.submit(e)) _closeOrRefresh()
							},
							no: _closeOrRefresh
						})
					}else {
						_closeOrRefresh()
					}
				}
			},
			// 驳回按钮
			rejectRecord (e){
				this.$refs.$rejectRemark.saveFunc(e, () => {
					this._initRenderByOrderId(this.form.after_order_id, 'noMsg')
				})
			},
			openConsultion (d){
        const id = d.consultion_id;
        if(d.bill_type === '物流咨询') {
          this.$root.eventHandle.$emit('creatTab',{
            name: '咨询单详情',
            component: ()=>import('@components/after_sales/consultationNew'),
            params: {
              id,
            },
          })
        }else if(d.bill_type === '产品咨询'){
			    this.$root.eventHandle.$emit("creatTab", {
            params: { id },
				    component: () => import('@components/product_consulting/product_consulting_detail.vue'),
				    name: "产品咨询单详情",
			    });
        }else if(d.bill_type === '逆向咨询单'){
          this.$message.info('逆向咨询单暂无详情页面')
        }
			},
			getConsulationlist (){
				this.form.after_order_id && this.ajax.postStream('/afterSale-web/api/aftersale/order/bill/consulationlist', {
					id: this.form.after_order_id,
					merge_trade_id: this.form.merge_trade_id,
				}, res => {
					if(res.body.result){
						this.consultionList = res.body.content.list.map(item=>{
              if(item.second_reply_message&&item.reply_message){
                item.reply_message=`【首次回复】${item.reply_message} 【二次回复】${item.second_reply_message}`
              }
              item.satisfyLoading=false;
              return item
            })
            this.ifConsultReplyTag=false;
            this.getRevertConsulationList()
					}
				})
			},
      getRevertConsulationList() {
        this.ajax.postStream('/afterSale-web/api/aftersale/reverseCon/getListforAftersaleOrder', this.form.merge_trade_id, res => {
					if(res.body.result){
            let reverseConsultationList = res.body.content.map(item=>{
              if(item.first_con_cotent){
                if (item.second_con_cotent) {
                  item.reply_message=`【首次回复】${item.first_con_cotent} 【二次回复】${item.second_con_cotent}`
                }else {
                  item.reply_message=`【首次回复】${item.first_con_cotent}`
                }
              }
              item.bill_type = '逆向咨询单'
              item.consultion_no = item.consultation_no
              item.source_bill_no = item.batch_trade_no
              item.content = item.con_content
              item.creator_name = 'AUTO.AUTO'
              item.create_time = item.create_time
              item.consultion_id = item.id
              item.satisfyLoading=false;
              return item
            })
						this.consultionList = this.consultionList.concat(reverseConsultationList)
					}
				}).catch(() => {})
      },
			getinsideTypelist (){
				let self = this;
				this.form.merge_trade_id && this.ajax.postStream('/order-web/api/mergetrade/remark/list', this.form.merge_trade_id, res => {
					if(res.body.result){
						self.insideTypelist = res.body.content;
						console.log(self.insideTypelist)
					}
				})
			},
			// 处理进度、订单信息、驳回信息、操作记录、优惠等页签点击请求刷新
			callComponentUpdate (tab){
				if(tab.index != 0){
					var params

					if(tab.index == 2){
						params = {
							ids: (this._$orderDetailIds || []).filter(Boolean),
							merge_trade_id: this.form.merge_trade_id,
						}
					}else if (tab.index == 5){
						params = this.form.merge_trade_id
					}else if (tab.index == 7){
						this.getConsulationlist()
						return
					}else if (tab.index == 9){
						this.getinsideTypelist()
						return
					}else {
						params = this.form.after_order_id
					}

					tab.$children[0].init(params)
				}
			},
			// 新增按钮
			addNewOrder (){
				delete this.params.id//防止“该售后单已经打开”限制
				this.blockSameIdOpenTab()

				this.$root.eventHandle.$emit('updateTab', {
					name: this.params.tabName,
					title: '新增售后单' + (this.isReSaleOrder ? '(退货再售)' : ''),
				})

				this.refresh('init')
				this.getBuyerNickEdit()
			},
			// 完结前先检查：存在未提交的方案，售后单不能完结
			checkAllAfterPlanIsSubmit (cb){
				if(this.form.goodsDataListVO.some(o => (o.listAftersaleOrderPlan || []).some(p => p.after_plan_status == 'CREATE'))){
					this.$message.error('存在未提交的方案，售后单不能完结')
				}else {
					cb()
				}
			},
			updateThreeAge(){
				let self = this;
				let id = this.form.after_order_id;
				self.$root.eventHandle.$emit('alert', {
					params: {
						id:id,
						callback(data){
							if(data){
								self.ajax.postStream('/afterSale-web/api/aftersale/order/updateThreeAge',{id:self.form.after_order_id,if_three_age:data.if_three_age == true?1:0}, res => {
									if(res.body.result){
										self.$message.success(res.body.msg)

										self._initRenderByOrderId(self.form.after_order_id)

									}else{
										self.$message.error(res.body.msg)
									}
								})
							}
						}
					},
					component: () => import("@components/after_sales/updateThreeAge"),
					style: 'width:300px;height:200px',
					title: '是否三月前售后'
				});
			},
			// 锁定按钮
			//submit?permissionCode=AFTERSALE_SUBMIT_AFTERSALE,提交售后，
			//submit?permissionCode=AFTERSALE_SUBMIT_AFTERSALE_ABC,提交售后ABC
			changeStatus (api, postDataObj){
				if (/AFTERSALE_SUBMIT_AFTERSALE|AFTERSALE_BUSINESS_LOCK|AFTERSALE_BUSINESS_UNLOCK|AFTERSALE_SUBMIT_AFTERSALE_ABC/.test(api)) {
					this.ifDataStatusChange = true
				} else {
					this.ifDataStatusChange = false
				}
				if(api == 'submit?permissionCode=AFTERSALE_SUBMIT_AFTERSALE' || api == 'submit?permissionCode=AFTERSALE_SUBMIT_AFTERSALE_ABC'){
					var bool = this.isNeedSave();
					if(!bool){//没有数据改动，不需要保存，直接提交
						this.askBatchSubmitFunction(api, postDataObj);
						return;
					}
					var isPass = this.validateIsPass();
					if(!isPass) {
						this.ifDataStatusChange = (api == 'submit?permissionCode=AFTERSALE_SUBMIT_AFTERSALE' || api == 'submit?permissionCode=AFTERSALE_SUBMIT_AFTERSALE_ABC') ? false : this.ifDataStatusChange
						this.$message.error('由于数据有改动并且不符合要求，请验证数据');
						return;
					}
					//验证通过
					this.askeSaveData(false,(res)=>{
						if(res.body.result){
							this.askBatchSubmitFunction(api, postDataObj);
						}

					});
					return;
				}
				this.askBatchSubmitFunction(api, postDataObj);
			},
			// 点击售后锁定后，进行保存或添加方案时必须要填至少一个处理进度信息
			testAfterSaleLock (cb){
				if(this.form.status === 'AFTER_HANDLE'){
					if(this.$refs.$handleQrogress.list.length){
						if(this.$refs.$handleQrogress.list.some(obj => obj.id)){
							cb()
						}else {
							this.$message.error('请先保存处理进度信息')
						}
					}else {
						if(this.$refs.$handleQrogress.orderId){
							this.$message.error('请保存至少一条处理进度信息')
						}else {
							this.$refs.$handleQrogress.init(this.form.after_order_id, () => {
								if(this.$refs.$handleQrogress.list.length){
									cb()
								}else {
									this.$message.error('请保存至少一条处理进度信息')
								}
							})
						}
					}
				}else {
					cb()
				}
			},
			/***
			*向服务器请求锁定，提交售后，提交售后ABC功能
			***/
			askBatchSubmitFunction(api, postDataObj){
				this.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/order/' + api), {
					id: this.form.after_order_id,
					...postDataObj,
				}, res => {
					this.ifDataStatusChange = false
					if(res.body.result){
						this.$message.success(res.body.msg)
						this._initRenderByOrderId(this.form.after_order_id, 'noMsg',()=>{
                             this.openQualityFeedbackBtn()//锁定重新判断是否能提交品质反馈
                        })
					}else {
						if(/^finish/.test(api)){
							this.$message.error({
								message: res.body.msg.replace(/,/g, ',\n')	,
								customClass: this.$style['message-box'],
							})
							return
						}

            const errorMsg = res.body.msg;
						this.$message.error(errorMsg);

            if (errorMsg.indexOf('直转') > -1 && errorMsg.indexOf('提交') > -1) {
              this.urgentSignErrorTip = errorMsg
            }
					}
				})
			},
			blockSameIdOpenTab (id, cb){
				if(!cb){//set className
					this.$el.setAttribute('data-symbol', 'after_order_id_' + id)
				}else {//检测是否已打开相同售后单
					if(document.querySelector('[data-symbol="after_order_id_' + id + '"]')){
						this.$message.error('该售后单已经打开')
					}else {
						this.$el.setAttribute('data-symbol', 'after_order_id_' + id)
						cb()
					}
				}
			},
			// 转交人列表
			selectTransmitPeople (){
				this.$root.eventHandle.$emit('alert',{
					params: {
						callback: res => {
							this.$set(this.form, '_deliver_name', res.fullName)
							this.$set(this.form, '_deliver', res.id)
						}
					},
					component:()=>import('@components/after_sales_common/selectTransmitPeople.vue'),
					style:'width:800px;height:600px',
					title:'转交人列表'
				})
			},

			/**
			*比较新旧数据,从而得出是否需要重新保存数据
			**/
			isNeedSave(){
				var oldData = this.originalData;
				if(!oldData) return true;
				var newData = this.form;
				var bool = this.compareData(newData, oldData);
				return bool;
			},

			// 经销商时去掉接口permissionCode权限控制
			_delPermissionCodeWhenDealerUser (api){
				return /*this.personBusinessAttribute.attributeValue ? api.replace(/\?permissionCode=.+/, '') : */api
			},

			/**
			*向服务器保存数据
			*isSave=》是否是单纯的保存接口
			*****/
			askeSaveData(isSave, callback, disabledBtnCb){
				var bool = this.validateIsPass();
				if(!bool) return;
				var postData = Object.assign(this.$refs.$badGoods.returnSaveData(), {
          			id: this.form.after_order_id,
          			buyer: this.form.buyer,
				    buyer_nick: this.form.buyer_nick,
				    merge_trade_id: this.form.merge_trade_id,
				    merge_trade_no: this.form.merge_trade_no,
				    pre_order_time: this.form.pre_order_time,
				    after_order_time: this.form.after_order_time,
				    staff: this.form.staff,
				    staff_group: this.form.staff_group,
				    staff_group_name: this.form.staff_group_name,
				    staff_name: this.form.staff_name,
				    recommend_handler_group: this.form.recommend_handler_group,  //推荐处理人分组ID
				    recommend_handler_group_name: this.form.recommend_handler_group_name, //推荐处理人分组名称
				    if_valid_duty: this.form.if_valid_duty, //是否有效售后
                    sales_label: this.form.sales_label,

            if_express_install: this.form.if_express_install,
				    if_three_age: this.form.if_three_age ? 1 : 0,
				    recommend_handler: this.form.best_staff,//推荐处理人id
				    recommend_handler_name: this.form.best_staff_name,//推荐处理人id
				    close_status: this.form.close_status,
				    remark: this.form.service_note,
				    type: /退货再售/.test($('.el-tabs__item.is-active').text()) ? 'RESALE' : 'NORMAL',

				    receiver_name: this.form.receiver_name,
				    receiver_mobile: this.form.receiver_mobile,
				    address_id: this.form.address_id,
				    address_name: this.form.address_name,
				    address_name: this.form.address_name,

				    if_dealer : this.form.if_dealer,//  是否经销商订单(Y是/N否)
					dealer_customer_id :this.form.dealer_customer_id,//  经销商Id
					dealer_customer_number :this.form.dealer_customer_number,//  经销商编码
					dealer_customer_name :this.form.dealer_customer_name,//  经销商名称
					last_update_time :this.form.last_update_time,
          if_gov_subsidy: this.form.if_gov_subsidy,
          		})

				  let aftersaleOrderQuestionRowVO = Object.assign(this.$refs.$badGoods.returnSaveData()).aftersaleOrderQuestionRowVO.filter(item => item.aftersaleOrderQuestion.question_type != 'SUPPLY')
				  postData.aftersaleOrderQuestionRowVO = aftersaleOrderQuestionRowVO
        if(this.form.dealer_customer_id && (postData.aftersaleOrderQuestionRowVO || []).some(
					o => {
						o = o.aftersaleOrderQuestion || {}
						return o.if_has_source === 'Y' && o.if_goods_question === 'Y' && o._dealer_customer_id && o._dealer_customer_id !== this.form.dealer_customer_id
					}
				)){
					this.$message.error('新增有源商品行经销商与基本信息页签经销商不一致，不能保存')
					return
				}

				disabledBtnCb && disabledBtnCb()

				this.ajax.postStream(
					this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/order/save?permissionCode=AFTERSALE_SAVE'),
					postData,
					res => {
						if(isSave || !res.body.content){
							this.$message({
								type:res.body.result?'success':'error',
								message : res.body.msg
							})
						}
						callback && callback(res);
	          		}
          		)

			},
			validateIsPass(){
				var bool;
				this.$refs.form.validate(valid => {
					bool = valid;
				})
				return bool;

			},
			// 保存按钮
			submit (e){
				console.log('保存');
				var $btn = e.currentTarget;
				if(!this.validateIsPass()) {
					$btn.disabled = false;
					return;
				}/*else if (this.personBusinessAttribute.attributeValue && !this.form.merge_trade_id){
					this.$message.error('经销商角色不能建无源的售后单')
					return
				}*/

				this.testAfterSaleLock(() => {
					this.askeSaveData(true,(res)=>{
						if(res.body.content){

							if(!this.params.id){//防止“该售后单已经打开”
								this.params.id = res.body.content.id
								this.blockSameIdOpenTab(this.params.id)
								this.$root.eventHandle.$emit('updateTab', {
									name: this.params.tabName,
									title: '售后单详情' + (this.isReSaleOrder ? '(退货再售)' : ''),
								})
							}

							setTimeout(() => {
		          				// 数据库操作有延迟
								this._initRenderByOrderId(res.body.content.id, 'noMsg', () => {
					          		$btn.disabled = false
                                    this.openQualityFeedbackBtn()//保存时更新是否开放品质反馈按钮
								})
		          			}, 300)
	          			}else {
			          		$btn.disabled = false
	          			}
					}, () => {
						$btn.disabled = true;
					});
				})
			},
			// 选单
			selectMergeOrder() {
				console.log('看看选单的问题');
				this.$root.eventHandle.$emit('alert',{
					title:'选单列表',
					style:'width:800px;height:560px',
					component:()=>import('@components/after_sales/selectMergeOrderList'),
					params: {
						isReSaleOrder: this.isReSaleOrder,
						limit_field: this.params.limit_field, // 新增售后单，选择列表带入默认搜索条件，平台单号 - tid
						field_value: this.params.field_value, // 新增售后单，选择列表带入默认搜索条件，平台单号 - tid
						autoImportByPlatformCode: this.params.autoImportByPlatformCode, // 新增售后单，选择列表带入默认搜索条件，平台单号 - tid
						callback: d => {
							this.form.merge_trade_id = d.merge_trade_id;
							this.form.merge_trade_no = d.merge_trade_no;
				   			this.form.staff = d.user_id; //业务员 id
				   			this.form.staff_name = d.real_name; //业务员
				   			this.form.staff_group_name = d.group_name; //分组
				   			this.form.staff_group = d.group_id; //业务员分组id
				   			this.form.best_staff = d.user_id
							this.form.best_staff_name = d.real_name //推荐处理人
				   			this.form.recommend_handler_group = d.group_id; //推荐处理人分组ID
				   			this.form.recommend_handler_group_name = d.group_name; //推荐处理人分组名称
							this.form.buyer_nick = d.customer_name;
							this.form.buyer = d.customer_id;

							// 收货人
							this.form.receiver_name = d.receiver_name
							this.form.receiver_mobile = d.receiver_mobile
							this.form.address_id = d.address_id
							this.form.address_name = d.address_name

							this.form.if_dealer = d.if_dealer;
							this.form.dealer_customer_id = d.dealer_customer_id;
							this.form.dealer_customer_number = d.dealer_customer_number;
							this.form.dealer_customer_name = d.dealer_customer_name;
							this.form.settle_method = d.settle_method;
              this.form.if_gov_subsidy = d.if_gov_subsidy;

                            if(!!d.sales_label) this.form.sales_label = d.sales_label

				// console.log('this.form.merge_trade_id',this.form.merge_trade_id,this.form.merge_trade_no)
							// 默认当前登录用户
							// this.form.shop_name = d.shop_name;
						}
					},
				});
			},
			viewMergedOrder(id){
			if(!id) return
			var self = this;
			var params = {merge_trade_id :id};
			self.$root.eventHandle.$emit('creatTab',{
				name:"合并订单详情",
				params:params,
				component: () => import('@components/order/merge.vue')
			});
		},
			// 新增客户弹框
			addBuyer (){
				this.$root.eventHandle.$emit('alert',{
					title:'新增客户',
					style:'width:1000px;height:560px',
					component:()=>import('@components/customers/detail'),
				})

				if(!this.addBuyer.isBindEvent){
					this.$root.eventHandle.$on('close_addCustomer', ($alertParams, d) => {
						this.form.buyer_nick = d.name
						this.form.buyer = d.cust_id
						this.$root.eventHandle.$emit('removeAlert', $alertParams.alertId)
					})
					this.addBuyer.isBindEvent = true
				}
			},
			// 买家昵称
			selectBuyersName (){
				this.$root.eventHandle.$emit('alert', {
					component: () => import('@components/customers/list'),
					style:'width:900px;height:600px',
					title:'买家昵称列表',
					params: {
						close: d => {
							this.form.buyer_nick = d.name;
							this.form.buyer = d.cust_id;
							this.getOrClearReceiverInfo(d.cust_id)
						}
					},
				})
				// this.$root.eventHandle.$emit('alert',{
				// 	title:'买家昵称列表',
				// 	style:'width:800px;height:600px',
				// 	component:()=>import('@components/after_sales_common/selectBuyersNameList'),
				// 	params: {
				// 		buyer_nick: this.form.buyer_nick,
				// 	},
				// });
			},
			// 无源时选择买家昵称后设置收货人信息
			getOrClearReceiverInfo (type/* 'get' or 'clear'当买家昵称改变时清空之前收货人信息 */){
				if(type === 'clear'){
					this.form.receiver_name = ''
					this.form.receiver_mobile = ''
					this.form.address_id = ''
					this.form.address_name = ''
				}else {
					this.ajax.postStream('/order-web/api/customer/receiverInfo/getReceiverInfoList', {
						cust_id: type,
						page_no: 1,
						page_size: 0
					}, res => {
						var list1
						if(res.body.result){
							list1 = res.body.content.list[0]

							this.form.receiver_name = list1.receiver_name
							this.form.receiver_mobile = list1.receiver_mobile || list1.receiver_phone
							this.form.address_id = list1.fid
							this.form.address_name = list1.address_name
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}
			},
			// 推荐处理人
			selectBestStaff (){
				this.$root.eventHandle.$emit('alert',{
					title: '推荐处理人列表',
					style:'width:800px;height:600px',
					component:()=>import('@components/after_sales_common/selectPickrecommendhandler'),
					params: {
						// best_staff_name: this.form.best_staff_name,
						callback: d => {
							this.form.best_staff = d.userId
							this.form.best_staff_name = d._real_name_nick_name
							this.form.recommend_handler_group = d.group_id
							this.form.recommend_handler_group_name = d.group_name
						}
					},
				})
			},
			// 业务员选择
			selectBestStaff2 (){
				this.$root.eventHandle.$emit('alert',{
					title: '业务员列表',
					style:'width:800px;height:600px',
					component:()=>import('@components/after_sales_report/select_personel'),
					params: {
						callback: d => {
							this.form.staff = d.userId
							this.form.staff_name = d.fullName
							this.form.staff_group = d.groupId
							this.form.staff_group_name = d.groupName
						}
					},
				})
			},
			_initRenderByOrderId (orderId, isNoMsg, cb){
        // 呼叫按钮获取信息，信息没回来之前禁用呼叫按钮
        this.callBtnGroupsDisabled = true;
        // 重置提交售后单提示
        this.urgentSignErrorTip = '';
				this.ajax.postStream(
					this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/order/detail'),
					{ id: orderId },
					(res) => {
						if(res.body.result){
							!isNoMsg && this.$message.success('售后单加载成功')
							this.init(res.body.content)
							this._$orderDetailIds = res.body.content.aftersaleOrderQuestionRowVO.map((obj) => obj.aftersaleOrderQuestion.goods_code);
							//设置原始数据
							this.originalData = JSON.parse(JSON.stringify(this.form));
							/*
							CREATE: '待办',
							PRE_HANDLE: '售前处理',
							WAIT_HANDLE: '售后待办',
							AFTER_HANDLE: '售后处理',
							FINISH: '完结',
							CANCELLATION: '作废',
							 */

              this.setRights();
              // 呼叫按钮获取信息，信息没回来之前禁用呼叫按钮
              this.callBtnGroupsDisabled = false;

              //物流咨询单和产品咨询有最新的回复内容后，咨询详情tab显示红点提醒
              this.queryReplyFlag(this.params.id,this.form.merge_trade_id)
						}else {
							this.$message.error(res.body.msg)
						}
            Vue.prototype.tabNo[Vue.prototype.activeTab] = res.body.content.after_order_no || '';
            let pageParams = {
                id: res.body.content.id
            }
            Vue.prototype.tabId[Vue.prototype.activeTab] = JSON.stringify(pageParams);
            console.log("Vue",Vue.prototype.tabNo);
						cb && cb()
					}
				)
			},
			/**
			*前端设置可编辑权限
			***/
			setRights(){
				var status = this.form.status
				,	isSameLocker = this.form.otherInfo.locker == this.getEmployeeInfo('id')/* || this.form.if_dealer === 'Y'*//*经销商订单任何人都有编辑权限*/
				//业务状态为售前处理或是售后处理
				this.hasRightsToEidtSolution = isSameLocker && (status == 'PRE_HANDLE' || status == 'AFTER_HANDLE')
                this.isHasRights2Edit = !this.form.after_order_no ? true : isSameLocker && status != 'FINISH'

                //当前系统代理人为售后单业务锁定人或拥有“品质反馈专员“业务员类型
                // let codeArr=this.employeeGroupCodeList.map(item=>{
                //     return item.salesmanType
                // })
                // console.warn('codeArr',codeArr,'islock',isSameLocker,'includes',codeArr.includes('PROBLEM_FEEDBACK_SPECIALIST'))
                // this.isSubmitQualityFeedback=isSameLocker||codeArr.includes('PROBLEM_FEEDBACK_SPECIALIST')
			},
			//买家昵称、业务员等发生改变后立即搜索
			searchAfterChange (e, title){
				var self = this;
				console.log(e.target,'e.target--------------------------------');
				if(!e.target.value) return
				if(title === '买家昵称'){
					this.ajax.postStream(
						this._delPermissionCodeWhenDealerUser('/order-web/api/customer/list?permissionCode=CUSTOMER_QUERY'),
						{
							page_name: 'bd_customer',
							where: [],
							number:'',   //编码
							name: e.target.value,	//名称
							account:'',			//电商账号
							member_type:'',		  //客户类型
							effective_status:'',     //有效
							low_create_date:'',   //开始时间
							upper_create_date:'',   //结束时间
							page_no:1   //页码
						},
						_successCb
					)
				}else if (title === '业务员'){
					this.ajax.postStream('/user-web/api/userPerson/getUserPersonSalesmanList', {
						page: {"length":10,"pageNo":1},
						realName: e.target.value,
						nickName:e.target.nickName || null,
					}, _successCb)
				}

				function _successCb (res){
					var d = ((res.body.content && res.body.content.list) || [])[0]

					if(d && title === '业务员'){//有可能会返回多个值，取当前账号相近的第一个
						d = res.body.content.list
							.filter(obj => obj.employeeNumber === self.getEmployeeInfo('employeeNumber'))
							.sort((a, b) => {//取groupName有值的第一个，null排在最后
								if(a.groupName === null){
									return 1;
								}else if(b.groupName === null){
									return -1;
								}else if(a.groupName === b.groupName){
									return 0;
								}else {
									return a.groupName < b.groupName ? -1 : 1;
								}
							})
							[0]
					}
					console.log(d);
					if(d){
						if(title === '买家昵称'){
							self.form.buyer_nick = d.name
							self.form.buyer = d.cust_id
							self.getOrClearReceiverInfo(d.cust_id)
						}else if (title === '业务员'){
							self.form.staff = d.userId
							self.form.staff_name = d.fullName
							self.form.staff_group = d.groupId
							self.form.staff_group_name = d.groupName
						}
					}else {
						self.$message.error(title + '查找匹配失败')
						if(title === '买家昵称'){
							self.form.buyer_nick = ''
							self.getOrClearReceiverInfo('clear')
						}else if (title === '业务员'){
							self.form.staff_name = self.form.staff_group_name = ''
						}
						e.target.value = ''
					}
				}
			},
			// 完结按钮状态判断
			_closeBtnStatus (){
				/*
				1、  (业务状态 = 售前处理 or 业务状态 = 售后处理) && 业务锁定人 = 登录用户（或代理人），可以操作完结
				2、  已完结的不可再点完结。
				 */

				if(this.getEmployeeInfo('id') === this.form.otherInfo.locker && /(PRE_HANDLE|AFTER_HANDLE)/.test(this.form.status) && !this.form.isClosed){
					return false
				}

				return true
			},
			// 上一页 or 下一页
			nextOrPrevOrder (type){
				this.refresh('onlyCompareData', null, () => {
					;(this.params.idList || []).some((id, index) => {
						if(id === this.params.id){
							var newOrderId = this.params.idList[index + (type === 'next' ? 1 : -1)]

							if(newOrderId){
								this.blockSameIdOpenTab(newOrderId, () => {
									this.params.id = newOrderId
									this.selectTabTwo = 'badGoods'
									this._initRenderByOrderId(newOrderId)
								})
							}else {
								this.$message.error('没有更多')
							}
							return true
						}
					})
				})
			},
			getBuyerNickEdit (){
				var salesmanTypeList = ['SALES_PERSON', 'CUSTOMER_SERVICE_EXECUTIVE', 'CUSTOMER_SERVICE_ABC']
				,	personId = this.getEmployeeInfo('personId')
				,	queryFunc = () => {
					var salesmanType = salesmanTypeList.shift()
					if(!salesmanType) return

					this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttribute', {
						personId,
						salesmanType,
						attribute: 'IF_CHOOSE_BUYER',
					}, res => {
						if(res.body.result && res.body.content && res.body.content.attributeValueText === '是'){
							this.buyerNickEdit = true
						}else {
							queryFunc()
						}
					})
				}

				queryFunc()
			},
			// 查看4PL客户上传图片
			watchUploadFile (){
				this.$root.eventHandle.$emit('alert', {
					params: {
						parent_name_txt: '售后单号',
						parent_no : this.form.after_order_no,
						child_name : 'QUESTION_GOODS',
						child_no : null,
						ext_data : null,
						parent_name : 'AFTER_ORDER',
						notNeedDelBtn: this.form.isClosed || !this.isHasRights2Edit,
						nickname: this.form.buyer_nick,
						mergeTradeId: this.form.merge_trade_id,
						isFromAfterSaleIndex: true,
					},
					component: ()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
					style: 'width:80%;height:600px',
					title: '下载列表',
				})
			},
			updateAfterHandler(){
				var self = this;
				var postData=[this.form.after_order_id];

				let params = {};
				params.isSelect = true;
				params.callback=(d)=>{
					this.ajax.postStream('/afterSale-web/api/aftersale/order/updateAfterHandler?permissionCode=UPDATE_AFTERSALE_HANDER', {
            userId:d.userId,
            userName:d.fullName,
						afterOrderIds:postData
					}, d => {
							if (d.body.result) {
								this.$message.success(d.body.msg);
								self._initRenderByOrderId(this.form.after_order_id);
							}else{
								this.$message.error(d.body.msg);

							}
						}, (e) => {
							this.$message.error(e);
							resolve && resolve();
						})
					}
					self.$root.eventHandle.$emit('alert',{
					close: function(){},
					style:'width:900px;height:600px',
					title:"业务员列表",
					params:params,
					component: () => import('@components/after_sales/distribute/selectRecommendList.vue')
				});
            },
            // getEmployeeInfoGroupList(){
            // //获取当前处理人的业务信息列表
            // return new Promise((resolve,reject)=>{
            //      this.ajax.postStream('/user-web/api/userPerson/getUserPersonGroupList',{personId:this.getEmployeeInfo('personId')}, res=>{
            //         if(res.body.result && res.body.content){
            //         this.employeeGroupCodeList = res.body.content.list||[];
            //         console.warn(this.employeeGroupCodeList)
            //         resolve();
            //         } else {
            //         res.body.msg && this.$message.error(res.body.msg);
            //         resolve();
            //         }
            //     }, err => {
            //         this.$message.error(err);
            //         reject();
            //     });
            // })
            // },
            // 是否开放品质反馈按钮
            openQualityFeedbackBtn(){
                //当前系统代理人为售后单业务锁定人或勿动里拥有提交品质反馈权限
                //当前代理人是否锁定人
                let isSameLocker=this.form.otherInfo.locker == this.getEmployeeInfo('id')
                console.log('isSameLocker',isSameLocker)
                if(isSameLocker){
                    this.isSubmitQualityFeedback=true
                    return
                }
                //过滤失效的业务员
                // let nowTime=new Date().getTime();
                // let enAbledEmployeeGroupCodeList=this.employeeGroupCodeList.filter(item=>(!item.disableTime||item.disableTime>nowTime));
                //业务员编码列表
                // let codeArr=enAbledEmployeeGroupCodeList.map(item=>{
                //     return item.salesmanType
                // })
                // this.isSubmitQualityFeedback=isSameLocker||codeArr.includes('PROBLEM_FEEDBACK_SPECIALIST')
                // this.isSubmitQualityFeedback=isSameLocker

                //勿动提交品质反馈权限
                this.ajax.postStream('/afterSale-web/api/aftersale/order/qualityfeedback/checkHavePowerSubmitFeedBack',{},res=>{
                    if(res.body.result){
                        this.isSubmitQualityFeedback=true
                    }else{
                        this.isSubmitQualityFeedback=false
                    }
                },err=>{
                    this.$message.error(err)
                    this.isSubmitQualityFeedback=false
                })
            },
            initAfterSaleOrder(merge_trade_no) {
            const params = {
              "field_value": merge_trade_no,
              "limit_field": "merge_trade_no",
              "operator": "=",
              "page_no": 1,
              "page_size": 20
            }
            this.ajax.postStream('/afterSale-web/api/aftersale/order/choose/pickmergetradeorder', params, res => {
              if (res.body.result) {
                const d = res.body.content.list[0]
                if(!d.merge_trade_id){
                  return
                }
                this.form.merge_trade_id = d.merge_trade_id;
                this.form.merge_trade_no = d.merge_trade_no;
                this.form.staff = d.user_id; //业务员 id
                this.form.staff_name = d.real_name; //业务员
                this.form.staff_group_name = d.group_name; //分组
                this.form.staff_group = d.group_id; //业务员分组id
                this.form.best_staff = d.user_id
                this.form.best_staff_name = d.real_name //推荐处理人
                this.form.recommend_handler_group = d.group_id; //推荐处理人分组ID
                this.form.recommend_handler_group_name = d.group_name; //推荐处理人分组名称
                this.form.buyer_nick = d.customer_name;
                this.form.buyer = d.customer_id;

                // 收货人
                this.form.receiver_name = d.receiver_name
                this.form.receiver_mobile = d.receiver_mobile
                this.form.address_id = d.address_id
                this.form.address_name = d.address_name


                this.form.if_dealer = d.if_dealer;
                this.form.dealer_customer_id = d.dealer_customer_id;
                this.form.dealer_customer_number = d.dealer_customer_number;
                this.form.dealer_customer_name = d.dealer_customer_name;
                this.form.settle_method = d.settle_method;

                if (!!d.sales_label) this.form.sales_label = d.sales_label
              } else {
                this.$message.error(res.body.msg)
              }
            })
            }
		},
		mounted (){
      this.urgentSignOptions = __AUX.get('SHDBQ');
            let self=this;
        console.log('this.params', this.params)
      if(this.params.from==='interceptGoodsRequestLIst' || this.params.from === 'BATCH_TRADE'){
        const merge_trade_no=this.params.merge_trade_no;
        this.initAfterSaleOrder(merge_trade_no);
      }
      if (this.params.from==='reverseConsultation') {
        const merge_trade_no=this.params.merge_trade_no;
        console.log('sss-reverseConsultation-reverseConsultation', merge_trade_no)
        this.initAfterSaleOrder(merge_trade_no);
      }
			if(this.params.id){
				this._initRenderByOrderId(this.params.id,'',()=>{
                        self.openQualityFeedbackBtn()
                })
				this.blockSameIdOpenTab(this.params.id)
			}else {
				this.getBuyerNickEdit()
			}
			this.params.__close = this.refresh;
			//监听切换业务代理事件
			this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
				this.setRights();
                this.getBuyerNickEdit();
                this.openQualityFeedbackBtn()
			});
			// 如果params有平台单号，自动选单
			if(this.params.limit_field == 'tid' && this.params.field_value){
				this.selectMergeOrder()
			}
		},
		destroyed(){
			this.$root.offEvents('close_addCustomer')
		},
	}
</script>

<style module>
.row-height :global(.el-form-item__content) {
	height: auto!important;
	white-space: nowrap;
}
.addBuyerBtn {
	font-weight: bold;
    cursor: pointer;
    color: red;
    font-size: 20px!important;
    vertical-align: -2px;
}
.message-box :global(.el-message__group) p {
    white-space: pre-wrap;
}
.message-box :global(.el-message__img) {
    height: 100%;
    top: 50%;
    transform: translateY(-50%);
    background-color: #ff4949;
}
.message-box :global(.el-message__group) {
    height: auto;
}
.tab-pane-item {
  display:flex;
  align-items: center;
}
.error-tip {
  color: red;
}
.urgent-sign-error :global(input) {
  border-color: red;
}
</style>
