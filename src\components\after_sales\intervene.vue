<template>
	<div>
		<el-row class="mgt30" style="margin: 10px;">
			<el-col :span="24">
				<el-button class="mgr60" type="primary" size="mini" @click="ensureToMerge">提交</el-button> 
				<!-- <el-button class="mgr60" type="danger" size="mini" @click="close">取消</el-button>  -->
			</el-col>
			
		</el-row>
		<el-row>
			<el-form :model="form" class="mgt30" :rules="rules" ref="form">
				<el-form-item label="创建人:" label-width="100px" prop="intervene_type">
					<el-input v-model='creater' type="text" size="mini" disabled></el-input>
				</el-form-item>
				<el-form-item label="创建时间:" label-width="100px" prop="intervene_type">
					<el-date-picker :value="creatTime"  type="datetime" size="mini"  disabled></el-date-picker>
					
				</el-form-item>
				<el-form-item label="介入类型:" label-width="100px" prop="intervene_type">
					<xpt-select-aux v-model='form.intervene_type' aux_name='jrlx' ></xpt-select-aux>
					
					<el-tooltip v-if='rules.intervene_type[0].isShow' effect="dark" :content="rules.intervene_type[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
				<el-form-item label="需介入原因:" label-width="100px" prop="reason">
					<el-input v-model='form.reason' type="textarea"></el-input>
					<el-tooltip v-if='rules.reason[0].isShow' effect="dark" :content="rules.reason[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-form>
		</el-row>
		
	</div>
</template>
<script>
	import validate from '@common/validate.js'
	export default {
		props:["params"],
		data(){
			var self = this;
			return {
				creater:'',
				creatTime:new Date(),
				form:{
					parent_id:null,
					intervene_type:null,
					reason:null
				},
				rules:{
					reason:validate.isNotBlank({required:true,self:self}),
					intervene_type:validate.isNotBlank({required:true,self:self})
				},
				

			}
		},
		methods:{
			// close(){
			// 	let self=this;
			// 	self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
			// },
			ensureToMerge(){
				var self = this
				self.$refs.form.validate((valid) => {
					if(!valid) return
					this.ajax.postStream('/afterSale-web/api/aftersale/consultion/interveneSave', self.form, res => {
						if(res.body.result){
							self.params.callback&&self.params.callback();
							self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
							self.$message.success(res.body.msg);
						}else{
							self.$message.error(res.body.msg);
						}
					})
				})
			},
		
		},
		
		mounted:function(){
			let self = this;
			console.log(self.params);
			self.form.parent_id = self.params.parentsId;
			self.creater = self.getEmployeeInfo('fullName');
		}
	}
</script>
