export default {
  data() {
    return {
      ifConsultReplyTag: false,//物流咨询单和产品咨询有最新的回复内容后，咨询详情tab显示红点提醒
    }
  },
  methods: {

    // 是否可以点击满意度评价
    canSatisfaction(row) {
      // 当前业务代理人=当前提交人且单据状态=待确认
      return row.order_status === 'WAITCONFIRM' && row.submit_id == this.getEmployeeInfo('id');
    }, dissatisfyDialog(consultId, callback) {
      this.$root.eventHandle.$emit("alert", {
        title: "不满意评价",
        style: "width:400px;height:300px",
        component: () =>
          import("@components/after_sales/dissatisfyDialog.vue"),
        params: {
          consultionId: consultId,
          callback: (d) => {
            callback && callback();
          },
        },
      });
    },
    handleLogicalSatisfy(consultId, yOrN, callback) {
      const index = this.consultionList.findIndex(item => item.consultion_id === consultId)
      if (yOrN === 'N') {
        this.dissatisfyDialog(consultId)
      } else {
        this.consultionList[index].satisfyLoading = true;
        this.ajax.postStream('/afterSale-web/api/aftersale/consultion/manyCommitSatisfactionToZd', { consultion_satisfaction: yOrN, consultion_id: consultId }, res => {
          if (res.body.result) {
            this.consultionList[index].satisfyLoading = false;
            this.$message.success(res.body.msg || '操作成功')
            callback && callback()
          } else {
            this.$message.error(res.body.msg)
            this.consultionList[index].satisfyLoading = false;
          }
        })
      }
    },
    handleProductSatisfy(consultId, callback) {
      this.$root.eventHandle.$emit('alert', {
        title: '满意度评价',
        style: 'width:500px; height:200px',
        params: {
          pric_id: consultId,
          callback: () => {
            callback && callback();
          },
        },
        component: () => import('@components/product_consulting/alert/statisfaction.vue'),

      })
    },
    queryReplyFlag(id, mergeTradeId, callback) {
      const params={
        id,
        merge_trade_id:mergeTradeId,
      }
      this.ajax.postStream(`/afterSale-web/api/aftersale/order/bill/getReplyReadFlag`,params, res => {
        if (res.body.result) {
          this.ifConsultReplyTag = res.body.content;
          callback && callback()
        } else {
          this.$message.error(res.body.msg)
          this.ifConsultReplyTag = false;
        }
      }, () => {
        this.ifConsultReplyTag = false;
      })
    }
  }
}
