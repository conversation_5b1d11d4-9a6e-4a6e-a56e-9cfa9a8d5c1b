<!-- 操作记录 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-flex__bottom">
			<el-table border :data='list' tooltip-effect="dark" width='100%' style="width: 100%;" >
			    <el-table-column type="index" width="50" label="序号"></el-table-column>
				<el-table-column label="用户" prop="operator_name"></el-table-column>
				<el-table-column label="业务操作" prop="operate_type"></el-table-column>
				<el-table-column label="操作描述" prop="description"></el-table-column>
				<el-table-column label="操作时间" prop="operate_time">
					<template slot-scope="scope"><span>{{scope.row.operate_time | dataFormat1}}</span></template>
				</el-table-column>

			</el-table>
		</el-row>
	</div>
</template>
<script>
	export default {
		props: ['params'],
		data() {
			return {
				list: []
			}
		},
		methods: {
			init (orderId){
				if(orderId){
					this.ajax.postStream('/afterSale-web/api/aftersale/order/operationRecord/list', { id: orderId }, res => {
						this.list = res.body.content.list
					})
				}else {
					this.list = []
				}
			}
		},
		// watch:{
		// 	'data': {
		// 		handler: function(list) {
		// 			this.list = list;
		// 		},
		// 		deep: true
		// 	}
		// }
	}
</script>
