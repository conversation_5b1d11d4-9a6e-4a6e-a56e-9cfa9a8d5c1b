<!-- 订单详情 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-flex__bottom">
			<el-table border :data='list' tooltip-effect="dark" width='100%' style="width: 100%;">
			    <el-table-column type="index" width="50" label="序号"></el-table-column>
				<el-table-column label="合并单号" prop="merge_trade_no" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="订单店铺" prop="shop_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="客服店铺" prop="user_shop_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="原始店铺" prop="original_shop_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="淘宝单号" prop="tid" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="批次单号" prop="batch_trade_no" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="销售订单号" prop="sys_trade_no" width="200" show-overflow-tooltip>
					<template slot-scope="scope">
							<a style="text-decoration:none;" href="javascript:;" @click="viewdOrder(scope.row.sys_trade_id)" title="点击进入销售订单详情">{{scope.row.sys_trade_no}}</a>
					</template>

				</el-table-column>
				<el-table-column label="订单金额" prop="stand_price" align="right" header-align="left" show-overflow-tooltip ></el-table-column>
				<el-table-column label="实际发货时间" width="200" show-overflow-tooltip>
					<template slot-scope="scope">
						<span>{{scope.row.out_stock_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<el-table-column label="承诺发货时间" width="200" show-overflow-tooltip>
					<template slot-scope="scope">
						<span>{{scope.row.commit_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<el-table-column label="物流商" prop="logistics_supplier_name" width="200" show-overflow-tooltip></el-table-column>
				<!-- <el-table-column label="业务员" prop="staff_name" ></el-table-column>
				<el-table-column label="发货时间" prop="out_stock_time" >
					<template slot-scope="scope">
						<span>{{scope.row.out_stock_time | dataFormat1}}</span>
					</template>
				</el-table-column> -->
				<!-- <el-table-column label="淘宝退款" prop="cost_price">
					<template slot-scope="scope">
						<span>{{ scope.row.cost_price && (scope.row.cost_price | number)}}</span>
					</template>
				</el-table-column> -->
			</el-table>
		</el-row>
	</div>
</template>
<script>
	export default {
		data() {
			return {
				list: []
			}
		},
		methods: {
			viewdOrder(id){
				let self = this;
				let params = {};
				params.sys_trade_id = id;
				self.$root.eventHandle.$emit('creatTab', {
					name: "销售订单详情",
					params: params,
					component: () => import('@components/order/detail.vue')
				});
			},
			init ({ids, merge_trade_id}){
				if(merge_trade_id){
					if(ids && ids.length){
						this.ajax.postStream('/afterSale-web/api/aftersale/order/involveOrderDetail/list', {
							material_number: ids,
							merge_trade_id: merge_trade_id,
							page_no:1,
							page_size: ids.length
						}, res => {
							this.list = res.body.content.list
						})
					}
				}else {
					this.list = []
				}
			},
		},
		// mounted() {

		// },
		// destroyed() {
		// }
	}
</script>
