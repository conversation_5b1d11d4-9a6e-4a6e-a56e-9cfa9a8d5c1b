<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='primary' size='mini' @click="confirm">确定</el-button>
        <el-button type='primary' size='mini' @click="cancel">取消</el-button>
      </el-col>
    </el-row>
    <el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px"> 
      <el-col :span="12" style="width: 100%">
        <el-form-item label="截止时间：" required>
          <el-date-picker v-model="form.dead_line" type="datetime" placeholder="选择时间" size="mini" style="width:250px;" :editable="false"  :picker-options='endDateFormatFun'></el-date-picker>
          <el-tooltip v-if='rules.dead_line[0].isShow' class="item" effect="dark" :content="rules.dead_line[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="添加接收人：" required>
          <el-input size='mini' icon='search' style='width: 250px'
							v-model='form.receiver_names'
							:on-icon-click='searchCondition'
              readonly
							@change="changeCondition"
               placeholder="请选择接收人" 
							></el-input>
          <el-tooltip v-if='rules.receiver_names[0].isShow' class="item" effect="dark" :content="rules.receiver_names[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
       <el-form-item  label="删除接收人：">
         <el-tag :key="tag.employeeNumber" type='primary' v-for="(tag, idx) in dynamicTags" :closable="true" :close-transition="false" @close="handleClose(tag,idx)" > {{tag.fullName}}</el-tag>
       </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>

<script>
import VL from '@common/validate.js'
export default {
  props:["params"],
  data(){
    let self = this;
    return {
      form: {
        dead_line: '',
        receiver_names: '',
        receiver_no: [],
        after_order_id: '',
        id: ''
      },
      dynamicTags: [],
      rules: {
        ...[{'dead_line': '截止时间'}, {'receiver_names': '接收人'}].reduce((obj, b) => {
          let key = Object.keys(b)[0]
          obj[key] = VL.isNotBlank({
            self: this,
            msg: '请填写' + b[key],
          })
          return obj
        }, {})
      },
      endDateFormatFun:{
        disabledDate: time => {
            /*新增时能选所有时间*/
            return self.form.enable_time ? time.getTime() < new Date().getTime() : false
        },
				date:(function(){
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth()+1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
					return new Date(time);
        })()
			},
    }
  },
  methods:{
    // 关闭标签
    handleClose(tag, idx) {
      this.dynamicTags.splice(idx, 1)
      this.form.receiver_no.forEach((item,idx) => {

      })
      let regValue = `${tag.fullName},`
      this.form.receiver_names = this.form.receiver_names.replace(new RegExp(regValue), '')
      let list = JSON.parse(JSON.stringify(this.form.receiver_no)), currentIdx
      list.some((item, idx) => {
        if (item.employeeNumber === tag.employeeNumber) {
          currentIdx = idx
        }
      })
      this.form.receiver_no.splice(currentIdx, 1)
    },
    confirm(){
        this.$refs.form.validate((valid) => {
          if(!valid) return
          let postData = {
            after_order_id: this.params.afterOrderId,
            dead_line: this.form['dead_line'],
            receiver_no: this.form['receiver_no']
          }
          !!this.form.id ? postData.id = this.form.id : ''
          this.ajax.postStream('/afterSale-web/api/aftersale/order/outsideReminder', postData, (res)=> {
            if (res.body.result) {
              this.$message.success(res.body.msg)
              this.params.callback()
              this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
            } else {
              this.$message.error(res.body.msg)
            }
          })
        })
    },
    cancel(){
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    },
    changeCondition(){
      this.form.receiver_no = ''
      this.form.receiver_names = ''
    },
    searchCondition(){
      let self = this;
      this.$root.eventHandle.$emit('alert',{
        component:()=>import('@components/personel/list.vue'), 
        close:function(){},
        style:'width:800px;height:500px',
        title:'请选择接收人信息',
        params:{
          // type:'EMPLOYEE',
          status:1,//生效人员
          isEnable:1,//生效时间
          selection: 'checkbox',
          page_name: 'cloud_user_person',
          where: [],
          callback(b){
            if (Number(self.dynamicTags.length) + Number(b.data.length) > 3) {
              self.$message.error('最多只可以通知三人')
              return
            }
            let personId = [],personString = ''
            self.dynamicTags = self.dynamicTags.concat(...b.data)
            self.dynamicTags.forEach((item,idx) => { 
              personId.push(item.employeeNumber)
              personString += item.fullName + ','
            })
            // if (personString[personString.length - 1] == ',') {
            //     personString = personString.substring(0, personString.length-1)
            // }
            self.form.receiver_no = personId
            self.form.receiver_names = personString
          }
        }
      });
    },
    // 获取详情
    getOutsideReminderDetail () {
      let postData = {
        after_order_id: this.params.afterOrderId
      }
      this.ajax.postStream('/afterSale-web/api/aftersale/order/outsideReminderDetail', postData, (res)=> {
        if (res.body.result && res.body.content) {
          this.$message.success(res.body.msg)
          let data = res.body.content
          this.form.dead_line = data['dead_line'] || ''
          this.form.receiver_no = data['receiver_no'] || []
          this.form.after_order_id = data['after_order_id'] || ''
          this.form.id = data.id || ''
          let dynamicTags = []
          if (data['receiver_no'].length > 0) {
            this.form.receiver_names = data['receiver_no'].reduce((strings, b) => {
              strings = data['receiver_names'][b]
              dynamicTags.push({'employeeNumber': b, 'fullName': data['receiver_names'][b]})
              return strings
            }, '')
            this.dynamicTags = dynamicTags
          }
        } else {
          this.$message.error(res.body.msg)
        }
      })
    },
    initData() {
      let data = this.params.dataForm
      this.form.dead_line = data['dead_line'] || ''
      this.form.receiver_no = data['receiver_no'] || []
      this.form.after_order_id = data['after_order_id'] || ''
      this.form.id = data.id || ''
      let dynamicTags = []
      if (data['receiver_no'] && data['receiver_no'].length > 0) {
        this.form.receiver_names = data['receiver_no'].reduce((strings, b) => {
          strings = strings + data['receiver_names'][b] + ','
          dynamicTags.push({'employeeNumber': b, 'fullName': data['receiver_names'][b]})
          return strings
        }, '')
      }
      this.dynamicTags = dynamicTags
    }
  },
  mounted(){
    this.after_order_id = this.params.afterOrderId
    this.initData()
  }
}
</script>

<style scoped>

</style>
