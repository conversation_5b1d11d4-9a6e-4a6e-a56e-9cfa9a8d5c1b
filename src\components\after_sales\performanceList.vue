<!-- 售后单业绩页签 -->
<template>
<div class="xpt-flex">
	<el-row class="xpt-flex__bottom">
		<el-table border :data='list' tooltip-effect="dark" width='100%' style="width: 100%;" >
			<el-table-column label="单据编号" prop="sys_trade_no" width="170" show-overflow-tooltip></el-table-column>
			<el-table-column label="合并单号" prop="merge_trade_no" width="170" show-overflow-tooltip></el-table-column>
			<el-table-column label="拍单时间" prop="created" width="170" show-overflow-tooltip>
				<template slot-scope="scope"><span>{{scope.row.created | dataFormat1}}</span></template>
			</el-table-column>
			<el-table-column label="支付时间" prop="pay_time" width="170" show-overflow-tooltip>
				<template slot-scope="scope"><span>{{scope.row.pay_time | dataFormat1}}</span></template>
			</el-table-column>
			<el-table-column label="订单店铺" prop="shop_name" width="110" show-overflow-tooltip></el-table-column>
			<el-table-column label="客服店铺" prop="user_shop_name" width="110" show-overflow-tooltip></el-table-column>
			<el-table-column label="原始店铺" prop="original_shop_name" width="110" show-overflow-tooltip></el-table-column>
			<el-table-column label="买家昵称" prop="customer_name" show-overflow-tooltip></el-table-column>
			<el-table-column label="业务员" prop="staff_name" show-overflow-tooltip></el-table-column>
			<el-table-column label="淘宝单号" prop="tid" width="170" show-overflow-tooltip></el-table-column>
			<el-table-column label="订单支付金额" prop="amount" show-overflow-tooltip></el-table-column>
		</el-table>
	</el-row>
</div>
</template>
<script>
	export default {
		props: ['params'],
		data() {
			return {
				list: []
			}
		},
		methods: {
			init (orderId){
				if(orderId){
					this.ajax.postStream('/afterSale-web/api/aftersale/order/performance/list', { id: orderId }, res => {
						this.list = res.body.content.list
						if(this.list.length){
							this.list.push({
								amount: '合计' + this.list.reduce((num, obj) => num + Number(obj.amount), 0)
							})
						}
					})
				}else {
					this.list = []
				}
			},
		},
	}
</script>
