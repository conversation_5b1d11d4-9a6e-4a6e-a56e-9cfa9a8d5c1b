<!-- 方案进度 -->
<template>
	<div>
		<div style="margin-top: 60px;margin-left: 60px;white-space:nowrap;">
			<el-button size="big">售后单</el-button>
			<template v-for="v in firstList">
				<span style="display: inline-block; width: 120px;height:2px; border:1px solid #000"></span>
				<el-button size="big" style="position: relative;">{{v.plan_type_string}}单 <span>{{v.index}}</span><span style="dispaly:inline-block;widht:20px;heiht:20px;"></span><span style="position: absolute; left:20px; top: 40px; ">{{v.plan_bill_status_string}}</span></el-button>
			</template>
			<template v-for="v in secondList">
				<span style="display: inline-block; width: 60px;height:2px; border:1px solid #000"></span>

				<el-button size="big" style="position: relative; margin-right: 5px;">{{v.plan_type_string}}单 {{v.index}}<span style="position: absolute; left:20px; top: 40px; ">{{v.plan_bill_status_string}}</span></el-button>

			</template>
		</div>
		<div style="margin-left: 194px;margin-top: 60px; position: relative; white-space:nowrap;" v-for="itemList in orderList">
			<span style="display: inline-block; position: absolute; left: 0px; top: -74px; width: 2px;height:93px; border:1px solid #000"></span>

			<template v-for="v in itemList">
				<span style="display: inline-block; width: 60px;height:2px; border:1px solid #000"></span>

				<el-button size="big" style="position: relative;  margin-right: 5px;">{{v.plan_type_string}}单 {{v.index}}<span style="position: absolute; left:20px; top: 40px; ">{{v.plan_bill_status_string}}</span></el-button>

			</template>

		</div>

	</div>
</template>
<script>
	export default {
		props: ['params'],
		data() {
			return {
				firstList: [],
				secondList: [],
				orderList: []
			}
		},
		mounted() {
		},
		watch:{
			'params.planSpeedVO':{
				handler:function(d){
					if(d == null || d.length === 0)return;
					this.firstList = [];
					this.secondList = [];
					this.orderList = [];
					d.map((v,index) => {
						v.index = index+1;
						if(index === 0 ) {
							this.firstList.push(v);
						}else{
							if(v.plan_type == this.firstList[0].plan_type) {
								this.secondList.push(v);
							}else{
								var bool = true;
								this.orderList.map(v1 => {
									if(v.plan_type == v1[0].plan_type) {
										v1.push(v);
										bool = false;
										return;
									}
								})
								if(bool){
									let list = [];
									list.push(v);
									this.orderList[this.orderList.length] = list;
								}
							}
						}
					})
				},
				deep:true
			}
		},
		destroyed() {
		}
	}
</script>
