<!-- 退款结算单的差价商品 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-flex__bottom">
			<el-table border :data='list' tooltip-effect="dark" width='100%' style="width: 100%;" >
				<el-table-column label="商品编码" prop="material_number"></el-table-column>
				<el-table-column label="商品名称" prop="material_name"></el-table-column>
				<el-table-column label="规格描述" prop="material_desc"></el-table-column>
				<el-table-column label="拍下价格" prop="buying_price" align="right" header-align="left" ></el-table-column>
				<el-table-column label="活动价" prop="act_price" ></el-table-column>
				<el-table-column label="淘宝单号" prop="tid" ></el-table-column>
				<el-table-column label="订单状态" prop="order_status">
					<template slot-scope="scope">
						<span>{{status[scope.row.order_status]}}</span>
					</template>
				</el-table-column>
				<el-table-column label="单品差价" prop="price_spread"></el-table-column>
				<el-table-column label="店铺" prop="price_shop_name"></el-table-column>
				<el-table-column label="购买时间" prop="buy_time">
					<template slot-scope="scope">
						<span>{{scope.row.buy_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<el-table-column label="单位" prop="unit_name"></el-table-column>
				<el-table-column label="生效日期" >
					<template slot-scope="scope">
						<span>{{scope.row.enable_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<el-table-column label="失效日期" >
					<template slot-scope="scope">
						<span>{{scope.row.disable_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<el-table-column label="备注" prop="description"></el-table-column>
			</el-table>
		</el-row>
	</div>
</template>
<script>
	import Fn  from '@common/Fn.js'
	export default {
		props: ['params','data'],
		data() {
			return {
				list: [],
				status:{
					'CANCEL': '取消',
					'NORMAL': '生效',
					'WAITING': '等待发运',
					'PART_DELIVERED': '部分发运中',
					'DELIVERED': '已发运'
				}
			}
		},
		methods: {

		},
		mounted() {

		},
		watch:{
			'data': {
				handler: function(list) {
					this.list = list;
				},
				deep: true
			}
		}
	}
</script>
