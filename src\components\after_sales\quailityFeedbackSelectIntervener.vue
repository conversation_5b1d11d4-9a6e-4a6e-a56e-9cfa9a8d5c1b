<template>
    <div class="xpt-flex">
        <xpt-list
            :btns="userBtns"
            :data="userList"
            :colData="userCols"
            searchHolder="请输入工号或姓名或昵称"
            @search-click="presearch"
            :pageTotal="total"
            :pageLength="search.page.length"
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
            selection="radio"
            @radio-change="radioChange"
        ></xpt-list>
    </div>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            userList: [],
            userBtns: [
                {
                    type: "info",
                    txt: "确认",
                    click: () => {
                        self.params.callback(self.currentUserInfo);
                        self.$root.eventHandle.$emit(
                            "removeAlert",
                            self.params.alertId
                        );
                    },
                    disabled: true,
                },
            ],
            userCols: [
                {
                    label: "工号",
                    prop: "employeeNumber",
                },
                {
                    label: "姓名",
                    prop: "realName",
                },
                {
                    label: "昵称",
                    prop: "nickName",
                },
                {
                    label: "业务员",
                    prop: "",
                    formatter: () =>"品质反馈专员"
                }
            ],
            total: 0,
            search: {
                page: { length: 50, pageNo: 1 },
                page_name: "cloud_user_login",
                where: [],
                isEnable: 1,
                salesmanType: "PROBLEM_FEEDBACK_SPECIALIST",
            },
            currentUserInfo: {},
        };
    },
    mounted() {
        console.log(this.params);
        this.getQualityFeedbackUserList();
    },
    methods: {
        presearch(key, resolve) {
            this.search.key = key;
            this.getQualityFeedbackUserList(resolve);
        },
        getQualityFeedbackUserList(resolve) {
            let self = this;
            let params = this.search;
            this.ajax.postStream(
                "/user-web/api/userLogin/getUserLoginListBySaleMaleType",
                params,
                (res) => {
                    if (res.body.result) {
                        res.body.msg ? this.$message.success(res.body.msg) : "";
                        this.userList = res.body.content.list;
                        this.total = res.body.content.count;
                    } else {
                        this.$message.error(res.body.msg);
                    }
                    resolve && resolve();
                },
                (err) => {
                    this.$message.error(err);
                    resolve && resolve();
                }
            );
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page.length = size;
            this.getQualityFeedbackUserList();
        },
        pageChange(pageNo) {
            // 页数改变
            this.search.page.pageNo = pageNo;
            this.getQualityFeedbackUserList();
        },
        radioChange(data) {
            console.log(data);
            this.currentUserInfo = data;
            if (Object.keys(this.currentUserInfo).length) {
                this.userBtns[0].disabled = false;
            }
        },
    },
};
</script>