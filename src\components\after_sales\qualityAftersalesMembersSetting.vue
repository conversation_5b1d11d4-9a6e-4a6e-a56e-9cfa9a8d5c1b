<!-- 品质-售后专员设置列表-->
<template>
  <xpt-list
    ref="list"
    :data="list"
    :btns="btns"
    :colData="cols"
    :selection="selection"
    :pageTotal="count"
    :searchPage="search.page_name"
    @search-click="presearch"
    @selection-change="handleSelectionChange"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    @row-click="rowClick"
  >
    <template slot="shopCode" slot-scope="scope">
      <div>
        <el-input
          size="mini"
          icon="search"
          style="width: 140px"
          v-model="scope.row.shop_code"
          readonly
          :on-icon-click="chooseShop"
        >
        </el-input>
      </div>
    </template>
    <template slot="aftersaler" slot-scope="scope">
      <div>
        <el-input
          size="mini"
          icon="search"
          style="width: 140px"
          v-model="scope.row.aftersale_name"
          readonly
          :on-icon-click="chooseAfterSaler"
        >
        </el-input>
      </div>
    </template>
    <xpt-upload-v3
      slot="btns"
      uploadBtnText="导入"
      :uploadSize="20"
      acceptTypeStr=".xlsx,.xls"
      :dataObj="uploadDataObj"
      :disabled="false"
      :ifMultiple="false"
      :showSuccessMsg="false"
      @uploadSuccess="uploadSuccess"
      btnType="success"
      style="display: inline-block; margin: 0px 10px"
    >
    </xpt-upload-v3>
  </xpt-list>
</template>
<script>
const defaultVal = {
  shop_code: "",
  shop_id: "",
  shop_name: "",
  shop_area: "",
  aftersale_name: "",
  aftersale_id: "",
  status: "ENABLED", //默认生效
};
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      search: {
        page_name: "cloud_quality_feedback_aftersale",
        where: [],
        page_size: self.pageSize,
        page_no: 1,
        if_need_page: "Y",
      },
      list: [],
      listBackups: [],
      selection: "checkbox",
      count: 0,
      multipleSelection: [], //列表选择索引
      btns: [
        {
          type: "success",
          txt: "刷新",
          click: self.searching,
          loading: false,
        },
        {
          type: "info",
          txt: "新增",
          click: self.add,
          loading: false,
        },
        {
          type: "info",
          txt: "保存",
          click: self.save,
          loading: false,
        },
        {
          type: "warning",
          txt: "生效",
          click: self.enableEvent,
          loading: false,
        },
        {
          type: "warning",
          txt: "失效",
          click: self.disableEvent,
          loading: false,
        },
        {
          type: "danger",
          txt: "删除",
          click: self.deleteEvent,
          loading: false,
        },
        {
          type: "info",
          txt: "模板下载",
          click: self.getTemplate,
          loading: false,
        },
        {
          type: "info",
          txt: "导出",
          click: self.exportExcel,
          loading: false,
        },
        {
          type: "info",
          txt: "导入结果",
          click: self.importResult,
          loading: false,
        },
        {
          type: "info",
          txt: "导出结果",
          click: self.exportResult,
          loading: false,
        },
      ],
      cols: [
        {
          prop: "shop_area",
          label: "店铺地区",
          format: "auxFormat",
          formatParams: "shopArea",
          width: 150,
        },
        {
          label: "店铺编码",
          width: 94,
          slot: "shopCode",
          width: 160,
        },
        {
          prop: "shop_name",
          label: "店铺名称",
          width: 160,
        },
        {
          slot: "aftersaler",
          label: "推荐售后处理人",
          width: 160,
        },
        {
          prop: "status",
          label: "状态",
          formatter(val) {
            return (
              {
                ENABLED: "生效",
                DISABLED: "失效",
              }[val] || val
            );
          },
          width: 60,
        },
        {
          prop: "creator_name",
          label: "创建人",
          width: 120,
        },

        {
          prop: "create_time",
          label: "创建时间",
          format: "dataFormat1",
          width: 140,
        },
        {
          prop: "last_modifier_name",
          label: "更新人",
        },
        {
          prop: "last_modify_time",
          label: "更新时间",
          format: "dataFormat1",
          width: 140,
        },
      ],
      uploadDataObj: {
        parent_name: "",
        parent_no: "",
        child_name: null,
        child_no: null,
        content: {},
      },
    };
  },
  methods: {
    //上传成功返回结果
    uploadSuccess(result) {
      if (result.length > 0 && !!result[0].path) {
        this.importFileUrl(result[0].path);
      }
    },
    //导入
    importFileUrl(fileUrl) {
      let params = {
        fileUrl: fileUrl,
      };
      let self = this;
      this.ajax.postStream(
        "/reports-web/api/reports/aftersale/qualityFeedbackAftersale/import",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            setTimeout(() => {
              self.searching();
            }, 800);
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    //   模板下载
    getTemplate() {
      let self = this;
      let data = {
        templateName: "品质售后专员设置模板",
      };
      this.ajax.postStream(
        "/reports-web/api/template/getTemplateByName",
        data,
        (res) => {
          if (res.body.result) {
            self.download(res.body.content);
          } else {
            self.$message.error(res.body.msg);
          }
        }
      );
    },
    download(content) {
      let url = content.url;

      let filename = content.template_name;
      if (!fetch) {
        window.location.href = url;
        return;
      }
      return fetch(url).then((res) => {
        res.blob().then((blob) => {
          let a = document.createElement("a");
          let url = window.URL.createObjectURL(blob);
          a.href = url;
          a.download = filename;
          a.click();
          a.remove();
          window.URL.revokeObjectURL(url);
        });
      });
    },
    getIds() {
      let arr = this.multipleSelection.filter((item) => !!item.creator);
      return arr.map((item) => {
        return item.id;
      });
    },
    // 删除
    deleteEvent() {
      if (!this.multipleSelection.length) {
        return this.$message.error("请至少选择一条数据");
      }
      let self = this;
      let data = this.getIds();
      if (!data.length) {
        return this.$message.error("请选择一条有效数据");
      }
      this.$confirm("是否确定删除已选数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          self.ajax.postStream(
            "/afterSale-web/api/aftersale/order/qualityFeedbackAftersale/delete",
            data,
            (res) => {
              if (res.body.result) {
                self.$message.success(res.body.msg);
                setTimeout(() => {
                  self.searching();
                }, 800);
              } else {
                self.$message.error(res.body.msg);
              }
            }
          );
        })
        .catch(() => {
          this.$message.error("已取消");
        });
    },
    // 失效
    disableEvent() {
      if (!this.multipleSelection.length) {
        return this.$message.error("请至少选择一条数据");
      }
      let self = this;
      let data = this.getIds();
      if (!data.length) {
        return this.$message.error("请选择一条有效数据");
      }
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/order/qualityFeedbackAftersale/disabled",
        data,
        (res) => {
          if (res.body.result) {
            self.$message.success(res.body.msg);
            setTimeout(() => {
              self.searching();
            }, 800);
          } else {
            self.$message.error(res.body.msg);
          }
        }
      );
    },
    // 生效
    enableEvent() {
      if (!this.multipleSelection.length) {
        return this.$message.error("请至少选择一条数据");
      }
      let self = this;
      let data = this.getIds();
      if (!data.length) {
        return this.$message.error("请选择一条有效数据");
      }
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/order/qualityFeedbackAftersale/enabled",
        data,
        (res) => {
          if (res.body.result) {
            self.$message.success(res.body.msg);
            setTimeout(() => {
              self.searching();
            }, 800);
          } else {
            self.$message.error(res.body.msg);
          }
        }
      );
    },
    filterData(arr) {
      return arr.map((item) => {
        let str = String(item.id);
        if (str.indexOf("fakeid") == -1) {
          return {
            id: item.id,
            shop_code: item.shop_code,
            aftersale_id: item.aftersale_id,
          };
        } else {
          return {
            id: null,
            shop_code: item.shop_code,
            aftersale_id: item.aftersale_id,
          };
        }
      });
    },
    checkRepeat(arr1, arr2) {
      if(arr1.some(a => arr2.some(b => a.shop_code === b.shop_code && a.aftersale_id === b.aftersale_id))) return true
      return false;
    },
    checkEmpty() {
      let result = false;
      this.multipleSelection.forEach((item) => {
        if (!item.aftersale_id || !item.shop_code) {
          result = true;
        }
      });
      return result;
    },
    // 保存
    save() {
      if (!this.multipleSelection.length) {
        return this.$message.error("请至少选择一条新增数据");
      }
      if (this.checkRepeat(this.multipleSelection, this.listBackups)) {
        return this.$message.error("数据无更新");
      }
      if (this.checkEmpty()) {
        return this.$message.error("【店铺编码】【推荐售后处理人】不能为空");
      }
      let self = this;
      let data = this.filterData(this.multipleSelection);
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/order/qualityFeedbackAftersale/save",
        data,
        (res) => {
          if (res.body.result) {
            self.$message.success(res.body.msg);
            setTimeout(() => {
              self.searching();
            }, 800);
          } else {
            self.$message.error(res.body.msg);
          }
        }
      );
    },
    createId() {
      let number = Math.floor(Math.random() * 100000);
      if (this.list.findIndex((item) => item.id == number) == -1) {
        return "fakeid" + number;
      } else {
        return this.createId();
      }
    },
    //   新增
    add() {
      let obj = {
        ...defaultVal,
      };
      obj.id = this.createId();
      this.list.unshift(obj);
    },
    rowClick(row) {
      this.index = this.list.findIndex((item) => item.id == row.id);
    },
    chooseShop() {
      let self = this;
      self.params.callback = (data) => {
        self.list[this.index].shop_code = data.shop_code;
        self.list[this.index].shop_area = data.shop_area;
        self.list[this.index].shop_name = data.shop_name;
      };
      self.$root.eventHandle.$emit("alert", {
        params: self.params,
        component: () => import("./alert/shopAlert"),
        style: "width:900px;height:600px",
        title: "店铺列表",
      });
    },
    chooseAfterSaler() {
      let self = this;
      self.params.callback = (data) => {
        self.list[self.index].aftersale_id = data.id;
        self.list[self.index].aftersale_name = data.fullName;
      };
      self.$root.eventHandle.$emit("alert", {
        params: self.params,
        component: () => import("./quailityFeedbackSelectIntervener"),
        style: "width:900px;height:600px",
        title: "业务员列表",
      });
    },
    presearch(list, resolve) {
      this.search.where = list;
      this.searching(resolve);
    },
    searching(resolve) {
      let self = this;
      this.btns[0].loading = true;
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/order/qualityFeedbackAftersale/list",
        this.search,
        (res) => {
          if (res.body.result) {
            self.count = res.body.content.count;
            let list = res.body.content.list
            if (list) {
              self.list = list;
              self.listBackups = JSON.parse(JSON.stringify(list))
            }
            self.$message.success(res.body.msg);
          } else {
            self.list = [];
            self.listBackups = []
            self.count = 0;
            self.$message.error(res.body.msg);
          }
          self.btns[0].loading = false;
          "function" === typeof resolve ? resolve() : this;
        },
        (err) => {
          self.$message.error(err);
          self.btns[0].loading = false;
          "function" === typeof resolve ? resolve() : this;
        }
      );
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    sizeChange(size) {
      // 第页数改变
      this.search.page_size = size;
      this.searching();
    },
    pageChange(page_no) {
      // 页数改变
      this.pageNow = page_no;
      this.search.page_no = page_no;
      this.searching();
    },
    //导出
    exportExcel() {
      this.btns[1].loading = true;
      let params = {
        if_need_page: "N",
        page_name: "cloud_quality_feedback_aftersale",
        where: this.search.where,
      };
      this.ajax.postStream(
        "/reports-web/api/reports/aftersale/qualityFeedbackAftersale/export",
        params,
        (res) => {
          if (res.body.result) {
            res.body.msg && this.$message.success(res.body.msg);
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
          this.btns[1].loading = false;
        },
        (err) => {
          this.btns[1].loading = false;
          this.$message.error(err);
        }
      );
    },
    importResult() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "报表导入列表",
        params: {
          query: {
            type: "EXCEL_TYPE_QUALITY_FEEDBACK_AFTERSALE_IMPORT",
          },
        },
      });
    },
    exportResult() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "报表导出列表",
        params: {
          query: {
            type: "EXCEL_TYPE_QUALITY_FEEDBACK_AFTERSALE_EXPORT",
          },
        },
      });
    },
  },
  mounted() {
    let self = this;
    self.searching();
  },
};
</script>
<style  scoped>
</style>