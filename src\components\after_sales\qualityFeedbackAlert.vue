<!--品质反馈详情弹框-->
<template>
    <div style="height: 100%">
        <xpt-list
            v-if="fromBtn == 'deliver'"
            :btns="userBtns"
            :data="userList"
            :colData="userCols"
            searchHolder='请输入工号或姓名或昵称'
            @search-click="presearch"
            :pageTotal="total"
            :pageLength="search.page.length"
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
            selection="radio"
            @radio-change="radioChange"
        ></xpt-list>
        <div class="mgt15" v-else-if="fromBtn == 'getFileList'">
            <el-table :data="fileList" class="seeFileList">
                <el-table-column
                    prop="name"
                    label="文件预览"
                    show-overflow-tooltip
                    width="80"
                >
                    <template slot-scope="scope">
                        <div v-if="isImage(scope.row.information_content)">
                            <a href="#" class="showImg"
                                ><img
                                    :src="scope.row.information_content"
                                    alt="图片"
                                    width="60"
                            /></a>
                        </div>
                        <div v-else>
                            暂不支持
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="消息ID"
                    width="80"
                    prop="information_id"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    label="消息类型"
                    width="50"
                    prop="information_format"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">{{
                        messageTypeObj[scope.row.information_format]||'异常'
                    }}</template>
                </el-table-column>
                <el-table-column
                    label="创建人"
                    width="80"
                    prop="creator_name"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    label="创建时间"
                    width="120"
                    prop="create_time"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">{{
                        scope.row.create_time | dataFormat1
                    }}</template>
                </el-table-column>
                 <el-table-column label="文件预览" width="60">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            v-if="isImage(scope.row.information_content)"
                            @click="showImageList(scope.row)"
                        >
                            预览
                        </el-button>
                        <div v-else>
                            暂不支持
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="文件下载"
                    width="60"
                    prop="information_content"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <a
                            :href="scope.row.information_content"
                            target="_blank"
                            download
                            ><el-button type="primary" size="mini"
                                >下载</el-button
                            ></a
                        >
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    label="文件大小"
                    width="60"
                    prop="size"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">{{
                        scope.row.size | fileSize
                    }}</template>
                </el-table-column> -->
            </el-table>
            <xpt-image
                :images="imageList"
                :show="ifShowImage"
                :ifUpload="false"
                :ifClose="false"
                @close="closeShowImage"
            >
            </xpt-image>
        </div>
        <div class="xpt-flex" v-else-if="fromBtn == 'questionSolve'">
            <div class="xpt-top">
                <el-button type="info" size="mini" @click="questionSure"
                    >确定</el-button
                >
            </div>
            <div class="xpt-flex__bottom">
                <el-form :model="questionData">
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="6">
                            <el-form-item label="上传附件：" label-width="80px">
                                <xpt-upload-v3
                                    uploadBtnText="上传附件"
                                    acceptType="usually"
                                    :dataObj="uploadData"
                                    @uploadSuccess="uploadSuccess"
                                    :uploadSize="20"
                                ></xpt-upload-v3>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="18">
                            <el-form-item
                                label="文本消息："
                                label-width="80px"
                                prop="question_text"
                            >
                                <el-input
                                    type="textarea"
                                    placeholder="请输入内容"
                                    v-model="questionData.question_text"
                                    :autosize="{ minRows: 2, maxRows: 4 }"
                                    :maxlength="255"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <div class="xpt-flex" v-else-if="fromBtn == 'InRecord'">
            <div class="xpt-top">
                <el-button type="info" size="mini" @click="inRecordSure"
                    >确定</el-button
                >
            </div>
            <div class="xpt-flex__bottom">
                <el-form :model="recordData" :rules="rules" ref="recordData">
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="18">
                            <el-form-item
                                label="需介入原因："
                                prop="intervene_reason"
                                label-width="90px"
                            >
                                <el-input
                                    type="textarea"
                                    placeholder="请输入内容"
                                    v-model="recordData.intervene_reason"
                                    :autosize="{ minRows: 2, maxRows: 4 }"
                                    :maxlength="500"
                                >
                                </el-input>
                                <el-tooltip
                                    v-if="rules.intervene_reason[0].isShow"
                                    class="item"
                                    effect="dark"
                                    :content="rules.intervene_reason[0].message"
                                    placement="right"
                                    popper-class="xpt-form__error"
                                >
                                    <i class="el-icon-warning"></i>
                                </el-tooltip>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <div class="xpt-flex" v-else-if="fromBtn == 'GreenWay'">
            <div class="xpt-top">
                <el-button type="info" size="mini" @click="greenWaySure"
                    >确定</el-button
                >
            </div>
            <div class="xpt-flex__bottom">
                <el-form :model="greenWayData" :rules="rules" ref="greenWayData">
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="18">
                          <el-form-item
                                    label="是否绿通："
                                    prop="if_greenway"
                                    label-width="80px"
                                >
                                <el-select  size='mini'  placeholder="请选择" v-model='greenWayData.if_greenway' :disabled="false"  @change="onIfGreenWayChange">
                                    <el-option label="是" value="Y"></el-option>
                                    <el-option label="否" value="N"></el-option>
                                    <el-option label="待定，待品质部复核" value="WAIT"></el-option>
                                  </el-select>
                                  <el-tooltip
                                    v-if="rules.if_greenway[0].isShow"
                                    class="item"
                                    effect="dark"
                                    :content="rules.if_greenway[0].message"
                                    placement="right"
                                    popper-class="xpt-form__error"
                                >
                                    <i class="el-icon-warning"></i>
                                </el-tooltip>
                                </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="18">
                          <el-form-item
                                    label="绿通类型："
                                    prop="greenway_type"
                                    label-width="80px"
                                >
                                  <xpt-select-aux
                                    v-model="greenWayData.greenway_type"
                                    aux_name='PZFKDLTLX'
                                    :disabled="['WAIT','N'].includes(greenWayData.if_greenway)"
                                    >
                                  </xpt-select-aux>
                                  <el-tooltip
                                    v-if="rules.greenway_type[0].isShow"
                                    class="item"
                                    effect="dark"
                                    :content="rules.greenway_type[0].message"
                                    placement="right"
                                    popper-class="xpt-form__error"
                                >
                                    <i class="el-icon-warning"></i>
                                </el-tooltip>
                                </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="18">
                          <el-form-item label="绿通备注：" label-width="80px">
                                <el-input type='textarea' v-model="greenWayData.greenway_remark" size='mini' :maxlength="200"></el-input>
                              </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
    </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from "@common/Fn.js";
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            quality_feedback_id: "",
            quality_feedback_no: "",
            fromBtnArray: [
                "questionSolve", //包含问题回复和添加
                "deliver",
                "getFileList",
                "InRecord",
            ],
            fromBtn: "",
            userList: [],
            userBtns: [
                {
                    type: "info",
                    txt: "确认",
                    click: () => {
                        self.params.callback(self.currentUserInfo);
                        self.$root.eventHandle.$emit(
                            "removeAlert",
                            self.params.alertId
                        );
                    },
                    disabled: true,
                },
            ],
            userCols: [
                {
                    label: "工号",
                    prop: "employeeNumber",
                },
                {
                    label: "姓名",
                    prop: "realName",
                },
                {
                    label: "昵称",
                    prop: "nickName",
                },
                {
                    label: "业务员",
                    prop: "salesmanType",
                    formatter:val=>{
                        if(val=='PROBLEM_FEEDBACK_DEALER'){
                            return '品质反馈处理专员'
                        }
                    }
                },
                {
                    label: "分组",
                    prop: "groupName",
                },
            ],
            total:0,
            search:{
                page_name: "user_person_default_business",
                isEnable: 1,
                where: [
                    {
                        field: "9ba82492fa0e14efdce2b6aab53ac32e",
                        table: "cfcadaec5fdc3edaa2afea4ac01cfdf2",
                        value: "品质反馈分组",
                        operator: "=",
                        condition: "AND",
                        listWhere: [],
                    }
                ],
                page:{
                    length: 10,
                    pageNo: 1,
                },
                if_need_page: "Y",
                key:""
            },
            currentUserInfo: {},
            fileList: [],
            imageList: [],
            ifShowImage: false,
            recordData: {
                intervene_reason: "",
            },
            questionData: {
                question_text: "",
                question_list: "",
            },
            rules: {
                intervener_name: validate.isNotBlank({
                    self: self,
                }),
                intervene_reason: validate.isNotBlank({
                    self: self,
                }),
                if_greenway: validate.isNotBlank({
                    self: self,
                }),
                greenway_type: [{
                  required: false,
                  validator: (rule, value, callback) => {
                    if(this.greenWayData.if_greenway === 'Y' && !value){
                      this.rules[rule.field][0].isShow = true
                      callback(new Error(''))
                    }else {
                      this.rules[rule.field][0].isShow = false
                      callback()
                    }
                  },
                  trigger: 'blur',
                  isShow: false,
                  message: '请选择',
                }],
              },
            //附件上传路径标志
            uploadData: {},
            // 消息记录附件类型
            messageTypeObj: {
                picture: "图片",
                video: "视频",
                other: "其它",
            },
            greenWayData:{
              if_greenway:'N',
              greenway_type:'',
              greenway_remark:'',
            },
        };
    },
    created() {
        let self = this;
        console.log(this.params);
        this.fromBtn = this.params.fromBtn;
        this.quality_feedback_id = this.params.quality_feedback_id;
        this.quality_feedback_no = this.params.quality_feedback_no;
        this.questionData.question_text=this.params.question_text?this.params.question_text:null
        this.uploadData = {
            parent_name: "QUALITY_FEEDBACK",
            parent_no: this.params.quality_feedback_no,
            child_name: null,
            child_no: null,
            content: {},
        };
    },
    mounted() {
        if (this.fromBtn == "deliver") {
            this.getQualityFeedbackUserList();
            return;
        }
        if (this.fromBtn == "getFileList") {
            this.getFileList();
            return;
        }
        if(this.fromBtn=="GreenWay"){
          this.greenWayData=this.params.greenWayData;
        }
        // this.params.callback();
    },
    computed: {},
    methods: {
        presearch(key, resolve) {
            this.search.key = key;
            this.getQualityFeedbackUserList(resolve);
        },
        getQualityFeedbackUserList(resolve) {
            let self = this;
            let params = this.search;
            if (this.params.content && this.params.content != 0) {
                params.groupId = this.params.content
            }
            this.ajax.postStream(
                "/user-web/api/userPerson/getUserPersonBusinessList",
                params,
                (res) => {
                    if (res.body.result) {
                        res.body.msg ? this.$message.success(res.body.msg) : "";
                        let nowTime=new Date().getTime();
                        this.userList = res.body.content.list;
                        this.total = res.body.content.count;

                    } else {
                        this.$message.error(res.body.msg);
                    }
                    resolve && resolve();
                },
                (err) => {
                    this.$message.error(err);
                    resolve && resolve();
                }
            );
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page.length = size;
            this.getQualityFeedbackUserList();
        },
        pageChange(pageNo) {
            // 页数改变
            this.search.page.pageNo = pageNo;
            this.getQualityFeedbackUserList();
        },

        getFileList() {
            let params = {
                quality_feedback_id: this.quality_feedback_id,
            };
            this.$axios(
                "post",
                "/afterSale-web/api/aftersale/order/qualityfeedback/getQualityFeedbackAppendixList",
                params
            )
                .then((res) => {
                    if (res.result) {
                        this.fileList = res.content;
                        this.params.callback && this.params.callback();
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .catch((err) => {
                    this.$message.error(err);
                });
        },
        radioChange(data) {
            console.log(data);
            this.currentUserInfo = data;
            if (Object.keys(this.currentUserInfo).length) {
                this.userBtns[0].disabled = false;
            }
        },
        inRecordSure() {
            this.$refs.recordData.validate((valid) => {
                if (!valid) return;
                this.params.callback && this.params.callback(this.recordData);
                this.$root.eventHandle.$emit(
                    "removeAlert",
                    this.params.alertId
                );
            });
        },
        questionSure() {
            if (
                this.questionData.question_list.length == 0 &&
                this.questionData.question_text.length == 0
            ) {
                this.$message.error("请回复至少任意一种消息");
                return;
            }
            this.params.callback && this.params.callback(this.questionData);
            this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
        },
        selectUser() {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                component: () =>
                    import("@components/after_sales/quailityFeedbackSelectIntervener.vue"),
                close: function () {},
                style: "width:900px;height:600px",
                title: "请选择介入专员",
                params: {
                    type: "EMPLOYEE",
                    status: 1, //生效人员
                    isEnable: 1, //生效时间
                    page_name: "cloud_user_person",
                    where: [],
                    callback(d) {
                        self.recordData.intervener_name = d.fullName;
                        self.recordData.intervener_personId = d.personId;
                        self.$message.success("选择成功");
                    },
                },
            });
        },
        userChange(val) {
            console.log(val);
        },
        isImage(str) {
            var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
            return reg.test(str);
        },
        //初始化预览列表
        initImageList(row) {
            let imglist = [];
            //过滤出只有图片的列表
            let list = this.fileList.filter((item) => this.isImage(item.information_content));
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = value.information_content;
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = value.creator_name;
                obj.isPucture = true;
                //确定要预览那张图片
                if (value.information_id === row.information_id) {
                    obj.active = true;
                } else {
                    obj.active = false;
                }
                imglist.push(obj);
            });
            this.imageList = imglist;
        },
        closeShowImage() {
            this.ifShowImage = false;
        },
        showImageList(row) {
            this.initImageList(row);
            this.ifShowImage = true;
        },
        uploadSuccess(uploadFileList) {
            this.questionData.question_list = uploadFileList;
            console.log("文件上传成功", uploadFileList);
        },
        onIfGreenWayChange(){
          if(['N','WAIT'].includes(this.greenWayData.if_greenway)){
            this.greenWayData.greenway_type='';
            this.rules.greenway_type[0].required=false;
          }else{
            this.rules.greenway_type[0].required=true;
          }
        },
        greenWaySure(){
          this.$refs.greenWayData.validate((valid) => {
                if (!valid) return;
                this.params.callback && this.params.callback(this.greenWayData);
                this.$root.eventHandle.$emit(
                    "removeAlert",
                    this.params.alertId
                );
            });
        }
    },
};
</script>
<style scope>
.el-form {
    white-space: nowrap;
}
.mgt15 {
    margin-top: 15px;
}
.seeFileList {
    position: absolute;
    bottom: 0;
    left: 10px;
    right: 10px;
    top: 0px;
    width: auto;
    min-width: auto;
    padding: 10px 0px;
}
.seeFileList .el-table__body-wrapper td .cell {
    height: auto;
}
.el-table__empty-block {
    width: auto !important;
}
.showImg {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #dbdbdb;
    margin: 5px 0;
    line-height:100%;
}
#xpt-image .xpt-image__body{
    width:100%;
    height:100%;
}
.select-intervener{
    width:180px !important;
}
</style>
