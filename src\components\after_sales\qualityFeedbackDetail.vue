<!-- 品质反馈详情-->
<template>
    <div class="xpt-flex quality-feedback">
        <div class="xpt-top">
            <el-button
                type="success"
                size="mini"
                :loading="false"
                @click="initDetail"
                >刷新</el-button
            >
            <el-button
                type="warning"
                size="mini"
                @click="deliver"
                :disabled="!canIdeliver"
                >转交</el-button
            >
            <xpt-btngroup
                class="mgl10"
                type="danger"
                :btngroup="urgeBtns"
                :disabled="!canIurge"
                size="mini"
            ></xpt-btngroup>
            <el-button type="success" size="mini" @click="getFileList"
                >查看附件</el-button
            >
            <el-button
                type="danger"
                size="mini"
                @click="InRecord"
                :disabled="!canIinRecord"
                >品质介入</el-button
            >
            <!-- :disabled="!canIquestionAddType" -->
            <el-button
                type="primary"
                size="mini"
                disabled
                @click="addQuestionType"
                >添加问题类型</el-button
            >
            <el-button
                type="warning"
                size="mini"
                @click="questionSure"
                :disabled="!canIquestionSure"
                >问题确认</el-button
            >
            <el-button
                type="danger"
                size="mini"
                @click="reopen"
                :disabled="!canIreopen"
                >重新打开</el-button
            >
            <el-button
                type="primary"
                size="mini"
                @click="changeGreenWay"
                :disabled="!canIChangeGreenWay"
                >是否绿通</el-button
            >
        </div>
        <div class="xpt-flex__bottom">
            <el-form
                :model="submitData"
                :rules="rules"
                ref="submitData"
                class="detail-form"
            >
                <el-tabs v-model="firstTab" style="height: 300px" @tab-click="handleFirstTabChange">
                    <el-tab-pane label="基础信息" name="basicInfo">
                        <el-row :gutter="40" class="mgt10">
                            <el-col :span="6">
                                <el-form-item
                                    label="品质反馈单："
                                    prop="quality_feedback_no"
                                    label-width="80px"
                                >
                                    <el-input
                                        v-model="submitData.quality_feedback_no"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="售后单号："
                                    prop="after_order_no"
                                    label-width="80px"
                                >
                                    <el-input
                                        v-model="submitData.after_order_no"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="状态："
                                    prop="status"
                                    label-width="110px"
                                >
                                    <el-input
                                        :value="
                                            submitData.status == 'EXECUTING'
                                                ? '处理中'
                                                : submitData.status ==
                                                  'WAITING_FOR_CONFIRM'
                                                ? '待确认'
                                                : submitData.status ==
                                                  'FINISHED'
                                                ? '已完结'
                                                : ''
                                        "
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="销售利润分类："
                                    prop="goods_type_code"
                                    label-width="90px"
                                >
                                    <xpt-select-aux
                                        v-model="submitData.sales_profit_class"
                                        aux_name="sale_profit_class"
                                        placeholder=""
                                        disabled
                                    ></xpt-select-aux>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="40" class="mgt10">
                            <el-col :span="6">
                                <el-form-item
                                    label="物料编码："
                                    prop="material_number"
                                    label-width="80px"
                                >
                                    <el-input
                                        v-model="submitData.material_number"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="物料名称："
                                    prop="material_name"
                                    label-width="80px"
                                >
                                    <el-input
                                        v-model="submitData.material_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="物料规格："
                                    prop="material_spec"
                                    label-width="110px"
                                >
                                    <el-input
                                        v-model="submitData.material_spec"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="问题类型："
                                    prop="problem_type"
                                    label-width="90px"
                                >
                                    <el-input
                                        v-model="submitData.problem_type"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="40" class="mgt10">
                            <el-col :span="6">
                                <el-form-item
                                    label="批号："
                                    prop="lot"
                                    label-width="80px"
                                >
                                    <el-input
                                        v-model="submitData.lot"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="供应商名称："
                                    prop="suppiler_name"
                                    label-width="80px"
                                    v-show="!ifPurchaseDealer"
                                >
                                    <el-input
                                        v-model="submitData.suppiler_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="推荐售后处理人："
                                    prop="handler_name"
                                    label-width="110px"
                                >
                                    <el-input
                                        v-model="submitData.handler_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="受理时间："
                                    prop="handler_time"
                                    label-width="90px"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.handler_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="40" class="mgt10">
                            <el-col :span="6">
                                <el-form-item
                                        label="物料项目组："
                                        prop="material_project_team"
                                        label-width="80px"
                                    >
                                        <el-input
                                            v-model="submitData.material_project_team"
                                            size="mini"
                                            disabled
                                        ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="是否绿通："
                                    prop="if_greenway"
                                    label-width="80px"
                                >
                                <el-select  size='mini'  placeholder="请选择" v-model='submitData.if_greenway' :disabled="true">
                                    <el-option label="是" value="Y"></el-option>
                                    <el-option label="否" value="N"></el-option>
                                    <el-option label="待定，待品质部复核" value="WAIT"></el-option>
                                  </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="绿通类型："
                                    prop="greenway_type"
                                    label-width="110px"
                                >
                                  <xpt-select-aux
                                    v-model="submitData.greenway_type"
                                    aux_name='PZFKDLTLX'
                                    :disabled="true"
                                    >
                                  </xpt-select-aux>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="40" class="mgt10">
                          <el-col :span="6">
                              <el-form-item label="绿通备注：">
                                <el-input type='textarea' v-model="submitData.greenway_remark" size='mini' disabled :maxlength="200"></el-input>
                              </el-form-item>
                            </el-col>
                        </el-row>
                      </el-tab-pane>
                    <el-tab-pane label="其它信息" name="otherInfo">
                        <el-row :gutter="40" class="mgt10">
                            <el-col :span="6">
                                <el-form-item
                                    label="提交人："
                                    prop="creator_name"
                                    label-width="100px"
                                >
                                    <el-input
                                        v-model="submitData.creator_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="提交人时间："
                                    prop="create_time"
                                    label-width="100px"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.create_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="提交人分组："
                                    prop="creator_group_name"
                                    label-width="120px"
                                >
                                    <el-input
                                        v-model="submitData.creator_group_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="买家昵称："
                                    prop="customer_name"
                                    label-width="120px"
                                >
                                    <el-input
                                        v-model="submitData.customer_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="40" class="mgt10">
                            <el-col :span="6">
                                <el-form-item
                                    label="转交人："
                                    prop="transfer_name"
                                    label-width="100px"
                                >
                                    <el-input
                                        v-model="submitData.transfer_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="转交时间："
                                    prop="transfer_time"
                                    label-width="100px"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.transfer_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="完结人："
                                    prop="finisher_name"
                                    label-width="120px"
                                >
                                    <el-input
                                        v-model="submitData.finisher_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="完结时间："
                                    prop="finish_time"
                                    label-width="120px"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.finish_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="40" class="mgt10">
                            <el-col :span="6">
                                <el-form-item
                                    label="合并单号："
                                    prop="merge_trade_no"
                                    label-width="100px"
                                >
                                    <el-input
                                        v-model="submitData.merge_trade_no"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="批次单号："
                                    prop="batch_trade_no"
                                    label-width="100px"
                                >
                                    <el-input
                                        v-model="submitData.batch_trade_no"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="售后初次处理时间："
                                    prop="first_reply_time"
                                    label-width="120px"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.first_reply_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="品质初次处理时间："
                                    prop="intervene_first_reply_time"
                                    label-width="120px"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.intervene_first_reply_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="40" class="mgt10">
                            <el-col :span="6">
                                <el-form-item
                                    label="是否超72小时："
                                    prop="if_overtime"
                                    label-width="100px"
                                >
                                    <el-select
                                        v-model="submitData.if_overtime"
                                        placeholder="否"
                                        size="mini"
                                        disabled
                                    >
                                        <el-option
                                            v-for="item in timeOutOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="处理方案跟踪" name="greenWayInfo">
                        <xpt-list
                            class="green-way-list"
                            :orderNo="true"
                            :btns="greenWayBtns"
                            :data="greenWayList"
                            :colData="greenWayCols"
                            style="margin-top: 10px"
                            @selection-change="handleGreenWaySelectionChange"
                        ></xpt-list>
                    </el-tab-pane>
                </el-tabs>
                <el-tabs v-model="secondTab" class="record-list" @tab-click="handleSecondTabChange">
                    <el-tab-pane
                        label="消息记录"
                        name="messageRecord"
                        class="message-record"
                    >
                        <div class="xpt-top">
                            <el-button
                                type="primary"
                                size="mini"
                                @click="questionSolve('reply')"
                                :disabled="!canIquestionReply"
                                >问题回复</el-button
                            >
                            <xpt-btngroup
                                class="mgl10"
                                type="danger"
                                :btngroup="replayBtns"
                                :disabled="!canIquestionReply"
                                size="mini"
                            ></xpt-btngroup>
                            <el-button
                                type="primary"
                                size="mini"
                                @click="questionSolve('add')"
                                :disabled="!canIquestionAdd"
                                >追加问题</el-button
                            >
                        </div>
                        <qualityFeedbackInformationList
                            class="message-list"
                            :list="currentMessageList"
                            :total="messageRecordTotal"
                            :currentPage="messagePageNow"
                            @current-page-change="currentPageChange"
                        ></qualityFeedbackInformationList>
                    </el-tab-pane>
                    <el-tab-pane label="介入记录" name="InRecord">
                        <xpt-list
                            :showHead="false"
                            :orderNo="true"
                            :data="inRecordlist"
                            :colData="inRecordCols"
                            style="margin-top: 10px"
                        ></xpt-list>
                    </el-tab-pane>
                    <el-tab-pane label="操作记录" name="OperateRecord">
                      <el-table :data="operateLogList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
                        <el-table-column label="序号" type="index" width="50">
                          <template slot-scope="scope">
                            <div class="table-index">{{ scope.$index + 1 }}</div>
                          </template>
                        </el-table-column>
                        <el-table-column label="用户" prop="operator_name" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column label="业务操作" prop="operate_type" width="200" show-overflow-tooltip>
                          <template slot-scope="scope">
                            {{ operate_type_options[scope.row.operate_type] || scope.row.operate_type }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作描述" prop="description" width="400" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作时间" prop="operate_time" width="200" show-overflow-tooltip>
                          <template slot-scope="scope">
                            <span>{{ scope.row.operate_time | dataFormat1 }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                  </el-tab-pane>
                </el-tabs>
            </el-form>
        </div>
    </div>
</template>
<script>
import validate from "@common/validate.js";
import qualityFeedbackInformationList from "./qualityFeedbackInformationList";
import qs from "qs";
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            firstTab: "basicInfo",
            secondTab: "messageRecord",
            submitData: {},
            replayBtns:[
                {
                    type: "primary",
                    txt: "快速回复",
                    click: () => {},
                },
                {
                    type: "primary",
                    txt: "通用类",
                    click: () => {

                        self.quickReplay("COMMON")
                    },
                },
                {
                    type: "primary",
                    txt: "批量类",
                    click() {
                       self.quickReplay("BATCH")
                    },
                },
            ],
            urgeBtns: [
                {
                    type: "primary",
                    txt: "催办",
                    click: () => {},
                },
                {
                    type: "primary",
                    txt: "反馈催办",
                    click() {
                        self.urge("feedback");
                    },
                },
                {
                    type: "primary",
                    txt: "介入催办",
                    click() {
                        self.urge("inrecord");
                    },
                },
            ],
            rules: {},
            inRecordlist: [],
            inRecordCols: [
                {
                    label: "申请介入原因",
                    prop: "intervene_reason",
                },
                {
                    label: "品质部跟进人",
                    prop: "intervener_name",
                },
                {
                    label: "申请人",
                    prop: "creator_name",
                },
                {
                    label: "申请时间",
                    prop: "create_time",
                    format: "dataFormat1",
                },
            ],
            greenWayList:[],
            multipleGreenWaySelection:[],
            greenWayCols:[
                {
                    label: "处理方案",
                    prop: "track_programme",
                    bool:true,
                    isSelect:true,
                    disabled(row) {
                        return false
                    },
                    obj:{},
                    rHeader: true,
                    renderHeader: (h, {column, $index}) => {
                        return h('div',
                            [
                                h('i', {style: 'color:red;margin-left:5px;'}, '*'),
                                h('span', column.label)
                            ]
                        )
                    },
                },
                {
                    label: "处理金额",
                    prop: "track_amount",
                    bool: true,
                    isInput: true,
                    maxlength:50,
                    disabled(row) {
                        return false
                    },
                    rHeader: true,
                    renderHeader: (h, {column, $index}) => {
                        return h('div',
                            [
                                h('i', {style: 'color:red;margin-left:5px;'}, '*'),
                                h('span', column.label)
                            ]
                        )
                    },
                },
                {
                    label: "充值单号",
                    prop: "order_no",
                    bool: true,
                    isInput: true,
                    maxlength:50,
                    disabled(row) {
                        return false
                    },
                },
                {
                    label: "备注",
                    prop: "track_remark",
                    bool: true,
                    isInput: true,
                    maxlength:200,
                    disabled(row) {
                        return false
                    },
                },
            ],
            greenWayBtns: [
            {
              type: 'success',
              txt: '新增',
              disabled:true,
              loading:false,
              click() {
                  self.addGreenWayRow();
              },
            },{
              type: 'primary',
              txt: '保存',
              disabled:true,
              loading:false,
              click: ()=>{
                self.saveGreenWayRow();
              }
            },{
              type: 'danger',
              txt: '删除',
              disabled:true,
              loading:false,
              click: ()=>{
                self.deleteGreenWayRow();
              }
            }],
            messagePageSize: 10,
            messagePageNow: 1,
            currentMessageList: [], //单页消息记录
            messageRecordList: [], //消息记录列表
            messageRecordTotal: 0, //消息记录总数
            employeeGroupCodeList: [], //当前代理人业务员类型列表
            isQualityFeedbackDealer: false, //是否品质反馈处理专员
            isQualityFeedbackAdmin: false, //是否品质反馈管理员
            isQualityFeedbackSpecialist: false, //是否品质反馈专员
            canIdeliver: false, //问题转交
            canIquestionAdd: false, //问题添加
            canIquestionReply: false, //问题回复
            canIinRecord: false, //介入
            canIurge: false, //催办
            canIquestionSure: false, //问题确认
            canIquestionAddType: false, //添加问题类型
            canIreopen: false, //重新打开
            canIChangeGreenWay:false,//是否绿通
            timeOutOptions: [
                { value: "Y", label: "是" },
                { value: "N", label: "否" },
            ], //是否超时转换
            ifPurchaseDealer: false,
            operateLogList: [],
            operate_type_options: {
              CREATE: '新增',
              SAVE: '保存',
              LOCK: '锁定',
              UNLOCK: '解锁',
              SUBMIT: '提交',
              RETRACT: '撤回',
              AUDIT: '审核',
              CHANGE: '确认变更',
              REVERSE_AUDIT: '反审核',
              SUBMIT_APPROVE: '提交审批',
              PASS: '审批通过',
              NOT_PASS: '审批不通过',
              TRACK: '执行跟踪',
              RECALL: '撤回仓储',
              CLOSE: '关闭',
              OPEN: '反关闭',
              CANCEL: '已取消',
              //extra
              ADD_REFUND_ITEM: '引入退款明细',
              DELETE_REFUND_ITEM: '删除退款明细',
              ADD_TAOBAO_REFUND: '引入淘宝退款申请',
              DELETE_TAOBAO_REFUND: '删除淘宝退款申请',
              SUBMITPURCHASE: '提交采购',
              PLAN_RETRACT: '方案撤回',
              REJECT: '驳回',
              LOCKER: '锁定',
              REVOCATION: '撤回',
              VERIFY: '审核',
              OPPOSITE_VERIFY: '反审核',
              SUBMIT_EXAMINE: '提交审批',
              PASS_EXAMINE: '审批通过',
              UNPASS_EXAMINE: '审批不通过',
            },
        };
    },
    mounted() {
        let self = this;
        this.getGreenWayOptions().then(res=>{
          const options=res.reduce((a, b) => {
            a[b.code] = b.name
            return a
          }, {})
          this.greenWayCols[0].obj=options;
        })
        this.getEmployeeInfoGroupList().then(() => {
            self.ifQualityFeedbackDealerOrAdmin();
            self.initDetail();
        });
        //监听切换业务代理事件
        this.$root.eventHandle.$on("resetAllBtnStatus", () => {
            this.getEmployeeInfoGroupList().then(() => {
                self.ifQualityFeedbackDealerOrAdmin();
                self.setButtonPermissions();
            });
        });
    },
    computed: {},
    created () {
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
		this.ifPurchaseDealerOfProxyId()
	},
	beforeDestroy(){
		//解除监听切换业务代理事件
		this.$root.eventHandle.$off('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
	},
    methods: {
        getGreenWayOptions() {
            return new Promise((resolve, reject) => {
                this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryDataList', { categoryCode: 'PZFKDCLFA',isEnable:1,status:1 }, res => {
                    if (res.body.result) {
                        resolve(res.body.content.list)
                    } else {
                        reject(res.body.msg);
                    }
                })
            })
        },
        ifPurchaseDealerOfProxyId() {
			let id = this.getEmployeeInfo('personId'), self = this
			this.ajax.get('/user-web/api/userPerson/getUserPerson/'+id,function(data){
				data = data.body;
				if(data.result){
					if (data.content && data.content.type == 'PURCHASE_DEALER') {
                        self.ifPurchaseDealer = true
					} else {
                        self.ifPurchaseDealer = false
					}
				}
			},function(error){
                self.ifPurchaseDealer = false
			})
		},
        initDetail() {
            let p1 = this.getQualityFeedbackEntity();
            let p2 = this.getMessageRecordList();
            let p3 = this.getInRecordList();
            Promise.all([p1, p2, p3]).then(() => {
                this.setButtonPermissions();
            });
        },
        getEmployeeInfoGroupList() {
            //获取当前处理人的业务信息列表
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/user-web/api/userPerson/getUserPersonGroupList",
                    { personId: this.getEmployeeInfo("personId") },
                    (res) => {
                        if (res.body.result && res.body.content) {
                            this.employeeGroupCodeList =
                                res.body.content.list || [];
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        //品质反馈单详情
        getQualityFeedbackEntity() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/aftersale/order/qualityfeedback/getQualityFeedbackEntity",
                    { quality_feedback_id: this.params.quality_feedback_id },
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.submitData = res.body.content;
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        //消息列表
        getMessageRecordList() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/aftersale/order/qualityfeedback/getQualityFeedbackImformationList",
                    { quality_feedback_id: this.params.quality_feedback_id },
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.messageRecordList = res.body.content;
                            self.messageRecordTotal = res.body.content.length;
                            self.currentMessageList = res.body.content.slice(
                                0,
                                self.messagePageSize
                            );
                            self.messagePageNow = 1;
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        //介入记录列表
        getInRecordList() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/aftersale/order/qualityfeedback/getQualityFeedbackIntervention",
                    { quality_feedback_id: this.params.quality_feedback_id },
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.inRecordlist = res.body.content;
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        //处理方案跟踪
        getGreenWayList(){
          let self = this;
            return new Promise((resolve, reject) => {
              this.ajax.get('/afterSale-web/api/aftersale/order/qualityfeedbacktrack/getTrack/' + this.params.quality_feedback_id,
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.greenWayList = res.body.content;
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        handleGreenWaySelectionChange(val){
          this.multipleGreenWaySelection = val;
        },
        //设置按钮权限
        setButtonPermissions() {
            let self = this;
            let currentAgent = this.getEmployeeInfo("fullName"); //当前代理人
            let isDealer = this.isQualityFeedbackDealer; //是否处理专员
            let isAdmin = this.isQualityFeedbackAdmin; //是否管理员
            let isSpecialList = this.isQualityFeedbackSpecialist; //是否专员
            let isCreator = this.submitData.creator_name == currentAgent; //是否当前创建人
            let isHandler = this.submitData.handler_name == currentAgent; //是否当前受理人
            let isInRecorder = this.inRecordlist.some(
                (item) =>
                    item.intervener_person_id ==
                    self.getEmployeeInfo("personId")
            ); //是否介入专员
            let fbStatus = this.submitData.status; //品质反馈单状态
            let isPassTime =
                new Date().getTime() -
                    new Date(this.submitData.create_time).getTime() >
                1800000;

            this.canIdeliver =
                (isAdmin || (isDealer && isHandler)) && fbStatus == "EXECUTING"; // 转交
            this.canIquestionAdd = isCreator && fbStatus != "FINISHED"; //问题添加
            this.canIquestionReply =
                (isAdmin || isHandler || isInRecorder) &&
                fbStatus != "FINISHED"; //问题回复
            this.canIinRecord =
                (isAdmin || isCreator || isHandler || isInRecorder) &&
                fbStatus != "FINISHED"; //介入
            this.canIurge = isCreator && fbStatus == "EXECUTING" && isPassTime; //催办
            this.canIquestionSure =
                (isCreator || isHandler) && fbStatus == "WAITING_FOR_CONFIRM"; //问题确认
            this.canIquestionAddType =
                (isAdmin || isHandler) && fbStatus != "FINISHED"; //添加问题类型
            this.canIreopen = fbStatus == "FINISHED"; //完结
            this.canIChangeGreenWay = isAdmin || isDealer || isSpecialList; //是否绿通

            //绿通方案按钮
            if(this.canIChangeGreenWay){
              this.greenWayBtns[0].disabled=false;
              this.greenWayBtns[1].disabled=false;
              this.greenWayBtns[2].disabled=false;
            }else{
              this.greenWayBtns[0].disabled=true;
              this.greenWayBtns[1].disabled=true;
              this.greenWayBtns[2].disabled=true;
            }
        },
        // 是否拥有品质反馈处理专员或管理员类型
        ifQualityFeedbackDealerOrAdmin() {
            //过滤失效的业务员
            let nowTime = new Date().getTime();
            let enAbledEmployeeGroupCodeList = this.employeeGroupCodeList.filter(
                (item) => !item.disableTime || item.disableTime > nowTime
            );
            //业务员编码列表
            let codeArr = enAbledEmployeeGroupCodeList.map((item) => {
                return item.salesmanType;
            });
            this.isQualityFeedbackDealer = codeArr.includes(
                "PROBLEM_FEEDBACK_DEALER"
            );
            this.isQualityFeedbackSpecialist = codeArr.includes(
                "PROBLEM_FEEDBACK_SPECIALIST"
            );
            this.isQualityFeedbackAdmin = codeArr.includes(
                "PROBLEM_FEEDBACK_ADMIN"
            );
        },
        deliver() {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                title: "转交",
                style: "width:600px;height:300px",
                component: () =>
                    import("@components/after_sales/qualityFeedbackAlert"),
                params: {
                    fromBtn: "deliver",
                    callback: (data) => {
                        self.ajax.postStream(
                            "/afterSale-web/api/aftersale/order/qualityfeedback/deliverQualityFeedBack",
                            {
                                quality_feedback_id:
                                    self.params.quality_feedback_id,
                                person_id: data.id,
                            },
                            (res) => {
                                if (res.body.result && res.body.content) {
                                    self.$message.success(res.body.msg);
                                    self.getQualityFeedbackEntity().then(() => {
                                        self.setButtonPermissions();
                                        self.getInRecordList();
                                    });
                                } else {
                                    res.body.msg &&
                                        self.$message.error(res.body.msg);
                                }
                            },
                            (err) => {
                                self.$message.error(err);
                            }
                        );
                        // self.submitData.transfer_name = data.fullName;
                        // self.submitData.transfer_time = +new Date();
                    },
                },
            });
        },
        urge(type) {
            let self = this;
            let params = {};
            let url = "";
            if (type == "feedback") {
                url =
                    "/afterSale-web/api/aftersale/order/qualityfeedback/feedBackremin";
                params = {
                    quality_feedback_id: self.params.quality_feedback_id,
                };
            } else if (type == "inrecord") {
                let id =
                    self.inRecordlist.length > 0 &&
                    self.inRecordlist[0].intervener_person_id;
                if (!id) {
                    self.$message.error("暂无介入专员，无法催办");
                    return;
                }
                url =
                    "/afterSale-web/api/aftersale/order/qualityfeedback/interveneremin";
                params = {
                    quality_feedback_id: self.params.quality_feedback_id,
                    person_id: id,
                };
            }
            self.ajax.postStream(
                url,
                params,
                (res) => {
                    if (res.body.result && res.body.content) {
                        self.$message.success(res.body.msg);
                        self.getQualityFeedbackEntity();
                    } else {
                        res.body.msg && self.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
        },
        getFileList() {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                title: "查看附件",
                style: "width:900px;height:600px;",
                component: () =>
                    import("@components/after_sales/qualityFeedbackAlert"),
                params: {
                    quality_feedback_id: self.submitData.quality_feedback_id,
                    quality_feedback_no: self.submitData.quality_feedback_no,
                    fromBtn: "getFileList",
                    callback: () => {
                        self.$message.success("获取附件列表成功");
                    },
                },
            });
        },
        addQuestionType() {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                params: {
                    callback: (d) => {
                        let params = {
                            quality_feedback_id:
                                self.submitData.quality_feedback_id,
                            problem_type: d.liability_question, //问题类型
                        };
                        self.ajax.postStream(
                            "/afterSale-web/api/aftersale/order/qualityfeedback/addProblemType",
                            params,
                            (res) => {
                                if (res.body.result && res.body.content) {
                                    self.$message.success(res.body.msg);
                                    self.getQualityFeedbackEntity();
                                } else {
                                    res.body.msg &&
                                        self.$message.error(res.body.msg);
                                }
                            },
                            (err) => {
                                self.$message.error(err);
                            }
                        );
                    },
                },
                component: () =>
                    import(
                        "@components/after_sales/qualityFeedbackQuestionType"
                    ),
                style: "width:80%;height:80%",
                title: "添加问题类型",
            });
        },
        questionSure() {
            let self = this;
            this.$confirm("确定关闭当前品质反馈单吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    self.ajax.postStream(
                        "/afterSale-web/api/aftersale/order/qualityfeedback/solveQualityFeedBack",
                        {
                            quality_feedback_id:
                                self.params.quality_feedback_id,
                        },
                        (res) => {
                            if (res.body.result && res.body.content) {
                                self.$message.success(res.body.msg);
                                self.getQualityFeedbackEntity().then(() => {
                                    self.setButtonPermissions();
                                });
                            } else {
                                res.body.msg &&
                                    self.$message.error(res.body.msg);
                            }
                        },
                        (err) => {
                            self.$message.error(err);
                        }
                    );
                })
                .catch(() => {
                    this.$message.error("已取消");
                });
        },
        questionSolve(type,d) {
            let self = this;
            // if (type == "reply" && !self.submitData.problem_type) {
            //     this.$message.error("请添加问题类型");
            //     return;
            // }
            let title = type == "reply" ? "问题回复" : "追加问题";
            let url =
                type == "reply"
                    ? "/afterSale-web/api/aftersale/order/qualityfeedbackInformation/feedBackReply"
                    : "/afterSale-web/api/aftersale/order/qualityfeedbackInformation/addfeedBackProblem";
            this.$root.eventHandle.$emit("alert", {
                title: title,
                style: "width:600px;height:300px",
                component: () =>
                    import("@components/after_sales/qualityFeedbackAlert"),
                params: {
                    quality_feedback_id: self.submitData.quality_feedback_id,
                    quality_feedback_no: self.submitData.quality_feedback_no,
                    question_text:d?d.question_text:null,
                    fromBtn: "questionSolve",
                    callback: (d) => {
                        let params = self.formatSubmitParams(d);
                        self.ajax.postStream(
                            url,
                            params,
                            (res) => {
                                if (res.body.result && res.body.content) {
                                    self.$message.success(res.body.msg);
                                    self.getMessageRecordList().then(() => {
                                        self.getQualityFeedbackEntity().then(
                                            () => {
                                                self.setButtonPermissions();
                                            }
                                        );
                                    });
                                } else {
                                    res.body.msg &&
                                        self.$message.error(res.body.msg);
                                }
                            },
                            (err) => {
                                self.$message.error(err);
                            }
                        );
                    },
                },
            });
        },
        InRecord() {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                title: "品质介入",
                style: "width:600px;height:300px",
                component: () =>import("@components/after_sales/qualityFeedbackAlert"),
                params: {
                    fromBtn: "InRecord",
                    callback: (d) => {
                      let params={
                                quality_feedback_id:self.params.quality_feedback_id,
                                intervene_reason: d.intervene_reason,
                                 }
                        self.ajax.postStream("/afterSale-web/api/aftersale/order/qualityIntervention/inventer",params,
                          (res) => {
                              if (res.body.result && res.body.content) {
                                  self.$message.success(res.body.msg);
                                  self.getInRecordList();
                                  self.getQualityFeedbackEntity();
                              } else {
                                  res.body.msg &&
                                      self.$message.error(res.body.msg);
                              }
                          },
                          (err) => {
                              self.$message.error(err);
                          })
                         }

                        }
                })
        },
        reopen() {
            let self = this;
            this.$confirm("确定打开当前品质反馈单吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    self.ajax.postStream(
                        "/afterSale-web/api/aftersale/order/qualityfeedback/restartFeedBack",
                        {
                            quality_feedback_id:
                                self.params.quality_feedback_id,
                        },
                        (res) => {
                            if (res.body.result) {
                                self.$message.success(res.body.msg);
                                self.getQualityFeedbackEntity().then(() => {
                                    self.setButtonPermissions();
                                });
                            } else {
                                res.body.msg &&
                                    self.$message.error(res.body.msg);
                            }
                        },
                        (err) => {
                            self.$message.error(err);
                        }
                    );
                })
                .catch(() => {
                    this.$message.error("已取消");
                });
        },
        changeGreenWay(){
          let self = this;
            this.$root.eventHandle.$emit("alert", {
                title: "是否绿通",
                style: "width:600px;height:300px",
                component: () =>import("@components/after_sales/qualityFeedbackAlert"),
                params: {
                    fromBtn: "GreenWay",
                    greenWayData:{
                      if_greenway:self.submitData.if_greenway,
                      greenway_type:self.submitData.greenway_type,
                      greenway_remark:self.submitData.greenway_remark,
                    },
                    callback: (d) => {
                      let params={
                                quality_feedback_id:self.params.quality_feedback_id,
                                if_greenway: d.if_greenway,
                                greenway_type: d.greenway_type,
                                greenway_remark:d.greenway_remark,
                        }
                        self.ajax.postStream("/afterSale-web/api/aftersale/order/qualityfeedback/modifyGreenway",params,
                          (res) => {
                              if (res.body.result) {
                                  self.$message.success(res.body.msg||'操作成功');
                                  self.getQualityFeedbackEntity();
                                  self.getOperateList()
                              } else {
                                 self.$message.error(res.body.msg||'操作失败');
                              }
                          },
                          (err) => {
                              self.$message.error(err);
                          })
                         }

                        }
                })
        },
        currentPageChange(p) {
            let allList = this.messageRecordList;
            let pageSize = this.messagePageSize;
            let messageStartIndex = (p - 1) * pageSize;
            let messageEndIndex = p * pageSize;
            this.messagePageNow = p;
            this.currentMessageList = allList.slice(
                messageStartIndex,
                messageEndIndex
            );
        },
        //格式化要提交的附件消息格式
        formatFileType(str) {
            let imgStr = ".jpg,.png,.jpeg,.gif,.JPG,.PNG,.JPEG,.GIF";
            let videoStr = ".mp4,.MP4";
            if (imgStr.includes(str)) {
                return "picture";
            } else if (videoStr.includes(str)) {
                return "video";
            } else {
                return "other";
            }
        },
        // 格式化要提交的参数
        formatSubmitParams(data) {
            let self = this;
            let params = {
                quality_feedback_id: self.params.quality_feedback_id,
                cloudQualityFeedbackInformationVoList: [],
            };
            //添加信息列表-附件
            if (data.question_list.length > 0) {
                let informationFileList = data.question_list.map((item) => {
                    return {
                        information_format: self.formatFileType(item.file_type),
                        information_content: item.path,
                    };
                });
                params.cloudQualityFeedbackInformationVoList = informationFileList;
            }

            //添加信息列表-文本
            if (data.question_text && data.question_text.length > 0) {
                let informationTextObj = {
                    information_format: "text",
                    information_content: data.question_text,
                };
                params.cloudQualityFeedbackInformationVoList.unshift(
                    informationTextObj
                );
            }

            return params;
        },
        // 快速回复下拉框点击弹框
        quickReplay(msg_type){
			let self = this;
            // if(!self.submitData.problem_type){
            //     return this.$message.error("请添加问题类型")
            // }
			this.$root.eventHandle.$emit('alert', {
                title:msg_type==="COMMON"?"通用类":"批量类",
				params: {
                    msg_type,
                    if_enable:"Y",
					 callback: (d) => {
                        self.questionSolve('reply',d)
                    },
                    user_type: 'FEEDBACK'
				},
				style:'width:600px;height:300px',
				component: () => import('@components/after_sales/qualityFeedbackReplyList.vue')
			})
        },
        addGreenWayRow() {
            let addData = {};
            addData.arowId = new Date().getTime();
            this.greenWayList.unshift(addData);
        },
        //删除
        deleteGreenWayRow() {
            if (this.multipleGreenWaySelection.length < 1) {
                this.$message.warning("请至少选择一条处理方案数据！");
                return
            }
            let delAddIdList = []; //选择的所有新增未保存的行
            let delIdList = []; //选择的所有已保存的行
            this.multipleGreenWaySelection.forEach((val) => {
                if (!!val.arowId) {
                  delAddIdList.push(val.arowId);
                }
                if (!!val.track_id) {
                  delIdList.push(val.track_id);
                }
            });
            this.$confirm("是否确定删除已选数据？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(()=>{
              this.greenWayBtns[2].loading = true;
              if(delIdList.length>0){
                  this.ajax.postStream(
                    '/afterSale-web/api/aftersale/order/qualityfeedbacktrack/delete',
                    delIdList,
                    (res) => {
                        if (res.body.result) {
                            this.$message.success(res.body.msg);
                            this.greenWayList = this.greenWayList.filter((val) => {
                              return !delIdList.includes(val.track_id);
                            });
                            this.getOperateList()
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                        }
                        this.greenWayBtns[2].loading = false;
                    },
                    (err) => {
                        this.$message.error(err);
                        this.greenWayBtns[2].loading = false;
                    }
                );
              }
              if(delAddIdList.length>0){
                this.greenWayList = this.greenWayList.filter((val) => {
                  return !delAddIdList.includes(val.arowId);
                });
                this.greenWayBtns[2].loading = false;
              }
            })
        },
        saveGreenWayRow(){
          if(this.greenWayList.length===0){
            this.$message.warning('请至少添加一条处理方案数据！')
            return;
          }
          if (this.multipleGreenWaySelection.length < 1) {
            this.$message.warning("请至少选择一条处理方案数据！");
            return;
          }
          const emptyProgrammeIndex=this.multipleGreenWaySelection.findIndex(item=>!item.track_programme);
          const emptyAmountIndex=this.multipleGreenWaySelection.findIndex(item=>!item.track_amount);
          if(emptyProgrammeIndex!==-1){
            this.$message.warning('请输入选择的第'+(emptyProgrammeIndex+1)+'行的处理方案')
            return;
          }
          if(emptyAmountIndex!==-1){
            this.$message.warning('请输入选择的第'+(emptyAmountIndex+1)+'行的处理金额')
            return;
          }
          const validateIndex=this.multipleGreenWaySelection.findIndex(item=>item.track_amount&&!/^(0\.[1-9]\d*|[1-9]\d*(\.\d{1,2})?)$/.test(item.track_amount));
          if(validateIndex!==-1){
            this.$message.warning('选择的第'+(validateIndex+1)+'行金额格式错误')
            return;
          }
          const params=this.multipleGreenWaySelection.map(item=>{
            const obj={
              quality_feedback_id: this.params.quality_feedback_id,
              track_programme: item.track_programme,
              track_amount: item.track_amount,
              track_remark: item.track_remark,
              order_no: item.order_no,
            };
            if(item.track_id){
              obj.track_id=item.track_id;
            }
            return obj
          })
          this.greenWayBtns[1].loading = true;
          this.ajax.postStream(
                '/afterSale-web/api/aftersale/order/qualityfeedbacktrack/saveOrUpdateTrack',
                params,
                (res) => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg);
                        this.initDetail();
                        this.getOperateList();
                    } else {
                        res.body.msg && this.$message.error(res.body.msg);
                    }
                    this.greenWayBtns[1].loading = false;
                },
                (err) => {
                    this.$message.error(err);
                    this.greenWayBtns[1].loading = false;
                }
            );
        },
        getOperateList() {
          let self = this;
          return new Promise((resolve, reject) => {
            this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.params.quality_feedback_id }, res => {
              if (res.body.result) {
                this.operateLogList = res.body.content || []
                console.log('this.operateLogList', this.operateLogList)
              }
            })
          })
        },
        handleFirstTabChange() {
          if (this.firstTab === 'greenWayInfo') {
            this.getGreenWayList()
          }
        },
        handleSecondTabChange() {
          if (this.secondTab === 'OperateRecord') {
            this.getOperateList()
          }
        },
    },
    components: {
        qualityFeedbackInformationList,
    },
};
</script>
<style scoped>
.el-input,
.el-select,
.el-date-editor.el-input {
    width: 196px;
}
.el-form-item{
    white-space: nowrap;
}
.record-list {
    flex: 1;
}
.mgt10 {
    margin-top: 10px;
}
.ptb2 {
    padding: 10px 0px;
}
.detail-form {
    height: 100%;
    display: flex;
    flex-direction: column;
}
.message-record {
    display: flex;
    flex-direction: column;
}
.message-list {
    position: absolute;
    top: 30px;
    bottom: 0;
    left: 0;
    right: 0;
}
.nowrap {
    white-space: nowrap;
}
.green-way-list /deep/.el-table .el-table__body-wrapper td .cell {
    height: 24px;
}
</style>
<style>
.quality-feedback .xpt-flex__bottom .el-tabs .el-tabs__content {
    overflow: hidden !important;
}
</style>
