<template>
    <div class="message-content">
        <div class="message">
            <el-card
                v-for="item in followList"
                :key="item.information_id"
                class="card"
            >
                <div class="m-header">
                    <span class="name">{{ item.reply_name }}</span>
                    <span class="name">{{rejectOption[item.reply_type ] }}</span>
                    <span class="time">{{
                        item.create_time | dataFormat1
                    }}</span>
                </div>
                <div class="m-footer">
                    <div>
                        <div class="reply-content">
                            {{ item.reply_content }}
                        </div>
                        <template v-if="item.fileList && item.fileList.length > 0">
                        <a href="#" v-for="file in item.fileList" :key="file.file_id" class="showImg">
                            <video v-if="/\.(mp4)$/i.test(file.path)" controls class="video">
                                <source :src="file.path" />
                                <p>
                                    Your browser doesn't support HTML5 video. Here
                                    is a
                                    <a :href="file.path">link to the video</a>
                                    instead.
                                </p>
                            </video>
                            <img v-else @click="showImageList(file)" :src="changeOssUrl(file.path)" alt="图片" width="60" />
                        </a>
                        </template>
                    </div>
                    
                </div>
            </el-card>
        </div>
       
        <xpt-image
            :images="imageList"
            :show="ifShowImage"
            :ifUpload="false"
            :ifClose="false"
            @close="closeShowImage"
        >
        </xpt-image>
    </div>
</template>
<script>
import Fn from "@common/Fn.js";
export default {
    data() {
        return {
            imageList: [],
            ifShowImage: false,
            rejectOption:{
                reply:'回复',
                representative:'受理售后工单',
                processed:'完成售后工单',
                furtherInformation: '补充了资料',
                submitWorkOrder: '提交售后工单'
            }
        };
    },
    props: {
        infoList: {
            type: Array,
            default: () => []
        },
        fileList: {
            type: Array,
            default: [],
        },
        total: {
            type: Number,
            default: 0,
        },
        currentPage:{
            type:Number,
            default:1
        },
        buyObj:{
            type:Object,
            default:{buyer_name:'',question_submit_time:'',appraise_content:''}
        },
        followList: {
            type: Array,
            default: () => []
        },
    },
    methods: {
        changeOssUrl(url){
            return Fn.changeOssUrl(url);
        },
        currentPageChange(p) {
            // 当前页码改变时派发事件：current-page-change
            this.$emit("current-page-change", p);
        },
        isImage(str) {
            var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
            return reg.test(str);
        },
        showImageList(row) {
            this.initImageList(row);
            this.ifShowImage = true;
        },
        //初始化预览列表
        initImageList(row) {
            let imglist = [];
            //过滤出只有图片的列表
            let list = this.fileList.filter((item) =>
                this.isImage(this.changeOssUrl(item.path))
            );
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = Fn.changeOssUrl(value.path);
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = value.name;
                obj.isPucture = true;
                //确定要预览那张图片
                if (value.file_id === row.file_id) {
                    obj.active = true;
                } else {
                    obj.active = false;
                }
                imglist.push(obj);
            });
            
            this.imageList = imglist;
        },
        closeShowImage() {
            this.ifShowImage = false;
        },
    },
};
</script>
<style scope>
.message-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    padding-bottom: 30px;
}
.message {
    overflow: auto;
}
.card {
    width: 60%;
    margin: 15px;
    margin-top: 5px;
}
.card-no {
    width: 60%;
    margin: 10px;
    padding: 0px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.card > div {
    background: #f3f3f2;
    color: #777373;
    padding: 5px;
    font-size: 12px;
}
.m-header,
.reply-content {
    line-height: 20px;
    white-space:normal;
    width: 100%;
    margin-bottom: 5px;
}
.name {
    margin-right: 5px;
}
.m-footer {
    color: #000;
}
.picture {
    max-height: 100px;
    margin-top: 5px;
    margin-left: 5px;
    cursor: pointer;
}
.video {
    max-height: 200px;
    margin-top: 5px;
    margin-left: 5px;
    width: 300px;
}
</style>
