<!-- 推荐受理人设置 -->
<template>
    <xpt-list-dynamic
        ref="list"
        :data="list"
        :btns="btns"
        :colData="cols"
        :selection="selection"
        :pageTotal="count"
        :searchPage="search.page_name"
        @search-click="presearch"
        @selection-change="handleSelectionChange"
        @cell-click="handleCellClick"
        @page-size-change="sizeChange"
        @current-page-change="pageChange"
    ></xpt-list-dynamic>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            search: {
                page_name: "cloud_quality_feedback_handle_setting",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
                if_need_page: "Y",
            },
            list: [],
            selection: "checkbox",
            count: 0,
            multipleSelection: [], //列表选择索引
            currentCellSelect: {}, //选择的当前单元格
            btns: [
                {
                    type: "success",
                    txt: "刷新",
                    click() {
                        self.refresh();
                    },
                    loading: false,
                },
                {
                    type: "primary",
                    txt: "新增",
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.addRow();
                    },
                },
                {
                    type: "primary",
                    txt: "保存",
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.save();
                    },
                    loading: false,
                },
                {
                    type: "warning",
                    txt: "生效",
                    click() {
                        self.changeStatus("ENABLED");
                    },
                    loading: false,
                },
                {
                    type: "warning",
                    txt: "失效",
                    click() {
                        self.changeStatus("DISABLED");
                    },
                    loading: false,
                },
                {
                    type: "danger",
                    txt: "删除",
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.deleteRow();
                    },
                },
            ],
            cols: [
                {
                    label: "供应商ID",
                    prop: "suppiler_id",
                    bool: true,
                    iconClick: self.selectSupplier,
                    // isClickEvent: true,
                    disabled(row) {
                        return row.status == "DISABLED"||!!row.suppiler_id;
                    },
                },
                {
                    label: "供应商名称",
                    prop: "suppiler_name",
                },
                {
                    label: "品质反馈推荐处理人",
                    prop: "current_handler_name",
                    bool: true,
                    iconClick:()=>{
                        self.selectAcceptor('current_handler_')
                    }, 
                    // isClickEvent: true,
                    disabled(row) {
                        return row.status == "DISABLED";
                    },
                },
                {
                    label: "产品咨询推荐受理人",
                    prop: "pri_handler_name",
                    bool: true,
                    iconClick: ()=>{
                        self.selectAcceptor('pri_handler_')
                    },
                    // isClickEvent: true,
                    disabled(row) {
                        return row.status == "DISABLED";
                    },
                },
                {
                    label: "状态",
                    prop: "status",
                    formatter(val) {
                        switch (val) {
                            case "ENABLED":
                                return "生效";
                                break;
                            case "DISABLED":
                                return "失效";
                                break;
                            default:
                                return val;
                                break;
                        }
                    },
                },
                {
                    label: "创建人",
                    prop: "creator_name",
                },
                {
                    label: "创建时间",
                    prop: "create_time",
                    format: "dataFormat1",
                },
                {
                    label: "更新人",
                    prop: "last_modifier_name",
                },
                {
                    label: "更新时间",
                    prop: "last_modify_time",
                    format: "dataFormat1",
                },
            ],
        };
    },
    methods: {
        presearch(list, resolve) {
            this.search.where = list;
            this.searching(resolve);
        },
        searching(resolve) {
            let self = this;
            this.btns[0].loading = true;
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/order/qualityfeedbackHandingSetting/getSettingList",
                this.search,
                (res) => {
                    if (res.body.result) {
                        self.count = res.body.content.count;
                        if (res.body.content.list) {
                            self.list = res.body.content.list;
                        }
                        self.$message.success(res.body.msg);
                    } else {
                        self.list = [];
                        self.count = 0;
                        self.$message.error(res.body.msg);
                    }
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                },
                (err) => {
                    self.$message.error(err);
                    "function" === typeof resolve ? resolve() : this;
                    self.btns[0].loading = false;
                }
            );
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log(this.multipleSelection);
        },
        //新增
        addRow() {
            let addData = {};
            addData.arowId = new Date().getTime();
            addData.suppiler_id = "";
            addData.suppiler_name = "";
            addData.current_handler_id = "";
            addData.pri_handler_id = ""
            addData.current_handler_name = "";
            addData.status = "ENABLED";
            this.list.unshift(addData);
        },
        //删除
        deleteRow() {
            if (this.multipleSelection.length < 1) {
                this.$message.error("请选择至少一行数据");
            }
            let hasSaveId = false;
            let arowIdList = []; //选择的所有新增未保存的行
            let list = this.list;
            this.multipleSelection.forEach((val) => {
                if (!!val.setting_id) {
                    hasSaveId = true;
                }
                if (!!val.arowId) {
                    arowIdList.push(val.arowId);
                }
            });
            this.list = list.filter((val) => {
                return !val.arowId || !arowIdList.includes(val.arowId);
            });
            console.log(this.list);
            if (hasSaveId) {
                this.$message.warning("只能删除未保存的行");
            }
        },
        //选择供应商
        selectSupplier() {
            let params = {},
                self = this;
            // 选择商品
            params.callback = (data) => {
                console.log("供应商列表：", self.list);
                if (self.ifSupplierRepeat(data.supplier_id)) {
                    this.$message.error(
                        `供应商【${data.supplier_name}】已设置推荐处理人`
                    );
                    return;
                }
                this.$message.success("供应商选择成功");
                if (data) {
                    self.currentCellSelect.suppiler_id = data.supplier_id;
                    self.currentCellSelect.suppiler_name = data.supplier_name;
                    self.currentCellSelect.editrow = true; //标志修改数据
                }
            };
            self.$root.eventHandle.$emit("alert", {
                params: params,
                component: () =>
                    import("@components/sale_plan/selectSupplier.vue"),
                style: "width:800px;height:500px",
                title: "供应商列表",
            });
        },
        // 供应商是否重复
        ifSupplierRepeat(id) {
            return this.list.some(
                (item) => item.suppiler_id == id && item.status == "ENABLED"
            );
        },
        //选择受理人
        selectAcceptor(type) {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                title: "选择推荐处理人",
                style: "width:800px;height:500px",
                component: () =>
                    import("@components/after_sales/qualityFeedbackAlert"),
                params: {
                    fromBtn: "deliver",
                    callback: (data) => {
                        console.log(data);
                        self.currentCellSelect[type+'name'] =
                            data.fullName;
                        self.currentCellSelect[type+'id'] = data.userId;
                        self.currentCellSelect.editrow = true; //标志修改数据
                    },
                },
            });
        },
        //生效or失效
        changeStatus(status) {
            let self = this;
            let ifHasAdd = this.list.some((item) => item.arowId);
            if (ifHasAdd) {
                this.$message.error("列表存在新增数据，请执行保存操作");
                return;
            } else if (this.multipleSelection.length == 0) {
                this.$message.error("请选择需要操作的行");
                return;
            }

            let ifAllSameStatus = self.multipleSelection.every(
                (item) => item.status == status
            ); //所选择全部相同状态
            let ifHasSameStatus = self.multipleSelection.some(
                (item) => item.status == status
            ); //所选择有相同状态
            
            //判断是否同时生效同一个供应商，失效不控制
            let ifHasSameSupplierId=false
            if(status=="ENABLED"){
                let selectSupplierIdList=self.multipleSelection.map(item=>item.suppiler_id)
                ifHasSameSupplierId=self.multipleSelection.some(item=>selectSupplierIdList.indexOf(item.suppiler_id)!=selectSupplierIdList.lastIndexOf(item.suppiler_id))
            }
            
            if (ifAllSameStatus) {
                let msg = status == "ENABLED" ? "已生效的" : "已失效的";
                this.$message.error(`${msg}不可以重复操作`);
            } else if (ifHasSameStatus) {
                this.$message.error(`请选择生效状态一致的数据进行批量操作`);
            } else if(ifHasSameSupplierId){
                this.$message.error(`无法同时生效相同的供应商`);
            }else {
                // 生效失效接口
                let cloudQualityFeedbackHandleSettingVoTempsList = self.formatSaveData(
                    "changeStatus"
                );
                self.ajax.postStream(
                    "/afterSale-web/api/aftersale/order/qualityfeedbackHandingSetting/updateStatus",
                    {
                        cloudQualityFeedbackHandleSettingVoTempsList,
                        status: status,
                    },
                    (res) => {
                        if(res.body.result){
                            self.$message.success(res.body.msg)
                            self.searching()
                        }else{
                            self.$message.error(res.body.msg)
                        }
                    },
                    (err) => {
                        self.$message.error(err);
                    }
                );
            }
        },
        //点击行
        handleCellClick(row) {
            console.log("行的回调函数：", row);
            this.currentCellSelect = row;
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page_size = size;
            this.searching();
        },
        pageChange(page_no) {
            // 页数改变
            this.search.page_no = page_no;
            this.searching();
        },
        refresh() {
            this.searching();
        },
        //格式化保存数据，包括新增和已修改的，格式化生失效数据，只包含选择的
        formatSaveData(type) {
            let submitRowList = [];
            if (type == "save") {
                let saveRowList = this.list.filter((val) => val.editrow);
                submitRowList = saveRowList.map((val) => {
                    return {
                        setting_id: val.setting_id ? val.setting_id : null,
                        suppiler_id: val.suppiler_id,
                        current_handler_id: val.current_handler_id,
                        pri_handler_id: val.pri_handler_id,
                    };
                });
            } else if (type = "changeStatus") {
                let statusRowList = this.multipleSelection;
                submitRowList = statusRowList.map((val) => {
                    return {
                        setting_id: val.setting_id,
                    };
                });
            }
            console.log(submitRowList);
            return submitRowList;
        },
        save() {
            let self = this;
            let data = [];
            let msg = "";
            this.list.every(item=>!item.editrow)?msg="数据无更新":()=>{}
            this.list.find((item, index) => {
                if (item.suppiler_id == "" && item.current_handler_id != "") {
                    msg = `第${index + 1}行供应商不能为空`;
                    return true;
                } else if (
                    item.suppiler_id != "" &&
                    item.current_handler_id == ""
                ) {
                    msg = `第${index + 1}行推荐人不能为空`;
                    return true;
                }
            });
            // console.log(this.list);
            if (msg) {
                this.$message.error(msg);
                return;
            }
            let cloudQualityFeedbackHandleSettingVoTempsList = this.formatSaveData(
                "save"
            );
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/order/qualityfeedbackHandingSetting/saveEntity",
                {
                    cloudQualityFeedbackHandleSettingVoTempsList,
                },
                (res) => {
                    if(res.body.result){
                        this.$message.success(res.body.msg)
                        self.searching()
                    }else{
                        this.$message.error(res.body.msg)
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
            // this.btns[2].loading = true;
            // // 保存接口：包括新增和修改
            // window.setTimeout(()=>{
            //     this.$message.success("保存成功！")
            //     this.btns[2].loading = false;
            //     this.refresh();
            // },2000)
        },
    },
    mounted() {
        this.searching();
    },
    destroyed() {},
};
</script>
<style lang="css" scoped>
</style>