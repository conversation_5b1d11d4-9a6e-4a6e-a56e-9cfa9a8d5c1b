<template>
    <div class="message-content">
        <div class="message">
            <el-card
                v-for="item in list"
                :key="item.information_id"
                class="card"
            >
                <div class="m-header">
                    <span class="name">{{ item.creator_name }}</span>
                    <span class="time">{{
                        item.create_time | dataFormat1
                    }}</span>
                </div>
                <div class="m-footer">
                    <div v-if="item.information_format == 'picture'">
                        <img
                            :src="item.information_content"
                            alt=""
                            class="picture"
                            @click="showImageList(item)"
                        />
                    </div>
                    <div v-else-if="item.information_format == 'video'">
                        <video controls class="video">
                            <source :src="item.information_content" />
                            <p>
                                Your browser doesn't support HTML5 video. Here
                                is a
                                <a :href="item.information_content"
                                    >link to the video</a
                                >
                                instead.
                            </p>
                        </video>
                    </div>
                    <div
                        v-else-if="item.information_format == 'text'"
                        style="word-break: break-word; white-space: normal"
                    >
                        {{ item.information_content }}
                    </div>
                    <div v-else>
                        <div class="other-content">
                            {{ item.information_content }}
                        </div>
                        <div class="other-download">
                            <a :href="item.information_content" target="_blank"
                                ><el-button type="primary" size="mini"
                                    >暂不支持在线查看，点击下载</el-button
                                ></a
                            >
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
        <el-pagination
            small
            layout="prev, pager, next, total"
            :total="total"
            :current-page="currentPage"
            class="card-no"
            @current-change="currentPageChange"
        >
        </el-pagination>
        <xpt-image
            :images="imageList"
            :show="ifShowImage"
            :ifUpload="false"
            :ifClose="false"
            @close="closeShowImage"
        >
        </xpt-image>
    </div>
</template>
<script>
import Fn from "@common/Fn.js";
export default {
    data() {
        return {
            imageList: [],
            ifShowImage: false,
        };
    },
    props: {
        list: {
            type: Array,
            default: [],
        },
        total: {
            type: Number,
            default: 0,
        },
        currentPage:{
            type:Number,
            default:1
        }
    },
    mounted() {},
    methods: {
        currentPageChange(p) {
            // 当前页码改变时派发事件：current-page-change
            this.$emit("current-page-change", p);
            console.log(p);
        },
        isImage(str) {
            var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
            return reg.test(str);
        },
        showImageList(row) {
            this.initImageList(row);
            this.ifShowImage = true;
        },
        //初始化预览列表
        initImageList(row) {
            let imglist = [];
            //过滤出只有图片的列表
            let list = this.list.filter((item) =>
                this.isImage(item.information_content)
            );
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = value.information_content;
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = value.creator_name;
                obj.isPucture = true;
                //确定要预览那张图片
                if (value.information_id === row.information_id) {
                    obj.active = true;
                } else {
                    obj.active = false;
                }
                imglist.push(obj);
            });
            this.imageList = imglist;
        },
        closeShowImage() {
            this.ifShowImage = false;
        },
    },
};
</script>
<style scope>
.message-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}
.message {
    overflow: auto;
}
.card {
    width: 60%;
    margin: 15px;
    margin-top: 5px;
}
.card-no {
    width: 60%;
    margin: 10px;
    padding: 0px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.card > div {
    background: #f3f3f2;
    color: #777373;
    padding: 5px;
    font-size: 12px;
}
.m-header,
.other-content {
    margin-bottom: 5px;
}
.name {
    margin-right: 5px;
}
.m-footer {
    color: #000;
}
.picture {
    max-height: 100px;
    margin-top: 5px;
    margin-left: 5px;
    cursor: pointer;
}
.video {
    min-width: 200px;
    max-height: 200px;
    margin-top: 5px;
    margin-left: 5px;
}
</style>