<!-- 品质反馈列表-->
<template>
    <xpt-list-dynamic
        ref="list"
        :data="list"
        :btns="btns"
        :colData="cols"
        :selection="selection"
        :pageTotal="count"
        :searchPage="search.page_name"
        @search-click="presearch"
        @selection-change="handleSelectionChange"
        @page-size-change="sizeChange"
        @current-page-change="pageChange"
        :taggelClassName="taggelClassName"
        :filterList="filterList"
    ></xpt-list-dynamic>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            search: {
                page_name: "cloud_quality_feedback",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
                if_need_page: "Y",
            },
            list: [],
            selection: "checkbox",
            count: 0,
            multipleSelection: [], //列表选择索引
            btns: [
                {
                    type: "success",
                    txt: "刷新",
                    click: self.searching,
                    loading: false,
                },
                {
                    type: "info",
                    txt: "导出",
                    click: self.exportExcel,
                    loading: false,
                },
                {
                    type: "warning",
                    txt: "导出结果",
                    click: self.exportResult,
                    loading: false,
                },
            ],
            cols: [
                {
                    prop: "quality_feedback_no",
                    label: "品质反馈单号",
                    width:150,
                    redirectClick(row) {
                        let params = {};
                        params.quality_feedback_id = row.quality_feedback_id;
                        params.quality_feedback_no = row.quality_feedback_no;
                        params.project_from = "qualityFeedbackList";
                        self.$root.eventHandle.$emit("creatTab", {
                            name: "品质反馈单详情",
                            params: params,
                            component: () =>
                                import(
                                    "@components/after_sales/qualityFeedbackDetail.vue"
                                ),
                        });
                    },
                },
                {
                    prop: "material_name",
                    label: "发运物料名称",
                    width:94
                },
                {
                    prop: "material_number",
                    label: "发运物料编码",
                    width:120
                },
                {
                    prop: "material_project_team",
                    label: "物料项目组",
                    width:120
                },
                {
                    prop: "information_content",
                    label: "问题",
                },
                {
                    prop: "lot",
                    label: "批号",
                    width:140
                },
                {
                    prop: "suppiler_name",
                    label: "供应商名称",
                    width:205
                },
                {
                    prop: "status",
                    label: "状态",
                    formatter(val) {
                        return{
                            'EXECUTING':'处理中',
                            'WAITING_FOR_CONFIRM':'待确认',
                            'FINISHED':'已完结'
                        }[val]||val
                    },
                    width:60
                },
                {
                    prop: "after_order_no",
                    label: "售后单号",
                    width:100
                },
                {
                    prop: "problem_type",
                    label: "问题类型",
                    width:130
                },
                {
                    prop: "sales_profit_class",
                    label: "销售利润分类",
                    width:130,
                    format: 'auxFormat',
					formatParams: 'sale_profit_class'
                },
                {
                    prop: "material_spec",
                    label: "发运物料规格",
                    width:312
                },
                {
                    prop: "creator_name",
                    label: "提交人",
                },
                {
                    prop: "create_time",
                    label: "提交时间",
                    format: "dataFormat1",
                    width:130
                },
                {
                    prop: "creator_group_name",
                    label: "提交人分组",
                    width:85
                },
                {
                    prop: "handler_name",
                    label: "推荐售后处理人",
                },
                {
                    prop: "last_modifier_name",
                    label: "更新人",
                },
                {
                    prop: "last_modify_time",
                    label: "更新时间",
                    format: "dataFormat1",
                    width:130
                },
                {
                    prop: "batch_trade_no",
                    label: "批次单号",
                    width:200
                },
                {
                    prop: "merge_trade_no",
                    label: "合并单号",
                    width:180
                },
                {
                    prop: "customer_name",
                    label: "买家昵称",
                    width:180
                },
                {
                    prop: "first_reply_time",
                    label: "售后初次处理时间",
                    format: "dataFormat1",
                    width:130
                },
                {
                    prop: "intervene_first_reply_time",
                    label: "品质初次处理时间",
                    format: "dataFormat1",
                    width:130
                },
                {
                    prop: "if_overtime",
                    label: "是否超72小时",
                    formatter(val) {
                        return{
                            'N':'否',
                            'Y':'是',
                        }[val]||val
                    },
                    width:98
                },
                {
                    prop: "if_intervene",
                    label: "是否品质介入",
                    formatter(val) {
                        return{
                            'N':'否',
                            'Y':'是',
                        }[val]||val
                    },
                    width:98
                },
                {
                    prop: "intervene_person_name",
                    label: "品质部跟进人",
                    width:130
                },
            ],
            filterList: []
        };
    },
    created () {
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
		this.ifPurchaseDealerOfProxyId()
	},
	beforeDestroy(){
		//解除监听切换业务代理事件
		this.$root.eventHandle.$off('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
	},
    methods: {
		ifPurchaseDealerOfProxyId() {
			let id = this.getEmployeeInfo('personId'), self = this
			this.ajax.get('/user-web/api/userPerson/getUserPerson/'+id,function(data){
				data = data.body;
				if(data.result){
					if (data.content && data.content.type == 'PURCHASE_DEALER') {
                        self.filterList = ['suppiler_name']
                    } else {
                        self.filterList = []
                    }
				}
			},function(error){
                self.filterList = []
			})
		},
        presearch(list, resolve) {
            this.search.where = list;
            this.searching(resolve);
        },
        searching(resolve) {
            let self = this;
            this.btns[0].loading = true;
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/order/qualityfeedback/getQualityFeedbackList",
                this.search,
                (res) => {
                    if (res.body.result) {
                        self.count = res.body.content.count;
                        if (res.body.content.list) {
                            self.list = res.body.content.list;
                        }
                        self.$message.success(res.body.msg);
                    } else {
                        self.list = [];
                        self.count = 0;
                        self.$message.error(res.body.msg);
                    }
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                },
                (err) => {
                    self.$message.error(err);
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                }
            );
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page_size = size;
            this.searching();
        },
        pageChange(page_no) {
            // 页数改变
            this.pageNow = page_no;
            this.search.page_no = page_no;
            this.searching();
        },
        //状态列添加标点
        taggelClassName(row) {
            if (row.status === "EXECUTING"&&row.if_overtime=='Y'){
                return this.$style["execut-tag-time-out"];
            }
            else if (row.status === "EXECUTING"&&(row.if_overtime=='N'||!row.if_overtime)){
                return this.$style["execut-tag"];
            }
            else if (row.status === "WAITING_FOR_CONFIRM"&&row.if_overtime=='Y'){
                 return this.$style["wait-tag-time-out"];
            }
            else if (row.status === "WAITING_FOR_CONFIRM"&&(row.if_overtime=='N'||!row.if_overtime)){
                 return this.$style["wait-tag"];
            }
            else if (row.status === "FINISHED"&&row.if_overtime=='Y'){
                 return this.$style["time-out"];
            }
            else if (row.status === "FINISHED"){
                 return
            }
        },
        //导出
        exportExcel() {
            this.btns[1].loading = true;
            this.ajax.postStream(
                "/reports-web/api/reports/afterSaleExport/exportQualityFeedback",
                this.search,
                (res) => {
                    if (res.body.result) {
                        res.body.msg && this.$message.success(res.body.msg);
                    } else {
                        res.body.msg && this.$message.error(res.body.msg);
                    }
                    this.btns[1].loading = false;
                },
                (err) => {
                    this.btns[1].loading = false;
                    this.$message.error(err);
                }
            );
        },
        exportResult(){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
					type: 'EXCEL_TYPE_QUALITY_FEEDBACK',
					},
				},
			})
        }
    },
    mounted() {
        let self = this;
        self.searching();
        // 检测新增、编辑数据，刷新
        // self.$root.eventHandle.$on("refresh_list", function () {
        //   self.searching();
        // });
    },
    destroyed() {
        // this.$root.offEvents("refresh_list");
    },
};
</script>
<style module>
.execut-tag td:nth-child(2):after {
    content: "";
    position: absolute;
    right: 4px;
    top: 9px;
    border: 4px solid #ff0000;
    border-radius: 100%;
}
.execut-tag-time-out,.execut-tag-time-out td{
    background-color: #f99 !important;
}
.execut-tag-time-out td:nth-child(2):after {
    content: "";
    position: absolute;
    right: 4px;
    top: 9px;
    border: 4px solid #ff0000;
    border-radius: 100%;
}
.wait-tag td:nth-child(2):after {
    content: "";
    position: absolute;
    right: 4px;
    top: 9px;
    border: 4px solid #13ce66;
    border-radius: 100%;
}
.wait-tag-time-out,.wait-tag-time-out td{
    background-color: #f99 !important;
}
.wait-tag-time-out td:nth-child(2):after {
    content: "";
    position: absolute;
    right: 4px;
    top: 9px;
    border: 4px solid #13ce66;
    border-radius: 100%;
}
.time-out,.time-out td{
    background-color: #f99 !important;
}
</style>