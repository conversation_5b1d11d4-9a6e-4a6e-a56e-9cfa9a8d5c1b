<!-- 品质反馈问题类型 -->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-col :span="6">
			<el-button
				type="primary"
				size="mini"
				@click="() => submit()"
				:disabled="!selectData"
			>确定</el-button>
		</el-col>
		<el-col :span="18">
			<div style="float:right;">
				<el-select v-model="searchObj.type" size="mini" placeholder="请选择" @change="() => searchObj.keyword = ''">
					<el-option label="责任问题" value="liability_question"></el-option>
					<el-option label="责任范围" value="liability_scope"></el-option>
					<el-option label="责任类型" value="liability_type"></el-option>
				</el-select>
				<el-select v-if="searchObj.type === 'liability_scope'" v-model="searchObj.keyword" size="mini">
					<el-option
						v-for="(val, key) in liability_scope_options"
						:key="key"
						:label="val"
						:value="key"
					></el-option>
				</el-select>
				<el-input v-else size='mini' v-model="searchObj.keyword" placeholder="请输入搜索内容" style="width:150px;"></el-input>
				<el-button type="primary" size="mini" @click="searchFun">查询</el-button>
			</div>
		</el-col>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData='colData'
		selection="radio"
		:pageTotal='pageTotal'
		:showHead="false"
		:pageLength="100"
		@radio-change='s => selectData = s'
		@row-dblclick="submit"
		@page-size-change='pageChange'
		@current-page-change='currentPageChange' ref='xptList'>
	</xpt-list>
</div>
</template>
<script>
export default {
	props:['params'],
	data (){
		let self = this;
		return {
			roleList:[],
			selectData: null,

			searchObj: {
				pageNo: 1,
				pageSize: 100,
				keyword: '',
				type: 'liability_question',
			},
			pageTotal:0,
			liability_scope_options: {
				BD_Empinfo: '员工',
		        BD_Supplier: '供应商',
		        BD_Customer: '客户',
		        BD_Department: '部门',
			},
			colData: [{
				label: '责任问题',
				prop: 'liability_question',
				width: 150,
			},{
				label: '责任范围',
				prop: 'liability_scope',
				formatter: prop => (this.liability_scope_options[prop] || prop),
				width: 100,
			},{
				label: '责任类型',
				prop: 'liability_type',
				width: 100,
			},{
				label: '责任问题描述',
				prop: 'remark',
				width: 525,
			},{
				label: '是否生成纠纷单',
				prop: 'if_create_dispute',
				formatter: prop => ({
					true: '是',
					false: '否',
				}[prop]),
			},{
				label: '是否基金',
				prop: 'if_foundation',
				formatter: prop => ({
					true: '是',
					false: '否',
				}[prop]),
			},{
				label: '是否全额',
				prop: 'if_full_deduct',
				formatter: prop => ({
					true: '是',
					false: '否',
				}[prop]),
			}],
		}
	},
	methods: {
		searchFun(){
			this.ajax.postStream('/afterSale-web/api/aftersale/order/qualityfeedback/liabilityQuestionList', {
				pageNo: this.searchObj.pageNo,
				pageSize: this.searchObj.pageSize,
				user_type :this.params.user_type,

				[this.searchObj.type === 'liability_scope' ? 'liability_scope' : 'searchWork']: this.searchObj.keyword,
			}, res => {
				if(res.body.result){
					this.roleList = res.body.content.list
					this.pageTotal = res.body.content.count
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		pageChange(pageSize){
			this.searchObj.pageSize = pageSize
			this.searchFun()
		},
		currentPageChange(page){
			this.searchObj.pageNo = page
			this.searchFun()
		},
		submit (selectData){
			this.params.callback(selectData || this.selectData)
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
	},
	mounted (){
		this.searchFun()
	},
}
</script>