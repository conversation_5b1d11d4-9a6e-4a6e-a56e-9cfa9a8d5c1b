<!-- 品质反馈快速回复设置 -->
<template>
    <xpt-list
        ref="list"
        :data="list"
        :btns="btns"
        :colData="cols"
        :pageTotal="count"
        :selection="selection"
        :searchPage="search.page_name"
        @search-click="presearch"
        @cell-click="handleCellClick"
        @page-size-change="sizeChange"
        @current-page-change="pageChange"
    ></xpt-list>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            search: {
                page_name: "cloud_quality_feedback_msg",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
                if_need_page: "Y",
            },
            list: [],
            selection: "",
            count: 0,
            btns: [
                {   
                    type: "success",
                    txt: "刷新",
                    click() {
                        self.refresh();
                    },
                    loading: false,
                },
                {
                    type: "primary",
                    txt: "新增",
                    click() {
                        self.popover();
                    },
                }
            ],
            cols:[
                {
					label: '序号',
					prop: '_index',
					align: 'center',
					width: 50,
				},
                {
                    label: "话术类型",
                    prop: "msg_type",
                      formatter(val) {
                        switch (val) {
                            case "COMMON":
                                return "通用类";
                                break;
                            case "BATCH":
                                return "批量类";
                                break;
                            default:
                                return val;
                                break;
                        }
                    },
                },
                {
                    label: "应用单据类型",
                    prop: "user_type",
                      formatter(val) {
                        switch (val) {
                            case "FEEDBACK":
                                return "品质反馈单";
                                break;
                            case "COUPLEBACK":
                                return "客户售后反馈单";
                                break;
                            case "PRICONSULTIONBACK":
                                return "产品问题咨询";
                                break;
                            default:
                                return val;
                                break;
                        }
                    },
                },
                {
					label: '话术标题',
					prop: 'msg_title',
					width: 100,
				},
                {
                    label: "回复内容",
                    prop: "msg_content",
                    width:200
                },
                {
                    label: "生效状态",
                    prop: "if_enable",
                    formatter(val) {
                        switch (val) {
                            case "Y":
                                return "生效";
                                break;
                            case "N":
                                return "失效";
                                break;
                            default:
                                return val;
                                break;
                        }
                    },
                    redirectClick(row) {
                        console.log(row);
                        this.modify=true
                        let params = {};
                        params.msg_id = row.msg_id;
                        self.popover(params)
                    },
                },
                {
                    label: "创建人",
                    prop: "creator",
                },
                {
                    label: "创建时间",
                    prop: "create_time",
                    format: "dataFormat1",
                },
                {
                    label: "更新人",
                    prop: "last_modifier",
                },
                {
                    label: "更新时间",
                    prop: "last_modify_time",
                    format: "dataFormat1",
                },
            ],
            modify:false
        };
    },
    methods: {
        presearch(list, resolve) {
            this.search.where = list;
            this.searching(resolve);
        },
        searching(resolve) {
            let self = this;
            this.btns[0].loading = true;
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/order/qualityfeedbackmsg/queryMsgList",
                this.search,
                (res) => {
                    if (res.body.result) {
                        self.count = res.body.content.count;
                        if (res.body.content.list) {
                            self.list = self.handle(res.body.content.list);
                        }
                        self.$message.success(res.body.msg);
                    } else {
                        self.list = [];
                        self.count = 0;
                        self.$message.error(res.body.msg);
                    }
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                },
                (err) => {
                    self.$message.error(err);
                    "function" === typeof resolve ? resolve() : this;
                    self.btns[0].loading = false;
                }
            );
        },
        handle(data){
            data.forEach((element,i) => {
                element._index=i+1
            });
            return data
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log(this.multipleSelection);
        },
        // 新增编辑弹框
        popover(row) {
			let self = this;
			this.$root.eventHandle.$emit('alert', {
				title: self.modify?'新增':"修改",
				params: {
                   msg_id:row?row.msg_id:null, 
					callback(){
						self.searching();
					}
				}, 
				style:'width:600px;height:300px',
				component: () => import('@components/after_sales/qualityFeedbackReplyModify.vue')
			})
		},
        //生效or失效
        changeStatus(status) {
            let self = this;
           
        },
        //点击行
        handleCellClick(row) {
            console.log("行的回调函数：", row);
            this.currentCellSelect = row;
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page_size = size;
            this.searching();
        },
        pageChange(page_no) {
            // 页数改变
            this.search.page_no = page_no;
            this.searching();
        },
        refresh() {
            this.searching();
        },
    },
    mounted() {
        this.searching();
    },
    destroyed() {},
};
</script>
<style lang="css" scoped>
</style>