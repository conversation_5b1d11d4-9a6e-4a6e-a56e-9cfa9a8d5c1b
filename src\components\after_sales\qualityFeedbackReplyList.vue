<!-- 品质反馈详情快速回复列表展示 -->
<template>
  <div>
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button
          type="primary"
          size="mini"
          @click="save()"
          :loading="isLoading"
          :disabled="isLoading"
          >确定</el-button
        >
        <el-button size="mini" @click="preSave()">取消</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="40" style="height:200px;overflow:auto">
      <el-col>
        <xpt-list
          :showHead="false"
          :data="list"
          :colData="colData"
          :selection="selection"
          @radio-change="handleRadioChange"
          ref="list"
        >
        </xpt-list>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from "@common/Fn.js";
export default {
  props: ["params"],
  data() {
    var _this = this;
    var self = this;
    return {
      submitData: {
      },
      selection: "radio",
      isLoading: false,
      list: [], //快速回复列表
      questionData: {
        question_text: "",
        question_list: "",
      },
      colData: [
        {
          label: "话术标题",
          prop: "msg_title",
          width: 100,
        },
        {
          label: "回复内容",
          prop: "msg_content",
        },
      ],
    };
  },
  methods: {
    preSave() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    save() {
      if (Object.keys(this.submitData).length===0) {
        this.$message.warning("请选择一项进行快速回复！");
        return;
      }
      this.questionData.question_text = this.list.find(
        (item) => item.msg_id === this.submitData.msg_id
      ).msg_content;
      this.preSave();
      this.params.callback && this.params.callback(this.questionData);
    },
    getList() {
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/order/qualityfeedbackmsg/list",
        {
          msg_type: this.params.msg_type,
          if_enable: "Y",
          user_type: this.params.user_type || ''
        },
        (response) => {
          if (response.body.result) {
            this.list = response.body.content;
          } else {
            this.$message.error(response.body.msg);
          }
          this.isLoading = false;
        },
        (e) => {
          this.isLoading = false;
          this.$message.error(e);
        }
      );
    },
    handleRadioChange(val) {
      this.submitData=val
    },
  },
  mounted: function () {
    var self = this;
    self.getList();
    self.params.__close = self.closeTab;
  },
};
</script>
