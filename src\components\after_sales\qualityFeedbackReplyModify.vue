<!-- 新增/修改品质反馈快速回复设置 -->
<template>
  <div>
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button
          type="primary"
          size="mini"
          @click="preSave('submitData')"
          :loading="isLoading"
          :disabled="isLoading"
          >确定</el-button
        >
        <el-button size="mini" @click="cancel">取消</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-form
        label-position="right"
        class="mgt10"
        label-width="100px"
        :model="submitData"
        :rules="rules"
        ref="submitData"
      >
        <el-col :span="18">
          <el-form-item label="生效状态" prop="if_enable">
            <el-radio-group v-model="submitData.if_enable">
              <el-radio label="Y">生效</el-radio>
              <el-radio label="N">失效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="应用单据类型" prop="user_type">
            <el-select
              v-model="submitData.user_type"
              size="mini"
              style="width: 150px"
            >
              <el-option
                v-for="(value, key) in applicationTypeOption"
                :label="value.name"
                :value="value.type"
                :key="key"
              ></el-option>
            </el-select>
            <el-tooltip
              v-if="rules.user_type[0].isShow"
              class="item"
              effect="dark"
              :content="rules.user_type[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="话术类型" prop="msg_type">
            <el-select
              v-model="submitData.msg_type"
              size="mini"
              style="width: 150px"
            >
              <el-option
                v-for="(value, key) in types"
                :label="value.name"
                :value="value.type"
                :key="key"
              ></el-option>
            </el-select>
            <el-tooltip
              v-if="rules.msg_type[0].isShow"
              class="item"
              effect="dark"
              :content="rules.msg_type[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
         <el-col :span="18">
          <el-form-item label="话术标题" prop="msg_title">
             <el-input
              size='mini'
              :rows="2"
              placeholder="请输入内容"
              v-model="submitData.msg_title"
            >
            </el-input>
            <el-tooltip
              v-if="rules.msg_title[0].isShow"
              class="item"
              effect="dark"
              :content="rules.msg_title[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="回复内容" prop="msg_content">
            <el-input
              type="textarea"
              :rows="2"
              :maxlength="500"
              placeholder="请输入内容"
              v-model="submitData.msg_content"
            >
            </el-input>
            <el-tooltip
              v-if="rules.msg_content[0].isShow"
              class="item"
              effect="dark"
              :content="rules.msg_content[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from "@common/Fn.js";
let types = [
  { type: "BATCH", name: "批量类" },
  { type: "COMMON", name: "通用类" },
];
export default {
  props: ["params"],
  data() {
    var self = this;
    return {
      submitData: {
        if_enable: "Y", //是否生效
        msg_content: "", //回复内容
        msg_title:"",//话术标题
        msg_type: "", //话术类型
        user_type: '',
      },
      rules: {
        if_enable: validate.isNotBlank({
          required: true,
          self: self,
          msg: "",
          trigger: "blur",
        }),
        msg_content: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请输入回复内容",
          trigger: "blur",
        }),
        msg_title: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请输入话术标题",
          trigger: "blur",
        }),
        msg_type: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请选择话术类型",
          trigger: "change",
        }),
        user_type: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请选择应用单据类型",
          trigger: "change",
        }),
      },
      initialData: {},
      isLoading: false,
      types: types,
      applicationTypeOption: [
        { type: "FEEDBACK", name: "品质反馈单" },
        { type: "COUPLEBACK", name: "客户售后反馈单" },
        { type: "PRICONSULTIONBACK", name: "产品问题咨询" },
      ]
    };
  },
  methods: {
    preSave(formName) {
      var self = this;
      self.$refs[formName].validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        self.save();
      });
    },
    save(callback) {
      var self = this,
        params = {};
      var url = "/afterSale-web/api/aftersale/order/qualityfeedbackmsg/save";
      let saveData = JSON.parse(JSON.stringify(this.submitData));
      // 如果是修改保存时增加id信息
      if (self.params.msg_id) {
        saveData.msg_id = self.params.msg_id;
      }
      this.ajax.postStream(
        url,
        saveData,
        (response) => {
          if (response.body.result) {
            this.$message({ message: "操作成功", type: "success" });
            this.params.callback();
            this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
          } else {
            this.$message.error(response.body.msg);
          }
          this.isLoading = false;
        },
        (e) => {
          this.isLoading = false;
          this.$message.error(e);
        }
      );
    },
    cancel() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    // 修改时根据id查信息
    detail() {
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/order/qualityfeedbackmsg/detail",
        { msg_id: this.params.msg_id },
        (response) => {
          if (response.body.result) {
            Object.keys(this.submitData).forEach((key) => {
              this.submitData[key] = response.body.content[key];
            });
          } else {
            this.$message.error(response.body.msg);
          }
          this.isLoading = false;
        },
        (e) => {
          this.isLoading = false;
          this.$message.error(e);
        }
      );
    },
  },
  mounted: function () {
    var self = this;
    if (self.params.msg_id) {
      self.detail();
    }
    self.params.__close = self.closeTab;
  },
};
</script>