<!-- 驳回备注 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type="primary" size="mini" @click='addFun' :disabled="!orderId || cannotAdd || !params.isEdit">新增行</el-button>
				<el-button type="danger" size="mini" @click='delFun' :disabled="!selectArr.length || !params.isEdit">删除行</el-button>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom">
			<el-table border :data='list' tooltip-effect="dark" width='100%' style="width: 100%;" id='merge_goodList'
				@selection-change="_selectArr => selectArr = _selectArr"
			>
				<el-table-column type="selection"  width="50" :selectable="selectableFun"></el-table-column>
				<el-table-column label="驳回人" prop="rejector_name" width="120"></el-table-column>
				<el-table-column label="驳回日期" width="200">
					<template slot-scope="scope">
						<span>{{scope.row.reject_time | dataFormat1}}</span>
					</template>
				</el-table-column>
				<el-table-column label="驳回备注">
					<template slot-scope="scope">
						<el-input size="mini" :maxlength="255" v-model="scope.row.reject_remark" v-if="scope.row.ifEdit" style="width:100%"></el-input>
						<span v-else style="width:100%">{{scope.row.reject_remark}}</span>
					</template>
				</el-table-column>
				<el-table-column label="业务备注">
					<template slot-scope="scope">
						<el-input size="mini" v-model="businessRemark" readonly v-if="scope.row.ifEdit" style="width:100%"></el-input>
						<span v-else style="width:100%">{{scope.row.biz_note}}</span>
					</template>
				</el-table-column>
			</el-table>
		</el-row>
	</div>
</template>
<script>
	import Fn  from '@common/Fn.js'
	export default {
		props: ['params'],
		data() {
			return {
				orderId: '',
				ifLock:false,
				ifShowBusiness:false,
				list: [],
				cannotAdd: false,//只能新增一行
				selectArr: [],
				remark: null,
				businessRemark: null
			}
		},
		methods: {
			addFun() {
				let obj = {
					ifEdit: true,
					key: new Date().getTime(),
					rejector_name: Fn.getUserInfo('fullName'),
					reject_time: new Date().getTime(),
					reject_remark: this.remark,
				};
				// if(this.ifShowBusiness){
				// 	obj.biz_note = this.businessRemark;
				// }
				this.cannotAdd = true
				this.list.push(obj);
			},
			delFun() {
				this.list = this.list.filter((v) => {
					var bool = true;
					this.selectArr.forEach((v1) => {
						if(v.key === v1.key) {
							bool = false;
						}
					})
					return bool;
				})
				this.cannotAdd = false
			},
			saveFunc (e, cb){
				var $btn = e.currentTarget
				,	postData = this.list.filter(obj => obj.ifEdit).map(obj => {
					return {
						after_order_id: this.orderId,
						reject_remark: obj.reject_remark,
						biz_note: this.businessRemark,
					}
				})

				if(!postData.length){
					this.$message.error('请新增一条驳回信息')
					return
				}

				$btn.disabled = true
				this.ajax.postStream('/afterSale-web/api/aftersale/order/rejectRecord/save?permissionCode=AFTERSALE_NEW_REJECT', postData, res => {
					if(res.body.result){
						this.$message.success('保存成功')
						cb && cb()
						this.init(this.orderId, () => {
							$btn.disabled = false
						})
					}else {
						this.$message.error(res.body.msg)
						$btn.disabled = false
					}
				}, () => {
					$btn.disabled = false
				})
			},
			selectableFun(row,index) {
				if(row.ifEdit) {
					return true;
				}else {
					return false;
				}
			},
			init(_orderId, cb) {
				if(_orderId){
					// this.ifLock = this.params.ifLock;
					this.orderId = _orderId
					this.businessRemark = this.params.businessRemark
					// this.list = this.params.listAfterRejectVO || [];

					this.ajax.postStream('/afterSale-web/api/aftersale/order/rejectRecord/list', { id: _orderId }, res => {
						this.list = res.body.content.list
						this.cannotAdd = false
						cb && cb()
					}, () => {
						cb && cb()
					})
				}else {
					this.orderId = ''
					this.list = []
				}
				//如果有业务备注就添加传该参数为true
				if(this.params.ifShowBusiness) {
					this.ifShowBusiness = this.params.ifShowBusiness;
				}
			},
		},
		watch: {
			// 'params.ifLock':function(val){
			// 	this.ifLock = val;
			// },
			// 'params.remark':function(val){
			// 	this.remark = val;
			// },
			'params.businessRemark':function(val){
				this.businessRemark = val
			},
			// 'params.ifSave':function(){
			// 	var list =[];
			// 	this.list.forEach((v) => {
			// 		var obj = Object.assign({},v)
			// 		if(obj.key){
			// 			delete obj.ifEdit;
			// 			delete obj.key;
			// 		}
			// 		list.push(obj);

			// 	});
			// 	list = list.filter(v => {
			// 		return (v.reject_remark || v.biz_note);
			// 	})
			// 	this.$emit("rejectRemark", list);
			// },
			// 'data':{
			//         handler: function (list) {this.list = list || []},
			//         deep: true
			//     },
			// 'params.listAfterRejectVO':{
			//         handler: function (list) {if(this.params.listAfterRejectVO) this.list = list || []},
			//         deep: true
			//     }

		},
		destroyed() {
		}

	}
</script>
