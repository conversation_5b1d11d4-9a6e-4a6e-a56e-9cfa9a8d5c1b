<!--售后反馈详情弹框-->
<template>
    <div style="height: 100%">
        
        <div class="xpt-flex">
            <div class="xpt-top">
                <el-button type="info" size="mini" @click="questionSure"
                    >确定</el-button
                >
            </div>
            <div class="xpt-flex__bottom">
                <el-form :model="info"  ref='form' :rules='rules'>
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="6">
                            
                        </el-col>
                    </el-row>
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="18">
                            <el-form-item
                                v-if="params.type == 'representative'"
                                label="是否需要补充资料："
                                label-width="130px"
                                prop="ifNeedPlug"

                            >
                                <el-radio-group v-model="info.ifNeedPlug" size="medium">
                                <el-radio border label="NEED">需要补充资料</el-radio>
                                <el-radio border label="NONEED">无需补充资料</el-radio>
                            </el-radio-group>
                            <el-tooltip :content="rules.ifNeedPlug[0].message" class="item" effect="dark" placement="right-start"
                                            popper-class='xpt-form__error' v-if='rules.ifNeedPlug[0].isShow'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </el-form-item>
                            <el-form-item
                                v-if="params.type == 'processed'"
                                label="解决方案："
                                label-width="110px"
                                prop="totalSolutions"
                            >
                                <el-checkbox-group v-model="info.totalSolutions">
                                    <el-checkbox  v-for="item in solutionsList" :key="item.code" :label="item.code">{{item.name}}</el-checkbox>
                                </el-checkbox-group>
                                <el-tooltip :content="rules.totalSolutions[0].message" class="item" effect="dark" placement="right-start"
                                            popper-class='xpt-form__error' v-if='rules.totalSolutions[0].isShow'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </el-form-item>
                            <el-form-item
                                label="快捷回复话术类型："
                                prop="user_type"
                                label-width="110px"
                            >
                                <el-select size='mini' 	v-model="user_type" @change="changeQuickReplyType">
                                    <el-option v-for="item in quickReplyTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="快捷回复话术："
                                prop="quickReply"
                                label-width="110px"
                            >
                                <el-select size='mini' 	v-model="quickReply"  @change="changeQuickReply"  style="width:300px">
                                    <el-option  style="width:300px" v-for="item in quickReplyOption" :key="item.msg_id" :label="`【${item.msg_title}】${item.msg_content}`" :value="`${item.msg_content}`"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="文本消息："
                                label-width="110px"
                                prop="content"
                            >
                                <el-input
                                    type="textarea"
                                    placeholder="请输入内容"
                                    v-model="info.content"
                                    :autosize="{ minRows: 5, maxRows: 6 }"
                                    :maxlength="100"
                                    :show-word-limit="true"
                                >
                                </el-input>
                                <p class="red_text">！！！请注意，回复内容客户可见，请礼貌用语</p>
                                <el-tooltip :content="rules.content[0].message" class="item" effect="dark" placement="right-start"
                                            popper-class='xpt-form__error' v-if='rules.content[0].isShow'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
       
    </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from "@common/Fn.js";
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            rules:{
				
				content:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
                ifNeedPlug:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
                totalSolutions:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
            },
            solutionsList:[
                {
                    code:'SUPPLYGOODS',
                    name:'补货'
                },{
                    code:'DISCOUNTS',
                    name:'折让'
                },{
                    code:'REPAIR',
                    name:'维修'
                },{
                    code:'RETURNS',
                    name:'退货'
                },{
                    code:'CHANGES',
                    name:'换货'
                },{
                    code:'GIFT',
                    name:'赠品'
                },{
                    code:'OTHER',
                    name:'其他'
                },
            ],
            info:{
                
                content:'',
                ifNeedPlug:'NONEED',
                totalSolutions:[],
            },
            user_type: 'COMMON',
            quickReply: '',
            quickReplyTypeOption: [
                {
                    value: 'COMMON',
                    label: '通用类'
                }, {
                    value: 'BATCH',
                    label: '批量类'
                }
            ],
            quickReplyOption: []
        };
    },
    created() {
        let self = this;
        if(self.params.type !== 'representative'){
            delete self.info.ifNeedPlug;
        }
        if(self.params.type !== 'processed'){
          delete  self.info.totalSolutions ;
        }
        this.getQualityFeedbackReplyList()
    },
    mounted() {
        if (this.params.business_division == 'DZ') {
            this.solutionsList = this.solutionsList.filter(item => /^(补货|其他)$/.test(item.name))
        }
    },
    computed: {},
    methods: {
        getQualityFeedbackReplyList() {
            let self = this
            let params = {
                msg_type: this.user_type,
                if_enable: "Y",
                user_type: "COUPLEBACK"
            }
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/order/qualityfeedbackmsg/list",
                params,
                (res) => {
                    if (res.body.result) {
                        if (res.body.content) {
                            let list = res.body.content
                            self.quickReplyOption = list
                        }
                    } else {
                        self.quickReplyOption = []
                    }
                },
                (err) => {
                    self.quickReplyOption = []
                    self.$message.error(err);
                }
            );
        },
       questionSure(){
           let self = this;
           let bool ;
           this.$refs.form.validate((valid) => {
				bool = valid;
			});
            
            if(!bool){
                return;
            }
            let contentLength = self.info.content.length;
            if(contentLength>200){
                self.$message.error('请输入200字以内');            
                return false;
            }
            new Promise((resolve,reject)=>{
             self.params.callback(this.info,resolve); 

            }).then(res=>{
	    		self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
            })  

       },
        changeQuickReplyType () {
            this.getQualityFeedbackReplyList()
            this.quickReply = ''
            this.info.content = ''
        },
        changeQuickReply (item) {
            this.info.content = item
        }
    },
};
</script>
<style scope>
.red_text{
    color: red;
    font-weight: bold;
}
.el-form {
    white-space: nowrap;
}
.mgt15 {
    margin-top: 15px;
}
.seeFileList {
    position: absolute;
    bottom: 0;
    left: 10px;
    right: 10px;
    top: 0px;
    width: auto;
    min-width: auto;
    padding: 10px 0px;
}
.el-table .el-table__body-wrapper td .cell {
    height: auto;
}
.el-table__empty-block {
    width: auto !important;
}
.showImg {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #dbdbdb;
    margin: 5px 0;
    line-height:100%;
}
#xpt-image .xpt-image__body{
    width:100%;
    height:100%;
}
.select-intervener{
    width:180px !important;
}
</style>