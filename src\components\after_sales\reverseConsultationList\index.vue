<!-- 逆向咨询单列表 -->
<template>
	<count-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:searchPage='search.page_name'
		:pageTotal='count'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
		@search-click='searchClick'
		:dynamic='true'
    selection=""
		countUrl="/afterSale-web/api/aftersale/reverseCon/count"
    :showCount ='showCount'
		ref="xptList"
    :taggelClassName="taggelClassName"
	>
    <el-button slot='btns' type="primary" size="mini" :loading="exportLoading" @click="onExport">导出</el-button>
    <el-button slot='btns' type="primary" size="mini" @click="exportResultPopup">导出结果</el-button>

    <span style="color:red; margin-left: 8px;" slot='btns'>咨询单超过48小时没有任何更新，会自动关闭，请注意处理时效！</span>

  </count-list>
</template>
<script>
import Fn from '@common/Fn.js'
import countList from '@components/common/list-count'
export default {
  props:['params'],
	components: {
    countList,
  },
	data() {
    let self = this;
		return {
      exportLoading: false,
			showCount:false,
			isShowSatisfy:false,
			isShowDisSatisfy:false,
			list: [],
			btns: [],
			cols: [{
				label: '咨询单号',
				prop: 'consultation_no',
				// redirectClick: d => self.openConsultation(d.id),
				width: 160
			}, {
				label: '合并单号',
				prop: 'merge_trade_no',
				width: 180,
        redirectClick(row) {
          self.viewMergedOrder(row.merge_trade_id, row.merge_trade_no)
        },
			}, {
				label: '批次单号',
				prop: 'batch_trade_no',
				width: 200,
			}, {
				label: '买家信息',
				prop: 'buyer_name',
        width: 140,
        // redirectClick: d => self.showBuyerInfo(d),
			}, {
				label: '咨询单状态',
				prop: 'con_status',
				formatter: prop => ({
					NOREPLY: '未回复',
					FIRSTREPLY: '首次回复',
					SECONDREPLY: '已回复',
          CLOSE: '已关闭',
				}[prop] || prop),
			}, {
				label: '咨询内容',
				prop: 'con_content',
			}, {
        label: '咨询附件',
        prop: 'con_file',
        buttons: [
          {
            type: 'primary',
            txt: '查看附件',
            isShow: (row) => {
              return true
            },
            click: (row) => self.showAttachmentFile(row, 'FOURPL'),
          }
        ]
      },{
				label: '咨询时间',
				prop: 'con_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '咨询处理人',
				prop: 'con_processing_personnel_name',
				width: 100
			},{
				label: '咨询处理人分组',
				prop: 'conProcessingPersonnelGroupName',
				width: 100
			},{
				label: '回复内容',
				prop: 'first_con_cotent',
				width: 200,
        html: (h, row) => {
          return `<div>
                    <p>首次：${row.first_con_cotent ? row.first_con_cotent : '--'}</p>
                    <p>二次：${row.second_con_cotent ? row.second_con_cotent : '--'}</p>
                  </div>`
        }
      }, {
				label: '回复时间',
				prop: 'first_con_time',
				format: 'dataFormat1',
				width: 190,
        html: (h, row) => {
          return `<div>
                    <p>首次：${row.first_con_time ? self.formatTime(row.first_con_time) : '--'}</p>
                    <p>二次：${row.second_con_time ? self.formatTime(row.second_con_time) : '--'}</p>
                  </div>`
        }
			},{
				label: '回复附件',
				prop: 'replay_file',
				width: 100,
        buttons: [
          {
            type: 'primary',
            txt: '查看附件',
            isShow: (row) => {
              return true
            },
            click: (row) => self.showAttachmentFile(row, 'NBO'),
          }
        ]
			},{
				label: '售后单号',
				prop: 'afterOrderNo',
				width: 100,
        redirectClick: d => self.viewAfterSale(d.afterOrderId),
			},{
				label: '咨询人',
				prop: 'con_user',
				width: 100
			},{
				label: '咨询类型',
				prop: 'con_first_class',
				width: 150,
        html: (h, row) => {
          return `<div>${row.con_first_class ? self.reverseFirstClassObj[row.con_first_class] ? self.reverseFirstClassObj[row.con_first_class] : '--' : '--'}/${row.con_second_class ? self.reverseSecondClassObj[row.con_second_class] ? self.reverseSecondClassObj[row.con_second_class] : '--': '--'}</div>`
        }
			},
      {
				label: '客户业务员/分组',
				prop: 'staff_name',
				width: 150,
        html: (h, row) => {
          return `<div>${row.staff_name ? row.staff_name : '--'}/${row.staff_group_name ? row.staff_group_name : '--'}</div>`
        }
			},
      {
				label: '中转承运商',
				prop: 'transit_carrier_name',
				width: 100
			},
      {
				label: '三包服务商',
				prop: 'three_supply_name',
				width: 100
			},
      {
        label: '林氏自营三包',
        prop: 'is_linsy_self_three_guarantees',
        width: 95,
        format:'yesOrNo'
      },
      {
        label: '操作',
        prop: 'operate_line',
        width: 200,
        fixed: 'right',
        buttons: [
          {
            type: 'primary',
            txt: '回复',
            isShow: (row) => {
              return ['NOREPLY', 'FIRSTREPLY'].includes(row.con_status) || false
            },
            style: 'margin-right: 10px',
            click: (row) => self.replyConsultation(row),
          }, {
            type: 'primary',
            txt: '新增售后单',
            isShow: (row) => {
              return !row.afterOrderId || false
            },
            style: 'margin-right: 10px',
            click: (row) => self.addNewAfterSale(row),
          }, {
            type: 'primary',
            txt: '日志',
            isShow: (row) => {
              return true
            },
            click: (row) => self.getLog(row),
          }
        ]
      }
      ],
			search: {
				page_no:1,
				page_size: this.pageSize,
				page_name: 'aftersale_reverse_consultation',
				where: []
			},
			count: 0,
			selectData: '',
      buyerInfoLoading: false,
      logLoading: false,
      replyConsultationLoading: false,
      attachmentFileLoading: false,
      reverseFirstClassObj: __AUX.get('REVERSE_CON_FIRST_TYPE').reduce((a, b) => {
        a[b.code] = b.name
        return a
      }, {}),
      reverseSecondClassObj: __AUX.get('REVERSE_CON_SECOND_TYPE').reduce((a, b) => {
        a[b.code] = b.name
        return a
      }, {}),
		}
	},
	methods: {
    formatTime (date) {
      return Fn.dateFormat(date, 'yyyy-MM-dd hh:mm:ss')
    },
    taggelClassName (row){
      return this.$style['row-height']
		},
    viewMergedOrder(merge_trade_id, merge_trade_no){
			var params = {};
			params.merge_trade_id = merge_trade_id;
			params.mergeTradeIdList = this.list
			params.ifOrder = true
			// 呼叫按钮来源
			params.callBtnfromType = 'sys_order'
			this.$root.eventHandle.$emit('creatTab',{
				name:"合并订单详情",
				params:params,
				component: () => import('@components/order/merge.vue')
			});
		},
    // 收件人信息
    showBuyerInfo(row) {
      if (this.buyerInfoLoading) return
      let self = this
      this.buyerInfoLoading = true
      this.$root.eventHandle.$emit('alert', {
        params: {
          id: row.id,
          _close: () => {
            self.buyerInfoLoading = false
          },
          callback: data => {
            self.buyerInfoLoading = false
          },
        },
        component: () => import('@components/after_sales/reverseConsultationList/showBuyerInfo.vue'),
        style: 'width:800px;height:560px',
        title: '买家昵称'
      })
    },
    // 咨询附件
    showAttachmentFile(row, fileType) {
      let self = this
      if (this.attachmentFileLoading) return
      this.attachmentFileLoading = true
      let params = {
        id: row.id,
        fileType: fileType,
        _close: () => {
            self.attachmentFileLoading = false
          }
      };
      this.$root.eventHandle.$emit('alert', {
        params: params,
        component: () => import('@components/after_sales/reverseConsultationList/showAttachmentFile.vue'),
        style: 'width:800px;height:560px',
        title: '附件'
      })
    },
    getLog(row) {
      let self = this
      if (this.logLoading) return
      this.logLoading = true
      let params = {
        id: row.id,
        _close: () => {
            self.logLoading = false
          }
      };
      this.$root.eventHandle.$emit('alert', {
        params: params,
        component: () => import('@components/after_sales/reverseConsultationList/showLog.vue'),
        style: 'width:800px;height:560px',
        title: '咨询单日志'
      })
    },
    // 回复
    replyConsultation(row) {
      let self = this
      if (this.replyConsultationLoading) return
      this.replyConsultationLoading = true
      this.$root.eventHandle.$emit('alert', {
        params: {
          row: row,
          id: row.id,
          callback: data => {
              self.replyConsultationLoading = false
              self.getList()
          },
          _close: () => {
            self.replyConsultationLoading = false
          }
        },
        component: () => import('@components/after_sales/reverseConsultationList/replyConsultation.vue'),
        style: 'width:1000px;height:560px',
        title: '逆向咨询回复'
      })
    },
    addNewAfterSale(row) {
      let params = {
        from: 'reverseConsultation',
        merge_trade_no: row.merge_trade_no,
        merge_trade_id: row.merge_trade_id,
      };
      this.$root.eventHandle.$emit('creatTab', {
        name:"新增售后单",
        params:params,
        component: () => import('@components/after_sales/index')
      });
		},
    viewAfterSale(id) {
      var params = {
          id: id,
          idList: Array.from(new Set/*去重*/(this.list.map(obj => obj.afterOrderId))),//用于售后单详情的上一页/下一页
          code:''//查看售后单类型
        };

        if(document.querySelector('[data-symbol="after_order_id_' + id + '"]')){
          this.$message.error('该售后单已经打开')
        }else {
          this.$root.eventHandle.$emit('creatTab', {
            name:"售后单详情",
            params:params,
            component: () => import('@components/after_sales/index')
          });
        }
    },
    openConsultation (id){
			this.$root.eventHandle.$emit('creatTab',{
				name: id ? '咨询单详情' : '新增咨询单',
				component: ()=>import('@components/after_sales/consultationNew'),
				params: {
          id,
          orderList: JSON.parse(JSON.stringify(this._getOrderList())),
				},
			})
    },
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			let self = this;
			this.search.where = obj;
			new Promise((res,rej)=>{
					self.getList(resolve,res);
				}).then(()=>{
					if(this.search.page_no != 1){
						self.count = 0;
					}
					self.showCount = false;
				})
			},
		selectionChange(obj) {
			this.selectData = obj;
		},
		getList(resolve,callback) {
			this.ajax.postStream("/afterSale-web/api/aftersale/reverseCon/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					// this.count = res.body.content.count;
					if(!this.showCount){
						let total = this.search.page_no * this.search.page_size
						this.count = res.body.content.list.length == (this.search.page_size+1)? total+1:total;
              			this.list = res.body.content.list.splice(0,res.body.content.list.length == (this.search.page_size+1)?total:res.body.content.list.length);

					}
				} else {
					this.$message.error(res.body.msg)
				}
				callback && callback();
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				callback && callback();
				resolve && resolve()
			});
		},

    _getOrderList() {
			let newSetBillNo = [],	newSetOrder = []
			this.list.forEach(obj => {
				if(newSetBillNo.indexOf(obj.id) === -1){//去掉重复责任分析单号
					newSetBillNo.push(obj.id)
					newSetOrder.push({
							id: obj.id
					})
				}
			})
			return newSetOrder
    },

    onExport() {
      this.exportLoading = true;
      this.ajax.postStream(
        `/reports-web/api/reports/afterSaleExport/exportAftersaleReverseCon`,
        {
          page_name: this.search.page_name,
          where: this.search.where
        },
        (res) => {
          this.exportLoading = false;
          if (res.data.result){
            this.$message.success(res.body.msg)
          } else  {
            this.$message.error(res.body.msg||'操作失败，请稍候再试')
          }
        },
        err => {
          this.exportLoading = false;
          this.$message.error(err)
        }
      );
    },
    // 导出结果弹窗
    exportResultPopup() {
        this.$root.eventHandle.$emit('alert', {
        component: () => import('@components/after_sales_report/export'),
        style: 'width:900px;height:600px',
        title: '报表导出列表',
        params: {
          query: {
            type: 'EXCEL_TYPE_AFTERSALE_REVERSE_CON',
          },
        },
      })
    },
  },
	mounted() {
    this.getList();
	},
};
</script>
<style module>
.row-height :global(.cell) {
	height: auto!important;
}
.unread-tag td:nth-child(2):after {
	content: '';
	position: absolute;
    right: 4px;
    top: 9px;
    border: 4px solid red;
    border-radius: 100%;
}
.second-read-tag{
	background: #ff50e9!important;
}
</style>
