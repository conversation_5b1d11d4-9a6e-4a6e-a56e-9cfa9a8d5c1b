<!-- 逆向回复咨询 -->
<template>
	<div class='xpt-flex'>
    <div class="xpt-top">
      <el-button type='primary' size='mini'  @click='confirm'>回复</el-button>
    </div>
		<div>
			<el-tabs v-model='firstTab'>
				<el-tab-pane label='基础信息' name='base' class='xpt-flex'>
					<xpt-form :data='form' :cols='firstCols' label='100px'></xpt-form>
				</el-tab-pane>
			</el-tabs>
		</div>
		<el-row class='xpt-flex__bottom'>
			<el-tabs v-model="secondTab">
				<el-tab-pane label='咨询内容' name="consultationContent" class='xpt-flex' >
					<xpt-form :data='form' :cols='secondCols' label='100px'>
            <template slot='fileBtn'>
              <el-button type='primary' size='mini' @click='showAttachmentFile'>查看附件</el-button>
            </template>
            <template slot="con_first_class">
              <p class="xpt-text_ellipsis">{{form.con_first_class ? reverseFirstClassObj[form.con_first_class] ? reverseFirstClassObj[form.con_first_class] : '--' : '--'}}/{{form.con_second_class ? reverseSecondClassObj[form.con_second_class] ? reverseSecondClassObj[form.con_second_class] : '--': '--'}}</p>
            </template>
          </xpt-form>
          <el-form :model='contentForm' :rules='rules' ref='contentForm' label-position="right" label-width="110px">
            <el-row>
              <el-col :span="20">
                <el-form-item label='回复内容:' prop='content' style="height: 100px;">
                  <el-input type='textarea' v-model='contentForm.content' :rows='4' :maxlength="500"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="" style="height: 48px;">
                  <xpt-upload-v3
                    uploadBtnText="上传"
                    :uploadSize="50"

                    :dataObj="uploadData"
                    :disabled="ifClickUpload"
                    :ifMultiple="true"
                    :showSuccessMsg="false"
                    @uploadSuccess="uploadCallback"
                    btnType="success"
                    style="display: inline-block; margin: 0px 10px"
                  >
                  </xpt-upload-v3>
                  <p>支持文件格式：.rar .zip .doc .docx .pdf .JPG .jpeg .png，单个文件不能超过50M</p>
                </el-form-item>
                <el-form-item label="" style="height: 100px; overflow: scroll;margin-top: 10px">
                  <div v-for="item in fileList" :key="item.path">
                    <span>{{item.name}}</span> <el-button type="text" @click="delFile(item)">删除</el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
				</el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
export default {
	props: ['params'],
	data() {
    var self = this
		return {
      secondTab: 'consultationContent',
			firstTab: 'base',
			form: {},
			firstCols: [
				[
					{
						label: '批次单号:',
						key: 'batch_trade_no'
					}, {
						label: '合并单号:',
						key: 'merge_trade_no'
					}, {
						label: '提货点:',
						key: 'del_point_name'
					},
          {
            label: '林氏自营三包:',
            key: 'is_linsy_self_three_guarantees',
            format:'yesOrNo'
          },
				], [
					{
						label: '支付时间:',
						key: 'beforCreated',
						format: 'dataFormat1'
					}, {
						label: '批次发货时间:',
						key: 'zd_delivery_time',
						format: 'dataFormat1'
					}, {
						label: '买家昵称:',
						key: 'buyer_name',
					},
				], [
					{
						label: '收货人:',
						key: 'receiver_name',
					}, {
						label: '收货地址:',
						key: 'receiver_address',
					},
          {
            label: '三包服务商:',
            key: 'three_supply_name',
          },
				]
			],
      secondCols: [
				[
					{
						label: '咨询单状态:',
						key: 'con_status',
            formatter: prop => ({
							NOREPLY: '未回复',
              FIRSTREPLY: '首次回复',
              SECONDREPLY: '已回复',
              CLOSE: '已关闭',
						}[prop] || prop),
					}, {
						label: '咨询内容:',
						key: 'con_content'
					}
				], [
					{
						label: '咨询分类:',
						key: 'con_first_class',
						slot: 'con_first_class',
					}, {
						label: '',
						key: 'discount_condition_type',
            slot: 'fileBtn'
					},
				], [
          {
						label: '咨询时间:',
						key: 'con_time',
						format: 'dataFormat1'
					}, {
						label: '首次回复:',
						key: 'first_con_cotent',
					},
				]
			],
      contentForm: {
        content: ''
      },
      rules: {
        content: [
          { required: true, message: '请输入回复内容', trigger: 'blur' }
        ]
      },
      ifClickUpload: false,
      uploadData: {},
      alerts: [],
      reverseFirstClassObj: __AUX.get('REVERSE_CON_FIRST_TYPE').reduce((a, b) => {
        a[b.code] = b.name
        return a
      }, {}),
      reverseSecondClassObj: __AUX.get('REVERSE_CON_SECOND_TYPE').reduce((a, b) => {
        a[b.code] = b.name
        return a
      }, {}),
      fileList: [],
      attachmentFileLoading: false
		}
	},
  beforeDestroy(){
      if ((new Set(this.alerts).has(this.params.alertId))) {
          this.params._close()
          this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
      }
  },
	methods: {
    uploadCallback(e) {
      console.log('file upload callback', e)
      this.fileList.push(...e)
    },
    delFile(e) {
      this.fileList = this.fileList.filter(item => item.path !== e.path)
    },
    uploadAction (){
      this.uploadData = {
        parent_name: 'AFTER_ORDER',
        parent_no: this.params.row.afterOrderNo,
        child_name : 'aftersale_reverse_consultation',
        child_no : this.params.row.consultation_no,
        content: JSON.parse(JSON.stringify(this.params.row || {})),
      }
      this.ifClickUpload = true
      setTimeout(() => {
        this.ifClickUpload = false
      }, 100)
		},
    showAttachmentFile(row, fileType) {
      let self = this
      if (this.attachmentFileLoading) return
      this.attachmentFileLoading = true
      let params = {
        id: this.params.row.id,
        fileType: 'FOURPL',
        _close: () => {
            self.attachmentFileLoading = false
          }
      };
      this.$root.eventHandle.$emit('alert', {
        params: params,
        component: () => import('@components/after_sales/reverseConsultationList/showAttachmentFile.vue'),
        style: 'width:800px;height:560px',
        title: '附件'
      })
    },
		getData(){
      let self = this;
      if (!this.params.id) return
      this.ajax.postStream('/afterSale-web/api/aftersale/reverseCon/getDetail/', this.params.id,function(res){
        console.log('replyConsultation-getData',res)
        if(res.body.result && res.body.content) {
          self.form = res.body.content
          console.log('this.form', this, res.body.content)
        } else {
          self.$message.error(res.body.msg)
        }
      },function(error){
        self.$message.error(error)
      })
		},
    confirm(){
      this.$refs.contentForm.validate((valid) => {
        if(!valid) return
        let postData = {
          id: this.params.id,
          msgContent: this.contentForm.content,
          entityId: this.form.consultationMsgs && this.form.consultationMsgs.length > 0 ? this.form.consultationMsgs[0].entity_id : '',
          replyFiles: this.fileList && this.fileList.length > 0 ? this.fileList.map((item) => {
            return {
              filePath: item.path,
              fileName: item.name,
              fileSize: item.size,
              fileType: item.file_type
            }
          }) : [],
        }
        this.ajax.postStream('/afterSale-web/api/aftersale/reverseCon/reply', postData, (res)=> {
            if (res.body.result) {
                this.$message.success('回复成功')
                this.params.callback()
                this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
            } else {
                this.$message.error(res.body.msg)
            }
        })
    })
		}
	},
	created() {
    this.$parent.alerts.forEach(item => {
        this.alerts.push(item.params.alertId)
    })
		this.getData();
	}
}
</script>
<style lang="stylus">
// .scroll .el-table .xpt-flex__bottom, .scroll .xpt-flex .xpt-flex__bottom
// 	    overflow: auto;
	.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
		overflow: auto;
	.discount_table
		.xpt-flex__bottom
			.xpt-pagation
				position: absolute;
		.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
			overflow: hidden
			padding-bottom: 35px
	.discount_table
		.xpt-flex .el-table__body-wrapper, .el-table .el-table__body-wrapper
			min-height: 100px
			max-height: 200px
			overflow-x: hidden
	.tableGreen{
		background-color: #d8e4bc !important;
	}
	.tableGreen td {
		background-color: inherit !important;
	}
</style>

