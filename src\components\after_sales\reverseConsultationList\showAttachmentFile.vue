<template>
  <div class="download-group">
    <el-table :data="list"  tooltip-effect="dark" width='100%' style="width: 100%" class="pmm_div6" >
        <el-table-column width="25" align='center' type="selection"></el-table-column>
        <el-table-column show-overflow-tooltip prop="name" label="文件名称" >
            <template slot-scope="scope" >
                <span v-if="!scope.row.isPucture">{{scope.row.name}}</span>
                <div  v-else>
                      <a  @click="showPicture(scope.row)" alt="点击可预览哦" href="javascript:;" class="pmm_a5"><img :src="scope.row.path" width="60" /></a>
                </div>
            </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="create_name" label="创建人" width="80"></el-table-column>
        <el-table-column show-overflow-tooltip prop="create_time" label="创建时间" width="130">
            <template slot-scope="scope">{{scope.row.create_time | dataFormat1}}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="size" label="文件大小" width="60">
            <template slot-scope="scope">{{scope.row.size | fileSize}}</template>
        </el-table-column>
        <el-table-column label="图片预览" width="60">
            <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="showPicture(scope.row)" v-if="scope.row.isPucture">
                    预览
                </el-button>
            </template>
        </el-table-column>
        <el-table-column label="文件下载" width="60">
            <template slot-scope="scope">
                <el-button type="primary" size="mini" >
                    <a :href="scope.row.path" target="_blank" :download="scope.row.path">下载</a>
                </el-button>
            </template>
        </el-table-column>
    </el-table>
    <xpt-image
      :images="imagesList"
      :show="ifShow"
      :ifUpload="false"
      :ifClose="false"
      @close="closeFun">
  </xpt-image>
</div>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
  props:['params'],
  data() {
    return {
      ifShow: false,
      imagesList: [],
      list: [],
      alerts: [],
    }
  },
  methods: {
    closeFun() {
        this.ifShow = false;
        //预览支持删除，关闭再获取一次数据
        this.getFileList();
    },
    getFileList() {
      this.ajax.postStream('/afterSale-web/api/aftersale/reverseCon/getFile',{
        id: this.params.id,
        fileType: this.params.fileType
      },res => {
          if(res.body.result) {
            this.list = res.body.content || [];
            this.list.map(v => {
              v.isPucture = ['jpg', 'png', 'JPG', 'PNG', 'jpeg', 'JPEG'].includes(v.file_type);
            })
            console.log(res.body.content, res, '查看附件附件附件', this.list)
          }else{
              this.$message.error(res.body.msg);
          }
      })
    },
    showPicture(row) {
            this.ifShow = true;
            var list = [];
            this.list.forEach(v => {
                if(v.isPucture) {
                    var obj = Object.assign({},v);
                    obj.src =  obj.path;
                    obj.size =  obj.size;
                    obj.name =  obj.name;
                    obj.date =  Fn.dateFormat(obj.create_time,'yyyy-MM-dd hh:mm:ss');
                    obj.create_name =  obj.create_name;
                    obj.isPucture = true;
                    v.isPucture = true;
                    //确定要预览那张图片
                    if(obj.id === row.id) {
                        obj.active = true;
                    }else {
                        obj.active = false;
                    }

                    list.push(obj);
                }
            })
            console.log(list, '查看图片')
            this.imagesList = list;
        },
  },
  mounted() {
    this.$parent.alerts.forEach(item => {
        this.alerts.push(item.params.alertId)
    })
    this.getFileList()
  },
  beforeDestroy(){
    if ((new Set(this.alerts).has(this.params.alertId))) {
        this.params._close()
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    }
  },
}
</script>
