<!-- 优惠信息查看，用于合并订单添加优惠列表页面查看优惠详情 -->
<template>
	<div class='xpt-flex'>
		<div>
			<el-tabs v-model='firstTab'>
				<el-tab-pane label='收件人信息' name='first' class='xpt-flex'>
					<xpt-form :data='form' :cols='firstCols' label='100px'>
            <template slot='receiver_phone'>
              <!-- aboutNumber为应用显隐组件的当前主要单据号 -->
              <xpt-eye-switch v-model="form.receiver_phone" :readonly="true" :hideBorder="true" :aboutNumber="form.no"></xpt-eye-switch>
            </template>
            <template slot='province_name'>
              <div>{{form.province_name}} {{form.city_name}} {{form.area_name}}</div>
            </template>
          </xpt-form>
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>
<script>
export default {
	props: ['params'],
	data() {
    var self = this
		return {
			firstTab: 'first',
			form: {},
			firstCols: [
				[
					{
						label: '收件人:',
						key: 'receiver_name'
					}, {
						label: '收件电话:',
						key: 'receiver_phone',
            slot: 'receiver_phone'
					}, {
						label: '省市区:',
						key: 'province_name',
            slot: 'province_name',
					}, {
						label: '街道:',
						key: 'street_name',
					}
				],
			],
      alerts: [],
		}
	},
	methods: {
		getData(){
      let self = this;
      if (!this.params.id) return
      this.ajax.postStream('/afterSale-web/api/aftersale/reverseCon/getDetail/', this.params.id,function(res){
        if(res.body.result && res.body.content) {
          self.form = res.body.content
        } else {
          self.$message.error(res.body.msg)
        }
      },function(error){
        self.$message.error(error)
      })
		},
	},
  beforeDestroy(){
    if ((new Set(this.alerts).has(this.params.alertId))) {
        this.params._close()
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    }
  },
	created() {
    this.$parent.alerts.forEach(item => {
        this.alerts.push(item.params.alertId)
    })
		this.getData();
	}
}
</script>
<style lang="stylus">
// .scroll .el-table .xpt-flex__bottom, .scroll .xpt-flex .xpt-flex__bottom
// 	    overflow: auto;
	.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
		overflow: auto;
	.discount_table
		.xpt-flex__bottom
			.xpt-pagation
				position: absolute;
		.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
			overflow: hidden
			padding-bottom: 35px
	.discount_table
		.xpt-flex .el-table__body-wrapper, .el-table .el-table__body-wrapper
			min-height: 100px
			max-height: 200px
			overflow-x: hidden
	.tableGreen{
		background-color: #d8e4bc !important;
	}
	.tableGreen td {
		background-color: inherit !important;
	}
</style>

