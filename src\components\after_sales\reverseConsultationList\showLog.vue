<!--仓库列表-->
<template>
	<xpt-list
    :data='operateList'
    :colData='operateCol'
    :showHead="false"
    selection=''
  >
  </xpt-list>

</template>
<script>
	export default {//需要类型和相关的id
    props:['params'],
		data(){
      var self = this;
			return {
        btns: [],
				operateCol: [
          {
            label: '操作人',
            prop: 'operator_name',
          }, {
            label: '业务操作',
            prop: 'operate_type',
            formatter: prop => ({
              CREATE: '新增',
              SAVE: '保存',
              LOCK: '锁定',
              UNLOCK: '解锁',
              SUBMIT: '提交',
              RETRACT: '撤回',
              AUDIT: '审核',
              CHANGE: '确认变更',
              REVERSE_AUDIT: '反审核',
              SUBMIT_APPROVE: '提交审批',
              PASS: '审批通过',
              NOT_PASS: '审批不通过',
              TRACK: '执行跟踪',
              RECALL: '撤回仓储',
              CLOSE: '关闭',
              OPEN: '反关闭',
              CANCEL: '已取消',
              //extra
              ADD_REFUND_ITEM: '引入退款明细',
              DELETE_REFUND_ITEM: '删除退款明细',
              ADD_TAOBAO_REFUND: '引入淘宝退款申请',
              DELETE_TAOBAO_REFUND: '删除淘宝退款申请',
              SUBMITPURCHASE: '提交采购',
              PLAN_RETRACT: '方案撤回',
              REJECT: '驳回',
              LOCKER: '锁定',
              REVOCATION: '撤回',
              VERIFY: '审核',
              OPPOSITE_VERIFY: '反审核',
              SUBMIT_EXAMINE: '提交审批',
              PASS_EXAMINE: '审批通过',
              UNPASS_EXAMINE: '审批不通过',
            }[prop] || prop),
          }, {
            label: '操作描述',
            prop: 'description',
          }, {
            label: '操作时间',
            prop: 'operate_time',
            format: 'dataFormat1',
            width: 150,
          }
      ],
				count:0,
        operateList:[],
        alerts: [],
			}
		},
    beforeDestroy(){
      if ((new Set(this.alerts).has(this.params.alertId))) {
          this.params._close()
          this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
      }
    },
		methods:{
      getList(){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId',{
          after_bill_id: this.params.id
        },(d)=>{
          var data = d.body;
          if(!data.result){
            this.$message({
                message:data.msg,
                type:'error'
            })
            return;
          }
          this.operateList = data.content;
					this.count = data.content.length;
        },function(e){
					console.log(e);
        })
      }
		},
    created(){
      this.$parent.alerts.forEach(item => {
          this.alerts.push(item.params.alertId)
      })
			this.getList();
		}
	}
</script>
<style type="text/css" scoped>
	.el-table__body-wrapper{margin-bottom:20px;}
</style>
