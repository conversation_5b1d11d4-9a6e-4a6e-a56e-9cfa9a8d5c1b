<template>
<div class="xpt-flex">
  <div class="warning-tips">合并单需有已发运的商品才可以选单，创建售后单</div>
	<el-row	class='xpt-top'	:gutter='40'>
		<el-button type='warning' size='mini' @click='submit()' :disabled="selectIndex === ''">确认</el-button>
		<el-col class='xpt-align__right' :span='20' style="float:right;">
			<el-select v-model="search.limit_field" size='mini' style="width:150px;">
				<el-option label="买家昵称" value="customer_name"></el-option>
				<el-option label="合并订单号" value="merge_trade_no"></el-option>
				<el-option label="销售订单号" value="sys_trade_no"></el-option>
				<el-option label="平台单号" value="tid"></el-option>
			</el-select>
			<el-select v-model="search.operator" size='mini' style="width:100px;">
				<el-option label="等于" value="="></el-option>
				<el-option label="包含" value="%"></el-option>
			</el-select>
			<el-input placeholder="请输入查询值" size='mini' v-model="search.field_value"></el-input>
			<el-button type="primary" @click="searchFunc" size='mini' :disabled="!search.field_value">查询</el-button>
			<el-button type="primary" @click="() => search = Object.assign({}, defaultSearch)" size='mini'>重置</el-button>
		</el-col>
	</el-row>
	<el-row class="xpt-flex__bottom mgb20" >
		<el-table :data="list" border tooltip-effect="dark" width='100%' style="width: 100%" highlight-current-row @row-dblclick="row => submit(String(list.indexOf(row)))">
			<el-table-column width="55" align='center'>
				<template slot-scope='scope'>
					<el-radio class='xpt-table__radio' :label="scope.$index" v-model="selectIndex" @change.native="handleCurrentChange(scope.$index)"></el-radio>
				</template>
		   	</el-table-column>
		    <el-table-column prop="sys_trade_no" label="销售订单号" width="200" show-overflow-tooltip></el-table-column>
		    <el-table-column prop="tid" label="平台单号" width="200" show-overflow-tooltip></el-table-column>
		    <el-table-column prop="merge_trade_no" label="合并订单号" width="200" show-overflow-tooltip></el-table-column>
		    <el-table-column prop="real_name" label="业务员" show-overflow-tooltip ></el-table-column>
		    <el-table-column prop="customer_name" label="买家昵称" width="150" show-overflow-tooltip></el-table-column>
		    <el-table-column prop="shop_name" label="订单店铺" width="130" show-overflow-tooltip></el-table-column>
		    <el-table-column prop="created" label="拍单时间" width="200" show-overflow-tooltip>
		    	<template slot-scope="scope">
			    	<span>{{scope.row.created | dataFormat('yyyy-MM-dd hh:mm:ss')}}</span>
			    </template>
		    </el-table-column>
		    <el-table-column prop="pay_time" label="支付时间" width="200" show-overflow-tooltip>
		    	<template slot-scope="scope">
			    	<span>{{scope.row.pay_time | dataFormat('yyyy-MM-dd hh:mm:ss')}}</span>
			    </template>
		    </el-table-column>
		    <el-table-column prop="group_name" label="业务员分组" show-overflow-tooltip></el-table-column>
		    <el-table-column prop="bill_type_id" label="单据类型" width="150" show-overflow-tooltip>
		    	<template slot-scope="scope">
			    	<span>{{ { 'B2C': 'B2C商城订单', 'O2O': 'O2O实体店', 'TAOBAO': '淘宝订单', 'PRESENT': '赠品订单', 'PDD': '拼多多','RESALEORDER':'再售销售单' ,'RESHIPMENTORDER':'再发销售单' }[scope.row.bill_type_id] }}</span>
			    </template>
		    </el-table-column>
	  	</el-table>
	</el-row>
	<el-row class='xpt-pagation'>
	  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
		  	:current-page="search.page_no" :page-sizes="[10, 20, 50, 100]" :page-size="search.page_size"
		  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
		</el-pagination>
	</el-row>
</div>
</template>

<script>
export default {
	props: ['params'],
	data (){
		return {
			defaultSearch: {},
			search: {
				field_value: '',//值
				limit_field: 'customer_name',//query
				operator: '=',
				page_no: 1,
			    page_size: 20,
			},
			selectIndex: '',
			pageTotal: 0,
			list: [],
		}
	},
	methods: {
		submit (dblclickIndex){
			if(this.list[dblclickIndex || this.selectIndex].bill_type_id == 'RESALEORDER'||this.list[dblclickIndex || this.selectIndex].bill_type_id == 'RESHIPMENTORDER'){
				this.$message.error('该订单为再发或再售订单，不能建立售后单');
				return false;
			}
      this.params.callback(this.list[dblclickIndex || this.selectIndex])
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
		handleCurrentChange(index){
			this.selectIndex = index
		},
		pageChange (page){
			this.search.page_no = page
			this.searchFunc()
		},
		pageSizeChange (pageSize){
			this.search.page_size = pageSize
			this.searchFunc()
		},
		searchFunc (){
			var url = this.params.isReSaleOrder ? '/afterSale-web/api/aftersale/order/resale/choose/pickmergetradeorder' : '/afterSale-web/api/aftersale/order/choose/pickmergetradeorder'

			this.ajax.postStream(url, this.search, res => {
				if(res.body.result){
					this.list = res.body.content.list
					this.pageTotal = res.body.content.count
					// autoImportByPlatformCode 是否需要直接带入到售后单，true 是
					if(this.params.autoImportByPlatformCode && res.body.content.list && res.body.content.list.length > 0) {
						// params有带入平台单号，查询后自动选择关联合并单，确认（业务确认只会有一个结果，默认选第一个）
						this.selectIndex = 0
						this.submit()
					}
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
	},
	mounted (){
		this.search = {
			...this.search,
			limit_field: this.params.limit_field || this.search.limit_field,
			field_value: this.params.field_value || this.search.field_value,
		}
		this.defaultSearch = this.search
		// autoImportByPlatformCode 是否需要直接带入到售后单，true 是
		if(this.params.autoImportByPlatformCode && this.params.limit_field && this.params.field_value) this.searchFunc()
	}
}
</script>

<style scoped>
.warning-tips {
  position: absolute;
  color: red;
  top: -20px;
  left: 80px;
}
</style>
