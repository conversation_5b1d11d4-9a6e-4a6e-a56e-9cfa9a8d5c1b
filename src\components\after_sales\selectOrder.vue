<!-- 售后选单 -->
<template>
	<xpt-list 
		ref='list' 
		:data='list' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='count' 
		searchPage='scm_sys_trade' 
		@search-click='presearch' 
		@selection-change='handleSelectionChange' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange'
	></xpt-list>
</template>
<script>
import fn from '@common/Fn.js'
export default {
	props:["params"],
	data(){
		var _this = this;
		return {
			search:{
				page_name: "scm_sys_trade",
				where: [],
				field_value: "",//筛选条件：具体搜索值
			    limit_field: "", //筛选条件：合并订单、买家昵称、
			    operator: "",//筛选条件：= > <
				page_size: 20,     //页数
				page_no: 1   //页码
			},
			list:[],
			count:0,
			multipleSelection: [],
			btns: [
				{
					type: 'success',
					txt: '确认',
					// disabled: true,
					click: _this.selectOk
				}
			],
			cols: [
				{
					label: '销售订单号',
					prop: 'sys_trade_no',
					width: 180
				}, {
					label: '合并订单号',
					prop: 'merge_trade_no',
					width: 180
				}, {
					label: '业务员',
					prop: 'real_name',
					width: 140
				}, {
					label: '商品编码',
					prop: 'material_number',
					width: 140
				}, {
					label: '商品名称',
					prop: 'material_name'
				}, {
					label: '规格描述',
					prop: 'material_specification',
					width: 120
				}, {
					label: '订单店铺',
					prop: 'shop_name',
					width: 120
				}, {
					label: '数量',
					prop: 'number',
					width: 120
				}, {
					label: '承诺发货日期',
					prop: 'commit_time',
					width: 140
				}, {
					label: '销售发货日期',
					prop: 'zd_delivery_time',
					width: 140
				}, {
					label: '批次单号',
					prop: 'batch_trade_no',
					width: 180
				}, {
					label: '是否停产',
					prop: 'is_stop',
					width: 140
				}, {
					label: '销售单位',
					prop: 'material_unit',
					width: 140
				}
			]
		}
	},
	methods:{
		// 关闭标签页
		closeTab(){
			this.$root.eventHandle.$emit('removeTab',this.params.tabName);
		},
		handleSelectionChange(val){
			console.log('val',val)
			var _this = this;
			_this.multipleSelection=val;
		},
		sizeChange(size){
			console.log('size',size)
			// 第页数改变
			this.search.page_size = size;
			this.searching();
		},
		pageChange(page_no){
			console.log('page_no',page_no)
			// 页数改变
			// this.pageNow = page_no;
			this.search.page_no = page_no;
			this.searching();
		},
		presearch(list){
			list = list[0]
			console.log('list',list)

			this.search.field_value = list.value
			this.search.limit_field = list.field
			this.search.operator = list.operator

			// this.search.where = list;
			this.searching();
		},
		searching(){
			let _this = this;

			_this.list = [
	        {
	            "sys_trade_no": "LS20170615141035001449",
	            "merge_trade_no": "HB20170615141035001170",
	            "real_name": null,
	            "material_number": null,
	            "customer_name": "m18782206113",
	            "material_name": null,
	            "material_specification": null,
	            "shop_name": "B2C商城",
	            "number": 1,
	            "commit_time": 1497916800000,
	            "zd_delivery_time": 1505443607000,
	            "batch_trade_no": "HB20170615141035001170-9",
	            "material_unit": null,
	            "is_stop": null
	        },
	        {
	            "sys_trade_no": "LS20170615175530001454",
	            "merge_trade_no": "HB20170615141035001170",
	            "real_name": null,
	            "material_number": null,
	            "customer_name": "m18782206113",
	            "material_name": null,
	            "material_specification": null,
	            "shop_name": "B2C商城",
	            "number": 1,
	            "commit_time": 1497657600000,
	            "zd_delivery_time": 1505443607000,
	            "batch_trade_no": "HB20170615141035001170-9",
	            "material_unit": null,
	            "is_stop": null
	        }]
	        _this.count = 2

	        return
			this.ajax.postStream('/after-web/api/aftersale/order/choose', {
			    field_value: this.search.field_value, 
			    // limit_field: this.search.limit_field, 
			    limit_field: '', 
			    operator: this.search.operator, 
			    page: null, 
			    page_name: null, 
			    page_no: this.search.page_no, 
			    page_size: this.search.page_size, 
			    start: 0, 
			    end: 20, 
			    where: null
			}, response => {
				if(response.body.result){
					_this.list = response.body.content.list
					_this.count = response.body.content.count
				}else{
					_this.list = []
					_this.count = 0 
				}
			})
		},
		// 确认按钮事件
		selectOk (){
			if(!this.multipleSelection.length){
				this.$message.error('选单不能为空')
				return 
			}

			this.params.callback(this.multipleSelection)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
	},
	mounted: function(){
		var _this = this;
		_this.searching();
		// 检测新增、编辑数据，刷新
		// _this.$root.eventHandle.$on('refresh_list',function(){
		// 	_this.searching();
		// })
  	},
	destroyed(){
		// this.$root.offEvents('refresh_list');
	}
}
</script>
