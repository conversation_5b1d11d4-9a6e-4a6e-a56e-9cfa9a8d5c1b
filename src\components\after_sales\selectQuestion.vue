<!-- 添加商品 -->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
			<el-col :span="6">
				<el-button type='success' size='mini' @click="submit()" >确认</el-button >
			</el-col>
			<el-col :span="18">
				<span>一级标签问题</span>
				<el-input v-model="search.parentNameLike" size="mini"></el-input>
				<span>二级标签问题</span>
					<el-input v-model="search.nameLike" size="mini"></el-input>
					<span>
						<el-button type='primary' size='mini' @click="searchFunc()" >搜索</el-button >
						<el-button type='primary' size='mini' @click="reset()" >重置</el-button >
					</span>
			</el-col>
			
	</el-row>
	<el-row class="xpt-flex__bottom mgb20" >
		<xpt-list 
		ref='list' 
		:showHead='false'
		:data='list' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='count' 
		selection="radio"
		@row-dblclick="rowDblclick"
		@radio-change='handleCurrentChange' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange'
	>
	</xpt-list>
	</el-row>
	
</div>
</template>

<script>
export default {
	props:["params"],
	data(){
		var _this = this;
		return {
			cols:[{
					label: '一级问题标签',
					prop: 'parentName',
					width: 180
				},{
					label: '二级问题标签',
					prop: 'name',
					width: 180
				},],
			count:0,
			btns:[{
					type: 'success',
					txt: '确认',
					// disabled: true,
					click: _this.submit
				}],
			search:{
				categoryCode:'AFTER_SALES_ISSUE_LABELS_2',
				nameLike:'',
				parentNameLike:'',
				status:1,
				page:{"length":50,"pageNo":1}
			},
			list: [],
			pageTotal: 0,
			radioSelect: '',
		}
	},
	methods:{
		reset(){
			this.search.nameLike = '';
			this.search.parentNameLike = '';
		},
		handleCurrentChange(val){
			this.radioSelect = val
		},
		pageChange (page){
			this.search.page.pageNo = page
			this.searchFunc()
		},
		sizeChange (pageSize){
			this.search.page.length = pageSize
			this.searchFunc()
		},
		rowDblclick(data){
			this.params.callback(data)
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
		searchFunc(){
	     
	        	// 有源
				this.ajax.postStream(
					'/user-web/api/auxiliary/getAuxiliaryDataList',
					this.search,
				response => {
					if(response.body.result){
						this.list = response.body.content.list||[];
					 this.count = response.body.content.count;
					}else{
						this.list = []
						this.count = 0 
					}
				})
	       
		},
		// 确认按钮事件
		submit (dblclickIndex){
			this.params.callback(this.radioSelect)
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
	},
	mounted: function(){
		this.search.merge_trade_id = this.params.id
		this.searchFunc()
  	},
}
</script>
