<!--
* @description:
* @author: bin
* @date: 2025/6/5
-->
<template>
  <div class="xpt-flex">
    <div class="mt-4 border-1">
      <el-button type="primary" @click="onSubmit" size="mini" :loading="loading">提交</el-button>
    </div>
    <div>
      <el-form
        label-position="right"
        label-width="120px"
        :model="info"
        :rules="rules"
        ref="info"
      >
        <el-form-item label="投诉索赔类型" prop="serviceType">
          <el-select v-model="info.serviceType" placeholder="请选择" size="mini" >
            <el-option
              v-for="item in serviceTypeList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="info.remark" type="textarea" size="mini"   :rows="2"></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div class="mt-16">
      <el-tabs>
        <el-tab-pane label="上传附件">
          <div class="mt-4">
            <xpt-upload-v3
              uploadBtnText="上传附件"
              acceptType="usually"
              :dataObj="uploadDataObj"
              @uploadSuccess="uploadSuccess"
              :uploadSize="20"
            ></xpt-upload-v3>
          </div>
          <div class="auto">
            <xpt-list
              :data="pictureList"
              :colData="columns"
              :showHead="false"
              :orderNo="true"
              @selection-change='handleSelectionChange'
              style="margin-top: 10px"
            >
              <template slot='view' slot-scope='scope'>
                <el-button size='mini' type='success' v-model="scope.row.if_resolved"  @click='showImageList(scope.row)'>预览</el-button>

              </template>
              <template slot='del' slot-scope='scope'>
                <el-button size='mini' type='danger' v-model="scope.row.if_resolved"  @click='deleteFile(scope.row)'>删除</el-button>
              </template>
              <template slot='path' slot-scope='scope'>
                <div v-if="isImage(scope.row.path)">
                  <a href="#" class="showImg"
                  ><img
                    :src="scope.row.path"
                    alt="图片"
                    width="60"
                  /></a>
                </div>
                <div v-else>
                  {{ scope.row.name }}
                </div>
              </template>
            </xpt-list>
          </div>

        </el-tab-pane>
      </el-tabs>
      <xpt-image
        :images="imageList"
        :show="ifShowImage"
        :ifUpload="false"
        :ifClose="false"
        @close="closeShowImage"
      >
      </xpt-image>
    </div>

  </div>
</template>
<script>
import Fn from "@common/Fn.js";
export default {
  name: 'submit-onLogistics-modal',
  props:['params'],
  data() {
    return {
      info: {},
      loading: false,
      rules: {
        serviceType: [
          {required: true ,tigger:'change', message: "投诉索赔类型"},
        ],
        remark: [
          {required: true , message: "请输入备注信息", trigger: "blur" }
        ]
      },
      serviceTypeList: [],
      ifShowImage:false,
      imageList: [],
      uploadDataObj: {},
      pictureList: [],
      columns: [
        {
          label: "文件名称",
          slot: "path",
          width: 100
        },{
          label: "文件预览",
          slot: "view",
          width: 100
        },{
          label: "文件删除",
          slot: "del",
          width: 100
        }
      ]

    }
  },
  methods: {
    // 提交
    onSubmit() {

      this.$refs.info.validate((valid) => {
        if(valid){
          this.loading = true;
          const params = {
            ...this.info,
            questionId:this.params.questionId,
            afterOrderId: this.params.afterOrderId
          }
          this.ajax.postStream('/afterSale-web/api/aftersale/plan/claimsRepair/createClaims',params,res=>{
            if(res.body.result) {
              this.$message.success('提交成功');
              console.log(this.params.alertId,'this.params.alertId')
              this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
              this.params?.callBack?.()
              // this.getUploadFile()

            }else{
              this.$message.error(res.body.msg);
            }
            this.loading = false
          })
        }else{
          this.$message.error('请填写必填项');
        }
      })


    },
    // 获取下拉值
    getServiceTypeList(){
      this.serviceTypeList = __AUX.get('serviceType')?.filter(x=>x.parentCode==='COMPLAINTS_AND_CLAIMS'  && x.status)
    },
    uploadSuccess(data) {
      console.log(data)
     this.getUploadFile()
    },
    showImageList(row) {
      this.initImageList(row);
      this.ifShowImage = true;
    },
    deleteFile(val) {
      console.log(val)
      this.ajax.postStream('/file-iweb/api/cloud/file/delete', { list_cloud_file_id: [val.cloud_file_id] },res => {
        if(res.body.result) {
          this.$message.success('删除成功');
          let idx = this.pictureList.findIndex(x=>x.file_id===val.file_id)
          this.pictureList.splice(idx,1)
        }else{
          this.$message.error(res.body.msg);
        }
      })

    },
    handleSelectionChange(selects){
      this.selectedData = selects
    },
    changeOssUrl(url){
      return Fn.changeOssUrl(url);
    },
    isImage(str) {
      let reg = /\.(png|jpg|gif|jpeg|webp)$/i;
      return reg.test(str);
    },
    //初始化预览列表
    initImageList(row) {
      let imglist = [];
      //过滤出只有图片的列表
      let list = this.pictureList.filter((item) =>
        this.isImage(this.changeOssUrl(item.path))
      );
      list.forEach((value) => {
        let obj = Object.assign({}, value);
        obj.src = Fn.changeOssUrl(value.path);
        obj.date = Fn.dateFormat(
          value.date,
          "yyyy-MM-dd hh:mm:ss"
        );
        obj.creatName = value.name;
        obj.isPucture = true;
        //确定要预览那张图片
        if (value.file_id === row.file_id) {
          obj.active = true;
        } else {
          obj.active = false;
        }
        imglist.push(obj);
      });
      console.log(imglist, 'imgList4424555',list)
      this.imageList = imglist;
    },
    closeShowImage() {
      this.ifShowImage = false;
    },
    // 获取附件列表
    getUploadFile(){
      console.log(this.params)
      let url = "/file-iweb/api/cloud/file/list";
      let params = {
        order_no: this.params.afterOrderNo,
        page_no: 1,
        page_size: 10000,
      };
      this.$axios("post", url, params)
        .then((res) => {
          if (res.result) {
           this.pictureList = res.content.list;
          } else {
            self.$message.error(res.msg);
          }
        })
        .catch((err) => {
          self.$message.error(err);
        });
    },
  },
  created() {
  },
  mounted() {
    this.uploadDataObj = {
      parent_name: "AFTER_ORDER",
      parent_no: this.params.afterOrderNo,
      child_name: "QUESTION_GOODS",
      child_no: null,
      content: {},
    }
    this.getServiceTypeList()
    this.getUploadFile()
  }
}
</script>
<style scoped>
.auto{
  height: 300px;
}
.mt-4{
  margin-top: 4px;
}
/deep/ .el-table .el-table__body-wrapper td .cell {
  height: auto;
}
.border-1{
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 4px;
  padding-bottom: 4px;
}
.mt-16{
  margin-top: 16px;
}
</style>
