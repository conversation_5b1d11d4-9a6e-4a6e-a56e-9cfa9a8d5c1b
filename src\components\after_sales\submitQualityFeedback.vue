<!--提交品质反馈弹框-->
<template>
    <div class="xpt-flex">
        <div class="xpt-top">
            <el-button
                type="primary"
                size="mini"
                @click="submitFeedback"
                slot="left"
                :loading="isSubmiting"
                :disabled="canSubmit"
                >提交</el-button
            >
        </div>
        <div class="xpt-flex__bottom">
            <el-form :model="submitData" :rules="rules" ref="submitData">
                <el-tabs v-model="firstTab" style="height: 250px">
                    <el-tab-pane label="反馈资料" name="feedbackInfo">
                        <el-row
                            class="mgt20"
                            type="flex"
                            justify="center"
                            :gutter="40"
                        >
                            <el-col :span="8">
                                <el-form-item
                                    label="发运物料："
                                    prop="material_number"
                                    label-width="80px"
                                >
                                    <el-input
                                        v-model="submitData.material_number"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                    label="物料名称："
                                    prop="material_name"
                                    label-width="80px"
                                >
                                    <el-input
                                        v-model="submitData.material_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                    label="型号规格："
                                    prop="material_spec"
                                    label-width="80px"
                                >
                                    <el-input
                                        v-model="submitData.material_spec"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row
                            class="mgt20"
                            type="flex"
                            justify="left"
                            :gutter="40"
                        >
                          <el-col :span="8">
                                <el-form-item
                                    label="是否绿通："
                                    prop="if_greenway"
                                    label-width="80px"
                                >
                                  <el-select  size='mini'  placeholder="请选择" v-model='submitData.if_greenway' :disabled="false" @change="(val)=>{submitData.greenway_type=''}">
                                    <el-option label="是" value="Y"></el-option>
                                    <el-option label="否" value="N"></el-option>
                                  </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                    label="绿通类型："
                                    prop="greenway_type"
                                    label-width="80px"
                                >
                                  <xpt-select-aux
                                    v-model="submitData.greenway_type"
                                    aux_name='PZFKDLTLX'
                                    :disabled="submitData.if_greenway==='N'"
                                    >
                                  </xpt-select-aux>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row
                            class="mgt20"
                            type="flex"
                            justify="center"
                            :gutter="40"
                        >
                            <el-col :span="24">
                                <el-form-item
                                    label="反馈信息："
                                    prop="information_content"
                                    label-width="80px"
                                    class="nowrap"
                                >
                                    <el-input
                                        type="textarea"
                                        placeholder="请输入内容"
                                        v-model="submitData.information_content"
                                        :autosize="{ minRows: 5, maxRows: 5 }"
                                        :maxlength="255"
                                    >
                                    </el-input>
                                    <el-tooltip
                                        v-if="
                                            rules.information_content[0].isShow
                                        "
                                        class="item"
                                        effect="dark"
                                        :content="
                                            rules.information_content[0].message
                                        "
                                        placement="right"
                                        popper-class="xpt-form__error"
                                    >
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                </el-tabs>
                <el-tabs v-model="secondTab" class="chooseFile">
                    <el-tab-pane
                        label="附件选择"
                        name="chooseFile"
                        style="height: 100%"
                    >
                        <el-row>
                            <el-col :span="24" class="ptb2 flexRowStart">
                                <xpt-upload-v3
                                    uploadBtnText="附件上传"
                                    acceptType="usually"
                                    :dataObj="afterOrederUploadData"
                                    @uploadSuccess="uploadSuccess"
                                    :uploadSize="20"
                                ></xpt-upload-v3>
                                <el-tooltip
                                    content="附件为必选项"
                                    placement="right"
                                    class="upload-warn"
                                >
                                    <i class="el-icon-warning"></i>
                                </el-tooltip>
                            </el-col>
                        </el-row>
                        <el-table
                            :data="filelist"
                            class="chooseFileTable"
                            @selection-change="handleSelectionChange"
                        >
                            <el-table-column
                                width="25"
                                align="center"
                                type="selection"
                            ></el-table-column>
                            <el-table-column
                                prop="name"
                                label="文件名称"
                                show-overflow-tooltip
                            >
                                <template slot-scope="scope">
                                    <div v-if="isImage(scope.row.path)">
                                        <a href="#" class="showImg"
                                            ><img
                                                :src="scope.row.path"
                                                alt="图片"
                                                width="60"
                                        /></a>
                                    </div>
                                    <div v-else>
                                        {{ scope.row.name }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="创建人"
                                width="80"
                                prop="creator_nick"
                                show-overflow-tooltip
                            ></el-table-column>
                            <el-table-column
                                label="创建时间"
                                width="150"
                                prop="create_time"
                                show-overflow-tooltip
                            >
                                <template slot-scope="scope">{{
                                    scope.row.create_time | dataFormat1
                                }}</template>
                            </el-table-column>
                            <el-table-column
                                label="文件大小"
                                width="60"
                                prop="size"
                                show-overflow-tooltip
                            >
                                <template slot-scope="scope">{{
                                    scope.row.size | fileSize
                                }}</template>
                            </el-table-column>
                            <el-table-column label="文件预览" width="60">
                                <template slot-scope="scope">
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        v-if="isImage(scope.row.path)"
                                        @click="showImageList(scope.row)"
                                    >
                                        预览
                                    </el-button>
                                </template>
                            </el-table-column>
                            <el-table-column label="文件删除" width="60">
                                <template slot-scope="scope">
                                    <el-button
                                        type="danger"
                                        size="mini"
                                        @click="deleteUploadFile(scope.row)"
                                    >
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>
                </el-tabs>
            </el-form>
        </div>
        <xpt-image
            :images="imageList"
            :show="ifShowImage"
            :ifUpload="false"
            @close="closeShowImage"
        >
        </xpt-image>
    </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from "@common/Fn.js";
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            firstTab: "feedbackInfo",
            secondTab: "chooseFile",
            isSubmiting: false,
            canSubmit: false,
            questionSelect: {}, //选择的问题商品
            oldQuestionDes: [], //旧的问题，置灰
            questionDes: [], //新的问题，可填
            filelist: [], //附件列表
            filelistSelect: [], //附件选择列表
            imageList: [], //预览图片列表
            ifShowImage: false, //是否显示预览图片列表
            submitData: {
                quality_feedback_no: "", //品质反馈单号
                material_number: "", //物料编码
                material_name: "", //物料名称
                material_spec: "", //物料规格型号
                if_greenway: "N", //是否绿通
                greenway_type: "", //绿通类型
                after_order_question_id: "", //问题商品信息id
                after_order_no: "", //售后单号
                cloudQualityFeedbackInformationVoList: [], //消息记录列表，content为链接，format有文本，图片，视频，其它
                information_content: "", //反馈信息，取商品最新的问题描述信息
            },
            rules: {
                information_content: validate.isNotBlank({
                    self: self,
                    msg: "反馈信息不能为空",
                }),
            },
            afterOrederUploadData: {}, //提交附件库（当前售后单，标志为单号）携带参数
            // qualityFeedbackUploadData: {}//提交附件库（生成的品质反馈单，标志为单号）携带参数
            hasFirstSubmit:false,
        };
    },
    created() {},
    mounted() {
        if (Object.keys(this.params.tableSelect).length == 0) {
            this.$message.error("请选择商品问题");
            return;
        }
        this.getQualityFeedbackNo();
        this.questionSelect = this.params.tableSelect;
        this.questionDes = this.params.tableSelect._questions_des || [];
        this.oldQuestionDes = this.params.tableSelect._oldQuestions_des || [];
        this.afterOrederUploadData = {
            parent_name: "AFTER_ORDER",
            parent_no: this.params.after_order_no,
            child_name: "QUESTION_GOODS",
            child_no: null,
            content: {},
        };
        console.log(this.questionDes);
        console.log(this.oldQuestionDes);
        // console.log(JSON.parse(JSON.stringify(this.questionDes)));
        // console.log(JSON.parse(JSON.stringify(this.oldQuestionDes)));
        this.initData();
        // this.params.callback();
    },
    computed: {},
    methods: {
        //获取品质反馈单编码
        getQualityFeedbackNo() {
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/order/qualityfeedback/getNewPoblemNo",
                {},
                (res) => {
                    if (res.body.result && res.body.content) {
                        this.submitData.quality_feedback_no = res.body.content;
                    } else {
                        this.$message.error("获取品质单编码失败");
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
        //初始化反馈单
        initData() {
            this.submitData.material_number = this.questionSelect.material_number;
            this.submitData.material_name = this.questionSelect.material_name;
            this.submitData.material_spec = this.questionSelect.material_specification;
            this.submitData.after_order_question_id = this.questionSelect.id;
            this.submitData.after_order_no = this.params.after_order_no;
            this.submitData.information_content =
                this.questionSelect.if_goods_question === "Y"
                    ? this.questionDes.slice(0)[1]
                        ? this.questionDes.slice(-2)[0].question_description || ""
                        : this.oldQuestionDes.slice(-1)[0] == '无' ?"":this.oldQuestionDes.slice(-1)[0]
                    : "";
            this.getUploadFile();
        },
        //初始化附件列表
        getUploadFile() {
            let url = "/file-iweb/api/cloud/file/list";
            let params = {
                order_no: this.params.after_order_no,
                page_no: 1,
                page_size: 10000,
            };
            this.$axios("post", url, params)
                .then((res) => {
                    if (res.result) {
                        this.filelist = res.content.list;
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .catch((err) => {
                    this.$message.error(err);
                });
        },
        submitFeedback() {
            this.$refs.submitData.validate((valid) => {
                if (!valid) return;
                //判断是否选择附件
                if(this.filelistSelect.length<=0){
                    this.$message.error("提交品质反馈，请选择或上传附件！");
                    return
                }
                //判断是否选择绿通
                if(this.submitData.if_greenway=="Y"&&!this.submitData.greenway_type){
                  this.$message.warning("请选择绿通类型！");
                  return
                }
                let params = this.formatSubmitParams();
                this.isSubmiting = true;
                //permissionCode=CLOUD_QUALITY_FEEDBACK_SUBMIT由于锁定人也可提交，业务不同，估该接口不加permissionCode
                this.ajax.postStream(
                    "/afterSale-web/api/aftersale/order/qualityfeedback/saveEntity",
                    params,
                    (res) => {
                        if (res.body.result) {
                            this.$message.success(res.body.msg);
                            this.params.callback();
                            this.isSubmiting = false;
                            this.closeAlert();
                        } else {
                            this.$message.error(res.body.msg);
                            this.isSubmiting = false;
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        this.isSubmiting = false;
                    }
                );
            });
        },
        isImage(str) {
            var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
            return reg.test(str);
        },
        isVideo(str) {
            var reg = /\.(mp4)$/i;
            return reg.test(str);
        },
        //格式化要提交的附件消息格式
        formatFileType(str) {
            let imgStr = ".jpg,.png,.jpeg,.gif,.JPG,.PNG,.JPEG,.GIF";
            let videoStr = ".mp4,.MP4";
            if (imgStr.includes(str)) {
                return "picture";
            } else if (videoStr.includes(str)) {
                return "video";
            } else {
                return "other";
            }
        },
        // 格式化要提交的参数
        formatSubmitParams() {
            let self = this;

            //添加信息列表-附件
            if (this.filelistSelect.length > 0) {
                let informationFileList = this.filelistSelect.map((item) => {
                    return {
                        information_format: self.formatFileType(item.file_type),
                        information_content: item.path,
                    };
                });
                this.submitData.cloudQualityFeedbackInformationVoList = informationFileList;
            }

            //添加信息列表-文本
            if (
                this.submitData.information_content &&
                this.submitData.information_content.length > 0
            ) {
                let informationTextObj = {
                    information_format: "text",
                    information_content: this.submitData.information_content,
                };
                this.submitData.cloudQualityFeedbackInformationVoList.unshift(
                    informationTextObj
                );
            }

            let params = Object.assign({}, this.submitData);
            delete params.information_content;
            if(params.if_greenway==='N'){
              delete params.greenway_type;
            }
            return params;
        },
        //初始化预览列表
        initImageList(row) {
            let self = this;
            let imglist = [];
            //过滤出只有图片的列表
            let list = this.filelist.filter((item) => this.isImage(item.path));
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = value.path;
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = value.creator_nick;
                obj.isPucture = true;
                //确定要预览那张图片
                if (value.cloud_file_id === row.cloud_file_id) {
                    obj.active = true;
                } else {
                    obj.active = false;
                }
                imglist.push(obj);
            });
            this.imageList = imglist;
        },
        // 附件列表选择
        handleSelectionChange(data) {
            this.filelistSelect = data;
            console.log("filelistSelect", this.filelistSelect);
        },
        closeShowImage() {
            this.ifShowImage = false;
            //关闭图片预览更新图片附件列表
            this.getUploadFile();
            this.params.callback();
        },
        uploadSuccess() {
            this.getUploadFile();
            this.params.callback();
        },
        showImageList(row) {
            this.initImageList(row);
            this.ifShowImage = true;
        },
        deleteUploadFile(row) {
            let self = this;
            this.$root.eventHandle.$emit("openDialog", {
                txt: "是否确定删除？",
                okTxt: "确定",
                cancelTxt: "取消",
                noShowNoTxt: true,
                ok() {
                    var list_cloud_file_id = [];
                    list_cloud_file_id.push(row.cloud_file_id);
                    self.ajax.postStream(
                        "/file-iweb/api/cloud/file/delete",
                        { list_cloud_file_id: list_cloud_file_id },
                        (res) => {
                            if (res.body.result) {
                                self.$message.success("删除成功");
                                self.getUploadFile();
                            } else {
                                self.$message.error(res.body.msg);
                            }
                        }
                    );
                },
                cancel() {
                    self.$message.success("已取消");
                },
            });
        },
        closeAlert() {
            let self = this;
            self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
        },
    },
};
</script>
<style scope>
/* .el-input {
    width: 100%;
} */
.mgt20 {
    margin-top: 20px;
}
.ptb2 {
    padding: 10px 0px;
}
.flexRowStart{
    display: flex;
    align-items: center;
}
.nowrap {
    white-space: nowrap;
}
.chooseFile {
    height: auto !important;
    position: absolute;
    left: 0;
    right: 0;
    top: 250px;
    bottom: 0;
}
.chooseFileTable {
    height: auto !important;
    left: 0;
    right: 0;
    position: absolute;
    top: 36px;
    bottom: 0;
}
.el-table .el-table__body-wrapper td .cell {
    height: auto;
}
.el-table .el-table__header tr th:first-child .cell {
    text-overflow: clip;
}
.el-table .el-table__body tr td:first-child .cell {
    text-overflow: clip;
}
.showImg {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #dbdbdb;
    margin: 5px 0;
    line-height: 100%;
}
.upload-warn{
    color:#f5c75b;margin-left:5px;
}
.upload-error{
    color:red;margin-left:5px;
}
</style>
