<template>
    <div class='xpt-flex'>
        <el-row :gutter="10" class="xpt-top">
            <el-col class="btn-group" :span="6">
                <el-button type='success' size='mini' @click="getList" :loading="refreshLoading">刷新</el-button>
                <el-button type='warning' size='mini' @click="downloadTemplate()"
                    :loading="downloadLoading">模板下载</el-button>
                <xpt-upload-v3 uploadBtnText="导入" :uploadSize="20" acceptTypeStr=".xlsx,.xls" :dataObj="uploadDataObj"
                    :disabled="false" :ifMultiple="false" :showSuccessMsg="false" @uploadSuccess="uploadSuccess"
                    btnType="success" style="display: inline-block; margin: 0px 10px"></xpt-upload-v3>
                <el-button size="mini" type="warning" @click="showUploadResult()" :disabled="false">查看导入结果</el-button>
            </el-col>
            <el-col :span="18">
                <el-form :model="query" ref="query" label-position="right" label-width="90px">
                    <div class="search-box">
                        <el-form-item label="合并单号：" class="mr10">
                            <xpt-input v-model="query.merge_trade_no" size="mini"></xpt-input>
                        </el-form-item>
                        <el-form-item label="售后单号：" class="mr10">
                            <xpt-input v-model="query.after_order_no" size="mini"></xpt-input>
                        </el-form-item>
                        <el-form-item label="补件单号：" class="mr10">
                            <xpt-input v-model="query.after_supply_no" size="mini"></xpt-input>
                        </el-form-item>
                        <el-button type="success" size="mini" @click="getList" :loading="refreshLoading">查询</el-button>
                        <el-button type="primary" size="mini" @click="reset">重置查询条件</el-button>
                    </div>
                </el-form>
            </el-col>
        </el-row>
        <xpt-list :data='list' :showHead="false" :colData='colData' :pageTotal='pageTotal'
            @page-size-change='pageChange' @current-page-change='currentPageChange' :pageLength="page_size"
            ref='xptList' selection="">
        </xpt-list>
    </div>
</template>

<script>
export default {
    data() {
        let self = this;
        return {
            list: [],
            page_no: 1,
            page_size: 50,
            pageTotal: 0,
            colData: [{
                label: '任务编号',
                prop: 'id',
                width: 140,
            }, {
                label: '创建人',
                prop: 'creator_name',
                width: 100
            }, {
                label: '创建时间',
                prop: 'create_time',
                format: 'dataFormat1',
                width: 150,
            }, {
                label: '合并单号',
                prop: 'merge_trade_no',
                width: 180,
            }, {
                label: '运行状态',
                prop: 'status',
                width: 80,
            }, {
                label: '运行结果',
                prop: 'remark',
                width: 370,
            }, {
                label: '运行完成时间',
                prop: 'finish_time',
                format: 'dataFormat1',
                width: 150,
            },
            {
                label: '售后单',
                prop: 'after_order_no',
                width: 140,
                redirectClick(row) {
                    self.handleDetail('afterSale', row.after_order_id)
                },
            },
            {
                label: '补件单',
                prop: 'after_supply_no',
                width: 140,
                redirectClick(row) {
                    self.handleDetail('supply', row.after_supply_id)
                },
            },
            ],
            refreshLoading: false,
            downloadLoading: false,
            uploadDataObj: {
                parent_name: "售后补件方案导入",
                parent_no: `SUPPLY_IMPORT`, //主要通过该参数获取附件列表
                child_name: null,
                child_no: null,
                content: {},
            },
            query:{
                merge_trade_no:null,
                after_order_no:null,
                after_supply_no:null,
            }
        }
    },
    mounted() {
        this.getList();
    },
    methods: {
        // 监听每页显示数更改事件
        pageChange(pageSize) {
            this.page_size = pageSize
            this.page_no = 1;
            this.getList();
        },
        // 监听页数更改事件
        currentPageChange(pageNo) {
            this.page_no = pageNo
            this.getList();
        },
        getList() {
            let query={}
            Object.keys(this.query).forEach(item=>{
                if(this.query[item]){
                    query[item]=this.query[item]
                }
            })
            let params = {
                page_no: this.page_no,
                page_size: this.page_size,
                ...query
            }
            this.refreshLoading = true;
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/supply/import/list",
                params,
                (res) => {
                    if (res.body.result) {
                        if (!res.body.content) {
                            this.refreshLoading = false;
                            return
                        }
                        this.list = res.body.content.list || [];
                        this.pageTotal = res.body.content.count;
                    } else {
                        this.$message.error(res.body.msg || "查询售后补件方案导入列表成功");
                    }
                    this.refreshLoading = false;
                },
                (err) => {
                    this.$message.error(err || "查询售后补件方案导入列表失败");
                    this.refreshLoading = false;
                }
            );
        },
        reset(){
            Object.keys(this.query).find(item=>{
                this.query[item]=null
            })
        },
        handleDetail(type, id) {
            console.log(type, id);
            if (type === 'afterSale') {
                let params = {
                    id,
                    idList: [],//用于售后单详情的上一页/下一页
                };

                if (document.querySelector('[data-symbol="after_order_id_' + id + '"]')) {
                    this.$message.error('该售后单已经打开')
                } else {
                    this.$root.eventHandle.$emit('creatTab', {
                        name: "售后单详情",
                        params: params,
                        component: () => import('@components/after_sales/index')
                    });
                }
            } else if (type === 'supply') {
                this.$root.eventHandle.$emit('creatTab', {
                    name: '补件单详情',
                    params: { id: id, list: [] },
                    component: () => import('@components/after_sales_supply/supplyOrder_2.vue')
                })
            }
        },
        //   模板下载
        downloadTemplate(callback) {
            let data = {
                "templateName": "批量导入售后补件方案模板"
            };
            !callback && (this.downloadLoading = true);
            this.ajax.postStream(
                "/reports-web/api/template/getTemplateByName",
                data,
                (res) => {
                    if (res.body.result) {
                        if (!callback) {
                            this.download(res.body.content);
                        } else {
                            callback && callback(res.body.content);
                        }
                        this.downloadLoading = false;
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                () => {
                    this.downloadLoading = false;
                }
            );
        },
        download(content) {
            let url = content.url;

            let filename = content.template_name;
            if (!fetch) {
                window.location.href = url;
                return;
            }
            return fetch(url).then((res) => {
                res.blob().then((blob) => {
                    let a = document.createElement("a");
                    let url = window.URL.createObjectURL(blob);
                    a.href = url;
                    a.download = filename;
                    a.click();
                    a.remove();
                    window.URL.revokeObjectURL(url);
                });
            });
        },
        //上传成功返回结果
        uploadSuccess(result) {
            if (result.length > 0 && !!result[0].path) {
                this.importSub(result[0].path);
            }
        },
        //导入
        importSub(fileUrl) {
            let templateUrl = ""
            this.downloadTemplate(content => {
                templateUrl = content.url || ''
                let params = {
                    fileUrl: fileUrl,
                    "templateUrl": templateUrl
                };
                this.ajax.postStream(
                    "/reports-web/api/reports/aftersale/order/supply/excelImport",
                    params,
                    (res) => {
                        if (res.body.result) {
                            this.$message.success(res.body.msg);
                        } else {
                            this.$message.error(res.body.msg);
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                    }
                );
            })
        },
        //导入结果
        showUploadResult() {
            this.$root.eventHandle.$emit('alert', {
                component: () => import('@components/after_sales_report/export'),
                style: 'width:900px;height:600px',
                title: '导入结果',
                params: {
                    query: {
                        type: 'EXCEL_TYPE_AFTERSALE_SUPPLY_IMPORT',
                    },
                },
            })
        },
    }
}
</script>

<style scoped>
.btn-group{
    display: flex;
    align-items: center;
}
.search-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.mr10 {
    margin-right: 10px;
}
</style>