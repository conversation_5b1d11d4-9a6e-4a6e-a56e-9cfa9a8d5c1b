<!-- 淘宝申请单信息 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type="primary" size="mini" @click='addFun' :disabled="!params.ifEditTaoBaoAdd || !params.ifEditAll">新增行</el-button>
				<el-button type="danger" size="mini" @click='delFun' :disabled="!params.ifEditAll">删除行</el-button>
				<el-button type="success" size="mini" @click='introduceFun' :disabled="!params.ifEditAll">引入</el-button>
				<el-button type="success" size="mini" @click='fullFun' :disabled="!params.ifEditAll">批量填充</el-button>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom">
			<el-table border :data='list' tooltip-effect="dark" width='100%' style="width: 100%;" id='merge_goodList'  @selection-change="handleSelectionChange" @cell-dblclick="dblclick">
			    <el-table-column type="selection" width="50" ></el-table-column>
				<el-table-column label="淘宝单号" prop="tid">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.tid}}</span>
						<el-input v-else size="mini" v-model="scope.row.tid" style="width:100%;"></el-input>
					</template>

				</el-table-column>
				<el-table-column label="昵称" prop="buyer_nick">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.buyer_nick}}</span>
						<el-input v-else size="mini" v-model="scope.row.buyer_nick" icon="search" @click="selectBuyersName(scope.row)" style="width:100%;"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="申请日期" prop="goods_return_time">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.goods_return_time | dataFormat1}}</span>
						<el-date-picker
							v-else
						    v-model="scope.row.goods_return_time"
						    type="datetime"
						    placeholder="选择日期" size="mini" style="width:100%;">
						</el-date-picker>
					</template>
				</el-table-column>
				<el-table-column label="退款方式" prop="refund_type"  align="right" header-align="left" >
					<template slot-scope="scope">
						<!-- 淘宝下载的协议退款和纠纷退款不可编辑 -->
						<span v-if='scope.row.show && scope.row.refund_type ==="AGREEMENT_REFUND" &&  scope.row.refund_type ==="REFUND_DISPUTE"'>{{scope.row.refund_type_string}}</span>
						<el-select v-if='scope.row.show && scope.row.refund_type !=="AGREEMENT_REFUND" &&  scope.row.refund_type !=="REFUND_DISPUTE"' v-model="scope.row.refund_type" size="mini" placeholder="请选择" style="width:100%"   >
							<el-option v-for="(item,index) in refundTypes" :key="item.index" :label="item.label" :value="item.value"> </el-option>
						</el-select>
						<el-select  v-model="scope.row.refund_type" size="mini" placeholder="请选择" style="width:100%"   v-else>
						    <el-option v-for="(item,index) in addRefundTypes" :key="item.index" :label="item.label" :value="item.value"> </el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column label="淘宝退款单号"  prop="refund_id">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.refund_id}}</span>
						<el-input v-else size="mini" v-model="scope.row.refund_id" style="width:100%;"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="淘宝店铺" prop="seller_nick">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.seller_nick}}</span>
						<el-input v-else size="mini" v-model="scope.row.seller_nick" style="width:100%;"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="支付宝账号" prop="alipay_account">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.alipay_account}}</span>
						<el-input v-else size="mini" v-model="scope.row.alipay_account" style="width:100%;"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="推荐处理人" prop="best_staff_name">
					<template slot-scope="scope">
						<span>{{scope.row.best_staff_name}}</span>
					</template>
				</el-table-column>
				<el-table-column label="淘宝退款金额" prop="refund_fee">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.refund_fee | number}}</span>
						<el-input v-else size="mini" type="number" v-model="scope.row.refund_fee" style="width:100%;" @blur="refundFeeFun(scope.row)"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="退款原因" prop="reason">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.reason}}</span>
						<el-input v-else size="mini" v-model="scope.row.reason" style="width:100%;"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="超时时间" prop="timeout">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.timeout | dataFormat1}}</span>
						<el-date-picker
							v-else
						    v-model="scope.row.timeout"
						    type="datetime"
						    placeholder="选择日期" size="mini" style="width:100%;">
						</el-date-picker>
					</template>
				</el-table-column>
				<el-table-column label="淘宝退款状态" prop="status">
					<template slot-scope="scope">
						<span v-if='scope.row.show'>{{scope.row.status_string}}</span>
						<el-select  v-model="scope.row.status" size="mini" placeholder="请选择" style="width:100%"   v-else>
						    <el-option v-for="(item,index) in statusList" :key="item.index" :label="item.label" :value="item.value"> </el-option>
						</el-select>
					</template>
				</el-table-column>
			</el-table>
		</el-row>
	</div>
</template>
<script>
	import Fn  from '@common/Fn.js'
	export default {
		props: ['params','taoBaoList'],
		data() {
			return {
				list: [],
				selectArr: [],
				//淘宝下载的
				refundTypes:[{
					label: '支付宝退款',
					value: 'ZFB_REFUND',
				},{
					label: '银行卡退款',
					value: 'BANK_REFUND',
				},{
					label: '拍拍退款',
					value: 'PAI_PAI_REFUND',
				},{
					label: '财付通退款',
					value: 'TENPAY_REFUND',
				},{
					label: '保证金退款',
					value: 'MARGIN',
				},{
					label: 'B2C线上退款',
					value: 'B2C_ONLINE_REFUND',
				},{
					label: 'B2C线下退款',
					value: 'B2C_OFFLINE_REFUND',
				},],
				//手动新增
				addRefundTypes:[{
					label: '支付宝退款',
					value: 'ZFB_REFUND',
				},{
					label: '银行卡退款',
					value: 'BANK_REFUND',
				},{
					label: '协议退款',
					value: 'AGREEMENT_REFUND',
				},{
					label: '纠纷退款',
					value: 'REFUND_DISPUTE',
				},{
					label: '拍拍退款',
					value: 'PAI_PAI_REFUND',
				},{
					label: '财付通退款',
					value: 'TENPAY_REFUND',
				},{
					label: '保证金退款',
					value: 'MARGIN',
				},{
					label: 'B2C线上退款',
					value: 'B2C_ONLINE_REFUND',
				},{
					label: 'B2C线下退款',
					value: 'B2C_OFFLINE_REFUND',
				},],
				statusList: [{
					label: '买家已经申请退款，等待卖家同意',
					value: 'WAIT_SELLER_AGREE',
				},{
					label: '卖家已经同意退款，等待买家退货',
					value: 'WAIT_BUYER_RETURN_GOODS',
				},{
					label: '买家已经申请退款，等待卖家同意',
					value: 'WAIT_SELLER_CONFIRM_GOODS',
				},{
					label: '卖家拒绝退款',
					value: 'SELLER_REFUSE_BUYER',
				},{
					label: '退款关闭',
					value: 'CLOSED',
				},{
					label: '退款成功',
					value: 'SUCCESS',
				}]
			}
		},
		methods: {
			addFun() {
				let obj = {
					key: new Date().getTime(),
					 after_refund_download_id: null,//流水号		Long
					 address: null,//地址		String
					 advance_status: null,//退款先行垫付	Byte
					 alipay_no: null,//支付宝交易号		String
					 buyer: null,//买家id		Long
					 buyer_nick: null,//买家昵称		String
					 company_name: null,//物流公司名称		String
					 cs_status: null,//客服接入状态		Byte
					 description: null,//退款说明		String
					 goods_return_time: null,//退款时间—申请日期	Date
					 good_status: null,//货物状态		String
					 has_good_return: null,//是否退货		Byte
					 iid: null,//iid		String
					 modified: null,//更新时间		Date
					 num: null,//购买数量		Long
					 num_iid: null,//申请退款的商品数字编号		Long
					 oid: null,//子订单号		Long
					 order_status: null,//退款对应的订单交易状态		String
					 payment: null,//支付给卖家的金额		BigDecimal
					 price: null,//商品价格		BigDecimal
					 reason: null,//退款原因—退款原因		String
					 refund_fee: null,//退还金额—淘宝退款金额	BigDecimal
					 refund_id: null,//退款id—淘宝退款单号		Long
					 timeout: null,//超时时间---超时时间		Date
					 seller_nick: null,//卖家昵称—淘宝店铺		String
					 shipping_type: null,//物流方式		String
					 status: null,//退款状态		String
					 tid: null,//交易单号—淘宝单号		Long
					 merge_no: null,//合并单号		String
					 title: null,//商品标题		String
					 total_fee: null,//交易总金额	BigDecimal
					 currency: null,//币别		String
					 remind_type: null,//提醒类型		Byte
					 exist_timeout: null,//是否存在超时		Byte
					 refund_phase: null,//退款阶段		String
					 refund_type: null,//退款方式		String
					 come_from_type: null,//下载来源		String
					 best_staff: null,//推荐处理人	Long
					 best_staff_name: null,//处理人姓名—推荐处理人		String
					 after_status: null,//售后业务状态  		String
					 if_close: null,//是否关闭		Byte
					 modifier: null,//单据最后修改人		Long
					 modify_time: null,//单据最后修改时间		Date
					 alipay_account: null,//支付宝帐号—支付宝账号		String
				}
				this.list.push(obj);
			},
			delFun() {
				if(this.selectArr.length === 0) {
					this.$message.error('请选择要删除的行');
					return ;
				}
				this.list = this.list.filter(v => {
					var bool = true;
					this.selectArr.forEach(v1 => {
						if(v.key === v1.key) {
							bool = false;
						}
					})
					return bool;
				})
				//如果是已有或是引入删除要通过接口，手动新添加的则不用
				var list_ids = [];
				this.selectArr.map(v => {
					if(v.after_refund_download_id) {
						list_ids.push(v.after_refund_download_id);
					}
				})
				if(list_ids.length > 0) {
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/delete',{list_after_refund_download_id:list_ids},res => {
						if(res.body.result) {
							this.ajax.postStream('/after-web/api/after/refund/get',{after_refund_id:this.params.after_refund_id},res => {
								if(res.body.result) {
									res.body.content.listAfterRefundDownloadVO && res.body.content.listAfterRefundDownloadVO.map(v =>{
										v.show = true;
									})
									this.list = res.body.content.listAfterRefundDownloadVO || [];
									this.$message.success('删除成功');
								}
							})
						}else {
							this.$message.error(res.body.msg);
						}
					})
				}
			},
			introduceFun() {
				this.ajax.postStream('/after-web/api/after/refund/import',{after_refund_id:this.params.after_refund_id},res => {
					if(res.body.result) {
						this.ajax.postStream('/after-web/api/after/refund/get',{after_refund_id:this.params.after_refund_id},res => {
							if(res.body.result) {
								res.body.content.listAfterRefundDownloadVO && res.body.content.listAfterRefundDownloadVO.map(v =>{
									v.show = true;
								})
								this.list = res.body.content.listAfterRefundDownloadVO || [];
								this.$message.success('引人成功');
							}
						})
					}else {
						this.$message.error(res.body.msg);
					}
				})
			},
			fullFun() {
				if(!this.key) {
					this.$message.error('请双击要填充的原数据')
				}
				if(this.checkFull() && this.key){
					this.selectArr.forEach(v => {
					v[this.key] = this.value;
				})
				}

			},
			checkFull() {
				if(this.selectArr && this.selectArr.length == 0) {
					this.$message.error('请选择要批量填充的行');
					return;
				}
				var bool = true;
				this.selectArr.forEach(v => {
					if(v.show) bool = false;
				})
				!bool && this.$message.error('只能填充新添加的行');
				return bool;
			},
			dblclick(row, column) {
				// console.log(column,"column");
				// if(!column.property) {
				// 	this.$message.error('本列不能进行填充');
				// 	return;
				// }
				this.key = column.property;
				this.value = column.property && row[column.property];
			},
			handleSelectionChange(row) {
				this.selectArr = row;
			},
			selectableFun(row) {
				return !row.show;
			},
			selectBuyersName(obj) {
				var params = {};
				params.callback = d=>{
					obj.buyer_nick = d.name;
					obj.buyer = d.cust_id;
				}
				this.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/after_sales_common/selectBuyersNameList'),
					style:'width:800px;height:600px',
					title:'买家昵称列表'
				});
			},
			refundFeeFun(row){
				row.refund_fee = Fn.number(row.refund_fee);
			}
		},
		watch: {
			'taoBaoList':function(newVal) {

				let list = [];
				if(newVal.length > 0) {

					newVal.map(v => {
						let obj ={
							show: true
						};
						Object.assign(obj,v);
						list.push(obj);
					})
				}
				this.list = list;
			},
			'params.ifSave':function() {
				var list = [];
				this.list.map(v => {
					var obj = {};
					Object.assign(obj,v);
						delete obj.key;
						delete obj.show;
					list.push(obj);
				})
				console.log(list,'list')
				list = list && list.filter(v => {
					var bool = false;
					for(let v1 of Object.values(v)){
						if(v1 != null && v1 != ''){
							bool = true;
						}
					}
					console.log(bool,'bool')
					return bool;
				})
				this.$emit('getTaoBaoList',list);
			}
		},
		mounted() {
		},
		destroyed() {
		}
	}
</script>
