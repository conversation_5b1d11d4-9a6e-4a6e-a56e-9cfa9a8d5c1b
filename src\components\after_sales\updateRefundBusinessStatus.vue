<template>
<div class='xpt-flex'>
	<xpt-headbar>
    <el-button type='primary' slot='left' size='mini' @click="save">保存</el-button>
		      <el-button type='danger' slot='left' size='mini' @click="close">取消</el-button>
	</xpt-headbar>
  <el-form label-width='100px' :model='query'  ref="query">
    <!-- <el-row class="mgt30">
			<el-col :span='40'  class="xpt_col">
        <div style="width:100%;height:50px;text-align:center;line-height: 50px;">该物料当前存在{{hasData}}行生效商品，{{canEditData}}行可批量删除</div>
      </el-col>
		</el-row> -->
    <el-row :gutter='40' style='height:50px'>
      <el-col :span='40'  class="xpt_col">
        <el-form-item label='请选择' prop='number'>
         
          <el-select
            v-model="query.business_status"
            size="mini"
            placeholder="请选择"
            >
              <el-option key="CREATE" label="创建" value="CREATE"></el-option>
              <el-option key="SUBMIT" label="已提交" value="SUBMIT"></el-option>
              <el-option key="CHECK" label="已核对" value="CHECK"></el-option>
          </el-select>
        </el-form-item>
        
      </el-col>
     
    </el-row>
   

    <!-- <el-row class="mgt30">
			<el-col :span="24" class="txt-r">
				<el-button class="mgr60" type="primary" size="mini" @click="save">确定</el-button> 
			</el-col>
		</el-row> -->
  </el-form>
</div>
</template>
<script>
  export default {
    data() {
      let self = this;
      return {
        oldGoodsList: [],
        query:{
          business_status:'CREATE',
        },
        // hasData:self.params.sortList.length,
        // canEditData:self.params.canChangeList.length
      }
    },
	  props:['params'],
    methods: {
      close(){
        let self = this;
        self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
      },
      save(){
        
        this.params.callback(this.query);
        this.close();
      },
      changeSelect(item,index){
        if(item.checked != true){
          this.oldGoodsList[index].qty = '';
          // console.log(item,index,this.oldGoodsList[index])          

        }
      }
     
    },
    mounted(){
      // this.getOldGoodsList();
    }
  }
</script>
<style>
	.repo-input{
		width:70px;
    float: right;
    margin-right: 50px;
	}
  .xpt_col{
    margin-bottom: 10px;
  }
</style>
