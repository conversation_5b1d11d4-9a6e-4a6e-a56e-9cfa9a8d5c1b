<!-- 买家昵称列表 -->
<template>
	<div class="xpt-flex">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span='16'>
				<el-button type='warning' size='mini' @click='close'>确认</el-button>
			</el-col>
			<el-col :span='8' class='xpt-align__right'>
				<el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="searchString" :on-icon-click="searchData">
				</el-input>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom mgb20" >
			<el-table :data="list" border tooltip-effect="dark" width='100%' style="width: 100%" highlight-current-row @current-change="handleCurrentChange">
			    <el-table-column width="55" align='center' >
					<template slot-scope='scope'>
						<el-radio-group v-model="selectCode">
						    <el-radio :label="scope.row.number" class='xpt-table__radio'></el-radio>
						</el-radio-group>
					</template>
			   	</el-table-column>
			    <el-table-column prop="number" label="客户编码" ></el-table-column>
			    <el-table-column prop="name" label="客户名称" ></el-table-column>
		  	</el-table>
		</el-row>
	  	<el-row class='xpt-pagation'>
		  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
			  	:current-page="pageNow" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
			  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
			</el-pagination>
		</el-row>
	</div>
</template>
<script>

export default {
	data(){
		return {
			searchString:'',
			list:[],
			selectCode:'',
			pageNow: 1,
			pageTotal:0,
			pageSize: 10
		}
	},
	props:['params'],
	methods:{
		handleCurrentChange(data){
			if(data){
				this.selectCode=data.number;
			}

		},
		close(){

			let data = this.list.find(d=>d.number===this.selectCode)
			if(data==undefined){
				this.$message.error('请先选择一个客户');
				return;
			}
			this.params.callback(data)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		searchData(){
			this._getlist();
		},
		pageSizeChange(pageSize){
			this.pageSize = pageSize;
			this._getlist();
		},
		pageChange(page){
			this.pageNow = page
			this._getlist();
		},
		_getlist(){
			let self = this
			,	data = {
				number: self.searchString,//	搜索条件
			    name: "", 
			    account: "", 
			    member_type: "", 
			    effective_status: "B", 
			    low_create_date: "", 
			    upper_create_date: "", 
				page_no:self.pageNow,//	页面 默认1
				page_size:self.pageSize,//	大小 默认20
			};
			this.ajax.postStream('/order-web/api/customer/list',data,d=>{
				if(d.body.result&&d.body.content){
					self.pageTotal = d.body.content.count;
					self.list = d.body.content.list||[];
				}
			},d=>{
				self.$message.error(b.body.msg);
			})
		}
	},
	mounted() {
		if(this.params.buyer_nick) {
			this.searchString = this.params.buyer_nick;
		}
		this._getlist();
	}
}
</script>
