<!-- 差价商品选择，多选，单选 -->
<template>
	<div class="xpt-flex">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span='16'>
				<el-button type='warning' size='mini' @click='close'>确认</el-button>
			</el-col>
			<el-col :span='8' class='xpt-align__right'>
				<el-input placeholder="请输入合并单号" icon="search" size='mini' v-model="search" :on-icon-click="searchData">
				</el-input>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom mgb20" >
			<el-table ref="multipleTable" :data="goodsList" border tooltip-effect="dark" width='100%' style="width: 100%" @selection-change="handleSelectionChange" @row-dblclick='d => {
					handleSelectionChange([d])
					close()
				}'>
				<el-table-column width="55" align='center' v-if="ifRaido">
					<template slot-scope='scope'>
						<el-radio-group v-model="selectCode">
						    <el-radio :label="scope.row.sourceMaterialId" class='xpt-table__radio'></el-radio>
						</el-radio-group>
					</template>
			   	</el-table-column>
			    <el-table-column width="55" align='center' type="selection" v-if="!ifRaido"></el-table-column>
			    <el-table-column prop="customer_name" label="买家昵称" width="100" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.customer_name : ''}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="merge_trade_no" label="合并单号" width="100" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.merge_trade_no : ''}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop='create_time' label="日期" width="100" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.create_time : '' | dataFormat1}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="buyer_memo" label="买家备注" width="80" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.buyer_memo : ''}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="created" label="拍单时间" width="100" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.created : '' | dataFormat1}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="pay_time" label="支付时间" width="100" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.created : '' | dataFormat1}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="seller_bill_memo" label="订单备注" width="100" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.seller_bill_memo : ''}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="shop_name" label="订单店铺" width="80" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.shop_name : ''}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="tid" label="淘宝单号" width="80" show-overflow-tooltip>
			    	<template slot-scope="scope">
			    		<span>{{scope.row.show ? scope.row.tid : ''}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="materia_number" label="商品编码" width="120" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="materia_name" label="商品名称" width="80" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="materia_specifications" label="商品规格描述" width="200" show-overflow-tooltip></el-table-column>
		  	</el-table>
		</el-row>
	  	<el-row class='xpt-pagation'>
		  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
			  	:current-page="pageNow" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
			  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
			</el-pagination>
		</el-row>
	</div>
</template>
<script>

export default {
	data(){
		return {
			search: '',
			selectCode: '',
			pageNow: 1,
			pageTotal: 0,
			pageSize: 10,
			ifRaido: false,
			goodsList: [],
			selectGoodsList: []
		}
	},
	props:['params'],
	methods:{
		toggleSelection(rows) {
			var  lists = [];
			rows.map(v => {
				if(v.page === this.pageNow) {
					lists = v.list;
					return;
				}
			});
	        if (lists) {
	            this.goodsList.forEach(goods => {
	            	lists.forEach(list => {
	            		if(goods.sourceMaterialId === list.sourceMaterialId) {
	            			this.$refs.multipleTable.toggleRowSelection(goods);
	            		}
	            	})

	            });
	        } else {
	          this.$refs.multipleTable.clearSelection();
	        }
	    },
		close(){
			var listData = [];
			var backList = [];
			if(this.ifRaido) {
				listData.push(this.goodsList.find(d=>d.sourceMaterialId === this.selectCode));
			}
			this.selectGoodsList.map(v => {
				listData = [...listData,...v.list];
			});
			listData.forEach(v => {
				var obj = {
					sys_trade_id: v.sys_trade_id,//销售订单ID	Long
					sys_trade_no: v.sys_trade_no,//销售单号		String
					tid: v.tid,//淘宝单号—淘宝单号	String
					created: v.created,//拍单时间—购买时间	Date
					pay_time: v.pay_time,//支付时间		Date
					shop_name: v.shop_name,//店铺名称		String
					buyer_memo: v.buyer_memo,//买家备注	String
					seller_bill_memo: v.seller_bill_memo,//订单备注		String
					price_list_id: v.price_list_id,//价格表ID	Long
					price_list_name: v.price_list_name,//价目表名称	String
					create_time: v.create_time,//创建时间	Date
					merge_trade_id: v.merge_trade_id,//合并订单	Long
					merge_trade_no: v.merge_trade_no,//合并订单单号	String
					customer_name: v.customer_name,//客户昵称	String
					materia_id: v.materia_id,//商品ID	Long
					materia_number: v.materia_number,//商品编码—商品编码	String
					materia_name: v.materia_name,//商品名称—商品名称		String
					materia_specifications: v.materia_specifications,//商品规格—规格描述	String
					unit_id: v.unit_id,//单位 (取物料基本单位)	Long
					unit_name: v.unit_name,//单位名称	—单位	String
					act_price: v.act_price,//实际售价	--拍下价格	BigDecimal
					status: v.status,//行状态—订单状态		String
				};
				backList.push(obj);
				if(this.ifRaido) {
					backList[0].index = this.params.index;
				}

			})
			this.params.callback(backList);
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		searchData(){
			this._getGoodsList();
		},
		pageSizeChange(pageSize){
			this.pageSize = pageSize;
			this._getGoodsList();
		},
		pageChange(page){
			this.pageNow = page
			this._getGoodsList();
		},
		_getGoodsList(){

			let self = this,
				data = {
					merge_trade_no: this.search || this.params.merge_trade_no,
					// merge_trade_no: 'HB14110300154760',
					page_size: this.pageSize,
					page_no: this.pageNow
				};
			this.search = data.merge_trade_no;
			this.ajax.postStream('/afterSale-web/api/aftersale/order/plan/addPriceSpread',data,d=>{
				if(d.body.result&&d.body.content){
					this.pageTotal = d.body.content.count;

					var list = d.body.content.list||[];
					var goodsList = [];
					list.forEach(v => {
						if(v.listSysOrderVO && v.listSysOrderVO.length > 0) {
							v.listSysOrderVO.forEach((v1,index) => {

								v.show = index == 0 ? true : false;
								var obj = Object.assign({},v,v1);
								obj.merge_trade_no = v.merge_trade_no
								goodsList.push(obj);
							})
						}else {
							v.show = true;
							var obj = Object.assign({},v);
							goodsList.push(obj);
						}
					})
					this.goodsList = goodsList;
					setTimeout(() => {
						this.toggleSelection(this.selectGoodsList);
					},100);

				}else {
					this.$message.error(d.body.msg)
				}
			})
		},
		handleSelectionChange(selectArr) {
			if(this.ifRaido) {
				return;
			}
			var obj = {
				page: this.pageNow,
				list: selectArr
			};
			var bool = true;
			this.selectGoodsList.map((v) => {
				if(v.page === this.pageNow) {
					v = obj;
					bool = false;
					return;
				}
			})
			if(bool) {
				this.selectGoodsList.push(obj);
			}
		},
	},
	mounted() {
		//有多选和单选功能，“ADD”为多选
		if(this.params.key === "ADD") {
			this.ifRaido = false;
		}else if(this.params.key === "EDIT"){
			this.ifRaido = true;
		}
		this._getGoodsList();
	}
}
</script>
