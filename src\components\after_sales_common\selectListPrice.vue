<!-- 差价商品价目表列表 -->
<template>
	<div class="xpt-flex">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span='8'>
				<el-button type='warning' size='mini' @click='close'>确认</el-button>
			</el-col>
			
			<el-col :span='24' class='xpt-align__right'>
				<!-- <el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="searchString" :on-icon-click="searchData">
				</el-input> -->
				选择价目表
          		<xpt-input v-model='query.priceName' size='mini' ></xpt-input>
				
				选择店铺
          		<xpt-input v-model='query.shopName' size='mini'></xpt-input>
				
				价目表生效时间
				<el-date-picker
					v-model="searchString"
				    type="datetime"
				    size="mini"
				    :clearable="false"
				></el-date-picker>
				<el-button type='primary' size='mini' @click='searchData'>查询</el-button>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom mgb20" >
			<el-table :data="list" border tooltip-effect="dark" width='100%' style="width: 100%" highlight-current-row @current-change="handleCurrentChange" @row-dblclick="d => {
					handleCurrentChange(d)
					close()
				}">
			    <el-table-column width="55" align='center' >
					<template slot-scope='scope'>
						<el-radio-group v-model="selectCode">
						    <el-radio :label="scope.row.key" class='xpt-table__radio'></el-radio>
						</el-radio-group>
					</template>
			   	</el-table-column>
			    <el-table-column prop="price_list_code" label="价目表编码" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="shop_name" label="店铺" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="price_list_name" label="价目表名称" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="price" label="价格" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="price_list_type_string" label="价目表类型" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="store_area_string" label="状态（已审核）全局标志" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="material_code" label="物料编码" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="material_name" label="物料名称" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="material_specification" label="规格描述" show-overflow-tooltip></el-table-column>
			    <el-table-column prop="enable_date" label="生效日期" show-overflow-tooltip>
			    	<template slot-scope="scope">{{scope.row.enable_date | dataFormat1}}</template>
			    </el-table-column>
			    <el-table-column prop="disable_date" label="失效日期" show-overflow-tooltip>
			    	<template slot-scope="scope">{{scope.row.disable_date | dataFormat1}}</template>
			    </el-table-column>
		  	</el-table>
		</el-row>
	  	<el-row class='xpt-pagation'>
		  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
			  	:current-page="pageNow" :page-sizes="[1000]" :page-size="pageSize"
			  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
			</el-pagination>
		</el-row>
	</div>
</template>
<script>

export default {
	data(){
		return {
			searchString: this.params.singleShotDate || +new Date,
			list:[],
			selectCode:'',
			pageNow:1,
			pageTotal:0,
			pageSize:10,
			selectObj:null,
			query:{
				shopName:'',
				priceName:'',
			}
		}
	},
	props:['params'],
	methods:{
		handleCurrentChange(data){
			if(data){
				this.selectCode=data.key;
				this.selectObj = data;
			}
		},
		close(){
			if(this.selectObj === undefined || this.selectObj === null){
				this.$message.error('请先选择一行');
				return;
			}
			this.params.callback(this.selectObj);
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		searchData(){
			this._getlist();
		},
		pageSizeChange(pageSize){
			this.pageSize = pageSize;
			this._getlist();
		},
		pageChange(page){
			this.pageNow = page
			this._getlist();
		},
		_getlist(){
			let self = this,
			data = {
				material_id: this.params.material_id,
				singleShotDate: this.searchString,
				shopName:this.query.shopName,
				priceName:this.query.priceName,
				useType:(this.query.shopName == '' && this.query.priceName == '')?'N':'Y'
			};
			this.ajax.postStream('/price-web/api/price/order/getAllPriceByMaterial',data,(res => {
				if(res.body.result){
					var listData = res.body.content || [];

					// 由于接口没分页一次返回所有数据，所以先前端按生效时间倒序排列
					listData.sort((a, b) => b.enable_date - a.enable_date)

					this.$message.success(res.body.msg)
					this.list = listData;
					this.pageTotal = this.list.length
					this.list.map( v => {
						v.key = new Date().getTime() + Math.random()* 100;
					})
				}else {
					this.$message.error(res.body.msg)
				}
			}))
		},
		// 选择店铺
		openShop2() {
			let self = this;
			let params = {
				callback(data) {
				self.query.shop_id = data.shop_id;
				self.query.shop_name = data.shop_name;
				},
				selection: 'radio'
			}
			this.$root.eventHandle.$emit('alert', {
				params: params,
				component:() => import('@components/shop/list'),
				style: 'width:800px;height:500px',
				title: '店铺列表'
			})
		},
		shopChange(val) {
			if(!val) {
				this.query.shop_id = '';
				this.query.priceName = '';
			}
		},
	},
	mounted() {
		this._getlist();
	}
}
</script>
