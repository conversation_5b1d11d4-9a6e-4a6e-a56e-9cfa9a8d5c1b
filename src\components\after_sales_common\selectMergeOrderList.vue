<!-- 合并订单列表 -->
<template>
	<div class="xpt-flex">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span='16'>
				<el-button type='warning' size='mini' @click='close'>确认</el-button>
			</el-col>
			<el-col :span='8' class='xpt-align__right'>
				<el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="searchString" :on-icon-click="searchData">
				</el-input>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom mgb20" >
			<el-table :data="list" border tooltip-effect="dark" width='100%' style="width: 100%" highlight-current-row @current-change="handleCurrentChange">
			    <el-table-column width="55" align='center' >
					<template slot-scope='scope'>
						<el-radio-group v-model="selectCode">
						    <el-radio :label="scope.row.index" class='xpt-table__radio'></el-radio>
						</el-radio-group>
					</template>
			   	</el-table-column>
			    <el-table-column prop="merge_trade_no" label="合并订单" ></el-table-column>
			    <el-table-column prop="nick_name" label="买家昵称" ></el-table-column>
			    <el-table-column prop="saleman_name" label="业务员" ></el-table-column>
			    <el-table-column prop="from_user_group_name" label="分组" ></el-table-column>
			    <el-table-column prop="total_amount" label="订单总金额" ></el-table-column>
			    <el-table-column label="拍单时间" >
			    	<template slot-scope="scope">
			    		<span>{{scope.row.order_time | dataFormat1}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column label="支付时间" >
			    	<template slot-scope="scope">
			    		<span>{{scope.row.pay_time | dataFormat1}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop="shop_name" label="订单店铺" ></el-table-column>
			    <el-table-column prop="out_stock_time" label="承诺发货日期" >
			    	<template slot-scope="scope">
			    		<span>{{scope.row.out_stock_time | dataFormat1}}</span>
			    	</template>
			    </el-table-column>
			    <el-table-column prop='tid' label="淘宝单号" ></el-table-column>
			    <el-table-column prop="amount" label="数量"></el-table-column>
			    <el-table-column prop="batch_trade_no" label="批次单号"></el-table-column>
		  	</el-table>
		</el-row>
	  	<el-row class='xpt-pagation'>
		  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
			  	:current-page="pageNow" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
			  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
			</el-pagination>
		</el-row>
	</div>
</template>
<script>

export default {
	data(){
		return {
			searchString:'',
			list:[],
			selectCode:'',
			pageNow:1,
			pageTotal:0,
			pageSize:10,
			selectObj:null
		}
	},
	props:['params'],
	methods:{
		handleCurrentChange(data){
			if(data){
				this.selectCode=data.index;
				this.selectObj = data;
			}

		},
		close(){

			if(this.selectObj === undefined || this.selectObj === null){
				this.$message.error('请先选择一个合并订单');
				return;
			}
			this.params.callback(this.selectObj);
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		searchData(){
			this._getlist();
		},
		pageSizeChange(pageSize){
			this.pageSize = pageSize;
			this._getlist();
		},
		pageChange(page){
			this.pageNow = page
			this._getlist();
		},
		_getlist(){
			let self = this
			,	data = {
				page_no:self.pageNow,//	页面 默认1
				page_size:self.pageSize,//	大小 默认20
				search_string:self.searchString,//	搜索条件
			};
			this.ajax.postStream('/order-web/api/mergetrade/getList',data,d=>{
				if(d.body.result&&d.body.content){
					d.body.content.list.map(v => {
						v.index = new Date().getTime() + Math.random()*100;
					})
					self.pageTotal = d.body.content.count;
					self.list = d.body.content.list||[];
				}
			},d=>{
				self.$message.error(b.body.msg);
			})
		}
	},
	mounted() {
		if(this.params.merge_trade_no) {
			this.searchString = this.params.merge_trade_no;
		}
		this._getlist();
	}
}
</script>
