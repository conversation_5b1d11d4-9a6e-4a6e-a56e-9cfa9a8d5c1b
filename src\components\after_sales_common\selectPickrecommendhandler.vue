<!-- 推荐处理人列表:单选 -->
<template>
<div class="xpt-flex">
	<el-row	class='xpt-top'	:gutter='40'>
		<el-button type='primary' size='mini' @click='() => submit()' :disabled="!selectObject">确认</el-button>
		<el-col class='xpt-align__right' :span='20' style="float:right;">
			<el-select v-model="search.limit_field" size='mini' style="width:150px;">
				<!-- <el-option label="用户账号" value="employee_number"></el-option> -->
				<el-option label="用户名称" value="real_name"></el-option>
				<el-option label="昵称" value="nick_name"></el-option>
			</el-select>
			<el-select v-model="search.operator" size='mini' style="width:100px;">
				<el-option label="等于" value="="></el-option>
				<el-option label="包含" value="%"></el-option>
			</el-select>
			<el-input placeholder="请输入查询值" size='mini' v-model="search.field_value"></el-input>
			<el-button type='primary' @click="getSearch" size='mini'>查询</el-button>
		</el-col>
	</el-row>
	<xpt-list
		:data='roleList'
		:showHead="false"
		:colData='colData'
		:pageTotal='pageTotal'
		selection="radio"
		@search-click='searchFun'
		@radio-change='select'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
		@row-dblclick="submit"
	></xpt-list>
</div>
</template>

<script>
export default {
	props: ['params'],
	data (){
		return {
			roleList: [],
			selectObject: null,
			pageTotal: 0,
			search:{
				field_value: "",
				limit_field: "",
				operator: "%",
				page_no: 1,
				page_size: 50,
				// merge_trade_id: ''
			},
			// btns: [{
			// 	type: 'success',
			// 	txt: '确认',
			// 	// disabled: true,
			// 	click: this.submit,
			// }],
			colData: [{
				label: '用户账号',
				prop: 'employee_number',
			}, {
				label: '用户名称',
				prop: 'real_name',
			}, {
				label: '昵称',
				prop: 'nick_name',
			}, {
				label: '类型',
				prop: 'type',
				formatter: () => '员工',
			}, {
				label: '人员',
				prop: '_real_name_nick_name',
			}, {
				label: '业务员分组',
				prop: 'group_name',
			}],
		}
	},
	methods: {
		getSearch (){
			var apiUrl = '/afterSale-web/api/aftersale/order/choose/pickrecommendhandler';
			/* var apiUrl = '/order-web/api/mergetrade/getAnalysisHandler'; */

			// this.search.merge_trade_id = this.params.otherParams.mergeTradeId;

			this.ajax.postStream(apiUrl, this.search, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
					this.roleList = res.body.content.list.map(obj => {
						obj._real_name_nick_name = [obj.real_name, obj.nick_name].filter(Boolean).join('.')
						return obj
					})
					this.pageTotal = res.body.content.count
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		searchFun (text){
			this.search.field_value = text ? text : (this.search.field_value === this.params.best_staff_name ? this.params.best_staff_name : '')
			this.getSearch()
		},
		sizeChange(size){
			console.log('size',size)
			// 第页数改变
			this.search.page_size = size;
			this.getSearch();
		},
		pageChange (page_no){
			console.log('page_no',page_no)
			// 页数改变
			// this.pageNow = page_no;
			this.search.page_no = page_no;
			this.getSearch();
		},
		select (selectData){
			this.selectObject = selectData
		},
		submit (row){
			if(row) this.selectObject = row

			this.params.callback(this.selectObject)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
	},
	mounted (){
		// this.search.field_value = this.params.best_staff_name
		this.getSearch()
	}
}
</script>	