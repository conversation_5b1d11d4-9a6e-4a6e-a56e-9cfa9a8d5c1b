<!-- 责任分析单-责任范围选择 -->
<template>
    <div class="xpt-flex">

        <count-list
            style="overflow: inherit;"
            :data='roleList'
            :colData='colData'
            class="duty-person-list"
            selection="radio"
            :pageTotal='count'
            :btns="btns"
            :showHead="true"
            @search-click='searchClick'
            :searchPage="search.page_name"
            :pageLength="100"
            @radio-change='s => selectData = s'
            @row-dblclick="s => submit(s)"
            @page-size-change='pageChange'
            countUrl="/afterSale-web/api/afterQue/query/chooseCount"
            :extraCountUrlParams = {callSource:params.callSource}
            :showCount="showCount"
            :dynamic="true"
            row-key="id"
            @current-page-change='currentPageChange' ref='xptList'>
            <!-- <xpt-import slot='btns' :taskUrl="uploadUrl"  class='mgl10' :isupload="false"></xpt-import> -->
            <el-input size='mini' slot='btns' style="margin-left :100px"  v-model="searchValue" icon='search'
                            :on-icon-click="searching" placeholder="请输入关键词搜索" @keyup.native="makeSearch($event)" :disabled="false"></el-input>
        </count-list>
    </div>
    </template>
    <script>
    import countList from "@components/common/list-count";
    export default {
        components: {
            countList,
        },
        props:['params'],
        data (){
            let self = this;
            return {
                searchValue:'',
			    showCount: false,
                btns:[
                    {
                        type: 'success',
                        txt: '确定',
                        click: () => {self.submit(self.selectData);},
                        loading: false
                    },
                ],
                roleList:[],
                selectData: null,

                search: {
                    page_no: 1,
                    page_size: 100,
                    where: [],
                    type:'0',
                    page_name: 'aftersale_analysis_question_choose',
                },
                count:0,
                liability_scope_options: {
                    BD_Empinfo: '员工',
                    BD_Supplier: '供应商',
                    BD_Customer: '客户',
                    BD_Department: '部门',
                },
                judgmentSuggestionOptions: {
                    'JUDGMENT':'判责',
                    'SUGGESTION':'建议',
                    'PRINCIPAL':'主责',
                    'SECONDARY':'次责',
                },
                colData: [{
                    label: '故障原因',
                    prop: 'threeLevelName',
                    minWidth: 300,
                },{
                    label: '故障现象',
                    prop: 'secondLevelName',
                    width: 120,
                },{
                    label: '故障模块',
                    prop: 'firstLevelName',
                    width: 100,
                },{
                    label: '一级责任主体',
                    prop: 'liability_type',
                    width: 100,
                },{
                    label: '二级责任主体',
                    prop: 'second_liability_type',
                    width: 100,
                },{
                    label: '默认责任人',
                    prop: 'duty_default',
                    format: 'auxFormat',
                    formatParams: 'liability_duty_default',
                    width: 100,
                },{
                    label: '分摊规则',
                    prop: 'calculate_rule',
                    format: 'auxFormat',
                    formatParams: 'calculate_rule',
                    width: 100,
                },{
                    label: '责任问题描述',
                    prop: 'des',
                    width: 300,
                },{
                    label: '责任范围',
                    prop: 'liability_scope',
                    formatter: prop => (this.liability_scope_options[prop] || prop),
                    width: 100,
                },{
                    label: '主责/次责',
                    prop: 'judgment_suggestion',
                    formatter: prop => (this.judgmentSuggestionOptions[prop] || prop),
                    width: 100,
                }],
            }
        },
        created(){
            let plusCol = [
                {
                    label: "默认责任人",
                    prop: "duty_default",
                    format: 'auxFormat',
                    formatParams: 'liability_duty_default'
                },
                {
                    label: '分摊规则',
                    prop: 'calculate_rule',
                    format: 'auxFormat',
                    formatParams: 'calculate_rule'
                },
                {
                    label: '是否生成索赔服务单',
                    prop: 'if_create_dispute',
                    formatter: prop => ({
                        1: '是',
                        0: '否',
                    }[prop] || prop),
                },
                {
                    label: '主责/次责',
                    prop: 'judgment_suggestion',
                    formatter:(val)=>{
                        let status = {
                            'JUDGMENT':'判责',
                            'SUGGESTION':'建议',
                            'PRINCIPAL':'主责',
                            'SECONDARY':'次责',
                        }
                        return status[val] || val;
                    }
                },
            ]
            if (this.params.source == 'excel') {
                this.colData.push(...plusCol)
            }
        },
        methods: {
            makeSearch(e){
                if(e.keyCode == 13){
                    this.searching()
                }
            },
            searchClick(where,resolve){
                let self = this;
                this.search.where = where;
			new Promise((res,rej)=>{
				self.searchFun(resolve,res);
			}).then(()=>{
				if(this.search.page_no != 1){
					self.count = 0;
				}
				self.showCount = false;
			})
            },
            searching(){
               let self = this;
               this.search.type = '1';
                self.ajax.postStream('/afterSale-web/api/afterQue/query/chooseList', {
                    page_no: this.search.page_no,
                    callSource: this.params.callSource,
                    type:"1",
                    page_size: this.search.page_size,
                    page_name: this.search.page_name,
                    where: [{"condition":"OR","field":"629a43ec5251749e6ae59bd2bc7d821d","operator":"%","table":"********************************","value":this.searchValue}],
                }, res => {
                    if(res.body.result){
                        self.roleList = res.body.content.list
                        if(!self.showCount){
                            let total = this.search.page_no * this.search.page_size
                            self.count = res.body.content.list.length == (this.search.page_size+1)? total+1:total;

                        }
                    }else {
                        self.$message.error(res.body.msg||'查询失败')
                    }
                })
            },
            searchFun(resolve,callback){
                let self = this;
                this.search.type = '0';
                self.ajax.postStream('/afterSale-web/api/afterQue/query/chooseList', {
                    page_no: this.search.page_no,
                    callSource: this.params.callSource,
                    page_size: this.search.page_size,
                    page_name: this.search.page_name,
                    type:"0",
                    where: this.search.where,
                }, res => {
                    resolve&&resolve()
                    callback&&callback()
                    if(res.body.result){
                        self.roleList = res.body.content.list
                        if(!self.showCount){
                            let total = this.search.page_no * this.search.page_size
                            self.count = res.body.content.list.length == (this.search.page_size+1)? total+1:total;

                        }
                    }else {
                        self.$message.error(res.body.msg||'查询失败')
                    }
                })
            },
            pageChange(page_size){
                this.search.page_size = page_size
                this.searchFun()
            },
            currentPageChange(page){
                this.search.page_no = page
                this.searchFun()
            },
            submit (data){
                console.log(data);
                this.selectData = data;
                this.params.callback(this.selectData )
                this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
            },
        },
        mounted (){
            this.searchFun()
            console.log(this.$refs.xptList.$refs.list.$refs.dynamic_header.$refs.dynamic_searchbox.$refs.searchItem)
            this.$nextTick(()=>{
                setTimeout(()=>{
                    this.$refs.xptList.$refs.list.$refs.dynamic_header.$refs.dynamic_searchbox.$refs.searchItem.searchData.operator = "%";
                    this.$refs.xptList.$refs.list.$refs.dynamic_header.$refs.dynamic_searchbox.$refs.searchItem.searchData.table = "********************************";
                },300);
            });
        },
    }
    </script>
    <style>
    .duty-person-list .body{
        height: calc(100vh - 300px)!important;
    }
    </style>
