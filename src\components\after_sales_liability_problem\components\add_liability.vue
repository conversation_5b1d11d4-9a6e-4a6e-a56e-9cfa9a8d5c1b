<template>
    <div class="xpt-flex">
        <el-row	class='xpt-top'	:gutter='40'>
            <el-col :span='20'>
                <el-button type='primary' size='mini' @click="confirm">保存</el-button>
            </el-col>
        </el-row>
         <div v-if="params.parents.title">
            <div class="liability_title">
                <span class="liability_label"></span>{{params.parents.title}}
            </div>
            <div class="liability_content">
                <p  class="mrl6">
                    <el-input :value="params.parents.name" size="mini" :maxlength="getMaxLength()" disabled :class="isSave && !liabilityContent ? 'red_input' : ''"></el-input>
                </p>
            </div>
        </div>
        <div style="overflow: auto;">
            <div class="liability_title">
                <span class="liability_label">*</span>{{title}}
            </div>

            <div class="liability_content">

                <div v-for="(item,idx) in liabilityList" :key="idx" class="liability_item">
                    <p>
                        <el-input v-model="liabilityList[idx]" size="mini" :maxlength="getMaxLength()" :class="isSave && !item ? 'red_input' : ''" :style="isSave && !item ? 'border-color: #ff4949' : ''"></el-input>
                        <i class="el-icon-circle-cross delete_liability" @click="() => deleteLiability(idx)"></i>
                        <el-tooltip placement="right" popper-class="red_tooltip" content="请填写内容" v-show="isSave && !item">
                            <i class="el-icon-warning warning_liability"></i>
                        </el-tooltip>
                    </p>
                </div>
                <div><span @click="addNew" class="add_new">+</span></div>
            </div>

        </div>
    </div>
</template>

<script>
    import VL from '@common/validate.js'
    export default {
        props:["params"],
        data(){
            let self = this;
            return {
                alerts: [],
                liabilityList: [''],
                title: '',
                defaultList:[],
                isSave: false
            }
        },
        mounted() {
            this.title = this.params.title
            this.$parent.alerts.forEach(item => {
                this.alerts.push(item.params.alertId)
            })
            this.defaultList = this.getNeedApiDutyDefaultList();
            console.log(this.defaultList);
        },
        beforeDestroy(){
            if ((new Set(this.alerts).has(this.params.alertId))) {
                this.params._close()
                this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
            }
        },
        methods:{
          getMaxLength() {
            const reasonMaxItemList = __AUX.get('second_liability_type').filter( item => item.code === 'CKZR');
            let reasonItemValue = 100;
            if (this.params && this.params.parents.level === 'secondLevelName') {
              reasonItemValue = 1000;
            }
            return reasonItemValue
          },
            getNeedApiDutyDefaultList(){
                let list=__AUX.get("liability_duty_default").filter(item=>item.ext_field1==='AFTERSALE_ORDER')
                return list
            },
            strTrim(str) {
                return str.replace(/\s*/g,"");
            },
            deleteLiability(idx) {
                this.liabilityList.splice(idx, 1)
            },
            addNew(){
                this.liabilityList.push('')
            },
            confirm(){
                let liabilityList = this.liabilityList.map(item => this.strTrim(item))
                if (liabilityList.some(item => !item)) {
                    this.isSave = true
                    this.$message.error(`请填写${this.title}`)
                    return
                }
                let postData = {
                    que_id: this.params.queId,
                    parentId: this.params.parentId,
                    levelNameList: liabilityList,
                    currentVersion: this.params.currentVersion
                }, self = this
                this.ajax.postStream( '/afterSale-web/api/afterQue/action/saveLevel', postData,
                    (res) => {
                        if (res.body.result) {
                            self.params.callback(self.liabilityList)
                            self.$root.eventHandle.$emit('removeAlert', self.params.alertId)
                        } else {
                            res.body.msg ? self.$message.error(res.body.msg):self.$message.error('添加失败');

                        }
                        self.isSave = false
                    },
                    (err) => {
                        self.$message.error(err);
                        self.isSave = false
                    }
                );
            }
        }
    }
</script>
<style>
.red_tooltip {
    background: #ff4949 !important;
}
.red_tooltip .el-tooltip__popper .popper__arrow, .el-tooltip__popper .popper__arrow::after {
    border-right-color: #ff4949 !important;
    border-left-color: #ff4949 !important;
}
.red_input .el-input__inner{
    border-color: #ff4949  !important;
}
</style>
<style lang="stylus" scoped>
.liability_title {
    width: 100px;
    text-align: right;
    float: left;
    .liability_label {
        color: #ff4949;
        height: 24px;
        line-height: 24px;
        display: inline-block;
        margin-right: 5px;
    }
}
.liability_content {
    width: calc(100% - 110px);
    float: left;
    margin-left: 8px;
    .liability_item {
        margin-bottom: 6px;
        .delete_liability {
            cursor: pointer;
            margin-left: 5px;
        }
        .warning_liability {
            cursor: pointer;
            color: #ff4949;
        }

    }
    .add_new {
        background: #eee;
        padding: 3px 6px;
        cursor: pointer;
        display: inline-block;
        margin-left: 75px;
    }
}
</style>
