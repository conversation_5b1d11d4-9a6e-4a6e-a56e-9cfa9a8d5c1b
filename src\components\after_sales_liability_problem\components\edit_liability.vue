<template>
    <div class="xpt-flex">
        <el-row	class='xpt-top'	:gutter='40'>
            <el-col :span='20'>
                <el-button type='primary' size='mini' @click="confirm">保存</el-button>
            </el-col>
        </el-row>
        <div v-if="params.parents.title">
            <div class="liability_title">
                <span class="liability_label"></span>{{params.parents.title}}
            </div>
            <div class="liability_content">
                <p  class="mrl6">
                    <el-input :value="params.parents.name" size="mini" :maxlength="getMaxLength()" disabled :class="isSave && !liabilityContent ? 'red_input' : ''"></el-input>
                </p>
            </div>
        </div>
        <div>
            <div class="liability_title">
                <span class="liability_label">*</span>{{title}}
            </div>
            <div class="liability_content">
                <p  class="mrl6">
                    <el-input v-model="liabilityContent" size="mini" :maxlength="getMaxLength()" :class="isSave && !liabilityContent ? 'red_input' : ''"></el-input>
                </p>
            </div>
        </div>
    </div>
</template>

<script>
    import VL from '@common/validate.js'
    export default {
        props:["params"],
        data(){
            let self = this;
            return {
                alerts: [],
                liabilityContent: '',
                title: '',
                isSave: false
            }
        },
        mounted() {
            this.title = this.params.title
            this.liabilityContent = this.params.content
            this.$parent.alerts.forEach(item => {
                this.alerts.push(item.params.alertId)
            })
            console.log(this.params)
        },
        beforeDestroy(){
            if ((new Set(this.alerts).has(this.params.alertId))) {
                this.params._close()
                this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
            }
        },
        methods:{
          getMaxLength() {
            const reasonMaxItemList = __AUX.get('second_liability_type').filter( item => item.code === 'CKZR');
            let reasonItemValue = 100;
            if (this.params && this.params.level === 'threeLevelName') {
              reasonItemValue = 1000;
            }
            return reasonItemValue
          },
            strTrim(str) {
                return str.replace(/\s*/g,"");
            },
            confirm(){
                if (!this.strTrim(this.liabilityContent)) {
                    this.isSave = true
                    this.$message.error(`请填写${this.title}`)
                    return
                }
                console.log(this.params);
                let postData = {
                    que_id: this.params.queId,
                    id: this.params.rowId,
                    levelName: this.liabilityContent,
                    currentVersion: this.params.currentVersion
                }, self = this
                this.ajax.postStream( '/afterSale-web/api/afterQue/action/updateLevel', postData,
                    (res) => {
                        if (res.body.result) {
                            self.params.callback(self.liabilityContent)
                            self.$root.eventHandle.$emit('removeAlert', self.params.alertId)
                        } else {
                            res.body.msg && self.$message.error(res.body.msg);
                        }
                        self.isSave = false
                    },
                    (err) => {
                        self.$message.error(err);
                        self.isSave = false
                    }
                );
            }
        }
    }
</script>

<style lang="stylus" scoped>
.liability_title {
    width: 100px;
    text-align: right;
    float: left;
    .liability_label {
        color: red;
        height: 24px;
        line-height: 24px;
        display: inline-block;
        margin-right: 5px;
    }
}
.liability_content {
    width: calc(100% - 110px);
    float: left;
    margin-left: 8px;
    .mrl6 {
        margin-bottom: 6px;
        .red_input .el-input--mini .el-input__inner {
            border-color: red;
        }
    }
}
</style>
