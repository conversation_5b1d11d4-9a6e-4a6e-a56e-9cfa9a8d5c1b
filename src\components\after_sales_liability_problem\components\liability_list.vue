<!-- 责任明细 -->
<template>
    <!-- :pageTotal='count' -->
    <xpt-list
        :data='liabilityList'
        :btns='btns'
        :colData='cols'
        :orderNo='orderNo'
        :searchPage='""'
        :pageLength='searchData.pageSize'
        isNeedClickEvent
        :selection='selection'
        @page-size-change='sizeChange'
        @current-page-change='pageChange'
        @search-click='preSearch'
        @selection-change='select'
    >
        <template slot='out_liability_question' slot-scope='scope'>
            <!-- <xpt-input size='mini' v-model="menu.mapping_entity_shop_name" readonly icon='search' :on-icon-click="selectShop" @change='shopChange' :disabled="false"></xpt-input> -->
            <xpt-input type="text" icon="search" :on-icon-click="()=>{if((submitData.order_status == 'AUDIT')|| !scope.row.threeLevelId) return; selectQuestion(scope.row)}" v-model="scope.row.out_liability_question" readonly :disabled="(submitData.order_status == 'AUDIT')|| !scope.row.threeLevelId" size='mini' style="width:100%;" ></xpt-input>
        </template>
        <template slot='out_liability_type' slot-scope='scope'>
            <!-- <xpt-input size='mini' v-model="menu.mapping_entity_shop_name" readonly icon='search' :on-icon-click="selectShop" @change='shopChange' :disabled="false"></xpt-input> -->
            <xpt-input type="text" icon="search"  :on-icon-click="()=>{if((submitData.order_status == 'AUDIT')|| !scope.row.threeLevelId) return; selectQuestionPlate(scope.row)}" v-model="scope.row.out_liability_type" readonly :disabled="(submitData.order_status == 'AUDIT')|| !scope.row.threeLevelId" size='mini' style="width:100%;" ></xpt-input>
        </template>
        <template slot='duty_default' slot-scope='scope'>
                <el-select	size='mini' v-model="scope.row.duty_default" :disabled="(submitData.order_status == 'AUDIT')|| !scope.row.threeLevelId" style="width: 100%;" >
                    <el-option
                        v-for="item in scope.row.defaultOption"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
        </template>
        <template slot='liability_scope' slot-scope='scope'>
                <el-select  style="width: 100%;" size='mini' v-model="scope.row.liability_scope" :disabled="(submitData.order_status == 'AUDIT')|| !scope.row.threeLevelId" @change="()=>{
                        let option = [{label:'无需',value:'NONEED'}];
                         defaultOption.forEach((item)=>{

                            if (item.ext_field1 == 'AFTERSALE_ORDER'&&item.ext_field2 ==scope.row.liability_scope ) {
                                option.push({label: item.name, value: item.code})
                            }
                        })
                        $nextTick(()=>{
                            $set(scope.row,'defaultOption',option);
                        });

                    }">
                    <el-option
                        v-for="item in liability_scopeOption"
                        :key="item.value"
                        :label="item.label"
                        @click.native="()=>{
                            $nextTick(()=>{
                                $set(scope.row,'duty_default','NONEED');
                            });
                        }"
                        :value="item.value">
                    </el-option>
                </el-select>
        </template>
    </xpt-list>
</template>
<script>
export default {
    props: {
        questionId: {
            type: Number
        },
        submitData:{
            type: Object
        },
        queId: {
            type: Number
        },
        currentVersion: {
            type: Number
        },
    },
    data() {
        let self = this;
        return {
            selection: 'checkbox',
            orderNo: false,
            btns:[{
					type: 'success',
					txt: '新增',
					click: () => self.addLiability('first'),
					disabled: !self.queId
				}, {
					type: 'warning',
					txt: '禁用/启用',
					click: self.disableOrEnable,
					disabled: self.canDisableOrEnable
				}, {
					type: 'primary',
					txt: '显示所有',
					click: self.showAll,
					disabled: self.canShowAll
				}, {
					type: 'primary',
					txt: '保存明细',
					click: self.saveLiability,
					disabled: self.canSaveLiability
				},],
            isSHowAll:false,
            cols: [
                {
                    label: '故障模块',
                    prop: 'firstLevelName',
                    width: 140,
                    rHeader: true,
                    renderHeader: (h, {column, $index}) => {
                        return h('div',
                            [
                                h('i', {style: 'color:red;margin-left:5px;'}, '*'),
                                h('span', column.label),
                                h('el-tooltip', { props: { placement: 'bottom' } }, [
                                    h(
                                        'div',
                                        {
                                        slot: 'content',
                                        style: { width: '100px', whiteSpace: 'normal' }
                                        },
                                        '编辑故障模块'
                                    ),
                                    h('i',
                                        {
                                            class: 'el-icon-edit',
                                            style: 'color:black;margin-left:10px;cursor:pointer;',
                                            on: {click: () => self.handleEditLiability('first')}
                                        }
                                    )
                                ]),
                                h('el-tooltip', { props: { placement: 'bottom' } }, [
                                    h(
                                        'div',
                                        {
                                        slot: 'content',
                                        style: { width: '100px', whiteSpace: 'normal' }
                                        },
                                        '新增故障现象'
                                    ),
                                    h('i',
                                        {
                                            class: 'el-icon-plus',
                                            style: 'color:black;margin-left:10px;cursor:pointer;',
                                            on: {click: () => self.addLiability('second')}
                                        }
                                    )
                                ])
                            ]
                        )
                    }
                }, {
                    label: '故障现象',
                    prop: 'secondLevelName',
                    width: 140,
                    rHeader: true,
                    renderHeader: (h, {column, $index}) => {
                        return h('div',
                            [
                                h('i', {style: 'color:red;margin-left:5px;'}, '*'),
                                h('span', column.label),
                                h('el-tooltip', { props: { placement: 'bottom' } }, [
                                    h(
                                        'div',
                                        {
                                        slot: 'content',
                                        style: { width: '100px', whiteSpace: 'normal' }
                                        },
                                        '编辑故障现象'
                                    ),
                                    h('i',
                                        {
                                            class: 'el-icon-edit',
                                            style: 'color:black;margin-left:10px;cursor:pointer;',
                                            on: {click: () => self.handleEditLiability('second')}
                                        }
                                    ),
                                ]),
                                h('el-tooltip', { props: { placement: 'bottom' } }, [
                                    h(
                                        'div',
                                        {
                                        slot: 'content',
                                        style: { width: '100px', whiteSpace: 'normal' }
                                        },
                                        '新增故障原因'
                                    ),
                                    h('i',
                                        {
                                            class: 'el-icon-plus',
                                            style: 'color:black;margin-left:10px;cursor:pointer;',
                                            on: {click: () => self.addLiability('third')}
                                        }
                                    )
                                ]),
                            ]
                        )
                    }
                }, {
                    label: '故障原因',
                    prop: 'threeLevelName',
                    rHeader: true,
                    width: 140,
                    renderHeader: (h, {column, $index}) => {
                        return h('div',
                            [
                                h('i', {style: 'color:red;margin-left:5px;'}, '*'),
                                h('span', column.label),
                                h('el-tooltip', { props: { placement: 'bottom' } }, [
                                    h(
                                        'div',
                                        {
                                        slot: 'content',
                                        style: { width: '100px', whiteSpace: 'normal' }
                                        },
                                        '编辑故障原因'
                                    ),
                                    h('i',
                                        {
                                            class: 'el-icon-edit',
                                            style: 'color:black;margin-left:10px;cursor:pointer;',
                                            on: {click: () =>self.handleEditLiability('third')}
                                        }
                                    )
                                ])
                            ]
                        )
                    }
                }, {
                    label: '责任类型（旧版）',
                    slot: 'out_liability_type',
					width:180,
                },  {
                    label: '责任问题（旧版）',
                    slot: 'out_liability_question',
					width:180,
                }, {
                    label: '责任范围',
                    slot: 'liability_scope',
                    rHeader: true,
                    renderHeader: (h, {column, $index}) => {
                        return h('div',
                            [
                                h('i', {style: 'color:red;margin-left:5px;'}, '*'),
                                h('span', column.label)
                            ]
                        )
                    },
                }, {
                    label: '分摊规则',
                    prop: 'calculate_rule',
                    rHeader: true,
                    renderHeader: (h, {column, $index}) => {
                        return h('div',
                            [
                                h('i', {style: 'color:red;margin-left:5px;'}, '*'),
                                h('span', column.label)
                            ]
                        )
                    },
                    bool: true,
                    elSelect: true,
                    options: __AUX.getValidData('calculate_rule').reduce((arr, item)=>{
                        arr.push({label: item.name, value: item.code})
                        return arr
                    },[]),
                    disabled(row) {
                        return (self.submitData.order_status == 'AUDIT')|| !row.threeLevelId
                    },
                }, {
                    label: '默认责任人',
                    slot: 'duty_default',
                }, {
                    label: '是否生成索赔服务单',
                    prop: 'if_create_dispute',
                    width: 140,
                    bool: true,
                    elSelect: true,
                    options:[
                        { label: '是', value:1},
                        { label: '否', value:0},

                    ],
                    disabled(row) {
                        return (self.submitData.order_status == 'AUDIT')|| !row.threeLevelId
                    },
                },{
                    label: '是否生成绩效单',
                    prop: 'if_generate_per_sheet',
                    width: 140,
                    bool: true,
                    elSelect: true,
                    options:[
                        { label: '是', value:'Y'},
                        { label: '否', value:'N'},

                    ],
                    disabled(row) {
                        return (self.submitData.order_status == 'AUDIT')|| !row.threeLevelId
                    },
                },  {
                    label: '主责/次责',
                    prop: 'judgment_suggestion',
                    bool: true,
                    elSelect: true,
                    options:[
                        { label: '判责', value:'JUDGMENT', disabled:true},
                        { label: '建议', value:'SUGGESTION', disabled:true},
                        { label: '主责', value:'PRINCIPAL'},
                        { label: '次责', value:'SECONDARY'},
                    ],
                    disabled(row) {
                        return (self.submitData.order_status == "AUDIT")|| !row.threeLevelId
                    },
                }, {
                    label: '是否禁用',
                    prop: 'forbidden_status',
                    bool: true,
                    elSelect: true,
                    options:[
                        { label: '是', value:0},
                        { label: '否', value:1},

                    ],
                    disabled(row) {
                        return true
                    },
                }, {
                    label: '禁用人',
                    prop: 'forbidden_person_name'
                }, {
                    label: '禁用时间',
                    prop: 'forbidden_date',
                    format: 'dataFormat1'
                }
            ],
            liability_scopeOption:[
                { label: '员工', value:'BD_Empinfo'},
                { label: '供应商', value:'BD_Supplier'},
                { label: '客户', value:'BD_Customer'},
                { label: '部门', value:'BD_Department'},
            ],
            liabilityList: [],
            searchData: {
                page_name: "scm_sale_one_plan",
                where: [],
                page_no: 1,
                page_size: self.pageSize
            },
            count: 20,
            selectList: [],
            status: 'SAVE',
            defaultOption:[],
            oldLiabilityList: []
        }
    },
    computed: {
        canAddLiability: () => {
            return this.queId
        },
        canShowAll: () => {
            return this.queId
        },
        canDisableOrEnable: () => {
            let hasToBeSavedData = this.liabilityList.some(item => !item.id)
            console.log('!this.queId', this.queId, hasToBeSavedData)
            return this.queId ||  hasToBeSavedData
        },
        canSaveLiability: () => {
            return this.queId
        },
    },
    methods: {
        getSaveData() {
            let add = [], update = [],self= this;
            this.selectList.forEach(item => {
                if (item.id) {
                    let selectIdx = this.searchIdxOfSelect(item, this.oldLiabilityList)
                    let oldItem = self.oldLiabilityList[selectIdx]
                    let ifUpdate = this.compareData({
                            "out_liability_question": item.out_liability_question,
                            "out_liability_question_id": item.out_liability_question_id,
                            "liability_scope": item.liability_scope,
                            "calculate_rule": item.calculate_rule,
                            "duty_default": item.duty_default,
                            "if_create_dispute": item.if_create_dispute,
                            "judgment_suggestion": item.judgment_suggestion,
                            "out_liability_type": item.out_liability_type,
                            "if_generate_per_sheet": item.if_generate_per_sheet,

                        }, {
                            "out_liability_question": oldItem.out_liability_question,
                            "out_liability_question_id": oldItem.out_liability_question_id,
                            "liability_scope": oldItem.liability_scope,
                            "calculate_rule": oldItem.calculate_rule,
                            "duty_default": oldItem.duty_default,
                            "if_create_dispute": oldItem.if_create_dispute,
                            "judgment_suggestion": oldItem.judgment_suggestion,
                            "out_liability_type": oldItem.out_liability_type,
                            "if_generate_per_sheet": oldItem.if_generate_per_sheet,
                        }
                    )
                    if (ifUpdate) {
                        update.push({
                            "id": item.id,
                            "last_level_id": item.threeLevelId,
                            "out_liability_question": item.out_liability_question,
                            "out_liability_question_id": item.out_liability_question_id,
                            "liability_scope": item.liability_scope,
                            "calculate_rule": item.calculate_rule,
                            "duty_default": item.duty_default,
                            "if_create_dispute": item.if_create_dispute,
                            "judgment_suggestion": item.judgment_suggestion,
                            "out_liability_type": item.out_liability_type,
                            "if_generate_per_sheet": item.if_generate_per_sheet,
                            "out_liability_id": item.out_liability_id,
                            "forbidden_status": item.forbidden_status
                        })
                    }
                } else {
                    add.push({
                        "last_level_id": item.threeLevelId,
                        "out_liability_question": item.out_liability_question,
                        "out_liability_question_id": item.out_liability_question_id,
                        "liability_scope": item.liability_scope,
                        "calculate_rule": item.calculate_rule,
                        "duty_default": item.duty_default,
                        "if_create_dispute": item.if_create_dispute,
                        "judgment_suggestion": item.judgment_suggestion,
                            "out_liability_type": item.out_liability_type,
                        "if_generate_per_sheet": item.if_generate_per_sheet,
                        "out_liability_id": item.out_liability_id,
                        "forbidden_status": item.forbidden_status ? item.forbidden_status : 1
                    })
                }
            })
            return {
                add, update
            }
        },
        saveLiability() {
            if (this.selectList.length < 1) {
                this.$message.error('请至少选择一行责任明细')
                return false
            }
            let flag = false;
            this.selectList.forEach(item => {
                if(!item.calculate_rule ||!item.duty_default ||(!item.if_create_dispute&&item.if_create_dispute!==0) ||!item.judgment_suggestion){
                    flag = true;
                }
            })
            if(flag){
                this.$message.error('存在没有填写的项');
                return;
            }
            let postData = {
                que_id: this.queId,
                addList: this.getSaveData().add,
                updateList: this.getSaveData().update,
                currentVersion: this.currentVersion
            }, self = this
            this.ajax.postStream( '/afterSale-web/api/afterQue/action/saveThreeLevelDetail', postData,
                (res) => {
                    if (res.body.result) {
                        self.$emit('initDetail')
                        self.$message.success(res.body.msg);
                    } else {
                        res.body.msg && self.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
        },
        questionChange(row){
            row.out_liability_question = '';
            row.out_liability_question_id ='';
        },
        selectQuestionPlate(row) {
            let params = {}, self = this
            params.callback = d => {
                if(d) {
                    row.out_liability_type = d.liability_type;
                    row.out_liability_id = d.id;
                }
            }
            self.$root.eventHandle.$emit('alert', {
                params: params,
                component: () => import('@components/after_sales_liability_problem/components/select_liability.vue'),
                style: 'width:800px;height:500px',
                title: '责任问题列表'
            });
        },
        selectQuestion(row){
            if(!row.out_liability_id){
                this.$message.error('请选择责任类型(旧版)')
                return false;
            }
            let params = {
                id: row.out_liability_id
            }, self = this
            params.callback = d => {
                let idx = self.searchIdxOfSelect(row, self.liabilityList)
                if(d) {
                    row.out_liability_question = d.liability_question;
                    row.out_liability_question_id = d.id;
                    // self.$set(self.liabilityList[idx], 'out_liability_question', d.liability_question)
                    // self.$set(self.liabilityList[idx], 'out_liability_question_id', d.id)
                    // console.log('选择责任问题', d, row, idx, self.liabilityList)
                }
            }
            self.$root.eventHandle.$emit('alert', {
                params: params,
                component: () => import('@components/after_sales_liability_problem/components/select_liability_of_detail.vue'),
                style: 'width:800px;height:500px',
                title: '责任问题(外部)'
            });
        },
        updateLiability(){
            this.liabilityList.map(item => {
                item.out_liability_question = ''
                item.out_liability_question_id = ''
            })
        },
        searchIdxOfSelect(select, arr) {
            let index = ''
            arr.forEach((item, idx) => {
                if (item.id === select.id) {
                    index = idx
                }
            })
            return index
        },
        handleEditLiability(level){
            let self = this;
            if(this.submitData.order_status == "AUDIT"){
                return;
            }
            console.log(this.selectList.length)
            if (!(this.selectList.length === 1)) {
                this.$message.error('请选择一行责任明细')
                return
            }
            if(level == 'third' && !self.selectList[0].secondLevelId){
                self.$message.error('请添加父级问题');
                return
            }
            if(level == 'third' && !self.selectList[0].threeLevelId){
                self.$message.error('请添加三级问题');
                return
            }
            if(level == 'second' && !self.selectList[0].secondLevelId){
                self.$message.error('请添加二级问题');
                return
            }
            let levelMap = {
                 'first': {key: 'firstLevelName', title: '故障模块', id: 'firstLevelId',parent:{title:'',name:''}},
                'second': {key: 'secondLevelName', title: '故障现象', id: 'secondLevelId',parent:{title:'故障模块',name:'firstLevelName'}},
                'third': {key: 'threeLevelName', title: '故障原因', id: 'threeLevelId',parent:{title:'故障现象',name:'secondLevelName'}},
            }
            this.$root.eventHandle.$emit("alert", {
                params: {
                    title: `${levelMap[level].title}`,
                    content: self.selectList[0][levelMap[level].key],
                    rowId: self.selectList[0][levelMap[level].id],
                    level: levelMap[level].key,
                    parents:{
                        title:levelMap[level].parent.title,
                        name:self.selectList[0][levelMap[level].parent.name],
                        level:levelMap[level].parent.name
                    },
                    queId: self.queId,
                    currentVersion: self.currentVersion,
                    callback: (content) => {
                        // let selectIdx = this.searchIdxOfSelect(self.selectList[0], self.liabilityList)
                        // self.liabilityList[selectIdx][levelMap[level].key] = content;
                        self.$emit('initDetail')                    },
                    _close: () => {}
                },
                component: () =>
                    import(
                        "@components/after_sales_liability_problem/components/edit_liability.vue"
                    ),
                style: "width:500px;height:300px",
                title: `编辑${levelMap[level].title}`,
            });
        },
        addLiability(level){
            let self = this;
            let levelMap = {
                'first': {title: '故障模块', parentId: 0, id: 'firstLevelId',parent:{title:'',name:''}},
                'second': {title: '故障现象', parentId: 'firstLevelId',parent:{title:'故障模块',name:'firstLevelName'}},
                'third': {title: '故障原因', parentId: 'secondLevelId',parent:{title:'故障现象',name:'secondLevelName'}},
            }

            if (!((/^(second|third)$/.test(level) && this.selectList.length == 1)||/^(first)$/.test(level))) {
                this.$message.error('请选择一行责任明细')
                return false
            }
            if(level == 'second' && !self.selectList[0].firstLevelId){
                self.$message.error('请添加父级问题');
                return
            }
            if(level == 'third' && !self.selectList[0].secondLevelId){
                self.$message.error('请添加父级问题');
                return
            }
            this.$root.eventHandle.$emit("alert", {
                params: {
                    parentId: level == 'first' ? levelMap[level].parentId : self.selectList[0][levelMap[level].parentId],
                    queId: self.queId,
                    currentVersion: self.currentVersion,
                    parents:{
                        title:levelMap[level].parent.title,
                        name:level == 'first'?'':self.selectList[0][levelMap[level].parent.name],
                        level:levelMap[level].parent.name
                    },
                    title: `${levelMap[level].title}`,
                    callback: (list) => {
                        self.$emit('initDetail')
                    },
                    _close: () => {}
                },
                component: () =>
                    import(
                        "@components/after_sales_liability_problem/components/add_liability.vue"
                    ),
                style: "width:500px;height:300px",
                title: `新增${levelMap[level].title}`,
            });
        },
        disableOrEnable(){
            console.log('disableOrEnable')
            let status = this.selectList[0].forbidden_status, self = this
            if (!this.selectList.every(item => item.forbidden_status === status)) {
                this.$message.error('请选择状态相同的数据')
                return
            }
            let postData = {
                ids: this.selectList.reduce((arr, ele) => {
                    arr.push(ele.id)
                    return arr
                }, []),
                type: status === 1 ? 'DISABLE' : 'ENABLE',
                que_id: this.queId,
                currentVersion: this.currentVersion
            }
            this.ajax.postStream( '/afterSale-web/api/afterQue/action/enableOrDisable', postData,
                (res) => {
                    if (res.body.result) {
                        res.body.msg && self.$message.success(res.body.msg);
                        self.$emit('initDetail')
                    } else {
                        res.body.msg && self.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
        },
        showAll(){
            console.log(this.isSHowAll)
            this.getList({},this.isSHowAll)
            if(!this.isSHowAll){
                this.btns[2].txt = '显示所有'
            }else{
                this.btns[2].txt = '过滤'
            }


        },
        select(list, a , b) { console.log('aaaaaa', a, b)
            this.selectList = list
        },
        sizeChange(size) {
            this.searchData.page_size = size
            this.getList()
        },
        pageChange(page) {
            this.searchData.page_no = page
            this.getList()
        },
        preSearch(where, resolve) {
            this.searchData.where = where
            this.getList(resolve)
        },
        getList(resolve,isAll) {
            let self = this;
            this.ajax.postStream('/afterSale-web/api/afterQue/query/getOtherDetail', {
            id: this.queId,
            showType: isAll?"ALL":"ENABLE"
            }, d => {
                if (d.body.result) {
                    this.liabilityList = d.body.content || []
                    this.oldLiabilityList = JSON.parse(JSON.stringify(d.body.content))
                    this.liabilityList.forEach(row=>{
                        if(row.liability_scope){
                            let option = [{label:'无需',value:'NONEED'}];
                            self.defaultOption.forEach((item)=>{

                                if (item.ext_field1 == 'AFTERSALE_ORDER'&&item.ext_field2 ==row.liability_scope ) {
                                    option.push({label: item.name, value: item.code})
                                }
                            })
                            row.defaultOption = option;
                        }
                        if(!row.threeLevelId){
                            row.duty_default = 'NONEED';
                            row.defaultOption = [{label:'无需',value:'NONEED'}];
                        }
                    })
                    this.count = d.body.content.count;
                    this.isSHowAll = !isAll?true:false
                } else {
                    this.liabilityList = []
                    this.count = 0
                    this.$message.error(d.body.msg || '');
                }
            }, err => {
                this.$message.error(err);
            })
        }
    },
    mounted () {
        // if (this.queId) {
        //     this.getList();
        // }
        console.log(111111111);
        let self = this;
        self.defaultOption = __AUX.get('liability_duty_default');
        // self.btns[2].txt = '12354';

    },
}
</script>
