<template>
  <xpt-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :pageTotal="count"
    :searchPage="searchData.page_name"
    :pageLength="searchData.page_size"
    isNeedClickEvent
    selection="radio"
    @search-click="presearch"
    @radio-change="handleRadioChange"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
  ></xpt-list>
</template>
  <script>
import Fn from "@common/Fn.js";
export default {
  props: ["data", "params"],
  data() {
    let self = this;
    return {
      btns: [
        {
          type: "success",
          txt: "确认",
          loading: false,
          click() {
            self.confirm();
          },
        },
      ],
      cols: [
        {
          label: "编码",
          prop: "code",
        },
        {
          label: "责任类型",
          prop: "liability_type",
        },
        {
          label: "责任范围",
          prop: "liability_scope",
          formatter(val) {
            switch (val) {
              case "BD_Empinfo":
                return "员工";
              case "BD_Supplier":
                return "供应商";
              case "BD_Customer":
                return "客户";
              case "BD_Department":
                return "部门";
              default:
                return val;
            }
          },
        },

        {
          label: "是否基金",
          prop: "if_foundation",
          formatter(val) {
            switch (val) {
              case 0:
                return "否";
              case 1:
                return "是";
              default:
                return val;
            }
          },
        },

        {
          label: "是否全额",
          prop: "if_full_deduct",
          formatter(val) {
            switch (val) {
              case 0:
                return "否";
              case 1:
                return "是";
              default:
                return val;
            }
          },
        },
        {
          label: "状态",
          prop: "status",
          formatter(val) {
            switch (val) {
              case "create":
                return "保存";
              case "audit":
                return "审核";
              default:
                return val;
            }
          },
        },
      ],
      dataList: [],
      selectData: "",
      searchData: {
        page_name: "aftersale_analysis_k3_liability_type",
        "useType":"AFTERSALE_ORDER",
        where: [],
        page_no: 1,
        page_size: 50,
      },
      count: 20,
    };
  },
  methods: {
    handleRadioChange(data) {
      this.selectData = data;
    },
    sizeChange(size) {
      this.searchData.page_size = size;
      this.getList();
    },
    pageChange(page) {
      this.searchData.page_no = page;
      this.getList();
    },
    getList(resolve) {
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/analysis/type/list",
        this.searchData,
        (d) => {
          if (d.body.result) {
            this.dataList = d.body.content.list || [];
            this.count = d.body.content.count;
          } else {
            this.dataList = [];
            this.count = 0;
            this.$message.error(d.body.msg || "");
          }
          resolve && resolve();
        },
        (err) => {
          this.$message.error(err);
          resolve && resolve();
        }
      );
    },

    presearch(list, resolve) {
      this.searchData.where = list;
      this.getList(resolve);
    },
    confirm() {
      if (this.selectData == '') {
        this.$message.error('请选择一项责任类型')
        return
      }
      if(this.selectData.status == 'create'){
        this.$message.error('请选择已审核的责任类型')
        return
      }
      this.params.callback(this.selectData)
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
  },
  mounted: function () {
    this.getList();
    this.$root.eventHandle.$on("refresh_invoice", (d) => {
      this.getList();
    });
  },
  destroyed() {
    this.$root.offEvents("refresh_invoice");
  },
};
</script>
  