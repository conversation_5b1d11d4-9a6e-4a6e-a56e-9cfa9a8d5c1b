<template>
    <xpt-list
      :data="dataList"
      :btns="btns"
      :colData="cols"
      isNeedClickEvent
      selection="radio"
      @radio-change="handleRadioChange"
    ></xpt-list>
  </template>
    <script>
  import Fn from "@common/Fn.js";
  export default {
    props: ["data", "params"],
    data() {
      let self = this;
      return {
        btns: [
            {
                type: "success",
                txt: "确认",
                loading: false,
                click() {
                    self.confirm();
                },
            },
        ],
        cols: [
          {
            label: "责任问题",
            prop: "liability_question",
          },
          {
            label: "是否生成索赔服务单",
            prop: "if_create_dispute",
            formatter: prop => ({
                1: '是',
                0: '否',
            }[prop] || prop),
          },
          {
            label: "是否品质反馈问题类型",
            prop: "quality_feedback_question_type",
            formatter: prop => ({
                'Y': '是',
                'N': '否',
            }[prop] || prop),
          },
  
          {
            label: "默认责任人", 
            prop: "duty_default",
            format: 'auxFormat',
            formatParams: 'liability_duty_default'
          },
  
          {
            label: "是否禁用",
            prop: "forbidden_status",
            formatter(val) {
              switch (val) {
                case 0:
                  return "否";
                case 1:
                  return "是";
                default:
                  return val;
              }
            },
          },
          {
            label: "禁用人",
            prop: "forbidden_person_name"
          },
          {
            label: '禁用日期',
            prop: "forbidden_date",
            format:'dataFormat1'
          }
        ],
        dataList: [],
        selectData: "",
      };
    },
    methods: {
      handleRadioChange(data) {
        this.selectData = data;
      },
      getList(resolve) {
        this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/getDetail', {id: this.params.id}, d => {
            if (d.body.result) {
                this.dataList = d.body.content.questionList || [];
            } else {
                this.dataList = []
                this.$message.error(d.body.msg)
            }
        }, err=> {
            this.dataList = []
            this.$message.error(err)
        })
      },
      confirm() {
        if (this.selectData == '') {
            this.$message.error('请选择一项责任问题')
            return
        }
        if (this.selectData.forbidden_status == 1) {
            this.$message.error('请选非禁用责任问题')
            return
        }
        this.params.callback(this.selectData)
        this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
      }
    },
    mounted: function () {
      this.getList();
    },
  };
  </script>
    