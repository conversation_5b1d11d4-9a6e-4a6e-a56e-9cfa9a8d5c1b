<!-- 售后责任问题详情-->
<template>
    <div class="xpt-flex produce_consulting_detail">
        <div class="xpt-top ">
            <el-button
                type="success"
                size="mini"
                :loading="false"
                @click="initDetail"
                :disabled="ifAdd"
                >刷新</el-button
            >
            <el-button
                type="success"
                size="mini"
                @click="preSave"
                >保存</el-button
            >
            <el-button
                type="primary"
                size="mini"
                @click="() => auditOrDeAudit('AUDIT')"
                >审核</el-button
            >
            <el-button
                type="warning"
                size="mini"
                @click="() => auditOrDeAudit('DEAUDIT')"
                >反审核</el-button
            >
        </div>
        <div>
            <el-form
                :model="submitData"
                :rules="rules"
                ref="submitData"
                class="detail-form"
                label-position="right"
                label-width="130px"
            >
                <el-tabs v-model="firstTab" style="height: 160px">
                    <el-tab-pane label="基础信息" name="basicInfo">
                        <el-row :gutter="40" class="liability-detail">
                            <el-col :span="9">
                                <el-form-item
                                    label="编号"
                                    prop="liability_code"
                                >
                                    <el-input
                                        v-model="submitData.liability_code"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="一级责任主体"
                                    prop="liability_type"
                                >
                                <el-input
                                    :value="submitData.liability_type"
                                    v-model="submitData.liability_type"
                                    size="mini"
                                    :disabled="submitData.order_status == 'AUDIT'"
                                    :maxlength="50"
                                ></el-input>
                                <el-tooltip v-if='rules.liability_type[0].isShow' class="item" effect="dark"
                                    :content="rules.liability_type[0].message" placement="right-start"
                                    popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item
                                    label="状态"
                                    prop="order_status"
                                >
                                <el-input
                                    :value="orderStatus[submitData.order_status]"
                                    size="mini"
                                    disabled
                                ></el-input>
                                </el-form-item>
                                <!-- <el-form-item
                                    label="二级责任主体"
                                    prop="out_liability_type"
                                >
                                <el-input size='mini' v-model="submitData.out_liability_type" readonly icon='search' :on-icon-click="selectQuestionPlate" :disabled="submitData.order_status == 'AUDIT'"></el-input>
                                </el-form-item> -->
                                 <el-form-item
                                    label="二级责任主体"
                                    prop="second_liability_type_code"
                                >
                                <xpt-select-aux
								v-model="submitData.second_liability_type_code"
								aux_name='second_liability_type'
                                @change="secondLiabilityChange"
								:disabled="submitData.order_status == 'AUDIT'"
							></xpt-select-aux>
                                </el-form-item>
                            </el-col>
                            <el-col :span="1">
                            <el-form-item></el-form-item>
                            <el-form-item
                                    label="标签用途"
                                    prop="aftersale_label_usage"
                                >
                                <xpt-select-aux
								v-model="submitData.aftersale_label_usage"
								aux_name='aftersale_label_usage'
								:disabled="submitData.order_status == 'AUDIT'"
							></xpt-select-aux>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item
                                    label="描述"
                                    prop="des"
                                >
                                    <el-input
                                        v-model="submitData.des"
                                        size="mini"
                                        type="textarea"
                                        :autosize="{ minRows: 2, maxRows: 2 }"
                                        :disabled="submitData.order_status == 'AUDIT'"
                                        :maxlength="300"
                                    ></el-input>
                                    <el-tooltip v-if='rules.des[0].isShow' class="item" effect="dark"
                                    :content="rules.des[0].message" placement="right-start"
                                    popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                 <el-form-item
                                    label=""
                                    prop="des"
                                >
                                  
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="其它信息" name="otherInfo">
                        <xpt-form :data='submitData' :cols='otherCols' label='120px'></xpt-form>
                    </el-tab-pane>
                </el-tabs>
               
                
            </el-form>
        </div>
         <el-row  class="xpt-flex__bottom">
                    <el-tabs v-model="secondTab">
                    <el-tab-pane
                        label="责任明细"
                        name="liabilityInfo"
                        class="liability_list"
                    >
                        <liability-list
                            ref="liabilityList"
                            class="liability-list"
                            :questionId="submitData.queId"
                            :submitData="submitData"
                            :queId="params.id"
                            :currentVersion="currentVersion"
                            @initDetail="initDetail"
                        ></liability-list>
                    </el-tab-pane>
                </el-tabs>
            </el-row>
    </div>
</template>
<script>
import VL from '@common/validate.js'
import liabilityList from "@components/after_sales_liability_problem/components/liability_list.vue";
import other from "./model/other.js";
export default {
    props: ["params"],
    mixins: [other],
    components: {
        liabilityList,
    },
    data() {
        let self = this;
        return {
            firstTab: "basicInfo",
            secondTab: "liabilityInfo",
            submitData: {
                aftersale_label_usage:'',
                second_liability_type_code: '',
                out_liability_id: 0,
                liability_code: '',
                order_status: '',
                liability_type: '',
                des: '',
                queId: ''
            },
            rules: {
                des:[
                    {
                        required: false,
                        message: '描述字数不能超过300字',
                        isShow: false,
                        trigger: 'blur',
                        validator: function(rule,value,callback){
                            // 数据校验
                            if(value.length<300){
                                self.rules[rule.field][0].isShow = false;
                                // 校验成功
                                callback();
                            }else{
                                self.rules[rule.field][0].isShow = true
                                // 校验失败
                                callback(new Error(''));
                            }
                        }
                    }
                ],
                liability_type: [
                    {
                        required: true,
                        message: '请填写【一级责任主体】',
                        isShow: false,
                        trigger: 'blur',
                        validator: function(rule,value,callback){
                            // 数据校验
                            console.log('cesj', value, self, self.strTrim(value))
                            if(self.strTrim(value)){
                                self.rules[rule.field][0].isShow = false;
                                // 校验成功
                                callback();
                            }else{
                                self.rules[rule.field][0].isShow = true
                                // 校验失败
                                callback(new Error(''));
                            }
                        }
                    }
                     
                ]
            },
            isRequest: false,
            ifAdd: false,
            orderStatus: {
                'CREATE': '保存',
                'REAUDIT': '反审核',
                'AUDIT': '审核'
            },
            employeeId: this.getEmployeeInfo('id'),
            currentVersion: 0
        };
    },
    mounted() {
        let self = this;
        this.ifAdd = !this.params.id
        if (this.params.id) {
            this.initDetail();
        }
    },
    created () {
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
		this.ifPurchaseDealerOfProxyId()
	},
	beforeDestroy(){
        // 解除监听切换业务代理事件
		this.$root.eventHandle.$off('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
	},
    methods: {
        secondLiabilityChange(){
            let self = this;
            __AUX.get('second_liability_type').forEach( item => {
                if(item.code == this.submitData.second_liability_type_code ){
                    this.submitData.second_liability_type = item.name;
                }
            });
        },
        
        strTrim(str) {
            return str.replace(/\s*/g,"");
        },
        preSave(resolve){
            let self = this
            this.$refs.submitData.validate((valid) => {
                if(!valid) return
                let postData = {
                    liability_type: this.strTrim(this.submitData.liability_type),
                    second_liability_type_code: this.submitData.second_liability_type_code,
                    second_liability_type: this.submitData.second_liability_type,
                    data_type: "Y",
                    out_liability_id: this.submitData.out_liability_id,
                    des: this.submitData.des,
                    aftersale_label_usage:this.submitData.aftersale_label_usage
                }
                if (this.submitData.id) {
                    postData.currentVersion = this.currentVersion
                    postData.id = this.submitData.id
                }
                let url = this.submitData.id ? '/afterSale-web/api/afterQue/action/updateQuetionMainBody' : '/afterSale-web/api/afterQue/action/savePriConsultion'
                this.ajax.postStream(
                    url,
                    postData,
                    (res) => {
                        if (res.body.result) {
                            this.$message.success('保存成功');
                            if (!self.params.id) {
                                let params = {
                                    id:res.body.content,
                                    __close: ()=>{
                                        self.params.__close = self.$root.eventHandle.$emit('removeTab',self.params.tabName)
                                    }
                                }
                                self.$root.eventHandle.$emit('updateTab',{
                                    name:self.params.tabName,
                                    params:params,
                                    title:'售后责任问题详情',
                                    component: () => import('@components/after_sales_liability_problem/detail.vue')
                                })
                            }else{
                                this.initDetail();
                            }
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        // reject();
                    }
                );
            })
        },
        auditOrDeAudit(operateType){
            if (!(/^(CREATE)$/.test(this.submitData.order_status))&&operateType == 'AUDIT') {
                this.$message.error('当前单据状态不为【保存】')
                return
            }
            if (!(/^(AUDIT)$/.test(this.submitData.order_status))&&operateType == 'DEAUDIT') {
                this.$message.error('当前单据状态不为【审核】')
                return
            }
            let self = this

            this.ajax.postStream( '/afterSale-web/api/afterQue/action/auditOrDeAudit', {ids: [this.params.id], type: operateType},
                (res) => {
                    if (res.body.result) {
                        self.$message.success(res.body.msg);
                        self.initDetail();
                    } else {
                        res.body.msg && self.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
        },
        initDetail() {
            this.getProduceConsultingInfo()
            // let p1 = this.getProduceConsultingInfo();
            // let p2 = this.getMessageRecordList();
            // let p3 = this.getOperateList();
            // this.isRequest = true
            // Promise.all([p1, p2, p3]).then(() => {
            //     this.isRequest = false
            // });
        },
        
        getProduceConsultingInfo() {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/afterSale-web/api/afterQue/query/getBodyDetail",
                    this.params.id,
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.submitData = res.body.content;
                            // self.submitData.out_liability_type = res.body.content.out_liability_type;
                            self.currentVersion = res.body.content.currentVersion;
                            self.$refs.liabilityList.getList()
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        ifPurchaseDealerOfProxyId() {
            this.employeeId = this.getEmployeeInfo('id')
		},
    },
    
};
</script>
<style scoped>
.liability-detail >.el-tabs__content{
    overflow: inherit !important;
}
</style>