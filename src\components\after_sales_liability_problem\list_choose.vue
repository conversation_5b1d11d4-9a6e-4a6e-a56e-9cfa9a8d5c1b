<!-- 售后责任问题列表 引用版本-->
<template>
    <count-list
		ref='goods_list'
		:data='list'
		:btns='btn'
		:colData='cols'
		:pageTotal='count'
		:searchPage="'aftersale_analysis_question_first_level'"
		searchHolder='请输入查询条件'
		:orderNo="true"
		@search-click='searchClick'
		@selection-change='handleSelectionChange'
		:selection="selection"
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
		@radio-change='handleSelectionChange'
		:extraCountUrlParams = {callSource:params.callSource}
		countUrl="/afterSale-web/api/afterQue/query/count"
		:showCount="showCount"
		:dynamic="true"
	>
	</count-list>
</template>
<script>
import countList from "@components/common/list-count";
export default {
	components: {
		countList,
	},
  	props: ["params"],
    data () {
		var self = this;
        return {
			selection:'checkbox',
			showCount: false,
            list: [],
            count: 0,
            selectedData: [],
            search:{
				page_name:"aftersale_analysis_question_first_level",
				where:[],
				page_size:self.pageSize,     //页数
				page_no:1   //页码
			},
            btn: [
                {
					type: 'success',
					txt: '刷新',
					click: () => {self.searching();},
					loading: false
				}, {
                    type: 'success',
                    txt: '新增',
                    click: () => {self.add('')},
                }, {
					type: 'primary',
					txt: '审核',
					click: () => self.auditOrDeAudit('AUDIT'),
					loading: false
				},{
					type: 'warning',
					txt: '反审核',
					click: () => self.auditOrDeAudit('DEAUDIT'),
					loading: false
				}
			],
			cols: [
				{
					label: '编码',
					prop: 'liability_code',
					width: 180,
                    redirectClick(row) {
						self.add(row.id)
                    }
				},
				{
					label: '一级责任主体',
					prop: 'liability_type'
				},
				{
					label: '二级责任主体',
					prop: 'second_liability_type'
				},
				{
					label: '标签用途',
					prop: 'aftersale_label_usage'
				},
                // {
				// 	label: '责任类型（外部）',
				// 	prop: 'out_liability_type',
				// },
				{
					label: '状态',
					prop: 'order_status',
                    formatter(val){
						switch(val){
							case 'CREATE' :return '保存'; break;
							case 'AUDIT' :return '审核'; break;
							case 'REAUDIT' :return '反审核'; break;
						}
					}
				},
                {
					label: '创建人',
					prop: 'create_name',
				},
                {
					label: '创建时间',
					prop: 'create_time',
					format:'dataFormat1',
                    width: 130,
				},
				{
					label: '更新人',
					prop: 'update_name',
				},
				{
					label: '更新时间',
					prop: 'update_time',
                    width: 130,
                    format:'dataFormat1',
				}, {
					label: '审核人',
					prop: 'audit_name',
				}, {
					label: '审核时间',
					prop: 'audit_date',
                    width: 130,
                    format:'dataFormat1',
				}, {
					label: '反审核人',
					prop: 'de_approval_name',
				}, {
					label: '反审核时间',
					prop: 'de_approval_date',
                    width: 130,
                    format:'dataFormat1',
				}
			],
        }
    },
    methods: {
		add(id){
			let self = this, params = {
				id: id,
				callback: (data) => {
					self.searching()
				}
			}
			self.$root.eventHandle.$emit("creatTab", {
				params: params,
				component: () => import('@components/after_sales_liability_problem/detail.vue'),
				name: !id ? "新增售后责任问题" : "售后责任问题详情",
			});
		},
        auditOrDeAudit(operateType) {
            if (this.selectedData.length < 1) {
                this.$message.error('请至少选择一项')
                return
            }
            if (this.selectedData.some(item => !(/^(CREATE)$/.test(item.order_status))) && operateType == 'AUDIT') {
                this.$message.error('请勾选保存状态的数据进行审核!')
                return
            }
			if (this.selectedData.some(item => !(/^(AUDIT)$/.test(item.order_status))) && operateType == 'DEAUDIT') {
                this.$message.error('请勾选审核状态的数据进行反审核!')
                return
            }
			let ids = this.selectedData.reduce((arr, item) => {
				arr.push(item.id)
				return arr
			}, [])
			let params = {
                ids: ids,
                type: operateType
            }, self = this
            this.ajax.postStream( '/afterSale-web/api/afterQue/action/auditOrDeAudit', params,
                (res) => {
                    if (res.body.result) {
                        self.$message.success(res.body.msg);
                        self.searching();
                    } else {
                        res.body.msg && self.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
        },
        handleSelectionChange(selects){
			this.selectedData = selects
		},
        searchClick(val,resolve) {
			let self = this;
			this.search.where = val;
			new Promise((res,rej)=>{
				self.searching(resolve,res);
			}).then(()=>{
				if(this.search.page_no != 1){
					self.count = 0;
				}
				self.showCount = false;
			})
		},
		sizeChange(ps) {
			this.search.page_size = ps
			this.searching()
		},
		pageChange(page) {
			this.search.page_no = page
			this.searching()
		},
        searching(resolve, callback){ 
			let data = this.search;
			data.callSource =  this.params.callSource;
			let url = '/afterSale-web/api/afterQue/query/list';
			this.ajax.postStream(url,data,(res)=>{
				if(res.body.result) {
                    this.list = res.body.content.list || [];
					if(!this.showCount){
						let total = this.search.page_no * this.search.page_size
						this.count = res.body.content.list.length == (this.search.page_size+1)? total+1:total;
              			this.list = res.body.content.list.splice(0,res.body.content.list.length == (this.search.page_size+1)?total:res.body.content.list.length);

					}
				} else {
					this.$message({
						type:res.body.result?'success':'error',
						message:res.body.msg
					});
				}
				callback&&callback()
				resolve&&resolve();
			}, err=> {
				this.$message.error(err)
				this.list = []
				callback&&callback()
			});
		},
		commit(){
			this.params.callback(this.selectedData);
      		this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
		}
	},
	mounted () {
		let self = this;
		this.searching();
		console.log()
		if(this.params.source == 'excel'){
			this.selection = 'radio'
			this.btn =[
				{
					type: 'primary',
					txt: '提交',
					click: () => self.commit(),
					loading: false
				},
			]
			
		}
	},
}
</script>