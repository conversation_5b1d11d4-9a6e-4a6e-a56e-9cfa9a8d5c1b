<!-- 退款申请单退款明细弹框 -->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-form
			:model="form"
			ref="form"
			label-position="right" label-width="100px"
			style="display:inline-block;"
		>
			<el-form-item label="手工录入金额" prop="apply_amount">
				<el-input size='mini' type="number" v-model="form.apply_amount"></el-input>
			</el-form-item>
		</el-form>
		<el-button type='primary' size='mini' @click='() => submit()' >确定</el-button>
		
	</el-row>
	
</div>
</template>
<script>
export default {
	props: ['params'],
	data (){
		return {
			form:{
				apply_amount:'',
			}
		}
	},
	methods: {
		submit(){
			this.params.callback(this.form.apply_amount);
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId);

		}
	},
	mounted (){
	},
}
</script>
<style type="text/css" >
	.merge-red{
		background-color: 	#f99 !important;
	}
	.merge-red td {
		background-color: inherit !important;
	}
	.green{
		background-color: 	#13CE66 !important;
	}
	.green td {
		background-color: inherit !important;
	}
</style>