<!-- 新增项目信息 -->
<template>
  <div class="xpt-flex">
    <div class="xpt-top">
      <div class="btn-left">
        <el-button
          size="mini"
          type="success"
          @click="refresh()"
          :disabled="false"
          :loading="isRefreshing"
          >刷新</el-button
        >
        <el-button
          size="mini"
          type="primary"
          @click="preSave()"
          :disabled="false"
          :loading="isSaving"
          >保存</el-button
        >
      </div>
      <div class="btn-right">
        <el-button
          size="mini"
          type="danger"
          @click="closeTab()"
          :disabled="false"
          >关闭</el-button
        >
      </div>
    </div>
    <el-form
      :model="submitData"
      :rules="rules"
      ref="submitData"
      label-position="left"
      label-width="110px"
      class="my-form"
    >
      <el-tabs v-model="firstTab" class="my-ag-tabs">
        <el-tab-pane
          label="基础信息"
          name="basicInfo"
          class="xpt-flex"
          style="overflow: auto"
        >
          <div class="sub-content">
            <div class="sub-item">
              <el-row class="sub-title">基础信息：</el-row>
              <el-row class="sub-content">
                <el-col :span="24" style="white-space: nowrap">
                  <el-form-item
                    label="AG策略名称："
                    prop="strategy_name"
                    class="sub-content-item"
                  >
                    <xpt-input
                      v-model="submitData.strategy_name"
                      size="mini"
                      placeholder="请输入AG退款策略名称"
                      style="width: 500px"
                    ></xpt-input>
                    <el-tooltip
                      v-if="rules.strategy_name[0].isShow"
                      class="item"
                      effect="dark"
                      :content="rules.strategy_name[0].message"
                      placement="right-start"
                      popper-class="xpt-form__error"
                    >
                      <i class="el-icon-warning"></i>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item
                    label="来源类型："
                    prop="original_type"
                    class="sub-content-item"
                  >
                    <el-select
                      v-model="submitData.original_type"
                      clearable
                      placeholder="请选择"
                      size="mini"
                      style="width: 500px"
                      @change="originalTypeChange"
                      @visible-change="originalTypeVisibleChange"
                    >
                      <el-option
                        v-for="(value, key) in originalTypeOptions"
                        :key="key"
                        :label="value"
                        :value="key"
                      >
                      </el-option>
                    </el-select>
                    <el-tooltip
                      v-if="rules.original_type[0].isShow"
                      class="item"
                      effect="dark"
                      :content="rules.original_type[0].message"
                      placement="right-start"
                      popper-class="xpt-form__error"
                    >
                      <i class="el-icon-warning"></i>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item
                    label="AG订单店铺："
                    prop="shop_string"
                    class="sub-content-item"
                  >
                    <xpt-input
                      size="mini"
                      v-model="submitData.shop_string"
                      readonly
                      icon="search"
                      :on-icon-click="selectShop"
                      @change="resetShop"
                      placeholder=""
                      style="width: 500px"
                    ></xpt-input>
                    <el-tooltip
                      v-if="rules.shop_string[0].isShow"
                      class="item"
                      effect="dark"
                      :content="rules.shop_string[0].message"
                      placement="right-start"
                      popper-class="xpt-form__error"
                    >
                      <i class="el-icon-warning"></i>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item
                    label="平台退款原因："
                    prop="refund_reason"
                    class="sub-content-item"
                  >
                    <xpt-input
                      size="mini"
                      v-model="submitData.refund_reason"
                      readonly
                      placeholder=""
                      style="width: 500px"
                    ></xpt-input>
                  </el-form-item>
                  <!-- <el-form-item
                    label="平台退款原因："
                    prop="refund_reason"
                    class="sub-content-item"
                  >
                    <xpt-input
                      size="mini"
                      v-model="submitData.refund_reason"
                      readonly
                      icon="search"
                      :on-icon-click="selectReason"
                      @change="resetReason"
                      placeholder=""
                      style="width: 500px"
                    ></xpt-input>
                    <el-tooltip
                      v-if="rules.refund_reason[0].isShow"
                      class="item"
                      effect="dark"
                      :content="rules.refund_reason[0].message"
                      placement="right-start"
                      popper-class="xpt-form__error"
                    >
                      <i class="el-icon-warning"></i>
                    </el-tooltip>
                  </el-form-item> -->
                </el-col>
              </el-row>
            </div>
            <div class="sub-item">
              <el-row class="sub-title">整销售订单AG策略：</el-row>
              <el-row class="sub-content">
                <el-col :sapn="24">
                  <el-form-item
                    label="AG开关："
                    prop="if_all_ag"
                    label-width="60px"
                  >
                    <el-switch
                      v-model="submitData.if_all_ag"
                      on-text="开"
                      off-text="关"
                      :on-value="1"
                      :off-value="0"
                    >
                    </el-switch>
                  </el-form-item>
                  <div class="content-text-container">
                    <div class="content-text">
                      销售订单下非手工退款单累计退款总金额＝销售订单支付金额。注：销售订单下非手工的新退款申请单只包含业务状态=创建、待重转、驳回的协议退款单以及已退款（仅未发取消和多付退款类型）的新退款申请单
                    </div>
                    <div class="content-text">
                      当前销售订单的合单下所有商品行的状态等于“取消、生效、等待发运、未完全“
                    </div>
                    <div class="content-text">
                      当前销售订单的合单下所有发运明细行的状态等于“取消、等待发运”
                    </div>
                    <div class="content-text">
                      当前销售订单的合单下不存在“跨单结转”、“换货结转”退款方式或“收入结转后退款”退款类型且业务状态为“已提交”、“已核对”、退款中、“已退款”的退款申请单。
                    </div>
                    <div class="content-text">
                      当前销售订单的合单下不存在来源类型=手工单，业务状态=已提交、
                      已核对、 退款中、
                      已退款，且退款类型非服务折让的新退款申请单
                    </div>
                    <div class="content-text">
                      满足以上所有条件，整销售订单执行AG退款
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="sub-item">
              <el-row class="sub-title">按行实物商品AG策略：</el-row>
              <el-row class="sub-content">
                <el-col :sapn="24">
                  <el-form-item
                    label="AG开关："
                    prop="if_entry_ag"
                    label-width="60px"
                  >
                    <el-switch
                      v-model="submitData.if_entry_ag"
                      on-text="开"
                      off-text="关"
                      :on-value="1"
                      :off-value="0"
                    >
                    </el-switch>
                  </el-form-item>
                  <div class="content-text-container">
                    <div class="content-text">
                      商品行的状态等于“取消、生效、等待发运、未完全“
                    </div>
                    <div class="content-text">
                      发运明细行的状态等于“取消、等待发运”
                    </div>
                    <div class="content-text">退款方式=协议退款</div>
                    <div class="content-text">
                      退款申请金额=对应商品行付返前实际售价，且商品一致
                    </div>
                    <div class="content-text">合单对账余额≥０</div>
                    <div class="content-text">
                      <el-form-item
                        label=""
                        prop="if_entry_ag_amount"
                        label-width="0px"
                        style="display: flex"
                      >
                        退款申请金额小于等于
                        <xpt-input
                          size="mini"
                          v-model="submitData.if_entry_ag_amount"
                          placeholder=""
                          style="width: 100px"
                        ></xpt-input>
                        元
                        <el-tooltip
                          v-if="rules.if_entry_ag_amount[0].isShow"
                          class="item"
                          effect="dark"
                          :content="rules.if_entry_ag_amount[0].message"
                          placement="right-start"
                          popper-class="xpt-form__error"
                        >
                          <i class="el-icon-warning"></i>
                        </el-tooltip>
                      </el-form-item>
                    </div>
                    <div class="content-text">
                      满足以上所有条件，当前退款单执行AG退款
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="sub-item">
              <el-row class="sub-title">按行虚拟商品AG策略：</el-row>
              <el-row class="sub-content">
                <el-col :sapn="24">
                  <el-form-item
                    label="AG开关："
                    prop="if_virtual_ag"
                    label-width="60px"
                  >
                    <el-switch
                      v-model="submitData.if_virtual_ag"
                      on-text="开"
                      off-text="关"
                      :on-value="1"
                      :off-value="0"
                    >
                    </el-switch>
                  </el-form-item>
                  <el-form-item
                    label=""
                    prop="if_virtual_ag_amount"
                    label-width="0px"
                    style="display: flex"
                  >
                    退款申请金额小于等于
                    <xpt-input
                      size="mini"
                      v-model="submitData.if_virtual_ag_amount"
                      placeholder=""
                      style="width: 100px"
                    ></xpt-input>
                    元且满足对账余额，退款方式=协议退款，触发AG退款
                    <el-tooltip
                      v-if="rules.if_virtual_ag_amount[0].isShow"
                      class="item"
                      effect="dark"
                      :content="rules.if_virtual_ag_amount[0].message"
                      placement="right-start"
                      popper-class="xpt-form__error"
                    >
                      <i class="el-icon-warning"></i>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>
  </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
  props: ["params"],
  data() {
    let _this = this;
    return {
      strategy_id: "",
      firstTab: "basicInfo",
      submitData: {
        strategy_name: "", //退款策略名
        original_type: "", //来源类型
        refund_reason: "", //退款原因
        if_all_ag: 0, //整单AG开关
        if_entry_ag: 0, //行商品AG开关
        if_virtual_ag: 0, //虚拟商品AG开关
        if_virtual_ag_amount: "", //行虚拟商品AG策略额度
        if_entry_ag_amount: "", //行实物商品AG策略额度
        shop_string: "", //店铺名称字符
        shopList: [],
      },
      rules: {
        strategy_name: validate.isNotBlank({
          self: _this,
          msg: "请输入ＡＧ策略名称",
        }),
        original_type: validate.isNotBlank({
          self: _this,
          msg: "请输入来源类型",
        }),
        shop_string: validate.isNotBlank({
          self: _this,
          msg: "请输入AG订单店铺",
        }),
        // refund_reason: validate.isNotBlank({
        //   self: _this,
        // }),
        if_entry_ag_amount: validate.isNumber({
          self: _this,
          required: true,
          msg: "只可输入大于等于0的整数",
        }),
        if_virtual_ag_amount: validate.isNumber({
          self: _this,
          required: true,
          msg: "只可输入大于等于0的整数",
        }),
      },
      originalTypeOptions: {
        TMALL: "天猫",
        // B2C: "B2C",
        PDD: "拼多多",
        JD: "京东",
        JD_ZY: "京东自营",
        VIP: "唯品会",
        SUNING: "苏宁",
        // OTHER: "手工",
        YJ: "云集",
        I: "爱库存",
        KL: "考拉",
        MY: "蜜芽",
        KS: "快手",
        DY: "抖音",
      },
      isRefreshing: false,
      isSaving: false,
      isHandOriginalType: false, //是否手动更改来源类型
    };
  },
  methods: {
    //关闭标签页
    closeTab() {
      this.$root.eventHandle.$emit("removeTab", this.params.tabName);
    },
    selectShop() {
      let self = this;
      if (!this.submitData.original_type) {
        this.$message.warning("请先选择来源类型");
        return;
      }
      let params = {
        original_type: this.submitData.original_type,
        callback(d) {
          self.formatShop(d);
        },
      };
      this.$root.eventHandle.$emit("alert", {
        params: params,
        component: () => import("@components/after_sales_refund/agShopAlert"),
        style: "width:800px;height:500px",
        title: "店铺列表",
      });
    },
    resetShop(val) {
      if (!val) {
        this.submitData.shop_string = "";
      }
    },
    formatShop(shop) {
      // this.submitData.shop_string = shop
      //   .map((item) => item.shop_name)
      //   .join(",");
      // this.submitData.shopList = shop.map((item) => {
      //   return {
      //     shop_code: item.shop_code, //店铺编码
      //     shop_name: item.shop_name, //店铺名
      //   };
      // });
      this.submitData.shop_string = shop.shop_name
      this.submitData.shopList = [
        {
          shop_code: shop.shop_code, //店铺编码
          shop_name: shop.shop_name, //店铺名
        },
      ];
    },
    // selectReason() {
    //   let self = this;
    //   if (!this.submitData.original_type) {
    //     this.$message.warning("请先选择来源类型");
    //     return;
    //   }
    //   let params = {
    //     original_type: this.submitData.original_type,
    //     callback(d) {
    //       self.formatRefundReason(d);
    //     },
    //   };
    //   this.$root.eventHandle.$emit("alert", {
    //     params: params,
    //     component: () => import("@components/after_sales_refund/agReasonAlert"),
    //     style: "width:800px;height:500px",
    //     title: "平台退款原因列表",
    //   });
    // },
    // resetReason(val) {
    //   if (!val) {
    //     this.submitData.refund_reason = "";
    //   }
    // },
    // formatRefundReason(reason) {
    //   this.submitData.refund_reason = reason.map((item) => item.name).join(",");
    //   this.submitData.reasonList = reason.map((item) => item.name);
    // },
    originalTypeVisibleChange() {
      this.isHandOriginalType = true;
    },
    originalTypeChange(val) {
      if (!this.isHandOriginalType) {
        return;
      }
      if (!val) {
        return;
      }
      let params = {
        categoryCode: "AG_REFUND_REASON",
      };
      this.ajax.postStream(
        "/user-web/api/auxiliary/getAuxiliaryDataList",
        params,
        (res) => {
          if (res.body.result) {
            this.submitData.refund_reason = "";
            this.submitData.refund_reason = res.body.content.list
              .filter((item) => item.tag == val)
              .map((item) => item.name)
              .join(",");
            console.log(this.submitData.refund_reason);
            if (this.submitData.refund_reason.length == 0) {
              this.$message.warning("该来源类型无关联退款原因");
            }
          } else {
            this.$message.error(res.body.msg);
          }
        }
      );
    },
    refresh() {
      this.isHandOriginalType = false;
      if (this.strategy_id) {
        this.getDetail();
      } else {
        this.$message.warning("请先保存");
      }
    },
    preSave() {
      this.$refs["submitData"].validate((valid) => {
        if (!valid) return;
        this.isSaving = true;
        if (this.strategy_id) {
          this.save("edit");
        } else {
          this.save("add");
        }
      });
    },
    save(type) {
      let typeUrl = {
        add: "insertAgStrategy",
        edit: "updateAgStrategy",
      }[type];
      let params = JSON.parse(JSON.stringify(this.submitData));
      delete params.refund_reason;
      delete params.shop_string;
      this.ajax.postStream(
        `/afterSale-web/api/aftersale/agRefund/${typeUrl}`,
        params,
        (res) => {
          this.isSaving = false;
          if (res.body.result) {
            this.$message.success(res.body.msg);
            type == "add" ? (this.strategy_id = res.body.content) : "";
            this.refresh();
          } else {
            this.$message.error(res.body.msg);
          }
        },
        () => {
          this.isSaving = false;
        }
      );
    },
    getDetail() {
      this.isHandOriginalType = false;
      let params = {
        strategy_id: this.strategy_id,
      };
      this.isRefreshing = true;
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/agRefund/getAgStrategyDetail",
        params,
        (res) => {
          if (res.body.result) {
            this.submitData = { ...this.submitData, ...res.body.content };
            this.submitData.shop_string = res.body.content.shopList
              .map((item) => item.shop_name)
              .join(",");
            this.submitData.refund_reason = (
              this.submitData.reasonList || []
            ).join(",");
            // this.$message.success(res.body.msg || "获取策略详情成功");
          } else {
            this.$message.error(res.body.msg || "获取策略详情失败");
          }
          this.isRefreshing = false;
        },
        (err) => {
          this.$message.error(err);
          this.isRefreshing = false;
        }
      );
    },
  },
  mounted() {
    this.strategy_id = this.params.strategy_id || "";
    if (this.strategy_id) {
      this.getDetail();
    }
  },
};
</script>
<style lang="stylus" scoped>
.xpt-top {
  display: flex;
  align-items: center;
  justify-content: space-between;

  > .btn-left, .btn-right {
    display: flex;
    align-items: center;
    padding: 5px 0px !important;

    > .el-button {
      width: 60px;
    }
  }
}

.sub-item {
  padding: 10px;
  border-bottom: 1px dashed #ccc;
}

.sub-item:last-child {
  border-bottom: none;
}

.sub-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.sub-content {
  .sub-content-item {
    margin-bottom: 8px;
  }
}

.content-text {
  line-height: 2;
}

.my-form {
  height: calc(100% - 32px) !important;
}
</style>
<style>
.my-ag-tabs .el-tabs__content {
  height: calc(100% - 24px) !important;
}
</style>