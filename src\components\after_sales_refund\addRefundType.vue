<!-- 退款申请单退款明细弹框 -->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-form
			v-if="params.refundType === 'COMPENSATION' || params.refundType === 'REPAIR'"
			:model="form"
			:rules="rules"
			ref="form"
			label-position="right" label-width="100px"
			style="display:inline-block;"
		>
			<el-form-item :label="(params.refundType === 'COMPENSATION' ? '补偿' : '维修费') + '总金额'" prop="apply_amount">
				<el-input size='mini' type="number" v-model="form.apply_amount"></el-input>
				<el-tooltip v-if='rules.apply_amount[0].isShow' class="item" effect="dark" :content="rules.apply_amount[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item>
		</el-form>
		<el-button type='primary' size='mini' @click='() => submit()' :disabled="!selectData.length">确定</el-button>
		<el-col class='xpt-align__right' :span='15' style="float:right;">
			<el-select v-model="limit_field" size='mini' style="width:100px;" @change="field_value = ''">
				<el-option
					v-for="(value,key) in limit_field_options"
					:key="key.toString()"
					:label="value.toString()"
					:value="key.toString()">
				</el-option>
			</el-select>
			<el-input
				placeholder="请输入查询值"
				size='mini'
				v-model="field_value"
				:disabled="fieldValueDisabledTest()"
			></el-input>
			<el-button type='primary' @click="searchFun" size='mini'>查询</el-button>
		</el-col>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData='colData'
		:selection="params.refundType === 'REFUNDCARRYFORWARD' ? 'radio' : 'checkbox'"
		:pageTotal='pageTotal'
		:showHead="false"
		:isNeedClickEvent="true"
		@row-dblclick='() => submit()'
		@selection-change="s => this.selectData = s"
		@radio-change="s => this.selectData = [s]"
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
		:taggelClassName='refundGoodsRowClassName'>
	</xpt-list>
</div>
</template>
<script>
import refund_type_cols from './refund_type_cols.js';
export default {
	props: ['params'],
	mixins: [refund_type_cols],
	data (){
		return {
			roleList: [],
			colData: [],
			selectData: [],
			limit_field_options: {},
			limit_field: '',
			field_value: '',

			page_no: 1,
			page_size: 50,
			pageTotal: 0,
			form: {
				apply_amount: '',
			},
			rules: {
				apply_amount: [{
					required: true,
					validator: (rule, value, callback) => {
						if(value && value >= 0){
							this.rules[rule.field][0].isShow = false
							callback()
						}else {
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '补偿总金额必须要大于等于0',
				}],
			},
		}
	},
	methods: {
		fieldValueDisabledTest (){
			if(this.limit_field === 'merge_trade_no'){
				// 当方式为换货转结，合并订单号从目标合并订单改为合并订单号
				if (this.params.refundType === 'REFUNDCARRYFORWARD') {
					this.field_value = this.params.mergeNumber
				} else {
					this.field_value = this.params.mergeTradeNo
				}

				return true
			}else if (/^(COMPENSATION|REPAIR)$/.test(this.params.refundType)){
				if(this.limit_field === 'after_order_no' && this.params.afterOrderNo){
					this.field_value = this.params.afterOrderNo
					return true
				}else if(this.limit_field === 'buyer_nick' && this.params.nickName){
					this.field_value = this.params.nickName
					return true
				}
			}/*else {
				this.field_value = ''
			}*/
		},
		// 补偿和维修费的申请金额根据各自实际售价平摊
		calcEachApplyAmount (){
			var act_price_total = 0
			,	without_last_act_price_total = 0
			,	onlySelectOneAfterOrderNo = this.params.afterOrderNo

			// if(this.selectData.some(o => o.merge_trade_no !== this.params.mergeTradeNo)){
			// 	this.$message.error('所选的合并单号与表头合并单号不一致')
			// 	throw('所选的合并单号与表头合并单号不一致')
			// }

			this.selectData.forEach(obj => {
				if(onlySelectOneAfterOrderNo){
					if(onlySelectOneAfterOrderNo !== obj.after_order_no){
						this.$message.error('一张退款申请单只能选一个售后单退款')
						throw('一张退款申请单只能选一个售后单退款')
					}
				}else {
					onlySelectOneAfterOrderNo = obj.after_order_no
				}

				act_price_total += obj.act_price
			})

			this.$refs.form.validate(valid => {
				if(valid){
					this.selectData.forEach((obj, index) => {
						// 创建不可枚举属性，对外界无副作用
						Object.defineProperties(obj, {
						    _new_act_price: {
						        configurable: true,
						        writable: true,
						        enumerable: false,
						    },
						})

						if(index === this.selectData.length - 1){
							obj._new_act_price = Number((this.form.apply_amount - without_last_act_price_total).toFixed(2))
						}else {
							obj._new_act_price = Number((obj.act_price / act_price_total * this.form.apply_amount).toFixed(2))
							without_last_act_price_total += obj._new_act_price
						}
					})
				}else {
					throw('补偿总金额必须要大于等于0')
				}
			})
		},
		searchFun (){
			var searchFunUrl = ''
			,	searchFunData = {}
			,	refundType = this.params.refundType
			,	responseKey
			if(refundType === 'DELAY' || refundType === 'SINGLE_DISCOUNT'){
				searchFunUrl = '/order-web/api/scmsystrade/order/listMenu'
				responseKey = 'list'
				searchFunData = {
					merge_trade_no: this.params.mergeTradeNo,
					// page: {
					// 	pageNo: this.page_no,
					// 	pageCount: this.page_size,
					// 	length: this.page_size,
					// },
					page_no:this.page_no,
					page_size:this.page_size,
					status: refundType === 'CANCEL' ? 'CANCEL' : null,
				}

				if(refundType === 'SINGLE_DISCOUNT'){
					searchFunData['status_list'] = ['DELIVERED','PART_DELIVERED']
				}
				if(this.params.CANCEL_oid){
					searchFunData.fentry_id = this.params.CANCEL_oid
				}
				if(this.limit_field){
					searchFunData[this.limit_field] = this.field_value
				}

			}else if(refundType === 'CANCEL' ){
				searchFunUrl = '/order-web/api/scmsystrade/order/listMenuOfCancel'
				responseKey = 'list'
				searchFunData = {
					merge_trade_no: this.params.mergeTradeNo,
					refund_way:this.params.refund_way,
					refund_mode:this.params.refund_mode,//退款模式
					original_type:this.params.original_type,
					page_no:this.page_no,
					page_size:this.page_size,
					status: refundType === 'CANCEL' ? 'CANCEL' : null,
				}


				if(this.params.CANCEL_oid){
					searchFunData.fentry_id = this.params.CANCEL_oid
				}
				if(this.limit_field){
					searchFunData[this.limit_field] = this.field_value
				}
			}else if (refundType === 'RETURNS' || refundType === 'REFUNDCARRYFORWARD'){
				searchFunUrl =
					refundType === 'RETURNS'
					? '/afterSale-web/api/aftersale/bill/returns/choose/billReturnsChooseForRefundNormal'
					: '/afterSale-web/api/aftersale/bill/returns/choose/billReturnsChooseForRefundCarryForward'
				responseKey = 'list'
				searchFunData = {
					merge_trade_id: this.params.mergeTradeId,
					refund_mode:this.params.refund_mode,
					// merge_trade_id: 16697446907904,
				    page_no: this.page_no,
				    page_size: this.page_size,
				}
				if(refundType == 'RETURNS'){
					searchFunData.refund_way = this.params.refund_way;
				}
				// 京东
				if(refundType !== 'RETURNS'){
					delete(searchFunData.refund_mode)
				}
				// 当方式为换货转结，合并订单id从目标合并订单改为合并订单id xdhh
				searchFunData.merge_trade_id = refundType === 'REFUNDCARRYFORWARD' ? this.params.mergeNumberId : this.params.mergeTradeId
				console.log('searchFunData', searchFunData)
				if(this.limit_field !== 'merge_trade_no'){
					searchFunData.limit_field = this.limit_field
					searchFunData.field_value = this.field_value
					searchFunData.operator = '='
				}
			}else if (refundType === 'COMPENSATION' || refundType === 'REPAIR'){
				if(refundType === 'COMPENSATION') {
					searchFunUrl = '/afterSale-web/api/aftersale/plan/listQuestionSubForRefund'
				}else{
					searchFunUrl = '/afterSale-web/api/aftersale/plan/listQuestionSub'
				}
				searchFunData = {
					[this.limit_field]: this.field_value,
					type: 'NEED_NOW',
					merge_trade_id: this.params.mergeTradeId,
					is_refund : 'Y'
				}
			}

			let self = this
			this.ajax.postStream(searchFunUrl, searchFunData, res => {
				if(res.body.result){
					if(responseKey === 'list'){
						self.roleList = res.body.content.list || []
						self.pageTotal = res.body.content.count
						if(self.params.CANCEL_oid){//未发取消 && 天猫退款 && oid存在时，直接将全部结果返回到明细行
							self.selectData = self.roleList
							// 当点击的是未发取消按钮时，弹窗中商品所在销售单号为退款申请单上的销售单号时，该商品的背景颜色为绿色，反之为红色
							if (self.params.refundType == 'CANCEL') {
								self.roleList.forEach(item => {
									if (self.params.originalNo == item.sys_trade_no) {
										item.color_sign = 'B'
									} else {
										item.color_sign = 'A'
									}
								});
							}
							console.log('console.log(self.roleList)',self.roleList)
							self.submit()
							// if(this.roleList.length){
							// }else {
							// 	this.$message.error('该合并单下对应的商品行未取消，请稍后再试')
							// 	this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
							// }
						} else {
							if (self.params.refundType == 'CANCEL') {
								self.roleList.forEach(item => {
									if (self.params.originalNo == item.sys_trade_no) {
										item.color_sign = 'B'
									} else {
										item.color_sign = 'A'
									}
								});
							}
							console.log('console.log(self.roleList1)',self.roleList)
						}
					}else {
						this.roleList = res.body.content || []
						this.pageTotal = this.roleList.length
					}
				}else {
					this.$message.error(res.body.msg)
					if(this.params.CANCEL_oid){
						this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
					}
				}
			})
		},
		pageChange(pageSize){
			this.page_size = pageSize
			this.searchFun()
		},
		currentPageChange(page){
			this.page_no = page
			this.searchFun()
		},
		submit (selectData){
			var callback = () => {
				this.params.callback(selectData || this.selectData)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
			let self = this

			if (this.params.refundType === 'CANCEL' && !!self.params.tid && this.params.business_type_trade !== 'SHOP_MALL') {
				let ifSameTid = this.selectData.every(item => {
					return item.tid == self.params.tid
				})
				if(!ifSameTid) {
					this.$message.error('商品行的淘宝单号与表头平台单号不一致！')
					return
				}
			}
			if(this.params.refundType === 'COMPENSATION' || this.params.refundType === 'REPAIR'){
				this.calcEachApplyAmount()
			}else if (this.params.refundType === 'REFUNDCARRYFORWARD'){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/returns/choose/billReturnsGoodsForRefundCarryForward', { id: this.selectData[0].id }, res => {
					if(res.body.result){
						if((res.body.content.listAftersalePlanReturnsGoods || []).length === 0){
							this.$message.error('该退货跟踪单下没有可结转的退货商品行，无需结转！')
						}

						selectData = (res.body.content.listAftersalePlanReturnsGoods || []).map(obj => {
							obj.bill_returns_no = res.body.content.bill_returns_no
							return obj
						})
						callback()
					}else {
						this.$message.error(res.body.msg)
					}
				})
				return
			}

			callback()
		},
		refundGoodsRowClassName(row,index) {
			switch (row.color_sign){
				case 'A': return 'merge-red';
				case 'B': return  'green';
			}
		},
	},
	mounted (){
		console.log(this.params)
		var refundType = this.params.refundType

		if(refundType === 'CANCEL'){
			this.limit_field_options = {
				'customer_name': '买家昵称',
				'user_name': '业务员',
			}

			this.colData = this.CANCEL_Cols
		}else if(refundType === 'RETURNS'){
			this.limit_field_options = {
				'bill_returns_no': '退货跟踪单号',
				'after_order_no': '售后单号',
				'buyer_name': '买家昵称',
			}

			this.colData = this.RETURNS_Cols
		}else if(refundType === 'REFUNDCARRYFORWARD'){
			this.limit_field_options = {
				'bill_returns_no': '退货跟踪单号',
				'after_order_no': '售后单号',
				'buyer_name': '买家昵称',
			}

			this.colData = this.REFUNDCARRYFORWARD_Cols
		}else if(refundType === 'DELAY'){
			this.limit_field_options = {
				'customer_name': '买家昵称',
				'user_name': '业务员',
			}

			this.colData = this.DELAY_Cols
		}else if(refundType === 'SINGLE_DISCOUNT'){
			// this.limit_field_options = {
			// 	'customer_name': '买家昵称',
			// 	'user_name': '业务员',
			// }

			// 设置单品退款金额可输入
			this.SINGLE_DISCOUNT_Cols[3].bool = true
			this.SINGLE_DISCOUNT_Cols[3].isInput = true

			this.colData = this.SINGLE_DISCOUNT_Cols
		}else if (refundType === 'COMPENSATION' || refundType === 'REPAIR'){
			this.limit_field_options = {
				'after_order_no': '售后单号',
				'buyer_nick': '买家昵称',
			}
			this.colData = this.COMPENSATION_Cols

			if(this.params.afterOrderNo){
				this.limit_field = 'after_order_no'
				this.field_value = this.params.afterOrderNo
				this.searchFun()
			}else if (this.params.nickName){
				this.limit_field = 'buyer_nick'
				this.field_value = this.params.nickName
				this.searchFun()
			}
			return
		}

		this.limit_field_options.merge_trade_no = '合并订单号'
		this.limit_field = 'merge_trade_no'
		this.field_value = this.params.mergeTradeNo

		this.searchFun()
	},
}
</script>
<style type="text/css" >
	.merge-red{
		background-color: 	#f99 !important;
	}
	.merge-red td {
		background-color: inherit !important;
	}
	.green{
		background-color: 	#13CE66 !important;
	}
	.green td {
		background-color: inherit !important;
	}
</style>