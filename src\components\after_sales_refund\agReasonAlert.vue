<!-- ag退款原因弹窗 -->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="6">
        <el-button
          type="primary"
          size="mini"
          @click="() => submit()"
          :disabled="!selectData"
          >确定</el-button
        >
      </el-col>
      <el-col :span="18">
        <div style="float: right">
          <el-select
            v-model="searchObj.field"
            size="mini"
            placeholder="请选择"
            @change="() => (searchObj.key = '')"
            style="width: 110px"
          >
            <el-option label="平台退款原因" value="key"></el-option>
          </el-select>
          <el-select
            v-model="searchObj.operator"
            size="mini"
            placeholder="请选择"
            @change="() => (searchObj.key = '')"
            style="width: 80px"
          >
            <el-option
              v-for="(val, key) in operator_options"
              :key="key"
              :label="val"
              :value="key"
            ></el-option>
          </el-select>
          <el-input
            size="mini"
            v-model="searchObj.key"
            placeholder="请输入要搜索的值"
            style="width: 150px"
          ></el-input>
          <el-button type="primary" size="mini" @click="searchFun"
            >查询</el-button
          >
          <el-button type="primary" size="mini" @click="resetFun"
            >重置</el-button
          >
        </div>
      </el-col>
    </el-row>
    <xpt-list
      :data="reasonList"
      :colData="colData"
      selection="checkbox"
      :pageTotal="pageTotal"
      :showHead="false"
      :pageLength="50"
      :pageSizes="[50]"
      @selection-change="(s) => (selectData = s)"
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
      ref="xptList"
    >
    </xpt-list>
  </div>
</template>
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      reasonList: [],
      selectData: null,
      searchObj: {
        pageNo: 1,
        pageSize: 50,
        key: "",
        field: "key",
        operator: "=",
      },
      pageTotal: 0,
      operator_options: {
        "=": "等于",
      },
      colData: [
        {
          label: "编码",
          prop: "code",
        },
        {
          label: "平台退款原因名称",
          prop: "name",
        },
        {
          label: "来源类型",
          prop: "original_type",
          formatter: (val)=>{
              return this.originalTypeOptions[val]||val
          },
        },
        {
          label: "状态",
          prop: "status",
          formatter: (val) => {
            return (
              {
                1: "生效",
                0: "失效",
              }[val] || val
            );
          },
        },
      ],
      originalTypeOptions: {
        TMALL: "天猫",
        // B2C: "B2C",
        PDD: "拼多多",
        JD: "京东",
        JD_ZY: "京东自营",
        VIP: "唯品会",
        SUNING: "苏宁",
        // OTHER: "手工",
        YJ: "云集",
        I: "爱库存",
        KL: "考拉",
        MY: "蜜芽",
        KS: "快手",
        DY: "抖音",
      }
    };
  },
  methods: {
    searchFun() {
      let params = {
        categoryCode: "AG_REFUND_REASON",
        key: this.searchObj.key,
        page: {
          length: this.searchObj.pageSize,
          pageNo: this.searchObj.pageNo,
        },
      };
      this.ajax.postStream(
        "/user-web/api/auxiliary/getAuxiliaryDataList",
        params,
        (res) => {
          if (res.body.result) {
            this.formatList(res.body.content.list || []);
          } else {
            this.$message.error(res.body.msg);
          }
        }
      );
    },
    formatList(preList) {
      let list = preList
        .filter((item) => item.tag == this.params.original_type)
        .map((item) => {
          return {
            code: item.code,
            name: item.name,
            original_type: item.tag,
            status: item.status,
          };
        });
      this.reasonList = list;
      this.pageTotal = list.length;
    },
    resetFun() {
      this.searchObj = {
        pageNo: 1,
        pageSize: 50,
        categoryCode: "AG_REFUND_REASON",
        key: "",
        field: "key",
        operator: "=",
      };
    },
    pageChange(pageSize) {
      this.searchObj.pageSize = pageSize;
      this.searchFun();
    },
    currentPageChange(page) {
      this.searchObj.pageNo = page;
      this.searchFun();
    },
    submit(selectData) {
      this.params.callback(selectData || this.selectData);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
  },
  mounted() {
    this.searchFun();
  },
};
</script>