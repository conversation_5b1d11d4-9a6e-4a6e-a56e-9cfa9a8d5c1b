<!--ag退款策略列表-->
<template>
  <div class="xpt-flex">
    <el-row :gutter="10" class="xpt-top">
      <el-form
        ref="query"
        :rules="rules"
        :model="query"
        label-position="right"
        label-width="120px"
      >
        <el-col :span="2">
          <el-button type="primary" size="mini" @click="add">新增</el-button>
        </el-col>
        <el-col :span="16" style="display: flex">
          <el-form-item label="AG退款策略名称：" prop="strategy_name">
            <xpt-input
              v-model="query.strategy_name"
              size="mini"
              placeholder="请输入AG退款策略名称"
            ></xpt-input>
          </el-form-item>
          <el-form-item label="来源类型：" prop="original_type">
            <el-select
              v-model="query.original_type"
              clearable
              placeholder="请选择"
              size="mini"
              style="width: 150px"
            >
              <el-option
                v-for="(value, key) in originalTypeOptions"
                :key="key"
                :label="value"
                :value="key"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单店铺：" prop="shop_name">
            <xpt-input
              size="mini"
              v-model="query.shop_name"
              placeholder="请输入订单店铺名称"
            ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="xpt-align__right">
          <el-button
            type="success"
            size="mini"
            @click="queryData"
            :disabled="queryBtnStatus"
            :loading="queryBtnStatus"
            >查询</el-button
          >
          <el-button type="primary" size="mini" @click="reset"
            >重置查询条件</el-button
          ><br />
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead="false"
      :data="list"
      :colData="cols"
      :pageTotal="count"
      selection=""
      @page-size-change="pageSizeChange"
      @current-page-change="currentPageChange"
    ></xpt-list>
  </div>
</template>
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      query: {
        page_no: 1, // 页码
        page_size: 50, // 页数
        strategy_name: "",
        original_type: "",
        shop_name: "",
      },
      rules: {},
      // 查询按钮状态
      queryBtnStatus: false,
      list: [],
      cols: [
        {
          label: "AG退款策略ID",
          prop: "strategy_no",
          redirectClick(row) {
            self.edit(row);
          },
        },
        {
          label: "AG退款策略名称",
          prop: "strategy_name",
        },
        {
          label: "来源类型",
          prop: "original_type",
          formatter: (val) => self.originalTypeOptions[val] || val,
        },
        {
          label: "订单店铺",
          prop: "shop_name",
        },
        {
          label: "创建人",
          prop: "creator_name",
          width: 100,
        },
        {
          label: "创建时间",
          prop: "create_time",
          format: "dataFormat1",
          width: 150,
        },
        {
          label: "更新人",
          prop: "last_modifier_name",
          width: 100,
        },
        {
          label: "更新时间",
          prop: "last_modify_time",
          format: "dataFormat1",
          width: 150,
        },
      ],
      count: 0,
      originalTypeOptions: {
        TMALL: "天猫",
        B2C: "B2C",
        PDD: "拼多多",
        JD: "京东",
        JD_ZY: "京东自营",
        VIP: "唯品会",
        SUNING: "苏宁",
        OTHER: "手工",
        YJ: "云集",
        I: "爱库存",
        KL: "考拉",
        MY: "蜜芽",
        KS: "快手",
        DY: "抖音",
      },
    };
  },
  mounted() {
    this.queryData();
  },
  methods: {
    reset() {
      for (let v in this.query) {
        if (!["page_size", "page_no"].includes(v)) {
          this.query[v] = "";
        }
      }
      this.query.page_size = 50;
      this.query.page_no = 1;
    },
    add() {
      let params = {};
      this.$root.eventHandle.$emit("creatTab", {
        name: "新增AG退款策略",
        params: params,
        component: () =>
          import("@components/after_sales_refund/addAgRefundPolicy.vue"),
      });
    },
    edit(row) {
      let params = {
        strategy_id:row.strategy_id
      };
      this.$root.eventHandle.$emit("creatTab", {
        name: "编辑AG退款策略",
        params: params,
        component: () =>
          import("@components/after_sales_refund/addAgRefundPolicy.vue"),
      });
    },
    queryData() {
      this.$refs.query.validate((valid) => {
        if (!valid) {
          return;
        }
        this.getList();
      });
    },
    getList() {
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/agRefund/getAgRefundList",
        this.query,
        (res) => {
          this.list = res.body.content.list || [];
          this.count = res.body.content.count || 0;
        }
      );
    },
    pageSizeChange(ps) {
      this.query.page_size = ps;
      this.queryData();
    },
    currentPageChange(page) {
      this.query.page_no = page;
      this.queryData();
    },
  },
};
</script>
<style lang="css" scoped>
</style>