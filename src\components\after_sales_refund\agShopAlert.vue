<!-- ag退款订单店铺弹窗 -->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="6">
        <el-button
          type="primary"
          size="mini"
          @click="() => submit()"
          :disabled="!selectData"
          >确定</el-button
        >
      </el-col>
      <el-col :span="18">
        <div style="float: right">
          <el-select
            v-model="searchObj.field"
            size="mini"
            placeholder="请选择"
            @change="() => (searchObj.keyword = '')"
            style="width: 100px"
          >
            <el-option
              label="店铺名称"
              value="cd1a167cedd65a8472028ae57e9ae1ff"
            ></el-option>
          </el-select>
          <el-select
            v-model="searchObj.operator"
            size="mini"
            placeholder="请选择"
            @change="() => (searchObj.keyword = '')"
            style="width: 80px"
          >
            <el-option
              v-for="(val, key) in operator_options"
              :key="key"
              :label="val"
              :value="key"
            ></el-option>
          </el-select>
          <el-input
            size="mini"
            v-model="searchObj.keyword"
            placeholder="请输入搜索内容"
            style="width: 150px"
          ></el-input>
          <el-button type="primary" size="mini" @click="searchFun"
            >查询</el-button
          >
          <el-button type="primary" size="mini" @click="resetFun"
            >重置</el-button
          >
        </div>
      </el-col>
    </el-row>
    <xpt-list
      :data="shopList"
      :colData="colData"
      selection="radio"
      :pageTotal="pageTotal"
      :showHead="false"
      :pageLength="50"
      @radio-change="(s) => (selectData = s)"
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
      ref="xptList"
    >
    </xpt-list>
  </div>
</template>
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      shopList: [],
      selectData: null,
      searchObj: {
        pageNo: 1,
        pageSize: 50,
        keyword: "",
        field: "cd1a167cedd65a8472028ae57e9ae1ff",
        operator: "=",
      },
      pageTotal: 0,
      operator_options: {
        "=": "等于",
      },
      colData: [
        {
          label: "店铺编码",
          prop: "shop_code",
        },
        {
          label: "店铺名称",
          prop: "shop_name",
        },
        {
          label: "店铺分类",
          prop: "shop_type",
          format: "auxFormat",
          formatParams: "shopClassify",
        },
        {
          label: "店铺分组",
          format: "auxFormat",
          prop: "shop_group",
          formatParams: "shopGroup",
        },
        {
          label: "店铺状态",
          prop: "shop_status",
          format: "shopStatus",
        },
      ],
      shopGroupOptions:{
          'TMALL':'ALI',
          'JD_ZY':'JD',
      }
    };
  },
  methods: {
    searchFun() {
      let where = this.formatField();
      let params = {
        page_name: "cloud_shop_v2",
        where: where,
        page: {
          length: this.searchObj.pageSize,
          pageNo: this.searchObj.pageNo,
        },
      };
      this.ajax.postStream("/material-web/api/shopv2/list", params, (res) => {
        if (res.body.result) {
          this.shopList = res.body.content.list || [];
          this.pageTotal = res.body.content.count || 0;
        } else {
          this.$message.error(res.body.msg);
        }
      });
    },
    resetFun() {
      this.searchObj = {
        pageNo: 1,
        pageSize: 50,
        keyword: "",
        field: "cd1a167cedd65a8472028ae57e9ae1ff",
        operator: "=",
      };
    },
    formatField() {
      let shopGroup = this.shopGroupOptions[this.params.original_type]||this.params.original_type;
      let where = [
        // {
        //   field: "********************************",
        //   table: "13e8b2d623a88d6220b90d21e4fec931",
        //   value: "ON_LINE",
        //   operator: "=",
        //   condition: "AND",
        //   listWhere: [],
        // },
        {
          field: "247545254f6dd83b44590633c6d69533",
          table: "13e8b2d623a88d6220b90d21e4fec931",
          value: "OPEN",
          operator: "=",
          condition: "AND",
          listWhere: [],
        },
        {
          field: "5921c4692b52df21cf81e48791d3c7c0",
          table: "13e8b2d623a88d6220b90d21e4fec931",
          value: shopGroup,
          operator: "=",
          condition: "AND",
          listWhere: [],
        },
      ];
      if (this.searchObj.keyword) {
        let obj = {
          field: this.searchObj.field,
          table: "13e8b2d623a88d6220b90d21e4fec931",
          value: this.searchObj.keyword,
          operator: this.searchObj.operator,
          condition: "AND",
          listWhere: [],
        };
        where.push(obj);
      }
      return where;
    },
    pageChange(pageSize) {
      this.searchObj.pageSize = pageSize;
      this.searchFun();
    },
    currentPageChange(page) {
      this.searchObj.pageNo = page;
      this.searchFun();
    },
    submit(selectData) {
      this.params.callback(selectData || this.selectData);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
  },
  mounted() {
    this.searchFun();
  },
};
</script>