<!--新增辅资料值-->
<template>
  <div>
    <xpt-headbar>
      <el-button type='primary' size='mini' @click='submit' slot='left' :loading="submitting">确认</el-button>
    </xpt-headbar>

    <el-form :model='form' ref='form' :rules='rules' label-position="right" label-width="120px">
      <el-form-item label="需自动推退换货单" prop='if_auto_returns'>
        <el-select size='mini' placeholder="请选择" v-model='form.if_auto_returns' @change="onIfAutoReturnsChange">
          <el-option label="是" value="Y"></el-option>
          <el-option label="否" value="N"></el-option>
        </el-select>
        <el-tooltip v-if='rules.if_auto_returns[0].isShow' class="item" effect="dark"
          :content="rules.if_auto_returns[0].message" placement="right-start" popper-class='xpt-form__error'>
          <i class='el-icon-warning'></i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="退货方式" prop='returns_type' >
        <el-select size='mini' placeholder="请选择" v-model='form.returns_type' @change="onReturnsTypeChange" :disabled="!form.if_auto_returns || form.if_auto_returns=='N'">
          <el-option label="退货" value="RETURN"></el-option>
          <el-option label="换货" value="CHANGE"></el-option>
        </el-select>
        <el-tooltip v-if='rules.returns_type[0].isShow' class="item" effect="dark"
          :content="rules.returns_type[0].message" placement="right-start" popper-class='xpt-form__error'>
          <i class='el-icon-warning'></i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="换货商品" prop='change_goods_type'>
        <el-select size='mini' placeholder="请选择" v-model='form.change_goods_type' :disabled="form.returns_type!=='CHANGE'">
          <el-option label="同商品" value="SAME"></el-option>
          <el-option label="不同商品" value="DIFF"></el-option>
        </el-select>
        <el-tooltip v-if='rules.change_goods_type[0].isShow' class="item" effect="dark"
          :content="rules.change_goods_type[0].message" placement="right-start" popper-class='xpt-form__error'>
          <i class='el-icon-warning'></i>
        </el-tooltip>
      </el-form-item>
    </el-form>
    <div class="tips">温馨提示：设置需系统自动下推退换货跟踪单后，系统需待截货申请单的所有明细截货行状态均=仓库已处理 且发运明细行状态=已发运，才会执行将截货成功的明细行自动下推退换货跟踪单</div>
  </div>
</template>
<script>
export default {
  props: ['params'],
  data() {
    let self = this;
    return {
      submitting: false,
      form: {
       if_auto_returns: undefined,
       returns_type: undefined,
       change_goods_type: undefined
      },
      rules: {
        if_auto_returns: [{
          required: true,
          message: '请选择',
          isShow: false,
          validator: function (rule, value, callback) {
            // 数据校验
            if (value) {
              self.rules[rule.field][0].isShow = false
              callback();
            } else {
              self.rules[rule.field][0].isShow = true
              callback(new Error(''));
            }
          }
        }],
        returns_type: [{
          message: '请选择',
          isShow: false,
          validator: function (rule, value, callback) {
            // 数据校验
            if (value || self.form.if_auto_returns == 'N') {
              self.rules[rule.field][0].isShow = false
              callback();
            } else {
              self.rules[rule.field][0].isShow = true
              callback(new Error(''));
            }
          }
        }],
        change_goods_type: [{
          message: '请选择',
          isShow: false,
          validator: function (rule, value, callback) {
            // 数据校验
            if (value || self.form.returns_type !== 'CHANGE') {
              self.rules[rule.field][0].isShow = false
              callback();
            } else {
              self.rules[rule.field][0].isShow = true
              callback(new Error(''));
            }
          }
        }]
      },
    }
  },
  methods: {
    // 保存资料
    submit(callback) {
      let self = this;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if(self.form.change_goods_type === 'DIFF') {
            this.$message({
              message: '不支持自动下推换不同商品的退换货跟踪单，需人工处理',
              type: "warning",
            })
            return;
          }
          self.submitting = true;
          this.ajax.postStream(
            '/afterSale-web/api/aftersale/interceptNotice/updateInterceptAutoReturns',
            {
              ifAutoReturns: self.form.if_auto_returns,
              returnsType: self.form.returns_type,
              changeGoodsType: self.form.change_goods_type,
              itemIds: self.params.itemIds
            },
            (res) => {
              self.submitting = false;
              res = res.body;
              if (res.result) {
                this.$message({
                  message: res.msg,
                  type: "success",
                });
                this.params.callback();
                this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            },
            (err) => {
              self.submitting = false;
              this.$message({
                message: err.msg,
                type: "error",
              });
            }
          );

        }
      })

    },
    // 关闭
    close() {
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    },

    onIfAutoReturnsChange() {
      this.form.returns_type = undefined;
      this.form.change_goods_type = undefined;
      this.rules.returns_type[0].isShow = false;
      this.rules.change_goods_type[0].isShow = false;
    },

    onReturnsTypeChange() {
      this.form.change_goods_type = undefined;
      this.rules.change_goods_type[0].isShow = false;
    }
  },
}
</script>
<style scoped>
  .tips {
    color: #ff4949;
    line-height: 1.5;
    margin: 20px;
  }
</style>
