<!-- 弹窗选择店铺列表 -->
<template>
  <div class="xpt-flex">
    <list-header
      ref="dynamic_header"
      :btns="btns"
      :searchPage="search.page_name"
      @searchClick="searchData"
      class="bank-list-alert"
    >
    </list-header>
    <xpt-list
      :data="bankList"
      :btns="btns"
      :colData="cols"
      :searchPage="search.page_name"
      :pageTotal="pageTotal"
      :showCount="showCount"
      :selection="selection"
      :showHead="showHead"
      :isNeedClickEvent="showHead"
      @search-click="searchData"
      @radio-change="radioChange"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
      @count-off="countOff"
    ></xpt-list>
  </div>
</template>
<script>
import listHeader from "@/components/customized_base_material_goods/components/components/list-header";
export default {
  components: {
    listHeader,
  },
  data() {
    let self = this;
    return {
      btns: [
        {
          type: "primary",
          txt: "确认",
          click: self.close,
        },
      ],
      cols: [
        {
          label: "开户行编码",
          prop: "fnumber",
          width: 90,
        },
        {
          label: "开户行名称",
          prop: "fname",
          width: 250,
        },
        {
          label: "银行名称",
          prop: "bank_name",
        },
        {
          label: "省",
          prop: "fprovince",
        },
        {
          label: "城市",
          prop: "fcity",
        },
        {
          label: "地区",
          prop: "fdistrict",
        },
      ],
      search: {
        page_name: "erp_ifs_bank",
        where: [],
        page_size: 50,
        page_no: 1,
      },
      bankList: [],
      selectData: "",
      pageTotal: 0,
      showCount: false,
      countOffFlag: false,
      selection: "radio",
      showHead: false,
      refreshBtnStatus: false,
    };
  },
  props: ["params"],
  methods: {
    countOff() {
      let self = this,
        url = "/kingdee-web/api/use/ifs/bank/count";
      if (!self.bankList.length) {
        self.$message.error("当前列表为空，先搜索内容");
        return;
      }
      if (!!self.countOffFlag) {
        self.$message.error("请勿重复点击");
        return;
      }
      self.countOffFlag = true;

      self.ajax.postStream(url, self.search, function (response) {
        if (response.body.result) {
          self.pageTotal = response.body.content.count;
          self.showCount = true;
          self.countOffFlag = false;
        } else {
          self.$message.error(response.body.msg);
        }
      });
    },
    radioChange(data) {
      this.selectData = data;
    },
    close() {
      if (!this.selectData) {
        this.$message.error("请先选择开户行");
        return;
      }
      this.params.callback(this.selectData);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    searchData(obj, resolve) {
      this.search.where = obj;
      this.selectData = null;
      this.showCount = false
      this.getBankList(resolve);
    },
    pageSizeChange(pageSize) {
      this.search.page_size = pageSize;
      this.selectData = null;
      this.getBankList();
    },
    pageChange(page) {
      this.search.page_no = page;
      this.selectData = null;
      this.getBankList();
    },
    getBankList(resolve) {
      this.refreshBtnStatus = true;

      var postData = JSON.parse(JSON.stringify(this.search));

      if (this.params.setWhere) {
        this.params.setWhere(postData); //在setWhere方法里面直接修改postData对象内容
      }

      this.ajax.postStream(
        "/kingdee-web/api/use/ifs/bank/list",
        postData,
        (d) => {
          if (d.body.result && d.body.content) {
            this.bankList = d.body.content.list || [];
            if (!this.showCount) {
              this.pageTotal =
                d.body.content.list.length == this.search.page_size + 1
                  ? this.search.page_no * this.search.page_size + 1
                  : this.search.page_no * this.search.page_size;
            }
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
  },
  mounted() {
    this.getBankList();
  },
};
</script>
<style>
.bank-list-alert {
  display: flex;
}
.bank-list-alert .left {
  min-width: auto !important;
}
</style>