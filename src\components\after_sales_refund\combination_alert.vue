<!-- 修改密码弹框 -->
<template>
<div class="xpt-flex">
	<el-row	class='xpt-top'	:gutter='40'>
		<el-col :span='24'>
			<el-button size='mini' type="primary" @click="submitForm">确认</el-button>
		</el-col>
	</el-row>
	<el-row class="xpt-flex__bottom mgb20">
		<el-form :model="ruleForm2" :rules="rules" ref="ruleForm2" label-width="100px" style="margin-top: 26px;">
			<el-form-item label="物料编码" prop="code">
				<el-input size='mini' v-model="ruleForm2.code"></el-input>
				<el-tooltip v-if='rules.code[0].isShow' class="item" effect="dark" :content="rules.code[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="拍单时间" prop="orderTime">
				<el-date-picker
					v-model="ruleForm2.orderTime"
				    type="date"
				    :editable="false"
				    placeholder="选择拍单时间"
				    size="mini"
				>
				</el-date-picker>
				<el-tooltip v-if='rules.orderTime[0].isShow' class="item" effect="dark" :content="rules.orderTime[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item>
		</el-form>
	</el-row>
</div>
</template>
<script>
import VL from '@common/validate.js'
export default {
	props: ['params'],
    data() {
		return {
			ruleForm2: {
				code: '',
				orderTime: '',
			},
			rules: {
				code: this.VLFun('请填写物料编码'),
				orderTime: this.VLFun('请选择拍单时间'),
			},
		}
    },
    methods: {
		VLFun(msg){
			return VL.isNotBlank({
				required: true,
				self: this,
				msg: msg,
			})
		},
		submitForm() {
			this.$refs.ruleForm2.validate(valid => {
				if (valid){
					this.ajax.postStream('/order-web/api/mergetrade/materiel/getReturnMaterialVO', {
						shopId: this.params.shopId,
						codes: this.params.codes,
						code: this.ruleForm2.code,
						orderTime: new Date(this.ruleForm2.orderTime).toLocaleDateString().replace(/\//g, '-'),

						// shopId: '9630919000980042',
						// codes: '4382582,109451,109456,4381348,2034446',
						// code: 'LS02ZHA3AC004',
						// orderTime: '2018-04-19',
					}, res => {
						if(res.body.result){
							this.$message.success(res.body.msg)
							this.params.callback(res.body.content || {})
							this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}
			})
		},
    },
}
</script>