<template>
  <el-table :data="params.data || []">
    <el-table-column label="单号" prop="content" width="200px"></el-table-column>
    <el-table-column label="结果" prop="result" width="100px">
      <template slot-scope="scope">
        <span v-if="scope.row.result">成功</span>
        <span v-else>失败</span>
      </template>
    </el-table-column>
    <el-table-column label="信息" prop="msg" show-overflow-tooltip></el-table-column>
  </el-table>
</template>
<script>
export default {
  name: 'ExecutionResultTable',
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  }
}
</script>
