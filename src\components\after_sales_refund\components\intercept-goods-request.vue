<template>
  <div class="xpt-flex">
    <div class="xpt-top">
      <el-button type="info" size="mini" @click="submit" v-if="['reason'].includes(formType)">提交</el-button>
      <el-button type="info" size="mini" @click="submitAgain" v-if="['sure'].includes(formType)">继续提交</el-button>
      <el-button type="danger" size="mini" @click="cancel" v-if="['reason', 'sure'].includes(formType)">取消</el-button>
    </div>
    <div class="xpt-flex__bottom">
      <el-form :model="formData" :rules="rules" ref="formDataRef" v-if="['reason'].includes(formType)" label-width="120px">
        <el-row class="mgt20" :gutter="40">
          <el-col :span="18">
            <el-form-item label="截货原因：" prop="reason" >
              <xpt-select-aux v-model='formData.reason' aux_name='intercept_reason'></xpt-select-aux>
              <el-tooltip v-if='rules.reason[0].isShow' effect="dark" :content="rules.reason[0].message"
                placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="需自动推退换货单" prop='ifAutoReturns'>
              <el-select size='mini' placeholder="请选择" v-model='formData.ifAutoReturns' @change="onIfAutoReturnsChange" clearable>
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="退货方式" prop='returnsType' >
              <el-select size='mini' placeholder="请选择" v-model='formData.returnsType' @change="onReturnsTypeChange" :disabled="!formData.ifAutoReturns || formData.ifAutoReturns=='N'">
                <el-option label="退货" value="RETURN"></el-option>
                <el-option label="换货" value="CHANGE"></el-option>
              </el-select>
              <el-tooltip v-if='rules.returnsType[0].isShow' effect="dark" :content="rules.returnsType[0].message"
                placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="换货商品" prop='changeGoodsType'>
              <el-select size='mini' placeholder="请选择" v-model='formData.changeGoodsType' :disabled="formData.returnsType!=='CHANGE'">
                <el-option label="同商品" value="SAME"></el-option>
                <el-option label="不同商品" value="DIFF"></el-option>
              </el-select>
              <el-tooltip v-if='rules.changeGoodsType[0].isShow' effect="dark" :content="rules.changeGoodsType[0].message"
                placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="content-text" v-if="['sure'].includes(formType)">
        {{ contentText }}
      </div>
    </div>
  </div>
</template>
<script>
import validate from '@common/validate.js';
export default {
  props: ["params"],
  data() {
    let self=this;
    return {
      formData: {
        reason: "",
        ifAutoReturns: "Y",
        returnsType: "RETURN",
        changeGoodsType: ""
      },
      contentText: '',
      rules: {
        reason: validate.isNotBlank({
          required: true,
          self: self,
          msg: '截货原因不能为空',
          trigger: 'change'
        }),
        returnsType: [{
          message: '请选择',
          isShow: false,
          validator: function (rule, value, callback) {
            // 数据校验
            if (value || self.formData.ifAutoReturns !== 'Y') {
              self.rules[rule.field][0].isShow = false
              callback();
            } else {
              self.rules[rule.field][0].isShow = true
              callback(new Error(''));
            }
          }
        }],
        changeGoodsType: [{
          message: '请选择',
          isShow: false,
          validator: function (rule, value, callback) {
            // 数据校验
            if (value || self.formData.returnsType !== 'CHANGE') {
              self.rules[rule.field][0].isShow = false
              callback();
            } else {
              self.rules[rule.field][0].isShow = true
              callback(new Error(''));
            }
          }
        }]
      }
    }
  },
  created() {
    let self = this;
    console.log(this.params);
  },
  computed:{
    formType() {
      return this.contentText?'sure':'reason';
    }
  },
  methods: {
    submit() {
      this.$refs.formDataRef.validate((valid) => {
        if (!valid) return;
        if(this.formData.changeGoodsType === 'DIFF') {
            this.$message({
              message: '不支持自动下推换不同商品的退换货跟踪单，请将字段“需自动推退换货单”修改为“否”后再重新提交',
              type: "warning",
            })
            return;
          }
        if (this.params.checkParams) {
          this.ajax.postStream(`/afterSale-web/api/aftersale/interceptNotice/chekRefundOrder`, this.params.checkParams, (res) => {
            if(!res.body.content){
              this.contentText=res.body.msg||'当前退款单的商品编码，与您所选的商品编码不一致，请您检查并核实，确认是否继续提交？';
            }else{
              this.contentText=''
              this.submitAgain();
           }
          },
            (err) => {
              this.$message.error(err);
            })
        }else{
          this.submitAgain();
        }
      });
    },
    submitAgain() {
      this.params.callback && this.params.callback(this.formData);
      this.$root.eventHandle.$emit(
        "removeAlert",
        this.params.alertId
      );
    },
    cancel() {
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    },
    onIfAutoReturnsChange() {
      this.formData.returnsType = undefined;
      this.formData.changeGoodsType = undefined
      this.rules.returnsType[0].isShow = false;
      this.rules.changeGoodsType[0].isShow = false;
    },

    onReturnsTypeChange() {
      this.formData.changeGoodsType = undefined;
      this.rules.changeGoodsType[0].isShow = false;
    }
  }
}
</script>
