<template>
  <div class="xpt-flex">
    <div class="xpt-top">
      <el-button type="info" size="mini" @click="sure" :loading="sureLoading">确认</el-button>
    </div>
    <div class="xpt-flex__bottom">
      <el-form :model="formData" :rules="rules" ref="formData">
        <el-form-item label="上传附件：" label-width="80px" prop="fileList">
          <xpt-upload-v3 uploadBtnText="上传附件" :uploadSize="50" acceptType="usually" :dataObj="uploadDataObj"
            :disabled="false" :ifMultiple="true" :showSuccessMsg="true" @uploadSuccess="uploadSuccess" btnType="success"
            style="display: inline-block; margin: 0px 10px"></xpt-upload-v3>
        </el-form-item>
      </el-form>
      <div class="file-box">
        <div v-for="item in uploadFileList" :key="item.path" class="file-item">
          <a class="file-item__name" href="javascript:;" @click="downloadFile(item)"> {{ item.name }}</a>
          <div class="file-item__delete" @click="deleteFile(item)"><i class="el-icon-close"></i></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from '@common/Fn.js'
export default {
  props: ["params"],
  data() {
    const self = this;
    return {
      formData: {
        fileList:[]
      },
      rules: {
        fileList: validate.isNotBlank({
          self: self,
        }),
      },
      sureLoading: false,
      uploadDataObj: {
        parent_name: "自动提交未发销售折让",
        parent_no: `REFUND_AUTO_SUBMIT_SALE_DISCOUNT_UPLOAD`, //主要通过该参数获取附件列表
        child_name: null,
        child_no: null,
        content: {},
      },
      uploadFileList: [],
    }
  },
  methods: {
    //上传成功返回结果
    uploadSuccess(result) {
      this.uploadFileList.unshift(...result);
      this.formData.fileList=this.uploadFileList;
    },
    downloadFile(file) {
      const { path, name } = file;
      Fn.download(path, name);
    },
    deleteFile(file) {
      this.$confirm("是否确定删除已选附件？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const index = this.uploadFileList.findIndex(item => item.path === file.path);
          this.uploadFileList.splice(index, 1);
        })
        .catch(() => { });
    },
    getParams(){
      const fileInfoList = this.uploadFileList.map(item => {
        return {
          "file_type": item.file_type,
          "name": item.name,
          "size": item.size,
          "ext_data": null,
          "path": item.path,
        }
      })
      const orderInfoList = this.params.list.map(item => {
        const group_value_json = JSON.stringify({
          "parent_name": "AFTER_ORDER",
          "parent_no": item.bill_no,
          "child_name": "REFUNDAPPLYBILLDETAIL",
          "child_no": item.bill_no,
          "content": [
            {
              "name": "bill_no",
              "alias": "单据编号",
              "value": item.bill_no|| null,
              "active": false
            },
            {
              "name": "merge_trade_no",
              "alias": "合并订单号",
              "value": item.merge_trade_no|| null,
              "active": false
            },
            {
              "name": "after_order_no",
              "alias": "售后单号",
              "value": item.after_order_no || null,
              "active": false
            }
          ]
        })
        return {
          "order_no": item.bill_no,
          "sub_order_no": item.bill_no,
          group_value_json,
        }
      });
      return {
        fileInfoList,
        orderInfoList
      };
    },
    sure() {
      if (this.uploadFileList.length == 0) {
        this.$message.warning('请上传附件')
        return
      }
      this.$refs.formData.validate((valid) => {
        if (!valid) return;
        this.sureLoading = true;
        const params=this.getParams();
        const url = '/file-iweb/api/cloud/file/uploadBatch';
        this.ajax.postStream(
          url,
          params,
          (res) => {
            if (res.body.result) {
              this.params.callback && this.params.callback();
              this.$message.success(res.body.msg || '操作成功');
              this.$root.eventHandle.$emit(
                "removeAlert",
                this.params.alertId
              );
            } else {
              this.$message.error(res.body.msg);
            }
            this.sureLoading = false;
          },
          (err) => {
            this.$message.error(err);
            this.sureLoading = false;
          }
        );
      });
    }
  },
  created() {
    console.log('this.params :>> ', this.params);
  },
};
</script>
<style scoped>
.mgt20 {
  margin-top: 20px;
}

.file-box {
  padding: 5px;
  padding-left: 90px;
  box-sizing: border-box;
  overflow: auto;
  height: 120px;

  .file-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .file-item__name {
      cursor: pointer;
      margin-right: 10px;
      display: inline-block;
      word-break: break-word;
    }

    .file-item__delete {
      cursor: pointer;
    }
  }
}
</style>
