<template>
  <div>
    <div class="head-box">
      <el-button type="primary" size="mini" @click="handleSuccess('ruleForm')"
        >确认</el-button
      >
    </div>
    <el-form :model="form" :rules="rules" ref="ruleForm" class="form-box">
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          resize="none"
          placeholder="请输入内容"
          :maxlength="100"
          :show-word-limit="true"
          :value="form.remark"
          @change="handleInput"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import listHeader from "@/components/customized_base_material_goods/components/components/list-header";
export default {
  components: {
    listHeader,
  },
  props: ["params"],
  data() {
    return {
      form: {
        remark: "",
      },
      rules: {
        remark: [
          { required: true, message: "请输入备注信息", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleInput(e) {
      this.form.remark = e.trim();
    },
    handleSuccess(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.params.callback(this.form.remark);
          this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
        } else {
          this.$message.error("请输入备注信息");
        }
      });
    },
  },
};
</script>

<style scoped>
.remark_dialog {
  z-index: 10 !important;
}

.remark_dialog .dialog-footer {
  display: flex;
}

.remark_dialog .el-button {
  flex: 1;
}

.head-box {
  padding: 4px 10px 4px 0;
  border-bottom: 1px solid #eee;
}

.form-box {
  height: 850px;
}
</style>
<style>
.remark_dialog + .v-modal {
  z-index: 9 !important;
}
</style>