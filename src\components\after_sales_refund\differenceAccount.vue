// 退款申请单-差价核算单
<template>
    <div class="xpt-flex" id="couponAlert">
        <el-row class="xpt-top" :gutter="40" v-if="dialogSource">
            <el-button type="primary" size="mini" @click="confirm"  >确认</el-button>
            <el-button type="primary" size="mini" @click="cancel"  >取消</el-button>
        </el-row>
        <div class="differenceAccountArea">
            <el-form class="differenceForm" label-position="right" :rules="differenceAccountRules" :inline="true" ref="searchObj" id="differenceAccountDialog">
                <el-col :span="10">
                    <el-form-item label="虚拟拍单时间" prop="searchObj.order_date" > 
                        <!-- :disabled='true' :editable='false' -->
                        <el-date-picker v-model="searchObj.order_date" :clearable="false" @change="changeVirtualCreated" style="width: 150px" type="datetime" placeholder="选择日期" size='mini' :disabled="!dialogSource" :picker-options='enableDateOptions'></el-date-picker>
                    </el-form-item>
                    <el-button type="primary" size="mini" @click="refesh" v-if="dialogSource" style="margin-top: 6px;">查询差价商品</el-button>
                </el-col>
                
            </el-form>
            <el-tabs class="differenceTabArea" v-model="selectTab3">
                <el-tab-pane style="height: 100%;" label='基础信息' name='differenceAccountBasic' class='xpt-flex'>
                    <!-- 
                        @page-size-change="basicPageChange"
                        @current-page-change="basicCurrentPageChange"
                        :pageTotal="accountBasicCount" -->
                    <xpt-list2
                        ref='differenceAccountBasic'
                        :data='accountBasicList' 
                        :btns='[]' 
                        :colData='dialogSource ? accountBasicCols : accountBasicColsList' 
                        isNeedClickEvent 
                        :pageTotal="null"
                        :selection='basicSelction'
                        @selection-change="basicSelectChange"
                        :showHead='false'
                        :selectable="selectableFun"
                        orderNo 
                        :searchPage="''"
                    >
                        <template slot='if_cross_shop' slot-scope='scope'>
                           
                            <el-switch v-model="scope.row.if_cross_shop" v-if="scope.row.materia_number" on-text="是" off-text="否" on-value="Y" off-value="N" @change='crossShopChange(scope.row)'></el-switch>
                        </template>
                        <template slot='cross_shop_name' slot-scope='scope'>
                            <xpt-input v-model='scope.row.cross_shop_name' v-if="scope.row.materia_number" icon='search' readonly :disabled="scope.row.if_cross_shop !=='Y'" :on-icon-click='(()=>{
                                openShop(scope.row);
                            })' size='mini' style="width:99%;"></xpt-input>
                        </template>
                    </xpt-list2>
                </el-tab-pane>
                <el-tab-pane style="height: 100%;" label='活动优惠' name='differenceAccountCoupon' class='xpt-flex'>
                    <xpt-list 
                        ref='differenceAccountCoupon'
                        :data='accountCouponList' 
                        :pageTotal="null"
                        :selection='couponSelction'
                        :btns='accountCouponBtns' 
                        :colData='accountCouponCols' 
                        isNeedClickEvent 
                        orderNo 
                        @selection-change="couponSelectChange"
                        :searchPage="''"
                    >
                    </xpt-list>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<script>
export default {
    props: ['differenceAccountObj', 'params'],
    data() {
        let self = this;
        return {
            dialogSource: false,
            couponSelction: 'checkbox',
            basicSelction: 'checkbox',
            basicSelectList: [],
            couponSelectList: [],
            differenceAccountRules: {},
            selectTab3: 'differenceAccountBasic',
            searchObj: {
                order_date: null
            },
            accountBasicObj:{
                page_size: 50,
                page_no: 1
            },
            enableDateOptions:{
                disabledDate(time){
                    return time.getTime() < new Date(self.operate_time_begin).getTime() || time.getTime() > new Date(self.operate_time_end).getTime();
                },
                date:(function(){
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth()+1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
                    return new Date(time);
                })()
            },
            accountBasicList: [],
            accountBasicCols: [
                {
                    label: '商品编码',
                    prop: 'materia_number',
                    width: 120,
                }, {
                    label: '商品名称',
                    prop: 'materia_name',
                    width: 100,
                },{
                    label: '商品规格描述',
                    prop: 'materia_specifications',
                },{
                    label: '原系统售价',
                    prop: 'stand_price',
                },{
                    label: '原实际售价',
                    prop: 'act_price_v2',
                },{
                    label: '新系统售价',
                    prop: 'virtual_stand_price',
                },{
                    label: '使用优惠',
                    prop: 'virtual_discount_amount',
                    width: 100
                },{
                    label: '新实际售价',
                    prop: 'virtual_act_price_price',
                },{
                    label: '可退差价',
                    prop: 'max_diff_price',
                    width: 100
                },{
                    label: '退款申请金额',
                    prop: 'apply_amount',
                    width: 92,
                    elInput:true,
					bool: true,
					disabled(row) {
						return !row.if_could_diff_price
                    },
                    // blur(row){
                    //     if (!(row.apply_amount > 0 && row.apply_amount <= row.max_diff_price)){
                    //         let msg = '退款申请金额不符合：0<退款申请金额≤' + row.max_diff_price
                    //         self.$message.error(msg)
                    //     }
                    // },
                }, {
                    label: '是否跨店退款',
                    slot: 'if_cross_shop',
                }, {
                    label: '跨店退差店铺',
                    slot: 'cross_shop_name',
                }, {
                    label: '已退差价',
                    prop: 'refunded_diff_price',
                    width: 80,
                }
            ],
            accountBasicColsList: [
                {
                    label: '商品编码',
                    prop: 'materia_number',
                    width: 120,
                }, {
                    label: '商品名称',
                    prop: 'materia_number',
                    width: 100,
                },{
                    label: '商品规格描述',
                    prop: 'materia_specifications',
                },{
                    label: '原系统售价',
                    prop: 'stand_price',
                },{
                    label: '原实际售价',
                    prop: 'act_price',
                },{
                    label: '新系统售价',
                    prop: 'virtual_stand_price',
                },{
                    label: '使用优惠',
                    prop: 'virtual_discount_amount',
                },{
                    label: '新实际售价',
                    prop: 'virtual_act_price_price',
                },{
                    label: '可退差价',
                    prop: 'max_diff_price',
                },
            ],
            accountCouponList: [],
            accountCouponBtns: [
                {
                    type: 'primary',
                    txt: '添加',
                    click: self.add,
                    disabled(){
                      return !self.dialogSource || !(self.accountBasicList.length > 0)
                    }
                }, {
                    type: 'primary',
                    txt: '删除',
                    click: self.remove,
                    disabled(){
                    return !self.dialogSource || !(self.accountBasicList.length > 0)
                    }
                }, /*{
                    type: 'primary',
                    txt: '刷新可退差价',
                    click: self.save,
                    disabled(){
                    return !self.dialogSource || !(self.accountBasicList.length > 0)
                    }
                }*/
            ],
            accountBasicCount: 0,
            accountCouponCols: [
                {
                    label: '优惠活动编码',
                    prop: 'discount_no',
                },{
                    label: '优惠项目编码',
                    prop: 'discount_item_no',
                    width: 130
                },{
                    label: '优惠活动',
                    prop: 'activity_name',
                    width: 150,
                    redirectClick(row) {
                      self.$root.eventHandle.$emit('alert', {
                        params: {
                          act_id: row.activity_id
                        },
                        component: () => import('@components/discount/view'),
                        style: 'width:1000px;height:560px',
                        title: '优惠详情'
                      })
                    },
                },{
                    label: '优惠影响范围',
                    prop: 'discount_effect_area',
                    format: 'auxFormat',
                    formatParams: 'DISCOUNT_EFFECT_AREA',
                },{
                    label: '优惠类型',
                    prop: 'activity_type',
                    format: 'auxFormat',
                    formatParams: 'discountCategory',
                },{
                    label: '优惠子类型',
                    prop: 'activity_subclass',
                    format: 'auxFormat',
                    formatParams: 'discountSubclass',
                },{
                    label: '项目细类',
                    prop: 'item_type_detail_code',
                    format: 'auxFormat',
                    formatParams: 'ITEM_TYPE_DETAIL',
                },{
                    label: '优惠金额',
                    prop: 'discount',
                    elInput: true,
                    bool: true,
                    disabled(row) {
                    /*
                      if_change_price（是否能够改变金额）为Y时可以编辑
                      audit_status（审核状态）为N时可以编辑
                      当满足上面三个状态下时可以编辑
                      tempId，临时ID，有该值时无需考虑以上条件
                      scope.row.tempId?false:(params.ifAddEdition&&scope.row.audit_status==='N'
                      &&scope.row.if_change_price === 'Y'?false:true)
                                */
                      if (row.activity_type === 'REFUND') {
                        if (row.subtractMoney >= 0 && row.if_change_price==='Y' && row.subDiscount === null) {
                          return false
                          } else{
                            return true
                          }
                      }
                      if(row.if_change_price=='Y') {
                        return row.tempId ? false : (row.audit_status === 'N' && row.ifEachFull === 'Y' && row.if_change_price === 'Y' ? false : true)
                      } else {
                        return true;
                      }
                    },
                    change(row) {
                        self.checkDiscountPrice(row)
                    },
                    width: 136
                },{
                    label: '优惠券数量',
                    prop: 'num',
                    elInput:true,
                    bool: true,
                    disabled(row) {
                      return !(row.ifEachFull==='Y' && row.if_present === 'N')
                    },
                    blur(row){
                        let reg = /^[1-9][0-9]{0,6}$/;
                        if (!row.num) {
                            self.$message.error('请输入优惠券数量');
                            return true
                        }
                        if(!reg.test(row.num)){
                            self.$message.error('当前输入不符合规范，请输入1-7位正整数')
                            return
                        }
                        // self.$message.success('优惠券数量修改成功，刷新可退差价后生效！')
                        self.save()
                        return reg.test(row.num)
                    },
                    width: 136
                }
            ],
            operate_time_begin: '',
            operate_time_end: '',
            created: '',
            end_time: '',
            ifAdd: true,
            priceListOrders: [],
            couponAlerts: [],
            alereadyObj: {}
        }
    },
    created(){
        if (this.params && this.params.priceDiffSource && this.params.priceDiffSource == 'button') {
            this.dialogSource = true
            this.searchObj.order_date = this.params.differenceAccountObj.order_date
        } else {
            this.dialogSource = false
            this.couponSelction = ''
            this.basicSelction = ''
        }
    },
    mounted(){
        this.$parent.alerts.forEach(item => {
            this.couponAlerts.push(item.params.alertId)
        })
        if (this.params && this.params.priceDiffSource && this.params.priceDiffSource == 'button') {
            // this.getGoodsDetailList()
            this.accountCouponList = this.params.differenceAccountObj.accountCouponList
            this.accountBasicList = this.params.differenceAccountObj.accountBasicList
            // this.$set(this.searchObj, 'order_date', this.params.differenceAccountObj.order_date)
            // this.searchObj.order_date = this.params.differenceAccountObj.order_date
            // this.operate_time_begin = this.params.operate_time_begin
            // this.operate_time_end = this.params.operate_time_end
            this.params.goodsList = this.accountBasicList
            this.getDiffPriceOrderDate()
            this.created = this.params.created
            this.end_time = this.params.end_time
            this.ifAdd = !this.searchObj.order_date
            this.priceListOrders = this.params.differenceAccountObj.priceListOrders
            this.alereadyObj = {
              accountCouponList: JSON.parse(JSON.stringify(this.accountCouponList)),
              accountBasicList: JSON.parse(JSON.stringify(this.accountBasicList)),
            }
            // console.log('点击按钮操作',this.differenceAccountObj, 'this.params.differenceAccountObj', this.params.differenceAccountObj, 'this.searchObj.order_date', this.searchObj.order_date, this.accountBasicList)
        } else{
            this.accountCouponList = this.differenceAccountObj.accountCouponList
            this.accountBasicList = this.differenceAccountObj.accountBasicList
            this.params.goodsList = this.accountBasicList
            this.alereadyObj = {
              accountCouponList: JSON.parse(JSON.stringify(this.accountCouponList)),
              accountBasicList: JSON.parse(JSON.stringify(this.accountBasicList)),
            }
            // console.log('详情页tab页签',this.differenceAccountObj, this.accountBasicList)
        }
    },
    methods: {
        crossShopChange(row){
            if(row.if_cross_shop ==="N"){
                row.cross_shop_id = '';
                row.cross_shop_name = '';
                row.cross_shop_code = '';
            }
        },
        // 选择店铺
		openShop(row) {
			let self = this;
            if(row.if_cross_shop ==="N"){
                return;
            }
			let params = {
				callback(data) {
                    console.log(data);
					row.cross_shop_id = data.shop_id;
					row.cross_shop_name = data.shop_name;
					row.cross_shop_code = data.shop_code;
				},
                selection: 'radio'
			}
			this.$root.eventHandle.$emit('alert', {
				params: params,
				component:() => import('@components/shop/list'),
				style: 'width:800px;height:500px',
				title: '店铺列表'
			})
		},
        // 获取差价核单tab-虚拟拍单时间范围
        getDiffPriceOrderDate () {
            let postData = {
                merge_trade_id:this.params.mergeNumberId,
                merge_trade_no:this.params.mergeNumber,
            }, self = this
            this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/diffPriceOrderDate', postData, res => {
                if(res.body.result) {
                    self.operate_time_begin = res.body.content.operate_time_begin || ''
                    self.operate_time_end = res.body.content.operate_time_end || ''
                    let dateTime = this.params.differenceAccountObj.order_date ? this.params.differenceAccountObj.order_date : self.operate_time_begin
                    this.$set(this.searchObj, 'order_date', dateTime)
                } else {
                    self.operate_time_begin = ''
                    self.operate_time_end = ''
                    // self.$message.error(res.body.msg)
                }
            },err => {
                self.operate_time_begin = ''
                self.operate_time_end = ''
                // self.$message.error(err)
            });
        },
        changeVirtualCreated() {
            this.$message.success('请点击查询差价商品后生效哦')
        },
        selectableFun(row, index) {
			// 控制是否可以勾选
			if(row.entry_id==='合计' || !row.if_could_diff_price) {
				return false;
			} else {
				return true;
			}
        },
        getGoodsDetailList (result) {
            let postData = {
                //page_no:this.accountBasicObj.page_no,
                //page_size:this.accountBasicObj.page_size,
                merge_trade_id:this.params.mergeNumberId,
                merge_trade_no:this.params.mergeNumber,
                virtual_created:this.searchObj.order_date,
                shop_id: this.params.shopId,
                apply_id: this.params.apply_id
            }, self = this
            this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/diffPriceOrder', postData, res => {
                if(res.body.result) {
                    let diffPriceOrders = res.body.content.diffPriceOrders || []
                    let diffPriceDiscounts = res.body.content.diffPriceDiscounts || []
                    diffPriceDiscounts.forEach(item => {
                      item.discount = item.subtractMoney
                    })
                    diffPriceOrders.forEach(item => {
                        item.virtual_act_price_price = self.isNull(item.virtual_discount_amount) ? item.virtual_stand_price : (item.virtual_stand_price - item.virtual_discount_amount).toFixed(2)
                        item.max_diff_price = (item.act_price_v2 - item.virtual_act_price_price).toFixed(2)
                    })
                    let totalCols = ['max_diff_price'], maxDiffPriceTotal = 0, virtualDiscountAmountTotal = 0
                    diffPriceOrders.forEach(item => {
                        maxDiffPriceTotal = Number(maxDiffPriceTotal) + Number(item.max_diff_price)
                        virtualDiscountAmountTotal = Number(virtualDiscountAmountTotal) + Number(item.virtual_discount_amount)
                    }) 
                    self.accountBasicList = diffPriceOrders.concat({max_diff_price:'总计：' + (maxDiffPriceTotal).toFixed(2), virtual_discount_amount:'总计：' + (virtualDiscountAmountTotal).toFixed(2)})
                    self.accountCouponList = diffPriceDiscounts
                    self.params.goodsList = self.accountBasicList
                    // self.operate_time_begin = res.body.content.operate_time_begin
                    // self.operate_time_end = res.body.content.operate_time_end
                    self.created = res.body.content.created
                    self.end_time = res.body.content.end_time
                    self.searchObj.order_date = res.body.content.virtual_created
                    self.accountBasicList[self.accountBasicList.length - 1].entry_id = '合计'
                    // 直接调用校验+重算接口（checkCouponOfGoods 但优惠清空）
                    if (result == 'refresh') { //false代表校验重算失败，需要刷新商品行不需要调用重算。refresh代表重新下单，刷新商品行成功后调用校验重算
                        self.accountCouponList = []
                        self.params.goodsList = []
                        self.checkCouponOfGoods()
                    }
                } else {
                    self.accountBasicList = []
                    self.accountCouponList = []
                    self.params.goodsList = []
                    self.$message.error(res.body.msg)
                }
            },err => {
                self.accountBasicList = []
                self.accountCouponList = []
                self.params.goodsList = []
                self.$message.error(err)
            });
        },
        refesh () {
            let self = this
            if (!this.searchObj.order_date) {
                this.$message.error('请先选择虚拟拍单时间！')
                return
            }
            if (this.ifAdd) {
                this.params.differenceAccountObj.order_date = this.searchObj.order_date
                self.getGoodsDetailList('refresh')
                return
            }
            this.$root.eventHandle.$emit('openDialog', {
                ok() {
                    self.params.differenceAccountObj.order_date = self.searchObj.order_date
                    self.getGoodsDetailList('refresh')
                },
                beforeClose() {
                    self.$parent.$root.$children[0].dialog.visible = false
                    // self.ifDataChange = false
                },
                okTxt: '是',
                no() {
                  // self.ifDataChange = false
                },
                noTxt: '否',
                txt: '查询差价商品后会删除历史差价退款明细和活动优惠',
                body: '确认查询差价商品吗？',
                noCancelBtn: true
              })
        },
        basicSelectChange(s) {
            this.basicSelectList = s
        },
        couponSelectChange (s) {
            this.couponSelectList = s
        },
        basicPageChange(size){
            this.accountBasicObj.page_size = size
        },
        basicCurrentPageChange(page){
            this.accountBasicObj.page_no = page
        },
        // 表头-确认按钮
        confirm () {
            // 退款申请金额校验
            if(this.basicSelectList.length == 0) {
                this.$message.error('请至少选择一行商品行')
                return
            }
            let flag = false;
            flag = this.basicSelectList.some(item=>{
                return item.if_cross_shop === "Y" && !item.cross_shop_id
            });
            if(flag){
                this.$message.error('是否跨店退款为是时，跨店退差店铺必填');
                return
            }
            let msgOfNumber = ''
            this.basicSelectList.some(item => {
              if (!(/^(([1-9][0-9]+)|([1-9])|(0\.\d{0,2})|([1-9]\.\d{0,2})|([1-9][0-9]+\.\d{0,2}))$/.test(item.apply_amount))) {
                msgOfNumber = '退款金额需大于0，且只允许两位小数'
                return true
              } else if (!(Number(item.apply_amount) > 0 && Number(item.apply_amount) < Number(item.act_price))) {
                msgOfNumber = '退款申请金额不符合：退款申请金额<原商品行实际售价'
                return true
              }
            })
            if (msgOfNumber) {
              this.$message.error(msgOfNumber)
              return
            }
            let msg = '', self = this
            // 校验活动优惠中的优惠券数量
            this.accountCouponList.some((obj,idx) => {
                let reg = /^[1-9][0-9]{0,6}$/;
                if (!obj.num) {
                    msg = `活动优惠第${Number(idx) + Number(1)}行需要填写优惠券数量`
                    return true
                }
                if(!reg.test(obj.num)){
                    msg = `活动优惠第${Number(idx) + Number(1)}行优惠券数量填写不符合规范，请输入1-7位正整数`
                    return
                }
            })
            if(msg){
                this.$message.error(msg)
                return
            }
            if (!(this.searchObj.order_date === this.params.differenceAccountObj.order_date)) {
                this.searchObj.order_date = this.params.differenceAccountObj.order_date
            }
            // 对比下基础信息和活动优惠前后文件，看是否有变化 （优惠参照合并订单对比）
            this.params.callback({
                basicSelectList: this.basicSelectList, 
                accountBasicList: this.accountBasicList , 
                accountCouponList: this.accountCouponList,
                order_date: this.searchObj.order_date
            })
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
            
        },
        // 表头-取消按钮
        cancel () {
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
        },
        // 优惠添加
        add () {
            if (!this.searchObj.order_date) {
                this.$message.error('请先选择虚拟拍单时间！')
                return
            }
            this.listAvailableActCheckPermisson()
        },
        getDiscountSubList(postData){
          let self = this
          this.ajax.postStream('/user-web/api/personGroupDiscount/getDiscountSubType', postData, res => {
                if(res.body.result) {
                    let permissionList = res.body.content
                    if (permissionList.length == 0) {
                        self.$message.error('抱歉！您没有可以添加的优惠')
                        return
                    } else {
                        self.listAvailableActDialog(permissionList)
                    }
                } else {
                    if (res.body.msg) {
                        self.$message.error(res.body.msg)
                        return
                    } else {
                        self.$message.error('抱歉，您没有客服添加优惠的权限')
                        return
                    }
                }
            },err => {
                self.$message.error(err)
            });
        },
        listAvailableActCheckPermisson () {
            let business_mode_arr = [],self = this
            let postData = {
                userId: this.params.saleman_id,
                salesmanTypes: ['SALES_PERSON']
            }
            this.ajax.postStream('/user-web/api/personGroupDiscount/getDiscountSubType', postData, res => {
                if(res.body.result) {
                    let permissionList = res.body.content
                    if (permissionList.length == 0) {
                        // self.$message.error('抱歉！您没有可以添加的优惠')
                        let postData2 = {
                          userId: self.getEmployeeInfo('id'),
                          salesmanTypes: ['SALES_PERSON']
                        }
                        self.getDiscountSubList(postData2)
                        return
                    } else {
                        self.listAvailableActDialog(permissionList)
                    }
                } else {
                    if (res.body.msg) {
                        self.$message.error(res.body.msg)
                        return
                    } else {
                        // self.$message.error('抱歉，您没有客服添加优惠的权限')
                        let postData2 = {
                          userId: self.getEmployeeInfo('id'),
                          salesmanTypes: ['SALES_PERSON']
                        }
                        self.getDiscountSubList(postData2)
                        return
                    }
                }
            },err => {
                self.$message.error(err)
                // self.onlistAvailableAct = false
            });
        },
        checkDiscountPrice(row){
            let reg = /^(([1-9][0-9]+)|([1-9])|(0\.\d{0,2})|([1-9]\.\d{0,2})|([1-9][0-9]+\.\d{0,2}))$/;
            if(!reg.test(row.discount)){
                this.$message.error('优惠金额需大于0，且只允许两位小数')
            } else {
              this.save()
            }
            return reg.test(row.discount)
        },
        listAvailableActDialog (permissionList) {
            let orderList = [], basicIdx = this.accountBasicList.length
            this.accountBasicList.forEach((item, idx)=> {
              if (idx != (basicIdx - 1)) {
                orderList.push(item)
              }
            })
            let invalid_discount_subclass_list = __AUX.get('diff_price_except_discount').reduce((arr, item) => {
              if (item.status == 1) {
                arr.push(item.code)
              }
              return arr
            }, [])
            let data = {
                merge_trade_id: this.params.mergeNumberId,
                order_date: this.searchObj.order_date,
                payoff_date: this.searchObj.order_date,
                shop_id: this.params.originalShopId,
                sys_trade_id: this.params.sys_trade_id,
                discount_subclass_list: permissionList,
                select_type: 'MERGE',
                order_business_mode: '',
                total_price: '',
                if_diff_price: true,
                invalid_discount_subclass_list: invalid_discount_subclass_list,
                orders: orderList,
                if_peel_off_trade_up: true,
            }
            let self = this, count=0, num=0
            , params = {
                postData: data,
                alreadyCouponList: [],
                merge_trade_id: data.merge_trade_id,
                total_price: data.total_price,
                permissionList: permissionList,
                // 所选的销售订单
                selectOrder: {sys_trade_id: this.params.sys_trade_id, sys_trade_no: this.params.sys_trade_no, merge_trade_id: this.params.mergeNumberId, merge_trade_no: this.params.mergeNumber},
                goodsList: this.params.goodsList,
                alertResour: 'PRICE_DIFF',
                closee(d){
                    console.log('chajia ',d)
                },
                close(d) { 
                    for(let v of d){
                        let postData = {
                            discount_no: v.actDiscountCode,
                            discount_item_no: v.discountItemCode,
                            activity_name: v.actDiscountName,
                            discount_effect_area: v.discountEffectArea,
                            activity_type: v.discountType,
                            activity_subclass: v.discountSubType,
                            item_type_detail_code: v.item_type_detail_code,
                            num: 1,
                            ifEachFull:v.ifEachFull, 
                            if_present: v.if_present, // 是否礼品
                            discount: v.subtractMoney === null ? 0 : v.subtractMoney, //(v.discount === null && v.subtractMoney === null) ? 0 : (v.discount ? 0 : v.subtractMoney),
                            tempId: Number(new Date().getTime()) + Number(Math.floor(Math.random() * 10)),
                            apply_id: self.params.apply_id,
                            order_entry_id: v.entryId,
                            subtractMoney: v.subtractMoney, // 减元金额
                            subDiscount: v.discount, //打折的折扣为优惠金额是否可修改做控制
                            if_change_price:v.ifAmountChange,//是否改变价格
                            audit_status:v.ifNeefAudit === 'Y' ? 'N' : 'Y',//是否需要审核
                            merge_trade_id: self.params.mergeNumberId,
                            sys_trade_id: self.params.sys_trade_id,
                            discount_item_id: v.discountItemId,
                            activity_id: v.actDiscountId,
                        }
                        self.accountCouponList.push(postData);
                    }
                    self.save()
                },
            };
            this.ajax.postStream('/price-web/api/actDiscount/listAvailableAct', data, res => {
                if(res.body.result) {
                    let content =  res.body.content
                    params.listData = content.list
                    if (content.pageResult && content.pageResult.list && content.pageResult.list.length > 0 ) {
                        params.listData = params.listData.concat(content.pageResult.list)
                        params.listData.forEach(item => {
                            item.sys_trade_id = data.sys_trade_id
                        })
                    }
                    if (content.pageResultMerge && content.pageResultMerge.list && content.pageResultMerge.list.length > 0 ) {
                        params.listData = params.listData.concat(content.pageResultMerge.list)
                    }
                    self.$root.eventHandle.$emit('alert', {
                        params: params,
                        component: () => import("@components/order/couponActionDialog"),
                        style: 'width:1251px;height:600px',
                        title: '优惠活动'
                    });
                    self.$message.success(res.body.msg)
                } else {
                    // self.onlistAvailableAct = false
                    self.$message.error(res.body.msg)
                }
            },err => {
                // self.onlistAvailableAct = false
                self.$message.error(err)
            });
        },
        // 刷新可退差价接口
        save () {
            let msg = '', self = this
            this.accountCouponList.some((obj,idx) => {
                let reg = /^[1-9][0-9]{0,6}$/;
                if (!obj.num) {
                    msg = `活动优惠第${Number(idx) + Number(1)}行需要填写优惠券数量`
                    return true
                }
                if(!reg.test(obj.num)){
                    msg = `活动优惠第${Number(idx) + Number(1)}行优惠券数量填写不符合规范，请输入1-7位正整数`
                    return
                }
            })
            if(msg){
                this.$message.error(msg)
                return
            }
            // if (this.accountCouponList.length > 0) {
                this.checkCouponOfGoods()
            // } else {
            //     this.$message.error('请先添加至少一行活动优惠后，再操作可退差价')
            //     return
            // }
        },
        remove () {
            if (this.couponSelectList.length == 0) {
                this.$message.error('请至少选择一行数据')
                return
            }
            let self = this
            this.couponSelectList.forEach((item,index) => {
                self.accountCouponList.forEach((coupon,idx) => {
                    if (item.id) {
                      if (item.id === coupon.id) {
                        self.accountCouponList.splice(idx, 1)
                      }
                    } else if (!(item.tempId === null)) {
                      if (item.tempId === coupon.tempId) {
                        self.accountCouponList.splice(idx, 1)
                      }
                    }
                })
            })
            this.save()
            this.$message.success('删除优惠成功，请刷新可退差价！')
        },
        // 优惠校验+重算得出可退差价
        checkCouponOfGoods () {
            let list = [], len = this.accountBasicList.length
            for (let key = 0; key < len-1; key ++) {
                list.push(this.accountBasicList[key])
            }
            this.accountCouponList.forEach(item => {
                // delete item.actDiscountCode
                // delete item.actDiscountId
                // delete item.ifEachFull
                // delete item.tempId
                // delete item.if_present
                // delete item.num
            })
            console.log('oh no', list)
            let aftersaleDiffPriceVO = {
                virtual_created: this.searchObj.order_date,
                operate_time_begin: this.operate_time_begin,
                operate_time_end: this.operate_time_end,
                diffPriceOrders: list,
                diffPriceDiscounts: this.accountCouponList,
                end_time: this.end_time,
                created: this.created,
                merge_trade_id: this.params.mergeNumberId,
                priceListOrders: this.priceListOrders,
                sys_trade_id: this.params.sys_trade_id
            }, self = this
            this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/checkDiffPriceOrder', aftersaleDiffPriceVO, res => {
                if(res.body.result) {
                    let diffPriceOrders = res.body.content.diffPriceOrders || []
                    let diffPriceDiscounts = res.body.content.diffPriceDiscounts || []
                    diffPriceDiscounts.forEach(item => {
                      item.discount = item.subtractMoney
                    })
                    diffPriceOrders.forEach(item => {
                        item.virtual_act_price_price = self.isNull(item.virtual_discount_amount) ? item.virtual_stand_price : (item.virtual_stand_price - item.virtual_discount_amount).toFixed(2)
                        item.max_diff_price = (item.act_price_v2 - item.virtual_act_price_price).toFixed(2)
                    })
                    let totalCols = ['max_diff_price'], maxDiffPriceTotal = 0, virtualDiscountAmountTotal = 0
                    diffPriceOrders.forEach(item => {
                        maxDiffPriceTotal = Number(maxDiffPriceTotal) + Number(item.max_diff_price)
                        virtualDiscountAmountTotal = Number(virtualDiscountAmountTotal) + Number(item.virtual_discount_amount)
                    }) 
                    self.accountBasicList = diffPriceOrders.concat({max_diff_price:'总计：' + (maxDiffPriceTotal).toFixed(2), virtual_discount_amount:'总计：' + (virtualDiscountAmountTotal).toFixed(2)})
                    self.accountCouponList = diffPriceDiscounts
                    self.params.goodsList = self.accountBasicList
                    self.$message.success(res.body.msg)
                    self.alereadyObj = {
                      accountCouponList: JSON.parse(JSON.stringify(self.accountCouponList)),
                      accountBasicList: JSON.parse(JSON.stringify(self.accountBasicList)),
                    }
                } else {
                    self.accountBasicList = JSON.parse(JSON.stringify(self.alereadyObj.accountBasicList))
                    self.accountCouponList = JSON.parse(JSON.stringify(self.alereadyObj.accountCouponList))
                    self.params.goodsList = JSON.parse(JSON.stringify(self.alereadyObj.accountBasicList))
                    // self.getGoodsDetailList('false')
                    // self.alereadyObj = {
                    //   accountCouponList: [],
                    //   accountBasicList: [],
                    // }
                    self.$message.error(res.body.msg)
                }
            },err => {
                self.accountBasicList = []
                self.accountCouponList = []
                self.params.goodsList = []
                self.alereadyObj = {
                  accountCouponList: [],
                  accountBasicList: [],
                }
                self.getGoodsDetailList('false')
                self.$message.error(err)
            });
        },
        isNull (value) {
            if (!value && typeof value != "undefined" && value != 0) {
                return true
            } else {
                return false
            }
        },
    },
    watch: {
    },
    beforeDestroy(){
        if ((new Set(this.couponAlerts).has(this.params.alertId))) {
            this.params.close({
                accountBasicList: this.accountBasicList , 
                accountCouponList: this.accountCouponList,
                order_date: this.searchObj.order_date
            })
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
        }
    },
}


</script>
<style type="text/css" >
#differenceAccountDialog .el-form-item__content {
    width: 150px;
}
.el-dialog__wrapper {
    background-color: transparent
}
.differenceAccountArea {
    height: calc(100% - 35px);
    width: 100%;
}
.differenceForm {
    height: 40px;
}
.differenceTabArea {
    height:calc(100% - 58px);
}
.differenceTabArea .el-tabs__content {
    height:100%;
}
</style>