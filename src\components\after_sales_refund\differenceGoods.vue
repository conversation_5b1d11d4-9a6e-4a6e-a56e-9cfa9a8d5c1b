<!-- 差价商品选择，多选，单选 -->
<template>
<div class="xpt-flex">
	<el-row	class='xpt-top'	:gutter='40'>
		<el-col :span='16'>
			<el-button type='warning' size='mini' @click='close' :disabled="!selectGoodsList.length">确认</el-button>
			<el-button type='primary' size='mini' @click='combination' :disabled="!selectGoodsList.length">组合</el-button>
			<el-button type='primary' size='mini' @click='changeCalcPriceSpread'>切换</el-button>
		</el-col>
		<el-col :span='8' class='xpt-align__right'>
			<!-- <el-input placeholder="请输入合并单号" icon="search" size='mini' v-model="search" :on-icon-click="_getGoodsList">
			</el-input> -->
			<el-input size='mini' v-model="search" disabled>
			</el-input>
		</el-col>
	</el-row>
	<el-row class="xpt-flex__bottom mgb20" >
		<el-table ref="multipleTable" :data="goodsList" border tooltip-effect="dark" width='100%' style="width: 100%" @selection-change="handleSelectionChange" @row-dblclick='d => {
				if(!selectGoodsList.length){
					selectGoodsList = [d]
				}
				close()
			}'>
		    <el-table-column width="55" align='center' type="selection"></el-table-column>
			<el-table-column label="价格区间" width="200">
				<template slot-scope="scope">
					<el-input size="mini" :value="scope.row.price_list_name" style="width:100%" icon="search" @click="priceFun(scope.row)" readonly></el-input>
				</template>
			</el-table-column>
			<el-table-column label="价目表店铺" width="100">
				<template slot-scope="scope">
					<span v-if="params.refundType === 'PRICE_DIFF'">{{ scope.row.price_shop_name }}</span>
					<el-input v-else size="mini" style="width:100%" icon="search" @click="e => selectPriceListShop(e, scope.row)" readonly></el-input>
				</template>
			</el-table-column>
			<el-table-column label="标准售价" prop="stand_price"></el-table-column>
		    <el-table-column label="拍下价格" prop="act_price"></el-table-column>
		    <el-table-column label="活动价" prop="new_price"></el-table-column>
			<el-table-column label="差价" prop="price_spread">
				<template slot-scope="scope">{{ String(scope.row.price_spread) === 'NaN' ? '' : scope.row.price_spread }}</template>
			</el-table-column>

			<el-table-column label="商品编码" prop="materia_number" width="120" show-overflow-tooltip></el-table-column>
		    <el-table-column label="商品名称" prop="materia_name" width="80" show-overflow-tooltip></el-table-column>
		    <el-table-column label="商品规格描述" prop="materia_specifications" width="200" show-overflow-tooltip></el-table-column>

		    <el-table-column prop="tid" label="淘宝单号" width="80" show-overflow-tooltip>
		    	<!-- <template slot-scope="scope">
		    		<span>{{scope.row.show ? scope.row.tid : ''}}</span>
		    	</template> -->
		    </el-table-column>
		    <el-table-column label="订单状态" prop="status">
				<template slot-scope="scope">
					{{ {'CANCEL': '取消','NORMAL': '生效','WAITING': '等待发运','PART_DELIVERED': '部分发运中','DELIVERED': '已发运'}[scope.row.status] }}
				</template>
			</el-table-column>
			<el-table-column label="购买时间" prop="created" width="170" show-overflow-tooltip>
				<template slot-scope="scope">
					<span>{{scope.row.created | dataFormat1}}</span>
				</template>
			</el-table-column>
			<el-table-column label="单位" prop="unit_name"></el-table-column>
			
			<el-table-column label="活动商品编码" prop="price_material_no" width="150" show-overflow-tooltip></el-table-column>
			<el-table-column label="活动商品名称" prop="price_material_name" show-overflow-tooltip></el-table-column>
			<el-table-column label="活动规格描述" prop="price_material_desc" width="200" align="right" header-align="left" show-overflow-tooltip></el-table-column>

			<el-table-column label="生效日期" width="170" show-overflow-tooltip>
				<template slot-scope="scope">
					<span>{{scope.row.enable_time | dataFormat1}}</span>
				</template>
			</el-table-column>
			<el-table-column label="失效日期" width="170" show-overflow-tooltip>
				<template slot-scope="scope">
					<span>{{scope.row.disable_time | dataFormat1}}</span>
				</template>
			</el-table-column>
		    <el-table-column prop="shop_name" label="订单店铺" width="80" show-overflow-tooltip></el-table-column>
	  	</el-table>
	</el-row>
  	<el-row class='xpt-pagation'>
	  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
		  	:current-page="pageNow" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
		  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
		</el-pagination>
	</el-row>
</div>
</template>
<script>

export default {
	props:['params'],
	data(){
		return {
			search: '',
			selectCode: '',
			pageNow: 1,
			pageTotal: 0,
			pageSize: 10,
			goodsList: [],
			selectGoodsList: [],
			changeCalcPriceSpreadType: 'stand_price',
		}
	},
	methods:{
		combination (){
			var codesString = []
			,	actGroupPrice = 0
			,	standGroupPrice = 0

			this.selectGoodsList.forEach(o => {
				codesString.push(o.materia_id)
				actGroupPrice += Number(o.act_price)
				standGroupPrice += Number(o.stand_price)
			})

			this.$root.eventHandle.$emit('alert',{
				params: {
					codes: codesString.join(),
					shopId: this.params.shopId,
					callback: d => {
						var row = {}
						,	icon = +new Date
						,	withoutLastActGroupPrice = 0
						,	withoutLaststandGroupPrice = 0

						row.price_list_id= d.priceId;//价目表id		Long（需要传）
						row.price_list_name= d.priceListName;//价目表名称		String（需要传）
						row.price_shop_id= d.shopId;//活动店铺id	Long（需要传）
						row.price_shop_name= d.shopName;//活动店铺名称	Long（需要传）
						row.price_material_id= d.mateiralId;//活动商品id	Long（需要传）
						row.price_material_no= d.materialCode;//活动商品编码		String（需要传）
						row.price_material_name= d.materialName;//活动商品名称	String（需要传）
						row.price_material_desc= d.remark;//活动商品描述	String（需要传）
						// row.enable_time= d.enable_date;//生效日期	Date（需要传）
						// row.disable_time= d.disable_date;//失效日期	Date（需要传

						this.selectGoodsList.forEach((obj, index) => {
							Object.assign(obj, row)
							obj.combo_no_string = icon

							Object.defineProperties(obj, {
							    _act_group_price: {
							        configurable: true,
							        writable: true,
							        enumerable: false,
							        value: Number((d.price / actGroupPrice * obj.act_price).toFixed(2)),
							    },
							    _stand_group_price: {
							        configurable: true,
							        writable: true,
							        enumerable: false,
							        value: Number((d.price / standGroupPrice * obj.stand_price).toFixed(2)),
							    },
							    _last_price: {
							        configurable: true,
							        writable: true,
							        enumerable: false,
							        value: null,
							    },
							})

							if(index < this.selectGoodsList.length - 1){
								withoutLastActGroupPrice += obj._act_group_price
								withoutLaststandGroupPrice += obj._stand_group_price
							}else {
								obj._last_price = {
									act_price: Number((d.price - withoutLastActGroupPrice).toFixed(10)),
									stand_price: Number((d.price - withoutLaststandGroupPrice).toFixed(10)),
						        }
							}
						})
						this.changeCalcPriceSpreadType = this.changeCalcPriceSpreadType === 'act_price' ? 'stand_price' : 'act_price'
						this.changeCalcPriceSpread()
					},
				},
				component:()=>import('@components/after_sales_refund/combination_alert'),
				style:'width:380px;height:250px',
				title:'组合商品价目表列表'
			})
		},
		selectPriceListShop (e, row) {
			var $input = e.target.nextElementSibling
			this.$root.eventHandle.$emit('alert',{
				params: {
					price_list_id: row.price_list_id,
					callback (d){
						$input.value = d.shop_name
						row.price_shop_id = d.shop_id
						row.price_shop_name = d.shop_name
					},
				},
				component:()=>import('@components/after_sales_refund/selectPriceListShop'),
				style:'width:1000px;height:600px',
				title:'价目表店铺选择'
			});
		},
		priceFun(row) {
			var params = {
				material_id: row.materia_id,
				singleShotDate: row.created,//购买时间
			}
			params.callback = d => {
				row.price_list_id= d.price_list_id;//价目表id		Long（需要传）
				row.price_list_name= d.price_list_name;//价目表名称		String（需要传）
				row.price_shop_id= d.shop_id;//活动店铺id	Long（需要传）
				row.price_shop_name= d.shop_name;//活动店铺名称	Long（需要传）
				row.price_material_id= d.material_id;//活动商品id	Long（需要传）
				row.price_material_no= d.material_code;//活动商品编码		String（需要传）
				row.price_material_name= d.material_name;//活动商品名称	String（需要传）
				row.price_material_desc= d.material_specification;//活动商品描述	String（需要传）
				row.enable_time= d.enable_date;//生效日期	Date（需要传）
				row.disable_time= d.disable_date;//失效日期	Date（需要传
				row.new_price = d.price;//活动价	BigDecimal（需要传

				this.$set(row, 'price_spread', Number((parseFloat(row.act_price) - parseFloat(row.new_price)).toFixed(10)))//差价		BigDecimal（需要传）
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_sales_common/selectListPrice'),
				style:'width:1000px;height:600px',
				title:'差价商品价目表列表'
			});
		},
		toggleSelection(rows) {
			var  lists = [];
			rows.map(v => {
				if(v.page === this.pageNow) {
					lists = v.list;
					return;
				}
			});
	        if (lists) {
	            this.goodsList.forEach(goods => {
	            	lists.forEach(list => {
	            		if(goods.sourceMaterialId === list.sourceMaterialId) {
	            			this.$refs.multipleTable.toggleRowSelection(goods);
	            		}
	            	})

	            });
	        } else {
	          this.$refs.multipleTable.clearSelection();
	        }
	    },
		close(){
			if(
				this.selectGoodsList.some(obj => {
					if(this.params.refundType === 'PRICE_DIFF'){
						if(!obj.price_list_name){
							this.$message.error('选中项都要有价目表信息')
							return true
						}
					}else {
						if(!obj.price_list_name || !obj.price_shop_name){
							this.$message.error('选中项都要有价目表信息和价目表店铺')
							return true
						}
					}
				})
			){
				return
			}

			// var listData = [];
			// var backList = [];
			// this.selectGoodsList.map(v => {
			// 	listData = [...listData,...v.list];
			// });
			// listData.forEach(v => {
			// 	var obj = {
			// 		price_area: v.price_list_name,//价目表名称	String


			// 		sys_trade_id: v.sys_trade_id,//销售订单ID	Long
			// 		sys_trade_no: v.sys_trade_no,//销售单号		String
			// 		tid: v.tid,//淘宝单号—淘宝单号	String
			// 		created: v.created,//拍单时间—购买时间	Date
			// 		pay_time: v.pay_time,//支付时间		Date
			// 		shop_name: v.shop_name,//店铺名称		String
			// 		buyer_memo: v.buyer_memo,//买家备注	String
			// 		price_list_id: v.price_list_id,//价格表ID	Long
			// 		create_time: v.create_time,//创建时间	Date
			// 		merge_trade_id: v.merge_trade_id,//合并订单	Long
			// 		merge_trade_no: v.merge_trade_no,//合并订单单号	String
			// 		materia_id: v.materia_id,//商品ID	Long
			// 		materia_number: v.materia_number,//商品编码—商品编码	String
			// 		materia_name: v.materia_name,//商品名称—商品名称		String
			// 		materia_specifications: v.materia_specifications,//商品规格—规格描述	String
			// 		unit_id: v.unit_id,//单位 (取物料基本单位)	Long
			// 		unit_name: v.unit_name,//单位名称	—单位	String
			// 		act_price: v.act_price,//实际售价	--拍下价格	BigDecimal
			// 		status: v.status,//行状态—订单状态		String
			// 	};
			// 	backList.push(obj);
			// })

			this.params.callback(this.selectGoodsList)
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
		pageSizeChange(pageSize){
			this.pageSize = pageSize;
			this._getGoodsList();
		},
		pageChange(page){
			this.pageNow = page
			this._getGoodsList();
		},
		changeCalcPriceSpread (){
			if(this.changeCalcPriceSpreadType === 'act_price'){
				this.$message.info('当前为：拍下价格 - 活动价 = 差价')
				this.goodsList.forEach(obj => {
					if(obj._act_group_price){
						this.$set(obj, 'new_price', obj._last_price ? obj._last_price.act_price : obj._act_group_price)
					}

					this.$set(obj, 'price_spread', Number((parseFloat(obj.act_price) - parseFloat(obj.new_price)).toFixed(10)))
				})
				this.changeCalcPriceSpreadType = 'stand_price'
			}else {
				this.$message.info('当前为：标准售价 - 活动价 = 差价')
				this.goodsList.forEach(obj => {
					if(obj._stand_group_price){
						this.$set(obj, 'new_price', obj._last_price ? obj._last_price.stand_price : obj._stand_group_price)
					}

					this.$set(obj, 'price_spread', Number((parseFloat(obj.stand_price) - parseFloat(obj.new_price)).toFixed(10)))
				})
				this.changeCalcPriceSpreadType = 'act_price'
			}
		},
		_getGoodsList(){

			let self = this,
				data = {
					merge_trade_no: this.search || this.params.mergeTradeNo,
					// merge_trade_no: 'HB14110300154760',
					page_size: this.pageSize,
					page_no: this.pageNow
				};
			this.search = data.merge_trade_no;
			this.ajax.postStream('/afterSale-web/api/aftersale/order/plan/addPriceSpread',data,d=>{
				if(d.body.result&&d.body.content){
					this.pageTotal = d.body.content.count;

					var list = d.body.content.list||[];
					var goodsList = [];
					list.forEach(v => {
						if(v.listSysOrderVO && v.listSysOrderVO.length > 0) {
							v.listSysOrderVO.forEach((v1,index) => {

								// v.show = index == 0 ? true : false;
								var obj = Object.assign({},v,v1);
								obj.merge_trade_no = v.merge_trade_no
								goodsList.push(obj);
							})
						}else {
							// v.show = true;
							var obj = Object.assign({},v);
							goodsList.push(obj);
						}
					})
					this.goodsList = goodsList;
					// setTimeout(() => {
					// 	this.toggleSelection(this.selectGoodsList);
					// },100);

				}else {
					this.$message.error(d.body.msg)
				}
			})
		},
		handleSelectionChange(selectArr) {
			this.selectGoodsList = selectArr

			// var obj = {
			// 	page: this.pageNow,
			// 	list: selectArr
			// };
			// var bool = true;
			// this.selectGoodsList.map((v) => {
			// 	if(v.page === this.pageNow) {
			// 		v = obj;
			// 		bool = false;
			// 		return;
			// 	}
			// })
			// if(bool) {
			// 	this.selectGoodsList.push(obj);
			// }
		},
	},
	mounted() {
		this._getGoodsList();
	}
}
</script>
