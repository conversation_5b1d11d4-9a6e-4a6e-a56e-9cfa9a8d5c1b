<template>
  <div class="xpt-flex">
    <div class="xpt-top">
      <el-button type="info" size="mini" @click="forceInvalidSure" :loading="forceInvalidLoading"
        >确定</el-button
      >
    </div>
    <div class="xpt-flex__bottom">
      <el-form :model="form" :rules="rules" ref="form">
        <el-row class="mgt20" :gutter="40">
          <el-col :span="18">
            <el-form-item label="平台退款单号：" label-width="90px" prop="orderNo" style="height: 156px">
              <el-input
                type="textarea"
                placeholder="多订单请按行填写"
                v-model="form.orderNo"
                :autosize="{ minRows: 8, maxRows: 8 }"
                resize="none"
              >
              </el-input>
              <el-tooltip
                v-if="rules.orderNo[0].isShow"
                class="item"
                effect="dark"
                :content="rules.orderNo[0].message"
                placement="right"
                popper-class="xpt-form__error"
              >
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item
              label="作废原因："
              prop="reason"
              label-width="90px"
              style="height: 100px;margin-top: 20px;"
            >
              <el-input
                type="textarea"
                placeholder="请输入作废原因"
                v-model="form.reason"
                :autosize="{ minRows: 2, maxRows: 5 }"
                :maxlength="500"
                resize="none"
              >
              </el-input>
              <el-tooltip
                v-if="rules.reason[0].isShow"
                class="item"
                effect="dark"
                :content="rules.reason[0].message"
                placement="right"
                popper-class="xpt-form__error"
              >
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      form: {
        reason: "",
        orderNo: '',
      },
      rules: {
        reason: validate.isNotBlank({
          self: self,
        }),
        orderNo: validate.mulLineOrder({
          self: self,
          msg: "请输入正确的单号格式",
          required: true
        }),
      },
      forceInvalidLoading: false,
    };
  },
  methods: {
    forceInvalidSure() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (this.forceInvalidLoading) return
        this.forceInvalidLoading = true
        let url = '/afterSale-web/api/aftersale/bill/otherRefund/compulsoryInvalidation',
        postData = {
          reason: this.form.reason,
          refundIds:[]
        },list = this.form.orderNo.split("\n");
        postData.refundIds = list;

        this.ajax.postStream(url, postData, res => {
          if (res.body.result) {
            this.$message.success('作废成功');
            this.params.callback && this.params.callback(this.form);
            this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
          } else {
            this.$message.error(res.body.msg);
          }
          this.forceInvalidLoading = false
        }, err => {
          this.$message.error(err);
          this.forceInvalidLoading = false
        });
      });
    },
  },
  mounted() {
    this.form.orderNo = this.params.orderNo ? this.params.orderNo.join("\n") : '';
  }
};
</script>
<style scoped>
.el-form {
  white-space: nowrap;
}
</style>
