<template>
  <div class="xpt-flex">
    <div class="xpt-top">
      <el-button type="info" size="mini" @click="forceInvalidSure"
        >确定</el-button
      >
    </div>
    <div class="xpt-flex__bottom">
      <el-form :model="form" :rules="rules" ref="form">
        <el-row class="mgt20" :gutter="40">
          <el-col :span="18">
            <el-form-item
              label="作废原因："
              prop="invalid_reason"
              label-width="90px"
            >
              <el-input
                type="textarea"
                placeholder="请输入作废原因"
                v-model="form.invalid_reason"
                :autosize="{ minRows: 2, maxRows: 8 }"
                :maxlength="500"
                resize="none"
              >
              </el-input>
              <el-tooltip
                v-if="rules.invalid_reason[0].isShow"
                class="item"
                effect="dark"
                :content="rules.invalid_reason[0].message"
                placement="right"
                popper-class="xpt-form__error"
              >
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      form: {
        invalid_reason: "",
      },
      rules: {
        invalid_reason: validate.isNotBlank({
          self: self,
        }),
      },
    };
  },
  methods: {
    forceInvalidSure() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.params.callback && this.params.callback(this.form);
        this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
      });
    },
  },
};
</script>
<style scoped>
.el-form {
  white-space: nowrap;
}
</style>
