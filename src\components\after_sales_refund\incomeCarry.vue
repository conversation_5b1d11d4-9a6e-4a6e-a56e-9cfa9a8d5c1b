<!-- 退款申请单退款明细-收入结转后退款 -->
<template>
<div class="xpt-flex"> 
	<el-row class="xpt-top" :gutter="40">
		<el-button type='primary' size='mini' @click='() => submit()' :disabled="!selectData.length">确定</el-button>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData='colData'
		:selection="'checkbox'"
		:pageTotal='pageTotal'
		:showHead="false"
		:isNeedClickEvent="true"
		:selectable="selectableFun"
		@selection-change="s => this.selectData = s"
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
		:searchPage="pageName"
		@search-click="preSearch">
	</xpt-list>
</div>
</template>
<script>
export default {
	props: ['params'],
	data (){
		return {
			roleList: [],
			colData: [
				{
					label: '单据编号',
					prop: 'bill_code',
					width: 170
				},{
					label: '合并订单',
					prop: 'merge_trade_no',
					width: 150
				},{
					label: '买家昵称',
					prop: 'buyerNick',
				},{
					label: '收入结转类型',
					prop: 'income_carryover_type',
					width:120,
          format: 'auxFormat',
					formatParams: 'INCOME_CARRYOVER_TYPE',
				},{
					label: '结转金额',
					prop: 'carryover_sum',
				},{
					label: '已退结转金额',
					prop: 'act_sum',
					width: 150,
				},{
					label: '可退款金额',
					prop: 'refundable_amount'
				},{
					label: '退款申请金额',
					prop: 'apply_amount',
					width: 92,
					elInput:true,
					bool: true,
					disabled(row) {
						return row.refundable_amount < 0
					},
				}
		],
			selectData: [],
			page_no: 1,
			page_size: 50,
			pageTotal: 0,
			pageName: 'scm_merge_income_carryover',
			where: []
		}
	},
	methods: {
		selectableFun(row) {
			// 控制是否可以勾选
			if(row.refundable_amount < 0) {
				return false;
			} else {
				return true;
			}
		},
		preSearch(where, resolve){
			this.where = where
			this.searchFun(null, resolve);
		},
		searchFun (){
			let self = this
					, postData = {
						merge_trade_id: self.params.mergeNumberId,
						page_name: this.pageName,
						page_size: this.page_size,
						page_no: this.page_no,
						where: this.where
					}
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundIncomeCarry/list', postData, res => {
				if(res.body.result){
					this.roleList = res.body.content.list || []
					this.roleList.map(item => {
						item.apply_amount = ''
					})
					this.pageTotal = this.roleList.length
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		pageChange(pageSize){
			this.page_size = pageSize
			this.searchFun()
		},
		currentPageChange(page){
			this.page_no = page
			this.searchFun()
		},
		submit (selectData){
			let list = selectData || this.selectData
			let ifCheckSubmit = list.some(item => {
				return item.apply_amount <= 0 && /^\d+$/.test(item.apply_amount)
			})
			let ifCheckAmount = list.some(item => {
				return item.apply_amount > item.refundable_amount
			})
			if (ifCheckSubmit) {
				this.$message.error('退款申请金额必须为数字且大于0')
				return 
			}
			if (ifCheckAmount) {
				this.$message.error('退款申请金额必须小于等于可退款金额')
				return 
			}
			var callback = () => {
				this.params.callback(selectData || this.selectData)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
			callback()
		},
	},
	mounted (){
		console.log(this.params)
    this.searchFun()
	},
}
</script>
<style type="text/css" >
</style>