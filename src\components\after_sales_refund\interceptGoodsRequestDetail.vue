<!-- 截货申请详情-->
<template>
  <div class="xpt-flex quality-feedback">
    <div class="xpt-top">
      <el-button type="success" size="mini" :loading="false" @click="initDetail">刷新</el-button>
    </div>
    <div class="xpt-flex__bottom">
      <el-form :model="submitData" :rules="rules" ref="submitData" class="detail-form">
        <el-tabs v-model="firstTab" style="height: 180px">
          <el-tab-pane label="基础信息" name="basicInfo">
            <el-row :gutter="40" class="mgt10">
              <el-col :span="6">
                <el-form-item label="单据编号：" prop="order_no" label-width="100px">
                  <el-input v-model="submitData.order_no" size="mini" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="单据状态：" prop="order_status" label-width="100px">
                  <el-input :value="order_status_options[submitData.order_status]||submitData.order_status" size="mini" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="截货申请人：" prop="intercept_apply_user_name" label-width="100px">
                  <el-input v-model="submitData.intercept_apply_user_name" size="mini" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="40" class="mgt10">
              <el-col :span="6">
                <el-form-item label="合并单号：" prop="merge_trade_no" label-width="100px">
                  <a style="text-decoration:none;" href="javascript:;" @click="viewMergedOrder(submitData.merge_trade_id)"
                    title="点击进入合并订单信息页面">{{ submitData.merge_trade_no }}</a>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="截货申请时间：" prop="intercept_apply_time" label-width="100px">
                  <el-date-picker v-model="submitData.intercept_apply_time" type="datetime" size='mini' :disabled='true'></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="买家昵称：" prop="buyer_name" label-width="100px">
                  <el-input v-model="submitData.buyer_name" size="mini" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="40" class="mgt10">
              <el-col :span="12">
                <el-form-item label="截货原因：" prop="intercept_reason" label-width="100px">
                  <xpt-select-aux v-model="submitData.intercept_reason" aux_name="intercept_reason" placeholder=""
                    disabled></xpt-select-aux>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="secondTab" @tab-click="handleSecondTabChange">
          <el-tab-pane label="问题商品信息" name="QuestionInfo">
            <div class="xpt-top">
              <el-button type="primary" size="mini" @click="cancelIntercept" :disabled="false">取消截货</el-button>
            </div>
            <div class="tab-content-list">
              <el-table :data="questionGoodsList" border tooltip-effect="dark" style="width: 100%;" width='100%'
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="40"></el-table-column>
                <el-table-column label="序号" type="index" width="50">
                  <template slot-scope="scope">
                    <div class="table-index">{{ scope.$index + 1 }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="问题商品编码" prop="ques_goods_code" width="110"
                  show-overflow-tooltip></el-table-column>
                <el-table-column label="问题商品名称" prop="ques_goods_name" width="110"
                  show-overflow-tooltip></el-table-column>
                <el-table-column label="物料名称" prop="material_name" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column label="物料编码" prop="material_code" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="规格描述" prop="specs" width="200" show-overflow-tooltip></el-table-column>
                <el-table-column label="实际售价" prop="act_price" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column label="包件数" prop="pkg_qty" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column label="发运行状态" prop="m_status" width="100" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ m_status_options[scope.row.m_status] || scope.row.m_status }}
                  </template>
                </el-table-column>
                <el-table-column label="是否已截货" prop="if_intercept" width="100" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ {
                      Y: '是',
                      N: '否',
                    }[scope.row.if_intercept] || scope.row.if_intercept }}
                  </template>
                </el-table-column>
                <el-table-column label="截货失败原因" prop="intercept_fail_reason" width="200"
                  show-overflow-tooltip></el-table-column>
                <el-table-column label="仓库处理完成时间" prop="wms_finish_time" width="150"
                  show-overflow-tooltip>
                  <template slot-scope="scope">{{scope.row.wms_finish_time | dataFormat1}}</template>
                </el-table-column>
                <el-table-column label="截货行状态" prop="line_status" width="100" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ line_status_options[scope.row.line_status] || scope.row.line_status }}
                  </template>
                </el-table-column>
                <el-table-column label="截货通知单" prop="intercept_notice_no" width="140"
                  show-overflow-tooltip></el-table-column>
                <el-table-column label="批次单号" prop="batch_trade_no" width="220" show-overflow-tooltip></el-table-column>
                <el-table-column label="批次分单号" prop="batch_splitting_no" width="220"
                  show-overflow-tooltip></el-table-column>
                <el-table-column label="送货方式" prop="deliver_method" width="200" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ deliver_method_options[scope.row.deliver_method] || scope.row.deliver_method }}
                  </template>
                </el-table-column>
                <el-table-column label="收货人" prop="consignee" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column label="平台单号" prop="tid" width="170" show-overflow-tooltip></el-table-column>
                <el-table-column label="业务员" prop="staff_name" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column label="业务员分组" prop="staff_group" width="130" show-overflow-tooltip></el-table-column>
                <el-table-column label="取消截货人" prop="cancel_intercept_name" width="100"
                  show-overflow-tooltip></el-table-column>
                <el-table-column label="发起取消截货时间" prop="cancel_intercept_time" width="140"
                  show-overflow-tooltip>
                  <template slot-scope="scope">{{scope.row.cancel_intercept_time | dataFormat1}}</template>
                </el-table-column>
                <el-table-column label="取消截货状态" prop="cancel_intercept_status" width="200" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ cancel_intercept_status_options[scope.row.cancel_intercept_status] ||
                      scope.row.cancel_intercept_status }}
                  </template>
                </el-table-column>
                <el-table-column label="取消截货失败原因" prop="cancel_intercept_fail_reason" width="200"
                  show-overflow-tooltip></el-table-column>
                <el-table-column label="处理进度标签" prop="progress_label" width="100" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ progress_label_options[scope.row.progress_label] ||
                      scope.row.progress_label }}
                  </template>
                </el-table-column>
                <el-table-column label="处理进度标签添加者" prop="progress_label_name" width="150"
                  show-overflow-tooltip></el-table-column>
                <el-table-column label="处理进度标签添加时间" prop="progress_label_time" width="150"
                  show-overflow-tooltip>
                  <template slot-scope="scope">{{scope.row.progress_label_time | dataFormat1}}</template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          <el-tab-pane label="操作记录" name="OperateRecord">
            <el-table :data="operateLogList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
              <el-table-column label="序号" type="index" width="50">
                <template slot-scope="scope">
                  <div class="table-index">{{ scope.$index + 1 }}</div>
                </template>
              </el-table-column>
              <el-table-column label="用户" prop="operator_name" width="200" show-overflow-tooltip></el-table-column>
              <el-table-column label="业务操作" prop="operate_type" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ operate_type_options[scope.row.operate_type] || scope.row.operate_type }}
                </template>
              </el-table-column>
              <el-table-column label="操作描述" prop="description" width="400" show-overflow-tooltip></el-table-column>
              <el-table-column label="操作时间" prop="operate_time" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row.operate_time | dataFormat1 }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </div>
  </div>
</template>
<script>
import validate from "@common/validate.js";
import qs from "qs";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      firstTab: "basicInfo",
      secondTab: "QuestionInfo",
      submitData: {},
      rules: {},
      questionGoodsList: [],
      selectQuestionGoodsList: [],
      operateLogList: [],
      operate_type_options: {
        CREATE: '新增',
        SAVE: '保存',
        LOCK: '锁定',
        UNLOCK: '解锁',
        SUBMIT: '提交',
        RETRACT: '撤回',
        AUDIT: '审核',
        CHANGE: '确认变更',
        REVERSE_AUDIT: '反审核',
        SUBMIT_APPROVE: '提交审批',
        PASS: '审批通过',
        NOT_PASS: '审批不通过',
        TRACK: '执行跟踪',
        RECALL: '撤回仓储',
        CLOSE: '关闭',
        OPEN: '反关闭',
        CANCEL: '已取消',
        //extra
        ADD_REFUND_ITEM: '引入退款明细',
        DELETE_REFUND_ITEM: '删除退款明细',
        ADD_TAOBAO_REFUND: '引入淘宝退款申请',
        DELETE_TAOBAO_REFUND: '删除淘宝退款申请',
        SUBMITPURCHASE: '提交采购',
        PLAN_RETRACT: '方案撤回',
        REJECT: '驳回',
        LOCKER: '锁定',
        REVOCATION: '撤回',
        VERIFY: '审核',
        OPPOSITE_VERIFY: '反审核',
        SUBMIT_EXAMINE: '提交审批',
        PASS_EXAMINE: '审批通过',
        UNPASS_EXAMINE: '审批不通过',
      },
      order_status_options: {
        CREATE: '创建',
        AUDITING: '审核中',
        AUDITED: '已审核',
        CLOSE: '已关闭',
      },
      m_status_options: {
        'CANCEL': '取消',
        'WAITING': '等待发运',
        'RESERVED': '已保留',
        'PASS_GOODS_AUDIT': '已货审',
        'PASS_FINANCIAL_AUDIT': '已财审',
        'DELIVERED': '已发运',
        'SENT_TO_ZD': '已发送',
        'SENT_TO_SCM': '已发送SCM',
      },
      line_status_options: {
        WAIT: '等待',
        ING: '仓库处理中',
        OVER: '仓库已处理',
        CLOSE: '已关闭',
      },
      cancel_intercept_status_options: {
        WAIT: '等待',
        ING: '处理中',
        FAIL: '失败',
        SUC: '成功',
      },
      deliver_method_options: __AUX.get('deliver_method').reduce((a, b) => {
        a[b.code] = b.name
        return a
      }, {}),
      intercept_reason_options: __AUX.get('intercept_reason').reduce((a, b) => {
        a[b.code] = b.name
        return a
      }, {}),
      progress_label_options: __AUX.get('intercept_progress_label').reduce((a, b) => {
        a[b.code] = b.name
        return a
      }, {}),
    };
  },
  mounted() {
    if (this.params.request_id) {
      this.currentId = this.params.request_id;
      this.initDetail();
    }
  },
  computed: {},
  created() {
  },
  methods: {
    initDetail() {
      let p1 = this.getRequestDetail();
      let p2 = this.getQuestionInfoList();
      Promise.all([p1, p2]).then(() => {
      });
    },
    getRequestDetail() {
      let self = this;
      return new Promise((resolve, reject) => {
        this.ajax.postStream(
          "/afterSale-web/api/aftersale/interceptNotice/getHeadDetail",
          this.currentId,
          (res) => {
            if (res.body.result && res.body.content) {
              self.submitData = res.body.content;
              resolve();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              resolve();
            }
          },
          (err) => {
            this.$message.error(err);
            reject();
          }
        );
      });
    },
    //截货申请单明细详情
    getQuestionInfoList() {
      let self = this;
      return new Promise((resolve, reject) => {
        this.ajax.postStream(
          "/afterSale-web/api/aftersale/interceptNotice/getItemsDetail",
          this.currentId,
          (res) => {
            if (res.body.result && res.body.content) {
              self.questionGoodsList = res.body.content;
              console.log('self.questionGoodsList', self.questionGoodsList)
              resolve();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              resolve();
            }
          },
          (err) => {
            this.$message.error(err);
            reject();
          }
        );
      });
    },
    getOperateList() {
      let self = this;
      return new Promise((resolve, reject) => {
        this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.currentId }, res => {
          if (res.body.result) {
            this.operateLogList = res.body.content || []
            console.log('this.operateLogList', this.operateLogList)
          }
        })
      })
    },
    cancelIntercept() {
      let self = this;
      if (this.selectQuestionGoodsList.length === 0) {
        this.$message.warning("请选择至少一条明细！");
        return;
      }
      const cancelBtnDisabled = this.submitData.intercept_apply_user_name !== this.getEmployeeInfo("fullName") && this.selectQuestionGoodsList.some((item)=>item.staff_name !== this.getEmployeeInfo('fullName'));
      if (cancelBtnDisabled) {
        this.$message.warning("当前业务代理为当前单据截货申请提交人或业务员才有权限操作");
        return
      }
      const params = {
        itemIds: this.selectQuestionGoodsList.map(item => item.id),
        id: this.currentId,
        cancel_reason: ''
      }
      this.$confirm("确定取消截货吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          self.ajax.postStream(
            "/afterSale-web/api/aftersale/interceptNotice/cancelInterceptNotice",
            params,
            (res) => {
              if (res.body.result) {
                self.$message.success(res.body.msg);
                this.initDetail();
              } else {
                res.body.msg &&
                  self.$message.error(res.body.msg);
              }
            },
            (err) => {
              self.$message.error(err);
            }
          );
        })
        .catch(() => {});
    },
    handleSecondTabChange() {
      if (this.secondTab === 'QuestionInfo') {
        this.getQuestionInfoList();
      }
      if (this.secondTab === 'OperateRecord') {
        this.getOperateList()
      }
    },
    handleSelectionChange(data) {
      this.selectQuestionGoodsList = data
      console.warn(data)
    },
    viewMergedOrder(id) {
      if (!id) return
      var self = this;
      var params = { merge_trade_id: id };
      self.$root.eventHandle.$emit('creatTab', {
        name: "合并订单详情",
        params: params,
        component: () => import('@components/order/merge.vue')
      });
    },
  },
  components: {},
};
</script>
<style scoped>
.el-input,
.el-select,
.el-date-editor.el-input {
  width: 196px;
}

.el-form-item {
  white-space: nowrap;
}

.mgt10 {
  margin-top: 10px;
}

.ptb2 {
  padding: 10px 0px;
}

.detail-form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.nowrap {
  white-space: nowrap;
}

.tab-content-list {
  position: absolute;
  top: 30px;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
<style>
.quality-feedback .xpt-flex__bottom .el-tabs .el-tabs__content {
  overflow: hidden !important;
}
</style>
