<!-- 截货申请单列表-->
<template>
  <xpt-list-dynamic
    ref="list"
    :data="list"
    :btns="btns"
    :colData="cols"
    :selection="selection"
    :pageTotal="pageTotal"
    :searchPage="search.page_name"
    @search-click="preSearch"
    @selection-change="handleSelectionChange"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    :showCount="showCount"
    @count-off="countOff">
      <xpt-select-aux
        slot="btns"
        v-model='currentProgressTag'
        aux_name='intercept_progress_label'
        placeholder="处理进度标签"
        @change="handleProgressTagChange">
    </xpt-select-aux>
  </xpt-list-dynamic>
</template>
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      search: {
        page_name: "aftersale_return_intercept_notice",
        where: [],
        page_size: self.pageSize,
        page_no: 1,
      },
      list: [],
      selection: "checkbox",
      pageTotal: 0,
      isAfterSalesReception:false,
      multipleSelection: [], //列表选择索引
      m_status_options: {
        'CANCEL': '取消',
        'WAITING': '等待发运',
        'RESERVED': '已保留',
        'PASS_GOODS_AUDIT': '已货审',
        'PASS_FINANCIAL_AUDIT': '已财审',
        'DELIVERED': '已发运',
        'SENT_TO_ZD': '已发送',
        'SENT_TO_SCM': '已发送SCM',
      },
      btns: [
        {
          type: "success",
          txt: "刷新",
          click: self.searching,
          loading: false,
        },
        {
          type: "primary",
          txt: "下推售后单",
          click: self.pushAfterSale,
          loading: false,
        },
        {
          type: "primary",
          txt: "设置自动下推退换货单",
          click: self.onSetAutoPushClick,
          loading: false,
        }
      ],
      cols: [
        {
          prop: "order_no",
          label: "单据编号",
          width: 150,
          redirectClick(row) {
            let params = {};
            params.request_id = row.id;
            params.request_no = row.order_no;
            params.staff_name = row.staff_name;
            self.$root.eventHandle.$emit("creatTab", {
              name: "截货申请单详情",
              params: params,
              component: () =>
                import(
                  "@components/after_sales_refund/interceptGoodsRequestDetail.vue"
                ),
            });
          },
        },
        {
          prop: "order_status",
          label: "单据状态",
          width: 80,
          formatter: prop => ({
						CREATE: '创建',
						AUDITING: '审核中',
						AUDITED: '已审核',
						CLOSE: '已关闭',
					}[prop] || prop),
        },
        {
          prop: "buyer_name",
          label: "买家昵称",
          width: 130
        },
        {
          prop: "merge_trade_no",
          label: "合并单号",
          width: 170,
          redirectClick(row) {
            let params = {};
            params.merge_trade_id = row.merge_trade_id||'';
            if(!params.merge_trade_id){
              return
            }
            self.$root.eventHandle.$emit("creatTab", {
              name: "合并订单详情",
              params: params,
              component: () => import('@components/order/merge.vue')
            });
          },
        },
        {
          prop: "after_order_no",
          label: "售后单",
          width: 100,
          redirectClick(row) {
						self.viewAfterOrderDetail(row.after_order_id)
					}
        },
        {
          prop: "intercept_reason",
          label: "截货原因",
          width: 130,
          format: 'auxFormat',
					formatParams: 'intercept_reason',
        },
        {
          prop: "intercept_apply_user_name",
          label: "截货申请人",
          width: 120,
        },
        {
          prop: "intercept_apply_time",
          label: "截货申请时间",
          format: "dataFormat1",
          width: 130
        },
        {
          prop: "ques_goods_code",
          label: "问题商品编码",
          width: 110
        },
        {
          prop: "ques_goods_name",
          label: "问题商品名称",
          width: 110
        },
        {
          prop: "material_name",
          label: "物料名称",
          width: 100
        },
        {
          prop: "material_code",
          label: "物料编码",
          width: 150
        },
        {
          prop: "specs",
          label: "规格描述",
          width: 200
        },
        {
          prop: "act_price",
          label: "实际售价",
          width: 100
        },
        {
          prop: "pkg_qty",
          label: "包件数",
          width: 100
        },
        {
          prop: "m_status",
          label: "发运行状态",
          width: 100,
          formatter: prop => (this.m_status_options[prop] || prop),
        },
        {
          prop: "if_intercept",
          label: "是否已截货",
          formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
          width: 100
        },
        {
          prop: "intercept_fail_reason",
          label: "截货失败原因",
          width: 130
        },
        {
          prop: "wms_finish_time",
          label: "仓库处理完成时间",
          width: 150,
          format: "dataFormat1",
        },
        {
          prop: "if_auto_returns",
          label: "需自动推退换货单",
          width: 150,
          formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
        },
        {
          prop: "returns_type",
          label: "退货方式",
          width: 100,
          formatter: prop => ({
						RETURN: '退货',
						CHANGE: '换货',
					}[prop] || prop),
        },
        {
          prop: "change_goods_type",
          label: "换货商品",
          width: 100,
          formatter: prop => ({
						SAME: '同商品',
						DIFF: '不同商品',
					}[prop] || prop),
        },
        {
          prop: "auto_returns_result",
          label: "自动推退换货单结果",
          width: 160,
          redirectClick(row) {
              if(row.bill_returns_id) {
                self.$root.eventHandle.$emit('creatTab', {
                  name: "退换货跟踪单详情",
                  params: {id: row.bill_returns_id},
                  component: () => import('@components/after_invoices/returnexchangedetail_2')
                });
              } else {
                self.$message.info('无退货跟踪单');
              }
					}
        },
        {
          prop: "auto_returns_status",
          label: "自动推退换货单状态",
          width: 160,
          formatter: prop => ({
						WAIT: '等待',
						RUNNING: '处理中',
						FAILED: '失败',
						SUCCESS: '成功',
            NO_NEED: '无需'
					}[prop] || prop),
        },
        {
          prop: "line_status",
          label: "截货行状态",
          width: 100,
          formatter: prop => ({
						WAIT: '等待',
						ING: '仓库处理中',
						OVER: '仓库已处理',
						CLOSE: '已关闭',
					}[prop] || prop),
        },
        {
          prop: "intercept_notice_no",
          label: "截货通知单",
          width: 100
        },
        {
          prop: "batch_trade_no",
          label: "批次单号",
          width: 220,
          redirectClick(row) {
						self.viewBatchInfoDetail(row)
					}
        },
        {
          prop: "batch_splitting_no",
          label: "批次分单号",
          width: 220
        },
        {
          prop: "deliver_method",
          label: "送货方式",
          width: 100,
					format: 'auxFormat',
					formatParams: 'deliver_method'
        },
        {
          prop: "consignee",
          label: "收货人",
          width: 100
        },
        {
          prop: "tid",
          label: "平台单号",
          width: 150
        },
        {
          prop: "staff_name",
          label: "业务员",
          width: 100
        },
        {
          prop: "staff_group",
          label: "业务员分组",
          width: 130
        },
        {
          prop: "cancel_intercept_name",
          label: "取消截货人",
          width: 100
        },
        {
          prop: "cancel_intercept_time",
          label: "发起取消截货时间",
          width: 140,
          format: "dataFormat1",
        },
        {
          prop: "cancel_intercept_status",
          label: "取消截货状态",
          width: 100,
          formatter: prop => ({
						WAIT: '等待',
						ING: '处理中',
						FAIL: '失败',
						SUC: '成功',
					}[prop] || prop),
        },
        {
          prop: "cancel_intercept_fail_reason",
          label: "取消截货失败原因",
          width: 200
        },
        {
          prop: "progress_label",
          label: "处理进度标签",
          width: 100,
          format:'auxFormat',
          formatParams:'intercept_progress_label'
        },
        {
          prop: "progress_label_name",
          label: "处理进度标签添加者",
          width: 150
        },
        {
          prop: "progress_label_time",
          label: "处理进度标签添加时间",
          width: 140,
          format: "dataFormat1",
        },
      ],
      currentProgressTag: '',
      showCount: false, //显示总数
      countShowLoading: false, //显示总数请求中
    };
  },
  created() {
  },
  beforeDestroy() {
  },
  methods: {
    preSearch(list, resolve) {
      this.search.where = list;
      this.search.page_no = 1;
      this.pageTotal = 0;
      this.showCount = false;
      this.searching(resolve);
    },
    searching(resolve) {
      let self = this;
      this.btns[0].loading = true;
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/interceptNotice/list",
        this.search,
        (res) => {
          if (res.body.result) {
            if (res.body.content.list) {
              self.list = res.body.content.list;
              if (!this.showCount && this.list.length>=(this.search.page_size+1)){
                self.pageTotal = (Number(this.search.page_no) || 1) * Number(this.search.page_size) + 1
              };
            }
          } else {
            self.list = [];
            self.pageTotal = 0;
            self.$message.error(res.body.msg);
          }
          self.btns[0].loading = false;
          "function" === typeof resolve ? resolve() : this;
        },
        (err) => {
          self.$message.error(err);
          self.btns[0].loading = false;
          "function" === typeof resolve ? resolve() : this;
        }
      );
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    sizeChange(size) {
      // 第页数改变
      this.search.page_size = size;
      this.searching();
    },
    pageChange(page_no) {
      // 页数改变
      this.pageNow = page_no;
      this.search.page_no = page_no;
      this.searching();
    },
    countOff() {
      let self = this,
        url = "/afterSale-web/api/aftersale/interceptNotice/count";
      if (!!self.countShowLoading) {
        self.$message.error("请勿重复点击");
        return;
      }
      self.countShowLoading = true;

      self.ajax.postStream(url, self.search, function (response) {
        if (response.body.result) {
          self.pageTotal = response.body.content.count||0;
          self.showCount = true;
          self.countShowLoading = false;
        } else {
          self.countShowLoading = false;
          self.$message.error(response.body.msg);
        }
      });
    },
    getEmployeeInfoGroupList() {
            //获取当前处理人的业务信息列表
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/user-web/api/userPerson/getUserPersonGroupList",
                    { personId: this.getEmployeeInfo("personId") },
                    (res) => {
                        if (res.body.result && res.body.content) {
                            this.employeeGroupCodeList =
                                res.body.content.list || [];
                            resolve();
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                            resolve();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                        reject();
                    }
                );
            });
        },
        setAfterSaleSubmitPermission() {
            //过滤失效的业务员
            let nowTime = new Date().getTime();
            let enAbledEmployeeGroupCodeList = this.employeeGroupCodeList.filter(
                (item) => !item.disableTime || item.disableTime > nowTime
            );
            //售后接待岗
            let codeArr = enAbledEmployeeGroupCodeList.map((item) => {
                return item.salesmanType;
            });
            this.isAfterSalesReception = codeArr.includes(
                "AFTERSALES_RECEPTIONIST"
            );
        },
    pushAfterSale() {
      if (this.multipleSelection.length === 0) {
          this.$message.warning("请选择！");
          return;
      }
      if (this.multipleSelection.some(item=>item.order_no!==this.multipleSelection[0].order_no)) {
          this.$message.warning("只能选择同一个截货申请");
          return
      }
      let currentAgent = this.getEmployeeInfo("id"); //当前代理人
      let isHandler = this.multipleSelection[0].staff_id == currentAgent; //是否当前受理人
      if(!(this.isAfterSalesReception||isHandler)){
        this.$message.warning('当前业务代理为当前单据业务员或含有业务员类型“售后接待岗”才有权限操作');
        return
      }
      const params={
        from:'interceptGoodsRequestLIst',
        merge_trade_no:this.multipleSelection[0].merge_trade_no
      }
      this.$root.eventHandle.$emit('creatTab', {
          name:"新增售后单",
          params:params,
          component: () => import('@components/after_sales/index')
        });
    },
    viewBatchInfoDetail(row){
      if(!row.batch_tradeid){
        return
      }
			var self = this;
			var params = {};
			params.batch_trade_id = row.batch_tradeid;
			params.merge_trade_id = row.merge_trade_id;
			params.merge_trade_no = row.merge_trade_no;
			params.order_list = [];
			self.$root.eventHandle.$emit('creatTab',{
				name:"批次订单详情",
				params:params,
				component: () => import('@components/order/batch_order.vue')
			});
		},
    viewAfterOrderDetail(id) {
      if (!id) {
        return
      }
      var params = {
        id,
        idList: [],//用于售后单详情的上一页/下一页
      };

      if (document.querySelector('[data-symbol="after_order_id_' + id + '"]')) {
        this.$message.error('该售后单已经打开')
      } else {
        this.$root.eventHandle.$emit('creatTab', {
          name: "售后单详情",
          params: params,
          component: () => import('@components/after_sales/index')
        });
      }
    },
    handleProgressTagChange() {
      if (!this.currentProgressTag) {
        return
      }
      if (!this.multipleSelection.length) {
        this.$message.warning('请至少选择一条单据');
        this.currentProgressTag = '';
        return
      }
      const itemIds=this.multipleSelection.map(item=>item.itemId)
      const params={
        itemIds,
        progressLabel:this.currentProgressTag
      }
      this.ajax.postStream(
        `/afterSale-web/api/aftersale/interceptNotice/progressLabel`,
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            this.searching();
          } else {
            this.$message.error(res.body.msg);
          }
          this.currentProgressTag=''
        },
        (err) => {
          this.currentProgressTag=''
          this.$message.error(err);
        }
      );
    },
    onSetAutoPushClick() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请至少选择一条单据');
        return
      }

      if(this.multipleSelection.some((item)=> item.intercept_apply_user_name !== this.getEmployeeInfo('fullName') && item.staff_name !==this.getEmployeeInfo('fullName') && item.after_order_locker_name !== this.getEmployeeInfo('fullName'))) {
        const firstInvalidId = this.multipleSelection.filter(item=> item.intercept_apply_user_name !== this.getEmployeeInfo('fullName') && item.staff_name !==this.getEmployeeInfo('fullName') && item.after_order_locker_name !== this.getEmployeeInfo('fullName'))[0]
        this.$message.warning(`当前业务代理人不是截货申请单${firstInvalidId.order_no}的提交人或业务员或售后单业务锁定人，无权限操作`);
        return
      }

      if(this.multipleSelection.some(item=>item.progress_label==='已推送')) {
        this.$message.warning('存在已自动下推退换货单成功的截货申请单明细，请检查后重选选择');
        return
      }

      this.$root.eventHandle.$emit('alert',{
        title: '设置自动下推退货单',
        style: 'width: 500px; height: 280px',
        params: {
          itemIds: this.multipleSelection.map(item=>item.itemId),
          callback: ()=> {
            this.searching();
          },
        },
        component: () => import('@components/after_sales_refund/autoPushRefundSetting')
      })
    }
  },
  mounted() {
    let self = this;
    this.getEmployeeInfoGroupList().then(() => {
      self.setAfterSaleSubmitPermission();
      self.searching();
    });
    //监听切换业务代理事件
    this.$root.eventHandle.$on("resetAllBtnStatus", () => {
      this.getEmployeeInfoGroupList().then(() => {
        self.setAfterSaleSubmitPermission();
      });
    });
  },
  destroyed() {
  },
};
</script>
<style module></style>
