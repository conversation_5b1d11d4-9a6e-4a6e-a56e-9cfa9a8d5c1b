<template>
  <div class="xpt-flex">
    <div class="xpt-top">
      <el-button type="primary" size="mini" @click="download">下载</el-button>
    </div>
    <div class="xpt-flex__bottom">
      <el-row class="mgt20" :gutter="40">
        <el-col :span="12">
          <el-form :model="submitData" ref="submitData" :rules="rules">
            <el-form-item
              label="店铺："
              label-width="80px"
              prop="shopCode"
              class="mb20"
            >
              <xpt-select-aux
									v-model="submitData.shopCode"
									aux_name="manual_sync_refund_shop"
									placeholder="请选择"
									size="mini"
									style="width:180px;"
									@change="change"
								></xpt-select-aux>
              <el-tooltip
                v-if="rules.shopCode[0].isShow"
                class="item"
                effect="dark"
                :content="rules.shopCode[0].message"
                placement="right"
                popper-class="xpt-form__error"
              >
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item
              label="类型："
              label-width="80px"
              prop="refundType"
              class="mb20"
            >
              <el-select
                v-model="submitData.refundType"
                size="mini"
                placeholder="请选择"
                :disabled="!submitData.shopCode"
              >
                <el-option v-for="(value, key) in refundTypeObj[refundShopObj[submitData.shopCode]]" :key="key" :label="value" :value="key"></el-option>
              </el-select>
              <el-tooltip
                v-if="rules.refundType[0].isShow"
                class="item"
                effect="dark"
                :content="rules.refundType[0].message"
                placement="right"
                popper-class="xpt-form__error"
              >
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item :label="labelTitle" label-width="80px" prop="orderNo">
              <el-input
                type="textarea"
                :placeholder="orderNoPlaceholder"
                v-model="submitData.orderNo"
                :autosize="{ minRows: 10, maxRows: 10 }"
                :rows="10"
              >
              </el-input>
              <el-tooltip
                v-if="rules.orderNo[0].isShow"
                class="item"
                effect="dark"
                :content="rules.orderNo[0].message"
                placement="right"
                popper-class="xpt-form__error"
              >
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <el-table :data="downloadResult" border style="width: 100%" class="download-result">
            <el-table-column prop="document_no" :label="labelTitle" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column
              width="40"
              prop="sync_status"
              label="结果"
              :formatter="formatterResult"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              show-overflow-tooltip
            >
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      hasInitParams:true,//是否初始化参数
      labelTitle:'退款单号',
      orderNoPlaceholder:'多订单请按行填写',
      submitData: {
        shopCode: "",
        orderNo: "",
        refundType: ''
      },
      downloadResult: [],
      rules: {
        orderNo: validate.mulLineOrder({
          self: self,
          msg: "请输入正确的单号格式",
          required: true
        }),
        shopCode: validate.isNotBlank({
          self: self,
          msg: "请选择店铺",
        }),
        refundType: validate.isNotBlank({
          self: self,
          msg: "请选择类型",
        }),
      },
      refundTypeObj: {},
      refundShopObj: __AUX.get('manual_sync_refund_shop').reduce((a, b) => {
        a[b.code] = b.tag
        return a
      }, {}),
    };
  },
  created() {
    this.getRefundType()
  },
  methods: {
    getRefundType () {
      this.ajax.get('/afterSale-web/api/aftersale/bill/refundManualSync/getRefundType', res => {
				if (res.body.result) {
          this.refundTypeObj = res.body.content;
				} else {
          this.refundTypeObj = {};
					this.$message.error(res.body.msg);
				}
      })
    },
    change(){
      if(this.hasInitParams){
        this.hasInitParams=false;
      }else{
        this.submitData.orderNo = '';
      }

      this.submitData.refundType = ''
      this.downloadResult = [];
      // if(this.submitData.shopCode !="PDD"){
      //   this.labelTitle = '退款单号'
      // }else{
      //   this.labelTitle = '订单号'
      // }
      let currentShop=__AUX.get('manual_sync_refund_shop').find(item=>item.code===this.submitData.shopCode)
      let lineCount=currentShop?.ext_field1||'不限';
      this.orderNoPlaceholder=`多订单请按行填写，不能超过${lineCount}行`
    },
    download() {
      let self = this;
      this.$refs.submitData.validate((valid) => {
        if (!valid) return;
        let url = '/afterSale-web/api/aftersale/bill/refundManualSync/manualSyncRefund?permissionCode=MANUAL_SYNC_REFUND',
        postData = {
          shopCode:this.submitData.shopCode,
          refundType:this.submitData.refundType,
          refundIds:[]
        },list = this.submitData.orderNo.split("\n");
        postData.refundIds = list;

        this.ajax.postStream(url, postData, res => {
          if (res.body.result) {
            self.downloadResult = res.body.content.map(item=>{
              return{
                ...item,
                remark:item.remark?item.remark.slice(0,2000):item.remark
              }
            });
            this.$message.success('获取成功');
          } else {
            self.downloadResult = []
            this.$message.error(res.body.msg);
          }
        }, err => {
          self.downloadResult = []
          this.$message.error(err);
        });

      });
    },
    formatterResult(row) {
      return !row.sync_status ?'失败':'成功';
    },
  },
  mounted() {
    if(this.params.list.length>0){
      this.submitData.shopCode = this.params.list[0].shop_code;
      this.submitData.orderNo=this.params.list.filter(item=>!!item.refund_id).map(item=>item.refund_id).join('\n');
      this.hasInitParams=true;
    }
  },
};
</script>
<style scoped lang="stylus">
.mb20 {
  margin-bottom: 20px;
}
.el-form-item{
    white-space: nowrap;
}
</style>
<style>
.download-result .cell {
	padding-right: 0;
}
</style>
