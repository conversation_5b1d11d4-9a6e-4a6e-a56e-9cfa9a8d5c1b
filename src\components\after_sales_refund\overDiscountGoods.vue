<!-- 退款申请单退款明细-前置优惠-弹框 -->
<template>
<div class="xpt-flex"> 
	<el-row class="xpt-top" :gutter="40">
		<el-button type='primary' size='mini' @click='() => submit()' :disabled="!selectData.length">确定</el-button>
		<el-col class='xpt-align__right' :span='15' style="float:right;">
			<el-select v-model="limit_field" size='mini' style="width:100px;" @change="field_value = ''">
				<el-option 
					v-for="(value,key) in limit_field_options" 
					:key="key.toString()" 
					:label="value.toString()"
					:value="key.toString()">
				</el-option>
			</el-select>
			<el-input
				placeholder="请输入查询值"
				size='mini'
				v-model="field_value"
                v-show="limit_field != 'activity_subclass'"
			></el-input>
            <el-select size='mini' v-show="limit_field == 'activity_subclass'" v-model="field_value" >	
                <el-option key="ORDER_DISCOUNT_REFUND" label="订单返现" value="ORDER_DISCOUNT_REFUND"> </el-option>
                <el-option key="GOODS_PROMOTION_REFUND" label="单品返现" value="GOODS_PROMOTION_REFUND"> </el-option>
            </el-select>
			<el-button type='primary' @click="searchFun" size='mini'>查询</el-button>
		</el-col>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData='colData'
		:selection="'checkbox'"
		:pageTotal='pageTotal'
		:showHead="false"
		:isNeedClickEvent="true"
		@selection-change="s => this.selectData = s"
		@radio-change="s => this.selectData = [s]"
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'>
	</xpt-list>
</div>
</template>
<script>
export default {
	props: ['params'],
	data (){
		return {
			roleList: [],
			colData: [
                {
                    label: '销售单号',
                    prop: 'sys_trade_no',
                    width: 170
                },{
                    label: '淘宝单号',
                    prop: 'tid',
                    width: 150
                },{
                    label: '商品名称',
                    prop: 'material_name',
                },{
                    label: '优惠影响范围',
                    prop: 'discount_effect_area_name',
                    width: 90,
                },{
                    label: '优惠子类型',
                    prop: 'activity_subclass_name',
                   
                },{
                    label: '优惠活动',
                    prop: 'activity_name',
                    width: 150,
                },{
                    label: '项目细类',
                    prop: 'item_type_detail_name',
                },{
                    label: '退款申请金额',
                    prop: 'apply_amount',
                    width: 92,
                    elInput:true,
					bool: true,
					disabled(row) {
						return false
					},
                },{
                    label: '优惠金额',
                    prop: 'discount',
                },{
                    label: '已使用金额',
                    prop: 'share_discount',
                },{
                    label: '已退金额',
                    prop: 'refunded_amount',
                },{
                    label: '退款中金额',
                    prop: 'refunding_amount',
                }
            ],
			selectData: [],
			limit_field_options: {},
			limit_field: '',
			field_value: '',

			page_no: 1,
			page_size: 50,
			pageTotal: 0,
		}
	},
	methods: {
		fieldValueDisabledTest (){
			if(/^(sys_trade_no|tid|activity_subclass)$/.test(this.limit_field)){
				return false
			}
		},
		searchFun (){
            let self = this
                , postData = {
                    merge_trade_id: self.params.mergeNumberId
                }
                if(this.limit_field && this.field_value){
					postData[this.limit_field] = this.field_value
				}
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/getRefundDiscount', postData, res => {
				if(res.body.result){
                    this.roleList = res.body.content.list || []
                    this.roleList.map(item => {
                        item.apply_amount = ''
                    })
                    this.pageTotal = this.roleList.length
				}else {
					this.$message.error(res.body.msg)
					if(this.params.CANCEL_oid){
						this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
					}
				}
			})
		},
		pageChange(pageSize){
			this.page_size = pageSize
			this.searchFun()
		},
		currentPageChange(page){
			this.page_no = page
			this.searchFun()
		},
		submit (selectData){
            let list = selectData || this.selectData
            let ifCheckSubmit = list.some(item => {
                return item.apply_amount <= 0
            })
            if (ifCheckSubmit) {
                this.$message.error('退款申请金额必须大于0')
                return 
            }
			var callback = () => {
				this.params.callback(selectData || this.selectData)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
			callback()
		},
	},
	mounted (){
		console.log(this.params)
		var refundType = this.params.refundType
        this.limit_field_options = {
            'sys_trade_no': '销售单号',
            'tid': '淘宝单号',
            'activity_subclass': '优惠子类型',
        }
        
        this.limit_field_options.sys_trade_no = '销售单号'
		this.limit_field = 'sys_trade_no'
		this.field_value = ''
        this.searchFun()
	},
}
</script>
<style type="text/css" >
</style>