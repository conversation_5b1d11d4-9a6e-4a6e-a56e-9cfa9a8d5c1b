<template>
  <div class="xpt-flex">
    <div class="xpt-top">
      <el-button type="info" size="mini" @click="submit" :loading="submitting">确定</el-button>
    </div>
    <div class="xpt-flex__bottom">
      <el-form :model="form" :rules="rules" ref="form">
        <el-row class="mgt20" :gutter="40">
          <el-col :span="22">
            <el-form-item label="平台退款单号" label-width="90px" prop="refundOrder" style="height: 156px">
              <el-input type="textarea" placeholder="请输入平台退款单号，多订单请按行填写" v-model="form.refundOrder"
                :autosize="{ minRows: 8, maxRows: 8 }" resize="none">
              </el-input>
              <el-tooltip v-if="rules.refundOrder[0].isShow" class="item" effect="dark" :content="rules.refundOrder[0].message"
                placement="right" popper-class="xpt-form__error">
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item label="业务备注" prop="remark" label-width="90px" style="height: 100px;margin-top: 20px;">
              <el-input type="textarea" placeholder="请输入" v-model="form.remark" :autosize="{ minRows: 5, maxRows: 5 }"
                :maxlength="500" resize="none">
              </el-input>
              <el-tooltip v-if="rules.remark[0].isShow" class="item" effect="dark" :content="rules.remark[0].message"
                placement="right" popper-class="xpt-form__error">
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item label="标签" prop="urgentSign" label-width="90px" style="margin-top: 20px;">
              <xpt-select-aux v-model="form.urgentSign" aux_name="SHDBQ" placeholder="请选择" clearable
                size="mini" style="width: 100%"></xpt-select-aux>
            </el-form-item>
          </el-col>
          <el-col :span="22" style="margin-left: 90px;">
            <div style="color: #ff4949;line-height: 1.5;margin-top: 20px;">
              <p>温馨提示：批量下推的售后单</p>
              <p>1.全部售后单的业务备注赋值为以上输入的业务备注内容</p>
              <p>2.全部售后单的问题商品，系统自动添加为退款单平台单号关联的所有已发运商品</p>
              <p>3.针对存在未完结售后单的退款单，系统直接跳过，不再创建售后单</p>
            </div>
            <div style="margin-top: 15px;color: #f7ba2a">
              请慎重使用！！！
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      form: {
        remark: "",
        refundOrder: '',
        urgentSign: ''
      },
      rules: {
        remark: validate.isNotBlank({
          self: self,
        }),
        refundOrder: validate.mulLineOrder({
          self: self,
          msg: "请输入正确的单号格式",
          required: true
        }),
      },
      submitting: false,
    };
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        if (this.submitting) return
        this.submitting = true
        let url = '/afterSale-web/api/aftersale/bill/refundApp/pushAftersaleOrder/batch',
          postData = this.form.refundOrder.split("\n").map((item)=> {
            return {
              refundId: item,
              urgentSign: this.form.urgentSign,
              remark: this.form.remark
            }
          });

        this.ajax.postStream(url, postData, res => {
          if (res.body.result) {
            this.$message.success('下推成功');
          } else {
            this.params.callback && this.params.callback(res.body);
          }
          this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
          this.submitting = false
        }, err => {
          this.$message.error(err);
          this.submitting = false
        });
      });
    },
  },
  mounted() {
    this.form.refundOrder = this.params.orders ? this.params.orders.filter((item)=>item.refund_id).map((item) => item.refund_id).join("\n") : '';
  }
};
</script>
<style scoped>
.el-form {
  white-space: nowrap;
}
</style>
