<!-- 订单退款对账余额列表 -->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top">
      <el-col :span="15">
        <el-button
          type="primary"
          size="mini"
          :disabled="isLoading"
          :loading="isLoading"
          @click="refresh"
        >
          刷新
        </el-button>
        <el-button
          type="primary"
          size="mini"
          :disabled="isExporting"
          :loading="isExporting"
          @click="onExport"
        >
          导出
        </el-button>
        <el-button type="primary" size="mini" @click="showExportList">
          报表导出文件下载
        </el-button>
        <xpt-select-aux
          v-model="signValue"
          style="width: 110px; margin-left: 10px"
          aux_name="PROCESS_PROGRESS_TAG"
          placeholder="处理进度标签"
          @change="onSignChange"
        />
      </el-col>
      <el-col :span="9" style="text-align: right">
        <xpt-search-ex
          :page="searchObj.page_name"
          :click="preSearch"
        ></xpt-search-ex>
      </el-col>
    </el-row>
    <el-row class="xpt-flex__bottom scroll">
      <xpt-list
        :data="dataSource"
        :show-head="false"
        :col-data="cols"
        :page-total="count"
        :show-count="showCount"
        is-need-click-event
        :page-length="searchObj.page_size"
        @selection-change="onSelect"
        @page-size-change="onSizeChange"
        @current-page-change="onPageChange"
        @count-off="onCountOff"
      />
    </el-row>
  </div>
</template>

<script>
export default {
  props: ["params"],
  data() {
    return {
      isLoading: false,
      isExporting: false,
      signValue: "",
      searchObj: {
        page_no: 1,
        page_size: 50,
        page_name: "aftersale_order_refund_balance",
        where: [],
      },
      count: 0,
      showCount: false,
      dataSource: [],
      selections: [], // 选择的数据
      cols: [
        {
          label: "对账批次号",
          prop: "batch_no",
          width: 140,
        },
        {
          label: "对账批次创建时间",
          prop: "batch_time",
          format: "dataFormat1",
          width: 130,
        },
        {
          label: "合并订单号",
          prop: "merge_trade_no",
          width: 170,
        },
        {
          label: "合并订单状态",
          prop: "merge_trade_status_name",
          width: 85,
        },
        {
          label: "合并单退剩余额",
          prop: "refund_balance",
          width: 110,
        },
        {
          label: "对账余额",
          prop: "reconciliation_balance",
        },
        {
          label: "总支付金额",
          prop: "total_amount",
        },
        {
          label: "收款单金额",
          prop: "receipt_amount",
        },
        {
          label: "发货金额",
          prop: "deliver_amount",
        },
        {
          label: "前置优惠金额",
          prop: "over_discount_amount",
          width: 110,
        },
        {
          label: "已退前置优惠",
          prop: "refund_over_discount_amount",
          width: 110,
        },
        {
          label: "已退未前置优惠",
          prop: "refund_preferential_amount",
          width: 110,
        },
        {
          label: "退货货款金额",
          prop: "return_payment_amount",
          width: 110,
        },
        {
          label: "收入结转金额",
          prop: "income_carry_amount",
          width: 110,
        },
        {
          label: "完结状态",
          prop: "finish_status_name",
        },
        {
          label: "完结时间",
          prop: "finish_time",
          format: "dataFormat1",
          width: 130,
        },
        {
          label: "处理进度标签",
          prop: "process_progress_tag_name",
          width: 90,
        },
        {
          label: "处理进度标签添加者",
          prop: "process_progress_tag_creator_name",
          width: 120,
        },
        {
          label: "订单店铺",
          prop: "order_shop_name",
          width: 150,
        },
        {
          label: "收入店铺",
          prop: "income_shop_name",
          width: 150,
        },
      ],
      countOffFlag: false,
    };
  },
  methods: {
    // 搜索
    async search() {
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/orderRefundBalance/list?permissionCode=AFTERSALE_ORDER_REFUND_BALANCE_QUERY",
        this.searchObj,
        (response) => {
          if (response.body.result) {
            if (
              response.body.content.list?.length >
              this.searchObj.page_size + 1
            ) {
              response.body.content.list.pop();
            }
            this.dataSource = response.body.content.list;
            if (!this.showCount) {
              this.count =
                response.body.content.count > this.searchObj.page_size
                  ? this.searchObj.page_no * this.searchObj.page_size + 1
                  : this.searchObj.page_no * this.searchObj.page_size;
            }
          } else {
            this.$message.error(response.body.msg);
          }
        }
      );
    },
    // 刷新
    refresh() {
      this.search();
    },
    // 处理进度标签
    onSignChange(value) {
      if (value) {
        if (this.selections?.length) {
          const postData = {
            balance_ids: this.selections.map(item => item.balance_id),
            process_progress_tag: value,
          };
          this.ajax.postStream("/afterSale-web/api/aftersale/orderRefundBalance/updateBatchProcessTag?permissionCode=AFTERSALE_ORDER_REFUND_BALANCE_UPDATE_PROCESSTAG", postData, (res) => {
            if (res.body.result) {
              this.$message.success(res.body.msg);
              this.refresh();
            } else {
              this.$message.error(res.body.msg);
            }
          });
        } else {
          this.$message.error("请先选择数据");
        }
        this.$nextTick(() => {
          this.signValue = "";
        });
      }
    },
    // 预搜索
    preSearch(obj, resolve) {
      this.searchObj.where = obj;
      this.search().then(() => {
        resolve && resolve();
        if (this.searchObj.page_no != 1) {
          this.count = 0;
        }
        this.showCount = false;
      });
    },
    onSelect(val) {
      this.selections = val;
    },
    onSizeChange(size) {
      this.searchObj.page_size = size;
      this.search();
    },
    onPageChange(pageNo) {
      this.searchObj.page_no = pageNo;
      this.search();
    },
    onCountOff() {
      if (!this.dataSource.length) {
        this.$message.error("当前列表为空，先搜索内容");
        return;
      }
      if (!!this.countOffFlag) {
        this.$message.error("请勿重复点击");
        return;
      }
      this.countOffFlag = true;
      const url = "/afterSale-web/api/aftersale/orderRefundBalance/total";
      const searchObj = JSON.parse(JSON.stringify(this.searchObj));
      this.ajax.postStream(url, searchObj, (response) => {
        if (response.body.result) {
          this.count = response.body.content;
          this.showCount = true;
          this.countOffFlag = false;
        } else {
          this.$message.error(res.body.msg);
        }
      });
    },
    onExport() {
      if (this.searchObj.where.length == 0) {
        this.$message.error("导出必须添加查询条件");
        return;
      }
      const url =
        "/afterSale-web/api/aftersale/orderRefundBalance/addExport?permissionCode=AFTERSALE_ORDER_REFUND_BALANCE_EXPORT";
      const postData = JSON.parse(JSON.stringify(this.searchObj));
      this.isExporting = true;
      this.ajax.postStream(url, postData, (res) => {
        this.isExporting = false;
        if (res.body.result) {
          this.$message.success(res.body.msg);
        } else {
          this.$message.error(res.body.msg);
        }
      });
    },
    showExportList() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "报表导出列表",
        params: {
          query: {
            type: "EXCEL_TYPE_REFUND_BALANCE_EXPORT",
          },
        },
      });
    },
  },
};
</script>

<style scoped></style>
