<!-- 退款单列表 -->
<template>
	<xpt-list
		:data='roleList'
		:orderNo="true"
		:colData='colData'
		:pageTotal='pageTotal'
		:btns='btns'
	    :searchPage="form.page_name"
	    :isNeedClickEvent="true"
		:taggelClassName='tableRowClassName'
		:showCount ='showCount'
        @count-off="countOff"
		@selection-change='d => selectRole = d'
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
		@row-dblclick="d => openOrder(d.id)"
		@cell-click="cellClick"
		@search-click='search'
		ref='xptList'
	>
	</xpt-list>
</template>
<script>
export default {
	props:['params'],
	data (){
		let self = this;
		return {
			countOffFlag:false,
			  showCount:false,
			roleList:[],
			selectRole:[],
			pageTotal:0,
			isShowColData3OrColData2: [/*(单据状态 = 未审核) => 'colData2' or (业务状态 = 财务待办) => 'colData1'*/, /*where条件*/],//点击通用查询组件方案时，认为接下来的查询都按特定字段显示
			form: {
				page_name: 'aftersale_bill_refund',
				pageSize: 50,
				pageNo: 1,
				where: [],
			},
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: () => this.searchFun(),
	        }, {
				type: 'primary',
				txt: '查看附件',
				click: this.viewFile,
				// disabled: this.selectRole.length !== 1,
	        }, {
				type: 'primary',
				txt: '审核',
				click: this.audit,
				// disabled: this.selectRole.length == 0,
	        }, {
				type: 'primary',
				txt: '锁定',
				click: e => this.lockOrUnlock(e, 1),
				// disabled: this.selectRole.length == 0,
	        }, {
				type: 'primary',
				txt: '解锁',
				click: e => this.lockOrUnlock(e, 2),
				// disabled: this.selectRole.length !== 1,
	        }],
			colData: [],
			colData3:[//全字段显示
				{
					label: '单据编号',
					prop: 'bill_no',
					width: 150,
					redirectClick: d => {
						this.openOrder(d.id)
					},
				},{
					label: '单据状态',
					prop: 'status',
					formatter: prop => ({ 
						CREATE: '创建',
						REAUDIT: '重新审核',
						AUDITING: '审核中',
						AUDITED: '已审核',
						CANCELED: '作废',
					}[prop] || prop),
				},{
					label: '买家昵称',
					prop: 'nick_name',
					width: 110,
				},{
		            label: '是否经销商订单',
		            width:'100',
		            prop: 'if_dealer',
		            formatter: prop => ({
		              Y: '是',
		              N: '否',
		            }[prop] || prop),
		        },{
					label: '申请退款金额',
					prop: 'total_amount',
				},{
					label: '实际退款金额',
					prop: 'refund_amount',
				},{
					label: '业务锁定人',
					prop: 'business_locker_name',
					width: 110,
				}/*,{
					label: '淘宝退款状态',
					prop: '_taobao_refund_status',
				},{
					label: '推荐处理人',
					prop: '_taobao_refund_status',
				}*/,{
					label: '业务状态',
					prop: 'business_status',
					formatter: prop => ({ 
						BUSINESS_TODO: '业务待办',
						BUSINESS_PROCESSING: '业务处理中',
						FINANCIAL_TODO: '财务待办',
						FINANCIAL_PROCESSING: '财务处理中',
						FINANCIAL_PROCESSED: '财务处理完成',
						REJECTED: '驳回',
					}[prop] || prop),
				},{
					label: '锁定状态',
					prop: 'finance_locker',
					formatter: prop => prop && prop > 0 ? '已锁定' : '未锁定',
				},{
					label: '财务锁定人',
					prop: 'finance_locker_name',
				},{
					label: '业务锁定时间',
					prop: 'business_lock_date',
					format: 'dataFormat1',
					width: 200,
				},{
					label: '提交财务人',
					prop: 'submit_finance_person_name',
					width: 110,
				},{
					label: '业务锁定分组',
					prop: 'business_locker_group',
				},{
					label: '业务锁定大分组',
					prop: 'business_locker_big_group',
				}/*,{
					label: '部门',
					prop: 'dept',
				}*/,{
					label: '提交财务日期',
					prop: 'submit_finance_date',
					format: 'dataFormat1',
					width: 200,
				},{
					label: '财务锁定时间',
					prop: 'finance_lock_date',
					format: 'dataFormat1',
					width: 200,
				}/*,{
					label: '退款方式',//退款单无此字段
					prop: '_refund_way',
				}*/,{
					label: '合并单号',
					prop: 'merge_trade_no',
					width: 200,
				},{
					label: '审核人',
					prop: 'auditor_name',
				},{
					label: '审核日期',
					prop: 'audit_time',
					format: 'dataFormat1',
					width: 200,
				},{
					label: '驳回人',
					prop: 'rejector_name',
				},{
					label: '驳回时间',
					prop: 'reject_date',
					format: 'dataFormat1',
					width: 200,
				},
			],
			colData2:[//单据状态 = 未审核
				{
					label: '单据编号',
					prop: 'bill_no',
					width: 150,
					redirectClick: d => {
						this.openOrder(d.id)
					},
				},{
					label: '提交财务日期',
					prop: 'submit_finance_date',
					format: 'dataFormat1',
					width: 200,
				},{
					label: '单据状态',
					prop: 'status',
					formatter: prop => ({ 
						CREATE: '创建',
						REAUDIT: '重新审核',
						AUDITING: '审核中',
						AUDITED: '已审核',
						CANCELED: '作废',
					}[prop] || prop),
				},{
					label: '业务状态',
					prop: 'business_status',
					formatter: prop => ({ 
						BUSINESS_TODO: '业务待办',
						BUSINESS_PROCESSING: '业务处理中',
						FINANCIAL_TODO: '财务待办',
						FINANCIAL_PROCESSING: '财务处理中',
						FINANCIAL_PROCESSED: '财务处理完成',
						REJECTED: '驳回',
					}[prop] || prop),
				},{
		            label: '是否经销商订单',
		            width:'100',
		            prop: 'if_dealer',
		            formatter: prop => ({
		              Y: '是',
		              N: '否',
		            }[prop] || prop),
		        },{
					label: '锁定状态',
					prop: 'submit_account_person',
					formatter: prop => prop && prop > 0 ? '已锁定' : '未锁定',
				},{
					label: '财务锁定人',
					prop: 'finance_locker_name',
				},{
					label: '买家昵称',
					prop: 'nick_name',
					width: 110,
				},{
					label: '实际退款金额',
					prop: 'refund_amount',
				}/*,{
					label: '淘宝退款状态',
					prop: '_taobao_refund_status',
				}*//*,{
					label: '退款方式',
					prop: '_refund_way',
				}*//*,{
					label: '店铺',
					prop: '_shop_name',
				},{
					label: '收入店铺',
					prop: '_user_shop_name',
				}*//*,{
					label: '单据来源',
					prop: '_source_type',
				}*//*,{
					label: '淘宝退款单号',
					prop: '_taobao_refund_no',
				}*/,{
					label: '提交财务人',
					prop: 'submit_finance_person_name',
					width: 110,
				},{
					label: '业务锁定人',
					prop: 'business_locker_name',
					width: 110,
				},{
					label: '业务锁定分组',
					prop: 'business_locker_group',
				},{
					label: '业务锁定大分组',
					prop: 'business_locker_big_group',
				}/*,{
					label: '部门',
					prop: 'dept',
				}*/
			],
			colData1:[//业务状态 = 财务待办
				{
					label: '单据编号',
					prop: 'bill_no',
					width: 150,
					redirectClick: d => {
						this.openOrder(d.id)
					},
				},{
					label: '提交财务日期',
					prop: 'submit_finance_date',
					format: 'dataFormat1',
					width: 200,
				},{
					label: '提交财务人',
					prop: 'submit_finance_person_name',
					width: 110,
				},{
		            label: '是否经销商订单',
		            width:'100',
		            prop: 'if_dealer',
		            formatter: prop => ({
		              Y: '是',
		              N: '否',
		            }[prop] || prop),
		        },{
					label: '业务锁定人',
					prop: 'business_locker_name',
					width: 110,
				},{
					label: '业务锁定分组',
					prop: 'business_locker_group',
				},{
					label: '业务锁定大分组',
					prop: 'business_locker_big_group',
				}/*,{
					label: '部门',
					prop: 'dept',
				}*/,{
					label: '单据状态',
					prop: 'status',
					formatter: prop => ({ 
						CREATE: '创建',
						REAUDIT: '重新审核',
						AUDITING: '审核中',
						AUDITED: '已审核',
						CANCELED: '作废',
					}[prop] || prop),
				},{
					label: '业务状态',
					prop: 'business_status',
					formatter: prop => ({ 
						BUSINESS_TODO: '业务待办',
						BUSINESS_PROCESSING: '业务处理中',
						FINANCIAL_TODO: '财务待办',
						FINANCIAL_PROCESSING: '财务处理中',
						FINANCIAL_PROCESSED: '财务处理完成',
						REJECTED: '驳回',
					}[prop] || prop),
				},{
					label: '锁定状态',
					prop: 'finance_locker',
					formatter: prop => prop && prop > 0 ? '已锁定' : '未锁定',
				},{
					label: '财务锁定人',
					prop: 'finance_locker_name',
				},{
					label: '买家昵称',
					prop: 'nick_name',
					width: 110,
				},{
					label: '实际退款金额',
					prop: 'refund_amount',
				}/*,{
					label: '淘宝退款状态',
					prop: '_taobao_refund_status',
				}*//*,{
					label: '退款方式',
					prop: '_refund_way',
				}*//*,{
					label: '收入店铺',
					prop: '_user_shop_name',
				},*/
			],
		}
	},
	methods: {
		// 单据状态为重新审核的，整行标红显示
		tableRowClassName (row, index){
			if(row.status === 'REAUDIT') {
				return this.$style['mergered']
			}
		},
		// 审核
		audit (){
			var auditIds = []
			,	cannotAuditIds = []
			,	userId = this.getEmployeeInfo('id')
			,	msgList = []
			
			if(!this.selectRole.length){
				this.$message.error('请选择至少一项')
				return
			}

			this.selectRole.forEach(obj => {
				if(obj.finance_locker === userId){
					auditIds.push(obj)
				}else {
					cannotAuditIds.push(obj)
				}
			})

			if(cannotAuditIds.length){
				this._overrideToggleRowSelection.bool = true
				cannotAuditIds.forEach(obj => {
					this.$refs.xptList.$refs.table.toggleRowSelection(obj, false)
				})
				if(auditIds.length){
					this.$message.error('已去除不符合财务锁定人等于操作人的选项')
				}else {
					this.$message.error({
						message: '已去除不符合财务锁定人等于操作人的选项。\n请选择至少一项!',
						customClass: this.$style['message-box1'],
					})
					return
				}
			}

			auditIds.forEach(obj => {
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refund/audit?permissionCode=REFUND_ORDER_AUDIT', { id: obj.id }, res => {
					msgList.push('单据编号' + obj.bill_no + ':' + res.body.msg)

					if(msgList.length === auditIds.length){
						this.$message.info({
							message: msgList.join('\n'),
							customClass: this.$style['message-box'],
							duration: 5000,
						})
						this.searchFun(null, '不能为空', data => {
							auditIds.forEach(obj => {
								// console.log(
								// 	'data[this.roleList.indexOf(obj)]123',
								// 	JSON.parse(JSON.stringify(data[this.roleList.indexOf(obj)])),
								// 	JSON.parse(JSON.stringify(obj))
								// )
								Object.assign(obj, data[this.roleList.indexOf(obj)])
							})
						})
					}
				})
			})
		},
		// tabClick (){
		// 	this.colData = this.colDataSrc.filter(obj => this.activeTab == 5 || obj._type.split('|').indexOf(this.activeTab) !== -1)
		// 	this.btns = this.btnsSrc.filter(obj => this.activeTab == 5 || obj._type.split('|').indexOf(this.activeTab) !== -1)
		// },
		openOrder (id){
			this.$root.eventHandle.$emit('creatTab',{
				name: '退款单详情',
				params: {
					id,
					idList: this.roleList.map(obj => obj.id),
				},
				component:()=> import('@components/after_sales_refund/refundOrder_2')
			})
		},
		delOrder (){
			this._ajax('delete', this.selectRole.map(obj => obj.id), () => {
				this.searchFun()
			})
		},
		// 锁定 or 解锁(非批量)
		//1为锁定，2为解锁
		lockOrUnlock (e, type){
			if(type === 1){
				if(!this.selectRole.length){
					this.$message.error('请选择至少一行')
					return
				}
			}else {
				if(this.selectRole.length !== 1){
					this.$message.error('请选择一行')
					return
				}
			}

			var $btn = e.target
			$btn.disabled = true;
			var params;

			if(type == 1){
				//锁定
				params = [];
				this.selectRole.map((a,b)=>{
					params.push(a.id);
				});
			}else{
				params = { id: this.selectRole[0].id };
			}
			if(!params) return;
			let url = type == 1?'/afterSale-web/api/aftersale/bill/refund/lockBatch?permissionCode=REFUND_ORDER_LOCK':'/afterSale-web/api/aftersale/bill/refund/unlock?permissionCode=REFUND_ORDER_UNLOCK'
			this.ajax.postStream(url,params/*{ id: this.selectRole[0].id }*/, res => {
				$btn.disabled = false
				if(res.body.result){
					this.$message.success(res.body.msg)
					this.searchFun(null, res.body.msg)
					// this.selectRole[0].business_status = type === 1 ? 'FINANCIAL_PROCESSING'/*财务处理中*/ : 'FINANCIAL_TODO'/*业务待办*/
				}else {
					this.$message.error(res.body.msg)
				}
			}, () => {
				$btn.disabled = false
			})
		},
		// 查看附件
		viewFile (){
			if(this.selectRole.length !== 1){
				this.$message.error('请选择一行')
				return
			}

			this.$root.eventHandle.$emit('alert', {
				params: {
					parent_name_txt: '售后单号',
					parent_no : this.selectRole[0].after_order_no,
					child_no : null,
					// child_name_txt: '商品问题id',
					ext_data : null,
					parent_name : 'AFTER_ORDER',
					child_name : 'REFUNDBILLDETAIL',
					// callback: files => {
					// 	this.listAfterMaterialVO[index].attachment_count = files.length
					// },
				},
				component: ()=>import('@components/common/download.vue'),
				style: 'width:80%;height:600px',
				title: '下载列表',
			})
		},
		countOff(){

			let self = this;
			let url =  '/afterSale-web/api/aftersale/bill/refund/listCount'

			if(!self.roleList.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
			self.$message.error("请勿重复点击");
			return;
			}
			self.countOffFlag = true;

			self.ajax.postStream(url,self.form,function(response){
				if(response.body.result){
				
				self.pageTotal = response.body.content;
				self.showCount = true;
				self.countOffFlag = false;

				}else{
				self.$message.error(response.body.msg);
				}
			});
		},
		searchFun(resolve, msg, callback){
			let self = this;
			var colDataType = this[this.isShowColData3OrColData2[0] || 'colData3']//默认为this.colData3,搜索全部字段
			,	postData = JSON.parse(JSON.stringify(this.form))

			this._ajax('queryRefundBillNewList?permissionCode=REFUND_ORDER_QUERY', postData, res => {
				var data = JSON.parse(JSON.stringify((res.body.content && res.body.content.list) || []))

				if(callback){
					callback(data)
					return
				}

				this.colData = colDataType
				// this.roleList = this._tran(res.body.content && res.body.content.list || [], repeatItem)

				// this.roleList = data

				// this.pageTotal = res.body.content.count
				let dataList = JSON.parse(JSON.stringify(res.body.content.list));
				if(dataList.length == (self.form.pageSize+1)&&dataList.length>0){
					dataList.pop();
				}
				self.roleList = dataList;
				let totalCount = self.form.pageSize*self.form.pageNo;
				if(!self.showCount){
					self.pageTotal = res.body.content.count == (self.form.pageSize+1)? totalCount+1:totalCount;
				}

			}, resolve, msg)
		},
		// 数据中英文转换
		// _tran (data, repeatItem){
		// 	var newData = []

		// 	data.forEach((obj, index) => {
		// 		var repeatArray = []
		// 		Object.keys(repeatItem).forEach(key => {
		// 			if(obj[key]){
		// 				obj[key].forEach(item => {
		// 					repeatItem[key].forEach(itemKey => {
		// 						var newObj = Object.assign({}, obj)
		// 						newObj['_' + itemKey] = item[itemKey]
		// 						repeatArray.push(newObj)
		// 					})
		// 				})
		// 			}
		// 		})
		// 		if(repeatArray.length) newData[index] = repeatArray
		// 	})
		// 	newData.forEach((obj, index) => {
		// 		data.splice(index, 1, ...obj)
		// 	})

		// 	return data
		// },
		_ajax (apiName, postData, cb, resolve, msg){
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refund/' + apiName, postData, res => {
				if(res.body.result){
					!msg && this.$message.success(res.body.msg)
					cb && cb(res)
				}else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve()
			}, () => {
				resolve && resolve()
			}, this.params.tabName)
		},
		// 搜索
		search(list, resolve, isFromSelectPlan) {
			let self = this;
			if(this.isShowColData3OrColData2[0] && !isFromSelectPlan){
				if(list[0]){
					list = list.concat(JSON.parse(JSON.stringify(this.isShowColData3OrColData2[1])))
					list.forEach(obj => {
						if(!obj.condition) obj.condition = 'AND'
						if(!obj.listWhere) obj.listWhere = []
					})
				}else {//点击了重置
					list = [this.isShowColData3OrColData2[1]]
				}
			}else {
				if(list[0]){
					if(!list[0].listWhere && !list[0].condition){//从方案点击过来
						if(list[0].value === 'FINANCIAL_TODO'){
							this.isShowColData3OrColData2 = ['colData1', list[0]]
						}else if (list[0].value === 'AUDITED' && list[0].operator === '<>'){
							this.isShowColData3OrColData2 = ['colData2', list[0]]
						}
					}else {
						/*empty*/
					}
				}else {//点击了重置
					/*empty*/
				}
			}

			this.form.where = list


			 new Promise((res,rej)=>{
				this.searchFun(resolve,"",res)
				}).then(()=>{
				if(self.form.pageNo != 1){
					self.pageTotal = 0;
				}
					self.showCount = false;
				})
		},
		pageChange(pageSize){
			this.form.pageSize = pageSize
			this.searchFun()
		},
		currentPageChange(page){
			this.form.pageNo = page
			this.searchFun()
		},
		_setDefaultPlan (){
			var allPlans = JSON.parse(localStorage.getItem('SQL_PLAN') || '{}')

			if(!allPlans.aftersale_bill_refund) allPlans.aftersale_bill_refund = {}

			var thisPagePlans = allPlans.aftersale_bill_refund

			if(!thisPagePlans['未审核'] || !thisPagePlans['财务待办']){
				this.ajax.postStream('/user-web/api/sql/listFields', { page: 'aftersale_bill_refund' }, res => {
					if(res.body.result){
						res.body.content.fields.forEach(obj => {
							if(obj.comment === '单据状态' && !thisPagePlans['未审核']){
								_save('未审核', obj, '<>', obj.values.filter(obj => obj.name === '已审核')[0].code)
							}else if (obj.comment === '业务状态' && !thisPagePlans['财务待办']){
								_save('财务待办', obj, '=', obj.values.filter(obj => obj.name === '财务待办')[0].code)
							}
						})
						this.$refs.xptList.$refs.xptSearchEx.loadPlan()
					}
				})
			}

			function _save (name, field, operator, value){
				thisPagePlans[name] = {
					name,
					where: [{
						condition: 'AND',
						field,
						listWhere: [],
						operator,
						table: '',
						value,
					}],
				}
				localStorage.setItem('SQL_PLAN', JSON.stringify(allPlans))
			}
		},
		cellClick (obj, col, cell, event){
			var isClickCheckbox/*是否点击了checkbox*/ = !col.label

			// event.preventDefault()
			this._overrideToggleRowSelection.bool = isClickCheckbox

			if(isClickCheckbox && this._listenKeyEvent.isPressingShift){
				var a1 = this.roleList.indexOf(this.selectRole[0])
				,	a3 = this.roleList.indexOf(obj)

				if(a1 > a3) {
					[a1, a3] = [a3, a1]//大小调转
				}

				if(a1 > -1 && a3 > -1){
					setTimeout(() => {
						this.$refs.xptList.$refs.table.clearSelection()
						this.roleList.forEach((obj, index) => {
							if(index > a1 && index < a3 || (index === a1 || index === a3)){
								this.$refs.xptList.$refs.table.toggleRowSelection(obj)
							}
						})
					})
				}
			}
		},
		// 修改el-table原生toggleRowSelection方法
		_overrideToggleRowSelection (){
			var self = this
			,	$table = this.$refs.xptList.$refs.table
			,	_toggleRowSelectionFunc = $table.toggleRowSelection

			$table.toggleRowSelection = function (obj, bool){
				!self._overrideToggleRowSelection.bool && $table.clearSelection()
				_toggleRowSelectionFunc(obj, bool)
			}
		},
		_listenKeyEvent (e){
			if(e.type === 'keyup'){
				this._listenKeyEvent.isPressingShift = false
			}else if (e.type === 'keydown' && e.shiftKey){
				this._listenKeyEvent.isPressingShift = true
			}
		},
	},
	mounted (){
		// this.tabClick()
		this.colData = this.colData3
		this._setDefaultPlan()
		this.searchFun()

		this._overrideToggleRowSelection()

		window.addEventListener('keydown', this._listenKeyEvent, false)
		window.addEventListener('keyup', this._listenKeyEvent, false)
	},
	destroyed (){
		window.removeEventListener('keydown', this._listenKeyEvent, false)
		window.removeEventListener('keyup', this._listenKeyEvent, false)
	},
	// watch: {
	// 	'form.select_type': function (newVal) {
	// 		if(this.form.select_type === 'business_status'){
	// 			this.form.searchVal = '财务待办'
	// 		}else if (this.form.select_type === 'status'){
	// 			this.form.searchVal = '未审核'
	// 		}else {
	// 			this.form.searchVal = ''
	// 		}
	// 	},
	// },
}
</script>

<style module>
.message-box :global(.el-message__group) p, .message-box1 :global(.el-message__group) p {
    white-space: pre-wrap;
}
.message-box :global(.el-message__img) {
    height: 100%;
    top: 50%;
    transform: translateY(-50%);
    background-color: #50bfff;
}
.message-box :global(.el-message__group) {
    height: auto;
}
.mergered, .mergered td{
	background-color: #f99 !important;
}
</style>
