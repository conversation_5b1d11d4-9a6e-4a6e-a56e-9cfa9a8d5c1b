<!-- 退款单 -->
<template>
<div :class="'xpt-flex ' + $style['maxHeight']">
	<el-form :model='form' ref='form' label-position="right" label-width="120px">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="18">
				<el-button type="success" size="mini" @click="save"
					disabled>保存</el-button>
				<el-button type="primary" size="mini" @click="reject"
					:disabled="!isEdit">驳回</el-button>
				<!-- <el-button type="primary" size="mini" @click="submit"
					:disabled="!isEdit || form.approve_status == 'NEED'">提交</el-button> -->
					<el-button type="primary" size="mini" @click="submit" disabled>提交</el-button>

				<el-button type="primary" size="mini" @click="revoke"
					:disabled="!isSamePersonCanEdit || !(form.status == 'AUDITING' && form.business_status == 'FINANCIAL_PROCESSING' && /(NOT_NEED|^PASS)/.test(form.approve_status))">撤回</el-button>
				<el-button type="primary" size="mini" @click="audit"
					:disabled="!isSamePersonCanEdit || !(form.status == 'AUDITING' && form.business_status == 'FINANCIAL_PROCESSING' && /(NOT_NEED|^PASS)/.test(form.approve_status))">审核</el-button><!-- 单据状态 = 审核中 && 业务状态 = 财务处理中 && 审批状态 = (不需审批 or 审批通过) -->

				<el-button type="primary" size="mini" @click="reverseAudit"
					:disabled="!(form.status == 'AUDITED' && form.business_status == 'FINANCIAL_PROCESSING')">反审核</el-button>

				<!-- <el-button type="primary" size="mini" @click="lockOrUnlock('lock?permissionCode=REFUND_ORDER_LOCK')"
					:disabled="!(/(CREATE|REAUDIT)/.test(form.status) && form.business_status == 'FINANCIAL_TODO')">锁定</el-button>

				<el-button type="primary" size="mini" @click="lockOrUnlock('unlock?permissionCode=REFUND_ORDER_UNLOCK')"
					:disabled="!isEdit">解锁</el-button> -->

				<el-button type="primary" size="mini" @click="submitApprove"
					:disabled="!isSamePersonCanEdit || !/(^NEED|NOT_PASS)/.test(form.approve_status)">提交审批</el-button>

				<xpt-btngroup :btngroup='btnGroup' class="mgl10"
					:disabled="!(form.status == 'AUDITING' && form.business_status == 'FINANCIAL_PROCESSING' && form.approve_status == 'WAIT')"></xpt-btngroup>

				<el-button type="primary" size="mini" @click="_closeTabOrRefreshCheck">刷新</el-button>
				<el-button-group>
					<el-button type="primary" @click="uploadAction('post')" size="mini"
						:disabled="!isEdit">上传<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload></el-button>
					<el-button type="primary" @click="uploadAction('get')" size="mini">查看附件</el-button>
				</el-button-group>
			</el-col>
			<el-col :span="6" style="text-align: right;">
				<el-button type="primary" size="mini" @click="nextOrPrevOrder('prev')">上一页</el-button>
				<el-button type="primary" size="mini" @click="nextOrPrevOrder('next')">下一页</el-button>
			</el-col>
		</el-row>
		<el-tabs v-model="selectTab1" ref="selectTab1Info" @tab-click="() => selectTab1 === 'payforInformation' && payInfoListSelectHandler()">
			<el-tab-pane label='基本信息' name='basicInformation1'>
				<el-row :gutter="20">
					<el-col :span="6">
						<el-form-item label="单据编号">
							<el-input size='mini' v-model="form.bill_no" :disabled="true" placeholder="系统自动生成"></el-input>
						</el-form-item>
						<el-form-item label="合并订单号">
							<el-input size='mini' v-model="form.merge_trade_no" readonly></el-input>
						</el-form-item>
						<el-form-item label="日期">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.date"
							    :disabled="!isEdit"
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="买家昵称">
							<!-- <el-input v-if="isEdit" v-model="form.nick_name" size='mini' icon="search" :on-icon-click="() => selectBuyersName(form, 'nick_name')" readonly></el-input> -->
							<el-input v-model="form.nick_name" size='mini' disabled></el-input>
						</el-form-item>
						<el-form-item>
							<el-checkbox v-model="form.if_priority" true-label="1" false-label="0" style="margin-left:-60px;" :disabled="!isEdit">是否优先处理</el-checkbox>
							<!-- <el-checkbox v-model="form.if_history" true-label="1" false-label="0" :disabled="true">是否历史数据</el-checkbox> -->
							<el-checkbox :value="form.sysPaymentList.length === 0" true-label="1" false-label="0" :disabled="true">是否历史数据</el-checkbox>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="实退金额">
							<el-input size='mini' v-model="form.refund_amount" readonly></el-input>
						</el-form-item>
						<el-form-item label="应退金额">
							<el-input size='mini' v-model="form.total_amount" readonly></el-input>
						</el-form-item>
						<el-form-item label="业务员">
							<!-- <el-input v-if="isEdit" v-model="form.saleman" size='mini' icon="search" :on-icon-click="selectBestStaff" readonly></el-input> -->
							<el-input v-model="form.saleman" size='mini' disabled></el-input>
						</el-form-item>
						<el-form-item label="驳回人">
							<el-input size='mini' v-model="form.rejector_name" disabled></el-input>
						</el-form-item>
						<!-- <el-form-item label="部门">
							<el-input size='mini' v-model="form.dept" readonly></el-input>
						</el-form-item> -->
					</el-col>
					<el-col :span="6">
						<el-form-item label="退款状态">
							<el-input size='mini' :value="{ WAIT_REFUND: '待退款', REFUNDED: '已退款', UNREFUND: '未退款' }[form.refund_status]" readonly></el-input>
						</el-form-item>
						<el-form-item label="币别">
							<!-- <el-select v-if="isEdit" v-model="form.currency" size="mini" placeholder="请选择">
								<el-option
									v-for="(value,key) in currency_options"
									:key="key.toString()"
									:label="value.toString()"
									:value="key.toString()">
								</el-option>
							</el-select> -->
							<el-input :value="currency_options[form.currency]" size='mini' disabled></el-input>
						</el-form-item>
						<!-- <el-form-item label="业务备注" :class="$style['textarea-style']">
							<el-input type="textarea" v-model="form.business_remark" :maxlength="255" :disabled="!isEdit"></el-input>
						</el-form-item> -->
						<el-form-item label="单据状态">
							<el-input size='mini' :value="{
								CREATE: '创建',
								REAUDIT: '重新审核',
								AUDITING: '审核中',
								AUDITED: '已审核',
								CANCELED: '作废',
							}[form.status]" readonly></el-input>
						</el-form-item>
						<el-form-item label="驳回时间">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.reject_date"
							    :editable="false"
							    disabled
							>
							</el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="财务备注" :class="$style['textarea-style']">
							<el-input type="textarea" autosize v-model="form.finance_remark" :maxlength="255" :disabled="!isEdit"></el-input>
						</el-form-item>
						<el-form-item label="是否经销商订单">
							<el-switch on-text="是" off-text="否" :value="{'Y':true,'N':false,'':false,'null': false,}[form.if_dealer]"  disabled></el-switch>
						</el-form-item>
						<el-form-item label="经销商编码">
							<el-input size='mini' disabled :value="form.dealer_customer_number"></el-input>
						</el-form-item>
						<el-form-item label="经销商名称">
							<el-input size='mini' disabled :value="form.dealer_customer_name"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-tab-pane>
			<el-tab-pane label='退款信息' name='payforInformation'>
				<!-- <el-row class="xpt-top" :gutter="40">
					<el-col :span="18">
						<el-button
							type="danger"
							size="mini"
							@click="() => payInfoListSelect.length ? delSelect('payInfoListSelect', 'itemList') : $message.error('请选择一行退款信息')"
							:disabled="!isEdit"
						>删除</el-button>
						<el-button type="primary" size="mini" @click="itemListImport" :disabled="!isEdit">引入</el-button>
					</el-col>
				</el-row> -->
				<div class="xpt_pmm_scroll scroll">
					<el-table
						border tooltip-effect="dark" style="width: 100%;" width='100%'
						:data="form.payInfoList"
						:row-class-name="$style['table-input-w100']"
						@selection-change="payInfoListSelectHandler"
						@row-click="payInfoListRowClick"
						ref="$payInfoList"
					>
						<el-table-column type="selection" width="50" :selectable="() => isEdit"></el-table-column>
						<el-table-column label="驳回备注" 		prop="reject_remark" show-overflow-tooltip width="300">
							<template slot-scope="scope">
								<div style="position:relative;">
									<el-input
										size="mini"
										v-model="scope.row.reject_remark"
										style="position:absolute;width:100%;"
										:maxlength="255"
										:disabled="!isEdit"
									></el-input>
									<span style="color:#fff;">{{ scope.row.reject_remark }}</span>
								</div>
							</template>
						</el-table-column>
						<el-table-column label="退款方式" 		prop="refund_way" show-overflow-tooltip width="100">
							<template slot-scope="scope">
								{{ refund_way_options[scope.row.refund_way] }}
							</template>
						</el-table-column>
						<el-table-column label="银行名称" 		prop="bank_name" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column label="开户行" 		prop="opening_bank" show-overflow-tooltip width="200"></el-table-column>
						<!-- <el-table-column label="支付宝账号" 	prop="alipay_account" show-overflow-tooltip width="100"></el-table-column>
						<el-table-column label="支付类型"   	prop="pay_type" show-overflow-tooltip width="100">
							<template slot-scope="scope">
								{{ pay_type_options[scope.row.pay_type] }}
							</template>
						</el-table-column> -->
						<el-table-column label="收款账号" 		prop="accouont" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="姓名" 			prop="user_name" show-overflow-tooltip></el-table-column>
						<el-table-column label="应退金额" 		prop="total_amount" show-overflow-tooltip></el-table-column>
						<el-table-column label="应退核实金额" 	prop="check_amount" width="150" >
							<!-- <template slot-scope="scope">
								<el-input  size="mini" v-model="scope.row.check_amount"  type="number" style="width:100%;" @change="changeCheckAmount"></el-input>
							</template> -->
						</el-table-column>
						<!-- <el-table-column label="电话" 			prop="phone" show-overflow-tooltip width="100"></el-table-column> -->
						<el-table-column label="订单店铺" 		prop="shop_name" show-overflow-tooltip width="100"></el-table-column>
						<el-table-column label="收入店铺" 		prop="user_shop_name" show-overflow-tooltip width="100"></el-table-column>
						<el-table-column label="买家昵称"		prop="nick_name" show-overflow-tooltip width="100"></el-table-column>
						<el-table-column label="原始店铺" 		prop="original_shop_name" show-overflow-tooltip width="100"></el-table-column>
						<el-table-column label="业务备注" 		prop="remark" show-overflow-tooltip width="300"></el-table-column>
						<el-table-column label="客户" 			prop="customer" show-overflow-tooltip width="100"></el-table-column>
						<el-table-column label="淘宝退款单号" 	prop="taobao_refund_no" show-overflow-tooltip width="200"></el-table-column>
					</el-table>
				</div>
			</el-tab-pane>
			<el-tab-pane label='淘宝申请退款信息' name='taobaoInformation'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="18">
						<el-button type="danger" size="mini" @click="() => delSelect('taobaoListSelect', 'taobaoList')" :disabled="!taobaoListSelect.length">删除行</el-button>
						<el-button type="primary" size="mini" @click="introduceFun" :disabled="(/(AUDITED|CANCELED)/).test(form.status)">引入</el-button>
					</el-col>
				</el-row>
				<div class="xpt_pmm_scroll scroll">
					<el-table :data="form.taobaoList" border tooltip-effect="dark" style="width: 100%;" width='100%' @selection-change="s => taobaoListSelect = s">
						<el-table-column type="selection" width="50" :selectable="() => isEdit"></el-table-column>
						<el-table-column label="买家昵称" 			prop="nick_name" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="淘宝退款金额" 		prop="refund_amount" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="淘宝退款状态" 		prop="taobao_refund_status" show-overflow-tooltip width="150">
							<template slot-scope="scope">
								{{ taobao_refund_status_options[scope.row.taobao_refund_status] }}
							</template>
						</el-table-column>
						<el-table-column label="退款方式" 			prop="refund_way" show-overflow-tooltip width="150">
							<template slot-scope="scope">
								{{ refund_way_options[scope.row.refund_way] }}
							</template>
						</el-table-column>
						<el-table-column label="淘宝退款单号" 		prop="taobao_refund_no" show-overflow-tooltip width="150">
							<template slot-scope="scope">
								<a href="javascript:;" @click="toTaobaoRefund(scope.row.taobao_refund_no)">{{ scope.row.taobao_refund_no }}</a>
							</template>
						</el-table-column>
						<el-table-column label="淘宝店铺" 			prop="taobao_shop_name" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="申请日期"			prop="apple_time" show-overflow-tooltip width="150">
							<template slot-scope="scope">{{ scope.row.apple_time | dataFormat1 }}</template>
						</el-table-column>
						<el-table-column label="退款超时时间" 		prop="overtime" show-overflow-tooltip width="150">
							<template slot-scope="scope">{{ scope.row.overtime | dataFormat1 }}</template>
						</el-table-column>
						<el-table-column label="最后更新时间" 		prop="last_modify_time" show-overflow-tooltip width="150">
							<template slot-scope="scope">{{ scope.row.last_modify_time | dataFormat1 }}</template>
						</el-table-column>
						<el-table-column label="小二介入" 			prop="cs_status" show-overflow-tooltip width="150">
							<template slot-scope="scope">
								{{ cs_status_options[scope.row.cs_status] }}
							</template>
						</el-table-column>
						<el-table-column label="小二介入时间" 		prop="cs_interpose_time" show-overflow-tooltip width="150">
							<template slot-scope="scope">{{ scope.row.cs_interpose_time | dataFormat1 }}</template>
						</el-table-column>
						<el-table-column label="支付宝账号" 		prop="alipay_account" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="推荐处理人"			prop="recommend_handler_name" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="淘宝单号" 			prop="taobao_order_no" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="退款原因" 			prop="refund_reason" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="阿里掌柜订单类型" 	prop="tb_order_type" show-overflow-tooltip width="150"></el-table-column>
					</el-table>
				</div>
			</el-tab-pane>
			<el-tab-pane label='其他信息' name='otherInformation'>
				<el-row :gutter="20">
					<el-col :span="6">
						<el-form-item label="创建人">
							<el-input size='mini' v-model="form.creator_name" readonly></el-input>
						</el-form-item>
						<el-form-item label="创建日期">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.create_time"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="业务锁定人">
							<el-input size='mini' v-model="form.business_locker_name" readonly></el-input>
						</el-form-item>
						<el-form-item label="业务锁定日期">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.business_lock_date"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="业务状态">
							<el-input size='mini' :value="{ BUSINESS_TODO: '业务待办', BUSINESS_PROCESSING: '业务处理中', FINANCIAL_TODO: '财务待办', FINANCIAL_PROCESSING: '财务处理中', FINANCIAL_PROCESSED: '财务处理完成', REJECTED: '驳回', }[form.business_status]" readonly></el-input>
						</el-form-item>
						<el-form-item label="提交财务日期">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.submit_finance_date"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="提交财务人">
							<el-input size='mini' v-model="form.submit_finance_person_name" readonly></el-input>
						</el-form-item>
						<el-form-item label="业务锁定分组">
							<el-input size='mini' v-model="form.business_locker_group" readonly></el-input>
						</el-form-item>
						<el-form-item label="业务锁定大分组">
							<el-input size='mini' v-model="form.business_locker_big_group" readonly></el-input>
						</el-form-item>
						<el-form-item label="财务锁定人">
							<el-input size='mini' v-model="form.finance_locker_name" readonly></el-input>
						</el-form-item>
						<el-form-item label="财务锁定日期">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    :value="form.finance_lock_date || ''"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="审核人">
							<el-input size='mini' v-model="form.auditor_name" readonly></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="审核日期">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    :value="form.audit_time || ''"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item>
						<!-- <el-form-item label="驳回人">
							<el-input size='mini' v-model="form.rejector_name" readonly></el-input>
						</el-form-item>
						<el-form-item label="驳回日期">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.reject_date"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item> -->
						<el-form-item label="退款确认时间">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.refund_confirm_time"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="审批人">
							<el-input size='mini' v-model="form.approver" readonly></el-input>
						</el-form-item>
						<el-form-item label="审批状态">
							<el-input size='mini' :value="{ NOT_NEED: '不需审批', NEED: '需审批', WAIT: '待审批', PASS: '审批通过', NOT_PASS: '审批不通过' }[form.approve_status]" readonly></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<!-- <el-form-item label="关闭人">
							<el-input size='mini' v-model="form.closer_name" readonly></el-input>
						</el-form-item>
						<el-form-item label="关闭日期">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.closer_name_time"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item> -->
						<el-form-item label="下达时间">
							<el-date-picker
							    type="datetime"
							    placeholder="选择日期:" size="mini"
							    v-model="form.transmit_time"
							    :editable="false"
							    readonly
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="接口状态">
							<el-input size='mini' :value="{
								WAIT_TRANSMIT: '等待下达',
								TRANSMITTING: '下达中',
								TRANSMITTED: '下达成功',
								TRANSMIT_FAIL: '下达失败',
								CANCELED: '已取消',
							}[form.api_status]" readonly></el-input>
						</el-form-item>
						<el-form-item label="下达信息提示" :class="$style['textarea-style']">
							<el-input type="textarea" autosize v-model="form.api_transfer_result" :maxlength="200" :disabled="!isEdit"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-tab-pane>
		</el-tabs>
	</el-form>
	<el-row class='xpt-flex__bottom' v-fold>
	<el-tabs v-model="selectTab2" @tab-click="selectTab2Click">
		<el-tab-pane label='退款明细' name='refoudDetail' class='xpt-flex'>
			<div class="xpt-flex__bottom scroll">
				<el-table
					:data="form.itemList.length ? form.itemList.concat({
						actual_amount: '合计' + form.refund_amount,
						apply_amount: '合计' + Number(form.itemList.reduce((count, obj) => count + obj.apply_amount, 0).toFixed(2)),
						deduct_fee: '合计' + Number(form.itemList.reduce((count, obj) => count + obj.deduct_fee, 0).toFixed(2)),
					}) : []"
					border
					tooltip-effect="dark"
					style="width: 100%;"
					width='100%'
					ref="$refoudDetail"
				>
					<el-table-column type="selection" width="50" :selectable="() => 0"></el-table-column>
					<el-table-column label="序号" type="index" width="50">
						<template slot-scope="scope">
							<div class="table-index">{{ scope.$index < form.itemList.length ? scope.$index + 1 : '' }}</div>
						</template>
					</el-table-column>
					<el-table-column label="退款类型" prop="refund_type" width="170">
						<template slot-scope="scope">
							{{ refund_type_options[scope.row.refund_type] }}
						</template>
					</el-table-column>
					<el-table-column label="退款原因" prop="refund_reason" show-overflow-tooltip></el-table-column>
					<el-table-column label="申请金额" prop="apply_amount" width="120"></el-table-column>
					<el-table-column label="扣费"     prop="deduct_fee" width="120"></el-table-column>
					<el-table-column label="实退金额" prop="actual_amount" width="120"></el-table-column>
					<el-table-column label="来源类型" prop="source_type" width="120">
						<template slot-scope="scope">{{ {RETURNS_PLAN: '退货方案',REFUND_PLAN: '退款方案',LS_SALE_ORDER: '林氏销售订单',REFUND_APPLY_BILL:'退款申请单'}[scope.row.source_type] }}</template>
					</el-table-column>
					<el-table-column label="来源单号" prop="source_order_no" width="200">
						<template slot-scope="scope">
							<!-- <a v-if="scope.row.source_type === 'LS_SALE_ORDER' " href="javascript:;" @click="toRefundRequest(scope.row.refund_apply_bill_id)">{{ scope.row.source_order_no || scope.row.refund_apply_bill_id }}</a> -->
							<a v-if="scope.row.source_type == 'LS_SALE_ORDER' || scope.row.source_type == 'REFUND_APPLY_BILL'" href="javascript:;" @click="toRefundRequest(scope.row.refund_apply_bill_id)">{{ scope.row.source_order_no || scope.row.refund_apply_bill_id }}</a>
							<span v-else>{{ scope.row.source_order_no }}</span>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</el-tab-pane>
		<el-tab-pane label='支付明细' name='payforDetail' class='xpt-flex'>
			<div class="xpt-flex__bottom scroll">
				<el-table
					:data="form.sysPaymentList.length ? form.sysPaymentList.concat('') : form.sysPaymentList"
					:row-class-name='paymentListClassName'
					border
					tooltip-effect="dark"
					style="width: 100%;"
					width='100%'
				>
				<!-- <el-table :data="form.sysPaymentList" border tooltip-effect="dark" style="width: 100%;" width='100%' :summary-method="getSummaries" show-summary> -->
					<el-table-column label="序号" type="index" width="50">
						<template slot-scope="scope"><div class="table-index">{{ scope.$index < form.sysPaymentList.length ? scope.$index + 1 : '' }}</div></template>
					</el-table-column>
					<el-table-column label="支付渠道" 	prop="payment_channel" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ {TAOBAO:'淘宝', B2C:'B2C商城', EMERGING_PROJECT:'新兴项目', JOINED:'加盟',O2O:'O2O体验店'}[scope.row.payment_channel] || scope.row.payment_channel }}
						</template>
					</el-table-column>
					<el-table-column label="支付明细类型" 	prop="type" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ {RECEIVE:'收入', REFUND:'退款', CARRYOVERS:'结转', RETURNS: '退货货款', COMPENSATION: '赔偿'}[scope.row.type] || scope.row.type }}
						</template>
					</el-table-column>
					<el-table-column label="支付方式" 	prop="pay_type" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ pay_type_options[scope.row.pay_type] || scope.row.pay_type }}
						</template>
					</el-table-column>
					<el-table-column label="收款账号" 	prop="received_account" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="收款金额" 	prop="pay_amount" width="110" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.sysPaymentList.length ? ((/^(REFUND|RETURNS|COMPENSATION)$/.test(scope.row.type) ? -1 : 1) * scope.row.pay_amount) : ('合计' + _calcPayAmount()) }}
						</template>
					</el-table-column>
					<el-table-column label="支付账号"   prop="pay_account" show-overflow-tooltip></el-table-column>
					<el-table-column label="开户行" 	prop="pay_bank" show-overflow-tooltip></el-table-column>
					<el-table-column label="用户名" 	prop="pay_name" show-overflow-tooltip></el-table-column>
					<el-table-column label="销售订单号" prop="sys_trade_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="支付时间" 	prop="payment_time" width="180" show-overflow-tooltip>
						<template slot-scope="scope">
							<span>{{scope.row.payment_time | dataFormat1}}</span>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</el-tab-pane>
		<el-tab-pane label='对账明细' name='balanceDetail' class='xpt-flex'>
			<div class="xpt-flex__bottom scroll">
				<el-table :data="form.accountCheckList.length ? form.accountCheckList.concat('') : form.accountCheckList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column label="支付余额" 	prop="pay_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="总货值" 	prop="total_goods_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="已财审货值" prop="audit_goods_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="已财审运费"	prop="audit_carriage_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="未财审货值" prop="dis_audit_goods_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="未财审运费" prop="dis_audit_carriage_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="运费优惠" 	prop="discount_carriage_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="加收运费" 	prop="extra_carriage_fee" show-overflow-tooltip></el-table-column>
					<el-table-column label="退款金额"   prop="refund_amount" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.accountCheckList.length ? scope.row.refund_amount : ('合计' + form.accountCheckList.reduce((a, b) => a + (Number(b.refund_amount) || 0), 0)) }}
						</template>
					</el-table-column>
					<el-table-column label="对账余额" 	prop="state_rest_amount" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.accountCheckList.length ? scope.row.state_rest_amount : ('合计' + form.accountCheckList.reduce((a, b) => a + (Number(b.state_rest_amount) || 0), 0)) }}
						</template>
					</el-table-column>
					<el-table-column label="结余" 		prop="rest_amount" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.accountCheckList.length ? scope.row.rest_amount : ('合计' + form.accountCheckList.reduce((a, b) => a + (Number(b.rest_amount) || 0), 0)) }}
						</template>
					</el-table-column>
					<el-table-column label="合并单号" 	prop="merge_trade_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="总应收运费" prop="carriage_amount" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.accountCheckList.length ? scope.row.carriage_amount : ('合计' + form.accountCheckList.reduce((a, b) => a + (Number(b.carriage_amount) || 0), 0)) }}
						</template>
					</el-table-column>
          <el-table-column label="总应收服务费" prop="totalServiceFeesReceivable" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.accountCheckList.length ? scope.row.totalServiceFeesReceivable : ('合计' + form.accountCheckList.reduce((a, b) => a + (Number(b.totalServiceFeesReceivable) || 0), 0)) }}
						</template>
					</el-table-column>
          <el-table-column label="已财审服务费" prop="totalVerifiedServiceFee" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.accountCheckList.length ? scope.row.totalVerifiedServiceFee : ('合计' + form.accountCheckList.reduce((a, b) => a + (Number(b.totalVerifiedServiceFee) || 0), 0)) }}
						</template>
					</el-table-column>
				</el-table>
			</div>
		</el-tab-pane>
		<el-tab-pane label='差价商品' name='chajiaDetail' class='xpt-flex'>
			<div class="xpt-flex__bottom scroll">
				<el-table
					:data="form.priceDiffList.length ? form.priceDiffList.concat('') : form.priceDiffList"
					border
					tooltip-effect="dark"
					style="width: 100%;"
					width='100%'
				>
					<el-table-column label="序号" type="index" width="70">
						<template slot-scope="scope"><div class="table-index">{{ scope.$index < form.priceDiffList.length ? scope.$index + 1 : '' }}</div></template>
					</el-table-column>
					<el-table-column label="价格区间" 	  	prop="price_area" show-overflow-tooltip></el-table-column>
					<el-table-column label="店铺" 			prop="shop" width="130" show-overflow-tooltip></el-table-column>
					<el-table-column label="商品名称" 		prop="old_goods_name" show-overflow-tooltip></el-table-column>
					<el-table-column label="商品编码" 		prop="old_goods_code" width="130" show-overflow-tooltip></el-table-column>
					<el-table-column label="规格描述"		prop="old_specification" width="180" show-overflow-tooltip></el-table-column>
					<el-table-column label="拍下价格" 		prop="old_price" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.priceDiffList.length ? scope.row.old_price : ('合计' + form.priceDiffList.reduce((a, b) => a + (Number(b.old_price) || 0), 0)) }}
						</template>
					</el-table-column>
					<el-table-column label="活动价" 		prop="new_price" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.priceDiffList.length ? scope.row.new_price : ('合计' + form.priceDiffList.reduce((a, b) => a + (Number(b.new_price) || 0), 0)) }}
						</template>
					</el-table-column>
					<el-table-column label="差价" 			prop="diff_price" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ scope.$index < form.priceDiffList.length ? scope.row.diff_price : ('合计' + form.priceDiffList.reduce((a, b) => a + (Number(b.diff_price) || 0), 0)) }}
						</template>
					</el-table-column>
					<el-table-column label="购买时间" 		prop="buy_time" width="180" show-overflow-tooltip>
						<template slot-scope="scope">
							<span>{{scope.row.buy_time | dataFormat1}}</span>
						</template>
					</el-table-column>
					<el-table-column label="生效日期" 		prop="valid_date" width="180" show-overflow-tooltip>
						<template slot-scope="scope">
							<span>{{scope.row.valid_date | dataFormat1}}</span>
						</template>
					</el-table-column>
					<el-table-column label="失效日期" 		prop="disabled_date" width="180" show-overflow-tooltip>
						<template slot-scope="scope">
							<span>{{scope.row.disabled_date | dataFormat1}}</span>
						</template>
					</el-table-column>
					<el-table-column label="订单状态" 		prop="order_status" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ order_status_options[scope.row.order_status] }}
						</template>
					</el-table-column>
					<el-table-column label="淘宝单号" 		prop="order_no" width="150" show-overflow-tooltip></el-table-column>
					<el-table-column label="活动规格描述" 	prop="new_specification" width="180" show-overflow-tooltip></el-table-column>
					<el-table-column label="活动商品编码" 	prop="new_goods_code" show-overflow-tooltip></el-table-column>
					<el-table-column label="活动商品名称"   prop="new_goods_name" show-overflow-tooltip></el-table-column>
					<el-table-column label="单位" 			prop="units" show-overflow-tooltip></el-table-column>
				</el-table>
			</div>
		</el-tab-pane>
		<el-tab-pane label='操作记录' name='chaozuoDetail' class='xpt-flex'>
			<div class="xpt-flex__bottom scroll">
				<el-table :data="form.operateLogList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column label="序号" type="index" width="70">
						<template slot-scope="scope"><div class="table-index">{{ scope.$index + 1 }}</div></template>
					</el-table-column>
					<el-table-column label="用户" 		prop="operator_name" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="业务操作" 	prop="operate_type" width="100" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ operate_type_options[scope.row.operate_type] || scope.row.operate_type }}
						</template>
					</el-table-column>
					<el-table-column label="操作描述" 	prop="description" width="400" show-overflow-tooltip></el-table-column>
					<el-table-column label="操作时间"   prop="operate_time" width="300" show-overflow-tooltip>
						<template slot-scope="scope">
							<span>{{scope.row.operate_time | dataFormat1}}</span>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</el-tab-pane>
		<el-tab-pane label='接口信息' name='apiDetail' class='xpt-flex'>
			<div class="xpt-flex__bottom scroll">
				<el-table :data="apiList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column label="下游单据类型" prop="push_bill_type" width="150" show-overflow-tooltip></el-table-column>
					<el-table-column label="单据编号" 	prop="interface_respone" show-overflow-tooltip>
						<template slot-scope="scope">{{ getInterfaceResponeNumber(scope.row.interface_respone) }}</template>
					</el-table-column>
					<el-table-column label="方式" 	prop="business_type" width="70" show-overflow-tooltip></el-table-column>
					<el-table-column label="接口状态"   prop="interface_status" width="100" show-overflow-tooltip>
						<template slot-scope="scope">{{ interface_status_options[scope.row.interface_status] }}</template>
					</el-table-column>
					<el-table-column label="接口提示信息" 	prop="interface_respone" show-overflow-tooltip></el-table-column>
					<el-table-column label="生成时间"   prop="create_time" width="180" show-overflow-tooltip>
						<template slot-scope="scope">
							<span>{{scope.row.create_time | dataFormat1}}</span>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</el-tab-pane>
	</el-tabs>
	</el-row>
</div>
</template>
<script>
import VL from '@common/validate.js'
import fn from '@common/Fn.js'

export default {
	props: ['params'],
	data (){
		return {
			ifClickUpload: false,
			isRefoudDetailFormPlan: false,//用于退款信息删除时判断从方案带出来的退款明细不能删除
			uploadData: {},
			selectTab1: 'basicInformation1',
			selectTab2: 'refoudDetail',
			taobaoListSelect: [],//淘宝申请退款信息
			payInfoListSelect: [],//退款信息表格
			apiList: [],//接口信息表格
			isEdit: false,//编辑权限
			isSamePersonCanEdit: false,//用于按钮控制
			btnGroup: [{
				type:'success',
				txt:'审批通过',
				click: () => {
					this.ajaxFunc('pass?permissionCode=REFUND_ORDER_APPROVE', res => {
						this.getOrderDetail()
					})
				},
			}, {
				type:'warning',
				txt:'审批不通过',
				click: () => {
					this.ajaxFunc('notPass?permissionCode=REFUND_ORDER_APPROVE', res => {
						this.getOrderDetail()
					})
				},
			}],


			currency_options: Object.assign(fn.getCurrency(), { RMB: '人民币' }),//币别选项
			cs_status_options: {//小二介入
				1: '不需客服介入',
				2: '需要客服介入',
				3: '客服已经介入',
				4: '客服初审完成',
				5: '客服主管复审失败',
				6: '客服处理完成',
			},
			refund_way_options: {
				ALIPAY	: '支付宝退款',
				BANK	: '银行卡退款',
				PROTOCOL: '协议退款',
				DISPUTE : '纠纷退款',
				PAIPAI	: '拍拍退款',
				TENPAY	: '财付通退款',
				BAIL	: '保证金退款',
				B2C_ONLINE	: 'B2C线上退款',
				B2C_OFFLINE	: 'B2C线下退款',
				CARRY_OVER	: '退款结转',
        YSF: '云闪付退款'
			},
			refund_type_options: {
				CANCEL: '未发取消',
				OVERPAY: '多付',
				PREFERENTIAL: '优惠/返现',
				DELAY: '延迟赔付',
				COMPENSATE: '补偿',
				COMPENSATION: '补偿',
				REPAIR: '维修费',
				RETURNS: '退货货款',
				CARRIAGE: '运费',
				DELIVERY_FEE: '运费',
				THREE: '三包费',
				THREE_FEE: '三包费',
				O2O_DIFF: 'O2O跨店铺差价',
				O2O_PRICE_DIFF: 'O2O跨店铺差价',
				PRICE_DIFF: '差价',
				DISCOUNT: '外购折现',
				DISCOUNTING: '外购折现',
			},
			taobao_refund_status_options: {
				WAIT_SELLER_AGREE: '买家已经申请退款，等待卖家同意',
				WAIT_BUYER_RETURN_GOODS: '卖家已经同意退款，等待买家退货',
				WAIT_SELLER_CONFIRM_GOODS: '买家已经退货，等待卖家确认收货',
				SELLER_REFUSE_BUYER: '卖家拒绝退款',
				CLOSED: '退款关闭',
				SUCCESS: '退款成功',
			},
			pay_type_options: {
				ALIPAY:'支付宝',
	            WECHAT:'微信',
	            BANK_TRANSFER:'银行转账',
	            POS:'POS刷卡',
	            CASH:'现金',
	            CARRYOVERS: '结转',
	            REFUNDMENT: '退款',
				CAIFUTONG: '财付通',
				QQ: 'QQ',
				BANK: '银行',
				CARRYOVERS: '结转',
			},
			order_status_options: {
				'CANCEL': '取消',
				'NORMAL': '生效',
				'WAITING': '等待发运',
				'PART_DELIVERED': '部分发运中',
				'DELIVERED': '已发运'
			},
			operate_type_options: {
				CREATE: '新增',
				SAVE: '保存',
				LOCK: '锁定',
				UNLOCK: '解锁',
				SUBMIT: '提交',
				RETRACT: '撤回',
				AUDIT: '审核',
				CHANGE: '确认变更',
				REVERSE_AUDIT: '反审核',
				SUBMIT_APPROVE: '提交审批',
				PASS: '审批通过',
				NOT_PASS: '审批不通过',
				TRACK: '执行跟踪',
				RECALL: '撤回仓储',
				CLOSE: '关闭',
				OPEN: '反关闭',
				CANCEL: '已取消',
				//extra
				ADD_REFUND_ITEM: '引入退款明细',
				DELETE_REFUND_ITEM: '删除退款明细',
				ADD_TAOBAO_REFUND: '引入淘宝退款申请',
				DELETE_TAOBAO_REFUND: '删除淘宝退款申请',
				SUBMITPURCHASE: '提交采购',
				PLAN_RETRACT: '方案撤回',
				REJECT: '驳回',
				LOCKER: '锁定',
				REVOCATION: '撤回',
				VERIFY: '审核',
				OPPOSITE_VERIFY: '反审核',
				SUBMIT_EXAMINE: '提交审批',
				PASS_EXAMINE: '审批通过',
				UNPASS_EXAMINE: '审批不通过',
			},
			// 接口信息-接口状态
			interface_status_options: {
				'-1' : '处理失败',
				'1' : '处理成功',
				'0': '处理中',
				'2' : '已取消',
			},

			form: {
				currency: 'CNY',
				date: new Date,
				create_time: new Date,
				payInfoList: [],
				taobaoList: [],
				itemList: [],
				priceDiffList: [],
				sysPaymentList: [],
				accountCheckList: [],
				total_amount: 0,//退款信息 应退核实金额汇总
				refund_amount: 0,//退款明细	实退金额汇总
				if_priority: 0,
				if_history: 0,
				status: 'CREATE',
				refund_status: 'WAIT_REFUND',
				dealer_customer_id:null,//经销商Id
				if_dealer:null,//是否经销商订单(Y是/N否)
				dealer_customer_number:null,//经销商编码
				dealer_customer_name:null//经销商名称
			},


		}
	},
	methods: {
		// 上一页 or 下一页
		nextOrPrevOrder (type){
			this.params.idList.some((id, index) => {
				if(id === this.params.id){
					var newOrderId = this.params.idList[index + (type === 'next' ? 1 : -1)]

					if(newOrderId){
						this.selectTab2 = 'refoudDetail'//防止在接口信息页签下点击翻页导致接口信息数据没变化
						this.params.id = newOrderId
						this.getOrderDetail(null, true)
					}else {
						this.$message.error('没有更多')
					}
					return true
				}
			})
		},
		// 淘宝申请退款信息页签的淘宝退款单号跳转详情
		toTaobaoRefund (taobao_refund_no){
			var searchFunc = () => {
				this.toTaobaoRefund.where[0].value = taobao_refund_no
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/list?permissionCode=TAOBAO_REFUND_ORDER_QUERY', {
					"page_name": "aftersale_taobao_refund_download",
					"page_size": 50,
					"page_no": 1,
					"where": this.toTaobaoRefund.where,
				}, res => {
					if(res.body.result){
						if(res.body.content.list && res.body.content.list[0]){
							this.$root.eventHandle.$emit('creatTab', {
								name: '退款申请单淘宝下载查看',
								params: { after_refund_download_id: res.body.content.list[0].after_refund_download_id },
								component: ()=> import('@components/after_sales_refund/taobaoDownloadDetail.vue')
							})
						}else {
							this.$message.error('找不到该退款申请单淘宝下载详情')
						}
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}

			if(this.toTaobaoRefund.where){
				searchFunc()
			}else {
				this.ajax.postStream('/user-web/api/sql/listFields', { 'page': 'aftersale_taobao_refund_download' }, res => {
					res.body.content.fields.some(obj => {
						if(obj.comment === '淘宝退款单号'){
							this.toTaobaoRefund.where = [{
								condition: 'AND',
								field: obj.field,
								listWhere: [],
								operator: '=',
								table: obj.table,
								value: '',
							}]
							searchFunc()
							return true
						}
					})
				})
			}
		},
		// 支出，整行标红显示
		paymentListClassName (row, index){
			if(/^(REFUND|RETURNS|COMPENSATION)$/.test(row.type)) {
				return this.$style['mergered']
			}
		},
		_calcPayAmount (){
			/*{{ {RECEIVE:'收入', REFUND:'退款', CARRYOVERS:'结转', RETURNS: '退货货款', COMPENSATION: '赔偿'}[scope.row.type] }}*/
			return this.form.sysPaymentList.reduce((a, b) => {
				if(b.type === 'RECEIVE' || b.type == 'CARRYOVERS'){
					a += (Number(b.pay_amount) || 0)
				}else if (/^(REFUND|RETURNS)$/.test(b.type)){
					a -= (Number(b.pay_amount) || 0)
				}

				return a
			}, 0).toFixed(2)
		},
		/**
		*合计
		**/
		// getSummaries(param){
		// 	const { columns, data } = param;
	 //        const sums = ['总计'];
	 //        console.log('columns',columns);
	 //        console.log('data',data);
	 //        /*{{ {RECEIVE:'收入', REFUND:'退款', CARRYOVERS:'结转', RETURNS: '退货货款', COMPENSATION: '赔偿'}[scope.row.type] }}*/
	 //        let total = 0;
	 //        data.map((a,b)=>{
	 //        	console.log('a',a);
	 //        	let type = a.type;
	 //        	let money = Number(a.pay_amount);
	 //        	if(type == 'RECEIVE' || type == 'REFUND' || type == 'RETURNS'){
	 //        		type == 'RECEIVE'?total+=money:total-=money;
	 //        	}
	 //        })


	 //        columns.forEach((column, index) => {

	 //          if(column.property != 'pay_amount') return;
	 //          sums[index] = total

	 //        });

	 //        return sums;
		// },

		//对页面需要认证的内容认证，required：是否必填，msg：提示信息
		VLFun (required, msg){
			return VL.isNotBlank({
				required:required,
				self:this,
				msg:msg,
			})
		},
		// 接口信息转义
		getInterfaceResponeNumber (str){
			return str && JSON.parse(str).Result.Number
		},
		/**
		*设置是否是同一个人
		***/

		setIsSamePersonCanEdit(){
			this.isSamePersonCanEdit = this.form.finance_locker === this.getEmployeeInfo('id');
		},
		// 获取单据详情
		getOrderDetail (cb, isShowMsg){
			this.ajaxFunc('getRefundBill?permissionCode=REFUND_ORDER_QUERY', (res) => {
				res.sysPaymentList && res.sysPaymentList.sort((a, b) => a.payment_id - b.payment_id)

				this._closeTabOrRefreshCheck(null, this.form = res)//先保存对比值，用于this.compareData时对比
				//财务锁定人 = 操作人 才能编辑
				//不能放在isEdit判断里，由于有些按钮form.status不是'CREATE'
				//this.isSamePersonCanEdit = this.form.finance_locker === this.getEmployeeInfo('id')
				this.setIsSamePersonCanEdit();

				if(!this.payInfoListSelect.length && this.form.payInfoList.length){
					setTimeout(() => {//先让this.form渲染完再执行
						this.$refs.$payInfoList.toggleRowSelection(this.form.payInfoList[0], true)
					})
				}

				this.isEdit =
					/(CREATE|REAUDIT)/.test(this.form.status)
					&& this.form.business_status === 'FINANCIAL_PROCESSING'//业务状态 = 财务处理中 才能编辑
					&& this.isSamePersonCanEdit


				// !this.isEdit ? setTimeout(() => _disabledBool(!this.isEdit)) : _disabledBool(!this.isEdit)

				// function _disabledBool (bool){
				// 	// 非编辑状态下禁用所有btn
				// 	;[].forEach.call(self.$refs.selectTab1Info.$el.querySelectorAll('.el-input, .el-textarea, .el-checkbox__input'), $dom => {
				// 		$dom.classList[bool ? 'add' : 'remove']('is-disabled')
				// 		$dom.querySelector('input, textarea').disabled = bool
				// 	})
				// }
				cb && cb()
			}, null, !isShowMsg)
		},
		selectTab2Click (){
			if(this.selectTab2 === 'refoudDetail'){
				this.payInfoListSelectHandler()
			}else if (this.selectTab2 === 'apiDetail'){
				this.getApiDetail()
			}
		},
		getApiDetail (){
			if(this.form.bill_no){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryExtInterfaceByBillNo', {
					bill_no: this.form.bill_no,
					bill_type: 'REFUND',
				}, res => {
					var data = res.body
					if(!data.result) {
						this.$message.error(data.msg)
						return
					}
					this.apiList = data.content || []
				})
			}
		},
		// 跳转到退款申请单详情
		toRefundRequest (id){
			this.$root.eventHandle.$emit('creatTab',{
				name: '退款申请单',
				params: { id },
				component:()=> import('@components/after_sales_refund/refundRequest_2')
			})
		},
		// 提交按钮
		submit (){
			this._closeTabOrRefreshCheck(null, null, () => {
				this.ajaxFunc('submit?permissionCode=REFUND_ORDER_SUBMIT', this.form, res => {
					this.getOrderDetail()
				})
			})
		},
		// 审核按钮
		audit (){
			this.ajaxFunc('audit?permissionCode=REFUND_ORDER_AUDIT', res => {
				this.getOrderDetail()
			})
		},
		// 反审核按钮
		reverseAudit (){
			this.ajaxFunc('reverseAudit?permissionCode=REFUND_ORDER_AUDIT_BACK', res => {
				this.getOrderDetail()
			})
		},
		// 提交审批按钮
		submitApprove (){
			this._closeTabOrRefreshCheck(null, null, () => {
				this.ajaxFunc('submitApprove?permissionCode=REFUND_ORDER_SUBMIT_APPROVE', this.form, res => {
					this.getOrderDetail()
				})
			})
		},
		// 撤回按钮
		revoke (){
			this.ajaxFunc('revoke?permissionCode=REFUND_ORDER_WITHDRAW', res => {
				this.getOrderDetail()
			})
		},
		// 锁定 or 解锁按钮
		lockOrUnlock (apiName){
			this.ajaxFunc(apiName, res => {
				this.getOrderDetail()
			})
		},
		ajaxFunc (apiName, postData, cb, isNotShowMsg){
			if(!postData || Object.prototype.toString.call(postData) === '[object Function]'){
				cb = postData
				postData = { id: this.params.id }
			}

			if(this.ajaxFunc.disabled) return
			this.ajaxFunc.disabled = true
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refund/' + apiName, postData, res => {
				this.ajaxFunc.disabled = false
				if(res.body.result){
					cb && cb(res.body.content)
					!isNotShowMsg && this.$message.success(res.body.msg)
				}else {
					this.$message.error(res.body.msg)
				}
			}, () => {
				this.ajaxFunc.disabled = false
			})
		},
		// 买家昵称 or 客户
		selectBuyersName (obj, key){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/customers/list'),
				style:'width:900px;height:600px',
				title: (key === 'nick_name' ? '买家昵称' : '客户') + '列表',
				params: {
					close: d => {
						this.$set(obj, key, d.name)
					}
				},
			})
		},
		// 业务员选择
		selectBestStaff (){
			this.$root.eventHandle.$emit('alert',{
				title: '业务员列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/after_sales_common/selectPickrecommendhandler'),
				params: {
					callback: d => {
						this.form.saleman_id = d.id
						this.form.saleman_group = d.group_name
						this.$set(this.form, 'saleman', d._real_name_nick_name)
					}
				},
			})
		},
		// 订单店铺选择
		selectShop (row, id, val){
			this.$root.eventHandle.$emit('alert',{
				title: '店铺列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/shop/list.vue'),
				params: {
					selection: 'radio',
					callback: d => {
						this.$set(row, val, d.shop_name)
						row[id] = d.shop_id
					}
				},
			})
		},
		// 退款信息-新增行
		// addPayforInfo (){
		// 	var obj = {}
		// 	this.$set(obj, 'refund_way', '')
		// 	this.$set(obj, 'pay_type', '')

		// 	this.form.payInfoList.push(obj)
		// },
		// 表格删除按钮
		delSelect (thisName, formName){
			console.log('看看删除');
			var _successCb = () => {
				this[thisName].forEach(obj => {
					this.form[formName].splice(this.form[formName].indexOf(obj), 1)
				})
				this[thisName].length = 0
			}
			,	_delIds = this[thisName].map(obj => obj.id)

			if (thisName === 'payInfoListSelect'){
				if(this.isRefoudDetailFormPlan) {
					this.$message.error('由方案带出退款明细不能删除')
				}else {
					this.ajaxFunc('deletePayInfo', _delIds, res => {
						this.getOrderDetail();

					})
				}
			}else if (thisName === 'taobaoListSelect'){
				this.ajaxFunc('deleteTaobaoRefund', _delIds, res => {
					_successCb();
					this.getOrderDetail();
				})
			}
		},
		_checkSaveBefore (){
			var msg
			,	tips = {
				refund_way: '退款方式',
				pay_type: '支付类型',
				accouont: '账号',
				check_amount: '应退核实金额',
				nick_name: '买家昵称',
				customer: '客户',
				shop_name: '订单店铺',
				user_shop_name: '收入店铺',
				original_shop_name: '原始店铺',
				user_name: '姓名',
				phone: '电话',
			}

			this.form.payInfoList.some((obj, index) => {
				return Object.keys(tips).some(tip => {
					if(!obj[tip]) {
						msg = '退款信息页签-第' + (index + 1) + '行-' + tips[tip] + '不能为空'
						return true
					}
				})
			})

			if(msg) {
				this.$message.error(msg)
				throw(msg)
			}
		},
		// 驳回按钮
		reject (){
			if(!this.payInfoListSelect.length || !this.payInfoListSelect.every(obj => obj.reject_remark)){
				this.$message.error('必须选中退款信息对应的退款申请单，且“驳回备注”必填。')
				return
			}

			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refund/reject?permissionCode=REFUND_ORDER_ORDER', {
				id: this.form.id,
				payInfoList: this.payInfoListSelect.map(obj => ({
					id: obj.refund_apply_bill_id,
					reject_remark: obj.reject_remark,
				})),
			}, res => {
				if(res.body.result){
					this.getOrderDetail()
					this.$message.success(res.body.msg)
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		// 保存按钮
		save (e, cb){
			var $btn = e ? e.currentTarget : { disabled: '' }

			// this._checkSaveBefore()

			$btn.disabled = true
			console.log('save-btn', this.form)
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refund/save?permissionCode=REFUND_ORDER_SAVE', this.form, res => {
				$btn.disabled = false
				if(res.body.result){
					if(cb){
						cb()
					}else {
						this.$message.success(res.body.msg)
						this.getOrderDetail(() => {
							$btn.disabled = false
						})
					}
				}else {
					$btn.disabled = false
					this.$message.error(res.body.msg)
				}
			}, () => {
				$btn.disabled = false
			})
		},
		// 退款明细-----引入按钮
		itemListImport (){
			this.ajaxFunc('addItem', res => {
				console.log('看看是啥东西');
				this.form.itemList = res.itemList;
				this.form.payInfoList = res.payInfoList;
				this.getOrderDetail();
				//this._calcOneTotal('refund_amount', 'itemList', 'actual_amount')
			})
		},

		// 计算退款明细	实退金额汇总 + 退款信息 应退核实金额汇总
		_calcOneTotal (formName, formListName, keyName){
			this.form[formName] = this.form[formListName].reduce((count, obj) => count + Number(obj[keyName]), 0)
		},

		// 淘宝申请退款信息----引入按钮
		introduceFun() {
			// this.form.taobaoList.push({
		 //      "id": 900,
		 //      "refund_bill_id": ****************,
		 //      "tb_order_type": "ZFB_REFUND",
		 //      "nick_name": "wqe13",
		 //      "apple_time": *************,
		 //      "refund_way": null,
		 //      "taobao_refund_no": "TKSQ1709010002",
		 //      "taobao_shop_name": "林氏天猫",
		 //      "alipay_account": "skdiwjid",
		 //      "recommend_handler": 111,
		 //      "recommend_handler_name": "张",
		 //      "refund_amount": 1000.000,
		 //      "refund_reason": "不想买了",
		 //      "overtime": "",
		 //      "taobao_interpose": null,
		 //      "cs_interpose_time": null,
		 //      "taobao_order_no": "2222222",
		 //      "taobao_refund_status": "STATUS",
		 //      "create_time": *************,
		 //      "last_modify_time": *************
		 //    })

			this.ajaxFunc('addTaobaoRefund', res => {
				this.form.taobaoList = res.taobaoList
			})
		},
		// 当退款单没有售后单时，附件的上传和查看根据申请单找回售后单或申请单据号
		findAfterOrderNo (cb){
			var _searchFunc = () => {
				var source_order_no

				if(!this.payInfoListSelect.length){
					this.$message.error('请选择一行退款信息明细')
					return
				}

				this.form.itemList.some(obj2 => {
					if(obj2.refund_apply_bill_id === this.payInfoListSelect[0].refund_apply_bill_id){
						source_order_no = obj2.source_order_no
						return true
					}
				})

				if(this.findAfterOrderNo.markObj && this.findAfterOrderNo.markObj[source_order_no]){
					cb(
						this.findAfterOrderNo.markObj[source_order_no].no,
						this.findAfterOrderNo.markObj[source_order_no].type
					)
				}else {
					this.findAfterOrderNo.where[0].value = source_order_no
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApply/findAfterApplySaleList?permissionCode=REFUND_APPLY_ORDER_QUERY', {
						"page_name": "aftersale_bill_refund_apply",
						"pageSize": 50,
						"pageNo": 1,
						"where": this.findAfterOrderNo.where,
					}, res => {
						var data
						if(res.body.result){
							data = res.body.content.list[0]

							if(!this.findAfterOrderNo.markObj) this.findAfterOrderNo.markObj = {}
							this.findAfterOrderNo.markObj[source_order_no] = {
								no: data.after_order_no || data.bill_no,
								type: data.after_order_no ? 'after_order_no' : 'bill_no',
							}

							cb(data.after_order_no || data.bill_no, data.after_order_no ? 'after_order_no' : 'bill_no')
						}
					})
				}
			}

			if(this.form.after_order_no){
				cb(this.form.after_order_no, 'after_order_no')
			}else {
				if(this.findAfterOrderNo.where){
					_searchFunc()
				}else {
					this.ajax.postStream('/user-web/api/sql/listFields', { page: 'aftersale_bill_refund_apply' }, res => {
						if(res.body.result){
							res.body.content.fields.some(obj => {
								if(obj.comment === '单据编号'){
									this.findAfterOrderNo.where = [{
										condition: 'AND',
										field: obj.field,
										listWhere: [],
										operator: '=',
										table: obj.table,
										value: '',
									}]
									_searchFunc()
									return true
								}
							})
						}
					})
				}

			}
		},
		// 附件操作
		uploadAction (method){
			if(method === 'post'){
				this.findAfterOrderNo(parent_no => {
					this.ifClickUpload = true
					this.uploadData = {
						parent_name: 'AFTER_ORDER',
						parent_no,
						child_name : 'REFUNDBILLDETAIL',
						child_no : this.form.bill_no,
						content: JSON.parse(JSON.stringify(this.form || {})),
					}
					setTimeout(() => {
						this.ifClickUpload = false
					}, 100)
				})
			}else {
				this.findAfterOrderNo((parent_no, type) => {
					this.$root.eventHandle.$emit('alert', {
						params: {
							parent_name : 'AFTER_ORDER',
							parent_name_txt: type === 'after_order_no' ? '售后单号' : '退款申请单据号',
							parent_no,
							child_name : 'REFUNDBILLDETAIL',
							child_no : null,
							// content: JSON.parse(JSON.stringify(this.form || {})),
							// child_name_txt: '商品问题id',
							ext_data : null,
							// callback: files => {
							// 	this.listAfterMaterialVO[index].attachment_count = files.length
							// },
						},
						component: ()=>import('@components/common/download.vue'),
						style: 'width:80%;height:600px',
						title: '下载列表',
					})
				})
			}
		},
		payInfoListSelectHandler (s){
			var refund_apply_bill_id_array = (s ? this.payInfoListSelect = s : this.payInfoListSelect).map(obj => obj.refund_apply_bill_id)

			this.$refs.$refoudDetail.clearSelection()
			this.isRefoudDetailFormPlan = false
			setTimeout(() => {//退款明细页签tab-click切换时需要setTimeout延时
				this.form.itemList.forEach(obj2 => {
					if(refund_apply_bill_id_array.indexOf(obj2.refund_apply_bill_id) !== -1){
						if(obj2.source_type === 'REFUND_PLAN') this.isRefoudDetailFormPlan = true
						this.$refs.$refoudDetail.toggleRowSelection(obj2, true)
					}
				})
			})
		},
		// 关闭页签 or 刷新判断
		_closeTabOrRefreshCheck: function (){
			var _oldData
			return function (e, setOldData, cb){
				var self = this
				if(setOldData){
					_oldData = JSON.parse(JSON.stringify(setOldData))
				}else {
					var newData = JSON.parse(JSON.stringify(this.form))

					//过滤退款明细和退款信息对比
					delete newData.itemList
					delete _oldData.itemList
					delete newData.payInfoList
					delete _oldData.payInfoList

					//var isDiff = this.compareData(newData, _oldData)
					var isDiff = !this.isSamePersonCanEdit?false:this.compareData(newData, _oldData);

					if(cb) {
						if(isDiff){//用于提交按钮，当对比不相同，先保存后提交
							this.save(null, cb)
						}else {
							cb()
						}
						return
					}

					if(isDiff){
						this.$root.eventHandle.$emit('openDialog', {
							ok: e => {
								this.save(e)
							},
							no: _closeTab
						})
					}else {
						_closeTab()
					}
				}

				function _closeTab (){
					if(e){//刷新按钮
						self.getOrderDetail(null, true)
					}else {//关闭页签
						self.$root.eventHandle.$emit('removeTab', self.params.tabName)
					}
				}
			}
		}(),
		// 退款信息行点击总是选中一行
		payInfoListRowClick (obj, event, column){
			if(column.label){
				this.$refs.$payInfoList.clearSelection()
				this.$refs.$payInfoList.toggleRowSelection(obj)
			}
		},
	},
	mounted (){
		this._closeTabOrRefreshCheck(null, this.form)
		this.getOrderDetail(null, true)
		this.params.__close = this._closeTabOrRefreshCheck
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
			this.setIsSamePersonCanEdit();
		});
	},
}
</script>

<style module>
.maxHeight :global(.xpt_pmm_scroll) :global(.el-table__body-wrapper){
	max-height: 300px;
}
.textarea-style :global(.el-form-item__content) {
	width: 180px;
    height: auto;
}
.table-input-w100 :global(.el-select), .table-input-w100 :global(.el-input) {
	width: 100%;
}
.mergered, .mergered td{
	color: red!important;
}
</style>
