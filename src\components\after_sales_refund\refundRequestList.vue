<!-- 退款资料请求列表 -->
<template>
	<div class="xpt-flex">
		<xpt-list-dynamic
			:data="roleList"
			:colData="ColData"
			:pageTotal="pageTotal"
			:btns="[]"
			:orderNo="true"
			:searchPage="form.page_name"
			@search-click="search"
			@page-size-change="pageChange"
			@current-page-change="currentPageChange"
			ref="xptList"
		>
		</xpt-list-dynamic>
	</div>
</template>
<script>
export default {
	props: ["params"],
	data() {
		let self = this;
		return {
			ifClickUpload: false,

			financeLockBtnCheck: true,
			financeUNLockBtnCheck: true,
			checkBtnChech: true,
			confirmRefundBtnChech: true,

			uploadData: {},
			form: {
				page_name: "aftersale_bill_refund_info_request",
				page_size: 50,
				page_no: 1,
				where: []
			},
			
			pageTotal: 0,
			roleList: [],
			selectOrder: [],
			listType: "saleman_person", //默认业务专用 saleman_person or finance_person 财务专用
			ColData: [
				
				{
					label: "类型",
					prop: "refund_type",
					formatter: prop =>
                    ({
                        CANCEL: '未发取消',
                        OVERPAY: '多付',
                        PREFERENTIAL: '未前置优惠',
                        SINGLE_DISCOUNT: '单品优惠',
                        DELAY: '服务折让',
                        COMPENSATION: '销售折让',
                        REPAIR: '维修费',
                        RETURNS: '退货货款',
                        CARRIAGE: '运费',
                        THREE: '三包费',
                        O2O_DIFF: 'O2O跨店铺差价',
                        PRICE_DIFF: '差价',
                        DISCOUNT: '外购折现',
                        CROSS_OVER: '多付',
                        OTHER_REFUND: '其它退款',
                        PLATFORM_DISCOUNT: '平台优惠',
                        OVER_DISCOUNT: '多付-优惠前置',
                        OVER_FUND: '多付-货款'
                    }[prop] || prop),
					width: 100
				},{
					label: "资料请求状态",
					prop: "info_status",
					formatter: prop => ({
						UNASKED: '未允许请求',
						ALLOWASKED: '允许请求',
						ASKED: '已请求',
						CANCEL: '已作废',
					}[prop] || prop),
					width: 100
				},{
					label: "退库单号",
					prop: "bill_no",
					width: 100
				},{
					label: "买家昵称",
					prop: "nick_name",
					width: 100
				},{
					label: "是否财务确认",
					prop: "finance_confirm",
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
					width: 100
				},{
					label: "财务确认时间",
					prop: "finance_confirm_time",
					format: "dataFormat1",
					width: 100
				},{
					label: "财务确认人",
					prop: "finance_confirm_name",
					width: 100
				},{
					label: "是否业务确认",
					prop: "business_confirm",
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
					width: 100
				},{
					label: "客服确认人",
					prop: "ender_name",
					width: 100
				},{
					label: "客服确认时间",
					prop: "end_time",
					format: "dataFormat1",
					width: 100
				},{
					label: "是否超时",
					prop: "if_over_time",
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
					width: 100
				},{
					label: "创建人",
					prop: "creater_name",
					width: 100
				},{
					label: "创建时间",
					format: "dataFormat1",
					prop: "creater_time",
					width: 100
				},{
					label: "经销名称",
					prop: "dealer_customer_name",
					width: 100
				},{
					label: "是否经销",
					prop: "if_dealer",
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
					width: 100
				},{
					label: "业务锁定人",
					prop: "business_lock_person_name",
					width: 100
				},{
					label: "业务锁定人分组",
					prop: "business_lock_group_name",
					width: 100
				},{
					label: "订单店铺",
					prop: "shop_name",
					width: 100
				},{
					label: "驳回原因",
					prop: "reject_reason",
					width: 100,
					format: 'auxFormat',
					formatParams: 'sh_reject_reason',
				}
			],
		};
	},
	methods: {
		
	
		
		
		searchFun(msg, resolve) {
			console.log(this.params.menuInfo.code)
			let url = "/afterSale-web/api/aftersale/refundRquest/list";
			
            
			this.ajax.postStream(
				url,
				this.form,
				res => {
					if (res.body.result) {
						this.$message.success(msg || res.body.msg);
						this.pageTotal = res.body.content.count;
						this.roleList = res.body.content.list;
					} else {
						this.$message.error(res.body.msg);
					}
					resolve && resolve();
				},
				() => {
					resolve && resolve();
				},
				this.params.tabName
			);
		},
	
		// 搜索
		search(list, resolve) {
			this.form.where = list;
			this.searchFun(null, resolve);
		},
		pageChange(page_size) {
			this.form.page_size = page_size;
			this.searchFun();
		},
		currentPageChange(page) {
			this.form.page_no = page;
			this.searchFun();
		},
	
	
		
	
	
		
	},
	
	mounted() {
		
		this.searchFun();
		
	},
	
};
</script>
