<!-- 退款申请单列表 -->
<template>
	<xpt-list
		:data='roleList'
		:colData='colData'
		:pageTotal='pageTotal'
		:btns='btns'
		:isNeedClickEvent="true"
	    :searchPage="form.page_name"
	    :selectable="row => row.id"
		:showCount ='showCount'
		@count-off="countOff"
	    @search-click='search'
		@selection-change='selectChange'
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
		@row-dblclick="d => openOrder(d.id || d._id, d.original_type === 'REFUND_PLAN' && d.after_order_id, d.original_no)"
		ref='xptList'
	>
	</xpt-list>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
	props:['params'],
	data (){
		return {
			// selectType_options: {
			// 	bill_no		: '单据编号',
			// 	nick_name	: '买家昵称',
			// 	shop_name	: '订单店铺',
			// 	saleman		: '业务员',
			// 	original_no	: '来源单据号',
			// 	after_order_no	: '售后单号',
			// 	merge_trade_no	: '合并订单号',
			// 	refund_status	: '退款状态',
			// 	original_type	: '来源类型',
			// 	user_shop_name	: '收入店铺',
			// },
			countOffFlag:false,
			showCount:false,
			form: {
				page_name: 'aftersale_bill_refund_apply',
				pageSize: 50,
				pageNo: 1,
				where: [],
			},
			btns: [{
				type: 'primary',
				txt: '新增',
				// click: () => this.openOrder(),
				click: () => this.$message.error('请到新退款申请单列表新增'),
	        }/*,{
				type: 'primary',
				txt: '查看',
				click: () => this.openOrder(selectOrder[0].id),
	        }*/,{
				type: 'primary',
				txt: '刷新',
				click: this.searchFun,
	        },{
				type: 'danger',
				txt: '删除',
				click: this.delOrder,
				disabled: true,
	        }],
			pageTotal: 0,
			roleList:[],
			selectOrder: [],
			colData:[
				{
					label: '序号',
					prop: '_index',
					align: 'center',
					width: 50,
				},{
					label: '单据编号',
					prop: 'bill_no',
					redirectClick: d => this.openOrder(d.id, d.original_type === 'REFUND_PLAN' && d.after_order_id, d.original_no),
					width: 130,
				},{
					label: '订单店铺',
					prop: 'shop_name',
					width: 100,
				},{
					label: '收入店铺',
					prop: 'user_shop_name',
					width: 100,
				},{
					label: '售后单号',
					prop: 'after_order_no',
					width: 150,
				},{
					label: '业务状态',
					prop: 'business_status',
					formatter: prop => ({
						WAIT_SUBMIT: '待提交',
						SUBMIT_FINANCE: '提交财务',
						REJECTED: '驳回',
					}[prop] || prop),
					width: 70,
				},{
					label: '退款状态',
					prop: 'refund_status',
					formatter: prop => ({
						UNREFUND: '未退款',
						REFUNDED: '已退款',
					}[prop] || prop),
					width: 70,
				},{
					label: '业务员',
					prop: 'saleman',
					width: 100,
				},{
					label: '业务员分组',
					prop: 'saleman_group',
				},{
					label: '买家昵称',
					prop: 'nick_name',
					width: 120,
				},{
					label: '来源单据号',
					prop: 'original_no',
					width: 150,
				},{
					label: '原始店铺',
					prop: 'original_shop_name',
					width: 100,
				},{
					label: '来源类型',
					prop: 'original_type',
					formatter: prop => ({
						RETURNS_PLAN: '退货方案',
				        RETURNS_BILL: '退货跟踪单',
				        REFUND_PLAN: '退款方案',
				        LS_SALE_ORDER: '林氏销售订单',
				        MERGE_TRADE: '合并订单',
				        REFUND_APPLY_BILL: '退款申请单',
					}[prop] || prop),
					width: 100,
				},{
					label: '创建人',
					prop: 'creator_name',
				},{
					label: '创建日期',
					prop: 'create_time',
					format:"dataFormat1",
					width: 150,
				},{
					label: '作废状态',
					prop: 'cancel_status',
					formatter: prop => ({
						NORMAL: '未作废',
						CANCELLED: '已作废',
					}[prop] || prop),
				},{
					label: '退款类型',
					prop: '_refund_type',
					formatter: prop => ({ 
						COMPENSATION: '补偿',
						COMPENSATE: '补偿',//后端接口无此值
						PRICE_DIFF: '差价',
						DELIVERY_FEE: '延迟赔付',
						CARRIAGE: '运费',//后端接口无此值
						O2O_PRICE_DIFF: 'O2O跨店铺差价',
						O2O_DIFF: 'O2O跨店铺差价',//后端接口无此值
						SCRAP_OR_LOST: '报废',
						THREE_FEE: '三包费',
						THREE: '三包费',//后端接口无此值
						DISCOUNTING: '外购折现',
						DISCOUNT: '外购折现',
						CANCEL: '未发取消',
						DELAY: '延迟赔付',
						OVERPAY: '多付',
						PREFERENTIAL: '优惠/返现',
						RETURNS: '退货货款',
						REPAIR: '维修费',
					}[prop] || prop),
					width: 100,
				},{
					label: '退款原因',
					prop: '_refund_reason',
				},{
					label: '申请金额',
					prop: '_apply_amount',
				},{
					label: '扣费',
					prop: '_deduct_fee',
				},{
					label: '实退金额',
					prop: '_actual_amount',
				},
			],
		}
	},
	methods: {
		// _reset (){
		// 	this.form._type = ''
		// 	this.form.operate_type = ''
		// 	this.form.searchVal = ''
		// },
		// refresh (){
		// 	this._reset()
		// 	this.searchFun()
		// },
		/**
		*isNotTip值标识着是否需要提示信息
		**/

		countOff(){

			let self = this,
			url = "/afterSale-web/api/aftersale/bill/refundApply/findAfterApplySaleCount";
			if(!self.roleList.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;

			self.ajax.postStream(url,self.form,function(response){
					if(response.body.result){
						
						self.pageTotal = response.body.content;
						self.showCount = true;
						self.countOffFlag = false;

					}else{
						self.$message.error(response.body.msg);
					}
				});
		},
		searchFun(isNotTip, resolve,resolve2){
			let self = this;

			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApply/findAfterApplySaleList?permissionCode=REFUND_APPLY_ORDER_QUERY', this.form, res => {
			

				if(res.body.result){
					// this.roleList = this._tranStatus(res.body.content.list || [])
					// this.pageTotal = res.body.content.count

					let dataList = JSON.parse(JSON.stringify(res.body.content.list));
					if(dataList.length == (self.form.pageSize+1)&&dataList.length>0){
						dataList.pop();
					}

					self.roleList = dataList;

					let totalCount = self.form.pageNo*self.form.pageSize;
					if(!self.showCount){
						self.pageTotal = res.body.content.count == (self.form.pageSize+1)? totalCount+1:totalCount;
					}
					//this.$message.success(res.body.msg)
				}else {
					//this.$message.error(res.body.msg)
				}
				if(!isNotTip || !res.body.result){
					this.$message({
						type:res.body.result?'success':'error',
						message:res.body.msg
					})	
				}
				resolve && resolve()
				resolve2 && resolve2()
			}, () => {
				resolve && resolve()
			}, this.params.tabName)
		},
		_tranStatus (data){
			var otherRefundItemList = []
			,	data1 = data.map((obj, index) => {
				obj._index = index + 1

				this._tranRefundItemListKey(obj, obj.refundItemList[0])
				if(obj.refundItemList.length > 1){
					otherRefundItemList.push({
						index,
						data: obj.refundItemList.slice(1),
						orderId: obj.id,
					})
				}

				return obj
			})

			var spliceCount = 0
			otherRefundItemList.forEach(obj => {
				obj.data.forEach(obj2 => {
					obj2._domIndex = obj.index + spliceCount + 1
					data1.splice(obj2._domIndex, 0, this._tranRefundItemListKey({}, obj2, obj.orderId))
					++spliceCount
				})
			})

			// hack css将序号和复选框隐藏
			// setTimeout(() => {
			// 	var $tr = this.$refs.xptList.$el.querySelectorAll('tr')
			// 	;[].forEach.call($tr, $dom => $dom.classList.remove(this.$style['hide']));
			// 	otherRefundItemList.forEach(obj => {
			// 		obj.data.forEach(obj2 => {
			// 			$tr[obj2._domIndex + 1].classList.add(this.$style['hide'])
			// 		})
			// 	})
			// })

			return data1
		},
		_tranRefundItemListKey (obj, refundItemList0 = {}, orderId){
			if(orderId) obj._id = orderId
			;['refund_type', 'refund_reason', 'apply_amount', 'deduct_fee', 'actual_amount'].forEach(key => {
				if(key === 'actual_amount' && refundItemList0.apply_amount){
					obj._actual_amount = refundItemList0.apply_amount - refundItemList0.deduct_fee
				}else {
					obj['_' + key] = refundItemList0[key]
				}
			})

			return obj
		},
		selectChange (s){
			this.selectOrder = s
		},
		// 搜索
		search(list, resolve) {
			this.form.where = list
			// this.searchFun(null, resolve)
			new Promise((res,rej)=>{
				this.searchFun(null, resolve,res)

			}).then(()=>{
				if(this.form.pageNo != 1){
					this.pageTotal = 0;
				}
				this.showCount = false;
			})
		},
		pageChange(pageSize){
			this.form.pageSize = pageSize
			this.searchFun()
		},
		currentPageChange(page){
			this.form.pageNo = page
			this.searchFun()
		},
		_toPlanDetail (after_order_id, after_plan_no){
			this.ajax.postStream('/afterSale-web/api/aftersale/order/detail?permissionCode=AFTERSALE_QUERY', { id: after_order_id }, res => {
				if(res.body.result){
					res = res.body.content
					res.aftersaleOrderQuestionRowVO.some(obj => {
						var bool
						obj.listAftersaleOrderPlan && obj.listAftersaleOrderPlan.some(obj2 => {
							if(obj2.after_plan_no === after_plan_no){
								this.$root.eventHandle.$emit('creatTab',{
					                name: '售后方案详情',
					                params: {
					                	type: obj2.after_plan_type,
										afterOrderInfo: {
					                		after_order_id: obj2.after_order_id,
											after_order_no: res.after_order_no,
											merge_trade_id: res.merge_trade_id,
											merge_trade_no: res.merge_trade_no,
											after_plan_id: obj2.after_plan_id,
											after_question_id: obj2.after_question_sub_id,
											after_plan_group_id: obj2.after_plan_group_id,
											canEditSolutions: res.locker == this.getEmployeeInfo('id') && (res.status == 'PRE_HANDLE' || res.status == 'AFTER_HANDLE'),
											form:res
											//canEditSolutions: res.locker == Fn.getUserInfo('id') && res.status != 'FINISH',
						                },
					                },
					                component: () => import('@components/after_solutions/solutions')
					            })
								return (bool = true)
							}
						})
						return bool
					})
				}else {
					this.$message.error(res.body.msg || '找不到该售后单')
				}
			})
		},
		openOrder (id, after_order_id, after_plan_no){
			if(0 && after_order_id){//取消跳转到方案详情
				this._toPlanDetail(after_order_id, after_plan_no)
			}else {
				this.$root.eventHandle.$emit('creatTab',{
					name: '退款申请单',
					params: { id },
					component:()=> import('@components/after_sales_refund/refundRequest_2')
				})
			}
		},
		delOrder (){
			var successIds = []
			,	errorIds = []
			,	ajaxCount = 0

			if(!this.selectOrder.length){
				this.$message.error('请选择至少一行')
				return
			}

			this.$root.eventHandle.$emit('openDialog', {
				txt: '是否确认删除？',
				okTxt: '确认',
				noShowNoTxt: true,
				ok: () => {
					this.selectOrder.forEach(obj => {
						if(!obj.id) return
						this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApply/delete?permissionCode=REFUND_APPLY_ORDER_DELETE', { id: obj.id }, res => {
							++ajaxCount
							if(res.body.result){
								successIds.push(obj.bill_no)
							}else {
								errorIds.push(obj.bill_no + ':' + res.body.msg)
							}
							if(ajaxCount === this.selectOrder.length){
								if(successIds.length){
									this.$message.success(successIds.join() + '删除成功');
									this.searchFun(!1);
								}
								if(errorIds.length){
									this.$message.error('删除失败!' + errorIds.join())
								}
							}
						})
					})
				},
			})

		},
		//重写common/list的rowClick方法，避免退款明细复制行被选中
		_rowSelectionOverride (){
			var $table = this.$refs.xptList.$refs.table
			,	_func = $table.toggleRowSelection

			$table.toggleRowSelection = (data, bool) => {
				if(data.id) _func(data, bool)
			}
		},
	},
	mounted (){
		this._rowSelectionOverride()
	},
}
</script>

<style module>
.hide td:first-child :global(.cell) {
	display: none;
}
</style>