<!-- 新退款申请单列表 -->
<template>
	<div class="xpt-flex">
		<xpt-list-dynamic :data="roleList" :colData="salemanPersonColData" :pageTotal="pageTotal" :btns="salemanPersonBtns"
			:isNeedClickEvent="true" :showSelectRowNumByOnlyKey="true" :orderNo="true" :searchPage="form.page_name"
			:selectable="row => !row._total" @search-click="search" @selection-change="selectChange"
			@page-size-change="pageChange" @current-page-change="currentPageChange" @row-dblclick="d => openOrder(d)"
			@cell-click="cellClick" ref="xptList" :taggelClassName="refundRequestListClassName" @count-off="countOff"
			:showCount='showCount'>
			<template slot='btns'>
				<el-select v-model="progressLabel" placeholder="进度标签" size='mini' style="width:100px;margin-left:16px">
					<el-option v-for="item in progressLabelOptions" :key="item.code" :label="item.name" @click.native="setSigns"
						:value="item.code">
					</el-option>
				</el-select>
			</template>
		</xpt-list-dynamic>
		<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload>
	</div>
</template>
<script>
export default {
	props: ["params"],
	data() {
		let self = this;
		return {
      refundShopCodes: __AUX.get('manual_sync_refund_shop').map(item=>item.code),
			countOffFlag: false,
			showCount: false,
			ifClickUpload: false,
			refundOriginTypeCodeList: [],//复核锁定-来源店铺类型-编码
			financeLockBtnCheck: true,
			financeUNLockBtnCheck: true,
			checkBtnChech: true,
			confirmRefundBtnChech: true,
			progressLabel: '',
			progressLabelOptions: [],
			uploadData: {},
			form: {
				page_name: "aftersale_bill_refund_application",
				page_size: 50,
				page_no: 1,
				where: []
			},
			ifAllReturnData: false,
			salemanPersonBtns: [
				{
					type: "primary",
					txt: "新增",
					click: () => this.openOrder()
					// disabled: !!this.personBusinessAttribute.attributeValue,
				} /*,{
				type: 'primary',
				txt: '业务锁定',
				click: () => this.batchUpdate('businessLock'),
	        }*/,
				{
					type: "primary",
					txt: "上传附件",
					click: () => {
						if (this.ifAllReturnData) {
							this.$message.error('已选单据中存在业务状态为待重转的数据，请删除此数据！')
							return
						}
						this.uploadAction("post")
					}
				},
				{
					type: "primary",
					txt: "查看附件",
					click: () => this.uploadAction("get")
				},
				{
					type: "warning",
					txt: "初审锁定",
					click: () => {
						if (this.ifAllReturnData) {
							this.$message.error('已选单据中存在业务状态为待重转的数据，请删除此数据！')
							return
						}
						this.batchUpdate("financeLock")
					}
				},
				{
					type: "warning",
					txt: "初审解锁",
					click: () => {
						if (this.ifAllReturnData) {
							this.$message.error('已选单据中存在业务状态为待重转的数据，请删除此数据！')
							return
						}
						this.batchUpdate("financeUNLock")
					}
				},
				{
					type: "warning",
					txt: "核对",
					loading: false,
					click: () => {
						if (this.ifAllReturnData) {
							this.$message.error('已选单据中存在业务状态为待重转的数据，请删除此数据！')
							return
						}
						this.batchUpdate("check")
					}
				},
				{
					type: "warning",
					txt: "复核锁定",
					click: () => {
						if (this.ifAllReturnData) {
							this.$message.error('已选单据中存在业务状态为待重转的数据，请删除此数据！')
							return
						}
						this.batchUpdate("reviewLock")
					}
				},
				{
					type: "warning",
					txt: "复核解锁",
					click: () => {
						if (this.ifAllReturnData) {
							this.$message.error('已选单据中存在业务状态为待重转的数据，请删除此数据！')
							return
						}
						this.batchUpdate("reviewUNLock")
					}
				},
				{
					type: "warning",
					txt: "复核退款",
					loading: false,
					click: () => {
						if (this.ifAllReturnData) {
							this.$message.error('已选单据中存在业务状态为待重转的数据，请删除此数据！')
							return
						}
						this.beforeAuditRefund("confirmRefund")
					}
				},
				{
					type: "success",
					txt: "修改已退款",
					click: () => {
						if (this.ifAllReturnData) {
							this.$message.error('已选单据中存在业务状态为待重转的数据，请删除此数据！')
							return
						} else {
							this.updateFinish();
						}

					}
				},
				{
					isBtnGroup: true,
					btnGroupList: [
						{
							type: "success",
							txt: "下载",
							disable() {
								return self.params.menuInfo.code === "REFUND_APPLY_DETAIL_LIST"
							},
							click: this.showExportList
						}, {
							type: "success",
							txt: "导出",
							click: this.exportBillRefundApp
						}
					]
				},
				{
					type: "success",
					txt: "刷新",
					click: () => this.searchFun()
				},
				//  {
				// 	type: "primary",
				// 	txt: "重转",
				// 	click: () => this.returnFun()
				// },
				{
					type: "danger",
					txt: "强制作废",
					click: () => this.forceInvalid(),
					loading:false
				},
				{
					type: "primary",
					txt: "漏单下载",
					click: () => this.omitDownload(),
					loading: false
				},
				{
					type: 'success',
					txt: '修改退款方式',
					disabled() {
						return false
					},
					loading: false,
					click: () => self.modifyRefundType()
				},
				{
					type: 'primary',
					txt: '修改推荐处理人',
					disabled() {
						return false
					},
					loading: false,
					click: () => self.patchModifyProcessor()
				},
        {
					type: 'primary',
					txt: '自动提交未发销售折让',
					disabled() {
						return false
					},
					loading: false,
					click: () => self.beforePatchSubmitSalesAllowance()
				},
        {
					type: 'primary',
					txt: '下推售后单',
					disabled() {
						return false
					},
					loading: false,
					click: () => self.patchPushAfterSaleOrder()
				},
			],
			pageTotal: 0,
			roleList: [],
			selectOrder: [],
			listType: "saleman_person", //默认业务专用 saleman_person or finance_person 财务专用
			salemanPersonColData: [
				{
					label: "单据编号",
					prop: "bill_no",
					redirectClick: d =>
						this.openOrder(d /*, d.original_type === 'REFUND_PLAN' && d.after_order_id, d.original_no*/),
					width: 180
				},
				{
					label: "淘宝退款单号",
					prop: "refund_id",
					width: 150
				},
				{
					label: "订单店铺",
					prop: "shop_name",
					width: 100
				},
				{
					label: "业务状态",
					prop: "business_status",
					formatter: prop =>
					({
						CREATE: "创建",
						SUBMIT: "已提交",
						REJECT: "驳回",
						CHECK: "已核对",
						ING: "退款中",
						FINISH: "已退款",
						CANCELED: "作废",
						RE_TURN: '待重转',
						RE_REVIEW: '反审中',
					}[prop] || prop),
					width: 70
				},
				{
					label: "淘宝退款状态",
					prop: "status",
					// formatter: prop =>
					// 	({
					// 		WAIT_SELLER_AGREE: "买家已经申请退款，等待卖家同意",
					// 		WAIT_BUYER_RETURN_GOODS: "卖家已经同意退款，等待买家退货",
					// 		WAIT_SELLER_CONFIRM_GOODS: "买家已经退货，等待卖家确认收货",
					// 		SELLER_REFUSE_BUYER: "卖家拒绝退款",
					// 		CLOSED: "退款关闭",
					// 		SUCCESS: "退款成功",
					// 		// 云集新增淘宝退款状态字段值
					// 		WAIT_FOR_CONFIRMED_BY_THE_OWNER: "待店主确认-云集",
					// 		WAIT_FOR_BUSINESS_AUDIT: "待商家审核-云集",
					// 		WAIT_FOR_AMENDED_PETITION: "待修改申请-云集",
					// 		WAIT_FOR_RETURN_TO_BE_SENT: "待寄回退货-云集",
					// 		WAIT_FOR_CONFIRM_RECEIPT: "待确认收货-云集",
					// 		REFUND_IN_PROGRESS: "退款中-云集",
					// 		REFUND_FAILED: "退款失败-云集",
					// 		REFUNDED: "已退款-云集",
					// 		POST_SALE_CLOSED: "售后关闭-云集"
					// 	}[prop] || prop),
					format: "auxFormat",
					formatParams: "all_platform_refund_status",
					width: 100
				},
				{
					label: "退款原因",
					prop: "reason",
					width: 200
				},
				{
					label: "买家昵称",
					prop: "nick_name",
					width: 100
				},
				{
					label: "业务锁定人",
					prop: "business_lock_person_name",
					width: 100
				},
				{
					label: "经销商名称",
					prop: "dealer_customer_name",
					width: 100
				},
				{
					label: "店铺地区",
					format: "auxFormat",
					prop: "shop_area",
					formatParams: "shopArea"
				},
				{
					label: "经销商订单",
					prop: "if_dealer",
          width: 100,
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "是否批量走销售折让",
					prop: "if_sales_allowance",
					width: 180,
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
        {
					label: "是否批量走未前置优惠",
					prop: "if_preferential_discount",
					width: 180,
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "退款方式",
					prop: "refund_way",
					formatter: prop =>
					({
						ALIPAY: "支付宝退款",
						BANK: "银行卡退款",
						PROTOCOL: "协议退款",
						DISPUTE: "纠纷退款",
						BAIL: "保证金退款",
						B2C_ONLINE: "B2C线上退款",
						CARRY_OVER: "换货结转",
						CROSS_OVER_T: "跨单结转",
						DEALER_PREFERENTIAL: "经销优惠退款",
						OVERSEAS_AFTER_REFUND: '海外售后退款',
            YSF: '云闪付退款'
					}[prop] || prop)
				},
				{
					label: "用途",
					prop: "use_for_corss",
					formatter: prop =>
					({
						DELIVERY: "发货",
						DRAWBACK: "退款"
					}[prop] || prop)
				},
				{
					label: "业务员",
					prop: "saleman_name",
					width: 100
				},
				{
					label: "业务员分组",
					prop: "saleman_group_name"
				},
				{
					label: "代理人",
					prop: "proxy_name"
				},
				{
					label: "推荐处理人",
					prop: "best_staff_name"
				},
				{
					label: "推荐处理人分组",
          width:100,
					prop: "best_staff_group_name"
				},
				{
					label: "是否离职",
					prop: "best_staff_status"
				},
				{
					label: "退款成功时间",
					prop: "finish_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "退款子账号",
					prop: "sub_account"
				},
				{
					label: "退款超时时间",
					prop: "timeout",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "淘宝退款金额",
					prop: "refund_fee",
					width: 110
				},
				{
					label: "AG退款状态",
					prop: "ag_refunded",
					formatter: prop =>
					({
						Y: "成功",
						N: "等待",
						W: "进行中"
					}[prop] || prop)
				},
				{
					label: "AG退款时间",
					prop: "ag_refund_date",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "来源类型",
					prop: "original_type",
					formatter: prop =>
					({
						"TMALL": "天猫",
						"B2C": "B2C",
						"PDD": "拼多多",
						"JD": "京东",
						"JD_ZY": "京东自营",
						"VIP": "唯品会",
						"SUNING": "苏宁",
						"OTHER": "手工",
						"YJ": "云集",
						"I": "爱库存",
						"KL": "考拉",
						"MY": "蜜芽",
						"KS": "快手",
						"DY": "抖音",
						"MX": "猫享",
						"DW": "得物",
						"ZFE": '住范儿',
						"XMYP": '小米有品',
						"WX": '微信',
						"MZ": '喵住',
            "POS": '门店商城',
            "TM_C": '天猫超市',
            'DYLK': '抖音来客',
					}[prop] || prop),
					width: 100
				},
				{
					label: "创建人",
					prop: "creator_name"
				},
				{
					label: "创建日期",
					prop: "create_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "最后更新时间",
					prop: "last_modify_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "业务锁定",
					prop: "if_business_lock",
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "初审锁定",
					prop: "if_finance_lock",
          width:100,
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				}, {
					label: "是否价保",
					prop: "is_insured_refund",
          width: 100,
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "合并单号",
					prop: "merge_trade_no",
					width: 180
				},
        {
          label: "政府补贴订单",
					prop: "government_subsidy_order",
					width: 100,
          formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "初审锁定人",
					prop: "finance_lock_person_name"
				},
				{
					label: "申请金额",
					prop: "apply_amount",
					width: 130
				},
				{
					label: "实退金额",
					prop: "actual_amount",
					width: 130
				},
				{
					label: "提交财务人",
					prop: "submit_finance_person_name"
				},
				{
					label: "提交财务日期",
					prop: "submit_finance_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "提交财务人分组",
          width:100,
					prop: "submit_finance_group_name"
				},
				{
					label: "提交财务人大分组",
          width:120,
					prop: "submit_finance_big_group_name"
				},
				{
					label: "初审锁定时间",
					prop: "finance_lock_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "核对人",
					prop: "check_person_name"
				},
				{
					label: "核对时间",
					prop: "check_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "同意退款状态",
					prop: "agree_refund_status",
          width: 100,
					formatter(val) {
						switch (val) {
							case 'N':
								return '等待'
							case 'W':
								return '进行中'
							case 'Y':
								return '成功'
							default:
								return val
						}
					}
				},
				{
					label: "同意退款执行时间",
					prop: "agree_refund_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "驳回人",
					prop: "reject_person_name"
				},
				{
					label: "驳回时间",
					prop: "reject_date",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "复核锁定人",
					prop: "review_lock_person_name",
					width: 100
				},
				{
					label: "复核锁定时间",
					prop: "review_lock_time",
					format: "dataFormat1",
					width: 100
				},
				{
					label: "复核锁定",
					prop: "if_review_lock",
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop),
					width: 100
				},
				{
					label: "客服店铺",
					prop: "user_shop_name",
					width: 100
				},
				{
					label: "退款阶段",
					prop: "refund_phase",
					formatter: prop =>
					({
						ONSALE: "售中",
						AFTERSALE: "售后"
					}[prop] || prop)
				},
				{
					label: "小二介入时间",
					prop: "cs_interpose_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "是否林氏退款",
					prop: "if_ls_refund",
          width: 100,
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "是否上传附件",
          width: 100,
					prop: "if_exist_file",
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: '(明细)实退金额',
					prop: 'actual_amount_item'
				}, {
					label: '(明细)扣费',
					prop: 'deduct_fee'
				}, {
					label: '(明细)申请金额',
					prop: 'apply_amount_item'
				}, {
					label: '退款类型',
					prop: 'refund_type',
					formatter: prop =>
					({
						CANCEL: '未发取消',
						OVERPAY: '多付',
						PREFERENTIAL: '未前置优惠',
						SINGLE_DISCOUNT: '单品优惠',
						DELAY: '服务折让',
						COMPENSATION: '销售折让',
						REPAIR: '维修费',
						RETURNS: '退货货款',
						CARRIAGE: '运费',
						THREE: '三包费',
						O2O_DIFF: 'O2O跨店铺差价',
						PRICE_DIFF: '差价',
						DISCOUNT: '外购折现',
						CROSS_OVER: '多付',
						OTHER_REFUND: '其它退款',
						PLATFORM_DISCOUNT: '平台优惠',
						OVER_DISCOUNT: '多付-优惠前置',
						OVER_FUND: '多付-货款'
					}[prop] || prop)
				}, {
					label: '是否需要复核',
					prop: 'if_need_review',
          width: 100,
					formatter(val) {
						switch (val) {
							case 'Y': return '是';							case 'N': return '否';						}
					}
				}, {
					label: '是否开启资料补充',
					prop: 'if_open_request',
          width: 120,
					formatter(val) {
						switch (val) {
							case 'Y': return '是';							case 'N': return '否';						}
					}
				}, {
					label: '是否平台优惠退款',
					prop: 'if_platform_discount',
          width: 100,
					formatter(val) {
						switch (val) {
							case 'Y': return '是';							case 'N': return '否';						}
					}
				}, {
					label: '平台退款成功时间',
					prop: 'refund_success_time',
          width: 110,
					format: "dataFormat1",
				}, {
					label: '复核退款时间',
					prop: 'review_refund_time',
          width: 100,
					format: "dataFormat1",

				}, {
					label: '经销财账核对',
					prop: 'dealer_money_check',
          width: 100,
					formatter(val) {
						switch (val) {
							case 'Y': return '是';							case 'N': return '否';						}
					}
				}, {
					label: '帐号是否一致',
					width: '100',
					prop: 'if_same',
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop),
				}, {
					label: '交易成功+15天',
					width: '150',
					prop: 'end_time_plus',
					formatter: prop => ({
						Y: '是',
						N: '否',
					}[prop] || prop)
				}, {
					label: "退款模式",
					prop: "refund_mode",
					format: "auxFormat",
					formatParams: "refund_mode",
					width: 100
				}, {
					label: "驳回原因",
					prop: "reject_reason",
					width: 100,
					format: 'auxFormat',
					formatParams: 'sh_reject_reason',
				}, {
					label: "进度标签",
					prop: "progress_label",
					width: 100,
					format: 'auxFormat',
					formatParams: 'refund_progress_label',
				}
				, {
					label: "进度标签添加者",
					prop: "progress_label_creator",
					width: 100,
				}
			],
			financePersonColData: [
				{
					label: "单据编号",
					prop: "bill_no",
					redirectClick: d =>
						this.openOrder(d/*, d.original_type === 'REFUND_PLAN' && d.after_order_id, d.original_no*/),
					width: 180
				},
				{
					label: "合并单号",
					prop: "merge_trade_no",
					width: 180
				},
				{
					label: "业务状态",
					prop: "business_status",
					formatter: prop =>
					({
						CREATE: "创建",
						SUBMIT: "已提交",
						REJECT: "驳回",
						CHECK: "已核对",
						ING: "退款中",
						FINISH: "已退款",
						CANCELED: "作废",
						RE_TURN: '待重转',
						RE_REVIEW: '反审中',
					}[prop] || prop),
					width: 70
				},
        {
          label: "政府补贴订单",
					prop: "government_subsidy_order",
					width: 100,
          formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "初审锁定人",
					prop: "finance_lock_person_name"
				},
				{
					label: "买家昵称",
					prop: "nick_name",
					width: 120
				},
				{
					label: "是否批量走未发销售折让",
					prop: "if_sales_allowance",
					width: 180,
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "退款方式",
					prop: "refund_way",
					formatter: prop =>
					({
						ALIPAY: "支付宝退款",
						BANK: "银行卡退款",
						PROTOCOL: "协议退款",
						DISPUTE: "纠纷退款",
						BAIL: "保证金退款",
						B2C_ONLINE: "B2C线上退款",
						CARRY_OVER: "换货结转",
						CROSS_OVER_T: "跨单结转",
						DEALER_PREFERENTIAL: "经销优惠退款",
            YSF: '云闪付退款'
					}[prop] || prop)
				},
				{
					label: "用途",
					prop: "use_for_corss",
					formatter: prop =>
					({
						DELIVERY: "发货",
						DRAWBACK: "退款"
					}[prop] || prop)
				}
        /*, {
				label: '退款类型',
				prop: '??',
				formatter: prop => ({
					COMPENSATION: '销售折让',
					COMPENSATE: '销售折让',//后端接口无此值
					PRICE_DIFF: '差价',
					DELIVERY_FEE: '服务折让',
					CARRIAGE: '运费',//后端接口无此值
					O2O_PRICE_DIFF: 'O2O跨店铺差价',
					O2O_DIFF: 'O2O跨店铺差价',//后端接口无此值
					SCRAP_OR_LOST: '报废',
					THREE_FEE: '三包费',
					THREE: '三包费',//后端接口无此值
					DISCOUNTING: '外购折现',
					DISCOUNT: '外购折现',
					CANCEL: '未发取消',
					DELAY: '服务折让',
					OVERPAY: '多付',
					PREFERENTIAL: '未前置优惠',
					RETURNS: '退货货款',
					REPAIR: '维修费',
          }[prop] || prop),
          width: 100,
        }*/,
				{
					label: "申请金额",
					prop: "apply_amount",
					width: 130
				},
				{
					label: "实退金额",
					prop: "actual_amount",
					width: 130
				},
				{
					label: "订单店铺",
					prop: "shop_name",
					width: 100
				},
				{
					label: "淘宝退款状态",
					prop: "status",
					formatter: prop =>
					({
						WAIT_SELLER_AGREE: "买家已经申请退款，等待卖家同意",
						WAIT_BUYER_RETURN_GOODS: "卖家已经同意退款，等待买家退货",
						WAIT_SELLER_CONFIRM_GOODS: "买家已经退货，等待卖家确认收货",
						SELLER_REFUSE_BUYER: "卖家拒绝退款",
						CLOSED: "退款关闭",
						SUCCESS: "退款成功",
						// 云集新增淘宝退款状态字段值
						WAIT_FOR_CONFIRMED_BY_THE_OWNER: "待店主确认-云集",
						WAIT_FOR_BUSINESS_AUDIT: "待商家审核-云集",
						WAIT_FOR_AMENDED_PETITION: "待修改申请-云集",
						WAIT_FOR_RETURN_TO_BE_SENT: "待寄回退货-云集",
						WAIT_FOR_CONFIRM_RECEIPT: "待确认收货-云集",
						REFUND_IN_PROGRESS: "退款中-云集",
						REFUND_FAILED: "退款失败-云集",
						REFUNDED: "已退款-云集",
						POST_SALE_CLOSED: "售后关闭-云集"
					}[prop] || prop),
					width: 100
				},
				{
					label: "淘宝退款单号",
					prop: "refund_id",
					width: 150
				},
				{
					label: "淘宝退款金额",
					prop: "refund_fee",
					width: 110
				},
				{
					label: "提交财务人",
					prop: "submit_finance_person_name"
				},
				{
					label: "提交财务日期",
					prop: "submit_finance_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "提交财务人分组",
					prop: "submit_finance_group_name"
				},
				{
					label: "提交财务人大分组",
					prop: "submit_finance_big_group_name"
				},
				{
					label: "初审锁定时间",
					prop: "finance_lock_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "核对人",
					prop: "check_person_name"
				},
				{
					label: "核对时间",
					prop: "check_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "驳回人",
					prop: "reject_person_name"
				},
				{
					label: "驳回时间",
					prop: "reject_date",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "客服店铺",
					prop: "user_shop_name",
					width: 100
				},
				{
					label: "退款原因",
					prop: "reason",
					width: 200
				},
				{
					label: "退款超时时间",
					prop: "timeout",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "退款成功时间",
					prop: "finish_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "退款阶段",
					prop: "refund_phase",
					formatter: prop =>
					({
						ONSALE: "售中",
						AFTERSALE: "售后"
					}[prop] || prop)
				},
				{
					label: "小二介入时间",
					prop: "cs_interpose_time",
					format: "dataFormat1",
					width: 150
				},
				{
					label: "初审锁定",
					prop: "if_finance_lock",
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				}, {
					label: "是否价保",
					prop: "is_insured_refund",
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "业务锁定",
					prop: "if_business_lock",
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "是否离职",
					prop: "best_staff_status"
				},
				{
					label: "经销商名称",
					prop: "dealer_customer_name",
					width: 100
				},
				{
					label: "经销商订单",
					prop: "if_dealer",
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: "是否林氏退款",
					prop: "if_ls_refund",
					formatter: prop =>
					({
						Y: "是",
						N: "否"
					}[prop] || prop)
				},
				{
					label: '(明细)实退金额',
					prop: 'actual_amount_item'
				}, {
					label: '(明细)扣费',
					prop: 'deduct_fee'
				}, {
					label: '(明细)申请金额',
					prop: 'apply_amount_item'
				}, {
					label: '退款类型',
					prop: 'refund_type',
					formatter: prop =>
					({
						CANCEL: '未发取消',
						OVERPAY: '多付',
						PREFERENTIAL: '未前置优惠',
						SINGLE_DISCOUNT: '单品优惠',
						DELAY: '服务折让',
						COMPENSATION: '销售折让',
						REPAIR: '维修费',
						RETURNS: '退货货款',
						CARRIAGE: '运费',
						THREE: '三包费',
						O2O_DIFF: 'O2O跨店铺差价',
						PRICE_DIFF: '差价',
						DISCOUNT: '外购折现',
						CROSS_OVER: '多付',
						OTHER_REFUND: '其它退款',
						PLATFORM_DISCOUNT: '平台优惠',
						OVER_DISCOUNT: '多付-优惠前置',
						OVER_FUND: '多付-货款'
					}[prop] || prop)
				}, {
					label: '是否需要复核',
					prop: 'if_need_review',
					formatter(val) {
						switch (val) {
							case 'Y': return '是';							case 'N': return '否';						}
					}
				}, {
					label: '是否开启资料补充',
					prop: 'if_open_request',
					formatter(val) {
						switch (val) {
							case 'Y': return '是';							case 'N': return '否';						}
					}
				}, {
					label: '是否平台优惠退款',
					prop: 'if_platform_discount',
					formatter(val) {
						switch (val) {
							case 'Y': return '是';							case 'N': return '否';						}
					}
				}, {
					label: '平台退款成功时间',
					prop: 'refund_success_time',
					format: "dataFormat1",
				}, {
					label: '复核退款时间',
					prop: 'review_refund_time',
					format: "dataFormat1",

				}, {
					label: "驳回原因",
					prop: "reject_reason",
					width: 100,
					format: 'auxFormat',
					formatParams: 'sh_reject_reason',
				}, {
					label: "进度标签",
					prop: "progress_label",
					width: 100,
					format: 'auxFormat',
					formatParams: 'refund_progress_label',
				}
				, {
					label: "进度标签添加者",
					prop: "progress_label_creator",
					width: 100,
				}
			]
		};
	},
	methods: {
		patchModifyProcessor() {
			if (!this.selectOrder.length) {
				this.$message.error("请选择要操作的单据");
				return;
			}
			// 校验是否能修改
			this.ajax.postStream(`/afterSale-web/api/aftersale/bill/refundApp/checkBeforeUpdateRefundHandler`, {
				idList: this.selectOrder.map(item => item.id)
			}, (res) => {
				if (!res.body.result) {
					this.$message.error(res.body.msg);
					return;
				}
				const params = {
					isSelect: true,
					salesmanTypeList: [],
					callback: (d) => {
						//批量修改处理人
						this.ajax.postStream(`/afterSale-web/api/aftersale/bill/refundApp/batchUpdateRefundHandler`, {
							refund_handler_id: d.id,
							idList: this.selectOrder.map(item => item.id)
						}, d => {
							if (d.body.result) {
								this.$message.success(d.body.msg);
								this.searchFun();
							} else {
								this.$message.error(d.body.msg);

							}
						}, (e) => {
							this.$message.error(e);
							resolve && resolve();
						})
					}
				}
				this.$root.eventHandle.$emit('alert', {
					close: function () { },
					style: 'width:900px;height:600px',
					title: "业务员列表",
					params: params,
					component: () => import('@components/duty/receiverSelectIntervener.vue')
				});
			})
		},
		setSigns() {

			let ids = [];
			if (!this.selectOrder.length) {
				this.$message.error("请选择要操作的单据");
				return false;
			}
			this.selectOrder.forEach(item => {
				ids.push(item.id);
			});
      let progressValue=this.progressLabel;
      if(this.progressLabel==='CANCEL_MARK'){
        progressValue='';
      }
			this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/progressLabel", { ids: ids, progressLabel: progressValue }, res => {
				if (res.body.result) {
					this.$message.success(this.progressLabel==='CANCEL_MARK'?'取消标记成功':res.body.msg);
					this.searchFun();
					this.progressLabel = ''
				} else {
					this.$message.error(res.body.msg);
				}
			});
		},
		beforeAuditRefund() {
			let self = this;
			var postData = this.selectOrder.map(obj => obj.id);
			self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/checkZeroRefund', postData, res => {
				if (res.body.result) {
					self.batchUpdate("confirmRefund")
				} else {
					self.$confirm(
						"客户可能已修改过后台的退款金额，请确认是否继续复核退款单",
						"提示",
						{
							confirmButtonText: "确定",
							cancelButtonText: "取消",
							type: "danger",
						}
					).then(() => {
						self.batchUpdate("confirmRefund")
					}).catch(() => {
						return false;
					});
				}
			})
		},
		getUserPersonDiscountAttribute(callback) {
			this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttribute', {
				personId: this.getEmployeeInfo('personId'),
				salesmanType: 'GLOBAL',
				attribute: 'IF_CHANGE_REFUND_WAY',
			}, res => {
				let hasModificationOfApplicationAmountPrivilege = res.body.result && res.body.content && res.body.content.attributeValueText === '是'
				callback && callback(hasModificationOfApplicationAmountPrivilege)
			}, err => {
				this.$message.error(err)
				callback && callback(false)
			})
		},
		modifyRefundType() {
			let self = this
			new Promise(resolve => {
				this.getUserPersonDiscountAttribute(resolve)
			}).then((ifPermission) => {
				this.hasPlatformDiscountPrivilege = ifPermission
				if (!ifPermission) {
					this.$message.error('您无修改退款方式权限，请联系退款组人员进行操作')
					return
				}
				let orderList = ''
				if (this.selectOrder.length > 0) {
					orderList = this.selectOrder.reduce((string, item) => {
						string += `${item.bill_no}\n`
						return string
					}, '')
				}
				self.$root.eventHandle.$emit('alert', {
					params: {
						_close() {
							self.searchFun()
						},
						orderList: orderList
					},
					component: () => import("./refundRequest_3/modifyRefundType"),
					style: 'width:400px;height:500px',
					title: '修改退款方式'
				});
			})
		},
		// 重转操作
		returnFun() {
			let ids = [];
			if (!this.selectOrder.length) {
				this.$message.error("只能选择要操作的单据");
				return false;
			}
			let ifAllReturnData = this.selectOrder.some(item => {
				return item.business_status != 'RE_TURN'
			})
			if (ifAllReturnData) {
				this.$message.error("请选择业务状态为待重转的单据");
				return false;
			}
			this.selectOrder.forEach(item => {
				ids.push(item.id);
			});
			this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/convertAgRefund?permissionCode=REFUND_APPLY_ORDER_AG_CONVERT", { ids: ids }, res => {
				if (res.body.result) {
					this.$message.success('标签添加成功');
					this.searchFun();
				} else {
					this.$message.error(res.body.msg);
				}
			});
		},
		//强制作废
		forceInvalid() {
			let self = this;
      const idList = this.selectOrder.filter(item => item.refund_id)
      self.$root.eventHandle.$emit("alert", {
        title: "作废退款单",
        style: "width:600px;height:400px",
        component: () =>
          import("@components/after_sales_refund/forceInvalidAlertModal.vue"),
        params: {
          orderNo: idList.length > 0 ? idList.map(item => {
            return item.refund_id
          }) : [],
          callback: (d) => {
            console.log(d)
            let btnIndex = self.salemanPersonBtns.findIndex(item => item.txt == '强制作废')
            self.salemanPersonBtns[btnIndex].loading = true
            setTimeout(() => { self.salemanPersonBtns[btnIndex].loading = false }, 1000)
            self.searchFun();
          },
        },
      });
		},
		omitDownload() {
      if(this.selectOrder.length>1){
        const shopIds = Array.from(new Set(this.selectOrder.map(item => item.shop_id)))
        if(shopIds.length>1){
          this.$message.warning('请选择同一店铺的退款单')
          return
        }
      }
      // #BHWL-265 抖音来客需要特殊处理
      if(this.selectOrder.length>0&&!this.refundShopCodes.includes(this.selectOrder[0].shop_code)&&this.selectOrder[0].original_type !== 'DYLK'){
          this.$message.warning('该订单店铺不支持漏单下载')
          return
      }
			this.$root.eventHandle.$emit("alert", {
				title: "漏单下载",
				style: "width:900px;height:600px",
				component: () =>
					import("@components/after_sales_refund/omitDownloadPopup.vue"),
				params: {
          list:this.selectOrder.map((item) => ({
            ...item,
            shop_code: item.original_type === 'DYLK' ? 'DYLK' : item.shop_code
          })),
					callback: () => { },
				},
			});
		},
		exportBillRefundApp() {
			let url = "/reports-web/api/reports/afterSaleExport/exportBillRefundApp";
			// let postData = JSON.parse(JSON.stringify(this.from));
			let postData = Object.assign({}, this.form);

			if (this.params.menuInfo.code === "REFUND_APPLY_DETAIL_LIST") {
				if (this.form.where && this.form.where.length == 0) {
					this.form.where.push({
						condition: 'OR',
						field: '306ac610bb0c77402dee8aa5a424772b',
						operator: 'THIS MONTH',
						table: '5f7dea4f36a2a55e6c2fe9c8ace3a1b0',
						value: ''
					})
				}
				postData.ifExport = 'Y'
				url = "/reports-web/api/reports/afterSaleExport/exportAppRefundItem";
			}


			this.ajax.postStream(url, postData, res => {
				if (res.body.result) {
					this.$message.success(res.body.msg);
				} else {
					this.$message.error(res.body.msg);
				}
			});
		},
		updateFinish() {

			let ids = [];
			if (!this.selectOrder.length) {
				this.$message.error("只能选择要操作的单据");
				return false;
			}
			this.selectOrder.forEach(item => {
				ids.push(item.id);
			});
			this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/updateFinish", { ids: ids }, res => {
				if (res.body.result) {
					this.$message.success(res.body.msg);
					this.searchFun();
				} else {
					this.$message.error(res.body.msg);
				}
			});
		},
		uploadAction(method) {
			if (this.selectOrder.length !== 1) {
				this.$message.error("只能选择一行");
				// return
			}

			if (method === "post") {
				this.ifClickUpload = true;
				this.uploadData = {
					parent_name: "AFTER_ORDER",
					parent_no: this.selectOrder[0].after_order_no || this.selectOrder[0].bill_no,
					child_name: "REFUNDAPPLYBILLDETAIL",
					child_no: this.selectOrder[0].bill_no,
					content: JSON.parse(JSON.stringify(this.selectOrder[0] || {}))
				};
				setTimeout(() => {
					this.ifClickUpload = false;
				}, 100);
			} else {
				this.$root.eventHandle.$emit("alert", {
					params: {
						// className: this.$style['download-img-del-hide'],
						parent_name: "AFTER_ORDER",
						parent_name_txt: this.selectOrder[0].after_order_no ? "售后单号" : "退款申请单号",
						parent_no: this.selectOrder[0].after_order_no || this.selectOrder[0].bill_no,
						child_name: "REFUNDAPPLYBILLDETAIL",
						child_no: null,
						// child_name_txt: '商品问题id',
						ext_data: null,
						cantDel: true,
						nickname: this.selectOrder[0].nick_name,
						mergeTradeId: this.selectOrder[0].merge_trade_id
						// callback: files => {
						// 	this.listAfterMaterialVO[index].attachment_count = files.length
						// },
					},
					component: () => import("@components/after_sales/afterSale_aboutZD_download.vue"),
					style: "width:80%;height:600px",
					title: "下载列表"
				});
			}
		},

		// 批量 初审锁定 or 财务解锁 or 核对
		batchUpdate(type) {
			let self = this;
			var errMsg;
			if (type === "financeLock" && this.financeLockBtnCheck !== 1) {
				// 初审锁定
				errMsg =
					"批量初审锁定需每一项都满足以下条件：\n1.操作人是退款专员或退款组长身份\n2.业务状态=已提交\n3.是否初审锁定=否";
			} else if (type === "financeUNLock" && this.financeUNLockBtnCheck !== 1) {
				errMsg = "批量财务解锁需每一项都满足以下条件：\n1.操作人=初审锁定人\n2.业务状态=已提交\n3.是否初审锁定=是";
			} else if (type === "check" && this.checkBtnChech !== 1) {
				errMsg =
					"批量核对需每一项都满足以下条件：\n1.操作人=初审锁定人\n2.业务状态=已提交\n3.是否初审锁定=是\n4.非手工退款/非京东自营退款";
			} else if (type === "confirmRefund" && this.confirmRefundBtnChech !== 1) {
        const originShopAux = __AUX.get('REVIEW_REFUND_SHOP');
        const refundOriginTypeNameList = originShopAux.map(item=>item.name);
        const originTypeStr=refundOriginTypeNameList.join("/");
				errMsg =
					`批量复核退款需每一项都满足以下条件：\n1.操作人=复核锁定人\n2.业务状态=已核对\n3.是否复核锁定=是\n4.来源类型是${originTypeStr}`;
			}

			if (errMsg) {
				this.$message.error({
					message: errMsg,
					customClass: this.$style["message-box"]
				});
				return;
			}

			var postData = this.selectOrder.map(obj => obj.id);
			let btnIndex = -1;
			if (type === "confirmRefund") {
				btnIndex = this.salemanPersonBtns.findIndex(item => item.txt == '复核退款')
				this.salemanPersonBtns[btnIndex].loading = true
			}
			if (type === "check") {
				btnIndex = this.salemanPersonBtns.findIndex(item => item.txt == '核对')
				this.salemanPersonBtns[btnIndex].loading = true
			}
			this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/" + type, postData, res => {
				if (res.body.result) {
					this.searchFun(res.body.msg, () => {
						setTimeout(() => {
							this.roleList.forEach(obj => {
								if (postData.indexOf(obj.id) !== -1) {
									this.$refs.xptList.$refs.list.$refs.table.toggleRowSelection(obj);
								}
							});
						});
					});
				} else {
					this.$message.error(res.body.msg || '请稍候再试');
				}
				type === "confirmRefund" ? self.salemanPersonBtns[btnIndex].loading = false : "";
				type === "check" ? self.salemanPersonBtns[btnIndex].loading = false : "";
			}, err => {
				type === "confirmRefund" ? self.salemanPersonBtns[btnIndex].loading = false : "";
				type === "check" ? self.salemanPersonBtns[btnIndex].loading = false : "";
				this.$message.error(err);
			});
		},
		openOrder(d) {
			// 打开单号
			if (d && d.id && document.querySelector('[data-symbol="billRefundApplication_' + d.id + '"]')) {
				this.$message.error("该退款申请单已经打开");
			} else {
				this.$root.eventHandle.$emit("creatTab", {
					name: "新退款申请单",
					params: {
						id: d ? d.id : null,
						bill_no: d ? d.bill_no : null,
						idList: d && d.id ? Array.from(new Set(/*去重*/ this.roleList.map(obj => obj.id))) : [] //用于详情的上一页/下一页
					},
					component: () => import("@components/after_sales_refund/refundRequest_3")
				});
			}
		},
		// 计算每一页的合计行
		_calcPageTotalFee(list) {
			var totalFeeKey = {
				refund_fee: 0,
				apply_amount: 0,
				actual_amount: 0
			};

			if (list.length) {
				list.forEach(obj => {
					Object.keys(totalFeeKey).forEach(key => {
						totalFeeKey[key] += Number(obj[key]) || 0;
					});
				});

				Object.keys(totalFeeKey).forEach(key => {
					totalFeeKey[key] = "合计" + totalFeeKey[key].toFixed(2);
				});

				list.push(
					Object.assign(totalFeeKey, {
						_total: true
					})
				);
			}

			return list;
		},
		countOff() {

			let self = this,
				url = "/afterSale-web/api/aftersale/bill/refundApp/listCount";
			if (!self.roleList.length) {
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if (!!self.countOffFlag) {
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;

			self.ajax.postStream(url, self.form, function (response) {
				if (response.body.result) {
					// var totalObj =  {
					// 	amount:'合计: '+response.body.content.amount.extendsOne,
					// 	merge_trade_id:'合计'

					// };
					// self.list = self.list.concat(totalObj);
					self.pageTotal = response.body.content;
					self.showCount = true;
					self.countOffFlag = false;

				} else {
					self.$message.error(response.body.msg);
				}
			});
		},
		searchFun(msg, resolve, callback) {
			// console.log(this.params.menuInfo.code)
			let _this = this;
			let url = "/afterSale-web/api/aftersale/bill/refundApp/list";
			if (this.params.menuInfo.code === "REFUND_APPLY_LIST_2") {
				// 新退款申请单
				this.form.page_name = "aftersale_bill_refund_application";
			} else if (this.params.menuInfo.code === "REFUND_APPLY_DETAIL_LIST") {
				this.form.page_name = "aftersale_bill_refund_application_item";
				if (this.form.where && this.form.where.length == 0) {
					this.form.where.push({
						condition: 'OR',
						field: '306ac610bb0c77402dee8aa5a424772b',
						operator: 'THIS MONTH',
						table: '5f7dea4f36a2a55e6c2fe9c8ace3a1b0',
						value: ''
					})
				}
				url = '/afterSale-web/api/aftersale/bill/refundApp/listDetail';
			} else {
				// 经销商退款申请单
				this.form.page_name = "aftersale_bill_refund_application_dealer";
			}

			this.ajax.postStream(
				url,
				this.form,
				res => {
					if (res.body.result) {

						let dataList = JSON.parse(JSON.stringify(res.body.content.list));
						if (dataList.length == (_this.form.page_size + 1) && dataList.length > 0) {
							dataList.pop();
						}
						// this.$message.success(msg || res.body.msg);
						this.roleList = this._calcPageTotalFee(dataList || []);
						// this.pageTotal = res.body.content.count;
						if (!_this.showCount) {
							_this.pageTotal = res.body.content.count == (_this.form.page_size + 1) ? (_this.form.page_no * _this.form.page_size) + 1 : (_this.form.page_no * _this.form.page_size);
						}
						// _this.showCount = false;

					} else {
						this.$message.error(res.body.msg);
					}
					resolve && resolve();
					callback && callback();
				},
				() => {
					resolve && resolve();
				},
				this.params.tabName
			);
		},
		selectChange(s) {
			// var financeLockBtnDisabled = true
			// ,	financeUNLockBtnDisabled = true
			// ,	checkBtnDisabled = true

			this.financeLockBtnCheck = true;
			this.financeUNLockBtnCheck = true;
			this.checkBtnChech = true;
			this.confirmRefundBtnChech = true;

			this.selectOrder = s.map(obj => {
				console.log("obj", obj);
				if (this.financeLockBtnCheck && obj.business_status === "SUBMIT" && /^(null|N)$/.test(obj.if_finance_lock)) {
					this.financeLockBtnCheck = 1;
				} else {
					this.financeLockBtnCheck = false;
				}

				if (
					this.financeUNLockBtnCheck &&
					obj.finance_lock_person === this.getEmployeeInfo("id") &&
					obj.business_status === "SUBMIT" &&
					obj.if_finance_lock === "Y"
				) {
					this.financeUNLockBtnCheck = 1;
				} else {
					this.financeUNLockBtnCheck = false;
				}

				if (
					this.checkBtnChech &&
					obj.finance_lock_person === this.getEmployeeInfo("id") &&
					obj.business_status === "SUBMIT" &&
					obj.if_finance_lock === "Y"
				) {
					this.checkBtnChech = 1;
				} else {
					this.checkBtnChech = false;
				}

        const originShopAux = __AUX.get('REVIEW_REFUND_SHOP');
        const refundOriginTypeCodeList = originShopAux.map(item=>item.code);
        console.log('refundOriginTypeCodeList', refundOriginTypeCodeList)

				if (
					this.confirmRefundBtnChech &&
					obj.review_lock_person === this.getEmployeeInfo("id") &&
					obj.business_status === "CHECK" &&
					obj.if_review_lock === "Y" &&
					refundOriginTypeCodeList.includes(obj.original_type)
				) {
					this.confirmRefundBtnChech = 1;
				} else {
					this.confirmRefundBtnChech = false;
				}

				return obj;
			});
		},
		// 搜索
		search(list, resolve) {
			let self = this;

			this.form.where = list;
			// this.searchFun(null, resolve);
			new Promise((res, rej) => {
				this.searchFun(null, resolve, res);
			}).then(() => {
				if (self.form.page_no != 1) {
					self.pageTotal = 0;
				}
				this.showCount = false;
			})

		},
		pageChange(page_size) {
			this.form.page_size = page_size;
			this.searchFun();
		},
		currentPageChange(page) {
			this.form.page_no = page;
			this.searchFun();
		},
		cellClick(obj, col, cell, event) {
			if (obj._total) return;

			var isClickCheckbox /*是否点击了checkbox*/ = !col.label;

			// event.preventDefault()
			this._overrideToggleRowSelection.bool = isClickCheckbox;

			if (isClickCheckbox && this._listenKeyEvent.isPressingShift) {
				var a1 = this.roleList.indexOf(this.selectOrder[0]),
					a3 = this.roleList.indexOf(obj);

				if (a1 > a3) {
					[a1, a3] = [a3, a1]; //大小调转
				}

				if (a1 > -1 && a3 > -1) {
					setTimeout(() => {
						this.$refs.xptList.$refs.list.$refs.table.clearSelection();
						this.roleList.forEach((obj, index) => {
							if ((index > a1 && index < a3) || index === a1 || index === a3) {
								this.$refs.xptList.$refs.list.$refs.table.toggleRowSelection(obj);
							}
						});
					});
				}
			}
		},
		// 修改el-table原生toggleRowSelection方法
		_overrideToggleRowSelection() {
			var self = this,
				$table = this.$refs.xptList.$refs.list.$refs.table,
				_toggleRowSelectionFunc = $table.toggleRowSelection;

			$table.toggleRowSelection = function (obj, bool) {
				!self._overrideToggleRowSelection.bool && $table.clearSelection();
				_toggleRowSelectionFunc(obj, bool);
			};
		},
		_listenKeyEvent(e) {
			if (e.type === "keyup") {
				this._listenKeyEvent.isPressingShift = false;
			} else if (e.type === "keydown" && e.shiftKey) {
				this._listenKeyEvent.isPressingShift = true;
			}
		},
		checkIsREFUND_ASSISTANT(cb) {
			this.ajax.postStream(
				"/user-web/api/userPerson/isValidSalesmanType",
				{
					personId: this.getEmployeeInfo("personId"),
					salesmanType: "REFUND_ASSISTANT"
				},
				res => {
					if (res.body.content) this.listType = "finance_person";
					cb();
				},
				() => {
					cb();
				}
			);
		},
		refundRequestListClassName(row) {
			if (row.refund_amount_change == "Y" && row.end_time_plus == "Y") {
				return this.$style["refundAmount"];
			} else if (row.refund_amount_change == "Y" && row.end_time_plus == "N") {
				return this.$style["refundAmountAndEndTime"]
			} else if (row.refund_amount_change == "N" && row.end_time_plus == "N") {
				return this.$style["endTime"]
			} else if (row.end_time_plus == "N") {
				console.log("我到这了")
				return this.$style["endTime"]
			}
		},
		// 导出文件下载
		showExportList() {
			if (this.params.menuInfo.code !== "REFUND_APPLY_DETAIL_LIST") {
				this.$message.error('该按钮不能点击');
				return false;
			}
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style: 'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_AFTERSALE_REFUND_ITEM_APP',
					},
				},
			})
		},
    beforePatchSubmitSalesAllowance(){
      if (!this.selectOrder.length) {
				this.$message.warning("请选择单据");
				return false;
			}
      const callback = () => {
        this.patchSubmitSalesAllowance();
      }
      const params = {
        callback,
        list:this.selectOrder,
      }
      this.$root.eventHandle.$emit("alert", {
        params: params,
        component: () => import("@components/after_sales_refund/components/refund-upload-dialog"),
        style: "width:500px;height:250px",
        title: "自动提交未发销售折让",
      });
    },
    patchSubmitSalesAllowance() {
      if (!this.selectOrder.length) {
				this.$message.error("请选择单据");
				return false;
			}

      this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/salesAllowanceAutoSubmit/batch", this.selectOrder.map((item)=>{return {id: item.id, bill_no: item.bill_no}}), res => {
				if (res.body.result) {
					this.$message.success(res.body.msg);
          this.searchFun();
				} else {
          const failed = res.body.content;
          if(failed && failed.length) {
              this.$root.eventHandle.$emit('alert', {
                close: function () { },
                style: 'width:900px;height:600px',
                title: "执行结果",
                params: { data: res.body.content },
                component: () => import('@components/after_sales_refund/components/execution-result-table.vue')
              });

          } else {
            this.$message.error(res.body.msg);
          }
				}
			});
    },
    patchPushAfterSaleOrder() {
      this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/pushAftersaleOrderCheck",{},res=> {
        if(!res.body.result) {
          this.$message.error(res.body.msg);
          return;
        }

        this.$root.eventHandle.$emit('alert', {
            close: function () { },
            style: 'width:580px;height:570px',
            title: "下推售后单",
            params: {
              orders: this.selectOrder.map((item)=> {return {id: item.id, bill_no: item.bill_no, refund_id: item.refund_id}}),
              callback: (res) => {
                if(res.result) {
                  this.$message.success(res.msg);
                  this.searchFun();
                } else {
                  const failedData = res.content;
                  if(failedData && failedData.length) {
                    this.$root.eventHandle.$emit('alert', {
                      close: function () { },
                      style: 'width:800px;height:600px',
                      title: "执行结果",
                      params: {
                        data: failedData,
                      },
                      component: () => import('@components/after_sales_refund/components/execution-result-table.vue')
                  })

                } else {
                  this.$message.error(res.msg);
                }
              }
            }
          },
            component: () => import('@components/after_sales_refund/pushAfterSaleOrder.vue')
          });

      })
    }
	},
	created() {
		if (this.params.menuInfo.code === "REFUND_APPLY_LIST_2") {
			// 新退款申请单
			this.form.page_name = "aftersale_bill_refund_application";
		} else if (this.params.menuInfo.code === "REFUND_APPLY_DETAIL_LIST") {
			this.form.page_name = "aftersale_bill_refund_application_item";
		} else {
			// 经销商退款申请单
			this.form.page_name = "aftersale_bill_refund_application_dealer";
		}
	},
	mounted() {
		this._overrideToggleRowSelection();
		// this.checkIsREFUND_ASSISTANT(this.searchFun)
		// this.searchFun();
		window.addEventListener("keydown", this._listenKeyEvent, false);
		window.addEventListener("keyup", this._listenKeyEvent, false);
		this.progressLabelOptions = __AUX.get('refund_progress_label')
		let rejectReason = __AUX.get('sh_reject_reason').map(item => {
			return { name: item.name, code: item.code }
		})
		this.$refs.xptList.$refs.dynamic_header.$refs.dynamic_searchbox.$refs.searchItem.propsData.fields.forEach(item => {
			if (item.field == 'af7f64a1ca89b543081fe5dfcb3b08eb') {
				item.values = rejectReason;
			}
		})

	},
	destroyed() {
		window.removeEventListener("keydown", this._listenKeyEvent, false);
		window.removeEventListener("keyup", this._listenKeyEvent, false);
	},
	watch: {
		selectOrder: function (n, o) {
			let ifAllReturnData = n.some(item => {
				return item.business_status == 'RE_TURN'
			})
			this.ifAllReturnData = ifAllReturnData
		}
	}
};
</script>
<style module>
.message-box :global(.el-message__group) p,
.message-box1 :global(.el-message__group) p {
	white-space: pre-wrap;
}

.message-box :global(.el-message__img) {
	height: 100%;
	top: 50%;
	transform: translateY(-50%);
	background-color: #ff4949;
}

.message-box :global(.el-message__group) {
	height: auto;
}

/* .download-img-del-hide :global(.xpt-image__thumbnail .el-icon-close) {
	display: none;
} */
.refundAmount,
.refundAmount td {
	background-color: #f99 !important;
}

.endTime,
.endTime td {
	background-color: #f9c855 !important;
}

.refundAmountAndEndTime,
.refundAmountAndEndTime td {
	background-color: yellowgreen !important;
}
</style>
