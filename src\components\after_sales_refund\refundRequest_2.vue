<!-- 退款申请单 -->
<template>
<div class="xpt-flex">
	<el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="18">
				<el-button type="primary" size="mini" @click="addNewOrder" :disabled="addNewBtn">新增</el-button>
				<el-button type="success" size="mini" @click="save" :disabled="!isCanEditFormInfo">保存</el-button>
				<el-button type="primary" size="mini" @click="
					e => {
						if(form.business_locker_id === getEmployeeInfo('id')){
							save(e, cb => {
								ajaxFunc('submitFinance?permissionCode=REFUND_APPLY_ORDER_SUBMIT_FINANCE', { id: form.id }, () => {
									cb()
									getOrderDetail()
								}, cb)
							})
						}else {
							$refs.form.validate(valid => {
					          	if (valid){
					          		_checkRefoudDetailListRequired()
									ajaxFunc('submitFinance?permissionCode=REFUND_APPLY_ORDER_SUBMIT_FINANCE', { id: form.id }, () => {
										getOrderDetail()
									})
					          	}
					        })
						}
					}
				" :disabled="!form.id || !someCanEdit">提交财务</el-button><!-- 提交财务必须要先保存校验必填字段 -->
				<el-button type="primary" size="mini" @click="_refresh">刷新</el-button>
				<el-button type="primary" size="mini" @click="selectMergeOrder" :disabled="!!form.id || !isCanEditFormInfo">选单</el-button>
				<el-button type="primary" size="mini" @click="cancelOrder" :disabled="!form.id || !someCanEdit || form.refundItemList.some(obj => obj.refund_type === 'RETURNS')">作废</el-button>
				<el-button-group>
					<el-button
						type="primary"
						@click="uploadAction('post')"
						size="mini"
						:disabled="
							!form.id ||
							!(form.cancel_status !== 'CANCELLED' && form.refund_status !== 'REFUNDED')
						">上传</el-button>
					<el-button type="primary" @click="uploadAction('get')" size="mini" :disabled="!form.id">查看附件</el-button>
				</el-button-group>
			</el-col>
		</el-row>
		<el-tabs v-model="selectTab1">
			<el-tab-pane label='基本信息' name='basicInformation' ref="isFormReadDisabledInput">
				<el-row :gutter="20">
					<el-col :span="6">
						<el-form-item label="单据编号">
							<el-input size='mini' :value="form.bill_no" disabled placeholder="系统自动生成" disabled></el-input>
						</el-form-item>
						<el-form-item label="售后单号">
							<el-input size='mini' :value="form.after_order_no" readonly></el-input>
						</el-form-item>
						<el-form-item label="合并订单号">
							<el-input size='mini' :value="form.merge_trade_no" readonly></el-input>
						</el-form-item>
						<el-form-item label="订单店铺" prop="shop_name">
							<el-input v-if="!isCanEditFormInfo || sysTradeId" v-model="form.shop_name" readonly size='mini'></el-input>
							<el-input v-else v-model="form.shop_name" size='mini' readonly icon="search" :on-icon-click="() => selectShop('shop_id', 'shop_name')"
								></el-input>
							<el-tooltip v-if='rules.shop_name[0].isShow' class="item" effect="dark" :content="rules.shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="收入店铺" prop="user_shop_name">
							<el-input v-if="!isCanEditFormInfo || sysTradeId" v-model="form.user_shop_name" readonly size='mini'></el-input>
							<el-input v-else v-model="form.user_shop_name" size='mini' readonly icon="search" :on-icon-click="() => selectShop('user_shop_id', 'user_shop_name')"
								></el-input>
							<!-- <el-tooltip v-if='rules.user_shop_name[0].isShow' class="item" effect="dark" :content="rules.user_shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip> -->
						</el-form-item>
						<el-form-item label="退款方式" prop="refund_way">
							<el-select v-model="form.refund_way" size="mini" placeholder="请选择"  @change="refundWayChange" :disabled="!isCanEditFormInfo" class="js_pmm">
								<el-option
									v-for="(val, key) in pay_method_options"
									:key="key"
									:label="val"
									:value="key"
									:disabled="false && ((/(PROTOCOL|DISPUTE)/.test(key)) && /(LS_SALE_ORDER|MERGE_TRADE)/.test(form.original_type) || !!params.taobaoDownload)"
								>
								<!-- 手工新增的退款申请单,退款方式禁掉协议退款和纠纷退款,淘宝下载列表跳转过来时退款方式不能修改其它 -->
								</el-option>
							</el-select>
							<el-tooltip v-if='rules.refund_way[0].isShow' class="item" effect="dark" :content="rules.refund_way[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="支付方式" prop="pay_type" :rules="{required: false}" style="display:none;">
							<xpt-select-aux v-model='form.pay_type' aux_name="payType" :disabled="!isCanEditFormInfo" class="js_pmm"></xpt-select-aux>
							<el-tooltip v-if='rules.pay_type[0].isShow' class="item" effect="dark" :content="rules.pay_type[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="收款账号" prop="receiver_account" class="js_pmm">
							<el-input v-if="!isCanEditFormInfo || /(PROTOCOL|DISPUTE)/.test(form.refund_way)" v-model="form.receiver_account" size='mini' readonly></el-input>
							<el-input v-else v-model="form.receiver_account" size='mini' icon="search" :on-icon-click="selectReceiverAccount" ></el-input>
							<el-tooltip v-if='rules.receiver_account[0].isShow' class="item" effect="dark" :content="rules.receiver_account[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="开户行" prop="opening_bank" class="js_pmm">
							<el-input size='mini' v-model="form.opening_bank" :disabled="!isCanEditFormInfo" ></el-input>
							<el-tooltip v-if='rules.opening_bank[0].isShow' class="item" effect="dark" :content="rules.opening_bank[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>

					</el-col>

					<el-col :span="6">
						<el-form-item label="业务员" prop="saleman">
							<el-input v-if="!isCanEditFormInfo || sysTradeId" v-model="form.saleman" readonly size='mini'></el-input>
							<el-input v-else v-model="form.saleman" size='mini' icon="search" :on-icon-click="selectBestStaff" readonly></el-input>
							<el-tooltip v-if='rules.saleman[0].isShow' class="item" effect="dark" :content="rules.saleman[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="业务员分组">
							<el-input size='mini' v-model="form.saleman_group" readonly></el-input>
							<!-- <el-tooltip v-if='rules.saleman_group[0].isShow' class="item" effect="dark" :content="rules.saleman_group[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip> -->
						</el-form-item>
						<el-form-item label="买家昵称" prop="nick_name">
							<el-input v-if="!isCanEditFormInfo || sysTradeId" v-model="form.nick_name" readonly size='mini'></el-input>
							<el-input v-else v-model="form.nick_name" size='mini' icon="search" :on-icon-click="selectBuyersName" readonly></el-input>
							<el-tooltip v-if='rules.nick_name[0].isShow' class="item" effect="dark" :content="rules.nick_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="原始店铺" prop="original_shop_name">
							<el-input v-if="!isCanEditFormInfo || sysTradeId" v-model="form.original_shop_name" readonly size='mini'></el-input>
							<el-input v-else v-model="form.original_shop_name" size='mini' readonly icon="search" :on-icon-click="() => selectShop('original_shop_id', 'original_shop_name')"
								></el-input>
							<el-tooltip v-if='rules.original_shop_name[0].isShow' class="item" effect="dark" :content="rules.original_shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<!-- <el-form-item label="业务备注" :class="$style['textarea-style']" prop="remark">
							<el-input type="textarea" v-model="form.remark" :maxlength="255" :disabled="!isCanEditFormInfo" class="js_pmm"></el-input>
							<el-tooltip v-if='rules.remark[0].isShow' class="item" effect="dark" :content="rules.remark[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item> -->
						<el-form-item label="收款人姓名" prop="receiver_name" class="js_pmm">
							<el-input size='mini' v-model="form.receiver_name"  :disabled="!isCanEditFormInfo" ></el-input>
							<el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="银行名称" prop="bank_name" class="js_pmm">
							<el-input size='mini' v-model="form.bank_name"  :disabled="!isCanEditFormInfo" ></el-input>
							<el-tooltip v-if='rules.bank_name[0].isShow' class="item" effect="dark" :content="rules.bank_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<!-- <el-form-item label="收款人电话" prop="receiver_phone" class="js_pmm">
							<el-input size='mini' v-model="form.receiver_phone" :disabled="!isCanEditFormInfo"></el-input>
							<el-tooltip v-if='rules.receiver_phone[0].isShow' class="item" effect="dark" :content="rules.receiver_phone[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item> -->
					</el-col>

					<el-col :span="6">
						<el-form-item label="来源类型">
							<el-input size='mini' :value="{
						        RETURNS_PLAN: '退货方案',
						        RETURNS_BILL: '退货跟踪单',
						        REFUND_PLAN: '退款方案',
						        LS_SALE_ORDER: '林氏销售订单',
						        MERGE_TRADE: '合并订单',
						        REFUND_APPLY_BILL: '退款申请单',
							}[form.original_type]" readonly></el-input>
						</el-form-item>
						<el-form-item label="来源单据号" prop="original_no">
							<el-input size='mini' v-model="form.original_no" readonly></el-input>
							<el-tooltip v-if='rules.original_no[0].isShow' class="item" effect="dark" :content="rules.original_no[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="创建人">
							<el-input size='mini' v-model="form.creator_name" readonly></el-input>
						</el-form-item>
						<el-form-item label="创建日期">
							<el-date-picker
								v-model="form.create_time"
							    type="datetime"
							    size="mini"
							    readonly
							></el-date-picker>
						</el-form-item>
						<el-form-item label="业务状态">
							<el-input size='mini' :value="{ WAIT_SUBMIT: '待提交', SUBMIT_FINANCE: '提交财务', REJECTED: '驳回', }[form.business_status]" readonly></el-input>
						</el-form-item>
						<el-form-item label="业务锁定人">
							<el-input size='mini' v-model="form.business_locker_name" readonly></el-input>
						</el-form-item>
						<el-form-item label="退款状态">
							<el-input size='mini' :value="{ UNREFUND: '未退款', REFUNDED: '已退款' }[form.refund_status]" readonly></el-input>
						</el-form-item>
						<el-form-item label="作废状态">
							<el-input size='mini' :value="{ NORMAL: '未作废', CANCELLED: '已作废' }[form.cancel_status]" readonly></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="是否经销商订单">
							<el-switch on-text="是" off-text="否" :value="{'Y':true,'N':false,'':false,'null': false,}[form.if_dealer]"  disabled></el-switch>
						</el-form-item>
						<el-form-item label="经销商编码:">
							{{form.dealer_customer_number}}

							<!-- <el-input size='mini' disabled :value="form.dealer_customer_number"></el-input> -->
						</el-form-item>
						<el-form-item label="经销商名称:">
							<!-- <el-input size='mini' disabled :value="form.dealer_customer_name"></el-input> -->
							{{form.dealer_customer_name}}
						</el-form-item>
						<el-form-item label="驳回备注" :class="$style['textarea-style']">
							<el-input type="textarea" v-model="form.reject_remark" :maxlength="255" :autosize="{ maxRows: 4 }" disabled class="js_pmm"></el-input>
							<!-- <el-tooltip v-if='rules.remark[0].isShow' class="item" effect="dark" :content="rules.remark[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip> -->
						</el-form-item>
						<el-form-item label="币别">
							<el-select v-model="form.currency" size="mini" placeholder="请选择" :disabled="!isCanEditFormInfo">
								<el-option
									v-for="(value,key) in currency_options"
									:key="key.toString()"
									:label="value.toString()"
									:value="key.toString()">
								</el-option>
							</el-select>
						</el-form-item>
				</el-col>
				</el-row>


			</el-tab-pane>
			<el-tab-pane label='淘宝申请退款信息' name='taobaoList'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="18">
						<el-button type="danger" size="mini" @click="delTaobaoListSelect" :disabled="!taobaoListSelect.length">删除行</el-button>
						<el-button type="primary" size="mini" @click="introduceFun" :disabled="!form.id || !isCanEditFormInfo">引入</el-button>
					</el-col>
				</el-row>
				<div style="height: 200px;">
					<el-table :data="form.taobaoList" border tooltip-effect="dark" style="width: 100%;" width='100%' :class="$style['max-table-height']" @selection-change="s => taobaoListSelect = s">
						<el-table-column type="selection" width="50" :selectable="() => isCanEditFormInfo"></el-table-column>
						<el-table-column label="买家昵称" 			prop="nick_name" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="淘宝退款金额" 		prop="refund_amount" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="淘宝退款状态" 		prop="taobao_refund_status" show-overflow-tooltip width="150">
							<template slot-scope="scope">
								{{ taobao_refund_status_options[scope.row.taobao_refund_status] }}
							</template>
						</el-table-column>
						<el-table-column label="退款方式" 			prop="refund_way" show-overflow-tooltip width="150">
							<template slot-scope="scope">
								{{ pay_method_options[scope.row.refund_way] }}
							</template>
						</el-table-column>
						<el-table-column label="淘宝退款单号" 		prop="taobao_refund_no" show-overflow-tooltip width="150">
							<template slot-scope="scope">
								<a href="javascript:;" @click="toTaobaoRefund(scope.row.taobao_refund_no)">{{ scope.row.taobao_refund_no }}</a>
							</template>
						</el-table-column>
						<el-table-column label="淘宝店铺" 			prop="taobao_shop_name" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="申请日期"			prop="apple_time" show-overflow-tooltip width="150">
							<template slot-scope="scope">{{ scope.row.apple_time | dataFormat1 }}</template>
						</el-table-column>
						<el-table-column label="退款超时时间" 		prop="overtime" show-overflow-tooltip width="150">
							<template slot-scope="scope">{{ scope.row.overtime | dataFormat1 }}</template>
						</el-table-column>
						<el-table-column label="最后更新时间" 		prop="last_modify_time" show-overflow-tooltip width="150">
							<template slot-scope="scope">{{ scope.row.last_modify_time | dataFormat1 }}</template>
						</el-table-column>
						<el-table-column label="小二介入" 			prop="cs_status" show-overflow-tooltip width="150">
							<template slot-scope="scope">
								{{ cs_status_options[scope.row.cs_status] }}
							</template>
						</el-table-column>
						<el-table-column label="小二介入时间" 		prop="cs_interpose_time" show-overflow-tooltip width="150">
							<template slot-scope="scope">{{ scope.row.cs_interpose_time | dataFormat1 }}</template>
						</el-table-column>
						<el-table-column label="支付宝账号" 		prop="alipay_account" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="推荐处理人"			prop="recommend_handler_name" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="淘宝单号" 			prop="taobao_order_no" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="退款原因" 			prop="refund_reason" show-overflow-tooltip width="150"></el-table-column>
						<el-table-column label="阿里掌柜订单类型" 	prop="tb_order_type" show-overflow-tooltip width="150"></el-table-column>
					</el-table>
				</div>
			</el-tab-pane>
		</el-tabs>
	</el-form>
	<el-row class="xpt-flex__bottom" v-fold>
		<el-tabs v-model="selectTab2" class="xpt-flex__bottom" @tab-click="getOperateLogList">
			<el-tab-pane label='退款明细' name='refoudDetail' class='xpt-flex'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="24">
						<el-button
							type="primary"
							size="mini"
							@click="addRefoudDetailListLine"
							:disabled="!isCanEditFormInfo"
						>新增行</el-button>
						<el-button
							type="danger"
							size="mini"
							@click="delRefoudDetailListLine"
							:disabled="!isCanEditFormInfo"
						>删除行</el-button>
					</el-col>
				</el-row>
				<div class="xpt-flex__bottom scroll">
					<el-table :data="form.refundItemList.length ? form.refundItemList.concat(totalObj) : []" @selection-change="selRefoudDetailListLine" border tooltip-effect="dark" style="width: 100%;" width='100%'>
						<el-table-column  type="selection" width="55" align="center" :selectable="(row, index) => index < form.refundItemList.length"></el-table-column>
						<el-table-column label="序号" type="index" width="50">
							<template slot-scope="scope">
								<div class="table-index">
									{{ scope.$index < form.refundItemList.length ? scope.$index + 1 : '' }}
								</div>
							</template>
						</el-table-column>
						<el-table-column label="退款类型" prop="refund_type" width="120">
							<template slot-scope="scope">
								<span v-if="!isCanEditFormInfo || scope.row.if_edit === 'N'">{{ refoudDetailList_type_options[scope.row.refund_type] || refoudDetailList_type_options2[scope.row.refund_type] }}</span>
								<el-select
									v-else-if="scope.$index < form.refundItemList.length"
									v-model="scope.row.refund_type"
									size="mini"
									style="width:100%;"
									placeholder="请选择"
								>
									<el-option
										v-for="(value, key) in refoudDetailList_type_options"
										:key="key.toString()"
										:label="value.toString()"
										:value="key.toString()"
										:disabled="
											!/(CANCEL|OVERPAY|PREFERENTIAL|DISCOUNT|DELAY)/.test(key) && /(LS_SALE_ORDER|MERGE_TRADE|RETURNS_BILL)/.test(form.original_type)
											|| (/(PRICE_DIFF|O2O_DIFF)/.test(key) && !form.priceDiffList.length)
											|| key === 'RETURNS'
										"
									>
									<!--
										1.手工新增的退款申请单或退货跟踪单,退款类型控制只允许选择：未发取消、多付、优惠返现、外购折现、延迟赔付
										2.当差价商品页签为空时不能选差价/O2O跨店铺差价类型
										3.先总是禁掉退货货款
									-->
									</el-option>
								</el-select>
							</template>
						</el-table-column>
						<el-table-column label="退款原因" prop="refund_reason" show-overflow-tooltip>
							<template slot-scope="scope">
								<span v-if="!isCanEditFormInfo">{{ scope.row.refund_reason }}</span>
								<el-input
									v-else-if="scope.$index < form.refundItemList.length"
									v-model="scope.row.refund_reason"
									size='mini'
									:maxlength="255"
									style="width:100%;"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column label="申请金额" prop="apply_amount" width="120">
							<template slot-scope="scope">
								<span v-if="!isCanEditFormInfo">{{ scope.row.apply_amount }}</span>
								<el-input
									v-else-if="scope.$index < form.refundItemList.length"
									size='mini'
									type="number"
									style="width:100%;"
									:value="scope.row.apply_amount"
									@input.native="e => checkApplyAmountFirst(e, scope.row)"
								></el-input>
								<span v-else>{{ scope.row.apply_amount }}</span>
							</template>
						</el-table-column>
						<el-table-column label="扣费"     prop="deduct_fee" width="120">
							<template slot-scope="scope">
								<span v-if="!isCanEditFormInfo">{{ scope.row.deduct_fee }}</span>
								<el-input
									v-else-if="scope.$index < form.refundItemList.length"
									size='mini'
									type="number"
									style="width:100%;"
									v-model.number="scope.row.deduct_fee"
									@input.native="_calcTotalPrice"
									:disabled="!/^(OVERPAY|RETURNS)$/.test(scope.row.refund_type)"
								></el-input><!-- 只有退货货款、多付时才可以编辑扣费 -->
								<span v-else>{{ scope.row.deduct_fee }}</span>
							</template>
						</el-table-column>
						<el-table-column label="实退金额" prop="actual_amount" width="120"></el-table-column>
					</el-table>
				</div>
			</el-tab-pane>
			<el-tab-pane label="商品信息" name="goodsInfo" class='xpt-flex'>
				<xpt-list
					:data='goodsList'
					:colData='goodsCols'
					selection='hidden'
					:showHead='false'
					orderNo
				></xpt-list>
			</el-tab-pane>
			<el-tab-pane label="对账明细" name="accountDetail" class="xpt-flex">
				<xpt-list
					:data='accountList'
					:colData='accountCols'
					:showHead='false'
					selection='hidden'
				></xpt-list>
			</el-tab-pane>
			<el-tab-pane label='差价商品' name='chajiaDetail' class='xpt-flex'>
				<div class="xpt-flex__bottom scroll">
					<el-table
						:data="form.priceDiffList && form.priceDiffList.length ? form.priceDiffList.concat('') : form.priceDiffList"
						border
						tooltip-effect="dark"
						style="width: 100%;"
						width='100%'
					>
						<el-table-column label="序号" type="index" width="50">
							<template slot-scope="scope"><div class="table-index">{{ scope.$index < form.priceDiffList.length ? scope.$index + 1 : '' }}</div></template>
						</el-table-column>
						<el-table-column label="价格区间" 	  	prop="price_area" width="150" show-overflow-tooltip></el-table-column>
						<el-table-column label="店铺" 			prop="shop" width="130" show-overflow-tooltip></el-table-column>
						<el-table-column label="活动商品编码" 	prop="new_goods_code" width="130" show-overflow-tooltip></el-table-column>
						<el-table-column label="活动商品名称"   prop="new_goods_name" show-overflow-tooltip></el-table-column>
						<el-table-column label="活动规格描述" 	prop="new_specification" width="180" show-overflow-tooltip></el-table-column>
						<el-table-column label="商品编码" 		prop="old_goods_code" width="130" show-overflow-tooltip></el-table-column>
						<el-table-column label="商品名称" 		prop="old_goods_name" show-overflow-tooltip></el-table-column>
						<el-table-column label="规格描述"		prop="old_specification" width="180" show-overflow-tooltip></el-table-column>
						<el-table-column label="拍下价格" 		prop="old_price" width="90" show-overflow-tooltip>
							<template slot-scope="scope">
								{{ scope.$index < form.priceDiffList.length ? scope.row.old_price : ('合计' + form.priceDiffList.reduce((a, b) => a + (Number(b.old_price) || 0), 0)) }}
							</template>
						</el-table-column>
						<el-table-column label="活动价" 		prop="new_price" show-overflow-tooltip>
							<template slot-scope="scope">
								{{ scope.$index < form.priceDiffList.length ? scope.row.new_price : ('合计' + form.priceDiffList.reduce((a, b) => a + (Number(b.new_price) || 0), 0)) }}
							</template>
						</el-table-column>
						<el-table-column label="淘宝单号" 		prop="order_no" width="150" show-overflow-tooltip></el-table-column>
						<el-table-column label="订单状态" 		prop="order_status" show-overflow-tooltip>
							<template slot-scope="scope">
								{{ order_status_options[scope.row.order_status] }}
							</template>
						</el-table-column>
						<el-table-column label="差价" 			prop="diff_price" width="90" show-overflow-tooltip>
							<template slot-scope="scope">
								{{ scope.$index < form.priceDiffList.length ? scope.row.diff_price : ('合计' + form.priceDiffList.reduce((a, b) => a + (Number(b.diff_price) || 0), 0)) }}
							</template>
						</el-table-column>
						<el-table-column label="购买时间" 		prop="buy_time" width="180" show-overflow-tooltip>
							<template slot-scope="scope">
								<span>{{scope.row.buy_time | dataFormat1}}</span>
							</template>
						</el-table-column>
						<el-table-column label="单位" 			prop="units" show-overflow-tooltip></el-table-column>
						<el-table-column label="生效日期" 		prop="valid_date" width="150" show-overflow-tooltip>
							<template slot-scope="scope">
								<span>{{scope.row.valid_date | dataFormat1}}</span>
							</template>
						</el-table-column>
						<el-table-column label="失效日期" 		prop="disabled_date" width="150" show-overflow-tooltip>
							<template slot-scope="scope">
								<span>{{scope.row.disabled_date | dataFormat1}}</span>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</el-tab-pane>
			<el-tab-pane label='操作记录' name='chaozuoDetail' class='xpt-flex'>
				<div class="xpt-flex__bottom scroll">
					<el-table :data="operateLogList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
						<el-table-column label="序号" type="index" width="70">
							<template slot-scope="scope"><div class="table-index">{{ scope.$index + 1 }}</div></template>
						</el-table-column>
						<el-table-column label="用户" 		prop="operator_name" width="200" show-overflow-tooltip></el-table-column>
						<el-table-column label="业务操作" 	prop="operate_type" width="200" show-overflow-tooltip>
							<template slot-scope="scope">
								{{ operate_type_options[scope.row.operate_type] || scope.row.operate_type }}
							</template>
						</el-table-column>
						<el-table-column label="操作描述" 	prop="description" width="400" show-overflow-tooltip></el-table-column>
						<el-table-column label="操作时间"   prop="operate_time" width="200" show-overflow-tooltip>
							<template slot-scope="scope">
								<span>{{scope.row.operate_time | dataFormat1}}</span>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</el-tab-pane>
		</el-tabs>
	</el-row>
	<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload>
</div>
</template>
<script>
import VL from '@common/validate.js'
import fn from '@common/Fn.js'
import account from '../order/merge/account.js'
import goods from '../order/merge/goods.js'

export default {
	mixins: [account, goods],
	props: ['params'],
	data (){
		return {
			ifClickUpload: false,
			uploadData: {},
			selectTab1: 'basicInformation',
			selectTab2: 'refoudDetail',
			isCanEditFormInfo: true,//默认新增退款申请单时能编辑
			someCanEdit: false,
			selectRefoudDetailLists: [],
			taobaoListSelect: [],
			operateLogList: [],
			sysTradeId: '',//用于选收款账号入参
			cust_id: '',//客户id
			totalObj: {
				apply_amount: '合计0',
				deduct_fee: '合计0',
				actual_amount: '合计0',
			},

			merge_trade_id: '',
			addNewBtn:true,//控制新增按钮，如果页面没加载完之前，按钮给其置灰

			currency_options: Object.assign(fn.getCurrency(), { RMB: '人民币' }),//币别选项
			pay_method_options: {
				ALIPAY	: '支付宝退款',
				BANK	: '银行卡退款',
				PROTOCOL: '协议退款',
				DISPUTE : '纠纷退款',
				PAIPAI	: '拍拍退款',
				TENPAY	: '财付通退款',
				BAIL	: '保证金退款',
				B2C_ONLINE	: 'B2C线上退款',
				B2C_OFFLINE	: 'B2C线下退款',
				CARRY_OVER  : '退款结转',
        YSF: '云闪付退款'
			},
			refoudDetailList_type_options2: {//额外翻译的
				DELIVERY_FEE: '延迟赔付',
				COMPENSATION: '补偿',
				THREE_FEE: '三包费',
				O2O_PRICE_DIFF: 'O2O跨店铺差价',
				DISCOUNTING: '外购折现',
			},
			refoudDetailList_type_options: {
				CANCEL: '未发取消',
				OVERPAY: '多付',
				PREFERENTIAL: '优惠/返现',
				DELAY: '延迟赔付',
				COMPENSATION: '补偿',
				REPAIR: '维修费',
				RETURNS: '退货货款',
				CARRIAGE: '运费',
				THREE: '三包费',
				O2O_DIFF: 'O2O跨店铺差价',
				PRICE_DIFF: '差价',
				DISCOUNT: '外购折现',
			},
			cs_status_options: {//小二介入
				1: '不需客服介入',
				2: '需要客服介入',
				3: '客服已经介入',
				4: '客服初审完成',
				5: '客服主管复审失败',
				6: '客服处理完成',
			},
			taobao_refund_status_options: {
				WAIT_SELLER_AGREE: '买家已经申请退款，等待卖家同意',
				WAIT_BUYER_RETURN_GOODS: '卖家已经同意退款，等待买家退货',
				WAIT_SELLER_CONFIRM_GOODS: '买家已经退货，等待卖家确认收货',
				SELLER_REFUSE_BUYER: '卖家拒绝退款',
				CLOSED: '退款关闭',
				SUCCESS: '退款成功',
			},
			operate_type_options: {
				CREATE: '新增',
				SAVE: '保存',
				LOCK: '锁定',
				UNLOCK: '解锁',
				SUBMIT: '提交',
				RETRACT: '撤回',
				AUDIT: '审核',
				CHANGE: '确认变更',
				REVERSE_AUDIT: '反审核',
				SUBMIT_APPROVE: '提交审批',
				PASS: '审批通过',
				NOT_PASS: '审批不通过',
				TRACK: '执行跟踪',
				RECALL: '撤回仓储',
				CLOSE: '关闭',
				OPEN: '反关闭',
				CANCEL: '已取消',
				//extra
				ADD_REFUND_ITEM: '引入退款明细',
				DELETE_REFUND_ITEM: '删除退款明细',
				ADD_TAOBAO_REFUND: '引入淘宝退款申请',
				DELETE_TAOBAO_REFUND: '删除淘宝退款申请',
				SUBMITPURCHASE: '提交采购',
				PLAN_RETRACT: '方案撤回',
				REJECT: '驳回',
				LOCKER: '锁定',
				REVOCATION: '撤回',
				VERIFY: '审核',
				OPPOSITE_VERIFY: '反审核',
				SUBMIT_EXAMINE: '提交审批',
				PASS_EXAMINE: '审批通过',
				UNPASS_EXAMINE: '审批不通过',
			},
			order_status_options: {
				'CANCEL': '取消',
				'NORMAL': '生效',
				'WAITING': '等待发运',
				'PART_DELIVERED': '部分发运中',
				'DELIVERED': '已发运'
			},

			initForm: {},//在this.mounted时设置等于this.form值，用于表单重置
			form: {
				creator_name: fn.getUserInfo('fullName'),
				creator: fn.getUserInfo('creator'),
				business_locker_id: this.getEmployeeInfo('id'),
				business_locker_name: this.getEmployeeInfo('fullName'),
				create_time: new Date,
				refund_status: 'UNREFUND',//退款状态
				cancel_status: 'NORMAL',//作废状态
				currency: 'CNY',
				remark: '',
				pay_type: '',
				receiver_account: '',
				opening_bank: '',
				receiver_name: '',
				bank_name: '',
				refund_way: '',//退款方式
				original_type: 'LS_SALE_ORDER',//来源类型
				// receiver_phone: null,
				business_status: 'WAIT_SUBMIT',//业务状态
				dealer_customer_id:null,//经销商Id
				if_dealer:null,//是否经销商订单(Y是/N否)
				dealer_customer_number:null,//经销商编码
				dealer_customer_name:null,//经销商名称
				dealer_customer_id:null,//经销商ID
				refundItemList: [{
					refund_type: '',
					refund_reason: '',
					apply_amount: 0,
					deduct_fee: 0,
					actual_amount: '',
					if_edit: 'Y',
				}],
				taobaoList: [],
				priceDiffList: [],
			},
			rules: {
				// receiver_phone	: VL.mobile({
				// 	required: true,
				// 	self: this,
				// 	msg: '请填写正确收款人电话',
				// }),
				receiver_name: [{
					required: true,
					validator: (rule, value, callback) => {
						//PROTOCOL: '协议退款',DISPUTE : '纠纷退款',CARRY_OVER  : '退款结转',收款人姓名和收款账号不用必填
						if(!/(PROTOCOL|DISPUTE|CARRY_OVER)/.test(this.form.refund_way) && !value || value && /^\d+$/.test(value.trim())){
							this.rules[rule.field][0].message = !value ? '请填写收款人姓名' : '收款人姓名不能为纯数字'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写收款人姓名',
				}],
				bank_name: [{
					required: false,
					validator: (rule, value, callback) => {
						if(this.form.refund_way === 'BANK' && !value || (value && /^\d+$/.test(value.trim()))){
							this.rules[rule.field][0].message = !value ? '请填写银行名称' : '银行名称不能为纯数字'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写正确银行名称',
				}],
				opening_bank: [{
					required: false,
					validator: (rule, value, callback) => {
						if(this.form.refund_way === 'BANK' && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写开户行',
				}],
				refund_way: [{
					required: true,
					validator: (rule, value, callback) => {
						if(!value || Object.keys(this.pay_method_options).indexOf(value) === -1){
							this.rules[rule.field][0].message = !value ? '请填写退款方式' : '退款方式不符合条件'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写退款方式',
				}],
				original_no: [{
					required: true,
					validator: (rule, value, callback) => {
						// 来源单据号是必填的，所以不通过选单的是建不了申请单
						if(/(LS_SALE_ORDER|MERGE_TRADE)/.test(this.form.original_type) && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '来源单据号不能为空',
				}],
				receiver_account: [{
					required: true,
					validator: (rule, value, callback) => {
						var reg = /^1\d{10}$/;
						var reg2 = /^\d{8}$/;
						var reg3 = /^\d*$/;

						//PROTOCOL: '协议退款',DISPUTE : '纠纷退款',CARRY_OVER  : '退款结转',收款人姓名和收款账号不用必填
						if(!/(PROTOCOL|DISPUTE|CARRY_OVER)/.test(this.form.refund_way) && !value){
							this.rules[rule.field][0].message = '请填写收款账号'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else if (this.form.refund_way === 'BANK' && !/^\d+$/.test(value)){
							this.rules[rule.field][0].message = '收款账号只能为纯数字'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						/*}else if (this.form.refund_way === 'ALIPAY' && !(/^1[34578]\d{9}$/.test(value) || /\S+@\S+\.\S+/.test(value))){*/
						/*}else if (this.form.refund_way === 'ALIPAY' && (!(reg.test(value) && !(reg2.test(value)) || /\S+@\S+\.\S+/.test(value))){*/
						}else if (this.form.refund_way === 'ALIPAY' && !(reg3.test(value) || /\S+@\S+\.\S+/.test(value))){
							this.rules[rule.field][0].message = '收款账号只能为邮箱或手机号码';
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写收款账号',
				}],
				...[{
					saleman: '业务员'
				}/*,{
					saleman_group: '业务员分组'
				}*/,{
					nick_name: '买家昵称'
				},{
					original_shop_name: '原始店铺'
				},{
					remark: '业务备注'
				},{
					shop_name: '订单店铺'
				},{
					user_shop_name: '收入店铺'
				},{
					pay_type: '支付方式'
				}].reduce((a, b) => {
					var key = Object.keys(b)[0]
					a[key] = VL.isNotBlank({
						self: this,
						msg: '请填写' + b[key],
					})
					return a
				}, {})
			},
		}
	},
	methods: {
		getOperateLogList (){
			if(this.selectTab2 === 'chaozuoDetail' && this.form.id){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.form.id }, res => {
					if(res.body.result){
						this.operateLogList = res.body.content || []
					}
				})
			}
		},
		// 淘宝申请退款信息----删除按钮
		delTaobaoListSelect (){
			var _successCb = () => {
				this.taobaoListSelect.forEach(obj => {
					this.form.taobaoList.splice(this.form.taobaoList.indexOf(obj), 1)
				})
				this.taobaoListSelect.length = 0
			}

			if(this.form.id){
				this.ajaxFunc('deleteTaobaoRefund', this.taobaoListSelect.map(obj => obj.id), res => {
					_successCb()
				})
			}else {
				_successCb()
			}
		},
		// 淘宝申请退款信息----引入按钮
		introduceFun() {
			this.ajaxFunc('addTaobaoRefund', res => {
				this.form.taobaoList = res.body.content.taobaoList
			})
		},
		// 作废按钮
		cancelOrder (){
			this.$root.eventHandle.$emit('openDialog', {
				txt: '是否确认作废？',
				okTxt: '确认',
				noShowNoTxt: true,
				ok: () => {
					this.ajaxFunc('cancel?permissionCode=REFUND_APPLY_ORDER_CANCEL', () => this.getOrderDetail())
				},
			})
		},
		// 淘宝申请退款信息页签的淘宝退款单号跳转详情
		toTaobaoRefund (taobao_refund_no){
			var searchFunc = () => {
				this.toTaobaoRefund.where[0].value = taobao_refund_no
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/list?permissionCode=TAOBAO_REFUND_ORDER_QUERY', {
					"page_name": "aftersale_taobao_refund_download",
					"page_size": 50,
					"page_no": 1,
					"where": this.toTaobaoRefund.where,
				}, res => {
					if(res.body.result){
						if(res.body.content.list && res.body.content.list[0]){
							this.$root.eventHandle.$emit('creatTab', {
								name: '退款申请单淘宝下载查看',
								params: { after_refund_download_id: res.body.content.list[0].after_refund_download_id },
								component: ()=> import('@components/after_sales_refund/taobaoDownloadDetail.vue')
							})
						}else {
							this.$message.error('找不到该退款申请单淘宝下载详情')
						}
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}

			if(this.toTaobaoRefund.where){
				searchFunc()
			}else {
				this.ajax.postStream('/user-web/api/sql/listFields', { 'page': 'aftersale_taobao_refund_download' }, res => {
					res.body.content.fields.some(obj => {
						if(obj.comment === '淘宝退款单号'){
							this.toTaobaoRefund.where = [{
								condition: 'AND',
								field: obj.field,
								listWhere: [],
								operator: '=',
								table: obj.table,
								value: '',
							}]
							searchFunc()
							return true
						}
					})
				})
			}
		},
		refundWayChange (){
			;[{
				name: ['opening_bank', 'bank_name'],
				test: /BANK/.test(this.form.refund_way),
			},{
				name: ['receiver_account', 'receiver_name'],
				test: !/(PROTOCOL|DISPUTE|CARRY_OVER)/.test(this.form.refund_way),//PROTOCOL: '协议退款',DISPUTE : '纠纷退款',CARRY_OVER  : '退款结转',收款人姓名和收款账号不用必填
			}].forEach(obj => {
				obj.name.forEach(name => {
					this.rules[name][0].required = obj.test
				})
			})
		},
		// 新增按钮
		addNewOrder (){
			this.$message.error('请到新退款申请单列表新增')
			return

			this._refresh(null, null, () => {
				this.isCanEditFormInfo = true
				this.sysTradeId = ''
				this.cust_id = ''
				this.totalObj = {
					apply_amount: '合计0',
					deduct_fee: '合计0',
					actual_amount: '合计0',
				}

				//解除这个限制：手工新增的退款申请单,退款方式禁掉协议退款和纠纷退款,淘宝下载列表跳转过来时退款方式不能修改其它
				if(this.params.taobaoDownload)
					this.params.taobaoDownload = null

				// this._isFormReadDisabledInput(false,true)
				this._refresh(null, this.form = Object.assign({}, this.initForm))
			})
			// this.$root.eventHandle.$emit('creatTab',{
			// 	name: '退款申请单',
			// 	params: {
			// 		status: 'CREATE'
			// 	},
			// 	component:()=> import('@components/after_sales_refund/refundRequest_2')
			// })
		},
		checkApplyAmountFirst (e, row){
			var $input = e.target

			if(!$input._oldVal) $input._oldVal = Number(row.apply_amount)

			window.clearTimeout(this.checkApplyAmountFirst._clearTimer)
			this.checkApplyAmountFirst._clearTimer = setTimeout(() => {
				if(0/*去掉这个判断，由后端控制*/ && this.form.original_type == 'RETURNS_BILL' && $input.value > $input._oldVal){
					this.$message.error('来源类型是退货跟踪单时，申请金额只能改小')
					$input.value = Number(row.apply_amount)
				}else {
					$input.value = row.apply_amount = Number($input.value)
					this._calcTotalPrice()
				}
			}, 300)
		},
		_selectMergeOrderInit (cb){
			this.ajax.postStream("/user-web/api/sql/listFields",{page:'scm_sys_trade'}, res => {
				res.body.content.fields.forEach(obj => {
					if(/(审核状态|单据状态)/.test(obj.comment)){
						obj.values.some(val => {
							if(val.name === '已审核'){
								this._selectMergeOrderInit.audit_status_where = [{
								    "field": obj.field,
								    "table": obj.table,
								    "value": val.code,
								    "operator": "=",
								    "condition": "AND",
								    "listWhere": []
								}]
								return true
							}
						})
					}else if(/(客户昵称|买家昵称)/.test(obj.comment)){
						this._selectMergeOrderInit.customer_name_where = [{
						    "field": obj.field,
						    "table": obj.table,
						    "value": '',
						    "operator": "=",
						    "condition": "AND",
						    "listWhere": []
						}]
					}
				})
				cb()
		    })
		},
		// 选单
		selectMergeOrder() {
			this.$root.eventHandle.$emit('alert',{
				title:'销售订单列表',
				style:'width:900px;height:500px',
				component:()=>import('@components/order/list_alert'),
				params: {
					otherSearchParams: {
						where: this._selectMergeOrderInit.audit_status_where,
						page_name: 'scm_sys_trade',
					},
					callback: d => {
						this.sysTradeId = true
						if(this.cust_id !== d.customer_id){
							this.cust_id = d.customer_id
							this.form.receiver_account = ''
						}

						this.form.shop_id = d.shop_id
						this.form.saleman_id = d.user_id
						this.form.user_shop_id = d.user_shop_id
						this.form.original_shop_id = d.original_shop_id

						//解除这个限制：手工新增的退款申请单,退款方式禁掉协议退款和纠纷退款,淘宝下载列表跳转过来时退款方式不能修改其它
						if(this.params.taobaoDownload) {
							this.form.taobaoList = []
							this.form.refund_way = ''
							this.form.original_type = 'LS_SALE_ORDER'
							this.params.taobaoDownload = null
						}

						if(this.merge_trade_id = this.form.merge_trade_id = d.merge_trade_id){
							this.getGoodsList()
							this.getAccountList()
						}


						this.$set(this.form, 'original_no', d.sys_trade_no)
						this.$set(this.form, 'merge_trade_no', d.merge_trade_no)
						this.$set(this.form, 'shop_name', d.shop_name)//订单店铺
						this.$set(this.form, 'saleman', d.user_name)//业务员
						this.$set(this.form, 'saleman_group', d.group_name)//分组
						this.$set(this.form, 'nick_name', d.customer_name)
						this.$set(this.form, 'user_shop_name', d.user_shop_name)
						this.$set(this.form, 'original_shop_name', d.original_shop_name)
					}
				},
			})
		},
		// 提交财务、作废、删除、保存、读取详情
		ajaxFunc (apiName, postData, cb, errCb){
			if(/^(save|submitFinance)/.test(apiName) && (this.totalObj.apply_amount.match(/(\d|\.)+/)[0] <= 0 || this.form.refundItemList.some(obj => obj.actual_amount < 0))){
				this.$message.error(this.totalObj.apply_amount.match(/(\d|\.)+/)[0] <= 0 ? '申请金额合计必须要大于0' : '每一行实退金额不能小于0')
				errCb && errCb()
				return
			}

			this.addNewBtn = true;
			if(!postData || Object.prototype.toString.call(postData) === '[object Function]'){
				cb = postData
				postData = { id: this.form.id }
			}

			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApply/' + apiName, postData, res => {
				if(res.body.result){
					cb && cb(res)
					if(apiName === 'cancel') res.body.msg = '退款申请单作废成功'//原本接口返回“获取退款申请单成功”
					this.$message.success(res.body.msg)
				}else {
					errCb && errCb()
					this.$message.error(res.body.msg)
				}
			}, err => {
				errCb && errCb()
			})
		},
		// 保存
		save (e, cb){
			var $btn = e.currentTarget;
			this.addNewBtn = true;
			this.$refs.form.validate(valid => {
	          	if (valid){
	          		this._checkRefoudDetailListRequired()

	          		this.form.refund_bill_id = 0//接口报错，暂时性处理方法

	          		$btn.disabled = true
					this.ajaxFunc('save?permissionCode=REFUND_APPLY_ORDER_SAVE', this.form, res => {
						this.form.id = res.body.content
						if(cb){
							cb(() => {
				          		$btn.disabled = false
							})
						}else {
							this.getOrderDetail(() => {
				          		$btn.disabled = false
				          		setTimeout(() => {
					          		this.$message.success('退款申请单保存成功')
				          		})
							})
						}
					}, () => {
		          		$btn.disabled = false
						this.addNewBtn = false
					})
	          	}else {
	          		this.addNewBtn = false;
	          	}
	        })
		},
		setStatus(){
			/*
			基本编辑权限:
				业务锁定人 = 操作人
			不能编辑条件:
				当作废状态 = 已作废 or 退款状态 = 已退款 or 业务状态 = 提交财务
			额外可以编辑条件:
				来源类型 = 退货跟踪单
			 */

			this.someCanEdit =
				this.form.cancel_status !== 'CANCELLED'
				&& this.form.refund_status !== 'REFUNDED'
				&& this.form.business_status !== 'SUBMIT_FINANCE'
			this.isCanEditFormInfo =
				!this.form.id ||
				(
					this.form.business_locker_id === this.getEmployeeInfo('id')
					&& this.someCanEdit
				)
			// let isCurrentPerson = !this.form.bill_no?true:this.form.creator === this.getEmployeeInfo('id');//创建人 = 操作人才有编辑权限
			// this.isCancel = !isCurrentPerson?true:this.form.cancel_status === 'CANCELLED' || this.form.refund_status == 'REFUNDED';//已退款的情况下
			// this.isFormRead = !isCurrentPerson?true:this.form.original_type !== 'LS_SALE_ORDER' || this.form.business_status == 'SUBMIT_FINANCE' || this.isCancel;
			// this.isCanEditFormInfo = !isCurrentPerson?false:this.form.original_type == 'RETURNS_BILL' && /WAIT_SUBMIT|REJECTED/.test(this.form.business_status)/*驳回和待提交作用一样*/ && this.form.refund_way !== 'CARRY_OVER' && !this.isCancel;
		},
		getOrderDetail (cb){
			this.ajaxFunc('getRefundApplyBill', res => {
				this.form = res.body.content || {};
				this.setStatus();
				this.getcustIdByName(this.form.nick_name)
				this.addNewBtn = false;
				console.log('看看是啥东西');
				// this._isFormReadDisabledInput(this.isFormRead,this.isCanEditFormInfo)
				//if(this.isFormRead) this._isFormReadDisabledInput(true)

				if(this.merge_trade_id = this.form.merge_trade_id){
					this.getGoodsList()
					this.getAccountList()
				}

				cb && cb()
				this._calcTotalPrice()//里面代码setTimeout执行，所以下面_refresh保存旧数据时也要先setTimeout
				setTimeout(() => {
					this._refresh(null, this.form)
				})
			})
		},
		getcustIdByName (customer_name){
			this._selectMergeOrderInit.customer_name_where[0].value = customer_name
			this.ajax.postStream('/order-web/api/scmsystrade/list', {
				"page_name": "scm_sys_trade",
				"page_size": 20,
				"page_no": 1,
				"where": this._selectMergeOrderInit.customer_name_where,
			}, res => {
				this.cust_id = (res.body.content.list[0] || {}).customer_id
			})
		},
		addRefoudDetailListLine (){
			var list = this.form.refundItemList
			list.push({
				deduct_fee: 0,
				apply_amount: 0,
				if_edit: 'Y',
			})
			this.$set(list[list.length - 1], 'refund_type', '')
		},
		selRefoudDetailListLine (list){
			this.selectRefoudDetailLists = list
		},
		delRefoudDetailListLine (){
			if(!this.selectRefoudDetailLists.length){
				this.$message.error('请选择一行退款明细')
				return
			}

			var list = this.form.refundItemList
			this.selectRefoudDetailLists.forEach(obj => {
				list.splice(list.indexOf(obj), 1)
			})
			this._calcTotalPrice()
		},
		// 选收款账号
		selectReceiverAccount (){
			this.$root.eventHandle.$emit('alert',{
				title:'支付信息列表',
				style:'width:800px;height:560px',
				component:()=>import('@components/after_sales_refund/selectReceiverAccount'),
				params: {
					cust_id: this.cust_id || '',
					callback: d => {
						this.form.receiver_account = d.pay_account
						if(d.bank){
							this.form.opening_bank = d.bank
						}
					}
				},
			})
		},
		// 附件操作
		uploadAction (method){
			if(method === 'post'){
				this.ifClickUpload = true
				this.uploadData = {
					parent_name: 'AFTER_ORDER',
					parent_no: this.form.after_order_no || this.form.bill_no,
					child_name : 'REFUNDAPPLYBILLDETAIL',
					child_no : this.form.bill_no,
					content: JSON.parse(JSON.stringify(this.form || {})),
				}
				setTimeout(() => {
					this.ifClickUpload = false
				}, 100)
			}else {
				this.$root.eventHandle.$emit('alert', {
					params: {
						parent_name : 'AFTER_ORDER',
						parent_name_txt: this.form.after_order_no ? '售后单号' : '退款申请单号',
						parent_no : this.form.after_order_no || this.form.bill_no,
						child_name : 'REFUNDAPPLYBILLDETAIL',
						child_no : null,
						// child_name_txt: '商品问题id',
						ext_data : null,
						// callback: files => {
						// 	this.listAfterMaterialVO[index].attachment_count = files.length
						// },
					},
					component: ()=>import('@components/common/download.vue'),
					style: 'width:80%;height:600px',
					title: '下载列表',
				})
			}
		},
		// 买家昵称
		selectBuyersName (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/customers/list'),
				style:'width:900px;height:600px',
				title: '买家昵称列表',
				params: {
					close: d => {
						if(this.cust_id !== d.cust_id){
							this.cust_id = d.cust_id
							this.form.receiver_account = ''
						}
						this.$set(this.form, 'nick_name', d.name)
					}
				},
			})
		},
		// 业务员选择
		selectBestStaff (title){
			this.$root.eventHandle.$emit('alert',{
				title: '业务员列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/after_sales_common/selectPickrecommendhandler'),
				params: {
					callback: d => {
						this.form.saleman_id = d.id

						this.$set(this.form, 'saleman', d._real_name_nick_name)//业务员
						this.$set(this.form, 'saleman_group', d.group_name)//分组
					}
				},
			})
		},
		// 订单店铺选择
		selectShop (idName, valName){
			this.$root.eventHandle.$emit('alert',{
				title: '店铺列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/shop/list.vue'),
				params: {
					selection: 'radio',
					callback: d => {
						this.$set(this.form, valName, d.shop_name)
						this.form[idName] = d.shop_id
					}
				},
			})
		},
		// 刷新按钮
		_refresh: function (){
			var _oldData
			return function (e, oldData, cb){
				if(oldData){
					_oldData = JSON.parse(JSON.stringify(oldData))

					return oldData
				}else {
					var _closeOrRefresh = () => {
						if(e){//刷新按钮
							if(this.form.id){
								this.getOrderDetail()
							}else {
								this.form = JSON.parse(JSON.stringify(_oldData))
							}
						}else if (cb){
							cb()
						}else {//页签关闭事件
							this.$root.eventHandle.$emit('removeTab', this.params.tabName)
						}
					}

					if(this.compareData(JSON.parse(JSON.stringify(this.form)), _oldData)){
						this.$root.eventHandle.$emit('openDialog', {
							ok: e => {
								this.save(e)
							},
							no: _closeOrRefresh
						})
					}else {
						_closeOrRefresh()
					}
				}
			}
		}(),
		_isFormReadDisabledInput (isDisabled,isCanEditFormInfo){
			;[].forEach.call(this.$refs.isFormReadDisabledInput.$el.querySelectorAll('.el-input'), $dom => {
				let dom = $($dom).parents('.js_pmm');
				console.log('dom.size()',dom.size());
				if(dom.size()){
					console.log('faksdlfjasdlfjfjkasldkfjasldkf');
					let className = isCanEditFormInfo?'remove':isDisabled?'add':'remove';
					$dom.classList[className]('is-disabled');
					$dom.querySelector('input').disabled = isCanEditFormInfo?false:isDisabled;
				}else{
					$dom.classList[isDisabled ? 'add' : 'remove']('is-disabled')
					$dom.querySelector('input').disabled = isDisabled;
				}


			})
		},
		_checkRefoudDetailListRequired (){
			var msg = '请添加至少一条退款明细'

			this.form.refundItemList.some((obj, index) => {
				msg = ''
				if(!obj.refund_type){
					msg = '退款类型不能为空'
				}else if(!obj.refund_reason){
					msg = '退款原因不能为空'
//				}else if (obj.apply_amount <= 0 || !obj.apply_amount){
//					msg = '	申请金额要大于0'
				}else if(obj.deduct_fee < 0){
					msg = '扣费不能小于0'
				}
				if(msg) {
					msg = '退款明细-第' + (index + 1) + '行-' + msg
					return true
				}
			})
			if(msg){
				this.$message.error(msg)
				throw(msg)
			}
		},
		// 计算退款明细合计
		_calcTotalPrice (){
			var list = this.form.refundItemList
			,	totalObj = {
				apply_amount: 0,
				deduct_fee: 0,
				actual_amount: 0,
			}

			setTimeout(() => {
				list.forEach((obj, index) => {
					obj.apply_amount = obj.apply_amount > 0 ? Number(obj.apply_amount.toFixed(2)) : 0
					obj.deduct_fee = obj.deduct_fee > 0 ? Number(obj.deduct_fee.toFixed(2)) : 0
					obj.actual_amount = Number(((obj.apply_amount || 0) - (obj.deduct_fee || 0)).toFixed(2))

					totalObj.apply_amount += obj.apply_amount || 0
					totalObj.deduct_fee    += obj.deduct_fee || 0
					totalObj.actual_amount += obj.actual_amount
				})
				totalObj.apply_amount = '合计' + totalObj.apply_amount.toFixed(2)
				totalObj.deduct_fee = '合计' + totalObj.deduct_fee.toFixed(2)
				totalObj.actual_amount = '合计' + totalObj.actual_amount.toFixed(2)
				this.totalObj = totalObj
			})
		},
	},
	watch: {
		'form.original_type': function (){
			this.rules.original_no[0].required = /(LS_SALE_ORDER|MERGE_TRADE)/.test(this.form.original_type)
		},
	},
	mounted (){
		this.initForm = JSON.parse(JSON.stringify(this.form))//比this.getOrderDetail()先执行

		//从退款申请单淘宝下载列表跳转过来
		if(this.params.taobaoDownload){
			Object.keys(this.params.taobaoDownload).forEach(key => {
				if(this.params.taobaoDownload[key]){
					this.$set(this.form, key, this.params.taobaoDownload[key])
				}
			})
			this._calcTotalPrice()
		}

		if(this.merge_trade_id = this.form.merge_trade_id){
			this.getGoodsList()
			this.getAccountList()
		}

		this._selectMergeOrderInit(() => {
			if(this.params.id){
				this.form.id = this.params.id
				this.getOrderDetail()
			}else {
				this.addNewBtn = false;
				this._refresh(null, this.form);

			}
		})
		this.refundWayChange()
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
			this.setStatus();
		});
		this.params.__close = this._refresh
	},
}
</script>

<style module>
.textarea-style :global(.el-form-item__content) {
	/*width: 180px;*/
    height: auto;
}
.max-table-height :global(.el-table__body-wrapper) {
  max-height: inherit;
}
</style>
