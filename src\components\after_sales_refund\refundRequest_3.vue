<!-- 新退款申请单 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="20">
				<el-button type="primary" size="mini" @click="addNewOrder" :disabled="!!params.snapshotObj || refundNomalDisabled
					">新增</el-button>
				<el-button type="success" size="mini" @click="handleSave" :loading="saveLoading" :disabled="!(
						!form.billRefundApplication.id
						|| (
							isSomeLockPerson
							&& /^(REJECT)$/.test(form.billRefundApplication.business_status)
						)
            || (
							isSameBusinessLockPerson
							&& /^(CREATE)$/.test(form.billRefundApplication.business_status)
						)
						|| (
							isSameFinanceLockPerson
							&& form.billRefundApplication.if_finance_lock === 'Y'
						)
						|| (
							form.billRefundApplication.business_status === 'CHECK'
							&& form.billRefundApplication.if_review_lock === 'Y'
							&& isSamereviewLockPerson
						)
					)
					|| saveLoading
          || orderDetailLoading
					|| cantEditWhenPersonBusinessAttribute || refundNomalDisabled
					">保存</el-button>
				<el-button type="primary" size="mini" @click="_refresh" :disabled="!!params.snapshotObj || refreshLoading"
					:loading="refreshLoading">刷新</el-button>
				<el-button type="primary" size="mini" @click="selectMergeOrder" :disabled="!!form.billRefundApplication.id
					|| form.billRefundApplicationItem.length !== 0
					|| !!form.billRefundApplication.merge_trade_no_target
					|| refundNomalDisabled
					">选单</el-button>
				<el-button type="primary" size="mini" @click="takenLock" :disabled="!(
						form.billRefundApplication.id
						&& /^(CREATE|REJECT)$/.test(form.billRefundApplication.business_status)
						&& /^(null|N)$/.test(form.billRefundApplication.if_business_lock)
						&& !form.billRefundApplication.business_lock_person
					)
					|| cantEditWhenPersonBusinessAttribute || refundNomalDisabled
					">业务锁定</el-button>
				<el-button type="primary" size="mini" @click="unLockBtn" :disabled="!(
						/^(CREATE|REJECT)$/.test(form.billRefundApplication.business_status)
						&& form.billRefundApplication.if_business_lock === 'Y'
						&& isSameBusinessLockPerson
					)
					|| businessUNLockLoading || refundNomalDisabled
					" :loading="businessUNLockLoading">业务解锁</el-button>
				<el-button type="primary" size="mini" :disabled="!(
						(
              (
							isSomeLockPerson
							&& /^(REJECT)$/.test(form.billRefundApplication.business_status)
              ) || (
                isSameBusinessLockPerson
                && /^(CREATE)$/.test(form.billRefundApplication.business_status)
              )
            )
						&& form.billRefundApplication.if_business_lock === 'Y'
            && testBtn
					)
					|| submitLoading || refundNomalDisabled || orderDetailLoading
					" :loading="submitLoading" @click="submitCheck">提交初审</el-button>
				<el-button type="primary" size="mini"
					@click="ajaxFunc(_delPermissionCodeWhenDealerUser('recall'), res => getOrderDetail(res.body.msg))" :disabled="!(
							isSameBusinessLockPerson
							&& form.billRefundApplication.if_business_lock === 'Y'
							&& form.billRefundApplication.business_status === 'SUBMIT'
							&& /^(null|N)$/.test(form.billRefundApplication.if_finance_lock)
						) || refundNomalDisabled
						">撤回</el-button>
				<span style="color: #9c9898;">&nbsp;&nbsp;|&nbsp;&nbsp;</span>
				<el-button type="success" size="mini" :disabled="rejectDisabled" @click="rejectRefund">驳回</el-button>
				<el-button type="primary" size="mini"
					@click="ajaxFunc('financeUNLock', [form.billRefundApplication.id], res => getOrderDetail(res.body.msg))"
					:disabled="!(
							isSameFinanceLockPerson
							&& form.billRefundApplication.business_status === 'SUBMIT'
							&& form.billRefundApplication.if_finance_lock === 'Y'
						) || refundNomalDisabled
						">初审解锁</el-button>
				<el-button type="primary" size="mini" :loading="isCheckLoading" @click="() => {
					refreshLoading = true
					ajaxFunc(
						_delPermissionCodeWhenDealerUser(
							'check',
							!/^(DISPUTE|BAIL|PROTOCOL)$/.test(form.billRefundApplication.refund_way)
						),
						[form.billRefundApplication.id],
						res => {
							refreshLoading = false
							getOrderDetail(res.body.msg)
						},
						err => {
							refreshLoading = false
						}
					)
				}" :disabled="!(
							isSameFinanceLockPerson
							&& form.billRefundApplication.business_status === 'SUBMIT'
							&& form.billRefundApplication.if_finance_lock === 'Y'
						) || refundNomalDisabled || isCheckLoading
						">核对</el-button>
				<el-button type="primary" size="mini" @click="batchUpdate('reviewUNLock')" :disabled="!(
						isSamereviewLockPerson
						&& /^(TMALL|B2C|PDD|JD|VIP|SUNING|YJ)$/.test(form.billRefundApplication.original_type)
						&& form.billRefundApplication.business_status === 'CHECK'
						&& form.billRefundApplication.if_review_lock === 'Y'
					) || refundNomalDisabled
					">复核解锁</el-button>
				<el-button type="primary" size="mini" :loading="isAuditRefundLoading" @click="onCheckUnlock" :disabled="!(
							form.billRefundApplication.review_lock_person === getEmployeeInfo('id')
							&& form.billRefundApplication.business_status === 'CHECK'
							&& form.billRefundApplication.if_review_lock === 'Y'
							&& refundOriginTypeCodeList.includes(form.billRefundApplication.original_type)

						) || refundNomalDisabled || isAuditRefundLoading

						">复核退款</el-button>
				<el-button type="primary" size="mini" @click="recheckOrder" :disabled="!getCancelDisabled">反审</el-button>
				<el-button type="primary" size="mini" @click="cancelOrder" :disabled="!(
						form.billRefundApplication.id
						&& /^(CREATE|REJECT)$/.test(form.billRefundApplication.business_status)
					) || refundNomalDisabled
					">作废</el-button>
				<el-button-group>
					<el-button type="primary" @click="uploadAction('post')" size="mini" :disabled="!form.billRefundApplication.id
						|| !!params.snapshotObj
						|| cantEditWhenPersonBusinessAttribute || refundNomalDisabled
						">上传</el-button>
					<el-button type="primary" @click="uploadAction('get')" size="mini" :disabled="!form.billRefundApplication.id
						|| !!params.snapshotObj
						|| cantEditWhenPersonBusinessAttribute || refundNomalDisabled
						">查看附件</el-button>
				</el-button-group>
				<el-button type="danger" size="mini" @click="cancelCarryOver" :disabled="!form.billRefundApplication.id
					|| refundNomalDisabled
					|| ! /^(FINISH)$/.test(form.billRefundApplication.business_status)
					">取消结转</el-button>
				<el-button type="primary" size="mini" @click="updateFinish"
					:disabled="!(form.billRefundApplication.business_status == 'CHECK' && form.billRefundApplication.refund_way == 'PROTOCOL') || refundNomalDisabled">修改已退款</el-button>
				<el-button type="primary" size="mini" @click="supplementaryPaymentDetails"
					:disabled="refundNomalDisabled">预售订单添加支付明细</el-button>
				<el-button type="primary" size="mini" @click="dealerMoneyCheck">经销财账核对</el-button>
				<el-button type="danger" size="mini" @click="forceInvalid" :loading="forceInvalidLoading">强制作废</el-button>
				<el-button type="primary" size="mini" @click="updateShopRefundSource"
					:loading="updateShopRefundSourceLoading">是否林氏退款</el-button>
				<el-button type="primary" size="mini" @click="manualRefundAutoSubmit" :loading="manualRefundAutoSubmitLoading"
					:disabled="form.taobaoRefundDownload.refund_id && ['CANCELED', 'FINISH'].includes(form.billRefundApplication.business_status) || manualRefundAutoSubmitLoading">自动提交</el-button>
				<el-button type="primary" size="mini" @click="modificationOfApplicationAmount"
					:loading="modificationOfApplicationAmountLoading"
					:disabled="modificationOfApplicationAmountLoading">修改客户申请金额</el-button>
				<el-button type="primary" size="mini" @click="updateRefundBusinessStatus">修改业务状态</el-button>
        <!-- #BHWL-265 抖音来客需要特殊处理 -->
				<el-button type="primary" size="mini" @click="omitDownload" :disabled="!refundShopCodes.includes(form.shop_code)&&form.billRefundApplication.original_type !== 'DYLK'">漏单下载</el-button>
				<el-select v-model="progressLabel" placeholder="进度标签" size='mini' style="width:165px;margin-left:16px">
					<el-option v-for="item in progressLabelOptions" :key="item.code" :label="item.name" @click.native="setSigns"
						:value="item.code">
					</el-option>
				</el-select>
			</el-col>
			<el-col :span="4" style="text-align: right;">
				<el-button type="primary" size="mini" @click="nextOrPrevOrder('prev')"
					:disabled="!!params.snapshotObj">上一页</el-button>
				<el-button type="primary" size="mini" @click="nextOrPrevOrder('next')"
					:disabled="!!params.snapshotObj">下一页</el-button>
			</el-col>
		</el-row>
		<el-form :model="form.billRefundApplication" :rules="rules" ref='form' label-position="right" label-width="110px">
			<el-tabs v-model="selectTab1" @tab-click="handleTabClick">
				<el-tab-pane label='基本信息' name='basicInformation'>
					<el-row :gutter="20">
						<el-col :span="6">
							<el-form-item label="单据编号">
								<el-input size='mini' :value="form.billRefundApplication.bill_no" disabled
									placeholder="系统自动生成"></el-input>
							</el-form-item>
							<el-form-item label="合并订单号" prop="merge_trade_no">
                <a
                  v-if="form.billRefundApplication.merge_trade_no && form.billRefundApplication.merge_trade_id"
                  href='javascript:;'
                  @click="onOpenMergeTradeDetail"
                >
                  {{ form.billRefundApplication.merge_trade_no }}
                </a>
                <el-input v-else size='mini' v-model="form.billRefundApplication.merge_trade_no" disabled></el-input>
								<el-tooltip v-if='rules.merge_trade_no[0].isShow' class="item" effect="dark"
									:content="rules.merge_trade_no[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="订单店铺" prop="shop_name">
								<el-input v-if="canBuildNoSourceOrder" v-model="form.billRefundApplication.shop_name" size='mini'
									readonly icon="search" :on-icon-click="() => selectShop('shop_id', 'shop_name')"></el-input>
								<el-input v-else :value="form.billRefundApplication.shop_name" disabled size='mini'></el-input>
								<el-tooltip v-if='rules.shop_name[0].isShow' class="item" effect="dark"
									:content="rules.shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="客服店铺" prop="user_shop_name">
								<el-input v-if="canBuildNoSourceOrder" v-model="form.billRefundApplication.user_shop_name" size='mini'
									readonly icon="search" :on-icon-click="() => selectShop('user_shop_id', 'user_shop_name')"></el-input>
								<el-input v-else :value="form.billRefundApplication.user_shop_name" disabled size='mini'></el-input>
								<el-tooltip v-if='rules.user_shop_name[0].isShow' class="item" effect="dark"
									:content="rules.user_shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="是否林氏退款">
								<el-switch on-text="是" off-text="否" on-value="Y" off-value="N" disabled
									v-model="form.billRefundApplication.if_ls_refund"></el-switch>
							</el-form-item>
						</el-col>
						<el-col :span="5">
							<el-form-item label="业务状态">
								<el-input size='mini' :value="business_status_options[form.billRefundApplication.business_status]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="买家昵称" prop="nick_name">
								<el-input v-if="canBuildNoSourceOrder" v-model="form.billRefundApplication.nick_name" icon="search"
									:on-icon-click="selectBuyersName" size='mini' readonly></el-input>
								<el-input v-else :value="form.billRefundApplication.nick_name" disabled size='mini'></el-input>
								<el-tooltip v-if='rules.nick_name[0].isShow' class="item" effect="dark"
									:content="rules.nick_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="原始店铺" prop="original_shop_name">
								<el-input v-if="canBuildNoSourceOrder" v-model="form.billRefundApplication.original_shop_name"
									size='mini' readonly icon="search"
									:on-icon-click="() => selectShop('original_shop_id', 'original_shop_name')"></el-input>
								<el-input v-else :value="form.billRefundApplication.original_shop_name" size='mini' disabled></el-input>
								<el-tooltip v-if='rules.original_shop_name[0].isShow' class="item" effect="dark"
									:content="rules.original_shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="退款方式" prop="refund_way">
								<el-select v-model="form.billRefundApplication.refund_way" size="mini" placeholder="请选择" :disabled="!isCanEdit
									|| form.billRefundApplicationItem.length > 0
									|| form.billRefundApplication.original_type !== 'OTHER'
									|| (!!form.billRefundApplication.merge_trade_no_target && form.billRefundApplication.refund_way === 'CROSS_OVER_T')
									|| form.billRefundApplication.is_import == 'Y'
									" class="js_pmm">
									<!-- 采用click.native方法而不用change事件是因为首次加载详情和触发change事件，正常是不应该触发的 -->
									<el-option v-for="(val, key) in pay_method_options" :key="key" :label="val" :value="key"
										@click.native="refundWayChange(form.billRefundApplication.refund_way)" :disabled="(form.billRefundApplication.original_type === 'OTHER' && key === 'B2C_ONLINE')
											|| (form.billRefundApplication.original_type === 'OTHER' && key === 'PROTOCOL' && !refund_type_option['PROTOCOL'].test(form.billRefundApplication.shop_id))
											|| (form.billRefundApplication.original_type === 'OTHER' && key === 'DISPUTE' && !refund_type_option['DISPUTE'].test(form.billRefundApplication.shop_id))
											|| (key !== 'CARRY_OVER' && form.billRefundApplicationItem.some(o => o.refund_type === 'RETURNS'))
											|| (key === 'CARRY_OVER' && form.billRefundApplicationItem.some(o =>
												!/^(RETURNS|CROSS_OVER)$/.test(o.refund_type) || o.refund_type === 'RETURNS' && o.xentry_id
											))
											|| (
												key === 'CROSS_OVER_T' && (
													!form.billRefundApplication.merge_trade_no
													|| form.billRefundApplicationItem.some(o => o.refund_type !== 'OVERPAY')
													|| form.billRefundApplication.if_cross_refund === 'Y'
												)
											)
											|| (
												key === 'DEALER_PREFERENTIAL' && (
													form.billRefundApplication.original_type !== 'OTHER'
													|| form.billRefundApplicationItem.some(o => o.refund_type !== 'PREFERENTIAL')
												)
											)
											|| (form.billRefundApplication.if_dealer == 'N' && key === 'DEALER_PREFERENTIAL')
											">
										<!--
										手工新增的退款申请单时：
											1、退款方式要禁掉B2C线上退款
											2、非苏宁，贝店、蜜芽店、考拉、京东自营的订单店铺时，协议退款禁掉
											3、非京东(**************)、唯品会(30506000859136)、苏宁(30505857990656)、贝店(30419619823616)、拼多多(23965176315904)、蜜芽店(58425898975232)、林氏木业京东拼购店(**************)林氏木业云集旗舰店 (73436358656001)考拉(82716566110213)京东自营（115456873725955）时，纠纷退款也禁掉
										保证跨单结转对应的退款明细行只能有多付
										经销优惠退款只能是手工退款才能选
									-->
									</el-option>
								</el-select>
								<el-tooltip v-if='rules.refund_way[0].isShow' class="item" effect="dark"
									:content="rules.refund_way[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="结算方式">
								<el-select v-model="form.billRefundApplication.settle_method" size="mini" placeholder="请选择" disabled
									class="js_pmm">
									<el-option key="WANGPAI_DEALER" label="经销网拍" value="WANGPAI_DEALER"></el-option>
									<el-option key="DEALER" label="经销" value="DEALER"></el-option>
									<el-option key="OTHER" label="其它" value="OTHER"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="驳回原因" prop="reject_reason">
								<xpt-select-aux v-model='form.billRefundApplication.reject_reason' aux_name='sh_reject_reason'
									:disabled="rejectDisabled"></xpt-select-aux>
							</el-form-item>
						</el-col>
						<el-col :span="1" style="height:1px;"></el-col>
						<el-col :span="5" :class="$style['width220']">
							<el-form-item label="来源类型">
								<el-input size='mini' :value="original_type_options[form.billRefundApplication.original_type]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="来源单据号">
                <a
                  v-if="form.billRefundApplication.original_no && form.billRefundApplication.sys_trade_id"
                  href='javascript:;'
                  @click="onOpenSysTradeDetail"
                >
                  {{ form.billRefundApplication.original_no }}
                </a>
								<el-input v-else size='mini' v-model="form.billRefundApplication.original_no" disabled></el-input>
								<el-tooltip v-if='rules.original_no[0].isShow' class="item" effect="dark"
									:content="rules.original_no[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="是否跨单退款">
								<el-switch on-text="是" off-text="否" on-value="Y" off-value="N"
									v-model="form.billRefundApplication.if_cross_refund" :disabled="(form.billRefundApplication.refund_way === 'CARRY_OVER' || (/^(DISPUTE|PROTOCOL)$/.test(this.form.billRefundApplication.refund_way) && this.form.billRefundApplication.original_type === 'TMALL')) ? false : (form.billRefundApplication.refund_way === 'CROSS_OVER_T' || !!form.billRefundApplication.merge_trade_no_target || form.billRefundApplication.original_type === 'OTHER' || !(/^(DISPUTE|PROTOCOL)$/.test(this.form.billRefundApplication.refund_way) && this.form.billRefundApplication.original_type === 'TMALL'))
										" @click.native="ifCrossRefundChange"></el-switch><!-- 采用click.native方法而不用change事件是因为首次加载详情和触发change事件，正常是不应该触发的 -->
							</el-form-item>
							<el-form-item label="目标合并订单" prop="merge_trade_no_target">
								<!-- 不可编辑的状态 -->
								<el-input v-if="!isCanEdit || !!getUserPersonBusinessAttributeFlag"
									v-model="form.billRefundApplication.merge_trade_no_target" size='mini' disabled></el-input>
								<!-- 退款方式不为跨单转结且是跨单退款 -->
								<el-input
									v-else-if="form.billRefundApplication.refund_way !== 'CROSS_OVER_T' && form.billRefundApplication.if_cross_refund !== 'Y'"
									v-model="form.billRefundApplication.merge_trade_no_target" size='mini' disabled></el-input>
								<!--目标订单号 非空，未定义或者非空串  -->
								<el-input v-else-if="!!form.billRefundApplication.merge_trade_no_target"
									v-model="form.billRefundApplication.merge_trade_no_target" size='mini' readonly icon="close"
									:on-icon-click="mergeTradeNoTargetClear"></el-input>
								<el-input v-else v-model="form.billRefundApplication.merge_trade_no_target" size='mini' readonly
									icon="search" :on-icon-click="() => addRefundType('CROSS_OVER')"></el-input>
								<el-tooltip v-if='rules.merge_trade_no_target[0].isShow' class="item" effect="dark"
									:content="rules.merge_trade_no_target[0].message" placement="right-start"
									popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="结算主体">
								<el-input size='mini' disabled :value="form.billRefundApplication.settle_entity"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="1" style="height:1px;"></el-col>
						<el-col :span="6" :class="$style['width90']">
							<el-form-item label="退款账号" prop="receiver_account" class="js_pmm">
								<el-input
									v-if="!isCanEdit || /^(PROTOCOL|DISPUTE|B2C_ONLINE|CARRY_OVER)$/.test(form.billRefundApplication.refund_way)"
									v-model="form.billRefundApplication.receiver_account" size='mini' disabled></el-input>
								<el-input v-else v-model="form.billRefundApplication.receiver_account" size='mini' icon="search"
									:on-icon-click="() => selectReceiverAccount()"></el-input>
								<el-tooltip v-if='rules.receiver_account[0].isShow' class="item" effect="dark"
									:content="rules.receiver_account[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="退款人姓名" prop="receiver_name" class="js_pmm">
								<el-input size='mini' v-model="form.billRefundApplication.receiver_name"
									:disabled="!isCanEdit || /^(PROTOCOL|DISPUTE|B2C_ONLINE|CARRY_OVER)$/.test(form.billRefundApplication.refund_way)"></el-input>
								<el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark"
									:content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="银行名称" prop="bank_name" class="js_pmm">
								<el-input size='mini' v-model="form.billRefundApplication.bank_name" :disabled="true"></el-input>
								<el-tooltip v-if='rules.bank_name[0].isShow' class="item" effect="dark"
									:content="rules.bank_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="开户行" prop="opening_bank" class="js_pmm">
								<el-input size='mini' v-model="form.billRefundApplication.opening_bank"
									:disabled="!isCanEdit || /^(PROTOCOL|DISPUTE|B2C_ONLINE|CARRY_OVER)$/.test(form.billRefundApplication.refund_way)"
									readonly icon="search" :on-icon-click="() => bankListAlert()"></el-input>
								<el-tooltip v-if='rules.opening_bank[0].isShow' class="item" effect="dark"
									:content="rules.opening_bank[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="是否发运">
								<el-select v-model="form.billRefundApplication.if_deliver" size="mini" placeholder="请选择" disabled>
									<el-option key="Y" label="是" value="Y"></el-option>
									<el-option key="N" label="否" value="N"></el-option>
								</el-select>
							</el-form-item>
              <el-form-item label="政府补贴订单">
								<el-select v-model="form.billRefundApplication.government_subsidy_order" size="mini" placeholder="请选择" disabled>
									<el-option key="Y" label="是" value="Y"></el-option>
									<el-option key="N" label="否" value="N"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="6">
							<el-form-item label="用途">
								<el-select v-model="form.billRefundApplication.use_for_corss" size="mini" placeholder="请选择"
									:disabled="useForCorssDisabled" class="js_pmm">
									<el-option key="DELIVERY" label="发货" value="DELIVERY"></el-option>
									<el-option key="DRAWBACK" label="退款" value="DRAWBACK"></el-option>
								</el-select>
								<el-tooltip v-if='rules.use_for_corss[0].isShow' class="item" effect="dark"
									:content="rules.use_for_corss[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="驳回人">
								<el-input size='mini' :value="form.billRefundApplication.reject_person_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="驳回时间">
								<el-date-picker :value="form.billRefundApplication.reject_date" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span="18">
							<el-form-item label="驳回备注" :class="$style['textarea-style']">
								<el-input type="textarea" class="js_pmm" v-model="form.billRefundApplication.reject_remark"
									:maxlength="255" :autosize="{ maxRows: 4, minRows: 1 }" :disabled="rejectDisabled"></el-input>
							</el-form-item>
							<el-form-item label="财务备注" :class="$style['textarea-style']">
								<div v-if="form.billRefundApplication.if_finance_lock == 'Y'">
									<el-autocomplete
										:disabled="!(form.billRefundApplication.finance_lock_person == getEmployeeInfo('id') || form.billRefundApplication.review_lock_person == getEmployeeInfo('id'))"
										class="finance-input" type="textarea" v-model="form.billRefundApplication.finance_remark"
										:maxlength="255" :autosize="{ maxRows: 4, minRows: 1 }" :fetch-suggestions="querySearchAsync"
										@blur.stop="closeIconClick"></el-autocomplete>
									<div v-if="showBtn">
										<div>
											<el-button icon="check" size="mini" @click="sureChange"></el-button>
											<el-button icon="close" size="mini" @click="cancelChange"></el-button>
										</div>
									</div>
									<div style="margin-top:15px" v-else></div>
								</div>
								<el-input v-else type="textarea" v-model="form.billRefundApplication.finance_remark" :maxlength="255"
									:autosize="{ maxRows: 4, minRows: 1 }" class="js_pmm" :disabled="financeRemarkStatus"></el-input>
							</el-form-item>
							<el-form-item label="退款模式" prop="refund_mode" v-show="refoundModeShow">
                <xpt-select-aux
                  v-model="form.billRefundApplication.refund_mode"
                  aux_name="refund_mode"
                  :disabled="!refoundModeEditable || isJDZY"
                  class="js_pmm"
                  placeholder="请选择"
                  :disabledOption="[{code:'REFUND_VIOLATION_FORM'}]"
                ></xpt-select-aux>
							</el-form-item>
						</el-col>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label='平台退款申请' name='taobaoRefundDownloadInfo'>
					<el-row :gutter="20">
						<el-col :span="6">
							<el-form-item label="平台退款单号">
								<el-input :value="form.taobaoRefundDownload.refund_id" size='mini' disabled></el-input>
							</el-form-item>
							<el-form-item label="平台退款状态">
								<xpt-select-aux v-model='form.taobaoRefundDownload.status' aux_name='all_platform_refund_status'
									disabled></xpt-select-aux>

							</el-form-item>
							<el-form-item label="订单状态">
								<el-input size='mini' :value="form.taobaoRefundDownload.order_status" disabled></el-input>
							</el-form-item>
							<el-form-item label="平台单号">
								<el-input size='mini' :value="form.taobaoRefundDownload.tid" disabled></el-input>
							</el-form-item>
							<el-form-item label="平台子单号">
								<el-input size='mini' :value="form.taobaoRefundDownload.oid" disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="申请退款时间">
								<el-date-picker v-model="form.taobaoRefundDownload.goods_return_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="退款阶段">
								<el-input size='mini' :value="{ ONSALE: '售中', AFTERSALE: '售后' }[form.taobaoRefundDownload.refund_phase]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="客户申请金额">
								<el-input size='mini' :value="form.taobaoRefundDownload.refund_fee" disabled></el-input>
							</el-form-item>
							<el-form-item label="日期">
								<el-date-picker v-model="form.taobaoRefundDownload.create_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
              <el-form-item label="退货运单号">
								<el-input :value="form.taobaoRefundDownload.sid"  size="mini" disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="平台订单金额">
								<el-input size='mini' :value="form.taobaoRefundDownload.total_fee" disabled></el-input>
							</el-form-item>
							<el-form-item label="平台退款原因">
								<el-input size='mini' :value="form.taobaoRefundDownload.reason" disabled></el-input>
							</el-form-item>
							<el-form-item label="退款超时时间">
								<el-date-picker :value="form.taobaoRefundDownload.timeout" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="平台退款成功时间">
								<el-date-picker :value="form.taobaoRefundDownload.refund_success_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="下载来源">
								<el-input size='mini' :value="original_type_options[form.taobaoRefundDownload.come_from_type]"
									disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="小二介入">
								<el-input size='mini' :value="String(form.taobaoRefundDownload.cs_status) | auxFormat('csStatus')"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="小二介入时间">
								<el-date-picker v-model="form.taobaoRefundDownload.cs_interpose_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="更新时间">
								<el-date-picker v-model="form.taobaoRefundDownload.modified" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="分类">
								<el-input size='mini' :value="category_options[form.taobaoRefundDownload.category]" disabled></el-input>
							</el-form-item>
              <el-form-item label="纸质单号">
								<el-input size='mini' :value="form.billRefundApplication.external_order_no" disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="18">
							<el-form-item label="退款子账号" :class="$style['textarea-style']">
								<el-input type="textarea" v-model="form.taobaoRefundDownload.sub_account" :maxlength="255"
									:autosize="{ maxRows: 4, minRows: 1 }" disabled></el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label='其他信息' name='billRefundApplicationOtherInfo'>
					<el-row :gutter="20">
						<el-col :span="6">
							<el-form-item label="创建人">
								<el-input size='mini' :value="form.billRefundApplication.creator_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="创建日期">
								<el-date-picker v-model="form.billRefundApplication.create_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="业务员" prop="saleman_name">
								<el-input v-if="canBuildNoSourceOrder" size='mini' v-model="form.billRefundApplication.saleman_name"
									icon="search" :on-icon-click="selectBestStaff" readonly></el-input>
								<el-input v-else :value="form.billRefundApplication.saleman_name" size='mini' disabled></el-input>
								<el-tooltip v-if='rules.saleman_name[0].isShow' class="item" effect="dark"
									:content="rules.saleman_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="业务员分组">
								<el-input size='mini' :value="form.billRefundApplication.saleman_group_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="业务锁定">
								<el-input size='mini'
									:value="{ Y: '是', N: '否', null: '否', }[form.billRefundApplication.if_business_lock]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="业务锁定人">
								<el-input size='mini' :value="form.billRefundApplication.business_lock_person_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="业务锁定人分组">
								<el-input size='mini' :value="form.billRefundApplication.business_lock_group_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="业务锁定时间">
								<el-date-picker v-model="form.billRefundApplication.business_lock_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="推荐处理人">
								<el-input size='mini' :value="form.billRefundApplication.best_staff_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="推荐处理人分组">
								<el-input size='mini' :value="form.billRefundApplication.best_staff_group_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="是否离职">
								<el-input size='mini' :value="form.billRefundApplication.best_staff_status" disabled></el-input>
							</el-form-item>
							<el-form-item label="提交初审人">
								<el-input size='mini' :value="form.billRefundApplication.submit_finance_person_name"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="初审锁定">
								<el-input size='mini'
									:value="{ Y: '是', N: '否', null: '否', }[form.billRefundApplication.if_finance_lock]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="初审锁定人">
								<el-input size='mini' :value="form.billRefundApplication.finance_lock_person_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="初审锁定时间">
								<el-date-picker v-model="form.billRefundApplication.finance_lock_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="账号是否一致">
								<el-input size='mini' :value="{ Y: '是', N: '否', null: '', }[form.billRefundApplication.if_same]"
									disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="核对人">
								<el-input size='mini' :value="form.billRefundApplication.check_person_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="核对时间">
								<el-date-picker v-model="form.billRefundApplication.check_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="是否需要复核">
								<el-input size='mini' :value="{ Y: '是', N: '否' }[form.billRefundApplication.if_need_review]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="复核锁定">
								<el-input size='mini'
									:value="{ Y: '是', N: '否', null: '否', }[form.billRefundApplication.if_review_lock]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="复核锁定人">
								<el-input size='mini' :value="form.billRefundApplication.review_lock_person_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="复核锁定时间">
								<el-date-picker v-model="form.billRefundApplication.review_lock_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="复核退款时间">
								<el-date-picker v-model="form.billRefundApplication.review_refund_time" type="datetime" size="mini"
									disabled></el-date-picker>
							</el-form-item>
							<el-form-item label="同意退款失败原因">
								<el-input size='mini' :value="form.agree_refund_remark" disabled
									v-if="!form.agree_refund_remark"></el-input>
								<el-tooltip class="item" effect="dark" :content="form.agree_refund_remark" placement="top-start" v-else>
									<el-input size='mini' :value="form.agree_refund_remark" disabled></el-input>
								</el-tooltip>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="是否经销商订单">
								<el-input size='mini' :value="{ Y: '是', N: '否', null: '否', }[form.billRefundApplication.if_dealer]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="经销商编码">
								<el-input size='mini' :value="form.billRefundApplication.dealer_customer_number" disabled></el-input>
							</el-form-item>
							<el-form-item label="经销商名称">
								<el-input size='mini' :value="form.billRefundApplication.dealer_customer_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="是否上传附件">
								<el-input size='mini' :value="{ Y: '是', N: '否' }[form.billRefundApplication.if_exist_file]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="是否三个月售后">
								<el-input size='mini' :value="{ 1: '是', 0: '否' }[form.billRefundApplication.if_three_age]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="是否开启资料补充">
								<el-input size='mini' :value="{ Y: '是', N: '否' }[form.billRefundApplication.if_open_request]"
									disabled></el-input>
							</el-form-item>
							<el-form-item label="经销财账核对">
								<el-input size='mini' :value="{ Y: '是', N: '否' }[form.billRefundApplication.dealer_money_check]"
									disabled></el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label='费用登记' name='feeRegister'>
					<xpt-list :orderNo="true" :data='dataList' :colData='colData' :showHead='false' selection=' '>
					</xpt-list>
				</el-tab-pane>
				<el-tab-pane label='批次信息' name='batchInfo'>
					<xpt-list :data='scmBatchTradeList' :colData='scmBatchTradeCol' :btns="scmBatchBtn" selection='checkbox'
						@selection-change="handleScmBatchSelectionChange">
					</xpt-list>
				</el-tab-pane>
				<el-tab-pane label='售后信息' name='aftersaleOrder'>
					<xpt-list :data='aftersaleOrderList' :colData='aftersaleOrderCol' :btns="aftersaleOrderBtn" selection=' '>
					</xpt-list>
				</el-tab-pane>
        <el-tab-pane label="内部便签" name="innerTag">
          <inner ref='inner' :class="$style['inner-tag']" />
        </el-tab-pane>
			</el-tabs>
		</el-form>
		<el-row class="xpt-flex__bottom" v-fold>
			<el-tabs v-model="selectTab2" class="xpt-flex__bottom" @tab-click="selectTab2Click">
				<el-tab-pane label='退款明细' name='refoudDetail' class='xpt-flex'>
					<el-row class="xpt-top" :gutter="40">
						<el-col :span="24">
							<el-button type="danger" size="mini" :disabled="!isCanEdit" @click="delRefundType">删除行</el-button>
							<el-button type="primary" size="mini" :disabled="!isCanEdit||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('CANCEL')">未发取消</el-button>
							<el-button type="primary" size="mini" :disabled="!isCanEdit||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('OVERPAY')">多付</el-button>
							<el-button type="primary" size="mini" :disabled="!isCanEdit||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('OVER_DISCOUNT')">前置优惠</el-button>
							<el-button type="primary" size="mini" :disabled="!isCanEdit||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('PREFERENTIAL')">未前置优惠</el-button>
							<el-button type="primary" size="mini" :disabled="!isCanEdit||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('DELAY')">服务折让</el-button>
							<el-button type="primary" size="mini" :disabled="!isCanEdit"
								@click="addRefundType('COMPENSATION')">销售折让</el-button>
							<el-button type="primary" size="mini"
								:disabled="!isCanEdit || form.billRefundApplication.refund_way == 'CARRY_OVER_LB'||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('RETURNS')">退货货款</el-button>
							<el-button type="primary" size="mini"
								:disabled="!isCanEdit || form.billRefundApplication.refund_way == 'CARRY_OVER_LB'||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('REFUNDCARRYFORWARD')">换货结转</el-button>
							<el-button type="primary" size="mini"
								:disabled="!isCanEdit || form.billRefundApplication.refund_way == 'CARRY_OVER_LB'||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('CARRIAGE')">运费</el-button>
							<el-button type="primary" size="mini" :disabled="!isCanEdit||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="addRefundType('PRICE_DIFF')">差价</el-button>
							<el-button type="primary" size="mini" :disabled="!isCanEdit||form.billRefundApplication.refund_way=='OVERSEAS_AFTER_REFUND'"
								@click="checkIfIncomeCarry">收入结转后退款</el-button>
							<el-button type="primary" size="mini" @click="review('createRrfundAnalysis')">生成责任分析单</el-button>
							<el-button type="primary" size="mini" @click="review('cancelSubBill')">终止责任分析单</el-button>
							<el-button type="primary" size="mini" @click="modifyPlatformDiscount">修改平台优惠</el-button>
							<el-button type="primary" size="mini" @click="forceDeletePlatformDiscount">强制删除平台优惠明细</el-button>
						</el-col>
					</el-row>
					<div class="xpt-flex__bottom scroll">
						<el-table
							:data="newBillRefundApplicationItem = (form.billRefundApplicationItem.length ? form.billRefundApplicationItem.concat(totalObj) : [])"
							@selection-change="selRefoudDetailListLine" border tooltip-effect="dark" style="width: 100%;"
							ref="refundApplicationItemTable" :row-class-name="$style['row-height']" @select="refoudDetailSelect">
							<el-table-column type="selection" width="55" align="center"
								:selectable="(row, index) => index < newBillRefundApplicationItem.length - 1"></el-table-column>
							<el-table-column label="序号" type="index" width="50">
								<template slot-scope="scope">
									<div class="table-index">
										{{ scope.$index < newBillRefundApplicationItem.length - 1 ? scope.$index + 1 : '' }} </div>
								</template>
							</el-table-column>
							<el-table-column label="退款类型" prop="refund_type" width="100">
								<template slot-scope="scope">
									{{ refoudDetailList_type_options[scope.row.refund_type] ||
										refoudDetailList_type_options2[scope.row.refund_type] }}
									<!--
										1.手工新增的退款申请单或退货跟踪单,退款类型控制只允许选择：未发取消、多付、未前置优惠、外购折现、服务折让
										2.当差价商品页签为空时不能选差价/O2O跨店铺差价类型
										3.先总是禁掉退货货款
									-->
								</template>
							</el-table-column>
							<el-table-column label="申请金额" prop="apply_amount" width="110">
								<template slot-scope="scope">
									<span v-if="!isCanEdit">{{ scope.row.apply_amount }}</span>
									<el-input v-else-if="scope.$index < newBillRefundApplicationItem.length - 1" size='mini' type="number"
										style="width:100%;" :value="scope.row.apply_amount"
										@input.native="e => checkApplyAmountFirst(e, scope.row, 'apply_amount')"
										:disabled="scope.row.refund_type === 'PRICE_DIFF' || scope.row.refund_type === 'SINGLE_DISCOUNT' || scope.row.refund_type === 'PLATFORM_DISCOUNT'"></el-input>
									<span v-else>{{ scope.row.apply_amount }}</span>
								</template>
							</el-table-column>
							<el-table-column label="扣费" prop="deduct_fee" width="110">
								<template slot-scope="scope">
									<span v-if="!isCanEdit">{{ scope.row.deduct_fee }}</span>
									<el-input v-else-if="scope.$index < newBillRefundApplicationItem.length - 1" size='mini' type="number"
										style="width:100%;" v-model.number="scope.row.deduct_fee"
										@input.native="e => checkApplyAmountFirst(e, scope.row, 'deduct_fee')"
										:disabled="!/^(OVERPAY|RETURNS|OVER_FUND)$/.test(scope.row.refund_type)"></el-input><!-- 只有退货货款、多付时才可以编辑扣费 -->
									<span v-else>{{ scope.row.deduct_fee }}</span>
								</template>
							</el-table-column>
							<el-table-column label="实退金额" prop="actual_amount" width="90"></el-table-column>
							<el-table-column label="平台优惠" prop="platform_discount" width="90">
								<template slot-scope="scope">
									<el-input
										v-if="/^(CREATE|REJECT)$/.test(form.billRefundApplication.business_status) && hasPlatformDiscountPrivilege && scope.$index < newBillRefundApplicationItem.length - 1"
										size='mini' type="number" style="width:100%;" v-model.number="scope.row.platform_discount"
										@input.native="e => checkApplyAmountFirst(e, scope.row, 'platform_discount')"></el-input><!-- 只有退货货款、多付时才可以编辑扣费 -->
									<span v-else>{{ scope.row.platform_discount }}</span>
								</template>
							</el-table-column>
							<el-table-column label="责任分析子单单号" prop="sub_bill_no" width="120">
								<template slot-scope="scope">
									<a href="javascript:;" @click="openOrder(scope.row.sub_parent_id, scope.row.sub_bill_no)">{{
										scope.row.sub_bill_no }}</a>
								</template>
							</el-table-column>
							<el-table-column label="是否取消退款" prop="if_cancel_refund" width="90">
								<template slot-scope="scope">{{ { Y: '是', N: '否' }[scope.row.if_cancel_refund] ||
									scope.row.if_cancel_refund }}</template>
							</el-table-column>
							<el-table-column label="来源单号" prop="original_no" width="150">
								<template slot-scope="scope">
									<a href="javascript:;" @click="toOriginalNoDetail(scope.row)">{{ form.billRefundApplication.refund_way
										=== 'CROSS_OVER_T' && scope.row.refund_type === 'CROSS_OVER' ?
										form.billRefundApplication.merge_trade_no : scope.row.original_no }}</a>
								</template>
							</el-table-column>
							<el-table-column label="退款原因" prop="refund_reason" :show-overflow-tooltip="isCanEdit">
								<template slot-scope="scope">
									<el-tooltip v-if="!isCanEdit" class="item" effect="dark" :content="scope.row.refund_reason"
										placement="top-start" :popper-class="$style['pre-line']">
										<xpt-select-aux v-model='scope.row.refund_reason' aux_name='qrtkyy' disabled
											v-if="scope.row.refund_type == 'CANCEL'"></xpt-select-aux>
										<span v-else>{{ scope.row.refund_reason }}</span>

									</el-tooltip>
									<div v-else>
										<xpt-select-aux v-model='scope.row.refund_reason' aux_name='qrtkyy' disabled
											v-if="scope.row.refund_type == 'CANCEL'"></xpt-select-aux>
										<ol
											v-else-if="((scope.row.refund_type == 'RETURNS' && form.billRefundApplication.refund_way != 'CARRY_OVER' && scope.row.reason_version != 1) && scope.row.refundReason === '')">
											<li v-for="(obj, i) in refundReasonList2[scope.row.refund_type]" :key="obj">
												<a href="javascript:;" :class="$style['plan-btn']" @click="checkRefundReason(scope.row, i)"
													:style="i === 0 ? 'color:gray;' : ''">{{ i }}</a>
											</li>
										</ol>
										<div
											v-else-if="((/^(OVERPAY|PRICE_DIFF)$/.test(scope.row.refund_type) && scope.row.reason_version == '1') || (scope.row.refund_type == 'RETURNS' && form.billRefundApplication.refund_way != 'CARRY_OVER' && scope.row.reason_version != '1')) && scope.row.refundReason != ''"
											style="white-space: normal;">
											<div v-if="scope.row.excessiveParam">
												<div v-if="scope.row.refundReason === '多付' && (/^(1)$/).test(scope.row.reason_version)">
													<span>多付:</span><el-input type="textarea" autosize :maxlength='225'
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入原因" class="proto-textarea50"
														required></el-input><span>多付(</span><el-input type="textarea" autosize :maxlength='225'
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额" class="proto-textarea30"
														required style="width: 120px;"></el-input><span>)元</span></div>
												<div v-else-if="scope.row.refundReason === '单品优惠已前置' && (/^(1)$/).test(scope.row.reason_version)">
													单品优惠已前置:(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入型号"
														class="proto-textarea50" />)单品参与(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入日期"
														class="proto-textarea30" />)的(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入活动名称"
														class="proto-textarea50" />)活动退(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed3" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div v-else-if="scope.row.refundReason === '订单优惠已前置' && (/^(1)$/).test(scope.row.reason_version)">
													订单优惠已前置:(参与(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入日期"
														class="proto-textarea30" />)的(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入活动名称"
														class="proto-textarea50" />)活动退(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div v-else-if="scope.row.refundReason === '单个产品' && (/^(1)$/).test(scope.row.reason_version)">
													单个产品:(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入型号"
														class="proto-textarea50" />)按(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入日期"
														class="proto-textarea30" />)(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入价格"
														class="proto-textarea30" />)退差价(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed3" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div v-else-if="scope.row.refundReason === '组合' && (/^(1)$/).test(scope.row.reason_version)">
													组合:(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入型号"
														class="proto-textarea50" />)按(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入日期"
														class="proto-textarea30" />)(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入价格"
														class="proto-textarea30" />)退差价(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed3" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div v-else-if="scope.row.refundReason === '多个单品退组合' && (/^(1)$/).test(scope.row.reason_version)">
													多个单品退组合:(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请填单个或多个型号"
														class="proto-textarea50" />)构成(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入型号"
														class="proto-textarea50" />)组合+(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入组合编码"
														class="proto-textarea50" />),按(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed3" placeholder="请输入日期"
														class="proto-textarea30" />)(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed4" placeholder="请输入价格"
														class="proto-textarea50" />)退差价(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed5" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div
                          v-else-if="scope.row.refundReason === '跨合并单号的单品退组合' && (/^(1)$/).test(scope.row.reason_version)">
													跨合并单号的单品退组合:(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入合并单号"
														class="proto-textarea50"></el-input>)+(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请填单个或多个型号"
														class="proto-textarea50"></el-input>),构成(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入型号"
														class="proto-textarea50"></el-input>)组合+(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed3" placeholder="请输入组合编码"
														class="proto-textarea50"></el-input>)，按(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed4" placeholder="请输入日期"
														class="proto-textarea30"></el-input>)(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed5" placeholder="请输入价格"
														class="proto-textarea30"></el-input>)退差价(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed6" placeholder="请输入金额"
														class="proto-textarea30"></el-input>)元</div>
												<!-- <div v-if="scope.row.refundReason === '单个产品退货' && (/^(2|3aaaaaaaaaaaa)$/).test(scope.row.reason_version)">单个产品退货:(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0" placeholder="请输入**产品**原因" class="proto-textarea50"/>)退货,买家承担(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额" class="proto-textarea30"/>)元,实退(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed2" placeholder="请输入金额" class="proto-textarea30"/>)元</div> -->
												<div v-else-if="scope.row.refundReason === '整单退货' && (/^(2|3|4)$/).test(scope.row.reason_version)">
													整单退货:整单(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入原因"
														class="proto-textarea50" />)退货,买家承担(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额"
														class="proto-textarea30" />)元,实退(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
                        <span v-else>{{ scope.row.refund_reason }}</span>
											</div>
											<span v-else>{{ scope.row.refund_reason }}</span>
										</div>
										<!-- 版本1：未前置优惠、单品优惠、退货货款、运费、三包费、维修费、销售折让、外购折现 -->
										<!-- 版本2：未前置优惠、单品优惠、退货货款（版本1）、运费、三包费、维修费、销售折让、外购折现、多付-优惠前置、多付-货款、差价-->
										<div
											v-else-if="(/^(PREFERENTIAL|SINGLE_DISCOUNT|CARRIAGE|THREE|REPAIR|COMPENSATION|DISCOUNT|OVER_DISCOUNT|OVER_FUND)$/.test(scope.row.refund_type)) || (scope.row.refund_type == 'RETURNS' && form.billRefundApplication.refund_way != 'CARRY_OVER' && scope.row.reason_version == '1') || (scope.row.refund_type == 'PRICE_DIFF' && scope.row.reason_version != '1') || (scope.row.refund_type == 'CROSS_OVER' && (/^(2|3|4)$/).test(scope.row.reason_version) && form.billRefundApplication.refund_way == 'CROSS_OVER_T') || (scope.row.refund_type === 'OVERPAY' && (/^(3|4)$/).test(scope.row.reason_version))"
											style="white-space: normal;">
											<div v-if="scope.row.excessiveParam">
												<div v-if="scope.row.refund_type === 'PREFERENTIAL' && scope.row.reason_version == '1'">
													参与(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入日期"
														class="proto-textarea30" />)的(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入活动名称"
														class="proto-textarea50" />)退(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div
                          v-else-if="scope.row.refund_type === 'PREFERENTIAL' && (/^(2|3|4)$/).test(scope.row.reason_version)">
													<span>参与(</span><el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入**时间的**活动"
														class="proto-textarea50"></el-input>)退(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div
                          v-else-if="scope.row.refund_type === 'SINGLE_DISCOUNT' && (/^(1|2|3|4)$/).test(scope.row.reason_version)">
													(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0"
														placeholder="请输入型号" class="proto-textarea50" />)参与(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed1" placeholder="请输入日期"
														class="proto-textarea30" />)的(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入活动名称"
														class="proto-textarea50" />)退(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed3" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div v-else-if="scope.row.refund_type === 'RETURNS' && (/^(1)$/).test(scope.row.reason_version)">
													(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0"
														placeholder="请输入型号/整单/批次单号" style="width: 150px !important" class="proto-textarea50"
														required />)(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入原因"
														class="proto-textarea50" />）退货,(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" style="width: 150px !important"
														placeholder="请输入扣费原因+扣费金额" class="proto-textarea50" />)元,实退(<el-input type="textarea"
														autosize :maxlength="225" v-model="scope.row.excessiveParam.filed3" placeholder="请输入金额"
														class="proto-textarea30" />)元</div>
												<div
                          v-else-if="scope.row.refund_type === 'CARRIAGE' && (/^(1|2|3|4)$/).test(scope.row.reason_version)">
													(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0"
														placeholder="请输入原因" class="proto-textarea50" />)退运费(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额"
														class="proto-textarea30" />)元</div>
												<div v-else-if="scope.row.refund_type === 'THREE' && (/^(1|2|3|4)$/).test(scope.row.reason_version)">
													(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0"
														placeholder="请输入原因" class="proto-textarea50" />)退三包费(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额"
														class="proto-textarea30" />)元</div>
												<div
                          v-else-if="scope.row.refund_type === 'REPAIR' && (/^(1|2|3|4)$/).test(scope.row.reason_version)">
													(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0"
														placeholder="请输入原因" class="proto-textarea50" />)维修，退(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额"
														class="proto-textarea30" />)元</div>
												<div v-else-if="scope.row.refund_type === 'COMPENSATION' && scope.row.reason_version == '1'">
													(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0"
														placeholder="请输入原因" class="proto-textarea50" />)补偿(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额"
														class="proto-textarea30" />)元</div>
												<div
                          v-else-if="scope.row.refund_type === 'COMPENSATION' && (/^(2|3|4)$/).test(scope.row.reason_version)">
													(<el-input type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0"
														placeholder="请输入原因" class="proto-textarea50" />)折让(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额"
														class="proto-textarea30" />)元</div>
												<div
                          v-else-if="scope.row.refund_type === 'DISCOUNT' && (/^(2|3|4)$/).test(scope.row.reason_version)">
													参与(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入**时间**原因"
														class="proto-textarea50" />)赠送(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入赠品"
														class="proto-textarea50" />),折现(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入金额" class="proto-textarea50" />)元
												</div>
												<div v-else-if="scope.row.refund_type === 'DISCOUNT' && scope.row.reason_version == '1'">(<el-input
														type="textarea" autosize :maxlength="225" v-model="scope.row.excessiveParam.filed0"
														placeholder="请输入日期" class="proto-textarea30" />)的(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed1" placeholder="请输入活动名称"
														class="proto-textarea50" />)赠送(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入赠品"
														class="proto-textarea50" />)，要求折现(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed3" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div
                          v-else-if="scope.row.refund_type === 'OVER_FUND' && (/^(2|3|4)$/).test(scope.row.reason_version)">
													<span>多付(</span><el-input type="textarea" autosize :maxlength='225'
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入金额" class="proto-textarea30"
														required></el-input><span>)元</span></div>
												<div
                          v-else-if="scope.row.refund_type === 'OVER_DISCOUNT' && (/^(2|3|4)$/).test(scope.row.reason_version)">
													<span>参与(</span><el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入**时间的**活动"
														class="proto-textarea50"></el-input>)退(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入金额" class="proto-textarea30" />)元
												</div>
												<div
                          v-else-if="scope.row.refund_type === 'PRICE_DIFF' && (/^(2|3)$/).test(scope.row.reason_version)">
													按(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入日期"
														class="proto-textarea30"></el-input>)的(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed1" placeholder="请输入价格"
														class="proto-textarea30"></el-input>)减(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed2" placeholder="请输入优惠"
														class="proto-textarea50"></el-input>)的到手价(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed3" placeholder="请输入金额"
														class="proto-textarea30"></el-input>)减实际支付到手价(<el-input type="textarea" autosize
														:maxlength="225" v-model="scope.row.excessiveParam.filed4" placeholder="请输入金额"
														class="proto-textarea30"></el-input>)=差额(<el-input type="textarea" autosize :maxlength="225"
														v-model="scope.row.excessiveParam.filed5" placeholder="请输入金额"
														class="proto-textarea30"></el-input>)元</div>
												<div v-else-if="scope.row.refund_type === 'PRICE_DIFF' && (/^(4)$/).test(scope.row.reason_version)">
													<span>商品(</span><el-input type="textarea" autosize :maxlength='225'
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入商品" class="proto-textarea30"
														required></el-input><span>)</span><span>虚拟拍单时间(</span><el-input type="textarea" autosize
														:maxlength='225' v-model="scope.row.excessiveParam.filed1" placeholder="请输入虚拟拍单时间"
														class="proto-textarea30" required></el-input><span>)</span><span>的到手价(</span><el-input
														type="textarea" autosize :maxlength='225' v-model="scope.row.excessiveParam.filed2"
														placeholder="请输入到手价" class="proto-textarea30"
														required></el-input><span>)</span><span>退差额(</span><el-input type="textarea" autosize
														:maxlength='225' v-model="scope.row.excessiveParam.filed3" placeholder="请输入差额"
														class="proto-textarea30" required></el-input><span>)</span></div>
												<div
                          v-else-if="scope.row.refund_type === 'CROSS_OVER' && (/^(2|3|4)$/).test(scope.row.reason_version)">
													<span>多付(</span><el-input type="textarea" autosize :maxlength='225'
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入金额" class="proto-textarea30"
														required></el-input><span>)元</span></div>
												<div v-else-if="scope.row.refund_type === 'OVERPAY' && (/^(3|4)$/).test(scope.row.reason_version)">
													<span>多付(</span><el-input type="textarea" autosize :maxlength='225'
														v-model="scope.row.excessiveParam.filed0" placeholder="请输入金额" class="proto-textarea30"
														required></el-input><span>)元</span></div>
                        <span v-else>{{ scope.row.refund_reason }}</span>
											</div>
											<span v-else>{{ scope.row.refund_reason }}</span>
										</div>
										<!-- O2O跨店铺差价、服务折让、换货结转、跨单结转 、收入结转后退款-->
										<el-input type="textarea" autosize
											v-else-if="(/^(O2O_DIFF|DELAY|REFUNDCARRYFORWARD|INCOME_CARRY)$/.test(scope.row.refund_type)) || scope.row.realyRefundType == 'REFUNDCARRYFORWARD' || (scope.row.refund_type == 'RETURNS' && form.billRefundApplication.refund_way == 'CARRY_OVER') || (scope.row.refund_type == 'CROSS_OVER' && form.billRefundApplication.refund_way == 'CROSS_OVER_T')"
											:data-init="autoMakeRefundReason(scope.row)" v-model="scope.row.refund_reason" :maxlength="500"
											style="width:100%;"></el-input>
										<span v-else>{{ scope.row.refund_reason }}</span>

									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</el-tab-pane>
				<el-tab-pane label='退款一览' name='browse'>
					<el-collapse v-model="activeNames">
						<el-collapse-item v-for="(key, keyIndex) in ['PRICE_DIFF', 'O2O_DIFF', 'INCOME_CARRY']" :key="keyIndex"
							:title="key === 'PRICE_DIFF' ? '差价商品' : 'O2O跨店铺差价商品'" :name="String(keyIndex + 1)"
							:style="{ display: (key === 'PRICE_DIFF' ? PRICE_DIFF_List.length : O2O_DIFF_List.length) ? 'block' : 'none' }">
							<el-table :data="key === 'PRICE_DIFF' ? PRICE_DIFF_Total : O2O_DIFF_Total
								" border tooltip-effect="dark" width='100%'>
								<el-table-column label="价格区间" prop="price_list_name" width="200">
									<template slot-scope="scope">
										<a href="javascript:;" @click="toPriceListDetail(scope.row.price_list_id)">{{
											scope.row.price_list_name }}</a>
									</template>
								</el-table-column>
								<el-table-column label="商品名称" prop="materia_name" width="80" show-overflow-tooltip></el-table-column>
								<el-table-column label="商品规格描述" prop="materia_specifications" width="200"
									show-overflow-tooltip></el-table-column>
								<el-table-column label="标准售价" prop="stand_price"></el-table-column>
								<el-table-column label="拍下价格" prop="act_price"></el-table-column>
								<el-table-column label="活动价" prop="new_price"></el-table-column>
								<el-table-column label="差价" prop="price_spread"></el-table-column>
								<el-table-column label="生效日期" width="170" show-overflow-tooltip>
									<template slot-scope="scope">{{ scope.row.enable_time | dataFormat1 }}</template>
								</el-table-column>
								<el-table-column label="失效日期" width="170" show-overflow-tooltip>
									<template slot-scope="scope">{{ scope.row.disable_time | dataFormat1 }}</template>
								</el-table-column>
								<el-table-column label="购买时间" prop="created" width="170" show-overflow-tooltip>
									<template slot-scope="scope">{{ scope.row.created | dataFormat1 }}</template>
								</el-table-column>
								<el-table-column label="订单店铺" prop="shop_name" width="80" show-overflow-tooltip></el-table-column>
								<el-table-column label="订单状态" prop="status">
									<template slot-scope="scope">
										{{ {
											'CANCEL': '取消', 'NORMAL': '生效', 'WAITING': '等待发运', 'PART_DELIVERED': '部分发运中', 'DELIVERED':
												'已发运'
										}[scope.row.status] }}
									</template>
								</el-table-column>
								<el-table-column label="价目表店铺" prop="price_shop_name" width="100"></el-table-column>
								<el-table-column label="活动商品编码" prop="price_material_no" width="150"
									show-overflow-tooltip></el-table-column>
								<el-table-column label="活动商品名称" prop="price_material_name" show-overflow-tooltip></el-table-column>
								<el-table-column label="活动规格描述" prop="price_material_desc" width="200" align="right" header-align="left"
									show-overflow-tooltip></el-table-column>
								<el-table-column label="商品编码" prop="materia_number" width="120" show-overflow-tooltip></el-table-column>
								<el-table-column label="淘宝单号" prop="tid" width="80" show-overflow-tooltip></el-table-column>
								<el-table-column label="单位" prop="unit_name"></el-table-column>
							</el-table>
						</el-collapse-item>
						<el-collapse-item title="未发取消商品（未发取消）" name="3" :style="{ display: CANCEL_List.length ? 'block' : 'none' }">
							<xpt-list :data="CANCEL_Total" :colData='CANCEL_Cols' :showHead='false' :orderNo='true'
								selection='hidden'></xpt-list>
						</el-collapse-item>
						<el-collapse-item title="退货商品" name="4" :style="{ display: RETURNS_List.length ? 'block' : 'none' }">
							<xpt-list :data="RETURNS_Total" :colData='RETURNS_Cols2' :showHead='false' :orderNo='true'
								selection='hidden'></xpt-list>
						</el-collapse-item>
						<el-collapse-item title="问题商品(销售折让)" name="5"
							:style="{ display: COMPENSATION_List.length ? 'block' : 'none' }">
							<xpt-list :data="COMPENSATION_Total" :colData='COMPENSATION_Cols' :showHead='false' :orderNo='true'
								selection='hidden'></xpt-list>
						</el-collapse-item>
						<el-collapse-item title="问题商品(维修费)" name="6" :style="{ display: REPAIR_List.length ? 'block' : 'none' }">
							<xpt-list :data="REPAIR_Total" :colData='COMPENSATION_Cols' :showHead='false' :orderNo='true'
								selection='hidden'></xpt-list>
						</el-collapse-item>
						<el-collapse-item title="服务折让商品" name="7" :style="{ display: DELAY_List.length ? 'block' : 'none' }">
							<xpt-list :data="DELAY_Total" :colData='DELAY_Cols' :showHead='false' :orderNo='true'
								selection='hidden'></xpt-list>
						</el-collapse-item>
						<el-collapse-item title="单品优惠" name="8"
							:style="{ display: SINGLE_DISCOUNT_List.length ? 'block' : 'none' }">
							<xpt-list :data="SINGLE_DISCOUNT_Total" :colData='SINGLE_DISCOUNT_Cols' :showHead='false' :orderNo='true'
								selection='hidden'></xpt-list>
						</el-collapse-item>
						<el-collapse-item title="收入结转单" name="9" :style="{ display: INCOME_CARRY_List.length ? 'block' : 'none' }">
							<xpt-list :data="INCOME_CARRY_Total" :colData='INCOME_CARRY_Cols' :showHead='false' :orderNo='true'
								selection='hidden'></xpt-list>
						</el-collapse-item>
					</el-collapse>
				</el-tab-pane>
				<el-tab-pane label="对账明细" name="accountDetail" class="xpt-flex">
					<xpt-list :data='accountList' :colData='accountCols' :showHead='false' selection='hidden'></xpt-list>
				</el-tab-pane>
				<el-tab-pane label='支付明细' name='payforDetail' class='xpt-flex'>
					<div class="xpt-flex__bottom scroll">
						<el-table :data="form.sysPaymentList.length ? form.sysPaymentList.concat('') : form.sysPaymentList"
							:row-class-name='paymentListClassName' border tooltip-effect="dark" style="width: 100%;" width='100%'>
							<el-table-column label="序号" type="index" width="50">
								<template slot-scope="scope">
									<div class="table-index">{{ scope.$index < form.sysPaymentList.length ? scope.$index + 1 : '' }}</div>
								</template>
							</el-table-column>
							<el-table-column label="支付渠道" prop="payment_channel" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ {
										TAOBAO: '淘宝', B2C: 'B2C商城', EMERGING_PROJECT: '新兴项目',
										JOINED: '加盟', O2O: 'O2O体验店'
									}[scope.row.payment_channel] || scope.row.payment_channel }}
								</template>
							</el-table-column>
							<el-table-column label="支付明细类型" prop="type" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ { RECEIVE: '收入', REFUND: '退款', CARRYOVERS: '结转', RETURNS: '退货货款', COMPENSATION: '赔偿' }[scope.row.type]
										|| scope.row.type }}
								</template>
							</el-table-column>
							<el-table-column label="支付方式" prop="pay_type" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ pay_type_options[scope.row.pay_type] || scope.row.pay_type }}
								</template>
							</el-table-column>
							<el-table-column label="收款账号" prop="received_account" width="200" show-overflow-tooltip></el-table-column>
							<el-table-column label="收款金额" prop="pay_amount" width="110" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ scope.$index < form.sysPaymentList.length ?
										((/^(REFUND|RETURNS|COMPENSATION)$/.test(scope.row.type) ? -1 : 1) * scope.row.pay_amount) : ('合计' +
										_calcPayAmount()) }} </template>
							</el-table-column>
							<el-table-column label="支付账号" prop="pay_account" show-overflow-tooltip></el-table-column>
							<el-table-column label="开户行" prop="pay_bank" show-overflow-tooltip></el-table-column>
							<el-table-column label="用户名" prop="pay_name" show-overflow-tooltip></el-table-column>
							<el-table-column label="销售订单号" prop="sys_trade_no" width="200" show-overflow-tooltip></el-table-column>
							<el-table-column label="支付时间" prop="payment_time" width="180" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ scope.row.payment_time | dataFormat1 }}</span>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</el-tab-pane>
				<el-tab-pane label='资料请求' name='infoRequestList' class='xpt-flex'>
					<el-row class="xpt-top" :gutter="40">
						<el-col :span="24">
							<el-button type="primary" size="mini" @click="openReview()">开启补充资料</el-button>

							<el-button type="primary" size="mini"
								@click="financeConfirm('financeConfirmDataRequest?permissionCode=FINANCE_CECONFIRM_REQUEST')">财务确认</el-button>
							<el-button type="primary" size="mini"
								@click="financeConfirm('financeCancelDataRequest?permissionCode=FINANCE_CANCEL_REQUEST')">作废</el-button>
							<el-button type="primary" size="mini" @click="openServiceReview()">客服补充资料</el-button>

							<el-button type="primary" size="mini"
								@click="financeConfirm('businessConfirmDataRequest?permissionCode=BUSINESS_CECONFIRM_REQUEST')">补充完毕</el-button>
						</el-col>
					</el-row>
					<div class="xpt-flex__bottom scroll">
						<el-table :data="InfoRequestList" border tooltip-effect="dark" style="width: 100%;" width='100%'
							@selection-change="infoListSelectChange">
							<el-table-column type="selection" width="100" align="center"></el-table-column>
							<el-table-column label="序号" type="index" width="70">
								<template slot-scope="scope">
									<div class="table-index">{{ scope.$index + 1 }}</div>
								</template>
							</el-table-column>
							<el-table-column label="退款类型" prop="refund_type" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ refoudDetailList_type_options[scope.row.refund_type] ||
										refoudDetailList_type_options2[scope.row.refund_type] }}
								</template>
							</el-table-column>
							<el-table-column label="资料请求状态" prop="info_status" width="100" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ info_status_options[scope.row.info_status] }}
								</template>
							</el-table-column>
							<el-table-column label="是否财务确认" prop="finance_confirm" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ { Y: '是', N: '否' }[scope.row.finance_confirm] }}
								</template>
							</el-table-column>
							<el-table-column label="财务备注" prop="finance_remark" show-overflow-tooltip></el-table-column>

							<el-table-column label="财务确认时间" prop="finance_confirm_time" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ scope.row.finance_confirm_time | dataFormat1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="财务确认人" prop="finance_confirm_name" show-overflow-tooltip>
							</el-table-column>
							<el-table-column label="是否业务确认" prop="business_confirm" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ { Y: '是', N: '否' }[scope.row.business_confirm] }}
								</template>
							</el-table-column>
							<el-table-column label="业务备注" prop="business_remark" show-overflow-tooltip></el-table-column>
							<el-table-column label="客服确认人" prop="ender_name" width="200" show-overflow-tooltip></el-table-column>
							<el-table-column label="客服确认时间" prop="end_time" width="100" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ scope.row.end_time | dataFormat1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="是否超48小时" prop="if_over_time" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ { Y: '是', N: '否' }[scope.row.if_over_time] }}
								</template>
							</el-table-column>
							<el-table-column label="创建人姓名" prop="creater_name" show-overflow-tooltip></el-table-column>
							<el-table-column label="创建时间" prop="creater_time" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ scope.row.creater_time | dataFormat1 }}</span>
								</template>
							</el-table-column>

						</el-table>
					</div>
				</el-tab-pane>
				<el-tab-pane label='操作记录' name='operateLogList' class='xpt-flex'>
					<div class="xpt-flex__bottom scroll">
						<el-table :data="operateLogList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
							<el-table-column label="序号" type="index" width="70">
								<template slot-scope="scope">
									<div class="table-index">{{ scope.$index + 1 }}</div>
								</template>
							</el-table-column>
							<el-table-column label="用户" prop="operator_name" width="200" show-overflow-tooltip></el-table-column>
							<el-table-column label="业务操作" prop="operate_type" width="200" show-overflow-tooltip>
								<template slot-scope="scope">
									{{ operate_type_options[scope.row.operate_type] || scope.row.operate_type }}
								</template>
							</el-table-column>
							<el-table-column label="操作描述" prop="description" width="400" show-overflow-tooltip></el-table-column>
							<el-table-column label="操作时间" prop="operate_time" width="200" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ scope.row.operate_time | dataFormat1 }}</span>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</el-tab-pane>
				<el-tab-pane label='接口信息' name='apiDetail' class='xpt-flex'>
					<div class="xpt-flex__bottom scroll">
						<el-table :data="apiList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
							<el-table-column label="下游单据类型" prop="push_bill_type" width="150" show-overflow-tooltip></el-table-column>
							<el-table-column label="单据编号" prop="interface_respone" show-overflow-tooltip>
								<template slot-scope="scope">{{ scope.row.interface_respone_billno }}</template>
							</el-table-column>
							<el-table-column label="方式" prop="business_type" width="70" show-overflow-tooltip></el-table-column>
							<el-table-column label="接口状态" prop="interface_status" width="100" show-overflow-tooltip>
								<template slot-scope="scope">{{ interface_status_options[scope.row.interface_status] }}</template>
							</el-table-column>
							<el-table-column label="接口提示信息" prop="interface_respone" show-overflow-tooltip></el-table-column>
							<el-table-column label="生成时间" prop="create_time" width="180" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ scope.row.create_time | dataFormat1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="操作" width="100">
								<template slot-scope="scope">
									<el-button type="primary" size="mini" @click="pushOrder(scope.row.id)">重新推送</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</el-tab-pane>
				<el-tab-pane label='驳回信息' name='bohuiDetail' class='xpt-flex'>
					<div class="xpt-flex__bottom scroll">
						<el-table :data="form.billRefundApplicationReject" border tooltip-effect="dark" style="width: 100%;"
							width='100%'>
							<el-table-column label="序号" type="index" width="70">
								<template slot-scope="scope">
									<div class="table-index">{{ scope.$index + 1 }}</div>
								</template>
							</el-table-column>
							<el-table-column label="驳回人" prop="reject_person_name" width="150"
								show-overflow-tooltip></el-table-column>
							<el-table-column label="驳回原因" prop="reject_reason" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ rejectReasonObj[scope.row.reject_reason] }}</span>
								</template>
							</el-table-column>
							<el-table-column label="驳回备注" prop="reject_remark" show-overflow-tooltip></el-table-column>
							<el-table-column label="驳回时间" prop="reject_date" width="180" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ scope.row.reject_date | dataFormat1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="操作" width="50" v-if="!params.snapshotObj">
								<template slot-scope="scope">
									<el-button type="primary" size="mini" @click="snapshot(scope.row.id)">快照</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</el-tab-pane>
				<el-tab-pane label='优惠' name='couponTable' class='xpt-flex'>
					<xpt-list ref='coupon' :data='couponList' :btns='[]' :colData='couponCols' isNeedClickEvent orderNo
						:searchPage="couponSearchObj.page_name" @search-click='getCouponList'>
						<template slot='num' slot-scope='scope'>
							<el-input type="number" v-model="scope.row.num" size='mini' style="width:100%;" :disabled="true" @change="e => {
								scope.row.num = fitterPrice(scope.row.num) ? scope.row.num : 1
							}">
							</el-input>
						</template>
					</xpt-list>
				</el-tab-pane>
				<el-tab-pane label='差价核算单' name='differenceAccount' class='xpt-flex'>
					<div class="xpt-flex">
						<div>
							<el-form label-position="right" :rules="differenceAccountFormRules" :inline="true"
								ref="differenceSearchObj" id="differenceAccountDialog">
								<el-col :span="10">
									<el-form-item label="虚拟拍单时间" prop="differenceSearchObj.order_date">
										<el-date-picker v-model="differenceSearchObj.order_date" style="width: 150px" type="datetime"
											placeholder="选择日期" size='mini' disabled></el-date-picker>
									</el-form-item>
								</el-col>

							</el-form>
							<el-tabs v-model="selectTab">
								<el-tab-pane label='基础信息' name='differenceAccountBasic3' class='xpt-flex'>
									<xpt-list ref='differenceAccountBasic3' :data='accountBasicList' :btns='[]'
										:colData='accountBasicColsList' isNeedClickEvent :pageTotal="null" selection='' :showHead='false'
										orderNo :searchPage="''">
									</xpt-list>
								</el-tab-pane>
								<el-tab-pane label='活动优惠' name='differenceAccountCoupon3' class='xpt-flex'>
									<xpt-list ref='differenceAccountCoupon3' :data='accountCouponList' :pageTotal="null" selection=''
										:btns='accountCouponBtns' :colData='accountCouponCols' isNeedClickEvent orderNo :searchPage="''">
									</xpt-list>
								</el-tab-pane>
							</el-tabs>
						</div>
					</div>
				</el-tab-pane>
				<el-tab-pane label='反审信息' name='recheckDetail' class='xpt-flex'>
					<div class="xpt-flex__bottom scroll">
						<el-table :data="form.billRefundApplicationCancel" border tooltip-effect="dark" style="width: 100%;"
							width='100%'>
							<el-table-column label="序号" type="index" width="70">
								<template slot-scope="scope">
									<div class="table-index">{{ scope.$index + 1 }}</div>
								</template>
							</el-table-column>
							<el-table-column label="反审人" prop="cancel_person_name" width="150"
								show-overflow-tooltip></el-table-column>
							<el-table-column label="反审备注" prop="cancel_remark" show-overflow-tooltip></el-table-column>
							<el-table-column label="回传结果" prop="k3_result" show-overflow-tooltip></el-table-column>
							<el-table-column label="反审时间" prop="cancel_date" width="180" show-overflow-tooltip>
								<template slot-scope="scope">
									<span>{{ scope.row.cancel_date | dataFormat1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="快照" width="50" v-if="!params.snapshotObj">
								<template slot-scope="scope">
									<el-button type="primary" size="mini" @click="cancelSnapshot(scope.row.id)">快照</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</el-tab-pane>
			</el-tabs>
		</el-row>
		<xpt-upload :ifClickUpload="ifClickUpload" :callback="postCallback" :dataObj="uploadData"></xpt-upload>
	</div>
</template>
<script>
//驳回备注逻辑改为跟驳回按钮权限一致，********小庄
import Vue from 'vue'
import VL from '@common/validate.js'
import account from '../order/merge/account.js'
import fn from '@common/Fn.js'
import refund_type_cols from './refund_type_cols.js'
import couponTable from '@components/after_sales/couponTable.vue'
import { cloneDeep } from 'lodash'
// 内部便签
import inner from '@components/common/inner'
import differenceAccount from '@components/after_sales_refund/differenceAccount.vue'
import fee from "./refundRequest_3/fee.js"
import moment from 'moment'
import btnJs from './refundRequest_3/refundRequestOfBtn.js'
import { isValidParentheses, parseOuterParenthesesText } from '../../utils/parentheses.js'

export default {
	mixins: [account, refund_type_cols, fee, btnJs],
	props: ['params'],
	data() {
		let self = this;
		return {
      progressLabelOptions: [],
      progressLabel: '',
      orderDetailLoading:false,// 详情接口loading
			refoundModeEditable: false,
      shopInfo: {},
			copyChooseTchList: [],
			copyChooseTchIds: [],
			ifHasDiscount: false,
			testBtn: true, // 为提交初审添加的控制，解锁的时候，禁止提交初审
			selectTab1: 'basicInformation',
			selectTab2: 'refoudDetail',
			activeNames: ['1', '2', '3', '4', '5', '6', '7', '8'],
			CANCEL_List: [],
			DELAY_List: [],
			SINGLE_DISCOUNT_List: [],
			RETURNS_List: [],
			PRICE_DIFF_List: [],
			O2O_DIFF_List: [],
			COMPENSATION_List: [],
			REPAIR_List: [],
			INCOME_CARRY_List: [],


			CANCEL_Total: [],
			DELAY_Total: [],
			SINGLE_DISCOUNT_Total: [],
			RETURNS_Total: [],
			PRICE_DIFF_Total: [],
			O2O_DIFF_Total: [],
			COMPENSATION_Total: [],
			REPAIR_Total: [],
			INCOME_CARRY_Total: [],
			getUserPersonBusinessAttributeFlag: false,
			arrItem: {
				CANCEL: ['act_price', 'stand_price'],
				DELAY: ['stand_price', 'act_price'],
				SINGLE_DISCOUNT: ['sum_actual_amount', 'stand_price', 'act_price'],
				RETURNS: ['act_price', 'refund_amount'],
				PRICE_DIFF: ['stand_price', 'act_price', 'new_price', 'price_spread'],
				O2O_DIFF: ['stand_price', 'act_price', 'new_price', 'price_spread'],
				COMPENSATION: ['act_price'],
				REPAIR: ['act_price'],
				INCOME_CARRY: ['act_sum', 'refundable_amount']
			},

			couponCols: [
				{
					label: '销售单号',
					width: 170,
					prop: 'sys_trade_no'
				}, {
					label: '平台单号',
					width: 180,
					prop: 'tid'
				}, {
					label: '优惠影响范围',
					width: 90,
					prop: 'discount_effect_area',
					format: 'auxFormat',
					formatParams: 'DISCOUNT_EFFECT_AREA'
				}, {
					label: '优惠分类',
					prop: 'activity_type',
					format: 'auxFormat',
					formatParams: 'discountCategory',
				}, {
					label: '优惠活动',
					prop: 'activity_name',
					redirectClick(row) {
						self.$root.eventHandle.$emit('alert', {
							params: {
								act_id: row.activity_id
							},
							component: () => import('@components/discount/view'),
							style: 'width:1000px;height:560px',
							title: '优惠详情'
						})
					},
					width: 190
				}, {
					label: '项目细类',
					width: 90,
					prop: 'item_type_detail_code',
					format: 'auxFormat',
					formatParams: 'ITEM_TYPE_DETAIL'
				}, {
					label: '优惠金额',
					prop: 'discount',
					bool: true,
					elInput: true,
					disabled(row) {
						/*
							params.ifAddEdition为true时可以编辑
							if_change_price（是否能够改变金额）为Y时可以编辑
							audit_status（审核状态）为N时可以编辑
							当满足上面三个状态下时可以编辑
							tempId，临时ID，有该值时无需考虑以上条件
							scope.row.tempId?false:(params.ifAddEdition&&scope.row.audit_status==='N'
							&&scope.row.if_change_price === 'Y'?false:true)
						*/
						if (row.activity_type === 'REFUND' && !row.sys_trade_no) {
							if (row.subtractMoney >= 0 && row.if_change_price === 'Y' && row.subDiscount === null) {
								return false
							} else {
								return true
							}
						}
						// 批次订单转过来的优惠（客服责任优惠禁止在合并订单中修改金额）
						if (row.activity_subclass === 'DISCOUNT_COMPENSATION') {
							return true
						}
						if (!row.sys_trade_no && row.if_change_price == 'Y' || self.isSample) {
							return row.tempId ? false :
								(self.ifAddEdition && row.audit_status === 'N' && row.if_change_price === 'Y' ? false : true)
						} else {
							return true;
						}
					}
				}, {
					label: '已使用金额',
					prop: 'share_discount',
					width: 80
				}, {
					label: '优惠券数量',
					slot: 'num'
				}, {
					label: '影响售价',
					prop: 'if_effect_price',
					formatter(val) {
						switch (val) {
							case 'true': return '是'; case 'false': return '否'; case 'Y': return '是'; case 'N': return '否'; default: return val;
						}
					}
				}, {
					label: '业务员',
					prop: 'creator_name'
				}, {
					label: '创建时间',
					prop: 'create_time_str',
					format: 'dataFormat'
				}, {
					label: '状态',
					prop: 'audit_status',
					formatter(val) {
						switch (val) {
							case 'Y': return '已审核'; case 'N': return '未审核'; default: return val;
						}
					}
				}, {
					label: '取消状态',
					prop: 'cancel_status',
					formatter(val) {

						switch (val) {
							case 'Y': return '已取消'; case 'N': return '未取消'; case 'F': return '已冻结'; default: return val;
						}
					}
				}, {
					label: '审核人',
					prop: 'auditor_name'
				}, {
					label: '审核时间',
					prop: 'audit_time_str',
				}, {
					label: '取消人',
					prop: 'canceler_name'
				}, {
					label: '取消时间',
					prop: 'cancel_time_str'
				}, {
					label: '优惠说明',
					prop: 'buss_type',
					format: 'auxFormat',
					formatParams: 'discount_remark_reason',
					width: 90
				},
			],
			couponList: [],
			couponSearchObj: {
				page_no: 1,
				page_size: 20,
				page_name: 'scm_merge_discount',
				where: []
			},
			operateLogList: [],
			apiList: [],
			selectRefoudDetailLists: [],
			uploadData: {},
			isCanEdit: true,
			submitLoading: false,//防止提交初审多次
			businessUNLockLoading: false,//提交初审请求中时禁掉业务解锁按钮
			isSameBusinessLockPerson: '',//代理人是否等于业务锁定人，且业务状态 = 创建 or 驳回
			isSameFinanceLockPerson: '',//代理人是否等于初审锁定人
			isSamereviewLockPerson: "",//代理人是否等于复核锁定人
			saveLoading: false,
			refreshLoading: false,
			ifClickUpload: false,
			canBuildNoSourceOrder: true,//新增时没有选单情况下，可以选择店铺、买家昵称、业务员等建无源单
			cantEditWhenPersonBusinessAttribute: false,//经销商某些情况下按钮和编辑禁掉
			isClickSaveOrRefresh: undefined,//初始值要undefined
			totalObj: {
				apply_amount: '合计0',
				deduct_fee: '合计0',
				actual_amount: '合计0',
			},
			merge_trade_id: '',// 对账明细页签的order/merge/account.js接口要用到

			taobao_refund_status_options: {
				WAIT_SELLER_AGREE: '买家已经申请退款，等待卖家同意',
				WAIT_BUYER_RETURN_GOODS: '卖家已经同意退款，等待买家退货',
				WAIT_SELLER_CONFIRM_GOODS: '买家已经退货，等待卖家确认收货',
				SELLER_REFUSE_BUYER: '卖家拒绝退款',
				CLOSED: '退款关闭',
				SUCCESS: '退款成功',
			},
			taobao_order_status_options: {
				TRADE_NO_CREATE_PAY: '没有创建支付宝交易',
				WAIT_BUYER_PAY: '等待买家付款',
				WAIT_SELLER_SEND_GOODS: '等待卖家发货',
				WAIT_BUYER_CONFIRM_GOODS: '等待买家确认收货',
				TRADE_BUYER_SIGNED: '买家已签收',
				TRADE_FINISHED: '交易成功',
				TRADE_CLOSED: '交易关闭',
				TRADE_CLOSED_BY_TAOBAO: '交易被平台关闭',
				ALL_WAIT_PAY: '等待买家付款、没有创建支付宝交易',
				ALL_CLOSED: '交易关闭、交易被平台关闭',
			},
			original_type_options: {
				TMALL: '天猫退款',
				B2C: 'B2C退款',
				OTHER: '手工退款',
				PDD: '拼多多退款',
				JD: '京东退款',
				VIP: '唯品会',
				SUNING: '苏宁',
				YJ: '云集',
				I: '爱库存',
				KL: '考拉',
				MY: '蜜芽',
				KS: '快手',
				DY: '抖音',
				JD_ZY: '京东自营',
				MX: '猫享',
				DW: '得物',
				ZFE: '住范儿',
				YZ: '有赞',
				XMYP: '小米有品',
				WX: '微信',
        MZ: '喵住',
        POS: '门店商城',
        TM_C: '天猫超市',
        DYLK: '抖音来客',
			},
			taobao_status_options: {
				WAIT_SELLER_AGREE: '买家已经申请退款，等待卖家同意',
				WAIT_BUYER_RETURN_GOODS: '卖家已经同意退款，等待买家退货',
				WAIT_SELLER_CONFIRM_GOODS: '买家已经退货，等待卖家确认收货',
				SELLER_REFUSE_BUYER: '卖家拒绝退款',
				CLOSED: '退款关闭',
				SUCCESS: '退款成功',
				//云集新增
				WAIT_FOR_CONFIRMED_BY_THE_OWNER: "待店主确认-云集",
				WAIT_FOR_BUSINESS_AUDIT: "待商家审核-云集",
				WAIT_FOR_AMENDED_PETITION: "待修改申请-云集",
				WAIT_FOR_RETURN_TO_BE_SENT: "待寄回退货-云集",
				WAIT_FOR_CONFIRM_RECEIPT: "待确认收货-云集",
				REFUND_IN_PROGRESS: "退款中-云集",
				REFUND_FAILED: "退款失败-云集",
				REFUNDED: "已退款-云集",
				POST_SALE_CLOSED: "售后关闭-云集"
			},
			taobao_status_options: {
				WAIT_SELLER_AGREE: '买家已经申请退款，等待卖家同意',
				WAIT_BUYER_RETURN_GOODS: '卖家已经同意退款，等待买家退货',
				WAIT_SELLER_CONFIRM_GOODS: '买家已经退货，等待卖家确认收货',
				SELLER_REFUSE_BUYER: '卖家拒绝退款',
				CLOSED: '退款关闭',
				SUCCESS: '退款成功',
				//云集新增
				WAIT_FOR_CONFIRMED_BY_THE_OWNER: "待店主确认-云集",
				WAIT_FOR_BUSINESS_AUDIT: "待商家审核-云集",
				WAIT_FOR_AMENDED_PETITION: "待修改申请-云集",
				WAIT_FOR_RETURN_TO_BE_SENT: "待寄回退货-云集",
				WAIT_FOR_CONFIRM_RECEIPT: "待确认收货-云集",
				REFUND_IN_PROGRESS: "退款中-云集",
				REFUND_FAILED: "退款失败-云集",
				REFUNDED: "已退款-云集",
				POST_SALE_CLOSED: "售后关闭-云集"
			},

			business_status_options: {
				CREATE: '创建',
				SUBMIT: '已提交',
				REJECT: '驳回',
				CHECK: '已核对',
				ING: '退款中',
				FINISH: '已退款',
				CANCELED: '作废',
				RE_TURN: '待重转',
				RE_REVIEW: '反审中',
			},
			order_status_options: {
				'CANCEL': '取消',
				'NORMAL': '生效',
				'WAITING': '等待发运',
				'PART_DELIVERED': '部分发运中',
				'DELIVERED': '已发运'
			},
			refoudDetailList_type_options2: {//额外翻译的
				DELIVERY_FEE: '服务折让',
				THREE_FEE: '三包费',
				O2O_PRICE_DIFF: 'O2O跨店铺差价',
				DISCOUNTING: '外购折现',
			},
			refoudDetailList_type_options: {
				CANCEL: '未发取消',
				OVERPAY: '多付',
				PREFERENTIAL: '未前置优惠',
				SINGLE_DISCOUNT: '单品优惠',
				DELAY: '服务折让',
				COMPENSATION: '销售折让',
				REPAIR: '维修费',
				RETURNS: '退货货款',
				CARRIAGE: '运费',
				THREE: '三包费',
				O2O_DIFF: 'O2O跨店铺差价',
				PRICE_DIFF: '差价',
				DISCOUNT: '外购折现',
				CROSS_OVER: '多付',
				OTHER_REFUND: '其它退款',
				PLATFORM_DISCOUNT: '平台优惠',
				OVER_DISCOUNT: '前置优惠',
				OVER_FUND: '多付-货款',
				INCOME_CARRY: '收入结转后退款'
			},
			info_status_options: {
				UNASKED: '未允许请求',
				ALLOWASKED: '允许请求',
				ASKED: '已请求',
				CANCEL: '已作废'
			},
			pay_method_options: {
				ALIPAY: '支付宝退款',
				BANK: '银行卡退款',
				PROTOCOL: '协议退款',
				DISPUTE: '纠纷退款',
				BAIL: '保证金退款',
				B2C_ONLINE: 'B2C线上退款',
				CARRY_OVER: '换货结转',
				CROSS_OVER_T: '跨单结转',
				DEALER_PREFERENTIAL: '经销优惠退款',
				OVERSEAS_AFTER_REFUND: '海外售后退款',
        YSF: '云闪付退款'
			},
			pay_type_options: __AUX.get('payType').reduce((a, b) => {
				a[b.code] = b.name
				return a
			}, {}),
			operate_type_options: {
				CREATE: '新增',
				SAVE: '保存',
				LOCK: '锁定',
				UNLOCK: '解锁',
				SUBMIT: '提交',
				RETRACT: '撤回',
				AUDIT: '审核',
				CHANGE: '确认变更',
				REVERSE_AUDIT: '反审核',
				SUBMIT_APPROVE: '提交审批',
				PASS: '审批通过',
				NOT_PASS: '审批不通过',
				TRACK: '执行跟踪',
				RECALL: '撤回仓储',
				CLOSE: '关闭',
				OPEN: '反关闭',
				CANCEL: '已取消',
				//extra
				ADD_REFUND_ITEM: '引入退款明细',
				DELETE_REFUND_ITEM: '删除退款明细',
				ADD_TAOBAO_REFUND: '引入复核退款申请',
				DELETE_TAOBAO_REFUND: '删除复核退款申请',
				SUBMITPURCHASE: '提交采购',
				PLAN_RETRACT: '方案撤回',
				REJECT: '驳回',
				LOCKER: '锁定',
				REVOCATION: '撤回',
				VERIFY: '审核',
				OPPOSITE_VERIFY: '反审核',
				SUBMIT_EXAMINE: '提交审批',
				PASS_EXAMINE: '审批通过',
				UNPASS_EXAMINE: '审批不通过',
			},
			// 接口信息-接口状态
			interface_status_options: {
				'-1': '处理失败',
				'1': '处理成功',
				'0': '处理中',
				'2': '已取消',
			},
			category_options: {//分类
				STEP_ORDER: '特权定金',
				PRE_SALE: '预售订单',
				SUCCESSED: '退款成功',
				CLOSED: '退款关闭',
			},

			form: {
				billRefundApplication: { // 基本信息
					refund_way: '',//退款方式
					original_type: 'OTHER',//手工
					bank_name: '',
					opening_bank: '',
					opening_bank_code: '',
					receiver_account: '',
					receiver_name: '',
					original_no: '',
					if_cross_refund: 'N',
					business_status: 'CREATE',
					if_business_lock: null,
					if_finance_lock: null,
					apply_amount: 0,//申请金额合计
					actual_amount: 0,//实退金额合计
					if_ls_refund: '',
					use_for_corss: null, //用途
					settle_method: null,//结算方式
					settle_entity: null,//结算主体
					refund_mode: null,//退款模式
				},
				taobaoRefundDownload: {},
				billRefundApplicationItem: [], //退款明细
				sysPaymentList: [],
				delApplicationItem: [],
				billRefundApplicationReject: [], // 驳回信息
				billRefundApplicationCancel: [], // 反审信息
			},
			newBillRefundApplicationItem: [],
			rules: {
				receiver_name: [{
					required: true,
					validator: (rule, value, callback) => {
						//PROTOCOL: '协议退款',DISPUTE : '纠纷退款',CARRY_OVER  : '换货结转',B2C_ONLINE: 'B2C线上退款',BAIL: '保证金退款'，CROSS_OVER_T: '跨单结转' ，DEALER_PREFERENTIAL: '经销优惠退款' 收款人姓名和收款账号不用必填
						if (!/^(PROTOCOL|DISPUTE|B2C_ONLINE|CARRY_OVER|BAIL|CROSS_OVER_T|DEALER_PREFERENTIAL|YSF|OVERSEAS_AFTER_REFUND)$/.test(this.form.billRefundApplication.refund_way) && !value || value && /^\d+$/.test(value.trim())) {
							this.rules[rule.field][0].message = !value ? '请填写收款人姓名' : '收款人姓名不能为纯数字'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else if (/^BANK|ALIPAY$/.test(this.form.billRefundApplication.refund_way) && value?.includes('*')) {
              this.rules[rule.field][0].message = '收款人姓名不能带*号'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
            } else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写收款人姓名',
				}],
				bank_name: [{
					required: false,
					validator: (rule, value, callback) => {
						if (this.form.billRefundApplication.refund_way === 'BANK' && !value || (value && /^\d+$/.test(value.trim()))) {
							this.rules[rule.field][0].message = !value ? '请填写银行名称' : '银行名称不能为纯数字'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写正确银行名称',
				}],
				opening_bank: [{
					required: false,
					validator: (rule, value, callback) => {
						if (this.form.billRefundApplication.refund_way === 'BANK' && !value) {
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写开户行',
				}],
				refund_way: [{
					required: true,
					validator: (rule, value, callback) => {
						if (!value || Object.keys(this.pay_method_options).indexOf(value) === -1) {
							this.rules[rule.field][0].message = !value ? '请填写退款方式' : '退款方式不符合条件'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写退款方式',
				}],
				merge_trade_no_target: [{
					required: false,
					validator: (rule, value, callback) => {
						if (!value && (this.form.billRefundApplication.if_cross_refund === 'Y' || this.form.billRefundApplication.refund_way === 'CROSS_OVER_T')) {
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请选择目标合并订单',
				}],
				use_for_corss: [{
					required: false,
					validator: (rule, value, callback) => {
						if (!value && (this.form.billRefundApplication.refund_way === 'CROSS_OVER_T')) {
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请选择用途',
				}],
				original_no: [{
					required: true,
					validator: (rule, value, callback) => {
						// 来源单据号是必填的，所以不通过选单的是建不了申请单
						if (/^OTHER$/.test(this.form.billRefundApplication.original_type) && !value) {
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '来源单据号不能为空',
				}],
				receiver_account: [{
					required: true,
					validator: (rule, value, callback) => {
						//PROTOCOL: '协议退款',DISPUTE : '纠纷退款',CARRY_OVER  : '换货结转',B2C_ONLINE: 'B2C线上退款',BAIL: '保证金退款'，CROSS_OVER_T: '跨单结转'，DEALER_PREFERENTIAL: '经销优惠退款' 收款人姓名和收款账号不用必填
						if (!/^(PROTOCOL|DISPUTE|B2C_ONLINE|CARRY_OVER|BAIL|CROSS_OVER_T|DEALER_PREFERENTIAL|YSF|OVERSEAS_AFTER_REFUND)$/.test(this.form.billRefundApplication.refund_way) && !value) {
							this.rules[rule.field][0].message = '请填写收款账号'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else if (['BANK'].includes(this.form.billRefundApplication.refund_way) && !/^\d+$/.test(value)) {
							this.rules[rule.field][0].message = '收款账号只能为纯数字'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						} else if (['ALIPAY'].includes(this.form.billRefundApplication.refund_way) && value?.includes('*')) {
							this.rules[rule.field][0].message = '退款账号不能带*号'
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
            } else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写收款账号',
				}],
				...[{
					saleman_name: '业务员'
				}, {
					merge_trade_no: '合并订单号'
				}, {
					nick_name: '买家昵称'
				}, {
					original_shop_name: '原始店铺'
				}, {
					shop_name: '订单店铺'
				}, {
					user_shop_name: '客服店铺'
				}].reduce((a, b) => {
					var key = Object.keys(b)[0]
					a[key] = VL.isNotBlank({
						self: this,
						msg: '请填写' + b[key],
					})
					return a
				}, {})
			},
			businessUNLockLoad: false, // 判断是否点击了业务解锁
			freshSaveStatus: false,
			reason_version: 4, // 固化原因格式的当前版本号
			refundReasonList1: {
				'OVERPAY': {// 多付
					'多付': '多付:()多付()元',
					'单品优惠已前置': '单品优惠已前置:()单品参与()的()活动退()元',
					'订单优惠已前置': '订单优惠已前置:参与()的()活动退()元'
				},
				'PRICE_DIFF': {// 差价
					'单个产品': '单个产品:()按()()退差价()元',
					'组合': '组合:()组合按()()退差价()元',
					'多个单品退组合': '多个单品退组合:()构成()组合+()，按()()退差价()元',
					'跨合并单号的单品退组合': '跨合并单号的单品退组合:()+(),构成()组合+()，按()()退差价()元'
				},
				'PREFERENTIAL': '参与()的()退()元', // 未前置优惠
				'SINGLE_DISCOUNT': '()参与()的()退()元', // 单品优惠
				'RETURNS': '()()退货,()元,实退()元', // 退货货款
				'CARRIAGE': '()退运费()元', // 运费
				'THREE': '()退三包费()元', // 三包费
				'REPAIR': '()维修，退()元', // 维修费
				'COMPENSATION': '()补偿()元', // 销售折让
				'DISCOUNT': '()的()赠送()，要求折现()元', // 外购折现
			},
			refundReasonList2: {
				'OVERPAY': {// 版本2禁用多付
					'多付': '多付:()多付()元',
					'单品优惠已前置': '单品优惠已前置:()单品参与()的()活动退()元',
					'订单优惠已前置': '订单优惠已前置:参与()的()活动退()元'
				},
				'PRICE_DIFF': '按()的()减()的到手价()减实际支付到手价()=差额()元', // 差价
				//'PRICE_DIFF': '商品()按虚拟拍单时间()的到手价()退差额()元', // 差价

				'PREFERENTIAL': '参与()退()元', // 未前置优惠
				'SINGLE_DISCOUNT': '()参与()的()退()元', // 版本2禁用单品优惠
				'RETURNS': {// 退货货款
					'单个产品退货': '单个产品退货:()退货，买家承担()元，实退()元',
					'整单退货': '整单退货:整单()退货，买家承担()元，实退()元'
				},
				'CARRIAGE': '()退运费()元', // 运费
				'THREE': '()退三包费()元', // 三包费
				'REPAIR': '()维修，退()元', // 维修费
				'COMPENSATION': '()折让()元', // 销售折让
				'DISCOUNT': '参与()赠送()，折现()元', // 外购折现
				'OVER_DISCOUNT': '参与()退()元',
				'OVER_FUND': '多付()元',
				'CROSS_OVER': '多付()元',
			},
			refundReasonList3: {
				'OVERPAY': '多付()元',
				'PRICE_DIFF': '商品()按虚拟拍单时间()的到手价()退差额()元', // 差价
				// 'PRICE_DIFF': '按()的()减()的到手价()减实际支付到手价()=差额()元',//  差价
				'PREFERENTIAL': '参与()退()元', // 未前置优惠
				'RETURNS': {// 退货货款
					'单个产品退货': '单个产品退货:()退货，买家承担()元，实退()元',
					'整单退货': '整单退货:整单()退货，买家承担()元，实退()元'
				},
				'CARRIAGE': '()退运费()元', // 运费
				'THREE': '()退三包费()元', // 三包费
				'REPAIR': '()维修，退()元', // 维修费
				'COMPENSATION': '()折让()元', // 销售折让
				'DISCOUNT': '参与()赠送()，折现()元', // 外购折现
				'OVER_DISCOUNT': '参与()退()元',
				'OVER_FUND': '多付()元', //版本3禁用
				'CROSS_OVER': '多付()元',
			},
			refundReasonList4: {
				'OVERPAY': '多付()元',
				'PRICE_DIFF': '商品()按虚拟拍单时间()的到手价()退差额()元',//  差价
				'PREFERENTIAL': '参与()退()元', // 未前置优惠
				'RETURNS': {// 退货货款
					'单个产品退货': '单个产品退货:()退货，买家承担()元，实退()元',
					'整单退货': '整单退货:整单()退货，买家承担()元，实退()元'
				},
				'CARRIAGE': '()退运费()元', // 运费
				'THREE': '()退三包费()元', // 三包费
				'REPAIR': '()维修，退()元', // 维修费
				'COMPENSATION': '()折让()元', // 销售折让
				'DISCOUNT': '参与()赠送()，折现()元', // 外购折现
				'OVER_DISCOUNT': '参与()退()元',
				'OVER_FUND': '多付()元', //版本3禁用
				'CROSS_OVER': '多付()元',
			},
			InfoRequestList: [],
			infoSelect: [],
			refund_type_option: {},
      refundOriginTypeCodeList: [],//复核锁定-来源店铺类型-编码
			showBtn: false,
			priceDiffGoodsList: [], //仅为差价功能使用-销售订单下的商品
			differenceAccountObj: { //差价缓存的数据
				basicSelectList: [],
				accountCouponList: [],
				accountBasicList: [],
				order_date: '',
				priceListOrders: []
			},
			oldDifferenceAccountObj: { //数据库中保存的数据
				basicSelectList: [],
				accountCouponList: [],
				accountBasicList: [],
				order_date: ''
			},
			changeApplyAmountList: [], //差价商品退款金额的更改
			selectTab: 'differenceAccountBasic3',
			differenceSearchObj: {
				order_date: null
			},
			differenceAccountFormRules: {},
			accountBasicList: [],
			accountBasicColsList: [
				{
					label: '商品编码',
					prop: 'materia_number',
					width: 120,
				}, {
					label: '商品名称',
					prop: 'materia_name',
					width: 100,
				}, {
					label: '商品规格描述',
					prop: 'materia_specifications',
				}, {
					label: '原系统售价',
					prop: 'stand_price',
				}, {
					label: '原实际售价',
					prop: 'act_price_v2',
				}, {
					label: '新系统售价',
					prop: 'virtual_stand_price',
				}, {
					label: '使用优惠',
					prop: 'virtual_discount_amount',
				}, {
					label: '新实际售价',
					prop: 'virtual_act_price_price',
				}, {
					label: '已退差价',
					prop: 'refunded_diff_price_v2'
				}, {
					label: '退款中差价',
					prop: 'refunding_diff_price_v2'
				}, {
					label: '是否跨店退款',
					prop: 'if_cross_shop',
					format: 'yesOrNo',
					width: 80,
				}, {
					label: '跨店退差店铺',
					prop: 'cross_shop_name',
					width: 80,
				},
			],
			accountCouponList: [],
			accountCouponBtns: [
				{
					type: 'primary',
					txt: '添加',
					click: self.add,
					disabled() {
						return true
					}
				}, {
					type: 'primary',
					txt: '删除',
					click: self.remove,
					disabled() {
						return true
					}
				}, {
					type: 'primary',
					txt: '刷新可退差价',
					click: self.save,
					disabled() {
						return true
					}
				}
			],
			accountCouponCols: [
				{
					label: '优惠活动编码',
					prop: 'discount_no',
				}, {
					label: '优惠项目编码',
					prop: 'discount_item_no',
				}, {
					label: '优惠活动',
					prop: 'activity_name',
					width: 150
				}, {
					label: '优惠影响范围',
					prop: 'discount_effect_area',
					format: 'auxFormat',
					formatParams: 'DISCOUNT_EFFECT_AREA',
				}, {
					label: '优惠类型',
					prop: 'activity_type',
					format: 'auxFormat',
					formatParams: 'discountCategory',
				}, {
					label: '优惠子类型',
					prop: 'activity_subclass',
					format: 'auxFormat',
					formatParams: 'discountSubclass',
				}, {
					label: '项目细类',
					prop: 'item_type_detail_code',
					format: 'auxFormat',
					formatParams: 'ITEM_TYPE_DETAIL',
				}, {
					label: '优惠金额',
					prop: 'discount',
				}, {
					label: '优惠券数量',
					prop: 'num',
					width: 136
				}
			],
			operate_time_begin: '',
			operate_time_end: '',
			created: '',
			end_time: '',
			ifChange: false,
			oldBillRefundApplicationItem: [],
			selectMergeOrderObj: {
				audit_status_where: [],
				scm_batch_trade: []
			},
			refreshOldData: {},
			isAuditRefundLoading: false,//复核退款按钮请求中...
			isCheckLoading: false,//核对按钮请求中...
			forceInvalidLoading: false,//强制作废中
			isPush: false,//是否下推
			isMember: false,//是否是退款高级驳回权限管理员
			updateShopRefundSourceLoading: false,//修改经销关店退款来源
			refund_mode_copy: "",//退款模式备份判断是否切换用
			jd_shops: [],//辅助资料配置的所有退款模式相关的京东店铺
			manualRefundAutoSubmitLoading: false, //自动提交是否loading中

			isBusiness: false,
			isMounted: false,

			scmBatchTradeList: [],
			scmBatchTradeCol: [
				{
					label: '物料编码',
					prop: 'material_number',
				},
				{
					label: '物料名称',
					prop: 'material_name',
				},
				{
					label: '规格描述',
					prop: 'materia_specifications',
				},
				{
					label: '来源商品编码',
					prop: 'source_material_number',
					width: 150
				},
				{
					label: '来源商品名称',
					prop: 'source_material_name',
					width: 150
				},
				{
					label: '实际售价',
					prop: 'act_price',
				},
				{
					label: '批次单号',
					prop: 'batch_trade_no',
					width: 215,
					redirectClick(row) {
						let params = {};
						params.batch_trade_id = row.batch_trade_id;
						params.merge_trade_id = row.merge_trade_id;
						params.merge_trade_no = self.form.billRefundApplication.merge_trade_no
						self.$root.eventHandle.$emit('creatTab', {
							name: "批次订单详情",
							params: params,
							component: () => import('@components/order/batch_order.vue')
						});
					}
				},
				{
					label: '批次分单',
					prop: 'outbound_notice_no',
				},
				{
					label: '4PL接口状态',
					prop: 'zd_interface_status',
					formatter(val) {
						switch (val) {
							case 'NOT_READY': return "无下达";
							case 'SENT_TO_ZD': return "已发送";
							case 'DELIVERED': return "已发货";
							case 'PASS_FINANCIAL_AUDIT': return '已财审';
							case 'CANCEL': return '已取消';
							case 'FAIL': return '发送失败';
							case 'SENDING': return '发送中';
							case 'SENDLOCK': return '作业中';
							case 'REVOKING': return '撤回中';
							case 'SENT_TO_SCM': return '已发送SCM';
							default: return val;
						}
					}
				},
				{
					label: '打印锁定状态',
					prop: 'zd_print_lock_status',
					width: 100,
					formatter(val) {
						if (val === 'Y') {
							return '已锁定';
						} else {
							return '未锁定';
						}
					}
				},
				{
					label: '打印锁定时间',
					prop: 'zd_print_lock_date',
					format: 'dataFormat1',
					width: 130
				},
				{
					label: '计划发货日期',
					format: 'dataFormat1',
					prop: 'zd_delivery_time',
					width: 130
				},
				{
					label: '发货状态',
					prop: 'zd_delivery_status',
					formatter(val) {
						switch (val) {
							case 'N': return "未发货"; case 'Y': return "已发货";
						}
					}
				},
				{
					label: '发货日期',
					format: 'dataFormat1',
					prop: 'zd_delivery_time',
					width: 130
				},
				{
					label: '出库状态',
					prop: 'k_out_stock_status',
					formatter(val) {
						return val === 'Y' ? '已出库' : '未出库';
					}
				},
				{
					label: '出库日期',
					prop: 'k_out_stock_date',
					format: 'dataFormat1',
					width: 130
				},
				{
					label: '送货方式',
					prop: 'deliver_method',
					format: 'auxFormat',
					formatParams: 'deliver_method',
				},
				{
					label: '实发仓库',
					prop: 'outbound_warehouse_name',
				},
				{
					label: '物流单号',
					prop: 'zd_logistics_number',
				},
			],
			scmBatchBtn: [
				{
					type: 'primary',
					txt: '截货申请',
					click: self.interceptGoodsRequest,
					disabled: false,
				},
			],
			scmBatchSelectionList: [],
			statusList: { CREATE: "待办", PRE_HANDLE: "售前处理", WAIT_HANDLE: "售后待办", AFTER_HANDLE: "售后处理", FINISH: "完结", CANCELLATION: "作废" },
			aftersaleOrderCol: [
				{
					label: '售后单',
					width: '120',
					prop: 'after_order_no',
					redirectClick(row) {
						self.viewDetail(row.id)
					}
				},
				{
					label: '业务状态',
					prop: 'status',
					formatter: function (row) {
						return self.statusList[row];
					}
				},
				{
					label: '业务备注',
					prop: 'remark'
				},
				{
					label: '是否三个月前售后',
					width: '110',
					prop: 'if_three_age',
					formatter: prop => prop ? '是' : '否',
				},
				{
					label: '推荐处理人',
					width: '90',
					prop: 'recommend_handler_name'
				},
				{
					label: '业务锁定人',
					prop: 'locker_name'
				},
				{
					label: '业务锁定人分组',
					width: '130',
					prop: 'locker_group_name'
				},
				{
					label: '业务锁定日期',
					width: '90',
					prop: 'lock_time',
					format: 'dataFormat',
				},
				{
					label: '创建人',
					width: '90',
					prop: 'creator_name'
				},
				{
					label: '创建时间',
					width: '150',
					prop: 'create_time',
					format: "dataFormat1"
				}
			],
			aftersaleOrderList: [],
			aftersaleOrderBtn: [
				{
					type: 'primary',
					txt: '下推售后单',
					click: self.pushAfterSaleOrder,
					disabled: false,
				}
			],
			rejectReasonObj: __AUX.get('sh_reject_reason').reduce((a, b) => {
				a[b.code] = b.name
				return a
			}, {}),
      refundShopCodes: __AUX.get('manual_sync_refund_shop').map(item=>item.code),
		}
	},
	methods: {
    setSigns() {
      const ids=[this.params.id];
      let progressValue = this.progressLabel;
      if (this.progressLabel === 'CANCEL_MARK') {
        progressValue = '';
      }
      this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/progressLabel", { ids: ids, progressLabel: progressValue }, res => {
        if (res.body.result) {
          this.$message.success(this.progressLabel === 'CANCEL_MARK' ? '取消标记成功' : res.body.msg);
          this.getOrderDetail(false);
        } else {
          this.$message.error(res.body.msg);
        }
      });
    },
    omitDownload() {
      // #BHWL-265 抖音来客需要特殊处理
      if(!this.refundShopCodes.includes(this.form.shop_code) && this.form.billRefundApplication.original_type !== 'DYLK'){
        this.$message.warning('该订单店铺不支持漏单下载')
        return
      }
      const currentList=[
        {
          shop_code:this.form.billRefundApplication.original_type === 'DYLK' ? 'DYLK' : this.form.shop_code,
          refund_id:this.form.taobaoRefundDownload.refund_id
        }
      ]
			this.$root.eventHandle.$emit("alert", {
				title: "漏单下载",
				style: "width:900px;height:600px",
				component: () =>
					import("@components/after_sales_refund/omitDownloadPopup.vue"),
				params: {
          list:currentList,
					callback: () => { },
				},
			});
		},
		handleSave() {
			const self = this
			self.save()
		},
		viewDetail(id) {
			var params = {
				id,
				idList: Array.from(new Set/*去重*/(this.aftersaleOrderList.map(obj => obj.id))),//用于售后单详情的上一页/下一页
			};

			if (document.querySelector('[data-symbol="after_order_id_' + id + '"]')) {
				this.$message.error('该售后单已经打开')
			} else {
				this.$root.eventHandle.$emit('creatTab', {
					name: "售后单详情",
					params: params,
					component: () => import('@components/after_sales/index')
				});
			}
		},
		handleScmBatchSelectionChange(val) {
			this.scmBatchSelectionList = val;
		},
		interceptGoodsRequest() {
			//截货申请
			const scmBatchBtnDisabled = !([this.form.billRefundApplication.saleman_id, this.form.billRefundApplication.business_lock_person].includes(this.getEmployeeInfo("id")));
			if (scmBatchBtnDisabled) {
				this.$message.warning("当前业务代理人为当前退款单业务员或业务锁定人才可操作！");
				return
			}
			if (this.scmBatchSelectionList.length === 0) {
				this.$message.warning("至少选择一行！");
				return;
			}
			//校验选中的当前批次单的送货方式是否=三包、物流、快递、快递收费、仓库自提
			const deliverMethodPass = this.scmBatchSelectionList.every((item) => ['THREES', 'LOGISTICS', 'PICK_UP', 'EXPRESS', 'EXPRESS_PAY'].includes(item.deliver_method));
			if (!deliverMethodPass) {
				this.$message.warning("只有送货方式=三包、物流、快递、快递收费、仓库自提的单据，才支持库内截货！");
				return;
			}
			//校验选中的当前批次单是否已打印锁定
			const lockPass = this.scmBatchSelectionList.every((item) => item.zd_print_lock_status === 'Y');
			if (!lockPass) {
				this.$message.warning("只有已打印锁定后的单据，才可截货！");
				return;
			}
			const ids = this.scmBatchSelectionList.map((item) => item.materiel_id);
			const sourceNumbers = this.scmBatchSelectionList.map((item) => item.source_material_number);
			const { nick_name, id, merge_trade_no } = this.form.billRefundApplication;
			//校验m
			const checkParams = {
				sourceNumbers,
				refundId: id
			}
			this.$root.eventHandle.$emit("alert", {
				title: "截货申请",
				style: "width:600px;height:300px",
				params: {
					checkParams,
					callback: (data) => {
						const params = {
							mIds: ids,
							type: 'REFUND',
							sourceId: id,
							buyerName: nick_name,
							merge_trade_no,
							interceptReason: data.reason,
              ifAutoReturns: data.ifAutoReturns,
              returnsType: data.returnsType,
              changeGoodsType: data.changeGoodsType
						}
						this.ajax.postStream(
							`/afterSale-web/api/aftersale/interceptNotice/saveAndSubmit`,
							params,
							(res) => {
								if (res.body.result) {
									this.$message.success(res.body.msg);
									this.getScmBatchTradeList();
								} else {
									this.$message.error(res.body.msg);
								}
							},
							(err) => {
								this.$message.error(err);
							}
						);
					},
				},
				component: () =>
					import("@components/after_sales_refund/components/intercept-goods-request"),
			});
		},
		rejectRefund() {
			if (!this.form.billRefundApplication.reject_remark) {
				this.$message.error('请先输入驳回备注')
				return
			}
			if (!this.form.billRefundApplication.reject_reason) {
				this.$message.error('请选择驳回原因')
				return
			}
			this.ajaxFunc(
				this._delPermissionCodeWhenDealerUser('reject', !/^(DISPUTE|BAIL|PROTOCOL)$/.test(this.form.billRefundApplication.refund_way)),
				{
					id: this.form.billRefundApplication.id,
					reject_remark: this.form.billRefundApplication.reject_remark,
					reject_reason: this.form.billRefundApplication.reject_reason,
				},
				res => this.getOrderDetail(res.body.msg)
			)
		},
		handleTabClick() {
			if (this.selectTab1 === 'batchInfo') {
				this.getScmBatchTradeList()
			}
			if (this.selectTab1 === 'aftersaleOrder') {
				this.aftersaleOrderList = this.form.aftersaleOrderList || []
			}
		},
		getScmBatchTradeList() {
			this.ajax.postStream("/order-web/api/batchtrade/listBySysTradeId", this.form.billRefundApplication.sys_trade_id, res => {
				if (res.body.result && res.body.content) {
					this.scmBatchTradeList = res.body.content
				}
			}, err => {
				this.$message.error(err)
			})
		},
		recheckOrder() {
			let self = this
			this.$root.eventHandle.$emit('alert', {
				title: '反审',
				style: 'width:500px;height:200px',
				component: () => import('@components/after_sales_refund/components/remark_dialog.vue'),
				params: {
					callback: remark => {
						self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/cancelK3Bill', {
							id: self.form.billRefundApplication.id,
							cancel_remark: remark,
						}, res => {
							let { result, msg } = res.body
							if (result) {
								self.$message.success(msg)
								self._refresh(true)
							} else {
								self.$message.error(msg)
							}
						})
					}
				},
			})
		},
		getDefaultDealerCustomer() {
			this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttributeList', {
				"personId": this.getEmployeeInfo('personId'),
				status: 1,
				attribute: "REFUND_APPLICATION_CANCEL"
			}, res => {
				let { result, content } = res.body
				if (result && content.count === 1 && content.list[0].attributeValueText === '是') this.isBusiness = true
			})
		},
		cancelSnapshot(id) {
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/cancelSnapshot', { id }, res => {
				if (res.body.result) {
					this.$root.eventHandle.$emit('creatTab', {
						name: '新退款申请单(快照)',
						params: { snapshotObj: res.body },
						component: () => import('@components/after_sales_refund/refundRequest_3')
					})
				} else {
					this.$message.error(res.body.msg)
				}
			})
		},
		bankListAlert() {
			if (!this.isCanEdit || /^(PROTOCOL|DISPUTE|B2C_ONLINE|CARRY_OVER)$/.test(this.form.billRefundApplication.refund_way)) return
			this.$root.eventHandle.$emit('alert', {
				title: '请选择开户行',
				style: 'width:800px;height:600px',
				component: () => import('@components/after_sales_refund/bankListAlert.vue'),
				params: {
					callback: res => {
						this.form.billRefundApplication.bank_name = res.bank_name
						this.form.billRefundApplication.opening_bank = res.fname
						this.form.billRefundApplication.opening_bank_code = res.fnumber
					}
				},
			})
		},
		updateRefundBusinessStatus() {
			let self = this;
			let id = this.form.billRefundApplication.id;

			if (!this.form.billRefundApplication) {
				this.$message.error("退款单id为空，无法操作");
				return;
			}
			self.$root.eventHandle.$emit('alert', {
				params: {
					id: id,
					callback(data) {
						if (data) {
							self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/updateRefundBusinessStatus', { id: id, business_status: data.business_status }, res => {
								if (res.body.result) {
									self.$message.success(res.body.msg)
									setTimeout(() => {
										self.getOrderDetail()
									}, 1000)
								} else {
									self.$message.error(res.body.msg)
								}
							})
						}
					}
				},
				component: () => import("@components/after_sales/updateRefundBusinessStatus"),
				style: 'width:400px;height:200px',
				title: '修改业务状态'
			});
		},
		// 自动提交
		manualRefundAutoSubmit() {
			this.manualRefundAutoSubmitLoading = true
			let postData = {
				id: this.form.billRefundApplication.id,
			}
			this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/manualRefundAutoSubmit", postData, res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				this.manualRefundAutoSubmitLoading = false
			}, err => {
				this.$message.error(err)
				this.manualRefundAutoSubmitLoading = false
			})
		},
		//检测是否可以操作收入结转后退款
		checkIfIncomeCarry() {
			let params = {
				merge_trade_id: this.form.billRefundApplication.merge_trade_id,
			}
			this.form.billRefundApplication.id ? params.id = this.form.billRefundApplication.id : ''
			this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundIncomeCarry/checkIfDo", params, res => {
				if (res.body.result) {
					this.addRefundType('INCOME_CARRY')
				} else {
					this.$message.error(res.body.msg)
				}
			}, err => {
				this.$message.error(err)
			})
		},
		closeIconClick() {
			this.showBtn = true;
		},
		sureChange() {
			let self = this;
			let status = (!self.form.billRefundApplication.id || (self.isSameBusinessLockPerson && /^(CREATE|REJECT)$/.test(self.form.billRefundApplication.business_status))
				|| (self.isSameFinanceLockPerson && self.form.billRefundApplication.if_finance_lock === 'Y')
				|| (self.form.billRefundApplication.business_status === 'CHECK' && self.form.billRefundApplication.if_review_lock === 'Y' && self.isSamereviewLockPerson))
			if (!status) return false;
			this.editFinance(() => {
				this.showBtn = false;
				this.getOrderDetail();
				this.getBrowseList(this.params.id);
			});
		},
		querySearchAsync(queryString, cb) {
			this.showBtn = true;
			cb([]);
		},
		cancelChange() {
			this.showBtn = false;
		},
		postCallback(d) {
				params = {
					id: this.form.billRefundApplication.id,
					if_exist_file: 'Y'
				}
			if (this.form.billRefundApplication.if_exist_file && this.form.billRefundApplication.if_exist_file != 'Y') {
				this.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/updateIfExistFile", params, res => {
					if (res.body.result) {
						this.$message.success(res.body.msg)
						this.form.billRefundApplication.if_exist_file = 'Y'
					} else {
						this.$message.error(res.body.msg)
					}
				})
			}
		},
		checkRefundReason(row, type) {
			row.refundReason = type
			let scrStr = ''
			if (/^(RETURNS)$/.test(row.refund_type)) {
				scrStr = this.refundReasonList2[row.refund_type][row.refundReason]
			}
			let idx = this.getStrCount(scrStr, '(')
			while (idx--) {
				let ii = 'filed' + idx
				row.excessiveParam['filed' + idx] = ''
			}
			this.$set(row, 'refundReason', type)
		},
		getStrCount(scrstr, armstr) { //scrstr 源字符串 armstr 特殊字符
			let count = 0
			while (scrstr.indexOf(armstr) != -1) {
				scrstr = scrstr.replace(armstr, "")
				count++
			}
			return count
		},
		/*
		获取活动优惠
		*/
		getCouponList(d, resolve) {
			if (d) {
				this.couponSearchObj.where = d
			}
			let slef = this;
			this.couponSearchObj.merge_trade_id = this.form.billRefundApplication.merge_trade_id;
			this.ajax.postStream("/order-web/api/mergetrade/discount/list", this.couponSearchObj, res => {
				if (res.body.result) {
					if (res.body.content && res.body.content.length > 0) {
						slef.couponList = res.body.content;
					} else {
						slef.couponList = [{}]
					}
				} else {
					this.$message.error(res.body.msg);
				}
				this.couponSelect = ''
				resolve && resolve();
			});
		},
		// 业务解锁
		async unLockBtn() {
			var self = this
			this.testBtn = false
			this.businessUNLockLoading = true
			this.businessUNLockLoad = true
			await this.ajaxFunc(
				self._delPermissionCodeWhenDealerUser('businessUNLock'), res => {
					self.getOrderDetail(res.body.msg)
				}
			)
			self.businessUNLockLoading = false
		},
		// 业务锁定
		takenLock() {
			var self = this
			this.testBtn = true
			this.businessUNLockLoad = false
			this.ajaxFunc(self._delPermissionCodeWhenDealerUser('businessLock'), res => self.getOrderDetail(res.body.msg))
		},
		// 初始化按钮组
		initBtnGroup() {
			var self = this
			var btnsList = __AUX.get('tkxszfl')
			btnsList.forEach(function (item, index) {
				self.groupList.push({
					type: 'primary',
					txt: item.name,
					value: item.code,
					isDisabled: item.status == '0',
					click: function () {
						self.testClick(item)
					}
				})
				self.refund_type_subNmae[item.code] = item.name
			})
		},
		testClick(value) {
			if (value === 'PREFERENTIAL') {
				this.$message.error('请勾选退款子分类')
			} else {
				this.addRefundType('PREFERENTIAL', ' ', value.code)
			}
		},
		// 目标合并订单清空事件
		mergeTradeNoTargetClear() {
			if (this.form.billRefundApplicationItem.length) {
				this.$message.error('退款明细行已关联来源单号，请先删除所有明细行')
			} else {
				this.form.billRefundApplication.sys_trade_id_target = ''
				this.form.billRefundApplication.sys_trade_no_target = ''
				this.form.billRefundApplication.merge_trade_id_target = ''
				this.$set(this.form.billRefundApplication, 'merge_trade_no_target', '')
			}
		},
		// 是否跨单退款@change事件
		ifCrossRefundChange() {
			/*跨单退款按钮控制，退款方式=(纠纷/协议/且为天猫退款TMALL)换货结转且为手工退款OTHER 才可操作*/
			if (!/^(CARRY_OVER||DISPUTE|PROTOCOL)$/.test(this.form.billRefundApplication.refund_way)) {
				return
			}
			window.clearTimeout(this.ifCrossRefundChange.setTimer)
			this.ifCrossRefundChange.setTimer = setTimeout(() => {//防止触发两次
				var newVal = this.form.billRefundApplication.if_cross_refund
				if (this.form.billRefundApplicationItem.length > 0) {
					this.$message.error('请先清空退款明细行')
					this.form.billRefundApplication.if_cross_refund = newVal === 'Y' ? 'N' : 'Y'
					return
				}

				if (newVal === 'Y') {
					this.getCROSSOVERCanClick(() => {
						this.rules.merge_trade_no_target[0].required = true
					}, () => {
						// 校验没有特权时，是否跨单退款不能为Y
						this.form.billRefundApplication.if_cross_refund = 'N'
					})
				} else {
					this.mergeTradeNoTargetClear()
					this.rules.merge_trade_no_target[0].required = false
				}
			})
		},
		// 取消结转按钮
		cancelCarryOver() {
			if (
				this.selectRefoudDetailLists.length === 0
				|| this.selectRefoudDetailLists.some(
					o => !(o.id && (o.if_cancel_refund === 'N' || (/否/.test(o.if_cancel_refund) && !/是/.test(o.if_cancel_refund))))
				)
			) {
				this.$message.error('亲，请选择未取消结转的行')
				return
			}

			this.$root.eventHandle.$emit('openDialog', {
				txt: '亲，确认取消已选择的退款行？',
				okTxt: '确认',
				noShowNoTxt: true,
				ok: () => {
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/cancelCarryOver?permissionCode=REFUND_APPLY_ORDER_CANCEL_CARRY_OVER', {
						billRefundApplication: { id: this.form.billRefundApplication.id },
						billRefundApplicationItem: this.selectRefoudDetailLists.map(o => ({
							id: o.id,
							xentry_id: o.xentry_id,
						})),
					}, res => {
						if (res.body.result) {
							this.getOrderDetail(res.body.msg)
						} else {
							this.$message.error(res.body.msg)
						}
					})
				},
			})
		},
		// 提交初审前校验
		submitCheck() {
			var func = () => {
				this.submitLoading = true
				this.businessUNLockLoading = true
				this.save(() => {
					setTimeout(() => {
						this.ajaxFunc(
							// 权限控制
							this._delPermissionCodeWhenDealerUser('submit'),
							res => {
								this.submitLoading = false
								this.businessUNLockLoading = false
								this.getOrderDetail(res.body.msg)
							},
							() => {
								this.submitLoading = false
								this.businessUNLockLoading = false
								this.getOrderDetail(false)
							}
						)
					}, 1000)
				}, () => {
					this.submitLoading = false
					this.businessUNLockLoading = false
				})
			}

			func()
		},
		toTaobaoDownloadDetail(after_refund_download_id) {
			this.$root.eventHandle.$emit('creatTab', {
				name: '退款申请单平台下载查看',
				params: { after_refund_download_id },
				component: () => import('@components/after_sales_refund/taobaoDownloadDetail.vue')
			})
		},
		// 业务员选择
		selectBestStaff() {
			this.$root.eventHandle.$emit('alert', {
				title: '业务员列表',
				style: 'width:800px;height:600px',
				component: () => import('@components/after_sales_common/selectPickrecommendhandler'),
				params: {
					callback: d => {
						this.form.billRefundApplication.saleman_id = d.id

						this.$set(this.form.billRefundApplication, 'saleman_name', d._real_name_nick_name)//业务员
						this.$set(this.form.billRefundApplication, 'saleman_group_name', d.group_name)//分组
					}
				},
			})
		},
		// 跳转价目表详情
		toPriceListDetail(id) {
			this.$root.eventHandle.$emit('creatTab', {
				name: '编辑价格详情',
				params: { id },
				component: () => import('@components/goodsprice/price')
			})
		},
		// 买家昵称
		selectBuyersName() {
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/customers/list'),
				style: 'width:900px;height:600px',
				title: '买家昵称列表',
				params: {
					close: d => {
						if (this.form.billRefundApplication.nick_id !== d.cust_id) {
							this.form.billRefundApplication.nick_id = d.cust_id
							this.selectReceiverAccount('fetch')
						}

						this.$set(this.form.billRefundApplication, 'nick_name', d.name)
					}
				},
			})
		},
		// 无源单时，店铺选择
		selectShop(idName, valName) {
			const self = this;
			self.$root.eventHandle.$emit('alert', {
				title: '店铺列表',
				style: 'width:800px;height:600px',
				component: () => import('@components/shop/list.vue'),
				params: {
					selection: 'radio',
					callback: d => {
						self.$set(self.form.billRefundApplication, valName, d.shop_name)
						self.$set(self.form.billRefundApplication, idName, d.shop_id)

						if (idName === 'shop_id') {
							//选中订单店铺后要清空退款方式，避免通过选了下面特定店铺后选中协议或纠纷后再选其他订单店铺的漏洞
							/*
							手工新增的退款申请单时：
							1、退款方式要禁掉B2C线上退款
							2、非苏宁，贝店的订单店铺时，协议退款禁掉
							3、非京东、唯品会、苏宁、贝店时，纠纷退款也禁掉
							 */
              self.shopInfo = d;
							self.$set(self.form.billRefundApplication, 'refund_way', '')
						}
					}
				},
			})
		},
		// 计算退款一览页签合计行
		calcBrowseTotal(data, calcCols) {
			var totalObj = {}

			calcCols.forEach(col => totalObj[col] = 0)

			data.forEach(obj => {
				calcCols.forEach(col => {
					totalObj[col] += Number(obj[col]) || 0
				})
			})

			Object.keys(totalObj).forEach(col => {
				totalObj[col] = '合计' + Number(totalObj[col].toFixed(10))
			})

			return data.concat(totalObj)
		},
		calcBrowseTotal2(data, calcCols) {
			var totalObj = {}

			calcCols.forEach(col => totalObj[col] = 0)

			data.forEach(obj => {
				calcCols.forEach(col => {
					totalObj[col] += Number(obj[col]) || 0
				})
			});

			Object.keys(totalObj).forEach(col => {
				totalObj[col] = '合计' + Number(totalObj[col].toFixed(10))
			});

			return totalObj
		},
		openOrder(id, sub_bill_no) {
			this.$root.eventHandle.$emit('creatTab', {
				name: '责任分析单详情',
				params: {
					id,
					sub_bill_no,
					orderList: JSON.parse(JSON.stringify(this.newBillRefundApplicationItem)),
				},
				component: () => import('@components/duty/return_detail')
			})
		},
		// 来源单号跳转详情
		toOriginalNoDetail(row) {
			var refund_type = row.refund_type

			if (/^(CANCEL|DELAY|SINGLE_DISCOUNT|CROSS_OVER|O2O_DIFF|PRICE_DIFF|OVERPAY|PREFERENTIAL|OTHER_REFUND|OVER_DISCOUNT|OVER_FUND)$/.test(refund_type)) {
				this.$root.eventHandle.$emit('creatTab', {
					name: '合并订单详情',
					params: {
						merge_trade_id:
							this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && row.refund_type === 'CROSS_OVER'
								? this.form.billRefundApplication.merge_trade_id
								: row.original_id
					},
					component: () => import('@components/order/merge.vue'),
				})
			} else if (/^(CARRIAGE|THREE|DISCOUNT)$/.test(refund_type)) {
				this.selectMergeOrderObj.scm_batch_trade[0].value = row.original_no

				this.ajax.postStream('/order-web/api/batchtrade/list?permissionCode=ORDER_BATCHORDER_QUERY', {
					"batch_trade_no": "",
					"merge_trade_id": "",
					"page_no": 1,
					"page_size": 20,
					"page_name": "scm_batch_trade",
					"where": this.selectMergeOrderObj.scm_batch_trade,
				}, res => {
					res = res.body.content.list[0]
					this.$root.eventHandle.$emit('creatTab', {
						name: '批次订单详情',
						params: {
							batch_trade_id: row.original_id,
							merge_trade_id: res.merge_trade_id,
							merge_trade_no: res.merge_trade_no,
						},
						component: () => import('@components/order/batch_order.vue'),
					})
				})
			} else if (/^(COMPENSATION|REPAIR)$/.test(refund_type)) {
				this.$root.eventHandle.$emit('creatTab', {
					name: '售后单详情',
					params: { id: row.original_id },
					component: () => import('@components/after_sales/index'),
				})
			} else if (/^(RETURNS)$/.test(refund_type)) {
				this.$root.eventHandle.$emit('creatTab', {
					name: '退货跟踪单详情',
					params: { id: row.original_id },
					component: () => import('@components/after_invoices/returnexchangedetail_2'),
				})
			} else if (/^(INCOME_CARRY)$/.test(refund_type)) {
				this.$root.eventHandle.$emit('creatTab', {
					name: '收入结转单列表',
					component: () => import('@components/receipt/receipt_transfer_list.vue'),
				})
			}
		},
		// 上一页、下一页
		nextOrPrevOrder(type) {
			this._refresh('onlyCompareData', null, () => {
				; (this.params.idList || []).some((id, index) => {
					if (id === this.params.id) {
						var newOrderId = this.params.idList[index + (type === 'next' ? 1 : -1)]

						if (newOrderId) {
							this.blockSameIdOpenTab(newOrderId, () => {
								this.$root.eventHandle.$emit('removeTab', this.params.tabName)
								this.$root.eventHandle.$emit('creatTab', {
									name: '新退款申请单',
									params: {
										id: newOrderId,
										idList: this.params.idList,
									},
									component: () => import('@components/after_sales_refund/refundRequest_3')
								})
							})
						} else {
							this.$message.error('没有更多')
						}
						return true
					}
				})
			})
		},
		// 驳回信息--快照按钮
		snapshot(id) {
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/snapshot', { id }, res => {
				if (res.body.result) {
					this.$root.eventHandle.$emit('creatTab', {
						name: '新退款申请单(快照)',
						params: { snapshotObj: res.body },
						component: () => import('@components/after_sales_refund/refundRequest_3')
					})
				} else {
					this.$message.error(res.body.msg)
				}
			})
		},
		// 选择退款账号、退款人姓名、开户行
		selectReceiverAccount(isFetch) {
			if (isFetch) {
				this.ajax.postStream('/order-web/api/customer/payAccount/getCustomerPayAccountList', {
					page_no: 1,
					page_size: 50,
					cust_id: this.form.billRefundApplication.nick_id,
				}, res => {
					if (res.body.result) {
						var d = res.body.content.list[0]
						if (d) {
							this.form.billRefundApplication.receiver_name = d.bank_user_name
							this.form.billRefundApplication.receiver_account = d.pay_account//选择列表的支付账户而不是收款账号
						}
					}
				})
			} else {
				this.$root.eventHandle.$emit('alert', {
					title: '支付信息列表',
					style: 'width:800px;height:560px',
					component: () => import('@components/after_sales_refund/selectReceiverAccount'),
					params: {
						cust_id: this.form.billRefundApplication.nick_id || '',
						callback: d => {
							this.form.billRefundApplication.receiver_name = d.bank_user_name
							this.form.billRefundApplication.receiver_account = d.pay_account//选择列表的支付账户而不是收款账号
						}
					},
				})
			}
		},
		// 新增按钮
		addNewOrder() {
			this.$root.eventHandle.$emit('creatTab', {
				name: '新退款申请单',
				params: {},
				component: () => import('@components/after_sales_refund/refundRequest_3')
			})
		},
		// 刷新按钮
		_refresh: function (e, oldData, cb) {
			let self = this
			this.freshSaveStatus = false
			if (oldData) {
				this.refreshOldData = JSON.parse(JSON.stringify(oldData))
				return oldData
			} else {
				var _closeOrRefresh = () => {
					self.freshSaveStatus = true
					if (e) {//刷新按钮
						if (e === 'onlyCompareData') {
							cb()
						} else if (this.form.billRefundApplication.id) {
							this.isClickSaveOrRefresh = true
							let {
								business_status,
								refund_way
							} = this.form.billRefundApplication
							if (business_status === 'FINISH' && !['ALIPAY', 'BANK'].includes(refund_way)) {
								this.getDefaultDealerCustomer()
							}
							this.getOrderDetail()
							this.getBrowseList(this.params.id)
						} else {
							this.form = JSON.parse(JSON.stringify(this.refreshOldData))
						}
					} else if (cb) {
						cb()
					} else {//页签关闭事件
						this.$root.eventHandle.$emit('removeTab', this.params.tabName)
					}
				}

				if (this.compareData(JSON.parse(JSON.stringify(this.form)), this.refreshOldData)) {
					this.$root.eventHandle.$emit('openDialog', {
						ok: () => {
							this.freshSaveStatus = true
							this.save()
						},
						no: _closeOrRefresh
					})
				} else {
					_closeOrRefresh()
				}
			}
		},
		_calcPayAmount() {
			/*{{ {RECEIVE:'收入', REFUND:'退款', CARRYOVERS:'结转', RETURNS: '退货货款', COMPENSATION: '赔偿'}[scope.row.type] }}*/
			return Number(this.form.sysPaymentList.reduce((a, b) => {
				if (b.type === 'RECEIVE' || b.type == 'CARRYOVERS') {
					a += (Number(b.pay_amount) || 0)
				} else if (/^(REFUND|RETURNS)$/.test(b.type) || /^(运费|三包费)$/.test(b.received_account)) {
					a -= (Number(b.pay_amount) || 0)
				}
				return a
			}, 0).toFixed(10))
		},
		// 经销商时去掉接口permissionCode权限控制
		_delPermissionCodeWhenDealerUser(api, bool) {
			return api
		},
		// 检验退款明细
		_checkRefoudDetailListRequired(submitLoadingCb) {
			var msg = '请添加至少一条退款明细'

			this.form.billRefundApplicationItem.some((obj, index) => {
				msg = ''

				if (this.form.billRefundApplication.refund_way === 'CARRY_OVER') {
					if (!(obj.refund_type === this.form.billRefundApplicationItem[0].refund_type && /^(RETURNS)$/.test(obj.refund_type))) {
						msg = '换货结转时，退款类型只能是退货货款'
						return true
					} else if (!obj.refund_reason) {
						msg = '退款原因不能为空'
						return true
					}
				} else if (this.form.billRefundApplication.refund_way === 'CROSS_OVER_T') {
					if ((obj.apply_amount <= 0 || !obj.apply_amount) && obj.refund_type == 'CROSS_OVER' && /^(1)$/.test(obj.reason_version)) {
						msg = '申请金额都要大于0'
					} else if (obj.deduct_fee < 0) {
						msg = '扣费不能小于0'
					} else if (!obj.refund_reason && obj.refund_type == 'CROSS_OVER' && /^(1)$/.test(obj.reason_version)) {
						msg = '退款原因不能为空'
					}
				}
				if ((obj.apply_amount <= 0 || !obj.apply_amount) && obj.refund_type != 'PLATFORM_DISCOUNT') {
					msg = '申请金额都要大于0'
				} else if (obj.deduct_fee < 0) {
					msg = '扣费不能小于0'
				}
				if (!/^(RETURNS|OVER_DISCOUNT|CARRIAGE|OVERPAY|PRICE_DIFF)$/.test(obj.refund_type)) {
					for (let key in obj.excessiveParam) {
						if (!obj.excessiveParam[key]) {
							msg = '退款原因不能为空'
							break
						}
					}
				}

				if ((/^(RETURNS)$/.test(obj.refund_type) && obj.reason_version != '1' && this.form.billRefundApplication.refund_way != 'CARRY_OVER') || (/^(OVERPAY|PRICE_DIFF)$/.test(obj.refund_type) && obj.reason_version == '1')) {
					if (!obj.refundReason) {
						msg = '退款原因不能为空'
					}
				}
				if (/^(O2O_DIFF|REFUNDCARRYFORWARD|INCOME_CARRY)$/.test(obj.refund_type)) {
					if (!obj.refund_reason) {
						msg = '退款原因不能为空'
					}
				}
				if (msg) {
					msg = '退款明细-第' + (index + 1) + '行-' + msg
					return true
				}
			})

			if (msg) {
				submitLoadingCb && submitLoadingCb()
				this.$message.error(msg)
				throw (msg)
			}
		},
		checkApplyAmountFirst(e, row, rowName) {
			var $input = e.target

			if (!$input._oldVal) $input._oldVal = Number(row[rowName])

			window.clearTimeout(this.checkApplyAmountFirst._clearTimer)
			this.checkApplyAmountFirst._clearTimer = setTimeout(() => {
				$input.value = row[rowName] = Number($input.value)
				this._calcTotalPrice()
			}, 300)
		},
		// 计算退款明细合计
		_calcTotalPrice() {
			var list = this.form.billRefundApplicationItem
				, allRefundTypeEachTotal = {}
				, totalObj = {
					apply_amount: 0,
					deduct_fee: 0,
					actual_amount: 0,
					platform_discount: 0,
					refund_reason: '',
				}
			setTimeout(() => {
				list.forEach((obj, index) => {
					obj.apply_amount = obj.apply_amount > 0 ? Math.round(Number(obj.apply_amount) * 100) / 100 : 0
					obj.deduct_fee = obj.deduct_fee > 0 ? Math.round(Number(obj.deduct_fee) * 100) / 100 : 0
					if (obj.refund_type != 'PLATFORM_DISCOUNT') {
						obj.actual_amount = Number(((obj.apply_amount || 0) - (obj.deduct_fee || 0) - (obj.platform_discount || 0)).toFixed(2))
					} else {
						obj.actual_amount = Number(((obj.apply_amount || 0) - (obj.deduct_fee || 0)).toFixed(2))
					}
					obj.platform_discount = Number(((obj.platform_discount || 0)).toFixed(2))
					if (obj.if_cancel_refund !== 'Y') {
						totalObj.apply_amount += obj.apply_amount || 0
						totalObj.deduct_fee += obj.deduct_fee || 0
						totalObj.actual_amount += obj.actual_amount
						totalObj.platform_discount += obj.platform_discount || 0
						if (!allRefundTypeEachTotal[obj.refund_type]) allRefundTypeEachTotal[obj.refund_type] = 0
						allRefundTypeEachTotal[obj.refund_type] += Number(obj.actual_amount)
					}
				})
				totalObj.apply_amount = '合计' + Number(totalObj.apply_amount.toFixed(2))
				totalObj.deduct_fee = '合计' + Number(totalObj.deduct_fee.toFixed(2))
				totalObj.actual_amount = '合计' + Number(totalObj.actual_amount.toFixed(2))
				totalObj.platform_discount = '合计' + Number(totalObj.platform_discount.toFixed(2))

				totalObj.refund_reason = Object.keys(allRefundTypeEachTotal).map(refund_type => {
					if (refund_type != 'PLATFORM_DISCOUNT') {
						return this.refoudDetailList_type_options[refund_type] + '合计' + Number(allRefundTypeEachTotal[refund_type].toFixed(2))
					}
				}).join('，')

				this.totalObj = totalObj

			})
		},
		// 支出，整行标红显示
		paymentListClassName(row, index) {
			if (/^(REFUND|RETURNS|COMPENSATION)$/.test(row.type)) {
				return this.$style['mergered']
			}
		},
		delRefundType() {
			if (!this.selectRefoudDetailLists.length) {
				this.$message.error('请选择一行退款明细')
				return
			}
			let priceDiffs = [], self = this
			this.selectRefoudDetailLists.forEach(obj => {
				if (obj.id) {
					delete obj.excessiveParam
					delete obj.refundReason
					this.form.delApplicationItem.push(obj)
				}
				if (obj.xentry_id) {

					if (obj.refund_type === 'O2O_DIFF') {
						; (this[obj.refund_type + '_List'] || []).some((o, i) => {
							if (o.id === obj.pricediff.id || o.__id === obj.pricediff.__id/*新增时，两个不可枚举属性对比*/) {
								this[obj.refund_type + '_List'].splice(i, 1)
								return true
							}
						})
					} else if (obj.refund_type === 'PRICE_DIFF') {
						priceDiffs.push(obj.xentry_id)
						self.copyChooseTchList = []
						self.copyChooseTchIds = []
					} else {
						; (this[obj.refund_type + '_List'] || []).some((o, i) => {
							if (o[{ COMPENSATION: 'question_sub_id', REPAIR: 'question_sub_id', RETURNS: 'id' }[obj.refund_type] || 'entry_id'] === obj.xentry_id) {
								this[obj.refund_type + '_List'].splice(i, 1)
								return true
							}
						})
					}

				}
				this.form.billRefundApplicationItem.splice(this.form.billRefundApplicationItem.indexOf(obj), 1)
			})
			if (priceDiffs.length > 0) {
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/delDiffPrice', { apply_id: this.form.billRefundApplication.id, xentry_ids: priceDiffs }, res => {
					self.getGoodsDetailList()
				}, err => {
					self.getOrderDetail()
				});
				return
			}
			this._calcTotalPrice()

			if (
				this.form.billRefundApplicationItem.some(obj => {
					if (/^(COMPENSATION|REPAIR)$/.test(obj.refund_type)) {
						return true
					}
				})
			) {
				/*empty*/
			} else {
				//当销售折让和维修都没有时，删除售后单号
				this.form.billRefundApplication.after_order_no = ''
				this.form.billRefundApplication.after_order_id = ''
			}
		},
		selRefoudDetailListLine(list) {
			this.selectRefoudDetailLists = list
		},
		refoudDetailSelect(selection, row) {
			let list = []
			if (row.refund_type === 'PRICE_DIFF') {
				if (new Set(this.copyChooseTchIds).has(row.id) || new Set(this.copyChooseTchIds).has(row.tempId)) {
					list = this.copyChooseTchList.filter(item => {
						return item.refund_type != 'PRICE_DIFF'
					})
					this.copyChooseTchIds = []
					this.copyChooseTchList = JSON.parse(JSON.stringify(list))
				} else {
					list = this.form.billRefundApplicationItem.filter(item => {
						return item.refund_type === 'PRICE_DIFF'
					})
					list.forEach(item => {
						if (item.id) {
							this.copyChooseTchIds.push(item.id)
						} else if (item.tempId) {
							this.copyChooseTchIds.push(item.tempId)
						}
					})
					this.copyChooseTchList.push(...list)
				}
			}
		},
		refundWayChange(newVal) {
			this.form.billRefundApplication.if_cross_refund = 'N'
			this.mergeTradeNoTargetClear()
			this.form.billRefundApplication.use_for_corss = null
			if (newVal === 'CROSS_OVER_T') { // 跨单转结
				this.getCROSSOVERCanClick(() => {
					this.rules.merge_trade_no_target[0].required = true
				}, () => {
					// 校验没有特权时，退款方式不能为跨单结转
					this.form.billRefundApplication.refund_way = ''
				})
			} else {
				this.rules.merge_trade_no_target[0].required = false
			}
			this.form.billRefundApplication.bank_name = ''
			this.form.billRefundApplication.opening_bank = ''
			this.form.billRefundApplication.opening_bank_code = ''

				;[{
					name: ['opening_bank', 'bank_name'],
					test: /BANK/.test(newVal),
				}, {
					name: ['receiver_account', 'receiver_name'],
					//PROTOCOL: '协议退款',DISPUTE : '纠纷退款',CARRY_OVER  : '换货结转',B2C_ONLINE: 'B2C线上退款',BAIL: '保证金退款'，CROSS_OVER_T: '跨单结转'，DEALER_PREFERENTIAL: '经销优惠退款'，收款人姓名和收款账号不用必填
					test: !/^(PROTOCOL|DISPUTE|B2C_ONLINE|CARRY_OVER|BAIL|CROSS_OVER_T|DEALER_PREFERENTIAL|YSF|OVERSEAS_AFTER_REFUND)$/.test(newVal),
				}].forEach(obj => {
					obj.name.forEach(name => {
						this.rules[name][0].required = obj.test
					})
				})
		},
		// 作废按钮
		cancelOrder() {
			var func = () => {
				this.$root.eventHandle.$emit('openDialog', {
					txt: '是否确认作废？',
					okTxt: '确认',
					noShowNoTxt: true,
					ok: () => {
						this.ajaxFunc(
							this._delPermissionCodeWhenDealerUser(
								'invalid',
								!/^(DISPUTE|BAIL|PROTOCOL)$/.test(this.form.billRefundApplication.refund_way)
							),
							res => this.getOrderDetail(res.body.msg)
						)
					},
				})
			}

			if (/补退已使用的红包/.test(this.form.taobaoRefundDownload.reason)) {
				func()
			} else if (
				this.form.billRefundApplication.original_type === 'OTHER' &&
				!(
					this.form.billRefundApplication.if_finance_lock !== 'Y' &&
					/^(CREATE|REJECT)$/.test(this.form.billRefundApplication.business_status) &&
					this.isSameBusinessLockPerson
				)
			) {
				this.$message.error({
					message: '手工退款作废需满足：\n1.初审锁定=否\n2.业务状态=创建/驳回\n3.业务锁定人=代理人',
					customClass: this.$style['message-box'],
				})
			} else if (
				this.form.billRefundApplication.original_type !== 'OTHER' &&
				!(
					this.form.billRefundApplication.business_status === 'SUBMIT' &&
					this.form.taobaoRefundDownload.status === 'CLOSED' &&
					this.isSameFinanceLockPerson
				)
			) {
				this.$message.error({
					message: '非手工退款作废需满足：\n1.业务状态=已提交\n2.复核退款状态=退款关闭\n3.初审锁定人=代理人',
					customClass: this.$style['message-box'],
				})
			} else {
				func()
			}
		},
		compareDiffPriceOrders() {
			let discountOld = this.oldDifferenceAccountObj.accountBasicList,
				discountNow = this.differenceAccountObj.accountBasicList,
				discountAdd = [],
				discountUpd = [],
				discountDel = [];
			discountNow.find(row => {
				let i = discountOld.length;
				while (i--) {
					if (row.id === discountOld[i].id) {
						let isCouponUpd = this.compareData({
							id: row.id,
							cross_shop_id: row.cross_shop_id, //优惠券数量
						}, {
							id: discountOld[i].id,
							cross_shop_id: discountOld[i].cross_shop_id,
						});
						if (isCouponUpd) {
							discountUpd.push({
								id: row.id,
								cross_shop_id: Number(row.cross_shop_id),
							})
						}
					}
				}
			})
			if (discountAdd.length > 0 || discountUpd.length > 0 || discountDel.length > 0) {
				return true
			}
		},
		compareCouponList() {
			let discountOld = this.oldDifferenceAccountObj.accountCouponList,
				discountNow = this.differenceAccountObj.accountCouponList,
				discountAdd = [],
				discountUpd = [],
				discountDel = [];
			// 增
			discountNow.find(row => {
				if (row.tempId) {
					discountAdd.push(row)
				}
			})
			// 删
			let nowIdSet = new Set();
			discountNow.find(row => {
				nowIdSet.add(row.id);
			})
			discountOld.find(row => {
				if (!nowIdSet.has(row.id)) {
					discountDel.push(row.id);
				}
			})
			// 改
			discountNow.find(row => {
				let i = discountOld.length;
				while (i--) {
					if (row.id === discountOld[i].id) {
						let isCouponUpd = this.compareData({
							id: row.id,
							num: row.num, //优惠券数量
						}, {
							id: discountOld[i].id,
							num: discountOld[i].num,
						});
						if (isCouponUpd) {
							discountUpd.push({
								id: row.id,
								num: Number(row.num),
							})
						}
					}
				}
			})
			if (discountAdd.length > 0 || discountUpd.length > 0 || discountDel.length > 0) {
				return true
			}
		},
		compareAccountBasicList() {
			let discountOld = this.oldDifferenceAccountObj.accountBasicList,
				discountNow = this.differenceAccountObj.accountBasicList,
				discountUpd = []
			if (discountOld.length == 0 && discountNow.length > 1) {
				return true
			}
			discountNow.find(row => {
				let i = discountOld.length;
				while (i--) {
					if (row.id === discountOld[i].id) {
						let isCouponUpd = this.compareData({
							id: row.id,
							apply_amount: row.apply_amount,
						}, {
							id: discountOld[i].id,
							apply_amount: discountOld[i].apply_amount,
						});
						if (isCouponUpd) {
							discountUpd.push({
								id: row.id,
								apply_amount: Number(row.apply_amount),
							})
						}
					}
				}
			})
		},
		compareBasicList() {
			let newBillRefundApplication = this.form.billRefundApplicationItem.filter(item => {
				return item.refund_type == 'PRICE_DIFF'
			})
			let oldBillRefundApplication = this.oldBillRefundApplicationItem.filter(item => {
				return item.refund_type == 'PRICE_DIFF'
			})
			let discountOld = oldBillRefundApplication,
				discountNow = newBillRefundApplication,
				discountAdd = [],
				discountUpd = [],
				discountDel = [];
			// 增
			discountNow.find(row => {
				if (!row.id) {
					discountAdd.push(row)
				}
			})
			// 删
			let nowIdSet = new Set();
			discountNow.find(row => {
				nowIdSet.add(row.id);
			})
			discountOld.find(row => {
				if (!nowIdSet.has(row.id)) {
					discountDel.push(row.id);
				}
			})
			// 改
			discountNow.find(row => {
				let i = discountOld.length;
				while (i--) {
					if (row.id === discountOld[i].id) {
						let isCouponUpd = this.compareData({
							id: row.id,
							apply_amount: row.apply_amount, //优惠券数量
						}, {
							id: discountOld[i].id,
							apply_amount: discountOld[i].apply_amount,
						});
						if (isCouponUpd) {
							discountUpd.push({
								id: row.id,
								apply_amount: Number(row.apply_amount),
							})
						}
					}
				}
			})
			if (discountAdd.length > 0 || discountUpd.length > 0 || discountDel.length > 0) {
				return true
			}
		},
		save(cb, submitLoadingCb) {
      if(this.saveLoading){
        this.$message.warning('保存中，请稍后')
        return
      }
			if (!this.form.billRefundApplication.merge_trade_no) {
				this.$message.error('为不影响财务对账，合并订单号不可为空，请选单')
				submitLoadingCb && submitLoadingCb()
				return
			}
			if (!this.canBuildNoSourceOrder && (!this.form.billRefundApplication.user_shop_id || !this.form.billRefundApplication.saleman_id)) {
				new Promise((reslove, reject) => {
					this.getSysUserInfoByRefundId(reslove);
				}).then(() => {
					this.$refs.form.validate(valid => {
						let cancel = [], self = this, reasonFlag = true;

						if (valid) {
              const copyForm=cloneDeep(this.form);
						  let len = copyForm.billRefundApplicationItem.length, list = copyForm.billRefundApplicationItem


							this.form.billRefundApplicationItem.forEach(item => {
								if (/^(CANCEL)$/.test(item.refund_type) && !item.refund_reason) {
									reasonFlag = false;
								}
							})
							if (!reasonFlag) {
								this.$message.error('存在退款明细为未发取消并且退款原因为空，请在上游单据修改再进行保存');
								return false;
							}
							if (this.form.billRefundApplication.refund_way === 'CROSS_OVER_T') {
								if (!this.form.billRefundApplication.use_for_corss) {
									this.$message.error('跨单结转的用途字段不可以为空');
									return false;
								}
							}
							let excessiveParamFlag = false;
							for (let i = 0; i < len; i++) {
								for (let key in list[i].excessiveParam) {
									if (list[i].excessiveParam[key] && !isValidParentheses(list[i].excessiveParam[key])) {
										excessiveParamFlag = true;
										this.$message.error('退款原因括号必须成对，请重新填写第' + (i + 1) + '行退款明细行的退款原因');

									}
								}
							}
							if (!!excessiveParamFlag) {
								submitLoadingCb && submitLoadingCb();
								return false;
							}
							// 校验差价核算单是否有更改
							let ifCoupon = this.compareCouponList(), ifGoods = this.compareBasicList(), ifChange = false
							let ifDate = (this.differenceAccountObj.order_date == this.oldDifferenceAccountObj.order_date)

							if (ifCoupon || ifGoods || ifDate) {
								this.ifChange = true
							}

							// 检验退款明细
							this._checkRefoudDetailListRequired(submitLoadingCb);

							this.form.billRefundApplication.apply_amount = Number(this.totalObj.apply_amount.replace('合计', ''))
							this.form.billRefundApplication.actual_amount = Number(this.totalObj.actual_amount.replace('合计', ''))


							for (let i = 0; i < len; i++) { // 检测未保存的退款明细为未发取消的，且销售单号不一致的
								if (list[i].refund_type === 'CANCEL' && !list[i].id && list[i].sys_trade_id != this.form.billRefundApplication.sys_trade_id) {
									cancel.push(list[i])
								}

								if (/^(CREATE|REJECT)$/.test(this.form.billRefundApplication.business_status) && (!/^(O2O_DIFF|DELAY|REFUNDCARRYFORWARD|INCOME_CARRY)$/.test(list[i].refund_type)) && list[i].refund_type != 'CANCEL') {
									if (this.form.billRefundApplication.refund_way === 'CARRY_OVER' || this.form.billRefundApplication.refund_way === 'CROSS_OVER_T') {
										let refundBuss = '', currentRefundReasonList = 'refundReasonList' + list[i].reason_version
										if (/^(CROSS_OVER)$/.test(list[i].refund_type) && this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && /^(2|3|4)$/.test(list[i].reason_version)) {
											refundBuss = this[currentRefundReasonList][list[i].refund_type]
											let refundBussArr = refundBuss.split('('), reasonValue = []
											for (let key in list[i].excessiveParam) {
												if (!list[i].id) {
													reasonValue.unshift(list[i].excessiveParam[key])
												} else {
													reasonValue.push(list[i].excessiveParam[key])
												}
											}
											let reasonStr = ''
											refundBussArr.forEach((item, idx) => {
												if (idx != (refundBussArr.length - 1)) {
													item = item + '(' + reasonValue[idx]
												} else {
													item = item
												}
												reasonStr = reasonStr + item
											})
											list[i].refund_reason = reasonStr
										}
									} else {
										let refundBuss = '', currentRefundReasonList = 'refundReasonList' + list[i].reason_version
										if ((/^(OVERPAY|PRICE_DIFF)$/.test(list[i].refund_type) && list[i].reason_version == '1') || /^(RETURNS)$/.test(list[i].refund_type) && list[i].reason_version != '1') {
											refundBuss = this[currentRefundReasonList][list[i].refund_type][list[i].refundReason]
										} else if (/^(PREFERENTIAL|SINGLE_DISCOUNT|CARRIAGE|THREE|REPAIR|COMPENSATION|DISCOUNT|OVER_DISCOUNT|OVER_FUND)$/.test(list[i].refund_type) || (/^(PRICE_DIFF)$/.test(list[i].refund_type) && list[i].reason_version != '1') || (list[i].refund_type == 'RETURNS' && list[i].reason_version == '1') || (list[i].refund_type == 'OVERPAY' && list[i].reason_version == '3')) {
											refundBuss = this[currentRefundReasonList][list[i].refund_type]
										} else {

										}
										let refundBussArr = refundBuss.split('('), reasonValue = []
										for (let key in list[i].excessiveParam) {
											if (!list[i].id) {
												reasonValue.unshift(list[i].excessiveParam[key])
											} else {
												reasonValue.push(list[i].excessiveParam[key])
											}
										}
										let reasonStr = ''
										refundBussArr.forEach((item, idx) => {
											if (idx != (refundBussArr.length - 1)) {
												item = item + '(' + reasonValue[idx]
											} else {
												item = item
											}
											reasonStr = reasonStr + item
										})
										list[i].refund_reason = reasonStr
									}
								}
							}

							if ((cancel.length > 0 && !this.submitLoading) && (cancel.length > 0 && !this.freshSaveStatus)) { // submitLoading为true点击提交初审可直接提交，关闭刷新的保存按钮（true）都可直接保存
								this.$root.eventHandle.$emit('openDialog', {
									txt: '选择的商品所在的销售订单和表头销售单不一致，是否保存？',
									okTxt: '是',
									cancelTxt: '否',
									noShowNoTxt: true,
									ok() {
										self.confirmSave(cb, submitLoadingCb)
									},
									cancel() {
										return
									}
								})
							} else {
								self.confirmSave(cb, submitLoadingCb)
							}

						} else {
							submitLoadingCb && submitLoadingCb()
							if (!this.form.billRefundApplication.saleman_id) {
								this.selectTab1 = 'billRefundApplicationOtherInfo'
							} else {
								this.selectTab1 = 'basicInformation'
							}

						}
					})
				})
			} else {
				this.$refs.form.validate(valid => {
					let cancel = [], self = this, reasonFlag = true;

					if (valid) {
						this.form.billRefundApplicationItem.forEach(item => {
							if (/^(CANCEL)$/.test(item.refund_type) && !item.refund_reason) {
								reasonFlag = false;
							}
						})
						if (!reasonFlag) {
							this.$message.error('存在退款明细为未发取消并且退款原因为空，请在上游单据修改再进行保存');
							return false;
						}
						if (this.form.billRefundApplication.refund_way === 'CROSS_OVER_T') {
							if (!this.form.billRefundApplication.use_for_corss) {
								this.$message.error('跨单结转的用途字段不可以为空');
								return false;
							}
						}
						let len = this.form.billRefundApplicationItem.length, list = this.form.billRefundApplicationItem
						let excessiveParamFlag = false;
						for (let i = 0; i < len; i++) {
							for (let key in list[i].excessiveParam) {
								if (list[i].excessiveParam[key] && !isValidParentheses(list[i].excessiveParam[key])) {
									excessiveParamFlag = true;
									this.$message.error('退款原因括号必须成对，请重新填写第' + (i + 1) + '行退款明细行的退款原因');
								}
							}
						}
						if (!!excessiveParamFlag) {
							submitLoadingCb && submitLoadingCb();
							return false;
						}
						// 校验差价核算单是否有更改
						let ifCoupon = this.compareCouponList(), ifGoods = this.compareBasicList(), ifChange = false
						let ifDate = (this.differenceAccountObj.order_date == this.oldDifferenceAccountObj.order_date)
						let ifDiffPrice = this.compareDiffPriceOrders()
						if (ifCoupon || ifGoods || ifDate || ifDiffPrice) {
							this.ifChange = true
						}
						// 检验退款明细
						this._checkRefoudDetailListRequired(submitLoadingCb);

						this.form.billRefundApplication.apply_amount = Number(this.totalObj.apply_amount.replace('合计', ''))
						this.form.billRefundApplication.actual_amount = Number(this.totalObj.actual_amount.replace('合计', ''))


						for (let i = 0; i < len; i++) { // 检测未保存的退款明细为未发取消的，且销售单号不一致的
							if (list[i].refund_type === 'CANCEL' && !list[i].id && list[i].sys_trade_id != this.form.billRefundApplication.sys_trade_id) {
								cancel.push(list[i])
							}
							if (/^(CREATE|REJECT)$/.test(this.form.billRefundApplication.business_status) && (!/^(O2O_DIFF|DELAY|REFUNDCARRYFORWARD|INCOME_CARRY)$/.test(list[i].refund_type)) && list[i].refund_type != 'CANCEL') {
								if (this.form.billRefundApplication.refund_way === 'CARRY_OVER' || this.form.billRefundApplication.refund_way === 'CROSS_OVER_T') {
									let refundBuss = '', currentRefundReasonList = 'refundReasonList' + list[i].reason_version
									if (/^(CROSS_OVER)$/.test(list[i].refund_type) && this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && /^(2|3|4)$/.test(list[i].reason_version)) {
										refundBuss = this[currentRefundReasonList][list[i].refund_type]
										let refundBussArr = refundBuss.split('('), reasonValue = []
										for (let key in list[i].excessiveParam) {
											if (!list[i].id) {
												reasonValue.unshift(list[i].excessiveParam[key])
											} else {
												reasonValue.push(list[i].excessiveParam[key])
											}
										}
										let reasonStr = ''
										refundBussArr.forEach((item, idx) => {
											if (idx != (refundBussArr.length - 1)) {
												item = item + '(' + reasonValue[idx]
											} else {
												item = item
											}
											reasonStr = reasonStr + item
										})
										list[i].refund_reason = reasonStr
									}
								} else {
									let refundBuss = '', currentRefundReasonList = 'refundReasonList' + list[i].reason_version

									if (/^(OVERPAY|PRICE_DIFF)$/.test(list[i].refund_type) && list[i].reason_version == '4') {
										refundBuss = this[currentRefundReasonList][list[i].refund_type]
									}else if ((/^(OVERPAY|PRICE_DIFF)$/.test(list[i].refund_type) && list[i].reason_version == '1') || /^(RETURNS)$/.test(list[i].refund_type) && list[i].reason_version != '1') {
										refundBuss = this[currentRefundReasonList][list[i].refund_type][list[i].refundReason]
									}
                  else if (/^(PREFERENTIAL|SINGLE_DISCOUNT|CARRIAGE|THREE|REPAIR|COMPENSATION|DISCOUNT|OVER_DISCOUNT|OVER_FUND)$/.test(list[i].refund_type) || (/^(PRICE_DIFF)$/.test(list[i].refund_type) && list[i].reason_version != '1') || (list[i].refund_type == 'RETURNS' && list[i].reason_version == '1') || (list[i].refund_type == 'OVERPAY' && (list[i].reason_version == '3' || list[i].reason_version == '4'))) {
										refundBuss = this[currentRefundReasonList][list[i].refund_type]
									} else {

									}
									let refundBussArr = refundBuss.split('('), reasonValue = []
									for (let key in list[i].excessiveParam) {
										if (!list[i].id) {
											reasonValue.unshift(list[i].excessiveParam[key])
										} else {
											reasonValue.push(list[i].excessiveParam[key])
										}
									}
									let reasonStr = ''
									refundBussArr.forEach((item, idx) => {
										if (idx != (refundBussArr.length - 1)) {
											item = item + '(' + reasonValue[idx]
										} else {
											item = item
										}
										reasonStr = reasonStr + item
									})
									list[i].refund_reason = reasonStr
								}
							}
						}
						if ((cancel.length > 0 && !this.submitLoading) && (cancel.length > 0 && !this.freshSaveStatus)) { // submitLoading为true点击提交初审可直接提交，关闭刷新的保存按钮（true）都可直接保存
							this.$root.eventHandle.$emit('openDialog', {
								txt: '选择的商品所在的销售订单和表头销售单不一致，是否保存？',
								okTxt: '是',
								cancelTxt: '否',
								noShowNoTxt: true,
								ok() {
									self.confirmSave(cb, submitLoadingCb)
								},
								cancel() {
									return
								}
							})
						} else {
							self.confirmSave(cb, submitLoadingCb)
						}

					} else {
						submitLoadingCb && submitLoadingCb()
						if (!this.form.billRefundApplication.saleman_id) {
							this.selectTab1 = 'billRefundApplicationOtherInfo'
						} else {
							this.selectTab1 = 'basicInformation'
						}
					}
				})
			}
		},
		getSysUserInfoByRefundId(reslove) {

			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/getSysUserInfoByRefundId', { refund_id: this.form.taobaoRefundDownload.refund_id }, res => {
				if (res.body.result) {
					let data = res.body.content;
					if (!!data.saleman_id) {
						this.form.billRefundApplication.saleman_id = data.saleman_id;
						this.form.billRefundApplication.saleman_name = data.saleman_name;
						this.form.billRefundApplication.saleman_group_id = data.saleman_group_id;
						this.form.billRefundApplication.saleman_group_name = data.saleman_group_name;
						this.form.billRefundApplication.user_shop_id = data.user_shop_id;
						this.form.billRefundApplication.user_shop_name = data.user_shop_name;
						//this.save();
						reslove && reslove();
					} else {
						this.$message.error('客服店铺、业务员不能为空，请先锁定销售单！')
					}
				} else {
					this.$message.error('客服店铺、业务员不能为空，请先锁定销售单！')
				}
			}, err => {
				errCb && errCb()
			})
		},
		async confirmSave(cb, submitLoadingCb) {
			this.saveLoading = true
			let list = [], len = this.differenceAccountObj.accountBasicList.length
			for (let key = 0; key < len - 1; key++) {
				list.push(this.differenceAccountObj.accountBasicList[key])
			}
      const copyForm=cloneDeep(this.form);
			copyForm.billRefundApplicationItem.forEach((item, idx) => {
				if (item.tempId) {
					delete copyForm.billRefundApplicationItem[idx].tempId
				}
        delete item.excessiveParam
        delete item.refundReason
        if (item.realyRefundType = 'REFUNDCARRYFORWARD') {
					delete item.realyRefundType
				}
			})

			await this.ajaxFunc(
				this._delPermissionCodeWhenDealerUser('save', !/^(DISPUTE|BAIL|PROTOCOL)$/.test(copyForm.billRefundApplication.refund_way)),
				Object.assign({}, copyForm, {
					sysPaymentList: null,
					operateLogList: null,
					accountCheckList: null,
					aftersaleDiffPriceVO: {
						ifChange: this.ifChange,
						virtual_created: new Date(this.differenceAccountObj.order_date),
						operate_time_begin: this.operate_time_begin,
						operate_time_end: this.operate_time_end,
						diffPriceOrders: list,
						diffPriceDiscounts: this.differenceAccountObj.accountCouponList,
						end_time: this.end_time,
						created: this.created,
					},
				}),
				res => {
					this.saveLoading = false;
					this.ifChange = false
					if (cb) {
						cb()
						return
					}
					this.blockSameIdOpenTab(res.body.content)
					this.isClickSaveOrRefresh = true
					this.getOrderDetail(res.body.msg, res.body.content)
				},
				err => {
					submitLoadingCb && submitLoadingCb()
          if(this.form.billRefundApplication.id){
            this.getOrderDetail(err, this.form.billRefundApplication.id, true)
          }
					this.saveLoading = false
					this.ifChange = false
				}
			)
		},
		// 提交初审、作废、删除、保存、读取详情
		ajaxFunc(apiName, postData, cb, errCb) {
			let self = this
			if (apiName == 'confirmRefund') {
				this.isAuditRefundLoading = true;
			}
			if (apiName == 'check') {
				this.isCheckLoading = true;
			}
			if (/^(save|submit)/.test(apiName) && (this.totalObj.apply_amount.match(/(\d|\.)+/)[0] <= 0 || this.form.billRefundApplicationItem.some(obj => obj.actual_amount < 0))) {
				this.$message.error(this.totalObj.apply_amount.match(/(\d|\.)+/)[0] <= 0 ? '申请金额合计必须要大于0' : '每一行实退金额不能小于0')
				errCb && errCb()
				return
			}

			if (!postData || Object.prototype.toString.call(postData) === '[object Function]') {
				errCb = cb
				cb = postData
				postData = { id: this.form.billRefundApplication.id }
			}

			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/' + apiName, postData, res => {
				if (res.body.result) {
					cb && cb(res)
				} else {
					if (/^(save|submit)/.test(apiName)) {
						this.$notify.error({
							title: '提示',
							message: res.body.msg,
							duration: 5000
						});
					} else {
						this.$message.error(res.body.msg || '请稍候再试')
					}
					errCb && errCb()
				}
				if (apiName == 'confirmRefund') {
					self.isAuditRefundLoading = false;
				}
				if (apiName == 'check') {
					this.isCheckLoading = false;
				}
			}, err => {
				if (apiName == 'confirmRefund') {
					self.isAuditRefundLoading = false;
				}
				if (apiName == 'check') {
					this.isCheckLoading = false;
				}
				errCb && errCb()
			})
		},
		setStatus() {
			this.isSameBusinessLockPerson = this.form.billRefundApplication.business_lock_person === this.getEmployeeInfo('id')
			this.isSameFinanceLockPerson = this.form.billRefundApplication.finance_lock_person === this.getEmployeeInfo('id')
			this.isSamereviewLockPerson = this.form.billRefundApplication.review_lock_person === this.getEmployeeInfo('id')
      this.isCanEdit =
				!this.form.billRefundApplication.id
				|| (
					this.isSameBusinessLockPerson
					&& /^(CREATE)$/.test(this.form.billRefundApplication.business_status)
				) || (
          (this.isSameBusinessLockPerson||this.isSameFinanceLockPerson||this.isSamereviewLockPerson)
					&& /^(REJECT)$/.test(this.form.billRefundApplication.business_status)
        )
		},
		_selectMergeOrderInit(cb) {
			this.ajax.postStream("/user-web/api/sql/listFields", { page: 'scm_sys_trade' }, res => {
				this.selectMergeOrderObj.audit_status_where = []
				res.body.content.fields.forEach(obj => {
					if (/(审核状态|单据状态)/.test(obj.comment)) {
						obj.values.some(val => {
							if (val.name === '已审核') {
								this.selectMergeOrderObj.audit_status_where[0] = {
									"field": obj.field,
									"table": obj.table,
									"value": val.code,
									"operator": "=",
									"condition": "AND",
									"listWhere": []
								}
								return true
							}
						})
					} else if (/(客户昵称|买家昵称)/.test(obj.comment)) {
						this.selectMergeOrderObj.audit_status_where[1] = {
							"field": obj.field,
							"table": obj.table,
							"value": '',
							"operator": "=",
							"condition": "AND",
							"listWhere": []
						}
					} else if (/合并单号/.test(obj.comment)) {
						this.selectMergeOrderObj.audit_status_where[2] = {
							"field": obj.field,
							"table": obj.table,
							"value": '',
							"operator": "<>",
							"condition": "AND",
							"listWhere": []
						}
					}
				})
				cb()
			})
			this.ajax.postStream("/user-web/api/sql/listFields", { page: 'scm_batch_trade' }, res => {
				this.selectMergeOrderObj.scm_batch_trade = []
				res.body.content.fields.some(obj => {
					if (/批次订单/.test(obj.comment)) {
						this.selectMergeOrderObj.scm_batch_trade[0] = {
							"field": obj.field,
							"table": obj.table,
							"value": '',
							"operator": "=",
							"condition": "AND",
							"listWhere": []
						}
					}
				})
			})
		},
		// 选单
		selectMergeOrder() {
			this.$root.eventHandle.$emit('alert', {
				title: '销售订单列表',
				style: 'width:900px;height:500px',
				component: () => import('@components/order/list_alert'),
				params: {
					otherSearchParams: {
						where: [this.selectMergeOrderObj.audit_status_where[0]],
						page_name: 'scm_sys_trade',
					},
					callback: d => {
						this.canBuildNoSourceOrder = false
						if (this.form.billRefundApplication.nick_id = d.customer_id) {
							this.selectReceiverAccount('fetch')
						}
						this.form.billRefundApplication.sys_trade_no = d.sys_trade_no
						this.form.billRefundApplication.sys_trade_id = d.sys_trade_id
						this.form.billRefundApplication.shop_id = d.shop_id
						this.form.billRefundApplication.saleman_id = d.user_id
						this.form.billRefundApplication.user_shop_id = d.user_shop_id
						this.form.billRefundApplication.original_shop_id = d.original_shop_id
						this.form.billRefundApplication.dealer_customer_id = d.dealer_customer_id

						if (this.merge_trade_id = this.form.billRefundApplication.merge_trade_id = d.merge_trade_id) {
							this.getAccountList()
              this.$refs.inner && this.$refs.inner.getInnerList(this.merge_trade_id);
						}

						//选单后要清空退款方式，避免通过选了下面特定店铺后选中协议或纠纷后再选单的漏洞
						/*
						手工新增的退款申请单时：
						1、退款方式要禁掉B2C线上退款
						2、非苏宁，贝店的订单店铺时，协议退款禁掉
						3、非京东、唯品会、苏宁、贝店时，纠纷退款也禁掉
						 */
						this.$set(this.form.billRefundApplication, 'refund_way', '')
						this.$set(this.form.billRefundApplication, 'dealer_customer_name', d.dealer_customer_name)
						this.$set(this.form.billRefundApplication, 'dealer_customer_number', d.dealer_customer_number)
						this.$set(this.form.billRefundApplication, 'if_dealer', d.if_dealer)
						this.$set(this.form.billRefundApplication, 'original_no', d.sys_trade_no)
						this.$set(this.form.billRefundApplication, 'merge_trade_no', d.merge_trade_no)
						this.$set(this.form.billRefundApplication, 'shop_name', d.shop_name)//订单店铺
						this.$set(this.form.billRefundApplication, 'saleman_name', d.user_name)//业务员
						this.$set(this.form.billRefundApplication, 'saleman_group_name', d.group_name)//分组
						this.$set(this.form.billRefundApplication, 'nick_name', d.customer_name)
						this.$set(this.form.billRefundApplication, 'user_shop_name', d.user_shop_name)
						this.$set(this.form.billRefundApplication, 'original_shop_name', d.original_shop_name)
					}
				},
			})
		},
		// 接口信息转义
		getInterfaceResponeNumber(str) {

			if (typeof str == 'string') {
				try {
					JSON.parse(str);
					return JSON.parse(str).Result.Number;
				} catch (e) {
					return '';
				}
			}
		},
		getApiDetail() {
			if (this.form.billRefundApplication.bill_no) {
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryExtInterfaceByBillNo', {
					bill_no: this.form.billRefundApplication.bill_no,
					bill_type: 'REFUND',
				}, res => {
					var data = res.body
					if (!data.result) {
						this.$message.error(data.msg)
						return
					}
					this.apiList = data.content || []
				})
			}
		},
		getInfoRequestList() {
			let self = this;
			if (this.form.billRefundApplication.bill_no) {
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/getInfoRequestList',
					this.form.billRefundApplication.id, res => {
						var data = res.body
						if (!data.result) {
							this.$message.error(data.msg)
							return
						}
						self.InfoRequestList = data.content || []
					})
			}
		},
		//手工推送
		pushOrder(id) {
			if (id) {
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/resetPush', {
					id: id
				}, res => {
					if (res.body.result) {
						this.$message.success(res.body.msg)
					} else {
						this.$message.error(res.body.msg)
					}
				}), err => {
					this.$message.error(res.body.msg)
				}
			}
		},
		selectTab2Click() {
			if (this.selectTab2 === 'operateLogList' && this.form.billRefundApplication.id) {
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.form.billRefundApplication.id }, res => {
					if (res.body.result) {
						this.operateLogList = res.body.content || []
					}
				})
			} else if (this.selectTab2 === 'apiDetail') {
				this.getApiDetail()
			} else if (this.selectTab2 === 'browse' && this.form.billRefundApplication.id && (this.isClickSaveOrRefresh || this.isClickSaveOrRefresh === undefined)) {
				this.getBrowseList()
			} else if (this.selectTab2 === 'infoRequestList') {
				this.getInfoRequestList()
			}
		},
		getBrowseList(id) {
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/browse', { id: id || this.form.billRefundApplication.id }, res => {
				if (res.body.result) {
					this.isClickSaveOrRefresh = false
					Object.keys(res.body.content || {}).forEach(type => {
						if (type === 'PRICE_DIFF' || type === 'O2O_DIFF') {
							this[type + '_List'] = res.body.content[type].map(obj => this.setOrDelPricediffData(obj, 'wantRightKey'))
							this[type + '_Total'] = this.calcBrowseTotal(this[type + '_List'], this.arrItem[type])
						} else {
							this[type + '_List'] = res.body.content[type]
							this[type + '_Total'] = this.calcBrowseTotal(this[type + '_List'], this.arrItem[type])
						}
					})
				}
			})
		},
		// 附件操作
		uploadAction(method) {
			if (method === 'post') {
				this.ifClickUpload = true
				this.uploadData = {
					parent_name: 'AFTER_ORDER',
					parent_no: this.form.billRefundApplication.bill_no || this.form.billRefundApplication.after_order_no,
					child_name: 'REFUNDAPPLYBILLDETAIL',
					child_no: this.form.billRefundApplication.bill_no,
					content: JSON.parse(JSON.stringify(this.form || {})),
				}
				// 上传辨别-》退款明细中点击维修和销售折让类型传售后id（after_order_no），除此之外的传bill_no
				let ifContain = null
				ifContain = this.form.billRefundApplicationItem.some(item => {
					return (item.refund_type === 'REPAIR' || item.refund_type === 'COMPENSATION')
				})
				this.uploadData.parent_no = ifContain ? this.form.billRefundApplication.after_order_no : this.form.billRefundApplication.bill_no
				setTimeout(() => {
					this.ifClickUpload = false
				}, 100)
			} else {
				let param = {
					parent_name: 'AFTER_ORDER',
					parent_name_txt: '退款申请单号',
					parent_no: this.form.billRefundApplication.bill_no,
					other_parent_nos: [],
					child_name: 'REFUNDAPPLYBILLDETAIL',
					child_no: null,
					ext_data: null,
					cantDel: true,
					nickname: this.form.billRefundApplication.nick_name,
					mergeTradeId: this.form.billRefundApplication.merge_trade_id,
				};
				let ifContain = null, ary = [];
				for (let i = 0; i < this.form.billRefundApplicationItem.length; i++) {
					if (this.form.billRefundApplicationItem[i].refund_type === 'REPAIR' || this.form.billRefundApplicationItem[i].refund_type === 'COMPENSATION') {
						ary.push(this.form.billRefundApplicationItem[i].original_no);
					}
				}
				if (ary.length > 0) {
					ifContain = true;
				} else {
					ifContain = false;
				}
				param.other_parent_nos = ifContain ? ary : [];
				this.$root.eventHandle.$emit('alert', {
					params: param,
					component: () => import('@components/after_sales/afterSale_aboutZD_download.vue'),
					style: 'left:500px;width:1000px;height:600px;',
					title: '下载列表',
				})
			}
		},
		autoMakeRefundReason(row) {
			if (row.refund_type === 'RETURNS') {
				if (/退货商品.+实际售价/.test(row.refund_reason)) {
					row.refund_reason.replace(/(.*退货商品.+实际售价).+元，应退.+元(.*)/, ($1, $2, $3) => {//退货货款
						row.refund_reason = $2 + row.apply_amount + '元，应退' + row.apply_amount + '元' + $3
					})
				}
			}
		},

		changeLbMergeOrder() {
			this.$root.eventHandle.$emit('alert', {
				title: '销售订单列表',
				style: 'width:900px;height:500px',
				component: () => import('@components/order/list_alert'),
				params: {
					isShowMerge: true,
					callback: d => {


					}
				},
			})
		},
		// 点击退款明细中的按钮
		addRefundType(refundType, is_show_CANCEL_alert_again, refund_type_sub) {
			// 未发取消、退货货款时如果是京东平台的则先校验退款模式
			if (refundType == "CANCEL" || refundType == "RETURNS") {
				if ((this.jd_shops.indexOf(String(this.form.billRefundApplication.original_shop_id)) !== -1)) {
					//校验退款方式
					if (!this.form.billRefundApplication.refund_way) {
						return this.$message.error("请先选择退款方式")
					} else {
						if ((this.form.billRefundApplication.refund_way == "DISPUTE" || this.form.billRefundApplication.refund_way == "PROTOCOL") &&
							(!this.form.billRefundApplication.refund_mode ||
								this.form.billRefundApplication.refund_mode == "" ||
								this.form.billRefundApplication.refund_mode == null)) {
							return this.$message.error("请选择退款模式后，再添加【未发取消】、【退货货款】的退款明细")
						}
					}
				}
			}
			let self = this;
			if (refundType == 'PRICE_DIFF' && this.form.billRefundApplication.if_cross_refund === 'Y') {
				this.$message.error('是否跨单退款为是时，不允许添加差价类型的退款明细');
				return false;
			}
			if (refundType == 'PRICE_DIFF' && this.form.billRefundApplication.if_dealer === 'Y') {
				this.$message.error('是否经销商订单为是时，不允许添加差价类型的退款明细');
				return false;
			}
			var targetMergeTradeNo = this.form.billRefundApplication.merge_trade_no_target || this.form.billRefundApplication.merge_trade_no
				, targetMergeTradeId = this.form.billRefundApplication.merge_trade_id_target || this.form.billRefundApplication.merge_trade_id
				, mergeNumberId = this.form.billRefundApplication.merge_trade_id
			if (/^OVERPAY$/.test(refundType) && this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && this.form.billRefundApplicationItem.length >= 1) {
				this.$message.error('退款方式=跨单结转时，只允许存在一行的多付的退款明细');
				return false;
			}
			if (/^OVER_FUND$/.test(refundType) && this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && this.form.billRefundApplicationItem.length >= 1) {
				this.$message.error('退款方式=跨单结转时，只允许存在一行的多付-货款的退款明细');
				return false;
			}

			if (!this.form.billRefundApplication.refund_way) {
				this.$message.error('请先选择退款方式')
			} else if (this.rules.merge_trade_no_target[0].required && !this.form.billRefundApplication.merge_trade_no_target && refundType !== 'CROSS_OVER') {
				this.$message.error('请先选择目标合并订单')
			} else if (this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && !/^(CROSS_OVER|OVERPAY)$/.test(refundType)) {
				this.$message.error('退款方式是跨单结转时只能选择多付')
			} else if (this.form.billRefundApplication.refund_way === 'DEALER_PREFERENTIAL' && !/^PREFERENTIAL$/.test(refundType) && !/^SINGLE_DISCOUNT$/.test(refundType) && !/^DISCOUNT$/.test(refundType)) {
				this.$message.error('退款方式是经销优惠退款时只能选择未前置优惠/单品优惠/外购折现')
			} else if (
				!/^(CROSS_OVER|REFUNDCARRYFORWARD)$/.test(refundType) && this.form.billRefundApplication.refund_way === 'CARRY_OVER'
				|| (/^REFUNDCARRYFORWARD$/.test(refundType) && this.form.billRefundApplication.refund_way !== 'CARRY_OVER')
			) {
				this.$message.error('换货结转时，退款类型只能选择换货结转')
			} else if (refundType === 'CANCEL' && this.form.billRefundApplication.original_type === 'TMALL' && !this.form.taobaoRefundDownload.oid) {
				this.$message.error('复核退款申请oid不存在')
			} else if (/^(OVERPAY|PREFERENTIAL|OTHER_REFUND|OVER_FUND)$/.test(refundType) || (refundType == 'OVER_DISCOUNT' && /^(2)$/.test(self.reason_version))) { //若是优惠折现和多付/多付-优惠前置、多付-货款则添加行
				var pushObj = {
					refund_type: refundType,
					deduct_fee: 0,
					apply_amount: 0,
					original_no: targetMergeTradeNo,
					original_id: targetMergeTradeId,
					original_type: 'MERGE',
					if_cancel_refund: 'N',
					refund_type_sub: refund_type_sub,
					refund_reason: '',
					refundReason: '',
					excessiveParam: {},
					reason_version: this.reason_version
				}
				if (refundType == 'COMPENSATION' || refundType == 'REPAIR') { //销售折让 退货货款
					pushObj.is_refund = 'Y'
				}
				if ((this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && refundType === 'OVERPAY') || (this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && refundType === 'OVER_FUND')) { // 一版：CROSS_OVER_T跨单结转 OVERPAY多付
					pushObj.refund_type = 'CROSS_OVER'
					pushObj.xentry_id = targetMergeTradeId
				}
				let scrStr = ''
				if (/^(PREFERENTIAL|OVER_DISCOUNT|OVER_FUND|OVERPAY)$/.test(refundType) || (pushObj.refund_type == 'CROSS_OVER' && /^(2|3|4)$/.test(pushObj.reason_version))) {
					let currentRefundReasonList = 'refundReasonList' + this.reason_version
					scrStr = this[currentRefundReasonList][refundType]
				}
				let idx = this.getStrCount(scrStr, '(')
				while (idx--) {
					let ii = 'filed' + idx
					pushObj.excessiveParam['filed' + idx] = ''
				}
				this.form.billRefundApplicationItem.push(pushObj)
				this._calcTotalPrice()
			} else {
				var CANCEL_oid = !is_show_CANCEL_alert_again && refundType === 'CANCEL' && this.form.billRefundApplication.original_type === 'TMALL' && this.form.taobaoRefundDownload.oid

				this.$root.eventHandle.$emit('alert', {
					title: {
						CANCEL: '未发取消',
						RETURNS: '退货货款(拉货状态=已拉货)',
						REFUNDCARRYFORWARD: '换货结转(单据状态=已审核)',
						CROSS_OVER: '跨单结转(表体合并单转出，表头合并单转入)',
						DISCOUNT: '外购折现',
						DELAY: '服务折让商品',
						SINGLE_DISCOUNT: '单品优惠选择商品',
						CARRIAGE: '批次订单(发货状态=已发货)(合并单号' + targetMergeTradeNo + ')',
						THREE: '批次订单(发货状态=已发货)(合并单号' + targetMergeTradeNo + ')',
						DISCOUNT: '批次订单(发货状态=已发货)(合并单号' + targetMergeTradeNo + ')',
						O2O_DIFF: '差价商品列表',
						PRICE_DIFF: '新增差价核算单',
						COMPENSATION: '售后单问题商品(销售折让、维修费)' + (this.form.billRefundApplication.after_order_no ? '(售后单号' + this.form.billRefundApplication.after_order_no + ')' : ''),
						REPAIR: '售后单问题商品(销售折让、维修费)' + (this.form.billRefundApplication.after_order_no ? '(售后单号' + this.form.billRefundApplication.after_order_no + ')' : ''),
						OVER_DISCOUNT: '优惠活动列表',
						INCOME_CARRY: '收入结转后退款',
					}[refundType],
					style: CANCEL_oid ? 'display:none;' : 'width:1100px;height:500px;',
					params: {
						refundType,
						CANCEL_oid,
						mergeTradeNo: targetMergeTradeNo,
						mergeTradeId: targetMergeTradeId,
						afterOrderNo: this.form.billRefundApplication.after_order_no,
						nickName: this.form.billRefundApplication.nick_name,
						shopId: this.form.billRefundApplication.shop_id,//订单店铺id
						originalShopId: this.form.billRefundApplication.original_shop_id,//原始店铺id
						isFromRefundRequest: true,
						refund_way: this.form.billRefundApplication.refund_way,
						original_type: this.form.billRefundApplication.original_type,
						mergeNumber: this.form.billRefundApplication.merge_trade_no,
						mergeNumberId: (self.form.billRefundApplication.if_cross_refund == "Y" && /^(OVER_DISCOUNT)$/.test(refundType)) ? this.form.billRefundApplication.merge_trade_id_target : self.form.billRefundApplication.merge_trade_id,
						originalNo: this.form.billRefundApplication.original_no,
						otherSearchParams: {
							where: this.selectMergeOrderObj.audit_status_where.map((o, i) => {
								if (i === 1) {//买家昵称
									o.value = this.form.billRefundApplication.nick_name
								} else if (i === 2) {//合并单号
									o.value = targetMergeTradeNo
								}

								return o
							}),
							page_name: 'scm_sys_trade',
						},
						refund_mode: this.form.billRefundApplication.refund_mode || "",//京东渠道-》退款模式
						priceDiffSource: 'button', //仅适用于差价功能
						sys_trade_id: this.form.billRefundApplication.sys_trade_id,//仅适用于差价功能
						sys_trade_no: this.form.billRefundApplication.sys_trade_no,//仅适用于差价功能
						goodsList: this.priceDiffGoodsList,//仅适用于差价功能
						saleman_id: this.form.billRefundApplication.saleman_id,//仅适用于差价功能
						differenceAccountObj: this.differenceAccountObj,
						operate_time_begin: this.operate_time_begin,
						operate_time_end: this.operate_time_end,
						apply_id: this.form.billRefundApplication.id,
						tid: this.form.taobaoRefundDownload.tid,
						ifCrossRefund: this.form.billRefundApplication.if_cross_refund == 'Y', // 适用于跨单退款增加店铺校验
            business_type_trade: this.form.business_type_trade, // 单据业务类型
						close: d => {
							this.differenceAccountObj.accountCouponList = d.accountCouponList
							this.differenceAccountObj.accountBasicList = d.accountBasicList
						},
						callback: d => {
							if (refundType === 'CANCEL' && CANCEL_oid && !d.length) {//当返回数据为空时，重新弹框选择
								this.addRefundType('CANCEL', 'show_CANCEL_alert_again')
								return
							} else if (refundType === 'CROSS_OVER') { // 换货转结
								// if(d.merge_trade_id === targetMergeTradeId){ 换货转结修改后注释该条
								if (d.merge_trade_id === mergeNumberId) {
									this.$message.error('目标合并订单不能与表头的合并订单号相同')
								} else {
									this.form.billRefundApplication.sys_trade_id_target = d.sys_trade_id
									this.form.billRefundApplication.sys_trade_no_target = d.sys_trade_no
									this.form.billRefundApplication.merge_trade_id_target = d.merge_trade_id
									this.$set(this.form.billRefundApplication, 'merge_trade_no_target', d.merge_trade_no)
								}
								return
							} else if (refundType === 'PRICE_DIFF') { //差价
								this.differenceAccountObj = JSON.parse(JSON.stringify(d))
								d = JSON.parse(JSON.stringify(d.basicSelectList))
								// 校验差价核算单是否有更改
								let ifCoupon = this.compareCouponList(), ifGoods = this.compareAccountBasicList(), ifChange = false
								let ifDate = (this.differenceAccountObj.order_date == this.oldDifferenceAccountObj.order_date)

								if (ifCoupon || ifGoods || ifDate) {
									ifChange = true
									this.deleteDillRefundApplicationItem('PRICE_DIFF')
								}
							} else if (refundType === 'RETURNS') {
								// 来源类型等于手工退款，四种店铺id["JDLS","JDMYG","JDPG","JDLSSH"]才需判断,销售单号与来源单号一致
								let hasDiffOriginalNo = d.some(item => item.sys_trade_no !== this.form.billRefundApplication.original_no)
								let isShop = [**************, ***************, **************, ***************].includes(this.form.billRefundApplication.original_shop_id);
								let isOther = this.form.billRefundApplication.original_type === "OTHER";
								if (isOther && isShop && hasDiffOriginalNo) {
									this.$message.warning("选择的销售单号与来源单号不一致")
									return
								}

							}
							d.forEach(obj => {
								if (!(new Set(this.changeApplyAmountList).has(obj.entry_id)) && refundType === 'PRICE_DIFF') {
									this.changeApplyAmountList.push(obj.entry_id)
								}
								var pushObj = {
									refund_type: refundType,
									deduct_fee: 0,
									apply_amount: 0,
									refund_reason: '',
									original_no: targetMergeTradeNo,
									original_id: targetMergeTradeId,
									original_type: 'MERGE',
									xentry_id: '',
									if_cancel_refund: 'N',
									platform_discount: obj.platform_discount || '',
									excessiveParam: {},
									reason_version: this.reason_version,
									tempId: new Date(),
								}
								let scrStr = ''
								if (/^(OVER_DISCOUNT)$/.test(refundType)) {
									pushObj.xentry_id = obj.order_entry_id
									pushObj.original_id = this.form.billRefundApplication.if_cross_refund === 'Y' ? this.form.billRefundApplication.merge_trade_id_target : this.form.billRefundApplication.merge_trade_id
									pushObj.original_no = this.form.billRefundApplication.if_cross_refund === 'Y' ? this.form.billRefundApplication.merge_trade_no_target : this.form.billRefundApplication.merge_trade_no
									pushObj.act_id = obj.discount_id
								}
								if (/^(RETURNS)$/.test(refundType)) {
									pushObj.refundReason = '整单退货'
									let currentRefundReasonList = 'refundReasonList' + this.reason_version
									scrStr = this[currentRefundReasonList][refundType]['整单退货']
									let idx = this.getStrCount(scrStr, '(')
									while (idx--) {
										let ii = 'filed' + idx
										pushObj.excessiveParam['filed' + idx] = ''
									}
									pushObj.excessiveParam['filed0'] = obj.question_goods_name;
								} else {
									if (/^(PREFERENTIAL|SINGLE_DISCOUNT|CARRIAGE|THREE|REPAIR|COMPENSATION|DISCOUNT|PRICE_DIFF|OVER_DISCOUNT)$/.test(refundType)) {

										let currentRefundReasonList = 'refundReasonList' + this.reason_version
										scrStr = this[currentRefundReasonList][refundType]
										let idx = this.getStrCount(scrStr, '(')
										while (idx--) {
											let ii = 'filed' + idx
											pushObj.excessiveParam['filed' + idx] = ''
										}
										if (refundType == 'OVER_DISCOUNT') {
											pushObj.apply_amount = Number(obj.apply_amount)
											pushObj.excessiveParam['filed0'] = '优惠活动编码：' + obj.discount_no + '活动名称：' + obj.activity_name + ''
											pushObj.excessiveParam['filed1'] = obj.apply_amount
										}
									}
								}



								if (refundType === 'CANCEL' || refundType === 'RETURNS') { // 未发取消 退货货款
									pushObj.sys_trade_id = obj.sys_trade_id
								}

								if (refundType === 'CANCEL') {//未发取消
									pushObj.refund_reason = obj.cancel_reason
									pushObj.apply_amount = obj.act_price
									pushObj.xentry_id = obj.entry_id

									this.CANCEL_List.push(obj)

								} else if (refundType === 'DELAY') {//服务折让
									// pushObj.apply_amount = obj.act_price
									let data = this.dataList.find(item => {
										return item.entry_id === obj.entry_id;
									})

									pushObj.xentry_id = obj.entry_id
									//服务折让 自动填充退款原因
									let time1 = !!obj.commit_time ? moment(obj.commit_time).format('YYYY-MM-DD') : ""
									let time2 = !!obj.out_stock_time ? moment(obj.out_stock_time).format('YYYY-MM-DD') : ""

									pushObj.refund_reason = `商品名称：${obj.material_name} ；原因描述：${data ? data.reason : ''}；承诺发货时间：${time1}`
									obj.refund_reason = `商品名称：${obj.material_name}；原因描述：${data ? data.reason : ''}；承诺发货时间：${time1}`
									this.DELAY_List.push(obj);

								} else if (refundType === 'SINGLE_DISCOUNT') {//单品优惠
									pushObj.xentry_id = obj.entry_id
									pushObj.apply_amount = Number(obj.sum_actual_amount)

									this.SINGLE_DISCOUNT_List.push(obj)

								} else if (/^(CARRIAGE|THREE|DISCOUNT)$/.test(refundType)) { // 运费 三包 外购折现

									pushObj.xentry_id = obj.batch_trade_id
									pushObj.original_no = obj.batch_trade_no
									pushObj.original_id = obj.batch_trade_id
									pushObj.original_type = 'BATCH'

								} else if (/^(O2O_DIFF|PRICE_DIFF|INCOME_CARRY)$/.test(refundType)) {
									this[refundType + '_List'].push(obj)
									pushObj.pricediff = this.setOrDelPricediffData(obj, 'wantLeftKey')
									// 创建不可枚举属性，对外界无副作用，用于删除时找到一览差价商品对应关系
									Object.defineProperties(obj, {
										__id: {
											configurable: true,
											writable: true,
											enumerable: false,
											value: Math.random(),
										},
									})
									Object.defineProperties(pushObj.pricediff, {
										__id: {
											configurable: true,
											writable: true,
											enumerable: false,
											value: obj.__id,
										},
									})
									pushObj.refund_reason = ''
									pushObj.refundReason = '' //差价在版本2中成固定格式
									if (refundType == 'INCOME_CARRY') {
										pushObj.xentry_id = obj.income_carryover_id
										pushObj.apply_amount = obj.apply_amount//取差价
										pushObj.original_id = obj.income_carryover_id
										pushObj.original_no = obj.bill_code
										pushObj.original_type = 'INCOMECARRY'
									} else {
										pushObj.xentry_id = obj.entry_id
										pushObj.apply_amount = refundType == 'PRICE_DIFF' ? obj.apply_amount : obj.price_spread//取差价
										pushObj.original_id = refundType == 'PRICE_DIFF' ? this.form.billRefundApplication.merge_trade_id : obj.merge_trade_id
										pushObj.original_no = refundType == 'PRICE_DIFF' ? this.form.billRefundApplication.merge_trade_no : obj.merge_trade_no
									}
								} else if (/^(COMPENSATION|REPAIR)$/.test(refundType)) { // 销售折让、维修

									pushObj.apply_amount = obj._new_act_price
									pushObj.original_no = obj.after_order_no
									pushObj.original_id = obj.after_order_id
									pushObj.original_type = 'AFTERSALES'
									pushObj.after_order_no = obj.after_order_no
									pushObj.after_order_id = obj.after_order_id
									pushObj.parent_question_id = obj.parent_question_id
									pushObj.question_sub_id = obj.question_sub_id
									pushObj.xentry_id = obj.question_sub_id
									pushObj.refund_reason = obj.question_description

									this.form.billRefundApplication.after_order_no = obj.after_order_no
									this.form.billRefundApplication.after_order_id = obj.after_order_id
									this[refundType + '_List'].push(obj)
								} else if (/^(REFUNDCARRYFORWARD|RETURNS)$/.test(refundType)) { // 换货结转、退货货款

									if (this.form.billRefundApplicationItem.map(o => o.xentry_id).indexOf(obj.id) !== -1) {
										this.$message.error('一个退货商品行只能选择一次')
										return
									}
									if (/^(REFUNDCARRYFORWARD)$/.test(refundType)) {
										pushObj.realyRefundType = 'REFUNDCARRYFORWARD'
										pushObj.excessiveParam = {}
									}

									pushObj.refund_type = 'RETURNS'
									pushObj.xentry_id = obj.id/*商品行id*/
									pushObj.apply_amount = obj.act_price
									pushObj.original_no = obj.bill_returns_no
									pushObj.original_id = obj.after_bill_id
									pushObj.original_type = 'RETURNS'
									refundType === 'RETURNS' && this.RETURNS_List.push(obj)
								}


								if (/^(COMPENSATION|REPAIR|CANCEL|RETURNS|DELAY|SINGLE_DISCOUNT|O2O_DIFF|PRICE_DIFF)$/.test(refundType)) {
									this[refundType + '_Total'] = this.calcBrowseTotal(this[refundType + '_List'], this.arrItem[refundType])

								}

								if (refundType == 'PRICE_DIFF') {
									pushObj.excessiveParam.filed0 = obj.materia_number;
									pushObj.excessiveParam.filed1 = fn.dateFormat(this.differenceAccountObj.order_date);
									pushObj.excessiveParam.filed2 = obj.virtual_act_price_price;
									pushObj.excessiveParam.filed3 = obj.apply_amount;
									let ifHasSave = this.form.billRefundApplicationItem.some(item => {
										return (item.xentry_id == obj.entry_id && item.refund_type == 'PRICE_DIFF')
									})
									if (ifHasSave) {
										this.form.billRefundApplicationItem.forEach(item => {
											if (item.xentry_id == obj.entry_id) {
												item.apply_amount = pushObj.apply_amount
											}
										})
									} else {
										this.form.billRefundApplicationItem.push(pushObj)
									}

								} else {
									this.form.billRefundApplicationItem.push(pushObj)
								}
							})
							this._calcTotalPrice()
						},
					},
					component: () =>
						/^(CARRIAGE|THREE|DISCOUNT)$/.test(refundType)
							? import('@components/after_sales_service/select_order')
							: (
								refundType === 'CROSS_OVER'
									? import('@components/order/list_alert_copy')
									: (
										/^(O2O_DIFF)$/.test(refundType)
											? import('@components/after_sales_refund/differenceGoods')
											: (/^(PRICE_DIFF)$/.test(refundType)
												? import('@components/after_sales_refund/differenceAccount')
												:
												(/^(OVER_DISCOUNT)$/.test(refundType)
													? import('@components/after_sales_refund/overDiscountGoods')
													: (
														/^(INCOME_CARRY)$/.test(refundType)
															? import('@components/after_sales_refund/incomeCarry')
															: import('@components/after_sales_refund/addRefundType')
													)
												)
											)
									)
							),
				})
			}
		},
		isNull(value) {
			if (!value && typeof value != "undefined" && value != 0) {
				return true
			} else {
				return false
			}
		},
		// 获取差价核单tab-基本信息
		getGoodsDetailList() {
			let postData = {
				merge_trade_id: this.form.billRefundApplication.merge_trade_id,
				merge_trade_no: this.form.billRefundApplication.merge_trade_no,
				virtual_created: this.differenceSearchObj.order_date,// ,*************
				shop_id: this.form.billRefundApplication.shop_id,
				apply_id: this.form.billRefundApplication.id
			}, self = this
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/getDiffPriceAccounting', postData, res => {
				if (res.body.result) {
					let diffPriceOrders = res.body.content.diffPriceOrders || []
					let diffPriceDiscounts = res.body.content.diffPriceDiscounts || []
					let priceListOrders = res.body.content.priceListOrders || []
					diffPriceOrders.forEach(item => {
						item.virtual_act_price_price = self.isNull(item.virtual_discount_amount) ? item.virtual_stand_price : (item.virtual_stand_price - item.virtual_discount_amount).toFixed(2)
						item.max_diff_price = (item.act_price - item.virtual_act_price_price).toFixed(2)
					})
					let totalCols = ['max_diff_price'], maxDiffPriceTotal = 0, virtualDiscountAmountTotal = 0
					diffPriceOrders.forEach(item => {
						maxDiffPriceTotal = Number(maxDiffPriceTotal) + Number(item.max_diff_price)
						virtualDiscountAmountTotal = Number(virtualDiscountAmountTotal) + Number(item.virtual_discount_amount)
					})
					self.accountBasicList = diffPriceOrders.concat({ max_diff_price: '总计：' + (maxDiffPriceTotal).toFixed(2), virtual_discount_amount: '总计：' + (virtualDiscountAmountTotal).toFixed(2) })
					self.accountCouponList = diffPriceDiscounts
					// 保存历史数据做对比
					self.oldDifferenceAccountObj.accountBasicList = JSON.parse(JSON.stringify(diffPriceOrders))
					self.oldDifferenceAccountObj.accountCouponList = JSON.parse(JSON.stringify(diffPriceDiscounts))
					self.differenceSearchObj.priceListOrders = JSON.parse(JSON.stringify(priceListOrders))
					self.operate_time_begin = res.body.content.operate_time_begin
					self.operate_time_end = res.body.content.operate_time_end
					self.created = res.body.content.created || ''
					self.end_time = res.body.content.end_time || ''
					self.differenceSearchObj.order_date = res.body.content.virtual_created || ''
					self.differenceAccountObj.order_date = res.body.content.virtual_created || ''

					self.accountBasicList[self.accountBasicList.length - 1].entry_id = '合计'
					// 保存现有数据作为传承
					self.differenceAccountObj.accountCouponList = JSON.parse(JSON.stringify(diffPriceDiscounts))
					self.differenceAccountObj.accountBasicList = JSON.parse(JSON.stringify(self.accountBasicList))

				} else {
					self.accountBasicList = []
					self.accountCouponList = []
					self.$message.error(res.body.msg)
				}
			}, err => {
				self.accountBasicList = []
				self.accountCouponList = []
				self.$message.error(err)
			});
		},
		//删除特定的退款类型
		deleteDillRefundApplicationItem(type) {
			let self = this
			this.form.billRefundApplicationItem.forEach((item, idx) => {
				if (item.refund_type == type) {
					if (item.id) {
						delete item.excessiveParam
						delete item.refundReason
						self.form.delApplicationItem.push(item)
					}
					self.form.billRefundApplicationItem.splice(idx, 1)
					self.copyChooseTchList = []
					self.copyChooseTchIds = []

				}
			})
		},
		getSysGoodsList() {
			this.ajax.postStream('/order-web/api/mergetrade/order/list', {
				merge_trade_id: this.form.billRefundApplication.merge_trade_id,
				page_name: 'scm_sys_order',
				page_no: 1,
				page_size: 10000,
				where: [{
					condition: "OR",
					field: "a503455fe11615116802524320067e25",
					operator: "=",
					table: "c2efe235f5b46d1b211cdda901d3d361",
					value: this.form.billRefundApplication.sys_trade_no
				}]
			}, res => {
				if (res.body.result) {
					self.priceDiffGoodsList = JSON.parse(JSON.stringify(res.body.content.list)) || []
				} else {
					self.priceDiffGoodsList = []
				}
			}, err => {
				self.priceDiffGoodsList = []
			});
		},
		setOrDelPricediffData(obj, type) {
			var keyMap = {
				price_list_id: 'price_list_id',
				price_area: 'price_list_name',
				shop: 'price_shop_name',
				new_material_id: 'price_material_id',
				new_goods_code: 'price_material_no',
				new_goods_name: 'price_material_name',
				new_specification: 'price_material_desc',
				new_price: 'new_price',
				old_goods_code: 'materia_number',
				old_goods_name: 'materia_name',
				old_specification: 'materia_specifications',
				price: 'stand_price',
				order_shop: 'shop_name',
				old_price: 'act_price',
				order_no: 'tid',
				order_status: 'status',
				diff_price: 'price_spread',
				buy_time: 'created',
				units: 'unit_name',
				valid_date: 'enable_time',
				disabled_date: 'disable_time',
			}
				, returnObj = {}

			Object.keys(keyMap).forEach(key => {
				if (type === 'wantLeftKey') {
					returnObj[key] = obj[keyMap[key]]
				} else {
					returnObj[keyMap[key]] = obj[key]
				}
			})

			return returnObj
		},

		//下拉选择退款原因的所有退款类型
		getSelectRefundType() {

			this.disabledOption = [];
			let refundTypeArray = __AUX.get('AFTERSALE_REFUND_TYPE').filter((item, index, array) => {
				return item.tag == 'S';
			});

			this.disabledOption = refundTypeArray.map((item) => {
				return item.code
			});
			return this.disabledOption;

		},

		//获取退款原因文本显示
		getRefundReasonOfText(item) {
			let refundType = item.refund_type, isSelect = this.getSelectRefundType().some((i) => {
				return i == refundType
			});
			let refundReason = item.refund_reason
			if (isSelect) {
				let refundReasonArray = __AUX.get('qrtkyy');
				for (var i = 0; i < refundReasonArray.length; i++) {
					if (item.refund_reason == refundReasonArray[i].code) {
						refundReason = refundReasonArray[i].name;
						break;
					}

				}
			}
			return refundReason;


		},

		// 不能编辑时，将退款明细相同类型，来源单号一样的合并为一行
		mergeBillRefundApplicationItem() {
			var itemObj = {}
				, newItemList = []

			if (this.isCanEdit || this.form.billRefundApplication.refund_way === 'CARRY_OVER') {

				return this.form.billRefundApplicationItem
			}
			let self = this;
			this.form.billRefundApplicationItem.forEach(obj => {
				if (obj.original_no) {
					if (!itemObj[obj.refund_type + obj.original_no]) itemObj[obj.refund_type + obj.original_no] = Object.assign({}, obj, {
						apply_amount: 0,
						platform_discount: 0,
						deduct_fee: 0,
						actual_amount: 0,
						refund_reason: [],
						if_cancel_refund: [],
					});
					if (obj.platform_discount) {
						itemObj[obj.refund_type + obj.original_no].platform_discount += obj.platform_discount
					}

					let reason = self.getRefundReasonOfText(obj);

					itemObj[obj.refund_type + obj.original_no].apply_amount += obj.apply_amount
					itemObj[obj.refund_type + obj.original_no].deduct_fee += obj.deduct_fee
					itemObj[obj.refund_type + obj.original_no].actual_amount += obj.actual_amount

					itemObj[obj.refund_type + obj.original_no].refund_reason.push(reason)

					itemObj[obj.refund_type + obj.original_no].if_cancel_refund.push({ Y: '是', N: '否' }[obj.if_cancel_refund])
				} else {
					newItemList.push(obj)
				}
			})

			Object.keys(itemObj).forEach(key => {
				newItemList.push(Object.assign({}, itemObj[key], {
					apply_amount: Number(itemObj[key].apply_amount.toFixed(10)),
					deduct_fee: Number(itemObj[key].deduct_fee.toFixed(10)),
					actual_amount: Number(itemObj[key].actual_amount.toFixed(10)),
					platform_discount: Number(itemObj[key].platform_discount.toFixed(2)),
					refund_reason: itemObj[key].refund_reason.join('\n'),
					if_cancel_refund: itemObj[key].if_cancel_refund.join(),
				}))
			})

			return newItemList
		},
		// 下推售后单
		pushAfterSaleOrder() {
			this.ajax.postStream(
				'/afterSale-web/api/aftersale/bill/refundApp/checkBeforePushAftersaleOrder',
				{ id: this.form.billRefundApplication.id },
				(res) => {
					if (res.body.result) {
						this.$root.eventHandle.$emit('creatTab', {
							name: "新增售后单",
							params: {
								from: 'interceptGoodsRequestLIst',
								merge_trade_no: this.form.billRefundApplication.merge_trade_no
							},
							component: () => import('@components/after_sales/index')
						});
					} else {
						this.$message.error(res.body.msg)
					}
				}
			)
		},

    getShopInfo() {
      const shop_id = this.form?.billRefundApplication?.shop_id;
      this.shopInfo = {};
      if (shop_id) {
         let url = '/material-web/api/shopv2/edit';
        this.ajax.postStream(url, {
          shop_id,
        }, (res)=>{
            let {result,content:{shopV2Vo}} = res.body
            if(!result) return;
            this.shopInfo = shopV2Vo;
        });
      }
    },

		// 获得订单详情
		getOrderDetail(msg, orderId, result) {
      this.orderDetailLoading=true;
			// if (!orderId) return
			this.ajaxFunc('getBillRefund', { id: orderId || this.form.billRefundApplication.id }, res => {
				msg !== false && this.$message.success(msg || res.body.msg)
        if(!res.body.result){
          return
        }
				this.form = res.body.content || {};

        this.getShopInfo();

        // 初始化进度标签
        if(this.form.billRefundApplication){
          this.progressLabel=this.form.billRefundApplication.progress_label||'';
        }

				if (!this.form.taobaoRefundDownload) this.form.taobaoRefundDownload = {}
				// B2C退款时，不管接口返回什么值，强制为B2C线上退款
				if (this.form.billRefundApplication.original_type === 'B2C') this.form.billRefundApplication.refund_way = 'B2C_ONLINE'
				this.oldBillRefundApplicationItem = JSON.parse(JSON.stringify(this.form.billRefundApplicationItem))
				this.form.billRefundApplicationItem.forEach(item => {
					if (this.form.billRefundApplication.refund_way != 'CARRY_OVER') {
						item.excessiveParam = {}
						if (!/^(O2O_DIFF|DELAY|REFUNDCARRYFORWARD|INCOME_CARRY)$/.test(item.refund_type)) {
							if ((/^(OVERPAY|PRICE_DIFF)$/.test(item.refund_type) && item.reason_version == '1') || (item.refund_type == 'RETURNS' && /^(2|3|4)$/.test(item.reason_version) && this.form.billRefundApplication.refund_way != 'CARRY_OVER') || (item.refund_type == 'CROSS_OVER' && /^(2|3|4)$/.test(item.reason_version) && this.form.billRefundApplication.refund_way == 'CROSS_OVER_T')) {
								let str = item.refund_reason
								let spliceStr = str.split(':')
								item.refundReason = spliceStr[0]
							} else {
								item.refundReason = ''
							}
							let reason = !!item.refund_reason ? item.refund_reason : '';

							let refundReasonArray = parseOuterParenthesesText(reason);
							let refundReasonArrayLen = refundReasonArray.length
							for (let i = 0; i < (refundReasonArrayLen - 1); i++) {
								item.excessiveParam['filed' + i] = ''
							}
							refundReasonArray.forEach((fieldValue, idx) => {
								item.excessiveParam['filed' + idx] = fieldValue
							})
						}
					}
				})
				this.setStatus();
				this.getUserPersonDiscountAttribute()
				this.getGoodsDetailList()// 差价核算 -- 基本信息
				this.aftersaleOrderList = res.body.content.aftersaleOrderList || []
				this.canBuildNoSourceOrder = false

				if (this.merge_trade_id = this.form.billRefundApplication.merge_trade_id) {
					this.getCouponList()
					this.getAccountList()
          this.$refs.inner && this.$refs.inner.getInnerList(this.merge_trade_id);
				}
				Vue.prototype.tabNo[Vue.prototype.activeTab] = res.body.content.billRefundApplication.bill_no || '';
				let pageParams = {
					id: res.body.content.billRefundApplication.id
				}
				Vue.prototype.tabId[Vue.prototype.activeTab] = JSON.stringify(pageParams);

				this._calcTotalPrice()//里面代码setTimeout执行，所以下面_refresh保存旧数据时也要先setTimeout
				setTimeout(() => {
					this._refresh(null, this.form)
				})
				if (this.isMounted) {
					this.isMounted = false
					let { business_status, refund_way } = this.form.billRefundApplication
					if (business_status === 'FINISH' && !['ALIPAY', 'BANK'].includes(refund_way)) {
						this.getDefaultDealerCustomer()
					}
				}
				this.getList()
				this.updateRefoundModeEditable()
        this.orderDetailLoading=false;
			},()=>{
        this.orderDetailLoading=false;
      })
		},

		renderSnapshotDetail(res) {
			this.$message.success(res.body.msg)
			this.form = res.body.content || {}
			if (!this.form.taobaoRefundDownload) this.form.taobaoRefundDownload = {}

			this.isCanEdit = false
			if (this.merge_trade_id = this.form.billRefundApplication.merge_trade_id) {
				this.getAccountList()
				this.getCouponList()
        this.$refs.inner && this.$refs.inner.getInnerList(this.merge_trade_id);
			}
			this._calcTotalPrice()
		},
		getCROSSOVERCanClick(cb, errorCb) {
			var salesmanTypeList = ['SALES_PERSON', 'CUSTOMER_SERVICE_EXECUTIVE', 'CUSTOMER_SERVICE_ABC']
				, personId = this.getEmployeeInfo('personId')
				, queryFunc = () => {
					var salesmanType = salesmanTypeList.shift()
					if (!salesmanType) {
						this.$message.error('您没有跨单结转权限，请联系店长操作')
						errorCb && errorCb()
						return
					}
					this.getUserPersonBusinessAttributeFlag = true;
					this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttribute', {
						personId,
						salesmanType,
						attribute: 'IF_CHOOSE_CROSS_OVER',
					}, res => {
						this.getUserPersonBusinessAttributeFlag = false;

						if (res.body.result && res.body.content && res.body.content.attributeValueText === '是') {
							cb && cb()
						} else {
							queryFunc()
						}
					})
				}

			queryFunc()
		},
		blockSameIdOpenTab(id, cb) {// 打开窗口订单
			if (!cb) {//set className
				this.$el.setAttribute('data-symbol', 'billRefundApplication_' + id)
			} else {//检测是否已打开相同退款申请单
				if (document.querySelector('[data-symbol="billRefundApplication_' + id + '"]')) {
					this.$message.error('该退款申请单已经打开')
				} else {
					cb()
				}
			}
		},

		updateFinish() {
			let ids = [this.form.billRefundApplication.id];

			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/updateFinish', { ids: ids }, res => {
				if (res.body.result) {
					this.$message.success(res.body.msg)
					this.getOrderDetail()
					this.getBrowseList(this.params.id)
				} else {
					this.$message.error(res.body.msg)
				}
			})
		},
		supplementaryPaymentDetails() {
			let params = {

			},
				self = this;

			params.callback = d => {

				if (d) {
					self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/supplementaryPaymentDetails',
						{
							id: self.form.billRefundApplication.id,
							pay_amount: d
						}, res => {
							if (res.body.result) {
								this.$message.success(res.body.msg)
								this.getOrderDetail()
								this.getBrowseList(this.params.id)
							} else {
								this.$message.error(res.body.msg)
							}
						})
				}
			}
			self.$root.eventHandle.$emit('alert', {
				params: params,
				component: () => import('@components/after_sales_refund/PaymentDetails'),
				style: 'width:400px;height:100px',
				title: '预售订单添加支付明细'
			});
		},
		batchUpdate(type) {
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/' + type, [this.form.billRefundApplication.id], res => {
				if (res.body.result) {
					this.$message.success(res.body.msg)
					this.getOrderDetail()
				} else {
					this.$message.error(res.body.msg)
				}
			})
		},
		// 资料请求操作
		financeConfirm(type) {
			let ids = [];
			let self = this;
			self.infoSelect.forEach(item => {
				ids.push(item.id);
			})
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/' + type, ids, res => {
				if (res.body.result) {
					this.$message.success(res.body.msg)
					this.getInfoRequestList()
				} else {
					this.$message.error(res.body.msg)
				}
			})
		},
		// 开启补充资料
		openReview() {
			let self = this;
			new Promise((resolve, reject) => {
				self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/openRequestInfoControl?permissionCode=ALLOW_DATA_REQUEST', self.form.billRefundApplication.id, res => {
					if (res.body.result) {

						resolve && resolve(res.body.content)
					} else {
						self.$message.error(res.body.msg);
					}
				})
			}).then(res => {
				self.$root.eventHandle.$emit('alert', {
					title: '补充资料列表',
					style: 'width:800px;height:600px',
					component: () => import('@components/after_sales_refund/reviewList.vue'),
					params: {
						list: res,
						callback: d => {
							self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/allowDataRequest', d, res => {
								if (res.body.result) {
									self.$message.success(res.body.msg)
									self.getInfoRequestList()
								} else {
									self.$message.error(res.body.msg)
								}
							})
						},
						data: { billRefundApplicationItem: self.form.billRefundApplicationItem, apply_id: self.form.billRefundApplication.id }
					},
				})
			})



		},
		//客服补充资料
		openServiceReview() {
			let self = this;
			this.$root.eventHandle.$emit('alert', {
				title: '客服补充资料列表',
				style: 'width:800px;height:600px',
				component: () => import('@components/after_sales_refund/reviewServerList.vue'),
				params: {
					callback: d => {
						this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/businessSupplyDataRequest', d, res => {
							if (res.body.result) {
								this.$message.success(res.body.msg)
								this.getInfoRequestList()
							} else {
								this.$message.error(res.body.msg)
							}
						})
					},
					data: { InfoRequestList: self.InfoRequestList, apply_id: self.form.billRefundApplication.id }
				},
			})
		},
		review(type) {
			let self = this,
				ids = [];
			self.selectRefoudDetailLists.forEach(item => {
				ids.push(item.id);
			})
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/' + type, { id: self.form.billRefundApplication.id, ids: ids }, res => {
				if (res.body.result) {
					this.$message.success(res.body.msg)
					this.getOrderDetail()
				} else {
					this.$message.error(res.body.msg)
				}
			})
		},
		infoListSelectChange(list) {
			this.infoSelect = list;
		},
		editFinance(callback) {
			let self = this;
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/updateRefundFinanceRemark', {
				id: this.params.id,
				finance_remark: this.form.billRefundApplication.finance_remark,
			}, res => {
				if (res.body.result) {
					this.$message.success(res.body.msg)
					callback && callback(res.body.content || {})
				} else {
					this.$message.error(res.body.msg)
				}
			})
		},
		//经销财账核对
		dealerMoneyCheck() {
			if (this.form.billRefundApplication.refund_way != 'DEALER_PREFERENTIAL') {
				this.$message.error('退款方式不为经销优惠退款，不允许操作此按钮')
				return
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/updateDealerMoneyCheck?permissionCode=REFUND_DEALER_MONEY_CHECK', {
				bill_no: this.form.billRefundApplication.bill_no,
			}, res => {
				if (res.body.result) {
					this.$message.success(res.body.msg)
					setTimeout(() => {
						this.getOrderDetail()
					}, 1000)
				} else {
					this.$message.error(res.body.msg)
				}
			}, err => {
				this.$message.error(err)
			})
		},
		//强制作废
		forceInvalid() {
			let self = this;
			if (!this.form.billRefundApplication) {
				this.$message.error("退款单id为空，无法操作");
				return;
			}
			if (this.form.billRefundApplication.original_type == 'OTHER') {
				this.$message.error("来源类型应为非手工退款");
				return;
			}
			if (!['CREATE', 'REJECT'].includes(this.form.billRefundApplication.business_status)) {
				this.$message.error("业务状态应为创建、驳回");
				return;
			}
			this.$root.eventHandle.$emit("alert", {
				title: "强制作废",
				style: "width:600px;height:300px",
				component: () =>
					import("@components/after_sales_refund/forceInvalidAlertModalByDetail.vue"),
				params: {
					callback: (d) => {
						self.forceInvalidLoading = true;
						self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/compelCancel', {
							id: this.form.billRefundApplication.id,
							finance_remark: d.invalid_reason
						}, res => {
							if (res.body.result) {
								self.$message.success(res.body.msg)
								setTimeout(() => {
									self.getOrderDetail()
								}, 1000)
							} else {
								self.$message.error(res.body.msg)
							}
							self.forceInvalidLoading = false;
						}, err => {
							self.$message.error(err)
							self.forceInvalidLoading = false;
						})
					},
				},
			});
		},
		updateShopRefundSource() {
			let self = this;
			let id = this.form.billRefundApplication.id;

			if (!this.form.billRefundApplication) {
				this.$message.error("退款单id为空，无法操作");
				return;
			}
			self.$root.eventHandle.$emit('alert', {
				params: {
					id: id,
					callback(data) {
						if (data) {
							self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/updateShopRefundSource', { id: id, if_ls_refund: data.if_three_age == true ? 'Y' : 'N' }, res => {
								if (res.body.result) {
									self.$message.success(res.body.msg)
									setTimeout(() => {
										self.getOrderDetail()
									}, 1000)
								} else {
									self.$message.error(res.body.msg)
								}
							})
						}
					}
				},
				component: () => import("@components/after_sales/updateThreeAge"),
				style: 'width:300px;height:200px',
				title: '是否林氏退款'
			});
		},

		beforeAuditRefund() {
			let self = this;
			self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/checkZeroRefund', [self.form.billRefundApplication.id], res => {
				if (res.body.result) {
					self.ajaxFunc('confirmRefund', [self.form.billRefundApplication.id], res => self.getOrderDetail(res.body.msg))
				} else {
					self.$confirm(
						"客户可能已修改过后台的退款金额，请确认是否继续复核退款单",
						"提示",
						{
							confirmButtonText: "确定",
							cancelButtonText: "取消",
							type: "danger",
						}
					).then(() => {
						self.ajaxFunc('confirmRefund', [self.form.billRefundApplication.id], res => self.getOrderDetail(res.body.msg))
					}).catch(() => {
						return false;
					});
				}
			})
		},
		// 判断退款单是否下推
		isPushEvent() {
			this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryExtInterfaceByBillNo', {
				bill_no: this.params.bill_no,
				bill_type: 'REFUND',
			}, res => {
				var data = res.body
				if (!data.result) {
					this.$message.error(data.msg)
					return
				}
				if (data.content.length) {
					this.isPush = true
				}
			})
		},
		// 获取可直接进行驳回的人员
		isMembers() {
			this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryDataList', {
				categoryCode: "REFUND_REFUSAL_ADMINISTRATOR"
			}, res => {
				if (res.body.result) {
					var arr = res.body.content.list
					if (arr.length > 0) {
						for (var i = 0; i < arr.length; i++) {
							if (arr[i].code == this.getEmployeeInfo("employeeNumber")) {
								this.isMember = true
							}
						}
					}
				}
			})

		},
		// 更新当前状态
		//退款模式是否显示
		updateRefoundModeEditable() {

      //来源类型为京东的新退款申请单，根据字段taobaoRefundDownload.refund_mode，退款模式固定=先行赔付、自主售后，不可编辑
      if(this.form.billRefundApplication.original_type=='JD'&&['ADVANCE_COMPENSATION','INDEPENDENT_AFTER_SALES'].includes(this.form.taobaoRefundDownload.refund_mode)){
        this.refoundModeEditable = false
        this.form.billRefundApplication.refund_mode=this.form.taobaoRefundDownload.refund_mode
        return
      }

			// 业务状态不是创建、驳回时，不可编辑
			if (!['CREATE', 'REJECT'].includes(this.form.billRefundApplication.business_status)) {
				console.log('业务状态不是创建、驳回时，不可编辑',{business_status:this.form.billRefundApplication.business_status})
				this.refoundModeEditable = false
				return;
			}

			// 来源类型是京东 并且 业务状态是创建 并且 退款模式字段有值 不可编辑
			if (
				this.form.billRefundApplication.original_type == 'JD' &&
				this.form.billRefundApplication.business_status == "CREATE" &&
				this.form.billRefundApplication.refund_mode
			) {
				console.log('来源类型是京东 并且 业务状态是创建 并且 退款模式字段有值 不可编辑',
				{original_type:this.form.billRefundApplication.original_type,
				business_status:this.form.billRefundApplication.business_status,
				refund_mode:this.form.billRefundApplication.refund_mode})
				this.refoundModeEditable = false
				return;
			}

			// 来源类型是手工退款（original_type = OTHER) 并且 是导入 并且 业务状态是创建 并且 退款模式字段有值 不可编辑
			if (
				this.form.billRefundApplication.original_type == 'OTHER' &&
				this.form.billRefundApplication.is_import == 'Y' &&
				this.form.billRefundApplication.business_status == "CREATE" &&
				this.form.billRefundApplication.refund_mode
			) {
				console.log('来源类型是手工退款（original_type = OTHER) 并且 是导入 并且 业务状态是创建 并且 退款模式字段有值 不可编辑',
				{original_type:this.form.billRefundApplication.original_type,
				is_import:this.form.billRefundApplication.is_import,
				business_status:this.form.billRefundApplication.business_status,
				refund_mode:this.form.billRefundApplication.refund_mode})
				this.refoundModeEditable = false
				return;
			}

			// 除以上的其他情况，可以编辑
			console.log('除以上的其他情况，可以编辑',
			{original_type:this.form.billRefundApplication.original_type,
			is_import:this.form.billRefundApplication.is_import,
			business_status:this.form.billRefundApplication.business_status,
			refund_mode:this.form.billRefundApplication.refund_mode})
			this.refoundModeEditable = true
		},

		onCheckUnlock() {
			if (
        (this.form.taobaoRefundDownload.status == 'SUCCESS' ||
        this.form.taobaoRefundDownload.status == 'REFUNDED' ||
        this.form.taobaoRefundDownload.status == 'KUAISHOU_REFUND_SUCCESS' ||
        this.form.taobaoRefundDownload.status == 'SUNING_REFUND_SUCCESS' ||
        this.form.taobaoRefundDownload.status == 'VIP_REFUND_FINISHED' ||
        this.form.taobaoRefundDownload.status == 'DOUYIN_REFUND_SUCCESS') ||
        ['JD_ZY', 'POS'].includes(this.form.billRefundApplication.original_type)
      ) {
					this.beforeAuditRefund();
				} else {
					this.$message.error('当前平台退款状态未退款成功，请稍后再试！')
				}
		},

    onOpenMergeTradeDetail() {
      this.$root.eventHandle.$emit('creatTab',{
				name:"合并订单详情",
				params: {
          merge_trade_id: this.form.billRefundApplication.merge_trade_id
        },
				component: () => import('@components/order/merge.vue')
			});
    },

    onOpenSysTradeDetail() {
      this.$root.eventHandle.$emit('creatTab',{
				name:"销售订单详情",
				params: {
          sys_trade_id: this.form.billRefundApplication.sys_trade_id
        },
				component: () => import('@components/order/detail.vue')
			});
    }
	},
	created() {
		this.jd_shops = __AUX.get("jd_shops").filter(item => item.status == "1").map(item => item.tag)

		let refund_type = __AUX.get('refund_type_option');
		let dispute = '', protocol = ''

    const originShopAux = __AUX.get('REVIEW_REFUND_SHOP');
    this.refundOriginTypeCodeList = originShopAux.map(item=>item.code);

		refund_type.forEach(item => {
			if (item.status) {
				if (item.ext_field1) {
					dispute = dispute + item.code + '|'
				}
				if (item.ext_field2) {
					protocol = protocol + item.code + '|'
				}
			}
		});
		dispute = dispute.substring(0, dispute.length - 1)
		protocol = protocol.substring(0, protocol.length - 1)
		this.refund_type_option['DISPUTE'] = new RegExp('^(' + dispute + ')$');
		this.refund_type_option['PROTOCOL'] = new RegExp('^(' + protocol + ')$');

		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus', () => {
			this.hasPlatformDiscountPrivilege = false
			this.setStatus()
			this.getUserPersonDiscountAttribute()
		})
	},
	mounted() {
		let self = this;

    // 初始化标签
    this.progressLabelOptions = __AUX.get('refund_progress_label')

		if (this.params.snapshotObj) {
			this.renderSnapshotDetail({ body: this.params.snapshotObj })
			return
		}

		this._selectMergeOrderInit(() => {
			if (this.params.id) {
				this.isMounted = true
				this.blockSameIdOpenTab(this.params.id)
				this.getBrowseList(this.params.id)
				this.getOrderDetail(null, this.params.id)
			} else {
				this._refresh(null, this.form)
			}
		});
		this.params.__close = this._refresh;
		// 如果有单据编码才进行校验
		if (this.params.id && this.params.bill_no) {
			this.isPushEvent()
			this.isMembers()
		}
		// 更新可编辑状态
		this.updateRefoundModeEditable()
	},
	beforeDestroy() {
		this.$root.eventHandle.$off('resetAllBtnStatus')
		delete this.params.__close;
	},
	computed: {
    // 店铺渠道是否京东自营 #CTAL-9476-京东自营只显示，不允许编辑
    isJDZY() {
      return this.shopInfo?.distribution_channel === 'JDZY';
    },
    // 当前业务代理=当前退款单的业务锁定人 或 当前退款单的 初审锁定人 或 当前退款单的复核锁定人
    isSomeLockPerson(){
      return this.isSameBusinessLockPerson || this.isSameFinanceLockPerson || this.isSamereviewLockPerson
    },
		getCancelDisabled() {
			let { business_status, refund_way } = this.form.billRefundApplication
			return business_status === 'FINISH' && !['ALIPAY', 'BANK'].includes(refund_way) && this.isBusiness
		},
		refoundModeShow() {
			return (this.jd_shops.indexOf(String(this.form.billRefundApplication.shop_id)) !== -1) ||
        (this.isJDZY && this.form?.billRefundApplication?.refund_mode);
		},
		returnsTotalObj() {
			let arr = ['act_price', 'refund_amount'],
				data = this.RETURNS_List;
			return this.calcBrowseTotal2(data, arr);
		},
		useForCorssDisabled() {
			let useForCorssDisabled = !(this.form.billRefundApplication.refund_way === 'CROSS_OVER_T' && /^(CREATE|REJECT)$/.test(this.form.billRefundApplication.business_status))
			this.rules.use_for_corss[0].required = !useForCorssDisabled
			return useForCorssDisabled
		},
		// 若退款单状态为待重转，则不可操作
		refundNomalDisabled() {
			return this.form.billRefundApplication.business_status == 'RE_TURN'
		},
		//驳回备注
		rejectRemarkStatus() {
			return !(
				(
					this.isSameFinanceLockPerson || this.form.billRefundApplication.refund_way === 'CARRY_OVER'
					&& /^(SUBMIT|CHECK|ING|FINISH)$/.test(this.form.billRefundApplication.business_status)
					&& this.form.billRefundApplication.if_finance_lock === 'Y'
				)
				|| (
					/^(CHECK|ING)$/.test(this.form.billRefundApplication.business_status)
					&& /^(TMALL|B2C|PDD)$/.test(this.form.billRefundApplication.original_type)
				)
				|| (
					/^(FINISH)$/.test(this.form.billRefundApplication.business_status)
					&& /^(OTHER)$/.test(this.form.billRefundApplication.original_type)
				)
			)
				|| !!this.params.snapshotObj
		},
		financeRemarkStatus() {
			return this.form.billRefundApplication.business_status === 'SUBMIT' ?
				!(
					this.form.billRefundApplication.business_status === 'SUBMIT'
					&& this.form.billRefundApplication.if_finance_lock === 'Y'
					&& this.form.billRefundApplication.finance_lock_person == this.getEmployeeInfo('id')
				) || !!this.params.snapshotObj
				: !(this.form.billRefundApplication.business_status === 'CHECK'
					&& this.form.billRefundApplication.if_review_lock === 'Y'
					&& this.form.billRefundApplication.review_lock_person == this.getEmployeeInfo('id'))
				|| !!this.params.snapshotObj
		},
		// 驳回按钮权限
		rejectDisabled() {
			/* 新增*/
			//a)退款单未推送下游单据，即未推送“收款退款单”或者“应收单”
			if (/^(|CHECK|ING)$/.test(this.form.billRefundApplication.business_status) && !this.isPush &&
				this.isMember) {
				return false
			} else if (!(
				//!（(代理人不等于初审锁定人||退款方式为换货结转）&&业务状态为已提交，已核对，退款中&&初审锁定)|| (代理人是否为初审锁定人&&业务状态为已提交，已核对，退款中&&退款渠道为天猫，拼多多，B2C))
				(
					(this.isSameFinanceLockPerson || this.form.billRefundApplication.refund_way === 'CARRY_OVER')
					&& /^(SUBMIT|CHECK|ING)$/.test(this.form.billRefundApplication.business_status)
					&& this.form.billRefundApplication.if_finance_lock === 'Y'
				)
				|| (
					this.isSameFinanceLockPerson
					&& /^(CHECK|ING)$/.test(this.form.billRefundApplication.business_status)
					&& /^(TMALL|B2C|PDD)$/.test(this.form.billRefundApplication.original_type)
				)
				//订单状态为待重转
			) || this.refundNomalDisabled
			) {
				return true
			} else {
				return false
			}
		},
		// 退款模式控制的字段有的变化情况
		refundModeChangeOption() {
			const res = [
				this.form.billRefundApplication.business_status,
				this.form.billRefundApplication.original_type,
				this.form.billRefundApplication.shop_id,
				this.form.billRefundApplication.is_import,
			].join(',')
			return res
		}
	},
	components: {
		couponTable,
		differenceAccount,
    inner,
	},
	watch: {
		refundModeChangeOption: function (newVal) {
			this.updateRefoundModeEditable()
		},
		// 退款模式切换清空明细
		'form.billRefundApplication.refund_mode':
		{
			handler: function (newVal, oldVal) {
				if (!!oldVal && newVal !== this.refund_mode_copy && this.refund_mode_copy !== "") {
					// 清空明细的同时将清空的内容转移到delApplicationItem中,然后将表格置空
					this.form.billRefundApplicationItem.forEach(obj => {
						if (obj.id) {
							delete obj.excessiveParam
							delete obj.refundReason
							this.form.delApplicationItem.push(obj)
						}
					})
					this.form.billRefundApplicationItem = []
				}
				this.refund_mode_copy = newVal
			},
		},
		'form.billRefundApplication.if_cross_refund': function (newVal) {
			if (!/^(CARRY_OVER||DISPUTE|PROTOCOL)$/.test(this.form.billRefundApplication.refund_way)) {
				return
			}
			if (newVal === 'Y') {
				//this.getCROSSOVERCanClick(() => {
				//	this.rules.merge_trade_no_target[0].required = true
				//	}, () => {
				// 校验没有特权时，是否跨单退款不能为Y
				//		this.form.billRefundApplication.if_cross_refund = 'N'
				//})
			} else {
				this.rules.merge_trade_no_target[0].required = false
			}
		},
		copyChooseTchIds: {
			handler: function (newVal, oldVal) {
				if (newVal.length > 0) {
					this.form.billRefundApplicationItem.forEach((item, idx) => {
						if (item.refund_type === 'PRICE_DIFF') {
							this.$refs.refundApplicationItemTable && this.$refs.refundApplicationItemTable.toggleRowSelection(this.form.billRefundApplicationItem[idx], true)
						}
					})
				} else {
					this.form.billRefundApplicationItem.forEach((item, idx) => {
						if (item.refund_type === 'PRICE_DIFF') {
							this.$refs.refundApplicationItemTable && this.$refs.refundApplicationItemTable.toggleRowSelection(this.form.billRefundApplicationItem[idx], false)
						}
					})
				}
			},
			deep: true
		}
	},
}
</script>
<style module>
.inner-tag {
  height: 300px !important;
}
.textarea-style :global(.el-form-item__content) {
	height: auto;
}

.mergered,
.mergered td {
	color: red !important;
}

.message-box :global(.el-message__group) p,
.message-box1 :global(.el-message__group) p {
	white-space: pre-wrap;
}

.message-box :global(.el-message__img) {
	height: 100%;
	top: 50%;
	transform: translateY(-50%);
	background-color: #ff4949;
}

.message-box :global(.el-message__group) {
	height: auto;
}

.width90 :global(.el-form-item__content .el-input) {
	width: 90%;
}

.width220 :global(.el-form-item__content .el-input) {
	width: 220px;
}

.pre-line {
	white-space: pre-line;
}

.row-height :global(.cell) {
	height: auto !important;
}

.batchInfo :global(.el-table__body-wrapper) {
	max-height: 400px;
}
</style>
<style type="text/css">
.el-notification__icon {
	font-size: 40px !important;
}

input[type="text"].proto-input {
	box-sizing: border-box;
	height: 20px;
	border-radius: 4px;
	border: 1px solid #c8cccf;
	color: #6a6f77;
	-webkit-appearance: none;
	-moz-appearance: none;
	display: inline-block;
	outline: 0;
	padding: 0 4px;
	text-decoration: none;
	width: 110px;
	margin: 0 4px;
}

.proto-textarea50 {
	width: 60%;
}

.proto-textarea30 {
	width: 30%;
}

input[type="text"].proto-input:focus {
	border: 1px solid #20a0ff;
}

.finance-input {
	width: 100%;
}

.finance-input .el-input {
	width: 100%;
}
</style>
