  // 新退款申请单详情-费用登记
  export default {
    data() {
      return {
        dataList: [],
        colData: [{
            label: '合并单号',
            prop: 'merge_trade_no',
            width: 210
          },
          {
            label: '批次单号',
            prop: 'batch_trade_no',
            width: 210
          },
          {
            label: '销售单号',
            prop: 'sys_trade_no',
            width: 210
          },
          {
            label: '商品编码',
            prop: 'materialNumber',
            width: 150
          },
          {
            label: '费用类型',
            prop: 'fee_register_type',
            width: 130,
            formatter: (val) => {
              return (this.feeRegisterTypeAux.filter(item => val == item.code)[0].name)
            }
          }, {
            label: '费用金额',
            prop: 'fee',
            width: 130
          },
          {
            label: '已退金额',
            prop: 'returnAmount',
            width: 130
          },
          {
            label: '原因描述',
            prop: 'reason',
            width: 210
          }, {
            label: '状态',
            prop: 'status',
            formatter: (val) => {
              return {
                'Y': '生效',
                'N': '失效'
              } [val] || val
            }
          }, {
            label: '创建人',
            prop: 'creatorName',
            width: 130
          }, {
            label: '创建时间',
            prop: 'create_time',
            format: 'dataFormat1',
            width: 130
          }, {
            label: '修改人',
            prop: 'modifierName',
            width: 130
          }, {
            label: '修改时间',
            prop: 'modify_time',
            format: 'dataFormat1',
            width: 130
          }, {
            label: '审核人',
            prop: 'auditorName',
            width: 130,
            formatter: (val, index, row) => {
              return row.auditor == -1 ? 'AUTO.AUTO' : val
            }
          }, {
            label: '审核时间',
            prop: 'audit_time',
            format: 'dataFormat1',
            width: 130
          }, {
            label: 'OA状态',
            prop: 'oa_status',
              formatter:val=>{
                  return{
                      'agree':'同意',
                      'refuse':'拒绝',
                      'terminate':'审批终止',
                  }[val]||val
              }
          }
        ],
        feeRegisterTypeAux: __AUX.get('fee_register_type'),
      }
    },
    methods: {
      getList() {
        let params = {
          mergeTradeId: this.form.billRefundApplication.merge_trade_id,
          mergeTradeNo: this.form.billRefundApplication.merge_trade_no,
          ifRefund:true
        }
        this.ajax.postStream('/order-web/api/feeregister/findFeeRegisterBySearchVO', params, res => {
          if (res.body.result) {
            this.dataList = res.body.content;
          } else {
            this.$message.error(res.body.msg)
          }
        }, err => {
          this.$message.error(err)
        })
      },
    },
    watch: {
      selectTab1(newVal, oldVal) {
        if (newVal == "feeRegister") {
          // this.getList()
        }
      }
    }
  }
