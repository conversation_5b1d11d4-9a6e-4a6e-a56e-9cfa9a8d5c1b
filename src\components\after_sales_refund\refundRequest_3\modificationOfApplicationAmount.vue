<template>
<div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
        <el-col :span='20'>
            <el-button type='primary' size='mini' @click="confirm">保存</el-button>
        </el-col>
    </el-row>
    <el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px" @submit.native.prevent=""> 
        <el-col :span="12" style="width: 100%">
            <el-form-item label="客户申请金额">
                <el-input v-model='form.applyAmount' size="mini"></el-input>
                <el-tooltip v-if='rules.applyAmount[0].isShow' class="item" effect="dark" :content="rules.applyAmount[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip>
            </el-form-item>
        </el-col>
    </el-form>
</div>
</template>

<script>
export default {
    props:["params"],
    data(){
        let self = this;
        return {
            form: {
                applyAmount: ''
            },
            rules: {
                applyAmount:[
                    {
						message: '请输入客户申请金额,可以保留两位小数',
						isShow: false,
						trigger: 'change',
                        required: true,
						validator(rule, value, callback) {
							if(/^([1-9]\d*|0)(\.\d{1,2})?$/.test(value+'')){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}
                ],
            },
            alerts: []
        }
    },
    mounted() {
        this.$parent.alerts.forEach(item => { 
            this.alerts.push(item.params.alertId)
        })
    },
    beforeDestroy(){
        if ((new Set(this.alerts).has(this.params.alertId))) {
            this.params._close()
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
        }
    },
    methods:{
        confirm(){
            this.$refs.form.validate((valid) => {
                if(!valid) return
                if (!(/^([1-9]\d*|0)(\.\d{1,2})?$/.test(this.form.applyAmount)) || this.form.applyAmount == 0) {
                    this.$message.error('请输入客户申请金额,大于0且可以保留两位小数')
                    return
                }
                let postData = {
                    id: this.params.afterOrderId,
                    refund_fee: this.form.applyAmount
                }
                this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/updateRefundFee', postData, (res)=> {
                    if (res.body.result) {
                        this.$message.success(res.body.msg)
                        this.params.callback()
                        this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
                    } else {
                        this.$message.error(res.body.msg)
                    }
                })
            })
        }
    }
}
</script>

<style scoped>

</style>
