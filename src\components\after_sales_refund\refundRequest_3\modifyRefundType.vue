<template>
	<div class="xpt-flex">
        <el-row	class='xpt-top'	:gutter='40'>
            <el-col :span='20'>
                <el-button type='primary' size='mini' @click="confirm">确认</el-button>
            </el-col>
        </el-row>
		<el-row class="mgb10" style="margin-top: 10px">
			<el-form label-position="right" :rules="rules" ref='importData' :model="importData">
				<el-col :span="20">
					<el-form-item label="单据编号" prop="orderList" class="jd_h" required>
						<el-input size='mini' type="textarea" placeholder="多单据编号请换行填写" :autosize="{ minRows: 15, maxRows: 15}" v-model="importData.orderList" style="width:180px;height:280px" resize="none"></el-input>
						<el-tooltip v-if='rules.orderList[0].isShow' class="item" effect="dark" :content="rules.orderList[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
                    <el-form-item label="退款方式" prop="refund_way" required>
						<el-select
								v-model="importData.refund_way"
								size="mini"
								placeholder="请选择"
							>
								<!-- 采用click.native方法而不用change事件是因为首次加载详情和触发change事件，正常是不应该触发的 -->
								<el-option
									v-for="(val, key) in pay_method_options"
									:key="key"
									:label="val"
									:value="key"
								>
								</el-option>
							</el-select>
						<el-tooltip v-if='rules.refund_way[0].isShow' class="item" effect="dark" :content="rules.refund_way[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-form>
		</el-row>
	</div>
</template>
<script>
	
	import validate from '@common/validate.js';
	export default {
		props:['params'],
		data(){
			var self = this;
			return {
				importData: {
                    orderList: null,
                    refund_way: null
                },
                rules: {
                    orderList:validate.isNotBlank({
						required:true,
						self:self
					}),
                    refund_way: validate.isNotBlank({
						required:true,
						self:self
					})
                },
                pay_method_options: {
                    PROTOCOL: '协议退款',
                    DISPUTE : '纠纷退款',
                },
			}
		},
        methods: {
            closeAlert () {
                this.params.callback()
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
            },
            confirm () {
                let self = this
                this.$refs.importData.validate((valid) => {
                    if (!valid) return
                    let orderList = self.importData.orderList.replace(/\s+/g, ',')
                    let orderIdList = orderList.split(',')
                    let fitterList = []
                    orderIdList.forEach(item => {
                        if (!!item.trim()) {
                            fitterList.push(item.trim())
                        }
                    })
                    if (orderIdList.length > 200 || orderIdList.length == 0) {
                        self.$message.error('请填写单号且单号最多不超过200个')
                        return
                    }
                    let data = {
                        billNos: fitterList,
                        refund_way: self.importData.refund_way
                    }
                    self.ajax.postStream("/afterSale-web/api/aftersale/bill/refundApp/batchUpdateRefundWay",data,d=>{
                        if(d.body.result) {
                            self.$message.success(d.body.msg)
                            self.params._close()
                            self.$root.eventHandle.$emit('removeAlert', self.params.alertId)
                        } else {
                            self.$message.error(d.body.msg)
                        }
                    }, err => {
                        self.$message.error(err)
                    })
                })
                
            }
        },
		mounted(){
            this.importData.orderList = this.params.orderList
		}
	}
</script>
<style lang="stylus" scoped>
.jd_h 
    height: 280px !important
    .el-form-item__content 
        height: 280px !important
</style>