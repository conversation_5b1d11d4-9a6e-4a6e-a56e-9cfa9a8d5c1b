// 退款申请单 -- btnJs
export default {
    data(){
        var self =this;
        return{
            modificationOfApplicationAmountLoading: false,
            hasModificationOfApplicationAmountPrivilege: false,
            hasPlatformDiscountPrivilege: false
        }
    },
    methods:{
        getUserPersonDiscountAttribute(){
            this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttribute', {
                personId: this.getEmployeeInfo('personId'),
                salesmanType: 'GLOBAL',
				attribute: 'UPDATE_REFUND_FEE',
			}, res => {
                this.hasModificationOfApplicationAmountPrivilege = res.body.result && res.body.content && res.body.content.attributeValueText === '是'
			}, err => {
                this.$message.error(err)
                this.hasModificationOfApplicationAmountPrivilege = false
            })
        },
        modificationOfApplicationAmount() {
            if (!(['CREATE','REJECT'].includes(this.form.billRefundApplication.business_status) )) {
                this.$message.error({
					message: `需满足以下条件才可修改客户申请金额：\n-当前新退款申请单的业务状态=创建|驳回`,
					customClass: this.$style["message-box"]
				});
				return;
            }
            if (!this.hasModificationOfApplicationAmountPrivilege) {
                this.$message.error('您无修改客户申请金额权限，请联系退款单的创建人或退款组人员进行修改')
                return
            }
            let self = this
            this.modificationOfApplicationAmountLoading = true
            let params = {
                afterOrderId: this.params.id,
                callback: data => {
                    self.modificationOfApplicationAmountLoading = false
                    self._refresh(true)
                },
                _close: () => {
                    self.modificationOfApplicationAmountLoading = false
                }
            }
            this.$root.eventHandle.$emit('alert', {
                params,
                component: ()=>import('@components/after_sales_refund/refundRequest_3/modificationOfApplicationAmount.vue'),
                style: 'width:450px;height:250px',
                title: '修改客户申请金额',
            })
        },
        platformDiscountPermission(callback){
            this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttribute', {
                personId: this.getEmployeeInfo('personId'),
                salesmanType: 'GLOBAL',
                attribute: 'IF_CHANGE_PLATFORM_DISCOUNT',
            }, res => {
                let hasPlatformDiscountPrivilege = res.body.result && res.body.content && res.body.content.attributeValueText === '是'
                callback && callback(hasPlatformDiscountPrivilege)
            }, err => {
                this.$message.error(err) 
                callback && callback(false)
            })
        },
        modifyPlatformDiscount(){
            if (!(/^(CREATE|REJECT)$/.test(this.form.billRefundApplication.business_status))) {
                this.$message.error('业务状态=创建、驳回才可修改平台优惠')
                return
            }
            if (!this.form.billRefundApplicationItem.length) {
                this.$message.error('请先添加退款明细')
                return
            }
            new Promise(resolve => {
				this.platformDiscountPermission(resolve)
			}).then((ifPermission) => {
                this.hasPlatformDiscountPrivilege = ifPermission
                if (!ifPermission) {
                    this.$message.error('您无修改平台优惠权限，请联系退款组人员进行修改')
                    return
                }
            })
        },
        forceDeletePlatformDiscount(){
            if (!(/^(SUBMIT)$/.test(this.form.billRefundApplication.business_status))) {
                this.$message.error('业务状态=提交才可操作')
                return
            }
            if(!this.selectRefoudDetailLists.length){
				this.$message.error('请先选择退款明细')
				return
			}
            if (!(this.selectRefoudDetailLists.every(item => item.refund_type == 'PLATFORM_DISCOUNT'))) {
                this.$message.error('只可删除平台优惠类型的退款明细')
				return
            }
            let self = this
            new Promise(resolve => {
				this.platformDiscountPermission(resolve)
			}).then((ifPermission) => {
                if (!ifPermission) {
                    this.$message.error('您无操作权限，请联系退款组人员进行操作')
                    return
                }
                let ids = this.selectRefoudDetailLists.reduce((arr, item)=>{
                    arr.push(item.id)
                    return arr
                }, [])
                this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/delete/platform/discount/item', {itemIdList: ids, applyId: this.form.billRefundApplication.id}, res => {
                    if (res.data.result) {
                        self._refresh(true)
                    } else {
                        self.$message.error(res.data.msg)
                    }
                },err => {
                    self.$message.error(err)
                });
                return
            })
        }
    }
}