<template>
	<div>
		<el-row>
			<el-form :model="form" class="mgt30" :rules="rules" ref="form">
				<el-form-item label="原因:" label-width="100px" prop="reason">
					<!-- <el-input v-model="form.reason" size="mini"></el-input> -->
					<xpt-select-aux v-model='form.reason' aux_name='qrtkyy' :disabledOption='disabledOption'></xpt-select-aux>
					
					<el-tooltip v-if='rules.reason[0].isShow' effect="dark" :content="rules.reason[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-form>
		</el-row>
		<el-row class="mgt30">
			<el-col :span="24" class="txt-r">
				<el-button class="mgr60" type="primary" size="mini" @click="confirm">确定</el-button> 
			</el-col>
		</el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js'
	export default {
		props:["params"],
		data(){
			var self = this;
			return {
				form:{
					reason:null
				},
				rules:{
					reason:validate.isNotBlank({required:true,self:self})
				},
				disabledOption:[]
			}
		},
		methods:{
			confirm(){
				let self = this;
				this.$refs.form.validate((valid) => {
					if(!valid) return;
					console.log(this.params)
					let ids = this.params.array.map(item=>item.after_refund_download_id);
					let data = {ids:ids,confirm_refund_reason:this.form.reason}



					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/confirmRefundReason', data, d =>{
			          let msg=d.body.msg
					 self.$message[d.body.result?'success':'error'](msg);
					 
					 if(d.body.result){
						 self.$root.eventHandle.$emit('removeAlert',this.params.alertId);
						 self.params.callback();
					 }
			         
			         
			        })



					/*this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/confirmRefundReason', data, d => {
						let msg='d.body.msg'
						self.$message[d.body.result?'success':'error'](msg);
					})*/
				})
			},

			//要禁掉确认退款原因的下拉选项
			getDisabledOption(){
				let data = __AUX.get('qxspyy').filter((item,index,array)=>{
					return item.tag == 'H';
				});
				this.disabledOption = data.map((item)=>{
					return {code:item.code}
				})
				
			}
		},
		
		mounted:function(){
			console.log('测试数据哦');
			this.getDisabledOption();
		}
	}
</script>
<style lang="stylus">
	.txt-r
		text-align:right
	.mgt30
		margin-top:30px
	.mgr60
		margin-right:30px
</style>