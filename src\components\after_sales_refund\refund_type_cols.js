// 退款申请单-一览明细
export default {
	data() {
		return {
			CANCEL_Cols: [{
				label: '商品编码',
				prop: 'materia_number',
				width: 150,
			},{
				label: '商品名称',
				prop: 'material_name',
			},{
				label: '标准售价',
				prop: 'stand_price',
			},{
				label: '实际售价',
				prop: 'act_price',
			},{
				label: '订单店铺',
				prop: 'shop_name',
			},{
				label: '平台优惠',
				prop: 'platform_discount',
			},{
				label: '收入店铺',
				prop: 'user_shop_name',
			},{
				label: '支付时间',
				prop: 'pay_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '规格描述',
				prop: 'material_specification',
				width: 180,
			},{
				label: '销售单号',
				prop: 'sys_trade_no',
				width: 190
			},{
				label: '取消原因',
				prop: 'cancel_reason',
				format: 'auxFormat',
				formatParams: 'qxspyy',
				width: 180,
			},{
				label: '淘宝单号',
				prop: 'tid',
				width: 150,
			},{
				label: '原始店铺',
				prop: 'original_shop_name',
			},{
				label: '业务员',
				prop: 'user_name',
			}/*,{
				label: '商品组别',
				prop: '??',
			}*/,{
				label: '数量',
				prop: 'number',
			},{
				label: '基本单位',
				prop: 'material_unit',
			},{
				label: '拍单时间',
				prop: 'created',
				format: 'dataFormat1',
				width: 150,
			}],
			RETURNS_Cols: [
			{
				label: '淘宝单号',
				prop: 'tid',
				width: 120,
			},
			{
				label: '销售单号',
				prop: 'sys_trade_no',
				width: 180,
			},
			{
				label: '退货跟踪单号',
				prop: 'bill_returns_no',
				width: 150,
			},{
				label: '客户',
				prop: 'custom_name',
			},{
				label: '问题商品编码',
				prop: 'question_goods_code',
				width: 110,
			},{
				label: '问题商品名称',
				prop: 'question_goods_name',
				width: 110,
			},{
				label: '问题描述',
				prop: 'question_description',
			},{
				label: '收入店铺',
				prop: 'user_shop_name',
			},{
				label: '平台优惠',
				prop: 'platform_discount',
			},{
				label: '买家昵称',
				prop: 'nick_name',
			},{
				label: '业务员',
				prop: 'staff_name',
			},{
				label: '业务员分组',
				prop: 'staff_group',
			},{
				label: '实际售价',
				prop: 'act_price',
			},{
				label: '退货描述',
				prop: 'reason',
			},{
				label: '备注信息',
				prop: 'remark',
			},{
				label: '单位',
				prop: 'units',
			},{
				label: '数量',
				prop: 'number',
			},{
				label: '批次单号',
				prop: 'batch_trade_no',
				width: 150,
			},{
				label: '已退款金额（已结转金额）',
				prop: 'refund_amount',
			}],
			RETURNS_Cols2: [{
				label: '问题商品编码',
				prop: 'question_goods_code',
				width: 110,
			},{
				label: '问题商品名称',
				prop: 'question_goods_name',
			},{
				label: '实际售价',
				prop: 'act_price',
			},{
				label: '已退款金额',
				prop: 'refund_amount',
			},{
				label: '问题描述',
				prop: 'question_description',
				width: 200,
			}/*,{
				label: '规格描述',
				prop: 'question_goods_description',
			}*/,{
				label: '退货方式',
				prop: 'returns_type',
				formatter: prop => ({ 
					'RETURN':'退货',
            		'CHANGE':'换货',
				}[prop] || prop),
			},{
				label: '数量',
				prop: 'number',
			},{
				label: '存货类别',
				prop: 'inventory_category',
			},{
				label: '批次单号',
				prop: 'batch_trade_no',
				width: 150,
			},{
				label: '创建人',
				prop: 'creator_name',
			},{
				label: '创建人分组',
				prop: 'creator_group_name',
			},{
				label: '创建日期',
				prop: 'create_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '源单单号',
				prop: 'bill_returns_no',
				width: 150,
			}],
			DELAY_Cols: [{
				label: '商品编码',
				prop: 'materia_number',
				width: 120,
			},{
				label: '商品名称',
				prop: 'material_name',
			},{
				label: '规格描述',
				prop: 'material_specification',
				width: 200,
			},{
				label: '承诺发货日期',
				prop: 'commit_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '实际发货时间',
				prop: 'out_stock_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '拍单时间',
				prop: 'created',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '行状态',
				prop: 'status_name',
			},{
				label: '标准售价',
				prop: 'stand_price',
			},{
				label: '实际售价',
				prop: 'act_price',
			},
			{
				label: '费用金额',
				prop: 'totalAmount',
			},
			{
				label: '已退金额',
				prop: 'usedAmount',
			},
			{
				label: '可用金额',
				prop: 'availableAmount',
			},
			{
				label: '审核时间',
				prop: 'audit_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '数量',
				prop: 'number',
			},{
				label: '基本单位',
				prop: 'material_unit',
			},{
				label: '销售单号',
				prop: 'sys_trade_no',
				width: 180,
			},{
				label: '行创建时间',
				prop: 'create_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '支付时间',
				prop: 'pay_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '行类型',
				prop: 'line_type_name',
			},{
				label: '行锁定状态',
				prop: 'lock_status_name',
			},{
				label: '行商品类型',
				prop: 'item_type_name',
			}],
			SINGLE_DISCOUNT_Cols: [{
				label: '商品编码',
				prop: 'materia_number',
				width: 120,
			},{
				label: '商品名称',
				prop: 'material_name',
			},{
				label: '规格描述',
				prop: 'material_specification',
				width: 200,
			},{
				label: '单品优惠退款金额',
				prop: 'sum_actual_amount',
				// bool: true,
				// isInput: true,
			},{
				label: '承诺发货日期',
				prop: 'commit_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '实际发货时间',
				prop: 'out_stock_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '拍单时间',
				prop: 'created',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '行状态',
				prop: 'status_name',
			},{
				label: '标准售价',
				prop: 'stand_price',
			},{
				label: '实际售价',
				prop: 'act_price',
			},{
				label: '审核时间',
				prop: 'audit_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '数量',
				prop: 'number',
			},{
				label: '基本单位',
				prop: 'material_unit',
			},{
				label: '销售单号',
				prop: 'sys_trade_no',
				width: 180,
			},{
				label: '行创建时间',
				prop: 'create_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '支付时间',
				prop: 'pay_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '行类型',
				prop: 'line_type_name',
			},{
				label: '行锁定状态',
				prop: 'lock_status_name',
			},{
				label: '行商品类型',
				prop: 'item_type_name',
			}],
			COMPENSATION_Cols: [{
				label: '商品编码',
				prop: 'material_code',
				width: 120,
			},{
				label: '商品名称',
				prop: 'material_name',
				width: 100,
			},{
				label: '售后单号',
				prop: 'after_order_no',
				width: 120,
			},{
				label: '规格描述',
				prop: 'material_specification',
				width: 200,
			},{
				label: '实际售价',
				prop: 'act_price',
			},{
				label: '问题描述',
				prop: 'question_description',
				width: 200,
			},{
				label: '销售数量',
				prop: 'count',
			},{
				label: '单位',
				prop: 'units',
			},{
				label: '店铺',
				prop: 'shop_name',
			},{
				label: '批次订单号',
				prop: 'batch_trade_no',
				width: 200,
			},{
				label: '销售订单号',
				prop: 'sys_trade_no',
				width: 200,
			},{
				label: '批号',
				prop: 'lot',
			},{
				label: '是否停产',
				prop: 'lot',
				formatter: prop => ({ 
					0: '否',
					1: '是',
				}[prop] || prop),
			},{
				label: '供应商',
				prop: 'suppiler_name',
			},{
				label: '采购入库日期',
				prop: 'instock_date',
				format: 'dataFormat1',
				width: 150,
			},{
				label: 'BOM版本',
				prop: 'bom_version',
				width: 100,
			},{
				label: '来源商品编码',
				prop: 'source_material_number',
				width: 130,
			},{
				label: '来源商品名称',
				prop: 'source_material_name',
			}],
			REFUNDCARRYFORWARD_Cols: [{
				label: '退货跟踪单号',
				prop: 'bill_returns_no',
				width: 150,
			},{
				label: '买家昵称',
				prop: 'buyer_name',
			},{
				label: '售后单号',
				prop: 'after_order_no',
				width: 150,
			},{
				label: '单据状态',
				prop: 'status',
			},{
				label: '创建人',
				prop: 'creator_name',
			},{
				label: '创建人分组',
				prop: 'creator_group_name',
			},{
				label: '申请退款金额',
				prop: 'refund_apply_amount',
			},{
				label: '业务状态',
				prop: 'business_status',
			},{
				label: '创建日期',
				prop: 'create_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '关闭状态',
				prop: 'close_status',
			},{
				label: '关闭时间',
				prop: 'close_time',
				format: 'dataFormat1',
				width: 150,
			},
			// {
			// 	label: '是否拉货',
			// 	prop: 'if_pickup',
			// },
			{
				label: '实际到货时间',
				prop: 'actual_reach_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '是否算退货率',
				prop: 'return_rate',
            }],
            INCOME_CARRY_Cols: [{
                label: '单据编号',
				prop: 'bill_code',
				width: 150,
			},{
				label: '合并订单',
				prop: 'merge_trade_no',
				width: 180,
			},{
				label: '买家昵称',
				prop: 'buyerNick',
			},{
				label: '收入结转类型',
                prop: 'income_carryover_type',
                width:120,
                format: 'auxFormat',
                formatParams: 'INCOME_CARRYOVER_TYPE',
			},{
				label: '结转金额',
				prop: 'carryover_sum',
			},{
				label: '已退结转金额',
				prop: 'act_sum',
			},{
				label: '退款中金额',
				prop: 'refundable_amount',
            }]
		}
	},
}