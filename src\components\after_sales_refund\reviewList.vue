<!-- 开启补充资料 -->
<template>
<div class='xpt-flex'>
	<el-row class='xpt-top' :gutter='40' v-if="params.isFromRefundRequest">
		<el-col :span='3'>
			<el-button type='primary' size='mini' @click="close()">确认</el-button>
		</el-col>
	</el-row>
	<xpt-list3 
		:data='list' 
		:btns='btns' 
		:colData='colData' 
		:selection="'checkbox'"
		@selection-change='reviewSelect'
		:isNeedClickEvent='true'
		:showHead="true"
		:ifClearSelect="true"
		ref="xptList"
	>
	<template slot='finance_remark' slot-scope='scope'>
		<el-input v-model="scope.row.finance_remark"  size='mini' style="width:100%;" ></el-input>
	</template>
	</xpt-list3>
</div>
</template>
<script>
import fn from '@common/Fn.js'

	export default {
		props:["params"],
		data(){
			var _this = this;
			return {
				
				list:[],
				selectLine:[],
				btns:[{
					txt:'确认',
					type:'info',
					click:()=>{
						_this.close();
					}
				}],
				colData:[
                    {
                        prop:'refund_type',
						label:'类型',
						formatter(val){
							return _this.refoudDetailList_type_options[val];
						},
                        width:100
					},
					// {
                    //     prop:'actual_amount',
                    //     label:'实退金额',
                    //     width:120
                    // },{
                    //     prop:'deduct_fee',
					// 	label:'扣费',
						
                    //     width:200
					// },
					{
                        prop:'finance_confirm',
						label:'是否财务确认',
						formatter(val){
							return val=='Y'?'是':'否';
						},
                        width:100
                    },{
                        slot:'finance_remark',
						label:'财务备注',
						// elInput:true,
						// bool: true,
						// disabled(row) {return false},
                    }
				],
				refoudDetailList_type_options: {
					CANCEL: '未发取消',
					OVERPAY: '多付',
					PREFERENTIAL: '未前置优惠',
					SINGLE_DISCOUNT: '单品优惠',
					DELAY: '服务折让',
					COMPENSATION: '销售折让',
					REPAIR: '维修费',
					RETURNS: '退货货款',
					CARRIAGE: '运费',
					THREE: '三包费',
					O2O_DIFF: 'O2O跨店铺差价',
					PRICE_DIFF: '差价',
					DISCOUNT: '外购折现',
					CROSS_OVER: '多付',
					OTHER_REFUND: '其它退款',
					PLATFORM_DISCOUNT: '平台优惠',
					OVER_FUND:'多付-货款',
					OVER_DISCOUNT:'多付-优惠前置',
				},
			}
		},
		methods:{
			// 关闭标签页
			closeTab(){
				this.$root.eventHandle.$emit('removeTab',this.params.tabName);
			},
			
			init(){
				let self = this ;
				let newList = [];
				self.params.list.forEach(item=>{
					let ids = item.item_ids.split(',').filter(Boolean);
					newList.push({
						finance_remark:item.finance_remark,
						refund_type:item.refund_type,
						finance_confirmer:item.finance_confirmer == 'Y'?'Y':'N',
						ids:ids,
						id:item.id
					})
				})
				self.list = newList;

				// list = [
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'CANCEL'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'OVERPAY'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'PREFERENTIAL'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'SINGLE_DISCOUNT'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'DELAY'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'COMPENSATION'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'REPAIR'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'RETURNS'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'CARRIAGE'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'THREE'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'O2O_DIFF'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'PRICE_DIFF'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'DISCOUNT'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'CROSS_OVER'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'OTHER_REFUND'},
				// 	{deduct_fee:0,finance_confirm:'N',ids:[],actual_amount:0,addflag:false,refund_type:'PLATFORM_DISCOUNT'},
				// ];

				// self.billRefundApplicationItem.forEach(item=>{
				// 	list.forEach(refundItem=>{
				// 		if(item.refund_type == refundItem.refund_type){
				// 			refundItem.actual_amount+=parseFloat(item.actual_amount)*100;
				// 			refundItem.deduct_fee+=parseFloat(item.deduct_fee)*100;
				// 			refundItem.business_confirm = 'N';
				// 			refundItem.finance_remark = '';
				// 			refundItem.addflag = true;
				// 			refundItem.ids.push(item.id)
				// 		}
				// 	})
				// })
				// let j = list.length;
				// while(j--){
				// 	// console.log(list[j])
				// 	if(!list[j].addflag){
				// 		list.splice(j,1);
				// 	}else if(list[j].finance_confirm != 'N'){
				// 		list.splice(j,1);
				// 	}else{
				// 		list[j].deduct_fee = list[j].deduct_fee/100;
				// 		list[j].actual_amount = list[j].actual_amount/100;
				// 	}
				// }
				// // for( j>0;j--){
				// // console.log(list[j])
				// // 	if(!list[j].addflag){
				// // 		list.splice(j,1);
				// // 	}
				// // }
				
				// // self.list = list;
				// self.getInfoRequestList(list)

			},
			getInfoRequestList (list){
				let self = this;
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/getInfoRequestList',  
				this.params.data.apply_id, res => {
					var data = res.body.content || []
					
					// self.InfoRequestList = data.content || []
					let newList =[],
					cancelList = [];

					data.forEach(item=>{
						if(item.info_status =='CANCEL' || item.finance_confirm=='Y'){
							// return false;
							cancelList.push(item.refund_type)
						}
						list.forEach(listItem=>{
							// 当已有类型为已作废，新增类型不加进开启列表里
							if(item.info_status !='CANCEL'&& item.finance_confirm!='Y'){
								if(item.refund_type != listItem.refund_type){
									newList.push(listItem);
								}else if(!listItem.addflag){
									newList.push(item);
								}
							}
							
						})
					});
					console.log(newList);

					cancelList.forEach(item=>{
						let j = newList.length;
						while(j--){
							if(newList[j].refund_type == item){
								newList.splice(j,1);
							}
						}
					})
					
					// 如果newList为空，则选择list
					if(!!newList.length){
						self.list = newList 
					}else{
						self.list = !!data.length?[] :list ;

					}
					// self.list = newList || list;
				})
			},
			reviewSelect(row){
				this.selectLine = row;
			},
			/*
				该组件作为弹窗时
			*/
			close(){
                let addList = [],flag = true;
                if(this.selectLine && this.selectLine.length == 0){
				   this.$message.error('请选择要提交的内容');
				   return false;
			   }
			   console.log(this.selectLine);
				this.selectLine.forEach(item=>{
					if(!item.finance_remark){
						flag = false;
					}
					addList.push({id:item.id,"refund_type":item.refund_type,ids:item.ids,apply_id:this.params.data.apply_id,"finance_remark":item.finance_remark})
				})
				if(!flag){
					this.$message.error('财务备注不能为空');
					
                }
				this.params.callback(addList);
				
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
        },
        created () {
			// this.search.shopId = this.params.shopId
        },
		mounted: function(){
			this.billRefundApplicationItem  = this.params.data.billRefundApplicationItem;

            this.init()
        }
	}
</script>
<style type="text/css" >
</style>
