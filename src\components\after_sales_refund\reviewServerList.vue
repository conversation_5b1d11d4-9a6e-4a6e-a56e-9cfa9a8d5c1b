<!-- 开启补充资料 -->
<template>
<div class='xpt-flex'>
	<el-row class='xpt-top' :gutter='40' v-if="params.isFromRefundRequest">
		<el-col :span='3'>
			<el-button type='primary' size='mini' @click="close()">确认</el-button>
		</el-col>
	</el-row>
	<xpt-list3 
		:data='list' 
		:btns='btns' 
		:colData='colData' 
		:selection="'checkbox'"
		@selection-change='reviewSelect'
		:isNeedClickEvent='true'
		:showHead="true"
		:ifClearSelect="true"
		ref="xptList"
	></xpt-list3>
</div>
</template>
<script>
import fn from '@common/Fn.js'

	export default {
		props:["params"],
		data(){
			var _this = this;
			return {
				
				list:[],
				selectLine:[],
				btns:[{
					txt:'确认',
					type:'info',
					click:()=>{
						_this.close();
					}
				}],
				colData:[
                    {
                        prop:'refund_type',
						label:'类型',
						width:200,
						formatter(val){
							return _this.refoudDetailList_type_options[val];
						},
					},
					// {
                    //     prop:'actual_amount',
                    //     label:'实退金额',
                    //     width:120
                    // },{
                    //     prop:'deduct_fee',
					// 	label:'扣费',
						
                    //     width:200
					// },
					{
                        prop:'business_confirm',
						label:'是否业务确认',
						formatter(val){
							return val=='N'?'否':'是';
						},
                        width:200
                    },{
                        prop:'business_remark',
						label:'业务备注',
						elInput:true,
						bool: true,
						disabled(row) {return false},
                        width:200
                    }
				],
				refoudDetailList_type_options: {
					CANCEL: '未发取消',
					OVERPAY: '多付',
					PREFERENTIAL: '未前置优惠',
					SINGLE_DISCOUNT: '单品优惠',
					DELAY: '服务折让',
					COMPENSATION: '销售折让',
					REPAIR: '维修费',
					RETURNS: '退货货款',
					CARRIAGE: '运费',
					THREE: '三包费',
					O2O_DIFF: 'O2O跨店铺差价',
					PRICE_DIFF: '差价',
					DISCOUNT: '外购折现',
					CROSS_OVER: '多付',
					OTHER_REFUND: '其它退款',
					PLATFORM_DISCOUNT: '平台优惠',
					OVER_FUND:'多付-货款',
					OVER_DISCOUNT:'多付-优惠前置',
				},
			}
		},
		methods:{
			// 关闭标签页
			closeTab(){
				this.$root.eventHandle.$emit('removeTab',this.params.tabName);
			},
			
			
			getInfoRequestList (){
				let self = this;
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/businessGetUpdateRequestList?permissionCode=BUSINESS_SUPPLY_REQUEST',  
				this.params.data.apply_id, res => {
					self.list= res.body.content || [];
					if(!res.body.result){
						self.$message.error(res.body.msg);
					}
					// self.list = data || [];
				})
			},
			reviewSelect(row){
				this.selectLine = row;
			},
			/*
				该组件作为弹窗时
			*/
			close(){
			   let list = [];
			   this.selectLine.forEach(item=>{
				   list.push({id:item.id,business_remark:item.business_remark})
			   })
			   if(!list.length){
				   this.$message.error('请选择要提交的内容');
				   return false;
			   }
                this.params.callback(list)
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
        },
        created () {
			// this.search.shopId = this.params.shopId
        },
		mounted: function(){
			this.InfoRequestList  = this.params.data.InfoRequestList;

            this.getInfoRequestList()
        }
	}
</script>
<style type="text/css" >
</style>
