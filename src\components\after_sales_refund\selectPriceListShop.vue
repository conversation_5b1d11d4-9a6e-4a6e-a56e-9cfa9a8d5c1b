<!-- 价目表-选择店铺 -->
<template>
<div class="xpt-flex">
	<el-row class='xpt-top'>
		<el-col :span='18' >
			<el-button type='info' size='mini' @click="submit" :disabled="selectIndex === undefined">确认</el-button>
		</el-col>
		<el-col :span='6' class='xpt-align__right'>
			<el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="shopSearch.key" :on-icon-click="searchShopList"></el-input>
		</el-col>
	</el-row>
	<div class='xpt-flex__bottom'>
		<el-table :data="shopList" border style="width: 100%">
			<el-table-column width="55" align='center'>
				<template slot-scope='scope'>
					<el-radio class='xpt-table__radio' :label="scope.$index" v-model="selectIndex"></el-radio>
				</template>
		   	</el-table-column>
			<el-table-column prop="shop_code" label="店铺编码"></el-table-column>
			<el-table-column prop="shop_name" label="店铺"></el-table-column>
			<el-table-column prop="shop_group" label="店铺分组"></el-table-column>
			<el-table-column prop="shop_type_name" label="店铺分类"></el-table-column>
			<el-table-column  label="所属公司" prop="belong_company"></el-table-column>
			<el-table-column  label="生效">
				<template slot-scope="scope">{{ scope.row.disabled_status == 1?'失效':'生效' }}</template>
			</el-table-column>
			<el-table-column prop="disable_time" label="失效时间" >
				<template slot-scope='scope'>{{ scope.row.disable_time|dataFormat1 }}</template>
			</el-table-column>
			<el-table-column prop="disable_person_name" label="失效人"></el-table-column>
		</el-table>
		<el-row class='xpt-pagation'>
		  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
			  	:current-page="shopSearch.page.pageNo" :page-sizes="[10, 20, 50, 100]" :page-size="shopSearch.page.length"
			  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
			</el-pagination>
		</el-row>
	</div>
</div>
</template>
<script>
export default {
	props: ['params'],
	data (){
		return {
			selectIndex: undefined,
			pageTotal: 0,
			shopList: [],
			shopSearch:{
                key:'',
				page:{
                    length: 20,
					pageNo: 1
				},
				price_list_id: this.params.price_list_id,
			},
		}
	},
	methods: {
		pageSizeChange(pageSize){
			this.shopSearch.page.length = pageSize
			this.searchShopList()
		},
		pageChange(page){
			this.shopSearch.page.pageNo = page
			this.searchShopList()
		},
		searchShopList (){
			this.ajax.postStream('/price-web/api/price/getPriceListShopInfo', this.shopSearch, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
					this.shopList = res.body.content.list || []
					this.pageTotal = res.body.content.count
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		submit (){
			console.log('this.shopList[this.selectIndex]123',this.shopList[this.selectIndex])
			this.params.callback(this.shopList[this.selectIndex])
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
	},
	mounted (){
		this.searchShopList()
	},
}
</script>