<!-- 退款申请单选择收款账号列表 -->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-col :span="24">
			<el-button
				type="primary"
				size="mini"
				@click="() => submit()"
				:disabled="!selectData"
			>确定</el-button>
		</el-col>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData='colData'
		selection="radio"
		:pageTotal='pageTotal'
		:showHead="false"
		@radio-change='s => selectData = s'
		@row-dblclick="submit"
		@page-size-change='pageChange'
		@current-page-change='currentPageChange' ref='xptList'>
	</xpt-list>
</div>
</template>
<script>
export default {
	props:['params'],
	data (){
		return {
			roleList:[],
			selectData: null,

			searchObj: {
				page_no: 1,
				page_size: 50,
				cust_id: this.params.cust_id, // 搜索条件
			},
			pageTotal:0,
			colData: [{
				label: '支付方式',
				prop: 'pay_channel',
				format:'payTypeFilter'
			},{
				label: '支付类型',
				prop: 'pay_method',
				formatter: prop => ({
					ON_LINE: '线上',
					OFF_LINE: '线下',
				}[prop] || prop),
			},{
				label: '支付账号',
				prop: 'pay_account',
			},{
				label: '开户行',
				prop: 'bank',
			},{
				label: '用户名',
				prop: 'bank_user_name',
			},{
				label: '收款账号',
				prop: 'receiver_account',
			}],
		}
	},
	methods: {
		searchFun(){
			this.ajax.postStream('/order-web/api/customer/payAccount/getCustomerPayAccountList', this.searchObj, res =>{
				this.roleList = res.body.content.list
				this.pageTotal = res.body.content.count
			})
		},
		pageChange(pageSize){
			this.searchObj.page_size = pageSize
			this.searchFun()
		},
		currentPageChange(page){
			this.searchObj.page_no = page
			this.searchFun()
		},
		submit (selectData){
			this.params.callback(selectData || this.selectData)
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
	},
	mounted (){
		this.searchFun()
	},
}
</script>