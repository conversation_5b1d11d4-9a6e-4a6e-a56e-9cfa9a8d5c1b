<!-- 申请退款单平台下载详情 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="18">
				<el-button type="primary" size="mini" @click="refreshFun">刷新</el-button>
				<el-button type="primary" size="mini" @click="listFun">列表</el-button>
				<el-button type="primary" size="mini" @click="invalidFun">作废</el-button>
				<el-button type="primary" size="mini" @click="modifyRefundReason">保存退款原因</el-button>
				<el-button type="primary" size="mini" @click="stopAGreturn">终止AG重转</el-button>

				<!-- <el-button type="primary" size="mini" @click="agRefundRetry" :loading="agRefundRetryLoading" :disabled="agRefundRetryDisabled">重试AG退款</el-button> -->
				<!-- <el-button type="primary" size="mini" @click="agRefundRetryClose" :loading="agRefundRetryCloseLoading"  :disabled="agRefundRetryCloseDisabled">终止重试AG退款</el-button> -->
			</el-col>
			<el-col :span="6" class="xpt-align__right">
				<el-button type="danger" size="mini" @click="backFun">退出</el-button>
			</el-col>
		</el-row>
		<el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="110px">
			<el-row class='xpt-flex__bottom'>
				<el-tabs v-model="selectTab"  @tab-click="tabChangeClick">
					<el-tab-pane label='基本信息' name='basicInformation' class='xpt-flex'>
						<el-row>
							<el-col :span="6">
								<el-form-item label="平台退款单号" >
									<el-input v-model="form.refund_id" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="平台退款状态" >
									<!-- <el-input v-model="form.status_string" size='mini' disabled></el-input> -->
										<xpt-select-aux v-model='form.status' aux_name='all_platform_refund_status' disabled></xpt-select-aux>
								</el-form-item>

								<el-form-item label="订单状态" >
									<el-input v-model="form.order_status_string" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="订单店铺" >
									<el-input v-model="form.seller_nick" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="平台单号" >
									<el-input v-model="form.tid" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="平台订单金额" >
									<el-input v-model="form.total_fee" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="申请退款时间" >
									<el-date-picker
									    v-model="form.goods_return_time"   type="datetime" size="mini" style="width:200px;" disabled>
									</el-date-picker>
								</el-form-item>

								<el-form-item label="买家昵称" >
									<el-input v-model="form.buyer_nick" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="代理人" >
									<el-input v-model="form.proxy_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="退款申请单号" style="position: relative;">
									<el-input  size='mini' disabled></el-input>
									<a href="javascript:;" @click="toRefundApply(form.refund_apply_no)" style="position: absolute;left: 12px;top:0;">{{ form.refund_apply_no }}</a>
								</el-form-item>
								<el-form-item label="自动反审状态" >
									<el-input :value="batchCancelStatusOptions[form.batch_cancel_status]||form.batch_cancel_status" size='mini' disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">

								<el-form-item label="合并订单号" >
									<el-input v-model="form.merge_no" size='mini' style="width:230px;" disabled></el-input>
								</el-form-item>

								<el-form-item label="退款阶段" >
									<el-input v-model="form.refund_phase_string" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="客户申请金额" >
									<el-input v-model="form.refund_fee" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="平台退款原因" style="height:60px;">
									<el-input type="textarea" v-model="form.reason" size='mini' disabled style="width:200px;"></el-input>
								</el-form-item>

								<el-form-item label="确认退款原因" >
									<xpt-select-aux v-model='form.confirm_refund_reason' aux_name='qrtkyy' :disabledOption='disabledOption'></xpt-select-aux>
								</el-form-item>


								<el-form-item label="AG退款类型" >
									<xpt-select-aux v-model='form.ag_refund_type' aux_name='ag_type' disabled></xpt-select-aux>
								</el-form-item>



								<el-form-item label="平台子单号" >
									<el-input v-model="form.oid" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="日期" >
									<el-date-picker
									    v-model="form.create_time"
									    type="datetime" size="mini" style="width:200px;" disabled>
									</el-date-picker>
								</el-form-item>

								<el-form-item label="支付宝账号" >
									<el-input  v-model="form.alipay_account" size='mini' style="width:230px;" disabled></el-input>
								</el-form-item>
								<el-form-item label="自动反审时间" >
									<el-date-picker
									    v-model="form.batch_cancel_time"
									    type="datetime" size="mini" style="width:200px;" disabled>
									</el-date-picker>
								</el-form-item>
							</el-col>
							<el-col :span="6">

								<el-form-item label="退款超时时间" >
									<el-date-picker
									    v-model="form.timeout"
									    type="datetime" size="mini" style="width:200px;" disabled>
									</el-date-picker>
								</el-form-item>

								<el-form-item label="业务状态" >
									<el-input v-model="form.after_status_string" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="下载来源" >
									<el-input :value="formatterFromType(form.come_from_type)" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="更新时间" >
									<el-date-picker
									    v-model="form.modified"
									    type="datetime" size="mini" style="width:200px;" disabled>
									</el-date-picker>
								</el-form-item>

								<el-form-item label="推荐处理人" >
									<el-input v-model="form.best_staff_name" size='mini' disabled></el-input>
								</el-form-item>
								<el-form-item label="推荐处理人分组" >
									<el-input v-model="form.best_staff_group_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="是否离职" >
									<el-input v-model="form.best_staff_status" size='mini' disabled ></el-input>
								</el-form-item>

								<el-form-item label="销售订单号" >
									<el-input v-model="form.sys_trade_no" size='mini' style="width:230px;" disabled ></el-input>
								</el-form-item>
								<el-form-item label="退款子账号" >
									<el-input v-model="form.sub_account" size='mini' style="width:230px;" disabled></el-input>
								</el-form-item>
								<!-- <el-form-item label="退款单号" style="position: relative;">
									<el-input  size='mini' disabled></el-input>
									<a href="javascript:;" @click="toRefundBill(form.refund_bill_no)" style="position: absolute;left: 12px;top:0;">{{ form.refund_bill_no }}</a>
								</el-form-item> -->
								<el-form-item label="AG退款状态" >
									<el-input :value="{Y: '成功',N: '等待',W: '进行中'}[form.ag_refunded]" size='mini' disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">


								<el-form-item label="业务员" >
									<el-input v-model="form.user_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="业务员分组" >
									<el-input v-model="form.user_group_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="原始店铺名称" >
									<el-input v-model="form.original_shop_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="客服店铺名称" >
									<el-input v-model="form.user_shop_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="经销商名称" >
									<el-input v-model="form.dealer_customer_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="小二介入" >
									<el-input :value="String(form.cs_status)|auxFormat('csStatus')" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="小二介入时间" >
									<el-date-picker
									    v-model="form.cs_interpose_time"
									    type="datetime" size="mini" style="width:200px;" disabled>
									</el-date-picker>
								</el-form-item>

								<el-form-item label="币别" >
									<el-input v-model="form.currency_string" size='mini' disabled ></el-input>
								</el-form-item>
								<el-form-item label="分类" >
									<el-input :value="category_options[form.category]" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="AG退款时间" >
									<el-date-picker
									    v-model="form.ag_refund_date"
									    type="datetime" size="mini" style="width:200px;" disabled>
									</el-date-picker>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row>
							<el-col :span="20">
								<el-form-item label="AG失败原因" style="height:102px;">
									<el-input v-model="form.ag_reason" type="textarea" disabled :rows="5" :autosize="{ minRows: 5, maxRows: 5 }" resize="none"></el-input>
								</el-form-item>
							</el-col>
						</el-row>
					</el-tab-pane>
					<el-tab-pane label='批次反审' name='batchAutoCancel' class='xpt-flex'>
						<xpt-headbar>
							<el-button type='success' size='mini' @click='getBatchAutoCancelList' slot='left' :loading="batchAutoCancelLoading">刷新</el-button>
						</xpt-headbar>
						<xpt-list
							:data="batchAutoCancelList"
							:colData="batchAutoCancelCols"
							:showHead="false"
							:pageTotal='batchAutoCancelPageTotal'
							selection=""
							@page-size-change='batchAutoCancelPageChange'
							@current-page-change='batchAutoCancelCurrentPageChange'
						></xpt-list>
					</el-tab-pane>
				</el-tabs>
			</el-row>
		</el-form>
	</div>
</template>
<script>
	import Fn from '@common/Fn.js'
	export default {
		props: ['params'],
		data() {
			return {
				selectTab: 'basicInformation',
				form:{
					after_refund_download_id: null,//流水号		Long
					 address: null,//地址		String
					 advance_status: null,//退款先行垫付	Byte
					 alipay_no: null,//支付宝交易号		String
					 buyer: null,//买家id		Long
					 buyer_nick: null,//买家昵称		String
					 company_name: null,//物流公司名称		String
					 cs_status: null,//客服接入状态		Byte
					 description: null,//退款说明		String
					 goods_return_time: null,//退款时间	Date
					 good_status: null,//货物状态		String
					 has_good_return: null,//是否退货		Byte
					 iid: null,//iid		String
					 modified: null,//更新时间		Date
					 num: null,//购买数量		Long
					 num_iid: null,//申请退款的商品数字编号		Long
					 oid: null,//子订单号		Long
					 order_status: null,//退款对应的订单交易状态		String
					 payment: null,//支付给卖家的金额		BigDecimal
					 price: null,//商品价格		BigDecimal
					 reason: null,//退款原因		String
					 refund_fee: null,//退还金额		BigDecimal
					 refund_id: null,//退款id		Long
					 timeout: null,//超时时间		Date
					 seller_nick: null,//卖家昵称		String
					 shipping_type: null,//物流方式		String
					 status: null,//退款状态		String
					 tid: null,//交易单号		Long
					 merge_no: null,//合并单号		String
					 title: null,//商品标题		String
					 total_fee: null,//交易总金额	BigDecimal
					 currency: null,//币别		String
					 remind_type: null,//提醒类型		Byte
					 exist_timeout: null,//是否存在超时		Byte
					 refund_phase: null,//退款阶段		String
					 refund_type: null,//退款方式		String
					 come_from_type: null,//下载来源		String
					 best_staff: null,//推荐处理人		Long
					 best_staff_name: null,//处理人姓名		String
					 after_status: null,//售后业务状态  		String
					 if_close: null,//是否关闭		Byte
					 modifier: null,//单据最后修改人		Long
					 modify_time: null,//单据最后修改时间		Date
					 confirm_refund_reason:null,//确认退款原因
					 ag_refund_type : null,//ag退款类型
					 sub_account:null //退款子账号
				},


				rules: {

				},
				category_options: {//分类
					STEP_ORDER: '特权定金',
					PRE_SALE: '预售订单',
					SUCCESSED: '退款成功',
					CLOSED: '退款关闭',
				},
				batchCancelStatusOptions: {
					'NO_NEED': '无需',
					'ING': '进行中',
					'SUCCESS': '成功',
					'FAIL': '失败'
				},
				//禁掉下拉框
				disabledOption:[],
				agRefundRetryLoading:false,
				agRefundRetryCloseLoading:false,
				agRefundRetryDisabled:false,
				agRefundRetryCloseDisabled:false,
				employeeGroupCodeList: [], //当前代理人业务员类型列表
				isRefundAuditor: false, //是否退款审核
				isRefundAssistant: false, //是否退款专员
				batchAutoCancelCols:[{
						label: '批次单号',
						prop: 'batch_trade_no',
						width: 200,
					},{
						label: '创建时间',
						prop: 'create_time',
						width: 140,
						format:"dataFormat1",
					},{
						label: '执行时间',
						prop: 'finish_time',
						width: 140,
						format:"dataFormat1",
					},{
						label: '执行类型',
						prop: 'operation_type',
						width: 110,
					},{
						label: '执行结果',
						prop: 'status',
						width: 110,
					},{
						label: '失败原因',
						prop: 'remark',
				}],
				batchAutoCancelList:[],
				batchAutoCancelPageTotal:0,
				batchAutoCancelLoading:false,
				batchAutoCancelPageNo:1,
				batchAutoCancelPageSize:50,
			}
		},
		methods: {
				tabChangeClick(){
					if(this.selectTab == 'batchAutoCancel'){
						this.getBatchAutoCancelList()//获取批次反审任务明细
					}
				},
				getBatchAutoCancelList(){
					let data = {
						refundId: this.form.refund_id,
						"page_no": this.batchAutoCancelPageNo,
        				"page_size": this.batchAutoCancelPageSize,
					};
					this.batchAutoCancelLoading=true;
					this.ajax.postStream('/afterSale-web/api/aftersale/refund/batchAutoCancel/getBatchAutoCancelVOList',data,(res => {
						if(res.body.result){
							this.$message.success(res.body.msg||'获取批次反审任务明细成功')
							this.batchAutoCancelList=res.body.content.list||[]
							this.batchAutoCancelPageTotal=res.body.content.count||0
						}else{
							this.$message.error(res.body.msg||'获取批次反审任务明细失败')
						}
						this.batchAutoCancelLoading=false;
					}))
				},
				batchAutoCancelPageChange(pageSize){
					this.batchAutoCancelPageSize = pageSize
					this.getBatchAutoCancelList();
				},
				batchAutoCancelCurrentPageChange(page){
					this.batchAutoCancelPageNo = page
					this.getBatchAutoCancelList();
				},
				// 终止AG重转
				stopAGreturn(){
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/stopAgRetry?permissionCode=AG_STOP_RETRY', {
						after_refund_download_id:this.params.after_refund_download_id
					}, res => {
								if(res.body.result){
									this.$message.success(`${res.body.msg}`)
								}else {
									this.$message.error(res.body.msg)
								}
							})
				},
		      // 下载来源字段过滤
		      formatterFromType(string){
		        console.log(string);
		        switch (string) {
		          case 'YJ':
		            return '云集'
		            break;
		          default:
		            return string
		            break;
		        }
		      },
			// 根据单据号跳转到申请单详情
			toRefundApply (refund_apply_no){
				var searchFunc = () => {
					this.toRefundApply.where[0].value = refund_apply_no
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/list', {
						"page_name": "aftersale_bill_refund_apply",
						"page_size": 50,
						"page_no": 1,
						"where": this.toRefundApply.where,
					}, res => {
						if(res.body.result){
							if(res.body.content.list && res.body.content.list[0]){
								this.$root.eventHandle.$emit('creatTab',{
									name: '退款申请单',
									params: { id: res.body.content.list[0].id },
									component:()=> import('@components/after_sales_refund/refundRequest_3')
								})
							}else {
								this.$message.error('找不到该退款申请单详情')
							}
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}

				if(this.toRefundApply.where){
					searchFunc()
				}else {
					this.ajax.postStream('/user-web/api/sql/listFields', { page: 'aftersale_bill_refund_apply' }, res => {
						if(res.body.result){
							res.body.content.fields.some(obj => {
								if(obj.comment === '单据编号'){
									this.toRefundApply.where = [{
										condition: 'AND',
										field: obj.field,
										listWhere: [],
										operator: '=',
										table: obj.table,
										value: '',
									}]
									searchFunc()
									return true
								}
							})
						}
					})
				}
			},
			// 根据单据号跳转到退款单详情
			toRefundBill (refund_bill_no){
				var searchFunc = () => {
					this.toRefundBill.where[0].value = refund_bill_no
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refund/queryRefundBillNewList?permissionCode=REFUND_ORDER_QUERY', {
						"page_name": "aftersale_bill_refund",
						"pageSize": 50,
						"pageNo": 1,
						"where": this.toRefundBill.where,
					}, res => {
						if(res.body.result){
							if(res.body.content.list && res.body.content.list[0]){
								this.$root.eventHandle.$emit('creatTab',{
									name: '退款单详情',
									params: { id: res.body.content.list[0].id },
									component:()=> import('@components/after_sales_refund/refundOrder_2')
								})
							}else {
								this.$message.error('找不到该退款单详情')
							}
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}

				if(this.toRefundBill.where){
					searchFunc()
				}else {
					this.ajax.postStream('/user-web/api/sql/listFields', { page: 'aftersale_bill_refund' }, res => {
						if(res.body.result){
							res.body.content.fields.some(obj => {
								if(obj.comment === '单据编号'){
									this.toRefundBill.where = [{
										condition: 'AND',
										field: obj.field,
										listWhere: [],
										operator: '=',
										table: obj.table,
										value: '',
									}]
									searchFunc()
									return true
								}
							})
						}
					})
				}
			},
			getData() {
				var data = {
					after_refund_download_id: this.params.after_refund_download_id
				};

				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/get?permissionCode=TAOBAO_REFUND_ORDER_QUERY',data,(res => {
					if(res.body.result){
						this.form = res.body.content;
						this.form.ag_reason = res.body.content.ag_auto_refund_log&&res.body.content.ag_auto_refund_log.length>0?(res.body.content.ag_auto_refund_log.reduce((a,b)=>{
							a = a + Fn.dateFormat(b.handle_time,'yyyy-MM-dd hh:mm:ss') + '  ' + b.ag_return_errer_info + '\n'
							return a
						}, '')):""
						this.$message.success('刷新成功')
						this.setAgPremisson();
					}
				}))
			},
			refreshFun() {
				this.getData();
			},
			listFun() {
				this.$root.eventHandle.$emit('creatTab',
            		{name:'退款申请单平台下载列表',params:{},component:()=>import('@components/after_sales_refund/taobaoDownloadList.vue')})
			},
			invalidFun() {
				var list_id = [];
				list_id[0] = this.form.after_refund_download_id;
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/cancel?permissionCode=TAOBAO_REFUND_ORDER_INVALID',{list_after_refund_download_id:list_id},res => {
					if(res.body.result) {
						this.$message.success('作废成功');
						this.getData();
					}else {
						this.$message.error(res.body.msg);
					}
				})
			},
			backFun() {
				this.$root.eventHandle.$emit('removeTab',this.params.tabName);

			},
			//要禁掉确认退款原因的下拉选项
			getDisabledOption(){
				let data = __AUX.get('qxspyy').filter((item,index,array)=>{
					return item.tag == 'H';
				});
				this.disabledOption = data.map((item)=>{
					return {code:item.code}
				})

			},
			//确认退款原因
			modifyRefundReason(){
				if(!this.form.confirm_refund_reason){
					this.$message.error('亲，请选择退款原因')
					return;
				}
				if(this.form.status!='SUCCESS'||this.form.status!='SUNING_REFUND_SUCCESS'||this.form.status!='REFUNDED'||this.form.status!='VIP_REFUND_FINISHED'){
					this.$message.error('亲，未退款成功哦')
					return;
				}
				if(this.form.ag_refund_type!='WF_REFUND'){
					this.$message.error('亲，不属于AG退款哦')
					return;
				}

				let self = this;
				let data = {ids:[this.form.after_refund_download_id],confirm_refund_reason:this.form.confirm_refund_reason}
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/confirmRefundReason', data, d =>{
		          let msg=d.body.msg
				 self.$message[d.body.result?'success':'error'](msg);
				})


			},
			//设置按钮是否禁用
			setButtonDisabled(){
				let currentAgent = this.getEmployeeInfo("fullName"); //当前代理人
				let isRefundAuditor = this.isRefundAuditor; //是否退款审核
				let isRefundAssistant = this.isRefundAssistant; //是否退款专员
				let isHandler = this.form.user_name == currentAgent; //是否当前受理人
				let agSuccess = this.form.ag_refunded=='Y'//Ag状态等于成功
				let agSuccessOrFail = ['W','Y'].includes(this.form.ag_refunded)//Ag状态等于成功进行中
				this.agRefundRetryDisabled=!(isRefundAuditor || isRefundAssistant || isHandler) || agSuccess
				this.agRefundRetryCloseDisabled=agSuccessOrFail || !isHandler
			},
			//重试AG退款
			agRefundRetry(){
				this.agRefundRetryLoading=true;
				let params={
					'after_refund_download_id':this.form.after_refund_download_id
				}
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/resetAgRefund',params,res=>{
					if(res.body.result){
						this.$message.success(res.body.msg||`平台退款单号${this.form.refund_id}已AG退款成功`)
					}else{
						this.$message.error(res.body.msg||'本次执行未产生AG成功退款单')
					}
					this.agRefundRetryLoading=false;
				},err=>{
					this.$message.error(err||'本次执行未产生AG成功退款单')
					this.agRefundRetryLoading=false;
				})
			},
			//终止重试AG退款
			agRefundRetryClose(){
				this.$confirm("确认终止重试AG退款？", "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
					.then(_ => {
						this.agRefundRetryCloseLoading=true;
						let params={
							'after_refund_download_id':this.form.after_refund_download_id
						}
						this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/buildApplication',params,res=>{
							if(res.body.result){
								this.$message.success(res.body.msg||`终止重试AG退款成功`)
							}else{
								this.$message.error(res.body.msg||'终止重试AG退款失败')
							}
							this.agRefundRetryCloseLoading=false;
						},err=>{
							this.$message.error(err||'终止重试AG退款失败')
							this.agRefundRetryCloseLoading=false;
						})
					})
					.catch(_ => {});
			},
			getEmployeeInfoGroupList() {
            //获取当前处理人的业务信息列表
				return new Promise((resolve, reject) => {
					this.ajax.postStream(
						"/user-web/api/userPerson/getUserPersonGroupList",
						{ personId: this.getEmployeeInfo("personId") },
						(res) => {
							if (res.body.result && res.body.content) {
								this.employeeGroupCodeList =
									res.body.content.list || [];
								resolve();
							} else {
								res.body.msg && this.$message.error(res.body.msg);
								resolve();
							}
						},
						(err) => {
							this.$message.error(err);
							reject();
						}
					);
				});
			},
			ifRefundDirector(){
				//过滤失效的业务员
				let nowTime = new Date().getTime();
				let enAbledEmployeeGroupCodeList = this.employeeGroupCodeList.filter(
					(item) => !item.disableTime || item.disableTime > nowTime
				);
				//业务员编码列表
				let codeArr = enAbledEmployeeGroupCodeList.map((item) => {
					return item.salesmanType;
				});
				console.log(codeArr);
				this.isRefundAuditor = codeArr.includes(
					"REFUND_AUDITOR"
				);
				this.isRefundAssistant = codeArr.includes(
					"REFUND_ASSISTANT"
				);
			},
			setAgPremisson(){
				this.getEmployeeInfoGroupList().then(() => {
					this.ifRefundDirector();
					this.setButtonDisabled();
				});
			}
		},
		mounted(){
			if(this.params.after_refund_download_id) {
				this.getData();
			}
			this.getDisabledOption();

			//监听切换业务代理事件
			this.$root.eventHandle.$on("resetAllBtnStatus", () => {
				this.setAgPremisson();
			});
		}
	}
</script>
