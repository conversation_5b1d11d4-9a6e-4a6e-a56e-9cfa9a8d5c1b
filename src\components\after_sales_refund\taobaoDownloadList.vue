<!--退款申请单淘宝下载列表-->
<template>
	<xpt-list-dynamic
		:data='roleList'
		:colData='colData'
		:searchPage="form.page_name"
		:pageTotal='pageTotal'
		:btns='btns'
		:isNeedClickEvent="true"
		:taggelClassName='tableRowClassName'
		:selectable="row => !row._total"
		@search-click='search'
		@selection-change='select'
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
		ref='xptList'
	>
	</xpt-list-dynamic>
</template>
<script>

	export default {
		data(){
			let self = this;
			return {
				roleList:[],
				// 已选行Id
				selectRole:[],
				selectIds: [], // 选择了的ID列表
				pageTotal:0,
				form: {
					page_name: 'aftersale_taobao_refund_download',
					page_size: 50,
					page_no: 1,
					where: [],
				},
				colData:[
					{
						label: '淘宝单号',
						prop: 'tid',
						redirectClick(d) {
							self.mod(d.after_refund_download_id)
						},
						width: 170,
					},{
						label: '订单店铺',
						prop: 'order_shop_name',
						width: '100',
					},{
						label: '昵称',
						prop: 'buyer_nick',
						width: '100',
					},{
						label: '日期',
						prop: 'create_time',
						format:"dataFormat1",
						width: 150,
					},{
						label: '退款单号',
						prop: 'refund_bill_no',
						width: 140,
						redirectClick(d) {
							self.toRefundBill(d.refund_bill_no)
						},
					},
					{
						label: '确认退款原因',
						prop: 'confirm_refund_reason',
						format:'qrtkyy'

					},
					{
						label: 'ag退款类型',
						prop: 'ag_refund_type',
						format:'ag_type'

					},

					{
						label: '退款申请单号',
						prop: 'refund_apply_no',
						width: 140,
						redirectClick(d) {
							self.toRefundApply(d.refund_apply_no)
						},
					},{
						label: '业务状态',
						prop: 'after_status',
						formatter: prop => ({
							HAVE_CHECKED: '已关联',
							NO_CHECKED: '未关联',
							FINANCE_TODO: '财务待办',
							REFUSE_REFUND: '作废',
						}[prop] || prop),
					},{
						label: '业务员',
						prop: 'user_name'
					},{
						label: '业务员分组',
						prop: 'user_group_name'
					},{
						label: '代理人',
						prop: 'proxy_name'
					},{
						label: '推荐处理人',
						prop: 'best_staff_name'
					},{
						label: '推荐处理人分组',
						prop: 'best_staff_group_name'
					},{
						label: '是否离职',
						prop: 'best_staff_status'
					},{
						label: '超时时间',
						prop: 'timeout',
						format:"dataFormat1",
						width: 170,
					},{
						label: '淘宝退款单号',
						prop: 'refund_id',
						width: 170,
					},{
						label: '淘宝退款金额',
						width: 100,
						prop: 'refund_fee'/*,
						format:'number'*/
					},{
						label: '退款方式',
						prop: 'refund_type_string'
					},{
						label: '淘宝退款状态',
						prop: 'status_string'
					},{
						label: '退款阶段',
						prop: 'refund_phase_string'
					},{
						label: '合并订单号',
						prop: 'merge_no',
						width:'200'
					},{
						label: '淘宝退款原因',
						prop: 'reason',
						width:'200'
					},{
						label: '退款单业务锁定时间',
						prop: 'after_status_time',
						format:"dataFormat1",
						width: 150,
					},{
						label: '自动反审状态',
						prop: 'batch_cancel_status',
						formatter: prop => ({
							'NO_NEED': '无需',
							'ING': '进行中',
							'SUCCESS': '成功',
							'FAIL':'失败'
						}[prop] || prop),
						width: 100,
					},{
						label: '自动反审时间',
						prop: 'batch_cancel_time',
						format:"dataFormat1",
						width: 150,
					},{
						label: 'AG退款状态',
						prop: 'ag_refunded',
						formatter: prop => ({
							Y: '成功',
							N: '等待',
							W: '进行中',
						}[prop] || prop),
					},{
						label: 'AG退款时间',
						prop: 'ag_refund_date',
						format:"dataFormat1",
						width: 150,
					},{
						label: '首次关联时间',
						prop: 'first_checked_time',
						format:"dataFormat1",
						width: 150,
					},{
						label: '最后修改时间',
						prop: 'modify_time',
						format:"dataFormat1",
						width: 150,
					},{
						label: '更新时间',
						prop: 'modified',
						format:"dataFormat1",
						width: 150,
					}
				],
				btns:[
					{
						type:'primary',
						txt:'刷新',
						click(){
							self.refresh();
						}
					},
					/*{
						type:'primary',
						txt:'新增退款申请单',
						disabled: true,
						click(){
							if(/^sale.linshimuye.com/.test(window.location.host)){
								var row = self.selectRole[0]
								,	taobaoList = []
								,	refundItemList = []

								if(
									self.selectRole.some(obj => {
										if(obj.ag_refunded === 'Y') return true

										taobaoList.push({ taobao_refund_no: obj.refund_id })
										refundItemList.push({
											deduct_fee: 0,
											apply_amount: Number(obj.refund_fee) || 0,
											refund_type: '',//这个也要传，不然退款类型选不了
											if_edit: 'Y',
										})
									})
								){
									self.$message.error('AG退款状态是成功的不能建退款申请单')
									return
								}

								self.$root.eventHandle.$emit('creatTab',{
									name: '退款申请单',
									params: {
										taobaoDownload: {
											refund_way: {
												'售中': 'PROTOCOL',
												'售后': 'DISPUTE',
											}[row.refund_phase_string],
											merge_trade_id: row.merge_trade_id,
											merge_trade_no: row.merge_no,

											shop_name: row.seller_nick,
											shop_id: row.order_shop_id,
											user_shop_name: row.user_shop_name,
											user_shop_id: row.user_shop_id,
											original_shop_name: row.original_shop_name,
											original_shop_id: row.original_shop_id,

											saleman_id: row.user_id,
											saleman: row.user_name,
											saleman_group: row.user_group_name,
											
											receiver_account: row.alipay_account,
											nick_name: row.buyer_nick,
											original_type: self.selectRole.length === 1 ? 'LS_SALE_ORDER' : 'MERGE_TRADE',
											original_no: self.selectRole.length === 1 ? row.sys_trade_no : row.merge_no,//多个勾选时，来源单据号取值为合并订单号

											taobaoList,
											refundItemList,
										},
									},
									component:()=> import('@components/after_sales_refund/refundRequest_2')
								})
							}else {
								if(self.selectRole.length !== 1){
									self.$message.error('只能选择一行')
								}else {
									if(self.selectRole.some(obj => obj.ag_refunded === 'Y')){
										self.$message.error('AG退款状态是成功的不能建退款申请单')
										return
									}

									self.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/buildBillRefundApplication', {
										after_refund_download_id: self.selectRole[0].after_refund_download_id,
									}, res => {
										if(res.body.result){
											self.$root.eventHandle.$emit('creatTab',{
												name: '新退款申请单',
												params: { id: res.body.content },
												component:()=> import('@components/after_sales_refund/refundRequest_3')
											})
										}else {
											self.$message.error(res.body.msg)
										}
									})
								}
							}
						}
					},*/
					{
						type:'primary',
						txt:'财务待办',
						disabled: true,
						click(){
							self.financeOrCancel('finance')
						}
					},
					{
						type:'danger',
						txt:'撤回待办',
						disabled: true,
						click(){
							self.financeOrCancel('cancelFinance')
						}
					},
					{
						type:'danger',
						txt:'作废',
						disabled: true,
						click(){
							self.invalidFun()
						}
					},
					{
						type:'primary',
						txt:'导出',
						disabled: true,
						click(){
							self.exportAftersaleTaobaoRefundDownload()
						}
					},
					{
						type:'primary',
						txt:'确认完成',
						click(){
							self.update()
						}
					},
					{
						type:'primary',
						txt:'批量修改退款原因',
						click(){
							self.batchModifyReason()
						}
					}
				]
			}
		},
		methods:{
			// 根据单据号跳转到申请单详情
			toRefundApply (refund_apply_no){
				var searchFunc = () => {
					this.toRefundApply.where[0].value = refund_apply_no
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/list', {
						"page_name": "aftersale_bill_refund_apply",
						"page_size": 50,
						"page_no": 1,
						"where": this.toRefundApply.where,
					}, res => {
						if(res.body.result){
							if(res.body.content.list && res.body.content.list[0]){
								this.$root.eventHandle.$emit('creatTab',{
									name: '退款申请单',
									params: { id: res.body.content.list[0].id },
									component:()=> import('@components/after_sales_refund/refundRequest_3')
								})
							}else {
								this.$message.error('找不到该退款申请单详情')
							}
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}

				if(this.toRefundApply.where){
					searchFunc()
				}else {
					this.ajax.postStream('/user-web/api/sql/listFields', { page: 'aftersale_bill_refund_apply' }, res => {
						if(res.body.result){
							res.body.content.fields.some(obj => {
								if(obj.comment === '单据编号'){
									this.toRefundApply.where = [{
										condition: 'AND',
										field: obj.field,
										listWhere: [],
										operator: '=',
										table: obj.table,
										value: '',
									}]
									searchFunc()
									return true
								}
							})
						}
					})
				}
			},
			// 根据单据号跳转到退款单详情
			toRefundBill (refund_bill_no){
				var searchFunc = () => {
					this.toRefundBill.where[0].value = refund_bill_no
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/list', {
						"page_name": "aftersale_bill_refund_application",
						"page_no": 1,
						"page_size": 50,
						"where": this.toRefundBill.where,
					}, res => {
						if(res.body.result){
							if(res.body.content.list && res.body.content.list[0]){
								this.$root.eventHandle.$emit('creatTab',{
									name: '新退款申请单',
									params: { id: res.body.content.list[0].id },
									component:()=> import('@components/after_sales_refund/refundRequest_3')
								})
							}else {
								this.$message.error('找不到该退款单详情')
							}
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}

				if(this.toRefundBill.where){
					searchFunc()
				}else {
					this.ajax.postStream('/user-web/api/sql/listFields', { page: 'aftersale_bill_refund_application' }, res => {
						if(res.body.result){
							res.body.content.fields.some(obj => {
								if(obj.comment === '单据编号'){
									this.toRefundBill.where = [{
										condition: 'AND',
										field: obj.field,
										listWhere: [],
										operator: '=',
										table: obj.table,
										value: '',
									}]
									searchFunc()
									return true
								}
							})
						}
					})
				}
			},
			// 批量确认完成
			update (){
				if(!this.selectRole.length){
					this.$message.error('请勾选至少一行')
					return
				}

				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/update', this.selectRole.map(obj => obj.after_refund_download_id), res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
						this.selectRole.forEach(obj => {
							obj.if_no_stock = 'N'
						})
					}else {
						this.$message.error(res.body.msg)
					}
				})
			},
			// if_no_stock是否存在未发货的批次单，Y整行标红显示
			tableRowClassName (row, index){
				if(row.if_no_stock === 'Y') {
					return this.$style['mergered']
				}
			},
			exportAftersaleTaobaoRefundDownload (){
				this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportAftersaleTaobaoRefundDownload', this.form, res => {
					this.$message({
						type: res.body.result ? 'success' : 'error',
						message: res.body.msg,
					})
				})
			},
			invalidFun() {
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/cancel?permissionCode=TAOBAO_REFUND_ORDER_INVALID',{list_after_refund_download_id: this.selectRole.map(obj => obj.after_refund_download_id),
				}, res => {
					if(res.body.result) {
						this.$message.success('作废成功');
						this.searchFun();
					}else {
						this.$message.error(res.body.msg);
					}
				})
			},
			financeOrCancel (apiName){
				var msg
				,	option = {
					finance: {
						key: 'NO_CHECKED',
						name: '未关联',
						setValAfterSuccess: 'FINANCE_TODO',
					},
					cancelFinance: {
						key: 'FINANCE_TODO',
						name: '财务待办',
						setValAfterSuccess: 'NO_CHECKED',
					},
				}[apiName]

				// if(this.selectRole.length === 0){
				// 	msg = '请勾选至少一行'
				// }else if (!this.selectRole.every(obj => obj.after_status === option.key)){
				// 	msg = '只能勾选' + option.name
				// }

				if(msg){
					this.$message.error(msg)
				}else {
					this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/' + apiName, this.selectRole.map(obj => obj.after_refund_download_id), res => {
						if(res.body.result){
							this.selectRole.forEach(obj => {
								obj.after_status = option.setValAfterSuccess
							})
							this.watchSelectRole()
							this.$message.success(res.body.msg)
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}
			},
			searchFun(resolve){
				var self = this;
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundDownload/list?permissionCode=TAOBAO_REFUND_ORDER_QUERY', this.form, res => {
					if(res.body.result){
						self.roleList = (res.body.content.list || []);
						self.roleList.length && self.roleList.push({
							_total: true,
							refund_fee: '合计' + self.roleList.reduce((a, b) => a + (b.refund_fee || 0), 0).toFixed(2),
						})
						self.pageTotal = res.body.content.count;
					}else{
						self.$message.error(res.body.msg);
					}
					resolve && resolve()
				}, () => {
					resolve && resolve()
				}, this.params.tabName)
			},
			// 搜索
			search(list, resolve) {
				this.form.where = list
				this.searchFun(resolve)
			},
			pageChange(pageSize){
				this.form.page_size = pageSize
				this.searchFun();
			},
			currentPageChange(page){
				this.form.page_no = page;
				this.searchFun();
			},
			select(s){
				this.selectRole = s;
				this.selectIds = [];
				s.forEach((v) => {
					this.selectIds.push(v.after_order_id);
				})
			},

			refresh(){
				this.searchFun();
			},

			mod(id) {
				this.$root.eventHandle.$emit('creatTab',
            		{name:'退款申请单淘宝下载查看',params:{after_refund_download_id:id},component:()=>import('@components/after_sales_refund/taobaoDownloadDetail.vue')})
			},
			watchSelectRole (){
				if(
					(this.selectRole.length === 1 ||
					this.selectRole.length > 1 && this.selectRole[0].merge_no && this.selectRole.every(obj => {
						return obj.refund_phase_string === this.selectRole[0].refund_phase_string && obj.merge_no === this.selectRole[0].merge_no
					}))
					&& this.selectRole.every(obj => obj.after_status !== 'REFUSE_REFUND')//作废了不能建申请单
				){
					// if(this.selectRole.some(obj => obj.status_string === '退款关闭')){
					// 	this.btns[1].disabled = true
					// }else {
					// 	this.btns[1].disabled = false
					// }
				}else {
					// this.btns[1].disabled = true
				}

				if(this.selectRole.length === 0 || this.selectRole.some(obj => obj.after_status === 'REFUSE_REFUND')){
					this.btns[3].disabled = true
				}else {
					this.btns[3].disabled = false
				}

				if(this.selectRole.length === 0){
					this.btns[1].disabled = this.btns[2].disabled = true
				}else if (this.selectRole.every(obj => obj.after_status === 'NO_CHECKED')){
					this.btns[1].disabled = false
					this.btns[2].disabled = true
				}else if (this.selectRole.every(obj => obj.after_status === 'FINANCE_TODO')){
					this.btns[1].disabled = true
					this.btns[2].disabled = false
				}else {
					this.btns[1].disabled = this.btns[2].disabled = true
				}
			},

			//批量修改退款原因
			batchModifyReason(){
				let array = this.selectRole,
				self = this,
				params = {
					array:array,
					callback(){
						self.searchFun();
					}
				}
				if(!array.length){
					this.$message.error('亲，请选择退款行哦');
					return;
				}

				let isPass = true,errorMsg='';
				for(var i=0;i<array.length;i++){

					if(array[i].status!='SUCCESS'){
						isPass = false,errorMsg=array[i].tid+'淘宝单号，未退款成功哦';
						break;
					}


					if(array[i].ag_refunded!='Y'){
						isPass = false,errorMsg=array[i].tid+'淘宝单号，AG退款未成功哦';
						break;
					}


					if(array[i].confirm_refund_reason){
						isPass = false,errorMsg=array[i].tid+'淘宝单号，退款原因已编辑，不需要重复操作';
						break;
					}


					if(array[i].ag_refund_type!='WF_REFUND'){
						isPass = false,errorMsg=array[i].tid+'淘宝单号，AG退款类型不为未发退款';
						break;
					}
					
				}

				if(!isPass){
					this.$message.error(errorMsg);
					return;
				}
				
				//弹出取消原因选项
				this.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/after_sales_refund/refund_reason'),
					style:'width:420px;height:200px',
					title:'取消原因'
				})

			}
			

		},
		props:['params'],
		mounted(){

		},
		watch: {
			selectRole (){
				this.watchSelectRole()
			},
		},
		destroyed(){
		}
	}
</script>
<style module>
.mergered, .mergered td{
	background-color: #f3e5dd !important;
}
</style>
