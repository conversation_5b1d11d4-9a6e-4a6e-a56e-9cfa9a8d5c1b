<!-- 直发订单物流信息异常报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='search' :model='search' label-position="right" label-width="120px">
        <el-col :span='6'>
         <el-form-item label="批次订单号：">
            <xpt-input v-model='search.batch_trade_no'  size='mini'></xpt-input>
          </el-form-item>
         <el-form-item label="淘宝单号：">
            <xpt-input v-model='search.tid'  size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="实际发货日志：">
            <el-date-picker
             size='mini'
              v-model="datePicker"
              type="daterange"
              @change="dateChange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          </el-col>
         
         
        <el-col :span='6'>
          <el-form-item label="业务员：">
            <xpt-input v-model='search.staff_name'  size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="分组：">
            <xpt-input v-model='search.staff_group_name'  size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="是否一致：">
             <el-select size="mini"  v-model="search.if_same" placeholder="请选择" >
              <el-option
                label="是"
                value="Y" key='1'>
              </el-option>
              <el-option
                label="否"
                value="N" key='2'>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
           <el-col :span='6'>
          <el-form-item label="供应商名称：">
            <xpt-input v-model='search.supplier_name'  size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="送货方式：">
             <xpt-select-aux v-model='search.deliver_method' aux_name='deliver_method_k3'></xpt-select-aux>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button type='success' size='mini' @click='preSearch' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_SUPPLY_AUTO_DELIVER")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <count-list 
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
      countUrl="/reports-web/api/order/supply/count"
      :showCount ='showCount'
    ></count-list>
  </div>
</template>
<script>
import countList from '@components/common/list-count'
  // import mixin from './mixin.js'
  export default {
    props: ['params'],
    // mixins: [mixin],
    components: {
        countList,
      },
    data() {
      let self = this
      return {
			count: 0,
      listCount:[],
			list: [],
        datePicker:[],
        showCount:false,
			  showSearch:false,
			  exportBtnStatus: false,
        queryBtnStatus:false,
        search: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          end_date: '',
          begin_date: '',
          if_same: '',
          batch_trade_no: '',
          tid: '',
          staff_name: '',
          staff_group_name	: '',
          deliver_method:""
        },

        cols: [
          {
            label: '批次订单号',
            prop: 'batch_trade_no',
          }, {
            label: '淘宝单号',
            prop: 'tid',
          }, 
          {
            label: '供应商名称',
            prop: 'supplier_name',
            width: 180,
          },{
            label: '前置承运商',
            prop: 'scm_logistics_company',
          }, {
            label: '前置承运商编码',
            width: 120,
            prop: 'scm_logistics_company_code'
          }, {
            label: '前置物流单号',
            width: 120,
            prop: 'scm_logistics_number'
          }, {
            label: '前置发货时间',
            prop: 'scm_delivery_time',
            format:'dataFormat1'
          }, {
            label: '实际物流单号',
            prop: 'ifs_logistics_number',
            width: 100,
          }, {
            label: '实际承运商',
            width: 100,
            prop: 'ifs_logistics_company'
          }, {
            label: '实际承运商编码',
            prop: 'ifs_logistics_company_code',
            width: 100
          }, {
            label: '实际发货时间',
            prop: 'ifs_delivery_time',
            width: 100,
            format:'dataFormat1'
          }, {
            label: '业务员',
            prop: 'staff_name',
          }, {
            label: '业务员分组',
            prop: 'staff_group_name',
          },
          {
            label: '送货方式',
            prop: 'deliver_method_name',
            width: 140,
          }, {
            label: '创建时间',
            prop: 'create_time',
            width: 130,
            format:'dataFormat1'
          }, {
            label: '修改时间',
            prop: 'update_time',
            width: 130,
            format:'dataFormat1'
          }, {
            label: '是否一致',
            prop: 'if_same',
            format:"yesOrNo"
          }
        ],
       
        
      }
    },
    methods: {
      // 导出报表
      showExportList (exportType){
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/after_sales_report/export'),
          style:'width:900px;height:600px',
          title: '报表导出列表',
          params: {
            query: {
              type: exportType,
            },
          },
        })
      },
      dateChange(value){
        console.log(this.datePicker);
        if(!value){
          this.search.begin_date = '';
          this.search.end_date =  '';
        }else{
          this.search.begin_date = this.datePicker[0].getTime();
          this.search.end_date =  this.datePicker[1].getTime();
        }
        
        
      },
      pageSizeChange(ps) {
			this.search.page_size = ps;
			this.queryData();
		},
		currentPageChange(page) {
			this.search.page_no = page;
			this.queryData();
		},
      //得到查询条件
      getPostData(){
        let self =this;
        // let end_date =self.getEndDate(data.year,data.month);
        let data = JSON.parse(JSON.stringify(this.search));
        var postData={
          // 页码
          page_no:data.page_no,
          // 页数
          page_size: data.page_size,
          end_date: data.end_date,
          begin_date: data.begin_date,
          // end_date: self.search.begin_date.length?self.search.begin_date[1]:'',
          // begin_date: self.search.begin_date.length?self.search.begin_date[0]:'',
          if_same: data.if_same,
          batch_trade_no: data.batch_trade_no,
          tid: data.tid,
          staff_name: data.staff_name,
          staff_group_name	: data.staff_group_name,
          supplier_name:data.supplier_name,
          deliver_method:data.deliver_method
        }
        return postData;
      },
      reset() {
        this.datePicker = [];
			for(let v in this.search) {
				if(!(v === 'page_size' || v === 'page_no')) {
					this.search[v] = '';
				}
			}
		},
    preSearch(callback){
      let self = this;
      new Promise((resolve,rej)=>{
            self.queryData(resolve);
          }).then(()=>{
            if(self.search.page_no != 1){
              self.count = 0;
            }
            self.showCount = false;
          })
    },
      queryData(resolve) {
        let self = this;
        this.$refs.search.validate((valid) => {
          if(valid) {
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/order/supply/get?permissionCode=SUPPLIER_DELIVERY_QUERY', this.getPostData(), res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                self.list = content.list || [];
                // self.count = content.count || 0;
                if(!self.showCount){
                  let total = self.search.page_size * self.search.page_no;
                  self.count = content.list.length == (self.search.page_size+1)? total+1:total;
                }
                // console.log(self.showCount,self.count);

                resolve&&resolve();
              }
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.search.validate((valid) => {
          if(valid) {

            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/order/supply/export?permissionCode=SUPPLIER_DELIVERY_REPORT', this.getPostData(), res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
    mounted(){
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
