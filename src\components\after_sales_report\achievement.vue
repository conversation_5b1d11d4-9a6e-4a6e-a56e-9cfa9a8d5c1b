<!-- 售后业绩报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='5'>
          <el-form-item label="开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="大分组：">
            <xpt-input v-model='query.big_group' icon='search' :on-icon-click='openBigGroup' size='mini' @change='bigGroupChange'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='5'>
          <el-form-item label="结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="分组：">
            <xpt-input v-model='query.staff_group' icon='search' :on-icon-click='openGroup' size='mini' @change='groupChange'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='5'>
          <el-form-item label="业务员：">
            <xpt-input v-model='query.staff' icon='search' :on-icon-click='openSatff' size='mini' @change='staffChange'></xpt-input>
          </el-form-item>
          <!-- <el-form-item label="汇总：" >
            <el-select size="mini"  v-model="query.summary" placeholder="请选择">
              <el-option
                v-for="(value,key) in summaryList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
            </el-select>
          </el-form-item> -->
           <el-form-item label="结算方式" >
            <el-select size="mini"  v-model="query.settle_method" placeholder="请选择" style="width:150px">
                <el-option value='WANGPAI_DEALER' label='经销网拍'></el-option>
                <el-option value='OTHER' label='其它'></el-option>
            </el-select>
          </el-form-item>
          
        </el-col>
        <el-col :span='5'>
          <el-form-item label='维度：'>
            <el-select v-model='query.day_or_month' size='mini'>
              <el-option value='1' label='天'></el-option>
              <el-option value='2' label='月'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="店铺：">
            <xpt-input
              v-model="query.shop_name"
              icon="search"
              :on-icon-click="selectShop"
              @change="shopChange"
              size="mini"
              readonly
              style="width:180px"
            ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="4" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_AFTER_SALE_FEE")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        initQuery: {},
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          staff: '',
          staff_id: '',
          staff_group: '',
          staff_group_id: '',
          big_group: '',
          big_group_id: '',
          summary:"",
          day_or_month: '1',
          settle_method:'',
          shop_id:'',
          shop_name:'',
        },

        //人员标识
        salesmanTypeList:__AUX.getCode('afterSaleType') || [],
        //salesmanTypeList:['CUSTOMER_SERVICE_EXECUTIVE'],

        cols: [
          {
            label: '分组',
            prop: 'handler_group_name',
          }, {
            label: '售后专员',
            prop: 'handler_name',
          }, {
            label: '签单业绩',
            prop: 'sign_bill_amount',
          }, {
            label: '完结订单业绩',
            prop: 'end_amount'
          }, {
            label: '退款业绩',
            prop: 'refund_amount',
          }, {
            label: '三包业绩',
            prop: 'three_packages_amount',
          }, {
            label: '(物流)运费业绩',
            width: 100,
            prop: 'delivery_point_amount'
          }, {
            label: '退货业绩',
            prop: 'actual_amount',
            width: 100
          },  {
            label: '结算方式',
            prop: 'settle_method',
            width: 100
          }, {
            label: '结余',
            prop: 'balance_amount',
          }, {
            label: '年',
            prop: 'years',
          }, {
            label: '月',
            prop: 'months',
          }, {
            label: '日',
            prop: 'days',
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {
      reset (){
        this.query = Object.assign({}, this.initQuery)
      },
      //得到查询条件
      getPostData(){
        let self =this;
        let data = JSON.parse(JSON.stringify(this.query));
        let begin_date = +new Date(data.begin_date);
        let end_date =+new Date(data.end_date);

        var postData={
          page_no: data.page_no,
          page_size: data.page_size,
          begin_date: begin_date,
          end_date: end_date,
          staff_name: data.staff,
          staff_id: data.staff_id,
          staff_group_name: data.staff_group,
          staff_group_id: data.staff_group_id,
          big_group_name: data.big_group,
          big_group_id: data.big_group_id,
          day_or_month: data.day_or_month,
          settle_method:data.settle_method,
          shop_id:data.shop_id
          // summaryConditions:data.summary
        }
        return postData;
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/listAfterSaleFee', this.getPostData(), res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {

            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportAfterSaleFee', this.getPostData(), res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      selectShop () {
            let self = this
            self.$root.eventHandle.$emit('alert', {
                component:()=>import('@components/shop/list'),
                style:'width:800px;height:500px',
                title:'选择店铺',
                params: {
                	selection: 'radio',
                    callback (data) {
	                        self.query.shop_name = data.shop_name;
	                        self.query.shop_id = data.shop_id;
                    }
                }
            })
		  },
      shopChange(){
        this.query.shop_name = "";
	      this.query.shop_id = "";
      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.initQuery = Object.assign({}, this.query)
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
