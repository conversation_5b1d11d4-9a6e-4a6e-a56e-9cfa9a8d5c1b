<!-- 销售计划-可售量详情 -->
<template>
	<div class='xpt-flex'>
		<div class='xpt-top'>
			<el-button type='primary' size='mini'  slot='left' :loading="refreshLoading" :disabled="refreshDidabled" @click="refreshEvent">刷新</el-button>
			<el-button type='primary' size='mini'  slot='left' :loading="refreshLoading" :disabled="refreshDidabled" @click="save">保存</el-button>
		</div>
		<div>
			<!-- <el-form :model='form'  ref='form' :rules="rules" label-width='120px'> -->
				<el-tabs v-model="topTabs">
					<el-tab-pane label="基本信息" name="first">
						<el-form :model='form'  ref='form' label-width='140px'>
						<el-row :gutter='40'>
							<el-col :span='6'>
                <el-form-item label='物料编码' >
									<el-input size='mini' v-model='form.fnumber' disabled></el-input>
								</el-form-item>
                <el-form-item label='物料名称' >
									<el-input size='mini' v-model='form.fname' disabled></el-input>
								</el-form-item>
                <el-form-item label='规格描述' >
									<el-input size='mini' v-model='form.spec' disabled></el-input>
								</el-form-item>
                <el-form-item label='标题' prop="title">
									<el-input size='mini' v-model='form.title' disabled></el-input>
									<!-- <el-tooltip v-if='rules.goods_name[0].isShow' class="item" effect="dark" :content="rules.goods_name[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip> -->
								</el-form-item>
                <el-form-item label='淘宝ID' prop="tb_product_id">
									<el-input size='mini' v-model='form.tb_product_id' disabled></el-input>
								</el-form-item>
                <el-form-item label='SKUID' prop="tb_sku_id">
									<el-input size='mini' v-model='form.tb_sku_id' disabled></el-input>
								</el-form-item>
                <el-form-item label='卖点描述'>
									<el-input size='mini' v-model='form.sell_point' disabled></el-input>
								</el-form-item>
								<!-- <el-form-item label='商品编码' prop="material_no">
                  <xpt-input v-model='form.material_no'  size='mini'  icon="search" :on-icon-click="goodsChoose"  :disabled="isEdit"></xpt-input>
									<el-tooltip v-if='rules.material_no[0].isShow' class="item" effect="dark" :content="rules.material_no[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item> -->
                <el-form-item label='淘宝网链接'>
									<el-input size='mini' v-model='form.tb_link' disabled></el-input>
                  <!-- <el-tooltip class="if_offline_and_online_linkage_tooltip" placement="right">
                      <p slot="content">
                          当商品和线上联动资料时，门店POS端的商品详情展示淘宝内容
                      </p>
                      <i class="el-icon-warning"></i>
                  </el-tooltip> -->
								</el-form-item>
							</el-col>
							<el-col :span='6'>
                <el-form-item label='SPU商品图'>
									<el-input size='mini' v-model='form.pic_url' disabled></el-input>
								</el-form-item>
                <el-form-item label='商品详情图'>
									<el-input size='mini' v-model='form.detail_pic' disabled></el-input>
								</el-form-item>
                <el-form-item label='线上产品参数'>
                  <el-button class="button-width-100" el-button type="primary" size="mini" :disabled="!form.product_params?.length" @click="onParamsShow('online_goods_param')">查看</el-button>
                </el-form-item>
							</el-col>
              <!-- <el-col :span="6">
                <el-form-item label='产品图' prop="product_pic">
                  <el-input size='mini' v-model='form.product_pic'></el-input>
                  <el-button v-if="isBusiness" type="primary" size="mini" @click="goToGoodsImagePreview('product_pic')">立即维护</el-button>
                </el-form-item>
								<el-form-item label='无标签产品图' prop="white_product_pic">
                  <el-input size='mini' v-model='form.white_product_pic'></el-input>
                  <el-button v-if="isBusiness" type="primary" size="mini" @click="goToGoodsImagePreview('white_product_pic')">立即维护</el-button>
								</el-form-item>
              </el-col> -->
						</el-row>
						</el-form>
					</el-tab-pane>
				</el-tabs>
			<!-- </el-form> -->
		</div>
		<div class='xpt-flex__bottom' v-fold>
			<el-tabs v-model="bottomTabs">
				<el-tab-pane label="关联商品" name="first">
					<xpt-list2
            ref="goodList"
						:data='mdMaterialRelationList'
						:colData='mdMaterialRelationCol'
						:orderNo="true"
						:showHead='true'
						:btns="mdMaterialRelationBtns"
						@selection-change='mdMaterialRadioChange'
					>
            <template #btns>
              <xpt-upload-v3
                slot="btns"
                uploadBtnText="导入"
                :uploadSize="20"
                acceptTypeStr=".xlsx,.xls"
                :dataObj="{}"
                :disabled="false"
                :ifMultiple="false"
                :showSuccessMsg="false"
                @uploadSuccess="uploadSuccess"
                btnType="success"
                style="display: inline-block; margin: 0px 10px"
              ></xpt-upload-v3>
              <el-button
                slot="btns"
                size="mini"
                type="warning"
                @click="showUploadGoodResult()"
                :disabled="false"
                >导入结果</el-button
              >
            </template>
            <template #xptSearchExtra>
              <el-row style="height: 100%;">
                <span class="no-wrap">物料编码：</span>
                <el-input size="mini" class="button-width-70" v-model="goodSearch.materialNumber"></el-input>
                <el-button type='success' size='mini' @click='searchGoodList' :disabled='isQueryingGood' :loading='isQueryingGood'>查询</el-button>
                <el-button type='primary' size='mini' @click='resetGoodSearch'>重置</el-button>
              </el-row>
            </template>
          </xpt-list2>
				</el-tab-pane>
				<el-tab-pane label="关联店铺" name="second">
          <xpt-list2
						ref='ShopList'
						:data='cloudShopV2List'
						:btns='shopRelationBtns'
						:colData='mdShopRelationCol'
						@selection-change='shopSelectionChange'
					>
            <template #btns>
              <xpt-upload-v3
                slot="btns"
                uploadBtnText="导入"
                :uploadSize="20"
                acceptTypeStr=".xlsx,.xls"
                :dataObj="{}"
                :disabled="false"
                :ifMultiple="false"
                :showSuccessMsg="false"
                @uploadSuccess="uploadSuccess"
                btnType="success"
                style="display: inline-block; margin: 0px 10px"
              ></xpt-upload-v3>
              <el-button
                slot="btns"
                size="mini"
                type="warning"
                @click="showUploadGoodResult()"
                :disabled="false"
                >导入结果</el-button
              >
            </template>
            <template #xptSearchExtra>
              <el-row style="height: 100%;">
                <el-col :span="6" class="flex">
                  <span class="no-wrap">店铺编码：</span>
                  <el-input size="mini" class="button-width-70" v-model="shopSearch.shopCode"></el-input>
                </el-col>
                <el-col :span="6" class="flex">
                  <span class="no-wrap">店铺名称：</span>
                  <el-input size="mini" class="button-width-70" v-model="shopSearch.shopName"></el-input>
                </el-col>
                <el-col :span="6" class="flex">
                  <span class="no-wrap">生效状态：</span>
                  <el-select placeholder="请选择" size='mini' class="button-width-70" v-model="shopSearch.ifEnable" clearable>
                    <el-option label="生效" value="Y"></el-option>
                    <el-option label="失效" value="N"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-button type='success' size='mini' @click='searchShopList' :disabled='isQueryingShop' :loading='isQueryingShop'>查询</el-button>
                  <el-button type='primary' size='mini' @click='resetShopSearch'>重置</el-button>
                </el-col>
              </el-row>
            </template>
          </xpt-list2>
				</el-tab-pane>
			</el-tabs>

		</div>

    <product-params-dialog ref="productParamsDialogRef"></product-params-dialog>
	</div>
</template>
<script>

import validate from '@common/validate.js';
import { cloneDeep } from 'lodash'
import productParamsDialog from './components/productParamsDialog.vue'
import shop from '@/components/discount/new_model/shop.js'

export default {
	props: ['params'],
	mixins: [shop/*,availableorder*/],
    components:{
        productParamsDialog
    },
	data() {
		let self = this;
		return {
			refreshDidabled:false,//刷新
			refreshLoading:false,//刷新
			topTabs: 'first',
			bottomTabs: 'first',
			selectmdMaterial:null,
			isEdit:false,//是否是新增
			mdMaterialRelationCount:0,
			mdMaterialRelationSearch:{
				page_size:self.pageSize,     //页数
				page_no:1   //页码
			},
			cloudShopV2List:[],
      allShopList: [],
			similarGoodsCount:0,
			backupData:{
				configurator_id:"",
				configurator_name:"",
			},
			// 基本信息、其它信息
			form: {
        material_code: '',
        material_name: '',
				material_spec: '',
				goods_name: '',
				tb_id: '',
				sku_id: '',
				sales_description: '',
				tb_link: '',
				spu_image: '',
				sku_detail_image: '',
			},
			mdMaterialRelationCol:[
				{
					label: '物料编码',
					width:150,
					prop: 'material_no'
				},
				{
					label: '物料名称',
					width:150,
					prop: 'material_name'
				},
        {
          label: '生效状态',
          width: 150,
          prop: 'if_enable',
          formatter: self.shopStatusFormat
        },
				{
					label: '规格',
					width:150,
					prop: 'material_spec'
				},
			],
      mdShopRelationCol:[
        {
					label: '店铺编码',
					prop: 'shop_code'
				},
        {
					label: '店铺',
					prop: 'shop_name'
				},
        {
					label: '店铺分组',
					prop: 'shop_group_name',
					// format: 'auxFormat',
          // formatParams: 'shopGroup',
				},
        {
					label: '店铺分类',
					prop: 'shop_type_name'
				},
				{
					label: '生效状态',
					prop: 'if_enable',
					formatter: self.shopStatusFormat
				},
        {
					label: '失效人',
					prop: 'disable_person_name'
				},
        {
					label: '失效时间',
					prop: 'disable_time',
					format: 'dataFormat1'
				},
        {
					label: '创建时间',
          prop: 'create_time',
					format: 'dataFormat1',
					width: 150,
				}
			],
			mdMaterialRelationList:[],
      allMdMaterialRelationList: [],
			operaterList:[],
			operaterCols: [
				{
					label: '操作日志ID',
					prop: 'log_operation_id'
				}, {
					label: '来源单据ID',
					prop: 'source_id'
				}, {
					label: '来源单据编码',
					prop: 'source_no'
				}, {
					label: '单据类型',
					prop: 'order_type',
					// width:300,
					// format: 'dataFormat1'
				}, {
					label: '业务操作',
					prop: 'operation_name',
					// formatter(val){
					// 	switch(val){
					// 		case "ADD" :return "新增";break;
					// 		case "SAVE" :return "保存";break;
					// 		case "IMPORT" :return "导入";break;
					// 		case "PUSH_SHOP" :return "下推门店";break;
					// 		case "CANCEL_PUSH_SHOP" :return "取消下推";break;
					// 		case "ON_OFF_LINE_SHOP" :return "上下架店铺";break;
					// 		case "NORMAL_PRODUCT" :return "修改生产状态为'生产'";break;
					// 		case "STOP_PRODUCT" :return "修改生产状态为'停产'";break;
					// 	}
					// }
				}, {
					label: '操作描述',
					prop: 'operation_desc'
				}, {
					label: '操作时间',
					prop: 'operation_time',
					format:'dataFormat1'

				}, {
					label: '操作人',
					prop: 'operator_name'
				}, {
					label: '备注',
					prop: 'remark'
				}
			],
			operaterCount:0,

      isBusiness:false,
      shopSearch:{
				shopCode: '',
				shopName: '',
				ifEnable: '',
			},
      goodSearch: {
        materialNumber: ''
      },
      shopSelects: [],
      isDelMaterialing: false,
      isDelShoping: false,
      isInvalidShoping: false,
      isQueryingShop: false,
      isQueryingGood: false,
      addGoodList: [],
      addShopList: []
		}
	},
	computed: {
		mdMaterialRelationBtns() {
      const self = this
      return [
        {
          type: 'primary',
          txt: '新增',
          click: self.addMdMaterialRelation,
        },
        {
          type: 'danger',
          txt: '失效',
          click: self.disableMdMaterialRelation
        },
      ]
    },
    shopRelationBtns() {
      const self = this
      return [
				{
					type: 'primary',
					txt: '新增',
					click: self.openShopDialog,
				},
        {
					type: 'danger',
					txt: '失效',
          loading: self.isInvalidShoping,
					click: self.invalidShopRelation,
				},
			]
    }
	},
	methods: {
		/**
		*初使化表头数据
		*/
		initData(data, type){
			let self =this;
      this.addGoodList = []
      this.addShopList = []
      if (!type) {
        self.form = {
          id: '',
          title: '',
          tb_product_id: '',
          tb_sku_id: '',
          spec: '',
          fnumber: '',
          sell_point: '',
          pic_url: '',
          detail_pic: '',
          fname: '',
          tb_link: '',
          ...data
        }
        this.queryShopData();
        this.getGoodList()
      } else if (type === 'mdMaterialRelationList') {
        this.getGoodList()
      } else if (type === 'cloudShopV2List') {
        this.queryShopData();
      }
		},

		/**
		*获取头部信息
		***/
		getDetail(params, type){
			if(!params.row) return;
			let url = `/order-web/api/externalMaterial/getShopProductMaterielDetail?id=${params.row.id}`;
			this.ajax.get(url,(res)=>{
				let d = res.body;
				this.$message({
					type:d.result?'success':'error',
					message:d.msg
				});


        console.log('d: ', d);
				if(!d.result || !d.content) return;
				this.initData(d.content, type);
			});
		},

    // 获取商品列表信息
    getGoodList() {
      let self =this;
      let url = `/order-web/api/externalMaterial/queryShopProductMaterielDetail`;
      let params = {
        id: self.form.id,
        // materialNumber: this.goodSearch.materialNumber
      }
      this.isQueryingGood = true
			this.ajax.postStream(url, params, (res)=>{
				let d = res.body;
        this.isQueryingGood = false
				if(!d.result || !d.content) return;
        self.mdMaterialRelationList = d.content || [];
        self.allMdMaterialRelationList = cloneDeep(d.content || [])
			});
    },

    // 根据搜索条件，过滤商品列表
    searchGoodList() {
      if (!this.goodSearch.materialNumber) {
        this.mdMaterialRelationList = cloneDeep(this.allMdMaterialRelationList || [])
        return
      }
      this.mdMaterialRelationList = this.allMdMaterialRelationList.filter(item => {
        return item.material_no === this.goodSearch.materialNumber
      })
    },

    // 重置商品列表查询参数
    resetGoodSearch() {
      this.goodSearch = {
        materialNumber: ''
      }
    },

		/**
		*刷新数据
		**/
		refreshEvent(bool){
      this.resetGoodSearch()
      this.resetShopSearch()
			this.getDetail(this.params);
      this.$refs.productParamsDialogRef.reset()
		},
		mdMaterialRadioChange(dataList){
			this.selectmdMaterial = dataList;
		},

    // 打开新增关联商品弹窗
		addMdMaterialRelation(){
			let params = {},
			self = this;
			let data = {
				material_code:"",
				material_name:'',
				row_type:'',
				specific_desc:''

			}
			// 选择商品

			params.callback = d => {
        console.log('d: ', d);
				let isPass = true;
        if (!d?.length) {
          this.$message.error('请选择商品');
          return;
        }
        const selectedIds = d.map(item => String(item.materialId))
				self.allMdMaterialRelationList.forEach(item=>{
					if(selectedIds.includes(String(item.material_id))){
						this.$message.error('该物料已存在');
						isPass =  false;
					}
				})
				if(d && isPass) {
					self.saveMdMaterialRelation(d,'RELATIONGOOD','mdMaterialRelationList');
				}
			}
			self.$root.eventHandle.$emit('alert', {
				params: params,
				component: () => import('@components/discount/selectGoodsList'),
				style: 'width:800px;height:500px',
				title: '商品列表'
			});
		},

    // 新增关联商品
		saveMdMaterialRelation(d){
      const addGoods = d.map(item => {
        return {
          material_id: item.materialId,
          material_no: item.materialNumber,
          material_name: item.materialName,
          if_enable: 'Y',
          material_spec: item.materialSpecification,
        }
      })
      this.addGoodList = this.addGoodList.concat(addGoods) || [];
      this.allMdMaterialRelationList = this.allMdMaterialRelationList.concat(addGoods) || [];
      this.mdMaterialRelationList = this.mdMaterialRelationList.concat(addGoods) || [];
      // this.savingMaterial = true;
      // let self = this;
      // let data = {
      //   relation_type: 'GOODS',
      //   goodsList: d.map(item => item.materialId),
      //   productList: [this.params.row.id],
      // }
      // let url = '/order-web/api/externalMaterial/batchRelateShopProductWithGoods';
      // self.ajax.postStream(url,data,(res)=>{
      //   this.savingMaterial = false;
      //   let returnData = res.body;
      //   self.$message({
      //     type:returnData.result?'success':'error',
      //     message:returnData.msg
      //   });
      //   if(returnData.result){
      //     this.getDetail(this.params, 'mdMaterialRelationList')
      //   }
      // });
		},

    // 失效关联商品，只改变表格显示数据，在表头点击保存时，才更新数据库数据
		disableMdMaterialRelation(){
			let self = this;
      // let url = '/order-web/api/externalMaterial/deleteShopProductMaterielById';
      if(!self.selectmdMaterial?.length){
        self.$message.error('请选择关联商品');
        return false;
      }
      this.$refs.goodList.clearSelection()
      const ids = self.selectmdMaterial.map(item => String(item.material_id))
      self.allMdMaterialRelationList.forEach(item=>{
        if(ids.includes(String(item.material_id))){
          item.if_enable = 'N'
        }
      })

      self.mdMaterialRelationList.forEach(item=>{
        if(ids.includes(String(item.material_id))){
          item.if_enable = 'N'
        }
      })
		},

    //导入结果
    showUploadGoodResult() {
      this.$root.eventHandle.$emit("alert", {
        style: "width:900px;height:600px",
        title: "导入结果",
        params: {
          url: "/order-web/api/externalMaterial/importProduct/list",
          data: {},
          showDownload: true,
        },
        component: () => import("@components/common/eximport"),
      });
    },

    //上传成功返回结果
    uploadSuccess(result) {
      if (result.length > 0 && !!result[0].path) {
        this.importGoodFileUrl(result[0].path);
      }
    },

    // 导入关联商品
    importGoodFileUrl(fileUrl) {
      let params = {
        productId: this.form.id,
        path: fileUrl,
      };
      this.ajax.postStream(
        "/order-web/api/externalMaterial/addImportProductListInfo",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },

    // 失效关联店铺
		invalidShopRelation(){
      let self = this;
      // let url = '/order-web/api/externalMaterial/disableShopProductMaterielDetail';
      if(!self.shopSelects?.length){
        self.$message.error('请选择关联店铺');
        return false;
      }

      this.$refs.ShopList.clearSelection()
      const ids = self.shopSelects.map(item => String(item.shop_id))
      self.allShopList.forEach(item=>{
        if(ids.includes(String(item.shop_id))){
          item.if_enable = 'N'
        }
      })

      self.cloudShopV2List.forEach(item=>{
        if(ids.includes(String(item.shop_id))){
          item.if_enable = 'N'
        }
      })

      // const params = {
      //   product_id: this.params.row.id,
      //   shopList: self.shopSelects.map(item => item.shop_id),
      // }
      // this.isInvalidShoping = true;
      // self.ajax.postStream(url,params,(res)=>{
      //   this.isInvalidShoping = false;
      //   let returnData = res.body;
      //   self.$message({
      //     type:returnData.result?'success':'error',
      //     message:returnData.msg
      //   });
      //   if(returnData.result){
      //     this.getDetail(this.params, 'cloudShopV2List')
      //   }
      // });
		},

    // 保存数据
		save(){
      let data = {
        productId: this.form.id,
        batchRelateGoods: {
          relation_type: "GOODS",
          goodsList: this.allMdMaterialRelationList.map(item => item.material_id),
          disableGoodsList: this.allMdMaterialRelationList.filter(item => item.if_enable === 'N').map(item => item.material_id),
          productList: [this.form.id]
        },
        batchRelateShop: {
          relation_type: "SHOP",
          shopList: this.allShopList.map(item => item.shop_id),
          disableShopList: this.allShopList.filter(item => item.if_enable === 'N').map(item => item.shop_id),
          productList: [this.form.id]
        }
      }
			let url = '/order-web/api/externalMaterial/updateProductMaterielDetail';
			// this.$refs.form.validate((valid) => {
			// 	if(!valid) return
      this.ajax.postStream(url,data,(res)=>{
        let d = res.body;

        this.$message({
          type:d.result?'success':'error',
          message:d.msg
        });
        if(!d.result || !d.content) return;
        // this.initData(d.content);
        this.getDetail(this.params)
      });
		},

		// 打开新增关联店铺弹窗
		openShopDialog(){
			let self = this;
			let params = {
				shop_status: 'OPEN',
				callback(d){
          let isPass = true;
          if (!d?.length) {
            this.$message.error('请选择商品');
            return;
          }
          const selectedIds = d.map(item => String(item.shop_id))
          console.log('d: ', d);
          self.allShopList.forEach(item=>{
            if(selectedIds.includes(String(item.shop_id))){
              self.$message.error('该店铺已存在');
              isPass =  false;
            }
          })
          if(d && isPass) {
            self.saveShopRelation(d);
          }
        }
			}
			self.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/shop/list'),
					style:'width:800px;height:500px',
					title:'店铺列表'
			})
		},

    // 保存关联店铺
    saveShopRelation(d) {
      console.log('saveShopRelation: ', d);
      const addShops = d.map(item => {
        return {
          shop_id: item.shop_id,
          shop_code: item.shop_code,
          shop_name: item.shop_name,
          shop_group_name: item.shop_group_name,
          shop_type_name: item.shop_type_name,
          if_enable: 'Y',
          disable_person_name: '',
          disable_time: '',
          create_time: '',
        }
      })
      this.addShopList = this.addShopList.concat(addShops) || [];
      this.allShopList = this.allShopList.concat(addShops) || [];
      this.cloudShopV2List = this.cloudShopV2List.concat(addShops) || [];

      // this.savingShop = true;
      // let self = this;
      // let data = {
      //   relation_type: 'SHOP',
      //   shopList: d.map(item => item.shop_id),
      //   productList: [this.params.row.id],
      // }
      // let url = '/order-web/api/externalMaterial/batchRelateShopProductWithShop';
      // self.ajax.postStream(url,data,(res)=>{
      //   this.savingShop = false;
      //   let returnData = res.body;
      //   self.$message({
      //     type:returnData.result?'success':'error',
      //     message:returnData.msg
      //   });
      //   if(returnData.result){
      //     this.getDetail(this.params, 'cloudShopV2List')
      //   }
      // });
    },

    // 打开商品参数弹窗
    onParamsShow(){
      let params = {
        data: this.form.product_params,
        callback:function(e){}
      };
      this.$refs.productParamsDialogRef.open(params)
    },

    shopStatusFormat(val){
      if(val == 'Y'||val==undefined) {
        return "生效";
      }else{
        return "失效";
      }
    },

    // 获取关联店铺列表
    queryShopData (){
      const params = {
        product_id: this.params.row.id,
        // shopCode: this.shopSearch.shopCode,
        // shopName: this.shopSearch.shopName,
        // ifEnable: this.shopSearch.ifEnable,
      }
      this.isQueryingShop = true;
			this.ajax.postStream('/order-web/api/externalMaterial/selectRelateShopProductWithShop', params, res => {
        console.log('res: ', res);
        this.isQueryingShop = false;
				if(res.body.result){
          this.cloudShopV2List = res.body.content || [];
          this.allShopList = cloneDeep(res.body.content || [])
				}else{
					this.$message.error(res.body.msg);
				}
			}, () => {
			})
		},

    searchShopList() {
      this.cloudShopV2List = this.allShopList.filter(item => {
        if (this.shopSearch.shopCode && item.shop_code !== this.shopSearch.shopCode) return false
        if (this.shopSearch.shopName && item.shop_name !== this.shopSearch.shopName) return false
        if (this.shopSearch.ifEnable && item.if_enable !== this.shopSearch.ifEnable) return false
        return true
      })
    },

    resetShopSearch() {
      for(let v in this.shopSearch) {
        this.shopSearch[v] = '';
      }
      this.searchShopList()
    },
		shopSelectionChange(selectArr) {
			this.shopSelects = selectArr
		},

    // // 判断当前页的数据是否有修改
		// isListPmsShopChange (resolve){
		// 	// if(this.compareData(this.listPmsActShopVO, this.markListPmsShopVOData)){
		// 	// 	resolve && resolve()
		// 	// 	this.$message.error('当前页有修改，请先保存！')
		// 	// 	throw('当前页有修改，请先保存！')
		// 	// }
		// },
	},
	mounted() {
		this.getDetail(this.params)
	}
}
</script>

<style scoped>
.if_offline_and_online_linkage_tooltip{
  color: #909399 !important;
}
.button-width-100{
    width: 180px;
}
.button-width-70{
  width: 150px;
  margin-right: 5px;
}
.flex {
  display: flex;
}
.no-wrap {
  white-space: nowrap;
}
</style>
