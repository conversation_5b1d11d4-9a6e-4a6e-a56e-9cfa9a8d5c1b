<template>
    <div class="xpt-flex">
        <el-row :gutter="40" class="xpt-top">
			<el-col :span='10'>
                <el-button type='warning' size='mini'  @click="comfirm">确认</el-button>
            </el-col>
        </el-row>
        <el-row class='xpt-flex__bottom' id='top'  :gutter='40'>
            <el-form label-position="right" label-width="120px" ref="form" :model="form" :rules="rules">
                <el-col :span="20">
                    <el-form-item label="页面名称" prop="page_name">
                        <el-input  v-model="form['page_name']"  size='mini' value="number"></el-input>
                        <el-tooltip v-if='rules.page_name[0].isShow' class="item" effect="dark" :content="rules.page_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="模板名称" prop="template_name">
                        <el-input  v-model="form['template_name']"  size='mini' value="number"></el-input>
                        <el-tooltip v-if='rules.template_name[0].isShow' class="item" effect="dark" :content="rules.template_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark" style="height:48px">
                        <el-input type="textarea" v-model="form.remark" :maxlength="500"  :autosize="{ minRows: 2, maxRows: 2}" size='mini' style="width:100%; min-width:240px;min-height: 48px;"></el-input>
                    </el-form-item>
                    <el-form-item label="上传模板" prop="url">
                        <p style="width: 70px;display: contents;">
                            <xpt-upload-v3
                                slot="btns"
                                uploadBtnText="选择文件"
                                :uploadSize="20"
                                acceptTypeStr=".xlsx,.xls,.zip"
                                :dataObj="uploadDataObj"
                                :disabled="false"
                                :ifMultiple="false"
                                :showSuccessMsg="false"
                                @uploadSuccess="uploadSuccess"
                                btnType="success"
                                style="display: inline-block; margin: 0px 10px"
                            ></xpt-upload-v3>
                        </p>
                        <el-tooltip v-if='rules.url[0].isShow' class="item" effect="dark" :content="rules.url[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="" prop="">
                        <p class='oversize_p'>{{form.url}}</p>
                    </el-form-item>
                </el-col>
            </el-form>
        </el-row>
    </div>
</template>
<script>
import VL from '@common/validate.js'
export default {
    props: ['params'],
    data() {
        let self = this;
        return {
            form: {
                page_name: '',
                template_name: '',
                url: '',
                remark: ''
            },
            rules: {
                ...[
                    {'page_name':'页面名称'},
                    {'template_name':'模板名称'},
                    {'url':'上传模板'}
                ].reduce((a,b) => {
                    var key = Object.keys(b)[0]
					a[key] = VL.isNotBlank({
						self: this,
						msg: '请填写' + b[key],
					})
					return a
                },{})
            },
            uploadDataObj: {
                parent_name: self.page_name,
                parent_no: '', //主要通过该参数获取附件列表
                child_name: null,
                child_no: null,
                content: {},
            },
        }
    },
    mounted(){
        this.initData()
    },
    computed: {
        ifAdd () {
            return this.params.row && this.params.row.template_id
        }
    },
    methods: {
        initData() {
            !this.ifAdd ? '' : this.form.template_id = this.params.row.template_id
            this.form.page_name = this.params.row.page_name
            this.form.template_name = this.params.row.template_name
            this.form.remark = this.params.row.remark
            this.form.url = this.params.row.url
        },
        uploadSuccess(res) {
            if (res && res.length > 0) {
                this.form.url = res[0].path
            } else {
                this.form.url = ''
            }
            
        },
        comfirm () {
            let self = this
            this.$refs.form.validate((valid) => {
                if(!valid) return
                let url = ''
                url = !this.ifAdd ? '/reports-web/api/template/insert' : '/reports-web/api/template/update'
                this.ajax.postStream(url, JSON.parse(JSON.stringify(this.form)),function(response){
                    if(response.body.result){
                        self.$message.success(response.body.msg);
                        self.params.callback()
                        self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
                    }else{
                        self.$message.error(response.body.msg || '');
                    }
                }, err => {
                    self.$message.error(err);
                });
            })
        }
    }
}
</script>
<style>
    .oversize_p {
        height: 28px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 240px;
    }
</style>