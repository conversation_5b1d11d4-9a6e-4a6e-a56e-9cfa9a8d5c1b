<!-- 采购经销售后报表  -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='10' class='xpt-top'>
		<el-form :rules='rules' ref='$searchObj' :model='form' label-position="right" label-width="120px">
			<el-col :span='6'>
				<el-form-item label="开始日期：" prop="begin_date">
					<el-date-picker v-model="form.begin_date" type="date" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
		              <i class='el-icon-warning'></i>
		            </el-tooltip>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="结束日期：">
					<el-date-picker v-model="form.end_date" type="date" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="经销商名称：">
					<el-input
						v-if="dealerCustomerDisabled"
						v-model="form.dealer_customer_name"
						size='mini'
						disabled
					></el-input>
					<el-input
						v-else
						v-model="form.dealer_customer_name"
						size='mini'
						icon="search"
						readonly
						:on-icon-click="getDealerCustomerList"
					></el-input>
				</el-form-item>
			</el-col>
			<el-col :span="6" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='searchFun'>查询</el-button>
				<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>

	<el-tabs v-model="selectTab2" class="xpt-flex__bottom"  @tab-click="selectTab2Click">
		<el-tab-pane label="采购订单退货报表" name="pageReturnofpurchaseorder" class="xpt-flex">
			<xpt-list
				:data="tabObjData['pageReturnofpurchaseorder'].list"
				:colData="tabObjData['pageReturnofpurchaseorder'].col"
				:pageTotal="tabObjData['pageReturnofpurchaseorder'].pageTotal"
		        :showHead="false"
		        selection=''
				@page-size-change='pageChange'
				@current-page-change='currentPageChange'
			></xpt-list>
		</el-tab-pane>
		<el-tab-pane label="采购订单补件成本报表" name="pagePurchasingOrderSupplementCost" class="xpt-flex">
			<xpt-list
				:data="tabObjData['pagePurchasingOrderSupplementCost'].list"
				:colData="tabObjData['pagePurchasingOrderSupplementCost'].col"
				:pageTotal="tabObjData['pagePurchasingOrderSupplementCost'].pageTotal"
		        :showHead="false"
		        selection=''
				@page-size-change='pageChange'
				@current-page-change='currentPageChange'
			></xpt-list>
		</el-tab-pane>
		<el-tab-pane label="采购订单运费报表" name="pagePurchasingOrderFreight" class="xpt-flex">
			<xpt-list
				:data="tabObjData['pagePurchasingOrderFreight'].list"
				:colData="tabObjData['pagePurchasingOrderFreight'].col"
				:pageTotal="tabObjData['pagePurchasingOrderFreight'].pageTotal"
		        :showHead="false"
		        selection=''
				@page-size-change='pageChange'
				@current-page-change='currentPageChange'
			></xpt-list>
		</el-tab-pane>
		<el-tab-pane label="采购退款成本报表" name="pagePurchasingrefundcost" class="xpt-flex">
			<xpt-list
				:data="tabObjData['pagePurchasingrefundcost'].list"
				:colData="tabObjData['pagePurchasingrefundcost'].col"
				:pageTotal="tabObjData['pagePurchasingrefundcost'].pageTotal"
		        :showHead="false"
		        selection=''
				@page-size-change='pageChange'
				@current-page-change='currentPageChange'
			></xpt-list>
		</el-tab-pane>
		<el-tab-pane label="采购服务成本报表" name="pagePurchasingServiceCost" class="xpt-flex">
			<xpt-list
				:data="tabObjData['pagePurchasingServiceCost'].list"
				:colData="tabObjData['pagePurchasingServiceCost'].col"
				:pageTotal="tabObjData['pagePurchasingServiceCost'].pageTotal"
		        :showHead="false"
		        selection=''
				@page-size-change='pageChange'
				@current-page-change='currentPageChange'
			></xpt-list>
		</el-tab-pane>
	</el-tabs>
</div>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
	props:['params'],
	data (){
		var self = this

		return {
			form: {
				// page_name: 'aftersale_bill_refund_application',
				page_size: 50,
				page_no: 1,
				begin_date: '',
				end_date: '',
				dealer_customer_name: '',
				dealer_customer_id: null,
			},
			dealerCustomerDisabled: true,
			tabObjData: {
				pageReturnofpurchaseorder: {//采购订单退货报表
					page_size: 50,
					page_no: 1,
					pageTotal: 0,
					list: [],
					col: [{
						label: '经销商名称',
						prop: 'dealer_name',
					}, {
						label: '经销店铺',
						prop: 'dealer_shop_name',
					}, {
						label: '业务日期',
						prop: 'business_date',
						format: 'dataFormat1',
						width: 150,
					}, {
						label: '退货跟踪单号',
						prop: 'bill_returns_no',
					}, {
						label: '返货类型',
						prop: 'return_type',
						formatter: prop => ({
							DEALER: '经销退货',
							DEALER_PICK: '经销自提',
						}[prop] || prop),
					}, {
						label: '买家昵称',
						prop: 'custom_name',
					}, {
						label: '物料编码',
						prop: 'material_code',
					}, {
						label: '物料名称',
						prop: 'material_name',
					}, {
						label: '物料规格描述',
						prop: 'material_desc',
					}, {
						label: '实际售价',
						prop: 'act_price',
					}],
				},
				pagePurchasingOrderSupplementCost: {//采购订单补件成本报表
					page_size: 50,
					page_no: 1,
					pageTotal: 0,
					list: [],
					col: [{
						label: '经销商名称',
						prop: 'dealer_name',
					}, {
						label: '经销店铺',
						prop: 'dealer_shop_name',
					}, {
						label: '业务日期',
						prop: 'business_date',
						format: 'dataFormat1',
						width: 150,
					}, {
						label: '补件申请单号',
						prop: 'bill_supply_no',
					}, {
						label: '采购单号',
						prop: 'bill_purchase_no',
					}, {
						label: '买家昵称',
						prop: 'buyer_name',
					}, {
						label: '物料编码',
						prop: 'material_code',
					}, {
						label: '物料名称',
						prop: 'material_name',
					}, {
						label: '物料规格描述',
						prop: 'material_desc',
					}, {
						label: '补件成本',
						prop: 'supply_cost',
					}],
				},
				pagePurchasingOrderFreight: {//采购订单运费报表
					page_size: 50,
					page_no: 1,
					pageTotal: 0,
					list: [],
					col: [{
						label: '经销商名称',
						prop: 'dealer_name',
					}, {
						label: '业务日期',
						prop: 'business_date',
						format: 'dataFormat1',
						width: 150,
					}, {
						label: '补件申请单号',
						prop: 'bill_supply_no',
					}, {
						label: '买家昵称',
						prop: 'buyer_name',
					}, {
						label: '补件出库单号',
						prop: 'bill_outbound_no',
					}, {
						label: '补件运费',
						prop: 'supply_cost',
					}],
				},
				pagePurchasingrefundcost: {//采购退款成本报表
					page_size: 50,
					page_no: 1,
					pageTotal: 0,
					list: [],
					col: [{
						label: '经销商名称',
						prop: 'dealer_name',
					}, {
						label: '经销店铺',
						prop: 'dealer_shop_name',
					}, {
						label: '业务日期',
						prop: 'business_date',
						format: 'dataFormat1',
						width: 150,
					},  {
						label: '买家昵称',
						prop: 'buyer_name',
					},{
						label: '退款申请单号',
						prop: 'bill_refund_no',
					}, {
						label: '退款类型',
						prop: 'refund_type',
						formatter: prop => ({
							CANCEL: '未发取消',
							OVERPAY: '多付',
							PREFERENTIAL: '优惠/返现',
							DELAY: '延迟赔付',
							COMPENSATE: '补偿',
							COMPENSATION: '补偿',
							REPAIR: '维修费',
							RETURNS: '退货货款',
							CARRIAGE: '运费',
							DELIVERY_FEE: '运费',
							THREE: '三包费',
							THREE_FEE: '三包费',
							O2O_DIFF: 'O2O跨店铺差价',
							O2O_PRICE_DIFF: 'O2O跨店铺差价',
							PRICE_DIFF: '差价',
							DISCOUNT: '外购折现',
							DISCOUNTING: '外购折现',
						}[prop] || prop),
					}, {
						label: '退款金额',
						prop: 'refund_amount',
					}],
				},
				pagePurchasingServiceCost: {//采购服务成本报表
					page_size: 50,
					page_no: 1,
					pageTotal: 0,
					list: [],
					col: [{
						label: '经销商名称',
						prop: 'dealer_name',
					}, {
						label: '经销店铺',
						prop: 'dealer_shop_name',
					}, {
						label: '业务日期',
						prop: 'business_date',
						format: 'dataFormat1',
						width: 150,
					}, {
						label: '买家昵称',
						prop: 'buyer_name',
					}, {
						label: '服务单号',
						prop: 'bill_service_no',
					}, {
						label: '服务类型',
						prop: 'service_type',
					}, {
						label: '服务金额',
						prop: 'service_amount',
					}],
				},
			},
			selectTab2: 'pageReturnofpurchaseorder',
			rules: {
	          begin_date: [{
	            required:true,
	            message:'请选择开始日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	        },
		}
	},
	methods: {
		selectTab2Click (){
			this.searchFun()
		},
		reset() {
			for(let v in this.form) {
				if(v === 'dealer_customer_id'){
					this.form[v] = null;
				}else {
					this.form[v] = '';
				}
			}
		},
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_REPORT_RETURN_PURCHASE_ORDER',
					},
				},
			})
		},
		exportExcel (){
			this.checkValidate(() => {
				this.ajax.postStream('/reports-web/api/reports/afterSale/exportReturnofpurchaseorder', {
					begin_date: this.form.begin_date,
					end_date: this.form.end_date,
					dealer_customer_id:  this.form.dealer_customer_id,
					dealer_customer_name:  this.form.dealer_customer_name,
					page_size: 20000,
					page_no: 1,
					// page_no: this.tabObjData[this.selectTab2].page_no,
					// page_size: this.tabObjData[this.selectTab2].page_size,
				}, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
					}else {
						this.$message.error(res.body.msg)
					}
				})
			})
		},
		checkValidate (cb){
			this.$refs.$searchObj.validate(valid => {
				if(valid) {
					cb()
				}
			})
		},
		searchFun(){
			this.checkValidate(() => {
				var markClickTabName = this.selectTab2//先保存一个变量原因是：有可能存在一种情况，请求时是一个this.selectTab2值，但由于快速切换页签，导致请求callback后this.selectTab2变成了另一个值

				this.ajax.postStream('/reports-web/api/reports/afterSale/' + markClickTabName, {
					begin_date: this.form.begin_date,
					end_date: this.form.end_date,
					dealer_customer_id:  this.form.dealer_customer_id,
					page_no: this.tabObjData[markClickTabName].page_no,
					page_size: this.tabObjData[markClickTabName].page_size,
				}, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
						this.tabObjData[markClickTabName].list = res.body.content.list || []
						this.tabObjData[markClickTabName].pageTotal = res.body.content.count
					}else {
						this.$message.error(res.body.msg)
					}
				}, f => f, this.params.tabName)
			})
		},
		// 搜索
		// search(list, resolve) {
		// 	this.form.where = list
		// 	this.searchFun(null, resolve)
		// },
		pageChange(page_size){
			this.tabObjData[this.selectTab2].page_size = page_size
			this.searchFun()
		},
		currentPageChange(page){
			this.tabObjData[this.selectTab2].page_no = page
			this.searchFun()
		},
		getDealerCustomerList (){
			this.$root.eventHandle.$emit('alert',{
				title: '选择经销商',
				style: 'width:1000px;height:600px',
				component: ()=>import('@components/after_sales_report/afterSalePurchaseSelectDealerCustomer'),
				params: {
					callback: d => {						
						this.form.dealer_customer_name = d.customer_source_name
		              	this.form.dealer_customer_id = d.customer_source_id
					}
				},
			})
		},
		//   1)当用户登录账号为采购经销账号时，置灰该选择框，值默认为该账号下的经销商名称
		//   2当用户登录账号为不为采购经销账号时，不置灰该选择框，用户可自行选择经销商
		getDefaultDealerCustomer (){
			this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttributeList', {
		        "page": {
		          "length": 50,
		          "pageNo": 1
		        },
		        "personId": this.getEmployeeInfo('personId'),
		        "key": "",
		        "salesmanType": "GLOBAL"
		    }, res => {
		        if(res.body.result){
		          ;(res.body.content.list || []).some(o => {
		            if(o.salesmanType === 'GLOBAL' && o.attribute === 'CUSTOMER'){
		            	this.form.dealer_customer_name = o.attributeValueText
		              	this.form.dealer_customer_id = o.attributeValue
		              	return true
		            }
		          })
		        }
		    })
		},
		checkUserType (){
			this.ajax.get('/user-web/api/userPerson/getUserPerson/' + this.getEmployeeInfo('personId'), res => {
				if(res.body.result){
					if(res.body.content.type === 'PURCHASE_DEALER'){
						this.getDefaultDealerCustomer()
					}else {
						this.dealerCustomerDisabled = false
					}
				}
			})
		},
	},
	mounted (){
		this.checkUserType()
		this.searchFun()
	},
}
</script>