<!-- 采购经销报表-经销商名称弹框 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='cols' 
		:searchPage='search.page_name' 
		:pageTotal='count' 
		selection="radio" 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@search-click='searchClick' 
		@radio-change='selectionChange'
		@row-dblclick='rowDblclick'
		ref="xptList"
	></xpt-list>	
</template>
<script>
export default {
    props:['params'],
	data() {
		return {
			list: [],
			btns: [{
				type: 'primary',
				txt: '确认',
				click: this.close
			}],
			cols: [{
				label: '经销商名称',
				prop: 'customer_source_name',
			}, {
				label: '经销商编码',
				prop: 'customer_source_number',
			}, {
				label: '店铺编码',
				prop: 'shop_code'
			}, {
				label: '店铺名称',
				prop: 'shop_name'
			}, {
					label: '店铺分类',
					prop: 'shop_type',
					format: 'auxFormat',
					formatParams: 'shopClassify'
				},{
					label: '店铺分组',
					format: 'auxFormat',
					prop: 'shop_group',
					formatParams: 'shopGroup'
				}, {
				label: '所属公司',
				prop: 'belong_company'
			}, {
				label: '店铺仓库',
				prop: 'shop_warehouse_name'
			}],
			search: {
				page:{
					pageNo:1,
					length:50,
				},
				page_name: 'cloud_shop_v2',
				where: [],
				shop_type: 'PURCHASE_DEALER',
			},
			count: 0,
			selectData: null,
		}
	},
	methods: {
		pageSizeChange(ps) {
			this.search.page.length = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page.pageNo= page;
			this.getList();
		},
		searchClick(obj, resolve) {
			this.search.where = obj;
			this.getList(resolve);
		},
		selectionChange(obj) {
			this.selectData = obj;
		},
		rowDblclick(obj) {
			this.close();
		},
		getList(resolve) {
			this.ajax.postStream("/material-web/api/shopv2/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve()
			});
		},
		close() {
			if(!this.selectData) {
				this.$message.error('请选择一行');
				return;
			}
			this.params.callback(this.selectData);
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
		},
	},
	mounted() {
		this.getList()
	}
}
</script>
