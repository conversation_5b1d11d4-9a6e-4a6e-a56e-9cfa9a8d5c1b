<!-- 售后基金报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="年份：" prop='year'>
            <el-select size="mini"  v-model="query.year" placeholder="请选择">
              <el-option
                v-for="(value,key) in yearList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
              <el-tooltip v-if='rules.year[0].isShow' class="item" effect="dark" :content="rules.year[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-select>
          </el-form-item>
          <el-form-item label="大分组：">
            <xpt-input v-model='query.big_group' icon='search' :on-icon-click='openBigGroup' size='mini' @change='bigGroupChange'></xpt-input>
          </el-form-item>

        </el-col>
        <el-col :span='6'>
          <el-form-item label="月份：" prop='month'>
            <el-select size="mini"  v-model="query.month" placeholder="请选择">
              <el-option
                v-for="(value,key) in monthList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
              <el-tooltip v-if='rules.month[0].isShow' class="item" effect="dark" :content="rules.month[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-select>
          </el-form-item>
          <el-form-item label="分组：">
            <xpt-input v-model='query.staff_group' icon='search' :on-icon-click='openGroup' size='mini' @change='groupChange'></xpt-input>
          </el-form-item>

        </el-col>
        <el-col :span='6'>
          <el-form-item label="部门：" >
            <el-select size="mini"  v-model="query.department" placeholder="请选择">
              <el-option
                v-for="(value,key) in departmentList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="业务员：">
            <xpt-input v-model='query.staff_group' icon='search' :on-icon-click='openGroup' size='mini' @change='groupChange'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_AFTER_SALE_FUND")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          year: '',
          month: '',
          staff: '',
          staff_id: '',
          staff_group: '',
          staff_group_id: '',
          big_group: '',
          big_group_id: '',
          department:''
        },
        cols: [
          {
            label: '部门',
            prop: 'department'
          }, {
            label: '处理人',
            prop: 'staff_name',
          }, {
            label: '分组',
            prop: 'group_name'
          }, {
            label: '年月',
            prop: 'record_day',
            formatter:function(row){
              var date=new Date(row);
              return date.getFullYear()+"-"+(date.getMonth()+1);
            }
          }, {
            label: '基金总额',
            prop: 'fund_amount',
          }, {
            label: '已使用基金',
            prop: 'use_amount'
          }, {
            label: '剩余基金',
            prop: 'rest_amount'
          }, {
            label: '售后成本率',
            prop: 'aftersale_rate'
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {

      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.select_date=new Date(data.year+"-"+data.month+"-01").getTime();
            delete data.staff;
            delete data.big_group;
            delete  data.staff_group;
            delete data.year;
            delete data.month;

            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/pageReportAftersaleFund', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.select_date=new Date(data.year+"-"+data.month+"-01").getTime();
            delete data.staff;
            delete data.big_group;
            delete  data.staff_group;
            delete data.year;
            delete data.month;

            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportFund', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
