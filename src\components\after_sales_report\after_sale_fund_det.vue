<!-- 售后基金明细报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="统计人分组：">
            <xpt-input v-model='query.staff_group' icon='search' :on-icon-click='openGroup' size='mini' @change='groupChange'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="统计人项目：">
            <xpt-input v-model='query.source_name'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="统计人：">
            <xpt-input v-model='query.staff' icon='search' :on-icon-click='openSatff' size='mini' @change='staffChange'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_AFTER_SALE_FUND_DETAILS")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          staff: '',
          staff_id: '',
          staff_group: '',
          staff_group_id: '',
          big_group: '',
          big_group_id: '',
          source_name:''
        },
        //暂时所有业务人员
        salesmanTypeList:__AUX.getCode('businessType'),
        cols: [
          /*{
            label: '部门',
            prop: 'department'
          }, {
            label: '取值来源',
            prop: 'staff_group_name',
          },*/ {
            label: '统计项目',
            prop: 'source_name'
          }, {
            label: '统计人',
            prop: 'staff_name',
          }, {
            label: '分组',
            prop: 'group_name',
          }, {
            label: '部门',
            prop: 'department'
          }, {
            label: '统计日期',
            prop: 'source_date',
            format: 'dataFormat'
          }, {
            label: '单据日期',
            prop: 'record_day',
            format: 'dataFormat'
          }, {
            label: '统计金额',
            prop: 'fund_amount'
          }, {
            label: '买家昵称',
            prop: 'buyer_name'
          }, {
            label: '业务员',
            prop: 'salesman'
          }, {
            label: '统计单号',
            prop: 'source_no'
          }, {
            label: '单据类型',
            prop: 'source_type'
          }, {
            label: '合并订单编号',
            prop: 'merge_no'
          }, {
            label: '林氏销售订单编号',
            prop: 'sale_order_no'
          }, {
            label: '林氏批次订单编号',
            prop: 'batch_order_no'
          }, {
            label: '商品编码',
            prop: 'fnumber'
          }, {
            label: '商品名称',
            prop: 'fname'
          }, {
            label: '数量',
            prop: 'fnum'
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {

      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date = +new Date(data.end_date);
            delete data.staff;
            delete data.big_group;
            delete data.big_group_id;
            delete  data.staff_group;
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/pageReportAftersaleFundDetails', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date = +new Date(data.end_date);
            delete data.staff;
            delete data.big_group;
            delete data.big_group_id;
            delete  data.staff_group;
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportFundDetails', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
