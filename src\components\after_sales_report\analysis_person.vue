<!-- 责任人报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="提交开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="责任人名称：">
            <xpt-input v-model='query.liability_person_name' size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="责任范围名称：">
            <xpt-input v-model='query.liability_scope_name' size='mini'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="提交结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="责任类型：">
            <xpt-input v-model='query.liability_type' size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="来源单据类型：">
            <el-select v-model="query.original_bill_type" clearable placeholder="请选择" size='mini'>
              <el-option
                v-for="(val, key) in original_bill_type_options"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="合并单号：">
            <xpt-input v-model='query.merge_trade_no' size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="责任状态：">
            <el-select v-model="query.liability_status" clearable placeholder="请选择" size='mini'>
              <el-option
                v-for="(val, key) in liability_status_options"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_AFTERSALE_ANALYSIS_PERSON")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          liability_person_name: '',
          liability_scope_name: '',
          liability_type: '',
          merge_trade_no: '',
          liability_status: '',
          original_bill_type:''
        },

        liability_status_options: {
          // APPEAL: '申诉',
          WAIT_SUBMIT: '待提交',
          WAIT_CONFIRM: '待确认',
          COMPLAIN: '申诉',
          END: '终止',
          WAIT_REVOKE: '待撤回',
          RECALLING: '撤回中',
          RECALLED: '已撤回',
          RECALL_FAIL: '撤回失败',
          CONFIRMED: '已确认',
        },

        //来源单据类型
        original_bill_type_options: {
            SUPPLY: '补件单',
            REPAIR: '服务单',
            REFUND_NEW: '新退款申请单',
            REFUND: '退款单',
            RETURNS: '退货跟踪单',
        },

        cols: [
          {
            label: '责任分析单编号',
            prop: 'parent_bill_no',
            width:'150',
          }, {
            label: '责任分析子单编号',
            prop: 'sub_bill_no',
            width:'150',
          }, {
            label: '来源单据类型',
            prop: 'original_bill_type'
          }, {
            label: '来源单据编号',
            prop: 'original_bill_no',
            width:'150',
          }, {
            label: '责任状态',
            prop: 'liability_status'
          }, {
            label: '锁定人',
            prop: 'locker_name',
          }, {
            label: '子单费用',
            prop: 'sub_fee',
          }, {
            label: '提交时间',
            prop: 'submit_time',
            width:'150',
            format:"dataFormat1"
          }, {
            label: '合并单号',
            prop: 'merge_trade_no',
            width:'150',
          }, {
            label: '买家昵称',
            prop: 'buyer_nick'
          }, {
            label: '商品编码',
            prop: 'goods_code',
          }, {
            label: '规格型号',
            prop: 'specification'
          }, {
            label: '商品名称',
            prop: 'goods_name',
          }, {
            label: '责任问题名称',
            prop: 'liability_question_name',
          }, {
            label: '责任类型',
            prop: 'liability_type',
          }, {
            label: '责任范围名称',
            prop: 'liability_scope_name',
          }, {
            label: '责任问题描述',
            prop: 'liability_question_description',
          }, {
            label: '责任人名称',
            prop: 'liability_person_name'
          }, {
            label: '备注',
            prop: 'remark',
          }, {
            label: '责任金额',
            prop: 'liability_amount'
          }, {
            label: '处理金额',
            prop: 'handle_amount',
          }, {
            label: '批号',
            prop: 'lot',
            width:'150',
          }, {
            label: '供应商',
            prop: 'suppiler_name',
          }, {
            label: '发货时间',
            prop: 'out_stock_time',
            width:'150',
            format:"dataFormat1"
          }, {
            label: '批次订单号',
            prop: 'batch_trade_no',
            width:'150',
          }, {
            label: '采购入库日期',
            prop: 'instock_date',
            width:'150',
            format:"dataFormat1"
          }, {
            label: '物流供应商名字',
            prop: 'logistics_supplier_name',
          }
        ],
      }
    },
    methods: {

      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date =+new Date(data.end_date) + 1000 * 60 * 60 * 24 - 1000;//比如选中2018-05-08，则正确结束日期是：2018-05-08 23:59:59
            // delete data.big_group;
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/getAnalysisPerson', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date =+new Date(data.end_date) + 1000 * 60 * 60 * 24 - 1000;//比如选中2018-05-08，则正确结束日期是：2018-05-08 23:59:59
            // delete data.big_group;
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportAfterSaleAnalysisPerson', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
  }
</script>
