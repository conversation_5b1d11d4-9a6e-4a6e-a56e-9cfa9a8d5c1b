<!-- 售后完结率报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="大分组：">
            <xpt-input v-model='query.big_group' icon='search' :on-icon-click='openBigGroup' size='mini' @change='bigGroupChange'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="分组：">
            <xpt-input v-model='query.staff_group' icon='search' :on-icon-click='openGroup' size='mini' @change='groupChange'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="业务人员：">
            <xpt-input v-model='query.staff' icon='search' :on-icon-click='openSatff' size='mini' @change='staffChange'></xpt-input>
          </el-form-item>
          <!--<el-form-item label="部门：">
            <xpt-input v-model='query.staff' icon='search' :on-icon-click='openSatff' size='mini' @change='staffChange'></xpt-input>
          </el-form-item>-->
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          staff: '',
          staff_id: '',
          staff_group: '',
          staff_group_id: '',
          big_group: '',
          big_group_id: ''
        },
        //售前与售后人员
        salesmanTypeList:(__AUX.getCode('afterSaleType') || []).concat(__AUX.getCode('preSale') || []),
        cols: [
          /*{
            label: '部门',
            prop: 'staff_group_name',
          }, */{
            label: '大分组',
            prop: 'big_group_name',
          }, {
            label: '分组',
            prop: 'group_name',
          }, {
            label: '客服',
            prop: 'staff_name'
          }, {
            label: '是否离职',
            prop: 'status',
            formatter:function(row){

              return row==0?"是":"否";
            }
          }, {
            label: '完结订单数',
            prop: 'order_num',
          }, {
            label: '已发完货完结笔数',
            prop: 'not_deliver_num'
          }, {
            label: '已发完货30天内完结笔数',
            prop: 'in_thirty_num'
          }, {
            label: '已发完货超30天完结笔数',
            prop: 'over_thirty_num',
          }, {
            label: '未发完货完结笔数',
            prop: 'not_deliver_num'
          }, {
            label: '完结率',
            prop: 'completion_rate'
          }, {
            label: '退货金额',
            prop: 'return_amount'
          }, {
            label: '完结业绩',
            prop: 'completion_amount'
          }, {
            label: '退货率',
            prop: 'return_rate'
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {

      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date =+new Date(data.end_date);
            delete data.staff;
            delete data.staff_group;
            delete data.big_group;
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/pageReportAfterCompletion', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date =+new Date(data.end_date);
            delete data.staff;
            delete data.staff_group;
            delete data.big_group;
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportCompletion', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
