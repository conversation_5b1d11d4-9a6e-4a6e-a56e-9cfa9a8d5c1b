<!--产品参数-->
<template>
  <div>
    <el-dialog
      title="线上产品参数"
      :visible.sync="onlineVisible"
      class="product-params-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :center="true"
    >
      <row-item
        v-if="!!onlineParamsList.length"
        :paramsList="onlineParamsList"
      ></row-item>
      <span v-else class="no-data">暂无数据</span>
      <div slot="footer" class="dialog-footer" v-if="isInner">
        <el-button type="primary" @click="handleConfirmCopy">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import rowItem from "./rowItem.vue";
export default {
  components: {
    rowItem,
  },
  data() {
    return {
      onlineVisible: false,
      isInner: false,
      params: {},
      onlineParamsList: [],
    };
  },
  mounted() {},
  computed: {},
  methods: {
    open(params) {
      this.params = params;
      let { data } = this.params;
      this.onlineParamsList = data || [
        { paramName: '颜色', paramValue: '黑色' },
        { paramName: '尺码', paramValue: 'XL' },
        { paramName: '材质', paramValue: '棉' },
        { paramName: '品牌', paramValue: 'XXX' },
      ]
      this.onlineVisible = true;
    },
    reset() {
      this.onlineParamsList = [];
    },
    handleConfirmCopy() {
      if (this.onlineParamsList.length > 0) {
        this.offlineParamsList = JSON.parse(
          JSON.stringify(this.onlineParamsList)
        );
      }
      this.onlineVisible = false;
      this.isInner = false;
    },
  },
};
</script>

<style>
.product-params-dialog .el-dialog--small {
  width: 550px !important;
}
.product-params-dialog .el-dialog__body {
  height: 590px;
  overflow-y: auto;
}
.product-params-dialog .el-dialog__footer {
  text-align: center;
}

.product-params-dialog .dialog-footer {
  display: flex;
}

.product-params-dialog .el-button {
  flex: 1;
}

.product-params-dialog .no-data {
  text-align: center;
  margin-top: 50%;
  color: #999;
  display: block;
}
</style>
