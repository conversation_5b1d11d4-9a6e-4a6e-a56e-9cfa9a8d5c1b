<template>
  <div>
    <el-row
      :gutter="20"
      type="flex"
      class="row-bg"
      justify="center"
      v-for="(item, i) in paramsList"
      :key="i"
    >
      <el-col :span="12 - !!showClose">
        <el-input
          v-model="item.paramName"
          placeholder="请输入名称"
          class="input-width"
          :readonly="!showClose"
        ></el-input>
      </el-col>
      <el-col :span="12 - !!showClose">
        <el-input
          v-model="item.paramValue"
          placeholder="请输入内容"
          class="input-width"
          :readonly="!showClose"
        ></el-input>
      </el-col>
      <el-col :span="2" v-if="showClose">
        <div class="icon-flex" @click="handleClose(item, i)">
          <i class="el-icon-close"></i>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    showClose: {
      type: <PERSON>olean,
      default: false,
    },
    paramsList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      keyInput: "",
      valueInput: "",
    };
  },
  mounted() {},
  methods: {
    handleClose(item, i) {
      this.$emit("delete", { i, item });
    },
  },
};
</script>

<style scoped>
.row-bg {
  margin: 10px 0;
}
.input-width {
  width: 100%;
}
.icon-flex {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: red;
  height: 100%;
}
</style>