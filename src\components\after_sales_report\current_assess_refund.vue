<!-- 退款专员及时性考核报表  -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='10' class='xpt-top'>
		<el-form :rules='rules' ref='$searchObj' :model='searchObj' label-position="right" label-width="120px">
			<el-col :span='6'>
				<el-form-item label="买家昵称：">
					<xpt-input v-model='searchObj.nickName' size='mini' icon="search" :on-icon-click="selectBuyersName" @change="changeNickName"></xpt-input>
				</el-form-item>
				<el-form-item label="提交财务开始时间：" prop="submitFinanceTimeStart">
					<el-date-picker v-model="searchObj.submitFinanceTimeStart" type="date" @change="checkDateRequired" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.submitFinanceTimeStart[0].isShow' class="item" effect="dark" :content="rules.submitFinanceTimeStart[0].message" placement="right-start" popper-class='xpt-form__error'>
		              <i class='el-icon-warning'></i>
		            </el-tooltip>
				</el-form-item>
				<el-form-item label="核对开始时间：" prop="checkTimeStart">
					<el-date-picker v-model="searchObj.checkTimeStart" type="date" @change="checkDateRequired" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.checkTimeStart[0].isShow' class="item" effect="dark" :content="rules.checkTimeStart[0].message" placement="right-start" popper-class='xpt-form__error'>
		              <i class='el-icon-warning'></i>
		            </el-tooltip>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="核对人姓名：">
					<xpt-input v-model='searchObj.check_person_name' size='mini' icon="search" :on-icon-click="() => selectBestStaff2('check_person_name')" @change="() => changeBestStaff('check_person_name')"></xpt-input>
				</el-form-item>
				<el-form-item label="提交财务结束时间：" prop="submitFinanceTimeEnd">
					<el-date-picker v-model="searchObj.submitFinanceTimeEnd" type="date" @change="checkDateRequired" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.submitFinanceTimeEnd[0].isShow' class="item" effect="dark" :content="rules.submitFinanceTimeEnd[0].message" placement="right-start" popper-class='xpt-form__error'>
		              <i class='el-icon-warning'></i>
		            </el-tooltip>
				</el-form-item>
				<el-form-item label="核对结束时间：" prop="checkTimeEnd">
					<el-date-picker v-model="searchObj.checkTimeEnd" type="date" @change="checkDateRequired" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.checkTimeEnd[0].isShow' class="item" effect="dark" :content="rules.checkTimeEnd[0].message" placement="right-start" popper-class='xpt-form__error'>
		              <i class='el-icon-warning'></i>
		            </el-tooltip>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="财务锁定人姓名：">
					<xpt-input v-model='searchObj.finance_lock_person_name' size='mini' icon="search" :on-icon-click="() => selectBestStaff2('finance_lock_person_name')" @change="() => changeBestStaff('finance_lock_person_name')"></xpt-input>
				</el-form-item>
			</el-col>
			<el-col :span="6" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='searchFun'>查询</el-button>
				<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData="colData"
		:pageTotal='pageTotal'
        :showHead="false"
        selection=''
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
	></xpt-list>
</div>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
	props:['params'],
	data (){
		var self = this

		return {
			form: {
				page_name: 'aftersale_bill_refund_application',
				page_size: 50,
				page_no: 1,
			},
			searchObj: {
				nickName: '',
				check_person_name: '',
				finance_lock_person_name: '',
				submitFinanceTimeStart: '',
				submitFinanceTimeEnd: '',
				checkTimeStart: '',
                checkTimeEnd: '',
                checkPerson: '',
                financeLockPerson: ''
			},
			rules: {
	          submitFinanceTimeStart: [{
	            required:false,
	            message:'请选择开始日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              if(!self.rules[rule.field][0].required){
	                callback();
	                return
	              }

	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	          submitFinanceTimeEnd: [{
	            required:false,
	            message:'请选择结束日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              if(!self.rules[rule.field][0].required){
	            callback();
	            return
	          }

	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	          checkTimeStart: [{
	            required:false,
	            message:'请选择开始日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              if(!self.rules[rule.field][0].required){
	                callback();
	                return
	              }

	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	          checkTimeEnd: [{
	            required:false,
	            message:'请选择结束日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              if(!self.rules[rule.field][0].required){
	            callback();
	            return
	          }

	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	        },
			listFieldsWhere: null,
			pageTotal: 0,
			roleList:[],
			colData: [{
				label: '退款申请单号',
				prop: 'bill_no',
				width: 180,
			}, {
				label: '买家昵称',
				prop: 'nick_name',
			}, {
				label: '申请金额',
				prop: 'apply_amount',
			}, {
				label: '实退金额',
				prop: 'actual_amount',
			}, {
				label: '提交财务时间',
				prop: 'submit_finance_time',
				format: 'dataFormat1',
				width: 150,
			}, {
				label: '财务锁定人姓名',
				prop: 'finance_lock_person_name',
			}, {
				label: '核对人姓名',
				prop: 'check_person_name',
			}, {
				label: '核对时间',
				prop: 'check_time',
				format: 'dataFormat1',
				width: 150,
			}, {
				label: '小时差',
				prop: 'time_difference',
			}],
		}
	},
	methods: {
        changeNickName () {
            this.searchObj.nickName = ''
        },
        changeBestStaff (key) {
            if (key == 'finance_lock_person_name') {
                this.searchObj.financeLockPerson = ''
            } else if (key == 'check_person_name') {
                this.searchObj.checkPerson = ''
            }
            this.searchObj[key] = ''
        },
		// 核对人选择
		selectBestStaff2 (key){
			this.$root.eventHandle.$emit('alert',{
				title: '人员列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/after_sales_report/select_personel'),
				params: {
					callback: d => {
                        if (key == 'finance_lock_person_name') {
                            this.searchObj.financeLockPerson = d.userId
                        } else if (key == 'check_person_name') {
                            this.searchObj.checkPerson = d.userId
                        }
						this.searchObj[key] = d.fullName
					}
				},
			})
		},
		// 买家昵称
		selectBuyersName (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/customers/list'),
				style:'width:900px;height:600px',
				title:'买家昵称列表',
				params: {
					close: d => {
						this.searchObj.nickName = d.name
					}
				},
			})
		},
		reset() {
			for(let v in this.searchObj) {
				this.searchObj[v] = '';
			}
		},
		checkDateRequired (){
	        if(this.searchObj.submitFinanceTimeStart || this.searchObj.submitFinanceTimeEnd){
	          this.rules.submitFinanceTimeStart[0].required =
	          this.rules.submitFinanceTimeEnd[0].required = true
	        }

	        if(this.searchObj.checkTimeStart || this.searchObj.checkTimeEnd){
	          this.rules.checkTimeStart[0].required =
	          this.rules.checkTimeEnd[0].required = true
	        }

	        if(!this.searchObj.submitFinanceTimeStart && !this.searchObj.submitFinanceTimeEnd){
	          this.rules.submitFinanceTimeStart[0].required =
	          this.rules.submitFinanceTimeEnd[0].required = false
	        }

	        if(!this.searchObj.checkTimeStart && !this.searchObj.checkTimeEnd){
	          this.rules.checkTimeStart[0].required =
	          this.rules.checkTimeEnd[0].required = false
	        }
		},
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_REFUND_CURRENT_ASSESS',
					},
				},
			})
		},
		exportExcel (){
            let params = Object.assign({}, this.searchObj, this.form)
            delete params.finance_lock_person_name
            delete params.check_person_name
			this.checkValidate(() => {
				this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportCurrentAssessRefund', params, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
					}else {
						this.$message.error(res.body.msg)
					}
				})
			})
		},
		checkValidate (cb){
			this.$refs.$searchObj.validate(valid => {
				if(valid) {
					cb()
				}
			})
		},
		searchFun(){
			this.checkValidate(() => {
                let params = Object.assign({}, this.searchObj, this.form)
                delete params.finance_lock_person_name
                delete params.check_person_name
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/currentAssessRefundExcel', params, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
						this.roleList = res.body.content.list || []
						this.pageTotal = res.body.content.count
					}else {
						this.$message.error(res.body.msg)
					}
				}, f => f, this.params.tabName)
			})
		},
		// 搜索
		// search(list, resolve) {
		// 	this.form.where = list
		// 	this.searchFun(null, resolve)
		// },
		pageChange(page_size){
			this.form.page_size = page_size
			this.searchFun()
		},
		currentPageChange(page){
			this.form.page_no = page
			this.searchFun()
		},
	},
	mounted (){
	},
}
</script>