<!-- 抖音返点列表 -->
<template>
	<count-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:searchPage='search.page_name'
		:pageTotal='count'
		selection="checkbox"
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
		@search-click='searchClick'
		@selection-change='selectionChange'
		countUrl="/reports-web/api/dy/rebate/record/count"
    	:showCount ='showCount'
		ref="xptList"
	>
        <xpt-import slot='btns' :taskUrl="uploadUrl"  class='mgl10' :isupload="false"></xpt-import>
	
	</count-list>
</template>
<script>
import countList from '@components/common/list-count'
export default {
    props:['params'],
	components: {
        countList,
      },
	data() {
		return {
			uploadUrl:'/reports-web/api/dy/rebate/record/import?permissionCode=DY_REBATE_RECORD_IMPORT',
			showCount:false,
			list: [],
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: () => this.getList(),
			}, {
				type: 'primary',
				txt: '查看导入结果',
				click: () => this.showUploadResult(),
			},
			 {
				type: 'primary',
				txt: '作废',
				click: () => this.cancel(),
			},
			],
			cols: [{
				label: '父项编码',
				prop: 'pcode',
				width: 180
			}, {
				label: '抖音直播到手价',
				prop: 'live_price',
				width: 180
			}, {
				label: '门店佣金',
				prop: 'store_commission',
			}, {
				label: '生效时间',
				prop: 'effect_time',
                  format: 'dataFormat1'

			}, {
				label: '失效时间',
				prop: 'disable_time',
                  format: 'dataFormat1'

			}, {
				label: '返点类型',
				prop: 'rebate_type_name',
			}, {
				label: '作废状态',
				prop: 'status_name',
				width: 100
			}, ],
			search: {
				page_no:1,
				page_size: this.pageSize,
				page_name: 'report_dy_rebate_record',
				where: []
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
		 //导入结果
        showUploadResult() {
            this.$root.eventHandle.$emit("alert", {
                style: "width:900px;height:600px",
                title: "导入结果",
                params: {
                    url: "/reports-web/api/dy/rebate/record/import/list?permissionCode=DY_REBATE_RECORD_IMPORT",
                    data: {
						"excel_type":"EXCEL_TYPE_DY_REBATE_RECORD_IMPORT"
					},
                    showDownload: true,
                },
                component: () => import("@components/common/eximport"),
            });
        },
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			let self = this;
			this.search.where = obj;
			new Promise((res,rej)=>{
					self.getList(resolve,res);
				}).then(()=>{
					if(this.search.page_no != 1){
						self.count = 0;
					}
					self.showCount = false;
				})
			},
		selectionChange(obj) {
			this.selectData = obj;
		},
		getList(resolve,callback) {
			this.ajax.postStream("/reports-web/api/dy/rebate/record/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					// this.count = res.body.content.count;
					if(!this.showCount){
						let total = this.search.page_no * this.search.page_size
						this.count = res.body.content.list.length == (this.search.page_size+1)? total+1:total;
              			this.list = res.body.content.list.splice(0,res.body.content.list.length == (this.search.page_size+1)?total:res.body.content.list.length);

					}
				} else {
					this.$message.error(res.body.msg)
				}
				callback && callback();
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				callback && callback();
				resolve && resolve()
			});
		},
	
	cancel(status){
		if(!this.selectData.length){
			return this.$message.error("请先至少选择一条数据！")
		}

		this.$confirm("是否确定作废已选数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.ajax.postStream('/reports-web/api/dy/rebate/record/cancel?permissionCode=DY_REBATE_RECORD_CANCEL',
		 	this.selectData.map(item=>item.id), 
			res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
					this.getList()
				}else {
					this.$message.error(res.body.msg)
				}
			})
        })
        .catch(() => {
          this.$message.error("已取消");
        });

		
	}
	},
	mounted() {
		// this.getList()
	},
}
</script>
