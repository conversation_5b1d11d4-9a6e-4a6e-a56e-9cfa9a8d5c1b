<!-- 报表excel导出列表 -->
<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='primary' size='mini' @click='getList' slot='left' :loading='onRefresh' :disabled='onRefresh'>刷新</el-button>
      <!--<el-input size='mini' v-model='test' slot='right' placeholder='任务类型'></el-input>-->
      <!--<el-button type='info' size='mini' slot='right' @click='testClick' :loading='testStatus' :disabled='testStatus'>调用</el-button>-->
    </xpt-headbar>
    <xpt-list
      :data='list'
      :colData='cols'
      :btns='btns'
      :pageTotal='count'
      selection=''
      :showHead='false'
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this;
      return {
        list: [],
        btns: [{
          type: 'primary',
          txt: '刷新',
          click: self.getList,
          loading: false
        }],
        cols: [
          {
            label: '报表类型',
            prop: 'program_name'
          }, {
            label: '执行状态',
            prop: 'program_status',
            formatter: self.statusExchage
          }, {
            label: '返回结果',
            prop: 'result'
          }, {
            label: 'Excel生成状态',
            prop: 'run_status',
            formatter: self.statusExchage
          }, {
            label: '文件路径',
            prop: 'file_path',
            html(val) {
              return val ? '<a href="'+ val +'">下载</a>' : val;
            }
          }, {
            label: '提交人',
            prop: 'submit_man'
          }, {
            label: '提交时间',
            prop: 'submit_time',
            format: 'dataFormat1',
            width: '130'
          }
        ],
        query: {
          list_excel_type: [
            // 售后好评报表
            'EXCEL_TYPE_REPORT_AFTER_GOOD_COMMENT',
            //售后差评明细报表
            'EXCEL_TYPE_REPORT_AFTER_BAD_COMMENT',
            //售后业绩报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_FEE',
            // 售后赠品列表
            'EXCEL_TYPE_AFTER_GIFT',
            //超时处理订单报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_TIMEOUTPROCESSORDER',
            //售后绩效报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_PERFORMANCE',
            //退款专员分区报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_REFUND_PARTITION',
            //退款单数量报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_REFUND_AMOUNT',
            //收款单支付方式金额报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_RECEIPT_PAYMENT',
            //收款单驳回明细报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_RECEIPT_REJECT',
            //主客户变更订单报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_MASTERCUSTOMERCHANGEORDER',
            //收款分布统计报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_RECEIPT_DISTRIBUTION',
            //退货率报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_RETURN_RATE',
            //店铺退款汇总报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_SHOP_REFUND',
            //售后基金明细报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_FUND_DETAILS',
            //售后基金报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_FUND',
            //售后完结率报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_COMPLETION',
            //补件责任分析报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_SUPPLY_DUTY',
            //责任金额报表II
            'EXCEL_TYPE_REPORT_AFTER_SALE_DUTY_AMOUNT',
            // 维修满意度报表
            'EXCEL_TYPE_REPORT_AFTER_REPAIR_SATISFIED',
            // 淘宝下载列表
            'EXCEL_TYPE_AFTERSALE_TAOBAO_REFUND_DOWNLOAD',
            // 售后完结率报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_COMPLETIONRATE',
            // 售后单列表导出
            'EXCEL_TYPE_AFTERSALE_ORDER',
            // 新退款申请单
            'EXCEL_TYPE_AFTERSALE_REFUND_APP',
            // 补件申请单
            'EXCEL_TYPE_AFTERSALE_BILL_SUPPLY',
            // 新退款超时报表
            'EXCEL_AFTER_REPAIR_REFUNDAPP_OVER_TIME',
            // 店铺新退款汇总报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_SHOP_REFUNDAPP',
            // 新退款专员分区报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_REFUNDAPP_PARTITION',
            // 新退款单数量报表
            'EXCEL_TYPE_REPORT_AFTER_SALE_REFUNDAPP_AMOUNT',
            // 责任问题单报表
            'EXCEL_TYPE_AFTERSALE_ANALYSIS_QUESTION',
            // 售后已发货超30天完结明细报表
            'EXCEL_TYPE_AFTERSALE_COMPLETION_OVER_THIRTY_DAYS',
            // 责任分析单锁单量报表
            'EXCEL_TYPE_AFTERSALE_ANALYSIS_LOCK_COUNT',
            // 退款专员及时性考核报表
            'EXCEL_TYPE_REFUND_CURRENT_ASSESS',
            // 责任人报表
            'EXCEL_TYPE_AFTERSALE_ANALYSIS_PERSON',
            // 退款明细 报表
            'EXCEL_TYPE_REFUND_ITEM',
            // 补件明细列表
            'EXCEL_TYPE_SUPPLY_ITEM',
            // 售前售后关系报表
            'EXCEL_TYPE_REPORT_PRESALE_AFTERSALE_RELATIONS',
            // 采购经销售后报表
            'EXCEL_TYPE_REPORT_RETURN_PURCHASE_ORDER',
            // 售后成本汇总报表
            'EXCEL_TYPE_REPORT_COST_SUMMARY',
            //礼品申购数据导出报表
            'EXCEL_TYPE_PRESENT_APPLICATION',
            //停产物料监控报表导出
            'EXCEL_TYPE_STOP_MATERIAL_MONITOR',
            // 店铺物料报表导出
            'EXCEL_TYPE_REPORT_SHOP_MATERIEL',
            // 预约数据跟踪列表导出
            'EXCEL_TYPE_CRM_APPOINTMENT_STATION',
            // 新退款明细报表
            'EXCEL_TYPE_AFTERSALE_REFUND_ITEM_APP',
            // 退款单excel导入结果报表
            'EXCEL_TYPE_REFUND_INTERFACE_EXPORT',
            'EXCEL_TYPE_ACT_AUTO_WIN_LIST_REPORT_EXPORT',
            // 客户售后反馈单列表
            'EXCEL_TYPE_AFTER_DEAL_COUPLE_BACK',
            // 责任人修改记录报表
            'EXCEL_TYPE_AFTERSALE_ANALYSIS_PERSON_CHANGE_LOG',
          ],
          page_size: self.pageSize,
          page_no: 1
        },
        count: 0,
        statusKeyValue: {
          'EXEC_WAIT':'等待',
          'EXEC_RUNING':'执行中',
          'EXEC_FAILED':'失败',
          'EXEC_SUCCESS':'成功'
        },
        test: '',
        testStatus: false,
        onRefresh: false
      }
    },
    methods: {
      getList() {
        this.btns[0].loading = true;
        this.onRefresh = true;

        var data = JSON.parse(JSON.stringify(this.query))
        ,   url = '/file-iweb/api/cloud/excel/list'

        if(this.params.query){
          delete data.list_excel_type
          Object.assign(data, this.params.query)
          url = '/reports-web/api/reports/afterSale/findMyReportList'
        }


        this.ajax.postStream(url, data, res => {
          if(res.body.result && res.body.content) {
            this.list = res.body.content.list || [];
            this.count = res.body.content.count || 0;
          } else {
            this.$message.error(res.body.msg)
          }
          this.btns[0].loading = false;
          this.onRefresh = false;
        }, error => {
          this.btns[0].loading = false;
          this.onRefresh = false;
          this.$message.error(error.statusText)
        })
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.getList();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.getList();
      },
      statusExchage(val) {
        let keyValues = {
          'EXEC_WAIT':'等待',
          'EXEC_RUNING':'执行中',
          'EXEC_FAILED':'失败',
          'EXEC_SUCCESS':'成功'
        };
        return keyValues[val] || val;
      },
      testClick() {
        this.testStatus = true;
        this.ajax.postStream('/order-web/api/report/job/handle', {job_type: this.test}, res => {
          this.testStatus = false;
          this.$message.info(res.body.msg)
        }, err => {
          this.testStatus = false;
          this.$message.error(err)
        })
      }
    },
    mounted() {
      this.getList();
    }
  }
</script>
