<!-- 补件明细列表 -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='12' class='xpt-top'>
		<el-form :rules='rules' :model='serchData' ref="form" label-position="right" label-width="120px">
			<el-col :span='6'>
				<el-form-item label="开始时间：" label-width="120px;" prop='start'>
					<el-date-picker v-model="serchData.start" type="date" placeholder="选择时间" @change="val => serchData.start = val + ' 00:00:00'" :clearable="false" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.start[0].isShow' class="item" effect="dark" :content="rules.start[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
				<el-form-item label="单据类型：" label-width="120px;" >
					<el-select size="mini"  v-model="serchData.type" placeholder="请选择" >
						<el-option
							v-for="(value,key) in typeOption"
							:label="value"
							:value="key" :key='key'>
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="行状态" label-width="120px;">
					<el-select size="mini"  v-model="serchData.status" placeholder="请选择" >
						<el-option
							label="未关闭"
							value="CREATE" key='1'>
						</el-option>
						<el-option
							label="已关闭"
							value="CLOSE" key='2'>
						</el-option>
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="结束时间：" label-width="120px;" prop='end'>
					<el-date-picker v-model="serchData.end" type="date" placeholder="选择时间" @change="val => serchData.end = val + ' 23:59:59'" :clearable="false" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.end[0].isShow' class="item" effect="dark" :content="rules.end[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
				<el-form-item label="供应商" label-width="120px;">
					<el-input v-model="serchData.supplier_company" size="mini"></el-input>
				</el-form-item>
				
				
			</el-col>
			<el-col :span='6'>
				
				<el-form-item label="物料编码" label-width="120px;">
					<el-input v-model="serchData.material_code" size="mini"></el-input>
				</el-form-item>
				<el-form-item label="是否经销订单" label-width="120px;">
					<el-select size="mini"  v-model="serchData.if_dealer" placeholder="请选择" >
						<el-option
							label="是"
							value="Y" key='1'>
						</el-option>
						<el-option
							label="否"
							value="N" key='2'>
						</el-option>
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="6" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='_getList'>查询</el-button>
				<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>
	<xpt-list
        :data='list'
        :colData='cols'
        :pageTotal='pageTotal'
        :showHead="false"
        :orderNo="true"
        selection=''
        @search-click='search'
        @page-size-change='pageChange'
        @current-page-change='currentPageChange'
    ></xpt-list>
</div>
</template>

<script>
import VL from '@common/validate.js'
export default {
	data (){
		var today = (new Date).toLocaleDateString()

		return {
			serchData: {
				page_size: 50,
				page_no: 1,
				start: null,
				end: null,
				type:'',
				status:"",
				page_name: 'aftersale_bill_supply',
				where: [],
			},
			pageTotal: 0,
			list: [],

			listFieldsWhere: null,
			typeOption:{
				SHBJSQD:"售后补件申请单",
				ZTBJSQD:"展厅补件",
				NXBJSQD:"内销补件",
				RESALE:"再售补件",
				BYWJBJ:"备用五金补件",
				DZBJ:"定制补件",
				XSJGSQ:"销售加购申请单",
			},
			cols: [
				{prop: 'after_ticket_no', label: '补件单号', width: 180,},
				{prop: 'merge_trade_no', label: '合并单号', width: 180,},
				{prop: 'after_order_no', label: '售后单号', width: 180,},
				{prop: 'type', label: '补件类型',},
				{prop: 'buyer_name', label: '买家昵称',},
				{prop: 'if_all_lock', label: '是否整单锁库',},
				{prop: 'if_door_install', label: '是否上门安装',},
				{prop: 'business_man_name', label: '业务锁定人',},
				{prop: 'business_man_group', label: '业务锁定人分组',},
				{prop: 'clerk_lock_name', label: '文员锁定人名称',},
				{prop: 'clerk_lock_time', label: '文员锁定时间', format: 'dataFormat1', width: 150,},
				{prop: 'receiver_name', label: '收货人姓名',},
				{prop: 'reveiver_phone', label: '收货人电话', format: 'hidePhoneNumber'},
				{prop: 'province', label: '省',},
				{prop: 'city', label: '市',},
				{prop: 'area', label: '区',},
				{prop: 'receiver_addr', label: '收货人地址', width: 180,},
				{prop: 'business_remark', label: '业务备注',},
				{prop: 'delivery_name', label: '提货点名称',},
				{prop: 'three_name', label: '三包点名称',},
				{
					prop: 'business_status',
					label: '业务状态',
					formatter: prop => ({
						CLERKDEALTING: '文员待办',    
						CLERKHANDLING: '文员处理中',    
						REJECT: '驳回',         
						SALEMANHANDLING: '业务处理中',    
						CREATE: '创建',
					}[prop] || prop),
				},
				{prop: 'status', label: '单据状态',},
				{prop: 'audit_time', label: '审核时间', format: 'dataFormat1', width: 150,},
				{prop: 'creator_name', label: '创建人',},
				{prop: 'create_time', label: '创建时间', format: 'dataFormat1', width: 150,},
				{prop: 'submit_purchase_name', label: '提交采购人名称',},
				{prop: 'submit_purchase_time', label: '提交采购时间', format: 'dataFormat1', width: 150,},
				{prop: 'question_goods_code', label: '问题商品编码',},
				{prop: 'question_goods_name', label: '问题商品名称',},
				{prop: 'question_goods_desc', label: '问题商品规格描述',},
				{prop: 'bom_version', label: 'bom版本',},
				{prop: 'material_code', label: '物料编码',},
				{prop: 'zd_delivery_time', label: '批次发货时间', format: 'dataFormat1', width: 150,},
				{prop: 'material_name', label: '物料名称',},
				{prop: 'description', label: '物料规格描述',},
				{prop: 'number', label: '数量',},
				{prop: 'supplier_company', label: '供应商',},
				{prop: 'if_lock_stock', label: '锁库',},
				{prop: 'if_bought', label: '是否已采购',},
				{prop: 'if_pushed', label: '是否已入库',},
				{prop: 'if_pulled', label: '是否已出库',},
				{prop: 'supply_close_time', label: '补件单关闭日期', format: 'dataFormat1', width: 150,},
				{prop: 'close_time', label: '行关闭时间', format: 'dataFormat1', width: 150,},
				{prop: 'row_status', label: '行状态',
				formatter: prop => ({
					CREATE: '未关闭',    
					CLOSE: '已关闭',    
				}[prop] || prop),},
				{prop: 'warehouse_no', label: '出库单号',},
				{
					prop:'sale_out_stock_bill_no',
					label:'销售出库单号'
				},
				{prop: 'purchase_warehouse_no', label: '采购入库单号(财务中台回传)',},
				{prop: 'goods_branch_no', label: '商品批号',},
				{prop: 'close_name', label: '行状态操作人员',},
				{prop: 'line_close_type', label: '行状态操作原因',	formatter: prop => ({LOST_ITEM_FOUND:'丢件找到',STOP_PRODUCTION:'停产无法补件',OTHER:'其它',PLACE_WRONG_ORDER:'下错单',NO_NEED_TO_HANDLE:'不需要（协商，赔偿，维修）处理',}[prop] || prop),},
				{prop: 'close_remark', label: '行状态操作备注',},
				{prop: 'if_dealer', label: '是否经销商订单',},
				{prop: 'close_type', label: '关闭类型',
					formatter: prop => ({LOST_ITEM_FOUND:'丢件找到',STOP_PRODUCTION:'停产无法补件',OTHER:'其它',PLACE_WRONG_ORDER:'下错单',NO_NEED_TO_HANDLE:'不需要（协商，赔偿，维修）处理',}[prop] || prop),},
				{prop: 'supply_close_remark', label: '关闭备注',},
			],

			rules: {
				start: this.VLFun(true,'请选择开始时间'),
				end: this.VLFun(true,'请选择结束时间'),
			},
		}
	},
	methods: {
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_SUPPLY_ITEM',
					},
				},
			})
		},
		//对页面需要认证的内容认证，required：是否必填，msg：提示信息
		VLFun(required, msg){
			return VL.isNotBlank({
				required:required,
				self:this,
				msg:msg,
			})
		},
		exportExcel (e){
			var $btn = e.target
			// var postData = Object.assign({}, this.serchData)
			// 	delete postData.start
			// 	delete postData.end
			if(new Date(this.serchData.end).getTime()-new Date(this.serchData.start).getTime()>1000*60*60*24*31){
						this.$message.error("只能查询当前一个月数据");
						return;
					}
					var newWhere = []
					,	keyMapName = {
						status:"行状态",
						start: '审核日期',
						end: '审核日期',
						type:"单据类型",
						material_code:"物料编码",
						supplier_company:"供应商",
						if_dealer:"是否经销订单",	
					}

					this.listFieldsWhere && Object.keys(keyMapName).forEach(key => {
						var whereObj = {
							condition: 'AND',
							listWhere: [],
							operator: '=',
							field: this.listFieldsWhere[keyMapName[key]].field,
							table: this.listFieldsWhere[keyMapName[key]].table,
							value: this.serchData[key],
						}

						if(this.serchData[key]){
							if(/start$/.test(key)){//时间的处理
								whereObj.operator = '>='
							}else if(/end$/.test(key)){//时间的处理
								whereObj.operator = '<='
							}
							newWhere.push(whereObj)
						}
					})

					this.serchData.where = newWhere

					var postData = Object.assign({}, this.serchData)
					delete postData.start;
					delete postData.end;
					delete postData.if_dealer;
					delete postData.material_code;
					delete postData.supplier_company;
					delete postData.type;
					delete postData.status;
			$btn.disabled = true
			this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportSupplyItemExcel', Object.assign({}, postData, {
				page_size: 200000,
				page_no: 1,
			}), res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				$btn.disabled = false
			}, () => {
				$btn.disabled = false
			})
		},
		reset (){
			// var today = (new Date).toLocaleDateString()

			
			this.serchData.start = null
			this.serchData.end = null
			this.serchData.if_dealer = null
			this.serchData.material_code = null
			this.serchData.supplier_company = null
			this.serchData.type = null
			this.serchData.status = null
		},
		search (keyword){
			this.serchData.page_no = 1
			this.serchData.code = keyword
			this._getList()
		},
		pageChange (pageSize){
			this.serchData.page_size = pageSize
			// this.serchData.page_no = 1
			this._getList()
		},
		currentPageChange (page){
			this.serchData.page_no = page
			this._getList()
		},  
		_getList (resolve, msg){
			this.$refs.form.validate(valid => {
				if(valid){
					if(new Date(this.serchData.end).getTime()-new Date(this.serchData.start).getTime()>1000*60*60*24*31){
						this.$message.error("只能查询一个月数据");
						return;
					}
					var newWhere = []
					,	keyMapName = {
						status:"行状态",
						start: '审核日期',
						end: '审核日期',
						type:"单据类型",
						material_code:"物料编码",
						supplier_company:"供应商",
						if_dealer:"是否经销订单",	
					}

					this.listFieldsWhere && Object.keys(keyMapName).forEach(key => {
						var whereObj = {
							condition: 'AND',
							listWhere: [],
							operator: '=',
							field: this.listFieldsWhere[keyMapName[key]].field,
							table: this.listFieldsWhere[keyMapName[key]].table,
							value: this.serchData[key],
						}

						if(this.serchData[key]){
							if(/start$/.test(key)){//时间的处理
								whereObj.operator = '>='
							}else if(/end$/.test(key)){//时间的处理
								whereObj.operator = '<='
							}
							newWhere.push(whereObj)
						}
					})

					this.serchData.where = newWhere

					var postData = Object.assign({}, this.serchData)
					delete postData.start;
					delete postData.end;
					delete postData.if_dealer;
					delete postData.material_code;
					delete postData.supplier_company;
					delete postData.type;
					delete postData.status;

					this.ajax.postStream('/reports-web/api/reports/afterSale/supplyItemExcel', postData, res => {
						if(res.body.result){
							this.$message.success(msg || res.body.msg)
							this.list = res.body.content.list || []
							this.pageTotal = res.body.content.count
						}else {
							this.list = []
							this.pageTotal = 0
							this.$message.error(res.body.msg)
						}
					})
				}

			})
		},
		getListFields (){
			this.ajax.postStream('/user-web/api/sql/listFields', { page: this.serchData.page_name }, res => {
				if(res.body.result){
					this.listFieldsWhere = res.body.content.fields.reduce((a, obj) => {
						if(/(审核日期|单据类型|供应商|物料编码|是否经销订单|行状态)/.test(obj.comment)){
							a[obj.comment] = {
								field: obj.field,
								table: obj.table,
							}
						}
						return a
					}, {})
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
	},
	mounted (){
		this.getListFields()
	},
}
</script>