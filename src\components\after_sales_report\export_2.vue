<!-- EXCEL导入结果列表 -->
<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='primary' size='mini' @click='getList' slot='left' :loading='onRefresh' :disabled='onRefresh'>刷新</el-button>
    </xpt-headbar>
    <xpt-list
      :data='list'
      :colData='cols'
      :btns='btns'
      :pageTotal='count'
      selection=''
      :showHead='false'
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this;
      return {
        list: [],
        btns: [{
          type: 'primary',
          txt: '刷新',
          click: self.getList,
          loading: false
        }],
        cols: [
          {
            label: '操作类型',
            prop: 'program_name'
          },
          {
            label: '状态',
            prop: 'run_status',
            formatter: self.statusExchage
          },
          {
            label: '开始时间',
            prop: 'submit_time',
            format: 'dataFormat1',
            width: '130'
          },
          {
            label: '结束时间',
            prop: 'finish_time',
            format: 'dataFormat1',
            width: '130'
          },
          {
            label: '日志',
            prop: 'result'
          },
          {
            label: '附件',
            prop: 'file_path',
            html(val) {
              return val ? '<a href="'+ val +'">下载</a>' : val;
            }
          }
        ],
        query: {
          list_excel_type: [],
          page_size: self.pageSize,
          page_no: 1
        },
        count: 0,
        test: '',
        testStatus: false,
        onRefresh: false
      }
    },
    methods: {
      getList() {
        this.btns[0].loading = true;
        this.onRefresh = true;

        this.ajax.postStream('/reports-web/api/reports/afterSale/findMyReportList', this.params.query, res => {
          if(res.body.result && res.body.content) {
            this.list = res.body.content.list || [];
            this.count = res.body.content.count || 0;
            console.log(res.body.content.list)
          } else {
            this.$message.error(res.body.msg)
          }
          this.btns[0].loading = false;
          this.onRefresh = false;
        }, error => {
          this.btns[0].loading = false;
          this.onRefresh = false;
          this.$message.error(error.statusText)
        })
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.getList();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.getList();
      },
      statusExchage(val) {
        let keyValues = {
          'EXEC_WAIT':'等待执行',
          'EXEC_RUNING':'执行中',
          'EXEC_FAILED':'执行失败',
          'EXEC_SUCCESS':'执行完成',
        };
        return keyValues[val] || val;
      },
      testClick() {
        this.testStatus = true;
        this.ajax.postStream('/order-web/api/report/job/handle', {job_type: this.test}, res => {
          this.testStatus = false;
          this.$message.info(res.body.msg)
        }, err => {
          this.testStatus = false;
          this.$message.error(err)
        })
      }
    },
    mounted() {
      this.getList();
    }
  }
</script>
