<!-- 费用登记列表  -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='10' class='xpt-top'>
		<el-form :rules='rules' ref='$searchObj' :model='searchObj' label-position="right" label-width="120px">
			<el-col :span='6'>
				<el-form-item label="审核开始时间：" prop="auditDateStart">
					<el-date-picker v-model="searchObj.auditDateStart" type="date" @change="checkDateRequired" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.auditDateStart[0].isShow' class="item" effect="dark" :content="rules.auditDateStart[0].message" placement="right-start" popper-class='xpt-form__error'>
		              <i class='el-icon-warning'></i>
		            </el-tooltip>
				</el-form-item>
				<el-form-item label="状态：" prop="status">
					<el-select v-model="searchObj.status" placeholder="请选择" size="mini">
							<el-option
								v-for="item in status_option"
								:key="item.code"
								:label="item.name"
								:value="item.code">
							</el-option>
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="审核结束时间：" prop="auditDateEnd">
					<el-date-picker v-model="searchObj.auditDateEnd" type="date" @change="checkDateRequired" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.auditDateEnd[0].isShow' class="item" effect="dark" :content="rules.auditDateEnd[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
					
			</el-col>
			<el-col :span='6'>
				<el-form-item label="费用类型：" prop="feeRegisterType">
					<xpt-select-aux v-model='searchObj.feeRegisterType' aux_name='fee_register_type'></xpt-select-aux>
				</el-form-item>
			</el-col>
			<el-col :span="6" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='searchFun'>查询</el-button>
				<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData="colData"
		:pageTotal='pageTotal'
        :showHead="false"
        selection=''
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
	></xpt-list>
</div>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
	props:['params'],
	data (){
		var self = this

		return {
			
			searchObj: {
				auditDateStart: '',
				auditDateEnd: '',
				status:'Y',
				feeRegisterType:'',
				page_size: 50,
				page_no: 1,
			},
			rules: {
	          auditDateStart: [{
	            required:true,
	            message:'请选择开始日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              if(!self.rules[rule.field][0].required){
	                callback();
	                return
	              }

	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	          auditDateEnd: [{
	            required:true,
	            message:'请选择结束日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              if(!self.rules[rule.field][0].required){
	            callback();
	            return
	          }

	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	        },
			listFieldsWhere: null,
			pageTotal: 0,
			status_option:[
				{
					code:'Y',
					name:'生效'
				},{
					code:'N',
					name:'失效'
				},
			],
			roleList:[],
			colData: [{
				label: '合并单号',
				prop: 'mergeTradeNo',
				width: 180,
			}, {
				label: '业务员姓名',
				prop: 'bizName',
				width: 100,
			}, {
				label: '分组',
				prop: 'bizGroup',
				width: 180,
			},{
				label: '商品编码',
				prop: 'materiaNumber',
				width: 180,
			}, {
				label: '批次单号',
				prop: 'batchTradeNo',
			}, {
				label: '费用类型',
				prop: 'registerType',
			}, {
				label: '费用',
				prop: 'fee',
			}, {
				label: '审核时间',
				prop: 'auditTime',
            	format: 'dataFormat1',
			}, {
				label: '原因描述',
				prop: 'reason',
			},],
		}
	},
	methods: {
		reset() {
			for(let v in this.searchObj) {
				if(['page_size','page_no'].includes(v)){
					return;
				}
				this.searchObj[v] = '';
			}
		},
		checkDateRequired (){
	        if(this.searchObj.auditDateStart || this.searchObj.auditDateEnd){
	          this.rules.auditDateEnd[0].required = true
	        }

	        if(!this.searchObj.auditDateStart && !this.searchObj.auditDateEnd){
	          this.rules.auditDateEnd[0].required = false
	        }
		},
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_FEE_REGISTER_REPORT_EXPORT',
					},
				},
			})
		},
		
		exportExcel (){
			this.checkValidate(() => {
				this.ajax.postStream('/reports-web/api/reports/feeRegister/export',this.searchObj, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
					}else {
						this.$message.error(res.body.msg)
					}
				})
			})
		},
		checkValidate (cb){
			this.$refs.$searchObj.validate(valid => {
				if(valid) {
				
					cb()
				}
			})
		},
		searchFun(){
			this.checkValidate(() => {
				let threeMonth = 1000*60*60*24*30*3;
				if(this.searchObj.auditDateEnd <this.searchObj.auditDateStart){
						this.$message.error('开始时间不能大于结束时间')
						return;
					
				}
				if((this.searchObj.auditDateEnd - this.searchObj.auditDateStart)>threeMonth){
						this.$message.error('时间间隔不能超过3个月')
						return;
					
				}
				this.ajax.postStream('/reports-web/api/reports/feeRegister/list', this.searchObj, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
						this.roleList = res.body.content.list || []
						this.pageTotal = res.body.content.count
					}else {
						this.$message.error(res.body.msg)
					}
				}, f => f, this.params.tabName)
			})
		},
		pageChange(page_size){
			this.searchObj.page_size = page_size
			this.searchFun()
		},
		currentPageChange(page){
			this.searchObj.page_no = page
			this.searchFun()
		},
		
		
	},
	mounted (){
		
	},
}
</script>