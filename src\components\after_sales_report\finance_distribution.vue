<!-- 收款分布统计报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="支付开始日期："  label-width="120" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="到账开始日期：" label-width="120" prop='begin_date2'>
            <el-date-picker v-model="query.begin_date2" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date2[0].isShow' class="item" effect="dark" :content="rules.begin_date2[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="支付结束日期：" label-width="120" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="到账结束日期：" label-width="120" prop='end_date2'>
            <el-date-picker v-model="query.end_date2" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date2[0].isShow' class="item" effect="dark" :content="rules.end_date2[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='4'>
          <el-form-item label="渠道：">
            <xpt-input  value="o2o"  disabled  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          begin_date2: '',
          end_date2: ''
        },
        cols: [
          {
            label: '展厅',
            prop: 'original_shop_name',
          }, {
            label: 'pos刷卡',
            prop: 'pay_amount_pos',
          }, {
            label: '占比',
            prop: 'proportion_pos',
          }, {
            label: '手续费',
            prop: 'factorage_pos',
          }, {
            label: '微信',
            prop: 'pay_amount_wechat',
          }, {
            label: '占比',
            prop: 'proportion_wechat',
          }, {
            label: '手续费',
            width: 100,
            prop: 'factorage_wechat',
          }, {
            label: '银行转款',
            prop: 'pay_amount_bank_transfer',
            width: 100,
          }, {
            label: '占比',
            prop: 'proportion_bank_transfer',
          }, {
            label: '手续费',
            prop: 'factorage_bank_transfer',
          }, {
            label: '现金',
            prop: 'pay_amount_cash',
          },{
            label: '占比',
            prop: 'proportion_cash',
          },{
            label: '手续费',
            prop: 'factorage_cash',
          },{
            label: '支付宝',
            prop: 'pay_amount_alipay',
          },{
            label: '占比',
            prop: 'proportion_alipay',
          },{
            label: '手续费',
            prop: 'factorage_alipay',
          },{
            label: '应收金额',
            prop: 'pay_amount_total',
          },{
            label: '实收金额',
            prop: 'paid_amount_total',

          },{
            label: '手续费',
            prop: 'factorage_total',
          },{
            label: '驳回数',
            prop: 'reject_count'
          },{
            label: '作废数',
            prop: 'ch_count'
          },{
            label: '展厅排名',
            prop: 'shop_rank'
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {

      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            var date = new Date(data.end_date);
            date.setHours(23);
            date.setMinutes(59);
            date.setSeconds(59);
            data.end_date =new Date(date);


            var date2 = new Date(data.end_date2);
            date2.setHours(23);
            date2.setMinutes(59);
            date2.setSeconds(59);
            data.end_date2 =new Date(date2);

            data.begin_date = new Date(data.begin_date);
            data.begin_date2 = new Date(data.begin_date2);
            this.queryBtnStatus = true;


            let listPromise = new Promise((resolve, reject) => {
                this.ajax.postStream('/reports-web/api/reports/finance/listReceiptDistribution', data, res => {
                  this.queryBtnStatus = false;
                  if(res.body.result && res.body.content) {
//                    let content = res.body.content.body;
//                    this.list = content.list || [];
//                    this.count = content.count || 0;
                    resolve(res.body.content.body)
                  } else {
                    reject(res.body.msg)
                  }
                }, err => {
                  this.$message.error(err);
                  this.queryBtnStatus = false;
                })
              }),
            totalPromise = this.queryTotal(data);
            listPromise.then(val => {
              this.list = []
              totalPromise.then(total => {
                let list = val.list || [];
                list.push(total);
                this.list = list;
                this.count = val.count || 0;
              })
            }).catch(err => {
              this.errIntercept(err);
              // this.$message.error(err);
            })





          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = new Date(data.begin_date);
            var date = new Date(data.end_date);
            date.setHours(23);
            date.setMinutes(59);
            date.setSeconds(59);
            data.end_date =new Date(date);

            data.begin_date2 = new Date(data.begin_date2);
            var date2 = new Date(data.end_date2);
            date2.setHours(23);
            date2.setMinutes(59);
            date2.setSeconds(59);
            data.end_date2 =new Date(date2);
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportReceiptDistribution', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      // 查询汇总
      queryTotal(data) {
        return new Promise(resolve => {
          this.ajax.postStream('/reports-web/api/reports/finance/analysisReceiptDistribution', data, res => {
            this.queryBtnStatus = false;
            if(res.body.result && res.body.content) {
              let content = res.body.content;

              resolve({
                pay_amount_pos: '合计:' + content.hj_pay_amount_pos,
                pay_amount_alipay: '合计:' + content.hj_pay_amount_alipay,
                pay_amount_wechat: '合计:' + content.hj_pay_amount_wechat,
                pay_amount_bank_transfer: '合计:' + content.hj_pay_amount_bank_transfer,
                pay_amount_cash: '合计:' + content.hj_pay_amount_cash,
                factorage_pos: '合计:' + content.hj_factorage_pos,
                factorage_alipay: '合计:' + content.hj_factorage_alipay,
                factorage_wechat: '合计:' + content.hj_factorage_wechat,
                factorage_bank_transfer: '合计:' + content.hj_factorage_bank_transfer,
                factorage_cash: '合计:' + content.hj_factorage_cash,
                proportion_pos: '合计:' + content.hj_proportion_pos,
                proportion_alipay: '合计:' + content.hj_proportion_alipay,
                proportion_wechat: '合计:' + content.hj_proportion_wechat,
                proportion_bank_transfer: '合计:' + content.hj_proportion_bank_transfer,
                proportion_cash: '合计:' + content.hj_proportion_cash,
                factorage_total: '合计:' + content.hj_factorage_total,
                paid_amount_total: '合计:' + content.hj_paid_amount_total,
                pay_amount_total: '合计:' + content.hj_pay_amount_total,
              })

            }
          }, err => {
            this.$message.error(err);
            this.queryBtnStatus = false;
          })
        })


      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
