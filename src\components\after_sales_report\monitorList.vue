<!-- 退货跟踪单异常数据监控报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="推送开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="接口状态：">
            <el-select v-model='query.interfaceStatus' size='mini'>
              <el-option label='处理失败' :value='-1'></el-option>
              <el-option label='处理中' :value='0'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="推送结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="退货跟踪单号：">
            <xpt-input v-model='query.bill_no' size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br/>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_AFTERSALE_RETURN_MONITOR_REPORT")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>


    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
      @count-off="countOff"
      :show-count="showCount"
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          bill_no: '',
          begin_date: '',
          end_date: '',
          interfaceStatus: -1,
        },
        cols: [
          {
            label: '退货跟踪单号',
            prop: 'bill_no',
          }, {
            label: '下游单据类型',
            prop: 'push_bill_type'
          },  {
            label: '方式',
            prop: 'business_type',
          }, {
            label: '接口状态',
            prop: 'interface_status'
          }, {
            label: '接口提示信息',
            prop: 'interface_respone_errormsg'
          }, {
            label: '生成时间',
            prop: 'create_time',
            format:'dataFormat1'
          }
        ],
        showCount: false,
        countOffFlag: false,
        search: {},
      }
    },
    methods: {
      reset() {
			  for(let v in this.query) {
				  if(!(v === 'page_size' || v === 'page_no')) {
					  this.query[v] = '';
				  }
			  }
        this.query.interfaceStatus = -1;
		  },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.beginDate = +new Date(data.begin_date);
            data.endDate = +new Date(data.end_date) + 1000 * 60 * 60 * 24 - 1000;
            delete data.begin_date;
            delete data.end_date;
            this.search = data;

            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/aftersale/return/monitorList', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                if(!this.showCount){
						      this.count = content.count == (data.page_size+1)? (data.page_no*data.page_size)+1:(data.page_no*data.page_size);
					      }
                this.showCount = false;
              } else if(res.body.msg) {
                this.$message.error(res.body.msg)
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },

      countOff(){
        let self = this,
        url = "/reports-web/api/reports/aftersale/return/count";
        if(!self.list.length){
          self.$message.error("当前列表为空，先搜索内容");
          return;
        }
        if(!!self.countOffFlag){
          self.$message.error("请勿重复点击");
          return;
        }
        self.countOffFlag = true;

        self.ajax.postStream(url,this.search,function(response){
          if(response.body.result){
            self.count = response.body.content.count;
            self.showCount = true;
            self.countOffFlag = false;
          }else{
            self.$message.error(response.body.msg);
          }
        });
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.beginDate = +new Date(data.begin_date);
            data.endDate = +new Date(data.end_date) + 1000 * 60 * 60 * 24 - 1000;
            delete data.begin_date;
            delete data.end_date;

            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/aftersale/return/excelExport', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },

    mounted(){
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
