<!-- 订单售后跟进报表 -->
<template>
  <div class="xpt-flex">
    <el-row :gutter="10" class="xpt-top">
      <el-form
        :rules="rules"
        :model="query"
        ref="query"
        label-position="right"
        label-width="120px"
      >
        <el-col :span="5">
          <el-form-item label="开始日期：" prop="begin_date">
            <el-date-picker
              v-model="query.begin_date"
              type="date"
              placeholder="选择日期"
              :picker-options="beginDateOptions1"
              size="mini"
              :editable="false"
            ></el-date-picker>
            <el-tooltip
              v-if="rules.begin_date[0].isShow"
              class="item"
              effect="dark"
              :content="rules.begin_date[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="业务锁定人：">
            <xpt-input
              v-model="query.locker"
              icon="search"
              :on-icon-click="openStaffForLock"
              size="mini"
              @change="staffChangeForLock"
            ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="结束日期：" prop="end_date">
            <el-date-picker
              v-model="query.end_date"
              type="date"
              placeholder="选择日期"
              :picker-options="endDateOptions1"
              size="mini"
              :editable="false"
            ></el-date-picker>
            <el-tooltip
              v-if="rules.end_date[0].isShow"
              class="item"
              effect="dark"
              :content="rules.end_date[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="业务锁定人分组：">
            <xpt-input
              v-model="query.locker_group"
              icon="search"
              :on-icon-click="openGroup"
              size="mini"
              @change="groupChange"
            ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="业务员：">
            <xpt-input
              v-model="query.staff"
              icon="search"
              :on-icon-click="openStaff"
              size="mini"
              @change="staffChange"
            ></xpt-input>
          </el-form-item>
          <el-form-item label="合并单售后跟进情况">
            <el-select
              size="mini"
              v-model="query.order_status"
              placeholder="请选择"
            >
              <el-option value="FINISHED" label="已完结"></el-option>
              <el-option value="UNFINISHED" label="未完结"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="9" class="xpt-align__right">
          <el-button
            type="success"
            size="mini"
            @click="queryData"
            :disabled="queryBtnStatus"
            :loading="queryBtnStatus"
            >查询</el-button
          >
          <el-button type="primary" size="mini" @click="reset"
            >重置查询条件</el-button
          ><br />
          <el-button
            type="info"
            size="mini"
            @click="exportExcel"
            :disabled="exportBtnStatus"
            :loading="exportBtnStatus"
            >导出</el-button
          >
          <el-button type="info" size="mini" @click="showExportList"
            >报表导出文件下载</el-button
          >
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :data="list"
      :colData="cols"
      :showHead="false"
      selection=""
      :pageTotal="count"
      @page-size-change="pageSizeChange"
      @current-page-change="currentPageChange"
      :ifExpandTable="true"
      :class="$style['xpt-list-expand']"
    >
      <template slot="expandTableContent" slot-scope="{ expandTableContent }">
        <el-table
          :data="expandTableContent.itemVOList"
          border
          style="width: 100%"
          :class="$style['expand-table']"
          :row-class-name="tableRowClassName"
        >
          <el-table-column prop="aftersale_order_no" label="售后单号">
          </el-table-column>
          <el-table-column
            prop="aftersale_status"
            label="业务状态"
            :formatter="aftersaleStatusFormat"
          >
          </el-table-column>
          <el-table-column prop="buyer_nick" label="买家昵称">
          </el-table-column>
          <el-table-column prop="staff_name" label="业务员"> </el-table-column>
          <el-table-column prop="locker_name" label="业务锁定人">
          </el-table-column>
          <el-table-column prop="locker_group_name" label="业务锁定人分组">
          </el-table-column>
          <el-table-column
            prop="create_time"
            label="创建日期"
            width="140"
            :formatter="dateFormat"
          >
          </el-table-column>
          <el-table-column
            prop="finished_time"
            label="完结日期"
            width="140"
            :formatter="dateFormat"
          >
          </el-table-column>
        </el-table>
      </template>
    </xpt-list>
  </div>
</template>
<script>
import mixin from "./mixin.js";
import Fn from "@common/Fn.js";
export default {
  props: ["params"],
  mixins: [mixin],
  data() {
    let self = this;
    return {
      headInfo: {}, //头信息
      query: {
        page_no: 1,
        page_size: self.pageSize,
        begin_date: "", //开始时间
        end_date: "", //结束时间
        staff_id: "",
        locker_id: "",
        locker_group_id: "",
        order_status: "",

        staff: "",
        locker: "",
        locker_group: "",
      },
      cols: [
        {
          label: "合并订单号",
          prop: "merge_trade_no",
        },
        {
          label: "售后跟进情况",
          prop: "aftersale_status",
          formatter: (val) => {
            return (
              {
                UNFINISHED: "未完结",
                FINISHED: "完结",
              }[val] || val
            );
          },
        },
        {
          label: "买家昵称",
          prop: "buyer_nick",
        },
        {
          label: "业务员",
          prop: "staff_name",
        },
        {
          label: "业务锁定人",
          prop: "locker_name",
        },
        {
          label: "业务锁定人分组",
          prop: "locker_group_name",
        },
        {
          label: "首次售后提交时间",
          prop: "first_submit_time",
          format: "dataFormat1",
          width: 140,
        },
        {
          label: "最近售后提交时间",
          prop: "last_submit_time",
          format: "dataFormat1",
          width: 140,
        },
      ],
      count: 0,
      list: [],
      beginDateOptions1: self.beginDateSet(),
      endDateOptions1: self.endDateSet(),
    };
  },
  methods: {
    formatParams() {
      let query = this.query;
      let obj = {
        page_no: query.page_no,
        page_size: query.page_size,
        begin_date: +new Date(query.begin_date), //开始时间
        end_date: +(new Date(query.end_date).setHours(23,59,59)), //结束时间
        staff_id: query.staff_id, //业务员ID
        locker_id: query.locker_id, //业务锁定人ID
        locker_group_id: query.locker_group_id, //业务锁定人分组ID
        order_status: query.order_status, //售后单跟进情况
      };
      return obj;
    },
    queryData() {
      let self = this;
      this.$refs.query.validate((valid) => {
        if (valid) {
          let params = self.formatParams();
          this.ajax.postStream(
            "/reports-web/api/reports/afterSale/aftersaleFinishedRateReport",
            params,
            (res) => {
              if (res.body.result) {
                self.$message.success(res.body.msg);
                self.list = res.body.content.list || [];
                self.count = res.body.content.count || 0;
              }
            },
            (err) => {
              self.$message.error(err);
            }
          );
        } else {
          this.$message.error("请填写日期");
        }
      });
    },
    reset() {
      let obj = {
        page_no: 1,
        page_size: this.pageSize,
        begin_date: "",
        end_date: "",
        staff_id: "",
        locker_group_id: "",
        order_status: "",

        staff: "",
        locker: "",
        locker_group: "",
      };
      this.query = obj;
    },
    openStaff(){
      let self = this;
      let params = {
        callback(data) {
            self.query.staff_id = data.userId;
            self.query.staff = data.fullName;
        },
      };
      this.$root.eventHandle.$emit("alert", {
        params: params,
        component: () =>
          import("@components/after_sales_report/select_personel"),
        style: "width:800px;height:500px",
        title: "人员列表",
      });
    },
    staffChange(val) {
			if(!val) {
				this.query.staff_id = '';
				this.query.staff = '';
			}
		},
    openStaffForLock() {
      let self = this;
      let params = {
        callback(data) {
            self.query.locker_id = data.userId;
            self.query.locker = data.fullName;
        },
      };
      this.$root.eventHandle.$emit("alert", {
        params: params,
        component: () =>
          import("@components/after_sales_report/select_personel"),
        style: "width:800px;height:500px",
        title: "人员列表",
      });
    },
    staffChangeForLock(val) {
			if(!val) {
				this.query.locker_id = '';
				this.query.locker = '';
			}
		},
    // 选择分组
    openGroup() {
      let self = this;
      let params = {
        callback(data) {
          self.query.locker_group_id = data.id;
          self.query.locker_group = data.name;
        },
        level: "SUB_TEAM",
      };
      this.$root.eventHandle.$emit("alert", {
        params: params,
        component: () => import("@components/per_sales_report/select_group"),
        style: "width:800px;height:500px",
        title: "分组列表",
      });
    },
    // 分组数据变动
    groupChange(val) {
      if (!val) {
        this.query.locker_group_id = "";
        this.query.locker_group = "";
      }
    },
    exportExcel() {
      let self = this;
      this.$refs.query.validate((valid) => {
        if (valid) {
          self.exportBtnStatus = true;
          let params = self.formatParams();
          self.ajax.postStream(
            "/reports-web/api/reports/afterSale/exportAftersaleFinishedRateReport",
            params,
            (res) => {
              if (res.body.result) {
                res.body.msg && self.$message.success(res.body.msg);
              } else {
                res.body.msg && self.$message.error(res.body.msg);
              }
              self.exportBtnStatus = false;
            },
            (err) => {
              self.exportBtnStatus = false;
              this.$message.error(err);
            }
          );
        }
      });
    },
    showExportList() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "报表导出列表",
        params: {
          query: {
            type: "EXCEL_TYPE_REPORT_AFTERSALE_FINISHED_RATED",
          },
        },
      });
    },
    tableRowClassName() {
      return "expand-table-row";
    },
    aftersaleStatusFormat(row) {
      return (
        {
          CREATE: "待办",
          PRE_HANDLE: "售前处理",
          WAIT_HANDLE: "售后待办",
          AFTER_HANDLE: "售后处理",
          FINISH: "完结",
        }[row.aftersale_status] || row.aftersale_status
      );
    },
    dateFormat(row, column) {
      let date = row[column.property];
      if (date == undefined) {
        return "";
      }
      return Fn.dateFormat(date, "yyyy-MM-dd hh:mm:ss");
    },
    beginDateSet() {
      let self = this;
      console.log(self);
      return {
        disabledDate(time) {
          if (self.query.end_date) {
            return (
              time.getTime() > new Date(self.query.end_date).getTime() ||
              time.getTime() <
                new Date(self.query.end_date).getTime() -
                  30 * 24 * 60 * 60 * 1000
            );
          } else {
            //   return time.getTime() < new Date("2019/01/01 00:00:00").getTime()
            //   return time.getTime() < new Date("2019/01/01 00:00:00").getTime()||time.getTime()>new Date("2019/12/31 00:00:00").getTime();
          }
        },
      };
    },
    endDateSet() {
      let self = this;
      console.log(self);
      return {
        disabledDate(time) {
          if (self.query.begin_date) {
            return (
              time.getTime() < new Date(self.query.begin_date).getTime() ||
              time.getTime() >
                new Date(self.query.begin_date).getTime() +
                  30 * 24 * 60 * 60 * 1000
            );
          } else {
            //   return time.getTime() < new Date("2019/01/01 00:00:00").getTime()
          }
        },
      };
    },
  },
  mounted() {
    // this.queryData();
  },
  computed: {},
  watch: {},
};
</script>
<style lang="stylus" scoped>
.el-form {
  white-space: nowrap;
}
</style>
<style module>
.expand-table :global(.el-table__body-wrapper) {
  overflow: hidden !important;
}
.xpt-list-expand :global(.el-table__expanded-cell) {
  padding: 0 0 0 50px !important;
}
</style>
