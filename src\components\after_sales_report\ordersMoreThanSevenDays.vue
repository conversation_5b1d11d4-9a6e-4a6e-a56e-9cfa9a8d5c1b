<!-- 超7天滞留收货订单报表  -->
<template>
  <div class="xpt-flex">
    <el-row :gutter="10" class="xpt-top">
      <el-form
        ref="searchObjRef"
        :model="searchObj"
        label-position="right"
        label-width="120px"
      >
        <el-col :span="5">
          <el-form-item label="发货时间：">
            <el-date-picker
              v-model="shipped_time"
              type="daterange"
              placeholder="选择时间"
              :clearable="true"
              size="mini"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="售后锁定人：" prop="after_locker_name">
            <el-input
              v-model="searchObj.after_locker_name"
              size="mini"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="市：" prop="city">
            <el-input v-model="searchObj.city" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="售后锁定人分组：" prop="after_locker_group_name">
            <el-input
              v-model="searchObj.after_locker_group_name"
              size="mini"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="业务员：" prop="batch_user_name">
            <el-input
              v-model="searchObj.batch_user_name"
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="批次单号：" prop="batch_trade_no">
            <el-input v-model="searchObj.batch_trade_no" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="业务员分组：" prop="batch_group_name">
            <el-input
              v-model="searchObj.batch_group_name"
              size="mini"
            ></el-input>
          </el-form-item>
          <el-form-item label="买家昵称：" prop="buyer_nick_name">
            <el-input
              v-model="searchObj.buyer_nick_name"
              size="mini"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4" class="xpt-align__right">
          <el-button type="success" size="mini" @click="searchFun"
            >查询</el-button
          >
          <el-button type="primary" size="mini" @click="reset"
            >重置查询条件</el-button
          ><br />
          <el-button type="info" size="mini" @click="exportExcel"
            >导出</el-button
          >
          <el-button type="info" size="mini" @click="showExportList"
            >报表导出文件下载</el-button
          >
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :data="roleList"
      :colData="colData"
      :pageTotal="pageTotal"
      :showHead="false"
      selection=""
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
    ></xpt-list>
  </div>
</template>
  <script>
import Fn from "@common/Fn.js";
export default {
  props: ["params"],
  data() {
    var self = this;

    return {
      searchObj: {
        buyer_nick_name: "",
        batch_user_name: "",
        batch_group_name: "",
        shipped_time_start: "",
        shipped_time_end: "",
        after_locker_name: "",
        after_locker_group_name: "",
        batch_trade_no: "",
        city: "",
        page_no: 1,
        page_size: 50,
      },

      pageTotal: 0,
      shipped_time: "",

      roleList: [],
      colData: [
        {
          label: "批次单号",
          prop: "batch_trade_no",
          align: "center",
          width: 200,
        },
        {
          label: "业务状态",
          align: "center",
          prop: "business_status",
        },
        {
          label: "买家昵称",
          align: "center",
          prop: "buyer_nick_name",
        },
        {
          label: "业务员",
          align: "center",
          prop: "batch_user_name",
        },
        {
          label: "业务员分组",
          align: "center",
          prop: "batch_group_name",
          width: 100,
        },
        {
          label: "售后锁定人",
          align: "center",
          prop: "after_locker_name",
        },
        {
          label: "售后锁定人分组",
          align: "center",
          prop: "after_locker_group_name",
          width: 100,
        },
        {
          label: "收货人",
          align: "center",
          prop: "receiver",
        },
        {
          label: "手机号码",
          align: "center",
          prop: "receiver_mobile",
          format: "hidePhoneNumber",
        },
        {
          label: "线路区域",
          align: "center",
          prop: "line_area_name",
        },
        {
          label: "市",
          align: "center",
          prop: "city",
        },
        {
          label: "区",
          align: "center",
          prop: "district",
        },
        {
          label: "总体积数",
          align: "center",
          prop: "total_volume",
        },
        {
          label: "发货时间",
          align: "center",
          prop: "shipped_time",
          format: "dataFormat1",
          width: 110,
        },
        {
          label: "首次预约操作时间",
          prop: "called_time",
          align: "center",
          format: "dataFormat1",
          width: 110,
        },
        {
          label: "原始店铺",
          align: "center",
          prop: "original_shop_name",
        },
        {
          label: "区域经理",
          align: "center",
          prop: "supervision",
        },
        {
          label: "支装类型",
          align: "center",
          prop: "install_type_name",
        },
        {
          label: "滞留原因",
          align: "center",
          prop: "delay_reason",
        },
        {
          label: "滞留类型",
          align: "center",
          prop: "delay_type",
        },
      ],
    };
  },
  methods: {
    viewBatchInfoDetail(row) {
      var params = {};
      params.batch_trade_id = row.batch_trade_id;
      params.merge_trade_id = row.merge_trade_id;
      this.$root.eventHandle.$emit("creatTab", {
        name: "批次订单详情",
        params,
        component: () => import("@components/order/batch_order.vue"),
      });
    },

    reset() {
      for (let v in this.searchObj) {
        if (["page_size", "page_no"].includes(v)) {
          continue;
        }
        this.searchObj[v] = "";
      }
    },

    showExportList() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "报表导出列表",
        params: {
          query: {
            type: "EXCEL_TYPE_REPORT_AFTERSALE_DELAY_ARRIVAL",
          },
        },
      });
    },

    exportExcel() {
      let data = JSON.parse(JSON.stringify(this.searchObj));
      delete data.staff_name;
      delete data.staff_group_name;
      if (!!this.shipped_time[0]) {
        data.shipped_time_start = this.getTodayStartTime(this.shipped_time[0]);
        data.shipped_time_end = this.getTodayEndTime(this.shipped_time[1]);
        if (data.shipped_time_end - data.shipped_time_start > 2678399000) {
          this.$message.warning("时间查询范围不能大于31天");
          return;
        }
      }
      this.ajax.postStream(
        "/reports-web/api/reports/aftersale/delayArrival/excelExport",
        data,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
          } else {
            this.$message.error(res.body.msg);
          }
        }
      );
    },
    searchFun() {
      let data = JSON.parse(JSON.stringify(this.searchObj));
      delete data.staff_name;
      delete data.staff_group_name;
      if (!!this.shipped_time[0]) {
        data.shipped_time_start = this.getTodayStartTime(this.shipped_time[0]);
        data.shipped_time_end = this.getTodayEndTime(this.shipped_time[1]);
        if (data.shipped_time_end - data.shipped_time_start > 2678399000) {
          this.$message.warning("时间查询范围不能大于31天");
          return;
        }
      }

      this.ajax.postStream(
        "/reports-web/api/reports/aftersale/delayArrival/excelList",
        data,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            this.roleList = res.body.content.list || [];
            this.pageTotal = res.body.content.count;
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (f) => f,
        this.params.tabName
      );
    },
    pageChange(page_size) {
      this.searchObj.page_size = page_size;
      this.searchFun();
    },
    currentPageChange(page) {
      this.searchObj.page_no = page;
      this.searchFun();
    },
    getTodayStartTime(val) {
      let date = new Date(val);
      let y = date.getFullYear();
      let m = date.getMonth() + 1;
      let d = date.getDate();
      return new Date(`${y}-${m}-${d} 00:00:00`).getTime();
    },
    getTodayEndTime(val) {
      let date = "";
      if (!val) {
        date = new Date();
      } else {
        date = new Date(val);
      }
      let y = date.getFullYear();
      let m = date.getMonth() + 1;
      let d = date.getDate();
      return new Date(`${y}-${m}-${d} 23:59:59`).getTime();
    },
  },
  mounted() {
    this.shipped_time = [
      this.getTodayStartTime(+Date.now() - 2592000000),
      this.getTodayEndTime(),
    ];
    this.searchFun();
  },
};
</script>