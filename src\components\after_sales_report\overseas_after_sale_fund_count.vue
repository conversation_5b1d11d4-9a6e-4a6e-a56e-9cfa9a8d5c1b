<!-- 海外售后基金统计报表 -->
<template>
	<div class='xpt-flex'>
		<el-row :gutter='10' class='xpt-top'>
			<el-form :rules='rules' :model='query' ref='query' label-position="right" label-width="120px">
				<el-col :span="5">
					<el-form-item label="年月：" prop='select_date'>
						<el-date-picker v-model="query.select_date" type="month" placeholder="选择月份" size='mini' :editable='false'></el-date-picker>
						<el-tooltip v-if='rules.select_date[0].isShow' class="item" effect="dark" :content="rules.select_date[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
                    <el-form-item label="币别：" prop='currency_code'>
						<el-select v-model="query.currency_code" placeholder="选择币别" size='mini'>
                            <el-option
                                v-for="item in currencyOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
						<el-tooltip v-if='rules.currency_code[0].isShow' class="item" effect="dark" :content="rules.currency_code[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
				</el-col>
                <el-col :span="5">
                     <el-form-item label="基金额度：">
                        <el-input v-model="headInfo.foundation_amount"  size='mini' readonly></el-input>
                    </el-form-item>
                    <el-form-item label="已用基金：">
                        <el-input v-model="headInfo.used_foundation_amount"  size='mini' readonly></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="5">
                    <el-form-item label="剩余基金：">
                        <el-input v-model="headInfo.remainder_foundation_amount"  size='mini' readonly></el-input>
                    </el-form-item>
                </el-col>
				<el-col :span="9" class='xpt-align__right'>
					<el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
					<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
					<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
					<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
				</el-col>
			</el-form>
		</el-row>
		<xpt-list 
			:data='list'
			:colData='cols' 
			:showHead='false' 
            selection=''
			:pageTotal='count' 
			@page-size-change='pageSizeChange' 
			@current-page-change='currentPageChange'
		></xpt-list>
	</div>
</template>
<script>
import mixin from './mixin.js'
import validate from "@common/validate.js";
export default {
	props: ['params'],
	mixins: [mixin],
	data() {
		let self = this;
		return {
            headInfo:{},//头信息
			query: {
				select_date: '',
                currency_code:'CNY',//币别
				page_no: 1,
				page_size: self.pageSize,
			}, 
			cols: [
				{
					label: '合并单号',
					prop: 'merge_trade_no'
				},{
					label: '业务员',
					prop: 'staff'
				}, {
					label: '业务员分组',
					prop: 'staff_group'
				}, {
					label: '已使用基金',
					prop: 'used_foundation_amount'
				}
			],
            rules: {
                currency_code: validate.isNotBlank({
                    self: self,
                    msg: "币别不能为空",
                }),
            },
            currencyOptions: [{
                value: 'CNY',
                label: '人民币'
            },{
                value: 'USD',
                label: '美元'
            }]
		}
	},
	methods: {
		queryData() {
			this.$refs.query.validate((valid) => {
				if(valid) {
					let data = JSON.parse(JSON.stringify(this.query));
					data.select_date = +new Date(data.select_date);
					let listP = new Promise((resolve, reject) => {
						this.ajax.postStream('/reports-web/api/report/foundation/listOverseasAftersaleFoundation', data, res => {
							if(res.body.result && res.body.content) {
								resolve(res.body);
							} else {
								reject(res.body.msg)
							}
						}, err => {
							reject(err)
						})
                    });
                    let headP = new Promise((resolve,reject)=>{
                        this.ajax.postStream('/reports-web/api/report/foundation/findOverseasAftersaleFoundationGroupList',{currency_code:this.query.currency_code},res=>{
                            if(res.body.result && res.body.content) {
								resolve(res.body);
							} else {
								reject(res.body.msg)
							}
                        },err=>{
                            reject(err)
                        })
                    })
					this.queryBtnStatus = true;
					Promise.all([listP,headP]).then(vals => {
						this.queryBtnStatus = false;
						let list = vals[0].content.list;
                        this.count = vals[0].content.count;
						if (!!vals[0].content.amount) {
							list.push({
								// foundation_amount: '总计：' + Number(vals[0].content.amount.foundation_amount).toFixed(2),
								// remainder_foundation_amount: '总计：' + Number(vals[0].content.amount.remainder_foundation_amount).toFixed(2),
								used_foundation_amount: '总计：' + Number(vals[0].content.amount.used_foundation_amount).toFixed(2)
							})
						}
                        this.list = list;
                        this.headInfo=vals[1].content;
                        // if(vals[1].msg){
                        //     this.$message.success(vals[1].msg);
                        // }
                        if(vals[0].msg){
                            this.$message.success(vals[0].msg);
                        }
					}).catch(err=>{
                        this.queryBtnStatus = false;
                        this.$message.error(err)
                    })
				}else{
                    this.$message.error('请填写月份和币别')
                }
			})
        },
        reset(){
            this.query.select_date=''
            this.query.currency_code=''
        },
		exportExcel() {
			this.$refs.query.validate((valid) => {
				if(valid) {
					let data = JSON.parse(JSON.stringify(this.query));
					data.select_date = +new Date(data.select_date);
					this.exportBtnStatus = true;
					this.ajax.postStream('/reports-web/api/report/foundation/exportOverseasAftersaleFoundation', data, res => {
						this.$message({
							type: res.body.result ? 'success' : 'error',
							message: res.body.msg
						});
						this.exportBtnStatus = false
					}, err => {
						this.$message.error(err);
						this.exportBtnStatus = false;
					});
				}
			})
		},
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
					type: 'EXCEL_TYPE_REPORT_OVERSEAS_AFTERSALE_FOUNDATION',
					},
				},
			})
		},
	},
	computed: {},
	watch: {}
}
</script>
<style type="text/css" scoped>
.el-form{
    white-space: nowrap;
}
</style>
