<!-- 售后成本汇总报表 -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='10' class='xpt-top'>
		<el-form :rules='rules' :model='serchData' ref="form" label-position="right" label-width="120px">
			<el-col :span='6'>
				<el-form-item label="开始日期：" label-width="120px;" prop='begin_date'>
					<el-date-picker v-model="serchData.begin_date" type="date" placeholder="选择时间" :clearable="false" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="结束日期：" label-width="120px;" prop='end_date'>
					<el-date-picker v-model="serchData.end_date" type="date" placeholder="选择时间" :clearable="false" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="经销商名称：">
					<el-input
						v-if="dealerCustomerDisabled"
						v-model="serchData.dealer_customer_name"
						size='mini'
						disabled
					></el-input>
					<el-input
						v-else
						v-model="serchData.dealer_customer_name"
						size='mini'
						icon="search"
						readonly
						:on-icon-click="getDealerCustomerList"
					></el-input>
				</el-form-item>
			</el-col>
			<el-col :span="6" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='_getList'>查询</el-button>
				<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>
	<xpt-list
        :data='list'
        :colData='cols'
        :pageTotal='pageTotal'
        :showHead="false"
        selection=''
        @page-size-change='pageChange'
        @current-page-change='currentPageChange'
    ></xpt-list>
</div>
</template>

<script>
import VL from '@common/validate.js'
export default {
	data (){
		return {
			serchData: {
				page_size: 50,
				page_no: 1,
				begin_date: null,
				end_date: null,
				dealer_customer_name: '',
				dealer_customer_id: null,
			},
			pageTotal: 0,
			list: [],
			dealerCustomerDisabled: true,

			cols: [
				{prop: 'dealer_name', label: '经销商名称',},
				{prop: 'dealer_shop_name', label: '经销店铺',},
				{prop: 'dealer_return_amount', label: '经销物流退货总货值',},
				{prop: 'dealer_pick_amount', label: '经销自提总货值',},
				{prop: 'delay_refund_amount', label: '延迟赔付退款',},
				{prop: 'compensation_refund_amount', label: '补偿退款',},
				{prop: 'repair_refund_amount', label: '维修费退款',},
				{prop: 'supplement_purchasing_amount', label: '补件采购成本',},
				{prop: 'supplement_outbound_amount', label: '补件出库运费',},
				{prop: 'service_installation_amount', label: '上门安装服务费',},
				{prop: 'service_pickup_amount', label: '上门拉货服务费',},
				{prop: 'service_repair_amount', label: '上门维修服务费',},
				{prop: 'service_debugging_amount', label: '上门调试服务费',},
				{prop: 'sum_amount', label: '金额汇总',},
			],

			rules: {
				begin_date: this.VLFun(true,'请选择开始时间'),
				end_date: this.VLFun(true,'请选择结束时间'),
			},
		}
	},
	methods: {
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_REPORT_COST_SUMMARY',
					},
				},
			})
		},
		//对页面需要认证的内容认证，required：是否必填，msg：提示信息
		VLFun(required, msg){
			return VL.isNotBlank({
				required:required,
				self:this,
				msg:msg,
			})
		},
		exportExcel (e){
			var $btn = e.target

			$btn.disabled = true
			this.ajax.postStream('/reports-web/api/reports/afterSale/exportCostsummary', Object.assign({}, this.serchData, {
				dealer_customer_name: this.serchData.dealer_customer_name,
				page_size: 200000,
				page_no: 1,
			}), res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				$btn.disabled = false
			}, () => {
				$btn.disabled = false
			})
		},
		reset (){
			this.serchData.begin_date = null
			this.serchData.end_date = null
			this.serchData.dealer_customer_name = null
			this.serchData.dealer_customer_id = null
			this.serchData.page_size = 50
			this.serchData.page_no = 1
		},
		pageChange (pageSize){
			this.serchData.page_size = pageSize
			// this.serchData.page_no = 1
			this._getList()
		},
		currentPageChange (page){
			this.serchData.page_no = page
			this._getList()
		},  
		_getList (resolve, msg){
			this.$refs.form.validate(valid => {
				if(valid){

					var postData = JSON.parse(JSON.stringify(this.serchData))
					delete postData.dealer_customer_name

					this.ajax.postStream('/reports-web/api/reports/afterSale/pageCostsummary', postData, res => {
						if(res.body.result){
							this.$message.success(msg || res.body.msg)
							this.list = res.body.content.list || []
							this.pageTotal = res.body.content.count
						}else {
							this.list = []
							this.pageTotal = 0
							this.$message.error(res.body.msg)
						}
					})
				}

			})
		},
		getDealerCustomerList (){
			this.$root.eventHandle.$emit('alert',{
				title: '选择经销商',
				style: 'width:1000px;height:600px',
				component: ()=>import('@components/after_sales_report/afterSalePurchaseSelectDealerCustomer'),
				params: {
					callback: d => {						
						this.serchData.dealer_customer_name = d.customer_source_name
		              	this.serchData.dealer_customer_id = d.customer_source_id
					}
				},
			})
		},
		//   1)当用户登录账号为采购经销账号时，置灰该选择框，值默认为该账号下的经销商名称
		//   2当用户登录账号为不为采购经销账号时，不置灰该选择框，用户可自行选择经销商
		getDefaultDealerCustomer (){
			this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttributeList', {
		        "page": {
		          "length": 50,
		          "pageNo": 1
		        },
		        "personId": this.getEmployeeInfo('personId'),
		        "key": "",
		        "salesmanType": "GLOBAL"
		    }, res => {
		        if(res.body.result){
		          ;(res.body.content.list || []).some(o => {
		            if(o.salesmanType === 'GLOBAL' && o.attribute === 'CUSTOMER'){
		            	this.serchData.dealer_customer_name = o.attributeValueText
		              	this.serchData.dealer_customer_id = o.attributeValue
		              	return true
		            }
		          })
		        }
		    })
		},
		checkUserType (){
			this.ajax.get('/user-web/api/userPerson/getUserPerson/' + this.getEmployeeInfo('personId'), res => {
				if(res.body.result){
					if(res.body.content.type === 'PURCHASE_DEALER'){
						this.getDefaultDealerCustomer()
					}else {
						this.dealerCustomerDisabled = false
					}
				}
			})
		},
	},
	mounted (){
		this.checkUserType()
	},
}
</script>