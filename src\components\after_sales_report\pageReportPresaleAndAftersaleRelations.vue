<!-- 售前售后关系报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="人员工号：">
            <xpt-input v-model='query.employee_number' size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="真实姓名：">
            <xpt-input v-model='query.real_name' size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="是否离职：">
            <el-checkbox v-model="query.if_leave" :true-label="0" :false-label="1"></el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="昵称：">
            <xpt-input v-model='query.nick_name' size='mini'></xpt-input>
          </el-form-item>
          <el-form-item label="客服分组：">
            <xpt-input v-model='query.group_name' size='mini'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="业务属性：">
            <el-select v-model="query.attribute" clearable placeholder="请选择" size='mini'>
              <el-option
                v-for="(val, key) in attribute_options"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="业务类型：">
            <xpt-input v-model='query.salesman_type' size='mini'></xpt-input>
          </el-form-item> -->
          </el-form-item>
          <el-form-item label="业务属性值：">
            <xpt-input v-model='query.attribute_value' size='mini'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_PRESALE_AFTERSALE_RELATIONS")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          employee_number: '',
          real_name: '',
          nick_name: '',
          group_name: '',
          salesman_type: '',
          if_leave: '',
          attribute_value: '',
          attribute: '',
        },

        attribute_options: {
          CUSTOMER_SERVICE_EXECUTIVE: '售后专员',
          CUSTOMER_SERVICE_ABC: '售后ABC',
        },

        // salesman_type_options: {
        //   // APPEAL: '申诉',
        //   WAIT_SUBMIT: '待提交',
        //   WAIT_CONFIRM: '待确认',
        //   COMPLAIN: '申诉',
        //   END: '终止',
        //   WAIT_REVOKE: '待撤回',
        //   RECALLING: '撤回中',
        //   RECALLED: '已撤回',
        //   RECALL_FAIL: '撤回失败',
        //   WAIT_CONDFIRM: '待确认',
        //   CONFIRMED: '已确认',
        // },

        cols: [{
          label: '人员工号',
          prop: 'employee_number',
        },{
          label: '真实姓名',
          prop: 'real_name',
        },{
          label: '昵称',
          prop: 'nick_name',
        },{
          label: '客服分组',
          prop: 'group_name',
        },{
          label: '是否离职',
          prop: 'if_leave',
        },{
          label: '业务类型',
          prop: 'salesman_type',
        },{
          label: '业务属性',
          prop: 'attribute',
        },{
          label: '业务属性值',
          prop: 'attribute_value',
        }],
      }
    },
    methods: {

      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            // delete data.big_group;
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/pageReportPresaleAndAftersaleRelations', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            // delete data.big_group;
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/exportPresaleAndAftersaleRelations', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
    mounted (){
      this.queryData()
    },
  }
</script>
