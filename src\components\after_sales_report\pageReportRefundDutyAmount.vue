<!-- 责任金额报表II -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="创建开始时间：" label-width="120px;" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" :picker-options="dateFormatFun" type="datetime" placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="确认开始时间：" label-width="120px;" prop='begin_date2'>
            <el-date-picker v-model="query.begin_date2"  type="datetime" :picker-options="dateFormatFun"  placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date2[0].isShow' class="item" effect="dark" :content="rules.begin_date2[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <!-- <el-form-item label="责任类型：" label-width="120px;">
            <el-input v-model="query.duty_type_str" size='mini' icon="search" readonly :on-icon-click="() => selectDutyTypeOrProblem('duty_type')" ></el-input>
          </el-form-item> -->
          <el-form-item label="是否经销商订单:" label-width="120px;">
            <el-select v-model="query.if_dealer" clearable placeholder="请选择" size='mini'>
              <el-option
                v-for="(val, key) in if_dealer_options"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="源单单号：" label-width="120px;">
            <xpt-input v-model='query.source_no' size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="创建结束时间：" label-width="120px;" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="datetime" :picker-options="dateFormatFun1"  placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="确认结束时间：" label-width="120px;" prop='end_date2'>
            <el-date-picker v-model="query.end_date2"  type="datetime" :picker-options="dateFormatFun1" placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date2[0].isShow' class="item" effect="dark" :content="rules.end_date2[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="买家昵称：">
            <el-input
              v-model="query.buyer_name"
              size='mini'
              icon="search"
              :on-icon-click="selectBuyersName"
            ></el-input>
          </el-form-item>
          <el-form-item label="责任人：">
            <el-input
              v-model="query.staff"
              size='mini'
              :icon="query.staff ? 'close' : 'search'"
              readonly
              :on-icon-click="() => query.staff ? changeDutyPerson() : selectDutyPerson()"
            ></el-input>
            <!-- <xpt-input v-model='query.staff_group' icon='search' :on-icon-click='openGroup' size='mini' @change='groupChange'></xpt-input> -->
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="责任问题：">
            <el-input v-model="query.liability_question" size='mini' icon="search" readonly :on-icon-click="() =>  selectDutyTypeOrProblem('duty_problem')" ></el-input>
          </el-form-item>
          <el-form-item label="合并单号：">
            <el-input v-model="query.merge_no" size='mini' @blur="_getMergeNoId"></el-input>
          </el-form-item>
          <el-form-item label="责任范围：">
            <el-select v-model="query.duty_scope" clearable placeholder="请选择" size='mini'>
              <el-option
                v-for="(val, key) in duty_scope_options"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="结算方式 ：">
            <xpt-select-aux v-model='query.settle_method' aux_name='settle_method' size="mini" clearable></xpt-select-aux>
          </el-form-item>

           
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          begin_date2: '',
          end_date2: '',
          liability_question: '',
          duty_type_str: '',
          duty_type: '',
          duty_scope: '',
          buyer_name: '',
          source_no: '',
          staff_id: '',
          staff: '',
          if_dealer:'',
          settle_method:"",
          merge_no:""
        },
        dateFormatFun: {
          
          date: (function() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var time = year + "-" + month + "-" + day + " " + "00:00:00";
            return new Date(time);
          })()
        },
        dateFormatFun1: {
          
          date: (function() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var time = year + "-" + month + "-" + day + " " + "23:59:59";
            return new Date(time);
          })()
        },
        liability_scope: '',
        merge_trade_id: '',
        rules: {
          begin_date: [{
            required:true,
            message:'请选择开始日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
                callback();
                return
              }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
          begin_date2: [{
            required:true,
            message:'请选择开始日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
            callback();
            return
          }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
          end_date: [{
            required:true,
            message:'请选择结束日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
                callback();
                return
              }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
          end_date2: [{
            required:true,
            message:'请选择结束日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
            callback();
            return
          }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
        },
        cols: [
          {
            label: '合并单号',
            prop: 'merge_no',
          }, {
            label: '买家昵称',
            prop: 'buyer_name',
          }, {
            label: '责任问题',
            prop: 'duty_problem',
          }, {
            label: '责任类型',
            prop: 'duty_type'
          }, {
            label: '责任范围',
            prop: 'duty_scope',
          }, {
            label: '责任人工号',
            prop: 'staff_no',
          }, {
            label: '责任人',
            width: 100,
            prop: 'staff_name'
          }, {
            label: '责任人昵称',
            prop: 'staff_nick',
            width: 100
          }, /*{
            label: '部门',
            prop: 'department',
          },*/ {
            label: '责任金额',
            prop: 'liability_amount'
          }, {
            label: '处理金额',
            prop: 'handle_amount'
          }, {
            label: '备注',
            prop: 'remark'
          }, {
            label: '分组',
            prop: 'group_name'
          }, {
            label: '组长',
            prop: 'group_leader'
          }, {
            label: '大分组',
            prop: 'big_group_leader'
          },{
            label: '子单单号',
            prop: 'sub_bill_no'
          }, {
            label: '源单单号',
            prop: 'source_no'
          }, {
            label: '创建人',
            prop: 'create_man'
          }, {
            label: '创建时间',
            prop: 'create_time',
            format:"dataFormat1"
          },{
            label: '责任确认时间',
            prop: 'liability_confirm_time',
            format:"dataFormat1"
          },  {
            label: '责任状态',
            prop: 'duty_status'
          }, {
            label: '经销商',
            prop: 'dealer_customer_name'
          },{
            label: '结算方式',
            prop: 'settle_method',
            format: 'auxFormat',
            formatParams: 'settle_method',
          }, {
            label: '业务类型',
            width: 150,
            prop: 'business_type_trade'
          }, {
            label: '是否经销商订单',
            width: 150,
            prop: 'if_dealer'
          },
        ],
        duty_scope_options: {
          BD_Empinfo: '人员',
          BD_Supplier: '经销商',
          BD_Customer: '客户',
          BD_Department: '部门',
        },
        if_dealer_options:{
          Y: '是',
          N: '否',
        },
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {
      _getMergeNoId (){
        var self = this

        if(!this.query.merge_no){
          this.merge_trade_id = ''
          return
        }

        if(!this._getMergeNoId.where){
          this.ajax.postStream('/user-web/api/sql/listFields', { page: 'scm_sys_trade' }, res => {
            res.body.content.fields.some(obj => {
              if(obj.comment === '合并单号'){
                this._getMergeNoId.where = [{
                  "field": obj.field,
                  "table": obj.table,
                  "value": this.query.merge_no,
                  "operator": "=",
                  "condition": "AND",
                  "listWhere": []
                }]
                _get()
                return
              }
            })
          })
        }else {
          this._getMergeNoId.where[0].value = this.query.merge_no
          _get()
        }

        function _get (){
          self.ajax.postStream('/order-web/api/scmsystrade/list?permissionCode=ORDER_SALESORDER_QUERY', {
            "page_name": "scm_sys_trade",
            "where": self._getMergeNoId.where,
            "page_size": 50,
            "page_no": 1
          }, res => {
            self.merge_trade_id = res.body.content.list[0].merge_trade_id
          })
        }
      },
      // 责任人选择
      selectDutyPerson (){
        // if(!this.query.duty_type){
        //   this.$message.error('请先选择责任类型')
        //   return
        // }

        if(this.liability_scope === 'BD_Customer' && !this.query.merge_no){
          this.$message.error('请先填写合并订单号')
          return
        }
        console.log(this.query.duty_type);
        let otherParams = {
          mergeTradeId : this.merge_trade_id,//客户弹出框所需要的接口参数
          list_type : {
            '工厂责任': ['SP','SA'],
            '物流三包': ['SB','WL'],
          }[this.query.duty_type] || []//供应商弹出框所需要的接口参数
        }
        , params = {
          otherParams:otherParams,
          callback: d => {
            console.log('d12345674678',d)
            //客户和其它弹出框返回的参数需要区别
            // if(this.liability_scope == 'BD_Customer'){
            //   row.liability_person = d.cust_id//客户ID
            //   this.$set(row, 'liability_person_name', d.name);//客户昵称
            // }else{
              this.query.staff_id = d.id || d.cust_id || d.data.supplier_id || d.data.id || d.data[0].id
              this.query.staff = d.fullName || d._real_name_nick_name || d.name || d.data.name || d.data.realName || d.data[0].name
              console.log('this.query.staff_id123',this.query.staff_id,this.query.staff)
            //   this.$set(row, 'liability_person_name', d.fullName || d._real_name_nick_name || d.data.name || d.data.realName || d.data[0].name)
            // }
            
          },
          liability_type: this.liability_scope,
        }

        if(this.liability_scope === 'BD_Department') params.type = 1


        this.$root.eventHandle.$emit('alert',{
          params: params,
          close: f => f,
          // component:()=>import('@components/' + {
          //   BD_Empinfo: 'personel/list',//员工
          //   BD_Supplier: 'duty/supplier',//供应商
          //   BD_Customer: 'duty/selectPickrecommendhandler',//客户
          //   BD_Department: 'k3/departmentlist',//部门
          // }[this.liability_scope]),
          // style:'width:80%;height:80%',
          // title:'选择' + {
          //   BD_Empinfo: '员工',
          //   BD_Supplier: '供应商',
          //   BD_Customer: '客户',
          //   BD_Department: '部门',
          // }[this.liability_scope] + '列表'
           component:()=>import('@components/personel/list'),
          style:'width:80%;height:80%',
          title:'选择员工列表'
        })
      },
      // 创建时间和审核时间必须要至少一组，且开始时间和结束时间必须要同时存在
      checkDateRequired (){
        if(!this.query.begin_date && !this.query.end_date && !this.query.begin_date2 && !this.query.end_date2){
          this.rules.begin_date[0].required =
          this.rules.end_date[0].required =
          this.rules.begin_date2[0].required =
          this.rules.end_date2[0].required = true
        }

        if(this.query.begin_date || this.query.end_date){
          this.rules.begin_date[0].required =
          this.rules.end_date[0].required = true
        }

        if(this.query.begin_date2 || this.query.end_date2){
          this.rules.begin_date2[0].required =
          this.rules.end_date2[0].required = true
        }

        if((this.query.begin_date || this.query.end_date) && (!this.query.begin_date2 && !this.query.end_date2)){
          this.rules.begin_date2[0].required =
          this.rules.end_date2[0].required = false

          this.rules.begin_date2[0].isShow =
          this.rules.end_date2[0].isShow = false
        }

        if((this.query.begin_date2 || this.query.end_date2) && (!this.query.begin_date && !this.query.end_date)){
          this.rules.begin_date[0].required =
          this.rules.end_date[0].required = false

          this.rules.begin_date[0].isShow =
          this.rules.end_date[0].isShow = false
        }
      },
      // 选择买家昵称
      selectBuyersName (){
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/customers/list'),
          style:'width:900px;height:600px',
          title:'买家昵称列表',
          params: {
            close: d => {
              this.query.buyer_name = d.name
            }
          },
        })
      },
      selectDutyTypeOrProblem (model){
        this.$root.eventHandle.$emit('alert',{
          params: {
            callback: d => {
              console.log(d)
              this.query[model] = d.id;
              if(model !== 'duty_problem'){
                this.liability_scope = d.liability_scope
                this.query.duty_type_str = d.liability_question
              }else{
                this.query.liability_question = d.liability_question;

              }
              // this.query[model] = d[model === 'duty_problem' ? 'liability_question' : 'liability_type']
            },
          },
          component:()=>import('@components/duty/addDutyPerson'),
          style:'width:80%;height:80%',
          title:'选择责任问题'
        })
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.createStartTime = +new Date(data.begin_date);
            data.createEndTime =+new Date(data.end_date);
            data.auditStartTime = +new Date(data.begin_date2);
            data.auditEndTime =+new Date(data.end_date2);
            delete data.liability_question;
            delete data.duty_type_str;
            delete data.staff;
            delete data.begin_date;
            delete data.end_date;
            delete data.begin_date2;
            delete data.end_date2;
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/pageReportRefundDutyAmount', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
                this.$message.success(res.body.msg);

              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.createStartTime = +new Date(data.begin_date);
            data.createEndTime =+new Date(data.end_date);
            data.auditStartTime = +new Date(data.begin_date2);
            data.auditEndTime =+new Date(data.end_date2);
            delete data.liability_question;
            delete data.duty_type_str;
            delete data.staff;
            delete data.begin_date;
            delete data.end_date;
            delete data.begin_date2;
            delete data.end_date2;
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportRefundDutyAmount', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      showExportList (){
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/after_sales_report/export'),
          style:'width:900px;height:600px',
          title: '报表导出列表',
          params: {
            query: {
              type: 'EXCEL_TYPE_REPORT_REFUND_DUTY_AMOUNT',
            },
          },
        })
      },
      changeDutyPerson() {
          this.query.staff_id = ''
          this.query.staff = ''
      }
    },
    // computed: {
    //   staff() {
    //     return this.query.staff_id;
    //   },
    //   staff_group() {
    //     return this.query.staff_group_id;
    //   },
    //   big_group() {
    //     return this.query.big_group_id;
    //   }
    // },
    // watch: {
    //   staff(n) {
    //     if(n) {
    //       this.query.staff_group = '';
    //       this.query.staff_group_id = '';
    //       this.query.big_group = '';
    //       this.query.big_group_id = '';
    //     }
    //   },
    //   staff_group(n) {
    //     if(n) {
    //       this.query.staff = '';
    //       this.query.staff_id = '';
    //       this.query.big_group = '';
    //       this.query.big_group_id = '';
    //     }
    //   },
    //   big_group(n) {
    //     if(n) {
    //       this.query.staff = '';
    //       this.query.staff_id = '';
    //       this.query.staff_group = '';
    //       this.query.staff_group_id = '';
    //     }
    //   }
    // },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
