<!-- 物料包件明细报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <div>
        <!-- <span>第一次操作</span> -->
        <!-- <el-button type='primary' size='mini' @click='firstImport'>导入</el-button> -->
        <xpt-import slot='btns' :taskUrl='uploadUrl' class='mgl10' :otherParams="firstParams" :callback="firstCallback"></xpt-import>  

        <el-button type='primary' size='mini' @click='firstExport' :disabled="!firstExportUrl">导出</el-button>
      </div>
      <!-- <div>
      <span>第二次操作</span>
        <xpt-import slot='btns' :taskUrl='uploadUrl' :otherParams="secondParams" class='mgl10' :callback="secendCallback"></xpt-import>  

        <el-button type='primary' size='mini' @click='secondExport' :disabled="!secondExportUrl">导出</el-button>
      </div> -->
    </el-row>

    <xpt-list
      :showHead='false'
      :data='list'
      :colData='firstCol'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>

  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this
      return {
        firstParams:{
          ifPackage:true,
          importType: "FIRST_IMPORT"
        },
        secondParams:{
          ifPackage:true,
          importType: "SECOND_IMPORT"
        },
        firstExportUrl:null,
        secondExportUrl:null,
        uploadUrl:'/material-web/api/material/exportMaterialPackagesDetail',
        rules:{},
        // 导出按钮状态
        exportBtnStatus: false,
        count:0,
        list:[],
        cols:[],
        firstCol:[
          {
            label: '父项物料编码',
            prop: 'materialNumber',
            width:150
          }, {
            label: '物料名称',
            prop: 'materialName'
          }, {
            label: '规格型号',
            prop: 'specification'
          }, {
            label: '父项包件数',
            prop: 'packageQty'
          },  {
            label: '父项长',
            prop: 'fLength'
          }, {
            label: '父项宽',
            prop: 'fWide'
          }, {
            label: '父项高',
            prop: 'fHigh'
          }, {
            label: '项次',
            prop: 'itemNumber'
          },{
            label: '子项物料编码',
            prop: 'childMaterialNumber',
            width:'180'
          }, {
            label: '子项物料名称',
            prop: 'childMaterialName',
            width:'180'
          },{
            label: '子项规格型号',
            prop: 'childSpecification',
            width:'180'
          }, {
            label: '用量:分子',
            prop: 'numerator',
          }, {
            label: '包件版本号',
            prop: 'packageMaterialBomVersion',
          },  {
            label: '序号',
            prop: 'packagePkgSn',
          }, {
            label: '包件编码',
            prop: 'packageNumber',
          }, {
            label: '包件名称',
            prop: 'packageName',
          }, {
            label: '规格描述',
            prop: 'description',
          }, {
            label: '包装形式',
            prop: 'packagedForm',
          }, {
            label: '包件长',
            prop: 'length',
          }, {
            label: '包件宽',
            prop: 'wide',
          }, {
            label: '包件高',
            prop: 'high',
          }, {
            label: '包件体积',
            prop: 'volume',
          }, {
            label: '净重',
            prop: 'netWeight',
          }, {
            label: '毛重',
            prop: 'roughWeight',
          }, {
            label: '包装明细',
            prop: 'packingDetail',
          }, {
            label: '包装备注',
            prop: 'packingRemark',
          }
        ],
        secondCol:[
          {
            label: '父项物料编码',
            prop: 'materialNumber'
          }, {
            label: '物料名称',
            prop: 'materialName'
          }, {
            label: '规格型号',
            prop: 'specification'
          }, {
            label: '项次',
            prop: 'itemNumber'
          }, {
            label: '子项物料编码',
            prop: 'childMaterialNumber'
          }, {
            label: '子项物料名称',
            prop: 'childMaterialName',
          },{
            label: '子项规格型号',
            prop: 'childSpecification',
          }, {
            label: '用量:分子',
            prop: 'numerator',
            width:'180'
          }, {
            label: '包件编码',
            prop: 'packingNumber',
          }, {
            label: '名称',
            prop: 'name',
          }, {
            label: '规格描述',
            prop: 'description',
          }, {
            label: 'Pcs',
            prop: 'pcs',
          }, {
            label: '包装形式',
            prop: 'packagedForm',
          }, {
            label: '长',
            prop: 'length',
          }, {
            label: '高',
            prop: 'high',
          }, {
            label: '宽',
            prop: 'wide',
          }, {
            label: '体积',
            prop: 'volume',
          }, {
            label: '净重',
            prop: 'netWeight',
          }, {
            label: '毛重',
            prop: 'roughWeight',
          }, {
            label: '包装明细',
            prop: 'packingDetail',
          }, {
            label: '包装备注',
            prop: 'packingRemark',
          }
        ]
      
	
      }
    },
    methods: {
      firstExport(){
        if(this.firstExportUrl){
          window.open(this.firstExportUrl);
        }
      },
      secondExport(){
        if(this.secondExportUrl){
          window.open(this.secondExportUrl);
        }
      },
      
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/proxy/reportProxy', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 请选择用户信息
		selectproxysName (){
        var _this = this;
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/system_management/list2.vue'), close:function(){

                },
                style:'width:900px;height:600px',
				title:'请选择用户信息',
				params:{
                    type:'EMPLOYEE',
					status:1,//生效人员
                    isEnable:1,//生效时间
                    page_name: 'cloud_user_person',
                    where: [],
                    callback(b){
                        var data = b.data;
                        // _this.proxy_real_name = data.proxy_real_name;
                        _this.query.proxy_real_name = data.realName;

                        // _this.account_id = data.employeeNumber;
                        console.log(data)
                    }

                }

            	});
      },
      selectproxysNameChange(val){
        if(!val){
          this.query.proxy_real_name = ''
        }
      },
      principalNameChange(val){
        if(!val){
          this.query.principal_real_name = ''
        }
      },
      selectprincipalName (){
        var _this = this;
        this.$root.eventHandle.$emit('alert',{
            component:()=>import('@components/system_management/list2.vue'), close:function(){

        },
        style:'width:900px;height:600px',
				title:'请选择用户信息',
				params:{
          type:'EMPLOYEE',
					status:1,//生效人员
          isEnable:1,//生效时间
          page_name: 'cloud_user_person',
            where: [],
            callback(b){
                var data = b.data;
                _this.query.principal_real_name = data.realName;
                // _this.account_id = data.employeeNumber;
                console.log(data)
            }

          }

        });
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));

            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/proxy/exportExcel', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
	  },
	  showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_REPORT_PROXY',
					},
				},
			})
    },
    firstCallback(res){
      if(res.body.result){
        this.$message.success('导入成功');
        this.list = res.body.content.data;
        this.firstExportUrl = res.body.content.fileURL;
        this.cols = this.firstCol;
        this.count = this.list.length;

      }
    },
    secendCallback(res){
      console.log(res);
      if(res.body.result){
        this.$message.success('导入成功');
        this.list = res.body.content.data;
        this.secondExportUrl = res.body.content.fileURL;
        this.cols = this.secondCol;
        this.count = this.list.length;
      }
    }
    }
  }
</script>


