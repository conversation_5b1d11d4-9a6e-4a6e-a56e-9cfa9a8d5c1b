<!-- 优惠活动报表查询 -->
<template>
    <div class='xpt-flex'>
        <el-row :gutter='10' class='xpt-top'>
            <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="130px">
				<el-col :span='5'>
					<el-form-item label="优惠活动名称：" prop='discount_name'>
						<xpt-input v-model='query.discount_name'  size='mini' ></xpt-input>
					</el-form-item>
					<el-form-item label="优惠活动编码：" prop='discount_no'>
						<xpt-input v-model='query.discount_no'  size='mini'></xpt-input>
					</el-form-item>
					<el-form-item label="优惠类型：" prop='discount_type'>
						<xpt-select-aux v-model='query.discount_type' aux_name='discountCategory' clearable></xpt-select-aux>
					</el-form-item>
					<el-form-item label="优惠子类型：" prop='discount_sub_type'>
						<xpt-select-aux v-model='query.discount_sub_type' aux_name='discountSubclass' clearable></xpt-select-aux>
					</el-form-item>
					<el-form-item label="状态：" prop='status'>
						<el-select v-model='query.status' placeholder="请选择" label-width="150px"  size='mini' clearable>
							<el-option v-for='(row,index) in discountStatus' :key='index' :label='row.name' :value='row.code'></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="变更状态：" prop='change_status'>
						<el-select placeholder="请选择" size='mini' v-model="query.change_status" clearable>
							<el-option v-for='item in changeStatusList' :key='item.code' :label='item.name' :value='item.code'></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="是否预案活动：" prop='if_plan_activity'>
						<el-select placeholder="请选择" size='mini' v-model="query.if_plan_activity" clearable>
							<el-option label="是" value="Y"></el-option>
							<el-option label="否" value="N"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="实施店铺：" prop='shop_name'>
          <xpt-input v-model='query.shop_name' icon='search' :on-icon-click='openShop3' size='mini' ></xpt-input>
						
					</el-form-item>
				</el-col>
				<el-col :span='5'>
					<el-form-item label="生效时间：" prop="enable_time">
						<el-date-picker v-model="query.enable_time" type="datetime"  placeholder="选择日期" size='mini'   :picker-options='enableDateOptions'></el-date-picker>
						<el-tooltip v-if='rules.enable_time[0].isShow' class="item" effect="dark" :content="rules.enable_time[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="失效时间：" prop="disable_time">
						<el-date-picker v-model="query.disable_time" type="datetime" placeholder="选择日期" size='mini' :editable='false'  :default-time="['00:00:00']"  :picker-options='disableDateOptions'></el-date-picker>
						<!-- <el-tooltip v-if='rules.disable_time[0].isShow' class="item" effect="dark" :content="rules.disable_time[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip> -->
					</el-form-item>
					<el-form-item label="时间维度：" prop="discount_affection_date">
						<xpt-select-aux v-model='query.discount_affection_date' aux_name='discountAffection' clearable></xpt-select-aux>
						<el-tooltip v-if='rules.discount_affection_date[0].isShow' class="item" effect="dark" :content="rules.discount_affection_date[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="店铺范围：" prop='store_area'>
						<el-select v-model='query.store_area' placeholder="请选择" label-width="150px"  size='mini' clearable>
							<el-option v-for='(row,index) in store_area_type' :key='index' :label='row.name' :value='row.type'></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="优惠影响范围：" prop='discount_effect_area'>
						<xpt-select-aux v-model='query.discount_effect_area' aux_name='DISCOUNT_EFFECT_AREA' :clearable='true'></xpt-select-aux>
					</el-form-item>
					<el-form-item label="活动区间：" prop='act_section'>
						<xpt-select-aux v-model='query.act_section' aux_name='act_aection' :clearable='true'></xpt-select-aux>
					</el-form-item>
                    <el-form-item label="是否排名礼：" prop='if_act_winning'>
						<el-select placeholder="请选择" size='mini' v-model="query.if_act_winning" clearable>
							<el-option label="是" value="Y"></el-option>
							<el-option label="否" value="N"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span='5'>
					<el-form-item label="优惠条件：" prop='discount_condition_type'>
						<xpt-select-aux v-model='query.discount_condition_type' aux_name='discount_condition'  :disabled="false" :clearable='true'></xpt-select-aux>
					</el-form-item>
					<el-form-item label="是否叠加优惠：" prop='if_superposition_discount'>
						<el-select placeholder="请选择" size='mini' v-model="query.if_superposition_discount" clearable>
							<el-option label="是" value="Y"></el-option>
							<el-option label="否" value="N"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="是否每满" prop="if_each_full">
						<el-select placeholder="请选择" size='mini' v-model="query.if_each_full" clearable>
							<el-option label="是" value="Y" :disabled="query.if_superposition_discount === 'N' || query.discount_condition_type === 'UNCONDITIONAL'"></el-option>
							<el-option label="否" value="N"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="需要审核：" prop='if_need_audit'>
						<el-select placeholder="请选择" size='mini' v-model="query.if_need_audit" clearable>
							<el-option label="是" value="Y"></el-option>
							<el-option label="否" value="N"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="金额可变：" prop='if_amount_change'>
						<el-select placeholder="请选择" size='mini' v-model="query.if_amount_change" clearable>
							<el-option label="是" value="Y"></el-option>
							<el-option label="否" value="N"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="优惠清单范围：" prop='search_area'>
						<el-select placeholder="请选择" size='mini' v-model="query.search_area" clearable>
							<el-option label="全局" value="ALL_AREA"></el-option>
							<el-option label="条件" value="CONDITION"></el-option>
						</el-select>
					</el-form-item>
                    <el-form-item label="是否提前购：" prop='if_in_advance_discount'>
						<el-select placeholder="请选择" size='mini' v-model="query.if_in_advance_discount" clearable>
							<el-option label="是" value="Y"></el-option>
							<el-option label="否" value="N"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="5">
					<el-form-item label="优惠项目物料编码：" prop='material_number'>
						<el-input size='mini' icon='search' style='width: 100%'
							v-model='query.material_number'
							:on-icon-click='selectMaterial'
							@change="checkMaterialNumber"
							></el-input>
					</el-form-item>
					<el-form-item label="优惠项目物料名称：" prop='material_name'>
						<xpt-input v-model='query.material_name' size='mini' style="width:100%;"></xpt-input>
					</el-form-item>
					<el-form-item label="金额门槛：" prop='threshold_price'>
						<el-input  type="number"  v-model="query.threshold_price" size='mini' style="width:100%;"> </el-input>
					</el-form-item>
					<el-form-item label="件数门槛：" prop='threshold_count'>
						<el-input  type="number"  v-model="query.threshold_count" size='mini' style="width:100%;"> </el-input>
					</el-form-item>
					
					<el-form-item label="优惠清单物料编码：" prop='inventory_material_number'>
						<el-input size='mini' icon='search' style='width: 100%'
							v-model='query.inventory_material_number'
							:on-icon-click='selectMaterialInventory'
							@change="checkMaterialNumber"
							></el-input>
					</el-form-item>
                    <el-form-item label="应用场景："  prop='application_scenarios'>
                        <xpt-select-aux v-model='query.application_scenarios' aux_name='application_scenarios' clearable></xpt-select-aux>
                    </el-form-item>
                    <el-form-item label="优惠性质：">
                        <el-select size='mini' 	v-model="query.discount_kind"  clearable>	
                            <el-option
                                v-for="item in discount_kind_opiton"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                :disabled="item.disabled">
                            </el-option>
                        </el-select>
                    </el-form-item>
				</el-col>
				<el-col :span="4">
					<el-form-item label="">
						<el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
					</el-form-item>
					<el-form-item label="">
						<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button>
					</el-form-item>
					<el-form-item label="">
						<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出查询结果</el-button>
					</el-form-item>
					<el-form-item label="">
						<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
					</el-form-item>
				</el-col>
            </el-form>
        </el-row>
        <xpt-list
          :showHead='false'
          :data='list'
          :colData='cols'
          :pageTotal='count'
          selection=''
          @page-size-change='pageSizeChange'
          @current-page-change='currentPageChange' >
        </xpt-list>
    </div>
</template>
<script>
  import mixin from '../mixin.js'
  import Fn from '@common/Fn.js'
  import validate from '@common/validate.js';
  export default {
		props: ['params'],
    mixins: [mixin],
		data() {
			let self = this
			return {
				query: {
					shop_name:null,
					shop_code:null,
					discount_name: null,
					discount_no: null,
					discount_type: '',
					discount_sub_type: '',
					status: '',
					change_status: '',
					enable_time: null,
					disable_time: null,
					discount_affection_date: 'ORDER_DATE',
					store_area: '',
					discount_effect_area: '',
					act_section: 'INTERVAL_ACT',
					discount_condition_type: '',
					if_superposition_discount: '',
					if_each_full: '',
					if_need_audit: '',
					if_amount_change: '',
					material_number: null,
					material_name: null,
					threshold_price: null,
					threshold_count: null,
					page_size: self.pageSize,
					page_no: 1,
					inventory_material_number: '',
                    search_area: '',
                    application_scenarios: '', // 运用场景
                    discount_kind: '', // 优惠性质
                    if_in_advance_discount: '',
                    if_act_winning: '',
                    if_plan_activity: ''
				},
				enableDateOptions:{},
				disableDateOptions:{},
				rules:{
					enable_time:validate.isNotBlank({
						required:true,
						self:self
					}),
					// disable_time:validate.isNotBlank({
					// 	required:true,
					// 	self:self
					// }),
					discount_affection_date:validate.isNotBlank({
						required:true,
						self:self
					})
				},
				// 查询按钮状态
				queryBtnStatus: false,
				// 导出按钮状态
				exportBtnStatus: false,
				queryBtnStatusTimer: '',
				exportBtnStatusTimer: '',
				store_area_type:[
					{
						name:'全局',
						type:'1'
					}, {
						name:'店铺',
						type:'0'
					},
				],
				discountStatus: [
					{name: "创建", code: "CREATE"},
					{name: "待审核", code: "WAIT_APPROVED"},
					{name: "审核中", code: "APPROVING"},
					{name: "已审核", code: "APPROVED"},
					{name: "驳回", code: "REJECTED"},
					{name: "禁用", code: "FORBID"}
				],
				changeStatusList: [
					{name: "变更创建", code: "CHANGE_CREATE"},
					{name: "变更待审核", code: "CHANGE_WAIT_APPROVED"},
					{name: "变更审核中", code: "CHANGE_APPROVING"},
					{name: "变更已审核", code: "CHANGE_APPROVED"},
					{name: "变更驳回", code: "CHANGE_REJECTED"},
				],
				count:0,
				list:[],
				cols: [
					{
						label: '优惠项目物料编码',
						prop: 'material_number',
						width: 120
					}, {
						label: '优惠项目物料名称',
						prop: 'material_name',
						width: 120
					}, {
						label: '规格描述',
						prop: 'material_specification',
						width: 240
					}, {
						label: '单位',
						prop: 'material_unit',
					}, {
						label: '价格',
						prop: 'act_price',
					}, {
						label: '成本',
						prop: 'cost_price',
					}, {
						label: '优惠金额',
						prop: 'discount_amount',
					}, {
						label: '生效时间',
						prop: 'enable_time',
						formatter(val){
							return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss')
						},
						width: 130
					}, {
						label: '失效时间',
						prop: 'disable_time',
						formatter(val){
							return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss')
						},
						width: 130
					}, {
						label: '行状态',
						prop: 'row_status',
						formatter(val){
							switch(val){
								case '1':   return '失效';break;
								case '0':   return '生效';break;
							}
						}
					}, {
						label: '优惠活动名称',
						prop: 'discount_name',
						width: 200
					}, {
						label: '优惠活动编码',
						prop: 'discount_no',
						width: 120
					}, {
						label: '优惠类型',
						prop: 'discount_type',
						format: 'auxFormat',
						formatParams: 'discountCategory',
					}, {
						label: '优惠子类型',
						prop: 'discount_sub_type',
						format: 'auxFormat',
						formatParams: 'discountSubclass',
                    }, {
						label: '运用场景',
						prop: 'application_scenarios',
						format: 'auxFormat',
						formatParams: 'application_scenarios',
                    }, {
						label: '优惠性质',
                        prop: 'discount_kind',
                        formatter(val){
							switch(val){
								case 'ORDER_CUT':   return '拍减';break;
                                case 'ORDER_PRESENT':   return '拍赠';break;
                                case 'PAID_RETURN':   return '付返';break;
                                case 'PAID_PRESENT':   return '付赠';break;
							}
						}
                    }, {
						label: '活动状态',
						prop: 'status',
						formatter (val) {
							switch (val) {
								case "CREATE": return "创建" ; break;
								case "WAIT_APPROVED": return "待审核"; break;
								case "APPROVING": return "审核中" ; break;
								case "APPROVED": return "已审核"; break;
								case "FORBID": return "禁用"; break;
								case "REJECTED": return "驳回"; break;
							}
						}
					}, {
						label: '金额门槛',
						prop: 'threshold_price'
					}, {
						label: '件数门槛',
						prop: 'threshold_count',
					}, {
						label: '减元',
						prop: 'subtract_money'
					}, {
						label: '折扣',
						prop: 'discount'
					}, {
						label: '是否赠品',
						prop: 'if_gift',
						formatter (val) {
							switch(val){
								case 'Y' :   return '是';break;
								case 'N' :   return '否';break;
							}
						}
					}, {
						label: '是否礼品',
						prop: 'if_present',
						formatter (val) {
							return val === 'Y' ? '是' : '否'
						}
                    }, {
						label: '是否预案活动',
						prop: 'if_plan_activity',
						formatter (val) {
							switch(val){
								case 'Y' :   return '是';break;
								case 'N' :   return '否';break;
							}
						}
					}, {
						label: '是否排名礼',
						prop: 'if_act_winning',
						formatter (val) {
							switch(val){
								case 'Y' :   return '是';break;
								case 'N' :   return '否';break;
							}
						}
                    },  {
						label: '是否提前购',
						prop: 'if_in_advance_discount',
						formatter (val) {
							switch(val){
								case 'Y' :   return '是';break;
								case 'N' :   return '否';break;
							}
						}
                    }, {
						label: '三包减免',
						prop: 'three_reduction'
					}, {
						label: '物流减免',
						prop: 'logistics_reduction'
					}, {
						label: '个人承担比例',
						prop: 'person_bear_ratio',
						width: 90
					}, {
						label: '是否享受优惠',
						prop: 'if_enjoy_discount',
						formatter (val) {
							return val === 'Y' ? '是' : '否'
						},
						width: 90
					}
                ],
                discount_kind_opiton:[
                    {
                        label: '拍减',
                        value: 'ORDER_CUT',
                        disabled:false,
                    },{
                        label: '拍赠',
                        value: 'ORDER_PRESENT',
                        disabled:false,
                    },{
                        label: '付返',
                        value: 'PAID_RETURN',
                        disabled:false,
                    },{
                        label: '付赠',
                        value: 'PAID_PRESENT',
                        disabled:false,
                    },
                ]
			}
		},
		methods: {
			// 查看详情
			reset() {
				for(let v in this.query) {
					if (v != 'page_size' || v != 'page_no') {
						this.query[v] = ''
					}
					if (v === 'page_size') {
						this.query[v] = 50
					}
					if (v === 'page_no') {
						this.query[v] = 1
					}
				}
			},
			pageSizeChange(ps) {
				this.query.page_size = ps;
				this.queryData();
			},
			currentPageChange(page) {
				this.query.page_no = page;
				this.queryData();
			},
			queryData() {
				this.$refs.query.validate((valid) => {
					if(valid) {
						let data = JSON.parse(JSON.stringify(this.query));
						data['disable_time']=='' ?   data['disable_time']='' : data['disable_time']= +new Date(data.disable_time);
						data['enable_time']=='' ?   data['enable_time']='' : data['enable_time']= +new Date(data.enable_time);
						this.queryBtnStatus = true;
            delete data.shop_name;
						console.log(data)
						this.ajax.postStream('/reports-web/api/discount/activity/discountActivityReportFormList', data, res => {
						this.queryBtnStatus = false;
						if(res.body.result && res.body.content) {
							let content = res.body.content;
							this.list = content.list || [];
							this.count = content.count || 0;
							this.$message.success(res.body.msg);
						}
						}, err => {
						this.$message.error(err);
						this.queryBtnStatus = false;
						})
					}
				})
			},
			// 导出功能
			exportExcel(e) {
				this.$refs.query.validate((valid) => {
					if(valid) {
						let data = JSON.parse(JSON.stringify(this.query));
            delete data.shop_name;
						this.exportBtnStatus = true;
						this.ajax.postStream('/reports-web/api/discount/activity/discountActivityReportFormExport', data, res => {
						this.exportBtnStatus = false;
						console.log(res)
						this.$message({
							type: res.body.result ? 'success' : 'error',
							message: res.body.msg
						})
						}, err => {
							console.log(err)
						this.$message.error(err);
						this.exportBtnStatus = false;
						})
					}
				})
			},
			enable_time() {
				// 当this.query.begin_date值更新时，触发begin_date值的更新
				return this.query.enable_time;
			},
			disable_time() {
				return this.query.disable_time;
			},
			// 导出文件下载  
			showExportList (){
				this.$root.eventHandle.$emit('alert', {
					component: () => import('@components/after_sales_report/export'),
					style:'width:900px;height:600px',
					title: '报表导出列表',
					params: {
						query: {
						type: 'EXCEL_TYPE_DISCOUNT_ACTIVITY',//EXCEL_TYPE_REPORT_BATCH_TRADE  
						},
					},
				})
			},
			// 添加商品
			selectMaterial(){
				let params = {}, self = this
				params.callback = d => {
					if(d) {
						self.query.material_number = d.materialNumber;
					}
				}
				self.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/order/selectGoodsList'),
					style: 'width:800px;height:500px',
					title: '商品列表'
				});
			},
			
			selectMaterialInventory () {
				let params = {}, self = this
				params.callback = d => {
					if(d) {
						self.query.inventory_material_number = d.materialNumber;
					}
				}
				self.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/order/selectGoodsList'),
					style: 'width:800px;height:500px',
					title: '商品列表'
				});
			},
			checkMaterialNumber () {
				if(!/^[A-Z0-9\-]+$/.test(this.query.material_number)){
					this.$message.error('物料编码必须是大写字母、数字、-的组合格式')
					return false
				}
			}
		},
		watch: {
			'query.enable_time': function (n, o) {
				console.log(n,o)
				this.disableDateOptions = {
					disabledDate(time) {
						// 设置结束时间的失效时间为小于开始时间
						return time.getTime() < +new Date(n);
					}
				}
			},
			'query.disable_time': function (n, o) {
				this.enableDateOptions = {
					disabledDate(time) {
						return time.getTime() > +new Date(n);
					}
				}
			},
		
		},
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{
    width: 150px;
  }
</style>


