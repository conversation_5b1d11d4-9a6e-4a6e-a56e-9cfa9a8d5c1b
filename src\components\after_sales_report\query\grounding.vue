<!-- 上架单查询 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="单据编号：" prop='bill_no'>
            <xpt-input v-model='query.bill_no'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="开始时间：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date"  placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="单据状态：" prop='status'>
            <el-select v-model='query.status' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in receiptList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="结束时间：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="物料名称：" prop='fname'>
            <xpt-input v-model='query.fname'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="物料编码：" prop='fnumber'>
            <xpt-input v-model='query.fnumber'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <!--<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>-->
        </el-col>
      </el-form>
    </el-row>

    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>

  </div>
</template>
<script>
  import mixin from '../mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          page_no: 1,// 页码
          page_size: self.pageSize, // 页数
          begin_date:'',
          end_date:'',
          bill_no:"",//单据编号
          fname:"",//物料名称
          fnumber:"",//物料编码
          status:""//单据状态
        },
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        count:0,
        list:[],
        cols:[
          {
            label: '单据编号',
            prop: 'bill_no'
          }, {
            label: '单据状态',
            prop: 'status'
          }, {
            label: '物料编码',
            prop: 'fnumber'
          }, {
            label: '物料名称',
            prop: 'fname'
          }, {
            label: '数量',
            prop: 'qty'
          }, {
            label: '最新修改时间',
            prop: 'modify_date',
            format:"dataFormat"
          }
        ],
        receiptList: [
          {
            name: '暂存',
            value: 'Z'
          }, {
            name: '创建',
            value: 'A'
          }, {
            name: '审核中',
            value: 'B'
          }, {
            name: '已审核',
            value: 'C'
          }, {
            name: '重新审核',
            value: 'D'
          }, {
            name: '请选择',
            value: ''
          }
        ],
      }
    },
    methods: {
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            var date = new Date(data.end_date);
            date.setHours(23);
            date.setMinutes(59);
            date.setSeconds(59);
            data.end_date =new Date(date);
            data.begin_date = new Date(data.begin_date);
            this.queryBtnStatus = true;
            this.ajax.postStream('/kingdee-web/api/otwb/pageShelfList', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));

            this.exportBtnStatus = true;
            this.ajax.postStream('/order-web/api/report/exportAchievement', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{width: 150px;}
</style>


