<!-- 物料清单查询 -->
<template>
  <div class='xpt-flex' style="overflow-y: auto">
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="父项物料编码：" prop='fnumber'>
            <xpt-input v-model='query.fnumber'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="BOM简称：" prop='bname'>
            <xpt-input v-model='query.bname'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="禁用状态：" prop='disable_status'>
            <el-select v-model='query.disable_status' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in disablesStatusList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="父项物料名称：" prop='fname'>
            <xpt-input v-model='query.fname'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="BOM分类：" prop='cat'>
            <el-select v-model='query.cat' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in catList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="BOM版本：" prop='bnumber'>
            <xpt-input v-model='query.bnumber'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="BOM用途：" prop='bom_use'>
            <el-select v-model='query.bom_use' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in bomuseList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <!--<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>-->
        </el-col>
      </el-form>
    </el-row>
    <el-row :gutter='10' class='xpt-top' style="background:#ffffff;height: 30px;border-bottom: 1px solid #eeeeee">
      <span>父项物料</span>
    </el-row>
    <div>
      <xpt-list
        :showHead='false'
        :data='list'
        :colData='cols'
        :pageTotal='count'
        selection=''
        @page-size-change='pageSizeChange'
        @current-page-change='currentPageChange'
      ></xpt-list>
    </div>
    <div>
      <el-row :gutter='10' class='xpt-top' style="background:#ffffff;height: 30px;border-bottom: 1px solid #eeeeee">
        <span>子项物料</span>
      </el-row>

      <xpt-list
        :showHead='false'
        :data='list_shop'
        :colData='cols_shop'
        selection=''
      ></xpt-list>
    </div>
  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this
      return {
        query: {
          page_no: 1,// 页码
          page_size: self.pageSize, // 页数
          bname:"",//BOM简称
          bnumber:"",//BOM版本
          //bomId:"",//bomID
          //bom_group:"",//BOM分组
          bom_use:"",//BOM用途
          cat:"",//BOM分类
         // data_status:"",//数据状态
          disable_status:"",//禁用状态
          fname:"",//物料名称
          fnumber:""//物料编码
        },
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        count:0,
        list:[],
        list_shop:[],
        cols: [
          {
            label: '父项物料编码',
            prop: 'fnumber'
          }, {
            label: 'BOM版本',
            prop: 'bnumber',
            redirectClick(row) {
              self.queryDataChild(row)
            }
          }, {
            label: 'BOM简称',
            prop: 'bname',
          }, {
            label: '物料名称',
            prop: 'fname'
          }, {
            label: '规格型号',
            prop: 'spec',
          }, {
            label: '物料属性',
            prop: 'ferpcls_id',
          }, {
            label: '父项物料单位',
            prop: 'funit'
          }, {
            label: '包件数',
            prop: 'pakeqty'
          }, {
            label: 'BOM版本体积',
            prop: 'bom_valume',
          }, {
            label: '绑定销售',
            prop: 'binding'
          }, {
            label: '组合自动拆分',
            prop: 'auto_split'
          }, {
            label: 'BOM分类',
            prop: 'cat'
          }, {
            label: 'BOM用途',
            prop: 'bom_use'
          }, {
            label: 'BOM分组',
            prop: 'bom_group'
          }, {
            label: '数据状态',
            prop: 'data_status'
          }, {
            label: '禁用状态',
            prop: 'disable_status'
          }
        ],
        cols_shop:[
          {
            label: '项次',
            prop: 'order_no'
          }, {
            label: '子项物料编码',
            prop: 'fnumber'
          }, {
            label: '子项物料名称',
            prop: 'fname'
          }, {
            label: '子项规格型号',
            prop: 'spce'
          }, {
            label: '子项类型',
            prop: 'ftype'
          }, {
            label: '子项物料属性',
            prop: 'fattr'
          }, {
            label: '是否停产',
            prop: 'stop'
          }, {
            label: '用量类型',
            prop: 'use_type'
          }, {
            label: '用量',
            prop: 'use_num'
          }, {
            label: '子项单位',
            prop: 'funit'
          }, {
            label: '上级物料编码',
            prop: 'parentMaterialNumber'
          }, {
            label: '生效日期',
            prop: 'effect_date'
          }, {
            label: '失效日期',
            prop: 'expire_date'
          }, {
            label: '备注',
            prop: 'memo'
          }
        ],
        catList:[{
        name: '标准BOM',
        value: '1'
      }, {
        name: '配置BOM',
        value: '2'
      }, {
        name: '套件BOM',
        value: '3'
        }],
        bomuseList:[{
          name: '通用',
          value: '99'
        }, {
          name: '自制',
          value: '2'
        }, {
          name: '委外',
          value: '3'
        }, {
          name: '组装',
          value: '4'
        }],
        disablesStatusList:[{
          name: '正常',
          value: 'A'
        }, {
          name: '已禁用',
          value: 'B'
        }]
      }
    },
    methods: {
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            this.queryBtnStatus = true;
            this.list_shop = [];
            this.ajax.postStream('/kingdee-web/api/materiel/pageBomInfo', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      queryDataChild(row) {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = {bomId:row.bnumber, fnumber:row.fnumber};
            this.queryBtnStatus = true;
            this.ajax.postStream('/kingdee-web/api/materiel/listBomSub', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list_shop = content || [];
              } 
              if (!res.body.result) {
                this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));

            this.exportBtnStatus = true;
            this.ajax.postStream('/order-web/api/report/exportAchievement', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{width: 150px;}
</style>

