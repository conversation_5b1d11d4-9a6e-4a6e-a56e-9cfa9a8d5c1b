<!-- 店铺物料查询 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="物料编码：" prop='fnumber'>
            <xpt-input v-model='query.fnumber'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="店铺名称：" prop='shop_name'>
            <xpt-input v-model='query.shop_name'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="平台商品状态：" prop='status'>
            <el-select v-model='query.status' label-width="150px" size='mini' clearable>
              <el-option v-for='(row,index) in goodStatusList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="物料名称：" prop='fname'>
            <xpt-input v-model='query.fname'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="下架：" prop='soldout'>
            <el-select v-model='query.soldout' label-width="150px" size='mini' clearable>
              <el-option v-for='(row,index) in undercarriageList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="SKU_ID：" prop='fname'>
            <xpt-input v-model='query.tb_sku_id'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="淘宝商品ID：" prop='tb_product_id'>
            <xpt-input v-model='query.tb_product_id'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="商品来源：" prop='material_source'>
            <el-select placeholder="请选择" size='mini' v-model="query.material_source" clearable>
							<el-option label="拼多多" value="PDD"></el-option>
							<el-option label="淘宝" value="TAOBAO"></el-option>
              <el-option label="京东" value="JD"></el-option>
              <el-option label="快手" value="KUAISHOU"></el-option>
              <el-option label="抖音" value="DOUYIN"></el-option>
						</el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportShopExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>

      <xpt-list
        :showHead='false'
        :data='list'
        :colData='cols'
        :pageTotal='count'
        selection=''
        @page-size-change='pageSizeChange'
        @current-page-change='currentPageChange'
      ></xpt-list>

  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this
      return {
        query: {
          page_no: 1,// 页码
          page_size: self.pageSize, // 页数
          fnumber:"",//物料编码
          fname:"",//物料名称
          shop_name:"",//店铺名称
          soldout:"",//下架
          tb_product_id:"",//淘宝商品id
          //fnumberList:null//物料编码列表,
          status: '', //商品状态
          tb_sku_id: '', //sku_id
          
          material_source: '' //物料来源
        },
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        count:0,
        list:[],
        cols:[
          {
            label: '淘宝商品ID',
            prop: 'tb_product_id',
            width: 100
          }, {
            label: 'sku_id',
            prop: 'tb_sku_id',
            width: 110
          }, {
            label: '物料名称',
            prop: 'fname',
            width: 100
          }, {
            label: '物料编码',
            prop: 'fnumber',
            width: 120
          }, {
            label: '规格描述',
            prop: 'spec',
            width: 120
          }, {
            label: '店铺名称',
            prop: 'shop_name'
          }, {
            label: '商品标题',
            prop: 'title',
            width: 220
          }, {
            label: '淘宝链接',
            prop: 'tb_link'
          }, {
            label: '淘宝库存',
            prop: 'tb_qty'
          }, {
            label: '一口价',
            prop: 'fixed_price'
          }, {
            label: '下架',
            prop: 'soldout'
          }, {
            label: '平台商品状态',
            prop: 'status',
            width:100,
            formatter(val){
              switch(val){
                case 'instock':   return '库中';break;
                case 'onsale':   return '出售中';break;
                case 'sold_out':   return '售罄';break;
                case '1':   return '上架';break;
                case '2':   return '下架';break;
                case '3':   return '售罄';break;
                case '4':   return '已删除';break;
              }
            }
          }, {
            label: '商品来源',
            prop: 'material_source',
            formatter(val){
              return{
                'PDD':'拼多多',
                'TAOBAO':'淘宝',
                'JD':'京东',
                'KUAISHOU':'快手',
                'DOUYIN':'抖音',
              }[val] || val
            }
          }, {
            label: '承诺发货时间',
            prop: 'commit_date',
            width:100
          }, {
            label: '上架时间',
            prop: 'puaway_dte',
            format:"dataFormat1",
            width:130
          }, {
            label: '下架时间',
            prop: 'delist_time',
            format:"dataFormat1",
            width:130
          }, {
            label: '修改时间',
            prop: 'modified_time',
            format:"dataFormat1",
            width:130
          }, {
            label: '卖点',
            prop: 'sell_point'
          }, {
            label: '减库存',
            prop: 'sub_stock'
          }, {
            label: '包邮',
            prop: 'freight_payer'
          }, {
            label: '图片链接',
            prop: 'pic_link'
          }, {
            label: '产品定位',
            prop: 'prd_position'
          }, {
            label: '物料更新时间',
            prop: 'fentry_update_time',
            format: "dataFormat1",
            width:100
          }

        ],
        undercarriageList: [
          {
            name: '是',
            value: '0'
          }, {
            name: '否',
            value: '1'
          }
        ],
        PDDStatusList: [
          {
            name: '上架',
            value: '1',
          }, {
            name: '下架',
            value: '2',
          }, {
            name: '售罄（拼多多）',
            value: '3',
          }, {
            name: '已删除',
            value: '4',
          }
        ],
        TAOBAOStatusList: [
          {
            name: '出售中',
            value: 'onsale',
          }, {
            name: '库中',
            value: 'instock',
          }, {
            name: '售罄（淘宝）',
            value: 'sold_out',
          }
        ],
        goodStatusList: []
      }
    },
    methods: {
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            this.queryBtnStatus = true;
            this.ajax.postStream('/order-web/api/externalMaterial/pageShopMaterielBySystemExternalMaterial', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportShopExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/materiel/exportShopMateriel', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      // 导出文件下载  
			showExportList (){
				this.$root.eventHandle.$emit('alert', {
					component: () => import('@components/after_sales_report/export'),
					style:'width:900px;height:600px',
					title: '报表导出列表',
					params: {
						query: {
						type: 'EXCEL_TYPE_REPORT_SHOP_MATERIEL',  
						},
					},
				})
			}
    },
    mounted() {
      this.goodStatusList = this.PDDStatusList.concat(this.TAOBAOStatusList)
    },
    watch: {
      'query.material_source': function (n, o) {
        if (n === '') {
          this.goodStatusList = this.PDDStatusList.concat(this.TAOBAOStatusList)
        } else if (n === 'PDD'){
          if (/^(onsale|instock)$/.test(this.query.status)) {
            this.query.status = ''
          }
          this.goodStatusList = this.PDDStatusList
        } else if (n === 'TAOBAO'){
          if (!/^(onsale|instock)$/.test(this.query.status)) {
            this.query.status = ''
          }
          this.goodStatusList = this.TAOBAOStatusList
        }
      }
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{width: 150px;}
</style>

