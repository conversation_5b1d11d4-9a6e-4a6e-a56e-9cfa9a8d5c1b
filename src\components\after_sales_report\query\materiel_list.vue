<!-- 物料列表查询 -->
<template>
  <div class='xpt-flex' style="overflow-y: auto">
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="物料编码：" prop='fnumber'>
            <xpt-input v-model='query.fnumber'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="商品类别名称：" prop='category_name'>
            <xpt-input v-model='query.category_name'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="物料属性：" prop='material_attr'>
            <el-select v-model='query.material_attr'  size='mini'>
              <el-option v-for='(row,index) in materialList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="物料名称：" prop='fname'>
            <xpt-input v-model='query.fname'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="商品组别：" prop='product_group'>
            <xpt-input v-model='query.product_group'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="数据状态：" prop='data_status'>
            <el-select v-model='query.data_status' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in receiptList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="物料分组：" prop='material_group'>
            <xpt-input v-model='query.material_group'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="存货类别：" prop='stock_cat'>
            <xpt-input v-model='query.stock_cat'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="禁用状态：" prop='disable_status'>
            <el-select v-model='query.disable_status' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in disableList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <!--<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>-->
        </el-col>
      </el-form>
    </el-row>
     <el-row :gutter='10' class='xpt-top' style="background:#ffffff;height: 30px;border-bottom: 1px solid #eeeeee">
        <span>物料</span>
      </el-row>
    <div>
      <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
      :maxHeight="'300'"
    >
      <template slot-scope="scope" slot='viewInfo'>
        <el-button @click.native.prevent="erpPicManage(scope.row)" type="text" size="small">查看物料安装图</el-button>
      </template>
    </xpt-list>
    </div>
    <div>
    <el-row :gutter='10' class='xpt-top' style="background:#ffffff;height: 30px;border-bottom: 1px solid #eeeeee">
      <span>店铺物料</span>
    </el-row>

    <xpt-list
      :showHead='false'
      :data='list_shop'
      :colData='cols_shop'
      selection=''
    ></xpt-list>
    </div>
    <xpt-image
        :images="imagesList"
        :show="ifShow"
        :ifUpload="false"
        :ifClose='false'
        @close="closeFun">
    </xpt-image>
  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this
      return {
        query: {
          page_no: 1,// 页码
          page_size: self.pageSize, // 页数
          fnumber:"",//物料编码
          fname:"",//物料名称
          material_group:"",//物料分组
          category_name:"",//商品类别名称
          product_group:"",//商品组别
          stock_cat:"",//存货类别
          material_attr:"",//物料属性
          data_status:"",//数据状态
          disable_status:""//禁用状态
        },
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        count:0,
        list:[],
        list_shop:[],
        cols: [
          {
            label: '物料编码',
            prop: 'fnumber',
            redirectClick(row) {
              self.queryDataChild(row.fnumber)
            },
            width: 120
          }, {
            label: '物料名称',
            prop: 'fname',
          }, {
            label: '规格描述',
            prop: 'spec',
          }, {
            label: '物料分组',
            prop: 'material_group'
          }, {
            label: '商品类别',
            prop: 'category',
          }, {
            label: '类别名称',
            prop: 'category_name',
          }, {
            label: '商品组别',
            prop: 'product_group'
          }, {
            label: '物料属性',
            prop: 'material_attr'
          }, {
            label: '存货类别',
            prop: 'stock_cat',
          }, {
            label: '基本单位',
            prop: 'unit'
          }, {
            label: '订单不可选',
            prop: 'not_order'
          }, {
            label: '赠品',
            prop: 'premiums'
          }, {
            label: '通用补件',
            prop: 'additional'
          }, {
            label: '组合自动拆分',
            prop: 'auto_split',
            width: 100
          }, {
            label: '是否停产',
            prop: 'stop'
          }, {
            label: '停产时间',
            prop: 'stop_date',
            format:"dataFormat1"
          }, {
            label: '启用批号管理',
            prop: 'batch_manage',
            width: 100
          }, {
            label: '包邮费',
            prop: 'postage'
          }, {
            label: '长度',
            prop: 'length1'
          }, {
            label: '宽度',
            prop: 'width1'
          }, {
            label: '高度',
            prop: 'height1'
          }, {
            label: '属性明细',
            prop: 'prosum'
          }, {
            label: 'BOM版本',
            prop: 'bom_version'
          }, {
            label: '仓库',
            prop: 'store_house'
          }, {
            label: '仓位',
            prop: 'store_position'
          }, {
            label: '数据状态',
            prop: 'data_status'
          }, {
            label: '禁用状态',
            prop: 'disable_status',
          }, {
            label:'物料安装图',
            fixed: 'right',
            width: 150,
            slot:'viewInfo',
            // bool: true
          }
        ],
        cols_shop:[
          {
            label: '淘宝商品ID',
            prop: 'tb_product_id'
          }, {
            label: 'sku_id',
            prop: 'tb_sku_id'
          }, {
            label: '店铺名称',
            prop: 'shop_name'
          }, {
            label: '商品标题',
            prop: 'title'
          }, {
            label: '淘宝链接',
            prop: 'tb_link'
          }, {
            label: '淘宝库存',
            prop: 'tb_qty'
          }, {
            label: '一口价',
            prop: 'fixed_price'
          }, {
            label: '下架',
            prop: 'soldout'
          }, {
            label: '承诺发货时间',
            prop: 'commit_date',
            width: 100
          }, {
            label: '上架时间',
            prop: 'puaway_dte',
            format:"dataFormat1",
            width: 130
          }, {
            label: '下架时间',
            prop: 'delist_time',
            format:"dataFormat1",
            width: 130
          }, {
            label: '修改时间',
            prop: 'modified_time',
            format:"dataFormat1",
            width: 130
          }, {
            label: '卖点',
            prop: 'sell_point'
          }, {
            label: '减库存',
            prop: 'sub_stock'
          }, {
            label: '包邮',
            prop: 'freight_payer'
          }, {
            label: '图片链接',
            prop: 'pic_link'
          }, {
            label: '产品定位',
            prop: 'prd_position'
          }, {
            label: '物料更新时间',
            prop: 'fentry_update_time',
            format: "dataFormat1",
            width: 130
          }, {
            label: '物料名称',
            prop: 'fname'
          }, {
            label: '物料编码',
            prop: 'fnumber',
            width: 110
          }, {
            label: '规格描述',
            prop: 'spec'
          }
        ],
        receiptList: [
          {
            name: '暂存',
            value: 'Z'
          }, {
            name: '创建',
            value: 'A'
          }, {
            name: '审核中',
            value: 'B'
          }, {
            name: '已审核',
            value: 'C'
          }, {
            name: '重新审核',
            value: 'D'
          }, {
            name: '请选择',
            value: ''
          }
        ],
        disableList:[{
          name: '正常',
          value: 'A'
        }, {
          name: '禁用',
          value: 'B'
        }],
        materialList: [
          {
            name: '外购',
            value: '1'
          }, {
            name: '自制',
            value: '2'
          }, {
            name: '委外',
            value: '3'
          }, {
            name: '配置',
            value: '9'
          }, {
            name: '资产',
            value: '10'
          }, {
            name: '特征',
            value: '4'
          }, {
            name: '费用',
            value: '11'
          }, {
            name: '虚拟',
            value: '5'
          }, {
            name: '服务',
            value: '6'
          }, {
            name: '一次性',
            value: '7'
          }, {
            name: '请选择',
            value: ''
          }
        ],
        imagesList: [],
        ifShow: false,
      }
    },
    methods: {
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            this.queryBtnStatus = true;
            this.ajax.postStream('/kingdee-web/api/materiel/pageMateriel', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.materiels || [];
                this.list_shop=content.shopMateriels||[];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      queryDataChild(fnumber) {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = {fnumber:fnumber};
            this.queryBtnStatus = true;
            this.ajax.postStream('/kingdee-web/api/materiel/getMaterielShopInfo', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list_shop = content || [];
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));

            this.exportBtnStatus = true;
            this.ajax.postStream('/order-web/api/report/exportAchievement', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      // 查看物料安装图信息
      erpPicManage (row) {
        this.ajax.get('/price-web/api/actPresentApplication/transferOaForNbo?materialId=' + row.fnumber, res => {
            if (res.body.result && res.body.content) {
              if (res.body.content.length > 0) {
                this.imagesList = res.body.content;
                this.imagesList.map(item => {
                  return item.src = item.path
                })
                this.ifShow = true
              } else {
                this.$message.info('此物料没有维护物料安装图')
              }
            } else {
              this.$message.error(res.body.msg)
            }
          }, err => {
            this.$message.error(err);
          })
      },
      closeFun() {
          this.ifShow = false;
      },
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{width: 150px;}
</style>
