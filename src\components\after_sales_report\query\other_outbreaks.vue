<!-- 其他出库单查询 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="单据状态：" prop='documentStatus'>
            <el-select v-model='query.documentStatus' label-width="150px"  size='mini'>
              <el-option v-for='(row,index) in receiptList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="源单编号：" prop='sourceBillNo'>
            <xpt-input v-model='query.sourceBillNo'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="接口状态：" prop='sendStatus'>
            <el-select v-model='query.sendStatus' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in interfaceList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="业务类型：" prop='bizType'>
            <el-select v-model='query.bizType'  size='mini'>
              <el-option v-for='(row,index) in bizTypeList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="物料编码：" prop='materialNumber'>
            <xpt-input v-model='query.materialNumber'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="买家昵称：" prop='buyerNick'>
            <el-input
              v-model="query.buyerNick"
              size='mini'
              icon="search"
              :on-icon-click="selectBuyersName"
            ></el-input>
           <!-- <xpt-input v-model='query.buyerNick'  size='mini' ></xpt-input>-->
          </el-form-item>

        </el-col>
        <el-col :span='6'>
          <el-form-item label="单据编号：" prop='billNo'>
            <xpt-input v-model='query.billNo'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="物料名称：" prop='materialName'>
            <xpt-input v-model='query.materialName'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="物流公司：" prop='logisticsCompany'>
            <xpt-input v-model='query.logisticsCompany'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="运输单号：" prop='logisticsNo'>
            <xpt-input v-model='query.logisticsNo'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <!--<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>-->
        </el-col>
      </el-form>
    </el-row>

    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>

  </div>
</template>
<script>
  import mixin from '../mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          page_no: 1,// 页码
          page_size: self.pageSize, // 页数
          begin_date:'',
          end_date:'',
          materialNumber:"",//物料编码
          materialName:"",//物料名称
          billNo:"",//单据编号
          bizType:"",//业务类型
          buyerNick:"",//买家昵称
          buyerNick_str:'',
          documentStatus:"",//单据状态
          logisticsCompany:"",//物流公司
          sendStatus:"",//接口状态
          sourceBillNo:"",//源单单号
          logisticsNo:''//运输单号
        },
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        count:0,
        list:[],
        cols: [
          {
            label: '单据状态',
            prop: 'documentStatus'
          }, {
            label: '业务类型',
            prop: 'bizType',
          }, {
            label: '单据编号',
            prop: 'billNo',
            redirectClick(row) {
              self.viewDetail(row.id)
            }
          }, {
            label: '日期',
            prop: 'date',
            format:"dataFormat"
          }, {
            label: '物料编码',
            prop: 'materialNumber',
          }, {
            label: '物料名称',
            prop: 'materialName',
          }, {
            label: '规格型号',
            prop: 'specification'
          }, {
            label: '商品类别',
            prop: 'category'
          }, {
            label: '单位',
            prop: 'unit',
          }, {
            label: '实发数量',
            prop: 'quantity'
          }, {
            label: '发货仓库',
            prop: 'storehouse'
          }, {
            label: 'BOM版本',
            prop: 'bomVersion'
          }, {
            label: '批号',
            prop: 'lot'
          }, {
            label: '买家昵称',
            prop: 'buyerNick'
          }, {
            label: '接口状态',
            prop: 'sendStatus'
          }, {
            label: '收货人',
            prop: 'receiverName'
          }, {
            label: '物流公司',
            prop: 'logisticsCompany'
          }, {
            label: '运输单号',
            prop: 'logisticsNo'
          }, {
            label: '运输成本',
            prop: 'logisticsFee'
          }
        ],
        receiptList: [
          {
            name: '暂存',
            value: 'Z'
          }, {
            name: '创建',
            value: 'A'
          }, {
            name: '审核中',
            value: 'B'
          }, {
            name: '已审核',
            value: 'C'
          }, {
            name: '重新审核',
            value: 'D'
          }, {
            name: '请选择',
            value: ''
          }
        ],
        interfaceList: [
          {
            name: '等待',
            value: '1'
          }, {
            name: '已下达',
            value: '2'
          }, {
            name: '作业中',
            value: '3'
          }, {
            name: '已完成',
            value: '4'
          }, {
            name: '已拔出',
            value: '5'
          }, {
            name: '已取消',
            value: '6'
          }, {
            name: '无下达',
            value: '7'
          }, {
            name: '下达中',
            value: '8'
          }, {
            name: '撤回中',
            value: '9'
          }, {
            name: '无需下传',
            value: 'A'
          }, {
            name: '请选择',
            value: ''
          }
        ],
        bizTypeList:[{
          name: '物料领用',
          value: '0'
        },{
          name: '资产领用',
          value: '1'
        },{
          name: 'VMI业务',
          value: '2'
        },{
          name: '费用物料领用',
          value: '3'
        },{
          name: '库存调整',
          value: '4'
        }]

      }
    },
    methods: {
      // 查看详情
      viewDetail(id){
        var params = {};
        params.id = id;
        this.$root.eventHandle.$emit('creatTab', {
          name:"其他出库单详情",
          params:params,
          component: () => import('@components/after_sales_report/query/other_outbreaks_detail')
        });
      },
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data['beginDate'] = +new Date(data.begin_date);
            data['endDate'] =+new Date(data.end_date);
            delete  data.buyerNick_str;
            delete data.begin_date;
            delete data.end_date;
            this.queryBtnStatus = true;
            this.ajax.postStream('/kingdee-web/api/delivery/pageDeliveryInfoList', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));

            this.exportBtnStatus = true;
            this.ajax.postStream('/order-web/api/report/exportAchievement', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{
    width: 150px;
  }
</style>


