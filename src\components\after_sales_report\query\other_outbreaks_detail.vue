<!-- 其他出库单详情 -->
<template>
  <div class="xpt-flex">
    <el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px">
      <el-tabs v-model="selectTab1">
        <el-tab-pane label='基本信息' name='basicInformation'>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="单据编号">
                <el-input size='mini' v-model="form.deliverybase.billNo" readonly></el-input>
              </el-form-item>
              <el-form-item label="单据类型">
                <el-input size='mini' v-model="form.deliverybase.billType" readonly></el-input>
              </el-form-item>
              <el-form-item label="业务类型">
                <el-input size='mini' v-model="form.deliverybase.bizType" readonly></el-input>
              </el-form-item>
              <el-form-item label="日期">
                <el-date-picker type="date"  size='mini'  v-model="form.deliverybase.date" readonly></el-date-picker>
                <!--<el-input size='mini' v-model="form.deliverybase.date" readonly></el-input>-->
              </el-form-item>
              <el-form-item label="仓管员">
                <el-input size='mini' v-model="form.deliverybase.stocker"  readonly></el-input>
              </el-form-item>
              <el-form-item label="备注">
                <el-input type='textarea' v-model="form.deliverybase.note" size='mini'  readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="客户">
                <el-input size='mini' v-model="form.deliverybase.customer"  readonly></el-input>
              </el-form-item>
              <el-form-item label="买家昵称">
                <el-input size='mini' v-model="form.deliverybase.buyerNick"  readonly></el-input>
              </el-form-item>
              <el-form-item label="领料部门">
                <el-input size='mini' v-model="form.deliverybase.pickDepatment"  readonly></el-input>
              </el-form-item>
              <el-form-item label="领料人">
                <el-input size='mini' v-model="form.deliverybase.picker"  readonly></el-input>
              </el-form-item>
              <el-form-item label="作废人">
                <el-input size='mini' v-model="form.deliverybase.cancelBy"  readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="创建人">
                <el-input size='mini'  v-model="form.deliverybase.creator" readonly></el-input>
              </el-form-item>
              <el-form-item label="最后修改人">
                <el-input size='mini'  v-model="form.deliverybase.modifier" readonly></el-input>
              </el-form-item>
              <el-form-item label="最后修改日期">
                <el-date-picker type="date"  size='mini'  v-model="form.deliverybase.modifyDate" readonly></el-date-picker>
              </el-form-item>
              <el-form-item label="审核人">
                <el-input size='mini' v-model="form.deliverybase.approver"  readonly></el-input>
              </el-form-item>
              <el-form-item label="审核日期">
                <el-date-picker type="date"  size='mini'  v-model="form.deliverybase.approveDate" readonly></el-date-picker>
            </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="单据状态">
                <el-input size='mini' v-model="form.deliverybase.fdocumentstatus"  readonly></el-input>
              </el-form-item>
              <el-form-item label="作废状态">
                <el-input size='mini' v-model="form.deliverybase.cancelStatus"  readonly></el-input>
              </el-form-item>
              <el-form-item label="接口状态">
                <el-input size='mini' v-model="form.deliverybase.sendStatus"  readonly></el-input>
              </el-form-item>
              <el-form-item label="下达时间">
                <el-date-picker type="datetime"  size='mini'  v-model="form.deliverybase.sendTime" readonly></el-date-picker>
              </el-form-item>
              <el-form-item label="传送结果提示">
                <el-input type='textarea'  v-model="form.deliverybase.sendInfo" size='mini'  readonly></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label='补件信息' name='otherInformation'>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="收货人">
                <el-input size='mini' v-model="form.deliveryAdditional.receiver" readonly></el-input>
              </el-form-item>
              <el-form-item label="联系电话">
                <el-input size='mini'  v-model="form.deliveryAdditional.mobile"  readonly></el-input>
              </el-form-item>
              <el-form-item label="详细地址">
                <el-input size='mini'  v-model="form.deliveryAdditional.address"  readonly></el-input>
              </el-form-item>
              <el-form-item label="提货点">
                <el-input size='mini'  v-model="form.deliveryAdditional.getGoodsPoint"  readonly></el-input>
              </el-form-item>
              <el-form-item label="物流公司">
              <el-input size='mini' v-model="form.deliveryAdditional.logisticsCompany" ></el-input>
            </el-form-item>
              <el-form-item label="提货点电话">
                <el-input size='mini'  v-model="form.deliveryAdditional.getGoodsPointPhone"  readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="三包点">
                <el-input size='mini' v-model="form.deliveryAdditional.threeGuaranteesPoint"  readonly></el-input>
              </el-form-item>
              <el-form-item label="三包公司">
                <el-input size='mini'  v-model="form.deliveryAdditional.threeGuaranteesCompany"  readonly></el-input>
              </el-form-item>
              <el-form-item label="三包点电话">
                <el-input size='mini'  v-model="form.deliveryAdditional.threeGuaranteesPointPhone"  readonly></el-input>
              </el-form-item>
              <el-form-item label="运费类型">
                <el-input size='mini' v-model="form.deliveryAdditional.payType"   readonly></el-input>
              </el-form-item>
              <el-form-item label="运输方式">
                <el-input size='mini' v-model="form.deliveryAdditional.transportMode"   readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="快递地区">
                <el-input size='mini' v-model="form.deliveryAdditional.expressArea"  readonly ></el-input>
              </el-form-item>
              <el-form-item label="实际货物重量" readonly>
                <el-input size='mini' v-model="form.deliveryAdditional.weight"  readonly ></el-input>
              </el-form-item>
              <el-form-item label="预估费用">
              <el-input size='mini'  v-model="form.deliveryAdditional.estimateFreight"  readonly></el-input>
            </el-form-item>
              <el-form-item label="运输单号">
                <el-input size='mini' v-model="form.deliveryAdditional.logisticsNo"   readonly></el-input>
              </el-form-item>
              <el-form-item label="运输成本">
                <el-input size='mini' v-model="form.deliveryAdditional.freightCost"   readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结算方式">
                <el-input size='mini' v-model="form.deliveryAdditional.settlement"  readonly ></el-input>
              </el-form-item>
              <el-form-item label="发货时间" readonly>
                <el-date-picker type="date"  size='mini'  v-model="form.deliveryAdditional.sendDate" readonly></el-date-picker>
               </el-form-item>
              <el-form-item label="发货物流">
                <el-input size='mini' v-model="form.deliveryAdditional.logistics"   readonly></el-input>
              </el-form-item>
              <el-form-item label="发货件数">
                <el-input size='mini'  v-model="form.deliveryAdditional.goodsQuantity"  readonly></el-input>
              </el-form-item>
              <el-form-item label="实际发货人">
                <el-input size='mini'  v-model="form.deliveryAdditional.realReceiver"  readonly></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <el-row class='xpt-flex__bottom'>
      <el-tabs v-model="selectTab2">
        <el-tab-pane label='明细' name='goodsInfo' class='xpt-flex'>
          <xpt-list
            :data='detailList'
            :colData='detailCol'
            :showHead="false"
            :orderNo="true"
            selection=''
          >
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
  import VL from '@common/validate.js';

  export default {
    props:['params'],
    data (){
      var self=this;
      return {
        rules:{},
        detailList:[],
        detailCol:[
          {
            label: '物料编码',
            prop: 'materialNumber',
          },{
            label: '物料名称',
            prop: 'materialName',
          },{
            label: '规格型号',
            prop: 'specification',
          },{
            label: '商品类别',
            prop: 'category',
          },{
            label: '单位',
            prop: 'unit',
          },{
            label: '实发数量',
            prop: 'quantity',
          },{
            label: '发货仓库',
            prop: 'storehouse',
          },{
            label: '批号',
            prop: 'lot',
          },{
            label: '产品组',
            prop: 'productGroup',
          },{
            label: 'BOM版本',
            prop: 'bomVersion',
          },{
            label: '申请需求描述',
            prop: 'applyDescription',
          },{
            label: '工厂代发',
            prop: 'factoryBehalf',
          },{
            label: '包件数',
            prop: 'packageQuantity',
          },{
            label: '体积',
            prop: 'volume',
          },{
            label: '源单类型',
            prop: 'sourceBillType',
          },{
            label: '源单编号',
            prop: 'sourceBillNo',
          },{
            label: '售后单号',
            prop: 'afterBillNo',
          }
        ],

        selectTab1: 'basicInformation',
        selectTab2: 'goodsInfo',
        form: {
          deliveryAdditional:{},//补件信息
          deliverybase:{}//基本信息
        },

      }
    },
    methods: {
      //查询详情
      getDetailInfo(id){
        var self=this;
        var data={
          id: id
        }
        self.ajax.postStream('/kingdee-web/api/delivery/pageDeliveryInfo',data,function(response){
          if(response.body.result){
              //补件信息
              self.form.deliveryAdditional=response.body.content.deliveryAdditional;
              //基本信息
              self.form.deliverybase=response.body.content.deliverybase;
            //明细
            self.detailList=response.body.content.deliveryDetailList||[];
          }else{
            self.$message.error(response.body.msg)
          }
        },e=>{
          self.$message.error(e)
        })
      }

    },
    mounted(){
      this.getDetailInfo(this.params.id);
    }
  }
</script>

<style module>
  .btn-select {
    width: 109px;
    vertical-align: middle;
  }
  .btn-select input {
    color: #fff;
    background-color: #20a0ff;
    border-color: #20a0ff;
  }
  .btn-select :global(.el-input__inner){
    height: 18px;
  }
  .textarea-style :global(.el-form-item__content) {
    width: 180px;
    height: auto;
  }
</style>

