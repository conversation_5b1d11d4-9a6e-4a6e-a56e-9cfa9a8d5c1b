<!-- 拼多多物料列表查询 -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='10' class='xpt-top'>
		<el-form  :model='serchData' label-position="right" label-width="120px">
			<el-col :span="12" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='Refresh' :disabled="refreshDisabled">刷新</el-button>
				<!-- <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br> -->
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>
	<xpt-list
        :data='list'
        :colData='cols'
        :pageTotal='pageTotal'
        :showHead="false"
        :orderNo="true"
        selection=''
        @page-size-change='pageChange'
        @current-page-change='currentPageChange'
    ></xpt-list>
</div>
</template>

<script>
import VL from '@common/validate.js'
export default {
	data (){
		return {
			serchData: {
                material_source:"PDD",
				page_size: 50,
				page_no: 1,
				
			},
			pageTotal: 0,
            list: [],
            // 是否禁掉刷新按钮
            refreshDisabled:false,
			cols: [{
				label: '叶子类目ID',
				prop: 'cat_id',
			},{
				label: 'ID',
				prop: 'id',
			},{
				label: '标题',
				prop: 'goods_name',
				
			},{
				label: '商品上下架状态',
				prop: 'status_name',
			},{
				label: '宝贝ID',
				prop: 'goods_id',
			},{
				label: 'SKU ID',
				prop: 'sku_id',
			},{
				label: '一口价/SKU属性',
                prop: 'spec_name',
                width:260
			},{
				label: '商家编码',
				prop: 'material_number',
			},{
				label: 'SKU上下架状态',
				prop: 'is_onsale_name',
				
			},{
				label: '库存',
				prop: 'quantity',
			},{
				label: '单买价',
				prop: 'price',
				width: '150',
			},{
				label: '拼团价',
				prop: 'multi_price',
				width: '150',
			},{
				label: '市场价',
				prop: 'market_price',
				width: '150',
			},{
				label: '承诺发货时间（小时）',
				prop: 'shipment_limit_hour',
			},{
				label: '运费模板名字',
				prop: 'cost_template_name',
			},{
				label: '创建时间',
                prop: 'create_time',
                format: 'dataFormat1',
			},{
				label: '最后修改时间',
                prop: 'last_modify_time',
                format: 'dataFormat1',
			}],

		}
	},
	methods: {
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_PDD_MATERIAL',
					},
				},
			})
		},
		//对页面需要认证的内容认证，required：是否必填，msg：提示信息
		VLFun(required, msg){
			return VL.isNotBlank({
				required:required,
				self:this,
				msg:msg,
			})
		},
		exportExcel (e){
			var $btn = e.target

			$btn.disabled = true
			this.ajax.postStream('/order-web/api/externalMaterial/exportExternalMaterial', Object.assign({}, {"material_source":"PDD"}, {
				page_size: 200000,
				page_no: 1,
			}), res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				$btn.disabled = false
			}, () => {
				$btn.disabled = false
			})
		},
		
		// search (keyword){
		// 	this.serchData.page_no = 1
		// 	this.serchData.code = keyword
		// 	this._getList()
		// },
		pageChange (pageSize){
			this.serchData.page_size = pageSize
			// this.serchData.page_no = 1
			this._getList()
		},
		currentPageChange (page){
			this.serchData.page_no = page
			this._getList()
		},  
		_getList (resolve, msg){
           
			this.ajax.postStream('/order-web/api/externalMaterial/list', this.serchData, res => {
                
				if(res.body.result){
					this.$message.success(msg || res.body.msg)
					this.list = res.body.content.list || []
					this.pageTotal = res.body.content.count
				}else {
					this.list = []
					this.pageTotal = 0
					this.$message.error(res.body.msg)
				}
			})
        },
        Refresh(resolve, msg){
            this.refreshDisabled = true;
            this.ajax.postStream('/order-web/api/externalMaterial/refresh', this.serchData, res => {
                this.refreshDisabled = false;
				if(res.body.result){
					// this.$message.success(msg || res.body.msg)
					// this.list = res.body.content.list || []
                    // this.pageTotal = res.body.content.count
                    this._getList()
				}else {
					// this.list = []
					// this.pageTotal = 0
					this.$message.error(res.body.msg)
				}
			})
        }
	},
	mounted (){
		this._getList()
	},
}
</script>