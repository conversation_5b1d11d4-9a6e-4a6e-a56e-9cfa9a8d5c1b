<!-- 价目表报表查询 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="物料编码" prop='material_code'>
            <xpt-input v-model='query.material_code'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="淘宝商品ID" prop='goods_id'>
            <xpt-input v-model='query.goods_id'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="生效时间：" prop='enable_date'>
            <el-date-picker v-model="query.enable_date" type="datetime"  placeholder="选择日期" size='mini'   :picker-options='enableDateOptions'></el-date-picker>
            <!-- <el-tooltip v-if='rules.enable_date[0].required' class="item" effect="dark" :content="rules.enable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip> -->
          </el-form-item>
          <el-form-item label="失效时间：" prop='disable_date'>
            <el-date-picker v-model="query.disable_date" type="datetime" placeholder="选择日期" size='mini' :editable='false'  :default-time="['00:00:00']"  :picker-options='disableDateOptions'></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="行状态" prop="disabled_status">
            <el-select v-model='query.disabled_status' label-width="150px"  size='mini'>
              <el-option key='1' label='失效' value='1'></el-option>
              <el-option key='0' label='生效' value='0'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="物料名称" prop='material_name'>
            <xpt-input v-model='query.material_name'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="价目表编码：" prop='price_list_code'>
            <xpt-input v-model='query.price_list_code'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="价目表名称：" prop='price_list_name'>
            <xpt-input v-model='query.price_list_name'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="价目表分类：" prop='sale_profit_class'>
            <xpt-select-aux v-model='query.code' aux_name='sale_profit_class'></xpt-select-aux>
          </el-form-item>
          <el-form-item label="币别：" prop='currency_code'>
            <xpt-select-aux v-model='query.currency_code' aux_name='currency'></xpt-select-aux>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="店铺范围：" prop='store_area'>
            
            <el-select v-model='query.store_area' label-width="150px"  size='mini'>
              <el-option v-for='(row,index) in store_area_type' :key='index' :label='row.name' :value='row.type'></el-option>
            </el-select>
                
          </el-form-item>
          <el-form-item label="店铺：" prop='shop_name'>
          <xpt-input v-model='query.shop_name' icon='search' :on-icon-click='openShop2' size='mini' @change='shopChange'></xpt-input>
        </el-form-item>
          <el-form-item label="价目表类型：" prop='price_list_type'>
            
            <el-select v-model='query.price_list_type' label-width="150px"  size='mini'>
              <el-option v-for='(row,index) in price_type' :key='index' :label='row.name' :value='row.type'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="价目表状态" prop="status">
            <xpt-select-aux v-model='query.status' aux_name='priceListStatus'></xpt-select-aux>
            <!-- <el-select v-model='query.status' label-width="150px"  size='mini'>
              <el-option v-for='(row,index) in price_type' :key='index' :label='row.name' :value='row.type'></el-option>
            </el-select> -->
          </el-form-item>
          
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button>
          <!-- <el-button type='primary' size='mini' @click='queryEXel' >导出报表</el-button> -->
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
        </el-col>
      </el-form>
    </el-row>

    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>

  </div>
</template>
<script>
  import mixin from '../mixin.js'
  import Fn from '@common/Fn.js'
  import validate from '@common/validate.js';
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          currency_code:'',
          material_code: "",//物料编码
          material_name:"",//物料名称
          store_area:"-1",//店铺范围
          shop_id:"",//店铺id
          shop_name:"",//店铺名称
          price_list_type:'',//价目表类型
          enable_date:"",//生效时间
          disable_date:"",//失效时间
          price_list_code:"",//价目表编码
          price_list_name:"",//价目表名称
          disabled_status: '', // 行状态
          page: {
            length: self.pageSize,
            pageNo: 1
          },
          status:'',
          code:'',
          goods_id: '',// 淘宝商品id
        },
        enableDateOptions:{},
        disableDateOptions:{},
        rules:{
					enable_date:validate.isNotBlank({
						required:true,
						self:self
          }),
          disable_date:validate.isNotBlank({
						required:true,
						self:self
					}),
        },
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        store_area_type:[
          {
            name:'全部',
            type:'-1'
          },
          {
            name:'全局',
            type:'1'
          },
          {
            name:'店铺',
            type:'0'
          },
        ],
        count:0,
        list:[],
        cols: [
          {
            label: '物料编码',
            prop: 'material_code'
          }, {
            label: '物料名称',
            prop: 'material_name',
          }, {
            label: '规格描述',
            prop: 'material_specification',
          }, {
            label: '淘宝商品ID',
            prop: 'goods_id',
          }, {
            label: '单位',
            prop: 'material_unit',
          }, {
            label: '价格',
            prop: 'price',
            sort: true
          }, {
            label: '币别',
            prop: 'currency_code',
            format: 'auxFormat',
            formatParams: 'currency',
          }, {
            label: '生效时间',
            prop: 'enable_date',
            formatter(val){
              return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss')
            },
            sort: true
          }, {
            label: '失效时间',
            prop: 'disable_date',
            formatter(val){
              return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss')
            }
          }, {
            label: '行状态',
            prop: 'disabled_status',
            formatter(val){
              switch(val){
                case 1:   return '失效';break;
                case 0:   return '生效';break;
              }
              
            }
          }, {
            label: '价目表名称',
            prop: 'price_list_name',
          }, {
            label: '价目表类型',
            prop: 'price_list_type',
            sort: true,
            formatter(val){
              switch(val){
                case 'CATALOG_LIST':   return '目录价格';break;
                case 'STANDARD_LIST':   return '标准价格';break;
                case 'PREFERENTIAL_LIST':   return '活动价格';break;
                case 'SETTLE_LIST':   return '经销结算价格';break;
                case 'EXHIBITION_LIST':   return '摆场价格';break;
                case 'HARD_DECORATION_LIST':   return '硬装采集价格';break;
                case 'DIRECT_DESCENT_LIST':   return '直降价目';break;
                case 'REFERENCE_RETAIL_LIST':   return '参考零售价目';break;
                case 'PACKAGED_ITEMS_LIST': return '整装价目';break;
           
              }
            }
          }, {
            label: '店铺范围',
            prop: 'store_area',
            formatter(val){
              switch(val){
                case 1:   return '全局';break;
                case 0:   return '店铺';break;
                case -1:   return '全部';break;
              }
              
            }
          }, {
            label: '影响日期起',
            prop: 'effect_date_start',
            formatter(val){
              return Fn.dateFormat(val)
            }
          }, {
            label: '影响日期止',
            prop: 'effect_date_end',
            formatter(val){
              return Fn.dateFormat(val)
            }
          }, {
            label: '价目表编码',
            prop: 'price_list_code'
          }, {
            label: '价目表状态',
            prop: 'status',
            format: 'auxFormat',
            formatParams: 'priceListStatus',
          }, {
            label: '审核时间',
            prop: 'audit_time',
            formatter(val){
              return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss')
            }
          }, {
            label: '创建时间',
            prop: 'create_time',
            formatter(val){
              return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss')
            }
          }
        ],
        price_type:[
          {
            name:'目录价格',
            type:'CATALOG_LIST'
          },
          {
            name:'标准价格',
            type:'STANDARD_LIST'
          },
          {
            name:'活动价格',
            type:'PREFERENTIAL_LIST'
          },
          {
            name:'经销结算价格',
            type:'SETTLE_LIST'
          },
          {
            name:'摆场价格',
            type:'EXHIBITION_LIST'
          },
          {
            name:'硬装采集价格',
            type:'HARD_DECORATION_LIST'
          },
          {
            name:'直降价目',
            type:'DIRECT_DESCENT_LIST'
          },
          {
            name:'滞销品价目',
            type:'UNSALABLE_GOODS_LIST'
          },
          {
            name:'参考零售价目',
            type:'REFERENCE_RETAIL_LIST'
          }
        ]

      }
    },
    methods: {
      // 查看详情
      
      reset() {
        for(let v in this.query) {
          if(v !== 'page') {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page.length = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query['page'].pageNo = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            let todayTime = new Date().getTime();
            // 获取六个月前的时间
            let sixMonth = new Date(todayTime - (1000*60*60*12*30*6));
            // data['enable_date'] = +new Date(data.enable_date);
            // console.log(data)
            data['disable_date']=='' ?   data['disable_date']='' : data['disable_date']= +new Date(data.disable_date);
            data['enable_date']=='' ?   data['enable_date']='' : data['enable_date']= +new Date(data.enable_date);
            if(data['disable_date'] == '' && data['enable_date'] != ''){
            //  console.log(sixMonth,data['enable_date'] )
              if( data['enable_date']<new Date(sixMonth).getTime()){
                this.$message.error('请输入失效时间');
                return false;
              }
            }
            if( data['enable_date'] == ''){
            if(data['price_list_name'] == '' && data['price_list_code'] == ''){
            this.$message.error('请输入生效时间')
            return false;
            }
            }
              
            
            // console.log('今天时间',data['disable_date'],data['enable_date'])
            if(data['disable_date'] !== '' && data['disable_date'] < data['enable_date']){
              this.$message.error('失效时间必须大于生效时间')
              return false;
            }
            delete  data.buyerNick_str;
            delete data.begin_date;
            delete data.end_date;
            delete data.shop_name;
            this.queryBtnStatus = true;
            console.log(data)
            this.ajax.postStream('/price-web/api/price/getPriceBordereaux', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
                this.$message.success(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel(e) {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            delete data.shop_name;
            this.exportBtnStatus = true;
            this.ajax.postStream('/price-web/api/price/exportPriceBordereaux', data, res => {
              this.exportBtnStatus = false;
              console.log(res)
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
                console.log(err)
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      open4(msg) {
        this.$msgbox({
          title: '注意',
          message: msg,
          showCancelButton: true,
          confirmButtonText: '确定',
          
        }).then(action => {
          this.$message({
            type: 'info',
            message: 'action: ' + action
          });
        });
      },
      enable_date() {
			// 当this.query.begin_date值更新时，触发begin_date值的更新
			return this.query.enable_date;
      },
      disable_date() {
        return this.query.disable_date;
      },
      openPriceError(msg) {
        this.$msgbox({
          title: '注意',
          message: msg,
          showCancelButton: true,
          confirmButtonText: '确定',
          
        })
      }
    },
    watch: {
		enable_date(n, o) {
      console.log(n,o)
			this.disableDateOptions = {
				disabledDate(time) {
					// 设置结束时间的失效时间为小于开始时间
					return time.getTime() < +new Date(n);
				}
			}
		},
		disable_date(n, o) {
			this.enableDateOptions = {
				disabledDate(time) {
					return time.getTime() > +new Date(n);
				}
			}
		},
	
	},
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{
    width: 150px;
  }
</style>


