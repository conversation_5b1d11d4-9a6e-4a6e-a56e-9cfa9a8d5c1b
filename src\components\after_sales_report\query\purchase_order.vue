<!-- 采购订单查询 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="单据编号：" prop='billno'>
            <xpt-input v-model='query.billno'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="单据状态：" prop='documentstatus'>
            <el-select v-model='query.documentstatus' size='mini'>
              <el-option v-for='(row,index) in receiptList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="规格描述：" prop='specification'>
            <xpt-input v-model='query.specification'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="采购开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="物料编码：" prop='materialNumber'>
            <xpt-input v-model='query.materialNumber'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="物料名称：" prop='materialName'>
            <xpt-input v-model='query.materialName'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="采购结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="供应商：" prop='supplierNumber'>
            <xpt-input v-model='query.supplierNumber'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="买家昵称：" prop='customerNick'>
            <xpt-input v-model='query.customerNick'  size='mini' ></xpt-input>
          </el-form-item>

        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <!--<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>-->
        </el-col>
      </el-form>
    </el-row>

      <xpt-list
        :showHead='false'
        :data='list'
        :colData='cols'
        :pageTotal='count'
        selection=''
        @page-size-change='pageSizeChange'
        @current-page-change='currentPageChange'
      ></xpt-list>

  </div>
</template>
<script>
  import mixin from '../mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          page_no: 1,// 页码
          page_size: self.pageSize, // 页数
          begin_date:'',
          end_date:'',
          billno:"",//采购订单编号
          documentstatus:"",//单据状态
          materialName:"",//物料名称
          materialNumber:"",//物流编码
          specification:"",//规格描述
          supplierNumber:"",//供应商
          customerNick:"",//买家昵称
        },
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        count:0,
        list:[],
        cols: [
          {
            label: '单据编号',
            prop: 'billno',
            redirectClick(row) {
              self.viewDetail(row.fid)
            }
          }, {
            label: '采购日期',
            prop: 'fdate',
            format:"dataFormat"

          }, {
            label: '供应商',
            prop: 'supplierNumber',
          }, {
            label: '单据状态',
            prop: 'documentstatus'
          }, {
            label: '物料编码',
            prop: 'materialNumber',
          }, {
            label: '物料名称',
            prop: 'materialName',
          }, {
            label: '规格描述',
            prop: 'specification'
          }, {
            label: '单位',
            prop: 'fUnit'
          }, {
            label: '数量',
            prop: 'fQty',
          }, {
            label: 'BOM版本',
            prop: 'fBom'
          }, {
            label: '累计入库数量',
            prop: 'stockinQty'
          }, {
            label: '剩余入库数量',
            prop: 'remainStockinQty'
          }, {
            label: '累积上架数量',
            prop: 'onSaleQty'
          }, {
            label: '未上架数量',
            prop: 'notOnSaleQty'
          }, {
            label: '检验数量',
            prop: 'checkQty'
          }, {
            label: '累积合格数',
            prop: 'passQty'
          }
        ],
        receiptList: [
          {
            name: '暂存',
            value: 'Z'
          }, {
            name: '创建',
            value: 'A'
          }, {
            name: '审核中',
            value: 'B'
          }, {
            name: '已审核',
            value: 'C'
          }, {
            name: '重新审核',
            value: 'D'
          }, {
            name: '请选择',
            value: ''
          }
        ],

      }
    },
    methods: {
      // 查看详情
      viewDetail(fid){
       var params = {};
          params.fid = fid;
          this.$root.eventHandle.$emit('creatTab', {
            name:"采购单详情",
            params:params,
            component: () => import('@components/after_sales_report/query/purchase_order_detail')
          });
      },
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data['beginDate'] = +new Date(data.begin_date);
            data['endDate'] =+new Date(data.end_date) + 1000 * 60 * 60 * 24 - 1000;//比如选中2018-05-08，则正确结束日期是：2018-05-08 23:59:59
            delete data.begin_date;
            delete data.end_date;
            this.queryBtnStatus = true;
            this.ajax.postStream('/kingdee-web/api/forder/pageFOrderInfoList', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));

            this.exportBtnStatus = true;
            this.ajax.postStream('/order-web/api/report/exportAchievement', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{
    width: 150px;
  }
</style>

