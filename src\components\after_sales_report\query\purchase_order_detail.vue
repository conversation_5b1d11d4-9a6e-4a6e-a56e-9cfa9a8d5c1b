<!-- 采购单详情 -->
<template>
  <div class="xpt-flex">
    <el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px">
      <el-tabs v-model="selectTab1">
        <el-tab-pane label='基本信息' name='basicInformation'>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="单据类型">
                <el-input size='mini' v-model="form.forderBase.fbillType"  readonly></el-input>
              </el-form-item>
              <el-form-item label="业务类型">
                <el-input size='mini' v-model="form.forderBase.fbusinessType"    readonly></el-input>
              </el-form-item>
              <el-form-item label="单据编号">
                <el-input size='mini'  v-model="form.forderBase.fbillno"   readonly></el-input>
              </el-form-item>
              <el-form-item label="采购日期">
                <el-date-picker type="date"  size='mini'  v-model="form.forderBase.fdate" readonly></el-date-picker>
              </el-form-item>

            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商">
                <el-input size='mini' v-model="form.forderBase.fsupplier"    readonly></el-input>
              </el-form-item>
              <el-form-item label="采购部门">
                <el-input size='mini'  v-model="form.forderBase.fpurchaseDepament"   readonly></el-input>
              </el-form-item>
              <el-form-item label="采购组">
                <el-input size='mini' v-model="form.forderBase.fpurchaserGroup"    readonly></el-input>
              </el-form-item>
              <el-form-item label="采购员">
                <el-input size='mini'  v-model="form.forderBase.fpurchaser"   readonly></el-input>
              </el-form-item>
              <el-form-item label="送货单号">
                <el-input size='mini'v-model="form.forderBase.fdeliveryNumber"  readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单据状态">
                <el-input size='mini' v-model="form.forderBase.fdocumentStatus"    readonly></el-input>
              </el-form-item>
              <el-form-item label="接口状态">
                <el-input size='mini'  v-model="form.forderBase.sendStatus"   readonly></el-input>
              </el-form-item>
              <el-form-item label="下达时间">
                <el-date-picker type="date"  size='mini'  v-model="form.forderBase.sendTime" readonly></el-date-picker>

              </el-form-item>
              <el-form-item label="源单类型">
                <el-input size='mini' v-model="form.forderBase.fsrcbillType"    readonly></el-input>
              </el-form-item>
              <el-form-item label="源单单号">
                <el-input size='mini'  v-model="form.forderBase.fsrcBillNo"   readonly></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label='其他' name='otherInformation'>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="创建人">
                <el-input size='mini' v-model="form.forderBase.fcreator"    readonly></el-input>
              </el-form-item>
              <el-form-item label="创建日期">
                <el-date-picker type="date"  size='mini'  v-model="form.forderBase.fcreateDate" readonly></el-date-picker>
               </el-form-item>
              <el-form-item label="最后修改日期">
                <el-date-picker type="date"  size='mini'  v-model="form.forderBase.fmodifyDate" readonly></el-date-picker>
               </el-form-item>

            </el-col>
            <el-col :span="8">
              <el-form-item label="关闭状态">
                <el-input size='mini' v-model="form.forderBase.fcloseStatus"    readonly></el-input>
              </el-form-item>
              <el-form-item label="关闭日期">
                <el-date-picker type="date"  size='mini'  v-model="form.forderBase.fcloseDate" readonly></el-date-picker>
                </el-form-item>
              <el-form-item label="作废状态">
                <el-input size='mini' v-model="form.forderBase.fcancelStatus"    readonly></el-input>
              </el-form-item>

            </el-col>
            <el-col :span="8">
              <el-form-item label="审核人">
                <el-input size='mini' v-model="form.forderBase.fapprover"   readonly ></el-input>
              </el-form-item>
              <el-form-item label="审核日期" readonly>
                <el-date-picker type="date"  size='mini'  v-model="form.forderBase.fapproveDate" readonly></el-date-picker>
              </el-form-item>
              <el-form-item label="作废日期">
                <el-date-picker type="date"  size='mini'  v-model="form.forderBase.fcancelDate" readonly></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <el-row class='xpt-flex__bottom'>
      <el-tabs v-model="selectTab2">
        <el-tab-pane label='明细' name='goodsInfo' class='xpt-flex'>
          <xpt-list
            :data='detailList'
            :colData='detailCol'
            :showHead="false"
            :orderNo="true"
            selection=''
          >
          </xpt-list>
        </el-tab-pane>
        <el-tab-pane label='交货信息' name='rejectInfo' class='xpt-flex'>
          <xpt-list
            :data='goodsList'
            :colData='goodsCol'
            :showHead="false"
            :orderNo="true"
            selection=''
          >
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
  import VL from '@common/validate.js';

  export default {
    props:['params'],
    data (){
      var self=this;
      return {
        rules:{},
        detailList:[],
        detailCol:[
          {
            label: '物料编码',
            prop: 'materialNumber',
          },{
            label: '物料名称',
            prop: 'materialName',
          },{
            label: '规格描述',
            prop: 'specification',
          },{
            label: '物料分组',
            prop: 'materialGroup',
          },{
            label: '单位',
            prop: 'unit',
          },{
            label: '数量',
            prop: 'fQty',
          },{
            label: '未上架数量',
            prop: 'notOnSaleQty',
          },{
            label: '交货日期',
            prop: 'deliveryDate',
            format:"dataFormat"
          },{
            label: '仓库',
            prop: 'storehouse',
          },{
            label: 'BOM版本',
            prop: 'bomVersion',
          },{
            label: '问题商品批号',
            prop: 'problemGoodsLot',
          },{
            label: '合并单号',
            prop: 'mergeBillNo',
          },{
            label: '批次单号',
            prop: 'batchBillNo',
          },{
            label: '买家昵称',
            prop: 'customerNick',
          },{
            label: '免费补件',
            prop: 'freeSupplement',
          },{
            label: '收货人',
            prop: 'receiver',
          },{
            label: '电话',
            prop: 'phoneNumber',
          },{
            label: '详细地址',
            prop: 'adress',
          },{
            label: '申请需求描述',
            prop: 'applyDescription',
          },{
            label: '批号',
            prop: 'lot',
          },{
            label: '备注',
            prop: 'note',
          }
        ],
        goodsList:[],
        goodsCol:[
          {
            label: '交货日期',
            prop: 'fDeliverydate',
            format:"dataFormat"
          },{
            label: '单位',
            prop: 'fPlanUnit',
          },{
            label: '数量',
            prop: 'fplanQty',
          },{
            label: '已交货数量',
            prop: 'fDeliCommitQty',
          },{
            label: '剩余数量',
            prop: 'fDeliRemainQty',
          },{
            label: '供应商发货日期',
            prop: 'fSupplierDeliveryDate',
            format:"dataFormat"
          }
        ],
        selectTab1: 'basicInformation',
        selectTab2: 'goodsInfo',
        form: {
          forderBase:{}
        },

      }
    },
    methods: {
      //查询详情
      getDetailInfo(fid){
        var self=this;
        var data={
          fid: fid
        }
        self.ajax.postStream('/kingdee-web/api/forder/pageFOrderInfo',data,function(response){
          if(response.body.result){
            //基本信息和其他
            self.form.forderBase=response.body.content.forderBase||{};
            //交货信息
            self.goodsList=response.body.content.forderDelieries||[];
            //明细
            self.detailList=response.body.content.forderDetails||[];
          }else{
            self.$message.error(response.body.msg)
          }
        },e=>{
          self.$message.error(e)
        })
      }

    },
    mounted(){
      this.getDetailInfo(this.params.fid);
    }
  }
</script>

<style module>
  .btn-select {
    width: 109px;
    vertical-align: middle;
  }
  .btn-select input {
    color: #fff;
    background-color: #20a0ff;
    border-color: #20a0ff;
  }
  .btn-select :global(.el-input__inner){
    height: 18px;
  }
  .textarea-style :global(.el-form-item__content) {
    width: 180px;
    height: auto;
  }
</style>

