<!-- NBO待办推送查询列表 -->
<template>
    <div class='xpt-flex' id="pushtodoList">
        <el-row :gutter='10' class='xpt-top'>
            <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="130px">
				<el-col :span='5'>
                    <el-form-item label="事件类型：" prop='itemType'>
                        <xpt-select-aux v-model='query.itemType' aux_name='todo_item_type' clearable></xpt-select-aux>
						<el-select placeholder="请选择" size='mini' v-model="query.itemType" clearable>
							<el-option label="是" value="Y"></el-option>
							<el-option label="否" value="N"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="状态：" prop='status'>
						<el-select v-model='query.status' placeholder="请选择" label-width="150px"  size='mini' clearable>
							<el-option v-for='(row,index) in discountStatus' :key='index' :label='row.name' :value='row.code'></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="概要：" prop='summaryMatte'>
						<xpt-input v-model='query.summaryMatte'  size='mini' ></xpt-input>
					</el-form-item>
				</el-col>
				<el-col :span='5'>
					<el-form-item label="业务员：" prop='processPersonName'>
						<xpt-input v-model='query.processPersonName'  size='mini' ></xpt-input>
						<!-- <xpt-input v-model='query.staff' icon='search' :on-icon-click='openSatff' size='mini' @change='staffChange'></xpt-input> -->
					</el-form-item>
					<el-form-item label="业务员分组：" prop='groupName'>
						<xpt-input v-model='query.groupName'  size='mini' ></xpt-input>
						<!-- <xpt-input v-model='query.staff_group_name'  size='mini' ></xpt-input> -->
					</el-form-item>
				</el-col>
				<el-col :span='5'>
					<el-form-item label="创建时间：" required>
						<el-date-picker
							v-model="datePicker"
							type="daterange"
							@change="dateChange"
							placeholder="选择日期范围">
						</el-date-picker>
						<!-- <el-tooltip v-if='rules.create_time[0].isShow' class="item" effect="dark" :content="rules.create_time[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip> -->
					</el-form-item>
                    <el-form-item label="紧急程度：" prop='priority'>
						<el-select placeholder="请选择" size='mini' v-model="query.priority" clearable>
							<el-option label="立即解决" value="IMMEDIATE"></el-option>
							<el-option label="紧急" value="URGENT"></el-option>
							<el-option label="高" value="HIGH"></el-option>
							<el-option label="中" value="NORMAL"></el-option>
							<el-option label="低" value="LOW"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="9" class='xpt-align__right'>
                    <el-form-item label="">
						<el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
					</el-form-item>
					<el-form-item label="">
						<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button>
					</el-form-item>
				</el-col>
            </el-form>
        </el-row>
        <xpt-list
          :showHead='false'
          :data='list'
          :colData='cols'
          :pageTotal='count'
          selection=''
          @page-size-change='pageSizeChange'
          @current-page-change='currentPageChange'>
        </xpt-list>
    </div>
</template>
<script>
  import validate from '@common/validate.js';
  export default {
		props: ['params'],
		data() {
			let self = this
			return {
				query: {
					// staff: '',
					processPersonName: '',
					// groupId: '',
					groupName: '',
                    priority: '',
                    summaryMatte: '',
                    itemType: '',
                    status: '',
                    endDate: '',
                    startDate: '',
					page: {
						length: 50,
						pageNo: 1
					}
				},
				enableDateOptions:{},
				disableDateOptions:{},
				rules:{},
				// 查询按钮状态
				queryBtnStatus: false,
				// 导出按钮状态
				exportBtnStatus: false,
				queryBtnStatusTimer: '',
				exportBtnStatusTimer: '',
				discountStatus: [
					{name: "待处理", code: "PENDING"},
					{name: "挂起", code: "HOLD"},
					{name: "处理中", code: "PROCESSING"},
					{name: "重新打开", code: "REOPEN"},
					{name: "完成", code: "FINISHED"},
					{name: "关闭", code: "CLOSED"}
				],
				count:0,
				list:[],
				cols: [
					{
						label: '事件类型',
						prop: 'itemType',
						// format: 'auxFormat',
						// formatParams: 'todo_item_type',
						width: 120
					},  {
						label: '创建时间',
						prop: 'createTime',
						format:'dataFormat1',
						width: 130
					}, {
						label: '状态',
						prop: 'status',
						// formatter (val) {
						// 	switch (val) {
						// 		case "PENDING": return "待处理"; break;
						// 		case "HOLD": return "挂起"; break;
						// 		case "PROCESSING": return "处理中"; break;
						// 		case "REOPEN": return "重新打开"; break;
						// 		case "FINISHED": return "完成"; break;
						// 		case "CLOSED": return "关闭"; break;
						// 	}
						// }
					}, {
						label: '概要',
						prop: 'summaryMatte',
					}, {
						label: '下一步概要',
						prop: 'nextSummaryMatte',
					}, {
						label: '业务员（处理人）',
						prop: 'processPersonName',
					}, {
						label: '业务员分组（处理人所属的默认业务员分组）',
						prop: 'groupName',
						width: 260
					}, {
						label: '紧急程度',
						prop: 'priority',
						// formatter(val){
						// 	switch(val){
						// 		case 'IMMEDIATE': return"立即解决";break;
						// 		case 'URGENT': return"紧急";break;
						// 		case 'HIGH': return"高";break;
						// 		case 'NORMAL': return"中";break;
						// 		case 'LOW': return"低";break;
						// 	}
						// }
					}
                ],
				datePicker: ''
			}
		},
		methods: {
			dateChange(value){
				console.log(this.datePicker);
				if(!value){
					this.query.startDate = '';
					this.query.endDate =  '';
				}else{
					this.query.startDate = this.datePicker[0].getTime();
					this.query.endDate =  this.datePicker[1].getTime();
				}
			},
            // 选择业务员，arg为true 时选择人员类型的负责人
            openSatff(staffType, arg) {
                let self = this
                let params = {
                    callback(data) {
                        self.query.processPerson = data.id;
                        self.query.staff = data.fullName;
                        self.query.groupId = data.groupId;
                        self.query.staff_group_name = data.groupName;
                    },
                    salesmanTypeList: []
                }
                this.$root.eventHandle.$emit('alert', {
                    params: params,
                    component:() => import('@components/after_sales_report/select_personel'),
                    style: 'width:800px;height:500px',
                    title: '人员列表'
                })
            },
            staffChange(val) {
                if(!val) {
                    this.query.processPerson = '';
                    this.query.staff = '';
                    this.query.groupId = '';
                    this.query.staff_group_name = '';
                }
            },
			// 查看详情
			reset() {
				this.datePicker = []
				for(let v in this.query) {
					if (v != 'page') {
						this.query[v] = ''
					}
					if (v === 'page') {
						this.query.page.length = 50
						this.query.page.pageNo = 1
					}
				}
			},
			pageSizeChange(ps) {
				this.query.page.length = ps;
				this.queryData();
			},
			currentPageChange(page) {
				this.query.page.pageNo = page;
				this.queryData();
			},
			queryData() {
				this.$refs.query.validate((valid) => {
					if(valid) {
						if (!this.query['startDate']) {
							this.$message.error('请填写创建时间后查询列表')
							return
						}
						let data = JSON.parse(JSON.stringify(this.query));
						data['startDate']=='' ?   data['startDate']='' : data['startDate']= +new Date(data.startDate);
						data['endDate']=='' ?   data['endDate']='' : data['endDate']= +new Date(data.endDate);
						this.queryBtnStatus = true;
						this.ajax.postStream('/user-web/api/toDo/list', data, res => {
						this.queryBtnStatus = false;
						if(res.body.result && res.body.content) {
							let content = res.body.content;
							this.list = content.list || [];
							this.count = res.body.content.count
							this.$message.success(res.body.msg);
						}
						}, err => {
						this.$message.error(err);
						this.queryBtnStatus = false;
						})
					}
				})
			},
		}
  }
</script>
<style type="text/css">
  #pushtodoList .el-input__icon+.el-input__inner {
    height: 24px;
  }
</style>
</style>


