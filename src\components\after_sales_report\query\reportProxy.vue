<!-- 代理人与被代理人报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="委托人：" prop='"principal_real_name" '>
            <xpt-input v-model='query.principal_real_name'  size='mini'  icon="search" :on-icon-click="selectprincipalName" readonly placeholder="请选择委托人" @change='principalNameChange'></xpt-input>
          </el-form-item>
          <el-form-item label="委托人状态" prop='principal_status'>
            <el-select v-model='query.principal_status' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in principalList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="代理人：" prop='"proxy_real_name" '>
            <xpt-input v-model='query.proxy_real_name'  size='mini' icon="search" :on-icon-click="selectproxysName" readonly placeholder="请选择代理人" @change="selectproxysNameChange"></xpt-input>
          </el-form-item>
         <el-form-item label="代理状态" prop='status'>
            <el-select v-model='query.status' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in statusList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="代理原因" prop='reason'>
            <el-select v-model='query.reason' label-width="150px" size='mini'>
              <el-option v-for='(row,index) in reasonList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
		  <el-form-item >
            
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
		  <el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>

    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>

  </div>
</template>
<script>
  import mixin from '../mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
			page_no: 1,// 页码
			page_size: self.pageSize, // 页数
			principal_real_name : "",//委托人 （可空）
			principal_status : "",//委托人状态 0：失效，1：生效 （可空）
			proxy_real_name: "",//代理人 （可空）
			reason : "",//代理原因 HOLIDAY：休假、LEAVE：离职、OTHERS：其他 （可空）
			status : "",//代理状态 0：失效，1：生效 （可空）
			if_need_page: "Y",//是否分页 默认为Y，不分页送N （可空）
			},
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        count:0,
        list:[],
        cols:[
          {
            label: '委托人',
            prop: 'principal_real_name'
          }, {
            label: '委托人昵称',
            prop: 'principal_nick_name'
          }, {
            label: '委托人状态',
            prop: 'principal_status'
          }, {
            label: '代理人',
            prop: 'proxy_real_name'
          }, {
            label: '代理人昵称',
            prop: 'proxy_nick_name'
          }, {
            label: '代理原因',
            prop: 'reason',
          },{
            label: '代理状态',
            prop: 'status',
          }, {
            label: '创建时间',
            prop: 'create_time',
            format:"dataFormat1",
            width:'180'
          }, {
            label: '生效时间',
            prop: 'enable_time',
            format:"dataFormat"
          }, {
            label: '失效时间',
            prop: 'disable_time',
            format:"dataFormat"
          }
        ],
        principalList: [
          {
            name: '失效',
            value: '0'
          }, {
            name: '生效',
            value: '1'
          }
		],
		statusList: [
          {
            name: '失效',
            value: '0'
          }, {
            name: '生效',
            value: '1'
          }
		],
		reasonList: [
          {
            name: '休假',
            value: 'HOLIDAY'
          }, {
            name: '离职',
            value: 'LEAVE'
          }, {
            name: '其他',
            value: 'OTHERS'
          }
		],
      }
    },
    methods: {
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/proxy/reportProxy', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 请选择用户信息
		selectproxysName (){
        var _this = this;
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/system_management/list2.vue'), close:function(){

                },
                style:'width:900px;height:600px',
				title:'请选择用户信息',
				params:{
                    type:'EMPLOYEE',
					status:1,//生效人员
                    isEnable:1,//生效时间
                    page_name: 'cloud_user_person',
                    where: [],
                    callback(b){
                        var data = b.data;
                        // _this.proxy_real_name = data.proxy_real_name;
                        _this.query.proxy_real_name = data.realName;

                        // _this.account_id = data.employeeNumber;
                        console.log(data)
                    }

                }

            	});
      },
      selectproxysNameChange(val){
        if(!val){
          this.query.proxy_real_name = ''
        }
      },
      principalNameChange(val){
        if(!val){
          this.query.principal_real_name = ''
        }
      },
      selectprincipalName (){
        var _this = this;
        this.$root.eventHandle.$emit('alert',{
            component:()=>import('@components/system_management/list2.vue'), close:function(){

        },
        style:'width:900px;height:600px',
				title:'请选择用户信息',
				params:{
          type:'EMPLOYEE',
					status:1,//生效人员
          isEnable:1,//生效时间
          page_name: 'cloud_user_person',
            where: [],
            callback(b){
                var data = b.data;
                _this.query.principal_real_name = data.realName;
                // _this.account_id = data.employeeNumber;
                console.log(data)
            }

          }

        });
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));

            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/proxy/exportExcel', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
	  },
	  showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_REPORT_PROXY',
					},
				},
			})
		},
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{width: 150px;}
</style>


