<!-- 店铺折扣报表 -->
<template>
  <div class="xpt-flex">
    <el-row :gutter="10" class="xpt-top">
      <el-form
        ref="query"
        :rules="rules"
        :model="query"
        label-position="right"
        label-width="130px"
      >
        <el-col :span="5">
          <el-form-item label="店铺编码：" prop="shop_code">
            <xpt-input v-model="query.shop_code" size="mini"></xpt-input>
          </el-form-item>
          <el-form-item label="店铺名称：" prop="shop_name">
            <xpt-input v-model="query.shop_name" size="mini"></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="店铺渠道：" prop="shop_group">
            <xpt-select-aux
              v-model="query.shop_group"
              aux_name="shopGroup"
              clearable
            ></xpt-select-aux>
          </el-form-item>
          <el-form-item label="渠道类型：" prop="distribution_channel">
            <xpt-select-aux
              v-model="query.distribution_channel"
              aux_name="distribution_channel"
              clearable
            ></xpt-select-aux>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="折扣类型：" prop="discount_type">
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="query.discount_type"
              clearable
            >
              <el-option
                v-for="item in discount_types"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="生效时间：">
            <el-date-picker
              v-model="datePicker1"
              size="mini"
              type="daterange"
              @change="enableChange"
              placeholder="选择日期范围"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="失效时间：">
            <el-date-picker
              v-model="datePicker2"
              size="mini"
              type="daterange"
              @change="disableChange"
              placeholder="选择日期范围"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="">
            <el-button
              type="success"
              size="mini"
              @click="queryData"
              :disabled="queryBtnStatus"
              :loading="queryBtnStatus"
              >查询</el-button
            >
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" size="mini" @click="reset"
              >重置查询条件</el-button
            >
          </el-form-item>
          <el-form-item label="">
            <el-button
              type="info"
              size="mini"
              @click="exportExcel"
              :disabled="exportBtnStatus"
              :loading="exportBtnStatus"
              >导出查询结果</el-button
            >
          </el-form-item>
          <el-form-item label="">
            <el-button type="info" size="mini" @click="showExportList"
              >报表导出文件下载</el-button
            >
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead="false"
      :data="list"
      :colData="cols"
      :pageTotal="count"
      selection=""
      @page-size-change="pageSizeChange"
      @current-page-change="currentPageChange"
    >
    </xpt-list>
  </div>
</template>
<script>
import Fn from "@common/Fn.js";
export default {
  props: ["params"],
  data() {
    return {
      datePicker1: [],
      datePicker2: [],
      discount_types: [
        {
          value: "COMMON",
          label: "通用",
        },
        {
          value: "XSP",
          label: "家居",
        },
        {
          value: "EXP",
          label: "快递",
        },
        {
          value: "DZ",
          label: "定制",
        },
      ],
      query: {
        shop_code: "",
        shop_name: "",
        distribution_channel: "",
        discount_type: "",
        shop_group: "",
        begin_enable_time: "",
        end_enable_time: "",
        begin_disable_time: "",
        end_disable_time: "",
        page_no: 1,
        page_size: 50,
      },
      // 查询按钮状态
      queryBtnStatus: false,
      // 导出按钮状态
      exportBtnStatus: false,
      rules: {},
      count: 0,
      list: [],
      cols: [
        {
          label: "店铺编码",
          prop: "shop_code",
          width: 120,
        },
        {
          label: "店铺名称",
          prop: "shop_name",
          width: 120,
        },
        {
          label: "店铺渠道",
          prop: "shop_group_name",
          width: 240,
        },
        {
          label: "渠道类型",
          prop: "distribution_channel_name",
        },
        {
          label: "折扣类型",
          prop: "discount_type_name",
        },
        {
          label: "经销商折扣比例",
          prop: "dealer_discount",
        },
        {
          label: "生效时间",
          prop: "enable_time",
          formatter(val) {
            return Fn.dateFormat(val, "yyyy-MM-dd hh:mm:ss");
          },
          width: 130,
        },
        {
          label: "失效时间",
          prop: "disable_time",
          formatter(val) {
            return Fn.dateFormat(val, "yyyy-MM-dd hh:mm:ss");
          },
          width: 130,
        },
      ],
    };
  },
  methods: {
    enableChange(value) {
      if (!value) {
        this.query.begin_enable_time = "";
        this.query.end_enable_time = "";
      } else {
        this.query.begin_enable_time = this.datePicker1[0].getTime();
        this.query.end_enable_time = this.datePicker1[1].getTime();
      }
    },
    disableChange(value) {
      if (!value) {
        this.query.begin_disable_time = "";
        this.query.end_disable_time = "";
      } else {
        this.query.begin_disable_time = this.datePicker2[0].getTime();
        this.query.end_disable_time = this.datePicker2[1].getTime();
      }
    },
    // 查看详情
    reset() {
      this.datePicker1 = [];
      this.datePicker2 = [];
      for (let v in this.query) {
        if (v != "page_size" || v != "page_no") {
          this.query[v] = "";
        }
        if (v === "page_size") {
          this.query[v] = 50;
        }
        if (v === "page_no") {
          this.query[v] = 1;
        }
      }
    },
    pageSizeChange(ps) {
      this.query.page_size = ps;
      this.queryData();
    },
    currentPageChange(page) {
      this.query.page_no = page;
      this.queryData();
    },
    queryData() {
      this.$refs.query.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.query));
          data.begin_enable_time = Fn.dateFormat(
            data.begin_enable_time,
            "yyyy-MM-dd hh:mm:ss"
          );
          data.end_enable_time = Fn.dateFormat(
            data.end_enable_time,
            "yyyy-MM-dd hh:mm:ss"
          );
          data.begin_disable_time = Fn.dateFormat(
            data.begin_disable_time,
            "yyyy-MM-dd hh:mm:ss"
          );
          data.end_disable_time = Fn.dateFormat(
            data.end_disable_time,
            "yyyy-MM-dd hh:mm:ss"
          );
          this.queryBtnStatus = true;
          this.ajax.postStream(
            "/reports-web/api/reports/shopDiscount/list",
            data,
            (res) => {
              this.queryBtnStatus = false;
              if (res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
                this.$message.success(res.body.msg);
              }
            },
            (err) => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            }
          );
        }
      });
    },
    // 导出功能
    exportExcel(e) {
      this.$refs.query.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.query));
          data.begin_enable_time = Fn.dateFormat(
            data.begin_enable_time,
            "yyyy-MM-dd hh:mm:ss"
          );
          data.end_enable_time = Fn.dateFormat(
            data.end_enable_time,
            "yyyy-MM-dd hh:mm:ss"
          );
          data.begin_disable_time = Fn.dateFormat(
            data.begin_disable_time,
            "yyyy-MM-dd hh:mm:ss"
          );
          data.end_disable_time = Fn.dateFormat(
            data.end_disable_time,
            "yyyy-MM-dd hh:mm:ss"
          );
          this.exportBtnStatus = true;
          this.ajax.postStream(
            "/reports-web/api/reports/shopDiscount/export",
            data,
            (res) => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? "success" : "error",
                message: res.body.msg,
              });
            },
            (err) => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            }
          );
        }
      });
    },
    // 导出文件下载
    showExportList() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "报表导出列表",
        params: {
          query: {
            type: "EXCEL_TYPE_SHOP_DISCOUNT_REPORT_EXPORT",
          },
        },
      });
    },
  },
};
</script>
<style type="text/css" scoped>
.el-input {
  width: 150px;
}
.el-select {
  width: 150px;
}
</style>


