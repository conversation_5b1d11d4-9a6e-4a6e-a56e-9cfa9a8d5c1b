<!-- 库存查询 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="物料编码：" prop='fnumber'>
            <xpt-input v-model='query.fnumber'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="物料分组：" prop='material_group_name'>
            <xpt-input v-model='query.material_group_name'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="存货类别：" prop='stock_cat'>
            <xpt-input v-model='query.stock_cat'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="物料名称：" prop='fname'>
            <xpt-input v-model='query.fname'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="商品类别名称：" prop='category_name'>
            <xpt-input v-model='query.category_name'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="仓库：" prop='stock_group_name'>
            <xpt-input v-model='query.stock_group_name'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="商品组别：" prop='product_group'>
            <xpt-input v-model='query.product_group'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <!--<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>-->
        </el-col>
      </el-form>
    </el-row>
      <xpt-list
        :showHead='false'
        :data='list'
        :colData='cols'
        :pageTotal='count'
        selection=''
        @page-size-change='pageSizeChange'
        @current-page-change='currentPageChange'
      ></xpt-list>
  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this
      return {
        query: {
          page_no: 1,// 页码
          page_size: self.pageSize, // 页数
          fnumber:"",//物料编码
          fname:"",//物料名称
          material_group_name:"",//物料分组
          category_name:"",//物料类别名称
          stock_group_name:"",//仓库
          product_group:"",//商品组别
          stock_cat:"",//存货类别
        },
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        count:0,
        list:[],
        cols: [
          {
            label: '物料编码',
            prop: 'fnumber',
          }, {
            label: '物料名称',
            prop: 'fname',
          }, {
            label: '规格型号',
            prop: 'spec',
          }, {
            label: '仓库',
            prop: 'stock_group_name'
          }, {
            label: '库存单位',
            prop: 'unit_name',
          }, {
            label: '数量',
            prop: 'qty',
          }, {
            label: '锁库数量',
            prop: 'lock_qty'
          }, {
            label: '可用量',
            prop: 'use_qty'
          }, {
            label: '物料分组',
            prop: 'material_group_name',
          }, {
            label: '商品类别名称',
            prop: 'category_name'
          }, {
            label: '商品组别',
            prop: 'product_group'
          }, {
            label: '存货类别',
            prop: 'stock_cat'
          }
        ]

      }
    },
    methods: {
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            this.queryBtnStatus = true;
            this.ajax.postStream('/kingdee-web/api/stock/stockInfoList', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
                this.$message.success(res.body.msg);
              }else{
                this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));

            this.exportBtnStatus = true;
            this.ajax.postStream('/order-web/api/report/exportAchievement', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
