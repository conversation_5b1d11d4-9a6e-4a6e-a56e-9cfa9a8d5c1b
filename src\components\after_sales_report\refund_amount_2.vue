<!-- 新退款单数量报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="汇总：" >
            <el-select size="mini"  v-model="query.summary" placeholder="请选择">
              <el-option
                v-for="(value,key) in summaryList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span='6'>
          <el-form-item label="结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="6">
          <el-form-item label="退款方式：">
            <el-select size="mini" v-model="query.refund_way" clearable placeholder="请选择">
              <el-option v-for="(item, index) in refundWayList"
                :label="item.name"
                :value="item.code"
                :key="index">
              </el-option>
            </el-select>            
          </el-form-item>
        </el-col> -->
<!--         
        <el-col :span='6'>
          <el-form-item label="退款方式：">
            <xpt-input v-model='query.refund_way'   size='mini'></xpt-input>
          </el-form-item>
        </el-col>
 -->
        <el-col :span="12" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_AFTER_SALE_REFUNDAPP_AMOUNT")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        refundWayList: [{
          "name": "支付宝退款",
          "code": "ALIPAY"
        }, {
          "name": "银行卡退款",
          "code": "BANK"
        }, {
          "name": "协议退款",
          "code": "PROTOCOL"
        }, {
          "name": "纠纷退款",
          "code": "DISPUTE"
        }, {
          "name": "拍拍退款",
          "code": "PAIPAI"
        }, {
          "name": "财付通退款",
          "code": "TENPAY"
        }, {
          "name": "保证金退款",
          "code": "BAIL"
        }, {
          "name": "B2C线上退款",
          "code": "B2C_ONLINE"
        }, {
          "name": "B2C线下退款",
          "code": "B2C_OFFLINE"
        }/*, {
          "name": "退款结转",
          "code": "CARRY_OVER"
        }*/],
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          refund_way: '',
          summary:""
        },
        cols: [
          {
            label: '年月日',
            prop: 'summary',
          }, {
            label: '总提交数',
            prop: 'bills_amount',
          }, {
            label: '未提交数',
            prop: 'not_submit_amount',
          }, {
            label: '总驳回单据数',
            prop: 'reject_bills_amount'
          }/*, {
            label: '驳回次数',
            prop: 'reject_times_amount',
          }*/, {
            label: '已核对数',
            prop: 'check_bills_amount',
          }, {
            label: '待核对数',
            prop: 'not_check_bills_amount'
          }, {
            label: '已退款数',
            prop: 'all_finish_bills_amount'
          }, {
            label: '退款方式',
            prop: 'refund_way'
          }
        ]
      }
    },
    methods: {
      //得到查询条件
      getPostData(){
        let self =this;
        let data = JSON.parse(JSON.stringify(this.query));
        let begin_date = +new Date(data.begin_date);
        let end_date =+new Date(data.end_date);

        var postData={
          page_no: data.page_no,
          page_size: data.page_size,
          begin_date: begin_date,
          end_date: end_date,
          refund_way: data.refund_way,
          summaryConditions:data.summary
        }
        return postData;
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/listRefundAppAmount', this.getPostData(), res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {

            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportRefundAppAmount', this.getPostData(), res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      // 获得退款方式的枚举
      getRefundWayList() {
        var self = this;

        self.ajax.postStream('/reports-web/api/reports/afterSale/getFiled', {
          type: 'refund_way'
        }, res => {
          if (res.data.result) {
            if (res.data.content) {
              self.refundWayList = res.data.content;
            } else {
              self.refundWayList = [];
            }
          } else {
            self.$message({
              type: 'error',
              message: res.data.msg
            });
          }
        });
      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
      // this.getRefundWayList();//先前端写死
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
