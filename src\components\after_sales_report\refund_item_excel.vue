<!-- 退款明细 报表  -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='10' class='xpt-top'>
		<el-form :rules='rules' ref='$searchObj' :model='searchObj' label-position="right" label-width="120px">
			<el-col :span='6'>
				<el-form-item label="核对开始时间：" prop="check_time1">
					<el-date-picker v-model="searchObj.check_time1" type="date" @change="checkDateRequired" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.check_time1[0].isShow' class="item" effect="dark" :content="rules.check_time1[0].message" placement="right-start" popper-class='xpt-form__error'>
		              <i class='el-icon-warning'></i>
		            </el-tooltip>
				</el-form-item>
				<el-form-item label="是否经销商：" prop="if_dealer">
					<el-select v-model="searchObj.if_dealer" placeholder="请选择" size="mini">
							<el-option
								v-for="item in if_dealer_option"
								:key="item.code"
								:label="item.name"
								:value="item.code">
							</el-option>
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="核对结束时间：" prop="check_time2">
					<el-date-picker v-model="searchObj.check_time2" type="date" @change="checkDateRequired" placeholder="选择时间" :clearable="true" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.check_time2[0].isShow' class="item" effect="dark" :content="rules.check_time2[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
					<el-form-item label="订单店铺" prop="original_shop_name">
						<xpt-input size='mini' v-model="searchObj.original_shop_name" readonly icon='search' :on-icon-click="selectShop"></xpt-input>
					</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="退款类型" prop="refund_type">
					<xpt-select-aux v-model='searchObj.refund_type' aux_name='AFTERSALE_REFUND_TYPE'></xpt-select-aux>
				</el-form-item>
				<el-form-item label="退款方式" prop="refund_way">
						<xpt-select-aux v-model='searchObj.refund_way' aux_name='new_pay_method_options'></xpt-select-aux>
				</el-form-item>
				<el-form-item label="核对人" prop="check_person_name">
						<xpt-input size='mini' v-model="searchObj.check_person_name" readonly icon='search' :on-icon-click="selectBuyersName"></xpt-input>
					</el-form-item>
			</el-col>
			<el-col :span="6" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='searchFun'>查询</el-button>
				<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData="colData"
		:pageTotal='pageTotal'
        :showHead="false"
        selection=''
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
	></xpt-list>
</div>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
	props:['params'],
	data (){
		var self = this

		return {
			form: {
				page_name: 'aftersale_bill_refund_application',
				page_size: 50,
				page_no: 1,
				where: [],
				original_shop_id:'',
				if_dealer:''
			},
			searchObj: {
				check_time1: '',
				check_time2: '',
				if_dealer:'',
				check_person_name:'',
				check_person:'',
				refund_way:'',
				refund_type:'',
				original_shop_name :''
			},
			rules: {
	          check_time1: [{
	            required:false,
	            message:'请选择开始日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              if(!self.rules[rule.field][0].required){
	                callback();
	                return
	              }

	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	          check_time2: [{
	            required:false,
	            message:'请选择结束日期',
	            isShow:false,
	            validator: function(rule,value,callback){
	              if(!self.rules[rule.field][0].required){
	            callback();
	            return
	          }

	              // 数据校验
	              if(value){
	                self.rules[rule.field][0].isShow = false;
	                // 校验成功
	                callback();
	              }else{
	                self.rules[rule.field][0].isShow = true
	                // 校验失败
	                callback(new Error(''));
	              }
	            }
	          }],
	        },
			listFieldsWhere: null,
			pageTotal: 0,
			if_dealer_option:[
				{
					code:'Y',
					name:'是'
				},{
					code:'N',
					name:'否'
				},
			],
			roleList:[],
			colData: [{
				label: '退款单号',
				prop: 'bill_no',
				width: 180,
			}, {
				label: '订单店铺',
				prop: 'shop_name',
				width: 100,
			}, {
				label: '合并单号',
				prop: 'merge_trade_no',
				width: 180,
			}, {
				label: '核对人',
				prop: 'check_person_name',
			}, {
				label: '提交人',
				prop: 'submit_finance_person_name',
			}, {
				label: '提交人分组',
				prop: 'submit_finance_group_name',
			}, {
				label: '提交人大分组',
				prop: 'submit_finance_big_group_name',
			}, {
				label: '买家昵称',
				prop: 'nick_name',
			}, {
				label: '业务状态',
				prop: 'business_status',
			}, {
				label: '退款类型',
				prop: 'refund_type',
			}, {
				label: '申请金额',
				prop: 'apply_amount',
			}, {
				label: '扣费',
				prop: 'deduct_fee',
			}, {
				label: '实退金额',
				prop: 'actual_amount',
			}, {
				label: '收入店铺',
				prop: 'user_shop_name',
			}, {
				label: '退款方式',
				prop: 'refund_way',
			}, {
				label: '核对时间',
				prop: 'check_time',
				format: 'dataFormat1',
				width: 150,
			}, {
				label: '是否经销商',
				prop: 'if_dealer',
			}, {
				label: '原始店铺',
				prop: 'original_shop_name',
			}],
		}
	},
	methods: {
		reset() {
			for(let v in this.searchObj) {
				this.searchObj[v] = '';
			}
		},
		checkDateRequired (){
	        if(this.searchObj.check_time1 || this.searchObj.check_time2){
	          this.rules.check_time1[0].required =
	          this.rules.check_time2[0].required = true
	        }

	        if(!this.searchObj.check_time1 && !this.searchObj.check_time2){
	          this.rules.check_time1[0].required =
	          this.rules.check_time2[0].required = false
	        }
		},
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_REFUND_ITEM',
					},
				},
			})
		},
			// 审核人列表
		selectBuyersName (){
			// this.$root.eventHandle.$emit('alert', {
			// 	component: () => import('@components/system_management/list.vue'),
			// 	style:'width:900px;height:600px',
			// 	title:'审核人列表',
			// 	params: {
			// 		close: d => {
			// 			this.searchObj.check_person_name = d.name;
			// 			this.searchObj.check_person = d.cust_id;
			// 		}
			// 	},
			// })

			 var _this = this;
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/system_management/list2.vue'), close:function(){

                },
                style:'width:900px;height:600px',
								title:'请选择用户信息',
								params:{
                    type:'EMPLOYEE',
										// status:1,//生效人员
                    isEnable:1,//生效时间
                    page_name: 'cloud_user_person',
                    where: [],
                    callback(b){
                        var data = b.data;
                        _this.searchObj.check_person_name  = data.fullName;
                        _this.searchObj.check_person = data.id;
                        console.log(data)
                    }

                }

            	});
		},
		
		exportExcel (){
			this.checkValidate(() => {
				this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportRefundItemExcel', Object.assign({}, this.form, {
					page_size: 20000,
					page_no: 1,
				}), res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
					}else {
						this.$message.error(res.body.msg)
					}
				})
			})
		},
		checkValidate (cb){
			// this.$refs.$searchObj.validate(valid => {
				// if(valid) {
					var newWhere = []
					,	keyMapName = {
						check_time1: '核对时间',
						check_time2: '核对时间',
					}
					var timeObj = {
						check_time1:this.searchObj.check_time1,
						check_time2:this.searchObj.check_time2,
					}
					this.listFieldsWhere && Object.keys(timeObj).forEach(key => {
						var whereObj = {
							condition: 'AND',
							listWhere: [],
							operator: '=',
							field: this.listFieldsWhere[keyMapName[key]].field,
							table: this.listFieldsWhere[keyMapName[key]].table,
							value: this.searchObj[key],
						}

						if(this.searchObj[key]){
							if(/1$/.test(key)){//时间的处理
								whereObj.operator = '>='
								whereObj.value = Fn.dateFormat(whereObj.value, 'yyyy-MM-dd hh:mm:ss')
							}else if(/2$/.test(key)){//时间的处理
								whereObj.operator = '<='
								whereObj.value = Fn.dateFormat(whereObj.value, 'yyyy-MM-dd hh:mm:ss').split(' ')[0] + ' 23:59:59'
							}
							newWhere.push(whereObj)
						}
					})

					this.form.where = newWhere
					cb()
				// }
			// })
		},
		searchFun(){
			this.checkValidate(() => {
				this.form.if_dealer = this.searchObj.if_dealer;
				this.form.original_shop_id = this.searchObj.original_shop_id;
				this.form.check_person = this.searchObj.check_person;
				this.form.refund_way = this.searchObj.refund_way;
				this.form.refund_type = this.searchObj.refund_type;
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/refundApp/refundItemExcel', this.form, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
						this.roleList = res.body.content.list || []
						this.pageTotal = res.body.content.count
					}else {
						this.$message.error(res.body.msg)
					}
				}, f => f, this.params.tabName)
			})
		},
		// 搜索
		// search(list, resolve) {
		// 	this.form.where = list
		// 	this.searchFun(null, resolve)
		// },
		pageChange(page_size){
			this.form.page_size = page_size
			this.searchFun()
		},
		currentPageChange(page){
			this.form.page_no = page
			this.searchFun()
		},
		getListFields (cb){
			this.ajax.postStream('/user-web/api/sql/listFields', { page: this.form.page_name }, res => {
				if(res.body.result){
					this.listFieldsWhere = res.body.content.fields.reduce((a, obj) => {
						if(/(核对时间)/.test(obj.comment)){
							a[obj.comment] = {
								field: obj.field,
								table: obj.table,
							}
						}
						return a
					}, {})
				}else {
					this.$message.error(res.body.msg)
				}
				cb()
			})
		},
		selectShop(){
				var self = this
				var params = {
					selection: 'radio',
					shop_status: 'OPEN',
					callback(d){
						// 清样订单取店铺仓库
						
						self.searchObj.original_shop_id = d.shop_id;
						self.searchObj.original_shop_name = d.shop_name;
						// self.searchObj.source_shop_id = d.source_shop_id;
					}
				}
				self.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/shop/list'),
						style:'width:800px;height:500px',
						title:'店铺列表'
				})
			},
	},
	mounted (){
		this.getListFields(() => {
			this.searchFun()
		})
	},
}
</script>