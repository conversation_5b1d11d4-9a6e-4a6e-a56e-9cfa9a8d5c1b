<!-- 退款超时报表 -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='10' class='xpt-top'>
		<el-form :rules='rules' :model='serchData' label-position="right" label-width="120px">
			<el-col :span='6'>
				<el-form-item label="开始时间：" label-width="120px;" prop='begin_date'>
					<el-date-picker v-model="serchData.begin_date" type="datetime" placeholder="选择时间" :clearable="false" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="结束时间：" label-width="120px;" prop='end_date'>
					<el-date-picker v-model="serchData.end_date" type="datetime" placeholder="选择时间" :clearable="false" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-col>
			<el-col :span="12" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='_getList'>查询</el-button>
				<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>
	<xpt-list
        :data='list'
        :colData='cols'
        :pageTotal='pageTotal'
        :showHead="false"
        :orderNo="true"
        selection=''
        @search-click='search'
        @page-size-change='pageChange'
        @current-page-change='currentPageChange'
    ></xpt-list>
</div>
</template>

<script>
import VL from '@common/validate.js'
export default {
	data (){
		var today = (new Date).toLocaleDateString()

		return {
			serchData: {
				page_size: 50,
				page_no: 1,
				begin_date: new Date(today),
				end_date: new Date(new Date(today).getTime() + 1000*60*60*24-1000),
			},
			pageTotal: 0,
			list: [],

			cols: [{
				label: '单号',
				prop: 'order_no',
				width: '160',
			},{
				label: '买家昵称',
				prop: 'buyer_nick',
				width: '130',
			},{
				label: '客户申请金额',
				prop: 'refund_fee',
			},{
				label: '责任人',
				prop: 'staff_name',
			},{
				label: '责任人分组',
				prop: 'group_name',
			},{
				label: '责任人大分组',
				prop: 'big_group_name',
			},{
				label: '责任部门',
				prop: 'department',
			},{
				label: '分类',
				prop: 'status_name',
			},{
				label: '更新时间',
				prop: 'last_update_dt',
				format: 'dataFormat1',
				width: '150',
			},{
				label: '第一次提交时间',
				prop: 'first_submit_dt',
				format: 'dataFormat1',
				width: '150',
			},{
				label: '最后一次提交时间',
				prop: 'last_submit_dt',
				format: 'dataFormat1',
				width: '150',
			},{
				label: '第一阶段',
				prop: 'first_stage',
			},{
				label: '第一阶段扣分',
				prop: 'first_stage_fraction',
			},{
				label: '第二阶段',
				prop: 'two_stage',
			},{
				label: '第二阶段扣分',
				prop: 'two_stage_fraction',
			},{
				label: '第三阶段',
				prop: 'three_stage',
			},{
				label: '第三阶段扣分',
				prop: 'three_stage_fraction',
			},{
				label: '责任扣分',
				prop: 'fraction',
			}],

			rules: {
				begin_date: this.VLFun(true,'请选择开始时间'),
				end_date: this.VLFun(true,'请选择结束时间'),
			},
		}
	},
	methods: {
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_REPORT_AFTER_REPAIR_REFUND_OVER_TIME',
					},
				},
			})
		},
		//对页面需要认证的内容认证，required：是否必填，msg：提示信息
		VLFun(required, msg){
			return VL.isNotBlank({
				required:required,
				self:this,
				msg:msg,
			})
		},
		exportExcel (e){
			var $btn = e.target

			$btn.disabled = true
			this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportRefundOverTime', this.serchData, res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				$btn.disabled = false
			}, () => {
				$btn.disabled = false
			})
		},
		reset (){
			var today = (new Date).toLocaleDateString()

			this.serchData.begin_date = new Date(today)
			this.serchData.end_date = new Date(new Date(today).getTime() + 1000*60*60*24-1000)
		},
		search (keyword){
			this.serchData.page_no = 1
			this.serchData.code = keyword
			this._getList()
		},
		pageChange (pageSize){
			this.serchData.page_size = pageSize
			// this.serchData.page_no = 1
			this._getList()
		},
		currentPageChange (page){
			this.serchData.page_no = page
			this._getList()
		},  
		_getList (resolve, msg){
			this.ajax.postStream('/reports-web/api/reports/afterSale/findReportRefundOverTime', this.serchData, res => {
				if(res.body.result){
					this.$message.success(msg || res.body.msg)
					this.list = res.body.content.list || []
					this.pageTotal = res.body.content.count
				}else {
					this.list = []
					this.pageTotal = 0
					this.$message.error(res.body.msg)
				}
			})
		},
	},
	mounted (){
		this._getList()
	},
}
</script>