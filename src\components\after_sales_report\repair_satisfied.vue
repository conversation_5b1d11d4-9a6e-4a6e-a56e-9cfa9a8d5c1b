<!-- 维修满意度报表 -->
<template>
<div class='xpt-flex'>
	<el-row :gutter='10' class='xpt-top'>
		<el-form :rules='rules' :model='serchData' label-position="right" label-width="120px">
			<el-col :span='6'>
				<el-form-item label="开始时间：" label-width="120px;" prop='begin_date'>
					<el-date-picker v-model="serchData.begin_date" type="datetime" placeholder="选择时间" :clearable="false" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
				<el-form-item label="处理类型：" label-width="120px;">
					<xpt-select-aux v-model='serchData.service_type' clearable aux_name='afsHandleType'>
						<el-option label=' ' value='NULL' slot='option'></el-option>
					</xpt-select-aux>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="结束时间：" label-width="120px;" prop='end_date'>
					<el-date-picker v-model="serchData.end_date" type="datetime" placeholder="选择时间" :clearable="false" size='mini'></el-date-picker>
					<el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
				<el-form-item label="满意度：" label-width="120px;">
					<xpt-select-aux v-model='serchData.service_satisfaction' clearable aux_name='afsSatifcation'>
						<el-option label=' ' value='NULL' slot='option'></el-option>
					</xpt-select-aux>
				</el-form-item>
			</el-col>
			<el-col :span="12" class='xpt-align__right'>
				<el-button type='success' size='mini' @click='_getList'>查询</el-button>
				<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
				<el-button type='info' size='mini' @click='exportExcel'>导出</el-button>
				<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
			</el-col>
		</el-form>
	</el-row>
	<xpt-list
        :data='list'
        :colData='cols'
        :pageTotal='pageTotal'
        :showHead="false"
        :orderNo="true"
        selection=''
        @search-click='search'
        @page-size-change='pageChange'
        @current-page-change='currentPageChange'
    ></xpt-list>
</div>
</template>

<script>
import VL from '@common/validate.js'
export default {
	data (){
		var today = (new Date).toLocaleDateString()

		return {
			serchData: {
				page_size: 50,
				page_no: 1,
				begin_date: new Date(today),
				end_date: new Date(new Date(today).getTime() + 1000*60*60*24-1000),
				service_type: '',
				service_satisfaction: '',
			},
			pageTotal: 0,
			list: [],

			cols: [{
				label: '单据编号',
				prop: 'no',
				width: '130',
			},{
				label: '批次单号',
				prop: 'batch_no',
				width: '200',
			},{
				label: '买家昵称',
				prop: 'buyer_name',
				width: '130',
			},{
				label: '业务员',
				prop: 'saleman_name',
			},{
				label: '业务员分组',
				prop: 'saleman_group_name',
			},{
				label: '指令工单完成时间',
				prop: 'finish_time',
				format: 'dataFormat1',
				width: '150',
			},{
				label: '发货时间',
				prop: 'delivery_time',
				format: 'dataFormat1',
				width: '150',
			},{
				label: '物流供应商',
				prop: 'logistics_supplier',
				width: '130',
			},{
				label: '三包供应商名称',
				prop: 'three_supplier',
				width: '130',
			},{
				label: '物流单号',
				prop: 'logistics_no',
				width: '150',
			},{
				label: '提货点',
				prop: 'delivery_name',
				width: '150',
			},{
				label: '三包点',
				prop: 'three_name',
			},{
				label: '收货人',
				prop: 'receiver_name',
			},{
				label: '省',
				prop: 'province_name',
			},{
				label: '市',
				prop: 'city_name',
			},{
				label: '区',
				prop: 'area_name',
			},{
				label: '街道',
				prop: 'stree_name',
			},{
				label: '处理类型',
				prop: 'service_type_name',
			},{
				label: '满意度',
				prop: 'satisfied_name',
			},{
				label: '处理说明',
				prop: 'handle_explain',
				width: '130',
			},{
				label: '支出对象',
				prop: 'expense_object',
			},{
				label: '支出说明',
				prop: 'expense_explain',
				width: '130',
			},{
				label: '收入对象',
				prop: 'income_object',
			},{
				label: '收入说明',
				prop: 'income_explain',
			},{
				label: '单据状态',
				prop: 'status_name',
			},{
				label: '创建人',
				prop: 'creator_name',
			},{
				label: '创建时间',
				prop: 'create_time',
				format: 'dataFormat1',
				width: '150',
			},{
				label: '业务锁定人',
				prop: 'lock_name',
			},{
				label: '责任来源单号',
				prop: 'responsibility_bill_no',
				width: '150',
			},{
				label: '锁定人员分组',
				prop: 'lock_group_name',
			}],

			rules: {
				begin_date: this.VLFun(true,'请选择开始时间'),
				end_date: this.VLFun(true,'请选择结束时间'),
			},
		}
	},
	methods: {
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_REPORT_AFTER_REPAIR_SATISFIED',
					},
				},
			})
		},
		//对页面需要认证的内容认证，required：是否必填，msg：提示信息
		VLFun(required, msg){
			return VL.isNotBlank({
				required:required,
				self:this,
				msg:msg,
			})
		},
		exportExcel (e){
			var $btn = e.target

			$btn.disabled = true
			this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportRepairSatisfied', this.serchData, res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				$btn.disabled = false
			}, () => {
				$btn.disabled = false
			})
		},
		reset (){
			var today = (new Date).toLocaleDateString()

			this.serchData.begin_date = new Date(today)
			this.serchData.end_date = new Date(new Date(today).getTime() + 1000*60*60*24-1000)
			this.serchData.service_type = ''
			this.serchData.service_satisfaction = ''
		},
		search (keyword){
			this.serchData.page_no = 1
			this.serchData.code = keyword
			this._getList()
		},
		pageChange (pageSize){
			this.serchData.page_size = pageSize
			// this.serchData.page_no = 1
			this._getList()
		},
		currentPageChange (page){
			this.serchData.page_no = page
			this._getList()
		},  
		_getList (resolve, msg){
			this.ajax.postStream('/reports-web/api/reports/afterSale/findAfterRepairSatisfiedPage', this.serchData, res => {
				if(res.body.result){
					this.$message.success(msg || res.body.msg)
					this.list = res.body.content.list || []
					this.pageTotal = res.body.content.count
				}else {
					this.list = []
					this.pageTotal = 0
					this.$message.error(res.body.msg)
				}
			})
		},
	},
	mounted (){
		this._getList()
	},
}
</script>