<!-- 责任金额报表II -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='5'>
          <el-form-item label="创建开始时间：" label-width="120px;" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" :picker-options="dateFormatFun" type="datetime"  placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="确认开始时间：" label-width="120px;" prop='begin_date2'>
            <el-date-picker v-model="query.begin_date2"  type="datetime" :picker-options="dateFormatFun"  placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date2[0].isShow' class="item" effect="dark" :content="rules.begin_date2[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="首次确认开始时间：" label-width="120px;" prop='firstConfirmStartTime'>
            <el-date-picker v-model="query.firstConfirmStartTime"  type="datetime" :picker-options="dateFormatFun"  placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.firstConfirmStartTime[0].isShow' class="item" effect="dark" :content="rules.firstConfirmStartTime[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <!-- <el-form-item label="责任类型(旧版)：" label-width="120px;">
            <el-input v-model="query.duty_type" size='mini' :icon="query.duty_type ? 'close' : 'search'" readonly :on-icon-click="() => query.duty_type ? liability_questionClear() : selectDutyTypeOrProblem('duty_type')" ></el-input>
          </el-form-item> -->
          <el-form-item label="源单单号：" label-width="120px;">
            <xpt-input v-model='query.source_no' size='mini' ></xpt-input>
          </el-form-item>
           <el-form-item label="结算方式 ：" label-width="120px;">
            <xpt-select-aux v-model='query.settle_method' aux_name='settle_method' size="mini" clearable></xpt-select-aux>
          </el-form-item>
          <el-form-item label="主责/次责" label-width="120px;">
            <el-select v-model="query.judgment_suggestion" clearable placeholder="请选择" size='mini'>
              <el-option
                v-for="(val, key) in judgmentSuggestionOptions"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="创建结束时间：" label-width="120px;" prop='end_date'>
            <el-date-picker v-model="query.end_date"  type="datetime" :picker-options="dateFormatFun1" placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="确认结束时间：" label-width="120px;" prop='end_date2'>
            <el-date-picker v-model="query.end_date2"  type="datetime" :picker-options="dateFormatFun1"  placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date2[0].isShow' class="item" effect="dark" :content="rules.end_date2[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="首次确认结束时间：" label-width="120px;" prop='firstConfirmEndTime'>
            <el-date-picker v-model="query.firstConfirmEndTime"  type="datetime" :picker-options="dateFormatFun"  placeholder="选择日期" @change="checkDateRequired" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.firstConfirmEndTime[0].isShow' class="item" effect="dark" :content="rules.firstConfirmEndTime[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="买家昵称：" label-width="120px;">
            <el-input
              v-model="query.buyer_name"
              size='mini'
              icon="search"
              :on-icon-click="selectBuyersName"
            ></el-input>
          </el-form-item>
          <el-form-item label="责任人：" label-width="120px;">
            <el-input
              v-model="query.person_name"
              size='mini'
            ></el-input>
            <!-- <xpt-input v-model='query.staff_group' icon='search' :on-icon-click='openGroup' size='mini' @change='groupChange'></xpt-input> -->
          </el-form-item>
          <el-form-item label="一级责任主体：">
            <el-input v-model="query.tinner_liability_type" size='mini' icon='search' readonly :on-icon-click="() => selectDutyType()" ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span='5'>
          <!-- <el-form-item label="责任问题（旧版）：">
            <el-input v-model="query.liability_question" size='mini' :icon="query.liability_question ? 'close' : 'search'" readonly :on-icon-click="() => query.liability_question ? liability_questionClear() : selectDutyTypeOrProblem('liability_question')" ></el-input>
          </el-form-item> -->
          <el-form-item label="合并单号：">
            <el-input v-model="query.merge_no" size='mini' @blur="_getMergeNoId"></el-input>
          </el-form-item>
          <el-form-item label="责任范围：">
            <el-select v-model="query.duty_scope" clearable placeholder="请选择" size='mini'>
              <el-option
                v-for="(val, key) in duty_scope_options"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
           <el-form-item label="是否经销商订单：">
            <el-select v-model="query.if_dealer" clearable placeholder="请选择" size='mini'>
              <el-option
                v-for="(val, key) in if_dealer_options"
                :key="key"
                :label="val"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="故障原因：">
            <el-input v-model="query.inner_quesd_name" size='mini' clearable
            icon='search' readonly :on-icon-click="() => selectDutyTypeOrProblem('inner_quesd_name')"
            ></el-input>
          </el-form-item>
          <el-form-item label="责任人分组：" label-width="120px;">
            <el-input
              v-model="query.liability_person_group_name"
              size='mini'
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          judgment_suggestion:'',
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          begin_date2: '',
          end_date2: '',
          firstConfirmStartTime: '',
          firstConfirmEndTime: '',
          duty_problem: '',
          liability_question:'',
          duty_type: '',
          duty_scope: '',
          buyer_name: '',
          source_no: '',
          settle_method:"",
          // staff_id: '',
          person_name: '',
          liability_person_group_name: '',
          if_dealer:'',
          merge_no:"",
          inner_quesd_id:"",
          inner_quesd_name:"",
          tinner_liability_type:"",
          inner_liability_type_id:'',
        },
         dateFormatFun: {

          date: (function() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var time = year + "-" + month + "-" + day + " " + "00:00:00";
            return new Date(time);
          })()
        },
        dateFormatFun1: {

          date: (function() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var time = year + "-" + month + "-" + day + " " + "23:59:59";
            return new Date(time);
          })()
        },
        liability_scope: '',
        merge_trade_id: '',
        rules: {
          begin_date: [{
            required:true,
            message:'请选择开始日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
                callback();
                return
              }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
          begin_date2: [{
            required:true,
            message:'请选择开始日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
            callback();
            return
          }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
          end_date: [{
            required:true,
            message:'请选择结束日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
                callback();
                return
              }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
          end_date2: [{
            required:true,
            message:'请选择结束日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
            callback();
            return
          }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
          firstConfirmStartTime: [{
            required:true,
            message:'请选择开始日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
            callback();
            return
          }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
          firstConfirmEndTime: [{
            required:true,
            message:'请选择开始日期',
            isShow:false,
            validator: function(rule,value,callback){
              if(!self.rules[rule.field][0].required){
            callback();
            return
          }

              // 数据校验
              if(value){
                self.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }else{
                self.rules[rule.field][0].isShow = true
                // 校验失败
                callback(new Error(''));
              }
            }
          }],
        },
        cols: [
          {
            label: '源单单号',
            prop: 'source_no'
          },
          {
            label: '合并单号',
            prop: 'merge_no',
          }, {
            label: '买家昵称',
            prop: 'buyer_name',
          },{
            label: '一级责任主体',
            prop: 'tinner_liability_type',
            width:150
          }, {
            label: '二级责任主体',
            prop: 'que_sec_name',
            width:150
          }, {
            label: '故障模块',
            prop: 'first_quesd_name',
            width:150
          }, {
            label: '故障现象',
            prop: 'second_quesd_name',
            width:150
          }, {
            label: '故障原因',
            prop: 'inner_quesd_name',
            width:150
          },
          // {
          //   label: '责任类型（旧版）',
          //   prop: 'duty_type',
          //   width:150
          // }, {
          //   label: '责任问题（旧版）',
          //   prop: 'duty_problem',
          //   width:150
          // },
          {
            label: '责任范围',
            prop: 'duty_scope',
          },{
            label: '主责/次责',
            prop: 'judgment_suggestion',
          }, {
            label: '责任人工号',
            prop: 'staff_no',
          }, {
            label: '责任人',
            width: 100,
            prop: 'staff_name',
            formatter(val,index,row){
              return row.staff_name+(row.staff_nick?("."+row.staff_nick):'')
            },
          },
          {
            label: '责任人分组',
            prop: 'liability_person_group_name',
            width: 100
          },
          {
            label: '是否生成绩效单',
            prop: 'if_generate_per_sheet',
            format: 'yesOrNo',
            width:100
          }, /*{
            label: '部门',
            prop: 'department',
          },*/ {
            label: '责任金额',
            prop: 'liability_amount'
          }, {
            label: '处理金额',
            prop: 'handle_amount'
          }, {
            label: '备注',
            prop: 'remark'
          }, {
            label: '组长',
            prop: 'group_leader'
          }, {
            label: '大分组',
            prop: 'big_group_leader'
          }, {
            label: '源单类型',
            prop: 'sourceTypeName'
          }, {
            label: '退款方式',
            prop: 'refund_way'
          },{
            label: '退款原因',
            prop: 'refund_reason'
          }, {
            label: '创建人',
            prop: 'create_man'
          }, {
            label: '创建时间',
            prop: 'create_time',
            format:"dataFormat1"
          }, {
            label: '责任状态',
            prop: 'duty_status'
          }, {
            label: '经销商',
            prop: 'dealer_customer_name'
          }, {
            label: '原始店铺',
            prop: 'primitive_shop_name'
          },{
            label: '订单店铺',
            prop: 'shop_name',
          }, {
            label: '结算方式',
            prop: 'settle_method',
            format: 'auxFormat',
            formatParams: 'settle_method',
          },{
					label: '业务类型',
					prop: 'business_type_trade',
					format: 'auxFormat',
					formatParams: 'ddywlx',
				},{
					label: '是否经销商订单',
					prop: 'if_dealer',
					format: 'yesOrNo',
          width:100
				},{
					label: '首次确认时间',
					prop: 'liability_first_confirm_time',
          format:"dataFormat1",
          width:140
				}
        ],
        duty_scope_options: {
          BD_Empinfo: '人员',
          BD_Supplier: '经销商',
          BD_Customer: '客户',
          BD_Department: '部门',
        },
        if_dealer_options:{
          Y: '是',
          N: '否',
        },
        judgmentSuggestionOptions: {
                    'JUDGMENT':'判责',
                    'SUGGESTION':'建议',
                    'PRINCIPAL':'主责',
                    'SECONDARY':'次责',
                },
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {
      _getMergeNoId (){
        var self = this

        if(!this.query.merge_no){
          this.merge_trade_id = ''
          return
        }

        if(!this._getMergeNoId.where){
          this.ajax.postStream('/user-web/api/sql/listFields', { page: 'scm_sys_trade' }, res => {
            res.body.content.fields.some(obj => {
              if(obj.comment === '合并单号'){
                this._getMergeNoId.where = [{
                  "field": obj.field,
                  "table": obj.table,
                  "value": this.query.merge_no,
                  "operator": "=",
                  "condition": "AND",
                  "listWhere": []
                }]
                _get()
                return
              }
            })
          })
        }else {
          this._getMergeNoId.where[0].value = this.query.merge_no
          _get()
        }

        function _get (){
          self.ajax.postStream('/order-web/api/scmsystrade/list?permissionCode=ORDER_SALESORDER_QUERY', {
            "page_name": "scm_sys_trade",
            "where": self._getMergeNoId.where,
            "page_size": 50,
            "page_no": 1
          }, res => {
            self.merge_trade_id = res.body.content.list[0].merge_trade_id
          })
        }
      },

      // 创建时间和审核时间必须要至少一组，且开始时间和结束时间必须要同时存在
      checkDateRequired (){
        // if(this.query.firstConfirmStartTime||this.query.firstConfirmEndTime){
        //   this.rules.firstConfirmStartTime[0].required = this.rules.firstConfirmEndTime[0].required = true
        // }else{
        //   this.rules.firstConfirmStartTime[0].required = this.rules.firstConfirmEndTime[0].required = false
        //   this.rules.firstConfirmStartTime[0].isShow=this.rules.firstConfirmEndTime[0].isShow  = false;
        // }
        if(!this.query.begin_date && !this.query.end_date && !this.query.begin_date2 && !this.query.end_date2){
          this.rules.begin_date[0].required =
          this.rules.end_date[0].required =
          this.rules.begin_date2[0].required =
          this.rules.end_date2[0].required = true
        }

        if(this.query.begin_date || this.query.end_date){
          this.rules.begin_date[0].required =
          this.rules.end_date[0].required = true
        }

        if(this.query.begin_date2 || this.query.end_date2){
          this.rules.begin_date2[0].required =
          this.rules.end_date2[0].required = true
        }

        if((this.query.begin_date || this.query.end_date) && (!this.query.begin_date2 && !this.query.end_date2)){
          this.rules.begin_date2[0].required =
          this.rules.end_date2[0].required = false

          this.rules.begin_date2[0].isShow =
          this.rules.end_date2[0].isShow = false
        }

        if((this.query.begin_date2 || this.query.end_date2) && (!this.query.begin_date && !this.query.end_date)){
          this.rules.begin_date[0].required =
          this.rules.end_date[0].required = false

          this.rules.begin_date[0].isShow =
          this.rules.end_date[0].isShow = false
        }
      },
      // 选择买家昵称
      selectBuyersName (){
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/customers/list'),
          style:'width:900px;height:600px',
          title:'买家昵称列表',
          params: {
            close: d => {
              this.query.buyer_name = d.name
            }
          },
        })
      },
      liability_questionClear(model){
        if (model === 'tinner_liability_type') {
          this.query.inner_liability_type_id = '';
          this.query.tinner_liability_type = '';
        } else {
          this.query.duty_problem = '';
          this.query.liability_question = '';
        }
      },
      selectDutyType (){
        this.$root.eventHandle.$emit('alert',{
          params: {
            source: 'excel',
            callSource: 'responsibility_amount',
            callback: d => {
              console.log(d)
              this.query.inner_liability_type_id = d.id;
              this.query.tinner_liability_type = d.liability_type;
            },
          },
          component:()=>import('@components/after_sales_liability_problem/list_choose.vue'),
          style:'width:90%;height:99%',
          title:'选择内部责任类型'
        })
      },
      selectDutyTypeOrProblem (model){
        console.log('选择问题', model)
        this.$root.eventHandle.$emit('alert',{
          params: {
            source: 'excel',
            callback: d => {
              if (['liability_question', 'duty_type'].includes(model)) {
                this.query.liability_question = d.out_liability_question
                this.query.duty_type = d.out_liability_type
              } else if (model === 'inner_quesd_name'){
                this.query.inner_quesd_id = d.threeLevelId;
                this.query.inner_quesd_name = d.threeLevelName;
              } else if (model === 'tinner_liability_type'){
                this.query.inner_liability_type_id = d.id;
                this.query.tinner_liability_type = d.liability_type;
              }
              console.log('选择问题-callback',d)
            },
          },
          component:()=>import('@components/after_sales_liability_problem/components/add_duty_person.vue'),
          style:'width:1250px;height:80%',
          title:'选择责任问题'
        })
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.createStartTime = +new Date(data.begin_date);
            data.createEndTime =+new Date(data.end_date);
            data.auditStartTime = +new Date(data.begin_date2);
            data.auditEndTime =+new Date(data.end_date2);
            data.firstConfirmStartTime=+new Date(data.firstConfirmStartTime);
            data.firstConfirmEndTime=+new Date(data.firstConfirmEndTime);
            delete data.staff;
            delete data.begin_date;
            delete data.end_date;
            delete data.begin_date2;
            delete data.end_date2;
            delete data.liability_question;
            delete data.inner_quesd_name;
            delete data.tinner_liability_type;
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/pageReportDutyAmount', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.createStartTime = +new Date(data.begin_date);
            data.createEndTime =+new Date(data.end_date);
            data.auditStartTime = +new Date(data.begin_date2);
            data.auditEndTime =+new Date(data.end_date2);
            data.firstConfirmStartTime=+new Date(data.firstConfirmStartTime);
            data.firstConfirmEndTime=+new Date(data.firstConfirmEndTime);
            delete data.staff;
            delete data.begin_date;
            delete data.end_date;
            delete data.begin_date2;
            delete data.end_date2;
            delete data.liability_question;
            delete data.inner_quesd_name;
            delete data.tinner_liability_type;
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportDutyAmount', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      showExportList (){
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/after_sales_report/export'),
          style:'width:900px;height:600px',
          title: '报表导出列表',
          params: {
            query: {
              type: 'EXCEL_TYPE_REPORT_AFTER_SALE_DUTY_AMOUNT',
            },
          },
        })
      },
    },
    // computed: {
    //   staff() {
    //     return this.query.staff_id;
    //   },
    //   staff_group() {
    //     return this.query.staff_group_id;
    //   },
    //   big_group() {
    //     return this.query.big_group_id;
    //   }
    // },
    // watch: {
    //   staff(n) {
    //     if(n) {
    //       this.query.staff_group = '';
    //       this.query.staff_group_id = '';
    //       this.query.big_group = '';
    //       this.query.big_group_id = '';
    //     }
    //   },
    //   staff_group(n) {
    //     if(n) {
    //       this.query.staff = '';
    //       this.query.staff_id = '';
    //       this.query.big_group = '';
    //       this.query.big_group_id = '';
    //     }
    //   },
    //   big_group(n) {
    //     if(n) {
    //       this.query.staff = '';
    //       this.query.staff_id = '';
    //       this.query.staff_group = '';
    //       this.query.staff_group_id = '';
    //     }
    //   }
    // },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
