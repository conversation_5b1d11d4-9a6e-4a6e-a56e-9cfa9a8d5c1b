<!-- 退货率明细报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="年份：" prop='year'>
            <el-select size="mini"  v-model="query.year" placeholder="请选择">
              <el-option
                v-for="(value,key) in yearList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
              <el-tooltip v-if='rules.year[0].isShow' class="item" effect="dark" :content="rules.year[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="月份：" prop='month'>
            <el-select size="mini"  v-model="query.month" placeholder="请选择">
              <el-option
                v-for="(value,key) in monthList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
              <el-tooltip v-if='rules.month[0].isShow' class="item" effect="dark" :content="rules.month[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="业务员：">
            <xpt-input v-model='query.staff' icon='search' :on-icon-click='openSatff' size='mini' @change='staffChange'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='info' 
            size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>
            导出
          </el-button>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' 
            :loading='queryBtnStatus'>
            查询
          </el-button>
          <el-button type='primary' size='mini' @click='reset'>
            重置查询条件
          </el-button>
        </el-col>
      </el-form>
    </el-row>

    <xpt-list :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'>    
    </xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this;
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          year: '',
          month: '',
          staff: '',
          staff_id: '',
          select_date: ''
        },
        //所有人员类型
        salesmanTypeList:__AUX.getCode('businessType'),
        list: [],
        cols: [{
          label: '退货跟踪单号',
          prop: 'bill_returns_no'
        }, {
          label: '业务员',
          prop: 'staff_locker_name'
        }, {
          label: '买家昵称',
          prop: 'buyer_name'
        }, {
          label: '日期',
          prop: 'create_time'
        }, {
          label: '金额',
          prop: 'money'
        }],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {
      // 查询退货率明细
      queryData() {
        var self = this;

        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.select_date=new Date(data.year+"-"+data.month+"-01").getTime();

            delete data.staff;
            delete data.year;
            delete data.month;

            self.queryBtnStatus = true;
            self.ajax.postStream('/reports-web/api/reports/return/findReportShopRefundItem', data, res => {
              self.queryBtnStatus = false;
              
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                self.list = content.list || [];
                self.count = content.count || 0;

                self.cols = [];
                res.body.content.header.forEach(function(item) {
                  var filter = null;
                  
                  switch(item.type) {
                    case 'DateField':
                      filter = 'dataFormat1';
                      break;

                    default :
                      filter = null;
                      break;
                  }

                  self.cols.push({
                    label: item.name,
                    prop: item.code,
                    format: filter
                  });
                });

              } else {
                self.$message.error(res.body.msg);
              }

            }, err => {
              self.$message.error(err);
              self.queryBtnStatus = false;
            });
          }
        });
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.select_date=new Date(data.year+"-"+data.month+"-01").getTime();
            delete data.staff;
            delete data.big_group;
            delete  data.staff_group;
            delete data.year;
            delete data.month;
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportReturnRate', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      }
    },
    watch: {
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
