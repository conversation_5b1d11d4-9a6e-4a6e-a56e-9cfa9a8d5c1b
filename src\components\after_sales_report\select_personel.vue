<!-- 售后报表选择人员信息 -->
<template>
	<!-- <xpt-list 
		ref='plList' 
		selection='radio' 
		:data='plData' 
		:btns='plBtns' 
		:colData='plCol' 
		:pageTotal='total'
		searchHolder='请输入工号或姓名或昵称'
		@search-click='searchClick'
		@radio-change='select' 
		@row-dblclick='rowDblclick'
		@page-size-change='plPageSizeChange' 
		@current-page-change='plPageChange'>
	</xpt-list> -->
	<xpt-list 
		ref='plList' 
		selection='radio' 
		:data='plData' 
		:btns='plBtns' 
		:colData='plCol' 
		:pageTotal='total'
		:searchPage='plPage.page_name' 
		@search-click='searchClick'
		@radio-change='select' 
		@row-dblclick='rowDblclick'
		@page-size-change='plPageSizeChange' 
		@current-page-change='plPageChange'>
	</xpt-list>
</template>
<script>
export default {
	props: ['params'],
	data () {
		let self = this
		return {
			plData: [],
			plBtns: [{
				type: 'primary',
				txt: '确认',
				click: self.close
			}],
			plCol: [
				{
					label: '工号',
					prop: 'employeeNumber'
				}, {
					label: '姓名',
					prop: 'realName',
				}, {
					label: '昵称',
					prop: 'nickName'
				}, {
					label: '生效时间',
					prop: 'enableTime',
					format: 'dataFormat',
					width: '130',
				}, {
					label: '失效时间',
					prop: 'disableTime',
					format: 'dataFormat',
					width: '130',
				}, {
					label: '业务员分组',
					prop: 'groupName',
					width: '150',
				}, {
					label: '业务员类型',
					prop: 'salesmanType',
					format: 'auxFormat',
					formatParams: 'businessType',
					width: '100',
				}, {
					label: '移动电话',
					prop: 'mobile',
					width: '100',
					format:'hidePhoneNumber'
				}, {
					label: '备注',
					prop: 'remark'
				}
			],
			total: 0,
			plPage: {
				/*salesmanType: 'SALES_PERSON',
				page: {
					length: self.pageSize,
					pageNo: 1
				},
				page_name: '',
				where: [],
				key: '',*/
				page: {
					length: self.pageSize,
					pageNo: 1
				},
				salesmanTypeList:[],
				//key: '',
				where: [],
				page_name: 'user_person_salesman'
			},
			// 选中的数据
			selectData: ''
		}
	},
	methods: {
		rowDblclick(obj) {
			this.selectData = obj;
			this.close();
		},
		select(obj) {
			this.selectData = obj;
		},
		plPageSizeChange (ps) {
			this.plPage.page.length = ps;
			this.getList();
		},
		plPageChange (pn) {
			this.plPage.page.pageNo = pn;
			this.getList();
		},
		getList (resolve) {
			/*let self = this,
				url = 'getUserPersonSalesmanList';		//选择人员接口
			// 选择人员类型的负责人
			if(this.params.isSelectManager) {
				url = 'getGroupResponsibleList'
			}*/
			let url = '/user-web/api/userPerson/getUserPersonSalesmanList'
			this.ajax.postStream(url, this.plPage, d => {
				if (d.body.result && d.body.content) {
					this.total = d.body.content.count
					this.plData = d.body.content.list || []
				} else {
					this.$message({
						type: 'error',
						message: d.body.msg
					})
				}
				resolve && resolve();
			}, e => {
				this.$message.error(e);
				resolve && resolve();
			})
		},
		close() {
			if(!this.selectData) {
				this.$message({
					type: 'error',
					message: '请选择人员'
				})
				return;
			}
			this.params.callback(this.selectData);
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
		},
		searchClick(where, resolve) {
			//this.plPage.key = where
			this.plPage.where = where;
			this.getList(resolve);
		}
	},
	mounted() {
		/*if(this.params.salesmanType){
			this.plPage.salesmanType = this.params.salesmanType;
			if(this.params.salesmanType === 'all') {
				delete this.plPage.salesmanType;
			}
		}*/
		if(this.params.salesmanTypeList){
			this.plPage.salesmanTypeList = this.params.salesmanTypeList;
		}
		this.getList()
	}
}
</script>
