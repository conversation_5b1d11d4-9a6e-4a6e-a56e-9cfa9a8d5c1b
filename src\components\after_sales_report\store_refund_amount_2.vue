<!-- 店铺新退款金额汇总报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="汇总：" >
            <el-select size="mini"  v-model="query.summaryConditions " placeholder="请选择">
              <el-option
                v-for="(value,key) in summaryList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="收入店铺：">
            <xpt-input v-model='query.shop_name' icon='search' :on-icon-click='openShop2' @change='shopChange' size='mini' ></xpt-input>
          </el-form-item>

        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_AFTER_SALE_SHOP_REFUNDAPP")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          shop_name:'',
          shop_id:'',
          summaryConditions :""
        },
        cols: [
          {
            label: '日期',
            prop: 'record_day_str',
            width: 80,
          },{
            label: '收入店铺',
            prop: 'shop_name',
          }, {
            label: '发货金额',
            prop: 'deliver_amount',
          }, {
            label: '未发取消',
            prop: 'cancel_amount',
          }, {
            label: '多付',
            prop: 'overpay_amount'
          }, {
            label: '差价',
            prop: 'price_diff_amount',
          }, {
            label: 'O2O跨店铺差价',
            prop: 'o2o_diff_amount',
          }, {
            label: '三包费',
            width: 100,
            prop: 'three_amount'
          }, {
            label: '运费',
            prop: 'carriage_amount',
            width: 100
          }, {
            label: '优惠/返现',
            prop: 'preferential_amount',
          },{
            label: '单品优惠',
            prop: 'single_discount_amount',
          }, {
            label: '外购折现',
            prop: 'discount_amount'
          }, {
            label: '退货货款',
            prop: 'returns_amount'
          }, {
//            label: '报废/丢件',
//            prop: ''
//          }, {
            label: '补偿',
            prop: 'compensate_amount'
          }, {
            label: '延迟赔付',
            prop: 'delay_amount'
          }, {
            label: '维修费',
            prop: 'repair_amount'
          }, {
            label: '店铺合计',
            prop: 'shop_amount'
          }, {
            label: '占总退款比',
            prop: 'refund_rate'
          }, {
            label: '占发货业绩比',
            prop: 'deliver_rate'
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {

      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date = +new Date(data.end_date) + 1000 * 60 * 60 * 24 - 1000;//比如选中2018-05-08，则正确结束日期是：2018-05-08 23:59:59
            this.queryBtnStatus = true;

            let listPromise = new Promise((resolve, reject) => {
                this.ajax.postStream('/reports-web/api/reports/shopRefund/pageReportShopRefundApp', data, res => {
                  this.queryBtnStatus = false;
                  if(res.body.result && res.body.content) {
//                    let content = res.body.content.body;
//                    this.list = content.list || [];
//                    this.count = content.count || 0;
                    resolve(res.body.content.body)
                  } else {
                    reject(res.body.msg)
                  }
                }, err => {
                  this.$message.error(err);
                  this.queryBtnStatus = false;
                })
              }),
            totalPromise = this.queryTotal(data);
            listPromise.then(val => {
              this.list = []
              totalPromise.then(total => {
                let list = val.list || [];
                for(let totalList of total) {
                  list.push(totalList);
                }
                this.list = list;
                this.count = val.count || 0;
              })
            }).catch(err => {
              this.errIntercept(err);
              // this.$message.error(err);
            })

          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date = +new Date(data.end_date) + 1000 * 60 * 60 * 24 - 1000;//比如选中2018-05-08，则正确结束日期是：2018-05-08 23:59:59
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportShopRefundApp', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      },
      // 查询汇总
      queryTotal(data) {
        return new Promise(resolve => {
          this.ajax.postStream('/reports-web/api/reports/shopRefund/pageReportShopRefundApp', data, res => {
            this.queryBtnStatus = false;
            if(res.body.result && res.body.content) {
              let content = res.body.content.body.list;



              let sum = {
                shop_name: '小计:',
                deliver_amount:content[0].sum_deliver_amount,
                cancel_amount:content[0].sum_cancel_amount,
                overpay_amount:content[0].sum_overpay_amount,
                preferential_amount:content[0].sum_preferential_amount,
                delay_amount:content[0].sum_delay_amount,
                compensate_amount:content[0].sum_compensate_amount,
                repair_amount:content[0].sum_repair_amount,
                returns_amount:content[0].sum_returns_amount,
                carriage_amount:content[0].sum_carriage_amount,
                three_amount:content[0].sum_three_amount,
                o2o_diff_amount:content[0].sum_o2o_diff_amount,
                price_diff_amount:content[0].sum_price_diff_amount,
                discount_amount:content[0].sum_discount_amount,
                single_discount_amount:content[0].sum_single_discount_amount,
//                shop_amount:content[0].sum_shop_amount,
//                refund_rate:content[0].sum_refund_rate,
//                deliver_rate:content[0].sum_deliver_rate

              };
              let tk = {
                shop_name: '占总退款比:',
//                deliver_amount:content[0].tk_proportion_deliver_amount,
                cancel_amount:content[0].tk_proportion_cancel_amount,
                overpay_amount:content[0].tk_proportion_overpay_amount,
                preferential_amount:content[0].tk_proportion_preferential_amount,
                delay_amount:content[0].tk_proportion_delay_amount,
                compensate_amount:content[0].tk_proportion_compensate_amount,
                repair_amount:content[0].tk_proportion_repair_amount,
                returns_amount:content[0].tk_proportion_returns_amount,
                single_discount_amount:content[0].tk_proportion_single_discount_amount,
                carriage_amount:content[0].tk_proportion_carriage_amount,
                three_amount:content[0].tk_proportion_three_amount,
                o2o_diff_amount:content[0].tk_proportion_o2o_diff_amount,
                price_diff_amount:content[0].tk_proportion_price_diff_amount,
                discount_amount:content[0].tk_proportion_discount_amount,
//                shop_amount:content[0].tk_proportion_shop_amount,
//                refund_rate:content[0].tk_proportion_refund_rate,
//                deliver_rate:content[0].tk_proportion_deliver_rate
              };
              let fh = {
                shop_name: '占发货业绩比:',
//                deliver_amount:content[0].fh_proportion_deliver_amount,
                cancel_amount:content[0].fh_proportion_cancel_amount,
                overpay_amount:content[0].fh_proportion_overpay_amount,
                preferential_amount:content[0].fh_proportion_preferential_amount,
                delay_amount:content[0].fh_proportion_delay_amount,
                compensate_amount:content[0].fh_proportion_compensate_amount,
                repair_amount:content[0].fh_proportion_repair_amount,
                returns_amount:content[0].fh_proportion_returns_amount,
                single_discount_amount:content[0].fh_proportion_single_discount_amount,
                carriage_amount:content[0].fh_proportion_carriage_amount,
                three_amount:content[0].fh_proportion_three_amount,
                o2o_diff_amount:content[0].fh_proportion_o2o_diff_amount,
                price_diff_amount:content[0].fh_proportion_price_diff_amount,
                discount_amount:content[0].fh_proportion_discount_amount,
//                shop_amount:content[0].fh_proportion_shop_amount,
//                refund_rate:content[0].fh_proportion_refund_rate,
//                deliver_rate:content[0].fh_proportion_deliver_rate
              };
              resolve([sum,tk,fh])

            }
          }, err => {
            this.$message.error(err);
            this.queryBtnStatus = false;
          })
        })


      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
