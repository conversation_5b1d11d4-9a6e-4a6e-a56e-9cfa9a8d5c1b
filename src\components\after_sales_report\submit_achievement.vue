<!-- 售后单据提交业绩报表 -->
<template>
  <div
    id="submit_achievement_list"
    style="height:100%;"
  >
    <xpt-list
      ref="submitAchievement"
      :orderNo="false"
      :selection="params.fromAlert ? 'radio':''"
      :searchHolder="params.fromAlert ? '':'true'"
      :btns="btns"
      :colData="cols"
      :data="tableData"
      :pageLength="search.page_size"
      :pageTotal="totalPage"
      :searchPage="search.page_name"
      @radio-change='radioChange'
      @search-click="searchClick"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    ></xpt-list>
  </div>
</template>

<script>
import Fn from "@common/Fn.js";
import xptList from "../common/list-dynamic-new/list-dynamic"

export default {
  components:{
    xptList
  },
  props: ["params"],
  data() {
    let self = this;
    return {
      btns: [
        {
          type: "success",
          txt: "刷新",
          loading: false,
          disabled: false,
          click() {
            self.getList();
          },
        },
        {
          type: "primary",
          txt: "导出",
          loading: false,
          disabled: false,
          click() {
            self.exportExcel();
          },
        },
        {
          type: "primary",
          txt: "导出结果查询",
          loading: false,
          disabled: false,
          click() {
            self.showExportList("EXCEL_TYPE_REPORT_AFTERSALE_ORDER");
          },
        },
      ],
      cols: [
        {
          label: "单据编号",
          prop: "after_order_no"
        },
        {
          label: "合并单号",
          prop: "merge_trade_no",
        },
        {
          label: "买家昵称",
          prop: "buyer_nick"
        },
        {
          label: "业务员分组",
          prop: "staff_group_name"
        },
        {
          label: "提交业绩金额",
          prop: "amount_count"
        },
        {
          label: "业务员",
          prop: "staff_name"
        },
        {
          label: "提交售后日期",
          format: 'dataFormat',
          prop: "submit_time"
        },
        {
          label: "业务锁定日期",
          format: 'dataFormat',
          prop: "lock_time"
        },
        {
          label: "业务状态",
          prop: "status"
        },
        {
          label:"完结时间",
          format: 'dataFormat',
          prop:"finish_time"
        },
        {
          label: "是否三个月前售后",
          prop: "if_three_age"
        },
        {
          label: "业务锁定人",
          prop: "locker_name" 
        },
        {
          label: "业务锁定人分组",
          prop: "locker_group_name"
        },
        {
          label: "售后锁定大分组",
          prop: "sale_locker_big_group_name"
        },
        {
          label: "订单店铺",
          prop: "shop_name"
        },
        {
          label: "关闭状态",
          prop: "close_status"
        }
      ],
      tableData: [],
      totalPage: 0,
      search: {
        page_no: 1,
        page_size: 200,
        page_name: "report_aftersale_order", //搜索条件在这字段配置
        where: [],
      },
      select: {}, //单选
    };
  },
  watch: {},
  methods: {
    // //  获取展示信息字段
    // showDesc() {
    //   let params = {
    //     page_code : this.search.page_name,
    //     type: "show",
    //     user_id: Fn.getUserInfo('id')
    //   };
    //   this.ajax.postStream('/user-web/api/new/showDesc', params, res => {
    //     if(res.body.result) {
    //       this.cols = res.body.content.data;
    //       this.getList();
    //     } else {
    //       this.$message.error("获取默认显示方案失败！");
    //       return
    //     }
    //   }, err => {
    //     this.$message.error(err);
    //   });
    // },
    // 获取售后单据提交业绩报表
    getList(resolve) {
      this.btns[0].loading = true;
      let params = {
        page_no: this.search.page_no,
        page_size: this.search.page_size,
        page_name: this.search.page_name,
        where: this.search.where,
      };
      this.ajax.postStream(
        "/reports-web/api/reports/aftersale/order/excelList",
        params,
        (res) => {
          if (res && res.body.result) {
            this.tableData = res.body.content.list;
            this.totalPage = res.body.content.count;
          } else {
            this.$message.error(res.body.msg);
          }
          resolve && resolve();
          this.btns[0].loading = false;
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
          this.btns[0].loading = false;
        }
      );
    },
    // 导出功能
    exportExcel() {
      let params = {
        page_name: this.search.page_name,
        where: this.search.where,
      };
      this.ajax.postStream(
        "/reports-web/api/reports/aftersale/order/excelExport",
        params,
        (res) => {
          this.$message({
            type: res.body.result ? "success" : "error",
            message: res.body.msg,
          });
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 导出报表结果
    showExportList(exportType) {
      let fromData = {
        // page_no: this.search.page_no,
        // page_size: this.search.page_size,
        page_no: 1,
        page_size: 20,
        type: exportType,
      };
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "报表导出列表",
        params: {
          query: fromData,
        },
      });
    },
    // 搜索
    searchClick(obj, resolve) {
      this.search.where = obj;
      this.getList(resolve);
    },
    // 当前条数
    pageSizeChange(ps) {
      this.search.page_size = ps;
      this.getList();
    },
    // 当前页数
    pageChange(no) {
      this.search.page_no = no;
      this.getList();
    },
    // 修改对应措施
    checkAlertStatus() {
      if (!this.params.fromAlert) {
        return;
      } else if (this.params.fromAlert) {
        const self = this;
        let nowbtns = [
          {
            type: "success",
            txt: "刷新",
            loading: false,
            disabled: false,
            click() {
              self.getList();
            },
          },
        ];
        this.btns = nowbtns;
      }
    },
    // 单选
    radioChange(obj) {
      this.select = obj;
    },
    // 单选确定
    choiceItem() {
      if (!this.select) {
        this.$message.warning("尚未选中相关数据");
        return;
      } else if (this.select.remark) {
        this.$message.error("选中数据已有处理内容，无法关联");
        return;
      }
      let item = JSON.parse(JSON.stringify(this.select));
      this.params.callback(item);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },

    manualSyncStatusFun() {
      this.manualSyncStatus = true;
    },
  },
  mounted() {
    // 检测到当前是alert形式打开，修改按钮以及界面
    this.checkAlertStatus();
    this.getList();
  },
  destroyed() {},
};
</script>

<style>
#submit_achievement_list .remarkClassByCallCenterCallRecordList {
  height: 100% !important;
}

#submit_achievement_list .remarkClassByCallCenterCallRecordList .cell {
  height: 100% !important;
  line-height: 20px !important;
}

#submit_achievement_list .remarkClassByCallCenterCallRecordList p {
  white-space: pre-line;
}
</style>
