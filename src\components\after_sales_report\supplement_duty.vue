<!-- 补件责任分析报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="开始日期：" prop='begin_date'>
            <el-date-picker v-model="query.begin_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="供应商：" >
            <el-input v-model='query.supplier'  size='mini' ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="结束日期：" prop='end_date'>
            <el-date-picker v-model="query.end_date" type="date" placeholder="选择日期" size='mini' :editable='false' ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="商品编码：" >
            <el-input v-model='query.fnumber'  size='mini' ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="责任人：">
            <el-input v-model='query.duty_person'   size='mini'></el-input>
          </el-form-item>
          <el-form-item label="批号：" >
            <el-input v-model='query.batch_number'  size='mini' ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          batch_number:'',
          duty_person:'',
          fnumber:'',
          supplier:''
        },
        cols: [
          {
            label: '日期',
            prop: 'record_day',
            format:"dataFormat"
          }, {
            label: '单据编号',
            prop: 'duty_no',
          }, {
            label: '合并单号',
            prop: 'merge_no',
          }, {
            label: '售后单号',
            prop: 'after_sale_no'
          }, {
            label: '买家昵称',
            prop: 'buyer_name',
          }, {
            label: '来源单据类型',
            prop: 'source_type',
          }, {
            label: '来源单据号',
            prop: 'source_no'
          }, {
            label: '责任状态',
            prop: 'duty_status'
          }, {
            label: '商品编码',
            prop: 'fnumber',
          }, {
            label: '商品名称',
            prop: 'fname'
          }, {
            label: '规格描述',
            prop: 'fspec'
          }, {
            label: '收货人',
            prop: 'receiver'
          }, {
            label: '责任问题',
            prop: 'duty_problem'
          }, {
            label: '责任类型',
            prop: 'duty_type'
          }, {
            label: '责任人',
            prop: 'duty_person'
          }, {
            label: '责任金额',
            prop: 'duty_amount'
          }, {
            label: '处理金额',
            prop: 'deal_amount'
          }, {
            label: '备注',
            prop: 'remark'
          }, {
            label: '批号',
            prop: 'batch_number'
          }, {
            label: '供应商',
            prop: 'supplier'
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {

      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date =+new Date(data.end_date);
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/pageReportSupplyDuty', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.begin_date = +new Date(data.begin_date);
            data.end_date =+new Date(data.end_date);
            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportSupplyDuty', data, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
