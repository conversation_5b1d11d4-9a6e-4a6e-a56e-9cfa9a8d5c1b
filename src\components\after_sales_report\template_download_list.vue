<!-- 导入模板下载列表 -->
<template>
	<xpt-list
		ref='templateDownloadList'
		:data='list'
		:btns='btns'
		:colData='cols'
		:pageTotal='count'
		:searchPage='search.page_name'
		isNeedClickEvent
		@search-click='presearch'
        selection="radio"
		@radio-change='handleSelectionChange'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
	>
        <template slot="operate" slot-scope='scope'>
            <el-button type='primary' size='mini' @click="() => downOperate(scope.row)">下载</el-button>
        </template>
    </xpt-list>
</template>
<script>
export default {
	data(){
		var self = this;
		return {
			search:{
				page_name:"template_download_list",
				where:[],
				page_size:self.pageSize,     //页数
				page_no:1   //页码
			},
			list:[],
			count:0,
			multipleSelection: '',
			btns: [
				{
					type: 'success',
					txt: '刷新',
					click() {
						self.getList()
					},
					loading: false
				}, {
					type: 'primary',
					txt: '新增',
                    disabled() {
                        return !self.ifWtfkdealer
                    },
					click() {
						self.addNew('ADD')
					}
				}, {
					type: 'success',
					txt: '修改',
                    disabled() {
                        return !self.ifWtfkdealer
                    },
					click() {
						self.addNew('CHANGE')
					}
				}
			],
			cols: [
				{
					label: '页面名称',
					prop: 'page_name',
				}, {
					label: '模板名称',
					prop: 'template_name',
				}, {
					label: '备注',
					prop: 'remark',
					width: 150
				}, {
					label: '创建人',
					prop: 'creator_name',
				}, {
					label: '创建时间',
					prop: 'create_time',
                    format:'dataFormat1',
					width: 130
				}, {
					label: '最后更新人',
					prop: 'modifier_name',
					width: 120
				},{
					label: '最后更新时间',
					prop: 'modify_time',
                    format:'dataFormat1',
					width: 130
				}, {
					label: '操作',
					slot: 'operate',
				}
			],
            ifWtfkdealer: false
		}
	},
	created () {
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',this.getEmployeeInfoGroupList);
	},
	beforeDestroy(){
		this.$root.eventHandle.$off('resetAllBtnStatus',this.getEmployeeInfoGroupList);
	},
	methods:{
        getEmployeeInfoGroupList(){
            //获取当前处理人的业务分组列表
          this.ajax.postStream('/user-web/api/userPerson/getUserPersonGroupList',{personId:this.getEmployeeInfo('personId')}, res=>{
            if(res.body.result && res.body.content){
              let list = res.body.content.list||[];
              this.ifWtfkdealer = list.some(item => {
				  let ifDisabled = item.disableTime ? item.disableTime > new Date().getTime() : true
				  return item.salesmanType === 'IT_DEVOPS_GROUP' && item.enableTime < new Date().getTime() && ifDisabled
              })
            } else {
              this.ifWtfkdealer = false
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.ifWtfkdealer = false
            this.$message.error(err);
          });
        },
        downOperate(item) {
			this.download(item.url, item.template_name)
        },
		//命名转换中文
        download(url, filename) {
            if (!fetch) {
                window.location.href = url;
                return;
            }
            return fetch(url).then((res) => {
                res.blob().then((blob) => {
                    let a = document.createElement("a"); 
                    let url = window.URL.createObjectURL(blob);
                    a.href = url;
                    a.download = filename;
                    a.click();
					a.remove()
                    window.URL.revokeObjectURL(url);
                });
            });
        },
        // 新增
        addNew (type) {
            if(type == 'CHANGE' && !this.multipleSelection) {
                this.$message.error('请选择一行数据')
                return
            }
            let self = this
            let params = {
				callback(res){
                    self.getList()
				},
                row: type === 'ADD' ? {} : this.multipleSelection
			};
			self.$root.eventHandle.$emit('alert', {
				params:params,
				component: () => import('@components/after_sales_report/add_template_down.vue'),
				close:function(res){
				},
				style:'width:500px;height:300px',
				title:'新增模板',
			});
        },
        handleSelectionChange(val){
			this.multipleSelection = val;
		},
        sizeChange(size){
			// 第页数改变
			this.search.page_size = size;
			this.getList();
		},
		pageChange(page_no){
			// 页数改变
			this.pageNow = page_no;
			this.search.page_no = page_no;
			this.getList();
		},
        presearch(list, resolve){
			this.search.where = list;
			this.getList(resolve);
		},
        // 获取列表数据
        getList(resolve){
			let self = this;
			this.ajax.postStream('/reports-web/api/template/list',self.search,function(response){
				if(response.body.result){
                    self.list= response.body.content.list;
					self.count = response.body.content.count;
                    self.$message.success(response.body.msg);
				}else{
					self.list = [];
					self.count = 0 ;
					self.$message.error(response.body.msg || '');
				}
				resolve && resolve()
			}, err => {
				self.$message.error(err);
			});
		},
    },
	mounted: function(){
		this.getList()
        this.getEmployeeInfoGroupList()
  	},
	destroyed(){
		this.$root.offEvents('refresh_list');
	}
}
</script>
