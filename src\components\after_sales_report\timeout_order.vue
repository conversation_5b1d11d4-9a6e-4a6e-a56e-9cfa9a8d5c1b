<!-- 超时处理订单报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="年份：" prop='year'>
            <el-select size="mini"  v-model="query.year" placeholder="请选择">
              <el-option
                v-for="(value,key) in yearList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
              <el-tooltip v-if='rules.year[0].isShow' class="item" effect="dark" :content="rules.year[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-select>
          </el-form-item>
          <el-form-item label="大分组：">
            <xpt-input v-model='query.big_group' icon='search' :on-icon-click='openBigGroup' size='mini' @change='bigGroupChange'></xpt-input>
          </el-form-item>
          <el-form-item label="超时时长≥：">
            <xpt-input v-model='query.time_out_begin' size='mini' clearable @input="() => checkTime(query.time_out_begin)" type="number"></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="月份：" prop='month'>
            <el-select size="mini"  v-model="query.month" placeholder="请选择">
              <el-option
                v-for="(value,key) in monthList"
                :label="value"
                :value="key" :key='key'>
              </el-option>
              <el-tooltip v-if='rules.month[0].isShow' class="item" effect="dark" :content="rules.month[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-select>
          </el-form-item>
          <el-form-item label="分组：">
            <xpt-input v-model='query.staff_group' icon='search' :on-icon-click='openGroup' size='mini' @change='groupChange'></xpt-input>
          </el-form-item>
          <el-form-item label="超时时长≤：">
            <xpt-input v-model='query.time_out_end' size='mini' clearable @input="() => checkTime(query.time_out_end)" type="number"></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="业务员：">
            <xpt-input v-model='query.staff_name' icon='search' :on-icon-click='openSatffOfTimeoutOrder'  size='mini' @change='staffChangeofTimeoutOrder'></xpt-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button><br>
          <el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_AFTER_SALE_TIMEOUTPROCESSORDER")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          year: '',
          month: '',
          staff_name: '',
          staff_id: '',
          staff_group: '',
          staff_group_id: '',
          big_group: '',
          big_group_id: '',
          time_out_end: '',
          time_out_begin: ''
        },
        //所有人员类型
        salesmanTypeList:__AUX.getCode('businessType'),
        cols: [
          {
            label: '月份',
            prop: 'years',
          }, {
            label: '来源单号',
            prop: 'after_order_no',
          }, {
            label: '合并单号',
            prop: 'merge_trade_no',
          }, {
            label: '买家昵称',
            prop: 'buyer_nick'
          }, {
            label: '业务员',
            prop: 'staff_name',
          }, {
            label: '分组',
            prop: 'staff_group_name',
          }, {
            label: '大分组',
            width: 100,
            prop: 'big_group_name'
          }, {
            label: '提交售后时间',
            prop: 'submit_time',
            width: 130,
            format: 'dataFormat1'
          }, {
            label: '第一处理进度时间',
            prop: 'handle_time',
            width: 130,
            format: 'dataFormat1'
          }, {
            label: '超时时长',
            prop: 'time_out_time',
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if(self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {
      // 选择业务员，arg为true 时选择人员类型的负责人
      openSatffOfTimeoutOrder(staffType, arg) {
        let self = this
        let params = {
          callback(data) {

            self.query.staff_id = data.userId;
            self.query.staff_name = data.fullName;
          },
          salesmanTypeList:this.salesmanTypeList || []
        }
        this.$root.eventHandle.$emit('alert', {
          params: params,
          component:() => import('@components/after_sales_report/select_personel'),
          style: 'width:800px;height:500px',
          title: '人员列表'
        })
      },
      staffChangeofTimeoutOrder(val) {
        if(!val) {
          this.query.staff_id = '';
          this.query.staff_name = '';
        }
      },
      checkTime(time) {
        if (!(/^[0-9]*$/.test(time))) {
          this.$message.error('超时时长请输入大于0的整数')
          return
        }
      },
      //得到查询条件
      getPostData(){
        let self =this;
        let data = JSON.parse(JSON.stringify(this.query));
        let begin_date = self.getStartDate(data.year,data.month);
        let end_date =self.getEndDate(data.year,data.month);
        var postData={
          page_no: data.page_no,
          page_size: data.page_size,
          begin_date: begin_date,
          end_date: end_date,
          staff_name: data.staff_name,
          staff_id: data.staff_id,
          staff_group_name: data.staff_group,
          staff_group_id: data.staff_group_id,
          big_group_name: data.big_group,
          big_group_id: data.big_group_id,
          time_out_end: data.time_out_end,
          time_out_begin: data.time_out_begin
        }
        return postData;
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            if ((this.query.time_out_begin && !(/^[0-9]*$/.test(this.query.time_out_begin))) || (this.query.time_out_end && !(/^[0-9]*$/.test(this.query.time_out_end)))) {
              this.$message.error('超时时长请输入大于0的整数')
              return
            }
            this.queryBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSale/listTimeOutProcessOrder', this.getPostData(), res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content.body;
                this.list = content.list || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      // 导出功能
      exportExcel() {
        this.$refs.query.validate((valid) => {
          if(valid) {

            this.exportBtnStatus = true;
            this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportTimeOutProcessOrder', this.getPostData(), res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
          }
        })
      }
    },
    computed: {
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff_name(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff_name = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff_name = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
