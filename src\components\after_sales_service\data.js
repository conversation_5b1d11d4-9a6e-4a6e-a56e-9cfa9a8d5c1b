// 服务单--数据声明
import vl from '@common/validate.js';
import { BILL_TYPE_STATUS, TAOBAO_STATUS } from "@/components/after_solutions/enum";
export default {
	data() {
		let self = this;
		return {
			base: {
				/*基本信息*/
				after_ticket_no: '', //单据编号
				batch_no: '', //批次订单号
				merge_trade_no: '', //合并订单号
				confirm_status: '', //确认状态
				buyer_name: '', //买家昵称（冗余字段）
				creator_name: '', //创建人姓名
				creator_group_name: '', //
				saleman: '', //业务员ID
				saleman_name:null,//业务员
				saleman_group:null,//业务员分组ID
				saleman_group_name:null,//业务员分组名字
				three_phone: '', //三包公司电话
				finish_time: '', //指令工单完成时间

				pulled_sign: '', //已拉货标识
				schedule_finish_status: '', //进度完结状态
				feedback_duration: '', //反馈时长
				delivery_time: '', //发货时间
				logistics_supplier: '', //物流供应商
				three_supplier: '', //三包供应商
				logistics_no: '', //物流单号
				delivery_name: '', //提货点
				delivery_phone: '', //提货点电话

				receiver_name: '',//收货人
				receiver_phone: '', //收货人手机
				receiver_addr: '',	//收货人地址
				province: '',	// 省
				city: '',	// 市
				area: '',	// 区
				street: '',	// 街道

				service_request: '', //服务请求
				que_type: 'SD',	// 问题类型
				compensate_amount: '', //赔偿金额
				service_satisfaction: '', //服务满意度
				service_type: '', //服务类型，新增的时候默认平急咨询服务
				door_service_time: '', //上门服务时间
				commitment_time: '',//承诺上门时间

				service_explain: '', //服务说明

				id: '',	//主键ID
				merge_trade_id: '', //合并订单id
				batch_id: '',

				delivery_id: '', //提货点ID
				If_cargo:null,//是否上门取货

				invalid_name : null,//执鼎作废人

				invalid_note:null,//执鼎作废备注
				invalid_time:null,//执鼎作废时间
				document_type :null,//单据类型
				appointment_service_time:null,//预约服务时间

				/*方案信息*/
				aftersaleBillServiceHandleVO: {
					version: '', //方案版本
					status: '', //方案状态
					handle_type: '', //处理类型
					handle_explain: '', //处理说明
					expense_type: '', //支出类型
					expense_object: '', //支出对象
					expense_amount: '', //支出金额
					expense_explain: '', //支出说明
					income_type: '', //收入类型
					income_object: '', //收入对象
					income_amount: '', //收入金额
					income_explain: '', //收入说明
					deduct_type: '', //扣款类型
					settle_way: '', //结款方式
					repair_amount: '', //维修金额
					repair_provider: '', //维修供应商
				    //invalid_note: null,		//	作废备注
				},

				/*其它信息*/
				status: '', //单据状态
				business_status: '', //业务状态
				create_time: '', //创建时间
				lock_name: "", // 业务锁定人
				responsibility_bill_no: '',// 责任来源单号

				auditor_name: '', //审核人姓名
				audit_time: '', //审核时间
				cancel_name: '',	//服务取消人
				cancel_time: '',		// 服务取消时间
				lock_time: '',	// 业务锁定日期
				responsibility_bill_type: '',// 责任来源单类型

				wms_jf_lock_name: '', //WMS纠纷锁定人
				lock_group_name: '', //锁定人员分组
				wms_jf_audit_name: '', //WMS纠纷审核人
				submit_request_name: '', //提交服务人
				submit_request_time: '', //提交需求时间

				end_name: '', //完结人名称
				end_time: '', //完结时间
				api_status: '', //接口状态
				transmit_time: '', //下达时间
				api_transfer_result: '', //传送结果提示最长200

				if_dealer:null,//是否经销商订单
				dealer_customer_id:null,//经销商ID
				dealer_customer_name:null,//经销商名称
				dealer_customer_number:null,//经销商编码

				/*问题商品*/
				listHandle: [],
				/*客户*/
				listCustomer: [],
				/*历史处理方案*/
				listHandleHis: [],
        /*客户承担费用信息*/
        bearCostsList: [],
				/*处理进度*/
				listHandleLog: [],
				/*操作记录*/
				operateLogs: [],
			},
			// 商品商品列表字段
			goodsCols: [
				{
					label: '买家昵称',
					prop: 'buyer_name'
				}, /*{
					label: '来源单号',
					prop: 'source_order_no',
					width: 120
        		},*/ {
					label: '店铺',
					prop: 'shop_name'
        		}, {
					label: '商品编码',
					prop: 'goods_code'
				}, {
		          	label: '商品名称',
		          	prop: 'goods_name'
		        }, {
					label: '规格描述',
					prop: 'goods_desc'
				}, {
					label: '问题描述',
					prop: 'question_description',
					bool: true,
					isInput: true,
					width: 120,
					maxLength: 500
				}, {
					label: '物料编码',
					prop: 'goods_material_code'
				}, {
					label: '物料名称',
					prop: 'material_name'
				}, {
					label: '物料规格描述',
					width: 100,
					prop: 'material_desc'
				}, {
					label: '单位',
					prop: 'units'
				}, {
					label: '数量',
					prop: 'number'
				}, {
					label: '包件数',
					prop: 'pkg_qty'
				}, {
					label: '实际售价',
					prop: 'actual_price'
				}, {
					label: '批号',
					prop: 'lot'
				}, {
					label: '收货人',
					prop: 'receiver_name'
				}, {
					label: '批次单号',
					prop: 'batch_order_no'
				}, {
					label: '取货费用',
					prop: 'fetch_goods_fee',
				}, {
					label: '体积',
					prop: 'volume'
				}, {
					label: '退货运费',
					prop: 'returns_delivery_fee'
				}, {
					label: '来源类型',
					prop: 'source_type',
					format: 'auxFormat',
					formatParams: 'afsSourceOrderType'
				},
        {
          prop: "returns_type",
          label: "退货方式",
          width: 100,
          formatter: prop => ({
						RETURN: '退货',
						CHANGE: '换货',
					}[prop] || prop),
        },
        {
          prop: "change_goods_type",
          label: "换货商品",
          width: 100,
          formatter: prop => ({
						SAME: '同商品',
						DIFF: '不同商品',
					}[prop] || prop),
        },
			],
			// 客户列表字段
			customerCols: [
		        {
		          	label: '系统单号',
		          	prop: 'order_no'
		        }, {
		          	label: '客户',
		          	prop: 'customer'
		        }, {
		          	label: '收入店铺',
		          	prop: 'sales_store'
		        }, {
		          	label: '原始店铺',
		          	prop: 'income_store'
		        }
		    ],
		    // 操作记录列表字段
		    operaterCols: [
				{
					label: '操作人',
					prop: 'operator_name'
				}, {
					label: '业务操作',
					prop: 'operate_type',
					format: 'auxFormat',
					formatParams: 'afsOperatorType'
				}, {
					label: '操作描述',
					prop: 'description'
				}, {
					label: '操作时间',
					prop: 'operate_time',
					format: 'dataFormat1'
				}
			],
			// 历史处理方案列表字段
			handleHisCols:[
				{
					label: '方案版本',
					prop: 'version'
				}, {
					label: '处理类型',
					prop: 'handle_type',
					format: 'auxFormat',
					formatParams: 'afsHandleType'
				}, {
					label: '扣款类型',
					prop: 'deduct_type',
					format: 'auxFormat',
					formatParams: 'afsDeductionsType'
				}, {
				  	label: '结算方式',
				  	prop: 'settle_way',
				  	format: 'auxFormat',
				  	formatParams: 'afsJiesuanType'
				}, {
				  	label: '支出类型',
				  	prop: 'expense_type',
					format: 'expenseType'
				}, {
				  	label: '支出对象',
				  	prop: 'expense_object'
				}, {
				  	label: '支出金额',
				  	prop: 'expense',
				}, {
				  	label: '支出说明',
				  	prop: 'expense_explain'
				}, {
				  	label: '收入类型',
				  	prop: 'income_type',
					format: 'incomeType'
				}, {
				  	label: '收入对象',
				  	prop: 'income_object'
				}, {
				  	label: '收入金额',
				  	prop: 'income',
				}, {
				  	label: '收入说明',
				  	prop: 'income_explain'
				}, {
				  	label: '变更人',
				  	prop: 'creator_name'
				}, {
				  	label: '变更时间',
				  	prop: 'create_time',
				  	format: 'dataFormat1'
				}, {
				  	label: '处理说明',
				  	prop: 'handle_explain'
				}
			],
      handleBearCostCols: [
        { label: '平台订单号', prop: 'tid' },
        {
          label: '订单业务模式',
          prop: 'orderBusinessMode',
          formatter: (val) => {
            return (
              BILL_TYPE_STATUS[val] || val
            );
          }
        },
        { label: '合并单号', prop: 'mergeTradeNo' },
        {
          label: '淘宝交易状态',
          prop: 'status',
          formatter: (val) => {
            return (
              TAOBAO_STATUS[val] || val
            );
          } },
        { label: '客户承担费用', prop: 'bearFee' }
      ],
			// 处理进度列表字段
			handleLogCols: [
				{
					label: '处理人',
					prop: 'handler_name'
				}, {
					label: '处理时间',
					prop: 'handle_time',
					format: 'dataFormat1'
				}, {
					label: '处理内容',
					prop: 'handle_content'
				}
			],
			rules: {
				//服务分类不能为空
		          service_type:vl.isNotBlank({
		            required:true,
		            self: this,
		            msg: '服务分类不能为空'
		          }),
		          //服务类型不能为空
		          service_type_one:vl.isNotBlank({
		            required:true,
		            self: this,
		            msg: '服务类型不能为空'
		          }),
		          //承诺上门时间不能为空
		          // commitment_time:vl.isNotBlank({
		          //   required:true,
		          //   self: this,
		          //   msg: '承诺上门时间不能为空'
		          // }),
				compensate_amount: [{
					message:'赔偿金额最多保留两位小数',
					trigger: 'change',
					isShow:false,
					validator: function(rule, value, callback){
						if(value) {
							if(/^(0{1}|[1-9]\d*)(?:\.\d{1,2})?$/.test(value + '')) {
								self.rules[rule.field][0].isShow = false
								callback();
							} else {
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						} else {
							self.rules[rule.field][0].isShow = false
							callback();
						}
					}
				}],
				service_explain: [{
					message: '请输入服务说明',
					trigger: 'blur',
					isShow: false,
					required: true,
					validator(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false;
							callback();
						} else {
							self.rules[rule.field][0].isShow = true;
							callback(new Error(''));
						}
					}
				}]
			},
			baseCols: [
				[
					{
						label: '单据编号:',
						key: 'after_ticket_no'
					}, {
						label: '批次单号:',
						key: 'batch_no'
					}, {
						label: '合并单号:',
						key: 'merge_trade_no'
					}, {
						label: '确认状态:',
						key: 'confirm_status',
						format: 'auxFormat',
						formatParams: 'afsConfirmStatus'
					}, {
						label: '买家昵称:',
						key: 'buyer_name'
					}, {
						label: '业务员:',
						key: 'saleman_name'
					}, {
						label: '业务员分组:',
						key: 'saleman_group_name'
					}, {
						label: '三包公司电话:',
						key: 'three_phone',
						slot: 'three_phone'
					},
					{
						label: '是否上门取货:',
						slot: 'if_cargo'
					}
					,{
						label: '4PL平台作废人：',
						key: 'invalid_name'
					},{
						label: '单据类型',
						key: 'document_type',
						formatter(val){
							return val == 'JFLX0002' ? '售后单' : val == 'JFLX0003'?'增值服务单':val;
						}
					},{
						label: '再售客户',
						key: 'resale_cust',

					},{
						label: '快递转寄/返货单号:',
						key: 'express_forward_return_number'
					},{
            label: '客户承担费用:',
            key: 'customer_fee'
          },{
            label: '服务商收取服务费用:',
            key: 'if_charge_service_fees',
            formatter(val){
              return val !== 'N'? val === 'Y'?'是':'':'否';
            }
          }
				], [
					{
						label: '已拉货标识:',
						key: 'pulled_sign',
						format: 'auxFormat',
						isShow : false,
						formatParams: 'afsPulledsign'
					}, {
                        label: '销售标签:',
                        key: 'sales_label',
                        html(val) {
                            return val ? '<span style="color:red;">'+val+'</span>' : val;
                        },
                    },{
						label: '进度完结状态:',
						key: 'schedule_finish_status',
						format: 'auxFormat',
						formatParams: 'afsFinishStatus'
					}, {
						label: '反馈时长:',
						key: 'feedback_duration'
					}, {
						label: '发货时间:',
						key: 'delivery_time',
						format: 'dataFormat1'
					}, {
						label: '物流供应商:',
						key: 'logistics_supplier'
					}, {
						label: '三包供应商:',
						key: 'three_supplier'
					}, {
						label: '物流单号:',
						key: 'logistics_no'
					}, {
						label: '提货点:',
						key: 'delivery_name'
					}, {
						label: '提货点电话:',
						key: 'delivery_phone',
						slot: 'delivery_phone'
					}, {
						label: '三包点:',
						key: 'three_name'
					},
					{
						label: '来源类型:',
						key: 'bill_type',
						format: 'auxFormat',
						formatParams: 'afsSourceOrderType'
					}
					,{
						label: '4PL平台作废备注:',
						key: 'invalid_note'
					},{
            label: '4PL平台作废类型:',
            key: 'storage_cancel_type',
            format: 'auxFormat',
            formatParams: 'STORAGE_CANCEL_TYPE'
          },
          {
						label: '再售折扣:',
						key: 'sales_discount',
					}
				], [
					{
						label: '收货人:',
						key: 'receiver_name'
					}, {
						label: '收货人手机:',
						key: 'receiver_phone',
						slot: 'receiver_phone'
					}, {
						label: '客户收货地址:',
						key: 'receiver_addr'
					}, {
						label: '省:',
						key: 'province'
					}, {
						label: '市:',
						key: 'city'
					}, {
						label: '区:',
						key: 'area'
					}, {
						label: '街道:',
						key: 'street'
					}, {
						label: '详细地址:',
						key: 'receiver_addr'
					}, {
						label: '来源单号:',
						key: 'source_bill_no',
            slot: 'source_bill_no'
					},{
						label: '4PL平台作废时间：',
						key: 'invalid_time',
						format: 'dataFormat1'
					},{
						label: '运输单号：',
						key: 'transport_no',
					},{
						label: '签约状态：',
						key: 'if_need_service_operation',
						formatter(val){
							return val != 'N'? val == 'Y'?'是':'':'否';
						}
					},{
						label: '货款充值状态：',
						key: 'funds_manage_status',
						formatter(val){
							return{
								'PAY_UNNEEDED':'无需充值',
								'PAY_NOT':'未充值',
								'PAY_EXEC':'充值中',
								'PAY_FINISH':'已充值'
							}[val]||val
						}
					}, {
						label: '货款充值单号:',
						key: 'fundsManageOrderNo',
					},
				], [
					{
						label: '服务请求类型:',
						key: 'service_request'
					}, {
						label: '问题类型:',
						key: 'que_type',
						format: 'auxFormat',
						formatParams: 'afsProblemType'
					}, {
						label: '赔偿金额:',
						slot: 'compensate_amount'
					}, {
						label: '满意度:',
						slot: 'service_satisfaction'
					},{
            label: '',
            slot: 'service_satisfaction_action',
          },{
						label: '不满意标签:',
						slot: 'dissatisfied_tag'
					},{
						label: '不满意备注:',
						slot: 'dissatisfied_remark'
					},
					{
						label: '服务类型:',
						slot: 'service_type_one'
					}, {
						label: '服务分类:',
						slot: 'service_type'
					}, {
						label: '客户期望上门时间:',
						slot: 'commitment_time'
					}, {
						label: '服务商与客户预约服务时间:',
						key: 'appointment_service_time',
						format: 'dataFormat1'
					}, {
						label: '指令工单完成时间:',
						key: 'finish_time',
						format: 'dataFormat1'
					}, {
						label: '索赔时间:',
						key: 'claim_time',
						format: 'dataFormat1',
					}, {
						label: '服务商分类:',
						key: 'logistics_supplier_class',
						formatter(val){
							return val == 'A'?'普通':'蓝豚';
						}
					}, {
						label: '结算主体:',
						key: 'settle_entity',
					}, {
						label: '服务说明:',
						slot: 'service_explain'
					},
				]
			],
			caseCols: [
				[
					{
						label: '方案版本:',
						key: 'version'
					}, {
						label: '方案状态:',
						key: 'status',
						format: 'auxFormat',
						formatParams: 'afsSchemeStatus'
					}, {
						label: '处理类型:',
						key: 'handle_type',
						format: 'auxFormat',
						formatParams: 'afsHandleType'
					}, {
						label: '处理说明:',
						key: 'handle_explain'
					}
				], [
					{
						label: '支出类型:',
						key: 'expense_type',
						format: 'expenseType'
					}, {
						label: '支出对象:',
						key: 'expense_object'
					}, {
						label: '支出对象名称:',
						key: 'expense_object_name'
					}, {
						label: '支出金额:',
						key: 'expense'
					}, {
						label: '支出说明:',
						key: 'expense_explain'
					}
				], [
					{
						label: '收入类型:',
						key: 'income_type',
						format: 'incomeType'
					}, {
						label: '收入对象:',
						key: 'income_object'
					}, {
						label: '收入对象名称:',
						key: 'income_object_name'
					},  {
						label: '收入金额:',
						key: 'income'
					}, {
						label: '收入说明:',
						key: 'income_explain'
					}
				], [
					{
						label: '扣款类型:',
						key: 'deduct_type',
						format: 'auxFormat',
						formatParams: 'afsDeductionsType'
					}, {
						label: '结款方式:',
						key: 'settle_way',
						format: 'auxFormat',
						formatParams: 'afsJiesuanType'
					}, {
						label: '维修金额:',
						key: 'repair'
					}, {
						label: '维修供应商:',
						key: 'repair_provider'
					}/*, {
						label: '作废备注:',
						key: 'invalid_note',
						formatter: prop => this.base.invalid_note,
					}*/
				]
			],
			otherCols: [
				[
					{
						label: '单据状态:',
						key: 'status',
						format: 'auxFormat',
						formatParams: 'afsStatus'
					}, {
						label: '业务状态:',
						key: 'business_status',
						format: 'auxFormat',
						formatParams: 'afsNativeBusinessStatus'
					}, {
						label: '创建人:',
						key: 'creator_name'
					}, {
						label: '创建日期:',
						key: 'create_time',
						format: 'dataFormat1'
					}, {
						label: '业务锁定人:',
						key: 'lock_name'
					}, {
						label: '责任来源单号:',
						key: 'responsibility_bill_no'
					}, {
            label: '处理进度完结时间:',
            key: 'progress_finish_time',
            format: 'dataFormat1'
          }
				], [
					{
						label: '审核人:',
						key: 'auditor_name'
					}, {
						label: '审核日期:',
						key: 'audit_time',
						format: 'dataFormat1'
					}, {
						label: '服务取消人:',
						key: 'cancel_name'
					}, {
						label: '服务取消时间:',
						key: 'cancel_time',
						format: 'dataFormat1'
					}, {
						label: '业务锁定日期:',
						key: 'lock_time',
						format: 'dataFormat1'
					}, {
						label: '责任来源单类型:',
						key: 'responsibility_bill_type',
						format: 'auxFormat',
						formatParams: 'afsSourceOrderType'
					}
				], [
					{
						label: 'WMS纠纷锁定人:',
						key: 'wms_jf_lock_name'
					}, {
						label: '锁定人员分组:',
						key: 'lock_group_name'
					}, {
						label: 'WMS纠纷审核人:',
						key: 'wms_jf_audit_name'
					}, {
						label: '提交服务人:',
						key: 'submit_request_name'
					}, {
						label: '提交服务日期:',
						key: 'submit_request_time',
						format: 'dataFormat1'
					},{
						label: '是否经销商订单:',
						key: 'if_dealer',
						formatter(val){
		  					return val == 'Y' ? '是' : val == 'N'?'否':val
		  				}
					}, {
						label: '单据来源:',
						key: 'source',
						format: 'auxFormat',
            formatParams: 'AFTERSALE_SOURCE_TYPE'
					}
				], [
					{
						label: '完结人:',
						key: 'end_name'
					}, {
						label: '完结时间:',
						key: 'end_time',
						format: 'dataFormat1'
					}, {
						label: '接口状态:',
						key: 'api_status',
						format: 'auxFormat',
						formatParams: 'afsInterfaceStatus'
					}, {
						label: '下达时间:',
						key: 'transmit_time',
						format: 'dataFormat1'
					}, {
						label: '传送结果:',
						key: 'api_transfer_result'
					},{
						label: '经销商名称',
						key: 'dealer_customer_name'
					},{
						label: '经销商编码',
						key: 'dealer_customer_number'
					}
				]
			]
		}
	},
  methods:{
    toSourceBillNoDetail(row){
      if (row.after_order_id) {
				this.$root.eventHandle.$emit('creatTab', {
					name: '售后单详情',
					params: { id: row.after_order_id },
					component: () => import('@components/after_sales/index'),
				})
			}else{
        this.$message.warning("功能开发中，请期待")
      }
    }
  }
}
