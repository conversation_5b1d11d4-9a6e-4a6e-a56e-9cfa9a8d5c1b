<!--服务单详情-->
<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button
        type='warning'
        size='mini'
        slot='left'
        @click='save()'
        :loading='saveBtnStatus'
        :disabled="
          saveBtnStatus
        "
      >保存</el-button>
      <el-button type='primary' size='mini' slot='left' @click='addNew'>新增</el-button>
      <el-button type='info' size='mini' slot='left' @click='selectOrder' :disabled='!isAdd'>选单</el-button>
      <el-button type='success' size='mini' slot='left' @click='refresh' :loading='refreshBtnStatus' :disabled='refreshBtnStatus||isAdd'>刷新</el-button>
      <el-button
        type='primary'
        size='mini'
        slot='left'
        @click='submitZD'
        :loading='submitBtnStatus'
        :disabled="
          submitBtnStatus ||
          submitStatus ||
          isAdd ||
          base.status === 'CRIPPLED'
        "
      >提交服务至4PL平台</el-button><!-- 单据状态 = 作废，不能点击提交服务至4PL和取消4PL服务 -->
      <el-button
        type='danger'
        size='mini'
        slot='left'
        @click='cancelZD'
        :loading='cancelBtnStatus'
        :disabled="
          cancelBtnStatus ||
          cancelStatus ||
          isAdd ||
          base.status === 'CRIPPLED'
        "
      >取消4PL平台服务</el-button><!-- 单据状态 = 作废，不能点击提交服务至4PL和取消4PL服务 -->
      <el-button
        type='primary'
        size='mini'
        slot='left'
        @click='uploadAccessory'
        :disabled="
          isAdd
        "
      >上传附件<xpt-upload :ifClickUpload='uploadClick' :dataObj='uploadObj' :callback='uploadCallback'></xpt-upload>
      </el-button>
      <el-button type='success' size='mini' slot='left' @click='viewAccessory' :loading='viewBtnStatus' :disabled='viewBtnStatus||isAdd'>查看附件</el-button>
      <el-button type='success' size='mini' slot='left' @click='pushConsultation' >专员介入</el-button>
      <el-button type='primary' size='mini' slot='left' @click='paymentRechargePush' :disabled="!canIPaymentRechargePush" :loading="paymentRechargePushLoading">下推货款充值</el-button>
    </xpt-headbar>
    <div>
      <el-tabs v-model="topTabs">
        <el-tab-pane label="基本信息" name="first">
          <xpt-form :cols='baseCols' :data='base' label='155px' :rules='rules' ref='detail'>
            <template slot='source_bill_no'>
              <a style="text-decoration:none;" href="javascript:;" @click="toSourceBillNoDetail(base)" title="">{{base.source_bill_no}}</a>
            </template>
            <!-- 赔偿金额 -->
            <template slot='compensate_amount'>
                <el-input size='mini'  v-model="base.compensate_amount" v-if='canEdit'></el-input>
                <span v-tooltip='base.compensate_amount' v-else>{{base.compensate_amount}}</span>
                <el-tooltip v-if='canEdit&&rules.compensate_amount[0].isShow' class="item" effect="dark" :content="rules.compensate_amount[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
                </el-tooltip>
            </template>

            <!-- 是否上门取货 -->
            <template slot='if_cargo'>
                <span>
                  {{base.if_cargo=='Y'?'是':base.if_cargo=='N'?'否':base.if_cargo}}
                </span>


            </template>

            <!-- 三包公司电话 -->
           <template slot='three_phone'>
                <xpt-eye-switch v-model="base.three_phone" :readonly="true" :hideBorder="true" :aboutNumber="base.after_ticket_no"></xpt-eye-switch>
            </template>
            <!-- 提货点电话 -->
            <template slot='delivery_phone'>
                <xpt-eye-switch v-model="base.delivery_phone" :readonly="true" :hideBorder="true" :aboutNumber="base.after_ticket_no"></xpt-eye-switch>
            </template>
            <!-- 收货人手机 -->
            <template slot='receiver_phone'>
                <xpt-eye-switch v-model="base.receiver_phone" :readonly="true" :hideBorder="true" :aboutNumber="base.after_ticket_no"></xpt-eye-switch>
            </template>
            <!-- 满意度
            指令工单有时间且方案页签的处理类型为维修才可以编辑，
            2018.3.28
             -->
            <template slot='service_satisfaction'>
              <!-- <xpt-select-aux v-model='base.service_satisfaction' aux_name='afsSatifcation' @change='satisfactionChange' v-if='(canEdit||canEditPart)&&base.finish_time&&("WX"==base.aftersaleBillServiceHandleVO.handle_type)'></xpt-select-aux>
              <span v-else>{{base.service_satisfaction|auxFormat('afsSatifcation')}}</span> -->
              <!-- *处理类型 = 维修 &&  指令工单完成时间有值 -->
              <!-- XPT-11243服务分类为补件安装/先送货后安装/调试/维修/上门拉货时，指令工单时间回传新平台后，服务满意度可编辑 -->
              <el-input :value="base.service_satisfaction|auxFormat('afsSatifcation')" size='mini' :maxlength='240' :disabled='true'></el-input>
            </template>
            <template slot="service_satisfaction_action">
              <el-button size='mini' type="primary" @click="preValid('SATISFIED')">满意</el-button>
              <el-button size='mini' type="primary" @click="preValid('DISSATISFIED')">不满意</el-button>
            </template>
            <template slot='dissatisfied_tag'>
              <el-input :value="base.dissatisfied_tag|auxFormat('FWDBQ')" size='mini' :maxlength='240' :disabled='true'></el-input>
            </template>
            <template slot='dissatisfied_remark'>
              <el-input  :value="base.dissatisfied_remark" size='mini' :maxlength='500' :disabled='true'></el-input>
            </template>
            <!-- 服务类型 -->
            <template slot='service_type_one'>
              <el-tooltip
                v-if='isAdd'
                class="item"
                effect="dark"
                placement="top"
                v-model="serviceTypeOneTooltipControl"
                :content="serviceTypeOneTooltipContent"
                :disabled="!serviceTypeOneTooltipContent || serviceTypeOneTooltipDisabled"
                :manual="serviceTypeOneTooltipControl"
              >
                <xpt-select-aux v-model='base.service_type_one' aux_name='serviceTypeOne' :disabledOption='disabledOption' @change='selectParent' @visible-change="bindOptionsItemHoverEvent" popperClass="service-type-one-select" @mouseover.native="serviceTypeOneTooltipDisabled = false" @mouseout.native="!bindOptionsItemHoverEvent.bool && (serviceTypeOneTooltipDisabled = true)"></xpt-select-aux>
              </el-tooltip>
              <span v-else>{{base.service_type_one|auxFormat('serviceTypeOne')}}</span>
            </template>
            <!-- 服务分类 -->
            <template slot='service_type'>
              <el-tooltip
                v-if='isAdd'
                class="item"
                effect="dark"
                placement="top"
                v-model="serviceTypeTooltipControl"
                :content="serviceTypeTooltipContent"
                :disabled="!serviceTypeTooltipContent || serviceTypeTooltipDisabled"
                :manual="serviceTypeTooltipControl"
              >
                <el-select v-model="base.service_type" placeholder="请选择" size="mini" @visible-change="serviceTypeSelectHover"  @mouseover.native="serviceTypeTooltipDisabled = false" @mouseout.native="!serviceTypeSelectHover.bool && (serviceTypeTooltipDisabled = true)">
                    <el-option
                      v-for="item in childClassData"
                      :key="item.code"
                      :label="item.name"
                      @mouseover.native="() => serviceTypeSelectItemHover(item.code)"
                      :disabled="disabledOption.some(o => o.tag == item.tag)"
                      :value="item.code">
                    </el-option>
                </el-select>
              </el-tooltip>
              <span v-else>{{base.service_type|auxFormat('serviceType')}}</span>

              <!-- <xpt-select-aux v-model='base.service_type' aux_name='serviceType' v-if='isAdd'></xpt-select-aux> -->
            </template>
            <!-- 承诺上门时间 -->
            <template slot='commitment_time'>
              <el-date-picker v-model='base.commitment_time' type="datetime" placeholder="选择日期" size='mini' :editable="false" v-if='canEdit'></el-date-picker>
              <span v-else>{{base.commitment_time|dataFormat1}}</span>
            </template>
            <!-- 上门服务时间 -->
            <template slot='door_service_time'>
              <el-date-picker ref='test' v-model='base.door_service_time' type="datetime" placeholder="选择日期" size='mini' :editable="false" v-if='canEdit'></el-date-picker>
              <span v-else>{{base.door_service_time|dataFormat1}}</span>
            </template>
            <!-- 服务说明 -->
            <template slot='service_explain'>
              <el-input type='textarea' size='mini' v-model='base.service_explain' :maxlength='255' v-if='canEdit'></el-input>
              <span v-tooltip='base.service_explain' v-else>{{base.service_explain}}</span>
              <el-tooltip v-if='canEdit&&rules.service_explain[0].isShow' class="item" effect="dark" :content="rules.service_explain[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
                </el-tooltip>
            </template>
          </xpt-form>
        </el-tab-pane>

        <el-tab-pane label="方案信息" name="second">
          <xpt-form :cols='caseCols' :data='base.aftersaleBillServiceHandleVO' label='120px'></xpt-form>
        </el-tab-pane>

        <el-tab-pane label="其他信息" name="three">
          <xpt-form :cols='otherCols' :data='base' label='120px'></xpt-form>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class='xpt-flex__bottom' v-fold>
      <el-tabs v-model="bottomTabs">
        <el-tab-pane label="问题商品信息" name="first">
          <xpt-list
            :data='base.listHandle'
            :colData='goodsCols'
            :orderNo="true"
            :showHead='false'
            selection=''
          ></xpt-list>
        </el-tab-pane>
        <el-tab-pane label="客户" name="second">
          <xpt-list
            :data='base.listCustomer'
            :colData='customerCols'
            :showHead='false'
            :orderNo="true"
            selection=''
          ></xpt-list>
        </el-tab-pane>
        <el-tab-pane label="操作记录" name="three">
          <xpt-list
            :data='base.operateLogs'
            :colData='operaterCols'
            :showHead='false'
            :orderNo="false"
            selection=''
          ></xpt-list>
        </el-tab-pane>
        <el-tab-pane label="处理进度" name="four">
          <xpt-list
            :data='base.listHandleLog'
            :colData='handleLogCols'
            :showHead='false'
            selection=''
          ></xpt-list>
        </el-tab-pane>
        <el-tab-pane label="历史处理方案" name="five">
          <xpt-list
            :data='base.listHandleHis'
            :colData='handleHisCols'
            :showHead='false'
            selection=''
          ></xpt-list>
        </el-tab-pane>
        <el-tab-pane label="客户承担费用信息" name="six">
          <xpt-list
            :data='base.bearCostsList'
            :colData='handleBearCostCols'
            :showHead='false'
            selection=''
          ></xpt-list>
        </el-tab-pane>
        <el-tab-pane label='接口信息' name='apiDetail' class='xpt-flex'>
          <div class="xpt-flex__bottom scroll">
            <el-table :data="apiList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
              <el-table-column label="下游单据类型" prop="push_bill_type" width="150" show-overflow-tooltip></el-table-column>
              <el-table-column label="单据编号" prop="interface_respone" show-overflow-tooltip>
                <template slot-scope="scope">{{ scope.row.interface_respone_billno }}</template>
              </el-table-column>
              <el-table-column label="方式" prop="business_type" width="70" show-overflow-tooltip></el-table-column>
              <el-table-column label="接口状态" prop="interface_status" width="100" show-overflow-tooltip>
                <template slot-scope="scope">{{ interface_status_options[scope.row.interface_status] }}</template>
              </el-table-column>
              <el-table-column label="接口提示信息" prop="interface_respone" show-overflow-tooltip></el-table-column>
              <el-table-column label="生成时间" prop="create_time" width="180" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row.create_time | dataFormat1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="primary" size="mini" @click="pushOrder(scope.row.id)">重新推送</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
  // 数据声明
  import Vue from 'vue'
  import data from './data.js';
  export default {
    props: ['params'],
    mixins: [data],
    data() {
      return {
        apiList: [], // 接口列表
        serviceTypeOneTooltipContent: '',
        serviceTypeOneTooltipControl: false,
        serviceTypeOneTooltipDisabled: false,
        serviceTypeTooltipContent: '',
        serviceTypeTooltipControl: false,
        serviceTypeTooltipDisabled: false,
        childClassData: [],
        // 所有子分类
        childClassDataAll: __AUX.get('serviceType'),
        service_type_one_remark: __AUX.get('serviceTypeOne').reduce((newObj, obj) => {
          newObj[obj.code] = obj.remark
          return newObj
        }, {}),
        service_type_remark: __AUX.get('serviceType').reduce((newObj, obj) => {
          newObj[obj.code] = obj.remark
          return newObj
        }, {}),
        // 当前可以选子分类
        uploadObj: {},
        uploadCallback: () => {},
        uploadClick: false,
        topTabs: 'first',
        bottomTabs: 'first',
        saveBtnStatus: false, //保存按钮状态
        refreshBtnStatus: false, //刷新按钮状态
        submitBtnStatus: false, //提交4PL按钮状态
        cancelBtnStatus: false, //取消4PL按钮状态
        viewBtnStatus: false, //查看按钮状态,
        // 禁用的服务类型;手工新增的服务单：问题类型只能选咨询服务；服务类型只能选赔偿、索赔、投诉、咨询。
        disabledOption: [{code: 'RESALE'},{code: 'MAINTAIN'},{code: 'INSTALL'},{code: 'CARGO'},{code: 'CONSULTATION'}],//总是禁掉这五个选项
        paymentRechargePushLoading:false,//下推货款充值loading
        // 接口信息-接口状态
        interface_status_options: {
          '-1': '处理失败',
          '1': '处理成功',
          '0': '处理中',
          '2': '已取消',
        },
      }
    },
    methods: {
      // 获取接口信息数据
      getApiDetail() {
        if (this.base?.after_ticket_no) {
          this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryExtInterfaceByBillNo', {
            bill_no: this.base?.after_ticket_no,
            bill_type: 'SERVICE',
          }, res => {
            var data = res.body
            if (!data.result) {
              this.$message.error(data.msg)
              return
            }
            this.apiList = data.content || []
          })
        }
      },
      preValid(aux){
        let data = {id:this.base.id}
        let postUrl = "/afterSale-web/api/aftersale/service/authSaveSatisfactionForWeb"
        this.ajax.postStream(postUrl, data, res => {
          if(res.body.result){
            switch(aux){
              case 'SATISFIED':
                this.saveServiceSatisfy({
                  service_satisfaction: 'SATISFIED'
                });
                break;
              case 'DISSATISFIED':this.toPopUp();break;
            }
          }else{
            this.$message({
              type: 'error',
              message: res.body.msg
            });
          }
        }, err => {
            this.saveBtnStatus = false;
            this.$message({
              type: 'error',
              message: err.body.msg
            });
          })
      },
      saveServiceSatisfy(params){
        let postUrl = "/afterSale-web/api/aftersale/service/saveSatisfaction"
        let data =  {
          id: this.base.id,
          ...params,
        }
         this.ajax.postStream(postUrl, data, res => {
          if(res.data.result){
            this.refresh()
            this.$message({
              type: 'success',
              message: "操作成功"
            });
          }else{
            this.$message({
              type: 'error',
              message: res.body.msg
            });
          }
        })
      },
      toPopUp(){
        let self = this;
        let d = {dissatisfied_tag:this.base.dissatisfied_tag,dissatisfied_remark:this.base.dissatisfied_remark}
        this.$root.eventHandle.$emit('alert', {
                component:()=>import('@components/after_sales_service/popUnSatisfy'),
                style:'width:800px;height:400px',
                title: '不满意评价',
                params: {
                    initData:d,
                    callback (data) {
                        self.saveServiceSatisfy({
                          service_satisfaction: 'DISSATISFIED',
                          dissatisfied_tag: data.dissatisfied_tag,
                          dissatisfied_remark: data.dissatisfied_remark,
                        })
                    }
                }
            })
      },
      serviceTypeSelectHover (bool){
        this.serviceTypeSelectHover.bool = bool
        if(!bool){
          this.serviceTypeTooltipControl = false
          this.serviceTypeTooltipContent = this.service_type_remark[this.base.service_type]
        }
      },
      serviceTypeSelectItemHover (code){
        if(this.serviceTypeSelectHover.bool){
          this.serviceTypeTooltipContent = this.service_type_remark[code]
          this.serviceTypeTooltipControl = true
        }
      },
      bindOptionsItemHoverEvent (bool){
        this.bindOptionsItemHoverEvent.bool = bool
        if(bool){
          if(!this.bindOptionsItemHoverEvent.isBind){
            this.bindOptionsItemHoverEvent.isBind = true
            $('.service-type-one-select').mouseover(e => {
              if(/el-select-dropdown__item/.test(e.target.className) && this.bindOptionsItemHoverEvent.bool){
                this.serviceTypeOneTooltipContent = this.service_type_one_remark[e.target.getAttribute('data-code')]
                this.serviceTypeOneTooltipControl = true
              }
            })
          }
        }else {
          this.serviceTypeOneTooltipControl = false
          this.serviceTypeOneTooltipContent = this.service_type_one_remark[this.base.service_type_one]
        }
      },
      selectParent(value) {
        let childClassData = []
        this.childClassDataAll.find(row => {
          if(row.parentCode === value && row.status) {
            childClassData.push(row)
          }
        })
        this.childClassData = childClassData
        this.base.service_type = ''
        this.serviceTypeTooltipContent = ''
      },
      // 经销商时去掉接口permissionCode权限控制
      _delPermissionCodeWhenDealerUser (api){
        return /*this.personBusinessAttribute.attributeValue ? api.replace(/\?permissionCode=.+/, '') : */api
      },
      // 保存
      save(resolve) {
        if(this.isAdd){
          if(!this.base.service_type_one){
            this.$message.error('服务类型不能为空')
            return
          }else if(!this.base.service_type){
            this.$message.error('服务分类不能为空')
            return
          }/*else if(!this.base.commitment_time){
            this.$message.error('承诺上门时间不能为空')
            return
          }*/
        }

        let data = {
            id: this.base.id,
            compensate_amount: this.base.compensate_amount,
            service_type: this.base.service_type,
            service_type_one: this.base.service_type_one,
            door_service_time: +new Date(this.base.door_service_time),
            commitment_time: +new Date(this.base.commitment_time),
            service_satisfaction: this.base.service_satisfaction,
            service_explain: this.base.service_explain,
            confirm_status: this.base.confirm_status,
            listHandle: [],
          },
          postUrl = '/afterSale-web/api/aftersale/service/update?permissionCode=SERVICE_ORDER_SAVE';

        let i = this.base.listHandle.length;
        while(i--) {
          data.listHandle.push({
            id: this.base.listHandle[i].id,
            question_description: this.base.listHandle[i].question_description
          })
        }
        if(!data.id) {
          delete data.id;
          delete data.listHandle;
          data.batch_id = this.base.batch_id;
          data.batch_no = this.base.batch_no;
          if(!data.batch_id) {
            this.$message.error('请选择批次订单');
            return;
          }
          // 新增数据时的接口
          postUrl = '/afterSale-web/api/aftersale/service/save?permissionCode=SERVICE_ORDER_SAVE';
        }
        new Promise(resolve => {
          this.$refs.detail.validate(resolve)
        }).then(() => {
          this.saveBtnStatus = true;
          this.ajax.postStream(this._delPermissionCodeWhenDealerUser(postUrl), data, res => {
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
            this.params.__data = JSON.parse(JSON.stringify(this.base));
            this.saveBtnStatus = false;
            resolve && resolve();
            // 新增成功后，重新获取数据，改变窗口名字
            if(postUrl === '/afterSale-web/api/aftersale/service/save?permissionCode=SERVICE_ORDER_SAVE' && res.body.result && res.body.content) {
              this.$root.eventHandle.$emit('updateTab', {
                 name: this.params.tabName,
                 title:'服务单详情'
              })
              this.base.id = res.body.content;
              this.refresh();
            }
            if(res.body.result) {
              this.$root.eventHandle.$emit('refresh_afs_service_list');
            }
          }, err => {
            this.saveBtnStatus = false;
            resolve && resolve();
            this.$message.error(err);
          })
        })
      },
      /**
      *设置已拉货标识字段的显示
      *服务类型 == '上门拉货'时，显示拉货标识，其余的都隐藏
      ***/
      showOrNotPulledSign(){
        console.log('服务单调试数据');
        var type = this.base.service_type;
        var bool = type == 'CARGO'?true:false;
        //直接定位到数据
        var data = this.baseCols[1];
        var i = data.length;
        for(var a = 0; a < i;a++){
          var d = data[a];
          if(d.key == 'pulled_sign'){
            d.isShow = !bool;
            break;
          }
        }
      },
      // 刷新
      refresh() {
        if(!this.base.id) return;
        this.refreshBtnStatus = true;
        this.ajax.postStream(
          this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/service/get?permissionCode=SERVICE_ORDER_QUERY'),
          {id: this.base.id},
          res => {
            this.refreshBtnStatus = false;
            let data = res.body.content;
            if(res.body.result && data) {
              this.base = res.body.content;
              /*if(!this.base.pulled_sign) {
                this.base.pulled_sign = 'A';
              }*/
              this.showOrNotPulledSign();

              if(this.base.que_type === 'SD') {
                /*this.disabledOption = ['MAINTAIN', 'INSTALL', 'CARGO', 'FEEDBACK', 'INFO_CHANGE', 'ADDRESS_CHANGE', 'DELIVERY'];*/
                this.disabledOption = [{tag:''},{tag:'S'},{tag:'s'}];
              } else {
                this.disabledOption = [{code: 'RESALE'},{code: 'MAINTAIN'},{code: 'INSTALL'},{code: 'CARGO'},{code: 'CONSULTATION'}];//总是禁掉这五个选项
              }
              this.params.__data = JSON.parse(JSON.stringify(this.base));
              this.getApiDetail() // 接口tab查询
            } else {
              this.$message.error(res.body.msg);
            }
            //问题反馈单号获取
            Vue.prototype.tabNo[Vue.prototype.activeTab] = data.after_ticket_no || '';
            let pageParams = {
                id: data.id
            }
            Vue.prototype.tabId[Vue.prototype.activeTab] = JSON.stringify(pageParams);
            console.log("Vue",Vue.prototype.tabNo);
          },
          err => {
            this.$message.error(err);
            this.refreshBtnStatus = false;
          }
        )
      },
      /*
      服务满意度变更
      只要满意度有值，则确认状态为已确认，更新取点，在服务单保存时。
      */
      satisfactionChange(val) {
        if(val) {
          this.base.confirm_status = 'CONFIRM';
        }
      },
      // 提交执鼎
      submitZD() {
        this.submitBtnStatus = true;
        this.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/service/send?permissionCode=SERVICE_ORDER_SUBMIT_TO_ZD'), [this.base.id], res => {
          this.$message({
            type: res.body.result ? 'success' : 'error',
            message: res.body.msg
          })
          this.submitBtnStatus = false;
          this.refresh();
        }, err => {
          this.submitBtnStatus = false;
          this.$message.error(err);
        })
      },
      // 取消执鼎
      cancelZD() {
        this.cancelBtnStatus = true;
        this.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/service/recall?permissionCode=SERVICE_ORDER_WITHDRAW_TO_ZD'), [this.base.id], res => {
          this.$message({
            type: res.body.result ? 'success' : 'error',
            message: res.body.msg
          })
          this.cancelBtnStatus = false;
          this.refresh();
        }, err => {
          this.cancelBtnStatus = false;
          this.$message.error(err);
        })
      },
      // 查看附件
      viewAccessory() {
        let self = this,
          params = {
            parent_no : this.base.after_order_no || this.base.after_ticket_no,
            //child_no : this.base.after_ticket_no,
            child_no:null,
            ext_data : null,
            parent_name :'AFTER_ORDER',
            child_name : 'SERVICE',
            parent_name_txt :'售后单',
            nickname: this.base.buyer_name,
            mergeTradeId: this.base.merge_trade_id,
            //child_name_txt : '服务单',
          }
        params.callback = (d) => {
          console.log(d)
        }
        this.$root.eventHandle.$emit('alert',{
          params: params,
          component: () => import('@components/after_sales/afterSale_aboutZD_download.vue'),
          style: 'width:1000px;height:600px',
          title: '附件列表'
        })
      },
      // 上传附件
      uploadAccessory() {
        if(!this.base.after_ticket_no){
          this.$message.error('请先创建服务单');
          return;
        }
        this.uploadClick = true;
        this.uploadObj = {

          parent_no: this.base.after_order_no || this.base.after_ticket_no,
          child_no:this.base.after_ticket_no,
          parent_name:'AFTER_ORDER',
          child_name: 'SERVICE',

          parent_name_txt: '售后单',
          child_name_txt:'服务单',
          content: this.base
        };
        setTimeout(() => {
          this.uploadClick = false;
        }, 100)
      },
      // 关闭
      close() {
        let isUpdate = this.compareData(this.params.__data, this.base),
          self = this;
        if(isUpdate){
              this.$root.eventHandle.$emit('openDialog',{
              ok(){
                new Promise(resolve => {
                  self.save(resolve);
                }).then(() => {
                  self.$root.eventHandle.$emit('removeTab',self.params.tabName);
                })
              },
              no(){
                self.$root.eventHandle.$emit('removeTab',self.params.tabName);
              }
            })
        }else{
          this.$root.eventHandle.$emit('removeTab',this.params.tabName);
        }
      },
      /**
      *根据批次订单ID去获取相关业务员信息
      ***/
      getSalemanInfo(batch_trade_id){
          if(!arguments.length) return;
          this.ajax.postStream('/order-web/api/batchtrade/getEarlyScmSysTrade',{batch_trade_id:batch_trade_id},(res)=>{
             if(!res.body.result){
                this.$message.error(res.body.msg);

             }
             let data = res.body.content || {};
             this.base.saleman = data.user_id;
             this.base.saleman_name = data.user_name;
             this.base.saleman_group = data.group_id;
             this.base.saleman_group_name = data.group_name;

          })
      },
      // 选单
      selectOrder() {
        let self = this;
        this.$root.eventHandle.$emit('alert', {
          params: {
            callback(d) {
              console.log(JSON.parse(JSON.stringify(d)));
              self.base.merge_trade_id = d.merge_trade_id; //合并订单id
              self.base.merge_trade_no = d.merge_trade_no; //合并订单号
              self.base.batch_id = d.batch_trade_id;
              self.base.batch_no = d.batch_trade_no; //批次订单号
              self.base.buyer_name = d.master_cust_name; //买家昵称（冗余字段）
              // self.base.saleman = d.; //业务员ID
              //self.base.creator_name = d.creator_name; //业务员
              // self.base.saleman_group = d.; //业务员分组
              self.base.delivery_time = d.commit_time; //发货时间
              self.base.logistics_supplier = d.zd_delivery_logistics_company; //物流供应商
              self.base.three_supplier = d.three_guarantees_supplier; //三包供应商
              self.base.logistics_no = d.zd_logistics_number; //物流单号
              self.base.delivery_id = d.logistics_point; //提货点ID
              self.base.delivery_name = d.logistics_point_name; //提货点
              // self.base.delivery_phone = d.; //提货点电话
              // self.base.three_phone = d.; //三包公司电话
              self.base.receiver_name = d.receiver_name;  //  收货人
              self.base.receiver_phone = d.receiver_mobile;  //  收货人手机
              self.base.receiver_addr = d.receiver_address_name;  //  收地址
              // self.base.province = d.receiverInfoVO.receiver_state_name;  //  省
              // self.base.city = d.receiverInfoVO.receiver_city_name;  //  市
              // self.base.area = d.receiverInfoVO.receiver_district_name;  //  区
              // self.base.street = d.receiverInfoVO.receiver_street_name;  //  收货人
              self.getSalemanInfo(d.batch_trade_id);
            }
          },
          title: '批次订单列表',
          style:'width:900px;height:560px',
          component: () => import('@components/after_sales_service/select_order')
        })
      },
      addNew() {
        this.$root.eventHandle.$emit('creatTab', {
          name: '新增服务单',
          params: {},
          component: () => import('@components/after_sales_service/detail.vue')
        })
      },
      pushConsultation(){
        if(!this.base.batch_no){
          this.$message.error('无源单不可以专员介入');
          return false;
        }

        this.$root.eventHandle.$emit('creatTab',{
          name: '咨询单详情',
          component: ()=>import('@components/after_sales/consultation'),
          params: {
            isConsultation:true,
            isFromAfterSales: {
              after_order_id: this.base.id,
              source_bill_id: this.base.id,
              after_order_no: this.base.after_ticket_no,
              merge_trade_id: this.base.merge_trade_id,
              merge_trade_no: this.base.merge_trade_no,
              question_goods_ids: [],
              batch_trade_id: this.base.batch_id,
              batch_trade_no:this.base.batch_no,
              buyer_name:this.base.buyer_name,
            },
          },
        })

      },
      //下推货款充值
      paymentRechargePush(){
        this.paymentRechargePushLoading=true;
        let params={
          after_ticket_no:this.base.after_ticket_no
        }
        this.ajax.postStream("/afterSale-web/api/aftersale/service/paymentRechargePush?permissionCode=PAYMENT_RECHARGE_PUSH",params,(res)=>{
          if(res.body.result && res.body.content){
            this.$root.eventHandle.$emit('creatTab', {
              name: '新增货款充值',
              params: {
                isFromPaymentRechargePush:true,
                after_ticket_no:this.base.after_ticket_no,
                paymentRechargeObj:{
                  ...res.body.content
                }||{}
              },
              component: () => import('@components/dealer/recharge_of_goods_detail.vue')
            })
          }else{
            this.$message.warning(res.body.msg)
          }
          this.paymentRechargePushLoading=false;
        },err=>{
          this.$message.error(err)
          this.paymentRechargePushLoading=false;
        })
      }
    },
    mounted() {
      this.params.__close = this.close;
      this.params.__data = JSON.parse(JSON.stringify(this.base));
      if(!this.params.id) {
        // 新增服务单时，设置禁用的服务类型
        //this.disabledOption = ['MAINTAIN', 'INSTALL', 'CARGO', 'FEEDBACK', 'INFO_CHANGE', 'ADDRESS_CHANGE', 'DELIVERY'];
        this.disabledOption = [{tag:''},{tag:'S'},{tag:'s'}];
      } else {
        this.base.id = this.params.id;
      }
      this.refresh();
    },
    computed: {
      submitStatus() {
        let statusSet = new Set(['WAITING', 'CANCEL', 'FAILURE']);
        return !(statusSet.has(this.base.api_status));
      },
      // 接口状态为已下达、失败时，且指令工单完成时间为空时才可以操作
      cancelStatus() {
        let statusSet = new Set(['ARRIVE', 'FAILURE'])
        if (statusSet.has(this.base.api_status) && !this.base.finish_time) {
          return false;
        } else {
          return true;
        }
      },
      isAdd() {
        return this.base.id ? false : true;
      },
      /*
      全部可编辑
      单据状态为创建、业务状态为业务办理可以编辑
      */
      canEdit() {
        return (
          this.isAdd
          ? true
          : this.base.status == 'CREATE' && this.base.business_status == 'WAITING_HANDLE'
        );
      },
      /*
      可编辑满意度
      单据状态为已审核、业务状态为WMS办理或处理完结可以编辑
      指令工单完成时间没有值时不能编辑
      */
      canEditPart() {
        return this.base.status == 'AUDITED' &&
          (this.base.business_status == 'WMS_HANDLE' || this.base.business_status == 'FINISH')
      },
      canIPaymentRechargePush(){
        let {expense_object_name,income_object_name,expense,income} = this.base.aftersaleBillServiceHandleVO
        if(!!expense_object_name && !!income_object_name && !!expense && !!income && (expense_object_name === income_object_name && expense === income)) return false
        return this.base.status == 'AUDITED' && this.base.business_status == 'FINISH' && this.base.funds_manage_status == 'PAY_NOT'
      }
    }
  }
</script>
<style type="text/css" scoped>
  .el-textarea{
    width: 180px;
  }
</style>
