<!--服务单据列表-->
<template>
  <xpt-list-dynamic
    :data='list'
    :btns='btns'
    :colData='col'
    :pageTotal='pageTotal'
		:showCount ='showCount'
    selection='checkbox'
    :searchPage='search.page_name'
		@count-off="countOff"
    @selection-change='select'
    @search-click='searchClick'
    @page-size-change='pageChange'
    @current-page-change='currentPageChange'
    ref="xptList"
  >
  </xpt-list-dynamic>
</template>

<script>
  import data from './data.js';
  export default {
    mixins: [data],
    data(){
      let self = this;
      let _this = this;
      return {
        countOffFlag:false,
			  showCount:false,
        pageTotal:0,
        list:[],
        // 已选行
        selectRow:[],
        col:[
          {
            label: '销售标签',
            prop: 'sales_label',
            width: 70,
            html(val) {
                return val ? '<span style="color:red;">'+val+'</span>' : val;
            },
          },
          {
            label: '单据编号',
            prop: 'after_ticket_no',
            width:130,
            redirectClick(d) {
              self.mod(d)
            }
          },{
            label: '单据状态',
            prop: 'status',
            // format: 'auxFormat',
            // formatParams: 'afsStatus'
          },{
            label: '业务状态',
            prop: 'business_status',
            // format: 'auxFormat',
            // formatParams: 'afsNativeBusinessStatus'
          },{
            label: '是否经销商订单',
            width:'100',
            prop: 'if_dealer',
            // formatter: val => {
            //   switch(val){
            //     case 'Y':return'是';break;
            //     case 'N':return'否';break;
            //     default: return val; break;
            //   }
            // },
          },{
            label: '经销商名称',
            prop: 'dealer_customer_name',
            width:100
          },{
					label: '店铺地区',
					format: 'auxFormat',
					prop: 'shop_area',
					formatParams: 'shopArea'
				},{
            label: '进度完结状态',
            width: 100,
            prop: 'schedule_finish_status',
            // format: 'auxFormat',
            // formatParams: 'afsFinishStatus'
          },{
            label: '买家昵称',
            prop: 'buyer_name',
            width:130
          },{
            label: '处理类型',
            prop: 'handle_type',
            format: 'auxFormat',
            formatParams: 'afsHandleType'
          },{
            label: '合并单号',
            prop: 'merge_trade_no',
            width:200
          },{
            label: '来源单号',
            prop: 'source_bill_no',
            width:200,
            redirectClick(row) {
              self.toSourceBillNoDetail(row)
            },
          },{
            label: '批次订单',
            width:200,
            prop: 'batch_no'
          },{
            label: '服务类型',
            prop: 'service_type_one',
            format: 'auxFormat',
            formatParams: 'serviceTypeOne'
          },
          {
            label: '服务分类',
            prop: 'service_type',
            format: 'auxFormat',
            formatParams: 'serviceType'
          },{
            label: '业务员',
            prop: 'saleman_name',
          },{
            label: '业务员分组',
            prop: 'saleman_group_name'
          },{
            label: '确认状态',
            prop: 'confirm_status',
            // format: 'auxFormat',
            // formatParams: 'afsConfirmStatus'
          },{
            label: '已拉货标识',
            prop: 'pulled_sign',
            format: 'auxFormat',
            formatParams: 'afsPulledsign'
          },{
            label: '物流供应商',
            width:150,
            prop: 'logistics_supplier'
          },{
            label: '三包供应商',
            width:150,
            prop: 'three_supplier'
          },{
            label: '扣款类型',
            prop: 'deduct_type',
            format: 'auxFormat',
            formatParams: 'afsDeductionsType'
          },{
            label: '支出类型',
            prop: 'expense_type',
            // format: 'expenseType'
          },{
            label: '支出对象',
            prop: 'expense_object'
          },{
            label: '支出金额',
            prop: 'expense_amount'
          },{
            label: '收入类型',
            prop: 'income_type',
            // format: 'incomeType'
          },{
            label: '收入对象',
            prop: 'income_object'
          },{
            label: '收入金额',
            prop: 'income_amount'
          },{
            label: '维修供应商',
            prop: 'repair_provider'
          },{
            label: '维修金额',
            prop: 'repair_amount'
          },{
            label: '提交人',
            prop: 'submit_request_name'
          },{
            label: '提交时间',
            format: 'dataFormat1',
            width: 140,
            prop: 'submit_request_time'
          },{
            label: '运输单号',
            prop: 'transport_no'
          },{
            label: '签约状态',
            prop: 'if_need_service_operation',
            // formatParams(val){
            //   //  return val == 'N'?'否':'是';
            //   switch(val){
            //     case 'Y':return'是';break;
            //     case 'N':return'否';break;
            //     default: return val; break;
            //   }
            // }
          },{
            label: '完结人',
            prop: 'end_name'
          },{
            label: '完结时间',
            format: 'dataFormat1',
            width: 140,
            prop: 'end_time'
          },{
            label: '处理进度完结时间',
            format: 'dataFormat1',
            width: 140,
            prop: 'progress_finish_time'
          },
          {
            prop: "auto_returns_status",
            label: "自动推退换货单状态",
            width: 160,
            formatter: prop => ({
              WAIT: '等待',
              RUNNING: '处理中',
              FAILED: '失败',
              SUCCESS: '成功',
              NO_NEED: '无需'
            }[prop] || prop),
        },
          {
            label: '自动推退换货单结果',
            width: 160,
            prop: 'auto_returns_result',
            redirectClick(row) {
              if(row.bill_returns_id) {
                self.$root.eventHandle.$emit('creatTab', {
                  name: "退换货跟踪单详情",
                  params: {id: row.bill_returns_id},
                  component: () => import('@components/after_invoices/returnexchangedetail_2')
                });
              } else {
                self.$message.info('无退货跟踪单');
              }
					}
          },
          {
            label: '单据来源',
            width: 80,
            prop: 'source',
            format: 'auxFormat',
            formatParams: 'AFTERSALE_SOURCE_TYPE'
          }
        ],
        btns:[
          {
            type:'danger',
            txt:'删除',
            loading:false,
            // disabled: true,
            click: self.del,
          }, {
            type:'primary',
            txt:'刷新',
            loading:false,
            click: () => self.searchFun(),
          }, {
            type: 'info',
            txt: '新增',
            // disabled: !!self.personBusinessAttribute.attributeValue,
            click() {
              self.mod()
            }
          }
        ],
        search: {
          page_no: 1,
          page_size: self.pageSize,
          page_name: 'aftersale_bill_service',
          where: []
        }
      }
    },
    methods:{
      searchClick(where, resolve) {
        this.search.where = where;
        // this.searchFun(resolve);
        new Promise((res,rej)=>{
				  this.searchFun(resolve,res);
        }).then(()=>{
          if(this.search.page_no != 1){
            this.pageTotal = 0;
          }
            this.showCount = false;
        })
      },
      countOff(){

        let self = this,
        url = "/afterSale-web/api/aftersale/service/listCount";
        if(!self.list.length){
          self.$message.error("当前列表为空，先搜索内容");
          return;
        }
        if(!!self.countOffFlag){
          self.$message.error("请勿重复点击");
          return;
        }
        self.countOffFlag = true;

        self.ajax.postStream(url,{...self.search,jFType:"NOT_CLAMIS"},function(response){
            if(response.body.result){

              self.pageTotal = response.body.content;
              self.showCount = true;
              self.countOffFlag = false;

            }else{
              self.$message.error(response.body.msg);
            }
          });
      },
      searchFun(resolve,resolve2){
        let self = this;
        this.btns[1].loading = true;
        let url = '/afterSale-web/api/aftersale/service/list'+(this.__judgeIsDealerMenu(this.params.menuInfo)?'':'?permissionCode=SERVICE_ORDER_QUERY');
        this.ajax.postStream(url/*'/afterSale-web/api/aftersale/service/list?permissionCode=SERVICE_ORDER_QUERY'*/, {
          ...this.search,
          jFType:'NOT_CLAMIS'
        }, res => {
          if(res.body.result) {
            // this.list = res.body.content.list || [];
            // this.pageTotal = res.body.content.count || 0;
            let dataList = JSON.parse(JSON.stringify(res.body.content.list));
            if(dataList.length == (self.search.page_size+1)&&dataList.length>0){
              dataList.pop();
            }
            self.list = dataList;
            let totalCount = self.search.page_no*self.search.page_size;
            if(!self.showCount){
              self.pageTotal = res.body.content.list.length == (self.search.page_size+1)? totalCount+1:totalCount;
            }
          }else {
            this.$message.error(res.body.msg);
          }
          this.btns[1].loading = false;
          resolve && resolve();
          resolve2 && resolve2();
        }, err => {
          this.$message.error(err);
          this.btns[1].loading = false;
          resolve && resolve();
          resolve2 && resolve2();
        }, this.params.tabName)
      },
      pageChange(pageSize) {
        this.search.page_size = pageSize
        this.searchFun();
      },
      currentPageChange(page){
        this.search.page_no = page
        this.searchFun()
      },
      select(s){
        this.selectRow = s;
      },
      //删除
      del(){
        if(!this.selectRow.length) {
          this.$message.error('请选择要删除的数据');
          return;
        }
        let idArr = [],
          i = this.selectRow.length;
        while(i--) {
          idArr.push(this.selectRow[i].id)
        }
        this.btns[0].loading = true;
        let url = '/afterSale-web/api/aftersale/service/del'+(this.__judgeIsDealerMenu(this.params.menuInfo)?'':'?permissionCode=SERVICE_ORDER_DELETE');
        this.ajax.postStream(url/*'/afterSale-web/api/aftersale/service/del?permissionCode=SERVICE_ORDER_DELETE'*/, idArr, res => {
          this.$message({
            type: res.body.result ? 'success' : 'error',
            message: res.body.msg
          })
          this.btns[0].loading = false;
          // 删除已经删除的数据
          let idArrSet = new Set(idArr);
          if(res.body.result) {
            i = this.list.length;
            while(i--) {
              if(idArrSet.has(this.list[i].id)) {
                this.list.splice(i, 1);
              }
            }
          }
        }, err => {
          this.btns[0].loading = false;
          this.$message.error(err)
        })
      },
      mod(obj) {
        this.$root.eventHandle.$emit('creatTab', {
          name: obj ? '服务单详情' : '新增服务单',
          params: obj,
          component: () => import('@components/after_sales_service/detail.vue')
        })
      },
      openReturnBill(id) {
        this.$root.eventHandle.$emit('creatTab', {
          name: "退换货跟踪单详情",
          params: {id},
          component: () => import('@components/after_invoices/returnexchangedetail_2')
        });
      }
    },
    props:['params'],
    mounted(){
      // if(this.personBusinessAttribute.attributeValue){
      //   // 经销商时，去掉通用查询组件服务类型-退货再售选项
      //   setTimeout(() => {
      //     this.$refs.xptList.$refs.xptSearchEx.params.fields.some(obj => {
      //       if(obj.comment === '服务类型'){
      //         obj.values.some((o, i) => {
      //           if(o.code === 'RESALE'){
      //             obj.values.splice(i, 1)
      //             return true
      //           }
      //         })
      //         return true
      //       }
      //     })
      //   }, 1000)
      // }
      // this.searchFun();
      this.$root.eventHandle.$on('refresh_afs_service_list', () => {
        this.searchFun();
      })
    },
    destroyed() {
      this.$root.offEvents('refresh_afs_service_list');
    },
  }
</script>
