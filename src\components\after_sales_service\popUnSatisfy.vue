<!-- 添加业务员类型 -->
<template>
	<div class='xpt-flex'>
		<xpt-headbar>
			<el-button size='mini' type='primary' @click='submit' slot='left'>确认</el-button>
			<el-button size='mini' type='danger' @click='close' slot='right'>关闭</el-button>
		</xpt-headbar>
		<el-form label-width='100px' :model='obj' :rules='rules' ref='obj'>
			<el-row :gutter='10'>
				<el-col :span='10'>
					<el-form-item label='不满意标签' prop='dissatisfied_tag'>
					  	<xpt-select-aux v-model='obj.dissatisfied_tag' aux_name='FWDBQ'  @change='tagChange' showcode></xpt-select-aux>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter='10'>
				<el-col	:span='20'>
					<el-form-item label='不满意备注' prop='dissatisfied_remark'>
						<el-input type='textarea' v-model='obj.dissatisfied_remark' size='mini' :maxlength='200' ></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
export default {
	props: ['params'],
	data () {
		let self = this
		return {
			obj: {
				dissatisfied_tag:'',
				dissatisfied_remark:''
			},
	        rules: {
	        	dissatisfied_tag: [{ required: true, message: '请选择不满意标签', trigger: 'blur' }],
				dissatisfied_remark: [{ required: true, message: '请填写不满意备注', trigger: 'blur' }]
	        },
	        isInit: false,
		}
	},
	methods : {
		// 确认对象信息
		submit () {
			this.$refs['obj'].validate((valid) => {
				if(valid){
					console.log(this.obj)
					if(this.obj.disableTime){
						if(typeof this.obj.disableTime == 'number'){
								this.obj.disableTime = new Date(this.obj.disableTime);
                            }
                            // +60*60*24*1000 -1000
                        this.obj.disableTime = this.dataFormater(this.obj.disableTime).getTime()
					}
					// console.log(this.obj.disableTime)
					this.params.callback(this.obj)
					this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
				}
			})
        },
        dataFormater(times) {
            let date = new Date(times)
            let year = date.getFullYear();
            let month = date.getMonth()+1;
            let day = date.getDate();
            let time = year + '-' + month + '-' + day + ' ' + '00:00:00';
            return new Date(time);
        },
		close () {
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		tagChange (val) {
			if(val=='OTHER'){
				this.rules.dissatisfied_remark[0].required=true;
			}else{
				this.rules.dissatisfied_remark[0].required=false;
			}
		}
	},
	mounted () {
		if (this.params.initData) {
			console.log('this.params.initData',this.params.initData);
			this.isInit = true
			for (var item in this.obj) {
				this.obj[item] = this.params.initData[item]
			}
			setTimeout(() => {
				this.isInit = false
			},1000)
		}
	}
}
</script>
