<!--BOM产品-->
<template>
  <div class='xpt-flex'>
    <el-row class='xpt-top' :gutter='40'>
      <el-col :span='3'>
        <el-button type='info' size='mini' @click="confirmAddGoods">确认</el-button>
      </el-col>

      <el-col :span='21' class='xpt-align__right' >
				<span v-if="!params.notNeedParams || !params.notNeedParams.all">
					全部物料:<el-select placeholder="请选择" size="mini" style="width:80px;" v-model="search.all" :disabled="isDisabledAll" @change="changeGoods">
						<el-option
              v-for="(value,key) in boolObj1"
              :label="value"
              :value="key"
              :key='key'>
						</el-option>
		    		</el-select>
				</span>
       <!-- <span v-if="!params.notNeedParams || !params.notNeedParams.universal">
					是否通用:<el-select placeholder="请选择" size="mini" style="width:80px;" v-model="search.universal" :disabled="isDisabledUniversal">
					<el-option
            v-for="(value,key) in boolObj"
            :label="value"
            :value="key"
            :key='key'>
					</el-option>
					</el-select>
				</span>-->
        <el-select placeholder="请选择" size="mini"  v-model="search.condition" style="width:100px;" @change="conditionChange">
          <el-option label="物料编码" value="MATERIAL_NUMBER"></el-option>
          <el-option label="物料名称" value="MATERIAL_NAME"></el-option>
         <!-- <el-option label="BOM分组" value="BOM_GROUP" v-if="search.all==0"></el-option>
-->
          <el-option label="是否通用" value="IF_ADDITIONAL"></el-option>
        </el-select>
        <el-select placeholder="请选择" size="mini"  style="width:100px;" v-model="search.symbol">
          <el-option
            v-for="(value,key) in condition"
            :label="value"
            :value="key"
            :key='key'>
          </el-option>
        </el-select>

        <el-select placeholder="请选择" size="mini"  v-model="search.string" style="width:80px;" v-if="search.condition=='IF_ADDITIONAL'">
          <el-option
            v-for="(value,key) in yesOrNo"
            :label="value"
            :value="key"
            :key='key'>
          </el-option>
        </el-select>

        <el-input placeholder="请输入查询条件" size='mini' v-model="search.string" v-else></el-input>



        <!--<el-input placeholder="请输入物料编码" size='mini' v-model="search.string"></el-input>
        -->
        <el-button type='primary' size='mini' @click="searchList">搜索</el-button>
      </el-col>
    </el-row>

    <xpt-list
      ref='list'
      selection='radio'
      :showHead = 'false'
      :data='list'
      :colData='colData'
      :pageTotal='count'
      @row-dblclick="rowDblclick"
      searchHolder='请输入搜索条件'
      @radio-change='select'
      @page-size-change='sizeChange'
      @current-page-change='pageChange'>
    </xpt-list>
  </div>


</template>
<script>
  export default {//需要类型和相关的id
    props:['params'],
    data(){
      var self = this;
      return {

        search:{
          all:"1",//全部物料
          condition:'MATERIAL_NUMBER',
          symbol:'CONTAIN',
          string:'',//描述
          bomVersion:null,//bom版本信息，用于无源单
          page:{
            page_size:50,
            page_no:1
          }
        },
        yesOrNo:{
          'Y':'是',
          'N':'否',
          'ALL':'全部'
        },
        condition:{
          "CONTAIN":'包含',
          "EQAUL":'等于'
        },
        newSearch:null,//
        isDisabledAll : false,
        isDisabledUniversal : false,
        isDisabledBomGroup : false,
        //根据页面的需求传过来的参数
        searchAllGoods:{},//全部物料接口传过来的参
        searchpartGoods:{},//拆分物料接口传过来的参
        getAllBomVersionParam:{},//获取bom版本的接口参数
        isNeedBomVerison:false,//是否是需要bom版本进行查询

        count:0,
        selectedData:'',
        boolObj:{
          1:'是',
          0:'否'
        },
        boolObj1:{
          1:'是',
        },
        //bom分组
        bomGroup:{
          BJ:'部件',
          CP:'成品'
        },
        //bom版本信息
        bomVersion:{},

        list:[],//店铺列表
        colData:[
          {
            label: '是否通用',
            prop: 'isAdditional',
            format:'yesOrNo'
          }, {
            label: '物料编码',
            prop: 'materialNumber'
          }, {
            label: '物料名称',
            prop: 'materialName'
          }, {
            label: '规格描述',
            prop: 'materialSpecification'
          }, {
            label: '单位',
            prop: 'materialUnit'
          }]
      }
    },
    methods:{
      /**
       *双击事件
       **/
      rowDblclick(data){
        this.selectedData=data;
        this.confirmAddGoods();

      },
      /**
       *成品物料显示值
       */
      getShowGoodsParams(){
        return [
          {
            label: '是否通用',
            prop: 'isAdditional',
            format:'yesOrNo'
          }, {
            label: '物料编码',
            prop: 'materialNumber'
          }, {
            label: '物料名称',
            prop: 'materialName'
          }, {
            label: '规格描述',
            prop: 'materialSpecification'
          }, {
            label: '单位',
            prop: 'materialUnit'
          }/*, {
            label: 'BOM编码',
            prop: 'bom_material_num'
          }, {
            label: 'BOM分组',
            prop: ''
          }, {
            label: 'BOM名称',
            prop: 'bom_material_name'
          }, {
            label: '销售单位',
            prop: ''
          }*/]
      },
      /**
       *bom物料显示值
       */
      getShowBomParams(){
        return [
          {
            label: '是否通用',
            prop: 'isAdditional',
            format:'yesOrNo'
          }, {
            label: '物料编码',
            prop: 'bom_material_num'
          }, {
            label: '物料名称',
            prop: 'bom_material_name'
          }, {
            label: '规格描述',
            prop: 'bom_material_desc'
          }, {
            label: '单位',
            prop: 'materialUnit'
          },{
            label: 'BOM编码',
            prop: 'bom_version'
          }, {
            label: 'BOM分组',
            prop: ''
          }, {
            label: 'BOM名称',
            prop: ''
          }, {
            label: '销售单位',
            prop: ''
          }]
      },
      /**
       *物料选择改变
       */
      changeGoods(){
        var bool = false;
        if(this.search.all == '1' && !this.isEmptyObject(this.bomVersion)){
          bool = true;
        }
        this.isNeedBomVerison = bool;
      },
      /**
       *关闭当前弹出框组件
       */
      closeCurrentComponent(){
        this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
      },
      conditionChange(){
        var d = this.search.condition;
        this.condition = '';
        if(d == 'BOM_GROUP' || d == 'IF_ADDITIONAL'){
          this.condition = {"EQAUL":'等于'};
          this.search.symbol = "EQAUL";

        }else{
          this.condition = {"CONTAIN":'包含',"EQAUL":'等于'};
        }
        //this.condition = d == 'BOM_GROUP' || d == 'IF_ADDITIONAL'?{"EQAUL":'等于'}:{"CONTAIN":'包含',"EQAUL":'等于'};

        this.tongyongShow = d == 'IF_ADDITIONAL'?true:false;
        //d == 'BOM_GROUP' || d == 'IF_ADDITIONAL'?this.search.string = null:'';
        if(this.search.string == 'Y' || this.search.string == 'N' || this.search.string == 'ALL'){
          this.search.string = null;
        }
      },
      /**
       *确认回调函数
       **/
      confirmAddGoods(){
        var selectedData = this.selectedData;
        if(!selectedData){
          this.$message.error('请选择数据');
          return;
        }

        this.params.callback && this.params.callback({data:selectedData});
        this.closeCurrentComponent();
      },
      getMatchKey(){//1为成品，2为bom
        var colData = this.colData;
        var col1 = this.getShowBomParams();
        var col2 = this.getShowGoodsParams();
        //key--为成品的KEY值，value为拆分物料
        /*var params = {
         materialNumber : 'bom_material_num',
         materialName : 'bom_material_name',
         materialSpecification : 'bom_material_desc'

         }*/
        var type = 1;//1是成品,2是bom
        if(JSON.stringify(colData) == JSON.stringify(col1)){
          type = 2;
        }
        return type;
      },
      sizeChange(size){
        // 每页加载数据
        this.search.page.page_size = size;
        this.search.page.page_no = 1;

        this.getList();
      },
      pageChange(page_no){
        // 页数改变
        this.search.page.page_no = page_no;
        this.getList();
      },

      resetSearchData(bool){//bool,是要加载首页的数据还是加载当前页的数据，为true时，加载首页，false则为当前页面
        bool?this.search.page.page_no = 1:'';
      },
      select(selections){
        this.selectedData = selections;
      },
      /**
       * 请空已选的数据
       * **/
      clearSelectedData(){
        this.selectedData = '';
        this.$refs.list && this.$refs.list.clearSelection();
      },

      searchList(){
        //模糊搜索
        this.resetSearchData(!0);
        this.getList();
      },

      /**
       *初使化数据
       **/
      initData(){
        let data = this.params || {};
        let searchData = data.search;
        let searchAllGoods = data.searchAllGoods|| {};//全部物料的接口的参数
        let searchpartGoods = data.searchpartGoods || {};//折分物料的接口的参数
        let setDisabled = data.setDisabled || {};
        let getAllBomVersionParam = data.getAllBomVersionParam || {};
        this.newSearch = data.newSearchCondition;
        if(searchData &&　!this.isEmptyObject(searchData)){
          for(var key in searchData){
            this.search[key] = searchData[key];
          }
        }

        /*if(searchAllGoods && !this.isEmptyObject(searchAllGoods)){
         for(var key in searchAllGoods){
         this.searchAllGoods[key] = searchAllGoods[key];
         }
         }
         if(searchpartGoods && !this.isEmptyObject(searchpartGoods)){
         for(var key in searchpartGoods){
         this.searchpartGoods[key] = searchpartGoods[key];
         }
         }*/

        this.searchAllGoods = Object.assign({},searchAllGoods);
        this.searchpartGoods = Object.assign({},searchpartGoods);
        this.getAllBomVersionParam = Object.assign({},getAllBomVersionParam);

        if(setDisabled && !this.isEmptyObject(setDisabled)){
          this.isDisabledAll = setDisabled.all?true:false;
          this.isDisabledUniversal = setDisabled.universal?true:false;
          this.isDisabledBomGroup = setDisabled.bomGroup?true:false;
        }
        this.isNeedBomVerison = data.notNeedParams && !data.notNeedParams.bomVersion;
        //this.colData = this.search.all == 0?this.getShowBomParams():this.getShowGoodsParams();

      },
      /**
       *拿到全部物料接品参数
       */
      getAllParams(){
        let params = Object.assign({},this.searchAllGoods || {});
        let info = this.search;
        let pa = this.params || {};

        params.isAdditional = info.universal;
        //params.disableStatus = 'A';
        //params.documentStatus = 'C';
        params.page = {
          length:info.page.page_size,
          pageNo:info.page.page_no
        }
        return params;

      },
      /**
       *拿到拆分物料接品参数
       */
      getBomMaterialParams(){
        let params = Object.assign({},this.searchpartGoods || {});
        let info = this.search;
        //let pa = this.params || {};

        params.all_string = info.string;//文本框
        params.inventory_category = info.bomGroup;//分组
        params.is_universal_patch = info.universal;//通用
        params.page_size = info.page.page_size;
        params.page_no = info.page.page_no;

        //无源单商品问题的非成品退换货
        this.isNeedBomVerison?params.bom_version = this.search.bomVersion:'';

        return params;
      },

      /**
       *获取bom列表
       */
      getList(){
        var url = '/material-web/api/material/getMaterialList';
        let params = {
          disableStatus : 'A',
          sale_enabled: '1',
          audit_flag: '1'
        }
        //let info = this.search;
        //params.isAdditional = info.universal;

        params.select_key = this.search.condition;
        params.select_type = this.search.symbol == 'EQAUL'?'=':'%';
        params.select_value = this.search.string;
        if(params.select_key == 'IF_ADDITIONAL'){
          params.select_value = params.select_value == 'Y'?1:params.select_value == 'N'?0:'ALL';
        }
        params.page = {
          length:this.search.page.page_size,
          pageNo:this.search.page.page_no
        }
        if(this.params.isSupply){
          delete params.sale_enabled;
        }

        this.ajax.postStream(url,params,d=>{
          if(!d.body.result){
            this.$message.error(d.body.msg);
            return;
          }
          let count,list;
          if(this.search.all === '1'){
            count = d.body.content.count;
            list = d.body.content.list;
          }else{
            count = d.body.content.count;
            list = d.body.content.list;
          }
          this.list = list||[]
          this.count = count
          console.log('d',d);

        });
      },
      /**
       *根据物料ID获取所有的bom版本号
       **/
      getAllVersion(callback){
        var data = this.getAllBomVersionParam;
        data.page = {
          length : 100,
          pageNo : 1
        }
        /*this.initPageSize = data.page.length;*/
        this.ajax.postStream('/material-web/api/material/getMaterialListAndBOM',data,(res)=>{
          var resData = res.body;
          if(!resData.result) return;
          var version = {};
          var list = resData.content.list || [];
          list.map((a,b)=>{
            let c = a.bomVersion;
            version[c] = c;
            this.search.bomVersion?'':this.search.bomVersion = c;

          });
          this.bomVersion = '';
          this.bomVersion = version;
          callback && callback();

        });
      }


    },
    /*watch:{
     'search.all':function(newVal,oldVal){
     console.log('this.search.all',newVal);
     this.colData = newVal == 0?this.getShowBomParams():this.getShowGoodsParams();
     }
     },*/
    created(){
      //this.colData = this.getShowBomParams();
      this.initData();
      if(this.isNeedBomVerison){
        this.getAllVersion(()=>{
          this.getList();
        })
      }else{
        this.getList();
      }

    }
  }
</script>
<style type="text/css" scoped>
  .el-table__body-wrapper{margin-bottom:20px;}
</style>
