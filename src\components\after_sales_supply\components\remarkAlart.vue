<template>
  <div class="remark-alart">
    <div class="btn-wrap">
      <el-button v-if="params.btn" :loading="loading" type="primary" size='mini' @click="handleSubmit">{{ params.btn.txt
        }}</el-button>
    </div>
    <div class="remark-alert-remark-wrap">
      <el-form label-position="left" class="remark-alert-remark-wrap-form" :inline="true" :model="ruleForm" :rules="rules" ref="remarkFormRef">
        <el-form-item prop="remark" label="备注">
          <el-input class="remark-alert-remark" :rows="10" type="textarea" v-model="ruleForm.remark"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    callback: {
      type: Function,
      default: () => {
        return () => {
          console.log('默认回调')
        }
      }
    }
  },
  data() {
    return {
      ruleForm: {
        remark: "",
      },
      rules: {
        remark: {
          required: true, message: '请输入备注', trigger: 'blur'
        }
      },
      remark: "",
      loading: false,
    }
  },
  methods: {
    async handleSubmit() {
      this.$refs.remarkFormRef.validate(async (res)=>{
        if(res) {
          try {
            const d = { alertId: this.params.alertId, remark: this.ruleForm.remark }
            this.loading = true;
            this.params.callback && await this.params.callback(d)
          } catch (error) {
            console.log(error);
          } finally {
            this.loading = false;
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.remark-alart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 14px;
  padding: 14px;

  & .btn-wrap {
    width: 100%;
  }

  & .remark-alert-remark-wrap {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: nowrap;
    gap: 14px;
    & .remark-alert-remark-wrap-form {
      width: 100%;
      height: 100%;
    }
  }
  /deep/.el-form-item {
    width: 100%;
  }
  /deep/.el-form-item__content {
    width: calc(100% - 40px);
  }
}
</style>