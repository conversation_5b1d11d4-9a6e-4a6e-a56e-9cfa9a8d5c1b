<!--补件收入调整单列表-->
<template>
	<count-list
		:data='list'
		:colData='colData'
		:pageTotal='count'
		:btns='btns'
		selection='radio'
		:searchPage='search.page_name'
		:isNeedClickEvent='true'
		:dynamic="true"
		countUrl="/afterSale-web/api/aftersale/supply/incomeAdjust/count"
      	:showCount="showCount"
		@radio-change='radioChange'
		@search-click='searchClick'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
	>

	</count-list>
</template>
<script>
import countList from "@components/common/list-count";


	export default {//需要类型和相关的id
	    props:['params'],
		 components: {
			countList,
		},
		data(){
            var self = this;
			return {
                showCount: false,
				uploadUrl:'/reports-web/api/reports/afterSaleExport/exportSupplyIncomeAdjust',
                search:{
                    page_name: 'aftersale_supply_income_adjust',
					// page:{
                    //     /*length:20,
					// 	pageNo:1*/
					// 	page_size:self.pageSize,
					// 	page_no:1,

					// },
					where:[],
					page_no: 1,
                    page_size: 50,
				},
				btns:[{
					txt:'重新推送',
					type:'info',
					click:()=>{
						self.confirmAddGoods();
					}
				},
				{
					type: "success",
					txt: "导出",
					click: this.export
				},
				{
					type: "success",
					txt: "导出文件下载",
					disable(){
						return false
					},
					click: this.showExportList
				},
				],
				colData:[{
					prop:'supply_income_adjust_no',
					width:150,
					label:'单据编号'
				},{
					prop:'analysis_sub_no',
					width:150,
					label:'责任分析子单'
				},{
					prop:'analysis_sub_create_time',
					label:'责任单创建时间',
					format: 'dataFormat1',
					width:150,
				},{
					prop:'buyer_name',
					label:'买家昵称'
				},{
					prop:'dealer_customer_name',
					width:150,
					label:'经销商名称'
				},{
					prop:'shop_name',
					width:150,
					label:'店铺名称'
				},{
					prop:'designer_name',
					label:'设计师'
				},{
					prop:'original_bill_no',
					width:150,
					label:'子单来源单号'
				},{
					prop:'original_bill_type',
					width:120,
					label:'子单来源单据类型',
				formatter: prop => ({
					SHBJSQD:"售后补件申请单",
					ZTBJSQD:"展厅补件",
					NXBJSQD:"内销补件",
					RESALE:"再售补件",
					BYWJBJ:"备用五金补件",
					DZBJ:"定制补件",
					XSJGSQ:"销售加购申请单",
          HWBJ: "海外补件",
				}[prop]),
				},{
					prop:'merge_trade_no',
					width:180,
					label:'合并单号'
				},{
					prop:'sale_out_stock_bill_no',
					width:150,
					label:'销售出库单号'
				},{
					prop:'client_number',
					width:150,
					label:'定制补单号'
				},{
					prop:'question_goods_code',
					width:180,
					label:'商品编码'
				},{
					prop:'question_goods_name',
					width:220,
					label:'商品名称'
				},{
					prop:'material_code',
					width:180,
					label:'物料编码'
				},{
					prop:'material_name',
					width:120,
					label:'物料名称'
				},{
					prop:'question_goods_desc',
					width:220,
					label:'规格描述'
				},{
					prop:'create_time',
					format: 'dataFormat1',
					width:150,
					label:'创建时间'
				},{
					prop:'modify_time',
					format: 'dataFormat1',
					width:150,
					label:'更新时间'
				},{
					prop:'liability_type',
					width:150,
					label:'责任类型'
				},{
					prop:'liability_question',
					width:150,
					label:'责任问题'
				},{
					prop:'old_liability_amount',
					width:120,
					label:'调整前责任金额'
				},{
					prop:'new_liability_amount',
					width:120,
					label:'调整后责任金额'
				},{
					prop:'diff_liability_amount',
					label:'差额'
				},{
					prop:'interface_bill_no',
					width:150,
					label:'下推K3调整应收单'
				},{
					prop:'interface_status',
					formatter:prop => ({
						NONE : '不处理',
						WAIT: '等待',
						SUCCESS: '成功',
						FAILED: '失败',
						RUNNING: '执行中',
					}[prop] || prop),
					label:'接口状态'
				},{
					prop:'interface_info',
					width:120,
					label:'接口提示信息'
				},{
					prop:'interface_time',
					format: 'dataFormat1',
					width:150,
					label:'K3调整应收单生成时间'
				},{
					label: "补件规格描述",
					prop: "description",
					width: 150,
				},{
				label: "锁定人",
				prop: "lock_name",
				width: 120,
				},],
				count:0,
                selectedShopData:'',
                radioData:'',
                list:[],//店铺列表

                selectType:'radio',//默认单选，因为是财务中台导入过来的数据

			}
		},
		methods:{
			repushK3Receivable(row){
				this.ajax.postStream('afterSale-web/api/aftersale/supply/incomeAdjust/repushK3Receivable',{billNo: row.supply_income_adjust_no}, res => {
					if (res.body.result) {
						this.getList();
						this.$message.success(res.body.msg);
					} else {
						this.$message.error(res.body.msg);
					}
				});
			},

			/**
			*单选事件触发
			*/
			radioChange(data){
				this.selectedShopData = data;
			},

			/**
			*关闭当前弹出框组件
			*/
            closeCurrentComponent(){
                this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
			},

            confirmAddGoods(){
				if(!this.selectedShopData){
					this.$message.error('请选择单据');
					return;
				}
				this.ajax.postStream('/afterSale-web/api/aftersale/supply/incomeAdjust/repushK3Receivable',{billNo:this.selectedShopData.supply_income_adjust_no} , res => {
					if (res.body.result) {
			  			this.getList();
						this.$message.success(res.body.msg);
					} else {
						this.$message.error(res.body.msg);
					}
				});


			},
            sizeChange(size){
                // 每页加载数据
				this.search.page_size = size;
                this.search.page_no = 1;

                this.getList();
			},
            pageChange(page_no){
                // 页数改变
                this.search.page_no = page_no;
                this.getList();
            },

            resetSearchData(bool){//bool,是要加载首页的数据还是加载当前页的数据，为true时，加载首页，false则为当前页面
				 bool?this.search.page.page_no = 1:'';
			},
			/**
			 * 请空已选的数据
			 * **/
			clearSelectedData(){
                this.selectedShopData = '';
                this.radioData = '';
                this.$refs.list && this.$refs.list.clearSelection();
			},
			searchClick(obj, resolve) {
			let self = this;
			this.search.where = obj;
			new Promise((res,rej)=>{
					self.getList(resolve,res);
				}).then(()=>{
					if(this.search.page_no != 1){
						self.count = 0;
					}
					self.showCount = false;
				})
			},
            searchList(callback){
              //模糊搜索
			  this.resetSearchData(!0);
			  this.getList();
			},
            shopSelect(selection){//单选发生改变
				var i = selection.length;
                this.selectedShopData = i?selection:'';
            },

            /**
            *初使化数据
            **/
			initData(){
			  var data = this.params || {};
			   //列表选择框的问题，1为单选，2为多选，3为没有任何选择框
			  this.selectType = data.type == 1?'radio':data.type == 2?'checkbox':'';

			},
			export() {
				let url = this.uploadUrl;
				// let postData = JSON.parse(JSON.stringify(this.from));
				if(this.search.where.length==0){
					this.$message.error('请添加查询条件');
					return false;
				}
				let postData = Object.assign({}, this.search);




				this.ajax.postStream(url, postData, res => {
					if (res.body.result) {
						this.$message.success(res.body.msg);
					} else {
						this.$message.error(res.body.msg);
					}
				});
			},
			showExportList (){
				this.$root.eventHandle.$emit('alert', {
					component: () => import('@components/after_sales_report/export'),
					style:'width:900px;height:600px',
					title: '报表导出列表',
					params: {
						query: {
						type: 'EXCEL_TYPE_SUPPLY_INCOME_ADJUST',
						},
					},
				})
			},
            getList(resolve,callback){
				var params = {
					page_name :this.search.page_name,
					page:{
						page_no:this.search.page_no,
						page_size:this.search.page_size,
					}
				};
				// params.key = this.search.key;
				this.ajax.postStream('/afterSale-web/api/aftersale/supply/incomeAdjust/list',this.search,(d)=>{
              	    var data = d.body;

              	    if(!data.result){

                        this.$message.error(data.msg);
              	        return;
					}
					if(!this.showCount){
						let total = this.search.page_no * this.search.page_size
						this.count = data.content.length == (this.search.page_size+1)? total+1:total;

					}
                    this.list = data.content;
					this.list.forEach(item => {
						item.repush = ''
					});
					// this.count = data.content.count;
					callback && callback();
					resolve && resolve();
                    this.clearSelectedData();
                },function(e){
					callback && callback();
					resolve && resolve();
					console.log(e);
                })
            }


		},

        created(){
			var _this = this;

            // 查询用户信息
			_this.initData();
			_this.getList();
		}



	}
</script>
<style type="text/css" scoped>
	.el-table__body-wrapper{margin-bottom:20px;}
</style>