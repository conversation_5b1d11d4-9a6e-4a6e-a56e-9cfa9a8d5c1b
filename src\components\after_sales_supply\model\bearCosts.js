// 补件申请单详情--客户承担费用
import { BILL_TYPE_STATUS, TAOBAO_STATUS } from "@/components/after_solutions/enum";
export default {
  data() {
    let self = this
    return {
      bearCostsList: [],
      bearCostsCol: [
        { label: '平台订单号', prop: 'tid' },
        {
          label: '订单业务模式',
          prop: 'orderBusinessMode',
          formatter: (val) => {
            return (
              BILL_TYPE_STATUS[val] || val
            );
          }
        },
        { label: '合并单号', prop: 'mergeTradeNo' },
        {
          label: '淘宝交易状态',
          prop: 'status',
          formatter: (val) => {
            return (
              TAOBAO_STATUS[val] || val
            );
          } },
        { label: '客户承担费用', prop: 'bearFee' }
      ]
    }
  },
  methods:{
  }
}
