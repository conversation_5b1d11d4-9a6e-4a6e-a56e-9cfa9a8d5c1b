// 补件申请单详情--接口信息
export default {
  data() {
    let self = this
    return {

      interfaceList: [],
      interface_statusL:{ISSUEDING:'下达中',ISSUED:'已下达',EXECFAILED:'失败'},
      bill_typeL:{RETURNS:'退换货跟踪单',SUPPLY:'补件单',REFUND:'退款申请单',REPAIR:'服务单',SERVICE:'4PL平台服务单'},
      interface_typeL:{
        GENERATPURCHASE:'生成采购需求',
        GENERATOUTBOUND:'生成出库单',
        CANCELPURCHASE:'取消采购需求',
        CANCELOUTBOUND:'取消出库单',
        COMMITOUTBOUND :'提交出库单',
        GENERATSERVICEBILL: '生成服务单',
        CANCELSERVICEBILL: '取消服务单',
        GENERATANALYSISSUBORDER: '生成责任分析单',
        PUSHUPDARTEPURCHASE: '修改采购补件收货信息，推送财务中台',
        GENERATK3SaleOutStock: '生成K3销售出库单',
        CANCELANALYSISSUBORDER: '取消责任分析单',
        CANCELK3SaleOutStock: '取消K3销售出库单',
        GETTOKEN: '获取财务中台TOKEN'
      },
      interfaceCol: [
        {
          label: '下游单据类型',
          prop: 'bill_type',
          formatter:function(row){
            return self.bill_typeL[row] || row
          }
        }, {
          label: '单据编号',
          prop: 'bill_no',
          width:300,

        },{
          label: '方式',
          prop: 'operation_type',
          width:100,
          formatter:function(row){
            return self.interface_typeL[row] || row;
          }

        },{
          label: '接口状态',

          prop: 'status',
          formatter:function(row){
            return self.interface_statusL[row] || row;
          }
        },{
          label: '单号',
           width:300,
          prop: 'kingdee_no'
        },{
          label: '接口提示信息',
           width:300,
          prop: 'interface_info'
        },{
          label: '生成时间',
          width:150,
          prop: 'create_time',
          format:'dataFormat1'
        },{
          label: '传送参数',
          width:300,
          slot:'parameter_info',
          prop: 'parameter_info',
        },
        {
          label: '操作',
          width:100,
          slot:'operation',
          prop: 'operation',
        }
      ]
    }
  },

  methods:{
    test(){
      console.log('asdfasdfasdf');
    }
  }
}
