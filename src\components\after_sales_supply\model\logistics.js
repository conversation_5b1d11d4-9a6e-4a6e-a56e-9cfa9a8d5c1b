// 补件申请单详情--物流信息
export default {
  data() {
    return {
      outboundNo: "",
      logisticsBtns: [
        {
          type: "primary",
          txt: "快递转寄",
          click: () => this.deliveryForward(),
        },
      ],
      logisticsSelected: "",
      logisticsList: [],
      logisticsCol: [
        {
          label: "来源单据号",
          prop: "outbound_no",
        },
        {
          label: "运输单号",
          prop: "transport_no",
        },
        {
          label: "发货物流",
          prop: "delivery_logistics",
        },
        {
          label: "货运方式",
          prop: "transport_mode",
          format: "auxFormat",
          formatParams: "bjd_hyfs",
        },
        {
          label: "是否与供应商结算",
          prop: "if_settle_supplier",
          formatter(val) {
            return val === "Y" ? "是" : val === "N" ? "否" : val;
          },
        },
        {
          label: "发货时间",
          width: 150,
          prop: "delivery_time",
          format: "dataFormat1",
        },
        {
          label: "签收时间",
          width: 150,
          prop: "sign_time",
          format: "dataFormat1",
        },
        {
          label: "结算方式",
          prop: "settlement_method",
        },
        {
          label: "实际发货人",
          prop: "actual_shipper",
        },
        {
          label: "运输成本",
          prop: "transportation_cost",
        },
        {
          label: "发货件数",
          prop: "delivery_quantity",
        },
      ],
    };
  },

  methods: {
    test() {
      console.log("asdfasdfasdf");
    },
    handleLogisticsListSelectionChange(logistic) {
      this.logisticsSelected = logistic;
      this.outboundNo = logistic.outbound_no;
    },
    deliveryForward() {
      if (!this.logisticsSelected) {
        this.$message.error("请先勾选一行物流信息");
        return;
      }
      // 快递转寄校验
      this.ajax.get(
        `/afterSale-web/api/aftersale/ticketSupply/checkExpressForward?outboundNo=${this.outboundNo}`,
        (res) => {
          if (res.body.result) {
            // 弹窗备注
            this.$root.eventHandle.$emit("alert", {
              params: {
                isAlert: true,
                btn: {
                  txt: "提交",
                },
                callback: async (data) => {
                  const { remark, alertId } = data;
                  return await new Promise((resolve, reject) => {
                    this.ajax.postStream(
                      `/afterSale-web/api/aftersale/ticketSupply/expressForward`,
                      {
                        outboundNo: this.outboundNo,
                        remark,
                      },
                      (res) => {
                        if (res.body.result) {
                          this.$message.success(res.body.msg);
                          this.$root.eventHandle.$emit("removeAlert", alertId);
                          resolve();
                        } else {
                          this.$message.error(res.body.msg);
                          reject();
                        }
                      },
                      (err) => {
                        reject();
                        this.$message.error(err);
                      }
                    );
                  });
                },
              },
              component: () =>
                import(
                  "@/components/after_sales_supply/components/remarkAlart.vue"
                ),
              style: "width:500px;height:360px",
              title: "快递转寄",
            });
          } else {
            this.$message.error(res.body.msg);
          }
        }
      );
    },
  },
};
