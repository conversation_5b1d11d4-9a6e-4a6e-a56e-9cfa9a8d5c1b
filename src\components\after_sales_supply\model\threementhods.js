// 第三方系统对接接口信息
export default {


  methods:{
    //得到生产采购单参数
    getPurchaseOrderData(){
      var self =this;
      var list=[];
      for(var i=0;i<self.selectGoodsList.length;i++){
        var obj={id:self.selectGoodsList[i].id,supplier_company_id:self.selectGoodsList[i].supplier_company_id}
        list.push(obj);
      }
      var data={ticketSupply:{id:self.form.id,after_ticket_no:self.form.after_ticket_no},listPlanSupplyComponent:list}
      return data;

    },
    //生成采购订单
    generatPurchaseOrder(){
      var self=this;

      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(self.selectGoodsList.length==0){
        self.$message.error("操作失败，商品信息为空,请选择商品")
        return;
      }
      self.ajax.postStream(
        this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/generatPurchaseOrder?permissionCode=SUPPLY_APPLY_GENERATE_PURCHASE_ORDER'),
        self.getPurchaseOrderData(),
        function(response){
          if(response.body.result){
            self.$message.success("操作成功");
            self.getDetailInfo(self.form.id);
          }else{
            self.$message.error(response.body.msg)
          }
        },
        e=>{
            self.$message.error(e)
        }
      )
    },

    //得到取消采购单参数-问题商品信息-采购需求
    getrCancelPurchaseOrderData(){
      var self =this;
      var list=[];
      for(var i=0;i<self.selectGoodsList.length;i++){
        if(self.selectGoodsList[i].purchase_demand_code){
          list.push(self.selectGoodsList[i].purchase_demand_code);
        }
      }
      list = Array.from(new Set(list));
      console.log('取消采购单');
      var data={after_ticket_no:self.form.after_ticket_no,purchaseDemandCodeList:list}
      return data;
    },
    //取消采购单
    cancelPurchaseOrder(){
      var self=this;

      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(self.selectGoodsList.length==0){
        self.$message.error("操作失败，商品信息为空,请选择商品")
        return;
      }
      var data=self.getrCancelPurchaseOrderData();
      /*if(data.purchaseOrderNoList.length!=self.selectGoodsList.length){
        self.$message.error("操作失败，所选商品信息存在采购订单号为空情况")
        return;
      }*/
      self.ajax.postStream(
        this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/cancelPurchaseOrder?permissionCode=SUPPLY_APPLY_CANCEL_PURCHASE_ORDER'),
        data,
        function(response){
          if(response.body.result){
            self.$message.success("操作成功");
            self.getDetailInfo(self.form.id);
          }else{
            self.$message.error(response.body.msg)
          }
        },
        e=>{
            self.$message.error(e)
        }
      )
    },

    //得到出库单参数
    getOutboundOrder(){
      var self =this;
      var list=[];
      for(var i=0;i<self.selectGoodsList.length;i++){
        var obj={id:self.selectGoodsList[i].id,if_company_send:self.selectGoodsList[i].if_company_send,supplier_company_id:self.selectGoodsList[i].supplier_company_id}
        list.push(obj);
      }
      var data={ticketSupply:{id:self.form.id,after_ticket_no:self.form.after_ticket_no},listPlanSupplyComponent:list}
      return data;

    },
    //生成出库单
    generatOutboundOrder(){
      var self=this;

      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(self.selectGoodsList.length==0){
        self.$message.error("操作失败，商品信息为空,请选择商品")
        return;
      }
      self.ajax.postStream(
        this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/generatOutboundOrder?permissionCode=SUPPLY_APPLY_GENERATE_OTHER_DELIVERY_ORDER'),
        self.getOutboundOrder(),
        function(response){
            if(response.body.result){
              self.$message.success("操作成功");
              self.getDetailInfo(self.form.id);
            }else{
              self.$message.error(response.body.msg)
            }
          },
          e=>{
              self.$message.error(e)
          }
        )
    },
    //生成K3出库单
    generateFinSaleOutStock(){
      var self=this;

      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(self.selectGoodsList.length==0){
        self.$message.error("操作失败，商品信息为空,请选择商品")
        return;
      }
      self.ajax.postStream(
        this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/generateFinSaleOutStock?permissionCode=SUPPLY_APPLY_GENERATE_OTHER_DELIVERY_ORDER'),
        self.getOutboundOrder(),
        function(response){
            if(response.body.result){
              self.$message.success("操作成功");
              self.getDetailInfo(self.form.id);
            }else{
              self.$message.error(response.body.msg)
            }
          },
          e=>{
              self.$message.error(e)
          }
        )
    },

    //得到取消出库单参数
    getrCancelOutBoundOrderData(){
      //var self =this;
      var list=[];
      for(var i=0;i<this.selectGoodsList.length;i++){
        list.push(this.selectGoodsList[i].id);
      }
      list = Array.from(new Set(list));
      console.log('取消出库单');
      var data={after_ticket_no:this.form.after_ticket_no,supplyComponentIds:list}
      return data;
    },
    //取消出库单
    cancelOutBoundOrder(){
      var self=this;

      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(self.selectGoodsList.length==0){
        self.$message.error("操作失败，商品信息为空,请选择商品")
        return;
      }
      self.ajax.postStream(
        this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/cancelOutBoundOrder?permissionCode=SUPPLY_APPLY_CANCEL_OTHER_DELIVERY_ORDER'),
        self.getrCancelOutBoundOrderData(),
        function(response){
          if(response.body.result){
            self.$message.success("操作成功");
            self.getDetailInfo(self.form.id);
          }else{
            self.$message.error(response.body.msg)
          }
        },
        e=>{
            self.$message.error(e)
        }
      )
    },
    cancelFinSaleOutStock(){
      var self=this;

      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(self.selectGoodsList.length==0){
        self.$message.error("操作失败，商品信息为空,请选择商品")
        return;
      }
      self.ajax.postStream(
        this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/cancelFinSaleOutStock?permissionCode=SUPPLY_APPLY_CANCEL_OTHER_DELIVERY_ORDER'),
        self.getrCancelOutBoundOrderData(),
        function(response){
          if(response.body.result){
            self.$message.success("操作成功");
            self.getDetailInfo(self.form.id);
          }else{
            self.$message.error(response.body.msg)
          }
        },
        e=>{
            self.$message.error(e)
        }
      )
    },

    //得到生成服务单参数
    getgenerateServiceBillData(){
      var self =this;
      var list=[];
      for(var i=0;i<self.selectGoodsList.length;i++){
        list.push(self.selectGoodsList[i].id);
      }
      return list;

    },
    //生成出服务单
    generateServiceBill(){
      var self=this;

      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(self.selectGoodsList.length==0){
        self.$message.error("操作失败，商品信息为空,请选择商品")
        return;
      }
      self.ajax.postStream(
        this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/generateServiceBill?permissionCode=SUPPLY_APPLY_GENERATE_SERVICE_ORDER'),
        self.getgenerateServiceBillData(),
        function(response){
          if(response.body.result){
            self.$message.success("操作成功");
            self.getDetailInfo(self.form.id);
          }else{
            self.$message.error(response.body.msg)
          }
        },
        e=>{
            self.$message.error(e)
        }
      )
    },
    //得到取消服务单参数
    getrCancelServiceBill(){
      var self =this;
      var list=[];
      for(var i=0;i<self.selectGoodsList.length;i++){
        list.push(self.selectGoodsList[i].service_bill_no);
      }
      var data={list_service_bill_no:list}
      return data;
    },
    //取消服务单
    cancelServiceBill(){
      var self=this;

      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(self.selectGoodsList.length==0){
        self.$message.error("操作失败，商品信息为空,请选择商品")
        return;
      }
      self.ajax.postStream(
        this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/cancelServiceBill?permissionCode=SUPPLY_APPLY_CANCEL_SERVICE_ORDER'),
        self.getrCancelServiceBill(),
        function(response){
          if(response.body.result){
            self.$message.success("操作成功");
            self.getDetailInfo(self.form.id);
          }else{
            self.$message.error(response.body.msg)
          }
        },
        e=>{
          self.$message.error(e)
        }
      )
    },

  }

}
