<!--提货点-->
<template>
	<xpt-list 
		:data='list' 
		
		:colData='colData' 
		:pageTotal='count' 
		:btns='btns'
		:selection='selectType' 
		:isNeedClickEvent='true'
		:simpleSearch="true"
		@row-dblclick='rowDblclick' 
		@radio-change='radioChange'
		@search-click='searchList' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange' 
	></xpt-list>
</template>
<script>
	

	export default {//需要类型和相关的id
	    props:['params'],
		data(){
            var self = this;
			return {
                
                search:{
                    key:'',
					page:{
                        /*length:20,
						pageNo:1*/
						page_size:self.pageSize,
						page_no:1,
						
					}
				},
				btns:[{
					txt:'确认',
					type:'info',
					click:()=>{
						self.confirmAddGoods();
					}
				}],
				colData:[{
					prop:'f_LS_NUMBER',
					label:'提货点编码'
				},{
					prop:'f_LS_NAME',
					label:'提货点名称'
				},{
					prop:'fname',
					label:'物流公司'
				},{
					prop:'f_LS_PHONE',
					label:'提货点电话'
				}],
				count:0,
                selectedShopData:'',
                radioData:'',
                list:[],//店铺列表
               	
                selectType:'radio',//默认单选，因为是财务中台导入过来的数据
                
			}
		},
		methods:{
			/**
			*点双击
			**/
			rowDblclick(data){
				console.log('多选');
				if(this.selectType === 'checkbox') {
					this.selectedShopData = this.selectedShopData.length?this.selectedShopData:[];
					this.selectedShopData.push(data);
					this.selectedShopData = Array.from(new Set(this.selectedShopData));
				}
				this.confirmAddGoods();
			},
			/**
			*单选事件触发
			*/
			radioChange(data){
				this.radioData = data;
			},

			/**
			*关闭当前弹出框组件
			*/
            closeCurrentComponent(){
                this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
			},
			
            confirmAddGoods(){
				//确认添加物料信息
				var type = this.selectType;
				var  selectedGoodsData = type == 'radio'?this.radioData:type=='checkbox'?this.selectedShopData:'';
				selectedGoodsData.constructor == Object?selectedGoodsData = [selectedGoodsData]:'';
				if(type != 3 && (!selectedGoodsData || !selectedGoodsData.length)) {
				    this.$message({
				        message:'请选择三包信息',
						type:'error'
					})
					return;
				}

                this.closeCurrentComponent();
				this.params.callback && this.params.callback({data:selectedGoodsData});


			},
            sizeChange(size){
                // 每页加载数据
				this.search.page.page_size = size;
                this.search.page.page_no = 1;
               
                this.getGoodsList();
			},
            pageChange(page_no){
                // 页数改变
                this.search.page.page_no = page_no;
                this.getGoodsList();
            },

            resetSearchData(bool){//bool,是要加载首页的数据还是加载当前页的数据，为true时，加载首页，false则为当前页面
				 bool?this.search.page.page_no = 1:'';
			},
			/**
			 * 请空已选的数据
			 * **/
			clearSelectedData(){
                this.selectedShopData = '';
                this.radioData = '';
                this.$refs.list && this.$refs.list.clearSelection();
			},

            searchList(value){
              //模糊搜索
              this.search.key = value;
			  this.resetSearchData(!0);
			  this.getGoodsList();
			},
            shopSelect(selection){//单选发生改变
				var i = selection.length;
                this.selectedShopData = i?selection:'';
            },

            /**
            *初使化数据
            **/
			initData(){
			  var data = this.params || {};
			   //列表选择框的问题，1为单选，2为多选，3为没有任何选择框
			  this.selectType = data.type == 1?'radio':data.type == 2?'checkbox':'';
			
			},

            getGoodsList(){
				var params = this.search.page;
				params.key = this.search.key;
				this.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/getPageDelPointLogicstisInfo',params,(d)=>{
              	    var data = d.body;

              	    if(!data.result){
                        /*_this.$message({
                            message:data.msg,
                            type:'error'
                        })*/
                        this.$message.error(data.msg);
              	        return;
					}
                    this.list = data.content.list;
					this.count = data.content.count;
                    this.clearSelectedData();
                },function(e){
					console.log(e);
                })
            }


		},

        created(){
			var _this = this;

            // 查询用户信息
			_this.initData();
			_this.getGoodsList();
		}



	}
</script>
<style type="text/css" scoped>
	.el-table__body-wrapper{margin-bottom:20px;}
</style>