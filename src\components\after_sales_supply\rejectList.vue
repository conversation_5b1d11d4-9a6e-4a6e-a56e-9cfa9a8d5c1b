<!-- 补件申请单波或信息 -->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button type="primary" size="mini" @click='addRow' :disabled="isAddRow">新增行</el-button>
        <el-button type="danger" size="mini" @click="dealRow" :disabled="isDelRow">删除行</el-button>
        </el-col>
    </el-row>
    <el-row class="xpt-flex__bottom">
      <el-table border :data='rejectList'  tooltip-effect="dark" style="width: 100%;" @selection-change="handleSelectionChange" show-overflow-tooltip >
        <el-table-column type="selection" ></el-table-column>
       <!-- <el-table-column width="28">
          <template slot-scope='scope'>
            <el-checkbox-group v-model="scope.row.testid" >
              <el-checkbox :label="scope.row.testid" class='xpt-table__checkbox'
                        :disabled="scope.row.testid==null?true:false">
              </el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>-->
        <el-table-column label="驳回人" prop="rejector_name" width="100"></el-table-column>
        <el-table-column label="业务备注" prop="biz_note"  show-overflow-tooltip width="350">

         <!--  <template  slot-scope="scope">
            <el-input  v-if="scope.row.id==null?true:false" v-model="scope.row.reject_remark" size='mini'  style="width:100%;" maxlength="255"></el-input>
            <span v-else>{{scope.row.reject_remark}}</span>
          </template> -->
        </el-table-column>
        <el-table-column label="驳回原因"  show-overflow-tooltip>
          <template  slot-scope="scope">
            <el-input  v-if="scope.row.id==null?true:false" v-model="scope.row.reject_remark" size='mini'   style="width:100%;" maxlength="255"></el-input>
            <span v-else>{{scope.row.reject_remark}}</span>
          </template>

        </el-table-column>
        <el-table-column label="驳回时间" width="150">
          <template slot-scope="scope">
            <span>{{scope.row.reject_time | dataFormat1}}</span>
           </template>
        </el-table-column>
      </el-table>
    </el-row>
  </div>
</template>

<script>
  import Fn from '@common/Fn.js';
  export default {
    props: ['params','isAdd','rejectList','formInfo','rejectDisabled'],
    data (){
      var self=this;
      return {
        indexNumber:1,
        rejectObj:{},
        selectDatas:'',//多选数据
        isAddRow:false,
        isDelRow:true,
       // testid:null,
      }
    },
    methods: {
      //得到一个新的goodsObj
      getRejectObj(){
        let name = Fn.getUserInfo('fullName');
        console.log('name',name);
        var obj = {
          id:null,
          after_bill_id: null,
          rejector_name: name,//驳回人
          biz_note : this.formInfo.business_remark,//业务备注
          reject_remark : null,//驳回原因
          reject_time: null,//驳回时间
          create_time:null,
          creator:null,
          creator_name:null,
          rejector:null,
          //testid:"test"
        }
        return obj;
      },
      /**
      *
      **/
      //判断能否新增或者删除
      isAddRowFun(){
          //var self=this;
          //var noIdNum=0;
          //this.isAddRow=false;
          /*if(!this.formInfo.clerk_lock_id){//文员未锁定
            this.isAddRow = true;
            return;
          }*/
          this.isAddRow = this.rejectDisabled;
          if(this.isAddRow) return;
          /*if(!this.rejectList || this.rejectList.length==0){
              this.isAddRow=false;
              return;
          }*/
          //如果有一条新增的数据，则不让它新增
          let i = this.rejectList.length;
          for(let a = 0;a < i; a++){
            if(!this.rejectList[a].id){
              this.isAddRow = true;
              return;
            }
          }

          this.isAddRow = false;
          return;

          /*for(var i=0;i<this.rejectList.length;i++){
              if(!(this.rejectList[i].id && this.rejectList[i].id !=null)){
                noIdNum=noIdNum+1;
              }
          }
          if(noIdNum>0){
            this.isAddRow=true;
          }*/
      },
      /**
      *删除驳回，只能删除新增行
      **/
      isDelRowFun(){
        /*var self=this;
        var noIdNum=0;
        self.isDelRow=true;
        if(!self.selectDatas || self.selectDatas.length==0){
          self.isDelRow=true;
          return;
        }
        for(var i=0;i<self.selectDatas.length;i++){
          if(!(self.selectDatas[i].id && self.selectDatas[i].id !=null)){
            noIdNum=noIdNum+1;
          }
        }
        if(noIdNum>0){
          self.isDelRow=false;
        }*/
        if(!this.selectDatas || !this.selectDatas.length){
          this.isDelRow = true;
          return;
        }
        let i = this.selectDatas.length;
        for(let a = 0;a < i; a++){
          if(!this.selectDatas[a].id){
            this.isDelRow = false;
            return;
          }
        }
        this.isDelRow = true;

      },
      //新增行
      addRow(){
        var self = this;
        self.rejectList.push(self.getRejectObj());
        self.isAddRowFun();
      },
      handleSelectionChange (selects){
          var self=this;
        this.selectDatas = selects;
        //self.isAddRowFun();
        self.isDelRowFun();
      },

      //删除行
      dealRow(){
        var self = this;
        if (!self.selectDatas || self.selectDatas.length < 1) {
          this.$message.error('请至少选择一行数据');
          return;
        }
        var ids = [];
        for (var i = 0; i < self.selectDatas.length; i++) {
          var item = self.selectDatas[i];
          if (item.id) {
            ids.push(item.id);
          }
          for (var j = 0; j < self.rejectList.length; j++) {
            if (self.rejectList[j] == item && item.id==null) {
              self.rejectList.splice(j, 1);
              break;
            }
          }
        }
        self.isAddRowFun();
        self.isDelRowFun();
      },
    },
    watch:{
      'rejectList':function(newval,oldval){
        this.isAddRowFun();
        this.isDelRowFun();
        this.$emit("getRejectList",this.rejectList);
      },
      'rejectDisabled':function(newval,oldval){
        this.isAddRowFun();
      }/*,
      'formInfo.clerk_lock_id':function(newval,oldval){
        this.isAddRowFun();
      }*/
    },
    mounted(){
      this.isAddRowFun();
    }
  }
</script>
