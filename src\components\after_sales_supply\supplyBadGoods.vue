<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-col :span="24">
			<!-- <el-button type="primary" size="mini" @click='addRow' :disabled="!isSqdAdd">新增行</el-button>
			<el-button type="danger" size="mini" @click="dealRow" :disabled="!isSqdAdd">删除行</el-button> -->

      <!-- 单据状态是已审核，新增行和删除行禁掉 -->
      <el-button type="primary" size="mini" @click='addRow' :disabled="formInfo.status === 'APPROVED' || businessBtnDisabled || isDZBJ">新增行</el-button>
      <el-button type="danger" size="mini" @click="dealRow" :disabled="formInfo.status === 'APPROVED' || businessBtnDisabled || isDZBJ">删除行</el-button>
			<el-button type="danger" size="mini" @click="checkIsSUPPLY_ASSISTANT(lockStock)" :disabled="clerkBtnDisabled || isDZBJ">锁库</el-button>
			<el-button type="primary" size="mini" @click="checkIsSUPPLY_ASSISTANT(unLockStock)" :disabled="clerkBtnDisabled || isDZBJ">反锁库</el-button>
			<el-button type="primary" size="mini"  @click="getRefreshUsed" :disabled="isDZBJ">获取库存数</el-button>
			<!-- <el-button type="danger" size="mini" @click="lineTerminate" :disabled="!isSqdAdd">行终止</el-button>
      <el-button type="primary" size="mini" @click="lineUnTerminate" :disabled="!isSqdAdd">行反终止</el-button> -->
      <!-- <el-button type="danger" size="mini" @click="lineTerminate" :disabled="canTerminate">行终止</el-button>
      <el-button type="primary" size="mini" @click="lineUnTerminate" :disabled="canTerminate">行反终止</el-button> -->

      <el-button type="danger" size="mini" :disabled="formInfo.status === 'CLOSE'" @click="checkIsSUPPLY_ASSISTANT(lineTerminate)" >行终止</el-button>
      <el-button type="primary" size="mini" :disabled="formInfo.status === 'CLOSE'" @click="checkIsSUPPLY_ASSISTANT(lineUnTerminate)" >行反终止</el-button>
			<el-button type="primary" size="mini" :disabled="formInfo.status === 'CLOSE'"  @click="getSupplyCost" >获取补件采购成本</el-button>
			<el-button type="primary" size="mini" :disabled="formInfo.status === 'CLOSE'"  @click="pushRechargeBill" >下推货款充值单</el-button>
		</el-col>
	</el-row>
	<el-row class="xpt-flex__bottom">
		<el-table border :data='goodsInfoList'  tooltip-effect="dark" width='100%' style="width: 100%;" @selection-change="handleSelectionChange" show-overflow-tooltip v-if="typeNumber==2" >
			<el-table-column type="selection" width="50"></el-table-column>
      <el-table-column type="index" width="40" label="序号"></el-table-column>
			<el-table-column label="商品编号" prop="question_goods_code" width="160"></el-table-column>
			<el-table-column label="商品名称" prop="question_goods_name" width="150" show-overflow-tooltip></el-table-column>
			<el-table-column label="规格描述" prop="question_goods_desc" width="100" show-overflow-tooltip></el-table-column>
			<el-table-column label="物料编码" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-if="scope.row.if_sqdadd=='Y'" v-model="scope.row.material_code" size='mini' icon="search" @click="choseMaterialCode(scope.row)" :disabled="isNotCanSave" readonly></el-input>

          <span v-else >{{scope.row.material_code}}</span>
        </template>
      </el-table-column>
			<el-table-column label="物料名称" prop="material_name" width="160" show-overflow-tooltip></el-table-column>
      <el-table-column label="是否为赠品" prop="present_flag" show-overflow-tooltip width="100">
          <template slot-scope="scope">
            {{scope.row.present_flag == '1'?'是':'否'}}
          </template>
        </el-table-column>
      <el-table-column label="采购需求" prop="purchase_demand_code" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column label="运单号" prop="transport_no" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="采购单号" prop="purchase_warehouse_no" width="100" show-overflow-tooltip></el-table-column>
			<el-table-column label="补件规格描述" prop="description" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column label="预计交货日期" prop="delivery_date" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{scope.row.delivery_date | dataFormat}}</span>
        </template>
      </el-table-column>
			<el-table-column label="补件数量"  width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-if="scope.row.if_sqdadd=='Y'" v-model="scope.row.number" size='mini' type="number" :disabled="isNotCanSave"></el-input>

          <span v-else >{{scope.row.number}}</span>
        </template>
      </el-table-column>
      <el-table-column label="补件次数"  width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{scope.row.times}}</span>
        </template>
      </el-table-column>
      <div v-if="!rowDisabled" >
          <el-table-column label="补件采购成本" prop="bought_price" width="90" show-overflow-tooltip></el-table-column>
      </div>
      <el-table-column label="加购成本" prop="add_cost" width="90" show-overflow-tooltip></el-table-column>
      <el-table-column label="采购日期" prop="purchase_date" width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{scope.row.purchase_date | dataFormat1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="生成的服务单号" prop="service_bill_no" width="130" show-overflow-tooltip></el-table-column>
      <el-table-column label="收入店铺" prop="incom_shop_name" width="100" show-overflow-tooltip></el-table-column>

      <el-table-column label="物料单位" prop="units" width="100" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column label="工厂代发"  width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-select size="mini"  style="width:100%"  v-model="scope.row.if_company_send" placeholder="请选择" disabled>
            <el-option
              v-for="(value,key) in {Y:'是',N:'否'}"
              :label="value"
              :value="key" :key='key'>
            </el-option>
          </el-select>
        </template>
      </el-table-column> -->
			<!-- <el-table-column label="申请需求描述" width="200" show-overflow-tooltip v-if="isNotCanSave||clerkBtnDisabled">
        <template slot-scope="scope">
          <span>{{scope.row.apply_desc}}</span>

        </template>
      </el-table-column> -->
      <el-table-column label="申请需求描述" width="200" show-overflow-tooltip  :class-name="pmm_input_textarea">
        <template slot-scope="scope">
          <span v-if="applyDescDisabled">{{scope.row.apply_desc}}</span>
          <el-input  :value="autoMakeApplyDesc(scope.row)" size='mini'  type="textarea" v-else disabled></el-input>
        </template>
      </el-table-column>

			<el-table-column label="采购员" prop="buyer_name" width="100" show-overflow-tooltip></el-table-column>
      <div v-if="!rowDisabled" >
        <el-table-column label="供应商"  width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-input v-model="scope.row.supplier_company" size='mini'  icon="search" @click="choseSupplier(scope.row)" :disabled="isNotCanSave" @blur="changeSupplier(scope.row)"></el-input>
          </template>
        </el-table-column>
      </div>

			<el-table-column label="工厂代发"  width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-select size="mini"  style="width:100%"  v-model="scope.row.if_company_send" placeholder="请选择" :disabled="scope.row.if_lock_stock=='Y'?true:false">
            <el-option
              v-for="(value,key) in {Y:'是',N:'否'}"
              :label="value"
              :value="key" :key='key'>
            </el-option>
          </el-select>
        </template>
      </el-table-column>
			<el-table-column label="是否包装"  width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-select size="mini"  style="width:100%"  v-model="scope.row.if_packaging" placeholder="请选择" :disabled="isNotCanSave">
            <el-option
              v-for="(value,key) in {Y:'是',N:'否'}"
              :label="value"
              :value="key" :key='key'>
            </el-option>
          </el-select>
        </template>
      </el-table-column>
			<!--<el-table-column label="是否安装"  width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          querylect size="mini"  style="width:100%"  v-model="scope.row.if_install" placeholder="请选择">
            <el-option
              v-for="(value,key) in {Y:'是',N:'否'}"
              :label="value"
              :value="key" :key='key'>
            </el-option>
          <querylect>
        </template>
      </el-table-column>-->
			<el-table-column label="可用库存" prop="has_stock" width="100" show-overflow-tooltip></el-table-column>
			<el-table-column label="锁库" prop="if_lock_stock" width="100" show-overflow-tooltip>
				<template slot-scope="scope">
					<span>{{ { Y: '是', N: '否' }[scope.row.if_lock_stock] }}</span>
				</template>
			</el-table-column>

			<!--
			<el-table-column label="参考价" prop="supply_code" width="100" show-overflow-tooltip></el-table-column>-->
			<!---->
      <el-table-column label="已采购"  prop="if_bought" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { Y: '是', N: '否' }[scope.row.if_bought] }}</span>
        </template>
			</el-table-column>
			<el-table-column label="已入库" prop="if_pushed" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { Y: '是', N: '否' }[scope.row.if_pushed] }}</span>
        </template>
			</el-table-column>
      <el-table-column label="入库日期" prop="pushed_date" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-date-picker type="date" placeholder="选择日期:" size="mini" v-model="scope.row.pushed_date":editable="false" :disabled="true" style="width:100%"></el-date-picker>
        </template>
      </el-table-column>
			<el-table-column label="已出库" prop="if_pulled" width="100" show-overflow-tooltip>
				<template slot-scope="scope">
					<span>{{ { Y: '是', N: '否' }[scope.row.if_pulled] }}</span>
				</template>
			</el-table-column>
			<!-- <el-table-column label="已入库日期" prop="close_time" width="100" show-overflow-tooltip>
				<template slot-scope="scope">
					<span>{{scope.row.close_time | dataFormat}}</span>
				</template>
			</el-table-column> -->
      <el-table-column label="已签收" prop="if_sign" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { Y: '是', N: '否' }[scope.row.if_sign] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="签收时间" prop="sign_time" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{scope.row.sign_time | dataFormat1}}</span>
        </template>
      </el-table-column>
			<el-table-column label="商品批号" prop="goods_branch_no" width="100" show-overflow-tooltip></el-table-column>
			<el-table-column label="通用补件"  width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { 1: '是', 0: '否' }[scope.row.common_supply] }}</span>
        </template>
      </el-table-column>
			<el-table-column label="商品BOM版本" prop="bom_version" width="100" show-overflow-tooltip></el-table-column>
			<el-table-column label="是否停产" width="100" show-overflow-tooltip>
				<template slot-scope="scope">
					<span>{{ { Y: '是', N: '否' }[scope.row.if_stop_produce] }}</span>
				</template>
			</el-table-column>
      <el-table-column label="备注" v-if="isNotCanSave" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
            {{scope.row.remark}}
        </template>
      </el-table-column>
			<el-table-column label="备注" v-else width="200" show-overflow-tooltip :class-name="pmm_input_textarea">
        <template slot-scope="scope">
          <el-input v-model="scope.row.remark" size='mini'  type="textarea"></el-input>
        </template>
      </el-table-column>


			<el-table-column label="行状态操作人员" prop="closer_name" width="100" show-overflow-tooltip></el-table-column>
			<el-table-column label="行状态操作日期" prop="close_time" width="100" show-overflow-tooltip>
				<template slot-scope="scope">
					<span>{{scope.row.close_time | dataFormat}}</span>
				</template>
			</el-table-column>
			<el-table-column label="行状态操作备注" prop="close_remark" width="100" >
        <template slot-scope="scope">
            <el-input v-model="scope.row.close_remark" size='mini'  :disabled="scope.row.if_sale_out_stock_audit != 'N'" style="width:100%;" ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="行状态操作原因" prop="line_close_type" width="200" >
        <template slot-scope="scope">
            <!-- <el-input v-model="scope.row.line_close_type" size='mini'  :disabled="canTerminate" style="width:100%;" ></el-input> -->
            <el-select
              width="160"
              v-model="scope.row.line_close_type"
              size="mini"
              placeholder="请选择"
               :disabled="scope.row.if_sale_out_stock_audit != 'N'"
              >
                <el-option key="LOST_ITEM_FOUND" label="丢件找到" value="LOST_ITEM_FOUND"></el-option>
                <el-option key="STOP_PRODUCTION" label="停产无法补件" value="STOP_PRODUCTION"></el-option>
                <el-option key="PLACE_WRONG_ORDER" label="下错单" value="PLACE_WRONG_ORDER"></el-option>
                <el-option key="NO_NEED_TO_HANDLE" label="不需要（协商，赔偿，维修）处理" value="NO_NEED_TO_HANDLE"></el-option>
                <el-option key="OTHER" label="其它" value="OTHER"></el-option>
              </el-select>
        </template>
      </el-table-column>
      <el-table-column label="出库单号" prop="warehouse_no" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column label="出库日期" prop="warehouse_date" width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{scope.row.warehouse_date | dataFormat}}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售出库单号" prop="sale_out_stock_bill_no" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column label="销售出库单已审核" prop="if_sale_out_stock_audit" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { Y: '是', N: '否' }[scope.row.if_sale_out_stock_audit] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售出库单审核时间" prop="sale_out_stock_audit_time" width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{scope.row.sale_out_stock_audit_time | dataFormat}}</span>
        </template>
      </el-table-column>
      <el-table-column label="货款充值单号" prop="recharge_bill_no" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column label="货款充值单状态" prop="recharge_status" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { A: '无需扣款', B: '未扣款' , C: '扣款中' , D: '已扣款' }[scope.row.recharge_status] }}</span>
        </template>
      </el-table-column>

      <el-table-column label="行状态" prop="status" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { CREATE: '未关闭', CLOSE: '已关闭' }[scope.row.status] }}</span>
        </template>
      </el-table-column>
			<el-table-column label="来源类型" prop="type" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.if_sqdadd=='N'">补件方案</span>
          <span v-else>手工新增</span>
        </template>
      </el-table-column>
			<el-table-column label="来源单号" prop="after_plan_id" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.if_sqdadd=='N'">{{scope.row.after_plan_no}}</span>
          <span v-else-if="scope.row.if_sqdadd=='Y'">{{formInfo.after_ticket_no}}</span>
          <span v-else></span>
        </template>
      </el-table-column>
		</el-table>

    <!-- 补件文员操作的界面 -->
    <el-table border :data='goodsInfoList'  tooltip-effect="dark" width='100%' style="width: 100%;" @selection-change="handleSelectionChange" show-overflow-tooltip v-else>
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column label="锁库" prop="if_lock_stock" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { Y: '是', N: '否' }[scope.row.if_lock_stock] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" prop="question_goods_name" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="规格描述" prop="question_goods_desc" width="150" show-overflow-tooltip></el-table-column>

      <el-table-column label="物料编码" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-if="scope.row.if_sqdadd=='Y'" v-model="scope.row.material_code" size='mini' icon="search" @click="choseMaterialCode(scope.row)" :disabled="isNotCanSave" readonly></el-input>

          <span v-else >{{scope.row.material_code}}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" prop="material_name" width="160" show-overflow-tooltip></el-table-column>
      <el-table-column label="补件规格描述" prop="description" width="100" show-overflow-tooltip></el-table-column>
       <el-table-column label="申请需求描述" width="200" show-overflow-tooltip  :class-name="pmm_input_textarea">
        <template slot-scope="scope">
          <span v-if="applyDescDisabled">{{scope.row.apply_desc}}</span>
          <el-input  :value="autoMakeApplyDesc(scope.row)" size='mini'  type="textarea" v-else disabled></el-input>
        </template>
      </el-table-column>

      <el-table-column label="补件数量"  width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-if="scope.row.if_sqdadd=='Y'" v-model="scope.row.number" size='mini' type="number" :disabled="isNotCanSave"></el-input>

          <span v-else >{{scope.row.number}}</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商"  width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model="scope.row.supplier_company" size='mini'  icon="search" @click="choseSupplier(scope.row)" :disabled="isNotCanSave" @blur="changeSupplier(scope.row)"></el-input>
        </template>
      </el-table-column>

      <el-table-column label="商品批号" prop="goods_branch_no" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column label="库存可用数" prop="has_stock" width="100" show-overflow-tooltip></el-table-column>



      <!-- <el-table-column label="商品编号" prop="question_goods_code" width="160"></el-table-column> -->



      <el-table-column label="行状态" prop="status" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { CREATE: '未关闭', CLOSE: '已关闭' }[scope.row.status] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否入库" prop="if_pushed" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { Y: '是', N: '否' }[scope.row.if_pushed] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否采购"  prop="if_bought" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { Y: '是', N: '否' }[scope.row.if_bought] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否出库" prop="if_pulled" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ { Y: '是', N: '否' }[scope.row.if_pulled] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="采购单号" prop="purchase_warehouse_no" width="100" show-overflow-tooltip>

      </el-table-column>
      <el-table-column label="出库单号" prop="warehouse_no" width="100" show-overflow-tooltip>

      </el-table-column>
      <el-table-column label="出库日期" prop="warehouse_date" width="130" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{scope.row.warehouse_date | dataFormat}}</span>
        </template>
      </el-table-column>

    </el-table>
	</el-row>
</div>
</template>

<script>
export default {
	props: ['params',/*'isAdd',*/'isNotCanSave','goodsInfoList','reflushList','formInfo',/*'isSqdAdd'*/'isFromSolution','businessBtnDisabled','clerkBtnDisabled','typeNumber','applyDescDisabled'],
	data (){
	    var self=this;
		return {
		    indexNumber:1,
        goodsObj:{},
        selectDatas:'',//多选数据
        pmm_input_textarea:'pmm_input_textarea',
        userType:""
		}
	},
	methods: {
    checkIsSUPPLY_ASSISTANT (cb){
      this.ajax.postStream('/user-web/api/userPerson/isValidSalesmanType', {
        'personId': this.getEmployeeInfo('personId'),
        'salesmanType': 'SUPPLY_ASSISTANT',
      }, res => {
        if(res.body.content) cb()
        else this.$message.error(res.body.msg || '您不是补件文员角色，没有权限操作')
      })
    },


    autoMakeApplyDesc (row){
      return (
        // 下面这个判断只对新增或已经按照模板生成的进行自动生成，对以前的数据不进行自动生成
        !row.apply_desc || /^(补发:.+，)?(商品规格:.+，)?(物料名称:.+，)?(物料规格:.+，)?数量:\d+(\.\d+)?$/.test(row.apply_desc)
        ? (
          row.apply_desc =
            (row.question_goods_name ? '补发:' + row.question_goods_name + ' ，' : '') +
            (row.question_goods_desc ? '商品规格:' + row.question_goods_desc + ' ，' : '') +
            (row.material_name       ? '物料名称:' + row.material_name + ' ，' : '') +
            (row.description         ? '物料规格:' + row.description + ' ，' : '') +
            ('数量:' + (Math.abs(row.number) || 0))
        )
        : row.apply_desc
      )
    },
	    //得到一个新的goodsObj
    getNewGoodsObj(){
        var obj={
          after_plan_id:null,
          after_ticket_id:null,
        question_goods_code:null,//商品编码
          question_goods_name:null,//商品名称
          question_goods_desc:null,//规格描述
          material_id:null,//物料Id
          material_code:null,//物料编码
          material_name:null,//物料名称
          description:null,//补件规格描述
          number:null,//补件数量
          units:null,//物料单位
          apply_desc:null,//申请需求描述
          buyer:null,
          buyer_name:null,//采购员
          supplier_company:null,//供应商
          supplier_company_id:null,//供应商id
          if_company_send:'N',//工厂代发
          if_sale_out_stock_audit:'N',
          recharge_bill_no:'N',
          if_packaging:null,//是否包装
          //if_install:null,//是否安装
          has_stock:null,//可用库存  //待定  需要删除
          if_lock_stock:'N',//锁库
          //test_price:null,//参考价 //待定
          if_bought:'N',//已采购
          if_pushed:'N',//已入库
          if_pulled:'N',//已出库
          if_sign: 'N', // 已签收
          sign_time: null, // 签收时间
         // pushed_time:null,//已入库日期 //待定
          goods_branch_no:null,//商品批号
          common_supply:null,//通用补件
          bom_version:null,//商品BOM版本
          if_stop_produce:null,//是否停产
          remark:null,//备注
          closer_name:null,//行关闭人员
          closer:null,
         // closer_name:null,
          //close_time:null,//行关闭日期
         // shop:null,//收入店铺 //待定
          close_remark:null,//关闭备注
          line_close_type:null,
          warehouse_no:null,//出库单号
          status:null,//行状态
          //type:null,//来源类型
         // after_plan_no:null//来源单号
          create_time:null,
          creator:null,
          creator_name:null,
          customer_fee:null,
          entry_bom_version:null,
          id:null,
          //ids:null,
          if_goods_question:null,
          last_modifier:null,
          last_modifier_name:null,
          last_modify_time:null,
          purchase_warehouse_no:null,//采购订单号
          purchase_demand_code: null, // 采购需求单号
          question_description:null,
          question_goods_id:null,
          sqd_remark:null,
          supply_code:null,
          //supply_company:null,
          supplier_company:null,
          supplier_company_id:null,
          supply_request:null,
          if_sqdadd:'Y',
          service_bill_no:null,
          incom_shop_id:null,
          incom_shop_name:null
        }
        return obj;
    },
    /**
    *改变供应商
    ***/
    changeSupplier(row){
      console.log('rowasdfasdfsdf',row);
      if(this.isNotCanSave) return;
      this.getSupplierByName(row);
    },
    /**
    *根据供应商名称精确查找供应商信息
    ***/
    getSupplierByName(data){
      let name = data.supplier_company;
      this.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/getNewSupplierByName',{name:name},(res)=>{
        let d = res.body;
        let msg = !d.result?d.msg:!d.content?'没有对应的供应商信息':'';
        data.supplier_company_id = (d.content || {}).supplier_id || '';
        if(msg){
          this.$message.error(msg);
        }

      })
    },
    //新增行
    addRow(){
      var self=this;
      self.goodsInfoList.push(self.getNewGoodsObj());
    },
    //选择供应商
    choseSupplier(row){
      console.log('看看是什么东西叫芝加哥艺术大师 靣回去');
      if(this.isNotCanSave) return;
      let params={};
      params.callback = (d)=>{
        var data = d.data;
        row.supplier_company_id=data.supplier_id;
        row.supplier_company=data.name;
      }
      this.$root.eventHandle.$emit('alert',{
        params:params,
        component:()=>import('@components/after_sales_supply/supplier_2'),
        style:'width:80%;height:80%',
        title:'选择供应商'
      });
    },
    /**
    *判断物料是否可采购
    ***/
    judgeCanPurchase(data,resolve){
      let materialCodeList = [];
      if(!data) return;
      let code = data.bom_material_num || data.materialNumber;
        materialCodeList.push(code);
      if(!materialCodeList.length)return;

      let canNotPurchase = '';
      this.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/getIsPurchaseInfo',materialCodeList,(res)=>{
        let d = res.body;
        if(!d.result) {
          this.$message.error(d.msg);
          return;
        }
        /*for(var key in d.content){
          d.content[key]=0;
        }*/
        if(d.content[code]==0){
           this.$message.error(code+'不能进行采购');
           return;
        }

        resolve && resolve();
      })
    },


    //选择物料
    choseMaterialCode(row){
        if(this.isNotCanSave)return;
        let params={};
        params.isSupply = true;
      params.callback = (d)=>{
        var data = d.data;
        new Promise((resolve)=>{
            this.judgeCanPurchase(data,resolve);
        }).then(()=>{
            row.material_id=data.sourceMaterialId || data.bom_material_id;
            row.material_code=data.materialNumber||data.bom_material_num;
            row.material_name=data.materialName || data.bom_material_name;
            row.description=data.materialSpecification||data.bom_material_desc;
            row.units=data.materialUnit;
            row.common_supply=data.isAdditional;
            row.incom_shop_name=null;
            row.if_stop_produce = data.isStop?'Y':'N';
        })


        /*row.material_id=data.sourceMaterialId || data.bom_material_id;
        row.material_code=data.materialNumber||data.bom_material_num;
        row.material_name=data.materialName || data.bom_material_name;
        row.description=data.materialSpecification||data.bom_material_desc;
        row.units=data.materialUnit;
        row.common_supply=data.isAdditional;
        row.incom_shop_name=null;
        row.if_stop_produce = data.isStop?'Y':'N';*/
      }
      this.$root.eventHandle.$emit('alert',{
        params:params,
        component:()=>import('@components/after_sales_supply/bomgoods'),
        style:'width:80%;height:80%',
        title:'选择物料'
      });
    },
		handleSelectionChange (selects){
      this.selectDatas=selects;
		},
    //得到行id
    getSelectIds(key){
		    var self=this;
		    var ids=[];
      if(self.selectDatas&&self.selectDatas.length>0){
        for(var i=0;i<self.selectDatas.length;i++){
            if(self.selectDatas[i][key]){
              ids.push(self.selectDatas[i][key]);
            }
        }
      }
      return ids;
    },
    // 经销商时去掉接口permissionCode权限控制
    _delPermissionCodeWhenDealerUser (api){
      return api
      // return this.personBusinessAttribute.attributeValue ? api.replace(/\?permissionCode=.+/, '') : api
    },

    //删除行
    dealRow(){
		    var self=this;
		    if(!self.selectDatas || self.selectDatas.length<1){
          this.$message.error('请至少选择一行数据');
		        return;
        }
        var ids=[];
		    for(var i=0;i<self.selectDatas.length;i++){
		        var item=self.selectDatas[i];
		        if(item.id){
		            ids.push(item.id);
            }
		        for(var j=0;j<self.goodsInfoList.length;j++){
		            if(self.goodsInfoList[j]==item){
                  self.goodsInfoList.splice(j,1);
		                break;
                }
          }
        }
        //数据库删除
      if(ids && ids.length>0){
        self.sqdDelete(ids)
      }
    },
    //行删除
    sqdDelete(ids){
      var self=this;
      if(ids && ids.length<1){
        //self.$message.error("请至少选择一条数据");
        return;
      }
      var data={ids:ids};
      self.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/sqdDelete',data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.$emit("reflushList",{});
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    //得到锁库数据
    getLockStockData(){
        var self= this;
        var data=[];
        for(var i=0;i<self.selectDatas.length;i++){
            if(self.selectDatas[i].if_lock_stock == 'Y' ) continue;
            var obj={
              material_id:self.selectDatas[i].material_id,
              number:self.selectDatas[i].number,
              id:self.selectDatas[i].id,
              if_company_send:self.selectDatas[i].if_company_send,
              after_ticket_id:self.selectDatas[i].after_ticket_id
            }
          data.push(obj);
        }
        return data;
    },

    getSupplyCost(){
      var self=this;
      var ids=self.getSelectIds('id');
      if(ids && ids.length<1){
        self.$message.error("请至少选择一条数据");
        return;
      }
      if(ids.length!=self.selectDatas.length){
        self.$message.error("所选数据有未保存的,请先保存数据");
        return;
      }
      let d = self.selectDatas.map(item=>{return{id:item.id}});

      if(!d.length){
          self.$message.error("所选数据已锁库，无需再进行操作");
          return;
      }
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/getSupplyCost'),d,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.$emit("reflushList",{});
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
     pushRechargeBill(){
      var self=this;
      var ids=self.getSelectIds('id');
      if(ids && ids.length<1){
        self.$message.error("请至少选择一条数据");
        return;
      }
      if(ids.length!=self.selectDatas.length){
        self.$message.error("所选数据有未保存的,请先保存数据");
        return;
      }
      let d = self.selectDatas.map(item=>{return {id:item.id,if_company_send:item.if_company_send,supplier_company_id:item.supplier_company_id}});
      if(!d.length){
          self.$message.error("所选数据已锁库，无需再进行操作");
          return;
      }
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/pushRechargeBill'),{
        ticketSupply:{
        after_ticket_no:self.formInfo.after_ticket_no,
        id:self.formInfo.id,
        },
        listPlanSupplyComponent:d
      },function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.$emit("reflushList",{});
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    //锁库
    lockStock(){
      var self=this;
      var ids=self.getSelectIds('id');
      if(ids && ids.length<1){
        self.$message.error("请至少选择一条数据");
        return;
      }
      if(ids.length!=self.selectDatas.length){
        self.$message.error("所选数据有未保存的,请先保存数据");
        return;
      }
      let d = this.getLockStockData();
      if(!d.length){
          self.$message.error("所选数据已锁库，无需再进行操作");
          return;
      }
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/lockStock?permissionCode=SUPPLY_APPLY_LOCK_STOCK'),d,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.$emit("reflushList",{});
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    //反锁库 解锁
    unLockStock(){
      var self=this;
      var ids=self.getSelectIds('id');
      if(ids && ids.length<1){
        self.$message.error("请至少选择一条数据");
        return;
      }
      if(ids.length!=self.selectDatas.length){
        self.$message.error("所选数据有未保存的,请先保存数据");
        return;
      }
      let data = [];
      for(let a = 0; a< this.selectDatas.length;a ++){
        let f = this.selectDatas[a];
        if(f.if_lock_stock == 'N' ) continue;
        data.push(f.id);
      }
      if(!data.length){
        this.$message.error("所选数据都已释放库存,无需再进行操作");
        return;
      }

      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/unLockStock?permissionCode=SUPPLY_APPLY_LOCK_STOCK_BACK'),data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.$emit("reflushList",{});
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },

    /**
    *获取终止行的数据
    *如果选择的数据是锁库的，则不让其进行行终止
    ***/
    getTerminationOfData(){
      console.log('lakjsfdklasdjfs');
      let data = [];
      let list = this.selectDatas;
      if(!list || !list.length) {
        this.$message.error('请至少选择一条数据');
        return '';
      }
      let i = list.length,a=0;
      for(;a<i;a++){
        let d = list[a];
        if(!d.id){
          this.$message.error('所选数据有未保存的,请先保存数据');
          return '';
        }

        // if(d.if_lock_stock=='Y'){
        //   this.$message.error('所选有已锁库的行，不能进行行终止哦');
        //   return '';
        // }

        if(d.status == 'CREATE'){
          let g = {
               id: d.id,
               close_remark : d.close_remark,
               line_close_type : d.line_close_type,
            }
            data.push(g);
        }
     }
      if(!data.length){
        this.$message.error('所选数据都已终止，不需要进行终止');
        return '';
      }
      return data;



    },
    //行终止
    lineTerminate(){//行终止的条件是未锁库
     /* var self=this;
      var ids=self.getSelectIds('id');
      if(ids && ids.length<1){
        self.$message.error("请至少选择一条数据");
        return;
      }
      if(ids.length!=self.selectDatas.length){
        self.$message.error("所选数据有未保存的,请先保存数据");
        return;
      }*/
      //接口传参
      //var data={ids:ids};
      //CREATE: '未关闭', CLOSE: '已关闭'
     /* var data = [];
      this.selectDatas.map((n,f)=>{
        if(n.status == 'CREATE'){
            let g = {
               id: n.id,
               close_remark : n.close_remark
            }
            data.push(g);
        }

      });
      if(!data.length){
        //
        this.$message.error('所选数据都已终止，不需要进行终止');
        return;
      }*/
      let data = this.getTerminationOfData();
      if(!data || !data.length) return;
      this.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/lineTerminate?permissionCode=SUPPLY_APPLY_STOP_ROW'),data,(response)=>{
        if(response.body.result){
          this.$message.success("操作成功");
          this.$emit("reflushList",{});
        }else{
          this.$message.error(response.body.msg)
        }
      },e=>{
        this.$message.error(e)
      })
    },
    //行反终止
    lineUnTerminate(){
      var self=this;
      var ids=self.getSelectIds('id');
      if(ids && ids.length<1){
        self.$message.error("请至少选择一条数据");
        return;
      }
      if(ids.length!=self.selectDatas.length){
        self.$message.error("所选数据有未保存的,请先保存数据");
        return;
      }
      let flag = false;

      //var data={ids:ids};
      var data = [];
      //CREATE: '未关闭', CLOSE: '已关闭'
      this.selectDatas.map((n,f)=>{
        if(n.if_sale_out_stock_audit == 'Y'){
          flag = true;
        }
        if(n.status == 'CLOSE'){
          let g = {
             id: n.id,
             close_remark : n.close_remark
          }
          data.push(g);
        }

      });
      if(flag){
          this.$message.error('存在是否销售出库已审核为是，行反终止不能操作');
         return;
      }
      if(!data.length){
        //
        this.$message.error('所选数据都未关闭，不需要进行返关闭');
        return;
      }
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/lineUnTerminate?permissionCode=SUPPLY_APPLY_STOP_ROW_BACK'),data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.$emit("reflushList",{});
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },

    //得到行id
    getSelectRefreshUsed(){
      var self=this;
      var ids=[];
      console.log(self.selectDatas);
      if(self.selectDatas&&self.selectDatas.length>0){
        for(var i=0;i<self.selectDatas.length;i++){
          if(self.selectDatas[i]['material_id']){
              var obj={material_id:self.selectDatas[i]['material_id'],if_company_send:self.selectDatas[i]['if_company_send'],material_code:self.selectDatas[i]['material_code']}
            ids.push(obj);

          }
        }
      }
      return ids;
    },
    //获取库存可用量
  getRefreshUsed(){
    var self=this;
    var ids=self.getSelectRefreshUsed();
    if(ids && ids.length<1){
      self.$message.error("请至少选择一条数据");
      return;
    }
    var data=ids;
    self.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/getRefreshUsed',data,function(response){
      if(response.body.result){
         if(response.body.content){
            self.$message.success("操作成功");
            self.setGoodsStok(response.body.content)
        }

      }else{
        self.$message.error(response.body.msg)
      }
    },e=>{
      self.$message.error(e)
    })
  },
    //刷新获取可用库存数据
    setGoodsStok(data){
      var self=this;
      for(var key in data){
        for(var n=0;n<this.selectDatas.length;n++){
            var obj=this.selectDatas[n];
              console.log(key,this.selectDatas,2)
            if(key==obj.material_code){
              console.log(key,this.selectDatas,2)
              obj['has_stock']=data[key]||0;
            }
          }
        }
    },
    ifPurchaseDealerOfProxyId() {
      this.ajax.get('/user-web/api/userPerson/getUserPerson/' + this.getEmployeeInfo('personId'), res => {
        if(res.body.result){
          this.userType=res.body.content.type
        }
      })
    }

	},
  watch:{
    'goodsInfoList':function(newval,oldval){
      this.$emit("getGoodsList",this.goodsInfoList);
    },
    'isAdd':function(newval,oldval){
        self.isAdd=newval;
    },
    'selectDatas':function(newval,oldval){
      this.$emit("getSelectGoodsList",this.selectDatas);
    },
    /*,
    'isSqdAdd':function(newval,oldval){
      self.isSqdAdd=newval;
    }*/
  },
  computed: {
    // 是否为定制补件类型
    isDZBJ() {
      return this.formInfo.type === 'DZBJ'
    },
    // 针对补件类型=定制补件，且当前登录用户的类型=采购经销客户，则屏蔽显示出字段“供应商”和“补件采购成本”
    rowDisabled(){
      return (this.formInfo.type == 'DZBJ'&&this.userType =='PURCHASE_DEALER')
    },
    canTerminate() {
      let i = this.selectDatas.length,
        hasPushed = false;
      if(i) {
        while(i--) {
          if(this.selectDatas[i].if_pushed == 'Y') {
            hasPushed = true;
            break;
          }
        }
        return hasPushed;
      } else {
        return true;
      }
    }
  },
  created () {
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
		this.ifPurchaseDealerOfProxyId()
	},
	beforeDestroy(){
		//解除监听切换业务代理事件
		this.$root.eventHandle.$off('resetAllBtnStatus',this.ifPurchaseDealerOfProxyId);
	},
}
</script>
<style type="text/css">
  .pmm_input_textarea{height: auto}
  .el-table .el-table__body-wrapper td.pmm_input_textarea .cell, .el-table .el-table__fixed-body-wrapper td.pmm_input_textarea .cell{height: 100%;}
</style>
