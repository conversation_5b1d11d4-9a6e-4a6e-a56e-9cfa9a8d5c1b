<!-- 补件申请单列表 -->
<template>
  <xpt-list-dynamic
    :data='roleList'
    :colData='colData'
    :pageTotal='pageTotal'
    :btns='btns'
    :showSelectRowNumByOnlyKey="!!firstData.after_ticket_no"
    :searchPage='searchObj.page_name'
		:showCount ='showCount'
		@count-off="countOff"
    @row-dblclick="dbclick"
    @search-click='searchClick'
    @selection-change='select'
    @page-size-change='pageChange'
    @current-page-change='currentPageChange' ref='xptList'>
  </xpt-list-dynamic>
</template>
<script>
  export default {
    props:['params'],
    data (){
        var self=this;
      return {
        countOffFlag:false,
			  showCount:false,
        searchObj: {
          page:{
            length: self.pageSize,
            pageNo: 1,
          },
          page_name: 'aftersale_bill_supply',
          where:[]
        },
        roleList:[],
        typeNumber:2,//1为补件文员专用，2为业务人员专用
        // 已选行Id
        selectRole:[],
        selectIds: [], // 选择了的ID列表
        pageNow:1,
        pageTotal:0,
        typeActive:true,
        // pageSize:10,
        searchString:'', // 搜索条件
        statusList:{
          CREATE: '创建',
          APPROVING: '审核中',
          APPROVED: '已审核',
          COMMIT: '提交',
          SUBMITPURCHASE:'提交采购',
          LOCK:'锁定',
          REJECT:'驳回',
          UNLOCK:'解锁',
          CLOSE:'关闭'
        },
        typeList:{SHBJSQD:'售后补件申请单',ZTBJSQD:'展厅补件',NXBJSQD:'内销补件',BCBJSQD:'摆场补件',RESALE: '再售补件', BYWJBJ : '备用五金补件',XSJGSQD:'销售加购申请单',DZBJ:'定制补件',HWBJ: "海外补件",},
        isOrNot:{Y:'是',N:'否'},
        businessStatusList:{SALEMANHANDLING:"业务处理中",CLERKHANDLING:"文员处理中",SUBMITPURCHASE:"提交采购",CLERKDEALTING:"文员待办",REJECT:"驳回"},
        colData: [
          {
              label: '销售标签666',
              prop: 'sales_label',
              width: 70,
              html(val) {
                  return val ? '<span style="color:red;">'+val+'</span>' : val;
              },
          },
          {
            label: '商品批号',
            prop: 'goods_branch_no',
            width:120
          },
          {
            label: '定制补单号',
            prop: 'client_number',
            width:120
          },
          {
            label: '供应商',
            prop: 'supplier_company',
            width:120
          },
          {
            label: '单据编号',
            prop: 'afterTicketNo',
            width:150,
            redirectClick (d){
              self.mod(d.id)
            }
          },
          {
            label: '是否经销商订单',
            width:'100',
            prop: 'if_dealer',
            formatter: prop => ({
              Y: '是',
              N: '否',
            }[prop] || prop),
          },
          {
            label: '经销商名称',
            width:100,
            prop: 'dealer_customer_name'
          },{
            label: '买家昵称',
            width:100,
            prop: 'buyerName'
          },
          {
            label: '收货人',
            width: 100,
            prop: 'receiverName'
          },
          {
            label: '物料编码',
            width:150,
            prop: 'material_code'
          },
          {
            label: '物料名称',
            width:150,
            prop: 'material_name'
          },
          {
            label: '申请需求描述',
            width: 150,
            prop: 'apply_desc'
          },
          {
            label: '补件数量',
            width:100,
            prop: 'number'
          },
          {
            label: '补件规格描述',
            width: 200,
            prop: 'description'
          },
          {
            label: '行状态',
            prop: 'line_status',
            width:80,
            formatter:function(val){

              return val=='CLOSE'?'已关闭':'未关闭';
              //return row.line_status=='N'?'未关闭':'已关闭';
            }
          },
          {
            label: '关闭类型',
            width:120,
            prop: 'close_type',
            formatter:function(val){

            return {LOST_ITEM_FOUND:'丢件找到',STOP_PRODUCTION:'停产无法补件',OTHER:'其它',PLACE_WRONG_ORDER:'下错单',NO_NEED_TO_HANDLE:'不需要（协商，赔偿，维修）处理'}[val]
              //return row.line_status=='N'?'未关闭':'已关闭';
            }
          },
          {
            label: '关闭备注',
            width:120,
            prop: 'close_remark'
          },
          {
            label: '提交采购人',
            prop: 'submit_purchase_name',
          },
          {
            label: '提交采购时间',
            format:"dataFormat1",
            prop: 'submit_purchase_time',
            width:150
          },
          {
            label: '锁库',
            width:80,
            prop: 'if_lock_stock',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },
          {
            label: '是否采购',
            width:80,
            prop: 'if_bought',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否入库',
            width:80,
            prop: 'if_pushed',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否出库',
            prop: 'if_pulled',
            width:80,
            formatter:function(row){
              return self.isOrNot[row]
            }
          },
          {
            label: '出库日期',
            width:150,
            prop: 'warehouse_date',
            format:"dataFormat1"
          },
          {
            label: '业务状态',
            width:100,
            prop: 'businessStatus',
            formatter:function(row){
              return self.businessStatusList[row];
            }
          },
          {
            label: '单据状态',
            width:80,
            prop: 'billStatus',
            formatter:function(row){
              return self.statusList[row];
            }
          },
          {
            label: '出库单号',
            prop: 'warehouse_no',
            width:120
          },
          {
            label: '采购单号',
            prop: 'purchase_warehouse_no',
            width:120
          },
          {
            label: '文员锁定人名称',
            prop: 'clerk_lock_name',
          },
          {
            label: '文员锁定人时间',
            prop: 'clerk_lock_time',
            format:"dataFormat1",
            width:150
          },
          {
            label: '店铺地区',
            format: 'auxFormat',
            prop: 'shop_area',
            formatParams: 'shopArea'
          },{
            label: '补件类型',
            prop: 'billType',
            width:100,
            formatter:function(row){
              return self.typeList[row]||row;
            }
          },{
            label: '驳回日期',
            width:150,
            prop: 'reject_time',
            format:"dataFormat1"
          },{
            label: '创建人',
            width:80,
            prop: 'creatorName'
          },{
            label: '创建日期',
            width:150,
            prop: 'createTime',
            format:"dataFormat1"
          },{
            label: '售后单号',
            width:120,
            prop: 'afterOrderNo'
          },{
            label: '业务员',
            prop: 'salesmanName'
          },{
            label: '业务员分组',
            prop: 'salesmanGroup'
          },{
            label: '是否上门安装',
            prop: 'ifDoorInstall',
            width: 100,
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否整单锁库',
            prop: 'ifAllLock',
            width: 100,
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '物料单位',
            width:100,
            prop: 'units'
          },{
            label: '采购员',
            prop: 'buyer_name1',
          },{
            label: '工厂代发',
            prop: 'if_company_send',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否包装',
            prop: 'if_packaging',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否安装',
            prop: 'if_install',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '参考价',
            prop: 'supply_code'
          },{
            label: '通用补件',
            prop: 'common_supply',
            formatter:function(row){
              return { 1: '是', 0: '否' }[row];
            }
          },{
            label: '是否停产',
            prop: 'if_stop_produce',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '业务锁定人',
            width:100,
            prop: 'business_man_name'
          },{
            label: '业务锁定人分组',
            width:100,
            prop: 'business_man_group'
          },{
            label: '业务锁定时间',
            width:150,
            format:"dataFormat1",
            prop: 'business_time'
          },{
            label: '是否多次补件',
            prop: 'if_multiple_patch',
            formatter:function(row){
              return { Y: '是', N: '否' }[row];
            }
          },{
            prop:"recharge_bill_no",
            label:"货款充值单号"
            },
          {
            prop:"recharge_status",
            label:"货款充值状态",
						formatter(val){
							return{
								'A':'无需扣款',
								'B':'未扣款',
								'C':'扣款中',
								'D':'已扣款',
								'PAY_UNNEEDED':'无需充值',
								'PAY_NOT':'未充值',
								'PAY_EXEC':'充值中',
								'PAY_FINISH':'已充值'
							}[val]||val
						}
          },
          {
            prop: 'income_shop_name',
            label: '收入店铺',
            width: 140
          },
        ],
        btns: [{
            type:'primary',
            txt:'新增',
            disabled(){
              return self.typeActive
            },// 采购经销商和返点经销时禁掉新增按钮
            click(){
                self.add();
            }
          },{
            type:'success',
            txt:'刷新',
            _type: '1|2|3|4',
            click(){
                self.searchFun();
            }
          },
          /*{
            type:'primary',
            txt:'锁定',
            click(){
              self.batchLock();
            }
          },*/
          // {
          //   type:'primary',
          //   txt:'切换文员专用',
          //   typeBtn:true,
          //   click(){
          //       self.changeCloDataByType();
          //   }
          // },
          {
            type:'primary',
            txt:'导出',
            click(){
              self.exportBillSupplyList()
            }
          }

        ],
        firstData:{}
      }
    },
    methods: {
      exportBillSupplyList (){
        if(!this.searchObj.where.length){
          this.$message.error('导出功能至少要有一个过滤条件')
          return
        }

        var postData = JSON.parse(JSON.stringify(this.searchObj))
        delete postData.page
        this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportBillSupplyList', postData, d => {
          this.$message({
            type: d.body.result ? 'success' : 'error',
            message: d.body.msg,
          })
        })
      },
      /**
      *锁定
      ***/
      batchLock(){
        let data = this.getIds();
        let matchData = data.data;
        let ids = data.lockIds;
        if(!ids || !ids.length) return;
        let length=0,errorMsg='',successMsg='';
        let _this = this;
        ids.map((a,b)=>{
          this.ajax.postStream(
            this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/lock?permissionCode=SUPPLY_APPLY_LOCK'),
            {id:a},
            (response)=>{
              if(!response.body.result){
                errorMsg += (matchData[a]+response.body.msg+',');
              }else{
                successMsg += (matchData[a]+',')
              }
              length++;
              /*if(length != ids.length) return;
              if(errorMsg){
                this.$message.error(errorMsg);
              }
              if(successMsg){
                this.$message.success(successMsg);
              }
              this.searchFun();*/

              callback(errorMsg,successMsg,_this)
            },e=>{
              length++;
              this.$message.error(e);
              callback(errorMsg,successMsg,_this)
            })
          }
        )
        function callback(errorMsg,successMsg,_this){
          if(length != ids.length) return;
          if(errorMsg){
            _this.$message.error(errorMsg);
          }
          if(successMsg){
            _this.$message.success(successMsg+'锁定成功');
          }
          _this.searchFun();
        }

      },
      /***
      *获取补件单ID
      **/
      getIds(){
        let lockedErrorMsg,canNotLockErrorMsg,lockIds=[];
        let list = this.selectRole,data={};
        if(!list || !list.length){
          this.$message.error('请选择要锁定的单据');
          return '';
        }

        list.map((a,b)=>{
          if(a.status == 'CREATE' && a.business_status == 'CLERKDEALTING'){
            lockIds.push(a.id);
            data[a.id] = a.after_ticket_no;
          }
        })
        if(!lockIds.length){
          this.$message.error('单据状态为创建，业务状态为文员代办的单据才可以锁定哦');
        }
        lockIds = Array.from(new Set(lockIds));
        return {
          lockIds:lockIds,
          data:data
        }
      },
      /**
      *切换列表数据
      ***/
      changeCloDataByType(){
        this.typeNumber = this.typeNumber == 1?2:1;
        this.btns.map((a,b)=>{
          if(a.typeBtn){
            a.txt = this.typeNumber == 1?'切换业务员专用':'切换文员专用'
          }
        });
        this.setColData();
      },
      /**
      *双击列表
      **/
      dbclick(row){
        this.mod(row.id);
        console.log('row',row);
      },
      /**
      *设置列表字段顺序
      ***/
      setColData(){
        this.colData = this.getColData();
      },
      /**
      *根据typeNumber类型去判断是要业务列表字段还是文员列表字段
      *typeNumber=1为文员列表字段，2为业务员列表字段
      **/
      getColData(){
        let self = this;
        let clerkData = [
          {
            label: '商品批号',
            prop: 'goods_branch_no',
            width:120
          },
          {
            label: '供应商',
            prop: 'supplier_company',
            width:120
          },
          {
            label: '单据编号',
            prop: 'afterTicketNo',
            width:150,
            redirectClick (d){
              self.mod(d.id)
            }
          },
          {
            label: '定制补单号',
            prop: 'client_number',
            width:120
          },
          {
            label: '是否经销商订单',
            width:'100',
            prop: 'if_dealer',
            formatter: prop => ({
              Y: '是',
              N: '否',
            }[prop] || prop),
          },
          {
            label: '经销商名称',
            width:100,
            prop: 'dealer_customer_name'
          },{
            label: '买家昵称',
             width:100,
            prop: 'buyerName'
          },
          {
            label: '收货人',
            width: 100,
            prop: 'receiverName'
          },
          {
            label: '物料编码',
            width:150,
            prop: 'material_code'
          },
          {
            label: '物料名称',
            width:150,
            prop: 'material_name'
          },
          {
            label: '申请需求描述',
            width: 150,
            prop: 'apply_desc'
          },
          {
            label: '补件数量',
            width:100,
            prop: 'number'
          },
          {
            label: '补件规格描述',
            width: 200,
            prop: 'description'
          },
          {
            label: '行状态',
            prop: 'line_status',
            width:80,
            formatter:function(val){

              return val=='CLOSE'?'已关闭':'未关闭';
              //return row.line_status=='N'?'未关闭':'已关闭';
            }
          },
          {
            label: '关闭备注',
            width:120,
            prop: 'close_remark'
          },
          {
            label: '提交采购人',
            prop: 'submit_purchase_name',
          },
          {
            label: '提交采购时间',
            format:"dataFormat1",
            prop: 'submit_purchase_time',
            width:150
          },
          {
            label: '锁库',
            width:80,
            prop: 'if_lock_stock',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },
          {
            label: '是否采购',
            width:80,
            prop: 'if_bought',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否入库',
            width:80,
            prop: 'if_pushed',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否出库',
            prop: 'if_pulled',
            width:80,
            formatter:function(row){
              return self.isOrNot[row]
            }
          },
          {
            label: '出库日期',
            width:150,
            prop: 'warehouse_date',
            format:"dataFormat1"
          },
          {
            label: '业务状态',
            width:100,
            prop: 'businessStatus',
            formatter:function(row){
              return self.businessStatusList[row];
            }
          },
          {
            label: '单据状态',
            width:80,
            prop: 'billStatus',
            formatter:function(row){
              return self.statusList[row];
            }
          },
          {
            label: '出库单号',
            prop: 'warehouse_no',
            width:120
          },
          {
            label: '采购单号',
            prop: 'purchase_warehouse_no',
            width:120
          },

          {
            label: '文员锁定人名称',
            prop: 'clerk_lock_name',
          },
          {
            label: '文员锁定人时间',
            prop: 'clerk_lock_time',
            format:"dataFormat1",
            width:150
          },


          /*{
            label: '业务状态',
            prop: 'businessStatus',
            formatter:function(row){
              return self.businessStatusList[row];
            }
          },
          {
            label: '已入库日期',
            width:150,
            prop: 'pushed_date',
            format:"dataFormat1"

          },
          {
            label: '驳回日期',
            width:150,
            prop: 'reject_time',
            format:"dataFormat1"
          },{
            label: '采购员',
            prop: 'buyer_name1',
          },{
            label: '工厂代发',
            prop: 'if_company_send',
            formatter:function(row){
                return self.isOrNot[row]
            }
          },{
            label: '是否包装',
            prop: 'if_packaging',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否安装',
            prop: 'if_install',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '通用补件',
            prop: 'common_supply',
            formatter:function(row){
              return { 1: '是', 0: '否' }[row];
            }
          },{
            label: '是否停产',
            prop: 'if_stop_produce',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },
          {
            label: '补件类型',
            prop: 'billType',
            width:100,
            formatter:function(row){
              return self.typeList[row];
            }
          },
          {
            label: '参考价',
            prop: 'supply_code'
          },
          {
            label: '单据状态',
            prop: 'status',
            formatter:function(row){
              return self.statusList[row];
            }
          },
          {
            label: '创建人',
            width:80,
            prop: 'creatorName'
          },{
            label: '创建日期',
            width:150,
            prop: 'createTime',
            format:"dataFormat1"
          },{
            label: '售后单号',
            width:120,
            prop: 'afterOrderNo'
          },{
            label: '业务员',
            prop: 'salesmanName'
          },{
            label: '业务员分组',
            prop: 'salesmanGroup'
          },{
            label: '是否上门安装',
            prop: 'ifDoorInstall',
            width: 100,
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否整单锁库',
            prop: 'ifAllLock',
            width: 100,
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '物料单位',
            width:100,
            prop: 'units'
          }*/
        ];
        //业务
        let businessData = [
          {
            label: '单据编号',
            prop: 'afterTicketNo',
            width:130,
            redirectClick (d){
              self.mod(d.id)
            }
          },{
            label: '单据状态',
            prop: 'billStatus',
            formatter:function(row){
              return self.statusList[row];
            }
          },{
            label: '业务状态',
            prop: 'businessStatus',
            formatter:function(row){
              return self.businessStatusList[row];
            }
          },{
            label: '定制补单号',
            prop: 'client_number',
            width:120
          },{
            label: '是否经销商订单',
            width:'100',
            prop: 'if_dealer',
            formatter: prop => ({
              Y: '是',
              N: '否',
            }[prop] || prop),
          },{
            label: '经销商名称',
            width:100,
            prop: 'dealer_customer_name'
          },{
					label: '店铺地区',
					format: 'auxFormat',
					prop: 'shop_area',
					formatParams: 'shopArea'
				},{
            label: '补件类型',
            prop: 'billType',
            width:100,
            formatter:function(row){
              return self.typeList[row];
            }
          },{
            label: '买家昵称',
             width:150,
            prop: 'buyerName'
          },{
            label: '驳回日期',
            width:150,
            prop: 'reject_time',
            format:"dataFormat1"
          },{
            label: '物料名称',
            width:150,
            prop: 'material_name'
          },{
            label: '补件规格描述',
            width: 150,
            prop: 'description'
          },{
            label: '收货人',
            width: 100,
            prop: 'receiverName'
          },{
            label: '申请需求描述',
            width: 100,
            prop: 'apply_desc'
          },{
            label: '供应商',
            width:150,
            prop: 'supplier_company'
          },{
            label: '创建人',
            width:80,
            prop: 'creatorName'
          },{
            label: '创建日期',
            width:150,
            prop: 'createTime',
            format:"dataFormat1"
          },{
            label: '提交采购人',
            prop: 'submit_purchase_name'
          },{
            label: '提交采购时间',
            width:150,
            prop: 'submit_purchase_time',
            format:"dataFormat1"
          },{
            label: '售后单号',
            width:120,
            prop: 'afterOrderNo'
          },{
            label: '业务员',
            prop: 'salesmanName'
          },{
            label: '业务员分组',
            prop: 'salesmanGroup'
          },{
            label: '是否上门安装',
            prop: 'ifDoorInstall',
            width: 100,
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否整单锁库',
            prop: 'ifAllLock',
            width: 100,
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '物料编码',
            width:150,
            prop: 'material_code'
          },{
            label: '补件数量',
            prop: 'number'
          },{
            label: '物料单位',
            width:100,
            prop: 'units'
          },{
            label: '采购员',
            prop: 'buyer_name1',
          },{
            label: '工厂代发',
            prop: 'if_company_send',
            formatter:function(row){
                return self.isOrNot[row]
            }
          },{
            label: '是否包装',
            prop: 'if_packaging',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否安装',
            prop: 'if_install',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '锁库',
            prop: 'if_lock_stock',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '参考价',
            prop: 'supply_code'
          },{
            label: '已采购',
            prop: 'if_bought',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '已入库',
            prop: 'if_pushed',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '已出库',
            prop: 'if_pulled',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '出库日期',
            width:150,
            prop: 'warehouse_date',
            format:"dataFormat1"
          },{
            label: '文员锁定人时间',
            prop: 'clerk_lock_time',
            format:"dataFormat1",
            width:180
          }, {
            label: '通用补件',
            prop: 'common_supply',
            formatter:function(row){
              return { 1: '是', 0: '否' }[row];
            }
          },{
            label: '是否停产',
            prop: 'if_stop_produce',
            formatter:function(row){
              return self.isOrNot[row]
            }
          },{
            label: '是否多次补件',
            prop: 'if_multiple_patch',
            formatter:function(row){
              return { Y: '是', N: '否' }[row];
            }
          },{
            label: '文员锁定人名称',
            prop: 'clerk_lock_name',
          },{
            label: '文员锁定人时间',
            prop: 'clerk_lock_time',
            format:"dataFormat1",
            width:180
          }
        ]
        return this.typeNumber==1?clerkData:businessData;
      },

      //进去编辑页面
      mod(id){
        this.$root.eventHandle.$emit('creatTab',
          {name:'补件单详情',params:{id:id,list:this.roleList,typeNumber:this.typeNumber},component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')})
      },
      //新增
      add(){
        this.$root.eventHandle.$emit('creatTab',
          {name:'新增补件单',params:{typeNumber:2},component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')})
      },
      /**
      *同一单据，只显示第一行的表头信息
      ***/
      switchBysameBill(){
        let list = this.roleList;
        if(!list || !list.length) return;
        let i = list.length,a=0;
        let firstData;
        for(;a<i;a++){
          if(!firstData || firstData.after_ticket_no != list[a].after_ticket_no){
            firstData = list[a];
            firstData.afterTicketNo = list[a].after_ticket_no;
            firstData.billStatus = list[a].status;
            firstData.businessStatus = list[a].business_status;
            firstData.billType = list[a].type;
            firstData.buyerName = list[a].buyer_name;
            firstData.receiverName = list[a].receiver_name;
            firstData.creatorName = list[a].creator_name;

            firstData.createTime = list[a].create_time;
            firstData.afterOrderNo = list[a].after_order_no;
            firstData.salesmanName = list[a].salesman_name;
            firstData.salesmanGroup = list[a].salesman_group;

            firstData.ifDoorInstall = list[a].if_door_install;
            firstData.ifAllLock = list[a].if_all_lock;
            continue;
          }

        }
        this.firstData=firstData;
      },
      countOff(){

        let self = this,
        url = "/afterSale-web/api/aftersale/ticketSupply/listCount";
        if(!self.roleList.length){
          self.$message.error("当前列表为空，先搜索内容");
          return;
        }
        if(!!self.countOffFlag){
          self.$message.error("请勿重复点击");
          return;
        }
        self.countOffFlag = true;

        self.ajax.postStream(url,self.searchObj,function(response){
            if(response.body.result){

              self.pageTotal = response.body.content;
              self.showCount = true;
              self.countOffFlag = false;

            }else{
              self.$message.error(response.body.msg);
            }
          });
      },
      searchFun(/*searchString,*/ reslove,reslove2){
          // console.log('看看是什么东西');
          let self=this;
       // self.searchObj.where = searchString||[];
       let url = '/afterSale-web/api/aftersale/ticketSupply/list' + (this.__judgeIsDealerMenu(this.params.menuInfo)?'':'?permissionCode=SUPPLY_APPLY_QUERY');
        this.ajax.postStream(url/*'/afterSale-web/api/aftersale/ticketSupply/list?permissionCode=SUPPLY_APPLY_QUERY'*/,this.searchObj,(response)=>{
          if(response.body.result){
            // this.roleList=response.body.content.list;
            // this.pageTotal=response.body.content.count;
            let dataList = JSON.parse(JSON.stringify(response.body.content.list));
            if(dataList.length == (self.searchObj.page.length+1)&&dataList.length>0){
              dataList.pop();
            }
            self.roleList = dataList;
            let totalCount = self.searchObj.page.pageNo*self.searchObj.page.length;
            if(!self.showCount){
              self.pageTotal = response.body.content.list.length == (self.searchObj.page.length+1)? totalCount+1:totalCount;
            }





            this.switchBysameBill();

          }else{
            this.$message.error(response.body.msg)
          }
          reslove && reslove();
          reslove2&& reslove2();
        },e=>{
          reslove && reslove();
          this.$message.error(e)
        }, this.params.tabName)
      },
      pageChange(pageSize){
        this.searchObj.page.length = pageSize
        this.searchObj.page.pageNo = 1
        this.searchFun()
      },
      currentPageChange(page){
        this.searchObj.page.pageNo = page
        this.searchFun()
      },
      searchClick(where, resolve){
        let self = this;
        this.searchObj.where = where;
        new Promise((res,rej)=>{
				this.searchFun(resolve,res)
			}).then(()=>{
				if(self.searchObj.page.pageNo != 1){
					self.pageTotal = 0;
				}
					self.showCount = false;
			})
      },
      select (s){
        this.selectRole = s;
        this.selectIds = [];
        s.forEach((v) => {
          this.selectIds.push(v.after_order_id);
        })
      },
      // 经销商时去掉接口permissionCode权限控制
      _delPermissionCodeWhenDealerUser (api){
        return api;
        //return this.personBusinessAttribute.attributeValue ? api.replace(/\?permissionCode=.+/, '') : api
      },//http://salesit.linshimuye.com:83/user-web/api/userPerson/getUserPersonBusiness
      checkUserType (){
        this.loading = true
        this.ajax.get('/user-web/api/userPerson/getUserPerson/' + this.getEmployeeInfo('personId'), res => {
          if(res.body.content.type === 'PURCHASE_DEALER'||res.body.content.type === 'DEALER'){
           this.typeActive = true
          }else{
            this.typeActive = false
          }
        })
      },
    },
    mounted (){
      // this.setColData();
      // this.searchFun();
      this.checkUserType ();
    },
  }
</script>
