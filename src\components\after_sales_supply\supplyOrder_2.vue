<!--  补件申请单 -->
<template>
<div class="xpt-flex">
	<el-row style="height:330px;">
	<el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="40">
				<el-button type="primary" size="mini" @click="add" :disabled='typeActive'>新增</el-button>
				<el-button type="success" size="mini" @click="saveEvent" :disabled="saveDisabled || ifDealerOrderCantEdit || ifSave || isDZBJ">保存</el-button>
				<el-button type="primary" size="mini"  @click="reflush">刷新</el-button>
				<el-button type="primary" size="mini" @click="submitBuyerEvent" :disabled="submitPurchaseDisabled || ifDealerOrderCantEdit || isDZBJ">提交采购</el-button>
				<el-button type="danger" size="mini"  @click="reject" :disabled="rejectDisabled || ifDealerOrderCantEdit || isDZBJ">驳回</el-button>
				<el-button type="primary" size="mini" @click="lock" :disabled="lockDisabled || ifDealerOrderCantEdit || isDZBJ" >锁定</el-button>
				<el-button type="primary" size="mini" @click="unlock" :disabled="unlockDisabled || ifDealerOrderCantEdit || isDZBJ">解锁</el-button>
        		<xpt-btngroup  :btngroup='submitOrCancel' :disabled="ifDealerOrderCantEdit || isDZBJ" class="mgl10"></xpt-btngroup>

        		<el-button type="primary" size="mini" @click="audit" :disabled="auditDisabled || ifDealerOrderCantEdit || isDZBJ">审核</el-button>
				<xpt-btngroup  :btngroup='buyerDoBtns' :disabled="ifDealerOrderCantEdit" class="mgl10" ></xpt-btngroup>
        		<xpt-btngroup  :btngroup='buyerDoCancelBtns' :disabled="ifDealerOrderCantEdit" class="mgl10"></xpt-btngroup>
        		<el-button type="danger" size="mini" @click="close" :disabled=" ifDealerOrderCantEdit || isDZBJ">关闭</el-button>
        		<el-button-group>
					<el-button type="primary"   @click='uploadFun'  size="mini" :disabled="saveDisabled || ifDealerOrderCantEdit || isDZBJ">上传图片<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload></el-button>
					<el-button type="primary" @click='pictureFun' size="mini" :disabled="ifDealerOrderCantEdit">查看附件</el-button>
				</el-button-group>

				<el-button type="primary" size="mini" @click="switchBills(0)"  style="float:right;margin-top:2px">下一页</el-button>
				<el-button type="primary" size="mini" @click="switchBills(1)"  style="float:right;margin-right:10px;margin-top:2px">上一页</el-button>
			</el-col>
		</el-row>
		<el-tabs v-model="selectTab1">
			<el-tab-pane label='基本信息' name='basicInformation' >
				<el-row :gutter="20">
					<el-col :span="6">
						<el-form-item label="单据编号">
							<el-input size='mini' v-model="form.after_ticket_no" disabled placeholder="系统自动生成"></el-input>
						</el-form-item>
						<el-form-item label="补件类型" prop="type">
              				<el-select v-model="form.type" size="mini" style="width:150px;" :disabled="inputDisabled">
              					<el-option  label="售后补件申请单" value="SHBJSQD" key="SHBJSQD" ></el-option>
              					<el-option  label="展厅补件" value="ZTBJSQD" key="ZTBJSQD" ></el-option>
              					<el-option  label="内销补件" value="NXBJSQD" key="NXBJSQD" ></el-option>
              					<el-option  label="再售补件" value="RESALE" key="RESALE" disabled></el-option>
								<el-option  label="定制补件" value="DZBJ" key="DZBJ" disabled></el-option>
              					<el-option  label="备用五金补件" value="BYWJBJ" key="BYWJBJ"></el-option>
              					<el-option  label="销售加购申请单" value="XSJGSQD" key="XSJGSQD" disabled></el-option>
              					<el-option  label="海外补件" value="HWBJ" key="HWBJ"></el-option>
              				</el-select>
              				<el-tooltip v-if='rules.type[0].isShow' class="item" effect="dark" :content="rules.type[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
            			</el-form-item>
						<el-form-item label="合并订单号">
							<el-input size='mini' v-model="form.merge_trade_no" readonly  disabled></el-input>
						</el-form-item>
						<el-form-item label="售后单号">
							<el-input size='mini' v-model="form.after_order_no" readonly  disabled></el-input>
						</el-form-item>
						<el-form-item label="结算主体" >
							<el-input size='mini'  disabled :value="form.settle_entity "></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="业务员" prop="salesman_name">
							<el-input v-model="form.salesman_name" size='mini' icon="search" :on-icon-click="selectBestStaff"  :disabled="inputDisabled" readonly></el-input>
							<el-tooltip v-if='rules.salesman_name[0].isShow' class="item" effect="dark" :content="rules.salesman_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="业务员分组">
							<el-input size='mini' v-model="form.salesman_group" disabled></el-input>
						</el-form-item>
						<el-form-item label="业务锁定人名称">
							<el-input size='mini' v-model="form.business_man_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="业务锁定人分组">
							<el-input size='mini' v-model="form.business_man_group"  disabled></el-input>
						</el-form-item>
						<el-form-item label="允许修改货运方式">
							<el-switch v-model="form.if_modify_logistics" on-text="是" off-text="否" on-value="Y" off-value="N" ></el-switch>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="提货点">
							<el-input v-model="form.delivery_name" size='mini'  disabled v-if="addressDisabled" ></el-input>
							<el-input v-model="form.delivery_name" size='mini' icon="search" :on-icon-click="selectDelivery"  v-else readonly></el-input>
						</el-form-item>
						<el-form-item label="三包点">
							<el-input v-model="form.three_name" size='mini'  v-if="addressDisabled" disabled></el-input>
							<xpt-input placeholder="请选择" icon="search" size='mini' :on-icon-click="selectThreeName" v-else v-model="form.three_name" readonly @change="changeThreeName"></xpt-input>
						</el-form-item>
						<el-form-item label="买家昵称"  prop="buyer_name">
							<el-input v-model="form.buyer_name" size='mini' icon="search" :on-icon-click="selectBuyersName" :disabled="isNotCanSave" readonly></el-input>
							<el-tooltip v-if='rules.buyer_name[0].isShow' class="item" effect="dark" :content="rules.buyer_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="上门服务类型">
							<el-select  size='mini'  placeholder="类型" v-model="form.if_door_install" disabled>
								<el-option label="安装" value="INSTALL"></el-option>
								<el-option label="维修" value="REPAIR"></el-option>
								<el-option label="无需" value="NONEED"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="是否多次补件">
							<el-select  size='mini'  placeholder="" v-model="form.if_multiple_patch" disabled>
								<el-option label="是" value="Y"></el-option>
								<el-option label="否" value="N"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="货运方式" prop="logistics_mode">
									<xpt-select-aux v-model='form.logistics_mode' aux_name='bjd_hyfs' :disabledOption="disabledOption"></xpt-select-aux>
									<el-tooltip v-if='rules.logistics_mode[0].isShow' class="item" effect="dark" :content="rules.logistics_mode[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="客户承担费用">
							<el-input size='mini' v-model="form.customer_fee" type="number" :disabled="customerFeeDisabled"></el-input>
						</el-form-item>
						<el-form-item label="运费方式">
							<el-select v-model="form.delivery_fee_pay_type" size="mini" :disabled="isNotCanSave">
								<el-option label="现付" value="NOW_PAY"></el-option>
								<el-option label="到付" value="ARRIVE_PAY"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="提交采购人">
							<el-input size='mini' v-model="form.submit_purchase_name" disabled></el-input>
						</el-form-item>
						<el-form-item label="提交采购时间">
							<el-date-picker
								v-model="form.submit_purchase_time"
							    type="datetime"
							    size="mini"
							    disabled
							></el-date-picker>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="6">
						<el-form-item label="收货人" prop="receiver_name">
							<!-- 问题商品信息里面存在一个采购单号就不能修改收货人 -->
							<el-input
								size='mini'
								v-model="form.receiver_name"
								:disabled="
									addressDisabled || (form.id && goodsList.some(obj => obj.purchase_warehouse_no))
								"
							></el-input>
							<el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="收货人电话" prop="reveiver_phone">
							<xpt-eye-switch v-model="form.reveiver_phone" :disabled="addressDisabled" :aboutNumber="form.after_ticket_no"></xpt-eye-switch>
							<el-tooltip v-if='rules.reveiver_phone[0].isShow' class="item" effect="dark" :content="rules.reveiver_phone[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="业务备注" :class="$style['textarea-style']"  prop="business_remark">
							<el-input type="textarea" v-model="form.business_remark" :autosize="{ minRows: 2, maxRows: 2}" :maxlength="255" :disabled="businessRemarkDisabled"></el-input>
							<el-tooltip v-if='rules.business_remark[0].isShow' class="item" effect="dark" :content="rules.business_remark[0].message" placement="right-start" popper-class='xpt-form__error'>
							 	<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
					</el-col>
					<el-col :span="6">
                        <el-form-item label="销售标签">
							<span style="color:red;">{{ form.sales_label }}</span>
						</el-form-item>
						<el-form-item>
							<el-checkbox v-model="form.if_all_lock" true-label="Y" false-label="N" style="margin-left:-60px;" disabled>是否整单锁库</el-checkbox>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="18">
						<el-form-item label="客户地址"  >
							<el-select v-model="form.province" size="mini" style="width:150px;" @change="changeAddress(form.province,1)" :disabled="addressDisabled||ifDecrypt">
								<el-option v-for="(value, key) in province" :label="value" :value="parseInt(key)" :key="key" ></el-option>
							</el-select>
            				<el-select v-model="form.city" size="mini" style="width:150px;" @change="changeAddress(form.city,2)" :disabled="addressDisabled||ifDecrypt">
								<el-option v-for="(value, key) in city" :label="value" :value="parseInt(key)" :key="key"   ></el-option>
							</el-select>
            				<el-select v-model="form.area" size="mini" style="width:150px;" @change="changeAddress(form.area,3)" :disabled="addressDisabled||ifDecrypt">
								<el-option v-for="(value, key) in area" :label="value" :value="parseInt(key)" :key="key" ></el-option>
							</el-select>
							<el-select v-model="form.street" size="mini" style="width:150px;" @change="changeAddress(form.street,4)" :disabled="addressDisabled||ifDecrypt">
								<el-option v-for="(value, key) in street" :label="value" :value="parseInt(key)" :key="key" ></el-option>
							</el-select>
           					<el-input size='mini' v-model="form.receiver_addr" :disabled="addressDisabled||ifDecrypt"></el-input>
							<el-button type="primary" size="mini" @click="choiceAddress" :disabled="addressDisabled">选择客户地址</el-button>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="是否批量事件">
							<el-switch on-text="是" off-text="否" on-value="Y" off-value="N" v-model="form.if_batch_event"
								:disabled="true"></el-switch>
						</el-form-item>
					</el-col>
				</el-row>
        <el-row :gutter="20">
          <el-col :span="6">
						<el-form-item label="收入店铺">
							<el-input size='mini' v-model="form.income_shop_name" disabled></el-input>
						</el-form-item>
					</el-col>
        </el-row>
			</el-tab-pane>
			<el-tab-pane label='其他信息' name='otherInformation'>
				<el-row :gutter="20">
					<el-col :span="6">
						<el-form-item label="物流公司">
							<el-input size='mini' v-model="form.delivery_company" disabled></el-input>
						</el-form-item>
						<el-form-item label="三包公司">
							<el-input v-model="form.three_company" size='mini' disabled></el-input>
						</el-form-item>
						<el-form-item label="提货点电话">
							<xpt-eye-switch v-model="form.delivery_phone" :disabled="true" :aboutNumber="form.after_ticket_no"></xpt-eye-switch>
						</el-form-item>
						<el-form-item label="三包公司电话">
							<xpt-eye-switch v-model="form.three_phone" :disabled="true" :aboutNumber="form.after_ticket_no"></xpt-eye-switch>
						</el-form-item>
			            <el-form-item label="单据状态" readonly disabled>
			              	<el-input size='mini' :value="{CREATE:'创建',APPROVING:'审核中',APPROVED:'已审核',COMMIT:'提交',SUBMITPURCHASE:'提交采购',LOCK:'锁定',REJECT:'驳回',UNLOCK:'解锁',CLOSE:'关闭'}[form.status]"  readonly disabled></el-input>
			            </el-form-item>
			            <el-form-item label="经销商名称">
							<el-input size='mini' disabled :value="form.dealer_customer_name"></el-input>
						</el-form-item>


					</el-col>
					<el-col :span="6">
						<el-form-item label="创建人">
							<el-input size='mini' v-model="form.creator_name" readonly disabled></el-input>
						</el-form-item>
						<el-form-item label="创建日期" readonly disabled>
							<el-date-picker
							    type="date"
							    placeholder="选择日期:" size="mini"
							    v-model="form.create_time"
							    :editable="false"
							    readonly disabled
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="驳回人">
							<el-input size='mini' v-model="form.rejector_name" readonly disabled></el-input>
						</el-form-item>
						<el-form-item label="驳回日期" readonly disabled>
							<el-date-picker
							    type="date"
							    placeholder="选择日期:" size="mini"
							    v-model="form.reject_time"
							    :editable="false"
							    readonly disabled
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="业务状态">
							<el-input size='mini' :value="{SALEMANHANDLING:'业务处理中',CLERKHANDLING:'文员处理中',SUBMITPURCHASE:'提交采购',CLERKDEALTING:'文员待办',REJECT:'驳回'}[form.business_status]"  readonly disabled></el-input>
						</el-form-item>

						<el-form-item label="经销商编码">
							<el-input size='mini' disabled :value="form.dealer_customer_number"></el-input>
						</el-form-item>

					</el-col>
					<el-col :span="6">
						<el-form-item label="审核人">
							<el-input size='mini' v-model="form.auditor_name" readonly disabled></el-input>
						</el-form-item>
						<el-form-item label="审核日期" readonly disabled>
							<el-date-picker
							    type="date"
							    placeholder="选择日期:" size="mini"
							    v-model="form.audit_time"
							    :editable="false"
							    readonly disabled
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="关闭人">
							<el-input size='mini' v-model="form.closer_name" readonly disabled></el-input>
						</el-form-item>
						<el-form-item label="关闭日期" readonly disabled>
							<el-date-picker
							    type="date"
							    placeholder="选择日期:" size="mini"
							    v-model="form.close_time"
							    :editable="false"
							    readonly disabled
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="关闭类型">
							<el-input size='mini'  v-model="{LOST_ITEM_FOUND:'丢件找到',STOP_PRODUCTION:'停产无法补件',OTHER:'其它',PLACE_WRONG_ORDER:'下错单',NO_NEED_TO_HANDLE:'不需要（协商，赔偿，维修）处理',}[form.close_type]" disabled></el-input>
						</el-form-item>
						<el-form-item label="关闭备注">
							<el-input size='mini'  v-model="form.closer_remark" disabled></el-input>
						</el-form-item>

					</el-col>
					<el-col :span="6">
						<el-form-item label="实际件数">
							<el-input size='mini' v-model="delivery_quantity" readonly disabled></el-input>
						</el-form-item>
			            <el-form-item label="运输成本">
			             	<el-input size='mini' v-model="transport_cost" readonly disabled></el-input>
			            </el-form-item>
			            <el-form-item label="文员锁定人名称">
			              	<el-input size='mini' v-model="form.clerk_lock_name" disabled></el-input>
			            </el-form-item>
			            <el-form-item label="文员锁定人分组">
			              	<el-input size='mini' v-model="form.clerk_lock_group"  disabled></el-input>
			            </el-form-item>
			            <el-form-item label="是否经销商订单">
							<el-switch on-text="是" off-text="否" :value="{'Y':true,'N':false,'':false,'null': false,}[form.if_dealer]"  disabled></el-switch>
						</el-form-item>
					</el-col>
				</el-row>
			</el-tab-pane>
		</el-tabs>
	</el-form>
	</el-row>

	<el-row class='xpt-flex__bottom'>
		<el-tabs v-model="selectTab2" @tab-click="updateSomeLists">
	      	<el-tab-pane label='问题商品信息' name='goodsInfo' class='xpt-flex'>
	          	<component is='supplyBadGoods' :formInfo="form" :isFromSolution="isFromSolution" :businessBtnDisabled="businessBtnDisabled || ifDealerOrderCantEdit"  :clerkBtnDisabled="clerkBtnDisabled":isNotCanSave="isNotCanSave" :applyDescDisabled="applyDescDisabled" :goodsInfoList="goodsList" @getGoodsList="getGoodsList" @reflushList="reflushList"  @getSelectGoodsList="getSelectGoodsList" :typeNumber="!!params.typeNumber?params.typeNumber:2"></component>
	      	</el-tab-pane>
	      	<el-tab-pane label='驳回信息' name='rejectInfo' class='xpt-flex'>
	        	<component is='rejectInfo'  :rejectList="rejectList" @getRejectList="getRejectList" :formInfo="form" :rejectDisabled="rejectDisabled"></component>
	      	</el-tab-pane>
      <el-tab-pane label="客户承担费用信息" name="customerFee">
        <xpt-list
          :data='bearCostsList'
          :colData='bearCostsCol'
          :showHead='false'
          selection=''
        ></xpt-list>
      </el-tab-pane>
			<el-tab-pane label='操作记录' name='operatInfo' class='xpt-flex'>
				<xpt-list
					:data='operatList'
					:colData='operatCol'
					:showHead="false"
					:orderNo="true"
	          		selection=''
					></xpt-list>
			</el-tab-pane>
	      	<el-tab-pane label='物流信息' name='logisticsInfo' class='xpt-flex'>
		        <xpt-list
							ref="logisticsListRef"
		          :data='logisticsList'
		          :colData='logisticsCol'
							:btns="logisticsBtns"
		          selection='radio'
							@radio-change="handleLogisticsListSelectionChange"
		        >
		        </xpt-list>
	      	</el-tab-pane>
	      	<el-tab-pane label='接口信息' name='interfaceInfo' class='xpt-flex'>
		        <xpt-list
		          :data='interfaceList'
		          :colData='interfaceCol'
		          :showHead="false"
		          :orderNo="false"
		          selection=''
		        >
              <template slot="parameter_info" slot-scope="scope">
                {{ scope.row.parameter_info.length <= 1000? scope.row.parameter_info : scope.row.parameter_info.substring(0, 1000) }}
		          </template>
              <template slot="operation" slot-scope="scope">
		          	<el-button v-if="scope.row.parameter_info.length > 0" type="primary" size="mini" @click="copyParameterInfo(scope.row.parameter_info)">复制传送参数</el-button>
              </template>
          </xpt-list>
	      	</el-tab-pane>
		</el-tabs>
	</el-row>
</div>
</template>

<script>
import VL from '@common/validate.js';
import supplyBadGoods from '@components/after_sales_supply/supplyBadGoods';//商品信息
import rejectInfo from '@components/after_sales_supply/rejectList';//驳回
import rejectold from '@components/after_sales_supply/model/reject.js';//驳回
import operat from '@components/after_sales_supply/model/operat.js';//操作记录
import logistics from '@components/after_sales_supply/model/logistics.js';//物流信息
import interfaceInfo from '@components/after_sales_supply/model/interface.js';//接口信息
import bearCosts from '@components/after_sales_supply/model/bearCosts.js';//客户承担费用信息
import threementhods from '@components/after_sales_supply/model/threementhods.js';//第三方对接方法
import Fn from '@common/Fn.js';
import Vue from 'vue';
import data from '../after_sales_service/data';

export default {
	props:['params'],
  mixins: [rejectold,operat,logistics,interfaceInfo,threementhods, bearCosts],
	data (){
	    var self=this;
		return {
			ifDecrypt:false,
			typeActive:true,
          disabledOption: [],
	      submitOrCancel:[
	        {
	          type:'primary',
	          txt:'提交',
	          code:'submit',
	          isDisabled:self.submitDisabled,
	          click:function(){
	          	//商品明细里面的供应商必填
	          	console.log('afkdsjlkfjasdlkf')
      			let bool = self.judgeHasSupply();
      			if(!bool) {
      				self.$message.error('请填写问题商品的供应商');
      				return;
      			}
	            self.preEvent((d)=>{
	            	self.submit();
	            })
	              //self.submit();
	          }
	        },
	        {
	          type:'warning',
	          txt:'撤回',
	          code:'withdraw',
	          isDisabled:self.withdrawDisable,
	          click:function(){
	            self.cancel();
	          }
	        }
	      ],
	      buyerDoBtns:[
	        {
	          type:'primary',
	          txt:'生成采购需求',
	          code:'creatPurchase',
	          isDisabled:self.creatPurchaseDisabled,
	          click:function(){
	            self.generatPurchaseOrder();
	          }
	        },
	        {
	          type:'primary',
	          txt:'生成4PL出库单',
	          code:'creatOtherStock',
	          isDisabled:self.creatOtherStockDisabled,
	          click:function(){
	            self.generatOutboundOrder();
	          }
	        },{
	          type:'primary',
	          txt:'生成K3销售出库单',
	          code:'creatOtherStock',
	          isDisabled:self.creatOtherStockDisabled,
	          click:function(){
	            self.generateFinSaleOutStock();
	          }
	        },{
	          type:'primary',
	          txt:'生成服务单',
	          code:'creatServe',
	          isDisabled:self.creatServeDisabled || self.isDZBJ,
	          click:function(){
	            // self.generateServiceBill();
              self.checkGenerateServiceBill()
	          }
	        }
	      ],
	      buyerDoCancelBtns:[{
	        type:'warning',
	        txt:'取消采购需求',
	        code:'cancelPurchase',
	        isDisabled:self.cancelPurchaseDisabled,
	        click:function(){
	            self.cancelPurchaseOrder();
	        }
	      },
	        {
	          type:'warning',
	          txt:'取消4PL出库单',
	          code:'cancelOtherStock',
	          isDisabled:self.cancelOtherStockDisabled,
	          click:function(){
	            self.cancelOutBoundOrder();
	          }
	        },{
	          type:'warning',
	          txt:'取消K3销售出库单',
	          code:'cancelOtherStock',
	          isDisabled:self.cancelOtherStockDisabled,
	          click:function(){
	            self.cancelFinSaleOutStock();
	          }
	        },{
	          type:'warning',
	          txt:'取消服务单',
	          code:'cancelServe',
	          isDisabled:self.cancelServeDisabled,
	          click:function(){
	            self.cancelServiceBill();
	          }
	        }],
			province: {},//省所有选项
			city: {},//市
			area: {},//市
			street:{},
			isFromSolution:false,//单据来源类型(默认新建)，暂时先用明细里面的if_sqdadd去做判断
			inputDisabled:false,//从方案带过来的字段不能编辑，但新增申请是可以编辑的
			customerFeeDisabled:false,//
			//isCurrentUser:true,//是否是当前操作人员的单
			isCurrentUserOfBusiness:true,//是否是当前业务锁定人
			isCurrentUserOfClerk:true,//是否是当前文员

			businessBtnDisabled:false,//问题商品按纽
			clerkBtnDisabled:true,//锁库按钮

			applyDescDisabled:false,//补件申请描述，只允许业务人员操作
			//by pmm
			//currentUserType:null,//当前登录用户相对应单据来说是属于哪个用户角色（业务锁单/文员锁定）
			addressDisabled:false,//地址编辑
			businessRemarkDisabled:false,

			//按钮操作分三部分=>业务锁定，文员锁定，其它交集按钮

			//业务锁定按钮,默认可编辑
			submitPurchaseDisabled:true,//提交采购
			closeDisabled:true,//关闭

			ifDealerOrderCantEdit: false,//经销商订单林氏员工不能编辑

			//文员锁定,默认不可操作
			//clerkDisabled:true,//是否到了文员操作那一步

			lockDisabled:true,//锁定
			rejectDisabled:true,//驳回
			unlockDisabled:true,//解锁
			submitDisabled:true,//提交
			withdrawDisable:true,//撤回
			auditDisabled:true,//审核
			creatPurchaseDisabled:true,//生成采购订单
			creatOtherStockDisabled:true,//生成4PL出库单
			creatServeDisabled:true,//生成服务单

			cancelPurchaseDisabled:true,//取消采购订单
			cancelOtherStockDisabled:true,//取消4PL出库单
			cancelServeDisabled:true,//取消服务单


			//业务锁定和文员锁定都可以操作的按钮
			saveDisabled:false,//保存



			selectTab1: 'basicInformation',
			selectTab2: 'goodsInfo',
      		businessStatusList:{SALEMANHANDLING:'业务处理中',CLERKHANDLING:'文员处理中',SUBMITPURCHASE:'提交采购',CLERKDEALTING:'文员待办',REJECT:'驳回'},
      		typeList:{SHBJSQD:'售后补件申请单',ZTBJSQD:'展厅补件',NXBJSQD:'内销补件'/*,BCBJSQD:'摆场补件'*/,RESALE:'再售补件'},
			form: {
			    id:null,// '主键ID',
		          after_ticket_no:null,//  '单据编号',
		          after_order_id:null,// '售后单ID',
		          after_order_no:null,//  '售后单号',
		          merge_trade_no:null,//  '合并订单号',
		          type:null,//  '补件类型',
		          if_all_lock:'N',//  '是否整单锁库,N/Y',
		          if_door_install:'NONEED',//  '是否上门安装,N/Y',
		          salesman:null,//  '业务员ID',
		          salesman_name:null,// '业务员姓名',
		          salesman_group:null,//  '业务员分组',
		          business_man_name:null,//业务锁定人名称
		          business_man_id:null,//业务锁定人id
		          business_man_group:null,//业务锁定人分组
				  if_modify_logistics:'Y',//是否允许修改货运方式
		          business_time:null,//业务锁定时间
		          clerk_lock_id:null,//'文员锁定人id'
		          clerk_lock_name:null,//'文员锁定人名称
		          clerk_lock_group:null,//'文员锁定人分组
		          clerk_lock_time:null,//'文员锁定人时间
		          receiver_name:null,//  '收货人',
		          reveiver_phone:null,//  '收货人电话',
		          receiver_addr:null,//  '收货人地址',
		          province:null,//  '省id',
		          city:null,//  '市id',
		          area:null,//  '区id',
		          street:null,//  '街道id',
		          last_modifier:null,
		          last_modifier_name:null,
		          last_modify_time:null,
		          logistics_mode:null,//  '物流方式 ''''三包/自提（物流点自提）/仓库自提/快递',
		          delivery_id:null,//  '提货点id',
		          delivery_name:null,//  '提货点名字（冗余字段）',
		          delivery_company:null,//  '物流公司（冗余字段）',
		          delivery_phone:null,//  '提货点电话（冗余字段）',
		          three_id:null,//  '三包点id',
		          three_name:null,//  '三包点名字（冗余字段）',
		          three_company:null,//  '三包点公司（冗余字段）',
		          three_phone:null,//  '三包点电话（冗余字段）',
		          customer_fee:null,//d  '客户承担费用',
		          delivery_fee_pay_type:'NOW_PAY',//  '运费支付类型',默认现付
		          buyer:null,//  '买家昵称id',
		          buyer_name:null,// '买家昵称（冗余字段）',
		          business_status:null,//  '业务状态',
		          business_remark:null,//  '业务备注',
		          status:null,//  '方案单据状态',
		          address_id:null,//  '收货信息id',
		          transport_cost:null,//  '运输成本',
		          delivery_quantity:null,//  '发货件数',
		          rejector:null,//  '驳回人ID',
		          rejector_name:null,//  '驳回人姓名',
		          reject_time:null,// '驳回时间',
		          auditor:null,//  '审核人ID',
		          auditor_name:null,//  '审核人姓名',
		          audit_time:null,//  '审核时间',
		          creator:null,//  '创建人ID',
		          creator_name:null,//  '创建人姓名',
		          create_time:null,//  '创建时间',
		          creator_group_name:null,//  '创建人分组（冗余字段）',
		          creator_big_group_name:null,//  '创建人大分组（冗余字段）',
		         // buyer:null,//  '采购员id',
		          //buyer_name:null,//  '采购员姓名',
		          closer:null,//  '关闭人员id',
		          closer_name:null,//  '关闭人员姓名',
		          close_time:null,//  '关闭时间',
		          bill_time:new Date(),//单据日期
		          //pushed_date:null,//入库日期
		          closer_remark:null,//关闭备注
		          dealer_customer_id:null,//经销商Id
		          if_dealer:null,//是否经销商订单(Y是/N否)
		          dealer_customer_number:null,//经销商编码
					dealer_customer_name:null,//经销商名称
					logistics_supplier_class :'A', //服务商分类
              income_shop_name: null, // 收入店铺
		      },

		      delivery_quantity:null,//实际件数
		      transport_cost:null,//运输成本

		      isNotCanSave:false,
		      //isclose:false,
		      //isAdd:false,
		      ifClickUpload:false,
		      uploadData:{},
		      goodsList:[],
		      selectGoodsList:[],
		      rejectList:[],
		      //isSqdAddSocted:true,//是否为上游单据并且锁定
		      oldFormData:{},
		      oldGoodsList:[],
		      isFirstEidt:false,



		      //isSqdAdd:true,

		      isFirstInit:true,
		      isFirstInitIng:false,
		      switchPreChange:true,//切换到上一页
			rules: {
				type: VL.isNotBlank({

					self:self,
					msg:'请选择补件类型',
				}),
				logistics_mode:VL.isNotBlank({

					self:self,
					msg:'请选择货运方式',
				}),
				reveiver_phone: VL.isNotBlank({

					self:self,
					msg:'请填写收货人电话',
				}),
				business_remark: VL.isNotBlank({

					self:self,
					msg:'请填写业务备注',
				}),
				receiver_name: VL.isNotBlank({

					self:self,
					msg:'请填写收货人姓名',
				}),
				salesman_name: VL.isNotBlank({

					self:self,
					msg:'请填写选择业务员信息',
				}),
				buyer_name:VL.isNotBlank({

					self:self,
					msg:'请选择买家昵称',
				})


            },
            ifSave:false,
		}
	},
	computed: {
		isDZBJ() {
			// 是否为定制补件类型
			return this.form.type === 'DZBJ'
		}
	},
	methods: {
    copyParameterInfo(text){
     Fn.copyToClipboard(text)
    },
        //要禁掉货运方式下拉选项
        getDisabledOption(){
            let data = __AUX.get('bjd_hyfs').filter((item,index,array)=>{
                return item.code == 'THD97244';
            });
            this.disabledOption = data.map((item)=>{
                return {code:item.code}
            })
        },
		/**
		*切换单据
		**/
		switchBills(number){
			this.switchPreChange = number?true:false;
			let data = this.getPreAndNextData();
			if(this.switchPreChange && !data.preData){
				this.$message.error('亲，这已经是第一页数据了哦')
				return;
			}
			if(!this.switchPreChange && !data.nextData){
				this.$message.error('亲，这已经是第后一页数据了哦')
				return;
			}
			let id = this.switchPreChange?data.preData.id:data.nextData.id;
			this.$root.eventHandle.$emit('removeTab',this.params.tabName);
			this.$root.eventHandle.$emit('creatTab',{
				name:'补件单详情',
				params:{id:id,list:this.params.list},
				component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')
			})


		},
		/***
		*获取上一页，下一页的数据
		**/
		getPreAndNextData(){
			let list = this.params.list;
			let id = this.params.id;
			if(!list || !list.length){
				this.$message.error('没有数据哦，请从列表从重选择');
				return;
			}
			let preData,nextData;
			list.map((a,b)=>{
				if(a.id == id){
					if(b){
						preData = list[b-1];
					}
					if(b != list.length -1){
						nextData = list[b+1];
					}
				}
			})
			return {
				preData : preData || '',
				nextData : nextData || ''
			}
		},
		/**
		*初使化页面按钮
		***/
		initBtnDisabled(){
			//单据状态=>{CREATE:'创建',APPROVING:'审核中',APPROVED:'已审核',COMMIT:'提交',SUBMITPURCHASE:'提交采购',LOCK:'锁定',REJECT:'驳回',UNLOCK:'解锁',CLOSE:'关闭'}
			//业务状态=>{SALEMANHANDLING:'业务处理中',CLERKHANDLING:'文员处理中',SUBMITPURCHASE:'提交采购',CLERKDEALTING:'文员待办',REJECT:'驳回'}
			console.log('状态');
			let status = this.form.status;
			let business_status = this.form.business_status;
			let hasStock = this.judgeHasStock();
			let isClose = status == 'CLOSE';
			let empolyeeObj = this.getEmployeeInfo();
			let currentUserId = empolyeeObj.id;


			let businessId = this.form.business_man_id;//业务锁定ID
	    	let clerkId = this.form.clerk_lock_id;//文员锁定IDc



	    	//业务锁定人（未锁定以及锁定）
	    	let isCurrentUserOfBusiness = !businessId || businessId == currentUserId;

	    	//文员还未锁定的时候以及文员锁定
	    	//let isCurrentUserOfClerk = !clerkId || clerkId == currentUserId;
	    	let isCurrentUserOfClerk = clerkId == currentUserId;

	    	//let isCurrentUser = isCurrentUserOfBusiness || isCurrentUserOfClerk;

	    	this.isCurrentUserOfBusiness = isCurrentUserOfBusiness;
	    	this.isCurrentUserOfClerk = isCurrentUserOfClerk;
			// 销售出库单已审核全部为N
			let if_all_sale_out_stock_audit = false;
			this.goodsList.forEach(item=>{
				if(item.if_sale_out_stock_audit == 'Y'){
					if_all_sale_out_stock_audit = true
				}
			})


        	//地址按钮：（是当前业务锁定人&&没有出库单）
        	//this.addressDisabled = !isCurrentUser || isClose?true:(isCurrentUserOfBusiness && !hasStock)?false:true;
        	this.addressDisabled = isCurrentUserOfBusiness && !hasStock && status != 'CLOSE'?false:true;

			//保存（只要是没有关闭并且没有出库单的详情下，都可以保存的）
			//this.saveDisabled = !isCurrentUser || isClose?true:hasStock;
			this.saveDisabled = (isCurrentUserOfBusiness || isCurrentUserOfClerk) && !hasStock && status != 'CLOSE'?false:true;

			//提交采购(单据状态==创建&&(业务状态==业务处理中 || 业务状态==驳回))
			this.submitPurchaseDisabled = isCurrentUserOfBusiness && status == 'CREATE' && (business_status == 'SALEMANHANDLING' || business_status == 'REJECT')?false:true;

			//锁定(单据状态==创建 && 业务状态==文员待办)
			this.lockDisabled = !clerkId && status == 'CREATE' && business_status == 'CLERKDEALTING'?false:true;


			//解锁(单据状态==创建 && 业务状态==文员处理中)
			this.unlockDisabled = isCurrentUserOfClerk && status == 'CREATE' && business_status == 'CLERKHANDLING'?false:true;

			//驳回(单据状态==创建 && 业务状态==文员处理中)
			this.rejectDisabled = isCurrentUserOfClerk && status == 'CREATE' && business_status == 'CLERKHANDLING'?false:true;


			//提交(单据状态==创建 && 业务状态==文员处理中)
			this.submitDisabled = isCurrentUserOfClerk && status == 'CREATE' && business_status == 'CLERKHANDLING'?false:true;


			//撤回(单据状态==审核中 &&　业务状态==文员处理中)
			this.withdrawDisable = isCurrentUserOfClerk && status == 'APPROVING' && business_status == 'CLERKHANDLING'?false:true;

			//关闭(没有出库单号)
			//this.closeDisabled = isCurrentUserOfClerk && status &&　status != 'CLOSE' && !hasStock?false:true;
			this.closeDisabled =  status &&　status != 'CLOSE' && !hasStock?false:true || if_all_sale_out_stock_audit;


			//审核(单据状态==审核中)
			this.auditDisabled = isCurrentUserOfClerk && status == 'APPROVING'?false:true;


			//生成采购订单(单据状态==已审核)
			this.creatPurchaseDisabled = /*isCurrentUserOfClerk &&*/ status == 'APPROVED'?false:true;


			//生成4PL出库单(单据状态==已审核)
			this.creatOtherStockDisabled = /*isCurrentUserOfClerk &&*/ status == 'APPROVED'?false:true;

			//生成服务单,取消采购订单,取消4PL出库单,取消服务单(单据状态 != 关闭 &&　业务状态==文员处理中)
			this.creatServeDisabled = this.cancelPurchaseDisabled = this.cancelOtherStockDisabled = this.cancelServeDisabled = /*isCurrentUserOfClerk &&*/ status && status !='CLOSE' && business_status == 'CLERKHANDLING'?false:true;



			//（业务员，补件类型）,不是方案下来的单据&&(单据状态 != '审核中' || 单据状态 != '已审核')
			this.inputDisabled = !this.saveDisabled && !this.isFromSolution && (status != 'APPROVING' || status != 'APPROVED')?false:true;

			this.ifDealerOrderCantEdit = false;

			/*if(
				this.ifDealerOrderCantEdit = !this.personBusinessAttribute.attributeValue && this.form.if_dealer === 'Y'
			){
				this.inputDisabled = true
			}*/

			//客户承担费用 (方案下来的单据&&单据状态==创建&&业务状态==文员处理中)||(不是方案下来的单据 &&　单据状态==创建)
			this.customerFeeDisabled = this.saveDisabled?true:(this.isFromSolution && status == 'CREATE' && business_status == 'CLERKHANDLING') || (!this.isFromSolution && status == 'CREATE')?false:true;

			//业务备注(单据状态='创建' &&  (业务状态='业务处理中' || 业务状态='驳回'))
			this.businessRemarkDisabled = !this.saveDisabled && (!status || (status == 'CREATE' && (business_status == 'REJECT' || business_status == 'SALEMANHANDLING'))) ?false:true;

			//日期,买家昵称,运费方式,这些字段在 (单据状态 !='审核中' && 单据状态 !='已审核')可编辑
			this.isNotCanSave = !this.saveDisabled && status !='APPROVING' && status !='APPROVED'?false:true;

			//补件申请描述（业务状态='业务处理中' || 业务状态='文员待办' || 业务状态='驳回')
			this.applyDescDisabled = !business_status || business_status == 'SALEMANHANDLING' || business_status == 'CLERKDEALTING' || business_status == 'REJECT'?false:true;

			//删除行，新增行(单据状态='创建' && (业务状态='业务处理中' || 业务状态='驳回'))
			this.businessBtnDisabled = !isCurrentUserOfBusiness || this.isFromSolution?true:!status || (status=='CREATE' && (business_status == 'CLERKHANDLING' || business_status =='REJECT'))?false:true;

			//锁库，反锁库(业务状态='文员处理中' || 业务状态='文员待办' )&& 单据状态！='关闭'
			this.clerkBtnDisabled = /*!isCurrentUserOfClerk?true:*/status != 'CLOSE' && (business_status == 'CLERKHANDLING' || business_status == 'CLERKDEALTING')?false:true;

			this.setBtnDisabledOGroup();


		},

		/**
		*改变按钮组可点击状态
		*/
		setBtnDisabledOGroup(){
			var btns = this.submitOrCancel;
			var buyerBtns = this.buyerDoBtns;
			var buyerCancelBtns = this.buyerDoCancelBtns;
			var i = btns.length,g = buyerBtns.length,f = buyerCancelBtns.length;
			for(var a = 0;a < i;a++){
				let b = btns[a];
				let code = b.code;
				if(code == 'withdraw'){//撤回
					b.isDisabled = this.withdrawDisable;
					continue;
				}
				if(code == 'submit'){//提交
					b.isDisabled = this.submitDisabled;
					continue;
				}
				/*if(code == 'withdraw'){
					b.isDisabled = this.withdrawDisable;
				}else if(code == 'submit'){
					b.isDisabled = this.submitDisabled;
				}*/
			}
			for(var gg = 0; gg < g;gg++){
				let b = buyerBtns[gg];
				let code = b.code;
				if(code == 'creatPurchase'){//生成采购订单
					b.isDisabled = this.creatPurchaseDisabled;
					continue;
				}
				if(code == 'creatOtherStock'){//生成4PL出库单
					b.isDisabled = this.creatOtherStockDisabled;
					continue;
				}
				if(code == 'creatServe'){//生成服务单
					b.isDisabled = this.creatServeDisabled || this.isDZBJ;
					continue;
				}
			}
			for(var ff = 0; ff < f;ff++){
				let b = buyerCancelBtns[ff];
				let code = b.code;
				if(code == 'cancelPurchase'){//取消采购订单
					b.isDisabled = this.cancelPurchaseDisabled;
					continue;
				}
				if(code == 'cancelServe'){//取消服务单
					b.isDisabled = this.cancelServeDisabled;
					continue;
				}
				if(code == 'cancelOtherStock'){//取消4PL出库单
					b.isDisabled = this.cancelOtherStockDisabled;
					continue;
				}
			}
			this.setOrderOfGroupBtns(btns);
			this.setOrderOfGroupBtns(buyerBtns);
			this.setOrderOfGroupBtns(buyerCancelBtns);
			console.log('buyerBtns',buyerBtns);
			console.log('buyerCancelBtns',buyerCancelBtns);
		},
		/**
		*改变按钮组的顺序
		*没有禁用的按钮放在最前面
		*/
		setOrderOfGroupBtns(btns){
			if(!btns || !btns.length) return;
			var disabledBtns = [],notDisabledBtns = [];
			var i = btns.length;
			for(var a=0;a<i;a++){
				if(btns[a].isDisabled){
					disabledBtns.push(btns[a]);
				}else{
					notDisabledBtns.push(btns[a]);
				}
			}
			let array = notDisabledBtns.concat(disabledBtns);
			for(var g=0;g<i;g++){
				btns.splice(g,1,array[g]);
			}
		},

		//对页面需要认证的内容认证，required：是否必填，msg：提示信息
		VLFun (required, msg){
			return VL.isNotBlank({
				required:required,
				self:this,
				msg:msg,
			})
		},
		/**
		*提货点
		**/
		selectDelivery(){
			let params = {
				type:1
		    };

	      params.callback = (d)=>{
	      	console.log('d',d);
	      	let data = d.data[0];
	      	this.form.delivery_id = data.fid;
	      	this.form.delivery_name = data.f_LS_NAME;
	      	this.form.delivery_company = data.fname;
	      	this.form.delivery_phone = data.f_LS_PHONE;

	      }
	      this.$root.eventHandle.$emit('alert',{
	        params:params,
	        component:()=>import('@components/after_sales_supply/picklist'),
	        style:'width:80%;height:560px',
	        title:'选择提货点信息'
	      });
		},
		/*
		*三包点
		**/

		selectThreeName(){
			let params = {
				type:1
		    };

	      params.callback = (d)=>{
	      	console.log('d',d);
	      	let data = d.data[0];
	      	this.form.three_id = data.fid;
	      	this.form.three_name = data.f_LS_NAME;
	      	this.form.three_company = data.fname;
	      	this.form.three_phone = data.f_LS_PHONE;

	      }
	      this.$root.eventHandle.$emit('alert',{
	        params:params,
	        component:()=>import('@components/after_sales_supply/threelist'),
	        style:'width:80%;height:560px',
	        title:'选择三包点信息'
	      });
		},
		/**
		*修改三包点信息
		**/
		changeThreeName(val){
			if(val) return;
			this.form.three_id = null;
	      	this.form.three_name = null;
	      	this.form.three_company = null;
	      	this.form.three_phone = null;
		},
    /**
     *选择客户地址
     **/
    choiceAddress(){
		let self = this;
      if(!this.form.buyer){
        this.$message.error('买家昵称为空，请先选择买家');
        return;
      }
      let params = {
        isAlert:true,
        type:'radio',
        cust_id:this.form.buyer,
		merge_trade_no:self.form.merge_trade_no
      };

      params.callback = (d)=>{
		  if(!!d.ifDecryption.content){
			  this.form.encryption_add_id_pur = d.data[0].fid;
		  }
        this.setAdressInfo(d.data[0])
        //this.fillAddressInfo(d.data[0]);
      }
      this.$root.eventHandle.$emit('alert',{
        params:params,
        component:()=>import('@components/after_invoices/addresslist2'),
        style:'width:80%;height:560px',
        title:'选择客户地址'
      });
    },
    //地址信息参数赋值
    setAdressInfo(data){
        var self=this;
        self.form.buyer=data.cust_id;//用户
        self.form.buyer_name=data.cust_name;//用户
        self.form.receiver_name=data.receiver_name;//  '收货人',
        self.form.reveiver_phone=data.receiver_mobile;//  '收货人电话',
        self.form.receiver_addr=data.receiver_address;//  '收货人地址',
        self.form.province=data.receiver_state;//  '省id',
        self.form.city=data.receiver_city;//  '市id',
        self.form.area=data.receiver_district//  '区id',
       	self.form.street=data.receiver_street//  '街道id',
      // "NOW_PAY" : '现付',"ARRIVE_PAY" : '到付' 转换成NOWPAY，ARRIVALPAY
      	self.form.delivery_fee_pay_type=data.post_fee_type;
		self.form.encryption_add_id = data.fid;



    },
	// 选择用户列表
	selectBestStaff (){
  		if(this.inputDisabled)return;
		this.$root.eventHandle.$emit('alert',{
			title: '选择用户列表',
			style:'width:800px;height:600px',
			component:()=>import('@components/after_sales_common/selectPickrecommendhandler'),
			params: {
				callback: d => {
		        this.form.salesman = d.id
					this.form.salesman_name = d._real_name_nick_name
					//this.form.staff_group = d.group_id
					//this.form.salesman_group = d.group_name
				}
			},
		})
	},
	// 买家昵称
	selectBuyersName (){
  		if(this.isNotCanSave)return;
		this.$root.eventHandle.$emit('alert', {
			component: () => import('@components/customers/list'),
			style:'width:900px;height:600px',
			title:'买家昵称列表',
			params: {
				close: d => {
					this.form.buyer_name = d.name;
					this.form.buyer = d.cust_id;
				}
			},
		})
	},
    //获取三包信息
    getThreeInfo(){
	    var self=this;
	    if(self.isFirstEidt)return;
	    if(!(this.form.province && this.form.city && this.form.area))return;
      var searchParams = {
        province:this.form.province,
        city:this.form.city,
        area:this.form.area,
        street:this.form.street,
        logistics_supplier_class :this.form.logistics_supplier_class
      }
      this.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/getThreeInfo',searchParams, res => {
        let data =res.body;
        if(res.body.result){
            if(res.body.content){
              self.form.delivery_id=res.body.content.th_point;
              self.form.delivery_name=res.body.content.th_name;
              self.form.three_id=res.body.content.three_id;
              self.form.three_name=res.body.content.sb_name;
              //self.form.delivery_company=res.body.content.wl_supplier; 物流公司id
              self.form.delivery_company=res.body.content.wl_supplier_name;
              //slef.form.three_company==res.body.content.sb_supplier;//三包公司id
              self.form.three_company=res.body.content.sb_supplier_name;
              self.form.delivery_phone=res.body.content.th_phone;
              self.form.three_phone=res.body.content.sb_phone;

            }
        }
      })
    },


    /*要查询下游地址code,
	    *type==1为city,2为area,3为street
	  **/
  changeAddress(code,type){
	  var key = type ==1?'city':type ==2?'area':'street';
	  if(!code) return;
	  if(type == 4) return;
	  this.getAddress(code,(data)=>{
		    this[key] = '';
		    this[key] = data || {};
		    var value = this.form[key];
		    if(!data || !data.hasOwnProperty(this.form[key])){
			      if(type == 1){
			        this.form.city = null;
			        this.form.area = null;
			        this.form.street = null;
			      }else if(type == 2){
			        this.form.area = null;
			        this.form.street = null;
			      }else if(type == 3){
			        this.form.street = null;
				  }

	    	}
		});
	},
  //刷新数据
    reflushList(){
      this.getDetailInfo(this.form.id,"FIRST");
    },
//获取驳回数据
    getRejectList(data){
      this.rejectList=data;
    },
    //获取问题商品数据
    getGoodsList(data){
        this.goodsList=data;
    },
    //获取选择的数据
    getSelectGoodsList(data){
        this.selectGoodsList=data;
    },
    getGoodsListPost(){

      var self=this;
      var dataList=[];
      for(var i=0;i<self.goodsList.length;i++){
        var obj=self.goodsList[i];
        for(var key in obj){
           if(key=='has_stock'){
              delete obj.has_stock;
            }
          }
        dataList.push(obj)
      }
      return dataList;

    },
    /**
    *判断数据是否变化
    **/

    dataIsChange(){
      var oldForm = this.oldFormData;
      var newFormData = this.form;
      let bool = this.compareData(newFormData,oldForm);
      if(bool) return true;
      //var oldList = this.oldGoodsList;
      //var newgoolist=this.goodsList;
      var oldList = JSON.parse(JSON.stringify(this.oldGoodsList));
      var newgoolist = JSON.parse(JSON.stringify(this.goodsList));

      //去掉库存字段的比较
      oldList.map((a,b)=>{
      	 if(a.hasOwnProperty('has_stock')){
      	 	delete a.has_stock;
      	 }
      })
      newgoolist.map((a,b)=>{
      	 if(a.hasOwnProperty('has_stock')){
      	 	delete a.has_stock;
      	 }
      })


      bool = this.compareData(newgoolist,oldList);
      return bool;
    },

    //数据变化提示保存
    preEvent(callback){
      var isChange = this.dataIsChange();
      var _this = this;
      if(!isChange){
      	callback && callback();
      	return;
      }
      let params = {
      	  txt:"数据已发生变化，是否先保存数据在操作？",
          noTxt:'不保存',
          okTxt:'保存',
          ok(){
            _this.saveOrAdd(callback);
          },
          no(){
            callback && callback();
          }
      }
      this.$root.eventHandle.$emit('openDialog',params);
    },
    /**
    *保存按钮
    ***/
    saveEvent(){
    	this.saveOrAdd((data)=>{
    		this.getDetailInfo(data,"FIRST");
    	})
    },
    //保存或者新增
    saveOrAdd(callback){
      var self=this;
      /*if(self.isNotCanSave){
        self.$message.error('审核中或已审核的单据不能修改保存')
          return;
      }*/

      var isPass = true;
      this.$refs.form.validate((valid) => {
			isPass = valid;
	   });
      if(!isPass) return;

      var postData={
        ticketSupply:self.form,
        listPlanSupplyComponent:self.getGoodsListPost()
      }
     postData.ticketSupply=self.form;
      if(!postData.listPlanSupplyComponent || postData.listPlanSupplyComponent.length==0){
        self.$message.error('问题商品信息不能为空')
          return;
      }
      let bool = true
     postData.listPlanSupplyComponent.some(obj => {
     	if(!obj.apply_desc) {
     		this.$message.error('请填写申请需求描述');
     		bool = false;
     		return true;
     	}else if(!(obj.number > 0 && !/\./.test(obj.number))) {
     		this.$message.error('补件数量必须大于0的整数');
     		bool = false;
     		return true;
     	}
     });
     if(!bool) return;
      //console.log(JSON.stringify(postData))
      self.ifSave = true
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/saveOrUpdate?permissionCode=SUPPLY_APPLY_SAVE'),postData,(response)=>{
          self.ifSave = false
        if(response.body.result){
          self.$message.success('操作成功')
          //self.getDetailInfo(response.body.content,"FIRST");
          callback && callback(response.body.content);

        }else{
          self.$message.error(response.body.msg);
          this.initStatus();
        }
      },e=>{
          self.ifSave = false
        self.$message.error(e);
        this.initStatus();
      })
    },
    //刷新
    reflush(){
      var self=this;
      if(!self.form.id){
          //刷新界面
        self.form={
          id:null,// '主键ID',
            after_ticket_no:null,//  '单据编号',
            after_order_id:null,// '售后单ID',
            after_order_no:null,//  '售后单号',
            merge_trade_no:null,//  '合并订单号',
            type:null,//  '补件类型',
            if_all_lock:'N',//  '是否整单锁库,N/Y',
            if_door_install:'NONEED',//  '是否上门安装,N/Y',
            salesman:null,//  '业务员ID',
            salesman_name:null,// '业务员姓名',
            salesman_group:null,//  '业务员分组',
            business_man_name:null,//业务锁定人名称
            business_man_id:null,//业务锁定人id
            business_man_group:null,//业务锁定人分组
			if_modify_logistics:'Y',//是否允许修改货运方式
            business_time:null,//业务锁定时间
            clerk_lock_id:null,//'文员锁定人id'
            clerk_lock_name:null,//'文员锁定人名称
            clerk_lock_group:null,//'文员锁定人分组
            clerk_lock_time:null,//'文员锁定人时间
            receiver_name:null,//  '收货人',
            reveiver_phone:null,//  '收货人电话',
            receiver_addr:null,//  '收货人地址',
            province:null,//  '省id',
            city:null,//  '市id',
            area:null,//  '区id',
            street:null,//  '街道id',
            last_modifier:null,
            last_modifier_name:null,
            last_modify_time:null,
            logistics_mode:null,//  '物流方式 ''''三包/自提（物流点自提）/仓库自提/快递',
            delivery_id:null,//  '提货点id',
            delivery_name:null,//  '提货点名字（冗余字段）',
            delivery_company:null,//  '物流公司（冗余字段）',
            delivery_phone:null,//  '提货点电话（冗余字段）',
            three_id:null,//  '三包点id',
            three_name:null,//  '三包点名字（冗余字段）',
            three_company:null,//  '三包点公司（冗余字段）',
            three_phone:null,//  '三包点电话（冗余字段）',
            customer_fee:null,//d  '客户承担费用',
            delivery_fee_pay_type:'NOW_PAY',//  '运费支付类型',默认现付
            buyer:null,//  '买家昵称id',
            buyer_name:null,// '买家昵称（冗余字段）',
            business_status:null,//  '业务状态',
            business_remark:null,//  '业务备注',
            status:null,//  '方案单据状态',
            address_id:null,//  '收货信息id',
            transport_cost:null,//  '运输成本',
            delivery_quantity:null,//  '发货件数',
            rejector:null,//  '驳回人ID',
            rejector_name:null,//  '驳回人姓名',
            reject_time:null,// '驳回时间',
            auditor:null,//  '审核人ID',
            auditor_name:null,//  '审核人姓名',
            audit_time:null,//  '审核时间',
            creator:null,//  '创建人ID',
            creator_name:null,//  '创建人姓名',
            create_time:null,//  '创建时间',
            creator_group_name:null,//  '创建人分组（冗余字段）',
            creator_big_group_name:null,//  '创建人大分组（冗余字段）',
            // buyer:null,//  '采购员id',
            //buyer_name:null,//  '采购员姓名',
            closer:null,//  '关闭人员id',
            closer_name:null,//  '关闭人员姓名',
            close_time:null,//  '关闭时间',
            bill_time:new Date(),//日期
            closer_remark:null,//关闭备注
            income_shop_name: null, //收入店铺
        }
        self.goodsList=[];
        //self.$message.error("操作失败，未获取到参数id");
        return;
      }
      self.getDetailInfo(self.form.id);
    },
    setGoodsListInfo(data){
        var self=this;
        self.goodsList=[];
        for(var i=0;i<data.length;i++){
            var obj=data[i];
            obj['has_stock']=null;
          self.goodsList.push(obj);
        }
      //self.initSetGoodsStok();
    },
    /**
    *根据明细行来判断单据的来源类型
    *默认新增申请单
    ****/
    setBillFromType(data){
    	//if_sqdadd-是否是下游申请单新增(Y/N)
    	if(!data) return;
    	var bool = false;
    	data.map((a,b)=>{
    		if(a.if_sqdadd == 'N'){
    			bool = true;
    		}
    	})
    	this.isFromSolution = bool;
    	console.log('bool',bool);
    },
    /**
    *设置表头原始数据
    ***/
    setOldFormInfo(){
    	this.oldFormData = JSON.parse(JSON.stringify(this.form));
    },
    /**
    *设置明细信息
    ***/
    setOldGoodsList(){
    	this.oldGoodsList = JSON.parse(JSON.stringify(this.goodsList));
    },
    // 当点击，操作记录/物流信息/接口信息页签时刷新改页签数据
    updateSomeLists (tab){
		if(this.form.id && /^(operatInfo|logisticsInfo|interfaceInfo)$/.test(tab.name)){
			this.getDetailInfo(this.form.id, null, true)
		}
    },
    //查询详情
    getDetailInfo(id, type, onlyRefreshSomeLists){
        var self=this;
        var data={id: id}
				this.logisticsSelected = null;
				this.outboundNo = null;
        this.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/edit?permissionCode=SUPPLY_APPLY_QUERY'),data,(response)=>{
				// console.log('看看这是什么===',response)
				if(response.body.result){
        	if(onlyRefreshSomeLists){
        		//操作记录
	          	this.operatList=response.body.content.listOperateLog;
	          	// 客户承担费用
	          	this.bearCostsList=response.body.content.bearCostsList;
	          	//接口信息
	          	this.interfaceList=response.body.content.listSupplyInterfaceVo;
	          	//物流信息
	          	this.logisticsList=response.body.content.listOutboudVO;
        		return
        	}

          	this.isFirstInit=true;
          	this.form=response.body.content.ticketSupply;
			this.form.encryption_add_id_pur = null;
            //问题反馈编号获取
            Vue.prototype.tabNo[Vue.prototype.activeTab] = response.body.content.ticketSupply.after_ticket_no || '';
            let pageParams = {
                id: response.body.content.ticketSupply.id
            }
            Vue.prototype.tabId[Vue.prototype.activeTab] = JSON.stringify(pageParams);
            console.log("Vue",Vue.prototype.tabNo);
          	//商品列表初始化数据
           	this.setGoodsListInfo(response.body.content.listPlanSupplyComponent);

           	//初使化单据的来源
           	this.setBillFromType(response.body.content.listPlanSupplyComponent);

          	//self.goodsList=response.body.content.listPlanSupplyComponent;
          	//操作记录
          	this.operatList=response.body.content.listOperateLog;
          // 客户承担费用
          this.bearCostsList=response.body.content.bearCostsList;
          	//驳回记录
          	this.rejectList=response.body.content.listRejectLog;
          	//接口信息
          	this.interfaceList=response.body.content.listSupplyInterfaceVo;
          	//物流信息
          	this.logisticsList=response.body.content.listOutboudVO;
						this.$refs.logisticsListRef.clearRadioSelect()

          	//设置当前用户相对单据是属于什么角色
          	//this.setCurrentUserType();

          	this.initStatus();

          	this.calcNumAndCost();

          	//设置原始信息
          	this.setOldFormInfo();
          	this.setOldGoodsList();
			this.ifDecryption();

          	setTimeout(function(){
	            /*self.oldFormData=Object.assign({},self.form);
	            var old_list=[];
	            for(var i=0;i<self.goodsList.length;i++){
	                var item=Object.assign({},self.goodsList[i])
	              old_list.push(item)
	            }
	            self.oldGoodsList=old_list;*/
	            if(type && type=='FIRST'){
	              self.initSetGoodsStok();
	            }

	            self.isFirstEidt=false;
          	}, 500)

          //self.isAdd=true;
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },

    /**
    *是否有出库单
    ****/
    judgeHasStock(){
    	let list = this.goodsList;
    	let bool = false;
    	if(!list || !list.length) return bool;
    	list.map((a,b)=>{
    		/*if(a.if_pulled == 'Y'){
    			bool = true;
    			return;
    		}*/
    		if(a.warehouse_no){
    			bool = true;
    			return;
    		}

    	});
    	return bool;
    },

    /**
    *计算实际件数和运输成本
    **/
    calcNumAndCost(){
    	let num = 0,cost = 0;
    	console.log('看看其它东西')
    	//this.logisticsList = [{transportation_cost:5,delivery_quantity:1}]
    	this.logisticsList.forEach((a,b)=>{
    		let c = parseFloat(a.transportation_cost || 0);
    		let d = parseFloat(a.delivery_quantity || 0);
    		num += d;
    		cost += c;
    	});

    	this.delivery_quantity = num;
    	this.transport_cost = cost;
    },
    //初始化状态
    initStatus(){
        //var self=this;
        //this.isNotCanSave=false;

        //if_sqdadd：是否是下游申请单新增(Y/N)

        if(this.params.id || this.form.id){
          //this.isclose=false;
          //this.isSqdAddSocted=true;
          //判断保存按钮是否能点击
          //========
          //APPROVING:'审核中',APPROVED:'已审核'
          /*if(this.form.status=='APPROVING' || this.form.status=='APPROVED'){
            this.isNotCanSave=true;
          }*/
         /*if(this.form.status=='CLOSE'){
             this.isclose=true;
         }*/
          /*if(this.goodsList && this.goodsList.length>0&&this.goodsList[0].if_sqdadd=='N'){
            this.isSqdAdd=false;
          }*/

         //判断是否为上游方案生成的并且为锁定状态的
         //方案下来的单据&&单据状态==创建&&业务状态==文员处理中
         //不是方案下来的单据 &&　单据状态==创建
          /*if( this.form.status=='CREATE' && this.form.business_status=='CLERKHANDLING' && this.goodsList && this.goodsList.length>0&&this.goodsList[0].if_sqdadd=='N'){
            this.isSqdAddSocted=false;
          }
          if(this.form.status=='CREATE' && this.goodsList && this.goodsList.length>0&&this.goodsList[0].if_sqdadd=='Y'){
            this.isSqdAddSocted=false;
          }*/
        }else{
          //手工新增
          //this.isSqdAddSocted=false;

        }

        /*let isClose = (this.form.status=='CLOSE');
        let hasStock = this.judgeHasStock();
        //地址按钮：新增 || (是当前业务锁定人&&没有出库单)
        let isCurrentUser = this.currentUserType == 1 || this.form.business_man_id == this.form.clerk_lock_id;
		this.addressDisabled = isClose?true:!this.currentUserType || (isCurrentUser && !hasStock)?false:true;*/

		this.initBtnDisabled();

		//保存（只要是没有关闭并且没有出库单的详情下，都可以保存的）
		//this.saveDisabled = isClose?true:hasStock;

    },

    //新增
    add(){
      this.$root.eventHandle.$emit('creatTab',
        {name:'新增补件单',params:{typeNumber:2},component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')})
    },
    judgeHasSupply(){
    	console.log('aflksdajfklsdjf');
    	let list = this.goodsList;
    	let bool = true;
    	list.map((a,b)=>{
    		if(!a.supplier_company_id){
    			bool = false;
    			return;
    		}
    	})
    	return bool;
    },
    //提交
    submit(){
      var self=this;
      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }


      var data={id:self.form.id}
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/submit?permissionCode=SUPPLY_APPLY_SUBMIT'),data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.getDetailInfo(self.form.id);
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    //撤回
    cancel(){
      var self=this;
      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      var data={id:self.form.id}
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/withdraw?permissionCode=SUPPLY_APPLY_WITHDRAW'),data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.getDetailInfo(self.form.id);
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    /**
    *提交采购按钮
    ***/
    submitBuyerEvent(){
    	this.preEvent((d)=>{
    		this.submitBuyer();
    	})
    },
    //提交采购
    submitBuyer(){
      var self=this;
      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      var data={id:self.form.id}
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/submitPurchase?permissionCode=SUPPLY_APPLY_SUBMIT_PURCHASE'),data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.getDetailInfo(self.form.id);
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    //锁定
    lock(){
      var self=this;
      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      var data={id:self.form.id}
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/lock?permissionCode=SUPPLY_APPLY_LOCK'),data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.getDetailInfo(self.form.id);
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    //解锁
    unlock(){
      var self=this;
      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      var data={id:self.form.id}
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/unlock?permissionCode=SUPPLY_APPLY_UNLOCK'),data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.getDetailInfo(self.form.id);
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },

    /***
    *拿到最新的驳回明细
    **/
    getNewRejectData(){
    	let obj;
    	let d = this.rejectList,a = d.length;
    	for(let c = 0; c < a; c++){
    		if(!d[c].id){
    			obj = d[c];
    			break;
    		}
    	}
    	return obj;
    },
    //驳回
    reject(){
    	console.log('看看是什么东西');
      var self=this;
      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      let d = this.getNewRejectData();
      if(!d){
      	this.$message.error('请添加驳回理由');
      	return;
      }
      let params = {
      	id:self.form.id,
      	business_remark : self.form.business_remark,
      	reject_remark : d.reject_remark
      }
      //var data={id:self.form.id}
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/reject?permissionCode=SUPPLY_APPLY_REJECT'),params,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.getDetailInfo(self.form.id);
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    //审核
    audit(){
      var self=this;
      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
      if(this.auditDisabled) return;
      this.auditDisabled = true;
      var data={id:self.form.id}
      self.ajax.postStream(this._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/audit?permissionCode=SUPPLY_APPLY_AUDIT'),data,function(response){
        if(response.body.result){
          self.$message.success("操作成功");
          self.getDetailInfo(self.form.id);
        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })
    },
    // 生成服务单检查，上门安装服务类型为“无需”时，弹窗选择上门服务类型；其他还是继续执行
    checkGenerateServiceBill() {
      if(this.form.if_door_install === 'NONEED') {
        if(!this.form.id){
          this.$message.error("操作失败，未获取到参数id")
          return;
        }
        if(this.selectGoodsList?.length === 0){
          this.$message.error("操作失败，商品信息为空,请选择商品")
          return;
        }
        let params = {popText:'请选择上门服务类型',type:'close', supplyOrderId: this.form.id};
        // 弹窗回调
        params.callback = (data)=>{
          if(data.modalType === 'confirm' && data.if_door_install) {
            this.form.if_door_install = data.if_door_install // 更新上门安装服务类型
            this.generateServiceBill()
          }
        }
        this.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/common/updateDoorInstall'),
          style:'width:400px;height:150px',
          title:'请选择上门服务类型'
        });
      } else {
        this.generateServiceBill()
      }
    },
    //关闭
    close(){
      var self=this;
      if(!self.form.id){
        self.$message.error("操作失败，未获取到参数id")
        return;
      }
	      let params = {popText:'请输入关闭理由',type:'close'};

	      params.callback = (data)=>{
				//this.form.closer_remark = data.data;
				if(!data.reason){
					this.$message.error('请输入关闭理由');
					return;
				}
				if(!data.close_type){
					this.$message.error('请选择关闭类型');
					return;
				}
				requestClose(data);
		  }
		this.$root.eventHandle.$emit('alert',{
			params:params,
			component:()=>import('@components/common/popinput'),
			style:'width:600px;height:250px',
			title:'请输入关闭理由'
		});

		function requestClose(data){
			//var data={id:self.form.id,closer_remark:self.form.closer_remark}
			var data={id:self.form.id,closer_remark:data.reason,close_type:data.close_type,}
		      self.ajax.postStream(self._delPermissionCodeWhenDealerUser('/afterSale-web/api/aftersale/ticketSupply/close?permissionCode=SUPPLY_APPLY_CLOSE'),data,function(response){
		        if(response.body.result){
		          self.$message.success("操作成功");
		          self.getDetailInfo(self.form.id);
		        }else{
		          self.$message.error(response.body.msg)
		        }
		      },e=>{
		        self.$message.error(e)
		      })
		}

    },

    /**
     *上传附件
     */

    uploadFun() {
    	if(!this.form.id){
    		this.$message.error('请先保存单据再上传附件');
    		return;
    	}
      this.ifClickUpload = true;

      this.uploadData = {
        parent_name: 'AFTER_ORDER',
        child_name: 'BATCH',
        parent_no: this.form.after_order_no?this.form.after_order_no:this.form.after_ticket_no,//售后单号
        child_no: this.form.after_ticket_no,//父问题id,必须是String
        parent_name_txt: this.form.after_order_no?'售后单':'补件申请单',
        child_name_txt:'补件申请单',
        content: JSON.parse(JSON.stringify(this.form || {})),

      }
      //console.log('this.uploadData',this.uploadData);
      //重置，防止第二次不能触发
      setTimeout(() => {
        this.ifClickUpload = false;
      },100)

    },
    /**
     *查看附件
     */
    pictureFun() {
      var params = {
        parent_no : this.form.after_order_no?this.form.after_order_no:this.form.after_ticket_no,//售后单号
        //child_no: this.form.after_ticket_no,//父问题id,必须是String
        child_no:null,
        ext_data : null,
        parent_name : 'AFTER_ORDER',
        child_name : 'BATCH',
        parent_name_txt: this.form.after_order_no?'售后单':'补件申请单',
        child_name_txt: '补件申请单',
        notNeedDelBtn:this.saveDisabled,
        nickname: this.form.buyer_name,
		mergeTradeId: this.form.merge_trade_id,
      };
      params.callback = d=>{
      }
      this.$root.eventHandle.$emit('alert',{
        params:params,
        component:()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
        style:'width:1000px;height:600px',
        title:'下载列表'
      });
    },
    //刷新初始化所有获取可用库存数据
    setGoodsStokAll(data){
      var self=this;
      for(var key in data){
        for(var n=0;n<this.goodsList.length;n++){
          var obj=this.goodsList[n];
          if(Number(key)==obj.material_code){
            obj['has_stock']=data[key]||0;
          }
        }
      }
    },
    //得到所有的物料id
    getSelectIdsAll(){
      var self=this;
      var ids=[];
      if(self.goodsList&&self.goodsList.length>0){
        for(var i=0;i<self.goodsList.length;i++){
          if(self.goodsList[i]['material_id']){
            var obj={material_id:self.goodsList[i]['material_id'],if_company_send:self.goodsList[i]['if_company_send'],material_code:self.goodsList[i]['material_code']}
            ids.push(obj);
          }
        }
      }
      return ids;
    },
    // 经销商时去掉接口permissionCode权限控制
	_delPermissionCodeWhenDealerUser (api){
		return api;
		//return this.personBusinessAttribute.attributeValue ? api.replace(/\?permissionCode=.+/, '') : api
	},
    //初始化调用获取库存
    initSetGoodsStok(){
      var self=this;

      if(!self.isFirstInit && !self.isFirstInitIng)return;
      var ids=self.getSelectIdsAll();
      if(ids && ids.length<1){
        return;
      }
      self.isFirstInitIng=false;
      var data=ids;
      self.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/getRefreshUsed',data,function(response){
        if(response.body.result){
          if(response.body.content){
            // self.$message.success("操作成功");
            self.setGoodsStokAll(response.body.content)
            self.isFirstInit=false;
            self.isFirstInitIng=true;
          }

        }else{
          self.$message.error(response.body.msg)
        }
      },e=>{
        self.$message.error(e)
      })

    },
		//获取当前用户人员类型
		checkUserType (){
			this.ajax.get('/user-web/api/userPerson/getUserPerson/' + this.getEmployeeInfo('personId'), res => {
				if(res.body.content.type === 'PURCHASE_DEALER'||res.body.content.type === 'DEALER'){
					this.typeActive = true
				}else{
					this.typeActive = false
				}
			})
		},
		// 是否解密订单
		ifDecryption(){
			// console.log(row);
			let self = this;
			if(!self.form.encryption_add_id){
				return;
			}
			let data ={
				"addressId": self.form.encryption_add_id?self.form.encryption_add_id:self.form.address_id,
			  }
			this.ajax.postStream("/kingdee-web/api/end/ifDecryption", data	, res => {
				var obj = res.body;
				// console.log(obj)
				if(obj.result){

					// callback && callback(obj);
					if(!!obj.content){
						self.ifDecrypt = true;
						// if(self.params.addressObj.info_from  =="TAOBAO"){
						// self.submitData.source_info_encryption = self.params.addressObj.info_encryption
						// }
					}
				}else{
					// params.callback(false,obj.msg);
				}
			});
		},
    /**
    *设置当前用户相对于单据来说是属于哪种角色
    *null->新建单据装态，属于业务锁定，1为业务锁定，2为文员锁定，3为其它
    ***/
    /*setCurrentUserType(){
    	//TODO,还有一个代理
    	var userId = this.getEmployeeInfo('id');
    	var businessId = this.form.business_man_id;//业务锁定ID
    	var clerkId = this.form.clerk_lock_id;//文员锁定IDc
    	//先判断单据处理编辑还是新建状态
    	this.currentUserType = !businessId?null:!clerkId?businessId == userId?1:3:clerkId == userId?2:3;
    	//this.clerkDisabled = !(this.currentUserType == 2);
    }*/

	},
  watch:{
    'form.province':function(newVal,oldVal){
        this.getThreeInfo();
    },
    'form.city':function(newVal,oldVal){
      this.getThreeInfo();
    },
    'form.area':function(newVal,oldVal){
      this.getThreeInfo();
    },
    'goodsList':function(newval,oldval){
        if(newval && newval.length==this.oldGoodsList.length && this.oldGoodsList.length>0){
          this.initSetGoodsStok();
        }

    },
    'form.business_remark':function(newval,oldval){
    	console.log('asdlfkjsdlfkjsd');
    	this.rejectList.map((a,b)=>{
    		if(!a.id){
    			a.biz_note = newval;
    		}
    	})
    }
  },
	mounted (){
			this.checkUserType ();
      this.getDisabledOption()
	    this.getAddress((data)=>{
	      this.province = '';
	      this.province = data;
	    });

	    if(this.params.id){
	        this.isFirstEidt=true;
	      	this.getDetailInfo(this.params.id,"FIRST")

	    }
	    if(!this.params.id){
	    	console.log('看看是什么东西');
	    	this.initStatus();
	    }
    	//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
			this.initStatus();
		});
	},
	components: {
		supplyBadGoods,
    	rejectInfo
	}
}
</script>

<style module>
.btn-select {
	width: 109px;
	vertical-align: middle;
}
.btn-select input {
	color: #fff;
    background-color: #20a0ff;
    border-color: #20a0ff;
}
.btn-select :global(.el-input__inner){
	height: 18px;
}
.textarea-style :global(.el-form-item__content) {
    height: auto;
}
</style>
