<template>
	<div class="mgt10">
    <el-row	:gutter='40' class="mgt10">
      <h3 style="padding-left:20px">智能粘贴地址</h3>
    </el-row>
    <el-row	:gutter='40' class="mgt10">
				<el-col :span='24'>
          <el-input
            type="textarea"
            :rows="4"
            :maxlength="500"
            placeholder="粘贴或输入文本，点击'一键识别'自动识别收货人信息。如浙江省杭州市余杭区文一西路969号小邮局,186XXXX1234，淘小宝"
            v-model="addressData">
          </el-input>
        </el-col>
    </el-row>
    <el-row	:gutter='40' class="mgt10">
				<el-col :span='24'>
          <div class="address-btn">
            <el-button  size='mini' @click="clearAddress" icon="delete">清空</el-button>
            <el-button type='primary' size='mini' @click="getAddress" :loading="readLoading">一键识别</el-button>
          </div>
        </el-col>
    </el-row>
		<el-form label-position="right" label-width="95px" :model="submitData" :rules="rules" ref="submitData">
			<el-row	:gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="收货人" prop="receiver_name">
						<el-input size='mini' v-model="submitData.receiver_name" :disabled="unDecrypt"></el-input>
						<el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="收货人固话" prop="receiver_phone">
						<el-input size='mini' v-model="submitData.receiver_phone" :disabled="unDecrypt"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="收货人手机" prop="receiver_mobile">
						<el-input size='mini' v-model="submitData.receiver_mobile" :disabled="unDecrypt"></el-input>
						<el-tooltip v-if='rules.receiver_mobile[0].isShow' class="item" effect="dark" :content="rules.receiver_mobile[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="40" class="mgt10">
				<el-col :span='8'>
					<el-form-item label="省" prop="receiver_state">
						<el-select placeholder="请选择" size='mini' filterable v-model="submitData.receiver_state" @change="selectProvince" :disabled="unDecrypt">
						    <el-option
						      v-for="(value,key) in provinceObj"
						      :label="value"
						      :value="parseInt(key)"  :key='key'>
						    </el-option>
						</el-select>
						<el-tooltip v-if='rules.receiver_state[0].isShow' class="item" effect="dark" :content="rules.receiver_state[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="市" prop="receiver_city">
						<el-select placeholder="请选择" size='mini' filterable v-model="submitData.receiver_city" @change="selectCity" :disabled="unDecrypt">
						    <el-option
						       v-for="(value,key) in cityObj"
						      :label="value"
						      :value="parseInt(key)" :key='key'>
						    </el-option>
						</el-select>
						<el-tooltip v-if='rules.receiver_city[0].isShow' class="item" effect="dark" :content="rules.receiver_city[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="区" prop="receiver_district">
						<el-select placeholder="请选择" size='mini' filterable v-model="submitData.receiver_district" @change="selectArea" :disabled="unDecrypt">
						    <el-option
						       v-for="(value,key) in areaObj"
						      :label="value"
						      :value="parseInt(key)" :key='key'>
						    </el-option>
						</el-select>
						<el-tooltip v-if='rules.receiver_district[0].isShow' class="item" effect="dark" :content="rules.receiver_district[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row	:gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="街道">
						<el-select placeholder="请选择" size='mini' filterable v-model="submitData.receiver_street"  :disabled="unDecrypt">
						    <el-option
						       v-for="(value,key) in streetObj"
						      :label="value"
						      :value="parseInt(key)" :key='key'>
						    </el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="详细地址" prop="receiver_address">
						<el-input size='mini' v-model="submitData.receiver_address" :disabled="unDecrypt"></el-input>
						<el-tooltip v-if='rules.receiver_address[0].isShow' class="item" effect="dark" :content="rules.receiver_address[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter='40' class="mgt10">



			</el-row>
			<el-row :gutter='40' class="mgt10">
				<el-col :span="24" style="text-align:right;">
          <el-button type='primary' size='mini' @click="decryptData" v-if="needDecrypt" :loading="decryptLoading">手动修改</el-button>
					<el-button type='success' size='mini' @click="preSave('submitData')">保存</el-button >
					<el-button type='warning' size='mini' @click="closeWindow">取消</el-button>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
import validate from '@common/validate.js'
import smart from 'address-smart-parse';
export default {
	props:["params"],
	data(){
		var self = this;
		return {
			provinceObj:{},
			cityObj:{},
			areaObj:{},
			streetObj:{},
      addressData:'',
      readLoading:false,
      readProvinceLocking:false,// 识别锁住省市区下拉框方法
      readCityLocking:false,// 识别锁住省市区下拉框方法
      readAreaLocking:false,// 识别锁住省市区下拉框方法
      needDecrypt:false,// 是否需要解密
      decryptLoading:false,// 解密中
      ifDecryptSuccess:false,// 解密是否成功
			submitData:{
				cust_id:self.params.cust_id,
				receiver_name:"",
				receiver_phone:"",
				receiver_mobile:"",
				receiver_state:"",
				receiver_city:"",
				receiver_district:"",
				receiver_street:"",
				receiver_address:"",
			},
			// ifTransit:true,
			rules:{
				receiver_name:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请输入收货人姓名",
					trigger:"change"
				}),
				receiver_mobile:validate.mobileIncludeVirtual({
					self:self,
					required:true
				}),
				receiver_state:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请选择所在省",
					trigger:"change"
				}),
				receiver_city:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请选择所在市",
					trigger:"change"
				}),
				receiver_district:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请选择所在区",
					trigger:"change"
				}),
				receiver_address:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请输入详细地址",
					trigger:"change"
				})
			},
		}
	},
	methods:{
		closeWindow(){
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
    // 判断是否需要解密
    ifDecrypt(){
			// console.log(this.selectedData[0])
			this.ajax.postStream('/kingdee-web/api/end/ifDecryption',{
        "addressId": this.submitData.addressId,
      }, res => {
				this.needDecrypt=res.body.content;
			})
    },
    decryptData(){
      try{
        this.decryptLoading=true;
        const data ={
					"addressId": this.submitData.addressId,
					"mergeTradeNo": this.params.merge_trade_no,
        }
        this.ajax.postStream('/kingdee-web/api/end/decryption',data, res => {
          console.log('res', res)
          if(res.body.result){
            this.submitData.receiver_address = res.body.content.receiverAddress;
			this.submitData.receiver_mobile = res.body.content.receiverMobile;
			this.submitData.receiver_phone = res.body.content.receiverPhone;
			this.submitData.receiver_name = res.body.content.receiverName;
            this.ifDecryptSuccess=true;
          }else{
            this.submitData.receiver_name="";
            this.submitData.receiver_mobile="";
            this.submitData.receiver_phone="";
            this.submitData.receiver_address="";
            this.ifDecryptSuccess=true;
            this.$message.warning(res.body.msg||'解密失败，请补充数据')
          }
          this.decryptLoading=false;
        })
      }catch{
        this.decryptLoading=false;
      }
    },
		// 地区
		getAreaCode(code,key){
			var self = this;
			var url = "/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId";
			if(!code) return;
			self.ajax.postStream(url,code,function(response){
				if(response.body.result){
					self[key] = response.body.content
				}
			})
		},
		selectProvince(address){//选择省份，获得市级信息
			var self = this
      // 识别中锁住下拉
      if(this.readProvinceLocking){
        this.readProvinceLocking=false;
        return
      }
			if(!address) return
			self.getAreaCode(address,"cityObj")
			self.submitData.receiver_city = ''
			self.submitData.receiver_district = ''
			self.submitData.receiver_street = ''
		},
		selectCity(address){//选择市级，获得区级信息
			if(!address) return
      // 识别中锁住下拉
      if(this.readCityLocking){
        this.readCityLocking=false;
        return
      }
			var self = this;
      self.getAreaCode(address,"areaObj")
			self.submitData.receiver_district = ''
			self.submitData.receiver_street = ''
		},
		selectArea(address){//选择区级，获得街道信息
			if(!address) return
       // 识别中锁住下拉
       if(this.readAreaLocking){
        this.readAreaLocking=false;
        return
      }
			var self = this
      self.getAreaCode(self.submitData.receiver_district,"streetObj")
			self.submitData.receiver_street = ''
		},
		/*
		添加地址做保存时校验不允许收货人存在先生小姐等字符。
		*/
		preSave(formName){
      if (!this.ifDecryptSuccess && this.needDecrypt) {
          this.$message.warning('请先点击【手动修改】解密');
          return;
      }
			var self = this
			self.$refs[formName].validate((valid) => {
				if(!valid) return
				self.save()
			});
		},
		save(){
			var self = this;

			self.submitData.receiver_state_name = self.provinceObj[self.submitData.receiver_state]||self.submitData.receiver_state
			self.submitData.receiver_city_name = self.cityObj[self.submitData.receiver_city]||self.submitData.receiver_city
			self.submitData.receiver_district_name = self.areaObj[self.submitData.receiver_district]||self.submitData.receiver_district
			self.submitData.receiver_street_name = self.streetObj[self.submitData.receiver_street]||self.submitData.receiver_street
      if(self.params&&self.params.addressObj&&self.submitData&&self.submitData.receiver_mobile===self.params.addressObj.receiver_mobile){
        self.submitData.oaid = self.params.addressObj.oaid ;
        self.submitData.expire_time = self.params.addressObj.expire_time
      }
      const formatSubmitData=_.cloneDeep(self.submitData)
      delete formatSubmitData.addressId;
			var url = "/afterSale-web/api/aftersale/addReceriverInfo";
					self.ajax.postStream(url,formatSubmitData,function(response){
						if(response.body.result){
							let data = self.submitData;
							data.fid = response.body.content;
							self.params.callback(data);
							self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
							self.$root.eventHandle.$emit("close_copy_address");
						}else{
							self.$message.error(response.body.msg || '')
						}
					});

			return false;
		},
    clearAddress(){
      this.addressData=''
    },
    async getAddress(){
      if (!this.addressData) {
        this.$message.warning('请粘贴或输入地址');
        return;
      }
      try{
        this.readProvinceLocking=true;
        this.readCityLocking=true;
        this.readAreaLocking=true;
        this.readLoading=true;
        const result = smart(this.addressData);
        const { city, address, name, phone, province, county, street } = result;
        if (!city || !address) {
          this.$message.warning('识别失败，请重新输入');
          this.readLoading=false;
          return;
        }
        const params={
          cityName: `${city}${county || ''}${street || ''}`,
          addressDetail: `${province || ''}${city || ''}${county || ''}${
            street || ''
          }${address}`,
        }
        this.ajax.postStream('/external-web/api/AddressAndEncry/supplyAddress',params,async res=>{
					if(res.body.result){
            const {receiverState,receiverCity,receiverDistrict,receiverStreet,addressName}=res.body.content;
            await Promise.all([this.getAreaCode(1,"provinceObj"),this.getAreaCode(receiverState,"cityObj"),
            this.getAreaCode(receiverCity,"areaObj"),
            this.getAreaCode(receiverDistrict,"streetObj")])
            this.submitData.receiver_state=receiverState
            this.submitData.receiver_city=receiverCity
            this.submitData.receiver_district=receiverDistrict
            this.submitData.receiver_street=receiverStreet
            this.submitData.receiver_address=address
            this.submitData.receiver_name=name;
            this.submitData.receiver_mobile=phone;
            this.readLoading=false;
            this.needDecrypt = false;
            this.ifDecryptSuccess = false;
					}else{
            this.$message.error(res.body.msg||'识别失败，请重新输入');
            this.readLoading=false;
          }
				})
      }catch{
        this.readLoading=false;
      }
    }
	},
  computed:{
    unDecrypt(){
      return this.needDecrypt&&!this.ifDecryptSuccess
    }
  },
	mounted(){
		var self = this;
		if(self.params.ifCopy){//判断是否复制新增
      const {receiver_state,receiver_city,receiver_district,receiver_street,fid}=self.params.addressObj;
      self.submitData.receiver_name = self.params.addressObj.receiver_name
			self.submitData.receiver_phone = self.params.addressObj.receiver_phone
			self.submitData.receiver_mobile = self.params.addressObj.receiver_mobile
			self.submitData.receiver_address = self.params.addressObj.receiver_address
			self.submitData.addressId = fid
      this.readProvinceLocking=true;
      this.readCityLocking=true;
      this.readAreaLocking=true;
      this.ifDecrypt();
      Promise.all([this.getAreaCode(1,"provinceObj"),this.getAreaCode(receiver_state,"cityObj"),
            this.getAreaCode(receiver_city,"areaObj"),
            this.getAreaCode(receiver_district,"streetObj")]).then(()=>{
              this.submitData.receiver_state=receiver_state
            this.submitData.receiver_city=receiver_city
            this.submitData.receiver_district=receiver_district
            this.submitData.receiver_street=receiver_street
            })

		} else {
			self.getAreaCode(1,"provinceObj");//新增
		}
	},
	destroyed(){
	}
}
</script>
<style lang="stylus" scoped>
.address-btn{
  display:flex;
  justify-content:flex-end;
  margin-bottom:10px;
}
</style>
