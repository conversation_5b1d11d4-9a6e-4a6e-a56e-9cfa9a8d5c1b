<!--补件货方案-->
<template>
  <div class="xpt_pmm">
    <el-form
      label-position="right"
      label-width="120px"
      :model="info"
      :rules="rules"
      ref="info"
    >
<!--      <div style="margin-left: 100px;margin-bottom: 10px">如需安装/维修，请在此处选择上门服务类型，提交后会自动生成服务单，补件签收后系统会自动提交服务单，无法再单独下补件安装的服务单</div>-->
      <el-row :gutter="40">
        <el-col :span="6">
          <el-form-item label="方案编号">
            <!-- <el-input size='mini' disabled v-model="questionData[0]._after_plan_no" placeholder="系统自动生成"></el-input> -->
            <el-input
              size="mini"
              disabled
              v-model="after_plan_no"
              placeholder="系统自动生成"
            ></el-input>
          </el-form-item>
          <el-form-item label="上门服务类型" prop="if_door">
            <el-select
              size="mini"
              placeholder="类型"
              v-model="info.if_door"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
            >
              <el-option label="安装" value="INSTALL"></el-option>
              <el-option label="维修" value="REPAIR"></el-option>
              <el-option label="无需" value="NONEED"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="日期" prop="after_plan_date">
            <el-date-picker
              type="date"
              placeholder="选择日期"
              size="mini"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
              :editable="false"
              v-model="info.after_plan_date"
            ></el-date-picker>
            <el-tooltip
              v-if="rules.after_plan_date[0].isShow"
              class="item"
              effect="dark"
              :content="rules.after_plan_date[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item
            label="客户承担费用"
            prop="customer_fee"
            style="white-space: nowrap"
          >
            <el-input
              size="mini"
              type="number"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
              v-model="info.customer_fee"
              min="0"
              @blur="
                () => (info.customer_fee = Number(info.customer_fee).toFixed(2))
              "
              readonly icon="search" :on-icon-click="() => selectCustomerFee()"
            ></el-input>
            <el-tooltip
              v-if="rules.customer_fee[0].isShow"
              class="item"
              effect="dark"
              :content="rules.customer_fee[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="运费方式" prop="delivery_type">
            <el-select
              size="mini"
              placeholder="类型"
              v-model="info.delivery_type"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
            >
              <el-option label="现付" value="NOW_PAY"></el-option>
              <el-option label="到付" value="ARRIVE_PAY"></el-option>
            </el-select>
            <el-tooltip
              v-if="rules.delivery_type[0].isShow"
              class="item"
              effect="dark"
              :content="rules.delivery_type[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="方案状态">
						<el-input  size='mini' :value="{
							CREATE: '创建',
							SUBMIT: '提交',
							RECALL: '撤回',
							RETRACT: '撤回',
						}[info.status]" disabled></el-input>
					</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="货运方式">
            <xpt-select-aux
              v-model="info.transport_type"
              aux_name="bjd_hyfs"
              disabled
              :disabledOption="disabledOption"
            ></xpt-select-aux>
            <!-- <el-tooltip v-if='rules.transport_type[0].isShow' class="item" effect="dark" :content="rules.transport_type[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip> -->
          </el-form-item>
          <el-form-item label="允许修改货运方式">
            <el-switch
              v-model="info.if_modify_logistics"
              disabled
              on-text="是"
              off-text="否"
              on-value="Y"
              off-value="N"
            ></el-switch>
          </el-form-item>
          <el-form-item label="是否加购订单">
            <el-switch
              v-model="info.if_repurchase_order"
              on-text="是"
              off-text="否"
              on-value="Y"
              off-value="N"
              :disabled="!!info.id"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40" style="height: 50px">
        <el-col :span="24">
          <el-form-item label="备注信息" prop="remark">
            <el-input
              type="textarea"
              v-model="info.remark"
              :maxlength="255"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
              style="width: 98%"
            ></el-input>
            <el-tooltip
              v-if="rules.remark[0].isShow"
              class="item"
              effect="dark"
              :content="rules.remark[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="商品信息" style="margin: 10px 0">
            <el-button
              type="primary"
              size="mini"
              @click="addGoods"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
              >增加商品</el-button
            >
            <el-button
              type="danger"
              size="mini"
              @click="del"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
              >删除</el-button
            >
            <el-button type="primary" size="mini" @click="updateStore"
              >刷新可用库存</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="uploadFun"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
            >
              上传图片
              <xpt-upload
                :ifClickUpload="ifClickUpload"
                :dataObj="uploadData"
              ></xpt-upload>
            </el-button>
            <el-button type="success" size="mini" @click="pictureFun"
              >查看或者下载附件</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      :data="goods.length ? goods.concat('') : goods"
      border
      @selection-change="select"
      ref="goods"
      class="xpt_pmm_scroll"
      @row-click="rowClick"
      :row-class-name="tableRowClassNameRepair"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        align="center"
        width="40"
        :selectable="(row, index) => index < goods.length"
      ></el-table-column>
      <el-table-column label="序号" type="index" width="30">
        <template slot-scope="scope"
          ><div class="table-index">
            {{ scope.$index < goods.length ? scope.$index + 1 : "" }}
          </div></template
        >
      </el-table-column>
      <el-table-column
        label="商品编码"
        prop="question_goods_code"
        show-overflow-tooltip
        width="130"
      ></el-table-column>
      <el-table-column
        label="物料编码"
        prop="material_code"
        show-overflow-tooltip
        width="140"
      ></el-table-column>
      <el-table-column
        label="物料名称"
        prop="material_name"
        show-overflow-tooltip
        width="150"
      ></el-table-column>
      <el-table-column
        label="规格描述"
        prop="description"
        show-overflow-tooltip
        width="200"
      ></el-table-column>
      <el-table-column
        label="单位"
        prop="units"
        show-overflow-tooltip
        width="40"
      ></el-table-column>
      <el-table-column
        label="是否为赠品"
        prop="present_flag"
        show-overflow-tooltip
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.present_flag == "1" ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column label="补件数量" show-overflow-tooltip width="100">
        <template slot-scope="scope">
          <el-input
            v-if="scope.$index < goods.length"
            size="mini"
            style="width: 80%"
            v-model="scope.row.number"
            type="number"
            min="0"
            :disabled="info.status === 'SUBMIT' || !otherInfo.canEditSolutions"
            @change.native="
              (e) =>
                _checkValIsRight(
                  e,
                  scope.row.number > 0 && !/\./.test(scope.row.number)
                )
            "
          ></el-input>
          <el-tooltip
            class="item table-check"
            effect="dark"
            content="请填写正确的补件数量"
            placement="right-start"
            popper-class="xpt-form__error"
            style="color: #ff4949; display: none"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="补件要求"
        :class-name="$style['row-height'] + ' ' + $style['th-required']"
        show-overflow-tooltip
        width="200"
      >
        <template slot-scope="scope">
          <!-- <el-input
		    			v-if="scope.$index < goods.length"
		    			type="textarea"
		    			:maxlength="255"
		    			:disabled="info.status === 'SUBMIT' || !otherInfo.canEditSolutions"
		    			v-model="scope.row.supply_request"
		    			@blur="() => scope.row.supply_request = scope.row.supply_request.replace(/\n/g, '')"
		    		></el-input> -->
          <el-input
            v-if="scope.$index < goods.length"
            type="textarea"
            disabled
            :value="autoMakeApplyDesc(scope.row)"
          ></el-input>
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" :class-name="$style['row-height']" width="200">
		    	<template slot-scope="scope">
		    		<el-input type="textarea" style="width:100%" :maxlength="255" :disabled="info.status === 'SUBMIT'" v-model="scope.row.remark"></el-input>
		    	</template>
		    </el-table-column> -->
      <!-- <el-table-column label="参考价" prop="supply_code" show-overflow-tooltip width="40">
		    	<template slot-scope="scope">
		    		{{ scope.$index < goods.length ? scope.row.supply_code : ('合计' + totalCost) }}
		    	</template>
		    </el-table-column> -->
      <el-table-column label="客户支付金额" show-overflow-tooltip width="150">
        <template slot-scope="scope">
          <div v-if="scope.$index < goods.length">
            <el-input
              type="number"
              size="mini"
              style="width: 80%"
              min="0"
              v-model="scope.row.customer_fee"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
              @blur="
                () => {
                  scope.row.customer_fee = Number(
                    scope.row.customer_fee
                  ).toFixed(2);
                  calc();
                }
              "
              @change.native="
                (e) => _checkValIsRight(e, scope.row.customer_fee >= 0)
              "
            ></el-input>
            <el-tooltip
              class="item table-check"
              effect="dark"
              content="客户支付金额不能为负数"
              placement="right-start"
              popper-class="xpt-form__error"
              style="color: #ff4949; display: none"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </div>
          <div v-else>合计{{ totalCustomer }}</div>
        </template>
      </el-table-column>
      <el-table-column label="是否停产" width="55">
        <template slot-scope="scope">{{
          { Y: "是", N: "否" }[scope.row.if_stop_produce]
        }}</template>
      </el-table-column>
      <el-table-column
        label="物料BOM版本"
        prop="bom_version"
        show-overflow-tooltip
        width="150"
      ></el-table-column>
      <el-table-column
        label="供应商"
        :class-name="$style['th-required']"
        prop="supplier_company"
        show-overflow-tooltip
        width="150"
      >
        <template slot-scope="scope">
          <el-input
            v-if="
              scope.$index < goods.length &&
              !(info.status === 'SUBMIT' || !otherInfo.canEditSolutions)
            "
            size="mini"
            style="width: 100%"
            v-model="scope.row.supplier_company"
            icon="search"
            :disabled="!ifLinsyOrder(scope.row)"
            :on-icon-click="() => selectSupplierCompany(scope.row)"
            readonly
          ></el-input>
          <span v-else>{{ scope.row.supplier_company }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="可用库存"
        prop=""
        show-overflow-tooltip
        width="70"
      >
        <template slot-scope="scope">{{
          validStock[scope.row.material_code]
        }}</template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-form
        label-position="right"
        label-width="120px"
        :model="info"
        :rules="rules"
        ref="addressInfo"
      >
        <el-form-item label="确认信息">
          <el-button
            type="primary"
            size="mini"
            style="margin-top: 5px; margin-left: 5px"
            @click="choiceAddress"
            :disabled="!otherInfo.canEditSolutions || info.status === 'SUBMIT'"
            >修改客户地址</el-button
          >
        </el-form-item>
        <div style="overflow: hidden; width: 100%">
          <el-form-item
            label="取货地址"
            prop="province"
            style="float: left; width: auto"
          >
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="info.province"
              @change="changeProvice"
              :disabled="!canEditAddress"
            >
              <el-option
                v-for="(value, key) in province"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label=""
            prop="city"
            style="float: left; width: auto; margin-left: -110px"
          >
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="info.city"
              @change="changeCity"
              :disabled="!canEditAddress"
            >
              <el-option
                v-for="(value, key) in city"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label=""
            prop="area"
            style="float: left; width: auto; margin-left: -110px"
          >
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="info.area"
              @change="changeArea"
              :disabled="!canEditAddress"
            >
              <el-option
                v-for="(value, key) in area"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label=""
            style="float: left; width: auto; margin-left: -110px"
          >
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="info.street"
              :disabled="!canEditAddress"
            >
              <el-option
                v-for="(value, key) in street"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="详细信息" prop="receiver_addr">
          <el-input
            size="mini"
            v-model="info.receiver_addr"
            style="width: 80%"
            :disabled="!canEditAddress"
          ></el-input>
          <el-tooltip
            v-if="rules.receiver_addr[0].isShow"
            class="item"
            effect="dark"
            :content="rules.receiver_addr[0].message"
            placement="right-start"
            popper-class="xpt-form__error"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>

        <el-form-item label="收货人电话" prop="reveiver_phone">
          <el-input
            size="mini"
            v-model="info.reveiver_phone"
            :disabled="!canEditAddress"
          ></el-input>
          <el-tooltip
            v-if="rules.reveiver_phone[0].isShow"
            class="item"
            effect="dark"
            :content="rules.reveiver_phone[0].message"
            placement="right-start"
            popper-class="xpt-form__error"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="收货人姓名" prop="receiver_name">
          <el-input
            size="mini"
            v-model="info.receiver_name"
            icon="linshi-eye-open"
            :on-icon-click="decryption"
            :disabled="!canEditAddress"
          ></el-input>
          <!-- <xpt-eye-switch v-model="info.receiver_name" hideType="name" ref="receiver_name" :readonly='true' :disabled="!canEditAddress" :hideBorder="true" :isCallback="true" :aboutNumber="otherInfo.merge_trade_no" @onOpenHideData="decryption"></xpt-eye-switch> -->
          <el-tooltip
            v-if="rules.receiver_name[0].isShow"
            class="item"
            effect="dark"
            :content="rules.receiver_name[0].message"
            placement="right-start"
            popper-class="xpt-form__error"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>
      </el-form>
      <div style="margin: 10px 0; text-align: center">
        <!-- <el-button type='primary' size='mini' @click="save" :disabled="!canSave || info.status === 'SUBMIT' || !otherInfo.canEditSolutions">保存</el-button>
		  		<el-button type='primary' size='mini' @click="submit" :disabled="!canSubmit || info.status === 'SUBMIT' || !otherInfo.canEditSolutions">提交</el-button>
		  		<el-button type='primary' size='mini' @click="withdraw" :disabled="!canWithdraw || info.status !== 'SUBMIT' && otherInfo.canEditSolutions">撤回</el-button> -->
        <el-button
          type="primary"
          size="mini"
          @click="save"
          :disabled="
            !otherInfo.canEditSolutions
              ? true
              : !canSave || info.status === 'SUBMIT'
          "
          >保存</el-button
        >
        <el-button
          type="primary"
          size="mini"
          @click="preSubmit"
          :disabled="
            !otherInfo.canEditSolutions
              ? true
              : !canSubmit ||
                !info.id ||
                !info.status ||
                info.status === 'SUBMIT'
          "
          >提交</el-button
        >
        <el-button
          type="primary"
          size="mini"
          @click="withdraw"
          :disabled="
            !otherInfo.canEditSolutions
              ? true
              : !canWithdraw || info.status !== 'SUBMIT'
          "
          >撤回</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :disabled="!otherInfo.canEditSolutions"
          @click="addNew"
          >新增</el-button
        >
      </div>
    </el-row>
  </div>
</template>
<script>
import addressInfo from "./model/addressInfo";
import VL from "@common/validate.js";
export default {
  mixins: [addressInfo],
  props: {
    selectedData: {
      type: Array,
      default() {
        return [];
      },
    },
    otherInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    editOtherInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    // 是否添加下推产品咨询按钮
    isPushDownConsult: {
      type: Boolean,
      default: false,
    },
    questionList: Object,
    copySelectData: Object,
    editQuestion: Object,
    operatParams: Object,
    afterGroup: Array,
    allQuestions: Array,
    isReSaleOrder: Boolean,
  },
  data() {
    // this.questionData = this.selectedData//子问题详情，比this.init(null, 'init')先初始化
    // if(!this.questionData[0]) this.questionData[0] = {}
    return {
      selectFeeList: [], // 历史选择的客户承担费用
      questionData: [],
      goodsMaterialIds: [],
      goodsMapQuestionList: [], //找到商品明细对应哪个子问题
      afterPlanGroupId: "",
      oldGoods: [],
      ...this.init(null, "init"),
      after_plan_no: null,
      selectedGoods: [],
      ifClickUpload: false,
      uploadData: {},
      validStock: {}, //material_id和库存对应关系
      after_plan_group_id: "", //方案组id
      // planStatus: {},
      // nowPlanStatus: 'CREATE',//当前选中子问题的方案状态

      //补件成本，客户支付金额
      totalCost: 0,
      totalCustomer: 0,
      rules: [
        "transport_type",
        "after_plan_date",
        "delivery_type",
        "remark",
      ].reduce(
        (obj, key) => {
          obj[key] = VL.isNotBlank({
            required: true,
            self: this,
          });
          return obj;
        },
        {
          customer_fee: VL.maxThreeDecimals({
            self: this,
            trigger: "change",
          }),
        }
      ),

      //地址是否已经验证通过了
      addressIsPass: true,

      //拿到当前的表头信息,
      orginalTableData: null,
      canSave: true,
      canSubmit: true,
      canWithdraw: true,
    };
  },
  methods: {
    // 选择客户承担费用
    selectCustomerFee() {
      const self = this;
      if(self.info?.status === 'SUBMIT' || !self.otherInfo?.canEditSolutions) {
        return false
      }
      self.$root.eventHandle.$emit('alert', {
        title: '客户承担费用',
        style: 'width:950px;height:600px',
        component: () => import('@components/after_solutions/selectCustomerFee.vue'),
        params: {
          info: self.info,
          selectData: self.selectedData,
          selectFeeList: self.selectFeeList,
          callback: data => {
            if(data?.feeSelects?.length > 0) {
              self.selectFeeList = data.feeSelects
              self.$set(self.info, 'customer_fee', data?.totalFee)
            }

          }
        },
      })
    },
    ifLinsyOrder(row) {
      let isLinsy = false;
      let order = this.selectedData.find(
        (item) => item.material_code == row.question_goods_code
      );
      let ls_suppler = __AUX.get("ls_suppler");
      if (!order) {
        isLinsy = true;
        return true;
      }
      ls_suppler.forEach((item) => {
        // console.log(order,item);
        if (
          !!order.suppiler_name &&
          item.status == 1 &&
          order.suppiler_name.indexOf(item.name) != -1
        ) {
          isLinsy = true;
        }
      });
      if (!order.suppiler_name) {
        isLinsy = true;
      }
      return isLinsy;
      // return (order.suppiler_name.indexOf('林氏家居') != -1||order.suppiler_name.indexOf('林氏木业') != -1)
    },
    autoMakeApplyDesc(row) {
      return (
        // 下面这个判断只对新增或已经按照模板生成的进行自动生成，对以前的数据不进行自动生成
        !row.supply_request ||
          /^(补发:.+，)?(商品规格:.+，)?(物料名称:.+，)?(物料规格:.+，)?数量:\d+(\.\d+)?$/.test(
            row.supply_request
          )
          ? (row.supply_request =
              (row.question_goods_name
                ? "补发:" + row.question_goods_name + " ，"
                : "") +
              (row.question_goods_desc
                ? "商品规格:" + row.question_goods_desc + " ，"
                : "") +
              (row.material_name
                ? "物料名称:" + row.material_name + " ，"
                : "") +
              (row.description ? "物料规格:" + row.description + " ，" : "") +
              ("数量:" + (Math.abs(row.number) || 0)))
          : row.supply_request
      );
    },
    /**
     *跳到工作台
     ***/
    addNew() {
      let params = JSON.parse(JSON.stringify(this.otherInfo));
      /*after_plan_id:null,//编辑,方案ID
		          after_question_id:null,//方案所挂的问题ID
		          after_plan_group_id:null,//方案组ID*/
      if (params.hasOwnProperty("after_plan_id")) {
        delete params.after_plan_id;
      }
      if (params.hasOwnProperty("after_question_id")) {
        delete params.after_question_id;
      }
      if (params.hasOwnProperty("after_plan_group_id")) {
        delete params.after_plan_group_id;
      }
      this.$root.eventHandle.$emit("creatTab", {
        name: "售后方案",
        url: "after_solutions/solutions",
        params: {
          afterOrderInfo: params,
        },
        component: () => import("@components/after_solutions/solutions"),
      });
    },
    //对页面需要认证的内容认证，required：是否必填，msg：提示信息
    VLFun(required, msg) {
      return VL.isNotBlank({
        required: required,
        self: this,
        msg: msg,
      });
    },
    /**
     *设置问题对应的方案
     ***/
    tableRowClassNameRepair(row, rowIndex) {
      // console.log('asdkfjlksdf');
      let id =
        this.copySelectData.question_sub_id ||
        this.copySelectData.originalSubQuestionId;
      if (!this.copySelectData || !id) return "";

      let question_sub_id = row.question_sub_id;

      // console.log('this.copySelectData',this.copySelectData);
      // console.log('this.question_sub_id',question_sub_id);

      if (question_sub_id == id) return "pmm_blue22";
      return "";
    },
    /**
     *单击某行执行的数据
     **/
    rowClick(row, event, column) {
      console.log("column", column);
      this.currentRowselectGoods = row;
      this.concatSelectReturnAndExchangeGoods();
    },
    /**
     *合并退货，换货商品
     **/
    concatReturnAndExchangeGoods() {
      let goods = this.goods;
      this.$emit("allGoodsmatchQuestions", goods);
    },

    /**
     *合并已选中的明细行
     ***/
    concatSelectReturnAndExchangeGoods() {
      this.$emit("selectGoodsmatchQuestions", [this.currentRowselectGoods]);
    },

    /**
     *保存校验
     */
    validateIsPass() {
      var bool,
        // ,	whichQuestionNoGoods
        goodsSubIds = this.goods.map((obj) => obj.question_sub_id);

      this.$refs.info.validate((valid) => {
        bool = valid;
      });
      bool &&
        this.$refs.addressInfo.validate((valid) => {
          if (!valid) this.modify();
          bool = valid;
        });

      bool &&
        [].some.call(
          this.$refs.goods.$el.querySelectorAll(".table-check"),
          ($dom) => {
            if ($dom.style.display === "inline-block") {
              bool = false;
              return true;
            }
          }
        );
      // if(!this.questionData[0].if_goods_question) {
      // 	this.$message.error('请选择一个父问题')
      // 	bool = false
      // }else if (
      // 	this.questionData.some((obj, index) => {
      // 		if(/*!obj._after_plan_no &&*/ goodsSubIds.indexOf(obj.question_sub_id) === -1){
      // 			whichQuestionNoGoods = index
      // 			return true
      // 		}
      // 	})
      // ){
      // 	this.$message.error('第' + (whichQuestionNoGoods + 1) + '个勾选的父问题请选择商品明细')
      // 	bool = false
      // }
      if (!this.goods.length) {
        this.$message.error("请添加至少一个商品明细");
        bool = false;
      } else if (
        this.goods.some((obj) => {
          if (!obj.supply_request) {
            this.$message.error("商品明细的补件要求不能为空");
            return true;
          } else if (!obj.supplier_company) {
            this.$message.error("商品明细的供应商不能为空");
            return true;
          }
        })
      ) {
        bool = false;
      } /*else if(this.questionData.length != Array.from(new Set(goodsSubIds)).length){
					//有多少个父问题就有多少方案，先这样控制
					let length = this.questionData.length;
					let subIds = Array.from(new Set(goodsSubIds)).length;
					this.$message.error(length < subIds?'相关的问题没有选择明细行':'多出了明细行没有勾选相应的问题');
					bool = false
				}*/

      return bool;
    },

    /**
     *撤回
     */
    withdraw() {
      var _planId;
      console.log("asdlkfjlksdf");
      /*this.allQuestions.some(obj => {
					if(obj.question_sub_id === this.copySelectData.question_sub_id){
						_planId = obj._after_plan_id
						return true
					}
				})*/
      if (!this.orginalTableData) return;
      _planId = this.orginalTableData.id;
      console.log(
        "this.allQuestions123",
        this.allQuestions,
        this.copySelectData,
        _planId
      );
      // return
      this.canWithdraw = false;
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/ticketSupply/withdrawPlan",
        { after_plan_id: _planId },
        (res) => {
          if (res.body.result) {
            this.getPlanAndRender();
          } else {
            this.$message.error(res.body.msg);
          }
          this.canWithdraw = true;
        },
        () => {
          this.canWithdraw = true;
        }
      );
    },
    //获取方案组id
    getAfterPlanGroupId(cb) {
      if (this.afterPlanGroupId) {
        cb();
      } else {
        this.ajax.postStream(
          "/afterSale-web/api/aftersale/plan/getPlanGroupId",
          {},
          (d) => {
            if (d.body.result) {
              this.afterPlanGroupId = d.body.content;
              cb();
            }
          }
        );
      }
    },
    /**
     *设置最新的子问题ID
     ***/
    /*setNestQuestionSubId(){
				if(!this.questionData || !this.questionData.length) return;
				this.questionData.map((a,b)=>{
					let parent_question_id = a.parent_question_id;
					this.allQuestions.map((c,d)=>{
						let parent_question_id2 = c.parent_question_id;
						if(parent_question_id == parent_question_id2){
							a.question_sub_id = c.question_sub_id;
						}
					})
				})
			},*/
    filterIsDeleteHasIdGoods() {
      /*var questionDataSubIds = this.goods.map(obj => obj.question_sub_id);


				var returnObj = {}

				this.oldGoods.forEach(obj => {
					if(obj.id && questionDataSubIds.indexOf(obj.question_sub_id) === -1){
						if(!returnObj[obj.after_plan_no]) returnObj[obj.after_plan_no] = []
						returnObj[obj.after_plan_no].push(obj.id)
					}
				})

				return returnObj*/
      console.log("this.questionData", this.questionData);

      var questionDataSubIds = this.questionData.map(
          (obj) => obj.question_sub_id
        ),
        newGoodsIds = this.goods.map((obj) => {
          let s = questionDataSubIds.indexOf(obj.question_sub_id);
          let n = obj.id;
          console.log("s", s);
          console.log(
            "questionDataSubIds.indexOf(obj.question_goods_id) != -1",
            questionDataSubIds.indexOf(obj.question_sub_id) != -1
          );
          console.log("n", n);
          return (
            questionDataSubIds.indexOf(obj.question_sub_id) != -1 && obj.id
          );
        }),
        returnObj = {};

      this.oldGoods.forEach((obj) => {
        if (obj.id && newGoodsIds.indexOf(obj.id) === -1) {
          if (!returnObj[obj.after_plan_no]) returnObj[obj.after_plan_no] = [];
          returnObj[obj.after_plan_no].push(obj.id);
        }
      });

      return returnObj;
    },

    /**
     *问题对应的明细是否存在
     **/
    hasQuestionDetail(question) {
      let question_sub_id = question.question_sub_id;
      let bool = false;
      this.goods.map((a, b) => {
        if (question_sub_id == a.question_sub_id) {
          bool = true;
        }
      });
      return bool;
    },

    /**
     *保存
     */
    save(e) {
      let arr = __AUX
        .get("BJ_HYFS_GJC")
        .map((item) => {
          if (item.status) {
            return item.name;
          }
        })
        .filter((item2) => {
          return item2 !== undefined;
        });
      // console.log(arr);
      let reg = new RegExp("(" + arr.join("|") + ")");
      let self = this;
      let flag = false;
      self.goods.forEach((item) => {
        if (reg.test(item.material_name)) {
          flag = true;
        }
      });
      self.info.transport_type = flag ? "THD00001" : "THD99975";
      // self.info.transport_type = self.goods.some(item=>{
      // 	return reg.test(item.material_name)

      // }) ? 'THD00001':'THD99975'
      new Promise((resolve, reject) => {
        self.getDecryption(resolve, reject);
      })
        .then((res) => {
          setTimeout(() => {
            if (!this.addressIsPass) return;
            if (!this.validateIsPass()) return;
            this.canSave = false;
            //this.setNestQuestionSubId();
            var listDeleteGoodsIdObj = this.filterIsDeleteHasIdGoods();
            var ajaxCount = 0;
            var successMsg, errorMsg;
            this.getAfterPlanGroupId(() => {
              var _loop = (questionObj, index, address_id) => {
                if (index === this.allQuestions.length) {
                  /*if(!isEdit){
									this.$emit('resetAllData',this.afterPlanGroupId);
								}*/

                  if (errorMsg) {
                    //有可能提示补件方案头信息更新,区id为空
                    this.$message.error(errorMsg);
                    if (!successMsg) {
                      this.canSave = true;
                    }
                  }

                  if (successMsg) {
                    !errorMsg && this.$message.success(successMsg);
                    this.getPlanAndRender(() => {
                      this.canSave = true;
                    });
                  }
                  return;
                }

                var addressObj;

                //问题没有，但是对应的商品明细行存在
                //let bool = this.hasQuestionDetail(questionObj);
                if (
                  this.questionData.some(
                    (obj) =>
                      obj.parent_question_id === questionObj.parent_question_id
                  ) ||
                  listDeleteGoodsIdObj[questionObj._after_plan_no] /*|| bool*/
                ) {
                  ajaxCount++;
                  if (!this.after_plan_no) {
                    if (ajaxCount === 1) {
                      addressObj = {
                        address_id: null,
                      };
                    } else {
                      addressObj = {
                        address_id,
                        source_address_id: null,
                      };
                    }
                  }
                  this.getAfterPlanNo(index, () => {
                    this.ajax.postStream(
                      "/afterSale-web/api/aftersale/supply/saveOrUpdate",
                      {
                        questionSubVO: Object.assign(
                          {},
                          /*this.questionSubVO, */ {
                            id: questionObj.question_sub_id,
                            if_goods_question: questionObj.if_goods_question,
                            parent_question_id: questionObj.parent_question_id,
                            source_id: questionObj.question_sub_id,
                            question_description:
                              questionObj.question_description,
                            after_order_id:
                              this.otherInfo.after_order_id ||
                              this.info.after_order_id, //售后单ID
                          }
                        ),
                        supplyVo: Object.assign(
                          {},
                          this.info,
                          {
                            encryption_add_id: this.encryption_add_id,
                            id: questionObj._after_plan_id,
                            after_plan_no: questionObj._after_plan_no,
                            if_has_source: questionObj.if_has_source,
                            question_sub_id: questionObj.question_sub_id,
                            cust_id: questionObj.buyer,
                            after_plan_group_id: this.afterPlanGroupId,
                            // batch_trade_id: questionObj.batch_trade_id,
                            // batch_trade_no: questionObj.batch_trade_no,
                            bearCostsList: this.selectFeeList || []
                          },
                          addressObj
                        ),
                        supplyComponentVo: this.goodsMapQuestionList
                          .map((questionIndex, goodsIndex) => {
                            if (
                              questionIndex === index &&
                              (
                                listDeleteGoodsIdObj[
                                  questionObj._after_plan_no
                                ] || []
                              ).indexOf(this.goods[goodsIndex].id) === -1
                            ) {
                              let goods = JSON.parse(
                                JSON.stringify(this.goods[goodsIndex])
                              );

                              delete goods.creat_time;

                              if (goods.hasOwnProperty("question_sub_id")) {
                                delete goods.question_sub_id;
                              }
                              if (goods.hasOwnProperty("parentQuestionId")) {
                                delete goods.parentQuestionId;
                              }
                              goods.if_goods_question =
                                this.allQuestions[
                                  questionIndex
                                ].if_goods_question;

                              return goods;
                            }
                          })
                          .filter(Boolean),
                        listDeleteGoodsId:
                          listDeleteGoodsIdObj[questionObj._after_plan_no] ||
                          [],
                      },
                      (res) => {
                        if (res.body.result) {
                          successMsg = res.body.msg;
                        } else {
                          errorMsg = res.body.msg;
                          this.canSave = true;
                          this.$message.error(errorMsg);
                          return;
                        }

                        if (ajaxCount === 1 && !this.after_plan_no) {
                          this.ajax.postStream(
                            "/afterSale-web/api/aftersale/supply/edit",
                            this.afterPlanGroupId,
                            (res1) => {
                              _loop(
                                this.allQuestions[index + 1],
                                index + 1,
                                res1.body.content.listSupplyVo[0].address_id
                              );
                            }
                          );
                        } else {
                          _loop(
                            this.allQuestions[index + 1],
                            index + 1,
                            address_id
                          );
                        }
                      },
                      () => {
                        this.canSave = true;
                      }
                    );
                  });
                } else {
                  _loop(this.allQuestions[index + 1], index + 1, address_id);
                }
              };
              _loop(this.allQuestions[0], 0);
            });
          });
        })
        .catch((res) => {
          self.$message.error("该地址无法解密， 请手工增加新地址或者选择地址");
        });
      let isEdit = this.info.id ? true : false;
    },
    preSubmit() {
      let self = this;
      new Promise((resolve, reject) => {
        self.getDecryption(resolve, reject);
      })
        .then((res) => {
          this.submit();
        })
        .catch((res) => {
          self.$message.error("该地址无法解密， 请手工增加新地址或者选择地址");
        });
    },
    /**
     *提交
     */
    submit(e) {
      var self = this;
      var data = {
        after_order_id: self.info.after_order_id,
        after_plan_group_id: self.after_plan_group_id,
      };
      if (!self.info.after_order_id) {
        self.$message.error("参数获取失败");
        return;
      }
      this.canSubmit = false;
      this.canSave = false;
      self.ajax.postStream(
        "/afterSale-web/api/aftersale/ticketSupply/commit",
        data,
        (response) => {
          if (response.body.result) {
            self.$message({ message: "操作成功", type: "success" });
            self.getPlanAndRender(() => {
              this.canSubmit = true;
              this.canSave = true;
            });
          } else {
            self.$message.error(response.body.msg);
            this.canSubmit = true;
            this.canSave = true;
          }
        },
        (e) => {
          this.canSubmit = true;
          this.canSave = true;
          self.$message.error(e);
        }
      );
    },

    /**
     *key转换
     *key值为地址组件里面的KEY值，value为接口拿到的数据
     **/
    switchKey() {
      return {
        address_id: "source_address_id",
        receiver_name: "receiver_name",
        reveiver_phone: "reveiver_phone",
        receiver_addr: "receiver_addr",
        province: "province",
        city: "city",
        area: "area",
        street: "street",
      };
    },
    /**
     *获取修改地址详情
     **/
    getModifyAddress(data) {
      var isPass = data.isPass;
      this.addressIsPass = isPass;
      if (!isPass) return;
      var addressInfo = data.data;
      var keyData = this.switchKey();

      for (var key in keyData) {
        let value = keyData[key];
        this.info[value] = addressInfo[key];
      }
    },

    /**
     *计算补件成本和客户金额
     */
    calc() {
      var _totalCustomer = 0,
        _totalCost = 0;

      this.goods.map((good) => {
        _totalCustomer += parseFloat(good.customer_fee || 0);
        _totalCost += parseFloat(good.supply_code || 0);
      });
      this.totalCustomer = _totalCustomer.toFixed(2);
      this.totalCost = _totalCost;
    },

    /**
     *问题商品字段
     *key方案里面对应的key,
     *value 为弹出框里面对应的字段
     **/
    questionGoodsInfo(index) {
      return {
        question_goods_id: this.allQuestions[index].question_sub_id,
        question_goods_code: this.allQuestions[index].material_code,

        entry_bom_version: this.allQuestions[index].bom_version,
        supplier_company: this.allQuestions[index].suppiler_name,
        supplier_company_id: this.allQuestions[index].suppiler_id,
        question_goods_desc: this.allQuestions[index].material_specification,
        question_goods_name: this.allQuestions[index].material_name,
        question_description: this.allQuestions[index].question_description,
        incom_shop_id: this.allQuestions[index].user_shop_id,
        incom_shop_name: this.allQuestions[index].user_shop_name,
        goods_branch_no: this.allQuestions[index].lot,
        question_sub_id: this.allQuestions[index].question_sub_id,

        batch_trade_id: this.allQuestions[index].batch_trade_id,
        batch_trade_no: this.allQuestions[index].batch_trade_no,

        supply_request: null,
        supply_code: null,

        customer_fee: null,
        if_stop_produce: null,
        bom_version: null,

        remark: null,
        bom_version: null,
        description: null,

        units: null,
        number: null,
        parentQuestionId: this.allQuestions[index].parent_question_id, //父问题ID
        sale_order_no: this.allQuestions[index].sys_trade_no, //销售单号
      };
    },
    // 刷新可用库存
    updateStore() {
      if (!this.selectedGoods.length) {
        this.$message.error("请选择至少一个商品明细");
        return;
      }
      let params = [];
      this.selectedGoods.map((a, b) => {
        let array = {
          if_company_send: "N",
          material_id: a.material_id,
          material_code: a.material_code,
        };
        params.push(array);
      });
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/ticketSupply/getRefreshUsed",
        params /*{
					list_material_id: this.selectedGoods.map(obj => obj.material_id),
				}*/,
        (res) => {
          var data = res.body.content;

          if (res.body.result) {
            this.$message.success(res.body.msg);
            Object.keys(data).forEach((id) => {
              if (!data[id]) data[id] = 0;
            });
            this.validStock = data;
          } else {
            this.$message.error(res.body.msg);
          }
        }
      );
    },
    // 获取参考价
    taxPrice(questionIndex, _goodsMaterialIds) {
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/supply/getTaxPriceOrFprice",
        {
          supplier_id: this.allQuestions[questionIndex].supplier_id,
          material_ids: _goodsMaterialIds.filter(Boolean),
        },
        (res) => {
          var materialIdMapTaxPrice = {};
          (res.body.content || []).forEach(
            (obj) =>
              (materialIdMapTaxPrice[obj.materialId] = Number(obj.taxPrice))
          );

          _goodsMaterialIds.forEach((material_id, index) => {
            var price = materialIdMapTaxPrice[material_id];
            if (price) this.goods[index].supply_code = price;
          });
          this.calc();
        }
      );
    },
    /**
     *添加bom产品
     */
    addGoods() {
      // let params = this.questionData[0].if_has_source === 'Y' ? this.getSearchCondition() : this.getSearchCondition2();
      // console.log('params123123',this.questionData)
      this.$root.eventHandle.$emit("alert", {
        params: {
          isPushDownConsult: !!this.isPushDownConsult, // 是否展示推送下推咨询按钮
          questionList: this.selectedData,
          tipContent:'如果添加赠品,请关闭弹窗,将期望类型修改为赠品下单',
          callback: (d) => {
            var data = d.data,
              questionIndex;

            if (!data) return;

            this.allQuestions.some((obj, index) => {
              if (obj.question_sub_id === d.questionId) {
                questionIndex = index;
                return true;
              }
            });
            new Promise((resolve) => {
              this.judgeCanPurchase(data, resolve);
            }).then(() => {
              if (!data.length) return;
              this.isGiftProduct(data).then(()=>{
                this.taxPrice(
                  questionIndex,
                  this.addGoodsCallback(data, d.questionId, questionIndex)
                );
              }).catch((e)=>{
                console.log(e,'555')
              })

            });
          },
        },
        component: () => import("@components/after_solutions/bomgoods"),
        style: "width:90%;height:80%",
        title: "添加商品",
      });
    },
    isGiftProduct(data) {
      return new Promise((resolve,reject)=>{
        if (!data || !data.length) {
          reject()
        };
        let materialCodeList = data.map((a, b) => {
          return a.bom_material_num || a.materialNumber;
        });
        materialCodeList = Array.from(new Set(materialCodeList));
        if (!materialCodeList.length) return;
        this.ajax.postStream("/afterSale-web/api/gift/config/checkSupplyGift", {mcode:materialCodeList},(res)=>{
          console.log(res,'44444')
          if(res.body.result){
            resolve()
          }else{
            this.$message.error(res.body.msg);
            reject()
          }
        })
      })

    },
    /**
     *判断物料是否可采购
     ***/
    judgeCanPurchase(data, resolve) {
      let materialCodeList = [];
      if (!data || !data.length) return;
      data.map((a, b) => {
        let code = a.bom_material_num || a.materialNumber;
        materialCodeList.push(code);
      });
      materialCodeList = Array.from(new Set(materialCodeList));
      if (!materialCodeList.length) return;

      let canNotPurchase = "";
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/ticketSupply/getIsPurchaseInfo",
        materialCodeList,
        (res) => {
          let d = res.body;
          if (!d.result) {
            this.$message.error(d.msg);
            return;
          }
          /*for(var key in d.content){
						d.content[key] = 0;
					}*/
          //去掉不可采购的明细行
          for (let i = 0, n = 0; i < data.length; i = n) {
            let code = data[i].bom_material_num || data[i].materialNumber;
            if (d.content.hasOwnProperty(code) && d.content[code] == 0) {
              canNotPurchase += code + ",";
              data.splice(i, 1);
              n = 0;
              continue;
            }
            n++;
          }
          if (canNotPurchase) {
            this.$message.error(canNotPurchase + "不能进行采购");
          }
          resolve && resolve();
        }
      );
    },
    // 当新增商品行时，供应商为空且在未保存情况下可以选择供应商
    selectSupplierCompany(row) {
      if (this.ifLinsyOrder(row)) {
        this.$root.eventHandle.$emit("alert", {
          params: {
            callback: (d) => {
              var data = d.data;
              row.supplier_company_id = data.supplier_id;
              row.supplier_company = data.name;
              // Object.defineProperties(row, {
              //     __canSelectSupplierCompany: {
              //         configurable: true,
              //         writable: true,
              //         enumerable: false,
              //         value: true,
              //     },
              // })
            },
          },
          component: () => import("@components/after_sales_supply/supplier_2"),
          style: "width:80%;height:80%",
          title: "选择供应商",
        });
      }
    },
    /**
     *关闭标签页面
     */
    closeParentTab(oldData, isSwitchOrCloseTab) {
      if (oldData) {
        if (/Object/.test(Object.prototype.toString.call(oldData))) {
          // 获取地址信息并记录作为对比数据
          oldData.isPass = true;
          this.getModifyAddress(oldData);
          oldData = [this.info, this.goods];
        }
        this.closeParentTab.oldData = JSON.parse(JSON.stringify(oldData));
        return;
      }
      // this.otherData.getAddress = !this.otherData.getAddress;//先获取地址组件信息

      setTimeout(() => {
        this.closeParentTab.oldData[0].source_address_id =
          this.info.source_address_id; //去掉source_address_id差异

        var tabName = this.operatParams.params.tabName,
          isDiff = this.compareData(
            JSON.parse(JSON.stringify([this.info, this.goods])),
            this.closeParentTab.oldData
          ),
          _removeTab = () =>
            isSwitchOrCloseTab === "switchTab"
              ? this.$emit("changeSolution")
              : this.$root.eventHandle.$emit("removeTab", tabName);

        if (isDiff) {
          this.$root.eventHandle.$emit("openDialog", {
            ok: (e) => {
              this.save(e);
            },
            no: _removeTab,
          });
        } else {
          _removeTab();
        }
      });
    },
    //TODO,关闭保存的提醒
    switchTab() {
      this.closeParentTab(null, "switchTab");
      // this.$emit("changeSolution");
    },

    /**
     *添加商品的回调函数
     **/
    addGoodsCallback(data, questionId, questionIndex) {
      var returnThisMaterialIds = [];

      data.map((good, c) => {
        var obj = this.questionGoodsInfo(questionIndex);
        obj.material_id = good.bom_material_id || good.sourceMaterialId;
        obj.material_code = good.bom_material_num || good.materialNumber;
        obj.material_name = good.bom_material_name || good.materialName;
        obj.description = good.bom_material_desc || good.materialSpecification;
        obj.present_flag = good.present_flag;
        obj.units = good.bom_version_unit || good.materialUnit;
        obj.common_supply = good.isAdditional; //是否通用
        obj.supply_code = 0;
        obj.if_stop_produce = good.isStop ? "Y" : "N";
        obj.bom_version = good.bom_version;
        obj.creat_time = new Date().getTime();
        obj.number = 1; //补件数量默认1

        if (this.allQuestions[questionIndex].if_goods_question === "N") {
          obj.question_goods_code = obj.material_code;
        }

        returnThisMaterialIds[this.goods.push(obj) - 1] = obj.material_id;
        this.goodsMapQuestionList.push(questionIndex);
        this.goodsMaterialIds.push(obj.material_id);
      });

      this.filterQuestionData();

      return returnThisMaterialIds;
    },
    /**
     *根据明细去判断哪些问题建了方案
     ***/
    filterQuestionData() {
      var newQuestionData = [];
      this.goodsMapQuestionList.forEach((questionIndex) => {
        if (!newQuestionData[questionIndex]) {
          newQuestionData[questionIndex] = this.allQuestions[questionIndex];
        }
      });
      this.questionData = newQuestionData.filter(Boolean);
      /*this.questionData = [];
				var parent_question_ids = this.goods.map(obj=>obj.parentQuestionId);
				this.allQuestions.map((a,b)=>{
					parent_question_ids.some(c=>{
						if(c == a.parent_question_id){
							this.questionData.push(JSON.parse(JSON.stringify(a)));
							return true;
						}
					})
				})*/
    },
    /**
     *删除方案明细
     */
    del() {
      var currentGoods = this.goods;
      var selectedGoods = this.selectedGoods;
      if (!selectedGoods || !selectedGoods.length) {
        this.$message.error("请选择当前面板要删除的明细");
        return;
      }
      let deleteList = [];
      console.log(selectedGoods, currentGoods);
      selectedGoods.map((selectedG, a) => {
        currentGoods.map((goods, b) => {
          if (JSON.stringify(selectedG) == JSON.stringify(goods)) {
            //TODO,唯一标识ID
            var detailId = selectedG.id;
            // if(detailId){
            // 	deleteList.push(detailId);
            // 	return;
            // }
            currentGoods.splice(b, 1);
            this.goodsMaterialIds.splice(b, 1);
            this.goodsMapQuestionList.splice(b, 1);
            return;
          }
        });
      });
      this.filterQuestionData();
      this.calc();
      this.concatReturnAndExchangeGoods();
    },

    /**
     *获取方案编码
     **/
    getAfterPlanNo(index, cb) {
      if (this.allQuestions[index]._after_plan_no) {
        // 编辑更新
        cb();
      } else {
        let url = "/afterSale-web/api/aftersale/plan/getNos";
        let data = {
          number_count: 1,
          number_type: "BJFA",
        };
        this.ajax.postStream(url, data, (res) => {
          let d = res.body;
          if (!d.result) {
            this.$message.error(d.msg);
            return;
          }
          this.allQuestions[index]._after_plan_no = d.content[0];
          // this.otherData.canUploadImg = true;
          cb();
        });
      }
    },
    /**
     *设置子问题ID
     **/
    setQuestionSubId(data) {
      if (!data) return;
      var tabInfo = data.listSupplyVo;
      if (!tabInfo) return;
      let schemeData = {};
      for (var key in tabInfo) {
        let a = tabInfo[key];
        if (a.id) {
          schemeData[a] = a.question_sub_id;
        }
      }
      let question = data.supplyComponentVo;
      for (var key in question) {
        let s = question[key];
        let after_plan_id = s.after_plan_id;
        if (schemeData[after_plan_id]) {
          s.question_sub_id = schemeData[after_plan_id];
        }
      }
    },
    /**
     *获取已挂方案的问题
     ***/
    /*getQuestionByCreatSolution(list){
				let listSupplyVo = list.listSupplyVo;
				let supplyComponentVo = list.supplyComponentVo;
				if(!listSupplyVo || !listSupplyVo.length || !supplyComponentVo || !supplyComponentVo.length) return '';
				let questions = [];
				listSupplyVo = JSON.parse(JSON.stringify(listSupplyVo));
				listSupplyVo.map((a,b)=>{
					supplyComponentVo.map((c,d)=>{
						if(a.after_plan_no == c.after_plan_no){
							listSupplyVo.after_plan_id = c.after_plan_id;
						}
					})
				})
			},*/

    /**
     *设置已挂方案的问题列表
     ***/
    setQuestionListBySoutions(listQuestionSubVO, listSupplyVo) {
      if (!listQuestionSubVO || !listQuestionSubVO.length) return;
      var questions = [];
      listQuestionSubVO.map((q, b) => {
        //let q = a.aftersaleOrderQuestionSubVO;
        let obj = {
          source_id: q.source_id,
          question_description: q.question_description,
          parent_question_id: q.parent_question_id,
          question_sub_id: q.id,
          //after_plan_id:q.after_plan_no//补件先用方案编号去做关联
        };
        /*if(q.id == this.info.question_sub_id){
						obj.isCurrentSolution  = true;
					}*/
        questions.push(obj);
      });

      questions.map((a, b) => {
        let question_sub_id = a.question_sub_id;
        listSupplyVo.map((n, f) => {
          let id = n.question_sub_id;
          if (question_sub_id == id) {
            a.after_plan_id = n.after_plan_no;
            if (n.after_plan_no == this.after_plan_no) {
              a.isCurrentSolution = true;
            }
          }
        });
      });
      this.$emit("reSetQuestionListFn", {
        questions: questions,
        canEditQuestion: this.canSave || this.info.status !== "SUBMIT",
      });
    },
    /**
     *根据子问题ID去找问题信息
     ***/

    /*
			获取方案详情
			 */
    getPlanAndRender(cb) {
      console.log("看看为什么会执行到这里面来，方案详情");
      if (!this.afterPlanGroupId) return;
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/supply/edit",
        this.afterPlanGroupId,
        (res) => {
          if (res.body.result) {
            var planNoMapSubId = {};
            this.$message.success(res.body.msg);
            res = res.body.content;
            res.listQuestionSubVO.forEach((obj) => {
              this.allQuestions.some((obj2) => {
                if (obj2.parent_question_id === obj.parent_question_id) {
                  obj2.question_sub_id = obj.id;
                  obj2.source_id = obj.source_id;
                  return true;
                }
              });
            });

            res.listSupplyVo.forEach((obj) => {
              // this.planStatus[obj.question_sub_id] = obj.status
              planNoMapSubId[obj.after_plan_no] = obj.question_sub_id;
              this.allQuestions.some((obj2) => {
                if (obj2.question_sub_id === obj.question_sub_id) {
                  obj2._after_plan_no = obj.after_plan_no;
                  obj2._after_plan_id = obj.id;
                  return true;
                }
              });
            });
            this.goodsMapQuestionList = [];
            res.supplyComponentVo.forEach((obj) => {
              this.allQuestions.some((obj2, index) => {
                if (obj2._after_plan_no === obj.after_plan_no) {
                  this.goodsMapQuestionList.push(index);
                  // obj.question_sub_id = obj2.question_sub_id;//由于接口question_sub_id改变，正常时的代码
                  obj.question_goods_id = obj.question_sub_id =
                    planNoMapSubId[obj.after_plan_no]; //由于接口question_sub_id改变，临时覆盖处理
                  obj.parentQuestionId = obj2.parent_question_id;
                  return true;
                }
              });
            });
            this.filterQuestionData();
            //this.setQuestionSubId(res);
            this.oldGoods = [].concat(res.supplyComponentVo);
            this.init({
              supplyVo: res.listSupplyVo[0],
              supplyComponentVo: res.supplyComponentVo,
            });
            this.encryption_add_id = res.listSupplyVo[0].encryption_add_id;

            this.oaid=res.listSupplyVo[0].oaid;
            this.info_from = res.listSupplyVo[0].info_from;

            this.orginalTableData = JSON.parse(
              JSON.stringify(res.listSupplyVo[0])
            );
            this.after_plan_no = res.listSupplyVo[0].after_plan_no;
            this.setQuestionListBySoutions(
              res.listQuestionSubVO,
              res.listSupplyVo
            );
            this.after_plan_group_id = res.listSupplyVo[0].after_plan_group_id;
            this.calc();
            cb && cb();
          } else {
            this.orginalTableData = null;
            this.$message.error(res.body.msg);
          }
        }
      );
    },
    //上传图片
    uploadFun() {
      if (!this.selectedGoods || this.selectedGoods.length != 1) {
        this.$message.error("只能选择一个明细行操作");
        return;
      }
      var questionObj = this.questionData.filter(
        (obj) => obj.question_sub_id === this.selectedGoods[0].question_goods_id
      )[0];

      //TODO,明细行里面的父问题ID
      this.ifClickUpload = true;
      this.uploadData = {
        parent_name: "AFTER_ORDER",
        parent_no: this.otherInfo.after_order_no, //售后单号
        child_name: "QUESTION_GOODS",
        child_no: questionObj.parent_question_id, //父问题id,必须是String
        content: questionObj,
      };
      //重置，防止第二次不能触发
      setTimeout(() => {
        this.ifClickUpload = false;
      }, 100);
    },
    /**
     *查看或下载附件
     **/
    pictureFun() {
      if (!this.selectedGoods || this.selectedGoods.length != 1) {
        this.$message.error("只能选择一个明细行操作");
        return;
      }

      var params = {
        parent_no: this.otherInfo.after_order_no, //售后单号
        child_no: null,
        ext_data: null,
        parent_name: "AFTER_ORDER",
        child_name: "QUESTION_GOODS",
        parent_name_txt: "售后单",
        // child_name_txt: '问题商品id',
        notNeedDelBtn:
          this.info.status === "SUBMIT" || !this.otherInfo.canEditSolutions,
        nickname: this.otherInfo.form.buyer_nick,
        mergeTradeId: this.otherInfo.merge_trade_id,
      };
      this.$root.eventHandle.$emit("alert", {
        params: params,
        component: () =>
          import("@components/after_sales/afterSale_aboutZD_download.vue"),
        style: "width:1000px;height:600px",
        title: "下载列表",
      });
    },

    init(data, isInit) {
      var returnData = {
        // questionSubVO: data ? data.questionSubVO : {},
        info: data
          ? data.supplyVo
          : {
              // after_plan_no:null,
              after_plan_date: new Date(),
              // if_goods_question: this.otherInfo.type == 1 ? 'Y' : 'N',
              customer_fee: null,
              delivery_type: "NOW_PAY",
              transport_type: null,
              if_door: "NONEED",
              status: "CREATE",
              remark: null,

              id: null,
              after_order_id: this.otherInfo.after_order_id,
              after_order_no: this.otherInfo.after_order_no,
              merge_trade_id: this.otherInfo.merge_trade_id,
              merge_trade_no: this.otherInfo.merge_trade_no,
              if_modify_logistics: "Y", //是否允许修改货运方式
              if_repurchase_order: "N", //是否加购订单

              /*这两id保存时再设置*/
              // question_sub_id: this.questionData[0].question_sub_id,
              // cust_id: this.questionData[0].buyer,

              source_address_id: null,
              receiver_name: null,
              reveiver_phone: null,
              receiver_addr: null,
              province: null,
              city: null,
              area: null,
              street: null,
              logistics_supplier_class: "",
            },
        goods: data ? data.supplyComponentVo : [],
      };

      this.selectFeeList = data?.supplyVo?.bearCostsList?.length > 0 ? data.supplyVo.bearCostsList : []; // 客户承担费用
      this.closeParentTab([returnData.info, returnData.goods]); //init 关闭页签时对比数据

      if (isInit) return returnData;
      else {
        // this.questionSubVO = returnData.questionSubVO
        this.goods = returnData.goods;
        this.info = returnData.info;
        // returnData.otherData && (this.otherData = returnData.otherData)
      }
    },
    /*
     *选择退换货商品
     **/
    select(selections) {
      this.selectedGoods = selections.length ? selections : [];
    },
    // table检测值是否大于0
    _checkValIsRight(e, bool) {
      e.target.parentElement.nextElementSibling.style.display = bool
        ? "none"
        : "inline-block";
    },
    // _initQuestionData (){
    // 	if(this.afterGroup.length && this.allQuestions.length){
    // 		var question_ids = this.afterGroup.map(obj => obj.after_question_id)

    // 		this.questionData = this.allQuestions.filter(obj => {
    // 			return question_ids.indexOf(obj.parent_question_id) !== -1
    // 		})
    // 		// if(!this.questionData[0]) this.questionData[0] = {};
    // 		console.log('看看数据',this.questionData);

    // 		//this.afterPlanGroupId = this.afterGroup[0].after_plan_group_id
    // 		//this.getPlanAndRender()
    // 	}
    // },
  },
  computed: {
    disabledOption: function () {
      let data = __AUX.get("bjd_hyfs").filter((item, index, array) => {
        return item.code == "THD97244";
      });
      let disabledList = data.map((item) => {
        return { code: item.code };
      });
      return disabledList;
    },
  },
  watch: {
    // selectedData (newVal, oldVal){
    // 	// this.questionData = newVal

    // 	var questionDataIds = this.questionData.map(obj => obj.question_sub_id)
    // 	,	delGoodsIndex = []

    // 	console.log('监听数据');

    // 	this.goods.forEach((obj, index) => {
    // 		if(questionDataIds.indexOf(obj.question_goods_id) === -1){
    // 			delGoodsIndex.push(index)
    // 		}
    // 	})

    // 	delGoodsIndex.forEach(goodsIndex => {
    // 		this.goods.splice(goodsIndex, 1)
    // 		this.goodsMaterialIds.splice(goodsIndex, 1)
    // 		this.goodsMapQuestionList.splice(goodsIndex, 1)
    // 	})
    // },
    //切换的提醒
    "operatParams.isSwitchTab": function (newVal, oldVal) {
      this.switchTab();
    },
    // 关闭页签时判断
    "operatParams.bool": function (val) {
      this.closeParentTab();
    },
    // allQuestions (){
    // 	this._initQuestionData()
    // },
    // afterGroup (){
    // 	this._initQuestionData()
    // },
    // copySelectData (){
    // 	this._changeNowPlanStatus()
    // },
  },
  mounted() {
    if (this.selectedData[0]) {
      if (!this.otherInfo.after_plan_group_id) {
        this.questionData = this.selectedData;
      }
      this.getAddressInfoByMergeMaterialId(true);

      // this.otherData.loadingAddress = !this.otherData.loadingAddress
    }
    if (this.otherInfo.after_plan_group_id) {
      this.afterPlanGroupId = this.otherInfo.after_plan_group_id;
      this.getPlanAndRender();
    }
    this.info.logistics_supplier_class = !!this.selectedData.length
      ? this.selectedData[0].logistics_supplier_class
      : "";

    // if(this.editOtherInfo.id){
    // 	this.getPlanAndRender(this.editOtherInfo.id)
    // }else {
    // }
  },
};
</script>
<style module>
/*:global(.xpt_pmm) :global(.xpt_pmm_scroll) :global(.el-table__body-wrapper){
	max-height: 100px;
}*/
.only-show-total :global(.el-checkbox),
.only-show-total :global(.el-input),
.only-show-total :global(.el-textarea),
.only-show-total :global(.table-index) {
  display: none;
}
.row-height :global(.cell) {
  height: auto !important;
}
.row-height textarea {
  width: 100%;
  overflow-x: hidden;
}
th.th-required :global(.cell:before) {
  content: "*";
  color: red;
}
</style>
