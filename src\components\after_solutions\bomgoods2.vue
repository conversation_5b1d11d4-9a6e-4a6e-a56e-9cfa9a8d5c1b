<!--退换货方案的添加产品-->
<template>
	<div class='xpt-flex'>
		<el-row class='xpt-top' :gutter='40'>
			<el-col :span='3'>
				<el-button type='info' size='mini' @click="confirmAddGoods">确认</el-button>
			</el-col>

			<el-col :span='21' class='xpt-align__right' >
				<span>
					问题商品:
					<el-select placeholder="请选择" size="mini" style="width:250px;" v-model="question_sub_id" @change="changeQuestion">
						<el-option
						 v-for="(obj,key) in questionData"
						 	:label="obj.popName"
		  					:value="obj.question_sub_id"
							:key='obj.question_sub_id'>
						</el-option>
		    		</el-select>
				</span>

				<!-- bom -->
				<span v-if="isBomOrAllGoods">
					<span>
						全部物料:<el-select placeholder="请选择" size="mini" style="width:80px;" v-model="search.all" :disabled="isDisabledAll" @change="changeGoods">
							<el-option
							 v-for="(value,key) in boolObj"
							 	:label="value"
			  					:value="key"
								:key='key'>
							</el-option>
			    		</el-select>
					</span>

		    		<span v-if="isNeedBomVerison">
		    			<!-- 拆分物料-->
		    			BOM版本:<el-select placeholder="请选择" size="mini" style="width:200px;" v-model="search.bomVersion">
						<el-option
						 v-for="(value,key) in bomVersion"
						 	:label="value"
		  					:value="key"
							:key='key'>
						</el-option>
		    		</el-select>

		    		</span>
		    		<el-select placeholder="请选择" size="mini"  v-model="search.condition" style="width:100px;" @change="conditionChange" >
						<el-option label="物料编码" value="MATERIAL_NUMBER"></el-option>
						<el-option label="物料名称" value="MATERIAL_NAME"></el-option>
						<el-option label="BOM分组" value="BOM_GROUP" v-if="search.all==0"></el-option>

						<el-option label="是否通用" value="IF_ADDITIONAL"></el-option>
					</el-select>
					<el-select placeholder="请选择" size="mini"  style="width:100px;" v-model="search.symbol">
						<el-option
						 v-for="(value,key) in condition"
						 	:label="value"
		  					:value="key"
							:key='key'>
						</el-option>
					</el-select>

					<el-select placeholder="请选择" size="mini"  v-model="search.string" style="width:80px;" v-if="tongyongShow ">
						<el-option
						 v-for="(value,key) in yesOrNo"
						 	:label="value"
		  					:value="key"
							:key='key'>
						</el-option>
					</el-select>

					<el-input placeholder="请输入查询条件" size='mini' v-model="search.string" v-else></el-input>

					<el-button type='primary' size='mini' @click="searchList">搜索</el-button>
				</span>

				<!-- 发运明细 -->
				<span v-else>
					<el-select v-model="search2.limit_field" size='mini' style="width:150px;">
						<el-option label="商品名称" value="material_name"></el-option>
						<el-option label="商品编码" value="material_number"></el-option>
						<el-option label="规格描述" value="material_specification"></el-option>
						<!-- <el-option label="是否通用" value="is_additional"></el-option> -->
					</el-select>
					<el-select v-model="search2.operator" size='mini' style="width:100px;">
						<el-option label="等于" value="="></el-option>
						<el-option label="包含" value="%"></el-option>
					</el-select>
					<el-input placeholder="请输入查询值" size='mini' v-model="search2.field_value"></el-input>
					<el-button type='primary' @click="searchList" size='mini'>查询</el-button>
				</span>

			</el-col>
		</el-row>

		<!-- 全部物料-->
		<xpt-list v-if="currentType==1"
			ref='list'
			selection='checkbox'
			:showHead = 'false'
			:data='list'
			:colData='allGoodsProps'
			:pageTotal='count'

			searchHolder='请输入搜索条件'
			@selection-change='select'
			@page-size-change='sizeChange'
			@current-page-change='pageChange'>
      <template #bom_imgs="{ row }">
        <el-button v-if="!!row.attach_count" type="text" size="mini" @click="onShowImg(row)">查看图片</el-button>
      </template>
		</xpt-list>

		<!-- bom树 -->
		<el-row  v-if="currentType==2" style="overflow: hidden;height: calc(100% - 54px);">
			<div style="position:relative;height: 100%" id="testBom">
        <vxe-table
          ref="xptBoom"
          border
          resizable
          stripe
          show-overflow
          show-header-overflow
          :data="tableData"
          height="auto"
          :auto-resize="true"
          :seq-config="{startIndex: 0}"
          :tree-config="{children: 'children', lazy: true, hasChild: 'haveChildren', loadMethod: loadChildrenMethod}"
          :checkbox-config="{labelField: 'a', showIcon: true, highlight: true, checkStrictly: true, showHeader: false}"
          >
          <vxe-table-column type="checkbox" field="a" width="80" tree-node></vxe-table-column>
          <vxe-table-column field="bom_material_num" title="物料编码" width="150">
            <template #header="{ row }">
              <div>物料编码</div>
            </template>
          </vxe-table-column>
          <vxe-table-column field="bom_imgs" title="图片" width="100">
            <template #default="{ row }">
              <el-button v-if="!!row.attach_count" type="text" size="mini" @click="onShowImg(row)">查看图片</el-button>
            </template>
          </vxe-table-column>
          <vxe-table-column field="bom_material_name" title="物料名称" width="220"></vxe-table-column>
          <vxe-table-column field="bom_material_desc" title="规格描述" width="600"></vxe-table-column>
          <vxe-table-column field="bom_version_unit" title="单位" width="80"></vxe-table-column>
          <vxe-table-column field="isAdditional" title="是否通用" :formatter="formatAddition" width="80"></vxe-table-column>
          <vxe-table-column field="bom_version" title="BOM编码" width="160"></vxe-table-column>
          <vxe-table-column field="bom_version_group" title="BOM分组" width="160"></vxe-table-column>
          <vxe-table-column field="bom_version_name" title="BOM名称" width="160"></vxe-table-column>
          <vxe-table-column field="materialUnit" title="销售单位" width="80"></vxe-table-column>
        </vxe-table>
			</div>
		</el-row>

		<!-- 发运明细 -->
		<xpt-list v-if="currentType==3"
			ref='list'
			selection='checkbox'
			:showHead = 'false'
			:data='list'
			:colData='saleGoodsProps'
			:pageTotal='count'

			searchHolder='请输入搜索条件'
			@selection-change='select'
			@page-size-change='sizeChange'
			@current-page-change='pageChange'>
      <template #bom_imgs="{ row }">
        <el-button v-if="!!row.attach_count" type="text" size="mini" @click="onShowImg(row)">查看图片</el-button>
      </template>
		</xpt-list>

	</div>


</template>
<script>
  import Vue from 'vue'
  import tree from '@components/common/bom-tree'
  import XEUtils from'xe-utils'
  import VXETable from 'vxe-table'
  import 'vxe-table/lib/style.css'

  Vue.use(VXETable)
	export default {//需要类型和相关的id
	    props:['params'],
		data(){
            var self = this;
			return {
        filterName: '',
				search:{
                    all:'0',//全部物料

					condition:'MATERIAL_NUMBER',
					symbol:'CONTAIN',
					string:'',//描述
					bomVersion:null//bom版本信息，用于无源单
				},
				yesOrNo:{
					'Y':'是',
					'N':'否',
					'ALL':'全部'
				},
				tongyongShow:false,
				//发运明细的查询
				search2:{
					field_value:null,
					limit_field:'material_number',
					operator:'CONTAIN'

				},

				condition:{
					"CONTAIN":'包含',
					"EQAUL":'等于'
				},
				//分页数据
				page:{
                   	page_size:self.pageSize,
					page_no:1
				},
				isBomOrAllGoods:true,//是否要显示全部物料以及拆分物料的头部信息
				questionData:[],//
				question_sub_id:null,
				realy_question_sub_id:null,//当前问题下的物料列表

				isDisabledAll : false,
				isDisabledUniversal : false,
				isDisabledBomGroup : false,

				isNeedBomVerison:false,//是否是需要bom版本进行查询

				count:0,
                selectedData:'',
                boolObj:{
                	1:'是',
                	0:'否'
                },

                //bom版本信息
                bomVersion:null,

                list:[],//
                //colData:[],
                //templateColData:null,//中间量
                currentType:null,//currentType 1 -->全部物料（成品），2--->拆分物料(bom),3---->发运明细
                templateType:null,//中间量
                initIndex:1,//bom树的key

                //全部物料
                allGoodsProps:[
               	{
					label: '是否通用',
					prop: 'isAdditional',
					format:'yesOrNo'
				}, {
          label: '图片',
					prop: 'bom_imgs',
          slot: 'bom_imgs',
        }, {
					label: '物料编码',
					prop: 'materialNumber'
				}, {
					label: '物料名称',
					prop: 'materialName'
				}, {
					label: '规格描述',
					prop: 'materialSpecification'
				}, {
					label: '单位',
					prop: 'materialUnit'
				}],

				//发运明细
				saleGoodsProps:[
               	{
					label: '商品编码',
					prop: 'material_number'
				}, {
          label: '图片',
					prop: 'bom_imgs',
          slot: 'bom_imgs',
        },  {
					label: '商品名称',
					prop: 'material_name'
				}, {
					label: '规格描述',
					prop: 'material_specification'
				}, {
					label: '批次单号',
					prop: 'batch_trade_no'
				}, {
					label: '淘宝单号',
					prop: 'tid'
				},{
					label: '店铺',
					prop: 'shop_name'
				}, {
					label: '是否停产',
					prop: 'is_stop',
					prop:'yesOrNo'
				}, {
					label: '销售订单号',
					prop: 'sys_trade_no'
				}, {
					label: '商品组别',
					prop: 'inventory_category'
				}, {
					label: '基本单位',
					prop: 'material_unit'
				}],

                //bom树
                props: {
		          	label:[{
		          		name:'isAdditional',
		          		width:80,
		          		label:'是否通用'/*,
		          		childNode:'a'*/
		          	},{
		          		name:'bom_material_num',
		          		label:'物料编码',
		          		width:150,
		          	},{
		          		name:'bom_material_name',
		          		label:'物料名称',
		          		width:150,
		          	},{
		          		name:'bom_material_desc',
		          		label:'规格描述',
		          		width:150,
		          	},{
		          		name:'bom_version_unit',
		          		label:'单位',
		          		width:80,
		          	},{
		          		name:'bom_version',
		          		width:160,
		          		label:'BOM编码',
		          	},{
		          		name:'bom_version_group',
		          		width:160,
		          		label:'BOM分组',
		          	},{
		          		name:'bom_version_name',
		          		width:150,
		          		label:'BOM名称',
		          	},{
		          		name:'materialUnit',
		          		width:80,
		          		label:'销售单位'
		          	}],
		          	children: 'children'



		        },
         tableData: [],
         originData: []
			}
		},
		methods:{
      // 格式化是否通用
      formatAddition ({ cellValue }) {console.log('cellValuecellValue', cellValue)
        return cellValue === 0 ? '否' : (cellValue === 1 ? '是' : '')
      },
      loadChildrenMethod ( { row } ) {
        console.log('loadChildrenMethod', row)
        // this.setChildNode(row, row)
        // return false
        let self = this
        return new Promise( resolve => {
          let question = this.getChoiceQuestion(this.realy_question_sub_id);
          let question_id = question.isReturnGoodsOfBill?question.question_goods_id:question.parent_question_id
          let params = {
            material_id : row.bom_material_id,
            bom_version : row.bom_version,
            question_id : question_id
          }
          let initIndex = row.initIndex;
          this.ajax.postStream('/afterSale-web/api/aftersale/plan/listBomMaterial',params,(d)=>{
            if(!d.body.result){
              this.$message.error(d.body.msg);
              row.hasLoading = false;
              resolve([])
              return;
            }
            let list =  d.body.content.list || [];

            if(!list.length){
              row.haveChildren = false;
              resolve([])
              return;
            }
            console.log('查看物料loadChildrenMethod', this.tableData)
            this.setNodeKey(list);
            //设置树节点
            list.map((a,b)=>{
              a.id = a.bom_material_id
              a.materialNumber = a.bom_material_num
              a.materialName = a.bom_material_name
              a.materialSpecification = a.bom_material_desc
            });

            console.log('loadChildrenMethodlist', this.tableData)
            // this.tableData = list
            this.originData = list
            resolve(list)

          });
        })
      },
      handleSearch () {
        let filterName = XEUtils.toValueString(this.filterName).trim()
        if (filterName) {
          let options = { children: 'children' }
          let searchProps = ['name']
          this.tableData = XEUtils.searchTree(this.originData, item => searchProps.some(key => XEUtils.toValueString(item[key]).indexOf(filterName) > -1), options)
          // 搜索之后默认展开所有子节点
          this.$nextTick(() => {
            this.$refs.xptBoom.setAllTreeExpand(true)
          })
        } else {
          this.tableData = this.originData
        }
      },
      // 创建一个防反跳策略函数，调用频率间隔 500 毫秒
      searchEvent: XEUtils.debounce(function () {
          this.handleSearch()
        }, 500, { leading: false, trailing: true }
      ),
			/**
			*bom树
			****/
			handleCheckChange(data,isChecked,childChecked){
				if(isChecked){
					!this.selectedData?this.selectedData = []:'';
					this.selectedData.unshift(data);
				}else{
					let i = this.selectedData.length;
					while(i--){
						if(this.selectedData[i].initIndex===data.initIndex){
							this.selectedData.splice(i,1);
							break;
						}
					}
				}
			},
			userClick(d){
				console.log('userClick',d);

			},
			nodeClick(data,node,_this){
				if(data.hasLoading || !data.haveChildren) return;
				data.hasLoading = true;
				this.setChildNode(data,node);

			},
			/**
			*根据父节点拿到下面的所有子节点
			***/
			setChildNode(parentNode,node){
				if(!parentNode) return;
				var url = '/afterSale-web/api/aftersale/plan/listBomMaterial';
				let question = this.getChoiceQuestion(this.realy_question_sub_id);

				let question_id = question.isReturnGoodsOfBill?question.question_goods_id:question.parent_question_id
				let params = {
					material_id : parentNode.bom_material_id,
					bom_version : parentNode.bom_version,
					question_id : question_id
				}

				let initIndex = parentNode.initIndex;
				this.ajax.postStream(url,params,(d)=>{
					if(!d.body.result){
						this.$message.error(d.body.msg);
						parentNode.hasLoading = false;
						return;
					}
					let list =  d.body.content.list || [];

					if(!list.length){
						node.data.haveChildren = false;
						return;
          }
          list.forEach(element => {
            element.id = element.bom_material_id
          });
 console.log('setChildNode查看物料', list)
 this.tableData = list
 this.originData = list
					this.setNodeKey(list);
				});
			},

			conditionChange(){
				var d = this.search.condition;
				this.condition = '';
				if(d == 'BOM_GROUP' || d == 'IF_ADDITIONAL'){
					this.condition = {"EQAUL":'等于'};
					this.search.symbol = "EQAUL";

				}else{
					this.condition = {"CONTAIN":'包含',"EQAUL":'等于'};
				}

				this.tongyongShow = d == 'IF_ADDITIONAL'?true:false;

				if(this.search.string == 'Y' || this.search.string == 'N' || this.search.string == 'ALL'){
					this.search.string = null;
				}
			},

			/**
			*是bom接口还是全部物料接口
			*如果是拆分物料接口，当前选择的问题没有bom版本号，则需要根据物料ID拿到所有的bom版本号
			*/
			changeGoods(){
				let question = this.getChoiceQuestion();
				this.isNeedBomVerison = this.search.all == 1?false:true;
				this.search.version = this.search.all == 1?null:question.bom_version;

				this.templateType = this.search.all==0?2:1;
			},
			/**
			*选择问题商品
			*需要重新初使化数据
			**/
			changeQuestion(){
				this.page.page_no = 1;
				let type = this.currentType;
				this.setSearchParams();
				//只要当前的接口不为拆分物料的接口，那不同的问题都是可以挂相同的产品的
				if(type != 2){
					this.realy_question_sub_id = this.question_sub_id;
				}else{//拆分物料，获取bom
					//this.bomVersion = [];
					this.getAllVersion();
				}


			},

			/**
			*关闭当前弹出框组件
			*/
            closeCurrentComponent(){
                this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
			},

			/**
			*确认回调函数
			**/
			confirmAddGoods(){
				console.log('看看这个保存')
        let selectedData = ''
        if (this.currentType==1) {
          selectedData = this.selectedData
        } else {
          selectedData = this.$refs.xptBoom.getCheckboxRecords()
        }
        // var selectedData = this.selectedData;
				if(!selectedData || !selectedData.length){
					this.$message.error('请选择数据');
					return;
				}
				//多个接口拿数据，所以要对调整的数据再次进行整理
				//var type = this.getMatchKey(this.colData);
				var type = this.currentType;

				//this.closeCurrentComponent();
				var d = JSON.parse(JSON.stringify(selectedData));
				this.params.callback && this.params.callback({
					data:d,
					type:type,
					questionId:this.realy_question_sub_id,
					removeAlert: () => this.closeCurrentComponent(),
				});
        this.$refs.list && this.$refs.list.clearSelection();
        this.$refs.xptBoom && this.$refs.xptBoom.clearCheckboxRow()

				this.selectedData = '';
			},


            sizeChange(size){
                // 每页加载数据
				this.page.page_size = size;
                this.page.page_no = 1;
				this.getList();
			},
            pageChange(page_no){
                // 页数改变
                this.page.page_no = page_no;
                this.getList();
            },


			select(selections){
				this.selectedData = selections.length?selections:'';
			},


            searchList(){
              //模糊搜索
				this.page.page_no = 1;
				this.getList();
			},

            /**
			*'/afterSale-web/api/aftersale/order/choose/pickfromorder'-->有源单
			*'/afterSale-web/api/aftersale/order/choose/pickfrommaterial'---->无源单
			*发运明细的物料查询
			**/
			getMergeTradeList(){
				//let obj = this.getChoiceQuestion();
				var data = this.params.info;
				if(!data) return;
				//合并订单ID拿发运明细
				let mergeTradeId = data.merge_trade_id;
				let url = mergeTradeId?'/afterSale-web/api/aftersale/order/choose/pickfromorder':'/afterSale-web/api/aftersale/order/choose/pickfrommaterial';
				let search = this.search2;
				let params = {
					field_value: search.field_value,
				    limit_field: search.limit_field,
				    operator: search.operator,

				    merge_trade_id: mergeTradeId?mergeTradeId:'',
					page_size: this.page.page_size,
					page_no: this.page.page_no
				}
				this.ajax.postStream(url, params, res => {
					let d = res.body;
					if(!d.result){
						this.$message.error(d.msg);
						return;
					}
					//this.colData = this.getShowOrderParams();
					this.list = d.content.list || [];
					this.count = d.content.count;
				})
			},
			/**
			*获取全部物料
			**/
			getAllGoodsList(){
				var url = '/material-web/api/material/getMaterialList';
				let params = {
					disableStatus : 'A',
					// sale_enabled: '1',
                    audit_flag: '1'
				}

				params.select_key = this.search.condition;
				params.select_type = this.search.symbol == 'EQAUL'?'=':'%';
				params.select_value = this.search.string;
				if(params.select_key == 'IF_ADDITIONAL'){
					params.select_value = params.select_value == 'Y'?1:params.select_value == 'N'?0:'ALL';
				}
				params.page = {
					length:this.page.page_size,
					pageNo:this.page.page_no
				}
				this.ajax.postStream(url,params,(d)=>{
					if(!d.body.result){
						this.$message.error(d.body.msg);
						return;
					}
					//this.colData = this.getShowGoodsParams();
					this.list = d.body.content.list || [];
					this.count = d.body.content.count;
					console.log('d',d);

				});
			},
			/**
			*设置node-key值
			*bom_material_id + bom_version
			*如果传参过来，则是对指定的数组进行ID的设置，如果是没有，则是对整个list进行操作
			***/
			setNodeKey(data){
				var list = data || this.list;
				if(!list||!list.length) return;

				list.map((a,b)=>{
					a.initIndex = this.initIndex;
					this.initIndex++;
					a.haveChildren = a.bom_version?true:false;
        })
        console.log('setNodeKeysetNodeKey', list)
				return list;
			},

			/**
			*获取bom版本
			**/
			getBomGoodsList(){
				var url = '/afterSale-web/api/aftersale/plan/listBomMaterial';
				let question = this.getChoiceQuestion();
				let question_id = question.isReturnGoodsOfBill?question.question_goods_id:question.parent_question_id
				let params = {
					material_id : question.material_id, // 29351304
					bom_version : this.search.bomVersion, // 'LS01ZHR277040_V2.0'
					/*question_id : question.parent_question_id*/
					question_id : question_id
				}
				/*let params = {
					material_id : '6523314',
					bom_version : 'LS02CZSN2R001_V1.0',
					question_id : question.parent_question_id
				}*/

				params.select_key = this.search.condition;
				params.select_type = this.search.symbol;
				params.select_value = this.search.string;

				/*params.page_size = this.page.page_size;
				params.page_no = this.page.page_no;*/

				//无源单商品问题的非成品退换货
				//this.isNeedBomVerison?params.bom_version = this.search.bomVersion:'';
				//params.bom_version?'':params.bom_version = this.search.bomVersion;
				this.ajax.postStream(url,params,(d)=>{
					if(!d.body.result){
						this.$message.error(d.body.msg);
						return;
					}
					//this.colData = this.getShowBomParams();
          this.list = d.body.content.list || [];
           console.log('getBomGoodsList查看物料', this.list)
          this.tableData = this.list
          console.log('getBomGoodsListlastone', this.list)
          this.originData = this.list
					this.setNodeKey();
					//this.count = d.body.content.count;

				});
			},

	        /**
	        *根据物料ID获取所有的bom版本号
	        **/
	        getAllVersion(callback){
	        	var data = this.getChoiceQuestion();
	        	var d = {
	        		sourceMaterialId : data.material_id
	        	}
				d.page = {
	        		length : 100,
	        		pageNo : 1
	        	}
	        	/*this.isNeedBomVerison = true;
	        	this.search.bomVersion = data.bomVersion || null;*/
	        	this.ajax.postStream('/material-web/api/material/getMaterialListAndBOM',d,(res)=>{
	        		var resData = res.body;
	        		if(!resData.result) return;
	        		var version = {};
	        		var list = resData.content.list || [];

	        		list.map((a,b)=>{
	        			let c = a.bomVersion;
	        			version[c] = c;
	        			this.search.bomVersion?'':this.search.bomVersion = c;

	        		});
	        		this.bomVersion = null;
	        		this.bomVersion = version;

	        		callback && callback();
	        	});
	        },
	        /**
	        *初使化数据
	        ****/
	        initData(){
	        	var list = [];
	        	this.params.questionList.map((a,b)=>{
	        		if(a.if_goods_question == 'Y'){
	        			a.popName = a.material_code+'+'+a.material_name
	        		}else{
	        			a.popName = a.question_description;
	        		}
	        		list.push(a);
	        	});
	        	this.questionData = list;
	        	this.realy_question_sub_id = this.question_sub_id = list[0].question_sub_id;

				this.setSearchParams();
				if(this.search.bomVersion){//初使化的时候，有bom信息则不会去执行查找bom接口
					this.getAllVersion();
				}


				this.getList();
	        },

	        /***
	        *根据列表不同信息调用不同的接口
	        *type 1 -->全部物料（成品），2--->拆分物料(bom),3---->发运明细
	        ***/
	        getList(){
	        	//this.colData = JSON.parse(JSON.stringify(this.templateColData));
	        	this.currentType = this.templateType;
	        	this.realy_question_sub_id = this.question_sub_id;
	        	//var type = this.getMatchKey();
	        	let type = this.currentType;
	        	this.selectedData = '';

	        	//发运明细接口
	        	if(type == 3){
	        		this.getMergeTradeList();
	        		return;
	        	}
	        	//全部物料
	        	if(this.search.all == 1){//全部物料
	        		this.getAllGoodsList();
	        		return;
	        	}


	        	//拆分物料
	        	if(this.search.bomVersion){
	        		this.getBomGoodsList();
	        		return;
	        	}
	        	this.getAllVersion(()=>{
	        		this.getBomGoodsList();
	        	})

	        },

	        /**
	        *根据已选的问题设置刷选参数，列表信息值
	        *逻辑处理，首先得确认是退换货方案还是补件方案
	        *然后再细分两个方案当中的添加商品
	        **/
	        setSearchParams(){
	        	this.setReturnExchangeGoodsParams();
			},
			/**
			*设置退换货方案弹出框添加商品
			*退换货方案需要要区分到底是换货还是退货
			 *type 1 -->全部物料（成品），2--->拆分物料(bom),3---->发运明细
			***/
			setReturnExchangeGoodsParams(){
				console.log('kdkdkdkdkdkdkd');
				let info = this.params.info || {};
	        	let d = this.getChoiceQuestion();
	        	let isHasSource = d.if_has_source == 'Y'?true:false;
	        	let isGoodsQuestion = d.if_goods_question == 'Y'?true:false;
	        	//直接分商品问题和非商品问题

	        	//商品问题
	        	if(isGoodsQuestion){
	        		this.isBomOrAllGoods = true;
					this.templateType = 2;
					this.currentType?'':this.currentType = this.templateType;

					this.search.bomVersion = d.bom_version || null;
					this.isNeedBomVerison = true;
	        		this.bomVersion = null;

					this.search.all = "0";
					this.isDisabledAll = (info.isReturnGoods && isHasSource)
					return;
	        	}

	        	//非bom接口重新选择值
	        	this.search.bomVersion = null;
				this.isNeedBomVerison = false;
        		this.bomVersion = null;

	        	/*********非问题商品************/

	        	//退货添加商品
	        	if(info.isReturnGoods){
	        		if(isHasSource){
	        			//发运接口明细
	        			this.isBomOrAllGoods = false;
	        			this.templateType = 3;
						this.currentType?'':this.currentType = this.templateType;

						return;
	        		}
	        		this.isBomOrAllGoods = true;
					this.search.all = "1";
					this.isDisabledAll = true;
					this.templateType = 1;
					this.currentType?'':this.currentType = this.templateType;
					return;

				}

				//换货与补件
				this.isBomOrAllGoods = true;
				this.search.all = "1";
				this.isDisabledAll = true;
				this.templateType = 1;
				this.currentType?'':this.currentType = this.templateType;
				return;
				//退货页签

			},

	        /**
	        *获取当前已选的问题
	        *当没有传参的时候，拿当前显示问题的ID
	        ***/
	        getChoiceQuestion(questionId){
	        	//var question_sub_id = this.question_sub_id;
	        	var question_sub_id = questionId || this.question_sub_id;
	        	let obj;
	        	this.questionData.map((a,b)=>{
	        		if(a.question_sub_id == question_sub_id) {
	        			obj = a;
	        			return;
	        		}
	        	});
	        	return obj;
	        },

      onShowImg(row) {
        this.$root.eventHandle.$emit('alert', {
          title: '查看图片',
          style: "width:800px;height:600px;",
					params: {
            bom_material_num: row.bom_material_num || row.materialNumber,
            material_name: row.bom_material_name || row.materialName,
          },
					component: () => import('@components/after_solutions/materialPicture'),
				})
      }
		},

		created(){
			this.initData();

		},
		//注册组件
		components:{
			'tree':tree
		}
	}
</script>
<style type="text/css" scoped>
	.el-table__body-wrapper{margin-bottom:20px;}
	#pmm_ul  li{float: left;line-height: 24px;}
  .vxe-table .vxe-body--column:not(.col--ellipsis){
    padding: 0px !important
  }
</style>
<style>
.vxe-table .vxe-body--column:not(.col--ellipsis){
    padding: 0px !important
  }
</style>
