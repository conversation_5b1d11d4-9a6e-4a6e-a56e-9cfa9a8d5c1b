<!--退换货方案-->
<template>
	<div class="xpt_pmm">
		<el-form label-position="right" label-width="120px" :model="info" :rules="rules" ref="info" >
			<el-row :gutter='40'>
				<el-col :span='8'>
					<el-form-item label="方案编号1" >
						<el-input size='mini' disabled v-model="info.after_plan_no" placeholder="系统自动生成"></el-input>

					</el-form-item>

					<!-- <el-form-item label="退货类型" prop="returns_type" >
						<el-select  size='mini'  placeholder="类型" v-model="info.returns_type" @change="changeReturnTypeEvent" @visible-change="chickChangeCanLiage">
							<el-option
									v-for="(value,key) in returnType"
									:label="value"
									:value="key"
									:key='key'>
							</el-option>
						</el-select>
						<el-tooltip v-if='rules.returns_type[0].isShow' class="item" effect="dark" :content="rules.returns_type[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item> -->
					<el-form-item label="方案状态">
						<el-input  size='mini' v-model="info.status_string" disabled></el-input>
					</el-form-item>
					<el-form-item label='运费方式'>
						<el-select  size='mini'  placeholder="类型" v-model='info.transport_method' :disabled="!canAddNewOne">
							<el-option label="现付" value="NOW_PAY"></el-option>
							<el-option label="到付" value="PICK_PAY"></el-option>
						</el-select>
					</el-form-item>

				</el-col>
				<el-col :span='8'>
					<el-form-item label="日期">
						<el-date-picker type="date" placeholder="选择日期" size='mini' :editable="false" v-model="info.after_plan_date" :picker-options="pickerOptions" :disabled="!canAddNewOne"></el-date-picker>
					</el-form-item>


					<el-form-item label="返货类型" prop="transport_type">

						<xpt-select-aux v-model='info.transport_type' aux_name='freightType' :disabled="!canAddNewOne" :disabledOption='disabledOption'></xpt-select-aux>
						<el-tooltip v-if='rules.transport_type[0].isShow' class="item" effect="dark" :content="rules.transport_type[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="上门取货" v-if="info.transport_type === 'RESALE'">
						<el-checkbox v-model="info.if_cargo" true-label="Y" false-label="N" :disabled="ifCargoDisabled"></el-checkbox>
					</el-form-item>
				</el-col>
				<el-col :span='8'>

					<el-form-item label="客户承担费用" prop="customer_fee">
						<el-input  size='mini' type="number" v-model="info.customer_fee" min="0" :disabled="!canAddNewOne"></el-input>

						<el-tooltip v-if='rules.customer_fee[0].isShow' class="item" effect="dark" :content="rules.customer_fee[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>

					</el-form-item>
					<el-form-item label="退货费用" >
						<el-input size='mini' type="number" v-model="info.delivery_fee"  disabled></el-input>

					</el-form-item>
					<el-form-item label="取货费用" >
						<el-input size='mini' type="number"v-model="info.fetch_goods_fee"  disabled></el-input>

					</el-form-item>

				</el-col>


			</el-row>
			<el-row :gutter='40' style='height:50px'>
				<el-col :span='24'>
					<el-form-item label="备注信息" class="pmm_form1" prop="remark">
					    <el-input type="textarea" v-model="info.remark" :disabled="!canAddNewOne" :maxlength="255"></el-input>
					    <el-tooltip v-if='rules.remark[0].isShow' class="item" effect="dark" :content="rules.remark[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter='40'>
				<el-col :span='24'>
					<el-form-item label="商品信息" style="margin:10px 0">
					    <el-button type='primary' size='mini' @click="addFinishedGoods" :disabled="addGoodsBtnDisable">添加成品</el-button>
					    <el-button type='primary' size='mini' @click="addGoods" disabled>增加商品</el-button>
					    <el-button type='primary' size='mini' @click="updateStore" :disabled="upDateBtnIsDisable">刷新可用库存</el-button>
					    <el-button type="danger" size='mini' @click="del" :disabled="goodsBtnIsDisable">删除</el-button>

					</el-form-item>

				</el-col>
			</el-row>
		</el-form>
		<div>

		</div>
		<!-- <xpt-list
			:data='rejectLists'
			:colData='rejectCols'
			:showHead='false'
			:orderNo="true"
			selection=''

		></xpt-list> -->
		<el-tabs v-model="activeName1" v-show="info.after_plan_no && rejectLists.length">
			<el-tab-pane label="驳回记录" name="first1">
				<xpt-list
					:data='rejectLists'

					:colData='rejectCols'

					:showHead='false'
					:orderNo="true"
					selection=''

				></xpt-list>
			</el-tab-pane>

	    </el-tabs>

		<el-tabs v-model="activeName">
	    	<el-tab-pane label="退货" name="first">
	    		<xpt-headbar>
					<el-button type="primary" size="mini" slot='left' @click='uploadFun' :disabled="!canAddNewOne">上传图片<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload></el-button>
					<el-button type="success" size="mini" slot='left' @click='pictureFun'>查看或者下载附件</el-button>

  				</xpt-headbar>

	    		<el-table :data="returnGoods" border @selection-change="selectReturnGoods"  ref="returnGoods" class='xpt_pmm_scroll' @row-click="rowClick" :row-class-name="tableRowClassName">

	    			<el-table-column  type="selection" width="55" align="center"></el-table-column>

					<el-table-column label="序号" type="index"></el-table-column>
					<el-table-column label="问题商品编码" prop="question_goods_code" show-overflow-tooltip width="150"></el-table-column>
				    <el-table-column label="物料编码" show-overflow-tooltip prop="material_code" width="150">

				    </el-table-column>
				    <el-table-column label="物料名称" prop="material_name" show-overflow-tooltip width="100"></el-table-column>
				    <el-table-column label="规格描述" prop="material_desc" show-overflow-tooltip width="200" ></el-table-column>
				    <el-table-column label="单位" prop="units" show-overflow-tooltip width="80"></el-table-column>

				    <el-table-column label="数量" prop="number"  width="80">
				    	<template slot-scope="scope">
				    		<el-input size="mini" style="width:100%" v-model="scope.row.number" type="number" disabled></el-input>
				    	</template>
				    </el-table-column>

				    <el-table-column label="退货描述" prop="reason" show-overflow-tooltip width="200">
				    	<template slot-scope="scope">
				    		<el-input size="mini" style="width:100%" v-model="scope.row.reason" :disabled="!canAddNewOne"></el-input>
				    	</template>
				    </el-table-column>


				    <el-table-column label="退货方式" width="80">
				    	<template slot-scope="scope">
				    		<el-select placeholder="请选择" size="mini" style="width:100%" v-model="scope.row.returns_type" @change="changeType(scope.row)" :disabled="!canAddNewOne" @visible-change="chickChangeCanLiage">
									<!-- <el-option
									 v-for="(value,key) in returnGoodsType"
									 	:label="value"
				      					:value="key"
										:key='key'>
									</el-option> -->
									<!-- 'RETURN':'退货',
            		'CHANGE':'换货' -->
									<el-option label="退货" value="RETURN" key='RETURN'></el-option>
									<el-option label="换货" value="CHANGE" key='CHANGE' :disabled="otherInfo.form.if_dealer == 'Y'"></el-option>
				    		</el-select>


				    	</template>
				    </el-table-column>
				    <el-table-column label="是否退款" width="80">
				   		<template slot-scope="scope">
					    	<el-select placeholder="请选择" size="mini" style="width:100%" v-model="scope.row.if_refund" :disabled="!canAddNewOne?true:scope.row.returns_type=='CHANGE'">
									<el-option
									 v-for="(value,key) in yesOrNo"
									 	:label="value"
				      					:value="key"
										:key='key'>
									</el-option>
				    		</el-select>
			    		</template>
				    </el-table-column>


				    <el-table-column label="退货仓库"  width="150">
				    	<template slot-scope="scope">
				    		<el-input size='mini' icon="search" @click="selectWarehouse(scope.row)" readonly style="width:100%" v-model="scope.row.returns_storage" v-if="canAddNewOne && info.transport_type !== 'RESALE' && info.transport_type !== 'DEALER'"></el-input>
				    		<el-input size='mini' icon="search"  readonly style="width:100%" v-model="scope.row.returns_storage" v-else disabled></el-input>
				    	</template>
				    </el-table-column>

				    <el-table-column label="成品实际售价" prop="act_price" width="100" show-overflow-tooltip></el-table-column>
				    <el-table-column label="退货费用" prop="returns_delivery_fee" width="100" show-overflow-tooltip></el-table-column>
				    <el-table-column label="取货费用" prop="fetch_goods_fee" width="100"  show-overflow-tooltip></el-table-column>

				    <el-table-column label="物料BOM版本" prop="bom_version" width="150"></el-table-column>

				    <el-table-column label="存货类别" prop="inventory_category" width="100"></el-table-column>
				    <el-table-column label="销售订单号" prop="sale_order_no" width="150"></el-table-column>
				    <el-table-column label="是否停产" prop="if_stop_produce" width="80" >
				    	<template slot-scope="scope">
				    		{{scope.row.if_stop_produce=='N'?'否':'是'}}
				    	</template>
				    </el-table-column>

		  		</el-table>

	    	</el-tab-pane>
	    	<el-tab-pane label="换货" name="second" v-if="otherInfo.form.if_dealer != 'Y'" >
	    		<el-table :data="exchangeGoods" border  @selection-change="selectExchangeGoods"  ref="exchangeGoods" class='xpt_pmm_scroll' @row-click="rowClick" :row-class-name="tableRowClassName">
	    			<el-table-column  type="selection" width="55" align="center"></el-table-column>
					<el-table-column label="序号" type="index" width="55"></el-table-column>
					<el-table-column label="商品编码" width="200" prop="material_code"></el-table-column>
				    <el-table-column label="商品名称" width="200" prop="goods_name"></el-table-column>
				    <el-table-column label="单位" width="80" prop="units"></el-table-column>
				    <el-table-column label="数量" width="100" prop="number">
				    	<template slot-scope="scope">
				    		<el-input size="mini" style="width:100%" type="number" v-model="scope.row.number" disabled></el-input>
				    	</template>
				    </el-table-column>
				    <el-table-column label="换货原因" width="300" >
				    	<template slot-scope="scope">
				    		<el-input size="mini" style="width:100%" v-model="scope.row.change_reason" :disabled="!canAddNewOne"></el-input>
				    	</template>
				    </el-table-column>

				    <el-table-column label="标准售价" width="150" prop="standard_price">
				    	<!-- <template slot-scope="scope">
				    		<el-input size="mini" style="width:100%" v-model="scope.row.standard_price" disabled></el-input>
				    	</template> -->
				    </el-table-column>
				    <el-table-column label="实际售价" width="150" prop="actual_price">
				    	<!-- <template slot-scope="scope">
				    		<el-input size="mini" style="width:100%" v-model="scope.row.actual_price" v-if="!canAddNewOne || scope.row.isChange" disabled type="number" min="1"></el-input>
				    		<el-input size="mini" style="width:100%" v-model="scope.row.actual_price" v-else @change="calcPriceDiffEvent(scope.row)" type="number"></el-input>
				    	</template> -->
				    </el-table-column>
				    <el-table-column label="差价" width="150" prop="price_diff">
				    	<template slot-scope="scope">
				    		<el-input size="mini" style="width:100%" v-model="scope.row.price_diff" disabled></el-input>
				    	</template>
				    </el-table-column>
				    <el-table-column label="价目表" width="150" prop="price_list">
				    	<template slot-scope="scope">
				    		<el-input size='mini' icon="search"  readonly style="width:100%" v-model="scope.row.price_list"  v-if="!canAddNewOne || scope.row.isChange" disabled></el-input>
				    		<el-input size='mini' icon="search"  readonly style="width:100%" v-model="scope.row.price_list" @click="selectPriceList(scope.row)" v-else></el-input>
				    	</template>
				    </el-table-column>
				    <el-table-column label="运费" width="150" prop="delivery_fee"></el-table-column>

				    <el-table-column label="可用库存" prop="valid_stock"></el-table-column>
				</el-table>

			</el-tab-pane>
	  	</el-tabs>
		<el-row>
	  		<!-- 地址 -->
	  		<el-form label-position="right" label-width="120px" :model="info" :rules="rules" ref="addressInfo">
				<el-form-item label="确认信息" >
					<!-- <a href="javascript:;" @click="modify" v-if="!canRecall">{{text}}</a> -->
            		<a href="javascript:;" @click="addAddress">添加地址</a>
					<el-button type='primary'  size='mini' style="margin-top:5px;margin-left:5px;" @click="choiceAddress" :disabled="canRecall">选择客户地址</el-button>
				</el-form-item>
				<div style="overflow:hidden;width:100%;">
					<el-form-item label="取货地址" prop="province" style="float:left;width:auto;">
						<el-select  placeholder="请选择" size='mini'  v-model="info.province" @change="changeProvice" :disabled="!canEditAddress">
						    <el-option
						      v-for="(value,key) in province"
						      :key="key"
						      :label="value"
						      :value="parseInt(key)">
						    </el-option>
						 </el-select>
					</el-form-item>
					<el-form-item label="" prop="city" style="float:left;width:auto;margin-left:-110px;">
						 <el-select placeholder="请选择" size='mini'  v-model="info.city" @change="changeCity" :disabled="!canEditAddress">
						    <el-option
						      v-for="(value,key) in city"
						      :key="key"
						      :label="value"
						      :value="parseInt(key)">
						    </el-option>
						 </el-select>
					</el-form-item>
					<el-form-item label="" prop="area" style="float:left;width:auto;margin-left:-110px;">
						 <el-select  placeholder="请选择" size='mini' v-model="info.area" @change="changeArea" :disabled="!canEditAddress">
						    <el-option
						      v-for="(value,key) in area"
						      :key="key"
						      :label="value"
						      :value="parseInt(key)">
						    </el-option>
						 </el-select>

					</el-form-item>
					<el-form-item label=""  style="float:left;width:auto;margin-left:-110px;" >
						 <el-select  placeholder="请选择" size='mini'  v-model="info.street" :disabled="!canEditAddress">
						    <el-option
						      v-for="(value,key) in street"
						      :key="key"
						      :label="value"
						      :value="parseInt(key)">
						    </el-option>
						 </el-select>

					</el-form-item>
				</div>

				<el-form-item label="详细信息" prop="receiver_addr">
					<el-input size='mini' v-model="info.receiver_addr" style="width:80%" :disabled="!canEditAddress"></el-input>
					 <el-tooltip v-if='rules.receiver_addr[0].isShow' class="item" effect="dark" :content="rules.receiver_addr[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>

				<el-form-item label="收货人电话" prop="reveiver_phone">
					<el-input size='mini'  v-model="info.reveiver_phone" :disabled="!canEditAddress" ></el-input>
					<el-tooltip v-if='rules.reveiver_phone[0].isShow' class="item" effect="dark" :content="rules.reveiver_phone[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
				<el-form-item label="收货人姓名" prop="receiver_name">
					<!-- <el-input size='mini'  v-model="info.receiver_name" :disabled="!canEditAddress"></el-input> -->
					<!-- <xpt-eye-switch v-model="info.receiver_name" hideType="name" :disabled="!canEditAddress" :readonly='true' :hideBorder="true" :isCallback="true" :aboutNumber="otherInfo.merge_trade_no" @onOpenHideData="decryption"></xpt-eye-switch> -->
	            <el-input size='mini'  v-model="info.receiver_name" icon="linshi-eye-open" :on-icon-click="decryption" :disabled="!canEditAddress"></el-input>

					<el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-form>

			<div style="margin:10px 0;text-align:center">
		  		<el-button type='primary' size='mini' :disabled="!otherInfo.canEditSolutions?true:!canAddNewOne" @click="saveEvent">保存</el-button>
		  		<el-button type='primary' size='mini' :disabled="!otherInfo.canEditSolutions?true:!canSubmit" @click="submitEvent">提交</el-button>
		  		<el-button type='primary' size='mini' :disabled="!otherInfo.canEditSolutions?true:!canRecall" @click="recallEvent">撤回</el-button>
		  		<el-button type='primary' size='mini' :disabled="!otherInfo.canEditSolutions" @click="addNew">新增</el-button>
          <el-button type='primary' size='mini' v-show="persuadeSuccessVisibility" @click="persuadeSuccess">说服成功</el-button>
		  	</div>
	  	</el-row>


	</div>
</template>
<script>
	/*import addressComponent from '@components/after_solutions/otherInfo2.vue';*/
	import validate from '@common/validate.js';
	import fn from '@common/Fn.js';
	import returngoods from './model/returngoods';
	import exchangegoods from './model/exchangegoods';
	import addressInfo from './model/addressInfo';
	import rejectlist from './model/rejectlist';
	export default {
		mixins: [returngoods,exchangegoods,addressInfo,rejectlist],
	    props:['selectedData','otherInfo','copySelectData','operatParams','allQuestions', 'refundingPaymentList'],
		data(){
            var self = this;
			return {
				questionData:null,//传过来的问题信息
				canAddNewOne:true,//是否可以新建方案
				canSubmit:false,//是否可以提交方案
				canRecall:false,//是否可撤回
				//fee:0,//运费
				//
				//feeObj:{},//费用对像
				canLinkage:false,//退货类型改变事件是否要联动，如果是从编辑进入，开始不改动，如果是新增，则需要
				canLinkage2:false,//返货类型改变事件是否要联动，如果是从编辑进入，开始不改动，如果是新增，则需要
				isEdit:false,//是否是编辑

				schemeData:{},//问题对应的方案详情,以对应的子问题question_sub_id作为KEY去储存
				activeName:'first',

				ifCargoDisabled:false,

				//returnGoodsType:fn.returnGoodsType(),


				returnGoodsType:{
					'RETURN':'退货',
            		'CHANGE':'换货'
				},
				pickerOptions:{
		          disabledDate(time) {
		            return time.getTime() < Date.now() - 8.64e7;
		          }
		        },

				info:{
					id:null,
					question_sub_id:null,
					if_goods_question:'Y',
					//question_goods_bom_version:null,
					after_order_id:null,
					after_order_no:null,
					after_plan_group_id:null,//方案组ID
					transport_method:'PICK_PAY',
					fetch_goods_fee:null,
					delivery_fee:null,

					after_plan_no:null,
					after_plan_date:new Date(),
					status:null,
					status_string:null,
					merge_trade_id:null,
					merge_trade_no:null,
					//merge_trade_id:'7754443800980001',
					//type:'INSERT',
					customer_fee:null,
					//returns_type:'UNFINISH_RETURN',
					returns_type:null,
					transport_type: this.otherInfo.form.if_dealer == 'Y' ? 'DEALER' : 'RESALE',//退货再售
					if_cargo: 'Y',
					logistics_order_no:null,
					remark:null,
					logistics_company:null,
					merge_trade_no:null,
					province:null,
					city:null,

					street_name:null,
					city_name:null,
					province_name:null,
					area_name:null,

					source_address_id:null,
					address_id:null,
					area:null,
					street:null,
					receiver_addr:null,
					receiver_name:null,
					reveiver_phone:null


				},

				originalInfo:'',//原始的表头数据
				currentRowselectGoods:null,//当前鼠标选中的明细行

				//退货类型
				returnType:{
					FINISH_CHANGE_RETURN :'成品退换货',
					UNFINISH_RETURN :'非成品退换货'
				},

				//增加商品，删除按钮
				goodsBtnIsDisable:true,
				upDateBtnIsDisable:true,
				addGoodsBtnDisable:true,

				rules:{
					transport_type:validate.isNotBlank({
						self:self,
						trigger:'change'
					}),
					remark:validate.isNotBlank({
						self:self,
						trigger:'change'
					}),
					customer_fee:validate.maxTwoDecimals({
						self:self,
						trigger:'change'
					})
				},
				//禁掉不可选的货运方式
				//返货报废,返货丢件,返货退件,返货待定,取消退货,拉货前报废
				//disabledOption : [{code:'CG_SCRAP'},{code:'CG_LOST'},{code:'CG_RETURN'},{code:'CG_WAIT'},{code:'CG_CANCEL'},{code:'CG_ESCRAP'},{code:'DEALER'}]
				disabledOption:[],
			}
		},
		methods:{

			/**
			*跳到工作台
			***/
			addNew(){
				let params = JSON.parse(JSON.stringify(this.otherInfo));
				/*after_plan_id:null,//编辑,方案ID
					after_question_id:null,//方案所挂的问题ID
					after_plan_group_id:null,//方案组ID*/
					if(params.hasOwnProperty('after_plan_id')){
						delete params.after_plan_id;
					}
					if(params.hasOwnProperty('after_question_id')){
						delete params.after_question_id;
					}
					if(params.hasOwnProperty('after_plan_group_id')){
						delete params.after_plan_group_id;
					}
				this.$root.eventHandle.$emit('creatTab',{
	                name: '售后方案',
	                url: 'after_solutions/solutions',
	                params: {
	                	afterOrderInfo: params
	                },
	                component: () => import('@components/after_solutions/solutions')
	            })
			},
			/**
			*设置问题对应的方案
			***/
			tableRowClassName(row, rowIndex) {
				console.log('asdkfjlksdf');
				if(!this.copySelectData) return '';
				let id = this.copySelectData.question_sub_id || this.copySelectData.originalSubQuestionId;
				let question = this.getShowQuestionByQuestionSubId(id);
				if(!question) return '';
				let question_sub_id = row.question_sub_id;
				//如果是新增的明细的话，可以用父问题ID去比较
				if(question_sub_id == id || (!row.id && this.copySelectData.parent_question_id == row.parentQuestionId)) return 'pmm_blue22';
				return '';

			},

			/**
			*初使化表头数据
			**/
		    initData(data){
		     	if(!data) return;
				for(var key in this.info){
					this.info[key] = data[key] || null;
				}
				this.setOriginalInfo();
				this.setDisabledOfBtn();
				this.initStatus();
			},
			/**
			*获取方案组ID
			**/
			getAfterPlanGroupId(){
				this.ajax.postStream('/afterSale-web/api/aftersale/plan/getPlanGroupId',{},(res)=>{
					var d = res.body;
					if(!d.result) return;
					this.info.after_plan_group_id = d.content;
				})
			},
			/**
			*根据父问题ID获取子问题对像
			***/
			getQuestionByParentId(parent_question_id){
				if(!parent_question_id) return '';
				let g = '';
				this.allQuestions.map((a,b)=>{
					if(a.parent_question_id == parent_question_id) {
						g = a;
						return;
					}
				});
				return g;
			},
			/**
			*根据子问题ID来找到当前问题列表里面的问题对像
			**/
			getShowQuestionByQuestionSubId(id){
				if(!id) return '';
				let g = '';
				this.allQuestions.map((a,b)=>{
					if(a.question_sub_id == id) {
						g = a;
						return;
					}
				});
				return g;
			},
			/**
			*根据子问题ID来找到对应的问题对像
			**/
			getQuestionByQuestionSubId(id){
				//var id = obj.question_sub_id;
				if(!id) return '';
				/*let g = '';
				this.allQuestions.map((a,b)=>{
					if(a.question_sub_id == id) {
						g = a;
						return;
					}
				});*/
				let g = this.getShowQuestionByQuestionSubId(id);
				//如果通过子问题ID找不到问题，则说明是旧的子问题
				if(!g){
					let oldInfo = this.schemeData[id];
					let parentQuestionId = oldInfo.aftersaleOrderQuestionSubVO?oldInfo.aftersaleOrderQuestionSubVO.parent_question_id:'';
					this.allQuestions.map((a,b)=>{
						if(a.parent_question_id == parentQuestionId) {
							g = a;
							return;
						}
					});
				}
				return g;
			},
			/**
			*更新下游单据的列表数据
			**/
			upDateData(){
				this.$root.eventHandle.$emit('updateAfterSaleList');
			},
			/**
			*撤回
			**/
			recallEvent(){
				this.canRecall = false;
				let url = '/afterSale-web/api/aftersale/bill/returns/planReturnsRetract';
				let params = {
					after_plan_group_id  : this.info.after_plan_group_id
				}

				this.ajax.postStream(url,params,res=>{
					let data = res.body;
					this.$message({
						type : data.result?'success':'error',
						message:data.msg
					});
					if(!data.result){
						this.initStatus();
						//this.setBtnDisabledOfClick(!0);
						return;
					}
					//this.getDetail();
					this.getSchemeList();
					this.upDateData();
				});
			},
			/**
			*表头信息是否有改变
			*分新增和编辑,默认是有修改
			**/
			infoIsChange(){
				let info = Object.assign({},this.info);
				let original = this.getOriginalInfo();
				/*if(!original) return false;
				let originalInfo = JSON.parse(original);*/
				let bool = this.compareData(info,original);
				return bool;
			},
			/**
			*明细行数据的对比
			*退换货商品
			**/
			goodsIsChange(){
				var currentExchangeGoods = this.exchangeGoods;
				var currentReturnGoods = this.returnGoods;
				var originalReturnGoods = this.originalReturnGoods?JSON.parse(this.originalReturnGoods):[];
				var originalExchangeGoods = this.originalExchangeGoods?JSON.parse(this.originalExchangeGoods):[];

				var bool = this.compareData(currentExchangeGoods,originalExchangeGoods);
				if(bool) return bool;
				bool = this.compareData(currentReturnGoods,originalReturnGoods);
				return bool;
			},

			/**
			*
			*初使化带过来的otherInfo数据
			**/
			initOtherInfo(){
				var data = this.otherInfo || {};
				//this.type = data.type;
				var info = this.info;
				info.after_order_no = data.after_order_no;
				info.after_order_id = data.after_order_id;
				info.merge_trade_id = data.merge_trade_id;
				info.id = data.after_plan_id;
				info.after_plan_group_id = data.after_plan_group_id;
				//this.otherData.parent_no = data.after_order_no;
				if(data.after_plan_id){
					this.isEdit = true;
				}
				//this.initStatus();

			},

			/**
			*设置表单的原始数据
			*数据的对比，对于同一个对像而言，可以把它转换成字符串来进行对比
			**/
			setOriginalInfo(){
				var info = this.info;
				this.originalInfo = JSON.stringify(info || {});
			},
			/**
			*获取表头原始数据
			**/
			getOriginalInfo(){
				var info = this.originalInfo;
				var data = info?JSON.parse(info):{};
				return data;
			},
			/**
			*保存校验
			*/
			validateIsPass(){
				//var bool = this.addressIsPass;
				//if(!bool) return false;
				if(!this.returnGoods.length) {
					this.$message.error('请选择退货商品');
					return false;
				}
				var bool = true;
				this.$refs.addressInfo.validate((valid) => {
					bool = valid;
				});
				console.log('bool',bool);
				if(!bool) return false;

				this.$refs.info.validate((valid) => {
					bool = valid;
				});
				console.log('bool',bool);
				if(!bool) return false;

				bool = this.validateReturnGoods();
				if(!bool) return false;
				bool = this.validateExchangeGoods();
				return bool;
			},


			/**
			*初使化保存，提交，撤回按钮状态
			*首先得判断售后单的状态是否是完结或是没有权限的状态
			**/
			initStatus(){
				let status = this.info.status;

				let canEditSolution = this.otherInfo.canEditSolutions;
	     		//创建，撤回都是可以编辑和提交的
	     		this.canAddNewOne  =  !canEditSolution?false:(status == 'SAVE' || status == 'WITHDRAWED' || !status )?true:false;

	     		//只有保存,撤回方案才可以提交
	     		this.canSubmit = !canEditSolution?false:(status == 'SAVE' || status == 'WITHDRAWED')?true:false;
	     		//只有提交了的可以撤回
	     		this.canRecall = !canEditSolution?false:(status == 'SUBMITTED' || status == 'SUBMIT')?true:false;

			},


			/**
			*是否可新增方案->是否已提交->退货类型(退换货面板)
			*根据上述的条件一步步判断，添加，删除问题商品按钮是否可操作
			**/
			setDisabledOfBtn(){
				console.log('asdlfkjasdlkfjasdklfjsdkf');
				let canEditSolution = this.otherInfo.canEditSolutions;
				if(!canEditSolution){
					this.goodsBtnIsDisable = this.goodsBtnIsDisable = this.addGoodsBtnDisable = true;
					return;
				}
				this.addGoodsBtnDisable = this.activeName == 'first'?false:true;

				var status = this.info.status;
				this.upDateBtnIsDisable = status=='SUBMITTED' || status == 'SUBMIT'?true:this.activeName == 'first'?true:false;
				if(status=='SUBMITTED' || status == 'SUBMIT' /*|| !this.info.returns_type*/ ){
					this.goodsBtnIsDisable =  true;
					return;
				}
				this.goodsBtnIsDisable = false;
			},

			/**
			*保存事件
			*/
			saveEvent(){
				//JUST TEST
				if(!this.canAddNewOne) return;
				this.canAddNewOne = this.canSubmit = this.canLinkage = this.canEditAddress = false;
				this.save(()=>{
					//this.getDetail();
					this.getSchemeList();
				},!0);

			},
			/**
			*提交事件
			*/
			submitEvent(){
				if(!this.canSubmit ||!this.canAddNewOne) return;
				this.canAddNewOne = this.canSubmit = this.canLinkage = this.canEditAddress = false;
				this.submit();

			},


			/**
			*开始提交
			*/
			submit(){
				this.closeParentTab({callback:()=>{
					this.submitSolution();
				},text:'有数据改动,提交之前是否需要保存'})

				/*var isChange = this.judgeIsChange();
				var _this = this;
				if(isChange){
					//提交
					this.save(()=>{
						this.submitSolution();
					});
					return;
				}
				this.submitSolution();*/
			},
			/***
			*提交方案
			*提交方案的时候有两种情况，第一种是没有任何的修改，直接提交
			*第二种是有修改，先保存再提交
			**/
			submitSolution(bool){
				var id = this.info.after_order_id;
				if(!id) return;
				var data = {
					id : id
				}
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/returns/planReturnsSubmit',data,res=>{
					let data = res.body;
					this.$message({
						type:data.result?'success':'error',
						message:data.msg
					});
					//this.getDetail();
					this.getSchemeList();
					this.upDateData();

				});
			},
			/**
			*判断整个页面的数据是否有修改
			**/
			judgeIsChange(){
				var infoIsChange = this.infoIsChange();
				if(infoIsChange) return true;
				var goodsIsChange = this.goodsIsChange();
				return goodsIsChange;

			},
			/**
			*获取方案组的表头信息
			**/
			getInfo(){
				let info = JSON.parse(JSON.stringify(this.info));


				if(!info.id){
					//新增
					info.address_id = null;
					info.after_order_no = this.otherInfo.after_order_no;
					info.after_order_id = this.otherInfo.after_order_id;
					info.merge_trade_id = this.otherInfo.merge_trade_id;
					info.merge_trade_no = this.otherInfo.merge_trade_no;

				}
				/*info.aftersaleOrderQuestionSubVO = {
					if_goods_question : question.if_goods_question,
					after_order_id : info.after_order_id,
				}*/
				info.city_name = this.street[info.city];
				info.area_name = this.street[info.area];
				info.province_name = this.street[info.province];
				info.street_name = this.street[info.street];

				return info;
			},
			/**
			*根据明细去判断哪些方案被踢出方案组
			*如果明细行里面没有任何旧方案ID,则认为是踢出方案
			***/
			/*deleteSchemeByGoods(){
				let goods = (this.returnGoods || []).concat(this.exchangegoods || []);
				goods = JSON.stringify(goods);
				let deleteScheme = {};
				if(!this.schemeData || JSON.stringify(this.schemeData) == '{}') return;
				for(var key in this.schemeData){
					let after_plan_id = this.schemeData[key].id;
					if(goods.indexOf(after_plan_id) == -1){
						let scheme = JSON.parse(JSON.stringify(this.schemeData[key]));
						let aftersaleOrderQuestionSubVO = this.getOldQuestionInfo(scheme);

						let exchangegoods = scheme.list_aftersalePlanReturnsChangeGoodsVO || [];
						let returnGoods = scheme.list_aftersalePlanReturnsGoodsVO || [];
						let exchangegoodsId = exchangegoods.map(obj=>obj.id);
						let returnGoodsId = returnGoods.map(obj=>obj.id);
						if(scheme.hasOwnProperty('list_aftersalePlanReturnsChangeGoodsVO')){
							delete scheme.list_aftersalePlanReturnsChangeGoodsVO;
						}
						if(scheme.hasOwnProperty('list_aftersalePlanReturnsGoodsVO')){
							delete scheme.list_aftersalePlanReturnsGoodsVO;
						}
						scheme.aftersaleOrderQuestionSubVO = aftersaleOrderQuestionSubVO;
						scheme.list_delete_aftersalePlanReturnsGoodsVO = exchangegoodsId;
						scheme.list_delete_aftersalePlanReturnsChangeGoodsVO = exchangegoodsId;
						deleteScheme[after_plan_id] = scheme;
					}
				}
				return deleteScheme;
			},*/
			/**
			*循环退换货商品得到对应的方案对像
			*方案有两种情况，有可能是新建（必须要有明细才会新建），也有可能是编辑
			*逻辑处理：1:根据退换货商品明细去组装成方案
			*				组装成的方案有可能是新建，也有可能是编辑
			*          2：拿现组装好的方案与旧的方案进行对比
							1：有可能是方案踢出了方案组，有可能是方案进行了修改
			*
			**/
			getBatchScheme(){
				var batchScheme = {};
				//1:根据商品明细去组装成方案
				batchScheme = this.getBatchSchemeByGoods();
				//2:拿现组装好的方案与旧的方案进行对比，哪些方案明细被删除（以旧方案信息作为参考）

				var oldBatchScheme = this.schemeData || {};

				for(var key in oldBatchScheme){
					let newScheme = batchScheme[key];
					//let new_question_sub_id = newScheme.question_sub_id;
					let oldScheme = oldBatchScheme[key];
					if(!oldScheme) continue;
					let id = oldScheme.question_sub_id;
					//删除了一些方案明细
					if(newScheme){
						this.setDelGoodsByOldGoods(newScheme,oldScheme);
						continue;
					}
					//方案踢出方案组
					//let id = oldScheme.question_sub_id;

					let returnGoods = oldScheme.list_aftersalePlanReturnsGoodsVO || [];
					let exchangeGoods = oldScheme.list_aftersalePlanReturnsChangeGoodsVO || [];

					let info = JSON.parse(JSON.stringify(oldScheme));
					let questionInfo = this.getOldQuestionInfo(info);
					info.aftersaleOrderQuestionSubVO = questionInfo;

					delete info.list_aftersalePlanReturnsChangeGoodsVO;
					delete info.list_aftersalePlanReturnsGoodsVO;

					//let info = this.getSingleSchemeDetail(returnGoods[0] || exchangeGoods[0]);

					!info.list_delete_aftersalePlanReturnsGoodsVO?info.list_delete_aftersalePlanReturnsGoodsVO = []:'';
					!info.list_delete_aftersalePlanReturnsChangeGoodsVO?info.list_delete_aftersalePlanReturnsChangeGoodsVO = []:'';

					returnGoods.map((n,h)=>{
						info.list_delete_aftersalePlanReturnsGoodsVO.push(n.id);
					});
					exchangeGoods.map((n,h)=>{
						info.list_delete_aftersalePlanReturnsChangeGoodsVO.push(n.id);
					});

					batchScheme[id] = info;
				}
				//3：看踢出方案组的方案
				/*let deleteScheme = this.deleteSchemeByGoods();
				for(var key in deleteScheme){
						batchScheme[key] = deleteScheme[key];
				}*/
				return batchScheme;
			},

			/**
			*单个新老方案明细进行对比
			*找出删除的商品明细(对于已经存在的明细而言)
			***/
			setDelGoodsByOldGoods(newScheme,oldScheme){
				console.log('看看放到删除里面的东西');
				if(!newScheme || !oldScheme) return;
				/*var questionId = newScheme.question_sub_id;
				var oldNewScheme = this.schemeData[questionId];
				if(!oldNewScheme) return;*/
				let oldRGoods = oldScheme.list_aftersalePlanReturnsGoodsVO || [];
				let oldExGoods = oldScheme.list_aftersalePlanReturnsChangeGoodsVO || [];

				let upDateRGoods = newScheme.list_update_aftersalePlanReturnsGoodsVO || [];
				let upDateExGoods = newScheme.list_update_aftersalePlanReturnsChangeGoodsVO || [];

				let oldRgoodsIds = oldRGoods.map(obj=>obj.id);
				let oldExGoodss = oldExGoods.map(obj=>obj.id);

				let upDateRGoodsIds = upDateRGoods.map(obj=>obj.id || '');
				let upDateExGoodsIds = upDateExGoods.map(obj=>obj.id || '');

				upDateRGoodsIds = JSON.stringify(Array.from(new Set(upDateRGoodsIds)))
				upDateExGoodsIds = JSON.stringify(Array.from(new Set(upDateExGoodsIds)));

				oldRgoodsIds.map((a,b)=>{
					if(a && upDateRGoodsIds.indexOf(a) == -1){
						newScheme.list_delete_aftersalePlanReturnsGoodsVO.push(a);
					}
				})
				oldExGoodss.map((a,b)=>{
					if(a && upDateExGoodsIds.indexOf(a) == -1){
						newScheme.list_delete_aftersalePlanReturnsChangeGoodsVO.push(a);
					}
				})

				/*if(oldRGoods.length && !upDateRGoods.length){
					oldRGoods.map((a,b)=>{
						newScheme.list_delete_aftersalePlanReturnsGoodsVO.push(a.id);
					})
				}

				if(oldExGoods.length && !upDateExGoods.length){
					oldExGoods.map((c,d)=>{
						newScheme.list_delete_aftersalePlanReturnsChangeGoodsVO.push(c.id);
					})
				}

				//退货商品
				var oLength = oldRGoods.length,nLength = upDateRGoods.length;
				for(let aa = 0;aa < oLength;aa++){
					let o = oldRGoods[aa];
					for(let cc = 0;cc < nLength;cc++){
						let f = upDateRGoods[cc];
						if(f.id == o.id) break;
						if(cc == nLength - 1){
							newScheme.list_delete_aftersalePlanReturnsGoodsVO.push(o.id);
						}
					}
				}

				//换货商品
				var oELength = oldExGoods.length,nELength = upDateExGoods.length;
				for(let nn= 0;nn < oELength;nn++){
					let obj = oldExGoods[nn];
					for(let ff = 0;ff < nELength;ff++){
						let obj2 = upDateExGoods[ff];
						if(obj.id == obj2.id) break;
						if(ff == nELength -1){
							newScheme.list_delete_aftersalePlanReturnsChangeGoodsVO.push(obj.id);
						}
					}
				}*/

			},


			/**
			*根据商品明细去组装成方案
			*明细行组装成的方案有可能是新增的方案，也有可能是编辑方案
			*已挂方案的问题用question_sub_id去做key值，没有方案的用最新的question_sub_id去做key值
			**/
			getBatchSchemeByGoods(){
				var batchScheme = {};
				//退货
				this.returnGoods.map((a,b)=>{
					let questionId = a.question_sub_id;
					/*let question = this.getQuestionByParentId(a.parentQuestionId);*/
					let parentQuestionId = a.parentQuestionId;
					let matchQuestion = this.getQuestionByParentId(parentQuestionId);
					let key = a.id?questionId:matchQuestion.question_sub_id;
					if(!batchScheme[key]){
						//batchScheme[questionId] = this.getSingleSchemeDetail(questionId);
						batchScheme[key] = this.getSingleSchemeDetail(a);

					}
					let info = batchScheme[key];

					let good = this.getSaveOfReturnGood(a);
					good.question_sub_id = info.question_sub_id;
					!info.list_update_aftersalePlanReturnsGoodsVO?info.list_update_aftersalePlanReturnsGoodsVO = []:'';
					!info.list_add_aftersalePlanReturnsGoodsVO?info.list_add_aftersalePlanReturnsGoodsVO = []:'';

					if(good.id){
						//修改
						info.list_update_aftersalePlanReturnsGoodsVO.push(good);
					}else{
						//新增
						info.list_add_aftersalePlanReturnsGoodsVO.push(good);
					}
				});

				//换货
				this.exchangeGoods.map((a,b)=>{
					let questionId = a.question_sub_id;
					let parentQuestionId = a.parentQuestionId;
					let matchQuestion = this.getQuestionByParentId(parentQuestionId);
					let key = a.id?questionId:matchQuestion.question_sub_id;

					if(!batchScheme[key]){
						//batchScheme[questionId] = this.getSingleSchemeDetail(questionId);
						batchScheme[key] = this.getSingleSchemeDetail(a);
					}
					let info = batchScheme[key];

					let good = this.getSaveOfExchangeGood(a);
					!info.list_update_aftersalePlanReturnsChangeGoodsVO?info.list_update_aftersalePlanReturnsChangeGoodsVO = []:'';
					!info.list_add_aftersalePlanReturnsChangeGoodsVO?info.list_add_aftersalePlanReturnsChangeGoodsVO = []:'';
					if(good.id){
						//修改
						info.list_update_aftersalePlanReturnsChangeGoodsVO.push(good);
					}else{
						//新增
						info.list_add_aftersalePlanReturnsChangeGoodsVO.push(good);
					}
				});

				//TODO

				return batchScheme
			},
			/**
			*获取旧方案里面的问题信息
			***/
			getOldQuestionInfo(oldScheme){
				if(!oldScheme) return '';
				let info = oldScheme.aftersaleOrderQuestionSubVO;
				if(!info) return '';
				return {
					id:info.id,
					parent_question_id:info.parent_question_id,
					question_description:info.question_description,
					source_id:info.source_id
				}
			},
			/**
			*新增明细行所带过去的问题数据
			*{question} 子问题
			**/
			getSaveQuestionInfo(question){
				if(!question) return {};
				return {
					id:question.question_sub_id,
					parent_question_id:question.parent_question_id,
					question_description:question.question_description,
					source_id:question.question_sub_id
				}
			},

			/**
			*获取单个方案的表头信息
			*{good}明细对像
			**/
			getSingleSchemeDetail(/*id*/good){
				let id = good.question_sub_id;
				let parent_question_id = good.parentQuestionId;

				//let matchQuestion = this.getQuestionByQuestionSubId(id);
				let matchQuestion = this.getQuestionByParentId(parent_question_id);
				let info = this.getInfo();
				let oldInfo = this.schemeData[id];
				//拿旧数据
				//var questionInfo = this.getOldQuestionInfo(oldInfo) || this.getSaveQuestionInfo(matchQuestion);
				var questionInfo = this.getSaveQuestionInfo(matchQuestion);
				info.aftersaleOrderQuestionSubVO = questionInfo;
				let isGoodsQuestion = matchQuestion.if_goods_question == 'Y'?true:false;


				//表头信息需要重置的问题
				info.if_goods_question = info.aftersaleOrderQuestionSubVO.if_goods_question = matchQuestion.if_goods_question;
				if(matchQuestion.if_has_source == 'N' || (matchQuestion.if_has_source == 'Y' && !isGoodsQuestion)){
					info.cust_id = matchQuestion.buyer;
				}else{
					//delete info.cust_id;
					info.cust_id = null;
				}
				//info.aftersaleOrderQuestionSubVO.if_goods_question = matchQuestion.if_goods_question;
				//let singleSchemeDetail = this.schemeData[id];
				if(!oldInfo){
					//没有，则为新增
					info.after_order_no = this.otherInfo.after_order_no;
					info.after_order_id = this.otherInfo.after_order_id;
					info.merge_trade_id = this.otherInfo.merge_trade_id;
					info.merge_trade_no = this.otherInfo.merge_trade_no;
					info.address_id = null;
					info.id = null;
					info.after_plan_no = null;

					//info.customer_fee = null;
				}else {
					info.id = oldInfo.id;
					info.after_plan_no = oldInfo.after_plan_no;
					//info.status = oldInfo.status;
					info.address_id = oldInfo.address_id;
					//info.customer_fee = singleSchemeDetail.customer_fee;
				}
				info.list_update_aftersalePlanReturnsGoodsVO = [];
				info.list_add_aftersalePlanReturnsGoodsVO = [];
				info.list_delete_aftersalePlanReturnsGoodsVO = [];

				info.list_update_aftersalePlanReturnsChangeGoodsVO = [];
				info.list_add_aftersalePlanReturnsChangeGoodsVO = [];
				info.list_delete_aftersalePlanReturnsChangeGoodsVO = [];



				info.question_sub_id = questionInfo.id;
				console.log('info',info);
				return info;
			},

			/**
			*保存
			*调用保存这个方法，有可能是提交，也有可能是保存
			*/
			save(callback,tip){
				var isPass = this.validateIsPass();
				if(!isPass) {
					this.initStatus();
					//this.setBtnDisabledOfClick(!0);
					return;
				}
				console.log('保存');

				let batchScheme = this.getBatchScheme();
				var i = 0;
				for(var Q in batchScheme){//计算出有多少个方案需要保存
					i++;
				}

				var b = 0;
				var successTip = '',errorTip = '';
				var needTip = arguments.length == 2 && tip;
				let isEdit = this.info.id;
				for(var key in batchScheme){
					var a = batchScheme[key];
					this.requestSaveScheme(a,(c,d)=>{
						var description = c.aftersaleOrderQuestionSubVO.question_description;
						d.result?successTip += description+',':errorTip += description +'问题' + d.msg + ',';
						b++;
						if(!this.info.id){//新增，则默认第一个
							this.info.id = d.content;
						}

						if(needTip){
							if(b == i && successTip){
								this.$message.success(successTip + '保存成功');
							}
							if(b == i && errorTip){
								this.$message.error(errorTip);
							}
						}

						if(b == i){
							//没有一个保存成功，则不需要回调函数
							if(!successTip) {
								this.initStatus();
								return;
							}
							//所有异步完成之后再调用回调函数
							/*if(successTip && !isEdit){
								//只有新增的时候需要重新刷一遍数据
								console.log('所有异步问题');
								this.$emit('resetAllData',this.info.after_plan_group_id);
							}*/
							callback && callback(d);
						}
					})
				}

			},
			/**
			*向服务器请求保存
			***/
			requestSaveScheme(d,callback){
				if(!d) return;

				d.encryption_add_id = this.encryption_add_id;
				this.ajax.postStream('/afterSale-web/api/aftersale/plan/saveReturns',d,res=>{
					let data = res.body;
					callback && callback(d,data);
				});
			},
			/**
			*给明细设置对应的子问题ID,父问题ID
			**/
			setQuestionSubId(data){
				var id = data.question_sub_id;
				var parentQuestionId = data.aftersaleOrderQuestionSubVO.parent_question_id;
				var returnGoods = data.list_aftersalePlanReturnsGoodsVO || [];
				var exchangeGoods = data.list_aftersalePlanReturnsChangeGoodsVO || [];
				returnGoods.map((a,b)=>{
					a.question_sub_id = id;
					a.parentQuestionId = parentQuestionId;

				});
				exchangeGoods.map((a,b)=>{
					a.question_sub_id = id;
					a.parentQuestionId = parentQuestionId;

				});
				this.flagExchangeGoods(returnGoods,exchangeGoods);
				return data;
			},
			/**
			*根据方案组ID来获取方案组列表
			***/
			getSchemeList(callback){
				console.log('加载方案组列表')
				var url='/afterSale-web/api/aftersale/plan/getPlanByGroup';
				var data = {after_plan_group_id:this.info.after_plan_group_id};
				this.canLinkage = false;
				this.ajax.postStream(url,data,(res)=>{
					let data = res.body;
					console.log('adfkajsdlfkjsldkf');
					if(!data.result) {
						this.initStatus();
						this.$message.error(data.msg);
						return;
					}
					console.log('data,list',data);
					this.clearGoodsOfPage();//清空数据
					this.schemeData = {};//清空方案明细
					let list = data.content || [];
					this.encryption_add_id = list[0].encryption_add_id

					var returnGoods = [],exchangeGoods = [];
					var info;
					var length = list.length;
					for(var b = 0; b < length; b++){
						let a = list[b];
						this.setQuestionSubId(a);
						if(a.id == this.info.id){
							info = a;
							//this.setQuestionByafterPlanId(a.aftersaleOrderQuestionSubVO);
						}
						this.schemeData[a.question_sub_id] = JSON.parse(JSON.stringify(a));

						let rg = a.list_aftersalePlanReturnsGoodsVO || [];
						let eg = a.list_aftersalePlanReturnsChangeGoodsVO || [];

						rg.map((nn,qq)=>{
							returnGoods.push(JSON.parse(JSON.stringify(nn)));
						});
						eg.map((ff,gg)=>{
							exchangeGoods.push(JSON.parse(JSON.stringify(ff)));
						});
					}
					this.returnGoods = returnGoods;
					this.exchangeGoods = exchangeGoods;


					this.setOriginalExchangeGoods();
					this.setOriginalReturnGoods();
					this.initData(info);//方案组表头信息

					callback && callback(list);
					this.setQuestionListBySoutions(list);

					console.log('this.schemeData',this.schemeData);
					console.log('this.returnGoods',this.returnGoods);
					console.log('this.exchangeGoods',this.exchangeGoods);
				});

			},

			/**
			*设置当前方案对应的问题
			***/
			/*setQuestionByafterPlanId(question){
				if(!question) return;
				this.$emit('setCurrentRow',JSON.parse(JSON.stringify(question)));
			},*/


			/**
			*退货商品和换货商品进行对比
			*找出退货商品带过去的换货商品
			**/
			flagExchangeGoods(returnGoods,exchangeGoods){
				if(!returnGoods || !returnGoods.length) return;
				returnGoods.map((a,b)=>{
					exchangeGoods.map((c,d)=>{
						let combo_str = a.combo_str;
						if(!combo_str) return;
						if(combo_str == c.combo_str){
							c.isChange = true;
							return;
						}
					});
				});
			},
			/**
			*获取退货明细行商品信息
			*主要是保存的时候，要进行明细信息对像的整理
			*{returnGood} 单个退货商品
			***/
			getSaveOfReturnGood(returnGood){
				if(!returnGood) return '';
				var d = Object.assign({},returnGood);

				if(d.hasOwnProperty('parentQuestionId')){
					delete d.parentQuestionId;
				}
				d.remark = this.info.remark;
				return d;
			},
			/**
			*获取退货明细行商品信息
			*主要是保存的时候，要进行明细信息对像的整理
			*{returnGood} 单个退货商品
			***/
			getSaveOfExchangeGood(exchangeGood){
				if(!exchangeGood) return '';
				let d = Object.assign({},exchangeGood);
				if(d.hasOwnProperty('valid_stock')){//库存
					delete d.valid_stock;//库存
				}
				if(d.hasOwnProperty('isChange')){
					delete d.isChange;
				}
				if(d.hasOwnProperty('question_sub_id')){
					delete d.question_sub_id;
				}
				if(d.hasOwnProperty('parentQuestionId')){
					delete d.parentQuestionId;
				}
				return d;
			},

			/**
			*根据商品的改变退货，换货，运费费用
			**/
			calcFeeByChangeGoods(){
				let info = this.info;
				let data = {
					province : info.province,
					city : info.city,
					area : info.area,
					street : info.street,
					logistics_supplier_class: info.logistics_supplier_class,

				}
				let getReturnGoodsParams = this.getReturnFeeAndPickFeeParams();
				let getExchangeGoodsParams = this.getFreightFeeParams();
				data.list_return = getReturnGoodsParams;
				data.list_change = getExchangeGoodsParams;
				this.ajax.postStream('/afterSale-web/api/aftersale/plan/getFee',data,(res)=>{
					let d = res.body;
					if(!d.result){
						this.$message.error(d.msg);
					}
					let content = d.content || {};

					this.info.delivery_fee = content.total_delivery_fee;
					this.info.fetch_goods_fee = content.total_fetch_goods_fee;
					this.fillExchangeGoodFee(content.change_material || []);
					this.fillReturnGoodFeeAndPickGoodsFee(content.return_material || []);

				});
			},
			/**
			*退货，换货费用所需要的参数值
			**/
			getReturnFeeAndPickFeeParams(){
				let list = [];
				let goods = this.returnGoods;
				goods.map((a,b)=>{
					let obj = {
						material_id : a.material_id,
						question_sub_id : a.question_sub_id
						/*sale_order_no :a.sale_order_no*/
					}
					list.push(obj);
				});
				return list;
			},
			/**
			*运费所需要的参数值
			**/
			getFreightFeeParams(){
				let list = [];
				let goods = this.exchangeGoods;
				goods.map((a,b)=>{
					/*let obj = {
						material_id : a.material_id
					}*/
					list.push(a.material_id);
				});
				return list;
			},


			/**
			*删除方案明细
			*需要做一个判断到底是换货的删除还是退货的删除
			*/
			del(){
				console.log('adjflkasdjfksadjflksdfjskdfjsdkf');
				if(this.activeName == 'first'){
					this.delReturnGoodsFn();
				}else{
					this.delExchangeGoodsFn();
				}
			},

			/**
			*添加bom产品
			*/
			addGoods(){
				if(this.activeName == 'first'){
					//退货
					this.getSearchConditionOfReturnGoods();
					return;
				}
				this.getSearchConditionOfExchangeGoods();
			},
			/**
			*对选中的问题进行分类
			*分为问题商品，非问题商品
			**/
			classifyByQuestion(){
				var data = {
					questionList:[],
					unQuestionList:[]
				}
				if(!this.questionData || !this.questionData.length) return data;
				this.questionData.map((a,b)=>{
					if(a.if_goods_question == 'Y'){
						data.questionList.push(a);
					}else{
						data.unQuestionList.push(a);
					}
				})
				return data;
			},

			/**
			*向服务器请求是否可删除商品
			**/
			requestDelGoods(goodsId,callback){
				let url = '/afterSale-web/api/aftersale/plan/validateDelete';
				this.ajax.postStream(url,{list_plan_material_id:goodsId},res=>{
					let data = res.body;
					this.$message({
						type:data.result?'success':'error',
						message:data.msg
					});
					callback && callback(data);
				})
			},

			/**
			*清空页面上显示的数据
			**/
			 clearGoodsOfPage(){
			 	this.returnGoods = [];
				this.exchangeGoods = [];
				this.selectedReturnGoods = [];
				this.selectedExchangeGoods = [];
			},



			/**
			*把方案明细添加到相应的方案组明细里面去
			*添加明细得要注意，它的退货类型必须是一致
			**/
			addGoodsIntoSchemeGroup(schemeDetail){
				if(schemeDetail.returns_type != this.info.returns_type) return;
				let rGoods = schemeDetail.list_aftersalePlanReturnsGoodsVO || [];
				let exGoods = schemeDetail.list_aftersalePlanReturnsChangeGoodsVO || [];
				rGoods.map((a,b)=>{
					let c = Object.assign({},a);
					this.returnGoods.push(c);
				});
				exGoods.map((h,j)=>{
					let o = Object.assgin({},h);
					this.exchangeGoods.push(o);
				})
			},

			/**
			*关闭标签页面
			*/
			closeParentTab(params){
				if(!params) return;
				var isChange = this.canRecall || !this.otherInfo.canEditSolutions?false:this.judgeIsChange();
				if(!isChange){
					params.callback && params.callback();
					return;
				}
				var _this = this;
				var data = {
					ok(){
						_this.save(()=>{
							params.callback && params.callback();
						},!0)

					},
					no(){
						params.callback && params.callback();
					},
					cancel(){
						_this.initStatus();
					}
				}
				params.text?data.txt = params.text:'';
				this.$root.eventHandle.$emit('openDialog',data);
			},



			/**
			*子组件切换
			**/
			switchTab(){
				this.closeParentTab({
					callback:()=>{
						this.$emit("changeSolution");
					}
				})

			},
			/**
			*运费的计算需要传参，因为是不同的方案，定义的数据名会不一样
			*重新计算运费
			**/
			reCalcFee(){

				this.calcFeeByChangeGoods(!0);

			},
			/**
			*根据问题添加相应的明细行数据
			*{questions} 已选的问题
			****/
			setDetailGoodsByQuestion(questions){

				console.log('questions',questions);
				var a = this.questionData || [];
				var b = questions || [];

				var deleteQuestions = !b.length && a.length?JSON.parse(JSON.stringify(a)):[]//新的数据没有，旧的数据存在
				var addQuestions = !a.length && b.length?JSON.parse(JSON.stringify(b)):[];//旧的数据不存在，新的数据有
				//以旧的问题为参照物，可以知道哪些问题是删除的，哪些不变的
				var c = a.length,d = b.length;
				for(var n = 0; n < c;n++){
					let o = a[n];
					for(var j = 0;j < d;j++){
						if(o.question_sub_id == b[j].question_sub_id) break;
						if(j == d-1){
							deleteQuestions.push(o);
							break;
						}
					}
				}

				for(var g = 0;g < d;g++){
					let o = b[g];
					for(var h = 0;h < c;h++){
						if(o.question_sub_id == b[h].question_sub_id) break;
						if(h == c-1){
							addQuestions.push(o);
							break;
						}
					}
				}


				console.log('deleteQuestions',deleteQuestions);
				console.log('addQuestions',addQuestions);

				this.deleteScheme(deleteQuestions);
				//this.addScheme(addQuestions);
				//赋值
				this.questionData = JSON.parse(JSON.stringify(questions));

			},
			/**
			*根据去掉勾选的问题去删除相应明细
			*{questionList}要删除的问题列表
			**/
			deleteScheme(questionList){
				if(!questionList || !questionList.length) return;
				console.log('questionList',JSON.stringify(questionList));
				console.log('this.returnGoods',JSON.stringify(this.returnGoods));
				questionList.map((a,b)=>{
					//TODO通过问题ID删除退换货商品
					var questionId = a.question_sub_id;
					for(var i = 0,j = 0;i < this.returnGoods.length;i = j){
						if(questionId == this.returnGoods[i].question_sub_id){
							this.returnGoods.splice(i,1);
							j = 0;
							continue;
						}
						j++
					}
					for(var ii = 0,jj = 0;ii < this.exchangeGoods.length;ii = jj){
						if(questionId == this.exchangeGoods[ii].question_sub_id){
							this.exchangeGoods.splice(ii,1);
							jj = 0;
							continue;
						}
						jj++
					}
				})
				console.log('this.returnGoods',this.returnGoods);
			},

			/**
			*不会添加之前方案里面的明细
			***/
			addScheme(questionList){
				if(!questionList || !questionList.length) return;
				questionList.map((a,b)=>{
					//如果是成品退换货，则需要添加相应的退货明细
					/*if(this.info.returns_type == 'FINISH_CHANGE_RETURN' && a.if_goods_question == 'Y'){
						let good = this.getReturnGoodInfo(a);
						//good = this.changeReturnGoodsInfo(good,a);
						good.combo_str = new Date().getTime() + b;
						this.returnGoods.push(good);
					}*/
					let good = this.getReturnGoodInfo(a);
					good.combo_str = new Date().getTime() + b;
					//this.calcSingleReturnGoodFee(good);
					this.returnGoods.push(good);
				})
				//this.calcTotalReturnGoodFeeAndPickGoodsFee();
				this.calcFeeByChangeGoods();
			},
			/**
			*新增方案的时候根据merge_material_id改变收货地址
			*用父问题ID去判断有没有改变子问题
			**/
			changeAddressInfoByMaterialId(newQuestionList,oldQuestionList){
				//if(this.isEdit) return;
				if(!newQuestionList || !newQuestionList.length) return;
				if(!oldQuestionList || !oldQuestionList.length) {
					this.getAddressInfoByMergeMaterialId();
					return;
				}
				var id = oldQuestionList[0].parent_question_id;
				var i = newQuestionList.length,a = 0;
				for(;a < i;a++){
					if(newQuestionList[a].parent_question_id == id) return;
				}
				this.getAddressInfoByMergeMaterialId();

			},
			/**
			*单击某行执行的数据
			**/
			rowClick(row, event, column){
				console.log('column',column);
				this.currentRowselectGoods = row;
				this.concatSelectReturnAndExchangeGoods();
			},
			/**
			*合并退货，换货商品
			**/
			concatReturnAndExchangeGoods(){
				let returnGoods = this.returnGoods || [];
				let exchangeGoods = this.exchangeGoods || []
				let goods = returnGoods.concat(exchangeGoods);
				this.$emit('allGoodsmatchQuestions',goods)
			},
			/**
			*合并已选中的退换货
			***/
			concatSelectReturnAndExchangeGoods(){
				/*let returnGoods = this.selectedReturnGoods || [];
				let exchangeGoods = this.selectedExchangeGoods || []
				let goods = returnGoods.concat(exchangeGoods);*/
				this.$emit('selectGoodsmatchQuestions',[this.currentRowselectGoods])
			},
			/**
			*设置已挂方案的问题列表
			***/
			setQuestionListBySoutions(list){
				if(!list || !list.length) return;
				var questions = [];
				list.map((a,b)=>{
					let q = a.aftersaleOrderQuestionSubVO;
					let obj = {
						source_id : q.source_id,
						question_description : q.question_description,
						parent_question_id : q.parent_question_id,
						question_sub_id : q.id,
						//after_plan_no:a.after_plan_no,
						after_plan_id:a.id
					}
					if(q.id == this.info.question_sub_id){
						obj.isCurrentSolution  = true;
					}
					questions.push(obj);
				});
				this.$emit('reSetQuestionListFn',{questions:questions,canEditQuestion:this.canAddNewOne});
			},
			/***
			*设置返货类型的下拉
			**/
			setReturnType(){
				let array = [{code:'CG_SCRAP'},{code:'CG_LOST'},{code:'CG_RETURN'},{code:'CG_WAIT'},{code:'CG_CANCEL'},{code:'CG_ESCRAP'},{code:'THREE_BAG'}]
				let if_dealer = this.otherInfo.form.if_dealer;
				if(if_dealer == 'Y'){
					//是经销订单,除了退后再售，经销退货，其它的都禁掉
					array.push({code:'THREE_BAG'})
					array.push({code:'CUSTOMER'})
					array.push({code:'RESALE'})
				}/*else{
					//禁掉经销退货
					array.push({code:'DEALER'})
				}*/
				this.disabledOption = array;
			},
      /**
       * @Description: 舒服成功的回调方法
       * @author: liangyj
       * @date: 4/9/19
       * @param
       * @return
       */
      persuadeSuccess() {
        const self = this;
        this.ajax.postStream('/afterSale-web/api/aftersale/plan/changeReturnPlanStatus',{after_plan_group_id: this.info.after_plan_group_id},(res)=>{
          if (!res.body.result) {
            self.$message.error(res.body.msg);
            return;
          }
          self.$message.success(res.body.msg);
          self.getSchemeList();
          self.upDateData();
        }, err => {
          self.$message.error(err);
        });
      }
		},
		watch:{
			activeName:function(newVal,oldVal){
				this.setDisabledOfBtn();

			},
			/**
			*判断是否是编辑的初次登录，如果是则直接赋值，如果不是则按逻辑进行
			**/
			selectedData:{
				handler:function(newVal,oldVal){
					console.log('999');
					//newVal[0].merge_material_id = '9075508200980000';
					if(this.canRecall) return;
					if(this.otherInfo.after_plan_id && !this.questionData){
						//编辑的初次登录
						this.questionData = JSON.parse(JSON.stringify(newVal));
						return
					}

					if(JSON.stringify(newVal) == JSON.stringify(this.questionData)) return;
					let oldQuestionData = JSON.parse(JSON.stringify(this.questionData || []));
					this.questionData = JSON.parse(JSON.stringify(newVal));

					this.changeAddressInfoByMaterialId(newVal,oldQuestionData);

				},
				deep:true
			},

			otherInfo:{
				handler:function(newVal,oldVal){
					console.log('asdlkfjalskdfjlsadkfjasldkfj');
					this.initOtherInfo();
					//this.setDisabledOfBtn();
				},
				deep:true
			},
			'operatParams.isSwitchTab':function(newVal,oldVal){
				//切换的提醒
				this.switchTab();
			},
			'operatParams.bool':function(newVal,oldVal){
				console.log('看看有没有响应到');
				this.closeParentTab({callback:()=>{
					var tabName = this.operatParams.params.tabName;
					this.$root.eventHandle.$emit('removeTab',tabName);
				}});
			},
			'info.transport_type': function (){
				//this.info.if_cargo = this.info.transport_type === 'RESALE' ? 'Y' : 'N'
				this.ifCargoDisabled = this.info.transport_type === 'RESALE'?false:true;

				if(!this.canLinkage2){
					this.canLinkage2 = true;
					return;
				}

				this.info.transport_type != 'RESALE' ? this.info.if_cargo='N' : '';
				//切换返货类型时清空退货和换货商品
				this.returnGoods = []
				this.exchangeGoods = []
			},

		},

		mounted(){
			console.log('this.initOtherInfo',this.initOtherInfo);
			console.log('allStorageInfo',this.allStorageInfo);
			this.initOtherInfo();
			this.setDisabledOfBtn();
			this.setReturnType();
			if(!this.isEdit){
				this.questionData = JSON.parse(JSON.stringify(this.selectedData));
				this.getAddressInfoByMergeMaterialId(true);
				this.getAfterPlanGroupId();
				this.canLinkage2 = true;
			}else{
				//编辑
				//this.canLinkage = false;
				//TODO
				console.log('adskfjaskdf');
				this.getSchemeList();
			}

		},
    computed: {
      persuadeSuccessVisibility() {
        return this.otherInfo.canEditSolutions && this.info.status === 'SAVE'? true:false;
      }
    }


	}
</script>
<style type="text/css">
	/*.xpt_pmm .xpt_pmm_scroll .el-table__body-wrapper{max-height: 350px;min-height: 100px;}*/
	.el-form-item.pmm_form1 .el-form-item__content{height: auto;}
</style>
