// 订单业务模式
export const BILL_TYPE_STATUS = {
  PRESELL_ORDER: '预售订单',
  STEP_ORDER: '特权订金订单',
  HFH_ORDER: '火凤凰订单',
  ETICKET_ORDER: '电子凭证订单',
  COMMON_ORDER: '普通订单',
  NOPAY_ORDER: '试用订单',
  YUNFEI_ORDER: '补运费订单',
  BUKUAN_ORDER: '补款订单',
  LIBAO_ORDER: '礼包订单'
}

// 淘宝订单状态
export const TAOBAO_STATUS = {
  TRADE_NO_CREATE_PAY: "没有创建支付交易",
  WAIT_BUYER_PAY: "等待买家付款",
  SELLER_CONSIGNED_PART: "卖家部分发货",
  WAIT_SELLER_SEND_GOODS: "等待卖家发货,即:买家已付款",
  WAIT_BUYER_CONFIRM_GOODS: "等待买家确认收货,即:卖家已发货",
  TRADE_BUYER_SIGNED: "买家已签收,货到付款专用",
  TRADE_FINISHED: "交易成功",
  TRADE_CLOSED: "付款以后用户退款成功，交易自动关闭",
  TRADE_CLOSED_BY_TAOBAO: "付款以前，卖家或买家主动关闭交易",
  PAY_PENDING: "国际信用卡支付付款确认中",
  PARTIAL_SEND_GOODS: "部分发货",
  WAIT_PRE_AUTH_CONFIRM: "0元购合约中",
  PAID_FORBID_CONSIGN: "拼团中订单，已付款但禁止发货"
}
