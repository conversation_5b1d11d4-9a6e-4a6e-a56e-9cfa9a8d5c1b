<!--
* @description: 赠品添加商品
* @author: bin
* @date: 2025/3/14
-->
<template>
  <div class="xpt-flex">
    <custom-list-dynamic
              class="custom-list"
              ref="list"
              selection="checkbox"
              :data="dataSource"
              :colData="columns"
              :pageTotal="pageTotal"
              :btns="btns"
              :showCount='showCount'
              searchPage='aftersale_gift_config'
              searchHolder='请输入搜索条件'
              @search-click="searchData"
              @selection-change="selectChange"
              @page-size-change="sizeChange"
              @count-off="getTotalCount"
              @current-page-change="pageChange">
   		<span slot="btns" style="margin-left: 16px">
					问题商品:
					<el-select placeholder="请选择" size="mini" style="width:250px;" v-model="question_sub_id" @change="changeQuestion">
						<el-option
              v-for="(obj,key) in questionData"
              :label="obj.material_code"
              :value="obj.question_sub_id"
              :key='obj.question_sub_id'>
						</el-option>
		    		</el-select>
				</span>
        <template #picture="{row}">
          <div class="cursor-pointer" @click="onShowImg(row)">查看</div>
        </template>

      <template #comp_amount="{row}">
          <div>{{row.min_comp_amount}}~{{row.max_comp_amount}}</div>
      </template>
    </custom-list-dynamic>
  </div>


</template>
<script>
import CustomListDynamic from '@components/common/list-dynamic-other-new/list-dynamic'
export default {
  name: 'giveaway-modal',
  props:['params'],
  components: {
    CustomListDynamic
  },
  data() {
    return {
      dataSource:[],
      questionData:[],
      question_sub_id:'',
      showCount:false,
      btns:[
        {
          type: 'primary',
          txt: '确定',
          loading: false,
          click:()=> {
            this.onSure()
          }
        },
        // {
        //   type: 'primary',
        //   txt: '刷新可用库存',
        //   loading: false,
        //   click:()=> {
        //     this.onFresh()
        //   }
        // }
      ],
      columns:[
        {
          label: '物料编码',
          prop: 'material_code',
          width:150
        },
        {
          label: '图片',
          prop: 'picture',
          slot:'picture',
          width:60
        },
        {
          label: '物料名称',
          prop: 'material_name',
          width:150
        },
        {
          label: '规格描述',
          prop: 'specification_description',
          minWidth:300
        },
        {
          label: '单位',
          prop: 'unit',
          width:60
        },
        {
          label: '供应来源',
          prop: 'source_of_supply',
          formatter:row=>{
            const obj = {
              SUPPLIER:"供应商",
              INNER:"内部"
            }
            return row && obj[row]
          },
          width:100
        },
        {
          label: '赔付金额区间',
          prop: 'comp_amount',
          slot:'comp_amount',
          width:120
        },
        {
          label: '可用库存',
          prop: 'quantity',
          width:90
        },
        {
          label: '冗余库存数',
          prop: 'inventorySurplus',
          width:90
        },
      ],
      search: {
        length: this.pageSize,
        pageNo: 1,
      },
      pageTotal:0,
      where: [],
      selectionData:[]
    }
  },
  methods: {
    onShowImg(row) {
        this.$root.eventHandle.$emit('alert', {
          title: '查看图片',
          style: "width:800px;height:600px;",
					params: {
            bom_material_num: row.material_code,
            material_name: row.material_name,
          },
					component: () => import('@components/after_solutions/materialPicture'),
				})
    },
    //
    selectChange(e){
      console.log(e,'444')
    },
    // 尺寸改变
    sizeChange(page){
      this.search.length = page
      this.search.pageNo = 1
      this.getDataList();
    },

    getTotalCount(){
      if(!this.dataSource.length){
        this.$message.error("当前列表为空，先搜索内容");
        return;
      }
      if(!!this.countOffFlag){
        this.$message.error("请勿重复点击");
        return;
      }
      this.countOffFlag = true
      this.ajax.postStream('/afterSale-web/api/gift/config/count',{
        page_no: this.search.pageNo,
        page_size: this.search.length,
        page_name: "aftersale_gift_config",
        where: this.where,
        status:1
      },d=>{
        if(d.body.result&&d.body.content){
          this.pageTotal = d.body.content.count;
          this.showCount = true;
          this.countOffFlag = false;
        }

      }, err => {
        this.$message.error(err);
        this.countOffFlag = false
      })
    },
    // 页数改变
    pageChange(pageSize){
      this.search.pageNo = pageSize
      this.getDataList();
    },

    changeQuestion(){
      this.selectionData = []
    },
    // 确定
    onSure(){
      const list = this.$refs.list?.selectRows?.()
      if(list.length === 0){
        return this.$message.error('请选择商品')
      }
      list.forEach(e=>{
        e.question_sub_id = this.question_sub_id
      })
      if(this.params.callback){
        this.params.callback(list)
        this.selectionData = []
      }
    },
    // 刷新
    onFresh(){
     this.getDataList()
    },
    getDataList(resolve){
      let self = this
      let data = {
        page_no: this.search.pageNo,
        page_size: this.search.length,
        page_name: "aftersale_gift_config",
        where: this.where,
        status:1
      }
      this.ajax.postStream('/afterSale-web/api/gift/config/listEnable',data,d=>{
        if(d.body.result&&d.body.content){
          // self.pageTotal = d.body.content.count;
          let dataList = JSON.parse(JSON.stringify(d.body.content.list||[]));

          if(dataList.length == (this.search.length+1)&&dataList.length>0){
            dataList.pop();
          }
          let totalCount = this.search.pageNo * this.search.length;
          if(!this.showCount){
            this.pageTotal =d.body.content.count == (this.search.length+1)? totalCount+1:totalCount
          }
          self.dataSource = dataList||[];
        }else {
          this.$message.error(d.body.msg);
        }
        resolve && resolve();
      }, err => {
        this.$message.error(err);
        resolve && resolve();
      })
    },
    searchData(obj, resolve){
      this.where = obj
      this.getDataList(resolve);
    },

  },
  created() {
    this.getDataList()
  },
  mounted() {
    this.questionData = this.params.questionList
    this.question_sub_id = this.params.questionList[0]?.question_sub_id
  }
}
</script>
<style lang="stylus" scoped>
  .custom-list{
    /deep/ .body{
      height: calc(100vh - 280px) !important;
    }
  }
  .cursor-pointer{
    cursor: pointer;
    color: #1876c9;
  }
</style>
