<!--
* @description: 赠品  其实是copy 补件的改改的
* @date: 2025/3/9
-->
<template>
  <div class="xpt_pmm">
    <el-form
      label-position="right"
      label-width="120px"
      :model="info"
      :rules="rules"
      ref="info"
    >
      <el-row :gutter="40">
        <el-col :span="6">
          <el-form-item label="方案编号">
            <!-- <el-input size='mini' disabled v-model="questionData[0]._after_plan_no" placeholder="系统自动生成"></el-input> -->
            <el-input
              size="mini"
              disabled
              v-model="after_plan_no"
              placeholder="系统自动生成"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="方案状态">
            <el-input  size='mini' :value="{
							SAVE: '创建',
							SUBMIT: '提交',
							RECALL: '撤回',
							RETRACT: '撤回',
						}[info.status]" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40" style="height: 50px">
        <el-col :span="24">
          <el-form-item label="备注信息" prop="remark">
            <el-input
              type="textarea"
              v-model="info.remark"
              :maxlength="255"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
              style="width: 98%"
            ></el-input>
            <el-tooltip
              v-if="rules.remark[0].isShow"
              class="item"
              effect="dark"
              :content="rules.remark[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="商品信息" style="margin: 10px 0">
            <el-button
              type="primary"
              size="mini"
              @click="addGoods"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
            >增加商品</el-button
            >
            <el-button
              type="danger"
              size="mini"
              @click="onDelete"
              :disabled="
                info.status === 'SUBMIT' || !otherInfo.canEditSolutions
              "
            >删除</el-button
            >
            <el-button type="primary" size="mini" @click="updateStore(true)"
            >刷新可用库存</el-button>
            <el-button type="primary" size="mini" @click='uploadFun' :disabled="!canSave">上传图片<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload></el-button>
            <el-button type="success" size="mini" @click='pictureFun'>查看或者下载附件</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      :data="giftGoodsSaveVOS"
      border
      @selection-change="select"
      ref="goods"
      class="xpt_pmm_scroll"
      @row-click="rowClick"
      :row-class-name="tableRowClassNameRepair"
      style="width: 100%;padding-left: 100px"
    >
      <el-table-column
        type="selection"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column label="序号" type="index" width="60">
        <template slot-scope="scope"
        ><div class="table-index">
          {{ scope.$index + 1  }}
        </div></template
        >
      </el-table-column>
      <el-table-column
        label="物料编码"
        prop="material_code"
        show-overflow-tooltip
        width="140"
      ></el-table-column>
      <el-table-column
        label="物料名称"
        prop="material_name"
        show-overflow-tooltip
        width="150"
      ></el-table-column>
      <el-table-column
        label="规格描述"
        prop="specification_description"
        show-overflow-tooltip
        width="200"
      ></el-table-column>
      <el-table-column
        label="单位"
        prop="unit"
        show-overflow-tooltip
        width="100"
      ></el-table-column>

      <el-table-column label="赠品数量" show-overflow-tooltip width="100">
        <template slot-scope="scope">
          <el-input
            size="mini"
            style="width: 80%"
            v-model="scope.row.gift_quantity"
            type="number"
            min="0"
            disabled
            @change.native="
              (e) =>
                _checkValIsRight(
                  e,
                  scope.row.gift_quantity > 0 && !/\./.test(scope.row.gift_quantity)
                )
            "
          ></el-input>

        </template>
      </el-table-column>

      <el-table-column
        label="供应来源"
        prop="supply_source"
        show-overflow-tooltip
        width="150"
      >
        <template slot-scope="scope">
          <span >{{ {
            SUPPLIER:"供应商",
            INNER:"内部"
          }[scope.row.supply_source] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="冗余库存数"
        prop="inventorySurplus"
        show-overflow-tooltip
        width="150"
      >
        <template slot-scope="scope">{{
            scope.row.inventorySurplus
          }}</template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-form
        label-position="right"
        label-width="120px"
        :model="info"
        :rules="rules"
        ref="addressInfo"
      >
        <el-form-item label="确认信息">
          <el-button
            type="primary"
            size="mini"
            style="margin-top: 5px; margin-left: 5px"
            @click="choiceAddress"
            :disabled="!otherInfo.canEditSolutions || info.status === 'SUBMIT'"
          >修改客户地址</el-button
          >
        </el-form-item>
        <div style="overflow: hidden; width: 100%">
          <el-form-item
            label="取货地址"
            prop="province"
            style="float: left; width: auto"
          >
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="info.province"
              @change="changeProvice"
              :disabled="!canEditAddress"
            >
              <el-option
                v-for="(value, key) in province"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label=""
            prop="city"
            style="float: left; width: auto; margin-left: -110px"
          >
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="info.city"
              @change="changeCity"
              :disabled="!canEditAddress"
            >
              <el-option
                v-for="(value, key) in city"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label=""
            prop="area"
            style="float: left; width: auto; margin-left: -110px"
          >
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="info.area"
              @change="changeArea"
              :disabled="!canEditAddress"
            >
              <el-option
                v-for="(value, key) in area"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label=""
            style="float: left; width: auto; margin-left: -110px"
          >
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="info.street"
              :disabled="!canEditAddress"
            >
              <el-option
                v-for="(value, key) in street"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="详细信息" prop="receiver_addr">
          <el-input
            size="mini"
            v-model="info.receiver_addr"
            style="width: 80%"
            :disabled="!canEditAddress"
          ></el-input>
          <el-tooltip
            v-if="rules.receiver_addr[0].isShow"
            class="item"
            effect="dark"
            :content="rules.receiver_addr[0].message"
            placement="right-start"
            popper-class="xpt-form__error"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>

        <el-form-item label="收货人电话" prop="reveiver_phone">
          <el-input
            size="mini"
            v-model="info.reveiver_phone"
            :disabled="!canEditAddress"
          ></el-input>
          <el-tooltip
            v-if="rules.reveiver_phone[0].isShow"
            class="item"
            effect="dark"
            :content="rules.reveiver_phone[0].message"
            placement="right-start"
            popper-class="xpt-form__error"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="收货人姓名" prop="receiver_name">
          <el-input
            size="mini"
            v-model="info.receiver_name"
            icon="linshi-eye-open"
            :on-icon-click="decryption"
            :disabled="!canEditAddress"
          ></el-input>
          <!-- <xpt-eye-switch v-model="info.receiver_name" hideType="name" ref="receiver_name" :readonly='true' :disabled="!canEditAddress" :hideBorder="true" :isCallback="true" :aboutNumber="otherInfo.merge_trade_no" @onOpenHideData="decryption"></xpt-eye-switch> -->
          <el-tooltip
            v-if="rules.receiver_name[0].isShow"
            class="item"
            effect="dark"
            :content="rules.receiver_name[0].message"
            placement="right-start"
            popper-class="xpt-form__error"
          >
            <i class="el-icon-warning"></i>
          </el-tooltip>
        </el-form-item>
      </el-form>
      <div style="margin: 10px 0; text-align: center">
        <el-button
          type="primary"
          size="mini"
          @click="save"
          :disabled="
            !otherInfo.canEditSolutions
              ? true
              : !canSave || info.status === 'SUBMIT'
          "
        >保存</el-button
        >
        <el-button
          type="primary"
          size="mini"
          @click="preSubmit"
          :loading="submitLoading"
          :disabled="
            !otherInfo.canEditSolutions
              ? true
              : !canSubmit ||
                !info.id ||
                !info.status ||
                info.status === 'SUBMIT'
          "
        >提交</el-button
        >
<!--        <el-button-->
<!--          type="primary"-->
<!--          size="mini"-->
<!--          @click="withdraw"-->
<!--          :disabled="-->
<!--            !otherInfo.canEditSolutions-->
<!--              ? true-->
<!--              : !canWithdraw || info.status !== 'SUBMIT'-->
<!--          "-->
<!--        >撤回</el-button-->
<!--        >-->
        <el-button
          type="primary"
          size="mini"
          :disabled="!otherInfo.canEditSolutions"
          @click="addNew"
        >新增</el-button
        >
      </div>
    </el-row>
  </div>
</template>
<script>
import addressInfo from "./model/addressInfo";
import VL from "@common/validate.js";
import { cloneDeep } from 'loadsh'
export default {
  mixins: [addressInfo],
  props: {
    selectedData: {
      type: Array,
      default() {
        return [];
      },
    },
    otherInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    editOtherInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    questionList: Object,
    copySelectData: Object,
    editQuestion: Object,
    operatParams: Object,
    afterGroup: Array,
    allQuestions: Array,
    isReSaleOrder: Boolean,
  },
  data() {
    // this.questionData = this.selectedData//子问题详情，比this.init(null, 'init')先初始化
    // if(!this.questionData[0]) this.questionData[0] = {}
    return {
      questionData: [],
      listDeleteGoodsId:[],
      goodsMaterialIds: [],
      goodsMapQuestionList: [], //找到商品明细对应哪个子问题
      afterPlanGroupId: "",
      oldGoods: [],
      ...this.init(null, "init"),
      after_plan_no: null,
      selectedGoods: [],
      ifClickUpload: false,
      uploadData: {},
      validStock: {}, //material_id和库存对应关系
      after_plan_group_id: "", //方案组id
      // planStatus: {},
      // nowPlanStatus: 'SAVE',//当前选中子问题的方案状态
      giftGoodsSaveVOS:[],
      //补件成本，客户支付金额
      totalCost: 0,
      totalCustomer: 0,
      rules: [
        "transport_type",
        "after_plan_date",
        "delivery_type",
        "remark",
      ].reduce(
        (obj, key) => {
          obj[key] = VL.isNotBlank({
            required: true,
            self: this,
          });
          return obj;
        },
        {
          customer_fee: VL.maxThreeDecimals({
            self: this,
            trigger: "change",
          }),
        }
      ),

      //地址是否已经验证通过了
      addressIsPass: true,

      //拿到当前的表头信息,
      orginalTableData: null,
      canSave: true,
      canSubmit: true,
      canWithdraw: true,
      submitLoading:false,
    };
  },
  methods: {
          /**
       *查看或下载附件
       **/
       pictureFun() {
        var params = {
          parent_no : this.otherInfo.after_order_no,//售后单号
          child_no:null,
          ext_data : null,
          parent_name : 'AFTER_ORDER',
          child_name : '',
          parent_name_txt: '售后单',
        };
        params.callback = d=>{
        }
        this.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
          style:'width:1000px;height:600px',
          title:'下载列表'
        });
      },
      //上传图片
      uploadFun() {
        this.ifClickUpload = true;
        this.uploadData = {
          parent_no : this.otherInfo.after_order_no,//售后单号
          child_no:null,
          ext_data : null,
          parent_name : 'AFTER_ORDER',
          child_name : '',
          parent_name_txt: '售后单',
        }
        //重置，防止第二次不能触发
        setTimeout(() => {
          this.ifClickUpload = false;
        },100)

      },
    ifLinsyOrder(row) {
      let isLinsy = false;
      let order = this.selectedData.find(
        (item) => item.material_code == row.question_goods_code
      );
      let ls_suppler = __AUX.get("ls_suppler");
      if (!order) {
        isLinsy = true;
        return true;
      }
      ls_suppler.forEach((item) => {
        // console.log(order,item);
        if (
          !!order.suppiler_name &&
          item.status == 1 &&
          order.suppiler_name.indexOf(item.name) != -1
        ) {
          isLinsy = true;
        }
      });
      if (!order.suppiler_name) {
        isLinsy = true;
      }
      return isLinsy;
      // return (order.suppiler_name.indexOf('林氏家居') != -1||order.suppiler_name.indexOf('林氏木业') != -1)
    },
    autoMakeApplyDesc(row) {
      return (
        // 下面这个判断只对新增或已经按照模板生成的进行自动生成，对以前的数据不进行自动生成
        !row.supply_request ||
        /^(补发:.+，)?(商品规格:.+，)?(物料名称:.+，)?(物料规格:.+，)?数量:\d+(\.\d+)?$/.test(
          row.supply_request
        )
          ? (row.supply_request =
            (row.question_goods_name
              ? "补发:" + row.question_goods_name + " ，"
              : "") +
            (row.question_goods_desc
              ? "商品规格:" + row.question_goods_desc + " ，"
              : "") +
            (row.material_name
              ? "物料名称:" + row.material_name + " ，"
              : "") +
            (row.description ? "物料规格:" + row.description + " ，" : "") +
            ("数量:" + (Math.abs(row.number) || 0)))
          : row.supply_request
      );
    },
    /**
     *跳到工作台
     ***/
    addNew() {
      let params = JSON.parse(JSON.stringify(this.otherInfo));
      /*after_plan_id:null,//编辑,方案ID
		          after_question_id:null,//方案所挂的问题ID
		          after_plan_group_id:null,//方案组ID*/
      if (params.hasOwnProperty("after_plan_id")) {
        delete params.after_plan_id;
      }
      if (params.hasOwnProperty("after_question_id")) {
        delete params.after_question_id;
      }
      if (params.hasOwnProperty("after_plan_group_id")) {
        delete params.after_plan_group_id;
      }
      this.$root.eventHandle.$emit("creatTab", {
        name: "售后方案",
        url: "after_solutions/solutions",
        params: {
          afterOrderInfo: params,
        },
        component: () => import("@components/after_solutions/solutions"),
      });
    },
    //对页面需要认证的内容认证，required：是否必填，msg：提示信息
    VLFun(required, msg) {
      return VL.isNotBlank({
        required: required,
        self: this,
        msg: msg,
      });
    },
    /**
     *设置问题对应的方案
     ***/
    tableRowClassNameRepair(row, rowIndex) {
      // console.log('asdkfjlksdf');
      let id =
        this.copySelectData.question_sub_id ||
        this.copySelectData.originalSubQuestionId;
      if (!this.copySelectData || !id) return "";

      let question_sub_id = row.question_sub_id;

      // console.log('this.copySelectData',this.copySelectData);
      // console.log('this.question_sub_id',question_sub_id);

      if (question_sub_id == id) return "pmm_blue22";
      return "";
    },
    /**
     *单击某行执行的数据
     **/
    rowClick(row, event, column) {
      console.log("column", column);
      this.currentRowselectGoods = row;
      this.concatSelectReturnAndExchangeGoods();
    },
    /**
     *合并退货，换货商品
     **/
    concatReturnAndExchangeGoods() {
      let goods = this.goods;
      this.$emit("allGoodsmatchQuestions", goods);
    },

    /**
     *合并已选中的明细行
     ***/
    concatSelectReturnAndExchangeGoods() {
      this.$emit("selectGoodsmatchQuestions", [this.currentRowselectGoods]);
    },

    /**
     *保存校验
     */
    validateIsPass() {
      let bool,
        // ,	whichQuestionNoGoods
        goodsSubIds = this.goods.map((obj) => obj.question_sub_id);

      this.$refs.info.validate((valid) => {
        bool = valid;
      });
      bool &&
      this.$refs.addressInfo.validate((valid) => {
        if (!valid) this.modify();
        bool = valid;
      });

      bool &&
      [].some.call(
        this.$refs.goods.$el.querySelectorAll(".table-check"),
        ($dom) => {
          if ($dom.style.display === "inline-block") {
            bool = false;
            return true;
          }
        }
      );
      if (!this.giftGoodsSaveVOS||this.giftGoodsSaveVOS.length===0) {
        this.$message.error("请添加至少一个商品明细");
        bool = false;
      }
      return bool;
    },

    /**
     *撤回
     */
    withdraw() {
      let _planId;
      console.log("asdlkfjlksdf");
      /*this.allQuestions.some(obj => {
					if(obj.question_sub_id === this.copySelectData.question_sub_id){
						_planId = obj._after_plan_id
						return true
					}
				})*/
      if (!this.orginalTableData) return;
      _planId = this.orginalTableData.id;
      console.log(
        "this.allQuestions123",
        this.allQuestions,
        this.copySelectData,
        _planId
      );
      // return
      this.canWithdraw = false;
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/ticketSupply/withdrawPlan",
        { after_plan_id: _planId },
        (res) => {
          if (res.body.result) {
            this.getPlanAndRender();
          } else {
            this.$message.error(res.body.msg);
          }
          this.canWithdraw = true;
        },
        () => {
          this.canWithdraw = true;
        }
      );
    },
    //获取方案组id
    getAfterPlanGroupId() {
      return new Promise((resolve,reject) => {
        this.ajax.postStream(
          "/afterSale-web/api/aftersale/plan/getPlanGroupId",
          {},
          (d) => {
            if (d.body.result) {
              this.afterPlanGroupId = d.body.content;
              resolve();
            }else{
              this.$message.error(d.body.msg)
              reject()
            }
          },
          (e)=>{
            this.$message.error(e)
            reject()
          }
        );
      });
    },
    filterIsDeleteHasIdGoods() {
      console.log("this.questionData", this.questionData);
      let questionDataSubIds = this.questionData.map(
          (obj) => obj.question_sub_id
        ),
        newGoodsIds = this.goods.map((obj) => {
          let s = questionDataSubIds.indexOf(obj.question_sub_id);
          let n = obj.id;
          console.log("s", s);
          console.log(
            "questionDataSubIds.indexOf(obj.question_goods_id) != -1",
            questionDataSubIds.indexOf(obj.question_sub_id) != -1
          );
          console.log("n", n);
          return (
            questionDataSubIds.indexOf(obj.question_sub_id) != -1 && obj.id
          );
        }),
        returnObj = {};

      this.oldGoods.forEach((obj) => {
        if (obj.id && newGoodsIds.indexOf(obj.id) === -1) {
          if (!returnObj[obj.after_plan_no]) returnObj[obj.after_plan_no] = [];
          returnObj[obj.after_plan_no].push(obj.id);
        }
      });

      return returnObj;
    },
    pickByObj(obj,key){
      let mapObj = {};
       (key||[]).forEach(item=>{
        mapObj[item] = obj?.[item] || ''
      })
      return mapObj
    },
    // 构建新增的参数
    getInfoVal(questionSubVO){
      const giftSaveVo = this.pickByObj(this.info, [
        'after_plan_group_id',
        'after_order_id',
        'after_order_no',
        'merge_trade_id',
        'merge_trade_no',
        'receiver_name',
        'reveiver_phone',
        'receiver_addr',
        'province',
        'city',
        'area',
        'street',
        'remark'
      ])
      if(this.oldGoods&&this.oldGoods.length>0){
        for(let item of this.oldGoods){
          if(item.question_sub_id === questionSubVO.question_sub_id){
            giftSaveVo.id = item.id
            break;
          }
        }
      }

      return giftSaveVo
    },


    saveLoop(allQuestionsItems,needTip){
      return new  Promise((resolve,reject) => {
        // 获取问题子表数据
        let  questionSubVO = this.questionData.find(item=>allQuestionsItems.parent_question_id === item.parent_question_id)
        if(!questionSubVO){
          resolve()
          return
        }
        const giftSaveVo = this.getInfoVal(questionSubVO)

        // giftSaveVo.id =

        giftSaveVo.encryption_add_id = this.encryption_add_id
        // 创建时候info 没有问题id 所以统一一下取afterPlanGroupId
        giftSaveVo.after_plan_group_id = this.afterPlanGroupId
        console.log(this.afterPlanGroupId,'this.afterPlanGroupId')
        const listDeleteGoodsId = this.listDeleteGoodsId?.map(e=>e.id)

        const giftGoodsSaveVOS = []
        this.giftGoodsSaveVOS.forEach(e=>{
          if(e.question_sub_id===questionSubVO.question_sub_id){
            const obj = this.pickByObj(questionSubVO,['merge_material_id','batch_trade_id','batch_trade_no'])
            obj.scm_merge_materiel_id=obj.merge_material_id
            Object.assign(obj,e)
            const val = this.pickByObj(obj,['id','material_code','material_name','specification_description','unit','gift_quantity','supply_source','supplier_code','supplier_name','scm_merge_materiel_id','batch_trade_id','batch_trade_no'])
            val.plan_gift_id = obj.plan_gift_id
            giftGoodsSaveVOS.push(val)
          }

        })
        questionSubVO = questionSubVO || {}
        questionSubVO = this.pickByObj(questionSubVO,['after_order_id','question_sub_id','parent_question_id','question_description','source_id','if_goods_question'])
        questionSubVO.id = questionSubVO.question_sub_id
        delete questionSubVO.question_sub_id
        giftSaveVo.question_sub_id = questionSubVO.id
        this.ajax.postStream("/afterSale-web/api/gift/plan/saveAndUpdate",{
          giftSaveVo,
          giftGoodsSaveVOS,
          listDeleteGoodsId,
          questionSubVO
        },(res)=>{
          if(res.body.result){
            if(needTip){
              this.$message.success(res.body.msg);
            }

            resolve()
          }else{
            console.log("res",res)
            this.$message.error(res.body.msg);
            reject()
          }

        },()=>{
          this.canSave = true
          reject()
        })
      })
    },
    // 更新下问题单
    updateProblem(){
      const questionIdMap = {}
      this.giftGoodsSaveVOS.forEach(e=>{
        if(e.question_sub_id&&!questionIdMap[e.question_sub_id]){
          questionIdMap[e.question_sub_id]=e.question_sub_id
        }
      })
      this.listDeleteGoodsId.forEach(e=>{
        if(e.question_sub_id&&!questionIdMap[e.question_sub_id]){
          questionIdMap[e.question_sub_id]=e.question_sub_id
        }
      })
      const questionIdList = []
      this.allQuestions.forEach((e,index)=>{
        if(questionIdMap[e.question_sub_id]){
          questionIdList.push(index)
        }
      })
      this.goodsMapQuestionList= questionIdList
      this.filterQuestionData()
    },
    /**
     *保存
     */
    save(needFresh=true) {

      let self = this;
      this.canSave = false
      new Promise((resolve, reject) => {
        self.getDecryption(resolve, reject);
      })
        .then((res) => {
          setTimeout( () => {
            this.saveData(true)
          });
        })
        .catch((res) => {
          self.$message.error("该地址无法解密， 请手工增加新地址或者选择地址");
          this.canSave = true;
        });
    },
    saveData(needFresh){
      return new Promise((resolve,reject)=>{
          if (!this.addressIsPass){
            this.canSave = true
            return reject()
          }
          if (!this.validateIsPass()) {
            this.canSave = true
            return reject()
          }
          const cb = async () => {
            try {
              this.canSave = false
              for (let item of this.allQuestions) {
                await this.saveLoop(item,needFresh)
              }
              if(needFresh){
                this.getPlanAndRender(() => {
                  this.canSave = true;
                });
              }
            }catch (e){
              console.log(e)
              this.canSave = true;
            }
          }
          // 重新计算一下问题单有什么
          this.updateProblem()

          if(!this.afterPlanGroupId){
            this.getAfterPlanGroupId().then(res=>{
              cb().then(()=>{
                resolve()
              })
            }).catch(()=>{
              this.canSave = true
            })
          }else{
            cb().then(()=>{
              resolve()
            })
          }
      })
    },



    preSubmit() {
      let self = this;
      new Promise((resolve, reject) => {
        self.getDecryption(resolve, reject);
      })
        .then((res) => {
          this.saveData().then(()=>{
            this.submit();
          })
        })
        .catch((res) => {
          self.$message.error("该地址无法解密， 请手工增加新地址或者选择地址");
        });
    },
    /**
     *提交
     */
    submit(e) {
      let self = this;
      let data = {
        after_order_id: self.info.after_order_id,
        after_plan_group_id: self.afterPlanGroupId,
      };
      if (!self.info.after_order_id) {
        self.$message.error("参数获取失败");
        return;
      }
      this.canSubmit = false;
      this.canSave = false;
      this.submitLoading = true
      self.ajax.postStream(
        "/afterSale-web/api/gift/plan/commit",
        data,
        (response) => {
          if (response.body.result) {
            this.submitLoading = true
            self.$message({ message: "操作成功", type: "success" });
            self.getPlanAndRender(() => {
              this.canSubmit = true;
              this.canSave = true;
            });
          } else {
            self.$message.error(response.body.msg);
            this.canSubmit = true;
            this.canSave = true;

          }
          this.submitLoading = false
        },
        (e) => {
          this.canSubmit = true;
          this.submitLoading = false
          this.canSave = true;
          self.$message.error(e);
        }
      );
    },

    /**
     *key转换
     *key值为地址组件里面的KEY值，value为接口拿到的数据
     **/
    switchKey() {
      return {
        address_id: "source_address_id",
        receiver_name: "receiver_name",
        reveiver_phone: "reveiver_phone",
        receiver_addr: "receiver_addr",
        province: "province",
        city: "city",
        area: "area",
        street: "street",
      };
    },
    /**
     *获取修改地址详情
     **/
    getModifyAddress(data) {
      let isPass = data.isPass;
      this.addressIsPass = isPass;
      if (!isPass) return;
      let addressInfo = data.data;
      let keyData = this.switchKey();

      for (let key in keyData) {
        let value = keyData[key];
        this.info[value] = addressInfo[key];
      }
    },

    /**
     *计算补件成本和客户金额
     */
    calc() {
      let _totalCustomer = 0,
        _totalCost = 0;

      this.goods.map((good) => {
        _totalCustomer += parseFloat(good.customer_fee || 0);
        _totalCost += parseFloat(good.supply_code || 0);
      });
      this.totalCustomer = _totalCustomer.toFixed(2);
      this.totalCost = _totalCost;
    },

    // 刷新可用库存
    updateStore(neeTip = false) {

      return new Promise((resolve,reject) =>{
        if (!this.selectedGoods.length) {
          this.$message.error("请选择至少一个商品明细");
          return;
        }
        let params = [];
        this.selectedGoods.forEach((a, b) => {
          let array = {
            material_code: a.material_code,
            supply_code:a.supplier_code,
            source_of_supply:a.supply_source,
          };
          params.push(array);
        });
        this.ajax.postStream(
          "/afterSale-web/api/gift/config/getQuantity",
          params,
          (res) => {
            let data = res.body.content;

            if (res.body.result) {
              if(neeTip){
                this.$message.success(res.body.msg);
              }

              const dataList = JSON.parse(JSON.stringify(this.giftGoodsSaveVOS))
              data?.forEach(e=>{
                dataList.forEach(item =>{
                  if(item.material_code===e.material_code){
                    item.quantity = e.quantity
                    item.inventorySurplus = e.inventorySurplus
                  }
                })
              })
              this.giftGoodsSaveVOS = dataList
              resolve()
            } else {
              this.$message.error(res.body.msg);
              reject()
            }
          }
        );
      })
    },
    /**
     *添加bom产品
     */
    addGoods() {
      const  alertId = new Date().getTime();
      this.$root.eventHandle.$emit("alert", {
        params: {
          questionList: this.selectedData,
          alertId,
          callback: (d) => {
            if(!d||d.length===0){
              return
            }
            const list = []
            d.forEach((item)=>{
              list.push({
                ...item,
                supplier_code:item.supply_code,
                supplier_name:item.supply_name,
                source_of_supply:undefined,
                supply_name:undefined,
                supply_code:undefined,
                id:null,
                gift_quantity:1,
                supply_source:item.source_of_supply
              })
            })
            this.giftGoodsSaveVOS.push(...list);
            // this.goodsMapQuestionList
            // this.filterQuestionData()
          },
        },
        component: () => import("@components/after_solutions/giveaway-modal.vue"),
        style: "width:80%;height:80%",
        title: "添加商品",
      });
    },
    /**
     *关闭标签页面
     */
    closeParentTab(oldData, isSwitchOrCloseTab) {
      if (oldData) {
        if (/Object/.test(Object.prototype.toString.call(oldData))) {
          // 获取地址信息并记录作为对比数据
          oldData.isPass = true;
          this.getModifyAddress(oldData);
          oldData = [this.info, this.giftGoodsSaveVOS];
        }
        this.closeParentTab.oldData = JSON.parse(JSON.stringify(oldData));
        return;
      }
      // this.otherData.getAddress = !this.otherData.getAddress;//先获取地址组件信息

      setTimeout(() => {
        this.closeParentTab.oldData[0].source_address_id =
          this.info.source_address_id; //去掉source_address_id差异

        let tabName = this.operatParams.params.tabName,
          isDiff = this.compareData(
            JSON.parse(JSON.stringify([this.info, this.giftGoodsSaveVOS])),
            this.closeParentTab.oldData
          ),
          _removeTab = () =>
            isSwitchOrCloseTab === "switchTab"
              ? this.$emit("changeSolution")
              : this.$root.eventHandle.$emit("removeTab", tabName);

        if (isDiff) {
          this.$root.eventHandle.$emit("openDialog", {
            ok: (e) => {
              this.save(e);
            },
            no: _removeTab,
          });
        } else {
          _removeTab();
        }
      });
    },
    //TODO,关闭保存的提醒
    switchTab() {
      this.closeParentTab(null, "switchTab");
      // this.$emit("changeSolution");
    },

    /**
     *根据明细去判断哪些问题建了方案
     ***/
    filterQuestionData() {
      let newQuestionData = [];
      this.goodsMapQuestionList.forEach((questionIndex) => {
        if (!newQuestionData[questionIndex]) {
          newQuestionData[questionIndex] = this.allQuestions[questionIndex];
        }
      });
      this.questionData = newQuestionData.filter(Boolean);
    },
    /**
     *删除方案明细
     */
    onDelete() {
      for(let item of this.selectedGoods){
        if(item.id){
          this.listDeleteGoodsId.push(item)
        }
        const index = this.giftGoodsSaveVOS.findIndex(val=>val.material_code === item.material_code && val.question_sub_id===item.question_sub_id)
        this.giftGoodsSaveVOS.splice(index,1)
      }
    },
    /**
     *设置已挂方案的问题列表
     ***/
    setQuestionListBySoutions(listQuestionSubVO, planGiftList) {
      if (!listQuestionSubVO || !listQuestionSubVO.length) return;
      let questions = [];
      listQuestionSubVO.map((q, b) => {
        //let q = a.aftersaleOrderQuestionSubVO;
        let obj = {
          source_id: q.source_id,
          question_description: q.question_description,
          parent_question_id: q.parent_question_id,
          question_sub_id: q.id,
          //after_plan_id:q.after_plan_no//补件先用方案编号去做关联
        };
        /*if(q.id == this.info.question_sub_id){
						obj.isCurrentSolution  = true;
					}*/
        questions.push(obj);
      });

      questions.map((a, b) => {
        let question_sub_id = a.question_sub_id;
        planGiftList.map((n, f) => {
          let id = n.question_sub_id;
          if (question_sub_id == id) {
            a.after_plan_id = n.after_plan_no;
            if (n.after_plan_no == this.after_plan_no) {
              a.isCurrentSolution = true;
            }
          }
        });
      });
      this.$emit("reSetQuestionListFn", {
        questions: questions,
        canEditQuestion: this.canSave || this.info.status !== "SUBMIT",
      });
    },
    /**
     *根据子问题ID去找问题信息
     ***/
    getPlanAndRender(cb) {
      if (!this.afterPlanGroupId) return;
      this.ajax.postStream(
        "/afterSale-web/api/gift/plan/getPlanDetail",
        this.afterPlanGroupId,
        (res) => {
          if (res.body.result) {
            let planNoMapSubId = {};
            // this.$message.success(res.body.msg);
            res = res.body.content;
            res.listQuestionSubVO?.forEach((obj) => {
              this.allQuestions.some((obj2) => {
                if (obj2.parent_question_id === obj.parent_question_id) {
                  obj2.question_sub_id = obj.id;
                  obj2.source_id = obj.source_id;
                  return true;
                }
              });
            });

            res.planGiftList?.forEach((obj) => {
              // this.planStatus[obj.question_sub_id] = obj.status
              planNoMapSubId[obj.after_plan_no] = obj.question_sub_id;
              this.allQuestions.some((obj2) => {
                if (obj2.question_sub_id === obj.question_sub_id) {
                  obj2._after_plan_no = obj.after_plan_no;
                  obj2._after_plan_id = obj.id;
                  return true;
                }
              });
            });
            this.goodsMapQuestionList = [];
            res.giftGoodsSaveVOS?.forEach((obj) => {
              this.allQuestions.some((obj2, index) => {
                if (obj2._after_plan_no === obj.after_plan_no) {
                  this.goodsMapQuestionList.push(index);
                  // obj.question_sub_id = obj2.question_sub_id;//由于接口question_sub_id改变，正常时的代码
                  obj.question_goods_id = obj.question_sub_id =
                    planNoMapSubId[obj.after_plan_no]; //由于接口question_sub_id改变，临时覆盖处理
                  obj.parentQuestionId = obj2.parent_question_id;
                  return true;
                }
              });
            });
            this.filterQuestionData();

            this.oldGoods = [].concat(res.planGiftList);
            this.init({
              supplyVo: res.planGiftList?.[0],
              giftGoodsSaveVOS: res.giftGoodsSaveVOS,
            });
            this.encryption_add_id = res.planGiftList?.[0]?.encryption_add_id;
            this.orginalTableData = JSON.parse(
              JSON.stringify(res.planGiftList?.[0]||[])
            );
            this.after_plan_no = res.planGiftList?.[0]?.after_plan_no;
            this.setQuestionListBySoutions(
              res.listQuestionSubVO,
              res.planGiftList
            );
            // this.after_plan_group_id = res.planGiftList[0].after_plan_group_id;
            this.afterPlanGroupId = res.planGiftList?.[0]?.after_plan_group_id;
            console.log(this.afterPlanGroupId,'afterPlanGroupId')
            this.calc();
            cb && cb();
          } else {
            this.orginalTableData = null;
            this.$message.error(res.body.msg);
          }
        }
      );
    },
    init(data, isInit) {
      let returnData = {
        // questionSubVO: data ? data.questionSubVO : {},
        info: data
          ? data.supplyVo
          : {
            // after_plan_no:null,
            after_plan_date: new Date(),
            // if_goods_question: this.otherInfo.type == 1 ? 'Y' : 'N',
            customer_fee: null,
            delivery_type: "NOW_PAY",
            transport_type: null,
            if_door: "NONEED",
            status: "SAVE",
            remark: null,

            id: null,
            after_order_id: this.otherInfo.after_order_id,
            after_order_no: this.otherInfo.after_order_no,
            merge_trade_id: this.otherInfo.merge_trade_id,
            merge_trade_no: this.otherInfo.merge_trade_no,
            if_modify_logistics: "Y", //是否允许修改货运方式
            if_repurchase_order: "N", //是否加购订单
            source_address_id: null,
            receiver_name: null,
            reveiver_phone: null,
            receiver_addr: null,
            province: null,
            city: null,
            area: null,
            street: null,
            logistics_supplier_class: "",
          },
        goods: data ? data.giftGoodsSaveVOS : [],
      };

      this.closeParentTab([returnData.info, returnData.goods]); //init 关闭页签时对比数据

      if (isInit) return returnData;
      else {
        // this.questionSubVO = returnData.questionSubVO
        this.giftGoodsSaveVOS = returnData.goods;
        this.info = returnData.info;
        console.log(this.info,'555this.info')
        this.selectedGoods = returnData.goods;
        this.updateStore().then(()=>{
          const list = this.giftGoodsSaveVOS?cloneDeep(this.giftGoodsSaveVOS):[]
          this.closeParentTab([returnData.info, list]);
          this.selectedGoods = []
        })

        // returnData.otherData && (this.otherData = returnData.otherData)
      }
    },
    /*
     *选择退换货商品
     **/
    select(selections) {
      this.selectedGoods = selections.length ? selections : [];
    },
    // table检测值是否大于0
    _checkValIsRight(e, bool) {
      console.log(e,bool,e.target.parentElement.nextElementSibling)
      e.target.parentElement.nextElementSibling.style.display = bool
        ? "none"
        : "inline-block";
    },
  },
  watch: {
    //切换的提醒
    "operatParams.isSwitchTab": function (newVal, oldVal) {
      this.switchTab();
    },
    // 关闭页签时判断
    "operatParams.bool": function (val) {
      this.closeParentTab();
    },
  },
  mounted() {
    console.log(this.selectedData,'4555',this.otherInfo,this.otherInfo.after_plan_group_id)
    if (this.selectedData[0]) {
      if (!this.otherInfo.after_plan_group_id) {
        this.questionData = this.selectedData;
      }
      this.getAddressInfoByMergeMaterialId(true);
    }
    if (this.otherInfo.after_plan_group_id) {
      this.afterPlanGroupId = this.otherInfo.after_plan_group_id;
      this.getPlanAndRender();
    }
    this.info.logistics_supplier_class = !!this.selectedData.length
      ? this.selectedData[0].logistics_supplier_class
      : "";

  },
};
</script>
<style module>
/*:global(.xpt_pmm) :global(.xpt_pmm_scroll) :global(.el-table__body-wrapper){
	max-height: 100px;
}*/
.only-show-total :global(.el-checkbox),
.only-show-total :global(.el-input),
.only-show-total :global(.el-textarea),
.only-show-total :global(.table-index) {
  display: none;
}
.row-height :global(.cell) {
  height: auto !important;
}
.row-height textarea {
  width: 100%;
  overflow-x: hidden;
}
th.th-required :global(.cell:before) {
  content: "*";
  color: red;
}
</style>
