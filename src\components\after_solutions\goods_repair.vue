<!--退换货，补件两方案的添加产品-->
<template>
  <div class='xpt-flex'>
    <el-row class='xpt-top' :gutter='40'>
      <el-col :span='3'>
        <el-button type='info' size='mini' @click="confirmAddGoods">确认</el-button>
      </el-col>

      <el-col :span='21' class='xpt-align__right' >
				<span>
					问题商品:
					<el-select placeholder="请选择" size="mini" style="width:80px;" v-model="question_sub_id" >
						<el-option
              v-for="(obj,key) in questionData"
              :label="obj.popName"
              :value="obj.question_sub_id"
              :key='obj.question_sub_id'>
						</el-option>
		    		</el-select>
				</span>

        <!-- 发运明细 -->
        <span>
					<el-select v-model="search2.limit_field" size='mini' style="width:150px;">
						<!-- <el-option label="批次单号" value="batch_trade_no"></el-option> -->
						<el-option label="商品名称" value="material_name"></el-option>
						<el-option label="商品编码" value="material_number"></el-option>
						<el-option label="规格描述" value="material_specification"></el-option>
					</el-select>
					<el-select v-model="search2.operator" size='mini' style="width:100px;">
						<el-option label="等于" value="="></el-option>
						<el-option label="包含" value="%"></el-option>
					</el-select>
					<el-input placeholder="请输入查询值" size='mini' v-model="search2.field_value"></el-input>
					<el-button type='primary' @click="searchList" size='mini'>查询</el-button>
				</span>



      </el-col>
    </el-row>

    <xpt-list
      ref='list'
      selection='checkbox'
      :showHead = 'false'
      :data='list'
      :colData='colData'
      :pageTotal='count'
      :isNeedClickEvent='true'
      @row-dblclick="rowDblclick"
      searchHolder='请输入搜索条件'
      @selection-change='select'
      @page-size-change='sizeChange'
      @current-page-change='pageChange'>
    </xpt-list>
  </div>


</template>
<script>
  export default {//需要类型和相关的id
    props:['params'],
    data(){
      var self = this;
      return {

        search:{
          all:'0',//全部物料
          //universal:'0',//通用
          //bomGroup:'BJ',//部件
          condition:null,
          symbol:null,
          bomVersion:null,//bom版本信息，用于无源单
          string:''//描述


        },
        yesOrNo:{
          'Y':'是',
          'N':'否'
        },
        tongyongShow:false,
        //发运明细的查询
        search2:{
          field_value:null,
          limit_field:null,
          operator:null

        },
        //组合条件查询
        searchGroup:{
          materialCode:'物料编码',
          materialName:'物料名称',
          bomGroup:'BOM分组',
          isUniversal:'是否通用'
        },
        condition:{
          "%":'包含',
          "=":'等于'
        },
        //分页数据
        page:{
          page_size:self.pageSize,
          page_no:1
        },
        isBomOrAllGoods:true,//是否要显示全部物料以及拆分物料的头部信息
        questionData:[],//
        question_sub_id:null,
        //newSearch:null,//
        isDisabledAll : false,
        isDisabledUniversal : false,
        isDisabledBomGroup : false,
        isNeedBomVerison:false,//是否是需要bom版本进行查询

        count:0,
        selectedData:'',
        boolObj:{
          1:'是',
          0:'否'
        },

        //bom版本信息
        bomVersion:{},

        list:[],//
        colData:[
          {
            label: '商品编码',
            prop: 'material_number'
          }, {
            label: '商品名称',
            prop: 'material_name'
          }, {
            label: '规格描述',
            prop: 'material_specification'
          }, {
            label: '批次单号',
            prop: 'batch_trade_no'
          }, {
            label: '淘宝单号',
            prop: 'tid'
          },{
            label: '店铺',
            prop: 'shop_name'
          }, {
            label: '是否停产',
            prop: 'is_stop',
            prop:'yesOrNo'
          }, {
            label: '销售订单号',
            prop: 'sys_trade_no'
          }, {
            label: '物料属性',
            prop: ''
          }, {
            label: '商品组别',
            prop: 'inventory_category'
          }, {
            label: '基本单位',
            prop: 'material_unit'
          }]

      }
    },
    methods:{
      /**
       *双击事件
       **/
      rowDblclick(data){
        console.log(data);
        if(!this.selectedData){
          this.selectedData = [];
        }
        this.selectedData.push(data);
        this.selectedData = Array.from(new Set(this.selectedData));
        this.confirmAddGoods();
      },
      conditionChange(){
        var d = this.search.condition;
        this.condition = '';
        this.condition = d == 'bomGroup' || d == 'isUniversal'?{"=":'等于'}:{"%":'包含',"=":'等于'}
        this.tongyongShow = d == 'isUniversal'?true:false;
        d == 'isUniversal'?this.search.string = null:'';
      },





      /**
       *关闭当前弹出框组件
       */
      closeCurrentComponent(){
        this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
      },
      /**
       *确认回调函数
       **/
      confirmAddGoods(){
        var selectedData = this.selectedData;
        if(!selectedData || !selectedData.length){
          this.$message.error('请选择数据');
          return;
        }
        if(!this.question_sub_id){
          this.$message.error('请选问题商品');
          return;
        }
        //多个接口拿数据，所以要对调整的数据再次进行整理

        this.closeCurrentComponent();
        this.params.callback && this.params.callback({data:selectedData,questionId:this.question_sub_id});
      },


      sizeChange(size){
        // 每页加载数据
        this.page.page_size = size;
        this.page.page_no = 1;
        this.getList();
      },
      pageChange(page_no){
        // 页数改变
        this.page.page_no = page_no;
        this.getList();
      },


      select(selections){
        this.selectedData = selections.length?selections:'';
      },
      /**
       * 请空已选的数据
       * **/
      clearSelectedData(){
        this.selectedData = '';
        this.$refs.list && this.$refs.list.clearSelection();
      },

      searchList(){
        //模糊搜索
        this.page.page_no = 1;
        this.getList();

      },







      /***
       *根据列表不同信息调用不同的接口
       *发运明细
       ***/

        getList(){
          var data = this.params
          if(!data) return;
          let mergeTradeId = data.merge_trade_id;
          let url = mergeTradeId?'/afterSale-web/api/aftersale/order/choose/pickfromorder':'/afterSale-web/api/aftersale/order/choose/pickfrommaterial';
          let search = this.search2;
          let params = {
            field_value: search.field_value,
            limit_field: search.limit_field,
            operator: search.operator,
            merge_trade_id: mergeTradeId?mergeTradeId:'',
            page_size: this.page.page_size,
            page_no: this.page.page_no
          }
          this.ajax.postStream(url, params, res => {
            let d = res.body;
            if(!d.result){
              this.$message.error(d.msg);
              return;
            }
            this.list = d.content.list || [];
            this.count = d.content.count;
          })


      },






    },
    mounted(){
      this.questionData=this.params.questionList;
    }
  }
</script>
<style type="text/css" scoped>
  .el-table__body-wrapper{margin-bottom:20px;}
</style>
