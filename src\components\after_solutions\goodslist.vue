<!-- 销售订单里面的成品 -->
<template>
<div class="xpt-flex">
	<el-row	class='xpt-top'	:gutter='40'>
		<el-button type='warning' size='mini' @click='submit' :disabled="!multipleSelection.length">确认</el-button>
		<el-col class='xpt-align__right' :span='20' style="float:right;">
			<el-select v-model="search.limit_field" size='mini' style="width:150px;">
				<el-option label="批次单号" value="batch_trade_no"></el-option>
				<el-option label="商品名称" value="material_name"></el-option>
				<el-option label="商品编码" value="material_number"></el-option>
				<el-option label="规格描述" value="material_specification"></el-option>
			</el-select>
			<el-select v-model="search.operator" size='mini' style="width:100px;">
				<el-option label="等于" value="="></el-option>
				<el-option label="包含" value="%"></el-option>
			</el-select>
			<el-input placeholder="请输入查询值" size='mini' v-model="search.field_value"></el-input>
			<el-button @click="searchFunc" size='mini'>查询</el-button>
		</el-col>
	</el-row>
	<el-row class="xpt-flex__bottom mgb20" >
		<el-table
			:data="list"
			border
			tooltip-effect="dark"
			width='100%'
			style="width: 100%"
			highlight-current-row 
			:isNeedClickEvent='true'
			@selection-change="handleCurrentChange"
			@row-dblclick="row => submit(String(list.indexOf(row)))"
		>
			<el-table-column type="selection" width="55" align='center'></el-table-column>
		    <el-table-column prop="material_number" label="商品编码"></el-table-column>
		    <el-table-column prop="material_name" label="商品名称"></el-table-column>
		    <el-table-column prop="material_specification" label="规格描述"></el-table-column>
		    <el-table-column prop="batch_trade_no" label="批次单号"></el-table-column>
		    <el-table-column prop="tid" label="淘宝单号"></el-table-column>
		    <el-table-column prop="shop_name" label="店铺"></el-table-column>
		    <el-table-column prop="is_stop" label="是否停产">
		    	<template slot-scope="scope">
		    		<span>{{ ['否', '是'][scope.row.is_stop] }}</span>
		    	</template>
		    </el-table-column>
		    <el-table-column prop="sys_trade_no" label="销售订单号"></el-table-column>
		    <el-table-column prop="" label="物料属性"></el-table-column>
		    <el-table-column prop="inventory_category" label="商品组别"></el-table-column>
		    <el-table-column prop="material_unit" label="基本单位"></el-table-column>		    
	  	</el-table>
	</el-row>
	<el-row class='xpt-pagation'>
	  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
		  	:current-page="search.page_no" :page-sizes="[10, 20, 50, 100]" :page-size="search.page_size"
		  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
		</el-pagination>
	</el-row>
</div>
</template>

<script>
export default {
	props:["params"],
	data(){
		var _this = this;
		return {
			search:{
				field_value: "",//筛选条件：具体搜索值
			    limit_field: "", //筛选条件：合并订单、买家昵称、
			    operator: "=",//筛选条件：= > <
			    merge_trade_id: '',//针对合并订单号的问题商品
				page_size: 20,     //页数
				page_no: 1   //页码
			},
			list: [],
			pageTotal: 0,
			multipleSelection: [],
		}
	},
	methods:{
		handleCurrentChange(val){
			this.multipleSelection = val
		},
		pageChange (page){
			this.search.page_no = page
			this.searchFunc()
		},
		pageSizeChange (pageSize){
			this.search.page_size = pageSize
			this.searchFunc()
		},

		searchFunc(){
	        if(this.params.id){
	        	// 有源
				this.ajax.postStream('/afterSale-web/api/aftersale/order/choose/pickfromorder', this.search, response => {
					if(response.body.result){
						this.list = response.body.content.list
						this.pageTotal = response.body.content.count
					}else{
						this.list = []
						this.pageTotal = 0 
					}
				})
	        }else {
	        	// 无源
	        	this.ajax.postStream('/afterSale-web/api/aftersale/order/choose/pickfrommaterial', {
	        		page_no: this.search.page_no,
	        		page_size: this.search.page_size
	        	}, res => {
					this.list = res.body.content.list
					this.pageTotal = res.body.content.count
	        	})
	        }
		},
		// 确认按钮事件
		submit (dblclickIndex){
			this.params.callback(dblclickIndex && !this.multipleSelection.length ? [this.list[dblclickIndex]] : this.multipleSelection)
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
	},
	mounted: function(){
		this.search.merge_trade_id = this.params.id
		this.searchFunc()
  	},
}
</script>
