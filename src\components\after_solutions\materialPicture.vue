<template>
  <div class="picture-wrapper" v-loading="loading">
    <div v-if="params.material_name" class="material-title">
      {{ params.material_name }}
    </div>
    <div class="title">商品安装图片</div>
    <div>
      <div v-if="installationImg.length" class="image-list">
        <img
          v-for="(item, index) in installationImg"
          :key="index"
          :src="item.path"
          class="image-item"
          @click="onShowImageList('PA', item)"
        />
      </div>
      <div v-else class="empty-text">暂无内容</div>
    </div>
    <div class="title">商品产品图片</div>
    <div>
      <div v-if="proImg.length" class="image-list">
        <img
          v-for="(item, index) in proImg"
          :key="index"
          :src="item.path"
          class="image-item"
          @click="onShowImageList('CP', item)"
        />
      </div>
      <div v-else class="empty-text">暂无内容</div>
    </div>
    <div class="title">商品材质图片</div>
    <div>
      <div v-if="materialImg.length" class="image-list">
        <img
          v-for="(item, index) in materialImg"
          :key="index"
          :src="item.path"
          class="image-item"
          @click="onShowImageList('CZ', item)"
        />
      </div>
      <div v-else class="empty-text">暂无内容</div>
    </div>
    <div class="title">商品尺寸图片</div>
    <div>
      <div v-if="sizeImg.length" class="image-list">
        <img
          v-for="(item, index) in sizeImg"
          :key="index"
          :src="item.path"
          class="image-item"
          @click="onShowImageList('CC', item)"
        />
      </div>
      <div v-else class="empty-text">暂无内容</div>
    </div>
    <div class="title">图纸</div>
    <div>
      <div v-if="fileList.length">
        <ul
          v-for="(fileItem, index) in fileList"
          :key="index"
          class="file-item"
        >
          <li>
            <a :href="fileItem.path" target="_blank">
              {{ fileItem.fileName }}
            </a>
          </li>
        </ul>
      </div>
      <div v-else class="empty-text">暂无内容</div>
    </div>

    <xpt-image
      :images="previewImgList"
      :show="ifShowImage"
      :ifUpload="false"
      :ifClose="false"
      @close="closeShowImage"
    >
    </xpt-image>
  </div>
</template>

<script>
import Fn from "@common/Fn.js";

export default {
  name: "materialPicture",
  props: ["params"],
  data() {
    return {
      imgList: [],
      previewImgList: [],
      ifShowImage: false,
      loading: false,
    };
  },
  computed: {
    // 商品安装图片
    installationImg() {
      return this.imgList.filter((item) => item.fileType === "PA");
    },
    // 商品产品图片
    proImg() {
      return this.imgList.filter((item) => item.fileType === "CP");
    },
    // 商品材质图片
    materialImg() {
      return this.imgList.filter((item) => item.fileType === "CZ");
    },
    // 商品尺寸图片
    sizeImg() {
      return this.imgList.filter((item) => item.fileType === "CC");
    },
    // 附件
    fileList() {
      return this.imgList.filter((item) => item.fileType === "QT");
    },
  },
  methods: {
    initData() {
      if (!this.params.bom_material_num) {
        return;
      }
      this.loading = true;
      this.ajax.postStream1(
        "/afterSale-web/api/aftersale/plan/getBomMaterialAttach",
        this.params.bom_material_num,
        (res) => {
          if (res.body.result) {
            this.loading = false;
            this.imgList = res.body.content.list;
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },

    closeShowImage() {
      this.ifShowImage = false;
    },
    onShowImageList(type, row) {
      let previewImgList = [];
      let list = [];

      switch (type) {
        case "PA":
          list = JSON.parse(JSON.stringify(this.installationImg));
          break;
        case "CP":
          list = JSON.parse(JSON.stringify(this.proImg));
          break;
        case "CZ":
          list = JSON.parse(JSON.stringify(this.materialImg));
          break;
        case "CC":
          list = JSON.parse(JSON.stringify(this.sizeImg));
          break;
        default:
          break;
      }

      list.forEach((value) => {
        let obj = Object.assign({}, value);
        obj.src = value.path;
        obj.date = Fn.dateFormat(value.createDate, "yyyy-MM-dd hh:mm:ss");
        obj.creatName = value.creatorName || "--";
        obj.name = value.fileName || "--";
        obj.isPucture = true;
        //确定要预览那张图片
        if (value.path === row.path) {
          obj.active = true;
        } else {
          obj.active = false;
        }
        previewImgList.push(obj);
      });
      this.previewImgList = previewImgList;
      this.ifShowImage = true;
    },
  },
  created() {
    this.initData();
  },
};
</script>

<style scoped>
.picture-wrapper {
  overflow: auto;
  height: 100%;
}
.material-title {
  font-size: 20px !important;
  margin: 16px 0;
  font-weight: bold;
}
.title {
  margin: 16px 0;
  font-size: 16px !important;
  font-weight: bold;
}
.image-list {
  display: flex;
  gap: 8px;
}
.image-item {
  width: 100px;
  height: 100px;
  cursor: pointer;
}
.empty-text {
  color: #999;
}
.file-item {
  margin-bottom: 12px;
}
</style>
