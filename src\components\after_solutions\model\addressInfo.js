//方案--地址信息
import validate from '@common/validate.js';
export default {
	data() {
		let self = this;
		return {
			encryption_add_id:'',
			text:'修改',
			canEditAddress:false,//修改地址
			/*addressInfo:{

			},*/
			rules:{
				reveiver_phone:validate.isNotBlank({
					trigger:'change',
					required:true,
					self:self
				}),
				receiver_name:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				receiver_addr:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				province:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				city:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				area:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				street:validate.isNotBlank({
					trigger:'change',
					self:self
				}),
				commitment_time:validate.isNotBlank({
		            self: self,
		            msg: '承诺上门时间不能为空'
		        })
			},
			province:{},
			city:{},
			area:{},
			street:{}
		}
	},

	methods:{
		/**
		*选择省份，改变对应的城市
		*/
		changeProvice(){
			let code = this.info.province;
			if(!code) return;
			this.getAddress(code,(data)=>{
				this.city = '';
				this.city = data || {};
				let city = this.info.city;
				if(!data ||  !data.hasOwnProperty(city)){
					this.info.city = null;
					this.info.area = null;
					this.info.street = null;
				}
			});
		},
		/**
		*选择城市，改变对应的区域
		*/
		changeCity(){
			let code = this.info.city;
			if(!code) return;
			this.getAddress(code,(data)=>{
				this.area = '';
				this.area = data || {};
				let area = this.info.area;
				if(!data || this.isEmptyObject(data)){
					//没有第三级
					this.info.area = null;
					this.info.street = null;
					/*if(this.reCalcFee){
						this.getModifyAddressFee();
					}*/
					//this.getModifyAddressFee();

				}else{
					//有第三级
					if(!data.hasOwnProperty(area)){//有，但是不是这个查询里面
						this.info.area = null;
						this.info.street = null;
					}
				}

			});
		},
		/**
		*选择区域，改变对应的街道
		*/
		changeArea(){
			console.log('改变区域');
			let code = this.info.area;
			if(!code) return;
			//this.getModifyAddressFee();
			/*if(this.reCalcFee){
				this.getModifyAddressFee();
			}*/
			this.reCalcFee && this.reCalcFee();
			this.getAddress(code,(data)=>{
				this.street = '';
				this.street = data || {};
				let street = this.info.street;
				if(!data || this.isEmptyObject(data)){
					this.info.street = null;

				}else{
					if(!data.hasOwnProperty(street)){//第四级地址已存在
						this.info.street = null;
					}
				}
			});
		},
		ifDecryption2(callback,row,){
			// console.log(row);

			let data ={
				"addressId": this.encryption_add_id,
			  }
			this.ajax.postStream("/kingdee-web/api/end/ifDecryption", data	, res => {
				var obj = res.body;
				// console.log(obj)
				if(obj.result){

					callback && callback(obj);

				}else{
					// params.callback(false,obj.msg);
				}
			});
		},
		getDecryption(resolve,reject){
			// console.log(row);
			let self = this;
			let data ={
				"addressId": self.encryption_add_id?self.encryption_add_id:self.info.encryption_add_id?self.info.encryption_add_id:self.info.address_id,
			  }
			this.ajax.postStream("/kingdee-web/api/end/ifDecryption", data	, res => {
					var obj = res.body;
					if(!!obj.result&&!!obj.content){
						let data ={
							"addressId":  self.encryption_add_id?self.encryption_add_id:self.info.encryption_add_id?self.info.encryption_add_id:self.info.address_id,
							"mergeTradeNo": this.otherInfo.merge_trade_no
						  }
						this.ajax.postStream("/kingdee-web/api/end/decryption", data	, res => {
							var obj = res.body;
							if(obj.result){
								// this.info.receiver_addr = obj.content.receiverAddress;
								// this.info.reveiver_phone = obj.content.receiverMobile;
								// this.info.receiver_name = obj.content.receiverName;
								resolve&&resolve()

							}else{
								reject&&reject()
							}
						});
					}else{
						resolve&&resolve()

					}
				});
			},
		decryption(params){
      const callBack = ()=>{
        new Promise((resolve,reject)=>{
          this.ifDecryption2(resolve);
        }).then(result=>{
          if(!result.content){
            return;
          }
          let data ={
            "addressId": this.encryption_add_id,
            "mergeTradeNo": this.otherInfo.merge_trade_no
          }
          this.ajax.postStream("/kingdee-web/api/end/decryption", data	, res => {
            var obj = res.body;
            if(obj.result){
              this.info.receiver_addr = obj.content.receiverAddress;
              this.info.reveiver_phone = obj.content.receiverMobile;
              this.info.receiver_name = obj.content.receiverName;
            }else{
              this.$message.error(obj.msg||'解密失败，请稍候重试')
            }
          });
        })
      }
      const reg = /^[1][3,4,5,7,8][0-9]{9}-\d{4}$/;
      const list = ['JD_ZY','PDD','DY','WX','VIP','JD']
      const flag = list.includes(this.info_from)
      if(this.info_from==='TAOBAO'){
        callBack()
      }else if(flag){
        if(!this.oaid){
          return;
        }
        callBack()
      }
		},
		/**
		*选择地址
		**/
		choiceAddress(){
			//TODO;
			let buyerId = this.copySelectData.buyer || this.copySelectData.originalBuyer;
			if(!buyerId){
				this.$message.error('请选择问题');
				return;
			}
			let params = {
				isAlert:true,
				type:'radio',
				cust_id:buyerId,
        merge_trade_no:this.otherInfo.merge_trade_no
			};
			params.callback = (d)=>{
				var data = d.data[0];
				this.fillAddressOfPop(data);
				//运费
				this.reCalcFee && this.reCalcFee();
				//添加addressId
				this.isNeedAddressId && this.isNeedAddressId(data);
				this.encryption_add_id = data.fid;

			}
			console.log('看看传过去的参',params);
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_invoices/addresslist'),
				style:'width:80%;height:560px',
				title:'选择客户地址'
			});
		},
		/**
		*选择地址弹出框填充信息
		**/
		fillAddressOfPop(data){
			this.info.province = data.receiver_state;
			this.info.city = data.receiver_city;
			this.info.area = data.receiver_district;
			this.info.street = data.receiver_street;

			this.info.receiver_addr = data.receiver_address;
			this.info.receiver_name = data.receiver_name;
			this.info.reveiver_phone = data.receiver_mobile;
			//少了一个address_id

		},


		/**
		*修改
		*/
		modify(){
			this.canEditAddress = !this.canEditAddress;
			this.text = this.canEditAddress?'确认':'修改';
		},
		// 添加地址
		addAddress(){
			let buyerId = this.copySelectData.buyer || this.copySelectData.originalBuyer;

			var self = this;
			var params = {};
			params.ifCopy = false;
			params.cust_id = buyerId
			params.ifCallback = false;
			params.callback = (d)=>{
				var data = d;
				//填充地址

				/*this.fillAddressAndChangeFee(data);*/
				console.log(data)
				this.fillAddressOfPop(data);
				//运费
				this.reCalcFee && this.reCalcFee();
				//添加addressId
				this.isNeedAddressId && this.isNeedAddressId(data);
				this.encryption_add_id = data.fid;
			}
			self.$root.eventHandle.$emit('alert', {
				params:params,
				component: () => import('@components/after_solutions/addAddress.vue'),
				close:function(){},
				style:'width:1100px;height:280px',
				title:'新增地址'
			});
		},
		/**
		*向服务器请求地址以及相关信息
		****/
		requestAddressInfo(params,callback){
			if(!params) return;
			this.ajax.postStream('/afterSale-web/api/aftersale/plan/getAddress',params,res=>{
				let data = res.body;
				if(!data.result){
					this.$message.error(data.msg);
					return;
				}
				callback && callback(data);
			});
		},
		/**
		*获取修改之后的地址ID,并且拿到三包运费
		**/
		/*getModifyAddressFee(){
			console.log('看看地址信息');
			if(!this.info.city || !this.info.province) return false;
			let params = {
				province : this.info.province,
				city : this.info.city
			}

			params.if_address = 'Y';
			params.if_need_three = 'Y';
			if(!this.questionData || !this.questionData[0]) return;
			params.question_goods_id = this.questionData[0].merge_material_id;
			this.info.area?params.area = this.info.area:'';
			this.getAddressAndFee(params,(data)=>{
				this.reCalcFee && this.reCalcFee(data);
			});
		},*/
		/**
		*新增的时候，初使化地址
		*根据发运明细去拿地址
		**/
		getAddressInfoByMergeMaterialId(ifEditPlan){
			console.log('看有没有拿到地址信息');
			//var id = this.questionData[0].merge_material_id;
			var id;
			for(var i = 0;i < this.questionData.length;i++){
				let question = this.questionData[i];
				if(question.if_goods_question == 'Y'){
					id = question.merge_material_id;
					break;
				}
			}
			if(!id) return;
			var params = {
				question_goods_id : id,
				if_need_three : 'Y'
			}
			let info = this.info;
			// 初始化不请求接口拿到原始地址
			if(!ifEditPlan) return;
			this.requestAddressInfo(params,(data)=>{
				let d = data.content;
				d.reveiver_phone = d.receiver_phone;
				info.source_address_id = d.address_id;
        this.oaid = d.oaid || '123'
        this.info_from = d.info_from
				this.fillAddressAndChangeFee(d);

				//储存原始地址信息
				this.setOriginalInfo && this.setOriginalInfo();
			})
		},
		/**
		*填充地址信息
		***/
		fillAddressAndChangeFee(addressInfo){
			console.log('11111',addressInfo)
			if(!addressInfo) return;
			var d = this.addressKey();
			var info = this.info;
			for(var key in d){
					console.log('11111',key)
				info[key] = addressInfo[key];
			}
			this.encryption_add_id = addressInfo.address_id;
			//TODO,运费的计算，到各自的模块里面去定义函数
			this.reCalcFee && this.reCalcFee(addressInfo);
			//this.reCalcFee(data);
		},
		/**
		*请求回来的地址KEY
		**/
		addressKey(){
			return {
				receiver_name:null,
				reveiver_phone:null,
				receiver_addr:null,
				province:null,
				city:null,
				area:null,
				street:null,
				address_id:null,
				street_name:null,
				city_name:null,
				province_name:null,
				area_name:null,
			}
		},
		/**
		*获取初使化地址和提货点费用
		*/
		getAddressAndFee(params,callback){
			console.log('aslkdjfalksdjfaksdfj');
			if(!params) return;
			this.ajax.postStream('/afterSale-web/api/aftersale/plan/getAddress',params,res=>{
				let data = res.body;
				callback && callback(data);
			});
		}
	},
	mounted(){
		//获取省份城市
		this.getAddress((data)=>{
			this.province = '';
			this.province = data;
		});


	}
}
