// 退换货方案--换货商品信息
export default {
	data() {
		let self = this
		return {
			exchangeGoods:[],//换货
			selectedExchangeGoods:[],
			//delExchangeGoods:[],//手动删除
			//selDelExchangeGoods:[],//系统自动删除
			
			originalExchangeGoods:'',//原换货数据
			
		}
	},

	methods:{
		selectExchangeGoods(selection){
			this.selectedExchangeGoods = selection.length?selection:[];
			//this.concatSelectReturnAndExchangeGoods();
		},
		calcPriceDiffEvent(row){
			this.calcPricDiff(row);
		},
		/**
		*计算差价
		*/
		calcPricDiff(data){
			var standard_price = parseFloat(data.standard_price);
			var actual_price = parseFloat(data.actual_price);
			standard_price = isNaN(standard_price)?0:standard_price;
			actual_price = isNaN(actual_price)?0:actual_price;
			//防止浮点数计算出现的BUG,先让转换成所有的浮点数
			standard_price *= 1000;
			actual_price *= 1000;
			var price_diff = (standard_price - actual_price)/1000;
			data.price_diff = price_diff.toFixed(3);
		},
		
		
		/**
		*选择价目
		*/
		selectPriceList(row){
			console.log('aljdfasdfjasdfsfdf')
			var question = this.getQuestionByQuestionSubId(row.question_sub_id) || {};
			let params = {
				material_id : row.material_id,
				shop_id : question.shop_id
			}
			params.callback = (d)=>{
				let data = d.data;
				row.actual_price = data.price;
				row.standard_price = data.price;
				row.price_list_id = data.price_list_id;
				row.price_list = data.price_list_name;
				this.calcPricDiff(row);
			}

			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_solutions/pricelist'),
				style:'width:800px;height:560px',
				title:'选择价目表'
			});
		},
		/**
		*刷新可用库存
		*/
		updateStore(){
			let selectedData = this.selectedExchangeGoods;
			if(!selectedData || !selectedData.length){
				this.$message.error('请选择要查询的换货商品');
				return;
			}
			let id = [];
			selectedData.map((good,a)=>{
				id.push(good.material_id);
			});
			this.ajax.postStream('/afterSale-web/api/aftersale/plan/RefreshUsed',{list_material_id:id},(res)=>{
				var data = res.body;
				this.$message({
					type:data.result?'success':'error',
					message:data.msg
				})
				if(!data.result) return;
				selectedData.map((exGood,a)=>{
					var id = exGood.material_id;
					if(data.content.hasOwnProperty(id)){
						exGood.valid_stock = data.content[id];
					}
				});
				/*var goods = JSON.stringify(this.exchangeGoods);
				this.exchangeGoods = [];
				this.exchangeGoods = JSON.parse(goods);*/
				

			});
		},
		/**
		*获取换货明细行商品字段
		*/
		getExchangeGoodInfo(question){
			var questionSubId = question.question_sub_id;
			var schemeDetail = this.schemeData[questionSubId] || {};	
			var questionSubId = schemeDetail.question_sub_id || question.question_sub_id;
			return{
				actual_price:null,
				question_sub_id:questionSubId,
				standard_price:0,
				after_plan_id:schemeDetail.id || null,
				combo_str:null,
				change_reason:null,
				create_time:null,
				creator:null,
				creator_name:null,
				delivery_fee:null,
				goods_name:null,
				id:null,
				last_modifier:null,
				last_modifier_name:null,
				last_modify_time:null,
				material_code:null,
				number:1,
				price_diff:null,
				price_list:null,
				price_list_id:null,
				if_gift:null,
				
				units:null,
				valid_stock:null,
				material_id:null,
				volume:null,
				parentQuestionId:question.parent_question_id || null//父问题ID

			}
		},
		/**
		*换货商品的删除
		**/
		delExchangeGoodsFn(){
			var selectedGoods = this.selectedExchangeGoods;
			var currentGoods = this.exchangeGoods;

			if(!selectedGoods || !selectedGoods.length){
				this.$message.error('请选择当前面板要删除的明细');
				return;
			}
			//let delKey = 'delExchangeGoods';
			let bool = true;
			var curDelGoods = [];
			selectedGoods.map((selectedG,a)=>{
				if(!selectedG) return;
				//换货
				// if(selectedG.combo_str){
				// 	bool = false;
				// 	this.$message.error(selectedG.material_code + '不能删除该商品');
				// 	return;
				// }
				currentGoods.map((goods,b)=>{
					if(JSON.stringify(selectedG) == JSON.stringify(goods)){
						let id = goods.id;
						if(!id){
							currentGoods.splice(b,1);
							return;
						}
						curDelGoods.push(id);
						return;
					}
				})
			});
			if(!bool || !curDelGoods.length) {
				this.concatReturnAndExchangeGoods();
				return;
			}
			
			this.requestDelGoods(curDelGoods,(res)=>{
				if(!res.result) return;
				selectedGoods.map((selectedG,a)=>{
					if(!selectedG) return;
					currentGoods.map((goods,b)=>{
						if(JSON.stringify(selectedG) == JSON.stringify(goods)){
							currentGoods.splice(b,1);
						}
					})
				});
				//this.delExchangeGoods = Array.from(new Set([...this.delExchangeGoods,...curDelGoods]));
				this.selectedExchangeGoods = [];
				this.concatReturnAndExchangeGoods();
				
			})
			
		},
		
		/**
		*计算单个换货运费
		**/
		/*calcFee(obj){
			var volume = parseFloat(obj.volume);
			volume = isNaN(volume)?0:volume;
			var fee = parseFloat(this.feeObj.fee);
			fee = isNaN(fee)?0:fee;
			obj.delivery_fee = (volume * fee).toFixed(3);
		},*/
		/**
		*重新计算所有的换货明细行运费
		*/
		/*calcExchangeGoods(data){
			//重新计算
			if(!data) return;
			if(this.feeObj.fee == data.fee) {
				this.feeObj = data;
				return;
			}
			this.feeObj = data;
			//换货商品里面相应的运费要重新计算
			this.exchangeGoods.map((obj,a)=>{
				if(!obj) return;
				this.calcFee(obj);
			});
		},*/
		/**
		*填充换货的运费
		**/
		fillExchangeGoodFee(data){
			if(!data || !data.length){
				this.exchangeGoods.map((a,b)=>{
					a.delivery_fee = null;
				})
				return;
			}
			let goods = this.exchangeGoods;
			let i = goods.length;
			let f = data.length;
			for(let aa = 0;aa < f;aa++){
				let good = data[aa];
				let material_id = good.material_id;
				//let volume = good.volume;
				for(let a = 0;a < i;a++){
					let good2 = goods[a];
					let material_id2 = good2.material_id;
					//let volume2 = good2.volume2;
					if(material_id2 != material_id ) continue;
					good2.delivery_fee = good.delivery_fee;
				}
			}
		},

		/*
		*添加商品
		**/
		popExchangeGoods(params){
			if(!params) return;
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_solutions/bomgoods'),
				style:'width:80%;height:560px',
				title:'添加商品'
			});
		},
		/**
		*获取标准价目表
		*根据商品ID，方案创建时间，订单店铺ID
		**/
		setStandardPrice(data){
			if(!data || !data.questionId || !data.data || !data.data.length) return;
			let  questionSubId = data.questionId;
			let question = this.getQuestionByQuestionSubId(questionSubId);
			let goods = data.data;
			let materialId = [];
			let type = data.type;
			goods.map((a,b)=>{
				let id = type==1?a.sourceMaterialId:a.bom_material_id;
				materialId.push(id);
			});
			materialId = Array.from(new Set(materialId));
			if(!materialId.length) return;
			let params = {
				material_ids : materialId,
				shop_id : question.shop_id,
				singleShotDate : this.info.after_plan_date || new Date().getTime()
			}
			this.getSamplePriceByExchangeGoods(params,questionSubId);
		},
		/***
		*根据换货商品自动获取价目表
		***/
		getSamplePriceByExchangeGoods(params,questionSubId){
			if(!params) return;
			let url = '/price-web/api/price/order/getStandShopPrice';
			this.ajax.postStream(url,params,(res)=>{
				if(!res.body.result){
					this.$message.error(res.body.msg);
					return;
				}
				let data = res.body.content;
				if(!data.length) return;
				let exchangeGoods = this.getExchangeByQuestionSubId(questionSubId);
				if(!exchangeGoods) return;
				let exLength = exchangeGoods.length,dLength = data.length,a= 0,b = 0;
				for(;a < exLength ; a++){
					let eGood = exchangeGoods[a].good;
					let index = exchangeGoods[a].index
					for(;b < dLength;b++){
						let dGood = data[b];
						if(eGood.material_id == dGood.k3_material_id){
							eGood.price_list_id = dGood.price_list_id;//价目表ID
							eGood.price_list = dGood.price_list_name;//价目表
							eGood.actual_price = dGood.price;//实际售价
							eGood.standard_price = dGood.price;//标准售价
							//用本身替代自己
							this.exchangeGoods.splice(index,1,eGood);
							break;
						}
					}
				}
				console.log('res',res);
			});

		},
		/**
		*通过子问题ID获取没有保存的换货商品
		**/
		getExchangeByQuestionSubId(questionSubId){
			if(!questionSubId) return '';
			let list = [];
			this.exchangeGoods.map((a,b)=>{
				if(!a.id && a.question_sub_id == questionSubId){
					list.push({good:a,index:b});
				}
			});
			return list;
		},

		/**
		*换货页签添加商品
		**/
		getSearchConditionOfExchangeGoods(){
			let info = Object.assign({},this.info);
			info.isReturnGoods = false;
			let params = {questionList:this.questionData,info:info};
			console.log('添加商品');
			params.callback = (d)=>{
				/*if(d.type == 1){
					//成品
					this.setExchangeGoodsOfGoods(d);
					return;
				}
				if(d.type == 2){
					//拆分
					this.setExchangeGoodsOfBom(d);
					return;
				}*/
				if(d.type == 1){
					//成品
					this.setExchangeGoodsOfGoods(d);
					
				}else if(d.type == 2){
					//拆分
					this.setExchangeGoodsOfBom(d);
				}
				
				this.setStandardPrice(d);

				//this.getSearchConditionOfExchangeGoods();
				this.concatReturnAndExchangeGoods();
			}
			this.popExchangeGoods(params);
		},
		/***
		*换货商品自动匹配价目表
		**/
		matchExchangePrice(){
			let goods = this.exchangeGoods;
			if(!goods || !goods.length) return;

		},
		/**
		*拆分物料
		***/
		setExchangeGoodsOfBom(d){
			var data = d.data;
			if(!data || !data.length) return;
			let questionId = d.questionId;
			let question = this.getQuestionByQuestionSubId(questionId);
			// let errorTip = '';
			data.map((good,a)=>{
				var obj = this.getExchangeGoodInfo(question);
				var code = good.bom_material_num;
				/*var bool = this.judgeHasSameGoodByQuestion(code,question);
				if(bool){
					errorTip += code + ',';
					return;
				}*/
				obj.material_code = code;
				obj.goods_name = good.bom_material_name;
				obj.units = good.bom_version_unit;
				obj.material_id = good.bom_material_id;
				obj.actual_price = good.act_price;
				obj.volume = good.volume;
				obj.if_gift = good.present_flag;
				//this.calcFee(obj);
				this.calcPricDiff(obj);
				this.exchangeGoods.push(obj);
			});
			this.calcFeeByChangeGoods();
			// this.popErrorMsg(errorTip);
		},
		/**
		*全部物料
		**/
		setExchangeGoodsOfGoods(d){
			var data = d.data;
			if(!data || !data.length) return;
			let questionId = d.questionId;
			let question = this.getQuestionByQuestionSubId(questionId);
			// let errorTip = '';
			data.map((good,a)=>{
				var obj = this.getExchangeGoodInfo(question);
				var code = good.materialNumber;
				/*var bool = this.judgeHasSameGoodByQuestion(code,question);
				if(bool){
					errorTip += code + ',';
					return;
				}*/
				obj.material_code = code;
				obj.goods_name = good.materialName;
				obj.units = good.materialUnit;
				obj.material_id = good.sourceMaterialId;
				obj.actual_price = good.act_price;
				obj.volume = good.volume;
				obj.if_gift = good.present_flag;
				//this.calcFee(obj);
				this.calcPricDiff(obj);
				this.exchangeGoods.push(obj);
			});
			this.calcFeeByChangeGoods();
			// this.popErrorMsg(errorTip);
		},
		/*
		*设置换货数据
		**/
		setOriginalExchangeGoods(){
			var goods = this.exchangeGoods || [];
			this.originalExchangeGoods = JSON.stringify(goods);
		},
		/**
		*验证换货信息
		*/
		validateExchangeGoods(){
			var goods = this.exchangeGoods;
			if(!goods || !goods.length) return true;
			var isPass = true;
			goods.map((good,a)=>{
				if(good.price_diff === '' || good.price_diff === null || good.actual_price === '' || good.actual_price === null){
					isPass = false;
					this.$message.error('换货商品，差价,实际售价必填');
					return;
				}
				/*if(!good.price_list_id){
					isPass = false;
					this.$message.error('请选择价目表');
					return;
				}*/
				let number = good.number;
				if(number <= 0 || parseInt(number) != parseFloat(number)){
					isPass = false;
					this.$message.error('换货数量必须为大于0的整数');
					return;
				}
				let act_price = good.actual_price;
				if(typeof act_price == 'undefined' || act_price < 0){
					isPass = false;
					this.$message.error('实际售价不能为空或负数');
					return;
				}
				let reg = /^[0-9]*([.][0-9]{1,3})?$/;
				if(!reg.test(act_price)){
					isPass = false;
					this.$message.error('实际售价最多只能保存三位小数');
					return;
				}
			});
			return isPass;
		},

	}
}