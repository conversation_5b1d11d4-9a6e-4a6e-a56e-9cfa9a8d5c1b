// 退货跟踪单---驳回记录
export default {
	data() {
		let self = this
		return {
			activeName1:'first1',
			rejectLists:[],
			rejectCols:[
				{
					label: '驳回人',
					prop: 'rejector_name'
				},
				/*{
					label: '用户',
					prop: 'creator_name'
				},*/
				{
					label: '驳回日期',
					prop: 'rejector_time',
					format:'dataFormat1'
					/*width:150*/
				},
				{
					label: '驳回备注',
					
					prop: 'description',
					/*bool:true,
					isInput:'true'*/
				}/*,
				{
					label: '业务备注',
					width:300,
					prop: 'biz_note',
					bool:true,
					isInput:'true'
				}*/
			]
		}
	},

	methods:{
		/**
		*驳回记录列表
		****/
		getRejectList(){
		
			let after_plan_group_id = this.info.after_plan_group_id;
			if(!after_plan_group_id /*|| this.info.after_plan_no*/) return;
			var url='/afterSale-web/api/aftersale/bill/returns/rejectRecord/listByPlanGroupId';
			var data = {after_plan_group_id:after_plan_group_id};
			this.ajax.postStream(url,data,(res)=>{
				console.log('resasfdasdfasdfasdf',res);
				let d = res.body;
				if(!d.result) return;
				this.rejectLists = d.content.list || [];
				console.log('this.rejectLists',this.rejectLists)

			})
		}		

	},
	watch:{
		'info.after_plan_group_id':function(newVal,oldVal){
			this.getRejectList();
		}
	},
	mounted(){
		//console.log('kalsdjflksadjflaskdjflskdjflaksdjfksadjfksadjfaskdljfalsdkfjalsdkfjaskdfj');
		this.getRejectList();
		
	}
}