export default {
  data() {
    let self = this
    return {
      old_repairinfo:{},
      old_repariGoodsinfo:[],
      dataIsChange:false
    }
  },
  methods:{
    /**
     *表头信息是否有改变
     *分新增和编辑,默认是有修改
     **/
    infoIsChange(){
      console.log('看看数据对比');
      let info = Object.assign({},this.submitData);
      let original = this.old_repairinfo;
      if(!original) return false;
      let originalInfo = JSON.parse(original);
      let bool = this.compareData(info,originalInfo);
      return bool;
    },
    /**
     *明细行数据的对比
     *退换货商品
     **/
    goodsIsChange(){
      var newGoods = this.returnGoods;
      var originalReturnGoods = this.old_repariGoodsinfo?JSON.parse(this.old_repariGoodsinfo):[];

      var bool = this.compareData(newGoods,originalReturnGoods);
      return bool;
    },
    /**
     *判断整个页面的数据是否有修改
     **/
    judgeIsChange(){
      var infoIsChange = this.infoIsChange();
      if(infoIsChange) return true;
      var goodsIsChange = this.goodsIsChange();
      return goodsIsChange;

    },
  }
}
