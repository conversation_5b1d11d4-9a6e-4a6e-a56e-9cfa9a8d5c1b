// 退换货方案--退货商品信息
export default {
	data() {
		let self = this
		return {
			ifClickUpload: false,
			uploadData:{},
			returnGoods: [],
			//仓库编码以及ID
			allStorageInfo:{},
			//返货类型对应的仓库
			returnTypeMathStorageInfo:{
				'RESALE':'THXNC',//退货再售
				'THREE_BAG':'TH',//三包返货
				'CUSTOMER':'CP',//客户自返
				'DEALER':'',//经销退货
			},
			//delReturnGoods:[],
			//selDelReturnGoods:[],
			selectedReturnGoods:[],//选择的商品数据
			originalReturnGoods:'',
			yesOrNo:{
				Y:'是',
				N:'否'
			}/*,
			returnGoodsCol: [
				{
					label: '问题商品编码',
					prop: 'question_goods_code',
					width:200

				}, {
					label: '物料编码',
					width:200,
					prop: 'material_code'
				}, {
					label: '物料名称',
					width:200,
					prop: 'material_name'
					
				}, {
					label: '规格描述',
					width:150,
					prop: 'material_desc'
				}, {
					label: '数量',
					prop: 'number',
					type:'number',
					bool:true,
					isInput:true
					
				}, {
					label: '退货方式',
					prop: 'returns_type',
					bool:true,
					isSelect:true,
					obj:{
						RETURN:'退货',
           		 		CHANGE:'换货'
					},
					redirectClick:self.changeType

				}, {
					label: '是否退款',
					prop: 'if_refund',
					bool:true,
					isSelect:true,
					obj:{
						Y:'是',
						N:'否'
					}
				}, {
					label: '是否报废',
					prop: 'if_scrap',
					bool:true,
					isSelect:true,
					obj:{
						Y:'是',
						N:'否'
					}
				}, {
					label: '退货仓库',
					prop: 'returns_storage',
					bool:true,
					isInputSearch:true,
					redirectChange:self.selectWarehouse

				}, {
					label: '成品实际售价',
					prop: 'act_price'
				}, {
					label: '物料BOM版本',
					prop: 'bom_version'
				}, {
					label: '存货类别',
					prop: 'inventory_category'
				}, {
					label: '销售订单号',
					prop: 'sale_order_no'
				}, {
					label: '是否停产',
					prop: 'if_stop_produce',
					format:'yesOrNo'
				}
			]*/
		}
	},

	methods:{
		/**
		*计算退货费用
		*取货费用=体积*运费单价*返货倍数
		*1、体积：优先是由已发运明细的商品带出来。如果没有，则取值物料基础资料档中的体积。2、运费单价和返货倍数 提货点列表的运费字段和返货倍数字段
		**/
		/*calcReturnGoodFee(returnGood){
			if(!returnGood) return;
			//var volume = isNaN(returnGood.volume)?0:parseFloat(returnGood.volume);
			var volume = !returnGood.volume?0:parseFloat(returnGood.volume);
			var fee = parseFloat(this.feeObj.fee || 0);//运费单价
			var multiple = parseFloat(this.feeObj.multiple || 0);//返货倍数
			var p = volume * fee * multiple;
			returnGood.returns_delivery_fee = p.toFixed(2);
		},*/
		/**
		*计算取货费用
		*取货费用 = 实际单价*结算费率*0.9
		*1、优先取实际单价*区对应的三包点的不超区费率*0.9，其次取值：实际单价*区对应的三包点的超区费率*0.9
		**/
		/*calcPickGoodFee(returnGood){
			if(!returnGood) return;
			//var act_price = isNaN(returnGood.act_price)?0:parseFloat(returnGood.act_price);
			var act_price = !returnGood.act_price?0:parseFloat(returnGood.act_price);
			var clear_rates = parseFloat(this.feeObj.clear_rates || 0);//结算率
			var p = act_price * clear_rates * 0.9;
			returnGood.fetch_goods_fee = p.toFixed(2);
		},*/
		/**
		*计算总的取货费用和退货费用
		**/
		/*calcTotalReturnGoodFeeAndPickGoodsFee(){
			var returnGoodsFee =0, pickGoodsFee = 0;
			this.returnGoods.map((a,b)=>{
				//TODO
				let rFee = parseFloat(a.returns_delivery_fee || 0);
				let pFee = parseFloat(a.fetch_goods_fee || 0);
				returnGoodsFee += rFee;
				pickGoodsFee += pFee;
			});
			//提货点费用    fee
  
  			//最小费用  min_fee;退货最小
   
  			//结算起步价  clear_star;取货费用
  
  			//结算率    clear_rates;
  
  			//倍数     multiple;、

			returnGoodsFee = returnGoodsFee < this.feeObj.min_fee?this.feeObj.min_fee:returnGoodsFee;
			pickGoodsFee = pickGoodsFee < this.feeObj.clear_star?this.feeObj.clear_star:pickGoodsFee;

			this.info.delivery_fee = returnGoodsFee.toFixed(2);
			this.info.fetch_goods_fee = pickGoodsFee.toFixed(2);

		},*/
		/**
		*计算单个退货商品里面的相关费用
		**/
		/*calcSingleReturnGoodFee(returnGood){
			this.calcReturnGoodFee(returnGood);
			this.calcPickGoodFee(returnGood);
		},*/
		
		/**
		*重新计算明细行所有退货商品的相关费用
		**/
		/*calcReturnGoodsByChangeAddress(){
			this.returnGoods.map((a,b)=>{
				this.calcSingleReturnGoodFee(a);
			});
			this.calcTotalReturnGoodFeeAndPickGoodsFee();
		},*/

		/**
		*填充退货的取货费用和退货费用
		*一对一的关系
		**/
		fillReturnGoodFeeAndPickGoodsFee(data){
			if(!data || !data.length){
				//重置费用数据
				this.returnGoods.map((a,b)=>{
					a.returns_delivery_fee = null;
					a.fetch_goods_fee = null;
				})
				return;
			}
			let goods = this.returnGoods;
			let i = goods.length;
			let f = data.length;
			for(let aa = 0;aa < f;aa++){
				let good = data[aa];
				let material_id = good.material_id;
				let question_sub_id = good.question_sub_id;
				for(let a = 0;a < i;a++){
					let good2 = goods[a];
					let material_id2 = good2.material_id;
					let question_sub_id2 = good2.question_sub_id;
					if(material_id2 != material_id || question_sub_id != question_sub_id2) continue;
					good2.returns_delivery_fee = good.delivery_fee;
					good2.fetch_goods_fee = good.fetch_goods_fee;
				}
			}
			console.log('this.returnGoods',this.returnGoods);
			/*for(let c = 0; c<goods.length;c++){
				goods.splice(c,1,goods[c]);
			}*/
		},
		
		/**
		*退货页签添加成品
		*添加逻辑：首选得找出没有添加到明细的商品问题，然后把没有添加到明细的商品问题添加进去
		*要确保明细里面的问题所带的成品的唯一性
		**/
		addFinishedGoods(){
			var questions = this.getQuestionsByGoods();
			this.addScheme(questions);
			this.concatReturnAndExchangeGoods();

		},
		/**
		*获取没有添加到明细行的商品问题
		**/
		getQuestionsByGoods(){
			if(!this.questionData || !this.questionData.length) return;
			var list = [];
			var goodsQuestion = [];
			this.questionData.map((a,b)=>{
				if(a.if_goods_question == 'Y'){
					goodsQuestion.push(a);
				}
			})
			if(!this.returnGoods || !this.returnGoods.length){
				list = goodsQuestion;
				return list;
			}
			var i = this.returnGoods.length - 1;
			goodsQuestion.map((c,d)=>{
				//var question_sub_id = c.question_sub_id;
				var parent_question_id = c.parent_question_id;
				//var question_goods_material_id = c.material_id;
				var question_goods_code = c.material_code;//问题商品编码
				for(var n = 0;n <= i;n++){
					let rGood = this.returnGoods[n];
					//商品问题已添加明细，这个验证的唯一性
					if(rGood.parentQuestionId == parent_question_id && rGood.material_code == question_goods_code) break;
					if(n == i){
						list.push(c);
						break;
					}
				}
			});
			return list;
		},
		
		/**
		*选择
		**/
		selectReturnGoods(selection){
			this.selectedReturnGoods = selection.length?selection:[];
			//this.concatSelectReturnAndExchangeGoods();
			//this.$emit('matchQuestions',this.selectedReturnGoods)
		},

		/**
		*改变明细退货类型
		*当是成品退换货时，退货页签改变退货方式，则换货页签要相应的改变
		*/
		changeType(row){
			console.log('测试数据')
			if(!this.canAddNewOne || !this.canLinkage) return;
			let type = row.returns_type;
			if(!type) return;
			if(type == 'CHANGE'){
				row.if_refund = 'N';
			}
			let combo_str = row.combo_str;
			if(!row || row.inventory_category != '成品' || !combo_str) return;//当不是原商品退货时，
			console.log('类型');
			this.addOrDelExchangeGoodsByReturnGoods(row);
		},
		chickChangeCanLiage(){
			this.canLinkage = true;
		},
		/**
		*退货商品明细行
		*根据退货商品增删换货商品
		**/
		addOrDelExchangeGoodsByReturnGoods(returnGoods){
			if(!returnGoods) return;
			let type = returnGoods.returns_type;
			let combo_str = returnGoods.combo_str;
			if(!type || returnGoods.inventory_category != '成品' || !combo_str) return;
			//改变退货方式，首先得删除掉相应的换货商品
			let eI = this.exchangeGoods.length;
			for(let a = 0;a < eI;a++){
				let good = this.exchangeGoods[a];
				let combo = good.combo_str;
				if(combo == combo_str){
					this.exchangeGoods.splice(a,1);
					break;
				}
			}
			//退货=>A换B的情况
			if(type == 'RETURN') return ;

			/*if(type == 'RETURN'){
				this.exchangeGoods.map((good,index)=>{
					if(!good) return;
					let combo = good.combo_str;
					if(combo == combo_str){
						this.exchangeGoods.splice(index,1);
						return;
					}
				});
				return;
			}*/

			//换货=>A换A的情况,添加换货商品
			var question_sub_id = returnGoods.question_sub_id;
			
			var question = this.getQuestionByParentId(returnGoods.parentQuestionId);
			
			if(!question) return;
			
			let obj = this.getExchangeGoodInfo(question);
			obj.isChange = true;
			obj.question_sub_id = question_sub_id;
			this.changeExchangeGoodsInfoByReturnGood(returnGoods,obj);
			this.exchangeGoods.push(obj);
			if(!obj.price_list_id){
				//没有价目表的时候自动获取价目表
				let params = {
					material_ids : [obj.material_id],
					shop_id : question.shop_id,
					singleShotDate : this.info.after_plan_date || new Date().getTime()
				}
				this.getSamplePriceByExchangeGoods(params,question_sub_id);
			}
			this.calcFeeByChangeGoods();

		},
		/**
		*发运明细的商品添加到退货商品当中
		*成品退货
		*/
		setRetrunGoodsOfSaleGoods(d){
			if(!d) return;
			let data = d.data;
			if(!data || !data.length) return;
			let questionId = d.questionId;
			let question = this.getQuestionByQuestionSubId(questionId);
			// let errorTip = '';
			data.map((good,a)=>{
				var obj = this.getReturnGoodInfo(question);
				var code = good.material_number;
				// var bool = this.judgeHasSameGoodByQuestion(code,question,1);
				// if(bool){
				// 	errorTip += code + ',';
				// 	return;
				// }

				obj.material_code = code;
				obj.material_name = good.material_name;
				obj.material_id = good.materia_id;
				obj.material_desc = good.material_specification;
				obj.bom_version = good.bom_version;
				
				obj.units = good.material_unit;
				obj.volume = good.volume;
				obj.if_stop_produce = good.is_stop?'Y':'N';
				obj.inventory_category = good.inventory_category;

				obj.act_price = good.act_price || 0;

				//TODO,这边还需要取值
				obj.stand_price = good.stand_price || 0;
				obj.price_list = good.price_list;
				obj.price_list_id = good.price_list_name;
				obj.sale_order_no = good.sys_trade_no;
				obj.sys_trade_id = good.sys_trade_id;

				obj.custom_id = good.customer_id;//客户ID
				obj.custom_name = good.customer_name;//客户名称
				obj.nick_name = good.customer_name;//买家昵称（冗余字段）

				obj.shop_name = good.shop_name;//店铺名称（冗余字段）

				obj.staff = good.user_id;//业务员id（冗余字段）

				obj.staff_name = good.real_name;//业务员姓名（冗余字段）
				obj.staff_group = good.group_name;//业务员分组（冗余字段）

				obj.batch_trade_no = good.batch_trade_no;//批次订单号
				obj.batch_trade_id = good.batch_trade_id;//批次订单id

				obj.logistics_supplier = good.logistics_supplier;//物流运输商ID
				obj.logistics_supplier_name = good.logistics_supplier_name;//物流运输商名称

				obj.merge_material_id = good.merge_material_id;//发运明细ID
				obj.branch_no = good.lot;//商品批号
				obj.supplier_id = good.suppiler_id;//供应商id
				obj.supplier_company = good.supplier_company;//供应商名称
				obj.user_shop_name = good.user_shop_name;
				obj.original_shop_name = good.original_shop_name;
				obj.if_gift = good.present_flag;


				if(obj.inventory_category=='成品'){
					obj.combo_str = new Date().getTime()+a;
				}
				//this.calcSingleReturnGoodFee(obj);
				this.returnGoods.push(obj);
			});
			this.calcFeeByChangeGoods();
			// this.popErrorMsg(errorTip);
			//this.calcTotalReturnGoodFeeAndPickGoodsFee();
		},
		/**
		*把成品添加到退货商品页签
		*全部物料接口
		**/
		setReturnGoodsOfGoods(d){
			var data = d.data;
			if(!data || !data.length) return;
			let questionId = d.questionId;
			let question = this.getQuestionByQuestionSubId(questionId);
			// let errorTip = '';
			data.map((good,a)=>{
				var obj = this.getReturnGoodInfo(question);
				var code = good.materialNumber;
				
				// var bool = this.judgeHasSameGoodByQuestion(code,question,1);
				// if(bool){
				// 	errorTip += code + ',';
				// 	return;
				// }
				obj.material_code = code;
				obj.material_name = good.materialName;
				obj.material_desc = good.materialSpecification;
				obj.material_id = good.sourceMaterialId;
				
				obj.act_price = good.act_price || 0;

				obj.stand_price = 0;
				obj.price_list = null;
				obj.price_list_id = null;

				obj.bom_version = good.bom_version;
				obj.volume = good.volume;
				obj.units = good.materialUnit;
				obj.inventory_category = good.inventoryCategory;
				obj.if_stop_produce = good.isStop?'Y':'N';
				
				obj.returns_storage = this.returnTypeMathStorageInfo[this.info.transport_type].remark || '';
				obj.storage_id = this.returnTypeMathStorageInfo[this.info.transport_type].code || '';

				/*if(this.info.transport_type === 'RESALE'){
					obj.storage_id = this.otherInfoByUrl().THXNC
					obj.returns_storage = '退货虚拟仓'
				}else {
					good.stock_id?obj.storage_id = good.stock_id:'';
					good.stock_id?obj.returns_storage = good.stock_name:'';
				}*/

				obj.user_shop_name = null;
				obj.original_shop_name = null;
				obj.if_gift = good.present_flag;
				
				if(obj.inventory_category=='成品'){
					obj.combo_str = new Date().getTime()+a;
				}
				if (this.info.transport_type === 'RESALE'){
					// 返货类型是退货再售时，仓库默认为退货虚拟仓
					item.returns_storage = '退货虚拟仓'
					item.storage_id = '23678858'
				}
				//this.calcSingleReturnGoodFee(obj);
				this.returnGoods.push(obj);
			});
			this.calcFeeByChangeGoods();
			// this.popErrorMsg(errorTip);
			//this.calcTotalReturnGoodFeeAndPickGoodsFee();
		},

		/**
		*把拆分物料添加到退货商品页签
		**/
		setReturnGoodsOfBom(d){
			var data = d.data;
			if(!data || !data.length) return;
			let questionId = d.questionId;
			let question = this.getQuestionByQuestionSubId(questionId);
			// let errorTip = '';
			data.map((good,a)=>{
				var obj = this.getReturnGoodInfo(question);
				var code = good.bom_material_num;
				// var bool = this.judgeHasSameGoodByQuestion(code,question,1);
				// if(bool){
				// 	errorTip += code + ',';
				// 	return;
				// }
				obj.material_code = code;
				obj.material_name = good.bom_material_name;
				obj.material_desc = good.bom_material_desc;
				obj.material_id = good.bom_material_id;//用bomID

				obj.act_price = good.act_price || 0;
				obj.stand_price = 0;
				obj.price_list = null;
				obj.price_list_id = null;

				obj.bom_version = good.bom_version;
				obj.volume = good.volume;
				obj.units = good.bom_version_unit;
				obj.branch_no = (good.lot_controller == 'Y' || good.lot_controller == 1)?good.lot || 888888:null;

				obj.returns_storage = (this.allStorageInfo[this.returnTypeMathStorageInfo[this.info.transport_type]] || {}).remark || '';
				obj.storage_id = (this.allStorageInfo[this.returnTypeMathStorageInfo[this.info.transport_type]] || {}).code || '';
				/*if(this.info.transport_type === 'RESALE'){
					obj.storage_id = this.otherInfoByUrl().THXNC
					obj.returns_storage = '退货虚拟仓'
				}else {
					good.stock_id?obj.storage_id = good.stock_id:'';
					good.stock_id?obj.returns_storage = good.stock_name:'';
				}*/
				
				obj.inventory_category = good.inventoryCategory;
				obj.if_stop_produce = good.isStop?'Y':'N';
				obj.if_gift = good.present_flag;
				
				//obj.question_goods_material_id = good.sourceMaterialId;
				if(obj.inventory_category=='成品'){
					obj.combo_str = new Date().getTime()+a;
				}
				if (this.info.transport_type === 'RESALE') {
					// 返货类型是退货再售时，仓库默认为空(退货在售XPT-14889)
					obj.returns_storage = '退货虚拟仓'
					obj.storage_id = '23678858'
				}
				//this.calcSingleReturnGoodFee(obj);
				this.returnGoods.push(obj);
			});
			this.calcFeeByChangeGoods();
			// this.popErrorMsg(errorTip);
			//this.calcTotalReturnGoodFeeAndPickGoodsFee();
		},
		/**
		*弹出相同物料的提示----去掉这个判断限制
		**/
		// popErrorMsg(msg){
		// 	if(!msg) return;
		// 	this.$message.error('物料'+msg+'已存在，不需要重复添加');
		// },
		
		/**
		*退货页签添加商品
		*首先得过虑掉没有接口的问题商品
		**/
		getSearchConditionOfReturnGoods(){
			console.log('添加商品');
			if(!this.questionData || !this.questionData.length) return;
			//var isHasSource = this.questionData[0].if_has_source == 'Y'?true:false;
			var list = this.questionData;
			if(!list.length){
				this.$message.error('选所问题不符合添加商品的条件');
				return;
			}
			let info = Object.assign({},this.info);

			info.isReturnGoods = true;
			let params = {questionList:list,info:info};
			params.callback = (d)=>{
				/*if(d.type == 1){
					//成品
					this.setReturnGoodsOfGoods(d);
					return;
				}
				if(d.type == 2){
					//拆分物料
					this.setReturnGoodsOfBom(d);
					return;
				}
				if(d.type == 3){
					//发运明细 
					this.setRetrunGoodsOfSaleGoods(d);
					return;
				}*/
				if(d.type == 1){
					//成品
					this.setReturnGoodsOfGoods(d);
					
				}else if(d.type == 2){
					//拆分物料
					this.setReturnGoodsOfBom(d);
				}else if(d.type == 3){
					//发运明细 
					this.setRetrunGoodsOfSaleGoods(d);
				}
				
				this.concatReturnAndExchangeGoods();
				
			}
			this.popReturnGoods(params);
		},
		/**
		*判断指定对应的问题下面是否有相同的退货物料，
		*{material_code},物料编码，
		*{question}物料所对应的问题
		*{key},退货商品还是换货商品
		**/
		judgeHasSameGoodByQuestion(material_code,question,key){
			if(!material_code || !question) return true;
			var goods = this[key?'returnGoods':'exchangeGoods'];
			if(!goods || !goods.length) return false;
			var question_sub_id = question.question_sub_id;
			var i = goods.length - 1;
			for(var a = 0;a <= i;a++){
				let rGood = goods[a];
				if(rGood.question_sub_id == question_sub_id && rGood.material_code == material_code) return true;
			}
			return false;
		},

		/*
		*添加商品
		**/
		popReturnGoods(params){
			if(!params) return;
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_solutions/bomgoods2'),
				style:'width:80%;height:560px',
				title:'添加商品'
			});
		},
		/**
		*选择退货仓库
		*/
		selectWarehouse(row){
			let params = {
				material_id : row.material_id,
				selectType:1
			}
			params.callback = (d)=>{
				console.log('d',d);
				let data = d.data;
				let id = data.stock_id;
				let name = data.stock_name;
				this.returnGoods.map((a,b)=>{
					a.storage_id = id;
					a.returns_storage = name;
				})
				/*row.storage_id = data.stock_id;
				row.returns_storage = data.stock_name;*/
			}

			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/after_solutions/warehouselist'),
				style:'width:800px;height:560px',
				title:'选择退货仓库'
			});
		},

		

		/**
		*获取退货商品字段
		*默认与带过来的商品一致
		*/
		getReturnGoodInfo(question){
			var sale_order_no = question.sys_trade_no;//

			var questionSubId = question.question_sub_id;//问题商品id，对应问题商品ID
			var parent_question_id = question.parent_question_id;//父问题ID
			var question_description = question.question_description;//问题描述

			//var schemeDetail = this.schemeData[questionSubId] || {};

			var question_goods_code = question.material_code || null;//问题商品编码
			var bom_version = question.bom_version || null;//问题商品BOM版本
			var material_id = question.material_id || null;//问题商品物料ID
			var description = question.material_specification || null;//问题商品规格描述
			var material_name = question.material_name || null;//问题商品名称
			var number = /*question.count || */1;//问题商品数据---默认1
			var units = question.units || null;//问题商品单位

			var act_price = question.act_price || 0;//成本实际售价
			var stand_price = question.stand_price || 0;//标准售价
			var price_list_id = question.price_list_id || null;//价目表ID
			var price_list = question.price_list_name || null;//价目表名称

			var volume = question.volume || 0;//物料BOM体积
			var reason = material_name + description;//退货原因
			var if_stop_produce = question.is_stop === '0'?'N':question.is_stop == '1'?'Y':null;
			var inventory_category = question.inventory_category || null;//

			var sys_trade_id = question.sys_trade_id//销售单ID
			return{
				//after_plan_id:this.info.id,
				//after_plan_id:schemeDetail.id || null,
				after_plan_id:null,
				id:null,
				sale_order_no:sale_order_no,//销售单号
				sys_trade_id:sys_trade_id,//销售单ID

				//问题
				question_description:question_description,//问题描述
				question_goods_id:parent_question_id,//父问题ID
				question_sub_id:question.question_sub_id,//子问题ID


				//问题商品信息
				question_goods_bom_version:bom_version,
				question_goods_code:question_goods_code,
				question_goods_material_id:material_id,
				description:description,//问题商品规格描述,父问题描述
				question_goods_name:material_name,//问题商品名称

				//物料信息，默认和问题商品信息一样
				material_desc:description,//子规格描述
				material_id:material_id,//物料ID
				material_code:question_goods_code,//物料编码
				material_name:material_name,//物料名称（冗余字段）
				bom_version:bom_version,//物料BOM版本

				units:units,//单位
				volume:volume,
				if_stop_produce:if_stop_produce,//是否停产,N/Y
				inventory_category:inventory_category,//存货类别

				act_price:act_price,//成本实际售价
				stand_price:stand_price,//标准售价
				price_list_id:price_list_id,//价目表ID
				price_list:price_list,//价目表名称

				number:number,//数量

				//returns_storage:this.info.transport_type === 'RESALE' ? '退货虚拟仓' : '成品仓库',
				//storage_id:this.otherInfoByUrl()[this.info.transport_type === 'RESALE' ? 'THXNC' : 'CP'],
				returns_storage :this.info.transport_type === 'RESALE' ? '退货虚拟仓' : ((this.allStorageInfo[this.returnTypeMathStorageInfo[this.info.transport_type]] || {}).remark || ''),
				storage_id :this.info.transport_type === 'RESALE' ? '23678858' :  ((this.allStorageInfo[this.returnTypeMathStorageInfo[this.info.transport_type]] || {}).code || ''),

				//returns_storage:this.returnTypeMathStorageInfo[this.info.transport_type].remark || '',
				//storage_id:this.returnTypeMathStorageInfo[this.info.transport_type].code || '',

				returns_type:'RETURN',//默认为退货
				reason:reason?reason:null,//退货描述
				if_refund:"Y",//是否退款
				if_scrap:"N",//是否报废,N/Y
				returns_delivery_fee:null,
				fetch_goods_fee:null,

				if_special_cut:question.if_special_cut,//是否为特裁商品

				

				custom_id:question.buyer,//客户ID
				custom_name:question.buyer_nick,//客户名称
				nick_name:question.buyer_nick,//买家昵称（冗余字段）

				shop_name:question.shop_name,//店铺名称（冗余字段）
				staff:question.user_id,//业务员id（冗余字段）

				staff_name:question.user_name,//业务员姓名（冗余字段）
				staff_group:question.group_name,//业务员分组（冗余字段）

				if_three_age:question.if_three_age,//是否是三个月售后

				
				batch_trade_no:question.batch_trade_no,//批次订单号

				batch_trade_id:question.batch_trade_id,//批次订单id
				logistics_supplier:question.logistics_supplier,//物流运输商ID
				logistics_supplier_name:question.logistics_supplier_name,//物流运输商名称

				merge_material_id:question.merge_material_id,//发运明细ID
				branch_no:question.lot,//商品批号
				supplier_id:question.suppiler_id,//供应商id
				supplier_company:question.suppiler_name,//供应商名称

				user_shop_name:question.user_shop_name,//客服店铺
				original_shop_name:question.original_shop_name,//原始店铺

				if_has_source:question.if_has_source,
				if_goods_question : question.if_goods_question,
				if_gift:question.if_gift || 0,//是否为赠品


				combo_str:null,
				create_time:null,
				creator:null,
				creator_name:null,
				last_modifier:null,
				last_modifier_name:null,
				last_modify_time:null,
				parentQuestionId:question.parent_question_id
			}
		},
		
		/**
		*根据退货页面签里面的问题商品改变相应的换货页签里面的商品
		*returnGood为退货商品
		*exchangeGood为换货商品
		***/
		changeExchangeGoodsInfoByReturnGood(returnGood,exchangeGood){
			exchangeGood.material_code = returnGood.material_code;
			exchangeGood.goods_name = returnGood.material_name;
			exchangeGood.units = returnGood.units;
			exchangeGood.number = returnGood.number;
			exchangeGood.material_id = returnGood.material_id;

			exchangeGood.combo_str = returnGood.combo_str;

			exchangeGood.standard_price = returnGood.stand_price || 0;
			exchangeGood.actual_price = returnGood.act_price || 0;
			
			exchangeGood.price_list_id = returnGood.price_list_id;
			exchangeGood.price_list = returnGood.price_list;

			exchangeGood.volume = returnGood.volume;
			exchangeGood.if_gift = returnGood.if_gift;
			

			//this.calcFee(exchangeGood);
			//TODO,还需要添加东西
			this.calcPricDiff(exchangeGood);

		},
		/**
		*退货商品的删除
		**/
		delReturnGoodsFn(){
			var selectedGoods = this.selectedReturnGoods;
			var goods = this.returnGoods;
			var exchangeGoods = this.exchangeGoods;
			if(!selectedGoods || !selectedGoods.length){
				this.$message.error('请选择当前面板要删除的明细');
				return;
			}
			//var delGoods = this.delReturnGoods;
			var bool = true;
			var curDelGoods = [];
			var type = this.info.returns_type
			selectedGoods.map((selectedG,a)=>{
				if(!selectedG) return;
				//退货类型为成品退换货，存货类别为成品的明细不能删除
				/*if(type == 'FINISH_CHANGE_RETURN' && selectedG.combo_str &&　selectedG.question_goods_code ){
					bool = false;
					this.$message.error(selectedG.material_code + '不能删除该商品');
					return;
				}*/
				goods.map((good,b)=>{
					if(JSON.stringify(selectedG) == JSON.stringify(good)){
						let id = good.id;
						if(id){
							console.log('id',id);
							curDelGoods.push(id);
							return;
						}
						//选过来的是不是成品，然后还要根据删除换货商品
						var combo_str = selectedG.combo_str;

						if(combo_str){
							exchangeGoods.map((exgood,c)=>{
								if(!exgood) return;
								if(combo_str == exgood.combo_str){
									exchangeGoods.splice(c,1);
								}
							});
						}
						goods.splice(b,1);
						return;
					}
				});
			});



			//向服务器请求是否可删除
			if(!bool || !curDelGoods.length){
				this.calcFeeByChangeGoods();
				this.concatReturnAndExchangeGoods();
				//this.calcTotalReturnGoodFeeAndPickGoodsFee();
				return;
			}
			
			//把退货商品里面的删除的成品储存起来，用来删除相应的换货商品
			var copyDelReturnGoods = [];
			this.requestDelGoods(curDelGoods,(res)=>{
				//不可删除的校验
				if(!res.result) return;
				//可以删除
				selectedGoods.map((s,a)=>{
					if(!s) return;
					goods.map((g,b)=>{
						if(JSON.stringify(s) == JSON.stringify(g)){
							copyDelReturnGoods.push(Object.assign({},g));
							goods.splice(b,1);
						}
					})
				});
				//this.delReturnGoods = Array.from(new Set([...this.delReturnGoods,...curDelGoods]));
				this.selectedReturnGoods = [];
				this.calcFeeByChangeGoods();
				//this.calcTotalReturnGoodFeeAndPickGoodsFee();
				
				if(!copyDelReturnGoods.length) {
					this.concatReturnAndExchangeGoods();
					return;
				}

				//删除相应的换货商品
				copyDelReturnGoods.map((c,d)=>{
					if(!c) return;
					//找到退换货商品的唯一关系值
					if(c.combo_str){
						exchangeGoods.map((f,d)=>{
							if(f.combo_str == c.combo_str){
								//this.delExchangeGoods.push(f.id);
								this.exchangeGoods.splice(d,1);
							}
						});
					}
					
				})
				this.concatReturnAndExchangeGoods();
				//console.log('this.delExchangeGoods',this.delExchangeGoods);
				
			});

		},
		
		/*
		*设置退货数据
		**/
		setOriginalReturnGoods(){
			var goods = this.returnGoods;
			this.originalReturnGoods = JSON.stringify(goods);
		},
		/**
		*验证退货信息
		*/
		validateReturnGoods(){
			var goods = this.returnGoods;
			if(!goods || !goods.length) return true;
			var isPass = true;
			goods.map((good,a)=>{
				console.log('good.returns_type',good.returns_type);
				console.log('good.if_scrap',good.if_scrap);
				if(!good.returns_type /*|| !good.if_scrap*/){
					isPass = false;
					this.$message.error('退货商品，退货方式必填');
					return;
				}
				let number = good.number;
				if(number <= 0 || parseInt(number) != parseFloat(number)){
					isPass = false;
					this.$message.error('退货数量必须为大于0的整数');
					return;
				}

				//TODO,先把没有bom物料的卡下来
				// if(good.question_goods_code == good.material_code && !good.bom_version){
				// 	isPass = false;
				// 	this.$message.error('成品，物料bom为空');
				// 	return;
				// }
				

			});
			return isPass;
		},
		//上传图片
		uploadFun() {
			if(!this.selectedReturnGoods || this.selectedReturnGoods.length != 1){
				this.$message.error('只能选择一个明细行操作');
				return;
			}
			//TODO,明细行里面的父问题ID
			this.ifClickUpload = true;
			this.uploadData = {
				parent_name: 'AFTER_ORDER',
				parent_no: this.info.after_order_no,//售后单号
				child_name: 'QUESTION_GOODS',
				child_no: this.selectedReturnGoods[0].parent_question_id,//父问题id,必须是String
				content: this.selectedReturnGoods[0],
				parent_name_txt: '售后单',
				child_name_txt:'问题商品'
			}
			console.log('this.uploadData',this.uploadData);
			//重置，防止第二次不能触发
			setTimeout(() => {
				this.ifClickUpload = false;
			},100)

		},
		/**
		*查看或下载附件
		**/
		pictureFun() {
			console.log('555555');
			var params = {
				parent_no : this.info.after_order_no,//售后单号
				child_no : null,//父问题id,必须是String
				ext_data : null,
				notNeedDelBtn:!this.canAddNewOne,
				parent_name : 'AFTER_ORDER',
				child_name : 'QUESTION_GOODS',
				parent_name_txt: '售后单',
				//child_name_txt: '问题商品',
			};
			params.callback = d=>{
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/common/download.vue'),
				style:'width:1000px;height:600px',
				title:'下载列表'
			});
		},

		/**
		*获取辅助资料里面的仓库信息
		**/
		getAllStorageInfo(){
			let storageInfo = __AUX.get('CK');
			storageInfo.map((a,b)=>{
				this.allStorageInfo[a.name] = a;
			})
		}

	},
	mounted(){
		this.getAllStorageInfo();
	}
}