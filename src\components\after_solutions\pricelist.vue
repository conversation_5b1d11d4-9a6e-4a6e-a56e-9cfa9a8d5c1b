<!--换货价目列表-->
<template>
	<xpt-list 
		selection='radio'
		:btns='btns' 
		:data='list' 
		:colData='colData' 
		:isNeedClickEvent='true'
		@row-dblclick="rowDblclick"
		searchHolder='请输入搜索条件'
		@radio-change='select'  
		
		>
	</xpt-list>

</template>
<script>
	export default {//需要类型和相关的id
	    props:['params'],
		data(){
            var self = this;
			return {
                search:{
                   	material_id:'',
                   	singleShotDate :null,
                   	shop_id : null
                   	//page_no:1,
                   	//page_size:20
					
				},
				 btns: [
                {
                    type: 'primary',
                    txt: '确定',
                    click: function(){
                    	self.confirmAddGoods();
                    }
                }],
				colData:[
				{
					label: '价目表编码',
					prop: 'price_list_code'
				}, {
					label: '价目表名称',
					prop: 'price_list_name'
				}, {
					label: '物料编码',
					prop: 'material_code'
				}, {
					label: '物料名称',
					prop: 'material_name'
				},{
					label: '规格描述',
					prop: 'material_specification'
				},{
					label: '单位',
					prop: 'material_unit'
				}, {
					label: '价格',
					prop: 'price'
				}, {
					label: '生效时间',
					prop: 'enable_date',
					format:'dataFormat'
				}, {
					label: '失效时间',
					prop: 'disable_date',
					format:'dataFormat'
				}, {
					label: '行状态',
					prop: 'disabled_status',
					format:'statusFilter'
				}],
				count:0,
                selectedShopData:'',
                //radioData:'',
                list:[]
			}
		},
		methods:{
			/**
			*双击事件
			**/
			rowDblclick(data){
				this.selectedShopData = data;
				this.confirmAddGoods();
			},
			/**
			*关闭当前弹出框组件
			*/
            closeCurrentComponent(){
                this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
			},
			
            confirmAddGoods(){
				
				var selectedGoodsData = this.selectedShopData;
				console.log('selectedGoodsData',selectedGoodsData);
				if(!selectedGoodsData ) {
				    this.$message({
				        message:'请选择价目信息',
						type:'error'
					})
					return;
				}

                this.closeCurrentComponent();
				this.params.callback && this.params.callback({data:selectedGoodsData});


			},
            /*sizeChange(size){
                // 每页加载数据
				this.search.page_size = size;
                this.search.page_no = 1;
               
                this.getList();
			},
            pageChange(page_no){
                // 页数改变
                this.search.page_no = page_no;
                this.getList();
            },*/

           
			/**
			 * 请空已选的数据
			 * **/
			clearSelectedData(){
                this.selectedShopData = '';
                //this.radioData = '';
                this.$refs.list && this.$refs.list.clearSelection();
			},

            /*searchList(){
              //模糊搜索
				this.getList();
			},*/
            select(selection){//单选发生改变
				this.selectedShopData = selection;
            },

            /**
            *初使化数据
            **/
			initData(){
			  var data = this.params || {};
			   //列表选择框的问题，1为单选，2为多选，3为没有任何选择框
			  //this.selectType = data.type == 1?'radio':data.type == 2?'checkbox':'';
			  //this.search.material_id = 104827;
			  this.search.material_id = data.material_id || null;
			  this.search.shop_id = data.shop_id || null;
			
			},

            getList(){
            	this.search.singleShotDate = new Date().getTime();
				this.ajax.postStream('/price-web/api/price/order/getReturnPrice',this.search,(d)=>{
              	    var data = d.body;

              	    if(!data.result){
                        this.$message({
                            message:data.msg,
                            type:'error'
                        })
              	        return;
					}
                    this.list = data.content;
					//this.count = data.content.count;
                    this.clearSelectedData();
                },function(e){
					console.log(e);
                })
            }


		},

        created(){
			this.initData();
			this.getList();
		}



	}
</script>
<style type="text/css" scoped>
	.el-table__body-wrapper{margin-bottom:20px;}
</style>