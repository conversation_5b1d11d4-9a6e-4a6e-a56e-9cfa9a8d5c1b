<!-- 售后单方案--服务  单详情、新增 -->
<template>
  <div  >
    <el-form :model='submitData' :rules='rules1' label-width='130px' ref="submitData">
      <el-row :gutter='40'>
        <el-col :span='8'>
          <el-form-item label='方案编码'>
            <el-input size='mini'  v-model="submitData.after_plan_no"  disabled placeholder="系统自动生成"></el-input>
          </el-form-item>
          <el-form-item label='服务类型' prop='service_type_one'>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"

              v-model="serviceTypeOneTooltipControl"
              :content="serviceTypeOneTooltipContent"
              :disabled="!serviceTypeOneTooltipContent || serviceTypeOneTooltipDisabled"
              :manual="serviceTypeOneTooltipControl"
            >
              <xpt-select-aux v-model='submitData.service_type_one' aux_name='serviceTypeOne' @change='selectParent' :disabledOption='disabledServiceTypeOption' :disabled="isSave"  @visible-change="bindOptionsItemHoverEvent" popperClass="service-type-one-select" @mouseover.native="serviceTypeOneTooltipDisabled = false" @mouseout.native="!bindOptionsItemHoverEvent.bool && (serviceTypeOneTooltipDisabled = true)"></xpt-select-aux>
            </el-tooltip>

            <el-tooltip v-if='rules1.service_type_one[0].isShow' class="item" effect="dark" :content="rules1.service_type_one[0].message" placement="right-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
          </el-form-item>
          <el-form-item label='方案状态'>
            <!-- <el-input size='mini'  placeholder="创建" v-model="submitData.status" disabled></el-input> -->
            <el-input size='mini'  placeholder="创建" v-model="statusList[submitData.status]" disabled></el-input>
          </el-form-item>
<!--          <el-form-item label='服务分类' prop='service_type'>-->
<!--            <el-tooltip-->
<!--              class="item"-->
<!--              effect="dark"-->
<!--              placement="top"-->
<!--              v-model="serviceTypeTooltipControl"-->
<!--              :content="serviceTypeTooltipContent"-->
<!--              :disabled="!serviceTypeTooltipContent || serviceTypeTooltipDisabled"-->
<!--              :manual="serviceTypeTooltipControl"-->
<!--            >-->
<!--              <el-select v-model="submitData.service_type" placeholder="请选择" size="mini" :disabled="isSave" @visible-change="serviceTypeSelectHover"  @mouseover.native="serviceTypeTooltipDisabled = false" @mouseout.native="!serviceTypeSelectHover.bool && (serviceTypeTooltipDisabled = true)">-->
<!--                  <el-option-->
<!--                    v-for="item in childClassData"-->
<!--                    :key="item.code"-->
<!--                    :label="item.name"-->
<!--                    @mouseover.native="() => serviceTypeSelectItemHover(item.code)"-->
<!--                    :disabled="disabledOption.some(o => o.tag == item.tag)"-->
<!--                    :value="item.code">-->
<!--                  </el-option>-->
<!--              </el-select>-->
<!--            </el-tooltip>-->

<!--            <el-tooltip v-if='rules1.service_type[0].isShow' class="item" effect="dark" :content="rules1.service_type[0].message" placement="right-start" popper-class='xpt-form__error'>-->
<!--                <i class='el-icon-warning'></i>-->
<!--              </el-tooltip>-->
<!--          </el-form-item>-->
        </el-col>
        <el-col :span='8'>
          <el-form-item label='日期'>
            <el-date-picker type="date"  v-model="submitData.after_plan_date" placeholder="选择日期" :picker-options="pickerOptions0" size='mini' :disabled="isSave"></el-date-picker>
          </el-form-item>
          <el-form-item label='服务费用'>
            <el-input size='mini'  type="number" @change='valideFn1' v-model="submitData.service_fee" min="0" @blur="() => submitData.service_fee = Number(submitData.service_fee).toFixed(2)" :disabled="isSave"></el-input>
          </el-form-item>
          <el-form-item label='客户期望上门时间' prop='commitment_time'>
            <el-date-picker type="date"  v-model="submitData.commitment_time" placeholder="选择日期" :picker-options="pickerOptions0" size='mini' :disabled="isSave"></el-date-picker>

          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label='客户承担费用'>
            <el-input
              size='mini'
              type="number"
              @change='valideFn2'
              v-model="submitData.customer_fee"
              min="0"
              @blur="() => submitData.customer_fee = Number(submitData.customer_fee).toFixed(2)"
              :disabled="isSave"
              readonly
              icon="search"
              :on-icon-click="() => selectCustomerFee()"
            ></el-input>
          </el-form-item>
          <el-form-item label='服务商收取服务费用' v-if="submitData.service_type_one==='EWFW'">
            <el-switch
              v-model="submitData.if_charge_service_fees"
              on-text="是"
              off-text="否"
              on-value="Y"
              off-value="N"
            ></el-switch>
          </el-form-item>
<!--          <el-form-item label='方案状态'>-->
<!--            &lt;!&ndash; <el-input size='mini'  placeholder="创建" v-model="submitData.status" disabled></el-input> &ndash;&gt;-->
<!--            <el-input size='mini'  placeholder="创建" v-model="statusList[submitData.status]" disabled></el-input>-->
<!--          </el-form-item>-->
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="22">
          <el-form-item label='备注信息' class='height60'>
            <el-input type='textarea'  v-model="submitData.remark" size='mini'  :maxlength="255" :disabled="isSave"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='40'>
        <el-col :span='24'>
          <el-form-item label="商品信息" style="margin:10px 0">
            <el-button type='primary' size='mini' @click="addGoodsMore" :disabled="isSave">增加商品</el-button>
            <el-button type="danger" size='mini' @click="del" :disabled="isSave">删除</el-button>
          </el-form-item>

        </el-col>
      </el-row>

      <el-row>
        <xpt-headbar>
          <el-button type="primary" size="mini" slot='left' @click='uploadFun' :disabled="isSave">上传图片<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload></el-button>
          <el-button type="success" size="mini" slot='left' @click='pictureFun'>查看或者下载附件</el-button>

        </xpt-headbar>
        <el-table row-key="id" :data="returnGoods" border @selection-change="select"  ref="returnGoods" class='xpt_pmm_scroll' style="width:100%"  @row-click="rowClick" :row-class-name="tableRowClassNameRepair">
          <el-table-column   type="selection" width="55" align="center" ></el-table-column>
          <el-table-column  label="序号" type="index"></el-table-column>

          <el-table-column label="商品编码" prop="material_code"></el-table-column>
          <el-table-column label="商品名称" prop="material_name" >
          </el-table-column>
          <el-table-column label="规格描述" prop="description"></el-table-column>
          <!--<el-table-column label="有无补件发货" >
            <template slot-scope="scope">
              querylect size="mini"  style="width:100%"  v-model="scope.row.if_send" placeholder="请选择">
                <el-option
                  v-for="(value,key) in if_sends"
                  :label="value"
                  :value="key" :key='key'>
                </el-option>
              <querylect>
            </template>
          </el-table-column>-->
          <el-table-column label="销售订单号" prop="sale_order_no"></el-table-column>
          <el-table-column label="供应商" prop="provider"></el-table-column>
          <el-table-column label="BOM版" prop="bom_version"></el-table-column>
          <el-table-column label="服务分类" prop="bom_version">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.service_class"
                placeholder="请选择" size="mini"
                @visible-change="serviceTypeSelectHover"
                @mouseover.native="serviceTypeTooltipDisabled = false"
                :disabled="submitData.service_type_one==='AZ'||submitData.service_type_one==='CHANGE_OF_ADDRESS'|| isSave"
                @mouseout.native="!serviceTypeSelectHover.bool && (serviceTypeTooltipDisabled = true)">
                <el-option
                  v-for="item in childClassData"
                  :key="item.code"
                  :label="item.name"
                  @mouseover.native="() => serviceTypeSelectItemHover(item.code)"
                  :disabled="disabledOption.some(o => o.tag == item.tag)"
                  :value="item.code">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="退货方式" prop="returns_type" v-if="submitData.service_type_one === 'KDFH'">
            <template slot-scope="scope">
              <el-select size='mini' placeholder="请选择" v-model='scope.row.returns_type' @change="onReturnsTypeChange(scope)">
                <el-option label="退货" value="RETURN"></el-option>
                <el-option label="换货" value="CHANGE"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="换货商品" prop="change_goods_type" v-if="submitData.service_type_one === 'KDFH'">
            <template slot-scope="scope">
              <el-select size='mini' placeholder="请选择" v-model='scope.row.change_goods_type' :disabled="scope.row.returns_type!=='CHANGE'">
                <el-option label="同商品" value="SAME"></el-option>
                <el-option label="不同商品" value="DIFF"></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>

      </el-row>
    </el-form>
    <el-row	:gutter='40' class="mgt10">
      <el-col :span='20'>
        <el-input
          type="textarea"
          :rows="4"
          :maxlength="500"
          :disabled="isSave"
          placeholder="粘贴或输入文本，点击'一键识别'自动识别收货人信息。如浙江省杭州市余杭区文一西路969号小邮局,186XXXX1234，淘小宝"
          v-model="addressData">
        </el-input>
      </el-col>
    </el-row>
    <el-row	:gutter='40' class="mgt10">
      <el-col :span='24'>
        <div class="address-btn">
          <el-button  size='mini' @click="clearAddress" icon="delete" :disabled="isSave">清空</el-button>
          <el-button type='primary' size='mini' @click="getAddressByAuto" :loading="readLoading" :disabled="isSave">一键识别</el-button>
        </div>
      </el-col>
    </el-row>
      <el-row>
        <el-form label-position="right" label-width="120px" :model="info" :rules="rules" ref="addressInfo">
          <el-form-item label="确认信息" >
            <!-- <a v-if="isSave" href="javascript:;" style="color:#EEEEEE;">{{text}}</a> -->
            <!-- <a v-else href="javascript:;" @click="modify">添加地址</a> -->
            <!-- <a v-else href="javascript:;" @click="modify">{{text}}</a> -->
            <el-button type='primary'  size='mini' style="margin-top:5px;margin-left:5px;" @click="choiceAddress" :disabled="isSave">修改客户地址</el-button>
          </el-form-item>
          <div style="overflow:hidden;width:100%;">
            <el-form-item label="取货地址" prop="province" style="float:left;width:auto;">
              <el-select  placeholder="请选择" size='mini'  v-model="info.province" @change="changeProvice" :disabled="!canEditAddress">
                <el-option
                  v-for="(value,key) in province"
                  :key="key"
                  :label="value"
                  :value="parseInt(key)">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="city" style="float:left;width:auto;margin-left:-110px;">
              <el-select placeholder="请选择" size='mini'  v-model="info.city" @change="changeCity" :disabled="!canEditAddress">
                <el-option
                  v-for="(value,key) in city"
                  :key="key"
                  :label="value"
                  :value="parseInt(key)">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="area" style="float:left;width:auto;margin-left:-110px;">
              <el-select  placeholder="请选择" size='mini' v-model="info.area" @change="changeArea" :disabled="!canEditAddress">
                <el-option
                  v-for="(value,key) in area"
                  :key="key"
                  :label="value"
                  :value="parseInt(key)">
                </el-option>
              </el-select>

            </el-form-item>
            <el-form-item label=""  style="float:left;width:auto;margin-left:-110px;">
              <el-select  placeholder="请选择" size='mini'  v-model="info.street" :disabled="!canEditAddress">
                <el-option
                  v-for="(value,key) in street"
                  :key="key"
                  :label="value"
                  :value="parseInt(key)">
                </el-option>
              </el-select>

            </el-form-item>
          </div>

          <el-form-item label="详细信息" prop="receiver_addr">
            <el-input size='mini' v-model="info.receiver_addr" style="width:80%" :disabled="!canEditAddress"></el-input>
            <el-tooltip v-if='rules.receiver_addr[0].isShow' class="item" effect="dark" :content="rules.receiver_addr[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="收货人电话" prop="reveiver_phone">
            <el-input size='mini'  v-model="info.reveiver_phone" :disabled="!canEditAddress"></el-input>
            <el-tooltip v-if='rules.reveiver_phone[0].isShow' class="item" effect="dark" :content="rules.reveiver_phone[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="收货人姓名" prop="receiver_name">
	            <el-input size='mini'  v-model="info.receiver_name" icon="linshi-eye-open" :on-icon-click="decryption" :disabled="!canEditAddress"></el-input>
            <!-- <el-input size='mini'  v-model="info.receiver_name" :disabled="!canEditAddress"></el-input> -->
					<!-- <xpt-eye-switch v-model="info.receiver_name" hideType="name" :disabled="!canEditAddress" :readonly='true' :hideBorder="true" :isCallback="true" :aboutNumber="otherInfo.merge_trade_no" @onOpenHideData="decryption"></xpt-eye-switch> -->
            <el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-form>

        <div style="margin:10px 0;text-align:center">
          <el-button type='primary' @click="preSaveEvent" size='mini' :disabled="!otherInfo.canEditSolutions?true:isSave"  :loading='saving'>保存</el-button>
          <el-button type='primary' size='mini' @click='submitEvent' :disabled="!otherInfo.canEditSolutions?true:isSubmit">提交</el-button>
          <el-button type='primary' size='mini' @click='withdraw' :disabled="!otherInfo.canEditSolutions?true:isWithdrawed">撤回</el-button>
          <el-button type='primary' size='mini' :disabled="!otherInfo.canEditSolutions" @click="addNew">新增</el-button>
        </div>
      </el-row>

  </div>
</template>
<script>
  //import addressComponent from '@components/after_solutions/otherInfo2.vue';
  import fn from '@common/Fn.js';
  import addressInfo from './model/addressInfo';
  import repairHelp from './model/repair_help';
  import vl from '@common/validate.js';
  import smart from "address-smart-parse";
  export default {
    mixins: [addressInfo,repairHelp],
    props:['params','selectedData','otherInfo','copySelectData','operatParams','afterGroup','allQuestions'],
    //props:["params",'selectedData','addressInfo','otherInfo','isEditSolution','questionList','editOtherInfo','editQuestion','operatParams','copySelectData'],
    data(){
      var self=this;
      return{
        initStatus:true,
        selectFeeList: [], // 历史选择的客户承担费用
        scmMaterielId: {},
        serviceTypeOneTooltipContent: '',
        serviceTypeOneTooltipControl: false,
        serviceTypeOneTooltipDisabled: false,
        serviceTypeTooltipContent: '',
        serviceTypeTooltipControl: false,
        serviceTypeTooltipDisabled: false,
        addressData: undefined,
        readLoading:false,
        questionData:null,
          info:{
            province:null,
            city:null,
            area:null,
            street:null,
            receiver_addr:null,
            receiver_name:null,
            reveiver_phone:null,
            area:null,
            address_id:null,
            street_name:null,
            city_name:null,
            province_name:null,
            area_name:null,

            // source_address_id:null
          },
        ifClickUpload:false,
        uploadData:{},
        submitData:{
          id:'',
          question_sub_id:'',
          after_order_id:'',//售后单ID
          after_order_no:'',//售后单号
          merge_trade_id:null,
          merge_trade_no:null,
          after_plan_no:'',//方案编码
          after_plan_date:new Date(),//方案日期
          status:'',//方案状态
          customer_fee:'',//客户承担费用
          service_type:null,//服务分类
          service_type_one:'',//服务类型
          commitment_time:'',//承诺上门时间
          service_fee:'',//服务费用
          remark:'',//备注信息
          //urls:[''],//图片
          receiver_name:'',//收货人
          reveiver_phone:'',//收货人电话
          receiver_addr:'',//收货人地址
          province:'',//省id
          city:'',//市id
          area:'',//区id
          street:'',//街道id
          repairGoods:[],//问题商品信息
          addRepairGoods:[],//增加问题商品信息
          updateRepairGoods:[],//更新问题商品信息
          delRepairGoods:[],//删除问题商品信息
          questionSubVO:{},//子问题信息,
          cust_id:'',
          street_name:null,
          city_name:null,
          province_name:null,
          area_name:null,

          // source_address_id:null,
          address_id:null,
          after_plan_group_id:null,
          logistics_supplier_class:'',
          if_charge_service_fees: '' // 收费服务商收取服务费用
        },
        isExist:false,//是否存在方案
        isEditPlan:false,
        statusList:{SAVE:"创建",SUBMIT:"提交",RETRACT:"撤回"},
        addressIsPass:true,
        rules1:{
          // //服务分类不能为空
          // service_type:vl.isNotBlank({
          //   required:true,
          //   self: this,
          //   msg: '服务分类不能为空'
          // }),
          //服务类型不能为空
          service_type_one:vl.isNotBlank({
            required:true,
            self: this,
            msg: '服务类型不能为空'
          }),
          // commitment_time:vl.isNotBlank({
          //   required:true,
          //   self: this,
          //   msg: '承诺上门时间不能为空'
          // })
        },

        saving:false,
        isSave:false,
        isSubmit:true,
        isWithdrawed:true,

        childClassData: [],
        // 所有子分类
        childClassDataAll: __AUX.get('serviceType'),
        service_type_one_remark: __AUX.get('serviceTypeOne').reduce((newObj, obj) => {
          newObj[obj.code] = obj.remark
          return newObj
        }, {}),
        service_type_remark: __AUX.get('serviceType').reduce((newObj, obj) => {
          newObj[obj.code] = obj.remark
          return newObj
        }, {}),

        indexNumber:0,
        delRepairGoods:[],
        returnGoods:[],
        selectsGoodsData:[],
        service_types:{MAINTAIN:"上门维修",
          INSTALL:"上门安装",
          CARGO:"上门拉货",
          FEEDBACK:"48小时反馈",
          INFO_CHANGE:"其他信息变更",
          ADDRESS_CHANGE:"变更地址信息",
          COMPEN:"赔偿",
          CLAIM:"索赔",
          COMPLAINT:"投诉",
          CONSULTATION:"咨询",
          DELIVERY:"村淘送货"
        },
        isAddUpdateDes:true,
        if_sends:{1:"是",0:"否"},
        pickerOptions0:{
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          }
        },
       successmsg:"",
        errormsg:"",
        totalSave:0,//
        saveNum:0,
        detailPostData:{
            id:"",
          after_plan_group_id:""
        },
        afterGroupLocal:[],
        disabledOption : [{tag:''},{tag:'M'},{tag:'m'}],
        disabledServiceTypeOption : [{tag:null},{tag:''},{tag:'M'},{tag:'m'}]
      }
    },
    watch:{
      //监听子问题ID的变化
      selectedData:{
        handler:function(newVal){
          this.changeAddressInfoByMaterialId(newVal);
          this.changeServiceType(newVal)
        }
      },
      otherInfo:{
        handler:function(newVal,oldVal){
           this.setStatus();
        },
        deep:true
      },
      'operatParams.bool':function(newVal,oldVal){
        //关闭页面
        this.closeParentTab({callback:()=>{
          let  tabName = this.operatParams.params.tabName;
          this.$root.eventHandle.$emit('removeTab',tabName);
        }});
      },
      'operatParams.isSwitchTab':function(newVal,oldVal){
        //切换的提醒
        this.switchTab();
      }
    },
    methods:{
      changeServiceType(val) {
        if (!(val.every(item => /^(EXPRESS|EXPRESS_PAY)$/.test(item.deliver_method)))) {
          if (this.otherInfo.form.if_dealer == 'N') {
            this.$message.info('送货方式=快递、快递收费、的批次单才可走快递转寄，送货方式满足条件且批次单是非经销商订单可走快递返货')
          } else {
            this.$message.info('送货方式=快递、快递收费、的批次单才可走快递转寄')
          }
          this.disabledServiceTypeOption = [{tag:null},{tag:''},{tag:'M'},{tag:'m'}, {code: 'KDFH'}, {code: 'KDZJ'}]
        } else {
          if(this.otherInfo.form.if_dealer == 'Y') {
            this.disabledServiceTypeOption = [{tag:null},{tag:''},{tag:'M'},{tag:'m'}, {code: 'KDFH'}]
          } else {
            this.disabledServiceTypeOption = [{tag:null},{tag:''},{tag:'M'},{tag:'m'}]
          }
        }
      },
      clearAddress(){
        this.addressData = ''
      },
      async getAddressByAuto(){
        console.log('获取地址')
        this.canEditAddress = false
        if (!this.addressData) {
          this.$message.warning('请粘贴或输入地址');
          return;
        }
        try{
          this.readProvinceLocking=true;
          this.readCityLocking=true;
          this.readAreaLocking=true;
          this.readLoading=true;
          const result = smart(this.addressData);
          const { city, address, name, phone, province, county, street } = result;
          if (!city || !address) {
            this.$message.warning('识别失败，请重新输入');
            this.readLoading=false;
            return;
          }
          const params={
            cityName: `${city}${county || ''}${street || ''}`,
            addressDetail: `${province || ''}${city || ''}${county || ''}${
              street || ''
            }${address}`,
          }
          this.ajax.postStream('/external-web/api/AddressAndEncry/supplyAddress',params,async res=>{
            if(res.body.result){
              const {receiverState,receiverCity,receiverDistrict,receiverStreet,addressName}=res.body.content;
              const  dataResId= await this.saveEdit({
                ...(res.body.content || {}),
                receiver_mobile:phone,
                receiver_name:name,
                address
              })
              this.encryption_add_id = dataResId
              //
              await Promise.all([this.getAreaCode(1,"provinceObj"),this.getAreaCode(receiverState,"cityObj"),
                this.getAreaCode(receiverCity,"areaObj"),
                this.getAreaCode(receiverDistrict,"streetObj")])
              this.info.province = receiverState
              this.info.city = receiverCity
              this.info.area = receiverDistrict
              this.info.street = receiverStreet
              this.info.receiver_addr = address
              this.info.receiver_name = name
              this.info.reveiver_phone = phone
              this.readLoading=false;
              this.canEditAddress = true
              this.needDecrypt = false;
              this.ifDecryptSuccess = false;
            }else{
              this.$message.error(res.body.msg||'识别失败，请重新输入');
              this.readLoading=false;
            }
          })
        }catch{
          this.readLoading=false;
        }
      },

      saveEdit(data){
        return new Promise((resolve,reject)=>{
          const params = {
            receiver_state_name: data.receiverStateName,
            receiver_city_name: data.receiverCityName,
            receiver_district_name: data.receiverDistrictName,
            receiver_street_name: data.receiverStreetName,
            receiver_name: data.receiver_name,
            receiver_mobile: data.receiver_mobile,
            cust_id: this.copySelectData.buyer || this.copySelectData.originalBuyer,
            receiver_phone: '',
            receiver_state: data.receiverState,
            receiver_city: data.receiverCity,
            receiver_district: data.receiverDistrict,
            receiver_street: data.receiverStreet,
            receiver_address: data.address,

          }
          this.ajax.postStream("/afterSale-web/api/aftersale/addReceriverInfo",params,function(response){
            if(response.body.result){
              console.log(response.body.content)
              resolve(response.body.content)
              // let data = self.submitData;
              // data.fid = response.body.content;
              // self.params.callback(data);
              // self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
              // self.$root.eventHandle.$emit("close_copy_address");
            }else{
              this.$message.error(response.body.msg || '')
              reject()
            }
          },err=>{
            reject()
          });
        })
      },




      // 地区
      getAreaCode(code,key){
        var self = this;
        var url = "/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId";
        if(!code) return;
        self.ajax.postStream(url,code,function(response){
          if(response.body.result){
            self[key] = response.body.content
          }
        })
      },
      serviceTypeSelectHover (bool){
        this.serviceTypeSelectHover.bool = bool
        if(!bool){
          this.serviceTypeTooltipControl = false
          this.serviceTypeTooltipContent = this.service_type_remark[this.submitData.service_type]
        }
      },
      serviceTypeSelectItemHover (code){
        if(this.serviceTypeSelectHover.bool){
          this.serviceTypeTooltipContent = this.service_type_remark[code]
          this.serviceTypeTooltipControl = true
        }
      },
      bindOptionsItemHoverEvent (bool){
        this.bindOptionsItemHoverEvent.bool = bool
        if(bool){
          if(!this.bindOptionsItemHoverEvent.isBind){
            this.bindOptionsItemHoverEvent.isBind = true
            $('.service-type-one-select').mouseover(e => {
              if(/el-select-dropdown__item/.test(e.target.className) && this.bindOptionsItemHoverEvent.bool){
                this.serviceTypeOneTooltipContent = this.service_type_one_remark[e.target.getAttribute('data-code')]
                this.serviceTypeOneTooltipControl = true
              }
            })
          }
        }else {
          this.serviceTypeOneTooltipControl = false
          this.serviceTypeOneTooltipContent = this.service_type_one_remark[this.submitData.service_type_one]
        }
      },
      selectParent(value) {
        if (this.otherInfo.form.if_dealer === 'N' && value === 'CCFW') {
          this.$message.warning('非经销商订单，不能下推“仓储服务”');
          value = '';
          this.$nextTick(() => {
            this.submitData.service_type_one = value;
          })
        }
        let childClassData = []
        this.childClassDataAll.find(row => {
          if(row.parentCode === value && row.status) {
            childClassData.push(row)
          }
        })
        this.childClassData = childClassData
        // this.submitData.service_type = ''
        this.returnGoods.forEach(e=>{
          if(this.childClassData.length == 1){// CTAL-7911 当选择服务类型下的服务分类为一个时 默认选上服务分类
            // this.submitData.service_type = this.childClassData[0].code
            e.service_class = this.childClassData[0].code
          }else{
            if(!this.initStatus){
              e.service_class = ''
            }
          }
        })
        if(this.initStatus){
          this.initStatus = false
        }
        this.serviceTypeTooltipContent = ''
      },
      /**
      *跳到工作台
      ***/
      addNew(){
        let params = JSON.parse(JSON.stringify(this.otherInfo));
        /*after_plan_id:null,//编辑,方案ID
          after_question_id:null,//方案所挂的问题ID
          after_plan_group_id:null,//方案组ID*/
          if(params.hasOwnProperty('after_plan_id')){
            delete params.after_plan_id;
          }
          if(params.hasOwnProperty('after_question_id')){
            delete params.after_question_id;
          }
          if(params.hasOwnProperty('after_plan_group_id')){
            delete params.after_plan_group_id;
          }
        this.$root.eventHandle.$emit('creatTab',{
                  name: '售后方案',
                  url: 'after_solutions/solutions',
                  params: {
                    afterOrderInfo: params
                  },
                  component: () => import('@components/after_solutions/solutions')
              })
      },
      /**
      *选择客户地址的时候，把fid填充到address_id字段里面去
      **/
      isNeedAddressId(data){
        this.submitData.address_id = data.fid;
        console.log('data',data,this.submitData.address_id);
      },
      /**
      *设置问题对应的方案
      ***/
      tableRowClassNameRepair(row, rowIndex) {
        console.log('asdkfjlksdf');
        if(!this.copySelectData) return '';
        console.log('this.copySelectData',this.copySelectData);
        let id = this.copySelectData.question_sub_id || this.copySelectData.originalSubQuestionId;
        let question_sub_id = row.question_sub_id;
        if(question_sub_id == id || (!row.id && row.parentQuestionId == this.copySelectData.parent_question_id)) return 'pmm_blue22';
        return '';
      },
      /**
      *单击某行执行的数据
      **/
      rowClick(row, event, column){
        console.log('column',column);
        this.currentRowselectGoods = row;
        this.concatSelectReturnAndExchangeGoods();
      },
      /**
      *合并退货，换货商品
      **/
      concatReturnAndExchangeGoods(){
        let goods = this.returnGoods;
        this.$emit('allGoodsmatchQuestions',goods)
      },

      /**
      *合并已选中的明细行
      ***/
      concatSelectReturnAndExchangeGoods(){

        this.$emit('selectGoodsmatchQuestions',[this.currentRowselectGoods])
      },

        //子问题选择变化 去掉下面已添加的商品
      // delNoQuestionSubIdGoods(newData){
      //     var self=this;
      //     if(self.returnGoods && self.returnGoods.length>0){
      //         for(var i=0;i<self.returnGoods.length;i++){
      //             var sub_id=self.returnGoods[i].question_sub_id;
      //             if(JSON.stringify(newData).indexOf(sub_id)<0){
      //               self.returnGoods.splice(i,1);
      //               i--;
      //             }
      //         }
      //     }
      // },
        //检验重复商品 根据子问题id去除重复的
      isRepeatGoods(parentQuestionId){
         var flag=false;
          if(this.returnGoods && this.returnGoods.length>0) {
            for (var i = 0; i < this.returnGoods.length; i++) {
              if (this.returnGoods[i].parentQuestionId == parentQuestionId) {
                flag = true;
                break;
              }
            }
          }
          return flag;
      },
      //通过子问题id获取问题描述
      getSpecBySubId(subid,key){
          var out='';
          for(var i=0;i<this.selectedData.length;i++){
              if(this.selectedData[i].question_sub_id==subid){
                out=this.selectedData[i][key];
                  break;
              }
          }
          return out;
      },

      /**
      *获取新增一行明细行对像，默认问题带过来
      ***/
      getGoodInfo(question){
        var question = question || {};
        return {
          id : '',
          after_plan_id : '',
          sale_order_no : question.sys_trade_no || null,
          provider : question.suppiler_name || null,
          bom_version : question.bom_version || null,
          material_code : question.material_code || null,
          material_name : question.material_name || null,
          description : question.material_specification || null,

          batch_trade_no : question.batch_trade_no || null,
          batch_trade_id : question.batch_trade_id || null,
          lot : question.lot || null,
          shop_name : question.shop_name || null,
          shop_id : question.shop_id || null,
          if_stop : question.is_stop || null,
          question_sub_id : question.question_sub_id || null,
          question_description : question.question_description || null,

          units : question.units || null,
          num : question.count || null,
          buyer : question.buyer || null,
          buyer_nick : question.buyer_nick || null,

          act_price : question.act_price || null,
          volume : question.volume || null,//体积
          parentQuestionId : question.parent_question_id,
          service_class: question.service_class || undefined,
        }
      },

      /**
      *非商品添加明细
      **/
      getGoodsInfoByNotQuestion(good,question_sub_id){
        if(!good) return '';
        var question = this.getQuestionByQuestionSubId(question_sub_id);
        var obj = this.getGoodInfo();

        obj.sale_order_no = good.sys_trade_no;
        obj.provider = good.suppiler_name;
        obj.bom_version = good.bom_version;
        obj.material_code = good.material_number;
        obj.material_name = good.material_name;
        obj.description = good.material_specification;
        obj.batch_trade_no = good.batch_trade_no;
        obj.batch_trade_id = good.batch_trade_id;
        obj.lot = good.lot;

        obj.buyer = good.buyer || question.buyer;
        obj.buyer_nick = good.buyer_nick || question.buyer_nick;
        obj.shop_name = good.shop_name || question.shop_name;
        obj.shop_id = good.shop_id || question.shop_id;


        obj.if_stop = good.if_stop;
        obj.question_sub_id = question_sub_id;
        obj.question_description = question.question_description;
        obj.units = good.material_unit;
        obj.num = good.number;

        obj.act_price = good.act_price;
        obj.volume = good.volume;
        obj.parentQuestionId = question.parent_question_id;
        return obj;
      },
      /**
      *添加问题商品到明细行
      **/
      addQuestionGoodsIntoList(question){
        if(!question || !question.length) return;
        let i = question.length,a=0;
        for(;a<i;a++){
          let obj = this.getGoodInfo(question[a]);
          if(this.childClassData.length == 1) {// CTAL-7911 当选择服务类型下的服务分类为一个时 默认选上服务分类
            // this.submitData.service_type = this.childClassData[0].code
            obj.service_class = this.childClassData[0].code
          }
          this.returnGoods.push(obj);
        }
        this.concatReturnAndExchangeGoods();
      },
      /**
      *添加非商品到明细行
      ***/
      addNotQuestionGoodsIntoList(goods){
        //有源单的非商品问题拿明细里面的值
        if(!goods || !goods.data || !goods.data.length) return;
        let d = goods.data,i = d.length;
        let questionId = goods.questionId;
        for(var a=0;a<i;a++){
          let f = d[a];
          let obj = this.getGoodsInfoByNotQuestion(f,questionId);
          if(this.childClassData.length == 1) {//  当选择服务类型下的服务分类为一个时 默认选上服务分类
            obj.service_type = this.childClassData[0].code
          }else{
            obj.service_type = undefined
          }
          this.returnGoods.push(obj);
        }
        this.concatReturnAndExchangeGoods();

      },
      /**
      *根据选择的问题把问题商品和非问题商品分为两问题
      ***/
      getQuestionsByIfGoodsQuestion(){
        let goods = this.selectedData;
        if(!goods || !goods.length){
          this.$message.error('请选择问题再进行操作');
          return ''
        }
        let questionGoods = [],notQuestionGoods = [];
        let i = goods.length,a=0;
        console.log('alskdjflaksdjf',goods);
        for(;a<i;a++){
          let isGoodsQuestion = goods[a].if_goods_question == 'Y'?true:false;
          if(isGoodsQuestion && !this.isRepeatGoods(goods[a].parent_question_id)){
            questionGoods.push(goods[a]);
            continue;
          }
          if(!isGoodsQuestion){
            notQuestionGoods.push(goods[a]);
            continue;
          }
        }
        return {
          questionGoods:questionGoods,
          notQuestionGoods:notQuestionGoods
        }
      },

      //添加多个问题商品的商品信息，去除重复的和非商品问题
      addGoodsMore(){

          let data = this.getQuestionsByIfGoodsQuestion();
          if(!data) return;
          var goodsQuestion = data.questionGoods;
          var notGoodsQesid = data.notQuestionGoods;
          if(goodsQuestion && goodsQuestion.length){
            this.addQuestionGoodsIntoList(goodsQuestion);
          }
          if(!notGoodsQesid || !notGoodsQesid.length) return;
          //var isGoodsQuestion = this.selectedData[0].if_goods_question == 'Y'?true:false;
          let params = {
            questionList:notGoodsQesid,
            merge_trade_id:this.submitData.merge_trade_id,
            callback:(data)=>{
              if(!data || !data.data || !data.data.length) return;
              this.addNotQuestionGoodsIntoList(data);

            }
          }
          this.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('@components/after_solutions/goods_repair'),
            style:'width:80%;height:560px',
            title:'添加商品'
          });
          console.log('添加的商品this.returnGoods',this.returnGoods);
      },
      /**
       *关闭标签页面
       */
      closeParentTab(params){
        if(!params) return;
        var isChange = !this.isWithdrawed || !this.otherInfo.canEditSolutions?false:this.judgeIsChange();

        if(!isChange){
          params.callback && params.callback();
          return;
        }
        var _this = this;
        var data = {
          ok(){
            _this.preSave(()=>{
              params.callback && params.callback();
            },!0)

          },
          no(){
            params.callback && params.callback();
          },
          cancel(){
            console.log('看看关闭的东西');
            _this.setStatus();
          }
        }
        params.text?data.txt = params.text:'';
        this.$root.eventHandle.$emit('openDialog',data)
      },
      /**
       *子组件切换
       **/
      switchTab(){
        this.closeParentTab({
          callback:()=>{
            this.$emit("changeSolution");

          }
        })
      },
      //数值不能为负数
      valideFn1(){
        var self=this;
        if(self.submitData.service_fee<0){
          self.submitData.service_fee=-self.submitData.service_fee;
        }
      },
      valideFn2(){
        var self=this;
        if(self.submitData.customer_fee<0){
          self.submitData.customer_fee=-self.submitData.customer_fee;
        }
      },
      //根据值得到code
      getStatusCode(val,list){
        var res=val;
        for(var key in list){
          if(list[key]==val){
            res=key;
          }
        }
        return res;
      },
      //选择维修商品
      select(selections){
        this.selectsGoodsData=selections;
      },
      //删除维修商品
      del(){
          var self=this;
          if(self.selectsGoodsData && self.selectsGoodsData.length>0){
              for(var i=0;i<self.selectsGoodsData.length;i++){
                for(var j=0;j<self.returnGoods.length;j++) {
                  if(self.returnGoods[j]==self.selectsGoodsData[i]){
                      if(self.returnGoods[j].id && JSON.stringify(self.delRepairGoods).indexOf(self.returnGoods[j].id)<0){
                        let g = JSON.parse(JSON.stringify(self.returnGoods[j]));
                        if(g.hasOwnProperty('parentQuestionId')){
                          delete g.parentQuestionId;
                        }
                        self.delRepairGoods.push(g);
                      }
                      self.returnGoods.splice(j,1);
                      break;
                    }
                  }
              }
          }
      },
      //得到更新商品和修改商品
      getAddAndUpdateGoods(){
        var self=this;
        self.submitData.addRepairGoods=[];
        self.submitData.updateRepairGoods=[];
        for(var i=0;i<self.returnGoods.length;i++){
          var obj={};//先去除index
          if(obj.id){
            self.submitData.updateRepairGoods.push(obj);//修改商品信息
          }else{
            self.submitData.addRepairGoods.push(obj);//增加的商品信息
          }
        }
      },
      //转换得到地址信息
      getAddressloocal(){
        this.submitData.province = this.info.province
        this.submitData.city = this.info.city
        this.submitData.area = this.info.area
        this.submitData.street = this.info.street
        this.submitData.receiver_addr = this.info.receiver_addr
        this.submitData.receiver_name = this.info.receiver_name
        this.submitData.reveiver_phone = this.info.reveiver_phone
      },
      //子问题信息赋值
      setQuestionSubVO(datas){
        var self=this;
        var obj={
          id:datas.question_sub_id,// bigint(20) NOT NULL COMMENT '主键ID',
          parent_question_id:datas.parent_question_id,// bigint(20) DEFAULT NULL COMMENT '父问题ID',
          source_id:datas.question_sub_id,// bigint(20) DEFAULT NULL COMMENT '来源子问题ID',
          question_description:datas.question_description,// varchar(1000) DEFAULT NULL COMMENT '问题描述',
          creator:'',// bigint(20) DEFAULT NULL COMMENT '创建者',
          creator_name:'',// varchar(50) DEFAULT NULL COMMENT '创建者名字',
          create_time:'',// datetime DEFAULT NULL COMMENT '创建时间',
          last_modifier:'',// bigint(20) DEFAULT NULL COMMENT '最后修改人',
          last_modifier_name:'',// varchar(50) DEFAULT NULL COMMENT '最后修改者名字',
          last_modify_time:''// datetime DEFAULT NULL COMMENT '最后修改时间',
        }
        return obj;
      },
      //得到更新商品
      setUpdateGoods(sub_id){
        var self=this;
        var updateList=[]
        for(var i=0;i<self.returnGoods.length;i++){
          var obj=self.returnGoods[i];
          if(obj.id && obj.question_sub_id==sub_id){
            updateList.push(obj);//修改商品信息
          }
        }
        return updateList;
      },
      //设置新增修改商品
      setAndGoods(sub_id){
        var self=this;
        var addList=[]
        for(var i=0;i<self.returnGoods.length;i++){
          var obj=self.returnGoods[i];
          if(!obj.id && obj.question_sub_id==sub_id){
            addList.push(obj);//修改商品信息
          }
        }
        return addList;
      },
      // 选择客户承担费用
      selectCustomerFee() {
        const self = this;
        if(this.isSave) { return false }
        self.$root.eventHandle.$emit('alert', {
          title: '客户承担费用',
          style: 'width:950px;height:600px',
          component: () => import('@components/after_solutions/selectCustomerFee.vue'),
          params: {
            info: self.submitData,
            selectData: self.selectedData,
            selectFeeList: self.selectFeeList,
            callback: data => {
              if(data?.feeSelects?.length > 0) {
                self.selectFeeList = data.feeSelects
                self.$set(self.submitData, 'customer_fee', data?.totalFee)
              }

            }
          },
        })
      },
      //设置删除修改商品
      setDelGoods(sub_id){
        var self=this;
        var delList=[]
        for(var i=0;i<self.delRepairGoods.length;i++){
          var obj=self.delRepairGoods[i];
          if(obj.id && obj.question_sub_id==sub_id){
            delList.push(obj);//修改商品信息
          }
        }
        return delList;
      },
      /**
       *获取方案组ID
       **/
      getAfterPlanGroupId(){
        this.ajax.postStream('/afterSale-web/api/aftersale/plan/getPlanGroupId',{},(res)=>{
          var d = res.body;
          if(!d.result) return;
          this.submitData.after_plan_group_id = d.content;
        })
      },
      /**
       *保存校验
       */
      validateIsPass(){
        var bool = true;
        this.$refs.addressInfo.validate((valid) => {
          bool = valid;
        });
        return bool;
      },
      /**
       *根据方案ID获取方案组关联的列表
       */
      getQuestionsByAfterPlanGroupId(afterPlanGroupId){
        var params = {
          after_plan_group_id:afterPlanGroupId
        }
        this.ajax.postStream("/afterSale-web/api/aftersale/plan/getPlanGroupDetail",params,res=>{
          var data = res.body;
          if(!data.result) {
            return;
          }
          let d = data.content || [];
          this.afterGroupLocal = JSON.parse(JSON.stringify(d));

        });
      },
      /**
      *踢出方案组的方案
      *根据现在有明细子问题ID和已建方案的原始明细进行对比，如果原始子问题ID不存在现有子问题，则是踢出方案组
      ***/
      delPlanByQuestionSubId(){
        var goods = this.returnGoods;
        var length = this.afterGroupLocal.length,i = 0;
        for(;i < length; i++){

          if(JSON.stringify(goods).indexOf(this.afterGroupLocal[i].after_question_sub_id)<0){

            this.totalSave++;
            this.delPlan(this.afterGroupLocal[i].after_plan_id);
          }
        }
      },

      /**
      *根据父问题ID找到问题信息
      **/
      getQuestionByParentId(parent_question_id){
        if(!parent_question_id) return;
        var questions = this.allQuestions;
        var data = '';
        questions.map((a,b)=>{
          if(a.parent_question_id == parent_question_id){
              data = a;
              return;
          }
        })
        return data;
      },
      /**
      *通过子问题ID获取问题信息
      ***/
      getQuestionByQuestionSubId(questionSubId){
        var questions = this.allQuestions;
        var data = '';
        questions.map((a,b)=>{
          if(a.question_sub_id == questionSubId){
              data = a;
              return;
          }
        })
        return data;
      },

      /**
      *根据明细组装方案
      *已建方案用原子问题ID去关联，没有建方案的，用最新的子问题ID去关联
      ****/
      getSchemeList(){
        var postdata = {}
        for(var i = 0;i < this.returnGoods.length;i++){

            let good = JSON.parse(JSON.stringify(this.returnGoods[i]));

            let parentQuestionId = good.parentQuestionId;
            //原子问题ID去关联
            let question = this.getQuestionByParentId(parentQuestionId);
            let key = good.id?good.question_sub_id:question.question_sub_id;

            let info = postdata[key];
            if(!info){

              info = postdata[key] = JSON.parse(JSON.stringify(this.submitData));
              console.log(this.submitData);

              //let question = this.getQuestionByParentId(parentQuestionId);


              info.questionSubVO = this.setQuestionSubVO(question)
              if(!info.questionSubVO){
                this.$message.error('明细没有找到对应的问题');
                return '';
              }
              info.question_sub_id = info.questionSubVO.id;
              info.cust_id = good.buyer;

              //明细里面有把方案ID带过来
              info.id = good.id?good.after_plan_id : null;
              info.repairGoods = [];
            }

            good.question_sub_id = info.question_sub_id;

              good.scm_materiel_id = good.id ? good.scm_materiel_id : this.scmMaterielId[good.question_sub_id]

            //设置增加商品
            if(!good.id){

              info.addRepairGoods = info.addRepairGoods || [];
              info.addRepairGoods.push(good);
            }else{
              //修改
              info.updateRepairGoods=info.updateRepairGoods || [];
              info.updateRepairGoods.push(good);
            }



        }
        //对已存在的方案进行部分明细删除
        this.getDelGoodsByScheme(postdata);

        for(var key in postdata){
          let d = postdata[key];
          if(d.addRepairGoods){
              d.addRepairGoods.map((a,b)=>{
                if(a.hasOwnProperty('parentQuestionId')){
                  delete a.parentQuestionId;
                }
                if(a.hasOwnProperty('originalSubQuestionId')){
                  delete a.originalSubQuestionId;
                }
              })
          }

          if(d.updateRepairGoods){
              d.updateRepairGoods.map((a,b)=>{
                if(a.hasOwnProperty('parentQuestionId')){
                  delete a.parentQuestionId;
                }
                if(a.hasOwnProperty('originalSubQuestionId')){
                  delete a.originalSubQuestionId;
                }
              })
          }

          if(d.delRepairGoods){
              d.delRepairGoods.map((a,b)=>{
                if(a.hasOwnProperty('parentQuestionId')){
                  delete a.parentQuestionId;
                }
                if(a.hasOwnProperty('originalSubQuestionId')){
                  delete a.originalSubQuestionId;
                }
              })
          }

        }
        return postdata;
      },

      /**
      *获取存在方案已删除的明细
      *
      ****/
      getDelGoodsByScheme(schemes){

       if(!schemes) return;

        var old_repariGoodsinfo = this.old_repariGoodsinfo;
        if(!old_repariGoodsinfo) return '';
        old_repariGoodsinfo = JSON.parse(old_repariGoodsinfo);
        let i = old_repariGoodsinfo.length,a = 0;
        let delGoods = [];
        for(;a < i;a++){
          let oldGood = old_repariGoodsinfo[a];
          let question_sub_id = oldGood.question_sub_id;
          let scheme = schemes[question_sub_id];

          if(scheme){//存在方案
            scheme.delRepairGoods = scheme.delRepairGoods || [];
            if(JSON.stringify(scheme.updateRepairGoods || []).indexOf(oldGood.id) < 0){//修改明细里面找不到原有的明细
              let gg = JSON.parse(JSON.stringify(good));

              scheme.delRepairGoods.push(gg);
            }
          }
        }

      },

      /**
      *保存事件
      ***/
      preSaveEvent(){
        let self = this;
						new Promise((resolve,reject)=>{
						self.getDecryption(resolve,reject);
						}).then(res=>{
							if(this.isSave) return;
              this.isSave = this.isSubmit = this.isWithdrawed = true;
              this.preSave();
						}).catch(res=>{
							self.$message.error('该地址无法解密， 请手工增加新地址或者选择地址')
						})

      },
      //检验参数
      preSave(callback){
        var self = this;
        console.log('保存 奇江苏大丰开朗大方介绍说了大空间发生拉动开发建设');
        let errorMsg = '';
        if(this.returnGoods.length<1){
          errorMsg = '请先添加商品信息';
        }
        if(!errorMsg && !this.submitData.service_type_one){
          errorMsg = '服务类型不能为空';
        }
        // if(!errorMsg && !this.submitData.service_type){
        //   errorMsg = '服务分类不能为空';
        // }

        if(!errorMsg && !this.submitData.remark){
          errorMsg = '备注信息不能为空';
        }
        if((this.submitData.service_type =='WLBJAZ' ||this.submitData.service_type =='KDBJAZ')&&this.submitData.commitment_time == ''){
          errorMsg = '服务分类为快递/物流补件安装时，承诺上门时间不能为空';
        }

        if(this.returnGoods.some((item)=> item.returns_type == 'CHANGE' && !item.change_goods_type)){
          errorMsg = '快递返货，退货方式=换货，换货商品必填';
        }

        if(errorMsg){
          this.$message.error(errorMsg);
          this.setStatus();
          return;
        }


        //转换头部地址信息
        self.getAddressloocal();//
        //校验收货地址
        if(!self.validateIsPass()){
          self.$message.error("地址信息不对");
          this.setStatus();
          return;
        }

         //重置提示信息
         self.successmsg="";
         self.errormsg="";
         self.saveNum=0;
         this.totalSave = 0;
        self.saving=true;
        self.isSave=true;
        self.canEditAddress = false
        //删除已不存在方案明细的方案
        this.delPlanByQuestionSubId();

        //获取所有方案
        let list = this.getSchemeList();
        if(!list) return;
        for(var key in list){
          this.totalSave++;
          this.saveEvent(list[key],callback);

        }

      },
      //得到删除方案的参数
      getdelPlandata(id){
          var self=this;
          var postdata={};
        for(var i=0;i<self.afterGroupLocal.length;i++){
          if(self.afterGroupLocal[i].after_plan_id==id){
            postdata.after_order_id=self.afterGroupLocal[i].after_order_id;
            postdata.after_plan_id=self.afterGroupLocal[i].after_plan_id;
            postdata.after_plan_no=self.afterGroupLocal[i].after_plan_no;
            postdata.after_plan_type=self.afterGroupLocal[i].after_plan_type;
            break;
          }
        }
        return postdata;
      },
      //删除方案
      delPlan(id,callback){

        this.ajax.postStream('/afterSale-web/api/aftersale/order/plan/delete', this.getdelPlandata(id), res => {
          if(res.body.result){
            this.successmsg+=this.getdelPlandata(id).after_plan_no+"-删除成功-"+res.body.msg+";";
            this.saveNum++;
            this.showTips(callback);
            if(id==this.detailPostData.id){
              this.detailPostData.id="";
              this.detailPostData.after_plan_group_id="";
            }
          }else {
            this.errormsg+=this.getdelPlandata(id).after_plan_no+"-删除失败-"+res.body.msg+";";
            this.saveNum++;
            this.showTips(callback);
          }
        })
      },
      //保存/api/aftersale/plan/repair/saveUpdate
      saveEvent(postdata,callback){
        var self=this;
        postdata.encryption_add_id = this.encryption_add_id
        postdata.bearCostsList = this.selectFeeList || [] // 客户承担费用
        self.ajax.postStream("/afterSale-web/api/aftersale/plan/repair/saveUpdate",postdata,function(res){
          if(res.body.result){
            if(self.isEditPlan){
              self.successmsg+=self.getdelPlandata(postdata.id).after_plan_no+"-编辑成功-"+res.body.msg+";";
            }else{
              self.successmsg+=postdata.questionSubVO.question_description+"-保存成功-"+res.body.msg+";";
            }

            if(!self.detailPostData.id){
              self.detailPostData.id=postdata.id||res.body.content;
              self.detailPostData.after_plan_group_id=postdata.after_plan_group_id;
            }
          }else{
            if(self.isEditPlan && postdata.id){
              self.errormsg+=self.getdelPlandata(postdata.id).after_plan_no+"-编辑失败-"+res.body.msg+";";
            }else{
              self.errormsg+=postdata.questionSubVO.question_description+"-保存失败-"+res.body.msg+";";
            }

          }
          self.saveNum++;
          self.showTips(callback);
        },e=>{
          if(self.isEditPlan && postdata.id){
            self.errormsg+=self.getdelPlandata(postdata.id).after_plan_no+"-编辑失败-"+e.body.msg+";";
          }else{
            self.errormsg+=postdata.questionSubVO.question_description+"-保存失败-"+e.body.msg+";";
          }
          self.saveNum++;
          self.showTips(callback);
        })
      },
      //提示保存信息
      showTips(callback){

          var self=this;

        console.log("保存后参数：==="+self.saveNum+"===="+self.selectedData.length)
          if(self.saveNum==this.totalSave){

            if(self.successmsg)self.$message.success(self.successmsg);

            setTimeout(function(){
            if(self.errormsg) {
              self.saving=false;
              self.$message.error(self.errormsg);
            }

            },1000)

            if(!self.successmsg){
              //所有的接口都失败，则不执行callback
              self.setStatus();
              return;
            }
            callback && callback();

            self.saving=false;
            self.isSave=false;

            self.getDetailInfo(self.detailPostData.id,self.detailPostData.after_plan_group_id)
            self.getQuestionsByAfterPlanGroupId(self.detailPostData.after_plan_group_id);
            self.isEditPlan=true;

         }
      },
      /**
      *重新设置明细行的question_sub_id
      *map{after_plan_id:question}
      ***/
      setQuestionSubId(schameList,goodsList){
        if(!schameList || !schameList.length) return;
        var map = {};
        let a = schameList.length;
        for(var b = 0;b <a;b++){
          let val = schameList[b];
          for(var key in val){
            map[key] = val[key];
          }
        }
        goodsList.map((gg,ff)=>{
          let after_plan_id = gg.after_plan_id;
          if(map[after_plan_id]){
            gg.question_sub_id = map[after_plan_id].id;
            gg.parentQuestionId = map[after_plan_id].parent_question_id;
            //把最初挂方案的ID拿过来
            gg.originalSubQuestionId = map[after_plan_id].id;
          }
        });
      },
      setQuestionListBySoutions(list){
        if(!list || !list.length) return;
        var questions = [];
        list.map((q,b)=>{

          for(var key in q){
            let qq = q[key];
            let obj = {
              source_id : qq.source_id,
              question_description : qq.question_description,
              parent_question_id : qq.parent_question_id,
              question_sub_id : qq.id,
              after_plan_id:key
            }
            if(qq.id == this.submitData.question_sub_id){
              obj.isCurrentSolution  = true;
            }
            questions.push(obj);
          }

        });
        this.$emit('reSetQuestionListFn',{questions:questions,canEditQuestion:!this.save});
      },

      //查询单个信息
      getDetailInfo(id,after_plan_group_id){
        var self=this;
        if(!id)return;
        var data={id:id,after_plan_group_id:after_plan_group_id};

        self.ajax.postStream("/afterSale-web/api/aftersale/plan/repair/getPlan",data,(response)=>{
          console.log(response.body.content);
          if(response.body.result){
            self.encryption_add_id=response.body.content.address_id||'';//添加解密id
            self.submitData=response.body.content;
            self.selectFeeList = self.submitData?.bearCostsList?.length > 0 ? self.submitData.bearCostsList : [] // 客户承担费用
            var _mark_service_type = response.body.content.service_type
            setTimeout(() => {
              self.submitData.service_type = _mark_service_type//设置异步的原因是：初始化时el-select的change事件触发了this.selectParent导致service_type值清空
            })
            self.submitData.service_fee=self.submitData.service_fee?Number(self.submitData.service_fee).toFixed(2):'';
            self.submitData.customer_fee=self.submitData.customer_fee?Number(self.submitData.customer_fee).toFixed(2):'';
            self.setStatus();

            //赋值商品信息
            self.setQuestionSubId( response.body.content.map, response.body.content.repairGoods || []);
            this.setQuestionListBySoutions(response.body.content.map);

            self.returnGoods=response.body.content.repairGoods;
            //如果是编辑的话，则需要重新拿数据去填充附件的参数
           // 赋值地址
            self.setAddressloocal();
            self.old_repairinfo=JSON.stringify(self.submitData);
            self.old_repariGoodsinfo=JSON.stringify(self.returnGoods)
          }
        },e=>{
          self.isExist=false;
          self.$message.error(e)
        })
      },
      //转换得到地址信息
      setAddressloocal(){
        this.info.province = this.submitData.province
        this.info.city = this.submitData.city
        this.info.area = this.submitData.area
        this.info.street = this.submitData.street
        this.info.receiver_addr = this.submitData.receiver_addr
        this.info.receiver_name = this.submitData.receiver_name
        this.info.reveiver_phone = this.submitData.reveiver_phone
        this.info.address_id = this.submitData.address_id
        this.info.street_name = this.submitData.street_name
        this.info.city_name = this.submitData.city_name
        this.info.province_name = this.submitData.province_name
        this.info.area_name = this.submitData.area_name

      },

      /**
       *初始化图片按钮
       **/
      setStatus(){
        let canEditSolutions = this.otherInfo.canEditSolutions;
        if(!canEditSolutions){
           this.isSave = this.isSubmit = this.isWithdrawed = true;
           this.saving = false;
           return;
        }
        let status = this.submitData.status;
        if(!status){
          this.isSave = false
        }else if(status == 'SAVE' || status == 'RETRACT'){
          this.isSave = this.isSubmit = false;
          this.isWithdrawed=true;
        }else if(status == 'SUBMIT'){
          this.isSave = this.isSubmit = true;
           this.isWithdrawed = false;
        }
      },
      setOrthinfoTosubmit(){
        var self=this;
        if(self.otherInfo){
          self.submitData.after_order_id=self.otherInfo.after_order_id?self.otherInfo.after_order_id:'';
          self.submitData.after_order_no=self.otherInfo.after_order_no?self.otherInfo.after_order_no:'';
          self.submitData.merge_trade_id=self.otherInfo.merge_trade_id?self.otherInfo.merge_trade_id:'';
          self.submitData.merge_trade_no=self.otherInfo.merge_trade_no?self.otherInfo.merge_trade_no:'';
          self.submitData.id=self.otherInfo.after_plan_id?self.otherInfo.after_plan_id:'';
          self.submitData.after_plan_group_id=self.otherInfo.after_plan_group_id?self.otherInfo.after_plan_group_id:'';
          self.submitData.question_sub_id=self.otherInfo.after_question_id?self.otherInfo.after_question_id:'';

        }
        if(self.otherInfo.after_plan_id){
          self.isEditPlan=true;
        }

      },
      /***
      *提交事件
      ***/
      submitEvent(){
        let self = this;
						new Promise((resolve,reject)=>{
						self.getDecryption(resolve,reject);
						}).then(res=>{
							if(this.isSubmit) return;
              this.isSubmit = this.isSave = this.isWithdrawed = true;
              if(!this.submitData.id){
                this.$message.error('请先保存方案再进行提交');
                return;
              }
              if(!this.submitData.after_order_id){
                this.$message.error("参数错误");
                  return ;
              }
              if(!this.isWithdrawed) return;//单据状态是已提交的状态，不能做比较
              this.closeParentTab({callback:()=>{
                this.submit();
              },text:'有数据改动,提交之前是否需要保存'})
						}).catch(res=>{
							self.$message.error('该地址无法解密， 请手工增加新地址或者选择地址')
						})

      },

      // 提交
      submit() {
        var dataList={after_order_id:this.submitData.after_order_id,after_order_no:this.submitData.after_order_no};
        this.ajax.postStream('/afterSale-web/api/aftersale/plan/repair/submit',dataList,(response)=>{
            this.$message({message:response.body.result?'操作成功':response.body.msg ,type:response.body.result?'success':'error'})
            if(response.body.result){
              this.getDetailInfo(this.detailPostData.id,this.detailPostData.after_plan_group_id)
              this.canEditAddress = false
            }else{
              this.isSubmit = this.isSave = false;
            }
          },e=>{
          this.isSubmit = this.isSave = false;
            this.$message.error(e)
          })
      },
      // 撤回
      withdraw () {
        if(this.isWithdrawed) return;
        this.isSave = this.isSubmit = this.isWithdrawed = true;
        var self = this;
        if(!self.submitData.id || self.submitData.id==''){
          return;
        }
        var dataList={after_order_id:self.submitData.after_order_id};
        if(!dataList.after_order_id){
          self.$message.error("参数错误");
          return ;
        }else{
          self.ajax.postStream('/afterSale-web/api/aftersale/plan/repair/withdraw',dataList,(response)=>{
            if(response.body.result){
              self.$message({message: '操作成功',type: 'success'})
              self.getDetailInfo(self.detailPostData.id,self.detailPostData.after_plan_group_id)

            }else{
              self.$message.error(response.body.msg)
              this.setStatus();
            }
          },e=>{
            self.$message.error(e);
            this.setStatus();
          })
        }
      },
      //获取上传图片的父问题id
      getFilePid(sid){
          var pid=null;
          for(var i=0;i<this.selectedData.length;i++){
              if(sid==this.selectedData[i].question_sub_id){
                pid=this.selectedData[i].parent_question_id;
              }
          }
          return pid;
      },
      //上传图片
      uploadFun() {
        if(!this.selectsGoodsData || this.selectsGoodsData.length != 1){
          this.$message.error('只能选择一个明细行操作');
          return;
        }
        var pid=this.getFilePid(this.selectsGoodsData[0].question_sub_id);
        //TODO,明细行里面的父问题ID
        this.ifClickUpload = true;
        this.uploadData = {
          parent_name: 'AFTER_ORDER',
          parent_no: this.submitData.after_order_no,//售后单号
          child_name: 'QUESTION_GOODS',
          child_no: pid,//父问题id,必须是String
          content: this.selectsGoodsData[0],
        }
        console.log('this.uploadData',this.uploadData);
        //重置，防止第二次不能触发
        setTimeout(() => {
          this.ifClickUpload = false;
        },100)

      },
      /**
       *查看或下载附件
       **/
      pictureFun() {
        var params = {
          parent_no : this.submitData.after_order_no,//售后单号
          child_no:null,
          ext_data : null,
          notNeedDelBtn:this.isSubmit,
          parent_name : 'AFTER_ORDER',
          child_name : 'QUESTION_GOODS',
          parent_name_txt: '售后单',
          child_name_txt: '问题商品id',
          nickname: this.otherInfo.form.buyer_nick,
          mergeTradeId: this.submitData.merge_trade_id,
        };
        params.callback = d=>{
        }
        this.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
          style:'width:1000px;height:600px',
          title:'下载列表'
        });
      },
      /**
       *新增方案的时候根据merge_material_id改变收货地址
       **/
      changeAddressInfoByMaterialId(newQuestionList){

        if(!newQuestionList || !newQuestionList.length) return;
        //需要有问题商品
         console.log('看看地址的东西');
         let d;
         for(var i = 0; i < newQuestionList.length;i++){
          if(newQuestionList[i].if_goods_question == 'Y'){
            d = JSON.parse(JSON.stringify(newQuestionList[i]));
            break;
          }
         }
        !d?d = JSON.parse(JSON.stringify(newQuestionList[0])):d;
        if(!this.questionData || !this.questionData.length){
            this.questionData = [d];
            this.getAddressInfoByMergeMaterialId();
            return;
        }
        //改为parentId去判断
        var id = this.questionData[0].parent_question_id;
        var i = newQuestionList.length,a = 0;
        for(;a < i; a++){
          let f = newQuestionList[a];
          if(f.parent_question_id == id) return;
        }

        this.questionData=[];
        this.questionData.push(d);
        this.getAddressInfoByMergeMaterialId();

      },
      /**
      *获取选中的第一个问题商品
      **/
      getGoodsQuestionBySelectQuestion(){
        let d,i =0;
        if(!this.selectedData || !this.selectedData.length) return '';

        for(;i < this.selectedData.length;i++){
          let data = this.selectedData[i];
          if(data.if_goods_question == 'Y'){
            d = JSON.parse(JSON.stringify(data));
            break;
          }
        }
        !d?d=JSON.parse(JSON.stringify(this.selectedData[0])):'';
        return d;
      },
      onReturnsTypeChange(scope) {
        const index = scope.$index
       this.$set(this.returnGoods,index ,{...this.returnGoods[index], change_goods_type: ''})
      }


    },

    mounted(){
      var self=this;
      self.selectFeeList = []
      self.setOrthinfoTosubmit();
      if(!self.isEditPlan){
        //新增的时候设置方案组id
        self.getAfterPlanGroupId();
        this.questionData=[];
        let d = this.getGoodsQuestionBySelectQuestion();
        if(d){
          this.questionData.push(d);
        }

        this.getAddressInfoByMergeMaterialId(true);//第一次调用
      }else{
        self.getDetailInfo(self.submitData.id,self.submitData.after_plan_group_id);
        if(!self.detailPostData.id){
          self.detailPostData.id=self.submitData.id;
          self.detailPostData.after_plan_group_id=self.submitData.after_plan_group_id;
        }
        self.getQuestionsByAfterPlanGroupId(self.detailPostData.after_plan_group_id);

        self.isEditPlan=true;
      }
      self.old_repairinfo=JSON.stringify(self.submitData);
      self.old_repariGoodsinfo=JSON.stringify(self.returnGoods);
			this.submitData.logistics_supplier_class = !!this.selectedData.length?this.selectedData[0].logistics_supplier_class:''

      this.scmMaterielId = this.allQuestions.reduce((obj, good) => {
        obj[good.question_sub_id] = good.merge_material_id
        return obj
      }, {})
      this.changeServiceType(this.selectedData)
    }
  }
</script>
<style lang='stylus' scoped>
  .xpt-btngroup
    margin-left: 10px
  .height60
    height: 60px
</style>
