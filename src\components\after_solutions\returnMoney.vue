<!-- 退款方案 -->
<template>
<div class="xpt_pmm" ref="parentWrap">
	<el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="120px">
		<el-row :gutter="0" style="white-space: nowrap;">
			<el-col :span='4'>
				<el-form-item label="方案编码:">
					<el-input size='mini' disabled :value="form.after_plan_no" placeholder="系统自动生成"></el-input>
				</el-form-item>
			</el-col>

			<el-col :span='5'>
				<el-form-item label="日期:" prop="after_plan_date">
					<el-date-picker
					    type="date"
					    placeholder="选择日期:" size="mini"
					    v-model="form.after_plan_date"
					    :editable="false"
					    :disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions"
					>
					</el-date-picker>
					<el-tooltip v-if='rules.after_plan_date[0].isShow' class="item" effect="dark" :content="rules.after_plan_date[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-col>

			<el-col :span='4'>
				<el-form-item label="方案状态:" >
					<el-input size='mini' :value="{
						SAVE: '创建',
						SUBMIT: '提交',
						RETRACT: '撤回',
					}[form.status]" disabled style="width:120px;"></el-input>
				</el-form-item>
			</el-col>

			<el-col :span='5'>
				<el-form-item label="退款金额:" >
					<el-input size='mini' :value="form.return_amount" :disabled="true"></el-input>
				</el-form-item>
			</el-col>

			<el-col :span='5'>
				<el-form-item label="差价金额:" prop="diff_amount">
					<el-input size='mini' :value="form.diff_amount" :disabled="true"></el-input>
					<el-tooltip v-if='rules.diff_amount[0].isShow' class="item" effect="dark" :content="rules.diff_amount[0].message" placement="right-start" popper-class='xpt-form__error'>
					 	<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-col>

		</el-row>
		<el-row :gutter='0'>
			<el-col :span='24'>
				<el-form-item label="备注信息：" :class="$style['textarea-style']" prop="remark">
				    <el-input type="textarea" v-model="form.remark" :maxlength="255" :disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions"></el-input>
				    <!-- <el-tooltip v-if='rules.remark[0].isShow' class="item" effect="dark" :content="rules.remark[0].message" placement="right-start" popper-class='xpt-form__error'>
				    					 	<i class='el-icon-warning'></i>
				    					</el-tooltip> -->
				</el-form-item>
			</el-col>
		</el-row>
		<el-tabs v-model="selectMoreDetailTab">
			<el-tab-pane label='退款明细' name='refundDetailsTab' class='xpt-flex'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="24">
						<el-button
							type="primary"
							size="mini"
							@click="addLine"
							:disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions || !selectedData.length"
						>新增行</el-button>
						<el-button
							type="danger"
							size="mini"
							@click="
								delLine('refundItemList')
								refundItemListCalc()
							"
							:disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions"
						>删除行</el-button>
						<el-button
							type="primary"
							size="mini"
							@click='uploadFun'
							:disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions"
						>
							上传图片
							<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload>
						</el-button>
						<el-button type="success" size="mini" @click='pictureFun'>查看或者下载附件</el-button>
					</el-col>
				</el-row>
			
				<el-table 
					border 
					:data="form.refundItemList.length ? form.refundItemList.concat('') : form.refundItemList" 
					@selection-change="delLine" 
					class='xpt_pmm_scroll' 
					:row-class-name="(row, index) => index < form.refundItemList.length ? tableRowClassNameRepair(row, index) : $style['only-show-total']"
					@row-click="row => _selectGoodsmatchQuestions(row)"
				>
					<el-table-column  type="selection" width="55" align="center" :selectable="(row, index) => index < form.refundItemList.length"></el-table-column>
					<el-table-column label="序号" type="index" width="150">
						<template slot-scope="scope"><div class="table-index">{{ scope.$index + 1 }}</div></template>
					</el-table-column>
					<el-table-column label="退款类型">
						<template slot-scope="scope">
							<el-select :value="scope.row.refund_type" @input="$set(scope.row, 'refund_type', $event)" size="mini" :disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions" placeholder="请选择">
								<el-option label="补偿" value="COMPENSATION"></el-option>
								<el-option label="差价" value="PRICE_DIFF" :disabled="!(/(1|Y)/.test(scope.row._if_has_source) && /(1|Y)/.test(scope.row._if_goods_question))"></el-option>
								<el-option label="运费" value="CARRIAGE"></el-option>
								<el-option label="O2O跨店铺差价" value="O2O_DIFF" :disabled="!(/(1|Y)/.test(scope.row._if_has_source) && /(1|Y)/.test(scope.row._if_goods_question))"></el-option>
								<!-- <el-option label="报废/丢件" value="SCRAP_OR_LOST"></el-option> -->
								<el-option label="三包费" value="THREE"></el-option>
								<el-option label="外购折现" value="DISCOUNT"></el-option>

								<el-option label="未发取消" value="CANCEL"></el-option>
								<el-option label="延迟赔付" value="DELAY"></el-option>
								<el-option label="多付" value="OVERPAY"></el-option>
								<el-option label="优惠/返现" value="PREFERENTIAL"></el-option>
								<el-option label="退货货款" value="RETURNS" :disabled="true"></el-option>
								<!-- 先放开退货货款这一选项 -->
								<!-- <el-option label="退货货款" value="RETURNS" ></el-option> -->
								<el-option label="维修费" value="REPAIR"></el-option>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column label="退款原因" :class-name="$style['row-height'] + ' ' + $style['th-required']"  width="450">
						<template slot-scope="scope">
							<el-input
								v-model="scope.row.refund_reason"
								:maxlength="255"
								style="width:100%;"
								:disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions"
								type="textarea"
								autosize
							></el-input>
						</template>
					</el-table-column>
					<el-table-column label="退款金额">
						<template slot-scope="scope">
							<el-input
								type="number"
								min="0"
								size='mini'
								v-model="scope.row.refund_amonut"
								:disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions"
								@blur="e => onlyNumber(e, scope.row)"
							></el-input>
							<div class="calc-total" style="display:none;">合计 {{ form.return_amount }}</div>
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='差价商品' name='differenceGoods' class='xpt-flex'>
				<component
					is='differenceGoods'
					:initData="readRefundPriceDiffList"
					:params="{
						plan_time: +new Date,
						isEdit: form.status !== 'SUBMIT' && otherInfo.canEditSolutions,
						copySelectDataId: copySelectData.question_sub_id || copySelectData.originalSubQuestionId,
					}"
					@getMergeTradeNo="getMergeTradeNo2DifferenceGoods"
					@getDifferenceGoods="setDifferenceGoods"
					@selectGoodsmatchQuestions="_selectGoodsmatchQuestions"
				></component>
			</el-tab-pane>
		</el-tabs>
		<div style="margin-top:30px;">
			<el-form-item label="确认信息">
				<a href="javascript:;" @click="isEdit = (!isEdit && form.status !== 'SUBMIT' && otherInfo.canEditSolutions)">{{ isEdit ? '确认' : '修改' }}</a>
				<el-button type="primary" size="mini" @click='showPayAccount' :disabled="form.status === 'SUBMIT' || !otherInfo.canEditSolutions" style="margin-left:10px;">收款账号选择</el-button>
			</el-form-item>
			<el-form-item label="退款方式" prop="pay_method" style="display:inline-block;">
				<el-select v-model="form.pay_method" size='mini' @change="payMethodChange" :disabled="!isEdit">
					<el-option
						v-for="(val, key) in pay_method_options"
						:key="key"
						:label="val"
						:value="key"
						:disabled="false && (key === 'PROTOCOL' || key === 'DISPUTE')"
					></el-option>
				</el-select>
				<el-tooltip v-if='rules.pay_method[0].isShow' class="item" effect="dark" :content="rules.pay_method[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="收款人姓名" prop="receiver_name" style="display:inline-block;">
				<el-input v-model="form.receiver_name" size='mini' :disabled="!isEdit"></el-input>
				<el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="开户行" prop="opening_bank" style="display:inline-block;">
				<el-input v-model="form.opening_bank" size='mini' style="width:350px;" :disabled="!isEdit"></el-input>
				<el-tooltip v-if='rules.opening_bank[0].isShow' class="item" effect="dark" :content="rules.opening_bank[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item><br>
			<el-form-item label="收款账号" prop="receiver_account" style="display:inline-block;">
				<el-input v-model="form.receiver_account" size='mini' :disabled="!isEdit"></el-input>
				<el-tooltip v-if='rules.receiver_account[0].isShow' class="item" effect="dark" :content="rules.receiver_account[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="收款人电话" style="display:inline-block;" prop="receiver_phone">
				<el-input v-model="form.receiver_phone" size='mini' :disabled="!isEdit"></el-input>
				<el-tooltip v-if='rules.receiver_phone[0].isShow' class="item" effect="dark" :content="rules.receiver_phone[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="银行名称" style="display:inline-block;" prop="bank_name">
				<el-input v-model="form.bank_name" size='mini'style="width:350px;" :disabled="!isEdit"></el-input>
				<el-tooltip v-if='rules.bank_name[0].isShow' class="item" effect="dark" :content="rules.bank_name[0].message" placement="right-start" popper-class='xpt-form__error'>
				 	<i class='el-icon-warning'></i>
				</el-tooltip>
			</el-form-item>
		</div>
	</el-form>
	<div style="margin:20px 0;text-align:center">
  		<el-button type='primary' size='mini' @click="saveBtn" :disabled="!otherInfo.canEditSolutions?true:!canSave || form.status === 'SUBMIT'">保存</el-button>
  		<el-button type='primary' size='mini' @click="e => ajaxSaveSubmitRecall(e, 'submit')" disabled>提交</el-button>
  		<!-- <el-button type='primary' size='mini' @click="e => ajaxSaveSubmitRecall(e, 'recall')" :disabled="!otherInfo.canEditSolutions?true:!canRecall || !form.id || form.status !== 'SUBMIT'">撤回</el-button> -->
  		<el-button type='primary' size='mini' @click="e => ajaxSaveSubmitRecall(e, 'recall')" disabled>撤回</el-button>
  		<el-button type='primary' size='mini' :disabled="!otherInfo.canEditSolutions" @click="addNew">新增</el-button>
  	</div>
</div>
</template>

<script>
	import VL from '@common/validate.js'
	// import otherInfo from '@components/after_solutions/otherInfo.vue'
	import differenceGoods from '@components/after_sales/differenceGoods.vue'

	export default {
		// props: ['selectedData', 'otherInfo', 'questionList', 'editOtherInfo', 'editQuestion', 'operatParams'],
		props: {
	    	selectedData: {
	    		type: Array,
				default () {
					return {}
				}
	    	},
	    	otherInfo: {
	    		type: Object,
				default () {
					return {}
				}
	    	},
	    	editOtherInfo: {
	    		type: Object,
				default () {
					return {}
				}
	    	},
	    	questionList: Array,
	    	editQuestion: Object,
	    	copySelectData: Object,
	    	afterGroup: Array,
	    	allQuestions: Array,
	    	operatParams: Object,
	    },
		data (){
			// this.questionSubData = this.selectedData[0] || {}//子问题详情，比this.init(null, 'init')先初始化
			return {
				selectMoreDetailTab: 'refundDetailsTab',
				// ifSave: false,

				selectTableData: [],
				readRefundPriceDiffList: [],
				pay_method_options: {
					ALIPAY	: '支付宝退款',
					BANK	: '银行卡退款',
					PROTOCOL: '协议退款',
					DISPUTE : '纠纷退款',
					PAIPAI	: '拍拍退款',
					TENPAY	: '财付通退款',
					BAIL	: '保证金退款',
					B2C_ONLINE	: 'B2C线上退款',
					B2C_OFFLINE	: 'B2C线下退款',
				},
				/*保存时入参*/
				form: this.init(),
				// questionSubVO: {//公共组件传递
				// 	// parent_question_id: '父问题ID',
				// 	// source_id: '来源子问题ID',
				// 	// question_description: '问题描述',
				// },
				/*保存时入参*/

				rules: {
					receiver_phone: [{
						validator: (rule, value, callback) => {
							var reg = /^1\d{10}$/;
			                var reg2 = /^\d{8}$/;
			                var reg3 = /^\d*$/;//纯数据
							/*if(this.rules[rule.field][0].isShow = value && !/^1[34578]\d{9}$/.test(value)){*/
							/*if(this.rules[rule.field][0].isShow = value && !reg.test(value) && !reg2.test(value)){*/
							if(this.rules[rule.field][0].isShow = value && !reg3.test(value)){	
								callback(new Error(''))
							}else {
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请输入正确收款人电话',
					}],
					// remark: this.VLFun(true, '请填写问题描述'),
					pay_method: [{
						required: true,
						validator: (rule, value, callback) => {
							if(!value || Object.keys(this.pay_method_options).indexOf(value) === -1){
								this.rules[rule.field][0].message = !value ? '请填写退款方式' : '退款方式不符合条件'
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else {
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请填写退款方式',
					}],
					receiver_name: [{
						required: false,
						validator: (rule, value, callback) => {
							if(/(BANK|ALIPAY)/.test(this.form.pay_method) && !value || (value && /^\d+$/.test(value.trim()))){
								this.rules[rule.field][0].message = !value ? '请填写收款人姓名' : '收款人姓名不能为纯数字'
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else {
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请填写收款人姓名',
					}],
					receiver_account: [{
						required: false,
						validator: (rule, value, callback) => {
							if(/(BANK|ALIPAY)/.test(this.form.pay_method) && !value){
								this.rules[rule.field][0].message = '请填写收款账号'
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else if (this.form.pay_method === 'BANK' && !/^\d+$/.test(value)){
								this.rules[rule.field][0].message = '收款账号只能为纯数字'
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else if (this.form.pay_method === 'ALIPAY' && !(/^1\d{10}$/.test(value) || /\S+@\S+\.\S+/.test(value))){
								this.rules[rule.field][0].message = '收款账号只能为邮箱或11位手机号码'
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else {
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请填写正确收款账号',
					}],
					bank_name: [{
						required: false,
						validator: (rule, value, callback) => {
							if(this.form.pay_method === 'BANK' && !value || (value && /^\d+$/.test(value.trim()))){
								this.rules[rule.field][0].message = !value ? '请填写银行名称' : '银行名称不能为纯数字'
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else {
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请填写正确银行名称',
					}],
					opening_bank: [{
						required: false,
						validator: (rule, value, callback) => {
							if(this.form.pay_method === 'BANK' && !value){
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else {
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请填写开户行',
					}],
					diff_amount: [{
						validator: (rule, value, callback) => {
							if(value < 0){
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else {
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '差价金额合计不能为负数',
					}],
					after_plan_date: this.VLFun(true, '请填写日期'),
				},

				//上传图片
				ifClickUpload: false,
				uploadData: {
					// parent_name: 'AFTER_ORDER',
					// child_name: 'QUESTION_GOODS',
					// content: JSON.parse(JSON.stringify(this.otherData.content || {}))
					// parent_no: 'SH1710110016',
					// child_no: '',
				},
				isEdit: false,//默认是不修改
				canSave: true,
				canSubmit: true,
				canRecall: true,
			}
		},
		methods: {
			/**
		      *跳到工作台
		      ***/
		      addNew(){
		        let params = JSON.parse(JSON.stringify(this.otherInfo));
		        /*after_plan_id:null,//编辑,方案ID
		          after_question_id:null,//方案所挂的问题ID
		          after_plan_group_id:null,//方案组ID*/
		          if(params.hasOwnProperty('after_plan_id')){
		            delete params.after_plan_id;
		          }
		          if(params.hasOwnProperty('after_question_id')){
		            delete params.after_question_id;
		          }
		          if(params.hasOwnProperty('after_plan_group_id')){
		            delete params.after_plan_group_id;
		          }
		        this.$root.eventHandle.$emit('creatTab',{
		                  name: '售后方案',
		                  url: 'after_solutions/solutions',
		                  params: {
		                    afterOrderInfo: params
		                  },
		                  component: () => import('@components/after_solutions/solutions')
		              })
		      },  
			payMethodChange (){
				;[{
					name: ['receiver_name', 'receiver_account'],
					test: /(BANK|ALIPAY)/.test(this.form.pay_method),
				},{
					name: ['opening_bank', 'bank_name'],
					test: /BANK/.test(this.form.pay_method),
				}].forEach(obj => {
					obj.name.forEach(name => {
						this.rules[name][0].required = obj.test
					})
				})
			},
			init (data){
				var formData = {
					id: data ? data.id : null,//方案主键
					after_plan_no: data ? data.after_plan_no : '',//方案编码
					after_order_id: data ? data.after_order_id : this.otherInfo.after_order_id,//售后单id
					after_order_no: data ? data.after_order_no : this.otherInfo.after_order_no,//售后单号
					after_plan_group_id: data ? data.after_plan_group_id : '',
					// question_sub_id: data ? data.question_sub_id : this.questionSubData.question_sub_id,

					remark: data ? data.remark : '',
					status: data ? data.status : 'SAVE',//方案状态
					return_amount: data ? data.return_amount : 0,//退款金额
					diff_amount:  data ? data.diff_amount : 0,//差价金额
					after_plan_date:  data ? data.after_plan_date : new Date,//方案时间
					question_sub_id:data?data.question_sub_id:null,//子问题ID

					pay_method: (data || this.form || {}).pay_method,
					receiver_name: (data || this.form || {}).receiver_name,
					receiver_phone: (data || this.form || {}).receiver_phone,
					receiver_account: (data || this.form || {}).receiver_account,
					opening_bank: (data || this.form || {}).opening_bank,
					bank_name: (data || this.form || {}).bank_name,

					refundItemList: data ? data.refundItemList : [],//退款明细表格
					refundPriceDiffList: data ? data.refundPriceDiffList : [],//差价商品组件返回的新增商品信息
				}

				if(data){
					this.readRefundPriceDiffList = formData.refundPriceDiffList = (formData.refundPriceDiffList || []).map(obj => this._tranRefundPriceDiffList(obj, 'get'))
					this.form = formData
					this.goodMapQuestion('refundItemList')
					// this.goodMapQuestion('refundPriceDiffList')
				}
				this.closeParentTab(formData)//init 对比数据

				if(!data){
					this.readRefundPriceDiffList = []
					return formData
				}
			},

			//对页面需要认证的内容认证，required：是否必填，msg：提示信息
			VLFun(required, msg){
				return VL.isNotBlank({
					required:required,
					self:this,
					msg:msg,
				})
			},
			// 删除行
			delLine (selectDataOrTableName){
				if(typeof selectDataOrTableName === 'string'){
					if(!this.selectTableData.length){
						this.$message.error('请选择至少一行明细')
						return
					}

					// 删除行
					this.selectTableData.forEach(obj => {
						this.form[selectDataOrTableName].splice(this.form[selectDataOrTableName].indexOf(obj), 1)
					})
					this.goodMapQuestion('refundItemList')
				}else {
					// selection-change触发的
					this.selectTableData = selectDataOrTableName
				}

				this.$emit('allGoodsmatchQuestions', this.form.refundItemList)
			},
			//上传图片
			uploadFun() {
				if(this.selectTableData.length !== 1){
					this.$message.error('只能选择一个明细行操作')
					return
				}

				this.ifClickUpload = true
				this.uploadData = {
					parent_name: 'AFTER_ORDER',
					parent_no: this.form.after_order_no || this.otherInfo.after_order_no,
					child_name: 'QUESTION_GOODS',
					child_no: this.selectTableData[0]._parent_question_id,
					content: this.allQuestions.filter(obj => obj.parent_question_id === this.selectTableData[0]._parent_question_id)[0],
				}
				//重置，防止第二次不能触发
				setTimeout(() => {
					this.ifClickUpload = false;
				},100)
			},
			/**
			*查看或下载
			**/
			pictureFun() {
				if(this.selectTableData.length !== 1){
					this.$message.error('只能选择一个明细行操作')
					return
				}

				var params = {
					parent_no : this.form.after_order_no || this.otherInfo.after_order_no,
					child_no : null,
					ext_data : null,
					parent_name : 'AFTER_ORDER',
					child_name : 'QUESTION_GOODS',
					parent_name_txt: '售后单',
					// child_name_txt: '问题商品id',
					notNeedDelBtn: this.form.status === 'SUBMIT' || !this.otherInfo.canEditSolutions,
				};
				this.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/common/download.vue'),
					style:'width:1000px;height:600px',
					title:'下载列表'
				});
			},
			_selectGoodsmatchQuestions (row){
				this.$emit('selectGoodsmatchQuestions',[row])
			},
			/**
		      *设置问题对应的方案
			***/

			tableRowClassNameRepair(row, rowIndex) {
		        console.log('asdkfjlksdf');
		        if(!this.copySelectData) return '';
		        console.log('this.copySelectData',this.copySelectData);
		        let id = this.copySelectData.question_sub_id || this.copySelectData.originalSubQuestionId;
		        let question_sub_id = row.question_sub_id;
		        if(question_sub_id == id || (!row.id && row.parentQuestionId == this.copySelectData.parent_question_id)) return 'pmm_blue22';
		        return '';
		      },

			// tableRowClassNameRepair(row, rowIndex) {
			// 	console.log('asdkfjlksdf');
			// 	 let id = this.copySelectData.question_sub_id || this.copySelectData.originalSubQuestionId;
			// 	if(!this.copySelectData || !id) return '';

			// 	let question_sub_id = row.question_sub_id;

			// 	console.log('this.copySelectData',this.copySelectData);
			// 	console.log('this.question_sub_id',question_sub_id);

			// 	if(question_sub_id == id) return 'pmm_blue22';
			// 	return '';
			// },
			_tranRefundPriceDiffList (data, type){
				var returnData = {}
				,	forEachCallback = type === 'post' ? key => returnData[key] = data[mapObj[key]] : key => returnData[mapObj[key]] = data[key]
				,	mapObj  = {
					id: 'id',
					after_plan_id: 'after_plan_id',

					price_area: 'price_list_name',
					shop: 'price_shop_name',
					new_goods_code: 'price_material_no',
					new_goods_name: 'price_material_name',
					new_specification: 'price_material_desc',
					new_price: 'act_price',
					old_goods_code: 'material_number',
					old_goods_name: 'material_name',
					old_specification: 'material_desc',
					old_price: 'buying_price',
					order_no: 'tid',
					diff_price: 'price_spread',
					valid_date: 'enable_time',
					units: 'unit_name',
					disabled_date: 'disable_time',
					
					new_material_id: 'material_id',
					order_status: 'order_status',
					buy_time: 'buy_time',
				}

				if(type === 'get') {
					mapObj._question_sub_id = '_question_sub_id'
					mapObj.question_sub_id = 'question_sub_id'
				}
				
				Object.keys(mapObj).forEach(forEachCallback)
				return returnData
			},
			// 先判断后返回合并订单号
			getMergeTradeNo2DifferenceGoods (cb){
				if(this.selectedData.length === 1){
					cb(this.selectedData[0].merge_trade_no)
				}else {
					this.$message.error('请勾选一个子问题')
				}
			},
			/***
			*验证明细行数据
			***/
			validateIsPass(){
				let bool;
				this.$refs.form.validate(valid => {
					bool = valid;
				})
				if(!bool) return false;
				//refundItemList == T退款明细
				//refundPriceDiffList == 差价商品
				let errorMsg;
				if(
					this.allQuestions.some((question, index) => {
						var isHasPriceDiffItem = (question._refundItemList || []).some(obj => /(PRICE_DIFF|O2O_DIFF)/.test(obj.refund_type))

						if(isHasPriceDiffItem && !(question._refundPriceDiffList || []).length){
							errorMsg = '请为序号' + (index + 1) + '子问题新增一个差价商品'
							return true
						}else if ((question._refundPriceDiffList || []).length && !isHasPriceDiffItem){
							errorMsg = '请为序号' + (index + 1) + '子问题新增一个差价类型'
							return true
						}
					})
				){
				}else {
					if(!this.form.refundItemList.length) errorMsg = '退款明细不能空'
					if(!errorMsg){
						this.form.refundItemList.some(obj => {
							if(!obj.refund_type || !obj.refund_amonut || !obj.refund_reason.trim()){
								errorMsg = '退款类型，退款原因、退款金额不能为空'
								return true
							}else if(obj.refund_amonut <= 0){
								errorMsg = '退款金额必须大于0'
								return true
							}
						})
					}
				}
				if(errorMsg){
					this.$message.error(errorMsg);
					bool = !errorMsg;
				}
				return bool;

			},
			saveBtn (e){
				var $btn = e && e.currentTarget
				console.log('asdfkjasdklfj');
				var bool = this.validateIsPass();
				if(!bool && this.form.after_plan_date) {
					this.isEdit = true;

				}
				if(!bool) return;
				if($btn){
					this.canSave = false
				}
				this.getAfterPlanGroupId(() => {
					this.ajaxSaveSubmitRecall(null, 'save', res => {

						setTimeout(() => {
							this.$message.success('保存成功')
							this.getPlanAndRender(this.form.after_plan_group_id, () => {
								if($btn){
									this.canSave = true
								}
							})
						}, 300)
					}, () => {
						if($btn){
							this.canSave = true
						}
					})
				})

				/*this.$refs.form.validate(valid => {
					if(valid){
						var errorMsg = ''

						if(this.selectedData.length > 1) {
							errorMsg = '只能选择一个子问题'
						}else if(this.form.refundItemList.some(obj => /PRICE_DIFF/.test(obj.refund_type))){
							if(!this.form.refundPriceDiffList.length) errorMsg = '请新增一个差价商品'
						}else if (!this.questionSubData.parent_question_id){
							errorMsg = '请选择一个子问题商品'
						}else {
							if(!this.form.refundItemList.length) errorMsg = '退款明细不能空'
							else if(this.form.refundPriceDiffList.length) errorMsg = '请在退款明细新增一个差价类型'
							else if(this.form.refundItemList.some(obj => {
								if(!obj.refund_type || !obj.refund_amonut){
									errorMsg = '退款类型、退款金额不能为空'
									return true
								}else if(!(obj.refund_amonut > 0)){
									errorMsg = '退款金额必须大于0'
									return true
								}
							})){}
							else {

							}
						}

						if(errorMsg) {
							this.$message.error(errorMsg)
							return
						}

						if($btn){
							$btn.disabled = true
						}

						this.getAfterPlanNo(() => {
							this.getAfterPlanGroupId(() => {
								this.ajaxSaveSubmitRecall(null, 'save', res => {

									setTimeout(() => {
										this.$message.success('保存成功')
										this.getPlanAndRender(res.body.content, () => {
											if($btn){
												$btn.disabled = false
											}
										})
									}, 300)
								}, () => {
									if($btn){
										$btn.disabled = false
									}
								})
							})
						})
					}else if (this.form.remark && this.form.after_plan_date){
						this.isEdit = true
					}
				})*/
			},
			/**
			*api = submit 为保存
			**/
			ajaxSaveSubmitRecall (e, apiName, cb, errCb){
				//提交的时候需要验证数据
				if(apiName == 'submit'){
					var isPass = this.validateIsPass();
					if(!isPass) return;
				}
				var $btn = e && e.currentTarget
				,	loopCount = 0
				,	ajaxCount = 0

				if($btn) this[apiName === 'submit' ? 'canSubmit': 'canRecall'] = false

				this.allQuestions.forEach(questionObj => {
					this.getAfterPlanNo(questionObj, () => {
						if(apiName !== 'save' && loopCount) return//提交和撤回只请求一次

						if(questionObj._after_plan_no){
							++loopCount

							var postData = Object.assign({
								questionSubVO: {
									id: questionObj.question_sub_id,
									parent_question_id: questionObj.parent_question_id,
									source_id: questionObj.question_sub_id,
									question_description: questionObj.question_description,
									if_goods_question: questionObj.if_goods_question,
								}
							}, this.form, {
								id: questionObj._id,
								after_plan_no: questionObj._after_plan_no,
								refundItemList: questionObj._refundItemList.map(obj => {
									return {
										refund_amonut: obj.refund_amonut,
										refund_reason: obj.refund_reason,
										refund_type: obj.refund_type,
									}
								}),
								refundPriceDiffList: (questionObj._refundPriceDiffList || []).map(obj => this._tranRefundPriceDiffList(obj, 'post')),
							})

							console.log('postData123',postData)
							// return

							this.ajax.postStream('/afterSale-web/api/aftersale/plan/refund/' + apiName, postData, res => {
								++ajaxCount
								if(loopCount !== ajaxCount) return

								if(res.body.result){
									if(apiName === 'save'){
										cb(res);
										/*if(!this.form.id){
											this.$emit('resetAllData',this.form.after_plan_group_id);	
										}*/
									}else {
										if(apiName === 'submit'){
											this.form.status = 'SUBMIT'
											this.isEdit = false
										}else {
											this.form.status = 'RETRACT'
										}

										this.$message.success(res.body.msg)
									}
								}else {
									this.$message.error(res.body.msg)
									!this.form.id && (this.form.after_plan_no = '')
								}
								if($btn) this[apiName === 'submit' ? 'canSubmit': 'canRecall'] = true
							}, () => {
								++ajaxCount
								errCb && errCb()
								if($btn) this[apiName === 'submit' ? 'canSubmit': 'canRecall'] = true
							})
						}
					})
				})
			},
			// 新增行
			addLine (){
				if(this.selectedData.length === 1){
					this.form.refundItemList.push({
						_parent_question_id: this.selectedData[0].parent_question_id,
						parentQuestionId: this.selectedData[0].parent_question_id,
						_question_sub_id: this.selectedData[0].question_sub_id,
						question_sub_id: this.selectedData[0].question_sub_id,
						_if_has_source: this.selectedData[0].if_has_source,
						_if_goods_question: this.selectedData[0].if_goods_question,
					})
					this.goodMapQuestion('refundItemList')
				}else {
					this.$root.eventHandle.$emit('alert',{
						component:()=>import('@components/after_solutions/returnMoney_refundItemList'),
						title:'均分退款金额',
						style:'width:800px;height:300px',
						params: {
							actPriceList: this.selectedData.map(obj => obj.act_price),
							callback: d => {
								// 读取详情时，this.selectedData是否有数据？？？？？？
								console.log('d123123',d)
								d.forEach((item, index) => {
									this.form.refundItemList.push({
										_parent_question_id: this.selectedData[index].parent_question_id,
										parentQuestionId: this.selectedData[index].parent_question_id,
										_question_sub_id: this.selectedData[index].question_sub_id,
										question_sub_id: this.selectedData[index].question_sub_id,
										_if_has_source: this.selectedData[index].if_has_source,
										_if_goods_question: this.selectedData[index].if_goods_question,
										refund_type: item.refund_type,
										refund_reason: item.refund_reason,
										refund_amonut: item.refund_amonut,
									})
								})
								this.goodMapQuestion('refundItemList')
								this.refundItemListCalc()
							},
						},
					})
				}
			},
			// 添加差价商品
			setDifferenceGoods (listData, diff_amount){
				listData.forEach(obj => {
					if(!obj._question_sub_id) {
						obj.question_sub_id = obj._question_sub_id = this.selectedData[0].question_sub_id
					}
				})

				this.form.refundPriceDiffList = listData
				this.form.diff_amount = diff_amount
				this.goodMapQuestion('refundPriceDiffList')
			},
			// 商品(包括差价商品)匹配问题
			goodMapQuestion (formKeyName){
				var question_sub_id_obj = {}
				this.allQuestions.forEach(obj => {
					question_sub_id_obj[obj.question_sub_id] = obj
					obj['_' + formKeyName] = []
				})//init

				this.form[formKeyName].forEach(item => {
					question_sub_id_obj[item._question_sub_id]['_' + formKeyName].push(item)
				})
			},
			// 退款明细合计
			refundItemListCalc (){
				this.form.return_amount = this.form.refundItemList.reduce((a,b) => a + Number(b.refund_amonut||0), 0).toFixed(2)
			},
			// _setOldQuestion (oldQuestion){
			// 	var returnData
			// 	this.myAllQuestions.some(obj => {
			// 		if(obj.parent_question_id === oldQuestion.questionSubVO.parent_question_id){
			// 			returnData = {
			// 				...obj,
			// 				question_sub_id: oldQuestion.question_sub_id,
			// 				question_description: oldQuestion.questionSubVO.question_description,
			// 				if_goods_question: oldQuestion.questionSubVO.if_goods_question,
			// 			}
			// 			this.myAllQuestions.push(returnData)
			// 			return true
			// 		}
			// 	})

			// 	return returnData
			// },
			/**
			*设置已挂方案的问题列表
			***/
			setQuestionListBySoutions(list, cb){
				if(!list || !list.length) return;
				var questions = [];
				list.map((a,b)=>{
					let q = a.questionSubVO;
					let obj = {
						source_id : q.source_id,
						question_description : q.question_description,
						parent_question_id : q.parent_question_id,
						question_sub_id : q.id,
						//after_plan_no:a.after_plan_no,
						after_plan_id:a.id
					}
					if(b === 0){
						obj.isCurrentSolution  = true;
					}
					questions.push(obj);
				});
				this.$emit('reSetQuestionListFn',{questions:questions,canEditQuestion:this.canSave || form.status !== 'SUBMIT'}, cb);
			},
			// 获取方案详情
			getPlanAndRender (id, cb){
				this.ajax.postStream('/afterSale-web/api/aftersale/plan/refund/getPlan', { after_plan_group_id: id }, res => {
					cb && cb(res)
					if(res.body.result){
						this.$message.success('加载方案成功')
						
						this.setQuestionListBySoutions(res.body.content || [], () => {
							var firstPlanObj = JSON.parse(JSON.stringify(res.body.content[0]))
							,	question_sub_id_obj = this.allQuestions.reduce((obj, data) => {
								obj[data.question_sub_id] = data
								return obj
							}, {})

							firstPlanObj.refundItemList = []
							firstPlanObj.refundPriceDiffList = []

							res.body.content.forEach(obj => {
								// if(!question_sub_id_obj[obj.question_sub_id]) {
								// 	question_sub_id_obj[obj.question_sub_id] = this._setOldQuestion(obj)
								// }

								question_sub_id_obj[obj.question_sub_id]._after_plan_no = obj.after_plan_no
								question_sub_id_obj[obj.question_sub_id]._id = obj.id
								;(obj.refundItemList || []).forEach(item => {
									Object.assign(item, {
										_parent_question_id: obj.questionSubVO.parent_question_id,
										parentQuestionId: obj.questionSubVO.parent_question_id,
										_question_sub_id: obj.question_sub_id,
										question_sub_id: obj.question_sub_id,
										_if_has_source: question_sub_id_obj[obj.question_sub_id].if_has_source,
										_if_goods_question: obj.questionSubVO.if_goods_question,
									})
									firstPlanObj.refundItemList.push(item)
								})
								;(obj.refundPriceDiffList || []).forEach(item => {
									Object.assign(item, {
										_question_sub_id: obj.question_sub_id,
										question_sub_id: obj.question_sub_id,
									})
									firstPlanObj.refundPriceDiffList.push(item)
								})
							})

							console.log('firstPlanObj123',firstPlanObj)
							this.init(firstPlanObj)
							if(this.isEdit && this.form.status === 'SUBMIT'){
								this.isEdit = false
							}
						});
						
					}else {
						this.$message.error('加载方案失败')
					}
				})
			},
			// 获取收款人信息
			getReceiver (id){
				if(id && id !== this.getReceiver.oldId){
					this.getReceiver.oldId = id
					this.ajax.postStream('/afterSale-web/api/aftersale/plan/refund/getReceiver', { materialId: id }, res => {
						if(res.body.result){
							res = res.body.content

							if(!res) return
							this.form.pay_method = res.pay_method
							this.form.receiver_name = res.receiver_name
							this.form.receiver_phone = res.receiver_phone
							this.form.receiver_account = res.receiver_account
							this.form.opening_bank = res.opening_bank
							this.closeParentTab(this.form)
						}else {
							this.$message.error('读取收款人信息失败')
						}
					})
				}
			},
			//获取方案组id
			getAfterPlanGroupId (cb){
				if(this.form.after_plan_group_id){
					cb()
				}else {
					this.ajax.postStream('/afterSale-web/api/aftersale/plan/getPlanGroupId', {}, d => {
						if(d.body.result){
							this.form.after_plan_group_id = d.body.content
							cb()
						}
					})
				}
			},
			/**
			*获取方案编码
			**/
			getAfterPlanNo (questionObj, cb){
				if(!questionObj._after_plan_no && questionObj._refundItemList && questionObj._refundItemList.length){
					// 新增方案
					let url = '/afterSale-web/api/aftersale/plan/getNos';
					let data = {
						number_count:1,
						number_type:'TKFA'
					}
					this.ajax.postStream(url, data, res => {
						let d = res.body;
						if(!d.result){
							this.$message.error(d.msg);
							return;
						}
						questionObj._after_plan_no = /*this.uploadData.child_no =*/ d.content[0];
						cb()
						// console.log('this.form.after_plan_no',this.form.after_plan_no)
						// this.otherData.canUploadImg = true;
					});
				}else {
					cb()
				}
			},
			/**
			*关闭标签页面
			*/
			closeParentTab (oldData, isSwitchOrCloseTab){
				//TODO,还少了一个判断
				if(oldData){
					this.closeParentTab.oldData = JSON.parse(JSON.stringify(oldData))
					return
				}

				var tabName = this.operatParams.params.tabName
				,	formData = JSON.parse(JSON.stringify(this.form))

				delete formData.status
				delete this.closeParentTab.oldData.status

				var isDiff = this.compareData(formData, this.closeParentTab.oldData)
				,	_removeTab = () => isSwitchOrCloseTab === 'switchTab' ? this.$emit("changeSolution") : this.$root.eventHandle.$emit('removeTab', tabName)

				if(isDiff){
					this.$root.eventHandle.$emit('openDialog', {
						ok: () => {
							this.saveBtn()
						},
						no: _removeTab,
					})
				}else {
					_removeTab()
				}
			},
			//TODO,关闭保存的提醒
			switchTab(){
				this.closeParentTab(null, 'switchTab')
				// this.$emit("changeSolution");
			},
			
			// 收款账号弹框选择
			showPayAccount (){
				if(!this.selectedData.length){
					this.$message.error('请勾选一个子问题')
				}else {
					this.$root.eventHandle.$emit('alert',{
						component:()=>import('@components/after_solutions/selectPayAccounts'),
						style:'width:1000px;height:600px',
						title:'收款账号列表',
						params: {
							cust_id: this.selectedData[0].buyer,
							callback: d => {
								this.form.pay_method = d.pay_channel
								this.form.receiver_name = d.bank_user_name
								this.form.receiver_phone = d.receiver_phone
								this.form.receiver_account = d.pay_account//选择列表的支付账户而不是收款账号
								this.form.opening_bank = d.bank
							},
						},
					})
				}
			},
			onlyNumber (e, row){
				row.refund_amonut = e.target.value = e.target.value > 0 ? Number(Number(e.target.value).toFixed(2)) : 0
				this.refundItemListCalc()
			},
		},
		components: {
			differenceGoods,
		},

		mounted (){
			// console.log('afterGroup123123',this.afterGroup,this.selectedData,this.allQuestions)
			// if(this.editOtherInfo.id){
			// 	this.getPlanAndRender(this.editOtherInfo.id)
			// }

			this.selectedData[0] && this.getReceiver(this.selectedData[0].merge_material_id)
			if(this.otherInfo.after_plan_group_id){
				this.getPlanAndRender(this.otherInfo.after_plan_group_id)
			}
		},
		watch: {
			// selectedData (val, old){
			// 	console.log('selectedData121',val)
			// 	// this.questionSubData = val[0] || {}
			// 	// if(val[0].parent_question_id !== old[0].parent_question_id){
			// 	// 	this.form = this.init()
			// 	// }
			// 	// this.getReceiver(val[0].merge_material_id)
			// },
			// questionList (val){
			// 	console.log('questionList121',val)
			// 	// this.questionSubData = val[0] || {}
			// },
			// 编辑入口的子问题描述修改
			// editQuestion (val,old){
			// 	Object.assign(this.questionSubData, {
			// 		// parent_question_id: val.parent_question_id,
			// 		source_id: val.question_sub_id,
			// 		question_description: val.question_description,
			// 		// if_goods_question: val.if_goods_question,
			// 	})
			// },
			//切换的提醒
			'operatParams.isSwitchTab':function(newVal,oldVal){
				
				this.switchTab();
			},
			'operatParams.bool': function (val){
				this.closeParentTab()
			},
			// afterGroup (val){
			// 	console.log('val123',val)
			// 	return
			// 	this.getPlanAndRender(val[0].after_plan_id)
			// 	// console.log('1231235',val)
			// },
		},
	}
</script>

<style module>
.textarea-style :global(.el-form-item__content) {
	width: 80%;
    height: auto;
    margin-top: 10px;
    white-space: nowrap;
}
.row-height :global(.cell) {
	height: auto!important;
}
.only-show-total :global(.el-checkbox),
.only-show-total :global(.el-select),
.only-show-total :global(.el-input),
.only-show-total :global(.el-textarea),
.only-show-total :global(.table-index) {
	display: none;
}
.only-show-total :global(.calc-total) {
	display: block!important;
}
th.th-required :global(.cell:before) {
	content: '*';
	color: red;
}
</style>