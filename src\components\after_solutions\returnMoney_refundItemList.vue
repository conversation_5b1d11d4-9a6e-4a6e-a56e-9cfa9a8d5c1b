<!-- 退款方案-均分金额 -->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-col :span="6">
			<el-button
				type="primary"
				size="mini"
				@click="submit"
			>确定</el-button>
		</el-col>
		<el-col :span="18">
			<div style="float:right;">
				<el-input size='mini' v-model="inputAllMoney" type="number" @input="averageMoney" placeholder="请输入总金额"></el-input>
			</div>
		</el-col>
	</el-row>
	<el-table 
		border 
		:data="refundItemList.length ? refundItemList.concat('') : refundItemList" 
		class='xpt_pmm_scroll' 
		:row-class-name="(row, index) => index < refundItemList.length ? '' : $style['only-show-total']"
	>
		<el-table-column label="退款类型">
			<template slot-scope="scope">
				<el-select v-model="scope.row.refund_type" @input="$event => refundItemList.forEach(obj => obj.refund_type = $event)" size="mini" placeholder="请选择" style="width:100%;">
					<el-option label="补偿" value="COMPENSATION"></el-option>
					<el-option label="延迟赔付" value="DELAY"></el-option>
					<el-option label="维修费" value="REPAIR"></el-option>
				</el-select>
			</template>
		</el-table-column>
		<el-table-column label="退款原因" :class-name="$style['row-height']"  width="350">
			<template slot-scope="scope">
				<el-input
					v-model="scope.row.refund_reason"
					:maxlength="255"
					style="width:100%;"
					type="textarea"
					autosize
				></el-input>
			</template>
		</el-table-column>
		<el-table-column label="退款金额">
			<template slot-scope="scope">
				<el-input
					type="number"
					min="0"
					size='mini'
					v-model="scope.row.refund_amonut"
					@blur="e => onlyNumber(e, scope.row)"
					style="width:100%;"
				></el-input>
				<div class="calc-total" style="display:none;">合计 {{ refundItemListAllAmount.toFixed(2) }}</div>
			</template>
		</el-table-column>
	</el-table>
</div>
</template>
<script>
export default {
	props:['params'],
	data (){
		return {
			allActPrice: 0,
			inputAllMoney: undefined,
			refundItemListAllAmount: 0,
			refundItemList: [{
				refund_type: '',
				refund_reason: '',
				refund_amonut: 0,
			}],
		}
	},
	methods: {
		// 退款明细合计
		refundItemListCalc (){
			this.refundItemListAllAmount = this.refundItemList.reduce((a,b) => a + Number(b.refund_amonut||0), 0)
		},
		onlyNumber (e, row){
			row.refund_amonut = e.target.value = Number(Number(e.target.value).toFixed(2))
			this.refundItemListCalc()
		},
		averageMoney (){
			setTimeout(() => {
				if(!(this.inputAllMoney >= 0) || !this.inputAllMoney) this.inputAllMoney = 0

				var count = 0

				this.refundItemList.forEach((obj, index) => {
					if(index === this.refundItemList.length - 1){
						obj.refund_amonut = Number((this.inputAllMoney - count).toFixed(2))
					}else {
						var realNum = this.params.actPriceList[index] / this.allActPrice * this.inputAllMoney
						,	toFixedNum = String(Number(realNum.toFixed(2)))

						// 取两位小数不四舍五入
						count += (obj.refund_amonut = Number(String(realNum).slice(0, toFixedNum.length)))
					}
				})
				this.refundItemListCalc()
			}, 500)
		},
		submit (){
			if(!this.refundItemList[0].refund_type){
				this.$message.error('请选择退款类型')
			}else if (this.inputAllMoney === undefined){
				this.$message.error('请输入总金额')
			}else {
				this.params.callback(this.refundItemList)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
		},
	},
	mounted (){
		this.params.actPriceList.forEach((price, index) => {
			this.allActPrice += Number(price)
			if(!this.refundItemList[index]){
				this.refundItemList.push({
					refund_type: '',
					refund_reason: '',
					refund_amonut: 0,
				})
			}
		})
	},
}
</script>
<style module>
.row-height :global(.cell) {
	height: auto!important;
}
.only-show-total :global(.el-checkbox),
.only-show-total :global(.el-select),
.only-show-total :global(.el-input),
.only-show-total :global(.el-textarea),
.only-show-total :global(.table-index) {
	display: none;
}
.only-show-total :global(.calc-total) {
	display: block!important;
}
</style>