<!-- 选择客户承担费用 -->
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-button type='primary' size='mini' @click='submit'>确认</el-button>
      <el-col class='xpt-align__right' :span='20' style="float:right;">
        <el-select v-model="search.limit_field" size='mini' style="width:150px;">
          <el-option label="平台单号" value="tid"></el-option>
        </el-select>
        <el-select v-model="search.operator" size='mini' style="width:100px;">
          <el-option label="等于" value="="></el-option>
        </el-select>
        <el-input placeholder="请输入查询值" size='mini' v-model="search.field_value"></el-input>
        <el-button type='primary' @click="searchFunc" size='mini'>查询</el-button>
        <el-button type='primary' @click="search.field_value=''" size='mini'>重置</el-button>
      </el-col>
    </el-row>
    <el-row class="xpt-flex__bottom mgb20 table-part" >
      <div class="empty-tip" v-if="list.length < 1&&!loading">
        <div><i class="el-icon-warning warn-icon"></i>&nbsp;不存在符合以下条件的补款单</div>
        <div class="tip-text">1. 同客户ID</div>
        <div class="tip-text">2. 同订单店铺</div>
        <div class="tip-text">3. 合单对账余额>0</div>
      </div>
      <el-table
        ref="customerFeeTableRef"
        :data="list"
        border
        tooltip-effect="dark"
        width='100%'
        style="width: 100%"
        highlight-current-row
        @selection-change="handleCurrentChange"
      >
        <el-table-column type="selection" width="55" align='center'></el-table-column>
        <el-table-column prop="tid" label="平台单号" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderBusinessMode" label="订单业务模式" width="120">
          <template slot-scope="scope">
            <span>{{BILL_TYPE_STATUS[scope.row.orderBusinessMode]}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="mergeTradeNo" label="合并单号" width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="stateRestAmount" label="合并对账余额" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" label="淘宝交易状态：">
          <template slot-scope="scope">
            <span>{{TAOBAO_STATUS[scope.row.status]}}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户承担费用" width="140">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.bearFee"
              min="0"
              style="width: 90%"
              type="number"
              size="mini"
              @change="valideFn1(scope.row)"
              @blur="calBearFee(scope.row)"
              placeholder="请输入"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-row>
<!--    <el-row class='xpt-pagation'>
      <el-pagination @size-change="pageSizeChange" @current-change="pageChange"
                     :current-page="search.page_no" :page-sizes="[10, 20, 50, 100]" :page-size="search.page_size"
                     layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
      </el-pagination>
    </el-row>-->
  </div>
</template>

<script>
import { BILL_TYPE_STATUS, TAOBAO_STATUS } from "@/components/after_solutions/enum";
export default {
  props:["params"],
  data(){
    var _this = this;
    return {
      loading: false,
      BILL_TYPE_STATUS,
      TAOBAO_STATUS,
      search:{
        field_value: "",//筛选条件：具体搜索值
        limit_field: "tid", //筛选条件：平台单号
        operator: "=",//筛选条件：= > <
        merge_trade_id: '',//针对合并订单号的问题商品
        page_size: 20,     //页数
        page_no: 1   //页码
      },
      list: [],
      pageTotal: 0,
      multipleSelection: [],
    }
  },
  methods:{
    //计算客户承担费用不能为负数
    valideFn1(row){
      if(Number(row.bearFee)<0){
        row.bearFee=-row.bearFee;
      }
    },
    // 计算客户承担费用
    calBearFee(row) {
      if(Number(row.stateRestAmount || 0) < Number(row.bearFee || 0)) {
        this.$message.error('合单输入的客户承担费用金额不可大于合单对账余额')
        // row.bearFee = Number(row.stateRestAmount).toFixed(2)
      }
      row.bearFee = Number(row.bearFee).toFixed(2)
    },
    handleCurrentChange(val){
      this.multipleSelection = val
    },
    // pageChange (page){
    //   this.search.page_no = page
    //   this.searchFunc()
    // },
    // pageSizeChange (pageSize){
    //   this.search.page_size = pageSize
    //   this.searchFunc()
    // },

    searchFunc(){
      this.loading = false
      this.list = []
      this.multipleSelection = []
      if(this.params?.selectData?.length > 0) {
        const self  = this
        // 获取承担费用列表
        const params = {
          tid: this.search?.field_value || '',
          aftersaleId: self.params.selectData[0].after_order_id
        }
        self.loading = true
        self.$http.post('/afterSale-web/api/aftersale/supplyBearCosts/list', params).then(res => {
          const resData = res?.body?.content || []
          const historyList = self.params?.selectFeeList || []
          if(self.params?.selectFeeList?.length > 0) {
            resData.forEach(row => {
              const getRow = historyList.find(v => v.tid === row.tid && v.mergeTradeId === row.mergeTradeId)
              if(getRow) {
                row.bearFee = getRow.bearFee
                self.multipleSelection.push(row)
              }
            })
            setTimeout(() => {
              self.multipleSelection.forEach(row => {
                self.$refs.customerFeeTableRef.toggleRowSelection(row);
              })
            }, 500)
          }
          self.list = resData
          // self.pageTotal = res.body.content.count
        }).finally(() => {
          self.loading = false
        })
      }
    },
    // 判断是否存在重复
    hasDuplicates(arr) {
      return arr.some((item, index) => arr.lastIndexOf(item) !== index);
    },
    // 确认按钮事件
    submit (){
      if(this.multipleSelection?.length < 1) {
        this.$message.error('请选择单据')
        return
      }
      if(this.multipleSelection.find(item => !item?.bearFee)) {
        this.$message.error('请输入选中单据的客户承担费用')
        return
      }
      if(this.multipleSelection.find(item => !(['WAIT_BUYER_CONFIRM_GOODS', 'TRADE_BUYER_SIGNED', 'TRADE_FINISHED'].includes(item.status)))) {
        this.$message.error('订单淘宝交易状态=“等待买家确认收货,即:卖家已发货”、“买家已签收,货到付款专用”、“交易成功”才可选')
        return
      }
      if(this.hasDuplicates(this.multipleSelection?.map(item => item.mergeTradeNo))) {
        this.$message.error('请选择不同的合并订单号')
        return
      }
      if(this.multipleSelection.find(item => Number(item.stateRestAmount || 0) < Number(item.bearFee || 0))) {
        this.$message.error('合单输入的客户承担费用金额不可大于合单对账余额')
        return
      }
      const callBackParams = {
        feeSelects: this.multipleSelection,
        totalFee: (this.multipleSelection.reduce((acc, cur) => acc + Number(cur.bearFee), 0)).toFixed(2)
      }
      this.params.callback(callBackParams)
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    },
  },
  mounted: function(){
    this.loading = false
    this.search.merge_trade_id = this.params.id
    this.searchFunc()
  },
}
</script>
<style lang="stylus" scoped>
.table-part{
  position: relative;
  .empty-tip{
    position: absolute;
    top: 45px;
    left: 40%;
    z-index: 999;
    background: #fff;
    div{
      line-height: 20px;
      color: #666;
    }
    .warn-icon{
      color: #facd91;
    }
    .tip-text{
      margin-left: 13px
    }
  }
}
</style>
