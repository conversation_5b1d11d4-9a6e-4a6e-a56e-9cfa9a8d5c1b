<!-- 合并订单/批次订单选择地址 -->
<template>
  <div class="xpt-flex">
    <xpt-headbar>
      <el-button type="primary" size="mini" slot="left" @click="close"
        >确认</el-button
      >
    </xpt-headbar>
    <xpt-list
      :data="addressList"
      :btns="[]"
      :colData="cols"
      selection="radio"
      @radio-change="radioChange"
      @row-dblclick="rowDblclick"
      :showHead="false"
    ></xpt-list>
  </div>
</template>
<script>
export default {
  data() {
    let self = this;
    return {
      addressList: [],
      selectCode: "",
      cols: [
        {
          label: "服务单号",
          width: 160,
          prop: "after_ticket_no",
        },
        {
          label: "合并单号",
          prop: "merge_trade_no",
        },
        {
          label: "批次单号",
          prop: "batch_no",
        },
        {
          label: "服务类型",
          prop: "service_type_one",
          format: "auxFormat",
          formatParams: "serviceTypeOne",
        },
        {
          label: "指令工单完成时间",
          prop: "finish_time",
          width: 120,
          format: "dataFormat1",
        },
      ],
      count: 0,
    };
  },
  props: ["params"],
  methods: {
    close() {
      if (!this.selectCode) {
        this.$message({
          type: "error",
          message: "请选择地址",
        });
        return;
      }
      this.params.callback(this.selectCode);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    getList() {
      let self = this;
      this.ajax.postStream(
        "/afterSale-web/api/aftersale/service/getReturnExpressNumber",
        this.params.postData,
        (d) => {
          if (d.body.result) {
            this.addressList = d.body.content;
            self.$message.success(d.body.msg);
          } else {
            self.$message.error(d.body.msg);
          }
        },
        (err) => {
          self.$message.error(err);
        }
      );
    },
    rowDblclick(obj) {
      this.selectCode = obj;
      this.close();
    },
    radioChange(obj) {
      this.selectCode = obj;
    },
    tableCustomerRowClassName(row, index) {
      /*return 'red';*/
      switch (row.color_sign) {
        case "A":
          return "merge-red";
        case "B":
          return "pink";
        case "C":
          return "yellow";
        case "D":
          return "green";
        case "E":
          return "blue";
        case "F":
          return "orange";
      }
    },
  },
  mounted() {
    this.getList();
  },
};
</script>
<style>
#order_select_address .list-dynamic {
  min-width: 600px;
  height: 100% !important;
}
#order_select_address .body {
  height: calc(100% - 38px) !important;
}
#order_select_address .list-dynamic .list-header .left {
  min-width: 50px;
}
#order_select_address .search-items-box li {
  width: 470px;
}
</style>
