<!-- 退款方案-收款账号列表 -->
<template>
<div class="xpt-flex">
	<el-row	class='xpt-top'	:gutter='40'>
		<el-button type='primary' size='mini' @click='() => submit()' :disabled="!selectData">确认</el-button>
	</el-row>
	<xpt-list
		:data='roleList'
		:colData='colData'
		selection="radio"
		:pageTotal='pageTotal'
		:showHead="false"
		@radio-change='s => selectData = s'
		@page-size-change='pageChange'
		@row-dblclick='submit' 
		@current-page-change='currentPageChange' ref='xptList'>
	</xpt-list>
</div>
</template>

<script>
export default {
	props:['params'],
	data (){
		return {
			roleList:[],
			selectData: null,
			pageTotal:0,
			searchObj: {
				page_no: 1,
				page_size: 50,
				cust_id: this.params.cust_id, // 搜索条件
			},
			
			colData: [{
				label: '支付方式',
				prop: 'pay_channel',
				format: 'auxFormat',
				formatParams: 'payType',
			},{
				label: '支付类型',
				prop: 'pay_method',
				formatter (val){
					return {
						ON_LINE: '线上',
						OFF_LINE: '线下',
					}[val] || val
				},
			},{
				label: '支付账号',
				prop: 'pay_account',
			},{
				label: '开户行',
				prop: 'bank',
			},{
				label: '用户名',
				prop: 'bank_user_name',
			},{
				label: '收款账号',
				prop: 'receiver_account',
			}],
		}
	},
	methods: {
		searchFun (){
			this.ajax.postStream("/order-web/api/customer/payAccount/getCustomerPayAccountList", this.searchObj, response => {
				var obj = response.body;
				if(obj.result){
					this.roleList = obj.content.list;
					this.pageTotal = obj.content.count;
				}
			})
		},
		submit (){
			this.params.callback(this.selectData)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		pageChange(pageSize){
			this.searchObj.page_size = pageSize
			this.searchFun()
		},
		currentPageChange(page){
			this.searchObj.page_no = page
			this.searchFun()
		},
	},
	mounted (){
		this.searchFun()
	},
}
</script>