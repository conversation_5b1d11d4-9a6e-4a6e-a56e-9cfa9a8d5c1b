<!--售后解决方案，新增和编辑进同一个页面-->
<template>
	<div style="height:100%;overflow-y:scroll;">
			<el-row class="allHeight" :class="classObj">
				<el-table :data="data" border  ref="data" class="initTable" @row-click="rowClick" @selection-change="selectionChange" :row-class-name="tableRowClassName">
						   <el-table-column  align='center' type="selection" width="55" :selectable="selectableFun"></el-table-column>
						   	<el-table-column label="序号" type="index" >
						   		<template slot-scope="scope">
						   			<span v-if="scope.row.question_sub_id == copySelectData.question_sub_id" style="font-weight:bold;color:red">{{scope.$index+1}}</span>
						   			<span v-else>{{scope.$index+1}}</span>
						   		</template>
						   	</el-table-column>
						    <el-table-column label="商品编码" prop="material_code" width="200" show-overflow-tooltip></el-table-column>
						    <el-table-column  label="商品名称" prop="material_name" width="200" show-overflow-tooltip></el-table-column>
						    <el-table-column label="规格描述"  prop="material_specification" show-overflow-tooltip></el-table-column>
						    <el-table-column label="问题描述" width="150" prop="question_description" show-overflow-tooltip></el-table-column>

							<el-table-column label="店铺" prop="shop_name">
							</el-table-column>
							<el-table-column label="服务商分类" prop="logistics_supplier_class">
								<template slot-scope='scope'>{{scope.row.logistics_supplier_class==='B'?'蓝豚':'普通'}}</template>
							</el-table-column>
							<el-table-column label="货运方式" prop="deliver_method">
								<template slot-scope='scope'>{{deliverMethodObject[scope.row.deliver_method]}}</template>
							</el-table-column>
              <el-table-column label="是否直发" prop="batch_deliver_method">
								<template slot-scope='scope'>{{scope.row.batch_deliver_method === 'SUPPLIER' ? '直发' : '非直发'}}</template>
							</el-table-column>
							<el-table-column  label="是否协作方发货" width="200">
								<template slot-scope='scope'>{{scope.row.ship_routing ==='EXTERNAL'?'是':scope.row.is_stop==='INTERNAL'?'否':''}}</template>
							</el-table-column>
							<el-table-column  label="销售数量" prop="count"></el-table-column>
							<el-table-column  label="单位" prop="units"></el-table-column>
							<el-table-column  label="实际售价" prop="act_price" ></el-table-column>

							<el-table-column  label="批次订单号" prop="batch_trade_no" width="200" show-overflow-tooltip></el-table-column>
							<el-table-column  label="销售订单号" prop="sys_trade_no" width="200" show-overflow-tooltip></el-table-column>
							<el-table-column  label="批号" prop="lot"></el-table-column>
							<el-table-column  label="是否停产">
								<template slot-scope='scope'>{{scope.row.is_stop==='0'?'否':scope.row.is_stop==='1'?'是':''}}</template>
							</el-table-column>
							<el-table-column  label="供应商" prop="suppiler_name"></el-table-column>
							<el-table-column  label="采购入库日期" width="200" prop="instock_date">
								<template slot-scope='scope'>{{scope.row.instock_date|dataFormat}}</template>
							</el-table-column>
							<el-table-column prop="bom_version" label="BOM版本" width="200"></el-table-column>
							<el-table-column prop="source_material_number" label="来源商品编码" width="130"></el-table-column>
							<el-table-column prop="source_material_name" label="来源商品名称" width="100"></el-table-column>
					</el-table>
			</el-row>

			<el-row :class="classObj2">
			<el-row  style="margin:10px 0;">

					<!-- 商品问题的方案 -->
					<table style="width:100%">
						<tr>
							<td style="color:#48576a;padding-right:10px; text-align:right" width="120">问题描述:</td>
							<td>
								<div v-show="descriptionShow"  class="pmm_div2">
									<div style="height:36px;">
										<div class="description-panel" v-on:click="openIconClick">
											<span v-text="copySelectData.question_description"></span>
											<i class="el-icon-edit" :on-icon-click="openIconClick"></i>
										</div>
									</div>
								</div>
								<div class="pmm_div2" v-show="!descriptionShow">
									<el-autocomplete
										v-model="copySelectData.question_description"
										:fetch-suggestions="querySearchAsync"
										placeholder="请输入内容"
										:trigger-on-focus="false"
										@select="handleSelect"
										:disabled="otherInfo.canEditSolutions?!canEditQuestion:true"
										ref="question_description"
										@blur.stop="closeIconClick"

									>
									</el-autocomplete>
								</div>
								<div class="xpt_pmm_div" v-if="showBtn">
						  			<div class="pmm_div" >
						  				<el-button  icon="check" size="mini" @click="sureChange"></el-button>
							    		<el-button  icon="close" size="mini" @click="cancel"></el-button>
						  			</div>
								</div>
								<div style="margin-top:15px" v-else></div>
							</td>
						</tr>
					</table>



					<ul class="solutions-ul" v-if="!isEdit && !params.isReSaleOrder">
						<li>期望类型:</li>
						<li :class="componentType == 1 || componentType=='RETURNS'?'solutions-active':''" @click="changComponents(1)">退/换货</li>
						<li :class="componentType == 2 || componentType=='REPAIR' ?'solutions-active':''" @click="changComponents(2)">服务</li>
						<li :class="componentType == 3 || componentType=='SUPPLY'?'solutions-active':''" @click="changComponents(3)">补件</li>
            <li :class="componentType == 6 || componentType=='GIFT'?'solutions-active':''" @click="changComponents(6)">赠品</li>
						<!-- <li :class="componentType == 4 || componentType=='REFUND'?'solutions-active':''" @click="changComponents(4)">退款</li> -->
					</ul>
					<ul class="solutions-ul" v-else>
						<li>期望类型:</li>
						<li :class="componentType == 1 || componentType=='RETURNS'?'solutions-active':''" >退/换货</li>
						<li :class="componentType == 2 || componentType=='REPAIR' ?'solutions-active':''" >服务</li>
						<li :class="componentType == 3 || componentType=='SUPPLY'?'solutions-active':''" >补件</li>
            <li :class="componentType == 6 || componentType=='GIFT'?'solutions-active':''" >赠品</li>
						<!-- <li :class="componentType == 4 || componentType=='REFUND'?'solutions-active':''" >退款</li> -->
					</ul>

				</el-row>
				<!-- 商品问题的方案 -->
				<el-row>

					<!-- 退/换货方案 -->
					<component is='returnGoods' v-if="componentType == 1 || componentType=='RETURNS'" :selectedData="selectData" :otherInfo="otherInfo" :operatParams="operatParams" :copySelectData="copySelectData" :allQuestions="data" :afterGroup="afterGroup" @changeSolution="changeSolution" @allGoodsmatchQuestions="allGoodsmatchQuestions" @selectGoodsmatchQuestions="selectGoodsmatchQuestions" @reSetQuestionListFn="reSetQuestionListFn" :refundingPaymentList="refundingPaymentList"></component>
			        <!-- 服务 -->
			        <component is='returnRepair' v-if="componentType == 2 || componentType=='REPAIR'" :selectedData="selectData" :otherInfo="otherInfo" :operatParams="operatParams" :copySelectData="copySelectData" :allQuestions="data" :afterGroup="afterGroup" @changeSolution="changeSolution" @allGoodsmatchQuestions="allGoodsmatchQuestions" @selectGoodsmatchQuestions="selectGoodsmatchQuestions"  @reSetQuestionListFn="reSetQuestionListFn"></component>

					<!-- 补件方案 -->
					<component is='batchGoods' v-if="componentType == 3 || componentType=='SUPPLY'" :selectedData="selectData" :otherInfo="otherInfo" :operatParams="operatParams" :copySelectData="copySelectData" :allQuestions="data" :afterGroup="afterGroup" @changeSolution="changeSolution" @allGoodsmatchQuestions="allGoodsmatchQuestions" @selectGoodsmatchQuestions="selectGoodsmatchQuestions"  @reSetQuestionListFn="reSetQuestionListFn" :isReSaleOrder="params.isReSaleOrder" :isPushDownConsult="true"></component>
          <giveaway v-if="componentType == 6 || componentType=='GIFT'" :selectedData="selectData" :otherInfo="otherInfo" :operatParams="operatParams" :copySelectData="copySelectData" :allQuestions="data" :afterGroup="afterGroup" @changeSolution="changeSolution" @allGoodsmatchQuestions="allGoodsmatchQuestions" @selectGoodsmatchQuestions="selectGoodsmatchQuestions"  @reSetQuestionListFn="reSetQuestionListFn" :isReSaleOrder="params.isReSaleOrder"></giveaway>
				</el-row>


			</el-row>

	</div>
</template>
<script>
	//问题列表，如果是新增进来的话，则拿父问题下面的最新的子问题，如果是编辑进来的话，则拿已建方案的问题，如果父问题没有下面的子问题没有建方案，则拿最新的子问题
	/*一个子问题只能对应一个方案*/
	//退换货
	import returnGoods from '@components/after_solutions/changingOrRefunding.vue';
	import returnGoods2 from '@components/after_solutions/changingOrRefunding_2.vue';
	import returnMoney from '@components/after_solutions/returnMoney.vue';
  	import returnRepair from '@components/after_solutions/repair.vue';
  	//补件
	import batchGoods from '@components/after_solutions/batch.vue';
  import Giveaway from '@components/after_solutions/giveaway.vue';
	export default {
	    props:['params'],
		data(){
            var self = this;
			return {
				descriptionShow:true,
				activeName:'first',//子问题的table
				componentType:'',//问题商品
				canEditQuestion:true,


				selectGoods:null,//选中的明细行（选中退换货并集）
				allGoods:null,//所有明细行（退换货并集）

				isEdit:self.params && self.params.afterOrderInfo && self.params.afterOrderInfo.after_plan_id,//是否是有方案的页面


				classObj:{//问题商品高度的一个样式
					changeHeight:self.componentType?true:false
				},
				classObj2:{
					boxShadow:self.componentType?true:false
				},
				showBtn:false,//按钮
				templateType:null,//方案按钮

				//对应的方案类型的ID,需要传参过去拿到相应方案字段，暂时先这样
				otherInfo:{
					after_order_id:null,
					after_order_no:null,
					merge_trade_id : null,
					merge_trade_no : null,
					after_plan_id:null,//编辑,方案ID
					after_question_id:null,//方案所挂的问题ID
					after_plan_group_id:null,//方案组ID
					canEditSolutions:true,//问题是否可以新增方案
					form:null,
          if_dealer: null, // 是否经销商订单
				},
				operatParams:{
					bool : true,//新增和编辑公用的参数
					isSwitchTab : true//方案按钮之间的切换
				},
				afterGroup:[],//方案组ID关联的方案组信息

				selectData:[],//已选择的商品
				copySelectData:{},//copy一份已选择的商品

				data:[],//问题商品列表
				options: [],
				timer:null,//计时器
				refundingPaymentList: [],
				deliverMethodObject: {
					"LOGISTICS":"物流",
					"EXPRESS":"快递",
					"THREES":"三包",
					"DOORSTEP":"送货上门",
					"FAILED": "匹配失败"
				}

			}
		},
		methods:{
		    initData(){
		    	var data = this.params || {};
		    	if(data.afterOrderInfo){
					this.otherInfo = Object.assign({},this.otherInfo,data.afterOrderInfo);
				}


		    	this.setCloseTab();

			},
			/**
			*方案里面商品对应的问题信息列表
			***/
			allGoodsmatchQuestions(data){
				this.allGoods = data;
				//console.log('data',data);
			},


			/**
			*设置问题是否可勾选
			*补件方案禁止无源单非商品问题建方案
			*退换货非商品问题不能退换货
			***/
			selectableFun(row){
				if(!this.otherInfo.canEditSolutions || !this.data || !this.data.length) return false;//没有权限不给勾选
				if((this.componentType =='RETURNS' || this.componentType == 1) && row.if_goods_question == 'N') return false;

				if(this.componentType !='SUPPLY' && this.componentType != 3) return true;
				let if_has_source = row.if_has_source;
				let if_goods_question = row.if_goods_question;
				if(if_has_source == 'N' && if_goods_question == 'N') {
					return false;
				}
				return true;
			},
			/**
			*根据componentType来获取查询需要的字段
			**type==1为退/换货,type==2为服务,type==3为补件,type==4为退款
			**/
			getSearchType(type){
				var typeList = {
					1:'RETURNS',
					2:'REPAIR',
					3:'SUPPLY',
					4:'REFUND',
          5:'GIFT',
					RETURNS:'1',
					REPAIR:'2',
					SUPPLY:'3',
					REFUND:'4',
          GIFT:'5'
				}
				return typeList[type];
			},
			/**
			*是否可以为售后单添加方案
			*售后单没有关闭，并且是售后锁定人是自己
			***/
			isCanEidtSolutions(){
				let data = this.params.afterOrderInfo.form;
				//经销商订单，林氏员工只允许查看
				let isDealer = data.if_dealer=='Y'?true:false;
				let isDealerUser = this.getUserInfo('isDealerUser');
				if(isDealer && !isDealerUser){
					this.otherInfo.canEditSolutions = false;
					return;
				}
				//业务状态为售前处理或是售后处理
				let bool = data.otherInfo.locker == this.getEmployeeInfo('id') && (data.status == 'PRE_HANDLE' || data.status == 'AFTER_HANDLE')
				this.otherInfo.canEditSolutions = bool;
			},
			/**
			*单击某行执行的数据
			**/
			rowClick(row, event, column){

				if(!column.label) return;
				this.setCopyData(row);

			},
			/**
			*拿到各方案选中明细行所对应的问题
			***/
			selectGoodsmatchQuestions(data){
				let question_sub_id = data[0].question_sub_id;
				if(!question_sub_id) return;
				let question = this.getQuestionByParentQuestionId(data[0].parentQuestionId).question;
				this.setCopyData(question);

			},
			/**
			*问题列表-复选框选择问题
			**/
			selectionChange(selection){
				let self = this;
				this.selectData = selection.length?selection:[];
				this.selectData.forEach(item => {
					self.selectData.forEach(secItem =>{
						if(item.logistics_supplier_class != secItem.logistics_supplier_class){
							this.$message.error('方案商品服务商分类必须一致，请重新选择');
							this.selectData = [];
							this.$refs.data.clearSelection();
							return;
						}
					})
				});
				//新增首次点击
				if(!this.params.afterOrderInfo.after_plan_id &&　(!this.copySelectData || this.isEmptyObject(this.copySelectData))){
					this.setCopyData(this.selectData[0])
				}

			},
			/**
			*设置copySelectData值
			**/
			setCopyData(data){
				if(!data) {
					this.copySelectData = {};
					return;
				}
				let o = Object.assign({},data);
				if(this.copySelectData && !this.isEmptyObject(this.copySelectData)){
					if(o.question_sub_id == this.copySelectData.question_sub_id) return;
				}
				this.copySelectData = o;
				if(!this.otherInfo.canEditSolutions) return;
				this.getAllQuestions();
			},

			/**
			*模糊搜索父问题下面对应的子问题
			**/
			querySearchAsync(queryString, cb) {

				this.showBtn = true;
				var results = queryString ? this.options.filter(this.createStateFilter(queryString)) : this.options;
				cb(results);

		    },
		    /**
			*模糊搜索条件过虑
			**/
			createStateFilter(queryString) {
		        return (state) => {
		          return (state.value.indexOf(queryString.toLowerCase()) === 0);
		        };
		    },
		    /**
		    *下拉选中子问题
		    **/

			handleSelect(item){
				let obj = Object.assign({},item);
				//把copySelectData初使化的sub_question_id存进去
				obj.originalSubQuestionId = this.copySelectData.question_sub_id || this.copySelectData.originalSubQuestionId;
				obj.originalBuyer = this.copySelectData.buyer;
				this.copySelectData = obj;
			},

			/**
			*根据父问题获取所有子问题
			*/
			getAllQuestions(){
				var parentId = this.copySelectData.parent_question_id;
				if(!parentId) return;
				this.ajax.postStream('/afterSale-web/api/aftersale/plan/listQuestionSubByParentId',{parent_question_id:parentId},res=>{
					var data = res.body;
					if(!data.result) return;
					this.options = [];
					var list = data.content || [];
					list.map((obj)=>{
						var copyObj = Object.assign({},obj);
						copyObj.value = copyObj.question_description;
						copyObj.isSubQuestion = true;
						this.options.push(copyObj);
					})

				})
			},


			/**
			*判断当前修改的问题有没有挂方案
			***/
			judgeHasSolutionByQuestionSubId(){
				var data = this.getQuestionByParentQuestionId().question;
				if(!data) return false;
				return data.initAfterPlanId?true:false;

			},
			/**
			*取消的改变问题描述
			*
			***/
			cancel(){
				//问题列表里面没有找到，则说明是旧的数据
				let originalData = this.getQuestionByParentQuestionId().question;
				this.showBtn = false;
				if(!originalData) return;
				let question_sub_id = this.copySelectData.question_sub_id || this.copySelectData.originalSubQuestionId;
				if(question_sub_id != originalData.question_sub_id) return;
				if(JSON.stringify(this.copySelectData) == JSON.stringify(originalData)) return;
				this.copySelectData = Object.assign({},originalData);
			},

			/**
			*改变问题描述
			*type==1为确定改变，0为取消
			*/
			sureChange(){
				var copyData = this.copySelectData;
				if(!copyData || this.isEmptyObject(copyData)){
					this.$message.error('请选择问题进行修改');
					return;
				}

				var description = copyData.question_description;
				if(!description) {
					this.$message.error('请填写描述');
					return;
				}
				//当前问题列表里面找不到问题，则说明是旧数据
				var selectData = this.getQuestionByParentQuestionId().question;
				if(!selectData) return;
				//判断描述有没有改变
				if(JSON.stringify(copyData) == JSON.stringify(selectData)){
					this.$message.error('问题描述没有任何的修改');
					return;
				}

				//保存子问题
				var obj = {//如果是该问题已建方案，则传该问题id，如果是没有建方案，则传null过去
					id:this.judgeHasSolutionByQuestionSubId()?copyData.question_sub_id || copyData.originalSubQuestionId:null,
					question_description : copyData.question_description,
					parent_question_id : copyData.parent_question_id,
					source_id : copyData.source_id
				};

				this.ajax.postStream('/afterSale-web/api/aftersale/plan/saveQuestionSub',obj,(res)=>{
					let data = res.body;

					if(!data.result){
						this.$message.error(data.msg);
						return;
					}
					this.showBtn = false;
					this.updataQuestion(data.content);
				});
			},
			/**
			*更新问题信息
			****/
			updataQuestion(newData){
				if(!newData || !newData.id) return;
				var parent_question_id = this.copySelectData.parent_question_id || this.copySelectData.after_question_id;
				if(!parent_question_id) return;
				var data = this.getQuestionByParentQuestionId(parent_question_id);
				var oldQuestion = data.question,index = data.index;
				if(!oldQuestion) return;
				var isChecked = this.isSelectData(oldQuestion);
				let newId = newData.id;


				let c = Object.assign({},oldQuestion);
				c.source_id = newData.source_id;
				c.question_sub_id = newId;
				c.question_description = newData.question_description;
				this.data.splice(index,1,c);
				this.copySelectData = Object.assign({},c);
				this.getAllQuestions();
				if(isChecked){
					this.$refs.data.toggleRowSelection(c);
				}

			},
			/***
			*根据父问题ID找到问题
			*有传参数则拿参数对应的问题，没有传参则拿当前问题的父问题ID
			***/

			getQuestionByParentQuestionId(parentQuestionId){
				let parent_question_id = parentQuestionId || this.copySelectData.parent_question_id;
				if(!parent_question_id) return '';
				var d = '',index;
				this.data.map((a,b)=>{
					if(a.parent_question_id == parent_question_id){
						d = a;
						index = b;
					}
				})
				return {
					question:d,
					index:index
				}
			},
			/**
			*更改的数据是否在已选中的数据里面
			*｛question｝
			**/
			isSelectData(question){
				if(!question || !this.selectData.length) return false;
				let bool = false;
				this.selectData.map((a,b)=>{
					if(a.question_sub_id == question.question_sub_id){
						bool = true;
						return;
					}
				});
				return bool;
			},


			/**
			*根据售后单ID
			*获取问题的子问题列表
			*/
			getQuestions(callback){
				var params = {
					 after_order_id:this.otherInfo.after_order_id
				}
				this.ajax.postStream("/afterSale-web/api/aftersale/plan/listQuestionSub",params,res=>{
					var data = res.body;
					this.componentType = this.params.type || null;
					if(!this.componentType || !data.result){
						this.$message({message: data.msg,type:data.result?'success':'error' });
					}

					if(!data.result) return;
					this.data = data.content || [];
					if (this.params.afterOrderInfo.merge_trade_no) {
						this.getRefundingPaymentList()
					}
					callback && callback(this.data);

				});
			},
			/*
			*切换时获取相应的标题
			**/
			getTitle(type){
				var title = {
					1 : '退/换货',
					2 : '服务',
					3 : '补件',
					4 : '退款',
					5:'仅问题提交',
          6:'赠品'
				}
				let t = title[type] || '';
				return t;
			},
			/**
			*禁掉无源单创建退换货方案
			***/
			judgeCreatSolutionByIfHasSource(type){
				if(!this.data || !this.data.length) return true;
				if(this.data[0].if_has_source == 'N' && (type == 1 || type == 'RETURNS')){
					this.$message.error('无源单不能做退换货方案');
					return false;
				}
				return true;
			},
			/**
			*设置相应的标题
			*/
			setTitle(type){
				let title = this.getTitle(type);
				this.$root.eventHandle.$emit('updateTab',{name:this.params.tabName,title:'申请售后办理'+title});
			},
			/**
			*问题商品里面的方案类型
			*切换到对应的组件里面去
			*type==1为退/换货,type==2为服务,type==3为补件,type==4为退款 6 赠品
			*/
			async changComponents(type){
				let self = this;
				if(!this.componentType && (!this.data || !this.data.length || !this.selectData || !this.selectData.length)) {
					this.$message.error('请选择问题');
					return;
				}
        if(type===6){
         const data = await this.isDealerSelection()
          if(!data){
            this.$message.error('经销商不可保存赠品');
            return
          }
        }

				let bool = this.judgeCreatSolutionByIfHasSource(type);
				if(!bool)  return;
				//把不能建方案的问题排除掉


				this.templateType = type;
				if(!this.componentType){
					this.changeSolution();
					return;
				}

				this.operatParams.params = this.params;
				this.operatParams.isSwitchTab = !this.operatParams.isSwitchTab;

			},

      // 赠品内容经销商不可点击判断
      isDealerSelection(){
        return new Promise((resolve)=>{
          this.ajax.postStream('/afterSale-web/api/gift/plan/checkIfSaveGiftPlan',undefined,(res)=>{
            console.log(res)
            if(res.body.result){
              resolve(true)
            }else{
              this.$message.error(res.data.msg)
              resolve(false)
            }

          },(err) => {
            this.$message.error(err)
            resolve(false)
          })
        })
      },

			/**
			*设置问题列表可勾选性
			***/
			setDisabledOfQuestions(){
				if(!this.selectData || !this.selectData.length) return;
				let parentIds = this.selectData.map(obj=>obj.parent_question_id);
				parentIds = JSON.stringify(parentIds);
				this.data.map((a,b)=>{
					let parentId = a.parent_question_id;
					if(parentIds.indexOf(parentId) != -1){
						let bool = this.selectableFun(a);
						if(!bool){
							this.$refs.data && this.$refs.data.toggleRowSelection && this.$refs.data.toggleRowSelection(a,false);
						}
					}
				})
			},
			/**
			*方案相互间的切换
			**/
			changeSolution(){
				var type = this.templateType;
				this.componentType = type;
				let title = this.getTitle(type);

				this.setTitle(type);
				this.classObj.changeHeight = true;
				this.classObj2.boxShadow = true;
				this.setCloseTab();
				this.setDisabledOfQuestions();
			},
			/*
			*关闭页面
			***/
			closeTab(){
				this.operatParams.params = this.params;
				this.operatParams.bool = !this.operatParams.bool;
			},
			/**
			*根据进入方案的页面设置关闭的回调函数
			**/
			setCloseTab(){
				if(!this.componentType) return;
				this.params.__close = this.closeTab;
			},
			/**
			*设置行样式
			***/
			tableRowClassName(row, rowIndex) {

				//鼠标选中的问题
				if(this.copySelectData && row.question_sub_id == this.copySelectData.question_sub_id) return 'pmm_green22';

				//所有明细行
				if(!this.allGoods || !this.allGoods.length) return '';
				//所有明细行
				let k = this.allGoods.length;
				for(let g = 0; g < k; g++){
					let good = this.allGoods[g];

					//增加一个，新增明细行时，按照父问题ID去映射
					if(row.question_sub_id == good.question_sub_id || (!good.id && row.parent_question_id == good.parentQuestionId)){


						return 'pmm_red22';
					}
				}
				return '';

		    },

			setCurrentRow(question){
				if(!question) return;
				let q = Object.assign({},question);
				this.copySelectData = q;
				this.getAllQuestions();
			},

			/**
			*重新设置问题列表
			*编辑进来，有挂方案的问题拿挂方案的问题，没有方案的，则拿最新问题
			*以父问题为维度
			*传参过来则是方案页面事件响应，没有传参则是编辑刚进来
			***/
			reSetQuestionList(questions){
				if(!questions) return;
				var afterGroup = questions;
				let data = this.data;
				let a = afterGroup.length,i = data.length,d=0;
				if(!a || !i) return;
				this.allGoods = [];

				//vue必须是已经渲染了节点
				if(!this.$refs.data || !this.$refs.data.toggleRowSelection) return;

				this.isEdit = true;
				for(;d<i;d++){
					let questionData = data[d];
					let parent_question_id = questionData.parent_question_id;
					let c = 0;
					for(;c<a;c++){
						let groupData = afterGroup[c];

						if(parent_question_id == groupData.parent_question_id){
							questionData.question_sub_id = groupData.question_sub_id;
							questionData.source_id = groupData.source_id;

							questionData.question_description = groupData.question_description;
							questionData.initAfterPlanId = groupData.after_plan_id;//把方案ID传进去

							if(groupData.isCurrentSolution){
								this.setCurrentRow(questionData);
							}
							this.$refs.data.toggleRowSelection(questionData,true);

							this.allGoods.push(questionData);
							break;
						}
						if(c == a-1){
							this.$refs.data.toggleRowSelection(questionData,false);
						}

					}
				}
				clearInterval(this.timer);
			},

			reSetQuestionListFn(data, cb){
				var _this = this;
				this.canEditQuestion = data.canEditQuestion;
        if(this.timer){	clearInterval(this.timer);}
				this.timer = setInterval(()=>{
					_this.reSetQuestionList(data.questions);
					cb && cb()
				},100);
			},

			// 获取商品对应的支付明细
			getRefundingPaymentList () {
				let mergeMaterialIds = [], self = this
				this.data.forEach(refunding => {
					mergeMaterialIds.push(refunding.merge_material_id)
				})
				let params = {
					mergeMaterialIds: mergeMaterialIds
				}
				this.ajax.postStream('/afterSale-web/api/aftersale/plan/getRealStcok',params,(res)=>{
					if (!res.body.result) {
						self.$message.error(res.body.msg)
						return
					}
					if (res.body.content) {
						self.refundingPaymentList = res.body.content
					} else {
						self.refundingPaymentList = []
					}
				}, err => {
					self.$message.error(err)
				})
			},
			openIconClick(){
				let self = this;
				this.descriptionShow = false;
				console.log(self.$refs['question_description'])
			},
			closeIconClick(){
				alert('u are');
				this.descriptionShow = true;
			}

		},

		components: {
			'returnGoods': window.__test_changingOrRefunding ? returnGoods : returnGoods2,
			'returnMoney': returnMoney,
      		'returnRepair': returnRepair,
      		'batchGoods' : batchGoods,
      Giveaway
		},
		created(){
			this.initData();
			this.getQuestions();
		},
		mounted(){
			//监听切换业务代理事件
			this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
				this.isCanEidtSolutions();
			});

		},
		watch:{
			componentType:function(newVal,oldVal){
				//屏蔽补件无源单非商品问题建方案
				if(!this.selectData || !this.selectData.length || !(newVal == 'SUPPLY' || newVal == 3)) return;
				this.selectData.map((a,b)=>{
					if(a.if_has_source == 'N' && a.if_goods_question == 'N'){
						 this.$refs.data && this.$refs.data.toggleRowSelection && this.$refs.data.toggleRowSelection(a,false);
					}
				})
			}
		}
	}
</script>

<style type="text/css" slot-scope="scope">
	/**
	*自定义的全居css
	*/
	.el-table tr:hover.pmm_blue22 td,.el-table tr.pmm_blue22{background: beige;}
	.el-table tr:hover.pmm_green22 td,.el-table tr.pmm_green22{background: burlywood;}
	.el-table tr:hover.pmm_red22 td,.el-table tr.pmm_red22{background: aquamarine;}
	.el-tabs.allHeight{ height: auto}
	.boxShadow{box-shadow: -2px -2px 3px #dbdbdb,2px 2px 3px #dbdbdb;padding:0 15px 15px;margin-top:10px;}
	.pmm_div2 .el-autocomplete{width: 100%;}
	.pmm_div2 .el-input,.pmm_div2 .el-select, .pmm_div2 .el-date-editor.el-input{width: 100%;}

	.show{display: block !important;}
	.hidden{display: none !important;}
	.xpt_pmm_div{text-align: right;margin-top:-3px;}

	.pmm_div{display: inline-block;border: 1px solid #bfcbd9;background: #bfcbd9;border-radius: 3px;padding: 0 5px;}
	.xpt_pmm_div [class*=" el-icon-"], .xpt_pmm_div [class^=el-icon-]{
		font-weight: normal;color: #dbdbdb;
	}

	.initTable.el-table .el-table__body-wrapper td .cell, .el-table .el-table__fixed-body-wrapper td .cell{height: auto;}

	.solutions-ul{overflow: hidden;width: 100%;color:#48576a;padding: 0 15px;}
	.solutions-ul li{float: left; padding: 10px 20px;cursor: pointer;border:1px solid #dbdbdb; position: relative;margin-right: 10px;}
	.solutions-ul li:first-child{border:0;padding: 10px;width: 150px;margin-right:20px;text-align: right;}
	.solutions-ul li.solutions-active{border:2px solid red;background:url('/static/images/bg.gif') no-repeat bottom right;cursor: none;}
	.description-panel{
		display: inline-block;
		height: 26px;
		line-height: 26px;
		border: #fff 1px solid;
	}
	.description-panel:hover{
		border: #dbdbdb 1px solid;
	}
</style>
