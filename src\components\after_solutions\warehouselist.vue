<!--仓库列表-->
<template>
	<xpt-list 
		selection='radio'
		:btns='btns' 
		:data='list' 
		:colData='colData' 
		:pageTotal='count' 
		:isNeedClickEvent='true'
		@row-dblclick="rowDblclick"
		searchHolder='请输入搜索条件'
		@search-click='searchClick'
		@radio-change='select'  
		@page-size-change='sizeChange' 
		@current-page-change='pageChange'>
	</xpt-list>

</template>
<script>
	export default {//需要类型和相关的id
	    props:['params'],
		data(){
            var self = this;
			return {
                search:{
                   	material_id:'',
                   	stock_number:null,
                   	page_no:1,
                   	page_size:self.pageSize
					
				},
				 btns: [
                {
                    type: 'primary',
                    txt: '确定',
                    click: function(){
                    	self.confirmAddGoods();
                    }
                }],
				colData:[
				{
					label: '仓库名称',
					prop: 'stock_name'
				}, {
					label: '仓库编码',
					prop: 'stock_number'
				}],
				count:0,
                selectedShopData:'',
                radioData:'',
                list:[]//店铺列表
               	//selectType:'radio',//默认单选，因为是财务中台导入过来的数据
                
			}
		},
		methods:{
			/**
			*双击事件
			**/
			rowDblclick(data){
				this.selectedShopData = data;
				this.confirmAddGoods();
			},
			/**
			*关闭当前弹出框组件
			*/
            closeCurrentComponent(){
                this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
			},
			searchClick(key){
				this.search.stock_number = key;
				 this.getList();
			},
			
            confirmAddGoods(){
				//确认添加物料信息
				/*var type = this.selectType;
				var  selectedGoodsData = type == 'radio'?this.radioData:type=='checkbox'?this.selectedShopData:'';
				selectedGoodsData.constructor == Object?selectedGoodsData = [selectedGoodsData]:'';*/
				var selectedGoodsData = this.selectedShopData;
				console.log('selectedGoodsData',selectedGoodsData);
				if(!selectedGoodsData) {
				    this.$message({
				        message:'请选择仓库信息',
						type:'error'
					})
					return;
				}

                this.closeCurrentComponent();
				this.params.callback && this.params.callback({data:selectedGoodsData});


			},
            sizeChange(size){
                // 每页加载数据
				this.search.page_size = size;
                this.search.page_no = 1;
               
                this.getList();
			},
            pageChange(page_no){
                // 页数改变
                this.search.page_no = page_no;
                this.getList();
            },

           
			/**
			 * 请空已选的数据
			 * **/
			clearSelectedData(){
                this.selectedShopData = '';
                //this.radioData = '';
                this.$refs.list && this.$refs.list.clearSelection();
			},

            searchList(){
              //模糊搜索
				this.getList();
			},
            select(selection){//单选发生改变
				this.selectedShopData = selection;
            },

            /**
            *初使化数据
            **/
			initData(){
			  var data = this.params || {};
			   //列表选择框的问题，1为单选，2为多选，3为没有任何选择框
			  //this.selectType = data.type == 1?'radio':data.type == 2?'checkbox':'';
			  this.search.material_id = data.material_id;
			
			},

            getList(){
				this.ajax.postStream('/kingdee-web/api/stock/list',this.search,(d)=>{
              	    var data = d.body;

              	    if(!data.result){
                        this.$message({
                            message:data.msg,
                            type:'error'
                        })
              	        return;
					}
                    this.list = data.content.list;
					this.count = data.content.count;
                    this.clearSelectedData();
                },function(e){
					console.log(e);
                })
            }


		},

        created(){
			this.initData();
			this.getList();
		}



	}
</script>
<style type="text/css" scoped>
	.el-table__body-wrapper{margin-bottom:20px;}
</style>