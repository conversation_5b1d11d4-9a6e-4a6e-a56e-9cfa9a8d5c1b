<template>
    <div class="xpt-flex">
        <el-row	class='xpt-top'	:gutter='40'>
            <el-col :span='19'>
                <el-button type='success' size='mini' @click="confirm">确认</el-button>
            </el-col>
            <el-col :span='5'>
              <span>自动分配开关<el-switch v-model="autoDistribution" on-text="是" off-text="否" :on-value='1' :off-value='0' ></el-switch></span>
            </el-col>
        </el-row>
        <xpt-list
            :data="dataList"
            :colData="cols"
            :btns="btns"
            :showHead="false"
            :pageTotal='null'
            :selection="''"
        >
          <template slot='weightSlot' slot-scope='scope'>
            <div class="weightSelect">
            <el-select v-model="scope.row.weight" filterable placeholder="请选择"  size='mini'>
              <el-option v-for="(item, idx) in weightObj" :label="item.label" :value="item.values" :key='item.values + idx'></el-option>
            </el-select>
            </div>
          </template>
        </xpt-list>
    </div>
</template>
<script>
export default {
    props: ['params'],
    data () {
        return {
          autoDistribution: 1,
          dataList: [],
           btns: [
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.getList();
              },
            },
          ],
          cols: [
            {
              label: "用户账户",
              prop: "employee_number",
            }, {
              label: "用户名称",
              prop: "real_name",
            }, {
              label: "昵称",
              prop: "nick_name",
            }, {
              label:'生效',
              prop:'status',
              format:'statusFilter'
            }, {
              label:'权重',
              slot: 'weightSlot',
            },
          ],
          pageTotal: 0,
          weightObj: [
            {values: '0.05', label: '5%',},
            {values: '0.1', label: '10%'},
            {values: '0.15', label: '15%'},
            {values: '0.2', label: '20%'},
            {values: '0.25', label: '25%'},
            {values: '0.3', label: '30%'},
            {values: '0.35', label: '35%'},
            {values: '0.4', label: '40%'},
            {values: '0.45', label: '45%'},
            {values: '0.5', label: '50%'},
            {values: '0.55', label: '55%'},
            {values: '0.6', label: '60%'},
            {values: '0.65', label: '65%'},
            {values: '0.7', label: '70%'},
            {values: '0.75', label: '75%'},
            {values: '0.8', label: '80%'},
            {values: '0.85', label: '85%'},
            {values: '0.9', label: '90%'},
            {values: '0.95', label: '95%'},
            {values: '1', label: '100%'},
          ]
        }
    },
    mounted() {
      this.autoDistribution = this.params.autoDistribution
      this.getList()
    },
    methods: {
      confirm() {
        let list = []
        this.dataList.forEach(item => {
          list.push({
            relationId: item.relationId,
            weight: item.weight,
          })
        })
        let postdata = {
          shopCode: this.params.appointment_send_shop_no,
          autoDistribution: this.autoDistribution,
          list: list
        }
        this.ajax.postStream('/crm-web/api/crmAppointmentTracing/assignShopGuideWeight',postdata,res => {
            if(res.body.result){
              this.params.callback();
              this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
      },
      getList(){
          let _this = this;
          let params = {
            key: '',
            appointment_send_shop_no: this.params.appointment_send_shop_no,
            page:{
              length:200,
              pageNo:1,
            }
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getShopGuideList',params,res => {
            if(res.body.result){
              this.dataList = res.body.content.list;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
    }
}
</script>
<style lang="stylus">
.weightSelect{
  .el-input, .el-select, .el-date-editor.el-input{
    width: 100%
  }
}
</style>