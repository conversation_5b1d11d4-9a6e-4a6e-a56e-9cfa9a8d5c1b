<template>
  <div class='xpt-flex'>
    <xpt-list
      :data="dataList"
      :colData="cols"
      :btns="btns"
      :pageTotal='pageTotal'
      :selection="selection"
      :searchPage="page_name"
      @search-click="searchClick"
      @page-size-change='pageChange'
      @current-page-change='currentPageChange'
      @radio-change='select'
    ></xpt-list>
  </div>
</template>

<script>
    export default {
      name: "appoint_shop_list",
      props:["params"],
      data () {
        let self = this;
        return {
          dataList:[],
          selection:"radio",
          pageTotal:0,
          pageSize:50,
          pageNo:1,
          page_name:'cloud_shop_v2',
          where:[],
          selectRow:{
            shop_code:"",
            shop_name:"",
            shop_activity_name:"",
            shop_owner_name:"",
            shop_owner_id:"",
            shop_id: '',
            business_department:''
          },
          btns: [
          {
                type:'success',
                txt:'刷新',
                click(){
                self.fresh();
                }
            },
            {
                type:'primary',
                txt:'确认',
                click(){
                self.confirm();
                }
            }
        ],
          cols:[
            {
              label:"店铺编码",
              prop:"shop_code",
            },
            {
              label:"活动店铺名称",
              prop:"shop_activity_name",
            },
            {
                label: '事业部',
                format: "auxFormat",
                formatParams: "business_division",
                prop: 'business_department'
            },{
                label: '店铺主营类目',
                format: "auxFormat",
                formatParams: "main_sale_categories",
                prop: 'main_sale_categorie'
            },  
            {
              label:"店铺分类",
              prop:"shop_type_name",
            },
          ],
          search: {
           key:"",
          },
        }
      },
      methods: {
        getList(resolve){
          let params = {
            shop_activity_name: this.search.key ? this.search.key : "",
            appointment_activity_id:this.params.appointment_activity_id,
            clue_city_id:this.params.clue_city_id,
            page:{
              length:this.pageSize,
              pageNo:this.pageNo,
            },
            page_name:this.page_name,
            where:this.where,
          };
          this.ajax.postStream('/crm-web/api/crm_appointment_activity/appointmentShopList', params, res=> {
            if(res.body.result){
              this.dataList = res.body.content.list || [];
              this.pageTotal = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve()
          }, err => {
            resolve && resolve()
            this.$message.error(err);
          });
        },
        confirm(){
          //确认选择
          //let self = this;
          console.log("selectRow:%s",this.selectRow.shop_code);
          if(this.selectRow.shop_code === ""){
            this.$message({
              message:'请选择店铺！',
              type:'error'
            });
            return;
          }
          //关闭弹窗
          this.params.callback(this.selectRow);
          this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
        },
        fresh() {
          this.reset();
          this.getList();
        },
        reset(){
          this.search.key = "";
          this.selectRow = {
            shop_code:"",
            shop_name:"",
            shop_activity_name:"",
            shop_owner_name:"",
            shop_owner_id:"",
            shop_id: '',
            business_department:''
          };
        },
        // 选择事件
        select(s){
          //获取话务员账户
          this.selectRow.shop_code = s.shop_code;
          this.selectRow.shop_name = s.shop_name;
          this.selectRow.shop_activity_name = s.shop_activity_name;
          this.selectRow.shop_owner_id = s.customer_source_id;
          this.selectRow.shop_owner_name = s.customer_source_name;
          this.selectRow.shop_id = s.shop_id;
          this.selectRow.business_department = s.business_department
        },
        // 监听每页显示数更改事件
        pageChange(pageSize){
          this.pageSize = pageSize;
          this.getList();
        },
        // 监听页数更改事件
        currentPageChange(page){
          this.pageNo = page;
          this.getList();
        },
        searchClick(obj, resolve) {
            this.where = obj
            this.getList(resolve);
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      },
    }
</script>

<style scoped>

</style>
