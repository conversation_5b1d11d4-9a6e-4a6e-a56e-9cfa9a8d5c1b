<template>
  <div class="xpt-flex">
    <div class="header-main">
        <el-button size="mini" type="primary" @click="close">确认</el-button>
        <div>
            <xpt-select-aux v-model='params.businessDivision' aux_name='business_division'></xpt-select-aux>
            <xpt-select-aux v-model='params.main_sale_categorie' aux_name='main_sale_categories'></xpt-select-aux>
            <el-input size="mini" style="margin-right: 10px" v-model="selectId" placeholder="请输入店铺名称(模糊查询)"></el-input>
            <el-button size="mini" type="primary" @click="searchData">搜索</el-button>
            <el-button size="mini" type="primary" @click="reset">重置</el-button>
        </div>
    </div>
    <xpt-list
      ref='table'
      orderNo
      :showHead="false"
      :data='list'
      :btns='btns'
      :colData='cols'
      :pageTotal='totalPage'
      @radio-change='radioChange'
      @selection-change='selectChange'
      @page-size-change='pageSizeChange'
      @current-page-change='pageChange'
    >
    </xpt-list>
  </div>
</template>

<script>
  export default {
    props:['params'],
    name: 'list',
    components: {},
    data() {
      let self = this;
      return {
        btns: [
          {
            type:'primary',
            txt:'确认',
            click(){
              self.close();
            }
          }
        ],
        cols: [
          {
            label: '店铺名称',
            prop: 'shop_name',
          },
          {
            label: '活动店铺名称',
            prop: 'shop_activity_name',
          },
          {
            label: '省份',
            prop: 'shop_province_name',
          },
          {
            label: '市',
            prop: 'shop_city_name',
          },
          {
            label: '店铺类型',
            prop: 'shop_type_name'
          }
        ],
        list: [],
        search:{
          page_name: "cloud_shop_v2",
          where: [],
          page: {
            length: 50,
            pageNo: 1
          }
        },
        totalPage: 0,
        selectId:'',
        uploadUrl: '/order-web/api/scmexhibitionmaterial/import',
        code:''
      }

    },
    watch: {},
    methods: {
      handleChange(e){  
        this.getList();
      },
      radioChange(data) {
        this.selectData = data;
      },
      close() {
        if (!this.selectData) {
          this.$message.error('请先选择一个批次');
          return;
        }
        this.params.callback(this.selectData);
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      },
      selectChange(data){
        this.selectData = data;
      },
      reset() {
        this.selectId = '';
        this.code = ''
        this.getList()
      },
      searchData(obj, resolve) {
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page.length = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page.pageNo = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve) {
        console.log(this.params);
        this.$request('/crm-web/api/crm_appointment_activity/appointmentShopList',
          {
            shop_name:this.selectId,
            ...this.search,
            group_code: this.code || null,
            business_department:this.params.businessDivision,
            main_sale_categorie:this.params.main_sale_categorie,
          }).then(res => {
          if (res.result) {
            this.list = res.content.list || [];
            this.totalPage = res.content.count;
          }
          resolve && resolve();
        }).catch(err => {
          resolve && resolve();
        }).finally(() => {
          resolve && resolve();
        })
      },
    },
    computed: {},
    created() {
        this.getList();
    },
    mounted() {
    },
    destroyed() {
    }
  }
</script>

<style scoped>
    .header-main{
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        height: 37px;
        border-bottom: 2px solid #dfe6ec;
        margin-bottom: 10px;
    }
</style>
