<!-- 预约活动列表 --> 
<template>
    <xpt-list  
        :data='goodsList' 
        :btns='goodsBtns' 
        :colData='goodsCols' 
        orderNo 
        isNeedClickEvent 
        @selection-change='goodsRadioChange' 
        :pageTotal='goodsCount' 
        :searchPage='goodsQuery.page_name' 
        @search-click='goodsListSearch' 
        :selection='selection'
        @page-size-change='goodsPageSizeChange' 
        @radio-change='goodsRadioChange'
        @current-page-change='goodsPageChange'>
        <el-button v-show="!(params && params.ifAlert)" slot='btns' size="mini" type="primary" @click="handleImport"
          >导入</el-button
        >
    </xpt-list>
</template>
<script>
    import fn from '@common/Fn.js'
    export default {
        props: ['params'],
        data() {
            let self = this;
            return {
                goodsList: [],
                goodsBtns: [
                    {
                        type: 'success',
                        txt: '刷新',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.refresh()
                        }
                    }, {
                        type: 'primary',
                        txt: '新增',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.goodsAdd()
                        }
                    }, {
                        type: "info",
                        txt: "导入结果",
                        loading: false,
                        click() {
                            self.downloadExcel()
                        }
                    }
                ],
                goodsCols: [
                    {
                        label: '预约活动编号',
                        prop: 'appointmentActivityNo',
                        width: 100,
                        redirectClick(row) {
                            let params = {};
                            params.id = row.appointmentActivityId;
                            self.$root.eventHandle.$emit('creatTab', {
                                name: "预约活动详情",
                                params: params,
                                component: () => import('@components/appoint_management/appointment_activity_detail.vue')
                            });
                        },
                    }, {
                        label: '预约活动名称',
                        prop: 'appointmentActivityName',
                        width: 120
                    }, {
                        label: '活动类型',
                        prop: 'activityType',
                        formatter(val) {
                        	switch(val) {
                        		case 'DAILY': return '日常活动'; break;
                        		case 'OPENING': return '开业活动'; break;
                        		case 'SPECIAL': return '专场活动'; break;
                        		default: return val; break;
                        	}
                        },
                    }, {
                        label: '开始日期',
                        prop: 'startTime',
                        width: 130,
                        format: 'dataFormat1'
                    }, {
                        label: '结束日期',
                        prop: 'endTime',
                        width: 130,
                        format: 'dataFormat1'
                    }, {
                        label: '状态',
                        prop: 'status',
                        formatter(val) {
                        	switch(val) {
                        		case 'CREATED': return '创建'; break;
                                case 'PUBLISHED': return '已发布'; break;
                                case 'END': return '已结束'; break;
                        		default: return val; break;
                        	}
                        }
                    }, {
                        label: '浏览终端',
                        prop: 'browsingTerminal',
                        formatter(val) {
                        	switch(val) {
                        		case 'ALL': return '所有'; break;
                                case 'PC_CLIENT': return 'PC端'; break;
                                case 'MOBILE_CLIENT': return '客户端'; break;
                        		default: return val; break;
                        	}
                        }
                    }, {
                        label: '预约权益',
                        prop: 'appointmentRights'
                    },
                    {
                        label: '事业部',
                        width:120,
                        format: "auxFormat",
                        formatParams: "business_division",
                        prop: 'businessDivision'
                    },
                    {
                        label: '店铺主营类目',
                        width:200,
                        format: "auxFormat",
                        formatParams: "main_sale_categories",
                        prop: 'main_sale_categorie'
                    },  {
                        label: '发布人',
                        prop: 'pubilishName'
                    }, {
                        label: '发布时间',
                        prop: 'pubilishTime',
                        format: 'dataFormat1',
                        width: 130
                    }, {
                        label: '预约次数',
                        prop: 'appointCount'
                    }, {
                        label: '备注',
                        prop: 'remark'
                    }, {
                        label: '站外活动编码',
                        prop: 'stationActivityNo',
                        width: 90
                    }, {
                        label: '创建人',
                        prop: 'createName'
                    }, {
                        label: '创建时间',
                        prop: 'createTime',
                        format: 'dataFormat1',
                        width: 130
                    }, {
                        label: '投放模式',
                        prop: 'launchMode',
                        formatter(val){
                            return !val ? val : __AUX.get('launch_mode').filter(item=>item.code == val)[0].name
                        }
                    }
                ],
                goodsSelect: [],
                goodsCount: 0,
                goodsQuery: {
                    page_name: "crm_appointment_activity",
                    where: [],
                    page_size: 50,
                    page_no: 1,
                    // 是否过滤,1为不过滤，不传则默认过滤
                    if_need_page: 'Y'
                },
                otherParams: {
                    ifAppointment: true
                },
                uploadUrl: '/crm-web/api/crm_appointment_activity/import',
                ifSave: false,
                selection: 'checkbox'
            }
        },
        methods: {
            handleImport(){
                this.$root.eventHandle.$emit('alert', {
                    params: {
                        uploadUrl:this.uploadUrl,
                        otherParams:this.otherParams,
                        isupload:false,
                        filename:'预约活动导入标准模板',
                        uploadCallback:(data)=>{
                            let {result,msg} = data.body
                            if (result) {
                               this.$message.success(msg)
                               return
                            }
                            this.$message.error(msg)
                        },
                    },
                    component: () => import("@components/common/template-download-dialog.vue"),
                    style: 'width:400px;height:150px',
                    title: '导入'
                });
            },
            close () {
                if(!this.goodsSelect){
                    this.$message.error('请先选择一个预约活动');
                    return;
                }
                this.params.callback(this.goodsSelect);
                this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
            },
            // 行选中
            goodsRadioChange(row) {
                this.goodsSelect = row;
            },
            // 导入后的回调函数
            uploadCallback(data) {
                if (data.body.result) {
                    this.getGoodsList()
                }
            },
            // 查看导入结果
            downloadExcel() {
                this.$root.eventHandle.$emit('alert', {
                    params: {},
                    component: () => import("@components/appoint_management/appointment_import_result.vue"),
                    style: 'width:800px;height:400px',
                    title: '导入结果'
                });
            },
            refresh() {
                this.getGoodsList()
            },
            // 行选中
            goodsRadioChange(row) {
                this.goodsSelect = row;
            },
            // 获取商品信息
            getGoodsList(resolve, ifPromiss) {
                let self = this;
                let data = JSON.parse(JSON.stringify(this.goodsQuery));
                let url = '/crm-web/api/crm_appointment_activity/list'
                if (this.params.source == 'callFollow') {
                  data.where.push({
                    condition: "AND",
                    field: "9acb44549b41563697bb490144ec6258",
                    listWhere: [],
                    operator: "=",
                    table: "d8a4cc02b39d718f9b5005ec1e091e73",
                    value: "PUBLISHED",
                  })
                }
                this.ajax.postStream(url, data, d => {
                    if (d.body.result && d.body.content) {
                        this.goodsList = d.body.content.list
                        this.goodsCount = d.body.content.count
                    }
                    if (d.body.result) { 
                        this.$message.success(d.body.msg)
                    } else {
                        this.$message.error(d.body.msg)
                    }
                    resolve && resolve();
                }, (e) => {
                    this.$message.error(e);
                    resolve && resolve();
                })
            },
            //通用查询搜索
            goodsListSearch(where, reslove) {
                this.goodsQuery.where = where
                this.getGoodsList(reslove)
            },
            // 当前页改变
            goodsPageSizeChange(ps) {
                this.goodsQuery.page_size = ps;
                this.getGoodsList();
            },
            // 当前页面显示行数改变
            goodsPageChange(page) {
                this.goodsQuery.page_no = page;
                this.getGoodsList();
            },
            // 新增，复制新增
            goodsAdd() {
                this.$root.eventHandle.$emit('creatTab', 
                {
                    name:'预约活动新增', 
                    component:()=>import('@components/appoint_management/appointment_activity_detail.vue')
                });
            }
        },
        computed: {},
        watch: {},
        mounted() {
          if (this.params && this.params.selection) {
            this.selection = this.params.selection
          }
          if (this.params.ifAlert) {
            this.goodsBtns = [
              {
                type: 'primary',
                txt: '确认',
                click: this.close
              }
            ]
            this.goodsCols = [
              {
                label: '预约活动编号',
                prop: 'appointmentActivityNo',
                width: 100
              }, {
                label: '预约活动名称',
                prop: 'appointmentActivityName',
                width: 120
              }, {
                label: '活动类型',
                prop: 'activityType',
                formatter(val) {
                  switch(val) {
                    case 'DAILY': return '日常活动'; break;
                    case 'OPENING': return '开业活动'; break;
                    case 'SPECIAL': return '专场活动'; break;
                    default: return val; break;
                  }
                },
              }, {
                label: '开始日期',
                prop: 'startTime',
                width: 130,
                format: 'dataFormat1'
              }, {
                label: '结束日期',
                prop: 'endTime',
                width: 130,
                format: 'dataFormat1'
              }, {
                label: '状态',
                prop: 'status',
                formatter(val) {
                  switch(val) {
                    case 'CREATED': return '创建'; break;
                    case 'PUBLISHED': return '已发布'; break;
                    case 'END': return '已结束'; break;
                    default: return val; break;
                  }
                }
              }
            ]
          }
          this.getGoodsList()
        }
    }
</script>
<style type="text/css">
    .el-dialog__wrapper {
        background-color: transparent
    }

    .xiudounb {
        color: red
    }
</style>