
<!-- 预约活动详情-->
<template>
<div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
		<el-col :span="24">
			<el-button
				:type='btn.type'
				size='mini'
				v-for='(btn, index) in topBtns'
				:key='"good" + index'
				:disabled=" typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled|| false "
				:loading="btn.loading||false"
				@click='btn.click'
			>{{btn.txt}}</el-button>
		</el-col>
	</el-row>
	<div>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基本信息" name="appointment">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="duopai" :rules="rules" ref="form">
						<el-col :span='6'>
							<el-form-item label="预约活动编号：" prop="appointmentActivityNo">
                                <el-input readonly size='mini' v-model='duopai.appointmentActivityNo' disabled ></el-input>
                            </el-form-item>
							<el-form-item label="预约活动名称：" prop="appointmentActivityName">
								<el-input v-model="duopai.appointmentActivityName" size='mini' :disabled="duopai.status==='END'"></el-input>
                                <el-tooltip v-if='rules.appointmentActivityName[0].isShow' class="item" effect="dark" :content="rules.appointmentActivityName[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
							</el-form-item>
                            <el-form-item label="浏览终端：" prop="browsingTerminal">
                                <el-select placeholder="请选择" size='mini' v-model="duopai.browsingTerminal" :disabled="duopai.status == 'PUBLISHED'||duopai.status=='END'">
                                    <el-option label="所有" value="ALL"></el-option>
                                    <el-option label="PC端" value="PC_CLIENT"></el-option>
                                    <el-option label="客户端" value="MOBILE_CLIENT"></el-option>
                                </el-select>
                                <el-tooltip v-if='rules.browsingTerminal[0].isShow' class="item" effect="dark" :content="rules.browsingTerminal[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </el-form-item>
                            <el-form-item label="发布人：" prop="pubilishName">
								<el-input v-model="duopai.pubilishName" prop="pubilishName" disabled size='mini' ></el-input>
							</el-form-item>
						</el-col>
						<el-col :span='6'>
                            <el-form-item label="活动类型：" prop="activityType">
                                <el-select placeholder="请选择" size='mini' v-model="duopai.activityType" :disabled="duopai.status == 'PUBLISHED'||duopai.status=='END'">
                                    <el-option label="日常活动" value="DAILY"></el-option>
                                    <el-option label="开业活动" value="OPENING"></el-option>
                                    <el-option label="专场活动" value="SPECIAL"></el-option>
                                </el-select>
                                <el-tooltip v-if='rules.activityType[0].isShow' class="item" effect="dark" :content="rules.activityType[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </el-form-item>
							<el-form-item label="状态："  prop="status">
                                <el-select placeholder="请选择" size='mini' v-model="duopai.status" disabled>
                                    <el-option label="创建" value="CREATED"></el-option>
                                    <el-option label="已发布" value="PUBLISHED"></el-option>
                                    <el-option label="已结束" value="END"></el-option>
                                </el-select>
							</el-form-item>
                            <el-form-item label="预约次数："  prop="appointCount" >
								<el-input v-model="duopai.appointCount" disabled  size='mini' ></el-input>
							</el-form-item>
                            <el-form-item label="发布时间：" prop="pubilishTime">
								<el-date-picker v-model="duopai.pubilishTime" type="date" placeholder="选择日期" size='mini' :disabled='true' :editable='false'></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span='6'>
                            <el-form-item label="开始日期："  prop="startTime">
								<el-date-picker v-model="duopai.startTime" type="datetime" placeholder="选择日期" size='mini' :editable='duopai.status == "CREATED"? false : true'  :disabled='duopai.status == "CREATED"? false : true' :picker-options='enableDateOptions'></el-date-picker>
								<el-tooltip v-if='rules.startTime[0].isShow' class="item" effect="dark" :content="rules.startTime[0].message" placement="right-start" popper-class='xpt-form__error'>
                 					  <i class='el-icon-warning'></i>
                 				 </el-tooltip>
							</el-form-item>
							<el-form-item label="结束日期："  prop="endTime" >
								<el-date-picker v-model="duopai.endTime" type="datetime" placeholder="选择日期" size='mini' :editable='false' :disabled='duopai.status=="END"' :picker-options='disableDateOptions'></el-date-picker>
								<el-tooltip v-if='rules.endTime[0].isShow' class="item" effect="dark" :content="rules.endTime[0].message" placement="right-start" popper-class='xpt-form__error'>
                 					  <i class='el-icon-warning'></i>
                 				</el-tooltip>
							</el-form-item>
                            <el-form-item label="创建人：" prop="createName">
								<el-input v-model="duopai.createName" size='mini' disabled></el-input>
							</el-form-item>
                            <el-form-item label="创建时间：" prop="createTime">
								<el-date-picker v-model="duopai.createTime" type="date" placeholder="选择日期" size='mini' :disabled='true' :editable='false'></el-date-picker>
							</el-form-item>
						</el-col>
                        <el-col :span="6">
                            <el-form-item label="外部活动编号：" prop="stationActivityNo" >
           						<xpt-input size='mini' v-model="duopai.stationActivityNo" :disabled='duopai.status == "CREATED"? false : true' ></xpt-input>
                                <!-- <el-tooltip v-if='rules.stationActivityNo[0].isShow' class="item" effect="dark" :content="rules.stationActivityNo[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip> -->
          					</el-form-item>
                            <el-form-item label="更新人：" prop="modifyName">
								<el-input v-model="duopai.modifyName" size='mini' disabled></el-input>
                        </el-form-item>
                          <el-form-item label="更新时间：" prop="modifyTime">
                          <el-date-picker v-model="duopai.modifyTime" type="date" placeholder="选择日期" size='mini' :disabled='true' :editable='false'></el-date-picker>
                        </el-form-item>
                        <el-form-item label="店铺主营类目：" prop="main_sale_categorie">
                          <!-- <el-input v-model="duopai.main_sale_categorie" size='mini' disabled></el-input> -->
               		        <xpt-select-aux v-model='duopai.main_sale_categorie' aux_name='main_sale_categories' :disabled='(duopai.status == "CREATED"? false : true) || !duopai.businessDivision'></xpt-select-aux>
                           <el-tooltip v-if='rules.main_sale_categorie[0].isShow' class="item" effect="dark" :content="rules.main_sale_categorie[0].message" placement="right-start" popper-class='xpt-form__error'>
                                  <i class='el-icon-warning'></i>
                              </el-tooltip>
                        </el-form-item>
                        <el-form-item label="事业部：" prop="businessDivision">
               		        <xpt-select-aux v-model='duopai.businessDivision' aux_name='business_division' :disabled='duopai.status == "PUBLISHED" ? true : false'></xpt-select-aux>
                           <!-- <el-tooltip v-if='rules.businessDivision[0].isShow' class="item" effect="dark" :content="rules.businessDivision[0].message" placement="right-start" popper-class='xpt-form__error'>
                                  <i class='el-icon-warning'></i>
                              </el-tooltip> -->
                        </el-form-item>
                        </el-col>
                        <el-col :span='18'>
                            <el-form-item label="备注：" :class="$style['row-height'] " class="mgb5" prop="remark">
                                <el-input type="textarea" size='mini' v-model="duopai.remark" :maxlength='250' :disabled='duopai.status=="END"'></el-input>
                            </el-form-item>
                            <el-form-item label="预约权益：" :class="$style['row-height']" prop="appointmentRights">
                                <el-input type="textarea" size='mini' v-model="duopai.appointmentRights" :maxlength='250' :disabled='duopai.status=="END"'></el-input>
                                <el-tooltip v-if='rules.appointmentRights[0].isShow' class="item" effect="dark" :content="rules.appointmentRights[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </el-form-item>
                            <el-form-item label="投放模式：" :class="$style['row-height']" prop="launchMode">
                                <div class="flexBox">
               		                <xpt-select-aux v-model='duopai.launchMode' aux_name='launch_mode' :disabled="duopai.status == 'PUBLISHED'||duopai.status=='END'"></xpt-select-aux>
                                    <el-tooltip v-if='rules.launchMode[0].isShow' class="item" effect="dark" :content="rules.launchMode[0].message" placement="right-start" popper-class='xpt-form__error'>
                                        <i class='el-icon-warning'></i>
                                    </el-tooltip>
                                    <svg t="1672884021388" class="item icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5051" width="16" height="16" @click="questionVisible = true">
                                        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" p-id="5052" fill="#bfbfbf"></path>
                                        <path d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7c0-19.7 12.4-37.7 30.9-44.8 59-22.7 97.1-74.7 97.1-132.5 0.1-39.3-17.1-76-48.3-103.3z" p-id="5053" fill="#bfbfbf"></path>
                                        <path d="M512 732m-40 0a40 40 0 1 0 80 0 40 40 0 1 0-80 0Z" p-id="5054" fill="#bfbfbf"></path>
                                    </svg>
                                </div>
                            </el-form-item>
                        </el-col>
			    	</el-form>
			 	 </el-tab-pane>
          <el-tab-pane label="短信配置" name="text">
            <xpt-headbar class="topBtn">
              <el-button size='mini'  type="primary" @click="textPop" slot='left' :disabled="duopai.appointDefaultNote!=='Y'">短信模板覆盖</el-button>
              <el-button type='primary'  size='mini' @click="setDefault" slot='left' :disabled="duopai.status=='END'">设为默认</el-button>
            </xpt-headbar>
          <el-form label-position="right" class="mgt10" label-width="100px" :model="duopai" :rules="rules" ref="duopai">
            <el-col :span='16'>
              <el-form-item label="预约成功信息：" style="height: 60px">
                <el-row>
                  <el-button size="mini" :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'" @click="quickInput('{预约活动名称}')" >预约活动名称</el-button>
                  <el-button size="mini" :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'" @click="quickInput('{预约活动权益}')">预约活动权益</el-button>
                  <el-button size="mini" :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'" @click="quickInput('{预约活动开始时间}')" >预约活动开始时间</el-button>
                  <el-button size="mini" :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'" @click="quickInput('{预约活动结束时间}')" >预约活动结束时间</el-button>
                  <el-button size="mini" :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'" @click="quickInput('{预约码}')" >预约码</el-button>
                </el-row>
                <el-row>
                  <el-input
                    size='mini'
                    ref="appointSuccessedNote"
                    id="appointSuccessedNote"
                    :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'"
                    style="width: 700px"
                    v-model='duopai.appointSuccessedNote'>
                  </el-input>
                </el-row>
              </el-form-item>
              <el-form-item label="预约门店信息：" style="height: 60px" prop="appointShopNote">
                <el-row>
                  <el-button size="mini" :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'" @click="quickEndMsgInput('{活动店铺名称}')">活动店铺名称</el-button>
                  <el-button size="mini" :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'" @click="quickEndMsgInput('{门店地址}')">门店地址</el-button>
                  <el-button size="mini" :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'" @click="quickEndMsgInput('{门店电话}')">门店电话</el-button>
                </el-row>
                <el-row>
                  <el-input
                    style="width: 700px"
                    ref="appointShopNote"
                    :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'"
                    id="appointShopNote"
                    v-model="duopai.appointShopNote"
                    size='mini'>
                  </el-input>
                </el-row>
              </el-form-item>
              <el-form-item style="width: 550px" label="短信收尾信息：" prop="appointEndNote">
                <el-input
                  style="width: 700px"
                  ref="appointEndNote"
                  :disabled="duopai.status==='PUBLISHED'||duopai.status=='END'"
                  v-model="duopai.appointEndNote"
                  size='mini'>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="短信预览："  style="height: 120px">
                <el-input
                  :class="$style['resizeNone']"
                  :autosize="false"
                  :rows="5"
                  readonly
                  type="textarea"
                  v-model="preview"
                  size='mini'></el-input>
              </el-form-item>
              <el-form-item label="是否默认：" prop="appointDefaultNote">
                <el-select
                  disabled
                  v-model="duopai.appointDefaultNote"
                  size='mini'>
                  <el-option label="是" value="Y"></el-option>
                  <el-option label="否" value="N"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>

      </el-tabs>
		</el-row>
	</div>
    <el-row class="xpt-flex__bottom" v-fold>
        <el-tabs v-model="secondTab">
            <el-tab-pane label="平台推广" name="activityInfo" class='xpt-flex'>
                <xpt-list
                    ref='appointment_Activity_detail'
                    id='appointment_Activity_detail'
                    :data='materiaListInfoData.materiaListData'
                    :colData='materiaListInfoData.materiaListCols'
                    :btns='materiaListInfoData.materiaListBtns'
                    :orderNo= true
                    :selectable='selectableFun'
                    isNeedClickEvent
                    :pageTotal="materiaListInfoData.materiaListCount"
                    @selection-change='langeRadioChange'
                    :searchPage='materiaListQuery.page_name'
                    @search-click='serchMaterial'
                    @page-size-change='materiaListPageSizeChange'
                    @current-page-change='materiaListPageChange'
                ></xpt-list>
            </el-tab-pane>
            <el-tab-pane label="问题设置" name="question" class='xpt-flex'>
              <xpt-list
                :data='question.questionData'
                :colData='question.questionCols'
                :btns='question.questionBtns'
                @selection-change='questionSelectChange'
                isNeedClickEvent>
                <template slot='problem_order' slot-scope='scope'>
                  <el-input style="width: 70px" :maxlength="20" :disabled="!!scope.row.id"  size="mini" :min="1" v-model="scope.row.problem_order"></el-input>
                </template>
                <template slot='problem_describe' slot-scope='scope'>
                  <el-input style="width: 300px" :disabled="!!scope.row.id"  size="mini" :maxlength="20" :min="1" v-model="scope.row.problem_describe"></el-input>
                </template>
              </xpt-list>
            </el-tab-pane>
            <el-tab-pane label="页面设计" name="imgArrange" class='xpt-flex'>
              <xpt-list
                :data='imgArrange.imgArrangeData'
                :colData='imgArrange.imgArrangeCols'
                :btns='imgArrange.imgArrangeBtns'
                isNeedClickEvent
                orderNo
                @selection-change='imgSelectChange'>
                <template slot='picture_order' slot-scope='scope'>
                  <el-input :disabled="!scope.row.tempId" :maxlength="4" size="mini" :min="1" v-model="scope.row.picture_order"></el-input>
                </template>
                <template slot='picture_url' slot-scope='scope'>
                  <a href="javascript:;" @click="viewOrUpload(scope.row,scope)">查看或上传附件</a>
                </template>
              </xpt-list>
            </el-tab-pane>
            <el-tab-pane label="实施店铺" name="shopImplementation" class='xpt-flex'>
              <xpt-list
                :data='shopImplementation.shopImplementationData'
                :colData='shopImplementation.shopImplementationCols'
                :btns='shopImplementation.shopImplementationBtns'
                :orderNo= true
                isNeedClickEvent
                @selection-change='shopSelectChange'
                :pageTotal="shopImplementation.count"
                :searchPage="shopImplementation.query.page_name"
                @search-click='shopImplementationQuerySearchClick'
                @page-size-change='shopImplementationQueryPageSizeChange'
                @current-page-change='shopImplementationQueryPageChange'
              >
              <el-button v-show="!(params && params.ifAlert)" slot='btns' size="mini" type="primary" @click="handleImport" :disabled="!(duopai && duopai.appointmentActivityId)">批量导入</el-button>
            </xpt-list>
            </el-tab-pane>
        </el-tabs>
    </el-row>
      <xpt-image-v3
        :isGetImgList="isGetImgList"
        @path="getPath"
        @delImg="delImgCatch"
        :show="isShow"
        :paramsInfo="paramsInfo"
        :ifUpload="true"
        :dataObj="dataObj"
        @close="xptImageClose">
      </xpt-image-v3>
      <el-dialog
        :visible.sync="questionVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="dialog-container-appointment-activity-detail"
    >
      <div>
        <p>投放模式会决定话务跟进线索时——话务跟进详情——选择店铺，有哪些可选择的店铺范围</p>
        <p>1、总部投放：选择店铺时，可选择配置店铺之外的店铺；</p>
        <p>2、店铺投放：选择店铺时，只能选择配置范围内的店铺；</p>
      </div>
    </el-dialog>
</div>
</template>
<script>
  import validate from '@common/validate.js';
  import xptImageV3 from './imageView_v2'
  export default {
		props: ['params'], //上游参数
    components: {xptImageV3},
    data() {
			var self = this;//本vue
			return{
				firstTab:"appointment",
                secondTab: 'activityInfo',
                bill_type_id_tag: '',
                questionVisible:false,
				duopai:{
                    appointmentActivityNo: '',
                    appointmentActivityName: '',
                    browsingTerminal: '',
                    pubilishName: '',
                    activityType: '',
                    status: 'CREATED',
                    appointCount: '',
                    pubilishTime: '',
                    startTime: '',
                    endTime: '',
                    stationActivityNo: '',
                    createName: '',
                    createTime: '',
                    modifyName: '',
                    modifyTime: '',
                    remark: '',
                    appointmentRights: '',
                    appointShopNote: '',
                    appointEndNote: '',
                    appointSuccessedNote: '',
                    main_sale_categorie:'',
                    businessDivision:'',
                    launchMode:''
				},//表单内容
				rules:{
                    ...[
                        {
                            appointmentActivityName: '预约活动名称'
                        }, {
                            browsingTerminal: '浏览终端'
                        }, {
                            activityType: '活动类型'
                        }, {
                            startTime: '开始日期'
                        }, {
                            endTime: '结束日期'
                        }, {
                            appointmentRights: '预约权益'
                        }, {
                            main_sale_categorie: '店铺主营类目'
                        },{
                            launchMode: '投放模式'
                        }
                    ].reduce((a, b) => {
                        var key = Object.keys(b)[0]
                        a[key] = validate.isNotBlank({
                            self: this,
                            msg: '请填写' + b[key],
                        })
                        return a
                    }, {})
				},
				topBtns: [
					{
						type: 'primary',
						txt: '刷新',
						disabled () {
                            return self.params.id ? false : true
                        },
						click () {
                            self.getMaterialList()
                        }
					}, {
						type: 'primary',
						txt: '新增',
						disabled () {
                            return self.duopai.status == 'END'
                        },
						click: self.addNew
					}, {
						type: 'success',
						txt: '保存',
                        disabled () {
                            return self.duopai.status == 'END'
                        },
						click () {
							self.save()
						}
					}, {
						type: 'success',
						txt: '发布',
                        disabled () {
                            return self.duopai.status == 'CREATED' ? false : true
                        },
						click () {
							self.pulish()
						}
					}
				],
				materiaListInfoData: {
					materiaListData: [],
					materiaListCols: [
                        {
							label: '留资编码',
                            prop: 'platformNo',
                            // width: 150,
							// bool: true,
							// iconClick(row) {
							// 	setTimeout(self.platformChoose, 10)
							// },
							// disabled(row) {
							// 	return row.platformPushId ? true : false
							// }
						}, {
							label: '留资名称',
							prop: 'platformName',
							width: 150
						}, {
							label: '平台渠道',
                            prop: 'platformChannel'
						}, {
                            label: '平台类型',
                            prop: 'platformType',
                            formatter(val) {
                                switch(val) {
                                    case 'SYSTEM': return '系统'; break;
                                    case 'STATION': return '外部'; break;
                                    case 'IMPORT': return '导入'; break;
                                    default: return val; break;
                                }
                            }
                        }, {
							label: '推广计划ID',
                            prop: 'actPlanId',
                            bool: true,
                            elInput: true,
                            disabled(row) {
                                return row.platformPushId ? true : false
                            },
                            change(row) {
                                self.changeActPlanId(row)
                            },
                            blur: (row, e) => {},
                            width: 140
						}, {
							label: '推广计划名称',
                            prop: 'actPlanName',
                            bool: true,
                            elInput: true,
                            disabled(row) {
                                return row.platformPushId ? true : false
                            },
                            change(row) {},
                            blur: (row, e) => {},
                            width: 140
						}, {
              label: '预约链接',
              prop: 'appointmentUrl'
            },{
							label: '创建人',
							prop: 'createName'
						}, {
							label: '创建时间',
							prop: 'createTime',
							format: 'dataFormat1',
							width: 130
						}
                    ],
                    materiaListCount: 0,
                    materiaListBtns: [
                        {
                            type: 'primary',
                            txt: '添加',
                            disabled(){
                                return self.duopai.status=="END"
                            },
                            click: self.materiasAdd
                        }, {
                            type: 'danger',
                            txt: '删除',
                            disabled() {
                                return false
                            },
                            click: self.deleteMaterial
                        },{
                          type: 'primary',
                          txt: '生成预约链接',
                        disabled() {
                          return this.saveStatus||self.duopai.status=="END"
                        },
                          click: self.generateReservationLink
                        },
                        {
                            type: 'warning',
                            txt: '复制',
                            disabled(){
                                return self.duopai.status=="END"
                            },
                            click: self.selectActivity
                        }
                    ]
				},
				materiaListOld: [], // 旧的数据做对比
				materiaSelect: [],
				materiaListQuery: {
                    where: [],
                    page_size: 50,
                    page_no: 1,
                    if_need_page:'Y',
                    page_name: 'crm_appointment_platformpush'
                },
                activityDelete:[],
                enableDateOptions:{
                    /*生效时间允许选所有值*/
                    disabledDate(time){
                        return self.duopai.endTime ? time.getTime() > new Date(self.duopai.endTime) : false;
                    },
                    date:(function(){
                        var date = new Date();
                        var year = date.getFullYear();
                        var month = date.getMonth()+1;
                        var day = date.getDate();
                        var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
                        return new Date(time);
                    })()
                },
                disableDateOptions:{
                    disabledDate: time => {
                        /*新增时能选所有时间*/
                        return self.duopai.startTime ? time.getTime() < new Date(self.duopai.startTime) : false
                    },
                    date:(function(){
                        var date = new Date();
                        var year = date.getFullYear();
                        var month = date.getMonth()+1;
                        var day = date.getDate();
                        var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
                        return new Date(time);
                    })()
                },
        question: {
          select: [],
          questionData:[],
          addQuestion:[],
          questionDelList:[],
          questionCols:[
            {
            label: '问题顺序',
              slot: 'problem_order',
              width:80
            },
            {
              label: '问题模块',
              prop: 'problem_module',
              bool: true,
              elSelect: true,
              options:[
                {
                  label: '店铺',
                  value:'SHOP',
                },
                {
                  label: '姓名',
                  value:'NAME'},
                {
                  label: '电话',
                  value:'PHONE'},
                {
                  label: '需求',
                  value:'REQUIREMENT'}],
              disabled(row) {
                return row.id ? true : false
              },
              change: (r, i) => {
                if (r.problem_module) {
                  r.if_response = 'Y'
                }
              },
              width: 100,
            },
            {
              label: '问题描述',
              slot: 'problem_describe',
              width: 200
            },
            {
              label: '应答方式',
              prop: 'response_way',
              bool: true,
              elSelect: true,
              options:[
                {
                  label: '应答',
                  value:'ANSWER'
                }],
              disabled(row) {
                return row.id ? true : false
              },
              change: (r, i) => {},
              width: 100,
            },
            {
              label: '是否必答',
              prop: 'if_response',
              width: 80,
              bool: true,
              elSelect: true,
              options:[
                {
                  label: '是',
                  value:'Y'
                },
                {
                  label: '否',
                  value:'N'
                }],
              disabled(row) {
                // return !!row.id || ( !!row.problem_module )
                return !!row.id
              },
              change: (r, i) => {},
            },
            {
              label: '创建人',
              prop: 'create_name',
              width: 100
            },
            {
              label: '创建时间',
              prop: 'create_time',
              format: 'dataFormat1',
              width: 120
            },
          ],
          questionBtns:[
            {
              type: 'primary',
              txt: '添加',
              disabled() {
                return this.saveStatus||self.duopai.status=="END"
              },
              click: self.questionAdd
            }, {
              type: 'danger',
              txt: '删除',
              disabled() {
                return this.saveStatus||self.duopai.status=="END"
              },
              click: self.questionDel
            }
          ]
        },
        ifClickUpload:false,
        isGetImgList: false,//是否立即获取图片
        dataObj: {},
        paramsInfo: {},
        isShow: false,
        imgArrange: {
          count:0,
          select: [],
          addImgArrange:[],
          imgArrangeDelList:[],
          imgArrangeData:[],
          imgArrangeCols:[
            {
              label: '终端类型',
              prop: 'terminal_type',
              bool: true,
              elSelect: true,
              options:[
                {
                  label: 'PC端',
                  value:'PC'
                },
                {
                  label: '客户端',
                  value:'MOBILE'
                }],
              disabled(row) {
                return !row.tempId
              },
              change: (r, i) => {},
              width: 100
            },
            {
              label: '图片类型',
              prop: 'picture_type',
              bool: true,
              elSelect: true,
              options:[
                {
                  label: '页首',
                  value:'HEAD'
                },
                {
                  label: '页尾',
                  value:'FOOT'
                },
                {
                  label: '预约弹窗',
                  value:'POP'
                }],
              disabled(row) {
                return !row.tempId
              },
              change: (r, i) => {},
              width: 100
            },
            {
              label: '图片顺序',
              slot: 'picture_order',
            },
            {
              label: '图片地址',
              slot: 'picture_url',
            },
            {
              label: '创建人',
              prop: 'create_name',
            },
            {
              label: '创建时间',
              prop: 'create_time',
              format: 'dataFormat1'
            }
          ],
          imgArrangeBtns:[
            {
              type: 'primary',
              txt: '添加',
              disabled(row) {
                return this.saveStatus||self.duopai.status=="END"
              },
              click: self.imgArrangeAdd
            }, {
              type: 'danger',
              txt: '删除',
              disabled(row) {
                return this.saveStatus||self.duopai.status=="END"
              },
              click: self.imgArrangeDel
            }
          ]
        },
        shopImplementation: {
				  select:{},
                  count:0,
                  query:{
                    "page_name":"crm_appointment_shop_setting",
                    "where":[],
                    "page_size":50,
                    "page_no":1,
                    "if_need_page":"Y"
                  },
          shopImplementationData:[],
          copyShopImplementationData:[],
          shopImplementationAdd: [],
          shopImplementationDelList:[],
          shopImplementationCols:[
            {
              label: '店铺编码',
              prop: 'shop_code',
              width: 100
            },
            {
              label: '店铺',
              prop: 'shop_name',
              width: 100
            },
            {
              label: '活动店铺名称',
              prop: 'shop_activity_name',
              width: 200
            },
            {
                label: '事业部',
                width:120,
                format: "auxFormat",
                formatParams: "business_division",
                prop: 'business_division'
            },
            {
                label: '店铺主营类目',
                prop: 'main_sale_categorie',
                width: 120,
                format: "auxFormat",
                formatParams: "main_sale_categories"
            },
            {
              label: '省',
              prop: 'shop_province_name',
              width: 80
            },
            {
              label: '市',
              prop: 'shop_city_name',
              width: 150
            },
            {
              label: '状态',
              prop: 'enable_status',
              width: 150,
              switch: true,
              onText: '生效',
              offText: '失效',
              onValue: 1,
              offValue: 0,
              changeState(row){
                self.changeState(row)
              }
            },
            {
              label: '创建人',
              prop: 'create_name',
              width: 150
            },
            {
              label: '创建时间',
              prop: 'create_time',
              format: 'dataFormat1',
              width: 150
            }
          ],
          shopImplementationBtns:[
            {
              type: 'primary',
              txt: '添加',
              disabled() {
                return this.saveStatus||self.duopai.status=="END"
              },
              click: self.shopImplementationAdd
            }, {
              type: 'danger',
              txt: '删除',
              disabled() {
                return this.saveStatus || self.duopai.status === 'PUBLISHED'||self.duopai.status=="END"
              },
              click: self.shopDel
            }, {
              type: 'success',
              txt: '生效',
              click:self.takeEffect
            }, {
              type: 'warning',
              txt: '失效',
             click:self.loseEfficacy
            }, {
                type: "primary",
                txt: "导出店铺",
                click() {
                    self.exportExcel()
                },
                disabled() {
                    return !(self.duopai && self.duopai.appointmentActivityId)
                },
            }, {
                type: "info",
                txt: "导出结果",
                click() {
                    self.exportResult()
                }
            }, {
                type: "info",
                txt: "导入结果",
                click() {
                    self.downloadExcel()
                }
            }
          ]
        },
        saveStatus: false, // 保存状态
        pulishLoading: false,
        original: {}, //未进行任何修改时候的初始值

        uploadUrl: '/crm-web/api/crm_appointment_activity/import/shop/setting',
        otherParams: {
            ifAppointmentActivity: true,
            importMode: 1,
            appointmentActivityId: '',
        },
    }
		},
		methods:{
            // 导入
            handleImport(){
                this.otherParams.appointmentActivityId = this.duopai.appointmentActivityId
                this.$root.eventHandle.$emit('alert', {
                    params: {
                        uploadUrl:this.uploadUrl,
                        otherParams:this.otherParams,
                        isupload: false,
                        filename:'实施店铺导入模板',
                        uploadCallback:(data)=>{
                            let {result,msg} = data.body
                            if (result) {
                               this.$message.success(msg)
                               return
                            }
                            this.$message.error(msg)
                        },
                    },
                    component: () => import("@components/common/template-download-dialog.vue"),
                    style: 'width:400px;height:200px',
                    title: '批量导入'
                });
            },
            // 查看导入结果
            downloadExcel() {
                this.$root.eventHandle.$emit('alert', {
                    params: {
                        url:'/crm-web/api/crm_appointment_activity/shop/setting/import/list',
                        SQLUrl:'supplierInport'
                    },
                    component: () => import("@components/appoint_management/appointment_import_result.vue"),
                    style: 'width:800px;height:400px',
                    title: '导入结果'
                });
            },
            //导出
            exportExcel() {
                let params = {
                    "page_name":"crm_appointment_shop_setting",
                    "where":[],
                    "appointmentActivityId": this.duopai.appointmentActivityId
                }

                this.ajax.postStream(
                    "/crm-web/api/crm_appointment_activity/export/shop/setting",
                    params,
                    (res) => {
                        if (res.body.result) {
                            res.body.msg && this.$message.success(res.body.msg);
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                    }
                );
            },
            // 查看导出结果
            exportResult(){
                this.$root.eventHandle.$emit('alert', {
                    component: () => import('@components/after_sales_report/export'),
                    style:'width:900px;height:600px',
                    title: '导出结果',
                    params: {
                        query: {
                            type: 'EXCEL_CRM_SHOP_SETTING_EXPORT',
                        },
                    },
                })
            },
            pulish () {
                let self = this
                this.pulishLoading = true
                new Promise((resolve, reject) => {
                    this.save(resolve, reject);
                }).then(() => {
                    self.$root.eventHandle.$emit('openDialog', {
                        ok() {
                            self.comfirmPulish()
                        },
                        okTxt: '确定',
                        no() {
                            self.pulishLoading = false
                        },
                        noTxt: '取消',
                        txt: '确认发布该预约活动吗？',
                        noCancelBtn: true
                    })
                })
            },
            comfirmPulish() {
                let id = this.params.id ? this.params.id : this.duopai.appointmentActivityId, self = this
                this.ajax.postStream('/crm-web/api/crm_appointment_activity/release',id,function(response){
                    if(response.body.result) {
                        self.$message.success(response.body.msg)
                        self.getMaterialList();
                    } else {
                        self.$message.error(response.body.msg || '')
                    }
                    self.pulishLoading = false
                },err => {
                    self.pulishLoading = false
                    self.$message.error(err)
                });
            },
			 save(resolve, reject,callback){
                typeof(callback)==='function'&&callback()
                this.saveStatus = true;
                var self =  this;
        let saveData = JSON.parse(JSON.stringify(self.duopai));
        


       
        // let hasUpdate = this.compareData(this.form, this.params.__data);
        
				let materiaSaveData = this.materiaSaveData(), ifRequired = this.checkGoodListRequired()
				if (!ifRequired) {
                    reject && reject()
					return
				}
				if (!this.checkOnly(this.question.questionData,'问题')){
				  return;
                }
				if (!this.checkImg()){
				  return;
                }
				this.paramsDelId(self.question.addQuestion);
        this.paramsDelId(self.imgArrange.addImgArrange);
        this.paramsDelId(self.shopImplementation.shopImplementationAdd);
        console.log(saveData);
				let data = {
					          stationActivityNo: saveData.stationActivityNo,
                    appointDefaultNote: saveData.appointDefaultNote,
                    appointEndNote: saveData.appointEndNote,
                    appointShopNote: saveData.appointShopNote,
                    appointSuccessedNote: saveData.appointSuccessedNote,
                    main_sale_categorie:saveData.main_sale_categorie,
                    appointmentActivityName: saveData.appointmentActivityName,
                    appointmentRights: saveData.appointmentRights,
                    launchMode: saveData.launchMode,
                    browsingTerminal: saveData.browsingTerminal,
                    activityType: saveData.activityType,
                    startTime: saveData.startTime == '' ? '' : +new Date(saveData.startTime),
                    endTime: saveData.endTime == '' ? '' : +new Date(saveData.endTime),
                    remark: saveData.remark,
                    savedItems: materiaSaveData.all,
                    status: saveData.status,
                    appointCount: saveData.appointCount,
                    createName: saveData.createName,
                    createTime: saveData.createTime,
                    modifyName: saveData.modifyName,
                    modifyTime: saveData.modifyTime,
                    pubilishName: saveData.pubilishName,
                    pubilishTime: saveData.pubilishTime,
                    businessDivision:saveData.businessDivision,
                    deletedItems: self.activityDelete,
                    crmAppointmentProblemSettingsVOs: self.question.addQuestion,
                    crmAppointmentProblemSettingsIds: self.question.questionDelList,
                    crmAppointmentPictureDesignVOs: self.imgArrange.addImgArrange,
                    crmAppointmentPictureDesignIds: self.imgArrange.imgArrangeDelList,
                    crmAppointmentShopSettingsVOs: self.shopImplementation.shopImplementationAdd,
                    crmAppointmentShopSettingsIds: self.shopImplementation.shopImplementationDelList,
                }
                saveData.appointmentActivityId ? data.appointmentActivityId = saveData.appointmentActivityId : ''
                saveData.appointmentActivityNo ? data.appointmentActivityNo = saveData.appointmentActivityNo : ''
				if (this.materiaListInfoData.materiaListData && (this.materiaListInfoData.materiaListData.length <= 0)) {
                    this.$message.error('请至少添加一行平台推广明细')
                    reject && reject()
					return
				}
				self.$refs.form.validate((valid) => {
					if(!valid) {
                        reject && reject()
                        return
                    }
					this.ajax.postStream('/crm-web/api/crm_appointment_activity/save',data,function(response){
						if(response.body.result) {
							self.$message.success(response.body.msg)
                            self.duopai.appointmentActivityId = response.body.content.appointmentActivityId
                            let params = {
                                id:response.body.content.appointmentActivityId,
                                tabName: self.params.tabName
                            }
                            params.__close = function() {
							                self.params.__close = self.$root.eventHandle.$emit('removeTab',self.params.tabName)
                            };
                            if (!self.params.id) {
                                self.$root.eventHandle.$emit('updateTab',{
                                    name:self.params.tabName,
                                    params:params,
                                    title:'预约活动详情',
                                    component: () => import('@components/appoint_management/appointment_activity_detail.vue')
                                })
                            }
                            if (!self.pulishLoading) {
                                self.getMaterialList();
                            }
                            resolve && resolve()
            } else {
                if (!self.pulishLoading) {
                    self.getMaterialList();
                }
              self.$message.error(response.body.msg || '')
                            reject && reject()
						}
            self.saveStatus = false;
          },err => {
            self.getMaterialList();
            self.saveStatus = false;
            self.$message.error(err)
                        reject && reject()
                    });

				})

			},
			// 获取商品信息数据变更
			materiaSaveData() {
				let materiaOld = this.materiaListOld,
					materiaNow = this.materiaListInfoData.materiaListData,
					materiaAdd = [],
					materiaUpd = [],
					i;
				// 获取新增的和修改的物料
				i = materiaNow.length;

				while(i--) {
					if(materiaNow[i].platformPushId) { // 保存后修改数据
						// 获取更新行数据，status过滤掉取消状态的数据,换货和取消的
						let j = materiaOld.length;
						while(j--) {
							if(materiaNow[i].platformPushId === materiaOld[j].platformPushId) {
								let newObj = {
									platformNo: materiaNow[i].platformNo,
									platformName: materiaNow[i].platformName,
									platformChannel: materiaNow[i].platformChannel,
									platformType: materiaNow[i].platformType,
                                    actPlanId: materiaNow[i].actPlanId,
                                    actPlanName: materiaNow[i].actPlanName
								}
								let oldObj = {
									platformNo: materiaOld[j].platformNo,
									platformName: materiaOld[j].platformName,
									platformChannel: materiaOld[j].platformChannel,
									platformType: materiaOld[j].platformType,
                                    actPlanId: materiaOld[j].actPlanId,
                                    actPlanName: materiaOld[j].actPlanName
								}
								let isUpd = this.compareData(oldObj, newObj)
								if(isUpd) {
									materiaAdd.push({
										platformPushId: materiaNow[i].platformPushId,
										platformNo: materiaNow[i].platformNo,
                                        platformName: materiaNow[i].platformName,
                                        platformChannel: materiaNow[i].platformChannel,
                                        platformType: materiaNow[i].platformType,
                                        actPlanId: materiaNow[i].actPlanId,
                                        actPlanName: materiaNow[i].actPlanName.trim()
									})
								}
							}
						}
					}
					// 需要过滤掉没有物料信息的数据
					if( materiaNow[i].tempId && materiaNow[i].platformNo) { // 新增数据
						let addGoods = {
							platformNo: materiaNow[i].platformNo,
                            platformName: materiaNow[i].platformName,
                            platformChannel: materiaNow[i].platformChannel,
                            platformType: materiaNow[i].platformType,
                            actPlanId: materiaNow[i].actPlanId,
                            actPlanName: materiaNow[i].actPlanName.trim()
						}
						materiaAdd.unshift(addGoods)
					}
                }
                let materiaAll = [...materiaAdd, ...materiaUpd]
				return {
					add: materiaAdd,
                    upd: materiaUpd,
                    all: materiaAll
				}
			},
            materiaListPageSizeChange (size) {
                this.materiaSelect = []
                this.materiaListQuery.page_size = size
                this.getMaterialList()
            },
            serchMaterial(where,reslove) {
                this.materiaListQuery.where = where
                this.getMaterialList(reslove)
            },
            materiaListPageChange(page){
                this.materiaSelect = []
                this.materiaListQuery.page_no = page
                this.getMaterialList()
            },
            getMaterialList(resolve) {
              this.question.questionDelList=[];
              this.question.addQuestion=[];
              this.imgArrange.imgArrangeDelList=[];
              this.imgArrange.addImgArrange=[];
              this.activityDelete = [];
                let id = this.params.id ? this.params.id : this.duopai.appointmentActivityId, self = this
				if(id){
                    let params = Object.assign({}, self.materiaListQuery)
                    params.appointmentActivityId = id
					this.ajax.postStream('/crm-web/api/crm_appointment_activity/details/list', params,function(response){
						if(response.body.result && response.body.content) {
                            let data = response.body.content
                            if(!!data.launchMode){
                                data.launchMode = String(data.launchMode)
                            }
                            self.duopai = data
                            self.$set(self.duopai,  'status', response.body.content.status)
                            self.$set(self.duopai,  'main_sale_categorie', response.body.content.main_sale_categorie?response.body.content.main_sale_categorie:'')
                            let content = response.body.content.result || []
                            self.materiaListInfoData.materiaListData = content.list
                            self.materiaListInfoData.materiaListCount = content.count
                            self.question.questionData = response.body.content.crmAppointmentProblemSettingsVOs || [];
                            self.imgArrange.imgArrangeData = response.body.content.crmAppointmentPictureDesignVOs || [];
							self.materiaListOld = JSON.parse(JSON.stringify(self.materiaListInfoData.materiaListData))
              				self.original = JSON.parse(JSON.stringify(self.duopai))
                            self.getShopImplementation()
              				resolve&&resolve()
						} else {
                            self.$message.error(response.body.msg || '');
                            resolve&&resolve()
						}
					});
				} else {
                    resolve&&resolve()
                }
            },
            shopImplementationQuerySearchClick(where,reslove){
                this.shopImplementation.query.where = where
                this.getShopImplementation(reslove)
            },
            shopImplementationQueryPageSizeChange(size){
                this.shopImplementation.query.page_size = size
                this.getShopImplementation()
            },
            shopImplementationQueryPageChange(page){
                this.shopImplementation.query.page_no = page
                this.getShopImplementation()
            },
            getShopImplementation(resolve){
                this.shopImplementation.shopImplementationDelList=[];
                this.shopImplementation.shopImplementationAdd=[];
                let self = this
                let params = Object.assign({}, self.shopImplementation.query)
                params.appointmentActivityId = this.duopai.appointmentActivityId
                this.ajax.postStream('/crm-web/api/crm_appointment_activity/shop/setting/list', params,function(response){
                    if(response.body.result && response.body.content) {
                        let content = response.body.content || []
                        self.shopImplementation.count = content.count
                        self.shopImplementation.shopImplementationData = content.list || []
                        self.shopImplementation.copyShopImplementationData = JSON.parse(JSON.stringify(self.shopImplementation.shopImplementationData))
                        resolve&&resolve()
                    } else {
                        self.$message.error(response.body.msg || '');
                        resolve&&resolve()
                    }
                });
                
            },
			addNew () {
				this.$root.eventHandle.$emit('creatTab', {name:'预约活动新增', component:()=>import('@components/appoint_management/appointment_activity_detail.vue')})
			},
			// 选中的物料
			langeRadioChange (val, list, data) {
				this.materiaSelect = data
            },
            platformChoose(){
				var self = this
				var params = {
					selection: 'radio',
					ifAlert: true,
					callback(d){console.log('d', d, self.materiaSelect)
                        let len = self.materiaSelect.length
						self.materiaSelect[len-1].platformNo = d.platformNo;
						self.materiaSelect[len-1].platformName = d.platformName;
                        self.materiaSelect[len-1].platformChannel = d.platformChannel;
                        self.materiaSelect[len-1].platformType = d.platformType
                        self.materiaSelect[len-1].appointmentActivityId = ''
                        self.materiaSelect[len-1].actPlanName = ''
                        self.materiaSelect.forEach(item => {
                            if (item.platformNo == self.materiaSelect[len-1].platformNo) {
                                self.$refs.appointment_Activity_detail.$refs.table.toggleRowSelection(item, false)
                            }
                        })
					}
				}
				self.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/appoint_management/information_platform.vue'),
                    style:'width:800px;height:500px',
                    title:'留资平台列表'
				})
			},
            materiasAdd () { // 添加天猫物料
                new Promise((resolve) => {
                        setTimeout(resolve, 10)
                }).then(() => {
                    let params = {
                        ifAlert: true,
                        selection: 'checkbox',
                    }, self = this;
                    params.callback = t => {
                        if(t) {
                            if (t.length && t.length > 0) {
                                t.forEach(d => {
                                    let obj = {
                                        tempId: new Date(),
                                        platformNo: d.platformNo,
                                        platformName: d.platformName,
                                        platformChannel: d.platformChannel,
                                        platformType: d.platformType,
                                        appointmentActivityId: '',
                                        actPlanName: ''
                                    }
                                    self.materiaListInfoData.materiaListData.unshift(obj)
                                })
                            } else {
                                let obj = {
                                    tempId: new Date(),
                                    platformNo: t.platformNo,
                                    platformName: t.platformName,
                                    platformChannel: t.platformChannel,
                                    platformType: t.platformType,
                                    appointmentActivityId: '',
                                    actPlanName: ''
                                }
                                self.materiaListInfoData.materiaListData.unshift(obj)
                            }
                        }
                        // 滚动到最底部
                        let scrollObj = document.querySelector('#appointment_Activity_detail').querySelector('.el-table__body-wrapper')
                        this.$nextTick(function() {
                            scrollObj.scrollTop = 0
                        })
                    }
                    self.$root.eventHandle.$emit('alert', {
                        params: params,
                        component: () => import('@components/appoint_management/information_platform.vue'),
                        style: 'width:800px;height:500px',
                        title: '留资平台列表'
                    });
                })
            },
			// 物料是否可选
			selectableFun(row){
				// if(row.status === 'DISABLED') return false;
				return true;
			},
			// 删除多拍物料
			deleteMaterial () {
                let idList = [], self = this, ifAdd = null
                if(this.materiaSelect.length == 0) {
                    this.$message.error('请选择需要删除的数据')
                    return
                }
				this.materiaSelect.forEach( (good, idx) => {
					if (good.platformPushId) {
						idList.push(good.platformPushId)
					} else if (good.tempId){
                        let list = self.materiaListInfoData.materiaListData
                        let len = list.length
                        while(len--) {
                            if (list[len].materialNo === good.materialNo && list[len].tempId === good.tempId) {
                                self.materiaListInfoData.materiaListData.splice(len, 1)
                            }
                        }
					}
                })
                this.activityDelete = JSON.parse(JSON.stringify(idList))
                this.$message.success('操作成功，保存后生效')
            },
            changeActPlanId (row) {
                if (!(/^\d{1,}$/.test(row.actPlanId))) {
                    this.$message.error('推广计划ID只能为数字')
                    return
                }
            },
			// 校验商品行是否都填写
			checkGoodListRequired (saveCb) {
				let msg = ''
				this.materiaListInfoData.materiaListData.some((obj, index) => {
					if(!obj.platformNo){
						msg = '留资编码不能为空'
					}else if(obj.platformType == 'STATION'){
                        if (!(/^\d{1,}$/.test(obj.actPlanId))) {
                            msg = '推广计划ID不能为空且只能为数字'
                        } else if (!obj.actPlanName) {
                            msg = '推广计划名称不能为空'
                        } else if (!obj.actPlanName.trim()) {
                            msg = '推广计划名称不能为空'
                        }
					}

					if(msg) {
						msg = '平台推广明细-第' + (index + 1) + '行-' + msg
						return true
					}
				})
				if(msg){
					this.$message.error(msg)
					return false
				} else {
					return true
				}
			},
      diff(list,select){
        return list.reduce((pre, cur) => {
          if (select.every((item => {
            if (item.id) {
              return item.id !== cur.id
            } else {
              return item.tempId !== cur.tempId
            }
          }))) {
            pre.push(cur)
          }
          return pre;
        }, [])
      },
      // 生成预约链接
      generateReservationLink() {
        let params = {
          appointmentActivityId: this.duopai.appointmentActivityId
        };
        this.$axios(
          'post',
          '/crm-web/api/crm_appointment_activity/generateReservationLink',
          params
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.getMaterialList();
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
        }).finally(()=>{

        })
      },
      //拷贝预约活动
      selectActivity() {
      let self=this
      this.$root.eventHandle.$emit('alert', {
            params: {
                ifAlert: true,
                selection: 'radio',
                callback: data => {
                let params={
                    currentActId:self.duopai.appointmentActivityId,//当前活动ID
                    targetActId:data.appointmentActivityId//目标活动ID
                }
                self.$axios(
                    'post',
                    '/crm-web/api/crm_appointment_activity/copyActivityPlanInfo',
                    params
                ).then(res => {
                if (res.result) {
                    self.$message.success(res.msg);
                    self.getMaterialList();
                } else {
                    self.$message.error(res.msg)
                }
                }).catch(err => {
                    self.$message.error(err)
                })
                },
            },
            component: ()=>import('@components/appoint_management/appointment_activity'),
            style: 'width:800px;height:500px',
            title: '预约活动列表',
      })
    },
      // 检验问题顺序是否唯一
      checkOnly(list,name){
        let reg = /^[1-9]\d*$/;
        let isOnly=true;
        let isHasList=[];
        let shopCount=0
        list.forEach((v,i)=>{
          if (!reg.test(v.problem_order)) {
            this.$message.error('第'+(i+1)+'行问题顺序不是正整数请修改');
            isOnly= false;
            return
          }
          if (isHasList.indexOf(parseInt(v.problem_order))!==-1) {
            this.$message.error('第'+(i+1)+'行问题顺序重复请修改');
            isOnly= false;
            return
          }
          if (v.problem_module==='SHOP') {
            shopCount+=1
          }
          isHasList.push(parseInt(v.problem_order));
        });
        if (shopCount>1) { // 检查关于店铺问题是否只有1个
          this.$message.error('问题模块店铺只能有一个请修改');
          isOnly = false
        }
        return isOnly
      },
      checkImg(){
        let reg = /^[1-9]\d*$/;
        let isOnly=true;
        let HEAD=[],FOOT=[],POP=[];
        let PCHEAD=[],PCFOOT=[],PCPOP=[];
        this.imgArrange.imgArrangeData.forEach((v,i)=>{
          if (!reg.test(v.picture_order)) {
            this.$message.error('第'+(i+1)+'行图片顺序不是正整数请修改');
            isOnly= false;
            return
          }
          if (!v.picture_url) {
            this.$message.error('第'+(i+1)+'行图片没有上传');
            isOnly= false;
            return
          }
          if (v.terminal_type === 'PC') {
            switch(v.picture_type) {
              case 'HEAD':
                if (PCHEAD.indexOf(parseInt(v.picture_order))!==-1) {
                  this.$message.error('第'+(i+1)+'行图片顺序重复请修改');
                  isOnly= false;
                  return
                }
                PCHEAD.push(parseInt(v.picture_order));
                break;
              case 'FOOT':
                if (PCFOOT.indexOf(parseInt(v.picture_order))!==-1) {
                  this.$message.error('第' + (i + 1) + '行图片顺序重复请修改');
                  isOnly = false;
                  return
                }
                PCFOOT.push(parseInt(v.picture_order));
                break;
              case 'POP':
                if (PCPOP.indexOf(parseInt(v.picture_order))!==-1) {
                  this.$message.error('第'+(i+1)+'行图片顺序重复请修改');
                  isOnly= false;
                  return
                }
                PCPOP.push(parseInt(v.picture_order));
                break;
            }
          } else if (v.terminal_type === 'MOBILE') {
            switch(v.picture_type) {
              case 'HEAD':
                if (HEAD.indexOf(parseInt(v.picture_order))!==-1) {
                  this.$message.error('第'+(i+1)+'行图片顺序重复请修改');
                  isOnly= false;
                  return
                }
                HEAD.push(parseInt(v.picture_order));
                break;
              case 'FOOT':
                if (FOOT.indexOf(parseInt(v.picture_order))!==-1) {
                  this.$message.error('第' + (i + 1) + '行图片顺序重复请修改');
                  isOnly = false;
                  return
                }
                FOOT.push(parseInt(v.picture_order));
                break;
              case 'POP':
                if (POP.indexOf(parseInt(v.picture_order))!==-1) {
                  this.$message.error('第'+(i+1)+'行图片顺序重复请修改');
                  isOnly= false;
                  return
                }
                POP.push(parseInt(v.picture_order));
                break;
            }
          }
        });
        return isOnly
      },
      questionAdd() {
        let obj={
          if_response: "Y",
          problem_describe: "",
          problem_module: "",
          problem_order: this.question.questionData.length+1,
          response_way: "ANSWER",
          tempId: this.genID()
        };
        this.question.questionData.unshift(obj);
        this.question.addQuestion.unshift(this.question.questionData[0])
      },
      questionDel () {
        let idList = [];
        if(this.question.select.length === 0) {
          this.$message.error('请选择需要删除的数据');
          return
        }
        this.question.questionData=this.diff(this.question.questionData,this.question.select);
        this.question.addQuestion = this.diff(this.question.addQuestion,this.question.select);
        this.question.select.forEach(v=>{
          if (v.id){
            idList.push(v.id)
          }
        });
        idList = JSON.parse(JSON.stringify(idList));
        this.question.questionDelList.push(...idList);
        this.question.select={};
      },
      questionSelectChange(val) {
        this.question.select=val
      },
      imgArrangeAdd() {
        let params={
          appointmentActivityId: this.duopai.appointmentActivityId
        };
        this.$axios('get',
          '/crm-web/api/crm_appointment_activity/getCrmAppointmentPictureDesignId',
          params
        ).then(res => {
          if (res.result) {
            let obj={
              id: res.content,
              terminal_type: "",
              picture_type: "",
              picture_url: '',
              picture_order: this.imgArrange.imgArrangeData.length+1,
              create_name: '',
              create_time: '',
              tempId: this.genID()
            };
            this.imgArrange.imgArrangeData.unshift(obj)
            this.imgArrange.addImgArrange.unshift(this.imgArrange.imgArrangeData[0])
          }
        }).catch(err => {
        }).finally(()=>{
        })
      },
      imgArrangeDel () {
        let idList = [], self = this, ifAdd = null;
        if(this.imgArrange.select.length === 0) {
          this.$message.error('请选择需要删除的数据');
          return
        }
        this.imgArrange.imgArrangeData=this.diff(this.imgArrange.imgArrangeData,this.imgArrange.select);
        this.imgArrange.addImgArrange=this.diff(this.imgArrange.addImgArrange,this.imgArrange.select);
        this.imgArrange.select.forEach(v=>{
          if (v.id){
            idList.push(v.id)
          }
        });
        idList = JSON.parse(JSON.stringify(idList));
        this.imgArrange.imgArrangeDelList.push(...idList);
        this.imgArrange.select={};
      },
      imgSelectChange(val){
        this.imgArrange.select=val
      },
      getPath(data,id){
        console.log(data,'path,pathpathpathpathpathpath');
        this.imgArrange.imgArrangeData.forEach((v,i)=>{
          if (v.id===id) {
            v.picture_url=data
          }
        })
      },
      // 上传图片组件
      delImgCatch(val,id) {
        console.log(val,'delImgCatchdelImgCatchdelImgCatch');
        this.imgArrange.imgArrangeData.forEach((v,i)=>{
          if (v.id===id) {
            v.picture_url=''
          }
        })
      },
      paramsDelId(val){
        val.forEach(v=>{
          delete v.tempId
        })
      },
      genID(length){
        return Number(Math.random().toString().substr(3,length) + Date.now()).toString(36);
      },
      shopImplementationAdd() {
        let {businessDivision,main_sale_categorie} = this.duopai
        if(!businessDivision || !main_sale_categorie){
            this.$message.warning('请先选择事业部和主营类目')
            return
        }
        let self = this;
        let params = {};
        params.callback =  (d)=> {
          if (d) {
            let isExist = false;
            let newObj= [];
            d.forEach((item,index)=>{
              let obj = {
                shop_name: d[index].shop_name,
                shop_province_name: d[index].shop_province_name,
                shop_city_name: d[index].shop_city_name,
                shop_activity_name: d[index].shop_activity_name,
                shop_province_id: d[index].shop_province_id,
                shop_city_id: d[index].shop_city_id,
                shop_code: d[index].shop_code,
                tempId: this.genID()
              };
              // 检验物料行是否已经存在同样的物料
              self.shopImplementation.shopImplementationData.forEach(val=>{
                if (val.shop_code===item.shop_code) {
                  isExist = true
                }
              });
              newObj.push(obj)
            });
            if (!isExist) {
              newObj.forEach(v=>{
                this.shopImplementation.shopImplementationData.unshift(v);
                this.shopImplementation.copyShopImplementationData.unshift(v)
                this.shopImplementation.shopImplementationAdd.unshift(v)
              });
              this.shopSave();
            } else {
              self.$message.info('不能选择已添加的店铺');
            }
          }
        };
        params.businessDivision = businessDivision
        params.main_sale_categorie = main_sale_categorie
        this.$root.eventHandle.$emit('alert', {
          params:params,
          component: () => import('@components/appoint_management/appointmentShopList'),
          style: 'width:800px;height:500px',
          title: '店铺列表',
        })
      },
      shopSelectChange(val){
        this.shopImplementation.select=val
      },
      shopDel (val) {
        let idList = []
        if(this.shopImplementation.select.length === 0) {
          this.$message.error('请选择需要删除的数据');
          return
        }
        this.shopImplementation.shopImplementationData=this.diff(this.shopImplementation.shopImplementationData,this.shopImplementation.select);
        this.shopImplementation.copyShopImplementationData = this.diff(this.shopImplementation.shopImplementationData,this.shopImplementation.select)
        this.shopImplementation.select.forEach(v=>{
          if (v.id){
            idList.push(v.id)
          }
        });
        this.shopImplementation.shopImplementationDelList = JSON.parse(JSON.stringify(idList));
        this.shopImplementation.select={};
        this.shopSave()
      },
      // 只用来保存店铺用
      shopSave() {
        let saveData = JSON.parse(JSON.stringify(this.duopai));
        this.paramsDelId(this.shopImplementation.shopImplementationAdd);
        let data = {
          stationActivityNo: saveData.stationActivityNo,
          main_sale_categorie:saveData.main_sale_categorie,
          appointmentActivityName: saveData.appointmentActivityName,
          appointmentRights: saveData.appointmentRights,
          launchMode: saveData.launchMode,
          browsingTerminal: saveData.browsingTerminal,
          activityType: saveData.activityType,
          startTime: saveData.startTime === '' ? '' : +new Date(saveData.startTime),
          endTime: saveData.endTime === '' ? '' : +new Date(saveData.endTime),
          remark: saveData.remark,
          status: saveData.status,
          deletedItems: [],
          savedItems: [],
          appointCount: saveData.appointCount,
          createName: saveData.createName,
          createTime: saveData.createTime,
          modifyName: saveData.modifyName,
          modifyTime: saveData.modifyTime,
          pubilishName: saveData.pubilishName,
          pubilishTime: saveData.pubilishTime,
          businessDivision: saveData.businessDivision,
          crmAppointmentProblemSettingsVOs: [],
          crmAppointmentProblemSettingsIds:[],
          crmAppointmentPictureDesignVOs: [],
          crmAppointmentShopSettingsVOs: this.shopImplementation.shopImplementationAdd,
          crmAppointmentShopSettingsIds: this.shopImplementation.shopImplementationDelList
        }
        saveData.appointmentActivityId ? data.appointmentActivityId = saveData.appointmentActivityId : ''
        saveData.appointmentActivityNo ? data.appointmentActivityNo = saveData.appointmentActivityNo : ''
        this.$axios(
          'post',
          '/crm-web/api/crm_appointment_activity/save',
          data
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.shopImplementation.shopImplementationAdd = [];
            this.shopImplementation.shopImplementationDelList=[]
          } else {
            this.$message.error(res.msg);
          }
        }).catch(err => {
        }).finally(()=>{
          this.getShopImplementation();
          this.secondTab='shopImplementation'
        })
      },
      // 关闭标签页
      closeTab(isSave){
        let isUpdate = false
        let self = this
        if (this.imgArrange.addImgArrange.length>0 || this.question.addQuestion.length>0) {
          isUpdate = true
        }
        if (self.saveCompare()){
          isUpdate = true
        }
        if(isUpdate && !isSave){
          self.$root.eventHandle.$emit('openDialog',{
            ok(){
              self.save(()=>{
                self.$root.eventHandle.$emit('removeTab',self.params.tabName)
              })
            },
            no(){
              self.$root.eventHandle.$emit('removeTab',self.params.tabName)
            }
          })
        }else if (!isUpdate && isSave){
          self.params.__close=self.$root.eventHandle.$emit('removeTab',self.params.tabName)
        } else {
          self.$root.eventHandle.$emit('removeTab',self.params.tabName)
        }
      },
      /**
       *查看附件事件
       */
      uploadFun() {
        //TODO,明细行里面的父问题ID
        // this.ifClickUpload = true;
        // this.uploadData = {
        //   parent_name: 'AFTER_ORDER',
        //   parent_no: this.otherInfo.after_order_no,//售后单号
        //   child_name: 'QUESTION_GOODS',
        //   child_no: questionObj.parent_question_id,//父问题id,必须是String
        //   content: questionObj,
        // }
        //重置，防止第二次不能触发
        setTimeout(() => {
          this.ifClickUpload = false;
        },100)
      },
      viewOrUpload(row,scope){
        this.imgArrange.imgArrangeData.forEach((v,i)=>{
          if (row.id===v.id) {
            this.paramsInfo.index=i
          }
        });
        this.isShow = true;
        this.isGetImgList = true;
        this.paramsInfo.parent_no = this.dataObj.parent_no = this.duopai.appointmentActivityId;
        this.paramsInfo.child_no = this.dataObj.child_no = row.id;
        this.paramsInfo.picture_url = row.picture_url;
        this.paramsInfo.cloud_file_id = null;
        this.dataObj.parent_name = 'appointment';
        this.dataObj.child_name = 'appointmentImg';
        this.dataObj.content = JSON.parse(JSON.stringify(row));
      },
      xptImageClose (imgList){
        this.isShow = false;
        this.isGetImgList = false;
      },
      async quickInput(myValue,type) {
        let  myField = this.$refs.appointSuccessedNote.$el.querySelector('input');
        if (myField.selectionStart || myField.selectionStart === 0) {
          let startPos = myField.selectionStart;
          let endPos = myField.selectionEnd;
          this.duopai.appointSuccessedNote = myField.value.substring(0, startPos) + myValue
            + myField.value.substring(endPos, myField.value.length);
          await this.$nextTick() // 这句是重点, 圈起来
          myField.focus();
          myField.setSelectionRange(endPos + myValue.length, endPos + myValue.length)
        } else {
          this.duopai.appointSuccessedNote += myValue
        }
      },
      async quickEndMsgInput(myValue,type) {
        let  myField = this.$refs.appointShopNote.$el.querySelector('input');
        if (myField.selectionStart || myField.selectionStart === 0) {
          let startPos = myField.selectionStart;
          let endPos = myField.selectionEnd;
          this.duopai.appointShopNote = myField.value.substring(0, startPos) + myValue
            + myField.value.substring(endPos, myField.value.length);
          await this.$nextTick() // 这句是重点, 圈起来
          myField.focus();
          myField.setSelectionRange(endPos + myValue.length, endPos + myValue.length)
        } else {
          this.duopai.appointShopNote += myValue
        }
      },
      textPop() {
        this.save()
        let self = this;
        let params = {
          appointment_activity_name: this.duopai.appointment_activity_name,
          appointmentActivityId: this.duopai.appointmentActivityId,
          appointEndNote: this.duopai.appointEndNote,
          appointShopNote: this.duopai.appointShopNote,
          appointSuccessedNote: this.duopai.appointSuccessedNote
        };
        this.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/appoint_management/note_cover_pop'),
          style: 'width:960px;height:600px',
          title: '短信模板覆盖列表',
        })
      },
      setDefault() {
        let params = {
          appointmentActivityId: this.duopai.appointmentActivityId,
          appointSuccessedNote: this.duopai.appointSuccessedNote,
          appointShopNote: this.duopai.appointShopNote,
          appointEndNote: this.duopai.appointEndNote
        };
        this.$axios(
          'post',
          '/crm-web/api/crm_appointment_activity/noteDefault',
          params
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.getMaterialList();
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
        }).finally(()=>{
        })
      },
      //用来关闭页签前数据是否有修改的对比函数
      saveCompare() {
        let saveData = this.duopai,
          isDiff = false
        let materiaSaveData = this.materiaSaveData(), ifRequired = this.checkGoodListRequired()
        let data={
          appointDefaultNote: saveData.appointDefaultNote,
          appointEndNote: saveData.appointEndNote,
          appointShopNote: saveData.appointShopNote,
          stationActivityNo: saveData.stationActivityNo,
          appointSuccessedNote: saveData.appointSuccessedNote,
          appointmentActivityName: saveData.appointmentActivityName,
          main_sale_categorie:saveData.main_sale_categorie,
          appointmentRights: saveData.appointmentRights,
          launchMode: saveData.launchMode,
          browsingTerminal: saveData.browsingTerminal,
          activityType: saveData.activityType,
          remark: saveData.remark,
          startTime: saveData.startTime,
          endTime: saveData.endTime,
          businessDivision: saveData.businessDivision,
         }
        for (const i in data) {
          if (this.original[i]!==data[i]){
            isDiff=true
          }
        }
        if (materiaSaveData.all.length>0) {
          isDiff=true
        }
        if (this.activityDelete.length>0) {
          isDiff=true
        }
        return isDiff
      },
      // 实施店铺状态切换
      changeState(item){
        let enable_status = item.enable_status == 1 ? 0 : 1
         let params = {
           idList:[item.id],
           enable_status
        };
        this.$axios(
          'post',
          '/crm-web/api/crm_appointment_activity/shopSettings/changeStatus?permissionCode=CHANGE_ENABLE_STATUS',
          params
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.getShopImplementation();
          } else {
            this.$message.error(res.msg)
          }
        })
      },
      // 批量生效
      takeEffect(){
        let idList = []
        if(Object.prototype.toString.call(this.shopImplementation.select) === '[object Array]'){
          idList = this.shopImplementation.select.map(item=>{
                return item.id
            })
        }
        if(!idList.length){
            this.$message.warning('请选择店铺')
            return 
        }
         let params = {
           idList,
           enable_status:1
        };
        this.$axios(
          'post',
          '/crm-web/api/crm_appointment_activity/shopSettings/changeStatus?permissionCode=CHANGE_ENABLE_STATUS',
          params
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.getShopImplementation();
          } else {
            this.$message.error(res.msg)
          }
        })
      },
      // 批量失效
      loseEfficacy(){
        let idList = []
        if(Object.prototype.toString.call(this.shopImplementation.select) === '[object Array]'){
          idList = this.shopImplementation.select.map(item=>{
                return item.id
            })
        }
        if(!idList.length){
            this.$message.warning('请选择店铺')
            return 
        }
         let params = {
           idList,
           enable_status:0
        };
        this.$axios(
          'post',
          '/crm-web/api/crm_appointment_activity/shopSettings/changeStatus?permissionCode=CHANGE_ENABLE_STATUS',
          params
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.getShopImplementation();
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
		mounted: function(){
      this.getMaterialList()
    },
    created() {
      this.params.__close = this.closeTab
    },
    watch: {},
    computed:{
      preview() {
        return this.duopai.appointSuccessedNote  + this.duopai.appointShopNote  + this.duopai.appointEndNote;
      }
    }
	}
</script>
<style module>
  /deep/ .el-input-number__increase{
    width: 20px;
    line-height: 20px;
  }
  .el-select>.el-input {
    width: initial !important;
  }
  .resizeNone> textarea{
    resize: none !important;
  }
.row-height :global(.el-form-item__content) {
	height: auto!important;
	white-space: nowrap;
}
.mgb5 {
    margin-bottom: 5px;
}
</style>
<style scoped>
.flexBox{
    display: flex;
    align-items: center;
}
.flexBox .item{
    margin-left: 5px;
}
</style>
<style>
.dialog-container-appointment-activity-detail .el-dialog__body{
    padding: 10px 20px 30px;
    line-height: 1.5;
}
.dialog-container-appointment-activity-detail .el-dialog__body p:nth-of-type(1){
    font-weight: bold;
}</style>
