<!-- 预约数据跟踪列表 --> 
<template>
    <xpt-list  
        :data='goodsList' 
        :btns='goodsBtns' 
        :colData='goodsCols' 
        orderNo 
        isNeedClickEvent 
        @selection-change='goodsRadioChange'
        :pageTotal='goodsCount' 
        :searchPage='goodsQuery.page_name' 
        @search-click='goodsListSearch'
        :selectable='selectableFun'
        @page-size-change='goodsPageSizeChange' 
        @current-page-change='goodsPageChange'>
        <el-button slot='btns' size="mini" type="primary" @click="handleImport"
          >导入</el-button
        >
        <template slot="customerBehavior" slot-scope="scope">
            <el-button type="text" size="small" @click="handleGotoPerformance(scope)">查看</el-button>
        </template>
        <template slot='phone' slot-scope='scope'>
            <xpt-eye-switch v-model="scope.row.mobile" :readonly='true' :hideBorder="true"></xpt-eye-switch>
        </template>
    </xpt-list>
</template>
<script>
    import fn from '@common/Fn.js'
    export default {
        data() {
            let self = this;
            return {
                goodsList: [],
                goodsBtns: [
                    {
                        type: 'success',
                        txt: '刷新',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.refresh()
                        }
                    }, {
                        type: 'primary',
                        txt: '导出',
                        loading: false,
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.exportList()
                        }
                    }, {
                        type: 'info',
                        txt: '导出文件下载',
                        loading: false,
                        click() {
                            self.downExportList()
                        }
                    }, {
                        type: "info",
                        txt: "导入结果",
                        loading: false,
                        click() {
                            self.downloadExcel()
                        }
                    }
                ],
                goodsCols: [
                    {
                        label:'客户行为表现',
                        slot:'customerBehavior',
                        width: 90,
                    },
                    {
                        label: '客户姓名',
                        prop: 'name'
                    }, {
                        label: '客户手机',
                        prop: 'mobile',
                        slot: "phone",
                        width: 120
                    },
                    {
                        label: '事业部',
                        width:120,
                        format: "auxFormat",
                        formatParams: "business_division",
                        prop: 'business_division'
                    },
                    {
                        label: '店铺主营类目',
                        prop: 'main_sale_categorie',
                        width: 120,
                         format: "auxFormat",
                        formatParams: "main_sale_categories"
                    },/*{
                        label: '预约门店编码',
                        prop: 'appointment_shop_no'
                    },*/ {
                        label: '预约活动编号',
                        prop: 'appointment_question_no',
                        width: 130,
                    }, {
                        label: '预约活动名称',
                        prop: 'appointment_activity_name',
                        width: 120,
                    }, {
                        label: '线索归属省',
                        prop: 'clue_province_name'
                    }, {
                        label: '线索归属市',
                        prop: 'clue_city_name'
                    }, {
                        label: '线索归属区',
                        prop: 'clue_district_name'
                    }, {
                        label: '预约门店',
                        prop: 'appointment_shop_name'
                    }, {
                        label: '留资方式',
                        prop: 'information_way',
                        formatter(val) {
                        	switch(val) {
                        		case 'SYSTEM': return '系统'; break;
                        		case 'IMPORT': return '导入'; break;
                        		case 'STATION': return '外部'; break;
                        		default: return val; break;
                        	}
                        }
                    }, {
                        label: '留资类型',
                        prop: 'information_type',
                        formatter(val) {
                        	switch(val) {
                        		case 'APPOINT_FORM': return '预约表单'; break;
                        		case 'CALL_CONSULT': return '来电咨询'; break;
                        		case 'ONLINE_CONSULTATION': return '在线咨询'; break;
                        		default: return val; break;
                        	}
                        }
                    }, {
                        label: '推广计划ID',
                        prop: 'station_plan_id'
                    },{
                        label: '平台线索ID',
                        prop: 'station_clue_id'
                    },{
                        label: '留资广告主',
                        prop: 'imformation_act_name'
                    },{
                        label: '留资平台',
                        prop: 'information_platform_name',
                        width: 90,
                    }, {
                        label: '留资渠道',
                        prop: 'appointment_channel_name',
                        width: 90,
                    }, {
                        label: '预约提交时间',
                        prop: 'appointment_submit_time',
                        width: 130,
                        format: 'dataFormat1'
                    }, {
                        label: '预约生成时间',
                        prop: 'create_time',
                        width: 130,
                        format: 'dataFormat1'
                    },{
                        label: '是否发送短信',
                        prop: 'if_send_note',
                        width: 90,
                        formatter(val) {
                        	switch(val) {
                        		case 0: return '否'; break;
                        		case 1: return '是'; break;
                        		default: return val; break;
                        	}
                        }
                    },{
                        label: '状态',
                        prop: 'status',
                        formatter(val) {
                        	switch(val) {
                        		case 'WATING': return '待跟进'; break;
                        		case 'FOLLOWING': return '跟进中'; break;
                        		case 'FOLLOWED': return '已跟进'; break;
                        		case 'ASSIGNED': return '已分配'; break;
                        		case 'FINISHED': return '已完成'; break;
                        		case 'DISABLED': return '已失效'; break;
                        		default: return val; break;
                        	}
                        }
                    },{
                        label: "跟进话务",
                        prop: "tel_operator_name",
                    },{
                        label: "话务跟进时间",
                        prop: "tel_operator_follow_time",
                        width: 130,
                        format: 'dataFormat1'
                    }, {
                        label: "话务跟进标签",
                        prop: "tel_operator_tag",
                        width: 86,
                        format: 'auxFormat',
                        formatParams: 'appointment_tel_operator_tag'
                    }, {
                        label: "话务跟进结果",
                        prop: "tel_operator_mark_requirement",
                        width: 100,
                    },{
                        label: "话务跟进得分",
                        prop: "tel_operator_score",
                        width: 90,
                    },{
                        label: "店铺地区",
                        prop: "shop_area",
                        format: "auxFormat",
                        formatParams: "shopArea"
                    },{
                        label: "预约发送店铺",
                        prop: "appointment_send_shop_name",
                        width: 92
                    }, 
                    {
                        label: "话务完成时间",
                        prop: "tel_operator_confirm_time",
                        width: 130,
                        format: 'dataFormat1'
                    },{
                        label: "店长分配次数",
                        prop: "shop_owner_assign_count",
                        width: 90
                    }, {
                        label: "店长首次分配时间",
                        prop: "shop_owner_first_assign_time",
                        width: 130,
                        format: 'dataFormat1'
                    },{
                        label: "店长最近分配时间",
                        prop: "shop_owner_last_assign_time",
                        width: 130,
                        format: 'dataFormat1'
                    },{
                        label: "导购跟进次数",
                        prop: "shop_guide_follow_count",
                        width: 90
                    }, {
                        label: "导购首次跟进时间",
                        prop: "shop_guide_first_follow_time",
                        width: 130,
                        format: 'dataFormat1'
                    },{
                        label: "导购首次跟进标签",
                        prop: "shop_guide_first_follow_tag",
                        width: 130,
                        formatter(val) {
                        	switch(val) {
                        		case 'GUIDE_INTENTIONAL': return '已接通有意向'; break;
                        		case 'GUIDE_UNINTENTIONAL': return '已接通无意向'; break;
                        		case 'GUIDE_NO_VISIT': return '未接待访'; break;
                        		default: return val; break;
                        	}
                        }
                    },{
                        label: "导购首次跟进备注",
                        prop: "shop_guide_first_follow_mark",
                        width: 130,
                    },{
                        label: "导购最近跟进时间",
                        prop: "shop_guide_recent_follow_time",
                        width: 130,
                        format: 'dataFormat1'
                    },{
                        label: "导购最近跟进标签",
                        prop: "shop_guide_recent_follow_tag",
                        width: 130,
                        formatter(val) {
                        	switch(val) {
                        		case 'GUIDE_INTENTIONAL': return '已接通有意向'; break;
                        		case 'GUIDE_UNINTENTIONAL': return '已接通无意向'; break;
                        		case 'GUIDE_NO_VISIT': return '未接待访'; break;
                        		default: return val; break;
                        	}
                        }
                    },{
                        label: "导购最近跟进备注",
                        prop: "shop_guide_recent_follow_mark",
                        width: 130,
                    },/*{
                        label: '预约活动店铺',
                        width: 90,
                        prop: 'appointment_shop_activity_name'
                    },*/ 
                    // {
                    //     label: "话务完成时间",
                    //     prop: "tel_operator_confirm_time",
                    //     width: 130,
                    //     format: 'dataFormat1'
                    // },
                    // {
                    //     label: "店长分配时间",
                    //     prop: "shop_owner_assign_recent_time",
                    //     width: 130,
                    //     format: 'dataFormat1'
                    // }, 
                    // {
                    //     label: "导购跟进时间",
                    //     prop: "shop_guide_recent_follow_time",
                    //     width: 130,
                    //     format: 'dataFormat1'
                    // }, 
                    // {
                    //     label: "导购跟进标签",
                    //     prop: "shop_guide_recent_tag",
                    //     width: 92,
                    //     formatter(val) {
                    //     	switch(val) {
                    //     		case 'GUIDE_INTENTIONAL': return '已接通有意向'; break;
                    //     		case 'GUIDE_UNINTENTIONAL': return '已接通无意向'; break;
                    //     		case 'GUIDE_NO_VISIT': return '未接待访'; break;
                    //     		default: return val; break;
                    //     	}
                    //     }
                    // },
                     {
                        label: '预约码',
                        prop: 'appointment_code',
                        width: 90
                    }, 
                    {
                    label: "系统下推时间",
                    prop: "tel_operator_confirm_time",
                    format: "dataFormat1" ,
                    },
                    // {
                    //     label: '导购跟进备注',
                    //     prop: 'shop_guide_mark',
                    //     width: 150
                    // }
                ],
                goodsSelect: [],
                goodsCount: 0,
                goodsQuery: {
                    page_name: "crm_appointment_station",
                    where: [],
                    page_size: 50,
                    page_no: 1,
                    // 是否过滤,1为不过滤，不传则默认过滤
                    if_need_page: 'Y'
                },
                ifSave: false,
                otherParams: {
                    ifAppointment: true
                },
                uploadUrl: '/crm-web/api/crmAppointmentTracing/tracing/import',
            }
        },
        methods: {
            handleGotoPerformance(params){
                params.isReservation = true;
                this.$root.eventHandle.$emit("creatTab", {
                    name: "客户行为表现",
                    params,
                    component: () => import("@components/appoint_management/customer_behavior_performance.vue"),
                });
            },
            handleImport(){
                this.$root.eventHandle.$emit('alert', {
                    params: {
                        uploadUrl:this.uploadUrl,
                        otherParams:this.otherParams,
                        isupload:false,
                        filename:'预约数据跟踪导入模板',
                        uploadCallback:(data)=>{
                            let {result,msg} = data.body
                            if (result) {
                               this.$message.success(msg)
                               return
                            }
                            this.$message.error(msg)
                        },
                    },
                    component: () => import("@components/common/template-download-dialog.vue"),
                    style: 'width:400px;height:150px',
                    title: '导入'
                });
            },

            // 导入后的回调函数
            uploadCallback(data) {
                if (data.body.result) {
                    this.getGoodsList()
                }
            },
            // 查看导入结果
            downloadExcel() {
                this.$root.eventHandle.$emit('alert', {
                    params: {
                        SQLUrl: 'appointmentTrack'
                    },
                    component: () => import("@components/appoint_management/appointment_import_result.vue"),
                    style: 'width:800px;height:400px',
                    title: '导入结果'
                });
            },
            // 物料是否可选
			selectableFun(row){
				if(row.clue_is_defect == '0') return false;
				return true;
			},
            // 导出文件下载
            downExportList() {
                this.$root.eventHandle.$emit('alert', {
                    component: () => import('@components/after_sales_report/export'),
                    style:'width:900px;height:600px',
                    title: '报表导出列表',
                    
                    params: {
                        query: {
                            type: 'EXCEL_TYPE_CRM_APPOINTMENT_STATION'
                        }
                    }
                })
            },
            // 导出
            exportList() {
                this.goodsBtns[1].loading = true;
                this.ajax.postStream('/crm-web/api/crmAppointmentTracing/exprot',this.goodsQuery,res => {
                    if(res.body.result) {
                    res.body.msg && this.$message.success(res.body.msg)
                    } else {
                    res.body.msg && this.$message.error(res.body.msg)
                    }
                    this.goodsBtns[1].loading = false
                }, err => {
                    this.goodsBtns[1].loading = false
                    this.$message.error(err)
                })
            },
            refresh() {
                this.getGoodsList()
            },
            // 行选中
            goodsRadioChange(row) {
                this.goodsSelect = row;
            },
            // 获取商品信息
            getGoodsList(resolve, ifPromiss) {
                let self = this;
                let data = JSON.parse(JSON.stringify(this.goodsQuery));
                let url = '/crm-web/api/crmAppointmentTracing/tracing/list'
                this.ajax.postStream(url, data, d => {
                    if (d.body.result && d.body.content) {
                        this.goodsList = d.body.content.list
                        this.goodsCount = d.body.content.count
                        this.$message.success(d.body.msg)
                    } else {
                        this.$message.error(d.body.msg)
                    }
                    resolve && resolve();
                }, (e) => {
                    this.$message.error(e);
                    resolve && resolve();
                })
            },
            //通用查询搜索
            goodsListSearch(where, reslove) {
                this.goodsQuery.where = where
                this.getGoodsList(reslove)
            },
            // 当前页改变
            goodsPageSizeChange(ps) {
                this.goodsQuery.page_size = ps;
                this.getGoodsList();
            },
            // 当前页面显示行数改变
            goodsPageChange(page) {
                this.goodsQuery.page_no = page;
                this.getGoodsList();
            }
        },
        computed: {},
        watch: {},
        mounted() {
            this.getGoodsList()
        }
    }
</script>
<style type="text/css">
    .el-dialog__wrapper {
        background-color: transparent
    }
</style>