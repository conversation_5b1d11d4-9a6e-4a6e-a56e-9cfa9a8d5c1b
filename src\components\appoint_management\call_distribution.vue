//话务分配列表
<template>
  <xpt-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :selection="selection"
    :searchPage='search.page_name'
    :orderNo="true"
    :pageTotal='pageTotal'
    @selection-change='select'
    @search-click='searchClick'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
  ></xpt-list>
</template>

<script>
    export default {
      name: "call_distribution",
      props: ["data", "params"],
      data() {
        let self = this;
        return {
          selection: "checkbox",
          pageTotal:0,
          selectRow:[],
          submitTime:"",
          btns: [
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.getList();
              },
            },
            {
              type: "primary",
              txt: "分配",
              loading: false,
              click() {
                self.distributeList();
              },
            },
          ],
          cols: [
            {
              label: "姓名",
              prop: "name",
            },
            {
              label: "手机号码",
              prop: "mobile",
            },
            {
              label: "预约活动店铺",
              prop: "appointment_shop_activity_name",
            },
            {
              label: "来源渠道",
              prop: "appointment_channel_name",
            },
            {
              label: "预约提交时间",
              prop: "appointment_submit_time",
              format: "dataFormat1" ,
            },
          ],
          dataList: [],
          callAssistant:{},
          search:{
            page_name:"crm_appointment_tracing_tel_admin_assign",
            where:[],
            page:{
              length: 50,
              pageNo: 1,
            }
          }
        }
      },
      methods: {
        getList(resolve) {
          this.btns[0].loading = true;
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getTelAssignList',this.search,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.pageTotal = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        distributeList() {
          let self = this,
            ids = [];
          this.selectRow.find(d=>{
            ids.push(d.id)
          });
          if(!ids.length){
            self.$message({
              message:'请选择要分配的客户！',
              type:'error'
            });
            return;
          }
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/appoint_management/call_distribution_list.vue'),
            style: 'width:800px;height:500px',
            title: "话务员列表",
            params: {
              roleId:"100000021347009",
              customer:ids,
              //刷新分配列表
              callback: data => {
                this.getList();
              }
            }
          });
        },
        // 多选事件
        select(s){
          this.selectRow = s;
        },
        searchClick(list,resolve){
          this.search.where = list;
          this.getList(resolve);
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page.length = pageSize;
          this.getList();
        },
        // 监听页数更改事件
        pageChange(page){
          this.search.page.pageNo  = page;
          this.getList();
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
