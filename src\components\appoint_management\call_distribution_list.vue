//话务列表分配弹窗
<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='success' size='mini' @click='fresh' slot='left'>刷新</el-button>
      <el-button type='primary' size='mini' @click='confirm' slot='left'>确认分配</el-button>
      <el-input
        placeholder="输入用户名或昵称"
        icon="search"
        size='mini'
        v-model="search.key"
        :on-icon-click="searchClick"
        slot='right'
        @keyup.enter.native="searchClick"
      ></el-input>
    </xpt-headbar>
    <xpt-list
      :data="dataList"
      :colData="cols"
      :showHead="false"
      :pageTotal='pageTotal'
      :selection="selection"
      @page-size-change='pageChange'
      @current-page-change='currentPageChange'
      @radio-change='select'
    ></xpt-list>
  </div>
</template>

<script>
    export default {
      name: "call_distribution_list",
      props:['params'],
      data() {
        let self = this;
        return {
          selection:"radio",
          selectRow:"",
          cols: [
            {
              label: "用户账户",
              prop: "identification",
            },
            {
              label: "用户名称",
              prop: "realName",
            },
            {
              label: "昵称",
              prop: "nickName",
            },
            {
              label:'生效',
              prop:'status',
              format:'statusFilter'
            },
          ],
          dataList: [],
          search: {
            key : ""
          },
          pageTotal:0,
          pageNow:1,
        }
      },
      methods: {
        getList(){
          let _this = this;
          let params = {
            roleId:_this.params.roleId,
            key: this.search.key ? this.search.key : "",
            page:{
              length:this.pageSize,
              pageNo:this.pageNow,
            }
          };
          this.ajax.postStream('/permission-web/api/role/getUserRoleRelationList',params,res => {
            if(res.body.result){
              this.dataList = res.body.content.list || [];
              this.pageTotal = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        fresh() {
          this.reset();
          this.getList();
        },
        reset(){
          this.search.key = "";
          this.selectRow = "";
        },
        searchClick() {
          this.getList();
        },
        confirm() {
          //确认分配
          let self = this;
          console.log("selectRow:%s",this.selectRow);
          if(!this.selectRow.length){
            self.$message({
              message:'请选择话务员！',
              type:'error'
            });
            return;
          }
          let data = {
            ids:this.params.customer,
            employeeNumber:this.selectRow,
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/telAdminAssignTel',data,res => {
            if(res.body.result){
              this.$message.success(res.body.msg);
              //关闭弹窗
              this.params.callback();
              this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        // 选择事件
        select(s){
          //获取话务员账户
          this.selectRow = s.identification;
        },
        // 监听每页显示数更改事件
        pageChange(pageSize){
          this.pageSize = pageSize;
          this.pageNow = 1;
          this.getList();
        },
        // 监听页数更改事件
        currentPageChange(page){
          this.pageNow = page;
          this.getList();
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
