//话务跟进列表
<template>
  <xpt-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :searchPage='search.page_name'
    :selection="selection"
    :orderNo="true"
    isNeedClickEvent
    :pageTotal='pageTotal'
    @search-click='searchClick'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    @selection-change='callFollowSelectionChange'
    :taggelClassName="callFollowClassName"
  >
        <el-button
              v-if="isOperationChild"
							slot='btns'
							@click="pushYTData"
							size='mini'
							type='danger'
              :loading="pushYTLoading"
				>下推(婴童)</el-button>
  </xpt-list>
</template>

<script>
    export default {
      name: "call_follow",
      data(){
        let self = this;
        return {
          selection:"checkbox",
          dataList:[],
          pageTotal:0,
          pageSize:50,
          pageNow:1,
          search:{
            page_name:"crm_appointment_tracing",
            where:[],
            page: {
              length: self.pageSize,
              pageNo: 1,
            }
          },
          btns:[
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.refresh();
              },
            }, {
              type: "info",
              txt: "跟进",
              loading: false,
              click() {
                self.following('following');
              },
            }, {
              type: "info",
              txt: "取消跟进",
              loading: false,
              key: 'cancleFollowing',
              disabled: true,
              click() {
                self.following('cancleFollowing');
              },
            }, {
              type: "info",
              txt: "完成跟进",
              loading: false,
              click() {
                self.following('finishFollowing');
              },
            }, {
              type: "success",
              txt: "导出",
              key: 'export',
              loading: false,
              disabled: true,
              click() {
                self.exportExcel();
              },
            }, {
              type: "info",
              txt: "报表导出文件下载",
              loading: false,
              disabled: true,
              key: 'exportDownload',
              click() {
                self.showExportList();
              },
            }, {
                type: 'primary',
                txt: '新增',
                disabled: true,
                key: 'add',
                click() {
                    self.addNew()
                }
            } ,{
              type: "success",
              txt: "下推",
              loading: false,
              click() {
                self.pushData();
              },}
          ],
          cols:[
            {
              label: "客户姓名",
              prop: "name",
              redirectClick(row) {
                    let params = {
                      eyePermisson: self.ifPermisson,
                      isOutsourceCustomer: !self.ifPermisson
                    };
                    params.id = row.id;
                    self.$root.eventHandle.$emit('creatTab', {
                        name: "话务跟进详情",
                        params: params,
                        component: () => import('@components/appoint_management/call_follow_detail.vue')
                    });
                },
            },
            {
              label: "客户手机",
              prop: "mobile",
              format: 'hidePhoneNumber',
              width: 95
            },
            {
              label: "活动类型",
              prop: "appointment_act_type",
              width: 90
            },
            {
              label: "预约活动编码",
              prop: "appointment_question_no",
              width: 100
            },
            {
              label: "预约活动店铺",
              prop: "appointment_shop_activity_name",
              width: 90
            },
            {
                label: '留资平台',
                prop: 'information_platform_name',
                width: 90,
            }, {
              label: "留资渠道",
              prop: "appointment_channel_name",
            },
            {
              label: "预约提交时间",
              prop: "appointment_submit_time",
              format: "dataFormat1" ,
              width: 130
            },{
              label: "预约生成时间",
              prop: "create_time",
              format: "dataFormat1" ,
              width: 130
            }, {
                label: '状态',
                prop: 'status',
                formatter(val) {
                    switch(val) {
                        case 'WATING': return '待跟进'; break;
                        case 'FOLLOWING': return '跟进中'; break;
                        case 'FOLLOWED': return '已跟进'; break;
                        case 'ASSIGNED': return '已分配'; break;
                        case 'FINISHED': return '已完成'; break;
                        case 'DISABLED': return '已失效'; break;
                        default: return val; break;
                    }
                }
            },
            {
              label: "跟进话务",
              prop: "tel_operator_name",
            }, {
                label: '线索归属省',
                prop: 'clue_province_name'
            }, {
                label: '线索归属市',
                prop: 'clue_city_name'
            }, {
                label: '线索归属区',
                prop: 'clue_district_name'
            }, {
                label: '留资类型',
                prop: 'information_type',
                formatter(val) {
                    switch(val) {
                        case 'APPOINT_FORM': return '预约表单'; break;
                        case 'CALL_CONSULT': return '来电咨询'; break;
                        case 'ONLINE_CONSULTATION': return '在线咨询'; break;
                        default: return val; break;
                    }
                }
            }, {
                label: '留资广告主',
                prop: 'imformation_act_name' 
            }, /*{
                label: '通话呼出时间',
                prop: 'record_start_time',
                format: "dataFormat1",
                width: 90
            }, {
                label: '振铃时长（秒）',
                prop: 'ring_down_time',
                width: 100
            }, {
                label: '通话应答时间',
                prop: 'call_reply_time',
                format: "dataFormat1",
                width: 90
            }, {
                label: '通话结束时间',
                prop: 'record_end_time',
                format: "dataFormat1",
                width: 90
            }, {
                label: '接通时长',
                prop: 'call_connection_time'
            }, */
            {
              label: "店铺地区",
              prop: "shop_area",
            },
            {
              label: "预约活动发送店铺",
              prop: "appointment_send_shop_activity_name",
              width: 110,
            },
            {
              label: "话务跟进标签",
              prop: "tel_operator_tag",
              width: 86,
              format: 'auxFormat',
              formatParams: 'appointment_tel_operator_tag'
            },
            {
              label: "话务跟进结果",
							prop: "tel_operator_mark_requirement",
							width: 100
            },
            // {
            //   label: "预计到店时间",
            // 	prop: "tel_operator_mark_arrival",
            // 	width: 88
            // },
            {
              label: "话务跟进得分",
            	prop: "tel_operator_score",
            	width: 94
            },
            {
              label: "话务跟进时间",
              prop: "tel_operator_follow_time",
              format: "dataFormat1" ,
							width: 88
            },
            {
              label: "话务完成时间",
              prop: "tel_operator_confirm_time",
              format: "dataFormat1" ,
							width: 88
            },
            {
                label: '事业部',
                width:110,
                format: "auxFormat",
                formatParams: "business_division",
                prop: 'business_division'
            },{
                label: '店铺主营类目',
                width:200,
                format: "auxFormat",
                formatParams: "main_sale_categories",
                prop: 'main_sale_categorie'
            },  
            {
              label: "系统下推时间",
            	prop: "tel_operator_confirm_time",
              format: "dataFormat1" ,
            	width: 88
            },
          ],
          callFollowSelect: [],
          ifPermisson: true,
          isOperationChild:false,//是否【预约留资婴童部运营】
          pushYTLoading:false,//婴童下推按钮loading
        }
      },
      methods:{
        pushData(){
          let self = this;
          if(!self.search.where.length){
              this.$message.error("请添加筛选条件！");
              return;
          }
          let postData = {
            page_name:self.search.page_name,
            where:self.search.where,
          }
          this.$confirm(
					"当前操作不可逆，是否继续？",
					"提示",
					{
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "danger",
					}
				).then(() => {
					// 请求后台delete 接口 -- 待修改
					this.ajax.postStream(
						"/crm-web/api/crmAppointmentTracing/push/data",
						postData,
						(res) => {
							if (res.body.result) {
								this.$message.success(res.body.msg || "下推成功！");
                self.getList();
							} else {
								this.$message.error(res.body.msg || "下推失败！");
							}
						},
						(err) => {
							this.$message.error(err);
						}
					);
				}).catch(() => {
					return false;
				});
        },
        // 判断代理人是否角色为【预约留资婴童部运营】
        judgeOperationChild(){
          let params = {
                identification: this.getEmployeeInfo('employeeNumber'),
                key: '',
                page: {
                  length: 100,
                  pageNo: 1
                }
              }
              this.ajax.postStream('/user-web/api/userLogin/getUserRoleRelationList',params, res=>{
                if(res.body.result && res.body.content){
                  let list = res.body.content.list||[];
                  this.isOperationChild=
                    list.length>0
                    &&list.some(item=>item.roleCode=='RESERVE_OPERATION_CHILD'
                    &&item.status==1
                    &&(!item.enableTime||item.enableTime<new Date().getTime()))
                } else {
                  res.body.msg && this.$message.error(res.body.msg);
                }
              });
        },
        // 婴童下推前如果选择了【待跟进】，需先跟进
        pushYTPreFollow(){
          let details=[];
          details=this.callFollowSelect.map(item=>{
            return {id: item.id,status: item.status}
          })
          console.log('pushYTPreFollow',details)
          return new Promise((resolve,reject)=>{
            	this.ajax.postStream('/crm-web/api/crmAppointmentTracing/followUp', {details}, res => {
                if(res.body.result) {
                  resolve(true);
                } else {
                  this.$message.error(res.body.msg||'跟进失败')
                  reject(false)
                }
              }, (error) => {
                this.$message.error(error||'跟进失败')
                 reject(false)
              });
          })
        },
        pushYTData(){
            if(this.callFollowSelect.length===0){
              this.$message.warning("请选择需要操作的行")
              return
            }
            // 勾选的线索是否有【预约活动店铺】
            let hasAppointmentActivityShop=this.callFollowSelect.some(item=>item.appointment_shop_activity_name)
             if(hasAppointmentActivityShop){
              this.$message.warning('存在不满足条件的线索，不满足一键下推功能')
              return
            }

            // 勾选的存在不全是【待跟进】【跟进中】状态
            let currentSelectStatus=this.callFollowSelect[0].status;
            let isSameStatus=this.callFollowSelect.some(item=>item.status!==currentSelectStatus)
            if(!['WATING','FOLLOWING'].includes(currentSelectStatus)||isSameStatus){
              this.$message.warning('存在不满足条件的线索，不满足一键下推功能')
              return
            }

            // 勾选的主营类目是否相同
            let currentSelectMainSaleCategorie=this.callFollowSelect[0].main_sale_categorie;
            let isSameMainSaleCategorie=this.callFollowSelect.some(item=>item.main_sale_categorie!==currentSelectMainSaleCategorie)
            if(isSameMainSaleCategorie){
              this.$message.warning('存在不满足条件的线索，不满足一键下推功能')
              return
            }

             // 弹框填写1）“预约活动发送店铺"、2）"话务跟进标签"、3）"话务备注”
                let params={
                  main_sale_categorie:this.callFollowSelect[0].main_sale_categorie,
                  callback:async (data)=>{

                     // 如果是待跟进，需先调用跟进接口,只有跟进成功才能继续
                    let followResult=false;
                    if(currentSelectStatus==='WATING'){
                      followResult=await this.pushYTPreFollow();
                    }else{
                      followResult=true;
                    }
                    if(!followResult){
                      return
                    }

                    let details=this.callFollowSelect.map(item=>{
                      return{
                            status: item.status,
                            clue_city_id: item.clue_city_id,
                            clue_city_name: item.clue_city_name,
                            clue_province_id: item.clue_province_id,
                            clue_province_name: item.clue_province_name,
                            id: item.id,
                            name: item.name,
                            tel_operator_mark_requirement: item.tel_operator_mark_requirement,
                            tel_operator_mark_arrival: item.tel_operator_mark_arrival,
                            appointment_send_shop_no: data.appointment_send_shop_activity_no,
                            mobile: item.mobile,
                            customer_requirement: item.customer_requirement,
                            appointment_code: item.appointment_code,
                            tel_operator_tag: data.tel_operator_tag,
                            appointment_send_shop_activity_no: data.appointment_send_shop_activity_no,
                            appointment_send_shop_activity_name: data.appointment_send_shop_activity_name,
                            telRemark:data.tel_remark
                        }
                    })
                    this.pushYTLoading = true
                    this.ajax.postStream('/crm-web/api/crmAppointmentTracing/finishFollowUp', {details}, res => {
                      if(res.body.result) {
                          res.body.msg && this.$message.success(res.body.msg||'下推成功');
                      } else {
                          res.body.msg && this.$message.error(res.body.msg||'下推失败');
                      }
                      this.getList();
                      this.pushYTLoading = false
                  }, err => {
                        this.$message.error(err);
                        this.pushYTLoading = false
                      });
                  }
                }
                this.$root.eventHandle.$emit('alert', {
                  params,
                  component: ()=>import('@components/appoint_management/push_yt_alert'),
                      style: 'width:600px;height:300px',
                      title: '下推[婴童]',
                })
        },
        addNew() {
            this.$root.eventHandle.$emit('creatTab', 
            {
                name:'预约数据新增', 
                params: {
                  ifAdd: true,
                  eyePermisson: true
                },
                component:()=>import('@components/appoint_management/call_follow_detail.vue')
            });
        },
        // 跟进
        following(type){
          let ifStatus = type == 'following' ? 'WATING' : 'FOLLOWING'
          if (this.callFollowSelect.length === 0) {
              this.$message.error('请选择需要操作的行')
              return
          }
          if (this.callFollowSelect.length > 20 && type == 'finishFollowing') {
              this.$message.error('完成跟进批量操作的行最佳控制在20行以内')
              return
          }
					let self = this, remark_value = this.callFollowSelect[0].status
					let ifChangeSign = this.callFollowSelect.every(item => {
						return item.status == remark_value
					}), checkMsg = ''
					if (!ifChangeSign) {
						checkMsg = '请选择状态一致的数据进行批量操作'
					} else if ((type =='following' && this.callFollowSelect[0].status != 'WATING' && ifChangeSign)) {
						checkMsg = '跟进操作请选择状态为待跟进的行'
					} else if (((type =='cancleFollowing' || type =='finishFollowing') && this.callFollowSelect[0].status != 'FOLLOWING' && ifChangeSign)) {
						let key = type == 'cancleFollowing'? '取消跟进' : '完结跟进'
						checkMsg = key + '操作请选择状态为跟进中的行'
					}
					if (checkMsg) {
						this.$message.error(checkMsg)
						return
					}
					let url = '', status = '', params = {}, details = []
					if (type == 'following') {
						status = 'WATING'
						url = '/crm-web/api/crmAppointmentTracing/followUp'
					} else if (type == 'cancleFollowing') {
						status = 'FOLLOWING'
						url = '/crm-web/api/crmAppointmentTracing/cancelFollowUp'
					} else {
						status = 'FOLLOWING'
						url = '/crm-web/api/crmAppointmentTracing/finishFollowUp'
					}
					if (type == 'finishFollowing') {
						this.callFollowSelect.forEach(item => {
							details.push({
								status: item.status,
								appointment_send_shop_activity_name: item.appointment_send_shop_activity_name,
                appointment_send_shop_no: item.appointment_send_shop_no,
								clue_city_id: item.clue_city_id,
								clue_city_name: item.clue_city_name,
								clue_province_id: item.clue_province_id,
								clue_province_name: item.clue_province_name,
								id: item.id,
								if_urgent: item.if_urgent,
								name: item.name,
								tel_operator_mark_arrival: item.tel_operator_mark_arrival,
								tel_operator_mark_requirement: item.tel_operator_mark_requirement,
								tel_operator_tag: item.tel_operator_tag,
                mobile: item.mobile,
                customer_requirement: item.customer_requirement,
                appointment_code: item.appointment_code,
                appointment_send_shop_activity_no: item.appointment_send_shop_no
							})
						})
					} else {
						this.callFollowSelect.forEach(item => {
							details.push({id: item.id,status: item.status})
						})
					}
					params = {
						details
					}
					this.ajax.postStream(url, params, res => {
						if(res.body.result) {
							res.body.msg && this.$message.success(res.body.msg);
						} else {
							res.body.msg && this.$message.error(res.body.msg);
						}
						this.getList()
					}, err => {
						this.$message.error(err);
						this.getList()
					});
				},
				callFollowClassName(row, index) {
					if (row.tel_operator_tag == "TEL_FOLLOW_TWICE") {
						return "callFpllowOrange";
					}
				},
        callFollowSelectionChange(data){
          this.callFollowSelect = data
        },
        getList(resolve){
          this.btns[0].loading = true;
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getAssignFollowList',this.search,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.pageTotal = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        refresh(){
          this.getList();
        },
        searchClick(list, resolve){
          this.search.where = list;
          this.getList(resolve);
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page.length = pageSize;
          this.getList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.search.page.pageNo = page;
          this.getList();
        },
        // 导出
        exportExcel () {
          this.btns[4].loading = true;
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/exportAssignFollowList',this.search,res => {
            if(res.body.result) {
              res.body.msg && this.$message.success(res.body.msg)
            } else {
              res.body.msg && this.$message.error(res.body.msg)
            }
            this.btns[4].loading = false
          }, err => {
            this.btns[4].loading = false
            this.$message.error(err)
          })
        },
        showExportList (){
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/after_sales_report/export'),
            style:'width:900px;height:600px',
            title: '报表导出列表',
            params: {
              query: {
              type: 'EXCEL_TYPE_LIST_CRM_APPOINTMENT_TRACING',
              },
            },
          })
        },
        getPermissonCode () {
          let params = {
            identification: this.getUserInfo('employeeNumber'),
            key: '',
            page: {
              length: 100,
              pageNo: 1
            }
          }
          this.ajax.postStream('/user-web/api/userLogin/getUserRoleRelationList',params, res=>{
            if(res.body.result && res.body.content){
              let list = res.body.content.list||[];
              let ifPermissonList = list.filter(item => {
                let ifDisabled = item.disableTime ? item.disableTime > new Date().getTime() : true
                return item.roleCode === 'OUTSOURCIN_CUSTOMER' && item.enableTime < new Date().getTime() && ifDisabled && item.status == 1
              })
              this.ifPermisson = !(ifPermissonList.length > 0)
            } else {
              this.ifPermisson = true
              res.body.msg && this.$message.error(res.body.msg);
            }
            this.changeExportBtnsDisabled()
          }, err => {
            this.ifPermisson = true
            this.$message.error(err);
            this.changeExportBtnsDisabled()
          });
        },
        changeExportBtnsDisabled () {
          let self = this
          this.btns.map(item => {
            if (/^(export|exportDownload|add|cancleFollowing)$/.test(item.key) || /^(报表导出文件下载|导出|新增|取消跟进)$/.test(item.txt)) {
              item.disabled = !self.ifPermisson 
            }
          })
        }
      },
      created () {
        this.getPermissonCode()
        this.judgeOperationChild();
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
        //监听切换业务代理事件
        this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
            this.judgeOperationChild();
        });
      }
    }
</script>

<style type="text/css" >
	.callFpllowOrange,
	.callFpllowOrange td {
		background-color: orange !important;
	}
</style>
