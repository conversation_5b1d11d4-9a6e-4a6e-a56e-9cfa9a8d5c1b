
<!-- 话务跟进详情-->
<template> 
<div class="xpt-flex">
  <el-row class="xpt-top" :gutter="40">
    <el-col :span="24">
      <el-button 
        :type='btn.type' 
        size='mini' 
        v-for='(btn, index) in topBtns' 
        :key='"good" + index'
        :disabled=" typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled|| false "
        :loading="btn.loading||false" 
        @click='btn.click'
      >{{btn.txt}}</el-button>
      <xpt-upload-v3
        uploadBtnText="附件上传"
        :uploadSize="20"
        acceptType="usually"
        :dataObj="uploadDataObj"
        :disabled="!canIupload"
        btnType="success"
        style="display:inline-block;margin: 0px 10px;"
    ></xpt-upload-v3>
    <el-button type="success" size="mini" @click="getFileList" :disabled="!basicInfo.id">查看附件</el-button>
    <el-button type="success" size="mini" @click="sendMessage" >短信发送</el-button>
    </el-col>
  </el-row>
  <div>
  <el-row	:gutter='40' >
    <el-tabs v-model="firstTab" >
        <el-tab-pane label="基本信息" name="offSite_info">
        <el-form label-position="right" class="mgt10" label-width="100px" :model="basicInfo" :rules="rules" ref="basicInfo">
          <el-col :span='8'>
            <el-form-item label="客户姓名：" prop="name">
              <el-input v-model="basicInfo.name" size='mini' :disabled="ifAdd ? false : !(basicInfo.status=='FOLLOWING' && ifCallProxyId)" :maxlength='20'></el-input>
              <!-- <el-input v-model="basicInfo.name" size='mini' :disabled="ifAdd ? false : !(basicInfo.status=='FOLLOWING' && ifCallProxyId)" ></el-input> -->
                <el-tooltip v-if='rules.name[0].isShow' class="item" effect="dark" :content="rules.name[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip>
            </el-form-item>
            <el-form-item label="手机号码：" prop="mobile">
              <el-input size='mini' v-if="isOutsourceCustomer" disabled value="***********"></el-input>
              <xpt-eye-switch v-model="basicInfo.mobile" v-else :disabled="!ifAdd" :aboutNumber="basicInfo.appointment_question_no"></xpt-eye-switch> 
              <el-tooltip v-if='rules.mobile[0].isShow' class="item" effect="dark" :content="rules.mobile[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="预约活动店铺："  prop="appointment_shop_activity_name">
              <el-input v-model="basicInfo.appointment_shop_activity_name" size='mini' disabled></el-input>
            </el-form-item>
            <el-form-item label="预约提交时间：" prop="appointment_submit_time">
              <el-date-picker v-model="basicInfo.appointment_submit_time" type="datetime" placeholder="选择日期" size='mini' :disabled='true' :editable='false'></el-date-picker>
            </el-form-item>
            <el-form-item label="活动类型" prop="appointment_act_type">
                <el-input v-model="basicInfo.appointment_act_type" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="预约活动：" required>
              <el-input v-model="basicInfo.appointment_activity_name" size='mini' readonly icon="search" :on-icon-click="selectActivity" :disabled="!ifAdd" ></el-input>
              <el-tooltip v-if='rules.appointment_activity_name[0].isShow' class="item" effect="dark" :content="rules.appointment_activity_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="留资平台：" required>
                <el-input size='mini' v-model="basicInfo.information_platform_name" readonly icon="search" :on-icon-click="selectPlatform"  :disabled="!ifAdd"></el-input>
                <el-tooltip v-if='rules.information_platform_name[0].isShow' class="item" effect="dark" :content="rules.information_platform_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
                </el-tooltip>
            </el-form-item>
            <el-form-item label="留资渠道："  prop="appointment_channel_name">
              <el-input v-model="basicInfo.appointment_channel_name" disabled  size='mini' ></el-input>
            </el-form-item>
            <el-form-item label="留资类型：" prop="information_type">
                <el-select placeholder="请选择" size='mini' v-model="basicInfo.information_type" :disabled="!ifAdd">
                    <el-option label="预约表单" value="APPOINT_FORM"></el-option>
                    <el-option label="来电咨询" value="CALL_CONSULT"></el-option>
                    <el-option label="在线咨询" value="ONLINE_CONSULTATION"></el-option>
                </el-select>
                <el-tooltip v-if='rules.information_type[0].isShow' class="item" effect="dark" :content="rules.information_type[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
                </el-tooltip>
            </el-form-item>
            <el-form-item label="店铺主营类目" prop="main_sale_categorie">
                <xpt-select-aux v-model='basicInfo.main_sale_categorie' aux_name='main_sale_categories' disabled></xpt-select-aux>
            </el-form-item>
          </el-col>
          <el-col :span='8'>
            <el-form-item label="线索归属省：" prop="clue_province_id">
                <el-select placeholder="请选择" size='mini' v-model="basicInfo.clue_province_id"  :disabled="ifAdd ? false :  !(basicInfo.status=='FOLLOWING' && ifCallProxyId)" @change="selectProvince">
                    <el-option
                    v-for="(value,key) in provinceObj"
                    :label="value"
                    :value="parseInt(key)"  :key='key'>
                    </el-option>
                </el-select>
                <el-tooltip v-if='rules.clue_province_id[0].isShow' class="item" effect="dark" :content="rules.clue_province_id[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip>
            </el-form-item>
            <el-form-item label="线索归属市：" prop="clue_city_id">
                <el-select placeholder="请选择" size='mini' v-model="basicInfo.clue_city_id" @change="selectCity" :disabled="ifAdd ? false : !(basicInfo.status=='FOLLOWING' && ifCallProxyId)">
                    <el-option
                        v-for="(value,key) in cityObj"
                        :label="value"
                        :value="parseInt(key)" :key='key'>
                    </el-option>
                </el-select>
                <el-tooltip v-if='rules.clue_city_id[0].isShow' class="item" effect="dark" :content="rules.clue_city_id[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip>
            </el-form-item>
            <el-form-item label="线索归属区：" prop="clue_district_id">
                <el-select placeholder="请选择" size='mini' v-model="basicInfo.clue_district_id"  :disabled="ifAdd ? false : !(basicInfo.status=='FOLLOWING' && ifCallProxyId)">
                    <el-option
                        v-for="(value,key) in districtObj"
                        :label="value"
                        :value="parseInt(key)" :key='key'>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="线索推广链接：" prop="act_url">
                <el-input size='mini' v-model="basicInfo.act_url" readonly @click.native="onLink(basicInfo.act_url)"></el-input>
            </el-form-item>
            <el-form-item label="状态：" prop="status">
                <el-select placeholder="请选择" size='mini' v-model="basicInfo.status" disabled>
                    <el-option label="待跟进" value="WATING"></el-option>
                    <el-option label="跟进中" value="FOLLOWING"></el-option>
                    <el-option label="已跟进" value="FOLLOWED"></el-option>
                    <el-option label="已分配" value="ASSIGNED"></el-option>
                    <el-option label="已完成" value="FINISHED"></el-option>
                    <el-option label="已失效" value="DISABLED"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="事业部：" prop="business_division">
                <xpt-select-aux v-model='basicInfo.business_division' aux_name='business_division' disabled></xpt-select-aux>
            </el-form-item>
          </el-col>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </el-row>
  <div class="xpt-flex__bottom" v-fold>
    <el-tabs v-model="secondTab">
      <el-tab-pane label="跟进信息" name="follow_info" class='xpt-flex'>
          <el-form label-position="right" class="mgt10" label-width="130px" :model="basicInfo" :rules="rules" ref="basicInfo">
              <el-col :span="8">
                  <el-form-item label="话务员：">
                      <el-input v-model="basicInfo.tel_operator_name" size='mini' disabled></el-input>
                  </el-form-item>
                  <el-form-item label="预约活动发送店铺：" required>
                      <el-input v-model="basicInfo.appointment_send_shop_activity_name" size='mini' readonly icon="search" :on-icon-click="selectShop" :disabled="ifAdd ? false :  !(basicInfo.status=='FOLLOWING' && ifCallProxyId)" ></el-input>
                      <el-tooltip v-if='rules.appointment_send_shop_activity_name[0].isShow' class="item" effect="dark" :content="rules.appointment_send_shop_activity_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                          <i class='el-icon-warning'></i>
                      </el-tooltip>
                  </el-form-item>
                  <el-form-item label="客户微信：">
                      <el-input v-model="basicInfo.customer_wechat" size='mini' :maxlength='20' ></el-input>
                  </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="话务跟进时间：">
                  <el-date-picker 
                      v-model="basicInfo.tel_operator_follow_time"
                      type="datetime"
                      :editable="false"
                      disabled
                      placeholder="选择时间"
                      size="mini">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="话务跟进标签：" prop="tel_operator_tag" required>
                  <el-select placeholder="请选择" size='mini' v-model="basicInfo.tel_operator_tag"  :disabled="ifAdd ? false :  !(basicInfo.status=='FOLLOWING' && ifCallProxyId)" @change="tagChange">
                      <el-option 
                        v-for="(item, idx) in telOperatorTagList" 
                        :label="item.name" :key="idx" 
                        :value="item.code" 
                        :disabled="/^(TEL_FOLLOW_TWICE|SYSTEM_PUSH)$/.test(item.code) ? true : (item.status == 1 ? !(basicInfo.main_sale_categorie == item.tag || item.tag === 'ALL') : true)"
                      ></el-option>
                      <!-- <el-option label="有意向" value="TEL_HAVE_INTENTION"></el-option>
                      <el-option label="未接电话" value="TEL_NO_INTENTION"></el-option>
                      <el-option label="无效" value="TEL_INVALID"></el-option>
                      <el-option label="系统直接推送" value="SYSTEM_PUSH" disabled></el-option>
                      <el-option label="二次跟进" value="TEL_FOLLOW_TWICE" disabled></el-option> -->
                  </el-select>
                  <el-tooltip v-if='rules.tel_operator_tag[0].isShow' class="item" effect="dark" :content="rules.tel_operator_tag[0].message" placement="right-start" popper-class='xpt-form__error'>
                      <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="话务跟进得分：">
                  <el-input v-model="basicInfo.tel_operator_score" size='mini' disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8"> 
                <!-- <el-form-item label="立即下推到店长：">
                    <el-switch v-model="basicInfo.if_urgent" on-text="是" off-text="否" off-value="N" on-value="Y" :disabled="!(basicInfo.status=='FOLLOWING' && ifCallProxyId)"></el-switch>
                </el-form-item> -->
                <el-form-item label="话务完成时间：">
                  <el-date-picker 
                    v-model="basicInfo.tel_operator_confirm_time"
                    type="datetime"
                    :editable="false"
                    disabled
                    placeholder="选择时间"
                    size="mini">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="预计到店时间：" prop="tel_operator_mark_arrival">
                  <el-input v-model="basicInfo.tel_operator_mark_arrival" :disabled="ifAdd ? false : !(basicInfo.status=='FOLLOWING' && ifCallProxyId)" size="mini"> </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="22">
                <!-- <el-form-item label="话务跟进结果：" style="height: 55px;" required>
                  <el-input v-model="basicInfo.tel_operator_mark_requirement" type='textarea' size='mini' :disabled="ifAdd ? false : !(basicInfo.status=='FOLLOWING' && ifCallProxyId)"  maxlength='250' placeholder=""></el-input>
                  <el-tooltip v-if='rules.tel_operator_mark_requirement[0].isShow' class="item" effect="dark" :content="rules.tel_operator_mark_requirement[0].message" placement="right-start" popper-class='xpt-form__error'>
                      <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item> -->
                <el-form-item label="客户需求：" style="height: 55px;">
                  <el-input v-model="basicInfo.customer_requirement" :maxlength='200' type='textarea' size='mini'   :disabled="ifAdd ? false : !(basicInfo.status=='FOLLOWING' && ifCallProxyId)"   placeholder=""></el-input>
                  <!-- <el-tooltip v-if='rules.customer_requirement[0].isShow' class="item" effect="dark" :content="rules.customer_requirement[0].message" placement="right-start" popper-class='xpt-form__error'>
                      <i class='el-icon-warning'></i>
                  </el-tooltip> -->
                </el-form-item>
                <el-form-item label="话务备注：" style="height: 55px;">
                  <el-input v-model="basicInfo.tel_remark" type='textarea' :maxlength='200' size='mini'  :disabled="ifAdd ? false : !(basicInfo.status=='FOLLOWING' && ifCallProxyId)"   placeholder=""></el-input>
                </el-form-item>
              </el-col>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="最近通话记录" name="call_info" class='xpt-flex' v-if="false">
        <el-form label-position="right" class="mgt10" label-width="100px" :model="callInfo" :rules="callRules" ref="basicInfo">
          <el-col :span="8">
            <el-form-item label="通话呼出时间：">
                <el-input v-model="callInfo.callOutTime" size='mini' disabled></el-input>
            </el-form-item>
            <el-form-item label="通话应答时间：">
                <el-input v-model="callInfo.callTime" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="振铃时长(s)：" prop="shakeSecond">
              <el-input v-model="callInfo.shakeSecond" size='mini' disabled></el-input>
            </el-form-item>
            <el-form-item label="通话结束时长：">
                <el-input v-model="callInfo.callFinishTime" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="接通时长(s)：" prop="callSecond">
              <el-input v-model="callInfo.callSecond" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label='通话详情' name='record_list' class='xpt-flex'>
        <xpt-list
          :data='recordList'
          :colData='recordListCols'
          :showHead='false'
          :pageTotal='recordCount'
          selection=''
          @page-size-change='recordListPageSizeChange'
          @current-page-change='recordListPageChange'
				></xpt-list>
      </el-tab-pane>
    </el-tabs>
  </div>
</div>
  <div class="call_dialog" v-show="ifCall">
      <strong v-show="ifShow">最小化</strong>
      <span class="call_icon" @click="hideCalling()" @mouseenter="enter()" @mouseleave="leave()">
          <i class="el-icon-minus mgr4"></i>
      </span>
      <div class="call_racle">
          <p class="calling">
              <span class="call_span_first">通话中</span>
              <span class="call_span_last">{{talkTime}}</span>
          </p>
      </div>
      <div class="call_btn_area">
          <span class="call_btn" @click='finishCall()'>挂断</span>
      </div>
  </div>
  <div class="show_calling" @click="showCalling()" v-show="!ifCall && !ifCallHide">
      <i class="iconfont icon-dainhau"></i>
  </div>
</div>
</template>
<script>
import Vue from 'vue'
import VL from '@common/validate.js';
import basicFun from "@components/call_system/six_page_callFun/basicFun";
import { makeUrl, apiUrl, EventBus } from '@components/call_system/base.js';
export default {
  inject: [ 'diaConfig' ],
  props: ['params'], //上游参数
  data() {
    var self = this;//本vue
    return{
      firstTab:"offSite_info",
      secondTab: 'follow_info',
      bill_type_id_tag: '',
      provinceObj:{},
      cityObj:{},
      districtObj: {},
      basicInfo: {
        clue_province_name: '',
        mobile: '',
        name: '',
        clue_city_name: '',
        information_type: '',
        appointment_shop_activity_name: '',
        status: '',
        // shop_area: '',
        appointment_submit_time: '',
        information_platform_name: '',
        appointment_channel_name: '',
        tel_operator_name: '',
        appointment_send_shop_activity_name: '',
        tel_operator_follow_time: '',
        tel_operator_tag: '',
        tel_operator_mark_arrival: '',
        // if_urgent: '',
        tel_operator_mark_requirement: '',
        tel_operator_confirm_time: '',
        act_url: '',
        platformNo: '',  
        information_platform_name:  '', 	
        appointment_channel_name:  '',  
        appointment_activity_name:  '',  
        activityId:  ''


      },//表单内容
      rules:{
        ...[
            {
                name: '客户姓名'
            },{
                clue_city_id: '线索归属市'
            }, {
                clue_province_id: '线索归属省'
            },
             {
                appointment_send_shop_activity_name: '预约活动发送店铺'
            }, {
                tel_operator_tag: '话务跟进标签'
            }, {
                appointment_activity_name: '预约活动'
            }, {
              information_type: '留资类型'
            }, {
              information_platform_name: '留资平台'
            }, {
              mobile: '手机号码'
            }
        ].reduce((a, b) => {
            var key = Object.keys(b)[0]
            a[key] = VL.isNotBlank({
                self: this,
                msg: '请填写' + b[key],
            })
            return a
        }, {})
      },
      followRules:{},
      followInfo: {
          tel_operator_name: '',
          appointment_send_shop_activity_name: '',
          tel_operator_follow_time: '',
          tel_operator_tag: '',
          tel_operator_mark_arrival: '',
          // if_urgent: '',
          tel_operator_mark_requirement: ''
      },
      callInfo: {
          callOutTime: '',
          callTime: '',
          shakeSecond: '',
          callFinishTime: '',
          callSecond: ''
      },
      callRules: {},
      topBtns: [
        { 
          type: 'primary', 
          txt: '刷新', 
          disabled () {
            return self.params.id ? false : true
          },
          click () {
            self.getInfo()
          } 
        }, { 
          type: 'warning', 
          txt: '跟进', 
          disabled(){
            return self.basicInfo.status != 'WATING'
          }, 
          click(){
            self.following('following')
          } 
        }, { 
          type: 'warning', 
          txt: '二次跟进', 
          disabled(){
            return self.basicInfo.status != 'FOLLOWING'
          }, 
          click(){
            self.following('followingTwice')
          } 
        }, { 
          type: 'warning', 
          txt: '取消跟进', 
          disabled(){
            return self.basicInfo.status != 'FOLLOWING' || self.isOutsourceCustomer
          }, 
          click(){
            self.following('cancleFollowing')
          } 
        }, { 
          type: 'warning', 
          txt: '完成跟进', 
          disabled(){
            return self.basicInfo.status != 'FOLLOWING'
          },
          click(){
            self.finishFollowPre()
          },
          loading: false,
        },{ 
          type: 'warning', 
          txt: '呼出',
          loading: false, 
          disabled(){
              // return (self.basicInfo.mobile && self.basicInfo.status == 'FOLLOWING')
              return false
          },
          click(){
              self.exhaleKeybord()
          }
        },{ 
          type: 'warning', 
          txt: '飞鱼呼出',
          loading: false, 
          disabled(){
              // return (self.basicInfo.mobile && self.basicInfo.status == 'FOLLOWING')
              return false
          },
          click(){
              self.checkReadyCall()
          }
        }
      ],
      ifCall: false,
      second: 0,
      callTimer: '',
      talkTime: '',
      ifCallHide: true,
      ifShow: false,
      ifCallProxyId: true,
      currentRequirement: '',
      telOperatorTagList:__AUX.get('appointment_tel_operator_tag').reduce((newObj, obj) => {
        newObj.push(obj)
        return newObj
      }, []),
      ifAdd: false,
      uploadDataObj: {},
      recordList: [],
      recordListCols: [
        {
          label: "话务员",
          prop: "userName",
        },
        {
          label: "接通状态",
          prop: "connectStatus",
          formatter: (val)  =>{
            let obj = {
              0:'未接通',
              1:'已接通'
            }
            return obj[val] || val;
          }
        },
        {
          label: "通话时长（秒）",
          prop: "callDuration",
        },
        {
            label:'拨打时间',
            prop:'beginTime',
            format: "dataFormat1",
        },
        {
          label: "有效通话",
          prop: "effect",
          formatter: (val)  =>{
            let obj = {
              0:'否',
              1:'是'
            }
            return obj[val] || val;
          }
        },
        {
          label: "录音",
          prop: "recordFile",
          redirectClick(row) {
            if (row.recordFile) {
              window.open(row.recordFile);
            }
          },
          formatter: val => {
            if (val) return "播放";
          },
          width: 50
        }
      ],
      recordCount: 0,
      recordPageNo: 1,
      recordPageSize: 50,
      pageTwoDiaConfig: this.diaConfig,
      isOutsourceCustomer:false,//是否新零售外包话务
      feiyuClueInfo:{},

      isFirstLoad:false,
      shopDetail:{
        addressDetail:'',
        guideConcatTel:'',
      }
    }
  },
  computed: {
    canIupload () {
      return /^(FOLLOWING|FOLLOWED)$/.test(this.basicInfo.status)
    }
  },
  created () {
    this.isFirstLoad = true
    //监听切换业务代理事件
    this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
        this.changeCallProxyId();
    });
    if((this.params && this.params.ifAdd) || (this.params.menuInfo && this.params.menuInfo.code === 'ADD_CALL_FOLLOW_RECORDS')) {
      this.ifAdd = true
      this.topBtns.push({
        type: 'warning', 
        txt: '保存', 
        disabled(){
          return false
        },
        click: this.save
      })
    }

    if(this.params&&this.params.isOutsourceCustomer){
      this.isOutsourceCustomer=true;
    }else{
      this.judgeOutsourceCustomer();
    }
  },
  methods:{
    getShopDetail(id){
        if(!id) return;
        let url = '/material-web/api/shopv2/expand/edit'
        this.ajax.postStream(url, id, (res)=>{
            let {result,content:{addressDetail,guideConcatTel}} = res.body
            if(!result) return;
            this.shopDetail = {
                addressDetail,
                guideConcatTel,
            }
        });
    },
    onLink(url){
        if(!url) return
        window.open(url)
    },
    judgeOutsourceCustomer(){
      let params = {
            identification: this.getUserInfo('employeeNumber'),
            key: '',
            page: {
              length: 100,
              pageNo: 1
            }
          }
          this.ajax.postStream('/user-web/api/userLogin/getUserRoleRelationList',params, res=>{
            if(res.body.result && res.body.content){
              let list = res.body.content.list||[];
              this.isOutsourceCustomer=
                list.length>0
                &&list.some(item=>item.roleCode=='OUTSOURCIN_CUSTOMER'
                &&item.status==1
                &&(!item.enableTime||item.enableTime<new Date().getTime()))
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          });
    },
    sendMessage () {
      if (!this.basicInfo.appointment_send_shop_activity_name) {
        this.$message.error('预约活动发送店铺不能为空')
        return
      }
      let params = {
        mobile: this.basicInfo.mobile, // 用户手机号
        shopMobile: this.shopDetail.guideConcatTel || this.basicInfo.appointment_send_shop_mobile, // 门店联系电话
        address: this.shopDetail.addressDetail || this.basicInfo.appointment_send_shop_address,// 门店地址
        shopName: this.basicInfo.appointment_send_shop_activity_name// 预约活动发送店铺
      }
      this.ajax.postStream('/crm-web/api/crmAppointmentTracing/sendMsg', params, res => {
        this.$message({
          message: res.body.msg,
          type: res.body.result ? 'success' : 'error'
        });
      }, err => {
        this.$message.error(err);
      });
    },
    recordListPageSizeChange (size) {
      this.recordPageSize = size
      this.getRecordList()
    },
    recordListPageChange (page) {
      this.recordPageNo = page
      this.getRecordList()
    },
    getRecordList () {
      let self = this,
      params = {
        page: this.recordPageNo,
        size: this.recordPageSize,
        appointment_tracing_id: this.params.id ? this.params.id : this.basicInfo.id
      }
      this.ajax.postStream('/callcenter-web/callStat/crm/call/detail.do', params, res => {
        if(res.body.code == 0) {
          self.recordList = res.body.content || []
          self.recordCount = res.body.totalNum || 0
        } else {
          self.recordList = []
          self.recordCount = 0
        }
      }, err => {
        this.$message.error(err);
      });
    },
    selectActivity() {
      if(!this.ifAdd) return
      this.$root.eventHandle.$emit('alert', {
        params: {
            source: 'callFollow',
            ifAlert: true,
            selection: 'radio',
            callback: data => {
                this.basicInfo.appointment_activity_name = data.appointmentActivityName;
                this.basicInfo.activityId = data.appointmentActivityId;
                this.basicInfo.main_sale_categorie = data.main_sale_categorie;
                this.basicInfo.appointment_activity_id = data.appointmentActivityId
                this.basicInfo.business_division = data.businessDivision
            },
        },
        component: ()=>import('@components/appoint_management/appointment_activity'),
            style: 'width:800px;height:500px',
            title: '预约活动列表',
      })
    },
    selectPlatform () {
      if (!this.basicInfo.appointment_activity_name) {
        this.$message.error('请先选择预约活动！')
        return
      }
      if(!this.ifAdd) return
      let params = {
          ifAlert: true,
          activityId: this.basicInfo.activityId
      }, self = this;
      params.callback = t => {
        this.basicInfo.platformNo = t.platformNo
        this.basicInfo.information_platform_name = t.platformName
        this.basicInfo.appointment_channel_name = t.platformChannel
      }
      self.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/appoint_management/information_platform_activity.vue'),
          style: 'width:800px;height:500px',
          title: '留资平台列表'
      });
    },
    save (operateType, idx) {
      if (!(/^[1](([3-9])[0-9]{9}$)|(([3-9])[0-9]{9}-[0-9]{4}$)/.test(this.basicInfo.mobile))) {
        this.$message.error('请填写有效的手机号码')
        if (operateType === 'finishFollowing') {
          this.topBtns[idx].loading = false
        }
        return
      }
      let params = {
        name: this.basicInfo.name,
        tel: this.basicInfo.mobile,
        platformNo: this.basicInfo.platformNo,
        provinceId: this.basicInfo.clue_province_id,
        cityId: this.basicInfo.clue_city_id,
        districtId: this.basicInfo.clue_district_id,
        platformChannel: this.basicInfo.platformChannel,
        platformType: this.basicInfo.information_type,
        shopId: this.basicInfo.shopId,
        telOperatorTag: this.basicInfo.tel_operator_tag,
        wechat: this.basicInfo.customer_wechat,
        activityId: this.basicInfo.activityId,
        customer_requirement: this.basicInfo.customer_requirement,
        telRemark: this.basicInfo.tel_remark,
        appointmentTime: this.basicInfo.tel_operator_mark_arrival,
      }
      if (!(operateType === 'finishFollowing')) {
        let msg = ''
        if (!this.basicInfo.appointment_send_shop_activity_name) {
            msg = '预约活动发送店铺'
        } else if (!params.name){
            msg = '客户姓名'
        } else if (!params.provinceId){
            msg = '线索归属省'
        } else if (!params.cityId){
            msg = '线索归属市'
        } else if (!params.activityId){
            msg = '预约活动'
        } else if (!params.platformNo){
            msg = '留资平台'
        } else if (!params.platformType){
            msg = '留资类型'
        } else if (!params.telOperatorTag){
            msg = '话务跟进标签'
        }
        if (msg) {
            let tipMsg = msg + '不能为空'
            this.$message.error(tipMsg)
            if (operateType === 'finishFollowing') {
              this.topBtns[idx].loading = false
            }
            return
        }
      }
      this.basicInfo.id ? params.id = this.basicInfo.id : ''
      let self = this
      this.ajax.postStream('/crm-web/api/crm_appointment_activity/addAppointmentBySelf', params, res => {
        if(res.body.result) {
          res.body.msg && this.$message.success(res.body.msg);
          if (operateType === 'finishFollowing') {
            this.following(operateType, idx)
          } else {
            if (res.body.content) {
              self.basicInfo.id = res.body.content
              let params = {
                  id:res.body.content,
                  tabName: self.params.tabName
              }
              params.__close = function(){
                  self.$root.eventHandle.$emit('removeTab',self.params.tabName);
              }
              self.$root.eventHandle.$emit('updateTab',{
                  name:self.params.tabName,
                  params:params,
                  title:'话务跟进详情',
                  component: () => import('@components/appoint_management/call_follow_detail.vue')
              })
              self.getInfo()
            }
          }
        } else {
          res.body.msg && this.$message.error(res.body.msg);
          if (operateType === 'finishFollowing') {
            this.topBtns[idx].loading = false
          }
        }
      }, err => {
        this.$message.error(err);
        if (operateType === 'finishFollowing') {
          this.topBtns[idx].loading = false
        }
      });
    },
    // 锁定人与代理人的判断
    changeCallProxyId () {
        if (this.basicInfo && this.basicInfo.tel_operator_id == this.getUserInfo('id')) {
            this.ifCallProxyId = true
        } else {
            this.ifCallProxyId = false
        }
    },
    // 地区
    getAreaCode(code,key){
        let self = this;
        let url = "/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId";
        //let url = "/order-web/api/customer/receiverInfo/addReceriverInfo?permissionCode=ORDER_MERGEORDER_CUSTOMER_SAVE";
        if(!code) return;
        this.ajax.postStream(url,code,function(response){
            if(response.body.result){
            self[key] = response.body.content
            }
        })
    },
    selectProvince(address){//选择省份，获得市级信息
        if(!address) return;
        this.getAreaCode(this.basicInfo.clue_province_id,"cityObj");
        if(!this.isFirstLoad){
            this.basicInfo.clue_city_name = '';
            this.basicInfo.clue_city_id = '';
            this.basicInfo.clue_district_name = '';
            this.basicInfo.clue_district_id = '';
        }
    },
    selectCity(address){//选择市级，获得区级信息
        console.log(address,'selectCity=====');
        if(!address) return;
        this.getAreaCode(this.basicInfo.clue_city_id,"districtObj");
        if(!this.isFirstLoad){
            this.basicInfo.clue_district_name = '';
            this.basicInfo.clue_district_id = '';
            return
        }
        this.isFirstLoad = false
       
    },
    // 跟进话务标签更改
    tagChange() {
      if (!this.basicInfo.appointment_activity_name) {
        this.$message.error('请先选择预约活动！')
        this.$set(this.basicInfo, 'tel_operator_tag', '')
        return
      }
      let tagList = ['TEL_NO_INTENTION']
      let tagObj = {
        // 'TEL_FOLLOW_TWICE': '门店二次跟进',
        'TEL_NO_INTENTION': '电话暂时无法接通，需要导购再次跟进',
        // 'SYSTEM_PUSH': '系统直推，需要导购马上跟进'
      }
      if (new Set(tagList).has(this.basicInfo.tel_operator_tag)) {
        this.$set(this.basicInfo, 'tel_remark', tagObj[this.basicInfo.tel_operator_tag])
          // this.basicInfo.tel_remark = tagObj[this.basicInfo.tel_operator_tag]
      } else {
        this.$set(this.basicInfo, 'tel_remark', this.currentRequirement)
          // this.basicInfo.tel_remark = this.currentRequirement
      }
    },
    finishFollowPre() {
      let idx = ''
      this.topBtns.forEach((element,index) => {
        if (element.txt == '完成跟进') {
          idx = index
        }
      });
      this.topBtns[idx].loading = true
      this.save('finishFollowing', idx)
    },
    // 跟进
    following(type, idx){
        if (!this.basicInfo.id) return
        let self = this, url = '', status = '', params = {}, details = []
        if (type == 'following') {
            status = 'WATING'
            url = '/crm-web/api/crmAppointmentTracing/followUp'
        } else if (type == 'cancleFollowing') {
            status = 'FOLLOWING'
            url = '/crm-web/api/crmAppointmentTracing/cancelFollowUp'
        } else if (type == 'followingTwice') {
            status = 'FOLLOWING'
            url = '/crm-web/api/crmAppointmentTracing/telFollowTwice'
        } else {
            status = 'FOLLOWING'
            url = '/crm-web/api/crmAppointmentTracing/finishFollowUp'
        }
        if (type == 'finishFollowing') {
            let msg = ''
            details.push({
                status: this.basicInfo.status,
                appointment_send_shop_activity_name: this.basicInfo.appointment_send_shop_activity_name,
                clue_city_id: this.basicInfo.clue_city_id,
                clue_city_name: this.cityObj[this.basicInfo.clue_city_id],
                clue_province_id: this.basicInfo.clue_province_id,
                clue_province_name: this.provinceObj[this.basicInfo.clue_province_id],
                clue_district_id: this.basicInfo.clue_district_id,
                clue_district_name: this.districtObj[this.basicInfo.clue_district_id],
                id: this.basicInfo.id,
                // if_urgent: this.basicInfo.if_urgent,
                name: this.basicInfo.name,
                tel_operator_mark_requirement: this.basicInfo.tel_operator_mark_requirement,
                tel_operator_tag: this.basicInfo.tel_operator_tag,
                tel_operator_mark_arrival: this.basicInfo.tel_operator_mark_arrival,
                appointment_send_shop_no: this.basicInfo.appointment_send_shop_no,
                mobile: this.basicInfo.mobile,
                customer_requirement: this.basicInfo.customer_requirement,
                appointment_code: this.basicInfo.appointment_code,
                appointment_send_shop_activity_no: this.basicInfo.appointment_send_shop_no
            })
            if (!this.basicInfo.appointment_send_shop_activity_name) {
                msg = '预约活动发送店铺'
            } else if (!details[0].name){
                msg = '客户姓名'
            } else if (!details[0].clue_city_name){
                msg = '线索归属市'
            } else if (!details[0].clue_province_name){
                msg = '线索归属省'
            } else if (!details[0].tel_operator_tag){
                msg = '话务跟进标签'
            } 
            if (msg) {
                let tipMsg = msg + '不能为空'
                this.$message.error(tipMsg)
                this.topBtns[idx].loading = false
                return
            }
        } else {
            details.push({id: this.basicInfo.id,status: this.basicInfo.status})
        }
        params = {
            details
        }
        this.ajax.postStream(url, params, res => {
            if(res.body.result) {
                res.body.msg && this.$message.success(res.body.msg);
            } else {
                res.body.msg && this.$message.error(res.body.msg);
            }
            if(type == 'following'){
               if(res.body.result){
                this.getInfo()
               }
            }else if (type != 'followingTwice') {
                this.getInfo()
            } else {
                this.$root.eventHandle.$emit('removeTab', this.params.tabName)
            }
            if (type == 'finishFollowing') {
              this.topBtns[idx].loading = false
            }
        }, err => {
            this.$message.error(err);
            this.getInfo()
            if (type == 'finishFollowing') {
              this.topBtns[idx].loading = false
            }
        });
    },
    enter(){
        this.ifShow = true
    },
    leave() {
        this.ifShow = false
    },
    showCalling () {
        this.ifCall = true
        this.ifCallHide = true
    },
    hideCalling() {
        this.ifCall = false
        this.ifCallHide = false
    },
    operationPermission (resolve, reject) {
        if (!this.basicInfo.id) return
        this.ajax.postStream('/crm-web/api/crmAppointmentTracing/validateOperationPermissions',{id: this.basicInfo.id},res => {
            if(res.body.result) {
                res.body.msg && this.$message.success(res.body.msg);
                resolve && resolve()
            } else {
                res.body.msg && this.$message.error(res.body.msg);
                this.topBtns[4].disabled = false
                reject && reject()
            }
        }, err => {
            this.$message.error(err);
            this.topBtns[4].disabled = false
            reject && reject()
        });
    },
    initCall(resolve,reject){
      let self = this;
      this.ajax.get('/callcenter-web/feiyu/clue/get/token.do',function(response){
          if(response.body.result && response.body.content) {
            resolve && resolve(response.body);
            self.feiyuClueInfo = response.body.content
          } else {
            self.$message.error(response.body.msg || '');
          }
        });
    },
    checkReadyCall(){
      let self = this;
      Vue.prototype.feiyuCallObj.checkReadyCall().then(({ code, msg }) => {
        console.log(code,msg)
        if (code) {
          // 开启sdk的视图的话，可以不用管错误处理
          message.error(msg);
        } else {
          self.setFeiyuCallInfo();
        }
      })
    },
    setFeiyuCallInfo(){
      let self = this;
      
      this.ajax.postStream('/callcenter-web/feiyu/clue/common/call.do',
      {
        appointmentTracingId:self.basicInfo.id,
        // station_clue_id:self.basicInfo.id,
        advertiser_id:self.basicInfo.advertiser_id,
        clue_id:self.basicInfo.station_clue_id,
        callee_number:self.basicInfo.mobile,
        // access_token:self.feiyuClueInfo.access_token
      },
      function(response){
          if(response.body.result) {
            // resolve && resolve(response.body);
          } else {
            self.$message.error(response.body.msg || '');
          }
        });
    },
    oceanenginePhoneCall(){
      let self = this;
      // console.log(Vue.prototype.feiyuCallObj)
        if( Object.keys(Vue.prototype.feiyuCallObj) != 0) return;
        self.$bus.emit('feiyuCallObjInit', true);
        Vue.prototype.feiyuCallObj = new window.FeiyuCall(
        {
           view: true ,
           onInit: () => {
             new Promise((resolve,reject)=>{
               self.initCall(resolve)
               
             }).then(res=>{
                Vue.prototype.feiyuCallObj.init(res.content.telToken);
             })
            },
            onMessage: (status, data) => {
            // do something
            console.log(status,data )
            if(status == 'WRAPUP'){
              self.getRecordList();
            }
          }
        }
      );

    },

    // 呼出键盘
		exhaleKeybord() {
      // this.showCalling()
		  const self = this;
		  // 当呼叫号码弹窗已经打开的手时候进入另一层的校验
			if (this.pageTwoDiaConfig.dialogVisible) {
			  basicFun.verifyExhaleKeybord1(self);
				// this.$message.warning('呼叫面板已打开');
				// return;
			} else if(!this.pageTwoDiaConfig.dialogVisible){
        // 弹窗尚未打开的情况下，收集信息打开弹窗
        this.gatherData();
        this.$bus.emit('changeCallProp', true);
      };
		},
    gatherData() {
        let params = {
          pageNo: 1,
          pageSize: 20,
          staffNo: this.getEmployeeInfo('employeeNumber'),
          staffName: "",
          groupName: "",
          appointment_tracing_id:this.params.id ? this.params.id : this.basicInfo.id
        };
        this.$http
          .post(apiUrl.personGroup_list1, { params })
          .then(res => {
            if (res.data.code == 0 && res.data.content.length > 0) {
              this.nowUserData = res.data.content[0];
            } else if (res.data.code != 0) {
              this.$message.error(res.data.msg || "");
            }
          })
          .catch(err => {
            this.$message.error(`${err.status}` || "");
          })
          .finally(() => {
          });
      },
    // 呼出电话
    phoneCallOut(){ console.log('呼出测试', window)
        this.topBtns[4].disabled = true
        let self = this
        new Promise((resolve, reject) => {
            this.operationPermission(resolve, reject);
        }).then(() => {
            // window.application.oJVccBar.MakeCall(self.basicInfo.mobile, 0, '', '', '');
            self.ifCall = true
            self.showTime()
        })
    },
    // 呼出键盘
		exhaleKeybord() {
      // this.showCalling()
		  const self = this;
		  // 当呼叫号码弹窗已经打开的手时候进入另一层的校验
			if (this.pageTwoDiaConfig.dialogVisible) {
			  basicFun.verifyExhaleKeybord1(self);
				// this.$message.warning('呼叫面板已打开');
				// return;
			} else if(!this.pageTwoDiaConfig.dialogVisible){
        // 弹窗尚未打开的情况下，收集信息打开弹窗
        this.gatherData();
        this.$bus.emit('changeCallProp', true);
      };
		},
    gatherData() {
      // 数据收集
        // 下面开始，字段信息会很紊乱
			let formCol = [
				{
					label: 'type',
					value: '来源类型'
				},
				{
					label: 'typeNum',
					value: '单据编号'
				},
				{
					label: 'nickName',
					value: '客户昵称'
				},
				{
					label: 'name',
					value: '收货人'
				},
				{
					label: 'payTime',
					value: '支付时间'
				},
				{
					label: 'phone',
					value: '手机号码'
				},
				{
					label: 'address',
					value: '收货地址'
				}
			];
			let formData = {
				type: '话务跟进详情',
				appointment_tracing_id: this.basicInfo.id, //业务单号
				phone:  this.basicInfo.mobile,
				// add.do 的数据
				// calledNumber
				sourceType: 'appointment_tracing',
				callingTpe: '呼出',
                self_motion_hang_up: true
			};
			this.$bus.emit('coverColAndData', formCol, formData);
        
      },
    
    // phoneCallOut(){ console.log('呼出测试', window)
    //     this.topBtns[4].disabled = true
    //     let self = this
    //     new Promise((resolve, reject) => {
    //         this.operationPermission(resolve, reject);
    //     }).then(() => {
    //         // window.application.oJVccBar.MakeCall(self.basicInfo.mobile, 0, '', '', '');
    //         self.ifCall = true
    //         self.showTime()
    //     })
    // },
    showTime() {
        this.clearMyInterval();
        this.second = 0;
        this.second = this.second + 1;
        this.talkTime = this.getTimerString(this.second);
        this.myInterval();
    },
    clearMyInterval() {
        window.clearInterval(this.callTimer);
    },
    myInterval() {
        this.callTimer = setInterval(() => {
        this.second = this.second + 1;
        this.talkTime = this.getTimerString(this.second);
        }, 1000);
    },
    // 计时器
    getTimerString(len) {
        if (len === 0) return '';
        let hour = parseInt(len / 3600);
        hour = (hour < 10 ? '0' + hour : hour);
        let minute = parseInt((len % 3600) / 60);
        minute = (minute < 10 ? '0' + minute : minute);
        let second = len % 60;
        second = (second < 10 ? '0' + second : second);
        return (hour.toString() + ':' + minute.toString() + ':' + second.toString());
    },
    // 挂断电话
    finishCall(){
        console.log('挂断电话')
        // window.application.oJVccBar.Disconnect();
        // application.oJVccBar.SetBusy();
        this.topBtns[4].disabled = false
        this.ifCall = false
        this.clearMyInterval()
        this.getRecordList()
    },
    /**选择店铺列表**/
    selectShop() {
      let disabledStatus = this.ifAdd ? false :  !(this.basicInfo.status=='FOLLOWING' && this.ifCallProxyId)
      if (disabledStatus) return
        this.$root.eventHandle.$emit('alert', {
            params: {
                callback: data => {
                    this.basicInfo.appointment_send_shop_activity_name = data.shop_activity_name;
                    this.basicInfo.appointment_send_shop_no = data.shop_code;
                    this.basicInfo.shopId = data.shop_id;
                    this.basicInfo.business_division = data.business_department
                    this.getShopDetail(data.shop_id)
                },
                appointment_activity_id:this.basicInfo.appointment_activity_id,
                clue_province_id:this.basicInfo.clue_province_id,
                clue_city_id:this.basicInfo.clue_city_id,
            },
            component: ()=>import('@components/appoint_management/components/call_follow_shop_list'),
                style: 'width:1000px;height:700px',
                title: '店铺列表',
        })
    },
    //获取详情信息
    getInfo(resolve){
      // 基本信息中来源平台、店铺地区prop没有根据
      let id = this.params.id ? this.params.id : this.basicInfo.id, self = this
      if(id){
        let {addressDetail,guideConcatTel} = this.shopDetail
        if(!!addressDetail || !!guideConcatTel){
            this.shopDetail = {
                addressDetail,
                guideConcatTel,
            }
        }
        this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getAssignAppointmentDetail',{id:id},function(response){
          if(response.body.result && response.body.content) {
            self.basicInfo = response.body.content || {};
            //当后端无预约活动发送店铺信息时，默认取预约活动店铺为预约活动发送店铺
            if (!response.body.content.appointment_send_shop_activity_name || response.body.content.appointment_send_shop_activity_name === ""){
                self.basicInfo.appointment_send_shop_activity_name = response.body.content.appointment_shop_activity_name;
                self.basicInfo.appointment_send_shop_no = response.body.content.appointment_shop_no;
                self.basicInfo.shopId = response.body.content.shopId;
            }
            self.currentRequirement = response.body.content.tel_remark
            
            resolve && resolve(response);
            self.getAreaCode(self.basicInfo.clue_province_id,"cityObj");
            self.getRecordList();
          } else {
            self.$message.error(response.body.msg || '');
          }
        });
      }
    },
    //设置附件上传参数
    setUploadDataObj(){
      this.uploadDataObj={
          parent_name: "CALL_FOLLOW",
          parent_no: `HWGJ${this.params.id}`,//主要通过该参数获取附件列表
          child_name: null,
          child_no: null,
          content: {},
      }
    },
    //查看附件
    getFileList() {
      let self = this;
      this.$root.eventHandle.$emit("alert", {
          title: "查看附件",
          style: "width:900px;height:600px;",
          component: () =>
              import("@components/appoint_management/call_follow_detail_file"),
          params: {
              uploadData:this.uploadDataObj,
              callback: () => {
                  self.$message.success("获取附件列表成功");
              },
          },
      });
    },
    closeTab () {
      let self = this
      if(!this.ifCall && this.ifCallHide) {
        this.$root.eventHandle.$emit('removeTab',this.params.tabName);
        return;
      }
      this.$root.eventHandle.$emit('openDialog',{
        okTxt: '确定',
        noTxt: '取消',
        txt: '当前正在通话，关闭页面会直接挂断',
        noCancelBtn: true,
        ok(){
          self.finishCall()
          self.$root.eventHandle.$emit('removeTab',self.params.tabName)
        },
        no(){}
      })
    }
  },
  mounted: function(){
    // console.log('wow')
    let self = this;
    this.getRecordList()

    this.getAreaCode(1,"provinceObj");
    this.setUploadDataObj()
    new Promise((resolve,reject)=>{
    this.getInfo(resolve)
    }).then((response)=>{
      if(response.body.content.callSystem == 'FEIYU'){
        self.oceanenginePhoneCall();
      }
    })
    
    this.params.__close = this.closeTab
  },
  watch: {}
}
</script>
<style module>
.row-height :global(.el-form-item__content) {
height: auto!important;
white-space: nowrap;
}
.mgb5 {
  margin-bottom: 5px;
}
</style>
<style type="text/css">
.call_dialog {
  width: 200px;
  height: 250px;
  position: absolute;
  right: 20px;
  top: 28px;
  border: 1px solid #ddd;
  background: #fff;
  z-index: 11;
}
.call_racle {
  height: 130px;
  width: 130px;
  background: #eee;
  margin: 30px auto 20px;
  border-radius: 50%;
  position: relative;
}
.call_racle .calling {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}
.call_racle span{
  display: block;
  padding: 10px 0px;
}
.call_racle span.call_span_first {
  border-bottom: 1px solid #000;
}
.call_btn_area {
  text-align: center;
  padding-top: 8px;
}
.call_btn_area .call_btn {
  padding: 4px 16px;
  background: red;
  color: #fff;
  cursor: pointer;
}
.call_icon {
  position: absolute;
  right: 20px;
  top: 18px;
}
.call_icon i {
  cursor: pointer;
}
.mgr4 {
  margin-right: 4px;
}
.show_calling {
  position: absolute;
  left: 304px;
  top: 9px;
}
.show_calling i {
  color: red;
  cursor: pointer;
}
strong {
  position: absolute;
  top: 5px;
  right: 14px;
}
</style>