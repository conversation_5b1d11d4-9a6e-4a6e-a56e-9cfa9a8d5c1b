<template>
    <div>
        <el-table :data="fileList" class="seeFileList">
            <el-table-column prop="name" label="文件名称" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div v-if="isImage(scope.row.path)">
                        <a href="#" class="showImg"
                            ><img :src="scope.row.path" alt="图片" width="60"
                        /></a>
                    </div>
                    <div v-else>
                        {{ scope.row.name }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="创建人"
                width="80"
                prop="creator_nick"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                label="创建时间"
                width="150"
                prop="create_time"
                show-overflow-tooltip
            >
                <template slot-scope="scope">{{
                    scope.row.create_time | dataFormat1
                }}</template>
            </el-table-column>
            <el-table-column
                label="文件大小"
                width="60"
                prop="size"
                show-overflow-tooltip
            >
                <template slot-scope="scope">{{
                    scope.row.size | fileSize
                }}</template>
            </el-table-column>
            <el-table-column label="文件预览" width="60">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            v-if="isImage(scope.row.path)"
                            @click="showImageList(scope.row)"
                        >
                            预览
                        </el-button>
                    </template>
                </el-table-column>
            <el-table-column
                label="文件下载"
                width="60"
                prop="path"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    <a :href="scope.row.path" target="_blank" download
                        ><el-button type="primary" size="mini"
                            >下载</el-button
                        ></a
                    >
                </template>
            </el-table-column>
        </el-table>
        <xpt-image
            :images="imageList"
            :show="ifShowImage"
            :ifUpload="false"
            @close="closeShowImage"
        >
        </xpt-image>
    </div>
</template>
<script>
import Fn from "@common/Fn.js";
export default {
    props: ["params"],
    data() {
        return {
            fileList: [],
            imageList: [],
            ifShowImage: false,
        };
    },
    mounted() {
        this.getFileList();
    },
    methods: {
        getFileList() {
            let url = "/file-iweb/api/cloud/file/list";
            let params = {
                order_no: this.params.uploadData.parent_no,
                page_no: 1,
                page_size: 10000,
            };
            this.$axios("post", url, params)
                .then((res) => {
                    if (res.result) {
                        this.fileList = res.content.list;
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .catch((err) => {
                    this.$message.error(err);
                });
        },
        isImage(str) {
            var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
            return reg.test(str);
        },
        //初始化预览列表
        initImageList(row) {
            let imglist = [];
            //过滤出只有图片的列表
            let list = this.fileList.filter((item) => this.isImage(item.path));
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = value.path;
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = value.creator_name;
                obj.isPucture = true;
                //确定要预览那张图片
                if (value.cloud_file_id === row.cloud_file_id) {
                    obj.active = true;
                } else {
                    obj.active = false;
                }
                imglist.push(obj);
            });
            this.imageList = imglist;
        },
        closeShowImage() {
            this.ifShowImage = false;
            //关闭图片预览更新图片附件列表
            this.getFileList();
        },
        showImageList(row) {
            this.initImageList(row);
            this.ifShowImage = true;
        },
    },
};
</script>
<style scope>
.seeFileList {
    position: absolute;
    bottom: 0;
    left: 10px;
    right: 10px;
    top: 0px;
    width: auto;
    min-width: auto;
    padding: 10px 0px;
}
.el-table .el-table__body-wrapper td .cell {
    height: auto;
}
.el-table__empty-block {
    width: auto !important;
}
.showImg {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #dbdbdb;
    margin: 5px 0;
    line-height:100%;
}
</style>