话务留资列表
<template>
  <div class="xpt-flex">
    <div style="margin-top: 5px;width: 100%;height: 33px;display: table;background: #ffffff">
      <div style="width: 46%;display: table-cell;">
        <div style="width: 20%;margin-left: 81%;text-align: center;font-size: 20px;cursor: pointer;line-height: 25px;border: 1px solid #3b4249;" @click="selectNoPassBtn">
          <div v-if="noPassBtn === true">
            <p style="color: #ffffff;background: #3b4249;">未跟进列表</p>
          </div>
          <div v-else>
            <p style="color: #3b4249;background: #ffffff;">未跟进列表</p>
          </div>
        </div>
      </div>
      <div style="width: 9%;display: table-cell;">
        <div style="width: 100%;margin-left: 0px;text-align: center;font-size: 20px;cursor: pointer;line-height: 25px;border: 1px solid #3b4249;" @click="selectSendingBtn">
          <div v-if="SendingBtn === true">
            <p style="background: #3b4249;color: #ffffff;">待发送列表</p>
          </div>
          <div v-else>
            <p style="color: #3b4249;background: #ffffff;">待发送列表</p>
          </div>
        </div>
      </div>
      <div style="width: 45%;display: table-cell;">
        <div style="width: 20%;margin-left: -1%;text-align: center;font-size: 20px;cursor: pointer;line-height: 25px;border: 1px solid #3b4249;" @click="selectInvalidBtn">
          <div v-if="invalidBtn === true">
            <p style="background: #3b4249;color: #ffffff;">无效列表</p>
          </div>
          <div v-else>
            <p style="color: #3b4249;background: #ffffff;">无效列表</p>
          </div>
        </div>
      </div>
    </div>
    <xpt-list
      :data="dataList"
      :btns="btns"
      :colData="cols"
      :selection="selection"
      :orderNo="true"
      :searchPage='search.page_name'
      :pageTotal='pageTotal'
      @search-click='searchClick'
      @selection-change='select'
      @page-size-change="sizeChange"
      @current-page-change="pageChange"
      ref="xptList"
    ></xpt-list>
  </div>
</template>

<script>
import page_six from '@components/call_system/six_page_callFun/page_six_callList.js'

    export default {
      name: "call_information",
      mixins: [page_six],
      data() {
        let self = this;
        return {
          SendingBtn:false,
          dataList:[],
          selection:"checkbox",
          pageTotal:0,
          pageNow:1,
          pageSize:50,
          noPassBtn:true,
          invalidBtn:false,
          ifFollow:"N",
          selectRow:[],
          search:{
            if_follow:"N",
            page_name:"crm_appointment_tracing_assign",
            where:[],
            page: {
              length: 50,
              pageNo: 1,
            }
          },
          btns:[
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.refresh();
              },
            },
            {
              type: "primary",
              txt: "二次分配",
              loading: false,
              click() {
                self.checkRoles();
              },
            },
            {
              isBtnGroup:true,
              btnGroupList:[
                {
                  type: 'success',
                  txt: ' 呼 出 ',
                  code: 'call',
                  loading: false,
                  disabled: () => false,
                  click() {
                    self.exhaleKeybord();
                  }
                },
                {
                  type: 'info',
                  txt: '呼出记录',
                  code: 'callRecord',
                  loading: false,
                  disabled: () => false,
                  click() {
                    self.turnOnCallOutList();
                  }
                }
              ]
            }
          ],
          cols:[
            {
              label: "姓名",
              prop: "name",
              redirectClick(row) {
                self.openCustAlert(row.id);
              },
            },
            {
              label: "手机号码",
              prop: "mobile",
            },
            {
              label: "预约活动店铺",
              prop: "appointment_shop_activity_name",
            },
            {
              label: "来源渠道",
              prop: "appointment_channel_name",
            },
            {
              label: "预约提交时间",
              prop: "appointment_submit_time",
              format: "dataFormat1" ,
            },
            {
              label: "话务员",
              prop: "tel_operator_name",
            },
            {
              label: "话务分配时间",
              prop: "tel_assign_time",
              format: "dataFormat1" ,
            },
          ],
        }
      },
      methods: {
        selectNoPassBtn() {
          if (!this.noPassBtn) {
            let self = this;
            this.dataList = [];
            this.selectRow = [];
            this.noPassBtn = true;
            this.invalidBtn = false;
            this.SendingBtn = false;
            this.ifFollow = "N";
            this.$refs.xptList.$refs.xptSearchEx.reset();
            this.search.where = [];
            this.search.if_follow = "N";
            this.btns = [
              {
                type: "success",
                txt: "刷新",
                loading: false,
                click() {
                  self.refresh();
                },
              },
              {
                type: "primary",
                txt: "二次分配",
                loading: false,
                click() {
                  self.checkRoles();
                },
              }
            ];
            this.cols = [
              {
                label: "姓名",
                prop: "name",
                redirectClick(row) {
                  self.openCustAlert(row.id);
                }
              },
              {
                label: "手机号码",
                prop: "mobile",
              },
              {
                label: "预约活动店铺",
                prop: "appointment_shop_activity_name",
              },
              {
                label: "来源渠道",
                prop: "appointment_channel_name",
              },
              {
                label: "预约提交时间",
                prop: "appointment_submit_time",
                format: "dataFormat1" ,
              },
              {
                label: "话务员",
                prop: "tel_operator_name",
              },
              {
                label: "话务分配时间",
                prop: "tel_assign_time",
                format: "dataFormat1" ,
              },
            ];
            this.getList();
          }
        },
        selectSendingBtn() {
          if (!this.SendingBtn) {
            let self = this;
            this.dataList = [];
            this.selectRow = [];
            this.SendingBtn = true;
            this.invalidBtn = false;
            this.noPassBtn = false;
            this.ifFollow = "Y";
            this.$refs.xptList.$refs.xptSearchEx.reset();
            this.search.where = [];
            this.search.if_follow = "Y";
            this.btns = [
              {
                type: "success",
                txt: "刷新",
                loading: false,
                click() {
                  self.refresh();
                },
              },
              {
                type: "primary",
                txt: "发送",
                loading: false,
                click() {
                  self.confirm();
                },
              }
            ];
            this.cols = [
              {
                label: "姓名",
                prop: "name",
                redirectClick(row) {
                  self.openCustAlert(row.id);
                }
              },
              {
                label: "手机号码",
                prop: "mobile",
              },
              {
                label: "预约活动店铺",
                prop: "appointment_shop_activity_name",
              },
              {
                label: "来源渠道",
                prop: "appointment_channel_name",
              },
              {
                label: "预约提交时间",
                prop: "appointment_submit_time",
                format: "dataFormat1" ,
              },
              {
                label: "话务员",
                prop: "tel_operator_name",
              },
              {
                label: "话务分配时间",
                prop: "tel_assign_time",
                format: "dataFormat1" ,
              },
              {
                label: "预约活动发送店铺",
                prop: "appointment_send_shop_activity_name",
              },
              {
                label: "话务跟进标签",
                prop: "tel_operator_tag",
                width: 86,
                format: 'auxFormat',
                formatParams: 'appointment_tel_operator_tag'
              },
            ];
            this.getList();
          }
        },
        selectInvalidBtn (){
          if (!this.invalidBtn) {
            let self = this;
            this.dataList = [];
            this.selectRow = [];
            this.invalidBtn = true;
            this.noPassBtn = false;
            this.SendingBtn = false;
            this.ifFollow = "Z";
            this.$refs.xptList.$refs.xptSearchEx.reset();
            this.search.where = [];
            this.search.if_follow = "Z";
            this.btns = [
              {
                type: "success",
                txt: "刷新",
                loading: false,
                click() {
                  self.refresh();
                },
              },
            ];
            this.cols = [
              {
                label: "姓名",
                prop: "name",
                redirectClick(row) {
                  self.openCustAlert(row.id);
                }
              },
              {
                label: "手机号码",
                prop: "mobile",
              },
              {
                label: "预约活动店铺",
                prop: "appointment_shop_activity_name",
              },
              {
                label: "来源渠道",
                prop: "appointment_channel_name",
              },
              {
                label: "预约提交时间",
                prop: "appointment_submit_time",
                format: "dataFormat1" ,
              },
              {
                label: "话务员",
                prop: "tel_operator_name",
              },
              {
                label: "话务分配时间",
                prop: "tel_assign_time",
                format: "dataFormat1" ,
              },
              {
                label: "预约活动发送店铺",
                prop: "appointment_send_shop_activity_name",
              },
              {
                label: "话务跟进标签",
                prop: "tel_operator_tag",
                width: 86,
                format: 'auxFormat',
                formatParams: 'appointment_tel_operator_tag'
              },
            ];
            this.getList();
          }
        },
        // getList(resolve){
        //   // if (!this.noPassBtn) {
        //   //   let params = {
        //   //     if_follow: this.ifFollow,
        //   //     page:{
        //   //       length:this.pageSize,
        //   //       pageNo:this.pageNow,
        //   //     }
        //   //   };
        //   //   this.getData(params);
        //   // } else {
        //   //   this.getData(this.search, resolve);
        //   // }
        //   this.getData(this.search,resolve);
        // },
        getList(resolve){
          this.btns[0].loading = true;
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getAssignAppointmentList',this.search,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.pageTotal = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        openCustAlert(id) {
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/appoint_management/call_information_edit.vue'),
            style: 'width:640px;height:440px',
            title: "编辑客户",
            params: {
              id:id,
              ifFollow:this.ifFollow,
              //刷新列表
              callback: data => {
                this.getList();
              }
            },
          });
        },
        //话务主管二次分配
        distribute(ids){
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/appoint_management/call_distribution_list.vue'),
            style: 'width:800px;height:500px',
            title: "话务员列表",
            params: {
              roleId:"100000021347009",
              customer:ids,
              //刷新分配列表
              callback: data => {
                this.getList();
              }
            }
          });
        },
        //校验角色是否为话务主管
        checkRoles(){
          let self = this,
            ids = [];
          this.selectRow.find(d=>{
            ids.push(d.id)
          });
          if(!ids.length){
            self.$message({
              message:'请选择要分配的客户！',
              type:'error'
            });
            return;
          }
          let params = {page:{length:50,pageNo:1}};
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/telAssignAccessControl?permissionCode=TEL_ASSIGN_ACCOUNT',params,res => {
            if(res.body.result) {
             this.distribute(ids);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        refresh(){
          this.selectRow = [];
          this.search.where = [];
          this.getList();
        },
        confirm(){
          let self = this,
            ids = [];
          this.selectRow.find(d=>{
            ids.push(d.id)
          });
          if(!ids.length){
            self.$message({
              message:'请选择要发送的客户！',
              type:'error'
            });
            return;
          };
          let data = {
            ids:ids,
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/telOperatorConfirm',data,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        searchClick(list,resolve){
          this.search.where = list;
          this.getList(resolve);
        },
        // 多选事件
        select(s){
          this.selectRow = s;
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          //this.pageSize = pageSize;
          this.search.page.length = pageSize;
          this.getList();
        },
        // 监听页数更改事件
        pageChange(page){
          //this.pageNow = page;
          this.search.page.pageNo = page;
          this.getList();
        },
      },
      mounted: function() {
        this.ifFollow = "N";
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
