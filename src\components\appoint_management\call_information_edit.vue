<template>
    <div class="xpt-flex">
      <el-row	class='xpt-top'	:gutter='40'>
        <el-col :span='20'>
          <el-button type='primary' size='mini' @click="saveCustomer">保存</el-button>
        </el-col>
      </el-row>
      <div style="height: 120px">
        <div>
          <p style="line-height: 30px;font-weight: 600;color: #696969;font-size: 40px;">客户信息</p>
        </div>
        <el-form label-position="right" label-width="120px" style="background: #e0e0e0">
          <el-col :span="6" style="width: 50%">
            <el-form-item label="姓名：" required>
              <el-input v-model="customer" size='mini' style="width: 145px;" :maxlength='20'></el-input>
            </el-form-item>
            <el-form-item label="手机号码：">
              <el-input v-model="dataList.mobile" size='mini' style="width: 145px;" disabled></el-input>
            </el-form-item>
            <el-form-item label="预约活动店铺：">
              <el-input v-model="dataList.appointment_shop_activity_name" size='mini' style="width: 145px;" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="width: 50%">
            <el-form-item label="来源渠道：">
              <el-input v-model="dataList.appointment_channel_name" size='mini' style="width: 145px;" disabled></el-input>
            </el-form-item>
            <el-form-item label="预约提交时间：">
              <el-input v-model="submitTime" size='mini' style="width: 145px;" disabled></el-input>
            </el-form-item>
            <el-form-item label="话务分配时间：">
              <el-input v-model="assignTime" size='mini' style="width: 145px;" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <div>
        <div>
          <p style="line-height: 35px;font-weight: 600;color: #696969;font-size: 40px;">话务跟进记录</p>
        </div>
        <el-form label-position="right" label-width="120px" style="height: 32px;">
          <el-col :span="6" style="width: 50%">
            <el-form-item label="预约活动发送店铺：">
              <el-input v-model="shopData.shop_send_activity_name" size='mini' icon="search" :on-icon-click="selectShop" style="width: 145px;" :maxlength='50'></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="width: 50%">
            <el-form-item label="意向标签：" required>
              <el-select v-model="tag" filterable placeholder="请选择" size="mini" class="mgr5 v-middle" style="width: 145px;" @change="tagChanged(false)">
                <el-option label="有意向" value="TEL_HAVE_INTENTION"></el-option>
                <el-option label="未接电话" value="TEL_NO_INTENTION"></el-option>
                <el-option label="无效" value="TEL_INVALID"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
        <el-form label-position="right" label-width="120px">
          <el-col :span="6" style="width: 100%">
            <el-form-item label="客户需求：" style="height: 55px;" required>
              <el-input v-model="requirement" type='textarea' size='mini' style="width:92%" :maxlength='50' placeholder="50字以内"></el-input>
            </el-form-item>
            <el-form-item label="预计到店：" style="height: 55px;">
              <el-input v-model="arrival" type='textarea' size='mini' style="width:92%" :maxlength='10' placeholder="10字以内"></el-input>
            </el-form-item>
          </el-col>
        </el-form>
        <el-form label-position="right" label-width="120px">
          <el-col :span="6" style="width: 100%">
            <el-form-item label="立即下推到店长：">
              <el-switch v-model="ifPushOwner" on-text="是" off-text="否" off-value="N" on-value="Y" :disabled="ifPush"></el-switch>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
    </div>
</template>

<script>
  import Fn from '@common/Fn.js'
    export default {
      name: "call_information_edit",
      props:["params"],
      data(){
        let self = this;
        return {
          required:true,
          tag:"",
          customer:"",
          requirement:"",
          arrival:"",
          submitTime:"",
          assignTime:"",
          dataList:{},
          ifPushOwner:"",//是否立即下推到店长
          ifPush:false,
          ifData:false,
          shopData:{
            shop_code:"",
            shop_send_activity_name:"",
            shop_send_name:"",
            shop_owner_id:"",
            shop_owner_name:"",
          }
        }
      },
      methods: {
        getList(){
          let data = {
            id: this.params.id
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getAssignAppointmentDetail',data,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content;
              this.customer = res.body.content.name;
              this.ifPushOwner = res.body.content.if_urgent;
              this.requirement = res.body.content.tel_operator_mark_requirement;
              this.arrival = res.body.content.tel_operator_mark_arrival;
              //当后端无预约活动发送店铺信息时，默认取预约活动店铺为预约活动发送店铺
              if (!res.body.content.appointment_send_shop_activity_name || res.body.content.appointment_send_shop_activity_name === ""){
                this.shopData.shop_send_activity_name = res.body.content.appointment_shop_activity_name;
              } else {
                this.shopData.shop_send_activity_name = res.body.content.appointment_send_shop_activity_name;
                this.shopData.shop_code = res.body.content.appointment_send_shop_no;
                this.shopData.shop_send_name = res.body.content.appointment_send_shop_name;
                this.shopData.shop_owner_id = res.body.content.shop_owner_id;
                this.shopData.shop_owner_name = res.body.content.shop_owner_name;
              }
              this.setTag(res.body.content.tel_operator_tag);
              this.submitTime = Fn.dateFormat(res.body.content.appointment_submit_time,'yyyy-MM-dd hh:mm:ss');
              this.assignTime = Fn.dateFormat(res.body.content.tel_assign_time,'yyyy-MM-dd hh:mm:ss');
              if (this.params.ifFollow === "N") {
                this.ifData = false;
              } else {
                this.ifData = true;
              }
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        /**选择店铺列表**/
        selectShop() {
          this.$root.eventHandle.$emit('alert', {
            params: {
              callback: data => {
                this.shopData.shop_code = data.shop_code;
                this.shopData.shop_send_name = data.shop_name;
                this.shopData.shop_send_activity_name = data.shop_activity_name;
                this.shopData.shop_owner_name = data.shop_owner_name;
                this.shopData.shop_owner_id = data.shop_owner_id;
                this.dataList.appointment_send_shop_activity_name = data.shop_activity_name;
              },
            },
            component: ()=>import('@components/appoint_management/appoint_shop_list'),
            style: 'width:800px;height:500px',
            title: '店铺列表',
          })
        },
        setTag(tag){
          if (tag === "TEL_HAVE_INTENTION") {
            this.tag = "有意向";
          } else if (tag === "TEL_NO_INTENTION") {
            this.tag = "未接电话";
          } else if (tag === "TEL_INVALID")  {
            this.tag = "无效";
          }
        },
        saveCustomer(){
          if (!this.customer || this.customer.length === 0 ){
            this.$message.error("客户姓名不能为空！");
            return;
          }
          if (!this.tag || this.tag.length === 0 ){
            this.$message.error("意向标签不能为空！");
            return;
          }
          if (this.tag === "有意向" || this.tag === "未接电话"){
            if (!this.shopData.shop_send_activity_name || this.shopData.shop_send_activity_name.length === 0) {
              this.$message.error("意向标签为有意向或未接电话时预约活动发送店铺不能为空！");
              return;
            }
          }
          if (!this.requirement || this.requirement.length === 0){
            this.$message.error("请填写客户需求！");
            return;
          }
          console.log(":::%s%s",this.requirement,this.tag);
          let tags = "";
          if (this.tag === "有意向") {
            tags = "TEL_HAVE_INTENTION";
          } else if (this.tag === "未接电话") {
            tags = "TEL_NO_INTENTION";
          } else if (this.tag === "无效")  {
            tags = "TEL_INVALID";
          } else {
            tags = this.tag;
          }
          if (this.shopData.shop_code === "") {
            //当直接点击保存且后端无预约活动发送店铺信息时，直接取预约店铺为预约发送店铺
            console.log("shop_code为空");
            this.shopData.shop_code = this.dataList.appointment_shop_no;
            this.shopData.shop_send_name = this.dataList.appointment_shop_name;
          }
          console.log(this.shopData.shop_code,this.shopData.shop_send_name);
          // if (this.shopData.shop_owner_id === "") {
          //   //如果预约活动发送店铺和预约活动店铺相同则调用接口获取shop_owner_id和shop_owner_name
          //   console.log("shop_owner_id为空");
          //   this.getShopList(tags);
          // } else {
          //   this.save(tags);
          // }
          //三个列表在点击保存时都校验预约活动发送店铺信息
          this.getShopList(tags);
        },
        getShopList(tag) {
          let params = {
            shop_code: this.shopData.shop_code,
            page:{
              length:50,
              pageNo:1,
            }
          };
          console.log("shop_activity_name:%s",this.dataList.appointment_shop_activity_name);
          this.ajax.postStream('/material-web/api/shopv2/getShopInfoByShopExpandDisplay', params, res=> {
            if(res.body.result){
              if (res.body.content.list.length === 0) {
                this.$message.error("获取预约活动发送店铺信息失败，请先确认该店铺拓展信息是否为展示！");
              } else {
                this.shopData.shop_owner_id = res.body.content.list[0].customer_source_id;
                this.shopData.shop_owner_name = res.body.content.list[0].customer_source_name;
                console.log("owner_id:%s",this.shopData.shop_owner_id);
                this.save(tag);
              }
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        save(tag) {
          let data = {
            id:this.params.id,
            name:this.customer,
            appointment_send_shop_no:this.shopData.shop_code,
            appointment_send_shop_name:this.shopData.shop_send_name,
            appointment_send_shop_activity_name:this.shopData.shop_send_activity_name,
            shop_owner_id:this.shopData.shop_owner_id,
            shop_owner_name:this.shopData.shop_owner_name,
            tel_operator_tag:tag,
            tel_operator_mark_requirement: this.requirement,
            tel_operator_mark_arrival: this.arrival,
            if_urgent: tag === "TEL_INVALID" ? "" : this.ifPushOwner,
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/updateAssignAppointmentDetail',data,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              //关闭弹窗
              this.params.callback(this.shopData);
              this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        tagChanged(){
          console.log(123123123,this.ifData);
          //标签取无效是立即下推店长按钮取否并置灰
          if (this.ifData) {
            //如果是保存过的数据，首次打开时取后端的值
            this.requirement = this.dataList.tel_operator_mark_requirement;
            if (this.tag === "无效" || this.tag === "TEL_INVALID") {
              this.ifPushOwner = "N";
              this.ifPush = true;
            } else {
              this.ifPush = false;
            }
            this.ifData = false;
          } else {
            if (this.tag === "未接电话" || this.tag === "TEL_NO_INTENTION") {
              this.requirement = "门店二次跟进";
              this.ifPush = false;
            } else {
              this.requirement = "";
              if (this.tag === "无效" || this.tag === "TEL_INVALID") {
                this.ifPushOwner = "N";
                this.ifPush = true;
              } else {
                this.ifPush = false;
              }
            }
          }
        }
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
