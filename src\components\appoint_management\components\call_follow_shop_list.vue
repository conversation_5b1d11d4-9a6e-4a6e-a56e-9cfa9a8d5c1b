<template>
  <div class="xpt-flex">
    <div class="btns-box">
      <el-button type="success" size="mini" @click="fresh">刷新</el-button>
      <el-button type="primary" size="mini" @click="confirm">确认</el-button>
    </div>
    <el-tabs v-model="shopTab" @tab-click="handleTabsClick" class="xpt-flex">
      <el-tab-pane label="活动店铺" name="activity_shop" class="xpt-flex">
        <callFollowShopListContent
          :params="params"
          :shop_range="1"
          ref="activity_shop_ref"
          @firstEvent="fresh"
        />
      </el-tab-pane>
      <el-tab-pane label="其他店铺" name="other_shop" class="xpt-flex">
        <callFollowShopListContent
          :params="params"
          :shop_range="2"
          ref="other_shop_ref"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
  
  <script>
import callFollowShopListContent from "./call_follow_shop_list_content.vue";
export default {
  name: "call_follow_shop_list",
  props: ["params"],
  components: { callFollowShopListContent },
  data() {
    return {
      shopTab: "activity_shop",
    };
  },
  methods: {
    fresh() {
      this.$refs[`${this.shopTab}_ref`].getList();
    },
    handleTabsClick() {
      this.fresh();
    },
    confirm() {
      //确认选择
      let selectRow = this.$refs[`${this.shopTab}_ref`].selectRow;
      console.log("selectRow:%s", selectRow.shop_code);
      if (!selectRow.shop_code) {
        this.$message({
          message: "请选择店铺！",
          type: "error",
        });
        return;
      }
      //关闭弹窗
      this.params.callback(selectRow);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
  },
  mounted: function () {},
};
</script>

<style scoped>
.btns-box {
  line-height: 40px;
}
</style>
  
