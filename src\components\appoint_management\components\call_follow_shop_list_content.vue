<template>
  <div class="xpt-flex">
    <div class="search-box">
      <el-select
        v-model="shop.province_id"
        size="mini"
        style="width: 140px"
        class="mr10"
        placeholder="选择省"
        @change="changeAddress(shop.province_id, 1)"
      >
        <el-option
          v-for="(value, key) in province"
          :label="value"
          :value="parseInt(key)"
          :key="key"
        ></el-option>
      </el-select>
      <el-select
        v-model="shop.clue_city_id"
        size="mini"
        style="width: 140px"
        class="mr10"
        placeholder="选择市"
        @change="changeAddress(shop.clue_city_id, 2)"
      >
        <el-option
          v-for="(value, key) in clue_city"
          :label="value"
          :value="parseInt(key)"
          :key="key"
        ></el-option>
      </el-select>
      <el-select
        v-model="shop.district_id"
        size="mini"
        style="width: 140px"
        class="mr10"
        placeholder="选择区"
      >
        <el-option
          v-for="(value, key) in district"
          :label="value"
          :value="parseInt(key)"
          :key="key"
        ></el-option>
      </el-select>

      <xpt-search-ex
        class="call-follow-shop-list-data-search mr10"
        :page="page_name"
        ref="activityShopXptSearchEx"
        :click="searchClick"
      ></xpt-search-ex>

      <el-button type="primary" size="mini" @click="searchClicks"
        >搜索</el-button
      >
      <el-button type="primary" size="mini" @click="searchReset"
        >重置</el-button
      >
    </div>
    <xpt-list
      :data="dataList"
      :colData="cols"
      :btns="btns"
      :showHead="false"
      :pageTotal="pageTotal"
      :selection="selection"
      @search-click="searchClick"
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
      @radio-change="select"
    >
    </xpt-list>
  </div>
</template>
    
    <script>
export default {
  name: "call_follow_shop_list_content",
  props: ["params", "shop_range"],
  data() {
    let self = this;
    return {
      shopTab: "activity_shop",
      btns: [],

      province: {},
      clue_city: {},
      district: {},

      shop: {
        province_id: "",
        clue_city_id: "",
        district_id: "",
      },

      dataList: [],
      selection: "radio",
      pageTotal: 0,
      pageSize: 50,
      pageNo: 1,
      page_name: "crm_shop_setting",
      where: [],

      selectRow: {},

      cols: [
        {
          label: "店铺编码",
          prop: "shop_code",
        },
        {
          label: "活动店铺名称",
          prop: "shop_activity_name",
        },
        {
          label: "事业部",
          format: "auxFormat",
          formatParams: "business_division",
          prop: "business_department",
        },
        {
          label: "店铺主营类目",
          format: "auxFormat",
          formatParams: "main_sale_categories",
          prop: "main_sale_categorie",
        },
        {
          label: "营业时间",
          prop: "open_hour",
        },
        {
          label: "省",
          prop: "shop_province_name",
        },
        {
          label: "市",
          prop: "shop_city_name",
        },

        {
          label: "区",
          prop: "shop_district_name",
        },
        {
          label: "门店地址",
          prop: "address_detail",
        },
      ],

      search: {
        key: "",
      },

      shopMountedCount: -1,
      shopMountedState: true,
    };
  },
  methods: {
    changeAddress(code, type) {
      var key = type == 1 ? "clue_city" : "district";
      if (!code) return;
      this.getAddress(code, (data) => {
        this[key] = "";
        this[key] = data || {};
        if (this.shopMountedCount > 0) {
          let keys = ["clue_city_id"];
          this.shop[keys[type - 1]] = this.params[keys[type - 1]];
          this.shopMountedCount--;
          return;
        }
        if (this.shopMountedState) {
          this.shopMountedState = false;
          this.$emit("firstEvent");
        }
        if (!data || !data.hasOwnProperty(this.shop[key])) {
          if (type == 1) {
            this.shop.clue_city_id = null;
            this.shop.district_id = null;
          } else if (type == 2) {
            this.shop.district_id = null;
          }
        }
      });
    },
    searchClicks() {
      this.$refs.activityShopXptSearchEx.search();
    },
    searchReset() {
      this.shop = { province_id: "", clue_city_id: "", district_id: "" };
      this.$refs.activityShopXptSearchEx.reset();
    },

    getList(resolve) {
      let params = {
        shop_activity_name: this.search.key ? this.search.key : "",
        appointment_activity_id: this.params.appointment_activity_id,
        ...this.shop,
        shop_range: this.shop_range,
        page: {
          length: this.pageSize,
          pageNo: this.pageNo,
        },
        page_name: this.page_name,
        where: this.where,
      };
      this.ajax.postStream(
        "/crm-web/api/crm_appointment_activity/appointmentShopList",
        params,
        (res) => {
          if (res.body.result) {
            this.dataList = res.body.content.list || [];
            this.pageTotal = res.body.content.count;
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
        }
      );
    },

    // 选择事件
    select(res) {
      this.selectRow = res;
    },

    // 监听每页显示数更改事件
    pageChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    // 监听页数更改事件
    currentPageChange(page) {
      this.pageNo = page;
      this.getList();
    },
    searchClick(obj, resolve) {
      if (!obj.length || !obj[0].value) {
        this.where = [];
      } else {
        this.where = obj;
      }
      this.getList(resolve);
    },
  },
  mounted: function () {
    let shop = {
      province_id: this.params.clue_province_id,
      clue_city_id: this.params.clue_city_id,
    };
    for (const key in shop) {
      if (!!shop[key]) {
        this.shopMountedCount++;
      }
    }
    this.getAddress((data) => {
      this.province = data;
      this.shop.province_id = this.params.clue_province_id;
    });
  },
};
</script>
  
  <style scoped>
.search-box {
  display: flex;
  height: 50px;
  align-items: center;
}
.mr10 {
  margin-right: 10px;
}
</style>
    
  <style>
.call-follow-shop-list-data-search .el-button,
.call-follow-shop-list-data-search .el-icon-menu {
  display: none;
}
.call-follow-shop-list-data-search {
  width: auto;
  float: none;
}
.call-follow-shop-list-data-search .where-show-panel li {
  display: flex;
  align-items: center;
}
.call-follow-shop-list-data-search .plan-show-panel,
.call-follow-shop-list-data-search .save {
  display: none !important;
}

.call-follow-shop-list-data-search .where-show-panel {
  width: auto;
}
</style>