<template>
  <div class="container xpt-flex">
    <customerBehaviorPerformanceForm
      :isAppointment="isAppointment"
      @getList="getList"
      ref="customerBehaviorPerformanceFormRef"
    />
    <xpt-list
      class="table-list-box"
      :data="list"
      :colData="cols"
      selection=""
      :showHead="false"
      :pageTotal="count"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    >
    </xpt-list>
  </div>
</template>

<script>
import customerBehaviorPerformanceForm from "./customer_behavior_performance_form";
import Fn from "@common/Fn.js";
export default {
  name: "customerBehaviorPerformanceContent",
  components: { customerBehaviorPerformanceForm },
  props: ["params", "id","isReservation"],
  data() {
    var self = this;
    return {
      isAppointment: false,

      list: [],
      cols: [
        {
          label: "销售单号",
          prop: "sys_trade_no",
          width: 200,
          redirectClick(row) {
            let params = {};
            params.sys_trade_id = row.sys_trade_id;
            self.$root.eventHandle.$emit("creatTab", {
              name: "销售订单详情",
              params: params,
              component: () => import("@components/order/detail.vue"),
            });
          },
        },
        {
          label: "合并单号",
          prop: "merge_trade_no",
          redirectClick(row) {
            self.viewMergedOrder(row);
          },
          width: 200,
        },
        {
          label: "拍单时间",
          prop: "created",
          width: 130,
          format: "dataFormat1",
        },
        {
          label: "支付时间",
          prop: "pay_time",
          width: 130,
          format: "dataFormat1",
        },
        {
          label: "买家昵称",
          prop: "customer_name",
        },
        {
          label: "订单店铺",
          prop: "shop_name",
        },
        {
          label: "原始店铺",
          prop: "original_shop_name",
        },
        {
          label: "原始店铺事业部",
          width: 120,
          format: "auxFormat",
          formatParams: "business_division",
          prop: "original_shop_business_division",
        },
        {
          label: "单据状态",
          prop: "audit_status",
          formatter(val) {
            switch (val) {
              case "Y":
                return "已审核";
                break;
              case "N":
                return "未审核";
                break;
              default:
                return val;
                break;
            }
          },
        },
        {
          label: "合并单总支付金额",
          prop: "merge_amount",
          width: 140,
        },
        {
          label: "销售单总支付金额",
          prop: "amount",
          width: 140,
        },
        {
          label: "订单类型",
          prop: "if_customization",
          formatter(val) {
            switch (val) {
              case "Y":
                return "定制单";
                break;
              case "N":
                return "成品单";
                break;
              default:
                return val;
                break;
            }
          },
        },
        {
          label: "定制单：经销采购价",
          prop: "dealer_price",
          width: 140,
          formatter(val) {
            return val ? val : "--";
          },
        },
        {
          label: "定制单：结算时间",
          prop: "scm_time",
          width: 140,
          formatter(val) {
            return val ? Fn.dateFormat(val, "yyyy-MM-dd hh:mm:ss") : "--";
          },
        },
        {
          label: "定制单号",
          prop: "client_number",
          formatter(val) {
            return val ? val : "--";
          },
          width: 200,
        },
      ],
      count: 0,
      search: {
        page_name: "",
        where: [],
        page_size: 50,
        page_no: 1,
      },
    };
  },
  methods: {
    viewMergedOrder(row) {
      let list = this.list.map((item) => {
        return { merge_trade_id: item.merge_trade_id };
      });
      let params = {
        merge_trade_id: row.merge_trade_id,
        mergeTradeIdList: list,
      };
      this.$root.eventHandle.$emit("creatTab", {
        name: "合并订单详情",
        params,
        component: () => import("@components/order/merge.vue"),
      });
    },
    getList(e) {
      let data = {
        ...e,
        ...this.search,
        appointment_tracing_id: this.id,
      };
      let url = "/crm-web/api/crm_customer_action/order";
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { result, content, msg } = res.body;
          if (result && content) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },
    pageSizeChange(res) {
      this.search.page_size = res;
      this.getList();
    },
    pageChange(res) {
      this.search.page_no = res;
      this.getList();
    },
  },
  mounted() {
    if(!this.isReservation){
      this.cols = [
        {
          label: "销售单号",
          prop: "sys_trade_no",
          width: 200,
          redirectClick(row) {
            let params = {};
            params.sys_trade_id = row.sys_trade_id;
            self.$root.eventHandle.$emit("creatTab", {
              name: "销售订单详情",
              params: params,
              component: () => import("@components/order/detail.vue"),
            });
          },
        },
        {
          label: "合并单号",
          prop: "merge_trade_no",
          redirectClick(row) {
            self.viewMergedOrder(row);
          },
          width: 200,
        },
        {
          label: "拍单时间",
          prop: "created",
          width: 130,
          format: "dataFormat1",
        },
        {
          label: "支付时间",
          prop: "pay_time",
          width: 130,
          format: "dataFormat1",
        },
        {
          label: "买家昵称",
          prop: "customer_name",
        },
        {
          label: "订单店铺",
          prop: "shop_name",
        },
        {
          label: "原始店铺",
          prop: "original_shop_name",
        },
        {
          label: "原始店铺事业部",
          width: 120,
          format: "auxFormat",
          formatParams: "business_division",
          prop: "original_shop_business_division",
        },
        {
          label: "单据状态",
          prop: "audit_status",
          formatter(val) {
            switch (val) {
              case "Y":
                return "已审核";
                break;
              case "N":
                return "未审核";
                break;
              default:
                return val;
                break;
            }
          },
        },
        {
          label: "合并单总支付金额",
          prop: "merge_amount",
          width: 140,
        },
        {
          label: "销售单总支付金额",
          prop: "amount",
          width: 140,
        },
        {
          label: "订单类型",
          prop: "if_customization",
          formatter(val) {
            switch (val) {
              case "Y":
                return "定制单";
                break;
              case "N":
                return "成品单";
                break;
              default:
                return val;
                break;
            }
          },
        },
        
        {
          label: "定制单：结算时间",
          prop: "scm_time",
          width: 140,
          formatter(val) {
            return val ? Fn.dateFormat(val, "yyyy-MM-dd hh:mm:ss") : "--";
          },
        },
        {
          label: "定制单号",
          prop: "client_number",
          formatter(val) {
            return val ? val : "--";
          },
          width: 200,
        },
      ]
    }
    
  },
};
</script>
<style scoped>
.table-list-box {
  height: 640px !important;
}
</style>
