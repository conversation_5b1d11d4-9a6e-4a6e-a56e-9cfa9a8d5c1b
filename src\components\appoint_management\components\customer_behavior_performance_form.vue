<template>
  <el-form ref="form" :model="form" class="flex form-box">
    <el-form-item
      label=""
      class="form-item"
      label-width=""
      v-if="!isAppointment"
    >
      <el-select
        placeholder="选择订单类型"
        size="mini"
        v-model="form.order_type"
      >
        <el-option
          v-for="item in orderTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="时间段 " class="flex form-item">
      <el-radio-group v-model="isTimePeriod" @change="handleTimePeriodChange">
        <el-radio
          v-for="item in timePeriodGroup"
          :key="item.value"
          :label="item.value"
          >{{ item.label }}</el-radio
        >
      </el-radio-group>
    </el-form-item>
    <el-form-item
      label="预约生成时间 "
      class="flex form-item"
      v-if="isAppointment"
    >
      <el-date-picker
        v-model="form.begin_date"
        type="datetime"
        placeholder="开始时间"
        size="mini"
        :picker-options="enableDateOptions"
        :disabled="isTimePeriod !== 2"
      ></el-date-picker>
      <el-date-picker
        v-model="form.end_date"
        type="datetime"
        placeholder="结束时间"
        size="mini"
        :picker-options="disableDateOptions"
        :disabled="isTimePeriod !== 2"
      ></el-date-picker>
    </el-form-item>
    <el-form-item label="" class="flex form-item" v-else>
      <el-select
        placeholder="拍单/支付时间"
        size="mini"
        v-model="isPayOrOrder"
        :disabled="isTimePeriod !== 2"
      >
        <el-option
          v-for="item in payOrOrderTime"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-date-picker
        v-model="form.begin_date"
        type="datetime"
        placeholder="开始时间"
        size="mini"
        :picker-options="enableDateOptions"
        :disabled="!isPayOrOrder && !isAppointment"
      ></el-date-picker>
      <el-date-picker
        v-model="form.end_date"
        type="datetime"
        placeholder="结束时间"
        size="mini"
        :picker-options="disableDateOptions"
        :disabled="!isPayOrOrder && !isAppointment"
      ></el-date-picker>
    </el-form-item>

    <el-form-item v-if="isReception" class="flex form-item">
      <el-input
        v-model="form.shopName"
        size="mini"
        placeholder="接待店铺名称"
        style="margin-right: 10px"
      ></el-input>
      <el-input
        v-model="form.shopCode"
        size="mini"
        placeholder="接待店铺编号"
      ></el-input>
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        size="mini"
        @click="handleSearch"
        class="search-button"
        >搜索</el-button
      >
      <el-button type="primary" size="mini" @click="searchReset"
        >重置</el-button
      >
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  props: ["params"],
  props: {
    isAppointment: {
      type: Boolean,
      default: true,
    },
    isReception: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    var self = this;
    return {
      form: {
        order_type: "",
        time_type: "",
        begin_date: "",
        end_date: "",
        shopName: "",
        shopCode: "",
      },
      orderTypeOptions: [
        {
          value: "",
          label: "全部订单类型",
        },
        {
          value: "CP",
          label: "成品订单",
        },
        {
          value: "DZ",
          label: "定制订单",
        },
      ],

      isTimePeriod: "",

      timePeriodGroup: [
        {
          label: "当前线索预约提交时间后",
          value: 1,
        },
        {
          label: "任意时间段",
          value: 2,
        },
      ],

      isPayOrOrder: "",

      payOrOrderTime: [
        {
          label: "拍单时间",
          value: 2,
        },
        {
          label: "支付时间",
          value: 3,
        },
      ],

      enableDateOptions: {
        /*生效时间允许选所有值*/
        disabledDate(time) {
          return self.form.end_date
            ? time.getTime() > new Date(self.form.end_date)
            : false;
        },
        date: (function () {
          let date = new Date();
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let time = year + "-" + month + "-" + day + " " + "00:00:00";
          return new Date(time);
        })(),
      },
      disableDateOptions: {
        disabledDate: (time) => {
          /*新增时能选所有时间*/
          return self.form.begin_date
            ? time.getTime() < new Date(self.form.begin_date)
            : false;
        },
        date: (function () {
          let date = new Date();
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let time = year + "-" + month + "-" + day + " " + "23:59:59";
          return new Date(time);
        })(),
      },
    };
  },
  methods: {
    searchReset() {
      this.form = {
        order_type: "",
        time_type: "",
        begin_date: "",
        end_date: "",
        shopName: "",
        shopCode: "",
      };
      this.isTimePeriod = "";
      this.isPayOrOrder = "";
    },
    handleSearch() {
      if (this.isTimePeriod == 2) {
        let { begin_date, end_date } = this.form;
        if (!begin_date || !end_date) {
          this.$message.error("开始时间和结束时间不能为空");
          return;
        }
      }
      let data = {
        ...this.form,
        time_type: this.getTimeType(),
      };
      if (this.isAppointment) {
        delete data.order_type;
      }
      if (!this.isReception) {
        delete data.shopName;
        delete data.shopCode;
      }
      console.log(data, "data=====");
      this.$emit("getList", data);
    },
    handleTimePeriodChange() {
      this.isPayOrOrder = "";
      this.form.begin_date = "";
      this.form.end_date = "";
    },
    getTimeType() {
      if (!this.isTimePeriod) return "";
      if (this.isTimePeriod == 1) return 1;
      if (this.isAppointment) return 4;
      return this.isPayOrOrder;
    },
  },
};
</script>

<style scoped>
.container {
  height: 100%;
}
.flex {
  display: flex;
}
.form-box {
  margin: 15px 0;
}
.form-item {
  margin-right: 20px;
}
.search-button {
  width: 90px;
}
</style>