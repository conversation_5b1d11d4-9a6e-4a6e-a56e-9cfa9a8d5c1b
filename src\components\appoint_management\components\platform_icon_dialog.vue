<template>
  <div class="form-dialog-container" v-if="visible">
    <el-dialog
      :title="formParams.title"
      :visible.sync="visible"
      class="platform-icon-dialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :center="true"
    >
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="留资平台" class="el-form-item">
          <el-input
            v-model="form.name"
            placeholder="输入平台名称"
            size="mini"
            class="input-width"
          ></el-input>
        </el-form-item>
        <el-form-item label="图标" class="el-form-item">
          <div class="upload-icon-container">
            <div
              class="img-container"
              :style="`background-image:url(${form.remark})`"
            ></div>
            <xpt-upload-v3
              ref="upload3Ref"
              uploadBtnText="选择图标"
              acceptType="img"
              btnSize="mini"
              :dataObj="uploadData"
              :limit="1"
              :ifMultiple="false"
              @uploadSuccess="uploadSuccess"
              class="xpt-upload-v3"
            ></xpt-upload-v3>
            <span style="color: #999">正方形图标，小于1M</span>
          </div>
        </el-form-item>
        <el-form-item label="是否启用" class="el-form-item">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="(item, i) in statusOptions"
              :key="i"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSuccess">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formParams: {},
      visible: false,
      statusOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      uploadData: {
        parent_name: null,
        parent_no: null,
        child_name: null,
        child_no: null,
        content: null,
      },
      form: {},
    };
  },
  methods: {
    open(formParams) {
      this.formParams = formParams;
      this.form = JSON.parse(JSON.stringify(formParams.form));
      this.visible = true;
    },
    handleClose() {
      this.visible = false;
    },
    handleSuccess() {
      let { code, name, remark, status } = this.form;
      if (!name || !remark) {
        this.$message.warning("数据不能为空");
        return;
      }
      if (
        this.formParams.list.some((item) => item.name === name) &&
        this.formParams.title === "新增"
      ) {
        this.$message.error("该平台已存在，不能重复新增");
        return;
      } else if (
        this.formParams.list.some((item) => item.name === name) &&
        name !== this.formParams.name
      ) {
        this.$message.error("该平台已存在，不能重复新增");
        return;
      }
      this.formParams.form.code = code;
      this.formParams.form.name = name;
      this.formParams.form.remark = remark;
      this.formParams.form.status = status;
      this.formParams.ok(this.formParams.form);
    },
    uploadSuccess(uploadFileList) {
      let path = uploadFileList[0].path;
      this.form.remark = path;
    },
  },
};
</script>

<style scoped>
.platform-icon-dialog {
  z-index: 10 !important;
}

.platform-icon-dialog .dialog-footer {
  display: flex;
}

.platform-icon-dialog .el-button {
  flex: 1;
}

.platform-icon-dialog .el-dialog--small {
  width: 500px !important;
}

.platform-icon-dialog .el-form-item {
  margin: 5px 0;
}

.platform-icon-dialog .input-width {
  width: 100%;
}

.upload-icon-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.img-container {
  width: 30px;
  height: 30px;
  background-size: 100% 100%;
  background-color: rgb(241, 241, 241);
}
</style>
<style>
.platform-icon-dialog + .v-modal {
  z-index: 9 !important;
}
.platform-icon-dialog .xpt-upload-v3 .el-button--mini {
  padding: 4px 7px !important;
}
.platform-icon-dialog .xpt-upload-v3 .el-button--mini span {
  color: #fff;
}
</style>
