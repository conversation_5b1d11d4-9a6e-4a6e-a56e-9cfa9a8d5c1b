<template>
  <div class="xpt-flex">
    <xpt-list
      ref="platform_icon_list"
      :data="list"
      :btns="btns"
      :colData="cols"
      selection=""
      :pageTotal="count"
      :pageLength="page.length"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
      class="platform-icon-list-container"
    >
      <template slot="remark" slot-scope="scope">
        <div
          class="image-box"
          :style="`background-image:url(${scope.row.remark})`"
        ></div>
      </template>
      <template slot="operate" slot-scope="scope">
        <el-button @click="handleEdit(scope.row)" type="text" size="small">
          编辑
        </el-button>
        <el-button @click="handleDel(scope.row)" type="text" size="small">
          删除
        </el-button>
      </template>
    </xpt-list>
    <platform-icon-dialog ref="platformIconDialogRef"></platform-icon-dialog>
  </div>
</template>

<script>
import PlatformIconDialog from "@components/appoint_management/components/platform_icon_dialog.vue";
export default {
  components: {
    PlatformIconDialog,
  },
  data() {
    let self = this;
    return {
      visible: false,
      list: [],
      btns: [
        {
          type: "primary",
          txt: "新增",
          disabled: () => {
            return false;
          },
          click() {
            self.handleAdd();
          },
        },
      ],
      cols: [
        {
          label: "留资平台",
          prop: "name",
          align: "center",
        },
        {
          label: "图标",
          slot: "remark",
        },
        {
          label: "是否启用",
          prop: "status",
          formatter(val) {
            return val == "1" ? "是" : "否";
          },
          align: "center",
        },
        {
          label: "操作",
          slot: "operate",
          align: "center",
        },
      ],
      page: {
        length: 10,
        pageNo: 1,
      },
      count: 0,
      categoryPo: {},
    };
  },
  methods: {
    getList(resolve) {
      let data = {
        categoryCode: "CLUE_WEBSITE_ICON",
        page: this.page,
      };
      let url = "/user-web/api/auxiliary/getAuxiliaryDataList";
      this.ajax.postStream(
        url,
        data,
        (d) => {
          if (d.body.result && d.body.content) {
            let { list, count } = d.body.content;
            this.list = list || [];
            this.count = count;
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve();
        }
      );
    },
    // 当前页改变
    pageSizeChange(ps) {
      this.page.length = ps;
      this.getList();
    },
    // 当前页面显示行数改变
    pageChange(page) {
      this.page.pageNo = page;
      this.getList();
    },
    handleAdd() {
      let formParams = {
        title: "新增",
        form: {
          categoryCode: "CLUE_WEBSITE_ICON",
          code: Date.now(),
          disableTime: null,
          enableTime: null,
          id: "",
          name: "",
          parentCode: "",
          platform: "NEW_SALE_PLATFORM",
          status: 1,
          remark: "",
          ext_field1: "",
          ext_field2: "",
          parentCategoryCode: "",
        },
        list: this.list,
        ok: (form) => {
          this.$refs.platformIconDialogRef.handleClose();
          let dataList = this.list;
          dataList.push(form);
          this.submit(dataList);
        },
      };
      this.$refs.platformIconDialogRef.open(formParams);
    },
    handleEdit(row) {
      let formParams = {
        title: "编辑",
        form: row,
        list: this.list,
        name: row.name,
        ok: () => {
          this.$refs.platformIconDialogRef.handleClose();
          let dataList = this.list;
          this.submit(dataList);
        },
      };
      this.$refs.platformIconDialogRef.open(formParams);
    },
    handleDel({ id }) {
      this.$root.eventHandle.$emit("openDialog", {
        txt: "是否确认删除该图标？",
        okTxt: "确认",
        noTxt: "取消",
        noShowNoTxt: true,
        ok: () => {
          this.ajax.postStream(
            "/user-web/api/auxiliary/deleteAuxiliaryDataBatch?permissionCode=AUXILIARY_DATA",
            {
              idList: [id],
            },
            (d) => {
              this.$message({
                message: d.body.msg,
                type: d.body.result ? "success" : "error",
              });
              if (d.body.result) {
                this.getList();
              }
            },
            (e) => {
              this.$message.error(e);
            }
          );
        },
      });
    },

    getAuxiliaryCategoryList() {
      let data = {
        page: { length: 50, pageNo: 1 },
        where: [
          {
            field: "ebfbc7a9d21ea1d26868b6b331afd07e",
            table: "c982ba0edaab6ffe3ebc4851a8c1e568",
            value: "CLUE_WEBSITE_ICON",
            operator: "=",
            condition: "AND",
            listWhere: [],
          },
        ],
        page_name: "cloud_auxiliary_category",
      };
      this.ajax.postStream(
        "/user-web/api/auxiliary/getAuxiliaryCategoryList",
        data,
        (d) => {
          if (d.body.result && d.body.content) {
            let { code, id, name, parentCode, platform, remark, system } =
              d.body.content.list[0];
            this.categoryPo = {
              code,
              id,
              name,
              parentCode,
              platform,
              remark,
              system,
            };
          }
        },
        (e) => {
          self.$message({
            message: e.statusText,
            type: "error",
          });
        }
      );
    },
    submit(dataList) {
      let data = {
        categoryPo: this.categoryPo,
        dataList,
      };
      this.ajax.postStream(
        "/user-web/api/auxiliary/saveAuxiliaryCategory?permissionCode=AUXILIARY_DATA",
        data,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg);
            this.getList();
          } else {
            this.$message.error(d.body.msg);
          }
        },
        (e) => {
          this.$message.error(d.body.msg);
        }
      );
    },
  },
  mounted() {
    this.getList();
    this.getAuxiliaryCategoryList();
  },
};
</script>

<style scoped>
.image-box {
  width: 60px;
  height: 60px;
  background-size: 100% 100%;
  margin: 5px auto;
}
</style>
<style>
.platform-icon-list-container .el-table .el-table__body-wrapper td .cell {
  height: auto;
}
</style>
