<template>
  <div class="container xpt-flex">
    <customerBehaviorPerformanceForm
      @getList="getList"
      ref="customerBehaviorPerformanceFormRef"
    />
    <xpt-list
      class="table-list-box"
      :data="list"
      :colData="cols"
      selection=""
      :showHead="false"
      :pageTotal="count"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    >
    </xpt-list>
  </div>
</template>
  
  <script>
import customerBehaviorPerformanceForm from "./customer_behavior_performance_form";
export default {
  name: "reservationRecordContent",
  components: { customerBehaviorPerformanceForm },
  props: ["params", "id"],
  data() {
    var self = this;
    return {
      list: [],
      cols: [
        {
          label: "预约生成时间",
          prop: "create_time",
          format: "dataFormat1",
        },
        {
          label: "姓名",
          prop: "name",
        },
        {
          label: "留资平台",
          prop: "information_platform_name",
        },
        {
          label: "留资渠道",
          prop: "appointment_channel_name",
        },
        {
          label: "预约活动名称",
          prop: "appointment_activity_name",
        },
        {
          label: "预约活动编号",
          prop: "appointment_question_no",
        },
      ],
      count: 0,
      search: {
        page_name: "",
        where: [],
        page_size: 50,
        page_no: 1,
      },
    };
  },
  methods: {
    getList(e) {
      let data = {
        ...e,
        ...this.search,
        appointment_tracing_id: this.id,
      };
      let url = "/crm-web/api/crm_customer_action/appointment";
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { result, content, msg } = res.body;
          if (result && content) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },
    pageSizeChange(res) {
      this.search.page_size = res;
      this.getList();
    },
    pageChange(res) {
      this.search.page_no = res;
      this.getList();
    },
  },
};
</script>
  <style scoped>
.table-list-box {
  height: 640px !important;
}
</style>
  