<template>
  <el-dialog
    :visible.sync="explainVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="dialog-container"
  >
    <ul class="explain-container">
      <li>
        <h3>预约数据跟踪生成规则</h3>
        <p>1、导出预约数据跟踪表中的字段</p>
        <p>
          2、文件名：时间段+预约数据跟踪，例如：20221212 121212至2022101
          101010预约数据跟踪
        </p>
      </li>
      <li>
        <h3>预约接待数据生成规则</h3>
        <p>1、根据预约过滤条件，筛选条件下的留资手机号，并去重。</p>
        <p>
          2、用去重后手机号列表查询门店接待记录，一个留资手机号如果有多条接待记录，生成多行数据。
        </p>
        <p>3、导出字段：留资客户手机、接待时间、接待店铺、接待导购、接待备注</p>
        <p>
          4、文件名：时间段+预约接待数据，例如：20221212 121212至2022101
          101010预约接待数据
        </p>
      </li>
      <li>
        <h3>成品预约成交数据生成规则</h3>
        <p>1、根据预约过滤条件，筛选条件下的留资手机号，并去重。</p>
        <p>
          2、用去重后手机号列表查询销售订单列表的商品行收货人手机号，如果相等则为该留资生成对应的销售订单记录一条。
        </p>
        <p>3、并非导出所有销售订单，只导出满足以下条件的销售订单：</p>
        <p>&nbsp;&nbsp;1）销售订单审核状态 = 已审核；</p>
        <p>
          &nbsp;&nbsp;2）合并订单总支付金额必须大于10元，合并订单中的销售订单才会显示出来。
        </p>
        <p>
          4、导出字段：留资客户手机、拍单时间、支付时间、收货人手机号、销售单号、合并单号、订单店铺、原始店铺、买家昵称、单据状态、合并单总支付金额、销售单总支付金额
        </p>
        <p>
          5、收货人手机号：一个销售订单如果有多个收货人手机号，则拼接成字符串。显示方式为：13187678878、13187678878
        </p>
        <p>
          6、文件名：时间段+成品预约成交数据，例如：20221212 121212至2022101
          101010成品预约成交数据
        </p>
      </li>
      <li>
        <h3>定制预约成交数据生成规则</h3>
        <p>1、根据预约过滤条件，筛选条件下的留资手机号，并去重。</p>
        <p>
          2、用去重后的留资列表查询经销采购单的”客户手机“，如果相等则为该留资生成对应的成交订单记录一条。
        </p>
        <p>
          3、导出所有的经销采购单-导出字段：留资客户手机、采购日期、订单门店、客户名称、订单状态、经销采购价；
        </p>
        <p>
          4、文件名：时间段+定制预约成交数据，例如：20221212 121212至2022101
          101010定制预约成交数据
        </p>
      </li>
    </ul>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      explainVisible: false,
    };
  },
  methods: {
    open() {
      this.explainVisible = true;
    },
  },
};
</script>

<style scoped>
.explain-container {
  color: #000;
  line-height: normal;
}
.explain-container li {
  margin-bottom: 15px;
}
.explain-container li h3 {
  font-size: 15px !important;
  font-weight: bold;
  margin-bottom: 5px;
}
</style>
<style>
.dialog-container  .el-dialog--small {
  width: 700px !important;
}
</style>