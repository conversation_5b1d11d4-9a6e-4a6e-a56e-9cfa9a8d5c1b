<template>
  <div class="container xpt-flex">
    <customerBehaviorPerformanceForm
      @getList="getList"
      :isReception="true"
      ref="customerBehaviorPerformanceFormRef"
    />
    <xpt-list
      class="table-list-box"
      :data="list"
      :colData="cols"
      selection=""
      :showHead="false"
      :pageTotal="count"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    >
    </xpt-list>
  </div>
</template>

<script>
import customerBehaviorPerformanceForm from "./customer_behavior_performance_form";
export default {
  name: "shopReceptionRecordContent",
  components: { customerBehaviorPerformanceForm },
  props: ["params", "id"],
  data() {
    var self = this;
    return {
      list: [],
      cols: [
        {
          label: "接待时间",
          prop: "visitedTime",
          format: "dataFormat1",
        },
        {
          label: "接待人工号",
          prop: "employeeNumber",
        },
        {
          label: "接待人昵称",
          prop: "guiderName",
        },
        {
          label: "接待店铺名称",
          prop: "shopName",
        },
        {
          label: "接待店铺编号",
          prop: "shopCode",
        },
      ],
      count: 0,
      search: {
        page_name: "",
        where: [],
        page_size: 50,
        page_no: 1,
      },
    };
  },
  methods: {
    getList(e) {
      let data = {
        ...e,
        ...this.search,
        appointment_tracing_id: this.id,
      };
      let url = "/crm-web/api/crm_customer_action/visit";
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { result, content, msg } = res.body;
          if (result && content) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },
    pageSizeChange(res) {
      this.search.page_size = res;
      this.getList();
    },
    pageChange(res) {
      this.search.page_no = res;
      this.getList();
    },
  },
};
</script>
    <style scoped>
.table-list-box {
  height: 640px !important;
}
</style>
    