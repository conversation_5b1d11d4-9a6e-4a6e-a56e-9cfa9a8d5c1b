<template>
  <div id="customer-behavior-performance-container" class="xpt-flex max-height">
    <div class="info-box">
      <h3 class="title">当前线索信息</h3>
      <p class="info">
        <span>姓名：{{ info.name }}</span>
        <span>手机号：{{ info.mobile }}</span>
        <span
          >预约提交时间：{{ info.appointment_submit_time | dataFormat1 }}</span
        >
        <span>留资平台：{{ info.information_platform_name }}</span>
        <span>留资渠道：{{ info.appointment_channel_name }}</span>
        <span>预约活动编码：{{ info.appointment_question_no }}</span>
        <span>预约活动名称：{{ info.appointment_activity_name }}</span>
      </p>
    </div>
    <div class="info xpt-flex max-height">
      <el-tabs
        v-model="tabName"
        class="max-height xpt-flex"
        @tab-click="handleTabsChange"
      >
        <el-tab-pane label="成交订单" name="deal">
          <customerBehaviorPerformanceContent
            :id="id"
            :isReservation="params.isReservation"
            ref="customerBehaviorPerformanceContentRef"
          />
        </el-tab-pane>
        <el-tab-pane label="预约记录" name="reservation">
          <reservationRecordContent
            :id="id"
            ref="reservationRecordContentRef"
          />
        </el-tab-pane>
        <el-tab-pane label="门店接待记录" name="storeReception">
          <shopReceptionRecordContent
            :id="id"
            ref="shopReceptionRecordContentRef"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import customerBehaviorPerformanceContent from "@/components/appoint_management/components/customer_behavior_performance_content";
import reservationRecordContent from "@/components/appoint_management/components/reservation_record_content";
import shopReceptionRecordContent from "@/components/appoint_management/components/shop_reception_record_content";
export default {
  props: ["params"],
  components: {
    customerBehaviorPerformanceContent,
    reservationRecordContent,
    shopReceptionRecordContent,
  },
  data() {
    return {
      info: {
        userName: "xxx",
        phone: "xxx",
        submit_time: "xxx",
        platform: "xxx",
        activity_no: "xxx",
        activity_name: "xxx",
      },
      tabName: "deal",
      id: null,
    };
  },
  mounted() {
    console.log(this.params, "params=====");
    this.id = this.params.row.id;
    this.getInfo();
    this.$nextTick(() => {
      this.handleTabsChange();
    });

    
  },
  methods: {
    //获取详情信息
    getInfo() {
      this.ajax.postStream(
        "/crm-web/api/crmAppointmentTracing/getAssignAppointmentDetail",
        { id: this.id },
        (res) => {
          let { result, content, msg } = res.body;
          if (result && content) {
            this.info = content || {};
          } else {
            this.$message.error(msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    handleTabsChange() {
      let tabs = {
        deal: "customerBehaviorPerformanceContentRef",
        reservation: "reservationRecordContentRef",
        storeReception: "shopReceptionRecordContentRef",
      };
      this.$refs[
        tabs[this.tabName]
      ].$refs.customerBehaviorPerformanceFormRef.handleSearch();
    },
  },
};
</script>
<style scoped>
#customer-behavior-performance-container {
  padding: 20px;
  height: 100%;
}

.info-box {
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
.title {
  font-size: 18px !important;
  font-weight: bold;
}

.info {
  margin-top: 20px;
}
.info span {
  margin-right: 40px;
}

.max-height {
  height: 100% !important;
}
</style>
<style>
.max-height .el-tabs__item {
  width: 100px;
  text-align: center;
}
</style>