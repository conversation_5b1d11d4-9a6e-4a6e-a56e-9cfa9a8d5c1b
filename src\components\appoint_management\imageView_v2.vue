<!-- 图片的增删改查组件,没有分组之说 -->
<template>
  <div id='xpt-image-v2' v-if='isShow'>
    <div class="xpt-image__body">
      <div class='xpt-image__main'>
        <div class="xpt-image__rotate" @click="rotate"><i class="reloadSingle"></i></div>
        <div class="xpt-image__close" @click="close"><i class="el-icon-close"></i></div>
<!--        <div class='xpt-image__direction' @click='prev'><i class='el-icon-arrow-left'></i></div>-->
        <div class='xpt-image__box'>
          <img
            v-if="updateImg"
            :src='imgList.picture_url'
            :style="'transform: rotate(' + imgRotate + 'deg);-webkit-transform:rotate(' + imgRotate + 'deg);-moz-transform:rotate(' + imgRotate + 'deg);-o-transform:rotate(' + imgRotate + 'deg);'"
          />
<!--          <el-row v-if='imgList' style="color:#fff">-->
<!--            <el-col :span="9" style="text-align:right;">创建人: {{imgList.creator_nick}}</el-col>-->
<!--            <el-col :span="6" style="text-align:center;">创建日期: {{imgList.create_time|dataFormat('yyyy-MM-dd hh:mm:ss')}}-->
<!--            </el-col>-->
<!--            <el-col :span="9" style="text-align:left;">文件名称: <span>{{imgList.name}}</span></el-col>-->
<!--          </el-row>-->
        </div>
<!--        <div class='xpt-image__direction' @click='next'><i class='el-icon-arrow-right'></i></div>-->
      </div>
      <div class='xpt-image__thumbnail'>
        <ul>
<!--          <li v-for='(img,_index) in imgList' :class='index===_index?checkIsLast():""' :key='_index'-->
<!--              @click='active(_index)'>-->
<!--            <img style="width:100%;height:100%;" :src='img.picture_url' />-->
<!--            <i class="el-icon-close" @click="cancel(_index)" v-if="ifUpload"></i>-->
<!--            <el-checkbox class="el-icon-checkbox" v-model="img._selected"></el-checkbox>-->
<!--          </li>-->
          <li class="lastLi" v-if="ifUpload && !imgList.picture_url">
            <el-button v-if="!imgList.picture_url" type="primary" size="medium" @click="startUpload">上传
              <input  style="display: none" type="file" :id='files' multiple="multiple" @change='change' >
            </el-button>
          </li>
          <!-- <li  class="lastLi" v-if="imgList.length">
            <el-button type="primary" size="medium" @click="zipDownload">批量下载</el-button>
          </li> -->
        </ul>
      </div>
    </div>
  </div>
</template>
<script>

  export default {
    data() {
      return {
        index: 0,
        imgList: [],
        isShow: this.show,
        imgRotate: 0,
        host: '',
        ifClickUpload: false,//是否要点击上传按钮
        // 上传按钮相关
        buttonIndex: 0,
        imgList: [],
        //oldData: {},
        files:'files'+Math.random(),//随机定义id，防止出现相同id
        buttonHost:null,
        uploadNum: 0,
        //uploadImgList:[],//上传的图片路径
        filesLength: null,
        updateImg: false
      };
    },
    props: {

      //是否要开始向数据库请求数据
      isGetImgList: {
        type: Boolean,
        default: false,
      },
      // 是否显示
      show: {
        type: Boolean,
        default: false,
      },
      ifUpload: {
        type: Boolean,
        default: false,
      },
      dataObj: {
        type: Object,
        default() {
          return {};
        },
      },
      paramsInfo: {
        type: Object,
        default() {
          return {};
        },
      },
      upLoadBtn: {
        type: Boolean,
        default: false,
      },
    },
    methods: {
      zipDownload() {
        //路径，名称集合
        var list_path = [];
        this.imgList.forEach(v => {
          if (v._selected) {
            list_path.push(v.path + '=' + v.name);
          }
        });

        if (!this.host) {
          this.$message.error('获取域名出错，请重新打开');
          return;
        }
        if (list_path.length === 0) {
          this.$message.error('请选择要下载的附件');
          return;
        }
        //定义下载的名称；
        var name = '批量下载';
        var form = document.createElement('form');   //定义一个form表单
        form.setAttribute('style', 'display:none');   //在form表单中添加查询参数
        form.setAttribute('method', 'post');//设置或返回将数据发送到服务器的HTTP方法
        form.setAttribute('action', this.host + '/file-web/api/file/download/' + name + '.zip');
        form.setAttribute('target', '');
        form.setAttribute('enctype', 'application/x-www-form-urlencoded');

        var input1 = document.createElement('input');
        input1.setAttribute('type', 'hidden');
        input1.setAttribute('name', 'path');
        input1.setAttribute('value', list_path.toString());

        document.getElementById('xpt-image-v2').append(form);  //将表单放置在web中
        form.append(input1);   //将查询参数控件提交到表单上
        form.submit();   //表单提交
        form.remove();
      },
      rotate() {
        this.rotate.ing = true;

        this.imgRotate += 90;

        this.$nextTick(() => {
          this.rotate.ing = false;
        });
      },
      checkIsLast() {
        if (!this.rotate.ing) {
          this.imgRotate = 0;
          // if (this.index === this.imgList.length - 1) {
          //   this.$message.info('已经是最后一张');
          // }
        }

        return 'active';
      },
      /**
       *获取已经上传的图片
       **/
      getImgList() {
        // let params={
        //   where: [],
        //   page_size: 50,
        //   page_no: 1,
        //   if_need_page:'Y',
        //   appointmentActivityId: this.paramsInfo.parent_no,
        //   page_name: 'crm_appointment_platformpush'
        // }
        // this.$request('/crm-web/api/crm_appointment_activity/details/list',
        //   {
        //     ...params
        //   }).then(res =>{
        //   if (res.result) {
        //     let list = res.content.crmAppointmentPictureDesignVOs;
        //     let imgList = [];
        //       list.map((v, i) => {
        //         console.log(v,'picture_urlpicture_urlpicture_urlpicture_urlpicture_urlpicture_url');
        //         if (this.isPucture(v.picture_url)) {
        //           v.isPucture = true;
        //         } else {
        //           v.isPucture = false;
        //         }
        //         if (v.active) {
        //           this.index = i;
        //         }
        //         //imgList.push(v);
        //       });
        //       let single = [];
        //       single.push(list[this.paramsInfo.index] || '')
        //       this.imgList = single || [];
        //   }
        //   }).catch(err=>{
        //   }).finally(()=>{
        //   });
        // 分割线
        // var submitData = {
        //   order_no: this.paramsInfo.parent_no,//单据编号 ：	String
        //   sub_order_no: this.paramsInfo.child_no,//子单据编号：	String
        //   ext_data: this.paramsInfo.ext_data,//扩展字段：		String
        //   cloud_file_id: null,//流水主键：		Long
        // };
        // var _this = this;
        // this.ajax.postStream('/file-iweb/api/cloud/file/list', submitData, res => {
        //   var data = res.body;
        //   //console.log('data',data);
        //   if (!data.result) {
        //     this.$message.error(data.msg);
        //     return;
        //   }
        //   var list = data.content.list;
        //   //判断图片
        //   //var imgList = [];
        //   list.map((v, i) => {
        //     if (this.isPucture(v.path)) {
        //       v.isPucture = true;
        //     } else {
        //       v.isPucture = false;
        //     }
        //     if (v.active) {
        //       this.index = i;
        //     }
        //     //imgList.push(v);
        //   });
        //   this.imgList = list || [];
        //
        // });
        this.imgList=this.paramsInfo
        if (this.paramsInfo.picture_url) {
          this.updateImg=true
        } else {
          this.updateImg=false
        }
      },
      /**
       *上传了新的图片，需要更新
       */
      uploadImg() {
        console.log('看一下事件监听');
        this.getImgList();
        this.ifClickUpload = false;

      },
      //开始上传图片
      startUpload() {

        this.ifClickUpload = true;
        setTimeout(() => {
          this.ifClickUpload = false;
        }, 100);
      },
      close() {
        this.isShow = false;
        this.$emit('close', this.imgList);
      },
      prev() {
        if (this.index > 0) {
          this.index--;
        } else {
          this.index = this.imgList.length - 1;
        }
      },
      next() {
        if (this.index < this.imgList.length - 1) {
          this.index++;
        } else {
          this.index = 0;
        }
      },

      cancel(index) {
        var self = this;
        self.ifClickUpload = false;
        this.$root.eventHandle.$emit('openDialog', {
          txt: '是否确定删除？',
          okTxt: '确定',
          cancelTxt: '取消',
          noShowNoTxt: true,
          ok() {
            var list_cloud_file_id = [];
            list_cloud_file_id.push(self.imgList[index].cloud_file_id);
            self.ajax.postStream('/file-iweb/api/cloud/file/delete', { list_cloud_file_id: list_cloud_file_id }, res => {
              if (res.body.result) {
                self.$emit('delImg',res.body,self.dataObj.child_no)
                self.$message.success('删除成功');
                self.imgList.splice(index, 1);
                //调整当前图片显示
                if (self.imgList.length == index) {
                  self.index = index - 1;
                }
              } else {
                self.$message.error(res.body.msg);
              }
            });
          },
          cancel() {
          },
        });
      },
      active(index) {
        this.index = index;
      },
      isPucture(str) {
        console.log('str', str);
        if (!str) return;
        str = str.toString();
        var strFilter = '.jpeg|.gif|.jpg|.png|.bmp|.pic|';
        if (str.indexOf('.') > -1) {
          var p = str.lastIndexOf('.');
          var strPostfix = str.substring(p, str.length) + '|';
          strPostfix = strPostfix.toLowerCase();

          if (strFilter.indexOf(strPostfix) > -1) return true;

        }


        return false;
      },
      // 上传按钮相关
      //获取上传文件的信息，并逐个上传
      change() {
        let files = document.getElementById(this.files).files,
          len = files.length,
          fr = new FileReader(),
          index = 0,
          self = this;
        fr.readAsDataURL(files[0]);

        this.uploadNum = 0;
        //this.uploadImgList = [];
        this.filesLength = files.length;

        //根据用户传过来的参来判断是否要作图片限制
        if(this.isOnlyPic){
          for(let i = 0; i < this.filesLength;i++){
            let bool = this.isPucture(files[i].name);
            if(!bool){
              this.$message.error('请传入jpeg,gif,jpg,png,bmp,pic格式的图片');
              document.getElementById(this.files).value = '';
              return;
            }
          }
        }


        self.upload(files,0);
        fr.onload = function(e) {
          index += 1;
          if(index<len){
            fr.readAsDataURL(files[index]);
            self.upload(files,index)
          }
        }
      },
      //判断是否为图片
      isPucture(str) {
        str = str.toString();
        var strFilter=".jpeg|.gif|.jpg|.png|.bmp|.pic|"
        if(str.indexOf(".")>-1)
        {
          var p = str.lastIndexOf(".");
          var strPostfix=str.substring(p,str.length) + '|';
          strPostfix = strPostfix.toLowerCase();
          if(strFilter.indexOf(strPostfix)>-1)
          {
            return true;
          }
        }
        return false;
      },
      // 获取上传图片的key值
      validateKey(resolve){
        this.ajax.postStream('/app-web/app/file/validateKey.do', {type:"GET"} ,(res)=>{
          console.log(res);
          if(res.body.success){
            resolve && resolve(res.body.data)
          }else{
            this.$message.error('获取文件路径失败，请重新操作');
          }
        })
      },
      // 提交至服务器
      upload(files,index) {
        let self = this
        new Promise((resolve,reject)=>{
          this.validateKey(resolve)
        }).then((key)=>{
          let formData = new FormData();
          formData.append('file', files[index]);
          // console.log(65);
          var _this = this;
          //上传到附件库
          let requestHeaderPath=this.getUploadFileApiHeader();
          let host = this.buttonHost.split('//')[1];
          let url = requestHeaderPath+host+'/'+key+'.do';
        //   let url = 'http://**************:12010/upload-web/app/file/uploadFile/'+host+'/'+key+'.do';
          this.ajax.post(url, formData, (s) => {
            if(s.ok && s.body && s.body.data){
              // var index1 = files[index].name.indexOf('.')+1;
              // var index2 = files[index].name.length;
              self.$emit('path',s.body.data,this.dataObj.child_no)
              var data ={
                file_type: files[index].name.split('.').pop(),// 文件类型 string
                order_no: this.dataObj.parent_no,// 单据编号 string
                sub_order_no: this.dataObj.child_no,// 子单据编号 string
                name: files[index].name,// 名字 string
                size: files[index].size,// 大小 long
                group_value_json: JSON.stringify(this.dataObj),//  分组内容 string
                //group_value_json: null,//分组内容 string,没有分组之说
                path: s.body.data,//  地址 string
                ext_data: null,// 扩展字段 string
              }

              //上传到接口，用于查看时获取
              this.ajax.postStream('/file-iweb/api/cloud/file/upload',data,res => {

                if(res.body.result){
                  this.uploadNum++;
                  _this.imgList.picture_url=s.body.data
                  _this.updateImg=true;
                  //全部上传完才显示成功
                  if(this.uploadNum === files.length){
                    this.$message.success('上传成功');
                    // this.$emit("uploadImg");//事件监听
                    _this.uploadImg()
                  }
                }else {
                  this.$message.error(res.body.msg);
                }
              })

            }
          }, (e) => {
            this.$message({
              message: files[index].name+'上传失败',
              type: 'error'
            })
          })
        })

      },
    },
    watch: {
      index(n, o) {
        let el = document.getElementsByClassName('xpt-image__thumbnail');
        el.scrollLeft = (n + 1) * 110 - el.clientWidth + 110;
      },
      //立即获取图片
      isGetImgList: function (newVal, old) {
        console.log(newVal, '777');
        if (!newVal) return;
        this.getImgList();
      },
      show: function (newVal) {
        console.log('show', newVal);
        this.isShow = newVal;
      },
      'ifClickUpload': function(newVal,oldVal) {
        if(newVal) {

          document.getElementById(this.files).click();
          if(this.buttonHost) {
            return;
          }
          //获取域名
          this.ajax.postStream('/file-iweb/api/cloud/fileConfig/get',{},res => {
            if(res.body.result){
              this.buttonHost = res.body.content.host;

            }else {
              this.$message.error(res.body.msg);
            }
          })
        }
      }
    },
    mounted() {
      //获取域名
      this.ajax.postStream('/file-iweb/api/cloud/fileConfig/get', {}, res => {
        if (res.body.result) {
          this.host = res.body.content.host;
        }
      });
    },
  };
</script>
<style lang="stylus" scoped>
  #xpt-image-v2 .reloadSingle {
    display: inline-block;
    position: relative;
    zoom: 1.1;
    border-color: transparent #999;
    border-radius: 50%;
    border-style: solid;
    border-width: 0.22em;
    height: 2em;
    margin: .25em;
    width: 2em;
  }

  #xpt-image-v2 .reloadSingle:before, .reloadSingle:after {
    border-style: solid;
    content: '';
    display: block;
    position: absolute;
    width: 0;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }

  #xpt-image-v2 .reloadSingle:before {
    border-color: transparent #999 transparent transparent;
    border-width: 0.4em 0.6em 0.4em 0;
    bottom: -0.3125em;
    right: 0;
  }

  #xpt-image-v2 .reloadSingle:after {
    border-color: transparent transparent transparent #999;
    border-width: 0.4em 0 0.4em 0.6em;
    top: -0.3125em;
    left: 0;
  }

  #xpt-image-v2
    width: 100%
    height: 100%
    background: rgba(0, 0, 0, .3)
    position: fixed
    top: 0
    left: 0
    z-index: 9

    .xpt-image__body
      width: 80%
      height: 80%
      background: #000
      position: absolute
      top: 50%
      left: 50%
      transform: translate(-50%, -50%)
      box-shadow: 0 0 5px rgba(0, 0, 0, .5)
      flex-direction: column

      .xpt-image__main
        position: absolute
        top: 0
        left: 0
        bottom: 100px
        width: 100%
        display: flex
        align-items: center

        .xpt-image__close
          position: absolute
          top: 10px
          right: 10px
          cursor: pointer

          i
            font-size: 24px !important
            color: #999

            &:hover
              color: #f5f5f5

        .xpt-image__rotate
          position: absolute
          top: 5px
          right: 40px
          cursor: pointer

          i
            &:hover
              border-color: transparent #fff;

              &:before
                border-right-color: #fff;

              &:after
                border-left-color: #fff;

        .xpt-image__direction
          flex: 1
          height: 80px
          text-align: center
          cursor: pointer

          &:after
            content: ''
            display: inline-block
            vertical-align: middle
            height: 80px

          i
            font-size: 20px !important
            color: #999
            vertical-align: middle

            &:hover
              color: #f5f5f5

        .xpt-image__box
          flex: 8
          padding: 10px 0
          text-align: center
          width: 100%
          height: 100%
          margin-top: 56px

          img
            max-width: 100%
            max-height: 100%
            vertical-align: middle

          &:after
            content: ''
            height: 100%
            display: inline-block
            vertical-align: middle

      .xpt-image__thumbnail
        position: absolute
        bottom: 0
        left: 0
        width: 100%
        height: 100px
        padding: 10px 0 10px 10px
        overflow: auto

        ul
          white-space: nowrap
          height: 100%

        li
          width: 100px
          height: 80px
          display: inline-block
          margin-right: 10px
          cursor: pointer
          overflow: hidden
          position: relative

          img
            width: 100%
            vertical-align: middle

          &:after
            content: ''
            display: inline-block
            height: 80px
            vertical-align: middle

          &.active
            border: 2px solid #ddde0f

          &:.lastLi
            position: fixed
            bottom: 9px
            width: 101px
            height: 82px
            right: 0
            z-index: 2
            background: #000
            margin-right: 0
            text-align: center

          .el-icon-close
            position: absolute
            top: 5px
            right: 5px

          .el-icon-checkbox
            position: absolute
            left: 0

    .xpt-image__upload
      height: 40px
      position: relative
      cursor: pointer
      overflow: hidden
      display: inline-block

  *
  display
  :
  inline
  *
  zoom
  :
  1
  input
    position: absolute
    font-size: 100px
    right: 0
    top: 0
    height: 100%
    opacity: 0
    cursor: pointer

  .wh100
    width: 100% !important
    height: 100% !important

</style>
