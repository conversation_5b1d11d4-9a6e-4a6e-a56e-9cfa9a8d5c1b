<!-- 留资平台列表 -->
<template>
    <xpt-list 
        ref='information_platform_list' 
        id='informationPlatform' 
        :data='goodsList' 
        :btns='goodsBtns' 
        :colData='goodsCols' 
        orderNo 
        isNeedClickEvent 
        :selection='selection'
        @selection-change='goodsSelectionChange'
        @radio-change='goodsRadioChange'
        :pageTotal='goodsCount' 
        :searchPage='goodsQuery.page_name' 
        @search-click='goodsListSearch' 
        @page-size-change='goodsPageSizeChange' 
        @current-page-change='goodsPageChange'
        @row-dblclick='rowDblclick'>
    </xpt-list>
</template>
<script>
    import fn from '@common/Fn.js'
    export default {
        props:['params'],
        data() {
            let self = this;
            return {
                goodsList: [],
                goodsBtns: [
                    {
                        type: 'success',
                        txt: '刷新',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.refresh()
                        }
                    }, {
                        type: 'primary',
                        txt: '新增',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.goodsAdd()
                        }
                    }, {
                        type: 'danger',
                        txt: '删除',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.goodDelete()
                        }
                    }, {
                        type: 'primary',
                        txt: '保存',
                        disabled: () => {
                            return self.ifSave;
                        },
                        click() {
                            self.save()
                        }
                    }, {
                        type: 'primary',
                        txt: '留资平台图标',
                        click() {
                            self.platformIcon()
                        }
                    }
                ],
                goodsCols: [
                    {
                        label: '留资编码',
                        prop: 'platformNo',
                        bool: true,
                        elInput: true,
                        disabled(row) {
                            return row.platformId ? true : false
                        },
                        change(row) {
                            self.changePlatformNo(row)
                        },
                        blur: (row, e) => {},
                        width: 140
                    }, {
                        label: '留资平台',
                        prop: 'platformName',
                        bool: true,
                        elInput: true,
                        disabled(row) {
                            // return self.materiaCanEdit(row)
                            return false
                        },
                        change(row) {},
                        blur: (row, e) => {},
                        width: 140
                    }, {
                        label: '平台渠道',
                        prop: 'platformChannel',
                        bool: true,
                        elInput: true,
                        disabled(row) {
                            return false
                        },
                        change(row) {},
                        blur: (row, e) => {},
                        width: 140
                    }, {
                        label: '平台类型',
                        prop: 'platformType',
                        width: 90,
                        bool: true,
                        // isThis: true,
                        isSelect: true,
                        disabled(row) {
                            return false
                        },
                        obj: {
                            'SYSTEM': '系统',
                            'STATION': '外部',
                        }
                    }, {
                        label: '更新人',
                        prop: 'modifyName'
                    }, {
                        label: '更新时间',
                        prop: 'modifyTime',
                        format: 'dataFormat1',
                        width: 130
                    }, {
                        label: '创建人',
                        prop: 'createName'
                    }, {
                        label: '创建时间',
                        prop: 'createTime',
                        format: 'dataFormat1',
                        width: 130
                    }
                ],
                goodsSelect: [],
                goodsCount: 0,
                goodsQuery: {
                    page_name: "crm_information_platform",
                    where: [],
                    page_size: 50,
                    page_no: 1,
                    // 是否过滤,1为不过滤，不传则默认过滤
                    if_need_page: 'Y'
                },
                goodsSelects: '',
                ifSave: false,
                selection: 'checkbox',
                goodsOldList: []
            }
        },
        methods: {
            platformIcon(){
                this.$root.eventHandle.$emit('alert',{
					component:()=>import('@components/appoint_management/components/platform_icon_list.vue'),
                    style:'width:800px;height:500px',
                    title:'留资平台图标'
				})
            },
            rowDblclick(obj) {
                if(!this.params.isAlert) return;
                let data = obj;
                if(this.selection === 'checkbox') {
                    this.goodsSelect = this.goodsSelect.length?this.goodsSelect:[];
                    this.goodsSelect.push(data);
                    this.goodsSelect = Array.from(new Set(this.goodsSelect));
                }
                this.close();
            },
            close () {
                if(this.goodsSelect.length == 0){
                    this.$message.error('请先选择一个留资平台');
                    return;
                }
                this.params.callback(this.goodsSelect);
                this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
            },
            // 删除
            goodDelete() {
                if (this.goodsSelect.length == 0) {
                    this.$message.error('请选择需要操作的行')
                    return
                }
                let illegalList = []
                this.goodsSelect.forEach(item => {
                    if (item.platformId) {
                        illegalList.push(item.platformId)
                    }
                    if (item.temId) {
                        var i = this.goodsList.length;
                        while (i--) {
                            if (this.goodsList[i].temId === item.temId) {
                                this.goodsList.splice(i, 1)
                                break
                            }
                        }
                    }
                })
                if (illegalList.length == 0) return
                this.ajax.postStream('/crm-web/api/crm_information_platform/delete', [...illegalList], d => {
                    if (d.body.result) {
                        this.$message.success(d.body.msg)
                    } else {
                        this.$message.error(d.body.msg)
                    }
                    this.getGoodsList()
                    resolve && resolve();
                }, (e) => {
                    this.$message.error(e);
                    resolve && resolve();
                })
            },
            refresh() {
                this.getGoodsList()
            },
            // 行选中
            goodsSelectionChange(row) {
                this.goodsSelect = row;
            },
            // 行选中
            goodsRadioChange(row) {
                this.goodsSelect = row;
            },
            // 获取商品信息
            getGoodsList(resolve, ifPromiss) {
                let self = this;
                let data = JSON.parse(JSON.stringify(this.goodsQuery));
                let url = '/crm-web/api/crm_information_platform/list'
                this.ajax.postStream(url, data, d => {
                    if (d.body.result && d.body.content) {
                        this.goodsList = d.body.content.list
                        this.goodsOldList = JSON.parse(JSON.stringify(d.body.content.list))
                        this.goodsCount = d.body.content.count
                    }
                    resolve && resolve();
                }, (e) => {
                    this.$message.error(e);
                    resolve && resolve();
                })
            },
            //通用查询搜索
            goodsListSearch(where, reslove) {
                this.goodsQuery.where = where
                this.getGoodsList(reslove)
            },
            // 当前页改变
            goodsPageSizeChange(ps) {
                this.goodsQuery.page_size = ps;
                this.getGoodsList();
            },
            // 当前页面显示行数改变
            goodsPageChange(page) {
                this.goodsQuery.page_no = page;
                this.getGoodsList();
            },
            // 新增，复制新增
            goodsAdd() {
                let copyData = {}
                copyData.temId = new Date().getTime()
                copyData.platformName = ''
                copyData.platformNo = ''
                copyData.platformChannel = ''
                copyData.platformType = ''
                this.goodsList.unshift(copyData)
                // 滚动到最底部
                let scrollObj = document.querySelector('#informationPlatform').querySelector('.el-table__body-wrapper')
                this.$nextTick(function() {
                    scrollObj.scrollTop = 0
                })
            },
            changePlatformNo(row){
                if (!(/^[A-Z0-9_]+$/.test(row.platformNo))) {
                    this.$message.error('留资编码仅能包含大写字母数字或者下划线')
                    return
                }
            },
            save() {
                let data = [], self = this, msg = ''
                this.goodsList.some((obj, index) => {
                    if (!obj.platformNo) {
                        msg = '留资编码不能为空'
                    } else if (!(/^[A-Z0-9_]+$/.test(obj.platformNo))) {
                        msg = '留资编码仅能包含大写字母数字或者下划线'
                    }/* else if (!obj.platformChannel) {
                        msg = '平台渠道不能为空'
                    }*/ else if (!obj.platformType) {
                        msg = '平台类型不能为空'
                    } else if (!obj.platformName.trim()) {
                        msg = '留资平台不能为空'
                    }
                    if (msg) {
                        msg = '留资平台明细-第' + (index + 1) + '行-' + msg
                        return true
                    }
                })
                if (msg) {
                    this.$message.error(msg)
                    return
                }
                this.ifSave = true
                this.goodsList.forEach(item => {
                    if (item.platformNo && item.temId) {
                        let obj = {
                            platformNo: item.platformNo,
                            platformName: item.platformName,
                            platformChannel: item.platformChannel,
                            platformType: item.platformType
                        }
                        data.push(obj)
                    }
                    if (item.platformNo && item.platformId) {
                        let len = self.goodsOldList.length
                        while(len--) {
                            if (self.goodsOldList[len].platformId == item.platformId) {
                                let newObj = {
                                    platformNo: item.platformNo,
                                    platformName: item.platformName,
                                    platformChannel: item.platformChannel,
                                    platformType: item.platformType
                                },
                                oldObj = {
                                    platformNo: self.goodsOldList[len].platformNo,
                                    platformName: self.goodsOldList[len].platformName,
                                    platformChannel: self.goodsOldList[len].platformChannel,
                                    platformType: self.goodsOldList[len].platformType
                                }
                                let ifUpdate = this.compareData(newObj, oldObj)
                                if (ifUpdate) {
                                    let obj = {
                                        platformId: item.platformId,
                                        platformNo: item.platformNo,
                                        platformName: item.platformName,
                                        platformChannel: item.platformChannel,
                                        platformType: item.platformType
                                    }
                                    data.push(obj)
                                }
                            }
                        }
                    }
                })
                this.ajax.postStream('/crm-web/api/crm_information_platform/save', [...data],  (d) => {
                    self.ifSave = false
                    if (d.body.result) {
                        this.$message.success(d.body.msg)
                    } else {
                        this.$message.error(d.body.msg)
                    }
                    setTimeout(() => {
                        self.getGoodsList()
                    }, 2000)
                }, err => {
                    self.ifSave = false
                    this.$message.error(err);
                });
            }
        },
        mounted() {
            if (this.params.selection) {
                this.selection = this.params.selection
            }
            if (this.params.ifAlert) {
                this.goodsBtns = [
                    {
                        type: 'primary',
                        txt: '确认',
                        click: this.close
                    }
                ]
                this.goodsCols = [
                    {
                        label: '留资编码',
                        prop: 'platformNo',
                        width: 140
                    }, {
                        label: '留资平台',
                        prop: 'platformName',
                        width: 140
                    }, {
                        label: '平台渠道',
                        prop: 'platformChannel',
                        width: 140
                    }, {
                        label: '平台类型',
                        prop: 'platformType',
                        formatter(val) {
                        	switch(val) {
                        		case 'SYSTEM': return '系统'; break;
                        		case 'STATION': return '外部'; break;
                        		default: return val; break;
                        	}
                        },
                        width: 90
                    }, {
                        label: '更新人',
                        prop: 'modifyName'
                    }, {
                        label: '更新时间',
                        prop: 'modifyTime',
                        format: 'dataFormat1',
                        width: 130
                    }, {
                        label: '创建人',
                        prop: 'createName'
                    }, {
                        label: '创建时间',
                        prop: 'createTime',
                        format: 'dataFormat1',
                        width: 130
                    }
                ]
            }
            this.getGoodsList()
        }
    }
</script>
<style type="text/css">
    .el-dialog__wrapper {
        background-color: transparent
    }
</style>