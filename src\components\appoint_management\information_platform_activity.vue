<template>
  <xpt-list
      ref='appointment_Activity_detail'
      id='appointment_Activity_detail'
      :data='materiaListData'
      :colData='materiaListCols'
      :btns='materiaListBtns'
      :orderNo= true
      isNeedClickEvent
      selection='radio'
      :pageTotal="materiaListCount"
      @radio-change='langeRadioChange'
      :searchPage='materiaListQuery.page_name'
      @search-click='serchMaterial'
      @page-size-change='materiaListPageSizeChange'
      @current-page-change='materiaListPageChange'
  ></xpt-list>
</template>
<script>
export default {
  props: ['params'],
  data () {
    var self = this;
    return {
      materiaListData: [],
      materiaListCols: [
        {
          label: '留资编码',
          prop: 'platformNo',
        }, {
          label: '留资名称',
          prop: 'platformName',
          width: 150
        }, {
          label: '平台渠道',
          prop: 'platformChannel'
        }, {
          label: '平台类型',
          prop: 'platformType',
          formatter(val) {
              switch(val) {
                  case 'SYSTEM': return '系统'; break;
                  case 'STATION': return '外部'; break;
                  case 'IMPORT': return '导入'; break;
                  default: return val; break;
              }
          }
        }, {
          label: '推广计划ID',
          prop: 'actPlanId',
        }, {
          label: '推广计划名称',
          prop: 'actPlanName',
          width: 140
        }, {
          label: '预约链接',
          prop: 'appointmentUrl'
        },{
          label: '创建人',
          prop: 'createName'
        }, {
          label: '创建时间',
          prop: 'createTime',
          format: 'dataFormat1',
          width: 130
        }
      ],
      materiaListCount: 0,
      materiaListBtns: [
        {
            type: 'primary',
            txt: '确认',
            disabled(){
                return false
            },
            click: self.close
        }
      ],
      materiaSelect: '',
      page_name: '',
      materiaListQuery: {
        where: [],
        page_size: 50,
        page_no: 1,
        if_need_page:'Y',
        page_name: 'crm_appointment_platformpush'
      },
    }
  },
  methods: {
    // 行选中 
    langeRadioChange(row) {
      this.materiaSelect = row;
    },
    materiaListPageSizeChange (size) {
      this.materiaSelect = ''
      this.materiaListQuery.page_size = size
      this.getMaterialList()
    },
    materiaListPageChange(page){
      this.materiaSelect = ''
      this.materiaListQuery.page_no = page
      this.getMaterialList()
    },
    serchMaterial(where,reslove) {
      this.materiaListQuery.where = where
      this.getMaterialList(reslove)
    },
    getMaterialList(resolve) {
      let id = this.params.activityId ? this.params.activityId : '', self = this
      if(id){
        let params = Object.assign({}, self.materiaListQuery)
        params.appointmentActivityId = id
        this.ajax.postStream('/crm-web/api/crm_appointment_activity/details/list', params,function(response){
          if(response.body.result && response.body.content) {
            let content = response.body.content.result || []
            self.materiaListData = content.list
            self.materiaListCount = content.count
            resolve&&resolve()
          } else {
            self.$message.error(response.body.msg || '');
            resolve&&resolve()
          }
        });
      } else {
        resolve&&resolve()
      }
    },
    close () {
      if(!this.materiaSelect){
        this.$message.error('请先选择一个留资平台');
        return;
      }
      this.params.callback(this.materiaSelect);
      this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
    },
  },
  mounted() {
    this.getMaterialList()
  }
}
</script>