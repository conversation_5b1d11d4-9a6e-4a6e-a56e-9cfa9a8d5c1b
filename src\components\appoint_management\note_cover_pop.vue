<template>
  <div class="xpt-flex">
    <xpt-headbar class="header-bar">
      <el-button slot='left' size="mini" type="primary" @click="close">确认</el-button>
      <el-input slot='right' size="mini" style="margin-right: 50px" v-model="selectId" placeholder="请输入预约活动名称(模糊查询)"></el-input>
      <el-button slot='right' size="mini" type="primary" @click="searchData">搜索</el-button>
      <el-button slot='right' size="mini" type="primary" @click="reset">重置</el-button>
    </xpt-headbar>
    <xpt-list
      ref='table'
      orderNo
      :showHead="false"
      :data='list'
      :btns='btns'
      :colData='cols'
      :pageTotal='totalPage'
      selection='checkbox'
      @radio-change='radioChange'
      @selection-change='selectChange'
      @page-size-change='pageSizeChange'
      @current-page-change='pageChange'
    >
      <template slot='text' slot-scope='scope'>
        <span>{{scope.row.appointSuccessedNote ? scope.row.appointSuccessedNote :''}}</span>
        <span>{{(scope.row.appointShopNote ? scope.row.appointShopNote : '') }}</span>
        <span>{{(scope.row.appointEndNote ? scope.row.appointEndNote : '')}}</span>
      </template>
    </xpt-list>
  </div>
</template>

<script>
  export default {
    props:['params'],
    name: 'list',
    components: {},
    data() {
      let self = this;
      return {
        btns: [
          {
            type:'primary',
            txt:'确认',
            click(){
              self.close();
            }
          }
        ],
        cols: [
          {
            label: '预约活动编码',
            prop: 'appointmentActivityNo',
            width: 150
          },
          {
            label: '预约活动名称',
            prop: 'appointmentActivityName',
            width: 180
          },
          {
            label: '预约短信模板',
            slot: 'text',
          }
        ],
        list: [],
        search:{
          page_size:50,   
          page_no:1,
        },
        totalPage: 0,
        selectId:'',
        uploadUrl: '/order-web/api/scmexhibitionmaterial/import'
      }

    },
    watch: {},
    methods: {
      radioChange(data) {
        this.selectData = data;
      },
      close() {
        if (this.selectData.length===0) {
          this.$message.error('请先选择一个批次');
          return;
        }
        let params = {
          appointEndNote: this.params.appointEndNote,
          appointShopNote: this.params.appointShopNote,
          appointment_activity_id: this.params.appointmentActivityId,
          appointSuccessedNote: this.params.appointSuccessedNote,
          appointment_activity_ids: []
        }
        this.selectData.forEach(v=>{
          params.appointment_activity_ids.push(v.appointmentActivityId)
        })
        this.$axios('post', '/crm-web/api/crm_appointment_activity/noteCover',
          params
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      },
      selectChange(data){
        this.selectData = data;
      },
      reset() {
        this.selectId = '';
        this.search={
          page_size:50,
          page_no:1,
        }
        this.getList()
      },
      searchData(obj, resolve) {
        this.search={
          page_size:50,
            page_no:1,
        }
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page_no = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve) {
        this.$request('/crm-web/api/crm_appointment_activity/noteCoverPop',
          {
            appointment_activity_name:this.selectId,
            ...this.search,
          }).then(res => {
          if (res.result) {
            this.list = res.content.list || [];
            this.totalPage = res.content.count;
          }
          resolve && resolve();
        }).catch(err => {
          resolve && resolve();
        }).finally(() => {
          resolve && resolve();
        })
      }
    },
    computed: {},
    created() {
      this.getList();
    },
    mounted() {
    },
    destroyed() {
    }
  }
</script>

<style scoped>
</style>
