<!-- 站外原生线索列表 --> 
<template>
    <xpt-list  
        :data='goodsList' 
        :btns='goodsBtns' 
        :colData='goodsCols' 
        orderNo 
        isNeedClickEvent 
        @selection-change='goodsRadioChange' 
        :pageTotal='goodsCount' 
        :searchPage='goodsQuery.page_name' 
        @search-click='goodsListSearch'
        :selectable='selectableFun'
        @page-size-change='goodsPageSizeChange' 
        @current-page-change='goodsPageChange'>
    </xpt-list>
</template>
<script>
    import fn from '@common/Fn.js'
    export default {
        data() {
            let self = this;
            return {
                goodsList: [],
                goodsBtns: [
                    {
                        type: 'success',
                        txt: '刷新',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.refresh()
                        }
                    }, /*{
                        type: 'primary',
                        txt: '线索修复',
                        disabled: () => {
                            return false;
                        },
                        click() {
                            self.repairClus()
                        }
                    },*/ {
                        type: 'info',
                        txt: '删除',
                        loading: false,
                        click() {
                            self.removeClues()
                        }
                    }
                ],
                goodsCols: [
                    {
                        label: '平台线索ID',
                        prop: 'clue_id',
                        width: 100,
                        redirectClick(row) {
                            let params = {};
                            params.id = row.id;
                            params.currentRow = row;
                            self.$root.eventHandle.$emit('creatTab', {
                                name: "站外原生线索详情",
                                params: params,
                                component: () => import('@components/appoint_management/off-site_native_cues_detail.vue')
                            });
                        },
                    }, {
                        label: '客户ID',
                        prop: 'uid',
                        width: 100,
                        redirectClick(row) {
                            let params = {};
                            params.id = row.id;
                            params.currentRow = row;
                            self.$root.eventHandle.$emit('creatTab', {
                                name: "站外原生线索详情",
                                params: params,
                                component: () => import('@components/appoint_management/off-site_native_cues_detail.vue')
                            });
                        },
                    }, {
                        label: '姓名',
                        prop: 'name',
                        width: 90
                    }, {
                        label: '手机号码',
                        prop: 'phone',
                        format: 'hidePhoneNumber'
                        // bool: true,
                        // elInput: true,
                        // disabled(row) {
                        //     // return self.materiaCanEdit(row)
                        //     return false
                        // },
                        // change(row) {},
                        // blur: (row, e) => {},
                        // width: 100
                    }, {
                        label: '归属地',
                        prop: 'location',
                        // bool: true,
                        // elInput: true,
                        // disabled(row) {
                        //     // return self.materiaCanEdit(row)
                        //     return false
                        // },
                        // change(row) {},
                        // blur: (row, e) => {},
                    }, {
                        label: '线索类型',
                        prop: 'clue_type',
                        formatter(val) {
                        	switch(val) {
                                case 'LEADS_TYPE_FORM': return '表单提交'; break;
                                case 'LEADS_TYPE_ONLINE_CONSULT': return '在线咨询'; break;
                                case 'LEADS_TYPE_PHONE': return '智能电话'; break;
                                case 'LEADS_TYPE_PROMOTION_COUPON': return '发券'; break;
                                case 'LEADS_TYPE_INTELLIGENT_TOOL': return '智能咨询'; break;
                                case 'LEADS_TYPE_LOTTERY': return '抽奖'; break;
                                case '0': return '表单提交'; break;
                                case '1': return '在线咨询'; break;
                                case '2': return '智能电话'; break;
                                case '3': return '网页回呼'; break;
                                case '4': return '卡券'; break;
                                case '5': return '抽奖'; break;
                                case  'APPOINT_FORM': return  '表单提交'; break;
                                case  'CALL_CONSULT': return  '来电咨询'; break;
                        		default: return val; break;
                        	}
                        },
                        // bool: true,
                        // // isThis: true,
                        // isSelect: true,
                        // disabled(row) {
                        //     return self.materiaCanEdit(row)
                        // },
                        // obj: {
                        //     'Y': '是',
                        //     'N': '否',
                        // }
                    }, {
                        label: '线索来源',
                        prop: 'clue_source',
                        formatter(val) {
                        	switch(val) {
                                case 'LEADS_SOURCE_TYPE_OUTER_TRAFFIC': return '外部流量'; break;
                                case 'LEADS_SOURCE_TYPE_INTERNAL_TRAFFIC': return '广告投放'; break;
                                case 'LEADS_SOURCE_TYPE_AD_PREVIEW': return '广告预览'; break;
                                case '0': return  '外部流量'; break;
                                case '1': return  '正常投放'; break;
                                case '2': return  '外部导入'; break;
                                case '3': return  '异常提交'; break;
                                case '4': return  '广告预览'; break;
                                case '5': return  '抖音私信'; break;
                                case '6': return  '鲁班线索'; break;
                        		default: return val; break;
                        	}
                        }
                    }, {
                        label: '线索平台',
                        prop: 'clue_platform',
                        formatter(val) {
                        	switch(val) {
                                case 'FEIYU': return '飞鱼'; break;
                                case 'TOUTIAO': return '今日头条'; break;
                                case 'TENCENT': return '腾讯'; break;
                                case 'TMALL': return '天猫'; break;
                                case 'MEITUAN': return '美团'; break;
                        		default: return val; break;
                        	}
                        }
                    }, {
                        label: '线索渠道',
                        prop: 'clue_channel',
                        // formatter(val) {
                        // 	switch(val) {
                        //         case  'ACCOUNT_TYPE_GDT': return  '广点通'; break;
                        //         case  'ACCOUNT_TYPE_WECHAT': return  '微信'; break;
                        // 		default: return val; break;
                        // 	}
                        // }
                    }, {
                        label: '备注信息',
                        prop: 'remark'
                    }, {
                        label: '推广计划ID',
                        prop: 'act_id',
                        // bool: true,
                        // elInput: true,
                        // disabled(row) {
                        //     return false
                        // },
                        // change(row) {},
                        // blur: (row, e) => {},
                        // width: 100
                    }, {
                        label: '线索获取时间',
                        prop: 'clue_pull_time',
                        width: 130,
                        format: 'dataFormat1'
                    }, {
                        label: '是否存在缺陷',
                        prop: 'clue_is_defect',
                        width: 130,
                        formatter(val) {
                        	switch(val) {
                        		case 0: return '否'; break;
                        		case 1: return '是'; break;
                        		default: return val; break;
                        	}
                        }
                    }, {
                        label: '线索广告主',
                        prop: 'clue_act_name'
                    }, {
                        label: '线索提交时间',
                        prop: 'clue_order_time',
                        format: 'dataFormat1',
                        width: 130,
                        // bool: true,
                        // elDate: true,
                        // date: 'datetime',
                        // pickerOptions: {
                        //     disabledDate(time) {
                        //         return false
                        //     }
                        // },
                        // disabled(row) {
                        //     return false
                        // },
                        // change(row) {},
                    }, {
                        label: '线索更新日期',
                        prop: 'clue_update_time',
                        width: 130,
                        format: 'dataFormat1'
                    }, {
                        label: '推广计划名称',
                        prop: 'act_name',
                        width: 90
                    }, {
                        label: '推广链接',
                        prop: 'act_url'
                    }, {
                        label: '同步状态',
                        prop: 'synchronoused',
                        formatter(val) {
                        	switch(val) {
                                case 0: return '未同步'; break;
                                case 1: return '已同步'; break;
                        		default: return val; break;
                        	}
                        },
                    }, {
                        label: '同步信息',
                        prop: 'sync_remark'
                    }
                ],
                goodsSelect: [],
                goodsCount: 0,
                goodsQuery: {
                    page_name: "crm_station_interface",
                    where: [],
                    page_size: 50,
                    page_no: 1,
                    // 是否过滤,1为不过滤，不传则默认过滤
                    if_need_page: 'Y'
                },
                ifSave: false
            }
        },
        methods: {
            // 物料是否可选
			selectableFun(row){
				// if(row.clue_is_defect == '0') return false;
				return true;
			},
            // 删除线索
            removeClues() {
                let postData = []
                this.goodsSelect.forEach(item => {
                    postData.push(item.id)
                })
                this.ajax.postStream('/crm-web/api/crm_station_interface/delete', postData, d => {
                    if (d.body.result) {
                        this.$message.success(d.body.msg)
                        this.getGoodsList()
                    } else {
                        this.$message.error(d.body.msg)
                    }
                }, (e) => {
                    this.$message.error(e)
                })
            },
            // 线索修复
            repairClus() {
                let postData = {}
                this.ajax.postStream('/crm-web/api/crm_station_interface/update', postData, d => {
                    if (d.body.result && d.body.content) {
                        this.getGoodsList()
                        this.$message.success(d.body.msg)
                    } else {
                        this.$message.error(d.body.msg)
                    }
                }, (e) => {
                    this.$message.error(e)
                })
            },
            refresh() {
                this.getGoodsList()
            },
            // 行选中
            goodsRadioChange(row) {
                this.goodsSelect = row;
            },
            // 获取商品信息
            getGoodsList(resolve, ifPromiss) {
                let self = this;
                let data = JSON.parse(JSON.stringify(this.goodsQuery));
                let url = '/crm-web/api/crm_station_interface/list'
                this.ajax.postStream(url, data, d => {
                    if (d.body.result && d.body.content) {
                        this.goodsList = d.body.content.list
                        this.goodsCount = d.body.content.count
                        this.$message.success(d.body.msg)
                    } else {
                        this.$message.error(d.body.msg)
                    }
                    resolve && resolve();
                }, (e) => {
                    this.$message.error(e);
                    resolve && resolve();
                })
            },
            //通用查询搜索
            goodsListSearch(where, reslove) {
                this.goodsQuery.where = where
                this.getGoodsList(reslove)
            },
            // 当前页改变
            goodsPageSizeChange(ps) {
                this.goodsQuery.page_size = ps;
                this.getGoodsList();
            },
            // 当前页面显示行数改变
            goodsPageChange(page) {
                this.goodsQuery.page_no = page;
                this.getGoodsList();
            }
        },
        computed: {},
        watch: {},
        mounted() {
            this.getGoodsList()
        }
    }
</script>
<style type="text/css">
    .el-dialog__wrapper {
        background-color: transparent
    }
</style>