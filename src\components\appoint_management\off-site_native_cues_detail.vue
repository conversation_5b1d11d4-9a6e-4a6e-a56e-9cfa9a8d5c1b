
<!-- 站外原生线索详情-->
<template>
<div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
		<el-col :span="24">
			<el-button 
				:type='btn.type' 
				size='mini' 
				v-for='(btn, index) in topBtns' 
				:key='"good" + index'
				:disabled=" typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled|| false "
				:loading="btn.loading||false" 
				@click='btn.click'
			>{{btn.txt}}</el-button>
		</el-col>
	</el-row>
	<div>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基本信息" name="offSite_info">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="offSite" :rules="rules" ref="offSite">
						<el-col :span='6'>
							<el-form-item label="平台线索ID：" prop="clue_id">
                                <el-input readonly size='mini' v-model='offSite.clue_id' disabled ></el-input>
                            </el-form-item>
                            <el-form-item label="客户ID：" prop="uid">
                                <el-input readonly size='mini' v-model='offSite.uid' disabled ></el-input>
                            </el-form-item>
                            <el-form-item label="姓名：" prop="name">
								<el-input v-model="offSite.name" prop="name" disabled size='mini' ></el-input>
							</el-form-item>
							<el-form-item label="手机号码：" prop="phone" required>
								<!-- <el-input v-model="offSite.phone" size='mini'></el-input> -->
                                <xpt-eye-switch v-model="offSite.phone" :aboutNumber="offSite.act_id"></xpt-eye-switch>
                                <el-tooltip v-if='rules.phone[0].isShow' class="item" effect="dark" :content="rules.phone[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
							</el-form-item>
                            <el-form-item label="归属地：" prop="location">
                                <el-input v-model="offSite.location" size='mini'></el-input>
                                <el-tooltip v-if='rules.location[0].isShow' class="item" effect="dark" :content="rules.location[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </el-form-item>
                            <el-form-item label="线索提交时间："  prop="clue_order_time">
								<el-date-picker v-model="offSite.clue_order_time" type="datetime" placeholder="选择日期" size='mini' :editable="offSite.clue_is_defect == '0' ? true : false"  :picker-options='enableDateOptions'></el-date-picker>
								<el-tooltip v-if='rules.clue_order_time[0].isShow' class="item" effect="dark" :content="rules.clue_order_time[0].message" placement="right-start" popper-class='xpt-form__error'>
                 					  <i class='el-icon-warning'></i>
                 				 </el-tooltip>
							</el-form-item>
                            <el-form-item label="线索获取时间：" prop="clue_pull_time">
								<el-date-picker v-model="offSite.clue_pull_time" type="date" placeholder="选择日期" size='mini' disabled :editable='false'></el-date-picker>
							</el-form-item>
                            <el-form-item label="线索更新时间：" prop="clue_update_time">
								<el-date-picker v-model="offSite.clue_update_time" type="date" placeholder="选择日期" size='mini' :disabled='true' :editable='false'></el-date-picker>
							</el-form-item>
                            
						</el-col>
						<el-col :span='6'>
                            <el-form-item label="是否存在缺陷：" prop="clue_is_defect">
                                <el-select placeholder="请选择" size='mini' v-model="offSite.clue_is_defect" disabled>
                                    <el-option label="否" :value='0'></el-option>
                                    <el-option label="是" :value='1'></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="线索类型：" prop="clue_type">
                                <el-select placeholder="请选择" size='mini' v-model="offSite.clue_type" v-if="offSite.clue_platform == 'FEIYU'">
                                    <el-option v-for="item in flyFishClueType" :key="'ClueType' + item.value" :label="item.label" :value='item.value'></el-option>
                                </el-select>
                                <el-select placeholder="请选择" size='mini' v-model="offSite.clue_type" v-else-if="offSite.clue_platform == 'TENCENT'">
                                    <el-option v-for="item in tencentClueType" :key="item.value" :label="item.label" :value='item.value'></el-option>
                                </el-select>
                                <el-select placeholder="请选择" size='mini' v-model="offSite.clue_type" v-else-if="offSite.clue_platform == 'TMALL'">
                                    <el-option v-for="item in tmallClueType" :key="item.value" :label="item.label" :value='item.value'></el-option>
                                </el-select>
                                    <!-- <el-input size='mini' v-model="offSite.clue_type"  v-else-if="offSite.clue_platform == 'YUNQUE'" disabled></el-input> -->
                                <el-select placeholder="请选择" size='mini' v-model="offSite.clue_type" v-if="offSite.clue_platform == 'YUNQUE'" disabled>
                                    <el-option v-for="item in tencentClueType" :key="'ClueType' + item.value" :label="item.label" :value='item.value'></el-option>
                                </el-select>

                                <el-tooltip v-if='rules.clue_type[0].isShow' class="item" effect="dark" :content="rules.clue_type[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </el-form-item>
                            <el-form-item label="线索平台："  prop="clue_platform">
                                <el-select placeholder="请选择" size='mini' v-model="offSite.clue_platform" disabled>
                                    <el-option v-for="item in cluePlatformList" :key="'clue_source' + item.value" :label="item.label" :value='item.value'></el-option>
                                </el-select>
							</el-form-item>
                            <el-form-item label="线索渠道：" prop="clue_channel">
                                <el-input size='mini' v-model="offSite.clue_channel" disabled></el-input>
                            </el-form-item>
							<el-form-item label="线索来源："  prop="clue_source">
                                <el-select placeholder="请选择" size='mini' v-model="offSite.clue_source" disabled>
                                    <el-option v-for="item in clueSourceList" :key="'clue_source' + item.value" :label="item.label" :value='item.value'></el-option>
                                </el-select>
							</el-form-item>
                            <el-form-item label="同步状态：" prop="synchronoused">
                                <el-select placeholder="请选择" size='mini' v-model="offSite.synchronoused" disabled>
                                    <el-option label="未同步" :value='0'></el-option>
                                    <el-option label="已同步" :value='1'></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="同步信息：" prop="sync_remark">
                                <el-input size='mini' v-model="offSite.sync_remark" disabled></el-input>
                            </el-form-item>
                            
                            
						</el-col>
						<el-col :span='6'>
                            <el-form-item label="备注信息：" prop="remark">
                                <el-input size='mini' v-model="offSite.remark" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="推广计划ID：" prop="act_id" required>
           						<xpt-input size='mini' v-model="offSite.act_id"></xpt-input>
                                <el-tooltip v-if='rules.act_id[0].isShow' class="item" effect="dark" :content="rules.act_id[0].message" placement="right-start" popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
          					</el-form-item>
                            <el-form-item label="推广计划名称：" prop="act_name">
								<el-input v-model="offSite.act_name" size='mini' disabled></el-input>
							</el-form-item>
                            <el-form-item label="推广链接：" prop="act_url">
								<el-input v-model="offSite.act_url" size='mini' disabled ></el-input>
							</el-form-item>
                            <el-form-item label="线索广告主：" prop="clue_act_name">
                                <el-input size='mini' v-model="offSite.clue_act_name" disabled></el-input>
                            </el-form-item>
						</el-col>
			    	</el-form>
			 	 </el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</div>
</template>
<script>
	import VL from '@common/validate.js';
	export default {
		props: ['params'], //上游参数
		data() {
			var self = this;//本vue
			return{
				firstTab:"offSite_info",
                bill_type_id_tag: '',
				offSite:{
                    clue_id: '',
                    phone: '',
                    location: '',
                    name: '',
                    clue_is_defect: '',
                    clue_type: '',
                    clue_update_time: '',
                    clue_source: '',
                    clue_order_time: '',
                    act_id: '',
                    act_name: '',
                    act_url: '',
                    clue_pull_time: '',
                    remark: '',
                },//表单内容
                enableDateOptions:{
                    /*生效时间允许选所有值*/
                    disabledDate(time){
                        return false
                    }
                },
				rules:{
                    ...[
                        {
                            phone: '手机号码'
                        }, {
                            location: '归属地'
                        }, {
                            clue_order_time: '线索提交时间'
                        }, {
                            clue_type: '线索类型'
                        }, {
                            act_id: '推广计划ID'
                        }
                    ].reduce((a, b) => {
                        var key = Object.keys(b)[0]
                        a[key] = VL.isNotBlank({
                            self: this,
                            msg: '请填写' + b[key],
                        })
                        return a
                    }, {}),
                    phone: VL.mobile({
                        self:self,
                        trigger:'change',
                        msg:'请填写正确的手机号码'
                    }),
                    act_id: VL.isNumber({
                        self:self,
                        trigger:'change',
                        msg: '请填写数字'
					})
				},
				topBtns: [
					{ 
						type: 'primary', 
						txt: '刷新', 
						disabled () {
                            return self.params.id ? false : true
                        },
						click () {
                            self.getInfo()
                        } 
					}, { 
						type: 'warning', 
						txt: '线索修复', 
						disabled: false, 
						click: self.repairClus 
					}
                ],
                tencentClueType: [
                    {value: 'LEADS_TYPE_FORM', label: '表单提交'},
                    {value: 'LEADS_TYPE_ONLINE_CONSULT', label: '在线咨询'},
                    {value: 'LEADS_TYPE_PHONE', label: '智能电话'},
                    // {value: 'LEADS_TYPE_PROMOTION_COUPON', label: '发券'},
                    // {value: 'LEADS_TYPE_INTELLIGENT_TOOL', label: '智能咨询'},
                    // {value: 'LEADS_TYPE_LOTTERY', label: '抽奖'},
                ],
                tmallClueType: [
                    {value: 'APPOINT_FORM', label: '表单提交'},
                    // {value: 'CALL_CONSULT', label: '来电咨询'},
                ],
                flyFishClueType: [
                    {value: '0', label: '表单提交'},
                    {value: '1', label: '在线咨询'},
                    {value: '2', label: '智能电话'},
                    // {value: '3', label: '网页回呼'},
                    // {value: '4', label: '卡券'},
                    // {value: '5', label: '抽奖'},
                ],
                clueSourceList: [
                    {value: 'LEADS_SOURCE_TYPE_OUTER_TRAFFIC', label:'外部流量'},
                    {value: 'LEADS_SOURCE_TYPE_INTERNAL_TRAFFIC', label:'广告投放'},
                    {value: 'LEADS_SOURCE_TYPE_AD_PREVIEW', label:'广告预览'},
                    {value: '0', label: '外部流量'},
                    {value: '1', label: '正常投放'},
                    {value: '2', label: '外部导入'},
                    {value: '3', label: '异常提交'},
                    {value: '4', label: '广告预览'},
                    {value: '5', label: '抖音私信'},
                    {value: '6', label: '鲁班线索'},
                ],
                cluePlatformList: [
                    {value: 'FEIYU', label: '飞鱼'},
                    {value: 'TOUTIAO', label: '今日头条'},
                    {value: 'TENCENT', label: '腾讯'},
                    {value: 'TMALL', label: '天猫'},
                    {value: 'MEITUAN', label: '美团'},
                ]
			}
		},
		methods:{
            repairClus () {
                let self = this
                self.$refs.offSite.validate((valid) => {
					if(!valid) return
					this.ajax.postStream('/crm-web/api/crm_station_interface/update', [this.offSite], d => {
                        if (d.body.result && d.body.content) {
                            self.getInfo()
                            self.$message.success(d.body.msg)
                        } else {
                            self.$message.error(d.body.msg)
                        }
                    }, (e) => {
                        self.$message.error(e)
                    })
				})
            },
			save(formName){
				var self =  this;
				let saveData = JSON.parse(JSON.stringify(self.offSite));
				self.$refs.offSite.VL((valid) => {
					if(!valid) return
					this.ajax.postStream('/material-web/api/change_sale/save',saveData,function(response){
						if(response.body.result) {
							self.$message.success(response.body.msg)
                            self.offSite.changeSaleId = response.body.content.changeSaleId
							self.getInfo();
						} else {
							self.$message.error(response.body.msg || '')
						}
					},err => {
                        self.$message.error(err)
                    });
					
				})
				
			},
			//获取详情信息
			getInfo(resolve){
				let id = this.params.id ? this.params.id : this.offSite.id, self = this
				if(id){
					this.ajax.postStream('/crm-web/api/crm_station_interface/find',id,function(response){
						if(response.body.result && response.body.content) {
                            self.offSite = response.body.content || {};
                            if(!!self.offSite.remark){
                                self.rules.location[0].required=false;
                                self.rules.location[0].validator=(rule, value, callback)=>{
                                    callback();
                                };  
                            }
							resolve && resolve();
						} else {
							self.$message.error(response.body.msg || '');
						}
					});
				}
            },
		},
		mounted: function(){
            this.getInfo()
            // this.offSite = this.params.currentRow
        },
        watch: {}
	}
</script>
<style module>
.row-height :global(.el-form-item__content) {
	height: auto!important;
	white-space: nowrap;
}
.mgb5 {
    margin-bottom: 5px;
}
</style>