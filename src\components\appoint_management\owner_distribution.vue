//店长分配列表
<template>
  <xpt-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :selection="selection"
    :orderNo="true"
    :pageTotal='pageTotal'
    :searchPage='search.page_name'
    @search-click='searchClick'
    @selection-change='select'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
  > 
    <template slot="customerBehavior" slot-scope="scope">
        <el-button type="text" size="small" @click="handleGotoPerformance(scope)">查看</el-button>
    </template>
    </xpt-list>
</template>

<script>
    export default {
      name: "owner_distribution",
      data(){
        let self = this;
        return {
          selection: "checkbox",
          dataList: [],
          pageTotal: 0,
          pageSize: 50,
          pageNow: 1,
          selectRow:[],
          btns: [
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.refresh();
              },
            },
            {
              type: "primary",
              txt: "分配",
              loading: false,
              disabled: () =>{
                return (self.shopCodeList && self.shopCodeList.length == 1  && self.shopCodeList[0].autoDistribution == 1)
              },
              click() {
                self.distributeList();
              },
            }, {
              type: "success",
              txt: "导出",
              loading: false,
              click() {
                self.exportExcel();
              },
            }, {
              type: "info",
              txt: "报表导出文件下载",
              loading: false,
              click() {
                self.showExportList();
              },
            }, {
              type: "primary",
              txt: "自动分配",
              loading: false,
              disabled: () =>{
                return !self.shopCodeList || self.shopCodeList.length > 1
              },
              click() {
                self.shopAdminAssignShopGuide();
              },
            },
          ],
          cols:[
            {
              label: "姓名",
              prop: "name",
            },
            {
              label: "手机号码",
              prop: "mobile",
              format: 'hidePhoneNumber',
              width:90
            },
            {
                label:'客户行为表现',
                slot:'customerBehavior',
                width: 90,
            },
            {
              label: "预约活动编码",
              prop: "appointment_question_no",
              width:115
            },
            // {
            //   label: "预约提交时间",
            //   prop: "appointment_submit_time",
            //   format: "dataFormat1" ,
            //   width:130
            // },
            {
                label: '事业部',
                width:110,
                format: "auxFormat",
                formatParams: "business_division",
                prop: 'business_division'
            },
            {
              label: "店铺地区",
              prop: "shop_area",
              width:120
            },
            {
                label: '留资类型',
                prop: 'information_type',
                formatter(val) {
                    switch(val) {
                        case 'APPOINT_FORM': return '预约表单'; break;
                        case 'CALL_CONSULT': return '来电咨询'; break;
                        case 'ONLINE_CONSULTATION': return '在线咨询'; break;
                        default: return val; break;
                    }
                }
            }, {
                label: '来源平台',
                prop: 'information_platform_name',
                width: 90,
            }, {
                label: "来源渠道",
                prop: "appointment_channel_name",
                width: 90,
            }, {
                label: '状态',
                prop: 'status',
                formatter(val) {
                    switch(val) {
                        case 'WATING': return '待跟进'; break;
                        case 'FOLLOWING': return '跟进中'; break;
                        case 'FOLLOWED': return '已跟进'; break;
                        case 'ASSIGNED': return '已分配'; break;
                        case 'FINISHED': return '已完成'; break;
                        case 'DISABLED': return '已失效'; break;
                        default: return val; break;
                    }
                }
            },
            {
              label: "预约发送店铺",
              prop: "appointment_send_shop_name",
            },
            {
              label: "优先分配",
              prop: "if_urgent",
              formatter(val){
                switch (val) {
                  case "Y" : return "是";
                  case "N" : return "否";
                }
              }
            },
            {
              label: "话务跟进标签",
              prop: "tel_operator_tag",
              width: 86,
              format: 'auxFormat',
              formatParams: 'appointment_tel_operator_tag'
            },
            {
              label: "话务跟进结果",
              prop: "tel_operator_mark_requirement",
              width: 130,
            },
            // {
            //   label: "预计到店时间",
            //   prop: "tel_operator_mark_arrival",
            //   width: 130,
            // },
            {
              label: "话务完成时间",
              prop: "tel_operator_confirm_time",
              format: "dataFormat1" ,
              width: 130,
            },
            {
              label: "线索生成时间",
              prop: "tel_operator_confirm_time",
              format: "dataFormat1" ,
              width: 130,
            },
          ],
          search:{
            page_name:"crm_appointment_tracing_shop_assign",
            where:[],
            page:{
              length:50,
              pageNo:1
            }
          },
          shopCodeList: []
        }
      },
      methods:{
        handleGotoPerformance(params){
                params.isReservation = false;
                this.$root.eventHandle.$emit("creatTab", {
                    name: "客户行为表现",
                    params,
                    component: () => import("@components/appoint_management/customer_behavior_performance.vue"),
                });
            },
        // 自动分配
        shopAdminAssignShopGuide() {
          let self = this
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/appoint_management/adminAssignShopGuide.vue'),
            style: 'width:800px;height:500px',
            title: "自动分配规则配置",
            params: {
              appointment_send_shop_no: self.shopCodeList[0].shopCode,
              autoDistribution: self.shopCodeList[0].autoDistribution || 1,
              //刷新分配列表
              callback: data => {
                self.getList('')
              }
            }
          });
        },
        getList(resolve){
          this.btns[0].loading = true;
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getShopAssignList',this.search,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list.list || [];
              this.pageTotal = res.body.content.count;
              this.shopCodeList = res.body.content.list.shopCodeList
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        distributeList() {
          let self = this,
            ids = [],
            shops = [],
            shop_no = "",
            count=0;
          this.selectRow.find(d=>{
            ids.push(d.id);
            shops.push(d.appointment_send_shop_no);
          });
          if(!ids.length){
            self.$message({
              message:'请选择要分配的客户！',
              type:'error'
            });
            return;
          }
          //判断所选数据的预约发送店铺是否一致
          shop_no = shops[0];
          if (shops.length > 1) {
            for (let i=1; i < shops.length; i++){
              if (shop_no !== shops[i]){
                count++;
              }
            }
            if (count > 0){
              self.$message.error("请选择预约发送店铺一致的数据进行批量分配！");
              return;
            }
          }
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/appoint_management/owner_distribution_list.vue'),
            style: 'width:800px;height:500px',
            title: "导购员列表",
            params: {
              customer:ids,
              appointment_send_shop_no:shop_no,
              //刷新分配列表
              callback: data => {
                this.getList();
              }
            }
          });
        },
        refresh(){
          this.getList();
        },
        // 多选事件
        select(s){
          this.selectRow = s;
        },
        searchClick(list,resolve){
          this.search.where = list;
          this.getList(resolve);
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page.length = pageSize;
          this.getList();
        },
        // 监听页数更改事件
        pageChange(page){
          this.search.page.pageNo  = page;
          this.getList();
        },
        // 导出
        exportExcel () {
          this.btns[2].loading = true;
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/exportShopAssignList',this.search,res => {
            if(res.body.result) {
              res.body.msg && this.$message.success(res.body.msg)
            } else {
              res.body.msg && this.$message.error(res.body.msg)
            }
            this.btns[2].loading = false
          }, err => {
            this.btns[2].loading = false
            this.$message.error(err)
          })
        },
        showExportList (){
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/after_sales_report/export'),
            style:'width:900px;height:600px',
            title: '报表导出列表',
            params: {
              query: {
              type: 'EXCEL_TYPE_CRM_APPOINTMENT_TRACING_SHOP_ASSIGN',
              },
            },
          })
        }
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
