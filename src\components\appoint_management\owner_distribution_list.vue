//导购员列表
<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='success' size='mini' @click='fresh' slot='left'>刷新</el-button>
      <el-button type='primary' size='mini' @click='confirm' slot='left'>确认分配</el-button>
      <el-button type='primary' size='mini' @click='change' slot='left'>状态调整</el-button>
      <el-input
        placeholder="输入用户名或昵称"
        icon="search"
        size='mini'
        v-model="search.key"
        :on-icon-click="searchClick"
        slot='right'
        @keyup.enter.native="searchClick"
      ></el-input>
    </xpt-headbar>
    <xpt-list
      :data="dataList"
      :colData="cols"
      :showHead="false"
      :pageTotal='pageTotal'
      :selection="selection"
      @page-size-change='pageChange'
      @current-page-change='currentPageChange'
      @radio-change='select'
    ></xpt-list>
  </div>
</template>

<script>
    export default {
      name: "owner_distribution_list",
      props:['params'],
      data() {
        let self = this;
        return {
          selection:"radio",
          selectRow:{},
          cols: [
            {
              label: "用户账户",
              prop: "employee_number",
            },
            {
              label: "用户名称",
              prop: "real_name",
            },
            {
              label: "昵称",
              prop: "nick_name",
            },
            {
              label: "状态",
              prop: "on_line",
              formatter:(val)=>{
                return val==1?"上线":"下线"
              }
            },
            {
              label:'生效',
              prop:'status',
              format:'statusFilter'
            },
          ],
          dataList: [],
          search: {
            key : ""
          },
          pageTotal:0,
          pageSize:50,
          pageNow:1,
        }
      },
      methods: {
        // 状态调整
        change(){
          let self = this;
          if(this.selectRow.guideId === ""){
            self.$message({
              message:'请选择导购员！',
              type:'error'
            });
            return;
          }
          console.log(this.selectRow)
          let params={
            online:this.selectRow.on_line==1?0:1,
            employeeNumber:[this.selectRow.employee_number]
          }
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/onlineChange',params,res => {
            if(res.body.result){
              res.body.msg && this.$message.success(res.body.msg);
              this.fresh()
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        getList(){
          let _this = this;
          let params = {
            key: this.search.key,
            appointment_send_shop_no: this.params.appointment_send_shop_no,
            page:{
              length:this.pageSize,
              pageNo:this.pageNow,
            }
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getShopGuideList',params,res => {
            if(res.body.result){
              this.dataList = res.body.content.list;
              this.pageTotal = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        fresh() {
          this.reset();
          this.getList();
        },
        reset(){
          this.search.key = "";
          this.selectRow = {};
        },
        searchClick() {
          this.getList();
        },
        confirm() {
          //确认分配
          let self = this;
          if(!Object.keys(this.selectRow).length){
            self.$message({
              message:'请选择导购员！',
              type:'error'
            });
            return;
          }
        //   if(this.selectRow.on_line==0){
        //     self.$message({
        //       message:'此导购已下线，请选择其他导购',
        //       type:'error'
        //     });
        //     return
        //   }
          let data = {
            ids:this.params.customer,
            shop_guide_id:this.selectRow.id,
            shop_guide_name:this.selectRow.real_name,
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/shopAdminAssignShopGuide',data,res => {
            if(res.body.result){
              this.$message.success(res.body.msg);
              //关闭弹窗
              this.params.callback();
              this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        // 选择事件
        select(s){
          //获取导购id及name
          this.selectRow=s
        },
        // 监听每页显示数更改事件
        pageChange(pageSize){
          this.pageSize = pageSize;
          this.pageNow = 1;
          this.getList();
        },
        // 监听页数更改事件
        currentPageChange(page){
          this.pageNow = page;
          this.getList();
        },
        searchClick() {
          this.getList();
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
