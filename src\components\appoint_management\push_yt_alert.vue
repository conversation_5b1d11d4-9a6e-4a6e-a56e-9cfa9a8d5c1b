<!--品质反馈详情弹框-->
<template>
  <div style="height: 100%">
    <div class="xpt-flex">
      <div class="xpt-top">
        <el-button type="info" size="mini" @click="sure">确定</el-button>
      </div>
      <div class="xpt-flex__bottom">
        <el-form :model="submitData" ref="submitData" :rules="rules">
          <el-row class="mgt20" :gutter="40">
            <el-col :span="18">
              <el-form-item
                label="话务跟进标签："
                label-width="120px"
                prop="tel_operator_tag"
                required
              >
                <el-select
                  placeholder="请选择"
                  size="mini"
                  v-model="submitData.tel_operator_tag"
                  @change="tagChange"
                >
                  <el-option
                    v-for="(item, idx) in telOperatorTagList"
                    :label="item.name"
                    :key="idx"
                    :value="item.code"
                    :disabled="
                      /^(TEL_FOLLOW_TWICE|SYSTEM_PUSH)$/.test(item.code) ||
                      item.status === 0 ||
                      !['ALL',mainSaleCategorie].includes(item.tag)
                    "
                  ></el-option>
                </el-select>
                <el-tooltip
                  v-if="rules.tel_operator_tag[0].isShow"
                  class="item"
                  effect="dark"
                  :content="rules.tel_operator_tag[0].message"
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="预约活动发送店铺：" label-width="120px"
                prop="appointment_send_shop_activity_name" required
                >
                <el-input
                  v-model="submitData.appointment_send_shop_activity_name"
                  size="mini"
                  readonly
                  icon="search"
                  :on-icon-click="selectShop"
                ></el-input>
                <el-tooltip
                  v-if="rules.appointment_send_shop_activity_name[0].isShow"
                  class="item"
                  effect="dark"
                  :content="
                    rules.appointment_send_shop_activity_name[0].message
                  "
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item
                label="话务备注："
                prop="tel_remark"
                label-width="120px"
              >
                <el-input
                  type="textarea"
                  placeholder="请输入内容"
                  v-model="submitData.tel_remark"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  :maxlength="500"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      mainSaleCategorie:'',
        submitData:{
            tel_operator_tag:'',
            appointment_send_shop_activity_name:'',
            appointment_send_shop_activity_no:'',
            tel_remark:'',
        },
      telOperatorTagList: __AUX
        .get("appointment_tel_operator_tag")
        .reduce((newObj, obj) => {
          newObj.push(obj);
          return newObj;
        }, []),
      rules: {
        tel_operator_tag: validate.isNotBlank({
          self: self,
        }),
        appointment_send_shop_activity_name: validate.isNotBlank({
          self: self,
        }),
      },
    };
  },
  created() {
    console.log(this.params);
    this.mainSaleCategorie=this.params.main_sale_categorie;
  },
  mounted() {},
  computed: {},
  methods: {
      sure(){
        this.$refs.submitData.validate((valid) => {
                if (!valid) return;
                this.params.callback && this.params.callback(this.submitData);
                this.$root.eventHandle.$emit(
                    "removeAlert",
                    this.params.alertId
                );
            });
      },
      selectShop(){
          this.$root.eventHandle.$emit('alert', {
            params: {
                callback: data => {
                    this.submitData.appointment_send_shop_activity_name = data.shop_activity_name;
                    this.submitData.appointment_send_shop_activity_no = data.shop_code;
                },
            },
            component: ()=>import('@components/appoint_management/appoint_shop_list'),
                style: 'width:800px;height:500px',
                title: '店铺列表',
        })
      },
      tagChange(){
        let tagList = ['TEL_NO_INTENTION']
        let tagObj = {
          // 'TEL_FOLLOW_TWICE': '门店二次跟进',
          'TEL_NO_INTENTION': '电话暂时无法接通，需要导购再次跟进',
          // 'SYSTEM_PUSH': '系统直推，需要导购马上跟进'
        }
        if(tagList.includes(this.submitData.tel_operator_tag)){
          this.submitData.tel_remark=tagObj[this.submitData.tel_operator_tag]
        }else{
          this.submitData.tel_remark=""
        }
      }
  },
};
</script>
<style scope>
.el-form {
  white-space: nowrap;
}
.mgt15 {
  margin-top: 15px;
}
.seeFileList {
  position: absolute;
  bottom: 0;
  left: 10px;
  right: 10px;
  top: 0px;
  width: auto;
  min-width: auto;
  padding: 10px 0px;
}
.el-table .el-table__body-wrapper td .cell {
  height: auto;
}
.el-table__empty-block {
  width: auto !important;
}
</style>