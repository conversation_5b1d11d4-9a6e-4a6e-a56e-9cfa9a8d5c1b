<template>
  <div class="reservation-relational-data xpt-flex">
    <div>
      <h2 class="h2">导出预约关系数据</h2>
      <div class="search-item">
        <span class="item-title">预约提交时间</span>
        <el-date-picker
          v-model="search.begin_date"
          type="datetime"
          placeholder="开始时间"
          size="mini"
          :picker-options="enableDateOptions"
          class="mr10"
        ></el-date-picker>
        <el-date-picker
          v-model="search.end_date"
          type="datetime"
          placeholder="结束时间"
          size="mini"
          :picker-options="disableDateOptions"
        ></el-date-picker>
      </div>
      <div class="search-item">
        <span class="item-title">留资过滤</span>
        <xpt-search-ex
          class="reservation-relational-data-search"
          :page="search.page_name"
          :click="searchClicks"
          ref="xptSearchEx"
        ></xpt-search-ex>
      </div>
      <div class="search-item">
        <span class="item-title">导出数据类型</span>
        <el-checkbox-group v-model="search.exportType">
          <el-checkbox :label="1">预约数据跟踪</el-checkbox>
          <el-checkbox :label="2">预约接待数据</el-checkbox>
          <el-checkbox :label="3">成品预约成交数据</el-checkbox>
          <el-checkbox :label="4">定制预约成交数据</el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="search-item">
        <span class="item-title">规则说明</span>
        <el-button type="text" size="small" @click="handleExamine"
          >查看</el-button
        >
      </div>
      <el-button
        size="small"
        type="primary"
        class="import-button"
        @click="handleBeginExport"
        :loading="exportLoading"
        >开始导出</el-button
      >
    </div>

    <div class="xpt-flex">
      <h2 class="h2">导出结果</h2>
      <xpt-headbar>
        <el-button
          type="primary"
          size="mini"
          @click="getList"
          slot="left"
          :loading="onRefresh"
          :disabled="onRefresh"
          >刷新</el-button
        >
      </xpt-headbar>
      <xpt-list
        :data="list"
        :colData="cols"
        :showHead="false"
        :pageTotal="count"
        selection=""
        @page-size-change="pageSizeChange"
        @current-page-change="pageChange"
      >
        <template slot-scope="scope" slot="file_path">
          <span>{{ getFilePath(scope.row) }}</span>
        </template>
      </xpt-list>
    </div>

    <ruleExplain ref="ruleExplain"></ruleExplain>
  </div>
</template>

<script>
import ruleExplain from "@/components/appoint_management/components/rule_explain";
export default {
  components: {
    ruleExplain,
  },
  data() {
    var self = this;
    return {
      list: [],
      cols: [
        {
          label: "提交时间",
          prop: "submit_time",
          format: "dataFormat1",
          width: "130",
        },
        {
          label: "导出数据类型",
          prop: "program_name",
        },
        {
          label: "文件名",
          slot: "file_path",
        },
        {
          label: "生成状态",
          prop: "run_status",
          formatter: self.statusExchage,
        },
        {
          label: "提交人",
          prop: "submit_man",
        },
        {
          label: "操作",
          prop: "file_path",
          html(val) {
            return val ? '<a href="' + val + '">下载</a>' : "--";
          },
        },
      ],
      count: 0,
      page_size: 50,
      page_no: 1,
      onRefresh: false,

      search: {
        page_name: "crm_appointment_station",
        where: [],
        exportType: [],
        if_need_page: "Y",
        begin_date: "",
        end_date: "",
      },
      exportLoading: false,

      enableDateOptions: {
        /*生效时间允许选所有值*/
        disabledDate(time) {
          return self.search.end_date
            ? time.getTime() > new Date(self.search.end_date)
            : false;
        },
        date: (function () {
          let date = new Date();
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let time = year + "-" + month + "-" + day + " " + "00:00:00";
          return new Date(time);
        })(),
      },
      disableDateOptions: {
        disabledDate: (time) => {
          /*新增时能选所有时间*/
          return self.search.begin_date
            ? time.getTime() < new Date(self.search.begin_date)
            : false;
        },
        date: (function () {
          let date = new Date();
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let time = year + "-" + month + "-" + day + " " + "23:59:59";
          return new Date(time);
        })(),
      },
    };
  },
  computed: {
    getFilePath() {
      return (row) => {
        let { file_path, result } = row;
        if (!file_path) return file_path || result;
        let arr = file_path.split("/");
        return arr[arr.length - 1].split(".")[0];
      };
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleBeginExport() {
      if (!this.search.begin_date || !this.search.end_date) {
        this.$message.error("预约提交时间不能为空");
        return;
      }
      if (!this.search.exportType.length) {
        this.$message.error("导出数据类型不能为空");
        return;
      }
      this.$refs.xptSearchEx.search();
    },

    searchClicks(e) {
      if (!e.length || !e[0].value) {
        this.search.where = [];
      } else {
        this.search.where = e;
      }
      this.export();
    },

    export() {
      this.exportLoading = true;
      this.ajax.postStream(
        "/crm-web/api/crm_customer_action/export?permissionCode=CRM_CUSTOMER_ACTION_EXPORT",
        this.search,
        (res) => {
          if (res.body.result) {
            res.body.msg && this.$message.success(res.body.msg);
            this.getList();
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
          this.exportLoading = false;
        },
        (err) => {
          this.$message.error(err);
          this.exportLoading = false;
        }
      );
    },

    handleExamine() {
      this.$refs.ruleExplain.open();
    },

    statusExchage(val) {
      let keyValues = {
        EXEC_WAIT: "等待",
        EXEC_RUNING: "执行中",
        EXEC_FAILED: "失败",
        EXEC_SUCCESS: "成功",
      };
      return keyValues[val] || val;
    },

    getList() {
      this.onRefresh = true;
      let data = {
        page_size: this.page_size,
        page_no: this.page_no,
        type: "EXCEL_TYPE_CRM_CUSTOMER_ACTION",
      };
      let url = "/reports-web/api/reports/afterSale/findMyReportList";
      this.ajax.postStream(
        url,
        data,
        (d) => {
          if (d.body.result && d.body.content) {
            this.list = d.body.content.list || [];
            this.count = d.body.content.count;
            this.$message.success(d.body.msg);
          } else {
            this.$message.error(d.body.msg);
          }
          this.onRefresh = false;
        },
        (e) => {
          this.$message.error(e);
          this.onRefresh = false;
        }
      );
    },
    pageSizeChange(res) {
      this.page_size = res;
      this.getList();
    },
    pageChange(res) {
      this.page_no = res;
      this.getList();
    },
  },
};
</script>

<style scoped>
.reservation-relational-data {
  padding: 20px 30px 0px !important;
}
.h2 {
  font-size: 21px !important;
  font-weight: bold;
  margin: 15px 0;
}
.search-item {
  display: flex;
  align-items: center;
  margin: 15px 0;
}
.item-title {
  margin-right: 30px;
  font-size: 15px !important;
  min-width: 100px;
  text-align: end;
}
.import-button {
  width: 220px;
}
.mr10 {
  margin-right: 10px;
}
</style>

<style>
.reservation-relational-data-search > div .el-button:nth-of-type(1),
.reservation-relational-data-search .el-icon-menu {
  display: none;
}
.reservation-relational-data-search {
  width: auto;
  float: none;
}
.reservation-relational-data .xpt-top {
  border-bottom: 2px solid #d1dbe5;
  background: none;
}

.reservation-relational-data-search .plan-show-panel,
.reservation-relational-data-search .save {
  display: none !important;
}

.reservation-relational-data-search .where-show-panel {
  width: auto;
  right: unset;
}

.reservation-relational-data-search .where-show-panel li {
  display: flex;
  align-items: center;
}
.reservation-relational-data-search > div {
  display: flex;
  align-items: center;
}

.search-item .el-checkbox-group {
  display: flex;
}
</style>