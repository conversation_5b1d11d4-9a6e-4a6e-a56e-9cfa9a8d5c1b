//导购跟进列表
<template>
  <xpt-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :selection="selection"
    :orderNo="true"
    :searchPage="search.page_name"
    :pageTotal="pageTotal"
    @search-click="searchClick"
    @selection-change="select"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
  >
    <template slot="customerBehavior" slot-scope="scope">
        <el-button type="text" size="small" @click="handleGotoPerformance(scope)">查看</el-button>
    </template>
  </xpt-list>
</template>

<script>
import Fn from '@common/Fn.js'
    export default {
      name: "saler_follow",
      data() {
        let self = this;
        return {
          selection: "checkbox",
          pageTotal:0,
          pageSize:50,
          selectRow:[],
          submitTime:"",
          btns: [
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.getList();
              },
            },
            {
              type: "primary",
              txt: "分配",
              loading: false,
              click() {
                self.distributeList();
              },
            },{
              type: "primary",
              txt: "同步微信状态",
              loading: false,
              click() {
                self.synJiKeWechatStatus();
              },
            },{
              type: "primary",
              txt: "回收",
              loading: false,
              click() {
                self.appointmentMobileRecycle();
              },
            }, {
              type: "success",
              txt: "导出",
              loading: false,
              click() {
                self.exportExcel();
              },
            }, {
              type: "info",
              txt: "报表导出文件下载",
              loading: false,
              click() {
                self.showExportList();
              },
            },
             {
              type: "warning",
              txt: "推送",
              loading: false,
              click() {
                self.pushDZ();
              },
            },{
              type: "primary",
              txt: "线索回收",
              loading: false,
              click() {
                self.clueRecycle();
              },
            },
          ],
          cols: [
            {
              label: "姓名",
              prop: "name",
              width:60,
              redirectClick(row) {
                let params = {};
                params.id = row.id;
                self.$root.eventHandle.$emit('creatTab', {
                  name: "导购跟进详情",
                  params: params,
                  component: () => import('@components/appoint_management/saler_follow_detail.vue')
                });
              },
            },
            {
              label: "手机号码",
              prop: "mobile",
              format: 'hidePhoneNumber',
              width:90,
            },
            {
                label:'客户行为表现',
                slot:'customerBehavior',
                width: 90,
            },
            {
              label: "预约活动编码",
              prop: "appointment_question_no",
              width:120,
            },
            {
              label: "预约提交时间",
              prop: "appointment_submit_time",
              width:140,
              format: "dataFormat1" ,
            },
            {
              label: "店铺地区",
              prop: "shop_area",
            },{
                label: '留资类型',
                prop: 'information_type',
                formatter(val) {
                    switch(val) {
                        case 'APPOINT_FORM': return '预约表单'; break;
                        case 'CALL_CONSULT': return '来电咨询'; break;
                        case 'ONLINE_CONSULTATION': return '在线咨询'; break;
                        default: return val; break;
                    }
                }
            }, {
                label: '来源平台',
                prop: 'information_platform_name',
                width: 90,
            }, {
                label: "来源渠道",
                prop: "appointment_channel_name",
                width: 90,
            }, {
                label: '状态',
                prop: 'status',
                formatter(val) {
                    switch(val) {
                        case 'WATING': return '待跟进'; break;
                        case 'FOLLOWING': return '跟进中'; break;
                        case 'FOLLOWED': return '已跟进'; break;
                        case 'ASSIGNED': return '已分配'; break;
                        case 'FINISHED': return '已完成'; break;
                        case 'DISABLED': return '已失效'; break;
                        default: return val; break;
                    }
                }
            },{
                label: '事业部',
                width:120,
                format: "auxFormat",
                formatParams: "business_division",
                prop: 'business_division'
            },
            {
                label: '店铺主营类目',
                prop: 'main_sale_categorie',
                width: 120,
                format: "auxFormat",
                formatParams: "main_sale_categories"
            },
            {
              label: "预约发送店铺",
              width:120,
              prop: "appointment_send_shop_name",
            },
            {
              label: "话务完成时间",
              prop: "tel_operator_confirm_time",
              width:140,
              format: "dataFormat1" ,
            },
            {
              label: "话务跟进标签",
              prop: "tel_operator_tag",
              width: 86,
              format: 'auxFormat',
              formatParams: 'appointment_tel_operator_tag'
            },
            {
              label: "话务跟进结果",
							prop: "tel_operator_mark_requirement",
							width: 100
            },
            // {
            //   label: "跟进话务",
						// 	prop: "tel_operator_name",
						// 	width: 100
            // },
            {
              label: "店长分配时间",
              width:140,
              prop: "shop_owner_assign_time",
              format: "dataFormat1" ,
            },
            {
              label: "导购员",
              width:60,
              prop: "shop_guide_name",
            },
            {
              label: "跟进次数",
              width:60,
              prop: "shop_guide_follow_times",
            },
            {
              label: "最近跟进时间",
              width:140,
              prop: "shop_guide_recent_follow_time",
              format: "dataFormat1",
            },
            {
              label: "首次跟进时间",
              width:140,
              prop: "shop_guide_first_follow_time",
              format: "dataFormat1",
            },
            {
              label: "导购跟进标签",
              width:90,
              prop: "shop_guide_tag",
              formatter(val){
                switch (val) {
                  case "GUIDE_INTENTIONAL" : return "已接通有意向";
                  case "GUIDE_UNINTENTIONAL" : return "已接通无意向";
                  case "GUIDE_NO_VISIT" : return "未接待访";
                }
              }
            },
            {
              label: "导购跟进备注",
              prop: "shop_guide_recent_mark",
              width:200,
            },
            {
                label: '微信添加状态',
                prop: 'wechat_status',
                width: 100,
                format: 'auxFormat',
                formatParams: 'appointment_wechat_status'
            },
            {
              label: '订单状态',
              prop: 'client_status',
              formatter(val) {
                  switch(val) {
                    case 'WAITING_DISTRIBUTION_DESIGN': return '待分配设计师'; break;
                    case 'WAITING_MEASUREMENT': return '待量尺'; break;
                    case 'WAITING_DESIGN': return '待设计'; break;
                    case 'IN_DESIGN': return '设计中'; break;
                    case 'REJECT_VERIFY': return '审图拆单驳回'; break;
                    case 'WAITING_COMMUNICATION': return '待沟通'; break;
                    case 'IN_COMMUNICATION': return '沟通中'; break;
                    case 'WAITING_SIGN_CONTRACT': return '待签合同'; break;
                    case 'IN_SIGN_CONTRACT': return '签定合同中'; break;
                    case 'WAITING_CLASSIFICATION': return '待分类'; break;
                    case 'IN_CLASSIFICATION': return '分类中'; break;
                    case 'SIGNED_CONTRACT_WAITING_VERIFY': return '已签合同待审图拆单'; break;
                    case 'REJECT_WAITING_SPLIT_ORDER': return '拆审驳回'; break;
                    case 'IN_VERIFY': return '审核中'; break;
                    case 'WAITING_SPLIT_ORDER': return '待拆单'; break;
                    case 'IN_SPLIT_ORDER': return '拆单中'; break;
                    case 'WAITING_SPLIT_AUDIT': return '待拆审'; break;
                    case 'REJECT_WAITING_PURCHASE': return '下推采购驳回'; break;
                    case 'IN_SPLIT_AUDIT': return '拆审中'; break;
                    case 'WAITING_PURCHASE': return '待下推采购'; break;
                    case 'WAITING_CHARGE': return '待收款'; break;
                    case 'IN_PUSH_PURCHASE': return '下推采购中'; break;
                    case 'WAITING_PUSH_GOODS': return '待下推商品'; break;
                    case 'WAITING_OUTBOUND': return '待出库'; break;
                    case 'SUCCESS_GOODS': return '已货审'; break;
                    case 'SUCCESS_FINANCE': return '已财审'; break;
                    case 'COMPLETED': return '已出库'; break;
                    case 'CLOSE': return '关单'; break;
                    default: return val; break;
                  }
              }
            },
            {
              label: "通话呼出时间",
              width:140,
              prop: "call_start_time",
              format: "dataFormat1",
            },
            {
              label: "振铃时长(秒)",
              prop: "call_ring_seconds",
            },
            {
              label: "通话应答时间",
              width:140,
              prop: "call_answer_time",
              format: "dataFormat1" ,
            },
            {
              label: "通话结束时间",
              width:140,
              prop: "call_end_time",
              format: "dataFormat1",
            },
            {
              label:"接通时长(秒)",
              prop:"call_answer_seconds",
            },
            {
              label: "预约码状态",
              prop: "appointment_is_visit",
              width:90,
              formatter(val){
                switch (val) {
                  case false : return "未核销";
                  case true : return "已核销";
                }
              }
            },
            {
              label: "到店时间",
              prop: "appointment_visit_time",
              width:140,
              formatter(val,index,row){
                return row.appointment_send_shop_no===row.appointment_visit_shop_no?Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss'):""
              },
            },
            {
              label: '合并单号',
              prop: 'merge_trade_no',
              width: 130,
              formatter(val,index,row){
                return row.appointment_send_shop_no===row.appointment_visit_shop_no?val:""
              }
            },{
              label: '买家昵称',
              prop: 'customer_name',
              width: 130,
              formatter(val,index,row){
                return row.appointment_send_shop_no===row.appointment_visit_shop_no?val:""
              }
            }, {
              label: '最早支付时间',
              prop: 'pay_time',
              width: 130,
              formatter(val,index,row){
                return row.appointment_send_shop_no===row.appointment_visit_shop_no?Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss'):""
              },
            }, {
              label: '总支付金额',
              prop: 'amount',
              width: 130,
              formatter(val,index,row){
                return row.appointment_send_shop_no===row.appointment_visit_shop_no?val:""
              }
            }
          ],
          search:{
            page_name:"crm_appointment_tracing_shop_display",
            where:[],
            page:{
              length:50,
              pageNo:1,
            }
          },
          dataList: [],
          callAssistant:{},
        }
      },
      methods:{
        handleGotoPerformance(params){
          params.isReservation = false;
            this.$root.eventHandle.$emit("creatTab", {
                name: "客户行为表现",
                params,
                component: () => import("@components/appoint_management/customer_behavior_performance.vue"),
            });
        },
        // 线索回收
        clueRecycle(){
            if(this.selectRow.length === 0 ||
                this.selectRow.length > 50 ||
                this.selectRow.some(item => {
                return item.appointment_is_visit
                })
            ) {
                let msg = this.selectRow.length === 0 ? '请至少选择一行数据！' : (this.selectRow.length > 50 ? '线索回收单次操作不能超过50行数据' : '已成交的线索不可回收!')
                this.$message.error(msg)
                return
            }
            let data = {
                ids: this.selectRow.map(item=>{
                    return item.id
                })
            }

            this.ajax.postStream('/crm-web/api/crmAppointmentTracing/clueRecycleBatch?permissionCode=APPOINTMENT_RECYCLE',data,res => {
                if(res.body.result) {
                res.body.msg && this.$message.success(res.body.msg);
                this.getList();
                } else {
                res.body.msg && this.$message.error(res.body.msg);
                }
            }, err => {
                this.$message.error(err);
            });
        },
        // 同步微信状态
        synJiKeWechatStatus() {
          this.ajax.postStream('/crm-web/api/crmJikeInterface/synJiKeWechatStatus?permissionCode=SYN_JI_KE_WECHAT_STATUS',{},res => {
            if(res.body.result) {
              res.body.msg && this.$message.success(res.body.msg);
              this.getList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        // 回收
        appointmentMobileRecycle() {
          if(this.selectRow.length === 0 ||
            this.selectRow.length > 50 ||
            this.selectRow.some(item => {
              return !/(^(WAIT_REMIND|WAIT_ADD)$)/.test(this.selectRow[0].wechat_status)
            })
          ) {
            let msg = this.selectRow.length === 0 ? '请至少选择一行数据！' : (this.selectRow.length > 50 ? '线索手机号回收单次操作不能超过50行数据' : '线索手机号回收只能是回收待提醒和待添加！')
            this.$message.error(msg)
            return
          }
          let postData = []
          this.selectRow.forEach(element => {
            postData.push({mobile:element.mobile, wechat_status: element.wechat_status})
          })
          this.ajax.postStream('/crm-web/api/crmJikeInterface/appointmentMobileRecycle?permissionCode=APPOINTMENT_MOBILE_RECYCLE',postData,res => {
            if(res.body.result) {
              res.body.msg && this.$message.success(res.body.msg);
              this.getList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        getList(resolve){
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getGuideFollowShopDisplayList',this.search,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.pageTotal = res.body.content.count;
              this.configCallData();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        distributeList() {
          let self = this,
            ids = [],
            shops = [],
            shop_no = "",
            count=0;
          this.selectRow.find(d=>{
            ids.push(d.id);
            shops.push(d.appointment_send_shop_no);
          });
          if(!ids.length){
            self.$message({
              message:'请选择要分配的客户！',
              type:'error'
            });
            return;
          }
          //判断所选数据的预约发送店铺是否一致
          shop_no = shops[0];
          if (shops.length > 1) {
            for (let i=1; i < shops.length; i++){
              if (shop_no !== shops[i]){
                count++;
              }
            }
            if (count > 0){
              self.$message.error("请选择预约发送店铺一致的数据进行批量分配！");
              return;
            }
          }
          console.log(ids,shop_no);
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/appoint_management/owner_distribution_list.vue'),
            style: 'width:800px;height:500px',
            title: "导购员列表",
            params: {
              customer:ids,
              appointment_send_shop_no:shop_no,
              //刷新分配列表
              callback: data => {
                this.getList();
              }
            }
          });
        },
        configCallData(){
          //如果是未接通的数据，应答时间和通话结束时间都设为空
          for (let i=0;i<this.dataList.length;i++){
            //console.log(this.dataList[i].call_answer_time);
            let call_answer_time = this.dataList[i].call_answer_time;
            if (!call_answer_time || call_answer_time === "" ) {
              this.dataList[i].call_end_time = "";
            }
          }
        },
        refresh(){
          this.getList();
        },
        searchClick(list, resolve){
          this.search.where = list;
          this.getList(resolve);
        },
        // 多选事件
        select(s){
          this.selectRow = s;
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page.length = pageSize;
          this.getList();
        },
        // 监听页数更改事件
        pageChange(page){
          this.search.page.pageNo = page;
          this.getList();
        },
        // 导出
        exportExcel () {
          this.btns[2].loading = true;
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/exportGuideFollowShopDisplayList',this.search,res => {
            if(res.body.result) {
              res.body.msg && this.$message.success(res.body.msg)
            } else {
              res.body.msg && this.$message.error(res.body.msg)
            }
            this.btns[2].loading = false
          }, err => {
            this.btns[2].loading = false
            this.$message.error(err)
          })
        },
        showExportList (){
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/after_sales_report/export'),
            style:'width:900px;height:600px',
            title: '报表导出列表',
            params: {
              query: {
              type: 'EXCEL_TYPE_CRM_APPOINTMENT_TRACING_SHOP_DISPLAY',
              },
            },
          })
        },
        pushDZ(){
          if(this.selectRow.length==0){
            this.$message.warning("请选择数据")
            return
          }
          let tagList=['INTENTION_FOR_MEASURE','INTENTION_FOR_ENTER_SHOP','INTENTION_FOR_FOLLOW']//跟进标签
          let canIPush=this.selectRow.every(item=>tagList.includes(item.tel_operator_tag))//是否可以操作推送
          if(!canIPush){
            this.$message.warning("话务跟进标签标为【有意向待量尺 】【有意向待进店 】【有意向待跟进导购】才可推送")
            return
          }
          let btnIndex=this.btns.findIndex(item=>item.txt=='推送')
          let ids=this.selectRow.map(item=>item.id)
          this.btns[btnIndex].loading=true;
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/pushDz',{ids},res=>{
            if(res.body.result){
              this.$message.success(res.body.msg)
              this.getList()
            }else{
              this.$message.error(res.body.msg||'推送失败，请稍候再试')
            }
            this.btns[btnIndex].loading=false;
          },err=>{
            this.$message.error(err)
            this.btns[btnIndex].loading=false;
          })
        }
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped></style>
