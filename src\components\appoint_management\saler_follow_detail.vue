//导购跟进详情
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='success' size='mini' @click="getList">刷新</el-button>
        <el-button type="success" size="mini" @click="getFileList" :disabled="!catIgetFileList">查看附件</el-button>
      </el-col>
    </el-row>
    <div class='custInfo'><div>客户信息</div></div>
      <el-form label-position="right" label-width="180px">
        <el-col :span="6" style="width: 40%">
          <el-form-item label="客户姓名：">{{headerDataList.name}}</el-form-item>
          <el-form-item label="手机号码：">
            <!-- {{headerDataList.mobile}} -->
            <xpt-eye-switch v-model="headerDataList.mobile" :readonly="true" width="110" :hideBorder="true" :aboutNumber="headerDataList.appointment_question_no"></xpt-eye-switch>
          </el-form-item>
          <el-form-item label="预约提交时间：">{{submitTime}}</el-form-item>
          <el-form-item label="预约发送店铺：">{{headerDataList.appointment_send_shop_name}}</el-form-item>
          <el-form-item label="店长分配时间：">{{assignTime}}</el-form-item>
          <el-form-item label="来源平台：">{{headerDataList.information_platform_name}}</el-form-item>
        </el-col>
        <el-col :span="6" style="width: 60%">
          <el-form-item label="话务跟进结果：">
            <p v-tooltip='headerDataList.tel_operator_mark_requirement'>{{headerDataList.tel_operator_mark_requirement}}</p>
          </el-form-item> 
          <!-- <el-form-item label="预计到店时间：">{{headerDataList.tel_operator_mark_arrival}}</el-form-item> -->
          <el-form-item label="状态：">{{statusObj[headerDataList.status]}}</el-form-item>
          <el-form-item label="导购员：">{{headerDataList.shop_guide_name}}</el-form-item>
          <el-form-item label="导购最新跟进标签：">{{tag}}</el-form-item>
          <el-form-item label="来源渠道：">{{headerDataList.appointment_channel_name}}</el-form-item>
        </el-col>
      </el-form>
    <div class='xpt-flex__bottom' id='bottom' v-fold>
      <el-tabs v-model="secondTab" @tab-click="changeTab">
        <el-tab-pane label="历史分配记录" name="distributionHistory" class='xpt-flex'>
          <xpt-list
            :data='distributionHistoryList'
            :colData='distributionHistoryCols'
            selection=''
            :showHead='false'
            :orderNo="true"
            :pageTotal='distributionHistoryCount'
            @page-size-change='distributionHistoryPageSizeChange'
            @current-page-change='distributionHistoryCurrentPageChange'
          ></xpt-list>
        </el-tab-pane>
        <el-tab-pane label="历史跟进记录" name="followHistory" class='xpt-flex'>
          <xpt-list
            :data='followHistoryList'
            :colData='followHistoryCols'
            selection=''
            :showHead='false'
            :orderNo="true"
            :pageTotal='followHistoryCount'
            @page-size-change='followHistoryPageSizeChange'
            @current-page-change='followHistoryCurrentPageChange'
          ></xpt-list>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
  import Fn from '@common/Fn.js'
    export default {
      name: "saler_follow_detail",
      props:["params"],
      data(){
        let self = this;
        return {
          tag:"",
          assignTime:"",//店长分配时间
          submitTime:"",//预约提交时间
          startTime:"",//通话开始时间
          endTime:"",//通话结束时间
          headerDataList:{},
          distributionHistoryCount:0,
          distributionHistoryQuery:{
            length:50,
            pageNo:1
          },
          distributionHistoryList:[],
          distributionHistoryCols:[
            {
             label:"被分配人",
             prop:"assignee_name"
            },
            {
              label:"分配时间",
              prop:"assign_time",
              format: "dataFormat1"
            }
          ],
          followHistoryCount:0,
          followHistoryList:[],
          followHistoryQuery:{
            length:50,
            pageNo:1
          },
          followHistoryCols:[
            {
              label:"导购员",
              prop:"shop_guide_name"
            },
            {
              label:"导购跟进来源",
              prop:"shop_guide_follow_frequency",
              formatter(val){
                switch (val) {
                  case "ONE" : return "首次";
                  case "TWO" : return "再次";
                  case "JIKE" : return "集客企微跟进";
                  default: return 'APP跟进'; break;
                }
              }
            },
            {
              label:"导购跟进时间",
              prop:"create_time",
              format: "dataFormat1" ,
            },
            {
              label:"导购跟进标签",
              prop:"shop_guide_tag",
              formatter(val){
                switch (val) {
                  case "GUIDE_INTENTIONAL" : return "已接通有意向";
                  case "GUIDE_UNINTENTIONAL" : return "已接通无意向";
                  case "GUIDE_NO_VISIT" : return "未接待访";
                }
              }
            },
            {
              label:"导购跟进备注",
              prop:"shop_guide_mark"
            },
            {
              label: "通话呼出时间",
              prop: "call_start_time",
              format: "dataFormat1",
            },
            {
              label: "振铃时长(秒)",
              prop: "call_ring_seconds",
            },
            {
              label:"通话应答时间",
              prop:"call_answer_time",
              format: "dataFormat1" ,
            },
            {
              label:"通话结束时间",
              prop:"call_end_time",
              format: "dataFormat1" ,
            },
            {
              label:"通话时长(秒)",
              prop:"call_answer_seconds",
            },
          ],
					secondTab:"distributionHistory",
					statusObj: {
						WATING: '待跟进',
						FOLLOWING: '跟进中',
						FOLLOWED: '已跟进',
						ASSIGNED: '已分配',
						FINISHED: '已完成',
						DISABLED: '已失效',
                    },
                    catIgetFileList:false,//能否查看附件
                    getDataObj:{}//获取附件列表参数
        }
      },
      methods: {
        getHeaderList() {
          let data = {
            id: this.params.id,
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getShopCrmAppointmentTracingDetail', data, res => {
            if (res.body.result && res.body.content) {
              this.headerDataList = res.body.content;
              this.assignTime = Fn.dateFormat(res.body.content.shop_owner_assign_time, 'yyyy-MM-dd hh:mm:ss');
              this.submitTime = Fn.dateFormat(res.body.content.appointment_submit_time, 'yyyy-MM-dd hh:mm:ss');
              this.setTag(res.body.content.shop_guide_recent_tag);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        //历史跟进记录
        getFollowHistory(){
          let data = {
            id: this.params.id,
            page: {
              length:this.followHistoryQuery.length,
              pageNo:this.followHistoryQuery.pageNo
            },
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getShopCrmAppointmentFollowList', data, res => {
            if (res.body.result && res.body.content) {
              this.followHistoryList = res.body.content.list;
              this.followHistoryCount = res.body.content.count;
              this.configCallData();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        //历史分配记录
        getDistributionHistory(){
          let data = {
            id: this.params.id,
            page: {
              length:this.distributionHistoryQuery.length,
              pageNo:this.distributionHistoryQuery.pageNo
            }
          };
          this.ajax.postStream('/crm-web/api/crmAppointmentTracing/getShopCrmAppointmentAssignList', data, res => {
            if (res.body.result && res.body.content) {
              this.distributionHistoryList = res.body.content.list;
              this.distributionHistoryCount = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        setTag(tag){
          if (tag === "GUIDE_INTENTIONAL") {
            this.tag = "已接通有意向";
          } else if (tag === "GUIDE_UNINTENTIONAL") {
            this.tag = "已接通无意向";
          } else if (tag === "GUIDE_NO_VISIT")  {
            this.tag = "未接待访";
          }
        },
        getList(){
          this.getHeaderList();
          this.getDistributionHistory();
          this.getFollowHistory()
        },
        configCallData(){
          //如果是未接通的数据，应答时间和通话结束时间都设为空
          for (let i=0;i<this.followHistoryCount;i++){
            console.log("haha:",this.followHistoryList[i].call_end_time);
            if (!this.followHistoryList[i].call_answer_time || this.followHistoryList[i].call_answer_time === "" ) {
              this.followHistoryList[i].call_end_time = "";
            }
          }
        },
        changeTab(){
          let self = this;
          if(self.secondTab === "distributionHistory"){
            self.getDistributionHistory();
          }else if(self.secondTab === "followHistory") {
            self.getFollowHistory();
          }
        },
        distributionHistoryPageSizeChange(size){
          this.distributionHistoryQuery.length = size;
          this.getDistributionHistory();
        },
        distributionHistoryCurrentPageChange(page){
          this.distributionHistoryQuery.pageNo = page;
          this.getDistributionHistory();
        },
        followHistoryPageSizeChange(size){
          this.followHistoryQuery.length = size;
          this.getFollowHistory();
        },
        followHistoryCurrentPageChange(page){
          this.followHistoryQuery.pageNo = page;
          this.getFollowHistory();
        },
        //设置获取附件上传的参数
        setgetFileListObj(){
            if(!this.params.id){
                this.catIgetFileList=false;
                return
            }
            this.getDataObj={
                parent_name: "CALL_FOLLOW",
                parent_no: `HWGJ${this.params.id}`,//主要通过该参数获取附件列表
                child_name: null,
                child_no: null,
                content: {},
            }
            this.catIgetFileList=true;
        },
        //查看附件
        getFileList() {
        let self = this;
        this.$root.eventHandle.$emit("alert", {
            title: "查看附件",
            style: "width:900px;height:600px;",
            component: () =>
                import("@components/appoint_management/call_follow_detail_file"),
            params: {
                uploadData:this.getDataObj,
                callback: () => {
                    self.$message.success("获取附件列表成功");
                },
            },
        });
      },
      },
      mounted: function() {
        this.getList();
        this.setgetFileListObj();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>
  .custInfo {
    border-bottom: 1px solid #d1dbe5;
  }
  .custInfo div{
    padding: 0 10px;
    display: block;
    height: 24px;
    line-height: 24px;
    color: #20a0ff;
    border-bottom: 3px solid #20a0ff;
    display: inline-block;
  }

</style>
