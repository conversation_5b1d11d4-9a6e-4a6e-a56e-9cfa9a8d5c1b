<template>
  <xpt-list-dynamic
    :data='dataList'
    :btns='btns'
    :colData='cols'
    :pageTotal='count'
    :searchPage='searchData.page_name'
    :pageLength='searchData.page_size'
    isNeedClickEvent
    @search-click='presearch'
    :selection="''"
    @page-size-change='sizeChange'
    @current-page-change='pageChange'
  ></xpt-list-dynamic>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
  data(){
    let self = this;
    return {
      btns: [
        {
          type: 'success',
          txt: '刷新',
          loading: false,
          click() {
            self.getList()
          }
        }, {
          type: 'primary',
          txt: '导出列表',
          loading: false,
          click() {
            self.exportAnalysisyList()
          }
        }, {
          type: 'primary',
          txt: '导出列表文件查看',
          loading: false,
          click() {
            self.expList()
          }
        },
        ],
      cols: [
        {
          label: '店铺名称',
          prop: 'store_name',
          width:140,
        },
        {
          label: '买家昵称',
          prop: 'customer_name',
          width:100,
        },
        {
          label: '手机',
          prop: 'phone',
        },
        {
          label: '接待开始时间',
          prop: 'start_time',
          format: 'dataFormat1',
          width: 150,
        },
        {
          label: '接待结束时间',
          prop: 'end_time',
          format: 'dataFormat1',
          width: 150,
        },
        {
          label: '是否成交',
          prop: 'dealed',

        },
        {
          label: '合并单号',
          prop: 'merge_trade_no',
          width: 120,
        },
        {
          label: '总支付金额',
          prop: 'amount',
        },
        {
          label: '导购员名称',
          prop: 'user_name',
          width: 150,
        },
        {
          label: '创建时间',
          prop: 'create_time',
          format: 'dataFormat1',
          width: 150,
        },
        {
          label: '更新时间',
          prop: 'update_time',
          format: 'dataFormat1',
          width: 150,
        }
      ],
      dataList:[],
      searchData:{
        page_name:"shop_reception_record",
        where:[],
        page_no:1,
        page_size:50,
        if_need_page: 'Y'
      },
      count:20,
    }
  },
  methods:{
    sizeChange(size){
      this.searchData.page_size = size;
      this.getList()
    },

    pageChange(page){
      this.searchData.page_no = page
      this.getList()
    },
    getList(resolve){
      this.btns[0].loading = true;
      this.ajax.postStream('/reports-web/api/reports/dealer/shopReceptionRecord/list', this.searchData, d=>{
        if(d.body.result){
          this.dataList = d.body.content.list || []
          this.count = d.body.content.count
				} else {
          this.dataList = []
		      this.count = 0
          this.$message.error(d.body.msg || '');
        }
        this.btns[0].loading = false;
        resolve && resolve()
      }, err => {
        this.$message.error(err);
        resolve && resolve();
        this.btns[0].loading = false;
      })
    },
    presearch(list, resolve){
        this.searchData.where = list;
        this.getList(resolve);
    },

    exportAnalysisyList (){
//      if(!this.searchData.where.length){
//        this.$message.error('导出功能至少要有一个过滤条件')
//        return
//      }
      var postData = JSON.parse(JSON.stringify(this.searchData))
      delete postData.page
      this.ajax.postStream('/reports-web/api/reports/dealer/shopReceptionRecord/export', postData, d => {
        this.$message({
          type: d.body.result ? 'success' : 'error',
          message: d.body.msg,
        })
      })
    },

    expList(){
      this.$root.eventHandle.$emit('creatTab', {
        name: "报表导出",
        params: { 
          url: '/reports-web/api/reports/afterSale/findMyReportList',
          data: {
            page_size: 50,
            page_no: 1,
            type: "EXCEL_TYPE_SHOP_RECEPTION_RECORD_EXPORT"
          }
        },
        component: () => import('@components/common/exout.vue')
      })
    },
  },

  mounted:function() {
    this.getList()
  }
}
</script>
