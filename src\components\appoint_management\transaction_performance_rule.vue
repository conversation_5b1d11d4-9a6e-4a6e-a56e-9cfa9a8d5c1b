<template>
  <div class="container">
    <div class="title-container">
      <h2 class="title">标题</h2>
      <el-input
        type="text"
        v-model="ruleData.name"
        class="input-style"
        placeholder="请输入规则标题"
      ></el-input>
    </div>
    <h2 class="content-title">内容</h2>
    <wangEditor
      :baseConfig="config"
      :divId="editorId"
      ref="we"
      defaultMsg=""
      class="wangEditor mgt15"
    ></wangEditor>
    <el-button
      el-button
      type="primary"
      size="small"
      @click="handleEditorSave"
      class="save-button"
      >保存</el-button
    >
  </div>
</template>

<script>
import wangEditor from "@components/wang_editor/wangEditor.vue";
import "@stylus/wangEditor.styl";
export default {
  props: ["params", "getCode"],
  components: { wangEditor },
  data() {
    return {
      config: {
        width: "100%",
        height: 380,
        zIndex: 2,
        placeholder: "请输入成交业绩规则",
      },
      editorId: "we" + new Date().getTime(),
      list: [],
      ruleData: {},
      categoryPo: {},
      code: "DEAL_RULE",
    };
  },
  methods: {
    getWEContent() {
      return this.$refs.we.getEditorHtml();
    },
    handleEditorSave() {
      let str = this.$refs.we.getEditorTxt();
      if (!this.ruleData.name || !str) {
        this.$message.error("标题和内容不能为空");
        return;
      }
      this.ruleData.remark = this.getWEContent();
      let data = this.list;
      for (let i = 0; i < data.length; i++) {
        if (data[i].code === this.code) {
          data[i] = this.ruleData;
          break;
        }
      }
      this.submit(data);
    },

    getList(resolve) {
      let data = {
        categoryCode: "SWB_RULE_NOTICE",
        page: this.page,
      };
      let url = "/user-web/api/auxiliary/getAuxiliaryDataList";
      this.ajax.postStream(
        url,
        data,
        (d) => {
          if (d.body.result && d.body.content) {
            let { list } = d.body.content;
            this.list = list;
            this.ruleData = list.filter((item) => item.code === this.code)[0];
            this.$refs.we.setEditorHtml(this.ruleData.remark);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve();
        }
      );
    },
    getAuxiliaryCategoryList() {
      let data = {
        page: { length: 50, pageNo: 1 },
        where: [
          {
            field: "ebfbc7a9d21ea1d26868b6b331afd07e",
            table: "c982ba0edaab6ffe3ebc4851a8c1e568",
            value: "SWB_RULE_NOTICE",
            operator: "=",
            condition: "AND",
            listWhere: [],
          },
        ],
        page_name: "cloud_auxiliary_category",
      };
      this.ajax.postStream(
        "/user-web/api/auxiliary/getAuxiliaryCategoryList",
        data,
        (d) => {
          if (d.body.result && d.body.content) {
            let { code, id, name, parentCode, platform, remark, system } =
              d.body.content.list[0];
            this.categoryPo = {
              code,
              id,
              name,
              parentCode,
              platform,
              remark,
              system,
            };
          }
        },
        (e) => {
          self.$message({
            message: e.statusText,
            type: "error",
          });
        }
      );
    },
    submit(dataList) {
      let data = {
        categoryPo: this.categoryPo,
        dataList,
      };
      this.ajax.postStream(
        "/user-web/api/auxiliary/saveAuxiliaryCategory?permissionCode=AUXILIARY_DATA",
        data,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg);
            this.getList();
          } else {
            this.$message.error(d.body.msg);
          }
        },
        (e) => {
          this.$message.error(d.body.msg);
        }
      );
    },
  },
  mounted() {
    this.code = this.getCode || "DEAL_RULE";
    this.getList();
    this.getAuxiliaryCategoryList();
  },
};
</script>

<style scoped>
.container {
  padding: 30px !important;
}
.save-button {
  width: 100px;
  margin-top: 30px;
}

.title-container {
  display: flex;
  align-items: center;
}
.title-container .title {
  margin-right: 20px;
}

.content-title {
  margin: 20px 0;
}

.input-style {
  width: 300px;
}
</style>