<!--新增辅资料值-->
<template>
	<div>
		<xpt-headbar>
			<el-button type='primary' size='mini' @click='submit' slot='left'>确认</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click='close' slot='right'>关闭</el-button>
		</xpt-headbar>

		<el-form :model='aux' ref='aux' :rules='rules' label-position="right" label-width="80px">
			<el-row	:gutter='40'>
				<el-col :span='10'>
					<el-form-item label="类别">
						<el-input v-model="aux.categoryName" size='mini' disabled></el-input>
					</el-form-item>
					<el-form-item label="父类别">
						<el-input v-model="aux.parentCategoryName" size='mini' disabled></el-input>
					</el-form-item>
					<el-form-item label="编码" prop='code'>
						<el-input v-model="aux.code" size='mini' style='width:180px' :disabled='isMod'></el-input>
						<el-tooltip v-if='rules.code[0].isShow' class="item" effect="dark" :content="rules.code[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="名称" prop='name'>
						<el-input v-model="aux.name" size='mini' style='width:180px'></el-input>
						<el-tooltip v-if='rules.name[0].isShow' class="item" effect="dark" :content="rules.name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="标识">
						<el-input v-model="aux.tag" size='mini' style='width:180px'></el-input>
					</el-form-item>
				</el-col>
				<el-col :span='10'>
					<el-form-item label="生效">
						<el-switch v-model="aux.status" on-text="生效" off-text="失效"></el-switch>
					</el-form-item>
					<el-form-item label="生效日期" prop='enableTime'>
						<el-date-picker v-model="aux.enableTime" type="datetime" placeholder="选择日期" size='mini' :picker-options="pickerOptions0" style='width:180px' @change="onEnableDateChange"></el-date-picker>
						<el-tooltip v-if='rules.enableTime[0].isShow' class="item" effect="dark" :content="rules.enableTime[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="失效日期" prop='disableTime'>
						<el-date-picker v-model="aux.disableTime" type="datetime" placeholder="选择日期" size='mini' :picker-options="pickerOptions0" style='width:180px' @change="onDisableDateChange"></el-date-picker>
						<el-tooltip v-if='rules.disableTime[0].isShow' class="item" effect="dark" :content="rules.disableTime[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="上级资料" prop='parentCode'>
						<xpt-input v-model="aux.parentName" size='mini' icon='search' :on-icon-click="openSuperior" :disabled='aux.parentCategoryCode?false:true'  style='width:180px' @change='parentCodeChange'></xpt-input>
						<el-tooltip v-if='rules.parentCode[0].isShow' class="item" effect="dark" :content="rules.parentCode[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="扩展1" >
						<el-input v-model='aux.ext_field1' size='mini'></el-input>

					</el-form-item>
					<el-form-item label="扩展2">
						<el-input v-model='aux.ext_field2' size='mini'></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row	:gutter='40'>
				<el-col :span='20'>
					<el-form label-position="right" label-width="80px">
						<el-form-item label="备注">
							<el-input type='textarea' v-model="aux.remark" size='small'></el-input>
						</el-form-item>
					</el-form>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
	export default {
		data(){
			let self = this;
			return {
				aux:{
					// 辅助资料类别
					categoryCode:'',
					categoryName: '',

					name:'',
					code:'',
					// 上级资料编码
					parentCode:'',
					parentName: '',
					// 失效日期
					disableTime:'',
					// 生效日期
					enableTime:'',
					status:true,
					remark:'',
					// 辅助资料父类别
					parentCategoryCode:'',
					parentCategoryName: '',
					id:'',
					tag: '',
					ext_field1:"",
					ext_field2:"",
				},
				rules:{
					code:[{
						required:true,
						message:'请输入编码',
						isShow:false,
						validator: function(rule,value,callback){
							if(value){
								if(/^[A-Za-z0-9_-]+$/.test(value)){
									self.rules[rule.field][0].isShow = false
									callback();
								}else{
									self.rules[rule.field][0].isShow = true
									self.rules[rule.field][0].message = '编码必须是大写字母、数字、_、-的组合格式'
									callback(new Error(''));
								}
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}],
					name:[{
						required:true,
						message:'请输入名称',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}],
					parentCode:[{
						isShow:false,
                        message:'请选择上级资料',
                        trigger:'change',
                        validator:(rule,value,callback)=>{
                            if(self.aux.parentCategoryCode){
                                if(value){
                                    self.rules[rule.field][0].isShow = false;
                                    callback()
                                }else{
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(''))
                                }
                            }else{
                                self.rules[rule.field][0].isShow = false;
                                callback()
                            }
                        }
					}],
					enableTime:[{
						message:'生效时间不能大于失效时间',
                        trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(value&&self.aux.disableTime){
								if(value>self.aux.disableTime){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false;
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false;
									}else{
										self.rules['disableTime'][0].isValidateFromOther = true;
										self.$refs['aux'].validateField('disableTime')
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = false
								callback();
							}
						}
					}],
					disableTime:[{
						message:'失效时间不能小于生效时间',
                        trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(value&&self.aux.enableTime){
								if(value<self.aux.enableTime){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false
									}else{
										self.rules['enableTime'][0].isValidateFromOther = true;
										self.$refs['aux'].validateField('enableTime')
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = false
								callback();
							}
						}
					}]
				},
				updateCount:0,
				pickerOptions0: {
          date:(function(){
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth()+1;
            var day = date.getDate();
            var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
            return new Date(time);
          })(),
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          }
        }
			}
		},
		props:['params'],
		methods:{
			// 保存资料
			submit(callback){
				this.$refs['aux'].validate((valid) => {
					if(valid){
						// 把所选辅助资料编码返回
						this.params.callback(this.aux);
						this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
					}
				})

			},
			// 关闭
			close(){
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
			// 打开上级
			openSuperior(){
				if(this.aux.parentCategoryCode){
					let self = this;
	                this.$root.eventHandle.$emit('alert',{
	                    component:()=>import('@components/auxiliary/auxiliaryList'),
	                	style:'width:800px;height:500px',
	                    title:'辅助资料列表',
						params:{
							categoryCode:this.aux.parentCategoryCode,
							callback(d){
								self.aux.parentCode = d.code
								self.aux.parentName = d.name
							}
						}//传参过去
	            	});
				}

			},
			parentCodeChange(s){
				if(!s){
					this.aux.parentCode = ''
					this.aux.parentName = ''
				}
			},
      onEnableDateChange(date){
				this.aux.enableTime = date
			},
      onDisableDateChange(date){
				this.aux.disableTime = date
			}
		},
		mounted(){
			var self = this;
			this.aux.categoryCode = this.params.categoryCode||''
			this.aux.categoryName = this.params.categoryName||''
			this.aux.parentCategoryCode = this.params.parentCategoryCode||''
			this.aux.parentCategoryName = this.params.parentCategoryName||''
			this.aux.parentCode = this.params.parentCode||'';
			this.aux.parentName = this.params.parentName||'';
			this.aux.name = this.params.name||'';
			this.aux.ext_field1 = this.params.ext_field1||'';
			this.aux.ext_field2 = this.params.ext_field2||'';
			this.aux.remark = this.params.remark||'';
			this.aux.disableTime = this.params.disableTime||''
			this.aux.enableTime = this.params.enableTime||''
			if(typeof(this.params.status)!=='undefined'){
				this.aux.status = this.params.status?true:false
			}
			this.aux.code = this.params.code||'';
			this.aux.id = this.params.id|| 'temp'+(+new Date());
			this.aux.tag = this.params.tag;
			this.params.__data = JSON.stringify(this._data);
		},
		computed: {
			// 是否为编辑已经保存的数据
			isMod() {
				return (this.aux.id + '').indexOf('temp') === -1;
			}
		}
	}
</script>
