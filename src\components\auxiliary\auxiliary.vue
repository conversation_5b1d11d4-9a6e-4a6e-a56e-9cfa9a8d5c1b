<!--新增、编辑辅助资料类别-->
<template>
	<div class='xpt-flex'>
		<xpt-headbar>
			<el-button type='primary' size='mini' @click='submit("auxiliary")' :loading='onAjax' :disabled='onAjax' slot='left'>保存</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click='close' slot='right'>关闭</el-button>
		</xpt-headbar>
		<el-form :model='auxiliary' :rules='rules' ref='auxiliary' label-position="right" label-width="100px">
			<el-row :gutter='40'>
				<el-col :span='8'>
					<el-form-item label="类别编码" prop='code'>
						<el-input v-model="auxiliary.code" size='mini' :disabled='auxiliary.id?true:false'></el-input>
						<el-tooltip v-if='rules.code[0].isShow' class="item" effect="dark" :content="rules.code[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="类别名称" prop='name'>
						<el-input :maxlength='30' v-model="auxiliary.name" size='mini'></el-input>
						<el-tooltip v-if='rules.name[0].isShow' class="item" effect="dark" :content="rules.name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="父类别">
						<el-input v-model='auxiliary.parentName' size='mini' v-if='auxiliary.id' disabled></el-input>
						<xpt-input v-model="auxiliary.parentName" size='mini' icon='search' :on-icon-click="openAuxiliaryParent" @change='parentCodeChange' v-else></xpt-input>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="平台" prop='platform'>
					  	<xpt-select-aux v-model='auxiliary.platform' aux_name='platform' showcode :disabled='auxiliary.id?true:false'></xpt-select-aux>
					  	<el-tooltip v-if='rules.platform[0].isShow' class="item" effect="dark" :content="rules.platform[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="系统">
						<el-switch v-model="auxiliary.status" on-text="是" off-text="否" :disabled='auxiliary.id?true:false'></el-switch>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter='40' style='height:50px'>
				<el-col :span='16'>
					<el-form-item label="备注">
					    <el-input type="textarea" :maxlength='1000' v-model="auxiliary.remark"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div class='xpt-flex__bottom' v-fold>
			<el-tabs v-model="activeName">
		    	<el-tab-pane label="资料列表" name="first">
					<xpt-list :data='auxiliaryList'
						:colData='colData'
						searchHolder='请输入辅助资料名称'
						:pageTotal='pageTotal'
						:btns='btns'
						@search-click='searchCondition'
						@selection-change='select'
						@page-size-change='pageSizeChange'
						@current-page-change='pageChange'
						ref='xptList'>
              <xpt-upload-v3
                slot="btns"
                uploadBtnText="导入"
                :uploadSize="20"
                acceptTypeStr=".xlsx,.xls"
                :dataObj="{}"
                :disabled="!auxiliary.id"
                :ifMultiple="false"
                :showSuccessMsg="false"
                @uploadSuccess="uploadSuccess"
                btnType="success"
                style="display: inline-block; margin: 0px 10px"
              ></xpt-upload-v3>
					</xpt-list>
		    	</el-tab-pane>
		  	</el-tabs>
	  	</div>
	</div>
</template>
<script>
	import vl from '@common/validate.js'
	export default {
		data(){
			let self = this
			return {
				auxiliary:{
					// 类别编码
					code:'',
					// 类别名称
					name:'',
					// 系统
					status:false,
					// 平台
					platform:'',
					// 备注
					remark:'',
					// 父类别
					parentCode:'',
					// 父类别名
					parentName: '',
					// ID
					id:''
				},
				rules:{
					code:vl.auxiliaryCode({
						required:true,
						self:self,
						msg:'类别编码必须是大写字母、数字、_、-的组合格式，必填。'
					}),
					name:[{
						required:true,
						message:'请输入类别名称',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}],
					platform:vl.isNotBlank({
						self:self,
						msg:'请选择平台'
					})
				},
				activeName:'first',
				pageNow:1,
				pageTotal:0,
				// pageSize:10,
				auxiliaryList:[],
				search:'',
				// 多选值
				selects:'',
				onAjax:false,
				colData:[
					{
						label:'编码',
						prop:'code',
						redirectClick(d){
							self.mod(d)
						}
					},{
						label:'名称',
						prop:'name'
					},{
						label:'上级资料',
						prop:'parentName'
					},{
						label:'描述',
						prop:'remark'
					},{
						label:'生效时间',
						prop:'enableTime',
						format:'dataFormat1',
            width: 130,
					},{
						label:'失效时间',
						prop:'disableTime',
						format:'dataFormat1',
            width: 130,
					},{
						label:'生效',
						prop:'status',
						format:'statusFilter'
					}, {
						label: '标识',
						prop: 'tag'
					}, {
						label: '扩展1',
						prop: 'ext_field1'
					}, {
						label: '扩展2',
						prop: 'ext_field2'
					}
				],
				btns:[
					{
						type:'primary',
						txt:'新增',
						click(){
							self.mod();
						}
					},{
						type:'success',
						txt:'生效',
						click(){
							self.upd(1)
						}
					},{
						type:'warning',
						txt:'失效',
						click(){
							self.upd(0)
						}
					},{
						type:'danger',
						txt:'删除',
						click(){
							self.del()
						}
					},
          {
            type: 'primary',
            txt: '导入模板下载',
            click() {
              window.open('http://lsmy-devfile.oss-cn-shenzhen.aliyuncs.com/doc/2025-05-215262f13fe1d045d8b9de193583aac5db.xls', '_blank');
            },
          },
          {
            type: 'warning',
            txt: '导入结果',
            click() {
              self.showUploadResult();
            }
          },
				]
			}
		},
		methods:{
			submit(fn,callback){
				this.$refs[fn].validate((valid) => {
					if(valid){
						let self = this;
						let data = {};
						data.categoryPo = {
							code:this.auxiliary.code,
						    name:this.auxiliary.name,
						    parentCode:this.auxiliary.parentCode,
						    platform:this.auxiliary.platform||'NEW_SALE_PLATFORM',
						    remark:this.auxiliary.remark,
						    system:this.auxiliary.status?1:0,
						}
						if(this.auxiliary.id){
							data.categoryPo.id = this.auxiliary.id;
						}
						data.dataList = [];
						if(this.auxiliaryList.length){
							this.auxiliaryList.find(d=>{
								d.categoryCode = data.categoryPo.code;
								data.dataList.push({
									categoryCode:data.categoryPo.code,
								    code:d.code,
								    disableTime: d.disableTime ? new Date(d.disableTime).getTime() : null,
								    enableTime: d.enableTime ? new Date(d.enableTime).getTime() : null,
								    id:(d.id + '').indexOf('temp') === -1 ? d.id : '',
								    name:d.name,
								    parentCode:d.parentCode,
								    platform:d.platform||'NEW_SALE_PLATFORM',
								    status:d.status?1:0,
								    remark:d.remark,
								    ext_field1:d.ext_field1,
								    ext_field2:d.ext_field2,
								    tag: d.tag,
								    parentCategoryCode: d.parentCategoryCode
								})
							})
						}
						this.onAjax = true;
						this.ajax.postStream('/user-web/api/auxiliary/saveAuxiliaryCategory?permissionCode=AUXILIARY_DATA',data,d=>{
							self.$message({
								message:d.body.msg,
								type:d.body.result?'success':'error'
							})
							if(d.body.result){
								// 更新辅助资料列表
								self.$root.eventHandle.$emit('auxiliaryCategoryListRefresh')
								// 更新本地辅助资料
								self.$root.eventHandle.$emit('updateAuxiliary')
								if(d.body.content){
									self.auxiliary.id = d.body.content;
									self.$root.eventHandle.$emit('updateTab',{
										name:self.params.tabName,
										title:'编辑辅助资料类别'
									})
									self._getAuxiliaryList();
									this.params.__data.auxiliary = JSON.parse(JSON.stringify(this.auxiliary));
								}
							}
							self.onAjax = false;
							callback&&callback();
						},e=>{
							self.onAjax = false;
							self.$message.error(e);
						})
					}
				})
			},
			close(){
                let isUpdate = this.compareData({
	                    auxiliary:this.auxiliary,
	                    list:this.auxiliaryList
	                }, this.params.__data);
				if(isUpdate){
					let self = this;
					this.$root.eventHandle.$emit('openDialog',{
						ok(){
							self.submit('auxiliary',()=>{
								self.$root.eventHandle.$emit('removeTab',self.params.tabName)
							})
						},
						no(){
							self.$root.eventHandle.$emit('removeTab',self.params.tabName)
						}
					})
				}else{
					this.$root.eventHandle.$emit('removeTab',this.params.tabName)
				}
			},
			// 校验是否有相同的编码
			checkCode(id, code) {
				let list = this.auxiliaryList || [],
					i = list.length;
				while(i--) {
					if(list[i].id != id && list[i].code == code) {
						this.$message.error('已有相同的编码');
						return false;
					}
				}
				return true;
			},
			mod(obj){
				var params = {};
				if(obj){
					params = JSON.parse(JSON.stringify(obj))
				}else{
					params.categoryCode = this.auxiliary.code
					params.categoryName = this.auxiliary.name
					params.parentCategoryCode = this.auxiliary.parentCode
					params.parentCategoryName = this.auxiliary.parentName
				}
				let self = this;
				params.callback = function(d){
					let i = self.auxiliaryList.length,
						isAdd = true;
					while(i--){
						if(self.auxiliaryList[i].id === d.id) {
							if(self.checkCode(d.id, d.code)) {
								self.auxiliaryList[i].name = d.name;
								self.auxiliaryList[i].disableTime = d.disableTime;
								self.auxiliaryList[i].enableTime = d.enableTime;
								self.auxiliaryList[i].parentCode = d.parentCode;
								self.auxiliaryList[i].parentName = d.parentName;
								self.auxiliaryList[i].remark = d.remark;
								self.auxiliaryList[i].status = d.status ? 1 : 0;
								self.auxiliaryList[i].tag = d.tag;
								self.auxiliaryList[i].code = d.code;
								self.auxiliaryList[i].ext_field1 = d.ext_field1;
								self.auxiliaryList[i].ext_field2 = d.ext_field2;
							}
							isAdd = false;
							break;
						}
					}
					isAdd && self.checkCode(d.id, d.code) && self.auxiliaryList.push(d);
				}
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/auxiliary/add'),
                	style:'width:800px;height:500px',
                    title:obj?'编辑辅助资料':'新增辅助资料',
					params:params
            	});
			},
			// 批量删除
			del(){
				if(!this.selects.length){
					this.$message({
						message:'请选择要删除的资料',
						type:'error'
					})
					return;
				}
				let obj = {},
					data = [],
					newData = {};
				;(this.selects||[]).find(d=>{
					obj[d.code] = true
					if((d.id + '').indexOf('temp') === -1) {
						data.push(d.id)
					} else {
						newData[d.code] = true;
					}
				});
				// 删除新增
				let i = this.auxiliaryList.length;
				while(i--){
					if(newData[this.auxiliaryList[i].code]){
						this.auxiliaryList.splice(i,1);
					}
				};
				if(data.length){
					let self = this;
					this.ajax.postStream('/user-web/api/auxiliary/deleteAuxiliaryDataBatch?permissionCode=AUXILIARY_DATA',{
						idList:data
					},d=>{
						self.$message({
							message:d.body.msg,
							type:d.body.result?'success':'error'
						})
						if(d.body.result){
							let i = self.auxiliaryList.length;
							while(i--){
								if(obj[self.auxiliaryList[i].code]){
									self.auxiliaryList.splice(i,1)
								}
							}
						}
						this.params.__data.list = JSON.parse(JSON.stringify(this.auxiliaryList));
						self.selects = [];
						self.$refs.xptList.clearSelection();
					},e=>{
						self.$message({
							message:e.statusText,
							type:'error'
						})
						self.$refs.xptList.clearSelection();
					})
				}
			},
			// 更新状态
			upd(s){
				if(!this.selects.length){
					this.$message({
						message:'请选择要删除的资料',
						type:'error'
					})
					return;
				}
				this.selects.find(d=>{
					d.status = s?true:false
				})
				this.selects = [];
				this.$refs.xptList.clearSelection();
			},
			select(s){
				this.selects = s;
			},
			searchCondition(s){
				this._getAuxiliaryList({
					key:s
				})
			},
			pageChange(page){
				this.pageNow = page;
				this._getAuxiliaryList();
			},
			pageSizeChange(pageSize){
				this.pageSize = pageSize;
				this._getAuxiliaryList();
			},
			openAuxiliaryParent(){
				if(this.auxiliary.id) return;
				let self = this;
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/auxiliary/auxiliaryCategoryList'),
                	style:'width:800px;height:500px',
                    title:'辅助资料类别列表',
					params:{
						callback(d){
							self.auxiliary.parentCode = d.code
							self.auxiliary.parentName = d.name
						}
					}//传参过去
            	});
			},
			parentCodeChange(s){
				if(!s){
					this.auxiliary.parentCode = ''
					this.auxiliary.parentName = ''
				}
			},
			_getAuxiliaryList(data){
				if(this.auxiliary.id){
					let self = this;
					data = data || {};
					data.categoryCode = this.auxiliary.code;
					data.page = {
						length:this.pageSize,
						pageNo:this.pageNow
					}
					if(this.params.isAlert){
					    data.status = 1;
						data.isEnable = 1;
					}
					this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryDataList',data,(d)=>{
						if(d.body.result&&d.body.content){
							self.auxiliaryList = d.body.content.list||[];
							self.pageTotal = d.body.content.count;
							this.params.__data.list = JSON.parse(JSON.stringify(this.auxiliaryList));
						}
					},(e)=>{
						self.$message({
							message:e.statusText,
							type:'error'
						})
					})
				}
			},

      //上传成功返回结果
      uploadSuccess(result) {
        if (result.length > 0 && !!result[0].path) {
          this.importFileUrl(result[0].path);
        }
      },

      // 导入关联商品
      importFileUrl(fileUrl) {
        let params = {
          categoryCode: this.auxiliary.code,
          platform: this.auxiliary.platform,
          path: fileUrl,
        };
        this.ajax.postStream(
          "/user-web/api/auxiliary/addImport",
          params,
          (res) => {
            if (res.body.result) {
              this.$message.success(res.body.msg);
            } else {
              this.$message.error(res.body.msg);
            }
          },
          (err) => {
            this.$message.error(err);
          }
        );
      },

      //导入结果
      showUploadResult() {
        this.$root.eventHandle.$emit("alert", {
          style: "width:900px;height:600px",
          title: "导入结果",
          params: {
            url: "/reports-web/api/reports/afterSale/findMyReportList",
            data: {
              page_size: this.page_size,
              page_no: this.page_no,
              type: "CLOUD_AUXILIARY_DATA_IMPORT",
            },
            showDownload: true,
          },
          component: () => import("@components/common/eximport"),
        });
      },
		},
		props:['params'],
		mounted(){
			this.auxiliary = {
				// 类别编码
				code:this.params.code||'',
				// 类别名称
				name:this.params.name||'',
				// 系统
				status:this.params.system?true:false,
				// 平台
				platform:this.params.platform||'NEW_SALE_PLATFORM',
				// 备注
				remark:this.params.remark||'',
				// 父类别
				parentCode:this.params.parentCode||'',
				parentName:this.params.parentName||'',
				// ID
				id:this.params.id||''
			}
			this._getAuxiliaryList();
			// 更新辅助资料列表
			this.$root.eventHandle.$emit('auxiliaryCategoryListRefresh')
			// 更新本地辅助资料
			this.$root.eventHandle.$emit('updateAuxiliary')
			this.params.__data = {
				auxiliary: JSON.parse(JSON.stringify(this.auxiliary)),
				list: JSON.parse(JSON.stringify(this.auxiliaryList))
			};
			this.params.__close = this.close;
		}
	}
</script>
