<!-- 辅助资料类别列表 -->
<template>
	<xpt-list :data='categoryList' 
		:colData='colData' 
		:searchPage='query.page_name' 
		:pageTotal='pageTotal' 
		:btns='btns' 
		:selection='selection' 
		@search-click='searchData' 
		@selection-change='select' 
		@radio-change='radioSelect' 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		ref='xptList'>
	</xpt-list>
</template>
<script>

export default {
	data(){
		let self = this;
		return {
			categoryList:[],
			pageNow:1,
			pageTotal:0,
			// pageSize:10,
			// 单选值
			selectCode:'',
			// 多选值
			selects:'',
			search:'',
			colData:[
				{
					label:'类别编码',
					prop:'code',
					width:370,
					redirectClick(d){
						self.mod(d)
					}
				},{
					label:'类别名称',
					width:370,
					prop:'name'
				},{
					label:'平台',
					width:370,
					prop:'platform',
					format:'auxFormat',
					formatParams:'platform'
				},{
					label:'类别',
					width:370,
					prop:'system',
					format:'systemFilter'
				},{
					label:'描述',
					width:370,
					prop:'remark'
				}
			],
			btns:[
				{	
					type:'primary',
					txt:'新增',
					click(){
						self.mod();
					}
				},{
					type:'danger',
					txt:'删除',
					loading: false,
					click(){
						self.del()
					}
				}
			],
			selection:"checkbox",
			query: {
				page: {
					length: self.pageSize,
					pageNo: 1,
				},
				where: [],
				page_name: 'cloud_auxiliary_category'
			}
		}
	},
	methods:{
		// 多选
		select(s){
			this.selects = s;
		},
		radioSelect(s){
			this.selectCode = s;
		},
		// 新增、编辑辅助资料
		mod(obj){
			var params = obj||{}
        	this.$root.eventHandle.$emit('creatTab',{
        		name:obj?'编辑辅助资料类别':'新增辅助资料类别',
        		params:params,
        		component:()=>import('@components/auxiliary/auxiliary.vue')
        	})
		},
		// 批量删除
		del(){
			let data = [],
				obj = {},
				self = this;
			(this.selects||[]).find(d=>{
				data.push(d.id);
				obj[d.id] = 1;
			});
			if(!data.length){
				this.$message({
					message:'请选择要删除的辅助资料类别',
					type:'error'
				})
				return
			};
			this.btns[1].loading = true;
			this.ajax.postStream('/user-web/api/auxiliary/deleteAuxiliaryCategoryBatch?permissionCode=AUXILIARY_DATA',{
				idList:data
			},d=>{
				self.$message({
					message:d.body.msg,
					type:d.body.result?'success':'error'
				})
				if(d.body.result){
					let i = self.categoryList.length;
					while(i--){
						if(obj[self.categoryList[i].id]){
							self.categoryList.splice(i,1)
						}
					}
				}
				self.selects = [];
				self.$refs.xptList.clearSelection();
				this.btns[1].loading = false;
			},e=>{
				this.btns[1].loading = false;
				self.$message.error(e)
			})
		},
		pageSizeChange(pageSize){
			this.query.page.length = pageSize;
			this._getCategoryList()
		},
		pageChange(page){
			this.query.page.pageNo = page;
			this._getCategoryList();
		},
		searchData(s, resolve){
			this.query.where = s;
			this._getCategoryList(resolve);
		},
		_getCategoryList(resolve){
			this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryCategoryList', this.query, (d)=>{
				if(d.body.result&&d.body.content){
					this.categoryList = d.body.content.list||[];
					this.pageTotal = d.body.content.count;
				} else {
					this.$message.error(d.body.msg || '')
				}
				resolve && resolve();
			},(e)=>{
				this.$message({
					message:e,
					type:'error'
				})
				resolve && resolve();
			})
		}
	},
	props:['params'],
	mounted(){
		if(this.params.isAlert){
			this._getCategoryList();
		}
		let self = this;
		if(this.params.isAlert){
			this.btns = [{
				type:'primary',
				txt:'确认',
				click(){
					if(!self.selectCode){
						self.$message({
							message:'请选择辅助资料类别',
							type:'error'
						})
						return;
					}
					if(self.params.isAdd){
						// 把所选辅助资料编码返回
						self.params.callback(self.selectCode)
					}else{
						// 把所选辅助资料编码返回
						self.params.callback(self.selectCode)
					}
					// 关闭弹窗
					self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
				}
			}];
			this.selection = 'radio';
			this.colData[0].redirect = false
		}


		this.$root.eventHandle.$on('auxiliaryCategoryListRefresh',()=>{
			self._getCategoryList();
		})
	},
	destroyed(){
		this.$root.offEvents('auxiliaryCategoryListRefresh');
	}
}
</script>