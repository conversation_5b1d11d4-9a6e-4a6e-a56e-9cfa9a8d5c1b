<!--辅助资料详情--资料列表-->
<template>
	<xpt-list :data='auxiliaryList'
		:colData='colData'
		searchHolder='请输入辅助资料类别名称'
		:pageTotal='pageTotal'
		:btns='btns'
     :radioGreyDisabled="selectable"
		:selection='selection'
		@search-click='searchCondition'
		@radio-change='select'
        @selection-change='select'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'>
	</xpt-list>
</template>
<script>
	export default {
		data(){
			let self = this;
			return {
                selection: 'radio',
				search:'',
				typeList:[],
				auxiliaryList:[],
				showSearch:false,
				pageNow:1,
				pageTotal:0,
				// pageSize:10,
				// 新增资料的父类别编码
				categoryCode:'',
				// 单选选中的列
				selectCode:'',
				colData:[
					{
						label:'编码',
						prop:'code',
					},{
						label:'名称',
						prop:'name'
					},{
						label:'上级资料',
						prop:'parentName'
					},{
						label:'描述',
						prop:'remark'
					},{
						label:'生效时间',
						prop:'enableTime',
						format:'dataFormat'
					},{
						label:'失效时间',
						prop:'disableTime',
						format:'dataFormat'
					},{
						label:'生效',
						prop:'status',
						format:'statusFilter'
					}
				],
				btns:[
					{
						type:'primary',
						txt:' 确认',
						click(){
							self.close();
						}
					}
				]
			}
		},
		props:['params','code'],
		methods:{
      selectable(e){
        if(this.params.radioDisabledFunByAuxiliary){
          return this.params.radioDisabledFunByAuxiliary(e)
        }
        return false
      },
			select(s){
				this.selectCode = s;
			},
			// 搜索
			searchCondition(s){
				this._getAuxiliaryList({
					key:s
				})
			},
			// 关闭弹窗
			close(){
				if(!this.selectCode || (this.selection == 'selection' && this.selectCode.length == 0)){
					this.$message({
						message:'请选择辅助资料',
						type:'error'
					})
					return;
				}
				if (this.params.isAlert && this.params.categoryCode == 'OFFLINE_PAYMENT_MODE' && this.params.selection == 'checkbox' && this.selectCode.length > 1) {
					let ifIegal = this.selectCode.some(item => {
						return item.code == 'EMPTY'
					})
					if(ifIegal){
						this.$message({
							message:'线下货款模式为“不限”时，不能选择其他模式。',
							type:'error'
						})
						return;
					}
				}
				this.params.callback(this.selectCode)
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
			pageChange(page){
				this.pageNow = page;
				this._getAuxiliaryList();
			},
			pageSizeChange(pageSize){
				this.pageSize = pageSize;
				this._getAuxiliaryList();
			},
			_getAuxiliaryList(data){
				data = data ||{}
				data.page = {
					length:this.pageSize,
					pageNo:this.pageNow
				}
				if(this.categoryCode){
					data.categoryCode = this.categoryCode;
				}
                if(this.params.isAlert){
                    data.status = 1;
                    data.isEnable = 1;
                }
				let self = this;
				this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryDataList',data,(d)=>{
					if(d.body&&d.body.result&&d.body.content){
						self.auxiliaryList = d.body.content.list||[];
						self.pageTotal = d.body.content.count||0;
					}
				},(e)=>{
					self.$message({
						message:e.statusText,
						type:'error'
					})
				})
			}
        },
        created(){
            this.selection = this.params.selection == 'checkbox' ? 'checkbox' : 'radio'
        },
		mounted(){
			this.categoryCode = this.params.categoryCode;
			this._getAuxiliaryList();
		}
	}
</script>
