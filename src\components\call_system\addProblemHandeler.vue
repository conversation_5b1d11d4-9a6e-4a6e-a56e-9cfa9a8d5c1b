<!--新增问题处理人-->
<template>
	<div>
		<xpt-headbar>
			<el-button size="mini" type="primary" @click="add" slot='left'>保存</el-button>
			<!-- <el-button size="mini" type="primary" @click="getPersonList" style="margin-top: 5px;">搜索</el-button> -->
		</xpt-headbar>
		<el-form ref='query'  label-position="right" label-width="120px">
			
			<el-col :span='8'>
				<el-form-item label="分组" prop='groupName'>
					<el-input  size="mini"  v-model="data.groupName" icon="search" :on-icon-click="selectGroup" placeholder="请输入分组名" :disabled="!!this.params.groupName"></el-input>
				</el-form-item>
				<el-form-item label="问题优先处理级" prop='handlerOrder'>
					<el-select
						v-model="data.handlerOrder"
						size="mini"
						placeholder="请选择"
						>
							<el-option v-for="(value,key) in handlerOrderItem" :key="key" :label="value" :value="key"></el-option>
							
						</el-select>
				</el-form-item>
			</el-col>
			<el-col :span='8'>
				<el-form-item label="角色" prop='handlerRole'>
					<!-- <el-input size="mini"  v-model="data.handlerRole" placeholder="请输入角色"></el-input> -->
					<el-select
						v-model="data.handlerRole"
						size="mini"
						placeholder="请选择角色"
						>
							<el-option v-for="(value,key) in handlerRoleItem" :key="key" :label="value" :value="key"></el-option>
							
						</el-select>
				</el-form-item>
				<el-form-item label="人员名称" prop='memberName' >
					<el-input  size="mini"  v-model="data.memberName" placeholder="请输入人员名称" icon="search" :on-icon-click="searchPerson"></el-input>
				</el-form-item>
			</el-col>
		
			
		</el-form>

	</div>
</template>
<script>
	export default {
        props:['params'],
		data(){
			let self = this;
			
			return {
				data:{
					// "handlerId":"",                  //主键ID
					"groupName":"",           //分组名
					"groupId":"",           //分组ID
					"handlerRole":"",     //角色
					"memberName":"",              //成员名称
					"employeeNumber":"",       //工号
					"handlerOrder":""              //序号
				},
				handlerRoleItem:{
					SHOP_OWNER:"店长",
					CALL_ASSISTANT:"话务员",	
				},
				handlerOrderItem:{
					1:"一级",
					2:"二级",
					3:"三级",
					4:"四级",
					5:"五级",
					6:"六级",
					7:"七级",
					8:"八级",
					9:"九级",
					10:"十级",
				}
			}
		},
		methods:{
			selectGroup() {
				if(this.params.groupName){
					return
				}
				var _this = this;
				// 打开分组选择列表的时候传参，给分组列表添加查询参数
				let addParams = [
				{
					name:'type',
					value:'TRAFFIC'
				}
				]
				this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/per_sales_report/select_group.vue'), close: function() {

				},
				style: 'width:900px;height:600px',
				title: '选择分组',
				params: {
					isAddParams:true,
					notNeedReportEmbody:true,
					addParams,
					callback(b) {
					_this.data.groupId = b.id;
					_this.data.groupName = b.name;
					},
				},
				});

			},
			add(resolve){
				
                var url = '/callcenter-web/api/cloudShopProblem/save.do';
                
                var data = this.data;
                
				var _this = this;
                this.ajax.postStream(url,data,function(d){
					var res = d.body;
					if(res.result){
						_this.$message.success(res.msg || '');
						_this.params.close();
                    	_this.$root.eventHandle.$emit('removeAlert',_this.params.alertId);

					} else {
						_this.$message.error(res.msg || '');
					}
					// resolve && resolve();
                },function(data){
                    // resolve && resolve();
                })
			},
			searchPerson() {
				var _this = this;
				this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/system_management/list.vue'), 
				style: 'width:900px;height:600px',
				title: '请选择用户信息',
				params: {
					type: 'EMPLOYEE',
					// status: 1,//生效人员
					isEnable: 1,//生效时间
					page_name: 'cloud_user_person',
					where: [],
					callback(b) {
						var data = b.data;
						// console.log(data,"data");
						_this.data.memberName = data.personFullName;
						_this.data.employeeNumber = data.employeeNumber;
					},
				},
				});
			},
           
          
           
          
           
		},
		mounted() {
			//从其它组件里传过来的参数，调用接口
			var _this = this;
           this.params.groupName&& (this.data.groupName=this.params.groupName)
		},
	
       
	}
</script>
