
import Vue from 'vue'

let baseUrl = '';
if (process.env.NODE_ENV == 'development') {
  baseUrl = '';
}
let eventBus = new Vue();

export const EventBus =  {
  emit(eventName,...args){
    eventBus.$emit(eventName, ...args);
  },
  on(eventName,callBack){
    eventBus.$on(eventName, callBack);
  },
  off(eventName,callBack){
    eventBus.$off(eventName, callBack);
  },
  order_saved:"orderSaved",
  call_system_make_call:"callSystemMakeCall",
}

export default baseUrl;

export const apiUrl = {
  employee_TelStatus: `/callcenter-web/personGroup/getByStaffName.do`, //获取员工的固话状态
  workOrder_get: `/callcenter-web/workOrder/get.do`, // 工单详情
  workOrder_mark: `/callcenter-web/workOrder/mark.do`,
  workOrder_updateApproved: `/callcenter-web/workOrder/updateApproved.do`,
  workOrder_updateInvalid: `/callcenter-web/workOrder/updateInvalid.do`,
  workOrder_getNonClose: `/callcenter-web/workOrder/getNonClose.do`,
  workOrder_add: `/callcenter-web/workOrder/add.do`,
  workOrder_update: `/callcenter-web/workOrder/update.do`,
  workOrder_delete: `/callcenter-web/workOrderProcessLog/delete.do`,
  workOrder_export: `/callcenter-web/workOrder/export.do`,
  workOrder_detailTableTow: `/callcenter-web/callStat/listCallHistory.do`,
  workOrder_recordDetalis: `/callcenter-web/workOrder/list.do`, // 工作台的工单列表
  workOrder_operateList: `/callcenter-web/workOrderOperation/list.do`,
  workOrderProcessLog_list: "/callcenter-web/workOrderProcessLog/list.do",
  workOrderProcessLog_listWithOrderNo:
    "/callcenter-web/workOrderProcessLog/listWithOrderNo.do",
  callRecordListFilterJobNumber: "/callcenter-web/callStat/listCallHistory.do", // 呼叫记录全局搜索手机电话
  callEventLog_add: "/callcenter-web/callEventLog/add.do",

  /*漏接*/
  missCall_CurrentMissCall: `/callcenter-web/missCall/listCurrentMissCall.do`, // 漏接来电
  missCall_Submitted: `/callcenter-web/missCall/listSubmittedMissCall.do`, // 已提交
  missCall_Returned: `/callcenter-web/missCall/listReturnedMissCall.do`, // 已回拨
  missCall_Before: `/callcenter-web/missCall/listMissCallBackLog.do`, // 以前的漏接
  missCall_CallHistoryContent: `/callcenter-web/workOrderProcessLog/list.do`, // 历史记录
  missCall_MissCallSubmit: `/callcenter-web/missCall/submit.do`, // 漏接来电的提交审核
  missCall_DetailInfo: `/callcenter-web/missCall/listMissCallBackLog.do`, // 已提交、已回拨 => 详细信息
  missCall_Check: `/callcenter-web/missCall/close.do`, // 审核（已提交 => 审核 => 已回拨）
  /*新增通话记录*/
  callStat_add: "/callcenter-web/callStat/add.do",
  callStat_export: "/callcenter-web/callStat/export.do",
  callStat_exportSearch: "/callcenter-web/callStat/exportSearch.do",
  /*统计报表*/
  month_work: `/callcenter-web/report/listStaffRptByMonth.do`, // 话务月工作统计报表(话务月汇总)
  month_summary: `/callcenter-web/report/listRptByMonth.do`, // 月汇总报表（月汇总）
  sta_report: `/callcenter-web/report/listStaffRptByDay.do`, // 话单统计报表(话务日汇总)
  service_sta_report: `/callcenter-web/report/listRptByService.do`, // 服务统计,
  satisfaction_rating_report: `/callcenter-web/report/callSatisfactionRpt.do`, //满意度调查报表
  agentStatus_report: `/callcenter-web/agentStatus/list.do`, //客服在线时长报表
  agentStatus_online: `/callcenter-web/agentStatus/online.do`, //话务在线状态报表

  month_detailed_sta: `/callcenter-web/report/listRptByMonthDtl.do`, // 月明细报表
  updateAgentStatus: "/callcenter-web/agentStatus/upate.do", // 更新坐席状态
  personGroup_list: "/callcenter-web/personGroup/list.do",
  // 测试用
  personGroup_list1: "/callcenter-web/callStat/crm/add.do",
  personGroup_list_new: "/callcenter-web/personGroup/listSelect.do",
  personGroup_add: "/callcenter-web/personGroup/add.do",
  personGroup_update: "/callcenter-web/personGroup/update.do",
  personGroup_delete: "/callcenter-web/personGroup/delete.do",

  /** 其它页面添加呼叫功能 */
  callLog_add: "/callcenter-web/callLog/add.do",
  callLog_list: "/callcenter-web/callLog/list.do",
  callLog_count: "/callcenter-web/callLog/count.do",

    /**新版页面添加呼叫功能 */
    callLog_list_pro: "/order-web/api/callCenter/queryCallRecordList",
    callLog_count_pro: "/order-web/api/callCenter/queryCallRecordCount",

  /** 快捷短语，短信模板 模块迁移到新平台 */
  smsQuick_GetList: "/file-web/api/smsUsefulWordings/list.do", //获取快捷短语列表
  smsQuick_DeleteItem: "/file-web/api/smsUsefulWordings/delete.do", //删除快捷短语
  smsQuick_AddItem: "/file-web/api/smsUsefulWordings/add.do", //新增快捷短语
  smsQuick_UpdateItem: "/file-web/api/smsUsefulWordings/update.do", //更新快捷短语

  smsTmpl_GetList: "/file-web/smsTmpl/list.do", //获取短信模板列表
  smsTmpl_AddItem: "/file-web/smsTmpl/add.do", //新增短信模板
  smsTmpl_UpdateItem: "/file-web/smsTmpl/update.do", //更新短信模板

  /** 呼叫记录同步 */
  callStat_dataSync: "/callcenter-web/doSyncCtiCallstat/syncCallStat.do", //手工同步

  /** 报表中心 */
  reportCenter_manualSync: "/callcenter-web/doSyncCtiCallstat/insert.do", //手工同步

  /** 发送短信 */
  send_msg: "/callcenter-web/satisfactionSurvey/sendMsg.do",
  /** 外包话务-月统计 */
  staffRpt_month_work: "/callcenter-web/report/crm/listStaffRptByMonth.do",
  /** 话务工单转交 */
  call_order_exchangeTest: "/callcenter-web/workOrder/deliver.do",
};

export function makeUrl(url, param) {
  url += '?';
  for (let i in param) {
    if (param[i] || 0 === param[i]) {
      url += i + '=' + encodeURIComponent(param[i]) + '&';
    }
  }

  let location = url.lastIndexOf('&');
  if (location > -1) {
    url = url.substr(0, url.length - 1);
  }

  return url;
}

export function makeParam(param) {
  let ret = {};
  for (let i in param) {
    if (param[i]) {
      ret[i] = param[i];
    }
  }

  return ret;
}
