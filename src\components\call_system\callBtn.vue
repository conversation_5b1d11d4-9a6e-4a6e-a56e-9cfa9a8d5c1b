<template>
  <div>
    <el-button
      v-for='(btn,index) in btns'
      :type='btn.type'
      size='mini'
      @click='e => btn.click && btn.click(index)'
      :disabled="btn.show" :key="index">
      {{btn.txt}}
    </el-button>
  </div>
</template>

<script>
export default {
  props: {
    btns: {
      type: Array,
      default () {
        return [
          {
            type: 'primary',
            txt: '新增',
            click (index) {
              console.log('新增')
            },
            show: true
          }
        ]
      }
    }
  }
}
</script>

<style scoped>
.call-btn div{
  display: inline-block;
  margin-right:6px;
}
</style>
