<template>
  <div class="call-history" v-loading="loading">
    <el-table
      :data="tableData"
      border
      style="width: 100%;min-height:100px;">
      <el-table-column type="index" label="序号"></el-table-column>
      <el-table-column prop="calledType" label="呼叫类型"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="staffName" label="处理人"></el-table-column>
      <el-table-column prop="staffNo" label="工号"></el-table-column>
      <el-table-column prop="remark" label="处理内容" class-name="remarkClassByCallCenterCallHistory" min-width="350">
        <template slot-scope="scope">
          <p>{{ scope.row.remark }}</p>
<!--          <el-popover trigger="hover" placement="top" popper-class="table-two-popperContent">-->
<!--            <p>{{ scope.row.remark }}</p>-->
<!--            <div class="name-wrapper">{{ scope.row.remark}}</div>-->
<!--          </el-popover>-->
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="processTime" label="处理时间"></el-table-column>
    </el-table>
    <div style="text-align: right">
      <el-pagination layout="total, sizes, prev, pager, next"
                     :page-sizes="[200, 400, 600]"
                     :page-size="pageSize"
                     :total="totalNum"
                     :current-page="pageNow"
                     @size-change="pageSizeChange"
                     @current-change="currentChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
  import baseUrl, {makeUrl} from './base.js';
  import {apiUrl} from './base';

  export default {
    name: 'callHistory',
    props: ['job'],
    data() {
      return {
        loading: false,
        pageSize: 200,
        totalNum: 0,
        pageNow: 1,
        tableData: [],
      };
    },
    methods: {
      pageSizeChange(pageSize){
        this.pageSize = pageSize;
        this.getHistoryList();
      },
      currentChange(page) {
        this.pageNow = page;
        this.getHistoryList()
      },
      // 检索呼叫记录内容
      getHistoryList() {
        this.loading = true;
        const params = {jobId: this.job.jobId,mergeTradeNo:this.job.mergeTradeNo, pageSize: this.pageSize, pageNo: this.pageNow};
        this.$http.get(apiUrl.workOrderProcessLog_listWithOrderNo, {params}).then((res) => {
          if (res.data && res.data.code === 0) {

            if (this.job.mergeTradeNo && res.data.content.length === 0){
              this.getHistoryListAddParmas();
              return
            }

            this.tableData = res.data.content;
            this.totalNum = res.data.totalNum;
            this.loading = false;

          } else {
            this.loading = false;
            // this.$message.error(res.data.msg);
          }
        }).catch(err => {
          console.error(error)
          this.loading = false;
          this.$message.error(err);
        }).finally(() => {
          // this.loading = false;
        });
      },
      // 当传过来的Job对象含有合并订单相关信息，但是还是查不到记录的时候，给参数添加一个手机号
      getHistoryListAddParmas(){
        const params = {
          jobId: this.job.jobId,
          // mergeTradeNo:this.job.mergeTradeNo,
          callNumber:this.job.calledNumber,
          pageSize: this.pageSize,
          pageNo: this.pageNow,
        };
        this.$http.get(apiUrl.workOrderProcessLog_listWithOrderNo, {params}).then((res) => {
          if (res.data && res.data.code === 0) {
            this.tableData = res.data.content;
            this.totalNum = res.data.totalNum;
          } else {
            this.$message.error(res.data.msg);
          }
        }).catch(err => {
          console.error(error)
          this.$message.error(err);
        }).finally(() => {
          this.loading = false;
        });
      }
    },
    mounted() {
      // this.getHistoryLsit()
    },
  };
</script>

<style>
  .table-two-popperContent{
    max-width: 450px;
  }
  .table-two-popperContent>p{
    line-height: 20px;
    font-size: 13px !important;
  }
</style>
<style scoped>
  .call-history {
    width: 100%;
    height: 88%;
  }
</style>

<style>
  .call-history .remarkClassByCallCenterCallHistory{
    height: 100%!important;
  }
  .call-history .remarkClassByCallCenterCallHistory .cell{
    height: 100% !important;
  }
  .call-history .remarkClassByCallCenterCallHistory p{
    white-space: pre-line;
  }
</style>
