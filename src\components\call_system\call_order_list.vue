<!-- 工单记录列表 -->
<template>
  <div
    id="call_order_list"
    style="height:100%;"
  >
  <xpt-headbar>
    <xpt-btngroup type="success" :btngroup='callBtnGroups' :beforeClick="beforeClick" :disabled="callBtnGroupsDisabled" width='60px' class="mgl10"  size="mini" slot='left'></xpt-btngroup>
    <xpt-btngroup  :btngroup='btnGroupList' width='60px' class="mgl10"  size="mini" slot='left'></xpt-btngroup>
  </xpt-headbar>
    <callOrderSearch @handle-order-search="tabeSearch"></callOrderSearch>
    <xpt-list
      ref="callRecordList"
      selection="radio"
      :btns="btns"
      :colData="cols"
      :data="tableData"
      :pageLength="search.page_size"
      :pageTotal="totalPage"
      :showHead="false"
      @radio-change='radioChange'
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    >
      <template slot="listSlot" slot-scope='data'>
        <textarea
          class="changeValue"
          style="vertical-align:middle"
          :placeholder='data.data.replyMsg.replyMsg?data.data.replyMsg.replyMsg:"暂无回复"'
          name="description"
          cols="30"
          rows="3"
          v-model="data.data.replyMsg.replyMsg"
          :readonly='data.data.isApproved == "1" ? true : false'
        ></textarea>
      </template>
      <template slot='remark' slot-scope='scope'>
        <span class="text-align-left">{{scope.row.remark}}</span>
      </template>
    </xpt-list>
  </div>
</template>

<script>
import { apiUrl, makeUrl } from "./base.js";
// 混入呼叫信息
import pag_five from '@components/call_system/six_page_callFun/pag_five_orderlist.js'
import callOrderSearch from "@/components/call_system/call_order_search";
import Fn from "@/common/Fn.js";

export default {
  props: ["params"],
  mixins: [pag_five],
  components: {
    callOrderSearch,
  },
  data() {
    let self = this;
    return {
      btns: [],
      cols: [
        {
          label: "工单号",
          width: "80",
          prop: "jobNumber",
        },
        {
          label: "问题处理人",
          prop: "problemHandlerName",
        },
        {
          label: "问题处理人分组",
          width: "110",
          prop: "problemHandlerGroupName",
        },
        {
            label: "合并单号",
            prop: "mergeTradeNo",
            width: "180"
        },
        {
            label: "买家昵称",
            prop: "custName",
        },
        {
            label: "收货人手机号",
            width: "110",
            prop: "receiverPhone",
          format: 'hidePhoneNumber',
        },
        {
            label: "来电号码",
            width: "110",
            prop: "calledNumber",
            format: 'hidePhoneNumber',
        },
        {
            label: "紧急程度",
            prop: "markType",
        },
        {
            label: "工单预约时间",
            width: "110",
            prop: "preHandlerTime",
        },
        {
          label: "客服",
          width: "110",
          prop: "salesUserName",
        },
        {
          label: "工单内容",
          slot: "remark",
          width: "180"
        },
        {
          label: "回复",
          width: "250",
          listSlot: true,
        },
        {
          label: "操作",
          width: "150",
          buttons: [
            {
              type: "success",
              size: "small",
              txt: "审核",
              plain: true,
              loading: false,
              disabled: false,
              isShow: () => {
                return true
              },
              click(row) {
                self.checkSigleOrder(row);
              },
            },
            {
              type: "success",
              size: "small",
              txt: "转交",
              plain: true,
              loading: false,
              disabled: false,
              isShow: () => {
                return true
              },
              click(row) {
                self.exchangeTest(row);
              },
            },
          ],
        },
        {
          label: "审核时间",
          width: "110",
          prop: "approvedTime",
        },
        // {
        //   label: "来电类型",
        //   prop: "callingType",
        // },
        // {
        //   label: "收货人名字",
        //   prop: "2",
        // },
        // {
        //   label: "收货人号码",
        //   prop: "2",
        // },
      ],
      tableData: [],
      SearchData: {},
      params:{
        callBtnfromType: 'sys_order'
      },
      totalPage: 0,
      search: {
        page_no: 1,
        page_size: 200,
        page_name: "cloud_call_stat",
        where: [],
      },
      select: {}, //单选
      isEmployee: false,

      btnGroupList:[
            {
                type:'success',
                txt:'显示号码',
                click(){
                    self.switchPhoneNumberShow(true)
                },
                isDisabled:self.switchPhoneNumberDisabled
            },{
                type:'warning',
                txt:'隐藏号码',
                click(){
                    self.switchPhoneNumberShow(false)
                },
                isDisabled:self.switchPhoneNumberDisabled
            }
        ],
      //查看隐藏号码
      switchPhoneNumberDisabled:false,
      showPhoneNumber:false
    };
  },
  watch: {},
  methods: {
         switchPhoneNumberShow(type){
			if(type&&this.showPhoneNumber){
				this.$message.warning('已显示全部号码')
				return
			}
			this.switchPhoneNumberDisabled = true
			let currentCol=this.cols.map(item=>{
				if(['calledNumber','receiverPhone'].includes(item.prop)){
						type?item.format='':item.format='hidePhoneNumber'
				}
				return item
			})
			this.cols=currentCol
			this.showPhoneNumber=type
			this.switchPhoneNumberDisabled = false
		},
    // 获取用户信息
    getUserPerson() {
      this.ajax.get(
        "/user-web/api/userPerson/getUserPerson/" +
          this.getEmployeeInfo("personId"),
        (res) => {
          if (res.body.result) {
            if (res.body.content.type === "EMPLOYEE") {
              this.isEmployee = true;
              this.getList();
            }
          }
        }
      );
    },
    tabeSearch(data) {
      this.SearchData = data;
      this.$nextTick(() => {
        this.getList();
      })
    },
    // 获取话务工单列表
    getList(resolve) {
      let params = {
        isApproved: "0",
        isEmployee: this.isEmployee,
        pageNo: this.search.page_no,
        pageSize: this.search.page_size,
        ...this.SearchData,
      };
      this.ajax.postStream(
        "/callcenter-web/workOrder/listNew.do",
        params,
        (res) => {
          if (res && res.body.result) {
            this.tableData = res.body.content.list;
            this.totalPage = res.body.content.count;
            this.tableData.map((i, item) => {
              //把isApproved和replyMsg放到replyMsg字段
              i.replyMsg = {
                replyMsg: i.replyMsg,
                isApproved: i.isApproved,
              };
              this.$set(this.tableData, item, i);
            });
            this.$forceUpdate();
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
        }
      );
    },
    // 审核功能
    checkSigleOrder(data) {
      if (data.isApproved == "1") {
        this.$message.error("此工单已审核,无需再审核");
        return;
      }
      let params = {
        jobId: data.jobId,
        replyMsg: data.replyMsg.replyMsg
      };
      this.$http
        .post(
          "/callcenter-web/workOrder/updateApprovedNew.do" +
            "?jobId=" +
            params.jobId +
            "&replyMsg=" +
            params.replyMsg,
          params
        )
        .then(
          (res) => {
            this.$message.success(res.data.msg);
            this.getList();
          },
          (res) => {}
        );
    },
    // 转交功能
    exchangeTest(param) {
      if (param.isApproved == 1 || param.isInvalid == 1) {
        this.$message.error("转交失败，工单已审核或工单无效,");
        return;
      }
      var _this = this;
      this.$root.eventHandle.$emit("alert", {
        component: () =>
          import("@components/call_system/customerHandleList.vue"),
        close: function () {},
        style: "width:900px;height:600px",
        title: "请选择用户信息",
        params: {
          type: "EMPLOYEE",
          // status: 1,//生效人员
          isEnable: 1, //生效时间
          showAllPerson: true,
          page_name: "cloud_user_person",
          where: [],
          callback(b) {
            var data = b.data;
            _this.ajax.postStream(
              apiUrl.call_order_exchangeTest,
              {
                jobId: param.jobId,
                problemHandlerId: data[0].id,
                problemHandlerNo: data[0].employeeNumber,
              },
              (res) => {
                if (res.data && res.data.code === 0) {
                  _this.$message.success("转交成功");
                  _this.getList();
                } else {
                  _this.$message.error(res.data.msg);
                }
              },
              (err) => {
                _this.$message.error(err);
              }
            );
          },
        },
      });
    },
    // 当前条数
    pageSizeChange(ps) {
      this.search.page_size = ps;
      this.getList();
    },
    // 当前页数
    pageChange(no) {
      this.search.page_no = no;
      this.getList();
    },
    // 单选
    radioChange(obj) {
      this.select = obj;
    },
    // 单选确定
    choiceItem() {
      if (!this.select) {
        this.$message.warning("尚未选中相关数据");
        return;
      } else if (this.select.remark) {
        this.$message.error("选中数据已有处理内容，无法关联");
        return;
      }
      let item = JSON.parse(JSON.stringify(this.select));
      this.params.callback(item);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
  },
  mounted() {
    this.getUserPerson();
  },
  destroyed() {},
};
</script>

<style>
#call_order_list .remarkClassByCallCenterCallRecordList {
  height: 100% !important;
}

#call_order_list .remarkClassByCallCenterCallRecordList .cell {
  height: 100% !important;
  line-height: 20px !important;
}

#call_order_list .remarkClassByCallCenterCallRecordList p {
  white-space: pre-line;
}
#call_order_list .el-table .el-table__body-wrapper td .cell,
#call_order_list .el-table .el-table__fixed-body-wrapper td .cell {
  line-height: unset;
  height: unset;
}
#call_order_list .el-table .el-table__body-wrapper td,
#call_order_list .el-table .el-table__fixed-body-wrapper td {
  text-align: center;
}
#call_order_list .TransferButton {
  margin-left: 21px;
}
#call_order_list .el-table .el-table__header-wrapper th {
  text-align: center;
}
#call_order_list .el-table .el-table__body-wrapper td .cell, #call_order_list .el-table .el-table__fixed-body-wrapper td .cell span{
  /* white-space: break-spaces; */
  line-height: normal;
}
#call_order_list .xpt-flex .el-table__body-wrapper{
  margin-bottom: 61px;
}
#call_order_list .text-align-left{
    white-space: break-spaces;
    text-align: left;
    display: inline-block;
    width: 100%;
}
</style>
