<!-- 搜索 -->
<template>
  <div class="search-work">
    <div class="form-box">
      <el-form ref="form" :model="formMap" label-width="100px">
        <div class="form-inline">
            <div class="form-inline">
                <div class="form-inline search-label">工单号</div>
                <el-input class="search-input" size="mini" v-model="formMap.jobNumber"></el-input>
            </div>
            <div class="form-inline">
                <div class="form-inline search-label">问题处理人</div>
                <el-input class="search-input" size="mini" v-model="formMap.problemHandlerName"></el-input>
            </div>
            <div class="form-inline">
                <div class="form-inline search-label">问题处理人分组</div>
                <el-input class="search-input" size="mini" v-model="formMap.problemHandlerGroupName" icon="search"
                        :on-icon-click="searchHandlerGroup" readonly
                        @keydown.delete.native="clearProperty('problemHandlerGroupId')"></el-input>
            </div>
            <div class="form-inline">
                <div class="form-inline search-label">审核状态</div>
                <el-select v-model="formMap.isApproved" placeholder="请选择" size="mini" class="search-input">
                    <el-option
                    v-for="item in isApproved"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="form-inline">
                <div class="form-inline search-label">紧急程度</div>
                <el-select v-model="formMap.markType" placeholder="请选择" size="mini" class="search-input">
                    <el-option
                    v-for="item in markType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="form-inline">
                <div class="form-inline search-label">预约时间</div>
                <el-date-picker
                    v-model="formMap.preHandlerTime"
                    type="date"
                    placeholder="选择时间"
                    size="mini">
                </el-date-picker>
            </div>
            <div class="form-inline">
                <div class="form-inline search-label">客服处理人</div>
                <el-input class="search-input" size="mini" v-model="formMap.salesUserName"></el-input>
            </div>
        </div>
      </el-form>
    </div>
    <div class="form-action">
      <el-button type='primary' size="mini" @click="search()">查询</el-button>
      <el-button type='primary' size="mini" @click="resetForm()">重置</el-button>
      <el-button type='success' size="mini" @click="search()">刷新</el-button>
    </div>
  </div>
</template>

<script>
  export default {
    props: ['params'],
    data() {
      var _this = this;
      return {
        formMap: {
          custName: '',
          receiverName: '',
          receiverPhone: '',
          startDate: '',
          endDate: '',
          calledNumber: '',
          salesUserName: '',
          salesUserId: '',
          salesGroupName: '',
          problemHandlerName: '',
          shopName: '',
          callInType: '',
          creatorName: '',
          creatorId: null,
          markedUserName: '',
          markedUserId: null,
          mergeTradeNo: '',
          callingType: '',
          isApproved: '未审核',
          orderOwnerType: '',
          jobNumber:'',
          problemHandlerGroupName:"",
          problemHandlerGroupId:null,
          markType:'',
          preHandlerTime:'',
        },
        callingType: [
          {
            value: '购买咨询',
            label: '购买咨询',
          }, {
            value: '发货截货',
            label: '发货截货',
          },  {
            value: '物流查询',
            label: '物流查询',
          }, {
            value: '售后问题',
            label: '售后问题',
          }, {
            value: '其他',
            label: '其他',
          }, {
            value: '预约到店',
            label: '预约到店',
          }, {
            value: '虚假签收',
            label: '虚假签收',
          }, {
            value: '话务投诉',
            label: '话务投诉',
          }],
          isApproved: [
          {
            value: '0',
            label: '未审核',
          },{
            value: '1',
            label: '已审核',
          }],
        orderOwnerType: [
          {value: '咨询', label: '咨询'},
          {value: '售前', label: '售前'},
          {value: '售中', label: '售中'},
          {value: '售后', label: '售后'},
          {value: '展厅', label: '展厅'},
          {value: '三包', label: '三包'},
          {value: '经销', label: '经销'},
          {value: '话务', label: '话务'},
          {value: '线上和线下', label: '线上和线下'},
          {value: '承包门店', label: '承包门店'},
          {value: '外包售前', label: '外包售前'},
        ],
        value: '',
        markType: [
            {value: "NEED_FOLOW",label: "需要跟进"},
            {value: "PROCESS_TODAY",label: "当天处理"},
            {value: "URGENCY",label: "紧急处理"},
            {value: "BEFORE_NOON",label: "上午12点前"}
        ],
      };
    },
    methods: {
      resetForm(formName) {
        for (let key in this.formMap) {
          this.formMap[key] = '';
        }
        // this.$emit('handle-order-search', {});
      },
      setPhone(phone) {
        this.formMap['calledNumber'] = phone;
      },
      getCondition() {
        return this.formMap;
      },
      search() {
        let newformMap = {};
        for (let key in this.formMap) {
          if(this.formMap[key] != '' && this.formMap[key] != null) {
            newformMap[key] = this.formMap[key];
          }
        }
        this.$emit('handle-order-search', newformMap);
      },
      exportOrderMsg() {
        this.$emit('handle-exprot');
      },
      clearProperty(...properties) {
        properties.forEach(key => {
          this.formMap[key] = null;
        });
      },
      searchSalesUserCondition() {
        var _this = this;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/system_management/list.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            // status: 1,//生效人员
            isEnable: 1,//生效时间
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;
              _this.formMap.salesUserName = data.fullName;
              _this.formMap.salesUserId = data.id;
            },
            callback_two:true,
            callbacktwo(self){
              _this.changeSearchParamsByChoiceUser(self)
            }
          },
        });
      },
      // 问题处理人分组
      searchHandlerGroup(){
         var _this = this;
        // 打开分组选择列表的时候传参，给分组列表添加查询参数
        let addParams = [
          {
            name:'type',
            value:'TRAFFIC'
          }
        ]
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/per_sales_report/select_group.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '选择分组',
          params: {
            isAddParams:true,
            notNeedReportEmbody:true,
            addParams,
            callback(b) {
              _this.formMap.problemHandlerGroupId = b.id;
              _this.formMap.problemHandlerGroupName = b.name;
            },
          },
        });
      },
      // 修改选择用户信息弹窗的查询参数
      changeSearchParamsByChoiceUser(self){
        self.$refs.personList.$refs.xptSearchEx.filterFields =
          fields => {
            let oldItem = fields[2];
            fields.splice(2,1);
            fields.unshift(oldItem);
            return fields;
          };
        self.$refs.personList.$refs.xptSearchEx.watchwhere =
          where => {
            where = where.map((item,index) => {
              if(index == 0){
                item.operator = '%'
              };
              return item;
            })
            return where;
          };
      },
    },
    mounted: function() {
      // _this.getForcedUnlock();
      // _this.updateUserOrder();
      // _this.searching();F
      // 检测新增、编辑数据，刷新
      let _this = this;
      _this.$root.eventHandle.$on('refresh_list', function() {
        // _this.searching();
      });
    },
    destroyed() {
      this.$root.offEvents('refresh_list');
    },
    watch: {
      btns() {
        let _this = this;
        _this.getBtnPower();
      },
    },
  };
</script>
<style scoped>
  .search-work {
    background: #eee;
    display: flex;
    /*margin: 0px -20px 0px -20px;*/
    padding-right: 85px;
    position: relative;
  }

  .form-action {
    /*margin: auto;*/
    /*min-width: 250px;*/
    position: absolute;
    right: 30px;
    top: 12px;
    display: flex;
  }

  .form-action button {
    display: block;
    margin: 0 5px 10px 0;
  }

  .form-box {
    position: relative;
    margin: 10px;
    flex: 1;
  }

  .form-inline {
    display: inline-block;
  }

  .search-label {
    width: 100px;
    text-align: center;
  }

  .search-input {
    margin-bottom: 3px;
    width: 120px;
  }
</style>
