<template>
	<div class="call_out_list">
		<div class="headerBox">
			<span class="headerPa" v-for="(item, index) in params.listData" :key="index"
				>{{ item.label }}: <span style="font-weight:900;">{{ item.value }}</span></span
			>
		</div>
		<div style="height: 288px;" class="xpt-flex__bottom">
			<xpt-list
				ref="call_out_list"
				:showHead="false"
				:orderNo="true"
				:colData="colData"
				:data="listdata"
				selection="none"
			></xpt-list>
		</div>
	</div>
</template>

<script>
import { makeUrl, apiUrl, EventBus } from "../base.js";
export default {
	name: "call_out_list",
	props: ["params"],
	data() {
		let self = this;
		return {
			listBtns: [],
			colData: [
				// {
				// 	label: "呼叫类型",
				// 	prop: "callOutType"
				// },
				{
					label: "处理人",
					prop: "handleName"
				},
				{
					label: "工号",
					prop: "staffNo",
					width: '50'
				},
				{
					label: "便签记录",
					prop: "noteRecord",
					width: '200',
          html: data => {
					  data = data ? data:'';
					  return `<p>${data}</p>`
          },
          getCellClassName() {
					  return `remarkClassByCallOutList`
          }
				},
				{
					label: "处理日期",
					prop: "handleTime",
					format: "dataFormat3"
				},
				{
					label: "电话开始时间",
					prop: "startRecordingTime",
					// formatter(val) {
					// 	return self.splitString(val);
					// }
				},
				{
					label: "电话结束时间",
					prop: "endRecordingTime",
					// formatter(val) {
					// 	return self.splitString(val);
					// }
				},
				{
            width: '95',
            label: "呼叫电话",
            prop: "calledNumber",
          },
				{
					label: "录音",
					prop: "recordFile",
					redirectClick(row) {
						if (row.recordFile) {
							window.open(row.recordFile);
						}
					},
					formatter: val => {
						if (val) return "播放";
					},
          width: 50
				}
			],
			listdata: []
		};
	},
	watch: {},
	methods: {
		// 获取列表信息
		getList() {
			let params = this.params.searchParams;
			this.$http
				.get(apiUrl.callLog_list, { params })
				.then(res => {
					if (res.data.result) {
						this.listdata = res.data.content;
					} else {
						this.$message.error(res.data.msg);
					}
				})
				.catch(err => {
          this.$message.error('呼叫中心报错: ',err.status,err.message);
				})
				.finally(() => {});
		},
		// 切割字符
		splitString(val) {
      let string = '';
      if(val){
        let stringArr = `${val}`.split("");
        string = `${stringArr[0]}${stringArr[1]}${stringArr[2]}${stringArr[3]}-${stringArr[4]}${stringArr[5]}-${stringArr[6]}${stringArr[7]} ${stringArr[8]}${stringArr[9]}:${stringArr[10]}${stringArr[11]}:${stringArr[12]}${stringArr[13]}`;
      }
			return string;
		}
	},
	mounted() {
		this.getList();
	},
	destroyed() {}
};
</script>

<style scoped lang="stylus">
.call_out_list {
	padding-top: 5px;

	.headerBox {
		padding-left: 5px;
		border: 1px solid #ccc;
		border-radius: 5px;
		margin-bottom: 10px;
		max-height: 61px;
		overflow: auto;

		.headerPa {
			display: inline-block;
			font-size: 14px;
			line-height: 35px;
			margin-right: 40px;
      /*width: 32%;*/
		}
	}
}
</style>

<style>
  .remarkClassByCallOutList{
    height: 100% !important;
  }
  .remarkClassByCallOutList .cell{
    height: 100% !important;
    line-height: 20px!important;
  }
  .remarkClassByCallOutList p{
    white-space: pre-line;
  }
</style>
