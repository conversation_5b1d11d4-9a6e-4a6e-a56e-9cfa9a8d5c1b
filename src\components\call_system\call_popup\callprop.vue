<template>
	<div id='callCenterPopup' class='container' ref='callCenterPopup' :style="{width:`${containerH}px`}" v-if="diaConfig.dialogVisible">
		<div id='callcenterPopupHeader'>
				<span class='header-title' v-html="diaConfig.title"></span>
        <span class='header-title'>{{callStatus}}</span>
        <div class="header-btn">
          <i class="el-icon-minus" @click="changeSize('small')" v-if='if_Enlarge'></i>
          <i class="el-icon-plus" @click="changeSize('big')" v-else></i>
          <i class="el-icon-close" @click="closeDialog" ></i>
          <!-- 关闭按钮改为常亮 <i class="el-icon-close" @click="closeDialog" v-if="!isCalling"></i> -->
        </div>
    </div>
    <div v-if='if_Enlarge'>
      <div class='main'  v-show="formData.sourceType != 'appointment_tracing'">
        <div v-if = "phoneInitEvent === false " style='color:#ec2f0e;padding-top: 5px;padding-bottom: 5px;border: 2px solid rgb(204, 204, 204);border-radius: 5px;'>
          <!-- <p style="margin-bottom: 10px; margin-left: 20px;">内网用户：请检查网络是否正常，正常后，点击底部重新注册按钮，重新启动电话条</p>
          <p style=" margin-left: 20px;">外网用户：请检查网络是否正常，且是否为外网连接，确认后，点击底部重新注册按钮，重启电话条</p> -->
          <p style="margin-bottom: 10px; margin-left: 20px;margin-right: 20px;line-height:18px;">{{errorInfo}}</p>
       </div>
        <div style='border: 2px solid rgb(204, 204, 204);border-radius: 5px;'>
          <drop-form :formCol='formCol' :formData='formData' @getPhone='getphoneBox'></drop-form>
        </div>
        <div class='phoneKey'>
          <phone-dial
          :canChange="true"
          :callOutDisabled="callOutDisabled"  :hangUpDisabled="hangUpDisabled" :phoneSuggest="[]"
          :phone="phone"
          ref="phoneDial"
          v-if="true"
          @makeCall="makeCall"
          @hangUp="hangUp"
          @changePhone="changePhone"/>
        </div>
        <div class='phoneAns'>
          <p style='font-size:14px;margin-bottom:10px;'>通话摘要: </p>
          <div style='margin-bottom:17px;'>
            <el-input
              type="textarea"
              :rows="18"
              placeholder="请输入内容"
              v-model="textarea"
              resize='none'>
            </el-input>
          </div>
          <div style='float:left;'>
            <el-button
              type="warning"
              @click='resignPhoneBar'>
            重新注册</el-button>
          </div>
          <div style='float:right;'>
            <el-button
              type="primary"
              :disabled="saveBtnDisabled" :loading='saveBtnLoading'
              @click='saveTheCallData'>
            保存</el-button>
          </div>
        </div>
      </div>
      <div class='main'  v-show="formData.sourceType == 'appointment_tracing'">
        <div class='phoneAns'>

          <div style='float:left;'>
            <el-button
              type="warning"
              @click='resignPhoneBar'>
            重新注册</el-button>
          </div>

          <div style='float:right;'>
            <el-button
              type="primary"
              @click='callClick'>
            呼出</el-button>
            <el-button
              type="danger"
              @click='hangUp'>
            挂断</el-button>
          </div>
        </div>
      </div>

      <!-- <div class="failmain" v-else>
        <p class="topP">电话条初始化异常</p>
        <div class="left-box">
          <p><span style="font-weight: 900;">内网环境</span>（在林氏总部大楼）</p>
          <p>请检查网络环境是否正常，确认正常后再次尝试重新打开新平台，再打开当前弹窗</p>
        </div>
        <div class="right-box">
          <p><span style="font-weight: 900;">外网环境</span>（不在林氏总部大楼）</p>
          <p>确认右下角电话条图标消失之后再进行下列操作</p>
          <p>1. 打开呼叫管理 2. 点击切换外网环境，点击重新注册 3. 重新打开当前弹窗</p>
        </div>
      </div> -->
    </div>
	</div>
</template>

<script>
  import dropPopup from "./onPopup.js";
  import dropForm from "./popupForm.vue";
  import PhoneDial from "./phoneDial.vue";
  import {apiUrl, makeUrl, EventBus} from "../base.js";
  import PhoneBar from "../phoneBar.js";
  import Fn from '@common/Fn.js'

  export default {
	name: "Popup",
	props: {
		diaConfig: {
			type: Object,
			required: true
		},
		// 数据项
		formCol: {
			type: Array,
			required: true
		},
		// 数据源
		formData: {
			type: Object,
			required: true
		}
	},
	watch: {
		// "diaConfig.dialogVisible": {
		// 	handler: function(newV) {
		// 		if (newV == true) {
		// 			// this.dataInit();
		// 			// setTimeout(() => {
		// 			// 	this.dropPopupBox();
		// 			// 	this.initPhoneBar1();
		// 			// }, 0);
		// 		} else {
    //       // this.$store.commit('common/SET_BUSY')
		// 			// PhoneBar.detachAll(this);
		// 		}
		// 	}
		// },
    if_Enlarge:function(newV){
      let params = {
        name:'callPopupIfEnlarge',
        value:newV
      }
      this.$store.commit('common/SET_CALLPOPUP_DATA',params)
    },
    formData:{
      handler(newVal){
        // 传来新数据的时候，映射一下手机号
        this.pullThephone();
      },
      deep:true
    }
	},
	data() {
		return {
			selectElement: "",
			containerH: 600, //组件宽度
			if_Enlarge: true, // 是否放大
      isCalling:false, // 通话中
			// 电话条注册
      serverIP: '************',
      // serverIP: 'callcenter.linshimuye.com',
			vccID: "800800",
			// agentID: "5069",
			sipPort: "5040",
			sipPassword: "00000000",
			mainPort: "14800",
			ctiPassword: "111111",
      agentId: '',
			// phoneSuggest: [],
			phone: "",
			savephone: "", //拨号之后将打的电话缓存起来
			textarea: "",
      callStatus: "",
      errorInfo: "电话条已出现异常，请点击底部【重新注册】按钮，重启电话条，失败可尝试多次",
			callParamsList: {}, //保存接受到的电话信息
			// shouldSave: false,
			id: "", //通话记录表id
			// 禁用按钮s
			saveBtnDisabled: true,
			callOutDisabled: true,
			hangUpDisabled: true,
			saveBtnLoading: false,
			called: false, //已经打过电话了
      jobId: "", //通话记录列表的id，短的
      phoneInitEvent:true, //电话条初始化是否成功
      isCallStatus: false,
      isSalesPersonDisConnect:false,//是否客服主动挂断
		};
	},
	computed: {},
	methods: {
    callClick(){
      // this.makeCall(this.displayPhone);
      // this.$emit('makeCall', this.formData.phone);
      this.phone = this.formData.phone;
      this.makeCall()
    },
    callClose(){
        this.$emit('hangUp');

    },
		// 数据初始化
		dataInit() {
			this.callParamsList = {};
      let params = {
        name:'callPopupParamsList',
        value:this.callParamsList
      }
      this.$store.commit('common/SET_CALLPOPUP_DATA',params)

			this.saveBtnDisabled = true;
			this.callOutDisabled = false;
			this.hangUpDisabled = true;
			this.saveBtnLoading = false;
			this.phone = "";
			this.textarea = "";
			this.id = "";
			this.jobId = "";
			this.called = false;
			// this.if_Enlarge = true;
			// this.containerH = 600
      if(!this.formData.notInitChoice){
        this.pullThephone();
      }
      // 当含有已经拨打的手机号的时候，将手机号映射会拨号盘上，再清楚
      if(this.savephone){
        this.phone = this.savephone;
      }
      this.savephone = "";
      this.isCallStatus = false;
		},
    //当手机号只有一个的时候，直接将手机号拉取到拨号盘上
    pullThephone(){
      let phoneList = this.formData.phone;
      if(phoneList&&phoneList.length === 1){
        this.phone = phoneList[0].split("  ")[0];
      }
    },
		// 修改弹窗 -----------------------
    // 弹窗开启以及关闭状态 : 获取 || 直接修改
    interactionPopup(payload){
      let { useModule,size } = payload;
      if(useModule == 1){
        this.changeSize(size);
      }
    },
    // 修改弹窗大小
		changeSize(size) {
			let theEl = document.getElementById("callCenterPopup");
			// window.ceshi = theEl;
			if (size == "big") {
        theEl.style.top = '75px';
        theEl.style.left = '30%';
				this.if_Enlarge = true;
				this.containerH = 600;
			} else if (size == "small") {
			  theEl.style.top = '10px';
			  theEl.style.left = '40%';
				this.containerH = 222;
				this.if_Enlarge = false;
			}
    },



    // 获取当前用户的分组信息
    getEmployeeData() {
        let params = {
          pageNo: 1,
          pageSize: 20,
          staffNo: this.getEmployeeInfo('employeeNumber'),
          staffName: "",
          groupName: ""
        };
        this.$http.get(apiUrl.personGroup_list, { params }).then(res => {
            if (res.data.code == 0 && res.data.content.length > 0) {
                  if(res.data.content[0].isIntranet == "N"){
                    // 外网
                    this.errorInfo = "当频繁出现电话接听突然中断、无法接听等现象，若办公地点在林氏总部则联系羊驼改为内网用户，"+
                    "若不在林氏总部，则检查网络是否稳定；若仍出现异常，请点击底部【重新注册】按钮，重启电话条，失败可尝试多次";
                  }else{
                    // 内网
                    this.errorInfo = "若出现【电话条注册中……】的提示，导致通话中断、无法接听或无法外呼的，请先重启电脑。若重启后问题仍未解决，且办公地点在林氏总部则联系羊驼改为内网用户；"+
                    "若不在林氏总部，则检查网络是否稳定;若仍出现异常，请点击底部【重新注册】按钮，重启电话条，失败可尝试多次。";
                  }
            }
        })
        .catch(err => {
        })
        .finally(() => {
        });
    },

    // 重新注册电话条
    resignPhoneBar(){
      this.$confirm(
          "是否重新注册电话条(请确认电话条是否异常)",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "danger",
          }
        ).then(() => {
          // 电话条是否离线
          console.log("重新注册，电话条状态："+ application.oJVccBar.GetAgentStatus());
          // if( application.oJVccBar.GetAgentStatus() != 0 ){
          //     PhoneBar.phoneUninstall();
          //     console.log("电话条非离线状态");
          // }
          // 退出电话条
          PhoneBar.phoneUninstall();
          // 重现注册电话条
          setTimeout(() => {
            this.initPhoneBar1();
          }, 2000);

        }).catch(() => {
          return;
        });
    },


    // 关闭弹窗
		closeDialog() {
			if ((!this.callParamsList.bgntime && !this.callParamsList.endtime && !this.callParamsList.filename)||this.formData.sourceType == "appointment_tracing") {
				this.$bus.emit("changeCallProp", false);
			} else {
				this.$message.warning("您有未保存的变更，请保存后再关闭");
			}
		},
    // 关闭弹窗
		// closeDialog() {

    //   if ( this.callStatus == '通话中') {
    //     this.$confirm(
    //       "当前操作会导致电话条注销，是否继续？",
    //       "提示",
    //       {
    //         confirmButtonText: "确定",
    //         cancelButtonText: "取消",
    //         type: "danger",
    //       }
    //     ).then(() => {
    //       this.$bus.emit("changeCallProp", false);
    //       PhoneBar.phoneUninstall();
    //     }).catch(() => {
    //       return false;
    //     });
    //   }else	if (!this.callParamsList.bgntime && !this.callParamsList.endtime && !this.callParamsList.filename) {
    //     this.$bus.emit("changeCallProp", false);
    //     // 退出电话条
    //     // 下线
    //     PhoneBar.phoneUninstall();
		// 	} else {
		// 		this.$message.warning("您有未保存的变更，请保存后再关闭");
		// 	}
		// },
    // 点击移动弹窗
		dropPopupBox() {
			let Box = document.getElementById("callCenterPopup");
			let header = document.getElementById("callcenterPopupHeader");
			dropPopup(Box, header);
		},
    // -------------------------------
		// 注册电话条
		initPhoneBar1() {
			this.callStatus = "电话条注册中……";
			// let userInfo = this.getUserInfo(); // userInfo.employeeNumber
			// let userNo = /^\d+$/.test(userInfo.employeeNumber) ? Number(userInfo.employeeNumber) : userInfo.employeeNumber;
      let status = application.oJVccBar.GetAgentStatus();
      //当电话条已经注册的时候就不注册了，直接刷新按你
      if(status === 0){
        let userNo = this.agentId;
        //  0：未登录   1：忙碌  2：空闲  3：通话中  4：后续态
        PhoneBar.phoneInitial(this.serverIP, this.vccID, userNo, this.sipPort, this.sipPassword, this.mainPort, this.ctiPassword);
        setTimeout(()=>{
          this.initJudge();
        },3000)
      }else{
        this.busy();
        this.changeBtnStatus();
        this.updateAgentStatus();
      }

      // 初始化事件
      this.initEventBusFn();
		},
    // 电话条初始化判断
    initJudge(){
      let choicenum = 0;
      let timeer = setInterval(()=>{
        choicenum++;
        if(application.oJVccBar.SetBusy() == 0){
          clearInterval(timeer);
          this.phoneInitEvent = true;
          // this.$message.success('注册成功')
        }else if(application.oJVccBar.SetBusy() != 0 && choicenum == 7){
          this.phoneInitEvent = false;
          console.log("电话条初始 异常了！！");
          clearInterval(timeer);
        }
      },1500)
    },


    // 初始化电话条事件
    initEventBusFn(){
      EventBus.on(PhoneBar.getAgentWorkEventName,this.getAgentWorkEventName);
      EventBus.on(PhoneBar.getAnswerCallEventName,this.answerCall);
      EventBus.on(PhoneBar.getCallEndEventName,this.callEnd);
      EventBus.on(PhoneBar.getCallingEventName,this.onCallRing);
    },
    // 销毁事件
    destoryEventFn() {
      EventBus.off(PhoneBar.getAgentWorkEventName,this.getAgentWorkEventName);
      EventBus.off(PhoneBar.getAnswerCallEventName,this.answerCall);
      EventBus.off(PhoneBar.getCallEndEventName,this.callEnd);
      EventBus.off(PhoneBar.getCallingEventName,this.onCallRing);
    },
    //更新按钮状态，以及后台状态
    getAgentWorkEventName(){
      console.log('更新呼出弹窗按钮状态');
      this.changeBtnStatus();
      this.updateAgentStatus();
    },
		// 修改按钮状态
		changeBtnStatus() {
			this.saveBtnDisabled = true;
			this.callOutDisabled = true;
			this.hangUpDisabled = true;
			let status = application.oJVccBar.GetAgentStatus();
			//  0：未登录   1：忙碌  2：空闲  3：通话中  4：后续态
			if (status == 1 || status == 4) {
				this.callOutDisabled = false;
			}
			if (status == 3) {
        this.hangUpDisabled = false;
        this.saveBtnDisabled = false;
			}
		},
		// 修改用户状态
		updateAgentStatus() {
			//0：未登录   1：忙碌  2：空闲  3：通话中   4：后续态
			let status = "offline";
			// console.log(PhoneBar.getStatus());
			switch (PhoneBar.getStatus()) {
				default:
					status = "busy";
					this.callStatus = "忙碌";
					// this.btnTip = '后续态';
					break;
				case 0:
					status = "offline";
          this.callStatus = "离线";
					// this.$confirm("电话条已离线", "提示", {
					// 	confirmButtonText: "重新登录",
					// 	cancelButtonText: "取消",
					// 	type: "warning"
					// }).then(() => {
					// 	this.initPhoneBar1();
					// });
					break;
				case 1:
					this.callStatus = "忙碌";
					status = "busy";
					break;
				case 2:
					this.callStatus = "空闲";
					status = "idle";
					break;
				case 3:
					this.callStatus = "通话中";
					status = "calling";
					break;
			}
			this.$http.get(makeUrl(apiUrl.updateAgentStatus, { status: status }));
		},
		// 获取手机号
		getphoneBox(num) {
			this.phone = num;
		},
    // 修改手机号
		changePhone(num) {
			this.phone = num;
		},
    //示忙
    busy() {
      console.log("callprop_busy 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());
      application.oJVccBar.SetBusy();
    },
		// 挂断
		hangUp() {
            console.log("callprop_hangUp 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());
            this.isSalesPersonDisConnect=true;
            if(this.called){
                var subfix = window.location.host.startsWith('sale.linshimuye') ? '9999' : '9998';
                let num = '00000180080000' + subfix;
                application.oJVccBar.TransferOut(num, num);
                if(this.formData.self_motion_hang_up){
                    setTimeout(()=>{
                        this.closeDialog()
                    },200)
                }
            }else{
                let value=window.application.oJVccBar.Disconnect();
                console.log('挂断',value)
                window.application.oJVccBar.SetBusy();
                if(this.formData.self_motion_hang_up){
                    this.closeDialog()
                }
            }
    },

    // 振铃时
    onCallRing(
        CallingNo/*主叫号码*/, CalledNo/*被叫号码*/, OrgCalledNo/*原始被叫号码*/, CallData, SerialID, ServiceDirect, CallID,
        UserParam, TaskID, UserDn, AgentDn, AreaCode, Filename, networkInfo, queueTime, opAgentID) {
        console.log("振铃时："+CallingNo, CalledNo, OrgCalledNo, CallData, SerialID, ServiceDirect, CallID, UserParam, TaskID, UserDn, AgentDn, AreaCode, Filename, networkInfo, queueTime, opAgentID);

        // 应答定时任务延时执行保存操作，避免影响到电话条主流程
        setTimeout(() => {
          var filename = "";
            var directory = "";
            var subIndex = Filename.lastIndexOf("/");
            if(subIndex > 0){
              directory = Filename.substring(0,subIndex);
              filename =  Filename.substring(subIndex+1);
            }
            var beginTimeText = Fn.dateFormat(new Date, 'yyyyMMddhhmmss');
            let url = this.formData.sourceType == 'appointment_tracing' ? apiUrl.personGroup_list1:apiUrl.callStat_add;
             let postData = {
              sourceOrderType: null,
              sourceOrderId: null,
              sourceOrderNo: null,
              callType: '呼出',
              type: '呼出',
              phoneNumber: userno,
              callNumber: "N",
              fileName: (!!bgntime && !!endtime) ? filename : null,
              directory: (!!bgntime && !!endtime) ? directory : null,
              beginTimeText: bgntime,
              endTimeText: endtime,
              serialId: serialid,
              sourceCallId: callid,
              callDuration: null,
              businessGroup: null,
              isInitiative: false,
              waitingTime: 0, //全是主动呼出，不存在振铃时间
              isSalesPersonDisConnect:this.isSalesPersonDisConnect//是否客服挂断
            };
            if(this.formData.sourceType == 'appointment_tracing'){
              postData.appointment_tracing_id = this.formData.appointment_tracing_id;
            }
            // 振铃的时候直接新增一条呼叫记录（callstat）
            this.$http.post(url, postData).then(res => {
                console.log("振铃成功插入callstat");
                // 返回的callId其实是通话记录的id，这里改成jobId，短的
                this.jobId = res.body.content.callId;
            });
            // 保存到log的数据
            this.callParamsList = {
              "agentalerttime": "",//座席振铃时长
              "bgntime": "",//处理
              "callid": CallID, //呼叫标识, string
              "directory": "",//录音文件保存目录
              "disconnecttype": "",//挂机方
              "endtime": "",//处理
              "fileName": "",//录音文件名
              "networkinfo": "",//可选参数，来自哪个网关
              "serialid": SerialID,  //可选参数，呼叫座席标识, string
              "servername": "", //录音文件所在服务器标识
              "servicedirect": "2", //呼叫类型, int
              "taskid": "",//外呼任务或者人工服务号
              "useralerttime": "",//用户振铃时长
              "userno": UserDn, //用户电话号码, string
              "userparam": "",// 透明参数
            };
        }, 100);

    },

		// 呼出
		makeCall() {
			let phone = this.phone;
			this.savephone = phone;
			// if (phone && !/^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/.test(phone)) {
			// 	this.$message.error("手机号不正确");
			// 	return;
			// }
      // 更换手机号正则，首号为1，后续允许1~9  /^1(\d{10})$/
      if (phone && !/^[1](([3-9])[0-9]{9}$)|(([3-9])[0-9]{9}-[0-9]{4}$)/.test(phone)) {
				this.$message.error("手机号不正确");
				return;
			}

			this.called = false;
			window.application.oJVccBar.MakeCall(phone, 0, "", "", "");
      this.isCalling = true;
		},
		// 接通了
		answerCall() {
			console.log("接通了");
			this.called = true; //接通标记
      // 刷新按钮状态
      this.saveBtnDisabled = true;
			this.callOutDisabled = true;
      this.hangUpDisabled = false;
      this.saveBtnDisabled = false;
			this.addDataByConnect();
		},
		// 一接通先给表格添加一条数据
	  async	addDataByConnect() {
			let params = {
				callingTpe: "呼出",
        tid: this.formData.typeNum, //业务id，可能是回访可能是合并，诸如此类的
        handleName: this.getEmployeeInfo("fullName"), //处理人
				sourceType: this.formData.sourceType,
				calledNumber: this.savephone,
				mergeTradeId: this.formData.mergeTradeId || "",
				mergeTradeNo: this.formData.mergeTradeNo || "",
				afterPlanId: this.formData.afterPlanId || ""
      };
      if(this.id != "" ){
        params["id"] = this.id ;
      }

			this.$http
				.post(apiUrl.callLog_add, params)
				.then(res => {
					if (res.data.result) {
            if(this.id == "" ){
               this.id = res.data.content.id;
            }

					} else {
						this.$message.error(res.data.msg);
					}
				})
				.catch(err => {
					// console.error(err);
					this.$message.error(err);
				})
				.finally(() => {});
		},
		// 挂断
    callEnd(
      callid,
      serialid,
      servicedirect,
      userno,
      bgntime,
      endtime,
      agentalerttime,
      useralerttime,
      filename,
      directory,
      disconnecttype,
      userparam,
      taskid,
      servername,
      networkinfo
    ) {


      // req.D-营销管理与业务（CRM）XPT-18771   2020.9.22   lxh
      // if (this.called) {      } else if (!this.called) {
      //   // console.log("挂断2,不需要保存数据");
      //   this.callOutDisabled = false;
      //   this.saveBtnDisabled = true;
      // }
        // console.log("挂断1,要保存数据");
        this.callOutDisabled = true;
        this.saveBtnDisabled = false;
        this.isCallStatus = true;
        this.callParamsList = {
          callid, //呼叫标识, string
          serialid, //可选参数，呼叫座席标识, string
          servicedirect, //呼叫类型, int
          userno, //用户电话号码, string
          bgntime:this.splitString(bgntime), //呼叫开始时间, string
          endtime:this.splitString(endtime), //呼叫结束时间
          agentalerttime, //座席振铃时长
          useralerttime, //用户振铃时长
          fileName:filename, //录音文件名
          directory, //录音文件保存目录
          disconnecttype, //挂机方
          userparam, // 透明参数
          taskid, //外呼任务或者人工服务号
          servername, //录音文件所在服务器标识
          networkinfo //可选参数，来自哪个网关
        };
        let params = {
          name:'callPopupParamsList',
          value:JSON.parse(JSON.stringify(this.callParamsList))
        }
        this.$store.commit('common/SET_CALLPOPUP_DATA',params)
        //客服接通，挂断方日志
        // console.table([['是否接通',bgntime>0],['是否客服挂断:',this.isSalesPersonDisConnect]]);
          let url = this.formData.sourceType == 'appointment_tracing' ? apiUrl.personGroup_list1:apiUrl.callStat_add
          let postData = {
          sourceOrderType: null,
          sourceOrderId: null,
          sourceOrderNo: null,
          callType: '呼出',
          type: '呼出',
          phoneNumber: userno,
          callNumber: "N",
          fileName: (!!bgntime && !!endtime) ? filename : null,
          directory: (!!bgntime && !!endtime) ? directory : null,
          beginTimeText: bgntime,
          endTimeText: endtime,
          serialId: serialid,
          sourceCallId: callid,
          callDuration: null,
          businessGroup: null,
          isInitiative: false,
          waitingTime: 0, //全是主动呼出，不存在振铃时间
          isSalesPersonDisConnect:this.isSalesPersonDisConnect//是否客服挂断
        };
        if(this.formData.sourceType == 'appointment_tracing'){
          postData.appointment_tracing_id = this.formData.appointment_tracing_id;
        }
        // 挂断的时候直接新增一条呼叫记录
        this.$http.post(url, postData).then(res => {
          // 返回的callId其实是通话记录的id，这里改成jobId，短的
          this.jobId = res.body.content.callId;
          this.isSalesPersonDisConnect=false
        });
      this.isCalling = false;
    },
		// 保存数据
		saveTheCallData() {
			// let params = this.callParamsList;
			// if (!this.textarea.replace(/^[\s\uFEFF]+|[\s\uFEFF]+$/g, "")) {
			// 	this.$message.error("摘要内容不能为空！");
			// 	return;
			// } else {

        //  this.textarea  去除特殊字符（近保留数字、字母、常用标点符号）


        this.saveBtnLoading = true;

				let paramB = JSON.parse(JSON.stringify(this.callParamsList));
				let paramA = {
					// 后端需要的数据1
          id: this.id, // 其它页面呼叫记录id
          sourceCallId: paramB.callid, //电话条返回的id，长的
					jobId: this.jobId, //通话记录用的id，短的
					tid: this.formData.typeNum, //业务id,多重形式
					staffNo: this.getEmployeeInfo("employeeNumber"), //工号
					handleName: this.getEmployeeInfo("fullName"), //处理人
					// jobNumber //工单编码
					calledNumber: this.savephone, //呼叫号码
					defaultCalloutNumber: paramB.userno, //默认呼出号码
					callingTpe: "呼出", //来电类型
					mergeTradeId: this.formData.mergeTradeId || "", //合并订单ID
					mergeTradeNo: this.formData.mergeTradeNo || "", //合并订单号
					afterPlanId: this.formData.afterPlanId || "", //售后单id
					// handleTime: Fn.dateFormat(new Date().valueOf(), "yyyy-MM-dd hh:mm:ss"), //处理时间
					startRecordingTime: `${paramB.bgntime}`, //开始录音时间
					endRecordingTime: `${paramB.endtime}`, //开始录音时间
					// recording //录音
					noteRecord: this.textarea, //便签记录
					sourceType: this.formData.sourceType, //来源类型
				};
				let params = Object.assign({}, paramA, paramB);
				this.$http
					.post(apiUrl.callLog_add, params)
					.then(res => {
						if (res.data.result) {
              // 成功了
              this.$message.success("保存成功");
              if(this.isCallStatus){
                this.dataInit();
                // this.closeDialog();
              }else{
                if( this.id == "" ){
                  this.id = res.data.content.id;
                }
              }
						} else {
							this.$message.error(res.data.msg);
						}
					})
					.catch(err => {
						// console.error(err);
						this.$message.error(err);
					})
					.finally(() => {
            this.saveBtnLoading = false;
					});
			// }
    },

    // 如果电话条功能出错，则写入日志
    saveLogByPhoneBar (eventName,eventStatus){
      this.$http.post(apiUrl.callEventLog_add, {
        'callId': PhoneBar.getCurrentCallInfo().CallID,
        'eventName': eventName,
        'eventStatus': eventStatus,
        'eventTime': Fn.dateFormat(new Date, 'yyyy-MM-dd hh:mm:ss'),
        'phoneNumber': PhoneBar.getCustPhoneNum(),
        'agentId': this.getUserInfo().employeeNumber,
      }).then(resp => {

      }).catch(err => {

      });
    },
    // 切割字符
    splitString(val) {
      let string = '';
      if(val){
        let stringArr = `${val}`.split("");
        string = `${stringArr[0]}${stringArr[1]}${stringArr[2]}${stringArr[3]}-${stringArr[4]}${stringArr[5]}-${stringArr[6]}${stringArr[7]} ${stringArr[8]}${stringArr[9]}:${stringArr[10]}${stringArr[11]}:${stringArr[12]}${stringArr[13]}`;
      }
      return string;
    },
    // 获取员工的固话状态值
    async getTelStatus() {
      let staffName = this.getEmployeeInfo('fullName');
      // 通过老接口查看用户的信息地址，再根据实际情况进行注册
      await this.$http.post(apiUrl.employee_TelStatus, {staffName})
        .then(res => {
        if(res.data.result && res.data.content){
          // 走专线还是外网电信服务器
          if(res.data.content.isIntranet == 'Y'){
            this.changeData_Intranet(1)
          } else {
            this.changeData_Intranet(2)
          }
          var agentIdData = res.data.content.agentId;
          if (agentIdData == null || agentIdData == "") {
            this.$message.error("呼叫中心获取人员信息失败,无法获取对应的坐席号,请关闭页签后重新打开");
            return;
          }
          this.agentId = /^\d+$/.test(agentIdData) ? Number(agentIdData) : agentIdData;
          console.log("坐席号:"+this.agentId);
        }else{
          //查不到相关信息的直接走外网
          this.changeData_Intranet(2)
        }
      }).catch(err => {
        this.$message.error(err);
      });
    },
    // 根据对应值进行设置
    changeData_Intranet(status){
      if(status == 1){
        //走专线
        this.serverIP =  '***********';
        this.sipPort= '5060';
      }else if(status == 2){
        //走公网
        this.serverIP =  '************';
        this.d= '5040';
      }
    },
	},
  // 组件
	components: {
		dropForm,
		PhoneDial
	},
  created(){
    // console.log('呼叫弹窗创建');
    this.$bus.on('interactionPopup',this.interactionPopup);
  },
	mounted: async function() {
	  // 获取员工初始化时候的状态
	  await this.getTelStatus();
    this.dataInit();
    this.getEmployeeData();
    setTimeout(() => {
      this.dropPopupBox();
      this.initPhoneBar1();
    }, 0);
	},
	beforeDestroy() {
    this.$bus.off('interactionPopup',this.interactionPopup);
    this.busy();
    this.destoryEventFn();
  }
};
</script>

<style scoped>
.container {
	/* border: 1px solid #969696!important; */
	background-color: white;
	box-shadow: 0 0 10px 1px #999;
	border-radius: 3px;
	position: absolute;
	max-height: 780px;
	top: 62px;
	left: 30%;
	z-index: 999;
}

#callcenterPopupHeader {
	height: 30px;
	background-color: #4f5f6f;
	color: white;
	border-radius: 3px 3px 0 0;
}
#callcenterPopupHeader .header-title {
	float: left;
	padding-left: 15px;
	line-height: 30px;
}
#callcenterPopupHeader .header-btn {
	float: right;
}
#callcenterPopupHeader .header-btn i {
	line-height: 30px;
	padding: 0 6px;
	cursor: pointer;
}

.main:after {
	content: "";
	height: 0;
	clear: both;
	overflow: hidden;
	display: block;
	visibility: hidden;
}

.phoneKey {
	display: inline-block;
	float: left;
	padding: 10px;
	border-right: 2px solid #ccc;
}
.phoneAns {
	display: inline-block;
	float: left;
	padding: 10px;
	width: 45%;
}

.failmain {
  text-align: center;
  padding-bottom: 20px;
}

.failmain .topP{
  font-size: 16px!important;
  line-height: 40px;
}

.failmain .left-box,.failmain .right-box{
  display: inline-block;
  width:45%;
  height: 120px;
  vertical-align: top;
  border:1px solid #ccc;
  padding:5px;
  margin: 0 5px;
  line-height: 20px;
}
</style>
