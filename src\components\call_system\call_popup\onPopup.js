function dropPopup(Box, header) {;
	header.style.cssText += ';cursor:move;';

	// 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
	const sty = (function() {
		if (window.document.currentStyle) {
			return (dom, attr) => dom.currentStyle[attr];
		} else {
			return (dom, attr) => getComputedStyle(dom, false)[attr];
		}
	})();

	header.onmousedown = (e) => {
		// 鼠标按下，计算当前元素距离可视区的距离
		const disX = e.clientX - header.offsetLeft;
		const disY = e.clientY - header.offsetTop;

		const screenWidth = document.body.clientWidth; // body当前宽度
		const screenHeight = document.documentElement.clientHeight; // 可见区域高度(应为body高度，可某些环境下无法获取)

		const BoxWidth = Box.offsetWidth; // 对话框宽度
		const Boxheight = Box.offsetHeight; // 对话框高度

		const minBoxLeft = Box.offsetLeft;
		const maxBoxLeft = screenWidth - Box.offsetLeft - BoxWidth;

		const minBoxTop = Box.offsetTop;
		const maxBoxTop = screenHeight - Box.offsetTop - Boxheight;

		// 获取到的值带px 正则匹配替换
		let styL = sty(Box, 'left');
		let styT = sty(Box, 'top');

		// 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
		if (styL.includes('%')) {
			styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100);
			styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100);
		} else {
			styL = +styL.replace(/\px/g, '');
			styT = +styT.replace(/\px/g, '');
		}

		document.onmousemove = function(e) {
			// 通过事件委托，计算移动的距离
			let left = e.clientX - disX;
			let top = e.clientY - disY;

			// 边界处理
			if (-left > minBoxLeft) {
				left = -minBoxLeft;
			} else if (left > maxBoxLeft) {
				left = maxBoxLeft;
			}

			if (-top > minBoxTop) {
				top = -minBoxTop;
			} else if (top > maxBoxTop) {
				top = maxBoxTop;
			}

			// 移动当前元素
			Box.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`;
		};

		document.onmouseup = function(e) {
			document.onmousemove = null;
			document.onmouseup = null;
		};
	};
}

export default dropPopup;
