<template>
  <div id='callCenterPopup' class='container' ref='callCenterPopup' :style="{ width: `${containerH}px` }"
    v-if="diaConfig.dialogVisible">
    <div id='callcenterPopupHeader'>
      <span class='header-title' v-html="diaConfig.title"></span>
      <span class='header-title' v-if="!if_Enlarge">{{ registerStatus }}</span>
      <div class="header-btn">
        <i class="el-icon-minus" @click="changeSize('small')" v-if='if_Enlarge'></i>
        <i class="el-icon-plus" @click="changeSize('big')" v-else></i>
        <i class="el-icon-close" @click="closeDialog"></i>
      </div>
    </div>
    <div v-if='if_Enlarge'>
      <div class='main' v-show="formData.sourceType != 'appointment_tracing'">
        <div v-if="phoneInitEvent === false"
          style='color:#ec2f0e;padding-top: 5px;padding-bottom: 5px;border: 2px solid rgb(204, 204, 204);border-radius: 5px;'>
          <p style="margin-bottom: 10px; margin-left: 20px;margin-right: 20px;line-height:18px;">{{ errorInfo }}</p>
        </div>
        <div style='border: 2px solid rgb(204, 204, 204);border-radius: 5px;' v-if="this.getCallInfo.agentNo">
          <div class="agent-box">
            <div class="agent-item agent-status" :style="{
              color: this.agentColorOptions[`${this.getCallInfo.agentState}`] || '#000'
            }">座席状态：{{ this.getCallInfo.stateName }}</div>
            <div class="agent-item agent-state">座席号：{{ this.getCallInfo.agentNo }}</div>
          </div>
          <drop-form :formCol='formCol' :formData='formData' @getPhone='getphoneBox'></drop-form>
        </div>
        <div class='phoneKey'>
          <out-call-box :canChange="true" :callOutDisabled="callOutDisabled" :hangUpDisabled="hangUpDisabled"
            :twiceCallOutDisabled="twiceCallOutDisabled" :phoneSuggest="[]" :phone="phone" ref="phoneDial" v-if="true"
            @makeCall="makeCall" @hangUp="handleHangUp" @sendDTMF="handleSendDTMF" @changePhone="changePhone" />
        </div>
        <div class='phoneAns'>
          <p style='font-size:14px;margin-bottom:10px;'>通话摘要: </p>
          <div style='margin-bottom:17px;'>
            <el-input type="textarea" :rows="18" placeholder="请输入内容" v-model="textarea" :maxlength="250" resize='none'>
            </el-input>
          </div>
          <div class="btn-group">
            <div class="btn-left">
            <el-button type="warning" @click='resignPhoneBar' :loading="registerLoading"
              :disabled="register">重新注册</el-button>
          </div>
          <div class="btn-right">
            <el-button type="primary" :disabled="saveBtnDisabled" :loading='saveBtnLoading' @click='saveTheCallData'>
              保存</el-button>
          </div>
          </div>
        </div>
      </div>
      <div class='main' v-show="formData.sourceType == 'appointment_tracing'">
        <div class='phoneAns'>

          <div style='float:left;'>
            <el-button type="warning" @click='resignPhoneBar' :loading="registerLoading" :disabled="register">
              重新注册</el-button>
          </div>

          <div style='float:right;'>
            <el-button type="primary" @click='callClick'>
              呼出</el-button>
            <el-button type="danger" @click='handleHangUp'>
              挂断</el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加音频标签 -->
    <audio ref="hangUpAudio" src="https://linsy-nbt-prod.oss-cn-shenzhen.aliyuncs.com/dst/e7155909bc8f4ee7a061383f405c8c01/hangup.mp3" preload="auto"></audio>
  </div>
</template>

<script>
import dropPopup from "./onPopup.js";
import dropForm from "./popupForm.vue";
import OutCallBox from "./out-call-box.vue";
import { apiUrl, makeUrl, EventBus } from "../base.js";
import { mapActions, mapMutations, mapGetters } from 'vuex'
import sensors from 'sa-sdk-javascript'
import PhoneBar from "../phoneBar.js";
import Fn from '@common/Fn.js'

const VIRTUALLY_MOBILE = /^1[1-9]\d{9}-\d{4}/
export default {
  name: "Popup",
  props: {
    diaConfig: {
      type: Object,
      required: true
    },
    // 数据项
    formCol: {
      type: Array,
      required: true
    },
    // 数据源
    formData: {
      type: Object,
      required: true
    }
  },
  watch: {
    formData: {
      handler(newVal) {
        // 传来新数据的时候，映射一下手机号
        this.pullThephone();
      },
      deep: true
    },
    'diaConfig.dialogVisible': {
      handler(newVal) {
        if (newVal) {
          this.initPhoneBar();
        }
      },
      immediate: true,
    }
  },
  data() {
    return {
      selectElement: "",
      containerH: 600, //组件宽度
      if_Enlarge: true, // 是否放大
      isCalling: false, // 呼叫中
      phone: "",
      savephone: "", //拨号之后将打的电话缓存起来
      textarea: "",
      errorInfo: "",
      // shouldSave: false,
      call_id: "", //通话记录表id
      // 禁用按钮
      saveBtnDisabled: true,
      callOutDisabled: true,
      twiceCallOutDisabled: true,
      hangUpDisabled: true,
      saveBtnLoading: false,
      phoneInitEvent: true, //电话条初始化是否成功
      agentColorOptions: {
        '1': 'red',
        '2': 'black',
        '3': '#00b700',
        '4': 'red',
        '6': 'red',
        '-2': 'red',
      },
      defaultIdleTimeoutDuration: 60 * 60 * 1000, //60分钟（单位：毫秒）
      idleTimeoutDuration: 0, // 30分钟（单位：毫秒）
      durationTimer: null,
    };
  },
  computed: {
    ...mapGetters('call', ['getCallInfo', 'getCallSdk']),
    registerLoading() {
      return this.getCallInfo.registerLoading
    },
    register() {
      return this.getCallInfo.register
    },
    checkSkillList() {
      return this.getCallInfo.checkSkillList
    },
    registerStatus() {
      if (this.getCallInfo.registerLoading) {
        return '电话条注册中...'
      }
      return this.getCallInfo.stateName;
    },
  },
  methods: {
    ...mapActions('call', ['initCall', 'login', 'updateState', 'loginSip', 'destroy', 'outCall', 'answer', 'hangup', 'sendDTMF', 'updateSdkCallInfo', 'checkInSkill']),
    ...mapMutations('call', ['setCallInfo']),
    getIdleStartTime() {
      const time = localStorage.getItem('idleStartTime');
      return time ? parseInt(time, 10) : null;
    },
    setIdleStartTime(time) {
      localStorage.setItem('idleStartTime', time);
    },
    clearIdleStartTime() {
      localStorage.removeItem('idleStartTime');
    },
    checkIdleTimeout() {
      const startTime = this.getIdleStartTime();
      if (!startTime) return false;

      const now = Date.now();
      if (now - startTime >= this.idleTimeoutDuration) {
        return true; // 超时
      }
      return false;
    },
    // 判断合并单是否呼叫占用
    judgeCanCall(){
      const self=this;
      if(!this.formData.mergeTradeNo){
        return true
      }
      return new Promise((resolve,reject)=>{
        self.ajax.get(`/order-web/api/callCenter/canCall?mergeTradeNo=${this.formData.mergeTradeNo}`,res=>{
          if(res.body.result){
            if(!res.body.content){
              resolve(true)
              return
            }
            if(!res.body.content.endTime){
              self.$message.warning("当前订单正在呼出中，请稍候再试")
              resolve(false)
              return
            }
            if(res.body.content.endTime){
              self.$message.warning(`该订单最近通话时间为${res.body.content.endTime}`)
              resolve(true)
            }
          }else{
            self.$message.error(res.body.msg)
            resolve(false)
          }
        },err=>{
          self.$message.error(err)
          reject(false)
        })
      })
    },
    // 记录合并订单占用
    recordMergeTradeNo(type){
      if(!this.formData.mergeTradeNo||!this.call_id){
        return
      }
      const api=type==='start'?'/order-web/api/callCenter/startCall':'/order-web/api/callCenter/endCall'
      const params={
        callId:this.call_id,
        mergeTradeNo:this.formData.mergeTradeNo
      }
      this.ajax.postStream(api, params, d => {
          if (!d.body.result) {
            this.$message.error(d.body.msg || "记录合并订单失败，请联系管理员");
          }
        }, err => {
          this.$message.error(err)
        })
    },
    callClick() {
      this.phone = this.formData.phone;
      this.makeCall()
    },
    callClose() {

      this.$emit('hangUp');
    },
    // 数据初始化
    initData() {
      this.saveBtnDisabled = true;
      this.callOutDisabled = false;
      this.twiceCallOutDisabled = true;
      this.hangUpDisabled = true;
      this.saveBtnLoading = false;
      this.phone = "";
      this.textarea = "";
      this.isCalling=false;
      this.call_id = "";
      if (!this.formData.notInitChoice) {
        this.pullThephone();
      }
      // 当含有已经拨打的手机号的时候，将手机号映射会拨号盘上，再清楚
      if (this.savephone) {
        this.phone = this.savephone;
      }
      this.savephone = "";
    },
    //当手机号只有一个的时候，直接将手机号拉取到拨号盘上
    pullThephone() {
      let phoneList = this.formData.phone;
      if (phoneList && phoneList.length === 1) {
        this.phone = phoneList[0].split("  ")[0];
      }
    },
    // 修改弹窗 -----------------------
    // 弹窗开启以及关闭状态 : 获取 || 直接修改
    interactionPopup(payload) {
      let { useModule, size } = payload;
      if (useModule == 1) {
        this.changeSize(size);
      }
    },
    // 修改弹窗大小
    changeSize(size) {
      let theEl = document.getElementById("callCenterPopup");
      // window.ceshi = theEl;
      if (size == "big") {
        theEl.style.top = '75px';
        theEl.style.left = '30%';
        this.if_Enlarge = true;
        this.containerH = 600;
      } else if (size == "small") {
        theEl.style.top = '10px';
        theEl.style.left = '40%';
        this.containerH = 222;
        this.if_Enlarge = false;
      }
    },


    // 重新注册电话条
    resignPhoneBar() {
      try {
            sensors.track('$WebClick', {$element_content: `重新注册-${this.phone}-${this.formData.sourceType||'未知'}-${this.formData.typeNum|'未知'}`})
      } catch (error) {}
      this.$confirm(
        "是否重新注册电话条(请确认电话条是否异常)",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "danger",
        }
      ).then(() => {
        this.initPhoneBar();
      }).catch(() => {

      });
    },
    // 关闭弹窗
    sureCloseDialog(){
      this.$bus.emit("changeCallProp", false);
      const audio = this.$refs.hangUpAudio;
      if (audio) {
        audio.currentTime=0;
        audio.pause().catch(error => { console.log('error', error) });
      }
    },
    // 关闭弹窗前置
    closeDialog() {
      // this.destroy();
      if ([4,-2].includes(this.getCallInfo.agentState)) {
        this.$message.warning("呼叫中，请挂断后再关闭")
        return
      }
      if (this.call_id && this.textarea.length > 0) {
        this.$confirm("内容未保存是否关闭？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
          }).then(()=>{
            this.sureCloseDialog();
          }).catch(() => {});
      }else{
        this.sureCloseDialog();
      }
    },    // 点击移动弹窗
    dropPopupBox() {
      let Box = document.getElementById("callCenterPopup");
      let header = document.getElementById("callcenterPopupHeader");
      dropPopup(Box, header);
    },
    // -------------------------------
    // 注册电话条
    async initPhoneBar() {
      try {
        // 已初始化，已注册默认置为空闲
        if (this.getCallSdk && this.register) {
          if (this.checkSkillList?.length === 0) {
            this.checkInSkill([]);
            return
          }
          if(this.checkSkillList?.length>0){
            this.updateState({
              state: 3,
              state_name: '空闲',
            });
          }
        }
        // 未注册已创建实例
        if (this.getCallSdk && !this.register) {
          await this.loginSip();
          this.phoneInitEvent = true;
          return;
        }
        // 未注册未创建实例
        if (!this.getCallSdk) {
          await this.login();
          const { agentNo, appKey, appSecret } = this.getCallInfo;
          if (!agentNo) {
            this.$message.warning('请先维护座席账号！');
            return;
          }
          await this.initCall({
            agentNo,
            appKey,
            appSecret,
          });
          this.phoneInitEvent = true;
          return;
        }
      } catch (err) {
        this.phoneInitEvent = false;
        this.errorInfo = err.message.msg || err.message || '电话条已出现异常，请点击【重新注册】按钮'
      }
    },

    handleAgentState() {
      const { agentState, stateName } = this.getCallInfo;
      //  坐席状态 1:离线 2:在线 3:空闲 4:忙碌 6:仅外呼 自定义状态-2:准备中
      if ([1, 2, 4, 6, -2].includes(agentState)) {
        this.callOutDisabled = true;
        this.hangUpDisabled = true;
        this.twiceCallOutDisabled = true;
      }
      if ([3].includes(agentState)) {
        this.callOutDisabled = false;
        this.hangUpDisabled = true;
        this.twiceCallOutDisabled = true;

        try{
            // 记录空闲开始时间
          if (!this.getIdleStartTime()) {
            this.setIdleStartTime(Date.now());
          }

          // 启动定时器
          if (!this.durationTimer) {
            this.durationTimer = setInterval(() => {
              console.log('时间长度 :>> ', this.idleTimeoutDuration);
              if (this.checkIdleTimeout()) {
                console.warn('空闲超时，即将销毁电话条');
                clearInterval(this.durationTimer);
                this.durationTimer = null;
                this.clearIdleStartTime();

                // 执行 destroy
                this.destroy();
              }
            }, 1000); // 每秒检查一次
          }
        }catch(e){
          console.log('err :>> ', err);
        }
      } else {
        // 停止计时器并清空本地存储
        if (this.durationTimer) {
          clearInterval(this.durationTimer);
          this.durationTimer = null;
        }
        this.clearIdleStartTime();
      }

      if ([4, 6].includes(agentState)) {
        this.hangUpDisabled = false;
        this.twiceCallOutDisabled = false;
      }
      if ([1, 2].includes(agentState)) {
        this.saveBtnDisabled = true;
        this.isCalling = false;
      }
    },
    async handleIncomingCall(data) {
      this.isCalling = true;
      const {
        type,
      } = data;
      if (type === 4) {
        try {
            sensors.track('$WebClick', {$element_content: `接听-${this.phone}-${this.formData.sourceType||""}-${this.formData.typeNum||""}`})
        } catch (error) {}
        try {
          await this.answer();
        } catch (err) {
          this.isCalling = false;
        }
      } else {
        this.$message.warning("接听失败，请稍候再试")
      }
    },
    playHangupSound() {
      const audio = this.$refs.hangUpAudio;
      if (audio) {
        audio.currentTime = 0; // 重置音频到开头
        audio.play().catch(error => {
          console.error('播放音频失败:', error);
        });

        // 设置播放时间
        const playDuration = 2000;

        // 清除之前的定时器（如果存在）
        if (this.audioTimeout) {
          clearTimeout(this.audioTimeout);
        }

         // 设置新的定时器
         this.audioTimeout = setTimeout(() => {
          audio.pause();
          audio.currentTime = 0; // 重置播放位置
        }, playDuration);
      }
    },
    handleHangUpSuccess() {
      this.playHangupSound();
      this.isCalling = false;
      this.setCallInfo({
        agentState: -2,
        stateName: '准备中',
      })
      this.handleAgentState();
      this.recordMergeTradeNo('end');
    },
    handleOutCallSuccess(data) {
      const { call_id } = data;
      this.call_id = call_id;
      this.saveBtnDisabled = false;
      this.recordMergeTradeNo('start');
    },
    handleSendDTMF(secondCallPhone) {
      this.sendDTMF(`${secondCallPhone}#`);
      try {
          sensors.track('$WebClick', {$element_content: `二次拨号-${this.phone}-${this.formData.sourceType||""}-${this.formData.typeNum||""}`})
      } catch (error) {}
    },
    // 保存数据
    async saveTheCallData() {
      const params = {
        "callId": this.call_id,
        "callNote": this.textarea
      }
      if (!this.textarea) {
        this.$message.error('请输入备注信息');
        return;
      }
      try {
            sensors.track('$WebClick', {$element_content: `保存-${this.phone}-${this.formData.sourceType||""}-${this.formData.typeNum||""}`})
      } catch (error) {}
      try {
        this.saveBtnLoading = true;
        this.ajax.postStream('/order-web/api/callCenter/saveNote', params, d => {
          if (d.body.result) {
            this.$message.success('保存成功');
            this.saveBtnLoading = false;
            this.textarea = '';
          } else {
            this.$message.error(d.body.msg)
            this.saveBtnLoading = false;
          }
        }, err => {
          this.$message.error(err)
          this.saveBtnLoading = false;
        })
      } finally {
        this.saveBtnLoading = false;
      }
    },
    // 初始化电话条事件
    initEventBusFn() {
      EventBus.on('agent_state', this.handleAgentState);
      EventBus.on('incomingcall', this.handleIncomingCall);
      EventBus.on('hangup_success', this.handleHangUpSuccess);
      EventBus.on('outcall_answer', this.handleOutCallSuccess);
    },
    // 销毁事件
    destroyEventFn() {
      EventBus.off('agent_state', this.handleAgentState);
      EventBus.off('incomingcall', this.handleIncomingCall);
      EventBus.off('hangup_success', this.handleHangUpSuccess);
      EventBus.off('outcall_answer', this.handleOutCallSuccess);
    },
    // 获取手机号
    getphoneBox(num) {
      this.phone = num;
    },
    // 修改手机号
    changePhone(num) {
      this.phone = num;
    },
    // 挂断
    handleHangUp() {
      this.hangup();
      try {
          sensors.track('$WebClick', {$element_content: `挂断-${this.phone}-${this.formData.sourceType||""}-${this.formData.typeNum||""}`})
      } catch (error) {}
    },

    // 呼出
    async makeCall() {
      try{
        const result = await this.judgeCanCall()
        if(!result){
          return
        }
      }catch{
        this.$message.warning('系统繁忙，请稍后再试！');
        return
      }

      let phone = this.phone;
      this.savephone = phone;
      if(!phone){
        this.$message.warning('请先输入呼叫号码');
        return
      }
      if (this.getCallInfo.agentState!==3) {
        this.$message.warning('只有【空闲】状态才能呼出！');
        return
      }
      // 更换手机号正则，首号为1，后续允许1~9  /^1(\d{10})$/
      if (phone && !/^[1](([3-9])[0-9]{9}$)|(([3-9])[0-9]{9}-[0-9]{4}$)/.test(phone)) {
        this.$message.error("手机号不正确");
        return;
      }
      if (this.checkSkillList.length === 0) {
        this.$message.warning("请先维护技能组")
        return
      }
      const skillId = this.checkSkillList[0].skill_id;
      const { type, typeNum } = this.formData;
      const userData = {
        externalNo: typeNum || '',
        sourceType: this.formData.sourceType || ''
      }
      this.call_id = '';
      this.saveBtnDisabled = true;
      this.hangUpDisabled = true;
      this.outCall({
        userCalled: VIRTUALLY_MOBILE.test(phone) ? phone.split('-')[0] : phone,
        skillId,
        userData
      });
      try {
          sensors.track('$WebClick', {$element_content: `呼出-${this.phone}-${this.formData.sourceType||""}-${this.formData.typeNum||""}`})
      } catch (error) {
        console.log('error', error)
      }
    },
  },
  // 组件
  components: {
    dropForm,
    OutCallBox
  },
  created() {
    this.initData();
    this.$bus.on('interactionPopup', this.interactionPopup);
    // 初始化事件
    this.initEventBusFn();
    let seconds=0;
    try{
      seconds=__AUX.get('CALL_CENTER_SWITCH').find(item=>item.code==='OUT_CALL_DURATION').ext_field1||0;
    }catch{
      seconds=0;
    }
    this.idleTimeoutDuration = seconds * 1000 || this.defaultIdleTimeoutDuration
  },
  mounted: async function () {
    setTimeout(() => {
      this.dropPopupBox();
    }, 0);
  },
  beforeDestroy() {
    this.$bus.off('interactionPopup', this.interactionPopup);
    // this.destroy();
    this.destroyEventFn();
  }
};
</script>

<style scoped>
.container {
  /* border: 1px solid #969696!important; */
  background-color: white;
  box-shadow: 0 0 10px 1px #999;
  border-radius: 3px;
  position: absolute;
  max-height: 780px;
  top: 62px;
  left: 30%;
  z-index: 999;
}

#callcenterPopupHeader {
  height: 30px;
  background-color: #4f5f6f;
  color: white;
  border-radius: 3px 3px 0 0;
}

#callcenterPopupHeader .header-title {
  float: left;
  padding-left: 15px;
  line-height: 30px;
}

#callcenterPopupHeader .header-btn {
  float: right;
}

#callcenterPopupHeader .header-btn i {
  line-height: 30px;
  padding: 0 6px;
  cursor: pointer;
}

.main:after {
  content: "";
  height: 0;
  clear: both;
  overflow: hidden;
  display: block;
  visibility: hidden;
}

.phoneKey {
  display: inline-block;
  float: left;
  padding: 10px;
  border-right: 2px solid #ccc;
}

.phoneAns {
  display: inline-block;
  float: left;
  padding: 10px;
  width: 45%;
}

.failmain {
  text-align: center;
  padding-bottom: 20px;
}

.failmain .topP {
  font-size: 16px !important;
  line-height: 40px;
}

.failmain .left-box,
.failmain .right-box {
  display: inline-block;
  width: 45%;
  height: 120px;
  vertical-align: top;
  border: 1px solid #ccc;
  padding: 5px;
  margin: 0 5px;
  line-height: 20px;
}

.agent-box {
  display: flex;
  align-items: center;
  padding: 10px 20px 10px;
  border-bottom: 1px solid #ccc;
  justify-content: space-between;

  .agent-item {
    margin-right: 10px;
  }

  .agent-status {
    font-weight: bold;
    font-size: 16px !important;
  }
}
  .btn-group{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .desc{
    margin-top: 10px;
    line-height: 1.5;
  }
</style>
