<template>
	<div class="call_out_list">
		<div class="headerBox">
			<span class="headerPa" v-for="(item, index) in params.listData" :key="index"
				>{{ item.label }}: <span style="font-weight:900;">{{ item.value }}</span></span
			>
		</div>
		<div style="height: 288px;" class="xpt-flex__bottom">
			<xpt-list
				ref="call_out_list"
				:showHead="false"
				:orderNo="true"
				:colData="colData"
				:data="listdata"
				selection="none"
			></xpt-list>
		</div>
	</div>
</template>

<script>
import { makeUrl, apiUrl, EventBus } from "../base.js";
export default {
	name: "call_out_list",
	props: ["params"],
	data() {
		let self = this;
		return {
			listBtns: [],
			colData: [
				{
					label: "座席号",
					prop: "belongAgentId",
          width: '50'
				},
				{
					label: "处理人",
					prop: "belongAgentName",
					width: '100'
				},
				{
					label: "电话开始时间",
					prop: "startTime",
          width: '130'
				},
				{
					label: "电话结束时间",
					prop: "endTime",
          width: '130'
				},
				{
            width: '95',
            label: "呼叫电话",
            prop: "customerNumber",
          },
				{
            width: '60',
            label: "是否接通",
            prop: "callResult",
            formatter: val => {
             return val===0 ? "接通" : "失败";
            },
          },
				  {
            width: '100',
            label: "通话摘要",
            prop: "callNote",
          },
				{
					label: "录音",
					prop: "recordUrl",
					redirectClick(row) {
						if (row.recordUrl) {
							window.open(row.recordUrl);
						}
					},
					formatter: val => {
						if (val) return "播放";
					},
          width: 50
				}
			],
			listdata: []
		};
	},
	watch: {},
	methods: {
    // 获取新呼叫列表信息
		getList() {
			let params = this.params.searchParams;
			this.$http
				.post(apiUrl.callLog_list_pro, params)
				.then(res => {
					if (res.data.result) {
						this.listdata = res.data.content||[];
					} else {
						this.$message.error(res.data.msg);
					}
				})
				.catch(err => {
          this.$message.error('呼叫中心报错: ',err.status,err.message);
				})
				.finally(() => {});
		},
	},
	mounted() {
		this.getList();
	},
	destroyed() {}
};
</script>

<style scoped lang="stylus">
.call_out_list {
	padding-top: 5px;

	.headerBox {
		padding-left: 5px;
		border: 1px solid #ccc;
		border-radius: 5px;
		margin-bottom: 10px;
		max-height: 61px;
		overflow: auto;

		.headerPa {
			display: inline-block;
			font-size: 14px;
			line-height: 35px;
			margin-right: 40px;
      /*width: 32%;*/
		}
	}
}
</style>

<style>
  .remarkClassByCallOutList{
    height: 100% !important;
  }
  .remarkClassByCallOutList .cell{
    height: 100% !important;
    line-height: 20px!important;
  }
  .remarkClassByCallOutList p{
    white-space: pre-line;
  }
</style>
