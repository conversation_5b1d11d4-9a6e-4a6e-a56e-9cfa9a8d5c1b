<template>
	<div class="formData">
		<div class="dataItem" v-for="(item, index) in formCol" :key="index">
			<div v-if="item.label == 'type'">
				<span class="dataLabel">{{ item.value }}:</span>
				<span class="dataValue-type">{{ formData[item.label] }}</span>
			</div>
			<div v-else-if="item.label == 'phone'">
				<span class="dataLabel">{{ item.value }}:</span>
				<div class="phone-box">
					<p
						style="margin:0;"
						:class="{ phoneItem: formData[item.label].length > 1 }"
						v-for="(item1, index1) in formData[item.label]"
						:key="index1"
					>
						<span class="dataValue-phone" @click="getPhone($event)">{{ item1 }}</span>
						<span v-show="formData.sourceType == 'batch_order'||formData.sourceType == 'return_visit'" class="reset-phone" @click="getVirtualMobileForTwo()">重新获取</span>
					</p>
				</div>
			</div>
			<div v-else>
				<span class="dataLabel">{{ item.value }}:</span>
				<span class="dataValue">{{ formData[item.label] }}</span>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		// 数据项
		formCol: {
			type: Array,
			required: true
		},
		// 数据源
		formData: {
			type: Object,
			required: true
		}
	},
	data() {
		let self = this;
		return {};
	},
	watch: {},
	methods: {
		getVirtualMobileForTwo(){
			let self = this;
			// let phone = this.formData.phone;
			let phone = this.formData.phone[0].split("  ")[0];
			let data = {receiverMobile: phone}
			if(!!this.formData.tid){
				data.tid = this.formData.tid;
			}
      data.addressId = this.formData?.address_id
			this.ajax.postStream('/encryption-web/api/encryption/getVirtualMobileForTwo', data, res => {
                if(!res.body.result) {
                this.$message.error(res.body.msg)
                return
                }else{
					this.$message.success('获取成功')
					self.$nextTick(()=>{
						self.$set(this.formData,'phone',[res.body.content.receiverMobile + '  '+this.formData.name])
						// this.formData.phone[0] = res.body.content.receiverMobile;
						this.$emit("getPhone", res.body.content.receiverMobile);
					});


				}



            }, err => {
                this.$message.error(err);
            });
		},
		getPhone(el) {
			let phone = el.target.innerHTML.split("  ")[0];
			this.$emit("getPhone", phone);
		}
	},
	mounted() {},
	destroyed() {}
};
</script>

<style scoped>
.formData {
	max-height: 180px;
	padding: 10px;
	overflow-y: auto;
}
.dataItem {
	line-height: 25px;
	width: 50%;
	float: left;
}
.phoneItem {
	border-bottom: 1px solid #777;
}
.dataLabel {
	display: inline-block;
	width: 60px;
	margin-right: 10px;
	text-align: right;
	float: left;
}
.dataValue,
.dataValue-type,
.phone-box {
	float: left;
	width: calc(95% - 60px);
}
.dataValue-type {
	font-size: 14px;
	font-weight: 900;
	color: #3c97f6;
}
.dataValue-phone {
	width: auto !important;
	color: #3c97f6;
	cursor: pointer;
}
.reset-phone {
	width: auto !important;
	color: #136fce;
	cursor: pointer;
	font-weight: 800;
}

</style>
