<template>
  <div style="height:100%;margin-top: 5px;display: flex;flex-direction: column">
    <span >
      <select-tab ref="tab" class="call-record-carb-tab" @changeTab="selectedTab" :tabs="tabs"
                  :selectedCode="selectCode"></select-tab>
    </span>

    <div class="mark-button" v-if="selectCode == 'untreated'">
      <el-popover ref="popoverSign" width="225" trigger="click">
        <div class="sign-choose">
          <div class="sign-item1" @click="mark('NEED_FOLOW')">需要跟进</div>
          <div class="sign-item2" @click="mark('PROCESS_TODAY')">当天处理</div>
          <div class="sign-item3" @click="mark('URGENCY')">紧急处理</div>
          <div class="sign-item4" @click="mark('BEFORE_NOON')">上午12点前</div>
          <div class="sign-item5" @click="mark('')">清除颜色</div>
        </div>
      </el-popover>
      <span style="position:relative;left:100px;display:flex;align-items:center">
       <el-button :disabled="selectedRows.length == 0" size="mini" type="warning" v-popover:popoverSign>标记</el-button>
       <el-button :disabled="selectedRows.length == 0" size="mini" type="warning" @click="sendMsg" :loading="isSelectSendingMsg">发送短信</el-button>
       <el-button-group style="margin-left: 10px;">
          <el-button type="primary" size="mini" @click="switchPhoneNumberShow(true)" :loading="isShowPhoneNumbering">显示号码</el-button>
          <el-button type="primary" size="mini" @click="switchPhoneNumberShow(false)">隐藏号码</el-button>
        </el-button-group>
      </span>
    </div>
    <div class="mark-button" v-else>
      <span style="position:relative;left:100px;display:flex;align-items:center">
        <el-button :disabled="selectedRows.length == 0" size="mini" type="warning" @click="sendMsg" :loading="isSelectSendingMsg">发送短信</el-button>
        <el-button-group style="margin-left: 10px;">
          <el-button type="primary" size="mini" @click="switchPhoneNumberShow(true)" :loading="isShowPhoneNumbering">显示号码</el-button>
          <el-button type="primary" size="mini" @click="switchPhoneNumberShow(false)">隐藏号码</el-button>
        </el-button-group>
      </span>
    </div>
    <div style="display: flex; flex-direction: column; flex-grow: 1">
      <el-table
        id="call-record-tableOne"
        class="table-one"
        @row-click="selectTableRow"
        v-loading="loading"
        :data="tableOneData"
        border
        ref="table"
        @selection-change="handleSelectionChange"
        :row-style="rowStyle">
        <el-table-column
          type="selection"></el-table-column>
        <el-table-column prop="jobNumber" label="工单号" show-overflow-tooltip width="120">
          <template slot-scope="scope">
            <a href="javascript:void(0)" @click="detail(scope.row)">{{scope.row.jobNumber}}</a>
          </template>
        </el-table-column>
        <el-table-column prop="calledNumber" label="来电号码" width="175" show-overflow-tooltip
                         :render-header="renderCalledNumberHeader">
          <!-- <template slot-scope="scope">
            <el-input class="table-search-input" placeholder="可搜索" size="mini" v-model="filters.calledNumber" @change="filterData" v-if="scope.row.isSearch"></el-input>
            <span v-else>
              {{scope.row.calledNumber}}
            </span>
          </template> -->
          <template slot-scope="scope">
            <a style="color:Black" href="javascript:void(0)" @click.stop="callPhone(scope.row.calledNumber, scope.row)">
              <template v-if="showphoneNumber">{{scope.row.calledNumber}}</template>
              <template v-else>{{scope.row.calledNumber | hidePhoneNumber}}</template>
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="custName" label="买家昵称" width="175" show-overflow-tooltip
                         :render-header="renderCustNameHeader">
          <!-- <template slot-scope="scope">
            <el-input class="table-search-input" placeholder="可搜索" size="mini" v-model="filters.custName" @change="filterData" v-if="scope.row.isSearch"></el-input>
            <span v-else>
              {{scope.row.custName}}
            </span>
          </template> -->
        </el-table-column>
        <el-table-column prop="receiverName" label="收货人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="receiverPhone" label="收货号码" width="175" show-overflow-tooltip
                         :render-header="renderReceiverNumberHeader">
          <!-- <template slot-scope="scope">
            <el-input class="table-search-input" placeholder="可搜索" size="mini" v-model="filters.receiverPhone" @change="filterData" v-if="scope.row.isSearch"></el-input>
            <span v-else>
              {{scope.row.receiverPhone}}
            </span>
          </template> -->
          <template slot-scope="scope">
            <a style="color:Black" href="javascript:void(0)" @click.stop="callPhone(scope.row.receiverPhone,scope.row)">
               <template v-if="showphoneNumber">{{scope.row.receiverPhone}}</template>
              <template v-else>{{scope.row.receiverPhone | hidePhoneNumber}}</template>
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="salesUserName" label="客服" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="salesGroupName" label="客服分组" width="120" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column prop="ifOutTime" label="是否超时" width="120"  show-overflow-tooltip >
           <template slot-scope="scope">
            <span>
              {{scope.row.ifOutTime == "Y"?"是":"否"}}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column prop="markRemark" label="标记备注" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="markedUserName" label="标记人" width="175" show-overflow-tooltip
                         :render-header="renderMarkedHeader"></el-table-column>
        <el-table-column prop="creatorName" label="创建人" width="175" show-overflow-tooltip
                         :render-header="renderCreater"></el-table-column>
        <el-table-column prop="sourceOrderType" label="来源单据" show-overflow-tooltip width="120"></el-table-column>
        <el-table-column prop="sourceOrderNo" label="来源单号" show-overflow-tooltip width="120"></el-table-column>
        <el-table-column prop="jobType" label="所选服务" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="callingType" label="来电类型" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderOwnerType" label="归属" width="120" show-overflow-tooltip></el-table-column>
        <!-- <template slot-scope="scope">
           <el-input class="table-search-input" placeholder="可搜索" size="mini" v-model="filters.markedUserName" @change="filterData" v-if="scope.row.isSearch"></el-input>
           <span v-else>
             {{scope.row.markedUserName}}
           </span>
         </template> -->
        <el-table-column prop="markeTime" label="标记时间" width="120" show-overflow-tooltip></el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="approvedUserName" label="审核人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="approvedTime" label="审核时间" width="120" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-row type="flex" justify="end" style="align-items:center">
        <span style="color: #48576a">筛选后 {{tableOneData.length}}</span>
        <el-pagination
          layout="total, sizes, prev, pager, next"
          :page-sizes="[200, 400, 600]"
          :page-size="pageSize"
          :total="totalNum"
          :current-page="pageNow"
          @size-change="pageSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </el-row>
    </div>
    <div class="call-detail-box" style="">
      <select-tab-two class="call-record-carb-tab" :currentComponent="currentComponent" :tabsTwo="tabsTwo"
                      @changeComponent="changeComponent"></select-tab-two>
      <div class="call-delete-button" v-if="currentComponent == 'tableTwo' && 'untreated' == selectCode && isTelTraffic">
        <el-button size="mini" type="danger" @click="deleteTwo">删除</el-button>
      </div>
      <table-two ref="tableTwo" class="my-height" v-show="currentComponent == 'tableTwo'" :jobId="jobId" :telTraffic='{isTelTraffic,isTelTrafficBoss}'
                 style="flex-grow: 1"/>
      <table-three ref="tableThree" class="my-height" v-show="currentComponent == 'tableThree'" :jobId="jobId"
                   style="flex-grow: 1"/>
    </div>
  </div>
</template>

<script>
  import baseUrl, {makeUrl, apiUrl,EventBus} from './base.js';
  import selectTab from '@/components/call_system/selectTab';
  import tableTwo from '@/components/call_system/tableTwo';
  import tableThree from '@/components/call_system/tableThree';
  import selectTabTwo from './selectTabTwo';
  import Fn from '@/common/Fn.js';
  import userStatus from "./userStatus";

  export default {
    mixins: [ userStatus ],
    props: ['condition'],
    components: {
      selectTab,
      tableTwo,
      tableThree,
      selectTabTwo,
      isEmployee:false,
    },
    data() {
      return {
        selectCode: 'untreated',
        selectItem: null,
        tabs: [
          {
            name: '未处理',
            code: 'untreated',
            isInvalid: 0,
            isApproved: 0,
            ifOutTime:0,
            makeName(count) {
              this.name = count ? '未处理(' + count + ')' : '未处理';
            },
          },
          {
            name: '已处理',
            code: 'processed',
            isInvalid: 0,
            isApproved: 1,
            ifOutTime:0,
            makeName(count) {
              this.name = count ? '已处理(' + count + ')' : '已处理';
            },
          },
          {
            name: '已作废',
            code: 'abandoned',
            isInvalid: 1,
            isApproved: 0,
            ifOutTime:0,
            makeName(count) {
              this.name = count ? '已作废(' + count + ')' : '已作废';
            },
          },
          // {
          //   name: '已超时',
          //   code: 'overTime',
          //   isInvalid: 0,
          //   isApproved: 0,
          //   ifOutTime:1,
          //   makeName(count) {
          //     this.name = count ? '已超时(' + count + ')' : '已超时';
          //   },
          // },
        ],
        tabsTwo: [
          {
            code: 'tableTwo',
            name: '详细信息',
          },
          {
            code: 'tableThree',
            name: '操作记录',
          },
        ],
        currentComponent: 'tableTwo',
        tableOneData: [],
        tableOneDataNoFilter: [],
        loading: false,
        totalNum: 0,
        jobId: null,
        selectRow: null,
        pageSize: 200,
        pageNow: 1,
        filters: {
          calledNumber: '',
          custName: '',
          receiverPhone: '',
          markedUserName: '',
          creatorName: '',
        },
        selectedRows: [],
        isSelectSendingMsg:false,
        showphoneNumber:false,//收货号码，来电号码是否全部显示
        isShowPhoneNumbering:false
      };
    },
    methods: {
      handleSelectionChange(rows) {
        this.selectedRows = rows;
      },
      init(selectCode) {
        if (selectCode && this.selectCode != selectCode) {
          // 选择了不同状态时，就清空筛选条件
          this.selectCode = selectCode;
        }
        this.jobId = null;
        this.getOneTablelist();
        this.$refs['tableTwo'] ? this.$refs['tableTwo'].clear() : null;
        this.$refs['tableThree'] ? this.$refs['tableThree'].clear() : null;
      },
      exportTable() {
        let params = {
          isInvalid: 'abandoned' == this.selectCode ? 1 : 0,
          isApproved: 'processed' == this.selectCode ? 1 : 0,
          ifOutTime: 'overTime' == this.selectCode ? 1 : 0,
        };
        this.tableOneData = [];
        this.loading = true;
        params['pageNo'] = this.pageNow;
        params['pageSize'] = this.pageSize;
        this.$emit('before-search');
        params = Object.assign({}, params, this.condition);
        if (this.condition.startDate) {
          Object.assign(params, {startDate: this.condition.startDate.toISOString().substr(0, 10).replace('T', ' ')});
        }
        if (this.condition.endDate) {
          Object.assign(params, {endDate: this.condition.endDate.toISOString().substr(0, 10).replace('T', ' ')});
        }
        let makeUrl1 = makeUrl(apiUrl.workOrder_export, params);

        window.open(makeUrl1);
      },
      selectedTab(obj) {
        console.log(obj,"selectedTab")
        this.$emit('change-tab', obj);
        this.selectCode = obj.code;
        this.selectItem = obj;
        this.filters.calledNumber = '';
        this.filters.custName = '';
        this.filters.receiverPhone = '';
        this.filters.markedUserName = '';
        this.filters.creatorName = '';
        this.getOneTablelist();
      },
      obtainQueryParam(){
        let params = {
          isInvalid: 'abandoned' == this.selectCode ? 1 : 0,
          isApproved: 'processed' == this.selectCode ? 1 : 0,
          ifOutTime: 'overTime' == this.selectCode ? 1 : 0,
        };
        this.tableOneData = [];
        this.loading = true;
        params['pageNo'] = this.pageNow;
        params['pageSize'] = this.pageSize;
        this.$emit('before-search');
        params = Object.assign({}, params, this.condition);
        if (this.condition.startDate) {
          Object.assign(params, {startDate: Fn.dateFormat(this.condition.startDate, 'yyyy-MM-dd')});
        }
        if (this.condition.endDate) {
          Object.assign(params, {endDate: Fn.dateFormat(this.condition.endDate, 'yyyy-MM-dd')});
        }
        return params;
      },
      getOneTablelist() {
        let params = {
          isInvalid: 'abandoned' == this.selectCode ? 1 : 0,
          isApproved: 'processed' == this.selectCode ? 1 : 0,
          ifOutTime: 'overTime' == this.selectCode ? 1 : 0,
          isEmployee:this.isEmployee
          
        };

        this.tableOneData = [];
        this.loading = true;
        params['pageNo'] = this.pageNow;
        params['pageSize'] = this.pageSize;
        // params['isEmployee'] = this.isEmployee;
        this.$emit('before-search');
        params = Object.assign({}, params, this.condition);
        if (this.condition.startDate) {
          Object.assign(params, {startDate: Fn.dateFormat(this.condition.startDate, 'yyyy-MM-dd')});
        }
        if (this.condition.endDate) {
          Object.assign(params, {endDate: Fn.dateFormat(this.condition.endDate, 'yyyy-MM-dd')});
        }
        console.log(params,"params");
        this.$http.get(apiUrl.workOrder_recordDetalis, {params}).then((res) => {
          if (res.data && res.data.code === 0) {
            this.tableOneData = res.data.content;
            this.tableOneDataNoFilter = this.tableOneData;
            // this.tableOneData.unshift({isSearch: true})

            if (this.selectItem)
              this.selectItem.makeName(res.data.totalNum);
            else
              this.tabs[0].makeName(res.data.totalNum);
            this.totalNum = res.data.totalNum;
            this.$nextTick(() => {
              if (this.tableOneData.length > 0) {
                this.$refs.table.setCurrentRow(this.tableOneData[0]);
                this.filterData()
              }
            });
          }

        }).catch(err => {
          console.error(error)
          this.$message.error(err);
        }).finally(() => {
          this.loading = false;
          this.selectedRows = [];
        });
      },
      changeComponent(code) {
        this.currentComponent = code;
        this.$emit('change-component', code);
        if (this.$refs && this.$refs[code].tableData.length == 0)
          this.$refs[code].search(this.jobId);
      },
      // 单击
      selectTableRow(row, event) {
        this.selectRow = row;  //保存选中行的数据方便标记
        this.$emit('table-click', row);
        if (this.jobId != row.jobId) {
          this.jobId = row.jobId;
          this.$refs[this.currentComponent].search(row.jobId);
        }
      },
      // 格式化时间
      fomterTime(date) {
        return Fn.dateFormat(date, 'yyyy-MM-dd hh:mm:ss');
      },
      detail(row) {
        this.$emit('job-click', row);
      },

      deleteTwo() {
        this.$refs['tableTwo'].deleteData();
      },
      mark(markType) {
        let _this = this;
        let jobId = this.jobId;
        if (null == this.selectedRows || this.selectedRows.length == 0) {
          this.$message.error('请选择工单');
          return;
        }

        let jobIds = [];
        for (let r of this.selectedRows) {
          jobIds.push(r.jobId);
        }
        let param = {jobId: jobIds.join(','), markType: markType};

        this.$http.post(apiUrl.workOrder_mark, param, {emulateJSON: true}).then(res => {
          if (res.data && res.data.code === 0) {
            for (let r of this.selectedRows) {
              r.markType = markType;
            }
            this.$refs.popoverSign.doClose();
            this.$message.success('成功');
          } else {
            this.$message.error(res.data.msg);
          }

        }).catch(err => {
          this.$message.error(err);
        }).finally(() => {
          this.$refs.table.clearSelection();
          this.$refs.table.setCurrentRow();
        });
      },
       sendMsg(){
        if(this.selectedRows.length==0){
          return
        }
        let phonelist=this.selectedRows.map(item=>item.calledNumber).filter(Boolean)
        let params={
           "mobiles":phonelist,
        }
        this.isSelectSendingMsg=true;
        this.$http.post(apiUrl.send_msg, params).then(res => {
          console.log(res)
          if (res.data.result) {
            this.$message.success(res.data.msg);
          } else {
            this.$message.error(res.data.msg);
          }
        }).catch(err => {
          this.$message.error(err);
        }).finally(() => {
          this.$refs.table.clearSelection();
          this.$refs.table.setCurrentRow();
          this.isSelectSendingMsg=false;
        });
      },
      async switchPhoneNumberShow(type){
        if(this.showphoneNumber&&type){
          this.$message.warning('号码已全部显示')
          return
        }
        this.isShowPhoneNumbering=true
        type?await this.saveUserSensitiveLookLog():''
        this.isShowPhoneNumbering=false
        this.showphoneNumber=type
      },
      saveUserSensitiveLookLog(){
        let self=this
        let params={}
        params.operatePageName=this.activeTabName;
        params.operateType='LOOK';
        params.lookContent='来电号码/收货号码';
        params.number='';
        return new Promise((resolve,reject)=>{
          self.ajax.postStream('/user-web/api/saveUserSensitiveLookLog',params,res=>{
            if(res.body.result){
              resolve()
            }else{
              self.$message.error(res.body.msg)
              reject()
            }
          },err=>{
              self.$message.error(err)
              reject()
          })
        })
      },
      rowStyle(row, index) {
        //设置行的颜色
        let styleStr = '';
        if (this.jobId == row.jobId) {
          styleStr += 'font-weight:bold;';
        }

        if (this.selectCode == 'untreated') {
          switch (row.markType) {
            case 'NEED_FOLOW':
              styleStr += 'background: rgb(47, 209, 169);';
              break;
            case 'PROCESS_TODAY':
              styleStr += 'background: rgb(255, 178, 192);';
              break;
            case 'URGENCY':
              styleStr += 'background: rgb(255, 0, 0);';
              break;
            case 'BEFORE_NOON':
              styleStr += 'background: rgb(0, 143, 0);';
              break;
            default:
              break;
          }
        }
        return styleStr;
      },
      handleCurrentChange(pageNo) {
        this.pageNow = pageNo;
        this.getOneTablelist();
      },
      pageSizeChange(pageSize) {
        this.pageSize = pageSize;
        this.getOneTablelist();
      },
      filterData() {
        this.tableOneData = this.tableOneDataNoFilter.filter((item) => {
          let calledNumberFalg = true;
          let custNameFalg = true;
          let receiverPhoneFalg = true;
          let markedUserFalg = true;
          let createrFalg = true;
          if (this.filters.calledNumber) {
            if (typeof item.calledNumber == 'string' && item.calledNumber.indexOf(this.filters.calledNumber) >= 0) {
              calledNumberFalg = true;
            } else {
              calledNumberFalg = false;
            }
          }
          if (this.filters.receiverPhone) {
            if (typeof item.receiverPhone == 'string' && item.receiverPhone.indexOf(this.filters.receiverPhone) >= 0) {
              receiverPhoneFalg = true;
            } else {
              receiverPhoneFalg = false;
            }

          }
          if (this.filters.custName) {
            if (typeof item.custName == 'string' && item.custName.indexOf(this.filters.custName) >= 0) {
              custNameFalg = true;
            } else {
              custNameFalg = false;
            }

          }
          if (this.filters.markedUserName) {
            if (typeof item.markedUserName == 'string' && item.markedUserName.indexOf(this.filters.markedUserName) >=
              0) {
              markedUserFalg = true;
            } else {
              markedUserFalg = false;
            }

          }

          if (this.filters.creatorName) {
            if (typeof item.creatorName == 'string' && item.creatorName.indexOf(this.filters.creatorName) >=
              0) {
              createrFalg = true;
            } else {
              createrFalg = false;
            }

          }

          return calledNumberFalg && custNameFalg && markedUserFalg && receiverPhoneFalg && createrFalg;
        });
        // if (this.tableOneDataNoFilter.length!== this.tableOneData.length) {
        //   this.tableOneData.unshift({isSearch: true})
        // }

      },
      callPhone(val, row) {
        this.$emit('call-number', {row, phone: val});
      },
      renderCalledNumberHeader(h, {column, $index}) {

        return h('div', [
          h('span', {}, '来电号码'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters.calledNumber,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.calledNumber = value;
              },
            },
          }),
        ]);
      },
      renderCreater(h, {column, $index}) {

        return h('div', [
          h('span', {}, '创建人'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': 'el-input el-input--mini table-search-input ',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              size: 'mini',
              value: this.filters.creatorName,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.creatorName = value;
              },
            },
          }),
        ]);
      },
      renderMarkedHeader(h, {column, $index}) {

        return h('div', [
          h('span', {}, '标记人'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': 'el-input el-input--mini table-search-input ',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              size: 'mini',
              value: this.filters.markedUserName,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.markedUserName = value;
              },
            },
          }),
        ]);
      },
      renderCustNameHeader(h, {column, $index}) {

        return h('div', [
          h('span', {}, '买家昵称'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': 'el-input el-input--mini table-search-input ',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              size: 'mini',
              value: this.filters.custName,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.custName = value;
              },
            },
          }),
        ]);
      },
      renderReceiverNumberHeader(h, {column, $index}) {

        return h('div', [
          h('span', {}, '收货号码'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': 'el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              size: 'mini',
              value: this.filters.receiverPhone,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.receiverPhone = value;
              },
            },
          }),
        ]);
      },
      loadUserIncumbency(rsolve) {
        var empolyeeObj = this.getEmployeeInfo();

        this.$http.post('/user-web/api/userPerson/getUserPersonList',{"employeeNumber":empolyeeObj.employeeNumber}).then(res => {
          if (res.data && !!res.data.content && res.data.content.count > 0) {
            // this.formData.status = res.body.content.list[0].incumbency;
            this.isEmployee = res.data.content.list[0].type == "EMPLOYEE"?true:false;
            rsolve && rsolve();
          }
        });
      },
        order_savedFn(jobId){
            if (this.selectRow && this.selectRow.jobId && jobId == this.selectRow.jobId) {
                this.$refs.tableTwo.search(this.selectRow.jobId);
            }
        }


    },
    mounted() {
      this.loadUserIncumbency(()=>{
        this.init();
      })
      EventBus.on(EventBus.order_saved,this.order_savedFn)
    },
      beforeDestroy(){
          EventBus.off(EventBus.order_saved,this.order_savedFn)
      }
  };
</script>
<style lang="stylus">
  #call-record-tableOne {
    .el-table__body .tableRowClass:hover td {
      background: inherit !important;
    }

    .tableRowClass.current-row {
      background: inherit;
    }
  }
</style>
<style scoped>
  .table-one {
    flex-grow: 1;
    margin-bottom: 10px;
    height: 0 !important;
  }

  .select-tab-two {
    position: relative;
    top: 1px;
  }

  .select-tab-two span {
    height: 26px;
    line-height: 26px;
    padding: 0 10px;
    display: inline-block;
    cursor: pointer;
    border-bottom: solid transparent 2px;
  }

  .select-tab-two span.active {
    color: #20a0ff;
    border-bottom: solid #20a0ff 2px;
  }

  .my-height {
    height: 100%;
  }

  .bj-btn {
    width: 100%;
    height: 30px;
  }

  .bj-btn .el-button {
    padding: 5px 0 !important;
    margin: 3px 0 0 10px !important;
  }

  .sign-choose div {
    width: 200px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    cursor: pointer;

  }

  .sign-choose div:hover {
    background: #dfe6ec;
  }

  .sign-item1 {
    background: rgb(47, 209, 169) !important;

  }

  .sign-item2 {
    background: rgb(255, 178, 192) !important;
  }

  .sign-item3 {
    background: rgb(255, 0, 0) !important;
  }

  .sign-item4 {
    background: rgb(0, 143, 0) !important;
  }

  .sign-item5 {
    background: rgb(253, 253, 253)
  }

  .table-search-input {
    width: 109px;
  }

  .mark-button {
    position: absolute;
    top: 10px;
    left: 330px;
    height: 30px;

  }

  .call-detail-box {
    position: relative;
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .call-delete-button {
    position: absolute;
    top: 5px;
    left: 157px;
  }

  .el-table tbody .current-row > td {
    font-weight: bold !important
  }

  .el-table tbody tr:hover > td {
    background-color: inherit !important;
    font-weight: bold !important
  }
</style>
<style>
  .call-record-carb-tab li.active {
    border: 1px solid #d1dbe5 !important;
    border-bottom-color: #fff !important;
    border-radius: 4px 4px 0 0;
    color: #20a0ff;
  }

  .call-record-carb-tab span.active {
    border: 1px solid #d1dbe5 !important;
    border-bottom-color: #fff !important;
    border-radius: 4px 4px 0 0;
    color: #20a0ff;
  }
</style>
