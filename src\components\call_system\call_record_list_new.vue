<!-- 新呼叫记录 -->
<template>
  <div id="call_record_list_new" style="height:100%;">
    <xpt-list ref="callRecordList" :orderNo="false" :btns="btns" :colData="cols" :data="tableData"
      :pageLength="search.page_size" :pageTotal="totalPage" :searchPage="search.page_name" @radio-change='radioChange'
      @search-click="searchClick" @page-size-change="pageSizeChange" @current-page-change="pageChange">
      <template slot='btns'>
        <span>开始时间：</span>
        <el-date-picker v-model="defaultDate" size="mini" style="width:300px;" :clearable="false" placeholder="选择开始时间范围"
          type="datetimerange">
        </el-date-picker>
      </template>
    </xpt-list>
  </div>
</template>

<script>
import { apiUrl, makeUrl } from './base.js';
import Fn from '@common/Fn.js';
import moment from 'moment'
import exportFn from '@common/export.js'

export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      btns: [
        {
          type: "success",
          txt: "刷新",
          loading: false,
          disabled: false,
          click() {
            self.getList();
          }
        },
        {
          type: "primary",
          txt: "导出",
          loading: false,
          disabled: false,
          click() {
            self.exportExcel();
          }
        },
        {
          isBtnGroup: true,
          btnGroupList: [
            {
              type: 'success',
              txt: '显示号码',
              click() {
                self.switchPhoneNumberShow(true)
              },
              isDisabled: self.switchPhoneNumberDisabled
            }, {
              type: 'warning',
              txt: '隐藏号码',
              click() {
                self.switchPhoneNumberShow(false)
              },
              isDisabled: self.switchPhoneNumberDisabled
            }
          ]
        }
      ],
      cols: [
        {
          label: "呼叫号码",
          prop: "calledNumber",
          format: "hidePhoneNumber",
          width: 120,
        },
        {
          label: "呼叫结果",
          prop: "status",
        },
        {
          label: "呼叫开始时间",
          prop: "startTime",
          width: 140,
        },
        {
          label: "呼叫结束时间",
          prop: "endTime",
          width: 140,
        },
        {
          label: "振铃时长(秒)",
          prop: "ringSeconds"
        },
        {
          label: "通话时长(秒)",
          prop: "duration"
        },
        {
          label: "挂机方",
          prop: "disconnection"
        },
        {
          label: '坐席工号',
          prop: 'callingId',
        },
        {
          label: '坐席昵称',
          prop: 'staffName',
        },
        {
          label: "操作",
          prop: "ccRecord",
          redirectClick(row) {
            if (row.ccRecord) {
              window.open(row.ccRecord);
            }
          },
          formatter: val => {
            if (val) return "录音";
          }
        },
      ],
      tableData: [],
      totalPage: 0,
      defaultDate: [],
      search: {
        page_no: 1,
        page_size: self.pageSize,
        page_name: "cloud_call_stat_new",
        where: []
      },
      select: {}, //单选

      //查看隐藏号码
      switchPhoneNumberDisabled: false,
      showPhoneNumber: false
    };
  },
  watch: {},
  methods: {
    // 获取呼叫记录列表
    getList(resolve) {
      this.btns[0].loading = true;

      let params = {
        startTime: moment(this.defaultDate[0]).format('YYYY-MM-DD 00:00:00'),
        endTime: moment(this.defaultDate[1]).format('YYYY-MM-DD 23:59:59'),
        page: {
          length: this.search.page_size,
          pageNo: this.search.page_no
        },
        page_name: this.search.page_name,
        where: this.search.where
      };
      this.ajax.postStream(
        "/callcenter-web/callStat/new/page.do", params,
        res => {
          if (res && res.body.result) {
            this.tableData = res.body.content.list;
            this.totalPage = res.body.content.count;
          } else {
            this.$message.error(res.body.msg);
          }
          resolve && resolve();
          this.btns[0].loading = false;
          this.switchPhoneNumberShow(false);//更新重置地址隐藏
        },
        err => {
          resolve && resolve();
          this.$message.error(err);
          this.btns[0].loading = false;
        }
      )
    },
    exportExcel() {
      this.$confirm("导出确认：单次导出数量限制10W条，请自行确认当前页面查询总数", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          startTime: moment(this.defaultDate[0]).format('YYYY-MM-DD 00:00:00'),
          endTime: moment(this.defaultDate[1]).format('YYYY-MM-DD 23:59:59'),
          page: {
            length: this.pageSize,
            pageNo: this.search.page_no
          },
          page_name: this.search.page_name,
          where: this.search.where
        };
        this.btns[1].loading = true;
        exportFn.exportSync(params,'/callcenter-web/callStat/new/export.do', "application/vnd.ms-excel","Call_system").then(() => {
          this.btns[1].loading = false;
        }).catch((err) => {
          this.$message.error(err || '导出失败，请稍候再试')
          this.btns[1].loading = false;
        })
      })
    },
    // 搜索
    searchClick(obj, resolve) {
      this.search.where = obj;
      this.getList(resolve);
    },
    // 当前条数
    pageSizeChange(ps) {
      this.search.page_size = ps;
      this.getList();
    },
    // 当前页数
    pageChange(no) {
      this.search.page_no = no;
      this.getList();
    },
    // 单选
    radioChange(obj) {
      this.select = obj;
    },
    async switchPhoneNumberShow(type) {
      if (type && this.showPhoneNumber) {
        this.$message.warning('已显示全部号码')
        return
      }
      this.switchPhoneNumberDisabled = true
      let currentCol = this.cols.map(item => {
        if (item.prop == 'calledNumber') {
          type ? item.format = '' : item.format = 'hidePhoneNumber'
        }
        return item
      })
      type ? await this.saveUserSensitiveLookLog() : ''
      this.cols = currentCol
      this.showPhoneNumber = type
      this.switchPhoneNumberDisabled = false
    },
    saveUserSensitiveLookLog() {
      let self = this
      let params = {}
      params.operatePageName = this.activeTabName;
      params.operateType = 'LOOK';
      params.lookContent = '';
      params.number = '';
      return new Promise((resolve, reject) => {
        self.ajax.postStream('/user-web/api/saveUserSensitiveLookLog', params, res => {
          if (res.body.result) {
            resolve()
          } else {
            self.$message.error(res.body.msg)
            reject()
          }
        }, err => {
          self.$message.error(err)
          reject()
        })
      })
    },
  },
  mounted() {
    let afterDate = moment().format('YYYY-MM-DD 23:59:59');
    let threeMonthAgo = moment().subtract(3, 'months').format('YYYY-MM-DD 00:00:00')
    console.log('threeMonthAgo', threeMonthAgo, afterDate)
    this.defaultDate = [threeMonthAgo, afterDate]
    this.getList();
  },
  destroyed() {
  }
};
</script>

<style></style>
