<template>
  <div class="xpt-flex">
    <xpt-headbar class="header-bar">
      <div slot='left'>
        <el-button size='mini' type="primary" @click='showWork'
                   :disabled="job.isApproved==1 || job.isInvalid==1">关联订单
        </el-button>
        <el-button size='mini' type="primary" @click='choiceCallRecordGetCallNum' v-if="params.fromBlankJob" :disabled="associatedCallRecordBtnDisabled">关联呼叫记录</el-button>
        <el-button size='mini' type="primary" @click='callOutDialog'
                   :disabled="btns.bohao.disable || job['isApproved'] == 1 || job['isInvalid'] == 1">电话拨号
        </el-button>
        <!-- <el-button size='mini' type="primary" @click='satisfyCheck' :disabled="btns.manyidu.disable">满意度调查</el-button> -->
        <el-button size='mini' type="danger" @click='callOutDialog' >二次拨号</el-button>
        <el-button size='mini' type="danger" @click='satisfyCheck' :disabled="btns.guaduan.disable">挂断电话</el-button>
        <el-button size='mini' type="primary" @click="beforeSubmit" :loading="btns.baocun.loading"
                   :disabled="btns.baocun.disable || job['isApproved'] == 1 || job['isInvalid'] == 1">保存
        </el-button>
        <el-button size="mini" type="primary" @click="showProblemHandler">显示处理人</el-button>
        <el-button size='mini' type="primary" @click="closeTab" :disabled="btns.guanbi.disable">关闭</el-button>
      </div>
    </xpt-headbar>
    <div class="search-input-box" v-loading="loading">
      <call-system-detail-search ref="detailForm" :outerJob="job" @update:outerJob="getInfo"
                                 @defaultCalloutNumberChanged="defaultCalloutNumberChanged"
      ></call-system-detail-search>

    </div>
    <div class="xpt-flex__bottom">
      <select-tab-two class="call-record-carb-tab" :currentComponent="currentComponent" :tabsTwo="tabsTwo"
                      @click.native="tabClick"
                      @changeComponent="changeComponent"></select-tab-two>

      <!-- 标记按钮 -->
      <div class="mark-button">
        <el-popover ref="popoverSign1" width="225" trigger="click">
          <div class="sign-choose">
            <div class="sign-item1" @click="mark1('NEED_FOLOW')">需要跟进</div>
            <div class="sign-item2" @click="mark1('PROCESS_TODAY')">当天处理</div>
            <div class="sign-item3" @click="mark1('URGENCY')">紧急处理</div>
            <div class="sign-item4" @click="mark1('BEFORE_NOON')">上午12点前</div>
            <div class="sign-item5" @click="mark1('')">清除颜色</div>
          </div>
        </el-popover>
        <span style="position:relative;left:100px">
        <el-button size="mini" type="warning" v-popover:popoverSign1>标记</el-button>
        </span>
      </div>

      <keep-alive>
        <component :is="currentComponent?.default || currentComponent" id="textareaData" class="my-height" :job="job" ref="tabComponent"
                   @updateContent="updateContent"></component>
      </keep-alive>
    </div>
    <phone-dial :phoneSuggest="phoneSuggest" :phone="phone" ref="phoneDial" v-if="showDial" @makeCall="makeCall"
                @close="showDial=false" :canChange="false"/>
  </div>
</template>

<script>
  import baseUrl, {makeUrl, apiUrl, EventBus} from './base.js';
  import callBtn from '@/components/call_system/callBtn';
  import callSystemDetailSearch from './call_system_detail_search';
  import contentEntry from './contentEntry';
  import callHistory from './callHistory';
  import selectTabTwo from './selectTabTwo';
  import PhoneDial from './phoneDial';
  import PhoneBar from './phoneBar';
  import Fn from '@/common/Fn.js';
  import moment from 'moment'

  export default {
    props: ['params'],
    // 从隔很多层的父级组件获取弹窗的状态值
    inject: [ 'diaConfig' ],
    components: {
      PhoneDial,
      callBtn,
      callSystemDetailSearch,
      contentEntry,
      callHistory,
      selectTabTwo,
    },
    data() {
      return {
        shouldSave: false,
        loading:false,
        job: {},
        showDial: false,
        currentComponent: 'contentEntry',
        contentEntry: '',
        phoneSuggest: [],
        phone: '',
        formHasUnSavedChange: false,
        lastSaveData: null,
        processId: null,
        tabsTwo: [
          {
            code: 'contentEntry',
            name: '内容录入',
          },
          {
            code: 'callHistory',
            name: '呼叫历史',
          },
        ],
        btns: {
          bohao: {disable: true},
          manyidu: {disable: true},
          guaduan: {disable: true},
          guanbi: {disable: true},
          baocun: {disable: false,loading:false},

        },
        callParamsList: {}, //保存接受到的电话信息
        associatedCallRecordBtnDisabled: false, //关联呼叫记录按钮
        firstSaveByBlankJob:false, //新建工单形式打开详情，进行的第一次保存
      };
    },
    mounted() {
      this.initEvent();

      // 当打开操作是呼叫管理页面新建工单按钮触发的时候，不进行原本的初始化操作
      if(this.params.fromBlankJob){
        // 手工模拟加载数据
        this.simulateLoadJobData();
      } else{
        if (this.params.fromCalling) {
          this.$root.eventHandle.$emit('setTabClosable', '电话工单-' + this.params.jobId, false);
        }
        this.$nextTick(() => {
          this.loadJobData(this.params.jobId, this.params.autoBinding);
        });
      }
    },
    methods: {
      initEvent(){
        EventBus.on(PhoneBar.getCallEndEventName,this.onCallEnd);
        EventBus.on(PhoneBar.getAgentWorkEventName,this.init);
        // 初始化完了设置状态为示忙
        EventBus.on(PhoneBar.getPhoneInitName, this.init);
      },
      // 工单标记功能
      mark1(markType) {
        let param = {jobId: this.job.jobId, markType: markType};
        this.$http.post(apiUrl.workOrder_mark, param, {emulateJSON: true})
        .then(res => {
          if (res.body && res.body.code === 0) {
            this.job.markType = markType;
            this.$message.success('成功');
            this.$refs.popoverSign1.doClose();
          } else {
            this.$message.error(res.body.msg);
          }
        }).catch(err => {
          this.$message.error(err);
        })
      },
      tabClick() {
        this.$nextTick(() => {
          if (this.currentComponent == 'callHistory') {
            this.$refs.tabComponent.getHistoryList();
          }
        });
      },
      defaultCalloutNumberChanged(newVal) {
        let result = this.phoneSuggest.filter(item => {
          return item.remark == '回拨号码';
        }).map(item => {
          item.value = newVal;
          item.label = newVal;
        });
        if (result.length == 0) {
          this.phoneSuggest.push({
            value: newVal, label: newVal, remark: '回拨号码',
          });
        }
      },
      satisfyCheck() {
        if(this.checkPopupStatus()) return;
        var subfix = window.location.host.startsWith('sale.linshimuye') ? '9999' : '9998';
        let num = '00000180080000' + subfix;
        application.oJVccBar.TransferOut(num, num);
        let isSalesPersonDisConnect=true;//客服主动挂断
        EventBus.emit('watchIsSalesPersonDisConnect', isSalesPersonDisConnect);
      },
      resetForm() {
        this.$refs.detailForm.resetForm();
      },
      phoneDisconnect() {
        if(this.checkPopupStatus()) return;
        window.application.oJVccBar.Disconnect();
        this.$nextTick(() => {
          window.application.oJVccBar.SetBusy();
        });
      },
      changeComponent(code) {
        this.currentComponent = code;
      },
      getInfo(job) {
        this.job = job;
        if (!!job.salesUserName) {
          let split = job.salesUserName.split('.');
          this.loadUserIncumbency(...split);
        }
        this.lastSaveData = this.collectData();
      },
      makeCall(phoneNo) {
        if(this.checkPopupStatus()) return;
        EventBus.emit(EventBus.call_system_make_call, phoneNo);
        this.onCallEnd();
      },
      showWork() {
        let _this = this;
        let detailInfo = this.$refs.detailForm.job;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/call_system/linked_order.vue'),
          style: 'width:800px;height:500px',
          title: '关联订单',
          params: {
            job: Object.assign({}, {receiverPhone: detailInfo.calledNumber}),
            // customer:ids,
            // 刷新分配列表
            callback: data => {
              _this.onRelevantSelected(data);
            },
          },
        });
      },
      //获取查询枚举人员名称和人员昵称field和table
      getLoadUserIncumbencyField(){
        return new Promise((resolve,reject)=>{
          this.$http.post('/user-web/api/sql/listFields', {
            page:"cloud_user_person"
          }).then(res=>{
            if(res.data.result){
              resolve(res.data.content)
            }else{
              reject(false)
            }
          }).catch(()=>{
            reject(false)
          })
        })
      },
      async loadUserIncumbency(userName, nickName) {
        let fieldContent=await this.getLoadUserIncumbencyField();
        let personNameField=fieldContent.fields.find(item=>item.comment==='人员名称')
        let personNickField=fieldContent.fields.find(item=>item.comment==='人员昵称')
        console.log(fieldContent);
        if(!fieldContent||fieldContent.fields.length===0||!personNameField.field||!personNickField.field||!personNickField.table){
          this.$message.error("获取人员列表查询条件异常，请联系管理员")
          return
        }
        this.$http.post('/user-web/api/userPerson/getUserPersonList', {
          'page': {'length': 1, 'pageNo': 1},
          'page_name': 'cloud_user_person',
          'where': [
            {
              field: personNameField.field,
              table: personNameField.table,
              'value': userName,
              'operator': '=',
              'condition': 'AND',
              'listWhere': [],
            }, {
              field: personNickField.field,
              table: personNickField.table,
              'value': nickName,
              'operator': '=',
              'condition': 'AND',
              'listWhere': [],
            }],
        }).then(res => {
          if (res.data && !!res.data.content && res.data.content.count > 0) {
            this.$refs.detailForm.tempIncumbency = res.body.content.list[0].incumbency;
          }
        });
      },
      onRelevantSelected(data) {
        let job = this.job;
        let mapper = {
          mergeTradeNo: 'merge_trade_no',
          custId: 'cust_id',
          custName: 'customer_name',
          shopName: 'shop_name',
          salesUserName: 'salesman_name',
          receiverName: 'receiver_name',
          receiverPhone: 'receiver_mobile',
        };
        for (let key in mapper) {
          job[key] = data[mapper[key]];
        }
        if (this.currentComponent == 'callHistory') {
          this.$refs.tabComponent.getHistoryList();
        }
        switch (data.currView) {
          default:
          case 'workBatchorderDialog':
            job.sourceOrderType = '批次订单';
            job.sourceOrderId = data.batch_trade_id;
            job.sourceOrderNo = data.batch_trade_no;
            job.salesGroupName = data.group_name;
            job.memberName = data.bestHandlerUser;
            job.problemHandlerId = data.problemHandlerId;
            job.problemHandlerNo  = data.problemHandlerNo;
            // job.salesGroupName = data.group_name;
            // self.btns.baocun.disable = true;
            // self.oaidDecrypt('batch_trade_no',data.batch_trade_no).then(res=>{
            //   if(res.body.result&&res.body.content){
            //     self.$message.success(res.body.msg)
            //     job.receiverName=res.body.content.receiverAddressList&&res.body.content.receiverAddressList[0].name;
            //     job.receiverPhone=res.body.content.receiverAddressList&&res.body.content.receiverAddressList[0].mobile;
            //   }else{
            //     self.$message.warning(res.body.msg)
            //   }
            //   self.btns.baocun.disable = false;
            // }).catch(err=>{
            //   self.$message.error(err)
            //   self.btns.baocun.disable = false;
            // })
            break;
          case 'worksaleDialog':
            job.sourceOrderType = '销售订单';
            job.sourceOrderId = data.sys_trade_id;
            job.sourceOrderNo = data.sys_trade_no;
            job.memberName = data.bestHandlerUser;
            job.salesGroupName = data.group_name;
            job.problemHandlerId = data.problemHandlerId;
            job.problemHandlerNo  = data.problemHandlerNo;
            break;
          case 'workSaleafterDialog':
            job.sourceOrderType = '售后单';
            job.sourceOrderId = data.id;
            job.sourceOrderNo = data.after_order_no;
            break;
          case 'workreturnDialog':
            job.sourceOrderType = '回访单';
            job.sourceOrderId = data.return_list_id;
            job.sourceOrderNo = data.return_list_no;
            job.salesGroupName = data.salesman_group;
            break;
        }
        // if (!!job.salesUserName) {
        //   let split = job.salesUserName.split('.');
        //   this.loadUserIncumbency(...split);
        // }
        this.$refs.detailForm.job = this.job;
      },
      closeTab() {
        let collectData1 = this.collectData();
        if (JSON.stringify(collectData1) != JSON.stringify(this.lastSaveData)) {
          this.$message.warning('您有未保存的变更，请保存后再关闭');
        } else {
          this.$root.eventHandle.$emit('removeTab', this.params.tabName);
        }
      },
      init() {
        for (let btnsKey in this.btns) {
          this.btns[btnsKey].disable = true;
        }
        this.btns.baocun.disable = false;
        //获取座席的工作状态   0：未登录   1：忙碌  2：空闲  3：通话中   4：后续态
        const agentStatus = window.application.oJVccBar.GetAgentStatus();
        if (agentStatus == 1) {
          this.btns.bohao.disable = false;// 电话拨号
        }
        if (agentStatus == 3) {
          this.btns.manyidu.disable = false;// 满意度调查
          this.btns.guaduan.disable = false; // 挂断电话
          if ((this.job.calledNumber+'') != PhoneBar.getCustPhoneNum()) {
            // 当前通话与本详情无关
            this.btns.guanbi.disable = false;// 关闭
            this.btns.baocun.disable = true;// 不能保存
            this.shouldSave = true;
            return;
          }
        } else {
          // 不在呼叫中
          this.btns.guanbi.disable = false;// 关闭
        }
        if ((this.job.calledNumber+'') == PhoneBar.getCustPhoneNum()) {
          // 本detail 与当前通话相关 不能关闭
          this.btns.guanbi.disable = true;// 关闭
          this.$root.eventHandle.$emit('setTabClosable', '电话工单-' + this.params.jobId, false);
        }
        if (this.shouldSave) {
          this.btns.guanbi.disable = true;
        }
      },
      callOutDialog(key) {
        //呼出电话  应呼出订单人的电话或收货人的电话
        let detailInfo = this.$refs.detailForm.job;

        this.phoneSuggest = [];
        this.phoneSuggest.push({value: detailInfo.calledNumber, label: detailInfo.calledNumber, remark: '来电号码'});
        if (detailInfo.defaultCalloutNumber) {
          this.phoneSuggest.push(
            {value: detailInfo.defaultCalloutNumber, label: detailInfo.defaultCalloutNumber, remark: '回拨号码'});
        }
        if (detailInfo.receiverPhone) {
          this.phoneSuggest.push({value: detailInfo.receiverPhone, label: detailInfo.receiverPhone, remark: '收货号码'});
        }
        this.phone = detailInfo.defaultCalloutNumber || detailInfo.calledNumber || detailInfo.receiverPhone;

        this.showDial = true;
      },
      updateContent(data) {
        //更新备注内容
        this.contentEntry = data;
      },
      wrapTime(timeStr) {
        if (!timeStr) return '';
        return timeStr.substr(0, 4) + '-' + timeStr.substr(4, 2) + '-' + timeStr.substr(6, 2) + ' ' +
          timeStr.substr(8, 2) + ':' + timeStr.substr(10, 2) + ':' + timeStr.substr(12, 2);
      },
      collectData() {
        const callParams = this.callParamsList;
        let calledType = '创建';
        if (callParams.callid) {
          calledType = callParams.servicedirect == 2 ? '呼出' : '呼入';
        }
        const workOrderProcessLog = {
          processId: this.processId,
          calledId: callParams.callid ? callParams.callid : '',
          calledType: calledType,
          remark: this.contentEntry,
          recordStartTime: callParams.callid ? this.wrapTime(callParams.bgntime) : '',
          recordEndTime: callParams.callid ? this.wrapTime(callParams.endtime) : '',
          recordFile: callParams.callid && callParams.bgntime ? (callParams.directory + '/' + callParams.filename) : '',
        };
        let params = Object.assign({}, this.job, {workOrderProcessLog}, {
          preOrderTime: this.job.preHandlerTime ?
            Fn.dateFormat(this.job.preHandlerTime, 'yyyy-MM-dd hh:mm:ss') : '',
            preHandlerTime: this.job.preHandlerTime ?
            Fn.dateFormat(this.job.preHandlerTime, 'yyyy-MM-dd hh:mm:ss') : '',
            isFirst:false,
        });
        return params;
      },
      checkIfJobChange(newJobId, oldJobId, newJobObj) {
        if (newJobId != oldJobId) {
          this.job.jobId = newJobId
        }
      },
      showProblemHandler(){
        let params = {
          isAlert:true,
          groupName:this.job.salesGroupName,
              
        };
        // params.callback = (d)=>{
        //   var data = d.data;
        //   if(!data) return;
        //   this.form.logistics_company_id = data.supplier_id;
        //   this.form.logistics_company_name = data.name;
        // }
        this.$root.eventHandle.$emit('alert',{
              params:params,
              component:()=>import('@components/call_system/problemHamderList'),
              style:'width:80%;height:80%',
              title:'查看店铺问题处理人'
          });
      },
      // 保存之前对操作来源进行判断，新建工单按钮触发 / 新建工单代码触发
      beforeSubmit(){
        if(this.params.fromBlankJob){
          // if(!this.job.calledNumber){
          //   this.$message.error('尚未关联呼叫记录!');
          // }else 
          if(!this.firstSaveByBlankJob){
            // 先触发新建工单方法，然后拿到工单ID在进行工单保存操作
            // 第一次进行保存的时候走这个方法获取到工单id，之后仍然用老的方法
            this.getJobIdAndSaveData();
          }else{
            this.submit();
          }
        }else{
          // 新建工单代码触发
          this.submit();
        }
      },
      submit() {
        this.btns.baocun.loading = true;
        //保存
        this.$refs.detailForm.validate((valid) => {
          if (valid) {
            if (!this.contentEntry.replace(/^[\s\uFEFF]+|[\s\uFEFF]+$/g, '')) {
              this.$message.error('工单内容不能为空！');
              this.btns.baocun.loading = false;
              return;
            }

            // 中文 + 数字 + 字母 + 常见字符（不在这个范围则认为是特殊符号）            
			var regRule = /[^\u4E00-\u9FA5|\d|\a-zA-Z||\r\n\s`~!@#$%^&*()_\-+=<>?:"|,.\/;'\[\]·！￥……（）——《》？：“”【】、；‘’，。、…—/{}[\]]/g;
            var checkValue = this.contentEntry.replace(regRule, "");
            this.contentEntry = checkValue;
            // 修改textarea 的内容
            $('#textareaData').children('div').children('textarea').val(checkValue);
            // var checkValue = this.contentEntry.replace(regRule, "");
            // var strList =  this.contentEntry.split("");
            // console.log("strList:"+strList);
            // var strArray = "";
            // var isNum = 0;
            // for(var i  in strList){
            //   var indexNum =	checkValue.indexOf(strList[i]);
            //   if( indexNum < 0 )
            //     strArray+=strList[i];
            //     isNum++;
            //     if(isNum == 70){
            //       strArray+="\r\n";
            //       isNum = 0;
            //     }                
            // }
            // if (strArray!="" && strArray.length > 0) {
            //   this.$message.warning('工单内容包含特殊字符，请修正:'+strArray);
            //   this.btns.baocun.loading = false;
            //   return;
            // }

            // 当打开操作是呼叫管理页面新建工单按钮触发的时候，使用不同的收集信息方法
            let callParams = {};
            if (this.params.fromBlankJob){
              callParams = this.collectDataByBlankJob();
            } else {
              callParams = this.collectData();
            }

            callParams.salesIncumbency = this.$refs.detailForm.tempIncumbency;
            this.$http.post(apiUrl.workOrder_update, callParams).then(res => {
              if (res.data && res.data.code === 0) {
                this.associatedCallRecordBtnDisabled = true; //保存成功之后锁定关联呼叫记录按钮
                this.processId = res.data.content.workOrderProcessLog.processId;
                this.$message.success('保存成功');
                this.shouldSave = false;
                this.formHasUnSavedChange = false;
                this.init();
                this.lastSaveData = this.collectData();
                this.$nextTick(()=>{
                  EventBus.emit(EventBus.order_saved, callParams.jobId);
                })
                this.checkIfJobChange(res.data.content.jobId, callParams.jobId, res.data.content);
              } else {
                // var errorMsg = res.data.msg;
                // console.log("res.data.msg:"+res.data.msg);
				        this.$message.error(res.data.msg+" 请关联呼叫记录后，再次尝试保存");
                // this.$message.error(res.data.msg+" 请重新录入保存");
                // if("系统繁忙, 请稍后再试" == errorMsg || errorMsg.indexOf("系统繁忙") || errorMsg.indexOf("请稍后再试")){
                //   this.$message.warning("工单内容包含特殊字符，请修正后保存");
                // }else{
                //   this.$message.error(res.data.msg);
                // }
              }
            }).catch(error => {
              console.error(error);
              this.$message.error('保存失败');
            }).finally(() => {
              this.loading = false;
              this.btns.baocun.loading = false;
            });
          } else {
            this.$message.error('必填字段不能为空！');
            this.btns.baocun.loading = false;
          }
        });
      },
      onCallEnd
        (
          callid, serialid, servicedirect, userno, bgntime, endtime, agentalerttime, useralerttime, filename,
          directory,
          disconnecttype, userparam, taskid, servername, networkinfo) {
          if(this.diaConfig.dialogVisible) return;
          this.init();
          if (PhoneBar.getCustPhoneNum() === this.job.calledNumber) {
            this.shouldSave = true;
          }
          this.callParamsList = {
            callid,
            serialid,
            servicedirect,
            userno,
            bgntime,
            endtime,
            agentalerttime,
            useralerttime,
            filename,
            directory,
            disconnecttype,
            userparam,
            taskid,
            servername,
            networkinfo,
          };
          if (this.currentComponent == 'callHistory') {
            this.$refs.tabComponent.getHistoryList();
          }
      },
      loadJobData(jobId, autoBinding = false){
        this.$http.get(makeUrl(apiUrl.workOrder_get, {jobId: jobId, isAutoBindOrder: autoBinding})).then((res) => {
          for (let k in res.data.content.workOrder) {
            this.$set(this.job, k, res.data.content.workOrder[k]);
          }
            this.$set(this.job, "memberName", res.data.content.problemHandler.memberName);
            this.$set(this.job, "problemHandlerId", '');
            this.$set(this.job, "problemHandlerNo", '');
        }).catch(err => {
          this.$message.error(err);
        }).finally(() => {
          this.loading = false;
          this.init();
        });
      },
      // 检测呼出弹窗状态
      checkPopupStatus(){
        if(this.diaConfig.dialogVisible) {
          this.$message.error('请先关闭外呼弹窗')
          return true
        };
      },
      // 手工模拟加载数据
      simulateLoadJobData(){
        // 仿照 loadJobData 方法手动生成响应式数据
        let responsiveData = {
          approvedTime: null,
          approvedUserId: null,
          approvedUserName: null,
          areaCode: null,
          calledNumber: null,
          callingType: null,
          createTime: null,
          creatorId: null,
          creatorName: null,
          custId: null,
          custName: null,
          defaultCalloutNumber: null,
          endDate: null,
          groupId: null,
          isApproved: 0,
          isInvalid: 0,
          jobId: null,
          jobNumber: null,
          jobType: null,
          markRemark: null,
          markType: null,
          markeTime: null,
          markedUserName: null,
          mergeTradeId: null,
          mergeTradeNo: null,
          missId: null,
          modifierId: null,
          modifierName: null,
          modifyTime: null,
          operatedGroupId: null,
          operatedGroupName: null,
          operatedUserId: null,
          operatedUserName: null,
          orderOwnerType: null,
          receiverName: null,
          receiverPhone: null,
          salesGroupId: null,
          salesGroupName: null,
          salesIncumbency: null,
          salesUserId: null,
          salesUserName: null,
          shopId: null,
          shopName: null,
          sourceOrderId: null,
          sourceOrderNo: null,
          sourceOrderType: null,
          staffNo: null,
          startDate: null,
          workOrderProcessLog: null
        };
        for (let k in responsiveData) {
          this.$set(this.job, k, responsiveData[k]);
        }
      },
      // 弹出呼叫记录弹窗，选中呼叫记录返回手机号
      choiceCallRecordGetCallNum(){
        this.$root.eventHandle.$emit('alert',{
          params: {
            fullName:this.getUserInfo('fullName'),
            fromAlert:true,
            callback: (item) => {
              this.associatedCallRecord(item);
            },
          },
          component:()=>import('@components/call_system/call_record_list'),
          style:'width:800px;height:560px',
          title:'选择呼叫记录'
        });
      },
      // 将获取的信息保存
      associatedCallRecord(item){
        this.callParamsList = {
          callid:item.sourceCallId, //呼叫标识
          serialid:null, //座席标识
          servicedirect:null, //呼叫类型
          userno:item.phoneNumber, //用户电话号码
          bgntime:item.recordStartTime, //呼叫开始时间
          endtime:item.recordEndTime, //呼叫结束时间
          agentalerttime:'', //座席振铃时长
          useralerttime:null, //用户振铃时长
          filename:item.fileName, //录音文件名
          directory:item.directory, //录音保存目录
          disconnecttype:null, //挂机方
          userparam:null, //可选参数，透明参数
          taskid:null, //可选参数，外呼任务号或者人工服务号号，string
          servername:null, //录音文件所在服务器标识，string
          networkinfo:null, //来自哪个网关：string
        };
        // 同时更新Job里面的手机号
        this.job.calledNumber = item.phoneNumber;
      },
      // 先拿到工单的ID，再进行工单的保存操作
      getJobIdAndSaveData(){
        this.btns.baocun.loading = true;
        this.$http.get(makeUrl(apiUrl.workOrder_getNonClose, {phoneNumber: this.job.calledNumber}))
          .then(res => {
            if(res.body.code == '0'){
              // 获取到新建工单后的ID以及一些操作数据
              this.consolidateData(res.body.content)
              this.firstSaveByBlankJob = true;
            }
            else {
              this.$message.success(`工单保存失败 ${res.body.msg}`);
              this.btns.baocun.loading = false;
            }
          });
      },
      // 将 showNoCloseJob 获取的数据与当前页面已经填写的数据进行对比，按规则合并
      consolidateData(urlJobData){
        let mergeData = JSON.parse(JSON.stringify(this.job));
        // 当前页面的Job数据 与 接口返回的urlData 进行合并；以当前页面为主
        for (let key in urlJobData) {
          if(urlJobData[key] && !mergeData[key]){
            mergeData[key] = urlJobData[key];
          }
        }
        this.job = mergeData;
        // 走回标准流程进行工单保存
        this.submit();
      },
      // 当为空白工单设置时，使用不同的收集信息方法
      collectDataByBlankJob(){
        const callParams = this.callParamsList;
        let calledType = '创建';
        // if (callParams.callid) {
        //   calledType = callParams.servicedirect == 2 ? '呼出' : '呼入';
        // }
        const workOrderProcessLog = {
          
          processId: this.processId,
          calledId: callParams.callid ? callParams.callid : '',
          calledType: calledType,
          remark: this.contentEntry,
          recordStartTime: callParams.callid ? callParams.bgntime: '', //修改
          recordEndTime: callParams.callid ? callParams.endtime: '', //修改
          recordFile: callParams.callid && callParams.bgntime ? (callParams.directory + '/' + callParams.filename) : '',
        };
        let params = Object.assign({}, this.job, {workOrderProcessLog}, {
          preOrderTime: this.job.preHandlerTime ?
            Fn.dateFormat(this.job.preHandlerTime, 'yyyy-MM-dd hh:mm:ss') : '',
            preHandlerTime: this.job.preHandlerTime ?
            Fn.dateFormat(this.job.preHandlerTime, 'yyyy-MM-dd hh:mm:ss') : '',
            isFirst:true,
        });
        return params;
      },
    },
    beforeDestroy() {
        EventBus.off(PhoneBar.getCallEndEventName,this.onCallEnd);
        EventBus.off(PhoneBar.getAgentWorkEventName,this.init);
        // 初始化完了设置状态为示忙
        EventBus.off(PhoneBar.getPhoneInitName, this.init);
      PhoneBar.detachAll(this);
    },
  };
</script>

<style scoped lang="stylus">
  .header-bar {
    height: 25px;
    line-height: 25px;
    margin-bottom: 0;
  }

  .my-height {
    height: 100%;
  }

  .search-input-box {
    position: relative;

    .bigCover1 {
      position absolute;
      left 0;
      top 0;
      right 0;
      bottom 0;
      background rgba(0, 0, 0, 0.4);
    }
  }

  .xpt-flex__bottom {
    position: relative;

    .bigCover2 {
      position absolute;
      background rgba(0, 0, 0, 0.4);
      left 0;
      top 0;
      right 0;
      bottom 0;

      .cover-text {
        color #ffffff;
        position absolute;
        top 20%;
        left 50%;
        transform translateX(-50%)
      }
    }
  }

</style>
<style>
  .call-record-carb-tab span.active {
    border: 1px solid #d1dbe5 !important;
    border-bottom-color: #fff !important;
    border-radius: 4px 4px 0 0;
    color: #20a0ff;
  }
  .sign-choose div {
    width: 200px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    cursor: pointer;

  }

  .sign-choose div:hover {
    background: #dfe6ec;
  }

  .sign-item1 {
    background: rgb(47, 209, 169) !important;

  }

  .sign-item2 {
    background: rgb(255, 178, 192) !important;
  }

  .sign-item3 {
    background: rgb(255, 0, 0) !important;
  }

  .sign-item4 {
    background: rgb(0, 143, 0) !important;
  }

  .sign-item5 {
    background: rgb(253, 253, 253)
  }

  .mark-button {
    position: absolute;
    top: 4px;
    left: 50px;
    height: 30px;

  }
</style>
