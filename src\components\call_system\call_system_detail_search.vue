<!-- 搜索 -->
<template>
  <div class="search-work" >
    <el-form class="detail-form" ref="form" :inline="true" :model="job" :rules="getRules()" label-width="90px">
      <el-form-item label="用户号码" prop="calledNumber">
        <!-- <el-input class="search-input" :disabled="true" size="mini" v-model="job.calledNumber"></el-input> -->
        <xpt-eye-switch class="search-input" v-model="job.calledNumber" :disabled="true"></xpt-eye-switch>
      </el-form-item>
      <el-form-item label="地区" prop="areaCode">
        <el-input class="search-input" :disabled="true" size="mini" v-model="job.areaCode"></el-input>
      </el-form-item>
      <el-form-item label="所选服务" prop="jobType">
        <el-input class="search-input" :disabled="true" size="mini" v-model="job.jobType"></el-input>
      </el-form-item>
      <el-form-item label="客服状态" prop="salesIncumbency">
        <el-input class="search-input" :disabled="true" size="mini" v-model="_salesIncumbency"></el-input>
      </el-form-item>
      <el-form-item label="合并单号" prop="mergeTradeNo">
        <el-input class="search-input" :disabled="true" size="mini" v-model="job.mergeTradeNo"></el-input>
      </el-form-item>
      <el-form-item label="买家昵称" prop="custName">
        <el-input class="search-input" size="mini" v-model="job.custName"
                  :disabled="job.isApproved==1 || job.isInvalid==1"></el-input>
      </el-form-item>
      <el-form-item label="收货人" prop="receiverName">
        <el-input class="search-input" :disabled="true" size="mini" v-model="job.receiverName"></el-input>
      </el-form-item>
      <el-form-item label="售后锁定人" prop="saleLockerName">
        <el-input class="search-input" :disabled="true" size="mini" v-model="job.saleLockerName"></el-input>
      </el-form-item>

      <el-form-item label="店铺" prop="shopName">
        <el-input class="search-input" :disabled="true" size="mini" v-model="job.shopName"></el-input>
      </el-form-item>

      <el-form-item label="客服" prop="salesUserName">
        <el-input class="search-input" :disabled="true" size="mini" v-model="job.salesUserName" icon="search"
                  readonly></el-input>
      </el-form-item>

      <el-form-item label="收货手机" prop="receiverPhone">
        <!-- <el-input class="search-input" :disabled="true" size="mini" v-model="job.receiverPhone"></el-input> -->
        <xpt-eye-switch class="search-input" v-model="job.receiverPhone" :disabled="true"></xpt-eye-switch>
      </el-form-item>
      <!-- <el-form-item label="下次处理时间" prop="preOrderTime">
        <el-date-picker
          v-model="job.preOrderTime"
          type="date"
          placeholder="选择日期" class="search-input" size="mini" :disabled="job.isApproved==1 || job.isInvalid==1">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="回拨号码" prop="defaultCalloutNumber">
        <el-input class="search-input" size="mini" v-model="job.defaultCalloutNumber"
                  :disabled="job.isApproved==1 || job.isInvalid==1"></el-input>
      </el-form-item>
      <el-form-item label="来电类型" prop="callingType">
        <el-select v-model="job.callingType" placeholder="请选择" size="mini" class="search-input"
                   :disabled="job.isApproved==1 || job.isInvalid==1">
          <el-option
            v-for="item in callingType"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="归属" prop="orderOwnerType">
        <el-select v-model="job.orderOwnerType" placeholder="请选择" size="mini" class="search-input"
                   :disabled="job.isApproved==1 || job.isInvalid==1">
          <el-option
            v-for="item in orderOwnerType"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>


      <el-form-item label="问题处理人" prop="memberName">
        <el-input class="search-input" size="mini" v-model="job.memberName" icon="search" disabled ></el-input>
        <!-- <el-button size="mini" type="primary">显示处理人</el-button> -->
      </el-form-item>

      <el-form-item label="客服处理人" prop="sendHandlerList">
        <xpt-input class="search-input" size="mini" v-model="job.sendHandlerList" icon="search"
                  :on-icon-click="searchOperatedUserCondition"></xpt-input>
      </el-form-item>
      <el-form-item label="客服分组" prop="salesGroupName">
        <el-input class="search-input" size="mini" v-model="job.salesGroupName" :disabled="true"></el-input>
      </el-form-item>
      <!-- <el-form-item label="处理人" prop="operatedUserName">
        <el-input class="search-input" size="mini" v-model="job.operatedUserName" icon="search"
                  :on-icon-click="searchOperatedUserCondition"
                  :disabled="job.isApproved==1 || job.isInvalid==1"></el-input>
      </el-form-item> -->
      <el-form-item label="问题处理时间" prop="preHandlerTime">
        <el-date-picker
          :popper-class="$style.preHandlerTimePicker"
          size="mini"
          v-model="job.preHandlerTime"
          format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          placeholder="选择时间"
          @change="preHandleTimeChange">
        </el-date-picker>
      </el-form-item>
    </el-form>
    <!--    <div class="form-action">
          <el-button type='primary' size="mini" @click="resetForm('ruleForm')">重置</el-button>
        </div>-->
  </div>
</template>

<script>
  import {makeUrl, apiUrl} from './base.js';
  import validate from '@common/validate.js';

  export default {
    props: ['outerJob'],
    data() {
      var _this = this;
      return {
        tempIncumbency: '',
        job: {
          receiverPhone: '',
          areaCode: '',
          jobType: '',
          salesIncumbency: '',
          mergeTradeNo: '',
          custName: '',
          receiverName: '',
          saleLockerName: '',
          shopName: '',
          salesUserName: '',
          preOrderTime: '',
          callingType: '',
          orderOwnerType: '',
          problemHandlerId: '',
          problemHandlerNo: '',
          memberName: '',
          sendHandlerList: '',
          // operatedUserName: '',
          salesGroupName:"",
          defaultCalloutNumber: '',
          calledNumber: '',
          isApproved: null,
          isInvalid: null,
          incumbency: '',
          preHandlerTime: '',
        },
        resetJob: {
          mergeTradeNo: '',
          custName: '',
          receiverName: '',
          saleLockerName: '',
          shopName: '',
          salesUserName: '',
          preOrderTime: '',
          callingType: '',
          orderOwnerType: '',
          problemHandlerId: '',
          problemHandlerNo: '',
          memberName: '',
          salesGroupName: '',
          sendHandlerList: '',
          receiverPhone: '',
          incumbency: '',
          preHandlerTime: '',
        },
        callingType: [
          {
            value: '购买咨询',
            label: '购买咨询',
          }, {
            value: '发货截货',
            label: '发货截货',
          },  {
            value: '物流查询',
            label: '物流查询',
          }, {
            value: '售后问题',
            label: '售后问题',
          }, {
            value: '其他',
            label: '其他',
          }, {
            value: '预约到店',
            label: '预约到店',
          }, {
            value: '虚假签收',
            label: '虚假签收',
          }, {
            value: '话务投诉',
            label: '话务投诉',
          }],
        orderOwnerType: [
          {value: '咨询', label: '咨询'},
          {value: '售前', label: '售前'},
          {value: '售中', label: '售中'},
          {value: '售后', label: '售后'},
          {value: '展厅', label: '展厅'},
          {value: '三包', label: '三包'},
          {value: '经销', label: '经销'},
          {value: '话务', label: '话务'},
          {value: '线上和线下', label: '线上和线下'},
          {value: '承包门店', label: '承包门店'},
          {value: '外包售前', label: '外包售前'},
        ],
        rules: {
          /*custName:  validate.isNotBlank({

              self:_this,
              msg:'买家昵称不能为空',
              trigger: 'change'
          }),*/
        },
        rules2:{
          preHandlerTime:[{ type: 'date', required: true, message: '问题处理时间不能为空', trigger: 'change' }]
        },
        nowUserData:'', // 获取当前用户的相关信息
      };
    },
    computed: {
      _salesIncumbency() {
        switch (this.tempIncumbency) {
          default:
            return '';
          case 'WORKING_STAFF':
            return '在职';
          case 'JOB_TRANSFER':
            return '转岗';
          case 'LEAVE_OFFICE':
            return '离职';
        }
      },
    },
    methods: {
      // 获取当前用户的分组信息
      getEmployeeData() {
        let params = {
          pageNo: 1,
          pageSize: 20,
          staffNo: this.getEmployeeInfo('employeeNumber'),
          staffName: "",
          groupName: ""
        };
        this.$http
          .get(apiUrl.personGroup_list, { params })
          .then(res => {
            if (res.data.code == 0 && res.data.content.length > 0) {
              this.nowUserData = res.data.content[0];
            } else if (res.data.code != 0) {
              this.$message.error(res.data.msg || "");
            }
          })
          .catch(err => {
            this.$message.error(`${err.status}` || "");
          })
          .finally(() => {
          });
      },
      // 设置校验
      getRules() {
        if(this.nowUserData && (this.nowUserData.groupName == '接待')){
          return {...this.rules,...this.rules2};
        }else{
          return this.rules2;
        }
      },
      resetForm() {
        this.job = Object.assign({}, this.job, this.resetJob);
      },
      validate(cb) {
        return this.$refs.form.validate(cb);
      },
      searchSalesUserCondition() {
        var _this = this;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/system_management/list.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            status: 1,//生效人员
            isEnable: 1,//生效时间
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;
              _this.job.salesUserName = data.fullName;
              _this.job.salesUserId = data.employeeNumber;
              _this.job.salesIncumbency = data.incumbency;
            },
          },
        });
      },
      searchOperatedUserCondition() {
        if (this.job.isApproved == 1 || this.job.isInvalid == 1) {
          return;
        }
        var _this = this;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/call_system/customerHandleList.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            // status: 1,//生效人员
            isEnable: 1,//生效时间
            showAllPerson: true,
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;
              let arr = [];
              data.forEach(item => {
                // console.log(item);
                arr.push(item.employeeNumber);
              });
            _this.$set(_this.job, "sendHandlerList", arr.join(","));


              //operatedUserId来源由工号 employeeNumber 改成 id
              // _this.job.operatedUserId = data.id;
            },
          },
        });
      },
      searchCreatorCondition() {
        var _this = this;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/system_management/list.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            // status: 1,//生效人员
            isEnable: 1,//生效时间
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;
              _this.formMap.creatorName = data.fullName;
              _this.formMap.creatorId = data.employeeNumber;

            },
          },
        });
      },
      preHandleTimeChange(val){
        // 1. 获得当前时间
        let date = new Date(val)
        // 2. 分秒归零
        date.setMinutes(0)
        date.setSeconds(0)
        this.job.preHandlerTime=new Date(date).getTime();
      }
    },
    mounted: function() {
      this.getEmployeeData();
      Object.assign({}, this.job, this.outerJob);
    },
    destroyed() {
      this.$root.offEvents('refresh_list');
    },
    watch: {
      outerJob:{
        deep:true,
        handler(newVal,oldVal){
          this.job = newVal;
        },
      },
      job: {
        handler: function(val, oldVal) {
          this.$emit('update:outerJob', val);
        },
        deep: true,
      },
      'job.defaultCalloutNumber': function(val, oldVal) {
        this.$emit('defaultCalloutNumberChanged', val);
      },
    },
  };
</script>
<style scoped>
  .search-work {
    background: #eee;
    /*display: flex;*/
    margin: 0px -20px 10px -20px;
  }

  .detail-form {
    max-width: 1300px;
    padding: 10px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }

  .search-input {
    margin-bottom: 10px;
  }
</style>
<style module>
.preHandlerTimePicker :global(.el-time-spinner__wrapper){
  width:100% !important;
}
.preHandlerTimePicker  :global(.el-time-spinner__wrapper):nth-of-type(2){
  display: none;
}
.preHandlerTimePicker :global(.el-time-panel__content)::before,.preHandlerTimePicker :global(.el-time-panel__content)::after{
    content: "" !important;
}
</style>
