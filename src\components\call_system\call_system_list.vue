<template>
  <div class="xpt-flex call_system_list">
    <div class="header-Box">
      <div class="header-bar">
        <div class="hb-left">
          <div class="call-left" v-if="!diaConfig.dialogVisible">{{btnTip}}<span>{{btnTipText}}</span></div>
          <div class="call-left call-out" v-else>外呼弹窗</div>
          <el-button size="small" type="warning" @click='free' :disabled="btn.freeStatus">示闲</el-button>
          <el-button size="small" type="primary" @click='busy' :disabled="btn.busyStatus">示忙</el-button>
          <el-button size="small" type="info" @click='answer' :disabled="btn.answerStatus">应答</el-button>
          <!-- <el-button size="small" type="danger" @click='disconnect' :disabled="btn.disconnectStatus">挂断</el-button> -->
          <el-button size="small" type="danger" @click='newDisconnect' :disabled="btn.disconnectStatus">挂断</el-button>
          <el-button size="small" type="success" @click='callDialog' :disabled="btn.callStatus">呼出</el-button>
        </div>
        <div class="hb-right">
          <el-button size="small" type="primary" @click='checkSigleOrder' :disabled="btn.checkStatus">审核</el-button>
          <el-button size="small" type="primary" @click='uncheckSigleOrder' :disabled="btn.backCheckStatus">退审</el-button>
          <el-button size="small" type="primary" @click='cancelOrder' :disabled="btn.cancelStatus">作废</el-button>
          <el-button size="small" type="primary" @click='restoreSigleOrder' :disabled="btn.restoreStatus">还原</el-button>
          <el-button size="small" type="primary" @click='tabeSearch'>刷新</el-button>
        </div>
        <div class="hb-three">
          <el-button size="small" type="warning" class="addBlankClass" @click="addBlankJob">新建工单</el-button>
        </div>
      </div>
      <div class="search-input-box">
        <call-system-search ref="condition" @handle-search="tabeSearch" @handle-exprot="exportOrderMsg"></call-system-search>
      </div>
      <div class="hB-right">
        <el-switch
          style="z-index: 0;margin-bottom: 8px;"
          v-model="hasSeat"
          on-text="有固话"
          off-text="无固话"
          :width="70"
          @change="handleSeatChange"
          on-color="#13ce66"
          off-color="#ff4949">
        </el-switch>
        <el-switch
          style="z-index: 0;margin-bottom: 8px;"
          v-model="isIntranet"
          on-text="内网"
          off-text="外网"
          :width="70"
          @change="handleNetChange"
          on-color="#13ce66"
          off-color="#ff4949">
        </el-switch>
        <el-button size="small" type="primary" style="margin-bottom: 8px" @click="resignPhoneBar">重新注册</el-button>
        <el-button size="small" type="warning" style="margin-left: 0;width: 68px;" @click="showPhoneStatus">诊   断</el-button>
      </div>
    </div>
    <div class="xpt-flex__bottom">
      <call-record ref="table" :condition="formMap" @table-click="checkReviewed" @before-search="setCondition"
                   @job-click="handleJobClick"
                   @change-tab="changeTab" @call-number="setPhone"></call-record>
    </div>
    <phone-dial :phone="phone" v-if="showDial" @makeCall="makeCall" @close="showDial=false;phone=''"/>
    <el-dialog
      size="tiny"
      title="重新注册电话条"
      custom-class="resignPhoneBarClass"
      :visible="dialogStatus1.popup"
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :modal-append-to-body="false">
      <p>此操作将重新注册电话条，需要等待数秒，是否继续？</p>
      <span slot="footer" class="dialog-footer">
                <el-button :disabled="dialogStatus1.cancelBtnDisabled" :loading="dialogStatus1.cancelBtnLoading" @click="cancelBtn">取 消</el-button>
                <el-button :disabled="dialogStatus1.submitBtnDisabled" :loading="dialogStatus1.submitBtnLoading" type="primary" @click="submitBtn">{{dialogStatus1.submitTxt}}</el-button>
            </span>
    </el-dialog>
    <el-dialog
      size="small"
      title="电话条初始化异常"
      custom-class="initPhoneBarClass"
      :visible="dialogStatus2.popup"
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :modal-append-to-body="false">
      <div class="left-box">
        <p><span style="font-weight: 900;">内网环境</span>（在林氏总部大楼）</p>
        <p>请检查网络环境是否正常，确认正常后再次尝试重新打开新平台，打开呼叫管理页面</p>
      </div>
      <div class="right-box">
        <p><span style="font-weight: 900;">外网环境</span>（不在林氏总部大楼）</p>
        <p>确认右下角电话条图标消失之后再进行下列操作</p>
        <p>1. 点击切换外网环境  2. 点击重新注册</p>
      </div>

      <span slot="footer" class="dialog-footer" style="padding-left: 400px">
                <el-button type="primary" @click="dialogStatus2.popup = false">确 定</el-button>
            </span>
    </el-dialog>
  </div>
</template>

<script>
  import Vue from 'vue'
  import fn from '@common/Fn.js'
  import {apiUrl, EventBus} from '../call_system/base';
  import 'element-ui/lib/theme-default/index.css'
  import callBtn from '@/components/call_system/callBtn';
  import callRecord from '@/components/call_system/call_record';
  import callSystemSearch from '@/components/call_system/call_system_search';
  import {makeUrl} from './base';
  import PhoneDial from './phoneDial';
  import PhoneBar from './phoneBar';
  import Fn from '@/common/Fn.js';

  export default {
    props: ['params'],
    // 从隔很多层的父级组件获取弹窗的状态值
    inject: [ 'diaConfig' ],
    components: {
      PhoneDial,
      callBtn,
      callRecord,
      callSystemSearch,
    },
    data() {
      var _this = this;
      return {
        exit: false,
        selfMakeCall: false,
        ringTime: 0,
        tempState: '',
        // serverIP: 'callcenter.linshimuye.com',
        // serverIP: '************',
        serverIP: '***********',
        vccID: '800800',
        //agentID: '5069',
        // sipPort: '5040',
        sipPort: '5060',
        sipPassword: '00000000',
        mainPort: '14800',
        ctiPassword: '111111',
        agentId: '',
        callingOut: false,
        showDial: false,
        isAwaitCall: false,
        phoneSuggest: [],
        phone: '',
        selectCode: 'untreated',
        list: [
          {
            sys_trade_no: '123456',
            merge_trade_no: '8767655',
            business_type_trade: '放哨',
            audit_status: 'Y',
            impoundment_active: 'kh',
            ali_type: '1',
          }],
        destopNotify: null,
        hasSeat: false, //有没有固话的初始值
        btns: {
          shixian: {disable: true},
          shimang: {disable: true},
          yingda: {disable: true},
          guaduan: {disable: true},
          huchu: {disable: true},
          shenhe: {disable: true},
          tuishen: {disable: true},
          zuofei: {disable: true},
          huanyuan: {disable: true},
        },
        hasPower: 'btn_01,btn_05,btn_06,btn_12',
        cols: [
          {
            label: '销售单号',
            prop: 'sys_trade_no',
            width: 200,
            redirectClick(row) {
              let params = {};
              params.sys_trade_id = row.sys_trade_id;
              _this.$root.eventHandle.$emit('creatTab', {
                name: '销售订单详情',
                params: params,
                component: () => import('@components/order/detail.vue'),
              });
            },
          },
          {
            label: '合并单号',
            prop: 'merge_trade_no',
            redirectClick(row) {
              _this.viewMergedOrder(row.merge_trade_id, row.merge_trade_no);
            },
            width: 200,
          },
          {
            label: '订单业务类型',
            prop: 'business_type_trade',
            format: 'auxFormat',
            formatParams: 'ddywlx',
            width: 130,
          },
          {
            label: '单据状态',
            prop: 'audit_status',
            // format: 'yesOrNo'
            formatter(val) {
              switch (val) {
                case 'Y':
                  return '已审核';
                case 'N':
                  return '未审核';
                default:
                  return val;
              }
            },
          },
          {
            label: '蓄水活动',
            prop: 'impoundment_active',
            format: 'auxFormat',
            formatParams: 'SALE_IMPOUNDMENT_ACTIVE',
            width: 100,
          },
          {
            label: '阿里掌柜订单类型',
            prop: 'ali_type',
            width: 120,
            formatter(val) {
              // switch(val) {
              // 	case 'SQR_ORDER': return '扫码购'; break;
              // 	case 'STEP_ORDER': return '特权定金'; break;
              // 	case 'OTHERS_ORDER': return '其它订单'; break;
              // 	default: return ''; break;
              // }
              return fn.formatAux('allO2oOrderType', val);
            },
          },
        ],
        btnTip: '初始化',
        btnTipText: '00:00',
        records: [],
        status: 1,
        currentStatus: '就绪',
        formMap: {},
        isUpDetailPage:true,
        isIntranet:true,  // 在内网
        dialogStatus1:{
          popup:false,
          cancelBtnDisabled:false,
          cancelBtnLoading:false,
          submitTxt:'确定',
          submitBtnDisabled:false,
          submitBtnLoading:false,
        },
        dialogStatus2:{
          popup:false,
        },
        ifTurnOn:false,//是否接通
        isSalesPersonDisConnect:false,//是否主动挂断
        onLineOptions:{
          '0':"offline",
          '1':"busy",
          '2':"idle",
          '3':"calling",
        }
      };
    },
    computed: {
      btn() {
        return this.$store.state.common.btn
      }
    },
    methods: {
      handleJobClick(row) {
        let status = application.oJVccBar.GetAgentStatus();
        // if (status) { // 0 未登录 2 空閑
        //   return;
        // }
        // 状态=示闲 可点击进入工单详情页 220914版本
        this.showJobDetail(row, false, false);
      },
      setPhone(obj) {
        this.phone = obj.phone;
        this.checkReviewed(obj.row, true);
        // 判断现在是否可以拨号
        if ((PhoneBar.getStatus() == 1 || PhoneBar.getStatus() == 4) && this.selectCode === 'untreated') {
          // 可以拨号的话， 显示拨号盘，
          this.showDial = true;
        }
      },
      setCondition() {
        this.formMap = this.$refs['condition'].getCondition();
      },
      createWork() {
        let _this = this;
        let params = {};
        params.sys_trade_id = '123456789';
        _this.$root.eventHandle.$emit('creatTab', {
          name: '工作台',
          params: params,
          component: () => import('@components/call_system/call_record.vue'),
        });
      },
      // 关闭标签页
      closeTab() {
        this.$root.eventHandle.$emit('removeTab', this.params.tabName);
      },
      showJobDetail(params, fromCalling = false, autoBinding = true) {


        let tabId = '电话工单-' + params.jobId;
        // this.$root.eventHandle.$emit('removeTab', tabId);
        this.$nextTick(() => {
          this.$root.eventHandle.$emit('creatTab', {
            name: '话务工单',
            tabId: tabId,
            params: Object.assign({}, params, {fromCalling: !!fromCalling, autoBinding: autoBinding}),
            component: () => import('@components/call_system/call_system_detail.vue'),
          });
        });

      },
      addNewJob(phoneNo) {
        let userInfo = this.getUserInfo();
        this.$http.post(apiUrl.workOrder_add, {
          calledNumber: phoneNo,
          operatedUserId: userInfo.employeeNumber,
          operatedUserName: userInfo.fullName,
        }).then(resp => {
          if (resp.data.code === 0) {
            this.showJobDetail(resp.data.content, true, true);
          }
        });
      },
      showNoCloseJob(phoneNo) {
        this.$http.get(makeUrl(apiUrl.workOrder_getNonClose, {phoneNumber: phoneNo})).then(resp => {
          if (!resp.data.content) {
            this.addNewJob(phoneNo);
            return;
          }
          this.showJobDetail(resp.data.content, true, true);



        });
      },
      showNotification(phoneNo) {
        let show = false;
        // 先检查浏览器是否支持
        if (!('Notification' in window)) {
          alert('本浏览器不支持弹窗通知，可能无法获得来电提醒');
        }
        // 检查用户是否同意接受通知
        else if (Notification.permission === 'granted') {
          show = true;
        }
        // 否则我们需要向用户获取权限
        else if (Notification.permission !== 'denied') {
          Notification.requestPermission(function(permission) {
            // 如果用户同意，就可以向他们发送通知
            if (permission === 'granted') {
              show = true;
            }
          });
        }
        if (show) {
          var notification = new Notification('来电通知', {
            body: '收到来自' + phoneNo + '的电话\n请尽快接听！',
            timestamp: 30000,
            image: '/static/images/ringtone_ringing-512.png',
          });
          this.destopNotify = notification;
          // let _this = this;
          // notification.onclick = (e) => {
          //   _this.answer();// 应答
          //   window.focus();
          //   notification.close();
          // };
        }
      },
      // 呼出电话
      makeCall(phoneNo) {
        // this.selfMakeCall = true;
        // this.ifCall = true;
        let value = window.application.oJVccBar.MakeCall(phoneNo, 0, '', '', '');
        if (value != 0) {
          for (var i = 0; i < 2; i++) {
            if (value != 0) {
              setTimeout(()=>{ 
                value = window.application.oJVccBar.MakeCall(phoneNo, 0, '', '', '');
              },3000)              
            } else {
              break;
            }
          }
          if (value != 0) {
            this.$message.error('电话条响应异常，请重试！  如果连续多次异常，请重新打开浏览器！')
            this.saveLogByPhoneBar('呼出', value);
          }
        }
      },
      // 振铃时
      onCallRing(
        CallingNo/*主叫号码*/, CalledNo/*被叫号码*/, OrgCalledNo/*原始被叫号码*/, CallData, SerialID, ServiceDirect, CallID,
        UserParam, TaskID, UserDn, AgentDn, AreaCode, Filename, networkInfo, queueTime, opAgentID) {
        if(this.diaConfig.dialogVisible) return;
        // this.ifCall = false;
        // this.ifAnswer = false;
        this.ringTime = new Date().getTime();
        this.btns.huchu.disable = true;
        this.tempState = this.btnTip;
        this.btnTip = '振铃';
        this.ifTurnOn=false;
        // console.log('CallingNo-------------------------', CallingNo, CalledNo);
        console.log("onCallRing 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());

        if (CallingNo !== CalledNo) { // 呼入
          this.btns.yingda.disable = false;
          this.callingOut = false;
          this.showNotification(CallingNo);
          // 增加全局变量
          this.isAwaitCall = true;
          this.changeBtnStatus(5)
          this.$http.post(apiUrl.callEventLog_add, {
            'callId': CallID,
            'eventName': '响铃事件',
            'eventTime': Fn.dateFormat(new Date, 'yyyy-MM-dd hh:mm:ss'),
            'phoneNumber': CallingNo,
            'agentId': this.getUserInfo().employeeNumber,
          }).then(resp => {

          }).catch(err => {

          });
        } else { // 呼出
          this.callingOut = true;
          this.btns.guaduan.disable = false;
          this.changeBtnStatus(6)

          // this.showNoCloseJob(OrgCalledNo);
        }
        this.$root.eventHandle.$emit('setTabClosable', 'call_system_list', false);
        this.showTime();


        // 振铃时先保存一部分数据
        setTimeout(() => {

          var filename = "";
          var directory = "";
          var subIndex = Filename.lastIndexOf("/");
          if(subIndex > 0){
              directory = Filename.substring(0,subIndex);
              filename =  Filename.substring(subIndex+1);
          }
          var beginTimeText = Fn.dateFormat(new Date, 'yyyyMMddhhmmss');
          // 振铃的时候直接新增一条呼叫记录（callstat），挂断时再更新该数据
          this.$http.post(apiUrl.callStat_add, { 
              "beginTimeText": beginTimeText, //处理
              "businessGroup": null,
              "callDuration": null,
              "callNumber": "Y", // 添加标识Y 标识未正常挂断，在callend方法中更新该数据时把Y改为N,表示已正常挂断
              "callType": this.callingOut ? '呼出' : '呼入',
              "type": this.callingOut ? '呼出' : '呼入',
              "directory": directory,
              "endTimeText": "",
              "fileName": filename,
              "isInitiative": false,
              "phoneNumber": UserDn,
              "serialId": SerialID,
              "sourceCallId": CallID,
              "sourceOrderId": null,
              "sourceOrderNo": null,
              "sourceOrderType": null,
              "waitingTime": new Date() - this.ringTime
            }).then(res => {
                console.log("振铃成功插入callstat");
                // 返回的callId其实是通话记录的id，这里改成jobId，短的
                //this.jobId = res.body.content.callId;
            });
          
        }, 100);
 
      },
      // 点击呼出按钮时触发
      callDialog() {
        if(this.checkPopupStatus()) return;
        this.showDial = !this.showDial;

        if (!this.showDial) {
          this.phone = '';
        }
      },
      //TODO: setBusy()  点击忙碌时触发
      busy(userStatus) {
        // 当外呼页面登陆了电话条的时候，再打开呼叫管理则是直接更新状态的，这个时候就会传值过来，就不应该进行打断了
        if(!userStatus && this.checkPopupStatus(userStatus)) return;
        // 如果phone加载完才调用

        if (window.applicationLoad) {

          let oJVccBarStatus = application.oJVccBar.GetAgentStatus();            
          if(oJVccBarStatus == 3 ){
            console.log(" 电话条状态为通话中，应该终止示忙请求："+oJVccBarStatus+" 时间："+new Date());
            //return;
          }
          console.log(" 用户触发示忙请求："+oJVccBarStatus+" 时间："+new Date());
          let value = application.oJVccBar.SetBusy();
          if (value != 0) {
            for (var i = 0; i < 2; i++) {
              if (value != 0) {
                setTimeout(()=>{ 
                  value = application.oJVccBar.SetBusy();
                },3000)
              } else {
                break;
              }
            }
            if (value != 0) {
              this.$message.error('电话条响应异常，请重试！  如果连续多次异常，请重新打开浏览器！')
              this.saveLogByPhoneBar('示忙', value);
            }
          }
          this.btnTip = '忙碌';
          this.currentStatus = '忙碌';
          this.showTime();
        }
      },
      // 点击空闲时触发
      free() {
        if(this.checkPopupStatus()) return;
        let value = window.application.oJVccBar.SetIdle();
        if (value != 0) {
          for (var i = 0; i < 2; i++) {
            if (value != 0) {
              setTimeout(()=>{
                value = window.application.oJVccBar.SetIdle();
              },3000)              
            } else {
              break;
            }
          }
          if (value != 0) {
            this.$message.error('电话条响应异常，请重试！  如果连续多次异常，请重新打开浏览器！')
            this.saveLogByPhoneBar('示闲', value);
          }
        }
        // this.btnTip = '就绪';
        // this.currentStatus = '就绪';
        this.btnTip = '示闲';
        this.currentStatus = '示闲';
        this.showTime();
      },
      // 修改固话参数
      handleSeatChange(newVal) {
        let oldStatus = !newVal;
        this.$confirm('此操作将修改固话参数，是否继续？', '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal:false,
          closeOnPressEscape:false,
          type: 'warning'
        }).then(() => {
          window.localStorage.setItem('hasSeatPreference', newVal);
        }).catch(() => {
          this.hasSeat = oldStatus;
          window.localStorage.setItem('hasSeatPreference', oldStatus);
          this.$message({
            type: 'info',
            message: '已取消修改'
          });
        });
      },
      // 如果电话条功能出错，则写入日志
      saveLogByPhoneBar (eventName,eventStatus){
        this.$http.post(apiUrl.callEventLog_add, {
          'callId': PhoneBar.getCurrentCallInfo().CallID,
          'eventName': eventName,
          'eventStatus': eventStatus,
          'eventTime': Fn.dateFormat(new Date, 'yyyy-MM-dd hh:mm:ss'),
          'phoneNumber': PhoneBar.getCustPhoneNum(),
          'agentId': this.getUserInfo().employeeNumber,
        }).then(resp => {

        }).catch(err => {

        });
      },
      forceAnswer() {
        this.answer();
        this.$http.post(apiUrl.callEventLog_add, {
          'callId': '',
          'eventName': '强制应答',
          'eventTime': Fn.dateFormat(new Date, 'yyyy-MM-dd hh:mm:ss'),
          'phoneNumber': '',
          'agentId': this.getUserInfo().employeeNumber,
        }).then(resp => {

        }).catch(err => {

        });
      },
      answer() {
        if(this.checkPopupStatus()) return;
        try {
          // this.ifAnswer = true;
          let value = window.application.oJVccBar.Answer();
          if (value != 0) {
            for (var i = 0; i < 2; i++) {
              if (value != 0) {
                setTimeout(()=>{ 
                  value = window.application.oJVccBar.Answer();
                },3000)                
              } else {
                break;
              }
            }
            if (value != 0) {
              this.$message.error('电话条响应异常，请重试  如果连续多次异常，请重新打开浏览器！')
              this.saveLogByPhoneBar('应答', value);
            }
          }
        } catch (e) {
          alert(JSON.stringify(e))
        }
        console.log("answer 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());

        // PhoneBar.addOnAnswerCall(this, this.answerCall);
        // window.application.oJVccBar.Answer();
        this.btnTip = '通话中';
        this.showTime();
        this.btns.yingda.disable = true;
        this.isAwaitCall = false;
        this.changeBtnStatus()
      },
      // 点击挂断按钮
      disconnect() {
        if(this.checkPopupStatus()) return;   // 检测呼出弹窗状态
        let value = window.application.oJVccBar.Disconnect();// 调用失败？ 返回结果-1 ， 没有接听直接挂断的结果？； 初步结果没有应答直接挂断
        if (value != 0) {
          for (var i = 0; i < 2; i++) {
            if (value != 0) {              
              setTimeout(()=>{
                value = window.application.oJVccBar.Disconnect();
               },3000)
            } else {
              break;
            }
          }
          if (value != 0) {
            this.$message.error('电话条响应异常，请重试！  如果连续多次异常，请重新打开浏览器！')
            this.saveLogByPhoneBar('挂断', value);
          }
        }
        console.log("disconnect 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());
        application.oJVccBar.SetBusy();

        this.btnTip = this.currentStatus;
        this.showTime();
      },
      newDisconnect(){
          this.isSalesPersonDisConnect=true
          if(this.ifTurnOn){
              this.satisfyCheck()
          }else{
              this.disconnect()
          }
      },
      //满意度调查-具有挂断功能
      satisfyCheck() {
        if(this.checkPopupStatus()) return;   // 检测呼出弹窗状态
        var subfix = window.location.host.startsWith('sale.linshimuye') ? '9999' : '9998';
        let num = '00000180080000' + subfix;
        application.oJVccBar.TransferOut(num, num);
    },
      answerCall(phoneNo/*电话号码*/) {
        if(this.diaConfig.dialogVisible) return;
        this.btnTip = '通话中';
        this.ifTurnOn = true;
        this.btns.guaduan.disable = false;
        this.changeBtnStatus()
        this.showTime();
        console.log("answerCall 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());
        if(!this.isUpDetailPage){
         return
        } else {
          this.isUpDetailPage = false
        }
        setTimeout(()=>{
          this.isUpDetailPage = true
        },4000)
        this.showNoCloseJob(phoneNo);
      },
      homeTabActive() {
      },
      showPhoneStatus() {
        var status = '';
        switch (PhoneBar.getStatus()) {
          default :
            status = '忙碌';
            break;
          case 0:
            status = '离线';
            break;
          case 1:
            status = '忙碌';
            break;
          case 2:
            status = '示闲';
            break;
          case 3:
            status = '通话中/呼叫中';
            break;
          case 4:
            status = '后续态';
            break;
        }
        let config = application.oJVccBar.GetConfiguration();
        let explain = {
          1: '自动应答',
          2: '挂机状态',
          3: '外呼内部号码支持内部号码',
          4: '来电滴滴声',
          5: '来电是否产生铃声',
          6: 'sip重新注册时间',
          7: '是否显示视频窗口',
          8: '带宽（*1000）',
          9: '帧率',
          10: '视频格式',
          11: '指定本机IP',
        };
        var msg = `当前电话条状态是：${status}`;
        if (config.lastIndexOf('|') == config.length - 1) {
          config = config.substr(0, config.length - 1);
        }
        var configs = config.split('|').map((val, index) => {
          return `${explain[index + 1]}  :${val}`;
        });
        configs.push(msg)
        const h = this.$createElement;
        let k = h('p',null,
          configs.map(conf=>{
            return h('div',null,conf)
          }));

        this.$msgbox( {
          title:'提示',
          message: k,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        }).then(() => {
        }).catch(() => {

        });
      },
      callEnd(
        callid, serialid, servicedirect, userno, bgntime, endtime, agentalerttime, useralerttime, filename, directory,
        disconnecttype, userparam, taskid, servername, networkinfo) {
        console.table([['是否接通',bgntime>0],['是否客服挂断:',this.isSalesPersonDisConnect]]);
        if(this.diaConfig.dialogVisible) return;
        // console.log('call_system_list----callEnd')
        console.log("callEnd 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());
        this.selfMakeCall = false;
        this.$root.eventHandle.$emit('setTabClosable', 'call_system_list', true);
        this.$http.post(apiUrl.callStat_add, {
          sourceOrderType: null,
          sourceOrderId: null,
          sourceOrderNo: null,
          callType: this.callingOut ? '呼出' : '呼入',
          type: this.callingOut ? '呼出' : '呼入',
          phoneNumber: userno,
          callNumber: null,
          fileName: (!!bgntime && !!endtime) ? filename : null,
          directory: (!!bgntime && !!endtime) ? directory : null,
          beginTimeText: bgntime,
          endTimeText: endtime,
          serialId: serialid,
          ringSeconds: parseInt(useralerttime),
          sourceCallId: callid,
          callDuration: null,
          businessGroup: null,
          isInitiative: false,
          waitingTime: new Date() - this.ringTime,
          isSalesPersonDisConnect:this.isSalesPersonDisConnect//是否客服挂断
        }).then(resp => {
            this.isSalesPersonDisConnect=false
        });
        this.busy();
        if (!!this.destopNotify) {
          this.destopNotify.close();
          this.destopNotify = null;
        }
      },
      OnTelReport(data) {

      },
      callOutDialog(key) {
        var spevent = window.event || this.arguments.callee.caller.arguments[0];
        window.application.oJVccBar.PopDlg(
          key,
          parseInt(spevent.screenX),
          parseInt(spevent.screenY),
        );
      },
      editDetail() {
        let params = {jobId: this.$refs.table.jobId};
        this.showJobDetail(params);
      },
      showTime() {
        this.clearMyInterval();
        this.count = 0;
        this.count = this.count + 1;
        this.btnTipText = this.getTimerString(this.count);
        this.myInterval();
      },
      // 计时器
      getTimerString(len) {
        if (len === 0) return '';
        let hour = parseInt(len / 3600);
        hour = (hour < 10 ? '0' + hour : hour);
        if (hour === '00') hour = '';
        else hour = hour + ':';
        let minute = parseInt((len % 3600) / 60);
        minute = (minute < 10 ? '0' + minute : minute);
        let second = len % 60;
        second = (second < 10 ? '0' + second : second);
        return (hour.toString() + minute.toString() + ':' + second.toString());
      },
      // 搜索
      tabeSearch() {
        // this.getTelStatus();
        this.$refs['table'].init();
        // this.getOneTablelist()
      },
      // 分页点击
      pageReload(val) {
        this.pageNo = val;
        this.getOneTablelist();
      },
      // tabs切换
      changeTab(data) {
        this.selectCode = data.code;
        this.isInvalid = data.isInvalid;
        this.isApproved = data.isApproved;

        // 判断是否应答
        if(this.isAwaitCall){
          this.changeBtnStatus(5);
        }else{
          this.changeBtnStatus();
        }
        
      },
      changeBtnStatus(inOrOut) {
        // console.log('btn status')
        this.btns.shixian.disable = true;//示闲
        this.btns.shimang.disable = true;//示忙
        // this.btns.huchu.disable = true;
        this.btns.shenhe.disable = true;//审核
        this.btns.tuishen.disable = true;//退审
        this.btns.zuofei.disable = true;//作废
        this.btns.huanyuan.disable = true;//还原
        this.btns.huchu.disable = false;//呼出
        if(inOrOut){
          this.btns.yingda.disable = true;
        }

        //  0：未登录   1：忙碌  2：空闲  3：通话中  4：后续态
        let status = application.oJVccBar.GetAgentStatus();
        console.warn('呼叫管理页面 - 修改按钮状态', status);
        if (status == 1 || status == 4) {
          // this.btns.huchu.disable = this.selectCode !== 'untreated'; // 未处理以外的标签页，不能呼出
          this.btns.shixian.disable = false;
          this.btns.guaduan.disable = true;
        }
        if (status == 2) {
          this.btns.shimang.disable = false;
          this.btns.huchu.disable = true;
          this.btns.guaduan.disable = true;
        }
        switch (this.selectCode) {
          case 'untreated': // 未处理
            this.btns.shenhe.disable = false;
            this.btns.zuofei.disable = false;
            break;
          case 'processed': // 已处理
            this.btns.tuishen.disable = false;
            break;
          default: // 已作废
            this.btns.huanyuan.disable = false;
            break;
        }
        if(inOrOut === 5 || inOrOut === 6) {
          status = inOrOut
        }
		// 恢复通话默认值
        if(status == 1 || status == 4){
          this.isAwaitCall  = false;
        }

        //  0：未登录   1：忙碌  2：空闲  3：通话中  4：后续态 5:来电振铃  6：呼出振铃 默认：后续态
        switch(status) {
          case 0:
            this.$store.commit('common/SET_WAIT')
            break;
          case 1:
            this.$store.commit('common/SET_BUSY')
            break;
          case 2:
            this.$store.commit('common/SET_FREE')
            break;
          case 3:
            this.$store.commit('common/SET_ANSWER')
            break;
          case 4:
            this.$store.commit('common/SET_BUSY')
            break;
          case 5:
            this.$store.commit('common/SET_ON_CALL_RING')
            break;
          case 6:
            this.$store.commit('common/SET_CALL')
            break;
          default:
            this.$store.commit('common/SET_WRAP_UP')
            break;
        }
        switch (this.selectCode) {
          case 'untreated': // 未处理
            this.$store.commit('common/SET_UNTREATED')
            break;
          case 'processed': // 已处理
            this.$store.commit('common/SET_PROCESSED')
            break;
          default: // 已作废
            this.$store.commit('common/SET_INVALID')
            break;
        }


      },
      updateAgentStatus() {
        //0：未登录   1：忙碌  2：空闲  3：通话中   4：后续态
        let status = 'offline';
        console.log('呼叫管理页面 - 修改用户状态',PhoneBar.getStatus()+" 时间："+new Date())
        switch (PhoneBar.getStatus()) {
          default :
            status = 'busy';
            // this.btnTip = '后续态';
            break;
          case 0:
            if(this.exit) {
              break;
            }else {
              status = 'offline';
              this.btnTip = '离线';
              this.showTime();
              // this.$confirm('电话条已离线', '提示', {
              //   confirmButtonText: '重新登录',
              //   cancelButtonText: '取消',
              //   type: 'warning',
              // }).then(() => {
              //   this.initPhoneBar();
              // });
            }
            break;
          case 1:
            status = 'busy';
            this.btnTip = '忙碌';
            break;
          case 2:
            status = 'idle';
            this.btnTip = '示闲';
            break;
          case 3:
            status = 'calling';
            // this.btnTip = '通话中';
            break;
        }
        this.showTime();
        this.$http.get(makeUrl(apiUrl.updateAgentStatus, {status: status}));
      },
      // 订单id存储
      checkReviewed(tableRow, doNotSetPhone) {
        this.sigleOrderNo = tableRow.jobId;
      },
      // 未选择订单 友好提示
      showTip() {
        /*
      if (this.sigleOrderNo === '') {
        this.$message('请选择需要操作的订单!')
        return
      }
      */
      },
      // 导出
      exportOrderMsg() {
        let param = this.$refs['table'].obtainQueryParam();
        window.open(makeUrl(apiUrl.workOrder_export, param));
        //  this.$http.get(makeUrl(apiUrl.workOrder_export,param))
        //  .then(res => {
        //
        //   }, res => {
        //
        //   });
      },
      getJobIds() {
        let jobIds = [];
        for (let r of this.$refs.table.selectedRows) {
          jobIds.push(r.jobId);
        }

        return jobIds.join(',');
      },
      // 作废
      cancelOrder() {
        if (!this.$refs.table.selectedRows || this.$refs.table.selectedRows.length == 0) {
          this.$message('请选择需要操作的工单');
          return;
        }

        let jobIds = this.getJobIds();
        let params = {
          jobId: jobIds,
          isInvalid: 1,
        };
        this.$http.post(apiUrl.workOrder_updateInvalid + '?jobId=' + params.jobId + '&isInvalid=' + params.isInvalid,
          {}).
          then(res => {
            this.tabeSearch();
          }, res => {

          });
      },
      // 还原功能
      restoreSigleOrder() {
        if (!this.$refs.table.selectedRows || this.$refs.table.selectedRows.length == 0) {
          this.$message('请选择需要操作的工单');
          return;
        }

        let jobIds = this.getJobIds();
        let params = {
          jobId: jobIds,
          isInvalid: 0,
        };
        this.$http.post(apiUrl.workOrder_updateInvalid + '?jobId=' + params.jobId + '&isInvalid=' + params.isInvalid,
          {}).
          then(res => {
            this.tabeSearch();
          }, res => {

          });
      },
      // 审核功能
      checkSigleOrder() {
        if (!this.$refs.table.selectedRows || this.$refs.table.selectedRows.length == 0) {
          this.$message('请选择需要操作的工单');
          return;
        }

        let jobIds = this.getJobIds();
        let params = {
          jobId: jobIds,
          isApproved: 1,
        };
        this.$http.post(apiUrl.workOrder_updateApproved + '?jobId=' + params.jobId + '&isApproved=' + params.isApproved,
          {}).
          then(res => {

            // 刷新tableone数据
            this.tabeSearch();
          }, res => {

          });
      },
      // 退审功能
      uncheckSigleOrder() {
        if (!this.$refs.table.selectedRows || this.$refs.table.selectedRows.length == 0) {
          this.$message('请选择需要操作的工单');
          return;
        }

        let jobIds = this.getJobIds();
        let params = {
          jobId: jobIds,
          isApproved: 0,
        };
        this.$http.post(apiUrl.workOrder_updateApproved + '?jobId=' + params.jobId + '&isApproved=' + params.isApproved,
          {}).
          then(res => {
            // 刷新tableone数据
            this.tabeSearch();
          }, res => {

          });
      },
      // 工单列表操作-标记-审核/退审-作废/还原
      // 计时器
      myInterval() {
        this.timer = setInterval(() => {
          this.count = this.count + 1;
          this.btnTipText = this.getTimerString(this.count);
        }, 1000);
      },
      clearMyInterval() {
        window.clearInterval(this.timer);
      },
      //TODO: initPhoneBar
      initPhoneBar() {
        this.exit = false;
        //let userInfo = this.getUserInfo(); // userInfo.employeeNumber
        //let userNo = /^\d+$/.test(userInfo.employeeNumber) ? Number(userInfo.employeeNumber) : userInfo.employeeNumber;
        // PhoneBar.phoneInitial(this.serverIP, this.vccID, userNo, this.sipPort, this.sipPassword, this.mainPort,
        //   this.ctiPassword);
        console.log("initPhoneBar 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());
        let status = application.oJVccBar.GetAgentStatus();
        if (status === 0) {
          let userNo = this.agentId;
          //  0：未登录   1：忙碌  2：空闲  3：通话中  4：后续态
          PhoneBar.phoneInitial(this.serverIP, this.vccID, userNo, this.sipPort, this.sipPassword, this.mainPort, this.ctiPassword);
          setTimeout(()=>{
            this.initJudge();
          },3500)
          // console.log('未登录')
        } else {
          this.busy('initSetBusy');
          this.changeBtnStatus(true);
          this.updateAgentStatus();
        }


        this.initEventBusFn();
      },

      // 电话条初始化判断
      initJudge(){
        let choicenum = 0;
        console.log("initJudge 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());
        let timeer = setInterval(()=>{
          choicenum++;
          if(application.oJVccBar.SetBusy() == 0){
            clearInterval(timeer);
            // this.$message.success('注册成功')
          }else if(application.oJVccBar.SetBusy() != 0 && choicenum == 7){
            this.dialogStatus2.popup = true;
            clearInterval(timeer);
          }
        },1000)
      },
      // 获取员工的固话状态值
      async getTelStatus() {
        let staffName = this.getEmployeeInfo('fullName');
        await this.$http.post(apiUrl.employee_TelStatus, {staffName}).then(res => {
          if(res.data.result && res.data.content){
            // 有无固话
            if(res.data.content.curingState == 0){
              this.hasSeat = true;
            }else if(res.data.content.currentStatus == 1){
              this.hasSeat = false;
            }else{
              this.hasSeat = false;
            }
            // 走专线还是外网电信服务器
            if(res.data.content.isIntranet == 'Y'){
              this.changeData_Intranet(1)
              this.isIntranet = true ;
            }else if(res.data.content.isIntranet == 'N'){
              this.changeData_Intranet(2)
              this.isIntranet = false ;
            }else{
              this.changeData_Intranet(2)
              this.isIntranet = false ;
            }
            var  agentIdData = res.data.content.agentId;
            if ( agentIdData == null || agentIdData == "") {
              this.$message.error("呼叫中心获取人员信息失败,无法获取对应的坐席号,请关闭页签后重新打开");
              return;
            }
            this.agentId = /^\d+$/.test(agentIdData) ? Number(agentIdData) : agentIdData;
            console.log("坐席号:"+this.agentId);
          }else{
            this.hasSeat = false;
            //查不到相关信息的直接走外网
            this.changeData_Intranet(2)
            this.isIntranet = false ;
          }
        }).catch(err => {
          this.$message.error(err);
        }).finally(() => {});
      },
      // 根据对应值进行设置
      changeData_Intranet(status){
        if(status == 1){
          //走专线
          this.serverIP =  '***********';
          this.sipPort= '5060';
        }else if(status == 2){
          //走公网
          this.serverIP =  '************';
          this.sipPort= '5040';
        }
      },
        getAgentWorkEventName(){
            this.changeBtnStatus();
            this.updateAgentStatus();
        },
        call_system_make_callFn(phoneNo){
            this.makeCall(phoneNo);
        },
        order_savedFn(){
            this.tabeSearch();
        },
        destoryEventFn(){
            EventBus.off(PhoneBar.getCallingEventName,this.onCallRing);

            EventBus.off(PhoneBar.getAnswerCallEventName,this.answerCall);

            EventBus.off(PhoneBar.getCallEndEventName,this.callEnd);

            EventBus.off(PhoneBar.getAgentWorkEventName,this.getAgentWorkEventName)

            // 初始化完了设置状态为示忙
            EventBus.off(PhoneBar.getPhoneInitName, this.busy);
            EventBus.off(EventBus.order_saved, this.order_savedFn);
            EventBus.off(EventBus.call_system_make_call,this.call_system_make_callFn);
        },
      // 初始化eventlisten TODO: initEventBusFn
      initEventBusFn(){
        // console.log(PhoneBar)
        // console.log(PhoneBar.getCallEndEventName)
        EventBus.on(PhoneBar.getCallingEventName,this.onCallRing);

        EventBus.on(PhoneBar.getAnswerCallEventName,this.answerCall);

        EventBus.on(PhoneBar.getCallEndEventName,this.callEnd);

        EventBus.on(PhoneBar.getAgentWorkEventName,this.getAgentWorkEventName)

        // 初始化完了设置状态为示忙
        // this.busy('initSetBusy');
        // EventBus.on(PhoneBar.getPhoneInitName, this.busy('initSetBusy'));
      },

      // 设置外网和内网
      handleNetChange(newVal){
        let oldStatus = !newVal;
        this.$confirm('此操作将修改网络参数，是否继续？', '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal:false,
          closeOnPressEscape:false,
          type: 'warning'
        }).then(() => {
          let thenum = newVal ? 1:2;
          this.changeData_Intranet(thenum)
        }).catch(() => {
          this.isIntranet = oldStatus;
          let thenum = oldStatus ? 1:2;
          this.changeData_Intranet(thenum)
          this.$message({
            type: 'info',
            message: '已取消修改'
          });
        });

        // //切换后要重新注册
        // PhoneBar.phoneUninstall();
        // // 如果2秒之内状态没有更改，直接重新出发登录
        // if(this.resetNum == '') this.resetNum = null;
        // this.resetNum = setTimeout(()=>{
        //   if(PhoneBar.getStatus() == 0){
        //     this.initPhoneBar();
        //   }
        // },1000)
      },
      // 重新注册电话条
      resignPhoneBar() {
        this.dialogStatus1.popup = true;
      },
      //重新注册弹窗取消按钮
      cancelBtn() {
        this.dialogStatus1.popup = false;
      },
      //重新注册弹窗确定按钮
      submitBtn() {
        this.dialogStatus1.cancelBtnDisabled = true;
        this.dialogStatus1.submitBtnLoading = true;
        // 下线
        PhoneBar.phoneUninstall();

        // 重登
        setTimeout(()=>{
           //let userInfo = this.getUserInfo(); // userInfo.employeeNumber
           //let userNo = /^\d+$/.test(userInfo.employeeNumber) ? Number(userInfo.employeeNumber) : userInfo.employeeNumber;
            let userNo = this.agentId;
            PhoneBar.phoneInitial(this.serverIP, this.vccID, userNo, this.sipPort, this.sipPassword, this.mainPort,
              this.ctiPassword);

        },2000)
        setTimeout(()=>{
          this.checkInitStatus();
        },3000)
      },
      // 轮询查看电话条注册了没有
      checkInitStatus(){
        let choicenum = 0;
        let timeer = setInterval(()=>{
          choicenum++;
          // console.log(application.oJVccBar.SetBusy())
          console.log("checkInitStatus 电话条状态："+ application.oJVccBar.GetAgentStatus()+" 时间："+new Date());
          if(application.oJVccBar.SetBusy() == 0){
            this.dialogStatus1.cancelBtnDisabled = false;
            this.dialogStatus1.submitBtnLoading = false;
            this.dialogStatus1.popup = false;
            clearInterval(timeer);
            this.$message.success('注册成功')
          }else if(application.oJVccBar.SetBusy() != 0 && choicenum == 7){
            this.dialogStatus1.cancelBtnDisabled = false;
            this.dialogStatus1.submitBtnLoading = false;
            clearInterval(timeer);
            this.$message.error('注册失败')
          }
        },1000)
      },
      // 检测呼出弹窗状态
      checkPopupStatus(userStatus){
        if(!userStatus &&　this.diaConfig.dialogVisible) {
          this.$message.error('请先关闭外呼弹窗')
          return true
        };
      },
      // 新增空白工单，功能按钮
      addBlankJob(){
        // 1. 查询用户当前状态，通话中 / 其它
        // 获取用户基本状态  0:未登录  1:忙碌  2：空闲  3:通话中  4：后续态
        let userStatus = application.oJVccBar.GetAgentStatus();
        // 获取当前通话中的手机号
        let phoneNo = PhoneBar.getCustPhoneNum();
        if(userStatus == 3){
          // 通话中，直接根据用户信息弹出工单
          this.showNoCloseJob(phoneNo);
        }else{
          // 直接新建一张空白的工单
          this.$root.eventHandle.$emit('creatTab', {
            name: '话务工单',
            params: Object.assign({}, {fromCalling: true, autoBinding: true, fromBlankJob: true}),
            component: () => import('@components/call_system/call_system_detail.vue'),
          });
        }
      },
      //客服主动挂断
      watchIsSalesPersonDisConnect(isSalesPersonDisConnect){
          this.isSalesPersonDisConnect=isSalesPersonDisConnect
      }
    },
    mounted: async function() {
      // this.getRegisterAddress();
      await this.getTelStatus();
      this.initPhoneBar();
      this.showTime();
      // console.log('mounted',this.$store.state.common)
      // console.log('mounted',this.$store)
      // 检测新增、编辑数据，刷新
      this.$root.eventHandle.$on('refresh_list', function() {
        this.$refs['table'].init();
      });
      // await this.getTelStatus();
      this.$nextTick(() => {
        EventBus.on(EventBus.order_saved, this.order_savedFn);
      })
      ;
      EventBus.on(EventBus.call_system_make_call,this.call_system_make_callFn);
      EventBus.on('watchIsSalesPersonDisConnect',this.watchIsSalesPersonDisConnect)
      // this.hasSeat = localStorage.getItem('hasSeatPreference') === 'true';

      let agentStatus=this.onLineOptions[application.oJVccBar.GetAgentStatus()]
      this.$http.post(`/callcenter-web/agentStatus/registerUserStatus.do?status=${agentStatus}`)
      !!Vue.prototype.onLineTimer&&clearInterval(Vue.prototype.onLineTimer)
      Vue.prototype.onLineTimer=setInterval(()=>{
        console.log("电话条状态：",this.onLineOptions[application.oJVccBar.GetAgentStatus()])
        let agentStatus=this.onLineOptions[application.oJVccBar.GetAgentStatus()]
        this.$http.post(`/callcenter-web/agentStatus/registerUserStatus.do?status=${agentStatus}`)
      },600000)
      this.$once('hook:beforeDestroy', () => {            
          clearInterval(Vue.prototype.onLineTimer);                                    
      }) 
    },
    beforeDestroy() {
      this.exit = true;
      if(!this.diaConfig.dialogVisible) {
        PhoneBar.phoneUninstall();
      };
        // 清空监听函数
        this.destoryEventFn();
      this.$http.get(makeUrl(apiUrl.updateAgentStatus, {status: 'offline'}));
    },
    destroyed() {
      this.$root.offEvents('refresh_list');

    },
    watch: {},

  };
</script>
<style>
  .call_system_list .el-dialog__wrapper .initPhoneBarClass {
    width: 635px !important;
  }
</style>
<style scoped>
  .initPhoneBarClass p{
    /*margin-bottom: 10px;*/
    line-height: 25px;
  }
  .header-Box{
    background-color: #eee;
    padding-right: 100px;
    position: relative;
    min-height: 140px;
    margin-left: -10px;
  }
  .header-bar {
    height: 40px;
    line-height: 40px;
    margin-left: -10px;
    /*margin-right: -10px;*/
    margin-bottom: 0;
    background: #eee;
    border-bottom: 1px solid #ccc;
    /*padding:5px 0px;*/
    padding-left: 5px;
  }

  .hb-left,.hb-right{
    display: inline-block;
  }

  .hb-left{
    border-right: 1px solid #ccc;
    padding-right: 15px;
  }
  .hb-right{
    padding: 0 15px;
    border-right: 1px solid #ccc;
  }
  .hb-three{
    display: inline-block;
    padding: 0 15px;
  }

  .hB-right{
    width: 100px;
    position: absolute;
    right: 0;
    top: 0;
    border-left: 1px solid #ccc;
    height: 100%;
    text-align: center;
    padding-top: 8px;
  }

  .left-box,.right-box{
    display: inline-block;
    width:45%;
    height: 120px;
    vertical-align: top;
    border:1px solid #ccc;
    padding:5px;
    margin: 0 5px;
  }

  .call-left {
    text-align: center;
    display: inline-block;
    width: 80px;
    line-height: 19px;
    float: left;
    padding: 0 10px;
    border-right: 1px solid #ccc;
    margin-right: 10px;
  }

  .call-out{
    height: 40px;
    line-height: 38px;
  }

  .call-left span {
    display: block;
    border-top: solid #ccc 2px;
  }

  .select-tab-two span {
    height: 26px;
    line-height: 26px;
    padding: 0 10px;
    display: inline-block;
    /*float:left;*/
    cursor: pointer;
    border: solid #ccc 1px;
    border-bottom: none;
    margin-right: 10px;
    border-radius: 5px 5px 0 0;
  }

  .select-tab-two span.active {
    background: #20a0ff;
    color: #fff;
    border: solid #20a0ff 1px;
    border-bottom: none;
  }
</style>
