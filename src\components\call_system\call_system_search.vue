<!-- 搜索 -->
<template>
  <div class="search-work">
    <div class="form-box">
      <el-form ref="form" :model="formMap" label-width="80px">
        <div class="form-inline">
          <div class="form-inline search-label">买家昵称</div>
          <el-input class="search-input" size="mini" v-model="formMap.custName"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">收货人</div>
          <el-input class="search-input" size="mini" v-model="formMap.receiverName"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">收货手机</div>
          <el-input class="search-input" size="mini" v-model="formMap.receiverPhone"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">创建时间</div>
          <el-date-picker
            v-model="formMap.startDate"
            type="date"
            placeholder="开始日期" class="search-input" size="mini">
          </el-date-picker>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">至</div>
          <el-date-picker
            v-model="formMap.endDate"
            type="date"
            placeholder="结束日期" class="search-input" size="mini">
          </el-date-picker>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">来电号码</div>
          <el-input class="search-input" size="mini" v-model="formMap.calledNumber"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">客服</div>
          <el-input class="search-input" size="mini" v-model="formMap.salesUserName" icon="search"
                    :on-icon-click="searchSalesUserCondition" readonly
                    @keydown.delete.native="clearProperty('salesUserName','salesUserId')"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">客服分组</div>
          <el-input class="search-input" size="mini" v-model="formMap.salesGroupName"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">店铺</div>
          <el-input class="search-input" size="mini" v-model="formMap.shopName"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">呼叫类型</div>
          <el-input class="search-input" size="mini" v-model="formMap.callInType"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">创建人</div>
          <el-input class="search-input" size="mini" v-model="formMap.creatorName" icon="search"
                    :on-icon-click="searchCreatorCondition" readonly
                    @keydown.delete.native="clearProperty('creatorName','creatorId')"
          ></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">标记人</div>
          <el-input class="search-input" size="mini" v-model="formMap.markedUserName" icon="search"
                    :on-icon-click="searchMarkedUserCondition" readonly
                    @keydown.delete.native="clearProperty('markedUserName','markedUserId')"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">合并单号</div>
          <el-input class="search-input" size="mini" v-model="formMap.mergeTradeNo"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">来电类型</div>
          <el-select v-model="formMap.callingType" placeholder="请选择" size="mini" class="search-input">
            <el-option
              v-for="item in callingType"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">归属</div>
          <el-select v-model="formMap.orderOwnerType" placeholder="请选择" size="mini" class="search-input">
            <el-option
              v-for="item in orderOwnerType"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
         <div class="form-inline">
          <div class="form-inline search-label">工单号</div>
          <el-input class="search-input" size="mini" v-model="formMap.jobNumber"></el-input>
        </div>
      </el-form>
    </div>
    <div class="form-action">
      <el-button type='primary' size="mini" @click="search()">查询</el-button>
      <el-button type='primary' size="mini" @click="resetForm('form')">重置</el-button>
      <el-button type='primary' size="mini" @click="exportOrderMsg()">导出</el-button>
    </div>
  </div>
</template>

<script>
  export default {
    props: ['params'],
    data() {
      var _this = this;
      return {
        formMap: {
          custName: '',
          receiverName: '',
          receiverPhone: '',
          startDate: '',
          endDate: '',
          calledNumber: '',
          salesUserName: '',
          salesUserId: '',
          salesGroupName: '',
          shopName: '',
          callInType: '',
          creatorName: '',
          creatorId: null,
          markedUserName: '',
          markedUserId: null,
          mergeTradeNo: '',
          callingType: '',
          orderOwnerType: '',
          jobNumber:''
        },
        callingType: [
          {
            value: '购买咨询',
            label: '购买咨询',
          }, {
            value: '发货截货',
            label: '发货截货',
          }, {
            value: '物流查询',
            label: '物流查询',
          }, {
            value: '售后问题',
            label: '售后问题',
          }, {
            value: '其他',
            label: '其他',
          }, {
            value: '预约到店',
            label: '预约到店',
          }, {
            value: '虚假签收',
            label: '虚假签收',
          }, {
            value: '话务投诉',
            label: '话务投诉',
          }],
        orderOwnerType: [
          {value: '咨询', label: '咨询'},
          {value: '售前', label: '售前'},
          {value: '售中', label: '售中'},
          {value: '售后', label: '售后'},
          {value: '展厅', label: '展厅'},
          {value: '三包', label: '三包'},
          {value: '经销', label: '经销'},
          {value: '话务', label: '话务'},
          {value: '线上和线下', label: '线上和线下'},
          {value: '承包门店', label: '承包门店'},
          {value: '外包售前', label: '外包售前'},
        ],
        value: '',
      };
    },
    methods: {
      resetForm(formName) {
        let xx = this.formMap;
        for (let key in xx) {
          xx[key] = '';
        }
      },
      setPhone(phone) {
        this.formMap['calledNumber'] = phone;
      },
      getCondition() {
        return this.formMap;
      },
      search() {
        this.$emit('handle-search');
      },
      exportOrderMsg() {
        this.$emit('handle-exprot');
      },
      clearProperty(...properties) {
        properties.forEach(key => {
          this.formMap[key] = null;
        });
      },
      searchSalesUserCondition() {
        var _this = this;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/system_management/list.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            // status: 1,//生效人员
            isEnable: 1,//生效时间
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;
              _this.formMap.salesUserName = data.fullName;
              _this.formMap.salesUserId = data.id;
            },
            callback_two:true,
            callbacktwo(self){
              _this.changeSearchParamsByChoiceUser(self)
            }
          },
        });
      },
      searchMarkedUserCondition() {
        var _this = this;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/system_management/list.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            status: 1,//生效人员
            isEnable: 1,//生效时间
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;
              _this.formMap.markedUserName = data.fullName;
              _this.formMap.markedUserId = data.id;
            },
            callback_two:true,
            callbacktwo(self){
              _this.changeSearchParamsByChoiceUser(self)
            }
          },
        });
      },
      searchCreatorCondition() {
        var _this = this;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/system_management/list.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            status: 1,//生效人员
            isEnable: 1,//生效时间
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;
              _this.formMap.creatorName = data.fullName;
              _this.formMap.creatorId = data.id;

            },
            callback_two:true,
            callbacktwo(self){
              _this.changeSearchParamsByChoiceUser(self)
            }
          },
        });
      },
      // 修改选择用户信息弹窗的查询参数
      changeSearchParamsByChoiceUser(self){
        self.$refs.personList.$refs.xptSearchEx.filterFields =
          fields => {
            let oldItem = fields[2];
            fields.splice(2,1);
            fields.unshift(oldItem);
            return fields;
          };
        self.$refs.personList.$refs.xptSearchEx.watchwhere =
          where => {
            where = where.map((item,index) => {
              if(index == 0){
                item.operator = '%'
              };
              return item;
            })
            return where;
          };
      },
    },
    mounted: function() {
      // _this.getForcedUnlock();
      // _this.updateUserOrder();
      // _this.searching();
      // 检测新增、编辑数据，刷新
      let _this = this;
      _this.$root.eventHandle.$on('refresh_list', function() {
        // _this.searching();
      });
    },
    destroyed() {
      this.$root.offEvents('refresh_list');
    },
    watch: {
      btns() {
        let _this = this;
        _this.getBtnPower();
      },
    },
  };
</script>
<style scoped>
  .search-work {
    background: #eee;
    display: flex;
    /*margin: 0px -20px 0px -20px;*/
    padding-right: 85px;
    position: relative;
  }

  .form-action {
    /*margin: auto;*/
    /*min-width: 250px;*/
    position: absolute;
    right: 30px;
    top: 10px;
  }

  .form-action button {
    display: block;
    margin: 0 5px 10px 0;
  }

  .form-box {
    position: relative;
    margin: 10px;
    flex: 1;
  }

  .form-inline {
    display: inline-block;
  }

  .search-label {
    width: 75px;
    text-align: center;
  }

  .search-input {
    margin-bottom: 3px;
    width: 120px;
  }
</style>
