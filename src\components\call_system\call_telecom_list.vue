<!-- 新呼叫记录-电信 -->
<template>
  <div class="xpt-flex">
    <el-row :gutter="10" class="xpt-top">
      <el-form ref="query" :rules="rules" :model="query" label-position="right" label-width="130px">
        <el-col :span="20" class="search-box">
          <el-form-item label="手机号：" prop="customerNumber">
            <xpt-input v-model="query.customerNumber" size="mini"></xpt-input>
          </el-form-item>
          <el-form-item label="单据编号：" prop="externalNo">
            <xpt-input v-model="query.externalNo" size="mini"></xpt-input>
          </el-form-item>

        </el-col>

        <el-col :span="4" class="search-btn">
          <el-button type="success" size="mini" @click="queryData" :disabled="isLoading"
            :loading="isLoading">查询</el-button>
          <el-button type="primary" size="mini" @click="reset">重置查询条件</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list :showHead="false" :data="list" :colData="cols" :pageTotal="count" selection="" :orderNo="true"
      @page-size-change="pageSizeChange" @current-page-change="currentPageChange">
      <template slot="recordUrl" slot-scope="scope">
        <el-button type="primary" size="mini" @click="viewFile(scope.row)" v-if="scope.row.recordUrl">播放</el-button>
      </template>
    </xpt-list>
  </div>
</template>
<script>
import validate from '@common/validate.js'
import Fn from '@common/Fn.js'
export default {
  props: ["params"],
  data() {
    const self = this;
    return {
      query: {
        current: 1,
        size: 50,
        customerNumber: '',
        externalNo: '',
      },
      // 查询按钮状态
      isLoading: false,
      rules: {
        customerNumber: validate.mobile({
          self: self,
          required: false
        }),
      },
      count: 0,
      list: [],
      cols: [
        {
          label: "电话号码",
          prop: "customerNumber",
          minWidth: 100,
        },
        {
          label: "呼叫方向",
          prop: "callType",
          width: 80,
          formatter: prop =>
          ({
            INCOMING_CALL: "呼入",
            BREATHE: "呼出",
          }[prop] || prop)
        },
        {
          label: "业务单据号",
          prop: "externalNo",
          minWidth: 200,
        },
        {
          label: "是否接通",
          prop: "callResult",
          width: 80,
          formatter: prop => prop?'未接通':'接通'
        },
        {
          label: "来电时间",
          prop: "startTime",
          minWidth: 140,
          format: "dataFormat1"
        },
        {
          label: "结束时间",
          prop: "endTime",
          minWidth: 140,
          format: "dataFormat1"
        },

        {
          label: "振铃时长(秒)",
          prop: "alertDuration",
          width: 160,
        },
        {
          label: "排队时长(秒)",
          prop: "accumQueuesWithoutAgentDuration",
          width: 160,
        },
        {
          label: "通话总时长(秒)",
          prop: "totalDuration",
          width: 120,
        },
        {
          label: "挂断方",
          prop: "hangupDisposition",
          width: 80,
          formatter: prop =>
          ({
            SEAT_SIDE: "坐席侧",
            CLIENT_SIDE: "客户侧",
          }[prop] || prop)
        },
        {
          label: "满意度结果",
          prop: "satisfyResult",
          width: 100
        },
        {
          label: "坐席",
          prop: "belongAgentName",
          width: 120
        },
        {
          label: "录音",
          prop: "recordUrl",
          width: 80,
          slot: 'recordUrl'
        },

      ],
    };
  },
  methods: {
    viewFile(row) {
      window.open(row.recordUrl);
    },
    // 查看详情
    reset() {
      this.query.customerNumber = '';
      this.query.externalNo = '';
      this.query.size = 50;
      this.query.current = 1;
    },
    pageSizeChange(ps) {
      this.query.size = ps;
      this.queryData();
    },
    currentPageChange(page) {
      this.query.current = page;
      this.queryData();
    },
    queryData() {
      if (!this.query.customerNumber && !this.query.externalNo) {
        this.$message.warning("请输入手机号或单据编号")
        return
      }
      this.$refs.query.validate((valid) => {
        if (valid) {
          let data = this.query;
          for (let key in data) {
            if (data[key] === '') {
              delete data[key];
            }
          }
          this.isLoading = true;
          this.ajax.postStream(
            "/order-web/api/callCenter/page",
            data,
            (res) => {
              this.isLoading = false;
              console.log('res', res.body.content)
              if (res.body.content && res.body.content.list) {
                this.list = res.body.content.list || [];
                this.count = res.body.content.count || 0;
                this.$message.success(res.body.msg || '查询成功');
              }
            },
            (err) => {
              this.$message.error(err);
              this.isLoading = false;
            }
          );
        }
      });
    },
  },
};
</script>
<style type="text/css" scoped>
.search-btn {
  display: flex;
  justify-content: flex-end;
  padding: 10px !important;
}

.search-box {
  display: flex;
  justify-content: flex-start;
  padding: 10px !important;
}
</style>


