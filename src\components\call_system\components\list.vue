<!--问题处理人分组列表-->
<template>
	<xpt-list
		ref='personList' 
		:data='userlist' 
		:btns='btns'
		:colData='colData' 
		:selection='selection' 
		:pageTotal='count' 
		:searchPage='search.page_name' 
		:isNeedClickEvent="true"
		@row-dblclick='rowDblclick' 
		@search-click='searchCondition' 
		@selection-change='select' 
		@radio-change='handleCurrentChange' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange' 
	></xpt-list>
</template>
<script>
	export default {
        props:['params'],
		data(){
			let self = this
			return {
				search:{
                    page:{
                        length: self.pageSize,
                        pageNo:1
					},
					page_name: 'cloud_user_login',
					where: [],
					isEnable:1
                    // status:'',		//状态是否生效,弹窗查询
					// isEnable:''		//时间段是否生效，弹窗查询
					
				},
				userlist:[],
                operationList:{},//批量操作的数据
                count:0,
				isAlert:false,
				form:'',//哪个页面进入的弹窗组件
				popSelectData:'',
				popPersonId:'',//是否有从其它页面带过来ID
				btns: [{
					type: 'primary',
					txt: '新增',
					click(){
						self.addPerson()
					}
				}, {
					type: 'success',
					txt: '生效',
					click(){
						self.changePersonalPower(1)
					}
				}, {
					type: 'warning',
					txt: '失效',
					click(){
						self.changePersonalPower(0)
					}
				}],
				colData: [{
					label: '姓名',
					prop: 'realName',
					redirectClick(d){
						self.editPerson(d)
					}
				}, {
					label: '昵称',
					prop: 'nickName'
				}, {
					label: '类型',
					prop: 'type',
					format: 'auxFormat',
					formatParams: 'personelType'
				}, {
					label: '工号',
					prop: 'employeeNumber'
				}, {
					label: '是否生效',
					prop: 'status',
					format: 'statusFilter'
				}, {
					label: '生效时间',
					prop: 'enableTime',
					format: 'dataFormat'
				}, {
					label: '失效时间',
					prop: 'disableTime',
					format: 'dataFormat'
				}, {
					label: '备注',
					prop: 'remark'
				}],
				selection: 'checkbox'
			}
		},
		methods:{
			/**
			*双击关闭弹窗
			*/
			rowDblclick(){
				this.closeAlert();
			},
			sizeChange(size){
                // 每页加载数据
                this.search.page.length = size;
                this.search.page.pageNo = 1;
                this.getPersonList();
            },
            pageChange(page_no){
                // 页数改变
                this.search.page.pageNo = page_no;
				this.getPersonList();
            },
            closeAlert(){
                // 关闭弹窗
                if(this.isAlert){
                    var triggerFn = this.form == 'proxy'?'getProxInfo':this.form == 'user'?'getLelationPerson':'';
                    if(!this.params.showAllPerson){//showAllPerson为true，则允许选择已失效人员
						if(!this.popSelectData || !this.popSelectData.status){
						    this.$message({
						        type:'error',
								message:!this.popSelectData?'请选择人员':'该人员已失效，请重新选择'
							})
							return;
						}
                    }
                    this.params.callback({data:this.popSelectData});
                    this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
                }
            },
            handleCurrentChange(val){
                if(!this.isAlert) return;
                this.popSelectData = val;
			},
			getPersonList(resolve){//获取人员列表
                var url = '/user-web/api/userLogin/getUserLoginList';
                var submitData = {};
				//TODO,多条件搜索的问题
                var data = this.search;
                // console.log(data)
                for(var key in data){
                	// console.log(key, data.hasOwnProperty(key) && data[key] !== '')
                    if(data.hasOwnProperty(key) && data[key] !== ''){
                        submitData[key] = data[key];
                    }
                }
                // console.log(submitData)
				var _this = this;
                this.ajax.postStream(url,submitData,function(d){
					var data = d.body;
					if(data.result){
                        _this.userlist = data.content.list || [];
                        _this.count = parseFloat(data.content.count);
						_this.clearSelectedData();
					} else {
						_this.$message.error(data.msg || '');
					}
					resolve && resolve();
                },function(data){
                    console.log('失败的回调函数')
                    resolve && resolve();
                })
			},
            reset(){
			  //重置查找条件
				var data = this.search;
				for(var key in data){
				    if(data.hasOwnProperty(key)){
				        data[key] = '';
					}
				}
				data.search = 'GENERAL';
			},
            addPerson(){
              //新增人员列表
                this.$root.eventHandle.$emit('creatTab',{name:'新增人员',params:{},component:()=>import('@components/personel/personel')});
			},
            editPerson(row){//跳转人员信息编辑页面
                if(this.isAlert) return;
                this.$root.eventHandle.$emit('creatTab',{name:'编辑人员信息',params:{id:row.id},component:()=>import('@components/personel/personel')});
			},
			resetSearchData(bool){//bool,是要加载首页的数据还是加载当前页的数据，为true时，加载首页，false则为当前页面
                bool?this.search.page.pageNo = 1:'';
			},
            searchCondition(txt, resolve){
            	this.search.where = txt
				this.resetSearchData(!0);
				this.getPersonList(resolve);
			},
            select(selection){//发生改变时触发
                if(this.isAlert) return;
                var i = selection.length;
                this.operationList = {};
                if(!i)  return;
				var a = 0;
                for(;a < i; a++){
                    var data = selection[a];
                    this.operationList[data.id] = data;
				}
                //this.operationList = selection.length?selection:[];
			},
			isEmptyObject(data){
              for(var key in data){
                  return false;
			  }
			  return true;
			},
            changePersonalPower(status){//改变人员生效失效的状态
				var data = this.operationList;
                var _this = this;
				if(_this.isEmptyObject(data)){
                    _this.$message({
                        message: '请选择人员信息',
                        type:'error'
                    });
                    return;
				}
				var idList = [];
				for(var key in data){
					if(data[key].status == status) continue;
				    idList.push(key);
				}
				if(!idList.length){
					_this.$message({
                        message: status?'所选人员已经是生效状态，无需再次进行修改':'所选人员已经是失效状态，无需再次进行修改',
                        type:'error'
                    });
					return;
				}
				var data = {
                    idList : idList,
					status:status
				};
                this.ajax.postStream('/user-web/api/userPerson/updateUserPersonStatusBatch',data,function(data){
					_this.$message({
                        message: data.body.msg,
                        type:data.body.result?'success':'error'
                    });
					if(data.body.result){
                        _this.resetSearchData(!1);
                        _this.getPersonList();//重新拿一遍数据,
					}
                },function(data){
                    //
                    console.log('失败的回调函数')
                })
			},
			selectedPopData(){
				if(!this.popPersonId || !this.userlist || !this.userlist.length || this.isAlert) return;
				var i = this.userlist.length,
					a = 0;
				for(;a < i;a++){
					if(this.popPersonId == this.userlist[a].id){
					    this.popSelectData = this.userlist[a];
					    break;
					}
				}
			},
            initData(){
                //初使化数据
                var data = this.params || {};
                if(!data.isAlert) return
                this.isAlert = data.isAlert?true:false;
                this.form = data.form?data.form:'proxy';//默认是代理
				data.personId?this.popPersonId = data.personId:'';//从其它页面上带过来选中的人员ID
				var searchData = this.search;
				 for(var key in searchData){
					 if(searchData.hasOwnProperty(key) && 'page' != key){
                         searchData[key] = data[key];
					 }
				 }
				 let self = this
				 this.search.page_name = 'cloud_user_login'
				 this.search.where = []
				 if (this.isAlert) {
				 	this.btns = [{
				 		type: 'primary',
				 		txt: '确认',
				 		click(){
				 			self.closeAlert()
				 		}
				 	}]
				 	this.selection = 'radio'
				 }
			},
            /**
             * 清空批量选择的数据
             * **/
            clearSelectedData(){
                this.operationList = {};
                this.popSelectData = null;
                this.$refs.userlist && this.$refs.userlist.clearSelection();
            }
		},
		mounted() {
			//从其它组件里传过来的参数，调用接口
			var _this = this;
            this.initData();
            this.getPersonList();
            this.$root.eventHandle.$on('updataPsersonList',function(){
                _this.resetSearchData(!0);
                _this.getPersonList();
            });
		},
		watch:{
            userlist:{
                handler(curVal,oldVal){
                    this.selectedPopData();
                },
                deep:true
			}
		},
        destroyed(){
			this.$root.offEvents('updataPsersonList');
        }
	}
</script>
