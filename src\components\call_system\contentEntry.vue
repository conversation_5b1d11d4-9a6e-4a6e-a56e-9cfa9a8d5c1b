<template>
  <div class="content-entry">
    <el-input type="textarea" :rows="8" placeholder="请输入内容" v-model="testArea" @blur="blurInput" :disabled="job.isApproved==1 || job.isInvalid==1"></el-input>
  </div>
</template>

<script>
  export default {
    name:'contentEntry',
    props:["job"],
    data () {
      return {
        testArea: ''
      }
    },
    methods: {
      blurInput() {
        this.$emit('updateContent', this.testArea)
      },
      setContent(content){
        this.testArea = content;
      }
    },
  }
</script>

<style scoped>
  .content-entry{
    width:100%;
    height:88%;
  }
  .el-textarea__inner{
    border-radius: 0!important;
    height: 100%!important;
  }
  .content-entry .el-textarea {
    height: 100%!important;
  }
</style>
