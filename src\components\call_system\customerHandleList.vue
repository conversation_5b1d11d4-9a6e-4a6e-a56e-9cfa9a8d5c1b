<!--人员信息列表-->
<template>
  <xpt-list
    ref="list"
    :data="userlist"
    :btns="btns"
    :colData="colData"
    :selection="selection"
    :pageTotal="pageTotal"
    :searchPage="search.page_name"
    :isNeedClickEvent="true"
    @row-dblclick="rowDblclick"
    @search-click="handleSearch"
    @selection-change="handleSelect"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    :showSelectRowNumByOnlyKey="true"
    :showCount="showCount"
    @count-off="countOff"
  ></xpt-list>
</template>
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      search: {
        page: {
          length: self.pageSize,
          pageNo: 1,
        },
        page_name: "cloud_user_login_group",
        where: [],
        isEnable: 1,
      },
      userlist: [],
      operationList: [],
      pageTotal: 0,
      btns: [
        {
          type: "primary",
          txt: "确认",
          click() {
            self.closeAlert();
          },
        },
      ],
      colData: [
        {
          label: "姓名",
          prop: "realName",
        },
        {
          label: "昵称",
          prop: "nickName",
        },
        {
          label: "类型",
          prop: "type",
          format: "auxFormat",
          formatParams: "personelType",
        },
        {
          label: "工号",
          prop: "employeeNumber",
        },
        {
          label: "是否生效",
          prop: "status",
          format: "statusFilter",
        },
        {
          label: "生效时间",
          prop: "enableTime",
          format: "dataFormat",
        },
        {
          label: "失效时间",
          prop: "disableTime",
          format: "dataFormat",
        },
        {
          label: "备注",
          prop: "remark",
        },
      ],
      selection: "checkbox",
      showCount: false, //显示总数
      countShowLoading: false, //显示总数请求中
    };
  },
  methods: {
    /**
     *双击关闭弹窗
     */
    rowDblclick() {
      this.closeAlert();
    },
    sizeChange(size) {
      // 每页加载数据
      this.search.page.length = size;
      this.search.page.pageNo = 1;
      this.$refs.list.pageNow = 1;
      this.getPersonList();
    },
    pageChange(page_no) {
      // 页数改变
      this.search.page.pageNo = page_no;
      this.getPersonList();
    },
    closeAlert() {
      // 关闭弹窗
      this.params.callback({ data: this.operationList });
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    getPersonList(resolve) {
      //获取人员列表
      let url = "/user-web/api/userLogin/getUserWithGroupList";
      let submitData = {};
      let data = this.search;
      for (let key in data) {
        if (data.hasOwnProperty(key) && data[key] !== "") {
          submitData[key] = data[key];
        }
      }
      this.ajax.postStream(
        url,
        submitData,
        (res) => {
          let data = res.body;
          if (data.result) {
            let list = data.content.list || [];
            this.userlist =
              list.length == this.search.page.length + 1
                ? data.content.list.slice(0, -1)
                : list.slice(0);

            if (!this.showCount) {
              let totalCount =
                this.search.page.pageNo * this.search.page.length;
              this.pageTotal =
                res.body.content.count == this.search.page.length + 1
                  ? totalCount + 1
                  : totalCount;
            }
            this.clearSelectedData();
          } else {
            this.$message.error(data.msg || "");
          }
          resolve && resolve();
        },
        () => {
          resolve && resolve();
        }
      );
    },
    countOff() {
      let self = this,
        url = "/user-web/api/userLogin/getUserWithGroupListCount";
      if (!self.userlist.length) {
        self.$message.error("当前列表为空，先搜索内容");
        return;
      }
      if (!!self.countShowLoading) {
        self.$message.error("请勿重复点击");
        return;
      }
      self.countShowLoading = true;

      self.ajax.postStream(url, self.search, function (response) {
        if (response.body.result) {
          self.pageTotal = response.body.content;
          self.showCount = true;
          self.countShowLoading = false;
        } else {
          self.$message.error(response.body.msg);
        }
      });
    },
    handleSearch(txt, resolve) {
      this.showCount = false;
      this.search.page.pageNo = 1;
	  this.$refs.list.pageNow = 1;
      this.search.where = txt;
      this.getPersonList(resolve);
    },
    handleSelect(selection) {
      //列表多选
      this.operationList = selection || [];
    },
    initData() {
      //初使化数据
      let data = this.params || {};
      let searchData = this.search;
      for (let key in searchData) {
        if (searchData.hasOwnProperty(key) && "page" != key) {
          searchData[key] = data[key];
        }
      }
      this.search.page_name = "cloud_user_login_group";
      this.search.where = [];
    },
    /**
     * 清空批量选择的数据
     * **/
    clearSelectedData() {
      this.operationList = [];
      this.$refs.list && this.$refs.list.clearSelection();
    },
  },
  mounted() {
    this.initData();
    this.getPersonList();
  },
};
</script>
