<!-- 呼叫中心 - 员工 - 员工设置列表 -->
<template>
	<xpt-list
		ref="employeeSetting"
		selection="checkbox"
		:orderNo="true"
		:btns="btns"
		:colData="cols"
		:data="tableData"
		:pageTotal="totalPage"
		:pageLength="search.page_size"
		:searchPage="search.page_name"
		@selection-change="selects"
		@search-click="searchClick"
		@page-size-change="pageSizeChange"
		@current-page-change="pageChange"
	>
	<xpt-import slot='btns' :taskUrl='uploadUrl' :callback="callback" class='mgl10' :isGet="true"></xpt-import>
	</xpt-list>
</template>

<script>
import { apiUrl } from "./base.js";
export default {
	props: ["params"],
	data() {
		let self = this;
		return {
			uploadUrl:'/callcenter-web/personGroup/import.do',
			
			btns: [
				{
					type: "success",
					txt: "刷新",
					loading: false,
					click() {
						self.getList();
					}
				},
				{
					type: "primary",
					txt: "新增",
					loading: false,
					click() {
						self.addOne();
					}
				},
				// {
				// 	type: "primary",
				// 	txt: "修改",
				// 	loading: false,
				// 	click() {
				// 		self.editOne();
				// 	}
				// },
				{
					type: "danger",
					txt: "删除",
					loading: false,
					click() {
						self.delChoice();
					}
				},
			],
			cols: [
				{
					label: "工号",
					prop: "staffNo",
					redirectClick(row) {
						self.turnOnSettingDetail(row);
					}
				},
				{
					label: "坐席号", 
					prop: "agentId"
				},
				{
					label: "话务昵称",
					prop: "staffName"
				},
				{
					label: "话务分组",
					prop: "groupName"
				},
				{
					label: "在职状态",
					prop: "dimissionStatus"
				},
				{
					label: "有无固话",
					prop: "curingState",
					formatter: prop =>
						({
							0: "有",
							1: "无"
						}[prop] || prop)
				},
        {
          label: "内网专线",
          prop: "isIntranet",
          formatter: prop =>
            ({
              Y: "是",
              N: "否"
            }[prop] || prop)
        },
				{
					label: "创建人",
					prop: "creatorName"
				},
				{
					label: "创建时间",
					prop: "creatorTime"
				},
				{
					label: "修改人",
					prop: "modifierName"
				},
				{
					label: "修改时间",
					prop: "modifierTime"
				},
				{
					label: "话务主管",
					prop: "isChief",
					formatter: prop =>
						({
							Y: "是",
							N: "否"
						}[prop] || prop)
				}
			],
			tableData: [],
			totalPage: 0,
			selectsData: [],
			search: {
				page_no: 1,
				page_size: 200,
				page_name: "scm_person_group",
				where: []
			}
		};
	},
	watch: {},
	methods: {
		callback(res){
			console.log(res);
			let msg = ''
			if(res.body.result){
				if(res.body.content.length){
					msg = JSON.stringify(res.body.content);
					this.$message({
							message:msg,
							type:'error'
						});
				}else{
					this.$message({
								message:res.body.msg,
								type:'success'
							});
				}
			}
		},
		// 获取列表
		getList(resolve) {
			this.btns[0].loading = true;
			let params = {
				page: {
					length: this.search.page_size,
					pageNo: this.search.page_no
				},
				page_name: this.search.page_name,
				staffNo: "",
				staffName: "",
				groupName: "",
				where: this.search.where || []
			};
			this.$http
				.post(apiUrl.personGroup_list_new, params)
				.then(res => {
					if (res.data.code == 0) {
						this.totalPage = res.data.totalNum;
						this.tableData = res.data.content;
					} else if (res.data.code != 0) {
						this.$message.error(res.data.msg || "");
					}
				})
				.catch(err => {
					this.$message.error(`${err.status}` || "");
				})
				.finally(() => {
					resolve && resolve();
					this.btns[0].loading = false;
				});
		},
		// 修改一条数据 - new
		turnOnSettingDetail(row) {
			const self = this;
			let createTabOptions = {
				name: "修改员工信息",
				params: {
					staffInfo: row
				},
				component: () => import("@components/call_system/employee_setting_detail.vue")
			};
			this.$root.eventHandle.$emit("creatTab", createTabOptions);

			let oldCloseFun = createTabOptions.params.__close;
			createTabOptions.params.__close = refreshSuccess => {
				if (refreshSuccess) {
					self.getList();
				}
				oldCloseFun();
			};
		},
		// 新增一条数据
		addOne() {
			const self = this;
			let createTabOptions = {
				name: "新增员工信息",
				component: () => import("@components/call_system/employee_setting_detail.vue")
			};
			this.$root.eventHandle.$emit("creatTab", createTabOptions);

			let oldCloseFun = createTabOptions.params.__close;
			createTabOptions.params.__close = refreshSuccess => {
				if (refreshSuccess) {
					self.getList();
				}
				oldCloseFun();
			};
		},
		/** // 修改一条数据
      editOne() {
        if (this.selectsData.length !== 1) {
          this.$message.error("请选择一个条目");
          return;
        }
        const self = this;
        let row = this.selectsData[0];
        this.$root.eventHandle.$emit("alert", {
          component: () => import("@components/call_system/employee_setting_detail.vue"),
          style: "width:400px;height:300px",
          title: "修改设置",
          params: {
            staffInfo: row,
            callback: () => {
              self.getList();
            }
          }
        });
      },
    */
		// 删除选中的数据
		delChoice() {
			if (this.selectsData.length < 1) {
				this.$message.warning("请至少选择一个条目");
				return;
			}
			this.$confirm("确认删除?", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			})
				.then(() => {
					this.deleteItems();
				})
				.catch(() => {});
		},
		deleteItems() {
			this.btns[2].loading = true;
			let ids = this.selectsData.map(item => item.id);
			this.$http
				.post(apiUrl.personGroup_delete, { ids: ids })
				.then(res => {
					if (res.data.code != 0) {
						this.$message.error(res.data.msg || "");
					} else {
						this.$message.success("删除成功");
						this.getList();
					}
				})
				.catch(err => {
					this.$message.error("删除失败", err);
				})
				.finally(() => {
					this.btns[2].loading = false;
				});
		},
		// 选中数据
		selects(data) {
			this.selectsData = data;
		},
		// 查询数据
		searchClick(obj, resolve) {
			this.search.where = obj;
			this.getList(resolve);
		},
		//单页条数
		pageSizeChange(size) {
			this.search.page_size = size;
			this.getList();
		},
		//换页
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		}
	},
	mounted() {
		this.getList();
	}
};
</script>

<style>
</style>
