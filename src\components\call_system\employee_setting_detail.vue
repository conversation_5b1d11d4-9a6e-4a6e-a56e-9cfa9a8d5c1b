<template>
  <div class="employee_setting_detail" v-loading="loading">
    <el-row class='xpt-top calltop' :gutter="40">
      <el-col :span="24">
        <el-button size='mini' type="primary" @click='saveDetail'>保存</el-button>
        <el-button size='mini' type="danger" @click='closeDialog'>关闭</el-button>
      </el-col>
    </el-row>
    <el-row class='call-bottom' :gutter="40">
      <el-form ref="form" :model="formData" label-width="120px" >
        <el-form-item label="工号" prop="staffNo" required style='height:40px'>
          <el-input size="mini" v-model="formData.staffNo" icon="search" placeholder="请选择话务"
                    :on-icon-click="searchOperatedUserCondition" readonly :disabled="editorMode"/>
        </el-form-item>

        <el-form-item label="坐席号" prop="agentId" required style='height:40px'>
          <el-input size="mini" v-model="formData.agentId"  placeholder="填写呼叫中心账号"   />
        </el-form-item>

        <el-form-item label="话务昵称" prop="staffName" required style='height:40px'>
          <el-input size="mini" v-model="formData.staffName" placeholder="请选择话务" readonly :disabled="editorMode"/>
        </el-form-item>
        <el-form-item label="话务分组" prop="groupName" required style='height:40px'>
          <el-input size="mini" v-model="formData.groupName" placeholder="请选择分组" icon="search"
                    :on-icon-click="selectGroup" readonly/>
        </el-form-item>
        <el-form-item label="在职状态" prop="status" style='height:40px'>
          <el-input size="mini" v-model="_staffStatus" disabled/>
        </el-form-item>
        <el-form-item label="有无固话" style='height:40px'>
          <el-radio-group v-model="formData.curingState">
              <el-radio :label="0">有</el-radio>
              <el-radio :label="1">无</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="话务主管" style='height:40px'>
          <el-radio-group v-model="formData.isChief">
              <el-radio label="Y">是</el-radio>
              <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="内网专线" style='height:40px'>
          <el-radio-group v-model="formData.isIntranet">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
         <el-form-item label="是否全局" style='height:40px'>
          <el-radio-group v-model="formData.isAllData">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import {apiUrl} from './base.js';

  export default {
    name: 'employee_setting_detail',
    props: ['params'],
    data() {
      return {
        loading: false,
        formData: {
          isAllData:'',
          id: '',
          staffNo: '',
          agentId: '',
          staffName: '',          
          staffId: '',
          groupId: '',
          groupName: '',
          status: '',
          curingState:0, //默认有固话
          isChief:'N', //默认不是话务主管
          isIntranet:'Y', //默认在内网专线
        },
        formRule: {
          staffNo: [
            {required: true},
          ],
          staffName: [
            {required: true},
          ],
          groupName: [
            {required: true},
          ],
          agentId: [
            {required: true},
          ],
        },
      };
    },
    computed: {
      editorMode() {
        return !!this.params.staffInfo;
      },
      _staffStatus() {
        switch (this.formData.status) {
          default:
            return '';
          case 'WORKING_STAFF':
            return '在职';
          case 'JOB_TRANSFER':
            return '转岗';
          case 'LEAVE_OFFICE':
            return '离职';
        }
      },
    },
    methods: {
      closeDialog() {
        this.$root.eventHandle.$emit('removeTab',this.params.tabName);
      },
      searchOperatedUserCondition() {
        if (this.editorMode) {
          return;
        }
        var _this = this;
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/system_management/list.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            // status: 1,//生效人员
            isEnable: 1,//生效时间
            showAllPerson: true,
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;
              _this.formData.staffName = data.fullName;
              _this.formData.staffNo = data.employeeNumber;
              _this.formData.staffId = data.id;
              _this.loadUserIncumbency(data.employeeNumber);
            },
          },
        });
      },
      selectGroup() {
        var _this = this;
        // 打开分组选择列表的时候传参，给分组列表添加查询参数
        let addParams = [
          {
            name:'type',
            value:'TRAFFIC'
          }
        ]
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/per_sales_report/select_group.vue'), close: function() {

          },
          style: 'width:900px;height:600px',
          title: '选择分组',
          params: {
            isAddParams:true,
            notNeedReportEmbody:true,
            addParams,
            callback(b) {
              _this.formData.groupId = b.id;
              _this.formData.groupName = b.name;
            },
          },
        });

      },
      async saveDetail() {
        let valid = await this.verified();
        if (!valid) {
          this.$message.error('输入框填写有误');
          return;
        }
        var agentIdValue =  this.formData['agentId'];
        if(agentIdValue.length > 8){
          this.$message.error('坐席号字段长度不能超过八');
          return;
        }else{
          if("0"== agentIdValue.substring(0,1) ){
            this.$message.error('坐席号首字母不能为0');
            return;
          } 
        }

        this.loading = true;

        let params = {
          isAllData:'Y',
          id: '',
          staffId: '',
          staffNo: '',
          agentId: '',
          staffName: '',
          groupId: '',
          groupName: '',
          curingState: '',
          isChief: '',
          isIntranet:''
        };
        for (let paramsKey in params) {
          params[paramsKey] = this.formData[paramsKey];
        }

        let url = '';
        if (this.editorMode) { // 修改模式
          url = apiUrl.personGroup_update;
        } else { // 新增模式
          url = apiUrl.personGroup_add;
          delete params.id;
        }

        this.$http.post(url, params).then(resp => {
          if (resp.data.code == 0) {
            this.$message.success('保存成功');
            this.params.__close(true);
            // this.params.callback();
            // this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
          } else {
            throw new Error();
          }
        }).catch(error => {
          console.error(error)
          this.$message.error('保存失败');
        }).finally(() => {
          this.loading = false;
        });
      },
      loadUserIncumbency(userId) {
        this.$http.post('/user-web/api/userPerson/getUserPersonList', {
          'page': {'length': 1, 'pageNo': 1},
          'page_name': 'cloud_user_person',
          'where': [
            {
              'field': 'd7f3e4fec7b5536df9c116f78b82356e',
              'table': '44e9610c58ab8c7b3c0c4aa0b7c20436',
              'value': userId,
              'operator': '=',
              'condition': 'AND',
              'listWhere': [],
            }],
        }).then(res => {
          if (res.data && !!res.data.content && res.data.content.count > 0) {
            this.formData.status = res.body.content.list[0].incumbency;
          }
        });
      },
      verified() {
        return new Promise((resolve, reject) => {
          this.$refs.form.validate(valid => {
            resolve(valid);
          });
        });

      },
      queryStaffStatus() {
        // 查询客服在职状态
        this.loading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterPersonIncumbency', this.formData.staffNo,
          {emulateJSON: true}).then(res => {
          this.formData.status = res.data.msg;
        }).catch(err => {
        }).finally(() => {
          this.loading = false;
        });

      },
    },
    mounted() {
      this.$nextTick(() => {
        if (this.editorMode) {
          this.formData.id = this.params.staffInfo.id;
          this.formData.agentId = this.params.staffInfo.agentId;
          this.formData.staffNo = this.params.staffInfo.staffNo;
          this.formData.staffName = this.params.staffInfo.staffName;
          this.formData.groupId = this.params.staffInfo.groupId;
          this.formData.groupName = this.params.staffInfo.groupName;
          this.formData.curingState = this.params.staffInfo.curingState || 0;
          this.formData.isChief = this.params.staffInfo.isChief || 'N';
          this.formData.isIntranet = this.params.staffInfo.isIntranet || 'Y';
          this.formData.isAllData = this.params.staffInfo.isAllData || 'N';
          // 查询客服在职状态
          this.loadUserIncumbency(this.formData.staffNo);
        }
      });
    },
  };
</script>

<style scoped>
.calltop{
  margin-bottom: 20px!important;
  padding-left: 25px!important;
}

.call-bottom{
  padding-left: 25px;
}

</style>

<style>
.employee_setting_detail .el-form-item .el-form-item__label {
    padding: 0 15px 0 10px;
}
</style>
