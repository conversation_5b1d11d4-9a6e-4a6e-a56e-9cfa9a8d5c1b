<template>
  <div class="app-container">
    <xpt-headbar class="header-bar">
      <div slot='left'>
        <el-button size='mini' type="primary" @click='callDialog' :disabled="callOutStatus()">呼出</el-button>
        <el-button size='mini' type="primary" @click='setting' :disabled="btns.shezhi.disable">设置</el-button>
        <el-button size='mini' type="primary" @click='check' :disabled="btns.shenhe.disable  || !isTelTrafficBoss">审核</el-button>
        <el-button size='mini' type="primary" @click='exportOrderMsg' :disabled="!isTelTrafficBoss">导出</el-button>
        <el-button size='mini' type="primary" :disabled="btns.tijiao.disable"
                   @click="searchCreatorCondition">提交
        </el-button>
        <el-button size='mini' type="primary" @click='search'>刷新</el-button>
         <xpt-btngroup
                class="mgl10"
                :btngroup="phoneNumberBtnGroup"
                size="mini"
          ></xpt-btngroup>
      </div>
    </xpt-headbar>
    <select-tab ref="tab" @changeTab="selectedTab" :tabs="tabs" :selectedCode="selectCode"></select-tab>
    <el-form ref="form" class="search-box" :model="formSearch" :inline="true">
      <el-form-item prop="phoneNumber" label="电话号码">
        <el-input class="search-input" size="mini" v-model="formSearch.callNumber"></el-input>
      </el-form-item>
      <el-form-item prop="phoneNumber" label="来电类型" v-show="selectCode=='submitted'||selectCode=='returned'">
        <!-- <el-input class="search-input" size="mini" v-model="formSearch.callinType"></el-input> -->
        <el-select
          v-model="formSearch.callType"
          size="mini"
          placeholder="请选择">
          <el-option
              v-for="(value,key) in callinTypeList"
              :key="key"
              :label="value"
              :value="value">
            </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="phoneNumber" label="来电时间" v-show="selectCode=='submitted'||selectCode=='returned'">
        <!-- <el-input class="search-input" size="mini" v-model="formSearch.callBeginDate"></el-input> -->
        <el-date-picker
          size="mini"
          v-model="formSearch.callBeginDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="phoneNumber" label="回拨时间" v-show="selectCode=='submitted'||selectCode=='returned'">
        <!-- <el-input class="search-input" size="mini" v-model="formSearch.callNumber"></el-input> -->
        <el-date-picker
          size="mini"
          v-model="formSearch.callbackBeginDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="phoneNumber" label="分组" v-show="selectCode=='submitted'||selectCode=='returned'">
        <!-- <el-input class="search-input" size="mini" v-model="formSearch.groupId"></el-input> -->
					<el-input  size="mini"  v-model="formSearch.groupName" icon="search" :on-icon-click="selectGroup" placeholder="请输入分组名"></el-input>
      </el-form-item>
      <el-form-item prop="phoneNumber" label="用户名" v-show="selectCode=='submitted'||selectCode=='returned'">
        <el-input class="search-input" size="mini" v-model="formSearch.managerUser"></el-input>
      </el-form-item>
      <el-form-item prop="phoneNumber" label="回拨时效" v-show="selectCode=='submitted'||selectCode=='returned'">
        <!-- <el-input class="search-input" size="mini" v-model="formSearch.callbackEffectTime"></el-input> -->
        <!-- <el-date-picker
          size="mini"
          v-model="formSearch.callbackEffectTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker> -->
        <el-input class="search-input" size="mini" v-model="formSearch.callbackEffectTimeMin" style="width:50px;"></el-input>至
        <el-input class="search-input" size="mini" v-model="formSearch.callbackEffectTimeMax" style="width:50px;"></el-input>

      </el-form-item>
      <el-form-item>
        <el-button type='primary' size="mini" @click="search()">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type='primary' size="mini" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table ref="table1" v-if="showTable1" :data="tableData" v-show="selectCode=='current'||selectCode=='beforeleak'"
              @selection-change="selectTableRow"
               class="table-view"
              :highlight-current-row="true" v-loading="loading" style="flex-grow: 2;">
      <el-table-column
        type="selection">
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" property="callNumber" label="电话号码"
                       :render-header="renderCalledNumberHeader" :formatter="formatterCalledNumber"/>
      <el-table-column :show-overflow-tooltip="true" property="callType" label="来电类型"
                       :render-header="renderCalledCallType"/>
      <el-table-column :show-overflow-tooltip="true" property="createTime" label="来电时间"
                       :render-header="renderCalledCallTime"/>
      <el-table-column :show-overflow-tooltip="true" property="diffMinute" label="间隔分钟"
                       :render-header="renderCalledDiffMinute"/>
      <el-table-column :show-overflow-tooltip="true" property="missType" label="漏接类型"
                       :render-header="renderCalledMissType"/>
      <el-table-column :show-overflow-tooltip="true" property="processUser" label="最后联系人"
                       :render-header="renderCalledProcessUser"/>
    </el-table>

    <el-table ref="table2" v-if="showTable2" :data="tableData2" class="table-view"
              v-loading="loading"
              v-show="selectCode=='submitted'||selectCode=='returned'"
              :highlight-current-row="true"
              @selection-change="selectTableRow" style="flex-grow: 2;">
      <el-table-column
        type="selection">
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="managerUser" label="负责人"
                      />
      <el-table-column :show-overflow-tooltip="true" prop="callNumber" label="漏接号码"
                        :formatter="formatterCalledNumber"/>
      <el-table-column :show-overflow-tooltip="true" prop="callType" label="来电类型"
                      />
      <el-table-column :show-overflow-tooltip="true" prop="missCallTime" label="来电时间"
                       />
      <el-table-column :show-overflow-tooltip="true" prop="missType" label="漏接类型"
                       />
      <el-table-column :show-overflow-tooltip="true" prop="submitTime" label="提交时间"
                       />
      <el-table-column :show-overflow-tooltip="true" prop="callTimes" label="回拨次数"
      :render-header="renderCalledCallTimes"
                       />
      <el-table-column :show-overflow-tooltip="true" prop="groupName" label="分组"
      />
      <el-table-column :show-overflow-tooltip="true" prop="callbackEffectTime" label="回拨时效"
      />
      <el-table-column :show-overflow-tooltip="true" prop="callbackTime" label="回拨时间"
      />
    </el-table>
    <el-row type="flex" justify="end" style="align-items:center">
      <span style="color: #48576a">筛选后 {{_filterLength}}</span>
      <el-pagination
        layout="total, sizes, prev, pager, next"
        :page-sizes="[200, 400, 600]"
        :page-size="pageSize"
        :total="totalNum"
        :current-page="pageNow"
        @size-change="pageSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </el-row>
    <el-table ref="table3" :data="tableData3" class="table-view"
              v-show="selectCode=='submitted'||selectCode=='returned'"
              v-loading="loading2"
              :highlight-current-row="true" style="flex-grow: 1"
    >
      <el-table-column :show-overflow-tooltip="true" type="index" label="序号"/>
      <el-table-column :show-overflow-tooltip="true" prop="callType" label="呼叫类型"/>
      <el-table-column :show-overflow-tooltip="true" prop="callbackStatus" label="状态"/>
      <el-table-column :show-overflow-tooltip="true" prop="userId" label="工号"/>
      <el-table-column :show-overflow-tooltip="true" prop="userName" label="用户名"/>
      <el-table-column :show-overflow-tooltip="true" prop="beginTime" label="开始时间"/>
      <el-table-column :show-overflow-tooltip="true" prop="endTime" label="结束时间"/>
      <el-table-column :show-overflow-tooltip="true" prop="recordFile" label="录音">
        <template slot-scope="scope">
          <a v-if="scope.row && scope.row.recordFile" target="_blank"
             :href="scope.row.recordFile">播放</a>
        </template>
      </el-table-column>
    </el-table>

    <phone-dial :phone="phone" ref="phoneDial" v-if="showDial" @makeCall="makeCall"
                @close="showDial=false"/>
  </div>
</template>

<script>
  import baseUrl, {apiUrl,EventBus} from './base.js';
  import {makeUrl} from './base';
  import callBtn from '@/components/call_system/callBtn';
  import selectTab from '@/components/call_system/selectTab';
  import PhoneDial from '@/components/call_system//phoneDial';
  import PhoneBar from './phoneBar';
  import userStatus from "./userStatus";
  import Fn from "@common/Fn.js";
  import moment from 'moment'

  export default {
    mixins: [ userStatus ],
    // 从隔很多层的父级组件获取弹窗的状态值
    inject: [ 'diaConfig' ],
    props: ['params'],
    components: {
      callBtn,
      selectTab,
      PhoneDial,
    },
    computed: {
      _filterLength() {
        // 'selectCode==\'current\'||selectCode==\'beforeleak\'';
        // "selectCode=='submitted'||selectCode=='returned'"
        switch (this.selectCode) {
          default:
            return 0;
          case 'current':
          case 'beforeleak':
            return this.tableData.length;
          case 'submitted':
          case 'returned':
            return this.tableData2.length;
        }
      },
      btn() {
        return this.$store.state.common.btn
      }

    },
    data() {
      let self=this;
      return {
        callinTypeList:[
          "呼入","呼出","售后","售后咨询","定制经销商加盟","投诉建议","未知","查询订单","沟通咨询","经销商加盟","订单查询","购买咨询"
        ],
        showDial: false,
        formMap: {
          missId: '',
        },
        phone: '',
        selectRow: [],
        tableData: [],
        tableData2: [],
        tableData3: [],
        tableDataNoFilter: [],
        tabs: [
          {
            name: '漏接来电',
            code: 'current',
            isInvalid: 0,
            isApproved: 0,
          },
          {
            name: '已提交',
            code: 'submitted',
            isInvalid: 0,
            isApproved: 1,
          },
          {
            name: '已回拨',
            code: 'returned',
            isInvalid: 1,
            isApproved: 0,
          },
          {
            name: '以前的漏接',
            code: 'beforeleak',
            isInvalid: 1,
            isApproved: 0,
          },
        ],
        tabTwos: [
          {
            name: '详细信息',
            code: 'detail',
            isInvalid: 1,
            isApproved: '',
          },
        ],
        btns: {
          huchu: {disable: true},
          shezhi: {disable: true},
          shenhe: {disable: true},
          tijiao: {disable: true},
          daochu: {disable: true},
        },
        formSearch: {
          jobNumber: '',
          callNumber: '',
          managerUser: '',
          callinType: '',
          callBeginDate: [],
          callEndDate: '',
          callbackBeginDate: [],
          callbackEndDate: '',
          groupId: '',
          groupName: '',
          callbackEffectTimeMin: '',
          callbackEffectTimeMax: '',
        },
        selectCode: 'current',
        tableOneData: [],
        loading: false,
        loading2: false,
        jobId: '',
        pageSize: 200,
        pageNow: 1,
        totalNum: 0,
        filters: {
          calledNumber: '',
          callTime: '',
          callType: '',
          diffMinute: '',
          missType: '',
          processUser: '',
          // callTimes: '',

        },
        filters2: {
          managerUser: '',
          calledNumber: '',
          submitTime: '',
          callType: '',
          callTime: '',
          missType: '',
        },
        submit: {
          tjData: '',
        },
        phoneNumberBtnGroup:[
						{
							type:'success',
							txt:'显示号码',
							click(){
								self.switchPhoneNumberShow(true)
							},
							isDisabled:self.switchPhoneNumberDisabled
						},{
							type:'warning',
							txt:'隐藏号码',
							click(){
							   self.switchPhoneNumberShow(false)
							},
							isDisabled:self.switchPhoneNumberDisabled
						}
					],
          //查看隐藏号码
        switchPhoneNumberDisabled:false,
        showPhoneNumber:false,
        showTable1:true,
        showTable2:true,
      };
    },
    methods: {
      selectGroup() {
				var _this = this;
				// 打开分组选择列表的时候传参，给分组列表添加查询参数
				let addParams = [
				{
					name:'type',
					value:'TRAFFIC'
				}
				]
				this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/per_sales_report/select_group.vue'), close: function() {

				},
				style: 'width:900px;height:600px',
				title: '选择分组',
				params: {
					isAddParams:true,
					notNeedReportEmbody:true,
					addParams,
					callback(b) {
					_this.formSearch.groupId = b.id;  
					_this.formSearch.groupName = b.name;
					},
				},
				});

			},
      callOutStatus(){
        // console.log('huchu', this.btns.huchu.disable)
        // console.log('callStatus', this.btn.callStatus)
        // console.log('submitted', this.selectCode == 'submitted')
        // console.log('selectRow', this.selectRow.length== 1)
        if(this.selectCode == 'submitted' && this.selectRow.length== 1 && !this.btn.callStatus) {
          return false
        }else {
          return true
        }
      },
      search() {
        this.loading = true;
        this.tableData = [];
        this.tableData2 = [];
        this.tableData3 = [];

        this.switchPhoneNumberShow(false);
        if (this.selectCode == 'current') {
          this.loadCurrentMiss();
        } else if (this.selectCode == 'submitted') {
          this.loadSubmittedMiss();
        } else if (this.selectCode == 'returned') {
          this.loadReturnMiss();
        } else if (this.selectCode == 'beforeleak') {
          this.loadBeforeMiss();
        }
        this.loading = false;
        this.btns.daochu.disable = false;
        this.btns.shenhe.disable = true;
        this.btns.shezhi.disable = true;
        this.btns.tijiao.disable = true;
        this.btns.huchu.disable = true;
        this.selectRow = [];
      },
      clickRowHandler(row, event, column) {
        if (column.label) {
          this.$refs.table1.clearSelection();
          this.$refs.table1.toggleRowSelection(row);
          this.$refs.table2.clearSelection();
          this.$refs.table2.toggleRowSelection(row);
        }
        let params = {
          missId: row.callId,
          pageNo: this.pageNow,
          pageSize: this.pageSize,
        };
        this.loading2 = true;
        this.$http.get(apiUrl.missCall_DetailInfo, {params}).then(resp => {
          if (resp.data.code == 0) {
            this.tableData3 = resp.data.content;
          }
        }).finally(() => {
          this.loading2 = false;
        });
      },
      loadCurrentMiss() {
        this.loading = true;
        console.log(this.formSearch);
        this.$http.post(makeUrl(apiUrl.missCall_CurrentMissCall, {
          pageNo: this.pageNow,
          pageSize: this.pageSize,
          qryType: 0,
          jobNumber: this.formSearch.jobNumber,
          callNumber: this.formSearch.callNumber,
          managerUser: this.formSearch.managerUser,
        })).then(resp => {
          if (resp.status === 200) {
            this.tableData = resp.body.content;
            this.totalNum = resp.body.totalNum;
            this.tableDataNoFilter = this.tableData;
            this.filterData();
          }
        }).finally(() => {
          this.loading = false;
        });
      },
      handleCurrentChange(pageNo) {
        this.pageNow = pageNo;
        this.search();
      },
      loadSubmittedMiss() {
        if((!!this.formSearch.callbackEffectTimeMin && !!this.formSearch.callbackEffectTimeMax && this.formSearch.callbackEffectTimeMax-this.formSearch.callbackEffectTimeMin>30)){
          this.$message.error("回拨时效间隔必须大于30");
          return;
        }
        if(!!this.formSearch.callbackEffectTimeMin && this.formSearch.callbackEffectTimeMin.length>6){
          this.$message.error("回拨时效数值小于7位数");
          return;
        }
        if(!!this.formSearch.callbackEffectTimeMax && this.formSearch.callbackEffectTimeMax.length>6){
          this.$message.error("回拨时效数值小于7位数");
          return;
        }
        this.loading = true;
        
        this.$http.post(apiUrl.missCall_Submitted, {
          pageNo: this.pageNow,
          pageSize: this.pageSize,
          jobNumber: this.formSearch.jobNumber,
          callNumber: this.formSearch.callNumber,
          managerUser: this.formSearch.managerUser,
          callbackEffectTimeMin: this.formSearch.callbackEffectTimeMin,
          callbackEffectTimeMax: this.formSearch.callbackEffectTimeMax,
          groupId: this.formSearch.groupId,
          callinType: this.formSearch.callinType,
          callbackBeginDate: this.formSearch.callbackBeginDate.length?moment(this.formSearch.callbackBeginDate[0]).format('YYYY-MM-DD HH:mm:ss'):'',
          callbackEndDate: this.formSearch.callbackBeginDate.length?moment(this.formSearch.callbackBeginDate[1]).format('YYYY-MM-DD HH:mm:ss'):'',
          callBeginDate:this.formSearch.callBeginDate.length? moment(this.formSearch.callBeginDate[0]).format('YYYY-MM-DD HH:mm:ss'):'',
          callEndDate: this.formSearch.callBeginDate.length?moment(this.formSearch.callBeginDate[1]).format('YYYY-MM-DD HH:mm:ss'):'',

        }).then(resp => {
          if (resp.body.code == 0) {
            this.tableData2 = resp.body.content;
            this.tableDataNoFilter = this.tableData2;
            this.totalNum = resp.body.totalNum;
            this.filterData2();
            this.$message.success("查询成功")
          }
        }).finally(() => {
          this.loading = false;
        });
      },
      loadReturnMiss() {
        if((!!this.formSearch.callbackEffectTimeMin && !!this.formSearch.callbackEffectTimeMax && this.formSearch.callbackEffectTimeMax-this.formSearch.callbackEffectTimeMin>30)){
          this.$message.error("回拨时效间隔必须大于30");
          return;
        }
        if(!!this.formSearch.callbackEffectTimeMin && this.formSearch.callbackEffectTimeMin.length>6){
          this.$message.error("回拨时效数值小于7位数");
          return;
        }
        if(!!this.formSearch.callbackEffectTimeMax && this.formSearch.callbackEffectTimeMax.length>6){
          this.$message.error("回拨时效数值小于7位数");
          return;
        }
        this.loading = true;
        
        this.$http.post(apiUrl.missCall_Returned, {
          pageNo: this.pageNow,
          pageSize: this.pageSize,
          jobNumber: this.formSearch.jobNumber,
          callNumber: this.formSearch.callNumber,
          managerUser: this.formSearch.managerUser,
          callbackEffectTimeMin: this.formSearch.callbackEffectTimeMin,
          callbackEffectTimeMax: this.formSearch.callbackEffectTimeMax,
          groupId: this.formSearch.groupId,
          callinType: this.formSearch.callinType,
          callbackBeginDate: this.formSearch.callbackBeginDate.length?moment(this.formSearch.callbackBeginDate[0]).format('YYYY-MM-DD HH:mm:ss'):'',
          callbackEndDate: this.formSearch.callbackBeginDate.length?moment(this.formSearch.callbackBeginDate[1]).format('YYYY-MM-DD HH:mm:ss'):'',
          callBeginDate:this.formSearch.callBeginDate.length? moment(this.formSearch.callBeginDate[0]).format('YYYY-MM-DD HH:mm:ss'):'',
          callEndDate: this.formSearch.callBeginDate.length?moment(this.formSearch.callBeginDate[1]).format('YYYY-MM-DD HH:mm:ss'):'',

        }).then(resp => {
          if (resp.body.code == 0) {
            this.tableData2 = resp.body.content;
            this.tableDataNoFilter = this.tableData2;
            this.totalNum = resp.body.totalNum;
            this.filterData2();
            this.$message.success("查询成功")
          }
        }).finally(() => {
          this.loading = false;
        });
      },
      loadBeforeMiss() {
        this.loading = true;
        this.$http.post(makeUrl(apiUrl.missCall_CurrentMissCall, {
          pageNo: this.pageNow,
          pageSize: this.pageSize,
          qryType: 1,
          jobNumber: this.formSearch.jobNumber,
          callNumber: this.formSearch.callNumber,
          managerUser: this.formSearch.managerUser,
        })).then(resp => {
          this.tableData = resp.body.content;
          this.tableDataNoFilter = this.tableData;
          this.totalNum = resp.body.totalNum;
          this.filterData();
        }).finally(() => {
          this.loading = false;
        });
      },
      resetForm() {
        this.formSearch.callType = '';
        this.formSearch.jobNumber = '';
        this.formSearch.callNumber = '';
        this.formSearch.managerUser = '';
        this.formSearch.callBeginDate = [];
        this.formSearch.callbackBeginDate = [];
        this.formSearch.groupId = '';
        this.formSearch.groupName = '';
        this.formSearch.callbackEffectTimeMin = '';
        this.formSearch.callbackEffectTimeMax = '';
      },
      

      selectedTab(tab) {
        if (this.selectCode != tab.code) {
          this.selectCode = tab.code;
          this.pageNow = 1;
          this.search();
        }
        this.tryShowCallBtn();
      },
      setting() {
        this.btns.shezhi.disable = false;
      },
      getOneTablelist() {
        //获取表单数据
        this.tableOneDat = this.tableData;
        this.tableOneDat = this.tableData2;
      },
      makeCall(phoneNo) {
        // PhoneBar.addOnAnswerCall(this, this.answerCall);
        // window.application.oJVccBar.MakeCall(phoneNo);
        EventBus.emit(EventBus.call_system_make_call, phoneNo);
      },
      callDialog() {
        if(this.checkPopupStatus()) return;
        this.showDial = !this.showDial;
        if (this.selectRow.length > 0) {
          this.phone = this.selectRow[this.selectRow.length-1].callNumber;
        }
      },
      tryShowCallBtn(isOneRow) {
        // console.log('isOneRow',isOneRow)
        this.btns.huchu.disable = true;
        if(isOneRow && this.selectCode == 'submitted') {
          // console.log('isOneRow',isOneRow)
          this.btns.huchu.disable = false;
        }
        // this.btns.huchu.disable = true;
        // if (this.selectCode == 'submitted' && application.oJVccBar.GetAgentStatus() == 1) {
        //   this.btns.huchu.disable = false;
        // }
      },
      // 多选中
      selectTableRow(row, event) {
        // console.log('row',row)
        // console.log('event',event)
        this.selectRow = row;  //保存选中行的数据方便标记

        this.btns.shenhe.disable = true;
        this.btns.tijiao.disable = true;
        //this.btns.daochu.disable = false;
        //  this.btns.shezhi.disable = true;
        if(row.length == 1) {
          this.tryShowCallBtn(true);
        }else {
          this.tryShowCallBtn()
        }
        if (!row || row.length == 0) {
        return;
        }

        let ids = [];
        if(['current','beforeleak'].includes(this.selectCode)){
            for (let r of this.selectRow) {
             ids.push(r.callId);
            }
        }else{
            for (let r of this.selectRow) {
             ids.push(r.id);
            }
        }
       
        if (this.selectCode == 'current') {
          this.btns.tijiao.disable = false;
          this.formMap.missId = ids.join(',');
          this.$emit('table-click', this.submit.tjData);
        } else if (this.selectCode == 'submitted') {
          this.btns.shenhe.disable = false;
          this.phone = row[0].callNumber;
          let params = {
            missId: ids.join(','),
            pageNo: this.pageNow,
            pageSize: this.pageSize,
          };
          this.$http.post(makeUrl(apiUrl.missCall_DetailInfo, params)).then(resp => {
            if (resp.data.code == 0) {
              this.tableData3 = resp.data.content;
            }
          });
          this.tryShowCallBtn();
          } else if (this.selectCode == 'returned') {
          // this.btns.shenhe.disable = false;
          this.phone = row[0].callNumber;
          let params = {
            missId: ids.join(','),
            pageNo: this.pageNow,
            pageSize: this.pageSize,
          };
          this.$http.post(makeUrl(apiUrl.missCall_DetailInfo, params)).then(resp => {

            this.tableData3 = resp.data.content;
          });
        }
      },
      searchCreatorCondition() {
        var missId = this.formMap.missId;
        var _this = this;
        this.$message.info('请选择一位负责人');
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/system_management/list.vue'), close: function() {
          },
          style: 'width:900px;height:600px',
          title: '请选择用户信息',
          params: {
            type: 'EMPLOYEE',
            status: 1,//生效人员
            isEnable: 1,//生效时间
            page_name: 'cloud_user_person',
            where: [],
            callback(b) {
              var data = b.data;

              let params = {
                assigneeUserId: data.id,
                assigneeUserName: data.fullName,
                missId: missId,
              };
              _this.$http.post(apiUrl.missCall_MissCallSubmit, params).then(resp => {

                _this.loadCurrentMiss();
              });
            },
          },
        });
      },
      check() {
        let ids = [];
        for (let r of this.selectRow) {
          ids.push(r.id);
        }
        let params = {
          missId: ids.join(','),
        };
        this.$http.post(apiUrl.missCall_Check, params).then((resp) => {

          if (resp.status == 200) {
            this.loadSubmittedMiss();
            this.$message.success('已提交审核');
          }
        });
      },
      // 导出
      exportOrderMsg() {
        let _this = this;
        let param = {pageNo: _this.pageNow, pageSize: _this.pageSize, callNumber: _this.formSearch.callNumber};

        if (this.selectCode == 'current') {
          param['qryType'] = 0;
          window.open(makeUrl('/callcenter-web/missCall/exportCurrentMissCall.do', param));
        } else if (this.selectCode == 'submitted') {
          param['jobNumber'] = _this.formSearch.jobNumber;
          param['managerUser'] = _this.formSearch.managerUser;
          window.open(makeUrl('/callcenter-web/missCall/exportSubmittedMissCall.do', param));
          this.loadSubmittedMiss();
          this.btns.daochu.disable = false;
        } else if (this.selectCode == 'returned') {
          param['jobNumber'] = _this.formSearch.jobNumber;
          param['managerUser'] = _this.formSearch.managerUser;
          window.open(makeUrl('/callcenter-web/missCall/exportReturnedMissCall.do', param));
        } else if (this.selectCode == 'beforeleak') {
          param['qryType'] = 1;
          window.open(makeUrl('/callcenter-web/missCall/exportCurrentMissCall.do', param));
        }
        // //  this.$http.get(makeUrl(apiUrl.workOrder_export,param))
        // //  .then(res => {
        // //
        // //   }, res => {
        // //
        // //   });
      },
      pageSizeChange(pageSize) {
        this.pageSize = pageSize;
        this.search();
      },
      filterData() {
        //
        this.tableData = this.tableDataNoFilter.filter((item) => {
          let calledNumberFalg = !!this.filters.calledNumber;
          let callTimeFalg = !!this.filters.callTime;
          let callTypeFalg = !!this.filters.callType;
          let diffMinuteFalg = !!this.filters.diffMinute;
          let missTypeFalg = !!this.filters.missType;
          let processUserFalg = !!this.filters.processUser;
          return (calledNumberFalg ? String(item.callNumber).includes(this.filters.calledNumber) : true)
            && (callTimeFalg ? String(item.createTime).includes(this.filters.callTime) : true)
            && (callTypeFalg ? String(item.callType).includes(this.filters.callType) : true)
            && (diffMinuteFalg ? String(item.diffMinute).includes(this.filters.diffMinute) : true)
            && (missTypeFalg ? String(item.missType).includes(this.filters.missType) : true)
            && (processUserFalg ? String(item.processUser).includes(this.filters.processUser) : true);

        });
      },
      filterData2() {
        this.tableData2 = this.tableDataNoFilter.filter((item) => {
          return (this.filters2.managerUser ? String(item.managerUser).includes(this.filters2.managerUser) : true)
            && (this.filters2.submiteTime ? String(item.submitTime).includes(this.filters2.submiteTime) : true)
            && (this.filters2.callType ? String(item.callType).includes(this.filters2.callType) : true)
            && (this.filters2.missType ? String(item.missType).includes(this.filters2.missType) : true)
            && (this.filters2.callTime ? String(item.missCallTime).includes(this.filters2.callTime) : true)
            && (this.filters2.calledNumber ? String(item.callNumber).includes(this.filters2.calledNumber) : true)
            && (this.filters2.callTimes ? String(item.callTimes).includes(this.filters2.callTimes) : true);
        });
      },
      renderCalledNumberHeader(h, {column, $index}) {
        return h('div', [
          h('span', {}, '来电号码'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters.calledNumber,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.calledNumber = value;
              },
            },
          }),
        ]);
      },
      formatterCalledNumber(row){
        return this.showPhoneNumber?row.callNumber:Fn.hidePhoneNumber(row.callNumber)
      },
      renderCalledCallTime(h, {column, $index}) {
        return h('div', [
          h('span', {}, '来电时间'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters.callTime,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.callTime = value;
              },
            },
          }),
        ]);
      },
      renderCalledCallType(h, {column, $index}) {
        return h('div', [
          h('span', {}, '来电类型'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters.callType,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.callType = value;
              },
            },
          }),
        ]);
      },
      renderCalledDiffMinute(h, {column, $index}) {
        return h('div', [
          h('span', {}, '间隔分钟'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters.diffMinute,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.diffMinute = value;
              },
            },
          }),
        ]);
      },
      renderCalledMissType(h, {column, $index}) {
        return h('div', [
          h('span', {}, '漏接类型'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters.missType,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.missType = value;
              },
            },
          }),
        ]);
      },
      renderCalledProcessUser(h, {column, $index}) {
        return h('div', [
          h('span', {}, '最后联系人'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters.processUser,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.processUser = value;
              },
            },
          }),
        ]);
      },
      renderCalledCallTimes(h, {column, $index}) {
        return h('div', [
          h('span', {}, '回拨次数'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters.callTimes,
            },
            on: {
              change: this.filterData,
              input: value => {
                this.filters.callTimes = value;
              },
            },
          }),
        ]);
      },
      renderCalledManagerUser(h, {column, $index}) {
        return h('div', [
          h('span', {}, '负责人'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters2.managerUser,
            },
            on: {
              change: this.filterData2,
              input: value => {
                this.filters2.managerUser = value;
              },
            },
          }),
        ]);
      },
      renderCalledCallNumber(h, {column, $index}) {
        return h('div', [
          h('span', {}, '漏接号码'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters2.calledNumber,
            },
            on: {
              change: this.filterData2,
              input: value => {
                this.filters2.calledNumber = value;
              },
            },
          }),
        ]);
      },
      renderCalledSubmitTime(h, {column, $index}) {
        return h('div', [
          h('span', {}, '提交时间'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters2.submiteTime,
            },
            on: {
              change: this.filterData2,
              input: value => {
                this.filters2.submiteTime = value;
              },
            },
          }),
        ]);
      },
      renderCalledCallTimes(h, {column, $index}) {
        return h('div', [
          h('span', {}, '回拨次数'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters2.callTimes,
            },
            on: {
              change: this.filterData2,
              input: value => {
                this.filters2.callTimes = value;
              },
            },
          }),
        ]);
      },
      renderCalledCallType2(h, {column, $index}) {
        return h('div', [
          h('span', {}, '来电类型'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters2.callType,
            },
            on: {
              change: this.filterData2,
              input: value => {
                this.filters2.callType = value;
              },
            },
          }),
        ]);
      },
      renderCalledCallTime2(h, {column, $index}) {
        return h('div', [
          h('span', {}, '来电时间'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters2.callTime,
            },
            on: {
              change: this.filterData2,
              input: value => {
                this.filters2.callTime = value;
              },
            },
          }),
        ]);
      },
      renderCalledMissType2(h, {column, $index}) {
        return h('div', [
          h('span', {}, '漏接类型'),
          h('el-input', {
            attrs: {
              'size': 'mini',
              'class': ' el-input el-input--mini table-search-input',
              'placeholder': '可搜索',
              'style': 'width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;',
            },
            props: {
              value: this.filters2.missType,
            },
            on: {
              change: this.filterData2,
              input: value => {
                this.filters2.missType = value;
              },
            },
          }),
        ]);
      },
        getAgentWorkEventNameFn(){
            if (PhoneBar.getStatus() === 1) { // 2 idle
                this.tryShowCallBtn();
            }
        },
      initEventBusFn(){
        EventBus.on(PhoneBar.getAgentWorkEventName, this.getAgentWorkEventNameFn);
        // 初始化完了设置状态为示忙
        EventBus.on(PhoneBar.getPhoneInitName, this.initFn);
      },
      initFn(){
        // 如果phone加载完才调用
        if (window.applicationLoad) {
          if (PhoneBar.getStatus() === 1) { // 0 未登录 2 空閑
            // this.initPhoneBar();
            this.tryShowCallBtn();
          }
        }
      },
      // 检测呼出弹窗状态
      checkPopupStatus(){
        if(this.diaConfig.dialogVisible) {
          this.$message.error('请先关闭外呼弹窗')
          return true
        };
      },
      //显示隐藏号码
      async switchPhoneNumberShow(type){
        if(this.showPhoneNumber&&type){
          this.$message.warning('号码已全部显示')
          return
        }
        this.switchPhoneNumberDisabled=true
        type?await this.saveUserSensitiveLookLog():''
        this.switchPhoneNumberDisabled=false
        this.showPhoneNumber=type
        this.refreshTable()
      },
      //重新渲染表格
      refreshTable () {
          let tableArray=[]
          let tableArray2=[]
          tableArray=this.tableData
          tableArray2=this.tableData2
          this.tableData=[]
          this.tableData2=[]
          this.$nextTick(() => {
            this.tableData = tableArray
            this.tableData2 = tableArray2
          })
      },
      saveUserSensitiveLookLog(){
        let self=this
        let params={}
        params.operatePageName=this.activeTabName;
        params.operateType='LOOK';
        params.lookContent='';
        params.number='';
        return new Promise((resolve,reject)=>{
          self.ajax.postStream('/user-web/api/saveUserSensitiveLookLog',params,res=>{
            if(res.body.result){
              resolve()
            }else{
              self.$message.error(res.body.msg)
              reject()
            }
          },err=>{
              self.$message.error(err)
              reject()
          })
        })
      },
    },
    mounted() {
      let _this = this;
      _this.loadCurrentMiss();
      this.initFn();
      // PhoneBar.saveRun(() => {
      //   if (PhoneBar.getStatus() === 1) { // 0 未登录 2 空閑
      //     // this.initPhoneBar();
      //     _this.tryShowCallBtn();
      //   }
      // });
      // PhoneBar.addOnAgentWorkReport(this, () => {
      //   if (PhoneBar.getStatus() === 1) { // 2 idle
      //     _this.tryShowCallBtn();
      //   }
      // });
    },

    beforeDestroy() {
        EventBus.off(PhoneBar.getAgentWorkEventName, this.getAgentWorkEventNameFn);
        // 初始化完了设置状态为示忙
        EventBus.off(PhoneBar.getPhoneInitName, this.initFn);
      PhoneBar.detachAll(this);
    },
  };
</script>
<style scoped>
  .app-container {
    height: 100%;
    display: flex;
    flex-direction: column;

  }

  .app-container > .table-view {
    height: 0 !important;
  }

  .search-input {
    width: 120px;
  }

  .search-box {
    border-top: 1px solid #d1dbe5;
    text-align: right;
  }

  .search-box .el-form {
    float: right;
  }

  .header-bar {
    height: 32px;
  }
</style>
