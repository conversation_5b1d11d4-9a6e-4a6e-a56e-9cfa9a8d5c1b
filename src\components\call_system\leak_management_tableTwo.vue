<template>
    <el-table
        @row-dblclick="detail"
        @row-click="selectTableRow"
         v-loading="loading"
        :data="data"
        border
        max-height="175"
        ref="table"
        :highlight-current-row="true">
        <el-table-column prop="index" label="序号" width="120"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="handleName" label="负责人" width="120"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="calledNumber" label="来电号码" width="120" :render-header="renderCommonHeader">
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="callingType" label="来电类型" width="120" :render-header="renderCommonHeader"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="callingTime" label="来电时间" width="120"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="leakType" label="漏接类型" width="120" :render-header="renderCommonHeader"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="updateTime" label="提交时间" width="120"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="callbackTimes" label="回拨次数" width="120"></el-table-column>
      </el-table>
</template>
<script>
export default {
    props:{
        data:{
            type: Array,
            default: function () {
                return []
            }
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    computed:{
        propsTable() {
            return this.data
        }
    },
    data() {
        return {
            filters: { //筛选参数

            },
            tableShowData:[], //表格展示的数据
        }
    },
    methods: {
        detail(row) {
            this.$emit('row-dblclick',row)
        },
        selectTableRow(){
            this.$emit('row-click',row, event)
        },
        filterData(){
            //表格内筛选
        },
        renderCommonHeader(h, { column, $index }){
            return h("div",[
                    h("span",{

                    }, column.label),
                    h("el-input",{
                      attrs: {
                            "size" : "mini",
                            "class": "el-input el-input--mini table-search-input",
                            "placeholder": "可搜索",
                            "style": "width: 109px;display: inline-block;margin-left: 5px;margin-bottom: 2px;"
                        },
                        props:{
                          size : "mini",
                          value: this.filters[column.property]
                        },
                        on: {
                            change: this.filterData,
                            input: value =>{this.filters[column.property] = value}
                        }
                    }),
                ])
        }
    }
}
</script>
