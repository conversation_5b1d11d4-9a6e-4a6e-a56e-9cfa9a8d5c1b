<!-- 关联订单 -->
<template>
  <div class="xpt-flex">
    <div style="margin-top:8px; margin-bottom: 8px">
      <el-button type="primary" slot='left' size='mini' @click="saveTabs()" :disabled="disabled">保存</el-button>
      <el-button type="primary" slot='left' size='mini' @click="closeTabs()" :disabled="disabled">关闭</el-button>
    </div>
    <select-tab class="call-record-carb-tab" @changeTab="selectedTab" :tabs="tabs" :selectedCode="currView" style="margin-bottom: 8px"></select-tab>
    <keep-alive>
      <component ref="tabPage" :is="currView?.default || currView" :job="params.job" @changed="setDisable"
                 @update:sharedPhone="sharedFormValue.phoneNum=$event" :sharedPhone="sharedFormValue.phoneNum"
                 @update:sharedCustomer="sharedFormValue.customerName=$event" :sharedCustomer="sharedFormValue.customerName"
      ></component>
    </keep-alive>
  </div>
</template>

<script>
  import workBatchorderDialog from '@/components/common/workBatchorderDialog';
  import worksaleDialog from '@/components/common/worksaleDialog';
  import workSaleafterDialog from '@/components/common/workSaleafterDialog';
  import workreturnDialog from '@/components/common/workreturnDialog';
  import selectTab from '@/components/call_system/selectTab'

  export default {
    props: ['params'],
    components: {
      selectTab,
      workBatchorderDialog,
      worksaleDialog,
      workSaleafterDialog,
      workreturnDialog,
    },
    data() {
      var _this = this;
      return {
        disabled:true,
        bestHandlerUser:'',
        problemHandlerNo:'',
        problemHandlerId:'',
        currView: 'workBatchorderDialog',
        selectedCode: 'workBatchorderDialog',
        sharedFormValue:{
          phoneNum:'',
          customerName:'',
        },
        tabs: [
          {
            code: 'workBatchorderDialog',
            name: '批次订单',
          }, {
            code: 'worksaleDialog',
            name: '销售订单',
          }, {
            code: 'workSaleafterDialog',
            name: '售后单',
          }, {
            code: 'workreturnDialog',
            name: '回访单',
          }],
      };
    },
    methods: {
      setDisable(data){
        // console.log(this.currView)
        this.disabled = data.val;
        this.getShopType(data.row,data.reslove);
      },
      getShopType(row,reslove){
        if(!row){
          return false;
        }
        let search = {
            "page_name": "cloud_shop_v2",
            "shop_name":row.shop_name,
            "page": {
                "length": 50,
                "pageNo": 1
            }
        }
        this.$http.post("/material-web/api/shopv2/list",search).then(res=>{
            // console.log(res);
              if(!!res.body.content.list[0]){
                if(res.body.content.list[0].shop_type == "ON_LINE"){
                  this.getBestHandlerUser(row,reslove);

                }else{
                  if(this.currView == "workBatchorderDialog" || this.currView == "worksaleDialog"){
                    this.getShopProblem(row,reslove)
                  }
                }
              }else{
                this.$message.error("获取店铺信息失败，请联系开发人员");

              }
              
            })
      },
      // 获取关联单推荐处理人
      getBestHandlerUser(row,reslove){
        if(!row) return;
        let search = {};
        if(this.currView == "worksaleDialog"){
          search.tradeNo = row.sys_trade_no;
        }else if(this.currView == "workBatchorderDialog"){
          search.batchNo = row.batch_trade_no;
        }
        if(this.currView == "workBatchorderDialog" || this.currView == "worksaleDialog"){
          if(row.salesman_name){
            this.bestHandlerUser = row.salesman_name;
            this.problemHandlerNo = row.salesman_no;
            reslove && reslove(this.bestHandlerUser);
          }else{
            this.$http.post("/external-web/api/callCenter/getBestHandlerUser",search).then(res=>{
            // console.log(res);
              if(!!res.body.content){
                this.bestHandlerUser = res.body.content.best_user_name;
                this.problemHandlerNo = res.body.content.best_user_employee_number;
                this.problemHandlerId = '';
                reslove && reslove(this.bestHandlerUser);
              }else{
                // this.getShopProblem(row)
              }
              
            })
          }
          
        }
      },
      getShopProblem(row){
        let search = {
          groupName:row.shop_name
        };
        
        if(this.currView == "workBatchorderDialog" || this.currView == "worksaleDialog"){
          this.$http.post("/callcenter-web/api/cloudShopProblem/get.do",search).then(res=>{
            // console.log(res);
            if(!!res.body.content){
              this.bestHandlerUser = res.body.content.memberName;
              this.problemHandlerNo = '';
              this.problemHandlerId = res.body.content.handlerId;
            }else{
              // this.getShopProblem()
            }
            
          })
        }
      },
      //点击tabs
      openView(key) {
        this.currView = key;

        // this.$emit('changeWork', key)
      },
      //保存
      saveTabs() {
        if (!this.$refs.tabPage.row) {
          return;
        }
        this.params.callback(Object.assign({}, this.$refs.tabPage.row, {currView: this.currView,bestHandlerUser:this.bestHandlerUser,problemHandlerNo:this.problemHandlerNo,problemHandlerId:this.problemHandlerId}));
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      },
      selectedTab(item){
        this.openView(item.code)
      },
      //关闭
      closeTabs() {
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      },
    },
    // computed:{
    //   disabled(){
    //     if(this.$refs['tabPage'] && this.$refs['tabPage'].row){
    //       return false
    //     }

    //     return true
    //   }
    // },
    updated(){
      this.disabled = !(this.$refs['tabPage'] && this.$refs['tabPage'].row)
    }
  };
</script>
<style scoped>
  .linked_order {
    margin: 10px;
    width: 100%;
  }

  .el-button:focus, .el-button:hover {
  }
</style>
