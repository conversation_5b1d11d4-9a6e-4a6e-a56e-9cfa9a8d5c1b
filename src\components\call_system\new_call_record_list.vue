<!-- 新零售话务呼叫记录列表 -->
<template>
  <div id="call_record_list" style="height:100%;">
    <xpt-list
      ref="callRecordList"
      :orderNo="false"
      :selection="params.fromAlert ? 'radio':''"
      :searchHolder="params.fromAlert ? '':'true'"
      :btns="btns"
      :colData="cols"
      :data="tableData"
      :pageLength="search.page_size"
      :pageTotal="totalPage"
	  :searchPage="search.page_name"
      @radio-change='radioChange'
      @search-click="searchClick"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    ></xpt-list>


   <!----- 手工同步弹窗 ----->
   <el-dialog
   :modal="false"
   :show-close="false"
   :visible="manualSyncStatus"
   custom-class="manualSyncClass"
   size="tiny"
   title="手工同步"
 >
   <el-form
     :model="manualSyncFormDatas"
     :rules="manualSyncFormRules"
     label-position="right"
     label-width="80px"
     ref="manualSyncFormRef"
   >
     <el-form-item class="timeInputClass mb15" label="同步时间：" prop="syncTime">
       <el-date-picker
         size="mini"
         type="date"
         v-model="manualSyncFormDatas.syncTime"
       ></el-date-picker>
     </el-form-item>

     <el-form-item label="同步电话：" prop="telephone">
       <el-input v-model='manualSyncFormDatas.telephone' size='mini' :maxlength='20'></el-input>
    </el-form-item>

     <el-form-item class="buttonClass mb15">
       <el-button :disabled="manuaSyncButton.cancelDisabled" @click="onCancel" size="small">取消</el-button>
       <el-button :loading="manuaSyncButton.submitLoading" @click="onSubmit" size="small" type="primary">确定
       </el-button>
     </el-form-item>
   </el-form>
 </el-dialog>


  </div>
</template>

<script>
  import {apiUrl, makeUrl} from './base.js';
  import Fn from '@common/Fn.js';

  export default {
    props: ["params"],
    data() {
      let self = this;
      return {
        btns: [
          {
            type: "success",
            txt: "刷新",
            loading: false,
            disabled: false,
            click() {
              self.getList();
            }
          },
          // {
          //   type: "primary",
          //   txt: "导出",
          //   loading: false,
          //   disabled: false,
          //   click() {
          //     self.exportTable();
          //   }
          // },
          // {
          //   type: "warning",
          //   txt: "手工同步",
          //   loading: false,
          //   disabled: false,
          //   click() {
          //     self.manualSyncStatusFun();
          //   }
          // },
          {
					isBtnGroup: true,
					btnGroupList:[
						{
							type:'success',
							txt:'显示号码',
							click(){
								self.switchPhoneNumberShow(true)
							},
							isDisabled:self.switchPhoneNumberDisabled
						},{
							type:'warning',
							txt:'隐藏号码',
							click(){
							   self.switchPhoneNumberShow(false)
							},
							isDisabled:self.switchPhoneNumberDisabled
						}
					]
				 }
        ],
        cols: [
          {
            label: "工单编号",
            prop: "jobNumber"
          },
          {
            label: "呼叫类型",
            prop: "calledType"
          },
          {
            label: "状态",
            prop: "connetctStatus"
          },
          {
            label: "工号",
            prop: "staffNo"
          },
          {
            label: "客服分组",
            prop: "staffGroupName"
          },
          {
            width: '95',
            label: "呼叫电话",
            prop: "phoneNumber",
            format: "hidePhoneNumber"
          },
          {
            label: "用户名",
            prop: "staffName"
          },
          {
            label: "处理内容",
            prop: "remark",
            width: '350',
            html: data => {
              data = data ? data : '';
              // 正则 去除重复的换行符
              // var reg = /(\n|\/)\1/;
              // while(reg.test(data)){
              //   data = data.replace(reg, "$1");
              // }
              //
              return `<p>${data}</p>`
            },
            getCellClassName() {
              return 'remarkClassByCallCenterCallRecordList'
            }
          },
          {
            label: "通话时长(秒)",
            prop: "callDuration"
          },
          {
            label: "开始时间",
            prop: "recordStartTime"
          },
          {
            label: "结束时间",
            prop: "recordEndTime"
          },
          {
            label: "主动评价",
            prop: "isInitiative",
            formatter: (val) => {
              switch (val) {
                case 'YES':
                  return '是';
                case 'NO':
                  return '否';
                default:
                  return ''
              }
            }
          },
          {
            label: "客户评分",
            prop: "satisfactionIndex",
            formatter: (val) => {
              switch (val) {
                case '1':
                  return '非常满意';
                case '2':
                  return '一般';
                case '3':
                  return '不满意';
                default:
                  return ''
              }
            }
          },
          {
            label: "录音",
            prop: "recordFile",
            redirectClick(row) {
              if (row.recordFile) {
                window.open(row.recordFile);
              }else if(row.callSystem =='FEIYU'){
                self.getCuleDetail(row)
              }else{
                self.$message.error('无播放录音源')
              }
            },
            formatter: val => {
              return "播放";
            }
          },
        ],
        tableData: [],
        totalPage: 0,
        search: {
          page_no: 1,
          page_size: 200,
          page_name: "cloud_crm_call_stat",
          where: []
        },
        select: {}, //单选


        // ----- 手工同步弹窗 -----
        manualSyncFormRules:{},
        manualSyncStatus: false,
        manuaSyncButton: {
          cancelDisabled: false,
          submitLoading: false,
        },
        manualSyncFormDatas: {
          syncTime: '',
        },

        //查看隐藏号码
        switchPhoneNumberDisabled:false,
        showPhoneNumber:false
      };
    },
    watch: {},
    methods: {
      // 获取飞鱼录音
      getCuleDetail(row){
        let self = this;
         self.ajax.postStream(
          "/callcenter-web/feiyu/clue/detail.do", {sourceCallId:row.sourceCallId},
          res => {
            if(res.body.result){
              window.open(res.body.content.recordFile)
            }
          },
          err => {
            
          }
        )
      },
      // 获取呼叫记录列表
      getList(resolve) {
        this.btns[0].loading = true;
        let params = {
          page: {
            length: this.search.page_size,
            pageNo: this.search.page_no
          },
          page_name: this.search.page_name,
          where: this.search.where
        };
        // 当呼叫记录以弹窗形式打开，添加查询参数
        if (this.params.fromAlert) {
          // 没有工单号
          params.noJobNumber = true;
          params.where.push(
            { // 通话记录为接通状态
              "field": "87ea8ff7efcc85f2bae4010f9b4dbf8d",
              "table": "c1df2e603d5109407243da02457a24b7",
              "value": "接通",
              "operator": "=",
              "condition": "AND",
              "listWhere": []
            }, { // 工号和当前用户相等
              "field": "5bf2fb3acc7f688e53b36d28cb7ce82a",
              "table": "c1df2e603d5109407243da02457a24b7",
              "value": this.getUserInfo('employeeNumber'),
              "operator": "=",
              "condition": "AND",
              "listWhere": []
            },
            // {  // 手工添加手机号，以便走完实验流程，之后需要注释
            //   "field": "75e6a9acb3c03434531e9989f656f8db",
            //   "table": "c1df2e603d5109407243da02457a24b7",
            //   "value": "18024149758",
            //   "operator": "=",
            //   "condition": "AND",
            //   "listWhere": []
            // },
            {
              // 获取最近三个月的时间; 获取当前时间戳，然后直接减掉三个月的毫秒数
              "field": "7651c1a6ef4941f5e7af3d574ce4b9c0",
              "table": "c1df2e603d5109407243da02457a24b7",
              "value": Fn.dateFormat(new Date().getTime() - 7776000000,'yyyy-MM-dd hh:mm:ss'),
              "operator": ">",
              "condition": "AND",
              "listWhere": []
            }
          )
        }
        this.ajax.postStream(
          // listCallHistoryPage.do 新的
          // listCallHistory.do 旧的
          
          "/callcenter-web/callStat/listCrmCallPage.do", params,
          res => {
            if (res && res.body.result) {
              this.tableData = res.body.content.list;
              this.totalPage = res.body.content.count;
            } else {
              this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
            this.switchPhoneNumberShow(false);//更新重置地址隐藏
          },
          err => {
            resolve && resolve();
            this.$message.error(err);
            this.btns[0].loading = false;
          }
        )
      },
      // 查询导出表格条件
      exportTableFun(url,params,callback){
        this.ajax.postStream( url, params,
          res => {
			callback(res.body.content);  
          },
          err => {
            //this.$message.error('呼叫记录导出查询异常');
          }
        )
      },

      // 导出表格
      exportTable() {
        let params = {
          page: {
            length: this.search.page_size,
            pageNo: this.search.page_no
          },
          page_name: this.search.page_name,
          where: this.search.where
        };
        // 获取呼叫记录excel导出条件，调用excel导出
        this.exportTableFun(apiUrl.callStat_exportSearch, params, function(data) {          
          let queryParam = {
            page_size : params.page.length,
            page_no : params.page.pageNo
          };
		  if(data){
            queryParam["whereSQL"]=data;         
          }
           window.open(makeUrl(apiUrl.callStat_export, queryParam));
        })
      },
      // 搜索
      searchClick(obj, resolve) {
        this.search.where = obj;
        this.getList(resolve);
      },
      // 当前条数
      pageSizeChange(ps) {
        this.search.page_size = ps;
        this.getList();
      },
      // 当前页数
      pageChange(no) {
        this.search.page_no = no;
        this.getList();
      },
      // 修改对应措施
      checkAlertStatus() {
        if (!this.params.fromAlert) {
          return
        } else if (this.params.fromAlert) {
          const self = this;
          let nowbtns = [
            {
              type: "success",
              txt: "刷新",
              loading: false,
              disabled: false,
              click() {
                self.getList();
              }
            },
            {
              type: "primary",
              txt: "选中",
              loading: false,
              disabled: false,
              click() {
                self.choiceItem();
              }
            },
            // 工单页面弹框
            {
              type: "warning",
              txt: "手工同步",
              loading: false,
              disabled: false,
              click() {
                self.submitmanualSync();
              }
            }
          ];
          this.btns = nowbtns;
        }
      },
      // 单选
      radioChange(obj) {
        this.select = obj;
      },
      // 单选确定
      choiceItem() {
        if (!this.select) {
          this.$message.warning('尚未选中相关数据');
          return
        } else if (this.select.remark) {
          this.$message.error('选中数据已有处理内容，无法关联');
          return
        }
        ;

        let item = JSON.parse(JSON.stringify(this.select));
        this.params.callback(item);
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
      },

      manualSyncStatusFun(){
        this.manualSyncStatus = true;
      },
      // ----- 手工同步弹窗 -----
      // 取消
      onCancel() {
        this.manualSyncFormDatas = {
          syncTime:''
        };
        this.manualSyncStatus = false;
      },
      // 确认提交
      onSubmit() {
        this.$refs['manualSyncFormRef'].validate((valid) => {
          if (valid) {
            // alert('submit!');
            this.submitmanualSync();
          } else {
            return false;
          }
        });
      },
      // 提交
      submitmanualSync() {
        this.manuaSyncButton.cancelDisabled = true;
        this.manuaSyncButton.submitLoading = true;
        let params = {
          date:Fn.dateFormat(this.manualSyncFormDatas.syncTime),
          telephone: this.manualSyncFormDatas.telephone
        };
        this.$http.post(apiUrl.callStat_dataSync,params)
          .then(({body})=>{
            if(body.result){
              this.$message.success(body.msg);
              this.refreshAfterSuccess(this.manualSyncFormDatas.syncTime)
            }else if(!body.result){
              this.$message.error(body.msg);
            }
          })
          .finally(()=>{
            this.manuaSyncButton.cancelDisabled = false;
            this.manuaSyncButton.submitLoading = false;
          })
      },
      // 成功同步之后关闭弹窗，然后刷新列表
      refreshAfterSuccess(thetime){
        this.onCancel();
        this.formSearch = {
          dateType: '自定义',
          startDate: thetime,
          endDate: thetime,
          groupName: '全部',
        };
        //刷新列表
        this.getList();
      },
      // ----- 手工同步弹窗   end -----
    async switchPhoneNumberShow(type){
			if(type&&this.showPhoneNumber){
				this.$message.warning('已显示全部号码')
				return
			}
			this.switchPhoneNumberDisabled = true
			let currentCol=this.cols.map(item=>{
				if(item.prop=='phoneNumber'){
						type?item.format='':item.format='hidePhoneNumber'
				}
				return item
			})
			type?await this.saveUserSensitiveLookLog():''
			this.cols=currentCol
			this.showPhoneNumber=type
			this.switchPhoneNumberDisabled = false
		},
		saveUserSensitiveLookLog(){
			let self=this
			let params={}
			params.operatePageName=this.activeTabName;
			params.operateType='LOOK';
			params.lookContent='';
			params.number='';
			return new Promise((resolve,reject)=>{
				self.ajax.postStream('/user-web/api/saveUserSensitiveLookLog',params,res=>{
					if(res.body.result){
						resolve()
					}else{
						self.$message.error(res.body.msg)
						reject()
					}
				},err=>{
						self.$message.error(err)
						reject()
				})
			})
		},
    },
    mounted() {
      // 检测到当前是alert形式打开，修改按钮以及界面
      this.checkAlertStatus();
      this.getList();
      if (!this.params.fromAlert) {
        // 操作查询关键字以及查询范围范围的显示顺序
        this.$refs.callRecordList.$refs.xptSearchEx.filterFields =
          fields => {
            let oldItem = fields[5];
            fields.splice(5, 1);
            fields.unshift(oldItem);
            return fields;
          }
      }
    },
    destroyed() {
    }
  };
</script>

<style>
  #call_record_list .remarkClassByCallCenterCallRecordList {
    height: 100% !important;
  }

  #call_record_list .remarkClassByCallCenterCallRecordList .cell {
    height: 100% !important;
    line-height: 20px !important;
  }

  #call_record_list .remarkClassByCallCenterCallRecordList p {
    white-space: pre-line;
  }
</style>
