// eslint-disable-next-line no-undef
import {EventBus} from './base.js';
console.log(EventBus)
const main = {
  onInitPhone:'PHONE_INIT',
  calling:'PHONE_CALLING',
  answerCall:'PHONE_ANSWER',
  callEnd:'PHONE_END',
  agentWorkReport:'PHONE_AGENTWORK',
  barExit:'PHONE_BAREXIT',

  custPhoneNum: '', // 通话时对方的号码
  sdkAvaliable: false,
  initFuncs: [],
  onCallRings: [],
  answerCalls: [],
  onCallEnds: [],
  onAgentWorkReports: [],
  onBarExits: [],
  currentCallInfo: null,
  quickRun(funcItem,...args){
    try {
      funcItem.func.call(funcItem.self, ...args);
    } catch (e) {
      console.error(e);
    }
  },
  onCallRing() {
    console.log('onCallRing')
    this.onCallRings.forEach(item => {
      this.quickRun(item, ...arguments);
    });
  },
  onAnswerCall() {
    console.log('onAnswerCall')
    this.answerCalls.forEach(item => {
      this.quickRun(item, ...arguments);
    });
  },
  onCallEnd() {
    console.log('onCallEnd')
    this.onCallEnds.forEach(item => {
      this.quickRun(item, ...arguments);
    });
  },
  onAgentWorkReport() {
    console.log('onAgentWorkReport')
    this.onAgentWorkReports.forEach(item => {
      this.quickRun(item, ...arguments);
    });
  },
  onBarExit() {
    console.log('onBarExit')
    this.onBarExits.forEach(item => {
      this.quickRun(item, ...arguments);
    });
  },
  initEvent: function() {
    console.log('PhoneBar InitEvent 电话条初始化')
    this.sdkAvaliable = true;

    // 初始化
    this.initFuncs.forEach(item => {
      try{
        item();
      } catch (e) {
        console.error(e)
      }
    });
    // 触发初始化
    EventBus.emit(this.onInitPhone)



    application.oJVccBar.OnCallRing = (...args) => {
      var callingNo = args[0];
      var calledNo = args[1];
      this.currentCallInfo = {...args};
      if (callingNo !== calledNo) { // 呼入
        this.custPhoneNum = callingNo;
      } else {
        this.custPhoneNum = args[2];
      }
      console.log('onCallRing')
      // 触发初始化
      EventBus.emit(this.calling,...args)

      // this.onCallRing.call(this, ...args);
    };
    application.oJVccBar.AnswerCall = (...args) => {
      console.log('AnswerCall')
      EventBus.emit(this.answerCall,...args)
      // this.onAnswerCall.call(this, ...args);
    };
    application.oJVccBar.OnCallEnd = (...args) => {
      // this.onCallEnd.call(this, ...args);
      console.log('OnCallEnd')
      EventBus.emit(this.callEnd,...args)
      this.custPhoneNum = '';
      this.currentCallInfo = null;
    };
    application.oJVccBar.OnAgentWorkReport = (...args) => {
      // this.onAgentWorkReport.call(this, ...args);
      console.log('OnAgentWorkReport')
      EventBus.emit(this.agentWorkReport,...args)
    };
    application.oJVccBar.OnBarExit = (...args) => {
      console.log('OnBarExit')
      // this.onBarExit.call(this, ...args);
      EventBus.emit(this.barExit,...args)
    };
  },
  phoneInit(
    serverIP, vccID, agentID, sipPort, sipPassword, mainPort, ctiPassword) {
    application.oJVccBar.SetAttribute('MainIP', serverIP);
    application.oJVccBar.SetAttribute('MainPortID', 14800);
    application.oJVccBar.SetAttribute('BackIP', serverIP);
    application.oJVccBar.SetAttribute('BackPortID', 14800);
    application.oJVccBar.SetAttribute('MonitorIP', serverIP);
    application.oJVccBar.SetAttribute('MonitorPort', 5040);
    application.oJVccBar.SetAttribute('MainPortID', parseInt(mainPort));
    application.oJVccBar.SetAttribute('BackPortID', parseInt(mainPort));
    application.oJVccBar.SetAttribute('SipServerIP', serverIP);
    application.oJVccBar.SetAttribute('SipDomain', serverIP);
    application.oJVccBar.SetAttribute('SipServerPort', parseInt(sipPort));
    application.oJVccBar.SetAttribute('SipProtocol', 'UDP');
    let hasSeat = localStorage.getItem('hasSeatPreference') === 'true';
    application.oJVccBar.SetAttribute('PhonType', hasSeat ? 2 : 1); // 0:内置坐席卡；1：内置Sip；2：外置其他终端 3：远程sip电话; 4：软交换前传号码; 5：yealink话机 6：agora
    application.oJVccBar.SetAttribute('AgentType', 1);
    application.oJVccBar.SetAttribute('SelfPrompt', 0);
    application.oJVccBar.SetAttribute('MediaFlag', vccID);
    application.oJVccBar.SetAttribute('AppType', 0);
    application.oJVccBar.SetAttribute('TaskID', '');
    application.oJVccBar.SetAttribute('PassWord', ctiPassword);
    application.oJVccBar.SetAttribute('AgentID', '000010' + vccID + agentID);
    application.oJVccBar.SetAttribute('Dn', '000002' + vccID + agentID);
    application.oJVccBar.SetAttribute('SipPassWord', sipPassword);
    application.oJVccBar.SetAttribute('WeChatServer', serverIP + ':8000');
    application.oJVccBar.SerialBtn(
      '0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 30',
      '19,20,21,22,30');
    application.oJVccBar.Initial();

  },
};

try {
  applicationLoad(10, 10, 1320, 40, 0, '', () => {
    main.initEvent();
  });
} catch (e) {
  timeout();
  // setTimeout(() => {
  //   main.initEvent();
  // }, 0);
}

function timeout() {
  if (window.applicationLoad) {
    applicationLoad(10, 10, 1320, 40, 0, '', () => {
      main.initEvent();
    });
  } else {
    setTimeout(() => {
      timeout();
    }, 200);
  }
}

export default {
  get getPhoneInitName(){
    return main.onInitPhone
  },
 get  getCallingEventName(){
    return main.calling;
  },
 get getAnswerCallEventName(){
    return main.answerCall;
  },
  get getCallEndEventName(){
    return main.callEnd;
  },
  get getAgentWorkEventName(){
    return main.agentWorkReport;
  },
  get getBarExitEventName(){
    return main.barExit;
  },

  getCustPhoneNum() {
    return main.custPhoneNum + '';
  },
  getCurrentCallInfo() {
    return Object.assign({}, main.currentCallInfo);
  },
  getSdkAvaliable() {
    return main.sdkAvaliable;
  },
  leakPhone(phoneNumber) {
    main.leakPhone = phoneNumber;
  },
  getLeakPhone() {
    return main.leakPhone;
  },
  getStatus() {
    //获取座席的工作状态   0：未登录   1：忙碌  2：空闲  3：通话中   4：后续态
    return application.oJVccBar.GetAgentStatus();
  },
  saveRun(func) {
    if (main.sdkAvaliable) {
      func();
    } else {
      main.initFuncs.push(() => {
        func();
      });
    }
  },
  phoneInitial(
    serverIP, vccID, agentID, sipPort, sipPassword, mainPort, ctiPassword) {
    if (main.sdkAvaliable) {
      main.phoneInit(serverIP, vccID, agentID, sipPort, sipPassword, mainPort,
        ctiPassword);
    } else {
      main.initFuncs.push(() => {
        main.phoneInit(serverIP, vccID, agentID, sipPort, sipPassword, mainPort,
          ctiPassword);
      });
    }
  },
  phoneUninstall() {
    application.oJVccBar.UnInitial();
  },
  addOnCallRing(vueComponent, func) {
    // main.onCallRings = []
    main.onCallRings.push({self: vueComponent, func});
  },
  addOnAnswerCall(vueComponent, func) {
    // main.answerCalls = []
    main.answerCalls.push({self: vueComponent, func});
  },
  addOnCallEnd(vueComponent, func) {
    // main.onCallEnds = []
    main.onCallEnds.push({self: vueComponent, func});
  },
  addOnAgentWorkReport(vueComponent, func) {
    main.onAgentWorkReports.push({self: vueComponent, func});
  },
  addOnBarExit(vueComponent, func) {
    main.onBarExits.push({self: vueComponent, func});
  },
  detachAll(vueComponent) {
    let that = vueComponent;
    let props = [
      'onCallRings',
      'answerCalls',
      'onCallEnds',
      'onAgentWorkReports',
      'onBarExits',
    ];
    props.forEach(funcs => {
      main[funcs] = main[funcs].filter(item => item.self !== that);
    });
  },
  detachOnCallEnd(vueComponent) {
    main.onCallEnds = main.onCallEnds.filter(
      item => item.self !== vueComponent);
  },
  detachAnswerCall(vueComponent) {
    main.answerCalls = main.answerCalls.filter(
      item => item.self !== vueComponent);
  },
};
