<template>
  <div class="dial-panel" ref="dial-panel">
    <div class="dial-display">
      <span>呼叫号码</span>
      <el-input v-model="displayPhone" size="mini" v-show="canChange"/>
      <el-select size="mini" v-model="displayPhone" ref="phone-input" id="phone-input-id" v-show="!canChange"
                 class="phone-input">
        <el-option
          v-for="item in phoneSuggest"
          :key="item.value"
          :label="item.label"
          :value="item.value">
          <span style="float: left">{{ item.label }}</span>
          <span class="remark" style="float: right; font-size: 13px">{{ item.remark }}</span>
        </el-option>
      </el-select>
    </div>
    <div v-show="!canChange">
      <span>二次呼叫</span>

      <el-input v-model="secondCallPhone" ref="phone2-input" class="phone-input" size="mini"/>

    </div>
    <ul class="dial-panel-btns">
      <li class="dial-btn" v-for="(btn, index) in dialBtns" @click="onBtnClick(btn)" :key="index">{{btn}}</li>
    </ul>
    <ul class="dial-action">
      <li @click="()=>onCallClick(this.displayPhone)" style="background: #3a87ad">呼出</li>
      <li @click="onCallClick(0)" style="background: #f7a35c">加0呼出</li>
      <li @click="SendDTMF()" style="background: #f7a35c">二次呼叫</li>
      <li @click="onDeleteClick" style="border: #6f7882 solid 1px">退格</li>
      <li @click="onClose" style="border: #6f7882 solid 1px">关闭</li>
    </ul>
  </div>
</template>

<script>
  export default {
    name: 'phoneDial',
    props: {
      phoneSuggest: Array,
      phone: String,
      canChange: { // 是否可以编辑号码
        type: Boolean,
        default: true
      },
    },
    watch: {
      phoneSuggest(newVal, oldVal) {
        if (!this.displayPhone && newVal.length > 0) {
          if (newVal[2].value) {
            this.displayPhone = newVal[2].value;
            return;
          }
          this.displayPhone = newVal[0].value;
        }
      },
      phone(val) {
        this.displayPhone = val;
      },
    },
    data() {
      return {
        dialBtns: [1, 2, 3, 4, 5, 6, 7, 8, 9, '*', 0, '#'],
        displayPhone: '',
        secondCallPhone:"",
        position: {
          x: null,
          y: null,
        },
      };
    },
    methods: {
      handleDrag(event) {
        let panel = this.$refs['dial-panel'];
        switch (event.type) {
          case 'mousedown':
            if (event.path&&event.path[0] === panel) {
              this.position.x = panel.offsetLeft - event.x;
              this.position.y = panel.offsetTop - event.y;
              event.stopPropagation();
            }
            break;
          case 'mousemove':
            if (this.position.x && this.position.y) {
              panel.style.left = event.x + this.position.x + 'px';
              panel.style.top = event.y + this.position.y + 'px';
            }
            break;
          case 'mouseup':
            this.position.x = null;
            this.position.y = null;
            break;
          default:
            break;
        }
      },
      makeCall(phoneNo) {
        this.$emit('makeCall', phoneNo);
        this.onClose();
      },
      onBtnClick(btn) {
        if (!this.canChange) {
          this.secondCallPhone = this.secondCallPhone + '' + btn;
          this.$refs['phone2-input'].$el.querySelector('input').focus();
          return
        }
        this.displayPhone = this.displayPhone + '' + btn;
        this.$refs['phone-input'].$el.querySelector('input').focus();
      },
      onDeleteClick() {
        if (this.displayPhone.length === 0||!this.canChange) {
          return;
        }
        this.displayPhone = this.displayPhone.substr(0, this.displayPhone.length - 1);
      },
      onCallClick(plusZero) {
        if (plusZero === 0 && !this.displayPhone.startsWith('0')) {
          this.displayPhone = '0' + this.displayPhone;
        }
        if(this.displayPhone.split('-').length>0){
          this.makeCall(this.displayPhone.split('-')[0]);
          this.secondCallPhone = this.displayPhone.split('-')[1];
        }
        this.makeCall(this.displayPhone);
      },
      SendDTMF(){
        new Promise((reslove,reject)=>{
          window.application.oJVccBar.SendDTMF(this.secondCallPhone);
          this.secondCallPhone = "";
        }).then(res=>{
          this.secondCallPhone = "";
        })
      },
      onClose() {
        this.$emit('close');
      },
    },
    mounted() {
      document.addEventListener('mousedown', this.handleDrag);
      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.handleDrag);

      if (this.canChange) {
        this.displayPhone = this.phone;
      } else if(this.phoneSuggest.length>0){
        this.displayPhone = this.phoneSuggest[0].value;
      }
    },
    beforeDestroy() {
      document.removeEventListener('mousedown', this.handleDrag);
      document.removeEventListener('mousemove', this.handleDrag);
      document.removeEventListener('mouseup', this.handleDrag);
    },
  };
</script>
<style lang="stylus">
  #phone-input-id {
    .el-input {
      width 100%
    }
  }
</style>
<style lang="stylus" scoped>
  .el-select-dropdown__item .remark {
    color: #8492a6;
  }
  .el-select-dropdown__item.selected .remark{
    color: #ffffff
  }
  .dial-display {
    display: flex;
    width: 100%;
  }

  .dial-display > .phone-input {
    flex-grow: 2;
  }

  .dial-display > span {
    line-height: 22px;
  }

  .dial-panel {
    background #ffffff
    position fixed;
    top: 10%;
    left: 50%;
    transform translateX(-50%)
    border: #3a87ad solid 1px;
    width: 300px;
    padding: 22px 10px 10px 10px;
    border-radius 6px;
  }

  .dial-panel-btns {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px 20px;
    margin-top 16px;
  }

  .dial-panel-btns > li {
    border: #9ea9b5 solid 1px
    background #c9d3df
    border-radius 4px
    font-weight: bold;
    color: #2D64B3;
    line-height: 44px;
    text-align: center;

    &:active {
      color: #f43838;
      border-color #0E2D5F
    }
  }

  .dial-action {
    margin-top 16px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 10px
  }

  .dial-action > li {
    border-radius 4px
    line-height: 44px;
    text-align: center;
    cursor: pointer;
  }
</style>
