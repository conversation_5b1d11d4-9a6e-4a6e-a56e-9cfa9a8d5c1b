<!--问题处理人列表-->
<template>
	<div>
		<el-form ref='query'  label-position="right" label-width="120px" v-show="!params.isAlert">
			
			<el-col :span='6' v-if="isEmployee">
				<el-form-item label="分组" prop='groupName'>
					<el-input  size="mini"  v-model="search.groupName" placeholder="请输入分组名"></el-input>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="角色" prop='handlerRole'>
					<!-- <el-input size="mini"  v-model="search.handlerRole" placeholder="请输入角色"></el-input> -->
					<el-select
						v-model="search.handlerRole"
						size="mini"
						placeholder="请选择角色"
						>
							<el-option v-for="(value,key) in handlerRoleItem" :key="key" :label="value" :value="key"></el-option>
							
						</el-select>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-form-item label="人员名称" prop='memberName'>
					<el-input  size="mini"  v-model="search.memberName" placeholder="请输入人员名称"></el-input>
				</el-form-item>
			</el-col>
			<el-col :span='6'>
				<el-button size="mini" type="primary" @click="add" style="margin-top: 5px;">新增</el-button>
				<el-button size="mini" type="primary" @click="getPersonList" style="margin-top: 5px;">搜索</el-button>
				<el-button size="mini" type="primary" @click="reset" style="margin-top: 5px;">重置</el-button>
			</el-col>
		</el-form>

		<xpt-list
			ref='list' 
			:showHead='false'
			:data='list' 
			:btns='btns'
			:colData='colData' 
			:selection='selection' 
			:pageTotal='count' 
			@row-dblclick='rowDblclick' 
			@radio-change='handleCurrentChange' 
			@page-size-change='sizeChange' 
			@current-page-change='pageChange' 
		></xpt-list>
	</div>
</template>
<script>
	export default {
        props:['params'],
		data(){
			let self = this
			return {
				search:  {
					"groupName":"",             //分组名
					"handlerRole":"",        //角色，枚举:店长（SHOP_OWNER），话务员（CALL_ASSISTANT）
					"memberName":"",                //人员名称
					"page":1,                           
					"pageSize":50                       
				},
				handlerRoleItem:{
					SHOP_OWNER:"店长",
					CALL_ASSISTANT:"话务员",	
				},
				handlerOrderItem:{
					1:"一级",
					2:"二级",
					3:"三级",
					4:"四级",
					5:"五级",
					6:"六级",
					7:"七级",
					8:"八级",
					9:"九级",
					10:"十级",
				},
				
				list:[],
                count:0,
				isAlert:false,
				form:'',//哪个页面进入的弹窗组件
				selectData:'',
				btns: [],
				colData: [ {
					label: '分组名',
					prop: 'groupName',
				}, {
					label: '成员名称',
					prop: 'memberName',
				}, {
					label: '工号',
					prop: 'employeeNumber'
				},{
					label: '角色',
					prop: 'handlerRole',
					formatter(val){
						return self.handlerRoleItem[val];
					}
				}, {
					label: '问题优先处理级',
					prop: 'handlerOrder',
					formatter(val){
						return self.handlerOrderItem[val];
					}
				},],
				selection: 'radio',
				isEmployee:false
			}
		},
		methods:{
			reset(){
				this.search={
					"groupName":"",             //分组名
					"handlerRole":"",        //角色，枚举:店长（SHOP_OWNER），话务员（CALL_ASSISTANT）
					"memberName":"",                //人员名称
					"page":1,                           
					"pageSize":50                       
				}
			},
			/**
			*双击关闭弹窗
			*/
			rowDblclick(){
				this.closeAlert();
			},
			async add(){
				let params = {},
                self = this;
				params.close= d => {
					// console.log(d)
					self.getPersonList();
				}
				
				if(!this.isEmployee){
					try{
						params.groupName=await this.getGroupShop()||""
					}catch(e){
						console.log(e)
					}
					if(!params.groupName){
						this.$message.warning("当前代理人未维护有效的业绩店铺")
						return
					}
				}

				self.$root.eventHandle.$emit("alert", {
					params: params,
					component: () => import('@components/call_system/addProblemHandeler.vue'),
					style: "width:800px;height:500px",
					title: "新增问题处理人",
				});
			},
			sizeChange(size){
                // 每页加载数据
                this.search.pageSize = size;
                this.search.page = 1;
                this.getPersonList();
            },
            pageChange(page_no){
                // 页数改变
                this.search.page = page_no;
				this.getPersonList();
            },
            closeAlert(){
                // 关闭弹窗
                if(this.isAlert){
                    this.params.callback({data:this.operationList});
                    this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
                }
            },
            handleCurrentChange(val){
                // if(!this.isAlert) return;
                this.selectData = val;
			},
			async getPersonList(resolve){

				let result=false

				if(!this.isEmployee){
					try{
						result=await this.getGroupShop();
					}catch(e){
						console.log(e)
					}
					
					if(!result){
						return
					}
					this.search.groupName=result
				}
				
                var url = '/callcenter-web/api/cloudShopProblem/list.do';
                
                var data = !!this.params.isAlert?{groupName:this.params.groupName,isFilter:"Y"} :this.search ;
				
                
				var _this = this;
                this.ajax.postStream(url,data,function(d){
					var data = d.body;
					if(data.result){
                        _this.list = data.content.list||[]
                        _this.count = parseFloat(data.content.count);
						
						// })
					} else {
						_this.$message.error(data.msg || '');
					}
					// resolve && resolve();
                },function(data){
                    // resolve && resolve();
                })
			},
           
          
           
            select(selection){//发生改变时触发
               
                this.operationList = selection.length?selection:[];
			},
			//判断是否为内部员工
			handleIsEmployee(){
				let staffNo= this.getEmployeeInfo().employeeNumber||""
				if(!staffNo){
					return
				}
				return new Promise((resolve,reject)=>{
					this.ajax.postStream('/user-web/api/userPerson/getUserPersonList', {"employeeNumber":staffNo}, res => {
						if(res.body.result&&res.body.content&&res.body.content.list){
							let result=res.body.content.list[0].type == "EMPLOYEE"
							resolve(result);
						}else{
							reject(false);
						}
					},err=>{
						this.$message.error(err)
						reject(false);
					})
				})
			},
			getGroupShop(){
				return new Promise((resolve,reject)=>{
					this.ajax.postStream('/user-web/api/userPerson/getUserPersonShopList', {
						"page": {
						"length": 50,
						"pageNo": 1
						},
						"personId": this.getEmployeeInfo('personId'),
						"isEnable": 1
					}, res => {
						if(res.body.result&&res.body.content&&res.body.content.list){
							resolve(res.body.content.list[0].shopName);
						}else{
							this.$message.error(res.body.msg||'当前代理人未维护有效的业绩店铺')
							reject(false);
						}
					},err=>{
						this.$message.error(err)
						reject(false);
					})
				})
			}
           
		},
		async mounted() {
			//从其它组件里传过来的参数，调用接口
			var _this = this;
			console.log(this.params)
			this.isEmployee=await this.handleIsEmployee();
			console.log(this.isEmployee)
            this.getPersonList();
			this.$root.eventHandle.$on("resetAllBtnStatus", async () => {
				this.search.groupName="";
				this.isEmployee=await this.handleIsEmployee();
				this.getPersonList();
			});
		},
	
       
	}
</script>
