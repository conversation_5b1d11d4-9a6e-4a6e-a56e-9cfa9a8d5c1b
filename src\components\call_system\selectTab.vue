<template>
<div class="select-tab">
  <ul>
    <li @click="switchTab(item)" v-for="(item, index) in tabs" :key="index" :class="{'active': selectedCode == item.code}">{{item.name}}</li>
  </ul>
</div>
</template>

<script>
export default {
  props: {
    selectedCode: String,
    tabs: Array
  },
  methods: {
    switchTab (item) {
      this.$emit('changeTab', item)
    }
  }
}
</script>

<style scoped>
  .select-tab {
    position: relative;
    top: 0;
    height: 26px;
  }
  .select-tab li{
    height: 26px;
    line-height: 26px;
    padding: 0 10px;
    display: inline-block;
    float: left;
    cursor: pointer;
    border: solid transparent 2px;
  }
  .select-tab li.active{
    color: #20a0ff;
    border-bottom: solid #20a0ff 2px;
  }
</style>
