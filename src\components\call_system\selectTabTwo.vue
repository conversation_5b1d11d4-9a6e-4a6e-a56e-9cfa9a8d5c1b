<template>
  <div class="select-tab-two">
        <span v-for='(item,index) in tabsTwo' :key="index"
              @click="selectedTabsTwo(item.code)"
              :class="{'active': (currentComponent === item.code)}">{{item.name}}</span>
  </div>
</template>

<script>
export default {
  props: {
    tabsTwo: Array,
    currentComponent: String
  },
  methods: {
    selectedTabsTwo (code) {
      this.$emit('changeComponent', code)
    }
  }
}
</script>

<style scoped>
  .select-tab-two{
    position: relative;
    top: 0;
    height: 26px;
  }
  .select-tab-two span{
    height: 26px;
    line-height: 26px;
    padding: 0 10px;
    display: inline-block;
    float: left;
    cursor: pointer;
    border: solid transparent 2px;
  }
  .select-tab-two span.active{
    color: #20a0ff;
    border-bottom: solid #20a0ff 2px;
  }
</style>
