import PhoneBar from "../phoneBar";

export default {
  //在呼叫弹窗已经打开的情况下进行的另一层校验
  verifyExhaleKeybord1(self) {
    // 通过全局的事件总线拉取弹窗的状态
    let {callPopupIfEnlarge, callPopupParamsList} = self.$store.state.common;

    // 当弹窗的为缩小状态的时候直接放大
    if (!callPopupIfEnlarge) self.$bus.emit('interactionPopup', {useModule: 1, size: 'big'});

    /**
     * 1. 电话条状态为通话中，不做任何处理
     * 2. 还有尚未保存的变更，弹窗提示
     * 3. else 传入最新的界面数据
     */
    let phoneBarStatus = PhoneBar.getStatus();
    if (phoneBarStatus === 3) {
      // 通话中
      self.$message.warning("通话中！");
    } else if (callPopupParamsList.bgntime || callPopupParamsList.endtime || callPopupParamsList.filename) {
      self.$message.warning("请先保存相关通话信息");
    } else {
      self.gatherData();
    }
  },
}
