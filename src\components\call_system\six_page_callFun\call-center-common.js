export default{
  computed:{
    isNewCallCenter(){
      return true;
      const item=__AUX.get('CALL_CENTER_SWITCH').find(item=>item.code==='OUT_CALL')
      const pageItem=__AUX.get('CALL_CENTER_SWITCH').find(item=>item.code==='OUT_CALL_PAGE')
      const pageList=pageItem.ext_field1.split(",")||[]
      const usePage=this.activeTabName&&pageList.includes(this.activeTabName)&&pageItem.status===1 || pageItem.status!==1
      return item.status===1&&usePage
    }
  },
}
