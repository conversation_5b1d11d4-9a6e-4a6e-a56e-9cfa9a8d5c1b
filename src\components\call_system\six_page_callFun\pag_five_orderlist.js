/**
 * 工单记录列表
 * call_order_list
 */
import Fn from '@/common/Fn.js';
import {
  makeUrl,
  apiUrl,
  EventBus
} from '../base.js';
import PhoneBar from "../phoneBar";
import basicFun from "./basicFun";
import callCenterCommon from './call-center-common.js';
export default {
  mixins:[callCenterCommon],
  data() {
    const self = this;
    return {
      callBtnGroupsDisabled: false,
      // 呼叫功能按钮组
      callBtnGroups: [{
          type: 'success',
          txt: ' 呼 出 ',
          code: 'call',
          loading: false,
          disabled: () => false,
          click() {
            self.exhaleKeybord();
          }
        },
        {
          type: 'info',
          txt: '呼出记录',
          code: 'callRecord',
          loading: false,
          disabled: () => false,
          click() {
            self.turnOnCallOutList();
          }
        }
      ],
      pageTwoDiaConfig: this.diaConfig,
      upDateListLong: true, //更新呼出记录数量
    };
  },
  inject: ['diaConfig'],
  watch: {},
  methods: {
    // 呼出键盘
    exhaleKeybord() {
      const self = this;
      // 当呼叫弹窗已经打开的手时候进入另一层的校验
      if (this.pageTwoDiaConfig.dialogVisible) {
        basicFun.verifyExhaleKeybord1(self);
        // this.$message.warning('呼叫面板已打开');
        // return;
      } else if (!this.pageTwoDiaConfig.dialogVisible) {
        // 弹窗尚未打开的情况下，收集信息打开弹窗
        this.gatherData();
        this.$bus.emit('changeCallProp', true);
      };
    },
    // 收集信息
    gatherData() {
        let formCol = [];
        let formData = {
            phone: "13888888888"
        };
        this.$bus.emit('coverColAndData', formCol, formData);
    },
    // 打开呼叫记录列表
    turnOnCallOutList() {
      let listData = [];
      let searchParams = {};
      let componentsUrl='';
      let params = {
        listData,
        searchParams
      };
      if(this.isNewCallCenter){
        componentsUrl='out-call-list'
      }else{
        componentsUrl='call_out_list'
      }
      // creatTab
      this.$root.eventHandle.$emit('alert', {
        title: '呼出记录列表',
        params: params,
        style: 'width:800px;height:400px',
        component: () => import(`@components/call_system/call_popup/${componentsUrl}.vue`)
      });
    },
    // 刷新呼叫记录列表
    refreshCallList() {
    },
    // 打开按钮组前执行
    beforeClick() {
      // 5s内呼出按钮组，不重新获取列表长度
      if (!this.upDateListLong) {
        return
      } else {
        this.upDateListLong = false;
      }
      setTimeout(() => {
        this.upDateListLong = true;
      }, 5000)

      this.refreshCallList();
    }
  },
  mounted() {},
  destroyed() {}
};
