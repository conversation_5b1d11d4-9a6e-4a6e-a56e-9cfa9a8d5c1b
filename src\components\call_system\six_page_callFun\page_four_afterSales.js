/**
 * 第四页面 -> 售后详情页面
 * after_sales/index.vue
 */
import Fn from '@/common/Fn.js';
import { makeUrl, apiUrl, EventBus } from '../base.js';
import basicFun from "./basicFun";
import callCenterCommon from './call-center-common.js';
export default {
  mixins:[callCenterCommon],
	data() {
		const self = this;
		return {
			// 呼叫按钮按钮组整体禁用
			callBtnGroupsDisabled: true,
			// 呼叫功能按钮组
			callBtnGroups: [
				{
					type: 'success',
					txt: ' 呼 出 ',
					code: 'call',
					loading: false,
					disabled: () => false,
					click() {
						self.exhaleKeybord();
					}
				},
				{
					type: 'info',
					txt: '呼出记录',
					code: 'callRecord',
					loading: false,
					disabled: () => false,
					click() {
						self.turnOnCallOutList();
					}
				}
			],
			pageFourDiaConfig: this.diaConfig,
      upDateListLong:true, //更新呼出记录数量
		};
	},
	inject: [ 'diaConfig' ],
	watch: {
		// callBtnGroupsDisabled(newVal) {
		// 	if (newVal == false) {
		// 		this.refreshCallList();
		// 	}
		// }
	},
	methods: {
		// 呼出键盘
		exhaleKeybord() {
      const self = this;
      // 当呼叫弹窗已经打开的手时候进入另一层的校验
      if (this.pageFourDiaConfig.dialogVisible) {
        basicFun.verifyExhaleKeybord1(self);
        // this.$message.warning('呼叫面板已打开');
        // return;
      } else if(!this.pageFourDiaConfig.dialogVisible){
        // 弹窗尚未打开的情况下，收集信息打开弹窗
        this.gatherData();
        this.$bus.emit('changeCallProp', true);
      };
		},
		// 收集信息
		gatherData() {
			// 下面开始，字段信息会很紊乱
			let formCol = [
				{
					label: 'type',
					value: '来源类型'
				},
				{
					label: 'typeNum',
					value: '单据编号'
				},
				{
					label: 'nickName',
					value: '客户昵称'
				},
				{
					label: 'name',
					value: '收货人'
				},
				{
					label: 'bookingDate',
					value: '支付时间'
				},
				{
					label: 'phone',
					value: '手机号码'
				},
				{
					label: 'address',
					value: '收货地址'
				}
			];

			let formData = {
				type: '售后单',
				typeNum: this.form.after_order_no, //业务单号
				nickName: this.form.buyer_nick,
				name: this.form.receiver_name,
				phone: [
					`${this.form.receiver_mobile}  ${this.form.receiver_name}`
				],
        bookingDate: Fn.dateFormat(this.form.pre_order_time, 'yyyy-MM-dd hh:mm:ss'),
				address: this.form.address_name,
				// add.do 的数据
				// calledNumber
				sourceType: 'after_sales',
				callingTpe: '呼出',
				mergeTradeId: this.form.merge_trade_id,
				mergeTradeNo: this.form.merge_trade_no,
        address_id:this.form.address_id,
      //  当当前的订单是售后订单的时候，是不需要考虑电话号码是否需要映射到下面列表这个问题的
        notInitChoice: true,
			};
			this.$bus.emit('coverColAndData', formCol, formData);
		},
		// 打开呼叫记录列表
		turnOnCallOutList() {
			let listData = [
				{
					label: '售后单号',
					value: this.form.after_order_no
				},
				{
					label: '客户姓名',
					value: this.form.receiver_name
				}
			];
      let searchParams={};
      let componentsUrl='';
      if(this.isNewCallCenter){
        searchParams={
          externalNo: this.form.after_order_no, //业务单号
          sourceType: 'after_sales'
        }
        componentsUrl='out-call-list'
      }else{
        searchParams = {
          	tid: this.form.after_order_no || '', //业务单号
          	source_type: 'after_sales' //来源类型
        };
        componentsUrl='call_out_list'
      }
			let params = {
				listData,
				searchParams
			};
			// creatTab
			this.$root.eventHandle.$emit('alert', {
				title: '呼出记录列表',
				params: params,
				style: 'width:800px;height:400px',
				component: () => import(`@components/call_system/call_popup/${componentsUrl}.vue`)
			});
		},
		// 刷新呼叫记录列表
		refreshCallList() {
			let params = {
				tid: this.form.after_order_no || '', //业务单号
				source_type: 'after_sales' //来源类型
			};
			this.$http
				.get(apiUrl.callLog_count, { params })
				.then((res) => {
					if (res.data.result) {
						let num = res.data.content;
						this.callBtnGroups[1].txt = '呼出记录(' + num + ')';
					} else {
						this.$message.error(res.data.msg);
					}
				})
				.catch((err) => {
          // this.$message.error(`呼叫中心报错: ${err.status}${err.statusText}`);
				})
				.finally(() => {});
		},
    // 呼叫新总数
    refreshCallProList(){
      const params={
        externalNo: this.form.after_order_no, //业务单号
        sourceType: 'after_sales'
      }
      this.$http.post(apiUrl.callLog_count_pro, params).then(res => {
        if (res.data.result) {
          let num = res.data.content||0;
          this.callBtnGroups[1].txt = '呼出记录(' + num + ')';
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 打开按钮组前执行
    beforeClick() {
      // 5s内呼出按钮组，不重新获取列表长度
      if(!this.upDateListLong){
        return
      }else{
        this.upDateListLong = false;
      }
      setTimeout(()=>{
        this.upDateListLong = true;
      },5000)

      if(this.isNewCallCenter){
        this.refreshCallProList();
      }else{
        this.refreshCallList();
      }
    }
	},
	mounted() {},
	destroyed() {}
};
