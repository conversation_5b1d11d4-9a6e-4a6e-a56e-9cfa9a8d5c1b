/**
 * 第七个页面 -> 回访单详情页面
 * returnvisit/add.vue
 */
import Fn from '@/common/Fn.js';
import { makeUrl, apiUrl, EventBus } from '../base.js';
import PhoneBar from "../phoneBar";
import callCenterCommon from './call-center-common.js';
export default {
  mixins:[callCenterCommon],
	data() {
		const self = this;
		return {
			// 呼叫按钮按钮组整体禁用
			callBtnGroupsDisabled: true,
			// 呼叫功能按钮组
			callBtnGroups: [
				{
					type: 'success',
					txt: ' 呼 出 ',
					code: 'call',
					loading: false,
					disabled: () => false,
					click() {
						self.exhaleKeybord();
					}
				},
				{
					type: 'info',
					txt: '呼出记录',
					code: 'callRecord',
					loading: false,
					disabled: () => false,
					click() {
						self.turnOnCallOutList();
					}
				}
			],
			pageOneDiaConfig: this.diaConfig,
      upDateListLong:true, //更新呼出记录数量
		};
	},
	inject: [ 'diaConfig' ],
	watch: {},
	methods: {
		// 呼出键盘
		exhaleKeybord() {
		  // 弹窗是否已经打开
			if (this.pageOneDiaConfig.dialogVisible) {
			  this.verifyExhaleKeybord1();
				// this.$message.warning('呼叫面板已打开');
				// return;
			}else if(!this.pageOneDiaConfig.dialogVisible){
			  // 弹窗尚未打开的情况下，收集信息打开弹窗
        this.gatherData();
        this.$bus.emit('changeCallProp', true);
      }
		},
    // 呼出键盘已经打开的情况下需要进行另一层打开的校验
    verifyExhaleKeybord1(){
      // 通过全局的事件总线拉取弹窗的状态
      let {callPopupIfEnlarge,callPopupParamsList} = this.$store.state.common;

      // 当弹窗的为缩小状态的时候直接放大
      if(!callPopupIfEnlarge)  this.$bus.emit('interactionPopup',{ useModule:1,size:'big' });

      /**
       * 1. 电话条状态为通话中，不做任何处理
       * 2. 还有尚未保存的变更，弹窗提示
       * 3. else 传入最新的界面数据
       */
      let phoneBarStatus = PhoneBar.getStatus();
      if(phoneBarStatus === 3){
        // 通话中
        this.$message.warning("通话中！");
      }else if(callPopupParamsList.bgntime || callPopupParamsList.endtime || callPopupParamsList.filename){
        this.$message.warning("请先保存相关通话信息");
      }else{
        this.gatherData();
      }
    },
		// 收集信息
		gatherData() {
			// 下面开始，字段信息会很紊乱
			let formCol = [
				{
					label: 'type',
					value: '来源类型'
				},
				{
					label: 'typeNum',
					value: '单据编号'
				},
				{
					label: 'nickName',
					value: '客户昵称'
				},
				{
					label: 'name',
					value: '收货人'
				},

				{
					label: 'phone',
					value: '手机号码'
				},

			];
			let formData = {
				type: '客户售后反馈单',
				typeNum: this.submitData.order_no, //业务单号
				nickName: this.submitData.buyer_name,
				name: this.submitData.buyer_name,
				phone: [ `${this.submitData.phone}` ],
				// payTime:
				// 	this.paymentList.length == 0
				// 		? ''
				// 		: Fn.dateFormat(
				// 				this.paymentList[this.paymentList.length - 1].payment_time,
				// 				'yyyy-MM-dd hh:mm:ss'
				// 			),
				// address: this.batchAddressData.receiverInfoVO.receiver_address,
				// add.do 的数据
				// calledNumber
				sourceType: 'customer_feedback',
				callingTpe: '呼出',
				// mergeTradeId: this.submitData.merge_trade_id,
				// mergeTradeNo: this.submitData.merge_trade_no,
				tid:this.submitData.order_no,
			};
			this.$bus.emit('coverColAndData', formCol, formData);
		},
		// 打开呼叫记录列表
		turnOnCallOutList() {
			let listData = [
				{
					label: '客户售后反馈单号',
					value: this.submitData.order_no
				},
				{
					label: '客户姓名',
					value: this.submitData.buyer_name
				},
				{
					label: '业务员',
					value: this.submitData.staff_name
				}
			];
      let searchParams={};
      let componentsUrl='';
      if(this.isNewCallCenter){
        searchParams={
          externalNo: this.submitData.order_no, //业务单号
          sourceType: 'customer_feedback'
        }
        componentsUrl='out-call-list'
      }else{
         searchParams = {
          tid: this.submitData.order_no || '', //业务单号
          source_type: 'customer_feedback' //来源类型
        };
        componentsUrl='call_out_list'
      }

			let params = {
				listData,
				searchParams
			};
			// creatTab
			this.$root.eventHandle.$emit('alert', {
				title: '呼出记录列表',
				params: params,
				style: 'width:800px;height:400px',
				component: () => import(`@components/call_system/call_popup/${componentsUrl}.vue`)
			});
		},
		// 刷新呼叫记录列表
		refreshCallList() {
			let params = {
				tid: this.submitData.order_no || '', //业务单号
				source_type: 'customer_feedback' //来源类型
      };
			this.$http
				.get(apiUrl.callLog_count, { params })
				.then((res) => {
					if (res.data.result) {
            let num = res.data.content;
            this.callBtnGroups[1].txt = '呼出记录('+num+')';
					} else {
						this.$message.error(res.data.msg);
					}
				})
				.catch((err) => {
					// this.$message.error(`呼叫中心报错: ${err.status}${err.statusText}`);
				})
				.finally(() => {});
		},
     // 呼叫新总数
     refreshCallProList(){
      const params={
        externalNo: this.submitData.order_no, //业务单号
        sourceType: 'customer_feedback'
      }
      this.$http.post(apiUrl.callLog_count_pro, params).then(res => {
        if (res.data.result) {
          let num = res.data.content||0;
          this.callBtnGroups[1].txt = '呼出记录(' + num + ')';
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 打开按钮组前执行
    beforeClick() {
		  // 5s内呼出按钮组，不重新获取列表长度
		  if(!this.upDateListLong){
		    return
      }else{
		    this.upDateListLong = false;
      }
		  setTimeout(()=>{
        this.upDateListLong = true;
      },5000)

      if(this.isNewCallCenter){
        this.refreshCallProList();
      }else{
        this.refreshCallList();
      }
    }
	},
	mounted() {
  },
  beforeDestroy() {
  }
};
