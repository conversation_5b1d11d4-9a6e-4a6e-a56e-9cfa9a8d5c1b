/**
 * 第六个页面 -> 话务留资列表
 * appoint_management/call_infomation.vue
 */
import Fn from '@/common/Fn.js';
import { makeUrl, apiUrl, EventBus } from '../base.js';
import basicFun from "./basicFun";
export default {
	data() {
		const self = this;
		return {
			// 呼叫按钮按钮组整体禁用
			callBtnGroupsDisabled: true,
			pageSixDiaConfig: this.diaConfig
		};
	},
	inject: [ 'diaConfig' ],
	watch: {},
	methods: {
		// 呼出键盘
		exhaleKeybord() {
      if (this.selectRow.length == 0) {
        this.$message.warning('尚未选择数据');
        return;
      } else if (this.selectRow.length > 1) {
        this.$message.warning('只能选择一条数据');
        return;
      }

      const self = this;
      // 当呼叫弹窗已经打开的手时候进入另一层的校验
      if (this.pageSixDiaConfig.dialogVisible) {
        basicFun.verifyExhaleKeybord1(self);
        // this.$message.warning('呼叫面板已打开');
        // return;
      } else if(!this.pageSixDiaConfig.dialogVisible){
        // 弹窗尚未打开的情况下，收集信息打开弹窗
        this.gatherData();
        this.$bus.emit('changeCallProp', true);
      };
		},
		// 收集信息
		gatherData() {
			// 下面开始，字段信息会很紊乱
			let formCol = [
				{
					label: 'type',
					value: '来源类型'
				},
				// {
				// 	label: 'typeNum',
				// 	value: '单据编号'
				// },
				// {
				// 	label: 'nickName',
				// 	value: '客户昵称'
				// },
				{
					label: 'bookStore',
					value: '预约店铺'
				},
				{
					label: 'from',
					value: '来源渠道'
				},
				{
					label: 'name',
					value: '客户姓名'
				},
				{
					label: 'phone',
					value: '手机号码'
				},
				{
					label: 'bookingTime',
					value: '分配时间'
				},
				{
					label: 'tel_operator_name',
					value: '话务员'
				},
				{
					label: 'submitTime',
					value: '预约时间'
				}
				// {
				// 	label: 'address',
				// 	value: '收货地址'
				// }
			];
			let formData = {
				type: '话务留资列表',
				typeNum: `${this.selectRow[0].id}`, //业务单号
				bookStore: this.selectRow[0].appointment_shop_activity_name,
				from: this.selectRow[0].appointment_channel_name,
				name: this.selectRow[0].name,
				phone: [ `${this.selectRow[0].mobile}  ${this.selectRow[0].name}` ],
				bookingTime: Fn.dateFormat(this.selectRow[0].tel_assign_time, 'yyyy-MM-dd hh:mm:ss'),
				submitTime: Fn.dateFormat(this.selectRow[0].appointment_submit_time, 'yyyy-MM-dd hh:mm:ss'),
				tel_operator_name: this.selectRow[0].tel_operator_name,

				// add.do 的数据
				// calledNumber
				sourceType: 'call_infomation',
				callingTpe: '呼出',
				mergeTradeId: '',
				mergeTradeNo: ''
			};
			this.$bus.emit('coverColAndData', formCol, formData);
		},
		// 打开呼叫记录列表
		turnOnCallOutList() {
			if (this.selectRow.length == 0) {
				this.$message.warning('尚未选择数据');
				return;
			} else if (this.selectRow.length > 1) {
				this.$message.warning('只能选择一条数据');
				return;
			}
			let listData = [
				// {
				// 	label: '售后单号',
				// 	value: this.form.after_order_no
				// },
				{
					label: '客户姓名',
					value: this.selectRow[0].name
				},
				{
					label: '话务员',
					value: this.selectRow[0].tel_operator_name
				}
			];
			let searchParams = {
				tid: `${this.selectRow[0].id}`, //业务单号
				// handle_name: this.getEmployeeInfo('fullName'), //处理人
				// calling_type: '呼出', //来电类型
				// after_plan_id: '', //售后单id
				source_type: 'call_infomation' //来源类型
			};
			let params = {
				listData,
				searchParams
			};
			// creatTab
			this.$root.eventHandle.$emit('alert', {
				title: '呼出记录列表',
				params: params,
				style: 'width:800px;height:400px',
				component: () => import('@components/call_system/call_popup/call_out_list.vue')
			});
		}
		// 刷新呼叫记录列表
		// refreshCallList() {
		// 	let params = {
		// 		tid: `${this.selectRow[0].id}`, //业务单号
		// 		// handle_name: this.getEmployeeInfo('fullName'), //处理人
		// 		// calling_type: '呼出', //来电类型
		// 		// after_plan_id: '', //售后单id
		// 		source_type: 'call_infomation' //来源类型
		// 	};
		// 	this.$http
		// 		.get(apiUrl.callLog_count, { params })
		// 		.then((res) => {
		// 			if (res.data.result) {
		// 				let num = res.data.content;
		// 				this.btns[2].btnGroupList[1].txt = '呼出记录(' + num + ')';
		// 			} else {
		// 				this.$message.error('呼叫中心报错: ',err.status,err.message);
		// 			}
		// 		})
		// 		.catch((err) => {
		// 			this.$message.error(err);
		// 		})
		// 		.finally(() => {});
		// }
	},
	mounted() {},
	destroyed() {}
};
