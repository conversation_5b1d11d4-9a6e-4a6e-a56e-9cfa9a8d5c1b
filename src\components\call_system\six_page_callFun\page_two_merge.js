/**
 * 第二个页面 -> 合并订单
 * order/merge.vue
 * 第五个页面 -> 订单核对任务列表 也是跳转到合并订单
 */
import Fn from '@/common/Fn.js';
import { makeUrl, apiUrl, EventBus } from '../base.js';
import PhoneBar from "../phoneBar";
import basicFun from "./basicFun";
import callCenterCommon from './call-center-common.js';
export default {
  mixins:[callCenterCommon],
	data() {
		const self = this;
		return {
			// 呼叫按钮按钮组整体禁用
			// callBtnGroupsDisabled: true,
			callBtnCustomerDisabled: true,
			callBtnGoodsDisabled: true,
			callBtnMergeOrderDisabled: true,
			// 呼叫功能按钮组
			callBtnGroups: [
				{
					type: 'success',
					txt: ' 呼 出 ',
					code: 'call',
					loading: false,
					disabled: () => false,
					click() {
						self.exhaleKeybord();
					}
				},
				{
					type: 'info',
					txt: '呼出记录',
					code: 'callRecord',
					loading: false,
					disabled: () => false,
					click() {
						self.turnOnCallOutList();
					}
				}
			],
			pageTwoDiaConfig: this.diaConfig,
      upDateListLong:true, //更新呼出记录数量
		};
	},
	inject: [ 'diaConfig' ],
	watch: {},
	computed: {
		callBtnGroupsDisabled: function() {
			if (this.callBtnCustomerDisabled || this.callBtnGoodsDisabled || this.callBtnMergeOrderDisabled || !this.params.callBtnfromType) {
				return true;
			} else {
				return false;
			}
		}
	},
	methods: {
		// 呼出键盘
		async exhaleKeybord() {
		  const self = this;
		  // 当呼叫弹窗已经打开的手时候进入另一层的校验
			if (this.pageTwoDiaConfig.dialogVisible) {
			  basicFun.verifyExhaleKeybord1(self);
				// this.$message.warning('呼叫面板已打开');
				// return;
			} else if(!this.pageTwoDiaConfig.dialogVisible){
        // 弹窗尚未打开的情况下，收集信息打开弹窗
        this.gatherData();
        this.$bus.emit('changeCallProp', true);
      };
		},
		// 收集信息
		gatherData() {
			let formCol = [
				{
					label: 'type',
					value: '来源类型'
				},
				{
					label: 'typeNum',
					value: '单据编号'
				},
				{
					label: 'nickName',
					value: '客户信息'
				},
				// {
				// 	label: 'name',
				// 	value: '收货人'
				// },
				// {
				// 	label: 'payTime',
				// 	value: '支付时间'
				// },
				{
					label: 'phone',
					value: '手机号码'
				}
				// {
				// 	label: 'address',
				// 	value: '收货地址'
				// }
			];
			// 商品行的地址值
			let goodsDetail = this.goodsList.map((i) => i.address_number).filter((i1) => i1 && i1.trim());
      let address_id = '';
			let addressDetail = this.customerList.map((i,index) => {
        if(index===0){
          address_id = i.fid
        }
				return {
					number: i.number,
					receiver_name: i.receiver_name,
					receiver_mobile: i.receiver_mobile,
          address_id:i.fid
				};
			});

			let formData = {
				typeNum: this.mergeTradeNo, //合并订单单号
				nickName: this.mergeDetailInfo.customer_name, //客户信息\
				// add.do 的数据
				// calledNumber //拨打的电话
				sourceType: this.params.callBtnfromType,
				callingTpe: '呼出',
				mergeTradeId: this.merge_trade_id,
				mergeTradeNo: this.mergeTradeNo,
        address_id,
				tid: ''
			};

			if (this.params.callBtnfromType == 'sys_order') {
				formData.type = '销售订单';
			} else if (this.params.callBtnfromType == 'order_check'){
        formData.type = '订单核对任务列表';
      }

			if (goodsDetail.length == 0) {
				this.$message.warning('商品行地址信息为空，直接提取地址信息');
				formData.phone = addressDetail.map((item) => `${item.receiver_mobile}  ${item.receiver_name}`);
			} else {
				// 去个重
				goodsDetail = Array.from(new Set(goodsDetail));
				let phoneBig = [];
				addressDetail.forEach((item) => {
					if (goodsDetail.indexOf(item.number) != -1) {
						phoneBig.push(`${item.receiver_mobile}  ${item.receiver_name}`);
					}
				});
				formData.phone = phoneBig;
			}
			// 最后再去一遍重
			formData.phone = Array.from(new Set(formData.phone));
			this.$bus.emit('coverColAndData', formCol, formData);
		},
		// 打开呼叫记录列表
		turnOnCallOutList() {
			let listData = [
				{
					label: '合并单单号',
					value: this.mergeTradeNo
				},
				{
					label: '客户姓名',
					value: this.mergeDetailInfo.customer_name
				},
				{
					label: '订单店铺',
					value: this.mergeDetailInfo.shop_name
				},
				{
					label: '原始店铺',
					value: this.mergeDetailInfo.original_shop_name
				}
			];
      let searchParams={};
      let componentsUrl='';
      if(this.isNewCallCenter){
        searchParams={
          externalNo: this.mergeTradeNo, //业务单号
          sourceType: this.params.callBtnfromType
        }
        componentsUrl='out-call-list'
      }else{
        searchParams = {
          	tid: this.mergeTradeNo || '', //业务单号
          	source_type: this.params.callBtnfromType //来源类型
        };
        componentsUrl='call_out_list'
      }
			let params = {
				listData,
				searchParams
			};
			// creatTab
			this.$root.eventHandle.$emit('alert', {
				title: '呼出记录列表',
				params: params,
				style: 'width:800px;height:400px',
				component: () => import(`@components/call_system/call_popup/${componentsUrl}.vue`)
			});
		},
		// 刷新呼叫记录列表
		refreshCallList() {
			let params = {
				tid: this.mergeTradeNo || '', //业务单号
				source_type: this.params.callBtnfromType //来源类型
			};
			this.$http
				.get(apiUrl.callLog_count, { params })
				.then((res) => {
					if (res.data.result) {
            let num = res.data.content;
            this.callBtnGroups[1].txt = '呼出记录('+num+')';
					} else {
						this.$message.error(res.data.msg);
					}
				})
				.catch((err) => {
          // this.$message.error(`呼叫中心报错: ${err.status}${err.statusText}`);
				})
				.finally(() => {});
		},
     // 呼叫新总数
     refreshCallProList(){
      const params={
        externalNo: this.mergeTradeNo, //业务单号
        sourceType: this.params.callBtnfromType
      }
      this.$http.post(apiUrl.callLog_count_pro, params).then(res => {
        if (res.data.result) {
          let num = res.data.content||0;
          this.callBtnGroups[1].txt = '呼出记录(' + num + ')';
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 打开按钮组前执行
    beforeClick() {
      // 5s内呼出按钮组，不重新获取列表长度
      if(!this.upDateListLong){
        return
      }else{
        this.upDateListLong = false;
      }
      setTimeout(()=>{
        this.upDateListLong = true;
      },5000)

      if(this.isNewCallCenter){
        this.refreshCallProList();
      }else{
        this.refreshCallList();
      }
    }
	},
	mounted() {},
	destroyed() {}
};
