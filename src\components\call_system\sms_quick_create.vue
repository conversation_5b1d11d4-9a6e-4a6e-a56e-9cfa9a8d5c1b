<template>
  <div class="xpt-flex">
    <div class="sms-quick-create">
    <el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<call-btn :btns="btns" ></call-btn>
			</el-col>
		</el-row>
    <el-row :gutter='40'>
      <!-- <el-tabs v-model="firstTab" > -->
        <!-- <el-tab-pane label="选择短信类型" name="orderInfo"> -->
        <el-form ref="form" :model="formDemo" :rules="rules" label-width="120px" class="form-box">
            <el-col :span='8'>
              <el-form-item label="模板类型" style="height:50px;">
                <el-select v-model="tmplId" placeholder="请选择" size="mini" class="search-input" @change="tmplChange" :disabled="params.type == 'edit'">
                  <el-option
                    v-for="item in tmpls"
                    :key="item.tmplId"
                    :label="item.typeName"
                    :value="item.tmplId">
                  </el-option>
                </el-select>
                <!-- <el-select v-model="tmplId" placeholder="请选择" size="mini" class="search-input" @change="tmplChange">
                  <el-option
                    v-for="item in tmpls"
                    :key="item.tmplId"
                    :label="item.typeCode"
                    :value="item.tmplId">
                  </el-option>
                </el-select> -->
                <!-- <el-tooltip v-if='rules.typeCode[0].isShow' class="item" effect="dark" :content="rules.typeCode[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
                </el-tooltip> -->
              </el-form-item>
              <el-form-item label="短语简称" prop="usefulWordingName" style="height:50px;">
                <el-input class="search-input"  size="mini" v-model="formDemo.usefulWordingName" placeholder="请输入短语名称"></el-input>
                <el-tooltip v-if='rules.usefulWordingName[0].isShow' class="item" effect="dark" :content="rules.usefulWordingName[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="短语内容" prop="usefulWordingContent" style="height:120px;">
                <el-input type="textarea" v-model="formDemo.usefulWordingContent" placeholder="请编辑短语内容" :rows='4' resize='none' style="width:320px;"></el-input>
                <el-tooltip v-if='rules.usefulWordingContent[0].isShow' class="item" effect="dark" :content="rules.usefulWordingContent[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <!-- <el-col :span='8'>
              <el-form-item label="类型名称" style="height:50px;" v-if="false">
                <el-input class="search-input" :disabled="true" size="mini" v-model="tmplMap[this.tmplId].typeName" placeholder="请输入类型名称"></el-input>
              </el-form-item>
            </el-col> -->



        </el-form>
        <!-- </el-tab-pane> -->
      <!-- </el-tabs> -->
    </el-row>
    </div>
  </div>
</template>

<script>
  import baseUrl, {makeUrl, apiUrl} from '../call_system/base.js'
  import 'element-ui/lib/theme-default/index.css'
  import callBtn from '@/components/call_system/callBtn'
  import callRecord from '@/components/call_system/call_record'
  import smsSearch from './sms_search'
  import selectTab from '@/components/call_system/selectTab'
  import validate from '@common/validate.js'

  export default {
    props:["params"],
    components: {
      callBtn,
      callRecord,
      smsSearch,
      selectTab
    },
    data () {
      var _this=this
      return {
        // params:{},
        firstTab:"orderInfo",
        btns: [
          {
            type: 'info',
            txt: '关闭',
            click () {
              _this.goBack()
            },
            show: false
          },
          {
            type: 'info',
            txt: '保存',
            click () {
              _this.submitForm()
            },
            show: false
          }
        ],
        form: {
          typeId:'',
          name: '',
          phoneType: '',
          compile:false,
          takeEffect: false,
          desc: ''
        },
        formDemo:{
          usefulWordingId:'',
          usefulWordingName:'',
          usefulWordingContent:'',
          smsTmpl:{
          }
        },
        tmpls:[

        ],
        loading:true,
        tmplMap:{},
        tmplId:'',
        rules: {
          usefulWordingName: validate.isNotBlank({

              self:_this,
              msg:'请输入短语名称',
              trigger: 'blur'
					}),
          usefulWordingContent: validate.isNotBlank({

              self:_this,
              msg:'请输入短信内容',
              trigger: 'blur'
					}),
        }
      }
    },
    methods:{
      // 取消返回
      goBack(){
        this.$root.eventHandle.$emit('removeTab', this.params.tabName);
      },
      // 确定，提交表单
      submitForm(form){
        //
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.params.type=='edit') {
              //如果是新增调用跟新接口
              this.submitEditForm()
            }
            else {
              this.submitAddForm()
            }
          }
          else {
            //this.$message.error('输入有误！')
            return false;
          }
        })

      },
      //新增接口
      submitAddForm() {
        let params = {} //入参
        let _this = this
        for(let i in this.formDemo){
          if(i != 'smsTmpl'){
            params[i] = this.formDemo[i]
          }else{
            params[i] = {tmplId:this.formDemo[i].tmplId}
          }

        }
        params['principalUserId'] = this.getEmployeeInfo('id')
        this.loading = true
        this.ajax.postStream(apiUrl.smsQuick_AddItem, params, res => {
            if(!res.body.result){
              return this.$message.error(res.body.msg)
            }
            // _this.$root.eventHandle.$emit('removeTab', _this.params.tabName)
            _this.$message({message: '操作成功',type: 'success'});
            _this.params.__close('refreshBeforeSuccess');
         }, err => {
             this.$message.error(err)
             this.loading = true
					})

      },
      //更新接口
      submitEditForm() {
        let params = {} //入参
        let _this = this
        for(let i in this.formDemo){
          if(i.indexOf('create') == 0 || i.indexOf('modify') == 0){
            continue
          }

          if(i != 'smsTmpl'){
            params[i] = this.formDemo[i]
          }else{
            params[i] = {tmplId:this.formDemo[i].tmplId}
          }
        }

        params['principalUserId'] = this.getEmployeeInfo('id')
        this.loading = true
        this.ajax.postStream( apiUrl.smsQuick_UpdateItem,params, res => {
            if(!res.body.result){
              return this.$message.error(res.body.msg)
            }

            this.loading = false
            // _this.tmpls = res.data.content
						// if(_this.tmpls && _this.tmpls.length > 0){
            //   _this.formDemo.smsTmpl['tmplId'] = _this.tmpls[0].tmplId

            //   _this.tmpls.forEach(o=>{
						// 		_this.tmplMap["" + o.tmplId] = o
						// 	})
            // }
            _this.$message({message: '操作成功',type: 'success'});
            _this.params.__close('refreshBeforeSuccess');
            // _this.$root.eventHandle.$emit('removeTab', _this.params.tabName)
        }, err => {
             this.$message.error(err)

            }
					)
      },
      //初始化数据
      init() {
        if (this.params.type === 'edit') {
          // this.formDemo
          Object.assign(this.formDemo, this.params.form)

        }
        this.getSmsTmpl()
      },
      //获取短信模板信息
      getSmsTmpl() {
        let _this = this        
         // originalUrl:baseUrl + '/callcenter-web/smsTmpl/list.do'
        //  this.ajax.postStream(baseUrl + apiUrl.smsTmpl_GetList, {status:1, page_size:200, page_no:1}, res => {
          this.ajax.postStream(baseUrl + apiUrl.smsTmpl_GetList,{status:1, page_size:10000, page_no:1,page: {length: 10000, pageNo: 1}} , res => {
            _this.tmpls = res.body.content.list            
						if(_this.tmpls && _this.tmpls.length > 0){
              if (this.params.type=='edit') {
                _this.tmplId = _this.formDemo.smsTmpl['tmplId']
              }
              else {
                 _this.formDemo.smsTmpl['tmplId'] = _this.tmpls[0].tmplId
                _this.tmplId =  _this.tmpls[0].tmplId
              }
              _this.tmpls.forEach(o=>{
								_this.tmplMap["" + o.tmplId] = o
							})
            }
            this.loading = false
         },err => {
             this.$message.error(err)
             this.loading = false
					})
      },
      tmplChange(val){
        this.formDemo.smsTmpl['tmplId'] = this.tmplMap["" + val].tmplId
      }
    },
    mounted() {

      this.init()
    }
  }
</script>

<style scoped>
.form-box {
  margin-top: 10px;
}
</style>

<style>
.sms-quick-create .el-form-item .el-form-item__label {
    padding: 0 15px 0 10px;
}
</style>
