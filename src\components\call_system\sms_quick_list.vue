<!-- 呼叫中心 - 快捷短语列表  -->
<template>
	<xpt-list
		ref="smsQuickList"
		selection="radio"
		:orderNo="true"
		:btns="btns"
		:colData="cols"
		:data="tableData"
		:pageTotal="totalPage"
		:pageLength="search.page_size"
		:searchPage="search.page_name"
		@radio-change="select"
		@search-click="searchClick"
		@page-size-change="pageSizeChange"
		@current-page-change="pageChange"
	></xpt-list>
	<!-- :isNeedClickEvent="true"
		@row-dblclick="rowDBLclick" -->
	<!-- <xpt-list-dynamic -->
</template>
<script>
import baseUrl, { makeUrl, apiUrl } from "../call_system/base.js";
export default {
	props: ["params"],
	data() {
		var self = this;
		return {
			btns: [
				{
					type: "success",
					txt: "刷新",
					loading: false,
					click() {
						self.getList();
					}
				},
				{
					type: "primary",
					txt: "创建",
					loading: false,
					click() {
						self.createSms();
					}
				},
				{
					type: "primary",
					txt: "删除",
					loading: false,
					click() {
						self.deleteSms();
					}
				}
			],
			cols: [
				{
					label: "类型编码",
					prop: "smsTmpl",
					html: data => data.typeCode,
					width: 120
				},
				{
					label: "类型名称",
					prop: "smsTmpl",
					html: data => data.typeName,
					width: 120
				},
				{
					label: "快捷短语简称",
					prop: "usefulWordingName",
					redirectClick(row) {
            // delete row.typeName;
            let createTabOptions = {
							name: "编辑短语",
							params: {
                type: "edit",
                form: row
              },
							component: () => import("@components/call_system/sms_quick_create.vue")
						}
            self.$root.eventHandle.$emit("creatTab", createTabOptions);

            var oldCloseFun = createTabOptions.params.__close;
            createTabOptions.params.__close = refreshBeforeSuccess => {
              if(refreshBeforeSuccess = 'refreshBeforeSuccess'){
                self.getList();
              };
              oldCloseFun();
            }
					},
					width: 120
				},
				{
					label: "短信内容",
					prop: "usefulWordingContent",
					width: 300
				},
				{
					label: "创建人",
          prop: "creatorName",
          // prop: "smsTmpl",
					// html: data => data.creatorName,
					width: 120
				},
				{
					label: "创建时间",
					prop: "createTime",
					width: 120,
					format: "dataFormat1"
				},
				{
					label: "修改人",
          prop: "modifierName",
          // prop: "smsTmpl",
					// html: data => data.modifierName,
					width: 120
				},
				{
					label: "修改时间",
					prop: "modifyTime",
					width: 120,
					format: "dataFormat1"
				}
			],
			tableData: [],
			totalPage: 0,
			selectData: [], //单选的数据
			search: {
				page_size: 200,
				page_no: 1,
				page_name: "cloud_sms_useful_wordings",
				where: []
			},
			// 原版参数
			formMap: {
				smsTmpl: {
					typeCode: "",
					typeName: ""
				},
				usefulWordingName: "",
				pageSize: 200,
				pageNo: 1
      },
      changeWhere: 0, //强制修改where
			// 测试数据
			// testData: []
		};
	},
	methods: {
		//查询表单
		getList() {
			let params = {
				page: {
					length: this.search.page_size,
					pageNo: this.search.page_no
				},
				page_name: this.search.page_name,
				where: this.search.where
			};
			this.getSmsTemplateList(params);
		},
		getSmsTemplateList(params, resolve) {
			// this.tableData = [];
			let _this = this;
			_this.selectData = [];
			_this.btns[0].loading = true;
			this.ajax.postStream(
				apiUrl.smsQuick_GetList,
				params,
				res => {
					if (res && res.body.result) {
						// let nowData = res.body.content.list;
						// nowData = nowData.map(i => {
						// 	i.typeName = i.smsTmpl.typeName;
						// 	return i;
						// });
						_this.tableData = res.body.content.list;
						_this.totalPage = res.body.content.count;
					} else {
						_this.$message.error(res.body.msg);
					}
					resolve && resolve();
					_this.btns[0].loading = false;
				},
				err => {
					resolve && resolve();
					_this.$message.error(err);
					_this.btns[0].loading = false;
				}
			);
		},
		//新建快捷短语
		createSms() {
      const self = this;
      let createTabOptions = {
        name: "创建短语",
        params: { type: "add" },
        component: () => import("@components/call_system/sms_quick_create.vue")
      }
      self.$root.eventHandle.$emit("creatTab", createTabOptions);

      var oldCloseFun = createTabOptions.params.__close;
      createTabOptions.params.__close = refreshBeforeSuccess => {
        if(refreshBeforeSuccess = 'refreshBeforeSuccess'){
          self.getList();
        };
        oldCloseFun();
      }
		},
		//删除短语
		deleteSms() {
			if (this.selectData.length === 0) {
				this.$message.error("请先选择数据");
				return;
			}
			this.$http
				.get(
					makeUrl(apiUrl.smsQuick_DeleteItem, {
						usefulWordingId: this.selectData.usefulWordingId
					})
				)
				.then(resp => {
					this.$message.success("删除成功");
				})
				.catch(err => {
          console.error(error)
					this.$message.error("删除失败");
				})
				.finally(() => {
					this.getList();
				});
		},
		// 双击事件 // row:object -> 行数据
		// rowDBLclick(row) {
		// 	//打开编辑页面
		// 	const params = {
		// 		type: "edit",
		// 		form: row
		// 	};
		// 	this.$root.eventHandle.$emit("creatTab", {
		// 		name: "编辑短语",
		// 		params: params,
		// 		component: () => import("@components/call_system/sms_quick_create.vue")
		// 	});
		// },
		// 单选数据
		select(data) {
			this.selectData = data;
		},
		// 搜索
		searchClick(obj, resolve) {
			this.search.where = obj;
			let params = {
				page: {
					length: this.search.page_size,
					pageNo: this.search.page_no
				},
				page_name: this.search.page_name,
				where: this.search.where
			};
			this.getSmsTemplateList(params, resolve);
		},
		//单页条数
		pageSizeChange(ps) {
			this.search.page_size = ps;
			// this.formMap.pageSize = ps;
			this.getList();
		},
		//换页
		pageChange(no) {
			this.search.page_no = no;
			// this.formMap.pageNo = no;
			this.getList();
    },
    // 检测是否为弹窗
    checkpop() {
      let self = this;
      if(this.params.popup){
        this.btns = [{
					type: "success",
					txt: "刷新",
					loading: false,
					click() {
						self.getList();
					}
				},
				{
					type: "primary",
					txt: "确定",
					loading: false,
					click() {
            self.senditem();
					}
        }];
        // 操作查询关键字以及查询范围范围的显示顺序
        this.$refs.smsQuickList.$refs.xptSearchEx.filterFields =
        fields => {
          let oldItem = fields[3];
          fields.splice(3,1);
          fields.unshift(oldItem);
          return fields;
        }
        this.$refs.smsQuickList.$refs.xptSearchEx.watchwhere =
        where => {
          if(this.changeWhere < 3){
            where = where.map((item,index)=>{
              if(index == 0){
                item.operator = '%'
              }
              return item
            })
            this.changeWhere++;
            return where
          }else{
            return where
          }
        }
      }
    },
    // 确认无误发送item
    senditem() {
      if(this.selectData.length === 0){
        this.$message.error('尚未选择相关数据');
      }else if(this.selectData){
        let item = this.selectData;
        this.params.close(item);
        this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
      }
    }
	},
	mounted() {
    this.getList();
    this.checkpop();
	}
};
</script>
