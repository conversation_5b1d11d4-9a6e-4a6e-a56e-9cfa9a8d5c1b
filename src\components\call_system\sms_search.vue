<!-- 搜索 -->
<template>
  <div class="search-work">
    <div class="form-btn" v-if="showBtn">
      <el-button class="form-btn-create" type='primary' size="mini" @click="smsDome">创建</el-button>
    </div>
    <div class="form-box" style="margin-left:10px">
      <el-form ref="form" :model="formMap" label-width="80px" :style="showBtn ? 'width:810px' : 'width:910px'" :class="showBtn ?'clear' : ''">
        <div class="form-inline">
          <div class="form-inline search-label">类型编码</div>
          <el-input class="search-input" size="mini" v-model="formMap.typeCode"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">类型名称</div>
          <el-input class="search-input" size="mini" v-model="formMap.typeName"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">短信内容</div>
          <el-input class="search-input" size="mini" v-model="formMap.tmplContent"></el-input>
        </div>
        <div class="form-inline">
          <div class="form-inline search-label">号码类型</div>
          <el-select v-model="formMap.senderType" placeholder="请选择" size="mini" class="search-input">
            <el-option
              v-for="item in senderType"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <div class="form-inline" v-if="!showBtn">
             <el-button type='primary' size="mini" @click="search()">查询</el-button>
              <el-button type='primary' size="mini" @click="resetForm()">重置</el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="form-action" v-if="showBtn">
      <el-button type='primary' size="mini" @click="search()">查询</el-button>
      <el-button type='primary' size="mini" @click="resetForm()">重置</el-button>
    </div>
  </div>
</template>

<script>
  // import callBtn from '@/components/call_system/callBtn'
  export default {
     props:["showBtn"],
    // components: {
    //   callBtn,
    // },
    data(){
      var _this = this
      return {
        formMap: {
          typeCode:'',
          typeName: '',
          status: 0,
          senderType: '',
          pageSize: 200,
          pageNo: 1,
          tmplContent:''
        },
        senderType: [{
          value: 'SALES_NUMBER',
          label: '营销号'
        }, {
          value: 'BUSINESS_NUMBER',
          label: '业务号'
        }],
        value: '',

      }
    },
    methods:{
      //重置查询表单
      resetForm(formName) {
        // this.$refs[formName].resetFields();

        Object.assign(this.formMap, this.$options.data.call(this).formMap)

      },
      // 切换组件（页面）
      smsDome(){
        //
        this.$root.eventHandle.$emit('creatTab', {
          name: "创建模板",
          component: () => import('@components/call_system/sms_template_demo.vue')
        });
      },
      //查询表单
      search() {

        this.$emit('handleSearch',this.formMap)
      }
    },
    mounted: function(){
      // _this.getForcedUnlock();
      // _this.updateUserOrder();
      // _this.searching();
      // 检测新增、编辑数据，刷新
      let _this = this
      _this.$root.eventHandle.$on('refresh_list',function(){
        // _this.searching();
      })
    },
    destroyed(){
      this.$root.offEvents('refresh_list');
    },
    watch: {
      btns () {
        let _this = this
        _this.getBtnPower()
      }
    }
  }
</script>
<style scoped>
  .clear{
    margin-right: 0
  }
  .search-work{
    background: #eee;
    display: flex;
    margin: 10px -20px 10px -20px;
  }
  .form-btn {
    flex: 2;
  }
  .form-btn-create {
    margin-top: 13px;
    margin-left: 20px;
  }
  .form-action{
    margin:auto;
    min-width: 100px;
  }
  .form-box{
    position:relative;
    /* margin:10px; */
    flex:2;
  }
  .form-inline{
    display: inline-block;
    margin-top: 10px
  }
  .search-label{
    width:80px;
    text-align: center;
  }
  .search-input{
    margin-bottom: 10px;
    width: 105px;
  }
</style>
