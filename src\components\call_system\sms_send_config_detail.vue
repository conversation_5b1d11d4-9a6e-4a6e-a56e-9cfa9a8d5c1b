<template>
    <div>
        <el-row class="xpt-top" :gutter="40">
            <el-col :span="24">
                <el-button
                    size="mini"
                    type="success"
                    @click="refresh()"
                    :disabled="false"
                    >刷新</el-button
                >
                <el-button
                    size="mini"
                    type="primary"
                    @click="presave()"
                    :loading="isSaving"
                    :disabled="!canISmsSave"
                    >保存</el-button
                >
                <el-button
                    size="mini"
                    type="success"
                    @click="changeStatus('valid')"
                    :loading="false"
                    :disabled="!canISmsValid"
                    >生效</el-button
                >
                <el-button
                    size="mini"
                    type="danger"
                    @click="changeStatus('invalid')"
                    :loading="false"
                    :disabled="!canISmsInvalid"
                    >失效</el-button
                >
            </el-col>
        </el-row>
        <el-row class="xpt-flex__bottom">
            <el-tabs v-model="firstTab" class="sendConfig">
                <el-tab-pane
                    label="短信发送配置"
                    name="sendConfig"
                    class="xpt-flex"
                    style="overflow: hidden"
                >
                    <el-form
                        ref="submitData"
                        :model="submitData"
                        label-position="right"
                        label-width="90px"
                        :rules="rules"
                    >
                        <el-row class="mgt20" :gutter="20">
                            <el-col :span="6">
                                <el-form-item
                                    label="单据号"
                                    prop="message_config_number"
                                >
                                    <el-input
                                        v-model="
                                            submitData.message_config_number
                                        "
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="短信名称"
                                    prop="message_config_name"
                                >
                                    <el-input
                                        v-model="submitData.message_config_name"
                                        size="mini"
                                        :disabled="!canISmsEdit"
                                        :maxlength="50"
                                    ></el-input>
                                    <el-tooltip
                                        v-if="
                                            rules.message_config_name[0].isShow
                                        "
                                        class="item"
                                        effect="dark"
                                        :content="
                                            rules.message_config_name[0].message
                                        "
                                        placement="right"
                                        popper-class="xpt-form__error"
                                    >
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="短信模板" prop="type_name">
                                    <xpt-input
                                        v-model="submitData.type_name"
                                        icon="search"
                                        :on-icon-click="selectSmsTemplate"
                                        @change="smsTemplateChange"
                                        size="mini"
                                        placement="right-start"
                                        readonly
                                        :disabled="!canISmsEdit"
                                    ></xpt-input>
                                    <el-tooltip
                                        v-if="rules.type_name[0].isShow"
                                        class="item"
                                        effect="dark"
                                        :content="rules.type_name[0].message"
                                        placement="right"
                                        popper-class="xpt-form__error"
                                    >
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item
                                    label="短信发送时间"
                                    prop="timing"
                                >
                                    <xpt-select-aux
                                        v-model="submitData.timing"
                                        aux_name="message_delivery_time"
                                        :disabled="!canISmsEdit"
                                    ></xpt-select-aux>
                                    <el-tooltip
                                        v-if="rules.timing[0].isShow"
                                        class="item"
                                        effect="dark"
                                        :content="rules.timing[0].message"
                                        placement="right"
                                        popper-class="xpt-form__error"
                                    >
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="短信发送范围" prop="range">
                                    <el-select
                                        v-model="submitData.range"
                                        placeholder="请选择"
                                        size="mini"
                                        @change="smsSendAreaChange"
                                        :disabled="!canISmsEdit"
                                    >
                                        <el-option
                                            v-for="item in sendAreaOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                    <el-tooltip
                                        v-if="rules.range[0].isShow"
                                        class="item"
                                        effect="dark"
                                        :content="rules.range[0].message"
                                        placement="right"
                                        popper-class="xpt-form__error"
                                    >
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="创建人"
                                    prop="creator_name"
                                >
                                    <el-input
                                        v-model="submitData.creator_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="创建时间"
                                    prop="create_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.create_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item
                                    label="更新人"
                                    prop="modifier_name"
                                >
                                    <el-input
                                        v-model="submitData.modifier_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="更新时间"
                                    prop="modify_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.modify_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item label="状态" prop="status">
                                    <el-switch
                                        v-model="submitData.status"
                                        on-color="#13ce66"
                                        off-color="#ff4949"
                                        on-text="生效"
                                        off-text="失效"
                                        size="mini"
                                        :disabled="true"
                                    >
                                    </el-switch>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="生效人"
                                    prop="effective_name"
                                >
                                    <el-input
                                        v-model="submitData.effective_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="生效时间"
                                    prop="effective_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.effective_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item
                                    label="失效人"
                                    prop="expiration_name"
                                >
                                    <el-input
                                        v-model="submitData.expiration_name"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="失效时间"
                                    prop="expiration_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.expiration_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
            <el-tabs
                v-model="secondTab"
                class="sendArea"
                v-show="showSmsAreaTab"
            >
                <el-tab-pane
                    label="短信发送范围"
                    name="sendArea"
                    class="xpt-flex"
                    style="overflow: hidden"
                >
                    <xpt-list
                        :orderNo="true"
                        :data="sendAreaList"
                        :btns="sendAreaBtns"
                        :colData="sendAreaColData"
                        selection="checkbox"
                        @selection-change="sendAreaHandleSelectionChange"
                        :pageTotal="sendAreaPage.total"
                        @search-click="sendAreaPresearch"
                        @cell-click="handleCellClick"
                        searchHolder=""
                        @page-size-change="sendAreaSizeChange"
                        @current-page-change="sendAreaPageChange"
                        ref="sendArea"
                    >
                    </xpt-list>
                </el-tab-pane>
            </el-tabs>
        </el-row>
    </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            currentSettingId: "",
            submitData: {
                message_config_name: "",
                type_name: "",
                timing: "",
                range: "",
            },
            sendAreaPage: {
                total: 0,
                pageSize: this.pageSize,
                pageNo: 1,
                key: "",
            },
            sendAreaList: [],
            sendAreaShopList: [],
            sendAreaShopGroupList: [],
            sendAreaSelectList: [],
            currentCellSelect: {}, //选择的当前单元格
            sendAreaBtns: [
                {
                    type: "primary",
                    txt: "添加",
                    click: self.addShop,
                    disabled: () => !this.canIAreaAdd,
                    loading: false,
                },
                {
                    type: "warning",
                    txt: "失效",
                    click: self.invalidShop,
                    disabled: () => !this.canIAreaInvalid,
                    loading: false,
                },
                {
                    type: "danger",
                    txt: "删除",
                    click: self.deleteRow,
                    disabled: () => !this.canIAreaDelete,
                    loading: false,
                },
            ],
            sendAreaColData: [],
            sendAreaShopColData: [
                {
                    label: "店铺编码",
                    prop: "shop_code",
                },
                {
                    label: "实施店铺",
                    prop: "shop_name",
                    bool: true,
                    iconClick: self.selectShop,
                    disabled(row) {
                        return !self.canIAreaEdit || !row.arowId;
                    },
                    width: 128,
                },
                {
                    label: "店铺分组",
                    prop: "shop_group",
                    format: "auxFormat",
                    formatParams: "shopGroup",
                },
                {
                    label: "店铺分类",
                    prop: "shop_type",
                    format: "auxFormat",
                    formatParams: "shopClassify",
                },
                {
                    label: "所属公司",
                    prop: "customer_source_name",
                },
                {
                    label: "生效状态",
                    prop: "status",
                    formatter: (val) => {
                        return (
                            {
                                true: "生效",
                                false: "失效",
                            }[val] || val
                        );
                    },
                },
                {
                    label: "创建时间",
                    prop: "create_time",
                    format: "dataFormat1",
                },
                {
                    label: "失效人",
                    prop: "expiration_name",
                },
                {
                    label: "失效时间",
                    prop: "expiration_time",
                    format: "dataFormat1",
                },
            ],
            sendAreaShopGroupColData: [
                {
                    label: "店铺分组",
                    prop: "shop_group",
                    bool: true,
                    isSelect: true,
                    obj: {},
                    disabled(row) {
                        return !self.canIAreaEdit || !row.arowId;
                    },
                },
                {
                    label: "生效状态",
                    prop: "status",
                    formatter: (val) => {
                        return (
                            {
                                true: "生效",
                                false: "失效",
                            }[val] || val
                        );
                    },
                },
                {
                    label: "创建时间",
                    prop: "create_time",
                    format: "dataFormat1",
                },
                {
                    label: "失效人",
                    prop: "expiration_name",
                },
                {
                    label: "失效时间",
                    prop: "expiration_time",
                    format: "dataFormat1",
                },
            ],
            firstTab: "sendConfig",
            secondTab: "sendArea",
            isSaving: false,
            rules: {
                message_config_name: validate.isNotBlank({
                    self: self,
                    msg: "不能为空",
                }),
                type_name: validate.isNotBlank({
                    self: self,
                    msg: "不能为空",
                }),
                timing: validate.isNotBlank({
                    self: self,
                    msg: "不能为空",
                }),
                range: validate.isNotBlank({
                    self: self,
                    msg: "不能为空",
                }),
            },
            sendAreaOptions: [
                {
                    value: "WHOLE",
                    label: "全局",
                },
                {
                    value: "SHOP",
                    label: "店铺",
                },
                {
                    value: "GROUP",
                    label: "店铺分组",
                },
            ],
            searchSmsTemplateTimer: null,
            showSmsAreaTab: false, //展示短信发送范围页签
            shopGroupAuxObj: {},
            canIAreaAdd: true, //短信发送范围-添加权限
            canIAreaDelete: false, //短信发送范围-删除权限
            canIAreaInvalid: false, //短信发送范围-失效权限
            canIAreaEdit: true, //短信发送范围-是否能操作输入框
            canISmsSave: true, //短信发送配置-保存权限
            canISmsValid: false, //短信发送配置-生效权限
            canISmsInvalid: false, //短信发送配置-失效权限
            canISmsEdit: true, //短信发送配置-是否能操作输入框
        };
    },
    created() {},
    mounted() {
        this.initDetail();
        this.initAuxDataObj();
    },
    methods: {
        initDetail() {
            console.log(this.params);
            let self = this;
            this.params && this.params.message_config_id
                ? (this.currentSettingId = this.params.message_config_id)
                : "";
            if (this.currentSettingId) {
                this.ajax.postStream(
                    "/file-web/api/messageDeliveryConfig/get.do",
                    { message_config_id: this.currentSettingId },
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.submitData = res.body.content;
                            self.sendAreaList = res.body.content.shopList||[];
                            if (res.body.content.range == "SHOP") {
                                self.sendAreaShopList =
                                    res.body.content.shopList||[];
                            } else if (res.body.content.range == "GROUP") {
                                self.sendAreaShopGroupList =
                                    res.body.content.shopList||[];
                            } else if (res.body.content.range == "WHOLE") {
                                self.sendAreaList =[]
                            }
                            this.setButtonPermission("detail");
                        } else {
                            res.body.msg && this.$message.error(res.body.msg);
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                    }
                );
            } else {
                this.setButtonPermission("create");
            }
        },
        initAuxDataObj() {
            let self = this;
            let shopGroupAux = __AUX.getValidData("shopGroup");
            shopGroupAux.forEach((item) => {
                self.shopGroupAuxObj[item.code] = item.name;
            });
            this.sendAreaShopGroupColData[0].obj = self.shopGroupAuxObj;
            console.log(self.shopGroupAuxObj);
        },
        setButtonPermission(type) {
            if (type == "create") {
                this.submitData.range = "SHOP";
                this.canIAreaAdd = true;
            } else if (type == "detail") {
                this.canISmsValid = true;
                this.canISmsInvalid = true;
                // this.canIAreaEdit = !this.submitData.message_config_number;
                this.canIAreaAdd = true;
                this.canISmsEdit = false;
            } else if (type == "area") {
                let status = this.sendAreaSelectList.length > 0;
                this.canIAreaDelete = status;
                this.canIAreaInvalid = status;
            }
        },
        refresh() {
            this.initDetail();
        },
        presave() {
            let self = this;
            this.$refs.submitData.validate((valid) => {
                if (!valid) {
                    return;
                }
                console.log(this.sendAreaList);
                if (
                    ["SHOP", "GROUP"].includes(this.submitData.range) &&
                    (this.sendAreaList.length == 0 ||
                        this.sendAreaList.some(
                            (item) => item.arowId && !item.shop_group
                        ))
                ) {
                    self.$message.error("请填写短信发送范围");
                    return;
                }
                let params = this.formatSaveMainParams();
                this.ajax.postStream(
                    "/file-web/api/messageDeliveryConfig/save.do",
                    params,
                    (res) => {
                        if (res.body.result && res.body.content) {
                            self.$message.success(res.body.msg);
                            self.currentSettingId = res.body.content;
                            self.refresh();
                        } else {
                            res.body.msg && self.$message.error(res.body.msg);
                            self.refresh();
                        }
                    },
                    (err) => {
                        this.$message.error(err);
                    }
                );
            });
        },
        formatSaveMainParams() {
            let params = {};
            if (this.currentSettingId) {
                params.message_config_id = this.currentSettingId;
                params.message_config_number = this.submitData.message_config_number;
            }
            params.message_config_name = this.submitData.message_config_name;
            params.tmpl_id = this.submitData.tmpl_id;
            params.timing = this.submitData.timing;
            params.range = this.submitData.range;
            params.status = this.submitData.status || false;
            params.shopList = this.formatShopList();
            return params;
        },
        formatShopList() {
            let sendArea = this.submitData.range;
            let formatList = [];
            if (sendArea == "WHOLE") {
                return formatList;
            }
            let list = this.sendAreaList;
            if (sendArea == "GROUP") {
                formatList = list.map((item) => {
                    let obj = {
                        shop_group: item.shop_group,
                        status: item.status,
                    };
                    item.message_config_id
                        ? (obj.message_config_id = item.message_config_id)
                        : "";
                    item.message_config_shop_id
                        ? (obj.message_config_shop_id =
                              item.message_config_shop_id)
                        : "";
                    return obj;
                });
                return formatList;
            }
            if (sendArea == "SHOP") {
                formatList = list.map((item) => {
                    let obj = {
                        shop_id: item.shop_id,
                        shop_code: item.shop_code,
                        shop_group: item.shop_group,
                        status: item.status,
                    };
                    item.message_config_id
                        ? (obj.message_config_id = item.message_config_id)
                        : "";
                    item.message_config_shop_id
                        ? (obj.message_config_shop_id =
                              item.message_config_shop_id)
                        : "";
                    return obj;
                });
                return formatList;
            }
        },
        changeStatus(type) {
            if (type == "valid") {
                this.submitData.status = true;
            } else if (type == "invalid") {
                this.submitData.status = false;
            }
            // this.presave();
        },
        selectSmsTemplate() {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                component: () =>
                    import(
                        "@components/call_system/sms_send_config_smstemplate.vue"
                    ),
                close: function () {},
                style: "width:900px;height:600px",
                title: "请选择短信模板",
                params: {
                    callback(d) {
                        console.log(d.typeName);
                        self.submitData.type_name = d.typeName;
                        self.submitData.tmpl_id = d.tmplId;
                        self.$message.success("选择成功");
                    },
                },
            });
        },
        smsTemplateChange(val) {
            if (!val) {
                this.submitData.type_name = "";
            }
        },
        smsSendAreaChange(val) {
            ["SHOP", "GROUP"].includes(val)
                ? (this.showSmsAreaTab = true)
                : (this.showSmsAreaTab = false);
            if (val == "SHOP") {
                this.sendAreaColData = this.sendAreaShopColData.slice();
                this.sendAreaList = this.sendAreaShopList.slice();
            } else if (val == "GROUP") {
                this.sendAreaColData = this.sendAreaShopGroupColData.slice();
                this.sendAreaList = this.sendAreaShopGroupList.slice();
            } else {
                this.sendAreaColData = [];
                this.sendAreaList = [];
            }
            console.log(val);
        },

        //短信发送范围模块

        addShop() {
            if (this.submitData.range == "SHOP") {
                let shopAddData = {};
                shopAddData.arowId = new Date().getTime();
                shopAddData.shop_id = "";
                shopAddData.shop_code = "";
                shopAddData.shop_name = "";
                shopAddData.shop_group = "";
                shopAddData.shop_group_name = "";
                shopAddData.shop_type = "";
                shopAddData.customer_source_name = "";
                shopAddData.status = true;
                this.sendAreaShopList.unshift(shopAddData);
                this.sendAreaList = [...this.sendAreaShopList];
                console.log(this.sendAreaList);
            } else if (this.submitData.range == "GROUP") {
                let shopGroupAddData = {};
                shopGroupAddData.arowId = new Date().getTime();
                shopGroupAddData.shop_group = "";
                shopGroupAddData.status = true;
                this.sendAreaShopGroupList.unshift(shopGroupAddData);
                this.sendAreaList = [...this.sendAreaShopGroupList];
            }
        },
        //发送范围-失效
        invalidShop() {
            let self = this;
            let ifHasAdd = this.sendAreaSelectList.some((item) => item.arowId);
            let ifHasInvalid = this.sendAreaSelectList.some(
                (item) => item.status == false
            );
            if (ifHasAdd) {
                this.$message.error("列表存在新增数据，请执行保存操作");
                return;
            }
            if (ifHasInvalid) {
                this.$message.error("选择存在失效数据，请重新操作");
                return;
            }
            let invalidListId = this.sendAreaSelectList.map(
                (item) => item.message_config_shop_id
            );
            let list = this.sendAreaList.slice();
            list.find((item) => {
                if (invalidListId.includes(item.message_config_shop_id)) {
                    item.status = false;
                }
            });
            console.log(list);
            this.sendAreaList = list;
        },
        //发送范围-删除
        deleteRow() {
            if (this.sendAreaSelectList.length < 1) {
                this.$message.error("请选择至少一行数据");
            }
            let hasSaveId = false;
            let arowIdList = []; //选择的所有新增未保存的行
            let list = this.sendAreaList;
            this.sendAreaSelectList.forEach((val) => {
                if (!!val.message_config_shop_id) {
                    hasSaveId = true;
                }
                if (!!val.arowId) {
                    arowIdList.push(val.arowId);
                }
            });
            if (hasSaveId) {
                this.$message.warning("只能删除未保存的行");
            }
            this.sendAreaList = list.filter((val) => {
                return !val.arowId || !arowIdList.includes(val.arowId);
            });
            if (this.submitData.range == "SHOP") {
                this.sendAreaShopList = this.sendAreaList.slice();
            } else if (this.submitData.range == "GROUP") {
                this.sendAreaShopGroupList = this.sendAreaList.slice();
            }
        },
        // 短信发送范围-店铺-选择店铺
        selectShop() {
            let self = this;
            let params = {
                callback(data) {
                    console.log(data);
                    self.currentCellSelect.shop_code = data.shop_code;
                    self.currentCellSelect.shop_id = data.shop_id;
                    self.currentCellSelect.shop_name = data.shop_name;
                    self.currentCellSelect.shop_group = data.shop_group;
                    self.currentCellSelect.shop_group_name =
                        data.shop_group_name;
                    self.currentCellSelect.shop_type = data.shop_type;
                    self.currentCellSelect.customer_source_name =
                        data.customer_source_name;
                },
                selection: "radio",
                shop_status: "OPEN",
            };
            this.$root.eventHandle.$emit("alert", {
                params: params,
                component: () => import("@components/shop/list"),
                style: "width:800px;height:500px",
                title: "店铺列表",
            });
        },

        //点击行
        handleCellClick(row) {
            console.log(row);
            this.currentCellSelect = row;
        },
        sendAreaHandleSelectionChange(list) {
            this.sendAreaSelectList = list;
            console.log(list);
            this.setButtonPermission("area");
        },
        sendAreaPresearch(txt) {
            this.sendAreaPage.key = txt;
        },
        sendAreaSizeChange(pageSize) {
            this.sendAreaPage.pageSize = pageSize;
        },
        sendAreaPageChange(pageNo) {
            this.sendAreaPage.pageNo = pageNo;
        },
    },
};
</script>
<style scoped>
.el-form-item {
    margin: 10px auto;
    white-space: nowrap;
}
</style>