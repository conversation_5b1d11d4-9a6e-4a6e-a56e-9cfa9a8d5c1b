<!--短信发送配置列表-->
<template>
    <xpt-list-dynamic
        ref="list"
        :data="list"
        :btns="btns"
        :colData="cols"
        :selection="selection"
        :pageTotal="count"
        :searchPage="search.page_name"
        @search-click="presearch"
        @selection-change="handleSelectionChange"
        @page-size-change="sizeChange"
        @current-page-change="pageChange"
    ></xpt-list-dynamic>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            search: {
                page_name: "message_delivery_config",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
                if_need_page: "Y",
            },
            list: [],
            selection: "checkbox",
            count: 0,
            multipleSelection: [], //列表选择索引
            btns: [
                {
                    type: "success",
                    txt: "刷新",
                    click: self.searching,
                    loading: false,
                },
                {
                    type: "warning",
                    txt: "创建",
                    click: self.createConfig,
                    loading: false,
                },
            ],
            cols: [
                {
                    prop: "message_config_number",
                    label: "单据号",
                    width: 130,
                    redirectClick(row) {
                        let params = {
                            message_config_number:row.message_config_number,
                            message_config_id:row.message_config_id
                        };
                        params.config_id = row.id;
                        self.$root.eventHandle.$emit("creatTab", {
                            name: "短信发送配置详情",
                            params: params,
                            component: () =>
                                import(
                                    "@components/call_system/sms_send_config_detail.vue"
                                ),
                        });
                    },
                },
                {
                    prop: "message_config_name",
                    label: "短信发送机制名称",
                },
                {
                    prop: "type_name",
                    label: "短信模板",
                },
                {
                    prop: "timing",
                    label: "短信发送时间",
                    format:'auxFormat',
                    formatParams: 'message_delivery_time'
                },
                {
                    prop: "range",
                    label: "短信发送范围",
                    formatter(val) {
                        return (
                            {
                                WHOLE: "全局",
                                SHOP: "店铺",
                                GROUP: "店铺分组",
                            }[val] || val
                        );
                    },
                },
                {
                    prop: "creator_name",
                    label: "创建人",
                },
                {
                    prop: "create_time",
                    label: "创建时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "modifier_name",
                    label: "更新人",
                },
                {
                    prop: "modify_time",
                    label: "更新时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "effective_name",
                    label: "生效人",
                },
                {
                    prop: "effective_time",
                    label: "生效时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "expiration_name",
                    label: "失效人",
                },
                {
                    prop: "expiration_time",
                    label: "失效时间",
                    format: "dataFormat1",
                    width: 130,
                },
            ],
        };
    },
    methods: {
        presearch(list, resolve) {
            this.search.where = list;
            this.searching(resolve);
        },
        searching(resolve) {
            let self = this;
            this.btns[0].loading = true;
            this.ajax.postStream(
                "/file-web/api/messageDeliveryConfig/list.do",
                this.search,
                (res) => {
                    if (res.body.result) {
                        self.count = res.body.content.count;
                        if (res.body.content.list) {
                            self.list = res.body.content.list;
                        }
                        self.$message.success(res.body.msg);
                    } else {
                        self.list = [];
                        self.count = 0;
                        self.$message.error(res.body.msg);
                    }
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                },
                (err) => {
                    self.$message.error(err);
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                }
            );
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page_size = size;
            this.searching();
        },
        pageChange(page_no) {
            // 页数改变
            this.pageNow = page_no;
            this.search.page_no = page_no;
            this.searching();
        },
        createConfig() {
            this.$root.eventHandle.$emit("creatTab", {
                name: "创建短信发送配置",
                params: {},
                component: () =>
                    import(
                        "@components/call_system/sms_send_config_detail.vue"
                    ),
            });
        },
    },
    created(){
    },
    mounted() {
        let self = this;
        self.searching();
    },
    destroyed() {},
};
</script>
<style scoped>
</style>