<template>
    <div class='xpt-flex'>
        <xpt-list
            :btns="btns"
            :data="templateList"
            :colData="smsTemplateCols"
            searchHolder="请输入模板名称"
            @search-click="presearch"
            :pageTotal="total"
            :pageLength="search.page.length"
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
            selection="radio"
            @radio-change="radioChange"
        ></xpt-list>
    </div>
</template>
<script>
import { apiUrl } from "../call_system/base.js";
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            templateList: [],
            btns: [
                {
                    type: "info",
                    txt: "确认",
                    click: () => {
                        self.params.callback(self.currentSmsTemplate);
                        self.$root.eventHandle.$emit(
                            "removeAlert",
                            self.params.alertId
                        );
                    },
                    disabled: true,
                },
            ],
            smsTemplateCols: [
                {
                    label: "模板编码",
                    prop: "typeCode",
                    width: 60,
                },
                {
                    label: "模板名称",
                    prop: "typeName",
                    width: 80,
                },
                {
                    label: "号码类型",
                    prop: "senderType",
                    width: 80,
                    formatter: (val) => {
                        return (
                            {
                                SALES_NUMBER: "营销号",
                                BUSINESS_NUMBER: "业务号",
                            }[val] || val
                        );
                    },
                },
                {
                    label: "短信内容",
                    prop: "tmplContent",
                    width: 300,
                },
            ],
            total: 0,
            search: {
                page: { length: 20, pageNo: 1 },
                page_name: "cloud_sms_tmpl",
                where: [
                    {
                        // 模板状态为生效
                        field: "12cd4e45b946f3106059f3165a5f5199",
                        table: "7956c4af2eefd4778cb8e0b303867fdc",
                        value: "1",
                        operator: "=",
                        condition: "AND",
                        listWhere: [],
                    },
                    {
                        ///模糊查询模板名称
                        field: "bfd2337dfe11136364e34c3d6c6b8a69",
                        table: "7956c4af2eefd4778cb8e0b303867fdc",
                        value: "",
                        operator: "%",
                        condition: "AND",
                        listWhere: [],
                    },
                ],
            },
            currentSmsTemplate: {},
        };
    },
    mounted() {
        console.log(this.params);
        this.getSmsTemplateList();
    },
    methods: {
        presearch(key, resolve) {
            this.search.where[1].value = key;
            this.getSmsTemplateList(resolve);
        },
        getSmsTemplateList(resolve) {
            let params = this.search;
            this.ajax.postStream(
                apiUrl.smsTmpl_GetList,
                params,
                (res) => {
                    if (res.body.result) {
                        res.body.msg ? this.$message.success(res.body.msg) : "";
                        this.templateList = res.body.content.list;
                        this.total = res.body.content.count;
                    } else {
                        this.$message.error(res.body.msg);
                    }
                    resolve && resolve();
                },
                (err) => {
                    this.$message.error(err);
                    resolve && resolve();
                }
            );
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page.length = size;
            this.getSmsTemplateList();
        },
        pageChange(pageNo) {
            // 页数改变
            this.search.page.pageNo = pageNo;
            this.getSmsTemplateList();
        },
        radioChange(data) {
            console.log(data);
            this.currentSmsTemplate = data;
            if (Object.keys(this.currentSmsTemplate).length) {
                this.btns[0].disabled = false;
            }
        },
    },
};
</script>
<style scope>
</style>