<template>
  <div class="xpt-flex">
    <div class="sms-template-demo">
    <!-- <xpt-header class="header-bar"> -->
    <!-- <xpt-headbar class="header-bar">
      <call-btn :btns="btns" slot='left'></call-btn>
    </xpt-headbar> -->
    <!-- </xpt-header> -->
    <el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<call-btn :btns="btns" ></call-btn>
			</el-col>
		</el-row>
    <el-row :gutter='40'>
      <!-- <el-tabs v-model="firstTab" > -->
        <!-- <el-tab-pane label="短信类型" name="orderInfo"> -->
          <el-form ref="form" label-position="right" label-width="120px" :model="formDemo" :rules="rules"  class="form-box">

              <el-col :span='10'>
                <el-form-item label="模板编码" prop="typeCode" style="height:50px;">
                  <el-input class="search-input" size="mini" v-model="formDemo.typeCode"  placeholder="请输入类型编码" :disabled="params.type == 'edit'" :maxlength='30'></el-input>
                  <el-tooltip v-if='rules.typeCode[0].isShow' class="item" effect="dark" :content="rules.typeCode[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                  <!-- <div class="type-code-color">类型编码由（大写字母＋数字＋下划线）组成</div> -->
                </el-form-item>
                <el-form-item label="模板名称" prop="typeName" style="height:50px;">
                  <el-input class="search-input"  size="mini" v-model="formDemo.typeName" prop="typeName" placeholder="请输入类型名称" :maxlength='30'></el-input>
                  <el-tooltip v-if='rules.typeName[0].isShow' class="item" effect="dark" :content="rules.typeName[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="短信内容" prop="tmplContent" style="height:120px;">
                  <el-input type="textarea" v-model="formDemo.tmplContent" prop="tmplContent" placeholder="请输入短信内容" :rows='4' style="width:320px;" resize='none'></el-input>
                  <el-tooltip v-if='rules.tmplContent[0].isShow' class="item" effect="dark" :content="rules.tmplContent[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label="号码类型" prop="senderType" style="height:50px;">
                  <el-select v-model="formDemo.senderType" placeholder="请选择" prop="senderType" size="mini" class="search-input" :disabled="params.type == 'edit'">
                    <el-option
                      v-for="item in numType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                  <el-tooltip v-if='rules.senderType[0].isShow' class="item" effect="dark" :content="rules.senderType[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="是否可编辑" prop="editable" style="height:50px;">
                  <el-switch on-text="是" off-text="否" :on-value="1" :off-value="0" v-model="formDemo.editable"></el-switch>

                </el-form-item>
                <el-form-item label="模板状态" prop="status" style="height:50px;">
                  <el-switch on-text="生效" off-text="失效" :on-value="1" :off-value="2" v-model="formDemo.status"></el-switch>

                </el-form-item>
              </el-col>

          </el-form>
        <!-- </el-tab-pane> -->
      <!-- </el-tabs> -->
    </el-row>
    </div>
  </div>
</template>

<script>
  import baseUrl, {makeUrl, apiUrl} from './base.js'
  import 'element-ui/lib/theme-default/index.css'
  import callBtn from '@/components/call_system/callBtn'
  import callRecord from '@/components/call_system/call_record'
  import smsSearch from './sms_search'
  import selectTab from '@/components/call_system/selectTab'
  import validate from '@common/validate.js'
  export default {
    props:["params"],
    components: {
      callBtn,
      callRecord,
      smsSearch,
      selectTab
    },
    data () {
      var _this=this
      return {
        // params:{},
        firstTab:"orderInfo",
        btns: [
          {
            type: 'info',
            txt: '关闭',
            click () {
              _this.goBack()
            },
            show: false
          },
          {
            type: 'info',
            txt: '保存',
            click () {
              _this.submitForm()
            },
            show: false
          }
        ],
        form: {
          tmplId:'',
          name: '',
          phoneType: '',
          compile:false,
          takeEffect: false,
          desc: ''
        },
        formDemo:{
          tmplId:'',
          typeCode:'',
          typeName:'',
          senderType: 'SALES_NUMBER',
          editable:0,   // 是否可编辑短信内容
          tmplContent:'',
          status:1, // 短信状态
        },
        numType:[
            {
              id:0,
              value: 'SALES_NUMBER',
              label: '营销号'
            },
            {
              id:1,
              value: 'BUSINESS_NUMBER',
              label: '业务号'
            },
        ],

        tabs: [
        {
          code: 'smsType',
          name: '短信类型'
        }
      ],
      selectCode:'smsType',
      rules: {
          typeCode: validate.code({
              required: true,
              self:_this,
              msg:'请输入类型编码',
              trigger: 'blur'
					}),
          senderType:  validate.isNotBlank({

              self:_this,
              msg:'请选择短信发送号码类型',
              trigger: 'change'
					}),
          typeName: validate.isNotBlank({

              self:_this,
              msg:'请输入名称',
              trigger: 'blur'
					}),
          tmplContent: validate.isNotBlank({

              self:_this,
              msg:'请输入短信内容',
              trigger: 'blur'
					}),

        }

      }
    },
    methods:{
      // 取消返回
      goBack(){
        this.$root.eventHandle.$emit('removeTab', this.params.tabName);
        // this.$root.eventHandle.$emit('creatTab', {
        //   name: "短信模板",
        //   component: () => import('@components/call_system/sms_template_list.vue')
        // });
      },
      // 确定，提交表单
      submitForm(form){
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.params.type=='edit') {
              //如果是新增调用跟新接口
              this.submitEditForm()
            }
            else {
              this.submitAddForm()
            }
          }
          else {
            //this.$message.error('输入有误！')
            return false;
          }
        })
      },
      //新增接口
      submitAddForm() {
        let _this = this
        let params = {} //入参
        params['tmplId'] = this.formDemo.tmplId
        params['typeCode'] = this.formDemo.typeCode
        params['typeName'] = this.formDemo.typeName
        params['senderType'] = this.formDemo.senderType
        params['editable'] = this.formDemo.editable
        params['tmplContent'] = this.formDemo.tmplContent
        params['status'] = this.formDemo.status
        params['principalUserId'] = this.getEmployeeInfo('id')

        ///调用接口
        this.ajax.postStream(apiUrl.smsTmpl_AddItem, params, res => {
          if(!res.body.result){
              return this.$message.error(res.body.msg)
            }
            // _this.$root.eventHandle.$emit('removeTab', _this.params.tabName)
            _this.$message({message: '操作成功',type: 'success'});
            _this.saveAuxiliary(false)
            _this.params.__close('refreshBeforeSuccess');
         }, err => {
             _this.$message.error(err)
          })

      },
			// 同步保存至辅助资料
      saveAuxiliary(type) {
                let self = this;
                let keyId = ''
                let url = window.location.href;
                if (/sale.linshimuye.com:8084/.test(url)) {
                    keyId = 59429213192196;
                } else if (/saleuat.linshimuye.com:60004|saleuat.linshimuye.com:60006/.test(url)) {
                    keyId = 59320995545088;
                } else if (/salesit.linshimuye.com:83/.test(url)) {
                    keyId = 58759971602432;
                } else {
                    keyId = 58143689408512;
                }
				let data = {
					categoryPo: {
						code: 'MESSAGE_TYPE',
						name: '短信类型',
						parentCode: '',
						platform: 'NEW_SALE_PLATFORM',
						remark: '',
						system: 0,
						id: keyId
					},
					dataList: [{
						categoryCode:'MESSAGE_TYPE',
						code: this.formDemo.typeCode, // 模板编码
						disableTime: null,
						enableTime: null,
						id: '',
						name: this.formDemo.typeName, // 模板名称
						parentCode: '',
						platform: 'NEW_SALE_PLATFORM',
						status: this.formDemo.status == 2 ? 0 : 1, // 模板状态
						remark: this.formDemo.tmplContent, // 模板内容
						tag: null,
						parentCategoryCode: ''
					}]
				};
				if (type) {
					let obj = JSON.parse(localStorage.getItem('AUXDATA'))
					let list = obj['MESSAGE_TYPE']
					list.forEach(item => {
						if (item.code == self.formDemo.typeCode) {
							data.dataList[0].id = item.id
						}
					})
				}
				this.ajax.postStream('/user-web/api/auxiliary/saveAuxiliaryCategory',data,d=>{
					self.$message({
						message:d.body.msg,
						type:d.body.result?'success':'error'
					})
					if(d.body.result){
						// 更新辅助资料列表
						self.$root.eventHandle.$emit('auxiliaryCategoryListRefresh')
						// 更新本地辅助资料
						self.$root.eventHandle.$emit('updateAuxiliary')
					}
				},e=>{
					self.$message.error(e);
				})
      },
      //更新接口
      submitEditForm() {
        let params = Object.assign({}, this.formDemo) //入参
         let _this = this

        params['principalUserId'] = this.getEmployeeInfo('id')
       this.ajax.postStream( apiUrl.smsTmpl_UpdateItem, params, res => {
         if(!res.body.result){
              return this.$message.error(res.body.msg)
            }
            // _this.$root.eventHandle.$emit('removeTab', _this.params.tabName)
						_this.$message({message: '操作成功',type: 'success'});
						_this.saveAuxiliary(true)
            _this.params.__close('refreshBeforeSuccess');
         }, err => {
             _this.$message.error(err)
          })
      },
      //初始化数据
      init() {
        if (this.params.type === 'edit') {
          // this.formDemo
          // Object.assign(this.formDemo, this.params.form)

          // Sit测试发现上一级表格传进params的数据过于杂乱；导致迁移后接口无法正常运行，这里按现有生产的数据重新组装 2020/08/02 libq
          let paramsForm = JSON.parse(JSON.stringify(this.params.form));
          this.formDemo = {
            createTime: paramsForm.createTime,
            creator: paramsForm.creator,
            creatorName: paramsForm.creatorName,
            editable: paramsForm.editable,
            modifier: paramsForm.modifier,
            modifierName: paramsForm.modifierName,
            modifyTime: paramsForm.modifyTime,
            principalUserId: paramsForm.principalUserId,
            senderType: paramsForm.senderType,
            status: paramsForm.status,
            tmplContent: paramsForm.tmplContent,
            tmplId: paramsForm.tmplId,
            typeCode: paramsForm.typeCode,
            typeName: paramsForm.typeName
          }
        }
      }
    },
    mounted() {
      this.init()
    }
  }
</script>

<style scoped>
.form-box {
  margin-top: 10px;
}
</style>

<style>
.sms-template-demo .el-form-item .el-form-item__label {
    padding: 0 15px 0 10px;
}
</style>
