<!-- 呼叫中心 - 短信模板列表  -->
<template>
	<xpt-list
		ref="smsTemplateList"
		selection=""
		:orderNo="true"
		:btns="btns"
		:colData="cols"
		:data="tableData"
		:pageTotal="totalPage"
		:pageLength="search.page_size"
		:searchPage="search.page_name"
		@search-click="searchClick"
		@page-size-change="pageSizeChange"
		@current-page-change="pageChange"
	></xpt-list>
	<!-- :isNeedClickEvent="true"
		@row-dblclick="rowDBLclick" -->
	<!-- @radio-change="select" -->
</template>
<script>
import baseUrl, { makeUrl, apiUrl } from "../call_system/base.js";
import smsSearch from "./sms_search";
export default {
	props: ["params"],
	components: { smsSearch },
	data() {
		const self = this;
		return {
			btns: [
				{
					type: "success",
					txt: "刷新",
					loading: false,
					click() {
						self.getList();
					}
				},
				{
					type: "primary",
					txt: "创建",
					loading: false,
					click() {
						self.smsDome();
					}
				}
			],
			cols: [
				{
					label: "模板编码",
					prop: "typeCode",
					width: 120
				},
				{
					label: "模板名称",
					prop: "typeName",
					redirectClick(row) {
						let createTabOptions = {
							name: "编辑模板",
							params: {
								type: "edit",
								form: row
							},
							component: () => import("@components/call_system/sms_template_demo.vue")
						};
						self.$root.eventHandle.$emit("creatTab", createTabOptions);

						var oldCloseFun = createTabOptions.params.__close;
						createTabOptions.params.__close = refreshBeforeSuccess => {
							if ((refreshBeforeSuccess = "refreshBeforeSuccess")) {
								self.getList();
							}
							oldCloseFun();
						};
					},
					width: 120
				},
				{
					label: "号码类型",
					prop: "senderType",
					formatter(val) {
						if (val === "SALES_NUMBER") {
							return "营销号";
						} else if(val === 'BUSINESS_NUMBER') {
							return "业务号";
						} else {
						  return val || '';
            }
					},
					width: 120
				},
				{
					label: "状态",
					prop: "status",
					formatter(val) {
						if (val === 1) {
							return "生效";
						} else if (val === 2) {
							return "失效";
						} else {
						  return val || '';
            }
					},
					width: 120
				},
				{
					label: "短信内容",
					prop: "tmplContent",
					width: 300
				},
				{
					label: "创建人",
					prop: "creatorName",
					width: 120
				},
				{
					label: "创建时间",
					prop: "createTime",
					width: 120,
					format: "dataFormat1"
				},
				{
					label: "修改人",
					prop: "modifierName",
					width: 120
				},
				{
					label: "修改时间",
					prop: "modifyTime",
					width: 120,
					format: "dataFormat1"
				}
			],
			tableData: [],
			totalPage: 0,
			// selectData: [], //单选的数据
			search: {
				page_size: 50,
				page_no: 1,
				page_name: "cloud_sms_tmpl",
				where: []
			}
			// 测试数据
			// testData: []
		};
	},
	methods: {
		//查询表单
		getList() {
			let params = {
				page: {
					length: this.search.page_size,
					pageNo: this.search.page_no
				},
				page_name: this.search.page_name,
				where: this.search.where
			};
			this.getSmsTemplateList(params);
		},
		getSmsTemplateList(params, resolve) {
			let _this = this;
			// _this.selectData = [];
			_this.btns[0].loading = true;
			this.ajax.postStream(
				apiUrl.smsTmpl_GetList,
				params,
				res => {
					if (res && res.body.result) {
						_this.tableData = res.body.content.list;
						_this.totalPage = res.body.content.count;
					} else {
						_this.$message.error(res.body.msg);
					}
					resolve && resolve();
					_this.btns[0].loading = false;
				},
				err => {
					resolve && resolve();
					_this.$message.error(err);
					_this.btns[0].loading = false;
				}
			);
		},
		// 创建短信模板
		smsDome() {
      const self = this;
			let createTabOptions = {
				name: "创建模板",
				params: { type: "add" },
				component: () => import("@components/call_system/sms_template_demo.vue")
			};
			self.$root.eventHandle.$emit("creatTab", createTabOptions);

			var oldCloseFun = createTabOptions.params.__close;
			createTabOptions.params.__close = refreshBeforeSuccess => {
				if ((refreshBeforeSuccess = "refreshBeforeSuccess")) {
					self.getList();
				}
				oldCloseFun();
			};
		},
		// 双击事件 // row:object -> 行数据
		// rowDBLclick(row) {
		// 	//打开编辑页面
		// 	const params = {
		// 		type: "edit",
		// 		form: row
		// 	};
		// 	this.$root.eventHandle.$emit("creatTab", {
		// 		name: "编辑模板",
		// 		params: params,
		// 		component: () => import("@components/call_system/sms_template_demo.vue")
		// 	});
		// },
		// 单选数据
		// select(data) {
		// 	this.selectData = data;
		// },
		// 搜索
		searchClick(obj, resolve) {
			this.search.where = obj;
			let params = {
				page: {
					length: this.search.page_size,
					pageNo: this.search.page_no
				},
				page_name: this.search.page_name,
				where: this.search.where
			};
			this.getSmsTemplateList(params, resolve);
		},
		//单页条数
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		//换页
		pageChange(no) {
			this.search.page_no = no;
			this.getList();
		}
	},
	mounted() {
		this.getList();
	}
};
</script>
