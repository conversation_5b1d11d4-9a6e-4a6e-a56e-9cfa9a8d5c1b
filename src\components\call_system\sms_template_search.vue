<!-- 关联订单 -->
<template>
  <div class="xpt-flex">
    <div style="margin-top:8px; margin-bottom: 8px">
      <el-button type="primary" slot='left' size='mini' @click="confirm()">确定</el-button>
      <el-button type="primary" slot='left' size='mini' @click="closeTabs()">关闭</el-button>
    </div>
    <div class="search-input-box">
      <sms-search ref="smsSearch" @handleSearch="searchSmsTemplateList" :showBtn="false"></sms-search>
    </div>
    <div class="xpt-flex__bottom">
      <div class="table-one">
        <el-table
          v-loading="loading"
          @row-click="selectTableRow"
          :data="tableData"
          border
          :highlight-current-row="true">
          <el-table-column prop="typeName" label="类型名称" width="120"></el-table-column>
          <el-table-column prop="tmplContent" label="短信内容" width="500"></el-table-column>
        </el-table>
      </div>
    </div>
    <!-- <el-row class='xpt-pagation'>
		  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
			  	:current-page="pageNow" :page-sizes="[200, 250, 300, 400]" :page-size="pageSize"
			  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
			</el-pagination>
		</el-row> -->
  </div>
</template>

<script>
    import baseUrl, {makeUrl, apiUrl}from './base.js'
    import smsSearch from './sms_search'
  export default {
    props: ['params'],
    components: {
      smsSearch
    },
    data() {
      var _this = this;
      return {
        selected:null,
        loading: false,
        searchParams:{},
        tableData:[
        ],
        isHigh: false,
        pageSize: 200,
        pageNow: 1,
        pageTotal: 0,
      };
    },
    methods: {
      //查询模板列表
      searchSmsTemplateList(params) {
        this.getSmsTemplateList(Object.assign({}, params, {page_size: this.pageSize, page_no: this.pageNow}))
      },
      //获取模板列表
      getSmsTemplateList(params) {
        let _this = this
        params['status'] = 1
         this.ajax.postStream(apiUrl.smsTmpl, params, res => {
            if (res && res.body.result) {
              _this.tableData = res.body.content.list
              _this.pageTotal = res.body.count
            }
            else {
              _this.$message.error(res.body.msg)
            }


         }, err => {
             _this.$message.error(err)
          })
      },
      selectTableRow(row){
        this.selected = row
      },
      confirm(){
        if(null == this.selected){
          return this.$message.error('请选择模板')
        }
        this.params.callback(this.selected)
        this.closeTabs()
      },
      //关闭
      closeTabs() {
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      },
    }
  };
</script>
<style scoped>
</style>
