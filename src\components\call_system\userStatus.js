// 呼叫中心使用者状态
import { apiUrl } from './base.js';
export default {
	data() {
		let self = this;
		return {
			// nowUserData: {}, // 获取当前用户的相关信息
			isTelTraffic: false, //当前用户是否为话务
			isTelTrafficBoss: false, //当前用户是否为话务主管
			// groupId: '' //当前用户所在的组 ID
		};
	},
	watch: {},
	methods: {
		// 获取当前用户的分组信息
		getEmployeeData() {
			let params = {
				pageNo: 1,
				pageSize: 20,
				staffNo: this.getEmployeeInfo('employeeNumber'),
				staffName: '',
				groupName: ''
			};
			this.$http
				.get(apiUrl.personGroup_list, { params })
				.then((res) => {
					if (res.data.code == 0) {
            this.upUserStatus(res.data.content[0]);
					} else if (res.data.code != 0) {
						// this.$message.error(res.data.msg || '');
					}
				})
				.catch((err) => {
					// this.$message.error(`${err.status}` || '');
				})
				.finally(() => {});
		},
		// 更新用户的一些状态
		upUserStatus(data) {
      // // 分离话务主管判断
      // let { groupName } = data;
      // if(groupName == '接待'){
      //   this.isTelTraffic = true;
      // }else{
      //   this.isTelTraffic = false;
      // }
			// 拿到 组名groupName 还有 话务主管状态isChief
			let { groupName, isChief } = data;
			if (groupName == '接待' && isChief != 'Y') {
				this.isTelTraffic = true;
			} else if (groupName == '接待' && isChief == 'Y') {
        this.isTelTraffic = true;
				this.isTelTrafficBoss = true;
			} else {
				this.isTelTraffic = false;
				this.isTelTrafficBoss = false;
			}
		}
	},
	mounted() {
		this.getEmployeeData();
	},
	beforeDestroy() {}
};
