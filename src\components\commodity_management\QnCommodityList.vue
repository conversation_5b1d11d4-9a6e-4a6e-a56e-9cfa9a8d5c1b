<!--TOP商品列表-->
<template>
  <xpt-list
    ref="list"
    :data="list"
    :btns="btns"
    :colData="cols"
    selection="checkbox"
    :pageTotal="count"
    :searchPage="search.page_name"
    @selection-change="selectChange"
    @search-click="presearch"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
  >
    <xpt-upload-v3
            slot="btns"
            uploadBtnText="导入"
            :uploadSize="20"
            acceptTypeStr=".xlsx,.xls"
            :dataObj="uploadDataObj"
            :disabled="false"
            :ifMultiple="false"
            :showSuccessMsg="false"
            @uploadSuccess="uploadSuccess"
            btnType="info"
            style="display: inline-block; margin: 0px 10px"
        ></xpt-upload-v3>
        <el-button
            slot="btns"
            size="mini"
            type="warning"
            @click="showUploadResult()"
            :disabled="false"
            >导入结果</el-button
        >
  </xpt-list>
</template>
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      uploadDataObj: {
          parent_name: "",
          parent_no: ``, //主要通过该参数获取附件列表
          child_name: null,
          child_no: null,
          content: {},
      },
      select:[],
      search: {
        page_name: "lskf_num_iid_weight",
        where: [],
        page_size: self.pageSize,
        page_no: 1,
        if_need_page: "Y",
      },
      list: [],
      count: 0,
      btns: [
        {
          type: "success",
          txt: "刷新",
          click: self.searching,
        },
        {
          type: "danger",
          txt: "批量删除",
          click: self.batchDelete,
        },
      ],
      cols: [
        {
          prop: "num_iid",
          label: "链接ID",
        },
        {
          prop: "type",
          label: "类型",
          format: 'auxFormat',
					formatParams: 'RECOMMENDED_PRODUCT_TYPES',
        },
        {
          prop: "weight_score",
          label: "推荐权重",
        },
        {
          prop: "shop_code",
          label: "店铺编码",
        },
        {
          prop: "shop_name",
          label: "店铺名称",
        },
        {
          prop: "create_time",
          label: "创建时间",
          format: "dataFormat1",
        },
        {
          prop: "creator_name",
          label: "创建人",
        },
      ],
    };
  },
  methods: {
    batchDelete(){
      const ids = this.select.map((item) => item.id);
      console.log(ids)
      if(!ids.length){
        this.$message.error('请选择数据')
        return;
      }
      this.$confirm(
					"当前操作会导致选中的数据被删除，是否继续？",
					"提示",
					{
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "danger",
					}
				).then(() => {
					// 请求后台delete 接口 -- 待修改
					this.ajax.postStream(
						"/plan-web/api/lskfNumIidWeight/batchDelete",
						{ids:ids},
						(res) => {
							if (res.body.result) {
								this.$message.success(res.body.msg || "删除成功！");
								this.searching();
							} else {
								this.$message.error(res.body.msg || "删除失败！");
							}
						},
						(err) => {
							this.$message.error(err);
						}
					);
				}).catch(() => {
					return false;
				});
    },
    //导入结果
        showUploadResult() {
            this.$root.eventHandle.$emit("alert", {
                style: "width:900px;height:600px",
                title: "导入结果",
                params: {
                    url: "/reports-web/api/reports/afterSale/findMyReportList",
                    data: {
                      page_size: this.page_size,
                      page_no: this.page_no,
                      type: "EXCEL_TYPE_LSKF_NUMIID_WEIGHT_IMPORT",
                    },
                    showDownload: true,
                },
                component: () => import("@components/common/eximport"),
            });
        },
    //导入
        importFileUrl(fileUrl) {
            let params = {
                fileUrl: fileUrl,
            };
            this.ajax.postStream(
                "/plan-web/api/lskfNumIidWeight/batchImport",
                params,
                (res) => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg);
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
     //上传成功返回结果
        uploadSuccess(result) {
            if (result.length > 0 && !!result[0].path) {
                this.importFileUrl(result[0].path);
            }
        },
    selectChange(list){
      this.select = list
    },
    presearch(list, resolve) {
      this.search.where = list;
      this.searching(resolve);
    },
    searching(resolve) {
      this.ajax.postStream(
        "/plan-web/api/lskfNumIidWeight/list",
        this.search,
        (res) => {
          console.log(res, "searching");
          if (res.body.result) {
            this.list = res.body.content.list;
            this.getCount();
            this.$message.success(res.body.msg || "获取数据成功");
          } else {
            this.$message.error(res.body.msg || "获取数据失败");
          }
          resolve && resolve();
        },
        (err) => {
          this.$message.error(err || "列表接口异常");
          resolve && resolve();
        }
      );
    },
    getCount() {
      this.ajax.postStream(
        "/plan-web/api/lskfNumIidWeight/listCount",
        this.search,
        (res) => {
          if (res.body.result) {
            this.count = res.body.content.count;
          }
        },
        (err) => {
          self.$message.error(err || "总数接口异常");
        }
      );
    },
    sizeChange(size) {
      // 第页数改变
      this.search.page_size = size;
      this.searching();
    },
    pageChange(page_no) {
      // 页数改变
      this.pageNow = page_no;
      this.search.page_no = page_no;
      this.searching();
    },
  },
  mounted() {
    this.searching();
  },
};
</script>
<style scoped></style>
