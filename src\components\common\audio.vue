<template>
<div class="di main-wrap" v-loading="audio.waiting">
      <!-- 这里设置了ref属性后，在vue组件中，就可以用this.$refs.audio来访问该dom元素 -->
      <audio ref="audio" class="dn" 
      :src="url" :preload="audio.preload"
      @play="onPlay" 
      @error="onError"
      @waiting="onWaiting"
      @pause="onPause" 
      @timeupdate="onTimeupdate" 
      @loadedmetadata="onLoadedmetadata"
      ></audio>
       <!-- <audio ref="audio" class="dn" 
      :src="url" :preload="audio.preload"
      controls
      ></audio> -->
      <div>
        <el-button type="text" @click="startPlayOrPause">{{audio.playing | transPlayPause}}</el-button>
        <el-button v-show="!controlList.noSpeed" type="text" @click="changeSpeed">{{audio.speed | transSpeed}}</el-button>
  
        <el-tag type="info" v-show="false">{{ audio.currentTime | formatSecond}}</el-tag>
  
        <el-slider v-show="!controlList.noProcess" v-model="sliderTime" :format-tooltip="formatProcessToolTip" @change="changeCurrentTime" class="slider"></el-slider>
        
        <el-tag type="info">{{ audio.maxTime | formatSecond }}</el-tag>
  
        <el-button v-show="!controlList.noMuted" type="text" @click="startMutedOrNot">{{audio.muted | transMutedOrNot}}</el-button>
  
        <el-slider v-show="!controlList.noVolume" v-model="volume" :format-tooltip="formatVolumeToolTip" @change="changeVolume" class="slider"></el-slider>
        
        <a :href="url" v-show="!controlList.noDownload" target="_blank" class="download" download>下载</a>
      </div>
    </div>
</template>

<script>

  export default {
    name: 'audio',

    

     props: {
        //播放音频URL
        theUrl: {
            type: String,
            required: true,
        },
        //播放速度
        theSpeeds: {
            type: Array,
            default:function () {
                return [1, 1.5, 2]
            }
        },
        //播放器样式     noDownload noVolume noMuted onlyOnePlaying noProcess noSpeed
        theControlList: {
            type: String,
            default: ''
        }
    },
    data:function(){
        return {
            url: this.theUrl,
            audio: {
                currentTime: 0,
                maxTime: 0,
                playing: false,
                muted: false,
                speed: 1,
                waiting: true,
                preload: 'preload'
            },

            sliderTime: 0,
            volume: 100,
            speeds: this.theSpeeds,

            controlList: {
                // 不显示下载
                noDownload: true,
                // 不显示静音
                noMuted: true,
                // 不显示音量条
                noVolume: true,
                // 不显示进度条
                noProcess: true,
                // 只能播放一个
                onlyOnePlaying: true,
                // 不要快进按钮
                noSpeed: false
            }
        }
    },
    methods:{
     
      setControlList :function() {
          var _self = this;
          var controlList = this.theControlList.split(' ')
          var i;
          var length = controlList.length;
          for(i = 0 ;i <= length;i++) {
              var obj = controlList[i];
              if (obj !== undefined) {
                  this.controlList[obj] = true;
              }
          }
      //     controlList.forEach((item) => {
      //         if(_self.controlList[item] !== undefined){
      //         this.controlList[item] = true
      //         }
      //     })
      },
      changeSpeed:function() {
          var index = this.speeds.indexOf(this.audio.speed) + 1
          this.audio.speed = this.speeds[index % this.speeds.length]
          this.$refs.audio.playbackRate = this.audio.speed
      },
      startMutedOrNot:function() {
          this.$refs.audio.muted = !this.$refs.audio.muted
          this.audio.muted = this.$refs.audio.muted
      },
      // 音量条toolTip
      formatVolumeToolTip:function(index) {
          return '音量条: ' + index
      },
      // 进度条toolTip
      formatProcessToolTip:function(index) {
          if(!index)
          {
              index = 0;
          }
          index = parseInt(this.audio.maxTime / 100 * index)
          return '进度条: ' + realFormatSecond(index)
      },
      // 音量改变
      changeVolume:function(index) {
          if(!index)
          {
              index = 0;
          }
          this.$refs.audio.volume = index / 100
          this.volume = index
      },
      // 播放跳转
      changeCurrentTime:function(index) {
          this.$refs.audio.currentTime = parseInt(index / 100 * this.audio.maxTime)
      },
      startPlayOrPause:function() {
          return this.audio.playing ? this.pausePlay() : this.startPlay()
      },
      // 开始播放
      startPlay:function() {
          console.log('startPlay');
          this.$refs.audio.play()
      },
      // 暂停
      pausePlay:function() {
        console.log('pasusePlay');
          this.$refs.audio.pause()
      },
      // 当音频暂停
      onPause :function() {
        console.log('onPause');
          this.audio.playing = false
      },
      // 当发生错误, 就出现loading状态
      onError :function(res) {
        console.log('onError');
        console.log(res)
          this.audio.waiting = true
      },
      // 当音频开始等待
      onWaiting :function(res) {
          console.log('onWaiting');
          console.log(res)
      },
      // 当音频开始播放
      onPlay :function(res) {
        console.log('onPlay');
          console.log(res)
          this.audio.playing = true
          this.audio.loading = false

          if(!this.controlList.onlyOnePlaying){
              return
          }

          var target = res.target

          var audios = this.$refs.audio
                  //   var audios = document.getElementsByTagName('audio');

          var i;
          var length = audios.length;
          console.log(audios);
          // for(i = 0;i <= length; i ++)
          // {
          //     var obj = audios[i]
          //     if( obj !== target){
          //         audios[i].pause()
          //     }
          // }
          // [...audios].forEach((item) => {
          //     if(item !== target){
          //     item.pause()
          //     }
          // })
      },
      // 当timeupdate事件大概每秒一次，用来更新音频流的当前播放时间
      onTimeupdate:function(res) {
          // console.log('timeupdate')
          // console.log(res,this.audio.currentTime,this.audio.maxTime)
          console.log('onTimeupdate');
          // this.audio.currentTime = res.target.currentTime
          // this.sliderTime = parseInt(this.audio.currentTime / this.audio.maxTime * 100)
      },
      // 当加载语音流元数据完成后，会触发该事件的回调函数
      // 语音元数据主要是语音的长度之类的数据
      onLoadedmetadata:function(res) {
          console.log('onLoadedmetadata')
          console.log(res)
          this.audio.waiting = false
          this.audio.maxTime = parseInt(res.target.duration)
      },
  },

    filters: {
      formatSecond:function(second) {
          if(!second)
          {
              second = 0;
          }
          return realFormatSecond(second)
      },
      transPlayPause:function(value) {
          return value ? '暂停' : '播放'
      },
      transMutedOrNot:function(value) {
          return value ? '放音' : '静音'
      },
      transSpeed:function(value) {
          return '快进: x' + value
      }
  },
  created:function() {
      this.setControlList()
  }
  };

  function realFormatSecond (second) {
  var secondType = typeof second

  if (secondType === 'number' || secondType === 'string') {
      second = parseInt(second)

      var hours = Math.floor(second / 3600)
      second = second - hours * 3600
      var mimute = Math.floor(second / 60)
      second = second - mimute * 60

      return hours + ':' + ('0' + mimute).slice(-2) + ':' + ('0' + second).slice(-2)
  } else {
      return '0:00:00'
  }
}
</script>
