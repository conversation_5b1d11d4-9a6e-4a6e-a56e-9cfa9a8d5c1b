<template>
  <el-cascader
    ref="cascader"
    v-bind="$attrs"
    :options="options"
    size="small"
    :value="value"
    @change="handleChange"
    class="aux-cascader"
    @active-item-change="handleExpandChange"
  >
  </el-cascader>
</template>

<script>
export default {
  name: 'auxCascader',
  props: {
    auxNames: {
      type: Array,
      default: () => []
    },
    filter: {
      type: Function,
      default: (item) => item
    },
    showInvalid: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    },
    showRemark: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: [],
      observer: null
    };
  },
  methods: {
    getOptions() {
      let axuOptions = [];

      this.auxNames.forEach((name,index) => {
        if (!__AUX.get(name)) return;
        if(index == 0) {
          axuOptions = axuOptions.concat(__AUX.get(name).filter(this.filter));
        } else {
          const codes = axuOptions.map(item => item.code);
          axuOptions = axuOptions.concat(__AUX.get(name).filter(this.filter).filter(item => codes.includes(item.parentCode)));
        }
      });
      this.options = this.buildTree(axuOptions);
    },
    buildTree(data) {
      const map = {};
      const processedNodes = new Set();

      data.forEach(item => {
        const { name, code,categoryCode,status } = item;
        if (!code) {
          console.warn(`Node without code:`, item);
          return;
        }
        map[`${categoryCode}-${code}`] = { value: code, label: name, disabled:status===0,...item };
      });

      const result = [];

      // 构建树结构
      data.forEach(item => {
        const { code, parentCode,categoryCode,parentCategoryCode } = item;
        const node = map[`${categoryCode}-${code}`];

        if (!node) {
          return;
        }

        if(processedNodes.has(`${categoryCode}-${code}`)){
          return
        }

        if (parentCode && map.hasOwnProperty(`${categoryCode}-${code}`)) {
          if (!map[`${parentCategoryCode}-${parentCode}`].children) {
            map[`${parentCategoryCode}-${parentCode}`].children = [];
          }
          map[`${parentCategoryCode}-${parentCode}`].children.push(node);
          processedNodes.add(`${categoryCode}-${code}`);
        } else {
          result.push(node); // 根节点
          processedNodes.add(`${categoryCode}-${code}`);
        }
      });

      return result;
    },
    handleChange(value) {
      this.$emit('change', value);
    },
    handleExpandChange(activePath) {
      if (!this.showRemark) {
        return;
      }

      this.$nextTick(() => {
        this.setTitleForPopper(activePath);
      });
    },
    setTitleForPopper(activePath) {
      const popper = this.$refs.cascader.popperElm;
      if (popper) {
        // 获取最后一级菜单
        const lastMenu = popper.querySelector('.el-cascader-menu:last-child');
        if (lastMenu) {
          const leafNodes = lastMenu.querySelectorAll('.el-cascader-menu__item');
          leafNodes.forEach((node, index) => {
            // 获取对应的选项对象
            const option = this.findOptionByValue(activePath, this.options);
            if (option && option.children) {
              const leafOption = option.children[index];
              if (leafOption.remark) {
                node.setAttribute('title', leafOption.remark);
              }
            }
          });
        }
      }
    },
    findOptionByValue(path, options) {
      let currentOptions = options;
      path = path.filter((item) => item);
      for (let i = 0; i < path.length; i++) {
        const value = path[i];
        const foundOption = currentOptions.find(option => option.value === value);
        if (!foundOption) return null;
        if (i < path.length - 1 && foundOption.children) {
          currentOptions = foundOption.children;
        } else {
          return foundOption;
        }
      }
      return null;
    },
    observePopper() {
      const cascaderRef = this.$refs.cascader.$el;

      this.observer = new MutationObserver((mutationsList) => {
        for (const mutation of mutationsList) {
          if (mutation.type === 'childList' || mutation.type === 'attributes') {
            const popper = this.$refs.cascader.popperElm;
            if (popper) {

              this.setTitleForPopper(this.value.slice(0, this.value.length - 1));
              this.observer.disconnect(); // 停止观察
            }
          }
        }
      });

      this.observer.observe(cascaderRef, {
        childList: true,
        subtree: true,
        attributes: true
      });
    }
  },
  mounted() {
    this.getOptions();
    this.$nextTick(() => {
      this.observePopper(); // 延迟初始化 MutationObserver
    });
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
};
</script>

<style>
.aux-cascader.is-disabled .el-cascader__label {
  color: #4e5e6e !important;
}
</style>
