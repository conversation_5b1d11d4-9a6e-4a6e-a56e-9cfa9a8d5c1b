<template>
  <div class="el-tree-node pmm_tree_node" 
    @click.stop="handleClick"
    v-show="node.visible"
    :class="{
      'is-expanded': childNodeRendered && expanded,
      'is-current': tree.store.currentNode === node,
      'is-hidden': !node.visible
    }">
    <div class="el-tree-node__content"
      :style="{ 'padding-left': (node.level - 1) * tree.indent + 'px' }">
      <span
        class="el-tree-node__expand-icon"
        @click.stop="handleExpandIconClick"
        :class="{  'is-leaf': !node.data.haveChildren,expanded: !node.isLeaf && expanded  }">
      </span>
      <!--  <span
        class="el-tree-node__expand-icon"
        @click.stop="handleExpandIconClick"
        :class="{expanded: !node.isLeaf && expanded }">
      </span> -->
      
      <el-checkbox
        v-if="showCheckbox"
        v-model="node.checked"
        :indeterminate="node.indeterminate"
        @change="handleCheckChange"
        @click.native.stop="handleUserClick(node.data)">
      </el-checkbox>
      <span
        v-if="node.loading"
        class="el-tree-node__loading-icon el-icon-loading">
      </span>
      <div class='el-tree-table pmm-el-tree-table'>
        <el-table :data='[node.data]' :show-header='false' >
          <el-table-column v-for='(item,index) in props.label' :key='index' :prop='item.name' show-overflow-tooltip align='left' :width="item.width" >
          <template slot-scope='scope'>
            <a v-if='item.childNode' href='javascript:;' @click='item.event&&item.event(scope.row)'>{{scope.row[item.name]}}</a>
            <template v-else>
              {{scope.row[item.name]|treeFilter(item.name)}}
            </template>
           
          </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <collapse-transition>
      <div
        class="el-tree-node__children"
        v-show="expanded">
        <el-bom-tree-node
          :render-content="renderContent"
          v-for="child in node.childNodes"
          :key="getNodeKey(child)"
          :node="child" 
          :props='props' 
          @node-expand="handleChildNodeExpand">
        </el-bom-tree-node>
      </div>
    </collapse-transition>
  </div>
</template>

<script type="text/jsx">
  import CollapseTransition from './model/collapse-transition';
  import emitter from './model/emitter';

  export default {
    name: 'ElBomTreeNode',

    componentName: 'ElBomTreeNode',

    mixins: [emitter],

    props: {
      node: {
        default() {
          return {};
        }
      },
      indexNode:{
        default(){
          return 0;
        }
      },
      props: {},
      renderContent: Function
    },

    components: {
      // ElCheckbox,
      CollapseTransition,
      NodeContent: {
        props: {
          node: {
            required: true
          },
          props:''
        },
        render(h) {
         /* const parent = this.$parent;
          const node = this.node;
          const data = node.data;
          const store = node.store;
          console.log('parent',parent);
          console.log('node',node);
          console.log('data',data);
          console.log('store',store);
          if(parent.renderContent){
            return parent.renderContent.call(parent._renderProxy, h, { _self: parent.tree.$vnode.context, node, data, store })
          }else{
            let nodes = [],
              self = this;
            ;(this.props.label||[]).find(d=>{
              
              let txt = this.node.data[d.name];
              if(d.name==='isAdditional'){
                txt = txt?'是':'否';
              }
              if(d.name==='type'){
                if(txt==='bom_material_num'){
                  txt = '物料编码'
                }
                if(txt==='bom_material_name'){
                  txt = '物料名称'
                }
                if(txt==='bom_material_desc'){
                  txt = '规格描述'
                }
                if(txt==='bom_version_unit'){
                  txt = '单位'
                }
                if(txt==='bom_version'){
                  txt = 'BOM编码'
                }
                if(txt==='bom_version_group'){
                  txt = 'BOM分组'
                }
                if(txt==='bom_version_name'){
                  txt = 'BOM名称'
                }
                if(txt==='materialUnit'){
                  txt = '销售单位'
                }
              }
              nodes.push(dom)
            })
            return h('ul',nodes);
          }*/
        }
      }
    },

    data() {
      return {
        tree: null,
        expanded: false,
        childNodeRendered: false,
        showCheckbox: false,
        oldChecked: null,
        oldIndeterminate: null
      };
    },

    watch: {
      'node.indeterminate'(val) {
        this.handleSelectChange(this.node.checked, val);
      },

      'node.checked'(val) {
        this.handleSelectChange(val, this.node.indeterminate);
      },

      'node.expanded'(val) {
        this.expanded = val;
        if (val) {
          this.childNodeRendered = true;
        }
      }
    },

    methods: {

      getNodeKey(node, index) {
        const nodeKey = this.tree.nodeKey;
        if (nodeKey && node) {
          return node.data[nodeKey];
        }
        return index;
      },

      handleSelectChange(checked, indeterminate) {
        if (this.oldChecked !== checked && this.oldIndeterminate !== indeterminate) {
          this.tree.$emit('check-change', this.node.data, checked, indeterminate);
        }
        this.oldChecked = checked;
        this.indeterminate = indeterminate;
      },

      handleClick() {
        const store = this.tree.store;
        store.setCurrentNode(this.node);
        this.tree.$emit('current-change', store.currentNode ? store.currentNode.data : null, store.currentNode);
        this.tree.currentNode = this;
        if (this.tree.expandOnClickNode) {
          this.handleExpandIconClick();
        }
        this.tree.$emit('node-click', this.node.data, this.node, this);
      },

      handleExpandIconClick() {
        //if (this.node.isLeaf) return;

        if (this.expanded) {
          this.tree.$emit('node-collapse', this.node.data, this.node, this);
          this.node.collapse();
        } else {
          this.tree.$emit('node-click', this.node.data, this.node, this);
          this.node.expand();
          this.$emit('node-expand', this.node.data, this.node, this);

        }
      },

      handleUserClick(data) {

        if (this.node.indeterminate) {
          this.node.setChecked(this.node.checked, !this.tree.checkStrictly);
        }
        this.tree.$emit('user-click',data)
      },

      handleCheckChange(ev) {
        if (!this.node.indeterminate) {
          this.node.setChecked(ev.target.checked, !this.tree.checkStrictly);
        }
      },

      handleChildNodeExpand(nodeData, node, instance) {
        this.broadcast('ElTreeNode', 'tree-node-expand', node);
        this.tree.$emit('node-expand', nodeData, node, instance);
      }
    },

    created() {
      const parent = this.$parent;

      if (parent.isTree) {
        this.tree = parent;
      } else {
        this.tree = parent.tree;
      }

      const tree = this.tree;
      if (!tree) {
        console.warn('Can not find node\'s tree.');
      }

      const props = tree.props || {};
      const childrenKey = props['children'] || 'children';

      this.$watch(`node.data.${childrenKey}`, () => {
        this.node.updateChildren();
      });

      this.showCheckbox = tree.showCheckbox;

      if (this.node.expanded) {
        this.expanded = true;
        this.childNodeRendered = true;
      }

      if(this.tree.accordion) {
        this.$on('tree-node-expand', node => {
          if(this.node !== node) {
            this.node.collapse();
          }
        });
      }
      // console.log(this.props.label)
    },
    filters:{
      treeFilter(val,type){
        let str = val;
        /*console.log('看看转换')
        console.log(val,type);*/
        if(typeof(val)!=='undefined' && type == 'isAdditional'){
          str = val?'是':'否'
        }
        
        return str;
      }
    }
  };
</script>
<style type="text/css" >
  .pmm-el-tree-table .xpt-flex, .pmm-el-tree-table .el-table{height: auto !important;}
 /* .pmm-el-tree-table{width: 100%;}*/
  .pmm-el-tree-table .el-table__body-wrapper{overflow: hidden;border-top: 1px solid #d1dbe5;}
  /*.pmm-el-tree-table .el-table__body-wrapper .el-table__row td:last-child{border-top: 1px solid #d1dbe5; }*/
  .pmm_tree_node .el-tree-node__children{overflow: visible;}
   .pmm_tree_node.el-tree-node{border-top: 0;}
   .pmm_tree_node .el-tree-node__content{border-top: 1px solid #d1dbe5;}
   .pmm_tree_node.el-tree-node:last-child .el-table__body-wrapper{border-bottom: 1px solid #d1dbe5;}
   #pmm_js table.el-table__header,#pmm_js table.el-table__body{min-width: 0;}

</style>
