<!-- 导出 -->
<template>
	<el-button type='primary' size='mini' @click="exportExcel" :disabled="disabled">{{btnText}}</el-button>
</template>
<script>
	
	export default {
		data() {
			var self = this;
			return {
				
			}
		},
		props: {

			disabled:{
				type:<PERSON><PERSON><PERSON>,
				default(){
					return false;
				}
			},
			//按钮文字
			btnText:{
				type:String,
				default(){
					return '导出';
				}
			},

			//导出列表tab描述
			tabText:{
				type:String,
				default(){
					return '导出列表';
				}
			},

			//加入导入计划的参数值,里面有URL，根据条件查询的那些数据
			taskData:{
				type:Object,
				default(){
					return {};
				}
			},

			//查询加入导入计划的URL
			getListUrl:{
				type:String,
				default(){
					return '';
				}
			},

			//列表要过虑的字段
			otherInfo:Object,

			//当前条件下查询的数据值，用这个来判断是否有要导入的数据
			data:{
				type:Array,
				default(){
					return [];
				}
			},
			//向后扩展的数据
			otherInfo:{
				type:Object,
				default(){
					return {};
				}
			}
			
		},
		methods: {
			//点击导出
			exportExcel(){
				//console.log('asdfasdfasdfsadfsdf');
				var listData = this.data;
				if(!listData || !listData.length) {
					this.$message.error('空白页面，无法进行导出的操作');
					return;
				}
				var _this = this;
				var data = this.taskData,
					url = data.url,
					submitData = data.data,
					tabText = this.tabText,
					getListUrl = this.getListUrl;
				if(!url || !getListUrl) return;
			    new Promise((resolve,reject)=>{
			        _this.ajax.postStream(url,submitData,d=>{
			          if(d.body.result) resolve(d.body)
			          else reject(d.body.msg)
			        })
			    }).then(v => {
			        _this.$message.success(v.msg)
			        _this.$root.eventHandle.$emit('creatTab',{
			            name:tabText,
			            params:{url:getListUrl,data:_this.otherInfo},
			            component:() => import('@components/common/exout.vue')
			         })
			    }).catch(e => this.$message.error(e))
			}
			
		},
		watch: {
			

		}
	}
</script>

