<template>
	<div :class='isShow?"xpt-btngroup active":"xpt-btngroup"' class="mgr10" @mouseleave='outs'>
		<el-button-group>
			<el-button size='mini' :type='btngroup[0].type' @click='isDisabled?"":btngroup[0].click()' :class='isDisabled || btngroup[0].isDisabled?"xpt-disabled":""' :disabled='isDisabled || btngroup[0].isDisabled?true:false' :style="{width:width}">{{btngroup[0].txt}}</el-button>
			<el-button size='mini' :type='btngroup[0].type' :class='isDisabled || btngroup[0].isDisabled?"xpt-disabled xpt-btngroup__caret":"xpt-btngroup__caret"' @click='click'>
				<i class='el-icon-caret-bottom'></i>
			</el-button>
		</el-button-group>
		<div class='xpt-btngroup__list' v-if='isShow'>
			<el-button v-for='(b,index) in downBtnList' :key='index' size='mini' :type='b.type' @click='b.click && b.click(b.value)' :disabled='isDisabled || b.isDisabled'>{{b.txt}}</el-button>
		</div>
	</div>
</template>
<script>
export default {
	data(){
		return {
			isShow:false,
			isOverList:false,
			isDisabled:false
		}
	},
	methods:{
		out(e){
			var self = this;
			var nodeName = e.target.nodeName.toLowerCase(),
				parentClassName = e.target.parentElement.className
			if(nodeName==='button'){
				if(parentClassName.indexOf('el-button-group')===-1&&parentClassName.indexOf('xpt-btngroup__list')===-1){
					self.isShow = false
				}

			}else if(nodeName==='span'){
				if(parentClassName.indexOf('xpt-btngroup__caret')===-1&&parentClassName.indexOf('el-button')===-1){
					self.isShow = false
				}

			}else if(nodeName==='i'){
				if(e.target.parentElement.parentElement.className.indexOf('xpt-btngroup__caret')===-1){
					self.isShow = false
				}
			}else{
				self.isShow = false
			}
		},
		outs(e){
			this.isShow = false
		},
		over(){
			this.isShow = true
		},
		click(){
			if(this.isDisabled) return;
			if(this.beforeClick){
			  this.beforeClick();
      }
			this.isShow = !this.isShow
		}
	},
	/*
	取第一个数组对象为默认显示按钮
	btngroup:[{
		type:'warning',		el-button的type属性
		txt:'按钮组',		el-button的名字
		click(){}			click事件
	}]
	*/
	props:{
    btngroup:Array,
    disabled:Boolean,
    width:{
      type:String,
      default:() => {
        return 'auto'
      }
    },
    /**
     * @Description: 暴露一个方法，在点击this.click的时候执行
     * <AUTHOR>
     * @date 2020/7/23
    */
    beforeClick:Function
  },
	watch:{
		disabled(n,o){
			this.isDisabled = n;
		}
	},
	computed:{
		downBtnList() {
            return this.btngroup.slice(1)||[]
        }
	},
	mounted(){
		var self = this;
		// if(this.disabled){
			this.isDisabled = this.disabled
		// }
		// this.$el.querySelector('.xpt-btngroup__caret').addEventListener('mouseleave',function(){
		// 	self.isShow = false
		// })
	}
}
</script>
<style type="text/css">
.xpt-disabled,.xpt-disabled:hover,.xpt-disabled:active,.xpt-disabled:focus{
	cursor:not-allowed;
	background: #eef1f6 !important;
	border:1px solid #d1dbe5;
	color:#bfcbd9;
}
</style>
