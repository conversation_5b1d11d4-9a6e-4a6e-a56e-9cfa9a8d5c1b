<!-- 带点击加载中和按钮禁用状态的通用组件 -->
<template>
	<el-button :type='type' @click='click' size='mini' :disabled='disabled||loading' :loading='loading' :icon='icon'><slot></slot></el-button>
</template>
<script>
export default {
	props: {
		type: {
			type: String,
			default() {
				return 'primary';
			}
		},
		icon: {
			type: String
		},
		disabled: {
			type: Boolean
		}
	},
	data() {
		return {
			loading: false
		}
	},
	methods: {
		click(event) {
			this.loading = true
			new Promise((resolve) => {
				/*
				向父组件派发事件
				传递的参数为resolve和event属性
				父组件在需要改变按钮加载状态的地方执行resolve方法即可
				*/
				this.$emit('click', {resolve, event});
			}).then(() => {
				this.loading = false;
			})
		}
	},
	mounted() {
		console.log(typeof this.disabled, this.disabled)
	}
}
</script>
