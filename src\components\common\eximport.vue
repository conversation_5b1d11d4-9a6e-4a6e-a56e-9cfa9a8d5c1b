<!-- 导入列表组件 -->
<template>
    <xpt-list
        :data="tableData"
        :btns="btns"
        :colData="cols"
        :pageTotal="totalPage"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        selection="hidden"
    >
        <template slot="file_path" slot-scope="scope" v-if="showDownload">
            <a v-if="canDownLoad(scope.row)" :href="scope.row.file_path"
                >下载</a
            >
        </template>
    </xpt-list>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            btns: [
                {
                    type: "success",
                    txt: "刷新",
                    loading: false,
                    click: self.search,
                },
            ],
            cols: [
                {
                    label: "提交人",
                    width: 80,
                    prop: "submit_man",
                },
                {
                    label: "程序名称",
                    prop: "program_name",
                },
                {
                    label: "运行状态",
                    prop: "run_status",
                    width: 80,
                    formatter: self.formats,
                },
                {
                    label: "程序状态",
                    prop: "program_status",
                    width: 80,
                    formatter: self.formats,
                },
                {
                    label: "提交时间",
                    prop: "submit_time",
                    format: "dataFormat1",
                },
                {
                    label: "完成时间",
                    prop: "finish_time",
                    format: "dataFormat1",
                },
                {
                    label: "失败原因",
                    prop: "result",
                },
            ],
            searchInput: "",
            tableData: [],
            currentPage: 1,
            totalPage: 0,
            showDownload: true, //是否显示下载列，假值不显示'',undefined,null,false,0,NaN
        };
    },
    methods: {
        search() {
            let self = this;
            let url = this.params.url;
            if (!url) return;
            let data = {
                page_size: this.pageSize,
                page_no: this.currentPage,
            };
            let paramsData = this.params.data;
            for (let key in paramsData) {
                data[key] = paramsData[key];
            }
            this.btns[0].loading = true;
            this.ajax.postStream(
                url,
                data,
                (res) => {
                    if (res.body.result) {
                        self.tableData = res.body.content.list;
                        self.totalPage = res.body.content.count;
                    }
                    this.btns[0].loading = false;
                },
                (res) => {
                    this.btns[0].loading = false;
                    this.$message.error(res);
                }
            );
        },
        handleSizeChange: function (val) {
            //val 为每页的条数
            this.pageSize = val;
            this.search();
        },
        handleCurrentChange: function (val) {
            //val为当前页数
            this.currentPage = val;
            this.search();
        },
        formats(row) {
            return (
                {
                    EXEC_WAIT: "等待",
                    EXEC_RUNING: "执行中",
                    EXEC_FAILED: "失败",
                    EXEC_SUCCESS: "成功",
                }[row] || row
            );
        },
        canDownLoad(row) {
            return row.run_status == "EXEC_SUCCESS" && row.file_path;
        },
    },
    created() {
        if (this.params.showDownload != undefined) {
            this.showDownload = !!this.params.showDownload;
        }
        if (this.showDownload) {
            this.cols.push({
                label: "输出文件",
                width: 80,
                slot: "file_path",
            });
        }
    },
    mounted() {
        this.search();
    },
    destroyed() {},
};
</script>
