<!-- 导出列表组件 -->
<template>
   	<xpt-list
	   	:data='tableData'
	   	:btns='btns'
	   	:colData='cols'
	   	:pageTotal='totalPage'
	   	placeholder='请输入搜索条件'
	   	@search-click='searchFun'
	   	@page-size-change='handleSizeChange'
	   	@current-page-change='handleCurrentChange'
   	>
   		<template slot='file_path' slot-scope='scope'>
		    <a v-if='canDownLoad(scope.row)' v-bind:href="scope.row.file_path">下载</a>
   		</template>
   	</xpt-list>
</template>
<script>
	export default {
		props:['params'],
		data() {
			let self = this;
			return {
				btns: [{
					type: 'primary',
					txt: '刷新',
					loading: false,
					click: self.searchFun
				}],
				cols: [
					{
//						label: '请求编号',
//						prop: 'request_no'
//					}, {
						label: '提交人',
						width: 80,
						prop: 'submit_man'
					}, {
						label: '程序名称',
						prop: 'program_name'
					}, {
//						label: '参数',
//						prop: 'parameter'
//					}, {
						label: '运行状态',
						prop: 'run_status',
						width: 80,
						formatter: self.formats
					}, {
						label: '程序状态',
						prop: 'program_status',
						width: 80,
						formatter: self.formats
					}, {
						label: '提交时间',
						prop: 'submit_time',
						format: 'dataFormat1'
					}, {
						label: '完成时间',
						prop: 'finish_time',
						format: 'dataFormat1'
					}, {
						label: '请求日志',
						prop: 'text'
					},
          			{
						label: '失败原因',
						prop: 'result'
					}, {
						label: '输出文件',
						width: 80,
						slot: 'file_path'
					}
				],
				searchInput:'',
				tableData:[],
				currentPage:1,
				totalPage:0,
			}
		},
		methods:{
			searchFun(){
				var _this = this;
				var url = this.params.url;
				if(!url) return;
				var data = {
					page_size:this.pageSize,
					page_no:this.currentPage
				};
				var paramsData = this.params.data
				for(var key in paramsData){
					data[key] = paramsData[key];
				}
				this.btns[0].loading = true;
				this.ajax.postStream(url, data, (res) => {
					if(res.body.result){
						var listData = res.body.content.list;
						for(let v of listData){
							if(v.run_status=="EXEC_SUCCESS") {
								v.isDownLoad=true;
							}
						}
						_this.tableData = listData;
						_this.totalPage = res.body.content.count
					}
					this.btns[0].loading = false;
				},(res) => {
					this.btns[0].loading = false;
					this.$message.error(res);
				})
			},
			handleSizeChange:function(val){				//val 为每页的条数
				this.pageSize = val;
				this.searchFun();
			},
			handleCurrentChange:function(val){
				//val为当前页数
				this.currentPage=val;
				this.searchFun();
			},
			formats(row){
				switch(row) {
					case 'EXEC_WAIT': return '等待';
					case 'EXEC_RUNING': return '执行中';
					case 'EXEC_FAILED': return '失败';
					case 'EXEC_SUCCESS': return '成功';
					default: return row
				}
			},
			canDownLoad(row) {
				return row.run_status == 'EXEC_SUCCESS' && row.file_path
			}
		},
		mounted(){
			var _this = this;
			this.searchFun()
			_this.$root.eventHandle.$on('export',function(d){
				if(d.val == "A"){
					_this.searchFun();
				}
			});
		},
		props:['params'],
		destroyed(){
			this.$root.offEvents('export');
		}
	}
</script>
