<template>
  <div>
    <el-input
      :icon="isOpen ? 'linshi-eye-open' : 'linshi-eye-close'"
      v-model="eyeData"
      :on-icon-click="handleIconClick"
      :disabled="disabled"
      :size="size"
      :readonly="readonly"
      @change="updateInput"
      v-loading="isLoading"
      :class="['eyeSwitchInput',hideBorder?'eyeSwitchBorderNone':'',hideBackground?'eyeSwitchHideBackground':'']"
      :style="{width:width+'px'}"
      :maxlength='maxlength'
      :placeholder='placeholder'
      :autofocus='autofocus'
      @blur="blurInput"
      :aboutNumber="aboutNumber"
    >
    </el-input>
  </div>
</template>
<script>
import "@/assets/css/iconfont.css";
export default {
  name: "EyeSwitch",
  props: {
    hideType:{
      type:String,
      default:'phoneNumber'//phoneNumberShort,phoneNumber,address,name
    },
    //是否需要回调函数-判断是否能显示文本，为true是需带上onOpenHideData回调函数
    isCallback: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    value: {
      default: "",
    },
    //是否禁用
    disabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    //宽度
    width:{
      type: String,
      default: () => {
        return '';
      },
    },
    //大小
    size: {
      type: String,
      default: () => {
        return "mini";
      },
    },
    //只读
    readonly:{
      type:Boolean,
      default:()=>{
        return false;
      }
    },
    //隐藏边框
    hideBorder: {
      type: Boolean,
      default:()=>{
        return false;
      }
    },
    //隐藏背景
    hideBackground: {
      type: Boolean,
      default:()=>{
        return false;
      }
    },
    //最大输入长度
    maxlength: {
      type: Number,
      default:()=>{
        return 20;
      }
    },
    //占位宽文本
    placeholder: {
      type: String,
      default:()=>{
        return '';
      }
    },
    //自动获取焦点
    autofocus: {
      type: Boolean,
      default:()=>{
        return false;
      }
    },
    //应用显隐组件的当前主要单据号
    aboutNumber: {
      type: String,
      default:()=>{
        return '';
      }
    },
  },
  data() {
    return {
      eyeData: "",//当前文本
      eyeHideData: "", //隐藏文本
      eyeOpenData: "", //开放文本
      isOpen: false, //是否打开
      canIuseEye: true, //是否可以使用打开
      isLoading: false, //是否请求接口中
    };
  },
  methods: {
    updateInput(val) {
      console.log('updateInput')
      this.eyeData = val;
      // this.eyeHideData = val;
      // this.eyeOpenData = val;
      this.isOpen = true;
      this.canIuseEye = false;
      this.$emit("input", val);
    },
    blurInput(val){
      this.$emit("blur", val);
    },
    handleIconClick() {
      if (
        !this.eyeData ||
        !this.canIuseEye ||
        !this.isPhoneNumber(this.eyeOpenData)&&this.hideType=='phoneNumber'
      ) {
        return;
      }
      this.switchHideData();
    },
    //验证是否手机还是电话号码
    isPhoneNumber(num){
      return /^0\d{2,3}-?\d{7,8}$/.test(num)||/^1\d{10}$/.test(num)
    },
    saveUserSensitiveLookLog(){
      let self=this
      let params={}
      // params.userNumber=this.getUserInfo('employeeNumber');
      params.operatePageName=this.activeTabName;
      params.operateType=this.isOpen?'LOOK':'HIDE';
      params.lookContent=this.eyeOpenData;
      this.aboutNumber&&this.aboutNumber.length>0&&(params.number=this.aboutNumber);
      console.table(params)
      this.ajax.postStream('/user-web/api/saveUserSensitiveLookLog',params,res=>{
        if(res.body.result){
        }else{
          self.$message.error(res.body.msg)
        }
      },err=>{
        self.$message.error(err)
      })
    },
    switchHideData() {
      let self = this;
      if (!this.isOpen) {
        if (this.isCallback) {
          self.isLoading = true;
          let params = {
            callback: function (result,msg) {
              if (!result) {
                self.$message.error(msg?msg:"无权限查看");
              } else {
                self.eyeData = self.eyeOpenData;
                self.isOpen = true;
                self.saveUserSensitiveLookLog();
              }
              self.isLoading = false;
            },
            isOpen: true
          };
          self.$emit("onOpenHideData", params);
        } else {
          self.eyeData = self.eyeOpenData;
          self.isOpen = true;
          self.saveUserSensitiveLookLog();
        }
      } else {
        if (this.isCallback) {
          self.isLoading = true;
          let params = {
            callback: function (result,msg) {
              if (!result) {
                self.$message.error(msg?msg:"无权限查看");
              } else {
                self.eyeData = self.eyeHideData;
                self.isOpen = false;
              }
              self.isLoading = false;
            },
            isOpen: false
          };
          self.$emit("onOpenHideData", params);
        } else {
          self.eyeData = self.eyeHideData;
          this.isOpen = false;
        }
      }
    },
    encryptPhoneNumber(num) {
        if(this.isPhoneNumber(num)){
          return String(num).includes('-')?`${String(num).substr(0, 4)}****${String(num).substr(8)}`:`${String(num).substr(0, 3)}****${String(num).substr(7)}`;
        }else{
          return num
        }
        // return Number(num)?`${String(num).substr(0, 3)}****${String(num).substr(7)}`:num;
        // return Number(num)?String(num).replace(/^(\d{3})\d{4}(\d+)/, "$1****$2"):num;
    },
    encryptPhoneNumberShort(num) {
        if(this.isPhoneNumber(num)){
          let length=String(num).length
          let leftnum=String(num).substr(length-4)
          return leftnum.padStart(length,'*');
        }else{
          return num
        }
    },
    encryptName(val) {
        if(val){
          return `${String(val).substr(0,1).padEnd(val.length,'*')}`
        }else{
          return val
        }
    },
    encryptAddress(val) {
      let length=val&&String(val).length;
      if(length>=3&&length<=10){
        let headStr=val.substr(0,1)
        let endStr=val.substr(-1)
        let midStr=''.padEnd(length-2,'*')
        return `${headStr}${midStr}${endStr}`
      }else if(length>10){
          let headStr=val.substr(0,2)
          let endStr=val.substr(length-3)
          let midStr=''.padEnd(length-5,'*')
          return `${headStr}${midStr}${endStr}`
      }else{
          return val
      }
      // if (val) {
      //   let newValue="";
      //   let valLength = val.toString().length;
      //   switch (valLength % 3) {
      //     case 0:
      //       newValue=val
      //         .toString()
      //         .split("")
      //         .map((item, index) => {
      //           return index < valLength - 3
      //             ? index % 3
      //               ? "*"
      //               : item
      //             : index == valLength - 1
      //             ? item
      //             : "*";
      //         })
      //         .join("");
      //       break;
      //     case 1:
      //       newValue=val
      //         .toString()
      //         .split("")
      //         .map((item, index) => {
      //           return index < valLength - 1 ? (index % 3 ? "*" : item) : item;
      //         })
      //         .join("");
      //       break;
      //     case 2:
      //       newValue=val
      //         .toString()
      //         .split("")
      //         .map((item, index) => {
      //           return index < valLength - 2
      //             ? index % 3
      //               ? "*"
      //               : item
      //             : index == valLength - 1
      //             ? item
      //             : "*";
      //         })
      //         .join("");
      //       break;
      //     default:
      //       newValue=val;
      //   }
      //   return newValue
      // }else{
      //   return val
      // }
    },
    initData() {
      if(this.hideType=='address'){
        this.eyeHideData = this.encryptAddress(this.value);
      }else if(this.hideType=='name'){
        this.eyeHideData = this.encryptName(this.value);
      }else if(this.hideType=='phoneNumberShort'){
        this.eyeHideData = this.encryptPhoneNumberShort(this.value);
      }else{
        this.eyeHideData = this.encryptPhoneNumber(this.value);
      }
      this.eyeOpenData = this.value;
      if (!this.isOpen) {
        this.eyeData = this.eyeHideData;
      } else {
        this.eyeData = this.eyeOpenData;
      }
    },
  },
  mounted() {
    this.initData();
  },
  watch: {
    value() {
      this.initData();
    },
  },
};
</script>
<style>
.eyeSwitchInput{
    max-width: 180px;
    width: 100%;
}
.eyeSwitchBorderNone .el-input__inner{
  border:0;
  padding-left: 0px;
}
.eyeSwitchHideBackground .el-input__inner{
  background:transparent;
}
</style>
