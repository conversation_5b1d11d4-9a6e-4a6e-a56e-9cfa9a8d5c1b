<!-- el-formitem -->
<template>
<el-form label-position="right" :label-width="label" :rules='rules' :model='data' ref='form'>
	<el-row>
		<el-col v-for='(item,index) in cols' :span='span || colSpan' :key='index' >
			<el-form-item v-for='(row,index) in item' :label="row.label" :key='index' :prop='row.slot' v-if="!row.isShow" :style="{'margin-top':row.className?'100px':'auto'}">
				<template v-if='row.format'>
					<p v-tooltip='ft[row.format](data[row.key], row.formatParams)'>{{ft[row.format](data[row.key], row.formatParams)}}</p>
				</template>
				<template v-else-if='row.formatter'>
					<p v-tooltip='row.formatter(data[row.key])'>{{row.formatter(data[row.key])}}</p>
				</template>
				<template v-else-if='row.redirect'>
					<a href='javascript:;' @click='row.redirect'><span v-tooltip='data[row.key]'>{{data[row.key]}}</span></a>
				</template>
                <template v-else-if="row.html">
                    <div v-html='row.html(data[row.key])'></div>
                </template>
				<slot v-else-if='row.slot' :name='row.slot'></slot>
				<p v-else v-tooltip='data[row.key]'>{{data[row.key]}}</p>
			</el-form-item>
		</el-col>
	</el-row>
</el-form>
</template>
<script>
export default {
	props: {
		cols: {
			type: Array,
			default() {
				return []
			}
		},
		data: {
			type: Object,
			default() {
				return {}
			}
		},
		label: {
			type: String,
			default: '80px'
		},
		span: {
			type: Number
		},
		rules: {
			type: Object,
			default() {
				return {}
			}
		},
	},
	data() {
		let self = this;
		return {
			ft: self.$root.$options.filters
		}
	},
	methods: {
		validate(resolve) {
        	this.$refs.form.validate(valid => {
        		if(valid) {
        			resolve && resolve();
        		}
        	})
		}
	},
	mounted() {
	},
	computed: {
		// 根据数组长度获取每列的span值,最大为8
		colSpan() {
			let span = Math.floor(24 / this.cols.length) || 8;
			return span > 8 ? 8 : span;
		}
	}
}
</script>
