<template>
	<div id='xpt-image' v-if='isShow'>
		<div class="xpt-image__body" :class="ifClose ? 'wh100' : ''" >
			<div class='xpt-image__main'>
				<div class="xpt-image__rotate" @click="rotate"><i class="reloadSingle"></i></div>
				<div class="xpt-image__close" @click="close"><i class="el-icon-close"></i></div>
				<div class='xpt-image__direction' @click='prev'><i class='el-icon-arrow-left'></i></div>
				<div class='xpt-image__box'>
					<img
						v-if="imgList[index]"
						:src='imgList[index].src'
						:style="'transform: rotate(' + imgRotate + 'deg);-webkit-transform:rotate(' + imgRotate + 'deg);-moz-transform:rotate(' + imgRotate + 'deg);-o-transform:rotate(' + imgRotate + 'deg);'"
					/>
					<el-row v-if='imgList[index]' style="color:#fff">
						<el-col :span="9" style="text-align:right;">创建人: {{imgList[index].creatName}}</el-col>
						<el-col :span="6" style="text-align:center;">创建日期: {{imgList[index].date}}</el-col>
						<el-col :span="9" style="text-align:left;white-space: nowrap;">文件名称: <span >{{imgList[index].name||'null'}}</span> </el-col>
					</el-row>
				</div>
				<div class='xpt-image__direction' @click='next'><i class='el-icon-arrow-right'></i></div>
			</div>
			<div class='xpt-image__thumbnail'>
				<ul>
					<li v-for='(img,_index) in imgList' :class='index===_index? checkIsLast() :""'  :key='_index' @click='active(_index)'>
					<img style="width:100%;height:100%;" :src='img.src'/>
					<i v-if="ifClose" class="el-icon-close" @click="cancel(_index)"></i></li>
					<li  class="lastLi" v-if="imgList.length">
						<el-button type="primary" size="medium" @click="zipDownload">下载</el-button>
						<!-- <a :href="imgList[index].src" :download="imgList[index].name">下载</a> -->
					</li>
					<!-- <a :href="imgList[index].src" :download="imgList[index].name" ></a> -->
				</ul>
			</div>
		</div>
	</div>
</template>
<script>
	import Fn from '@common/Fn.js'
	export default {
		data() {
			return {
				index: 0,
				imgList: [],
				isShow:this.show,
				imgRotate: 0,
			}
		},
		props: {
			// 图片数组
			images: {
				type: Array,
				default(){
					return []
				}
			},
			// 是否显示
			show: {
				type: Boolean,
				default: false
			},
			ifUpload:{
				type: Boolean,
				default: true
			},
			ifClose: {
				type: Boolean,
				default: true
			}
		},
		methods: {
			zipDownload() {
				//路径，名称集合
	            var list_path = [this.imgList[this.index]];
				window.open(list_path[0].path||list_path[0].information_content||list_path[0].pictureUrl)

	            // if(!this.host) {
	            //     this.$message.error('获取域名出错，请重新打开');
	            //     return;
	            // }
	            // if(list_path.length === 0){
	            //     this.$message.error('请选择要下载的附件');
	            //     return;
	            // }
	            // //定义下载的名称；
	            // var name = "图片下载";
				// var iframe= document.createElement("iframe");   //定义一个a标签
				// // 获取iframe的host
				// let iframeHost = list_path[0].path.split('//')[0] + '//'+list_path[0].path.split('//')[1].split('/');
	            // iframe.setAttribute('src',iframeHost);   //在form表单中添加查询参数

				// let target = document.createElement("a");
	            // target.setAttribute('style','display:none');   //在form表单中添加查询参数
	            // target.setAttribute('href',list_path[0].path);
				// target.setAttribute('download', list_path[0].name);
				// target.click();
				// target.remove();
	            // form.setAttribute('target','');
	            // form.setAttribute('enctype','application/x-www-form-urlencoded');

	            // var input1 =document.createElement('input');
	            // input1.setAttribute('type','hidden');
	            // input1.setAttribute('name','path');
	            // input1.setAttribute('value',list_path.toString());

	            // document.getElementById('xpt-image').append(form);  //将表单放置在web中
	            // form.append(input1);   //将查询参数控件提交到表单上
	            // form.submit();   //表单提交
	            // form.remove();
	        },
			rotate (){
				this.rotate.ing = true

				this.imgRotate += 90

				this.$nextTick(() => {
					this.rotate.ing = false
				})
			},
			checkIsLast (){
				if(!this.rotate.ing){
					this.imgRotate = 0
					if(this.index === this.imgList.length - 1) {
						this.$message.info('已经是最后一张')
					}
				}

				return 'active'
			},
			close() {
				this.isShow = false;
				this.$emit('close',this.imgList);
			},
			prev() {
				if(this.index>0) {
					this.index--
				} else {
					this.index = this.imgList.length-1;
				}
			},
			next() {
				if(this.index < this.imgList.length -1){
					this.index++
				} else {
					this.index = 0;
				}
			},
			cancel(index){
				var self = this;
				this.$root.eventHandle.$emit('openDialog',{
						txt:'是否确定删除？',
						okTxt:'确定',
						cancelTxt:'取消',
						noShowNoTxt:true,
						ok(){
							var list_cloud_file_id = [];
				            list_cloud_file_id.push(self.imgList[index].cloud_file_id);
				            self.ajax.postStream('/file-iweb/api/cloud/file/delete',{list_cloud_file_id: list_cloud_file_id},res => {
				                if(res.body.result) {
				                    self.$message.success('删除成功');
									self.imgList.splice(index,1);
									//调整当前图片显示
									if(self.imgList.length == index) {
										self.index = index-1;
									}
				                }else{
				                    self.$message.error(res.body.msg);
				                }
				            })
						},
						cancel(){
						}
					})
			},
			active(index) {
				this.index = index
			},
			isPucture(str) {
				str = str.toString();
				 var strFilter=".jpeg|.gif|.jpg|.png|.bmp|.pic|"
		        if(str.indexOf(".")>-1)
		        {
		            var p = str.lastIndexOf(".");

		            var strPostfix=str.substring(p,str.length) + '|';
		            strPostfix = strPostfix.toLowerCase();

		            if(strFilter.indexOf(strPostfix)>-1)
		            {

		                return true;
		            }
		        }

		        return false;
			}
		},
		watch: {
			index(n,o) {
				let el = document.getElementsByClassName('xpt-image__thumbnail');
				el.scrollLeft = (n + 1) * 110 - el.clientWidth + 110;
			},
			'show':function(newVal){
				if(newVal){
					this.isShow=true;
				}

			},
			'images':{
				handler:function(newVal){
					var imgList = [];
					var list = newVal || [];
					list.map((v,i) => {
						if(this.isPucture(v.src)){
							v.isPucture = true;
						}else{
							v.isPucture = false;
						}
						if(v.active){
							this.index = i;
						}
						imgList.push(v);
					})

					this.imgList = imgList || [];

				},
				deep:true
			}


		},
		mounted() {
		}
	}
</script>
<style lang="stylus">
#xpt-image .reloadSingle {
    display: inline-block;
    position: relative;
    zoom: 1.1;
  border-color: transparent #999;
	border-radius: 50%;
	border-style: solid;
	border-width: 0.22em;
	height: 2em;
	margin: .25em;
	width: 2em;
}
#xpt-image .reloadSingle:before, .reloadSingle:after {
    border-style: solid;
    content: '';
    display: block;
    position: absolute;
    width: 0;
    -webkit-transform: rotate(-45deg);
	  transform: rotate(-45deg);
} 
#xpt-image .reloadSingle:before {
    border-color: transparent #999 transparent transparent;
    border-width: 0.4em 0.6em 0.4em 0;
    bottom: -0.3125em;
    right: 0;
}
#xpt-image .reloadSingle:after {
    border-color: transparent transparent transparent #999;
    border-width: 0.4em 0 0.4em 0.6em;
    top: -0.3125em;
    left: 0;
}

#xpt-image
	width: 100%
	height: 100%
	background: rgba(0,0,0,.3)
	position: fixed
	top: 0
	left: 0
	z-index: 8
	.xpt-image__body
		width: 80%
		height: 80%
		background: #000
		position: absolute
		top: 50%
		left: 50%
		transform: translate(-50%, -50%)
		box-shadow: 0 0 5px rgba(0,0,0,.5)
		flex-direction:column
		.xpt-image__main
			position: absolute
			top: 0
			left: 0
			bottom: 100px
			width: 100%
			display: flex
			align-items: center
			.xpt-image__close
				position:absolute
				top:10px
				right:10px
				cursor: pointer
				i
					font-size: 24px !important
					color:#999
					&:hover
						color:#f5f5f5
			.xpt-image__rotate
				position:absolute
				top:5px
				right:40px
				cursor: pointer
				i
					&:hover
					    border-color: transparent #fff;
					    &:before
				    	    border-right-color: #fff;
					    &:after
				    	    border-left-color: #fff;
			.xpt-image__direction
				flex: 1
				height: 80px
				text-align: center
				cursor: pointer
				&:after
					content: ''
					display: inline-block
					vertical-align: middle
					height: 80px
				i
					font-size: 20px !important
					color: #999
					vertical-align: middle
					&:hover
						color:#f5f5f5
			.xpt-image__box
				flex: 8
				padding: 10px 0
				text-align: center
				width:100%
				height:100%
				padding-top: 56px
				img
					max-width:100%
					max-height:100%
					vertical-align:middle
				&:after
					content:''
					height:100%
					display:inline-block
					vertical-align:middle
		.xpt-image__thumbnail
			position: absolute
			bottom: 0
			left: 0
			width: 100%
			height: 100px
			padding:10px 0 10px 10px
			overflow: auto
			ul
				white-space: nowrap
				height:100%
			li
				width: 100px
				height: 80px
				display: inline-block
				margin-right: 10px
				cursor: pointer
				overflow:hidden
				position:relative
				img
					width: 100%
					vertical-align:middle
				&:after
					content: ''
					display: inline-block
					height: 80px
					vertical-align:middle
				&.active
					border: 2px solid #ddde0f
				.el-icon-close
					position:absolute
					top:5px
					right:5px
	.wh100
		width:100%!important
		height:100%!important
</style>
