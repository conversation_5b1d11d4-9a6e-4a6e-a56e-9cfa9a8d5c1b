<!-- 图片的增删改查组件,没有分组之说 -->
<template>
	<div id='xpt-image-v2' v-if='isShow'>
		<div class="xpt-image__body" >
			<div class='xpt-image__main'>
				<div class="xpt-image__rotate" @click="rotate"><i class="reloadSingle"></i></div>
				<div class="xpt-image__close" @click="close"><i class="el-icon-close"></i></div>
				<div class='xpt-image__direction' @click='prev'><i class='el-icon-arrow-left'></i></div>
				<div class='xpt-image__box'>
					<img
						v-if="imgList[index]"
						:src='imgList[index].path'
						:style="'transform: rotate(' + imgRotate + 'deg);-webkit-transform:rotate(' + imgRotate + 'deg);-moz-transform:rotate(' + imgRotate + 'deg);-o-transform:rotate(' + imgRotate + 'deg);'"
					/>
					<el-row v-if='imgList[index]' style="color:#fff">
						<el-col :span="source === 'configur'? 12 : 9" :style="(source === 'configur')? 'text-align:left;' : 'text-align:right;'">创建人: {{imgList[index].creator_nick}}</el-col>
						<el-col :span="source === 'configur'? 12 : 6" :style="(source === 'configur')? 'text-align:left;' : 'text-align:center;'">创建日期: {{imgList[index].create_time|dataFormat('yyyy-MM-dd hh:mm:ss')}}</el-col>
						<el-col :span="source === 'configur'? 12 : 9" style="text-align:left;" v-if="source === 'configur'"><span>图片类型: {{dataObj.functionTypeObj[imgList[index].function_type]}}</span> </el-col>
						<el-col :span="source === 'configur'? 12 : 9" style="text-align:left;">文件名称: <span >{{imgList[index].name}}</span> </el-col>
					</el-row>
				</div>
				<div class='xpt-image__direction' @click='next'><i class='el-icon-arrow-right'></i></div>
			</div>
			<div class='xpt-image__thumbnail'>
				<ul>
					<li v-for='(img,_index) in imgList' :class='index===_index?checkIsLast():""'  :key='_index' @click='active(_index)'>
						<img style="width:100%;height:100%;" :src='img.path'/>
						<i class="el-icon-close" @click="cancel(_index)" v-if="ifUpload"></i>
						<el-checkbox class="el-icon-checkbox" v-model="img._selected"></el-checkbox>
					</li>
					
					<li  class="lastLi" v-if="ifUpload"> 
						<el-button type="primary" size="medium" @click="startUpload" :disabled="(source === 'configur' && dataObj.function_type === 'other') ? true : upLoadBtn">上传附件<xpt-upload-v2  :dataObj="dataObj" :isOnlyPic="true" :source="source" :ifClickUpload="ifClickUpload" @uploadImg="uploadImg" :disabled="(source === 'configur' && dataObj.function_type === 'other') ? true : !ifClickUpload"></xpt-upload-v2></el-button>
					</li>
					<!-- <li  class="lastLi" v-if="imgList.length">
						<el-button type="primary" size="medium" @click="zipDownload">批量下载</el-button>
					</li> -->

				</ul>

			</div>
		</div>
	</div>
</template>
<script>
	
	export default {
		data() {
			return {
				index: 0,
				imgList: [],
				isShow:this.show,
				imgRotate: 0,
				host: '',
				ifClickUpload:false//是否要点击上传按钮
			}
		},
		props: {
			
			//是否要开始向数据库请求数据
			isGetImgList:{
				type: Boolean,
				default: false
			},
			// 是否显示
			show: {
				type: Boolean,
				default: false
			},
			ifUpload:{
				type: Boolean,
				default: false
			},
			dataObj:{
				type:Object,
				default(){
					return {}
				}
			},
			paramsInfo:{
				type:Object,
				default(){
					return {}
				}
			},
			upLoadBtn:{
				type: Boolean,
				default: false
      },
      source:{
				type: String,
				default: ''
      },
		},
		methods: {
			zipDownload() {
	            //路径，名称集合
	            var list_path = [];
	            this.imgList.forEach(v => {
	                if(v._selected) {
                        list_path.push(v.path+'='+v.name);
	                }
	            })

	            if(!this.host) {
	                this.$message.error('获取域名出错，请重新打开');
	                return;
	            }
	            if(list_path.length === 0){
	                this.$message.error('请选择要下载的附件');
	                return;
	            }
	            //定义下载的名称；
	            var name = "批量下载";
	            var form = document.createElement("form");   //定义一个form表单
	            form.setAttribute('style','display:none');   //在form表单中添加查询参数
	            form.setAttribute('method','post');//设置或返回将数据发送到服务器的HTTP方法
	            form.setAttribute('action', this.host+'/file-web/api/file/download/'+name+'.zip');
	            form.setAttribute('target','');
	            form.setAttribute('enctype','application/x-www-form-urlencoded');

	            var input1 =document.createElement('input');
	            input1.setAttribute('type','hidden');
	            input1.setAttribute('name','path');
	            input1.setAttribute('value',list_path.toString());

	            document.getElementById('xpt-image-v2').append(form);  //将表单放置在web中
	            form.append(input1);   //将查询参数控件提交到表单上
	            form.submit();   //表单提交
	            form.remove();
	        },
			rotate (){
				this.rotate.ing = true

				this.imgRotate += 90

				this.$nextTick(() => {
					this.rotate.ing = false
				})
			},
			checkIsLast (){
				if(!this.rotate.ing){
					this.imgRotate = 0
					if(this.index === this.imgList.length - 1) {
						this.$message.info('已经是最后一张')
					}
				}

				return 'active'
			},
			/**
			*获取已经上传的图片
			**/
			getImgList(){
				
				var submitData = {
	                order_no: this.paramsInfo.parent_no,//单据编号 ：	String
	        		sub_order_no: this.paramsInfo.child_no,//子单据编号：	String
	        		ext_data: this.paramsInfo.ext_data,//扩展字段：		String
	        		cloud_file_id: null//流水主键：		Long
	            }
	           	console.log('kasldfjalsdfjasldfkjsldkfj')
	           	var _this = this;
	            this.ajax.postStream('/file-iweb/api/cloud/file/list',submitData,res => {
	            	var data = res.body;
	            	//console.log('data',data);
	            	if(!data.result){
	            		this.$message.error(data.msg);
	            		return;
	            	}
	            	var list = data.content.list;
	            	//判断图片
	            	//var imgList = [];
	             	list.map((v,i) => {
						if(this.isPucture(v.path)){
							v.isPucture = true;
						}else{
							v.isPucture = false;
						}
						if(v.active){
							this.index = i;
						}
						//imgList.push(v);
					});
					this.imgList = list || [];

	            });
                
			},
			/**
			*上传了新的图片，需要更新
			*/
			uploadImg(){
				console.log('看一下事件监听');
				this.getImgList();
				this.ifClickUpload = false;

			},
			//开始上传图片
			startUpload(){
				
				this.ifClickUpload = true;
				setTimeout(() => {
   					this.ifClickUpload = false;
  				},100)
			},
			close() {
				this.isShow = false;
				this.$emit('close',this.imgList);
			},
			prev() {
				if(this.index>0) {
					this.index--
				} else {
					this.index = this.imgList.length-1;
				}
			},
			next() {
				if(this.index < this.imgList.length -1){
					this.index++
				} else {
					this.index = 0;
				}
			},

			cancel(index){
				var self = this;
				self.ifClickUpload = false;
				this.$root.eventHandle.$emit('openDialog',{
						txt:'是否确定删除？',
						okTxt:'确定',
						cancelTxt:'取消',
						noShowNoTxt:true,
						ok(){
							var list_cloud_file_id = [];
				            list_cloud_file_id.push(self.imgList[index].cloud_file_id);
				            self.ajax.postStream('/file-iweb/api/cloud/file/delete',{list_cloud_file_id: list_cloud_file_id},res => {
				                if(res.body.result) {
				                    self.$message.success('删除成功');
									self.imgList.splice(index,1);
									//调整当前图片显示
									if(self.imgList.length == index) {
										self.index = index-1;
									}
				                }else{
				                    self.$message.error(res.body.msg);
				                }
				            })
						},
						cancel(){
						}
					})
			},
			active(index) {
				this.index = index
			},
			isPucture(str) {
				console.log('str',str);
				if(!str) return;
				str = str.toString();
				 var strFilter=".jpeg|.gif|.jpg|.png|.bmp|.pic|"
		        if(str.indexOf(".")>-1){
		        	var p = str.lastIndexOf(".");
					var strPostfix=str.substring(p,str.length) + '|';
		            strPostfix = strPostfix.toLowerCase();

		            if(strFilter.indexOf(strPostfix)>-1) return true;
		           
		        }
		        

		        return false;
			}
		},
		watch: {
			index(n,o) {
				let el = document.getElementsByClassName('xpt-image__thumbnail');
				el.scrollLeft = (n + 1) * 110 - el.clientWidth + 110;
			},
			//立即获取图片
			isGetImgList:function(newVal,old){
				console.log(newVal,'777')
				if(!newVal) return;
				this.getImgList();
			},
			show:function(newVal){
				console.log('show',newVal);
				this.isShow=newVal;

			}

		},
		mounted() {
			//获取域名
	        this.ajax.postStream('/file-iweb/api/cloud/fileConfig/get',{},res => {
	            if(res.body.result){
	                this.host = res.body.content.host;
	            }
	        })
		}
	}
</script>
<style lang="stylus" scoped>
#xpt-image-v2 .reloadSingle {
    display: inline-block;
    position: relative;
    zoom: 1.1;
  border-color: transparent #999;
	border-radius: 50%;
	border-style: solid;
	border-width: 0.22em;
	height: 2em;
	margin: .25em;
	width: 2em;
}
#xpt-image-v2 .reloadSingle:before, .reloadSingle:after {
    border-style: solid;
    content: '';
    display: block;
    position: absolute;
    width: 0;
    -webkit-transform: rotate(-45deg);
	  transform: rotate(-45deg);
} 
#xpt-image-v2 .reloadSingle:before {
    border-color: transparent #999 transparent transparent;
    border-width: 0.4em 0.6em 0.4em 0;
    bottom: -0.3125em;
    right: 0;
}
#xpt-image-v2 .reloadSingle:after {
    border-color: transparent transparent transparent #999;
    border-width: 0.4em 0 0.4em 0.6em;
    top: -0.3125em;
    left: 0;
}
#xpt-image-v2
	width: 100%
	height: 100%
	background: rgba(0,0,0,.3)
	position: fixed
	top: 0
	left: 0
	z-index: 9
	.xpt-image__body
		width: 80%
		height: 80%
		background: #000
		position: absolute
		top: 50%
		left: 50%
		transform: translate(-50%, -50%)
		box-shadow: 0 0 5px rgba(0,0,0,.5)
		flex-direction:column
		.xpt-image__main
			padding: 0px 0 50px
			position: absolute
			top: 0
			left: 0
			bottom: 100px
			width: 100%
			display: flex
			align-items: center
			.xpt-image__close
				position:absolute
				top:10px
				right:10px
				cursor: pointer
				i
					font-size: 24px !important
					color:#999
					&:hover
						color:#f5f5f5
			.xpt-image__rotate
				position:absolute
				top:5px
				right:40px
				cursor: pointer
				i
					&:hover
					    border-color: transparent #fff;
					    &:before
				    	    border-right-color: #fff;
					    &:after
				    	    border-left-color: #fff;
			.xpt-image__direction
				flex: 1
				height: 80px
				text-align: center
				cursor: pointer
				&:after
					content: ''
					display: inline-block
					vertical-align: middle
					height: 80px
				i
					font-size: 20px !important
					color: #999
					vertical-align: middle
					&:hover
						color:#f5f5f5
			.xpt-image__box
				flex: 8
				padding: 10px 0
				text-align: center
				width:100%
				height:100%
				margin-top: 56px
				img
					max-width:100%
					max-height:100%
					vertical-align:middle
				&:after
					content:''
					height:100%
					display:inline-block
					vertical-align:middle
		.xpt-image__thumbnail
			position: absolute
			bottom: 0
			left: 0
			width: 100%
			height: 100px
			padding:10px 0 10px 10px
			overflow: auto
			ul
				white-space: nowrap
				height:100%
			li
				width: 100px
				height: 80px
				display: inline-block
				margin-right: 10px
				cursor: pointer
				overflow:hidden
				position:relative
				img
					width: 100%
					vertical-align:middle
				&:after
					content: ''
					display: inline-block
					height: 80px
					vertical-align:middle
				&.active
					border: 2px solid #ddde0f
				&:.lastLi
					position: fixed
					bottom: 9px
					width: 101px
					height: 82px
					right: 0
					z-index: 2
					background: #000
					margin-right: 0
					text-align: center
				.el-icon-close
					position:absolute
					top:5px
					right:5px
				.el-icon-checkbox
					position:absolute
					left:0
	.xpt-image__upload
		height: 40px
		position: relative
		cursor: pointer
		overflow: hidden
		display: inline-block
		*display: inline
		*zoom: 1
		input
			position: absolute
			font-size: 100px
			right: 0
			top: 0
			height:100%
			opacity: 0
			cursor: pointer
	.wh100
		width:100%!important
		height:100%!important

</style>
