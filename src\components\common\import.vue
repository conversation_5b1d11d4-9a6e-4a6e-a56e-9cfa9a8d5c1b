<!-- 导入excel表 -->
<template >
	<el-button type="primary" size='mini' class='image-upload' :loading="loading" :disabled="isUp">{{text}}<input type="file" :id='inputId'  @change='changeFile' :disabled="isUp" @click="testClick"></el-button>
</template>
<script>
	import Fn from '@common/Fn.js'
	export default {
		data() {
			return {
				url:'',
				isUp:false,
				loading:false,
			}
		},
		props: {
			sysTradeId: {
				type: Number,
				default(){
					return null;
				}
			},
			//是否上传
			isupload: {
				type: Boolean,
				default(){
					return false;
				}
			},
			//是否get请求
			isGet: {
				type: Boolean,
				default(){
					return false;
				}
			},
			text: {
				type: String,
				default(){
					return '导入';
				}
			},
			inputId:{
				type:String,
				default(){
					//return 'fillInputId'
					return Fn.guid('evaluate');
				}
			},
			taskUrl:{
				type:String,
				default(){
					return '';
				}
			},
			otherParams:Object,
			callback:Function,//上传成功之后的回调函数
		},
		methods: {
			/**
			*初使化数据
			*/
			initData(){
				this.isUp = this.isupload;

			},
			testClick(){
				//

			},
			changeFile(){
				console.log(88888);
				this.loading = this.isUp = true;

				var id = this.inputId;
				var elInput = document.getElementById(id);
				var files = elInput.files,
	    		len = files.length,
				fr = new FileReader();
				/*this.isUp = len?true:false;*/
				if(!len) {
					this.resetData();
					return;
				}

		      	fr.readAsDataURL(files[0])
			    fr.onload = e => {
					this.upload(files,0)
			     }
			     //elInput.value='';

			},
			/**
			*清空input里面的数据
			**/
			resetData(){
				//return;
				var id = this.inputId;
				var elInput = document.getElementById(id);
				elInput.value = '';
				this.loading = this.isUp = false;
			},

			/**
			*第一步提交至服务器
			*/
			upload(files,index) {
				var formData = new FormData()
		      	formData.append('file', files[index]);
		      	var _this = this;
		      	console.log('看看导入的东西');
		      	this.ajax.post('/app-web/app/file/uploadFile.do', formData, (s) => {
		      		if(s.ok && s.body && s.body.data){
						_this.url = s.body.data;
						_this.expIn();
			        }else{
			        	_this.$message.error('上传失败!');
			        	_this.resetData();
			        }
		      	}, e => {
					_this.$message({message: e.statusText,type: 'error'});
					_this.resetData();
		      	})
		    },
		    /**
		    *进行导入操作
		    */
		    expIn(){
		      var _this = this;
		      if(!this.taskUrl) {
		      	_this.resetData();
		      	return false;
		      }
			  //导入的任务不一样，url也不一样，需要设定
			  let params = null
		      let paramas = this.isEmptyObject(this.otherParams)?this.url:JSON.parse(JSON.stringify(this.otherParams));//导入的excel是否有归属
          if(!this.isEmptyObject(this.otherParams) && this.otherParams.argumentIsPathInsteadOfUrl) {
            paramas.path = this.url;
            delete paramas.argumentIsPathInsteadOfUrl
          } else {
            typeof paramas == 'string'?'':paramas.url= this.url;
          }
			  if (this.sysTradeId) {
				    params = {
						path: paramas,
						sys_trade_id: this.sysTradeId
					}
			  } else if (this.otherParams && (this.otherParams.ifDuopai || this.otherParams.ifAppointment)) {
				  params = {
					fileUrl: this.url,
				}
				this.otherParams.ifDuopai ? params.if_valid_inventory = 'Y' : ''
			  }else if (this.otherParams && this.otherParams.upgradePurchase) {
				  params = {
					path: this.url,
				}
				 params.changeSaleId = this.otherParams.changeSaleId
			  } else if (this.otherParams && this.otherParams.multiPlatformShipmentList) {
				  params = {
					fileUrl: this.url,
				}
			  } else if (this.otherParams &&this.otherParams.ifCusMartiel) { // 客户物料导入
				  params = {
					  path: this.url,
					  id: this.otherParams.id
				  }
			  } else if (this.otherParams &&this.otherParams.forinObj) { // 价格计算版本导入在售商品
					params = {};
					let Obj = this.otherParams;
					for(var key in Obj){
						if(key == 'forinObj') {continue}
						else if(Obj[key] == 'thisUrl'){
							params[key] = this.url;
						}else{
							params[key] = Obj[key];
						}
					}
			  }else if (this.otherParams &&this.otherParams.ifPackage) { //物料包件明细报表
				   params = {
					  fileURL: this.url,
					  importType: this.otherParams.importType
				  }
			  } else if (this.otherParams &&this.otherParams.ifPredictPrice) { //预估到手价目详情
				   params = {
					  path: this.url,
					  predict_price_list_id: this.otherParams.predict_price_list_id
				  }
			  } else if (this.otherParams &&this.otherParams.isSwj) { //三维家列表
				   params = {
					  path: this.url
				  }
			  } else if (this.otherParams && this.otherParams.discount_id) { //搭配购活动详情（优惠清单、实施店铺）
                    params = {
                        discount_id: this.otherParams.discount_id,
                        path: this.url
                    }
              }else if (this.otherParams && this.otherParams.discount_no) { //搭配购活动详情（优惠清单、实施店铺）
                    params = {
                        discount_no: this.otherParams.discount_no,
                        path: this.url
                    }
              } else if(this.otherParams && this.otherParams.releaseId){ //价目发布记录表详情
                    params = {
                        releaseId: this.otherParams.releaseId,
                        path: this.url
                    }
              } else if(this.otherParams && this.otherParams.purchase_id){ //价目发布记录表详情
                    params = {
                        purchase_id: this.otherParams.purchase_id,
                        patch: this.url
                    }
              } else {
				  params = paramas
			  }

			  if(!this.isGet){
				/*this.ajax.postStream1(this.taskUrl,_this.url,d=>{*/
					this.ajax.postStream1(this.taskUrl,params,d=>{
					// console.log('d',d);
					_this.$emit('reloadList',{url : _this.url});//子组件向父组件派发

					_this.$message({
						message:d.body.msg||'上传成功',
						type:d.body.result?'success':'error'
					});
					_this.resetData();
					_this.callback && _this.callback(d);

				})
			  }else{
				  this.ajax.get(this.taskUrl+"?path="+this.url,(d)=>{
					  _this.$emit('reloadList',{url : _this.url});//子组件向父组件派发

						// _this.$message({
						// 	message:d.body.msg||'上传成功',
						// 	type:d.body.result?'success':'error'
						// });
						_this.resetData();
						_this.callback && _this.callback(d);
				  })
			  }


		    }



		},

		watch:{
			isupload:function(newVal,oldVal){
				this.isUp = newVal;
				this.loading = false;
			}
		},

		mounted() {
			this.initData();
		}
	}
</script>
<style lang="stylus" scoped>

.image-upload
		position: relative
		cursor: pointer
		overflow: hidden
		display: inline-block
		*display: inline
		*zoom: 1
		input
			position: absolute
			font-size: 100px
			right: 0
			top: 0
			height:100%
			opacity: 0
			cursor: pointer
</style>
