<!-- 导入excel表 -->
<template >
	<el-button type="primary" size='mini' class='image-upload' :loading="loading" :disabled="isUp">{{text}}<input type="file" :id='inputId'  @change='changeFile' :disabled="isUp" @click="testClick"></el-button>
</template>
<script>
	/*import Fn from '@common/Fn.js'
	import HelpFile  from '@common/helpFile.js'*/
	export default {
		data() {
			return {
				url:'',
				isUp:false,
				loading:false,
			}
		},
		props: {
			//是否上传
			isupload: {
				type: Boolean,
				default(){
					return false;
				}
			},
			text: {
				type: String,
				default(){
					return '导入';
				}
			},
			inputId:{
				type:String,
				default(){
					//return 'fillInputId'
					return new Date().getTime();
				}
			},
			taskUrl:{
				type:String,
				default(){
					return '';
				}
			},
			otherParams:Object,
			callback:Function,//上传成功之后的回调函数
		},
		methods: {
			/**
			*初使化数据
			*/
			initData(){
				this.isUp = this.isupload;
				
			},
			testClick(){
				//

			},
			changeFile(){
				console.log(88888);
				this.loading = this.isUp = true;
				
				var id = this.inputId;
				var elInput = document.getElementById(id);
				var files = elInput.files,
	    		len = files.length,
				fr = new FileReader();
				/*this.isUp = len?true:false;*/
				if(!len) {
					this.resetData();
					return;
				}
			  	
		      	fr.readAsDataURL(files[0])
			    fr.onload = e => {
					this.upload(files,0)
			     }
			     //elInput.value='';

			},
			/**
			*清空input里面的数据
			**/
			resetData(){
				//return;
				var id = this.inputId;
				var elInput = document.getElementById(id);
				elInput.value = '';
				this.loading = this.isUp = false;
			},

			/**
			*第一步提交至服务器
			*/
			upload(files,index) {
				var formData = new FormData()
		      	formData.append('file', files[index]);
		      	var _this = this;
		      	console.log('看看导入的东西');
		      	this.ajax.post('/app-web/app/file/uploadFile.do', formData, (s) => {
		      		if(s.ok && s.body && s.body.data){
						_this.url = s.body.data;
						_this.expIn();
			        }else{
			        	_this.$message.error('上传失败!');
			        	_this.resetData();
			        } 
		      	}, e => {
					_this.$message({message: e.statusText,type: 'error'});
					_this.resetData();
		      	})
		    },
		    /**
		    *进行导入操作
		    */
		    expIn(){
		      var _this = this;
		      if(!this.taskUrl) {
		      	_this.resetData();
		      	return false;
		      }
		      //导入的任务不一样，url也不一样，需要设定
		      let paramas = this.isEmptyObject(this.otherParams)?this.url:JSON.parse(JSON.stringify(this.otherParams));//导入的excel是否有归属
		      typeof paramas == 'string'?'':paramas.url= this.url;

		      /*this.ajax.postStream1(this.taskUrl,_this.url,d=>{*/
		      	this.ajax.postStream1(this.taskUrl,paramas,d=>{
		      	console.log('d',d);
		      	_this.$emit('reloadList',{url : _this.url});//子组件向父组件派发
		      	
		      	_this.$message({
                    message:d.body.msg,
                    type:d.body.result?'success':'error'
                });
                _this.resetData();
                _this.callback && _this.callback();
		      	
		      })
		    }

		    
		    
		},
		
		watch:{
			isupload:function(newVal,oldVal){
				this.isUp = newVal;
				this.loading = false;
			}
		},

		mounted() {
			this.initData();
		}
	}
</script>
<style lang="stylus" scoped>

.image-upload
		position: relative
		cursor: pointer
		overflow: hidden
		display: inline-block
		*display: inline
		*zoom: 1
		input
			position: absolute
			font-size: 100px
			right: 0
			top: 0
			height:100%
			opacity: 0 
			cursor: pointer
</style>
