<!-- 内部便签 -->
<template>
	<xpt-list
		ref='inner'
		:data='innerList'
		:btns='innerBtns'
		:colData='innerCols'
		:radioDisabled='radioDisabled'
		selection='radio'
		@radio-change='innerRadioChange'
    class="inner-list"
	>
		<template slot='content' slot-scope='scope'>
      <div v-if="richTextRegex.test(scope.row.content)" class="richtext-box">
        <div v-html="scope.row.content"  :class="$style['richtext-content']"></div>
        <el-button type="primary" size="mini" class="richtext-button" @click="viewRichText(scope.row.content)">查看</el-button>
      </div>
			<el-input  v-else v-model="scope.row.content" size='mini' style="width:100%;vertical-align: middle;" type="textarea" :disabled="!scope.row.isEdit"></el-input>
		</template>
	</xpt-list>
</template>
<script>
import fn from '@common/Fn.js'
export default {
	data() {
		let self = this;
		return {
      richTextRegex:/<[^>]+>/,
			innerList: [],
			innerBtns: [
				{
					type: 'primary',
					txt: '新增',
					loading: false,
					disabled: true,
					click: self.innerAdd,
				},
        {
					type: 'warning',
					txt: '富文本新增',
					loading: false,
					disabled: true,
					click: self.richTextAdd,
				}
			],
			innerCols: [
				{
					label: '合并单号',
					prop: 'merge_trade_no',
					width: '170'
				}, {
					label: '用户',
					prop: 'creator_name',
					width: '150'
				}, {
					label: '创建时间',
					prop: 'create_time_str',
					format: 'dataFormat1',
					width: '150'
				}, {
					label: '内容',
          prop: 'content',
          slot: 'content',
					// bool: true,
					// isTextarea: true,
					// autosize: true,
					// disabled(row) {
					// 	return !row.isEdit
					// },
				}
			],
			innerSelect: '',
			radioDisabled: self.radioDisabled1,
			merge_trade_id: '',
		}
	},
	methods: {
		innerRadioChange(row) {
			this.innerSelect = row;
		},
		innerAdd() {
			let isAdd = (this.innerBtns[0].txt === '新增');
			if(isAdd) {
				// 添加新增行
				let userInfo = fn.getUserInfo();
				let data = {
					merge_trade_no: '',
					creator_name: userInfo.realName,
					group_name: '',
					modify_time_str: +new Date(),
					content: '',
					isEdit: true,
					tempId: +new Date()
				}
				this.innerList.unshift(data);
				this.innerSelect = data;
				this.$refs.inner.setRadioSelect(data);
				this.innerBtns[0].txt = '保存'
			} else {
				console.log(this.innerSelect)
				if(!this.innerSelect.content) {
					this.$message.error('便签内容不能为空');
					return
				}
				// 保存新增行
				this.innerSave({
					content: this.innerSelect.content,
					//  合并订单
					merge_trade_id: this.merge_trade_id
				})
				this.innerSelect = ''
				this.innerBtns[0].txt = '新增'
			}
		},
		// 保存便签，markId值不为空时为编辑，为空时为新增
		innerSave(data) {
			if(data.remark_id) {
				this.innerBtns[1].loading = true;
			} else {
				this.innerBtns[0].loading = true;
			}
			// 请示数据url
			this.ajax.postStream('/order-web/api/mergetrade/remark/insertRemark', data, res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				if(res.body.result) {
					// 重新获取内部便签列表
					this.getInnerList();
				}
				this.innerBtns[0].loading = false;
			}, err => {
				this.$message.error(err);
				this.innerBtns[0].loading = false;
			})
		},
		initSuccess(res) {
			if(res.body.result) {
				let list = res.body.content || [],
					i = list.length;
				while(i--) {
					list[i].isEdit = false;
				}
				this.innerList = list;

				// 加载过一次便签后才可以新增，防止新增了一行，然后数据加载后新增的行丢失的问题。
				if(this.innerBtns && this.innerBtns[0]) {
					this.innerBtns[0].disabled = false;
				}
				if(this.innerBtns && this.innerBtns[1]) {
					this.innerBtns[1].disabled = false;
				}
			}
			this.innerSelect = '';
			this.$refs.inner.clearRadioSelect();
		},
		getInnerList(id) {
			if (this.innerBtns) {
				this.innerBtns[0].txt = '新增';
			}
			if(id) {
				this.merge_trade_id = id;
			}
			if(!this.merge_trade_id) return;
			this.ajax.postStream("/order-web/api/mergetrade/remark/list", this.merge_trade_id, this.initSuccess);
		},
		// 当所选行为编辑时，在未保存前控制其它不可选。
		radioDisabled1() {
			return false;
		},
		radioDisabled2(row) {
			if(row.remark_id) {
				return row.remark_id != this.innerSelect.remark_id;
			} else {
				return row.tempId && row.tempId != this.innerSelect.tempId;
			}
		},
    richTextAdd(){
      const callback=(data)=>{
        this.innerSave({
            content: data,
            //  合并订单
            merge_trade_id: this.merge_trade_id
        })
      }
      const params={
        callback,
      }
      this.$root.eventHandle.$emit('alert', {
            params: params,
            component: () => import('@components/common/wangEditorDialog.vue'),
            style: 'width:960px;height:400px',
            title: '内部便签'
          });
    },
    viewRichText(content){
      const params={
        content,
      }
      this.$root.eventHandle.$emit('alert', {
            params: params,
            component: () => import('@components/common/wangEditorDialog.vue'),
            style: 'width:960px;height:600px',
            title: '内部便签'
          });
    },
	},
	computed: {
		isEdit() {
			return this.innerSelect.isEdit;
		}
	},
	watch: {
		isEdit(n) {
			if(n) {
				this.radioDisabled = this.radioDisabled2;
			} else {
				this.radioDisabled = this.radioDisabled1
			}
		}
	}
}
</script>
<style lang="stylus" scoped>
  .inner-list  /deep/ {
    .el-table .el-table__body-wrapper td .cell{
      line-height:auto;
      height: auto;
    }
  }
  .richtext-box{
    position:relative;
    .richtext-button{
     position:absolute;
     right: 0;
     top: 20px;
    }
  }

</style>
<style module>
.richtext-content{
  height: 58px;
}
.richtext-content :global(img) {
  max-width: 200px !important;
}
</style>
