<!-- 控制是否自动上传excel表 -->
<template >
  <el-button
    type="primary"
    size="mini"
    class="image-upload"
    :loading="loading"
    :disabled="isUp"
    >{{ text
    }}<input
      type="file"
      :id="inputId"
      @change="changeFile"
      :disabled="isUp"
      @click="testClick"
  /></el-button>
</template>
<script>
export default {
  data() {
    return {
      url: "",
      isUp: false,
      loading: false,
      isExpIn: false,
    };
  },
  props: {
    sysTradeId: {
      type: Number,
      default() {
        return null;
      },
    },
    isupload: {
      type: Boolean,
      default() {
        return false;
      },
    },
    isGet: {
      type: Boolean,
      default() {
        return false;
      },
    },
    text: {
      type: String,
      default() {
        return "导入";
      },
    },
    inputId: {
      type: String,
      default() {
        return String(new Date().getTime());
      },
    },
    taskUrl: {
      type: String,
      default() {
        return "";
      },
    },
    callbackText: Function,
    otherParams: Object,
    callback: Function,
  },
  methods: {
    /**
     *初使化数据
     */
    initData() {
      this.isUp = this.isupload;
    },
    testClick() {},
    changeFile() {
      this.loading = this.isUp = true;
      var id = this.inputId;
      var elInput = document.getElementById(id);
      var files = elInput.files,
        len = files.length,
        fr = new FileReader();
      if (!len) {
        this.resetData();
        return;
      }

      fr.readAsDataURL(files[0]);
      fr.onload = (e) => {
        this.upload(files, 0);
      };
    },

    resetData() {
      var id = this.inputId;
      var elInput = document.getElementById(id);
      elInput.value = "";
      this.loading = this.isUp = false;
      this.isExpIn = false;
    },

    upload(files, index) {
      var formData = new FormData();
      formData.append("file", files[index]);
      var _this = this;
      this.ajax.post(
        "/app-web/app/file/uploadFile.do",
        formData,
        (s) => {
          if (s.ok && s.body && s.body.data) {
            _this.url = s.body.data;
            if (_this.isupload) {
              _this.expIn();
            } else {
              _this.callbackText();
              _this.loading = _this.isUp = false;
            }
          } else {
            _this.$message.error("上传失败!");
            _this.resetData();
          }
        },
        (e) => {
          _this.$message({ message: e.statusText, type: "error" });
          _this.resetData();
        }
      );
    },
    /**
     *进行导入操作
     */
    expIn() {
      var _this = this;
      if (this.isExpIn) return;
      if (!this.taskUrl) {
        _this.resetData();
        return false;
      }
      if (!this.url) {
        this.$message.warning("请先选择文件！");
        return;
      }
      //导入的任务不一样，url也不一样，需要设定
      let params = null;
      let paramas = this.isEmptyObject(this.otherParams)
        ? this.url
        : JSON.parse(JSON.stringify(this.otherParams));
      typeof paramas == "string" ? "" : (paramas.url = this.url);

      if (this.otherParams && this.otherParams.ifAppointment) {
        params = {
          fileUrl: this.url,
        };
      }else if(this.otherParams && this.otherParams.ifAppointmentActivity){
        params = {
          fileUrl: this.url,
          importMode: this.otherParams.importMode,
          appointmentActivityId: this.otherParams.appointmentActivityId
        };
      }
      this.isExpIn = true;
      if (!this.isGet) {
        this.ajax.postStream1(this.taskUrl, params, (d) => {
          _this.$emit("reloadList", { url: _this.url });
          _this.$message({
            message: d.body.msg || "上传成功",
            type: d.body.result ? "success" : "error",
          });
          _this.resetData();
          _this.callback && _this.callback(d);
        });
      } else {
        this.ajax.get(this.taskUrl + "?path=" + this.url, (d) => {
          _this.$emit("reloadList", { url: _this.url });
          _this.resetData();
          _this.callback && _this.callback(d);
        });
      }
    },
  },

  watch: {
    isupload: function (newVal, oldVal) {
      this.isUp = newVal;
      this.loading = false;
    },
  },

  mounted() {
    this.initData();
  },
};
</script>
<style lang="stylus" scoped>
.image-upload {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  display: inline-block;
  *display: inline;
  *zoom: 1;

  input {
    position: absolute;
    font-size: 100px;
    right: 0;
    top: 0;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
}
</style>
