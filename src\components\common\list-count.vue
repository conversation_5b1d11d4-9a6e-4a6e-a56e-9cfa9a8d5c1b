<template>
    <div class="cout-list" >
        <xpt-list ref="list" v-if="!dynamic" v-bind="$props"
                  :colData="colData"
                  :showSelectRowNumByOnlyKey='showSelectRowNumByOnlyKey'
                  @search-click="searchClick"
                  @radio-change="eventObj['radio-change']"
                  @selection-change="eventObj['selection-change']"
                  @page-size-change="eventObj['page-size-change']"
                  @current-page-change="eventObj['current-page-change']"
                  @sort-change="eventObj['sort-change']"
                  @row-dblclick="eventObj['row-dblclick']"
                  @row-click="eventObj['row-click']"
                  @cell-click="eventObj['cell-click']"
                  @count-off="countOff"
                  :showCount ='showCount'
                  :taggelClassName="taggelClassName"
                  :row-key="rowKey"
        >
          <template slot="btns">
            <slot name="btns"></slot>
          </template>
        </xpt-list>
        <xpt-list-dynamic ref="list" v-else v-bind="$props"
                  :colData="colData"
                  @search-click="searchClick"
                  @radio-change="eventObj['radio-change']"
                  @selection-change="eventObj['selection-change']"
                  @page-size-change="eventObj['page-size-change']"
                  @current-page-change="eventObj['current-page-change']"
                  @sort-change="eventObj['sort-change']"
                  @row-dblclick="eventObj['row-dblclick']"
                  @row-click="eventObj['row-click']"
                  @cell-click="eventObj['cell-click']"
                  @count-off="countOff"
                  :row-key="rowKey"
        >
        <template slot="btns">
            <slot name="btns"></slot>
          </template>
        </xpt-list-dynamic>
    </div>

</template>

<script>

  import list from '@components/common/list'
  const eventType = ['radio-change', 'selection-change', 'page-size-change', 'current-page-change', 'sort-change', 'row-dblclick', 'row-click', 'cell-click',"count-off"]
  export default {
    name: "list-count",
    props: {
      ...list.props,
      dynamic: Boolean,
      countUrl: String,
      extraCountUrlParams:{},
      showSelectRowNumByOnlyKey:Boolean,
      rowKey: {
        type: String,
      },
    },
    data() {
      return {
			  countOffFlag:false,
        eventObj: {},
      }
    },
    created() {
      // 事件对象，用于把list组件的事件抛给调用者
      const self = this
      eventType.forEach(type => {
        this.eventObj[type] = function() {
          self.$emit(type, ...arguments)
        }
      })
    },
    mounted() {

      this.init()
    },
    methods: {
      init() {

      },
      countOff(callback){
			// console.log(this.$parent);
			let self = this,
			url = this.countUrl;
      let search = self.$parent.search;
			if(!self.data.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;
      search = {...this.extraCountUrlParams,...search}
			self.ajax.postStream(url,search,function(response){
					if(response.body.result){
						// var totalObj =  {
						// 	amount:'合计: '+response.body.content.amount.extendsOne,
						// 	merge_trade_id:'合计'

						// };
						// self.list = self.list.concat(totalObj);
            if(response.body.content.count){
						  self.$parent.count = response.body.content.count;
            }else{
              self.$parent.count = response.body.content;
            }
						self.$parent.showCount = true;
						self.countOffFlag = false;
            self.$parent.countCallback && self.$parent.countCallback(response.body.content)
            // console.log(self.$parent);

					}else{
						self.countOffFlag = false;
						self.$message.error(response.body.msg);
					}
				});
		},

      searchClick(data, callback) {
        let self = this;

        this.$emit('search-click', data, callback)
        // new Promise((res,rej)=>{
        //   this.$emit('search-click', data, callback,res)

        // }).then(()=>{
        //   if(self.search.page_no != 1){
        //     self.count = 0;
        //   }
        //   self.showCount = false;
        // })
      }
    },
  }
</script>
<style scoped>
  /* .list-dynamic {
    margin: 0 -10px;
    min-width: 1200px;
  } */
  .xpt-main .cout-list {
    padding: 8px 8px 0px 8px;
    height: calc(100vh - 120px);
  }
  .alert-body_main .cout-list {
    padding: 8px 8px 0px 8px;
    height: 100%;
  }
  /* /deep/.el-table__footer-wrapper {
    width: 100%;
  } */
</style>
