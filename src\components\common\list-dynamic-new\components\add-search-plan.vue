<template>
  <div id="searchplan" class="search-plan">
    <el-dialog
      v-if="dialogVisible"
      custom-class="dialog-center"
      size="small"
      :show-close="false"
      title="查询方案设置"
      :visible.sync="dialogVisible">
      <div class="content">
        <div style="margin-bottom: 5px;">
          <el-input v-model.trim="form.planName" placeholder="请输入方案名称" :maxlength="20" size="small" style="width: 100%"/>
        </div>
        <div class="left">
          <search-items v-model="form.data" :first-item-show-default="true" :searchPage="searchPage"></search-items>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-checkbox class="pull-left" v-model="form.isDefault">默认方案</el-checkbox>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import fn from '@common/Fn.js'
  import searchItems from './search-items'
  import moment from 'moment'

  export default {
    name: "add-search-plan",
    components: {
      searchItems
    },
    props: {
      searchPage: String
    },
    data() {
      return {
        dialogVisible: false,
        oldData: {},
        form: {
          planName: '',
          isDefault: false,
          data: []
        }
      }
    },
    methods: {
      show(data = {}) {
        if (data.id) {
          this.oldData = data
          this.form = {
            ...this.oldData.query_json,
            isDefault: this.oldData.id_default
          }
        } else {
          this.oldData = {};
          this.form = {
            planName: '',
            isDefault: false,
            data: Array.isArray(data) ? data : []
          }
        }
        this.dialogVisible = true
      },
      confirm() {
        if (!this.form.planName) {
          this.$message.error('请输入方案名称')
          return
        }
        const allSearchItem = this.form.data.filter(item => item.field)
        let isHasNotValue = false;
        const disabledKey = ['IS NULL', 'IS NOT NULL', 'THIS MONTH', 'THIS WEEK', 'TODAY', 'LAST MONTH', 'LAST WEEK', 'YESTERDAY']
        allSearchItem.forEach(item => {
          if(!item.value && disabledKey.indexOf(item.operator) < 0) {
            this.$message.error(`查询条件【${item.label}】值不能为空`);
            isHasNotValue = true
          }
          // 如果值为日期数据，转成字符串
          if(item.value && Object.prototype.toString.call(item.value) === '[object Date]') {
            item.value = moment(item.value).format('YYYY-MM-DD HH:mm:ss')
          }
        })
        if(isHasNotValue) {
          return;
        }
        if (allSearchItem.length < 1) {
          this.$message.error('请选择查询方案内容')
          return
        }
        const data = {
          id: this.oldData.id,
          user_id: fn.getUserInfo('id'),
          page_code: this.searchPage,
          // 定义显示方案的内容。后端不解析，前端给一份json
          query_json: JSON.stringify(
            {
              planName: this.form.planName,
              data: allSearchItem
            }
          ),
          type: 'find',
          id_default: !!this.form.isDefault
        }

        this.ajax.postStream('/user-web/api/new/saveOrUpdateQueryPlan', data, res => {
          if (!res.body.result) {
            this.$message.error(res.body.msg)
            return
          }
          this.$emit('save', res.body.content)
          this.$message.success('保存成功！')
          this.dialogVisible = false
        }, err => {
          this.$message.error(err);
        });
      }
    }
  }
</script>

<style scoped>

</style>
