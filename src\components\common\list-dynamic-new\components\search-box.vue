<template>
  <div class="search-box">
    <search-item v-model="searchData" ref="searchItem" @search="search()" :isShowDefaultField="true" :searchPage="searchPage" @click.native.stop=""/>
    <i @click.stop="switchItems" :class="[itemsToggle ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>&nbsp;
    <!--  这里添加的点击事件其实没有意义，只是单纯的使用@click.stop-->
    <div class="search-items" v-if="itemsToggle" @click.stop="">
      <searchItems v-model="searchItemsData" @search="search()" ref="items" :first-item-condition="true" :searchPage="searchPage" />
    </div>
    <el-button size="mini" type="primary" class="v-middle" :loading='loading' @click="search()">查询</el-button>

    <el-button size="mini" type="primary" class="v-middle" @click="resetSearchData">重置</el-button>

    <i class="mgl5 el-icon-menu v-middle cursor-pointer" title="显示方案" @click.stop="switchPlan"></i>
    <!-- 显示/查询方案列表   -->
    <div id="planList" @click.stop="" class="plan-list" v-if="planToggle">
      <el-collapse accordion v-model="tabing">
        <el-collapse-item name="1">
          <template slot="title">
            查询方案
            <i @click.stop="addSearchPlan()" title="新建方案" class="header-icon el-icon-plus h-right a"></i>
            <i @click.stop="updateSearchPlan" title="更新当前查询方案" class="header-icon el-icon-star-on h-right a"></i>
            <i @click.stop="addParamsToSearchPlan" title="保存当前查询条件" class="header-icon el-icon-star-off h-right a"></i>
          </template>
          <div class="list-box">
            <ul class="show-panel-list">
              <li v-if="searchPlanList.length < 1"><span style="color: #cccccc">暂无数据</span></li>
              <li v-for="item of searchPlanList" :key="item.id" :class="{'is-default': item.id_default, 'action': searchPlanIng.id === item.id}">
                <span class="left a" @click="setSearchData(item)" :title="item.query_json.planName">
                  {{item.query_json.planName}}
                </span>
                  <span class="right">
                  <i class="el-icon-edit a" @click="addSearchPlan(item)"></i>
                  <i class="el-icon-delete a color-red" @click="deletePlan(item.id, 'find', item.query_json.planName)"></i>
                </span>
              </li>
            </ul>
          </div>

        </el-collapse-item>
        <el-collapse-item title="显示方案" name="2">
          <template slot="title">
            显示方案 <i @click.stop="addShowPlan()" title="新建方案" class="header-icon el-icon-plus h-right a"></i>
          </template>
          <div class="list-box">
            <ul class="show-panel-list">
              <li v-if="showPlanList.length < 1"><span style="color: #cccccc">暂无数据</span></li>
              <li v-for="item of showPlanList" :key="item.id" :class="{'is-default': item.id_default, 'action': showPlanIng.id === item.id}">
                <span class="left a" @click="matchColConfig(item)" :title="item.query_json.planName">
                  {{item.query_json.planName}}
                </span>
                <span class="right" v-if="item.id !== -1">
                  <i class="el-icon-edit a" @click="addShowPlan(item)"></i>
                  <i class="el-icon-delete a color-red" @click="deletePlan(item.id, 'show', item.query_json.planName)"></i>
                </span>
              </li>
            </ul>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!-- 显示方案设置组件   -->
    <show-plan ref="showPlan" :searchPage="searchPage" @save="showPlanSaveHandel"/>
    <search-plan ref="searchPlan" :searchPage="searchPage" @save="searchPlanSaveHandel" />
  </div>
</template>

<script>
  import _ from 'lodash';
  import searchItem from './search-item'
  import searchItems from './search-items'
  import showPlan from './show-plan'
  import searchPlan from './add-search-plan'
  import fn from '@common/Fn.js'
  import moment from 'moment'
  export default {
    name: "search-box",
    components: {
      searchItem,
      searchItems,
      showPlan,
      searchPlan
    },
    props: {
      searchPage: String
    },
    data() {
      return {
        loading: false,
        searchData: {
          condition: "OR",
          field: "",
          operator: "",
          label: "",
          table: "",
          value: ""
        }, // 查询数据
        searchItemsData: [], // 查询数据
        planToggle: false, // 方案列表开关
        itemsToggle: false, // 多条件查询框开关
        tabing: '1', // 方案折叠框数据
        showPlanList: [], // 显示方案列表
        searchPlanList: [], // 查询方案列表
        searchPlanIng: {}, // 当前使用中的查询方案
        showPlanIng: {id: -1}, // 当前使用中的显示方案, 默认是系统默认方案，如修改了会在查询时修改
        isInit: true, // 设置系统默认查询方案阀值
        isInitShow: true, // 记录系统默认显示方案阀值
      }
    },
    mounted() {
      // window.addEventListener('click', this.closePlan)
      // window.addEventListener('click', this.colseItems)
      this.getPlanList('find')
      this.getPlanList('show')
    },
    methods: {
      search(data) {
        this.colseItems();
        let allSearchItem = data || [this.searchData, ...this.searchItemsData].filter(item => item.field);
        allSearchItem = _.cloneDeep(allSearchItem);
        let isHasNotValue = false;
        const disabledKey = ['IS NULL', 'IS NOT NULL', 'THIS MONTH', 'THIS WEEK', 'TODAY', 'LAST MONTH', 'LAST WEEK', 'YESTERDAY']
        allSearchItem.forEach(item => {
          if(!item.value && disabledKey.indexOf(item.operator) < 0) {
            this.$message.error(`查询条件【${item.label}】值不能为空`);
            isHasNotValue = true
          }
          // 如果值为日期数据，转成字符串
          if(item.value && Object.prototype.toString.call(item.value) === '[object Date]') {
            item.value = moment(item.value).format('YYYY-MM-DD HH:mm:ss')
          }
        })
        if(isHasNotValue) {
          return
        }
        allSearchItem.forEach(item => {
          delete item.label;
        })
        this.loading = true;
        this.$emit('searchClick', allSearchItem, () => {this.loading = false})
      },
      resetSearchData() {
        this.searchData = {
          condition: "OR",
          field: "",
          operator: "",
          table: "",
          label: "",
          value: ""
        }
        this.searchItemsData = []
        this.itemsToggle = false;
        this.searchPlanIng = {};
      },
      setSearchData(item) {
        this.closePlan()
        this.searchPlanIng = JSON.parse(JSON.stringify(item));
        const data = this.searchPlanIng.query_json.data
        this.searchData = data[0]
        this.searchItemsData = data.splice(1)
      },
      // 打开方案选择框
      switchPlan() {
        this.planToggle = !this.planToggle
        this.colseItems()
      },
      // 关闭方案选择框
      closePlan() {
        this.planToggle = false
      },
      // 开关多条件选择
      switchItems() {
        this.itemsToggle = !this.itemsToggle
        this.closePlan()
      },
      colseItems() {
        this.itemsToggle = false
      },
      // 新建显示方案
      addShowPlan(data) {
        this.closePlan()
        this.$refs.showPlan.show(data)
      },
      showPlanSaveHandel(data) {
        this.matchColConfig({
          ...data,
          query_json: JSON.parse(data.query_json)
        })
        this.getPlanList('show')
      },
      addSearchPlan(data) {
        this.closePlan()
        this.$refs.searchPlan.show(data)
      },
      searchPlanSaveHandel(data) {
        console.log(data);
        this.setSearchData({
          ...data,
          query_json: JSON.parse(data.query_json)
        })
        this.getPlanList('find')
      },
      addParamsToSearchPlan() {
        const data = _.cloneDeep([
          this.searchData,
          ...this.searchItemsData
        ])
        data.forEach(item => {
          // 如果值为日期数据，转成字符串
          if(item.value && Object.prototype.toString.call(item.value) === '[object Date]') {
            item.value = moment(item.value).format('YYYY-MM-DD HH:mm:ss')
          }
        })
        this.addSearchPlan(data);
      },
      // 刷新col的配置
      matchColConfig(data) {
        this.closePlan()
        this.showPlanIng = data
        this.$emit('matchColConfig', data.query_json.data)
      },
      getPlanList(type) {
        const params = {
          user_id: fn.getUserInfo('id'),
          page_code: this.searchPage,
          type
        }
        this.ajax.postStream('/user-web/api/new/listQueryPlan', params, res => {
          if(!res.body.result) {
            this.$message.error(res.body.msg)
            return
          }
          const key = type === 'find' ? 'searchPlanList' : 'showPlanList'
          const data = res.body.content || []
          this[key] = data.map((item) => {
            const res = {
              ...item,
              query_json: JSON.parse(item.query_json)
            };
            // 设置默认查询方案
            if (res.id_default && type === 'find' && this.isInit) {
              // alert(1);
              this.isInit = false;
              this.setSearchData(res)
              // this.search()
            }
            // 记录默认显示方案
            if (res.id_default && type === 'show' && this.isInitShow) {
              this.isInitShow = false;
              this.showPlanIng = res
            }
            return res
          })
        }, err => {
          this.$message.error(err);
        });
      },
      deletePlan(id, type, name) {
        this.$confirm(`是否确定删除"${name}"?`, '提示', {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.ajax.postStream('/user-web/api/new/removeQueryPlan', { id }, res => {
            if(res.body.result) {
              this.$message.success('删除成功')
              this.getPlanList(type)
            } else {
              this.$message.error('删除失败');
            }

          }, err => {
            this.$message.error(err);
          });
        })
      },
      // 取当前查询框数据更新当前查询方案
      updateSearchPlan() {
        const plan = this.searchPlanList.filter((item) => {
          return this.searchPlanIng && item.id === this.searchPlanIng.id
        })
        if(plan.length < 1) {
          this.$message.error('当前查询方案不存在！');
          return;
        }
        this.closePlan()
        let allSearchItem = data || [this.searchData, ...this.searchItemsData].filter(item => item.field);
        allSearchItem = _.cloneDeep(allSearchItem);
        let isHasNotValue = false;
        const disabledKey = ['IS NULL', 'IS NOT NULL', 'THIS MONTH', 'THIS WEEK', 'TODAY', 'LAST MONTH', 'LAST WEEK', 'YESTERDAY']
        allSearchItem.forEach(item => {
          if(!item.value && disabledKey.indexOf(item.operator) < 0) {
            this.$message.error(`查询条件【${item.label}】值不能为空`);
            isHasNotValue = true
          }
          // 如果值为日期数据，转成字符串
          if(item.value && Object.prototype.toString.call(item.value) === '[object Date]') {
            item.value = moment(item.value).format('YYYY-MM-DD HH:mm:ss')
          }
        })
        if(isHasNotValue) {
          return;
        }
        const data = {
          ...this.searchPlanIng,
          query_json: JSON.stringify({
            planName: this.searchPlanIng.query_json.planName,
            data: allSearchItem
          })
        }
        this.ajax.postStream('/user-web/api/new/saveOrUpdateQueryPlan', data, res => {
          if (!res.body.result) {
            this.$message.error(res.body.msg)
            return
          }
          this.getPlanList('find')
          this.$message.success('保存成功！')
        }, err => {
          this.$message.error(err);
        });
      }
    }
  }
</script>

<style scoped>
  .cursor-pointer:hover {
    color: #4db3ff;
  }
  .search-box {
    display: inline-block;
    position: relative;
    min-width: 600px;
  }
  .plan-list {
    position: absolute;
    right: -5px;
    top: 28px;
    width: 250px;
    z-index: 999;
    text-align: left;
    box-shadow: #d0d0d0 -7px 8px 12px 1px;
  }
  .plan-list .list-box {
    padding: 0px;
    margin: -10px -15px;
    overflow: auto;
    max-height: 180px;
  }
  .show-panel-list {
    height: auto;
    overflow: hidden;
  }

  .show-panel-list .left {
    display: inline-block;
    width: 100%;
    padding-right: 35px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
  }
  .show-panel-list li {
    padding: 0px 5px 0 15px;
    position: relative;
    height: 27px;
    line-height: 27px;
  }
  .show-panel-list .is-default .left {
    color: #67C23A;
  }
  .show-panel-list li.is-default::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 27px;
    width: 2px;
    background-color: #67C23A;
  }
  .show-panel-list li.action::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 27px;
    width: 2px;
    background-color: #4db3ff;
  }
  .show-panel-list .action .left {
    color: #4db3ff;
  }
  .show-panel-list li:nth-child(odd) {
    background-color: #f0f0f0;
  }
  .show-panel-list li > i {
    position: absolute;
    left: 1px;
    top: 4px;
  }
  .show-panel-list .right {
    position: absolute;
    right: 5px;
    top: 0;
  }
  .a:hover{
    color: #00a0e9;
    cursor: pointer;
  }
  .h-right {
    line-height: 30px;
    padding-right: 10px;
    float: right;
  }
  .search-items {
    position: absolute;
    padding: 5px 20px 10px 10px;
    left: 4px;
    top: 25px;
    background-color: rgb(238, 238, 238);
    z-index: 999;
    max-height: 200px;
    overflow: auto;
  }
  .color-red {
    color: #F56C6C;
  }
  .a.color-red:hover {
    color: #f43838;
  }
</style>
<style>
  .el-collapse-item__header {
    height: 30px!important;
    line-height: 30px!important;
  }
</style>
<style>
  #searchplan .dialog-center{
    min-width: 500px!important;
  }
  #searchplan .search-items-box {
    margin-left: -3px;
  }
  #searchplan .content .btn {
    right: 0px;
  }

  #searchplan .content .first.btn {
    right: 13px;
  }
  #searchplan .el-transfer-panel__body {
    box-sizing: content-box;
  }
  #searchplan .el-dialog__header {
    text-align: center;
    font-size: 14px!important;
  }
  #searchplan .el-dialog__body {
    text-align: center;
    padding: 10px 20px;
  }
  #searchplan .content{
    text-align: left;
    zoom: 1;
    position: relative;
  }
  #searchplan .content:after {
    clear: left;
    display: block;
    content: '';
    overflow: hidden;
  }

  #searchplan .content .left{
    float: left;
    width: 470px;
  }
  #searchplan .content .right{
    position: absolute;
    left: 261px;
    top: 73px;
    width: 198px;
    max-height: 246px;
    overflow: auto;
  }
  #searchplan .content .right li{
    height: 32px;
    line-height: 32px;
    width: 100%;
    position: relative;
  }
  #searchplan .dragging {
    border: 1px solid #99A9BF;
  }
</style>
