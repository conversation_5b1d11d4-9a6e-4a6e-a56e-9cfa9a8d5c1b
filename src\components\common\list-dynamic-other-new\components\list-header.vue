<template>
  <div class="list-header">
    <div class="left">
      <template v-for='(btn,index) in btns'>
        <xpt-btngroup v-if='btn.isBtnGroup' :btngroup='btn.btnGroupList' class="mgl10"
                      :disabled="
						typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled
						||btn.loading
						||false
					"></xpt-btngroup>
        <el-button
          v-else
          :type='btn.type'
          size='mini'
          @click='e => btn.click&&btn.click(e)'
          :disabled="
						typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled
						||btn.loading
						||false
					"
          :loading="btn.loading||false"
        >{{btn.txt}}</el-button>
      </template>

      <slot name='btns'></slot>
    </div>
    <div class="right">
      <search-box ref="dynamic_searchbox" @searchClick="searchClick" :searchPage="searchPage" @matchColConfig="matchColConfig" :searchParams="searchParams"></search-box>
    </div>
  </div>
</template>

<script>
  import searchBox from './search-box'
  export default {
    name: "list-header",
    components: {
      searchBox
    },
    props: {
      btns: {
        default: () => []
      },
      searchPage: String,
      searchParams: {
        type: Object,
        default: () => ({})
      }
    },
    methods: {
      matchColConfig(data) {
        this.$emit('matchColConfig', data)
      },
      searchClick(data, callback) {
        this.$emit('searchClick', data, callback)
      }
    }
  }
</script>

<style scoped>
  .list-header {
    padding: 4px 10px;
    background-color: #eee;
  }
  .list-header .left {
    height: auto;
    line-height: 22px;
    display: inline-block;
    width: calc(100% - 645px);
    min-width: 580px;
  }
  .list-header .right {
    display: inline-block;
    text-align: right;
    float: right;
    width: 600px;
  }
</style>
