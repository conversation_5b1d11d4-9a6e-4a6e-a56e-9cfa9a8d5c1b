<template>
  <div class="search-item">
    <el-select v-model="searchData.field" @visible-change="visibleChange" size='mini' class="mgr5 w120 v-middle" filterable placeholder="请选择字段">
      <el-option
        v-for="(item, index) of propsData.fields" :key="item.field"
        :label="item.comment"
        :value="item.field">
      </el-option>
    </el-select>
    <el-select size='mini' :key="searchData.field" class="mgr5 w120 v-middle" filterable placeholder="请选择匹配符" v-model="searchData.operator">
      <el-option
        v-for="(item, index) of propIng.options"
        :key="item.operator + index + Math.random"
        :label="item.desc"
        :value="item.operator">
      </el-option>
    </el-select>
    <!--  值有几种情况，根据类型不通，展示文本输入框或者下拉框或者日期选择器  -->
    <el-input v-if="valueDisabled" size='mini' class="mgr5 w180 v-middle" disabled/>
    <el-select
      v-else-if="propIng.type === 'Enum'"
      class="mgr5 w180 v-middle"
      placeholder="请选择"
      :multiple="propIng.is_checkbox"
      :class="propIng.is_checkbox ? 'multipleStyle' : ''"
      size='mini'
      filterable
      v-model="searchData.value">
        <el-option
          v-for="(item, index) of propIng.values"
          :key="index"
          :label="item.name"
          class="ssssssssss"
          :value="item.code">
        </el-option>
    </el-select>

    <el-date-picker
      v-else-if="propIng.type === 'Date'"
      class="w180 mgr5 v-middle"
      style='width:180px !important'
      v-model="searchData.value"
      :type="'datetime'"
      placeholder="选择日期"
      size='mini'
      :editable='false'
      :picker-options='options'
      format="yyyy-MM-dd HH:mm:ss">
    </el-date-picker>

    <el-input v-else @keydown.enter.native="search" size='mini' class="mgr5 w180 v-middle" placeholder="输入要搜索的值" v-model="searchData.value"/>
  </div>
</template>

<script>
  export default {
    name: "search-item",
    model: {
      prop: 'searchData',
      event: 'input'
    },
    props: {
      searchData: {
        default: () => {
          return {
            condition: 'AND', // 查询字段
            field: '', // 查询字段
            operator: '', // 匹配规则
            value: '',  // 查询值
            label: '',
            table: '',  // 关联表名，后端需要
          }
        }
      },
      isShowDefaultField: Boolean,
      searchPage: String
    },
    data() {
      return {
        propsData: {
          fields: [], // 字段列表
        },
        oldProp: {},
        options: {
          date:(function() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth()+1;
            var day = date.getDate();
            var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
            return new Date(time);
          })(),
        }
      }
    },
    computed: {
      // 当前选中的字段
      propIng: function () {
        const res = this.propsData.fields.filter((item) => {
          return item.field === this.searchData.field
        })
        return res[0] || {}
      },
      // 输入框是否禁用
      valueDisabled: function () {
        const disabledKey = ['IS NULL', 'IS NOT NULL', 'THIS MONTH', 'THIS WEEK', 'TODAY', 'LAST MONTH', 'LAST WEEK', 'YESTERDAY']
        const t = disabledKey.indexOf(this.searchData.operator) > -1;
        if(t) {
          this.searchData.value = '';
        }
        return t;
      }
    },
    watch: {
      'searchData.field': function (nval, oval) {
        if(nval) {
          this.searchData.label = this.propIng.comment
          this.searchData.table = this.propIng.table
        }
      }
    },
    mounted() {
      this.getProps()
    },
    methods: {
      search() {
        this.$emit('search')
      },
      visibleChange(tog) {
        if(tog) {
          this.oldProp = {...this.propIng}
        } else {
          const nval = this.propIng.field
          const oval = this.oldProp.field
          if (nval !== oval) {
            this.searchData.operator = this.propIng.options[0].operator
            this.searchData.value = ''
            const valArr = this.propsData.fields.filter((item) => {
              return item.field === nval
            })
            if(['Enum'].indexOf(valArr[0].type) > -1 && valArr[0].is_checkbox) {
              this.searchData.value = []
            }
          }
        }
      },
      filterFields (fields){
        fields.map(field => {
          if (field.is_checkbox && field.comment !== '进度标签') {
            field.options = [{operator: "MULTI", desc: "多选"}]
          }
        })
        return fields
      },
      // 获取所有可供查询的字段信息
      getProps() {
        const params = {
          page: this.searchPage
        }
        const data = window.sessionStorage.getItem(this.searchPage + 'fields')
        if (data) {
          this.propsData = JSON.parse(data)
          if(!this.searchData.field && this.isShowDefaultField) {
            this.propsData.fields.forEach((item) => {
              if (item.is_checkbox && item.comment !== '进度标签'){
                item.options = [{operator: "MULTI", desc: "多选"}]
              }
              if (item.is_default) {
                this.searchData.field = item.field
                this.searchData.operator = item.options[0].operator
              }
            })
          }
          return;
        }
        this.ajax.postStream('/user-web/api/new/listFields', params, res => {
          if (!res.data.result) {
            this.$message.error(res.data.msg)
            return
          }
          let fieldsList = this.filterFields(res.data.content.fields);;
          window.sessionStorage.setItem(this.searchPage + 'fields', JSON.stringify(res.data.content))
          this.propsData = res.data.content
          this.propsData.fields = JSON.parse(JSON.stringify(fieldsList))
          // 设置默认字段
          if(!this.searchData.field && this.isShowDefaultField) {
            this.propsData.fields.forEach((item) => {
              if (item.is_default) {
                this.searchData.field = item.field
                this.searchData.operator = item.options[0].operator
              }
            })
          }

        }, err => {
          this.$message.error(err);
        });
      }
    }
  }
</script>

<style scoped>
  .search-item {
    display: inline-block;
    width: 445px;
  }

  .multipleStyle{
    height: 24px;
    overflow: hidden;
  }
  .w180 {
    width: 180px;
  }
  .el-select .el-input, .el-input--mini .el-input__inner {
    height: 24px !important;
  }
  .el-select__tags{
    height: 24px;
    overflow: hidden;
  }
</style>

