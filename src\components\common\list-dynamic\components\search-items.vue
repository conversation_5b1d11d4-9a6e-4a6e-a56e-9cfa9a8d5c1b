<template>
  <ul class="search-items-box">
    <li v-for="(item, index) of searchItems" :key="index">
<!--      <span v-show="firstItemCondition || index > 0" class="condition" @click="switchCondition(searchItems[index])">-->
<!--        {{searchItems[index].condition === 'AND' ? '与' : '或'}}-->
<!--      </span>-->
      <item @search="search()" :is-show-default-field="index === 0 && firstItemShowDefault" v-model="searchItems[index]" :searchPage="searchPage"/>
      <span class="btn">
        <i class="el-icon-plus a" @click="pushItem(index)"></i>
        <i class="el-icon-delete a color-red" @click="deleteItem(index)"></i>
      </span>
    </li>
  </ul>
</template>

<script>
  import item from './search-item'
  export default {
    name: "search-items",
    components: {
      item
    },
    model: {
      prop: 'searchItems',
      event: 'change'
    },
    props: {
      searchPage: String,
      firstItemCondition: Boolean,
      firstItemShowDefault: {
        default: false
      },
      searchItems: {
        default: () => {
          return []
        }
      }
    },
    data() {
      return {
      }
    },
    mounted() {
      if(this.searchItems.length < 1) {
        this.pushItem()
      }
    },
    methods: {
      pushItem(index = -1) {
        const item = {
          condition: "AND",
          field: "",
          operator: "",
          table: "",
          value: ""
        }
        const newArr = [...this.searchItems]
        newArr.splice(++index, 0, item)
        this.$emit('change', newArr)
      },
      deleteItem(i) {
        const data = [...this.searchItems]
        // 当仅有一条数据，删除改为清空数据
        if(this.searchItems.length <= 1) {
          data[0] = {
            condition: "AND",
            field: "",
            operator: "",
            table: "",
            value: ""
          }
        } else {
          data.splice(i, 1);
        }
        this.$emit('change', data)
      },
      switchCondition(item) {
        // item.condition = item.condition === 'AND' ? 'OR' : 'AND'
      },
      search() {
        this.$emit('search')
      }
    }
  }
</script>

<style scoped>
  .search-items-box li {
    position: relative;
    padding: 0 20px 4px 4px;
  }
  .btn {
    position: absolute;
    right: -10px;
    top: 4px;
  }
  .first.btn {
    right: 3px;
  }
  .condition {
    padding: 2px;
    color: #4db3ff;
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 4px;
  }
  .a:hover{
    color: #00a0e9;
    cursor: pointer;
  }
  .color-red {
    color: #F56C6C;
  }
  .a.color-red:hover {
    color: #f43838;
  }
</style>
