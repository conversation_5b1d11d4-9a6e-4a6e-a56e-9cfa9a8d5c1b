<template>
  <div class="list-dynamic">
    <list-header ref="dynamic_header" :class="($parent && $parent.params && $parent.params.menuInfo) ? $parent.params.menuInfo.code + $parent.params.tabName : ''" :btns="btns" :searchPage="searchPage" @searchClick="searchClick" @matchColConfig="matchColConfig">
      <template slot="btns">
        <slot name="btns"></slot>
      </template>
    </list-header>
    <div class="body"  :style="'height: calc( 100vh - '+ listDynamicHeight + 'px)'">
      <slot name="table" :colData="colsConfig">
        <xpt-list ref="list" v-if="!list2" v-bind="$props"
                  :colData="colsConfig"
                  :showHead="false"
                  :showSelectRowNumByOnlyKey='showSelectRowNumByOnlyKey'
                  @radio-change="eventObj['radio-change']"
                  @selection-change="eventObj['selection-change']"
                  @page-size-change="eventObj['page-size-change']"
                  @current-page-change="eventObj['current-page-change']"
                  @sort-change="eventObj['sort-change']"
                  @row-dblclick="eventObj['row-dblclick']"
                  @row-click="eventObj['row-click']"
                  @cell-click="eventObj['cell-click']"
                  @count-off="eventObj['count-off']"
                  :showCount ='showCount'
                  :taggelClassName="taggelClassName"
                  :row-key="rowKey"
        ></xpt-list>
        <xpt-list2 ref="list" v-else v-bind="$props"
                  :colData="colsConfig"
                  :showHead="false"
                  @radio-change="eventObj['radio-change']"
                  @selection-change="eventObj['selection-change']"
                  @page-size-change="eventObj['page-size-change']"
                  @current-page-change="eventObj['current-page-change']"
                  @sort-change="eventObj['sort-change']"
                  @row-dblclick="eventObj['row-dblclick']"
                  @row-click="eventObj['row-click']"
                  @cell-click="eventObj['cell-click']"
                  @count-off="eventObj['count-off']"
                  :row-key="rowKey"
        ></xpt-list2>
      </slot>
    </div>
  </div>

</template>

<script>
  /**
   * <AUTHOR>
   * 使用方式：跟旧版一模一样
   * 组件说明：在list组件基础上拓展了一些功能，主要是两方面：
   * 1.多套显示方案，把列的配置(label, prop)保存到后端，动态控制列的显示以及顺序。增加显示方案配置页面
   * 2.查询字段动态匹配操作符
   *
   * 实现逻辑：
   * 1.显示方案：基于list组件做包装，在当前组件做colData数据封装
   * 2.查询方案：鉴于查询功能改动较大，所以重构list头部，主要增加字段匹配、显示方案修改新增功能
   *
   * ps: 方案的具体内容由前端定(方案保存组件内定义)，后端不解析。当没有默认方案时，后端返回所有可显示字段
   *
   * **/
  import fn from '@common/Fn.js'
  import list from '../list'
  import list2 from '../list2'
  import listHeader from './components/list-header'
  const eventType = ['radio-change', 'selection-change', 'page-size-change', 'current-page-change', 'sort-change', 'row-dblclick', 'row-click', 'cell-click',"count-off"]
  export default {
    name: "list-dynamic",
    components: {
      listHeader
    },
    props: {
      ...list.props,
      // 页面id，用于匹配查询字段和显示方案
      searchPage: {
        required: true,
        default: ''
      },
      list2: Boolean,
      btns: Array,
      showSelectRowNumByOnlyKey:Boolean,
      filterList: Array,
      rowKey: {
        type: String,
      },
    },
    data() {
      return {
        colsConfig: [],
        eventObj: {},
        listDynamicHeight: '',
        heightCss: {},
        baseColsConfig: []
      }
    },
    created() {
      // 事件对象，用于把list组件的事件抛给调用者
      const self = this
      eventType.forEach(type => {
        this.eventObj[type] = function() {
          self.$emit(type, ...arguments)
        }
      })
    },
    mounted() {
      if (this.$parent && this.$parent.params && this.$parent.params.menuInfo) {
        let id = '.' + this.$parent.params.menuInfo.code + this.$parent.params.tabName
        let element = document.querySelectorAll(id)
        let elementHeight = element[0].offsetHeight
        let jumpHight = Number(elementHeight) + Number(116)
        this.listDynamicHeight = jumpHight
      } else {
          this.listDynamicHeight = 138
      }
      this.heightCss = {
        height: 'calc( 100vh - ' + this.listDynamicHeight + 'px)'
      }
      this.init()
    },
    methods: {
      init() {
        // 保存初始数据
        this.colsConfig = [...this.colData]
        // 异步更新数据
        this.getColConfig().then((config) => {
           this.matchColConfig(config)
        }).catch((e) => {
          this.$message.error(e)
        })
      },
      /**
       * 获取默认表格列配置
       * @param key
       */
      getColConfig() {
        return new Promise((reslove, reject) => {
          const params = {
            user_id: fn.getUserInfo('id'),
            page_code: this.searchPage,
            type: 'show'
          }
          this.ajax.postStream('/user-web/api/new/showDesc', params, res => {
            if(!res.body.result) {
              reject('获取默认显示方案失败！')
              return
            }
            if(res.body.content[0]) {
              try {
                const fileds = JSON.parse(res.body.content[0].query_json)
                reslove(fileds.data)
              } catch (e) {
                reject('默认显示方案数据异常！')
                return;
              }
            } else {
              reslove(res.body.content.data)
            }


          }, err => {
            this.$message.error(err);
          });
        })
      },
      /**
       * 根据接口响应的列配置和本地的列配置糅合出需要展示的列配置
       * @param remoteConfig 接口返回的配置
       */
      matchColConfig(remoteConfig) {
        const newConfig = []
        remoteConfig.forEach((ritem) => {
          this.colData.forEach((litem) => {
            if(ritem.prop === litem.prop) {
              newConfig.push({
                ...litem,
                ...ritem
              })
            }
          })
        })
        this.colsConfig = newConfig
        this.baseColsConfig = newConfig
        this.filterColList()
      },
      searchClick(data, callback) {
        this.$emit('search-click', data, callback)
      },
      filterColList() {
        if (this.filterList.length > 0) {
            let self = this
            let colsConfig = []
            this.baseColsConfig.forEach(item => {
              if (!(self.filterList.includes(item.prop))) {
                colsConfig.push(item)
              }
            })
            this.colsConfig = colsConfig
          } else {
            this.colsConfig = this.baseColsConfig
          }
      }
    },
    watch: {
      filterList (n, o) {
        if (n && this.baseColsConfig && this.baseColsConfig.length > 0) {
          this.filterColList()
        }
      }
    }
  }
</script>

<style scoped>
  .list-dynamic {
    margin: 0 -10px;
    min-width: 1200px;
  }
  .body {
    padding: 8px 8px 0px 8px;
    height: calc(100vh - 138px);
  }
  /deep/.el-table__footer-wrapper {
    width: 100%;
  }
</style>
