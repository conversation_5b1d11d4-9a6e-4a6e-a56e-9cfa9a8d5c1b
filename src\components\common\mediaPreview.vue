<template>
  <div>
    <el-carousel :autoplay="false" indicator-position="none" @change="handleCarouselChange" :height="params.height">
      <el-carousel-item v-for="(item, index) in params.mediaList" :key="index">
        <div class="media-container">

          <img :style="{ transform: `rotate(${rotation}deg)` }" v-if="item.type === 'image'" :src="item.src" alt="图片" class="media-item"  />
          <video :style="{ transform: `rotate(${rotation}deg)` }" v-if="item.type === 'video'" :src="item.src" controls class="media-item"></video>
        </div>
      </el-carousel-item>
    </el-carousel>
    <div class="actions">

      <el-button @click="rotateLeft" size="mini">左旋</el-button>
      <span style="margin: 0 8px">({{currentIndex + 1}} / {{params.mediaList.length}})</span>
      <el-button @click="rotateRight" size="mini">右旋</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
   params: {
    type: Object,
    default: () => ({
      height: '500px',
      mediaList: [],
    }),
   }
  },
  data() {
    return {
      currentIndex: 0,
      rotation: 0,
      mounted: false
    };
  },
  methods: {
    handleCarouselChange(index) {
      this.currentIndex = index;
      this.rotation = 0; // 每次切换时重置旋转角度
    },
    prev() {
      if (this.currentIndex > 0) {
        this.$refs.carousel.setActiveItem(this.currentIndex - 1);
      }
    },
    next() {
      if (this.currentIndex < this.mediaList.length - 1) {
        this.$refs.carousel.setActiveItem(this.currentIndex + 1);
      }
    },
    rotateLeft() {
      this.rotation -= 90;
    },
    rotateRight() {
      this.rotation += 90;
    },

  },
};
</script>

<style scoped lang="stylus">
.media-container{
  margin-top: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  transition: transform 0.3s ease;
}

.media-item {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.actions {
  margin-top: 15px;
  text-align: center;
}
</style>
