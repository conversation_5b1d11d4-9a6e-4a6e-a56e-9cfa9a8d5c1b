<!-- 操作记录 -->
<template>
	<xpt-list 
		:data='operaterList' 
		:colData='operaterCols'
		:showHead='false' 
		:pageTotal='operaterCount'
		selection='' 
		@page-size-change='operaterPageSizeChange' 
		@current-page-change='operaterPageChange'
	></xpt-list>
</template>
<script>
export default {
	props: ['source_id', 'api'],
	data() {
		let self = this
		return {
			operaterList: [],
			operaterCols: [
				{
					label: '操作人',
					prop: 'operator_name'
				}, {
					label: '业务操作',
					prop: 'operation_name'
				}, {
					label: '操作时间',
					prop: 'operation_time',
					format: 'dataFormat1'
				}, {
					label: '操作描述',
					prop: 'operation_desc'
				}, {
					label: '备注',
					prop: 'remark'
				}
			],
			operaterQuery: {
				page: {
					length: self.pageSize,
					pageNo: 1
				}
			},
			operaterCount: 0
		}
	},
	methods: {
		getOperater(id) {
			let data = JSON.parse(JSON.stringify(this.operaterQuery));
			data.source_id = this.source_id || id;
			if(!data.source_id) return
			this.ajax.postStream(this.api || '/order-web/api/receipt/v2/logList', data, res => {
				if(res.body.result && res.body.content) {
					this.operaterList = res.body.content.list || [];
					this.operaterCount = res.body.content.count || 0;
				} 
			}, err => {
				this.$message.error(err);
			})
		},
		operaterPageSizeChange(ps) {
			this.operaterQuery.page.length = ps;
			this.getOperater();
		},
		operaterPageChange(page) {
			this.operaterQuery.page.pageNo = page;
			this.getOperater();
		}
	}
}
</script>
