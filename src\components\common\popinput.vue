<!--弹出框输入-->
<template>
	<div class="xpt-flex" style='padding-top: 10px'>
		<div>关闭类型</div>
		<div style="padding:10px 0;">
			<el-select
				v-model="close_type"
				size="mini"
				placeholder="请选择"
				>
					<el-option key="LOST_ITEM_FOUND" label="丢件找到" value="LOST_ITEM_FOUND"></el-option>
					<el-option key="STOP_PRODUCTION" label="停产无法补件" value="STOP_PRODUCTION"></el-option>
					<el-option key="PLACE_WRONG_ORDER" label="下错单" value="PLACE_WRONG_ORDER"></el-option>
					<el-option key="NO_NEED_TO_HANDLE" label="不需要（协商，赔偿，维修）处理" value="NO_NEED_TO_HANDLE"></el-option>
					<el-option key="OTHER" label="其它" value="OTHER"></el-option>
				</el-select>
		</div>
		<div>{{params.popText}}</div>
		<div style="padding:10px 0;"><el-input type="textarea"  v-model="reason" size="small" style="width:100%;"></el-input></div>
		<div><el-button type='primary' size='small' @click="callback(1)">确定</el-button><el-button type='primary' size='small' @click="callback(0)">取消</el-button></div>
	</div>
</template>
<script>
export default {
	props:["params"],
	data(){
		var self = this;
		return {
			reason:'',
			close_type:''
		}
	},
	methods:{
		closeCurrentComponent(){
            this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		callback(type){
			var bool = type == 1?true:false;
			bool&&this.params.callback({reason:this.reason,close_type:this.close_type});
			this.closeCurrentComponent();
		}
	},
	mounted:function(){
		let _this = this;
		this.reason = this.params.rejectReason;
	},
	watch:{
		params:{
            handler:(val,oldVal)=>{
                this.reason = val.rejectReason;
            },
            deep:true
        }
	}
}
</script>
<style lang="stylus">
.image-upload
	position: relative
	cursor: pointer
	overflow: hidden
	display: inline-block
	*display: inline
	*zoom: 1
	input
		position: absolute
		font-size: 100px
		right: 0
		top: 0
		height:100%
		opacity: 0 
		cursor: pointer
</style>
