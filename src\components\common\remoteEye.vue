<!-- 后端脱敏加密，调用接口解密 -->
<template>
  <span v-loading="isLoading" class="remote-eye">
    {{ displayData }}
    <i
      class="remote-eye-icon"
      :class="{'el-icon-linshi-eye-open': isOpen, 'el-icon-linshi-eye-close': !isOpen}"
      @click="onToggleEye"
    ></i>
  </span>
</template>

<script>
export default {
  name: 'RemoteEye',
  props: {
    initValue: {
      type: String
    },
    decryptApi: {
      type: Function,
      required: true,
    }
  },
  data() {
    return {
      openData: "",
      hideData: "",
      isOpen: false,
      isLoading: false,
    }
  },
  computed: {
    displayData() {
      return this.isOpen ? this.openData : this.hideData;
    }
  },
  methods: {
    onToggleEye() {
      if (this.isOpen || this.openData) {
        this.isOpen = !this.isOpen;
        return
      }

      if (typeof this.decryptApi === 'function') {
        this.isLoading = true;
        this.decryptApi().then((decryptedData) => {
          this.openData = decryptedData;
          this.isOpen = !this.isOpen;
        }).finally(() => {
          this.isLoading = false;
        })
      }
    }
  },
  watch: {
    initValue: {
      handler() {
        this.hideData = this.initValue
      },
      immediate: true,
    }
  }
}
</script>

<style scoped>
.remote-eye {
  display: inline-flex;
  gap: 8px;
}
.remote-eye-icon {
  cursor: pointer;
}
</style>
