<template>
	<div ref="$table" style="height:100%;">
		<slot :list="newList"></slot>
	</div>
</template>
<script>
	export default {
		props: ['list'],
		data (){
			return {
				newList: this.list,
			}
		},
		methods: {
			largeDataScrollRender (dataList){
	            var self = this
	            ,   maxLength = 100
	            ,   renderLength = 50
	            ,   trHeight = 25

	            if(this.largeDataScrollRender.initEvent){
					/*empty*/
	            }else if (dataList.length > maxLength){
	                this.largeDataScrollRender.initEvent = true
	                this.$refs.$table.querySelector('.el-table__body-wrapper').addEventListener('scroll', function (){
	                    if(
	                        (
	                            (this.scrollTop === 0 && self.newList[0].__index !== 1) ||
	                            (this.scrollTop === this.scrollHeight - this.clientHeight && self.newList.slice(-1)[0].__index % renderLength === 0)
	                        )
	                        && !self.largeDataScrollRender.isRendering
	                    ){
	                    	console.log(3745674567)
	                        self.largeDataScrollRender.isRendering = true
	                        var _scrollTop
	                        if(this.scrollTop === 0){
	                            var _headIndex = self.newList[0].__index - renderLength - 1
	                            self.newList = self.largeDataScrollRender.saveList.slice(_headIndex < 0 ? 0 : _headIndex, _headIndex < 0 ? maxLength : self.newList[0].__index + renderLength - 1)
	                            _scrollTop = _headIndex < 0 ? 0 : renderLength * trHeight
	                        }else {
	                            self.newList = self.largeDataScrollRender.saveList.slice(self.newList.slice(-1)[0].__index - renderLength, self.newList.slice(-1)[0].__index + renderLength)
	                            _scrollTop = renderLength * trHeight - this.clientHeight
	                        }
	                        self.$nextTick(() => {
	                            self.largeDataScrollRender.isRendering = false
	                            this.scrollTop = _scrollTop
	                        })
	                    }
	                })
	            }

	            if(dataList.length > maxLength){
	                self.largeDataScrollRender.isRendering = false
	                this.largeDataScrollRender.saveList = dataList.map((obj, index) => {
	                    obj.__index = index + 1
	                    return obj
	                })
	                return this.largeDataScrollRender.saveList.slice(0, maxLength)
	            }else {
	                self.largeDataScrollRender.isRendering = true
	                return dataList
	            }
	        },
		},
		watch: {
			list (){
				this.newList = this.largeDataScrollRender(this.list)
			},
		},
	}
</script>