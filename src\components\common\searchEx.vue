<template lang="html">
    <div class="rela">
        <!-- 初始化状态 -->
        <div class="">
            <el-select size='mini' class="mgr5 w120 v-middle" v-model="where[0].fieldString" filterable placeholder="请选择" @change='change'>
                <el-option
                  v-for="item in params.fields"
                  :key="item.comment"
                  :label="item.comment"
                  :value="JSON.stringify(item)">
                </el-option>
            </el-select>

            <el-select size='mini' class="mgr5 w99 v-middle" v-model="where[0].operator"  placeholder="请选择">
                <el-option
                  v-for="item in params.operators"
                  :key="item.desc"
                  :label="item.desc"
                  :value="item.operator">
                </el-option>
            </el-select>

            <el-date-picker ref='date' v-model="where[0].value" class="w180 mgr5 v-middle"
                :type="where[0].field&&where[0].field.is_default ? 'date' : 'datetime'"
                style='width:180px !important' placeholder="选择日期" size='mini'  :editable='false'
                :picker-options='options'
                v-if="where[0].field&&where[0].field.type=='Date'&&where[0].operator!=='IS NULL'&&where[0].operator!=='IS NOT NULL'&&where[0].operator!=='IS NOT NULL'&&where[0].operator!=='THIS MONTH'&&where[0].operator!=='THIS WEEK'&&where[0].operator!=='TODAY'&&where[0].operator!=='LAST MONTH'&&where[0].operator!=='LAST WEEK'&&where[0].operator!=='YESTERDAY'"
                format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>


            <el-select v-model="where[0].value" class="w120 mgr5 v-middle multiple-select" placeholder="请选择" size='mini' multiple
                v-else-if="where[0].field&&where[0].field.type=='Enum'&&where[0].field.is_checkbox==true&&where[0].operator!=='IS NULL'&&where[0].operator!=='IS NOT NULL'&&where[0].operator!=='IS NOT NULL'&&where[0].operator!=='THIS MONTH'&&where[0].operator!=='THIS WEEK'&&where[0].operator!=='TODAY'&&where[0].operator!=='LAST MONTH'&&where[0].operator!=='LAST WEEK'&&where[0].operator!=='YESTERDAY'">
                <el-option
                  v-for="item in where[0].field.values"
                  :label="item.name"
                  :value="item.code"
                  :key='item.code'>
                </el-option>
            </el-select>
            <el-select v-model="where[0].value" class="w120 mgr5 v-middle" placeholder="请选择" size='mini'
                v-else-if="where[0].field&&where[0].field.type=='Enum'&&where[0].operator!=='IS NULL'&&where[0].operator!=='IS NOT NULL'&&where[0].operator!=='IS NOT NULL'&&where[0].operator!=='THIS MONTH'&&where[0].operator!=='THIS WEEK'&&where[0].operator!=='TODAY'&&where[0].operator!=='LAST MONTH'&&where[0].operator!=='LAST WEEK'&&where[0].operator!=='YESTERDAY'">
                <el-option
                  v-for="item in where[0].field.values"
                  :label="item.name"
                  :value="item.code"
                  :key='item.code'>
                </el-option>
            </el-select>
            <el-input size='mini' class="mgr5 w180 v-middle" placeholder="输入要搜索的值"
                v-model="where[0].value"
                :disabled="where[0].operator==='IS NULL'||where[0].operator==='IS NOT NULL'||where[0].operator==='THIS MONTH'||where[0].operator==='THIS WEEK'||where[0].operator==='TODAY'||where[0].operator==='LAST MONTH'||where[0].operator==='LAST WEEK'||where[0].operator==='YESTERDAY'"
                @keyup.enter.native="search"
                v-else>

            </el-input>

            <i class="mgr5 el-icon-arrow-down v-middle cursor-pointer" @click="whereShow()"></i>

            <el-button size="mini" type="primary" class="v-middle" @click="search" :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>

            <el-button size="mini" type="primary" class="v-middle" @click="reset">重置</el-button>
            <i class="mgl5 el-icon-menu v-middle cursor-pointer" @click="whereShow()"></i>
        </div>

        <!-- 展开的搜索条件 -->
        <div class="where-show-panel" v-if="isWhereShow">
            <ul>
                <li v-for="(item,index) in where">
                    <el-select size='mini' class="mgr5 w120 v-middle" v-model="item.fieldString"
                        filterable placeholder="请选择" @change='itemChange(index,item)'>
                        <el-option v-for="(item,index) in params.fields"
                            :key="index"
                            :label="item.comment"
                            :value="JSON.stringify(item)">
                        </el-option>
                    </el-select>

                    <el-select size='mini' class="mgr5 w99 v-middle" v-model="item.operator"
                        placeholder="请选择" v-if="item.field&&item.field.type==='Date'">
                        <el-option v-for="items in operatorDate"
                            :key="items.desc"
                            :label="items.desc"
                            :value="items.operator">
                        </el-option>
                    </el-select>

                    <el-select size='mini' class="mgr5 w99 v-middle" v-model="item.operator"
                        placeholder="请选择" v-else-if="item.field&&item.field.type==='String'">
                        <el-option v-for="items in operatorString"
                            :key="items.desc"
                            :label="items.desc"
                            :value="items.operator">
                        </el-option>
                    </el-select>

                    <el-select size='mini' class="mgr5 w99 v-middle" v-model="item.operator"
                        placeholder="请选择" v-else-if="item.field&&item.field.type==='Number'">
                        <el-option v-for="items in operatorNumber"
                            :key="items.desc"
                            :label="items.desc"
                            :value="items.operator">
                        </el-option>
                    </el-select>
                    <el-select size='mini' class="mgr5 w99 v-middle" v-model="item.operator"
                        placeholder="请选择" v-else-if="item.field&&item.field.is_checkbox===true">
                        <el-option
                            key="1"
                            label="多选"
                            value="MULTI">
                        </el-option>
                    </el-select>
                    <el-select size='mini' class="mgr5 w99 v-middle" v-model="item.operator"
                        placeholder="请选择" v-else>
                        <el-option v-for="items in operatorEnum"
                            :key="items.desc"
                            :label="items.desc"
                            :value="items.operator">
                        </el-option>
                    </el-select>

                    <el-date-picker v-model="item.value" class="w180 mgr5 v-middle"
                        :type="item.field&&item.field.is_default ? 'date' : 'datetime'" placeholder="选择日期" size='mini'
                        v-if="item.field&&item.field.type=='Date'&&item.operator!=='IS NULL'&&item.operator!=='IS NOT NULL'&&item.operator!=='THIS MONTH'&&item.operator!=='THIS WEEK'&&item.operator!=='TODAY'&&item.operator!=='LAST MONTH'&&item.operator!=='LAST WEEK'&&item.operator!=='YESTERDAY'"
                        :editable='false' :picker-options='options'
                        format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>

                    <el-select v-model="item.value" class="w180 mgr5 v-middle"
                        multiple
                        placeholder="请选择" size='mini'
                        v-else-if="item.field&&item.field.is_checkbox==true&&item.field.type=='Enum'&&item.operator!=='IS NULL'&&item.operator!=='IS NOT NULL'&&item.operator!=='THIS MONTH'&&item.operator!=='THIS WEEK'&&item.operator!=='TODAY'&&item.operator!=='LAST MONTH'&&item.operator!=='LAST WEEK'&&item.operator!=='YESTERDAY'">
                        <el-option v-for="row in item.field.values"
                            :label="row.name"
                            :value="row.code"
                            :key='row.code'>
                        </el-option>
                    </el-select>
                    <el-select v-model="item.value" class="w180 mgr5 v-middle"
                        placeholder="请选择" size='mini'
                        v-else-if="item.field&&item.field.type=='Enum'&&item.operator!=='IS NULL'&&item.operator!=='IS NOT NULL'&&item.operator!=='THIS MONTH'&&item.operator!=='THIS WEEK'&&item.operator!=='TODAY'&&item.operator!=='LAST MONTH'&&item.operator!=='LAST WEEK'&&item.operator!=='YESTERDAY'">
                        <el-option v-for="row in item.field.values"
                            :label="row.name"
                            :value="row.code"
                            :key='row.code'>
                        </el-option>
                    </el-select>


                    <el-input size='mini' class="mgr5 w180 v-middle"
                        placeholder="输入要搜索的值" v-model="item.value"
                        :disabled="item.operator==='IS NULL'||item.operator==='IS NOT NULL'||item.operator==='THIS MONTH'||item.operator==='THIS WEEK'||item.operator==='TODAY'||item.operator==='LAST MONTH'||item.operator==='LAST WEEK'||item.operator==='YESTERDAY'"
                        @keyup.enter.native="search"
                        v-else>
                    </el-input>

                    <i v-if="index == 0" class="mgr5 el-icon-arrow-up v-middle cursor-pointer"
                        @click="whereShow(false)">
                    </i>
                    <i v-if="index !=0" class="mgr5 el-icon-delete2 v-middle cursor-pointer"
                        @click="deleteWhere(index)">
                    </i>
                </li>
                <slot name='sort'></slot><!-- 字段排序功能 -->
                <li class="save">
                    <el-input size="mini" class="mgr5" v-model="planName"
                        placeholder="方案名字" :maxlength='15'>
                    </el-input>
                    <el-button size="mini" type="primary"
                        class="v-middle" @click="savePlan()">
                        保存方案
                    </el-button>
                </li>
            </ul>
            <!-- 已保存的方案 -->
            <div class="plan-show-panel">
                <el-row>
                    <el-col :span='20'>
                        <el-input size="mini" v-model="planNameFilter" placeholder="方案名字"></el-input>
                    </el-col>
                    <el-col :span='4'>
                        <el-button icon='close' size='mini' @click='planNameFilter = ""'></el-button>
                    </el-col>
                </el-row>
                <ul>
                    <li @click="selectPlan(item)" v-for="(item,index) in plan" :key='index'><span>{{item.name}}</span><i class="el-icon-circle-cross" @click.stop="deletePlan(item)"></i></li>
                </ul>
            </div>
        </div>
    </div>
</template>

    <script>
    import fn from '@common/Fn.js'

    var cacheName = "SQL_PLAN";
    export default {
        data(){
            return {
                params:{
                    page:this.page,
                    fields:[],
                    operators:[]
                },
                where:[{
                      field: null,
                      table:'',
                      value:'',
                      operator:'=',
                      condition:'AND',
                      listWhere:[],
                      fieldString:''
                }],
                plan:[],
                planName:"",
                planNameFilter:"",
                isWhereShow:false,
                ifselectPlan:false,
                // 是否初始化,防止选择方案设置值时会触发change事件
                isInit: false,
                queryBtnStatus: false,
                operatorAll:[],
                operatorString: [],
                operatorDate: [],
                operatorNumber: [],
                operatorEnum: [],
                options: {
                    date:(function() {
                        var date = new Date();
                        var year = date.getFullYear();
                        var month = date.getMonth()+1;
                        var day = date.getDate();
                        var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
                        return new Date(time);
                    })(),
                },
                test:[],
                deleteStatus: false
            }
        },
        methods:{
            // 外面可以通过this.$refs.$xptList.$refs.xptSearchEx.filterFields对其重写，可以过滤不需要的选项
            filterFields (fields){
                return fields
            },
            // 用于调换where中条件的顺序
            watchwhere (where){
              return where
            },
            search(event, isFromSelectPlan){
                this.isWhereShow = false;
                var whereList = JSON.parse(JSON.stringify(this.where));
                var firstFieldString = whereList[0].fieldString;

                //去掉最后一项
                whereList.pop();

                //把搜索条件组装好格式直接传到页面
                whereList.forEach(whereVO => {
                    if(!whereVO.field) return true;
                    whereVO.table = whereVO.field.table;
                    if(whereVO.field.type == "Date" && whereVO.value){
                        whereVO.value = fn.dateFormat(new Date(whereVO.value.toString()),'yyyy-MM-dd hh:mm:ss');
                    }
                    if(whereVO.value && (whereVO.operator=="IN"||whereVO.operator=="BETWEEN"||whereVO.operator=="NOT IN"||whereVO.operator=="NOT BETWEEN")){
                        whereVO.value = JSON.stringify(whereVO.value.replace(/\s/g,"").split(/[,，]/));
                    }

                    if('IS NULL' == whereVO.operator) {
                        console.log('为空')
                        whereVO.value = '为空';
                    }
                    if('IS NOT NULL' == whereVO.operator) {
                        console.log('非空')
                        whereVO.value = '非空';
                    }
                    if('THIS MONTH' == whereVO.operator) {
                        console.log('本月')
                        whereVO.value = '本月';
                    }
                    if('THIS WEEK' == whereVO.operator) {
                        console.log('本周')
                        whereVO.value = '本周';
                    }
                    if('TODAY' == whereVO.operator) {
                        console.log('今天')
                        whereVO.value = '今天';
                    }
                    if('LAST MONTH' == whereVO.operator) {
                        console.log('上月')
                        whereVO.value = '上月';
                    }
                    if('LAST WEEK' == whereVO.operator) {
                        console.log('上周')
                        whereVO.value = '上周';
                    }
                    if('YESTERDAY' == whereVO.operator) {
                        console.log('昨天')
                        whereVO.value = '昨天';
                    }
                    whereVO.field = whereVO.field.field;
                    if(whereVO.hasOwnProperty('fieldString')){
                        delete whereVO.fieldString;
                    }
                });

                if(whereList.length == 1 && !firstFieldString){
                    whereList = [];
                }

                // 如果搜索条件只有时刻一项，把时刻扩展到天的时间段
                if ((1 === whereList.length) &&
                    (/^\d{4}-(0\d|1[0-2])-([0-2]\d|3[01])( ([01]\d|2[0-3])\:[0-5]\d\:[0-5]\d)$/.test(whereList[0].value)) &&
                    ('=' === whereList[0].operator)) {

                    var where = JSON.stringify(whereList[0]);
                    whereList.push(JSON.parse(where));

                    whereList[0].operator = '>=';
                    whereList[0].value = whereList[0].value.split(' ')[0] + ' 00:00:00';
                    whereList[1].operator = '<=';
                    whereList[1].value = whereList[1].value.split(' ')[0] + ' 23:59:59';
                }

                new Promise(resolve => {
                    this.queryBtnStatus = true;
                    this.click && this.click(whereList, resolve, isFromSelectPlan);
                }).then(() => {
                    this.queryBtnStatus = false;
                })
            },
            reset(){
                //this.where = [];
                console.log('看看是什么原因');
                this.callback && this.callback()
                this.planName = null;
                let i = this.where.length;
                if(!i) return;
                this.where[0].fieldString = null;
                this.where[0].value = ''
                // console.log(this.where[0].field.is_checkbox)

                // this.where[0].value = !!this.where[0].field.is_checkbox ? []:'';

                if(i == 1)return;
                for(var a = 1,b=1;a < this.where.length;a=b){
                    this.where.splice(a,1);
                    b = 1;
                }

            },
            //选择方案，显示搜索条件
            selectPlan(item){
                this.planName = item.name;
                this.where = [];
                this.isInit = true;
                let where = item.where,
                    i = where.length,
                newWhere = [];
                console.log('where++++',where)
                this.ifselectPlan = true;
                while(i--) {
                    if(where[i].field) {
                        let j = this.params.fields.length;
                        while(j--) {
                            if(where[i].field.comment === this.params.fields[j].comment) {

                                this.where.unshift({
                                    field: JSON.parse(JSON.stringify(this.params.fields[j])),
                                    operator: where[i].operator,
                                    value: where[i].value,
                                    fieldString:JSON.stringify(this.params.fields[j])
                                })
                            }
                        }
                    }
                }
                // console.log('where++++',this.where)

                this.$nextTick(() => {
                    this.search(null, true/*isFromSelectPlan*/);
                })
                setTimeout(() => {
                    this.isInit = false;
                }, 1000)
            },
            deletePlan(item){
                var pagesPlan = localStorage.getItem(cacheName) || "{}";
                try{pagesPlan = JSON.parse(pagesPlan)}catch(e){pagesPlan = {}}
                var pagePlan = pagesPlan[this.page] || {};
                let self = this;
                this.$root.eventHandle.$emit('openDialog', {
                    txt: '是否删除查询方案：' + item.name,
                    noCancelBtn: true,
                    okTxt: '是',
                    noTxt: '否',
                    ok(){
                        delete pagePlan[item.name];
                        self.submitPlan({
                            page_code: self.page,
                            query_json: JSON.stringify(pagePlan)
                        })
                    },
                    no(){}
                })
            },
            //保存在localStorage中
            savePlan(){
                if(!this.planName){
                    this.$message.error("请先填写方案名字");
                    return;
                }
                var pagesPlan = localStorage.getItem(cacheName) || "{}";
                try{pagesPlan = JSON.parse(pagesPlan)}catch(e){pagesPlan = {}}
                var pagePlan = pagesPlan[this.page] || {};
                let self = this;
                if(pagePlan[this.planName]) {
                    self.$root.eventHandle.$emit('openDialog', {
                        txt: '已存在相同的查询方案，是否覆盖',
                        noCancelBtn: true,
                        okTxt: '是',
                        noTxt: '否',
                        ok() {
                            pagePlan[self.planName].where = self.where;
                            self.submitPlan({
                                page_code: self.page,
                                query_json: JSON.stringify(pagePlan)
                            })
                        },
                        no() {}
                    })
                } else {
                    pagePlan[this.planName] = {
                        name: this.planName,
                        where: this.where
                    };
                    this.submitPlan({
                        page_code: this.page,
                        query_json: JSON.stringify(pagePlan)
                    })
                }
            },
            whereShow(show){
                if(typeof show == "undefined")  show = !this.isWhereShow;
                this.isWhereShow = show;
            },
            deleteWhere(index){
                this.deleteStatus = true
                this.where.splice(index,1);
            },
            ////////////////////////////////////////////////
            loadPlan(){
                var pagesPlan = localStorage.getItem(cacheName) || "{}";
                try{pagesPlan = JSON.parse(pagesPlan)}catch(e){pagesPlan = {}}
                var pagePlan = pagesPlan[this.page] || {};
                var plan = [];
                // console.log('+++++++',pagePlan)
                for(var key in pagePlan){
                    if(this.planNameFilter && pagePlan[key].name.indexOf(this.planNameFilter) ==-1 ){
                        continue;
                    }
                    plan.push(pagePlan[key]);
                }
                this.plan = plan;
            },
            // 搜索条件改变时，清空输入的值
            change(e) {
                let selectObj = JSON.parse(e);
                // let index = -1;
                // this.params.fields.forEach((item,indx)=>{
                //     if(item.field == selectObj.field){
                //         index = indx;
                //     }
                // })
                // // if(selectObj,index)
                // console.log(selectObj,index)

                // this.where[0].operator = this.params.operators[this.params.operators.length-1];
                // XPT-23598 单据列表查询组件优化 双十一 暂时更改批次订单列表的查询条件 后期释放
                let operatorString = [],  operatorEnum = [], operatorNumber = []
                // 是否需要去掉（解决代码冲突）
                let flag=false
                if(this.page=="scm_batch_trade"){
                    /**
                     说明：
                     * 运费类型  阶段付款状态  4PL接口状态  是否打印锁定 是否发货 是否出库 挂车模式 货审状态 财审状态 是否现货 是否可货审 订单业务类型
                     * /enum类型 去掉为空 非空两个条件
                    **/
                    if(selectObj.comment=="运费类型"||selectObj.comment=="送货方式"||selectObj.comment=="订单阶段付款状态"||selectObj.comment=="4PL接口状态"||selectObj.comment=="是否打印锁定"
                    ||selectObj.comment=="是否发货"||selectObj.comment=="是否出库"||selectObj.comment=="挂车模式"||selectObj.comment=="货审状态"||selectObj.comment=="财审状态"
                    ||selectObj.comment=="是否现货"||selectObj.comment=="是否可货审"||selectObj.comment=="订单业务类型"){
                        flag=true
                        this.operatorAll.forEach(elem=>{
                            if (/^(=|<>)$/.test(elem.operator)) {
                                operatorEnum.push(elem)
                            }
                                })
                            }
                            /**
                             说明：
                            * 发货批次  传送结果提示 合并单号 收货人手机号 地址 业务员分组
                            * /string类型 去掉为空 非空 包含三个条件
                            **/
                            else if(selectObj.comment=="发货批次"||selectObj.comment=="传送结果提示"
                            ||selectObj.comment=="合并单号"||selectObj.comment=="收货人手机号码"||selectObj.comment=="地址"||selectObj.comment=="业务员分组"){
                                flag=true
                                this.operatorAll.forEach(elem=>{
                                    if (/^(=|<>)$/.test(elem.operator)) {
                                        operatorString.push(elem)
                                    }
                                })
                            }
                            /**
                             说明：
                            * 主客户昵称  买家昵称  收货人姓名  业务员 货审锁定人 预货审人 推荐货审人 货审人
                            * /string类型 去掉为空 非空 两个条件
                            **/
                            else if(selectObj.comment=="主客户昵称"||selectObj.comment=="收货人姓名"||selectObj.comment=="业务员"
                            ||selectObj.comment=="货审锁定人"||selectObj.comment=="预货审人"||selectObj.comment=="推荐货审人"||selectObj.comment=="货审人"){
                                flag=true
                                this.operatorAll.forEach(elem=>{
                                    if (/^(=|<>|%|Q)$/.test(elem.operator)) {
                                        operatorString.push(elem)
                                    }
                                })

                            }
                            else if(selectObj.comment=="买家昵称"){
                                flag=true
                                this.operatorAll.forEach(elem=>{
                                    if (/^(=|Q)$/.test(elem.operator)) {
                                        operatorString.push(elem)
                                    }
                                })
                            }
                            /**
                             说明：
                            * 运费  三包费  总包件数  总货值 总标准售价 总体积
                            * /number类型 去掉为空 非空 两个条件
                            **/
                            else if(selectObj.comment=="运费"||selectObj.comment=="三包费"||selectObj.comment=="总包件数"||selectObj.comment=="总货值"
                            ||selectObj.comment=="总标准售价"||selectObj.comment=="总体积"){
                                flag=true
                                this.operatorAll.forEach(elem=>{
                                    if (/^(=|<>|>|>=|<|<=)$/.test(elem.operator)) {
                                        operatorNumber.push(elem)
                                    }
                                })

                            }
                            /**
                             说明：
                            * 合并订单ID
                            * /number类型 去掉为空 非空 包含 三个条件
                            **/
                            else if(selectObj.comment=="合并订单id"){
                                flag=true
                                this.operatorAll.forEach(elem=>{
                                    if (/^(=|<>|>|>=|<|<=)$/.test(elem.operator)) {
                                        operatorNumber.push(elem)
                                    }
                                })

                            }
                            /**
                             说明：
                            * 批次订单编号
                            * /string类型 去掉为空 非空 包含 不等于四个条件
                            **/
                            else if(selectObj.comment=="批次订单编号"){
                                flag=true
                                this.operatorAll.forEach(elem=>{
                                    if (/^(=)$/.test(elem.operator)) {
                                        operatorString.push(elem)
                                    }
                                })
                                /**
                                 * XPT-23529 批次单列表新增过滤字段=>批次订单新增过滤字段：进仓编号，条件：等于
                                 **/
                            }else if(selectObj.comment=="进仓编号"){
                                flag=true
                                this.operatorAll.forEach(elem=>{
                                    if (/^(=)$/.test(elem.operator)) {
                                        operatorString.push(elem)
                                    }
                                })
                            }else{
                                flag=false
                            }
                    }

                if(this.page=="bd_customer"){

                    //新平台客户列表查询条件调整：1.客户名称字段，只保留等于，包含（快）2.客户编码，只保留等于3.客户类型，只保留等于

                    if(selectObj.comment=="客户名称"){
                        flag=true;
                        this.operatorAll.forEach(elem=>{
                            if (/^(=|Q)$/.test(elem.operator)) {
                                operatorString.push(elem)
                            }
                        })
                    }else if(selectObj.comment=="客户编码"){
                        flag=true;
                        this.operatorAll.forEach(elem=>{
                            if (/^(=)$/.test(elem.operator)) {
                                operatorString.push(elem)
                            }
                        })
                    }else if(selectObj.comment=="客户类型"){
                        flag=true;
                        this.operatorAll.forEach(elem=>{
                            if (/^(=)$/.test(elem.operator)) {
                                operatorEnum.push(elem)
                            }
                        })
                    }
                }

                if (!!selectObj) {
                    if (selectObj.type == 'Date') {
                        this.params.operators =  this.operatorDate;
                    } else if (selectObj.type == 'String') {
                        this.params.operators =  flag?operatorString:this.operatorString;
                    } else if (selectObj.type == 'Number') {
                        this.params.operators =  flag?operatorNumber:this.operatorNumber;
                    } else if (selectObj.type == 'Enum') {
                        if(selectObj.is_checkbox){
                            this.params.operators = [{operator: "MULTI", desc: "多选"}];
                        }else{
                            this.params.operators =  flag?operatorEnum:this.operatorEnum;
                        }
                    }
                }
                // 承林发票、客户开票申请选单弹框条件全部是等于
                if(this.page=="scm_batch_invoice"){
                    this.params.operators=[
                        {desc: "等于",operator: "="}
                    ]
                }
                // console.log(this.where[0])
                // if(this.where[0].operator == 'THIS MONTH' || this.where[0].operator == 'THIS WEEK' || this.where[0].operator == 'TODAY' || this.where[0].operator == 'LAST MONTH' || this.where[0].operator == 'LAST WEEK' || this.where[0].operator == 'YESTERDAY'){
                //     // this.where[0].operator = this.params.operators[0].operator;
                //     if(this.where[0].field.type !='"Date"'){
                //         this.where[0].operator = this.params.operators[0].operator;
                //     }
                // }
                    // this.where[0].operator = null;
                // console.log(this.isInit,111111)
                if(!this.isInit) {
                    this.where[0].value = '';
                    this.where[0].operator = '=';
                }
                // console.log(this.isInit,'this.isInit',selectObj)
                if(selectObj&&selectObj.is_checkbox && !this.isInit){
                    this.where[0].value = [];
                    this.where[0].operator = 'MULTI';
                }
                this.stringIntoJson(this.where[0]);

            },
            itemChange(index,item) {
               console.log(item,'试试看的东西',this.params);
               let selectObj = {
                   is_checkbox:false
               }
               if(!!item.fieldString){
                 selectObj = JSON.parse(item.fieldString);

               }


                let self = this;
                // if(selectObj.is_checkbox){
                //     this.where[index].value = [];
                //     // this.where[index].value = 'MULTI';
                // }else {
                //     this.where[index].value = '';
                //     // this.where[index].operator = '=';
                // }
                if(!this.isInit &&selectObj.is_checkbox){
                    this.where[index].value = [];
                    this.where[index].operator = 'MULTI';
                    // this.where[index].value = 'MULTI';
                }else if(!this.isInit && !this.deleteStatus) {
                    this.where[index].value = null;
                    this.where[index].operator = '=';
                }

                // console.log(this.where[index].value,'this.where[index].value')

                // console.log(this.where[index]);
                // this.where[index].operator = this.params.operators[0].operator;
                // console.log('this.params.fields1',JSON.parse(JSON.stringify(this.params.fields)));

                this.stringIntoJson(this.where[index]);
                if(this.ifselectPlan != true){
                    // this.$set(this.where[index],'operator',this.params.operators[0].operator)
                    // this.ifselectPlan = false;
                }else if(index>=this.where.length){
                    this.ifselectPlan = false;
                }
                if (this.deleteStatus && index === (this.where.length - 1)) {
                    this.deleteStatus = false
                } else {
                    this.deleteStatus = true
                }
               self.$nextTick(()=>{
                    self.$forceUpdate();
                })

                // if(this.where[index].operator == 'THIS MONTH' || this.where[index].operator == 'THIS WEEK' || this.where[index].operator == 'TODAY' || this.where[index].operator == 'LAST MONTH' || this.where[index].operator == 'LAST WEEK' || this.where[index].operator == 'YESTERDAY'){
                //     // this.where[0].operator = this.params.operators[0].operator;
                //     if(this.where[index].field.type !='"Date"'){
                //         this.where[index].operator = this.params.operators[0].operator;
                //     }
                // }
                //if(index == 0) return;
                /*var data = this.where[index].fieldString;
                if(!data) return;
                data = JSON.parse(data);
                !this.where[index].field?this.where[index].field={}:'';
                for(var key in data){
                    this.where[index].field[key] = data[key];
                }*/
                //
                // console.log('this.where2',JSON.parse(JSON.stringify(this.where)));
                // console.log('this.params.fields2',JSON.parse(JSON.stringify(this.params.fields)));

            },
            /**
            *string
            ***/
            stringIntoJson(where){
                if(!where || !where.fieldString) return;
                var data = where.fieldString;
                if(!data) return;
                data = JSON.parse(data);
                !where.field?where.field={}:'';
                for(var key in data){
                    where.field[key] = data[key];
                }
            },


            submitPlan(data) {
                if(!data) return;
                data.user_id = fn.getUserInfo('id');
                console.log(data)
                this.ajax.postStream('/user-web/api/sql/saveOrUpdateQueryPlan', data, res => {
                    // 加载成功后重新获取保存的方案
                    if(res.body.result) {
                        this.$message.success('保存成功');
                        this.queryPlan()
                    } else {
                        this.$message.error(res.body.msg || '')
                    }
                }, err => {
                    this.$message.error(err);
                })
            },
            queryPlan() {
                let data = {
                    user_id: fn.getUserInfo('id')
                }
                if(!data.user_id) return;
                this.ajax.postStream('/user-web/api/sql/listQueryPlan', data, res => {
                    if(res.body.result) {
                        let list = res.body.content,
                            i = list.length,
                            sdata = {};
                        while(i--) {
                            sdata[list[i].page_code] = JSON.parse(list[i].query_json)
                        }
                        localStorage.setItem('SQL_PLAN', JSON.stringify(sdata));
                        this.loadPlan();
                    }
                })
            }
        },
        props:{
            callback: {
                type: Function
            },
            page:{
                type:String,
                default(){
                    return "";
                }
            },
            click:{
                type:Function
            }
        },
        watch:{
            planNameFilter(){
                this.loadPlan();
            },
            where:{
                handler(curVal, oldVal) {
                    if (curVal && '[object Array]'===Object.prototype.toString.call(curVal)) {
                        var len = curVal.length;
                        while(len--) {
                            switch(curVal[len].operator) {
                                case 'IS NULL':
                                    // 为空
                                    this.where[len].value = '为空';
                                    break;

                                case 'IS NOT NULL':
                                    // 非空
                                    this.where[len].value = '非空';
                                    break;

                                case 'THIS MONTH':
                                    // 本月
                                    this.where[len].value = '本月';
                                    break;

                                 case 'THIS WEEK':
                                    // 本周
                                    this.where[len].value = '本周';
                                    break;
                                case 'TODAY':
                                    // 今天
                                    this.where[len].value = '今天';
                                    break;
                                case 'LAST MONTH':
                                    // 上月
                                    this.where[len].value = '上月';
                                    break;
                                case 'LAST MONTH':
                                    // 上月
                                    this.where[len].value = '上月';
                                    break;
                                case 'LAST WEEK':
                                    // 上周
                                    this.where[len].value = '上周';
                                    break;
                                case 'YESTERDAY':
                                    // 昨天
                                    this.where[len].value = '昨天';
                                    break;
                                default:
                                    if ('为空'===this.where[len].value||this.where[len].value==='非空') {
                                        this.where[len].value = null;
                                    }
                            }

                        }
                    }
                    // console.log(curVal);
                    var row = this.where;

                    if(row.length != 0) {
                        var last = row[row.length - 1];
                        if(!last.field) {
                            if(row.length == 1){
                                return;
                            }
                            if(!row[row.length -2].field){
                                //删掉最后一行
                                row.pop();
                            }
                            return;
                        }
                    }

                    this.where.push({
                        field: null,
                        table:'',
                        value:'',
                        operator:'=',
                        condition:'AND',
                        listWhere:[],
                        fieldString:''
                    });
                },
                deep:true
            },
            /**
            *初使
            ***/
        },
        mounted(){
            this.ajax.postStream("/user-web/api/sql/listFields",{page:this.page},res => {
                this.params.fields = this.filterFields(res.body.content.fields);
                this.operatorAll = [];
                this.operatorAll = res.body.content.operators;
                /**操作符分类
                 *  字符型： 等于、不等于、包含慢、包含快，为空、非空
                    日期型： 等于、大于、大于等于、小于、小于等于,、本月、本周、上月、今天、昨天、上周、，为空、非空
                    枚举型： 等于、不等于，为空、非空
                    数值型：等于、不等于、大于、大于等于、小于、小于等于，为空、非空
                 */
                let operatorString = [], operatorDate = [], operatorEnum = [], operatorNumber = []
                res.body.content.operators.forEach(item =>{
                    if (/^(=|<>|%|Q|IS NULL|IS NOT NULL)$/.test(item.operator)) {
                        operatorString.push(item)
                    }
                    if (/^(=|>|>=|<|<=|THIS MONTH|THIS WEEK|LAST MONTH|TODAY|YESTERDAY|LAST WEEK|IS NULL|IS NOT NULL)$/.test(item.operator)) {
                        operatorDate.push(item)
                    }
                    if (/^(=|<>|IS NULL|IS NOT NULL)$/.test(item.operator)) {
                        operatorEnum.push(item)
                    }
                    if (/^(=|<>|>|>=|<|<=|IS NULL|IS NOT NULL)$/.test(item.operator)) {
                        operatorNumber.push(item)
                    }
                })

                if(this.page=="scm_sample_order"){
                    operatorString=[]
                    operatorNumber=[]
                    res.body.content.operators.forEach(item =>{
                        if (/^(=|Q)$/.test(item.operator)) {
                            operatorString.push(item)
                        }
                        if (/^(=|>|>=|<|<=|IS NULL|IS NOT NULL)$/.test(item.operator)) {
                            operatorNumber.push(item)
                        }
                    })
                }

                if(this.page=='cloud_call_stat_new'){
                  operatorString=[]
                  operatorEnum=[]
                  operatorNumber=[]
                  res.body.content.operators.forEach(item =>{
                        if (/^(=)$/.test(item.operator)) {
                            operatorString.push(item)
                            operatorEnum.push(item)
                            operatorNumber.push(item)
                        }
                    })
                }

                this.operatorString = operatorString
                this.operatorDate = operatorDate
                this.operatorNumber = operatorNumber
                this.operatorEnum = operatorEnum
                this.params.operators =  this.operatorAll;
                // 设置默认值
                let search_default = __SEARCH_DEFAULT[this.page] || {},
                    i = this.params.fields.length,
                    // 是否已经设置了默认值
                    unSet = true;
                // 设置搜索字段
                while(search_default.comment && i--) {
                    if(this.params.fields[i].comment == search_default.comment) {
                        this.where[0].field = JSON.parse(JSON.stringify(this.params.fields[i]));
                        if (this.params.fields[i].type == 'Date') {
                            this.params.operators =  this.operatorDate;
                        } else if (this.params.fields[i].type == 'String') {
                            this.params.operators =  this.operatorString;
                        } else if (this.params.fields[i].type == 'Number') {
                            this.params.operators =  this.operatorNumber;
                        } else if (this.params.fields[i].type == 'Enum') {
                            this.params.operators =  this.operatorEnum;
                        }
                        unSet = false;
                        break;
                    }
                }
                unSet && (this.where[0].field = JSON.parse(JSON.stringify(this.params.fields[0])));
                // 设置搜索条件
                i = this.params.operators.length;
                unSet = true;
                while(search_default.desc && i--) {
                    if(this.params.operators[i].desc == search_default.desc) {
                        this.where[0].operator = this.params.operators[i].operator;
                        unSet = false;
                        break;
                    }
                }
                if(this.where[0].field){
                    this.where[0].fieldString = JSON.stringify(this.where[0].field);
                }
                unSet && (this.where[0].operator = this.params.operators[0].operator);
            });

            this.loadPlan();
        }
    }
    </script>

    <style lang="css">
      .w99{width: 80px;}
      .el-input__icon {
        width: 20px
      }
      .el-input__icon+.el-input__inner {
          padding-right: 20px;
      }
      .v-middle{vertical-align: middle;}
      .rela{
        position: relative;
        width:530px;
        float: right;
      }
      .rela > div{
        font-size: 0 !important;
      }
      .where-show-panel{
        position: absolute;
        z-index: 8;
        width: 660px;
        right: 0;
        top:30px;
        background: #FFF;
        border: 1px solid #dfe6ec;
        border-radius: 5px;
        padding: 0 5px;
       /* max-height: 300px*/
      }
      .where-show-panel:after,.where-show-panel:before{
        content:"";
        display:table;
      }
      .cursor-pointer{
        cursor:pointer;
      }
      .where-show-panel ul{
        /*max-height: 250px;*/
        overflow-y: auto;
        float: left
      }
      .where-show-panel li{
        margin:5px 0;
        font-size: 0 !important;
      }
      .where-show-panel .save{
        height: 24px;
        line-height: 24px;
      }
      .where-show-panel .save .el-input{
        width: 283px;
      }
      .where-show-panel .save .el-button{
        vertical-align: top;
        margin-top: 2px;
      }
      .plan-show-panel{
        float: right;
        width: 230px;
      }
      .plan-show-panel>div{
        padding-bottom: 3px;
        margin: 5px 10px;
        border-bottom: 1px solid #dfe6ec;
      }
      .plan-show-panel>ul{
        padding:5px;
        text-align: left;
        max-height: 250px;
        overflow-y: auto;
      }
      .plan-show-panel>ul>li{
        display: inline-block;
        border: 1px solid #dfe6ec;
        padding:2px 10px;
        border-radius: 2px;
        cursor: pointer;
        margin-bottom: 5px;
        position: relative;
        border-radius: 5px;
        margin:5px;
        height: 30px;
      }
      .plan-show-panel>ul>li i{
        position: absolute;
        top: -5px;
        right: -5px;
      }
      .plan-show-panel .el-input {
        width: 100%;
      }
      .plan-show-panel .el-input input, .plan-show-panel button {
        border: none
      }
      .multiple-select .el-tag{
            overflow: hidden !important;
		    height: 24px !important;
      }
      .multiple-select .el-input__inner{
		    height: 22px !important;

      }
      .multiple-select .el-select__tags{
        height: 24px;
        overflow: hidden;
      }
    </style>

