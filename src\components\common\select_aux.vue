<!-- 辅助资料--下拉列表 -->
<template>
	<el-select v-model='model' @visible-change='visibleChange' size='mini' @change='change' :placeholder='placeholder' :popper-class="popperClass" :clearable='clearable' :disabled='disabled' :multiple="multiple" :class="{ 'is-multiple': multiple }">
		<slot name='option'></slot>
		<el-option
			v-for="(obj,index) in list"
			:key="index"
			:label="obj.name"
			:value="obj.code"
			:data-code="obj.code"
			:disabled='isDisabled(obj)'>
      <template v-if="showRemark && obj.remark">
        <el-tooltip :content="obj.remark" placement="right-start">
          <div v-if="showcode">{{obj.name}}/{{obj.code}}</div>
          <div v-else>{{obj.name}}</div>
        </el-tooltip>
      </template>
			<template v-if='showcode'>
				{{obj.name}}/{{obj.code}}
			</template>
		</el-option>
	</el-select>
</template>
<script>
export default {
	props: {
		// 辅助资料名
		aux_name: String,
		// 绑定的值
		value: {
			default: ''
		},
		// 禁用状态
		disabled: Boolean,
		// 可清空单选
		clearable: Boolean,
		// 占位符
		placeholder: {
			type: String,
			default() {
				return '请选择'
			}
		},
		popperClass: String,
		// 是否显示编码
		showcode: {
			type: Boolean,
			default: false
		},
    showRemark: {
			type: Boolean,
			default: false
		},
		// 禁用选项,{key:value}选用哪个值去禁用
		disabledOption: {
			type: Array,
			default() {
				return [];
			}
		},
    multiple: {
      type: Boolean,
      default: false,
    },
	},
	data() {
		return {
			list: __AUX.get(this.aux_name),
			model: this.value,
			// 下拉列表展开收起定时器
			timer: 0
		}
	},
	methods: {
		visibleChange(arg) {
			clearTimeout(this.timer)
			if(arg) {
				this.list = __AUX.getValidData(this.aux_name)
			} else {
				// 收起下拉列表，延时处理
				this.timer = setTimeout(() => {
					this.list = __AUX.get(this.aux_name)
				}, 200)
			}
			this.$emit('visible-change', arg)
		},
		change() {
			var targetObj

      if (this.multiple) {
        if (Array.isArray(this.model)) {
          targetObj = this.list.reduce((result, curItem) => {
            if (this.model.includes(curItem.code)) {
              result.push(curItem);
            }
            return result;
          }, []);
        }
      } else {
        this.list.some(obj => {
				if(obj.code === this.model){
					targetObj = obj
					return true
				}
			})
      }

			// 这儿必须用input 发送数据，发送的数据会被父级v-model=“test”接受到，再被value=test传回来。
			this.$emit('input', this.model)
			this.$emit('change', this.model, targetObj)
		},
		isDisabled(/*val*/obj) {
			/*let set = new Set(this.disabledOption);
			if(set.has(val)) return true;
			return false;*/
			if(!this.disabledOption || !this.disabledOption.length) return false;
			let length = this.disabledOption.length;
			//{key:val}

			for(var i = 0;i < length;i++){
				let a = this.disabledOption[i];
				for(var key in a){
					if(obj[key] == a[key]){
						return true;
					}
				}
			}
			return false;
		}
	},
	mounted() {
		this.$nextTick(() => {
			this.model = this.value
		})
	},
	watch: {
		value(n, o) {
			this.$nextTick(() => {
				this.model = n
			})
		}
	}
}
</script>

<style scoped lang="stylus">
.el-select.is-multiple {
  :global(.el-select__tags){
		height: initial;
    overflow: initial;
	}
  :global(.el-input.el-input--mini) {
    height: initial !important;
  }
}
</style>
