<template>
  <div class="table">
    <el-table
    v-loading="oneLoading"
    @cell-click="getDLdata"
    @row-dblclick="selectTableRow"
    :data="tableOneData.data"
    border style="width: 100%;min-height:150px;"
    :row-style="markType"
    :highlight-current-row="isHigh">
      <el-table-column :show-overflow-tooltip="true" prop="jobNumber" label="呼叫编码" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="calledNumber" label="呼叫电话" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="areaCode" label="区号" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="jobType" label="服务类型" width="300"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="callingType" label="来电类型" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="mergeTradeId" label="合并订单Id" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="mergeTradeNo" label="合并单号" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="custId" label="客户Id" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="custName" label="买家昵称" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="shopName" label="店铺" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="salesUserName" label="客服" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="salesGroupName" label="客服分组" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="receiverName" label="收货人" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="receiverPhone" label="收货人电话" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="operatedUserName" label="处理人" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="operatedGroupName" label="处理部门" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="orderOwnerType" label="归属" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="markRemark" label="标记备注" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="markedUserName" label="标记人" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="sourceOrderId" label="来源单据ID" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="sourceOrderNo" label="来源单据编号" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="approvedTime" label="审核时间" width="150"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="approvedUserName" label="审核人" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="creatorName" label="创建人姓名" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="createTime" label="创建时间" width="150">
      <template slot-scope="scope">
        <span  v-html="fomterTime(scope.row.createTime)"></span>
      </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="modifierName" label="更新姓名" width="120"></el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="modifyTime" label="更新时间" width="150">
        <template slot-scope="scope">
          <span  v-html="fomterTime(scope.row.modifyTime)"></span>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" fixed="right" label="操作" width="150">

        <template slot-scope="props">
            <el-dropdown trigger="click"  @command="handleCommand" style="margin-left: 10px">
              <span class="el-dropdown-link">
                标记<i class="el-icon-caret-bottom el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="['URGENCY', props.row.jobId]" style="color: #ff4949;">红色</el-dropdown-item>
                <el-dropdown-item :command="['PROCESS_TODAY', props.row.jobId]" style="color: #f7ba2a;">黄色</el-dropdown-item>
                <el-dropdown-item :command="['NEED_FOLOW', props.row.jobId]" style="color: #13ce66;">绿色</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div class="page">
      <el-pagination v-if="tableOneData.data.length > 0"
        layout="prev, pager, next"
        :total="this.tableOneData.totalNum"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  props: {
    tableOneData: {
      type: Object,
      default: {}
    },
    oneLoading: {
      type: Boolean,
      default: false
    }
  },
  components: {
  },
  data () {
    return {
      tableData: [],
      markColor: {
        URGENCY: '#ff4949',
        PROCESS_TODAY: '#f7ba2a',
        NEED_FOLOW: '#13ce66'
      },
      isHigh: false
    }
  },
  methods: {
    // 分页页数改变
    handleCurrentChange (val) {
      this.$emit('pageReload', val)
    },
    // 双击
    selectTableRow (row, event) {
      this.$emit('changeWorkDialog', row)
    },
    // 单击选中
    getDLdata (row, column, cell, event) {
      if (column.label === '操作') return
      this.isHigh = true
      this.$emit('reloadDLtable', row.jobId)
      this.$emit('check', row.jobId)
    },
    rowClassName (row, index) {
      return {'background': '#900'}
    },
    // 标记
    handleCommand (data) {
      console.log(data)
      let params = {
        jobId: data[1],
        markType: data[0],
        markRemark: '',
        actionType: 'markType'
      }
      this.$emit('tableOneAction', params)
    },
    // 设置颜色
    markType (row, index) {
      if (row.markType) {
        return `background-color:${this.markColor[row.markType]};`
      } else {
        return ''
      }
    },
    // 格式化时间
    fomterTime (date) {
      return utils.fomterTime(date)
    }
  },
  watch: {
    tableOneData (val) {
      this.tableOneData = val
    }
  }
}
</script>

<style scoped>
  .table{
    display:block;
    width:100%;
    /*height:260px;*/
    margin-bottom:20px;
  }
  .page{
    margin-top:10px;
  }
  .developing{
    background-color:red;
  }
  .el-dropdown-link{
    color: #20a0ff;
    font-size: 12px;
    cursor: pointer;
  }
  .no-margin{
    margin:0;
  }
</style>
<style>
  .el-table__body tr.current-row>td{
    background-color: #20a0ff!important;
  }
</style>
