<template>
  <el-table
    v-loading="threeLoading"
    :data="tableData"
    border
    style="width: 100%;min-height:100px;">
    <el-table-column :show-overflow-tooltip="true" prop="operatedUserName" label="用户名" ></el-table-column>
    <el-table-column :show-overflow-tooltip="true" prop="operationTypeName" label="操作类型" ></el-table-column>
    <el-table-column :show-overflow-tooltip="true" prop="createTime" label="创建时间"></el-table-column>
  </el-table>
</template>

<script>
import baseUrl, {makeUrl,apiUrl}from '../call_system/base.js'
export default {
  props: {
    jobId:{
      type:Number,
      default:()=>null
    }
  },
  data () {
    return {
      tableData: [],
      threeLoading:false
    }
  },
  computed: {
  },
  methods:{
    search(){
      if(null == this.jobId){
        return
      }

      this.threeLoading = true

       this.$http.get(makeUrl(apiUrl.workOrder_operateList, {jobId: this.jobId})).then((res) => {
         if (res.data&&res.data.code == 0) {
           this.tableData = res.data.content
         }
         else {
           this.$message.error(res.data.msg);
           (body.msg)
         }

      }).catch(err => {
          this.$message.success(body.msg)
      }).finally(()=>{
        this.threeLoading = false
      })
    },
    clear(){
      this.tableData = []
    }
  },
  mounted() {
    this.search()
  }
}
</script>

<style scoped>
</style>
