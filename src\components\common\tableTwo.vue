<template>
  <el-table
    v-loading="twoLoading"
    @row-click="selectionChange"
    :data="tableData"
    border
    :highlight-current-row="true"
    style="width: 100%;height:100%">
    <el-table-column :show-overflow-tooltip="true" type="index" label="序号"></el-table-column>
    <el-table-column :show-overflow-tooltip="true" prop="calledType" label="呼叫类型"></el-table-column>
    <el-table-column :show-overflow-tooltip="true" prop="staffName" label="处理人"></el-table-column>
    <el-table-column :show-overflow-tooltip="true" prop="staffNo" label="工号"></el-table-column>
    <el-table-column prop="remark" label="处理内容">
      <template slot-scope="scope">
        <el-popover trigger="hover" placement="top" popper-class="table-two-popperContent">
          <p>{{ scope.row.remark }}</p>
          <div slot="reference" class="name-wrapper">
            {{ scope.row.remark}}
          </div>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column :show-overflow-tooltip="true" prop="processTime" label="处理时间"></el-table-column>
    <el-table-column :show-overflow-tooltip="true" prop="recordStartTime" label="开始录音时间"></el-table-column>
    <el-table-column :show-overflow-tooltip="true" prop="recordEndTime" label="结束录音时间"></el-table-column>
    <el-table-column label="录音">
      <template slot-scope="scope">
        <a v-if="scope.row && scope.row.recordFile" target="_blank" :href="scope.row.recordFile">播放</a>
      </template>

    </el-table-column>
  </el-table>
</template>

<script>
  import baseUrl, {makeUrl} from '../call_system/base.js';
  import {apiUrl} from '../call_system/base';

  export default {
    props: {
      jobId: {
        type: Number,
        default: () => null,
      },
    },
    data() {
      return {
        twoLoading: false,
        row: null,
        tableData: [],
        callId: null,
      };
    },
    methods: {
      search(jobId) {
        if (null == this.jobId && !jobId) {
          return;
        }

        let params = {jobId: jobId ? jobId : this.jobId};
        this.twoLoading = true;
        this.ajax.get(makeUrl(apiUrl.workOrderProcessLog_list, params), res => {
          this.tableData = res.data.content;
          this.twoLoading = false;
          this.callId = null;
        }, err => {
          this.$message.success(res.data.msg);
          this.twoLoading = false;
          this.callId = null;
        });
      },
      clear() {
        this.tableData = [];
        this.callId = null;
      },
      selectionChange(row) {
        this.callId = row.callId;
        this.row = row;
      },
      deleteData() {
        if (null == this.callId) {
          this.$message.error('请选择删除的条目');
          return;
        }
        this.$confirm('是否确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.twoLoading = true;
          this.$http.get(makeUrl(apiUrl.workOrder_delete, {processId: this.row.processId})).then(
              res => {
                this.$message.success('删除成功');
                this.search();
              },
          ).catch(err => {
            this.$message.error(err);
          }).finally(() => {
            this.twoLoading = false;
          });
        })

      },
    },
    computed: {},
    mounted() {
      this.search();
    },
  };
</script>
<style>
  .table-two-popperContent{
    max-width: 450px;
  }
  .table-two-popperContent>p{
    line-height: 20px;
    font-size: 13px !important;
  }
</style>
<style scoped>
  .table {
  }
</style>
