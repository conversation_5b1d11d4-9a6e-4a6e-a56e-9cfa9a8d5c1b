<template>
  <div class="tabs-body">
    <div class="select-tab">
        <span v-for='(item,index) in tabs' :key="index"
        @click="selectedTabs(index)"
        :class="{'active': (currentClass === index)}">{{item.name}}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tabs: {
      type: Array,
      default: []
    },
    index: {
      type: Number,
      default: 0
    },
    currentClass: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {}
  },
  methods: {
    selectedTabs (index) {
      this.$emit('selectedTabs', index)
    }
  }
}
</script>

<style scoped>
.select-tab {
  position: relative;
  z-index: 99;
  top: 1px;
}
.select-tab span{
  height:26px;
  line-height:26px;
  padding:0 10px;
  display:inline-block;
  cursor: pointer;
  border-bottom:solid transparent 2px;
}
.select-tab span.active{
  color: #20a0ff;
  border-bottom: solid #20a0ff 2px;
}
</style>
