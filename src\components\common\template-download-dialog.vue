<template>
  <div>
    <div class="item-box">
      <span>导入模板：</span>
      <el-button size="mini" type="info" @click="downloadTemplate()"
        >下载</el-button
      >
    </div>
    <div class="item-box">
      <span>上传导入文件：</span>
      <isAutomaticImportUpload
        :taskUrl="params.uploadUrl"
        :otherParams="params.otherParams"
        :callback="uploadCallback"
        class="mgl10"
        :isupload="params.isupload || getStatus"
        :inputId="params.inputId"
        :text="text"
        :callbackText="callbackText"
        ref="isAutomaticImportUploadRef"
      />
    </div>
    <div class="item-box" v-if="params.otherParams.ifAppointmentActivity">
      <span
        >导入模式：<el-tooltip
          class="if_offline_and_online_linkage_tooltip"
          placement="right"
        >
          <p slot="content">
            1、覆盖模式：删除掉之前的实施店铺，使用新文件中的店铺
            <br />
            2、新增模式：文件中的店铺编码已存在，则不会导入；如果不存在，则会新增
          </p>
          <i class="el-icon-warning"></i> </el-tooltip
      ></span>
      <el-radio-group v-model="params.otherParams.importMode" class="mgl10">
        <el-radio :label="0" size="mini">覆盖模式</el-radio>
        <el-radio :label="1" size="mini">新增模式</el-radio>
      </el-radio-group>
    </div>
    <el-button size="small" type="info" @click="close" class="close-button"
      >确定</el-button
    >
  </div>
</template>

<script>
import isAutomaticImportUpload from "@/components/common/isAutomaticImportUpload";
export default {
  props: ["params"], //上游参数
  components: {
    isAutomaticImportUpload,
  },
  data() {
    return {
      text: "选择文件",
    };
  },
  computed: {
    getStatus() {
      let { ifAppointmentActivity, importMode } = this.params.otherParams;
      if (!ifAppointmentActivity) return;
      if (!importMode && typeof importMode != "undefined" && importMode != 0)
        return true;
    },
  },
  methods: {
    //模板下载
    downloadTemplate() {
      let data = {
        page_name: "template_download_list",
        where: [
          {
            field: "1e08dba31f3647691c61c2030bd8e632",
            table: "d331c120edc028bbbc83230f04b409fa",
            value: this.params.filename,
            operator: "=",
            condition: "AND",
            listWhere: [],
          },
        ],
        page_size: 50,
        page_no: 1,
      };
      this.ajax.postStream(
        "/reports-web/api/template/list",
        data,
        (response) => {
          if (response.body.result && response.body.content.count != 0) {
            this.download(response.body.content.list[0].url);
          } else {
            self.$message.error("未找到对应模板，请到模板下载列表维护");
          }
        },
        (e) => {
          self.$message.error(e);
        }
      );
    },
    //命名转换中文
    download(url) {
      if (!fetch) {
        window.location.href = url;
        return;
      }
      return fetch(url).then((res) => {
        res.blob().then((blob) => {
          let a = document.createElement("a");
          let url = window.URL.createObjectURL(blob);
          a.href = url;
          a.download = this.params.filename;
          a.click();
          window.URL.revokeObjectURL(url);
        });
      });
    },

    close() {
      this.$refs.isAutomaticImportUploadRef.expIn();
    },
    uploadCallback() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    callbackText() {
      this.text = "替换文件";
    },
  },
};
</script>

<style scoped>
.close-button {
  position: absolute;
  right: 15px;
  bottom: 15px;
}
.item-box {
  margin: 20px 0;
}
.if_offline_and_online_linkage_tooltip {
  color: #909399 !important;
}
</style>