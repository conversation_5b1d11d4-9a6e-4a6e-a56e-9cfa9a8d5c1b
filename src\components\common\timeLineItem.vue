<template>
    <div v-if="isDot" class="dot" :style="{color:onStatus?'#13ce66': (isActive? activeColor : defaultColor)}">●●●
      <span class="line" :style="{backgroundColor: (isActive? activeColor : defaultColor)}" ></span>
    </div>
    <div v-else>
      <div class="item" :style="{color:onStatus?'#13ce66': (isActive? activeColor : defaultColor)}">
          <div class="middle">
              <div class="circle" :style="{backgroundColor: (onStatus?'#13ce66':isActive? activeColor : defaultColor)}">{{tampText}}</div>
              <div class="line" v-show="isLast" :style="{backgroundColor: (isActive? activeColor : defaultColor)}"></div>
          </div>
          <div class="content">{{content}}</div>

          <div class="timestamp" v-if="!hideTimestamp">{{timestamp}}</div>
      </div>
  </div>
    
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    // 文字
    content: {
      type: String,
    },
    // 时间节点
    timestamp: {
      type: String,
    },
    // 是否隐藏时间节点
    hideTimestamp: {
      type: Boolean,
      default: false
    },
    // 是否...
    isDot: {
      type: Boolean,
      default: false
    },
    // 节点内文字
    tampText: {
      type: String,
      default: ''
    },
    // 当前状态
    onStatus: {
      type: Boolean,
      default: false
    },
    // 是否为激活状态
    isActive: {
      type: Boolean,
      default: false
    },
    isLast:{
      type: Boolean,
      default: false
    },
    // 激活状态文字颜色
    activeColor: {
      type: String,
      default: '#409eff'
    },
    // 未激活状态文字颜色
    defaultColor: {
      type: String,
      default: '#ccc'
    },
  },
  mounted() {

  }
};
</script>

<style scoped>
.dot{
    margin-right: 20px;
    position: relative;
}
.item {
    display: flex;
    flex-direction: column;
    /* min-width: 100px; */
    width: 100%;
}
.item .content,.timestamp {
    text-align: left;
    font-size: 14px;
    padding-right: 15px;
}
.item .middle {
    height: 20px;
    display: inline-flex;
    align-items: center;
    padding: 8px 0;
}
.item .middle .circle {
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 10px;
    color: #fff;
}

.item .middle .line {
    height: 2px;
    width: calc(100% - 20px);
}
.dot .line {
    height: 2px;
    width: calc(100% - 6px);
    display: inline-block;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 26px;
}
.onStatus{
  background-color:#13ce66 !important;
}
</style>