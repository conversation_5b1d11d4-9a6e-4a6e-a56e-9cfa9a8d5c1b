<template>
  <div class="el-tree-node"
    @click.stop="handleClick"
    v-show="node.visible"
    :class="{
      'is-expanded': childNodeRendered && expanded,
      'is-current': tree.store.currentNode === node,
      'is-hidden': !node.visible
    }">
    <div class="el-tree-node__content"
      :style="{ 'padding-left': (node.level - 1) * tree.indent + 'px' }">
      <span
        class="el-tree-node__expand-icon"
        @click.stop="handleExpandIconClick"
        :class="{ 'is-leaf': node.isLeaf, expanded: !node.isLeaf && expanded }">
      </span>
      <el-checkbox
        v-if="showCheckbox"
        v-model="node.checked"
        :indeterminate="node.indeterminate"
        @change="handleCheckChange"
        @click.native.stop="handleUserClick(node.data)">
      </el-checkbox>
      <span
        v-if="node.loading"
        class="el-tree-node__loading-icon el-icon-loading">
      </span>
      <div class='el-tree-table'>
        <el-table :data='[node.data]' :show-header='false' style='width:100%;' width='100%'>
          <el-table-column v-for='(item,index) in props.label' :key='index' :prop='item.name' show-overflow-tooltip align='left'>
          <template slot-scope='scope'>
            <a v-if='item.childNode' href='javascript:;' @click='item.event&&item.event(scope.row)'>{{scope.row[item.name]}}</a>
            <template v-else>
              {{scope.row[item.name]|treeFilter(item.name)}}
            </template>
          </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <collapse-transition>
      <div
        class="el-tree-node__children"
        v-show="expanded">
        <el-tree-node
          :render-content="renderContent"
          v-for="child in node.childNodes"
          :key="getNodeKey(child)"
          :node="child" 
          :props='props' 
          @node-expand="handleChildNodeExpand">
        </el-tree-node>
      </div>
    </collapse-transition>
  </div>
</template>

<script type="text/jsx">
  import CollapseTransition from './model/collapse-transition';
  import emitter from './model/emitter';
  import ElTreeNode from './tree-node.vue';

  export default {
    name: 'ElTreeNode',

    componentName: 'ElTreeNode',

    mixins: [emitter],

    props: {
      node: {
        default() {
          return {};
        }
      },
      props: {},
      renderContent: Function
    },

    components: {
      // ElCheckbox,
      ElTreeNode,
      CollapseTransition,
      NodeContent: {
        props: {
          node: {
            required: true
          },
          props:''
        },
        render(h) {
          const parent = this.$parent;
          const node = this.node;
          const data = node.data;
          const store = node.store;
          if(parent.renderContent){
            return parent.renderContent.call(parent._renderProxy, h, { _self: parent.tree.$vnode.context, node, data, store })
          }else{
            let nodes = [],
              self = this;
            ;(this.props.label||[]).find(d=>{
              let txt = this.node.data[d.name];
              if(d.name==='status'){
                txt = txt?'生效':'失效';
              }
              if(d.name==='type'){
                if(txt==='MENU'){
                  txt = '菜单'
                }
                if(txt==='FUNCTION'){
                  txt = '功能'
                }
                if(txt==='REPORT'){
                  txt = '报表'
                }
              }
              nodes.push(dom)
            })
            return h('ul',nodes);
          }
        }
      }
    },

    data() {
      return {
        tree: null,
        expanded: false,
        childNodeRendered: false,
        showCheckbox: false,
        oldChecked: null,
        oldIndeterminate: null
      };
    },

    watch: {
      'node.indeterminate'(val) {
        this.handleSelectChange(this.node.checked, val);
      },

      'node.checked'(val) {
        this.handleSelectChange(val, this.node.indeterminate);
      },

      'node.expanded'(val) {
        this.expanded = val;
        if (val) {
          this.childNodeRendered = true;
        }
      }
    },

    methods: {
      getNodeKey(node, index) {
        const nodeKey = this.tree.nodeKey;
        if (nodeKey && node) {
          return node.data[nodeKey];
        }
        return index;
      },

      handleSelectChange(checked, indeterminate) {
        if (this.oldChecked !== checked && this.oldIndeterminate !== indeterminate) {
          this.tree.$emit('check-change', this.node.data, checked, indeterminate);
        }
        this.oldChecked = checked;
        this.indeterminate = indeterminate;
      },

      handleClick() {
        const store = this.tree.store;
        store.setCurrentNode(this.node);
        this.tree.$emit('current-change', store.currentNode ? store.currentNode.data : null, store.currentNode);
        this.tree.currentNode = this;
        if (this.tree.expandOnClickNode) {
          this.handleExpandIconClick();
        }
        this.tree.$emit('node-click', this.node.data, this.node, this);
      },

      handleExpandIconClick() {
        if (this.node.isLeaf) return;
        if (this.expanded) {
          this.tree.$emit('node-collapse', this.node.data, this.node, this);
          this.node.collapse();
        } else {
          this.node.expand();
          this.$emit('node-expand', this.node.data, this.node, this);
        }
      },

      handleUserClick(data) {
        if (this.node.indeterminate) {
          this.node.setChecked(this.node.checked, !this.tree.checkStrictly);
        }
        this.tree.$emit('user-click',data)
      },

      handleCheckChange(ev) {
        if (!this.node.indeterminate) {
          this.node.setChecked(ev.target.checked, !this.tree.checkStrictly);
        }
      },

      handleChildNodeExpand(nodeData, node, instance) {
        this.broadcast('ElTreeNode', 'tree-node-expand', node);
        this.tree.$emit('node-expand', nodeData, node, instance);
      }
    },

    created() {
      const parent = this.$parent;

      if (parent.isTree) {
        this.tree = parent;
      } else {
        this.tree = parent.tree;
      }

      const tree = this.tree;
      if (!tree) {
        console.warn('Can not find node\'s tree.');
      }

      const props = tree.props || {};
      const childrenKey = props['children'] || 'children';

      this.$watch(`node.data.${childrenKey}`, () => {
        this.node.updateChildren();
      });

      this.showCheckbox = tree.showCheckbox;

      if (this.node.expanded) {
        this.expanded = true;
        this.childNodeRendered = true;
      }

      if(this.tree.accordion) {
        this.$on('tree-node-expand', node => {
          if(this.node !== node) {
            this.node.collapse();
          }
        });
      }
      // console.log(this.props.label)
    },
    filters:{
      treeFilter(val,type){
        let str = val;
        if((typeof(val)!=='undefined')&&type){
          if(type==='status'){
            str = val?'生效':'失效'
          }else if(type==='type'){
            let __data = __AUX.get('menuType'),
              obj = __data.find(d=>d.code===val);
            str = obj?obj.name:val;
          }
        }
        return str;
      }
    }
  };
</script>
