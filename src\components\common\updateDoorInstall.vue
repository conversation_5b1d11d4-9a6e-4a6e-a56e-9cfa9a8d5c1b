<!--生成服务单-更新上门服务类型-->
<template>
  <div class="xpt-flex" style='padding-top: 10px'>
    <div class="modal-content">
      <div class="form-label">上门服务类型:</div>
      <div class="form-value">
        <el-select
          v-model="if_door_install"
          size="mini"
          placeholder="请选择"
        >
          <el-option label="安装" value="INSTALL"></el-option>
          <el-option label="维修" value="REPAIR"></el-option>
        </el-select>
      </div>
    </div>
    <div class="modal-footer">
      <el-button type='primary' size='small' @click="callback('confirm')">确定</el-button>
      <el-button type='primary' size='small' @click="callback('close')">取消</el-button>
    </div>
  </div>
</template>
<script>
export default {
  props:["params"],
  data(){
    return {
      if_door_install:''
    }
  },
  methods:{
    closeCurrentComponent(){
      this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
    },
    callback(type){
      if(type === 'confirm' && !this.if_door_install) {
        this.$message.error('请选择上门服务类型');
        return;
      }
      if(type === 'confirm') {
        const self = this;
        self.ajax.postStream('/afterSale-web/api/aftersale/ticketSupply/updateIfDoorInstall', { id: self.params.supplyOrderId, ifDoorInstall: self.if_door_install },function(res){
          if(res?.body?.result){
            self.$message.success("更新成功");
            setTimeout(()=> {
              self.params.callback({ modalType: type, if_door_install: self.if_door_install });
              self.closeCurrentComponent();
            }, 500)
          }else{
            self.$message.error(res?.body?.msg || '更新失败')
          }
        },e=>{
          self.$message.error(e)
        })
      } else {
        this.params.callback({ modalType: type, if_door_install: '' });
        this.closeCurrentComponent();
      }
    }
  }
}
</script>
<style lang="stylus" scoped>
.modal-content{
  display: flex;
  .form-label {
    padding: 15px 10px;
  }
  .form-value{
    padding: 10px 0;
  }
}
.modal-footer{
  padding-top: 20px;
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
}
</style>
