<!-- 上传，没有分组 -->
<template>
		<input  style="display: none" type="file" :id='files' multiple="multiple" @change='change' >
</template>
<script>
	import Fn from '@common/Fn.js'
	import HelpFile  from '@common/helpFile.js'
	export default {
		data() {
			return {
				index: 0,
				imgList: [],
				//oldData: {},
				files:'files'+Math.random(),//随机定义id，防止出现相同id
				host:null,
				uploadNum: 0,
				//uploadImgList:[],//上传的图片路径
				filesLength: null
			}
		},
		props: {
			// 图片数组
			ifClickUpload: {
				type: <PERSON><PERSON>an,
				default(){
					return false;
				}
			},
			dataObj: {
				type: Object,
				default(){
					return {};
				}
			},

			//是不是只能传图片
			isOnlyPic:{
				type:Boolean,
				default(){
					return false;
				}
			},

			//是否有事件监听
			uploadImg:{
				type:Function,
				default(){
					return new Function();
				}
      },
      
      source:{
				type: String,
				default: ''
      },


		},
		methods: {
			
			//获取上传文件的信息，并逐个上传
			change() {
				let files = document.getElementById(this.files).files,
					len = files.length,
					fr = new FileReader(),
					index = 0,
					self = this;
				fr.readAsDataURL(files[0]);

				this.uploadNum = 0;
				//this.uploadImgList = [];
				this.filesLength = files.length;

				//根据用户传过来的参来判断是否要作图片限制
				if(this.isOnlyPic){
					for(let i = 0; i < this.filesLength;i++){
						let bool = this.isPucture(files[i].name);
						if(!bool){
							this.$message.error('请传入jpeg,gif,jpg,png,bmp,pic格式的图片');
							document.getElementById(this.files).value = '';
							return;
						}
					}
				}

				
				self.upload(files,0);
				fr.onload = function(e) {
					index += 1;
					if(index<len){
						fr.readAsDataURL(files[index]);
						self.upload(files,index)
					}
				}
			},
			//判断是否为图片
	        isPucture(str) {
	            str = str.toString();
	            var strFilter=".jpeg|.gif|.jpg|.png|.bmp|.pic|"
	            if(str.indexOf(".")>-1)
	            {
	                var p = str.lastIndexOf(".");
	                var strPostfix=str.substring(p,str.length) + '|';
	                strPostfix = strPostfix.toLowerCase();
	                if(strFilter.indexOf(strPostfix)>-1)
	                {
	                    return true;
	                }
	            }
	            return false;
	        },
			// 获取上传图片的key值
			validateKey(resolve){
				this.ajax.postStream('/app-web/app/file/validateKey.do', {type:"GET"} ,(res)=>{
					console.log(res);
					if(res.body.success){
						resolve && resolve(res.body.data)
					}else{
						this.$message.error('获取文件路径失败，请重新操作');
					}
				})
			},
			// 提交至服务器
			upload(files,index) {
				new Promise((resolve,reject)=>{
					this.validateKey(resolve)
				}).then((key)=>{
					let formData = new FormData();
					formData.append('file', files[index]);
					// console.log(65);
					var _this = this;
					//上传到附件库
					let host = this.host.split('//')[1];
                    let requestHeaderPath=this.getUploadFileApiHeader();
					let url = requestHeaderPath+host+'/'+key+'.do';
					// let url = 'http://**************:12010/upload-web/app/file/uploadFile/'+host+'/'+key+'.do';
					this.ajax.post(url, formData, (s) => {
						if(s.ok && s.body && s.body.data){

							// var index1 = files[index].name.indexOf('.')+1;
							// var index2 = files[index].name.length;

							var data ={
								file_type: files[index].name.split('.').pop(),// 文件类型 string
								order_no: this.dataObj.parent_no,// 单据编号 string
								sub_order_no: this.dataObj.child_no,// 子单据编号 string
								name: files[index].name,// 名字 string
								size: files[index].size,// 大小 long
								group_value_json: JSON.stringify(this.dataObj),//  分组内容 string
								//group_value_json: null,//分组内容 string,没有分组之说
								path: s.body.data,//  地址 string
								ext_data: null,// 扩展字段 string
							}

							if(this.source == 'configur') {
                data.function_type = this.dataObj.function_type
              }
							

							//上传到接口，用于查看时获取 
							this.ajax.postStream('/file-iweb/api/cloud/file/upload',data,res => {
								
								if(res.body.result){
									this.uploadNum++;
									//全部上传完才显示成功
									if(this.uploadNum === files.length){
										this.$message.success('上传成功');
										this.$emit("uploadImg");//事件监听
									}
								}else {
									this.$message.error(res.body.msg);
								}
							})

						}
						}, (e) => {
							this.$message({
								message: files[index].name+'上传失败',
								type: 'error'
							})
						})
					})
				
			},
			// upload(files,index) {
				
			// 	let formData = new FormData();
			// 	formData.append('file', files[index]);
			// 	// console.log(65);
			// 	var _this = this;
			// 	//上传到附件库
				
			// 	this.ajax.post(this.host+'/app-web/app/file/uploadFile.do', formData, (s) => {
			// 		if(s.ok && s.body && s.body.data){

			// 			// var index1 = files[index].name.indexOf('.')+1;
			// 			// var index2 = files[index].name.length;

			// 			var data ={
			// 				file_type: files[index].name.split('.').pop(),// 文件类型 string
			// 			  	order_no: this.dataObj.parent_no,// 单据编号 string
			// 			  	sub_order_no: this.dataObj.child_no,// 子单据编号 string
			// 			  	name: files[index].name,// 名字 string
			// 			  	size: files[index].size,// 大小 long
			// 			  	group_value_json: JSON.stringify(this.dataObj),//  分组内容 string
			// 			  	//group_value_json: null,//分组内容 string,没有分组之说
			// 			  	path: s.body.data,//  地址 string
			// 			  	ext_data: null,// 扩展字段 string
			// 			}

						
						

			// 			//上传到接口，用于查看时获取
			// 			this.ajax.postStream('/file-iweb/api/cloud/file/upload',data,res => {
							
			// 				if(res.body.result){
			// 					this.uploadNum++;
			// 					//全部上传完才显示成功
			// 					if(this.uploadNum === files.length){
			// 						 this.$message.success('上传成功');
			// 						 this.$emit("uploadImg");//事件监听
			// 					}
			// 				}else {
			// 					this.$message.error(res.body.msg);
			// 				}
			// 			})

			// 		}
			// 	}, (e) => {
			// 		this.$message({
			// 			message: files[index].name+'上传失败',
			// 			type: 'error'
			// 		})
			// 	})
			// },
		},
		watch:{
			'ifClickUpload': function(newVal,oldVal) {
				if(newVal) {
					
					document.getElementById(this.files).click();
					if(this.host) {
						return;
					}
					//获取域名
					this.ajax.postStream('/file-iweb/api/cloud/fileConfig/get',{},res => {
						if(res.body.result){
							this.host = res.body.content.host;

						}else {
							this.$message.error(res.body.msg);
						}
					})
				}
			}
			
		},

		mounted() {
			console.log('看一下传过来的参',this.dataObj);
			//this.setOldData(this.dataObj);
		}
	}
</script>
