<!-- 文件上传
<AUTHOR>
@info 基于element-ui el-upload 封装自定义上传类型
@progress 获取域名-》获取key值-》拼接域名key值以及formate input上传的file-》上传到附件库-》拼接data(主要为附件库上的文件做标记)上传附件到对应业务接口
@type 图片，视频，常用文本，通用，所有
@select 多选，单选
@message 类型错误弹出提示不打断其它文件上传，全部上传才成功，有失败项弹出提示
@params
    acceptTypeStr 自定义上传文件类型 String 优先级大于acceptType，如'.png,.mp4,.xlsx'
    acceptType    文件上传类型 String 提供 img,video,txt,usually,all 默认为all
    uploadBtnText 上传按钮文本 String 默认为-上传附件
    ifMultiple 是否多选 Boolean
    autoUpload 是否自动上传 Boolean 默认为true，false暂不支持，预留开发
    dataObj 上传业务接口携带参数 Object 必传，其实只要有一个parent_no就行了，其它可为null
    uploadSize 文件大小 Number 默认为1M
    ifJudgeImgSize 图片尺寸校检 Boolean 默认false
    imgSizeList 图片尺寸开放列表 Array 默认为空，与ifJudgeImgSize一起使用，例["800*800","1024*1024"]
    limit 图片限制个数 Number 默认为0，即不应用，需限制则必传
@methods
    uploadSuccess 上传业务接口成功回调函数，携带当次上传所有附件数组对象currentUploadFilePathList
    uploadStart 上传开始回调函数
 -->
<template>
    <div>
        <el-upload
            ref="upload"
            action="no"
            :show-file-list="false"
            :multiple="ifMultiple"
            :accept="accept"
            :on-change="handleChange"
            :http-request="httpRequest"
            :auto-upload="autoUpload"
            :before-upload="handleBeforeUpload"
        >
            <el-button :size="btnSize" :type="btnType" :disabled="disabled">{{
                uploadBtnText
            }}</el-button>
        </el-upload>
    </div>
</template>
<script>
export default {
    data() {
        return {
            fileNo: 0, //上传的文件个数，用于判断是否所有文件上传成功
            accept: "*", //接受上传的类型
            acceptTypeObj: {
                img: ".jpg,.png,.jpeg,.gif,.JPG,.PNG,.JPEG,.GIF",
                video:
                    ".mp4,.avi,.wmv,.flv,.webm,.mkv,.MP4,.AVI,.WMV,.FLV,.WEBM,.MKV",
                txt:
                    ".xlsx,.xls,.doc,docx,.txt,.ppt,.pptx,.pdf,.XLSX,.XLS,.DOC,DOCX,.TXT,.PPT,.PPTX,.PDF",
                usually:
                    ".jpg,.png,.jpeg,.gif,.JPG,.PNG,.JPEG,.GIF,.mp3,.mp4,.MP4,.avi,.AVI,.xlsx,.xls,.doc,docx,.txt,.ppt,.pptx,.pdf,.XLSX,.XLS,.DOC,DOCX,.TXT,.PPT,.PPTX,.PDF",
                all: "*",
            }, //文件类型对象
            currentUploadFilePathList: [], //当次上传的文件路径对象列表
            filesLength:0,//input选择文件个数
            moreThanFilesOnceMsg:false,//超过个数提示
        };
    },
    props: {
        //自定义上传文件类型
        acceptTypeStr: {
            type: String,
            default: () => {},
        },
        //文件上传类型
        acceptType: {
            type: String,
            default: () => {
                return "all";
            },
        },
        //上传按钮文本
        uploadBtnText: {
            type: String,
            default: () => "上传附件",
        },
        ifMultiple: {
            type: Boolean,
            default: () => true,
        },
        autoUpload: {
            type: Boolean,
            default: () => true,
        },
        /* dataObj字段
        @params parent_name,parent_no,child_name,child_no,content
        */
        dataObj: {
            type: Object,
            default: () => {},
        },
        uploadSize: {
            type: Number,
            default: () => 1,
        },
        disabled: {
            type: Boolean,
            default: () => false,
        },
        btnSize: {
            type: String,
            default: () => "mini",
        },
        btnType: {
            type: String,
            default: () => "primary",
        },
        //是否展示默认成功消息，否则可在回调函数uploadSuccess自定义处理
        showSuccessMsg: {
            type: Boolean,
            default: () => true,
        },
        ifJudgeImgSize: {
            type: Boolean,
            default: () => false,
            },
        imgSizeList: {
            type: Array,
            default: () => [],
        },
        limit:{
            type: Number,
            default:() => 0
        },
        //勿动权限参数
        permissionCode:{
            type:String,
            default:''
        }
    },
    mounted() {
        this.initAcceptStr();
    },
    methods: {
        //手动触发上传
        manualTriggerUpload(){
            console.log(this.$refs.upload)
            this.$refs['upload'].$children[0].$refs.input.click()
        },
        // 初始化接受文件类型
        initAcceptStr() {
            let acceptStr =
                this.acceptTypeStr ||
                this.acceptTypeObj[this.acceptType] ||
                "*";
            this.accept = acceptStr;
            console.log("接受的文件类型str", this.accept);
        },
        // 选择文件更改触发
        handleChange(file) {
            console.log("上传的文件handleChange", file);
            let files=this.$refs['upload'].$children[0].$refs.input.files;
            if (files.length==0) return;
            console.log("上传的文件数量", files.length);
            this.filesLength = files.length;
            this.limit!=0&&this.filesLength>this.limit&&(this.moreThanFilesOnceMsg=true);
        },
        judgeImgSize(file){
            let self=this;
             return new Promise(function(resolve, reject) {
                let reader = new FileReader()
                reader.readAsDataURL(file)
                reader.onload = function(theFile) {
                    let image = new Image()
                    image.src = theFile.target.result
                    image.onload = function() {
                        let csize = `${this.width}*${this.height}`
                        console.log(csize)
                        if (self.imgSizeList.includes(csize)) {
                            resolve(true)
                        } else {
                            self.$notify.error({
                                title: "文件尺寸错误",
                                message: `文件-${file.name}`,
                                customClass: "upload-v3-notify",
                            });
                            reject(false)
                        }
                    }
                }
            })
        },
        // 上传前限制文件
        handleBeforeUpload(file) {
            let self = this;
            //限制个数
            if(this.limit!=0&&this.filesLength>this.limit){
                if(this.moreThanFilesOnceMsg){
                    window.setTimeout(() => {
                        this.$notify.error({
                            title: `文件数量`,
                            message: `数量不能超过${this.limit}个`,
                            customClass: "upload-v3-notify",
                        });
                    }, 1);
                    this.moreThanFilesOnceMsg=false;
                }
                return false
            }

            // 限制大小
            let ifSize = file.size / 1024 / 1024 < this.uploadSize;
            if (!ifSize) {
                window.setTimeout(() => {
                    this.$notify.error({
                        title: `文件大小超过${self.uploadSize}M`,
                        message: `${file.name}`,
                        customClass: "upload-v3-notify",
                    });
                }, 1);
                return false;
            }
            // 限制类型
            let acceptStr = this.accept;
            if(!!acceptStr&&acceptStr!="*"){
                let type = file.name.split(".").slice(-1).join();
                if(!acceptStr.includes(type)){
                    window.setTimeout(() => {
                          self.$notify.error({
                              title: "文件格式错误",
                              message: `文件-${file.name}`,
                              customClass: "upload-v3-notify",
                          });
                      }, 1);
                     return false
                }
            }

            //限制尺寸
            if(this.ifJudgeImgSize){
                return this.judgeImgSize(file)
            }
        },
        async httpRequest(res) {
            //上传开始回调函数，可用于设置按钮loading状态
            this.$emit("uploadStart",{});
            // 格式化file
            const formData = new FormData();
            formData.append("file", res.file);
            // 获取附件库域名host和路径key，拼接附件库接口
            let host = await this.getHost();
            let key = await this.getKey();
            let uploadUrl = "";
            let requestHeaderPath=this.getUploadFileApiHeader();
            if (!!host && !!key) {
                uploadUrl =
                    requestHeaderPath +
                    host.split("//")[1] +
                    "/" +
                    key +
                    ".do";
            } else {
                return;
            }
            // 上传附件库
            let fileUrl = await this.uploadFileLibrary(
                uploadUrl,
                formData,
                res.file
            );

            // 上传业务接口
            this.uploadFileToApi(fileUrl, res.file);
        },
        // 预留方法，autoUpload为false时可手动上传
        // uploadFile() {
        //     //上传文件，会触发handleBeforeUpload和httpRequest
        //     this.$refs.upload.submit();
        // },
        getHost() {
            //获取域名
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/file-iweb/api/cloud/fileConfig/get",
                    {},
                    (res) => {
                        if (res.body.result) {
                            resolve && resolve(res.body.content.host);
                        } else {
                            this.$message.error(res.body.msg);
                            reject();
                        }
                    }
                );
            });
        },
        getKey() {
            return new Promise((resolve, reject) => {
                this.ajax.postStream(
                    "/app-web/app/file/validateKey.do",
                    { type: "GET" },
                    (res) => {
                        if (res.body.success) {
                            resolve && resolve(res.body.data);
                        } else {
                            this.$message.error("获取文件路径失败，请重新操作");
                            reject();
                        }
                    }
                );
            });
        },
        uploadFileLibrary(uploadUrl, formData, file) {
            let self = this;
            return new Promise((resolve, reject) => {
                this.ajax.post(
                    uploadUrl,
                    formData,
                    (res) => {
                        res.ok &&
                            res.body &&
                            res.body.data &&
                            resolve(res.body.data);
                    },
                    (err) => {
                        window.setTimeout(() => {
                            this.$notify.error({
                                title: "上传附件库失败",
                                message: `文件-${file.name}`,
                                customClass: "upload-v3-notify",
                            });
                        }, 1);
                        reject(err);
                    }
                );
            });
        },
        uploadFileToApi(fileUrl, file) {
            let self = this;
            let data = {
                file_type: file.name.split(".").pop(), // 文件类型 string
                order_no: this.dataObj.parent_no, // 单据编号 string
                sub_order_no: this.dataObj.child_no, // 子单据编号 string
                name: file.name, // 名字 string
                size: file.size, // 大小 long
                group_value_json: JSON.stringify(this.dataObj), //  分组内容 string
                path: fileUrl, //  地址 string
                ext_data: null, // 扩展字段 string
            };
            //当次上传附件列表，用于回调使用
            this.currentUploadFilePathList.push(data);

            let url="/file-iweb/api/cloud/file/upload"
            //判断是否有permissionCode
            if(this.permissionCode){
                url=`/file-iweb/api/cloud/file/upload?permissionCode=${this.permissionCode}`
            }

            //上传到接口，用于查看时获取
            this.ajax.postStream(
                url,
                data,
                (res) => {
                    if (res.body.result) {
                        this.fileNo++;
                        if (
                            this.fileNo == this.$refs.upload.uploadFiles.length
                        ) {
                            this.showSuccessMsg&&this.$notify.success({
                                title: "上传成功",
                                message: `已成功上传${this.fileNo}个文件`,
                                customClass: "upload-v3-notify",
                            });
                            this.fileNo = 0;
                            this.$refs.upload.clearFiles();
                            this.$emit(
                                "uploadSuccess",
                                self.currentUploadFilePathList
                            );
                            self.currentUploadFilePathList = [];
                        }
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
    },
};
</script>

<style scoped>
</style>
<style lang="css">
.el-notification.upload-v3-notify i {
    font-size: 30px !important;
}
</style>
