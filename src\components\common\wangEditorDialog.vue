<template>
  <div class="xpt-flex">
    <div class="xpt-top">
      <el-button type="danger" size="mini" @click="closeTab"  v-if="!detailContent">取消</el-button>
      <el-button type="primary" size="mini" @click="importSure"  v-if="!detailContent">确定</el-button>
    </div>
    <div class="xpt-flex__bottom">
      <div v-if="detailContent" v-html="detailContent" class="detail-content"></div>
      <wangEditor v-else :baseConfig="config" divId='inner-remark' ref='innerRemark' class="wangEditor" @onChange="htmlChange">
      </wangEditor>
    </div>
  </div>
</template>
<script>
import wangEditor from '@components/wang_editor/wangEditor.vue';
import '@stylus/wangEditor.styl'
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      detailContent: "",
      remarkCount: 0,
      maxRichTextLength:1000,
      config: {
        width: '100%',
        height: 245, //高度
        zIndex: 2, //层级
        placeholder: "请在此码字", //文本为空时的提示
        focus: false, //聚焦
        menus: [
          "head",
          "bold",
          "fontSize",
          "fontName",
          "italic",
          "underline",
          "strikeThrough",
          "indent",
          "lineHeight",
          "foreColor",
          "backColor",
          "list",
          "justify",
          "splitLine",
          "undo",
          "redo",
          "image",
        ], //菜单
      },
    };
  },
  methods: {
    closeTab() {
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
    },
    importSure() {
      if(!this.remarkCount){
        this.$message.warning('请输入')
        return
      }
      if(this.remarkCount.length>this.maxRichTextLength){
        this.$message.warning('内容超载，请删减后再提交')
        return
      }
      const content=this.getWEContent();
      if(this.params.validateCallback){
        if(!this.params.validateCallback(content)){
          return
        }
      }
      this.params.callback && this.params.callback(content);
      this.closeTab();
    },
    getWEContent() {
      return this.$refs.innerRemark.getEditorHtml();
    },
    getEditorTxt() {
      return this.$refs.innerRemark.getEditorTxt();
    },
    setWEContent(data) {
      return this.$refs.innerRemark.setEditorHtml(data);
    },
    htmlChange() {
      this.remarkCount = this.getEditorTxt().length;
    },
  },
  mounted: function () {
    if(this.params.content){
      this.detailContent=this.params.content;
    }
  },
  components: {
    wangEditor
  }
};
</script>
<style scoped>
.detail-content{
  height: 100%;
  overflow:auto;
}
</style>
<style></style>
