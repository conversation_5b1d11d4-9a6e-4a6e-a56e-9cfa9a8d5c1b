<template>
  <div class="xpt-flex" v-loading="loading">
    <div class="form-box">
      <div class="form-input">
        <div class="form-inline">
          <label>手机</label>
          <el-input size="mini" v-model="formMap.receiver_mobile"/>
        </div>
        <div class="form-inline">
          <label>买家昵称</label>
          <el-input size="mini" v-model="formMap.customer_name"/>
        </div>
        <div class="form-inline">
          <label>收货人</label>
          <el-input size="mini" v-model="formMap.receiver_name"/>
        </div>
        <div class="form-inline">
          <label>合并单号</label>
          <el-input size="mini" v-model="formMap.merge_trade_no"/>
        </div>
        
      </div>
      <div class="search-btn">
        <el-button type="primary" size='mini' @click="tableSearch">查询</el-button>
        <el-button type="primary" size='mini' @click="clear">重置</el-button>
      </div>
    </div>
    <div class="table-one">
      <el-table :data="tableDataOne" @row-click="getDLdata" border v-loading="tabLoading" highlight-current-row>
        <el-table-column :show-overflow-tooltip="true" prop="merge_trade_no" label="合并单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="batch_trade_no" label="批次单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="customer_name" label="买家昵称"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="receiver_name" label="收货人"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="receiver_mobile" label="手机"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="amount" label="支付金额"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="pay_time" label="支付时间"
                         :formatter="formatTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="salesman_name" label="业务员"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="best_user_name" label="推荐业务员"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="group_name" label="分组"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="big_group_name" label="大分组"></el-table-column>
        <!--<el-table-column :show-overflow-tooltip="true" prop="consign_time" label="业务时间" :formatter="formatTime"></el-table-column>-->
        <el-table-column :show-overflow-tooltip="true" prop="step_trade_status" label="定金订单"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="is_before" label="提前下单"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="seller_memo" label="卖家备注"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="business_type_trade" label="订单业务类型" width="150"
                         title="1"></el-table-column>
      </el-table>
    </div>
    <div class="table-two">
      <tabs :tabs="tabs" @selectedTabs="selectedTabs" :currentClass.sync="showTable"></tabs>
      <el-table
        ref="table2"
        :data="tableDataTwo"
        border v-loading="tabLoading">
        <el-table-column type="index"/>
        <template v-for="(col, idx) in cols[showTable]">
          <!--          <el-table-column v-if="!!col.formattor"
                        :key="idx" :prop="col.prop" :label="col.label" width="150">
                        <template slot-scope="scope">
                          <span v-html="formatter(scope.row, col)" width="150"></span>
                        </template>
                  </el-table-column>-->
          <el-table-column :show-overflow-tooltip="true"
                           :key="idx" :prop="col.prop" :label="col.label" width="150">
            <template slot-scope="scope">
              <span v-html="formatter(scope.row, col)" width="150"></span>
            </template>
          </el-table-column>
        </template>

      </el-table>
    </div>
    <!-- <div class="el-row xpt-pagation">
       <el-pagination layout="sizes, prev, pager, next"
                      :page-sizes="[300, 600, 900, 1200]"
                      :page-size="300"
                      :total="20"
                      @current-change="handleCurrentChange">
       </el-pagination>
     </div>-->
  </div>
</template>

<script>
  import baseUrl, {makeUrl, makeParam, apiUrl} from '../call_system/base.js';
  import tabs from '@/components/common/tabs';
  import Fn from '@/common/Fn.js';

  export default {
    props: ['job', 'params', 'sharedPhone', 'sharedCustomer'],
    components: {
      tabs,
    },
    watch: {
      'formMap.customer_name'(val, oldVal) {
        this.$emit('update:sharedCustomer', val);
      },
      'formMap.receiver_mobile'(val, oldVal) {
        this.$emit('update:sharedPhone', val);
      },
      sharedPhone(val, oldVal) {
        if (this.formMap.receiver_mobile !== val) {
          this.formMap.receiver_mobile = val;
        }
      },
      sharedCustomer(val, oldVal) {
        if (this.formMap.customer_name !== val) {
          this.formMap.customer_name = val;
        }
      },
    },
    data() {
      return {
        formMap: {
          merge_trade_no: null,
          customer_name: null,
          receiver_name: null,
          receiver_mobile: null,
        },
        zindex: '', // 弹窗标识
        tabs: [
          {
            id: 'getCallCenterBatchOrderBatchList',
            name: '批次信息',
            data: null,
          },
          {
            id: 'getCallCenterBatchOrderGoodsList',
            name: '商品信息',
            data: null,
          },
          {
            id: 'getCallCenterBatchOrderReceiverList',
            name: '收货信息',
            data: null,
          },
          {
            id: 'getCallCenterBatchOrderMemoList',
            name: '内部便签',
            data: null,
          },
          {
            id: 'getCallCenterBatchOrderOperationList',
            name: '操作记录',
            data: null,
          },
          {
            id: 'getCallOutList',
            name: '呼出记录',
            data: null,
          },
        ],
        showTable: 0,
        tableDataOne: [],
        tableDataTwo: [],
        cols: {
          0: [
            {
              prop: 'batch_trade_no',
              label: '批次单号',
            }, {
              prop: 'merge_trade_no',
              label: '合并单号',
            }, {
              prop: 'customer_name',
              label: '买家昵称',
            }, {
              prop: 'salesman_name',
              label: '业务员',
            }, {
              prop: 'group_name',
              label: '分组',
            }, {
              prop: 'three_guarantees_fee_type',
              label: '三包类型',
            }, {
              prop: 'three_guarantees_fee',
              label: '三包费',
            }, {
              prop: 'post_fee_type',
              label: '运费类型',
            }, {
              prop: 'post_fee',
              label: '运费',
            }, {
              prop: 'actual_cost_total',
              label: '商品总金额',
            }, {
              prop: 'commit_time',
              label: '承诺发货时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'zd_plan_delivery_time',
              label: '计划发货时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'consign_time',
              label: '业务时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'zd_delivery_status',
              label: '发货',
            }, {
              prop: 'business_type_trade',
              label: '订单业务类型',
            }, {
              prop: 'zd_delivery_time',
              label: '发货时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'goods_audit_status',
              label: '货审',
            }, {
              prop: 'goods_audit_time',
              label: '货审时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'financial_audit_status',
              label: '财审',
            }, {
              prop: 'financial_audit_time',
              label: '财审时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'zd_print_lock_date',
              label: '打印锁定时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'zd_print_lock_status',
              label: '打印锁定状态',
            }],
          1: [
            // {
            //   prop: 'inventory_group_quantity',
            //   label: '库存分组可用数',
            // }
            {
              prop: 'merge_trade_no',
              label: '合并单号',
            }, {
              prop: 'batch_trade_no',
              label: '系统单号',
            }, {
              prop: 'shop_name',
              label: '店铺',
            }, {
              prop: 'salesman_name',
              label: '业务员',
            }, {
              prop: 'zd_delivery_batch',
              label: '批次号',
            }, {
              prop: 'receiver_number',
              label: '收货信息',
            }, {
              prop: 'material_number',
              label: '商品编码',
            }, {
              prop: 'specification_number',
              label: '规格编码',
            }, {
              prop: 'material_name',
              label: '商品名称',
            }, {
              prop: 'material_specification',
              label: '规格名称',
            }, {
              prop: 'cost_price',
              label: '成本价',
            }, {
              prop: 'act_price',
              label: '实际售价',
            }, {
              prop: 'postage',
              label: '包邮费用',
            }, {
              prop: 'count',
              label: '数量',
            }, {
              prop: 'volume',
              label: '体积',
            }, {
              prop: 'package_number',
              label: '包件数',
            }],
          2: [
            {
              prop: 'receiver_number',
              label: '地址编码',
            }, {
              prop: 'receiver_name',
              label: '收货人',
            }, {
              prop: 'receiver_phone',
              label: '固定电话',
            }, {
              prop: 'receiver_mobile',
              label: '手机',
            }, {
              prop: 'receiver_province',
              label: '省',
            }, {
              prop: 'receiver_city',
              label: '市',
            }, {
              prop: 'receiver_district',
              label: '区',
            }, {
              prop: 'receiver_address',
              label: '地址',
            }, {
              prop: 'receiver_zip_code',
              label: '邮编',
            }, {
              prop: 'logistics_supplier_name',
              label: '物流公司',
            }, {
              prop: 'logistics_point',
              label: '提货点',
            }, {
              prop: 'three_guarantees_supplier_name',
              label: '三包公司',
            }, {
              prop: 'three_guarantees_phone',
              label: '三包电话',
            }],
          3: [
            {
              prop: 'creator',
              label: '用户',
            }, {
              prop: 'content',
              label: '内容',
            }, {
              prop: 'create_time',
              label: '提出时间',
              type: 'time',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'subject',
              label: '主题',
            }],
          4: [
            {
              prop: 'operator_name',
              label: '用户',
            }, {
              prop: 'operation_name',
              label: '操作',
            }, {
              prop: 'operation_desc',
              label: '操作描述',
            }, {
              prop: 'operation_time',
              label: '创建时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'source_no',
              label: '来源单据编码',
            }],
          5: [
            {
              prop: 'callOutType',
              label: '呼出类型',
            }, {
              prop: 'jobNumber',
              label: '来源单号',
            }, {
              prop: 'staffNo',
              label: '工号',
            }, {
              prop: 'staffName',
              label: '用户名',
            }, {
              prop: 'phoneNumber',
              label: '呼叫电话',
            }, {
              prop: 'recordStartTime',
              label: '开始时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'recordEndTime',
              label: '结束时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'remark',
              label: '呼出内容',
            }],
        },
        loading: false,
        tabLoading: false,
        row: null,
      };
    },
    computed: {
      hasArguments() {
        for (let key in this.formMap) {
          if (!!this.formMap[key]) {
            // 至少一个查询参数有值
            return true;
          }
        }
        return false;
      },
    },
    methods: {
      formatter(row, col) {
        if (!!col.formattor) {
          return col.formattor(row[col.prop]);
        }
        return row[col.prop];
      },
      formatTime(row, col, val) {
        return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
      },
      formatDate(row, col, val) {
        return Fn.dateFormat(val);
      },
      selectedTabs(index) {
        this.showTable = index;
        if (null != this.row && this.row != this.tabs[index].data) {
          this[this.tabs[index].id](this.row); // 调用对应tabs的请求方法, it's magic
          let data = {};
          for (let i in this.row) {
            data[i] = this.row[i];
          }
          this.tabs[index].data = data;
        }
      },
      // 单击
      getDLdata(row) {
        this.row = row;
        this.selectedTabs(this.showTable);
        new Promise((reslove,reject)=>{
          this.$emit('changed', {val:false,row:row,reslove:reslove});
        }).then(res=>{
          // console.log(res);
          this.tableDataOne.forEach(item=>{
            if(item == row){
              item.best_user_name = res;
            }
          })
        })
      },
      // 查询
      tableSearch() {
        this.getCallCenterBatchOrderList(this.formMap);
        this.$emit('changed', true);
      },
      clear() {
        for (let i in this.formMap) {
          this.formMap[i] = '';
        }
      },
      // 获取批次订单列表
      getCallCenterBatchOrderList() {
        if (!this.hasArguments) {
          this.$message.error('请输入查询条件');
          return;
        }
        this.loading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterBatchOrderList', makeParam(this.formMap)).
          then(res => {
            this.tableDataOne = res.data.content.list;
          }).
          catch(() => {
          }).
          finally(() => {
            this.loading = false;
            this.row = null;
            this.tableDataTwo = [];
          });
      },
      // 批次信息
      getCallCenterBatchOrderBatchList(data) {
        this.tabLoading = true;
        let params = {
          batch_trade_no: data.batch_trade_no,
          merge_trade_no: data.merge_trade_no,
        };
        this.$http.post('/external-web/api/callCenter/getCallCenterBatchOrderBatchList', makeParam(params)).
          then(res => {
            this.tableDataTwo = res.data.content.list;
          }).
          catch(() => {
          }).
          finally(() => {
            this.tabLoading = false;
          });
      },
      // 商品信息
      getCallCenterBatchOrderGoodsList(data) {
        let params = {
          batch_trade_no: data.batch_trade_no,
          merge_trade_no: data.merge_trade_no,
        };
        this.tabLoading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterBatchOrderGoodsList', makeParam(params)).
          then(res => {
            this.tableDataTwo = res.data.content.list;
          }).
          catch(() => {
          }).
          finally(() => {
            this.tabLoading = false;
          });
      },
      // 收货信息
      getCallCenterBatchOrderReceiverList(data) {
        let params = {
          batch_trade_no: data.batch_trade_no,
        };
        this.tabLoading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterBatchOrderReceiverList', makeParam(params)).
          then(res => {
            this.tableDataTwo = res.data.content.list;
          }).
          catch(() => {
          }).
          finally(() => {
            this.tabLoading = false;
          });
      },
      // 内部便签
      getCallCenterBatchOrderMemoList(data) {
        let params = {
          merge_trade_no: data.merge_trade_no,
        };
        this.tabLoading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterBatchOrderMemoList', makeParam(params)).then(res => {
          this.tableDataTwo = res.data.content.list;
        }).catch(() => {
        }).finally(() => {
          this.tabLoading = false;
        });
      },
      // 操作记录
      getCallCenterBatchOrderOperationList(data) {
        // debugger
        let params = {
          order_type: 'MERGE_ORDER',
          source_id: data.merge_trade_id,
        };
        this.tabLoading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterBatchOrderOperationList', makeParam(params)).
          then(res => {
            this.tableDataTwo = res.data.content.list;
          }).
          catch(() => {
          }).
          finally(() => {
            this.tabLoading = false;
          });
      },
      // 呼出记录
      getCallOutList(data) {
        let params = {
          sourceOrderNo: data.batch_trade_no,
          sourceOrderType: '批次订单',
        };
        this.tabLoading = true;
        this.$http.get(makeUrl(apiUrl.workOrder_detailTableTow, params)).then(res => {
          this.tableDataTwo = res.data.content;
        }).catch(() => {
        }).finally(() => {
          this.tabLoading = false;
        });
      },
      getAuditStatus(val) {
        if ('Y' == val) {
          return '已审核';
        }

        return '未审核';
      },
      // 关闭当前弹窗
      closeLayer() {
        // this.$layer.close(this.zindex)
      },
    },
    created() {
      this.formMap['customer_name'] = this.sharedCustomer;
      this.formMap['receiver_mobile'] = this.sharedPhone;
      // this.tableSearch();
    },
    updated() {
      console.log('u');
    },
    mounted() {
      console.log('m');
    },
  };
</script>

<style scoped>
  .form-box {
    margin-bottom: 10px;
  }

  .form-box .form-input {
    width: 620px;
    display: flex;
  }

  .form-input .form-inline {
    flex: 1;
  }

  .form-box .search-btn {
    float: right;
    margin-top: -20px;
  }

  .el-input {
    width: 100px !important;
  }

  .table-one {
    margin-bottom: 10px;
    height: 44%;
  }

  .table-two {
    height: 37%;
  }

  .xpt-flex, .el-table {
    height: 99% !important;
    display: flex;
    min-width: 100%;
    box-orient: vertical;
    flex-direction: column;
  }
</style>
