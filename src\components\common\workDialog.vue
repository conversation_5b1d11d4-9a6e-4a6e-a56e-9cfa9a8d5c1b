<template>
  <div class="layui-inline">
    <div class="tabs-work">
        <el-button type="primary"  size='mini' @click="openDialog('workbatchorde-bench')">
          批次订单
        </el-button>
        <el-button type="primary"  size='mini' @click="openDialog('worksale-bench')">
          销售订单
        </el-button>
         <el-button type="primary"  size='mini' @click="openDialog('worksaleafter-bench')">
          售后单
        </el-button>
        <!--
        <el-button type="primary"  size='mini'>
          退款申请单
        </el-button>
        <el-button type="primary"  size='mini'>
          内部咨询单
        </el-button>
        <el-button type="primary"  size='mini'>
          服务单
        </el-button>
         <el-button type="primary"  size='mini'>
          补件申请单
        </el-button>
        -->
        <el-button type="primary"  size='mini' @click="openDialog('workreturn-bench')">
          回访单
        </el-button>
      <!--
         <el-button type="primary"  size='mini'>
          三包点
        </el-button>
        <el-button type="primary"  size='mini'>
          提货点
        </el-button>
         <el-button type="primary"  size='mini'>
          询价单
        </el-button>
        -->
    </div>
    <div class="search-work">
      <div class="tabs-line">
        <div class="select-tab">
          基本信息
        </div>
      </div>
      <div class="form-box">
          <div class="form-inline">
              <div class="form-inline search-label">用户号码</div>
              <el-input class="search-input" size="mini" v-model="formMap.calledNumber"></el-input>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">地区</div>
              <el-input class="search-input"  size="mini" v-model="formMap.areaCode"></el-input>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">所选服务</div>
              <el-input class="search-input"  size="mini" v-model="formMap.receiverPhone"></el-input>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">客服状态</div>
              <div style="width:100px;height: 22px;display: inline-block;">{{formMap.salesIncumbency}}</div>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">合并单号</div>
              <el-input class="search-input"  size="mini" v-model="formMap.mergeTradeNo"></el-input>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">买家昵称</div>
              <el-input class="search-input" size="mini" v-model="formMap.custName"></el-input>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">收货人</div>
              <el-input class="search-input"  size="mini" v-model="formMap.receiverName"></el-input>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">店铺</div>
              <el-input class="search-input"  size="mini" v-model="formMap.shopName"></el-input>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">客服</div>
              <el-input class="search-input"  size="mini" v-model="formMap.salesUserName"></el-input>
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">收货手机</div>
              <el-input class="search-input"  size="mini" v-model="formMap.receiverPhone"></el-input>
          </div>
        <!--
          <div class="form-inline">
              <div class="form-inline search-label">下次处理时间</div>
              <el-input class="search-input"  size="mini" v-model="formMap.mergeTradeNo"></el-input>
          </div>
          -->
          <div class="form-inline">
              <div class="form-inline search-label">来电类型</div>
            <!--
              <el-select class="search-input"  size="mini" v-model="formMap.callingType">
                <el-option>合作加盟</el-option>
                <el-option>商品咨询</el-option>
                <el-option>订单状态</el-option>
                <el-option>货物损坏</el-option>
                <el-option>投诉建议</el-option>
                <el-option>其他</el-option>
                <el-option>预约到店</el-option>
              </el-select>
              -->
          </div>
          <div class="form-inline">
              <div class="form-inline search-label">归属</div>
              <el-input class="search-input"  size="mini" v-model="formMap.orderOwnerType"></el-input>
          </div>
           <div class="form-inline">
              <div class="form-inline search-label">客服分组</div>
               <el-input class="search-input"  size="mini" v-model="formMap.operatedUserName"></el-input>
          </div>
           <div class="form-inline">
              <div class="form-inline search-label">处理人</div>
              <el-input class="search-input"  size="mini" v-model="formMap.operatedUserName"></el-input>
          </div>
        </div>
    </div>
    <div class="body-work">
      <tabs :tabs="tabs" @selectedTabs="selectedTabs" :currentClass.sync="showTable"></tabs>
      <div class="call-in-table" v-show="showTable === 0">
        <textarea v-model="textareaText" style="width:780px;height:150px;margin:0 auto"></textarea>
      </div>
      <div class="history-table" v-show="showTable === 1">
        <el-table
          :data="tableData"
          border
          style="width: 100%;min-height:100px;">
          <el-table-column :show-overflow-tooltip="true" prop="callingType" label="呼叫类型"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" prop="operatedUserName" label="处理人"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" prop="jobNumber" label="工号"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" prop="" label="处理内容"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" prop="" label="处理时间"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" prop="" label="是否已删除">

          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="footer">
      <div class="footer-left">
        <el-button type="primary"  size='mini' @click="makeCall">
          电话拨号
        </el-button>
        <el-button type="primary"  size='mini'>
          满意度调查
        </el-button>
         <el-button type="danger"  size='mini'>
          挂断电话
        </el-button>
      </div>
      <div class="footer-right">
        <el-button size='mini'>
          重设
        </el-button>
        <el-button type="primary" @click="saveOrder" size='mini'>
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import tabs from '@/components/common/tabs'

import { mapGetters } from 'vuex'
export default {
  components: {
    tabs
  },
  computed: {
    ...mapGetters(['workDialogInput'])
  },
  data () {
    return {
      formMap: {
        operatedUserName: '',
        orderOwnerType: '',
        receiverPhone: '',
        salesUserName: '',
        calledNumber: '',
        receiverName: '',
        custName: '',
        areaCode: '',
        jobType: '',
        shopName: '',
        mergeTradeNo: ''
      },
      custormValue: '售中',
      options: [{
        value: '选项1',
        label: '黄金糕'
      }, {
        value: '选项2',
        label: '双皮奶'
      }, {
        value: '选项3',
        label: '蚵仔煎'
      }, {
        value: '选项4',
        label: '龙须面'
      }, {
        value: '选项5',
        label: '北京烤鸭'
      }],
      tabs: [
        {
          name: '内容呼入'
        },
        {
          name: '呼叫历史'
        }
      ],
      showTable: 0,
      tableData: [],
      tempZindex: '',
      textareaText: ''
    }
  },
  methods: {
    selectedTabs (index) {
      this.showTable = index
    },
    openDialog (htmlid) {
      this.$emit('openWorkDialog', htmlid)
    },
    makeCall () {
      window.application.oJVccBar.MakeCall('13690723617')
    },
    // 弹窗时初始化
    reloadData (data, zIndex, noData) {
      if (noData === 'noData') {
        this.formMap.calledNumber = data.phoneNo
        return
      }
      let params = {
        jobId: data.jobId
      }
      this.$http.get(this.$api.workDialogMsg, {params: params}).then((res) => {
        let obj = res.data.data
        this.formMap = {}
        this.formMap = {
          operatedUserName: obj.operatedUserName,
          orderOwnerType: obj.orderOwnerType,
          receiverPhone: obj.receiverPhone,
          salesUserName: obj.salesUserName,
          calledNumber: obj.calledNumber,
          receiverName: obj.receiverName,
          custName: obj.custName,
          areaCode: obj.areaCode,
          jobType: obj.jobType,
          shopName: obj.shopName,
          mergeTradeNo: obj.mergeTradeNo
        }
      })
      this.$http.get(this.$api.workOrderProcessLogList, {params: params}).then((res) => {
        this.textareaText = ''
      })
    },
    // 保存
    saveOrder () {
      let params = {
        operatedUserName: this.formMap.operatedUserName,
        orderOwnerType: this.formMap.orderOwnerType,
        receiverPhone: this.formMap.receiverPhone,
        salesUserName: this.formMap.salesUserName,
        calledNumber: this.formMap.calledNumber,
        receiverName: this.formMap.receiverName,
        custName: this.formMap.custName,
        areaCode: this.formMap.areaCode,
        jobType: this.formMap.jobType,
        shopName: this.formMap.shopName,
        mergeTradeNo: this.formMap.mergeTradeNo
      }
      let params2 = {
        remark: this.textareaText
      }
      Promise.all([this.$http.post(this.$api.addOrder, params, {emulateJSON: true}), this.$http.post(this.$api.workOrderProcessLog, params2, {emulateJSON: true})]).then(res => {
        console.log(res)
        this.$layer.close(window['work-bench'])
      })
    }
  },
  created () {
    console.log(this.workDialogInput)
    /*
    let data = this.workDialogInput.data
    this.formMap = {
      calledNumber: data.data.calledNumber
    }
    */
  }
}
</script>

<style scoped>
  .form-box{
    position:relative;
    margin:10px;
  }
  .form-inline{
    display: inline-block;
  }
  .tabs-work{
    margin:10px;
  }
  .footer{
    position: absolute;
    bottom: 0;
    width:100%;
  }
  .footer-left{
    float:left;
    margin-left:20px;
  }
  .footer-right{
    float:right;
    margin-right:20px;
  }
  .tabs-line{
    border-bottom:1px solid #ddd;
    margin:10px;
  }
  .select-tab{
    height:26px;
    line-height:26px;
    padding:0 10px;
    display:inline-block;
    cursor: pointer;
    margin-right:10px;
    border-radius: 5px 5px 0 0;
    background:#20a0ff;
    color:#fff;
    border:solid #20a0ff 1px;
    border-bottom:none;
  }
  .search-label{
    width:80px;
    text-align: center;
  }
  .search-input{
    margin-bottom: 10px;
    width: 105px;
  }
</style>
