<template>
  <div class="xpt-flex">
    <div class="form-box">
      <div class="form-input">
        <div class="form-inline">
          <label>手机</label>
          <el-input size="mini" v-model="formMap.receiver_mobile"></el-input>
        </div>
        <div class="form-inline">
          <label>买家昵称</label>
          <el-input size="mini" v-model="formMap.customer_name"></el-input>
        </div>
        <div class="form-inline">
          <label>收货人</label>
          <el-input size="mini" v-model="formMap.receiver_name"></el-input>
        </div>
        <div class="form-inline">
          <label>合并单号</label>
          <el-input size="mini" v-model="formMap.merge_trade_no"></el-input>
        </div>
      </div>
      <div class="search-btn">
        <el-button type="primary" size='mini' @click="getCallCenterAfterSaleList">查询</el-button>
        <el-button type="primary" size='mini' @click="resetForm">重置</el-button>
      </div>
    </div>
    <div class="table-one">
      <el-table
        highlight-current-row
        :data="tableDataOne"
        @row-click="getDLdata"
        border
        style="width: 100%;margin-bottom:10px;">
        <el-table-column :show-overflow-tooltip="true" prop="after_order_no" label="售后单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="merge_trade_no" label="合并单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="customer_name" label="买家昵称"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="locker_name" label="业务锁定人"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="lock_time" label="业务锁定时间" width="150" :formatter="formatDate"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="status" label="业务状态"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="finish_status" label="完结状态"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="" label="单据状态"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="submit_staff_name" label="提交售后人"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="submit_staff_time" label="提交售后人日期" width="150" :formatter="formatDate"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="creator_name" label="创建人"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="create_time" label="创建时间" :formatter="formatTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="pre_order_time" label="预处理时间" :formatter="formatTime"></el-table-column>

      </el-table>
    </div>
    <div class="table-two">
      <tabs :tabs="tabs" @selectedTabs="selectedTabs" :currentClass.sync="showTable"></tabs>
      <el-table

        :data="tableDataList"
        border
        style="width: 100%;">
        <el-table-column type="index"/>
        <template v-for="(col,idx) in cols[showTable]">
          <el-table-column
            :key="idx" :prop="col.prop" :label="col.label" :width="col.width ? col.width : 150"
             :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span v-html="formatter(scope.row, col)" :width="col.width ? col.width : 150"></span>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <!-- <div class="el-row xpt-pagation">
      <el-pagination layout="sizes, prev, pager, next"
        :page-sizes="[300, 600, 900, 1200]"
        :page-size="300"
        :total="20"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div> -->
  </div>
</template>

<script>
  import tabs from '@/components/common/tabs';
  import baseUrl, {makeUrl, makeParam} from '../call_system/base.js';
  import Fn from '@/common/Fn.js';

  export default {
    components: {
      tabs,
    },
    props: ['job', 'params','sharedPhone','sharedCustomer'],
    watch:{
      'formMap.receiver_name'(val,oldVal){
        this.$emit('update:sharedCustomer', val);
      },
      'formMap.receiver_mobile'(val,oldVal){
        this.$emit('update:sharedPhone', val);
      },
      sharedPhone(val,oldVal){
        if (this.formMap.receiver_mobile !== val) {
          this.formMap.receiver_mobile = val;
        }
      },
      sharedCustomer(val,oldVal){
        if (this.formMap.customer_name !== val) {
          this.formMap.customer_name = val;
        }
      }
    },
    data() {
      return {
        formMap: {
          merge_trade_no: null,
          customer_name: null,
          receiver_name: null,
          receiver_mobile: null,
        },
        zindex: '', // 弹窗标识
        tabs: [
          {
            name: '问题商品',
            id: 'getCallCenterAfterSaleGoodsList',
            data: null,
          },
          {
            name: '操作记录',
            id: 'getCallCenterAfterSaleOperationList',
            data: null,
          },
          {
            name: '处理进度',
            id: 'getCallCenterAfterSaleHandleLogList',
            data: null,
          },
          {
            name: '业绩',
            id: 'getCallCenterAfterSaleAchievementList',
            data: null,
          },
          {
            name: '责任人',
            id: 'getCallCenterAfterSaleDutyList',
            data: null,
          },
        ],
        showTable: 0,
        tableDataOne: [],
        tableDataList: [],
        cols: {
          0: [
            {
              prop: 'material_number',
              label: '规格编码',
            }, {
              prop: 'material_name',
              label: '商品名称',
            }, {
              prop: 'material_specification',
              label: '规格名称',
            }, {
              prop: 'produce_batch',
              label: '批号',
            }, {
              prop: 'question_type',
              label: '问题类型',
            }, {
              prop: 'saleman_name',
              label: '业务员',
            }, {
              prop: 'saleman_group',
              label: '分组',
            }, {
              prop: 'question_description',
              label: '问题描述',
              showToolTip: true,
            }, {
              prop: 'produce_no',
              label: '质检单号',
            }, {
              prop: 'batch_trade_no',
              label: '批次单号',
            }],
          1: [
            {
              prop: 'operator_name',
              label: '操作人',
            }, {
              prop: 'operation_time',
              label: '操作时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'operation_name',
              label: '操作',
              formattor(val){
                switch (val) {
                  default:
                    return val;
                  case 'SAVE':
                    return "保存";
                  case 'LOCK':
                    return "业务锁定";
                  case 'UNLOCK':
                    return "业务解锁";
                  case 'TURN':
                    return "转交";
                  case 'SUBMIT_AFTER':
                    return "提交售后";
                  case 'SUBMIT_AFTER_ABC':
                    return "提交售后ABC";
                  case 'RETRACT':
                    return "撤回";
                  case 'REJECT':
                    return "驳回";
                  case 'SALE_LOCK':
                    return "售后锁定";
                  case 'SALE_UNLOCK':
                    return "售后解锁";
                  case 'FINISHED':
                    return "完结";
                  case 'PLAN_SAVE':
                    return "保存方案";
                  case 'PLAN_UPDATE':
                    return "更新方案";
                  case 'PLAN_SUBMIT':
                    return "提交方案";
                  case 'PLAN_RETRACT':
                    return "撤回方案";
                  case 'PLAN_DELETE':
                    return "删除方案";
                  case 'MODIFY_AFTER_RANGE':
                    return "变更售后时限范围";
                  case 'DETELE_FILE':
                    return "删除售后图片";
                }

              },
            }, {
              prop: 'operation_desc',
              label: '操作描述',
              showToolTip: true,
            }],
          2: [
            {
              prop: 'handler_name',
              label: '处理人',
            }, {
              prop: 'handle_time',
              label: '处理时间',
              formattor(val) {
                return Fn.dateFormat(val);
              },
            }, {
              prop: 'handle_content',
              label: '处理内容',
              showToolTip: true,
              width: 300,
            }],
          3: [
            {
              prop: 'order_no',
              label: '单据编号',

            }, {
              prop: 'merge_trade_no',
              label: '合并单号',
            }, {
              prop: 'status',
              label: '状态',
              formattor(val) {
                switch (val) {
                  case 'AFTER_HANDLE' :
                    return '售后处理';
                  case 'CREATE':
                    return '待办';
                  case 'PRE_HANDL':
                    return '售前处理';
                  case 'WAIT_HANDLE' :
                    return '售后待办';
                  case 'FINISH':
                    return '完结';
                }
                return '';
              },
            }, {
              prop: 'created',
              label: '拍单时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'pay_time',
              label: '支付时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }, {
              prop: 'shop_name',
              label: '店铺',
            }, {
              prop: 'buyer_name',
              label: '买家昵称',
            }, {
              prop: 'tid',
              label: '淘宝单号',
            }, {
              prop: 'salesman_name',
              label: '业务员',
            }, {
              prop: 'amount',
              label: '支付金额',
            }],
          4: [
            {
              prop: 'order_name',
              label: '单据名称',
              formattor(val) {
                switch (val) {
                  case 'SUPPLY' :
                    return '补件申请单';
                  case 'RETURNS':
                    return '退货跟踪单';
                  case 'REPAIR':
                    return '服务单';
                  case 'REFUND_NEW' :
                    return '新退款申请单';
                }
                return '';
              },
            }, {
              prop: 'duty_person_name',
              label: '责任人',
            }, {
              prop: 'duty_amount',
              label: '责任金额',
            }, {
              prop: 'duty_remark',
              label: '责任备注',
            }, {
              prop: 'source_order_no',
              label: '源单单号',
            }, {
              prop: 'duty_question',
              label: '责任问题',
            }, {
              prop: 'duty_range',
              label: '责任范围',
            }, {
              prop: 'creator_name',
              label: '创建人',
            }, {
              prop: 'create_time',
              label: '创建时间',
              formattor(val) {
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
              },
            }],

        },
        loading: true,
        tabLoading: false,
        row: null,
      };
    },
    computed: {
      hasArguments() {
        for (let key in this.formMap) {
          if (!!this.formMap[key]) {
            // 至少一个查询参数有值
            return true;
          }
        }
        return false;
      },
    },
    methods: {
      formatter(row, col) {
        if (!!col.formattor) {
          return col.formattor(row[col.prop]);
        }
        return row[col.prop];
      },
      formatTime(row, col, val) {
        return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss');
      },
      formatDate(row, col, val) {
        return Fn.dateFormat(val);
      },
      selectedTabs(index) {
        this.showTable = index;
        if (null != this.row && this.row != this.tabs[index].data) {
          this[this.tabs[index].id](this.row); // 调用对应tabs的请求方法
          let data = {};
          for (let i in this.row) {
            data[i] = this.row[i];
          }
          this.tabs[index].data = data;
        }
      },
      // 单击
      getDLdata(row) {
        this.row = row;
        this.selectedTabs(this.showTable);
        // this.$emit('changed', false);
        this.$emit('changed', {val:false,row:row});
      },
      // 售后单查询
      getCallCenterAfterSaleList(data) {
        if (!this.hasArguments) {
          this.$message.error('请输入查询条件');
          return;
        }
        this.$emit('changed', true);
        this.loading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleList', makeParam(this.formMap)).
            then(res => {
              this.tableDataOne = res.data.content.list;
              this.tableDataList = [];
            }).
            catch(() => {
            }).
            finally(() => {
              this.loading = false;
              this.row = null;
            });
      },
      // 售后单-问题商品
      getCallCenterAfterSaleGoodsList(data) {
        this.tabLoading = true;
        let params = {
          after_order_no: data.after_order_no,
        };
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleGoodsList', makeParam(params)).then(res => {
          this.tableDataList = res.body.content.list;
        }).catch(() => {
        }).finally(() => {
          this.tabLoading = false;
        });
      },
      // 售后单-处理进度
      getCallCenterAfterSaleHandleLogList(data) {
        this.tabLoading = true;
        let params = {
          after_order_no: data.after_order_no,
        };
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleHandleLogList', makeParam(params)).
            then(res => {
              this.tableDataList = res.body.content.list;
            }).
            catch(() => {
            }).
            finally(() => {
              this.tabLoading = false;
            });
      },
      // 售后单-业绩
      getCallCenterAfterSaleAchievementList(data) {
        this.tabLoading = true;
        let params = {
          after_order_no: data.after_order_no,
          merge_trade_no: data.merge_trade_no,
        };
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleAchievementList', makeParam(params)).
            then(res => {
              this.tableDataList = res.body.content.list;
            }).
            catch(() => {
            }).
            finally(() => {
              this.tabLoading = false;
            });
      },
      // 售后单-责任人
      getCallCenterAfterSaleDutyList(data) {
        this.tabLoading = true;
        let params = {
          source_order_no: data.after_order_no,
        };
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleDutyList', makeParam(params)).then(res => {
          this.tableDataList = res.body.content.list;
        }).catch(() => {
        }).finally(() => {
          this.tabLoading = false;
        });
      },
      // 售后单-操作记录
      getCallCenterAfterSaleOperationList(data) {
        this.tabLoading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterOperateLogByAfterNo', data.after_order_no).
            then(res => {
              this.tableDataList = res.body.content;
            }).
            catch(() => {
            }).
            finally(() => {
              this.tabLoading = false;
            });
      },
      // 弹窗时初始化
      reloadData(data, zindex) {
        this.zindex = zindex;
      },
      //重置
      resetForm() {
        for (let i in this.formMap) {
          this.formMap[i] = '';
        }
      },
    },
    created() {
      this.formMap['customer_name'] = this.sharedCustomer;
      this.formMap['receiver_mobile'] = this.sharedPhone;
      // this.getCallCenterAfterSaleList();
    },
  };
</script>

<style scoped>

  .form-box {
    margin-bottom: 10px;
  }

  .form-box .form-input {
    width: 620px;
    display: flex;
  }

  .form-input .form-inline {
    flex: 1;
  }

  .form-box .search-btn {
    float: right;
    margin-top: -20px;
  }

  .el-input {
    width: 100px !important;
  }

  .table-one {
    margin-bottom: 10px;
    height: 44%;
  }

  .table-two {
    height: 37%;
  }

  .xpt-flex, .el-table {
    height: 99% !important;
    display: flex;
    min-width: 100%;
    box-orient: vertical;
    flex-direction: column;
  }


</style>
