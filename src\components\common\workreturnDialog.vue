<template>
  <div class="xpt-flex">
    <div class="form-box">
      <div class="form-input" style="margin-bottom: 10px;">
        <div class="form-inline">
          <label>手机&#12288; </label>
          <el-input size="mini" v-model="formMap.receiver_mobile"></el-input>
        </div>
        <div class="form-inline">
          <label>买家昵称&#12288;</label>
          <el-input size="mini" v-model="formMap.customer_name"></el-input>
        </div>
        <div class="form-inline">
          <label>收货人</label>
          <el-input size="mini" v-model="formMap.receiver_name"></el-input>
        </div>
        <div class="form-inline">
          <label>合并单号</label>
          <el-input size="mini" v-model="formMap.merge_trade_no"></el-input>
        </div>
      </div>
      <div class="form-input">
        <div class="form-inline">
          <label>业务员</label>
          <el-input size="mini" v-model="formMap.salesman_name" icon="search" :on-icon-click="searchSalesUserCondition" readonly></el-input>
        </div>
        <div class="form-inline">
          <label>回访类型</label>
          <el-select v-model="formMap.visit_status" placeholder="请选择" size="mini" class="search-input">
            <el-option
              v-for="item in visitStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="form-inline">
        </div>
        <div class="form-inline">
        </div>
      </div>
      <div class="search-btn">
        <el-button type="primary" size='mini' @click="getCallCenterAfterSaleVisitList">查询</el-button>
        <el-button type="primary" size='mini' @click="resetForm">重置</el-button>
      </div>
    </div>
    <div class="table-one">
      <el-table
        highlight-current-row
        :data="tableDataOne"
        @row-click="getDLdata"
        border
        style="width: 100%;margin-bottom:10px;">
        <el-table-column :show-overflow-tooltip="true" prop="return_visit_no" label="回访单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="batch_trade_no" label="批次编号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="merge_trade_no" label="合并单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="customer_name" label="买家昵称"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="receiver_name" label="收货人"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="zd_delivery_time" label="发货时间" :formatter="formatTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="post_fee" label="运费"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="order_status" label="单据状态"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="post_fee_type" label="运费运费支付类型" width="150"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="salesman_name" label="业务员"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="salesman_group" label="分组"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="modify_time" label="状态更新时间" width="150" :formatter="formatTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="pre_arrival_time" label="预计到货时间" width="150" :formatter="formatTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="visit_reminder_date" label="回访提醒时间" width="150" :formatter="formatTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="volume_total" label="体积"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="create_time" label="创建时间" :formatter="formatTime"></el-table-column>
      </el-table>
    </div>
    <div class="table-two">
      <tabs :tabs="tabs" @selectedTabs="selectedTabs" :currentClass.sync="showTable"></tabs>
      <el-table
        :data="tableDataList"
        border
        style="width: 100%;">
        <el-table-column type="index"/>
        <template v-for="(col, idx) in cols[showTable]">
        <el-table-column :show-overflow-tooltip="true"
            :key="idx" :prop="col.prop" :label="col.label" width="150">
          <template slot-scope="scope">
            <span v-html="formatter(scope.row, col)" width="150"></span>
          </template>
        </el-table-column>
         </template>

      </el-table>
    </div>
    <!-- <div class="el-row xpt-pagation">
      <el-pagination layout="sizes, prev, pager, next"
        :page-sizes="[300, 600, 900, 1200]"
        :page-size="300"
        :total="20"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div> -->
  </div>
</template>

<script>
  import tabs from '@/components/common/tabs';
  import baseUrl, {makeUrl,makeParam} from '../call_system/base.js';
import Fn from '@/common/Fn.js'
  export default {
    components: {
      tabs,
    },
    props: ['job', 'params','sharedPhone','sharedCustomer'],
    watch:{
      'formMap.receiver_name'(val,oldVal){
        this.$emit('update:sharedCustomer', val);
      },
      'formMap.receiver_mobile'(val,oldVal){
        this.$emit('update:sharedPhone', val);
      },
      sharedPhone(val,oldVal){
        if (this.formMap.receiver_mobile !== val) {
          this.formMap.receiver_mobile = val;
        }
      },
      sharedCustomer(val,oldVal){
        if (this.formMap.customer_name !== val) {
          this.formMap.customer_name = val;
        }
      }
    },
    data() {
      let _this = this
      return {
        formMap: {
          merge_trade_no: null,
          customer_name: null,
          receiver_name: null,
          receiver_mobile: null,
          source_order_no: null,
          visit_status: null,
          salesman_name: null,
        },
        zindex: '', // 弹窗标识
        tabs: [
          {
            name: '问题商品',
            id: 'getCallCenterAfterSaleVisitGoodsList',
            data: null,
          },
          {
            name: '内部便签',
            id: 'getCallCenterAfterSaleVisitMemoList',
            data: null,
          },
          {
            name: '回访记录',
            id: 'getCallCenterAfterSaleVisitRecordList',
            data: null,
          },
        ],
        showTable: 0,
        tableDataOne: [],
        tableDataList: [],
        cols: {
          0: [
            {
              prop: 'material_number',
              label: '规格编码',
            }, {
              prop: 'material_name',
              label: '商品名称',
            }, {
              prop: 'material_specification',
              label: '规格名称',
            }, {
              prop: 'tid',
              label: '淘宝单号',
            }, {
              prop: 'count',
              label: '数量',
            }, {
              prop: 'package_number',
              label: '包件数',
            }, {
              prop: 'system_bill_no',
              label: '系统单号',
            }, {
              prop: 'shop_name',
              label: '店铺',
            }, {
              prop: 'act_price',
              label: '实际售价',
            }, {
              prop: 'cost_price',
              label: '成本价',
            }, {
              prop: 'commit_time',
              label: '承诺发货时间',
              formattor(val){
               return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
              }
            }, {
              prop: 'lot',
              label: '批号',
            }],
          1: [
            {
              prop: 'merge_trade_no',
              label: '合并单号',
            }, {
              prop: 'creator',
              label: '用户',
            }, {
              prop: 'create_time',
              label: '操作时间',
              formattor(val){
               return  Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
              }
            }, {
              prop: 'subject',
              label: '主题',
            }, {
              prop: 'content',
              label: '内容',
            }],
          2: [
            {
              prop: 'merge_trade_no',
              label: '合并单号',
            }, {
              prop: 'batch_trade_no',
              label: '批次号',
            }, {
              prop: 'visitor_name',
              label: '回访人',
            }, {
              prop: 'visit_type',
              label: '回访类型'
            }, {
              prop: 'visit_time',
              label: '回访时间',
              formattor(val){
               return  Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
              }
            }, {
              prop: 'visit_remark',
              label: '回访备注',
            }, {
              prop: 'visit_reminder_date',
              label: '回访提醒时间',
              formattor(val){
               return  Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
              }
            }, {
              prop: 'sms_template',
              label: '短信模板',
            }],
        },
        loading: true,
        tabLoading: false,
        row: null,
        visitStatus: [
          {
            label: '待回访',
            value: 'STAYBACK',
          }, {
            label: '已回访',
            value: 'BACKED',
          },
        ],
      };
    },
    computed: {
      hasArguments() {
        for (let key in this.formMap) {
          if (!!this.formMap[key]) {
            // 至少一个查询参数有值
            return true;
          }
        }
        return false;
      },
    },
    methods: {
      formatter(row, col){
        if (!!col.formattor) {
          return col.formattor(row[col.prop]);
        }
       return row[col.prop];
      },
      formatTime(row, col, val){
       return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
      },
      formatDate(row, col, val){
       return Fn.dateFormat(val)
      },
      selectedTabs(index) {
        this.showTable = index;
        if (null != this.row && this.row != this.tabs[index].data) {
          this[this.tabs[index].id](this.row); // 调用对应tabs的请求方法
          let data = {};
          for (let i in this.row) {
            data[i] = this.row[i];
          }
          this.tabs[index].data = data;
        }
      },
      // 单击
      getDLdata(row) {
        this.row = row;
        this.selectedTabs(this.showTable);
        // this.$emit("changed", false)
        this.$emit('changed', {val:false,row:row});
      },
      // 回访单查询
      getCallCenterAfterSaleVisitList() {
        if (!this.hasArguments) {
          this.$message.error('请输入查询条件');
          return;
        }
        this.$emit("changed", true)
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleVisitList', makeParam(this.formMap)).then(res => {
          this.tableDataOne = res.body.content.list;
          if (this.tableDataOne.length > 0) {
            this.getCallCenterAfterSaleVisitGoodsList(this.tableDataOne[0]);
          }

          this.tableDataList = []
        }).catch(() => {
          this.row = null
        });
      },
      // 回访单-问题商品
      getCallCenterAfterSaleVisitGoodsList(data) {
        let params = {
          return_visit_no: data.return_visit_no,
        };
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleVisitGoodsList', makeParam(params)).
            then(res => {
              this.tableDataList = res.body.content.list;
            }).
            catch(() => {
            }).
            finally(() => {
              this.loading = false;
            });
      },
      // 回访单-内部便签
      getCallCenterAfterSaleVisitMemoList(data) {
        let params = {
          merge_trade_no: data.merge_trade_no,
        };
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleVisitMemoList', makeParam(params)).
            then(res => {
              this.tableDataList = res.body.content.list;
            }).
            catch(() => {
            }).
            finally(() => {
              this.loading = false;
            });
      },
      // 回访记录
      getCallCenterAfterSaleVisitRecordList(data) {
        let params = {
          merge_trade_no: data.merge_trade_no,
          return_visit_no: data.return_visit_no,
        };
        this.$http.post('/external-web/api/callCenter/getCallCenterAfterSaleVisitRecordList', makeParam(params)).
            then(res => {
              this.tableDataList = res.body.content.list;
            }).
            catch(() => {
            }).
            finally(() => {
              this.loading = false;
            });
      },
      // 弹窗时初始化
      reloadData(data, zindex) {
        this.zindex = zindex;
      },
      //重置
      resetForm() {
        for (let i in this.formMap) {
          this.formMap[i] = '';
        }
      },
      searchSalesUserCondition(){
        var _this = this;
        this.$root.eventHandle.$emit('alert',{
          component:()=>import('@components/system_management/list.vue'), close:function(){

          },
          style:'width:900px;height:600px',
          title:'请选择用户信息',
          params:{
            type:'EMPLOYEE',
            status:1,//生效人员
            isEnable:1,//生效时间
            page_name: 'cloud_user_person',
            where: [],
            callback(b){
              var data = b.data;
              _this.formMap.salesUserName = data.fullName
              _this.formMap.salesUserId = data.employeeNumber
              console.log(data)
            }
          }});
      },
    },
    created() {
      this.formMap['customer_name'] = this.sharedCustomer;
      this.formMap['receiver_mobile'] = this.sharedPhone;
      // this.getCallCenterAfterSaleVisitList();
    },
  };
</script>

<style scoped>

  .form-box {
    margin-bottom: 10px;
  }

  .form-box .form-input {
    width: 660px;
    display: flex;
  }

  .form-input .form-inline {
    flex: 1;
  }

  .form-box .search-btn {
    float: right;
    margin-top: -40px;
  }

  .el-input {
    width: 100px !important;
  }

  .table-one {
    margin-bottom: 10px;
    height: 40%;
  }

  .table-two {
    height: 37%;
  }

  .xpt-flex, .el-table {
    height: 99% !important;
    display: flex;
    min-width: 100%;
    box-orient: vertical;
    flex-direction: column;
  }
  .search-input{
    margin-bottom: 10px;
    width: 105px;
  }

</style>
