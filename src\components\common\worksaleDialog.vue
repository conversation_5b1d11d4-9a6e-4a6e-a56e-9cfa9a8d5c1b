<template>
  <div class="xpt-flex" v-loading="loading">
    <div class="form-box">
      <div class="form-input" style="margin-bottom: 10px;">
        <div class="form-inline">
          <label>淘宝单号</label>
          <el-input size="mini" v-model="formMap.tid"></el-input>
        </div>
        <div class="form-inline">
          <label>买家昵称</label>
          <el-input size="mini" v-model="formMap.customer_name"></el-input>
        </div>
        <div class="form-inline">
          <label>销售单号</label>
          <el-input size="mini" v-model="formMap.sys_trade_no"></el-input>
        </div>
        <div class="form-inline">
          <label>收货人号码</label>
          <el-input size="mini" v-model="formMap.receiver_mobile"></el-input>
        </div>
      </div>
      <div class="form-input">
        <div class="form-inline">
          <label>合并单号</label>
          <el-input size="mini" v-model="formMap.merge_trade_no"></el-input>
        </div>
      </div>
      <div class="search-btn">
        <el-button type="primary" size='mini' @click="getCallCenterSaleOrderList">查询</el-button>
        <el-button type="primary" size='mini' @click="resetForm">重置</el-button>
      </div>
    </div>
    <div class="table-one">
      <el-table
        highlight-current-row
        :data="tableDataOne"
        @row-click="getDLdata"
        border
        style="width: 100%;margin-bottom:10px;">
        <el-table-column :show-overflow-tooltip="true" prop="sys_trade_no" label="销售单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="merge_trade_no" label="合并单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="tid" label="淘宝单号"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="customer_name" label="买家昵称"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="shop_name" label="店铺名称"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="pay_time" label="支付时间" :formatter="formatTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="created" label="拍单时间" :formatter="formatTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="lock_time" label="锁定日期" :formatter="formatDate"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="salesman_name" label="业务员"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="best_user_name" label="推荐业务员"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="group_name" label="分组"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="big_group_name" label="大分组"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="seller_memo" label="卖家备注"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="buyer_memo" label="买家备注"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="discount" label="优惠券金额"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="status" label="订单状态"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="step_trade_status" label="阶段付款订单状态" width="150"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="off_line_amount" label="线下支付金额" width="150"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="share_discount" label="已使用优惠券" width="150"></el-table-column>
      </el-table>
    </div>
    <div class="table-two">
      <tabs :tabs="tabs" @selectedTabs="selectedTabs" :currentClass.sync="showTable"></tabs>
      <el-table
        :data="tableDataList"
        border
        style="width: 100%;" v-loading="tabLoading">
        <el-table-column type="index"/>
        <template v-for="(col,idx) in cols[showTable]">
         <el-table-column :show-overflow-tooltip="true"
            :key="idx" :prop="col.prop" :label="col.label" width="150">
          <template slot-scope="scope">
            <span v-html="formatter(scope.row, col)" width="150"></span>
          </template>
        </el-table-column>
        </template>

      </el-table>
    </div>
    <!--<div class="el-row xpt-pagation">
      <el-pagination layout="sizes, prev, pager, next"
        :page-sizes="[300, 600, 900, 1200]"
        :page-size="300"
        :total="20"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>-->
  </div>
</template>

<script>
  import tabs from '@/components/common/tabs';
  import baseUrl, {makeUrl,makeParam} from '../call_system/base.js';
import Fn from '@/common/Fn.js'
  export default {
    props: ['job', 'params','sharedPhone','sharedCustomer'],
    components: {
      tabs,
    },
    watch:{
      'formMap.receiver_name'(val,oldVal){
        this.$emit('update:sharedCustomer', val);
      },
      'formMap.receiver_mobile'(val,oldVal){
        this.$emit('update:sharedPhone', val);
      },
      sharedPhone(val,oldVal){
        if (this.formMap.receiver_mobile !== val) {
          this.formMap.receiver_mobile = val;
        }
      },
      sharedCustomer(val,oldVal){
        if (this.formMap.customer_name !== val) {
          this.formMap.customer_name = val;
        }
      }
    },
    data() {
      return {
        formMap: {
          tid: null,
          customer_name: null,
          sys_trade_no: null,
          receiver_mobile: null,
          merge_trade_no: null,
        },
        zindex: '', // 弹窗标识
        tabs: [
          {
            id: 'getCallCenterSaleOrderGoodsList',
            name: '商品信息',
          },
        ],
        showTable: 0,
        tableDataOne: [],
        tableDataList: [],
        cols: {
          0: [
            {
              prop: 'material_number',
              label: '物料编码',
            }, {
              prop: 'material_name',
              label: '物料名称',
            }, {
              prop: 'material_specification',
              label: '规格描述',
            }, {
              prop: 'number',
              label: '数量',
            }, {
              prop: 'package_number',
              label: '包件数量',
            }, {
              prop: 'warehouse_name',
              label: '仓库',
            }, {
              prop: 'address_name',
              label: '地址',
            }, {
              prop: 'stand_price',
              label: '标准售价',
            }, {
              prop: 'act_price',
              label: '实际售价',
            }, {
              prop: 'volume',
              label: '体积',
            }, {
              prop: 'post_fee',
              label: '包邮费',
            }, {
              prop: 'unit_price',
              label: '单价',
            }, {
              prop: 'post_fee',
              label: '包邮费',
            }, {
              prop: 'commit_time',
              label: '承诺发货时间',
              formattor(val){
                return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
              }
            }, {
              prop: 'coupons',
              label: '优惠卷',
            }, {
              prop: 'is_gift',
              label: '赠品',
            }],
        },
        loading: false,
        tabLoading: false,
        row: null,
      };
    },
    computed: {
      hasArguments() {
        for (let key in this.formMap) {
          if (!!this.formMap[key]) {
            // 至少一个查询参数有值
            return true;
          }
        }
        return false;
      },
    },
    methods: {
       formatter(row, col){
        if (!!col.formattor) {
          return col.formattor(row[col.prop]);
        }
       return row[col.prop];
      },
      formatTime(row, col, val){
        return Fn.dateFormat(val, 'yyyy-MM-dd hh:mm:ss')
      },
      formatDate(row, col, val){
        return Fn.dateFormat(val)
      },
      selectedTabs(index) {
        this.showTable = index;
        if (null != this.row && this.row != this.tabs[index].data) {
          this[this.tabs[index].id](this.row); // 调用对应tabs的请求方法
          let data = {};
          for (let i in this.row) {
            data[i] = this.row[i];
          }
          this.tabs[index].data = data;
        }
      },
      // 单击
      getDLdata(row) {
        this.row = row;
        this.selectedTabs(this.showTable);
        // this.$emit("changed", false)
        new Promise((reslove,reject)=>{
          this.$emit('changed', {val:false,row:row,reslove:reslove});
        }).then(res=>{
          // console.log(res,"res");
          this.tableDataOne.forEach(item=>{
            if(item == row){
              item.best_user_name = res;
            }
          })
        })
      },
      // 销售订单查询
      getCallCenterSaleOrderList() {
        if (!this.hasArguments) {
          this.$message.error('请输入查询条件');
          return;
        }
        this.$emit("changed", true)
        this.loading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterSaleOrderList', makeParam(this.formMap)).then(res => {
          this.tableDataOne = res.data.content.list;
           this.tableDataList = []
        }).catch(() => {
        }).finally(() => {
          this.loading = false;
          this.row = null
        });
      },
      // 销售订单-商品信息
      getCallCenterSaleOrderGoodsList() {
        this.tabLoading = true;
        this.$http.post('/external-web/api/callCenter/getCallCenterSaleOrderGoodsList',
            makeParam({sys_trade_no: this.row.sys_trade_no})).then(res => {
          this.tableDataList = res.data.content.list;
        }).catch(() => {
        }).finally(() => {
          this.tabLoading = false;
        });
      },
      // 弹窗时初始化
      reloadData(data, zindex) {
        this.zindex = zindex;
      },
      //重置
      resetForm() {
        for (let i in this.formMap) {
          this.formMap[i] = '';
        }
      },
    },
    created() {
      this.formMap['customer_name'] = this.sharedCustomer;
      this.formMap['receiver_mobile'] = this.sharedPhone;
      // this.getCallCenterSaleOrderList();
    },
  };
</script>

<style scoped>

  .form-box {
    margin-bottom: 10px;
  }

  .form-box .form-input {
    width: 660px;
    display: flex;
  }

  .form-input .form-inline {
    flex: 1;
  }

  .form-box .search-btn {
    float: right;
    margin-top: -40px;
  }

  .el-input {
    width: 100px !important;
  }

  .table-one {
    margin-bottom: 10px;
    height: 40%;
  }

  .table-two {
    height: 37%;
  }

  .xpt-flex, .el-table {
    height: 99% !important;
    display: flex;
    min-width: 100%;
    box-orient: vertical;
    flex-direction: column;
  }

</style>
