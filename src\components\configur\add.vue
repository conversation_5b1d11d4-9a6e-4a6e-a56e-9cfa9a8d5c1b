<!-- 商品配置详情、新增 copy discount/add.vue -->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
      <el-button type="primary" size="mini" @click="presave" :loading="isLoading" >保存</el-button>
      <el-button type="primary" size="mini" @click="init" :loading="isLoading" >刷新</el-button>
      </el-col>
    </el-row>
    <div>
		  <el-row	:gutter='40' >
        <el-tabs v-model="firstTab">
          <el-tab-pane label='基本信息' name='basic_info' class='xpt-flex' style="overflow:hidden;">
				<el-form label-position="right" class="mgt10" label-width="100px" :model="form" :rules="rules" ref="form">
					<el-col :span="12" style="padding-bottom: 22px;">
					<el-form-item label="配置器编码" prop="configurator_code">
						<el-input v-model="form.configurator_code" size='mini' :disabled="form.configurator_id ? true : false"></el-input>
							<el-tooltip v-if='rules.configurator_code[0].isShow' class="item" effect="dark" :content="rules.configurator_code[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
					</el-form-item>
					<el-form-item label="销售单位"  prop="sale_unit_code">
						<!-- <el-input v-model="form.sale_unit_code" size='mini'></el-input> -->
						<el-input size='mini' icon="search" :on-icon-click='openSaleUnitList' v-model="form.sale_unit_code" readonly :disabled="!!form.configurator_id"></el-input>

						<el-tooltip v-if='rules.sale_unit_code[0].isShow' class="item" effect="dark" :content="rules.sale_unit_code[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="配置器类" prop="configurator_class">
						<!-- <el-input v-model="form.configurator_class" size='mini' style="width:100%;"></el-input> -->
						<el-select	size='mini' v-model="form.configurator_class" >					
							<el-option
								v-for="item in configurator_option"
								:key="item.value"
								:label="item.label"
								:value="item.value">
							</el-option>
						</el-select>
							<el-tooltip v-if='rules.configurator_class[0].isShow' class="item" effect="dark" :content="rules.configurator_class[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
					</el-form-item>
					<el-form-item label="说明" prop="description">
						<el-input v-model="form.description" type="textarea" resize="none" size='mini'></el-input>
											<el-tooltip v-if='rules.description[0].isShow' class="item" effect="dark" :content="rules.description[0].message" placement="right-start" popper-class='xpt-form__error'>
												<i class='el-icon-warning'></i>
											</el-tooltip>
					</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="配置器名称"  prop="configurator_name">
							<el-input v-model="form.configurator_name" size='mini'></el-input>
							<el-tooltip v-if='rules.configurator_name[0].isShow' class="item" effect="dark" :content="rules.configurator_name[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="结算单位"  prop="clean_uint_code">
								<el-input size='mini' icon="search" :on-icon-click='openUnitList' v-model="form.clean_uint_code" readonly :disabled="!!form.configurator_id"></el-input>
								<!-- <el-input v-model="form.clean_uint_code" size='mini'></el-input> -->
								<el-tooltip v-if='rules.clean_uint_code[0].isShow' class="item" effect="dark" :content="rules.clean_uint_code[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
						</el-form-item>
            <el-form-item label="金额分类"  prop="amount_classify">
                <el-select	size='mini' v-model="form.amount_classify" :disabled="!!form.configurator_id">					
                    <el-option key="DJ" label="订金金额" value="DJ"> </el-option>
                    <el-option key="ACT" label="实际金额" value="ACT"> </el-option>
                </el-select>
                <el-tooltip v-if='rules.amount_classify[0].isShow' class="item" effect="dark" :content="rules.amount_classify[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip> 
						</el-form-item>
            <el-form-item label="需要审核"  prop="if_need_audit">
              <el-switch on-text="是" off-text="否" :value="{'Y':true,'N':false}[form.if_need_audit]"  disabled></el-switch>
              <el-tooltip v-if='rules.if_need_audit[0].isShow' class="item" effect="dark" :content="rules.if_need_audit[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
					</el-col>
				</el-form>
          </el-tab-pane>
        </el-tabs>
      </el-row>
    </div>
    <el-row class='xpt-flex__bottom'>
      <el-tabs v-model="secondTab">
        <el-tab-pane label='属性列表' name="configList">
          <xpt-list
            ref='configList'
            :data='configList'
            :colData='configCols'
        		:btns='btns'
            selection='radio'
            :isNeedClickEvent='true'
            @radio-change="selectconfigList"
            :orderNo="false"
          ></xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    
  </div>
</template>
<script>
  import validate from '@common/validate.js'
  import Fn from '@common/Fn.js'
 
  export default {
    props: ['params'],
    data() {
      var self = this;
      return {
        isLoading:false,
        firstTab: 'basic_info',
        secondTab: 'configList',
        btns:[
          {
            type: 'primary',
            txt: '新增',
            click: self.addFun
          }, 
		//   {
        //     type: 'primary',
        //     txt: '生效',
        //     click: () => {
		// 					self.operatoeConfigur('Y')
		// 				}
        //   }, {
        //     type: 'primary',
        //     txt: '失效',
        //     click: () => {
		// 					self.operatoeConfigur('N')
		// 				}
        //   }
        ],
        configurator_option:[
				// {
				// 	label: '导轨',
				// 	value: 'GUIDE_CONFIG_ABSTRACTIMPL',
				// },{
				// 	label: '成品窗帘',
				// 	value: 'WINDOW_CURTAINS_CONFIG_ABSTRACTIMPL',
				// },
        ],
        form: {
          configurator_id:null,
          configurator_code:'',//配置器编码
          configurator_name:'',//配置器名称
          description:'',//备注
          configurator_class:'',//配置器类
          sale_unit_code:'',//商品销售单位
          clean_uint_code:'',//商品结算单位
          amount_classify:'ACT', //金额分类
          if_need_audit: 'N',
        },
        rules:{
          if_need_audit:validate.isNotBlank({
            required:true,
            self:self,
            msg:"请选择需要审核",
            trigger:"change"
          }),
          amount_classify:validate.isNotBlank({
            required:true,
            self:self,
            msg:"请填写金额分类",
            trigger:"change"
          }),
          configurator_code:[{
            required:true,
            message:'配置器编码必须是大写字母、_的组合格式，必填。',
            isShow:false,
            validator: function(rule, value, callback) {
              if (value) {
                  if (/^[A-Z_]+$/.test(value)) {
                      self.rules[rule.field][0].isShow = false
                      callback();
                  } else {
                      self.rules[rule.field][0].isShow = true
                      self.rules[rule.field][0].message = '配置器编码必须是大写字母、_的组合格式'
                      callback(new Error(''));
                      self.setActiveTab && self.setActiveTab();
                  }
              } else {
                  self.rules[rule.field][0].isShow = true
                  callback(new Error(''));
                  self.setActiveTab && self.setActiveTab();
              }
            }
          }],
          configurator_name:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请填写配置器名称",
						trigger:"change"
          }),
          description:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请填写备注",
						trigger:"change"
          }),
          configurator_class:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请填写配置器类",
						trigger:"change"
          }),
          sale_unit_code:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请填写商品销售单位",
						trigger:"change"
          }),
          clean_uint_code:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请填写商品结算单位",
						trigger:"change"
          }),
        },
       configList:[],
       configCols:[
        {
					label: '序号',
					prop: 'seq_no',
        },{
					label: '编码',
					prop: 'option_code',
					width: 100
        },{
					label: '名称',
					prop: 'option_name',
					width: 100
				},{
					label: '属性类型',
					prop: 'option_type',
          width: 100,
          format: 'auxFormat',
					formatParams: 'config_option_type'
				},{
					label: '查询值类型',
					prop: 'lookup_type',
          width: 200,
          redirectClick(row) {
            self.lookAuxiliary(row)
					},
				},{
					label: '数据类别',
					prop: 'data_category',
          width: 100,
          format: 'auxFormat',
					formatParams: 'config_display_type'
				},{
					label: '有效性',
					prop: 'is_available',
					width: 100,
					formatter(val) {
						switch(val) {
							case 'Y': return '生效'; break;
							case 'N': return '失效'; break;
						}
					}
				},{
					label: '是否必填',
					prop: 'is_required',
					width: 100,
					formatter(val) {
						switch(val) {
							case 'Y': return '是'; break;
							case 'N': return '否'; break;
						}
					}
				},{
					label: '长度',
					prop: 'length',
					width: 100
				},{
					label: '精度',
					prop: 'option_precision',
					width: 100
				},{
					label: '最大值',
					prop: 'max_value',
					width: 100
				},{
					label: '最小值',
					prop: 'min_value',
					width: 100
				},{
					label: '是否可调整',
					prop: 'if_need_adjust',
					formatter (val) {
            return val === 'Y' ? '是' : '否'
          }
				},{
					label: '默认值',
					prop: 'default_value'
				},{
					label: '备注',
					prop: 'description',
					width: 100
				},
       ],
       configSelect: ''
      }
    },
    methods: {
      lookAuxiliary(row) {
        let self = this
        if(!row.lookup_type) return
        this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryCategoryList', {
          page: {length: 50, pageNo: 1},
          page_name: "cloud_auxiliary_category",
          where: [
            {
              condition: "AND",
              field: "ebfbc7a9d21ea1d26868b6b331afd07e",
              listWhere: [],
              operator: "=",
              table: "c982ba0edaab6ffe3ebc4851a8c1e568",
              value: row.lookup_type,
            }
          ]
        },(response)=>{
					if(response.body.result){
            if (response.body.content && response.body.content.list && response.body.content.list.length > 0) {
              self.$root.eventHandle.$emit('creatTab',{
                name:'编辑辅助资料类别',
                params:response.body.content.list[0],
                component:()=>import('@components/auxiliary/auxiliary.vue')
              })
            }
				  	
          }
				},e=>{
				})
        
      },
      getConfigurOption(){
        this.ajax.postStream('/plan-web/api/Configuration/getList',{
				page_no:1,
				page_size: 500,
				page_name: 'scm_product_configurator',
				where: []
        }, (res) => {
          if(res.body.result && res.body.content && res.body.content.list && res.body.content.list.length > 0){
            let list = []
            res.body.content.list.forEach(item => {
                list.push({
                    label: item.configurator_name.indexOf('配置器') > 0 ? item.configurator_name.slice(0, -3) : item.configurator_name,
                    value: item.configurator_class
                })
            })
            this.configurator_option = JSON.parse(JSON.stringify(list))
        }else{
					this.configurator_option = []
				}
			}, (err) => {
				this.configurator_option = []
			});
        },
      	selectconfigList(val){
				this.configSelect = val
			},
			operatoeConfigur(type) {
				let status = type == 'Y' ? '已生效' : '已失效', msg = ''
				if (!this.configSelect) {
					msg = '请选择一行属性配置'
				} else if (this.configSelect.is_available == type) {
					msg = status + '不可重复操作'
				}
				if(msg) {
					this.$message.error(msg)
					return
				}
				this.ajax.postStream('/plan-web/api/Option/saveOption',{config_option_id: this.configSelect.config_option_id, is_available: type},(response)=>{
					if(response.body.result){
				  	this.$message.success(response.body.msg)
						this.getConfigurList(this.form.configurator_id)
          }
					this.isLoading=false
				},e=>{
					this.isLoading=false
					this.$message.error(e)
				})
			},
      presave() {
        var self = this;
				self.$refs.form.validate((valid) => {
					if(!valid) return
					this.save(null, false);
				})
      },
      save(callback, isClose) {
        var self = this;
        self.isLoading=true;
				var url = "/plan-web/api/Configuration/saveConfiguration";
				let saveData = JSON.parse(JSON.stringify(this.form));
				this.ajax.postStream(url,/*self.submitData*/saveData,(response)=>{
					if(response.body.result){
				  	this.$message.success(response.body.msg)
						// self.goodsDetail(response.body.content.configurator_id)
						this.form = response.body.content

          			}else{
				  	this.$message.error(response.body.msg)

					}
					this.isLoading=false
				},e=>{
					this.isLoading=false
					this.$message.error(e)
				})

        
      },
      // 获取详情
      getData(id,code) {
        this.ajax.postStream('/plan-web/api/Configuration/ConfigurationDetail',{configurator_id:id,configurator_code:code},(response)=>{
          if(response.body.result){
              this.$message.success(response.body.msg)
              console.log(response.body.content);
              this.form = response.body.content
          }
				},e=>{
					this.$message.error(e)
				})
			},
			// 获取属性列表数据
			getConfigurList(id) {
				this.ajax.postStream('/plan-web/api/Option/getList',{configurator_id:id},(response)=>{
					if(response.body.result){
				  	this.$message.success(response.body.msg)
            this.configList = response.body.content.list
          }
				},e=>{
					this.$message.error(e)
				})
			},
      addFun(){
				if (!this.form.configurator_id) {
					this.$message.error('请先操作表头保存按钮保存基本信息')
					return
				}
				let self = this
        	this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/configur/addConfig'),
            style:'width:900px;height:600px',
            title: '添加属性',
            params: {
							configurator_id: this.form.configurator_id,
              callback(configurator_id){
                self.getConfigurList(configurator_id)
              }
            },
          })
      },


      close() {
        let hasUpdate = this.compareData(this.form, this.params.__data),
          self = this;
        if (hasUpdate) {
          this.$root.eventHandle.$emit('openDialog', {
            ok() {
              self.save(() => {
                self.$root.eventHandle.$emit('removeTab', self.params.tabName)
              }, true)
            },
            no() {
              self.$root.eventHandle.$emit('removeTab', self.params.tabName)
            }
          })
        } else {
          this.$root.eventHandle.$emit('removeTab', this.params.tabName)
        }
      },
      init() {
			let id = this.params.configurator_id, code = this.params.configurator_code
			this.getData(id, code)
			this.getConfigurList(id)
		},
		openUnitList(){
			if(!!this.form.configurator_id){
				return false;
			}
			let params = {}, self = this;
				params.callback = d => {
					if(d) {
						console.log(d);
						self.form.clean_uint_code = d.material_unit;
					}
				}
			self.$root.eventHandle.$emit('alert', {
				params: params,
				component: () => import('@components/configur/getunit.vue'),
				style: 'width:800px;height:500px',
				title: '单位列表'
			});
		},
		openSaleUnitList(){
			if(!!this.form.configurator_id){
				return false;
			}
			let params = {}, self = this;
				params.callback = d => {
					if(d) {
						console.log(d);
						self.form.sale_unit_code = d.material_unit;
					}
				}
			self.$root.eventHandle.$emit('alert', {
				params: params,
				component: () => import('@components/configur/getunit.vue'),
				style: 'width:800px;height:500px',
				title: '单位列表'
			});
		},
    },
    mounted() {
      if(!!this.params.configurator_id){
			this.init()
      }
      this.getConfigurOption()
    },
   
  }
</script>
