<!-- 新增商品配置 -->
<template>
	<div>
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type='primary' size='mini' @click="preSave('submitData')" :loading="isLoading" :disabled='isLoading'>保存</el-button >
			</el-col>
		</el-row>
		<el-row	:gutter='40' > 
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基本" name="baseInfo">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="submitData" :rules="rules" ref="submitData">
						<el-col :span='12'>
							<el-form-item label="编码" prop="option_code">
								<el-input size='mini' v-model="submitData.option_code"></el-input>
								<el-tooltip v-if='rules.option_code[0].isShow' class="item" effect="dark" :content="rules.option_code[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="名称" prop="option_name">
								<el-input size='mini' v-model="submitData.option_name"></el-input>
								<el-tooltip v-if='rules.option_name[0].isShow' class="item" effect="dark" :content="rules.option_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="属性类型" prop="option_type">
                <xpt-select-aux v-model='submitData.option_type' aux_name='config_option_type' @change="changeOptionType"></xpt-select-aux>
								<el-tooltip v-if='rules.option_type[0].isShow' class="item" effect="dark" :content="rules.option_type[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="查询值类型" prop="lookup_type">
                  <el-autocomplete
                    size="mini"
                    class="inline-input"
                    v-model="submitData.lookup_type"   
                    :fetch-suggestions="querySearch"
                    placeholder="请输入内容"
                    :disabled="optionTypeDisabled"
                    :validateevent="false"
                    @select="handleSelect"
                    @blur="getUserList"
                    :trigger-on-focus="false"
                  ></el-autocomplete>
                  <!-- <el-input size='mini' icon="search" :on-icon-click='openOptionType' v-model="submitData.lookup_type" readonly :disabled="optionTypeDisabled"></el-input> -->
                <el-tooltip v-if='rules.lookup_type[0].isShow' class="item" effect="dark" :content="rules.lookup_type[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
              <el-form-item label="数据类别" prop="data_category">
                <xpt-select-aux v-model='submitData.data_category' aux_name='config_display_type' ></xpt-select-aux>
                <el-tooltip v-if='rules.data_category[0].isShow' class="item" effect="dark" :content="rules.data_category[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
              </el-form-item>
							<el-form-item label="有效性" prop="is_available">
								<el-switch v-model="submitData.is_available" on-text="是" off-text="否" on-value="Y" off-value="N"></el-switch>
							</el-form-item>
							<el-form-item label="是否必填" prop="is_required">
								<el-switch v-model="submitData.is_required" on-text="是" off-text="否" on-value="Y" off-value="N"></el-switch>
							</el-form-item>
              <el-form-item label="是否可调整" prop="if_need_adjust">
								<el-switch on-text="是" off-text="否" v-model="submitData.if_need_adjust" on-value="Y" off-value="N" :disabled="!/^(NUMERICAL)$/.test(submitData.option_type)"></el-switch>
							</el-form-item>
						</el-col>
						<el-col :span='10'>
              <el-form-item label="序号" prop="seq_no">
								<el-input size='mini' v-model="submitData.seq_no"></el-input>
								<el-tooltip v-if='rules.seq_no[0].isShow' class="item" effect="dark" :content="rules.seq_no[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="长度" prop="length">
								<el-input size='mini' v-model="submitData.length" :disabled="/^(LOOKUP|CHARACTER)$/.test(submitData.option_type)"></el-input>
                <el-tooltip v-if='rules.length[0].isShow' class="item" effect="dark" :content="rules.length[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="精度" prop="option_precision">
								<el-input size='mini' v-model="submitData.option_precision" :disabled="/^(LOOKUP|CHARACTERS)$/.test(submitData.option_type)"></el-input>
                  <el-tooltip v-if='rules.option_precision[0].isShow' class="item" effect="dark" :content="rules.option_precision[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="最大值" prop="max_value">
								<el-input size='mini' v-model="submitData.max_value" :disabled="/^(LOOKUP|CHARACTERS)$/.test(submitData.option_type)"></el-input>
								<el-tooltip v-if='rules.max_value[0].isShow' class="item" effect="dark" :content="rules.max_value[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="最小值" prop="min_value">
								<el-input size='mini' v-model="submitData.min_value" :disabled="/^(LOOKUP|CHARACTERS)$/.test(submitData.option_type)"></el-input>
								<el-tooltip v-if='rules.min_value[0].isShow' class="item" effect="dark" :content="rules.min_value[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="备注" prop="description">
								<el-input size='mini' v-model="submitData.description"></el-input>
							</el-form-item>
              <el-form-item label="默认值" prop="default_value">
								<el-input v-model="submitData.default_value" size='mini'></el-input>
                <el-tooltip v-if='rules.default_value[0].isShow' class="item" effect="dark" :content="rules.default_value[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>	
			    </el-form>
			  </el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js';
	import Fn from '@common/Fn.js';
	export default {
		props:["params"],
		data() {
			var _this = this;
			var self = this;
			return{
				firstTab:"baseInfo",
				timeout:{},
				submitData:{
					option_code:"",//编码
					option_name:"",//名称
					option_type:"",//属性类型
					lookup_type:"",//查询值类型
					is_available:"Y",//有效性
					is_required:"Y",//是否必填
					length:'',//长度
					option_precision:'',//精度
					max_value:'',//最大值
					min_value:'',//最小值
          description:'',//备注
          if_need_adjust: 'N',
          data_category: '',
          default_value: ''
				},

				lookup_type_options:[
					{
						label:'',
						value:''
					}
				],
				rules:{
					option_code:[{
						required:true,
						message:'配置器编码必须是大写字母、_的组合格式，必填。',
						isShow:false,
						validator: function(rule, value, callback) {
                            if (value) {
                                if (/^[A-Z_]+$/.test(value)) {
                                    self.rules[rule.field][0].isShow = false
                                    callback();
                                } else {
                                    self.rules[rule.field][0].isShow = true
                                    self.rules[rule.field][0].message = '配置器编码必须是大写字母、_的组合格式'
                                    callback(new Error(''));
                                    self.setActiveTab && self.setActiveTab();
                                }
                            } else {
                                self.rules[rule.field][0].isShow = true
                                callback(new Error(''));
                                self.setActiveTab && self.setActiveTab();
                            }
                        }
					}],
					seq_no:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入名称",
						trigger:"change"
					}),
					option_name:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入名称",
						trigger:"change"
          }),
          data_category:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入名称",
						trigger:"change"
          }),
          default_value:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入名称",
						trigger:"change"
					}),
					option_type:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入属性类型",
						trigger:"change"
                    }),
                    lookup_type: [{
						required:false,
						validator: (rule, value, callback) => {
							if(/^(LOOKUP)$/.test(this.submitData.option_type) && !value){
								this.rules[rule.field][0].required  = true
								this.rules[rule.field][0].isShow = true
								callback(new Error(''))
							}else {
                                this.rules[rule.field][0].required  = false
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						self:_this,
						trigger: 'blur',
						isShow: false,
						message: '查询值类型不能为空',
                    }],
                    length:[{
						required:false,
						validator: (rule, value, callback) => {
							if(/^(CHARACTERS|NUMERICAL)$/.test(this.submitData.option_type)){
                                if (value) {
                                    if (/^(0|([1-9][0-9]+)|([1-9]))$/.test(value)) {
                                        self.rules[rule.field][0].isShow = false
                                        callback();
                                    } else {
                                        self.rules[rule.field][0].isShow = true
                                        self.rules[rule.field][0].message = '长度只能输入大于等于0的整数'
                                        callback(new Error(''));
                                        self.setActiveTab && self.setActiveTab();
                                    }
                                } else {
                                    this.rules[rule.field][0].required  = true
                                    this.rules[rule.field][0].isShow = true
                                    callback(new Error(''))
                                }
							}else {
                                this.rules[rule.field][0].required  = false
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						self:_this,
						trigger: 'blur',
						isShow: false,
						message: '长度不能为空',
                    }],
                    option_precision:[{
						required:false,
						validator: (rule, value, callback) => {
							if(/^(NUMERICAL)$/.test(this.submitData.option_type)){
                                if (value) {
                                    if (/^\d+$/.test(value)) {
                                        self.rules[rule.field][0].isShow = false
                                        callback();
                                    } else {
                                        self.rules[rule.field][0].isShow = true
                                        self.rules[rule.field][0].message = '精度只能输入大于等于0的整数'
                                        callback(new Error(''));
                                        self.setActiveTab && self.setActiveTab();
                                    }
                                } else {
                                    this.rules[rule.field][0].required  = true
                                    this.rules[rule.field][0].isShow = true
                                    callback(new Error(''))
                                }
							}else {
                                this.rules[rule.field][0].required  = false
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						self:_this,
						trigger: 'blur',
						isShow: false,
						message: '精度不能为空',
					}],
					min_value:[{
						required:false,
						validator: (rule, value, callback) => {
							if(/^(NUMERICAL)$/.test(this.submitData.option_type)){
                                if (value) {
                                    if (/^(([1-9][0-9]*)|(\d+\.\d{1,2}))$/.test(value)) {
                                        self.rules[rule.field][0].isShow = false
                                        callback();
                                    } else {
                                        self.rules[rule.field][0].isShow = true
                                        self.rules[rule.field][0].message = '最小值只能输入大于等于0的数，保留两位小数'
                                        callback(new Error(''));
                                        self.setActiveTab && self.setActiveTab();
                                    }
                                } else {
                                    this.rules[rule.field][0].required  = true
                                    this.rules[rule.field][0].isShow = true
                                    callback(new Error(''))
                                }
							}else {
                                this.rules[rule.field][0].required  = false
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						self:_this,
						trigger: 'blur',
						isShow: false,
						message: '最小值不能为空',
					}],
					max_value:[{
						required:false,
						validator: (rule, value, callback) => {
							if(/^(NUMERICAL)$/.test(this.submitData.option_type)){
                                if (value) {
                                    if (/^(([1-9][0-9]*)|(\d+\.\d{1,2}))$/.test(value)) {
                                        self.rules[rule.field][0].isShow = false
                                        callback();
                                    } else {
                                        self.rules[rule.field][0].isShow = true
                                        self.rules[rule.field][0].message = '最大值只能输入大于等于0的数,保留两位小数'
                                        callback(new Error(''));
                                        self.setActiveTab && self.setActiveTab();
                                    }
                                } else {
                                    this.rules[rule.field][0].required  = true
                                    this.rules[rule.field][0].isShow = true
                                    callback(new Error(''))
                                }
							}else {
                                this.rules[rule.field][0].required  = false
								this.rules[rule.field][0].isShow = false
								callback()
							}
						},
						self:_this,
						trigger: 'blur',
						isShow: false,
						message: '最大值不能为空',
                    }],
				},
				initialData:{},
				isLoading:false,
			}
		},
		methods : {
      changeOptionType(){
        if (!(/^(NUMERICAL)$/.test(this.submitData.option_type))) {
          this.submitData.if_need_adjust = 'N'
        }
      },
      openOptionType(){
        if(optionTypeDisabled) return
        let params = {}, self = this;
          params.callback = d => {
            if(d) {
              console.log(d);
              self.form.clean_uint_code = d.material_unit;
            }
          }
        self.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/configur/getunit.vue'),
          style: 'width:800px;height:500px',
          title: '单位列表'
        });
      },
            querySearch(queryString, cb) {
                // console.log(queryString)
                let searchdata = {
                    page : {length: 50, pageNo: 1},
                    page_name : "cloud_auxiliary_category",
                    where: [{
                        condition : "AND",
                        field : "8ef96bcfc111af9d130e0b7165375a3f",
                        listWhere : [],
                        operator : "%",
                        table : "c982ba0edaab6ffe3ebc4851a8c1e568",
                        value : this.submitData.lookup_type
                    }]
                }
				clearTimeout(this.timeout);
				this.timeout = setTimeout(() => {
					this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryCategoryList',searchdata,function(d){
						var data = d.body;
						if(data.result){
							console.log(data.content)
							if (data.content.count == 0) return;
							let result = [];
							data.content.list.forEach(item => {
								item.value = item.code;
								result.push(item);
							});
							var results = data.content.list;
							cb(result);
						}else{
							this.$message({
								message:d.body.msg,
								type:'error'
							})
						}
					})
				}, 500);
                
            },
            handleSelect(item) {
                console.log('item', item)
            },
            getUserList() {
                console.log('getUserList')
            },
			preSave(formName){
				var self = this;
				self.$refs[formName].validate((valid) => {
					if(!valid) return

					this.isLoading=true
					self.save()
				})
			},
			save(callback){	
				var self = this,
				params={};
				var url = "/plan-web/api/Option/saveOption";
                let saveData = JSON.parse(JSON.stringify(this.submitData));
                saveData.configurator_id = this.params.configurator_id
				this.ajax.postStream(url,/*self.submitData*/saveData,(response)=>{
					this.isLoading=false;
					if(response.body.result){
						 this.params.callback(this.params.configurator_id);
                   		 this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
					}else{
						this.$message.error(response.body.msg);
					}
                   
				},e=>{
					this.isLoading=false
					this.$message.error(e)
				})
			},
			// 关闭标签页
			closeTab(){
				var self = this,isUpdate;
				console.log(self.submitData,self.initialData)
				isUpdate = this.compareData(self.submitData,self.initialData);

				if(isUpdate){
					self.$root.eventHandle.$emit('openDialog',{
						ok(){
							self.save(()=>{
								self.$root.eventHandle.$emit('removeTab',self.params.tabName)
							})
						},
						no(){
							self.$root.eventHandle.$emit('removeTab',self.params.tabName)
						}
					})
				}else{
					self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				}
			},
		},
		mounted:function(){
			var self = this;
			self.params.__close = self.closeTab
        },
        computed: {
            optionTypeDisabled: function () {
                if (this.submitData.option_type == 'LOOKUP') {
                    this.rules['lookup_type'][0].required  = true
                    this.rules['length'][0].required  = false
					this.rules['option_precision'][0].required  = false
					this.rules['max_value'][0].required  = false
					this.rules['min_value'][0].required  = false
                    this.submitData.length = ''
                    this.submitData.option_precision = ''
                    return false
                } else {
                    if (this.submitData.option_type == 'NUMERICAL') { // 数字-》精度、长度必填，字符-》长度必填
                        this.rules['option_precision'][0].required  = true
                        this.rules['max_value'][0].required  = true
                        this.rules['min_value'][0].required  = true
                        this.rules['lookup_type'][0].required  = false
                        this.rules['length'][0].required  = true
                        this.submitData.lookup_type = ''
                    } else if (this.submitData.option_type == 'CHARACTERS') {
                        this.rules['option_precision'][0].required  = false
                        this.rules['lookup_type'][0].required  = false
						this.rules['length'][0].required  = true
						this.rules['max_value'][0].required  = false
                        this.rules['min_value'][0].required  = false
                        this.submitData.lookup_type = ''
                        this.submitData.option_precision = ''
                    }
                    return true
                }
            }
        }
	}
</script>
<style module>

</style>