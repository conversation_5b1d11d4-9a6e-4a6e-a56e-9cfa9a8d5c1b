<!-- 商品配置列表-->
<template>
	<xpt-list
		:data='tableData'
		:btns='btns'
		:colData='cols'
		:pageTotal='totalPage'
		selection=''
		:searchPage="searchObj.page_name"
		@search-click="preSearch"
		:orderNo='true'
		@page-size-change='handleSizeChange'
		@current-page-change='handleCurrentChange' >
	</xpt-list>
</template>
<script>
export default {
	data(){
		var self = this;
		return{
			btns: [
				{
					type: 'primary',
					txt: '新增',
					click: self.addFun
				}, {
					type: 'primary',
					txt: '刷新',
					click: self.searchFun
				}
			],
			cols: [
				
				{
					label: '配置器编码',
					prop: 'configurator_code',
					redirectClick(row) {
						self.viewDetail(row)
					},
					width: 180
							},
				{
					label: '配置器名称 ',
					prop: 'configurator_name',
					width: 100
				},
				{
					label: '备注',
					prop: 'description',
          			width: 100
				},
				{
					label: '创建人',
					prop: 'creator_name',
				},
				{
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
				},{
					label: '最后修改人',
					prop: 'last_modifier_name',
				},
				{
					label: '最后修改时间',
					prop: 'last_modify_time',
					format: 'dataFormat1',
				},{
                    label: '金额分类',
                    prop: 'amount_classify',
                    formatter(val){
                        switch(val){
                            case "DJ": return '订金金额'; break;
							case "ACT": return '实际金额'; break;
							default: return val; break;
                        }
                    }
                }
				
			],
			searchObj:{
				page_no:1,
				page_size: self.pageSize,
				page_name: 'scm_product_configurator',
				where: []
			},
			totalPage:0,
			searchInput:'',
			tableData:[]
		}
	},
	props:['params'],
	mounted(){
	this.searchFun();
	},
	destroyed(){
		this.$root.offEvents('discountAdd');
    },
	methods:{
    viewDetail(row){
      let params = {};
      params.configurator_id = row.configurator_id;
      params.configurator_code= row.configurator_code;
      this.$root.eventHandle.$emit('creatTab',{
        name:"商品配置详情",
        params:params,
        component: () => import('@components/configur/add.vue')
      });
    },
		addFun(){
			this.$root.eventHandle.$emit('creatTab', {name:'新增商品配置',params:{configurator_id:null}, component:()=>import('@components/configur/add.vue')});
		},
		
		
		clickFun(api,tip,name){
			var self = this;
			if(self.selectId.length===0){
				self.$message.error('请选择要'+tip+'的活动优惠');
				return;
			}
			var data={
				ids:self.selectId
			};
		},
		
		preSearch(txt, resolve){
			this.searchObj.where = txt
			this.searchFun(null, resolve);
		},
		initSearchData(){
		},
		searchFun(event, resolve){
			let self = this;
			this.ajax.postStream('/plan-web/api/Configuration/getList',this.searchObj, (res) => {
				if(res.body.result){
					self.tableData = res.body.content.list;
					self.totalPage = res.body.content.count;
				}else{
					self.$message.error(res.body.msg || '');
				}
				if (resolve) {
				resolve();
				}
			}, (err) => {
				self.$message.error(err);
				if (resolve) {
				resolve();
				}
			});
		},
		
		handleSizeChange(val){
			this.searchObj.page_size = val;
			this.initSearchData()
			this.searchFun();
		},
		handleCurrentChange(val){
			this.searchObj.page_no = val;
			this.initSearchData()
			this.searchFun();
		},
		
		
	}
}
</script>
