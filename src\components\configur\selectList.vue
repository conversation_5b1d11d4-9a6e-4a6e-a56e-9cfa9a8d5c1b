<!-- 商品配置列表-->
<template>
	<xpt-list
		:data='tableData'
		:btns='btns'
		:colData='cols'
		:pageTotal='totalPage'
		selection='radio'
		@radio-change='select' 
		:searchPage="searchObj.page_name"
		@search-click="preSearch"
		:orderNo='true'
		@page-size-change='handleSizeChange'
		@current-page-change='handleCurrentChange' >
	</xpt-list>
</template>
<script>
export default {
	data(){
		var self = this;
		return{
			selectData:'',
			btns: [
				{
					type: 'primary',
					txt: '确定',
					click: self.close
				}
			],
			cols: [
				
				{
					label: '配置器编码',
					prop: 'configurator_code',
					redirectClick(row) {
						self.viewDetail(row)
					},
					width: 180
							},
				{
					label: '配置器名称 ',
					prop: 'configurator_name',
					width: 100
				},
				
				{
					label: '备注',
					prop: 'description',
          			width: 100
				},
				{
					label: '创建人',
					prop: 'creator_name',
				},
				{
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
				},{
					label: '最后修改人',
					prop: 'last_modifier_name',
				},
				{
					label: '最后修改时间',
					prop: 'last_modify_time',
					format: 'dataFormat1',
				},
				
			],
			searchObj:{
				page_no:1,
				page_size: self.pageSize,
				page_name: 'scm_product_configurator',
				where: []
			},
			totalPage:0,
			searchInput:'',
			tableData:[]
		}
	},
	props:['params'],
	mounted(){
		this.searchFun();
	},
	
	methods:{
		select(obj) {
			this.selectData = obj;
		},
		preSearch(txt, resolve){
			this.searchObj.where = txt
			this.searchFun(null, resolve);
		},
		initSearchData(){
			if(!this.searchKey) this.searchObj.act_name = ''
			else this.searchObj.act_name = this.searchKey
		},
		searchFun(event, resolve){
			let self = this;
			this.ajax.postStream('/plan-web/api/Configuration/getList',this.searchObj, (res) => {
				if(res.body.result){
					self.tableData = res.body.content.list;
					self.totalPage = res.body.content.count;
				}else{
					self.$message.error(res.body.msg || '');
				}
				if (resolve) {
				resolve();
				}
			}, (err) => {
				self.$message.error(err);
				if (resolve) {
				resolve();
				}
			});
		},
		rowDblclick(obj) {
			this.selectData = obj;
			this.close();
		},
		close() {
			if(!this.selectData) {
				this.$message({
					type: 'error',
					message: '请选择配置'
				})
				return;
			}
			this.params.callback(this.selectData);
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
		},
		
		handleSizeChange(val){
			this.searchObj.page_size = val;
			this.initSearchData()
			this.searchFun();
		},
		handleCurrentChange(val){
			this.searchObj.page_no = val;
			this.initSearchData()
			this.searchFun();
		},
		
		
	}
}
</script>
