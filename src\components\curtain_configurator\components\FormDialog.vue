<template>
  <div class="form-dialog-container">
    <el-dialog
      :title="formParams.title"
      :visible.sync="visible"
      class="curtain-configurator-dialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :center="true"
    >
      <el-form ref="form" :model="formParams.form" label-width="120px">
        <el-form-item
          v-for="(items, index) in formParams.cols"
          :key="index"
          :label="items.label"
          :prop="items.prop"
          class="el-form-item"
        >
          <xpt-input
            v-if="items.xptInput"
            v-model="formParams.form[items.prop]"
            :placeholder="items.placeholder"
            :size="items.size || 'mini'"
            icon="search"
            :on-icon-click="items.onIconClick"
            :disabled="getDisabled(items)"
            :readonly="items.readonly || false"
            @focus="items.focus($event) || (() => {})"
            @change="items.change($event) || (() => {})"
            @blur="items.blur($event) || (() => {})"
          ></xpt-input>

          <el-input
            v-else-if="items.$input"
            v-model="formParams.form[items.prop]"
            :placeholder="items.placeholder"
            :disabled="getDisabled(items)"
            :readonly="items.readonly || false"
            :size="items.size || 'mini'"
            class="input-width"
            @input="items.$input($event)"
          ></el-input>

          <el-radio-group
            v-else-if="items.isRadio"
            v-model="formParams.form[items.prop]"
          >
            <el-radio
              v-for="(item, i) in items.formatParams"
              :key="i"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>

          <el-input
            v-else
            v-model="formParams.form[items.prop]"
            :placeholder="items.placeholder"
            :disabled="getDisabled(items)"
            :readonly="items.readonly || false"
            :size="items.size || 'mini'"
            class="input-width"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSuccess">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formParams: {},
      visible: false,
    };
  },
  computed: {
    getDisabled() {
      return (items) => {
        if (typeof items.disabled === "function") return items.disabled();
        return !!items.disabled;
      };
    },
  },
  methods: {
    open(formParams) {
      this.formParams = formParams;
      this.visible = true;
    },
    handleClose() {
      this.$refs.form.resetFields();
      this.formParams.form = {};
      this.visible = false;
    },
    handleSuccess() {
      this.formParams.ok();
    },
  },
};
</script>

<style scoped>
.curtain-configurator-dialog {
  z-index: 10 !important;
}

.curtain-configurator-dialog .dialog-footer {
  display: flex;
}

.curtain-configurator-dialog .el-button {
  flex: 1;
}

.curtain-configurator-dialog .el-dialog--small {
  width: 500px !important;
}

.curtain-configurator-dialog .el-form-item {
  margin: 5px 0;
}

.curtain-configurator-dialog .input-width {
  width: 100%;
}
</style>
<style>
.curtain-configurator-dialog + .v-modal {
  z-index: 9 !important;
}
</style>