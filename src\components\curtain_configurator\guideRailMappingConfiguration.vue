
<!-- 导轨映射配置-->
<template>
  <div>
    <xpt-list
      :data="list"
      :btns="btns"
      :colData="cols"
      selection=""
      :pageTotal="count"
      @page-size-change="handleSizeChange"
      @current-page-change="handleCurrentChange"
    >
      <template slot="btns">
        <el-tooltip
          class="if_offline_and_online_linkage_tooltip"
          placement="right"
        >
          <p slot="content">
            说明：本功能用于定制窗帘业务部门，配置安帮客测量的窗帘数据字段“产品名称”与导轨物料的一一对应关系。
            用于门店POS端，导购下窗帘定制单时，点击配置器，选择了安帮客的测量数据，能自动带出导轨物料。
          </p>
          <i class="el-icon-warning"></i>
        </el-tooltip>
      </template>
      <template slot="operation" slot-scope="scope">
        <el-button @click="handleEdit(scope.row)" type="text" size="small">
          编辑
        </el-button>
      </template>
    </xpt-list>
    <form-dialog ref="formDialogRef"></form-dialog>
  </div>
</template>
<script>
import formDialog from "./components/FormDialog.vue";
import guideRailMappingConfigurationMixins from "./mixins/guideRailMappingConfigurationMixins";
export default {
  components: {
    formDialog,
  },
  mixins: [guideRailMappingConfigurationMixins],
  data() {
    var self = this;
    return {
      btns: [
        {
          type: "primary",
          txt: "新增",
          click: self.addFun,
        },
        {
          type: "primary",
          txt: "刷新",
          click() {
            self.refresh();
          },
        },
      ],
      cols: [
        {
          label: "安帮客产品名称",
          prop: "abk_product_name",
          align: "center",
        },
        {
          label: "林氏木业导轨类型",
          prop: "lsmy_rail_type",
          align: "center",
        },
        {
          label: "导轨物料编号",
          prop: "material_code",
          align: "center",
        },
        {
          label: "导轨名称",
          prop: "material_name",
          align: "center",
        },
        {
          label: "规格",
          prop: "material_specification",
          align: "center",
        },
        {
          label: "是否启用",
          prop: "status",
          formatter(val) {
            return val == "Y" ? "是" : "否";
          },
          align: "center",
        },
        {
          label: "操作",
          slot: "operation",
          align: "center",
        },
      ],
      count: 0,
      search: {
        page_no: 1,
        page_size: 50,
        page_name: "",
        where: [],
      },
      list: [],
    };
  },
  mounted() {
    this.getList();
  },
  destroyed() {
    this.$root.offEvents("discountAdd");
  },
  methods: {
    refresh() {
      this.getList();
    },
    getList(resolve) {
      let self = this;
      this.ajax.postStream(
        "/plan-web/api/railMapperConfig/list",
        this.search,
        (res) => {
          let { content, result, msg } = res.body;
          if (result && content) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (err) => {
          self.$message.error(err);
          resolve && resolve();
        }
      );
    },

    handleSizeChange(val) {
      this.search.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.search.page_no = val;
      this.getList();
    },
  },
};
</script>
<style scoped>
.if_offline_and_online_linkage_tooltip {
  color: #909399 !important;
  margin-left: 10px;
}
</style>