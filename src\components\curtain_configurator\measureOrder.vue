<!-- 安帮客测量单 -->
<template>
  <xpt-list
    :data="list"
    :btns="btns"
    :searchPage="search.page_name"
    :colData="cols"
    selection="radio"
    :pageTotal="count"
    @search-click="handleSearchClick"
    @page-size-change="handleSizeChange"
    @current-page-change="handleCurrentChange"
    @radio-change="handleRadioChange"
  >
    <template slot="order_flow_status" slot-scope="scope">
      <span>{{ orderFlowStatus(scope.row) }}</span>
    </template>
    <template slot="order_num" slot-scope="scope">
      <span>{{
        scope.row.abk_push_status === "FAILED" || !scope.row.order_num
          ? "--"
          : scope.row.order_num
      }}</span>
    </template>
    <template slot="measur_excel_url" slot-scope="scope">
      <el-button
        v-if="!!scope.row.measur_excel_url && scope.row.order_flow_status == 90"
        @click="handleOpenItUp(scope.row.measur_excel_url)"
        type="text"
        size="small"
      >
        查看
      </el-button>
      <span v-else>--</span>
    </template>
  </xpt-list>
</template>

<script>
export default {
  data() {
    var self = this;
    return {
      btns: [
        {
          type: "success",
          txt: "刷新",
          click() {
            self.refresh();
          },
        },
        {
          type: "info",
          txt: "导出",
          click() {
            self.exportExcel();
          },
        },
        {
          type: "info",
          txt: "导出结果",
          click() {
            self.exportResult();
          },
        },
        {
          type: "primary",
          txt: "重新推送",
          click() {
            self.anewPush();
          },
          loading: false,
        },
      ],
      cols: [
        {
          label: "NBO销售订单号",
          prop: "sys_trade_no",
          align: "center",
          width: 190,
        },
        {
          label: "业主姓名",
          prop: "contact_name",
          align: "center",
        },
        {
          label: "业主手机号",
          prop: "contact_phone",
          align: "center",
        },
        {
          label: "业主地址",
          prop: "contact_address",
          align: "center",
        },
        {
          label: "订单备注",
          prop: "remark",
          align: "center",
          width: 190,
        },
        {
          label: "创建时间",
          prop: "create_time",
          align: "center",
          format: "dataFormat1",
        },
        {
          label: "安帮客订单状态",
          slot: "order_flow_status",
          align: "center",
        },
        {
          label: "推送安帮客状态",
          prop: "abk_push_status",
          align: "center",
          formatter(val) {
            switch (val) {
              case "SUCCESS":
                return "成功";
                break;
              case "FAILED":
                return "失败";
                break;
              case "WAIT":
                return "等待";
                break;
              default:
                return val;
                break;
            }
          },
        },
        {
          label: "安帮客订单号",
          slot: "order_num",
          align: "center",
        },
        {
          label: "师傅",
          prop: "worker_name",
          align: "center",
          formatter(val) {
            return !val ? "--" : val;
          },
        },
        {
          label: "师傅手机号",
          prop: "worker_phone",
          align: "center",
          formatter(val) {
            return !val ? "--" : val;
          },
        },
        {
          label: "测量文件",
          slot: "measur_excel_url",
          align: "center",
        },
      ],
      count: 0,
      search: {
        page_no: 1,
        page_size: 50,
        page_name: "scm_abk_measure_order",
        where: [],
      },
      list: [],
      radioData: [],
    };
  },
  computed: {
    orderFlowStatus() {
      return (row) => {
        let { order_flow_status, abk_push_status } = row;
        if (abk_push_status === "FAILED") return "--";
        let aux = __AUX.get("abk_order_flow_status");
        let list = aux.filter((item) => {
          return item.code == order_flow_status;
        });
        if (!list.length) {
          return order_flow_status;
        }
        return list[0].name;
      };
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    getList(resolve) {
      let self = this;
      this.ajax.postStream(
        "/order-web/api/anBangKe/list",
        this.search,
        (res) => {
          let { content, result, msg } = res.body;
          if (result && content) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (err) => {
          self.$message.error(err);
          if (resolve) {
            resolve();
          }
        }
      );
    },
    handleSearchClick(list, resolve) {
      this.search.where = list;
      this.getList(resolve);
    },
    handleSizeChange(val) {
      this.search.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.search.page_no = val;
      this.getList();
    },
    handleRadioChange(val) {
      this.radioData = val;
    },

    anewPush() {
      this.btns[3].loading = true;
      let self = this;
      if (!this.radioData.sys_trade_no) {
        this.btns[3].loading = false;
        this.$message.warning("请选择需要推送的数据");
        return;
      }
      if (this.radioData.abk_push_status !== "FAILED") {
        this.btns[3].loading = false;
        this.$message.warning("当前数据推送状态不是失败");
        return;
      }
      let {
        sys_trade_no,
        contact_name,
        contact_phone,
        contact_address,
        remark,
      } = this.radioData;
      let data = {
        partyNum: sys_trade_no,
        contactName: contact_name,
        contactPhone: contact_phone,
        contactAddress: contact_address,
        remark,
      };
      this.ajax.postStream(
        "/order-web/api/anBangKe/resetPushCreateMeasure",
        data,
        (res) => {
          let { result, msg } = res.body;
          if (result) {
            this.$message.success(msg);
            this.getList();
          } else {
            this.$message.error(msg);
          }
          this.btns[3].loading = false;
        },
        (err) => {
          self.$message.error(err);
          this.btns[3].loading = false;
        }
      );
    },

    handleOpenItUp(url) {
      window.open(url);
    },

    //导出
    exportExcel() {
      this.ajax.postStream(
        "/order-web/api/anBangKe/exportScmAbkMeasureOrderList",
        this.search,
        (res) => {
          let { result, msg } = res.body;
          if (result) {
            this.$message.success(msg);
          } else {
            this.$message.error(msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    exportResult() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "测量单导出列表",
        params: {
          query: {
            type: "EXCEL_TYPE_ABK_MEASURE_ORDER_EXPORT",
          },
        },
      });
    },
  },
};
</script>

<style>
</style>