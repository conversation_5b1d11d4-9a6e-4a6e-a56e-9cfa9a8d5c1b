export default {
  data() {
    var self = this;
    return {
      isDetails: false,
      blurStatus: true,
      formParams: {
        title: "新增",
        form: {
          abk_product_name: "",
          lsmy_rail_type: "",
          material_code: "",
          material_name: "",
          material_specification: "",
          status: "",
        },
        cols: [
          {
            label: "安帮客产品名称",
            prop: "abk_product_name",
            placeholder: "请输入名称",
            disabled: () => {
              return self.isDetails;
            },
          },
          {
            label: "林氏木业导轨类型",
            prop: "lsmy_rail_type",
            placeholder: "请输入名称",
          },
          {
            label: "导轨物料编号",
            prop: "material_code",
            placeholder: "请选择物料编号",
            xptInput: true,
            onIconClick: self.handleCodeOpen,
            focus: self.handleCodeFocus,
            change: self.handleCodeClose,
            blur: self.handleCodeBlur,
          },
          {
            label: "导轨名称",
            prop: "material_name",
            placeholder: "自动带出",
            readonly: true,
          },
          {
            label: "规格",
            prop: "material_specification",
            placeholder: "自动带出",
            readonly: true,
          },
          {
            label: "是否启用",
            prop: "status",
            isRadio: true,
            formatParams: [
              {
                label: "是",
                value: "Y",
              },
              {
                label: "否",
                value: "N",
              },
            ],
          },
        ],
        ok: function () {
          self.handleSave();
        },
      },
    };
  },
  methods: {
    initParams() {
      this.formParams.title = "新增";
      this.isDetails = false;
      this.formParams.form = {
        abk_product_name: "",
        lsmy_rail_type: "",
        material_code: "",
        material_name: "",
        material_specification: "",
        status: "",
      };
    },
    addFun() {
      this.initParams();
      this.$refs.formDialogRef.open(this.formParams);
    },
    handleEdit({ rail_mapper_config_id }) {
      this.formParams.title = "编辑";
      this.isDetails = true;
      this.ajax.postStream(
        "/plan-web/api/railMapperConfig/get",
        { rail_mapper_config_id },
        (res) => {
          let { content, result, msg } = res.body;
          if (result) {
            this.formParams.form = content;
            this.$refs.formDialogRef.open(this.formParams);
          } else {
            this.$message.error(msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    handleSave() {
      // 未失焦不给保存
      if (!this.blurStatus) {
        this.$message.warning("物料数据未更新!请重试");
        return;
      }
      // 安帮客产品名称不能重复添加
      if (this.getVerifyStatus()) return;
      this.ajax.postStream(
        "/plan-web/api/railMapperConfig/saveOrUpdate",
        this.formParams.form,
        (res) => {
          let { result, msg } = res.body;
          if (result) {
            this.$refs.formDialogRef.handleClose();
            this.getList();
          } else {
            this.$message.error(msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },

    handleCodeOpen() {
      let self = this;
      let params = {};
      params.callback = (e) => {
        if (e) {
          self.formParams.form.material_code = e.materialNumber;
          self.formParams.form.material_name = e.materialName;
          self.formParams.form.material_specification = e.materialSpecification;
        }
      };
      self.$root.eventHandle.$emit("alert", {
        params,
        component: () => import("@components/order/selectGoodsList"),
        style: "width:800px;height:500px",
        title: "物料列表",
      });
    },
    handleCodeClose(e) {
      if (typeof e === "undefined") {
        this.formParams.form.material_code = "";
        this.formParams.form.material_name = "";
        this.formParams.form.material_specification = "";
      }
    },
    handleCodeFocus() {
      this.blurStatus = false;
    },
    handleCodeBlur(e) {
      let val = e.target._value;
      if (!!val) {
        let data = {
          page: { length: 1, pageNo: 1 },
          page_name: "cloud_material",
          where: [
            {
              field: "5abf79c56641dadb3a110dc46099614c",
              table: "7f1a8a645d6df6602a3f1423f2c90752",
              value: val,
              operator: "=",
              condition: "AND",
              listWhere: [],
            },
          ],
          disableStatus: "A",
          sale_enabled: "1",
          audit_flag: "1",
          invalid_sale: "0",
          inventoryCategoryList: ["成品", "组合商品"],
          disableStatus: "A",
          audit_flag: "1",
        };
        this.ajax.postStream(
          "/material-web/api/material/getMaterialList",
          data,
          (res) => {
            let { result, content, msg } = res.body;
            if (result && content) {
              if (content.list.length > 0) {
                let row = content.list[0];
                this.formParams.form.material_code = row.materialNumber;
                this.formParams.form.material_name = row.materialName;
                this.formParams.form.material_specification =
                  row.materialSpecification;
              } else {
                this.formParams.form.material_code = "";
                this.formParams.form.material_name = "";
                this.formParams.form.material_specification = "";
              }
              this.blurStatus = true;
            } else {
              this.$message.error(msg);
              this.blurStatus = true;
            }
          },
          (err) => {
            this.blurStatus = true;
            this.$message.error(err);
          }
        );
      } else {
        this.blurStatus = true;
      }
    },

    getVerifyStatus() {
      let form = this.formParams.form;
      if (
        !form.rail_mapper_config_id &&
        this.list.some(
          (item) => item.abk_product_name === form.abk_product_name
        )
      ) {
        this.$message.error("安帮客产品名称不能重复添加");
        return true;
      }
      for (const key of this.getFormFilter(form)) {
        const val = form[key];
        if (!val) {
          this.$message.error("不能有空数据");
          return true;
        }
      }
      return;
    },
    getFormFilter(form) {
      let initArr = [
        "abk_product_name",
        "lsmy_rail_type",
        "material_code",
        "material_name",
        "material_specification",
        "status",
      ];
      return Object.keys(form).filter((item) => initArr.includes(item));
    },
  },
};
