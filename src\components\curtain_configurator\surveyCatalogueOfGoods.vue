<!-- 测量商品目录-->
<template>
  <div>
    <xpt-list
      :data="list"
      :btns="btns"
      :colData="cols"
      selection=""
      :pageTotal="count"
      @page-size-change="handleSizeChange"
      @current-page-change="handleCurrentChange"
    >
      <template slot="operation" slot-scope="scope">
        <el-button @click="handleEdit(scope.row)" type="text" size="small">
          编辑
        </el-button>
      </template>
    </xpt-list>
    <form-dialog ref="formDialogRef"></form-dialog>
  </div>
</template>
  <script>
import formDialog from "./components/FormDialog.vue";
import surveyCatalogueOfGoodsMixins from "./mixins/surveyCatalogueOfGoodsMixins";
export default {
  components: {
    formDialog,
  },
  mixins: [surveyCatalogueOfGoodsMixins],
  data() {
    var self = this;
    return {
      btns: [
        {
          type: "primary",
          txt: "新增",
          click: self.addFun,
        },
        {
          type: "primary",
          txt: "刷新",
          click() {
            self.refresh();
          },
        },
      ],
      cols: [
        {
          label: "物料编号",
          prop: "material_code",
          align: "center",
        },
        {
          label: "物料名称",
          prop: "material_name",
          align: "center",
        },
        {
          label: "规格",
          prop: "material_specification",
          align: "center",
        },
        {
          label: "是否启用",
          prop: "status",
          formatter(val) {
            return val == "Y" ? "是" : "否";
          },
          align: "center",
        },
        {
          label: "操作",
          slot: "operation",
          align: "center",
        },
      ],
      count: 0,
      search: {
        page_no: 1,
        page_size: 50,
        page_name: "",
        where: [],
      },
      list: [],
    };
  },
  mounted() {
    this.getList();
  },
  destroyed() {
    this.$root.offEvents("discountAdd");
  },
  methods: {
    refresh() {
      this.getList();
    },
    getList(resolve) {
      let self = this;
      this.ajax.postStream(
        "/plan-web/api/measureGoodsCatalog/list",
        this.search,
        (res) => {
          let { content, result, msg } = res.body;
          if (result && content) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (err) => {
          self.$message.error(err);
          resolve && resolve();
        }
      );
    },

    handleSizeChange(val) {
      this.search.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.search.page_no = val;
      this.getList();
    },
  },
};
</script>
  