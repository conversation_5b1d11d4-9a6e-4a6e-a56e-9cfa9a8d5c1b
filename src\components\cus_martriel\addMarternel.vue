<template>
	<div class="mgt10"> 
		<el-form label-position="right" label-width="95px" :model="submitData" :rules="rules" ref="submitData">
			<el-row :gutter='40' class="mgt10">
				<el-col :span="24" style="text-align:left;">
					<el-button type='success' size='mini' @click="preSave('submitData')"  v-show="!params.ifChange">确认</el-button >
					<el-button type='warning' size='mini' @click="closeWindow"  v-show="!params.ifChange">取消</el-button>
					<el-button type='warning' size='mini' @click="closeWindow" v-show="params.ifChange">确认</el-button>
				</el-col>
			</el-row>
			<el-row	:gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="物料ID" prop="materia_id">
						<el-input size='mini' v-model="submitData.materia_id" icon="search" :on-icon-click="goodsChoose" readonly></el-input>
						<el-tooltip v-if='rules.materia_id[0].isShow' class="item" effect="dark" :content="rules.materia_id[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="物料编码" prop="materiel_no">
						<el-input size='mini' v-model="submitData.materiel_no" readonly></el-input>
						<el-tooltip v-if='rules.materiel_no[0].isShow' class="item" effect="dark" :content="rules.materiel_no[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="物料名称" prop="materiel_name">
						<el-input size='mini' v-model="submitData.materiel_name" readonly></el-input>
						<el-tooltip v-if='rules.materiel_name[0].isShow' class="item" effect="dark" :content="rules.materiel_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="物料规格描述" prop="materiel_spec">
						<el-input size='mini' v-model="submitData.materiel_spec" readonly></el-input>
						<el-tooltip v-if='rules.materiel_spec[0].isShow' class="item" effect="dark" :content="rules.materiel_spec[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="物料材质" prop="materiel_texture">
						<el-input size='mini' v-model="submitData.materiel_texture" :disabled="params.ifChange && params.materialObj"></el-input>
						<el-tooltip v-if='rules.materiel_texture[0].isShow' class="item" effect="dark" :content="rules.materiel_texture[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="客户物料编码" prop="customer_materiel_no">
						<el-input size='mini' v-model="submitData.customer_materiel_no"></el-input>
						<el-tooltip v-if='rules.customer_materiel_no[0].isShow' class="item" effect="dark" :content="rules.customer_materiel_no[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="客户物料名称" prop="customer_materiel_name">
						<el-input size='mini' v-model="submitData.customer_materiel_name"></el-input>
						<el-tooltip v-if='rules.customer_materiel_name[0].isShow' class="item" effect="dark" :content="rules.customer_materiel_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="客户规格描述" prop="customer_materiel_spec">
						<el-input size='mini' v-model="submitData.customer_materiel_spec" :disabled="params.ifChange && params.materialObj"></el-input>
						<el-tooltip v-if='rules.customer_materiel_spec[0].isShow' class="item" effect="dark" :content="rules.customer_materiel_spec[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="条形码" prop="barcode">
						<el-input size='mini' v-model="submitData.barcode" :disabled="params.ifChange && params.materialObj"></el-input>
						<el-tooltip v-if='rules.barcode[0].isShow' class="item" effect="dark" :content="rules.barcode[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="是否生效" prop="if_take_effect">
						<el-switch
							v-model="submitData.if_take_effect"
							active-text="生效"
							inactive-text="失效">
						</el-switch>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
import validate from '@common/validate.js'
export default {
	props:["params"],
	data(){
		var self = this;
		return {
			provinceObj:{},
			cityObj:{},
			areaObj:{},
			streetObj:{},
			submitData:{
				// cust_id:self.params.cust_id,
				materia_id:"",// 物料ID
				materiel_no:"",// 物料编码
				materiel_name:"",// 物料名称
				materiel_spec:"",// 物料规格描述
				customer_materiel_no:"",// 物料编码
				customer_materiel_name:"",// 物料名称
				if_take_effect:true,// 是否生效
				materiel_texture:'',
				customer_materiel_spec:'',
				barcode:'',
			},
			rules:{
				...[
					{
						materiel_texture: '物料材质'
					},{
						customer_materiel_spec: '客户规格描述'
					},{
						barcode: '条形码'
					},{
						customer_materiel_name: '客户物料名称'
					},{
						customer_materiel_no: '客户物料编码'
					},{
						materiel_spec: '规格描述'
					},{
						materiel_name: '物料名称'
					},{
						materiel_no: '物料编码'
					},{
						materia_id: '物料ID'
					}
				].reduce((a, b) => {
					let key = Object.keys(b)[0]
					a[key] = validate.isNotBlank({
						self: this,
						msg: '请填写' + b[key],
					})
					return a
				}, {})
			},
			deliver_method_Options:[
				{
					value:'THREES',
					label:'三包'
				},{
					value:'LOGISTICS',
					label:'物流'
				},{
					value:'PICK_UP',
					label:'仓库自提'
				},{
					value:'ZTO',
					label:'中通小件'
				},{
					value:'ZTO_FREE',
					label:'中通免费'
				},{
					value:'SF',
					label:'顺丰'
				}
			],
			post_fee_type_Options:[
				{
					value:'NOW_PAY',
					label:'现付'
				},{
					value:'ARRIVE_PAY',
					label:'到付'
				}
			],
			addressNums:{
				stateNum:'',
				cityNum:'',
				districtNum:'',
				streetNum:''
			}
		}
	},
	methods:{
		closeWindow(){
			let self = this
			if (this.params.ifChange) {
				this.params.callback(self.submitData)
			}
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		
		goodsChoose(){
			var self = this
			let params = {};
			params.callback = d => {
						if(d) {
							self.submitData.materiel_no = d.materialNumber
							self.submitData.materia_id = d.sourceMaterialId
							self.submitData.materiel_name = d.materialName
							self.submitData.materiel_spec = d.materialSpecification
						}
					}
			self.$root.eventHandle.$emit('alert', {
						params:params,
						component: () => import('@components/order/selectGoodsList'),
						style: 'width:800px;height:500px',
						title: '商品列表'
						
					});
		},
		/*
		添加地址做保存时校验不允许收货人存在先生小姐等字符。
		*/
		preSave(formName){
			var self = this
			self.$refs[formName].validate((valid) => {
				if(!valid) return
				
				self.save()
			});
		},
		save(){
			
			var self = this;
			if(self.params.ifCallback){//客户模块地址新增，复制新增
				// self.submitData.if_take_effect = self.submitData.if_take_effect == 'true' ? 'Y' : 'N'
				self.params.callback(self.submitData)
				self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
			}
		}
	},
	mounted:function(){
		var self = this;
		if(self.params.ifCopy){//判断是否复制新增
			var url = '/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId'
			self.submitData.receiver_name = self.params.addressObj.receiver_name
			self.submitData.receiver_phone = self.params.addressObj.receiver_phone
			self.submitData.receiver_mobile = self.params.addressObj.receiver_mobile
			self.submitData.receiver_address = self.params.addressObj.receiver_address
			self.submitData.post_fee_type = self.params.addressObj.post_fee_type
			self.submitData.deliver_method = self.params.addressObj.deliver_method

      		new Promise((resolve,reject)=>{//请求省份信息，赋值省份编码
				self.ajax.postStream(url,1,function(response){
					if(response.body.result){
						self.provinceObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_state=self.params.addressObj.receiver_state
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_state) return;
			new Promise((resolve,reject)=>{//根据省份信息获得市级信息，赋值市级编码
				self.ajax.postStream(url,self.params.addressObj.receiver_state,function(response){
					if(response.body.result){
						self.cityObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_city=self.params.addressObj.receiver_city
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_city) return;
			new Promise((resolve,reject)=>{
				//根据市级信息获得区级信息，赋值区级编码
				self.ajax.postStream(url,self.params.addressObj.receiver_city,function(response){
					if(response.body.result){
						self.areaObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_district=self.params.addressObj.receiver_district
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_district) return;
			new Promise((resolve,reject)=>{//根据区级信息获得街道信息，赋值街道编码
				self.ajax.postStream(url,self.params.addressObj.receiver_district,function(response){
					if(response.body.result){
						self.streetObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_street=self.params.addressObj.receiver_street
			}).catch(error=>{
				self.$message.error(error)
			})
		} else if (this.params.ifChange && this.params.materialObj) { //修改客户物料详情
			self.submitData.materia_id = self.params.materialObj.source_material_id
			self.submitData.materiel_no = self.params.materialObj.materiel_no
			self.submitData.materiel_name = self.params.materialObj.materiel_name
			self.submitData.materiel_spec = self.params.materialObj.materiel_spec
			self.submitData.customer_materiel_no = self.params.materialObj.customer_materiel_no
			self.submitData.customer_materiel_name = self.params.materialObj.customer_materiel_name
			self.submitData.if_take_effect = self.params.materialObj.if_take_effect == 'Y' ? true : false
			self.submitData.barcode = self.params.materialObj.barcode
			self.submitData.customer_materiel_spec = self.params.materialObj.customer_materiel_spec
			self.submitData.materiel_texture = self.params.materialObj.materiel_texture
		} else {
			// self.getAreaCode(1,"provinceObj");//新增
		}
	},
	destroyed(){
	}
}
</script>