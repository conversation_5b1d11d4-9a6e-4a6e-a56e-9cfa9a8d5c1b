<!--添加客户物料/客户物料详情-->
<template>
	<div class="xpt-flex">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span="24">
				<el-button type='primary' size='mini' :disabled="!submit.id" @click="refresh" :loading='isLoading'>刷新</el-button >
				<el-button type='primary' size='mini' @click="preSave('submit')" :loading='isLoading'>保存</el-button >
				<!-- <el-button type='danger' class='xpt-close' size='mini' @click="closeTab">关闭</el-button> -->
			</el-col>
		</el-row>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="客户物料" name="materiel">
			    	<el-form label-position="right" label-width="100px" :model="submit" :rules="rules" ref="submit">
				    	<el-row class="mgt10">
							<el-col :span='8'>
								<!-- <el-form-item label="单据编号" prop="creator_name ">
									<el-input size='mini' v-model="submit.creator_name "  :disabled="true"></el-input>
								</el-form-item> -->
								<el-form-item label="客户" prop="cust_name">
									<el-input size='mini' v-model="submit.cust_name" icon="search" :on-icon-click="selectBuyersName" readonly :disabled="submit.id ? true : false"></el-input>
									<el-tooltip v-if='rules.cust_name[0].isShow' class="item" effect="dark" :content="rules.cust_name[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
								<el-form-item label="描述">
									<el-input size='mini' type="textarea" v-model="submit.describe_str" ></el-input>
								</el-form-item>
								<!-- <el-form-item label="生效" prop="name">
									<el-input size='mini' v-model="submit.name" :disabled='submit.number?true:false'></el-input>
									<el-tooltip v-if='rules.name[0].isShow' class="item" effect="dark" :content="rules.name[0].message" placement="right-start" popper-class='xpt-form__error'>
										<i class='el-icon-warning'></i>
									</el-tooltip> -->
								<!-- </el-form-item> -->
							</el-col>
							<el-col :span='8'>
								<el-form-item label="是否发送邮件">
									<el-switch v-model="submit.if_send_email" on-text="是" off-text="否" on-value="Y" off-value="N">
									</el-switch>
								</el-form-item>
								<el-form-item label="邮箱">
									<el-input size='mini' type="textarea" v-model="submit.email_address" placeholder="格式为【<EMAIL>,<EMAIL>,<EMAIL>】"></el-input>
								</el-form-item>
							</el-col>
				    		<el-col :span='8'>
								<el-form-item label="创建人" prop="creator_name ">
									<el-input size='mini' v-model="submit.creator_name "  :disabled="true"></el-input>
								</el-form-item>
								<el-form-item label="创建时间" prop="create_time  ">
									<el-input size='mini' v-model="submit.create_time "  :disabled="true"></el-input>
								</el-form-item>
								<el-form-item label="最后修改人" prop="last_modifier_name">
									<el-input size='mini' v-model="submit.last_modifier_name"  :disabled="true"></el-input>
								</el-form-item>
								<el-form-item label="最后修改时间" prop="last_modify_time">
									<el-input size='mini' v-model="submit.last_modify_time "  :disabled="true"></el-input>
								</el-form-item>
							</el-col>
				    	</el-row>
		    		</el-form>
			    </el-tab-pane>
			</el-tabs>
		</el-row>
		<el-row class='xpt-flex__bottom'> 
			<el-tabs v-model="secondTab" @tab-click="changeTab">
				<el-tab-pane label="物料信息" name="materi" class='xpt-flex'>
					<xpt-list 
						id="shopDetail"
						:data='materiList' 
						:btns='customerBtns' 
						:colData='materielCols' 
						:searchPage='search.page_name'
						@search-click='searching' 
						:pageTotal='materialCount'
						@page-size-change='materiaSizeChange' 
						@current-page-change='materiaPageChange'  
						isNeedClickEvent 
						@selection-change='mareriRadioChange' 
					>
					<xpt-import slot='btns' v-if="submit.id" :taskUrl="uploadUrl" class='mgl10' :otherParams='otherParams' :callback="uploadCallback" :isupload="false"></xpt-import>
					<xpt-import slot='btns' v-else :taskUrl="uploadUrl" class='mgl10' :otherParams='otherParams' :callback="uploadCallback" :isupload="true"></xpt-import>
					 <template slot="if_onsale" slot-scope="scope">
                            <el-select
                                placeholder="请选择"
                                v-model="scope.row.if_onsale"
                                :disabled="scope.row.if_take_effect=='N'"
                                size="mini"
                                style="width: 100%"
                            >
                                    <el-option
                                        v-for="item in [{value:0,label:'下架'},{value:1,label:'上架'}]"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                            </el-select>
                        </template>
						<template slot="onsale_time" slot-scope="scope">
                            <el-date-picker
								v-model="scope.row.onsale_time"
								:disabled="scope.row.if_take_effect=='N'"
								type="datetime"
								placeholder="选择日期"
								size="mini"
                                style="width: 100%"
							>
							</el-date-picker>
                        </template>
					</xpt-list>
				</el-tab-pane>
				
				<el-tab-pane label="操作记录" name="operation" class='xpt-flex'>
					<xpt-list 
						:data='operationList' 
						:colData='operationCols' 
						:btns=[] 
						:showHead='false' 
					></xpt-list>
				</el-tab-pane>

			</el-tabs>
		</el-row>
	</div>
</template>
<script>
import validate from '@common/validate.js'
import Fn from '@common/Fn.js'
export default {
	props:["params"],
	data(){
		var _this = this;
		return {
			search:{
                page_name: 'transit_customer_materiel_item',
                where: []
			},
			isLoading:false,//判断是否处于提交状态
			selectLine:null,
			forbid_options:"",
			idExistence:_this.params.cust_id ? true :false,
			idExistence_eidt:this.params.cust_id ? true :false,
			firstTab: 'materiel',
			secondTab:"materi",
			submit:{
				id:'',//物料编号
				cust_name:'',//客户名称
				cust_id:'',//客户id
				describe_str:'',//描述
				if_take_effect:'Y',//状态
				creator_name:'',//创建人
				last_modify_time:'',//'最后修改时间
				create_time :'',//创建日期
				last_modifier_name:'',//跟新人
				cust_number: '',// 客户编码
				email_address:'',//邮箱字符串【email,email,email格式】
				if_send_email:'N'//是否发送邮件
			},
			approve_options:"",
			
			
			
			ifShow:true,
			// 操作记录列表
			operationList:[],
			// 物料列表
			materiList:[],
			// 客户关系
			customerList:[],
			delItems:[],
			count:0,
			pageNow:1,
			page:{
				page_no:1,
				page_size:0
			},
			// 选中的物料
			curMareriObj: [],
			rules:{
				cust_name:validate.isNotBlank({
					required:true,
					self:_this
				}),
				member_type:validate.isNotBlank({
					required:true,
					self:_this
				})
			},
			initialData:{},
			visible:"",
			code_max:25,
			name_max:25,
			// 数据对比
			comparedProp:{
				receiver_name:null,
				receiver_state_name:null,
				receiver_city_name:null,
				receiver_district_name:null,
				receiver_street_name:null,
				receiver_address:null
			},
			customerBtns: [
				{
					type: 'primary',
					txt: '新增',
					click() {
						_this.add_marteri('add')
					}
				}, {
					type: 'danger',
					txt: '删除',
					click: _this.delAddress
				}, {
					type: 'info',
					txt: '失效',
					click () {
                        _this.enabledMarteri('N')
                    }
				}, {
					type: 'success',
					txt: '生效',
					click () {
                        _this.enabledMarteri('Y')
                    }
				}, /*{
					type: 'warning',
					txt: '修改',
					click: _this.changeMarteri
				},*/ {
					type: 'primary',
					txt: '导出',
					click: _this.exportMarteri
				}, {
					type: 'primary',
					txt: '导出文件下载',
					click: _this.showExportMarteri
				},
            ],
			operationCols:[
				{
					label: '操作人',
					prop: 'operator_name'
				}, {
					label: '业务操作',
					prop: 'operation_name'
				}, {
					label: '操作时间',
					prop: 'operation_time',
					format: 'dataFormat1'
				}, {
					label: '操作描述',
					prop: 'operation_desc'
				}, {
					label: '备注',
					prop: 'remark'
				}
			],
			materielCols: [
				
				{
					label: '物料编码',
					prop: 'materiel_no'
				}, 
				{
					label: '物料名称',
					prop: 'materiel_name'
				}, 
				{
					label: '规格描述',
					prop: 'materiel_spec'
				}, 
				{
					label: '物料材质',
					prop: 'materiel_texture'
				}, 
				{
					label: '条形码',
					prop: 'barcode'
				}, 
				{
					label: '客户物料编码',
					prop: 'customer_materiel_no'
				}, 
				{
					label: '客户物料名称',
					prop: 'customer_materiel_name'
				},
				{
					label: '客户物料描述',
					prop: 'customer_materiel_spec'
				},
				{
					label: '生产状态',
					prop: 'production_state',
					formatter:(val)=>{
						return{
							'NORMALPRODUCT':'生产',
							'STOPPRODUCT':'停产',
						}[val]||val
					}
				},
				// {
				// 	label: '销售状态',
				// 	prop: 'if_onsale',
				// 	formatter:(val)=>{
				// 		return{
				// 			0:'下架',
				// 			1:'上架'
				// 		}[val]||val
				// 	}
				// },
				{
                    label: "销售状态",
                    slot: "if_onsale",
                },
				// {
				// 	label: '上架时间',
				// 	prop: 'onsale_time',
				// 	format: 'dataFormat1',
				// 	width: 130
				// },
				{
                    label: "上架时间",
                    slot: "onsale_time",
					width: 160
                },
				{
					label: '行状态',
					prop: 'if_take_effect',
					formatter(val){
						switch(val){
							case 'Y':   return '生效';break;
							case 'N':   return '失效';break;
             			 }
					  }
				}, 
				{
					label: '行创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 130
				}, 
				{
					label: '行更新时间',
					prop: 'last_modify_time',
					format: 'dataFormat1',
					width: 130
				}, 
				// {
				// 	label: '行创建时间',
				// 	prop: 'number'
				// }, 
				// {
				// 	label: '行更新时间',
				// 	prop: 'number'
				// }, 
			],
			isInit: false,
			uploadUrl: '/order-web/api/transit/cus/importMateriel',
			otherParams: {
				ifCusMartiel: true,
				id: ''
			},
			materialCount: 0,
			materialPageObj: {
				page_no: 1,
				page_size: 50
			},
		}
	},
	methods:{
		/*
		控制送货方式、运费类型是否可以变更
		新增的可以更改，
		原始地址的不能编辑
		保存之后不可以编辑
		*/
		customerSelectDisabled(row) {
			if(row.fid || row.is_original) {
				return true;
			} else {
				return false;
			}
        },
        // 导出报表
		showExportMarteri (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_TRANSIT_CUSTOMER_MATERIEL',
					},
				},
			})
		},
		searching(d, resolve){
			if(d) {
				this.search.where = d
			}
			this.getMaterialList(this.params,resolve);
		},
		mareriRadioChange(obj) {
			this.curMareriObj= obj;
		},

			// 买家昵称
		selectBuyersName (){
			if(this.isNotCanSave)return;
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/customers/list'),
				style:'width:900px;height:600px',
				title:'买家昵称列表',
				params: {
					close: d => {
						this.submit.cust_name = d.name;
						this.submit.cust_id = d.cust_id;
						this.submit.cust_number = d.number;
					}
				},
			})
		},
		/*
		判断是否存在相同的地址信息（根据收货人，省，市，区，街道，详情地址，配送方式等七个字段判断，一个不同则可视为不同的地址，即可添加）
		*/ 
		compareForDifference(comparedObj,index){
			let self = this
			if(self.materiList.length<=1)return
			for(var i=0;i<self.materiList.length;i++){
				if(index!=i&&comparedObj.deliver_method==self.materiList[i].deliver_method){
					var count = 0
					for(var key in self.comparedProp){
						if(comparedObj[key]==self.materiList[i][key])count+=1
					}
					if(count==6){
						self.$message.error("已存在相同的地址信息")
						self.materiList[index].deliver_method = ""
						// self.materiList[index].if_exist_deliver_method = false;
						return;
					}
				}
			}
		},
		// 新增物料
		add_marteri(option){
			var _this = this;
			if(option=="add"){
				_this.alert();
			}else if(option=="copy_add"){
				if(_this.curMareriObj == null){
					_this.$message.error('没有选择任何数据，请先选择数据！')
				}else{
					_this.alert(_this.curMareriObj);
				}
			}
		},
		// 生失效
		enabledMarteri(status){
            if (this.curMareriObj.length === 0) {
                this.$message.error('请选择需要操作的行')
                return
            }
            let self = this, remark_value = this.curMareriObj[0].if_take_effect
            let ifChangeSign = this.curMareriObj.every(item => {
                return item.if_take_effect === remark_value
            }), checkMsg = ''
            if (!ifChangeSign) {
                checkMsg = '请选择行状态一致的数据进行批量操作'
            } else if ((status =='N' && this.curMareriObj[0].if_take_effect == 'N' && ifChangeSign) || (ifChangeSign && status =='Y' && this.curMareriObj[0].if_take_effect == 'Y')) {
                let key = status == 'Y'? '已生效的' : '已失效的'
                checkMsg = key + '不可以重复操作'
            }
            if (checkMsg) {
                this.$message.error(checkMsg)
                return
            }
            let message = status == 'Y' ? '确定设置物料生效?' : '确定设置物料失效?'
			 this.$confirm(message, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
				}).then(() => {
                    this.curMareriObj.forEach(item => {
                        item.if_take_effect = status
                    })
					this.$message({
						type: 'success',
						message: '设置成功，保存操作后生效'
					});
				}).catch(() => {
					// this.$message({
					// 	type: 'info',
					// 	message: '取消成功'
					// });          
				});
			
		},
		// 地址弹窗
		alert(obj){
			var self = this;
			// console.log(obj)
			if(obj){
				self.params.addressObj = obj;
				self.params.ifCopy = true;
			}else{
				self.params.ifCopy = false;
			}
			self.params.ifCallback = true;
			self.params.callback = (d)=>{	
				let newAddressObj = {}
				// console.log(d);
				if(d.if_take_effect == true){
					d.if_take_effect = 'Y';
				}else{
					d.if_take_effect = "N";
				}
				for(let [key,value] of Object.entries(d)){
					newAddressObj[key]=value
				}
                newAddressObj.source_material_id = d.materia_id
                newAddressObj.tempId = new Date()
				// Object.assign(newAddressObj,{fid:'',number:'', tempId: +new Date()})
				self.materiList.unshift(newAddressObj)
				// 滚动到最底部
				let scrollObj = document.querySelector('#shopDetail').querySelector('.el-table__body-wrapper')
				this.$nextTick(function(){
					scrollObj.scrollTop = 0
				})
			}
			self.$root.eventHandle.$emit('alert',{params:self.params,
				component:() => import('@components/cus_martriel/addMarternel.vue'),
				close:function(){},
				style:'width:1100px;height:250px',
				title:'新增物料'
			});
		},
		// tab切换
		changeTab(tab,event){
			this.page.page_no  = 1;
			// if(!this.params.cust_id)return;
			if(tab.name == "materi"){
				// this.getPaymentInfo();
				this.ifShow = true;
			}else if(tab.name == "operation"){
				// this.getAddressInfo();
				this.ifShow = true;
				
			}
		},
		sizeChange(size){
			// 第页数改变
			this.page.page_size = size;
			if(this.secondTab == "materi"){
				// this.getPaymentInfo();
			}else if(this.secondTab == "operation"){
				// this.getAddressInfo();
			}
		},
		pageChange(page_no){
			// 页数改变
			this.pageNow = page_no;
			this.page.page_no = page_no;
			if(this.secondTab == "materi"){
				// this.getPaymentInfo();
			}else if(this.secondTab == "operation"){
				// this.getAddressInfo();
			}
		},
		// 关闭标签页
		closeTab(){
			var _this = this,isUpdate
			isUpdate = this.compareData(_this.submit,_this.initialData)

			if(isUpdate){
				_this.$root.eventHandle.$emit('openDialog',{
					ok(){
						_this.save(()=>{
							_this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
						});
					},
					no(){
						_this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
					}
				});
			}else{
				_this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
			}
		},
		preSave(formName){
			var _this = this
			_this.$refs[formName].validate((valid) => {
				
				//校验邮箱
				if(!!_this.submit.email_address&&_this.submit.email_address.length>0){
					let emailList=_this.submit.email_address.split(",")
					let hasRightEmail=emailList.every(item=>Fn.isEmail(item))
					if(!hasRightEmail){
						this.$message.warning("非法邮箱格式")
						return
					}
				}

				_this.save()
			})
		},
		save(callback){
			var _this = this;
			var url,func;
			var params = JSON.parse(JSON.stringify(_this.submit));
			params['create_time']=='' ?   params['create_time']='' : params['create_time']= +new Date(params.create_time)
			params['last_modify_time']=='' ?   params['last_modify_time']='' : params['last_modify_time']= +new Date(params.last_modify_time)
			let materiList = JSON.parse(JSON.stringify(_this.materiList || [])),i = materiList.length;
			
			while(i--) {
				delete materiList[i].disabled;
				delete materiList[i].tempId;
				delete materiList[i].cust_id;
				delete  materiList[i].production_state;
				materiList[i].source_material_id = materiList[i].source_material_id
				materiList[i].onsale_time = materiList[i].onsale_time?+new Date(materiList[i].onsale_time):null
				// materiList[i].if_take_effect = materiList[i].if_take_effect == 'true' ? 'Y': 'N';
				// for(var j=materiList.length-1;j>0;j--){
				// 	if(materiList[i] == materiList[j]){
				// 		_this.$message('不能选取相同的物料');
				// 		return false;
				// 	}
				// }
			}
			params.items = materiList;
			params.delItems = _this.delItems;
			// console.log(materiList)
			
			this.isLoading = true
			this.ajax.postStream("/order-web/api/transit/cus/addOrupdate",params,function(response){
				if(response.body.result){
					_this.$message({message: '操作成功',type: 'success'})
					// response.body.content?func(response.body.content.cust_id):func()
					_this.$root.eventHandle.$emit('close_addCustomer', _this.params, response.body.content)
					_this.params.id = response.body.content
					callback&&callback();
					_this.getDetail({id:response.body.content})
					_this.getMaterialList({id:response.body.content})
					setTimeout(function(){
						// 页面初始化数据，用作对比，是否有更新
						for(var key in _this.submit){
							_this.initialData[key] = _this.submit[key]
						}
					},500)
					setTimeout(function(){
						// 页面初始化数据，用作对比，是否有更新
						for(var key in _this.submit){
							_this.initialData[key] = _this.submit[key]
						}
					},500)
				}else{
					_this.$message.error(response.body.msg)
				}
				_this.isLoading = false;
				// alert(_this.isLoading);
			})
		},
		// 客户信息和其他信息
		getDetail:function(params,resolve){
				var _this = this;
				if(!params.id){
					return false;
				}
				var data = _this.search;
				data.id = params.id;
				var url = '/order-web/api/transit/cus/get';

				this.ajax.postStream(url,/*self.submitData*/data,(response)=>{
					if(response.body.result){
						// _this.materiList = response.body.content.items || [];
						_this.operationList = response.body.content.logList;
						_this.submit.cust_name = response.body.content.cust_name;
						_this.submit.cust_id = response.body.content.cust_id;
						_this.submit.id = response.body.content.id;
						_this.otherParams.id = response.body.content.id;
						_this.submit.describe_str = response.body.content.describe_str;
						_this.submit.last_modifier_name = response.body.content.last_modifier_name;
						_this.submit.creator_name = response.body.content.creator_name;
						_this.submit.create_time = Fn.dateFormat(response.body.content.create_time, 'yyyy-MM-dd hh:mm:ss');
						_this.submit.last_modify_time = Fn.dateFormat(response.body.content.last_modify_time, 'yyyy-MM-dd hh:mm:ss');
						_this.submit.if_send_email = response.body.content.if_send_email;
						_this.submit.email_address = response.body.content.email_address;
						
						resolve && resolve();
					}else{
						// this.$message.error(response.body.msg)
						resolve && resolve();
					}
					// this.isLoading=false
				},e=>{
					this.isLoading=false
					this.$message.error(e)
					resolve && resolve();
				})
		},
		delAddress(){
			if(this.curMareriObj.length == 0){
				this.$message.error('请先选择需要删除的物料');
				return;
            }
            let ifAdd = this.curMareriObj.some(item => {
                return item.id
            })
			if(ifAdd){
				this.$message.error('已保存的数据不能删除，只能失效');
				return;
			}
			let i = this.materiList.length;
			while(i--) {
                this.curMareriObj.forEach(item => {
                    if(this.materiList[i].tempId == item.tempId) {
                        this.materiList.splice(i, 1);
                    }
                })
			}
		},
		changeMarteri() {
			if(!this.curMareriObj){
				this.$message.error('请选择物料信息行')
				return
			}
			let params = {
				ifChange: true,
				materialObj: this.curMareriObj
			}, self = this
			params.callback = (d)=>{
				// self.curMareriObj.materia_id = d.materia_id
				self.curMareriObj.materiel_no = d.materiel_no
				self.curMareriObj.materiel_name = d.materiel_name
				self.curMareriObj.materiel_spec = d.materiel_spec
				self.curMareriObj.customer_materiel_no = d.customer_materiel_no
				self.curMareriObj.customer_materiel_name = d.customer_materiel_name
				self.curMareriObj.if_take_effect = d.if_take_effect == true ? 'Y' : 'N'
				self.curMareriObj.source_material_id = d.materia_id
				self.curMareriObj.customer_materiel_spec = d.customer_materiel_spec
				self.curMareriObj.barcode = d.barcode
				self.curMareriObj.materiel_texture = d.materiel_texture
			}
			self.$root.eventHandle.$emit('alert',{
				params:params,
				component:() => import('@components/cus_martriel/addMarternel.vue'),
				close:function(){},
				style:'width:1100px;height:250px',
				title:'新增物料'
			});
		},
		uploadCallback(d) {
			if (d.body.result) {
                this.getDetail(this.params)
                this.getMaterialList(this.params)
			}
		},
		exportMarteri() {
            let params = this.search, self = this
            params.id = this.params.id;
			let postData = Object.assign({}, params, this.materialPageObj)
			this.ajax.postStream('/order-web/api/transit/cus/exportMateriel',postData,(response)=>{
				// resolve && resolve()
				if(response.body.result){
					self.$message.success(response.body.msg)
				}else{
					self.$message.error(response.body.msg)
				}
			},e=>{
				// resolve && resolve()
				self.$message.error(e)
			})
		},
		getMaterialList(data,resolve) {
			let params = this.search
			params.id = data.id;
			let postData = Object.assign({}, params, this.materialPageObj)
			this.ajax.postStream('/order-web/api/transit/cus/listDeatil',postData,(response)=>{
				resolve && resolve()
				if(response.body.result){
					this.materiList = response.body.content.list || [];
					this.materialCount = response.body.content.count || 0;
				}else{
					this.$message.error(response.body.msg)
				}
			},e=>{
				resolve && resolve()
				this.$message.error(e)
			})
		},
		refresh() {
			this.getDetail(this.params)
			this.getMaterialList(this.params)
		},
		materiaSizeChange (size) {
			this.materialPageObj.page_size = size
			this.getMaterialList(this.params)
		},
		materiaPageChange (page) {
			this.materialPageObj.page_no = page
			this.getMaterialList(this.params)
		},
	},
	mounted:function(){
		let _this = this;
		if (this.params.id) {
			this.refresh()
		}
		setTimeout(function(){
			// 页面初始化数据，用作对比，是否有更新
			for(var key in _this.submit){
				_this.initialData[key] = _this.submit[key]
			}
		},500)
		_this.params.__close = _this.closeTab
	}
}
</script>
<style type="text/css" scoped>
.el-select .el-input{width:120px}
.mgt10{
	min-height:60px;
}
</style>