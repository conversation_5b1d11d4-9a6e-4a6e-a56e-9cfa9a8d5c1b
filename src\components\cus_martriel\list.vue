<!--物料列表-->
<template>
	<xpt-list 
		:data='transitList' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='count' 
		:searchPage='search.page_name' 
		:selection='selection' 
		@search-click='searching' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange' 
		@radio-change='radioChange' 
		@row-dblclick='rowDblclick'  
		ref='transitList'
	></xpt-list>
</template>
<script>
export default {
	props:["params"],
	data(){
		let self = this
		return {
			showSearch:false,
			search:{
				page_size: self.pageSize,     //页数
				page_no:1,   //页码
                page_name: 'transit_customer_materiel',
                where: []
			},
			transitList:[],
			actions_value:"",
			count:0,
			pageNow:1,
			multipleSelection: [],
			// 单选选中的行
			returnObj:"",
			selectId:"",
			searchName:'',

			selection: '',
			btns: [
				{
					type: 'success',
					txt: '刷新',
					click() {
						self.refresh()
					}
				}, {
					type: 'primary',
					txt: '新增',
					click() {
						self.addMartriel('新增客户物料',{})
					}
				}
			],
			cols: [
				{
					label: '单据编号',
                    prop: 'id',
                    redirectClick(row) {
						self.viewDetail(row.id)
					}
				}, {
					label: '客户',
					prop: 'cust_name'
				}, {
					label: '描述',
					prop: 'describe_str',
				}
				//  ,{
				// 	label: '状态',
				// 	prop: 'if_take_effect',
				// }
			]
		}
	},
	methods:{
		preValid(api){
			var _this = this;
			var url = "/order-web/api/cus"+api;
			// 事件前验证
			if(_this.multipleSelection.length==0){
				_this.$alert('没有选择任何数据，请先选择数据！', '提示', {
					confirmButtonText: '确定'
				})
			}else{
				var custIdList = [];
				_this.multipleSelection.forEach(function(item,index,array){
					custIdList.push(item.cust_id);
				});

				this.ajax.postStream(url,custIdList,function(response){
					if(response.body.result){
						_this.searching();
						_this.$message({
									message: '操作成功',
										type: 'success'
								});
						// 重置业务操作
						_this.actions_value = "";
					}
				});
			}
		},
		// 新增
		addMartriel(name,params){
			this.$root.eventHandle.$emit('creatTab', {
				name:name, 
				params:params, 
				component:() => import('@components/cus_martriel/detail.vue')
			});
		},
		// 查看详情
		viewDetail(id){
			var params = {};
			if(this.params.isAlert)return;
			params.id = id;
			this.addMartriel("客户物料详情",params);
		},
		openSearch(){
			this.showSearch = !this.showSearch
		},
		sizeChange(size){
			// 第页数改变
			this.search.page_size = size;
			this.searching();
		},
		pageChange(page_no){
			// 页数改变
			this.pageNow = page_no;
			this.search.page_no = page_no;
			this.searching();
		},
		preSearching(){
			this.searchName = this.search.name
			this.searching()
		},
		//列表显示
		searching(d, resolve){
			if(d) {
				this.search.where = d
			}
			var _this = this;
			if(_this.params.isAlert){
				_this.search.effective_status = 'B';
			}
			this.ajax.postStream('/order-web/api/transit/cus/list',_this.search,function(response){
				if(response.body.result){
					_this.transitList = response.body.content.list;
					_this.count = response.body.content.count;
				}
				else{
					_this.$message.error(response.body.msg)
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
		refresh(){
			this.searching()
		},
		radioChange(obj) {
			this.returnObj = obj
		},
		rowDblclick(obj) {
			if(this.params.isAlert) {
				this.params.close(obj)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
		}
	},
	mounted: function(){
		var _this = this;
		_this.searching();
		// 检测新增、编辑数据，刷新
		_this.$root.eventHandle.$on('close_addCustomer',function(){
			_this.searching();
		})

		// 弹窗打开此组件,新增销售订单时会调用
		if(_this.params.isAlert) {
			this.selection = 'radio'
			this.btns = [{
				type: 'primary',
				txt: '确认',
				click() {
					if(!_this.returnObj){
						this.$message.error("请选择数据");
						return;
					}
					_this.params.close(_this.returnObj);
					_this.$root.eventHandle.$emit('removeAlert',_this.params.alertId)
				}
			}]
		}
	},
	destroyed(){
		this.$root.offEvents('close_addCustomer');
	}
}
</script>