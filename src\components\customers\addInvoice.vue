<!--新增发票-->
<template>
	<div class="mgt10"> 
		<el-form label-position="right" label-width="95px" :model="submitData" :rules="rules" ref="submitData">
			<el-row	:gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="发票抬头" prop="invoice_title">
						<el-input size='mini' v-model="submitData.invoice_title"></el-input>
						<el-tooltip v-if='rules.invoice_title[0].isShow' class="item" effect="dark" :content="rules.invoice_title[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="纳税人识别号" prop="taxpayer_number">
						<el-input size='mini' v-model="submitData.taxpayer_number"></el-input>
						<el-tooltip v-if='rules.taxpayer_number[0].isShow' class="item" effect="dark" :content="rules.taxpayer_number[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="经营地址" prop="operate_address">
						<el-input size='mini' v-model="submitData.operate_address"></el-input>
						<el-tooltip v-if='rules.operate_address[0].isShow' class="item" effect="dark" :content="rules.operate_address[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="40" class="mgt10">
				<el-col :span='8'>
					<el-form-item label="经营电话" prop="operate_tell">
						<el-input size='mini' v-model="submitData.operate_tell"></el-input>
						<el-tooltip v-if='rules.operate_tell[0].isShow' class="item" effect="dark" :content="rules.operate_tell[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="开户行" prop="opening_bank">
						<el-input size='mini' v-model="submitData.opening_bank"></el-input>
						<el-tooltip v-if='rules.opening_bank[0].isShow' class="item" effect="dark" :content="rules.opening_bank[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="银行账号" prop="bank_account">
						<el-input size='mini' v-model="submitData.bank_account"></el-input>
						<el-tooltip v-if='rules.bank_account[0].isShow' class="item" effect="dark" :content="rules.bank_account[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			
			<el-row :gutter='40' class="mgt10">
				
				<el-col :span='8'>
					<el-form-item label="收件人" prop="addressee">
						<el-input size='mini' v-model="submitData.addressee"></el-input>
						<el-tooltip v-if='rules.addressee[0].isShow' class="item" effect="dark" :content="rules.addressee[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="联系方式" prop="contact_information">
						<el-input size='mini' v-model="submitData.contact_information"></el-input>
						<el-tooltip v-if='rules.contact_information[0].isShow' class="item" effect="dark" :content="rules.contact_information[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="收货地址" prop="receiving_address">
						<el-input size='mini' v-model="submitData.receiving_address"></el-input>
						<el-tooltip v-if='rules.receiving_address[0].isShow' class="item" effect="dark" :content="rules.receiving_address[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="是否生效" prop="status">
						<el-switch
							v-model="submitData.status"
							active-text="生效"
							inactive-text="失效">
						</el-switch>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter='40' class="mgt10">
				<el-col :span="24" style="text-align:right;">
					<el-button type='success' size='mini' @click="preSave('submitData')">保存</el-button>
					<el-button type='warning' size='mini' @click="closeWindow">取消</el-button>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
import validate from '@common/validate.js'
export default {
	props:["params"],
	data(){
		var self = this;
		return {
			provinceObj:{},
			cityObj:{},
			areaObj:{},
			streetObj:{},
			submitData:{
				invoice_title:'',
				taxpayer_number:'',
				operate_address:'',
				operate_tell:'',
				opening_bank:'',
				bank_account:'',
				addressee:'',
				contact_information:'',
				receiving_address:'',
				code:'',
				status:''//状态
			},
			rules:{
				addressee:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
				invoice_title:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
				taxpayer_number:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
				operate_address:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
				operate_tell:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
				opening_bank:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
				bank_account:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
				contact_information:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
				receiving_address:validate.isNotBlank({
					required:true,
					isShow:true,
					self:self,
					trigger:"change"
				}),
			},
			deliver_method_Options:[
				{
					value:'THREES',
					label:'三包'
				},{
					value:'LOGISTICS',
					label:'物流'
				},{
					value:'PICK_UP',
					label:'仓库自提'
				},{
					value:'ZTO',
					label:'中通小件'
				},{
					value:'ZTO_FREE',
					label:'中通免费'
				},{
					value:'SF',
					label:'顺丰'
				}
			],
			post_fee_type_Options:[
				{
					value:'NOW_PAY',
					label:'现付'
				},{
					value:'ARRIVE_PAY',
					label:'到付'
				}
			],
			addressNums:{
				stateNum:'',
				cityNum:'',
				districtNum:'',
				streetNum:''
			}
		}
	},
	methods:{
		closeWindow(){
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		
		preSave(formName){
			var self = this
			self.$refs[formName].validate((valid) => {
				if(!valid) return
				self.save()
			});
		},
		save(){
			var self = this;
			// if(self.params.ifCallback){//客户模块地址新增，复制新增
				// console.log(self.submitData.if_take_effect)
				console.log(self.submitData)
				self.submitData.status = self.submitData.status == true ? 'Y' : 'N'
				self.params.callback(self.submitData)
				self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
			// }
		}
	},
	mounted:function(){
		var self = this;
		if(self.params.ifCopy){//判断是否复制新增
			var url = '/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId'
			self.submitData.receiver_name = self.params.addressObj.receiver_name
			self.submitData.receiver_phone = self.params.addressObj.receiver_phone
			self.submitData.receiver_mobile = self.params.addressObj.receiver_mobile
			self.submitData.receiver_address = self.params.addressObj.receiver_address
			self.submitData.post_fee_type = self.params.addressObj.post_fee_type
			self.submitData.deliver_method = self.params.addressObj.deliver_method

      		new Promise((resolve,reject)=>{//请求省份信息，赋值省份编码
				self.ajax.postStream(url,1,function(response){
					if(response.body.result){
						self.provinceObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_state=self.params.addressObj.receiver_state
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_state) return;
			new Promise((resolve,reject)=>{//根据省份信息获得市级信息，赋值市级编码
				self.ajax.postStream(url,self.params.addressObj.receiver_state,function(response){
					if(response.body.result){
						self.cityObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_city=self.params.addressObj.receiver_city
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_city) return;
			new Promise((resolve,reject)=>{
				//根据市级信息获得区级信息，赋值区级编码
				self.ajax.postStream(url,self.params.addressObj.receiver_city,function(response){
					if(response.body.result){
						self.areaObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_district=self.params.addressObj.receiver_district
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_district) return;
			new Promise((resolve,reject)=>{//根据区级信息获得街道信息，赋值街道编码
				self.ajax.postStream(url,self.params.addressObj.receiver_district,function(response){
					if(response.body.result){
						self.streetObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_street=self.params.addressObj.receiver_street
			}).catch(error=>{
				self.$message.error(error)
			})
		} else {
			// self.getAreaCode(1,"provinceObj");//新增
		}
	},
	destroyed(){
	}
}
</script>