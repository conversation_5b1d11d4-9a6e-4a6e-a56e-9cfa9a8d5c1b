<!--添加客户/客户详情-->
<template>
    <div class="xpt-flex">
        <el-row class="xpt-top" :gutter="40">
            <el-col :span="24">
                <el-button
                    type="primary"
                    size="mini"
                    @click="preSave('submit')"
                    :disabled="/^(LOGISTICSCOMPANY|DEALER)$/.test(submit.member_type)"
                    :loading="isLoading"
                >保存</el-button>
                <el-button type='warning' size='mini' @click="pilotSale" :loading="isPilotSaleLoading" v-if="!!params.cust_id">试点再售</el-button>
                <!-- <el-button type='danger' class='xpt-close' size='mini' @click="closeTab">关闭</el-button> -->
            </el-col>
        </el-row>
        <el-row :gutter="40">
            <el-tabs v-model="firstTab">
                <el-tab-pane label="客户" name="customer">
                    <el-form
                        label-position="right"
                        label-width="100px"
                        :model="submit"
                        :rules="rules"
                        ref="submit"
                    >
                        <el-row class="mgt10">
                            <el-col :span="8">
                                <el-form-item label="客户编码" prop="number">
                                    <el-input
                                        size="mini"
                                        disabled
                                        v-model="submit.number"
                                        :maxlength="code_max"
                                    ></el-input>
                                </el-form-item>
                                 <el-form-item label="客户名称" prop="name">
                                    <el-input
                                        size="mini"
                                        v-model="submit.name"
                                        :maxlength="name_max"
                                        :disabled="submit.number?true:false"
                                        v-on:keyup.native="nameChange"
                                    ></el-input>
                                    <el-tooltip
                                        v-if="rules.name[0].isShow"
                                        class="item"
                                        effect="dark"
                                        :content="rules.name[0].message"
                                        placement="right-start"
                                        popper-class="xpt-form__error"
                                    >
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="客户简称" prop="cust_short_name">
                                    <el-input
                                        size="mini"
                                        v-model="submit.cust_short_name"
                                        :maxlength="name_max"
                                        :disabled="submit.number?true:false"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="客户类型" prop="member_type">
                                    <xpt-select-aux
                                        v-model="submit.member_type"
                                        aux_name="customerSource"
                                        :disabledOption="[]"
                                        @change="(val, targetObj) =>
										 !submit.number && (submit.name = targetObj.tag)
										 "
                                        :disabled="submit.number?true:false"
                                    ></xpt-select-aux>
                                    <!-- <el-select size="mini" v-model="submit.member_type" placeholder="请选择" :disabled="submit.number?true:false">
												<el-option
													v-for="(item,index) in member_type_options"
													:label="item.label"
													:value="item.value" :key='index'>
												</el-option>
                                    </el-select>-->
                                    <el-tooltip
                                        v-if="rules.member_type[0].isShow"
                                        class="item"
                                        effect="dark"
                                        :content="rules.member_type[0].message"
                                        placement="right-start"
                                        popper-class="xpt-form__error"
                                    >
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                 <el-form-item label="生效状态" v-if="params.cust_id?true:false">
                                    <el-select
                                        size="mini"
                                        v-model="submit.effective_status"
                                        placeholder="请选择"
                                    >
                                        <el-option
                                            v-for="(value,key) in effective_status_options"
                                            :label="value"
                                            :value="key"
                                            :key="key"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="试点再售" v-if="params.cust_id?true:false">
                                    <el-switch
                                        v-model="submit.if_resale_point"
                                        on-value="Y"
                                        off-value="N"
                                        on-text="是"
                                        off-text="否"
                                        disabled>
                                    </el-switch>
                                </el-form-item>
                            </el-col>

                            <el-col :span="8">
                                <el-form-item label="电商账号">
                                    <el-input
                                        size="mini"
                                        v-model="submit.account"
                                        :disabled="submit.number?true:false"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item label="支付宝账号">
                                    <el-input
                                        size="mini"
                                        v-model="submit.alipay_account"
                                        :disabled="submit.number?true:false"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item label="服务商"  v-if="submit.member_type=='LOGISTICSCOMPANY'">
                                    <xpt-input
                                        v-model="submit.three_supplier_name"
                                        icon="search"
                                        :on-icon-click="openCompany"
                                        size="mini"
                                        readonly
                                        @change="companyChange"
                                    ></xpt-input>
                                </el-form-item>
                            </el-col>

                            <!-- <el-col :span="8">

                            </el-col>
                            <el-col :span="8">

                            </el-col>
                            <el-col :span="8">

                            </el-col>
                            <el-col :span="8">

                            </el-col> -->
                        </el-row>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
        </el-row>
        <el-row class="xpt-flex__bottom">
            <el-tabs v-model="secondTab" @tab-click="changeTab">
                <el-tab-pane label="支付信息" name="payment" class="xpt-flex">
                    <div class="xpt-flex__bottom scroll">
                        <el-table
                            :data="paymentList"
                            border
                            tooltip-effect="dark"
                            width="100%"
                            style="width: 100%;"
                        >
                            <el-table-column prop="pay_channel" label="支付方式">
                                <template
                                    slot-scope="scope"
                                >{{scope.row.pay_channel|auxFormat('payType')}}</template>
                            </el-table-column>
                            <el-table-column prop="pay_method" label="支付类型" :formatter="payMethod"></el-table-column>
                            <el-table-column prop="pay_account" label="支付账号"></el-table-column>
                            <el-table-column prop="bank" label="开户行"></el-table-column>
                            <el-table-column prop="bank_user_name" label="用户名"></el-table-column>
                            <el-table-column prop="receiver_account" label="收款账号"></el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="地址信息" name="address" class="xpt-flex">
                    <xpt-list-dynamic
                        :data="addressList"
                        :btns="customerBtns"
                        :colData="customerCols"
                        selection="radio"
                        isNeedClickEvent
                        @radio-change="customerRadioChange"
                        :searchPage="addressPage.page_name"
                        :showCount ='showCount'
                        @search-click='prSearch'
                        :pageTotal='addressCount'
                        @count-off="countOff"
                        :pageLength="addressPage.page_size"
                        @page-size-change='addressSizeChange'
                        @current-page-change='addressPageChange'
                        id="customer_address_list"
                    >
                    </xpt-list-dynamic>
                </el-tab-pane>

                <el-tab-pane label="客户关系" name="relationship" class="xpt-flex">
                    <div class="xpt-flex__bottom scroll">
                        <el-table
                            :data="customerList"
                            border
                            tooltip-effect="dark"
                            width="100%"
                            style="width: 100%;"
                        >
                            <el-table-column prop="master_cust_name" label="主客户"></el-table-column>
                            <el-table-column prop="merge_cust_name" label="合并客户"></el-table-column>
                            <el-table-column prop="merge_number" label="合并单号"></el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <!--<el-tab-pane label="发票信息" name="invoice" class='xpt-flex'>
				<el-row	class='xpt-top'	:gutter='40'>
						&lt;!&ndash; <el-col :span="24">
						<el-button type='primary' size='mini' @click="addInvoice" >新增行</el-button >
						<el-button type='danger'  size='mini' @click="delInvoice">删除行</el-button>
						<el-button type='primary' size='mini' @click="effectInvoice">生效</el-button >
						<el-button type='primary' size='mini' @click="invalidInvoice">失效</el-button >
					</el-col> &ndash;&gt;
				</el-row>
					<div class="xpt-flex__bottom scroll">
						<xpt-list
						:data='invoiceList'
						:btns='invoicebtns'
						:colData='invoicecol'
						:selection='selection'
						:pageTotal='count'
						:isNeedClickEvent="true"
						@selection-change='select'
						@radio-change='handleCurrentChange'
					></xpt-list>
				  </div>
                </el-tab-pane>-->
                <el-tab-pane label="其他信息" name="other_info">
                    <el-form label-position="right" label-width="100px">
                        <el-row class="mgt10">
                            <el-col :span="8">
                                <el-form-item label="创建人：">{{other_info.creator_name}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="创建日期：">{{other_info.create_date_str}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="修改人：">{{other_info.modify_name}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="修改日期：">{{other_info.modify_date_str}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                    label="生效状态："
                                >{{effective_status_options[other_info.effective_status]}}</el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>

                <!-- <el-tab-pane label="接待信息" name="receptionRecordList" class='xpt-flex'>
				<el-row	class='xpt-top'	:gutter='40'>
				</el-row>
					<div class="xpt-flex__bottom scroll">
						<xpt-list
						:data='receptionRecordList'
						:btns='[]'
						:colData='receptionCols'
					></xpt-list>
				  </div>
                </el-tab-pane>-->
            </el-tabs>
        </el-row>
    </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
    props: ["params"],
    data() {
        var _this = this;
        var self = this;
        return {
            isLoading: false, //判断是否处于提交状态
            selectLine: null,
            forbid_options: "",
            radioSelect: "",
            receptionRecordList: [],
            selectInvoice: [],
            idExistence: _this.params.cust_id ? true : false,
            idExistence_eidt: this.params.cust_id ? true : false,
            firstTab: "customer",
            secondTab: "payment",
            submit: {
                name: "",
                number: "",
                alipay_account: "",
                account: "",
                member_type: "",
                effective_status: "",
                three_supplier_name: "",
                three_supplier_code: "",
                cust_short_name: '',
                if_resale_point: '',
                // bdCustomerInvoices:[]
                // document_status:''
            },
            approve_options: "",
            add_address_opitons: "",
            other_info: {},
            member_type_options: [
                {
                    value: "TB",
                    label: "淘宝客户"
                },
                {
                    value: "B2C",
                    label: "商城客户"
                },
                {
                    value: "none",
                    label: "其他客户"
                },
                {
                    value: "APP",
                    label: "导购App客户"
                },
                {
                    value: "PDD",
                    label: "拼多多"
                },
                {
                    value: "JD",
                    label: "京东"
                }
            ],
            document_status_options: {
                Z: "暂存",
                A: "创建",
                B: "审核中",
                C: "审核",
                D: "重新审核"
            },
            effective_status_options: {
                A: "失效",
                B: "生效"
            },
            ifShow: true,
            // 支付列表
            paymentList: [],
            // 地址列表
            addressList: [],
            // 客户关系
            customerList: [],
            // 发票列表
            /*invoiceList:[],
			invoicebtns:[{
					type: 'primary',
					txt: '新增行',
					click(){
						self.addInvoice()
					}
				},{
					type: 'primary',
					txt: '删除行',
					click(){
						self.delInvoice()
					}
				},{
					type: 'primary',
					txt: '生效',
					click(){
						self.effectInvoice()
					}
				},{
					type: 'primary',
					txt: '失效',
					click(){
						self.invalidInvoice()
					}
				}
				],*/
            selection: "radio",
            deliverOption:[],
            invoicecol: [
                {
                    label: "发票抬头",
                    prop: "invoice_title"
                },
                {
                    label: "纳税人识别号",
                    prop: "taxpayer_number"
                },
                {
                    label: "经营地址",
                    prop: "operate_address"
                },
                {
                    label: "经营电话",
                    prop: "operate_tell"
                },
                {
                    label: "开户行",
                    prop: "opening_bank"
                },
                {
                    label: "银行账号",
                    prop: "bank_account"
                },

                {
                    label: "收件人",
                    prop: "addressee"
                },
                {
                    label: "联系方式",
                    prop: "contact_information"
                },
                {
                    label: "收货地址",
                    prop: "receiving_address"
                },
                {
                    label: "状态",
                    prop: "status",
                    formatter(val) {
                        if (val == "N") {
                            return "失效";
                        }
                        if (val == "Y") {
                            return "生效";
                        }
                        return val;
                    }
                }
            ],
            count: 0,
            pageNow: 1,
            page: {
                page_no: 1,
                page_size: 0
            },
            addressPage: {
                page_no: 1,
                page_size: 20,
                where: [],
                page_name: 'bd_receiver_info_group'
            },
            showCount: false,
            addressCount: 0,
            // 选中的地址对象
            curAddressObj: null,
            rules: {
                name: validate.isNotBlank({
                    required: true,
                    self: _this
                }),
                member_type: validate.isNotBlank({
                    required: true,
                    self: _this
                })
            },
            initialData: {},
            visible: "",
            code_max: 25,
            name_max: 25,
            // 数据对比
            comparedProp: {
                receiver_name: null,
                receiver_state_name: null,
                receiver_city_name: null,
                receiver_district_name: null,
                receiver_street_name: null,
                receiver_address: null
            },
            customerBtns: [
                {
                    type: "primary",
                    txt: "新增",
                    click() {
                        _this.add_address("add");
                    }
                },
                {
                    type: "primary",
                    txt: "复制新增",
                    click() {
                        _this.add_address("copy_add");
                    }
                },
                {
                    type: "danger",
                    txt: "删除",
                    click: _this.delAddress
                }
            ],
            customerCols: [
                {
                    label: "地址编码",
                    prop: "number"
                },
                {
                    label: "客户",
                    prop: "cust_name"
                },
                {
                    label: "收货人",
                    prop: "receiver_name"
                },
                // {
                //     label: "送货方式",
                //     prop: "deliver_method",
                //     width: 120,
                //     bool: true,
                //     elSelect: true,
                //     options: [],
                //     disabled: _this.customerSelectDisabled,
                //     change(row, index) {
                //         if (_this.isInit) return;
                //         _this.compareForDifference(row, index);
                //         _this.checkDeliverMethod(row);
                //     }
                // },
                {
                    label: "运费类型",
                    prop: "post_fee_type",
                    bool: true,
                    elSelect: true,
                    options: [
                        {
                            value: "NOW_PAY",
                            label: "现付"
                        },
                        {
                            value: "ARRIVE_PAY",
                            label: "到付"
                        }
                    ],
                    disabled: _this.customerSelectDisabled,
                    change: _this.compareForDifference
                },
                {
                    label: "省",
                    prop: "receiver_state_name"
                },
                {
                    label: "市",
                    prop: "receiver_city_name"
                },
                {
                    label: "区",
                    prop: "receiver_district_name"
                },
                {
					label: '街道',
					prop: 'receiver_street_name'
				},
                {
                    label: "详细地址",
                    prop: "receiver_address"
                },
                {
                    label: "收货人手机",
                    prop: "receiver_mobile"
                },
                {
                    label: "收货人固话",
                    prop: "receiver_phone"
                },
                {
                    label: "是否原始地址",
                    prop: "is_original",
                    format: "yesOrNo"
                },
                {
                    label: "状态",
                    prop: "effective_status",
                    formatter(val) {
                        if (val == "A") {
                            return "失效";
                        }
                        if (val == "B") {
                            return "生效";
                        }
                        return val;
                    }
                }
            ],
            isInit: false,
            receptionCols: [
                {
                    label: "导购员",
                    prop: "userName"
                },
                {
                    label: "展厅",
                    prop: "storeName"
                },
                {
                    label: "开始时间",
                    prop: "startTime"
                },
                {
                    label: "结束时间",
                    prop: "endTime"
                },
                {
                    label: "时长",
                    prop: "duration"
                },
                {
                    label: "是否成交",
                    prop: "dealed",
                    formatter(val) {
                        if (val) {
                            return "是";
                        } else {
                            return "否";
                        }
                    }
                },
                {
                    label: "下次跟进",
                    prop: "nextFollowTime"
                }
            ],
            isPilotSaleLoading:false,//试点再售按钮状态
            countOffFlag: false,
        };
    },
    methods: {
        addressSizeChange(size){
			// 第页数改变
			this.addressPage.page_size = size;
			this.getAddressInfo();
		},
		addressPageChange(page_no){
			// 页数改变
			this.addressPage.page_no = page_no;
			this.getAddressInfo();
		},
        countOff(){
			let self = this,
			url = "/order-web/api/customer/receiverInfo/getReceiverInfoListCount";
			if(!self.addressList.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;

			self.ajax.postStream(url,self.addressPage,function(response){
				if(response.body.result){
					self.addressCount = response.body.content;
					self.showCount = true;
					self.countOffFlag = false;

				}else{
					self.countOffFlag = false;
					self.$message.error(response.body.msg);
				}
			});
		},
        prSearch (list,resolve) {
            this.addressPage.where = list;
            new Promise((res,rej)=>{
                this.getAddressInfo(resolve,res);
            }).then(()=>{
                if(this.addressPage.page_no != 1){
                    this.addressCount = 0;
                }
                this.showCount = false;
            })
        },
        nameChange(e){
            if(e.keyCode == 32){
                console.log(this.submit.name)
                this.$message.error("客户名称不能输入空格");
                this.submit.name = this.submit.name.replace(/\s/g,"");
                // return false;
            }
            // console.log(e);
        },
        /*
		控制送货方式、运费类型是否可以变更
		新增的可以更改，
		原始地址的不能编辑
		保存之后不可以编辑
		*/
        customerSelectDisabled(row) {
            if (row.fid || row.is_original) {
                return true;
            } else {
                return false;
            }
        },
        customerRadioChange(obj) {
            this.curAddressObj = obj;
        },
        /*
		判断是否存在相同的地址信息（根据收货人，省，市，区，街道，详情地址，配送方式等七个字段判断，一个不同则可视为不同的地址，即可添加）
		*/
        compareForDifference(comparedObj, index) {
            let self = this;
            if (self.addressList.length <= 1) return;
            for (var i = 0; i < self.addressList.length; i++) {
                if (
                    index != i &&
                    comparedObj.deliver_method ==
                        self.addressList[i].deliver_method
                ) {
                    var count = 0;
                    for (var key in self.comparedProp) {
                        if (comparedObj[key] == self.addressList[i][key])
                            count += 1;
                    }
                    if (count == 6) {
                        self.$message.error("已存在相同的地址信息");
                        self.addressList[index].deliver_method = "";
                        // self.addressList[index].if_exist_deliver_method = false;
                        return;
                    }
                }
            }
        },
        // 新增地址
        add_address(option) {
            var _this = this;
            if (option == "add") {
                _this.alert();
            } else if (option == "copy_add") {
                if (_this.curAddressObj == null) {
                    _this.$message.error("没有选择任何数据，请先选择数据！");
                } else {
                    _this.alert(_this.curAddressObj);
                }
            }
        },
        // 地址弹窗
        alert(obj) {
            var self = this;
            console.log(obj);
            if (obj) {
                self.params.addressObj = obj;
                self.params.ifCopy = true;
            } else {
                self.params.ifCopy = false;
            }
            self.params.ifCallback = true;
            self.params.callback = d => {
                let newAddressObj = {};
                for (let [key, value] of Object.entries(d)) {
                    newAddressObj[key] = value;
                }

                if(/^(DEALER|LOGISTICSCOMPANY)$/.test(self.submit.member_type)){
                    self.addDealerReceriverInfo(newAddressObj);
                }else{
                    Object.assign(newAddressObj, {
                        fid: "",
                        number: "",
                        tempId: +new Date()
                    });
                    self.addressList.push(newAddressObj);
                }
            };
            self.$root.eventHandle.$emit("alert", {
                params: self.params,
                component: () => import("@components/customers/addAddress.vue"),
                close: function() {},
                style: "width:1100px;height:250px",
                title: "新增地址"
            });
        },
        // 经销商添加地址
        addDealerReceriverInfo(newAddressObj){
            let self = this;
            self.ajax.postStream('/order-web/api/customer/receiverInfo/addDealerReceriverInfo',newAddressObj,function(response){
						if(response.body.result){
							//self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
							//self.$root.eventHandle.$emit("close_addAddress");
                            self.$message.success(response.body.msg || '')
                            // self.addressList.push(newAddressObj);
                            self.getAddressInfo();

						}else{
							self.$message.error(response.body.msg || '')
						}
					})
        },
        // tab切换
        changeTab(tab, event) {
            this.page.page_no = 1;
            if (!this.params.cust_id) return;
            if (tab.name == "payment") {
                this.getPaymentInfo();
                this.ifShow = true;
            } else if (tab.name == "address") {
                this.getAddressInfo();
                this.ifShow = true;
            } else if (tab.name == "relationship") {
                this.getRelationInfo();
                this.ifShow = true;
            } /*else if(tab.name == "invoice"){
				this.getInvoiceDetail();
				this.ifShow = true;
			}*/ else if (
                tab.name == "other_info"
            ) {
                this.getCustomerDetail();
                this.ifShow = false;
            } else if (tab.name == "receptionRecordList") {
                this.getrReceptionRecordList();
                this.ifShow = true;
            }
        },
        sizeChange(size) {
            // 第页数改变
            this.page.page_size = size;
            if (this.secondTab == "payment") {
                this.getPaymentInfo();
            } else if (this.secondTab == "address") {
                this.getAddressInfo();
            } else if (this.secondTab == "relationship") {
                this.getRelationInfo();
            }
        },
        pageChange(page_no) {
            // 页数改变
            this.pageNow = page_no;
            this.page.page_no = page_no;
            if (this.secondTab == "payment") {
                this.getPaymentInfo();
            } else if (this.secondTab == "address") {
                this.getAddressInfo();
            } else if (this.secondTab == "relationship") {
                this.getRelationInfo();
            }
        },
        // 关闭标签页
        closeTab() {
            var _this = this,
                isUpdate;
            isUpdate = this.compareData(_this.submit, _this.initialData);
            if (isUpdate) {
                _this.$root.eventHandle.$emit("openDialog", {
                    ok() {
                        _this.save(() => {
                            _this.$root.eventHandle.$emit(
                                "removeTab",
                                _this.params.tabName
                            );
                        });
                    },
                    no() {
                        _this.$root.eventHandle.$emit(
                            "removeTab",
                            _this.params.tabName
                        );
                    }
                });
            } else {
                _this.$root.eventHandle.$emit(
                    "removeTab",
                    _this.params.tabName
                );
            }
        },
        preSave(formName) {
            var _this = this;

            _this.$refs[formName].validate(valid => {
                if (!valid) return;

                _this.save();
            });
        },
        save(callback) {
            // console.log(this.invoiceList)
            var _this = this;
            var url, func;
            var params = _this.submit;
            let addressList = JSON.parse(
                    JSON.stringify(_this.addressList || [])
                ),
                i = addressList.length;
            while (i--) {
                delete addressList[i].disabled;
                delete addressList[i].tempId;
                delete addressList[i].effective_status_name;
            }
            params.receiverInfoVOs = addressList;
            // params.bdCustomerInvoices = JSON.parse(JSON.stringify(_this.invoiceList || []));
            if (_this.params.cust_id) {
                // url = "/order-web/api/customer/update";
                params.cust_id = _this.params.cust_id;
                func = function() {
                    _this.getCustomerDetail();
                    _this.getPaymentInfo();
                    // _this.getInvoiceDetail();
                    _this.getrReceptionRecordList();
                    _this.getAddressInfo();
                };
            } else {
                // url = "/order-web/api/customer/add";
                func = function(id) {
                    _this.params.cust_id = id;
                    _this.getCustomerDetail();
                    // _this.getInvoiceDetail();
                    _this.getrReceptionRecordList();
                    _this.getPaymentInfo();
                    _this.getAddressInfo();
                    _this.$root.eventHandle.$emit("updateTab", {
                        name: _this.params.tabName,
                        title: "客户详情"
                    });
                };
            }
            this.isLoading = true;
            this.ajax.postStream(
                "/order-web/api/customer/save?permissionCode=CUSTOMER_SAVE",
                params,
                function(response) {
                    if (response.body.result) {
                        _this.$message({
                            message: "操作成功",
                            type: "success"
                        });
                        response.body.content
                            ? func(response.body.content.cust_id)
                            : func();
                        _this.$root.eventHandle.$emit(
                            "close_addCustomer",
                            _this.params,
                            response.body.content
                        );
                        callback && callback();
                    } else {
                        _this.$message.error(response.body.msg);
                    }
                    _this.isLoading = false;
                }
            );
        },
        pilotSale(){
            let self=this;
            if (!self.params.cust_id) {
                return
            }
            this.isPilotSaleLoading=true;
            this.ajax.postStream('/order-web/api/customer/updateResalePoint',self.params.cust_id,(res)=>{
                if(res.body.result){
                    self.$message.success(res.body.msg)
                    self.getCustomerDetail();
                    self.getPaymentInfo();
                }else{
                    self.$message.error(res.body.msg)
                }
                self.isPilotSaleLoading=false;
            },err=>{
                self.$message.error(err)
                self.isPilotSaleLoading=false;
            })
        },
        // 客户信息和其他信息
        getCustomerDetail() {
            var _this = this;
            this.ajax.postStream(
                "/order-web/api/customer/get?permissionCode=CUSTOMER_QUERY",
                _this.params.cust_id,
                function(response) {
                    var obj = response.body;
                    if (obj.result) {
                        // 客户信息
                        for (let key of Object.keys(_this.submit)) {
                            _this.submit[key] = obj.content[key];
                        }
                        // 其他信息
                        _this.other_info = obj.content;
                        for (var key in _this.submit) {
                            _this.initialData[key] = _this.submit[key];
                        }
                    }
                }
            );
        },
        // 发票信息
        /*getInvoiceDetail(){
			var _this = this;
			this.ajax.postStream("/order-web/api/customer/invoice/getBdCustomerInvoiceList",{"customer_id":_this.params.cust_id,"page_no":1,"page_size":0},function(response){
				var obj = response.body;
				if(obj.result){
					// 客户信息
					console.log(obj)
					_this.invoiceList = obj.content.list;
					_this.submit.bdCustomerInvoices =  obj.content.list;
					// for(let key of Object.keys(_this.submit)){
					// 	_this.submit[key]=obj.content[key]
					// }
					// // 其他信息
					// _this.other_info = obj.content;
					// for(var key in _this.submit){
					// 	_this.initialData[key] = _this.submit[key]
					// }
				}

			});
		},*/
        // 接待信息
        getrReceptionRecordList() {
            var _this = this;
            this.ajax.postStream(
                "/app-web/app/receptionRecordList.do",
                { cust_id: _this.params.cust_id },
                function(response) {
                    var obj = response.body;
                    if (obj.result) {
                        // 客户信息
                        console.log(obj);
                        _this.receptionRecordList = obj.content;
                        // _this.submit.bdCustomerInvoices =  obj.content.list;
                        // for(let key of Object.keys(_this.submit)){
                        // 	_this.submit[key]=obj.content[key]
                        // }
                        // // 其他信息
                        // _this.other_info = obj.content;
                        // for(var key in _this.submit){
                        // 	_this.initialData[key] = _this.submit[key]
                        // }
                    }
                }
            );
        },
        // 支付信息
        getPaymentInfo() {
            var _this = this;
            var params = {
                cust_id: this.params.cust_id,
                page_no: this.page.page_no,
                page_size: this.page.page_size
            };
            this.ajax.postStream(
                "/order-web/api/customer/payAccount/getCustomerPayAccountList",
                params,
                function(response) {
                    var obj = response.body;
                    if (obj.result) {
                        _this.paymentList = obj.content.list;
                        _this.count = obj.content.count;
                    }
                }
            );
        },
        // 地址信息
        getAddressInfo(resolve, callback) {
            var _this = this;
            _this.isInit = true;
            this.ajax.postStream(
                "/order-web/api/customer/receiverInfo/getReceiverInfoList",
                this.addressPage,
                function(response) {
                    if (response.body.result && response.body.content) {
                        let content = response.body.content,
                            list = content.list || [],
                            i = list.length;
                        while (i--) {
                            if (
                                list[i].is_original &&
                                (!list[i].deliver_method ||
                                    !list[i].post_fee_type)
                            ) {
                                list[i].disabled = false;
                            } else {
                                list[i].disabled = true;
                            }
                        }
                        if(!_this.showCount){
                            let total = _this.addressPage.page_size * _this.addressPage.page_no;
                            _this.addressCount = response.body.content.list.length == (_this.addressPage.page_size+1)? total+1:total;
                        }
                        _this.addressList = list;
                    } else {
                        _this.$message.error(response.body.msg);
                    }
                    _this.$nextTick(() => {
                        _this.isInit = false;
                    });
                    resolve&&resolve()
                    callback && callback();
                },
                err => {
                    this.$message.error(err);
                    this.isInit = false;
                    resolve&&resolve()
                    callback && callback();
                }
            );
        },
        // 客户关系信息
        getRelationInfo() {
            var _this = this;
            var params = {
                cust_id: this.params.cust_id,
                page_no: this.page.page_no,
                page_size: this.page.page_size
            };
            this.ajax.postStream(
                "/order-web/api/customer/merge/getCustomerMergeList",
                params,
                function(response) {
                    var obj = response.body;
                    if (obj.result) {
                        _this.customerList = obj.content.list;
                        _this.count = obj.content.count;
                    }
                }
            );
        },
        delAddress() {
            if (!this.curAddressObj) {
                this.$message.error("请先选择需要删除的地址");
                return;
            }
            if (this.curAddressObj.fid) {
                this.$message.error("地址已保存，不可删除");
                return;
            }
            let i = this.addressList.length;
            while (i--) {
                if (
                    this.addressList[i].tempId &&
                    this.addressList[i].tempId === this.curAddressObj.tempId
                ) {
                    this.addressList.splice(i, 1);
                    break;
                }
            }
        },
        // 支付类型过虑
        payMethod(row) {
            switch (row.pay_method) {
                case "ON_LINE":
                    return "线上";
                    break;
                case "OFF_LINE":
                    return "线下";
                    break;
                default:
                    return row.pay_method;
                    break;
            }
        },
        // 状态过虑
        getStatus(row) {
            switch (row.status) {
                case "Y":
                    return "生效";
                    break;
                case "N":
                    return "失效";
                    break;
                default:
                    return row.status;
                    break;
            }
        },
        // 校验送货方式是否匹配,原始地址不匹配。
        checkDeliverMethod(obj) {
            if (obj.is_original) return;
            this.ajax.postStream(
                "/order-web/api/customer/receiverInfo/validateReceiveInfoDeliverMethod",
                {
                    deliver_method: obj.deliver_method,
                    receiver_state: obj.receiver_state,
                    receiver_city: obj.receiver_city,
				    receiver_street: obj.receiver_street,
                    receiver_district: obj.receiver_district
                },
                res => {
                    if (res.body.result) {
                        this.$message.error(res.body.msg || "");
                    }
                },
                err => {
                    this.$message.error(err);
                }
            );
        },
        invoiceSelectionChange(row) {
            // console.log(row)
            this.selectInvoice = row;
        },
        changeselect(row) {
            console.log(row);
        },
        /*addInvoice(){
			let self = this;

			let params = {
				callback(data){
					// console.log(data)
					self.invoiceList.push(data)
				},
			}
				self.$root.eventHandle.$emit('alert',{params:params,
				component:() => import('@components/customers/addInvoice.vue'),
				close:function(){},

				style:'width:1100px;height:250px',
				title:'新增发票'
			});
		},
		effectInvoice(row){
			// console.log(row)
			if(this.selectInvoice.code == ''){
				this.$message.error('请先保存');
				return false;
			}
			this.invoiceList.forEach((item,index) =>{
				if(item == this.selectInvoice){
					item.status = 'Y'
					this.$message.success('操作成功');
				}
			})
		},
		invalidInvoice(row){
			// console.log(row)
			if(this.selectInvoice.code == ''){
				this.$message.error('请先保存');
				return false;
			}
			this.invoiceList.forEach((item,index) =>{
				if(item == this.selectInvoice){
					item.status = 'N'
					this.$message.success('操作成功');
				}
			})
		},
		delInvoice(row){
			// console.log(row)
			console.log(this.selectInvoice)
			if(this.selectInvoice.code != ''){
				this.$message.error('已保存不能删除');
				return false;
			}
			this.invoiceList.forEach((item,index) =>{
				if(item == this.selectInvoice){
					this.invoiceList.splice(index,1);
					this.$message.success('操作成功');
				}
			})
		},*/
        select(selection) {
            //发生改变时触发
            return;
        },
        handleCurrentChange(val) {
            // if(!this.isAlert) return;
            console.log(val);
            this.selectInvoice = val;
        },
        companyChange() {
            this.submit.three_supplier_name = "";
            this.submit.three_supplier_code = "";
        },
        openCompany(liability_scope, row, liability_type) {
            //
            let otherParams = {
                    list_type: ["SP", "SA", "SB", "WL"]
                },
                params = {
                    otherParams: otherParams,
                    callback: d => {
                        // alert("sb");
                        // console.log(d);
                        this.submit.three_supplier_name = d.data.name;
                        this.submit. three_supplier_code = d.data.number;
                    },
                    liability_type: "BD_Supplier",
                    showAllPerson: true
                };

            this.$root.eventHandle.$emit("alert", {
                params: params,
                close: f => f,
                component: () => import("@components/duty/supplier"),
                style: "width:80%;height:80%",
                title: "选择服务商列表"
            });
        },
        getOption(){
            let self = this;
            let list = [];
            __AUX.get('deliver_method').forEach( item => {
                if (item.status) {
                    list.push({
                        value: item.code,
                        label:item.name
                    })
                }
            });
            this.customerCols.map(item => {
                if (item.prop == 'deliver_method') {
                    item.options = list
                }
            })
            self.deliverOption = list;

        }
    },
    created() {
        this.getOption();
    },
    mounted: function() {
        var _this = this;
        if (_this.params.cust_id) {
            _this.getCustomerDetail();
            _this.getPaymentInfo();
            this.addressPage.cust_id = this.params.cust_id
        }
        setTimeout(function() {
            // 页面初始化数据，用作对比，是否有更新
            for (var key in _this.submit) {
                _this.initialData[key] = _this.submit[key];
            }
        }, 500);
        _this.params.__close = _this.closeTab;
    }
};
</script>
<style scoped>
.el-select .el-input {
    width: 120px;
}
</style>

<style lang="stylus">
#customer_address_list {
    height: 100% !important;
}
#customer_address_list .body{
    height: calc(100% - 38px) !important;
}
</style>
