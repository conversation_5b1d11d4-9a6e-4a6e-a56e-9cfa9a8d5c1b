<!--客户列表-->
<template>
	<count-list 
		:data='customersList' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='count' 
		:searchPage='search.page_name' 
		:selection='selection' 
		@search-click='prSearch' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange' 
		@radio-change='radioChange' 
		@row-dblclick='rowDblclick'  
		ref='customersList'
		countUrl="/order-web/api/customer/listCount"
    	:showCount ='showCount'
	></count-list>
</template>
<script>
import countList from '@components/common/list-count'
export default {
	props:["params"],
	components: {
        countList,
      },
	data(){
		let self = this
		
		return {
			showCount:false,
			showSearch:false,
			search:{
				page_name: 'bd_customer',
				where: [],
				number:'',   //编码
				name:'',	//名称
				account:'',			//电商账号
				member_type:'',		  //客户类型
				effective_status:'',     //有效
				low_create_date:'',   //开始时间
				upper_create_date:'',   //结束时间
				page_size: self.pageSize,     //页数
				page_no:1,   //页码
			},
			customersList:[],
			actions_value:"",
			count:0,
			pageNow:1,
			multipleSelection: [],
			// 单选选中的行
			returnObj:"",
			selectId:"",
			searchName:'',

			selection: '',
			btns: [
				{
					type: 'success',
					txt: '刷新',
					click() {
						self.refresh()
					}
				}, {
					type: 'primary',
					txt: '新增',
					click() {
						self.addCustomer('新增客户',{})
					}
				}
			],
			cols: [
				{
					label: '客户编码',
					prop: 'number',
					redirectClick(row) {
						self.viewDetail(row.cust_id)
					}
				}, {
					label: '客户名称',
					prop: 'name'
				}, {
					label: '客户简称',
					prop: 'cust_short_name'
				}, {
					label: '失效状态',
					prop: 'effective_status',
					format: 'customerStatusFilter'
				}, {
					label: '类型',
					prop: 'member_type',
					format: 'auxFormat',
					formatParams: 'customerSource',
					// format: 'customerTypeFilter'
				}, {
					label: '创建人',
					prop: 'creator_name'
				}, {
					label: '创建时间',
					prop: 'create_date_str'
				}
			]
		}
	},
	methods:{
		preValid(api){
			var _this = this;
			var url = "/order-web/api/customer"+api;
			// 事件前验证
			if(_this.multipleSelection.length==0){
				_this.$alert('没有选择任何数据，请先选择数据！', '提示', {
					confirmButtonText: '确定'
				})
			}else{
				var custIdList = [];
				_this.multipleSelection.forEach(function(item,index,array){
					custIdList.push(item.cust_id);
				});

				this.ajax.postStream(url,custIdList,function(response){
					if(response.body.result){
						_this.searching();
						_this.$message({
									message: '操作成功',
										type: 'success'
								});
						// 重置业务操作
						_this.actions_value = "";
					}
				});
			}
		},
		// 新增
		addCustomer(name,params){
			this.$root.eventHandle.$emit('creatTab', {
				name:name, 
				params:params, 
				component:() => import('@components/customers/detail.vue')
			});
		},
		// 查看详情
		viewDetail(id){
			var params = {};
			if(this.params.isAlert)return;
			params.cust_id = id;
			this.addCustomer("客户详情",params);
		},
		openSearch(){
			this.showSearch = !this.showSearch
		},
		sizeChange(size){
			// 第页数改变
			this.search.page_size = size;
			this.initSearchData()
			this.searching();
		},
		pageChange(page_no){
			// 页数改变
			this.pageNow = page_no;
			this.search.page_no = page_no;
			this.initSearchData()
			this.searching();
		},
		preSearching(){
			this.searchName = this.search.name
			this.searching()
		},
		prSearch(list,resolve){
			this.search.where = list;
          new Promise((res,rej)=>{
            this.searching(resolve,res);
          }).then(()=>{
            if(this.search.page_no != 1){
              this.count = 0;
            }
            this.showCount = false;
          })
		},
		//列表显示
		searching(resolve,callback){
			
			var _this = this;
			if(_this.params.isAlert){
				_this.search.effective_status = 'B';
			}
			if(!!_this.params.ifResale){
				// console.log(_this.$refs.customersList.$refs.xptSearchEx)
				// _this.search.where.push({"field":"1347a11188b0a3c4799fcb0dac5b6d13","table":"7f550dcbc8a63998c36d4bb89e04656f","value":"RESALE","operator":"=","condition":"AND","listWhere":[]});
				_this.search.where.push({
					"field": "882de8b01970dca31aff1934efc82c8f",
					"table": "7f550dcbc8a63998c36d4bb89e04656f",
					"value": "Y",
					"operator": "=",
					"condition": "AND",
					"listWhere": []
				});
			}
			this.ajax.postStream('/order-web/api/customer/list2?permissionCode=CUSTOMER_QUERY',_this.search,function(response){
				if(response.body.result){
					_this.customersList = response.body.content.list;
					// _this.count = response.body.content.count;
					if(!_this.showCount){
						let total = _this.search.page_size * _this.search.page_no;
						_this.count = response.body.content.list.length == (_this.search.page_size+1)? total+1:total;
					}
				}
				else{
					_this.$message.error(response.body.msg)
				}
				resolve && resolve();
            	callback && callback();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
		refresh(){
			this.initSearchData()
			this.searching()
		},
		initSearchData(){
			if(!this.searchName) this.search.name = ''
			else this.search.name = this.searchName
		},
		radioChange(obj) {
			this.returnObj = obj
		},
		rowDblclick(obj) {
			if(this.params.isAlert) {
				this.params.close(obj)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
		}
	},
	mounted: function(){
		var _this = this;
		// 检测新增、编辑数据，刷新
		_this.$root.eventHandle.$on('close_addCustomer',function(){
			_this.searching();
		})

		// 弹窗打开此组件,新增销售订单时会调用
		if(_this.params.isAlert) {
			this.selection = 'radio'
			this.btns = [{
				type: 'primary',
				txt: '确认',
				click() {
					if(!_this.returnObj){
						this.$message.error("请选择数据");
						return;
					}
					_this.params.close(_this.returnObj);
					_this.$root.eventHandle.$emit('removeAlert',_this.params.alertId)
				}
			}]
		}
	},
	destroyed(){
		this.$root.offEvents('close_addCustomer');
	}
}
</script>