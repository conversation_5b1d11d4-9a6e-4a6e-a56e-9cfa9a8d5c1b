<template>
  <xpt-list
    :data="list"
    :btns="btns"
    :colData="cols"
    :pageTotal="pageTotal"
    selection="radio"
    @row-dblclick="rowDblclick"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    @radio-change="radioChange"
    searchHolder="请输入查询条件"
    searchPage=""
    @search-click="this.getList"
  />
</template>
<script>
export default {
  data() {
    let self = this;
    return {
      btns: [
        {
          type: "primary",
          txt: "确认",
          click: self.close,
        },
      ],
      url: "/custom-web/api/customMaterial/base/enum",
      cols: [
        {
          label: "编码",
          align: "center",
          prop: "number",
        },
        {
          label: "名称",
          align: "center",
          prop: "name",
        },
      ],
      search: {
        page_no: 1,
        page_size: self.pageSize,
      },
      list: [],
      selectRow: "",
      pageTotal: 0,
    };
  },
  props: ["params"],
  methods: {
    close() {
      let self = this;
      if (!self.selectRow) {
        self.$message.error("请选择一行数据");
        return;
      }
      self.params.callback(self.selectRow);
      self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
    },
    sizeChange(pageSize) {
      let self = this;
      self.search.page_size = pageSize;
      self.getList();
    },
    pageChange(page_no) {
      let self = this;
      self.search.page_no = page_no;
      self.getList();
    },
    getList(search) {
      let self = this;
      if (search) {
        this.search.key = search;
      }
      this.search.typeGroup = this.params.typeGroup
      this.ajax.postStream(this.url, this.search, function (response) {
        if (response.body.result) {
          self.list = response.body.content.list;
          self.pageTotal = response.body.content.count;
        }
      });
    },
    radioChange(obj) {
      this.selectRow = obj;
    },
    rowDblclick(obj) {
      this.selectRow = obj;
      this.close();
    },
  },
  mounted() {
    this.getList();
  },
};
</script>
  