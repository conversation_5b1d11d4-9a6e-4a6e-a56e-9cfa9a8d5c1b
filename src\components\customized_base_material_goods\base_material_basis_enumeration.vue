<!-- 基材基础枚举 -->
<template>
  <list-dynamic
    :data="list"
    :btns="btns"
    :colData="cols"
    :searchPage="search.page_name"
    :pageTotal="count"
    :orderNo="true"
    @page-size-change="pageSizeChange"
    @current-page-change="pageChange"
    @search-click="searchClick"
    @selection-change="selectionChange"
    :taggelClassName="taggelClassName"
    ref="xptList"
  ></list-dynamic>
</template>

<script>
import listDynamic from "./components/list-dynamic.vue";
export default {
  components: {
    listDynamic,
  },
  data() {
    return {
      btns: [
        {
          type: "primary",
          txt: "刷新",
          click: () => this.getList(),
        },
      ],
      cols: [
        {
          label: "编码",
          prop: "number",
          align: "center",
        },
        {
          label: "名称",
          align: "center",
          prop: "name",
        },
        {
          label: "类型分组",
          align: "center",
          prop: "typeGroupName",
        },
        {
          label: "创建人",
          align: "center",
          prop: "creatorName",
        },
        {
          label: "创建时间",
          align: "center",
          prop: "createTime",
          format: "dataFormat1",
        },
        {
          label: "最近编辑",
          align: "center",
          prop: "modifierName",
        },
        {
          label: "最近编辑时间",
          align: "center",
          prop: "modifyTime",
          format: "dataFormat1",
        },
      ],
      search: {
        page_no: 1,
        page_size: 50,
        page_name: "custom_material_base_list",
        where: [],
      },
      list: [],
      count: 0,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    pageSizeChange(ps) {
      this.search.page_size = ps;
      this.getList();
    },
    pageChange(page) {
      this.search.page_no = page;
      this.getList();
    },
    taggelClassName(row) {
      if (row.if_answer_message_read === "0") return this.$style["unread-tag"];
    },
    searchClick(obj, resolve) {
      let self = this;
      this.search.where = obj;
      new Promise((res, rej) => {
        self.getList(resolve, res);
      }).then(() => {
        if (self.search.page_no != 1) {
          self.count = 0;
        }
        self.search.page_no = 1;
      });
    },
    selectionChange(obj) {
      this.selectData = obj;
    },
    getList(resolve, callback) {
      this.ajax.postStream(
        "/custom-web/api/customMaterial/base/list",
        this.search,
        (res) => {
          let { content, result, msg } = res.body;
          if (result) {
            this.list = content.list || [];
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          callback && callback();
          resolve && resolve();
        },
        (err) => {
          this.$message.error(err);
          callback && callback();
          resolve && resolve();
        }
      );
    },
  },
};
</script>