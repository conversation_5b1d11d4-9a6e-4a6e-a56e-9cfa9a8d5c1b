<!-- 基材商品信息详情-->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button
          :type="btn.type"
          size="mini"
          v-for="(btn, index) in topBtns"
          :key="'good' + index"
          :loading="btn.loading || false"
          @click="btn.click"
          >{{ btn.txt }}</el-button
        >
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-tabs v-model="firstTab" style="height: 200px">
        <el-tab-pane label="基本信息" name="appointment">
          <el-form
            label-position="right"
            class="mgt10"
            label-width="100px"
            :model="form"
            ref="form"
          >
            <el-col :span="8">
              <el-form-item label="基材商品编码：" prop="materialNumber">
                <el-input
                  readonly
                  size="mini"
                  v-model="form.materialNumber"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="规格：" prop="specificationName">
                <el-input
                  readonly
                  size="mini"
                  v-model="form.specificationName"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="最新修改人：" prop="modifierName">
                <el-input
                  readonly
                  size="mini"
                  v-model="form.modifierName"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="最新修改时间：" prop="modifyTime">
                <el-date-picker
                  v-model="form.modifyTime"
                  type="datetime"
                  placeholder="选择日期"
                  size="mini"
                  disabled
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="基材类型：" prop="materialTypeName">
                <el-input
                  readonly
                  size="mini"
                  v-model="form.materialTypeName"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="规格描述：" prop="specificationDesc">
                <el-input
                  readonly
                  size="mini"
                  v-model="form.specificationDesc"
                  disabled
                ></el-input>
              </el-form-item>

              <el-form-item label="创建人：" prop="creatorName">
                <el-input
                  v-model="form.creatorName"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="花色：" prop="designColorName">
                <el-input
                  readonly
                  size="mini"
                  v-model="form.designColorName"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="交期天数：" prop="deliveryDays">
                <el-input
                  readonly
                  size="mini"
                  v-model="form.deliveryDays"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="创建时间：" prop="createTime">
                <el-date-picker
                  v-model="form.createTime"
                  type="date"
                  placeholder="选择日期"
                  size="mini"
                  :disabled="true"
                  :editable="false"
                ></el-date-picker>
              </el-form-item>

            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class="xpt-flex__bottom quotation-list-head-margin">
      <el-tabs v-model="secondTab">
        <el-tab-pane
          label="签价明细"
          name="signForPriceDetails"
          class="xpt-flex xpt-flex__bottom"
        >
          <list-dynamic
            ref="signForPriceDetails"
            :colData="cols"
            :data="list"
            :pageTotal="count"
            :searchPage="search.page_name"
            selection=""
            :showHead="false"
            @search-click="searchClick"
            @page-size-change="itemPageSizeChange"
            @current-page-change="itemPageChange"
          ></list-dynamic>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>
  <script>
import signForPriceDetails from "./mixins/sign_for_price_details.js";
import listDynamic from "./components/list-dynamic.vue";
export default {
  components: {
    listDynamic,
  },
  mixins: [signForPriceDetails],
  props: ["params"], //上游参数
  data() {
    var self = this; //本vue
    return {
      firstTab: "appointment",
      secondTab: "signForPriceDetails",

      form: {
        materialNumber: "",
        specificationDesc: "",
        // materialName: "",
        designColorName: "",
        modifierName: "",
        creatorName: "",
        createTime: "",
        specificationName: "",
        modifyTime: "",
        materialTypeName: "",
      }, //表单内容

      topBtns: [
        {
          type: "primary",
          txt: "刷新",
          click() {
            self.getDetail();
            self.getList();
          },
        },
      ],
    };
  },
  methods: {
    // 获取价目明细
    getList(resolve) {
      let url = "/custom-web/api/customMaterial/quotation/list";
      let data = {
        ...this.search,
        customMaterialId: this.params.materialId,
      };
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { content, result, msg } = res.body;
          if (result && content) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve();
        }
      );
    },
    // 获取详情
    getDetail() {
      let self = this;
      this.ajax.get(
        "/custom-web/api/customMaterial/detail?customMaterialId=" +
          this.params.materialId,
        (res) => {
          let { result, content, msg } = res.body;
          if (result) {
            this.form = content;
          } else {
            self.$message.error(msg);
          }
        },
        (e) => {
          self.$message.error(e);
        }
      );
    },
  },
  mounted: function () {
    this.getDetail();
    this.getList();
  },
  created() {},
  watch: {},
  computed: {},
};
</script>
<style>
.quotation-list-head-margin .list-header {
  display: flex;
}
</style>
