<!-- 基材商品信息列表 -->
<template>
  <list-dynamic
    ref="listDynamic"
    :data="list"
    :btns="btns"
    :colData="cols"
    :orderNo="true"
    :pageTotal="count"
    :searchPage="search.page_name"
    @search-click="searchClick"
    selection=""
    @page-size-change="listPageSizeChange"
    @current-page-change="listPageChange"
  ></list-dynamic>
</template>
  <script>
import listDynamic from "./components/list-dynamic.vue";
export default {
  components: {
    listDynamic,
  },
  data() {
    let self = this;
    return {
      list: [],
      btns: [
        {
          type: "info",
          txt: "导出",
          click: self.exportExcel,
          loading: false,
        },
        {
          type: "warning",
          txt: "导出结果",
          click: self.exportResult,
          loading: false,
        },
        {
          type: "success",
          txt: "刷新",
          disabled: () => {
            return false;
          },
          click() {
            self.refresh();
          },
        },
      ],
      cols: [
        {
          label: "基材商品编码",
          align: "center",
          prop: "materialNumber",
          redirectClick(row) {
            let params = {
              materialId: row.materialId,
            };
            self.$root.eventHandle.$emit("creatTab", {
              name: "基材商品信息详情",
              params,
              component: () =>
                import(
                  "@components/customized_base_material_goods/base_material_goods_info_details.vue"
                ),
            });
          },
        },
        {
          label: "基材类型",
          align: "center",
          prop: "materialTypeName",
        },
        {
          label: "花色",
          align: "center",
          prop: "designColorName",
        },
        {
          label: "计价单位",
          align: "center",
          prop: "chargeUnitName",
        },
        {
          label: "规格",
          align: "center",
          prop: "specificationName",
        },
        {
          label: "规格描述",
          align: "center",
          prop: "specificationDesc",
        },
        {
          label: "交期天数",
          align: "center",
          prop: "deliveryDays",
        },
        {
          label: "标准售价",
          align: "center",
          prop: "standPrice",
        },
        {
          label: "最新签价",
          align: "center",
          prop: "thisQuotationPrice",
        },
        {
          label: "最新签价时间",
          align: "center",
          prop: "thisQuotationTime",
          format: "dataFormat1",
        },
        {
          label: "最新签价生效时间",
          align: "center",
          prop: "thisQuotationEnableTime",
          format: "dataFormat1",
        },
        {
          label: "最新签价失效时间",
          align: "center",
          prop: "thisQuotationDisableTime",
          format: "dataFormat1",
        },
        {
          label: "上次签价",
          align: "center",
          prop: "lastQuotationPrice",
        },
        {
          label: "上次签价时间",
          align: "center",
          prop: "lastQuotationTime",
          format: "dataFormat1",
        },
      ],

      count: 0,
      search: {
        page_name: "custom_material_list",
        where: [],
        page_size: 50,
        page_no: 1,
      },
      baseMaterialValueGroup: [],
    };
  },
  provide() {
    return {
      baseMaterialXptInput: this.handleXptInputOnIconClick,
      clearBaseMaterialXptInputValue: this.clearBaseMaterialXptInputValue,
    };
  },
  methods: {
    //刷新
    refresh() {
      this.getList();
    },

    // 获取基材商品信息
    getList(resolve) {
      let url = "/custom-web/api/customMaterial/list";
      this.ajax.postStream(
        url,
        this.search,
        (res) => {
          let { content, result, msg } = res.body;
          if (result && content) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve();
        }
      );
    },
    //通用查询搜索
    searchClick(obj, resolve) {
      let self = this;
      let where = JSON.parse(JSON.stringify(obj));
      if (!!this.baseMaterialValueGroup.length) {
        where.forEach((item) => {
          let i = this.baseMaterialValueGroup.findIndex(
            (row) => row.name === item.value
          );
          if (i !== -1) {
            item.value = this.baseMaterialValueGroup[i].number;
          }
        });
      }
      this.search.where = where;
      new Promise((res, rej) => {
        self.getList(resolve, res);
      }).then(() => {
        if (self.search.page_no != 1) {
          self.count = 0;
        }
        self.search.page_no = 1;
      });
    },
    // 当前页改变
    listPageSizeChange(ps) {
      this.search.page_size = ps;
      this.getList();
    },
    // 当前页面显示行数改变
    listPageChange(page) {
      this.search.page_no = page;
      this.getList();
    },
    //导出
    exportExcel() {
      this.btns[1].loading = true;
      this.ajax.postStream(
        "/custom-web/api/customMaterial/export?permissionCode=CUSTOM_MATERIAL_LIST_EXPORT",
        this.search,
        (res) => {
          if (res.body.result) {
            res.body.msg && this.$message.success(res.body.msg);
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
          this.btns[1].loading = false;
        },
        (err) => {
          this.btns[1].loading = false;
          this.$message.error(err);
        }
      );
    },
    exportResult() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "基材商品信息导出列表",
        params: {
          query: {
            type: "EXCEL_TYPE_CUSTOM_MATERIAL_EXPORT",
          },
        },
      });
    },

    // 筛选组件，弹窗选择数据
    handleXptInputOnIconClick(res, callback) {
      let self = this;
      let typeGroup = [
        {
          id: "1dfe5d1f8f360726fad8a2a32ee7f212",
          name: "基材类型",
          type: 0,
        },
        {
          id: "b66530dd43e4fc719ad9f935b6752cfc",
          name: "花色",
          type: 1,
        },
        {
          id: "9a2f1d17c0952cdff676d200108482e6",
          name: "规格",
          type: 2,
        },
      ];
      let i = typeGroup.findIndex((item) => item.id === res.field);
      if (i === -1) {
        this.$message.error("差找不到对应类型");
        return;
      }
      let params = {
        typeGroup: typeGroup[i].type,
        callback: function (e) {
          let { number, name } = e;
          let type = typeGroup[i].type;
          self.baseMaterialValueGroup.push({
            number,
            name,
            type,
          });
          callback && callback(name, type);
        },
      };
      this.$root.eventHandle.$emit("alert", {
        params,
        component: () =>
          import("@components/customized_base_material_goods/baseEnumDialog"),
        style: "width:800px;height:500px",
        title: "选择基材基础信息",
      });
    },
    clearBaseMaterialXptInputValue(type) {
      let i = this.baseMaterialValueGroup.findIndex(
        (item) => item.type === type
      );
      if (i === -1) return;
      this.baseMaterialValueGroup.splice(i, 1);
    },
  },
  computed: {},
  watch: {},
  mounted() {
    this.getList();
  },
};
</script>
