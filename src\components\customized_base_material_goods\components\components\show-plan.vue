<template>
  <div id="searchplan">
    <el-dialog
      v-if="dialogVisible"
      custom-class="dialog-center"
      size="small"
      :show-close="false"
      title="显示方案设置"
      :visible.sync="dialogVisible">
      <div class="content">
        <div style="margin-bottom: 5px;">
          <el-input v-model.trim="planName" placeholder="请输入方案名称" :maxlength="20" size="small" style="width: 100%" />
        </div>
        <div class="left">
          <el-transfer
            v-model="showColData"
            :data="allColData"
            :render-content="renderItem"
            :props="{
              key: 'prop',
              label: 'label'
            }"
            :titles="['所有列', '显示列（拖拽排序）']"
          ></el-transfer>
        </div>
        <!-- 用于拖拽的组件，样式覆盖在穿梭框上面，使其拖动时像拖动穿梭框      -->
        <ul ref="drag" class="right">
          <li
            v-for="item of allColData.filter((i) => {return showColData.indexOf(i.prop) > -1})"
            :key="item.prop"
            v-dragging="{ item: item, list: allColData, group: 'prop' }"
          ></li>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-checkbox class="pull-left" v-model="isDefault">默认方案</el-checkbox>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import fn from '@common/Fn.js'
  export default {
    name: "show-plan",
    props: {
      searchPage: String
    },
    data() {
      return {
        planName: '',
        isDefault: false,
        dialogVisible: false,
        allColData: [],
        showColData: [],
        oldData: {}, // 修改时当前方案数据
      }
    },
    computed: {
      // 排序后的显示数组
      sortedShowColData: function () {
        return this.allColData.filter((i) => {
          return this.showColData.indexOf(i.prop) > -1
        })
      },
      // 排序后的显示数组prop
      sortedShowColDataProp: function () {
        return this.sortedShowColData.map(item => item.prop)
      }
    },
    mounted() {
    },
    methods: {
      renderItem(h, option) {
        const i = this.sortedShowColDataProp.indexOf(option.prop)
        return h('span', `${i < 0 ? '' : i + 1 + '. '}${option.label}`)
      },
      show(data) {
        this.oldData = data || {}
        this.showColData = this.oldData.id ? this.oldData.query_json.data.map(item => item.prop) : []
        this.getAllCol()
        this.planName = this.oldData.id ? this.oldData.query_json.planName : ''
        this.isDefault = this.oldData.id_default
        this.dialogVisible = true

        this.$nextTick(() => {
          // 因为拖拽组件是无样式覆盖在已选列表上的，所以需要同步拖拽组件跟已选列表的滚动条
          const list = document.getElementsByClassName('el-transfer-panel__list')[1]
          this.$refs.drag.addEventListener('scroll', (e) => {
            list.scrollTop = e.target.scrollTop
          })
        })
      },
      confirm() {
        if(!this.planName) {
          this.$message.error('请输入方案名称')
          return
        }

        if(this.sortedShowColData.length < 1) {
          this.$message.error('请选择显示列数据')
          return
        }

        const data = {
          id: this.oldData.id,
          user_id: fn.getUserInfo('id'),
          page_code: this.searchPage,
          // 定义显示方案的内容。后端不解析，前端给一份json
          query_json: JSON.stringify(
            {
              planName: this.planName,
              data: this.sortedShowColData
            }
          ),
          type: 'show',
          id_default: !!this.isDefault
        }

        this.ajax.postStream('/user-web/api/new/saveOrUpdateQueryPlan', data, res => {
          if(!res.body.result) {
            this.$message.error(res.body.msg || '服务器错误！')
            return
          }
          this.$emit('save', res.body.content)
          this.$message.success('保存成功！')
          this.dialogVisible = false
        }, err => {
          this.$message.error(err);
        });
      },
      getAllCol() {
        this.ajax.postStream('/user-web/api/new/showAllFiled', { page_code: this.searchPage }, res => {
          if(!res.body.result) {
            this.$message.error(res.msg)
            return
          }
          this.allColData = res.body.content.data
          // 因为排序是排序所有字段数组，新的所有列数组跟方案里的数组排序必须一致才能正常显示
          const showArr = this.showColData.map(prop => {
            let value = {}
            res.body.content.data.forEach(item => {
              if(item.prop === prop) {
                value = item
              }
            })
            return value
          })
          const allArr = res.body.content.data.filter(item => {
            return this.showColData.indexOf(item.prop) < 0
          })
          this.allColData = [...showArr, ...allArr]
        }, err => {
          this.$message.error(err);
        });
      }
    }
  }
</script>

<style>
  .pull-left {
    float: left;
  }
</style>

