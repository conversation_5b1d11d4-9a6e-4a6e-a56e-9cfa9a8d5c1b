<!-- 价格发布记录列表 -->
<template>
  <list-dynamic
    :data="list"
    :btns="btns"
    :colData="cols"
    :pageTotal="count"
    :searchPage="search.page_name"
    @search-click="searchClick"
    :selection="selection"
    @page-size-change="listPageSizeChange"
    @radio-change="listRadioChange"
    @current-page-change="listPageChange"
  >
  </list-dynamic>
</template>
<script>
import listDynamic from "./components/list-dynamic.vue";
export default {
  components: {
    listDynamic,
  },
  data() {
    let self = this;
    return {
      list: [],
      btns: [
        {
          type: "primary",
          txt: "价格发布",
          disabled: () => {
            return false;
          },
          click() {
            self.listAdd();
          },
        },
        {
          type: "info",
          txt: "导出",
          click: self.exportExcel,
          loading: false,
        },
        {
          type: "warning",
          txt: "导出结果",
          click: self.exportResult,
          loading: false,
        },
        {
          type: "success",
          txt: "刷新",
          disabled: () => {
            return false;
          },
          click() {
            self.refresh();
          },
        },
      ],
      cols: [
        {
          label: "价格发布版本号",
          align: "center",
          prop: "releaseNo",
          redirectClick(row) {
            self.redirectClick(row);
          },
        },
        {
          label: "价格发布版本名称",
          align: "center",
          prop: "releaseName",
        },
        {
          label: "价目表",
          align: "center",
          prop: "priceListName",
        },
        {
          label: "价格类型",
          align: "center",
          prop: "priceListType",
        },
        {
          label: "审核状态",
          align: "center",
          prop: "releaseStatusStr",
        },
        {
          label: "生效时间",
          align: "center",
          prop: "enableTime",
          format: "dataFormat1",
        },
        {
          label: "创建人",
          align: "center",
          prop: "creatorName",
        },
        {
          label: "创建时间",
          align: "center",
          prop: "createTime",
          format: "dataFormat1",
        },
        {
          label: "初审人",
          align: "center",
          prop: "firstCheckUserName",
        },
        {
          label: "初审时间",
          align: "center",
          prop: "firstCheckTime",
          format: "dataFormat1",
        },
        {
          label: "复审人",
          align: "center",
          prop: "recheckUserName",
        },
        {
          label: "复审时间",
          align: "center",
          prop: "recheckTime",
          format: "dataFormat1",
        },
        {
          label: "备注",
          align: "center",
          prop: "remark",
        },
      ],

      listSelect: null,
      count: 0,
      search: {
        page_name: "custom_price_release_list",
        where: [],
        page_size: 50,
        page_no: 1,
      },
      selection: "radio",
    };
  },
  provide() {
    return {
      getFieldsValues: this.getFieldsValues,
    };
  },
  methods: {
    getFieldsValues(callback) {
      let list = __AUX
        .getValidData("custom_price_type")
        .map(({ name, code }) => {
          return {
            code,
            name,
          };
        });
      callback("bc1b836022e1974955c6ce27056b45f9", list);
    },
    // 跳转详情
    redirectClick(row) {
      let self = this;
      let params = {
        releaseId: row.releaseId,
      };
      self.$root.eventHandle.$emit("creatTab", {
        name: "价目发布记录表详情",
        params,
        component: () =>
          import(
            "@components/customized_base_material_goods/price_dissemination.vue"
          ),
      });
    },
    //刷新
    refresh() {
      this.getList();
    },
    // 行选中
    listRadioChange(row) {
      this.listSelect = row;
    },
    // 获取价目发布列表信息
    getList(resolve) {
      let url = "/custom-web/api/customPriceRelease/list";
      this.ajax.postStream(
        url,
        this.search,
        (res) => {
          let { result, content, msg } = res.body;
          if (result) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve();
        }
      );
    },
    //通用查询搜索
    searchClick(where, reslove) {
      this.search.where = where;
      this.getList(reslove);
    },
    // 当前页改变
    listPageSizeChange(ps) {
      this.search.page_size = ps;
      this.getList();
    },
    // 当前页面显示行数改变
    listPageChange(page) {
      this.search.page_no = page;
      this.getList();
    },
    // 新增
    listAdd() {
      let principal_user_id = this.getEmployeeInfo("id");
      let url = "/permission-web/api/businessPermission/check";
      let data = {
        principal_user_id,
        business_type_code: "CUSTOM_PRICE_RELEASE_SAVE",
      };
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { result, msg } = res.body;
          if (result) {
            this.$root.eventHandle.$emit("creatTab", {
              name: "价格发布",
              component: () =>
                import(
                  "@components/customized_base_material_goods/price_dissemination.vue"
                ),
            });
          } else {
            this.$message.error(msg);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },
    //导出
    exportExcel() {
      if (!this.listSelect) {
        this.$message.error("请选择需要导出的数据");
        return;
      }
      this.btns[1].loading = true;
      let data = {
        releaseId: this.listSelect.releaseId,
      };
      this.ajax.postStream(
        "/custom-web/api/customPriceRelease/export?permissionCode=CUSTOM_PRICE_RELEASE_EXPORT",
        data,
        (res) => {
          if (res.body.result) {
            res.body.msg && this.$message.success(res.body.msg);
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
          this.btns[1].loading = false;
        },
        (err) => {
          this.btns[1].loading = false;
          this.$message.error(err);
        }
      );
    },
    exportResult() {
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/after_sales_report/export"),
        style: "width:900px;height:600px",
        title: "价格发布记录导出列表",
        params: {
          query: {
            type: "EXCEL_TYPE_PRICE_RELEASE_EXPORT",
          },
        },
      });
    },
  },
  computed: {},
  watch: {},
  mounted() {
    this.getList();
  },
};
</script>
<style type="text/css">
.el-dialog__wrapper {
  background-color: transparent;
}

.xiudounb {
  color: red;
}
</style>
