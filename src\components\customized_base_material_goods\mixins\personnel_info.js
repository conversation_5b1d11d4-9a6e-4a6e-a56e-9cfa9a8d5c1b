export default {
  data() {
    var self = this;
    return {
      btns: [
        {
          type: "primary",
          txt: "添加人员",
          disabled: () => {
            return false;
          },
          click() {
            self.listAdd();
          },
        },
        {
          type: "danger",
          txt: "删行",
          disabled: () => {
            return false;
          },
          click() {
            self.listDel();
          },
        },
      ],
      cols: [
        {
          label: "人员类型",
          align: "center",
          prop: "managerTypeName",
        },
        {
          label: "人员工号",
          align: "center",
          prop: "managerEmployeeNumber",
        },
        {
          label: "人员名称",
          align: "center",
          prop: "managerUserName",
        },
      ],
      list: [],
      copyList: [],
      selectionList: [],
      count: 0,
      search: {
        page_no: 1,
        page_size: 50,
        page_name: "custom_material_base_list",
        where: [],
      },

      visible: false,
      dialogForm: {
        managerUserId: "",
        managerType: "",
        managerTypeName: "",
        managerEmployeeNumber: "",
        managerUserName: "",
      },
      managerTypeOptions: [
        {
          label: "初审人员",
          value: "FIRST_CHECK_USER",
        },
        {
          label: "复审人员",
          value: "RECHECK_USER",
        },
      ],
    };
  },
  methods: {
    listCheckboxChange(row, list, s) {
      this.selectionList = row;
    },
    listAdd() {
      this.open();
    },
    listDel() {
      if (!this.selectionList.length) {
        this.$message.warning("请选择数据");
        return;
      }
      for (let index = 0; index < this.selectionList.length; index++) {
        let { managerUserId, managerType } = this.selectionList[index];
        let idIndex = this.list.findIndex((item) => {
          return (
            item.managerUserId === managerUserId &&
            item.managerType === managerType
          );
        });
        if (idIndex === -1) {
          this.$message.error("查找不到数据");
          return;
        }
        this.list.splice(idIndex, 1);
      }
      this.selectionList = [];
      this.$message.success("人员已删除，保存之后生效");
    },

    open() {
      this.visible = true;
    },
    handleClose() {
      this.dialogForm = {
        managerUserId: "",
        managerType: "",
        managerTypeName: "",
        managerEmployeeNumber: "",
        managerUserName: "",
      };
      this.visible = false;
    },
    handleSuccess() {
      let { managerUserId, managerType } = this.dialogForm;
      if (!managerUserId || !managerType) {
        this.$message.warning("数据未填写");
        return;
      }
      if (this.isUserExist()) return;
      this.list.unshift(this.dialogForm);
      this.$message.success("人员已添加，保存之后生效");
      this.handleClose();
    },
    isUserExist() {
      let { managerUserId, managerType } = this.dialogForm;
      if (
        this.list.some((item) => {
          return (
            item.managerUserId === managerUserId &&
            item.managerType === managerType
          );
        })
      ) {
        this.$message.warning("数据已存在，无需添加");
        return true;
      }
      let i = this.managerTypeOptions.findIndex(
        (item) => item.value === managerType
      );
      this.dialogForm.managerTypeName = this.managerTypeOptions[i].label;
    },

    handleManagerEmployeeNumberOnIconClick() {
      let self = this;
      let params = {};
      params.callback = (e) => {
        let { employeeNumber, fullName, id } = e;
        self.dialogForm.managerEmployeeNumber = employeeNumber;
        self.dialogForm.managerUserName = fullName;
        self.dialogForm.managerUserId = id;
      };
      this.$root.eventHandle.$emit("alert", {
        params,
        component: () =>
          import(
            "@components/customized_base_material_goods/personnel_list_dialog"
          ),
        style: "width:800px;height:500px",
        title: "选择人员",
      });
    },
    handleManagerEmployeeNumberChange(e) {
      if (typeof e === "undefined") {
        this.dialogForm.managerEmployeeNumber = "";
        this.dialogForm.managerUserName = "";
        this.dialogForm.managerUserId = "";
      }
    },
    handleManagerEmployeeNumberBlur() {
      if (!this.dialogForm.managerEmployeeNumber) return;
      let url = "/user-web/api/userLogin/getUserLoginList";
      let data = {
        key: this.dialogForm.managerEmployeeNumber,
        page_name: "cloud_user_person",
        where: [],
        isEnable: 1,
        type: "EMPLOYEE",
      };
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { result, msg, content } = res.body;
          if (result) {
            let { employeeNumber, fullName, id } = content.list[0];
            this.dialogForm.managerEmployeeNumber = employeeNumber;
            this.dialogForm.managerUserName = fullName;
            this.dialogForm.managerUserId = id;
          } else {
            this.$message.error(msg);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },
  },
};
