export default {
  data() {
    var self = this;
    return {
      priceDetailList: [],
      copyPriceDetailList: [],
      selectionList: [],
      uploadUrl:
        "/custom-web/api/customPriceReleaseDetail/import?permissionCode=CUSTOM_PRICE_RELEASE_IMPORT",
      otherParams: {
        releaseId: "",
      },
      priceDetailBtns: [
        {
          type: "success",
          txt: "添加商品",
          click: self.handleAddGoods,
          disabled: () => {
            return self.handleDisabledStatus("add");
          },
        },
        {
          type: "danger",
          txt: "删除",
          click: self.handleDelGoods,
          disabled: () => {
            return self.handleDisabledStatus("del");
          },
        },
        {
          type: "info",
          txt: "失效",
          click: self.handleDisableGoods,
          disabled: () => {
            return self.handleDisabledStatus("disable");
          },
        },
        {
          type: "success",
          txt: "查看导入结果",
          click: self.showUploadResult,
          disabled: () => {
            return self.handleDisabledStatus("import");
          },
        },
      ],
      priceDetailCols: [
        {
          label: "基材商品编码",
          align: "center",
          prop: "customMaterialNumber",
        },
        {
          label: "基材类型",
          align: "center",
          prop: "materialTypeName",
        },
        {
          label: "花色名称",
          align: "center",
          prop: "designColorName",
        },
        {
          label: "规格",
          align: "center",
          prop: "specificationName",
        },
        {
          label: "签价",
          align: "center",
          prop: "quotationPrice",
        },
        {
          label: "价格",
          align: "center",
          prop: "price",
          slot: "price",
        },
        {
          label: "行状态",
          align: "center",
          prop: "enableStatusStr",
        },
        {
          label: "生效时间",
          align: "center",
          prop: "enableTime",
          format: "dataFormat1",
        },
        {
          label: "失效人",
          align: "center",
          prop: "disableUserName",
        },
        {
          label: "失效时间",
          align: "center",
          prop: "disableTime",
          format: "dataFormat1",
        },
      ],
      priceDetailCount: 0,
      priceDetailSearch: {
        where: [],
        page_name: "custom_price_release_detail_list",
        page_size: 50,
        page_no: 1,
      },
      baseMaterialValueGroup: [],
    };
  },
  mounted() {},
  provide() {
    return {
      baseMaterialXptInput: this.handleXptInputOnIconClick,
      clearBaseMaterialXptInputValue: this.clearBaseMaterialXptInputValue,
    };
  },
  methods: {
    getPriceDetailList(resolve) {
      let url = "/custom-web/api/customPriceReleaseDetail/list";
      let data = {
        ...this.priceDetailSearch,
        releaseId: this.params.releaseId,
      };
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { content, result, msg } = res.body;
          if (result && content) {
            this.priceDetailList = content.list;
            this.copyPriceDetailList = JSON.parse(JSON.stringify(content.list));
            this.priceDetailCount = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve();
        }
      );
    },
    priceDetailSearchClick(obj, resolve) {
      let self = this;
      let where = JSON.parse(JSON.stringify(obj));
      if (!!this.baseMaterialValueGroup.length) {
        where.forEach((item) => {
          let i = this.baseMaterialValueGroup.findIndex(
            (row) => row.name === item.value
          );
          if (i !== -1) {
            item.value = this.baseMaterialValueGroup[i].number;
          }
        });
      }
      this.priceDetailSearch.where = where;
      new Promise((res, rej) => {
        self.getPriceDetailList(resolve, res);
      }).then(() => {
        if (self.priceDetailSearch.page_no != 1) {
          self.priceDetailCount = 0;
        }
        self.priceDetailSearch.page_no = 1;
      });
    },
    priceDetailPageSizeChange(size, resolve) {
      this.priceDetailSearch.page_size = size;
      this.getPriceDetailList(resolve);
    },
    priceDetailPageChange(page_no, resolve) {
      this.priceDetailSearch.page_no = page_no;
      this.getPriceDetailList(resolve);
    },
    handleAddGoods() {
      let self = this;
      let params = {};
      params.callback = (e) => {
        let list = e.filter((item) => {
          return !self.priceDetailList.some(
            (row) => row.customMaterialNumber === item.materialNumber
          );
        });
        if (!list.length) {
          this.$message.warning("商品已存在");
          return;
        }
        self.priceDetailList.unshift(...self.getFieldNameSyncData(list));
        this.$message.success("商品已添加，保存之后生效");
      };
      this.$root.eventHandle.$emit("alert", {
        params,
        component: () =>
          import(
            "@components/customized_base_material_goods/select_base_material_goods_dialog"
          ),
        style: "width:800px;height:500px",
        title: "选择基材",
      });
    },
    getFieldNameSyncData(e) {
      return e.map((item) => {
        let {
          materialNumber,
          designColorName,
          specificationName,
          thisQuotationPrice,
          materialId,
          materialTypeName,
        } = item;
        return {
          customMaterialId: materialId,
          customMaterialNumber: materialNumber,
          materialTypeName,
          designColorName,
          specificationName,
          quotationPrice: thisQuotationPrice,
          price: "",
          enableStatusStr: "失效",
          enableTime: "",
          disableUserName: "",
          disableTime: "",
        };
      });
    },
    priceDetailSelectionChange(e) {
      this.selectionList = e;
    },
    handleDelGoods() {
      if (!this.selectionList.length) {
        this.$message.warning("请先选择数据");
        return;
      }
      for (let index = 0; index < this.selectionList.length; index++) {
        let customMaterialId = this.selectionList[index].customMaterialId;
        let idIndex = this.priceDetailList.findIndex(
          (item) => item.customMaterialId === customMaterialId
        );
        if (idIndex === -1) {
          this.$message.error("查找不到数据");
          return;
        }
        this.priceDetailList.splice(idIndex, 1);
      }
      this.selectionList = [];
      this.$message.success("商品已删除，保存之后生效");
    },
    handleDisableGoods() {
      if (!this.selectionList.length) {
        this.$message.warning("请先选择数据");
        return;
      }
      for (let index = 0; index < this.selectionList.length; index++) {
        let customMaterialId = this.selectionList[index].customMaterialId;
        let idIndex = this.priceDetailList.findIndex(
          (item) => item.customMaterialId === customMaterialId
        );
        if (idIndex === -1) {
          this.$message.error("查找不到数据");
          return;
        }
        this.priceDetailList[idIndex].enableStatusStr = "失效";
        this.priceDetailList[idIndex].enableStatus = 0;
      }
      this.selectionList = [];
      this.$message.success("选择的商品生效状态已设为失效，保存之后生效");
    },

    handlePriceChange(e) {
      let self = this;
      let reg = /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/;
      if (!reg.test(e) && !!e && e != 0) {
        self.$message.error("当前输入不符合规范");
        return null;
      } else {
        let nums = e.split(".");
        if (nums.length === 1 || nums[1].length <= 2) return Number(e);
        let num = nums[1].slice(0, 2);
        self.$message.warning("金额不能大于两位小数，已自动截取");
        return `${Number(nums[0])}.${num}`;
      }
    },

    showUploadResult() {
      this.$root.eventHandle.$emit("alert", {
        style: "width:900px;height:600px",
        title: "导入结果",
        params: {
          url: "/reports-web/api/reports/afterSale/findMyReportList",
          data: {
            type: "EXCEL_TYPE_PRICE_RELEASE_DETAIL_IMPORT",
          },
          showDownload: true,
        },
        component: () => import("@components/common/eximport"),
      });
    },

    // 筛选组件，弹窗选择数据
    handleXptInputOnIconClick(res, callback) {
      let self = this;
      let typeGroup = [
        {
          id: "1dfe5d1f8f360726fad8a2a32ee7f212",
          name: "基材类型",
          type: 0,
        },
        {
          id: "b66530dd43e4fc719ad9f935b6752cfc",
          name: "花色",
          type: 1,
        },
        {
          id: "9a2f1d17c0952cdff676d200108482e6",
          name: "规格",
          type: 2,
        },
      ];
      let i = typeGroup.findIndex((item) => item.id === res.field);
      if (i === -1) {
        this.$message.error("差找不到对应类型");
        return;
      }
      let params = {
        typeGroup: typeGroup[i].type,
        callback: function (e) {
          let { number, name } = e;
          let type = typeGroup[i].type;
          self.baseMaterialValueGroup.push({
            number,
            name,
            type,
          });
          callback && callback(name, type);
        },
      };
      this.$root.eventHandle.$emit("alert", {
        params,
        component: () =>
          import("@components/customized_base_material_goods/baseEnumDialog"),
        style: "width:800px;height:500px",
        title: "选择基材基础信息",
      });
    },
    clearBaseMaterialXptInputValue(type) {
      let i = this.baseMaterialValueGroup.findIndex(
        (item) => item.type === type
      );
      if (i === -1) return;
      this.baseMaterialValueGroup.splice(i, 1);
    },
  },
};
