export default {
  data() {
    return {
      list: [],
      cols: [
        {
          label: "签价单编码",
          align: "center",
          prop: "quotationNo",
        },
        {
          label: "签价",
          align: "center",
          prop: "quotationPrice",
        },
        {
          label: "是否含运费",
          align: "center",
          prop: "freightIncluded",
          formatter(val) {
            return val ? "是" : "否";
          },
        },
        {
          label: "计价单位",
          align: "center",
          prop: "chargeUnitName",
        },
        {
          label: "生效时间",
          prop: "enableTime",
          align: "center",
          format: "dataFormat1",
        },
        {
          label: "失效时间",
          prop: "disableTime",
          align: "center",
          format: "dataFormat1",
        },
        {
          label: "签价类型",
          align: "center",
          prop: "quotationTypeName",
        },
        {
          label: "采购组织",
          align: "center",
          prop: "purchaseOrgName",
        },
        {
          label: "合作商名称",
          align: "center",
          prop: "partnerName",
        },
        {
          label: "创建日期",
          align: "center",
          prop: "createDate",
          format: "dataFormat1",
        },
        {
          label: "审核完结日期",
          align: "center",
          prop: "completeDate",
          format: "dataFormat1",
        },
        {
          label: "签价原因备注",
          align: "center",
          prop: "remark",
        },
      ],
      count: 0,
      search: {
        where: [],
        page_name: "custom_material_quotation_list",
        page_size: 50,
        page_no: 1,
      },
    };
  },
  methods: {
    //通用查询搜索
    searchClick(obj, resolve) {
      let self = this;
      this.search.where = obj;
      new Promise((res, rej) => {
        self.getList(resolve, res);
      }).then(() => {
        if (self.search.page_no != 1) {
          self.count = 0;
        }
        self.search.page_no = 1;
      });
    },
    itemPageSizeChange(size, resolve) {
      this.search.page_size = size;
      this.getList(resolve);
    },
    itemPageChange(page_no, resolve) {
      this.search.page_no = page_no;
      this.getList(resolve);
    },
  },
};
