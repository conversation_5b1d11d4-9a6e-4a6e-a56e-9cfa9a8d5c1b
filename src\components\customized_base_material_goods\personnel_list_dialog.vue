<!--人员信息列表-->
<template>
  <xpt-list
    ref="personList"
    :data="userlist"
    :btns="btns"
    :colData="colData"
    :selection="selection"
    :pageTotal="count"
    :searchPage="search.page_name"
    @search-click="searchCondition"
    @radio-change="handleCurrentChange"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
  >
  </xpt-list>
</template>
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      search: {
        page: {
          length: self.pageSize,
          pageNo: 1,
        },
        where: [],
        page_name: "cloud_user_login",
        isEnable: 1,
        type: "EMPLOYEE",
      },
      userlist: [],
      count: 0,
      popSelectData: {},
      btns: [
        {
          type: "primary",
          txt: "确认",
          click() {
            self.closeAlert();
          },
        },
      ],
      colData: [
        {
          label: "姓名",
          prop: "realName",
        },
        {
          label: "昵称",
          prop: "nickName",
        },
        {
          label: "工号",
          prop: "employeeNumber",
        },
        {
          label: "类型",
          prop: "type",
          format: "auxFormat",
          formatParams: "personelType",
        },
        {
          label: "是否生效",
          prop: "status",
          format: "statusFilter",
        },
        {
          label: "生效时间",
          prop: "enableTime",
          format: "dataFormat",
        },
        {
          label: "失效时间",
          prop: "disableTime",
          format: "dataFormat",
        },
        {
          label: "备注",
          prop: "remark",
        },
      ],
      selection: "radio",
    };
  },
  methods: {
    sizeChange(size) {
      // 每页加载数据
      this.search.page.length = size;
      this.search.page.pageNo = 1;
      this.getPersonList();
    },
    pageChange(page_no) {
      // 页数改变
      this.search.page.pageNo = page_no;
      this.getPersonList();
    },

    closeAlert() {
      this.params.callback(this.popSelectData);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    handleCurrentChange(val) {
      this.popSelectData = val;
    },

    getPersonList(resolve) {
      var url = "/user-web/api/userLogin/getUserLoginList";
      this.ajax.postStream(
        url,
        this.search,
        (res) => {
          let { result, msg, content } = res.body;
          if (result) {
            this.userlist = content.list || [];
            this.count = content.count;
            this.clearSelectedData();
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },

    searchCondition(txt, resolve) {
      this.search.where = txt;
      this.getPersonList(resolve);
    },
    clearSelectedData() {
      this.popSelectData = null;
      this.$refs.userlist && this.$refs.userlist.clearSelection();
    },
  },
  mounted() {
    this.getPersonList();
  },
};
</script>
