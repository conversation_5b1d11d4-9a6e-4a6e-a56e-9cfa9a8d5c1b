<!-- 价格发布 -->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button
          type="success"
          size="mini"
          @click="handleSave"
          :disabled="handleDisabledStatus('save')"
          >保存</el-button
        >
        <el-button
          size="mini"
          type="primary"
          @click="handleSuccess"
          :disabled="handleDisabledStatus('success')"
          >提交</el-button
        >
        <el-button
          size="mini"
          type="danger"
          @click="handleRevocation"
          :disabled="handleDisabledStatus('revocation')"
          >撤回</el-button
        >
        <el-button
          size="mini"
          type="primary"
          @click="handleInitialAudit"
          :disabled="handleDisabledStatus('audit')"
          >初审</el-button
        >
        <el-button
          size="mini"
          type="info"
          @click="handleRecheck"
          :disabled="handleDisabledStatus('recheck')"
          >复审</el-button
        >
        <el-button
          size="mini"
          type="danger"
          @click="handleReject"
          :disabled="handleDisabledStatus('reject')"
          >驳回</el-button
        >
        <el-button size="mini" type="success" @click="handleRefresh"
          >刷新</el-button
        >
      </el-col>
    </el-row>
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-position="right"
      label-width="110px"
    >
      <el-row class="xpt-flex__bottom">
        <el-tabs v-model="firstTab" style="height: 276px">
          <el-tab-pane
            label="基础信息"
            name="discountDetail"
            class="xpt-flex"
            style="overflow: hidden"
          >
            <el-row>
              <el-col :span="8">
                <el-form-item label="价目表" prop="priceListName">
                  <xpt-input
                    v-model="form.priceListName"
                    size="mini"
                    icon="search"
                    readonly
                    :disabled="handleDisabledStatus('details')"
                    :on-icon-click="handlePriceListNameIconClick"
                    @change="handlePriceListNameChange"
                  ></xpt-input>
                  <el-tooltip
                    v-if="rules.priceListName[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.priceListName[0].message"
                    placement="right-start"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="价格版本名称" prop="releaseName">
                  <el-input
                    v-model="form.releaseName"
                    size="mini"
                    :maxlength="40"
                    :disabled="handleDisabledStatus('createdAndRejected')"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="价格类型" prop="priceListType">
                  <xpt-select-aux
                    v-model="form.priceListType"
                    aux_name="custom_price_type"
                    disabled
                  ></xpt-select-aux>
                  <el-tooltip
                    v-if="rules.priceListType[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.priceListType[0].message"
                    placement="right-start"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="生效时间" prop="enableTime">
                  <el-date-picker
                    v-model="form.enableTime"
                    type="datetime"
                    placeholder="选择时间"
                    size="mini"
                    popper-class="price_dissemination_date_picker"
                    :picker-options="enableDateOptions"
                    :disabled="handleDisabledStatus('createdAndRejected')"
                  ></el-date-picker>
                  <el-tooltip
                    v-if="rules.enableTime[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.enableTime[0].message"
                    placement="right-start"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="价格版本号" prop="releaseNo">
                  <el-input
                    v-model="form.releaseNo"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="审核状态" prop="releaseStatus">
                  <el-input
                    v-model="status_option[form.releaseStatus]"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="其他信息" name="moreSet" class="xpt-flex">
            <el-row>
              <el-col :span="8">
                <el-form-item label="创建人" prop="creatorName">
                  <el-input
                    v-model="form.creatorName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="初审时间" prop="firstCheckTime">
                  <el-date-picker
                    v-model="form.firstCheckTime"
                    type="datetime"
                    size="mini"
                    disabled
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="创建时间" prop="createTime">
                  <el-date-picker
                    v-model="form.createTime"
                    type="datetime"
                    size="mini"
                    disabled
                  ></el-date-picker>
                </el-form-item>
                <el-form-item label="复核人" prop="recheckUserName">
                  <el-input
                    v-model="form.recheckUserName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="初审人" prop="firstCheckUserName">
                  <el-input
                    v-model="form.firstCheckUserName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="复核时间" prop="recheckTime">
                  <el-date-picker
                    v-model="form.recheckTime"
                    type="datetime"
                    size="mini"
                    style="width: 180px"
                    disabled
                  ></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-row>
    </el-form>
    <el-row class="xpt-flex__bottom">
      <el-tabs v-model="secondTab">
        <el-tab-pane
          label="价格明细"
          name="priceDetail"
          class="xpt-flex xpt-flex__bottom"
        >
          <list-dynamic
            ref="priceDetail"
            :btns="priceDetailBtns"
            :colData="priceDetailCols"
            :data="priceDetailList"
            :pageTotal="priceDetailCount"
            :searchPage="priceDetailSearch.page_name"
            selection="checkbox"
            @search-click="priceDetailSearchClick"
          >
            <xpt-import
              slot="btns"
              :taskUrl="uploadUrl"
              :otherParams="otherParams"
              :isupload="handleDisabledStatus('import')"
              class="mgl10"
            ></xpt-import>
            <template #table="{ colData, props }">
              <xpt-list
                v-bind="props"
                ref="list"
                :colData="colData"
                :showHead="false"
                @selection-change="priceDetailSelectionChange"
                @page-size-change="priceDetailPageSizeChange"
                @current-page-change="priceDetailPageChange"
              >
                <template slot="price" slot-scope="scope">
                  <el-input
                    size="mini"
                    type="number"
                    :min="0"
                    v-model="scope.row.price"
                    class="price-input-width"
                    placeholder
                    @change.native="
                      () => {
                        scope.row.price = handlePriceChange(scope.row.price);
                      }
                    "
                    :disabled="handleDisabledStatus('price')"
                  ></el-input>
                </template>
              </xpt-list>
            </template>
          </list-dynamic>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>
<script>
import priceDetail from "./mixins/price_detail.js";
import validate from "@common/validate.js";
import listDynamic from "./components/list-dynamic.vue";
export default {
  components: {
    listDynamic,
  },
  mixins: [priceDetail],
  props: ["params"], //上游参数
  data() {
    var self = this; //本vue
    return {
      firstTab: "discountDetail",
      secondTab: "priceDetail",

      isDetails: false, // 是否详情

      status_option: {
        CREATED: "创建",
        FIRST_CHECK_WAIT: "待初审",
        FIRST_CHECK_REJECTED: "初审驳回",
        RECHECK_WAIT: "待复审",
        RECHECK_REJECTED: "复审驳回",
        COMPLETED: "已完成",
      },

      form: {
        releaseId: "",
        releaseNo: "",
        releaseName: "",
        releaseStatus: "",
        enableTime: "",
        firstCheckTime: "",
        creatorName: "",
        priceListId: "",
        priceListName: "",
        priceListType: "",
        createTime: "",
        recheckUserName: "",
        firstCheckUserName: "",
        recheckTime: "",
      },

      rules: {
        ...[
          {
            priceListName: "价目表",
          },
          {
            priceListType: "价格类型",
          },
          {
            enableTime: "生效时间",
          },
          {
            releaseName: "价格版本名称",
          },
        ].reduce((a, b) => {
          var key = Object.keys(b)[0];
          a[key] = validate.isNotBlank({
            self: this,
            msg: "请填写" + b[key],
          });
          return a;
        }, {}),
      },
      enableDateOptions: {
        /*不能小于当前时间*/
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
        date: (function () {
          return new Date();
        })(),
      },
      priceListData: {},
    };
  },
  methods: {
    handleDisabledStatus(type) {
      if (!this.form) return;
      switch (type) {
        case "createdAndRejected":
        case "add":
        case "del":
        case "price":
          if (!this.isDetails) return;
          return ![
            "CREATED",
            "RECHECK_REJECTED",
            "FIRST_CHECK_REJECTED",
          ].includes(this.form.releaseStatus);
          break;
        case "save":
          if (!this.isDetails) return;
          return ![
            "COMPLETED",
            "CREATED",
            "RECHECK_REJECTED",
            "FIRST_CHECK_REJECTED",
          ].includes(this.form.releaseStatus);
          break;
        case "success":
        case "import":
          return ![
            "CREATED",
            "RECHECK_REJECTED",
            "FIRST_CHECK_REJECTED",
          ].includes(this.form.releaseStatus);
          break;
        case "revocation":
        case "audit":
          return !["FIRST_CHECK_WAIT"].includes(this.form.releaseStatus);
          break;
        case "recheck":
          return !["RECHECK_WAIT"].includes(this.form.releaseStatus);
          break;
        case "reject":
          return !["FIRST_CHECK_WAIT", "RECHECK_WAIT"].includes(
            this.form.releaseStatus
          );
          break;
        case "disable":
          return this.form.releaseStatus !== "COMPLETED";
          break;
        case "details":
          return this.isDetails;
          break;
        default:
          return true;
          break;
      }
    },
    // 获取详情
    getDetail() {
      this.ajax.get(
        "/custom-web/api/customPriceRelease/detail?releaseId=" +
          this.params.releaseId,
        (res) => {
          let { result, content, msg } = res.body;
          if (result) {
            this.form = content;
          } else {
            this.$message.error(msg);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },
    // 价目表
    handlePriceListNameIconClick() {
      let self = this;
      this.$root.eventHandle.$emit("alert", {
        params: {
          callback: (obj) => {
            self.form.priceListId = obj.priceListId;
            self.form.priceListName = obj.priceListName;
            self.form.priceListType = obj.priceListType;
            self.$refs.form.validate();
          },
        },
        component: () => import("./price_list_dialog"),
        style: "width:800px;height:500px",
        title: "价目表",
      });
    },
    handlePriceListNameChange(e) {
      this.form.priceListId = "";
      this.form.priceListName = "";
      this.form.priceListType = "";
      this.$refs.form.validate();
    },

    // 保存
    handleSave() {
      this.handelSaveVerify().then((status) => {
        if (status) {
          this.handleAjaxRequest("save", this.getSaveData());
        }
      });
    },

    handleSuccess() {
      if (this.handleSuccessVerify()) return;
      this.handleAjaxRequest("submit");
    },
    handleRevocation() {
      this.handleAjaxRequest("withdraw");
    },
    handleInitialAudit() {
      this.handleAjaxRequest("check");
    },
    handleRecheck() {
      this.handleAjaxRequest("recheck");
    },
    handleReject() {
      this.handleAjaxRequest("rejected");
    },
    handleRefresh() {
      if (this.params.releaseId) {
        this.getDetail();
        this.getPriceDetailList();
      } else {
        this.$refs.form.resetFields();
        this.priceDetailList = [];
      }
    },

    handleAjaxRequest(urlName, params) {
      let urls = {
        save: "/custom-web/api/customPriceRelease/save?permissionCode=CUSTOM_PRICE_RELEASE_SAVE",
        submit:
          "/custom-web/api/customPriceRelease/submit?permissionCode=CUSTOM_PRICE_RELEASE_SUBMIT",
        withdraw:
          "/custom-web/api/customPriceRelease/withdraw?permissionCode=CUSTOM_PRICE_RELEASE_WITHDRAW",
        check: "/custom-web/api/customPriceRelease/check",
        recheck: "/custom-web/api/customPriceRelease/recheck",
        rejected: "/custom-web/api/customPriceRelease/rejected",
      };
      let url = urls[urlName];

      let data = params
        ? params
        : {
            releaseId: this.form.releaseId,
          };
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { result, content, msg } = res.body;
          if (result) {
            if (urlName === "save") {
              this.params.releaseId = content;
              this.otherParams.releaseId = this.params.releaseId;
              this.isDetails = true;
            }
            this.getDetail();
            this.getPriceDetailList();
            this.$message.success(msg);
          } else {
            this.$message.error(msg);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },

    handelSaveVerify() {
      return new Promise((resolve) => {
        this.$refs.form.validate((valid) => {
          let status = false;
          if (!valid) {
            resolve(status);
          }
          if (
            this.form.enableTime < Date.now() &&
            this.form.releaseStatus !== "COMPLETED"
          ) {
            this.$message.warning("生效时间不能小于当前时间");
            resolve(status);
          }
          if (
            this.priceDetailList.some(
              (item) => !(typeof item.price === "number" && !isNaN(item.price))
            )
          ) {
            this.$message.warning("《价格明细》中的价格不能为空");
            resolve(status);
          }
          resolve(!status);
        });
      });
    },
    handleSuccessVerify() {
      let status = false;
      let listLength = this.priceDetailList.length;
      let copyListLength = this.copyPriceDetailList.length;
      if (!copyListLength) {
        if (listLength) {
          this.$message.warning("请先保存价格明细数据");
          return !status;
        }
        this.$message.warning("价格明细不能为空");
        return !status;
      }
    },

    getSaveData() {
      let addMaterials = this.getAddMaterials();
      let updateMaterials = this.getUpdateMaterials(addMaterials);
      let removeMaterials = this.getRemoveMaterials();
      let disableMaterials = this.getDisableMaterials();
      let { releaseId, releaseName, priceListId, enableTime } = this.form;
      return {
        addMaterials,
        updateMaterials,
        removeMaterials,
        disableMaterials,
        releaseId,
        releaseName,
        priceListId,
        enableTime,
      };
    },

    getAddMaterials() {
      return this.priceDetailList
        .filter((item) => {
          return !this.copyPriceDetailList.some(
            (copyItem) => copyItem.customMaterialId === item.customMaterialId
          );
        })
        .map((item) => {
          let { customMaterialId, customMaterialNumber, price } = item;
          return {
            customMaterialId,
            customMaterialNumber,
            price,
          };
        });
    },
    getUpdateMaterials(addList) {
      return this.priceDetailList
        .filter((item) => {
          return !addList.some((addItem) => {
            return addItem.customMaterialId === item.customMaterialId;
          });
        })
        .filter((item) => {
          return !this.copyPriceDetailList.some((copyItem) => {
            return (
              copyItem.customMaterialId === item.customMaterialId &&
              copyItem.price === item.price
            );
          });
        })
        .map((item) => {
          let { releaseDetailId, price } = item;
          return {
            releaseDetailId,
            price,
          };
        });
    },
    getRemoveMaterials() {
      return this.copyPriceDetailList
        .filter((copyItem) => {
          return !this.priceDetailList.some(
            (item) => item.customMaterialId === copyItem.customMaterialId
          );
        })
        .map((item) => {
          let { releaseDetailId } = item;
          return releaseDetailId;
        });
    },
    getDisableMaterials() {
      return this.copyPriceDetailList
        .filter((copyItem) => {
          if (Number(copyItem.enableStatus) === 1) {
            return !this.priceDetailList.some((item) => {
              return (
                item.customMaterialId === copyItem.customMaterialId &&
                item.enableStatus === copyItem.enableStatus
              );
            });
          }
        })
        .map((item) => {
          let { releaseDetailId } = item;
          return releaseDetailId;
        });
    },
  },
  mounted() {
    if (this.params.releaseId) {
      this.isDetails = true;
      this.otherParams.releaseId = this.params.releaseId;
      this.getDetail();
      this.getPriceDetailList();
    }
  },
};
</script>

<style scoped>
.price-input-width {
  width: 100px;
}
</style>

<style>
.price_dissemination_date_picker .el-picker-panel__link-btn {
  display: none;
}
.price_dissemination_date_picker /deep/ .el-picker-panel__link-btn {
  display: none;
}
</style>
