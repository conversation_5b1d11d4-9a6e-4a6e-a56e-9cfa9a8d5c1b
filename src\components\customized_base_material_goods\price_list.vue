<!-- 定制价目表 -->
<template>
  <list-dynamic
    :data="list"
    :btns="btns"
    :colData="cols"
    @selection-change="listCheckboxChange"
    :pageTotal="count"
    :searchPage="search.page_name"
    @search-click="listSearch"
    :selection="selection"
    @page-size-change="listPageSizeChange"
    @current-page-change="listPageChange"
  >
  </list-dynamic>
</template>
<script>
import listDynamic from "./components/list-dynamic.vue";
export default {
  components: {
    listDynamic,
  },
  data() {
    let self = this;
    return {
      list: [],
      btns: [
        {
          type: "primary",
          txt: "新增价目表",
          disabled: () => {
            return false;
          },
          click() {
            self.listAdd();
          },
        },
        {
          type: "success",
          txt: "刷新",
          disabled: () => {
            return false;
          },
          click() {
            self.refresh();
          },
        },
      ],
      cols: [
        {
          label: "价目表编码",
          align: "center",
          prop: "priceListCode",
          redirectClick(row) {
            let params = {
              priceListId: row.priceListId,
            };
            self.$root.eventHandle.$emit("creatTab", {
              name: "价目列表详情",
              params,
              component: () =>
                import(
                  "@components/customized_base_material_goods/price_list_details.vue"
                ),
            });
          },
        },
        {
          label: "价目表名称",
          align: "center",
          prop: "priceListName",
        },
        {
          label: "价目表归属",
          align: "center",
          prop: "businessDivisionName",
        },
        {
          label: "价格类型",
          align: "center",
          prop: "priceListTypeName",
        },
        {
          label: "生效时间",
          align: "center",
          prop: "enableTime",
          format: "dataFormat1",
        },
        {
          label: "失效时间",
          align: "center",
          prop: "disableTime",
          format: "dataFormat1",
        },
        {
          label: "状态",
          align: "center",
          prop: "priceListStatus",
          formatter(val) {
            switch (val) {
              case 0:
                return "未启用";
                break;
              case 1:
                return "启用";
                break;
              case -1:
                return "停用";
                break;
              default:
                return val;
                break;
            }
          },
        },
        {
          label: "创建人",
          align: "center",
          prop: "creatorName",
        },
        {
          label: "创建时间",
          align: "center",
          prop: "createTime",
          format: "dataFormat1",
        },
      ],

      listSelect: [],
      count: 0,
      search: {
        page_name: "custom_price_list",
        where: [],
        page_size: 50,
        page_no: 1,
      },
      selection: "checkbox",
    };
  },
  provide() {
    return {
      getFieldsValues: this.getFieldsValues,
    };
  },
  methods: {
    getFieldsValues(callback) {
      let list = __AUX
        .getValidData("custom_price_type")
        .map(({ name, code }) => {
          return {
            code,
            name,
          };
        });
      callback("bc1b836022e1974955c6ce27056b45f9", list);
    },
    //刷新
    refresh() {
      this.getList();
    },
    // 行选中
    listCheckboxChange(row) {
      this.listSelect = row;
    },
    // 获取价目表信息
    getList(resolve) {
      let url = "/custom-web/api/customPriceList/list";
      this.ajax.postStream(
        url,
        this.search,
        (res) => {
          let { result, content, msg } = res.body;
          if (result) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve();
        }
      );
    },
    //通用查询搜索
    listSearch(where, reslove) {
      this.search.where = where;
      this.getList(reslove);
    },
    // 当前页改变
    listPageSizeChange(ps) {
      this.search.page_size = ps;
      this.getList();
    },
    // 当前页面显示行数改变
    listPageChange(page) {
      this.search.page_no = page;
      this.getList();
    },
    // 新增
    listAdd() {
      let principal_user_id = this.getEmployeeInfo("id");
      let url = "/permission-web/api/businessPermission/check";
      let data = {
        principal_user_id,
        business_type_code: "CUSTOM_PRICE_LIST_MANAGE",
      };
      this.ajax.postStream(
        url,
        data,
        (res) => {
          let { result, msg } = res.body;
          if (result) {
            this.$root.eventHandle.$emit("creatTab", {
              name: "价目表新增",
              component: () =>
                import(
                  "@components/customized_base_material_goods/price_list_details.vue"
                ),
            });
          } else {
            this.$message.error(msg);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },
  },
  computed: {},
  watch: {},
  mounted() {
    this.getList();
  },
};
</script>
<style type="text/css">
.el-dialog__wrapper {
  background-color: transparent;
}

.xiudounb {
  color: red;
}
</style>
