<!-- 价目列表详情-->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button
          :type="btn.type"
          size="mini"
          v-for="(btn, index) in firstBtns"
          :key="index"
          :disabled="btn.disabled"
          :loading="btn.loading || false"
          @click="btn.click"
          >{{ btn.txt }}</el-button
        >
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-tabs v-model="firstTab" style="height: 276px">
        <el-tab-pane label="基本信息" name="appointment">
          <el-form
            label-position="right"
            class="mgt10"
            :model="form"
            :rules="rules"
            label-width="100px"
            ref="form"
          >
            <el-col :span="8">
              <el-form-item label="价目表编码：" prop="priceListCode">
                <el-input
                  readonly
                  size="mini"
                  v-model="form.priceListCode"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="价目表归属：" prop="businessDivision">
                <xpt-select-aux
                  v-model="form.businessDivision"
                  aux_name="business_division"
                  :disabled="isDetails"
                ></xpt-select-aux>
                <el-tooltip
                  v-if="rules.businessDivision[0].isShow"
                  class="item"
                  effect="dark"
                  :content="rules.businessDivision[0].message"
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item
                label="启用状态："
                prop="priceListStatus"
                v-if="isDetails"
              >
                <el-select
                  placeholder="请选择"
                  size="mini"
                  v-model="form.priceListStatus"
                  disabled
                >
                  <el-option label="未启用" :value="0"></el-option>
                  <el-option label="启用" :value="1"></el-option>
                  <el-option label="停用" :value="-1"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="备注：" class="mgb5" prop="remark">
                <el-input
                  type="textarea"
                  size="mini"
                  v-model="form.remark"
                  :maxlength="100"
                  :disabled="isEnable"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="价目表名称：" prop="priceListName">
                <el-input
                  v-model="form.priceListName"
                  size="mini"
                  :maxlength="30"
                  :disabled="isEnable"
                ></el-input>
                <el-tooltip
                  v-if="rules.priceListName[0].isShow"
                  class="item"
                  effect="dark"
                  :content="rules.priceListName[0].message"
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="生效时间：" prop="enableTime">
                <el-date-picker
                  v-model="form.enableTime"
                  type="datetime"
                  placeholder="选择日期"
                  size="mini"
                  :picker-options="enableDateOptions"
                  :disabled="isEnable"
                ></el-date-picker>
                <el-tooltip
                  v-if="rules.enableTime[0].isShow"
                  class="item"
                  effect="dark"
                  :content="rules.enableTime[0].message"
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="创建人：" prop="creatorName" v-if="isDetails">
                <el-input
                  v-model="form.creatorName"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="价格类型：" prop="priceListType">
                <xpt-select-aux
                  v-model="form.priceListType"
                  aux_name="custom_price_type"
                  :disabled="isDetails"
                ></xpt-select-aux>
                <el-tooltip
                  v-if="rules.priceListType[0].isShow"
                  class="item"
                  effect="dark"
                  :content="rules.priceListType[0].message"
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="失效时间：" prop="disableTime">
                <el-date-picker
                  v-model="form.disableTime"
                  type="datetime"
                  placeholder="选择日期"
                  size="mini"
                  :editable="false"
                  :picker-options="disableDateOptions"
                  :disabled="isEnable"
                ></el-date-picker>
                <el-tooltip
                  v-if="rules.disableTime[0].isShow"
                  class="item"
                  effect="dark"
                  :content="rules.disableTime[0].message"
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item
                label="创建时间："
                prop="createTime"
                v-if="isDetails"
              >
                <el-date-picker
                  v-model="form.createTime"
                  type="date"
                  placeholder="选择日期"
                  size="mini"
                  disabled
                  :editable="false"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class="xpt-flex__bottom quotation-list-head-margin">
      <el-tabs v-model="secondTab">
        <el-tab-pane
          label="人员信息"
          name="personnelInfo"
          class="xpt-flex xpt-flex__bottom"
        >
          <xpt-list
            ref="personnelInfo"
            :btns="btns"
            :colData="cols"
            :data="list"
            selection="checkbox"
            @selection-change="listCheckboxChange"
          ></xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-dialog
      title="添加人员"
      :visible.sync="visible"
      class="personnel-Info-dialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :center="true"
    >
      <el-form ref="form" :model="dialogForm" label-width="120px">
        <el-form-item
          label="人员类型："
          prop="managerType"
          class="el-form-item"
        >
          <el-select
            v-model="dialogForm.managerType"
            placeholder="请选择人员类型"
            size="mini"
          >
            <el-option
              v-for="item in managerTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="人员工号："
          prop="managerEmployeeNumber"
          class="el-form-item"
        >
          <xpt-input
            size="mini"
            v-model="dialogForm.managerEmployeeNumber"
            icon="search"
            :on-icon-click="handleManagerEmployeeNumberOnIconClick"
            @change="handleManagerEmployeeNumberChange"
            @blur="handleManagerEmployeeNumberBlur"
            placeholder="工号/姓名/花名"
            :disabled="false"
          ></xpt-input>
        </el-form-item>
        <el-form-item
          label="人员名称："
          prop="managerUserName"
          class="el-form-item"
          size="mini"
        >
          <span>{{ dialogForm.managerUserName }}</span>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSuccess">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import validate from "@common/validate.js";
import personnelInfo from "./mixins/personnel_info";
export default {
  props: ["params"], //上游参数
  mixins: [personnelInfo],
  data() {
    var self = this; //本vue
    return {
      firstTab: "appointment",
      secondTab: "personnelInfo",
      isEnable: false, // 是否可编辑
      isDetails: false,

      form: {
        priceListCode: "",
        priceListName: "",
        priceListType: "",
        businessDivision: "",
        enableTime: "",
        disableTime: "",
        priceListStatus: 0,
        creatorName: "",
        createTime: "",
        remark: "",
      }, //表单内容
      rules: {
        ...[
          {
            priceListName: "价目表名称",
          },
          {
            businessDivision: "价目表归属",
          },
          {
            enableTime: "生效时间",
          },
          {
            disableTime: "失效时间",
          },
          {
            priceListType: "价格类型",
          },
        ].reduce((a, b) => {
          var key = Object.keys(b)[0];
          a[key] = validate.isNotBlank({
            self: this,
            msg: "请填写" + b[key],
          });
          return a;
        }, {}),
      },
      firstBtns: [
        {
          type: "success",
          txt: "保存",
          loading: false,
          click() {
            self.handleSave();
          },
          disabled: false,
        },
        {
          type: "primary",
          txt: "启用",
          disabled: true,
          loading: false,
          click() {
            self.handleEnable();
          },
        },
        {
          type: "info",
          txt: "停用",
          disabled: true,
          loading: false,
          click() {
            self.handleDisable();
          },
        },
        {
          type: "success",
          txt: "刷新",
          disabled: false,
          click() {
            self.refresh();
          },
        },
      ],

      enableDateOptions: {
        /*生效效时间小于失效时间*/
        disabledDate(time) {
          return self.form.disableTime
            ? time.getTime() > new Date(self.form.disableTime)
            : false;
        },
        date: (function () {
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth() + 1;
          var day = date.getDate();
          var time = year + "-" + month + "-" + day + " " + "00:00:00";
          return new Date(time);
        })(),
      },
      disableDateOptions: {
        disabledDate: (time) => {
          /*失效时间大于生效时间*/
          return self.form.enableTime
            ? time.getTime() < new Date(self.form.enableTime)
            : false;
        },
        date: (function () {
          var date = new Date();
          var year = date.getFullYear();
          var month = date.getMonth() + 1;
          var day = date.getDate();
          var time = year + "-" + month + "-" + day + " " + "23:59:59";
          return new Date(time);
        })(),
      },
    };
  },
  methods: {
    // 获取详情
    getDetail() {
      let self = this;
      this.ajax.get(
        "/custom-web/api/customPriceList/detail?priceListId=" +
          this.params.priceListId,
        (res) => {
          let { result, content, msg } = res.body;
          if (result) {
            this.form = content;
            let startStatus = [0, -1].includes(content.priceListStatus);
            this.firstBtns[1].disabled = !startStatus;
            this.firstBtns[2].disabled = startStatus;
            this.isEnable = content.priceListStatus !== 0;
            this.isDetails = true
            this.list = content.managerList;
            this.copyList = this.list.slice();
          } else {
            self.$message.error(msg);
          }
        },
        (e) => {
          self.$message.error(e);
        }
      );
    },
    // 保存
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.firstBtns[0].loading = true;
        let data = this.getFormData();
        this.ajax.postStream(
          "/custom-web/api/customPriceList/save?permissionCode=CUSTOM_PRICE_LIST_MANAGE",
          data,
          (res) => {
            let { result, content, msg } = res.body;
            if (result) {
              this.params.priceListId = content;
              this.getDetail();
              this.$message.success(msg);
            } else {
              this.$message.error(msg);
            }
            this.firstBtns[0].loading = false;
          },
          (err) => {
            this.firstBtns[0].loading = false;
            this.$message.error(err);
          }
        );
      });
    },
    getFormData() {
      let addManagerList = this.getAddManagerData();
      let removeManagerIds = this.getRemoveManagerIdsData();

      let {
        priceListId,
        priceListName,
        priceListType,
        businessDivision,
        enableTime,
        disableTime,
        remark,
      } = this.form;
      return {
        priceListId,
        priceListName,
        priceListType,
        businessDivision,
        enableTime,
        disableTime,
        remark,
        addManagerList,
        removeManagerIds,
      };
    },
    getAddManagerData() {
      return this.list.filter((item) => {
        return !item.managerId;
      });
    },
    getRemoveManagerIdsData() {
      let copyIds = this.copyList.map((item) => item.managerId);
      let ids = this.list
        .filter((item) => {
          return !!item.managerId;
        })
        .map((item) => item.managerId);
      return copyIds.filter((item) => {
        return !ids.includes(item);
      });
    },
    // 启用
    handleEnable() {
      let self = this;
      self.$root.eventHandle.$emit("openDialog", {
        ok() {
          let newTime = new Date().getTime();
          if (newTime >= self.form.disableTime) {
            self.$message.error("当前时间大于失效时间");
            return;
          }
          self.firstBtns[1].loading = true;
          let data = {
            priceListId: self.params.priceListId,
          };
          self.ajax.postStream(
            "/custom-web/api/customPriceList/enable?permissionCode=CUSTOM_PRICE_LIST_MANAGE",
            data,
            (res) => {
              let { result, msg } = res.body;
              if (result) {
                self.getDetail();
                self.$message.success(msg);
              } else {
                self.$message.error(msg);
              }
              self.firstBtns[1].loading = false;
            },
            (err) => {
              self.firstBtns[1].loading = false;
              self.$message.error(err);
            }
          );
        },
        okTxt: "确认",
        no() {},
        noTxt: "取消",
        txt: "确认启用？",
        noCancelBtn: true,
      });
    },
    // 停用
    handleDisable() {
      let self = this;
      self.$root.eventHandle.$emit("openDialog", {
        ok() {
          self.firstBtns[2].loading = true;
          let data = {
            priceListId: self.params.priceListId,
          };
          self.ajax.postStream(
            "/custom-web/api/customPriceList/disable?permissionCode=CUSTOM_PRICE_LIST_MANAGE",
            data,
            (res) => {
              if (res.body.result) {
                self.getDetail();
                self.$message.success(res.body.msg);
              } else {
                self.$message.error(res.body.msg);
              }
              self.firstBtns[2].loading = false;
            },
            (err) => {
              self.firstBtns[2].loading = false;
              self.$message.error(err);
            }
          );
        },
        okTxt: "确认",
        no() {},
        noTxt: "取消",
        txt: "确认停用？",
        noCancelBtn: true,
      });
    },
    refresh() {
      if (this.params.priceListId || this.params.priceListId >= 0) {
        this.getDetail();
      } else {
        this.$refs.form.resetFields();
        this.list = [];
      }
    },
  },
  mounted: function () {
    if (this.params.priceListId || this.params.priceListId >= 0) {
      this.getDetail();
      return;
    }
  },
  created() {},
  watch: {},
  computed: {},
};
</script>
<style scoped>
.personnel-Info-dialog {
  z-index: 10 !important;
}

.personnel-Info-dialog .dialog-footer {
  display: flex;
}

.personnel-Info-dialog .el-button {
  flex: 1;
}

.personnel-Info-dialog .el-dialog--small {
  width: 500px !important;
}

.personnel-Info-dialog .el-form-item {
  margin: 5px 0;
}

.personnel-Info-dialog .input-width {
  width: 100%;
}
</style>
<style>
.personnel-Info-dialog + .v-modal {
  z-index: 9 !important;
}
</style>
