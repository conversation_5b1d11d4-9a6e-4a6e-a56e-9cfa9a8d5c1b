<!-- 价目表弹窗 -->
<template>
  <div class="xpt-flex priceListDialog">
    <list-dynamic
      ref="listDynamic"
      :data="list"
      :btns="btns"
      :colData="cols"
      :pageTotal="count"
      :selection="selection"
      :searchPage="search.page_name"
      @search-click="searchData"
      @radio-change="radioChange"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    ></list-dynamic>
  </div>
</template>
  <script>
import moment from "moment";
import listDynamic from "./components/list-dynamic.vue";
export default {
  components: {
    listDynamic,
  },
  data() {
    let self = this;
    return {
      btns: [
        {
          type: "primary",
          txt: "确认",
          click: this.close,
        },
      ],
      cols: [
        {
          label: "价目表编码",
          align: "center",
          prop: "priceListCode",
        },
        {
          label: "价目表名称",
          align: "center",
          prop: "priceListName",
        },
        {
          label: "归属事业部",
          align: "center",
          prop: "businessDivisionName",
        },
        {
          label: "生效时间",
          align: "center",
          prop: "disableTime",
          format: "dataFormat1",
        },
        {
          label: "失效时间",
          align: "center",
          prop: "enableTime",
          format: "dataFormat1",
        },
      ],

      listSelect: [],
      count: 0,
      search: {
        page_name: "custom_price_list",
        where: [],
        page_size: 50,
        page_no: 1,
      },
      where: [
        {
          condition: "OR",
          field: "3129ba8bcde33a2101b6eb7cb3f1689e",
          operator: "=",
          table: "630756ba912bf7d09e992a22e1a44727",
          value: "1",
        },
        {
          condition: "AND",
          field: "66a45dbb8c4b5f212187efdf346cee2b",
          operator: ">",
          table: "630756ba912bf7d09e992a22e1a44727",
          value: moment(Date.now()).format("YYYY-MM-DD HH:mm:ss"),
        },
      ],
      selection: "radio",
      list: [],
      selectData: "",
    };
  },
  props: ["params"],
  methods: {
    radioChange(data) {
      this.selectData = data;
    },
    close() {
      if (!this.selectData) {
        this.$message.error("请先选择一个价目表");
        return;
      }
      this.params.callback(this.selectData);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    searchData(where, resolve) {
      where.forEach((item) => {
        item.condition = "AND";
      });
      this.search.where = where;
      this.selectData = null;
      this.getList(resolve);
    },
    pageSizeChange(pageSize) {
      this.search.page.length = pageSize;
      this.selectData = null;
      this.getList();
    },
    pageChange(page) {
      this.search.page.pageNo = page;
      this.selectData = null;
      this.getList();
    },
    getList(resolve) {
      let url = "/custom-web/api/customPriceList/list";
      this.search.where = this.where.concat(this.search.where);
      this.ajax.postStream(
        url,
        this.search,
        (res) => {
          let { result, content, msg } = res.body;
          if (result) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve(err);
        }
      );
    },
  },
  mounted() {
    this.getList();
  },
};
</script>
<style>
.priceListDialog .list-dynamic {
  width: 100%;
  margin: auto;
  min-width: auto;
}
.priceListDialog .list-header .left {
  width: auto;
  min-width: 50px;
}
.priceListDialog .body {
  padding: 8px 0px 0px 0px;
}
</style>
    
  