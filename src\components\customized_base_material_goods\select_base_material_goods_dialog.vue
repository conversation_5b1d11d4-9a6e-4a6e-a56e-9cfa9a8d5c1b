<!-- 选择基材商品弹窗 -->
<template>
  <div class="xpt-flex selectBaseMaterialGoodsDialog">
    <list-dynamic
      ref="selectBaseMaterialGoodsDialog"
      :data="list"
      :btns="btns"
      :colData="cols"
      :pageTotal="count"
      :selection="selection"
      :searchPage="search.page_name"
      @search-click="searchData"
      @selection-change="selectionChange"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    ></list-dynamic>
  </div>
</template>
<script>
import listDynamic from "./components/list-dynamic.vue";
export default {
  components: {
    listDynamic,
  },
  data() {
    let self = this;
    return {
      btns: [],
      cols: [
        {
          label: "基材商品编码",
          align: "center",
          prop: "materialNumber",
        },
        {
          label: "基材类型",
          align: "center",
          prop: "materialTypeName",
        },
        {
          label: "花色",
          align: "center",
          prop: "designColorName",
        },
        {
          label: "规格",
          align: "center",
          prop: "specificationName",
        },
        {
          label: "签价",
          align: "center",
          prop: "thisQuotationPrice",
        },
      ],

      listSelect: [],
      count: 0,
      search: {
        page_name: "custom_material_list",
        where: [],
        page_size: 50,
        page_no: 1,
      },
      selection: "checkbox",
      list: [],
      selectData: [],
      baseMaterialValueGroup: [],
    };
  },
  props: ["params"],
  provide() {
    return {
      baseMaterialXptInput: this.handleXptInputOnIconClick,
      clearBaseMaterialXptInputValue: this.clearBaseMaterialXptInputValue,
    };
  },
  methods: {
    selectionChange(data) {
      this.selectData = data;
    },
    close() {
      if (!this.selectData.length) {
        this.$message.error("请先选择基材");
        return;
      }
      this.params.callback(this.selectData);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    searchData(obj, resolve) {
      let where = JSON.parse(JSON.stringify(obj));
      if (!!this.baseMaterialValueGroup.length) {
        where.forEach((item) => {
          let i = this.baseMaterialValueGroup.findIndex(
            (row) => row.name === item.value
          );
          if (i !== -1) {
            item.value = this.baseMaterialValueGroup[i].number;
          }
        });
      }
      this.search.where = where;
      this.selectData = null;
      this.getList(resolve);
    },
    pageSizeChange(pageSize) {
      this.search.page_size = pageSize;
      this.selectData = null;
      this.getList();
    },
    pageChange(pageNo) {
      this.search.page_no = pageNo;
      this.selectData = null;
      this.getList();
    },
    getList(resolve) {
      var postData = JSON.parse(JSON.stringify(this.search));
      if (this.params.setWhere) {
        this.params.setWhere(postData); //在setWhere方法里面直接修改postData对象内容
      }
      let url = "/custom-web/api/customMaterial/list";
      this.ajax.postStream(
        url,
        this.search,
        (res) => {
          let { result, content, msg } = res.body;
          if (result) {
            this.list = content.list;
            this.count = content.count;
          } else {
            this.$message.error(msg);
          }
          resolve && resolve();
        },
        (e) => {
          this.$message.error(e);
          resolve && resolve(err);
        }
      );
    },

    // 筛选组件，弹窗选择数据
    handleXptInputOnIconClick(res, callback) {
      let self = this;
      let typeGroup = [
        {
          id: "1dfe5d1f8f360726fad8a2a32ee7f212",
          name: "基材类型",
          type: 0,
        },
        {
          id: "b66530dd43e4fc719ad9f935b6752cfc",
          name: "花色",
          type: 1,
        },
        {
          id: "9a2f1d17c0952cdff676d200108482e6",
          name: "规格",
          type: 2,
        },
      ];
      let i = typeGroup.findIndex((item) => item.id === res.field);
      if (i === -1) {
        this.$message.error("差找不到对应类型");
        return;
      }
      let params = {
        typeGroup: typeGroup[i].type,
        callback: function (e) {
          let { number, name } = e;
          let type = typeGroup[i].type;
          self.baseMaterialValueGroup.push({
            number,
            name,
            type,
          });
          callback && callback(name, type);
        },
      };
      this.$root.eventHandle.$emit("alert", {
        params,
        component: () =>
          import("@components/customized_base_material_goods/baseEnumDialog"),
        style: "width:800px;height:500px",
        title: "选择基材基础信息",
      });
    },
    clearBaseMaterialXptInputValue(type) {
      let i = this.baseMaterialValueGroup.findIndex(
        (item) => item.type === type
      );
      if (i === -1) return;
      this.baseMaterialValueGroup.splice(i, 1);
    },
  },
  mounted() {
    this.btns = [
      {
        type: "primary",
        txt: "确认",
        click: this.close,
      },
    ];
    this.getList();
  },
};
</script>
<style>
.selectBaseMaterialGoodsDialog .list-dynamic {
  width: 100%;
  margin: auto;
  min-width: auto;
}
.selectBaseMaterialGoodsDialog .list-header .left {
  width: auto;
  min-width: 50px;
}
.selectBaseMaterialGoodsDialog .body {
  padding: 8px 0px 0px 0px;
  overflow: hidden;
}
</style>
    