<!--经销商扣款列表-->
<template>
	<xpt-list
		:data='roleList.length ? roleList.concat(ListTotal) : roleList'
		:colData='colData'
		:btns="btns"
		selection='hidden'
		:pageTotal='pageTotal'
		:searchPage="searchParams.page_name"
		isNeedClickEvent
		@search-click='searchClick'
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
		@row-dblclick="d => openOrder(d.id, d.order_no)"
		ref='xptList'
	></xpt-list>
</template>

<script>
import fn from '@common/Fn.js'
export default {
	props: ['params', 'shopId'/*如果shopId存在则表示货款管理详情页面*/],
	data (){
		let self = this
		return {
			roleList:[],

			btns: [{
				type: 'primary',
				txt: '刷新',
				click: () => this.searchFun(),
			}
			// ,{
			// 		type: 'primary',
			// 		txt: '导出',
			// 		click: () => this.exportExcel(),
			// 	},
				// {
				// 	type: 'primary',
				// 	txt: '查看导出结果',
				// 	click: () => this.showExportList(),
				// }
				],
			ListTotal: {
				pay_amount: '合计0',
				factorage: '合计0',
				paid_amount: '合计0',

			},
			searchParams:{
				page_name: 'dealer_funds_change_item_admin',
				where: [],
				page_size: 50,
				page_no: 1,
				id: '',
			},

			pageTotal:0,
			colData:[{
				label: '合并单号',
				prop: 'mergeTradeNo',
				width: 200,
				redirectClick: d => this.openOrder(d.mergeTradeId, d.mergeTradeNo),
			},{
				label: '店铺',
				prop: 'shopName',
			},{
				label: '买家昵称',
				prop: 'cusName',
			},{
				label: '实际售价',
				prop: 'mergeActPrice',
				// formatter(val){
				// 	if(!val){
				// 		return self.getthousand(val)
				// 	}
				// }
			},{
				label: '未扣款金额',
				prop: 'unauditedAmount',
				// formatter(val){
				// 	if(!val){
				// 		return self.getthousand(val)
				// 	}
				// }
			},{
				label: '扣款金额',
				prop: 'dealerDeductionAmount',
				// formatter(val){
				// 	if(!val){
				// 		return self.getthousand(val)
				// 	}
				// }
			},{
				label: '冻结金额',
				prop: 'frozenAmount',
				// formatter(val){
				// 	if(!val){
				// 		return self.getthousand(val)
				// 	}
				// }
			},{
	            label: '已采购金额',
				prop: 'purchaseAmount',
				// formatter(val){
				// 	if(!val){
				// 		return self.getthousand(val)
				// 	}
				// }
	        },{
				label: '发运行应扣金额',
				prop: 'mergeDeductionAmount',
				// formatter(val){
				// 	if(!val){
				// 		return self.getthousand(val)
				// 	}
				// }
			},{
				label: '扣款是否异常',
				prop: 'ifError',
				formatter: prop => ({
					true: '是',
					false: '否'
				}[prop])
			},{
	            label: '最后更新时间',
	            prop: 'modifyTime',
	            format: 'dataFormat1',
	            width: 150,
	        },{
				label: '最后更新人',
				prop: 'modifierName',
			},{
	            label: '扣款明细',
	            prop: 'mergeTradeId',
	            formatter: prop => prop ? '点击查看' : '',
	            redirectClick: d => this.toDealerFundsDedution(d.mergeTradeId),
	        }],
		}
	},
	methods: {
		toDealerFundsDedution (id){
			this.$root.eventHandle.$emit('creatTab',{
				name: "货款扣款详情",
				params: { mergeTradeId: id },
				component: () => import('@components/dealer/dealerFundsDedution.vue')
			})
		},
		addNewOrder (){
			this.$root.eventHandle.$emit('creatTab', {
				name: '新增货款充值',
				params: {},
				component: () => import('@components/dealer/recharge_of_goods_detail'),
			})
		},
		searchClick(obj, resolve){
			this.searchParams.where = obj
			this.searchFun(resolve)
		},
		_ListTotal(){
			var totalObj =  {
				pay_amount: 0,
				factorage: 0,
				paid_amount: 0,
				change_amount:0,
				freeze_payment_amount:0
			}
			,	totalKey = Object.keys(totalObj)

			this.roleList.forEach(obj => {
				if(obj.status !== 'CANCEL'){//行状态是取消的不统计在合计
					totalKey.forEach(key => {
						if(obj[key]){
							totalObj[key] += Number(obj[key])
						}
					})
				}
			})
			// console.log(totalObj)
			totalKey.forEach(key => {
				totalObj[key] =  + totalObj[key].toFixed(2)
			})
			totalObj.order_status = '';
			totalObj.order_no = '合计';
			this.ListTotal = totalObj;
		},
		searchFun (resolve, shopId){
			if(shopId){
				this.shopId = shopId
				this.searchParams.shop_id = shopId
			}
			this.ajax.postStream('/dealer-web/api/dealerFundsManage/findAdminDealerFundsChangeItemPage', this.searchParams, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
					this.roleList = res.body.content.list || []
					this._ListTotal()
					// if(!this.shopId){//货款充值列表仅展示货款充值、结算与激励的数据
					// 	this.roleList = this.roleList.filter(obj => /^(RECHARGE|ADJUSTMENT)$/.test(obj.business_type))
					// }
					this.pageTotal = res.body.content.count
				}else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve()
			}, () => {
				resolve && resolve()
			}, this.params && this.params.tabName)
		},
		openOrder (id, order_no){
			console.log(id,order_no)
			if(order_no == '合计'){
				return false;
			}
			this.$root.eventHandle.$emit('creatTab',{
				name: "合并订单详情",
				params: { merge_trade_id: id },
				component: () => import('@components/order/merge.vue')
			})
		},
		pageChange(page_size){
			this.searchParams.page_size = page_size
			this.searchFun()
		},
		currentPageChange(page){
			this.searchParams.page_no = page
			this.searchFun()
		},
		getthousand(data){
			// 数字获取千分位
			// data = data.split(',').join('')
			if(data == '' || data == null){
				return data
			}
			let Negative = '';
			if(Number(data)<0){
				Negative = '-';
			}
			data = Math.abs(data);
			// console.log(data)
			let changeData = data.toString().split(',').join('');
			let splitArr = [];
			splitArr = changeData.split('.');
			// console.log(splitArr.length == 1)

			let leftArr = splitArr[0].split('').reverse();
			let rightArr =  splitArr[1] || '';
			let newArr = []
			leftArr.forEach((item,index) => {
				newArr.push(item)
				if((index + 1) % 3 == 0 && (index + 1) != leftArr.length){
                	newArr.push(",");
            	}
			})
			newArr = newArr.reverse();
			// console.log(newArr.join('')+'.'+rightArr);
			let returnData = splitArr.length == 1 ? newArr.join(''):(newArr.join('')+'.'+rightArr);
			// console.log(returnData,rightArr)
			return Negative + returnData;
		},
		exportExcel (e){
			// var $btn = e.target
			let self = this;
			// $btn.disabled = true
			this.ajax.postStream('/dealer-web/api/dealerFundsManageExport/exportDealerFundsChangeItem', self.searchParams, res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				// $btn.disabled = false
			}, () => {
				// $btn.disabled = false
			})
		},
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_DEALER_DEDUTION',
					},
				},
			})
		},
	},
	mounted (){

			this.searchFun()

	},
}
</script>
