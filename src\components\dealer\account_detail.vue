<!-- 新增/修改经销商账户 -->
<template>
	<div>
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type='primary' size='mini' @click="preSave('submitData')" :loading="isLoading" :disabled='isLoading'>保存</el-button >
				<el-button type='primary' size='mini' @click="actionById('submit?permissionCode=DEALER_RECHARGE_ACCOUNT_SUBMIT')" :loading="isLoading" :disabled='isLoading'>提交</el-button >
				<el-button type='primary' size='mini' @click="actionById('withdraw?permissionCode=DEALER_RECHARGE_ACCOUNT_WITHDRAW')" :loading="isLoading" :disabled='isLoading'>撤回</el-button >
				<el-button type='primary' size='mini' @click="actionById('lock?permissionCode=DEALER_RECHARGE_ACCOUNT_LOCK')" :loading="isLoading" :disabled='isLoading'>锁定</el-button >
				<el-button type='primary' size='mini' @click="retrialFunc('retrial?permissionCode=DEALER_RECHARGE_ACCOUNT_REJECT')" :loading="isLoading" :disabled='isLoading'>驳回</el-button >
				<el-button type='primary' size='mini' @click="actionById('audit?permissionCode=DEALER_RECHARGE_ACCOUNT_AUDIT')" :loading="isLoading" :disabled='isLoading'>审核</el-button >
				<el-button type='primary' size='mini' @click="actionById('invalid?permissionCode=DEALER_RECHARGE_ACCOUNT_INVALID')" :loading="isLoading" :disabled='isLoading'>作废</el-button >
				<!-- <el-button type='primary' size='mini' @click="actionById('invalid?permissionCode= DEALER_RECHARGE_ACCOUNT_INVALID')" :loading="isLoading" :disabled='isLoading'>生效</el-button > -->
				<el-button type='primary' size='mini' @click="actionById('loseEfficacy?permissionCode=DEALER_RECHARGE_ACCOUNT_EFFICACY')" :loading="isLoading" :disabled='isLoading'>失效</el-button >
			</el-col>

		
		</el-row>
		<el-row	:gutter='40'>
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基本信息" name="orderInfo">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="submitData" :rules="rules" ref="submitData">
						<el-col :span='6'>
							<el-form-item label="单据编号" prop="order_no">
								<el-input size='mini' v-model="submitData.order_no" disabled></el-input>
							</el-form-item>
							<el-form-item label="账号类型" prop="account_type">
								<el-select v-model='submitData.account_type' label-width="150px"  size='mini' :disabled="getChangeStatus()">
									<el-option v-for='item in account_type_options' :key='item.label' :label='item.label' :value='item.value'></el-option>
								</el-select>
								<!-- <el-tooltip v-if='rules.account_type[0].isShow' class="item" effect="dark" :content="rules.account_type[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip> -->
							</el-form-item>
							<el-form-item label="开户行" prop="account_bank">
								<el-input size='mini' v-model="submitData.account_bank" :disabled="getChangeStatus()"></el-input>
							</el-form-item>
							
						</el-col>
						
						
						
						<el-col :span="6">
							<el-form-item label="店铺" prop="shop_name">
								<el-input size='mini' v-model="submitData.shop_name" icon="search" :on-icon-click="selectShop" readonly :disabled="submitData.order_status =='RETRIAL'||submitData.order_status =='WITHDRAWED'||getChangeStatus()"></el-input>
							</el-form-item>
							<el-form-item label="支付方式" prop="payment_mode">
								<xpt-select-aux v-model='submitData.payment_mode' aux_name='payTypeDealer' :disabled="submitData.order_status =='RETRIAL'||submitData.order_status =='WITHDRAWED'||getChangeStatus()"></xpt-select-aux>

							</el-form-item>
							<el-form-item label="附件上传">
								<el-button type='primary' size='mini' @click="viewOrUpload()" >查看/上传附件</el-button >
							</el-form-item>
							
						</el-col>

						<el-col :span='6'>
							
							<el-form-item label="经销商" prop="dealer_name">
								<el-input size='mini' v-model="submitData.dealer_name" disabled ></el-input>
							</el-form-item>
							<el-form-item label="充值账号" prop="recharge_account">
								<!-- <el-input size='mini' v-model="submitData.freight_type"></el-input> -->
								<el-input size='mini' v-model="submitData.recharge_account" :disabled="getChangeStatus()"></el-input>
							</el-form-item>
							<el-form-item label="备注" prop="remark">
								<!-- <el-input size='mini' v-model="submitData.freight_type"></el-input> -->
								<el-input size='mini' v-model="submitData.remark" :disabled="getChangeStatus()"></el-input>
							</el-form-item>
							
						</el-col>	
						<el-col :span='6'>
							<el-form-item label="单据状态" prop="order_status">
								<!-- <el-input size='mini' v-model="submitData.order_status"></el-input> -->
								<el-select v-model='submitData.order_status' label-width="150px"  size='mini' disabled>
									<el-option v-for='item in order_status_option' :key='item.label' :label='item.label' :value='item.value'></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="开户名" prop="account_name">
								<el-input size='mini' v-model="submitData.account_name" :disabled="getChangeStatus()"></el-input>
							</el-form-item>
							<el-form-item label="状态" prop="status">
								<!-- <el-input size='mini' v-model="submitData.account_name"></el-input> -->
								<span>{{submitData.status == '1' ? '生效':'失效'}}</span>
							</el-form-item>
						</el-col>	
					
			    </el-form>
			  </el-tab-pane>
				 <el-tab-pane label="其它信息" name="otherInfo">
				
			    	<xpt-form :data='submitData' :cols='otherCols' label='120px'></xpt-form>

			  </el-tab-pane>
			</el-tabs>
		</el-row>
		<!-- 附件组件 -->
		<xpt-image-v2
			:isGetImgList="isGetImgList"
			:show="isShow"
			:paramsInfo="paramsInfo"
			:ifUpload="true"
			:dataObj="dataObj"
			:upLoadBtn="upLoadBtn"
			@close="xptImageClose">
		</xpt-image-v2>
	</div>
	
</template>
<script>
	import detailComponent from '@components/dealer/account_detail.vue';
	import validate from '@common/validate.js';
	import Fn from '@common/Fn.js';
	export default {
		props:["params"],
		data() {
			var _this = this;
			var self = this;
			return{
				upLoadBtn: null,
				firstTab:"orderInfo",
				isShow:false,
				isGetImgList:false,
				dataObj: {},
				paramsInfo: {},
				bill_type_id_tag: '',
				provinceObj:{},
				cityObj:{},
				areaObj:{},
				streetObj:{},
				otherCols: [
				[
					{
						label: '创建人:',
						key: 'creator_name'
					}, {
						label: '创建日期:',
						key: 'create_time',
						format: 'dataFormat1'
					}, {
						label: '锁定人:',
						key: 'locker_name'
					}
				],[
					 {
						label: '审核人:',
						key: 'auditor_name'
					}, {
						label: '审核日期:',
						key: 'audit_time',
						format: 'dataFormat1'
					},{
						label: '作废人:',
						key: 'invalid_name'
					},
				], [
					{
						label: '驳回原因:',
						key: 'reject_reason'
					}

					// }, {
					// 	label: '财务中台收款同步信息:',
					// 	key: 'synStatus',
					// 	formatter(val) {
					// 		return fn.synStatus()[val];
					// 	}
				],
			],
				order_status_option:[
						{
							value: 'CREATE',
							label: '创建'
						},
						{
							value: 'SUBMITTED',
							label: '提交审核'
						},
						{
							value: 'APPROVED',
							label: '已审核'
						},
						{
							value: 'RETRIAL',
							label: '重新审核'
						},
						{
							value: 'CANCELED',
							label: '已作废'
						},
						{
							value: 'INVALID',
							label: '已作废'
						},
						{
							value: 'WITHDRAWED',
							label: '已撤回'
						},
						{
							value: 'LOCKED',
							label: '锁定'
						},

				],
				submitData:{
				"id": '',
				"order_no": "",
				"order_status": "CREATE",
				"status": "",
				"shop_id":"" ,
				"shop_name": "",
				"shop_type": "",
				"dealer_id":"" ,
				"dealer_name": "",
				"account_type": "",
				"payment_mode": "BANK_TRANSFER_A",
				"recharge_account": "",
				"account_name": "",
				"account_bank": "",
				"remark": "",
				"isAdd": true  

				},
				account_type_options:[
					{
							value: 'private',
							label: '对私账号'
						}, {
							value: 'public',
							label: '对公账号'
						}
				],
				// 送货方式
				deliver_options: [
						{
							value: 'THREES',
							label: '三包'
						}, {
							value: 'LOGISTICS',
							label: '物流'
						}
					],
					post_fee_type_Options:[
						{
							value:'NOW_PAY',
							label:'现付'
						},{
							value:'ARRIVE_PAY',
							label:'到付'
						}
					],
				rules:{
					transit_store_name:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入中转仓名称",
						trigger:"change"
					}),
					transit_linkman:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入联系人",
						trigger:"change"
					}),
					linkman_mobile:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入联系人手机",
						trigger:"change"
					}),
					address:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入详细地址",
						trigger:"change"
					}),
					delivery_mode:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入运费类型",
						trigger:"change"
					}),
					freight_type:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入送货方式",
						trigger:"change"
					}),
					province:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					city:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					area:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					
				},
				province: {},//省所有选项
				city: {},//市
				area: {},//区
				addressDisabled:false,
				initialData:{},
				isLoading:false,
				businessTypeTradeDisabledOption: [],
				DDYWLX_AUX: __AUX.get('ddywlx').reduce((a, b) => {
					a[b.code] = b.tag
					return a
				}, {}),
				// business_type_trade_options:{
				// 	'COMMON':'普通订单',
				// 	'SAMPLE':'清样订单'
				// }
			}
		},
		methods : {
			xptImageClose (imgList){
			Object.defineProperties(this.submitData, {
			    __imgListLength: {
			        configurable: true,
			        writable: true,
			        enumerable: false,
			        value: imgList.length,
			    }
			})
			this.isShow = false
			this.isGetImgList = false
		},
		
			/**
		*查看附件事件
		*/
		viewOrUpload(row, rowIndex){
			this.viewOrUpload.rowIndex = 1
			this.isShow = true;
			this.isGetImgList = true;
			this.paramsInfo.parent_no = this.dataObj.parent_no = this.submitData.order_no;
			this.paramsInfo.child_no = this.dataObj.child_no = this.submitData.id;
			this.paramsInfo.cloud_file_id = null;
			this.dataObj.parent_name = 'ORDER';
			this.dataObj.child_name = 'DEALERFUNDSMANAGE';
			// this.dataObj.content = JSON.parse(JSON.stringify(row));
			console.log(this.paramsInfo,this.dataObj)
		},
		/**选择店铺列表**/
		selectShop(){
			console.log('----------------111')
			if(this.submitData.order_status =='RETRIAL'||this.submitData.order_status =='WITHDRAWED'){
				return false;
			}
			this.$root.eventHandle.$emit('alert', {
				params: {
					selection: 'radio',
					shop_status: 'OPEN',
					callback: data => {
						this.submitData.shop_id = data.shop_id;
						this.submitData.shop_name = data.shop_name;
						this.submitData.dealer_name = data.customer_source_name;
						this.submitData.dealer_id = data.customer_source_id;
						this.submitData.shop_type = data.shop_type;
						
					},
				},
				component: ()=>import('@components/shop/list'),
				style: 'width:800px;height:500px',
				title: '店铺列表',
			})
		},
		retrialFunc(url){
			let self = this;
			this.$root.eventHandle.$emit('alert',{
				params: {
					callback: data => {
						
						// this.actionById(url, { reject_reason : data.data })
						this.ajax.postStream(
							'/dealer-web/api/dealerRechargeAccount/' + url,
							{ reject_reason : data.data ,id:this.submitData.id},res => {
								this.isLoading = false;
								if(res.body.result){
									this.$message.success(res.body.msg)
									// this.isLoading = false;
									self.getDetail({id:self.submitData.id});
								}else {
									this.$message.error(res.body.msg)
									// this.isLoading = false;

								}
							}
						)
					}
				},
				component:()=>import('@components/receipt/reject_receipt'),
				style:'width:600px;height:200px',
				title:'请输入驳回理由'
			})
		},
			

			
			preSave(formName){
				var self = this;
				// if(this.submitData.bill_type_id=='PRESENT' && !this.submitData.arm_mager_id){
				// 	this.$message.error('赠品订单请选择合并订单号');
				// 	return;
				// }	
				self.$refs[formName].validate((valid) => {
					if(!valid) return

					this.isLoading=true
					self.save()
				})
			},
			save(callback){	
				var self = this,
				params={id:self.submitData.id};
				var url = "/dealer-web/api/dealerRechargeAccount/saveOrUpdate?permissionCode=DEALER_RECHARGE_ACCOUNT_SAVE";
				
				let saveData = JSON.parse(JSON.stringify(this.submitData));
				//delete saveData.merge_trade_no;
				//delete saveData.groupName;
				this.ajax.postStream(url,/*self.submitData*/saveData,(response)=>{
						this.isLoading = false;

					if(response.body.result){
						this.$message({message: '操作成功',type: 'success'})
						// params.sys_trade_id = response.body.content.sys_trade_id
						params.__close = function(){
							self.$root.eventHandle.$emit('removeTab',self.params.tabName);
						}
						// self.getDetail({id:response.body.content});
						this.$root.eventHandle.$emit('updateTab',{
							name:'经销商账号详情',
							params:params,
							title:'经销商账号详情',
							component:()=>import('@components/dealer/account_detail'),
						})
						this.$root.eventHandle.$emit('refresh_list')
						typeof(callback)=='function'&&callback()
					}else{
						this.$message.error(response.body.msg)
					}
					this.isLoading=false
				},e=>{
					this.isLoading=false
					this.$message.error(e)
				})
			},
			// 关闭标签页
			closeTab(){
				var self = this,isUpdate;
				self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				return false;
				// console.log(self.submitData,self.initialData)
				// isUpdate = this.compareData(self.submitData,self.initialData);

				// if(isUpdate){
				// 	self.$root.eventHandle.$emit('openDialog',{
				// 		ok(){
				// 			self.save(()=>{
				// 				self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				// 			})
				// 		},
				// 		no(){
				// 			self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				// 		}
				// 	})
				// }else{
				// 	self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				// }
			},
			actionById (apiName){
				let self = this;
				
				this.ajax.postStream(
					'/dealer-web/api/dealerRechargeAccount/' + apiName,
					{id:this.submitData.id},
					// postData ? Object.assign({ id: this.submitData.id }, postData) : (this.submitData.id),
					// params,
					res => {
						this.isLoading = false;

						if(res.body.result){
						
							this.$message.success(res.body.msg)
							// this.isLoading = false;
							self.getDetail({id:self.submitData.id});
						}else {
							this.$message.error(res.body.msg)
							// this.isLoading = false;

						}
					}
				)
		},
			getDetail:function(params){
				var _this = this;
				if(!params){
					return false;
				}
				var url = '/dealer-web/api/dealerRechargeAccount/listDetail';

				this.ajax.postStream(url,/*self.submitData*/{id:params.id},(response)=>{
					console.log(response);
					if(response.body.content){
						_this.submitData = response.body.content;
						// _this.submitData.create_time = Fn.dateFormat(_this.submitData.create_time);
						// _this.submitData.last_modify_time = Fn.dateFormat(_this.submitData.last_modify_time);
						
					}else{
						this.$message.error(response.body.msg)
					}
					// this.isLoading=false
				},e=>{
					this.isLoading=false
					this.$message.error(e)
				})
			},
			getOrderNo(){
				let self = this;
				this.ajax.postStream('/dealer-web/api/dealerRechargeAccount/getOrderNo',/*self.submitData*/{},(response)=>{
					console.log(response);
					if(response.body.content){
						self.submitData.order_no = response.body.content;
						// _this.submitData.create_time = Fn.dateFormat(_this.submitData.create_time);
						// _this.submitData.last_modify_time = Fn.dateFormat(_this.submitData.last_modify_time);
						
					}else{
						this.$message.error(response.body.msg)
					}
					// this.isLoading=false
				})
			
			},
			getDealerRechargeAccountId(){
				let self = this;
				this.ajax.postStream('/dealer-web/api/dealerRechargeAccount/getDealerRechargeAccountId',/*self.submitData*/{},(response)=>{
					console.log(response);
					if(response.body.content){
						self.submitData.id = response.body.content;
						// _this.submitData.create_time = Fn.dateFormat(_this.submitData.create_time);
						// _this.submitData.last_modify_time = Fn.dateFormat(_this.submitData.last_modify_time);
						
					}else{
						this.$message.error(response.body.msg)
					}
					// this.isLoading=false
				})
			},
			getChangeStatus(){
				// CREATE-创建 WITHDRAWED-已撤回 RETRIAL-重新审核 允许上传， SUBMITTED-提交审核  APPROVED-已审核  CANCELED/INVALID-已作废  LOCKED-锁定 不允许上传
				if (this.submitData.order_status == 'APPROVED' || this.submitData.order_status == 'SUBMITTED' || this.submitData.order_status == 'CANCELED' || this.submitData.order_status == 'INVALID' || this.submitData.order_status == 'LOCKED' ) {
					this.upLoadBtn = true
				} else {
					this.upLoadBtn =  false
				}
				return this.submitData.order_status =='SUBMITTED' || this.submitData.order_status =='APPROVED' || this.submitData.order_status =='CANCELED'|| this.submitData.order_status =='LOCKED'|| this.submitData.order_status =='INVALID';
			},
			
			
			
		},
		mounted:function(){
			var self = this;
			console.log(self.params)
			if(!self.params.id){
				this.getOrderNo();
				this.getDealerRechargeAccountId();
				this.submitData.isAdd = true;
				self.submitData.shop_id = self.params.shop_id;
				self.submitData.shop_name = self.params.shop_name;
				self.submitData.dealer_id=  self.params.customer_source_id;
				self.submitData.dealer_name= self.params.customer_source_name;
			}else{
				this.getDetail(self.params)
			}
			self.params.__close = self.closeTab
		}
	}
</script>
<style module>
.row-height :global(.el-form-item__content) {
	height: auto!important;
	white-space: nowrap;
}
.el-form-item__content .detail-address{
	width:300px;
}
</style>