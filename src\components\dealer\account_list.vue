<!--经销商账号列表-->
<template>
	<xpt-list 
		:data='resData' 
		:btns='btns' 
		:colData='cols' 
		:selection='selection'  
		@radio-change='radioChange' 
		@row-dblclick='rowDblclick'  
		ref='customersList'
	></xpt-list>
</template>
<script>
export default {
	props:["params"],
	data(){
		let self = this
		return {
			resData:'',
			showSearch:false,
			
			customersList:[],
			actions_value:"",
			count:0,
			pageNow:1,
			multipleSelection: [],
			// 单选选中的行
			returnObj:"",
			selectId:"",
			searchName:'',

			selection: 'radio',
			btns: [
				{
					type: 'success',
					txt: '确定',
					loading: false,
					click() {
						self.submit()
					}
				}
			],
			cols:[
				{
					label: '支付方式',
					prop: 'payment_mode',
					format:'auxFormat',
					formatParams: 'payType'
				},{
					label: '充值账号',
					prop: 'recharge_account',
				},{
					label: '开户名',
					prop: 'account_name',
				},{
					label: '开户行',
					prop: 'account_bank',
				},{
					label: '营业执照公司名',
					prop: 'company_name',
				},{
					label: '营业执照法人',
					prop: 'artificial_person',
				},{
					label: '状态',
					prop: 'status',
					formatter(val){
						switch(val){
							case '1': return '生效';
							case '2': return '失效';
						}
					}
				},{
					label: '失效人',
					prop: 'lose_efficacy_name',
				},{
					label: '失效时间',
					prop: 'lose_efficacy_time',
					format:'dataFormat1'
				},{
					label: '店铺ID',
					prop: 'shop_id',
				},
			],
			// cols: [
			// 	{
			// 		label: '客户编码',
			// 		prop: 'number',
			// 		redirectClick(row) {
			// 			self.viewDetail(row.cust_id)
			// 		}
			// 	}, {
			// 		label: '客户名称',
			// 		prop: 'name'
			// 	}, {
			// 		label: '失效状态',
			// 		prop: 'effective_status',
			// 		format: 'customerStatusFilter'
			// 	}, {
			// 		label: '类型',
			// 		prop: 'member_type',
			// 		format: 'auxFormat',
			// 		formatParams: 'customerSource',
			// 		// format: 'customerTypeFilter'
			// 	}, {
			// 		label: '创建人',
			// 		prop: 'creator_name'
			// 	}, {
			// 		label: '创建时间',
			// 		prop: 'create_date_str'
			// 	}
			// ]
		}
	},
	methods:{
		preValid(api){
			var _this = this;
			var url = "/order-web/api/customer"+api;
			// 事件前验证
			if(_this.multipleSelection.length==0){
				_this.$alert('没有选择任何数据，请先选择数据！', '提示', {
					confirmButtonText: '确定'
				})
			}else{
				var custIdList = [];
				_this.multipleSelection.forEach(function(item,index,array){
					custIdList.push(item.cust_id);
				});

				this.ajax.postStream(url,custIdList,function(response){
					if(response.body.result){
						_this.searching();
						_this.$message({
									message: '操作成功',
										type: 'success'
								});
						// 重置业务操作
						_this.actions_value = "";
					}
				});
			}
		},
		openSearch(){
			this.showSearch = !this.showSearch
		},
		radioChange(obj) {
			this.returnObj = obj
		},
		rowDblclick(obj) {
			if(this.params.isAlert) {
				this.params.close(obj)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
		},
		submit(){
			this.params.close(this.returnObj)
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		}
	},
	mounted: function(){
		var _this = this;
		this.resData = this.params.resData
	},
	destroyed(){
		this.$root.offEvents('close_addCustomer');
	}
}
</script>