<!-- 新增经销网拍居间服务费对账单-->
<template>
  <div>
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button type="primary" size="mini" @click="preSave('submitData')" :loading="isLoading" :disabled="isLoading">
          确定</el-button>
        <el-button size="mini" @click="cancel">取消</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-form label-position="right" class="mgt10" label-width="100px" :model="submitData" :rules="rules"
        ref="submitData">
        <el-col :span="18">
          <el-form-item label="店铺编码" prop="shop_code">
            <el-input size='mini' :rows="2" placeholder="请输入内容" v-model="submitData.shop_code">
            </el-input>
            <el-tooltip v-if="rules.shop_code[0].isShow" class="item" effect="dark"
              :content="rules.shop_code[0].message" placement="right-start" popper-class="xpt-form__error">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="对账期间" prop="reconcile_date">
            <el-date-picker :rows="2" type="month" size="mini" placeholder="请输入内容" v-model="submitData.reconcile_date" format="yyyy-MM">
            </el-date-picker>
            <el-tooltip v-if="rules.reconcile_date[0].isShow" class="item" effect="dark"
              :content="rules.reconcile_date[0].message" placement="right-start" popper-class="xpt-form__error">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>
<script>
  import validate from "@common/validate.js";
  import Fn from "@common/Fn.js";
  export default {
    props: ["params"],
    data() {
      var self = this;
      return {
        submitData: {
          // order_id: null,
          shop_code: "", //店铺名称
          reconcile_date: null, //对账期间
        },
        rules: {
          reconcile_date: validate.isNotBlank({
            required: true,
            self: self,
            msg: "请输入对账期间",
            trigger: "blur",
          }),
          shop_code: validate.isNotBlank({
            required: true,
            self: self,
            msg: "请输入店铺名称",
            trigger: "blur",
          }),
        },
        isLoading: false,
      };
    },
    methods: {
      preSave(formName) {
        var self = this;
        self.$refs[formName].validate((valid) => {
          if (!valid) return;
          this.isLoading = true;
          self.save();
        });
      },
      save(callback) {
        var self = this,
          params = {};
        var url = "/order-web/api/cloudWpOrder/create?permissionCode=WP_SERVICE_ORDER_ADD";
        let dateOfYear = new Date(this.submitData.reconcile_date).getFullYear()
        let dateOfMonth = new Date(this.submitData.reconcile_date).getMonth() < 9 ? ('0' + (new Date(this.submitData.reconcile_date).getMonth()+Number(1))) : new Date(this.submitData.reconcile_date).getMonth()+Number(1)
        let dateTime = `${dateOfYear}-${dateOfMonth}`
        this.submitData.reconcile_date = dateTime
        let saveData = JSON.parse(JSON.stringify(this.submitData));
        this.ajax.postStream(
          url,
          saveData,
          (response) => {
            if (response.body.result) {
              this.$message({
                message: "操作成功",
                type: "success"
              });
              this.params.callback();
              this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
              this.$root.eventHandle.$emit('creatTab', {
                name: "居间服务费对账详情",
                params: {
                  shop_code: saveData.shop_code,
                  reconcile_date: saveData.reconcile_date,
                  service_order_id:response.body.content
                },
                component: () => import('@components/dealer/serviceBillListDetail'),
              })
            } else {
              this.$message.error(response.body.msg);
            }
            this.isLoading = false;
          },
          (e) => {
            this.isLoading = false;
            this.$message.error(e);
          }
        );
      },
      cancel() {
        this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
      },
    },
    mounted: function () {
      var self = this;
      self.params.__close = self.closeTab;
    },
  };

</script>
