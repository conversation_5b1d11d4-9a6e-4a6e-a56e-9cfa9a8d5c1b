<template>
	<div class="mgt10"> 
		<el-form label-position="right" label-width="95px" :model="submitData" :rules="rules" ref="submitData">
			<el-row	:gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="支付方式" prop="payment_mode">
						<!-- <el-input size='mini' v-model="submitData.payment_mode" ></el-input> -->
						<xpt-select-aux v-model='submitData.payment_mode' aux_name='payTypeDealer'></xpt-select-aux>
						<el-tooltip v-if='rules.payment_mode[0].isShow' class="item" effect="dark" :content="rules.payment_mode[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="充值账号" prop="recharge_account">
						<el-input size='mini' v-model="submitData.recharge_account"></el-input>
						<el-tooltip v-if='rules.recharge_account[0].isShow' class="item" effect="dark" :content="rules.recharge_account[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="开户名" prop="account_name">
						<el-input size='mini' v-model="submitData.account_name" ></el-input>
						<el-tooltip v-if='rules.account_name[0].isShow' class="item" effect="dark" :content="rules.account_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="40" class="mgt10">
				<el-col :span='8'>
					<el-form-item label="开户行" prop="account_bank">
						<el-input size='mini' v-model="submitData.account_bank" ></el-input>
						<el-tooltip v-if='rules.account_bank[0].isShow' class="item" effect="dark" :content="rules.account_bank[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col> 
				<el-col :span='8'>
					<el-form-item label="执照公司名" prop="company_name">
						<el-input size='mini' v-model="submitData.company_name"></el-input>
						<el-tooltip v-if='rules.company_name[0].isShow' class="item" effect="dark" :content="rules.company_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			
			<el-row :gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="营业执照法人" prop="artificial_person">
						<el-input size='mini' v-model="submitData.artificial_person"></el-input>
						<el-tooltip v-if='rules.artificial_person[0].isShow' class="item" effect="dark" :content="rules.artificial_person[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="是否生效" prop="status">
						<el-switch
							v-model="submitData.status"
							active-text="生效"
							inactive-text="失效">
						</el-switch>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter='40' class="mgt10">
				<el-col :span="24" style="text-align:right;">
					<el-button type='success' size='mini' @click="preSave('submitData')">保存</el-button >
					<el-button type='warning' size='mini' @click="closeWindow">取消</el-button>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
import validate from '@common/validate.js'
export default {
	props:["params"],
	data(){
		var self = this;
		return {
			provinceObj:{},
			cityObj:{},
			areaObj:{},
			streetObj:{},
			submitData:{
				payment_mode:'',
				recharge_account:'',
				account_name:'',
				account_bank:'',
				company_name:'',
				artificial_person:'',
				status:''
			},
			
			rules:{
				payment_mode:validate.isNotBlank({
					required:true,
					self:self,
					trigger:"change"
				}),
				recharge_account:validate.isNotBlank({
					required:true,
					self:self,
					trigger:"change"
				}),
				account_name:validate.isNotBlank({
					required:true,
					self:self,
					trigger:"change"
				}),
				account_bank:validate.isNotBlank({
					required:true,
					self:self,
					trigger:"change"
				}),
				company_name:validate.isNotBlank({
					required:true,
					self:self,
					trigger:"change"
				}),
				artificial_person:validate.isNotBlank({
					required:true,
					self:self,
					trigger:"change"
				}),
				shop_id:validate.isNotBlank({
					required:true,
					self:self,
					trigger:"change"
				}),
			}
		}
	},
	methods:{
		closeWindow(){
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		
		
		/*
		添加地址做保存时校验不允许收货人存在先生小姐等字符。
		*/
		preSave(formName){
			var self = this
			// self.save();

			self.$refs[formName].validate((valid) => {
				if(!valid) return
				self.save()
			});
		},
		save(){
			var self = this;
			self.submitData.status = self.submitData.status == true ? '1' : '2'
			self.params.callback(self.submitData)
			self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
		}
	},
	mounted:function(){
		var self = this;
	},
	destroyed(){
	}
}
</script>