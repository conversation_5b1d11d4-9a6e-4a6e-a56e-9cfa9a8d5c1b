<template>
<div class='xpt-flex'>
	<xpt-headbar>
		<el-button type='primary' slot='left' size='mini' @click="save">保存</el-button>
		
	</xpt-headbar>
  <el-form label-width='100px' :model='query'  ref="query">
    <el-row :gutter='40' style='height:50px'>
      <el-col :span='40'  class="xpt_col">
        <el-form-item label='失效时间' prop='number'>
            <el-date-picker v-model="query.number" type="date" placeholder="选择日期"  size='mini'></el-date-picker>
        </el-form-item>
        
      </el-col>
    </el-row>
  </el-form>
</div>
</template>
<script>
  export default {
    data() {
      let self = this;
      return {
        oldGoodsList: [],
        query:{
          number:'',
        },
        // hasData:self.params.sortList.length,
        // canEditData:self.params.canChangeList.length
      }
    },
	  props:['params'],
    methods: {
      close(){
        let self = this;
        self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
      },
      save(){
        if(!this.query.number){
          this.$message.error('失效时间不能为空');
          return false;
        }
        this.query.number = new Date(this.query.number).getTime()+24*60*60*1000 - 1000
        this.params.callback(this.query.number);
        this.close();
      },
     
     
    },
    mounted(){
      // this.getOldGoodsList();
    }
  }
</script>
<style>
	.repo-input{
		width:70px;
    float: right;
    /* margin-right: 50px; */
	}
  .xpt_col{
    margin-bottom: 10px;
  }
</style>
