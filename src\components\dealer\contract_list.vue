<!--居间服务合同列表-->
<template>
  <div class='xpt-flex'>
    
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :model='query' label-position="right" label-width="120px">
        <el-col :span='5'>
          <el-form-item label="店铺名称：" prop="shop_name">
              <xpt-input size='mini' v-model="query.shop_name" readonly icon='search' :on-icon-click="selectShop" @change='shopChange'></xpt-input>
          </el-form-item>
         
        </el-col>
      
        <el-col :span="8" class='xpt-align__right'>
          <el-button type='info' size='mini' @click='getList()'>搜索</el-button>
          <!-- <el-button type='primary' size='mini' @click='shopChange'>重置查询条件</el-button><br> -->
          <xpt-upload-v3 uploadBtnText="导入" :uploadSize="20" acceptTypeStr=".xlsx,.xls" :dataObj="uploadDataObj"
            :disabled="false" :ifMultiple="false" :showSuccessMsg="false" @uploadSuccess="uploadSuccess" btnType="success"
            style="display: inline-block; margin: 0px 10px">
          </xpt-upload-v3>
          <el-button type='info' size='mini' @click='downloadExcel()'>查看导入结果</el-button>
        </el-col>
      </el-form>
    </el-row>
    <count-list 
      :data='contractList' 
      :btns='btns' 
      :colData='cols' 
      :pageTotal='count'
      :selection='selection'
      :showHead="false"
      @search-click='searchData' 
      searchHolder='请输入店铺名称'  
      @selection-change='selectionChange'
      @page-size-change='pageSizeChange' 
      @current-page-change='pageChange'
      countUrl="/order-web/api/cloudWpContract/count"
    	:showCount ='showCount'
      id="multiple-rows-table">
      
    </count-list>
  </div>
</template>
<script>
import countList from '@components/common/list-count' 
  export default {
    components:{countList},
    data() {
      let self = this
      return {
        query:{
          shop_code:'',
          shop_name:'',
        },
        btns: [{
            type: 'primary',
            txt: '刷新',
            click: () => this.getList(),
          },
          {
            type: 'primary',
            txt: '查看导入结果',
            click: () => this.downloadExcel(),
          },
         
        ],
        cols: [{
            label: '店铺编码	',
            prop: 'shop_code',
            width: 120,
          }, {
            label: '店铺名称',
            prop: 'shop_name',
            width: 220,
          },{
            label: '客户名称',
            prop: 'customer_source_name',
            width: 120,
          }, {
            label: '合同编码',
            prop: 'contract_no',
            width: 120,
          }, 
          {
            label: '创建人',
            width: 100,
            prop: 'creator_name'
          }, {
            label: '创建日期',
            width: 120,
            prop: 'create_time',
            format: 'dataFormat1'
          }, {
            label: '修改人',
            width: 100,
            prop: 'modifier_name'
          }, {
            label: '修改时间',
            width: 120,
            prop: 'modify_time',
            format: 'dataFormat1'
          },
        ],
        search: {
          shop_code:'',
          page_size: self.pageSize,
          page_no: 1,
        },
        contractList: [],
        selectData: [],
        count: 0,
        selection: 'checkbox',
        uploadDataObj: {
          parent_name: "经销网拍居间服务费对账单列表",
          parent_no: "WP_SERVICE_ORDER_LIST_IMPORT", //主要通过该参数获取附件列表
          child_name: null,
          child_no: null,
          content: {},
        },
        showCount:false,
      }
    },
    props: ['params'],
    methods: {
      selectShop(){
                let self = this
                let params = {
                    selection: 'radio',
                    callback(d){
                        self.query.shop_code = d.shop_code;
                        self.query.shop_name = d.shop_name;
                    }
                }
                self.$root.eventHandle.$emit('alert',{
                    params:params,
                    component:()=>import('@components/shop/list'),
                        style:'width:800px;height:500px',
                        title:'店铺列表'
                })
      },
      shopChange(val) { 
          if(!val) {
              this.query.shop_code = '';
              this.query.shop_name = '';
          }
      },
      selectionChange(data) {
        this.selectData = data.filter(item => !item.concat_type)
      },
      searchData(obj, resolve) {
        new Promise((res,rej)=>{
          this.getList(resolve, res);
        }).then(()=>{
          if(this.search.page_no != 1){
            this.count = 0;
          }
          this.showCount = false;
        })
      },
      pageSizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page_no = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve,callback) {
        var postData = JSON.parse(JSON.stringify(this.search))
        postData.shop_code = this.query.shop_code;
        // if (this.params.setWhere) {
        //   this.params.setWhere(postData) //在setWhere方法里面直接修改postData对象内容
        // }
        this.ajax.postStream('/order-web/api/cloudWpContract/list', postData, d => {
          callback && callback();
          if (d.body.result && d.body.content) {
            if(!this.showCount){
              let total = this.search.page_size * this.search.page_no;
              this.count = d.body.content.length == (this.search.page_size+1)? total+1:total;
            }
            this.contractList = d.body.content;
          } else {
            this.$message.error(d.body.msg || '')
          }
          resolve && resolve();
        }, err => {
          resolve && resolve();
          this.$message.error(err);
        })
      },
      
      //上传成功返回结果
      uploadSuccess(result) {
        if (result.length > 0 && !!result[0].path) {
          this.importFileUrl(result[0].path);
        }
      },
      //导入
      importFileUrl(fileUrl) {
        let params = {
          file_path: fileUrl,
        };
        this.ajax.postStream(
          "/order-web/api/cloudWpContract/import",
          params,
          (res) => {
            if (res.body.result) {
              this.$message.success(res.body.content);
              this.getList()
            } else {
              this.$message.error(res.body.content);
            }
          },
          (err) => {
            this.$message.error(err);
          }
        );
      },
      // 导出
      exportExcel() {
        this.ajax.postStream(
          "/order-web/api/cloudWpOrder/addExport?permissionCode=WP_SERVICE_ORDER_EXPORT", this.search,
          (res) => {
            if (res.body.result) {
              res.body.msg && this.$message.success(res.body.msg);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          },
          (err) => {
            this.$message.error(err);
          }
        );
      },
      // 查看导入结果
      downloadExcel() {
        this.$root.eventHandle.$emit('alert', {
          params: {
            obj: {
              excel_type: "EXCEL_TYPE_WP_SERVICE_CONTRACT_IMPORT"
            },
            url: "/price-web/api/price/import/list"
          },
          component: () => import("@components/dealer/import_result.vue"),
          style: 'width:800px;height:400px',
          title: '导入结果'
        });
      },
      withhloding(){
         let self = this;
         if(self.selectData.length === 0 ){
           self.$message.error('请选择要处理的数据');
           return;
         }
          this.$confirm('确定预提'+(self.selectData.length+1)+'张对账单？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
                this.ajax.postStream("/order-web/api/cloudWpOrder/withhloding", {
                  order_id_list: this.selectData.map(item => {
                    return item.service_order_id
                  })
                }, d => {
                  if (d.body.result) {
                    this.$message.success(d.body.msg || '')
                    this.getList()
                  } else {
                    this.$message.error(d.body.msg || '')
                  }
                }, err => {
                  this.$message.error(err);
                })
            });
         
      },
      // 查看导出结果
      exportList() {
        this.$root.eventHandle.$emit('alert', {
          params: {
            query: {
              type: 'EXCEL_TYPE_WP_SERVICE_ORDER_EXPORT',
            }
          },
          component: () => import("@components/after_sales_report/export"),
          style: 'width:800px;height:400px',
          title: '导出结果'
        });
      },
      // 新增
      addEvent() {
        let self = this
        self.$root.eventHandle.$emit('alert', {
          title: "新增单据",
          params: {
            callback: self.getList
          },
          style: 'width:600px;height:300px',
          component: () => import('@components/dealer/add')
        })
      },
      // 详情页面
      serviceBillListDetail(row) {
        this.$root.eventHandle.$emit('creatTab', {
          name: "居间服务费对账详情",
          params: {
            shop_code: row.shop_code,
            reconcile_date: row.reconcile_date,
            service_order_id: row.service_order_id
          },
          component: () => import('@components/dealer/serviceBillListDetail'),
        })
      },
      // 批量提交 批量审核 批量撤销
      handleEvent(status) {
        if (this.selectData.length < 1) {
          return this.$message.error("请至少勾选一条数据")
        }
        let flag = true
        let order_status, msg, url
        // 状态为创建CREATED时可提交
        if (status === "submit") {
          order_status = "CREATED"
          msg = "'创建'状态的对账单才能提交"
          url = "/order-web/api/cloudWpOrder/submit?permissionCode=WP_SERVICE_ORDER_SUBMIT"
        }
        // 状态为经销审核DEALER_CHECK时可审核
        if (status === "audit") {
          order_status = "DEALER_CHECK"
          msg = "'经销核对'状态的对账单才能审核"
          url = "/order-web/api/cloudWpOrder/audit?permissionCode=WP_SERVICE_ORDER_AUDIT"
        }
        let ids = {
          order_id_list: this.selectData.map(item => {
            if (item.order_status !== order_status) {
              flag = false
            }
            return item.service_order_id
          })
        }
        if (!flag) {
          return this.$message.error(msg)
        }
        this.ajax.postStream(url, ids, d => {
          if (d.body.result) {
            this.$message.success(d.body.msg || '')
            this.getList()
          } else {
            this.$message.error(d.body.msg || '')
          }
        }, err => {
          this.$message.error(err);
        })
      },
      //撤销单独领出来
      revokeEvent() {
        let flag = false
        for (var i = 0; i < this.selectData.length; i++) {
          if (this.selectData[i].order_status === "DEALER_CHECK" || this.selectData[i].order_status === "SUBMIT") {
            flag = true
          } else {
            flag = false
          }
        }
        if (!flag) {
          return this.$message.error("经销核对或者提交'状态的对账单才能撤销")
        }
        this.ajax.postStream("/order-web/api/cloudWpOrder/revoke?permissionCode=WP_SERVICE_ORDER_REVOKE", {
          order_id_list: this.selectData.map(item => {
            return item.service_order_id
          })
        }, d => {
          if (d.body.result) {
            this.$message.success(d.body.msg || '')
            this.getList()
          } else {
            this.$message.error(d.body.msg || '')
          }
        }, err => {
          this.$message.error(err);
        })
      },
    },
    mounted() {
      this.getList();
    }
  }

</script>
<style scoped>
.el-table .el-table__body-wrapper td .cell, .el-table .el-table__fixed-body-wrapper td .cell{
  height:auto !important;
}
#j_table /deep/.xpt-flex__bottom{
  background:red;
}
</style>
