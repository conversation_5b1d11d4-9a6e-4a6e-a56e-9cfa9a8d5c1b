<!--经销折扣商品详情-->
<template>
	<div class='xpt-flex'>

    <el-row class='xpt-top'>
			<el-button type='success'  size='mini' @click="init" :disabled="!submitData.discount_id">刷新</el-button>
			<el-button type='warning'  size='mini' @click="saveEvent" >保存</el-button>
			<el-button type='info'  size='mini' @click="publish" >发布</el-button>
		</el-row>
		<el-form label-position="right" label-width="120px" :model="submitData" ref="submitData">
			<el-tabs v-model="activeName"  >
				<el-tab-pane label="基本信息" name="first" class='xpt-flex'>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="版本号"  >
								<el-input v-model="submitData.discount_code"  size='mini'   disabled></el-input>
							</el-form-item>
              <el-form-item label="版本名称"  >
								<el-input v-model="submitData.discount_name" :disabled="!!submitData.discount_id"  size='mini'  ></el-input>
							</el-form-item>
              <el-form-item label="折扣类型"  >
                <el-select  size='mini'  placeholder="类型" :disabled="!!submitData.discount_id" v-model='submitData.discount_type'>
										<el-option label="指定商品折扣" value="MATERIAL"></el-option>
										<el-option label="商品类别折扣" value="GOOD"></el-option>
										<el-option label="商品类别正向结算折扣" value="FORWARD_GOOD"></el-option>
									</el-select>
								<!-- <el-input v-model="submitData.discount_type"  size='mini'  ></el-input> -->
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="开始时间">
									<el-date-picker v-model="submitData.begin_time" :disabled="submitData.status == 'PUBLISH'" type="date" :picker-options='startDateFormatFun' placeholder="选择日期"  size='mini'></el-date-picker>

                </el-form-item>
                <el-form-item label="结束时间">
									<el-date-picker v-model="submitData.end_time" :disabled="submitData.status == 'PUBLISH'" :picker-options='endDateFormatFun' type="date" value-format="yyyy-MM-dd 23:59:59" placeholder="选择日期"  size='mini'></el-date-picker>
                </el-form-item>
              </el-col>
            <el-col :span='8'>
                <el-form-item label="状态">
                  <!-- <el-input v-model="submitData.status"  size='mini'  ></el-input> -->
                 <el-select  size='mini'  placeholder="类型" v-model='submitData.status' disabled>
										<el-option label="创建" value="CREATE"></el-option>
										<el-option label="已发布" value="PUBLISH"></el-option>
									</el-select>
                </el-form-item>
                <el-form-item label="创建人">
                  <el-input v-model="submitData.creator_name"  size='mini' disabled></el-input>
                </el-form-item>
                <el-form-item label="创建时间">
									<el-date-picker v-model="submitData.create_time" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>
                </el-form-item>
						</el-col>

					</el-row>

					<el-row :gutter='40'>
						<el-col :span='16'>
							<el-form-item label="备注" style="height:60px">
								<el-input type="textarea" v-model="submitData.remark" :disabled="submitData.status == 'PUBLISH'" :maxlength="240" ></el-input>
							</el-form-item>
						</el-col>

					</el-row>

				</el-tab-pane>

			</el-tabs>
		</el-form>
		<div class='xpt-flex__bottom' >
			<el-tabs v-model="activeName">
		    	<el-tab-pane label="门店明细" name="first" class='xpt-flex'>
					<import-list
            ref="importList"
						:data='list'
						:discount_id='params.row.discount_id'
            :submit='submitData'
						orderNo
            selection=''
            :showHead="false"
						@selection-change='select'
					></import-list>
		    	</el-tab-pane>


		  	</el-tabs>
	  	</div>
	</div>
</template>
<script>
import validate from '@common/validate.js';
import Fn from '@common/Fn.js';
import importList from './sale_order_import.vue'
	export default {

    props:['params'],
    components: {
      importList,
    },
		data(){
      let self = this;
      return{
        startDateFormatFun:{
				  disabledDate: time => {
					/*新增时能选所有时间，编辑时选当天之后*/
				  	// return time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7
              return self.submitData.end_time ? time.getTime() > new Date(self.submitData.end_time ) ||time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7 : time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7;
				},
        date:(function(){
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth()+1;
            var day = date.getDate();
            var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
            return new Date(time);
      			})()

			},
        endDateFormatFun:{
          disabledDate: time => {
					/*新增时能选所有时间，编辑时选当天之后*/
				  	// return time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7
            return self.submitData.begin_time ? time.getTime() < new Date(self.submitData.begin_time) || time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7: time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7
				},
          date:(function(){
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth()+1;
            var day = date.getDate();
            var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
            return new Date(time);
      			})()
			},
        saveDisabled:false,
        activeName:'first',
        submit:{},
        submitData:{
          discount_id:null,
          discount_code:null,
          discount_name:null,
          discount_type:null,
          begin_time:null,
          end_time:null,
          status:'CREATE',
          remark:null,
        },
        listCols:[
          {
            label: '店铺名称',
            prop: 'shop_name',
            width: 130
          },
          {
            label: '区域经理',
            prop: 'regional_manager_name',
            width: 130
          },
          {
            label: '状态',
            prop: 'status',
            width: 130
          },
          {
            label: '错误信息',
            prop: 'error_info',
            width: 130
          },
          {
            label: '物料明细',
            prop: 'purchase_shop_id',
            width: 130
          },
          {
            label: '采购总价',
            prop: 'sum_purchase_submitData',
            width: 130
          },
          {
            label: '收货人姓名',
            prop: 'receiver_name',
            width: 130
          },
          {
            label: '收货人手机',
            prop: 'receiver_mobile',
            width: 130
          },
          {
            label: '省',
            prop: 'province_name',
            width: 130
          },
          {
            label: '市',
            prop: 'city_name',
            width: 130
          },
          {
            label: '区',
            prop: 'district_name',
            width: 130
          },
          {
            label: '街道',
            prop: 'street_name',
            width: 130
          },
          {
            label: '详细地址',
            prop: 'receiver_address',
            width: 130
          },
          {
            label: '送货方式',
            prop: 'deliver_method',
            width: 130
          },
          {
            label: '区域经理确认时间',
            prop: 'manager_confirm_time',
            width: 130
          },
          {
            label: '合并单号',
            prop: 'merge_trade_id',
            width: 130
          },
        ],
        list:[],

      }
    },
		methods:{

      publish(){
        let self = this;
        let data =JSON.parse(JSON.stringify(this.submitData))
        data.publish_id = self.getEmployeeInfo('id');
        data.publish_name = self.getEmployeeInfo('fullName');
         this.ajax.postStream('/price-web/api/cloudPurchase/publish?permissionCode=PURCHASE_DISCOUNT_PUBLISH', data, res => {
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
            if(res.body.result){
             self.init()
           }
            // $btn.disabled = false
          }, () => {
            // $btn.disabled = false
          })
      },
      saveEvent(){
        let self = this;
        if(!self.discount_id){
          self.submitData.creator = self.getEmployeeInfo('id');
          self.submitData.creator_name = self.getEmployeeInfo('fullName');
        }
        if(self.submitData.end_time){
          let date = new Date(self.submitData.end_time);
          var year = date.getFullYear();
          var month = date.getMonth()+1;
          var day = date.getDate();
          var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
          self.submitData.end_time = new Date(time).getTime()
        }

         this.ajax.postStream('/price-web/api/cloudPurchase/saveOrUpdate?permissionCode=PURCHASE_DISCOUNT', self.submitData, res => {
           this.submitData.discount_id = res.body.content;
          //  this.params.row.discount_id = res.body.content;
            self.$refs.importList.otherParams.discount_id  = res.body.content;
            self.$refs.importList.search.discount_id  = res.body.content;
           console.log(self.$refs.importList)
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
            if(res.body.result){
             self.init()
           }
            // $btn.disabled = false
          }, () => {
            // $btn.disabled = false
          })
      },
      select(){

      },
      init() {
          let self = this;
          self.ajax.get(
          "/price-web/api/cloudPurchase/getById/"+self.submitData.discount_id,

          res => {
            if (res.body.result) {
              //this.$message.success(res.body.msg);
              this.submitData = res.body.content;
            } else {
              this.$message.error(res.body.msg);
            }
          },
          err => {
            this.$message.error(err);
          }
        );
      },

			pageChange(page_size){
				this.storeDiscountParams.page_size = page_size
				this.getShopDiscountList()
			},
			currentPageChange(page){
				this.storeDiscountParams.page_no = page
				this.getShopDiscountList()
			},






		},
    computed:{
      publishDisabled() {
            return true;
            return this.submitData.creator !== this.getEmployeeInfo('id')
        }
    },

    beforeDestroy(){
         this.$root.eventHandle.$off('updateOrderStatus', this.init)
    },
		mounted(){
      // this.submit = this.params.row;
      if(this.params.row.discount_id){
        this.submitData.discount_id = this.params.row.discount_id
        this.init();
      }
        this.$root.eventHandle.$on('updateOrderStatus', this.init)
    }
	}
</script>
