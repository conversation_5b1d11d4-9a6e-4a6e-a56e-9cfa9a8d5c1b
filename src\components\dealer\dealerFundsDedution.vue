<!-- 贷款扣款详情 -->
<!--<template>
<div class="xpt-flex">
	<el-row>

	<el-tabs value="findDealerFundsChangeitem">
		<el-tab-pane label="基本信息" name="findDealerFundsChangeitem">
			<xpt-list
				:data='findDealerFundsChangeitemList'
				:colData='findDealerFundsChangeitemCol'
				selection='hidden'
				:btns="[]"
				:showHead="false"
			></xpt-list>
		</el-tab-pane>
	</el-tabs>
	</el-row>
	
	<el-row>
		<el-tabs v-model="selectTab2" @tab-click="selectTab2Click"  class="xpt-flex">
			<el-tab-pane label="扣款明细" name="findDealerFundsMergeChangePage">
				<xpt-list
					:data='findDealerFundsMergeChangePageList'
					:colData='findDealerFundsMergeChangePageCol'
					orderNo
					selection='hidden'
					:btns="findDealerFundsMergeChangePageBtns"
					:pageTotal='pageTotal.findDealerFundsMergeChangePage'
					:searchPage="searchParams.findDealerFundsMergeChangePage.page_name"
					@search-click='searchClick'
					@page-size-change='pageChange'
					@current-page-change='currentPageChange'
				></xpt-list>
			</el-tab-pane>
			<el-tab-pane label="扣款记录" name="findDealerFundsDedution">
				<xpt-list
					:data='findDealerFundsDedutionList'
					:colData='findDealerFundsDedutionCol'
					orderNo
					selection='hidden'
					:btns="findDealerFundsDedutionBtns"
					:pageTotal='pageTotal.findDealerFundsDedution'
					@page-size-change='pageChange'
					@current-page-change='currentPageChange'
				></xpt-list>
			</el-tab-pane>
		</el-tabs>
	</el-row>
</div>
</template>
-->
<template>
<div class="xpt-flex">
	<el-row>

	<el-tabs value="findDealerFundsChangeitem">
		<el-tab-pane label="基本信息" name="findDealerFundsChangeitem">
			<xpt-list
				:data='findDealerFundsChangeitemList'
				:colData='findDealerFundsChangeitemCol'
				selection='hidden'
				:btns="[]"
				:showHead="false"
			></xpt-list>
		</el-tab-pane>
	</el-tabs>
	</el-row>
	<el-tabs v-model="selectTab2" @tab-click="selectTab2Click" class="xpt-flex">
			<el-tab-pane label="扣款明细" name="findDealerFundsMergeChangePage">
				<xpt-list
					:data='findDealerFundsMergeChangePageList'
					:colData='findDealerFundsMergeChangePageCol'
					orderNo
					selection='hidden'
					:btns="findDealerFundsMergeChangePageBtns"
					:pageTotal='pageTotal.findDealerFundsMergeChangePage'
					:searchPage="searchParams.findDealerFundsMergeChangePage.page_name"
					@search-click='searchClick'
					@page-size-change='pageChange'
					@current-page-change='currentPageChange'
				></xpt-list>
			</el-tab-pane>
			<el-tab-pane label="扣款记录" name="findDealerFundsDedution">
				<xpt-list
					:data='findDealerFundsDedutionList'
					:colData='findDealerFundsDedutionCol'
					orderNo
					selection='hidden'
					:btns="findDealerFundsDedutionBtns"
					:pageTotal='pageTotal.findDealerFundsDedution'
					@page-size-change='pageChange'
					@current-page-change='currentPageChange'
				></xpt-list>
			</el-tab-pane>
			<el-tab-pane label="活动优惠" name="actionCoupon" class="xpt-flex">
					<xpt-list
						ref='coupon'
						:data='couponList'
						:btns='[]'
						:colData='couponCols'
						selection='hidden'
						orderNo
						>
					</xpt-list>
				</el-tab-pane>
	</el-tabs>
</div>	
</template>
<script>
export default {
	props: ['params'],
	data (){
		let self = this;
		return {
			couponList: [],
			couponBtns: [
				{
					type: 'warning',
					txt: '取消',
					disabled: () => {
						return (self.mergeInfo.business_type_trade === 'DEALER' && !(self.orderSelect.audit_status === 'N' || self.mergeInfo.change_status === 'EDIT'))||!self.ifHolding || !self.holdMergeEdit ||self.ifDataChange || !self.ifAddEdition||self.isResale
					},
					click: () => {
						self.couponCancel(false)
					}
				}, 
			],
			couponCols: [
				 {
                	label: '优惠活动编码',
                	prop: 'discount_no',
					width: 120
                }, {
					label: '优惠活动名称',
					prop: 'activity_name',
					redirectClick(row) {
						self.$root.eventHandle.$emit('alert', {
							params: {
								act_id: row.activity_id
							},
							component: () => import('@components/discount/view'),
							style: 'width:1000px;height:560px',
							title: '优惠详情'
						})
					},
					width: 190
				}, {
					label: '优惠金额',
					prop: 'discount',
					bool: true,
					elInput: true,
					disabled(row) {
						return true;
						
						
                    },
                    change(row) {
                        self.checkDiscountPrice(row)
                    },
				}, {
					label: '已使用金额',
					prop: 'share_discount',
					width: 80
				},{
					label: '经销结算比例',
					prop: 'dealer_discount',
					width: 180
				},{
					label: '经销商优惠分摊金额',
					prop: 'dealer_discount_share_amount',
					width: 180
				}, {
					label: '业务员',
					prop: 'creator_name'
				}, {
					label: '创建时间',
					prop: 'create_time_str',
					format: 'dataFormat'
				}, {
					label: '审核人',
					prop: 'auditor_name'
				}, {
					label: '审核时间',
					prop: 'audit_time_str',
				}, 
			
			],
			// 选中的优惠行
			couponSelect: '',
			couponSearchObj:{
				page_no:1,
				page_size:20,
				page_name:'scm_merge_discount',
				where: []
			},
			couponEntryId: null,
			goodTotalPrice: null, //商品行中标准售价
            ifCouponSelectChangeNum: null,
            onFinancialDialog: false,

			selectTab2: 'findDealerFundsMergeChangePage',
			findDealerFundsChangeitemList: [],
			findDealerFundsMergeChangePageList: [],
			findDealerFundsDedutionList: [],
			findDealerFundsChangeitemCol: [{//基本信息
				label: '合并单号',
				prop: 'mergeTradeNo',
				width: 200,
				redirectClick: d => this.openOrder(d.mergeTradeId),
			}, {
				label: '买家昵称',
				prop: 'cusName',
			}, {
				label: '未扣款金额',
				prop: 'unauditedAmount',
			}, {
				label: '扣款金额',
				prop: 'dealerDeductionAmount',
			}, {
				label: '冻结金额',
				prop: 'frozenAmount',
			}, {
				label: '已采购金额',
				prop: 'purchaseAmount',
			}],

			findDealerFundsDedutionBtns: [{//记录
				type: 'primary',
				txt: '导出',
				click: () => this.exportDealerFunds('exportDealerFundsDedutionRecord', this.searchParams.findDealerFundsDedution),
	        }, {
				type: 'primary',
				txt: '报表导出文件下载',
				click: () => this.downloadExportFile('EXCEL_TYPE_DEALER_DEDUTION_ITEM_RECORD'),
	        }],
	        findDealerFundsMergeChangePageBtns: [{//明细
				type: 'primary',
				txt: '导出',
				click: () => this.exportDealerFunds('exportDealerFundsDedution', this.searchParams.findDealerFundsMergeChangePage),
	        }, {
				type: 'primary',
				txt: '报表导出文件下载',
				click: () => this.downloadExportFile('EXCEL_TYPE_DEALER_DEDUTION_ITEM'),
	        }],

			findDealerFundsDedutionCol: [{//扣款记录
				label: '合并单号',
				prop: 'mergeTradeNo',
				width: 200,
				redirectClick: d => this.openOrder(d.mergeTradeId),
			}, {
				label: '买家昵称',
				prop: 'cusName',
			}, {
				label: '交易类型',
                prop: 'optionTypeName',
                formatter(val){
                    switch(val){
                        case 'DISCOUNT_AUDIT': return '批次订单反财审'; break;
                        default: return val; break; 
                    }
                }
			}, 
			// {
			// 	label: '货款余额',
			// 	prop: 'balanceAmount',
			// }, 
			{
				label: '变动金额',
				prop: 'changeAmount',
			}, {
				label: '货款冻结金额',
				prop: 'frozenAmount',
			}, {
				label: '操作人',
				prop: 'createName',
			}, {
				label: '扣减批次',
				prop: 'no',
			}, {
				label: '扣减时间',
				prop: 'createTime',
				format: 'dataFormat1',
	            width: 150,
			}],
			findDealerFundsMergeChangePageCol: [{//扣款明细
				label: '合并单号',
				prop: 'mergeTradeNo',
				width: 200,
				// redirectClick: d => this.openOrder(d.mergeTradeId),
			}, {
				label: '批次单号',
				prop: 'batchTradeNo',
				width: 200,
			}, {
				label: '物料编码',
				prop: 'materialNumber',
			}, {
				label: '物料名称',
				prop: 'materialName',
			}, {
				label: '规格描述',
				prop: 'materialSpecification',
			}, {
				label: '销售利润分类',
				prop: 'salesProfitClassName',
			}, {
				label: '行状态',
				prop: 'statusName',
			}, {
                label: '折扣类型',
                prop: 'discountType',
                formatter(val){
                    switch(val) {
                        case 'EXP': return '快递';
                        case 'COMMON': return '通用';
                        case 'XSP': return '家居';
                        default: return val;
                    }
                }
            },
			 {
				label: '发货时间',
				prop: 'outStockTime',
				format: 'dataFormat1',
	            width: 150,
			}, {
				label: '采购单号',
				prop: 'purchaseNo',
			}, {
				label: '实际售价',
				prop: 'actPrice',
			}, {
				label: '付返前实际售价',
                prop: 'beforeDiscountActPrice',
                width: 100
			}, {
				label: '经销折扣',
				prop: 'dealerDiscount',
			},  {
				label: '优惠分摊金额',
				prop: 'dealerDiscountShareAmount',
				format:'priceFilter',
	            width: 150,

			}, {
				label: '经销价',
				prop: 'dealerPrice',
			}, {
				label: '是否扣款',
				prop: 'ifDedution',
				formatter: prop => ({
					true: '是',
					false: '否'
				}[prop]),
			}],
			searchParams: {
				findDealerFundsMergeChangePage: {
					page_name: 'dealer_funds_merge_change',
					page_size: this.pageSize,
					page_no: 1,
					where: [],
					id: this.params.mergeTradeId,
				},
				findDealerFundsDedution: {
					page_size: this.pageSize,
					page_no: 1,
					id: this.params.mergeTradeId,
				},
			},
			pageTotal: {
				findDealerFundsMergeChangePage: 0,
				findDealerFundsDedution: 0,
			},
		}
	},
	methods: {
		clearSelection(){},
		getCouponList( resolve){
			let slef = this;
			this.couponSearchObj.merge_trade_id = this.params.mergeTradeId;
			this.ajax.postStream("/order-web/api/mergetrade/discount/listDealerDiscount", this.couponSearchObj, res => {
				if(res.body.result){
					if (res.body.content && res.body.content.length > 0) {
						slef.couponList = res.body.content;
                        
					} 
				} else {
					this.$message.error(res.body.msg);
				}
                resolve && resolve();
			}, err => {
                this.couponSelect = ''
                resolve && resolve();
            });
        },

		downloadExportFile (type){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/dealer_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type,
					},
				},
			})
		},
		exportDealerFunds (api, searchParams){
			this.ajax.postStream('/dealer-web/api/dealerFundsManageExport/' + api, searchParams, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		openOrder (id){
			this.$root.eventHandle.$emit('creatTab',{ 
				name: "合并订单详情",
				params: { merge_trade_id: id },
				component: () => import('@components/order/merge.vue')
			})
		},
		searchClick(obj, resolve){
			this.searchParams.findDealerFundsMergeChangePage.where = obj
			this.getFindDealerFundsMergeChangePage(resolve)
		},
		pageChange(page_size){
			this.searchParams[this.selectTab2].page_size = page_size
			if(this.selectTab2 === 'findDealerFundsMergeChangePage'){
				this.getFindDealerFundsMergeChangePage()
			}else {
				this.getFindDealerFundsDedution()
			}
		},
		currentPageChange(page){			
			this.searchParams[this.selectTab2].page_no = page
			if(this.selectTab2 === 'findDealerFundsMergeChangePage'){
				this.getFindDealerFundsMergeChangePage()
			}else {
				this.getFindDealerFundsDedution()
			}
		},

		selectTab2Click (){
			if(this.selectTab2 === 'findDealerFundsMergeChangePage'){
				this.getFindDealerFundsMergeChangePage()
			}else if(this.selectTab2 === 'actionCoupon'){
				this.getCouponList()
			}else{
				this.getFindDealerFundsDedution()
			}
		},
		getFindDealerFundsMergeChangePage (resolve){//扣款明细
			this.ajax.postStream('/dealer-web/api/dealerFundsManage/findDealerFundsMergeChangePage', this.searchParams.findDealerFundsMergeChangePage, res => {
				if(res.body.result){
					this.findDealerFundsMergeChangePageList = res.body.content.list || []
					this.pageTotal.findDealerFundsMergeChangePage = res.body.content.count || 0
				}else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve()
			}, () => {
				resolve && resolve()
			})
		},
		getFindDealerFundsChangeitem (){//基本信息
			this.ajax.postStream('/dealer-web/api/dealerFundsManage/getDealerFundsChangeItemVO', this.params.mergeTradeId, res => {
				if(res.body.result){
					this.findDealerFundsChangeitemList = [res.body.content]
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		getFindDealerFundsDedution (){//操作记录
			this.ajax.postStream('/dealer-web/api/dealerFundsManage/findDealerFundsDedution', this.searchParams.findDealerFundsDedution, res => {
				if(res.body.result){
					this.findDealerFundsDedutionList = res.body.content.list || []
					this.pageTotal.findDealerFundsDedution = res.body.content.count || 0
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
	},
	mounted (){
		this.getFindDealerFundsChangeitem()
		// this.getCouponList();
		if(this.selectTab2 === 'findDealerFundsMergeChangePage'){
			this.getFindDealerFundsMergeChangePage()
		}else {
			this.getFindDealerFundsDedution()
		}
	},
}
</script>