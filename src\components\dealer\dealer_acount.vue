<!-- 经销商账号列表 -->
<template>
	<div class='xpt-flex'>
		<xpt-list
			:data='shopList'
			:btns='btns'
			:colData='cols'
			:searchPage='search.page_name'
			:pageTotal='pageTotal'
			:selection='selection'
			@search-click='searchData'
			@selection-change='selectionChange'
			@page-size-change='pageSizeChange'
			@current-page-change='pageChange'
			@row-dblclick='rowDblclick'
		></xpt-list>

	</div>
</template>
<script>
	import fn from '@common/Fn.js';

export default {
	data(){
		let self = this
		return {
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: () => this.getShopList(),
	        },{
				type: 'primary',
				txt: '新增',
				click: () => this.toMoneyManagementDetail(),
	        }],
			cols: [
				{
					label: '单据编号',
					prop: 'order_no',
					redirectClick(row) {
						self.toMoneyManagementDetail(row)
					}
				}, {
					label: '店铺名称',
					prop: 'shop_name'
				},  {
					label: '店铺分类',
					prop: 'shop_type',
					format:'auxFormat',
					formatParams: 'shopClassify'
				},  {
					label: '所属经销商',
					prop: 'dealer_name',
				},{
					label: '账号类型',
					prop: 'account_type',
					formatter(val){
						return val == 'public' ? '对公账号':'对私账号'
					}
				}, {
					label: '支付方式',
					prop: 'payment_mode',
					format:'auxFormat',
					formatParams: 'payTypeDealer'
				}, {
					label: '充值账号',
					prop: 'recharge_account'
				}, {
					label: '开户名',
					prop: 'account_name'
				}, {
					label: '开户行',
					prop: 'account_bank'
				}, {
					label: '单据状态',
					prop: 'order_status',
					formatter(val) {
						return fn.getStatus()[val];
					}
				},{
					label: '创建人',
					prop: 'creator_name'
				}, {
					label: '创建日期',
					prop: 'create_time',
					format: 'dataFormat'
				}, {
					label: '审核人',
					prop: 'auditor_name'
				}, {
					label: '审核时间',
					prop: 'audit_time',
					format: 'dataFormat'

				}, {
					label: '备注',
					prop: 'remark'
				}, {
					label: '生效状态',
					prop: 'status',
					formatter(val){
						return val == 1 ? '生效':'失效'
					}
				}
			],
			search:{
				page_name: 'dealer_recharge_account',
				where: [],
				// page_size: self.pageSize,
				// page_no: 1,
				page:{
					length:self.pageSize,
					pageNo:1
				},
				dealer_id:null
			},
			shopList:[],
			selectData:'',
			pageTotal:0,
			selection: 'checkbox',
		}
	},
	/*
	params.selection：选择框状态，默认为多选；如需单选，请传值radio
	params.shop_type:清样商品选择店铺专有
	params.shop_status 店铺状态,"OPEN为生效的店铺"
	*/
	props:['params'],
	methods:{
		selectionChange(data) {
			this.selectData = data;
		},
		searchData(obj, resolve){
			this.search.where = obj;
			this.selectData = null;
			this.getShopList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.page.length = pageSize;
			this.selectData = null;
			this.getShopList();
		},
		pageChange(page){
			this.search.page.pageNo = page;
			this.selectData = null;
			this.getShopList();
		},
		getShopList(resolve){
			var postData = JSON.parse(JSON.stringify(this.search))

			if(this.params.setWhere){
				this.params.setWhere(postData)//在setWhere方法里面直接修改postData对象内容
			}

			this.ajax.postStream('/dealer-web/api/dealerRechargeAccount/list', postData, d=>{
				if(d.body.result&&d.body.content){
					this.pageTotal = d.body.content.count;
					this.shopList = d.body.content.list||[];
					
				} else {
					this.$message.error(d.body.msg || '')
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			})
		},
		rowDblclick(obj) {
			this.toMoneyManagementDetail(obj)
		},
		// 跳转到经销商账户详情
		toMoneyManagementDetail (row){
			this.$root.eventHandle.$emit('creatTab',{
				name: "经销商账户详情",
				params: {
					id: !row == true? '':row.id,
					// shop_id  : row.shopId,
					// isFromShopList: true,
					// cusName: row.cusName,
					// cusId: row.cusId,
				},
				component:()=>import('@components/dealer/account_detail'),
			})
		},
	},
	mounted() {
		this.ajax.postStream('/dealer-web/api/dealerFundsManageRecord/getDealerInfo', this.getEmployeeInfo('id'), res => {
				if(res.body.result){
					this.search.dealer_id = res.body.content
					this.getShopList();
					// this.searchFun()
				}else {
					this.$message.error(res.body.msg)
				}
			})
	}
}
</script>
