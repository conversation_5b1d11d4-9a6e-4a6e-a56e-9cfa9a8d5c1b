<!-- 经销商品采购折扣列表 -->
<template>
  <div class='xpt-flex'>
    <xpt-list :data='dataList' :btns='btns' :colData='cols' :searchPage='search.page_name' :pageTotal='pageTotal'
      :selection='selection' @search-click='searchData' @selection-change='selectionChange'
      @page-size-change='pageSizeChange' @current-page-change='pageChange' @row-dblclick='rowDblclick'>
    </xpt-list>
  </div>
</template>
<script>
  export default {
    data() {
      let self = this
      return {
        btns: [{
            type: 'primary',
            txt: '新增',
            click: () => this.add(),
          },
        ],
        cols: [{
            label: '版本号 ',
            prop: 'discount_code',
            redirectClick(row) {
				self.viewDetail(row)
			}
          },{
            label: '版本名称 ',
            prop: 'discount_name',
          },
          {
            label: '折扣类型',
            prop: 'discount_type',
            formatter:(val)=>{
						let status = {
							MATERIAL:'指定商品折扣',
							GOOD:'商品类别折扣',
							FORWARD_GOOD:'商品类别正向结算折扣',
						}
						return status[val] || val;
					}
          },
          {
            label: '开始时间',
            prop: 'begin_time',
            format: 'dataFormat'
          },
          {
            label: '结束时间',
            format: 'dataFormat',
            prop: 'end_time'
          },
          {
            label: '状态',
            prop: 'status',
            formatter:(val)=>{
						let status = {
							CREATE:'创建',
							PUBLISH:'已发布',
							EXECUTING:'执行跟踪',
							APPROVED:'已审核'
						}
						return status[val] || val;
					}
          },
		  {
            label: '创建人',
            prop: 'creator_name',
          },
		  {
            label: '创建时间',
            prop: 'create_time',
            format: 'dataFormat',
          },
		  {
            label: '发布人',
            prop: 'publish_name',
          },
		  {
            label: '发布时间',
            format: 'dataFormat1',
            prop: 'publish_time',
          },
		  {
            label: '备注',
            prop: 'remark',
          },
        ],
        search: {
          page_name: 'cloud_purchase_discount',
          where: [],
          page_size: self.pageSize,
          page_no: 1,
        },
        dataList: [],
        selectData: [],
        pageTotal: 0,
        selection:"",
      }
    },
    props: ['params'],
    methods: {
      viewDetail(row){
        let self= this;
        let params = {row :row}
        self.$root.eventHandle.$emit('creatTab',{
				name:"经销商品采购折扣管理详情",
				params:params,
				component: () => import('@components/dealer/dealerDiscountGoodsDetail.vue')
			});
      },
      selectionChange(data) {
        this.selectData = data;
      },
      add(){
        let self = this;
      
        let params = {row :{}}
        	self.$root.eventHandle.$emit('creatTab',{
				name:"新增经销商品采购折扣管理",
				params:params,
				component: () => import('@components/dealer/dealerDiscountGoodsDetail.vue')
			});
      },
      searchData(obj, resolve) {
        this.search.where = obj;
        this.selectData = null;
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page_no = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve) {
        var postData = JSON.parse(JSON.stringify(this.search))
        if (this.params.setWhere) {
          this.params.setWhere(postData) //在setWhere方法里面直接修改postData对象内容
        }
        this.ajax.postStream('/price-web/api/cloudPurchase/list', this.search, d => {
          if (d.body.result && d.body.content) {
            this.pageTotal = d.body.content.count;
            this.dataList = d.body.content.list || [];

          } else {
            this.$message.error(d.body.msg || '')
          }
          resolve && resolve();
        }, err => {
          resolve && resolve();
          this.$message.error(err);
        })
      },
			getFields(){

			},
      rowDblclick(obj) {
        this.serviceBillListDetail(obj)
      },
    },
    mounted() {
      this.getList();
    }
  }

</script>

