<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='primary' slot='left' size='mini' @click="save">保存</el-button>
    </xpt-headbar>
    <el-form label-width='100px' :model='query'  ref="query">
      <el-row :gutter='40' style='height:50px'>
        <el-col :span='40'  class="xpt_col">
          <el-form-item label='失效时间' prop='time'>
              <el-date-picker v-model="query.time" :picker-options='endDateFormatFun' type="datetime" value-format="yyyy-MM-dd 23:59:59" placeholder="选择日期"  size='mini'></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
  </template>
  <script>
    export default {
      data() {
        let self = this;
        return {
          query:{
            time:'',
          },
          endDateFormatFun:{
            disabledDate: time => {
              return time.getTime() <= Date.now()
          },
            date:(function(){
              var date = new Date();
              var year = date.getFullYear();
              var month = date.getMonth()+1;
              var day = date.getDate();
              var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
              return new Date(time);
              })()
        },
        }
      },
      props:['params'],
      methods: {
        close(){
          let self = this;
          self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
        },
        save(){
          if(!this.query.time){
            this.$message.error('失效时间不能为空');
            return false;
          }
          this.params.callback(this.query.time);
          this.close();
        },


      },
      mounted(){
        // this.getOldGoodsList();
      }
    }
  </script>
  <style>
    .repo-input{
      width:70px;
      float: right;
      /* margin-right: 50px; */
    }
    .xpt_col{
      margin-bottom: 10px;
    }
  </style>
