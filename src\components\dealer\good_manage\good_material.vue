<!--  -->
<template>
  <div class='xpt-flex'>
    <xpt-list :data='dataList' :btns='btns' :colData='cols' searchHolder='请输入SKUID查询条件' :pageTotal='pageTotal'
      :selection='selection' @search-click='searchData' @selection-change='selectionChange'
      @page-size-change='pageSizeChange' @current-page-change='pageChange' :taggelClassName="$style['row-height']">
    </xpt-list>
  </div>
</template>
<script>
import Fn from '@common/Fn.js'
  export default {
    data() {
      let self = this
      return {
        btns: [
          {
            type: 'success',
            txt: '刷新',
            click: () => this.getList(),
          },
        ],
        cols: [{
            label: 'SKUID',
            prop: 'sku_id',
            width: 110
          },{
            label: '物料编码',
            prop: 'outer_id',
            width: 110
          },{
            label: '规格描述 ',
            prop: 'material_specification',
            width: 190
          },
          {
            label: 'SKU状态',
            prop: 'is_onsale',
            formatter:(val)=>{
              let status = {
                1:'上架',
                0:'下架',
              }
              return status[val] || val;
            },
            width: 70
          },
          {
            label: '价格',
            prop: 'final_price',
            html:(val, row) =>{
              return `
                <p>到手价：<span style="color: red">${row.final_price ? row.final_price : ''}</span></p>
                <p>零售价：<span>${row.price ? row.price : ''}</span></p>
              `
            },
            width: 100
          },
          {
            label: '库存信息',
            prop: 'quantity',
            width: 60
          },
          {
            label: '线上货期',
            prop: 'sku_delivery_time',
            width: 60
          },
          {
            label: '是否同步',
            prop: 'if_sync',
            formatter:(val)=>{
              let status = {
                "Y":'是',
                "N":'否',
              }
              return status[val] || val;
            },
            width: 70
          },
          {
            label: '时间',
            prop: 'create_time',
            html:(val, row) =>{
              return `
                <p>创建时间：<span>${row.create_time ? Fn.dateFormat(row.create_time, 'yyyy-MM-dd hh:mm:ss') : ''}</span></p>
                <p>更新时间：<span>${row.update_time ? Fn.dateFormat(row.update_time, 'yyyy-MM-dd hh:mm:ss') : ''}</span></p>
              `
            },
            width: 190
          },
        ],
        search: {
          available_id: '',
          page_size: self.pageSize,
          page_no: 1,

        },
        dataList: [],
        selectData: [],
        pageTotal: 0,
        selection:"checkbox",
      }
    },
    props: ['params'],
    methods: {
      selectionChange(data) {
        this.selectData = data;
      },
      searchData(key, resolve) {
        if(key) {
          this.search.sku_id = key;
        } else {
          delete this.search.sku_id
        }
        this.selectData = null;
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page_no = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve) {
        if (this.params.id) {
          this.search.available_id = this.params.id
        }
        this.ajax.postStream('/plan-web/api/available/goods/query/listOnSaleGoodSkus', this.search, d => {
          if (d.body.result && d.body.content) {
            this.pageTotal = d.body.content.count;
            this.dataList = d.body.content.list || [];

          } else {
            this.$message.error(d.body.msg || '')
          }
          resolve && resolve();
        }, err => {
          resolve && resolve();
          this.$message.error(err);
        })
      },
			getFields(){

			},
    },
    mounted() {
      this.getList();
    }
  }

</script>
<style module>
.row-height :global(.cell) {
  height: auto!important;
}
</style>
