<!-- 经销商品折扣管理商品行列表  -->
<template>
  <xpt-list2 ref="saleOrderImportList" @cell-click="handleCellClick" :data="dataList" :orderNo="true" :btns="btns" :colData="cols" :searchPage="search.page_name"
    :pageTotal="pageTotal" :pageLength="search.page_size" :isNeedClickEvent="false" @selection-change="select"
    @search-click="searchClick" @page-size-change="sizeChange" @current-page-change="pageChange">
    <xpt-import slot="btns" :taskUrl="uploadUrl"  class="mgl10" :isupload="!canEidt || submit.status != 'CREATE'" :callback="saleOrderImportCallback" :otherParams="otherParams" text="导入">
    </xpt-import>
    <el-button slot="btns" type='success'  size='mini' @click="exportResultPopup" :disabled="!canEidt">导入结果</el-button>
  </xpt-list2>
</template>

<script>
  // import Fn from "@common/Fn.js";
  export default {
    props: ["params"],
    name: "saleOrderImportDataList",
    data() {
      let self = this;
      return {
      //  otherParams:{ event_id:self.event_id,argumentIsPathInsteadOfUrl: true},
        dataList: [],
        backupList: [],
        pageTotal: 0,
        // excel导入失败附件模板下载地址
        saleOrderExportUrl: null,
        // 附件上传地址
        uploadUrl: "/price-web/api/cloudEvent/importItem?permissionCode=RELEASE_EVENT_IMPORT",
        modelExcelExportUrl: null,
        selectRow: [],
        editList: [],
        btns: [
          //  按钮颜色：type 》 success 绿色， primary 蓝色 ，  danger 红色
          {
            type: "success",
            txt: "刷新",
            disabled(){
              return !self.event_id;
            },
            click() {
              self.getDataList();
            }
          },
          {
            type: "danger",
            txt: "删除",
            disabled(){
              return !self.canEidt || self.submit.status != 'CREATE';
            },
            click() {
              self.deleteData();
            }
          },
           {
            type: "warning",
            txt: "失效",
            disabled(){
              return !self.canEidt;
            },
            click() {
              self.disableData();
            }
          },

        ],

        cols:[
          {
            label: '链接ID',
            prop: 'num_iid',
            width: 130,
          },
          {
            label: '标题',
            prop: 'title',
          },
           {
            label: '活动标签',
            prop: 'event_tag',
            width: 130
          },
          {
            label: '活动说明',
            prop: 'event_description',
          },
          {
            label: '店铺名称',
            prop: 'nick',
            width: 130
          },
          {
            label: '行生效状态',
            prop: 'disabled_status',
            width: 80,
            formatter:(val)=>{
              let status = {
                1:'生效',
                0:'失效',
              }
              return status[val] || val;
            }
          },
          {
            label: '失效时间',
            prop: 'disable_time',
            format: "dataFormat1",
            width: 130
          },
          {
            label: '失效人',
            prop: 'disable_person_name',
            width: 130
          },
        ],
        currentCellSelect:{},

        search: {
          page_name: "cloud_release_event_item",
          where: [],
          page_no: 1,
          event_id:'',
          page_size: 20
        }
      };
    },
    props:{

      event_id: {
        type: Number
      },
      submit: {
        type: Object
      },
    },
    mounted: function () {
      this.search.event_id = this.event_id;
      if(this.event_id){
        this.getDataList();
      }
      // 固定复选框
    },
    computed:{
      canEidt(){
        return this.event_id
      },
      otherParams() {
        return {
          event_id: this.event_id,
          argumentIsPathInsteadOfUrl: true
        }
      }
    },

    methods: {
      //点击行
    handleCellClick(row) {
      this.currentCellSelect = row;
    },
      getDataList(resolve) {
        let self = this;

        this.editList = [];
        this.search.event_id = this.event_id
        // this.selectRow = [];
        // this.btns[0].loading = true;
        this.ajax.postStream(
          // 根据接口请求数据 - 替换对应接口url
          "/price-web/api/cloudEvent/listItem",
          this.search,
          res => {
            if (res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.backupList = JSON.parse(JSON.stringify(res.body.content.list));
              // self.$refs.saleOrderImportList.$refs.list.clearSelection();
              this.pageTotal = Number(res.body.content.count);
            } else {
              this.$message.error(res.body.msg);
            }
            if (resolve) {
              resolve();
            }
          },
          err => {
            this.$message.error(err);
            if (resolve) {
              resolve();
            }
          }
        );
      },

      deleteData() {
        if (this.selectRow.length < 1) {
          this.$message.error("请选择要删除的数据！");
          return;
        }

        const ids = this.selectRow.map(item => item.event_item_id);

        // this.$confirm(
        //   "当前操作会导致选中的数据被删除，是否继续？",
        //   "提示",
        //   {
        //     confirmButtonText: "确定",
        //     cancelButtonText: "取消",
        //     type: "danger"
        //   }
        // )
          // .then(() => {
            // 请求后台delete 接口 -- 待修改
            this.ajax.postStream(
              "/price-web/api/cloudEvent/deleteItem?permissionCode=RELEASE_EVENT_DELETE",
              ids,
              res => {
                if (res.body.result) {
                  this.$message.success(res.body.msg || "删除成功！");
                } else {
                  //res.body.msg &&
                  this.$message.error(res.body.msg || "删除失败！");
                }
                this.getDataList();
              },
              err => {
                this.getDataList();
                this.$message.error(err);
              }
            );
          // })
          // .catch(() => {
          //   return false;
          // });
      },
      disableData() {
        if (this.selectRow.length < 1) {
          this.$message.error("请选择要失效的数据！");
          return;
        }
        let self = this;
        var params = {
				callback(res){

					if(!!res){
            self.selectRow.forEach(item=>{
              item.disable_time = res;
              item.disable_person_id = self.getEmployeeInfo('id');
              item.disable_person_name = self.getEmployeeInfo('fullName');
            })
						self.ajax.postStream(
              "/price-web/api/cloudEvent/disableItem?permissionCode=RELEASE_EVENT_DISABLE",
              self.selectRow,
              res => {
                if (res.body.result) {
                  self.$message.success(res.body.msg || "成功！");
                } else {
                  //res.body.msg &&
                  self.$message.error(res.body.msg || "失败！");
                }
                self.getDataList();
              },
              err => {
                self.getDataList();
                self.$message.error(err);
              }
            );
					}

				}
			};

			self.$root.eventHandle.$emit('alert', {
				params:params,
				component: () => import('@components/dealer/good_manage/disable_dialog.vue'),
				close:function(res){

				},
				style:'width:500px;height:150px',
				title:'失效',
				recyclingGood:self.recyclingGood
			});
      },

      // 多选事件
      select(data) {
        this.selectRow = data;
      },

      searchClick(list, resolve) {
        this.search.where = list;
        this.getDataList(resolve); //模糊查找
      },
      // 监听每页显示数更改事件
      sizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.getDataList();
      },
      // 监听页数更改事件
      pageChange(page) {
        this.search.page_no = page;
        this.getDataList();
      },








      // 销售单导入(EXCEL) 回调
      saleOrderImportCallback(res) {
        this.saleOrderExportUrl = null;
        if (res.body.result) {
          this.$message.success(res.body.msg);
          this.saleOrderExportUrl = res.body.content;
          //  window.open(this.saleOrderExportUrl);
          this.getDataList();

          //this.saleOrderExportUrl = res.body.content.fileURL;
          // this.list = res.body.content.data;
          // this.cols = this.firstCol;
          // this.count = this.list.length;
        } else {
          this.$message.error(res.body.msg);
          this.saleOrderExportUrl = res.body.content;
          if (res.body.content != null || res.body.content != "") {
            //   window.open(this.saleOrderExportUrl);
          }
        }
      },
      // 导出结果弹窗
      exportResultPopup() {
        this.$root.eventHandle.$emit("alert", {
          component: () => import("@components/after_sales_report/export"),
          // component: () => import("@components/per_sales_report/export"), 过滤条件不生效，改回使用   @components/after_sales_report/export
          style: "width:900px;height:600px",
          title: "导入列表",
          params: {
            query: {
              type: 'EXCEL_TYPE_RELEASE_EVENT_IMPORT'
            }
          }
        });
      }
    }
  };
</script>
