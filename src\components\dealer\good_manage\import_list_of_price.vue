<!-- 经销商品折扣管理商品行列表  -->
<template>
  <xpt-list2 ref="saleOrderImportList" @cell-click="handleCellClick" :data="dataList" :orderNo="true" :btns="btns" :colData="cols" :searchPage="search.page_name"
    :pageTotal="pageTotal" :pageLength="search.page_size" :isNeedClickEvent="false" @selection-change="select"
    @search-click="searchClick" @page-size-change="sizeChange" @current-page-change="pageChange">
    <xpt-import slot="btns" :taskUrl="uploadUrl"  class="mgl10" :isupload="!canEidt || submit.status != 'CREATE'" :callback="saleOrderImportCallback" :otherParams="otherParams" text="导入">
    </xpt-import>
    <el-button slot="btns" type='success'  size='mini' @click="exportResultPopup" :disabled="!canEidt">导入结果</el-button>
  </xpt-list2>
</template>

<script>
  // import Fn from "@common/Fn.js";
  export default {
    props: ["params"],
    name: "saleOrderImportDataList",
    data() {
      let self = this;
      return {
      //  otherParams:{ header_id:self.header_id,argumentIsPathInsteadOfUrl: true},
        dataList: [],
        backupList: [],
        pageTotal: 0,
        // excel导入失败附件模板下载地址
        saleOrderExportUrl: null,
        // 附件上传地址
        uploadUrl: "/price-web/api/finalPrice/importItem?permissionCode=FINAL_PRICE_IMPORT",
        modelExcelExportUrl: null,
        selectRow: [],
        editList: [],
        btns: [
          //  按钮颜色：type 》 success 绿色， primary 蓝色 ，  danger 红色
          {
            type: "success",
            txt: "刷新",
            disabled(){
              return !self.header_id;
            },
            click() {
              self.getDataList();
            }
          },
          {
            type: "danger",
            txt: "删除",
            disabled(){
              return !self.canEidt || self.submit.status != 'CREATE';
            },
            click() {
              self.deleteData();
            }
          },
           {
            type: "warning",
            txt: "失效",
            disabled(){
              return !self.canEidt;
            },
            click() {
              self.disableData();
            }
          },
          {
            type: "warning",
            txt: "导入模板下载",
            disabled(){
              return !self.canEidt;
            },
            click() {
              self.downloadTemplate();
            }
          },

        ],

        cols:[
          {
            label: 'SKUID',
            prop: 'sku_id',
            width: 130,
          },
          {
            label: '链接ID',
            prop: 'num_iid',
            width: 130,
          },
          {
            label: '物料编码',
            prop: 'materia_number',
          },
           {
            label: '物料规格',
            prop: 'material_specification',
            width: 130
          },
          {
            label: '价格',
            prop: 'price',
          },
          {
            label: '店铺名称',
            prop: 'shop_name',
            width: 130
          },
          {
            label: '行生效状态',
            prop: 'disabled_status',
            width: 80,
            formatter:(val)=>{
              let status = {
                1:'生效',
                0:'失效',
              }
              return status[val] || val;
            }
          },
          {
            label: '失效时间',
            prop: 'disable_time',
            format: "dataFormat1",
            width: 130
          },
          {
            label: '失效人',
            prop: 'disable_name',
            width: 130
          },
        ],
        currentCellSelect:{},

        search: {
          page_name: "cloud_final_price_line",
          where: [],
          page_no: 1,
          header_id:'',
          page_size: 20
        }
      };
    },
    props:{

      header_id: {
        type: Number
      },
      submit: {
        type: Object
      },
    },
    mounted: function () {
      this.search.header_id = this.header_id;
      if(this.header_id){
        this.getDataList();
      }
      // 固定复选框
    },
    computed:{
      canEidt(){
        return this.header_id
      },
      otherParams() {console.log('this.header_id', this.header_id)
        return {
          header_id: this.header_id,
          argumentIsPathInsteadOfUrl: true
        }
      }
    },

    methods: {
      downloadTemplate() {
        let data = {
        page_name: "template_download_list",
        where: [
          {
            field: "1e08dba31f3647691c61c2030bd8e632",
            table: "d331c120edc028bbbc83230f04b409fa",
            value: '价格发布导入模板',
            operator: "=",
            condition: "AND",
            listWhere: [],
          },
        ],
        page_size: 50,
        page_no: 1,
      };
      this.ajax.postStream(
        "/reports-web/api/template/list",
        data,
        (response) => {
          if (response.body.result && response.body.content.count != 0) {
              window.open(response.body.content.list[0].url);
          } else {
            self.$message.error("未找到对应模板，请到模板下载列表维护");
          }
        },
        (e) => {
          self.$message.error(e);
        }
      );
      },
      //点击行
    handleCellClick(row) {
      this.currentCellSelect = row;
    },
      getDataList(resolve) {
        let self = this;

        this.editList = [];
        this.search.header_id = this.header_id
        // this.selectRow = [];
        // this.btns[0].loading = true;
        this.ajax.postStream(
          // 根据接口请求数据 - 替换对应接口url
          "/price-web/api/finalPrice/listLine",
          this.search,
          res => {
            if (res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.backupList = JSON.parse(JSON.stringify(res.body.content.list));
              // self.$refs.saleOrderImportList.$refs.list.clearSelection();
              this.pageTotal = Number(res.body.content.count);
            } else {
              this.$message.error(res.body.msg);
            }
            if (resolve) {
              resolve();
            }
          },
          err => {
            this.$message.error(err);
            if (resolve) {
              resolve();
            }
          }
        );
      },

      deleteData() {
        if (this.selectRow.length < 1) {
          this.$message.error("请选择要删除的数据！");
          return;
        }

        const ids = this.selectRow.map(item => item.line_id);

        // this.$confirm(
        //   "当前操作会导致选中的数据被删除，是否继续？",
        //   "提示",
        //   {
        //     confirmButtonText: "确定",
        //     cancelButtonText: "取消",
        //     type: "danger"
        //   }
        // )
        //   .then(() => {
            // 请求后台delete 接口 -- 待修改
            this.ajax.postStream(
              "/price-web/api/finalPrice/deleteItem?permissionCode=FINAL_PRICE_DELETE",
              ids,
              res => {
                if (res.body.result) {
                  this.$message.success(res.body.msg || "删除成功！");
                } else {
                  //res.body.msg &&
                  this.$message.error(res.body.msg || "删除失败！");
                }

                this.getDataList();
              },
              err => {

                this.getDataList();
                this.$message.error(err);
              }
            );
          // })
          // .catch(() => {
          //   return false;
          // });
      },
      disableData() {
        if (this.selectRow.length < 1) {
          this.$message.error("请选择要失效的数据！");
          return;
        }
        let self = this;
        var params = {
				callback(res){

					if(!!res){
            self.selectRow.forEach(item=>{
              item.disable_time = res;
              item.disable_id = self.getEmployeeInfo('id');
              item.disable_name = self.getEmployeeInfo('fullName');
            })
						self.ajax.postStream(
              "/price-web/api/finalPrice/disableItem?permissionCode=FINAL_PRICE_DISABLE",
              self.selectRow,
              res => {
                if (res.body.result) {
                  self.$message.success(res.body.msg || "成功！");
                } else {
                  //res.body.msg &&
                  self.$message.error(res.body.msg || "失败！");
                }
                self.getDataList();
              },
              err => {
                self.getDataList();
                self.$message.error(err);
              }
            );
					}

				}
			};

			self.$root.eventHandle.$emit('alert', {
				params:params,
				component: () => import('@components/dealer/good_manage/disable_dialog.vue'),
				close:function(res){
				},
				style:'width:500px;height:150px',
				title:'失效',
				recyclingGood:self.recyclingGood
			});
      },

      // 多选事件
      select(data) {
        this.selectRow = data;
      },

      searchClick(list, resolve) {
        this.search.where = list;
        this.getDataList(resolve); //模糊查找
      },
      // 监听每页显示数更改事件
      sizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.getDataList();
      },
      // 监听页数更改事件
      pageChange(page) {
        this.search.page_no = page;
        this.getDataList();
      },








      // 销售单导入(EXCEL) 回调
      saleOrderImportCallback(res) {
        this.saleOrderExportUrl = null;
        if (res.body.result) {
          this.$message.success(res.body.msg);
          this.saleOrderExportUrl = res.body.content;
          //  window.open(this.saleOrderExportUrl);
          this.getDataList();

          //this.saleOrderExportUrl = res.body.content.fileURL;
          // this.list = res.body.content.data;
          // this.cols = this.firstCol;
          // this.count = this.list.length;
        } else {
          this.$message.error(res.body.msg);
          this.saleOrderExportUrl = res.body.content;
          if (res.body.content != null || res.body.content != "") {
            //   window.open(this.saleOrderExportUrl);
          }
        }
      },
      // 导出结果弹窗
      exportResultPopup() {
        this.$root.eventHandle.$emit("alert", {
          component: () => import("@components/after_sales_report/export"),
          // component: () => import("@components/per_sales_report/export"), 过滤条件不生效，改回使用   @components/after_sales_report/export
          style: "width:900px;height:600px",
          title: "导入列表",
          params: {
            query: {
              type: 'EXCEL_TYPE_FINAL_PRICE_IMPORT'
            }
          }
        });
      }
    }
  };
</script>
