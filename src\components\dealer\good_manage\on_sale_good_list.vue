<!--  -->
<template>
  <div class='xpt-flex'>
    <xpt-list :data='dataList' :btns='btns' :colData='cols' :searchPage='search.page_name' :pageTotal='pageTotal'
      :selection='selection' @search-click='searchData' @selection-change='selectionChange'
      @page-size-change='pageSizeChange' @current-page-change='pageChange' :taggelClassName="$style['row-height']">
    <template slot-scope="scope" slot="message">
      <el-button type="primary"  size="mini" @click="showGoodMaterial(scope.row)">规格属性+</el-button>
    </template>
    <template slot-scope="scope" slot="picture">
      <img style="width:70px;height:70px;" :src='scope.row.pic_url'/>
    </template>
    </xpt-list>
  </div>
</template>
<script>
  export default {
    data() {
      let self = this
      return {
        btns: [
          {
            type: 'success',
            txt: '刷新',
            click: () => this.getList(),
          },
        ],
        cols: [{
            label: '主图',
            prop: 'pic_url',
            slot: 'picture'
          },{
            label: '链接ID ',
            prop: 'num_iid',
          },
          {
            label: '标题',
            prop: 'title',
          },
          {
            label: '规格信息',
            prop: 'message',
            slot: 'message'
          },
          {
            label: '店铺',
            prop: 'nick'
          },
          {
            label: '链接状态',
            prop: 'approve_status',
            formatter:(val)=>{
              let status = {
                "onsale":'在售',
                "instock":'下架',
              }
              return status[val] || val;
            }
          },
          {
            label: '活动标签',
            prop: 'event_tag'
          },
          {
            label: '上架时间',
            prop: 'list_time',
            format: 'dataFormat1'
          },
          {
            label: '更新时间',
            format: 'dataFormat1',
            prop: 'update_time'
          },
          {
            label: '下架时间',
            prop: 'delist_time',
            format: 'dataFormat1',
          },
        ],
        search: {
          page_name: 'on_sale_good',
          where: [],
          page_size: self.pageSize,
          page_no: 1,
        },
        dataList: [],
        selectData: [],
        pageTotal: 0,
        selection:"checkbox",
      }
    },
    props: ['params'],
    methods: {
      showGoodMaterial(row) {
        this.$root.eventHandle.$emit("alert", {
					params: {id: row.available_id},
					component: () => import('@components/dealer/good_manage/good_material.vue'),
					style: "width:800px;height:500px",
					title: "规格属性",
				});
      },
      selectionChange(data) {
        this.selectData = data;
      },
      searchData(obj, resolve) {
        this.search.where = obj;
        this.selectData = null;
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page_no = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve) {
        var postData = JSON.parse(JSON.stringify(this.search))
        if (this.params.setWhere) {
          this.params.setWhere(postData) //在setWhere方法里面直接修改postData对象内容
        }
        this.ajax.postStream('/plan-web/api/available/goods/query/listOnSaleGood?permissionCode=AVAILABLE_GOODS_QUERY', this.search, d => {
          if (d.body.result && d.body.content) {
            this.pageTotal = d.body.content.count;
            this.dataList = d.body.content.list || [];

          } else {
            this.$message.error(d.body.msg || '')
          }
          resolve && resolve();
        }, err => {
          resolve && resolve();
          this.$message.error(err);
        })
      },
    },
    mounted() {
      this.getList();
    }
  }

</script>
<style module>
.row-height :global(.cell) {
  height: auto!important;
}
</style>
