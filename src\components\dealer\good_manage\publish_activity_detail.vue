<!--经销折扣商品详情-->
<template>
	<div class='xpt-flex'>

    <el-row class='xpt-top'>
			<el-button type='success'  size='mini' @click="init" :disabled="!submitData.event_id">刷新</el-button>
			<el-button type='warning'  size='mini' @click="saveEvent" >保存</el-button>
			<el-button type='info'  size='mini' @click="submit" :disabled="!submitData.event_id">提交</el-button>
			<el-button type='info'  size='mini' @click="withdraw"  :disabled="!submitData.event_id">撤回</el-button>
			<el-button type='info'  size='mini' @click="publish"  :disabled="!submitData.event_id">发布</el-button>
			<el-button type='danger'  size='mini' @click="disable"  :disabled="!submitData.event_id">失效</el-button>
		</el-row>
		<el-form label-position="right" label-width="120px" :rules="rules" :model="submitData" ref="submitData">
			<el-tabs v-model="activeName"  >
				<el-tab-pane label="基本信息" name="first" class='xpt-flex'>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="活动发布版本"  >
								<el-input v-model="submitData.event_code"  size='mini'   disabled></el-input>
							</el-form-item>
              <el-form-item label="活动发布名称"  prop="event_name">
								<el-input v-model="submitData.event_name" :disabled="submitData.status != 'CREATE'"  size='mini'  @blur="e=>{
                  submitData.event_name = validName(submitData.event_name) ? submitData.event_name : ''}" ></el-input>
                  <el-tooltip v-if='rules.event_name[0].isShow' class="item" effect="dark" :content="rules.event_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="开始时间" prop="begin_time">
									<el-date-picker v-model="submitData.begin_time" :disabled="submitData.status != 'CREATE'" type="datetime" :picker-options='startDateFormatFun' placeholder="选择日期"  size='mini'></el-date-picker>
                  <el-tooltip v-if='rules.begin_time[0].isShow' class="item" effect="dark" :content="rules.begin_time[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
              </el-form-item>
                <el-form-item label="结束时间" prop="end_time">
									<el-date-picker v-model="submitData.end_time" :disabled="submitData.status != 'CREATE'" :picker-options='endDateFormatFun' type="datetime" value-format="yyyy-MM-dd 23:59:59" placeholder="选择日期"  size='mini'></el-date-picker>
                  <el-tooltip v-if='rules.end_time[0].isShow' class="item" effect="dark" :content="rules.end_time[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
            <el-col :span='8'>
                <el-form-item label="状态">
                  <!-- <el-input v-model="submitData.status"  size='mini'  ></el-input> -->
                 <el-select  size='mini'  placeholder="类型" v-model='submitData.status' disabled>
										<el-option label="创建" value="CREATE"></el-option>
										<el-option label="已发布" value="PUBLISH"></el-option>
										<el-option label="已提交" value="SUBMIT"></el-option>

									</el-select>
                </el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='16'>
							<el-form-item label="备注" style="height:60px">
								<el-input type="textarea" v-model="submitData.remark" :maxlength="240" ></el-input>
							</el-form-item>
						</el-col>
					</el-row>
				</el-tab-pane>
        <el-tab-pane label="编制信息" name="otherInfo" class="xpt-flex">
          <el-row :gutter='40'>
						<el-col :span='8'>
              <el-form-item label="创建人">
                <el-input v-model="submitData.creator_name"  size='mini' disabled></el-input>
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker v-model="submitData.create_time" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="提交人">
                <el-input v-model="submitData.submit_name"  size='mini' disabled></el-input>
              </el-form-item>
              <el-form-item label="提交时间">
                <el-date-picker v-model="submitData.submit_time" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="发布人">
                <el-input v-model="submitData.publish_name"  size='mini' disabled></el-input>
              </el-form-item>
              <el-form-item label="发布时间">
                <el-date-picker v-model="submitData.publish_time" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- <el-col :span='6'>
              <el-form-item label="失效人">
                <el-input v-model="submitData.disable_person_name"  size='mini' disabled></el-input>
              </el-form-item>
              <el-form-item label="失效时间">
                <el-date-picker v-model="submitData.disable_time" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-tab-pane>
			</el-tabs>
		</el-form>
		<div class='xpt-flex__bottom' >
			<el-tabs v-model="secondName">
		    	<el-tab-pane label="活动明细" name="activityDetail" class='xpt-flex'>
					<import-list
            ref="importList"
						:data='list'
						:event_id='submitData.event_id'
            :submit='submitData'
						orderNo
            selection=''
            :showHead="false"
						@selection-change='select'
					></import-list>
		    	</el-tab-pane>


		  	</el-tabs>
	  	</div>
	</div>
</template>
<script>
import validate from '@common/validate.js';
import Fn from '@common/Fn.js';
import importList from './import_list_of_activity.vue'
	export default {

    props:['params'],
    components: {
      importList,
    },
		data(){
      let self = this;
      return{
        startDateFormatFun:{
				  disabledDate: time => {
					/*新增时能选所有时间，编辑时选当天之后*/
				  	// return time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7
              return self.submitData.end_time ? time.getTime() > new Date(self.submitData.end_time ) ||time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7 : time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7;
				},
        date:(function(){
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth()+1;
            var day = date.getDate();
            var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
            return new Date(time);
      			})()

        },
          endDateFormatFun:{
            disabledDate: time => {
            /*新增时能选所有时间，编辑时选当天之后*/
              // return time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7
              return self.submitData.begin_time ? time.getTime() < new Date(self.submitData.begin_time) || time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7: time.getTime() <= Date.now() - 24*60*60*1000 + 8.64e7
          },
            date:(function(){
              var date = new Date();
              var year = date.getFullYear();
              var month = date.getMonth()+1;
              var day = date.getDate();
              var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
              return new Date(time);
              })()
        },
        saveDisabled:false,
        activeName:'first',
        secondName: 'activityDetail',
        submitData:{
          event_id:null,
          event_code:null,
          event_name:null,
          begin_time:null,
          end_time:null,
          status:'CREATE',
          remark:null,
          submit_name:null,
          submit_time:null,
          publish_name:null,
          publish_time:null,
          // disable_person_name:null,
          // disable_time:null,
        },
        list:[],
        rules: {
          event_name: validate.isNotBlank({
              required:true,
              self:self,
              msg:'请填写活动发布名称',
          }),
          end_time: validate.isNotBlank({
              required:true,
              self:self,
              msg:'请选择结束时间',
          }),
          begin_time: validate.isNotBlank({
              required:true,
              self:self,
              msg:'请选择开始时间',
          }),
        }
      }
    },
		methods:{
      validName(value) {
        if(value){
            if(!/^([\u4e00-\u9fa5_a-zA-Z0-9]+)$/.test(value)){
                this.$message.error('请输入中文、字母、数字组成的活动发布名称')
                return false
            }
        }else{
            this.$message.error('活动发布名称不允许为空')
            return false
        }
        return true
      },
      publish(){
        let self = this;
        let data =JSON.parse(JSON.stringify(this.submitData))
        data.publish_id = self.getEmployeeInfo('id');
        data.publish_name = self.getEmployeeInfo('fullName');
         this.ajax.postStream('/price-web/api/cloudEvent/publish?permissionCode=RELEASE_EVENT_PUBLISH', data, res => {
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
            if(res.body.result){
             self.init()
           }
            // $btn.disabled = false
          }, (err) => {
            self.$message.error(err)
          })
      },
      disable(){
        let self = this;
        this.$root.eventHandle.$emit("openDialog", {
            txt: "确认是否失效？",
            okTxt: "确定",
            cancelTxt: "取消",
            noShowNoTxt: true,
            ok() {
              self.ajax.postStream('/price-web/api/cloudEvent/disable?permissionCode=RELEASE_EVENT_DISABLE', [self.submitData.event_id], res => {
                  self.$message({
                    type: res.body.result ? 'success' : 'error',
                    message: res.body.msg
                  })
                  if(res.body.result){
                  self.init()
                }
                }, (err) => {
                  self.$message.error(err)
                })
            },
            cancel() {
              // self.$message.success("已取消");
            },
        })
      },
      submit() {
        let self = this;
        let data =JSON.parse(JSON.stringify(this.submitData))
        data.submit_id = self.getEmployeeInfo('id');
        data.submit_name = self.getEmployeeInfo('fullName');
         this.ajax.postStream('/price-web/api/cloudEvent/commit?permissionCode=RELEASE_EVENT_SUBMIT', data, res => {
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
            if(res.body.result){
             self.init()
           }
          }, (err) => {
            self.$message.error(err)
          })
      },
      withdraw() {
        let self = this;
        let data =JSON.parse(JSON.stringify(this.submitData))
         this.ajax.postStream('/price-web/api/cloudEvent/recall?permissionCode=RELEASE_EVENT_RECALL', data, res => {
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
            if(res.body.result){
             self.init()
           }
          }, (err) => {
            self.$message.error(err)
          })
      },
      saveEvent(){
        let self = this;
        this.$refs.submitData.validate( (valid) => {
          if (valid) {
            if(self.submitData.begin_time > self.submitData.end_time){
              self.$message.error('活动开始时间不能大于活动结束时间');
              return false;
            }
            if(!self.event_id){
              self.submitData.creator = self.getEmployeeInfo('id');
              self.submitData.creator_name = self.getEmployeeInfo('fullName');
            }
            // if(self.submitData.end_time){
            //   let date = new Date(self.submitData.end_time);
            //   var year = date.getFullYear();
            //   var month = date.getMonth()+1;
            //   var day = date.getDate();
            //   var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
            //   self.submitData.end_time = new Date(time).getTime()
            // }

            this.ajax.postStream('/price-web/api/cloudEvent/saveOrUpdate?permissionCode=RELEASE_EVENT_SAVE', self.submitData, res => {
                this.$message({
                  type: res.body.result ? 'success' : 'error',
                  message: res.body.msg
                })
                if (res.body.content) {
                  self.submitData.event_id = res.body.content;
                  //  this.params.row.event_id = res.body.content;
                  self.$refs.importList.otherParams.event_id  = res.body.content;
                  self.$refs.importList.search.event_id  = res.body.content;
                  self.init()
                }
              }, (err) => {
                self.$message.error(err)
              })
          }
        })
      },
      select(){

      },
      init() {
          let self = this;
          self.ajax.get(
          "/price-web/api/cloudEvent/getById/"+self.submitData.event_id,

          res => {
            if (res.body.result) {
              this.submitData = res.body.content;
            } else {
              this.$message.error(res.body.msg);
            }
            this.$refs.importList.getDataList()
          },
          err => {
            this.$message.error(err);
          }
        );
      },

			pageChange(page_size){
				this.storeDiscountParams.page_size = page_size
				this.getShopDiscountList()
			},
			currentPageChange(page){
				this.storeDiscountParams.page_no = page
				this.getShopDiscountList()
			},
		},

    beforeDestroy(){
         this.$root.eventHandle.$off('updateOrderStatus', this.init)
    },
		mounted(){
      // this.submit = this.params.row;
      if(this.params.row.event_id){
        this.submitData.event_id = this.params.row.event_id
        this.init();
      }
        this.$root.eventHandle.$on('updateOrderStatus', this.init)
    }
	}
</script>
