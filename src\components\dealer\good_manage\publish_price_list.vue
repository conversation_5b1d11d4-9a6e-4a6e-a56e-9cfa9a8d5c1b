<!--  -->
<template>
  <div class='xpt-flex'>
    <xpt-list :data='dataList' :btns='btns' :colData='cols' :searchPage='search.page_name' :pageTotal='pageTotal'
      :selection='selection' @search-click='searchData' @selection-change='selectionChange'
      @page-size-change='pageSizeChange' @current-page-change='pageChange' @row-dblclick='rowDblclick'>
    </xpt-list>
  </div>
</template>
<script>
  export default {
    data() {
      let self = this
      return {
        btns: [
          {
            type: 'success',
            txt: '刷新',
            click: () => this.getList(),
          },
          {
            type: 'primary',
            txt: '价格发布',
            click: () => this.add(),
          },
          {
            type: 'primary',
            txt: '导出',
            click: () => this.export(),
          },
          {
            type: 'primary',
            txt: '导出结果查看',
            click: () => this.downLoad(),
          },
        ],
        cols: [{
            label: '发布版本号',
            prop: 'price_version_number',
            redirectClick(row) {
              self.viewDetail(row)
            }
          },{
            label: '发布版本名称',
            prop: 'price_version_name',
          },
          {
            label: '价格类型',
            prop: 'price_type',
            format: 'auxFormat',
            formatParams: 'final_price_type',
          },
          {
            label: '状态',
            prop: 'status',
            formatter:(val)=>{
              let status = {
                CREATE:'创建',
                PUBLISH:'已发布',
                SUBMIT:'已提交',
              }
              return status[val] || val;
            }
          },
          {
            label: '开始时间',
            prop: 'start_time',
            format: 'dataFormat1'
          },
          {
            label: '结束时间',
            format: 'dataFormat1',
            prop: 'end_time'
          },
          {
            label: '创建人',
            prop: 'creator_name',
          },
          {
            label: '创建时间',
            prop: 'create_time',
            format: 'dataFormat1',
          },
          {
            label: '提交人',
            prop: 'submit_name',
          },
          {
            label: '提交时间',
            format: 'dataFormat1',
            prop: 'submit_time',
          },
          {
            label: '发布人',
            prop: 'publish_name',
          },
          {
            label: '发布时间',
            format: 'dataFormat1',
            prop: 'publish_time',
          },
          {
            label: '备注',
            prop: 'remark',
          },
        ],
        search: {
          page_name: 'cloud_final_price_header',
          where: [],
          page_size: self.pageSize,
          page_no: 1,
        },
        dataList: [],
        selectData: [],
        pageTotal: 0,
        selection:"checkbox",
      }
    },
    props: ['params'],
    methods: {
      viewDetail(row){
        let self= this;
        let params = {row :row}
        self.$root.eventHandle.$emit('creatTab',{
				name:"价格发布详情",
				params:params,
				component: () => import('@components/dealer/good_manage/publish_price_detail.vue')
			});
      },
      selectionChange(data) {
        this.selectData = data;
      },
      export() {
        if(this.selectData.length==0){
          this.$message.error('请选择行');
          return false;
        }
        let params = this.selectData.map(item => {
          return item.header_id
        })
        this.ajax.postStream('/price-web/api/finalPrice/export?permissionCode=FINAL_PRICE_EXPORT', params, res => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
          } else {
            this.$message.error(res.body.msg);
          }
        });
      },
      // 导出结果弹窗
      downLoad() {
        this.$root.eventHandle.$emit("alert", {
          component: () => import("@components/after_sales_report/export"),
          style: "width:900px;height:600px",
          title: "导出列表",
          params: {
            query: {
              type: 'EXCEL_TYPE_FINAL_PRICE_EXPORT'
            }
          }
        });
      },
      add(){
        let self = this;

        let params = {row :{}}
        	self.$root.eventHandle.$emit('creatTab',{
				name:"新增价格发布",
				params:params,
				component: () => import('@components/dealer/good_manage/publish_price_detail.vue')
			});
      },
      searchData(obj, resolve) {
        this.search.where = obj;
        this.selectData = null;
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page_no = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve) {
        var postData = JSON.parse(JSON.stringify(this.search))
        if (this.params.setWhere) {
          this.params.setWhere(postData) //在setWhere方法里面直接修改postData对象内容
        }
        this.ajax.postStream('/price-web/api/finalPrice/listHeader?permissionCode=FINAL_PRICE_QUERY', this.search, d => {
          if (d.body.result && d.body.content) {
            this.pageTotal = d.body.content.count;
            this.dataList = d.body.content.list || [];

          } else {
            this.$message.error(d.body.msg || '')
          }
          resolve && resolve();
        }, err => {
          resolve && resolve();
          this.$message.error(err);
        })
      },
      rowDblclick(obj) {
      },
    },
    mounted() {
      this.getList();
    }
  }

</script>

