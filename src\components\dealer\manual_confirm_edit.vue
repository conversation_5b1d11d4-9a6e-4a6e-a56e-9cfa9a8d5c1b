<template>
<el-form :model='form' :rules='rules' ref='form' label-position="left" style="padding: 20px;">
    <el-form-item label="合并订单ID" prop="mergeTradeVOListStr">
        <el-input
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5}"
            style="width:500px;"
            placeholder="可输入多个合并订单ID，以“,”分隔"
            v-model="form.mergeTradeVOListStr"
            @blur="form.mergeTradeVOListStr = form.mergeTradeVOListStr.trim()"
        >
        </el-input>
        <el-tooltip v-if='rules.mergeTradeVOListStr[0].isShow' class="item" effect="dark" :content="rules.mergeTradeVOListStr[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
        </el-tooltip>
        <el-button @click="search">手动确认变更</el-button>
    </el-form-item>
    <p v-if="form.msg">返回结果：{{ form.msg }}</p>
</el-form>
</template>

<script>
import VL from '@common/validate.js'

export default {
    data (){
        return {
            form: {
                mergeTradeVOListStr: '',
                msg: '',
            },
            rules: {
                mergeTradeVOListStr: VL.isNotBlank({
                    self: this,
                    msg: '请输入至少一个合并订单ID',
                }),
            },
        }
    },
    methods: {
        search (){
            this.$refs.form.validate(valid => {
                if(valid){
                    this.ajax.postStream(
                        '/dealer-web/api/dealerFundsManageRecord/manualConfirmEdit',
                        this.form.mergeTradeVOListStr.split(',').map(merge_trade_id => ({ merge_trade_id })),
                        res => {
                            this.form.msg = res.body.msg
                        }
                    )
                }else {
                    this.form.msg = ''
                }
            })
        },
    },
    mounted (){
    },
}
</script>