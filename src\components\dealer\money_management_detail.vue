<!-- 货款管理详情 -->
<template>
<div class="xpt-flex">
	<div style="padding:30px;">
		<h1 style="font-size:30px!important;margin-bottom:30px;">{{ paramsShopName }}管理</h1>
		<div style="font-size:20px!important;">货款可用金额：<span style="color:red;font-size:30px!important;margin-right:40px;padding-left: 30px;">{{ payment_amount }}</span>货款冻结金额：<span style="color:red;font-size:30px!important;padding-left: 30px;">{{ freeze_payment_amount }}</span>
		<div style="font-size:15px!important;margin-top: 20px;">已下单未审核金额 <span style="font-size:20px!important;margin-right:20px;padding-left: 20px;">{{ unaudited_amount  }}</span>累计充值金额 :<span style="font-size:20px!important;padding-left: 20px;margin-right:20px;">{{ recharge_amount  }}</span>累计采购金额:<span style="font-size:20px!important;padding-left: 20px;margin-right:20px;">{{ purchase_amount  }}</span></div>
			<el-button
				type='primary'
				size='mini'
				@click='() => {
					searchAmount(selectShopId);
					$refs.$detailList.searchFun(null, selectShopId);
					findDealerFundsChangeItemPageSearch()
				}'
				style="float:right;margin: 6px 0;"
			>刷新</el-button>
			<el-button type='primary' size='mini' @click='addNewDealerFundsManageRecordDetail' style="float:right;margin: 6px 20px 6px 0;">货款充值</el-button>
			<el-select v-if="selectShopId" size='mini' v-model='selectShopId'  style="float:right;margin: 6px 20px 6px 0;" @change="selectShopIdChange">
				<el-option v-for="item in shopList" :key="item.shopId" :label="item.shopName" :value="item.shopId"> </el-option>
			</el-select>
		</div>
	</div>
	<el-tabs value="findDealerFundsChangeItemPage" class="xpt-flex">
		<el-tab-pane label="货款扣款明细" name="findDealerFundsChangeItemPage" class="xpt-flex">
			<xpt-list
				:data='findDealerFundsChangeItemPageList'
				:colData='findDealerFundsChangeItemPageCol'
				selection='hidden'
				:btns="btns"
				:pageTotal='pageTotal'
				:searchPage="searchParams.page_name"
				@search-click='searchClick'
				@page-size-change='pageChange'
				@current-page-change='currentPageChange'
			></xpt-list>
		</el-tab-pane>
		<el-tab-pane label="货款充值明细" name="detailList" class="xpt-flex">
			<component
				is='dealerFundsManageRecordDetailList'
				v-if="params.shop_id || selectShopId"
				:shopId="params.shop_id || selectShopId"
				ref="$detailList"
			></component>
		</el-tab-pane>
		<el-tab-pane label="经销充值账号" name="dealerAccount" class="xpt-flex">
			<xpt-list
				:data='dealerAccountData'
				:colData='dealerAccountCol'
				selection='radio'
				:btns="dealerAccountBtns"
				@radio-change='accountDataChange'
			></xpt-list>
		</el-tab-pane>

	</el-tabs>
</div>
</template>

<script>
import dealerFundsManageRecordDetailList from '@components/dealer/recharge_of_goods_list'
export default {
	props: ['params'],
	data (){
		var self = this

		return {
			paramsShopName: 0,
			payment_amount: 0,
			freeze_payment_amount: 0,
			selectShopId: '',
			shopList: [],
			unaudited_amount:0,
			recharge_amount:0,
			purchase_amount:0,
			customer_source_id:'',
			customer_source_name:'',
			shop_id:'',
			dealerAccountBtns:[
				// {
				// 	type: 'primary',
				// 	txt: '保存',
				// 	click: () => this.saveDealerAccount(),
				// },
				// {
				// 	type: 'info',
				// 	txt: '新增',
				// 	click: () => this.addDealerAccount(),
				// },
				// {
				// 	type: 'danger',
				// 	txt: '删除',
				// 	click: () => this.delDealerAccount(),
				// },
				// {
				// 	type: 'primary',
				// 	txt: '生效',
				// 	click: () => this.statusDealerAccount('1'),
				// },
				// {
				// 	type: 'primary',
				// 	txt: '失效',
				// 	click: () => this.statusDealerAccount('2'),
				// },

			],
			ListTotal: {
				amount: '合计0',
				unauditedAmount: '合计0',
				dealerDeductionAmount: '合计0',
				frozenAmount: '合计0',
				purchase_amount: '合计0',
				mergeDeductionAmount: '合计0',
			},

			btns: [{
				type: 'primary',
				txt: '刷新',
				click: () => this.findDealerFundsChangeItemPageSearch(),
	        },{//明细
				type: 'primary',
				txt: '导出',
				click: () => this.exportDealerFunds('exportDealerFundsDedution', this.searchParams),
	        }, {
				type: 'primary',
				txt: '报表导出文件下载',
				click: () => this.downloadExportFile('EXCEL_TYPE_DEALER_DEDUTION'),
	        }],
			dealerAccountCol:[
				{
					label: '支付方式',
					prop: 'payment_mode',
					format:'auxFormat',
					formatParams: 'payTypeDealer'
				},{
					label: '充值账号',
					prop: 'recharge_account',
				},{
					label: '开户名',
					prop: 'account_name',
				},{
					label: '开户行',
					prop: 'account_bank',
				},{
					label: '状态',
					prop: 'status',
					formatter(val){
						switch(val){
							case '1': return '生效';
							case '2': return '失效';
						}
					}
				},{
					label: '失效人',
					prop: 'lose_efficacy_name',
				},{
					label: '失效时间',
					prop: 'lose_efficacy_time',
					format:'dataFormat1'
				},{
					label: '店铺ID',
					prop: 'shop_id',
				},
			],
			accountObj:'',
			findDealerFundsChangeItemPageList: [],
			dealerAccountData:[],
			findDealerFundsChangeItemPageCol: [
				{
				label: '合并单号',
				prop: 'mergeTradeNo',
				width: 200,
				redirectClick: d => this.openOrder(d.mergeTradeId, d.mergeTradeNo),
				},{
					label: '买家昵称',
					prop: 'cusName',
				},{
					label: '实际售价',
					prop: 'mergeActPrice',
					// formatter(val){
					// 	if(!val){
					// 		return self.getthousand(val)
					// 	}
					// }
				},{
					label: '未扣款金额',
					prop: 'unauditedAmount',
					// formatter(val){
					// 	if(!val){
					// 		return self.getthousand(val)
					// 	}
					// }
				},{
					label: '扣款金额',
					prop: 'dealerDeductionAmount',
					// formatter(val){
					// 	if(!val){
					// 		return self.getthousand(val)
					// 	}
					// }
				},{
					label: '冻结金额',
					prop: 'frozenAmount',
					// formatter(val){
					// 	if(!val){
					// 		return self.getthousand(val)
					// 	}
					// }
				},{
					label: '已采购金额',
					prop: 'purchaseAmount',
					// formatter(val){
					// 	if(!val){
					// 		return self.getthousand(val)
					// 	}
					// }
				},{
					label: '发运行应扣金额',
					prop: 'mergeDeductionAmount',
					// formatter(val){
					// 	if(!val){
					// 		return self.getthousand(val)
					// 	}
					// }
				},{
					label: '扣款是否异常',
					prop: 'ifError',
					formatter: prop => ({
						true: '是',
						false: '否'
					}[prop])
				},{
					label: '最后更新时间',
					prop: 'modifyTime',
					format: 'dataFormat1',
					width: 150,
				},{
					label: '最后更新人',
					prop: 'modifierName',
				},{
					label: '扣款明细',
					prop: 'mergeTradeId',
					formatter: prop => prop ? '点击查看' : '',
					redirectClick: d => this.toDealerFundsDedution(d.mergeTradeId),
				}
				],

			searchParams:{
				page_name: 'dealer_funds_change_item',
				where: [],
				page_size: 50,
				page_no: 1,
				id: '',
			},

			pageTotal:0,
		}
	},
	methods: {
		accountDataChange(obj){
			this.accountObj= obj;
		},
		toDealerFundsDedution (id){
			this.$root.eventHandle.$emit('creatTab',{
				name: "货款扣款详情",
				params: { mergeTradeId: id },
				component: () => import('@components/dealer/dealerFundsDedution.vue')
			})
		},
		openOrder (id, order_no){
			if(order_no == '合计'){
				return false;
			}
			console.log(id,order_no)
			this.$root.eventHandle.$emit('creatTab',{
				name: "合并订单详情",
				params: { merge_trade_id: id },
				component: () => import('@components/order/merge.vue')
			})
		},
		searchClick(obj, resolve){
			this.searchParams.where = obj
			this.findDealerFundsChangeItemPageSearch(resolve)
		},
		pageChange(page_size){
			this.searchParams.page_size = page_size
			this.findDealerFundsChangeItemPageSearch()
		},
		currentPageChange(page){
			this.searchParams.page_no = page
			this.findDealerFundsChangeItemPageSearch()
		},
		findDealerFundsChangeItemPageSearch (resolve){
			this.searchParams.id = this.selectShopId || this.params.shop_id

			if(this.searchParams.id){
				this.ajax.postStream('/dealer-web/api/dealerFundsManage/findDealerFundsChangeItemPage', this.searchParams, res => {
					if(res.body.result){
						// this.$message.success(res.body.msg)
						this.findDealerFundsChangeItemPageList = res.body.content.list || []
						// this._ListTotal()
						var totalObj =  {
							order_status: '',
							mergeTradeNo: '合计',
							unauditedAmount: res.body.content.amount.sum_unaudited_amount,
							dealerDeductionAmount: res.body.content.amount.sum_dealer_deduction_amount,
							frozenAmount: res.body.content.amount.sum_frozen_amount,
							purchaseAmount: res.body.content.amount.sum_purchase_amount,
							mergeDeductionAmount: res.body.content.amount.sum_merge_deduction_amount,

						};
						if(this.findDealerFundsChangeItemPageList.length > 0){
							this.findDealerFundsChangeItemPageList= res.body.content.list.concat(totalObj);
						}

						this.pageTotal = res.body.content.count
					}else {
						this.$message.error(res.body.msg)
					}

					resolve && resolve()
				}, () => {
					resolve && resolve()
				}, this.params && this.params.tabName)
			}
		},
		_ListTotal(){
			var totalObj =  {
				amount: 0,
				unauditedAmount: 0,
				dealerDeductionAmount: 0,
				frozenAmount: 0,
				purchase_amount: 0,
				mergeDeductionAmount: 0,
			}
			,	totalKey = Object.keys(totalObj)

			this.findDealerFundsChangeItemPageList.forEach(obj => {
				totalKey.forEach(key => {
					if(obj[key]){
						totalObj[key] += Number(obj[key])
					}
				})
			})
			totalKey.forEach(key => {
				totalObj[key] =  + totalObj[key].toFixed(2)
			})
			totalObj.order_status = '';
			totalObj.mergeTradeNo = '合计';
			this.ListTotal = totalObj;
		},
		addNewDealerFundsManageRecordDetail (){
			this.$root.eventHandle.$emit('creatTab', {
				name: '新增货款充值',
				params: {
					shop_name: this.params.shop_name,
					shop_id  : this.shop_id,
					cusId  	 : this.params.cusId,
					cusName  : this.params.cusName,
					isFromMoneyManagementDetail: true,
					customer_source_name:this.customer_source_name,
					customer_source_id:this.customer_source_id
				},
				component: () => import('@components/dealer/recharge_of_goods_detail'),
			})
		},
		searchAmount (selectShopId){
			this._fetch('/dealer-web/api/dealerFundsManage/get', { shop_id: selectShopId || this.params.shop_id }, res => {
				// res.body.content = res.body.content || {}
				if(res.body.content){
                    console.log(res.body.content);
                    // 现金余额 + 信用金额-冻结金额
					this.payment_amount = this.getthousand((Number(res.body.content.cash_balance) + Number(res.body.content.credit_amount)-res.body.content.freeze_payment_amount).toFixed(2) );
					// this.payment_amount = this.getthousand(res.body.content.payment_amount);
					this.freeze_payment_amount = this.getthousand(res.body.content.freeze_payment_amount);
                    this.purchase_amount  = this.getthousand(res.body.content.purchase_amount);
                    // 总充值金额 + 信用金额
					this.recharge_amount   = this.getthousand(Number(res.body.content.recharge_amount) + Number(res.body.content.credit_amount));
					this.unaudited_amount  = this.getthousand(res.body.content.unaudited_amount);
					this.customer_source_id  = res.body.content.customer_source_id;
					this.customer_source_name  = res.body.content.customer_source_name;
					this.shop_id  = res.body.content.shop_id;
					// this.getAccount();
				}else{
					this.payment_amount = 0;
					this.freeze_payment_amount = 0;
					this.purchase_amount  = 0;
					this.recharge_amount   = 0;
					this.unaudited_amount  = 0;
				}
			})
			this.getAccount();
		},
		_fetch (url, data, cb){
			this.ajax.postStream(url, data, res => {
				if(res.body.result){
					cb(res)
	            }else {
	            	this.$message.error(res.body.msg)
	            }
			})
		},
		selectShopIdChange (){
			this.shopList.some(obj => {
				if(obj.shopId === this.selectShopId){
					this.paramsShopName = obj.shopName
					return true
				}
			})
			this.searchAmount(this.selectShopId)
			this.$refs.$detailList.searchFun(null, this.selectShopId)
			this.findDealerFundsChangeItemPageSearch()
		},
		getDealerInfo (){
			this._fetch('/dealer-web/api/dealerFundsManageRecord/getDealerInfo', this.getEmployeeInfo('id'), res => {
				if(res.body.content){
					this._fetch('/dealer-web/api/dealerFundsManage/getList', { customer_source_id: res.body.content }, res => {
						this.shopList = (res.body.content || []).map(obj => ({
							shopName: obj.shop_name,
							shopId: obj.shop_id,
						}))

						if(this.selectShopId = (this.shopList[0] || {}).shopId){
							this.paramsShopName = this.params.shop_name = this.shopList[0].shopName
							this.searchAmount(this.selectShopId)
							this.findDealerFundsChangeItemPageSearch()
							this.getAccount();
						}
					})
				}else {
					this.$message.error('非经销商用户不能查看')
				}
			})
		},
		getthousand(data){
			// 数字获取千分位
			// data = data.split(',').join('')
			let changeData = data.toString().split(',').join('');
			let splitArr = [];
			splitArr = changeData.split('.');
			console.log(splitArr.length == 1,Number(splitArr[0])>0)
			let leftArr = splitArr[0].split('').reverse();
			let rightArr =  splitArr[1] || '';
			let newArr = []
			leftArr.forEach((item,index) => {
				newArr.push(item)
				// if((index + 1) % 3 == 0 && (index + 1) != leftArr.length && Number(splitArr[0])>0){
				// 	newArr.push(",");
				// 	console.log(Number(splitArr[0])>0)
				// }
				// if((index + 1) % 3 == 0 && (index + 1) != leftArr.length-1){
				// 	newArr.push(",");
				// }
				if(Number(splitArr[0])>0){
					if((index + 1) % 3 == 0 && (index + 1) != leftArr.length){
						newArr.push(",");
					}
				}else{
					if((index + 1) % 3 == 0 && (index + 1) != leftArr.length-1){
						newArr.push(",");
					}
				}
			})
			newArr = newArr.reverse();
			let returnData = splitArr.length == 1 ? newArr.join(''):(newArr.join('')+'.'+rightArr);
			console.log(returnData,rightArr)
			return returnData;
		},
		exportDealerFunds (api, searchParams){
			this.ajax.postStream('/dealer-web/api/dealerFundsManageExport/exportDealerFundsChangeItem' , searchParams, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		downloadExportFile (type){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/dealer_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type,
					},
				},
			})
		},
		getAccount(){
			let self = this;
			// {shop_id:this.shop.shop_id
			this.ajax.postStream('/dealer-web/api/dealerRechargeAccount/shopFundsList',{shop_id:self.params.shop_id||this.selectShopId, order_status: "APPROVED"}, res => {
				if(res.body.result){
					console.log(res.body)
					self.dealerAccountData = res.body.content.list;
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		saveDealerAccount(){
			let self = this;
			if(self.dealerAccountData.length == 0){
				super.$message.error('请添加充值账号')
			}
			console.log(self.params.shop_id)
			this.ajax.postStream('/dealer-web/api/dealerRechargeAccount/add',{shop_id:self.params.shop_id|| this.selectShopId,accounts:self.dealerAccountData}, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
					self.getAccount();
				}else {
					this.$message.error(res.body.msg)
				}

			})
		},
		addDealerAccount(row){
				this.$root.eventHandle.$emit('creatTab',{
				name: "经销商账户详情",
				params: {
					id: !row == true? '':row.id,
					shop_id  : this.params.shop_id,
					shop_name  : this.params.shop_name,
					customer_source_name : this.customer_source_name,
					customer_source_id : this.customer_source_id
					// shop_id  : row.shopId,
					// isFromShopList: true,
					// cusName: row.cusName,
					// cusId: row.cusId,
				},
				component:()=>import('@components/dealer/account_detail'),
			})
			// var self = this;
			// self.params.callback = (d)=>{
			// 	console.log(self.dealerAccountData)
			// 	self.dealerAccountData.push(d);
			// }
			// self.$root.eventHandle.$emit('alert',{params:self.params,
			// 	component:() => import('@components/dealer/addAccount.vue'),
			// 	close:function(){},
			// 	style:'width:1100px;height:250px',
			// 	title:'新增银行账号'
			// });
		},
		delDealerAccount(){
			let self = this;
			if(self.accountObj == ''){
				return false;
			}
			console.log(self.accountObj)
			self.dealerAccountData.forEach((item,index) =>{
				if(item == self.accountObj ){
					if(item.creator){
						self.$message.error('已保存数据无法删除')
						return false;
					}
					self.dealerAccountData.splice(index,1);
				}
			})
		},
		statusDealerAccount(type){
			let self = this;
			if(self.accountObj == ''){
				return false;
			}
			console.log(self.accountObj)
			self.dealerAccountData.forEach((item,index) =>{
				if(item == self.accountObj ){
					item.status = type;
				}
			})
		}
	},
	mounted (){
		this.paramsShopName = this.params.shop_name

		this.findDealerFundsChangeItemPageSearch()
		if(this.params.isFromShopList){
			this.searchAmount()

		}else {
			this.getDealerInfo()
		}
	},
	components: {
		dealerFundsManageRecordDetailList,
	},
}
</script>
