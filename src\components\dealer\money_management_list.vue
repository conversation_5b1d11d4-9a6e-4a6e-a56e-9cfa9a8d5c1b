<!-- 货款管理列表 -->
<template>
	<div class='xpt-flex'>
		<xpt-list
			:data='shopList'
			:btns='btns'
			:colData='cols'
			:searchPage='search.page_name'
			:pageTotal='pageTotal'
			:selection='selection'
			@search-click='searchData'
			@selection-change='selectionChange'
			@page-size-change='pageSizeChange'
			@current-page-change='pageChange'
			@row-dblclick='rowDblclick'
		></xpt-list>

	</div>
</template>
<script>
export default {
	data(){
		let self = this
		return {
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: () => this.getShopList(),
	        }],
			cols: [
				{
					label: '店铺编码',
					prop: 'shopCode',
					redirectClick(row) {
						self.toMoneyManagementDetail(row)
					}
				}, {
					label: '店铺名称',
					prop: 'shopName'
				}, {
					label: '店铺分类',
					prop: 'shopTypeName'
				}, {
					label: '所属经销商',
					prop: 'cusName',
				},{
					label: '店铺地区',
					prop: 'shopAreaName',
				},{
					label: '可用金额',
					prop: 'balanceAmount',
				}, {
					label: '冻结金额',
					prop: 'frozenAmount'
				}
			],
			search:{
				page_name: 'dealer_shop_admin',
				where: [],
				page_size: self.pageSize,
				page_no: 1,
			},
			shopList:[],
			selectData:'',
			pageTotal:0,
			selection: 'checkbox',
		}
	},
	/*
	params.selection：选择框状态，默认为多选；如需单选，请传值radio
	params.shop_type:清样商品选择店铺专有
	params.shop_status 店铺状态,"OPEN为生效的店铺"
	*/
	props:['params'],
	methods:{
		selectionChange(data) {
			this.selectData = data;
		},
		searchData(obj, resolve){
			this.search.where = obj;
			this.selectData = null;
			this.getShopList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.page_size = pageSize;
			this.selectData = null;
			this.getShopList();
		},
		pageChange(page){
			this.search.page_no = page;
			this.selectData = null;
			this.getShopList();
		},
		getShopList(resolve){
			var postData = JSON.parse(JSON.stringify(this.search))

			if(this.params.setWhere){
				this.params.setWhere(postData)//在setWhere方法里面直接修改postData对象内容
			}

			this.ajax.postStream('/dealer-web/api/dealerFundsManage/findDaelerFundAdminPage', postData, d=>{
				if(d.body.result&&d.body.content){
					this.pageTotal = d.body.content.count;
					this.shopList = d.body.content.list||[];
					
				} else {
					this.$message.error(d.body.msg || '')
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			})
		},
		rowDblclick(obj) {
			this.toMoneyManagementDetail(obj)
		},
		// 跳转货款管理详情
		toMoneyManagementDetail (row){
			this.$root.eventHandle.$emit('creatTab',{
				name: "货款管理详情",
				params: {
					shop_name: row.shopName,
					shop_id  : row.shopId,
					isFromShopList: true,
					cusName: row.cusName,
					cusId: row.cusId,
				},
				component:()=>import('@components/dealer/money_management_detail'),
			})
		},
	},
	mounted() {
		this.getShopList();
	}
}
</script>
