<!-- 货款充值新增、编辑界面 -->
<template>
	<div class="xpt-flex rechange-info">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span="24" >
				<el-button type='primary' size='mini' @click="refresh" :disabled="!submit.id">刷新</el-button>
				<el-button type='primary' size='mini' @click="addNewOrder">新增</el-button>
				<el-button type='success' size='mini' @click="save()" :loading="saveLoading" :disabled="!(submit.order_status == 'CREATE' || submit.order_status == 'WITHDRAWED' || submit.order_status == 'RETRIAL' || submit.order_status =='LOCKED') ">保存</el-button>
				<el-button type='success' size='mini' @click="save(() => actionById('submit?permissionCode=DEALER_FUNDS_SUBMIT'))" :loading="saveLoading" :disabled="!(submit.order_status == 'CREATE' || submit.order_status == 'WITHDRAWED' || submit.order_status == 'RETRIAL') ">提交</el-button>
				<el-button type='success' size='mini' @click="actionById('withdraw?permissionCode=DEALER_FUNDS_WITHDRAW')" :disabled="!submit.id || submit.order_status !='SUBMITTED' ">撤销</el-button>
				<el-button type='success' size='mini' @click="actionById('check?permissionCode=DEALER_FUNDS_CHECK')" :disabled="!submit.id || submit.order_status !='SUBMITTED' || submit.check_status == 'CHECKED'  ">核对</el-button>
				<el-button type='success' size='mini' @click="actionById('uncheck?permissionCode=DEALER_FUNDS_UNCHECK')" :disabled="!submit.id || !(submit.check_status == 'CHECKED' && submit.order_status =='SUBMITTED') ">反核对</el-button>
				<el-button type='success' size='mini' @click="actionById('lock?permissionCode=DEALER_FUNDS_LOCK')" :disabled="!submit.id || !(submit.order_status == 'SUBMITTED' || submit.order_status == 'RETRIAL') ">锁定</el-button>
				<el-button type='warning' size='mini' @click="retrialFunc" :disabled="!submit.id ||submit.order_status != 'LOCKED'">驳回</el-button>
				<el-button type='primary' size='mini' @click="examine" :disabled="!submit.id ||submit.order_status != 'LOCKED'">审核</el-button>
				<el-button type='primary' size='mini' @click="actionById('invalid?permissionCode=DEALER_FUNDS_INVALID', {})" :disabled="!submit.id || !(submit.order_status == 'CREATE' || submit.order_status == 'WITHDRAWED' || submit.order_status =='RETRIAL')">作废</el-button>
				<el-button type='primary' size='mini' @click="rePushRecord" :disabled="!submit.id || submit.k3_push_status != 'FAIL'">重新推送</el-button>
				<el-button type='primary' size='mini' @click="selectAfterSale" :disabled="!(submit.order_status == 'CREATE' && submit.business_type == 'DEALER_PURCHASE_RETURN')">选单</el-button>
				<el-button type='primary' size='mini' @click="oaRemind" :disabled="!submit.id||isSaveShop" :loading="oaRemindLoading">OA提醒</el-button>
			</el-col>
		</el-row>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab">
		    	<el-tab-pane label="基本信息" name="basic">
			    	<xpt-form :data='submit' :cols='baseCols' :rules='rules' label='120px' ref='base'>
			    		<template slot='business_type'>
			    			<el-select v-model="submit.business_type" size="mini" @change="businessTypeChange" :disabled="!!detaiMsglList.length">
								<el-option
									v-for="(value, key) in business_type_options"
									:key="key.toString()"
									:label="value.toString()"
                                    v-show="params.type == ('CREDIT_RECHARGE' ?  key == 'CREDIT_RECHARGE' : key != 'CREDIT_RECHARGE')||!/^(AFTER_SALE_REBATE|BOOTH_REBATE)$/.test(key)"
									:value="key.toString()"
									:disabled="['RECHARGE','OTHER','INTETRAL_USED','KDZJ'].includes(key)">
								</el-option>
							</el-select>
							<el-tooltip v-if='rules.business_type[0].isShow' class="item" effect="dark" :content="rules.business_type[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    		<template slot='pay_time'>
							<el-date-picker
								v-model="submit.pay_time"
								type="datetime"
								placeholder="选择日期"
								size='mini'
								@change="payTimeChange"
								:editable="false"
								:disabled="submit.business_type != 'RECHARGE' || submit.order_status == 'LOCKED'"
							></el-date-picker>
							<el-tooltip v-if='rules.pay_time[0].isShow' class="item" effect="dark" :content="rules.pay_time[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    		<template slot='income_time'>
							<el-date-picker
								v-model="submit.income_time"
								type="datetime"
								placeholder="选择日期"
								size='mini'
								:editable="false"
								:disabled="submit.order_status !== 'LOCKED' || submit.business_type != 'RECHARGE' "
							></el-date-picker>
							<el-tooltip v-if='rules.income_time[0].isShow' class="item" effect="dark" :content="rules.income_time[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    		<template slot='currency'>
							<el-select v-model="submit.currency" size="mini" placeholder="请选择">
								<el-option
									v-for="(value,key) in currency_options"
									:key="key.toString()"
									:label="value.toString()"
									:value="key.toString()">
								</el-option>
							</el-select>
							<el-tooltip v-if='rules.currency[0].isShow' class="item" effect="dark" :content="rules.currency[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    		<template slot='shop_name'>
							<el-input size="mini" icon="search" :on-icon-click="selectShop" readonly v-model="submit.shop_name" ></el-input>
							<el-tooltip v-if='rules.shop_name[0].isShow' class="item" effect="dark" :content="rules.shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>

			    	</xpt-form>
			    </el-tab-pane>
			    <el-tab-pane label="其它信息"  name="otherInfo">
			    	<xpt-form  :data='submit' :cols='otherCols' label='130px'></xpt-form>
		    	</el-tab-pane>
			</el-tabs>
		</el-row>
		<el-row class='xpt-flex__bottom'>
			<el-tabs v-model="secondTab" @tab-click="tabChange" >
				<el-tab-pane label="明细信息" name="detail" class='xpt-flex'>
					<el-row class='xpt-top' :gutter='40'>
						<el-col :span='24' >
							<el-button type='primary' size='mini' @click="addDetailItem" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'">新增</el-button>

							<el-button type='danger' size='mini' @click="delDetailItem" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'">删除</el-button>
						</el-col>
					</el-row>
					<el-row class='xpt-flex__bottom scroll'>
						<el-table :data="detaiMsglList" border tooltip-effect="dark" style="width:100%;" width='100%' @selection-change="s => detailListSelect = s">
						  	<el-table-column width="28" align='center' type="selection"></el-table-column>
						    <el-table-column prop="pay_type" label="支付方式"   width='100'>
						    	<template slot-scope="scope">
						    		<xpt-select-aux
						    			v-model='scope.row.pay_type'
						    			aux_name='payTypeDealer'
						    			style="width:100%;"
						    			:disabledOption="
						    				submit.business_type != 'RECHARGE'
						    				? [
						    					{ code: 'BANK_TRANSFER_A' },
						    					{ code: 'ALIPAY' },
						    					{ code: 'WECHAT' },
						    					{ code: 'POS_A' },
						    					{ code: 'POS_B' },
						    					{ code: 'BANK_TRANSFER' }
						    				]
						    				: [{ code: 'OTHER' }]
						    			"
										@change="e => {

												getbankAccount(scope.row,(data,res)=>{
													$set(scope.row, 'bank_account', data)
													$set(scope.row, 'pay_account', res.recharge_account)
													$set(scope.row, 'payer_name', res.account_name)
													$set(scope.row, 'opening_bank', res.account_bank)
												})
											}"
										:disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"

						    		></xpt-select-aux>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="pay_amount" label="支付金额" width="100">
						    	<template slot-scope="scope">
						    		<el-input
						    			size="mini"
						    			v-model="scope.row.pay_amount"
						    			style="width:100%;"
						    			@change.native="e => {
											fixNumber(e, scope.row, 'pay_amount')
											$set(scope.row, 'paid_amount', getthousand(((getNum(scope.row.pay_amount) || 0) - getNum(scope.row.factorage)).toFixed(2)))
						    			}"
										:disabled="(submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL') || /^(LOGISTICS_CLAIM|KDZJ)$/.test(submit.business_type)"
						    		></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="factorage" label="手续费" width="70">
						    	<template slot-scope="scope">
						    		<el-input
						    			size="mini"
						    			v-model="scope.row.factorage"
						    			style="width:100%;"
						    			@change.native="e => {
											fixNumber(e, scope.row, 'factorage')
											$set(scope.row, 'paid_amount', getthousand(( ( getNum(scope.row.pay_amount) || 0) - getNum(scope.row.factorage)).toFixed(2)))
						    			}"
										:disabled="(submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL') || submit.business_type == 'CREDIT_RECHARGE'"
						    		></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="paid_amount" label="实收金额" width="100">
						    	<template slot-scope="scope">
						    		<el-input
						    			size="mini"
						    			class="w120"
						    			v-model="scope.row.paid_amount"
						    			style="width:100%;"
						    			@change.native="e => {
											fixNumber(e, scope.row, 'paid_amount');
											$set(scope.row, 'pay_amount', getthousand(((getNum(scope.row.paid_amount) || 0) + getNum(scope.row.factorage)).toFixed(2)))
						    			}"
										:disabled="(submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL') || /^(LOGISTICS_CLAIM|KDZJ)$/.test(submit.business_type)"
						    		></el-input>
						    	</template>
						    </el-table-column>
							<el-table-column prop="bank_account" label="我方银行账号" width='100'>
						    	<template slot-scope="scope">
						    		<el-input size='mini' v-model="scope.row.bank_account" readonly style="width:100%;"></el-input>
						    	</template>
						    </el-table-column>

						    <el-table-column prop="payer_name" label="付款人姓名"  width='200'>
						    	<template slot-scope="scope">
						    		<el-input size="mini"  v-model="scope.row.payer_name" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'" readonly></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="pay_account" label="支付账号" width='200'>
						    	<template slot-scope="scope">
						    		<el-input size="mini" v-model="scope.row.pay_account" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'" readonly></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="opening_bank" label="开户行" width="200">
						    	<template slot-scope="scope">
						    		<el-input size="mini"  v-model="scope.row.opening_bank" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'" readonly></el-input>
						    	</template>
						    </el-table-column>
							 <el-table-column prop="remark" label="备注" width="200">
						    	<template slot-scope="scope">
						    		<el-input size="mini" v-model="scope.row.remark" style="width:100%;" :disabled="/^(CREATE|LOCKED|WITHDRAWED)$/.test(submit.order_status) ? false: true"></el-input>
						    		<!-- <el-input size="mini" v-model="scope.row.remark" style="width:100%;" :disabled="submit.order_status == 'LOCKED' ? !(submit.locker == employeeId): true"></el-input> -->
						    	</template>
						    </el-table-column>
						    <el-table-column  label="附件" width="130">
						    	<template slot-scope="scope">
						    		<a href="javascript:;" @click="viewOrUpload(scope.row, scope.$index)">查看或上传附件</a>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="pay_transaction_no" label="支付流水号"  width='200'>
						    	<template slot-scope="scope">
						    		<el-input size="mini" class="w100" v-model="scope.row.pay_transaction_no" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"></el-input>
						    	</template>
						    </el-table-column>
                            <el-table-column prop="mobile" label="客户手机号"   width='120'>
						    	<template slot-scope="scope">
						    		<!-- <el-input size="mini"  v-model="scope.row.mobile" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"></el-input> -->
									<xpt-eye-switch v-model="scope.row.mobile" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'" :aboutNumber="submit.order_no"></xpt-eye-switch>
						    	</template>
						    </el-table-column>
					  	</el-table>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label='操作记录' name='operationRecord' class='xpt-flex'>
                    <xpt-operate :source_id="submit.id" api="/dealer-web/api/dealerFundsManageRecordDetail/logList" ref='operate'></xpt-operate>
                </el-tab-pane>
                <el-tab-pane label='退货明细' name='returnGoods' class='xpt-flex' v-if="submit.business_type == 'DEALER_PURCHASE_RETURN'">
                    <xpt-list
                        :colData='returnGoodsColData'
                        :btns="returnGoodsBtns"
                        isNeedClickEvent
                        :data="returnGoodsList.length ? returnGoodsList.concat(
                            {
                                act_price: '合计' + returnGoodsList.reduce((a, b) => {
                                    a += (Number(b.act_price) || 0)
                                    return a
                                }, 0).toFixed(2),
                                dealer_price: '合计' + returnGoodsList.reduce((a, b) => {
                                    a += (Number(b.dealer_price) || 0)
                                    return a
                                }, 0).toFixed(2),
                                id: '合计'
                            }
                        ) : returnGoodsList"
                        @selection-change='returnGoodsSelectionChange'
                        ref='xptList'
                        :selectable="selectableFun"
                    ></xpt-list>
                </el-tab-pane>
				<!-- 定制 来源明细 -->
				<el-tab-pane label='来源明细' name='source' class='xpt-flex' v-if="submit.business_type == 'DJBUJIAN'">
                    <xpt-list
                        :colData='dzSourceColData'
						:btns="[
							{
								type: 'primary',
								txt: '刷新',
								click: dzSourceInitCallback
							}
						]"
						:data="dzSourceList"
                    ></xpt-list>
                </el-tab-pane>
				<el-tab-pane label='服务索赔' name='compensate' class='xpt-flex' v-if="submit.business_type == 'LOGISTICS_CLAIM'">
                    <xpt-list
                        :colData='compensateColData'
						:btns="compensateBtns"
						:data="totalServiceClaimList"
						selection="none"
                    ></xpt-list>
                </el-tab-pane>
				<el-tab-pane label="货款充值单来源明细" name="detailRecharge" class="xpt-flex" v-if="submit.business_type == 'BUJIAN'">
					<xpt-list
						:data='sourceList'
						:colData='sourceColData'
						selection='radio'
						:btns="[
							{
								type: 'primary',
								txt: '刷新',
								click: sourceInitCallback
							}
						]"
					></xpt-list>
				</el-tab-pane>
			</el-tabs>
		</el-row>
		<xpt-image-v2
			:isGetImgList="isGetImgList"
			:show="isShow"
			:paramsInfo="paramsInfo"
			:ifUpload="true"
			:dataObj="dataObj"
			@close="xptImageClose">
		</xpt-image-v2>
	</div>
</template>
<script>
import validate from '@common/validate.js'
import fn from '@common/Fn.js'
// import add from './add.js'
export default {
	// mixins: [add],
	props:['params'],
	data() {
        let self = this;
		return {
		saveLoading:false,
            returnGoodsList: [],
            returnGoodsColData: [
                    {
                        prop:'bill_returns_no',
                        label:'退货跟踪单号',
                        width:170
                    },{
                        prop:'material_code',
                        label:'物料编码',
                        width:120
                    },{
                        prop:'material_name',
                        label:'物料名称',
                        width:150
                    },{
                        prop:'act_price',
                        label:'实际售价',
                        width:150
                    },{
                        prop:'dealer_price',
                        label:'经销价',
                        width:150
                    },{
                        label: "返货类型",
                        prop: "shipping_method",
                        width: 90,
                        formatter: prop =>
                            ({
                            THREE_BAG: "三包返货",
                            CUSTOMER: "客户自返",
                            RESALE: "退货再售",
                            DEALER: "经销退货",
                            INTERCEPT: "截货",
                            SCRAP: "报废",
                            DEALER_PICK: "经销自提",
                            CG_ESCRAP: "拉货前报废",
                            CG_SCRAP: "返货报废",
                            CG_LOST: "返货丢件",
                            CG_RETURN: "返货退件",
                            CG_WAIT: "返货待定",
                            CG_CANCEL: "取消退货",
                            PUR_DEALER: '经销采购退货'
                            }[prop] || prop)
                    },{
                        label: "退货方式",
                        prop: "returns_type",
                        format: "returnsType"
                    },{
                        prop:'bill_salesreturn_no',
                        label:'销售退货单号',
                        width:150
                    },{
                        prop:'bill_receivables_no',
                        label:'应收单号',
                        width:150
                    },{
                        prop:'finish_time',
                        label:'完结日期',
                        format: 'dataFormat1',
                        width: 130,
                    }
            ],
            returnGoodsSelect: [],
            returnGoodsBtns: [
                {
					type: 'danger',
                    txt: '删除退货明细',
                    disabled: false,
					click: self.deleteReturnGoods
				}
            ],
			firstTab: 'basic',
			secondTab: 'detail',
			isShow: false,
			dataObj: {},
			paramsInfo: {},
			isGetImgList: false,//是否立即获取图片
			business_type_options: {},
			detailListSelect: [],
			currency_options:fn.getAuxType('currency'),
			submit: {
				order_no: '',
				business_type: '',
				pay_time: new Date(),
				income_time: '',
				currency: 'CNY',
				order_status: 'CREATE',
				shop_name: '',
				shop_id: '',
				dealer_name: '',
				detailList: [],
				detailIdList: [],//标记保存过的明细行删除时的id
				creator_name: this.getEmployeeInfo('fullName'),
                check_status:'UNCHECK',
                is_deposit_payment:'0'
			},
			detaiMsglList: [],
			rules:{
				// pay_time:validate.isNotBlank({
				// 	self:this,
				// 	trigger:'change'
				// }),
				pay_time: [{
					required: true,
					validator: (rule, value, callback) => {
						// alert(1)
						// console.log(this.business_type_options)
						if(this.submit.business_type === 'RECHARGE' && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写支付日期',
				}],
				income_time: [{
					required: true,
					validator: (rule, value, callback) => {
						if(this.submit.business_type === 'RECHARGE' && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写到账日期',
				}],
				// income_time:validate.isNotBlank({
				// 	self:this,
				// 	trigger:'change'
				// }),
				business_type:validate.isNotBlank({
					self:this,
					trigger:'change'
				}),
				shop_name:validate.isNotBlank({
					self:this,
					trigger:'change'
				}),
				currency:validate.isNotBlank({
					self:this,
					trigger:'change'
				}),
			},
			baseCols: [
				[
					{
						label: '单据类型:',
						slot: 'business_type'
					}, {
						label: '单据编号:',
						key: 'order_no'
					}, {
						label: '充值店铺:',
						slot: 'shop_name'
					},{
                        label: '是否缴纳保证金:',
                        key: 'is_deposit_payment',
                        formatter(val){
                        if(!!val){
                            switch(val){
                                case '0':return '否';break;
                                case '1':return '是';break;
                                }
                        }
                    }
                    }
				], [
					{
						label: '客户:',
						key: 'dealer_name'
					}, {
						label: '支付日期:',
						slot: 'pay_time',
						disabled(){
							return this.submit.business_type === 'RECHARGE' ? false : true;
						}
					}, {
						label: '到账日期:',
						slot: 'income_time'
					}
				], [
					{
						label: '币别:',
						slot: 'currency'
					},{
						label: '单据状态:',
						key: 'order_status',
						formatter(val) {
							return fn.getStatus()[val];
						}

					},
                    {
                        label: '核对状态:',
                        key: 'check_status',
                        formatter(val) {
                        return fn.getCheckStatus()[val];
                        }
                    }
				]
			],
			otherCols: [
				[
					{
						label: '创建人:',
						key: 'creator_name'
					}, {
						label: '创建日期:',
						key: 'create_time',
						format: 'dataFormat1'
					}, {
						label: '锁定人:',
						key: 'locker_name'
					}, {
						label: '锁定日期:',
						key: 'lock_time',
						format: 'dataFormat1'
					}
				],[
					 {
						label: '财务中台收款同步状态:',
						key: 'k3_push_status_name'
					}, {
						label: '财务中台收款同步信息:',
						key: 'k3_push_msg'
					},{
						label: '财务中台最后推送时间:',
						key: 'k3_push_time',
						format: 'dataFormat'
					}/*, {
						label: '货款管理同步状态:',
						key: 'synMessage'
					}, {
						label: '货款管理同步信息:',
						key: 'synMessage'
					}*/
				], [
					{
						label: '审核人:',
						key: 'auditor_name'
					}, {
						label: '审核日期:',
						key: 'audit_time',
						format: 'dataFormat1'
					},{
						label: '驳回原因:',
						key: 'reject_reason'
					}

					// }, {
					// 	label: 'K3收款同步信息:',
					// 	key: 'synStatus',
					// 	formatter(val) {
					// 		return fn.synStatus()[val];
					// 	}
				],
			],
			// 禁掉选项
			ifpayTypeDealer:[],
            init_time_type:false,
            employeeId: null,
            otherParams: {
                where: [],
                page_name: '',
                gIds: []
            },
			//来源明细
			dzSourceColData: Object.freeze([
				{
				label: "责任分析子单",
				prop: "analysis_sub_no",
				width: "150",
				redirectClick(d) {
					self.$root.eventHandle.$emit('creatTab',{
					name: '责任分析单详情',
					params: {
						id: d.analysis_id,
						sub_bill_no: d.analysis_sub_no,
						orderList: []//JSON.parse(JSON.stringify(this._getOrderList.orderList)),
					},
					component:()=> import('@components/dz_customer/modules/afterSales/detailOfDuty_2')
					})
				},
				},
				{
					label: "责任单创建时间",
					prop: "analysis_sub_create_time",
					format: 'dataFormat1',
					width: 130,
				},
				{
				label: "买家昵称",
				prop: "client_name",
				width: 120,
				},
				{
				label: "经销商名称",
				prop: "shop_name",
				width: 120,
				},
				{
				label: "设计师",
				prop: "designer_name",
				width: 120,
				},
				{
				label: "子单来源单据类型",
				prop: "original_bill_type",
				width: 120,
				formatter: prop => ({
						SHBJSQD:"售后补件申请单",
						ZTBJSQD:"展厅补件",
						NXBJSQD:"内销补件",
						RESALE:"再售补件",
						BYWJBJ:"备用五金补件",
						DZBJ:"定制补件",
						XSJGSQ:"销售加购申请单",
						RETURNS: '退换货跟踪单',
						SUPPLY: '补件单',
						REFUND: '退款单',
						AFTERSALES: '售后单',
						REPAIR: '服务单',
						SERVICE: '4PL平台服务单',
						REFUND_NEW: '新退款单',
						QUALITY_FEEDBACK: '品质反馈单',
            HWBJ: "海外补件",
					}[prop]),
				},
				{
				label: "子单来源单号",
				prop: "original_bill_no",
				width: 150,
				redirectClick: (d) => {
					self.$root.eventHandle.$emit('creatTab',
							{
								name:'补件单详情',
								params:{
									id:d.original_bill_id,
									list: [],
									typeNumber: 1 //typeNumber=1为文员列表字段，2为业务员列表字段
								},
								component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')
							}
					)
				},
				},
				{
				label: "合并单号",
				prop: "merge_trade_no",
				width: 200,
				redirectClick: (row) => {
					var params = {};
					params.merge_trade_id = row.merge_trade_id;
					params.mergeTradeIdList = []
					params.ifOrder = true
					// 呼叫按钮来源
					params.callBtnfromType = 'sys_order'
					this.$root.eventHandle.$emit('creatTab',{
						name:"合并订单详情",
						params,
						component: () => import('@components/order/merge.vue')
					});
				},
				},
				{
					prop:'sale_out_stock_bill_no',
					label:'销售出库单号'
				},
				{
				label: "定制补件单号",
				prop: "client_number",
				width: 150,
				redirectClick: (row) => {
					self.$root.eventHandle.$emit('creatTab', {
						name: '补单详情',
						component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
						params: {
							client_number: row.client_number,
						}
					})
				},
				},
				{
				label: "锁定人",
				prop: "lock_name",
				width: 120,
				},
				{
				label: "责任类型",
				prop: "liability_type",
				width: 100,
				},
				{
				label: "责任问题",
				prop: "liability_question_name",
				width: 200,
				},
				{
				label: "商品编码",
				prop: "question_goods_code",
				width: 100,
				},
				{
				label: "商品名称",
				prop: "question_goods_name",
				width: 200,
				},

				{
				label: "规格描述",
				prop: "question_goods_desc",
				width: 100,
				},
				{
					label: "物料编码",
					prop: "material_code",
					width: 100,
				},
				{
					label: "物料名称",
					prop: "material_name",
					width: 100,
				},
				{
					label: "补件规格描述",
					prop: "description",
					width: 100,
				},
				{
				label: "责任金额",
				prop: "liability_amount",
				width: 100,
				},

			]),
			dzSourceList: [],
			//来源明细
			sourceColData: Object.freeze([
				{
				label: "补件单据编号",
				prop: "after_ticket_no",
				width: 150,
				// redirectClick: (d) => {
				// 	self.$root.eventHandle.$emit('creatTab',
				// 			{
				// 				name:'补件单详情',
				// 				params:{
				// 					id:d.original_bill_id,
				// 					list: [],
				// 					typeNumber: 1 //typeNumber=1为文员列表字段，2为业务员列表字段
				// 				},
				// 				component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')
				// 			}
				// 	)
				// },
				},
				{
					label: "合并单号",
					prop: "merge_trade_no",
					width: 200,

				},
				{
				label: "买家昵称",
				prop: "buyer_name",
				width: 120,
				},
				{
				label: "补件单据类型",
				prop: "type",
				width: 120,
				formatter: prop => ({SHBJSQD:'售后补件申请单',ZTBJSQD:'展厅补件',NXBJSQD:'内销补件',BCBJSQD:'摆场补件',RESALE: '再售补件', BYWJBJ : '备用五金补件',XSJGSQD:'销售加购申请单',DZBJ:'定制补件',HWBJ: "海外补件",}[prop]),
				},
				{
				label: "销售出库单",
				prop: "sale_out_stock_bill_no",
				width: 120,
				},


				{
				label: "运输成本",
				prop: "transport_cost",
				width: 120,
				},
				{
				label: "商品编码",
				prop: "question_goods_code",
				width: 100,
				},
				{
				label: "商品名称",
				prop: "question_goods_name",
				width: 200,
				},

				{
				label: "规格描述",
				prop: "question_goods_desc",
				width: 100,
				},
				{
					label: "物料编码",
					prop: "material_code",
					width: 100,
				},
				{
					label: "物料名称",
					prop: "material_name",
					width: 100,
				},
				{
					label: "补件规格描述",
					prop: "description",
					width: 100,
				},
				{
					label: "补件数量",
					prop: "number",
					width: 100,
				},
				{
					label: "补件采购成本",
					prop: "bought_price",
					width: 100,
				},
				{
					label: "加购成本",
					prop: "add_cost",
					width: 100,
				},
			]),
			sourceList: [],
			//服务索赔列
			compensateColData:[{
                prop:'after_ticket_no',
                label:'服务单号',
                width:130
            },{
                prop:'buyer_name',
                label:'买家昵称',
                width:170
            },{
                prop:'goods_code',
                label:'商品编码',
                width:120
            },{
                prop:'goods_name',
                label:'商品名称',
                width:130
            },{
                prop:'goods_desc',
                label:'规格描述',
                width:350
            },{
                prop:'question_description',
                label:'问题描述',
                width:150
            },{
                prop:'units',
                label:'单位',
                width:50
            },{
                prop:'number',
                label:'数量',
                width:50
            },{
                prop:'actual_price',
                label:'实际售价',
                width:120
            },{
                prop:'dealer_price',
                label:'经销价',
                width:120
            }],
			serviceClaimList:[],//服务索赔列表
			totalServiceClaimList:[],
			compensateBtns:[],
			//是否中台下推
			isPushFromMiddleground:false,

			//是否已经选择了店铺（已保存）
			isSaveShop:false,

			//oa提醒中
			oaRemindLoading:false,
			logisticsList:[]
		}
	},
	methods: {
        selectableFun(row, index) {
			// 控制是否可以勾选
			if('合计'===row.id) {
				return false;
			} else {
				return true;
			}
		},
		// 标签切换
		tabChange(tab) {
			console.log(tab)
			tab.index == 1 && this.$refs.operate.getOperater()

			//只初始化一次
			if(this[tab.name+'IsInited']) {
				return
			}
			this[tab.name+'IsInited'] = true
			// 初始化
			this[tab.name+'InitCallback'] && this[tab.name+'InitCallback'](tab)
			if(tab.name == 'source'){
				this.dzSourceInitCallback();
			}
			if(tab.name == 'detailRecharge'){
				this.sourceInitCallback();
			}
		},
		// 获取来源明细
		dzSourceInitCallback () {
            this.ajax.postStream('/custom-web/api/customAnalysisRelation/getAnalysisByPayWithMaterial', {
				dealer_funds_manage_record_no: this.submit.order_no
			}, res => {
                if(res.body.result){
                    this.dzSourceList  = res.body.content ? res.body.content : []
					// this.dzSourceList = dzSourceList.map(item => {
					// 	item.analysis_sub_create_time = fn.dateFormat(item.analysis_sub_create_time,  'yyyy-MM-dd hh:mm:ss')
					// 	return item
					// })
                    this.$message.success(res.body.msg)
                }else {
                    this.returnGoodsList = []
                    this.$message.error(res.body.msg)
                }
            }, error => {
                this.dzSourceList = []
                this.$message.error(error)
            })
        },
		// 获取来源明细
		sourceInitCallback () {
            this.ajax.get('/afterSale-web/api/aftersale/ticketSupply/getByRecharge/'+this.submit.order_no,  res => {
                if(res.body.result && res.body.content){
                    let sourceList = res.body.content.listPlanSupplyComponent;
					this.logisticsList = res.body.content.listOutboudVO;
					this.calcNumAndCost();
					let total = {
						transport_cost:this.transport_cost,
						bought_price:0,
						sale_out_stock_bill_no:'合计'
					}
					sourceList.forEach((item,index)=>{
						total.bought_price  = (total.bought_price*100+item.bought_price*100)/100
						if(index == 0){
							item.after_ticket_no = res.body.content.ticketSupply.after_ticket_no;
							item.merge_trade_no = res.body.content.ticketSupply.merge_trade_no;
							item.buyer_name = res.body.content.ticketSupply.buyer_name;
							item.type = res.body.content.ticketSupply.type;
							item.transport_cost = this.transport_cost;
						}else{
							item.after_ticket_no = '';
							item.merge_trade_no = '';
							item.buyer_name = '';
							item.type = '';
							item.sale_out_stock_bill_no = '';
							item.transport_cost = '';
						}
					})
                	this.sourceList = sourceList.concat([total]);

                    this.$message.success(res.body.msg)
                }else {
                    this.returnGoodsList = []
                    // this.$message.error(res.body.msg)
                }
            }, error => {
                this.sourceList = []
                this.$message.error(error)
            })
        },
		calcNumAndCost(){
			let num = 0,cost = 0;
			console.log('看看其它东西')
			//this.logisticsList = [{transportation_cost:5,delivery_quantity:1}]
			this.logisticsList.forEach((a,b)=>{
				let c = parseFloat(a.transportation_cost || 0);
				let d = parseFloat(a.delivery_quantity || 0);
				num += d;
				cost += c;
			});

			this.delivery_quantity = num;
			this.transport_cost = cost;
		},
        // 获取退货明细
        getPurDealersRetrunGoods () {
            this.ajax.postStream('/dealer-web/api/dealerFundsManageRecordDetail/getPurDealersRetrunGoods', this.submit.id, res => {
                if(res.body.result){
                    this.returnGoodsList = res.body.content || []
                    this.$message.success(res.body.msg)
                }else {
                    this.returnGoodsList = []
                    this.$message.error(res.body.msg)
                }
            }, error => {
                this.returnGoodsList = []
                this.$message.error(error)
            })
        },
        returnGoodsSelectionChange (data) {
            this.returnGoodsSelect = data
        },
        // 删除退货明细
        deleteReturnGoods () {
            if (this.returnGoodsSelect.length ==0) {
                this.$message.error('请选择要删除的退货明细')
            }
            let params = []
            this.returnGoodsSelect.forEach(item => {
                params.push(item.id)
            })
            this.ajax.postStream('/dealer-web/api/dealerFundsManageRecordDetail/deleteReturnGoods', params, res => {
                if(res.body.result){
                    this.$message.success(res.body.msg)
                }else {
                    this.$message.error(res.body.msg)
                }
                this.refresh()
            }, error => {
                this.refresh()
                this.$message.error(res.body.msg)
            })
        },
        selectAfterSale () {
            if(this.submit.shop_name == ''){
				this.$message.error('请先选择充值店铺')
				return
            }
            let self = this
            let params = {
                ifNew: this.submit.id ? false : true,
                shopId: this.submit.shop_id,
                callback: function (d) {
                    self.otherParams = {
                        where: d.where,
                        page_name: d.page_name,
                        gIds: d.gIds,
                        ifChoose: d.ifChoose
                    }
                }
            }
            this.$root.eventHandle.$emit('alert', {
                style:'width:800px;height:500px',
                title:'选单列表',
                params:params,
                component: () => import('@components/dealer/selectAfterSale.vue')
            });
        },
		oaRemind(){
			this.oaRemindLoading=true;
			this.ajax.postStream("/dealer-web/api/dealerFundsManageRecordDetail/sendDingTalk",this.submit.id,res=>{
				if(res.body.result){
					this.$message.success(res.body.msg)
				}else{
					this.$message.error(res.body.msg)
				}
				this.oaRemindLoading=false;
			},err=>{
				this.$message.error(err)
				this.oaRemindLoading=false;
			})
		},
		businessTypeChange (){
			this.rules.pay_time[0].required = this.rules.income_time[0].required = this.submit.business_type === 'RECHARGE'
			if(!this.submit.id){
				this.submit.pay_time = this.submit.income_time = ''
			}
		},
		payTimeChange(){
			if(this.init_time_type == true){
				// console.log(111);
				this.init_time_type = false;
				return false;
			}
			this.submit.income_time = this.submit.pay_time;
		},
		fixNumber (e, row, key){
			row[key] = row[key].split(',').join('')
			row[key] = Number((Number(row[key]) || 0).toFixed(2))
			row[key] = this.getthousand(row[key])
			e.target.value = row[key] ;
		},
		getNum(data){
			if(!data){
				return 0
			}
			return Number(data.toString().split(',').join(''));
		},
		getthousand(data){
			if(data == '' || data == null){
				return data
			}
			let Negative = '';
			if(Number(data)<0){
				Negative = '-';
			}
			let changeData = Math.abs(data.toString().split(',').join('')).toString();
			let splitArr = [];
			splitArr = changeData.split('.');

			let leftArr = splitArr[0].split('').reverse();
			let rightArr =  splitArr[1] || '';
			let newArr = []
			leftArr.forEach((item,index) => {
				newArr.push(item)
				if((index + 1) % 3 == 0 && (index + 1) != leftArr.length){
                	newArr.push(",");
            	}
			})
			newArr = newArr.reverse();
			let returnData = splitArr.length == 1 ? newArr.join(''):(newArr.join('')+'.'+rightArr);
			return Negative + returnData;
		},
		addNewOrder (){
			this.$root.eventHandle.$emit('creatTab', {
				name: '新增货款充值',
				params: {},
				component: () => import('@components/dealer/recharge_of_goods_detail'),
			})
		},
		retrialFunc (){
			this.$root.eventHandle.$emit('alert',{
				params: {
					callback: data => {
						this.actionById('retrial?permissionCode=DEALER_FUNDS_REJECT', { reject_reason: data.data })
					}
				},
				component:()=>import('@components/receipt/reject_receipt'),
				style:'width:600px;height:200px',
				title:'请输入驳回理由'
			})
		},
		rePushRecord(){
			this.ajax.postStream('/dealer-web/api/dealerFundsManageRecord/rePushRecord2K3', this.submit.id, res => {
					// cb && cb('');
					if(res.body.result){
						this.$message.success(res.body.msg)

					}else {
						this.$message.error(res.body.msg)
					}
				})
		},
		xptImageClose (imgList){
			Object.defineProperties(this.detaiMsglList[this.viewOrUpload.rowIndex], {
			    __imgListLength: {
			        configurable: true,
			        writable: true,
			        enumerable: false,
			        value: imgList.length,
			    }
			})
			this.isShow = false
			this.isGetImgList = false
		},
		/**
		*查看附件事件
		*/
		viewOrUpload(row, rowIndex){
			this.viewOrUpload.rowIndex = rowIndex
			this.isShow = true;
			this.isGetImgList = true;
			this.paramsInfo.parent_no = this.dataObj.parent_no = this.submit.order_no;
			this.paramsInfo.child_no = this.dataObj.child_no = row.record_detail_id;
			this.paramsInfo.cloud_file_id = null;
			this.dataObj.parent_name = 'ORDER';
			this.dataObj.child_name = 'DEALERFUNDSMANAGE';
			this.dataObj.content = JSON.parse(JSON.stringify(row));
		},
		// 新增行
		addDetailItem (){
			if(!this.submit.business_type){
				this.$message.error('请先选择单据类型')
			}else {
				this.detaiMsglList.push({
					isAdd: true,
					pay_type: '',
					factorage: 0,
					remark:this.submit.business_type=='RECHARGE'?'销售收款':''
					// bank_account:this.getbankAccount()
				})
				this.getRecordDetailIdWhenIsAdd()
			}
		},
		// 获取银行账号
		getbankAccount(data,cb){
			let _this = this;
			if(this.submit.shop_name == ''){
				this.$message.error('请先选择充值店铺')
				return k3_bank_account;
			}
			console.log(_this.submit.shop_id);
			if(this.submit.business_type === 'RECHARGE' ){
				this.ajax.postStream('/material-web/api/shopv2/getShopBankAccount', {shop_id:_this.submit.shop_id,pay_type:data.pay_type}, res => {
					// cb && cb('');
					let k3_bank_account = '';

					if(res.body.result){

						if(JSON.stringify(res.body.content) != 'null'){
							// return account.k3_bank_account;
							if (res.body.content.disabled_status == 0){
								// cb && cb(account.k3_bank_account);
								k3_bank_account = res.body.content.k3_bank_account;
							}
						}
						// console.log(data,{k3_bank_account:k3_bank_account})

						_this.getaccuntInfo(data,k3_bank_account,cb);
					}else {
						this.$message.error(res.body.msg)
						// cb && cb('');
					}
				})
			}

		},
		// 获取对应店铺、支付方式的充值账号列表
		getaccuntInfo(data,k3_bank_account,cb){
			this.ajax.postStream('/dealer-web/api/dealerRechargeAccount/modeList', {shop_id:this.submit.shop_id,payment_mode:data.pay_type}, res => {
				// cb && cb('');
				if(res.body.result){
					// this.submit.id = res.body.content.id
					// this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, res.body.msg)
					// cb && cb()
					// console.log(JSON.stringify(res.body.content) == 'null',JSON.stringify(res.body.content))
					let resData = '';
					if(JSON.stringify(res.body.content) != 'null'){
						resData = res.body.content;

					}
					// console.log(data,resData)
					if(resData instanceof Array){
						if(resData.length != 0){
							this.$root.eventHandle.$emit('alert',{
							component:()=>import('@components/dealer/account_list.vue'),

								style:'width:800px;height:600px;z-index:1000;',
								title:'请选择银行账号',
								params:{
									resData :resData,
									close:function(data){
										cb && cb(k3_bank_account,data);
										console.log(data)
									},
								}
							});
						}else{
							cb && cb(k3_bank_account,{recharge_account:'', account_name:'', account_bank:''});
						}

					}else{
						cb && cb(k3_bank_account,resData);

					}
				}else {
					this.$message.error(res.body.msg)
					cb && cb('');
				}
			})
		},
		// 删除行
		delDetailItem (){
			if(!this.detailListSelect.length){
				this.$message.error('请选择至少一行')
			}else {
				this.detailListSelect.forEach(obj => {
					if(!obj.isAdd){
						if(!this.submit.detailIdList) this.submit.detailIdList = []
						this.submit.detailIdList.push(obj.record_detail_id)
					}
					this.detaiMsglList.splice(this.detaiMsglList.indexOf(obj), 1)
				})
			}
		},
		/**选择店铺列表**/
		selectShop(){
			if(!!this.detaiMsglList.length && !this.isPushFromMiddleground){
				this.$message.error('有明细行时不能修改充值店铺');
				return false;
			}
			if(this.isPushFromMiddleground){
				this.$root.eventHandle.$emit('alert', {
				params: {
					selection: 'radio',
					shop_status: 'OPEN',
					customer_source_id:this.submit.dealer_id,
					callback: data => {
						console.log(data)
						this.submit.shop_id = data.shop_id
						this.submit.shop_name = data.shop_name
					},
				},
				component: ()=>import('@components/dealer/recharge_of_goods_detail_shop_alert'),
				style: 'width:800px;height:500px',
				title: '店铺列表',
			})
			}else{
				this.$root.eventHandle.$emit('alert', {
				params: {
					selection: 'radio',
					shop_status: 'OPEN',
					callback: data => {
						this.submit.shop_id = data.shop_id
						this.submit.shop_name = data.shop_name
						this.submit.dealer_name = data.customer_source_name
						this.submit.dealer_id = data.customer_source_id
					},
				},
				component: ()=>import('@components/shop/list'),
				style: 'width:800px;height:500px',
				title: '店铺列表',
			})
			}
		},
		checkValidate (cb){
			this.$refs.base.validate(() => {
				if (!this.detaiMsglList.length){
					this.$message.error('请添加至少一行明细行')
				}else if(
					this.detaiMsglList.some((obj, index) => {
						var msg

						if(!obj.pay_type || !obj.pay_amount || !obj.paid_amount){
							msg = '支付方式、支付金额、实收金额必填'
						}else if ((obj.isAdd && !obj.__imgListLength || obj.__imgListLength === 0)&&this.submit.business_type === 'RECHARGE'&& !this.isPushFromMiddleground){
							msg = '第' + (index + 1) + '行明细至少上传一张附件'
						}else if(this.submit.business_type === 'RECHARGE' && obj.pay_amount < 0){
							msg = '货款充值时支付金额不能为负数'
						}else if (this.submit.business_type === 'RECHARGE' && (!obj.payer_name || !obj.pay_account || !obj.opening_bank || !obj.bank_account)){
							msg = '货款充值时付款人姓名、支付账号、开户行、银行账户必填'
						}

						if(msg){
							this.$message.error(msg)
							return true
						}
					})
				){
					/*empty*/
				}else {
					cb()
				}
			})
		},
		getRecordDetailIdWhenIsAdd (cb){
			var isAddCount = 0
			,	ajaxCount = 0

			this.detaiMsglList.forEach(obj => {
				if(obj.isAdd && !obj.record_detail_id){
					++isAddCount

					// ++ajaxCount
					// obj.record_detail_id='***********'
					// if(ajaxCount === isAddCount){
					// 	cb()
					// }
					// return

					this.ajax.get('/dealer-web/api/dealerFundsManageRecordDetail/getRecordDetailId', res => {
						if(res.body.result){
							++ajaxCount
							obj.record_detail_id = res.body.content

							if(ajaxCount === isAddCount){
								cb && cb()
							}
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}
			})

			if(!isAddCount){
				cb && cb()
			}
		},
		getOrderNo (cb){
			if(this.submit.order_no){
				cb && cb()
			}else {
				this.ajax.get('/dealer-web/api/dealerFundsManageRecordDetail/getOrderNo', res => {
					if(res.body.result){
						this.submit.order_no = res.body.content
            this.init_time_type = true;
            this.getDealerUserInfo('add')
						cb && cb()
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}
    },
    getDealerUserInfo (type) {
      this.ajax.get('/dealer-web/api/dealerFundsManageRecordDetail/getDealerUserInfo', res => {
        if(res.body.result){
          if (res.body.content) {
            if (type === 'add') {
              let data = res.body.content
              if(data.length === 1){
                  this.submit.shop_id = data[0].shop_id
                  this.submit.shop_name = data[0].shop_name
                  this.submit.dealer_name = data[0].dealer_name
                  this.submit.dealer_id = data[0].dealer_id
              }
            }
          }
        }else {
          // this.$message.error(res.body.msg)
        }
      })
    },
		// 审核
		examine(){
			let self = this;
			this.save(function(){
        self.aduitCheck({})
			},"examine")
    },
    aduitCheck(postData) {
      let self = this
      this.ajax.postStream(
				'/dealer-web/api/dealerFundsManageRecordDetail/aduitCheck',[this.submit.id],
				res => {
					if(res.body.result){
						if(res.body.content && res.body.content < 0){
							self.$root.eventHandle.$emit('openDialog',{
                txt:'当前充值资金池将会为负数，是否审核？',
                okTxt:'确认',
                cancelTxt:'取消',
                noShowNoTxt:true,
                ok(){
                  self.actionById('audit?permissionCode=DEALER_FUNDS_AUDIT', {})
                },
                cancel(){}
              })
						}else {
							self.actionById('audit?permissionCode=DEALER_FUNDS_AUDIT', {})
						}
					}else {
						this.$message.error(res.body.msg)
					}
				}
      )
    },
		actionById (apiName, postData, msg, id){
			this.ajax.postStream(
				'/dealer-web/api/dealerFundsManageRecordDetail/' + apiName,
				postData ? Object.assign({ id: this.submit.id }, postData) : (id || this.submit.id),
				res => {
					if(res.body.result){
						if(/^listDetail/.test(apiName)){
							this.submit = res.body.content;
							if (this.submit.business_type == 'KDZJ' && !(this.baseCols[2].some(item => item.label == '来源单号:'))) {
								this.baseCols[2].push(
									{
										label: '来源单号:',
										key: 'original_bill_no'
									}
								)
							}
							this.detaiMsglList = res.body.content.detailList;
							this.$nextTick(()=>{
								this.serviceClaimList=res.body.content.serviceClaimList||[];//服务单索赔列表
								this.totalServiceClaimList=this.formatServiceClaimList(this.serviceClaimList);//加上汇总价
							})
							this.isPushFromMiddleground = !!res.body.content.out_source_bill_no;
							if(this.isPushFromMiddleground&&!this.baseCols[1].some(item=>item.key=='out_source_bill_no')){
								this.baseCols[1].push({
									key:'out_source_bill_no',
									label:'中台单据体-结算号:'
								})
							}
							this.isSaveShop=!!res.body.content.shop_name;
							// console.log(this.detailListSelect)
                             // this.$message.success(msg || res.body.msg)
                                if (this.submit.business_type == 'DEALER_PURCHASE_RETURN'){
                                this.returnGoodsBtns[0].disabled = /^(APPROVED|INVALID)$/.test(this.submit.order_status)
                                this.getPurDealersRetrunGoods()
                            }
              this.init_time_type = true;
              this.getDealerUserInfo()
						}else {
							this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, res.body.msg)
						}
					}else {
						this.$message.error(res.body.msg)
					}
					this.detaiMsglList.forEach(item =>{
								item.pay_amount =this.getthousand(item.pay_amount);
								item.paid_amount =this.getthousand(item.paid_amount);
								item.factorage =this.getthousand(item.factorage);
							})
				}
            )
		},
		save (cb,val = null){

			let flag = false;
			this.detaiMsglList.forEach(item =>{
				if(!item.remark){
					flag = true;
				}
			})
			if(!!flag){
				this.$message.error('明细信息行备注不能为空');


				return false;
			}

			// this.submit.order_no='123123'
			this.checkValidate(() => {//先检验
				this.getOrderNo(() => {//获取单据编号
					this.getRecordDetailIdWhenIsAdd(() => {//明细行有新增时，往新增行添加record_detail_id
						this.detaiMsglList.forEach(item =>{
							item.pay_amount = this.getNum(item.pay_amount);
							item.factorage = this.getNum(item.factorage);
							item.paid_amount = this.getNum(item.paid_amount);
						})
                        this.submit.detailList = this.detaiMsglList;
                        let params = {}, self = this
                        if (this.submit.business_type == 'DEALER_PURCHASE_RETURN') {
                            params = Object.assign({}, this.submit, this.otherParams)
                            if (!this.submit.id) {
                                delete params.ifChoose
                            }
                        } else {
                            params = Object.assign({}, this.submit)

							//来源单号保存与未保存取值
							if (this.submit.business_type == 'LOGISTICS_CLAIM'){
								params.original_bill_no=this.submit.id?this.submit.original_bill_no:this.params.after_ticket_no;
								params.serviceClaimList=this.serviceClaimList;
							}

							// 快递转寄的充值单，需要带上来源单号
							if (this.submit.business_type == 'KDZJ') {
								params.original_bill_no = this.submit.original_bill_no;
							}
                            console.log('params', params)
                            delete params.ifChoose
                            delete params.gIds
                            delete params.where
                            delete params.page_name
                            console.log('paramsthis.submit', this.submit)
                        }
						this.saveLoading = true;

						this.ajax.postStream('/dealer-web/api/dealerFundsManageRecordDetail/save?permissionCode=DEALER_FUNDS_SAVE', params, res => {
							this.saveLoading = false;
							if(res.body.result){
								this.submit.id = res.body.content.id
								// this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, res.body.msg)
                                this.init_time_type = false;
                                let params={
                                    id: res.body.content.id
                                };
                                params.__close = function(){
                                    self.$root.eventHandle.$emit('removeTab',self.params.tabName);
                                }
                                if(!val){
                                  this.$root.eventHandle.$emit('updateTab',{
                                    name:self.params.tabName,
                                    params:params,
                                    title:'货款充值详情',
                                    component: () => import('@components/dealer/recharge_of_goods_detail')
                                  })
                                }
								cb && cb()
							}else {
								this.$message.error(res.body.msg)
							}
						})
					})
				})
			})
		},
		refresh() {
			this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, null, this.params.id)
		},
		formatServiceClaimList(list){
			if(list.length==0){
				return []
			}
			let obj={}
						obj.actual_price='合计：'+list.reduce((a,b)=>{
							return a+(Number(b.actual_price)||0)
						},0).toFixed(2)
						obj.dealer_price='合计：'+list.reduce((a,b)=>{
							return a+(Number(b.dealer_price)||0)
						},0).toFixed(2)
			return list.concat([{...obj}])
		}
    },
    created () {
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
			this.employeeId = this.getEmployeeInfo('id')
		});
	},
	mounted (){
        this.business_type_options = fn.getAuxType2('rechangeBusinessType')
        this.employeeId = this.getEmployeeInfo('id')
		if(this.params.isFromMoneyManagementDetail){
			this.submit.shop_name = this.params.shop_name
			this.submit.shop_id = this.params.shop_id
			// this.submit.dealer_name = this.params.cusName
			this.submit.dealer_id = this.params.customer_source_id
			this.submit.dealer_name = this.params.customer_source_name
			// this.submit.customer_source_id = this.params.customer_source_id
		}
		if(this.params.order_no){
			this.submit.order_no=this.params.order_no
			this.sourceInitCallback()
		}

		//从下推货款充值跳转
		if(this.params.isFromPaymentRechargePush){
			this.submit.business_type=this.params.paymentRechargeObj.business_type
			this.submit.dealer_name=this.params.paymentRechargeObj.dealer_name
			this.submit.dealer_id=this.params.paymentRechargeObj.dealer_id
			this.submit.shop_id=this.params.paymentRechargeObj.shop_id
			this.submit.shop_name=this.params.paymentRechargeObj.shop_name
			this.$nextTick(()=>{
				this.serviceClaimList=this.params.paymentRechargeObj.serviceClaimList||[]
				this.totalServiceClaimList=this.formatServiceClaimList(this.serviceClaimList);//加上汇总价
			})
			if (this.submit.business_type == 'KDZJ' && !(this.baseCols[2].some(item => item.label == '来源单号:'))) {
				this.submit.original_bill_no = this.params.after_ticket_no
				this.baseCols[2].push(
					{
						label: '来源单号:',
						key: 'original_bill_no'
					}
				)
			}
			this.getRecordDetailIdWhenIsAdd(()=>{
				this.detaiMsglList=this.params.paymentRechargeObj.detailList.length>0&&this.params.paymentRechargeObj.detailList.map(item=>{
					return {
						isAdd: true,
						pay_type: item.pay_type,
						factorage: 0,
						remark:'',
						pay_amount:item.pay_amount,
						paid_amount:item.paid_amount,
					}
				})
			})
		}

		if(this.params.id){
			this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, null, this.params.id)
		}else {
			this.getOrderNo()
		}

		// if(this.submit.business_type == 'BUJIAN'){
		// }
	},
}
</script>