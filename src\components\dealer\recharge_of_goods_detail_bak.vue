<!-- 货款充值新增、编辑界面 -->
<template>
	<div class="xpt-flex">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span="24" >
				<el-button type='primary' size='mini' @click="addNewOrder">新增</el-button>
				<el-button type='success' size='mini' @click="save()" :disabled="submit.order_status == 'INVALID'">保存</el-button>
				<el-button type='success' size='mini' @click="save(() => actionById('submit?permissionCode=DEALER_FUNDS_SUBMIT'))" :disabled="submit.order_status == 'INVALID'">提交</el-button>
				<el-button type='success' size='mini' @click="actionById('withdraw?permissionCode=DEALER_FUNDS_WITHDRAW')" :disabled="!submit.id ||submit.order_status == 'INVALID'">撤销</el-button>
				<el-button type='success' size='mini' @click="actionById('lock?permissionCode=DEALER_FUNDS_LOCK')" :disabled="!submit.id ||submit.order_status == 'INVALID'">锁定</el-button>
				<el-button type='warning' size='mini' @click="retrialFunc" :disabled="!submit.id ||submit.order_status == 'INVALID'">驳回</el-button>
				<el-button type='primary' size='mini' @click="examine" :disabled="!submit.id ||submit.order_status == 'INVALID'">审核</el-button>
				<el-button type='primary' size='mini' @click="actionById('invalid?permissionCode=DEALER_FUNDS_INVALID', {})" :disabled="!submit.id ||submit.order_status == 'INVALID'">报废</el-button>
				<el-button type='primary' size='mini' @click="rePushRecord" :disabled="!submit.id ||submit.k3_push_status != 'FAIL'">重新推送</el-button>
			</el-col>
		</el-row>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab">
		    	<el-tab-pane label="基本信息" name="basic">
			    	<xpt-form :data='submit' :cols='baseCols' :rules='rules' label='100px' ref='base'>
			    		<template slot='business_type'>
			    			<el-select v-model="submit.business_type" size="mini" @change="businessTypeChange" :disabled="!!detaiMsglList.length">
								<el-option 
									v-for="(value, key) in business_type_options" 
									:key="key.toString()" 
									:label="value.toString()"
									:value="key.toString()">
								</el-option>
							</el-select>
							<el-tooltip v-if='rules.business_type[0].isShow' class="item" effect="dark" :content="rules.business_type[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    		<template slot='pay_time'>
							<el-date-picker 
								v-model="submit.pay_time"
								type="datetime"
								placeholder="选择日期"
								size='mini'
								@change="payTimeChange"
								:editable="false"
								:disabled="submit.business_type != 'RECHARGE' || submit.order_status == 'LOCKED'"
							></el-date-picker>
							<el-tooltip v-if='rules.pay_time[0].isShow' class="item" effect="dark" :content="rules.pay_time[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    		<template slot='income_time'>
							<el-date-picker
								v-model="submit.income_time"
								type="datetime"
								placeholder="选择日期"
								size='mini'
								:editable="false"
								:disabled="submit.order_status !== 'LOCKED' || submit.business_type != 'RECHARGE' "
							></el-date-picker>
							<el-tooltip v-if='rules.income_time[0].isShow' class="item" effect="dark" :content="rules.income_time[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    		<template slot='currency'>
							<el-select v-model="submit.currency" size="mini" placeholder="请选择">
								<el-option
									v-for="(value,key) in currency_options"
									:key="key.toString()"
									:label="value.toString()"
									:value="key.toString()">
								</el-option>
							</el-select>
							<el-tooltip v-if='rules.currency[0].isShow' class="item" effect="dark" :content="rules.currency[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    		<template slot='shop_name'>
							<el-input size="mini" icon="search" :on-icon-click="selectShop" readonly v-model="submit.shop_name"></el-input>
							<el-tooltip v-if='rules.shop_name[0].isShow' class="item" effect="dark" :content="rules.shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
			    		</template>
			    	</xpt-form>
			    </el-tab-pane>
			    <el-tab-pane label="其它信息" name="otherInfo">
			    	<xpt-form :data='submit' :cols='otherCols' label='120px'></xpt-form>
		    	</el-tab-pane>
			</el-tabs>
		</el-row>
		<el-row class='xpt-flex__bottom'>
			<el-tabs v-model="secondTab" @tab-click="tab => tab.index == 1 && $refs.operate.getOperater()">
				<el-tab-pane label="明细信息" name="detail" class='xpt-flex'>
					<el-row class='xpt-top' :gutter='40'>
						<el-col :span='24' >
							<el-button type='primary' size='mini' @click="addDetailItem" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'">新增</el-button>
										
							<el-button type='danger' size='mini' @click="delDetailItem" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'">删除</el-button>
						</el-col>
					</el-row>
					<el-row class='xpt-flex__bottom scroll'>
						<el-table :data="detaiMsglList" border tooltip-effect="dark" style="width:100%;" width='100%' @selection-change="s => detailListSelect = s">
						  	<el-table-column width="28" align='center' type="selection"></el-table-column>
						    <el-table-column prop="pay_type" label="支付方式"   width='100'>
						    	<template slot-scope="scope">
						    		<xpt-select-aux
						    			v-model='scope.row.pay_type'
						    			aux_name='payTypeDealer'
						    			style="width:100%;"
						    			:disabledOption="
						    				submit.business_type != 'RECHARGE'
						    				? [
						    					{ code: 'BANK_TRANSFER_A' },
						    					{ code: 'ALIPAY' },
						    					{ code: 'WECHAT' },
						    					{ code: 'POS_A' },
						    					{ code: 'POS_B' },
						    					{ code: 'BANK_TRANSFER' }
						    				]
						    				: [{ code: 'OTHER' }]
						    			"
										@change="e => {
											
												getbankAccount(scope.row,(data,res)=>{
													$set(scope.row, 'bank_account', data)
													$set(scope.row, 'pay_account', res.recharge_account)
													$set(scope.row, 'payer_name', res.account_name)
													$set(scope.row, 'opening_bank', res.account_bank)
												})
											}"
										:disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"

						    		></xpt-select-aux>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="pay_amount" label="支付金额" width="100">
						    	<template slot-scope="scope">
						    		<el-input
						    			size="mini"
						    			v-model="scope.row.pay_amount"
						    			style="width:100%;"
						    			@change.native="e => {
											fixNumber(e, scope.row, 'pay_amount')
											$set(scope.row, 'paid_amount', getthousand(((getNum(scope.row.pay_amount) || 0) - getNum(scope.row.factorage)).toFixed(2)))
						    			}"
										:disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"
						    		></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="factorage" label="手续费" width="100">
						    	<template slot-scope="scope">
						    		<el-input
						    			size="mini" 
						    			v-model="scope.row.factorage"
						    			style="width:100%;"
						    			@change.native="e => {
											fixNumber(e, scope.row, 'factorage')
											$set(scope.row, 'paid_amount', getthousand(( ( getNum(scope.row.pay_amount) || 0) - getNum(scope.row.factorage)).toFixed(2)))
						    			}"
										:disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"
						    		></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="paid_amount" label="实收金额" width="100">
						    	<template slot-scope="scope">
						    		<el-input
						    			size="mini"
						    			class="w120"
						    			v-model="scope.row.paid_amount"
						    			style="width:100%;"
						    			@change.native="e => {
											fixNumber(e, scope.row, 'paid_amount');
											$set(scope.row, 'pay_amount', getthousand(((getNum(scope.row.paid_amount) || 0) + getNum(scope.row.factorage)).toFixed(2)))
						    			}"
										:disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"
						    		></el-input>
						    	</template>
						    </el-table-column>
							<el-table-column prop="bank_account" label="我方银行账号" width='100'>
						    	<template slot-scope="scope">
						    		<el-input size='mini' v-model="scope.row.bank_account" readonly style="width:100%;"></el-input>
						    	</template>
						    </el-table-column>
						    
						    <el-table-column prop="payer_name" label="付款人姓名"  width='250'>
						    	<template slot-scope="scope">
						    		<el-input size="mini"  v-model="scope.row.payer_name" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'" readonly></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="pay_account" label="支付账号" width='200'>
						    	<template slot-scope="scope">
						    		<el-input size="mini" v-model="scope.row.pay_account" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'" readonly></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="opening_bank" label="开户行" width="200">
						    	<template slot-scope="scope">
						    		<el-input size="mini"  v-model="scope.row.opening_bank" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'" readonly></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="mobile" label="客户手机号"   width='120'>
						    	<template slot-scope="scope">
						    		<el-input size="mini"  v-model="scope.row.mobile" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column  label="附件" width="130">
						    	<template slot-scope="scope">
						    		<a href="javascript:;" @click="viewOrUpload(scope.row, scope.$index)">查看或上传附件</a>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="pay_transaction_no" label="支付流水号"  width='200'>
						    	<template slot-scope="scope">
						    		<el-input size="mini" class="w100" v-model="scope.row.pay_transaction_no" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"></el-input>
						    	</template>
						    </el-table-column>
						    <el-table-column prop="remark" label="备注" width="200">
						    	<template slot-scope="scope">
						    		<el-input size="mini" v-model="scope.row.remark" style="width:100%;" :disabled="submit.order_status !== 'CREATE' && submit.order_status !== 'WITHDRAWED'&& submit.order_status !== 'RETRIAL'"></el-input>
						    	</template>
						    </el-table-column>
					  	</el-table>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label='操作记录' name='operationRecord' class='xpt-flex'>
                    <xpt-operate :source_id="submit.id" api="/dealer-web/api/dealerFundsManageRecordDetail/logList" ref='operate'></xpt-operate>
                </el-tab-pane>
			</el-tabs>
		</el-row>
		<xpt-image-v2
			:isGetImgList="isGetImgList"
			:show="isShow"
			:paramsInfo="paramsInfo"
			:ifUpload="true"
			:dataObj="dataObj"
			@close="xptImageClose">
		</xpt-image-v2>
	</div>
</template>
<script>
import validate from '@common/validate.js'
import fn from '@common/Fn.js'
// import add from './add.js'
export default {
	// mixins: [add],
	props:['params'],
	data() {
		return {
			firstTab: 'basic',
			secondTab: 'detail',
			isShow: false,
			dataObj: {},
			paramsInfo: {},
			isGetImgList: false,//是否立即获取图片
			business_type_options: fn.getAuxType2('rechangeBusinessType'),
			detailListSelect: [],
			currency_options:fn.getAuxType('currency'),
			submit: {
				order_no: '',
				business_type: '',
				pay_time: new Date(),
				income_time: '',
				currency: 'CNY',
				order_status: 'CREATE',
				shop_name: '',
				shop_id: '',				
				dealer_name: '',
				detailList: [],
				detailIdList: [],//标记保存过的明细行删除时的id
				creator_name: this.getEmployeeInfo('fullName'),
			},
			detaiMsglList: [],
			rules:{
				// pay_time:validate.isNotBlank({
				// 	self:this,
				// 	trigger:'change'
				// }),
				pay_time: [{
					required: true,
					validator: (rule, value, callback) => {
						// alert(1)
						console.log(this.business_type_options)
						if(this.submit.business_type === 'RECHARGE' && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写支付日期',
				}],
				income_time: [{
					required: true,
					validator: (rule, value, callback) => {
						if(this.submit.business_type === 'RECHARGE' && !value){
							this.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}else {
							this.rules[rule.field][0].isShow = false
							callback()
						}
					},
					trigger: 'blur',
					isShow: false,
					message: '请填写到账日期',
				}],
				// income_time:validate.isNotBlank({
				// 	self:this,
				// 	trigger:'change'
				// }),
				business_type:validate.isNotBlank({
					self:this,
					trigger:'change'
				}),
				shop_name:validate.isNotBlank({
					self:this,
					trigger:'change'
				}),
				currency:validate.isNotBlank({
					self:this,
					trigger:'change'
				}),
			},
			baseCols: [
				[
					{
						label: '单据类型:',
						slot: 'business_type'
					}, {
						label: '单据编号:',
						key: 'order_no'
					}, {
						label: '充值店铺:',
						slot: 'shop_name'
					}
				], [
					{
						label: '客户:',
						key: 'dealer_name'
					}, {
						label: '支付日期:',
						slot: 'pay_time',
						disabled(){
							return this.submit.business_type === 'RECHARGE' ? false : true;
						}
					}, {
						label: '到账日期:',
						slot: 'income_time'
					}
				], [
					{
						label: '币别:',
						slot: 'currency'
					}, {
						label: '单据状态:',
						key: 'order_status',
						formatter(val) {
							return fn.getStatus()[val];
						}
					}
				]
			],
			otherCols: [
				[
					{
						label: '创建人:',
						key: 'creator_name'
					}, {
						label: '创建日期:',
						key: 'create_time',
						format: 'dataFormat1'
					}, {
						label: '锁定人:',
						key: 'locker_name'
					}, {
						label: '锁定日期:',
						key: 'lock_time',
						format: 'dataFormat1'
					}
				],[
					 {
						label: '财务中台收款同步状态:',
						key: 'k3_push_status_name'
					}, {
						label: '财务中台收款同步信息:',
						key: 'k3_push_msg'
					},{
						label: '财务中台最后推送时间:',
						key: 'k3_push_time',
						format: 'dataFormat'
					}/*, {
						label: '货款管理同步状态:',
						key: 'synMessage'
					}, {
						label: '货款管理同步信息:',
						key: 'synMessage'
					}*/
				], [
					{
						label: '审核人:',
						key: 'auditor_name'
					}, {
						label: '审核日期:',
						key: 'audit_time',
						format: 'dataFormat1'
					},{
						label: '驳回原因:',
						key: 'reject_reason'
					}
					
					// }, {
					// 	label: '财务中台收款同步信息:',
					// 	key: 'synStatus',
					// 	formatter(val) {
					// 		return fn.synStatus()[val];
					// 	}
				],
			],
			// 禁掉选项
			ifpayTypeDealer:[],
			init_time_type:false
		}
	},
	methods: {
		businessTypeChange (){
			this.rules.pay_time[0].required = this.rules.income_time[0].required = this.submit.business_type === 'RECHARGE'
			if(!this.submit.id){
				this.submit.pay_time = this.submit.income_time = ''
			}
		},
		payTimeChange(){
			if(this.init_time_type == true){
				// console.log(111);
				this.init_time_type = false;
				return false;
			}
			this.submit.income_time = this.submit.pay_time;
		},
		fixNumber (e, row, key){
			// console.log(e)
			row[key] = row[key].split(',').join('')
			console.log(row[key])
			row[key] = Number((Number(row[key]) || 0).toFixed(2))
			// this.getthousand(row[key])
			row[key] = this.getthousand(row[key])
			e.target.value = row[key] ;
		},
		getNum(data){
			return Number(data.toString().split(',').join(''));
		},
		getthousand(data){
			// 数字获取千分位
			// data = data.split(',').join('')
			if(data == '' || data == null){
				return data
			}
			let Negative = '';
			if(Number(data)<0){
				Negative = '-';
			}
			data = Math.abs(data);
			let changeData = data.toString().split(',').join('');
			let splitArr = [];
			splitArr = changeData.split('.');
			console.log(splitArr.length == 1)
			
			let leftArr = splitArr[0].split('').reverse();
			let rightArr =  splitArr[1] || '';
			let newArr = []
			leftArr.forEach((item,index) => {
				newArr.push(item)
				if((index + 1) % 3 == 0 && (index + 1) != leftArr.length){
                	newArr.push(",");
            	}
			})
			newArr = newArr.reverse();
			// console.log(newArr.join('')+'.'+rightArr);
			let returnData = splitArr.length == 1 ? newArr.join(''):(newArr.join('')+'.'+rightArr);
			console.log(returnData,rightArr)
			return Negative + returnData;
			// return returnData;
		},
		addNewOrder (){
			this.$root.eventHandle.$emit('creatTab', {
				name: '新增货款充值',
				params: {},
				component: () => import('@components/dealer/recharge_of_goods_detail'),
			})
		},
		retrialFunc (){
			this.$root.eventHandle.$emit('alert',{
				params: {
					callback: data => {
						this.actionById('retrial?permissionCode=DEALER_FUNDS_REJECT', { reject_reason: data.data })
					}
				},
				component:()=>import('@components/receipt/reject_receipt'),
				style:'width:600px;height:200px',
				title:'请输入驳回理由'
			})
		},
		rePushRecord(){
			this.ajax.postStream('/dealer-web/api/dealerFundsManageRecord/rePushRecord2K3', this.submit.id, res => {
					// cb && cb('');
					if(res.body.result){
						this.$message.success(res.body.msg)
						
					}else {
						this.$message.error(res.body.msg)
					}
				})
		},
		xptImageClose (imgList){
			Object.defineProperties(this.detaiMsglList[this.viewOrUpload.rowIndex], {
			    __imgListLength: {
			        configurable: true,
			        writable: true,
			        enumerable: false,
			        value: imgList.length,
			    }
			})
			this.isShow = false
			this.isGetImgList = false
		},
		/**
		*查看附件事件
		*/
		viewOrUpload(row, rowIndex){
			this.viewOrUpload.rowIndex = rowIndex
			this.isShow = true;
			this.isGetImgList = true;
			this.paramsInfo.parent_no = this.dataObj.parent_no = this.submit.order_no;
			this.paramsInfo.child_no = this.dataObj.child_no = row.record_detail_id;
			this.paramsInfo.cloud_file_id = null;
			this.dataObj.parent_name = 'ORDER';
			this.dataObj.child_name = 'DEALERFUNDSMANAGE';
			this.dataObj.content = JSON.parse(JSON.stringify(row));
		},
		// 新增行
		addDetailItem (){
			if(!this.submit.business_type){
				this.$message.error('请先选择单据类型')
			}else {
				this.detaiMsglList.push({
					isAdd: true,
					pay_type: '',
					factorage: 0,
					// bank_account:this.getbankAccount()
				})
				this.getRecordDetailIdWhenIsAdd()
			}
		},
		// 获取银行账号
		getbankAccount(data,cb){
			let _this = this;
			if(this.submit.shop_name == ''){
				this.$message.error('请先选择充值店铺')
				return k3_bank_account;
			}
			console.log(_this.submit.shop_id);
			if(this.submit.business_type === 'RECHARGE' ){
				this.ajax.postStream('/material-web/api/shopv2/getShopBankAccount', {shop_id:_this.submit.shop_id,pay_type:data.pay_type}, res => {
					// cb && cb('');
					let k3_bank_account = '';

					if(res.body.result){
						
						if(JSON.stringify(res.body.content) != 'null'){
							// return account.k3_bank_account;
							if (res.body.content.disabled_status == 0){
								// cb && cb(account.k3_bank_account);
								k3_bank_account = res.body.content.k3_bank_account;
							}
						}
						// console.log(data,{k3_bank_account:k3_bank_account})
						
						_this.getaccuntInfo(data,k3_bank_account,cb);
					}else {
						this.$message.error(res.body.msg)
						// cb && cb('');
					}
				})
			}
			
		},
		// 获取对应店铺、支付方式的充值账号列表
		getaccuntInfo(data,k3_bank_account,cb){
			this.ajax.postStream('/dealer-web/api/dealerRechargeAccount/modeList', {shop_id:this.submit.shop_id,payment_mode:data.pay_type}, res => {
				// cb && cb('');
				if(res.body.result){
					// this.submit.id = res.body.content.id
					// this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, res.body.msg)
					// cb && cb()
					// console.log(JSON.stringify(res.body.content) == 'null',JSON.stringify(res.body.content))
					let resData = '';
					if(JSON.stringify(res.body.content) != 'null'){
						resData = res.body.content;
						
					}
					// console.log(data,resData)
					if(resData instanceof Array){
						if(resData.length != 0){
							this.$root.eventHandle.$emit('alert',{
							component:()=>import('@components/dealer/account_list.vue'),
							
								style:'width:800px;height:600px;z-index:1000;',
								title:'请选择银行账号',
								params:{
									resData :resData,
									close:function(data){
										cb && cb(k3_bank_account,data);
										console.log(data)
									},
								}
							});
						}else{
							cb && cb(k3_bank_account,{recharge_account:'', account_name:'', account_bank:''});
						}
						 
					}else{
						cb && cb(k3_bank_account,resData);
						
					}
				}else {
					this.$message.error(res.body.msg)
					cb && cb('');
				}
			})
		},
		// 删除行
		delDetailItem (){
			if(!this.detailListSelect.length){
				this.$message.error('请选择至少一行')
			}else {
				this.detailListSelect.forEach(obj => {
					if(!obj.isAdd){
						if(!this.submit.detailIdList) this.submit.detailIdList = []
						this.submit.detailIdList.push(obj.record_detail_id)
					}
					this.detaiMsglList.splice(this.detaiMsglList.indexOf(obj), 1)
				})
			}
		},
		/**选择店铺列表**/
		selectShop(){
			this.$root.eventHandle.$emit('alert', {
				params: {
					selection: 'radio',
					shop_status: 'OPEN',
					callback: data => {
						this.submit.shop_id = data.shop_id
						this.submit.shop_name = data.shop_name
						this.submit.dealer_name = data.customer_source_name
						this.submit.dealer_id = data.customer_source_id
					},
				},
				component: ()=>import('@components/shop/list'),
				style: 'width:800px;height:500px',
				title: '店铺列表',
			})
		},
		checkValidate (cb){
			this.$refs.base.validate(() => {
				if (!this.detaiMsglList.length){
					this.$message.error('请添加至少一行明细行')
				}else if(
					this.detaiMsglList.some((obj, index) => {
						var msg

						if(!obj.pay_type || !obj.pay_amount || !obj.paid_amount || !(obj.remark || '').trim()){
							msg = '支付方式、支付金额、实收金额、备注必填'							
						}else if ((obj.isAdd && !obj.__imgListLength || obj.__imgListLength === 0)&&this.submit.business_type === 'RECHARGE'){
							msg = '第' + (index + 1) + '行明细至少上传一张附件'
						}else if(this.submit.business_type === 'RECHARGE' && obj.pay_amount < 0){
							msg = '货款充值时支付金额不能为负数'
						}else if (this.submit.business_type === 'RECHARGE' && (!obj.payer_name || !obj.pay_account || !obj.opening_bank || !obj.bank_account)){
							msg = '货款充值时付款人姓名、支付账号、开户行、银行账户必填'
						}

						if(msg){
							this.$message.error(msg)
							return true
						}
					})
				){
					/*empty*/
				}else {
					cb()
				}
			})
		},
		getRecordDetailIdWhenIsAdd (cb){
			var isAddCount = 0
			,	ajaxCount = 0

			this.detaiMsglList.forEach(obj => {
				if(obj.isAdd && !obj.record_detail_id){
					++isAddCount

					// ++ajaxCount
					// obj.record_detail_id='***********'
					// if(ajaxCount === isAddCount){
					// 	cb()
					// }
					// return

					this.ajax.get('/dealer-web/api/dealerFundsManageRecordDetail/getRecordDetailId', res => {
						if(res.body.result){
							++ajaxCount
							obj.record_detail_id = res.body.content

							if(ajaxCount === isAddCount){
								cb && cb()
							}
						}else {
							this.$message.error(res.body.msg)
						}
					})
				}
			})

			if(!isAddCount){
				cb && cb()
			}
		},
		getOrderNo (cb){
			if(this.submit.order_no){
				cb && cb()
			}else {
				this.ajax.get('/dealer-web/api/dealerFundsManageRecordDetail/getOrderNo', res => {
					if(res.body.result){
						this.submit.order_no = res.body.content
						this.init_time_type = true;
						cb && cb()
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}
		},
		// 审核
		examine(){
			let self = this;
			this.save(function(){
				self.actionById('audit?permissionCode=DEALER_FUNDS_AUDIT', {});
			})
		},
		actionById (apiName, postData, msg, id){
			this.ajax.postStream(
				'/dealer-web/api/dealerFundsManageRecordDetail/' + apiName,
				postData ? Object.assign({ id: this.submit.id }, postData) : (id || this.submit.id),
				res => {
					if(res.body.result){
						if(/^listDetail/.test(apiName)){
							this.submit = res.body.content;
							this.detaiMsglList = res.body.content.detailList;
							// console.log(this.detailListSelect)
							this.$message.success(msg || res.body.msg)
							this.init_time_type = true;
						}else {
							this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, res.body.msg)
						}
					}else {
						this.$message.error(res.body.msg)
					}
					this.detaiMsglList.forEach(item =>{
								item.pay_amount =this.getthousand(item.pay_amount);
								item.paid_amount =this.getthousand(item.paid_amount);
								item.factorage =this.getthousand(item.factorage);
							})
				}
			)
		},
		save (cb){
			// this.submit.order_no='123123'
			this.checkValidate(() => {//先检验
				this.getOrderNo(() => {//获取单据编号
					this.getRecordDetailIdWhenIsAdd(() => {//明细行有新增时，往新增行添加record_detail_id
						this.detaiMsglList.forEach(item =>{
							item.pay_amount = this.getNum(item.pay_amount);
							item.factorage = this.getNum(item.factorage);
							item.paid_amount = this.getNum(item.paid_amount);
						})
						console.log(this.submit)
						this.submit.detailList = this.detaiMsglList;
						this.ajax.postStream('/dealer-web/api/dealerFundsManageRecordDetail/save?permissionCode=DEALER_FUNDS_SAVE', this.submit, res => {
							if(res.body.result){
								this.submit.id = res.body.content.id
								this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, res.body.msg)
								this.init_time_type = false;
								cb && cb()
							}else {
								this.$message.error(res.body.msg)
							}
						})
					})
				})
			})
		},
	},
	mounted (){
		if(this.params.isFromMoneyManagementDetail){
			this.submit.shop_name = this.params.shop_name
			this.submit.shop_id = this.params.shop_id
			// this.submit.dealer_name = this.params.cusName
			this.submit.dealer_id = this.params.customer_source_id
			this.submit.dealer_name = this.params.customer_source_name
			// this.submit.customer_source_id = this.params.customer_source_id
		}

		if(this.params.id){
			this.actionById('listDetail?permissionCode=DEALER_FUNDS_QUERY', null, null, this.params.id)
		}else {
			this.getOrderNo()
		}
	},
}
</script>
