<!-- 弹窗选择店铺列表 -->
<template>
	<div class='xpt-flex'>
        <el-row class="xpt-top" :gutter="40">
        <el-col :span="24">
            <el-button
            type="primary"
            size="mini"
            @click="close"
            :disabled="!selectData"
            >确定</el-button
            >
        </el-col>
        </el-row>
		<xpt-list
			:data='shopList'
			:btns='btns'
			:colData='cols'
			:searchPage='search.page_name'
			:pageTotal='pageTotal'
			:selection='selection'
			:showHead='showHead'
			:pageSizes='[10]'
            :pageLength="10"
			:isNeedClickEvent="showHead"
			@search-click='searchData'
			@radio-change='radioChange'
			@selection-change='selectionChange'
			@page-size-change='pageSizeChange'
			@current-page-change='pageChange'
		></xpt-list>

	</div>
</template>
<script>
export default {
	data(){
		let self = this
		return {
			btns: [],
			cols: [{
                  label: '店铺编码',
                  prop: 'shop_code'
              }, {
                  label: '店铺名称',
                  prop: 'shop_name'
              }, {
                  label: '店铺分类',
                  prop: 'shop_type',
                  format: 'auxFormat',
                  formatParams: 'shopClassify'
              },{
                  label: '店铺分组',
                  format: 'auxFormat',
                  prop: 'shop_group',
                  formatParams: 'shopGroup'
              }, {
                  label: '所属公司',
                  prop: 'belong_company'
              }, {
                  label: '店铺仓库',
                  prop: 'shop_warehouse_name'
              }, {
                  label: '客户简称',
                  prop: 'cust_short_name'
              }
			],
			search:{
				page_name: 'cloud_shop_v2',
				where: [],
				page: {
					length: 10,
					pageNo: 1
				}
			},
			shopList:[],
			selectData:'',
			pageTotal:0,
			selection: 'checkbox',
			showHead: false,
		}
	},
	/*
	params.selection：选择框状态，默认为多选；如需单选，请传值radio
	params.shop_status 店铺状态,"OPEN为生效的店铺"
	*/
	props:['params'],
	methods:{
		radioChange(data){
			this.selectData = data;
		},
		selectionChange(data) {
			this.selectData = data;
		},
		close(){
			if(!this.selectData){
				this.$message.error('请先选择一个店铺');
				return;
			}
			this.params.callback(this.selectData);
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		searchData(obj, resolve){
			this.search.where = obj;
			this.selectData = null;
			this.getShopList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.page.length = pageSize;
			this.selectData = null;
			this.getShopList();
		},
		pageChange(page){
			this.search.page.pageNo = page;
			this.selectData = null;
			this.getShopList();
		},
		getShopList(resolve){
			this.refreshBtnStatus = true;

			var postData = JSON.parse(JSON.stringify(this.search))

			if(this.params.setWhere){
				this.params.setWhere(postData)//在setWhere方法里面直接修改postData对象内容
			}

			this.ajax.postStream('/material-web/api/shopv2/list?permissionCode=SHOP_QUERY', postData, d=>{
				if(d.body.result&&d.body.content){
					this.pageTotal = d.body.content.count;
					this.shopList = d.body.content.list||[];
					
				} else {
					this.$message.error(d.body.msg || '')
				}
				this.refreshBtnStatus = false;
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
				this.refreshBtnStatus = false;
			})
		},
	},
	mounted() {
		if(this.params.selection) {
			this.selection = this.params.selection;
		}
        // 过滤失效的店铺
		if(this.params.shop_status) {
			this.search.shop_status = this.params.shop_status;
		}
        //根据客户选择对应的店铺
        if(this.params.customer_source_id) {
			this.search.customer_source_id = this.params.customer_source_id;
		}
		this.getShopList();
	}
}
</script>
