<!--货款充值列表 或 货款管理详情明细行-->
<template>
	<xpt-list
		:data='roleList'
		:colData='colData'
		:btns="btns"
		selection='checkbox'
		:pageTotal='pageTotal'
		:searchPage="searchParams.page_name"
		isNeedClickEvent
		@search-click='searchClick'
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
		@row-dblclick="d => openOrder(d.id, d.order_no)"
        :unShowWhere="unShowWhere"
        :unShowFields="unShowFields"
        ref='rechargeOfGoodsList'
		@selection-change="handleSelectionChange"
	>
		<xpt-upload-v3
            slot="btns"
            uploadBtnText="导入"
            :uploadSize="20"
            acceptTypeStr=".xlsx,.xls"
            :dataObj="uploadDataObj"
            :disabled="false"
            :ifMultiple="false"
            :showSuccessMsg="false"
            @uploadSuccess="uploadSuccess"
            btnType="success"
            style="display: inline-block; margin: 0px 10px"
        ></xpt-upload-v3>
        <el-button
            slot="btns"
            size="mini"
            type="warning"
            @click="showUploadResult()"
            :disabled="false"
            >导入结果</el-button
        >
	</xpt-list>
</template>

<script>
import fn from '@common/Fn.js'
export default {
	props: ['params', 'shopId'/*如果shopId存在则表示货款管理详情页面*/],
	data (){
		let self = this
		return {
			roleList:[],

			btns: [
                {
                    type: 'primary',
                    txt: '刷新',
                    click: () => this.searchFun(),
                },{
                    type: 'primary',
                    txt: '新增',
                    click: this.addNewOrder,
                },{
					type: 'primary',
					txt: '导出',
					click: () => this.exportExcel(),
				},{
					type: 'primary',
					txt: '查看导出结果',
					click: () => this.showExportList(),
				},{
					type: 'primary',
					txt: '提交',
					click: () => this.actionByIds('submit'),
					loading:false,
				},{
					type: 'primary',
					txt: '核对',
					click: () => this.actionByIds('batchCheck'),
					loading:false,
				},{
					type: 'primary',
					txt: '锁定',
					click: () => this.actionByIds('lock'),
					loading:false,
				},{
					type: 'primary',
					txt: '审核',
					click: () => this.aduitCheck('audit'),
					loading:false,
				}],
			ListTotal: {
				pay_amount: '合计0',
				factorage: '合计0',
				paid_amount: '合计0',

			},
			searchParams:{
				page_name: this.shopId ? 'dealer_funds_manage_record' : 'dealer_funds_manage_record_detail',
				where: [],
				page: {
					length: 50,
					pageNo: 1,
				},
				dealer_id: '',
				shop_id: this.shopId,
			},

			pageTotal:0,
			colData: this.shopId
                ? [
                {
                    label: '关联单据单号',
                    prop: 'order_no',
                    width: 200,
                    redirectClick: d => this.openOrder(d.id, d.order_no),
                },{
                    label: '买家昵称',
                    prop: 'buy_nick',
                },{
                    label: '单据类型',
                    prop: 'business_type',
                    // formatter: prop => ({
                    // 	RECHARGE: '货款充值',
                    // 	ADJUSTMENT: '结算与激励',
                    // 	DELIVER: '发货',
                    // 	COMMIT: '提交订单',
                    // 	ORDER_ADD: '变更订单增加',

                    // 	ORDER_SUBTRACT: '变更订单扣减',
                    // }[prop] || prop),
                    format:'auxFormat',
                    formatParams:'rechangeBusinessType'

                },{
                    label: '交易变动金额',
                    prop: 'change_amount',
                    formatter(val){
                        return self.getthousand(val)
                    }
                },{
                    label: '货款原可用余额',
                    prop: 'order_status',
                    formatter: (prop, index, row) => {
                        if(prop == ''){
                            return '';
                        }
                        return self.getthousand(((row.payment_amount || 0) - (row.change_amount || 0)).toFixed(2))
                    },
                },{
                    label: '货款可用余额',
                    prop: 'payment_amount',
                    formatter(val){
                        return self.getthousand(val)
                    }
                },{
                    label: '货款冻结金额',
                    prop: 'freeze_payment_amount',
                    formatter(val){
                        return self.getthousand(val)
                    }
                },{
                    label: '操作时间',
                    prop: 'create_time',
                    format: 'dataFormat1',
                    width: 150,
                },{
                    label: '操作人',
                    prop: 'creator_name',
                },{
                    label: '到账日期',
                    prop: 'income_time',
                    format: 'dataFormat1',
                    width: 150,
                }]
                : [{
                    label: '单据编号',
                    prop: 'order_no',
                    width: 200,
                    redirectClick: d => this.openOrder(d.id, d.order_no),
                },{
                    label: '充值店铺',
                    prop: 'shop_name',
                    width: 100,

                },{
                    label: '是否缴纳保证金',
                    prop: 'is_deposit_payment',
                    width: 100,
                    formatter(val){
                        if(!!val){
                            switch(val){
                                case '0':return '否';break;
                                case '1':return '是';break;
                                }
                        }
                    },
                },{
                    label: '店铺分类',
                    prop: 'shop_type',
                    width: 100,
                    format:'auxFormat',
                    formatParams:'shopClassify'
                },{
                    label: '单据类型',
                    prop: 'business_type_name',
                    // formatter: prop => ({
                    // 	RECHARGE: '货款充值',
                    // 	ADJUSTMENT: '结算与激励',
                    // 	DELIVER: '发货',
                    // 	COMMIT: '提交订单',
                    // 	ORDER_ADD: '变更订单增加',
                    // 	ORDER_SUBTRACT: '变更订单扣减',
                    // }[prop] || prop),
                    // format: 'auxFormat',
                    // formatParams: 'rechangeBusinessType',
                    format:'auxFormat',
                    formatParams:'rechangeBusinessType'

                },{
                    label: '客户',
                    prop: 'dealer_name',
                    width: 200,
                },{
                    label: '支付日期',
                    prop: 'pay_time',
                    format: 'dataFormat1',
                    width: 150,
                },{
                    label: '到账日期',
                    prop: 'income_time',
                    format: 'dataFormat1',
                    width: 150,
                },{
                    label: '支付方式',
                    prop: 'pay_type',
                    format: 'auxFormat',
                    formatParams: 'payTypeDealer',
                },{
                    label: '支付金额',
                    prop: 'pay_amount',
                    formatter(val){
                        return self.getthousand(val)
                    }
                },{
                    label: '手续费',
                    prop: 'factorage',
                    formatter(val){
                        return self.getthousand(val)
                    }
                },{
                    label: '实收金额',
                    prop: 'paid_amount',
                    formatter(val){
                        return self.getthousand(val)
                    }
                },{
                    label: '我方银行账号',
                    prop: 'bank_account',
                },{
                    label: '同步状态',
                    prop: 'k3_push_status_name',
                },{
                    label: '付款人名字',
                    prop: 'payer_name',
                },{
                    label: '创建人',
                    prop: 'creator_name',
                },{
                    label: '创建日期',
                    prop: 'create_time',
                    format: 'dataFormat1',
                    width: 150,
                },{
                    label: '单据状态',
                    prop: 'order_status',
                    formatter: prop => (fn.getStatus()[prop] || prop),
                }, {
                    label: '核对状态',
                    prop: 'check_status',
                    formatter(val){
                        return fn.getCheckStatus()[val]
                    },
                },{
                    label: '审核人',
                    prop: 'auditor_name',
                    width: 150,
                },{
                    label: '审核日期',
                    prop: 'audit_time',
                    format: 'dataFormat1',
                    width: 150,
                },{
                    label: '币别',
                    prop: 'currency',
                    width: 150,
                    format: 'auxFormat',
                    formatParams: 'currency',

                },{
                    label: '积分类型',
                    prop: 'integral_type',
                    width: 150,
                    format: 'auxFormat',
                    formatParams: 'dealer_integral_type',

                },{
                    label: '备注',
                    prop: 'remarkd',
                    width: 150,
                }
            ],
            unShowFields:[],
            unShowWhere:[],
			uploadDataObj: {
                parent_name: "货款充值列表",
                parent_no: `recharge_of_goods_list`, //主要通过该参数获取附件列表
                child_name: null,
                child_no: null,
                content: {},
            },
			multipleSelection:[]
        }
	},
	methods: {
		addNewOrder (){
			this.$root.eventHandle.$emit('creatTab', {
				name: this.params.menuInfo.code === "RECHARGE_BUSINESS_EALER_FUNDS" ? '新增信用货款充值' : '新增货款充值',
				params: this.params.menuInfo.code === "RECHARGE_BUSINESS_EALER_FUNDS" ? {type: 'CREDIT_RECHARGE'} : {},
				component: () => import('@components/dealer/recharge_of_goods_detail'),
			})
		},
		searchClick(obj, resolve){
			this.searchParams.where = obj
			this.searchFun(resolve)
		},
		_ListTotal(){
			var totalObj =  {
				pay_amount: 0,
				factorage: 0,
				paid_amount: 0,
				change_amount:0,
				freeze_payment_amount:0
			}
			,	totalKey = Object.keys(totalObj)

			this.roleList.forEach(obj => {
				if(obj.status !== 'CANCEL'){//行状态是取消的不统计在合计
					totalKey.forEach(key => {
						if(obj[key]){
							totalObj[key] += Number(obj[key])
						}
					})
				}
			})
			// console.log(totalObj)
			totalKey.forEach(key => {
				totalObj[key] =  + totalObj[key].toFixed(2)
			})
			totalObj.order_status = '';
			totalObj.order_no = '合计';
			this.ListTotal = totalObj;
      this.roleList = this.roleList.concat(this.ListTotal)
		},
		searchFun (resolve, shopId){
			let url = '/dealer-web/api/dealerFundsManageRecordDetail/list', self = this
			if(shopId){
				this.shopId = shopId
				this.searchParams.shop_id = shopId
				url = '/dealer-web/api/dealerFundsManageRecordDetail/list?permissionCode=DEALER_FUNDS_QUERY'
            }
            if (this.params && this.params.menuInfo && this.params.menuInfo.code === "RECHARGE_BUSINESS_EALER_FUNDS") {
                this.searchParams.where.push({"field":"b64b382644c732ef77022f93eb679444","table":"f167aeacf7fd2b38e51d9c1b746279aa","value":"CREDIT_RECHARGE","operator":"=","condition":"AND","listWhere":[]})
            }
			console.log(url)
			this.ajax.postStream(url, this.searchParams, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
					if (self.params && self.params.menuInfo && (self.params.menuInfo.code === "DEALER_FUNDS_LIST" || self.params.menuInfo.code === "DEALER_FUNDS_MANAGE_RECORD_L")) {
						self.roleList = res.body.content.list || []
						self._ListTotal()
					} else {
						var totalObj =  {
							order_no: '合计',
							order_status: '',
							change_amount: res.body.content.amount.sum_change_amount || 0,
							freeze_payment_amount: res.body.content.amount.sum_freeze_payment_amount || 0

						};
						self.roleList = res.body.content.list || []
						if(self.roleList.length > 0){
							self.roleList = res.body.content.list.concat(totalObj);
						}
					}

					// if(!this.shopId){//货款充值列表仅展示货款充值、结算与激励的数据
					// 	this.roleList = this.roleList.filter(obj => /^(RECHARGE|ADJUSTMENT)$/.test(obj.business_type))
					// }

					this.pageTotal = res.body.content.count
				}else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve()
			}, () => {
				resolve && resolve()
			}, this.params && this.params.tabName)
		},
		openOrder (id, order_no){
			if(order_no == '合计'){
				return false;
			}
			if(/^HB/.test(order_no)){
				this.openOrder.where[0].value = order_no
				this.ajax.postStream('/order-web/api/scmsystrade/list', {
					"page_name": "scm_sys_trade",
					"where": this.openOrder.where,
					"page_size": 50,
					"page_no": 1
				}, res => {
					if(res.body.result){
						this.$root.eventHandle.$emit('creatTab',{
							name: "合并订单详情",
							params: { merge_trade_id: res.body.content.list[0].merge_trade_id },
							component: () => import('@components/order/merge.vue')
						})
					}
				})
			}else {
                this.$root.eventHandle.$emit('creatTab', {
					order_no:order_no,
					name: this.params.menuInfo.code === "RECHARGE_BUSINESS_EALER_FUNDS" ? '信用货款充值详情' : '货款充值详情',
					params: this.params.menuInfo.code === "RECHARGE_BUSINESS_EALER_FUNDS" ? {id: id, type: 'CREDIT_RECHARGE'} : { id },
					component:()=> import('@components/dealer/recharge_of_goods_detail')
				})
			}
		},
		pageChange(page_size){
			this.searchParams.page.length = page_size
			this.searchFun()
		},
		currentPageChange(page){
			this.searchParams.page.pageNo = page
			this.searchFun()
		},
		getthousand(data){
			// 数字获取千分位
			// data = data.split(',').join('')
			if(data == '' || data == null){
				return data
			}
			let Negative = '';
			if(Number(data)<0){
				Negative = '-';
			}
			data = Math.abs(data);
			// console.log(data)
			let changeData = data.toString().split(',').join('');
			let splitArr = [];
			splitArr = changeData.split('.');
			// console.log(splitArr.length == 1)

			let leftArr = splitArr[0].split('').reverse();
			let rightArr =  splitArr[1] || '';
			let newArr = []
			leftArr.forEach((item,index) => {
				newArr.push(item)
				if((index + 1) % 3 == 0 && (index + 1) != leftArr.length){
                	newArr.push(",");
            	}
			})
			newArr = newArr.reverse();
			// console.log(newArr.join('')+'.'+rightArr);
			let returnData = splitArr.length == 1 ? newArr.join(''):(newArr.join('')+'.'+rightArr);
			// console.log(returnData,rightArr)
			return Negative + returnData;
		},
		exportExcel (e){
			// var $btn = e.target
			let self = this;
			// $btn.disabled = true
			this.ajax.postStream('/dealer-web/api/dealerFundsManageExport/exportDealerFundsManageRecordItem', self.searchParams, res => {
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				// $btn.disabled = false
			}, () => {
				// $btn.disabled = false
			})
		},
		showExportList (){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: 'EXCEL_TYPE_DEALER_MANAGE_RECORD',
					},
				},
			})
		},
    aduitCheck(type){
      let realList = this.multipleSelection.filter(item=>item.order_no != '合计')//去除合计行
			if (realList.length < 1) {
        this.$message.error("请选择至少一行数据");
			  return
      }
      let realListId = [];
      realList.map((i)=>{realListId.push(i.id)})
      this.ajax.postStream('/dealer-web/api/dealerFundsManageRecordDetail/aduitCheck',realListId,res=>{
        if(!!res.body.result){
          let newMoney = []
          for (let i in res.body.content) {
            newMoney.push(res.body.content[i]);
          }
          if(newMoney.some(i=>i<0)){
            this.$confirm('当前充值资金池将会为负数，是否审核？', '提示', {
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'warning'
            }).then(() => {
              this.actionByIds(type);
            }).catch(() => {
            });
          } else {
            this.actionByIds(type);
          }
        } else {
          this.$message.error("货款充值单审核校验失败");
        }
      },err=>{
				this.$message.error(err)
			})
    },
		actionByIds(type){
			let canIuse=false;
			let msg=false;
			let orderStatusList=[]////能提交的状态

			let realList = this.multipleSelection.filter(item=>item.order_no != '合计')//去除合计行
			if (realList.length < 1) {
                this.$message.error("请选择至少一行数据");
				return
            }
			if(realList.length > 50){
				this.$message.error("最多选择50行数据");
				return
			}

			let ids=realList.map(item=>item.id)||[]

			if(type=="submit"){
				orderStatusList=['CREATE','WITHDRAWED','RETRIAL']
				canIuse=realList.every(item=>orderStatusList.includes(item.order_status))
				msg='单据状态需为【创建,重新审核,已撤回】'
			}else if(type=="lock"){
				orderStatusList=['SUBMITTED','RETRIAL']
				canIuse=realList.every(item=>orderStatusList.includes(item.order_status))
				msg='单据状态需为【提交审核,重新审核】'
			}else if(type=="audit"){
				orderStatusList=['LOCKED']
				canIuse=realList.every(item=>orderStatusList.includes(item.order_status))
				msg='单据状态需为【锁定】'
			}else if(type == 'batchCheck'){
                orderStatusList=['SUBMITTED']
				canIuse=realList.every(item=>orderStatusList.includes(item.order_status))
				msg='单据状态需为【提交审核】'
            }

			if(!canIuse){
				this.$message.error(msg)
				return
			}

			//接口拼接
			let apiOptions={
				'submit':'batchSubmit?permissionCode=DEALER_FUNDS_SUBMIT',
				'lock':'batchLock?permissionCode=DEALER_FUNDS_LOCK',
				'audit':'batchAudit?permissionCode=DEALER_FUNDS_AUDIT',
                'batchCheck':'batchCheck?permissionCode=DEALER_FUNDS_CHECK'
			}
			let api=`/dealer-web/api/dealerFundsManageRecordDetail/${apiOptions[type]}`

			//按钮loading
			let btnOptions={
				'submit':'提交',
				'lock':'锁定',
				'audit':'审核',
                'batchCheck':'核对'
			}
			let index=this.btns.findIndex(item=>item.txt==btnOptions[type])


			this.btns[index].loading=true;
			this.ajax.postStream(api,ids,res=>{
				if(res.body.result){
					this.$message.success(res.body.msg)
					setTimeout(()=>{
						this.searchFun()
					},10)
				}else{
					this.$message.error(res.body.msg)
				}
				this.btns[index].loading=false;
			},err=>{
				this.$message.error(err)
				this.btns[index].loading=false;
			})
		},
		//上传成功返回结果
        uploadSuccess(result) {
            if (result.length > 0 && !!result[0].path) {
                this.importFileUrl(result[0].path);
            }
        },
		 //导入
        importFileUrl(fileUrl) {
            this.ajax.postStream(
                "/dealer-web/api/dealerFundsManageRecordDetail/import",
                fileUrl,
                (res) => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg);
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
        //导入结果
        showUploadResult() {
            this.$root.eventHandle.$emit("alert", {
                style: "width:900px;height:600px",
                title: "导入结果",
                params: {
                    url: "/dealer-web/api/dealerFundsManageRecordDetail/import/list",
                    data: {},
                    showDownload: true,
                },
                component: () => import("@components/common/eximport"),
            });
        },
		//列表选择
		handleSelectionChange(val) {
            this.multipleSelection = val;
        },
    },
	mounted (){
        if (this.params && this.params.menuInfo && this.params.menuInfo.code === "RECHARGE_BUSINESS_EALER_FUNDS") {
            // 经销商管理列表
            this.$refs.rechargeOfGoodsList.$refs.xptSearchEx.filterFields = fields => fields.filter(o => !/(单据类型)/.test(o.comment))
		}else { // 货款充值列表
			// empty
			// this.$refs.rechargeOfGoodsList.$refs.xptSearchEx.filterFields = fields => fields.filter(o => !/(单据类型)/.test(o.comment))
			let list = __AUX.getValidData('rechangeBusinessType'), auxList = []
			for (let i = 0; i < list.length; i++) {
                if (list[i].code != 'CREDIT_RECHARGE') {
                    let obj = {
                        code: list[i].code,
                        name: list[i].name
                    }
                    auxList.push(obj)
                }
			}
			this.$refs.rechargeOfGoodsList.$refs.xptSearchEx.filterFields = (fields) => {
				fields.forEach(item => {
					if (item.comment == '单据类型') {
						item.values = auxList
					}
				})
				return fields
			}
        }
		this.ajax.postStream("/user-web/api/sql/listFields", { page:'scm_sys_trade' }, res => {
			res.body.content.fields.some(obj => {
				if(/合并单号/.test(obj.comment)){
					this.openOrder.where = [{
					    "field": obj.field,
					    "table": obj.table,
					    "value": '',
					    "operator": "=",
					    "condition": "AND",
					    "listWhere": []
					}]
					return true
				}
			})
		})

		if(!this.shopId){
			this.ajax.postStream('/dealer-web/api/dealerFundsManageRecord/getDealerInfo', this.getEmployeeInfo('id'), res => {
				if(res.body.result){
					this.searchParams.dealer_id = res.body.content
					this.searchFun()
				}else {
					this.$message.error(res.body.msg)
				}
			})
		}else {
			this.searchFun()
		}
	},
}
</script>
