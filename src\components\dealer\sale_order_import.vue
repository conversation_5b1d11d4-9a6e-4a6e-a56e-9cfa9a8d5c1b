<!-- 经销商品折扣管理商品行列表  -->
<template>
  <xpt-list2 ref="saleOrderImportList" @cell-click="handleCellClick" :data="dataList" :orderNo="true" :btns="btns" :colData="cols" :searchPage="search.page_name"
    :pageTotal="pageTotal" :pageLength="search.page_size" :isNeedClickEvent="false" @selection-change="select"
    @search-click="searchClick" @page-size-change="sizeChange" @current-page-change="pageChange">
    <xpt-import slot="btns" :taskUrl="uploadUrl"  class="mgl10" :isupload="!canEidt ||submit.status != 'CREATE'" :callback="saleOrderImportCallback" :otherParams="otherParams" text="导入">
    </xpt-import>
    <template slot='shop_id' slot-scope='scope' >
        <el-button type='primary' size='mini' @click="getInfo(scope.row)" :disabled="false">点击查看</el-button>
    </template>
  </xpt-list2>
</template>

<script>
  // import Fn from "@common/Fn.js";
  export default {
    props: ["params"],
    name: "saleOrderImportDataList",
    data() {
      let self = this;
      return {
       otherParams:{ discount_id:self.discount_id,argumentIsPathInsteadOfUrl: true},
        dataList: [],
        backupList: [],
        pageTotal: 0,
        // excel导入失败附件模板下载地址
        saleOrderExportUrl: null,
        // 附件上传地址
        uploadUrl: "/price-web/api/cloudPurchase/importItem",
        templateUploadUrl: "/order-web/api/orderExcel/inputExceTemplate",
        modelExcelExportUrl: null,
        selectRow: [],
        editList: [],
        btns: [
          //  按钮颜色：type 》 success 绿色， primary 蓝色 ，  danger 红色
          {
            type: "success",
            txt: "刷新",
            click() {
              self.getDataList();
            }
          },
          {
            type: "success",
            txt: "导入结果",
            click() {
              self.exportResultPopup();
            }
          },
          {
            type: "danger",
            txt: "删除",
            disabled(){
              return !self.canEidt;
            },
            click() {
              self.deleteData();
            }
          },
           {
            type: "warning",
            txt: "失效",
            disabled(){
              return !self.canEidt;
            },
            click() {
              self.disableData();
            }
          },

        ],

        cols:[
          {
            label: '商品类别',
            prop: 'sales_profit_class_name',
            width: 130,
          },
          {
            label: '物料编码',
            prop: 'material_number',
            width: 130,
          },

           {
            label: '物料名称',
            prop: 'material_name',
            width: 130
          },
          {
            label: '物料描述',
            prop: 'material_specification',
            width: 130
          },
          {
            label: '额外折扣',
            prop: 'discount',
            width: 130
          },

          {
            label: '店铺编码',
            prop: 'shop_code',
            width: 130
          },
          {
            label: '店铺名称',
            prop: 'shop_name',
            width: 130
          },
          {
            label: '状态',
            prop: 'disabled_status',
            width: 130,
            formatter(val){
              let status = {
                1:'生效',
                0:'失效',
              }
              return status[val] || val;
            }
          },


          {
            label: '失效时间',
            prop: 'disable_time',
            format: "dataFormat1",
            width: 130
          },
          {
            label: '失效人',
            prop: 'disable_person_name',
            width: 130
          },

        ],
        currentCellSelect:{},

        search: {
          page_name: "cloud_purchase_discount_item",
          where: [],
          page_no: 1,
          discount_id:'',
          page_size: 20
        }
      };
    },
    props:{

      discount_id: {
        type: Number
      },
      submit: {
        type: Object
      },
    },
    mounted: function () {
      this.search.discount_id = this.discount_id;
      if(this.discount_id){
        this.getDataList();
      }
      // 固定复选框
    },
    computed:{
      canEidt(){
        return this.discount_id;
        return this.submit.creator == this.getEmployeeInfo('id')
      }
    },

    methods: {
      viewMergedOrder(merge_trade_id, merge_trade_no){
			var params = {};
			params.merge_trade_id = merge_trade_id;
			params.mergeTradeIdList = this.list
			params.ifOrder = true
			// 呼叫按钮来源
			params.callBtnfromType = 'sys_order'
			this.$root.eventHandle.$emit('creatTab',{
				name:"合并订单详情",
				params:params,
				component: () => import('@components/order/merge.vue')
			});
		},
      //点击行
    handleCellClick(row) {
      console.log("行的回调函数：", row);
      this.currentCellSelect = row;
    },
      getInfo(row){
        let self = this;
         this.ajax.get(
              "/order-web/api/marketing/listMaterial/"+row.shop_id,
              (res) => {
                if (res.body.result) {
                  self.$root.eventHandle.$emit('alert',{
                  params:{list:res.body.content},
                  component:()=>import('@components/order/centralizePurchaseAlert'),
                    style:'width:800px;height:500px',
                    title:'集采物料明细详情'
                })
                } else {
                  this.$message.error(res.body.msg);

                }
                // this.getDataList();
              },
              (err) => {
                this.$message.error(err);
              }
            );
      },
      getDataList(resolve) {
        let self = this;

        this.editList = [];
        // this.selectRow = [];
        // this.btns[0].loading = true;
        this.ajax.postStream(
          // 根据接口请求数据 - 替换对应接口url
          "/price-web/api/cloudPurchase/listItem",
          this.search,
          res => {
            if (res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.backupList = JSON.parse(JSON.stringify(res.body.content.list));
              // self.$refs.saleOrderImportList.$refs.list.clearSelection();
              this.pageTotal = Number(res.body.content.count);
            } else {
              this.$message.error(res.body.msg);
            }
            if (resolve) {
              resolve();
            }
          },
          err => {
            this.$message.error(err);
            if (resolve) {
              resolve();
            }
          }
        );
      },



      // 行是否可操作
      materiaCanEdit(row) {
        if (this.editList.length < 1) {
          return true;
        }
        var index = this.editList.indexOf(row.shop_id);
        if (index >= 0) {
          return false;
        }
        return true;
      },

      deleteData() {
        if (this.selectRow.length < 1) {
          this.$message.error("请选择要删除的数据！");
          return;
        }

        const ids = this.selectRow.map(item => item.item_id);

        this.$confirm(
          "当前操作会导致选中的数据被删除，是否继续？",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "danger"
          }
        )
          .then(() => {
            // 请求后台delete 接口 -- 待修改
            // this.ajax.postStream('/order-web/api/sys/import/delete?permissionCode=SINGLE_GOODS_DISCOUNT_IMPORT_DELETE', {list_import_id}, res => {
            this.ajax.postStream(
              "/price-web/api/cloudPurchase/deleteItem",
              ids,
              res => {
                if (res.body.result) {
                  this.$message.success(res.body.msg || "删除成功！");
                  this.getDataList();
                } else {
                  //res.body.msg &&
                  this.$message.error(res.body.msg || "删除失败！");
                }
              },
              err => {
                this.$message.error(err);
              }
            );
          })
          .catch(() => {
            return false;
          });
      },
      disableData() {
        if (this.selectRow.length < 1) {
          this.$message.error("请选择要失效的数据！");
          return;
        }
        let self = this;
        var params = {
				callback(res){

					if(!!res){
            self.selectRow.forEach(item=>{
              item.disable_time = res;
              item.disable_person_id = self.getEmployeeInfo('id');
              item.disable_person_name = self.getEmployeeInfo('fullName');
            })
						self.ajax.postStream(
              "/price-web/api/cloudPurchase/disableItem",
              self.selectRow,
              res => {
                if (res.body.result) {
                  self.$message.success(res.body.msg || "成功！");
                  self.getDataList();
                } else {
                  //res.body.msg &&
                  self.$message.error(res.body.msg || "失败！");
                }
              },
              err => {
                self.$message.error(err);
              }
            );
					}

				}
			};

			self.$root.eventHandle.$emit('alert', {
				params:params,
				component: () => import('@components/dealer/batchDisabled.vue'),
				close:function(res){
					//console.log(res)

				},
				style:'width:500px;height:150px',
				title:'失效',
				recyclingGood:self.recyclingGood
			});
      },

      // 多选事件
      select(data) {
        this.selectRow = data;
      },

      searchClick(list, resolve) {
        this.search.where = list;
        this.getDataList(resolve); //模糊查找
      },
      // 监听每页显示数更改事件
      sizeChange(pageSize) {
        this.search.page_size = pageSize;
        this.getDataList();
      },
      // 监听页数更改事件
      pageChange(page) {
        this.search.page_no = page;
        this.getDataList();
      },








      // 销售单导入(EXCEL) 回调
      saleOrderImportCallback(res) {
        this.saleOrderExportUrl = null;
        if (res.body.result) {
          this.$message.success(res.body.msg);
          this.saleOrderExportUrl = res.body.content;
          //  window.open(this.saleOrderExportUrl);
          this.getDataList();

          //this.saleOrderExportUrl = res.body.content.fileURL;
          // this.list = res.body.content.data;
          // this.cols = this.firstCol;
          // this.count = this.list.length;
        } else {
          this.$message.error(res.body.msg);
          this.saleOrderExportUrl = res.body.content;
          if (res.body.content != null || res.body.content != "") {
            //   window.open(this.saleOrderExportUrl);
          }
        }
      },

      // 导出结果弹窗
      exportResultPopup() {
        this.$root.eventHandle.$emit("alert", {
          component: () => import("@components/after_sales_report/export"),
          // component: () => import("@components/per_sales_report/export"), 过滤条件不生效，改回使用   @components/after_sales_report/export
          style: "width:900px;height:600px",
          title: "导入列表",
          params: {
            query: {
              type: 'EXCEL_TYPE_PURCHASE_DISCOUNT_IMPORT'
            }
          }
        });
      }
    }
  };
</script>
