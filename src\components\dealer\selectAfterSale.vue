<!-- 销售订单列表 -->
<template>
<div class='xpt-flex'>
	<el-row class='xpt-top' :gutter='40' v-if="params.isFromRefundRequest">
		<el-col :span='3'>
			<el-button type='primary' size='mini' @click="close()">确认</el-button>
		</el-col>
	</el-row>
	<el-row class='total-price'>
		<el-col :span="8">
			实际售价汇总：{{act_price_total.toFixed(2)}}
		</el-col>
		<el-col :span="4">
			经销价汇总：{{settle_price_total.toFixed(2)}}
		</el-col>
	</el-row>
	<xpt-list 
		:data='list' 
		:btns='btns' 
		:colData='colData' 
		:pageTotal='count' 
		:searchPage='search.page_name' 
		:selection="'checkbox'"
		:isNeedClickEvent='true'
		:showHead="true"
		@selection-change='radioChange'
		@search-click='presearch' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange' 
		:pageLength='search.page_size'
		ref="xptList"
	></xpt-list>
</div>
</template>
<script>
import orderDetail from '@components/order/detail.vue'
import mergedOrderDetail from '@components/order/merge.vue'
import fn from '@common/Fn.js'

	export default {
		props:["params"],
		data(){
			var _this = this;
			return {
				search:{
					page_name:"pur_dealer_returns_goods",
					where:[],
					page_size:50,     //页数
                    page_no:1,   //页码
                    shopId: ''
				},
				list:[],
				count:0,
                pageNow:1,
				selectLine:[],
				btns:[{
					txt:'确认',
					type:'info',
					click:()=>{
						_this.close();
					}
				}],
				colData:[
                    {
                        prop:'bill_returns_no',
                        label:'退货跟踪单号',
                        width:170
                    },{
                        prop:'material_code',
                        label:'物料编码',
                        width:120
                    },{
                        prop:'material_name',
                        label:'物料名称',
                        width:150
                    },{
                        prop:'act_price',
                        label:'实际售价',
                        width:150
                    },{
                        prop:'settle_price',
                        label:' 经销价',
                        width:150
                    },{
                        prop:'bill_salesreturn_no',
                        label:'销售退货单',
                        width:150
                    },{
                        prop:'bill_receivables_no',
                        label:'应收单',
                        width:150
                    },{
                        prop:'finish_time',
                        label:'完结日期',
                        format: 'dataFormat1',
                        width: 130,
                    }
                ],
				act_price_total:0,
				settle_price_total:0
			}
		},
		methods:{
			// 关闭标签页
			closeTab(){
				this.$root.eventHandle.$emit('removeTab',this.params.tabName);
			},
			sizeChange(size){
				// 第页数改变
				this.search.page_size = size;
				/*this.selectLine = null;*/
				this.searching();
			},
			pageChange(page_no){
				// 页数改变
				this.pageNow = page_no;
				this.search.page_no = page_no;
				/*this.selectLine = null;*/
				this.searching();
			},
			presearch(list, resolve){
				this.search.where = list;
				this.searching(resolve);
			},
			searching(resolve){
                let _this = this, gids = [];
                let params = JSON.parse(JSON.stringify(_this.search))
                if (this.selectLine.length > 0) {
                    this.selectLine.forEach(item => {
                        gids.push(item.id)
                    })
                    params.gIds = gids
                }
				this.ajax.postStream('/dealer-web/api/dealerFundsManageRecordDetail/getPurDealers', params,function(response){
					if(response.body.result){
						_this.list = response.body.content.list
						_this.count = response.body.content.count
                        // _this.$nextTick( () => {
                        //     _this.list.forEach(item => {
                        //         _this.$refs.xptList.$refs.table.toggleRowSelection(item, true)
                        //     })
                        // })
					}else{
						_this.list = []
						_this.count = 0 
					}
					// _this.selectLine = null;//清空已选择的数据
					resolve && resolve();
				}, err => {
					this.$message.error(err);
					resolve && resolve();
				})
            },
			radioChange(data){
				this.selectLine = data;
				this.act_price_total=data.reduce((acc,cur)=>{
					return acc+Number(cur.act_price)
				},0)
				this.settle_price_total=data.reduce((acc,cur)=>{
					return acc+Number(cur.settle_price)
				},0)
			},
			/*
				该组件作为弹窗时
			*/
			close(){
                let params = Object.assign({}, this.search), gIds = []
                if (this.selectLine.length > 0) {
                    this.selectLine.forEach(item => {
                        gIds.push(item.pur_return_goods_id)
                    })
                    params.where = []
                }
                params.gIds = gIds
                if (!this.params.ifNew) {
                    if(this.list.length > 0) {
                        params.ifChoose = 'Y'
                    } else {
                        params.ifChoose = 'N'
                    }
                }
                this.params.callback(params)
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
        },
        created () {
            this.search.shopId = this.params.shopId
        },
		mounted: function(){
            this.searching()
        }
	}
</script>
<style scoped>
.total-price{
	padding: 2px 10px;
    line-height: 24px;
}
</style>
