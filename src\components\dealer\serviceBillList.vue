<!--经销网拍居间服务费对账单列表-->
<template>
  <div class="xpt-flex">
    <count-list
      :data="shopList"
      :btns="btns"
      :colData="cols"
      :searchPage="search.page_name"
      :pageTotal="count"
      :selection="selection"
      :showCount="showCount"
      @search-click="searchData"
      @selection-change="selectionChange"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
      @row-dblclick="rowDblclick"
      countUrl="/order-web/api/cloudWpOrder/count"
      :dynamic="true"
      id="multiple-rows-table"
    >
      <xpt-upload-v3
        slot="btns"
        uploadBtnText="导入"
        :uploadSize="20"
        acceptTypeStr=".xlsx,.xls"
        :dataObj="uploadDataObj"
        :disabled="false"
        :ifMultiple="false"
        :showSuccessMsg="false"
        @uploadSuccess="uploadSuccess"
        btnType="success"
        style="display: inline-block; margin: 0px 10px"
      >
      </xpt-upload-v3>
    </count-list>
  </div>
</template>
<script>
import countList from "@components/common/list-count";

export default {
  components: { countList },
  data() {
    let self = this;
    return {
      countOffFlag: false,
      showCount: false,
      btns: [
        {
          type: "primary",
          txt: "刷新",
          click: () => this.getShopList(),
        },
        {
          type: "primary",
          txt: "新增",
          click: () => this.addEvent(),
        },
        {
          type: "primary",
          txt: "提交",
          click: () => this.handleEvent("submit"),
        },
        {
          type: "primary",
          txt: "审核",
          click: () => this.handleEvent("audit"),
        },
        {
          type: "primary",
          txt: "撤销",
          click: () => this.revokeEvent(),
        },
        {
          type: "primary",
          txt: "导出",
          click: () => this.exportExcel(),
        },
        {
          type: "info",
          txt: "导入结果",
          loading: false,
          click: () => {
            self.downloadExcel();
          },
        },
        {
          type: "info",
          loading: false,
          txt: "导出结果",
          click: () => self.exportList(),
        },
        {
          type: "info",
          loading: false,
          txt: "预提",
          click: () => self.withhloding(),
        },
        {
          type: "info",
          loading: false,
          txt: "冲减",
          click: () => self.offset(),
        },
        {
          type: "primary",
          loading: false,
          txt: "批量强制完结",
          click: () => self.onMandatoryEnd(),
        },
        {
          type: "info",
          loading: false,
          txt: "积分预提",
          click: () => self.advancePointsAccrual(),
        },
        {
          type: "info",
          loading: false,
          txt: "作废",
          click: () => self.cancelOrder(),
        },
      ],
      cols: [
        {
          label: "单据编号",
          prop: "order_no",
          width: 120,
          redirectClick(row) {
            self.serviceBillListDetail(row);
          },
          fixed: "left",
        },
        {
          label: "对账期间",
          prop: "reconcile_date",
          fixed: "left",
        },
        {
          label: "半月阶段名称",
          prop: "half_month_name",
          width: 100,
          fixed: "left",
        },
        {
          label: "店铺编码",
          prop: "shop_code",
          fixed: "left",
        },
        {
          label: "店铺名称",
          prop: "shop_name",
          width: 120,
          fixed: "left",
        },
        {
          label: "客户名称",
          prop: "cust_name",
          width: 120,
          fixed: "left",
        },
        {
          label: "单据状态",
          prop: "order_status",
          formatter(val) {
            switch (val) {
              case "CREATED":
                return "创建";
              case "SUBMIT":
                return "提交";
              case "DEALER_CHECK":
                return "经销核对";
              case "AUDIT":
                return "审核";
              case "CANCELED":
                return "已作废";
              case "INVOICE_INSPECTION":
                return "发票查验";
              case "OVERTIME_FORCE_END":
                return "超时强制完结";
              case "OVERTIME_CANCELED":
                return "超期作废";
            }
          },
        },
        {
          label: "发票类型",
          prop: "invoice_type",
          formatter(val) {
            switch (val) {
              case "PAPER":
                return "纸质普通发票";
              case "DEDICATED_PAPER":
                return "纸质增值税专用发票";
              case "ELECTRONIC":
                return "电子普通发票";
              case "DEDICATED_ELECTRONIC":
                return "增值税电子发票";
              case "COMMON_ELE":
                return "电子发票（普通发票）";
              default:
                return val;
            }
          },
        },
        {
          label: "本期开票金额",
          width: 120,
          prop: "invoice_current_price",
        },
        {
          label: "预提金额",
          width: 120,
          prop: "withholding_amount",
        },
        {
          label: "预提时间",
          width: 120,
          prop: "withholding_date",
          format: "dataFormat1",
        },
        {
          label: "付款时间",
          width: 120,
          prop: "pay_date",
          format: "dataFormat1",
        },
        {
          label: "冲减金额",
          width: 120,
          prop: "offset_amount",
        },
        {
          label: "冲减时间",
          width: 120,
          prop: "offset_date",
          format: "dataFormat1",
        },
        {
          label: "是否手工输入查验",
          prop: "if_person_made",
          format: "yesOrNo",
          width: 120,
        },
        {
          label: "付款单号",
          width: 120,
          prop: "pay_no",
        },
        {
          label: "居间服务费合计",
          width: 120,
          prop: "total_period_service_cost",
        },
        {
          label: "以往税点金额调整",
          width: 120,
          prop: "his_tax_adjustment",
        },
        {
          label: "激励服务费",
          prop: "excitation_service_cost",
        },
        {
          label: "以往服务费调整",
          width: 120,
          prop: "his_service_adjustment",
        },
        {
          label: "导购奖金业绩合计",
          width: 120,
          prop: "total_guide_bonus",
        },
        {
          label: "开票点数",
          prop: "invoice_dot",
        },
        {
          label: "开票返点",
          prop: "invoice_return_price",
        },
        {
          label: "责任扣款",
          prop: "liability_deduct",
        },
        {
          label: "销售利润分类",
          width: 100,
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          prop: "profit_type",
        },
        {
          label: "居间促销退货退款业绩",
          width: 140,
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          prop: "refund_achievement",
        },
        {
          label: "付返前实际售价",
          prop: "before_discount_delivery_achievement",
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          width: 140,
        },
        {
          label: "网拍单收款金额",
          prop: "wangpai_receiver_amount",
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          width: 140,
        },
        {
          label: "定制单经销价",
          prop: "dealer_price",
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          width: 140,
        },
        {
          label: "超区运费",
          prop: "excess_freight_charge",
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          width: 140,
        },
        {
          label: "总实际营收",
          width: 100,
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          prop: "total_actual_revenue",
        },
        {
          label: "运输方式",
          prop: "deliver_method",
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          width: 140,
          format: 'auxFormat',
          formatParams: 'WP_DELIVER_METHOD',
        },
        {
          label: "订单标签",
          prop: "order_type",
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          width: 140,
          format: 'auxFormat',
          formatParams: 'WP_ORDER_TYPE',
        },
        {
          label: "绝对值点数",
          width: 100,
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          prop: "absolute_points",
        },
        {
          label: "销售利润分类点数",
          width: 120,
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          prop: "profit_type_points",
        },
        {
          label: "额外折扣",
          prop: "extra_discount",
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          width: 140,
        },
        {
          label: "本期固定居间服务费合计",
          width: 150,
          type: "rows",
          arrayList: "cloudWpServiceOrderDetailList",
          prop: "period_service_cost",
        },
        {
          label: "创建人",
          width: 100,
          prop: "creator_name",
        },
        {
          label: "创建日期",
          width: 120,
          prop: "create_time",
          format: "dataFormat1",
        },
        {
          label: "修改人",
          width: 100,
          prop: "modifier_name",
        },
        {
          label: "修改时间",
          width: 120,
          prop: "last_modify_time",
          format: "dataFormat1",
        },
        {
          label: "经销核对人",
          width: 100,
          prop: "dealer_checker_name",
        },
        {
          label: "经销核对时间",
          prop: "dealer_check_time",
          format: "dataFormat1",
          width: 120,
        },
        {
          label: "审核人",
          width: 100,
          prop: "auditor_name",
        },
        {
          label: "审核日期",
          width: 120,
          prop: "audit_time",
          format: "dataFormat1",
        },
        {
          label: "备注",
          width: 120,
          prop: "remark",
        },
        {
          label: "合同签署状态",
          width: 120,
          prop: "contract_status",
          format: "yesOrNo",
        },
        {
          label: "开户银行",
          width: 120,
          prop: "bank_name",
        },
        {
          label: "银行账号",
          width: 120,
          prop: "bank_account",
        },
        {
          label: "账号名称",
          width: 120,
          prop: "bank_account_name",
        },
      ],
      search: {
        page_name: "cloud_wp_service_order",
        where: [],
        page_size: self.pageSize,
        page_no: 1,
      },
      shopList: [],
      selectData: [],
      count: 0,
      selection: "checkbox",
      uploadDataObj: {
        parent_name: "经销网拍居间服务费对账单列表",
        parent_no: "WP_SERVICE_ORDER_LIST_IMPORT", //主要通过该参数获取附件列表
        child_name: null,
        child_no: null,
        content: {},
      },
      showCount: false,
    };
  },
  props: ["params"],
  methods: {
    selectionChange(data) {
      this.selectData = data.filter((item) => !item.concat_type);
    },
    searchData(obj, resolve) {
      this.search.where = obj;
      new Promise((res, rej) => {
        this.getShopList(resolve, res);
      }).then(() => {
        if (this.search.page_no != 1) {
          this.count = 0;
        }
        this.showCount = false;
      });
    },
    pageSizeChange(pageSize) {
      this.search.page_size = pageSize;
      this.selectData = null;
      this.getShopList();
    },
    pageChange(page) {
      this.search.page_no = page;
      this.selectData = null;
      this.getShopList();
    },
    countOff() {
      let self = this,
        url = "/order-web/api/cloudWpOrder/count";
      if (!self.shopList.length) {
        self.$message.error("当前列表为空，先搜索内容");
        return;
      }
      if (!!self.countOffFlag) {
        self.$message.error("请勿重复点击");
        return;
      }
      self.countOffFlag = true;

      self.ajax.postStream(url, self.form, function (response) {
        if (response.body.result) {
          self.count = response.body.content;
          self.showCount = true;
          self.countOffFlag = false;
        } else {
          self.$message.error(response.body.msg);
        }
      });
    },
    getShopList(resolve, callback) {
      var postData = JSON.parse(JSON.stringify(this.search));
      if (this.params.setWhere) {
        this.params.setWhere(postData); //在setWhere方法里面直接修改postData对象内容
      }
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/list",
        postData,
        (d) => {
          callback && callback();
          if (d.body.result && d.body.content) {
            if (!this.showCount) {
              let total = this.search.page_size * this.search.page_no;
              this.count =
                d.body.content.list.length == this.search.page_size + 1
                  ? total + 1
                  : total;
            }
            this.handleShopList(d.body.content);
          } else {
            this.$message.error(d.body.msg || "");
          }
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
        }
      );
    },
    handleShopList(arr) {
      if (arr.list.length > 0) {
        let obj = {
          ...arr.amount,
          invoice_type: "合计：",
          concat_type: "total",
        };
        this.shopList = arr.list.concat([obj]);
      } else {
        this.shopList = [];
      }
    },
    //上传成功返回结果
    uploadSuccess(result) {
      if (result.length > 0 && !!result[0].path) {
        this.importFileUrl(result[0].path);
      }
    },
    //导入
    importFileUrl(fileUrl) {
      let params = {
        file_path: fileUrl,
      };
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/addImport?permissionCode=WP_SERVICE_ORDER_IMPORT",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.content);
            this.getShopList();
          } else {
            this.$message.error(res.body.content);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 导出
    exportExcel() {
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/addExport?permissionCode=WP_SERVICE_ORDER_EXPORT",
        this.search,
        (res) => {
          if (res.body.result) {
            res.body.msg && this.$message.success(res.body.msg);
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 查看导入结果
    downloadExcel() {
      this.$root.eventHandle.$emit("alert", {
        params: {
          obj: {
            excel_type: "EXCEL_TYPE_WP_SERVICE_ORDER_IMPORT",
          },
          url: "/price-web/api/price/import/list",
        },
        component: () => import("@components/dealer/import_result.vue"),
        style: "width:800px;height:400px",
        title: "导入结果",
      });
    },
    withhloding() {
      let self = this,
        data = [];
      if (self.selectData.length === 0) {
        self.$message.error("请选择要处理的数据");
        return;
      }
      self.selectData.forEach((item) => {
        if (item.order_status != "CREATED") {
          data.push(item.order_no);
        }
      });
      if (data.length != 0) {
        self.$message.error(
          "单据编号 " +
            data.concat(" ") +
            "状态不等于创建，不可预提 。 请重新选择"
        );

        return;
      }
      this.$confirm(
        "确定预提" + self.selectData.length + "张对账单？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.ajax.postStream(
          "/order-web/api/cloudWpOrder/withholding?permissionCode=WP_SERVICE_ORDER_AUDIT",
          {
            order_id_list: this.selectData.map((item) => {
              return item.service_order_id;
            }),
          },
          (d) => {
            if (d.body.result) {
              this.$message.success(d.body.msg || "");
              this.getShopList();
            } else {
              this.$message.error(d.body.msg || "");
            }
          },
          (err) => {
            this.$message.error(err);
          }
        );
      });
    },
    offset() {
      let self = this,
        data = [];
      if (self.selectData.length === 0) {
        self.$message.error("请选择要处理的数据");
        return;
      }
      this.$confirm(
        "确定冲减" + self.selectData.length + "张对账单？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.ajax.postStream(
          "/order-web/api/cloudWpOrder/offset?permissionCode=WP_SERVICE_ORDER_AUDIT",
          {
            order_id_list: this.selectData.map((item) => {
              return item.service_order_id;
            }),
          },
          (d) => {
            if (d.body.result) {
              this.$message.success(d.body.msg || "");
              this.getShopList();
            } else {
              this.$message.error(d.body.msg || "");
            }
          },
          (err) => {
            this.$message.error(err);
          }
        );
      });
    },
    // 积分预提
    advancePointsAccrual() {
      let self = this,
        data = [];
      if (self.selectData.length === 0) {
        self.$message.error("请选择要处理的数据");
        return;
      }
      const ids = this.selectData.map((item) => {
        return item.service_order_id;
      });
      console.log("idsids", ids);
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/integralWithholding?permissionCode=WP_SERVICE_ORDER_WITHHOLDING",
        {
          order_id_list: this.selectData.map((item) => {
            return item.service_order_id;
          }),
        },
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.getShopList();
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 作废
    cancelOrder() {
      let self = this,
        data = [];
      if (self.selectData.length === 0) {
        self.$message.error("请选择要处理的数据");
        return;
      }
      const ids = this.selectData.map((item) => {
        return item.service_order_id;
      });
      console.log("idsids", ids);
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/cancel?permissionCode=WP_SERVICE_ORDER_REVOKE",
        {
          order_id_list: this.selectData.map((item) => {
            return item.service_order_id;
          }),
        },
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.getShopList();
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 批量强制完结
    onMandatoryEnd() {
      if (this.selectData.length < 1) {
        return this.$message.error("请至少勾选一条数据");
      }
      const serviceOrderIds = [];
      for (let item of this.selectData) {
        // 单据状态不等于”审核“
        if (["OVERTIME_FORCE_END", "AUDIT"].includes(item.order_status)) {
          return this.$message.error("不满足完结状态");
        }
        serviceOrderIds.push(item.service_order_id);
      }
      const btnItem = this.btns.find((item) => item.txt === "批量强制完结");
      btnItem ? (btnItem.loading = true) : null;
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/batchForceEnd?permissionCode=WP_SERVICE_ORDER_OFFSET_BATCH_FORCEEND",
        { service_order_ids: serviceOrderIds },
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.getShopList();
          } else {
            this.$message.error(d.body.msg || "");
          }
          btnItem ? (btnItem.loading = false) : null;
        },
        (err) => {
          this.$message.error(err);
          btnItem ? (btnItem.loading = false) : null;
        }
      );
    },
    // 查看导出结果
    exportList() {
      this.$root.eventHandle.$emit("alert", {
        params: {
          query: {
            type: "EXCEL_TYPE_WP_SERVICE_ORDER_EXPORT",
          },
        },
        component: () => import("@components/after_sales_report/export"),
        style: "width:800px;height:400px",
        title: "导出结果",
      });
    },
    rowDblclick(obj) {
      this.serviceBillListDetail(obj);
    },
    // 新增
    addEvent() {
      let self = this;
      self.$root.eventHandle.$emit("alert", {
        title: "新增单据",
        params: {
          callback: () => {
            // self.$nextTick(()=>{
            //   self.getShopList();
            // })
          },
        },
        style: "width:600px;height:300px",
        component: () => import("@components/dealer/add"),
      });
    },
    // 详情页面
    serviceBillListDetail(row) {
      this.$root.eventHandle.$emit("creatTab", {
        name: "居间服务费对账详情",
        params: {
          shop_code: row.shop_code,
          reconcile_date: row.reconcile_date,
          service_order_id: row.service_order_id,
        },
        component: () => import("@components/dealer/serviceBillListDetail"),
      });
    },
    // 批量提交 批量审核 批量撤销
    handleEvent(status) {
      if (this.selectData.length < 1) {
        return this.$message.error("请至少勾选一条数据");
      }
      let flag = true;
      let order_status, msg, url;
      // 状态为创建CREATED时可提交
      if (status === "submit") {
        order_status = "CREATED";
        msg = "'创建'状态的对账单才能提交";
        url =
          "/order-web/api/cloudWpOrder/submit?permissionCode=WP_SERVICE_ORDER_SUBMIT";
      }
      // 状态为经销审核DEALER_CHECK时可审核
      if (status === "audit") {
        order_status = "DEALER_CHECK";
        msg = "'经销核对'状态的对账单才能审核";
        url =
          "/order-web/api/cloudWpOrder/audit?permissionCode=WP_SERVICE_ORDER_AUDIT";
      }
      let ids = {
        order_id_list: this.selectData.map((item) => {
          if (item.order_status !== order_status) {
            flag = false;
          }
          return item.service_order_id;
        }),
      };
      if (!flag) {
        return this.$message.error(msg);
      }
      this.ajax.postStream(
        url,
        ids,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.getShopList();
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    //撤销单独领出来
    revokeEvent() {
      let flag = false;
      for (var i = 0; i < this.selectData.length; i++) {
        if (
          this.selectData[i].order_status === "DEALER_CHECK" ||
          this.selectData[i].order_status === "SUBMIT"
        ) {
          flag = true;
        } else {
          flag = false;
        }
      }
      if (!flag) {
        return this.$message.error("经销核对或者提交'状态的对账单才能撤销");
      }
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/revoke?permissionCode=WP_SERVICE_ORDER_REVOKE",
        {
          order_id_list: this.selectData.map((item) => {
            return item.service_order_id;
          }),
        },
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.getShopList();
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
  },
  mounted() {
    this.getShopList();
  },
};
</script>
<style scoped>
.el-table .el-table__body-wrapper td .cell,
.el-table .el-table__fixed-body-wrapper td .cell {
  height: auto !important;
}
#j_table /deep/.xpt-flex__bottom {
  background: red;
}
</style>
