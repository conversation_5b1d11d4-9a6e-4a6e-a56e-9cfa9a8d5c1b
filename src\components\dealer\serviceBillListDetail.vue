<!--经销网拍居间服务费对账单详情-->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button
          type="primary"
          size="mini"
          :loading="isLoading"
          @click="initData"
          >刷新
        </el-button>
        <el-button
          type="primary"
          size="mini"
          :loading="isLoading"
          @click="save()"
          :disabled="isOvertimeForceEnd"
          >保存
        </el-button>
        <el-button
          type="primary"
          size="mini"
          :loading="isLoading"
          :disabled="submitDisabled"
          @click="handleEvent('submit')"
          >提交</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :loading="isLoading"
          :disabled="!revokeDisabled || isOvertimeForceEnd"
          @click="handleEvent('devoke')"
          >撤销</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :loading="isLoading"
          :disabled="checkDisabled"
          @click="checkPermission()"
          >经销核对</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :disabled="
            greenChannelOfInvoiceDisabled ||
            !(ifDealerCheck && ifVersion) ||
            isOvertimeForceEnd
          "
          @click="greenChannelOfInvoice"
          >设置发票查验通过</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :loading="isLoading"
          :disabled="auditDisabled"
          @click="handleEvent('audit')"
          >审核</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :loading="isLoading"
          :disabled="formData.order_status == 'SUBMIT' || isOvertimeForceEnd"
          @click="updateEvent()"
          >手动更新</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :disabled="!fileVO || !fileVO.length"
          @click="fileClick()"
        >
          下载电子对账单</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :loading="btnLoading"
          :disabled="mandatoryEndDisabled"
          @click="onMandatoryEnd"
          >强制完结</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :loading="btnLoading"
          :disabled="!isOvertimeForceEnd"
          @click="onReverseEnd"
          >反完结</el-button
        >
      </el-col>
    </el-row>
    <el-form
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
      ref="formData"
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="汇总信息" name="first" class="xpt-flex">
          <el-row :gutter="40">
            <el-col :span="8">
              <el-form-item label="单据编号">
                <el-input
                  v-model="formData.order_no"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="店铺编码">
                <el-input
                  v-model="formData.shop_code"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="发票类型" prop="invoice_type">
                <!-- 状态为submit提交时可填写开票点数 -->
                <el-select
                  v-model="formData.invoice_type"
                  clearable
                  @change="invoiceChange"
                  size="mini"
                  :disabled="!['SUBMIT'].includes(formData.order_status)"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in types"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <el-tooltip
                  v-if="!formData.invoice_type && isCheck"
                  class="item"
                  effect="dark"
                  content="发票类型必填"
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="开票点数" prop="invoice_dot">
                <!-- 状态为submit提交时可填写开票点数 -->
                <el-select
                  clearable
                  v-model="formData.invoice_dot"
                  size="mini"
                  :disabled="dotDisabled"
                  @change="invoiceDotChange"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in dotList"
                    :key="item.value"
                    :label="item.label"
                    :disabled="item.disable"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <el-tooltip
                  v-if="isCheck"
                  class="item"
                  effect="dark"
                  :content="content"
                  placement="right-start"
                  popper-class="xpt-form__error"
                >
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="发票返点" prop="enable_date">
                <el-input
                  v-model="formData.invoice_return_price"
                  size="mini"
                  disabled
                >
                </el-input>
              </el-form-item>
              <el-form-item label="责任扣款">
                <el-input
                  v-model="formData.liability_deduct"
                  size="mini"
                  disabled
                  @change="fitterPrice"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="对账期间">
                <el-input
                  v-model="formData.reconcile_date"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="半月阶段名称">
                <el-input
                  v-model="formData.half_month_name"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="店铺名称">
                <el-input
                  v-model="formData.shop_name"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="导购奖金业绩合计">
                <el-input
                  v-model="formData.total_guide_bonus"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="以往税点金额调整">
                <!-- 状态为CREATED创建时可填写以往税点金额调整 -->
                <el-input
                  v-model="formData.his_tax_adjustment"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="本期结算金额">
                <el-input
                  v-model="formData.invoice_current_price"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单据状态">
                <!-- 单据状态为创建时允许操作 -->
                <el-select
                  v-model="formData.order_status_name"
                  size="mini"
                  disabled
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in order_types"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="客户名称">
                <el-input
                  v-model="formData.cust_name"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="激励服务费">
                <!-- 状态为CREATED创建时可填写激励服务费 -->
                <el-input
                  v-model="formData.excitation_service_cost"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="以往服务费调整">
                <!-- 状态为CREATED创建时可填写以往服务费调整 -->
                <el-input
                  v-model="formData.his_service_adjustment"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="居间服务费合计">
                <el-input
                  v-model="formData.total_period_service_cost"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40">
            <el-col :span="8">
              <el-form-item label="合同签署状态">
                <!-- <el-input v-html="formData.contract_status =='true' ? '已签署':'未签署'" size='mini' :disabled="true" ></el-input> -->
                <el-select
                  v-model="formData.contract_status"
                  size="mini"
                  :disabled="true"
                >
                  <el-option key="1" label="已签署" :value="1"> </el-option>
                  <el-option key="2" label="未签署" :value="0"> </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="备注" prop="remark" style="height: 60px">
                <el-input
                  type="textarea"
                  :rows="2"
                  :maxlength="500"
                  placeholder="请输入内容"
                  :disabled="formData.order_status !== 'CREATED'"
                  v-model="formData.remark"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="明细信息" name="detail" class="xpt-flex">
          <xpt-list
            :data="cloudWpServiceOrderCollectList"
            :showHead="false"
            :btns="[]"
            :colData="datailCols"
            searchPage="trade_transform"
          >
          </xpt-list>
        </el-tab-pane>
        <el-tab-pane label="操作信息" name="second" class="xpt-flex">
          <xpt-form :data="formData" :cols="otherCols" label="130px"></xpt-form>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <div class="xpt-flex__bottom">
      <el-tabs v-model="listActiveName">
        <el-tab-pane label="汇总信息" name="first" class="xpt-flex">
          <div class="xpt-flex__bottom scroll">
            <el-table
              :data="goodsList"
              width="100%"
              border
              style="width: 100%"
              ref="goodsList"
            >
              <el-table-column label="网拍订单店铺" prop="wangpai_order_shop">
                <template slot-scope="scope">
                  <mdm-select
                    enum-id="WANGPAI_ORDER_SHOP"
                    default-val=""
                    disabled
                    :isEmptyVal="false"
                    v-model="scope.row.wangpai_order_shop"
                  ></mdm-select>
                </template>
              </el-table-column>
              <el-table-column
                label="销售利润分类"
                prop="profit_type"
              ></el-table-column>
              <el-table-column
                :label="computedLabel"
                prop="before_discount_delivery_achievement"
              ></el-table-column>
              <el-table-column
                label="居间促销退货退款业绩"
                prop="refund_achievement"
                width="140"
              ></el-table-column>
              <el-table-column
                label="网拍单收款金额"
                prop="wangpai_receiver_amount"
              ></el-table-column>
              <el-table-column
                label="定制单经销价"
                prop="dealer_price"
              ></el-table-column>
              <el-table-column
                label="超区运费"
                prop="excess_freight_charge"
              ></el-table-column>
              <el-table-column
                label="总实际营收"
                prop="total_actual_revenue"
              ></el-table-column>
              <el-table-column
                label="运输方式"
                prop="deliver_method"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.deliver_method | auxFormat('WP_DELIVER_METHOD') }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="订单标签"
                prop="order_type"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.order_type | auxFormat('WP_ORDER_TYPE') }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="绝对值点数"
                prop="absolute_points"
              ></el-table-column>
              <el-table-column
                label="销售利润分类点数"
                prop="profit_type_points"
              ></el-table-column>
              <el-table-column
                label="额外折扣"
                prop="extra_discount"
              ></el-table-column>
              <el-table-column
                label="平台扣点"
                prop="platform_point"
              ></el-table-column>
              <el-table-column
                label="本期固定居间服务费合计"
                prop="period_service_cost"
                align="right"
              ></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="接口信息" name="interface" class="xpt-flex">
          <el-table
            :data="interfaceData"
            width="100%"
            border
            style="width: 100%"
            ref="interfaceData"
          >
            <el-table-column
              label="下游单据类型"
              prop="push_bill_type"
            ></el-table-column>
            <el-table-column label="单据编号" prop="bill_no"></el-table-column>
            <el-table-column label="接口状态" prop="interface_status">
              <template slot-scope="scope">
                <div>
                  {{
                    scope.row.interface_status == "1"
                      ? "成功"
                      : scope.row.interface_status == "-1"
                      ? "失败"
                      : "下推中"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="接口提示信息"
              prop="interface_respone"
            ></el-table-column>
            <el-table-column label="生成时间" prop="last_modify_time">
              <template slot-scope="scope">
                {{ scope.row.last_modify_time | dataFormat1 }}
              </template>
            </el-table-column>
            <el-table-column label="操作" prop="profit_type_points">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="rePush(scope.row)"
                  >重新推送</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="收款银行账号" name="defaultBank" class="xpt-flex">
          <el-table
            :data="defaultBank"
            width="100%"
            border
            style="width: 100%"
            ref="interfaceData"
          >
            <el-table-column
              label="开户国家"
              prop="bank_country"
            ></el-table-column>
            <el-table-column
              label="开户银行"
              prop="bank_name"
            ></el-table-column>
            <el-table-column label="银行账号" prop="bank_account">
            </el-table-column>
            <el-table-column
              label="账号名称"
              prop="bank_account_name"
            ></el-table-column>
            <el-table-column label="币种" prop="currency"> </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="发票查验信息" name="invoiceInfo" class="xpt-flex">
          <xpt-list2
            :data="invoiceList"
            :colData="invoiceCols"
            :btns="invoiceBtns"
            selection="checkbox"
            orderNo
            searchHolder=""
            @selection-change="selectInvoice"
          >
            <template slot="operate" slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="invoiceInspection(scope.row)"
                :disabled="
                  !(
                    ifDealerCheck &&
                    scope.row.ins_status == 'WAIT' &&
                    scope.row.show_type != 'FIN'
                  )
                "
                >扫描查验</el-button
              >
              <el-button
                size="mini"
                type="primary"
                @click="invoiceInspectionByHand(scope.row)"
                :disabled="
                  !(
                    ifDealerCheck &&
                    scope.row.ins_status == 'CEHCKERR' &&
                    scope.row.show_type != 'FIN' &&
                    scope.row.if_person_made != 'Y'
                  )
                "
                >手工查验</el-button
              >
            </template>
            <template slot="inspection_result" slot-scope="scope">
              <span
                style="color: #20a0ff"
                @click="invoiceInspectionResult(scope.row)"
                v-if="
                  /^(CEHCKSUC|CEHCKERR|SUBMIT|SUBMIT)$/.test(
                    scope.row.ins_status
                  ) && scope.row.show_type == 'EMPL'
                "
                >查看</span
              >
            </template>
          </xpt-list2>
        </el-tab-pane>
      </el-tabs>
    </div>
    <invoice-upload
      :ifClickUpload="ifClickUpload"
      :dataObj="uploadData"
      :fileType="
        ['PAPER', 'DEDICATED_PAPER'].includes(formData.invoice_type)
          ? ''
          : 'pdf,odf'
      "
      :callback="postCallback"
      ref="detailUpload"
    ></invoice-upload>
  </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from "@common/Fn.js";
import { TYPES, LIST, ORDERTYPES } from "./constant.js";
import invoice from "./serviceBillModel/invoice";
import upload from "./serviceBillModel/upload";
import MdmSelect from "@components/mdm/components/form/MdmSelect";

export default {
  props: ["params"],
  mixins: [invoice],
  components: {
    invoiceUpload: upload,
    MdmSelect,
  },

  data() {
    var self = this;
    return {
      cloudWpServiceOrderCollectList: [],
      datailCols: [
        {
          label: "网拍订单店铺",
          prop: "wangpai_order_shop_name",
          width: 180,
        },
        {
          label: "居间服务费",
          prop: "period_service_cost",
        },
        {
          label: "责任扣款",
          prop: "liability_deduct",
          bool: true,
          elInput: true,
          disabled: (row) => {
            return self.formData.order_status != "CREATED";
          },
          blur: (row, e) => {
            if (
              e.target.value &&
              !/^[+-]?(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/.test(
                e.target.value
              )
            ) {
              self.$message.error(
                "当前输入不符合规范，请输入1-7位小数点后两位数字"
              );
              e.target.value = "";
            }
          },
          width: 180,
        },
        {
          label: "以往服务费调整",
          prop: "his_service_adjustment",
          bool: true,
          elInput: true,
          disabled: (row) => {
            return self.formData.order_status != "CREATED";
          },
          blur: (row, e) => {
            if (
              e.target.value &&
              !/^[+-]?(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/.test(
                e.target.value
              )
            ) {
              self.$message.error(
                "当前输入不符合规范，请输入1-7位小数点后两位数字"
              );
              e.target.value = "";
            }
          },
        },
        {
          label: "激励服务费",
          prop: "excitation_service_cost",
          bool: true,
          elInput: true,
          disabled: (row) => {
            return self.formData.order_status != "CREATED";
          },
          blur: (row, e) => {
            if (
              e.target.value &&
              !/^[+-]?(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/.test(
                e.target.value
              )
            ) {
              self.$message.error(
                "当前输入不符合规范，请输入1-7位小数点后两位数字"
              );
              e.target.value = "";
            }
          },
        },
        {
          label: "导购奖金业绩",
          prop: "total_guide_bonus",
          bool: true,
          elInput: true,
          disabled: (row) => {
            return self.formData.order_status != "CREATED";
          },
          blur: (row, e) => {
            if (
              e.target.value &&
              !/^[+-]?(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/.test(
                e.target.value
              )
            ) {
              self.$message.error(
                "当前输入不符合规范，请输入1-7位小数点后两位数字"
              );
              e.target.value = "";
            }
          },
        },
        {
          label: "预提金额",
          prop: "withholding_amount",
        },
        {
          label: "发票返点",
          prop: "invoice_return_price",
        },
        {
          label: "开票金额",
          prop: "invoice_current_price",
        },
        {
          label: "备注",
          prop: "remark",
          bool: true,
          elInput: true,
          disabled: (row) => {
            return self.formData.order_status != "CREATED";
            return false;
          },
          blur: (row, e) => {
            // if(e.target.value && !/^[0-9A-Za-z]{6}$/.test(e.target.value)){
            // 	this.$message.error('预约码只能输入6位数字或字母')
            // 	e.target.value = ''
            // }
          },
        },
      ],
      fileVO: null,
      content: "开票点数必填",
      formData: {
        order_no: null, //单据编号
        reconcile_date: null, //对账期间
        half_month_name: null, //半月阶段名称
        order_status: null, //单据状态
        order_status_name: null, //单据状态
        shop_code: null, //店铺编码
        shop_name: null, //店铺名称
        cust_name: null, //客户名称
        customer: null, //客户名称
        total_period_service_cost: null, //居间服务费合计
        total_guide_bonus: null, //导购奖金业绩合计
        excitation_service_cost: null, //激励服务费
        invoice_dot: null, //开票点数
        his_tax_adjustment: null, //以往税点金额调整
        his_service_adjustment: null, //以往服务费调整
        invoice_return_price: null, //发票返点
        invoice_current_price: null, //本期开票金额
        creator_name: null, //创建人
        create_time: null, //创建日期
        modifier_name: null, //修改人
        last_modify_time: null, //修改时间
        dealer_checker_name: null, //经销核对人
        dealer_check_time: null, //经销核对时间
        auditor_name: null, //审核人
        dataFormat1: null, //审核日期
        invoice_type: null, //发票类型
        remark: null, //备注
        liability_deduct: 0, // 责任扣款
        contract_status: 0, // 合同签署状态
        withholding_amount: 0, // 预提金额
        withholding_date: 0, // 预提时间
        withholding_staff_name: 0, // 预提人
        offset_staff_name: 0, // 预提人
        withholding_date: 0, // 预提人
        offset_amount: 0, // 预提人

        pay_no: 0, // 预提人
        pay_date: 0, // 预提人
        force_end_time: "", // 强制完结时间
        force_end_staff_name: "", // 强制完结人
      },
      rules: {},
      activeName: "first", //基本信息
      listActiveName: "first", //明细信息
      goodsList: [], //物料列表
      goodsSearch: {
        key: "",
        page: {
          length: 20,
          pageNo: 1,
        },
      },
      dotList: [
        {
          value: "0.00",
          label: "0%",
        },
        {
          value: 0.01,
          label: "1%",
        },
        {
          value: 0.03,
          label: "3%",
        },
        {
          value: 0.06,
          label: "6%",
        },
      ],
      isLoading: false,
      btnLoading: false,
      status: null, //按钮状态
      isCheck: false,
      types: TYPES, //开票类型
      order_types: ORDERTYPES, //单据状态
      otherCols: [
        [
          {
            label: "创建人:",
            key: "creator_name",
          },
          {
            label: "创建日期:",
            key: "create_time",
            format: "dataFormat1",
          },
          {
            label: "修改人:",
            key: "modifier_name",
          },
          {
            label: "修改时间:",
            key: "last_modify_time",
            format: "dataFormat1",
          },
        ],
        [
          {
            label: "经销核对人:",
            key: "dealer_checker_name",
          },
          {
            label: "经销核对时间:",
            key: "dealer_check_time",
            format: "dataFormat1",
          },
          {
            label: "预提金额:",
            key: "withholding_amount",
          },
          {
            label: "冲减金额:",
            key: "offset_amount",
          },
        ],
        [
          {
            label: "审核人:",
            key: "auditor_name",
          },
          {
            label: "审核日期:",
            key: "audit_time",
            format: "dataFormat1",
          },
          {
            label: "预提人:",
            key: "withholding_staff_name",
          },
          {
            label: "冲减人:",
            key: "offset_staff_name",
          },
        ],
        [
          {
            label: "结算单号:",
            key: "pay_no",
          },
          {
            label: "结算时间:",
            key: "pay_date",
            format: "dataFormat1",
          },
          {
            label: "预提时间:",
            key: "withholding_date",
            format: "dataFormat1",
          },
          {
            label: "冲减时间:",
            key: "offset_date",
            format: "dataFormat1",
          },
          {
            label: "强制完结人:",
            key: "force_end_staff_name",
          },
          {
            label: "强制完结时间:",
            key: "force_end_time",
            format: "dataFormat1",
          },
        ],
      ],
      interfaceData: [],
      defaultBank: [],
    };
  },
  computed: {
    // 添加新的计算属性
    computedLabel() {
      if (!this.formData.reconcile_date) return "付返前实际售价";

      const reconcileDate = this.formData.reconcile_date.replace(/-/g, "");
      const compareDate = "202410";

      return reconcileDate <= compareDate
        ? "居间发货业绩_付返前"
        : "付返前实际售价";
    },
    // 提交
    submitDisabled() {
      return (
        this.formData.order_status !== "CREATED" || this.isOvertimeForceEnd
      );
    },
    // 撤销
    revokeDisabled() {
      return (
        this.formData.order_status == "SUBMIT" ||
        this.formData.order_status == "DEALER_CHECK"
      );
    },
    // 经销审核
    checkDisabled() {
      return this.formData.order_status !== "SUBMIT" || this.isOvertimeForceEnd;
    },
    // 审核
    auditDisabled() {
      return (
        this.formData.order_status !== "INVOICE_INSPECTION" ||
        this.isOvertimeForceEnd
      );
    },
    // 开票点数
    dotDisabled() {
      return false;
    },
    ifDealerCheck() {
      return this.formData.order_status == "DEALER_CHECK";
    },
    // 强制完结按钮不可用 - 单据状态不等于”审核“可用
    mandatoryEndDisabled() {
      return this.formData.order_status === "AUDIT" || this.isOvertimeForceEnd;
    },
    // 当前状态为强制完结
    isOvertimeForceEnd() {
      return this.formData.order_status === "OVERTIME_FORCE_END";
    },
  },
  methods: {
    fitterPrice(num) {
      let reg =
        /^(-|0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2})|-([1-9][0-9]{0,6})|-([1-9][0-9]{0,6}\.\d{0,2})|-(0\.\d{0,2}))$/;
      if (!reg.test(num)) {
        this.$message.error("当前输入不符合规范，请输入数字");
        return "";
      } else {
        return num;
      }
    },
    invoiceDotChange(vlue) {
      if (vlue == 0.01) {
        this.formData.invoice_return_price = 0;
      }
    },
    // 重新推送
    rePush(row) {
      let data = {
        log_id: row.id,
      };
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/restPush",
        data,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    sizeChange(size) {
      // 每页加载数据
      var searchData = this.getCurrentSearchData();
      searchData.page.length = size;
      searchData.page.pageNo = 1;
    },
    pageChange(page_no) {
      // 页数改变
      var searchData = this.getCurrentSearchData();
      searchData.page.pageNo = page_no;
    },

    initData(isShowMsg) {
      //初使化数据
      this.isCheck = false;
      let data = {
        shop_code: this.params.shop_code,
        reconcile_date: this.params.reconcile_date,
        service_order_id: this.params.service_order_id,
      };

      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/detail",
        data,
        (d) => {
          if (d.body.result) {
            // console.log(d.body.content)
            Object.keys(d.body.content).forEach((key) => {
              this.formData[key] = d.body.content[key];
            });
            this.cloudWpServiceOrderCollectList = d.body.content
              .cloudWpServiceOrderCollectList
              ? d.body.content.cloudWpServiceOrderCollectList
              : [];
            this.cloudWpServiceOrderCollectList.forEach((item) => {
              if (!item.excitation_service_cost) {
                item.excitation_service_cost = 0;
              }
              if (!item.his_service_adjustment) {
                item.his_service_adjustment = 0;
              }
              if (!item.liability_deduct) {
                item.liability_deduct = 0;
              }
              if (!item.total_guide_bonus) {
                item.total_guide_bonus = 0;
              }
            });
            this.defaultBank = [
              {
                bank_country: d.body.content.bank_country,
                bank_name: d.body.content.bank_name,
                bank_account: d.body.content.bank_account,
                bank_account_name: d.body.content.bank_account_name,
                currency: d.body.content.currency,
              },
            ];
            this.goodsList = d.body.content.cloudWpServiceOrderDetailList;
            this.interfaceData = d.body.content.presaleExternalInterfaceLogs;
            this.fileVO = d.body.content.fileVO;
            if (!isShowMsg) {
              this.$message.success(d.body.msg || "");
            }
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.getInvoiceInspection();
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 按钮事件
    handleEvent(status) {
      this.btnStatus = status;
      this.isCheck = false;
      let url;
      // 提交
      if (status == "submit") {
        url =
          "/order-web/api/cloudWpOrder/submit?permissionCode=WP_SERVICE_ORDER_SUBMIT";
      }
      // 撤销
      if (status == "devoke") {
        url =
          "/order-web/api/cloudWpOrder/revoke?permissionCode=WP_SERVICE_ORDER_REVOKE";
      }
      // 审核
      if (status == "audit") {
        url =
          "/order-web/api/cloudWpOrder/audit?permissionCode=WP_SERVICE_ORDER_AUDIT";
      }
      let data = {
        order_id_list: [this.params.service_order_id],
      };
      this.ajax.postStream(
        url,
        data,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.initData();
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 强制完结
    onMandatoryEnd() {
      let data = {
        service_order_id: this.params.service_order_id,
      };
      this.btnLoading = true;
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/forceEnd?permissionCode=WP_SERVICE_ORDER_OFFSET_FORCEEND",
        data,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.initData();
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.btnLoading = false;
        },
        (err) => {
          this.$message.error(err);
          this.btnLoading = false;
        }
      );
    },
    // 反完结
    onReverseEnd() {
      let data = {
        service_order_id: this.params.service_order_id,
      };
      this.btnLoading = true;
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/reverseForceEnd?permissionCode=WP_SERVICE_ORDER_OFFSET_REVERSE_FORCEEND",
        data,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.initData();
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.btnLoading = false;
        },
        (err) => {
          this.btnLoading = false;
          this.$message.error(err);
        }
      );
    },
    checkPermission() {
      if (this.formData.invoice_dot === "") {
        this.$message.error("开票点数不能为空");
        return false;
      }
      if (!this.formData.invoice_type) {
        this.$message.error("发票类型不能为空");
        return false;
      }
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/checkPermission",
        this.formData.customer,
        (d) => {
          if (d.body.result) {
            this.checkEvent();
          } else {
            this.$message.error(d.body.msg || "");
          }
        }
      );
    },

    // 经销核对
    checkEvent() {
      this.status = "dealer_audit";
      this.checkBefore();
      if (this.isCheck) {
        return;
      }
      new Promise((resolve, reject) => {
        this.save(resolve);
      }).then(() => {
        this.ajax.postStream(
          "/order-web/api/cloudWpOrder/check?permissionCode=WP_SERVICE_ORDER_DEALER_CHECK",
          {
            order_id: this.params.service_order_id,
          },
          (d) => {
            if (d.body.result) {
              this.$message.success(d.body.msg || "");
              this.initData();
            } else {
              this.$message.error(d.body.msg || "");
            }
          }
        );
      });
    },
    //经销核对前校验
    checkBefore() {
      this.isCheck = false;
      if (!this.formData.invoice_type) {
        this.isCheck = true;
        return;
      }
      console.log(this.formData.invoice_dot);
      if (this.formData.invoice_dot === "") {
        this.isCheck = true;
        this.content = "发票点数必填";
        return;
      }
      // if((this.formData.invoice_type=="PAPER"||this.formData.invoice_type=="ELECTRONIC")
      // &&!!this.formData.invoice_dot){
      //   this.isCheck = true
      //   this.content="发票类型=电子普通发票 或 纸质普通发票，发票点数必需为空"
      //   return
      // }
    },
    // 手动更新
    updateEvent() {
      this.ajax.postStream(
        "/order-web/api/cloudWpOrder/manualUpdate?permissionCode=WP_SERVICE_ORDER_ADD",
        {
          shop_code: this.formData.shop_code,
          reconcile_date: this.formData.reconcile_date,
          order_id: this.params.service_order_id,
        },
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.initData();
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 保存前的校验
    saveBefore(resolve) {
      if (
        !this.formData.invoice_type &&
        this.formData.order_status == "SUBMIT"
      ) {
        this.isCheck = true;
        return;
      }
      if (this.formData.invoice_dot === "") {
        this.isCheck = true;
        this.content = "发票点数必填";
        return;
      }
      let flag = false;
      let reg =
        /^[+-]?(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/;
      this.cloudWpServiceOrderCollectList.forEach((item) => {
        if (
          !reg.test(item.his_service_adjustment) ||
          !reg.test(item.excitation_service_cost) ||
          !reg.test(item.liability_deduct) ||
          !reg.test(item.total_guide_bonus)
        ) {
          flag = true;
        }
      });
      console.log(flag);
      if (flag) {
        this.isCheck = true;
        this.$message.error(
          "存在【责任扣款】【以往服务费调整】【激励服务费】【导购奖金业绩】格式错误"
        );
        return;
      }
      this.isCheck = false;
      resolve && resolve();
    },
    // 保存
    save(callback) {
      this.isCheck = false;
      new Promise((resolve, reject) => {
        this.saveBefore(resolve);
      }).then(() => {
        if (this.isCheck) {
          return;
        }
        let reg =
          /^(-|0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2})|-([1-9][0-9]{0,6})|-([1-9][0-9]{0,6}\.\d{0,2})|-(0\.\d{0,2}))$/;
        if (
          !reg.test(this.formData.liability_deduct) &&
          this.formData.liability_deduct
        ) {
          this.$message.error("责任扣款输入不符合规范，请输入数字");
          return;
        }
        let data = {
          total_guide_bonus: this.formData.total_guide_bonus,
          shop_code: this.formData.shop_code,
          reconcile_date: this.formData.reconcile_date,
          excitation_service_cost: this.formData.excitation_service_cost,
          his_tax_adjustment: this.formData.his_tax_adjustment,
          his_service_adjustment: this.formData.his_service_adjustment,
          invoice_dot: this.formData.invoice_dot,
          invoice_type: this.formData.invoice_type,
          remark: this.formData.remark,
          liability_deduct: this.formData.liability_deduct,
          order_no: this.formData.order_no,
          cloudWpServiceOrderCollectList: this.cloudWpServiceOrderCollectList,
        };
        this.ajax.postStream(
          "/order-web/api/cloudWpOrder/save?permissionCode=WP_SERVICE_ORDER_SAVE",
          data,
          (d) => {
            if (d.body.result) {
              this.$message.success(d.body.msg || "");
              callback && callback();
              setTimeout(() => {
                this.initData();
              }, 1000);
            } else {
              this.$message.error(d.body.msg || "");
            }
          },
          (err) => {
            this.$message.error(err);
          }
        );
      });
    },
    closeCurrentComponent() {
      let _this = this;
      _this.$root.eventHandle.$emit("removeTab", _this.params.tabName);
      return;
    },
    invoiceChange(val) {
      if (
        this.formData.invoice_type == "PAPER" ||
        this.formData.invoice_type == "ELECTRONIC"
      ) {
        this.formData.invoice_dot = null;
        this.dotList = [
          {
            value: 0,
            label: "0%",
          },
          {
            value: 0.01,
            label: "1%",
          },
        ];
      } else {
        this.dotList = [
          {
            value: 0.03,
            label: "3%",
          },
          {
            value: 0.06,
            label: "6%",
          },
        ];
      }
    },
    fileClick() {
      window.open(this.fileVO[0].path);
    },
  },
  mounted() {
    var _this = this;
    if (this.params.shop_code && this.params.reconcile_date) {
      _this.initData(); //初使化数据
    }
  },
  watch: {
    "formData.order_status": function (newVal, oldVal) {
      if (newVal) {
        let val = newVal;
        if (val == "CREATED") {
          this.formData.order_status_name = "创建";
        }
        if (val == "SUBMIT") {
          this.formData.order_status_name = "提交";
        }
        if (val == "DEALER_CHECK") {
          this.formData.order_status_name = "经销核对";
        }
        if (val == "AUDIT") {
          this.formData.order_status_name = "审核";
        }
        if (val == "CANCELED") {
          this.formData.order_status_name = "已作废";
        }
        if (val == "INVOICE_INSPECTION") {
          this.formData.order_status_name = "发票查验";
        }
        if (val == "OVERTIME_FORCE_END") {
          this.formData.order_status_name = "超时强制完结";
        }
        if (val == "OVERTIME_CANCELED") {
          this.formData.order_status_name = "超期作废";
        }
      }
    },
    listActiveName: function (newVal) {
      if (newVal == "invoiceInfo") {
        this.getInvoiceInspection();
      }
    },
  },

  destroyed() {},
};
</script>
