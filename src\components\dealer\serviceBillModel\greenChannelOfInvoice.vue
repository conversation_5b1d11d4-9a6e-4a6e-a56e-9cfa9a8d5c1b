<template>
    <div class="xpt-flex">
        <el-row :gutter="40" class="xpt-top">
			<el-col :span='10'>
                <el-button type='primary' size='mini'  @click="confirm">确认</el-button>
                <el-button type='warning' size='mini'  @click="cancel">取消</el-button>
            </el-col>
        </el-row>
        <div class="xpt-flex__bottom">
            <el-form label-position="right"  label-width="120px">
                <el-form-item label="上传发票文件" required>
                    <el-button type='primary' size='mini'  @click="uploadFile">上传附件</el-button>
                </el-form-item>
            </el-form>
            <ul v-show="fileList.length > 0" class="file-auto">
                <li v-for="(item, idx) in fileList" :key="idx">
                    <a class="white-text" :href="item.path" target="_blank" :download="item.path">{{item.path}}</a><i class="el-icon-delete" style="width: 20px; margin:10px;" @click="() => deleteFile(idx)"></i><el-input size='mini' placeholder="请输入发票号码" :value="item.invoice_number" @input="setInvoiceNumber($event, idx)"></el-input>
                </li>
            </ul>
        </div>
        <invoice-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData" :callback="postCallback" accept=".pdf,.PDF,.jpg,.png,.jpeg,.JPG,.PNG,.JPEG"></invoice-upload>
    </div>
</template>
<script>
import upload from "./upload"
export default {
    props: ['params'],
    components: {
        'invoiceUpload': upload
    },
    data () {
        return {
            uploadData: {},
            ifClickUpload: false,
            fileList: []
        }
    },
    methods: {
        confirm () {
            let self = this
            if (this.fileList.length < 0) {
                this.$message.error('请上传附件')
                return
            }
            if (!(this.fileList.every(item => Boolean(item.invoice_number)))) {
                this.$message.error('请填写附件对应的发票号码！')
                return
            }
            this.ajax.postStream('/order-web/api/cloudWpOrder/invoice/setPass', { 
                "service_order_id": this.params.service_order_id,
                "filePathForSetPasses": this.fileList,
                "version": this.params.version
            }, res => {
                if(res.body.result){
                    self.params.callback()
                    self.cancel()
                } else {
                    self.$message.error(res.body.msg)
                }
            }, err => {
                self.$message.error(err)
            })
        },
        cancel () {
            this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
        },
        uploadFile () {
            this.ifClickUpload = true;
            this.uploadData = {
                parent_name: "SERVICE_BILL_ORDER",
                parent_no: this.params.service_order_no,
                child_name: "",
                child_no: '',
                content: JSON.parse(JSON.stringify({}))
            }
            setTimeout(() => {
                this.ifClickUpload = false;
            }, 100);
        },
        postCallback (data) {
            let list = data.reduce((arr, item) => {
                let obj = {
                    path: item,
                    invoice_number: ''
                }
                arr.push(obj)
                return arr
            }, [])
            this.fileList.push(...list)
        },
        deleteFile(idx) {
            this.fileList.splice(idx,1)
        },
        setInvoiceNumber(unm, idx) {
            this.fileList[idx].invoice_number = unm
        }
    }
}
</script>
<style scoped>
    .white-text{
        width: 356px;
        display: inline-block;
        height: 24px;
        overflow: hidden;
        line-height: 24px;
        padding-top: 6px;
    }
    .file-auto {
        overflow: auto;
        height: calc(100% - 28px);
    }
</style>
