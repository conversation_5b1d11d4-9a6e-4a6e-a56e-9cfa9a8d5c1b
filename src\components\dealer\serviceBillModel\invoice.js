export default {
    data () {
        let self = this;
        return {
            invoiceInspectionDisabled: false,
            greenChannelOfInvoiceDisabled: false,
            ifClickUpload: false,
            uploadData: {},
            invoiceList: [],
            selectInvoiceList: [],
            invoiceCols: [
                {
                    label: "发票文件",
                    prop: "file_path",
                    redirectClick: d =>{
                        window.open(d.file_path)
                    }
                },{
                    label: "操作",
                    prop: "operate",
                    slot: 'operate',
                    width: 140
                },{
                    label: "查验结果",
                    prop: "inspection_result",
                    slot: "inspection_result"
                },
                {
                    label: "发票状态",
                    prop: "ins_status",
                    formatter(val) {
                        switch (val) {
                            case 'SUBMIT':
                            return '已提交';
                            case 'WAIT':
                            return '待查验';
                            case 'CEHCKSUC':
                            return '查验通过';
                            case 'CEHCKERR':
                            return '查验不通过';
                            default:
                            return val
                        }
                    }
                },
                {
                    label: "发票类型",
                    prop: "invoice_type",
                    formatter: (val) => {
                        let obj = __AUX.get('wp_service_invoice_type').reduce((a, b) => {
                            a[b.code] = b.remark
                            return a
                        }, {})
                        return obj[val]
                    },
                    width: 120
                },
                {
                    label: "发票代码",
                    prop: "invoice_code",
                    width: 120
                },
                {
                    label: "发票号码",
                    prop: "invoice_number",
                    width: 100
                },
                {
                    label: "开票日期",
                    prop: "billing_date",
                    format: "dataFormat1",
                    width: 130
                },
                {
                    label: "税率",
                    prop: "tax_rate",
                    formatter(val) {
                        return val ? (val/10).toFixed(2) + '%' : val
                    }
                },{
                    label: "税额",
                    prop: "total_tax",
                },{
                    label: "不含税金额",
                    prop: "total_amount",
                },{
                    label: "价税合计",
                    prop: "amount_tax",
                },{
                    label: "购买方纳税人识别码",
                    prop: "purchaser_tax_no",
                    width: 130
                },{
                    label: "购买方名称",
                    prop: "purchaser_name",
                },{
                    label: "销售方纳税人识别码",
                    prop: "sales_tax_no",
                    width: 130
                },{
                    label: "销售方名称",
                    prop: "sales_name",
                },{
                    label: "是否手工输入查验",
                    prop: "if_person_made",
                    formatter(val) {
                        return val == 'Y' ? '是' : '否'
                    },
                    width: 130
                },
                {
                    label: "创建人",
                    prop: "create_name"
                },
                {
                    label: "创建时间",
                    prop: "create_time",
                    format: "dataFormat1",
                }
            ],
            invoiceBtns: [
                {
                    type: 'primary',
                    txt: '+上传附件',
                    disabled: () => {
                        return !(self.ifDealerCheck && self.ifVersion)
                    },
                    loading:false,
                    click: ()=>{
                        self.uploadFile();
                    }
                },{
                    type: 'danger',
                    txt: '删除',
                    click: ()=>{
                        self.deleteInvoice();
                    }
                },{
                    type: 'primary',
                    txt: '提交',
                    disabled: () => {
                        return !(self.ifDealerCheck && self.ifVersion)
                    },
                    click: ()=>{
                        self.submitInvoice();
                    }
                }
            ],
            version: 0,
            ifVersion: false
        }
    },
    methods: {
        getEmployeeInfoGroupList(resolve){
            this.ajax.postStream('/user-web/api/userPerson/getUserPersonGroupList',{personId:this.getEmployeeInfo('personId')}, res=>{
                let groupList = []
                if(res.body.result && res.body.content){
                    let list = res.body.content.list || [];
                    if (list && list.length > 0) {
                        list.forEach(item => {
                            let ifDisabled = item.disableTime ? item.disableTime > new Date().getTime() : true
                            if (item.enableTime < new Date().getTime() && ifDisabled) {
                                groupList.push(item.salesmanType)
                            }
                        });
                    }
                } else {
                    res.body.msg && this.$message.error(res.body.msg);
                }
                resolve && resolve(groupList)
            }, err => {
                this.$message.error(err);
                resolve && resolve([])
            });
        },
        getUserPersonCustomerAttribute(resolve){
            this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttribute', {
                personId: this.getEmployeeInfo('personId'),
                salesmanType: 'GLOBAL',
				attribute: 'CUSTOMER',
			}, res => {
                resolve && resolve(res.body.content.attributeValue)
			}, err => {
                resolve && resolve('')
            })
        },
        checkPermissionByUploadFile(callback) {
            let self = this
            new Promise(resolve => {
				this.getEmployeeInfoGroupList(resolve)
			}).then((groupList) => {
				new Promise(sResolve => {
					this.getUserPersonCustomerAttribute(sResolve);
				}).then((custId) => {
					if (
                        groupList.length > 0 && 
                        (
                            groupList.includes('RECEIVABLE_ACCOUNTANT') || ((groupList.includes('DEALER_SHOPKEEPER') || groupList.includes('DEALER_GUIDE')) && self.formData.customer === custId)
                        )
                    ) {
                        callback && callback()
                        return true
                    } else {
                        self.$message.error('应收会计人员或店铺的经销商人员才可操作')
                        return false
                    }
				})
			})
        },
        uploading() {
            this.ifClickUpload = true;
            this.uploadData = {
                parent_name: "SERVICE_BILL_ORDER",
                parent_no: this.formData.order_no,
                child_name: "",
                child_no: "",
                content: JSON.parse(JSON.stringify({}))
            }
            setTimeout(() => {
                this.ifClickUpload = false;
            }, 100);
        },
        uploadFile () {
            if (!this.checkPermissionByUploadFile(this.uploading)) return
        },
        postCallback(data) {
            this.ajax.postStream('/order-web/api/cloudWpOrder/invoice/saveUpLoadFile', { 
                "service_order_id": this.params.service_order_id,
                "filePaths": data,
                "version": this.version
            }, res => {
                this.ifClickUpload = false;
                if(res.body.result){
                    this.getInvoiceInspection()
                } else {
                    this.$message.error(res.body.msg)
                }
            }, err=> {
                this.$message.error(err)
            })
        },
        selectInvoice(list) {
            this.selectInvoiceList = list
        },
        deleteInvoice () {
            if (!this.checkSelectInvoiceLength()) return
            if (!this.checkSelectInvoicePerson()) return
            let ids = this.selectInvoiceList.reduce((arr, item) => {
                if (item.ins_id) {
                    arr.push(item.ins_id)
                }
                return arr
            }, []), self = this
            this.ajax.postStream("/order-web/api/cloudWpOrder/invoice/delete", ids, function(res){
				if(res.body.result){
					self.$message.success(res.body.msg);
				}else{
					self.$message.error(res.body.msg);
				}
                self.getInvoiceInspection()
			}, err => {
				self.$message.error(err);
			});
        },
        submitInvoice () {
            if (!this.checkSelectInvoiceLength()) return
            let ids = this.selectInvoiceList.reduce((arr, item) => {
                if (item.ins_id) {
                    arr.push(item.ins_id)
                }
                return arr
            }, []), self = this
            this.ajax.postStream("/order-web/api/cloudWpOrder/invoice/submit", {
                service_order_id: this.params.service_order_id,
                inSIds: ids,
                version: this.version
            },function(res){
				if(res.body.result){
                    self.$message.success(res.body.msg);
                    self.initData(true)
				}else{
					self.$message.error(res.body.msg);
				}
			}, err => {
				self.$message.error(err);
			});
        },
        invoiceInspectionResult (row) {
            let params = {
                type: 'SEE',
                ins_id: row.ins_id,
                service_order_id: this.params.service_order_id,
                if_person_made: row.if_person_made,
            }
            this.$root.eventHandle.$emit('alert',{      
		        params:params,
		        component:()=>import('@components/dealer/serviceBillModel/invoiceInspection.vue'),
		        style:'width:700px;height:500px',
		        title:'发票查验'
            });
        },
        checkLockPerson(row){
            if (row.create_id != this.getEmployeeInfo('id')) {
                this.$message.error('当前明细的创建人才可操作')
                return false
            } else {
                return true
            }
        },
        checkSelectInvoiceLength(){
            if (this.selectInvoiceList.length > 0) {
                return true
            } else {
                this.$message.error('请先勾选明细')
                return false
            }
        },
        checkSelectInvoicePerson() {
            let ifSameStatus = this.selectInvoiceList.every(item=>{
                return item.ins_status == 'WAIT'
            })
            let ifSamePerson = this.selectInvoiceList.every(item=>{
                return item.create_id == this.getEmployeeInfo('id')
            })
            if (!ifSameStatus) {
                this.$message.error('只可删除待查验的明细数据')
            } else if (!ifSamePerson) {
                this.$message.error('当前明细的创建人才可操作')
            }
            return (ifSameStatus && ifSamePerson)
        },
        invoiceInspection (row) {
            if (!this.checkLockPerson(row)) return
            let self = this
            let params = {
                ins_id: row.ins_id,
                service_order_id: this.params.service_order_id,
                type: 'INSPECTION',
                if_person_made: row.if_person_made,
                __close: () => {
                    self.getInvoiceInspection()
                }
            }
            this.$root.eventHandle.$emit('alert',{
		        params:params,
		        component:()=>import('@components/dealer/serviceBillModel/invoiceInspection.vue'),
		        style:'width:700px;height:500px',
		        title:'发票查验'
            });
        },
        invoiceInspectionByHand (row) {
            if (!this.checkLockPerson(row)) return
            let self = this
            let params = {
                ins_id: row.ins_id,
                service_order_id: this.params.service_order_id,
                __close: () => {
                    self.getInvoiceInspection()
                }
            }
            this.$root.eventHandle.$emit('alert',{
		        params:params,
		        component:()=>import('@components/dealer/serviceBillModel/invoiceInspectionByHand.vue'),
		        style:'width:700px;height:500px',
		        title:'手工查验'
            });
        },
        greenChannelOfInvoice () {
            let self = this
            new Promise(resolve => {
				this.getEmployeeInfoGroupList(resolve)
			}).then((groupList) => {
                if (!(groupList.length > 0 && groupList.includes('RECEIVABLE_ACCOUNTANT'))) {
                    this.$message.error('应收会计人员才可操作')
                    return
                }
                let paramsData = {
                    service_order_no: this.formData.order_no,
                    service_order_id: this.params.service_order_id,
                    version: this.version,
                    callback: () => {
                        self.initData(true)
                    }
                }
                this.$root.eventHandle.$emit('alert',{
                    params:paramsData,
                    component:()=>import('@components/dealer/serviceBillModel/greenChannelOfInvoice.vue'),
                    style:'width:80%;height:80%',
                    title:'设置发票查验通过'
                });
            })
        },
        getInvoiceInspection () {
            if(this.params.service_order_id){
				this.ajax.postStream('/order-web/api/cloudWpOrder/invoice/list', this.params.service_order_id , res => {
					if(res.body.result){
						this.invoiceList = res.body.content.list || []
                        this.version = res.body.content.version || 0
                        this.ifVersion = true
					} else {
                        this.ifVersion = false
                    }
				}, err => {
                    this.ifVersion = false
                })
			}
        },
        
    }
}