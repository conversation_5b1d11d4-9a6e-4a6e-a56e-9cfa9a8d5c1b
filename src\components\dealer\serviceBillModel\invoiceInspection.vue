<template>
    <div class="xpt-flex invoice_dialog">
        <div class="middle-area" v-if="/^(REQUESTFAIL|REQUESTING)$/.test(status)">
            <p>{{requestMsg}}</p>
        </div>
        <div v-if="status=='SUCCESS'">
            <div class="invoice_result margin10">
                <p v-if="/^(CEHCKSUC|SUBMIT)$/.test(inspectionInfo.ins_status)">查验通过<span class="pl10"><i class="el-icon-circle-check success_icon"></i></span></p>
                <p v-else>查验不通过<span class="pl10 fail-icon"><i class="el-icon-circle-cross pr10"></i>请按规范要求重新开票!</span></p>
            </div>
            <div class="invoice_content">
                <div class="invoice_content_left">
                    <p class="margin10">发票类型：{{invoiceTypeObj[inspectionInfo.invoice_type]}}</p>
                    <p class="margin10">发票代码：{{inspectionInfo.invoice_code}}</p>
                    <p class="margin10">发票号码：{{inspectionInfo.invoice_number}}</p>
                    <p class="margin10">开票日期：{{inspectionInfo.billing_date | dataFormat}}</p>
                    <p class="margin10">税率：{{inspectionInfo.tax_rate ? (inspectionInfo.tax_rate/10).toFixed(2) + '%' : inspectionInfo.tax_rate}}</p>
                    <p class="margin10">税额：{{inspectionInfo.total_tax}}</p>
                    <p class="margin10">不含税金额：{{inspectionInfo.total_amount}}</p>
                    <p class="margin10">价税合计：{{inspectionInfo.amount_tax}}</p>
                    <p class="margin10">购买方纳税人识别号：{{inspectionInfo.purchaser_tax_no}}</p>
                    <p class="margin10">购买方名称：{{inspectionInfo.purchaser_name}}</p>
                    <p class="margin10">销售方纳税人识别号：{{inspectionInfo.sales_tax_no}}</p>
                    <p class="margin10">销售方名称：{{inspectionInfo.sales_name}}</p>
                </div>
                <div class="invoice_content_right">
                    <p class="margin10" v-for='info in compareInfo' :key="info.form_id">{{info.content}}<i class="pl10" :class="info.icon_show == 'Y' ? 'el-icon-check success_icon' : 'el-icon-close fail-icon'"></i></p>
                </div>
            </div>
        </div>
        <div v-if="status=='FAIL'" class="middle-area fail-area">
            <p class="fail-text">
                <span style="height: 72px;width: 76px;"><img src='/static/images/invoice-icon.png' alt='logo' style="height: 72px"/></span><span class="fail-text">{{errorMsg}}</span>
            </p>
            <p class="fail-icon fail-msg">{{ifAutoInspection ? '请尝试【手工查验】或联系财务专员处理' : '请联系专员处理'}}</p>
        </div>
    </div>
</template>
<script>
export default {
    props: ['params'],
    data () {
        return {
            inspectionInfo: {},
            compareInfo: [],
            status: '',
            errorMsg: '',
            alerts: [],
            requestMsg: '查验中……',
            invoiceTypeObj: __AUX.get('wp_service_invoice_type').reduce((a, b) => {
                a[b.code] = b.remark
                return a
            }, {})
        }
    },
    computed: {
        ifAutoInspection () {
            return this.params.type == 'INSPECTION' || (this.params.type == 'SEE' && this.params.if_person_made == 'N')
        }
    },
    methods: {
        inspection() {
            let postData = {
                'SEE': {
                    url: '/order-web/api/cloudWpOrder/invoice/selectInspection',
                    data: this.params.ins_id
                },
                'INSPECTION': {
                    url: "/order-web/api/cloudWpOrder/invoice/scanningInspection",
                    data: {
                        ind_id: this.params.ins_id,
                        service_order_id: this.params.service_order_id
                    }
                },
            }, self = this
            this.status = this.params.type == 'INSPECTION' ? 'REQUESTING' : ''
            this.ajax.postStream(postData[this.params.type].url, postData[this.params.type].data, function(res){
				if(res.body.result){
                    let inspectionResult = res.body.content.invoiceInspection
                    let compareResult = res.body.content.compareResultList
                    if (inspectionResult.data_code != 0) {
                        self.inspectionInfo = {}
                        self.compareInfo = []
                        self.status = 'FAIL'
                        self.errorMsg = inspectionResult.data_content ? inspectionResult.data_content : '数据解析出错！'
                    } else {
                        self.inspectionInfo = inspectionResult || []
                        self.compareInfo = compareResult || {}
                        self.status = 'SUCCESS'
                    }
				}else{
                    self.inspectionInfo = {}
                    self.compareInfo = []
                    self.requestMsg = res.body.msg ? res.body.msg : '数据解析出错！'
					self.$message.error(res.body.msg);
                    self.status = 'REQUESTFAIL'
				}
			}, err => {
                self.inspectionInfo = {}
                self.compareInfo = []
                self.status = 'REQUESTFAIL'
                self.requestMsg = err
				self.$message.error(err);
			});
        }
    },
    beforeDestroy(){
        if (this.params.alertId && (new Set(this.alerts).has(this.params.alertId))) {
            if (this.params.type != 'SEE') {
                this.params.__close()
            }
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
        }
    },
    mounted() {
        if (this.$parent.alerts && this.$parent.alerts.length > 0) {
            this.$parent.alerts.forEach(item => {
                this.alerts.push(item.params.alertId)
            })
        }
        this.inspection()
    }
}
</script>
<style scoped>
.invoice_dialog {
    padding: 20px
}
.invoice_content_left {
    width: 300px;
    float: left;
}
.success_icon {
    color: green;
}
.fail-icon {
    color: red
}
.pl10 {
    padding-left: 10px;
}
.pr10 {
    padding-right: 10px;
}
.margin10 {
    margin: 10px;
}
.fail-text {
    height: 72px;
    line-height: 72px;
    display: inline-block;
    vertical-align: top;
}
.fail-msg {
    margin-top: 10px;
}
.middle-area {
    margin: auto
}
</style>
