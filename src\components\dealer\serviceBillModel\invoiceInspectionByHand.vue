<template>
    <div class="xpt-flex">
        <div class="xpt-flex__bottom" v-if="handlerStatus">
            <el-form label-position="right"  label-width="140px" :model='form' :rules='rules' ref='form' class="invoice_form">
                <el-form-item label="发票类型" prop="invoiceType">
                    <xpt-select-aux v-model='invoiceType' aux_name='wp_service_invoice_type' @change="changeInvoiceType"></xpt-select-aux>
                </el-form-item>
                <el-form-item :label="invoiceItem.name" required :prop="invoiceItem.code" v-for="invoiceItem in invoiceFormItem" :key="invoiceItem.code">
                    <el-date-picker  v-if="invoiceItem.type == 'time'" v-model="form[invoiceItem.code]" type="date" placeholder="选择时间" size="mini" style="width:180px;" :editable="false"></el-date-picker>
                    <el-input v-else-if="invoiceItem.type == 'input'" v-model="form[invoiceItem.code]" size='mini' ></el-input>
                    <el-tooltip v-if='rules[invoiceItem.code][0].isShow' class="item" effect="dark" :content="rules[invoiceItem.code][0].message" placement="right-start" popper-class='xpt-form__error'>
                        <i class='el-icon-warning'></i>
                    </el-tooltip>
                </el-form-item>
            </el-form>
            <div class="invoice_btn_top">
                <el-button type='primary' size='mini'  @click="inspection">查验</el-button>
                <el-button type='warning' size='mini'  @click="inset">重置</el-button>
            </div>
        </div>
        <div class="xpt-flex invoice_dialog" v-else>
            <div class="middle-area" v-if="/^(REQUESTFAIL)$/.test(status)">
                <p>{{requestMsg}}</p>
            </div>
            <div v-if="status=='SUCCESS'">
                <div class="invoice_result margin10">
                    <p v-if="/^(CEHCKSUC|SUBMIT)$/.test(inspectionInfo.ins_status)">查验通过<span class="pl10"><i class="el-icon-circle-check success_icon"></i></span></p>
                    <p v-else>查验不通过<span class="pl10 pr10"><i class="el-icon-circle-cross fail-icon"></i></span>请按规范要求重新开票!</p>
                </div>
                <div class="invoice_content">
                    <div class="invoice_content_left">
                        <p class="margin10">发票类型：{{invoiceTypeObj[inspectionInfo.invoice_type]}}</p>
                        <p class="margin10">发票代码：{{inspectionInfo.invoice_code}}</p>
                        <p class="margin10">发票号码：{{inspectionInfo.invoice_number}}</p>
                        <p class="margin10">开票日期：{{inspectionInfo.billing_date | dataFormat}}</p>
                        <p class="margin10">税率：{{inspectionInfo.tax_rate ? (inspectionInfo.tax_rate/10).toFixed(2) + '%' : inspectionInfo.tax_rate}}</p>
                        <p class="margin10">税额：{{inspectionInfo.total_tax}}</p>
                        <p class="margin10">不含税金额：{{inspectionInfo.total_amount}}</p>
                        <p class="margin10">价税合计：{{inspectionInfo.amount_tax}}</p>
                        <p class="margin10">购买方纳税人识别号：{{inspectionInfo.purchaser_tax_no}}</p>
                        <p class="margin10">购买方名称：{{inspectionInfo.purchaser_name}}</p>
                        <p class="margin10">销售方纳税人识别号：{{inspectionInfo.sales_tax_no}}</p>
                        <p class="margin10">销售方名称：{{inspectionInfo.sales_name}}</p>
                    </div>
                    <div class="invoice_content_right">
                        <p class="margin10" v-for='info in compareInfo' :key="info.form_id">{{info.content}}<i class="pl10" :class="info.icon_show == 'Y' ? 'el-icon-check success_icon' : 'el-icon-close fail-icon'"></i></p>
                    </div>
                </div>
            </div>
            <div v-if="status=='FAIL'" class="middle-area fail-area">
                <p class="fail-text">
                    <span style="height: 72px;width: 76px;"><img src='/static/images/invoice-icon.png' alt='logo' style="height: 72px"/></span><span class="fail-text">{{errorMsg}}</span>
                </p>
                <p class="fail-icon fail-msg">请联系专员处理</p>
            </div>
        </div>
    </div>
</template>
<script>
import VL from '@common/validate.js'
export default {
    props: ['params'],
    data () {
        return {
            form: {},
            rules: {},
            info: {},

            handlerStatus: true,
            inspectionInfo: {},
            compareInfo: [],
            status: '',
            errorMsg: '',
            alerts: [],
            requestMsg: '',
            invoiceTypeObj: __AUX.get('wp_service_invoice_type').reduce((a, b) => {
                a[b.code] = b.remark
                return a
            }, {}),
            invoiceFormItem: [],
            invoiceType: ''
        }
    },
    methods: {
        inspection () {
            this.$refs.form.validate((valid) => {
                if(!valid) return
                if (this.form.totalAmount && !((/^([1-9]\d*|0)(\.\d{1,2})?$/.test(this.form.totalAmount)) && this.form.totalAmount > 0)) {
                    this.$message.error('【不含税金额】, 请输入大于0且可以保留两位小数')
                    return
                }
                if (this.form.amountTax && !(/^([1-9]\d*|0)(\.\d{1,2})?$/.test(this.form.amountTax) && this.form.amountTax > 0)) {
                    this.$message.error('【价税合计】, 请输入大于0且可以保留两位小数')
                    return
                }
                let copyForm = Object.assign({}, this.form)
                let filters = this.$root.$options.filters
                copyForm.billingDate = filters['dataFormat'](this.form.billingDate)
                let postData = {
                    service_order_id: this.params.service_order_id,
                    ind_id: this.params.ins_id,
                    personMadeInvoiceInspectionVO: copyForm
                }, self = this
                this.ajax.postStream("/order-web/api/cloudWpOrder/invoice/personMadeInspection", postData,function(res){
                    if(res.body.result){
                        self.handlerStatus = false
                        let inspectionResult = res.body.content.invoiceInspection
                        let compareResult = res.body.content.compareResultList
                        if (inspectionResult.data_code != 0) {
                            self.inspectionInfo = {}
                            self.compareInfo = []
                            self.status = 'FAIL'
                            self.errorMsg = inspectionResult.data_content ? inspectionResult.data_content : '数据解析出错！'
                        } else {
                            self.inspectionInfo = inspectionResult || []
                            self.compareInfo = compareResult || {}
                            self.status = 'SUCCESS'
                        }
                    }else{
                        self.inspectionInfo = {}
                        self.compareInfo = []
                        self.requestMsg = res.body.msg ? res.body.msg : '数据解析出错！'
                        self.$message.error(res.body.msg);
                        self.status = 'REQUESTFAIL'
                        self.handlerStatus = false
                    }
                }, err => {
                    self.status = 'REQUESTFAIL'
                    self.requestMsg = err
                    self.$message.error(err);
                });
            })
        },
        inset () {
            for(let key in this.form) {
                this.form[key] = ''
            }
        },
        changeInvoiceType(val) {
            this.invoiceType = val
            this.form = {}
            this.rules = {}
            let formItemKey = {
                发票代码: {code: 'invoiceCode', type: 'input', name: '发票代码'},
                发票号码: {code: 'invoiceNumber', type: 'input', name: '发票号码'},
                开票日期: {code: 'billingDate', type: 'time', name: '开票日期'},
                不含税金额: {code: 'totalAmount', type: 'input', name: '不含税金额'},
                价税合计: {code: 'amountTax', type: 'input', name: '价税合计'},
                校验码: {code: 'checkCode', type: 'input', name: '校验码'},
                销售方纳税人识别号: {code: 'salesTaxNo', type: 'input', name: '销售方纳税人识别号'},
            }, self = this
            __AUX.get('wp_service_invoice_type').forEach(item => {
                if (item.status && item.code == self.invoiceType) {
                    let tagObj = JSON.parse(item.tag)
                    let formItem = []
                    
                    for(let key in tagObj) {
                        self.$set(self.form, formItemKey[key].code, '')
                        self.$set(self.rules, formItemKey[key].code, [{
                            required:tagObj[key],
                            message:`请${formItemKey[key].type == 'input' ? '输入' : '选择'}${formItemKey[key].name}`,
                            isShow:false,
                            validator: function(rule,value,callback){
                                // 数据校验
                                if(value){
                                    self.rules[rule.field][0].isShow = false;
                                    // 校验成功
                                    callback();
                                }else{
                                    self.rules[rule.field][0].isShow = true
                                    // 校验失败
                                    callback(new Error(''));
                                }
                                }
                            }]
                        )
                        formItem.push(formItemKey[key])
                    }
                    this.invoiceFormItem = formItem
                }
            })
        }
    },
    mounted () {
        if (this.$parent.alerts && this.$parent.alerts.length > 0) {
            this.$parent.alerts.forEach(item => {
                this.alerts.push(item.params.alertId)
            })
        }
    },
    beforeDestroy(){
        if (this.params && this.params.alertId && (new Set(this.alerts).has(this.params.alertId))) {
            this.params.__close()
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
        }
    },
    created() {
    }
}
</script>

<style scoped>
.invoice_dialog {
    padding: 20px
}
.invoice_content_left {
    width: 300px;
    float: left;
}
.success_icon {
    color: green;
}
.fail-icon {
    color: red
}
.pl10 {
    padding-left: 10px;
}
.pr10 {
    padding-right: 10px;
}
.margin10 {
    margin: 10px;
}
.fail-text {
    height: 72px;
    line-height: 72px;
    display: inline-block;
    vertical-align: top;
}
.fail-msg {
    margin-top: 10px;
}
.middle-area {
    margin: auto
}
.invoice_form {
    margin-top: 20px;
}
.invoice_form .el-input{
    width: 268px !important;
}
.invoice_form .el-select{
    width: 268px !important;
}
.invoice_btn_top{
    padding-top: 20px;
    padding-left: 200px;
}
</style>
