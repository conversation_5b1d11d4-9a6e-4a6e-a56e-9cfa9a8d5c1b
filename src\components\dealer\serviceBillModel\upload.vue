<template >
    <input v-if="isMultiple" style="display: none" type="file" :id='files' multiple="multiple" @change='change' :accept="accept">
    <input v-else style="display: none" type="file" :id='files' @change='change' :accept="accept">
</template>
<script>
import Fn from '@common/Fn.js'
import HelpFile  from '@common/helpFile.js'
export default {
    data() {
        return {
            index: 0,
            imgList: [],
            oldData: null,
            files:'files'+Math.random(),//随机定义id，防止出现相同id
            host:null,
            uploadNum: 0,
            filesLength: null,
            fileUrlList: []
        }
    },
    props: {
        // 图片数组
        ifClickUpload: {
            type: Boolean,
            default(){
                return false;
            }
        },
        dataObj: {
            type: Object,
            default(){
                return {};
            }
        },
        // 上传完成后callback
        callback: {
            type: Function,
            default(){
                return f => f
            }
        },
        // 上传至新平台还是阿里云
        ifFile: {
            type: <PERSON>olean,
            default(){
                return false
            }
        },
        fileType: {
            type: String,
            default(){
                return ''
            }
        },
        ifMaxSize: {
            type: Boolean,
            default(){
                return false
            }
        },
        maxSize:{
            type: Number,
            default(){
                return 10*1024*1024;
            }
        },
        maxFileSize: {
            type: Number,
            default() {
                return 100
            }
        },
        accept: {
            type: String,
            default(){
                return'*';
            }
        },
        isMultiple: {
            type: Boolean,
            default: true
        }
    },
    methods: {
        //获取上传文件的信息，并逐个上传
        change() {
            let files = document.getElementById(this.files).files,
                len = files.length,
                fr = new FileReader(),
                index = 0,
                self = this;
            if (len > self.maxFileSize) {
                let msg = `请最多上传${self.maxFileSize}个文件`
                self.$message.error(msg)
                return
            }
            fr.readAsDataURL(files[0]);
            this.uploadNum = 0;
            this.filesLength = files.length;
            self.upload(files,0);
            fr.onload = function(e) {
                index += 1;
                if(index<len){
                    fr.readAsDataURL(files[index]);
                    self.upload(files,index)
                }
            }
        },
        // 获取上传图片的key值
        validateKey(resolve){
            this.ajax.postStream('/app-web/app/file/validateKey.do', {type:"GET"} ,(res)=>{
                if(res.body.success){
                    resolve && resolve(res.body.data)
                }else{
                    this.$message.error('获取文件路径失败，请重新操作');
                }
            })
        },
        // 提交至服务器
        upload(files,index) {
            let self = this;
            new Promise((resolve,reject)=>{
                this.validateKey(resolve);
            }).then((key)=>{
                let formData = new FormData();
                formData.append('file', files[index]);
                if(self.ifMaxSize){
                    if(files[index].size>this.maxSize){
                        this.$message.error('该文件超过最大限制'+(this.maxSize/1024/1024)+'M,上传失败');
                        return false;
                    }
                }
                let file_type = files[index].name.split('.').pop();
                if(self.fileType){
                    if(this.fileType.indexOf(file_type)===-1){
                        this.$message.error('该文件类型应该为'+(this.fileType)+',上传失败');
                        return false;
                    }
                }

                let host = this.host.split('//')[1];
                let requestHeaderPath=this.getUploadFileApiHeader();
                let url = requestHeaderPath+host+'/'+key+'.do';
                
                this.ajax.post(url,formData, (s) => {
                    var data ={
                        file_type: files[index].name.split('.').pop(),// 文件类型 string
                        order_no: this.oldData.parent_no,// 单据编号 string
                        sub_order_no: this.oldData.child_no,// 子单据编号 string
                        name: files[index].name,// 名字 string
                        size: files[index].size,// 大小 long
                        group_value_json: JSON.stringify(this.oldData),//  分组内容 string
                        // path: s.body.data,//  地址 string
                        ext_data: null,// 扩展字段 string
                    }
                    if (this.ifFile) {
                        if(s.ok && s.body && s.body.content){
                            data.path = s.body.content//  地址 string
                            self.fileUrlList.push(s.body.content)
                        }
                    } else {
                        if(s.ok && s.body && s.body.data){
                            data.path = s.body.data//  地址 string
                            self.fileUrlList.push(s.body.data)
                        }
                    }
                    this.uploadNum++;
                    //全部上传完才显示成功
                    if(this.uploadNum === files.length){
                        this.$message.success('上传成功');
                        this.callback(self.fileUrlList)
                    }
                }, (e) => {
                    this.$message({
                        message: files[index].name+'上传失败',
                        type: 'error'
                    })
                })
            })
            
            
        },
    },
    watch:{
        'ifClickUpload': function(newVal,oldVal) {
            if(newVal) {
                this.fileUrlList = []
                document.getElementById(this.files).value = '';
                document.getElementById(this.files).click();

                if(this.host) {
                    return;
                }
                //获取域名
                this.ajax.postStream('/file-iweb/api/cloud/fileConfig/get',{},res => {
                    if(res.body.result){
                        this.host = res.body.content.host;

                    }else {
                        this.$message.error(res.body.msg);
                    }
                })
            }
        },
        'dataObj': function(newVal,oldVal) {
            this.oldData = JSON.parse(JSON.stringify(newVal));
            var list = [];
            if(this.oldData.parent_name && this.oldData.child_name) {
                list = HelpFile[this.oldData.parent_name][this.oldData.child_name] || [];
            }
            //过滤掉在已经定好的分组里没有分类
            list.forEach(v => {
                if(this.oldData.content){
                    for(var key in this.oldData.content){
                        if(v.name == key){
                            v.value = this.oldData.content[key];
                        }
                    }
                }
            })
            this.oldData.content = list;
        },

    },

    mounted() {
    }
}
</script>
