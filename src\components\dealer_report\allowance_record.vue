<template>
  <div class='xpt-flex'>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          if_need_page: 'N',
          shop_name: '',
          shop_id: '',
          if_timeliness: '',
          summaryConditions: ""
        },
        cols: [
          {
            label: '货款充值单据编号',
            prop: 'order_no',
          }, {
            label: '实收金额',
            prop: 'paid_amount',
          }, {
            label: '单据类型',
            prop: 'business_type',
          }, {
            label: '审核日期',
            prop: 'audit_time',
            format: 'dataFormat1',
          }, {
            label: '备注',
            prop: 'remark',
          }, {
            label: '单据状态',
            prop: 'order_status',
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if (self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {
      queryData() {
//          let data = JSON.parse(JSON.stringify(this.params));
          let data ={};
          data.begin_date = this.params.start_time;
          data.end_date = this.params.end_time;
          data.if_need_page = "Y";
          data.shop_id = this.params.shop_id;
          data.isCutPayment = this.params.type;
          data.page_no = this.query.page_no;
          data.page_size = this.query.page_size;

          this.queryBtnStatus = true;

          let listPromise = new Promise((resolve, reject) => {
            this.ajax.postStream('/reports-web/api/reports/dealer/allowanceRecord', data, res => {
              this.queryBtnStatus = false;
              if (res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content.list || [];
                this.count = content.count || 0;
                resolve(res.body.content.body)
              } else {
                this.$message.error(res.body.msg);
                reject(res.body.msg)
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          })
      },
      computed: {
        staff() {
          return this.query.staff_id;
        },
        staff_group() {
          return this.query.staff_group_id;
        },
        big_group() {
          return this.query.big_group_id;
        }
      },
      watch: {
        staff(n) {
          if (n) {
            this.query.staff_group = '';
            this.query.staff_group_id = '';
            this.query.big_group = '';
            this.query.big_group_id = '';
          }
        },
        staff_group(n) {
          if (n) {
            this.query.staff = '';
            this.query.staff_id = '';
            this.query.big_group = '';
            this.query.big_group_id = '';
          }
        },
        big_group(n) {
          if (n) {
            this.query.staff = '';
            this.query.staff_id = '';
            this.query.staff_group = '';
            this.query.staff_group_id = '';
          }
        }
      },
    },
    mounted(){
//        console.log("the mounted params ========== ", this.params);
        this.queryData();
      },
  }

</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
