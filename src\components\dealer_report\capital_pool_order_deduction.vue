<!-- 资金池订单扣款操作报表 -->
<template>
    <div class="xpt-flex">
        <el-form
            ref="query"
            :rules="rules"
            :model="query"
            label-position="right"
            label-width="120px"
        >
            <el-row :gutter="0" class="xpt-top">
                <el-col :span="6">
                    <el-form-item label="店铺名称：" prop="shop_name"  v-if="!isShopManager">
                        <xpt-input
                            v-model="query.shop_name"
                            icon="search"
                            :on-icon-click="selectShop"
                            @change="shopChange"
                            size="mini"
                            placement="right-start"
                            readonly
                        ></xpt-input>
                    </el-form-item>
                    <el-form-item label="店铺名称：" prop="shop_name"  v-if="isShopManager">
                        <el-input
                            v-model="query.shop_name"
                            size="mini"
                            readonly
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="合并单号：" prop="merge_trade_no">
                        <xpt-input
                            v-model="query.merge_trade_no"
                            size="mini"
                        ></xpt-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="操作类型：" prop="operation_type" v-if="!!params.data&& params.data.operationTypes">
                      <span>采购金额变动,订单变化</span>
                    </el-form-item>
                    <el-form-item label="操作类型：" prop="operation_type" v-else>
                        <el-select
                            placeholder="请选择"
                            size="mini"
                            v-model="query.operation_type"
                        >
                            <el-option
                                label="采购金额变动"
                                value="DEALER_PURCHASE"
                                key="DEALER_PURCHASE"
                            ></el-option>
                            <el-option
                                label="冻结金额变动"
                                value="DEALER_FROZEN"
                                key="DEALER_FROZEN"
                            ></el-option>
                            <el-option
                                label="货款充值变动"
                                value="DEALER_RECHARGE"
                                key="DEALER_RECHARGE"
                            ></el-option>
                            <el-option
                                label="信用货款充值变动"
                                value="DEALER_RECHARGE_CRD"
                                key="DEALER_RECHARGE_CRD"
                            ></el-option>
                            <el-option
                                label="订单变化"
                                value="DEALER_ORDER_CHANGED"
                                key="DEALER_ORDER_CHANGED"
                            ></el-option>
                        </el-select>
                    </el-form-item>

                    
                </el-col>
                <el-col :span="6" class="xpt-align__right">
                    <el-button
                        type="info"
                        size="mini"
                        @click="queryData()"
                        :disabled="queryBtnStatus||isShopLoading"
                        :loading="queryBtnStatus"
                        >实时查询</el-button
                    >
                    <el-button type="primary" size="mini" @click="reset"
                        >重置查询条件</el-button
                    >
                    <el-button
                        type="info"
                        size="mini"
                        @click="exportExcel"
                        :disabled="exportBtnStatus"
                        :loading="exportBtnStatus"
                        >导出</el-button
                    >
                    <el-button
                        type="info"
                        size="mini"
                        @click="
                            showExportList('EXCEL_TYPE_CAPITAL_POOL_DEDUCTION_OPERATION_EXPORT')
                        "
                        >报表导出文件下载</el-button
                    >
                </el-col>
                <el-col :span="6" style="clear: both">
                    <el-form-item
                        label="开始时间："
                        prop="operation_time_begin"
                    >
                        <el-date-picker 
                            v-model="query.operation_time_begin"
                            type="date"
                            placeholder="选择日期"
                            size="mini"
                            :editable="false"
                            :picker-options="beginDateOptions"
                        ></el-date-picker>
                        <el-tooltip
                            v-if="rules.operation_time_begin[0].isShow"
                            class="item"
                            effect="dark"
                            :content="rules.operation_time_begin[0].message"
                            placement="right-start"
                            popper-class="xpt-form__error"
                        >
                            <i class="el-icon-warning"></i>
                        </el-tooltip>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="结束时间：" prop="operation_time_end">
                        <el-date-picker
                            v-model="query.operation_time_end"
                            type="date"
                            placeholder="选择日期"
                            size="mini"
                            :editable="false"
                            :picker-options="endDateOptions"
                        ></el-date-picker>
                        <el-tooltip
                            v-if="rules.operation_time_end[0].isShow"
                            class="item"
                            effect="dark"
                            :content="rules.operation_time_end[0].message"
                            placement="right-start"
                            popper-class="xpt-form__error"
                        >
                            <i class="el-icon-warning"></i>
                        </el-tooltip>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                   <el-form-item label="是否冲收：" prop="operation_type" v-if="!!params.data&& params.data.act_as_income">
                      <span>{{params.data.act_as_income == "Y"? '是':'否'}}</span>
                    </el-form-item>
                    <el-form-item label="充值类型：" prop="operation_type" v-if="!!params.data&& params.data.business_type">
                      <span>货款充值</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <xpt-list
            :showHead="false"
            :data="list"
            :colData="cols"
            :pageTotal="count"
            selection=""
            @page-size-change="pageSizeChange"
            @current-page-change="currentPageChange"
        ></xpt-list>
    </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from '@common/Fn.js'
import mixin from "./mixin.js";
export default {
    props: ["params"],
    mixins: [mixin],
    data() {
        let self = this;
        return {
            query: {
                page_size:50,
                page_no:1,
                shop_id: "",
                shop_name: "",
                merge_trade_no: "",
                operation_type: "",
                operation_time_begin: "",
                operation_time_end: "",
                if_need_page:'Y'
            },
            rules: {
                operation_time_begin: validate.isNotBlank({
                    trigger: "change",
                    self: this,
                    msg: "请填写开始时间",
                }),
                operation_time_end: validate.isNotBlank({
                    trigger: "change",
                    self: this,
                    msg: "请填写结束时间",
                }),
            },
            cols: [
                {
                    label: "关联订单",
                    prop: "merge_trade_no",
                    width: 170,
                },
                {
                    label: "店铺名称",
                    prop: "shop_name",
                    width: 132,
                },
                {
                    label: "操作人",
                    prop: "modifier_name",
                    width: 130,
                },
                {
                    label: "更新时间",
                    prop: "modify_time",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    label: "操作类型",
                    prop: "type",
                    width: 94,
                }, {
                    label: "充值类型",
                    prop: "business_type_name",
                    formatter(val,index,row){
                        if(['DEALER_RECHARGE','DEALER_RECHARGE_CRD'].includes(row.operate_type)){
                            return row.business_type_name;
                        }else{
                            return row.type;
                        }
                    }
                },
                {
                    label: "充值",
                    prop: "ch_recharge_amount",
                },
                {
                    label: "冻结",
                    prop: "ch_frozen_amount",
                },
                {
                    label: "发货",
                    prop: "ch_purchase_amount",
                },
                {
                    label: "预扣",
                    prop: "ch_predit_cash",
                },
                {
                    label: "总余额",
                    prop: "sum_balance",
                },
                {
                    label: "现金余额",
                    prop: "cash_balance",
                },
                {
                    label: "冻结总额",
                    prop: "frozen_amount",
                },
                {
                    label: "发货总额",
                    prop: "purchase_amount",
                },
                {
                    label: "预扣总额",
                    prop: "predit_cash",
                },
                {
                    label: "充值总额",
                    prop: "recharge_amount",
                },
                {
                    label: "信用总额",
                    prop: "credit_amount",
                },
               
            ],
            list: [],
            count: 0,
            // 查询按钮状态
            queryBtnStatus: false,
            // 导出按钮状态
            exportBtnStatus: false,
            beginDateOptions: self.beginDateSet(),
            endDateOptions: self.endDateSet(),
            isShopManager:false,//是否有经销店
            isShopLoading:false,//获取店铺中
        };
    },
    methods: {
        // 选择店铺
        selectShop() {
            if(this.isShopLoading){
                return
            }
            let self = this;
            let params = {
                callback(data) {
                    self.query.shop_id = data.shop_id;
                    self.query.shop_name = data.shop_name;
                    console.log(self.query);
                },
                setWhere: (queryData) => {
                    var newWhere = (queryData.where || []).filter((o) => {
                        return (
                            o.field !== this.setShopDefault.where[0].field &&
                            o.field !== this.setShopDefault.where[1].field
                        );
                    });

                    newWhere = newWhere.concat(this.setShopDefault.where);

                    queryData.where = newWhere;
                },
                selection: "radio",
            };
            this.$root.eventHandle.$emit("alert", {
                params: params,
                component: () => import("@components/shop/list"),
                style: "width:800px;height:500px",
                title: "店铺列表",
            });
        },
        queryData() {
            this.$refs.query.validate((valid) => {
                if (valid) {
                    let data = JSON.parse(JSON.stringify(this.query));
                    delete data.shop_name;
                    data.operation_time_begin = new Date(
                        data.operation_time_begin
                    );
				
                    let endDate =  Fn.dateFormat(data.operation_time_end, 'yyyy-MM-dd 23:59:59')
                   
                    data.operation_time_end = new Date(endDate).getTime();
                    if(this.params.data){
                        data = Object.assign(this.params.data,data);
                    }
                    this.queryBtnStatus = true;
                    this.ajax.postStream(
                        "/reports-web/api/reports/dealer/fundsOperation/list",
                        data,
                        (res) => {
                            this.queryBtnStatus = false;
                            if (res.body.result && res.body.content) {
                                let content = res.body.content;
                                console.log(content);
                                this.list = content.list || [];
                                this.count = content.count || 0;
                            } else {
                                this.$message.error(res.body.msg);
                            }
                        },
                        (err) => {
                            this.$message.error(err);
                            this.queryBtnStatus = false;
                        }
                    );
                }
            });
        },
        // 导出功能
        exportExcel() {
            this.$refs.query.validate((valid) => {
                if (valid) {
                    let data = JSON.parse(JSON.stringify(this.query));
                    delete data.shop_name;
                    data.operation_time_begin = new Date(
                        data.operation_time_begin
                    );
                    data.operation_time_end = new Date(data.operation_time_end);
                    this.exportBtnStatus=true;
                    this.ajax.postStream(
                        "/reports-web/api/reports/dealer/fundsOperation/export",
                        data,
                        (res) => {
                            this.$message({
                                type: res.body.result ? "success" : "error",
                                message: res.body.msg,
                            });
                            this.exportBtnStatus=false;
                        },
                        (err) => {
                            this.$message.error(err);
                            this.exportBtnStatus=false;
                        }
                    );
                }
            });
        },
        setShopDefault() {
            this.setShopDefault.where = [];
            this.ajax.postStream(
                "/user-web/api/sql/listFields",
                { page: "cloud_shop_v2" },
                (res) => {
                    if (res.body.result) {
                        (res.body.content.fields || []).forEach((o) => {
                            if (o.comment === "店铺分类") {
                                this.setShopDefault.where.push({
                                    field: o.field,
                                    table: o.table,
                                    value: "PURCHASE_DEALER",
                                    operator: "=",
                                    condition: "AND",
                                    listWhere: [],
                                });
                            } else if (o.comment === "店铺状态") {
                                this.setShopDefault.where.push({
                                    field: o.field,
                                    table: o.table,
                                    value: "OPEN",
                                    operator: "=",
                                    condition: "AND",
                                    listWhere: [],
                                });
                            }
                        });
                    }
                }
            );
        },
        pageSizeChange(size) {
            this.query.page_size = size;
            this.queryData();
        },
        currentPageChange(page_no) {
            this.query.page_no = page_no;
            this.queryData();
        },
        shopChange(val){
            if(!val){
                this.query.shop_id=''
            }
        },
        reset() {
            //默认店铺重置不置空店铺
            if(!this.isShopManager){
                this.query.shop_name = "";
                this.query.shop_id = "";
            }
            this.query.merge_trade_no = "";
            this.query.operation_type = "";
            this.query.operation_time_begin = "";
            this.query.operation_time_end = "";
            this.query.page_size = 50;
            this.query.page_no = 1;
        },
        beginDateSet() {
            let self = this;
            console.log(self);
            return {
                disabledDate(time) {
                    if (self.query.operation_time_end) {
                        return (
                            time.getTime() >
                            new Date(self.query.operation_time_end).getTime()
                        );
                    }
                },
            };
        },
        endDateSet() {
            let self = this;
            console.log(self);
            return {
                disabledDate(time) {
                    if (self.query.operation_time_begin) {
                        return (
                            time.getTime() <
                            new Date(self.query.operation_time_begin).getTime()
                        );
                    }
                },
            };
        },
        getShopDefault(){
            let data={}
            let self=this;
            self.isShopLoading=true;
            this.ajax.postStream('/reports-web/api/reports/dealer/getShop',data,res=>{
                if(res.body.result&&res.body.content&&res.body.content.shop_id){
                    self.isShopManager=true;
                    self.query.shop_id=res.body.content.shop_id;
                    self.query.shop_name=res.body.content.shop_name;
                    self.$message.success(res.body.msg)
                }else{
                    self.isShopManager=false;
                    // self.$message.error('暂无生效的店铺信息')
                }
                self.isShopLoading=false;
            },err=>{
                sele.$message.error(err);
                self.isShopLoading=false;
            })
        }
    },
    mounted() {
        this.setShopDefault();
        this.getShopDefault();
        console.log(this.params);
        if(this.params.data){
            this.query.operation_time_begin = this.params.start_time
            this.query.operation_time_end = Fn.dateFormat(new Date(this.params.end_time),'yyyy-MM-dd 23:59:00')
            this.query.shop_id = this.params.shop_id
            this.query.shop_name = this.params.shop_name;
            if(!!this.params.data.operation_type){
                this.query.operation_type=this.params.data.operation_type;

            }
            this.queryData()
        }
    },
};
</script>
<style type="text/css" scoped>
.el-input {
    width: 180px;
}
.el-form-item__content {
    white-space: nowrap;
}
</style>




