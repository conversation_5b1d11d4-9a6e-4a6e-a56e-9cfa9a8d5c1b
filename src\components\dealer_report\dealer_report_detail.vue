<!-- 经销商发货明细报表 -->
<template>
  <div class="xpt-flex">
    <el-row :gutter="0" class="xpt-top">
      <el-form
        ref="query"
        :rules="rules"
        :model="query"
        label-position="right"
        label-width="120px"
      >
        <el-col :span="6">
          <!--<el-form-item label="开始时间1：" prop='begin_date'>-->
          <el-form-item label="开始时间：" prop="begin_date">
            <!--<el-date-picker v-model="query.begin_date" type="month" placeholder="选择月" size='mini' :editable='false' ></el-date-picker>-->
            <el-date-picker
              v-model="query.begin_date"
              type="date"
              placeholder="选择月"
              size="mini"
              :picker-options="beginDateOptions1"
            ></el-date-picker>
            <el-tooltip
              v-if="rules.begin_date[0].isShow"
              class="item"
              effect="dark"
              :content="rules.begin_date[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
          <!--<el-form-item label="*温馨提示：实时查询支持查所有月份至当前时刻的交易数据，因数据-->
          <!--未冻结，可能与实际交易会有出入冻结数据查询支持查询当前冻结月份的交易数-->
          <!--据，该数据为最终结算数据，查询以冻结数据查询结果为准！"  >-->
          <!--<el-date-picker v-model="query.begin_date" type="month" placeholder="选择月" size='mini' :editable='false' ></el-date-picker>-->
          <!--<el-date-picker v-model="query.begin_date" type="month" placeholder="选择月" size='mini'  ></el-date-picker>-->
          <!--<el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>-->
          <!--<i class='el-icon-warning'></i>-->
          <!--</el-tooltip>-->
          <!--</el-form-item>-->
        </el-col>
        <el-col :span="6">
          <el-form-item label="结束时间：" prop="end_date">
            <!--<el-date-picker v-model="query.end_date" type="month" placeholder="选择月" size='mini' :editable='false' ></el-date-picker>-->
            <el-date-picker
              v-model="query.end_date"
              type="date"
              placeholder="选择月"
              size="mini"
              :picker-options="endDateOptions1"
            ></el-date-picker>
            <el-tooltip
              v-if="rules.end_date[0].isShow"
              class="item"
              effect="dark"
              :content="rules.end_date[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="店铺：" prop="shop_name">
            <xpt-input
              v-model="query.shop_name"
              icon="search"
              :on-icon-click="openShop2"
              @change="shopChange"
              size="mini"
              placement="right-start"
              popper-class="xpt-form__error"
            ></xpt-input>
            <!-- <el-tooltip v-if='rules.shop_name[0].isShow' class="item" effect="dark" :content="rules.shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip> -->
          </el-form-item>
        </el-col>
        <el-col :span="6" class="xpt-align__right">
          <!--<el-button type='info'    size='mini'  @click='queryData'  :loading='queryBtnStatus'>实时查询</el-button>-->
          <el-button type="info" size="mini" @click="queryData(1)"
            >实时查询</el-button
          >
          <!-- <el-button type='success' size='mini'  @click='queryData(0)' >冻结数据查询</el-button><br> -->
          <el-button type="primary" size="mini" @click="reset"
            >重置查询条件</el-button
          >
          <el-button
            type="info"
            size="mini"
            @click="exportExcel"
            :disabled="exportBtnStatus"
            :loading="exportBtnStatus"
            >导出</el-button
          >
          <el-button
            type="info"
            size="mini"
            @click="showExportList('EXCEL_TYPE_Dealer_Detail_DELIVERY')"
            >报表导出文件下载</el-button
          >
        </el-col>
      </el-form>
    </el-row>
    <!--<el-row :gutter='0' class='xpt-top' >-->
    <!--<el-form ref='query' :rules='rules' :model='query' label-position="right" >-->
    <!--<el-form-item label="*温馨提示：实时查询支持查所有月份至当前时刻的交易数据，因数据-->
    <!--未冻结，可能与实际交易会有出入冻结数据查询支持查询当前冻结月份的交易数-->
    <!--据，该数据为最终结算数据，查询以冻结数据查询结果为准！"  >-->
    <!--</el-form-item>-->
    <!--</el-form>-->
    <!--</el-row>-->
    <xpt-list
      :showHead="false"
      :data="list"
      :colData="cols"
      :pageTotal="count"
      selection=""
      @page-size-change="pageSizeChange"
      @current-page-change="currentPageChange"
    ></xpt-list>
  </div>
</template>
<script>
import mixin from "./mixin.js";
import validate from "@common/validate.js";
export default {
  props: ["params"],
  mixins: [mixin],
  data() {
    let self = this;
    return {
      query: {
        // 页码
        page_no: 1,
        // 页数
        page_size: self.pageSize,
        begin_date: "",
        end_date: "",
        if_need_page: "Y",
        //          shop_name: '深圳宝安经销店',
        shop_name: "",
        shop_id: "",
        if_timeliness: "0",
        summaryConditions: "",
      },
      rules: {
        // shop_name:[{ required: true, message: '请选择店铺', trigger: 'blur' }],
        // shop_name:validate.isNotBlank({
        //   trigger: 'change',
        //   self: this,
        //   msg: '请选择店铺'
        // }),
        begin_date: validate.isNotBlank({
          trigger: "change",
          self: this,
          msg: "请填写开始时间",
        }),
        end_date: validate.isNotBlank({
          trigger: "change",
          self: this,
          msg: "请填写结束时间",
        }),
      },
      cols: [
        {
          label: "批次单号",
          prop: "batch_trade_no",
          width:200
        },
        {
          label: "合并单号",
          prop: "merge_trade_no",
          width:200
        },
        {
          label: "批次分单",
          prop: "outbound_notice_no",
          width:200
        },
        {
          label: "淘宝单号",
          prop: "tid",
          width:200
        },
        {
          label: "买家昵称",
          prop: "customer_name",
          width:200
        },
        {
          label: "订单业务类型",
          prop: "order_bussiness_type_name",
          width:110
        },
        {
          label: "送货方式",
          prop: "deliver_method_name",
          width:100
        },
        {
          label: "物料编码",
          prop: "material_number",
        },
        {
          label: "物料名称",
          prop: "material_name",
        },
        {
          label: "规格描述",
          prop: "material_specification",
        },
        {
          label: "实际售价",
          prop: "act_price",
          formatter(val) {
						return (val + '').indexOf('合计') > -1 ? val : Number(val).toFixed(2);
					}
        },
        {
          label: "经销折扣",
          width: 130,
          prop: "dealer_discount",
        },
        {
          label: "原经销价",
          prop: "org_dealer_price",
        },
        {
          label: "经销价",
          prop: "dealer_price",
          formatter(val) {
						return (val + '').indexOf('合计') > -1 ? val : Number(val).toFixed(2);
					}
        },
        {
          label: "发货时间",
          width: 130,
          prop: "out_stock_time",
          format: "dataFormat",
        },
        {
          label: "原始店铺",
          width: 130,
          prop: "original_shop_name",
        },
        {
          label: "订单店铺",
          prop: "shop_name",
        },
        {
          label: "客服店铺",
          prop: "user_shop_name",
        },
        {
          label: "经销商名称",
          prop: "dealer_customer_name",
        },
        {
          label: "客户昵称",
          prop: "customer_name",
        },
        {
          label: "是否换购",
          prop: "if_union",
        },
        {
          label: "是否赠品",
          prop: "if_gift",
        },
      ],
      beginDateOptions1:self.beginDateSet(),
      endDateOptions1: self.endDateSet(),
    //   beginDateDefault:"2019/01/01",
    //   endDateDefault:"2019/12/31",
      //        beginDateOptions1: {
      //          // 每个月的第一天且小于结束日期
      //          disabledDate(time) {
      //            if (self.query.end_date) {
      //              return time.getDate() > 1 || time > self.query.end_date;
      //            } else {
      //              return time.getDate() > 1;
      //            }
      //          }
      //        },
      //        endDateOptions1: {
      //          // 每个月的最后一天且大于开始日期
      //          disabledDate(time) {
      //            let year = time.getFullYear(),
      //              month = time.getMonth() + 1,
      //              lastDay = new Date(year, month, 0).getDate();
      //            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
      //          }
      //        }
    };
  },
  methods: {
    // 选择店铺
    openShop2() {
      let self = this;
      let params = {
        callback(data) {
          self.query.shop_id = data.shop_id;
          self.query.shop_name = data.shop_name;
        },
        setWhere: (queryData) => {
          var newWhere = (queryData.where || []).filter((o) => {
            return (
              o.field !== this.setShopDefault.where[0].field &&
              o.field !== this.setShopDefault.where[1].field
            );
          });

          newWhere = newWhere.concat(this.setShopDefault.where);

          queryData.where = newWhere;
          // queryData.customer_source_id = this.getCustomerSourceId.id
        },
        selection: "radio",
      };
      this.$root.eventHandle.$emit("alert", {
        params: params,
        component: () => import("@components/shop/list"),
        style: "width:800px;height:500px",
        title: "店铺列表",
      });
    },
    viewRechargeDetail(row) {
      var params = row;
      //         console.log("row=========================",row);
      this.$root.eventHandle.$emit("creatTab", {
        name: "预付款充值记录",
        params: params,
        component: () =>
          import("@components/dealer_report/recharge_record.vue"),
      });
    },
    viewDeliveryDetail(row) {
      //        var params = {problem_id:id, status:status};
      var params = row;
      //        console.log("row=========================",row);
      this.$root.eventHandle.$emit("creatTab", {
        name: "发货记录",
        params: params,
        component: () =>
          import("@components/dealer_report/delivery_record.vue"),
      });
    },
    viewAllowanceDetail(row, type) {
      //        var params = {problem_id:id, status:status};
      var params = row;
      params.type = type;
      if (type) {
        this.$root.eventHandle.$emit("creatTab", {
          name: "货款扣款记录",
          params: params,
          component: () =>
            import("@components/dealer_report/allowance_record.vue"),
        });
      } else {
        console.log("row=========================", row);
        this.$root.eventHandle.$emit("creatTab", {
          name: "货款补贴记录",
          params: params,
          component: () =>
            import("@components/dealer_report/allowance_record.vue"),
        });
      }
    },
    queryData(type) {
      this.$refs.query.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.query));
          data.if_timeliness = type;
          
          data.begin_date = new Date(data.begin_date);
          data.end_date = new Date(data.end_date);
          if(data.end_date.getTime()-data.begin_date.getTime()>1000*60*60*24*31){
            
                  this.$message.error('时间间隔必须小于31天');
                  return;
          }
          console.log("data =====");
          console.log(data);
          this.queryBtnStatus = true;

          let listPromise = new Promise((resolve, reject) => {
            //                this.ajax.postStream('/reports-web/api/reports/shopRefund/pageReportShopRefundApp', data, res => {
            this.ajax.postStream(
              "/reports-web/api/reports/dealer/deliveryDetail/getDealerDeliveryDetail",
              data,
              (res) => {
                this.queryBtnStatus = false;
                if (res.body.result && res.body.content) {
                  this.query.if_timeliness = type;
                  let content = res.body.content;
                  console.log("the list ================================111");
                  console.log(content);
                  this.list = content.list || [];
                  this.count = content.count || 0;
                  resolve(res.body.content);
                } else {
                  reject(res.body.msg);
                  this.$message.error(res.body.msg);
                }
              },
              (err) => {
                this.$message.error(err);
                this.queryBtnStatus = false;
              }
            );
          });
          let totalPromise = this.queryTotal(data);
          listPromise
            .then((val) => {
              totalPromise.then((total) => {
                let list = val.list || [];
                if(list.length==0){
                  return
                }
                list.push(total);
                this.list = list;
                this.count = val.count || 0;
              });
            })
            .catch((err) => {
              this.$message.error(err);
            });
        }
      });
    },

    // 查询汇总
    queryTotal(data) {
      return new Promise((resolve) => {
        this.ajax.postStream(
          "/reports-web/api/reports/dealer/deliveryDetail/getDealerDeliverySum",
          data,
          (res) => {
            console.log(res);
            if (res.body.result && res.body.content) {
              let content = res.body.content;
              resolve({
                act_price: "合计:" + Number(content.sum_act_price).toFixed(2),
                dealer_price: "合计:" + Number(content.sum_dealer_price).toFixed(2),
              });
            }
          }
        );
      });
    },
    // 导出功能
    exportExcel() {
      this.$refs.query.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.query));
          data.begin_date = new Date(data.begin_date);
          data.end_date = new Date(data.end_date);
          if(data.end_date.getTime()-data.begin_date.getTime()>1000*60*60*24*31){
            
                  this.$message.error('时间间隔必须小于31天');
                  return;
          }
          this.exportBtnStatus = true;
          this.ajax.postStream(
            "/reports-web/api/reports/dealer/deliveryDetail/exportDealerDeliveryDetail",
            data,
            (res) => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? "success" : "error",
                message: res.body.msg,
              });
            },
            (err) => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            }
          );
        }
      });
    },
    setShopDefault() {
      this.setShopDefault.where = [];
      this.ajax.postStream(
        "/user-web/api/sql/listFields",
        { page: "cloud_shop_v2" },
        (res) => {
          if (res.body.result) {
            (res.body.content.fields || []).forEach((o) => {
              if (o.comment === "店铺分类") {
                this.setShopDefault.where.push({
                  field: o.field,
                  table: o.table,
                  value: "PURCHASE_DEALER",
                  operator: "=",
                  condition: "AND",
                  listWhere: [],
                });
              } else if (o.comment === "店铺状态") {
                this.setShopDefault.where.push({
                  field: o.field,
                  table: o.table,
                  value: "OPEN",
                  operator: "=",
                  condition: "AND",
                  listWhere: [],
                });
              }
            });
          }
        }
      );
    },
    getCustomerSourceId() {
      this.ajax.postStream(
        "/user-web/api/userPerson/getUserPersonBusinessAttributeList",
        {
          page: {
            length: 50,
            pageNo: 1,
          },
          personId: this.getEmployeeInfo("personId"),
          key: "",
          salesmanType: "GLOBAL",
        },
        (res) => {
          if (res.body.result) {
            (res.body.content.list || []).some((o) => {
              if (o.salesmanType === "GLOBAL" && o.attribute === "CUSTOMER") {
                this.getCustomerSourceId.id = o.attributeValue;
                return true;
              }
            });
          }
        }
      );
    },
    beginDateSet(){
        let self=this;
        console.log(self)
         return {
          disabledDate(time) {
              if(self.query.end_date){
                  return time.getTime()>new Date(self.query.end_date).getTime()||time.getTime() < new Date("2019/01/01 00:00:00").getTime()
              }else{
                  return time.getTime() < new Date("2019/01/01 00:00:00").getTime()
                //   return time.getTime() < new Date("2019/01/01 00:00:00").getTime()||time.getTime()>new Date("2019/12/31 00:00:00").getTime();
              }
          }
        }
    },
    endDateSet(){
        let self=this;
        console.log(self)
        return {
          disabledDate(time) {
              if(self.query.begin_date){
                  return time.getTime()<new Date(self.query.begin_date).getTime()
              }else{
                  return time.getTime() < new Date("2019/01/01 00:00:00").getTime()
              }
          }
        }
    }
  },
  mounted() {
    this.setShopDefault();
    this.getCustomerSourceId();
    this.getYearList();
  },
};
</script>
<style type="text/css" scoped>
.el-input {
  width: 150px;
}
</style>
