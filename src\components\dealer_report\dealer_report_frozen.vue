<!-- 报表冻结 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='0' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="店铺：" >
            <xpt-input v-model='query.shop_name' icon='search' :on-icon-click='openShop3' @change='shopChange' size='mini' placement="right-start" popper-class='xpt-form__error' ></xpt-input>
          </el-form-item>

        </el-col>
        <el-col :span='6'>
          <el-form-item label="月份：" prop='end_date' >
            <el-date-picker v-model="query.end_date" type="month" popper-class="dealerFreeze-date-picker" placeholder="选择月份" size='mini' @click.native="bindClickHandler"></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>

        <el-col :span="12" class='xpt-align__right'>
          <el-button type='info' size='mini'  @click='frozenData' >冻结</el-button>
          <el-button type='primary' size='mini'  @click='reset'>重置查询条件</el-button>
        </el-col>
      </el-form>
      <p style="padding: 65px 0 0 48px;"><span style="color:red;">*</span>温馨提示：按月份冻结数据需要确保所选月份之前的数据均已冻结！</p>

    </el-row>

    <xpt-list
      :showHead='false'
      :data='list'
      :pageTotal='count'
      :colData='cols'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  import validate from '@common/validate.js';
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          end_date: '',
          shop_name: '',
          shop_id: '0',
        },
        markNextFreezeMonth: '',
        rules: {
          end_date:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请填写结束时间'
          }),
        },
        cols: [
        ],
      }
    },
    methods: {
      createTimeFormat(timestamp)
      {
          let date = new Date(timestamp );
//          let date = new Date(timestamp)
          let Y = date.getFullYear();
          let M = date.getMonth() + 1
          M = (M < 10 ? '0' + M : M);
        return Y + '-' + M
      },

      setShopDefault (){
        this.ajax.postStream('/user-web/api/sql/listFields', {"page":"cloud_shop_v2"}, res => {
          if(res.body.result){
            (res.body.content.fields || []).some(o => {
              if(o.comment === '店铺分类'){
               this.setShopDefault.where = [{
                "field": o.field,
                "table": o.table,
                "value": "PURCHASE_DEALER",
                "operator": "=",
                "condition": "AND",
                "listWhere": []
              }]
              }
            })
          }
        })
      },

      openShop3() {
        let self = this;
        let params = {
          callback(data) {
            self.query.shop_id = data.shop_id;
            self.query.shop_name = data.shop_name;
            self.get_month_data(self.query.shop_id);

          },
          setWhere: queryData => {
            var newWhere = (queryData.where || []).filter(o => {
              return o.field !== this.setShopDefault.where[0].field
            })

            newWhere.push(this.setShopDefault.where[0])

            queryData.where = newWhere
          },
          selection: 'radio'
        };
        this.$root.eventHandle.$emit('alert', {
          params: params,
          component:() => import('@components/shop/list'),
          style: 'width:800px;height:500px',
          title: '店铺列表'
        })
//        console.log("aaaaaaaaaaaaaaaaaaabbbbbbbbbbbbbbbbbbbbccccccccccccccccccc");
      },
      shopChange(val) {
        if(!val) {
          this.query.shop_id = '';
          this.query.shop_name = '';
          this.markNextFreezeMonth = ''
        }
      },
      get_month_data(shop_id){
        let mounth_data = {};
        mounth_data.shop_id = shop_id;
//        let listPromise = new Promise((resolve, reject) => {
          this.$refs.query.validate((valid) => {
            this.ajax.postStream('/reports-web/api/reports/freeze/getNextFreezeMonth', mounth_data, res => {
              if (res.body.result && res.body.content) {
                let content = res.body.content;
//                console.log(content);
//                data.end_date = res.body.msg;
                console.log("111111111111111111@@@@@@@@@@@@@@@@@@@@@@@@@@333333333333333333333333！！！！！！");
                // this.query.end_date = new Date(this.createTimeFormat(content));
                this.markNextFreezeMonth = this.query.end_date = new Date(Number(content));
//                this.query.end_date = new Date(content);
                console.log(this.query.end_date);
              } else {
                console.log("11111111111111111555@@@@@@@@@@@@@@@@@@@@@@@@@@555333333333333333333333333");
                this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
            })
          })
      },
      bindClickHandler (e){
        if(this.markNextFreezeMonth){
          this.bindClickHandler.year = this.markNextFreezeMonth.getFullYear()
          this.bindClickHandler.month = ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'][this.markNextFreezeMonth.getMonth()]
        }else {
          this.bindClickHandler.year  = ''
          this.bindClickHandler.month  = ''
        }

        if(!this.bindClickHandler.bool){
          this.bindClickHandler.bool = true

          $('.dealerFreeze-date-picker').click(() => {
            $('.dealerFreeze-date-picker .el-month-table a.cell').each((index, $dom) => {
              if($dom.innerHTML === this.bindClickHandler.month && new RegExp(this.bindClickHandler.year).test($('.dealerFreeze-date-picker .el-date-picker__header-label').html())){
                $dom.style.color = 'red'
              }else {
                $dom.style = ''
              }
            })
          })
        }
        $('.dealerFreeze-date-picker').click()
      },
      frozenData() {
        this.$refs.query.validate((valid) => {
          if (valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.end_date = new Date(data.end_date);
            data.shop_id = data.shop_id || '0'
//              let listPromise = new Promise((resolve, reject) => {
                this.ajax.postStream('/reports-web/api/reports/freeze/dealerFreeze', data, res => {
//                  this.queryBtnStatus = false;
                  if (res.body.result) {
                    let content = res.body.content || {};
                    console.log(content);
                    this.list = content.list || [];
                    this.count = content.count || 0;
//                    resolve(res.body.content.body)
                    this.$message.closeAll()
                    this.$message({
                      showClose: true,
                      message: res.body.msg,
                      type: 'success',
                      duration: 60000,
                      customClass: this.$style['message-box'],
                    });
                  } else {
                    this.$message.closeAll()
                    this.$message({
                      showClose: true,
                      message: res.body.msg,
                      type: 'error',
                      duration: 60000,
                      customClass: this.$style['message-box'],
                    });
                  }
                }, err => {
                  this.$message.error(err);
//                  this.queryBtnStatus = false;
                })
//              })
            }
        })
      },

      computed: {
        staff() {
          return this.query.staff_id;
        },
        staff_group() {
          return this.query.staff_group_id;
        },
        big_group() {
          return this.query.big_group_id;
        }
      },
      watch: {
        staff(n) {
          if (n) {
            this.query.staff_group = '';
            this.query.staff_group_id = '';
            this.query.big_group = '';
            this.query.big_group_id = '';
          }
        },
        staff_group(n) {
          if (n) {
            this.query.staff = '';
            this.query.staff_id = '';
            this.query.big_group = '';
            this.query.big_group_id = '';
          }
        },
        big_group(n) {
          if (n) {
            this.query.staff = '';
            this.query.staff_id = '';
            this.query.staff_group = '';
            this.query.staff_group_id = '';
          }
        }
      },
    },
    mounted(){
      this.setShopDefault()
    },
    destroyed (){
      this.$message.closeAll()
    },
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
<style module>
.message-box :global(.el-message__group) p, .message-box1 :global(.el-message__group) p {
    white-space: pre-wrap;
}
.message-box :global(.el-message__img) {
    height: 100%;
    top: 50%;
    transform: translateY(-50%);
    background-color: #ff4949;
}
.message-box :global(.el-message__group) {
    height: auto;
}
</style>
