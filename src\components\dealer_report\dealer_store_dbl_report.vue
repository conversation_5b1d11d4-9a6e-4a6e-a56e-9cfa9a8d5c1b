<!-- 经销商店铺对账报表 -->
<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='info'    size='mini'  @click='queryData()' >刷新</el-button>

      <el-row :gutter='40' class='xpt-top'  slot='left'>
        <el-form ref='query' :rules='rules' label-position="right" label-width="120px">
         
        <el-col :span="8">
            <!-- <el-form-item label="月份">
              <el-date-picker
                size="mini"
                v-model="startDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item> -->
            <el-form-item label="开始时间："  >
              <el-date-picker v-model="query.start_time" format="yyyy-MM-dd HH:mm:ss" type="month" placeholder="选择月" size='mini' :editable="false" ></el-date-picker>
            
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束时间：">
            <el-date-picker v-model="query.end_time" format="yyyy-MM-dd HH:mm:ss" type="month" @change="endDateChange" ref="$endDate" placeholder="选择月" size='mini' :editable="false" ></el-date-picker>
            
            </el-form-item>
          </el-col>
          </el-form>
      </el-row>
			<xpt-search-ex :page="query.page_name" :click="searchClick" :callback='reset' slot='right'/>

		</xpt-headbar>

    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      :btns="btns"
		  @search-click='searchClick' 
		  :searchPage='query.page_name' 
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    >
        <!-- <el-date-picker v-model="query.start_time" format="yyyy-MM-dd HH:mm:ss" type="month" placeholder="选择月份" size='mini' :editable="false" style="float: left;
    margin-left: 30px" slot='btns'></el-date-picker> -->
    </xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  import validate from '@common/validate.js';
  import fn from '@common/Fn.js';
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        startDate:[null,null],
        btns:[],
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          page_name: 'dlb_balance',
          start_time:'',
          end_time:'',
          where: []
        },
        rules: {
          begin_time:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请填写开始时间'
          }),
          end_time:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请填写结束时间'
          }),
        },
        cols: [
          {
            label: '核对月',
            prop: 'month',
          }, {
            label: '店铺',
            prop: 'shop_name',
          },{
            prop: 'mdm_shop_name',
            label: '实体店名称',
          },  {
            label: '经销商',
            prop: 'dealer_name',
          }, {
            label: '开始时间',
            prop: 'start_time',
            format: 'dataFormat1',
            
          }, {
            label: '结束时间	',
            prop: 'end_time',
            format: 'dataFormat1',
          }, {
            prop: 'initial_balance_amount',
            label: '本期期初余额	',
            width: 180,
          },  {
            label: '本期预付货款充值',
            width: 130,
            prop: 'recharge_amount',
          }, {
            label: '本期发货金额	',
            prop:'deliver_amount'
          }, {
            prop: 'subsidy_deduction_income_amount',
            label: '本期补贴/扣款 冲收入',
          }, {
            prop: 'subsidy_deduction_not_income_amount',
            label: '本期补贴/扣款 不冲收入	',
            width: 180,
          }

          , {
            label: '本期余额	',
            prop: 'balance_amount',
          },{
            prop: 'goods_invoicing_amount',
            label: '本期开票金额-商品',
          }, {
            prop: 'service_invoicing_amount',
            label: '本期开票金额-服务	',
          }, {
            prop: 'last_modifier_name',
            label: ' 操作人',
          }, {
            prop: 'last_modify_time',
            label: '操作时间	',
            format: 'dataFormat1',

          }, {
            prop: 'remark',
            label: '备注',
          }, 
          {
            prop: 'bill_status',
            label: '对账单状态',
            formatter(val){
              switch (val){
                case 1 : return '创建' ;break;
                case 4 : return '提交' ;break;
                case 7 : return '审核' ;break;
              }
            }
          }, {
            prop: 'confirm_status',
            label: '门店确认状态',
            formatter(val){
              switch (val){
                case 1 : return '待确认' ;break;
                case 4 : return '已确认' ;break;
                case 7 : return '数据异常' ;break;
              }
            }
          }
        ],
      }
    },
    methods: {
      endDateChange (val){
        var $inputParent = this.$refs.$endDate.$el
        ,   timer = new Date(val)

        timer.setMonth(timer.getMonth() + 1)
        let timer2 = new Date(timer.getTime() -1);
        $inputParent.classList.add(this.$style['end-date-fix'])
        $inputParent.setAttribute('data-date', val ? fn.dateFormat(timer2, 'yyyy-MM-dd hh:mm:ss') : '')
      },
     searchClick(where,res) {
			this.query.where = where
			this.queryData(res)
		},
      queryData(resolve) {
     
            let data = JSON.parse(JSON.stringify(this.query));
            
              this.ajax.postStream('/reports-web/api/reports/dlb/list', data, res => {
                this.queryBtnStatus = false;
                if (res.body.result && res.body.content) {
                  let content = res.body.content;
                  this.list =content.list
                  this.count = content.count || 0;
                  resolve&&resolve();
                } else {
                  this.$message.error(res.body.msg);
                }
              }, err => {
                this.$message.error(err);
                this.queryBtnStatus = false;
              })
            

          
       
      },

    reset(){
          this.query.start_time = '';
          this.query.end_time = '';
    },
    // 导出功能
    exportExcel() {
      
          this.ajax.postStream('/reports-web/api/reports/dlb/exportExcel', {
          }, res => {
            this.exportBtnStatus = false;
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
          }, err => {
            this.$message.error(err);
            this.exportBtnStatus = false;
          })
     
    },
   
  
    },
    mounted(){
    },
  }
</script>
<style module>
.end-date-fix input {
  color: #fff;
}
.end-date-fix:before {
  content: attr(data-date);
  position: absolute;
  left: 10px;
  pointer-events: none;
}
</style>