<!-- 触发平台发货报表 -->
<template>
    <div class='xpt-flex'>
        <el-row :gutter='10' class='xpt-top'>
            <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="130px">
				<el-col :span='5'>
					<el-form-item label="支付时间开始：" prop="pay_time_start">
						<el-date-picker v-model="query.pay_time_start" type="date"  placeholder="选择日期" size='mini' format="yyyy-MM-dd 00:00:00"  :picker-options='beginDateOptions1' style="width: 100%"></el-date-picker>
					</el-form-item>
					<!-- <el-form-item label="拍单时间开始：" prop="created_start">
						<el-date-picker v-model="query.created_start" type="date" placeholder="选择日期" size='mini' :editable='false'  :default-time="['00:00:00']" format="yyyy-MM-dd 00:00:00" :picker-options='orderEnableDateOptions' style="width: 100%"></el-date-picker>
					</el-form-item> -->
					<el-form-item label="合并订单单号：" prop='merge_trade_no'>
						<xpt-input v-model='query.merge_trade_no' size='mini' style="width: 100%"></xpt-input>
					</el-form-item>
					<el-form-item label="买家昵称：" prop='customer_name'>
						<xpt-input v-model='query.customer_name' size='mini' style="width: 100%"></xpt-input>
					</el-form-item>
				</el-col>
				<el-col :span='5'>
					<el-form-item label="支付时间结束：" prop="pay_time_end">
						<el-date-picker v-model="query.pay_time_end" type="date" placeholder="选择日期" size='mini' :editable='false'  :default-time="['00:00:00']" format="yyyy-MM-dd 00:00:00" :picker-options='endDateOptions1' style="width: 100%"></el-date-picker>
					</el-form-item>
					<!-- <el-form-item label="拍单时间结束：" prop="created_end">
						<el-date-picker v-model="query.created_end" type="date" placeholder="选择日期" size='mini' :editable='false'  :default-time="['00:00:00']" format="yyyy-MM-dd 00:00:00" :picker-options='orderDisableDateOptions'  style="width: 100%"></el-date-picker>
					</el-form-item> -->
					<el-form-item label="订单业务模式：" prop='order_business_mode'>
						<xpt-select-aux v-model='query.order_business_mode' aux_name='order_business_mode'  style="width: 100%"></xpt-select-aux>
					</el-form-item>
					<el-form-item label="支付类型：" prop="type">
						<el-select  placeholder="请选择" size='mini' v-model="query.type"  style="width: 100%">
							<el-option label='收入' value='RECEIVE' key='RECEIVE'></el-option>
							<el-option label='赔偿' value='COMPENSATION' key='COMPENSATION'></el-option>
							<el-option label='支出' value='REFUND' key='REFUND'></el-option>
							<el-option label='换货结转' value='CARRYOVERS' key='CARRYOVERS'></el-option>
							<el-option label='退货货款' value='RETURNS' key='RETURNS'></el-option>
							<el-option label='收入结转' value='SRYJZ' key='SRYJZ'></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="5">
                    <el-form-item label="店铺名称" prop="original_shop_name">
						<xpt-input v-model='query.original_shop_name' size='mini' style="width: 100%"></xpt-input>
					</el-form-item>
                    <el-form-item label="淘宝单号：" prop="tid">
						<xpt-input v-model='query.tid' size='mini' style="width: 100%"></xpt-input>
					</el-form-item>
					<el-form-item label="支付渠道：" prop="payment_channel">
						<xpt-select-aux v-model='query.payment_channel' aux_name='payChannel' style="width: 100%"></xpt-select-aux>
					</el-form-item>
				</el-col>
				<el-col :span="5">
					<div class="mgl10">
						<el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
						<el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button>
						<el-button type='info' size='mini' @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出查询结果</el-button>
						<el-button type='info' size='mini' @click='showExportList'>报表导出文件下载</el-button>
					</div>
				</el-col>
            </el-form>
        </el-row>
        <xpt-list
			:data="list.length > 0 ? list.concat({
				pay_amount: '合计' + list.reduce((a, b) => {
					if(b.type === 'RECEIVE' || b.type == 'CARRYOVERS'){
						a += (Number(b.pay_amount) || 0)
					}else if (/^(REFUND|RETURNS)$/.test(b.type)){
						a -= (Number(b.pay_amount) || 0)
					}
					return a
				}, 0).toFixed(2)
			}) : list"
			:showHead='false'
			:colData='cols'
			:pageTotal='count'
			selection=''
			@page-size-change='pageSizeChange'
			@current-page-change='currentPageChange' 
			:taggelClassName='tableRowClassName'
		  >
        </xpt-list>
    </div>
</template>
<script>
  import Fn from '@common/Fn.js'
  import validate from '@common/validate.js';
  export default {
		props: ['params'],
		data() {
			let self = this
			return {
				query: {
                    type: '',
                    merge_trade_no: '',
                    order_business_mode: '',
					payment_channel: '',
					customer_name: '',
                    pay_time_start: '',
					pay_time_end: '',
					// created_start: '',
                    // created_end: '',
                    page_size: self.pageSize,
					page_no: 1,
					tid: '',
					original_shop_name: ''
				},
				// payEnableDateOptions:{},
                // payDisableDateOptions:{},
                beginDateOptions1:self.beginDateSet(),
                endDateOptions1: self.endDateSet(),
                // beginDateDefault:"2019/01/01",
                // endDateDefault:"2019/12/31",
				orderEnableDateOptions:{},
				orderDisableDateOptions:{},
				rules:{
					pay_time_start: [{
						required: false,
						validator: (rule, value, callback) => {
							if(!value && (self.query.pay_time_end != '' || (self.query.merge_trade_no == '' && self.query.tid == '' && self.query.customer_name == ''))){
								self.rules[rule.field][0].isShow = true
								self.rules[rule.field][0].required = true
								callback(new Error(''))
							}else {
								self.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请填写支付时间开始',
					}],
					pay_time_end: [{
						required: false,
						validator: (rule, value, callback) => {
							if(!value && (self.query.pay_time_start != '' || (self.query.merge_trade_no == '' && self.query.tid == '' && self.query.customer_name == ''))){
								self.rules[rule.field][0].isShow = true
								self.rules[rule.field][0].required = true
								callback(new Error(''))
							}else {
								self.rules[rule.field][0].isShow = false
								callback()
							}
						},
						trigger: 'blur',
						isShow: false,
						message: '请填写支付时间结束',
					}]
				},
				// 查询按钮状态
				queryBtnStatus: false,
				// 导出按钮状态
				exportBtnStatus: false,
				queryBtnStatusTimer: '',
				exportBtnStatusTimer: '',
				count:0,
				list:[],
				cols: [
					{
						label: '店铺名称',
						prop: 'original_shop_name',
						width: 150
					}, {
						label: '合并单号',
						prop: 'merge_trade_no',
						width: 180
					}, {
						label: '订单业务类型',
						prop: 'business_type_trade',
						format: 'auxFormat',
						formatParams: 'ddywlx',
                        width: 130
					}, {
						label: '买家昵称',
                        prop: 'customer_name',
                        width: 150
					}, {
						label: '业务员',
                        prop: 'salesman_name',
                        width: 150
					}, {
						label: '支付时间',
						prop: 'pay_time',
						formatter(val){
							return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss')
						},
						width: 130
					}, {
						label: '支付渠道',
						prop: 'payment_channel',
						format: 'auxFormat',
						formatParams: 'payChannel',
						width: 100
					}, {
						label: '支付方式',
						prop: 'pay_type',
						format: 'auxFormat',
						formatParams: 'payType',
						width: 100
					}, {
						label: '支付类型',
						prop: 'type',
						formatter(val){
							switch(val) {
								case 'RECEIVE': return '收入';
								case 'REFUND': return '支出';
								case 'CARRYOVERS': return '换货结转';
								case 'RETURNS': return '退货货款';
								case 'COMPENSATION': return '赔偿';
								case 'SRYJZ': return '收入结转';
								default: return val; break;
							}
						}
					}, {
						label: '收款金额',
						prop: 'pay_amount',
						width: 130,
						formatter: (val, index, row)=> {
							return /合计/.test(val) ? val : ((/^(REFUND|RETURNS|COMPENSATION)$/.test(row.type) ? -1 : 1) * val)
						}
					}, {
						label: '收款账号',
						prop: 'received_account',
					}, {
						label: '来源类型',
						prop: 'source_type',
						format: 'auxFormat',
						formatParams: 'pay_source_type'
					}, {
						label: '支付账号',
						prop: 'pay_account',
						width: 150
					}, {
						label: '开户行',
						prop: 'pay_bank',
					}, {
						label: '用户名',
						prop: 'pay_name',
					}, {
						label: '销售订单',
						prop: 'sys_trade_no',
						width: 180
					}, {
						label: '订单业务模式',
						prop: 'order_business_mode',
						format: 'auxFormat',
						formatParams: 'order_business_mode',
						width: 100,
					}, {
						label: '拍单时间',
						prop: 'created',
						formatter(val){
							return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss')
						},
						width: 130
					}, {
						label: '订单状态',
						prop: 'audit_status',
						width: 120,
						formatter(val){
							switch(val){
								case "N":return "未审核"; break;
								case "Y":return "已审核"; break;
							}
						}
					}, {
						label: '线上淘宝交易状态',
						prop: 'status',
						width: 120,
						formatter(val){
							switch(val) {
								case "WAIT_BUYER_PAY":return "等待买家付款"; break;
								case "WAIT_SELLER_SEND_GOODS":return "等待卖家发货"; break;
								case "WAIT_BUYER_CONFIRM_GOODS":return "等待买家确认收货"; break;
								case "TRADE_BUYER_SIGNED":return "买家已签收"; break;
								case "TRADE_FINISHED":return "交易成功"; break;
								case "TRADE_CLOSED":return "付款以后用户退款成功，交易自动关闭"; break;
								case "TRADE_CLOSED_BY_TAOBAO":return "付款以前，卖家或买家主动关闭交易"; break;
								case "PAY_PENDING":return "国际信用卡支付付款确认中"; break;
								case "PARTIAL_SEND_GOODS":return "部分发货"; break;
							}
						}
					}, {
						label: '淘宝单号',
						prop: 'tid',
						width: 140,
					}, {
						label: '线下收款单号',
						prop: 'receipt_no',
						width: 100,
					}
				]
			}
		},
		methods: {
			// 查看详情
			reset() {
				for(let v in this.query) {
					if (v != 'page_size' || v != 'page_no') {
						this.query[v] = ''
					}
					if (v === 'page_size') {
						this.query[v] = 50
					}
					if (v === 'page_no') {
						this.query[v] = 1
					}
				}
				this.rules.pay_time_start[0].required = false
				this.rules.pay_time_end[0].required = false
				this.rules.pay_time_start[0].isShow = false
				this.rules.pay_time_end[0].isShow = false
			},
			pageSizeChange(ps) {
				this.query.page_size = ps;
				this.queryData();
			},
			currentPageChange(page) {
				this.query.page_no = page;
				this.queryData();
			},
			queryData() {
				this.$refs.query.validate((valid) => {
					if(valid) {
						let data = JSON.parse(JSON.stringify(this.query))
						data['pay_time_start']=='' ?   data['pay_time_start']='' : data['pay_time_start']= +new Date(data.pay_time_start)
						data['pay_time_end']=='' ?   data['pay_time_end']='' : data['pay_time_end']= +new Date(data.pay_time_end)
						// data['created_start']=='' ?   data['created_start']='' : data['created_start']= +new Date(data.created_start)
						// data['created_end']=='' ?   data['created_end']='' : data['created_end']= +new Date(data.created_end)
						// if ((data.created_start && data.created_end === '') || (data.created_start === '' && data.created_end)) {
						// 	this.$message.error('请填写拍单时间开始、拍单时间结束')
						// 	return false
						// }
						if (data.pay_time_start && data.pay_time_end) {
							if(this.timeDifference(data.pay_time_start, data.pay_time_end)){
								this.$message.error('支付时间范围不能超过三个月')
								return false
							}
						}
						// if (data.created_start && data.created_end) {
						// 	if(this.timeDifference(data.created_start, data.created_end)){
						// 		this.$message.error('拍单时间范围不能超过三个月')
						// 		return false
						// 	}
						// }
						this.queryBtnStatus = true
						this.ajax.postStream('/reports-web/api/distributor/payment/findBy', data, res => {
							this.queryBtnStatus = false
							if(res.body.result) {
								if (res.body.content) {
									let content = res.body.content
									if (!content.list && typeof content.list != "undefined" && content.list != 0) {
										this.list = []
									} else {
										this.list = content.list
									}
									this.count = content.count || 0
								} else {
									this.list = []
									this.count = 0
								}
								this.$message.success(res.body.msg)
							} else {
								this.list = []
								this.count = 0
								this.$message.error(res.body.msg)
							}
						}, err => {
							this.$message.error(err)
							this.queryBtnStatus = false
						})
					}
				})
			},
			// 导出功能
			exportExcel(e) {
				this.$refs.query.validate((valid) => {
					if(valid) {
                        let data = JSON.parse(JSON.stringify(this.query))
						data['pay_time_start']=='' ? data['pay_time_start']='' : data['pay_time_start']= +new Date(data.pay_time_start)
						data['pay_time_end']=='' ? data['pay_time_end']='' : data['pay_time_end']= +new Date(data.pay_time_end)
						// data['created_start']=='' ? data['created_start']='' : data['created_start']= +new Date(data.created_start)
						// data['created_end']=='' ? data['created_end']='' : data['created_end']= +new Date(data.created_end)
						// if ((data.created_start && data.created_end === '') || (data.created_start === '' && data.created_end)) {
						// 	this.$message.error('请填写拍单时间开始、拍单时间结束')
						// 	return false
						// }
						if (data.pay_time_start && data.pay_time_end) {
							if(this.timeDifference(data.pay_time_start, data.pay_time_end)){
								this.$message.error('支付时间范围不能超过三个月')
								return false
							}
						}
						// if (data.created_start && data.created_end) {
						// 	if(this.timeDifference(data.created_start, data.created_end)){
						// 		this.$message.error('拍单时间范围不能超过三个月')
						// 		return false
						// 	}
						// }
						this.exportBtnStatus = true
						this.ajax.postStream('/reports-web/api/distributor/payment/exportExcel', data, res => {
							this.exportBtnStatus = false
							this.$message({
								type: res.body.result ? 'success' : 'error',
								message: res.body.msg
							})
						}, err => {
                            this.$message.error(err)
                            this.exportBtnStatus = false
						})
					}
				})
			},
			enable_time() {
				// 当this.query.begin_date值更新时，触发begin_date值的更新
				return this.query.enable_time;
			},
			disable_time() {
				return this.query.disable_time;
			},
			// 导出文件下载  
			showExportList (){
				this.$root.eventHandle.$emit('alert', {
					component: () => import('@components/after_sales_report/export'),
					style:'width:900px;height:600px',
					title: '报表导出列表',
					params: {
						query: {
						type: 'EXCEL_TYPE_DEALER_PAYMENT_DETAIL',  
						},
					},
				})
            },
            tableRowClassName(row){
				if(/^(REFUND|RETURNS|COMPENSATION|SRYJZ)$/.test(row.type)) {
					return 'red'
				}
			},
			timeDifference(beginDate,endDate){
				let beginTime = new Date(beginDate)
				let endTime = new Date(endDate)
				var newYear = beginTime.getFullYear();
				var newMonth = beginTime.getMonth()+3; //先计算其实日期3个月后的日期
				if(newMonth>=11){
					newYear += 1;
					newMonth -= 11;
					beginTime.setFullYear(newYear);
					beginTime.setMonth(newMonth-1);
				}else{
					beginTime.setFullYear(newYear);
					beginTime.setMonth(newMonth);
				}
				if(beginTime.getTime()>=endTime.getTime()){ //不超过3个月
					return false;
				}else{
					return true;
				}
            },
            beginDateSet(){
                let self=this;
                return {
                disabledDate(time) {
                    if(self.query.pay_time_end){
                        return time.getTime()>new Date(self.query.pay_time_end).getTime()||time.getTime() < new Date("2019/01/01 00:00:00").getTime()
                    }else{
                        return time.getTime() < new Date("2019/01/01 00:00:00").getTime()
                    }
                }
                }
            },
            endDateSet(){
                let self=this;
                return {
                disabledDate(time) {
                    if(self.query.pay_time_start){
                        return time.getTime()<new Date(self.query.pay_time_start).getTime()
                    }else{
                        return time.getTime() < new Date("2019/01/01 00:00:00").getTime()
                    }
                }
                }
            }
		},
		watch: {
			// 'query.pay_time_start': function (n, o) {
			// 	this.payDisableDateOptions = {
			// 		disabledDate(time) {
			// 			// 设置结束时间的失效时间为小于开始时间
			// 			return time.getTime() < +new Date(n);
			// 		}
			// 	}
			// },
			// 'query.pay_time_end': function (n, o) {
			// 	this.payEnableDateOptions = {
			// 		disabledDate(time) {
			// 			return time.getTime() > +new Date(n);
			// 		}
			// 	}
			// },
			// 'query.created_start': function (n, o) {
			// 	this.orderDisableDateOptions = {
			// 		disabledDate(time) {
			// 			// 设置结束时间的失效时间为小于开始时间
			// 			return time.getTime() < +new Date(n);
			// 		}
			// 	}
			// },
			// 'query.created_end': function (n, o) {
			// 	this.orderEnableDateOptions = {
			// 		disabledDate(time) {
			// 			return time.getTime() > +new Date(n);
			// 		}
			// 	}
			// },
			query: {
				handler: function (newVal, oldVal) {
					if ((newVal.merge_trade_no == '' && newVal.tid == '' && newVal.customer_name == '') || newVal.pay_time_start != '' || newVal.pay_time_end != '') {
						this.rules.pay_time_start[0].required = true
						this.rules.pay_time_end[0].required = true
					} else {
						this.rules.pay_time_start[0].required = false
						this.rules.pay_time_end[0].required = false
						this.rules.pay_time_start[0].isShow = false
						this.rules.pay_time_end[0].isShow = false
					}
				},
				deep: true
			}
		},
  }
</script>
<style type="text/css" scoped>
  	.el-input{
    	width: 150px;
  	}
  	.el-select{
    	width: 150px;
  	}
</style>
<style lang="stylus">
  	.red{
		color: red !important;
	}
	.red td {
		color: inherit !important;
	}
</style>



