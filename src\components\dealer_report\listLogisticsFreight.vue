<!-- 经销商店铺对账报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='0' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='5'>
            <el-form-item label="开始时间：" prop='begin_date' >
            <el-date-picker v-model="query.begin_date" format="yyyy-MM-dd" type="date" placeholder="选择日期" size='mini' :picker-options='beginDateOptions' :editable="false" ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='5'>
          <el-form-item label="结束时间：" prop='end_date'>
            <el-date-picker v-model="query.end_date" format="yyyy-MM-dd" type="date" :picker-options='endDateOptions' placeholder="选择日期" size='mini' :editable="false" ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='5'>
          <el-form-item label="店铺：" >
            <xpt-input v-model='query.shop_name'  size='mini'  placement="right-start" popper-class='xpt-form__error' ></xpt-input>
            <!-- <xpt-input v-model='query.shop_name' icon='search' :on-icon-click='openShop2' @change='shopChange' size='mini' readonly  placement="right-start" popper-class='xpt-form__error' ></xpt-input> -->
            <!-- <el-tooltip v-if='rules.shop_name[0].isShow' class="item" effect="dark" :content="rules.shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip> -->
          </el-form-item>

        </el-col>
        <el-col :span='5'>
          <el-form-item label="合并单号：" >
            <xpt-input v-model='query.merge_trade_no'  size='mini'  placement="right-start" popper-class='xpt-form__error' ></xpt-input>
            <!-- <xpt-input v-model='query.shop_name' icon='search' :on-icon-click='openShop2' @change='shopChange' size='mini' readonly  placement="right-start" popper-class='xpt-form__error' ></xpt-input> -->
            <!-- <el-tooltip v-if='rules.shop_name[0].isShow' class="item" effect="dark" :content="rules.shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip> -->
          </el-form-item>

        </el-col>
        <el-col :span="4" class='xpt-align__right'>
          <el-button type='info'    size='mini'  @click='queryData(1)' >查询</el-button>
          <el-button type='primary' size='mini'  @click='reset'>重置查询条件</el-button>
          <el-button type='info'    size='mini'  @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_BATCH_WITH_FUNDS_RECORD")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  import validate from '@common/validate.js';
  import fn from '@common/Fn.js';
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        beginDateOptions: {
        date:(function(){
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            let time = year + "-" + month + "-" + day + " " + "00:00:00";
            return new Date(time);
        })(),
        disabledDate(time) {
          if (self.query.end_date) {
            return (
              time.getTime() > new Date(self.query.end_date).getTime()
            );
          }
        },
      },
      endDateOptions: {
        date:(function(){
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            let time = year + "-" + month + "-" + day + " " + "00:00:00";
            return new Date(time);
        })(),
        disabledDate(time) {
          if (self.query.begin_date) {
            return (
              time.getTime() < new Date(self.query.begin_date).getTime()
            );
          }
        },
      },
        query: {
          page_no: 1,
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          merge_trade_no: '',
          shop_name: '',
        },
        rules: {
          shop_name:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请选择店铺'
          }),
          begin_date:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请填写开始时间'
          }),
          end_date:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请填写结束时间'
          }),
        },
        cols: [ {
            label: '合并单号',
            prop: 'merge_trade_no',
          },{
            prop: 'batch_trade_no',
            label: '批次单号',
          },  
          {
            label: '批次单发货时间',
            prop: 'zd_delivery_time',
            format: 'dataFormat1',
            width: 200,
          }, {
            label: '发货状态',
            prop: 'zd_delivery_status',
          },  {
            label: '买家昵称',
            prop: 'customer_name',
          }, {
            label: '批次单总体积',
            prop: 'volume_total',
          }, {
            label: '总货值',
            prop: 'actual_cost_total',
          }, 
          {
            label: '运费',
            prop:'post_fee',
          }, {
            label: '送货方式',
            prop:'deliver_method',
          }, {
            label: '业务员',
            prop:'user_nme'
          }, {
            label: '业务员分组',
            prop:'group_name'
          }, {
            label: '订单业务类型',
            prop:'business_type_trade'
          },{
            prop: 'logistics_point_name',
            label: '提货点',
            // format: 'dataFormat1',
            width: 150,
          },{
            label: '订单业务类型',
            prop:'business_type_trade'
          },{
            label: '三包费用类型',
            prop:'three_guarantees_fee_type'
          },{
            label: '省',
            prop:'province'
          },{
            label: '市',
            prop:'city'
          },{
            label: '区',
            prop:'district'
          },{
            label: '充值单号',
            prop:'order_no'
          },{
            label: '充值单状态',
            prop:'order_status'
          },
        ],
      }
    },
    methods: {
      endDateChange (val){
        var $inputParent = this.$refs.$endDate.$el
        ,   timer = new Date(val)

        timer.setMonth(timer.getMonth() + 1)
        let timer2 = new Date(timer.getTime() -1);
        $inputParent.classList.add(this.$style['end-date-fix'])
        $inputParent.setAttribute('data-date', val ? fn.dateFormat(timer2, 'yyyy-MM-dd hh:mm:ss') : '')
      },
      // 选择店铺
      openShop2() {
        let self = this;
        let params = {
          callback(data) {
            self.query.shop_id = data.shop_id;
            self.query.shop_name = data.shop_name;
          },
          
          selection: 'radio'
        }
        this.$root.eventHandle.$emit('alert', {
          params: params,
          component:() => import('@components/shop/list'),
          style: 'width:800px;height:500px',
          title: '店铺列表'
        })
      },
     

      queryData(type) {
        this.$refs.query.validate((valid) => {
          if (valid) {
            let data = JSON.parse(JSON.stringify(this.query));
           data.end_date = new Date(this.query.end_date).getTime()+1000*60*60*24;
           data.begin_date = new Date(this.query.begin_date).getTime();

            this.queryBtnStatus = true;

            let listPromise = new Promise((resolve, reject) => {
              this.ajax.postStream('/reports-web/api/reports/batchWithFund/listLogisticsFreight', data, res => {
                this.queryBtnStatus = false;
                if (res.body.result && res.body.content) {
                  let content = res.body.content;
                  this.list =content.list;
                  this.count = content.count || 0;
                  resolve(res.body.content.body)
                } else {
                  reject(res.body.msg)
                this.$message.error(res.body.msg);
                }
              }, err => {
                this.$message.error(err);
                this.queryBtnStatus = false;
              })
            })

          }
        })
      },
     

    // 导出功能
    exportExcel() {
      this.$refs.query.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.query));
          data.begin_date = +new Date(data.begin_date);
          data.end_date = +new Date(data.end_date)+1000*60*60*24;
          this.exportBtnStatus = true;
          this.ajax.postStream('/reports-web/api/reports/batchWithFund/exportLogisticsFreight',data, res => {
            this.exportBtnStatus = false;
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
          }, err => {
            this.$message.error(err);
            this.exportBtnStatus = false;
          })
        }
      })
    },
   
    
    },
    mounted(){
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
<style module>
.end-date-fix input {
  color: #fff;
}
.end-date-fix:before {
  content: attr(data-date);
  position: absolute;
  left: 10px;
  pointer-events: none;
}
</style>
