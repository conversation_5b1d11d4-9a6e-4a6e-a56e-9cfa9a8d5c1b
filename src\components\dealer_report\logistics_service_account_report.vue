<!-- 店铺新退款金额汇总报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <el-form-item label="开始时间：" prop='begin_date'>
            <!--<el-date-picker v-model="query.begin_date" type="month" placeholder="选择月" size='mini' :editable='false' ></el-date-picker>-->
            <el-date-picker v-model="query.begin_date" type="month" placeholder="选择月" size='mini'  ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="结束时间：" prop='end_date'>
            <!--<el-date-picker v-model="query.end_date" type="month" placeholder="选择月" size='mini' :editable='false' ></el-date-picker>-->
            <el-date-picker v-model="query.end_date" type="month" placeholder="选择月" size='mini'  ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="店铺：">
            <xpt-input v-model='query.shop_name' icon='search' :on-icon-click='openShop2' @change='shopChange' size='mini' ></xpt-input>
          </el-form-item>

        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <!--<el-button type='info'    size='mini'  @click='queryData'  :loading='queryBtnStatus'>实时查询</el-button>-->
          <el-button type='info'    size='mini'  @click='queryData(1)' >实时查询</el-button>
          <el-button type='success' size='mini'  @click='queryData(0)' >冻结数据查询</el-button><br>
          <el-button type='primary' size='mini'  @click='reset'>重置查询条件</el-button>
          <el-button type='info'    size='mini'  @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <!--<el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_REPORT_AFTER_SALE_SHOP_REFUNDAPP")'>报表导出文件下载</el-button>-->
        </el-col>
      </el-form>
    </el-row>
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '1543593600000',
          end_date: '1543593600000',
          if_need_page: 'N',
          shop_name: '深圳宝安经销店',
          shop_id: '9630919000980037',
          if_timeliness: '0',
          summaryConditions: ""
//          page_size: self.pageSize,
//          begin_date: '',
//          end_date: '',
//          if_need_page: 'N',
//          shop_name: '',
//          shop_id: '',
//          if_timeliness: '0',
//          summaryConditions: ""
        },
        cols: [
          {
            label: '店铺',
            prop: 'shop_name',
          }, {
            label: '经销商',
            prop: 'dealer_name',
          }, {
            label: '期初余额',
            prop: 'initial_balance_amount',
          }, {
            label: '本期预付款充值',
            prop: 'recharge_amount',
            redirectClick(row) {
              self.viewRechargeDetail(row)
            }
          }, {
            label: '本期发货金额',
            prop: 'deliver_amount',
            redirectClick(row) {
              self.viewDeliveryDetail(row)
            }
          }, {
            label: '本期预付货款补贴',
            prop: 'advance_subsidy_amount',
            redirectClick(row) {
              self.viewAllowanceDetail(row,0)
            }
          }, {
            label: '本期预付货款扣款',
            width: 130,
            prop: 'advance_deduction_amount',
            redirectClick(row) {
              self.viewAllowanceDetail(row,1)
            }
          }
        ],
        beginDateOptions1: {
          // 每个月的第一天且小于结束日期
          disabledDate(time) {
            if (self.query.end_date) {
              return time.getDate() > 1 || time > self.query.end_date;
            } else {
              return time.getDate() > 1;
            }
          }
        },
        endDateOptions1: {
          // 每个月的最后一天且大于开始日期
          disabledDate(time) {
            let year = time.getFullYear(),
              month = time.getMonth() + 1,
              lastDay = new Date(year, month, 0).getDate();
            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
          }
        }
      }
    },
    methods: {
      viewRechargeDetail(row){
         var params = row;
//         console.log("row=========================",row);
         this.$root.eventHandle.$emit('creatTab',{name:"预付款充值记录",
          params:params,
          component:() => import('@components/dealer_report/recharge_record.vue')
        })
      },
      viewDeliveryDetail(row){
//        var params = {problem_id:id, status:status};
        var params = row;
//        console.log("row=========================",row);
        this.$root.eventHandle.$emit('creatTab',{name:"发货记录",
          params:params,
          component:() => import('@components/dealer_report/delivery_record.vue')
        })
      },
      viewAllowanceDetail(row,type){
//        var params = {problem_id:id, status:status};
        var params = row;
        params.type = type;
        if(type)
        {
          this.$root.eventHandle.$emit('creatTab',{name:"货款扣款记录",
            params:params,
            component:() => import('@components/dealer_report/allowance_record.vue')
          })
        }
        else{
          console.log("row=========================",row);
          this.$root.eventHandle.$emit('creatTab',{name:"货款补贴记录",
            params:params,
            component:() => import('@components/dealer_report/allowance_record.vue')
          })
        }
      },
      queryData(type) {
        this.$refs.query.validate((valid) => {
          if (valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            data.if_timeliness = type;
            data.begin_date = new Date(data.begin_date);
            data.end_date = new Date(data.end_date);
            console.log("data =====");
            console.log(data);
            this.queryBtnStatus = true;

            let listPromise = new Promise((resolve, reject) => {
//                this.ajax.postStream('/reports-web/api/reports/shopRefund/pageReportShopRefundApp', data, res => {
              this.ajax.postStream('/reports-web/api/reports/dealer/transactionSummary', data, res => {
                this.queryBtnStatus = false;
                if (res.body.result && res.body.content) {
                  let content = res.body.content;
                  console.log("the list ================================111");
                  console.log(content);
                  this.list = content.list || [];
                  this.count = content.count || 0;
                  resolve(res.body.content.body)
                } else {
                  reject(res.body.msg)
                }
              }, err => {
                this.$message.error(err);
                this.queryBtnStatus = false;
              })
            })

          }
        })
      },

    // 导出功能
    exportExcel() {
      this.$refs.query.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.query));
          data.begin_date = +new Date(data.begin_date);
          data.end_date = +new Date(data.end_date) + 1000 * 60 * 60 * 24 - 1000;//比如选中2018-05-08，则正确结束日期是：2018-05-08 23:59:59
          this.exportBtnStatus = true;
          this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportShopRefundApp', data, res => {
            this.exportBtnStatus = false;
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
          }, err => {
            this.$message.error(err);
            this.exportBtnStatus = false;
          })
        }
      })
    },
    // 查询汇总
//      queryTotal(data) {
//        return new Promise(resolve => {
//          this.ajax.postStream('/reports-web/api/reports/shopRefund/pageReportShopRefundApp', data, res => {
//            this.queryBtnStatus = false;
//            if(res.body.result && res.body.content) {
//              let content = res.body.content.body.list;
//
//
//
//              let sum = {
//                shop_name: '小计:',
////                deliver_amount:content[0].sum_deliver_amount,
//                cancel_amount:content[0].sum_cancel_amount,
//                overpay_amount:content[0].sum_overpay_amount,
//                preferential_amount:content[0].sum_preferential_amount,
//                delay_amount:content[0].sum_delay_amount,
//                compensate_amount:content[0].sum_compensate_amount,
//                repair_amount:content[0].sum_repair_amount,
//                returns_amount:content[0].sum_returns_amount,
//                carriage_amount:content[0].sum_carriage_amount,
//                three_amount:content[0].sum_three_amount,
//                o2o_diff_amount:content[0].sum_o2o_diff_amount,
//                price_diff_amount:content[0].sum_price_diff_amount,
//                discount_amount:content[0].sum_discount_amount,
////                shop_amount:content[0].sum_shop_amount,
////                refund_rate:content[0].sum_refund_rate,
////                deliver_rate:content[0].sum_deliver_rate
//
//              };
//              let tk = {
//                shop_name: '占总退款比:',
////                deliver_amount:content[0].tk_proportion_deliver_amount,
//                cancel_amount:content[0].tk_proportion_cancel_amount,
//                overpay_amount:content[0].tk_proportion_overpay_amount,
//                preferential_amount:content[0].tk_proportion_preferential_amount,
//                delay_amount:content[0].tk_proportion_delay_amount,
//                compensate_amount:content[0].tk_proportion_compensate_amount,
//                repair_amount:content[0].tk_proportion_repair_amount,
//                returns_amount:content[0].tk_proportion_returns_amount,
//                carriage_amount:content[0].tk_proportion_carriage_amount,
//                three_amount:content[0].tk_proportion_three_amount,
//                o2o_diff_amount:content[0].tk_proportion_o2o_diff_amount,
//                price_diff_amount:content[0].tk_proportion_price_diff_amount,
//                discount_amount:content[0].tk_proportion_discount_amount,
////                shop_amount:content[0].tk_proportion_shop_amount,
////                refund_rate:content[0].tk_proportion_refund_rate,
////                deliver_rate:content[0].tk_proportion_deliver_rate
//              };
//              let fh = {
//                shop_name: '占发货业绩比:',
////                deliver_amount:content[0].fh_proportion_deliver_amount,
//                cancel_amount:content[0].fh_proportion_cancel_amount,
//                overpay_amount:content[0].fh_proportion_overpay_amount,
//                preferential_amount:content[0].fh_proportion_preferential_amount,
//                delay_amount:content[0].fh_proportion_delay_amount,
//                compensate_amount:content[0].fh_proportion_compensate_amount,
//                repair_amount:content[0].fh_proportion_repair_amount,
//                returns_amount:content[0].fh_proportion_returns_amount,
//                carriage_amount:content[0].fh_proportion_carriage_amount,
//                three_amount:content[0].fh_proportion_three_amount,
//                o2o_diff_amount:content[0].fh_proportion_o2o_diff_amount,
//                price_diff_amount:content[0].fh_proportion_price_diff_amount,
//                discount_amount:content[0].fh_proportion_discount_amount,
////                shop_amount:content[0].fh_proportion_shop_amount,
////                refund_rate:content[0].fh_proportion_refund_rate,
////                deliver_rate:content[0].fh_proportion_deliver_rate
//              };
//              resolve([sum,tk,fh])
//
//            }
//          }, err => {
//            this.$message.error(err);
//            this.queryBtnStatus = false;
//          })
//        })
//
//
//      }
    },
    computed: {
      staff() {
        return this.query.staff_id;
      },
      staff_group() {
        return this.query.staff_group_id;
      },
      big_group() {
        return this.query.big_group_id;
      }
    },
    watch: {
      staff(n) {
        if(n) {
          this.query.staff_group = '';
          this.query.staff_group_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      staff_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.big_group = '';
          this.query.big_group_id = '';
        }
      },
      big_group(n) {
        if(n) {
          this.query.staff = '';
          this.query.staff_id = '';
          this.query.staff_group = '';
          this.query.staff_group_id = '';
        }
      }
    },
    mounted(){
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
