// 报表混合文件
export default {
	data() {
		let self = this;
		return {
			rules: {
		        year:[{
		          required:true,
		          message:'请选择年份',
		          isShow:false,
		          validator: function(rule,value,callback){
		            // 数据校验
		            if(value){
		              self.rules[rule.field][0].isShow = false;
		              // 校验成功
		              callback();
		            }else{
		              self.rules[rule.field][0].isShow = true
		              // 校验失败
		              callback(new Error(''));
		            }
		          }
		        }],
		        month:[{
		          required:true,
		          message:'请选择月份',
		          isShow:false,
		          validator: function(rule,value,callback){
		            // 数据校验
		            if(value){
		              self.rules[rule.field][0].isShow = false;
		              // 校验成功
		              callback();
		            }else{
		              self.rules[rule.field][0].isShow = true
		              // 校验失败
		              callback(new Error(''));
		            }
		          }
		        }],
				begin_date: [{
					required:true,
					message:'请选择开始日期',
					isShow:false,
					validator: function(rule,value,callback){
						// 数据校验
						if(value){
							self.rules[rule.field][0].isShow = false;
							// 校验成功
							callback();
						}else{
							self.rules[rule.field][0].isShow = true
							// 校验失败
							callback(new Error(''));
						}
					}
				}],
		        begin_date2: [{
		          required:true,
		          message:'请选择开始日期',
		          isShow:false,
		          validator: function(rule,value,callback){
		            // 数据校验
		            if(value){
		              self.rules[rule.field][0].isShow = false;
		              // 校验成功
		              callback();
		            }else{
		              self.rules[rule.field][0].isShow = true
		              // 校验失败
		              callback(new Error(''));
		            }
		          }
		        }],
				end_date: [{
					required:true,
					message:'请选择结束日期',
					isShow:false,
					validator: function(rule,value,callback){
						// 数据校验
						if(value){
							self.rules[rule.field][0].isShow = false;
							// 校验成功
							callback();
						}else{
							self.rules[rule.field][0].isShow = true
							// 校验失败
							callback(new Error(''));
						}
					}
				}],
		        end_date2: [{
		          required:true,
		          message:'请选择结束日期',
		          isShow:false,
		          validator: function(rule,value,callback){
		            // 数据校验
		            if(value){
		              self.rules[rule.field][0].isShow = false;
		              // 校验成功
		              callback();
		            }else{
		              self.rules[rule.field][0].isShow = true
		              // 校验失败
		              callback(new Error(''));
		            }
		          }
		        }],
				select_date: [{
					required: true,
					message: '请选择日期',
					isShow: false,
					validator(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false;
							callback();
						} else {
							self.rules[rule.field][0].isShow = true;
							callback(new Error(''));
						}
					}
				}]
			},
			// 结束时间不能小于开始时间
			endDateOptions: {},
			// 开始时间不能大于结束时间
			beginDateOptions: {},
			// 数据集
			list: [],
      //数据统计
      listCount:[],
			// 总数
			count: 0,
			// 查询按钮状态
			queryBtnStatus: false,
			// 导出按钮状态
			exportBtnStatus: false,
			queryBtnStatusTimer: '',
			exportBtnStatusTimer: '',
			yearList:{2013:"2013",2014:"2014",2015:"2015",2016:"2016",2017:"2017",2018:"2018",2019:"2019",2020:"2020",2021:"2021"},
			monthList:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12"},
			summaryList:{'':'请选择','year':"年",'month':"月"},
			channelList:{'':'请选择',ALIPAY:'支付宝',WECHAT:'微信',BANK_TRANSFER:'银行转账',POS:'POS',CASH:'现金'},
			departmentList:{'':'请选择','PRESALE':'售前','AFTER':'售后','AFTERABC':'售前ABC'}
		}
	},
	methods: {
		reset() {
			for(let v in this.query) {
				if(!(v === 'page_size' || v === 'page_no'|| v === 'if_need_page')) {
					this.query[v] = '';
				}
			}
		},
		pageSizeChange(ps) {
			this.query.page_size = ps;
			this.queryData();
		},
		currentPageChange(page) {
			this.query.page_no = page;
			this.queryData();
		},
		// 选择店铺
		openShop() {
			let self = this;
			let params = {
				callback(data) {
					self.query.shop_id = data.shop_id;
					self.query.shop_name = data.shop_name;
				}
			}


			this.$root.eventHandle.$emit('alert', {
				params: params,
				component:() => import('@components/shop/list'),
				style: 'width:800px;height:500px',
				title: '店铺列表'
			})
		},
    // 选择店铺
    openShop2() {
      let self = this;
      let params = {
        callback(data) {
          self.query.shop_id = data.shop_id;
          self.query.shop_name = data.shop_name;
        },
        selection: 'radio'
      }
      this.$root.eventHandle.$emit('alert', {
        params: params,
        component:() => import('@components/shop/list'),
        style: 'width:800px;height:500px',
        title: '店铺列表'
      })
    },

		shopChange(val) {
			if(!val) {
				this.query.shop_id = '';
				this.query.shop_name = '';
			}
		},
		// 选择分组
		openGroup() {
			let self = this
			let params = {
				callback(data) {
					self.query.staff_group_id = data.id;
					self.query.staff_group = data.name;
				},
				level: 'SUB_TEAM'
			}
			this.$root.eventHandle.$emit('alert', {
				params: params,
				component:() => import('@components/per_sales_report/select_group'),
				style: 'width:800px;height:500px',
				title: '分组列表'
			})
		},
		// 分组数据变动
		groupChange(val) {
			if(!val) {
				this.query.staff_group_id = '';
				this.query.staff_group = '';
			}
		},
		// 选择大分组
		openBigGroup() {
			let self = this
			let params = {
				callback(data) {
					self.query.big_group_id = data.id;
					self.query.big_group = data.name;
				},
				level: 'TEAM'
			}
			this.$root.eventHandle.$emit('alert', {
				params: params,
				component:() => import('@components/per_sales_report/select_group'),
				style: 'width:800px;height:500px',
				title: '分组列表'
			})
		},
		// 大分组数据变动
		bigGroupChange(val) {
			if(!val) {
				this.query.big_group_id = '';
				this.query.big_group = '';
			}
		},

		// 选择业务员，arg为true 时选择人员类型的负责人
		openSatff(staffType, arg) {
			let self = this
			let params = {
				callback(data) {

					self.query.staff_id = data.id;
          // self.query.staff = data.nickName;
					self.query.staff = data.fullName;
				},
				/*salesmanType: typeof(staffType) === 'string' ? staffType : '',
				isSelectManager: arg*/
				salesmanTypeList:this.salesmanTypeList || []
			}
			this.$root.eventHandle.$emit('alert', {
				params: params,
				component:() => import('@components/after_sales_report/select_personel'),
				style: 'width:800px;height:500px',
				title: '人员列表'
			})
		},
		staffChange(val) {
			if(!val) {
				this.query.staff_id = '';
				this.query.staff = '';
			}
		},
    getYearList(){
		   var yearstart=2013;
		  var yearend=new Date().getFullYear()+5;
		  for(var i=yearstart;i<=yearend;i++){
		    this.yearList[i]=i;
      }
    },
    //得到开始时间
    getStartDate(year,month){
      var datastr=year+"-"+month+"-01 00:00:00";
      return new Date(datastr).getTime();
    },
    //得到结束时间
    getEndDate(year,month){
      if(month==12){
        month=1;
        year=Number(year)+1;
      }else{
        month=Number(month)+1;
      }
      var datastr=year+"-"+month+"-01 00:00:00";
      return new Date(datastr).getTime();
    },
    // 买家昵称
    selectBuyersName (){
      this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/customers/list'),
        style:'width:900px;height:600px',
        title:'买家昵称列表',
        params: {
          close: d => {
            this.query.buyerNick = d.name;
            this.query.buyerNick_str = d.cust_id;
          }
        },
    })
		},
		// 导出报表
		showExportList (exportType){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: exportType,
					},
				},
			})
		},

	},
	watch: {
		begin_date(n, o) {
			this.endDateOptions = {
				disabledDate(time) {
					// 设置结束时间的失效时间为小于开始时间
					return time.getTime() < +new Date(n);
				}
			}
		},
		end_date(n, o) {
			this.beginDateOptions = {
				disabledDate(time) {
					return time.getTime() > +new Date(n);
				}
			}
		},
		// 如果值为true,30秒后自动设值为false
		queryBtnStatus(n, o) {
			if(n) {
				this.queryBtnStatusTimer = setTimeout(() => {
					this.$message.error('查询超时');
					this.queryBtnStatus = false;
				}, 30000)
			} else {
				clearTimeout(this.queryBtnStatusTimer);
			}
		},
		exportBtnStatus(n, o) {
			if(n) {
				this.exportBtnStatusTimer =	setTimeout(() => {
					this.$message.error('导出超时');
					this.queryBtnStatus = false;
				}, 30000)
			} else {
				clearTimeout(this.exportBtnStatusTimer);
			}
		},
		
	},
	computed: {
		begin_date() {
			// 当this.query.begin_date值更新时，触发begin_date值的更新
			return this.query.begin_date;
		},
		end_date() {
			return this.query.end_date;
		}
	}
}
