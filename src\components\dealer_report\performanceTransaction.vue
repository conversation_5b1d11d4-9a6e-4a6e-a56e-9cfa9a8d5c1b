<!-- 经销商业绩报表 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='0' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" label-width="120px">
        <el-col :span='6'>
          <!--<el-form-item label="开始时间1：" prop='begin_date'>-->
            <el-form-item label="开始时间：" prop='begin_date' >
            <!--<el-date-picker v-model="query.begin_date" type="month" placeholder="选择月" size='mini' :editable='false' ></el-date-picker>-->
            <el-date-picker v-model="query.begin_date" format="yyyy-MM-dd HH:mm:ss" type="month" placeholder="选择月" size='mini' :editable="false" ></el-date-picker>
            <el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          <!--<el-form-item label="*温馨提示：实时查询支持查所有月份至当前时刻的交易数据，因数据-->
                          <!--未冻结，可能与实际交易会有出入冻结数据查询支持查询当前冻结月份的交易数-->
                          <!--据，该数据为最终结算数据，查询以冻结数据查询结果为准！"  >-->
            <!--<el-date-picker v-model="query.begin_date" type="month" placeholder="选择月" size='mini' :editable='false' ></el-date-picker>-->
            <!--<el-date-picker v-model="query.begin_date" type="month" placeholder="选择月" size='mini'  ></el-date-picker>-->
            <!--<el-tooltip v-if='rules.begin_date[0].isShow' class="item" effect="dark" :content="rules.begin_date[0].message" placement="right-start" popper-class='xpt-form__error'>-->
            <!--<i class='el-icon-warning'></i>-->
            <!--</el-tooltip>-->
          <!--</el-form-item>-->
        </el-col>
        <el-col :span='6'>
          <el-form-item label="结束时间：" prop='end_date'>
            <!--<el-date-picker v-model="query.end_date" type="month" placeholder="选择月" size='mini' :editable='false' ></el-date-picker>-->
            <el-date-picker v-model="query.end_date" format="yyyy-MM-dd HH:mm:ss" type="month" @change="endDateChange" ref="$endDate" placeholder="选择月" size='mini' :editable="false" ></el-date-picker>
            <el-tooltip v-if='rules.end_date[0].isShow' class="item" effect="dark" :content="rules.end_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='6'>
          <el-form-item label="店铺："  prop='shop_name'>
            <xpt-input v-model='query.shop_name' icon='search' :on-icon-click='openShop2' @change='shopChange' size='mini'   placement="right-start" popper-class='xpt-form__error' ></xpt-input>
            <el-tooltip v-if='rules.shop_name[0].isShow' class="item" effect="dark" :content="rules.shop_name[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>

        </el-col>
        <el-col :span="6" class='xpt-align__right'>
          <!--<el-button type='info'    size='mini'  @click='queryData'  :loading='queryBtnStatus'>实时查询</el-button>-->
          <el-button type='info'    size='mini'  @click='queryData(1)' >实时查询</el-button>
          <el-button type='success' size='mini'  @click='queryData(0)' >冻结数据查询</el-button><br>
          <el-button type='primary' size='mini'  @click='reset'>重置查询条件</el-button>
          <el-button type='info'    size='mini'  @click='exportExcel' :disabled='exportBtnStatus' :loading='exportBtnStatus'>导出</el-button>
          <el-button type='info' size='mini' @click='showExportList("EXCEL_TYPE_DEALER_PERFORMANCE")'>报表导出文件下载</el-button>
        </el-col>
      </el-form>
      <p style="padding: 65px 0 0 48px;"><span style="color:red;">*</span>温馨提示：实时查询支持查所有月份至当前时刻的交易数据，因数据未冻结，可能与实际交易会有出入<br><span style="padding-left: 65px;"></span>冻结数据查询支持查询当前冻结月份的交易数据，该数据为最终结算数据，查询以冻结数据查询结果为准！</p>
    </el-row>
    <!--<el-row :gutter='0' class='xpt-top' >-->
      <!--<el-form ref='query' :rules='rules' :model='query' label-position="right" >-->
      <!--<el-form-item label="*温馨提示：实时查询支持查所有月份至当前时刻的交易数据，因数据-->
                          <!--未冻结，可能与实际交易会有出入冻结数据查询支持查询当前冻结月份的交易数-->
                          <!--据，该数据为最终结算数据，查询以冻结数据查询结果为准！"  >-->
        <!--</el-form-item>-->
      <!--</el-form>-->
    <!--</el-row>-->
    <xpt-list
      :showHead='false'
      :data='list'
      :colData='cols'
      :pageTotal='count'
      selection=''
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  import mixin from './mixin.js'
  import validate from '@common/validate.js';
  import fn from '@common/Fn.js';
  export default {
    props: ['params'],
    mixins: [mixin],
    data() {
      let self = this
      return {
        query: {
          // 页码
          page_no: 1,
          // 页数
          page_size: self.pageSize,
          begin_date: '',
          end_date: '',
          if_need_page: 'N',
//          shop_name: '深圳宝安经销店',
          shop_name: '',
          shop_id: '',
          if_timeliness: '0',
          summaryConditions: ""
//          page_size: self.pageSize,
//          begin_date: '',
//          end_date: '',
//          if_need_page: 'N',
//          shop_name: '',
//          shop_id: '',
//          if_timeliness: '0',
//          summaryConditions: ""
        },
        rules: {
          // shop_name:[{ required: true, message: '请选择店铺', trigger: 'blur' }],
          shop_name:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请选择店铺'
          }),
          begin_date:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请填写开始时间'
          }),
          end_date:validate.isNotBlank({
            trigger: 'change',
            self: this,
            msg: '请填写结束时间'
          }),
        },
        cols: [
          {
            label: '店铺',
            prop: 'shop_name',
          }, {
            label: '经销商',
            prop: 'dealer_name',
          }, {
            label: '本期销售',
            prop: 'saleAmount',
          },{
            label: '本期发货',
            prop: 'purchaseAmount',
          }, {
            label: '本期超区三包费收款',
            prop: 'threeFee',
          }, {
            label: '本期货款补贴',
            prop: 'allowance',
          }, {
            label: '本期物流服务赔偿',
            prop: 'compensation',
          }, {
            prop: 'afterSaleServiceAmount',
            label: '本期售后服务费用',  
          },{
            label: '本期发货费用',
            width: 130,
            prop: 'deliverFee',
          }, {
            prop: 'afterSaleAmount',
            label: '本期售后费用',
          },  {
            prop: 'placingCost',
            label: '本期摆场成本',  
          }, {
            prop: 'cutPayment',
            label: '本期货款扣款',  
          }, {
            prop: 'profit',
            label: '营业利润',  
          }
        ],
//        beginDateOptions1: {
//          // 每个月的第一天且小于结束日期
//          disabledDate(time) {
//            if (self.query.end_date) {
//              return time.getDate() > 1 || time > self.query.end_date;
//            } else {
//              return time.getDate() > 1;
//            }
//          }
//        },
//        endDateOptions1: {
//          // 每个月的最后一天且大于开始日期
//          disabledDate(time) {
//            let year = time.getFullYear(),
//              month = time.getMonth() + 1,
//              lastDay = new Date(year, month, 0).getDate();
//            return time.getDate() < lastDay || time < (self.query.begin_date || 0);
//          }
//        }
      }
    },
    methods: {
      endDateChange (val){
        // var $inputParent = this.$refs.$endDate.$el
        // ,   timer = new Date(val)

        // timer.setMonth(timer.getMonth() + 1)
        // $inputParent.classList.add(this.$style['end-date-fix'])
        // $inputParent.setAttribute('data-date', val ? fn.dateFormat(timer, 'yyyy-MM-dd hh:mm:ss') : '')
      },
      // 选择店铺
      openShop2() {
        let self = this;
        let params = {
          callback(data) {
            self.query.shop_id = data.shop_id;
            self.query.shop_name = data.shop_name;
          },
          setWhere: queryData => {
            var newWhere = (queryData.where || []).filter(o => {
              return o.field !== this.setShopDefault.where[0].field && o.field !== this.setShopDefault.where[1].field
            })

            newWhere = newWhere.concat(this.setShopDefault.where)

            queryData.where = newWhere
            queryData.customer_source_id = this.getCustomerSourceId.id
          },
          selection: 'radio'
        }
        this.$root.eventHandle.$emit('alert', {
          params: params,
          component:() => import('@components/shop/list'),
          style: 'width:800px;height:500px',
          title: '店铺列表'
        })
      },
      viewRechargeDetail(row){
         var params = row;
//         console.log("row=========================",row);
         this.$root.eventHandle.$emit('creatTab',{name:"预付款充值记录",
          params:params,
          component:() => import('@components/dealer_report/recharge_record.vue')
        })
      },
      viewDeliveryDetail(row){
//        var params = {problem_id:id, status:status};
        var params = row;
//        console.log("row=========================",row);
        this.$root.eventHandle.$emit('creatTab',{name:"发货记录",
          params:params,
          component:() => import('@components/dealer_report/delivery_record.vue')
        })
      },
      viewAllowanceDetail(row,type){
//        var params = {problem_id:id, status:status};
        var params = row;
        params.type = type;
        if(type)
        {
          this.$root.eventHandle.$emit('creatTab',{name:"货款扣款记录",
            params:params,
            component:() => import('@components/dealer_report/allowance_record.vue')
          })
        }
        else{
          console.log("row=========================",row);
          this.$root.eventHandle.$emit('creatTab',{name:"货款补贴记录",
            params:params,
            component:() => import('@components/dealer_report/allowance_record.vue')
          })
        }
      },
      queryData(type) {
        this.$refs.query.validate((valid) => {
          if (valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            if(!type){
              data.if_timeliness = this.if_timeliness
            }else{
              data.if_timeliness = type;
              this.if_timeliness = type;
            }
            // data.if_timeliness = type;
            data.begin_date = new Date(data.begin_date);
            data.end_date = new Date(data.end_date);
            console.log("data =====");
            console.log(data);
            this.queryBtnStatus = true;

            let listPromise = new Promise((resolve, reject) => {
//                this.ajax.postStream('/reports-web/api/reports/shopRefund/pageReportShopRefundApp', data, res => {
              this.ajax.postStream('/reports-web/api/reports/dealer/performance/transactionSummary', data, res => {
                this.queryBtnStatus = false;
                if (res.body.result && res.body.content) {
                  this.query.if_timeliness = type
                  let content = res.body.content;
                  console.log("the list ================================111");
                  console.log(content);
                  this.list = content.list || [];
                  this.count = content.count || 0;
                  resolve(res.body.content.body)
                } else {
                  reject(res.body.msg)
                this.$message.error(res.body.msg);
                }
              }, err => {
                this.$message.error(err);
                this.queryBtnStatus = false;
              })
            })

          }
        })
      },

    // 导出功能
    exportExcel() {
      this.$refs.query.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.query));
          data.begin_date = +new Date(data.begin_date);
          data.end_date = +new Date(data.end_date);
          this.exportBtnStatus = true;
          this.ajax.postStream('/reports-web/api/reports/dealer/performance/exportExcel', {
            begin_date: data.begin_date,
            end_date: data.end_date,
            shop_id: data.shop_id,
            if_timeliness: data.if_timeliness,
          }, res => {
            this.exportBtnStatus = false;
            this.$message({
              type: res.body.result ? 'success' : 'error',
              message: res.body.msg
            })
          }, err => {
            this.$message.error(err);
            this.exportBtnStatus = false;
          })
        }
      })
    },
    setShopDefault (){
      this.setShopDefault.where = []
      this.ajax.postStream('/user-web/api/sql/listFields', {"page":"cloud_shop_v2"}, res => {
        if(res.body.result){
          (res.body.content.fields || []).forEach(o => {
            if(o.comment === '店铺分类'){
              this.setShopDefault.where.push({
                "field": o.field,
                "table": o.table,
                "value": "PURCHASE_DEALER",
                "operator": "=",
                "condition": "AND",
                "listWhere": []
              })
            }else if(o.comment === '店铺状态'){
              this.setShopDefault.where.push({
                "field": o.field,
                "table": o.table,
                "value": "OPEN",
                "operator": "=",
                "condition": "AND",
                "listWhere": []
              })
            }
          })
        }
      })
    },
    getCustomerSourceId (){
      this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttributeList', {
        "page": {
          "length": 50,
          "pageNo": 1
        },
        "personId": this.getEmployeeInfo('personId'),
        "key": "",
        "salesmanType": "GLOBAL"
      }, res => {
        if(res.body.result){
          ;(res.body.content.list || []).some(o => {
            if(o.salesmanType === 'GLOBAL' && o.attribute === 'CUSTOMER'){
              if(o.attributeValue != '22549067'){
                this.getCustomerSourceId.id = o.attributeValue
              }
              return true
            }
          })
        }
      })
    },
    },
    mounted(){
      this.setShopDefault()
      this.getCustomerSourceId()
      this.getYearList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
</style>
<style module>
.end-date-fix input {
  color: #fff;
}
.end-date-fix:before {
  content: attr(data-date);
  position: absolute;
  left: 10px;
  pointer-events: none;
}
</style>
