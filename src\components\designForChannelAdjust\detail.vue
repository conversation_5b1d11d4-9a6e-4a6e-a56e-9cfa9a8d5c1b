<!--渠道专供调整单详情-->
<template>
    <div>
        <el-row class="xpt-top" :gutter="40">
            <el-col :span="24">
                <el-button size="mini" type="success" @click="refresh()"
                    >刷新</el-button
                >
                <el-button
                    size="mini"
                    type="info"
                    @click="presave()"
                    :loading="isSaving"
                    :disabled="!canIsave"
                    >保存</el-button
                >
                <el-button
                    size="mini"
                    type="warning"
                    @click="aduit()"
                    :disabled="!canIaduit"
                    >审核</el-button
                >
                <el-button
                    size="mini"
                    type="warning"
                    @click="valid()"
                    :disabled="!canIvalid"
                    >实施</el-button
                >
                <el-button
                    size="mini"
                    type="danger"
                    @click="invalid()"
                    :disabled="!canIinvalid"
                    >失效</el-button
                >
            </el-col>
        </el-row>
        <el-row class="xpt-flex__bottom">
            <el-tabs v-model="firstTab" class="basic-info">
                <el-tab-pane
                    label="基本信息"
                    name="basicInfo"
                    class="xpt-flex"
                    style="overflow: hidden"
                >
                    <el-form
                        ref="submitData"
                        :model="submitData"
                        label-position="right"
                        label-width="70px"
                    >
                        <el-row class="mgt20" :gutter="20">
                            <el-col :span="5">
                                <el-form-item label="单据号" prop="bill_no">
                                    <el-input
                                        v-model="submitData.bill_no"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item label="单据状态" prop="status">
                                    <el-input
                                        :value="
                                            statusObj[submitData.status] ||
                                            submitData.status
                                        "
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5">
                                <el-form-item
                                    label="影响日期起"
                                    prop="enable_time"
                                    label-width="80"
                                >
                                    <el-date-picker
                                        type="date"
                                        v-model="submitData.enable_time"
                                        size="mini"
                                        :picker-options="beginDateOptions"
                                        :disabled="disabledEdit"
                                        format="yyyy-MM-dd HH:mm:ss"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item
                                    label="影响日期止"
                                    prop="disable_time"
                                    label-width="80"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.disable_time"
                                        size="mini"
                                        :disabled="true"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5">
                                <el-form-item
                                    label="创建人"
                                    prop="creatorFullName"
                                >
                                    <el-input
                                        v-model="submitData.creatorFullName"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="创建时间"
                                    prop="create_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.create_time"
                                        :picker-options="endDateOptions"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5">
                                <el-form-item
                                    label="审核人"
                                    prop="auditorFullName"
                                >
                                    <el-input
                                        v-model="submitData.auditorFullName"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="审核时间"
                                    prop="audit_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.audit_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="5">
                                <el-form-item
                                    label="提交人"
                                    prop="submitterFullName"
                                >
                                    <el-input
                                        v-model="submitData.submitterFullName"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="提交时间"
                                    prop="submit_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.submit_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5">
                                <el-form-item
                                    label="实施人"
                                    prop="implementerFullName"
                                >
                                    <el-input
                                        v-model="submitData.implementerFullName"
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="实施时间"
                                    prop="implement_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.implement_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5">
                                <el-form-item
                                    label="修改人"
                                    prop="modifierFullName"
                                >
                                    <el-input
                                        v-model="
                                            submitData.modifierFullName
                                        "
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="修改时间"
                                    prop="modify_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.modify_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="5">
                                <el-form-item
                                    label="失效人"
                                    prop="invalid_personFullName"
                                >
                                    <el-input
                                        v-model="
                                            submitData.invalid_personFullName
                                        "
                                        size="mini"
                                        disabled
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    label="失效时间"
                                    prop="invalid_time"
                                >
                                    <el-date-picker
                                        type="datetime"
                                        v-model="submitData.invalid_time"
                                        size="mini"
                                        disabled
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
            <el-tabs v-model="secondTab" class="wait-adjust-list">
                <el-tab-pane
                    label="待调整物料清单"
                    name="waitAdjustMaterialList"
                    class="item-pane"
                >
                    <xpt-list
                        id="wait-list"
                        :orderNo="true"
                        :data="waitList"
                        :btns="btns"
                        :colData="colData"
                        selection="checkbox"
                        :searchPage="search.page_name"
                        :pageTotal="pageTotal"
                        @search-click="presearch"
                        @page-size-change="sizeChange"
                        @current-page-change="pageChange"
                        @selection-change="handleSelectionChange"
                        ref="waitList"
                    >
                        <template slot="material_channel" slot-scope="scope">
                            <xpt-select-aux
                                v-model="scope.row.material_channel"
                                aux_name="material_for_channel"
                                placeholder="请选择渠道"
                                :disabled="disabledEdit"
                                size="mini"
                                style="width: 100%"
                                @visible-change="selectChange(scope.row)"
                            ></xpt-select-aux>
                        </template>
                    </xpt-list>
                </el-tab-pane>
            </el-tabs>
        </el-row>
    </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            currentId: "",
            firstTab: "basicInfo",
            secondTab: "waitAdjustMaterialList",
            submitData: {
                audit_time: "",
                auditor: "",
                auditorFullName: "",
                bill_no: "",
                create_time: "",
                creator: "",
                creatorFullName: "",
                disable_time: "",
                enable_time: "",
                id: "",
                implement_time: "",
                implementer: "",
                implementerFullName: "",
                invalid_person: "",
                invalid_personFullName: "",
                invalid_time: "",
                modifier: "",
                modifierFullName: "",
                modify_time: "",
                scmMonitorMaterialListVOList: [],
                status: "",
                submit_time: "",
                submitter: "",
                submitterFullName: "",
            },
            statusObj: {
                SUBMIT: "已提交",
                AUDIT: "已审核",
                NORMAL: "生效",
                INVALID: "失效",
            },
            isSaving: false,
            canIsave: false,
            canIvalid: false,
            canIinvalid: false,
            canIaduit: false,
            canIDelete:false,
            disabledEdit:false,
            pageTotal: 0,
            waitList: [],
            search: {
                page_name: "scm_channel_monitor_material_list",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
                if_need_page: "Y",
                applicationId: "",
            },
            btns: [
                {
                    type: "success",
                    txt: "刷新",
                    click: self.searching,
                    loading: false,
                },
                {
                    type: "danger",
                    txt: "删除",
                    click: self.deleteMaterialItem,
                    loading: false,
                    disabled: () => {
                        return !self.canIDelete;
                    },
                },
            ],
            colData: [
                {
                    label: "物料编码",
                    prop: "material_number",
                },
                {
                    label: "物料规格",
                    prop: "material_spec",
                },
                {
                    label: "商品名称",
                    prop: "material_name",
                },
                {
                    label: "主型号",
                    prop: "model",
                },
                {
                    label: "状态",
                    prop: "status",
                    formatter: (val) => {
                        let status = {
                            CREATE: "创建",
                            SUBMIT: "已提交",
                            AUDIT: "已审核",
                            NORMAL: "生效",
                        };
                        return status[val] || val;
                    },
                },
                {
                    label: "原渠道专供",
                    prop: "origin_channel",
                    format: "auxFormat",
                    formatParams: "material_for_channel",
                },
                {
                    label: "新渠道专供",
                    slot: "material_channel",
                },
                {
                    prop: "is_new_material",
                    label: "是否新维护物料"
                },
            ],
            selectChangeList: [],//修改的新渠道专供列表
            beginDateOptions: self.beginDateSet(),
            endDateOptions: self.endDateSet(),
            multipleSelection:[],//待调整物料选择的行
        };
    },
    mounted() {
        console.log(this.params);
        this.currentId = this.params && this.params.product_adjust_id;
        this.initDetail();
    },
    computed: {},
    methods: {
        initDetail() {
            if (!this.currentId) {
                this.setDetailEdit()
                this.submitData.status = "SUBMIT";
                this.submitData.enable_time= new Date(new Date().toLocaleDateString()).getTime()+24*60*60*1000;
                this.submitData.disable_time = new Date(
                    "9999-12-31 23:59:59"
                ).getTime();
                this.searching();
            } else {
                this.getDetail();
                this.searching();
            }
        },
        getDetail() {
            this.ajax.get(
                `/material-web/api/materialChannelAdjust/get?id=${this.currentId}`,
                (res) => {
                    if (res.body.result) {
                        this.submitData = res.body.content;
                        this.setDetailEdit();
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
        presearch(list, resolve) {
            this.search.where = list;
            this.searching(resolve);
        },
        searching(resolve) {
            if (!this.currentId) {
                // this.$message.warning("请先保存！");
                // return;
                this.search.applicationId = '';
                this.search.where.push({"condition":"AND","field":"9acb44549b41563697bb490144ec6258","operator":"=","table":"af56ee08d4d37bbc402165b2d77172d5","value":"CREATE"})
            }else{
                this.search.applicationId = this.currentId;
                // this.search.where=[];
            }
            let self = this;
            this.multipleSelection = [];
            this.selectChangeList=[];
            this.btns[0].loading = true;
            console.log(this.search.applicationId);
            this.ajax.postStream(
                "/material-web/api/materialChannelAdjust/monitor/list",
                this.search,
                (res) => {
                    if (res.body.result) {
                        self.pageTotal = res.body.content.count;
                        if (res.body.content.list) {
                            let list = res.body.content.list;
                            self.waitList=list
                        }
                        self.$message.success(res.body.msg);
                    } else {
                        self.waitList = [];
                        self.pageTotal = 0;
                        self.$message.error(res.body.msg);
                    }
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                },
                (err) => {
                    self.$message.error(err);
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                }
            );
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page_size = size;
            this.searching();
        },
        pageChange(page_no) {
            // 页数改变
            this.search.page_no = page_no;
            this.searching();
        },
        refresh() {
            this.selectChangeList=[]//每次刷新重置修改的待调整物料
            this.initDetail();
        },
        presave(callback) {
            let self = this;
            console.log(this.selectChangeList);
            this.submitData.scmMonitorMaterialListVOList = this.selectChangeList;
            this.ajax.postStream(
                "/material-web/api/materialChannelAdjust/save",
                this.submitData,
                (res) => {
                    if (res.body.result) {
                        if(!self.currentId){
                            self.currentId=res.body.content;
                            self.search.where=[];
                        }
                            "function" === typeof callback ? callback() :(function(){
                                self.$message.success(res.body.msg||'保存成功');
                                self.refresh();
                            }())
                    } else {
                        self.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    self.$message.error(err);
                }
            );
        },
        setDetailEdit(){
            if(!this.currentId){
                this.canIsave=true;
                return;
            }
            let status=this.submitData.status;
            this.canIsave=["SUBMIT"].includes(status)
            this.canIaduit=["SUBMIT"].includes(status)
            this.canIvalid=["AUDIT"].includes(status)
            this.canIinvalid=!["NORMAL","INVALID"].includes(status)
            this.canIDelete=["SUBMIT"].includes(status)
            this.disabledEdit=["AUDIT","INVALID","NORMAL"].includes(status)
        },
        aduit() {
            this.presave(()=>{
                console.log("AUDIT")
                this.changeSheetStatus("AUDIT","CHANNEL_ADJUST_AUDIT");
            });
        },
        valid() {
            this.changeSheetStatus("NORMAL","CHANNEL_ADJUST_NORMAL");
        },
        invalid() {
            this.changeSheetStatus("INVALID","CHANNEL_ADJUST_NORMAL");
        },
        changeSheetStatus(status, code) {
            let ids = [];
            ids.push(this.currentId);
            let params = {
                ids: ids,
                status: status,
            };
            this.ajax.postStream(
                `/material-web/api/materialChannelAdjust/batchChangeStatus?permissionCode=${code}`,
                params,
                (res) => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg);
                        this.refresh();
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
        selectChange(changelist) {
            if (!!changelist.length) {
                return;
            }
            let list = this.selectChangeList;
            if (list.every((item) => item.id != changelist.id)) {
                this.selectChangeList.push(changelist);
            }
            console.log('selectChangeList',this.selectChangeList)
        },
        beginDateSet() {
            let self = this;
            console.log(self);
            return {
                disabledDate(time) {
                    if (self.submitData.disable_time) {
                        return (
                            time.getTime() >
                                new Date(
                                    self.submitData.disable_time
                                ).getTime()
                        );
                    }
                },
            };
        },
        endDateSet() {
            let self = this;
            return {
                disabledDate(time) {
                    if (self.submitData.enable_time) {
                        return (
                            time.getTime() <=
                            new Date(self.submitData.enable_time).getTime()
                        );
                    }
                },
            };
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            console.log(this.multipleSelection);
        },
        deleteMaterialItem(){
            let self=this
            let materialIdList=[]
            if(this.multipleSelection.length>0){
                materialIdList=this.multipleSelection.map(item=>item.id)
            }else{
                this.$message.error("请选择要删除的物料")
                return
            }
            this.btns[1].loading=true;
            this.ajax.postStream('/material-web/api/materialChannelAdjust/detail/remove',materialIdList,res=>{
                if(res.body.result){
                    // self.$message.success(res.body.msg);
                    self.searching();
                }else{
                    self.$message.error(res.body.msg);
                }
                self.btns[1].loading=false;
            },err=>{
                self.$message.error(err);
                self.btns[1].loading=false;
            })
        },
    },
};
</script>
<style lang="stylus" scoped>
.xpt-flex__bottom 
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 30px;

    .basic-info 
        height: 196px;

    .wait-adjust-list
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 196px;
        height:auto;
.item-pane 
    display: flex;
    flex-direction: column;
    height: auto;


.mgt20 
    margin-top: 20px;


.el-input 
    width: 144px;

.el-form-item 
    margin-bottom: 10px;


.right-search 
    display: flex;
    justify-content: flex-end;
    align-items: center;
</style>
<style>
#wait-list .xpt-top{
    padding: 2px 20px;
}
    

#wait-list .xpt-flex__bottom{
    padding-bottom: 48px;
}
</style>