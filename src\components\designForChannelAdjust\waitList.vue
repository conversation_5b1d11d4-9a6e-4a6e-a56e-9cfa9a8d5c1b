<!--待调整渠道专供物料清单-->
<template>
    <xpt-list-dynamic
        ref="list"
        :data="list"
        :btns="btns"
        :colData="cols"
        selection="hidden"
        :pageTotal="count"
        :searchPage="search.page_name"
        @search-click="presearch"
        @page-size-change="sizeChange"
        @current-page-change="pageChange"
    ></xpt-list-dynamic>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            search: {
                page_name: "scm_channel_monitor_material_list",
                applicationId: "",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
                if_need_page: "Y",
            },
            list: [],
            selection: "checkbox",
            count: 0,
            multipleSelection: [], //列表选择索引
            btns: [
                {
                    type: "success",
                    txt: "刷新",
                    click: self.searching,
                    loading: false,
                },
            ],
            cols: [
                { prop: "material_number", label: "物料编码" },
                { prop: "material_name", label: "商品名称" },
                { prop: "material_spec", label: "物料规格" },
                { prop: "model", label: "主型号" },
                {
                    prop: "is_new_material",
                    label: "是否新维护物料"
                },
                {
                    prop: "import_time",
                    label: "导入时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "status",
                    label: "行状态",
                    formatter(val) {
                        return (
                            {
                                CREATE: "创建",
                                SUBMIT: "已提交",
                                AUDIT: "已审核",
                                NORMAL: "生效",
                            }[val] || val
                        );
                    },
                },
                {
                    prop: "origin_channel",
                    label: "原渠道专供",
                    format: "auxFormat",
                    formatParams: "material_for_channel",
                },
                {
                    prop: "material_channel",
                    label: "新渠道专供",
                    format: "auxFormat",
                    formatParams: "material_for_channel",
                },
                {
                    prop: "enable_time",
                    label: "影响时间起",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "disable_time",
                    label: "影响时间止",
                    format: "dataFormat1",
                    width: 130,
                },
            ],
        };
    },
    methods: {
        presearch(list, resolve) {
            this.search.where = list;
            this.searching(resolve);
        },
        searching(resolve) {
            let self = this;
            this.btns[0].loading = true;
            this.ajax.postStream(
                "/material-web/api/materialChannelAdjust/monitor/list",
                this.search,
                (res) => {
                    if (res.body.result) {
                        self.count = res.body.content.count;
                        if (res.body.content.list) {
                            self.list = res.body.content.list;
                        }
                        self.$message.success(res.body.msg);
                    } else {
                        self.list = [];
                        self.count = 0;
                        self.$message.error(res.body.msg);
                    }
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                },
                (err) => {
                    self.$message.error(err);
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                }
            );
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page_size = size;
            this.searching();
        },
        pageChange(page_no) {
            // 页数改变
            this.pageNow = page_no;
            this.search.page_no = page_no;
            this.searching();
        },
    },
    mounted() {
        let self = this;
        self.searching();
    },
    destroyed() {},
};
</script>
<style scoped>
</style>