<!--渠道专供变更申请单详情-->
<template>
  <div>
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button size="mini" type="success" @click="refresh()"
          >刷新</el-button
        >
        <el-button
          size="mini"
          type="info"
          @click="presave()"
          :loading="isSaving"
          :disabled="!canIsave"
          >保存</el-button
        >
        <el-button
          size="mini"
          type="warning"
          @click="submit()"
          :disabled="!canIsubmit"
          >提交</el-button
        >
        <el-button size="mini" type="danger" @click="retract()" :disabled="true"
          >撤回</el-button
        >
        <el-button size="mini" type="warning" @click="aduit()" :disabled="true"
          >审核</el-button
        >
        <el-button
          size="mini"
          type="warning"
          @click="complete()"
          :disabled="submitData.status !== 'CHANGE'"
          >完成变更</el-button
        >
        <el-button
          size="mini"
          type="danger"
          @click="turnDown()"
          :disabled="true"
          >驳回</el-button
        >
        <el-button size="mini" type="warning" @click="valid()" :disabled="true"
          >实施</el-button
        >
        <el-button
          size="mini"
          type="warning"
          @click="invalid()"
          :disabled="!canIinvalid"
          >失效</el-button
        >
        <el-button size="mini" type="info" @click="exportExcel()"
          >导出</el-button
        >
        <el-button size="mini" type="info" @click="exportList()"
          >导出结果查看</el-button
        >
    
          <xpt-upload-v3
            uploadBtnText="上传附件"
            :uploadSize="20"
            :dataObj="uploadDataObj"
            :disabled="false"
            :ifMultiple="false"
            :showSuccessMsg="false"
            @uploadSuccess="uploadSuccess1"
            btnType="success"
            style="display: inline-block; margin: 0px 10px"
          >
        </xpt-upload-v3>
        <el-button size="mini" type="info" :disabled="!fileDownloadPath" @click="downloadFilePath()"
          >附件下载</el-button
        >
      </el-col>
    </el-row>
    <el-row class="xpt-flex__bottom">
      <el-tabs v-model="firstTab" class="basic-info">
        <el-tab-pane
          label="基本信息"
          name="basicInfo"
          class="xpt-flex"
          style="overflow: hidden"
        >
          <el-form
            ref="submitData"
            :model="submitData"
            label-position="right"
            label-width="70px"
            :rules="rules"
          >
            <el-row class="mgt20" :gutter="20">
              <el-col :span="6">
                <el-form-item label="单据号" prop="bill_no">
                  <el-input
                    v-model="submitData.bill_no"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="单据状态" prop="status">
                  <el-input
                    :value="statusObj[submitData.status] || submitData.status"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="提交人" prop="submitterFullName">
                  <el-input
                    v-model="submitData.submitterFullName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="提交时间" prop="submit_time">
                  <el-date-picker
                    type="datetime"
                    v-model="submitData.submit_time"
                    size="mini"
                    disabled
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="审核人" prop="auditorFullName">
                  <el-input
                    v-model="submitData.auditorFullName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="审核时间" prop="audit_time">
                  <el-date-picker
                    type="datetime"
                    v-model="submitData.audit_time"
                    size="mini"
                    disabled
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="实施人" prop="implementerFullName">
                  <el-input
                    v-model="submitData.implementerFullName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="实施时间" prop="implement_time">
                  <el-date-picker
                    type="datetime"
                    v-model="submitData.implement_time"
                    size="mini"
                    disabled
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item
                  label="影响日期起"
                  prop="enable_time"
                  label-width="80"
                >
                  <el-date-picker
                    type="date"
                    v-model="submitData.enable_time"
                    size="mini"
                    :picker-options="beginDateOptions"
                    :disabled="!editEnableTime"
                    format="yyyy-MM-dd HH:mm:ss"
                  >
                  </el-date-picker>
                  <el-tooltip
                    v-if="rules.enable_time[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.enable_time[0].message"
                    placement="right"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item
                  label="影响日期止"
                  prop="disable_time"
                  label-width="80"
                >
                  <el-date-picker
                    type="datetime"
                    v-model="submitData.disable_time"
                    size="mini"
                    :picker-options="endDateOptions"
                    :disabled="!editDisableTime"
                  >
                  </el-date-picker>
                  <el-tooltip
                    v-if="rules.disable_time[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.disable_time[0].message"
                    placement="right"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="创建人" prop="creatorFullName">
                  <el-input
                    v-model="submitData.creatorFullName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="创建时间" prop="create_time">
                  <el-date-picker
                    type="datetime"
                    v-model="submitData.create_time"
                    size="mini"
                    disabled
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="驳回人" prop="rejecterFullName">
                  <el-input
                    v-model="submitData.rejecterFullName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="驳回时间" prop="reject_time">
                  <el-date-picker
                    type="datetime"
                    v-model="submitData.reject_time"
                    size="mini"
                    disabled
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="失效人" prop="invalidPersonFullName">
                  <el-input
                    v-model="submitData.invalidPersonFullName"
                    size="mini"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="失效时间" prop="invalid_time">
                  <el-date-picker
                    type="datetime"
                    v-model="submitData.invalid_time"
                    size="mini"
                    disabled
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <el-tabs v-model="secondTab" class="item-material">
        <el-tab-pane label="子项物料" name="itemMaterial" class="item-pane">
          <xpt-list
            id="sub-list"
            :orderNo="true"
            :data="subList"
            :btns="subBtns"
            :colData="subColData"
            selection="checkbox"
            @selection-change="subHandleSelectionChange"
            :searchPage="subSearch.page_name"
            :pageTotal="subPageTotal"
            @search-click="subPresearch"
            @page-size-change="subSizeChange"
            @current-page-change="subPageChange"
            ref="subList"
          >
            <template slot="material_channel" slot-scope="scope">
              <xpt-select-aux
                v-model="scope.row.material_channel"
                aux_name="material_for_channel"
                placeholder="请选择渠道"
                :disabled="!editSubNewCategory"
                size="mini"
                style="width: 100%"
                @visible-change="subListSelectChange(scope.row.id)"
              ></xpt-select-aux>
            </template>
            <el-button
              slot="btns"
              size="mini"
              type="info"
              @click="downloadTemplate()"
              :disabled="!canIupload"
              >模板下载</el-button
            >
            <xpt-upload-v3
              slot="btns"
              uploadBtnText="导入"
              :uploadSize="20"
              acceptTypeStr=".xlsx,.xls"
              :dataObj="uploadDataObj"
              :disabled="!canIupload"
              :ifMultiple="false"
              :showSuccessMsg="false"
              @uploadSuccess="uploadSuccess"
              btnType="success"
              style="display: inline-block; margin: 0px 10px"
            ></xpt-upload-v3>
            <el-button
              slot="btns"
              size="mini"
              type="warning"
              @click="showUploadResult()"
              :disabled="!canIupload"
              >导入结果</el-button
            >
          </xpt-list>
        </el-tab-pane>
        <el-tab-pane
          label="关联商品清单"
          name="relatedProductsList"
          class="item-pane"
        >
          <xpt-list
            id="related-list"
            :orderNo="true"
            :data="relatedList"
            :btns="relatedBtns"
            :colData="relatedColData"
            selection=""
            :searchPage="relatedSearch.page_name"
            :pageTotal="relatedPageTotal"
            @search-click="relatedPresearch"
            @page-size-change="relatedSizeChange"
            @current-page-change="relatedPageChange"
            :taggelClassName="taggelClassName"
            ref="relatedList"
          >
            <!-- <template slot="channel" slot-scope="scope">
              <xpt-select-aux
                v-model="scope.row.channel"
                @visible-change="RelatedListSelectChange(scope.row.id)"
                aux_name="material_for_channel"
                placeholder="请选择渠道"
                :disabled="!editSubNewCategory"
                size="mini"
                style="width: 100%"
              ></xpt-select-aux>
            </template> -->
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>
<script>
import validate from "@common/validate.js";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      currentId: "",
      submitData: {
        id: "",
        bill_no: "",
        status: "",
        creator: "",
        creatorFullName: "",
        create_time: "",
        modifier: "",
        modifierFullName: "",
        modify_time: "",
        auditor: "",
        auditorFullName: "",
        audit_time: "",
        implementer: "",
        implementerFullName: "",
        implement_time: "",
        enable_time: "",
        disable_time: "",
        submitter: "",
        submitterFullName: "",
        submit_time: "",
        rejecter: "",
        rejecterFullName: "",
        reject_time: "",
        invalid_person: null,
        invalidPersonFullName: null,
        invalid_time: null,
        channelSubMaterialList: [],
        channelRelatedMaterialList: [],
      },
      submitAddData: {
        id: "",
        bill_no: "",
        status: "",
        creator: "",
        creatorFullName: "",
        create_time: "",
        modifier: "",
        modifierFullName: "",
        modify_time: "",
        auditor: "",
        auditorFullName: "",
        audit_time: "",
        implementer: "",
        implementerFullName: "",
        implement_time: "",
        enable_time: "",
        disable_time: "",
        submitter: "",
        submitterFullName: "",
        submit_time: "",
        rejecter: "",
        rejecterFullName: "",
        reject_time: "",
        invalid_person: null,
        invalidPersonFullName: null,
        invalid_time: null,
        channelSubMaterialList: [],
        channelRelatedMaterialList: [],
      }, //添加申请单默认参数
      rules: {
        enable_time: validate.isNotBlank({
          self: self,
          msg: "不能为空",
        }),
        disable_time: validate.isNotBlank({
          self: self,
          msg: "不能为空",
        }),
      },
      statusObj: {
        CREATE: "创建",
        SUBMIT: "待审核",
        RETRACT: "撤回",
        AUDIT: "待实施",
        CHANGE: "待变更",
        REJECT: "驳回",
        NORMAL: "生效",
        INVALID: "失效",
      },
      materialGroupTranslate: {
        "01": "成品",
        XSP: "小商品",
      },
      cpOptions: [
        {
          value: "A",
          label: "A类商品",
        },
        {
          value: "B",
          label: "B类商品",
        },
        {
          value: "C",
          label: "C类商品",
        },
        {
          value: "D",
          label: "D类商品",
        },
        {
          value: "S",
          label: "S类商品",
        },
      ],
      xspOptions: [
        {
          value: "A",
          label: "A类商品",
        },
        {
          value: "E",
          label: "E类商品",
        },
      ],
      firstTab: "basicInfo",
      secondTab: "itemMaterial",
      isSaving: false,
      changeSubIdList: [], //子项物料修改项id列表
      changeRelatedIdList: [], //关联项id列表
      itemSearch: {
        code: "",
        childCode: "",
      },
      isItemSearch: false,
      multipleSelection: [],
      beginDateOptions: self.beginDateSet(),
      endDateOptions: self.endDateSet(),

      // 子项物料列表
      subList: [],
      subBtns: [
        {
          type: "warning",
          txt: "添加",
          click: self.addGoods,
          disabled: () => !self.canIadd,
          loading: false,
        },
        {
          type: "danger",
          txt: "删除",
          click: self.deleteGoods,
          disabled: () => !self.canIdelete,
          loading: false,
        },
      ],
      subColData: [
        {
          label: "子项物料编码",
          prop: "material_number",
        },
        {
          label: "子项物料名称",
          prop: "material_name",
        },
        {
          label: "子项商品规格",
          prop: "material_desc",
        },
        {
          label: "主型号",
          prop: "model",
        },
        {
          label: "物料分组",
          prop: "material_group",
          formatter: (val) => {
            return (
              {
                "01": "成品",
                XSP: "小商品",
              }[val] || val
            );
          },
        },
        {
          label: "原渠道专供",
          prop: "origin_material_channel",
          format: "auxFormat",
          formatParams: "material_for_channel",
        },
        {
          label: "新渠道专供",
          slot: "material_channel",
        },
        {
          label: "MDM是否变更",
          prop: "if_mdm_change",
          formatter: (val) => {
            return (
              {
                Y: "是",
                N: "否",
              }[val] || val
            );
          },
          width: 130,
        },
        {
          label: "影响日期起",
          prop: "enable_time",
          format: "dataFormat1",
          width: 130,
        },
        {
          label: "影响日期止",
          prop: "disable_time",
          format: "dataFormat1",
          width: 130,
        },
      ],
      subPageTotal: 0,
      subSearch: {
        page_name: "material_channel_sub_material_list",
        where: [],
        page_size: self.pageSize,
        page_no: 1,
        if_need_page: "Y",
      },
      // 关联物料列表
      relatedList: [],
      relatedBtns: [
        {
          type: "success",
          txt: "导出",
          loading: false,
          click() {
            self.exportRelatedExcel();
          },
        },
        {
          type: "warning",
          txt: "导出结果",
          loading: false,
          click() {
            self.exportRelatedResult();
          },
        },
      ],
      relatedColData: [
        {
          label: "关联物料编码",
          prop: "material_number",
        },
        {
          label: "关联商品名称",
          prop: "material_name",
        },
        {
          label: "关联物料规格",
          prop: "material_spec",
        },

        {
          label: "主型号",
          prop: "model",
        },
        {
          label: "子项物料编码",
          prop: "child_material_number",
        },
        {
          label: "原渠道专供",
          prop: "origin_channel",
          format: "auxFormat",
          formatParams: "material_for_channel",
        },
        {
          label: "新渠道专供",
          prop: "channel",
          format: "auxFormat",
          formatParams: "material_for_channel",
        },
        {
          label: "MDM是否变更",
          prop: "if_mdm_change",
          formatter: (val) => {
            return (
              {
                Y: "是",
                N: "否",
              }[val] || val
            );
          },
          width: 130,
        },
        {
          label: "影响日期起",
          prop: "enable_time",
          format: "dataFormat1",
          width: 130,
        },
        {
          label: "影响日期止",
          prop: "disable_time",
          format: "dataFormat1",
          width: 130,
        },
      ],
      relatedPageTotal: 0,
      relatedSearch: {
        page_name: "material_channel_related_material_list",
        where: [],
        page_size: self.pageSize,
        page_no: 1,
        if_need_page: "Y",
      },

      canIeditDetail: true,
      canIeditSubNewCategory: false,
      canIeditRelatedNewCategory: false,
      canIsave: true,
      canIsubmit: true,
      canIretract: true,
      canIaduit: true,
      canIreject: true,
      canIvalid: true,
      canIinvalid: true,
      canIupload: false,
      canIadd: false,
      canIdelete: false,
      disabledAllEdit: false,
      canComplete: false, //完成变更按钮
      uploadDataObj: {},
      fileDownloadPath:"",//上传附件下载地址
    };
  },
  mounted() {
    console.log(this.params);
    this.currentId = (this.params && this.params.change_request_id) || "";
    this.refresh();
    //监听切换业务代理事件
    this.$root.eventHandle.$on("resetAllBtnStatus", () => {
      this.setDetailEdit();
    });
  },
  computed: {
    editEnableTime() {
      return this.canIeditDetail;
    },
    editDisableTime() {
      return this.canIeditDetail;
    },
    editSubNewCategory() {
      return this.canIeditSubNewCategory;
    },
    editRelatedNewCategory() {
      return this.canIeditRelatedNewCategory;
    },
  },
  methods: {
    // 导出
    exportExcel() {
      this.ajax.postStream(
        "/material-web/api/materialChannelChange/export",
        { id: this.currentId },
        (res) => {
          if (res.body.result) {
            res.body.msg && this.$message.success(res.body.msg);
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 查看导出结果
    exportList() {
      this.$root.eventHandle.$emit("alert", {
        params: {
          query: {
            type: "EXCEL_TYPE_MATERIAL_CHANNEL_CHANGE_EXPORT",
          },
        },
        component: () => import("@components/after_sales_report/export"),
        style: "width:800px;height:400px",
        title: "导出结果",
      });
    },
    // 完后变更
    complete() {
      this.ajax.postStream(
        "/material-web/api/materialChannelChange/complete?permissionCode=CHANNEL_CHANGE_COMPLETE",
        {
          id: this.submitData.id,
        },
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            this.refresh();
          } else {
            this.$message.error(res.body.msg);
            this.refresh();
          }
        },
        (err) => {
          this.$message.error(err);
          this.refresh();
        }
      );
    },
    getDetail() {
      this.ajax.get(
        `/material-web/api/materialChannelChange/get?id=${this.currentId}`,
        (res) => {
          console.log(res);
          if (res.body.result && res.body.content) {
            this.submitData = { ...res.body.content };
            // 附件下载地址
            this.fileDownloadPath=res.body.content.file_path
            this.submitData.channelSubMaterialList = [];
            this.submitData.channelRelatedMaterialList = [];
            this.setDetailEdit();
          } else {
            this.$message.error(res.body.msg);
          }
          this.isRefreshing = false;
        },
        (err) => {
          this.$message.error(err);
          this.isRefreshing = false;
        }
      );
    },
    async setDetailEdit() {
      // 添加
      if (!this.currentId) {
        this.canIsubmit = false;
        this.canIretract = false;
        this.canIaduit = false;
        this.canIreject = false;
        this.canIvalid = false;
        this.canIinvalid = false;
        this.canIupload = false;
        this.canIadd = true;
        this.canIdelete = true;
        return;
      }
      // 失效
      this.disabledAllEdit = ["INVALID"].includes(this.submitData.status);
      if (this.disabledAllEdit) {
        this.canIeditDetail = false;
        this.canIeditSubNewCategory = false;
        this.canIeditRelatedNewCategory = false;
        this.canIsave = false;
        this.canIsubmit = false;
        this.canIretract = false;
        this.canIaduit = false;
        this.canIreject = false;
        this.canIvalid = false;
        this.canIinvalid = false;
        this.canIupload = false;
        this.canIadd = false;
        this.canIdelete = false;
        return;
      }
      //其它状态
      let status = this.submitData.status;
      let creator = this.submitData.creator;
      // let auditor = this.submitData.auditor;
      let currentPersonId = this.getEmployeeInfo("id");
      console.log(currentPersonId);
      this.canIeditDetail = ["CREATE", "RETRACT", "REJECT"].includes(status);
      await this.getSubNewCategoryRights().then((res) => {
        this.canIeditSubNewCategory =
          res && ["CREATE", "RETRACT", "REJECT"].includes(status);
      });
      await this.getRelatedNewCategoryRights().then((res) => {
        this.canIeditRelatedNewCategory =
          res && ["CREATE", "RETRACT", "REJECT"].includes(status);
      });
      console.table([
        ["修改子项新渠道专供", this.canIeditSubNewCategory],
        ["修改关联项新渠道专供", this.canIeditRelatedNewCategory],
      ]);
      this.canIsave = [
        "CREATE",
        "SUBMIT",
        "RETRACT",
        "REJECT",
        "AUDIT",
      ].includes(status);
      this.canIsubmit = ["CREATE", "RETRACT", "REJECT"].includes(status);
      this.canIretract =
        ["SUBMIT"].includes(status) && currentPersonId == creator;
      this.canIaduit = ["SUBMIT"].includes(status);
      this.canIreject = ["SUBMIT"].includes(status);
      this.canIvalid = ["AUDIT"].includes(status);
      this.canIinvalid = !["NORMAL"].includes(status);
      this.canIupload =
        !!this.currentId && ["CREATE", "RETRACT", "REJECT"].includes(status);
      this.canIadd = ["CREATE", "RETRACT", "REJECT"].includes(status);
      this.canIdelete = ["CREATE", "RETRACT", "REJECT"].includes(status);
    },
    refresh() {
      this.isRefreshing = true;
      this.changeSubIdList = [];
      this.changeRelatedIdList = [];
      if (!!this.currentId) {
        this.getDetail();
        this.subSearching();
        this.relatedSearching();
        this.setUploadDataObj();
      } else {
        this.subList = [];
        this.submitAddData.channelSubMaterialList = [];
        this.submitAddData.channelRelatedMaterialList = [];
        // this.submitAddData.enable_time = new Date(
        //     new Date().toLocaleDateString()
        // ).getTime()+24*60*60*1000;
        this.submitAddData.enable_time = "";
        this.submitAddData.disable_time = +new Date(9999, 11, 31, 23, 59, 59);
        this.submitData = Object.assign({}, this.submitAddData);
        console.log(this.submitData);
        this.setDetailEdit();
      }
    },
    presave(callback) {
      let self = this;
      let submitData = Object.assign({}, this.submitData);
      this.$refs.submitData.validate((valid) => {
        if (!valid) {
          return;
        }
        //提交状态不传影响日期起止
        if (["SUBMIT"].includes(this.submitData.status)) {
          delete submitData.enable_time;
          delete submitData.disable_time;
        }
        //如果修改了子项物料类别，则加传参数
        if (this.changeSubIdList.length > 0) {
          let sublist = this.subList.filter((item) =>
            this.changeSubIdList.includes(item.id)
          );
          sublist.forEach((item) => {
            delete item.enable_time;
            delete item.disable_time;
            delete item.origin_material_channel_name;
            delete item.material_channel_name;
          });
          submitData.channelSubMaterialList.push(...sublist);
        }
        //如果修改了关联物料类别，则加传参数
        if (this.changeRelatedIdList.length > 0) {
          let relatedList = this.relatedList.filter((item) =>
            this.changeRelatedIdList.includes(item.id)
          );
          relatedList.forEach((item) => {
            delete item.enable_time;
            delete item.disable_time;
          });
          submitData.channelRelatedMaterialList.push(...relatedList);
        }
        //如果有新增的数据
        if (self.subList.some((item) => /^add\d*/.test(item.id))) {
          let addlist = self.subList.filter((item) => /^add\d*/.test(item.id));
          addlist.forEach((val) => {
            console.log(val);
            delete val.id;
            delete val.enable_time;
            delete val.disable_time;
          });
          console.log(addlist);
          submitData.channelSubMaterialList.push(...addlist);
        }
        this.isSaving = true;
        this.ajax.postStream(
          "/material-web/api/materialChannelChange/save",
          submitData,
          (res) => {
            if (res.body.result && res.body.content) {
              this.currentId = res.body.content;
              console.log("presave");
              "function" === typeof callback
                ? callback()
                : (function () {
                    self.refresh();
                    self.$message.success(res.body.msg);
                  })();
            } else {
              console.log("我在这！！！");
              this.$message.error(res.body.msg);
              self.refresh();
            }
            this.isSaving = false;
          },
          (err) => {
            this.isSaving = false;
            this.$message.error(err);
            this.refresh();
          }
        );
      });
    },
    submit() {
      this.presave(() => {
        console.log("submit");
        if (this.subPageTotal == 0) {
          this.$message.warning("未添加子项物料，不允许提交!");
          return;
        }
        if (
          this.subPageTotal > 0 &&
          this.subList.some((item) => !item.material_channel)
        ) {
          this.$message.warning("新渠道专供不能为空!");
          return;
        }
        this.changeSheetStatus("SUBMIT", "CHANNEL_CHANGE_SUBMIT_OR_RETRACT");
      });
    },
    // submit() {
    //   if(this.subPageTotal==0){
    //     this.$message.warning("未添加子项物料，不允许提交!")
    //     return
    //   }
    //   if(this.subPageTotal>0 && this.subList.some(item=>!item.material_channel)){
    //     this.$message.warning("新渠道专供不能为空!")
    //     return
    //   }
    //   this.changeSheetStatus("SUBMIT", "CHANNEL_CHANGE_SUBMIT_OR_RETRACT");
    // },
    retract() {
      this.changeSheetStatus("RETRACT", "CHANNEL_CHANGE_SUBMIT_OR_RETRACT");
    },
    aduit() {
      this.changeSheetStatus("AUDIT", "CHANNEL_CHANGE_AUDIT_OR_REJECT");
    },
    turnDown() {
      this.changeSheetStatus("REJECT", "CHANNEL_CHANGE_AUDIT_OR_REJECT");
    },
    valid() {
      console.log(this.submitData);
      let self = this;
      let startTime = this.submitData.enable_time;
      let endTime = this.submitData.disable_time;
      let nowTime = new Date().getTime();
      if (nowTime >= endTime) {
        this.$message.error(
          "变更申请单的【影响日期止】小于当前实施时间，申请单已失效，不允许实施申请单。"
        );
        return;
      }
      if (nowTime > startTime) {
        this.$confirm(
          "未在【影响日期起】开始前及时实施申请单，如果还要实施，【影响日期起】将改为明天零点开始。空窗的间隔时段物料将自动赋值为非专供商品。是否还要继续实施申请单？",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            self.changeSheetStatus(
              "NORMAL",
              "CHANNEL_CHANGE_NORMAL_OR_INVALID"
            );
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      } else {
        this.changeSheetStatus("NORMAL", "CHANNEL_CHANGE_NORMAL_OR_INVALID");
      }
    },
    invalid() {
      this.changeSheetStatus("INVALID", "CHANNEL_CHANGE_NORMAL_OR_INVALID");
    },
    changeSheetStatus(status, code) {
      let params = {
        id: this.currentId,
        status: status,
      };
      this.ajax.postStream(
        `/material-web/api/materialChannelChange/changeStatus?permissionCode=${code}`,
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            this.refresh();
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    addGoods() {
      let self = this;
      if (!this.submitData.enable_time || !this.submitData.disable_time) {
        this.$message.error("请先选择影响日期！");
        return;
      }
      this.$root.eventHandle.$emit("alert", {
        title: "子项物料添加",
        style: "width:900px;height:600px",
        component: () =>
          import("@components/designForChannelChange/selectGoodsList"),
        params: {
          callback: (list) => {
            let addList = [];
            let promiseList = [];
            list.forEach((d, index) => {
              promiseList[index] = self.getOriginChannelFor(d.materialNumber);
            });
            Promise.all([...promiseList])
              .then((values) => {
                if (values.length == list.length) {
                  addList = values.map((item, index) => {
                    let obj = {};
                    obj.id = "add" + new Date().getTime();
                    obj.material_number = list[index].materialNumber;
                    obj.material_name = list[index].materialName;
                    obj.material_desc = list[index].materialSpecification;
                    obj.origin_material_channel = item || "";
                    obj.material_channel = item || "";
                    obj.model = list[index].mainModel;
                    obj.material_group = list[index].groupNumber;
                    obj.enable_time = self.submitData.enable_time;
                    obj.disable_time = self.submitData.disable_time;
                    return obj;
                  });
                  self.subList = addList.concat(self.subList);
                } else {
                  self.$message.error("添加子项物料出错");
                }
              })
              .catch((err) => {
                self.$message.error(err);
              });
          },
        },
      });
    },
    deleteGoods() {
      if (this.multipleSelection.length <= 0) {
        this.$message.error("请选择要删除的物料！");
        return;
      }
      let deleteIdList = this.multipleSelection.map((item) => item.id);
      this.subList = this.subList.filter(
        (item) => !deleteIdList.includes(item.id)
      );
      let outOfAddIdlist = deleteIdList.filter((item) => {
        return !/^add\d*/.test(item);
      });
      this.submitData.delChannelSubMaterialListIds
        ? ""
        : (this.submitData.delChannelSubMaterialListIds = []);
      this.submitData.delChannelSubMaterialListIds.push(...outOfAddIdlist);
    },
    getOriginChannelFor(no) {
      let self = this;
      return new Promise((resolve, reject) => {
        this.ajax.get(
          `/material-web/api/materialChannelChange/material/get?materialNumber=${no}`,
          (res) => {
            console.log(res);
            if (res.body.result) {
              res.body.content
                ? resolve(res.body.content.channel)
                : resolve("N");
            } else {
              reject(res.body.msg);
            }
          },
          (err) => {
            reject(err);
          }
        );
      });
    },
    subListSelectChange(id) {
      if (!id || /^add\d*/.test(id)) {
        return;
      }
      let list = this.changeSubIdList;
      list.indexOf(id) == -1 && list.push(id);
      this.changeSubIdList = list;
      console.log(this.changeSubIdList);
    },
    RelatedListSelectChange(id) {
      if (!id || /^add\d*/.test(id)) {
        return;
      }
      let list = this.changeRelatedIdList;
      list.indexOf(id) == -1 && list.push(id);
      this.changeRelatedIdList = list;
      console.log(this.changeRelatedIdList);
    },
    subHandleSelectionChange(val) {
      this.multipleSelection = val;
    },
    beginDateSet() {
      let self = this;
      console.log(self);
      return {
        disabledDate(time) {
          if (self.submitData.disable_time) {
            return (
              time.getTime() >
                new Date(self.submitData.disable_time).getTime() ||
              time.getTime() < new Date().getTime()
            );
          } else {
            return time.getTime() < new Date().getTime();
          }
        },
      };
    },
    endDateSet() {
      let self = this;
      return {
        disabledDate(time) {
          if (self.submitData.enable_time) {
            return (
              time.getTime() <= new Date(self.submitData.enable_time).getTime()
            );
          } else {
            return time.getTime() < new Date().getTime();
          }
        },
      };
    },
    //设置附件上传参数
    setUploadDataObj() {
      this.uploadDataObj = {
        parent_name: "DESIGN_FOR_CHANNEL_CHANGE",
        parent_no: `QDZGBGSQ${this.currentId}`, //主要通过该参数获取附件列表
        child_name: null,
        child_no: null,
        content: {},
      };
    },
     //附件上传成功返回结果
    uploadSuccess1(result) {
      if (result.length > 0 && !!result[0].path) {
        this.uploadFile(result[0].path);
      }
    },
    // 上传附件按钮
    uploadFile(fileUrl) {
      console.log(fileUrl);
      let params = {
        file_path: fileUrl,
        id: this.currentId,
      };
      this.ajax.postStream(
        "/material-web/api/materialChannelChange/saveFile",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            this.refresh()
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 下载附件
    downloadFilePath(){
      window.open(this.fileDownloadPath)
    },
    //上传成功返回结果
    uploadSuccess(result) {
      if (result.length > 0 && !!result[0].path) {
        this.importSub(result[0].path);
      }
    },
    //模板下载
    downloadTemplate() {
      let url =
        "http://lsmy-devfile.oss-cn-shenzhen.aliyuncs.com/doc/2021-08-1801203e969acd4a33b0f603e47b861246.xlsx";
      let filename = "渠道专供变更单子项导入模板";
      this.download(url, filename);
    },
    //命名转换中文
    download(url, filename) {
      if (!fetch) {
        window.location.href = url;
        return;
      }
      return fetch(url).then((res) => {
        console.log(res);
        res.blob().then((blob) => {
          let a = document.createElement("a");
          let url = window.URL.createObjectURL(blob);
          a.href = url;
          a.download = filename;
          a.click();
          window.URL.revokeObjectURL(url);
        });
      });
    },
    //导入
    importSub(fileUrl) {
      console.log(fileUrl);
      let params = {
        filePath: fileUrl,
        id: this.currentId,
      };
      this.ajax.postStream(
        "/material-web/api/materialChannelChange/importSubMaterialLists",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    //导入结果
    showUploadResult() {
      this.$root.eventHandle.$emit("alert", {
        style: "width:900px;height:600px",
        title: "导入结果",
        params: {
          url: "/order-web/api/invoice/export/result/list",
          data: {
            excel_type: "EXCEL_TYPE_CHANNEL_SUB_MATERIAL_IMPORT",
          },
        },
        component: () => import("@components/common/eximport"),
      });
    },
    //子项物料列表相关接口
    subPresearch(list, resolve) {
      this.subSearch.where = list;
      this.subSearching(resolve);
    },
    subSearching(resolve) {
      let self = this;
      if (!self.currentId) {
        self.$message.error("请先保存！");
        return;
      }
      self.subSearch.id = this.currentId;
      this.ajax.postStream(
        "/material-web/api/materialChannelChange/labelSubMaterial/list",
        this.subSearch,
        (res) => {
          if (res.body.result) {
            self.subPageTotal = res.body.content.count;
            if (res.body.content.list) {
              self.subList = res.body.content.list;
            }
            // self.$message.success(res.body.msg);
          } else {
            self.subList = [];
            self.subPageTotal = 0;
            // self.$message.error(res.body.msg);
          }
          "function" === typeof resolve ? resolve() : this;
        },
        (err) => {
          self.$message.error(err);
          "function" === typeof resolve ? resolve() : this;
        }
      );
    },
    subSizeChange(size) {
      // 第页数改变
      this.subSearch.page_size = size;
      this.subSearching();
    },
    subPageChange(page_no) {
      // 页数改变
      this.subSearch.page_no = page_no;
      this.subSearching();
    },
    //关联物料列表相关接口
    relatedPresearch(list, resolve) {
      this.relatedSearch.where = list;
      this.relatedSearching(resolve);
    },
    relatedSearching(resolve) {
      let self = this;
      if (!self.currentId) {
        self.$message.error("请先保存！");
        return;
      }
      self.relatedSearch.id = this.currentId;
      this.ajax.postStream(
        "/material-web/api/materialChannelChange/labelRelatedMaterial/list",
        this.relatedSearch,
        (res) => {
          if (res.body.result) {
            self.relatedPageTotal = res.body.content.count;
            if (res.body.content.list) {
              self.relatedList = res.body.content.list;
            }
            // self.$message.success(res.body.msg);
          } else {
            self.relatedList = [];
            self.relatedPageTotal = 0;
            // self.$message.error(res.body.msg);
          }
          "function" === typeof resolve ? resolve() : this;
        },
        (err) => {
          self.$message.error(err);
          "function" === typeof resolve ? resolve() : this;
        }
      );
    },
    relatedSizeChange(size) {
      // 第页数改变
      this.relatedSearch.page_size = size;
      this.relatedSearching();
    },
    relatedPageChange(page_no) {
      // 页数改变
      this.relatedSearch.page_no = page_no;
      this.relatedSearching();
    },
    //关联物料导出
    exportRelatedExcel() {
      this.relatedBtns[0].loading = true;
      this.ajax.postStream(
        "/material-web/api/materialChannelChange/exportRelatedMaterial",
        this.relatedSearch,
        (res) => {
          if (res.body.result) {
            res.body.msg && this.$message.success(res.body.msg);
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
          this.relatedBtns[0].loading = false;
        },
        (err) => {
          this.relatedBtns[0].loading = false;
          this.$message.error(err);
        }
      );
    },
    //关联物料导出结果
    exportRelatedResult() {
      this.$root.eventHandle.$emit("alert", {
        style: "width:900px;height:600px",
        title: "导出结果",
        params: {
          url: "/file-iweb/api/cloud/excel/list",
          data: {
            list_excel_type: ["EXCEL_TYPE_CHANNEL_RELATED_MATERIAL_EXPORT"],
          },
        },
        component: () => import("@components/common/exout"),
      });
    },
    taggelClassName(row) {
      let number = row.material_number;
      let list = this.relatedList;
      let numberList = list.map((item) => item.material_number);
      let isRepeat =
        numberList.indexOf(number) != numberList.lastIndexOf(number);
      if (isRepeat) {
        return this.$style["same-number"];
      }
    },
    async getSubNewCategoryRights() {
      return true;
      let hasRights = await this.getRights("PRICE_LABEL_CHANGE_SUB_MATERIAL");
      return hasRights;
    },
    async getRelatedNewCategoryRights() {
      return true;
      let hasRights = await this.getRights(
        "PRICE_LABEL_CHANGE_RELATED_MATERIAL"
      );
      return hasRights;
    },
    getRights(type) {
      return new Promise((resolve, reject) => {
        this.ajax.get(
          `/material-web/api/materialChannelChange/checkPermission?permissionCode=${type}`,
          (res) => {
            resolve(res.body.result);
          },
          (err) => reject(err)
        );
      });
    },
  },
};
</script>
<style lang="stylus" scoped>
.xpt-flex__bottom {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 30px;

  .basic-info {
    height: 196px;
  }

  .item-material {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 196px;
    height: auto;
  }
}

.item-pane {
  display: flex;
  flex-direction: column;
  height: auto;
}

.mgt20 {
  margin-top: 20px;
}

.el-input {
  width: 144px;
}

.el-form-item {
  margin-bottom: 10px;
}

.right-search {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
<style module>
.same-number td:nth-child(2) {
  color: red;
}
</style>
<style>
#sub-list .xpt-top,
#related-list .xpt-top {
  padding: 2px 20px;
}

#sub-list .xpt-flex__bottom,
#related-list .xpt-flex__bottom {
  padding-bottom: 48px;
}
</style>