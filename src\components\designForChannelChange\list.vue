<!--渠道专供变更申请单列表-->
<template>
    <xpt-list-dynamic
        ref="list"
        :data="list"
        :btns="btns"
        :colData="cols"
        :selection="selection"
        :pageTotal="count"
        :searchPage="search.page_name"
        @search-click="presearch"
        @selection-change="handleSelectionChange"
        @page-size-change="sizeChange"
        @current-page-change="pageChange"
    ></xpt-list-dynamic>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            search: {
                page_name: "material_channel_change_application",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
                if_need_page: "Y",
            },
            list: [],
            selection: "checkbox",
            count: 0,
            multipleSelection: [], //列表选择索引
            btns: [
                {
                    type: "success",
                    txt: "刷新",
                    click: self.searching,
                    loading: false,
                },
                {
                    type: "warning",
                    txt: "创建",
                    click: self.createChangeRequest,
                    loading: false,
                },
                {
                    type: "danger",
                    txt: "审核",
                    click: () => {
                        self.changeSheetStatus("AUDIT","CHANNEL_CHANGE_AUDIT_OR_REJECT");
                    },
                    loading: false,
                    disabled: true
                },
            ],
            cols: [
                {
                    prop: "bill_no",
                    label: "单据编号",
                    width: 130,
                    redirectClick(row) {
                        console.log(row);
                        let params = {};
                        params.change_request_id = row.id;
                        self.$root.eventHandle.$emit("creatTab", {
                            name: "变更申请单详情",
                            params: params,
                            component: () =>
                                import(
                                    "@components/designForChannelChange/detail.vue"
                                ),
                        });
                    },
                },
                {
                    prop: "status",
                    label: "单据状态",
                    formatter(val) {
                        return (
                            {
                                CREATE:"创建",
                                SUBMIT:"待审核",
                                RETRACT:"撤回",
                                AUDIT:"待实施",
                                CHANGE:"待变更",
                                REJECT:"驳回",
                                NORMAL:"生效",
                                INVALID:"失效"
                            }[val] || val
                        );
                    },
                },
                {
                    prop: "creatorFullName",
                    label: "创建人",
                },
                {
                    prop: "create_time",
                    label: "创建时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "auditorFullName",
                    label: "审核人",
                },
                {
                    prop: "audit_time",
                    label: "审核时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "implementerFullName",
                    label: "实施人",
                },
                {
                    prop: "implement_time",
                    label: "实施时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "enable_time",
                    label: "影响日期起",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "disable_time",
                    label: "影响日期止",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "modifier",
                    label: "修改人",
                },
                {
                    prop: "modify_time",
                    label: "修改时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "submitterFullName",
                    label: "提交人",
                },
                {
                    prop: "submit_time",
                    label: "提交时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "rejecterFullName",
                    label: "驳回人",
                },
                {
                    prop: "reject_time",
                    label: "驳回时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "invalidPersonFullName",
                    label: "失效人",
                },
                {
                    prop: "invalid_time",
                    label: "失效时间",
                    format: "dataFormat1",
                    width: 130,
                },
            ],
        };
    },
    methods: {
        presearch(list, resolve) {
            this.search.where = list;
            this.searching(resolve);
        },
        searching(resolve) {
            let self = this;
            this.btns[0].loading = true;
            this.ajax.postStream(
                "/material-web/api/materialChannelChange/list",
                this.search,
                (res) => {
                    if (res.body.result) {
                        self.count = res.body.content.count;
                        if (res.body.content.list) {
                            self.list = res.body.content.list;
                        }
                        self.$message.success(res.body.msg);
                    } else {
                        self.list = [];
                        self.count = 0;
                        self.$message.error(res.body.msg);
                    }
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                },
                (err) => {
                    self.$message.error(err);
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                }
            );
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page_size = size;
            this.searching();
        },
        pageChange(page_no) {
            // 页数改变
            this.pageNow = page_no;
            this.search.page_no = page_no;
            this.searching();
        },
        createChangeRequest() {
            this.$root.eventHandle.$emit("creatTab", {
                name: "创建渠道专供变更申请单",
                params: {},
                component: () =>
                    import(
                        "@components/designForChannelChange/detail.vue"
                    ),
            });
        },
        changeSheetStatus(status,code) {
            if (this.multipleSelection.length <= 0) {
                this.$message.error("请至少选择一条数据！");
                return;
            }
            if (
                this.multipleSelection.some((item) => item.status != "SUBMIT")
            ) {
                this.$message.error("只有提交状态才能审核！");
                return;
            }
            let idlist = [];
            idlist = this.multipleSelection.map((item) => item.id);
            let params = {
                ids: idlist,
                status: status,
            };
            this.ajax.postStream(
                `/material-web/api/materialChannelChange/batchChangeStatus?permissionCode=${code}`,
                params,
                (res) => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg);
                        this.searching();
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
    },
    mounted() {
        let self = this;
        self.searching();
    },
    destroyed() {},
};
</script>
<style scoped>
</style>