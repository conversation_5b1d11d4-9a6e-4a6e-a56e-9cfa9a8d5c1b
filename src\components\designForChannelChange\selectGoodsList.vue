<!-- 选择子项物料 -->
<template>
	<xpt-list 
		:data='goodsList' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='pageTotal' 
		:searchPage='page_name' 
		:selection='selection' 
        isNeedClickEvent
		@search-click='searchData' 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@selection-change='selectionChange'
	></xpt-list>
</template>
<script>
export default {
	data(){
		let self = this
		return {
			cols: [
				{
					label: '物料编码',
					prop: 'materialNumber'
				}, {
					label: '物料名称',
					prop: 'materialName'
				}, {
					label: '商品规格',
					prop: 'materialSpecification'
				},{
					label: '主型号',
					prop: 'mainModel'
				}, {
					label: '物料分组',
					prop: 'groupName'
				}
			],
			btns: [{
				type: 'primary',
				txt: '确认',
				click: self.close
			}],
			search: {
				length: self.pageSize,
				pageNo: 1,
			},
			goodsList:[],
			selectGoodsList:[],
			pageTotal:0,
			page_name: 'price_label_change_sub_material_list',
            where: [],
            selection: 'checkbox'
		}
	},
	props:['params'],
	methods:{
		close(){
			if(this.selectGoodsList.length==0) {
				this.$message.error('请先选择物料');
				return;
			}
			if(this.selectGoodsList.length>10){
				this.$message.error('物料选择不允许超过10个');
				return;
			}
			this.params.callback(this.selectGoodsList)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		searchData(obj, resolve){
			this.where = obj
			this._getGoodsList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.length = pageSize
			this._getGoodsList();
		},
		pageChange(page){
			this.search.pageNo = page
			this._getGoodsList();
		},
		_getGoodsList(resolve){
			var self = this
			console.log(self.params)
			var data = {
				page: this.search,
                page_name: this.page_name,
                where: this.where,
                disableStatus: "A",
                audit_flag: "1",
				showPriceLabelChangeSubMaterial: true
			}
			this.ajax.postStream('/material-web/api/material/getMaterialList',data,d=>{
				if(d.body.result&&d.body.content){
					self.pageTotal = d.body.content.count;
					self.goodsList = d.body.content.list||[];
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve();
			})
		},
        selectionChange(row) {
            console.log(row)
			this.selectGoodsList=row;
        }
    },
    mounted() {
		this._getGoodsList();
    }
}
</script>