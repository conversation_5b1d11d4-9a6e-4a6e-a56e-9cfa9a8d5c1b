<!-- 智慧门店-摆场管理-设计方案列表 -->
<template>
  <div class='xpt-flex'>
    <el-row class="xpt-top">
      <el-button type="primary" size="mini" @click="refresh">刷新</el-button>
      <el-button type="primary" size="mini" @click="addData" :disabled="info.design_plan_status !== 'CREATE'">增行</el-button>
      <el-button type="primary" size="mini" @click="delData" :disabled="info.design_plan_status !== 'CREATE'">删行</el-button>
      <el-button type="primary" size="mini" @click="update" :loading="loading" :disabled="info.design_plan_status !== 'CREATE'">保存</el-button>
      <el-button type="primary" size="mini" @click="auditing" :disabled="info.design_plan_status !== 'CREATE'">审核</el-button>
      <el-button type="primary" size="mini" @click="undoAuditing" :disabled="info.design_plan_status!=='APPROVED'">反审核</el-button>
      <el-button type="primary" size="mini" @click="efficacy" :disabled="info.design_plan_status!=='APPROVED'">作废</el-button>
      <el-button type="primary" size="mini" @click="() => createOrder('DEALER_SHG_REPLENISH','INNER')" :disabled="info.design_plan_status!=='APPROVED'">生成经销补货订单</el-button>
      <el-button type="primary" size="mini" @click="() => createOrder('DEALER_SHG_REPLENISH','SUPPLIER')" :disabled="info.design_plan_status!=='APPROVED'">生成经销补货订单（供应商直发）</el-button>
      <el-button type="primary" size="mini" @click="() => createOrder('DEALER')" :disabled="info.design_plan_status!=='APPROVED'">生成摆场订单</el-button>
      <el-button type='primary' size='mini' @click="() => getPicturList(true)">方案图列表</el-button>
      <el-button type='primary' size='mini' @click="() => exportFile()">导出</el-button>
      <el-button type='primary' size='mini' @click="() => showExportList()">导出文件下载</el-button>
		</el-row>
    <div>
      <el-row :gutter='40' class="design-plan-info">
        <el-col :span='6'>
          <el-form label-position="right" label-width="110px">
            <el-form-item label='方案编号：'>{{info.design_plan_no}}</el-form-item>
            <el-form-item label='方案名称：'><p v-tooltip='info.swj_design_plan_name'>{{info.swj_design_plan_name}}</p></el-form-item>
            <el-form-item label='方案下推时间：'>{{dateFormat(info.swj_download_time)}}</el-form-item>
            <el-form-item label='摆场合并订单号：'>
              <span>{{ getMergeTradeNo('DEALER') }}</span>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span='6'>
          <el-form label-position='right' label-width='123px'>
            <el-form-item label='方案设计师：'>{{info.swj_design_name}}</el-form-item>
            <el-form-item label='设计方案审核人：'>{{info.swj_design_director_name}}</el-form-item>
            <el-form-item label='设计方案备注：'>{{info.swj_design_plan_remark}}</el-form-item>
            <el-form-item label='经销补货订单(内部)：'>
              <span>
                <span>{{ getMergeTradeNo('DEALER_SHG_REPLENISH','INNER') }}</span>
              </span>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span='6'>
          <el-form label-position='right' label-width='135px'>
            <el-form-item label='清单审核人：'>{{info.design_plan_audit_name}}</el-form-item>
            <el-form-item label='清单审核时间：'>{{dateFormat(info.design_plan_audit_time)}}</el-form-item>
            <el-form-item label='审核状态：'>{{statusFormat(info.design_plan_status)}}</el-form-item>
            <el-form-item label='经销补货订单(供应商)：'>
                <span>{{ getMergeTradeNo('DEALER_SHG_REPLENISH','SUPPLIER') }}</span>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span='6'>
          <el-form label-position='right' label-width='110px'>
            <el-form-item label='经销商店铺：'>
              <xpt-input
                v-if="info.design_plan_status=='CREATE'"
                v-model='info.shop_name'
                readonly
                icon='search'
                :on-icon-click='openShop'
                @change="openShopChange"
                size='mini'>
              </xpt-input>
              <span v-else>
                {{info.shop_name}}
              </span>
            </el-form-item>
            <el-form-item label='经销商备注：'>
              <xpt-input
                v-model='info.swj_dealer_remark'
                v-if="info.design_plan_status=='CREATE'"
                size='mini'>
              </xpt-input>
              <span v-else>
                {{info.swj_dealer_remark}}
              </span>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <el-tabs v-model="selectTab" @tab-click="selectTabClick">
        <el-tab-pane label='成品清单' name='WHOLE_INVENTORY'>
            <div class='xpt-flex design_plan_detail'>
                <xpt-headbar v-if="!isCreate">
                    <xpt-search-ex :page="search.page_name" :click="searchData" slot='right' style="margin-right: 50px"/>
                </xpt-headbar>
                <xpt-list
                    ref="query"
                    :showHead='false'
                    @search-click='searchData'
                    :data='list'
                    :colData='cols'
                    :pageTotal='count'
                    @selection-change='select'
                    @row-click='rowClick'
                    selection='checkbox'
                    @page-size-change='pageSizeChange'
                    @current-page-change='currentPageChange'>
                    <template slot='material_code' slot-scope='scope'>
                    <a style="color: blue;" v-if="!(info.design_plan_status == 'CREATE' && scope.row.if_have_order == 'N' && info.shop_name)"
                    @click="getPicturList(false, scope.row)">{{scope.row.material_code}}</a>
                    <el-input
                        v-else
                        size='mini'
                        icon='search'
                        :readonly='true'
                        :on-icon-click='selectGoods'
                        v-model='scope.row.material_code'
                        style='width: 100%'>
                    </el-input>
                    </template>
                    <template slot='material_num' slot-scope='scope' >
                    <el-input
                        :maxlength="3"
                        style="width:100px"
                        :disabled="true"
                        v-model="scope.row.material_num"
                        size='mini'
                        @keyup.native="scope.row.material_num=scope.row.material_num.replace(/^(0[1 ])|[^\d]+/g,'')">
                    </el-input>
                    </template>
                    <template slot='swj_material_area' slot-scope='scope' >
                    <p v-if="info.design_plan_status!='CREATE' || scope.row.if_have_order == 'Y' || !info.shop_name"
                        >{{scope.row.swj_material_area}}</p>
                    <el-input
                        v-else
                        size='mini'
                        icon='search'
                        :readonly='true'
                        :on-icon-click='selectShopArea'
                        v-model='scope.row.swj_material_area'
                        style='width: 100%'>
                    </el-input>
                    </template>
                </xpt-list>
            </div>
        </el-tab-pane>
        <el-tab-pane label='经销补货清单' name='REPLENISHMENT_INVENTORY'>
            <div class='xpt-flex design_plan_detail'>
                <xpt-headbar v-if="!isCreate">
                    <xpt-search-ex :page="search.page_name" :click="searchData" slot='right' style="margin-right: 50px"/>
                </xpt-headbar>
                <xpt-list
                    ref="query"
                    :showHead='false'
                    @search-click='searchData'
                    :data='list'
                    :colData='cols'
                    :pageTotal='count'
                    @selection-change='select'
                    @row-click='rowClick'
                    selection='checkbox'
                    @page-size-change='pageSizeChange'
                    @current-page-change='currentPageChange'>
                    <template slot='material_code' slot-scope='scope'>
                    <a style="color: blue;" v-if="!(info.design_plan_status == 'CREATE' && scope.row.if_have_order == 'N' && info.shop_name)"
                    @click="getPicturList(false, scope.row)">{{scope.row.material_code}}</a>
                    <el-input
                        v-else
                        size='mini'
                        icon='search'
                        :readonly='true'
                        :on-icon-click='selectGoods'
                        v-model='scope.row.material_code'
                        style='width: 100%'>
                    </el-input>
                    </template>
                    <template slot='material_num' slot-scope='scope' >
                    <el-input
                        :maxlength="3"
                        style="width:100px"
                        :disabled="true"
                        v-model="scope.row.material_num"
                        size='mini'
                        @keyup.native="scope.row.material_num=scope.row.material_num.replace(/^(0[1 ])|[^\d]+/g,'')">
                    </el-input>
                    </template>
                    <template slot='swj_material_area' slot-scope='scope' >
                    <p v-if="info.design_plan_status!='CREATE' || scope.row.if_have_order == 'Y' || !info.shop_name"
                        >{{scope.row.swj_material_area}}</p>
                    <el-input
                        v-else
                        size='mini'
                        icon='search'
                        :readonly='true'
                        :on-icon-click='selectShopArea'
                        v-model='scope.row.swj_material_area'
                        style='width: 100%'>
                    </el-input>
                    </template>
                </xpt-list>
            </div>
        </el-tab-pane>
    </el-tabs>
    <xpt-image
      :images="imagesList"
      :show="ifShow"
      :ifUpload="false"
      :ifClose="false"
      class="designPlanImg"
      @close="closeFun">
    </xpt-image>
  </div>
</template>

<script>
import Fn from '@common/Fn.js'
export default {
  props:['params'],
    components: {},
    data() {
    let self = this;
      return {
        imagesList: [],
        ifShow: false,
        ifClickUploadB: false,
        uploadDataB: null,
        list: [],
        listOld: [],
        info: {},
        selectTab:'WHOLE_INVENTORY',
        getListStatus:false,
        tradeList:[],
        count: 0,
        search: {
          page_name: 'scm_design_plan_detail',
          if_dealer_replenish:'N',
          where: [],
          page_size: 50,
          page_no: 1
        },
        selectData:{},//单行表数据
        selectRow: [],
        cols: [
          {
            label: '物料编码',
            slot: 'material_code',
          },
          {
            label: '物料名称',
            prop: 'material_name',
          },
          {
            label: '物料规格',
            prop: 'material_specification',
          },
          {
            label: '物料分组',
            prop: 'group_number',
            formatter: (val) => {
              let groupNumberObj = {
                'XSP': '小商品',
                '01': '成品',
                'CP': '成品'
              }
              return groupNumberObj[val] || ''
            }
          },
          {
            label: '供货来源',
            prop: 'source_of_supply',
            formatter: (val) => {
              let options = {
                'SUPPLIER': '供应商',
                'INNER': '内部',
              }
              return options[val] || ''
            }
          },
          {
            label: '是否启用经销补货',
            prop: 'if_dealer_replenish',
            formatter: (val) => {
              let options = {
                'Y': '是',
                'N': '否',
              }
              return options[val] || ''
            }
          },
          {
            label: '商品数量',
            slot: 'material_num',
            width: 120,
          },
          {
            label: '摆场区域',
            slot: 'swj_material_area',
          },
          {
            label: '区域编码',
            prop: 'swj_material_area_no',
          },
          {
            label: '方案页位置',
            prop: 'swj_material_space',
            formatter: function formatter(val, idx, row) {
              if (row.swj_material_page && row.swj_material_space) {
                return row.swj_material_page + row.swj_material_space
              } else {
                return ''
              }
            }
          },
          {
            label: '来源',
            width: 120,
            prop: 'swj_material_source',
            formatter: function formatter(val) {
              switch (val) {
                case '1':
                  return '来源方案';
                  break;
                case '2':
                  return '额外添加';
                  break;
                default:
                  return val;
              }
            }
          },
          {
            label: '是否下单',
            width: 80,
            prop: 'if_have_order',
            formatter(val) {
              if(val == 'N') {
                return '否'
              }
              if(val == 'Y') {
                return '是'
              }
              return val;
            }
          }
        ],
        deleteMaterialVOs:[], //删除列表
        shop_name:'',
        shop_id: '',
        isCreate: false, // 是否是新增页面
        loading: false // 保存longding状态控制
      };
    },
  methods: {
    exportFile() {
        if (!this.params.id) return
          this.ajax.postStream('/order-web/api/scmDesignPlan/exportExcel', {
            design_plan_id: this.params.id,
            ...this.search
          }, res => {
            if (res.body.result) {
              this.$message.success(res.body.msg);
            } else {
              this.$message.error(res.body.msg);
            }
          });
        },
        showExportList (){
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/after_sales_report/export'),
            style:'width:900px;height:600px',
            title: '报表导出列表',
            params: {
              query: {
              type: 'SCMDESIGNPLAN_RECEIPT',
              },
            },
          })
        },
      getMergeTradeNo(tradeType,sourceOfSupply,isId){
            if(!this.tradeList.length) return ''
            let i = this.tradeList.findIndex(item => item.tradeType === tradeType && item.sourceOfSupply == sourceOfSupply)
            if(i === -1) return ''
            return isId ? this.tradeList[i].mergeTradeId : this.tradeList[i].mergeTradeNo
      },
      selectTabClick(e){
        this.search = {
          page_name: 'scm_design_plan_detail',
          if_dealer_replenish: e.name === 'WHOLE_INVENTORY' ? 'N' : 'Y',
          where: [],
          page_size: 50,
          page_no: 1
        }
        this.count = 0
        this.deleteMaterialVOs = []
        this.getListStatus = true
        this.getList();
      },
      closeFun() {
        this.ifShow = false;
      },
      isImage(str) {
          var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
          return reg.test(str);
      },
      getPicturList (header, row) {
        let data = {
          order_no: this.info.design_plan_no
        }
        header ? data.if_parent = true : data.sub_order_no = row.design_plan_detail_id
        this.ajax.postStream('/file-iweb/api/cloud/file/list',data,res=>{
          if(res.body.result){
            let filelist = (res.body.content && res.body.content.list) || []
            let imglist = [];
            //过滤出只有图片的列表
            let list = filelist.filter((item) => this.isImage(item.path));
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = value.path;
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = value.creator_nick;
                obj.isPucture = true;
                imglist.push(obj);
            });
            this.imagesList = imglist
            if (this.imagesList.length > 0) {
              this.ifShow = true;
            } else {
              this.$message.info('未查找到对应的图')
            }
          } else {
            this.$message.error(res.body.msg)
          }
        })
      },
      pageSizeChange(ps) {
        if(this.getListStatus) return
        this.search.page_size = ps;
        this.getList();
      },
      currentPageChange(page) {
        if(this.getListStatus) return
        this.search.page_no = page;
        this.getList();
      },
      // 获取列表
      getList(val,resolve) {
        this.$request('/order-web/api/scmDesignPlan/detailSearchList', {
            design_plan_id: val? val: this.params.id,
          ...this.search
          },
        ).then(res =>{
          if (res.result) {
            this.list = res.content.list || [];
            this.listOld = JSON.parse(JSON.stringify(this.list));
            this.count = res.content.count;
          } else {
            this.$message.error(res.msg);
          }
          resolve && resolve();
        }).catch(err=>{
          resolve && resolve();
        }).finally(()=>{
          resolve && resolve();
          setTimeout(()=>{
            this.getListStatus = false
          },300)
          this.loading = false;
        });
      },
      // 选择店铺
      openShop() {
        console.log('点击=====');
        let self = this;
        this.$root.eventHandle.$emit('alert', {
          params: {
            callback: (d) => {
              self.info.shop_name = d.shop_name;
              self.info.shop_id = d.shop_id;
            },
            selection: 'radio'
          },
          component: () => import('@components/shop/list'),
          style: 'width:800px;height:500px',
          title: '店铺列表',
        })
      },
      openShopChange(){
        this.info.shop_name = ''
        this.info.shop_id = ''
      },
      refresh() {
        if (this.params.id) {
          this.getList();
          this.getDataHead();
        }
      },
      rowClick(row) {
        this.selectData = row;
      },
      // 选择物料
      selectGoods(){
        let self = this;
        let params = {};
        params.callback = d => {
          if(d) {
            self.selectData.material_code = d.materialNumber;
            self.selectData.material_name = d.materialName;
            self.selectData.material_specification = d.materialSpecification
            self.selectData.group_number = d.groupNumber
            // self.list[self.list.length-1] = self.selectData
          }
        };
        this.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/order/selectGoodsList'),
          style: 'width:800px;height:500px',
          title: '商品列表'
        })
      },
      // 获取表头
      getDataHead(id) {
        let design_plan_id = id ? id : this.params.id
        if (design_plan_id) {
          this.$request('/order-web/api/scmDesignPlan/detail', {
              design_plan_id: design_plan_id
            },
          ).then(res =>{
            if (res.result) {
              this.info = res.content.scmDesignPlan;
              this.tradeList = res.content.tradeList
            }
          }).catch(err=>{
          });
        }
      },
      // 选择摆场区域
      selectShopArea() {
        let self = this;
        this.$root.eventHandle.$emit('alert', {
            params: {
              callback: (d) => {
                self.info.shop_name = d.shop_name;
                self.selectData.swj_material_area = d.area_name;
                self.selectData.swj_material_area_no = d.shop_area;
              },
              shop_name: self.info.shop_name
            },
          component: () => import('@components/shop_regional_distribu/selectShopDistribution'),
          style: 'width:800px;height:500px',
          title: '店铺区域分布列表'
        })
      },
      compareInfo() {
        let goodsOld = this.listOld,
          goodsNow = this.list,
          updateList = [],
          i;

        // 获取新增的商品、赠品
        i = goodsNow.length;
        while (i--) {
          if (goodsNow[i].design_plan_detail_id) {
            // 获取更新行数据，status过滤掉取消状态的数据,换货和取消的
            let j = goodsOld.length;
            while (j--) {
              if (goodsNow[i].design_plan_detail_id === goodsOld[j].design_plan_detail_id) { 
                let isUpd = this.compareData({
                  material_code: goodsNow[i].material_code,
                  swj_material_area_no: goodsNow[i].swj_material_area_no,
                  deleted: goodsNow[i].deleted,
                }, {
                  material_code: goodsOld[i].material_code,
                  swj_material_area_no: goodsOld[i].swj_material_area_no,
                  deleted: goodsOld[i].deleted,
                });
                if (isUpd) {
                  updateList.push(goodsNow[i])
                }
              }
            }
          } else if (goodsNow[i].tempId) {
            delete goodsNow[i].tempId
            updateList.unshift(goodsNow[i])
          }
			}
			return updateList
		},
      // 编辑
      update() {
        this.loading = true
        let list = this.compareInfo()
        this.$axios(
          'post',
          '/order-web/api/scmDesignPlan/save?permissionCode=DESIGN_PLAN_SAVE',
          {
            scmDesignPlan: {
              design_plan_id: this.info.design_plan_id,
              shop_id: this.info.shop_id,
              shop_name: this.info.shop_name,
              swj_dealer_remark: this.info.swj_dealer_remark
            },
            detailList: list.concat(this.deleteMaterialVOs)
          }
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.getDataHead();
            this.getList();
            this.deleteMaterialVOs = [];
          } else {
            this.$message.error(res.msg)
            this.loading = false
          }
        }).catch(err => {
          this.$message.error(err)
          this.loading = false
        }).finally(()=>{
        })
      },
      createOrder(type,source_of_supply) {
          this.$axios('post',
          '/order-web/api/scmDesignPlan/generateTrade?permissionCode=DESIGN_PLAN_GENERATE',
          {
            design_plan_id: this.params.id,
            type,
            source_of_supply
          }
        ).then(res => {
          if (res.result) {
            this.tradeList = res.content.tradeList
            this.$message.success(res.msg)
            let params = {};
            params.merge_trade_id = this.getMergeTradeNo(type,source_of_supply,true)
            params.mergeTradeIdList = []
            params.ifOrder = false
            // 呼叫按钮来源
            params.callBtnfromType = 'sys_order'
            console.log(params,'paramsparamsparams=====');
            this.$root.eventHandle.$emit('creatTab',{
            name:"合并订单详情",
            params,
            component: () => import('@components/order/merge.vue')
            });
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      // 失效
      efficacy() {
        this.$axios('post',
          '/order-web/api/scmDesignPlan/disable?permissionCode=DESIGN_PLAN_DISABLE',
          {
            design_plan_id: this.params.id
          }
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg)
            this.getDataHead();
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      // 审核
      auditing() {
        this.$axios('post',
          '/order-web/api/scmDesignPlan/audit?permissionCode=DESIGN_PLAN_AUDIT',
          {
            design_plan_id: this.params.id
          }
        ).then(res => {
          if (res.result) {
            this.getDataHead();
            this.getList();
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      // 反审核
      undoAuditing () {
        this.$axios('post',
          '/order-web/api/scmDesignPlan/antiAudit?permissionCode=DESIGN_PLAN_ANIT_AUDIT',
          {
            design_plan_id: this.params.id
          }
        ).then(res => {
          if (res.result) {
            this.getDataHead();
            this.getList();
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      // 日期转换
      dateFormat(val) {
        return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss');
      },
      // 状态转换
      statusFormat(val) {
        switch (val) {
          case 'CREATE':
            return '创建';
            break;
          case 'APPROVED':
            return '已审核';
            break;
          case 'CANCELED':
            return '已作废';
            break;
          default:
            return val;
        }
      },
      searchData(obj, resolve) {
        this.search.where=obj;
        this.getList(null,resolve)
      },
      /**
       *新增行
       */
      addData(){
        if (!this.info.shop_name) {
          this.$message.info('请先选择店铺')
          return false;
        }
        let data = this.getDetailData();
        // data.shop_id = this.shop.shop_id;
        this.list.push(data);
      },
      // 多选列表
      select(s){
        this.selectRow = s;
      },
      /**
       *删除行
       */
      delData(){
        if (!this.info.shop_name) {
          this.$message.info('请先选择店铺')
          return false;
        }
        let self = this
        this.selectRow.forEach( (good, idx) => {
					if (good.design_plan_detail_id) {
              good.deleted = true
              this.deleteMaterialVOs.push(good)
              let list = self.list
              let len = list.length
              while(len--) {
                  if (list[len].material_code === good.material_code && list[len].design_plan_detail_id === good.design_plan_detail_id) {
                    self.list.splice(len, 1)
                  }
              }
					} else if (good.tempId){
            let list = self.list
            let len = list.length
            while(len--) {
                if (list[len].material_code === good.material_code && list[len].tempId === good.tempId) {
                    self.list.splice(len, 1)
                }
            }
					}
				})
      },
      /**
       *新增明细行的数据
       */
      getDetailData(){
        return {
          material_code: '',
          material_name: '',
          material_specification: '',
          material_num: 1,
          swj_material_area_no: '',
          swj_material_area: '',
          if_have_order: 'N',
          tempId: +new Date() + 0,
          design_plan_id: this.info.design_plan_id,
          group_number: ''
        }
      },
    },
    created() {
      if (this.params.id) {
        this.getList();
        this.getDataHead(this.params.id);
      }
    },
  }
</script>
<style>
  .design-plan-info {
    height: 14%;
  }
  #xpt-image.designPlanImg {
    width: 815px !important;
    height: 70% !important;
    top: 15% !important;
    left: 15% !important;
    background: transparent !important;
  }
  #xpt-image.designPlanImg .xpt-image__body .xpt-image__main {
    bottom: 126px;
  }
  .design_plan_detail {
    height: 77% !important;
  }
</style>
