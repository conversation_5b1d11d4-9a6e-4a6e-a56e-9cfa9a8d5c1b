<!--经销商账号列表-->
<template>
	<xpt-list
		:data='listData'
		:btns='btns'
		:colData='cols'
		selection=''
		:pageTotal='count'
		searchPage='scm_design_plan'
		@search-click='presearch'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
	></xpt-list>
</template>
<script>
export default {
	props:["params"],
	data(){
		let self = this
		return {
			listData: [],
			count: 0,
			search:{
				page_name:"scm_design_plan",
				where:[],
				page_size:self.pageSize,     //页数
				page_no:1   //页码
			},
			btns: [
				{
					type: 'success',
					txt: '刷新',
					loading: false,
					click() {
						self.searching()
					}
				}
			],
			cols:[
				{
					label: '方案编号',
					prop: 'design_plan_no',
					width: 200,
					redirectClick(row) {
						self.$root.eventHandle.$emit('creatTab', {
						name: "设计清单详情",
						params: {id: row.design_plan_id},
						component:()=>import('@components/design_plan/detail')
						});
					},
				},{
					label: '方案名称',
					prop: 'swj_design_plan_name',
				},{
					label: '方案设计师',
					prop: 'swj_design_name',
				},{
					label: '方案下推时间',
					prop: 'swj_download_time',
					format:'dataFormat1'
				},{
					label: '设计方案审核人',
					prop: 'swj_design_director_name',
				},{
					label: '设计方案备注',
					prop: 'swj_design_plan_remark',
				},{
					label: '清单审核时间',
					prop: 'design_plan_audit_time',
					format:'dataFormat1'
				},{
					label: '清单审核人',
					prop: 'design_plan_audit_name'
				},{
					label: '状态',
					prop: 'design_plan_status',
					formatter(val){
						switch(val){
							case 'CREATE': return '创建';
							case 'CANCELED': return '已作废';
							case 'APPROVED': return '已审核';
						}
					}
				},{
					label: '经销商备注',
					prop: 'swj_dealer_remark',
				},{
					label: "经销商店铺",
					prop: "shop_name"
				}
			]
		}
	},
	methods:{
		preValid(api){
			var _this = this;
			var url = "/order-web/api/customer"+api;
			// 事件前验证
			if(_this.multipleSelection.length==0){
				_this.$alert('没有选择任何数据，请先选择数据！', '提示', {
					confirmButtonText: '确定'
				})
			}else{
				var custIdList = [];
				_this.multipleSelection.forEach(function(item,index,array){
					custIdList.push(item.cust_id);
				});

				this.ajax.postStream(url,custIdList,function(response){
					if(response.body.result){
						_this.searching();
						_this.$message({
									message: '操作成功',
										type: 'success'
								});
						// 重置业务操作
						_this.actions_value = "";
					}
				});
			}
		},
		sizeChange(size){
			// 第页数改变
			this.search.page_size = size;
			this.searching();
		},
		pageChange(page_no){
			// 页数改变
			this.pageNow = page_no;
			this.search.page_no = page_no;
			this.searching();
		},
		presearch(list, resolve){
			this.search.where = list;
			this.searching(resolve);
		},
		searching(resolve){
			let self = this;
			this.ajax.postStream('/order-web/api/scmDesignPlan/list',this.search,function(response){
				if(response.body.result){
				self.listData= response.body.content.list || [];
					self.count = response.body.content.count;
				}else{
					self.listData = [];
					self.count = 0 ;
					self.$message.error(response.body.msg || '');
				}
				resolve && resolve()
			}, err => {
				this.$message.error(err);
				resolve && resolve()
			}, this.params.tabName);
		},
	},
	mounted: function(){
	},
	destroyed(){
	}
}
</script>
