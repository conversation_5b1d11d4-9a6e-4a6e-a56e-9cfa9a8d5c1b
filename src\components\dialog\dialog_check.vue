
<template>
	<div>
		<el-row class='xpt-top'>
			
		</el-row>
		<el-row :gutter='40' style="margin:40px">
			<el-col :span='12'>
				是否审核通过？
			</el-col>
			
		</el-row>
		<el-row :gutter='40' style="margin-top:60px;">
			<el-col :span='8'>
				<el-form label-position="right" label-width="80px">
					<el-button @click="backFun" type="warning">驳回重审</el-button>
				</el-form>
			</el-col>
			<el-col :span='8' :offset="8">
				<el-form label-position="right" label-width="80px">
					<el-button @click="passFun" type="success">审核通过</el-button>
				</el-form>
			</el-col>
		</el-row>
	</div>
</template>
<script>
	
	export default {
		props:['params'],
		data(){
			return {
				
			}
		},
		methods:{
			backFun(){
				var _this=this;
				var url = "/report-web/api/report/forecast/rejectForecast";
				var data={forecastNo:this.params.forecastNo};
				this.ajax.postStream(url,data,function(result){
					if(result.body.result){
						_this.$message.success('驳回成功');
						_this.$root.eventHandle.$emit('check',{val:"B"});
						_this.$root.eventHandle.$emit('removeAlert',_this.params.alertId);
					}else{
						_this.$message.error(result.body.msg);
					}
				},function(result){
					
					_this.$message.error(result.statusText);
				});
			},
			passFun(){
				var _this=this;
				var url = "/report-web/api/report/forecast/reviewForecast";
				var data={forecastNo:this.params.forecastNo};
				this.ajax.postStream(url,data,function(result){
					if(result.body.result){
						_this.$message.success('审核成功');
						_this.$root.eventHandle.$emit('check',{val:'B'});
						_this.$root.eventHandle.$emit('removeAlert',_this.params.alertId);
					}else{
						_this.$message.error(result.body.msg);
					}
				},function(result){
					
					_this.$message.error(result.statusText);
				});
			}
		},
		mounted:function(){
			var _this = this;
				
			console.log(this.params);
		}
	}
</script>