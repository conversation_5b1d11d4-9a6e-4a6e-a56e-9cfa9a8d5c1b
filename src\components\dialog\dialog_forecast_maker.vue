
<template>
	<div>
		<el-row class='xpt-top'>
			
		</el-row>
		<el-row :gutter='40'>
			<el-col :span='8'>
				<el-form label-position="right" label-width="80px">
					<el-form-item label="职员姓名">
						<el-input v-model="search.name" size='small'></el-input>
					</el-form-item>
					<el-form-item label="职员编码">
						<el-input v-model="search.code" size='small'></el-input>
					</el-form-item>
				</el-form>
			</el-col>
			<el-col :span='8'>
				<el-form label-position="right" label-width="80px">
					<el-form-item label="部门">
						<el-input v-model="search.department" size='small'></el-input>
					</el-form-item>
					
				</el-form>
			</el-col>
			<el-col :span='3' :offset="3">
				<el-form label-position="right" label-width="80px">
					<el-button type="primary" size="small" @click="searchFun" style="width:100%;margin:4px 0px">查询</el-button>
					<el-button type="primary" size="small" @click="resetFun" style="width:100%;margin:4px 0px 0px;">重置参数</el-button>
				</el-form>
			</el-col>
		</el-row>
		<div class="list-tab">
		  <el-table
		    :data="tableData"
		    border
		    stripe
		    max-height="350" 
		    width='100%' 
		    style="width: 100%"
		    @current-change="currentChange">
		    <el-table-column
		      type="index"
		      width="100">
		    </el-table-column>
		    <el-table-column
		      prop="parent_material_name"
		      label="职员名称"
		      width="200">
		    </el-table-column>
		    <el-table-column
		      prop="parent_material_no"
		      label="职员编码"
		      width="200">
		    </el-table-column>
		    <el-table-column
		      prop="parent_oos_number"
		      label="部门"
		      header-align="left"
		      align="right">
		    </el-table-column>
		  </el-table>
		</div>
		<div class="page">
			<el-pagination
		      @size-change="handleSizeChange"
		      @current-change="handleCurrentChange"
		      :current-page="currentPage"
		      :page-sizes="[20, 40, 60, 80 ,100]"
		      :page-size="pageSize"
		      layout="total, sizes, prev, pager, next, jumper"
		      :total="totalPage">
		    </el-pagination>
		</div>
	</div>
</template>
<script>
	export default {
		data(){
			return {
				search:{
					name:'',
					code:'',
					department:''
				},
				currentPage:1,
				pageSize:20,
				totalPage:100,
				activeName:'first',
				tableData:[]
			}
		},
		methods:{
			searchFun:function(){

			},
			resetFun:function(){

			},
			handleSizeChange:function(){

			},
			handleCurrentChange:function(){

			},
			currentChange:function(){

			}
		}
	}
</script>