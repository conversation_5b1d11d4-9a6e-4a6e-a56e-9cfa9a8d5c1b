
<template>
	<div class="xpt-flex">
		<el-row class='xpt-top' :gutter="40">
			<el-col :span="22">
				 <el-button type="warning" size="mini"  @click="cancelFun">取消</el-button>
			</el-col>
			<el-col :span="2" class="xpt-align__right">
				<el-button type="text" size="mini" @click="dialogShowMoreGoosFun">{{isDialogShowMoreSupplier?'更多筛选条件':'精简筛选条件'}}<i :class="isDialogShowMoreSupplier?'el-icon-arrow-down':'el-icon-arrow-up'"></i></el-button>
				<!-- <el-button type="text" size="mini" @click="dialogShowMoreGoosFun" v-else>更多筛选条件<i class="el-icon-arrow-down"></i></el-button> -->

			</el-col>
		</el-row>
		<el-row :gutter='40' v-if="!isDialogShowMoreSupplier">
			<el-col :span='8'>
				<el-form label-position="right" label-width="80px">
					<el-form-item label="编码">
						<el-input v-model="search.code" size='mini'></el-input>
					</el-form-item>
					<el-form-item label="规格描述">
						<el-input v-model="search.skuDes" size='mini'></el-input>
					</el-form-item>
					<el-form-item label="存货类别">
						<el-input v-model="search.class" size='mini'></el-input>
					</el-form-item>
				</el-form>
			</el-col>
			<el-col :span='8'>
				<el-form label-position="right" label-width="80px">
					<el-form-item label="名称">
						<el-input v-model="search.name" size='mini'></el-input>
					</el-form-item>
					
				</el-form>
				<el-form label-position="right" label-width="80px">
					<el-form-item label="物料分类">
						<el-input v-model="search.materialClass" size='mini'></el-input>
					</el-form-item>
					
				</el-form>
			</el-col>
			<el-col :span='3' :offset="3">
				<el-form label-position="right" label-width="80px">
					<el-button type="primary" size="mini" @click="searchFun" style="width:100%;margin:4px 0px">查询</el-button>
					<el-button type="primary" size="mini" @click="resetFun" style="width:100%;margin:4px 0px 0px;">重置参数</el-button>
				</el-form>
			</el-col>
		</el-row>
		<div class="xpt-flex__bottom" style="margin-bottom:20px">
		  <el-table
		    :data="tableData"
		    border
		    stripe
		    highlight-current-row
		    style="width: 100%" 
		    width='100%' 
		    @current-change="currentChange">
		    <el-table-column
		      type="index"
		      width="100">
		    </el-table-column>
		    <el-table-column
		      prop="fmaterialNumber"
		      label="编码"
		      width="200">
		    </el-table-column>
		    <el-table-column
		      prop="fmaterialName"
		      label="名称"
		      width="200">
		    </el-table-column>
		     <el-table-column
		      prop="fspecification"
		      label="规格描述"
		      show-overflow-tooltip
		      width="200">
		    </el-table-column>
		     <el-table-column
		      prop="fcategoryName"
		      label="物料分类"
		      width="200">
		    </el-table-column>
		    <el-table-column
		      prop="fgroupName"
		      label="存货类别">
		    </el-table-column>
		  </el-table>
		</div>
		
		<el-row style="margin-top:50px" class="xpt-pagation">
			<el-col :span="24">
				<el-pagination
			      @size-change="handleSizeChange"
			      @current-change="handleCurrentChange"
			      :current-page="currentPage"
			      :page-sizes="[20, 40, 60, 80 ,100]"
			      :page-size="pageSize"
			      layout="total, sizes, prev, pager, next, jumper"
			      :total="totalPage">
			    </el-pagination>
			</el-col>
			
		</el-row>
	</div>
</template>
<script>
	
	export default {
		props:['params'],
		data(){
			return {
				search:{
					name:'',
					code:'',
					class:'',
					skuDes:'',
					materialClass:''
				},
				currentPage:1,
				pageSize:20,
				totalPage:0,
				activeName:'first',
				isSelect:false,
				name:'',
				fmaterialId:'',
				code :'',
				sku :'',
				tableData:[],
				isDialogShowMoreSupplier:true,
			}
		},
		methods:{
			searchFun:function(){
				this.DataSearchFun();
			},
			DataSearchFun:function(){
				var _this = this;
				var url="/report-web/api/report/material/list";
				var data={
					page_size:this.pageSize?this.pageSize:20,
					page_no:this.currentPage?this.currentPage:1,
					material_number:this.search.code,
					material_name:this.search.name,
					specification:this.search.skuDes,
					group_name:this.search.materialClass,
					category_name:this.search.class,
				};
				this.ajax.postStream(url,data,function(result){

						if(result.body.content){
							_this.tableData=result.body.content.list;
							_this.totalPage=result.body.content.count;
							
						}
						
					},function(result){
						_this.tableData=[];
							_thistotalPage=0;
						_this.$message.error(result.statusText);
					});
				
				
			},
			resetFun:function(){
				this.search.code='';
				this.search.name='';
				this.search.skuDes='';
				this.search.materialClass='';
				this.search.class='';
			},
			handleSizeChange:function(val){
				//val 为每页的条数
				this.pageSize=val;	
				this.DataSearchFun();
			},
			handleCurrentChange:function(val){
				//val为当前页数
				this.currentPage=val;
				this.DataSearchFun();
			},
			currentChange:function(currentRow){
				this.params.close({name:currentRow.fmaterialName,code:currentRow.fmaterialNumber,sku:currentRow.fspecification,id:currentRow.fmaterialId});

			},
			cancelFun:function(){
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
			},
			dialogShowMoreGoosFun(){
				this.isDialogShowMoreSupplier=!this.isDialogShowMoreSupplier;
			},
		},
		mounted:function(){
			var _this = this;
			this.DataSearchFun();
				this.isDisabledM=true;			
			
			
			
		}
	}
</script>