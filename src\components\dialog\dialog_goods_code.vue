<template>
	<!-- <el-dialog title="商品编码" v-model="dialogVisibleGoodsCode" size="large" v-if="dialogIsShow"> -->
  		<div class="xpt-flex">
				<el-row :gutter="40" class="xpt-top">
					<el-col :span="22" >
						<el-button @click="backGoodsFun" type="warning" size="mini">返 回</el-button>
						<!-- <el-button type="success" @click="okGoodsCodeFun" size="mini">确 定</el-button> -->
					</el-col>
					<el-col :span="5" :offset="14" v-if="false">
						<el-input  placeholder="请输入预测单号或者编制人" icon="search" v-model="dialogSearchInputGoods" :on-icon-click="dialogSearchGoodsFun" size="mini"></el-input>
					</el-col>
					<el-col :span="2" class="xpt-align__right">
						<el-button type="text" size="mini" @click="dialogShowMoreGoosFun" v-if="isDialogShowMoreGoods">精简筛选条件<i class="el-icon-arrow-up"></i></el-button>
						<el-button type="text" size="mini" @click="dialogShowMoreGoosFun" v-else>更多筛选条件<i class="el-icon-arrow-down"></i></el-button>

					</el-col>
				</el-row>
			
			<div class="connent-search" v-if="isDialogShowMoreGoods">
				<el-row gutter="10" style="text-align:right;margin-bottom:10px" >
					<el-col :span="2">编码</el-col>
					<el-col :span="5">
						<el-input v-model="dialogCode" size="mini"></el-input>
					</el-col>
					<el-col :span="2">名称</el-col>
					<el-col :span="5">
						<el-input v-model="dialogName" size="mini"></el-input>
					</el-col>
					<el-col :span="2">规格描述</el-col>
					<el-col :span="5">
						<el-input v-model="dialogSkuDes" size="mini"></el-input>
					</el-col>
					<el-col :span="2" :offset="1">
						<el-button  @click="dialogSearchBoxGoodsFun" size="mini" type="success" style="width:80px;">查询</el-button>
					</el-col>
				</el-row>
				<el-row gutter="10" style="text-align:right;margin-bottom:10px">
					<el-col :span="2">物料分组</el-col>
					<el-col :span="5">
						<el-input v-model="dialogMaterialGroup" size="mini"></el-input>
					</el-col>
					<el-col :span="2">存货类别</el-col>
					<el-col :span="5">
						<el-input v-model="dialogInventoryCategory" size="mini"></el-input>
					</el-col>
					<el-col :span="2" :offset="8">
						<el-button  @click="dialogResetBoxGoodFun" size="mini" type="primary" style="width:80px;">重置条件</el-button>
					</el-col>
				</el-row>
			</div>
			<div class="xpt-flex__bottom scroll" style="margin-bottom:50px">
			  <el-table
			    :data="dialogTableDataGoods"
			    border
			    stripe
			    highlight-current-row
			    tooltip-effect="dark" 
			    width='100%' 
			    @current-change="dialoghandleCurrentChangeGoods">
			    <el-table-column
			      type="index"
			      width="50">
			    </el-table-column>
			    <el-table-column
			      prop="fmaterialNumber"
			      label="编码"
			      width="180">
			    </el-table-column>
			    <el-table-column
			      prop="fmaterialName"
			      label="名称"
			      width="180">
			    </el-table-column>
			    <el-table-column
			      prop="fspecification"
			      label="规格描述"
			      show-overflow-tooltip
			      width="180">
			    </el-table-column>
			    <el-table-column
			      prop="fgroupName"
			      label="物料分组"
			      width="180">
			    </el-table-column>
			    <el-table-column
			      prop="fcategoryName"
			      label="存货类别">
			    </el-table-column>
			  </el-table>
			</div>
			<div class="xpt-pagation">
				<el-pagination
			      @size-change="dialogGoodsHandleSizeChange"
			      @current-change="dialogGoodsHandleCurrentChange"
			      :current-page="dialogGoodsCurrentPage"
			      :page-sizes="[20, 40, 60, 80,100]"
			      :page-size="dialogGoodsPageSize"
			      layout="total, sizes, prev, pager, next, jumper"
			      :total="dialogGoodsTotalPage">
			    </el-pagination>
			</div>
		</div>
		   <!--  <span slot="footer" class="dialog-footer">
		    	<el-button @click="dialogVisibleGoodsCode = false">取 消</el-button>
		    	<el-button type="primary" @click="okGoodsCodeFun">确 定</el-button>
		    </span> -->
	<!-- </el-dialog> -->
</template>
<script>
	// import '@stylus/public.styl'
	
	export default{
		props:['params'],

		data(){
			return{
		        //商品编码弹框的变量
			

		        dialogVisibleGoodsCode:false,
		        isDialogShowMoreGoods:false,
		        dialogGoodsPageSize:20,
				dialogGoodsTotalPage:0,
				dialogGoodsCurrentPage:1,
		        dialogCode:'',
		        dialogName:'',
		        clickRow:false,
		        isCode1:false,
		        dialogSkuDes:'',
		        dialogMaterialGroup:'',
		        dialogInventoryCategory:'',
		        dialogSearchInputGoods:'',
		        dialogTableDataGoods:[],

			}
		},
		methods:{
			
			dialogShowMoreGoosFun:function(){
				this.isDialogShowMoreGoods=!this.isDialogShowMoreGoods;
			},
			dialogSearchBoxGoodsFun:function(){
				this.dialogGoodsSearchListFun();
			},
			dialogGoodsSearchListFun:function(){
				var _this = this;
				var url = "/report-web/api/report/material/list";
				var data = {
					page_size:this.dialogGoodsPageSize?this.dialogGoodsPageSize:20,
					page_no:this.dialogGoodsCurrentPage?this.dialogGoodsCurrentPage:1,

					material_number:this.dialogCode,
					material_name:this.dialogName,
					specification:this.dialogSkuDes,
					group_name:this.dialogMaterialGroup,
					category_name:this.dialogInventoryCategory
				};
				this.ajax.postStream(url,data,function(result){
					if(result.body){
						_this.dialogTableDataGoods=result.body.content.list;
						_this.dialogGoodsTotalPage=result.body.content.count;
					}
				},function(result){
					_this.dialogTableDataGoods=[];
					_this.dialogGoodsTotalPage=0;
					this.$message.error(result.statusText);
				});

			},
			dialogResetBoxGoodFun:function(){
				this.dialogCode='';
		        this.dialogName='';
		        this.dialogSkuDes='';
		        this.dialogMaterialGroup='';
		        this.dialogInventoryCategory='';
			},
			dialogGoodsHandleSizeChange:function(val){
				//val 为每页的条数
				this.dialogGoodsPageSize = val;
				this.dialogGoodsSearchListFun();
			},
			dialogGoodsHandleCurrentChange:function(val){
				//val为当前页数
				this.dialogGoodsCurrentPage=val;
				this.dialogGoodsSearchListFun();
			},
			dialoghandleCurrentChangeGoods:function(currentRow){
				this.clickRow=true;
				if(this.params.id=="A"){
					this.params.close({goodscode:currentRow.fmaterialNumber});
				}else{
					this.params.close({goodscode:currentRow.fmaterialNumber});
				}
				
			},
			backGoodsFun:function(){
				// this.dialogVisibleGoodsCode = false;
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
			},
			dialogSearchGoodsFun:function(){

			}
		},
		mounted(){
			var _this = this;
			this.dialogGoodsSearchListFun();
		},
		
	}
</script>