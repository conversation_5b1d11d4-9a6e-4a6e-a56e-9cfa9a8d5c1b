<!-- 搜索用的 -->
<template>
	<div class="xpt-flex">
		<el-row	class='xpt-top'	:gutter='40'>
			<el-col :span='16'>
				<el-button type='warning' size='mini' @click='close'>确认</el-button>
			</el-col>
			<el-col :span='8' class='xpt-align__right'>
				<el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="searchString" :on-icon-click="searchData">
				</el-input>
			</el-col>
		</el-row>
		<el-row class="xpt-flex__bottom mgb20" >
			<el-table :data="list" border tooltip-effect="dark" width='100%' style="width: 100%" highlight-current-row @current-change="handleCurrentChange">
			    <el-table-column width="55" align='center' >
					<template slot-scope='scope'>
						<el-radio-group v-model="selectCode">
						    <el-radio :label="scope.row.key" class='xpt-table__radio'></el-radio>
						</el-radio-group>
					</template>
			   	</el-table-column>
			    <el-table-column prop="fullName" label="编码" ></el-table-column>
			    <el-table-column prop="nickName" label="名称" ></el-table-column>
			    <el-table-column prop="employeeNumber" label="描述" ></el-table-column>
		  	</el-table>
		</el-row>
	  	<el-row class='xpt-pagation'>
		  	<el-pagination @size-change="pageSizeChange" @current-change="pageChange"
			  	:current-page="pageNow" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
			  	layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
			</el-pagination>
		</el-row>
	</div>
</template>
<script>

export default {
	data(){
		return {
			searchString:'',
			list:[],
			selectCode:'',
			pageNow:1,
			pageTotal:0,
			pageSize:10,
			selectObj:null
		}
	},
	props:['params'],
	methods:{
		handleCurrentChange(data){
			if(data){
				this.selectCode=data.key;
				this.selectObj = data;
			}
			
		},
		close(){

			// let data = this.list.find(d=>d.index === this.index)
			// this.$root.eventHandle.$emit('mergeSelectGoods',data)
			if(this.selectObj === undefined || this.selectObj === null){
				this.$message.error('请先选择一行');
				return;
			}
			this.params.callback(this.selectObj);
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		searchData(){
			this._getlist();
		},
		pageSizeChange(pageSize){
			this.pageSize = pageSize;
			this._getlist();
		},
		pageChange(page){
			this.pageNow = page
			this._getlist();
		},
		_getlist(){
			let self = this,
			data = {
				page: {
					length: self.pageSize,	
    				pageNo: self.pageNow
				},		
				key: self.searchString,//	搜索条件		
			};
			this.ajax.postStream(this.params.url,data,(res => {
				if(res.body.result){

					this.list = res.body.content.list;
					this.list.map( v => {
						v.key = new Date().getTime() + Math.random()* 100;
					})
				}
			}))
		}
	},
	mounted() {
		this._getlist();
	}
}
</script>