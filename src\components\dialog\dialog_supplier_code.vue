<template>
	<!-- <el-dialog title="供应商编码" v-model="dialogVisibleSupplierCode" size="large" v-if="dialogIsShow"> -->
  		<div class="xpt-flex">
				<el-row :gutter="40" class="xpt-top">
					<el-col :span="22">
						<el-button @click="backGoodsFun" size="mini" type="warning">返回</el-button>
						<!-- <el-button type="success" size="mini" @click="okSupplierCodeFun">确 定</el-button> -->
					</el-col>
					<el-col :span="5" :offset="14" v-if="false">
						<el-input  placeholder="请输入预测单号或者编制人" icon="search" v-model="dialogSearchInputGoods" :on-icon-click="dialogSearchGoodsFun" size="mini"></el-input>
					</el-col>
					<el-col :span="2" class="xpt-align__right">
						<el-button type="text" size="mini" @click="dialogShowMoreGoosFun" v-if="isDialogShowMoreSupplier">精简筛选条件<i class="el-icon-arrow-up"></i></el-button>
						<el-button type="text" size="mini" @click="dialogShowMoreGoosFun" v-else>更多筛选条件<i class="el-icon-arrow-down"></i></el-button>

					</el-col>
				</el-row>
		
			<div class="connent-search" v-if="isDialogShowMoreSupplier">
				<el-row gutter="10" style="text-align:right;margin-bottom:10px">
					<el-col :span="2">供应商编码</el-col>
					<el-col :span="5">
						<el-input v-model="dialogSupplierCode" size="mini"></el-input>
					</el-col>
					
					<el-col :span="2" :offset="15">
						<el-button  @click="dialogSearchBoxSupplierFun" size="mini" type="success" style="width:80px;">查询</el-button>
					</el-col>
				</el-row>
				<el-row gutter="10" style="text-align:right;margin-bottom:10px">
					<el-col :span="2">供应商名称</el-col>
					<el-col :span="5">
						<el-input v-model="dialogSupplierName" size="mini"></el-input>
					</el-col>
					
					<el-col :span="2" :offset="15">
						<el-button  @click="dialogResetBoxSupplierFun" size="mini" type="primary" style="width:80px;">重置条件</el-button>
					</el-col>
				</el-row>
			</div>
			<div class="xpt-flex__bottom scroll" style="margin-bottom:50px">
			  <el-table
			    :data="dialogTableDataSupplier"
			    border
			    stripe
			    highlight-current-row
			    tooltip-effect="dark"
			    style="width: 100%" 
			    width='100%' 
			    @current-change="dialoghandleCurrentChangeSupplier">
			    <el-table-column
			      type="index"
			      width="50">
			    </el-table-column>
			    <el-table-column
			      prop="supplier_no"
			      label="供应商编码"
			      width="480">
			    </el-table-column>
			    <el-table-column
			      prop="supplier_name"
			      label="供应商名称">
			    </el-table-column>
			    
			
			  </el-table>
			</div>
			<div class="xpt-pagation">
				<el-pagination
			      @size-change="handleSizeChange"
			      @current-change="handleCurrentChange"
			      :current-page="currentPage"
			      :page-sizes="[20, 40, 80, 100]"
			      :page-size="pageSize"
			      layout="total, sizes, prev, pager, next, jumper"
			      :total="totalPage">
			    </el-pagination>
			</div>
			</div>
		   <!--  <span slot="footer" class="dialog-footer">
		    	<el-button @click="dialogVisibleSupplierCode = false">取 消</el-button>
		    	<el-button type="primary" @click="okSupplierCodeFun">确 定</el-button>
		    </span> -->
	</el-dialog>
</template>
<script>
	import '@stylus/public.styl'
	
	export default{
		props:['params'],

		data(){
			return{
		        //商品编码弹框的变量
			
		        pageSize:20,
				totalPage:0,
				currentPage:1,
				clickRow:false,
		        isCode1:true,
		         isDialogShowMoreSupplier:false,
		        dialogVisibleSupplierCode:false,
		        dialogSupplierCode:'',
		        dialogSupplierName:'',	
		        dialogTableDataSupplier:[]

			}
		},
		
		methods:{
			
			dialogShowMoreGoosFun:function(){
				this.isDialogShowMoreSupplier=!this.isDialogShowMoreSupplier;
			},
			dialogSearchBoxSupplierFun:function(){
				this.dialogGoodsSearchListFun();
			},
			dialogGoodsSearchListFun:function(){
				var _this = this;
				var url = "/report-web/api/report/supplier/list";
				var data = {
					page_size:this.pageSize?this.pageSize:20,
					page_no:this.currentPage?this.currentPage:1,

					supplier_no:this.dialogSupplierCode,
					supplier_name:this.dialogSupplierName,
				};
				this.ajax.postStream(url,data,function(result){
					if(result.body){
						_this.dialogTableDataSupplier=result.body.content.list;
						_this.totalPage=result.body.content.count;
					}
				},function(result){
					_this.dialogTableDataSupplier=[];
					_this.totalPage=0;
					this.$message.error(result.statusText);
				});

			},
			dialogResetBoxGoodFun:function(){
				this.dialogCode='';
		        this.dialogName='';
		        this.dialogSkuDes='';
		        this.dialogMaterialGroup='';
		        this.dialogInventoryCategory='';
			},
			handleSizeChange:function(val){
				//val 为每页的条数
				this.pageSize = val;
				this.dialogGoodsSearchListFun();
			},
			handleCurrentChange:function(val){
				//val为当前页数
				this.currentPage=val;
				this.dialogGoodsSearchListFun();
			},
			dialoghandleCurrentChangeSupplier:function(currentRow){
				this.clickRow=true;
				if(this.params.id=="A"){
					this.params.close({supplierCode:currentRow.supplier_no});
					// this.$root.eventHandle.$emit('supplierCode1',{supplierCode:currentRow.supplier_no});
				}else{
					this.params.close({supplierCode:currentRow.supplier_no});
					// this.$root.eventHandle.$emit('supplierCode2',{supplierCode:currentRow.supplier_no});
				}
				
			},
			okSupplierCodeFun:function(){
				if(this.clickRow){
					this.dialogVisibleSupplierCode = false;
				}else{
					this.$message('请选择供应商');
				}
				
			},
			backGoodsFun:function(){
				// this.dialogVisibleSupplierCode = false;
			    this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
			},
			dialogSearchGoodsFun:function(){

			}
		},
		mounted(){
			this.dialogGoodsSearchListFun();
		}
		
	}
</script>