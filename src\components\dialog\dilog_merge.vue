<template>
<el-dialog
  title=""
  :visible.sync="dialogVisible"
  size="tiny">
  <span v-text="showinfo">{{showinfo}}</span>
  <span v-text="showinfo2"></span>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
  </span>
</el-dialog>
</template>
<script>
  export default {
    props:['params'],
    data() {
      var _this = this;
      return {
          showinfo:_this.params.materia_number,
        showinfo2:"ddd",
        dialogVisible: true
      };
    },
    methods: {
    }
  };
</script>
