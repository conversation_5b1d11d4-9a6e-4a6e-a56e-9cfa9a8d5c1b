<!-- 营销优惠活动区间详情、新增 -->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button type="primary" size="mini" :disabled="globalDisabled" @click="save">保存</el-button>
        <el-button size='mini' type='success' @click='add'>新增</el-button>
        <el-button size='mini' type='warning' @click='search'>刷新</el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-tabs v-model="firstTab">
          <el-tab-pane label='基本信息' name='discountDetail' class='xpt-flex' style="overflow:hidden;">
            <el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="110px">
              <el-row>
                <el-col :span="7">
                  <el-form-item label="活动名称" prop="time_zone_name">
                    <el-input v-model="form.time_zone_name" size='mini' :disabled="globalDisabled"></el-input>
                    <el-tooltip v-if='rules.time_zone_name[0].isShow' class="item" effect="dark" :content="rules.time_zone_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                      <i class='el-icon-warning'></i>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item label="是否全局">
                    <el-switch size="mini" v-model="form.if_grobal" :disabled="(Array.isArray(form.shops) && form.shops.length > 0) || globalDisabled" on-text="是" off-text="否"></el-switch>
                  </el-form-item>
                  <el-form-item label="是否生效">
                    <el-switch size="mini" v-model="form.if_effective" :disabled="globalDisabled" on-text="是" off-text="否"></el-switch>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="开始时间" prop='time_zone_start'>
                    <el-date-picker size="mini" v-model="form.time_zone_start" type="date" :editable="false" :disabled="globalDisabled" :picker-options='beginDateOptions'></el-date-picker>
                    <el-tooltip v-if='rules.time_zone_start[0].isShow' class="item" effect="dark" :content="rules.time_zone_start[0].message" placement="right-start" popper-class='xpt-form__error'>
                      <i class='el-icon-warning'></i>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item label="创建人">
                    <el-input v-model="form.creatorName" size='mini' disabled></el-input>
                  </el-form-item>
                  <el-form-item label="创建时间">
                    <el-date-picker size="mini" v-model="form.create_time" type="datetime" :disabled="true"></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="结束时间" prop='time_zone_end'>
                    <el-date-picker size="mini" v-model="form.time_zone_end" type="date" :editable="false" :disabled="globalDisabled" :picker-options='endDateOptions'></el-date-picker>
                    <el-tooltip v-if='rules.time_zone_end[0].isShow' class="item" effect="dark" :content="rules.time_zone_end[0].message" placement="right-start" popper-class='xpt-form__error'>
                      <i class='el-icon-warning'></i>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item label="最后操作人">
                    <el-input v-model="form.modifierName" size='mini' disabled></el-input>
                  </el-form-item>
                  <el-form-item label="最后操作时间">
                    <el-date-picker size="mini" v-model="form.modify_time" type="datetime" :disabled="true"></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="备注" prop='remark' style="height: 60px; padding-top: 5px">
                    <el-input v-model="form.remark" size='mini' type="textarea" :autosize="{minRows: 2, maxRows: 2}" :disabled="globalDisabled" style="width: 90%"></el-input>
                    <el-tooltip v-if='rules.remark[0].isShow' class="item" effect="dark" :content="rules.remark[0].message" placement="right-start" popper-class='xpt-form__error'>
                      <i class='el-icon-warning'></i>
                    </el-tooltip>
                  </el-form-item>
                </el-col>

              </el-row>
            </el-form>
            <el-form>
              <el-col :span="7">
                  <el-form-item label="核销优惠线下收款上限" label-width="180px"  >
                    <el-input v-model="form.verify_discount_offline_amount" size='mini' type="number" :disabled="globalDisabled" style="width: 90%" @change="e=>{
									const result = fitterPrice(form.verify_discount_offline_amount) ? form.verify_discount_offline_amount:'';
									$nextTick(() =>{
										$set(form, 'verify_discount_offline_amount',result)
									});

								}"></el-input>

                  </el-form-item>
                </el-col>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <el-row class='xpt-flex__bottom'>
      <el-tabs v-model="secondTab">
        <el-tab-pane label='店铺信息' name="shopList" class='xpt-flex'>
          <div style="width: 488px; position: absolute; right: 0; z-index: 100; margin-top: 2px">
            <el-select size='mini' v-model='query.col' @change="selectColsChange" style='width: 120px'>
              <el-option v-for='(item,index) in selectCols' :key='index' :label='item.label' :value='item.prop'></el-option>
            </el-select>
            <el-select size='mini' v-model='query.ope' style='width: 80px'>
              <el-option v-for='(item, index) in opes' :key='index' :label='item.label' :value='item.key' :disabled='item.disabled'></el-option>
            </el-select>
            <el-select size='mini' v-model='query.value' style='width: 120px' v-if="query.col === selectCols[2].prop">
              <el-option v-for='(item,index) in yesOrNo' :key='index' :label='item.label' :value='item.prop'></el-option>
            </el-select>
            <el-input size='mini' v-model='query.value' v-else></el-input>
            <el-button size='mini' type='success' @click='filterData'>过滤</el-button>
            <el-button size='mini' type='primary' @click='reset'>重置</el-button>
          </div>
          <xpt-list
            ref='shopList'
            :data='pageList'
            :btns='shopBtns'
            :colData='shopCols'
            :pageTotal="totalPage"
            @page-size-change='handleSizeChange'
            @current-page-change='handleCurrentChange'
          ></xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>
<script>
  export default {
    props: ['params'],
    // mixins: [goods, shop, setting],
    data() {
      var self = this;
      const now = new Date();
            const currentYear = now.getFullYear();
            const currentMonth = now.getMonth();
            const firstDayOfCurrentMonth = new Date(currentYear, currentMonth, 1);
      return {
        beginDateOptions: {
          // 开始时间不允许小于当前月份
          disabledDate(time) {
            if(self.form.time_zone_end) {
              return time < firstDayOfCurrentMonth || time > self.form.time_zone_end;
            } else {
              return time < firstDayOfCurrentMonth;
            }
          }
        },
        endDateOptions: {
          disabledDate(time) {
            if(self.form.time_zone_start) {
              return time < firstDayOfCurrentMonth ||time < self.form.time_zone_start;
            } else {
              return time < firstDayOfCurrentMonth;
            }
          }
        },
        firstTab: 'discountDetail',
        secondTab: 'shopList',
        globalDisabled: false,
        form: {
          /*// 活动ID
          id: '',*/
          // 活动名称
          time_zone_name: '',
          verify_discount_offline_amount:'',
          // 活动开始时间
          time_zone_start: '',
          // 活动结束时间
          time_zone_end: '',
          // 是否全局
          if_grobal: false,
          // 是否生效
          if_effective: true,
          // 创建时间
          create_time: '',
          // 创建人
          creatorName: '',
          // 最后操作时间
          modify_time: '',
          // 最后操作人
          modifierName: '',
          // 备注
          remark: '',
          // 店铺信息
          shops: []
        },
        shopBtns: [{
          type: 'primary',
          txt: '添加',
          click: self.addShop,
          disabled() {
            if (self.globalDisabled) {
              return true;
            }
            return self.form.if_grobal;
          }
        }, {
          type: 'danger',
          txt: '删除',
          click: self.deleteShop,
          disabled() {
            return self.globalDisabled;
          }
        }, {
          type: 'info',
          txt: '失效',
          click: self.invalidShop,
          disabled() {
            return self.globalDisabled;
          }
        }],
        totalPage:0,
        // pageNow: 1,
        pageSize: 50,
        filterList: [], // 过滤后商店列表
        pageList: [], // 分页后的商店列表
        shopCols: [
          {
            label: '店铺编码',
            prop: 'shop_code'
          },
          {
            label: '店铺名称',
            prop: 'shop_name'
          }, {
            label: '是否生效',
            prop: 'if_effective',
            format: 'yesOrNo'
          }, {
            label: '创建人',
            prop: 'creatorName'
          }, {
            label: '创建时间',
            prop: 'create_time',
            format: 'dataFormat1',
            width: 140
          }, {
            label: '最后操作人',
            prop: 'modifierName'
          }, {
            label: '最后操作时间',
            prop: 'modify_time',
            format: 'dataFormat1',
            width: 140
          }
        ],
        rules: {
          'time_zone_name': [{required: true, trigger: 'blur', isShow: false, message: '', validator(rule, value, callback) {
              if(!value){
                self.rules[rule.field][0].isShow = true;
                self.rules[rule.field][0].message = '请输入活动名称';
                callback(new Error(''));
                return;
              }
              if(/\s/.test(value)){
                self.rules[rule.field][0].isShow = true;
                self.rules[rule.field][0].message = '不能包含空白字符';
                callback(new Error(''));
                return;
              }
              if(value.length > 95) {
                self.rules[rule.field][0].isShow = true;
                self.rules[rule.field][0].message = '长度不能超过95个字符';
                callback(new Error(''));
                return;
              }
              self.rules[rule.field][0].isShow = false;
              callback();
            }}],
          'time_zone_start': [{required: true, trigger: 'blur', isShow: false, message: '开始时间不能为空，且小于结束时间', validator(rule, value, callback) {
              console.log(value);
              if(!value){
                self.rules[rule.field][0].isShow = true;
                callback(new Error(''));
              }else {
                self.rules[rule.field][0].isShow = false;
                callback();
              }
            }}],
          'time_zone_end': [{required: true, trigger: 'blur', isShow: false, message: '结束时间不能为空，且大于开始时间', validator(rule, value, callback) {
              if(!value){
                self.rules[rule.field][0].isShow = true;
                callback(new Error(''));
              }else {
                self.rules[rule.field][0].isShow = false;
                callback();
              }
            }}],
          'remark': [{required: false, trigger: 'blur', isShow: false, message: '', validator(rule, value, callback) {
              if(/\s/.test(value)){
                self.rules[rule.field][0].isShow = true;
                self.rules[rule.field][0].message = '不能包含空白字符';
                callback(new Error(''));
                return;
              }
              if(value.length > 190) {
                self.rules[rule.field][0].isShow = true;
                self.rules[rule.field][0].message = '长度不能超过190个字符';
                callback(new Error(''));
                return;
              }
              self.rules[rule.field][0].isShow = false;
              callback();
            }}],
        },
        query: {
          col: '',
          ope: 0,
          value: ''
        },
        // 可过滤的字段
        selectCols: [
          {
            label: '店铺名称',
            prop: 'shop_name'
          },
          {
            label: '店铺编码',
            prop: 'shop_code'
          },
          {
            label: '是否生效',
            prop: 'if_effective'
          }
        ],
        // 过滤规则， 如果选中是否生效，则只能用等于
        opes: [
          {
            label: '包含',
            key: 0,
            disabled: false
          }, {
            label: '等于',
            key: 1,
            disabled: false
          }
        ],
        // 是否生效的数组
        yesOrNo: [
          {
            label: '是',
            prop: true
          },
          {
            label: '否',
            prop: false
          }
        ],
        autoDiscountSwitchTime: ''
      }
    },
    methods: {
      fitterPrice(num){
			var reg = /^(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/;
			if(!reg.test(num)){
			this.$message.error('当前输入不符合规范，请输入1-7位小数点后两位数字')
			}
			return reg.test(num)
		},
      getAutoDiscountSwitchTime() {
      this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryDataList', {
						categoryCode:"SALE_ORDER"
      }, res => {
        let result = ''
        if(res.body.result){
          let arr= res.body.content.list
          if( arr.length > 0 ){
            arr.forEach(item => {
              if (item.code === 'AUTO_DISCOUNT_SWITCH_TIME' && item.status == 1) {
                let ifDisabled = item.disableTime ? item.disableTime > new Date().getTime() : true
                let ifEnabled = item.enableTime ? item.enableTime < new Date().getTime() : true
                if (ifDisabled || ifEnabled) {
                  result = item.tag ? item.tag : false
                }
              }
            })
          }
        }
        this.autoDiscountSwitchTime = result
      }, err => {
        this.autoDiscountSwitchTime = ''
      })
    },
    // changeForm () {
    //   var str='2020-10-20 12:00:00'
    // new Date(str.replace(/-/g,'/')).getTime()
    // //当前时间戳
    // new Date().getTime()
    // },
      // 保存
      save() {
        const self = this;
        let flag = false;
        self.$refs.form.validate(valid=>{
          flag = valid;
        });
        if (!flag) {
          return;
        }
        // 控制结束时间不能大于开始时间
        let startTime = self.form.time_zone_start;
        if (startTime instanceof Date) {
          startTime = startTime.getTime();
        }
        let endTime = self.form.time_zone_end;
        if (endTime instanceof Date) {
          endTime.setHours(23);
          endTime.setMinutes(59);
          endTime.setSeconds(59);
          endTime.setMilliseconds(0);
          endTime = endTime.getTime();
        }
        if (startTime >= endTime) {
          self.rules.time_zone_start[0].isShow = true;
          self.rules.time_zone_end[0].isShow = true;
          return;
        }
        // 由于控件原因，只能用1和0
        // 重新将1和0转成true和false
        /* if (self.form.if_grobal) {
          self.form.if_grobal = true;
        } else {
          self.form.if_grobal = false;
        }
        if (self.form.if_effective) {
          self.form.if_effective = true;
        } else {
          self.form.if_effective = false;
        } */
        if (!!this.autoDiscountSwitchTime) { // XPT-23841
          let switchTime = new Date(this.autoDiscountSwitchTime).getTime()
          if (this.form.time_zone_start < switchTime && (this.form.time_zone_end > switchTime || new Date(this.form.time_zone_end).getTime() == switchTime)) {
            let msg = `不能创建跨“${this.autoDiscountSwitchTime}”的营销优惠区间`
            this.$message.error(msg);
            return false;
          }
        }
        self.ajax.postStream('/order-web/api/scmSaleDiscountSection/saveScmSaleDiscountSectionData?permissionCode=SALE_DISCOUNT_SECTION_SAVE', self.form, (res) => {
          console.log(res);
          if(res.body.result){
            self.form.id = res.body.content.id;
            this.$message.success(res.body.msg || '保存成功');
            self.search();
          }else{
            self.$message.error(res.body.msg || '保存失败');
          }
        }, (err) => {
          self.$message.error(err);
        });
      },
      // 新增
      add() {
        this.$root.eventHandle.$emit('creatTab',{name:'新建营销优惠活动区间',component:()=>import('@components/discount/MarketingDiscountSectionDetail.vue')});
      },
      // 查询详情
      search() {
        const self = this;
        let id;
        if (self.params && self.params.id) {
          id = self.params.id;
        }
        if (self.form.id) {
          id = self.form.id;
        }
        if (!id) {
          return;
        }
        // 查询详情
        self.ajax.postStream('/order-web/api/scmSaleDiscountSection/getScmSaleDiscountSectionVOById', id, (res) => {
          if(res.body.result){
            self.globalDisabled = !res.body.content.if_effective;
            self.form = res.body.content;
            self.filterData();
          }else{
            self.$message.error(res.body.msg || '');
            self.globalDisabled = true;
          }
        }, (err) => {
          self.$message.error(err);
          self.globalDisabled = true;
        });
      },
      refresh() {
      },
      addShop() {
        var self = this;
        var params = {
          shop_status: 'OPEN',
          callback(shops){
            // 清样订单取店铺仓库
            /*if(self.submitData.business_type_trade == 'SAMPLE') {
              if(!d.shop_warehouse_id) {
                self.$message.error('当业务类型为清样订单时，所选店铺必须设置店铺仓库。');
                return;
              }
              // self.submitData.shop_id = d.shop_warehouse_id;
              // self.submitData.shop_name = d.shop_warehouse_name;
              // self.submitData.source_shop_id = d.shop_warehouse_id;
            }*/
            /*self.submitData.shop_id = d.shop_id;
            self.submitData.shop_name = d.shop_name;
            self.submitData.source_shop_id = d.source_shop_id;*/
            let hintFlag = false;
            for (const item of shops) {
            // 去重，不能重复选择
              let flag = false;
              for (const _item of self.form.shops) {
                if (item.shop_id === _item.shop_id) {
                  flag = true;
                  hintFlag = true;
                  break;
                }
              }
              if (flag) {
                continue;
              }
              const shop = {
                shop_id: item.shop_id,
                shop_name: item.shop_name,
                shop_code: item.shop_code,
                if_effective: true
              };
              if (self.form.id) {
                shop.sale_discount_id = self.form.id;
              }
              self.form.shops.unshift(shop);
            }
            if (hintFlag) {
              self.$message.error('重复选择的店铺已被过滤');
            }
            self.filterData();
          }
        }
        self.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/shop/list'),
          style:'width:800px;height:500px',
          title:'店铺列表'
        })
      },
      invalidShop() {
        const arr = this.$refs.shopList.getSelectRows();
        if (arr.length < 1) {
          return;
        }
        let flag = false;
        for(let i = 0; i < arr.length; i++) {
          let item = arr[i];
          if (!item.id) {
            flag = true;
            continue;
          }
          item.if_effective = false;
          // this.$forceUpdate();
          // console.log(this);
          // this.$set(item,'if_effective', false);
        }
        if (flag) {
          this.$message.error('未保存的店铺不能失效，只能删除');
        }
        this.filterData();
        // this.$refs.shopList.updateTable();
        // console.log(this.$refs.shopList.getListData());
      },
      deleteShop() {
        const arr = this.$refs.shopList.getSelectRows();
        if (arr.length < 1) {
          return;
        }
        let flag = false;
        for (let i = this.form.shops.length - 1; i > -1; i--) {
          const item = this.form.shops[i];
          if (item.id) {
            flag = true;
            continue;
          }
          for (let j = 0; j < arr.length; j++) {
            if (item.shop_id === arr[j].shop_id) {
              this.form.shops.splice(i, 1);
              break;
            }
          }
        }
        if (flag) {
          this.$message.error('已保存的店铺不能删除，只能失效');
        }
        this.filterData();
      },
      // 过滤数据
      filterData() {
        this.filterList = [];
        if (!Array.isArray(this.form.shops)) {
          return;
        }
        for (let i = 0; i < this.form.shops.length; i++) {
          const shop = this.form.shops[i];
          // 如果是清空了选项，就返回整个列表
          if (this.query.col == '') {
            this.filterList.push(this.form.shops[i]);
            continue;
          }
          switch (this.query.ope) {
            case this.opes[0].key:
              if (shop[this.query.col].indexOf(this.query.value) === -1) {
                continue;
              }
              break;
            case this.opes[1].key:
              if (shop[this.query.col] !== this.query.value) {
                continue;
              }
              break;
            default:
              break;
          }
          this.filterList.push(this.form.shops[i]);
        }
        this.totalPage = this.filterList.length;
        this.handleCurrentChange(1);
      },
      // 重置
      reset() {
        this.query = {
          col: '',
          ope: 0,
          value: ''
        };
        this.filterData();
      },
      selectColsChange(val) {
        if (val === this.selectCols[2].prop) {
          this.opes[0].disabled = true;
          this.query.ope = this.opes[1].key;
          this.query.value = this.yesOrNo[0].prop;
        } else {
          this.opes[0].disabled = false;
          this.query.value = '';
        }
      },
      handleSizeChange(size) {
        this.pageSize = size;
        this.handleCurrentChange(1);
      },
      handleCurrentChange(pageNow) {
        this.pageList.splice(0, this.pageList.length);
        let start = (pageNow - 1) * this.pageSize;
        if (start > this.filterList.length -1) {
          return;
        }
        let end = pageNow * this.pageSize;
        if (end > this.filterList.length) {
          end = this.filterList.length;
        }
        for (let i = start; i < end; i++) {
          this.pageList.push(this.filterList[i]);
        }
      }
    },
    mounted() {
      this.search();
      this.getAutoDiscountSwitchTime()
    }
  }
</script>
