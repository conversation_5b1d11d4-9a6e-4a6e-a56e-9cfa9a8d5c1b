<!-- 营销优惠活动区间列表 -->
<template>
	<xpt-list
    :orderNo='true'
		:data='tableData'
		:btns='btns'
		:colData='cols'
		:pageTotal='totalPage'
		:searchPage='searchObj.page_name'
		searchHolder='请输入查询条件'
		@search-click='preSearch'
		@page-size-change='handleSizeChange'
		@current-page-change='handleCurrentChange'
	>
	</xpt-list>
</template>
<script>
export default {
	data(){
		var self = this;
		return{
			btns: [
				{
					type: 'primary',
					txt: '新增',
					click: self.add
				},
        {
          type: 'primary',
          txt: '刷新',
          click: self.refresh,
          loading: false
        }
			],
			cols: [
				{
					label: '活动id',
					prop: 'id'
				}, {
					label: '活动名称',
					prop: 'time_zone_name',
					redirectClick(row) {
						self.toEditFun(row.id)
					}
				}, {
					label: '活动开始时间',
					prop: 'time_zone_start',
					format: 'dataFormat3',
					width: 140
				}, {
					label: '活动结束时间',
					prop: 'time_zone_end',
					format: 'dataFormat3',
					width: 140
				}, {
          label: '全局标志',
          prop: 'if_grobal',
          format: 'yesOrNo'
        }, {
          label: '是否生效',
          prop: 'if_effective',
          format: 'yesOrNo'
        }, {
					label: '备注',
					prop: 'remark'
				},{
					label: '核销优惠线下收款上限',
					prop: 'verify_discount_offline_amount'
				}, {
					label: '创建人',
					prop: 'creatorName'
				}, {
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
          width: 140
				}, {
					label: '最后操作人',
					prop: 'modifierName'
				}, {
					label: '最后操作时间',
          prop: 'modify_time',
          format: 'dataFormat1',
          width: 140
				}
			],
			searchObj:{
				page_no:1,
				page_size: 50,
				page_name: 'scm_sale_discount_section',
        if_need_page: 'Y',
				where: []
			},
			totalPage:0,
			searchInput:'',
			tableData:[],
			selectId:[],
			searchKey:''
		}
	},
	props:['params'],
	mounted(){
		/* var self=this;
		self.$root.eventHandle.$on('discountAdd',(d)=>{
			self.searchFun();
		}); */
    this.searchFun();
	},
	destroyed(){
		// this.$root.offEvents('discountAdd');
  },
	methods:{
		add(){
			this.$root.eventHandle.$emit('creatTab',{name:'新建营销优惠活动区间',component:()=>import('@components/discount/MarketingDiscountSectionDetail.vue')});
		},
    preSearch(txt, resolve){
      this.searchObj.where = txt
      this.searchFun(resolve);
    },
		searchFun(resolve){
		  const self = this;
			self.ajax.postStream('/order-web/api/scmSaleDiscountSection/findScmSaleDiscountSectionPage', this.searchObj, (res) => {
        console.log(res);
        if(res.body.result){
          self.tableData = res.body.content.list;
          self.totalPage = res.body.content.count;
        }else{
          self.$message.error(res.body.msg || '');
        }
        if (resolve) {
          resolve();
        }
      }, (err) => {
        self.$message.error(err);
        if (resolve) {
          resolve();
        }
      })
		},
		handleSizeChange(val){
			this.searchObj.page_size = val;
			this.searchFun();
		},
		handleCurrentChange(val){
			this.searchObj.page_no = val;
			this.searchFun();
		},
    toEditFun(id){
      let params = {};
      params.id = id;
      this.$root.eventHandle.$emit('creatTab',{name:'编辑营销优惠活动区间',params:params,component:()=>import('@components/discount/MarketingDiscountSectionDetail.vue')});
    },
    refresh() {
      const self = this;
      new Promise((resolve) => {
        self.btns[1].loading = true;
        self.searchFun(resolve);
      }).then(() => {
        self.btns[1].loading = false;
      });
    }
	}
}
</script>
