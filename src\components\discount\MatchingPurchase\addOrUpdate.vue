<!-- 搭配购活动详情、新增、编辑-->
<template>
    <div class="xpt-flex">
        <el-row class="xpt-top" :gutter="40">
            <el-col :span="24">
                <el-button type="primary" size="mini" @click="presave()" :loading="saveStatus">保存</el-button>
                <el-button size="mini" type="success" @click="submit" :loading="onAudit">提交</el-button>
                <el-button size="mini" type="warning" @click="withdrawAudit" :disabled="form.status != 'WAIT_APPROVED'">
                    撤回
                </el-button>
                <el-button size="mini" type="success" @click="reflash" :disabled="isAdd || form.if_change == 'Y'"
                    :loading="onReflash">刷新</el-button>
                <el-button size="mini" type="danger" @click="delOrder"
                    :disabled="listBtnControl || isAdd || form.if_change == 'Y'" :loading="onFaile">删除</el-button>
                <el-button size="mini" type="primary" @click="copyAdd"
                    :disabled="form.document_type == 'COPY' || isAdd">复制新增
                </el-button>
                <el-button size="mini" type="danger" @click="forbidden"
                    :disabled="form.status != 'APPROVED' || !if_enable_time">禁用</el-button>
            </el-col>
        </el-row>
        <el-form :model="form" :rules="rules" ref="form" label-position="right" label-width="110px">
            <el-row class="xpt-flex__bottom">
                <el-tabs v-model="firstTab" style="height: 276px">
                    <el-tab-pane label="基础信息" name="discountDetail" class="xpt-flex" style="overflow: hidden">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="搭配购ID" prop="discount_id">
                                    <el-input v-model="form.discount_id" size="mini" disabled></el-input>
                                </el-form-item>
                                <el-form-item label="搭配购编码" prop="discount_no">
                                    <el-input v-model="form.discount_no" size="mini" disabled></el-input>
                                </el-form-item>
                                <el-form-item label="搭配购名称" prop="discount_name">
                                    <el-input v-model="form.discount_name" size="mini" :maxlength="50"
                                        :disabled="formDIsabledControl2">
                                    </el-input>
                                    <el-tooltip v-if="rules.discount_name[0].isShow" class="item" effect="dark"
                                        :content="rules.discount_name[0].message" placement="right-start"
                                        popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="状态" prop="status">
                                    <el-input v-model="status_option[form.status]" size="mini" disabled></el-input>
                                </el-form-item>
                                <el-form-item label="生效时间" prop="enable_time">
                                    <el-date-picker v-model="form.enable_time" type="datetime" placeholder="选择时间"
                                        size="mini" style="width: 180px"
                                        :picker-options="dateFormatFun" :editable="false"></el-date-picker>
                                    <el-tooltip v-if="rules.enable_time[0].isShow" class="item" effect="dark"
                                        :content="rules.enable_time[0].message" placement="right-start"
                                        popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="失效时间" prop="disable_time">
                                    <el-date-picker v-model="form.disable_time" type="datetime" placeholder="选择时间"
                                        size="mini" style="width: 180px"
                                        :editable="false" :picker-options="endDateFormatFun"></el-date-picker>
                                    <el-tooltip v-if="rules.disable_time[0].isShow" class="item" effect="dark"
                                        :content="rules.disable_time[0].message" placement="right-start"
                                        popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="优惠类别" prop="discount_category">
                                    <el-select  v-model="form.discount_category" placeholder="请选择" size="mini"
                                    :disabled="listBtnControl">
                                        <el-option v-for="item in discountCategoryData" :key="item.code"
                                            :label="item.name" :value="item.code">
                                        </el-option>
                                    </el-select>
                                    <el-tooltip v-if="rules.discount_category[0].isShow" class="item" effect="dark"
                                        :content="rules.discount_category[0].message" placement="right-start"
                                        popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="线下活动分类" >
                                    <xpt-select-aux v-model='form.offline_discount_class' aux_name='offline_discount_class'  :disabled="listBtnControl" ></xpt-select-aux>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="线下货款模式" prop="offline_payment_mode_name">
                                    <xpt-input v-model="form.offline_payment_mode_name" size="mini" icon="search"
                                        :disabled="listBtnControl"
                                        :on-icon-click="() => selectGoodsDiscountCategory('OFFLINE_PAYMENT_MODE')"
                                        readonly placeholder="请选择线下货款模式"
                                        @change="goodsDiscountCategoryChange('OFFLINE_PAYMENT_MODE')">
                                    </xpt-input>
                                    <el-tooltip v-if="rules.offline_payment_mode_name[0].isShow" class="item"
                                        effect="dark" :content="rules.offline_payment_mode_name[0].message"
                                        placement="right-start" popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="店铺范围" prop="store_area">
                                    <el-select size="mini" v-model="form.store_area" :disabled="formDIsabledControl"
                                        @change="shopsChanged">
                                        <el-option v-for="item in store_area_option" :key="item.value"
                                            :label="item.label" :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <!-- <el-form-item label="优惠性质">
                                    <el-select size="mini" v-model="form.discount_kind"
                                        :disabled="formDIsabledControl2">
                                        <el-option v-for="item in discount_kind_opiton" :key="item.value"
                                            :label="item.label" :value="item.value" :disabled="item.disabled">
                                        </el-option>
                                    </el-select>
                                </el-form-item> -->
                                <el-form-item label="时间维度" prop="discount_affection_date">
                                    <xpt-select-aux v-model="form.discount_affection_date" aux_name="discountAffection"
                                        :disabled="formDIsabledControl2"></xpt-select-aux>
                                    <el-tooltip v-if="rules.discount_affection_date[0].isShow" class="item"
                                        effect="dark" :content="rules.discount_affection_date[0].message"
                                        placement="right-start" popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="套餐类型" prop="package_type">
                                    <el-select v-model="form.package_type" size="mini" style="width:150px;" :disabled="Boolean(form.discount_id)">
                                        <el-option v-for="packageType in packageTypeOption" :label="packageType.label" :value="packageType.key" :key="packageType.key"   ></el-option>
                                    </el-select>
                                    <el-tooltip v-if="rules.package_type[0].isShow" class="item"
                                        effect="dark" :content="rules.package_type[0].message"
                                        placement="right-start" popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="套餐基础价格" prop="package_base_price">
                                    <el-input v-model="form.package_base_price" size="mini" type="number"
                                        @change.native="(e) => { form.package_base_price = fitterPackageBasePrice(form.package_base_price) }" :disabled="listBtnControl || form.package_type == 'FIXED_DISCOUNT_PACKAGE'">
                                    </el-input>
                                    <el-tooltip v-if="rules.package_base_price[0].isShow" class="item"
                                        effect="dark" :content="rules.package_base_price[0].message"
                                        placement="right-start" popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="经销结算比例" prop="dealer_discount">
                                    <el-input v-model="form.dealer_discount" size="mini" type="number"
                                        @change.native="(e) => { form.dealer_discount = fitterPrice3(form.dealer_discount) }" :disabled="ifSaved">
                                    </el-input>%
                                </el-form-item>
                                <el-form-item label="组数" prop="groups">
                                    <el-radio-group v-model="form.groups" :disabled="ifSaved || isCopyAdd">
                                        <el-radio :label="2"></el-radio>
                                        <el-radio :label="3"></el-radio>
                                        <el-radio :label="4"></el-radio>
                                        <el-radio :label="5"></el-radio>
                                        <el-radio :label="6" v-if='form.package_type==="FIXED_PRICE_PACKAGE"'></el-radio>
                                        <el-radio :label="7" v-if='form.package_type==="FIXED_PRICE_PACKAGE"'></el-radio>
                                        <el-radio :label="8" v-if='form.package_type==="FIXED_PRICE_PACKAGE"'></el-radio>
                                        <el-radio :label="9" v-if='form.package_type==="FIXED_PRICE_PACKAGE"'></el-radio>
                                        <el-radio :label="10" v-if='form.package_type==="FIXED_PRICE_PACKAGE"'></el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="限制时效" prop="time_limit_share">
                                    <el-switch v-model="form.time_limit_share" on-text="是" off-text="否" on-value="Y"
                                        off-value="N" :disabled="formDIsabledControl || auto_add_control"
                                        @change="shopsBtnControl"></el-switch>
                                </el-form-item>
                                <el-form-item label="添加时效控制" prop='if_time_control'>
                                    <el-switch v-model="form.if_time_control" on-text="是" off-text="否" on-value="Y"
                                        off-value="N">
                                    </el-switch>
                                </el-form-item>
                                <el-form-item label="添加控制时长" prop="time_control_value"
                                    v-if="form.if_time_control == 'Y'">
                                    <xpt-input v-model="form.time_control_value" size="mini" :disabled="formDIsabledControl" placeholder="请选择添加控制时长"></xpt-input>
                                    <el-tooltip v-if="rules.time_control_value[0].isShow" class="item" effect="dark"
                                        :content="rules.time_control_value[0].message" placement="right-start"
                                        popper-class="xpt-form__error">
                                        <i class="el-icon-warning"></i>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="需要审核" prop="if_need_audit">
                                    <el-switch v-model="form.if_need_audit" on-text="是" off-text="否" on-value="Y"
                                        off-value="N" :disabled="formDIsabledControl || need_audit_control"
                                        @change="shopsBtnControl"></el-switch>
                                </el-form-item>
                                <el-form-item label="仅使用一次" prop="if_use_once">
                                    <el-switch v-model="form.if_use_once" on-text="是" off-text="否" on-value="Y"
                                        off-value="N" :disabled="formDIsabledControl || auto_add_control"
                                        @change="shopsBtnControl"></el-switch>
                                </el-form-item>
                                <el-form-item label="创建人" prop="creator_name">
                                    <el-input v-model="form.creator_name" size="mini" disabled></el-input>
                                </el-form-item>
                                <el-form-item label="创建时间" prop="create_time">
                                    <el-date-picker v-model="form.create_time" type="datetime" size="mini"
                                        style="width: 180px" disabled></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="修改人" prop="modifier_name">
                                    <el-input v-model="form.modifier_name" size="mini" disabled></el-input>
                                </el-form-item>
                                <el-form-item label="修改时间" prop="modify_time">
                                    <el-date-picker v-model="form.modify_time" type="datetime" size="mini"
                                        style="width: 180px" disabled></el-date-picker>
                                </el-form-item>
                                <el-form-item label="审核人" prop="audit_name">
                                    <el-input v-model="form.audit_name" size="mini" disabled></el-input>
                                </el-form-item>
                                <el-form-item label="审核时间" prop="audit_time">
                                    <el-date-picker v-model="form.modify_time" type="datetime" size="mini"
                                        style="width: 180px" disabled></el-date-picker>
                                </el-form-item>
                                <el-form-item label="备注" prop="remark">
                                    <el-input type="textarea" v-model="form.remark" :maxlength="240"
                                        :autosize="{ minRows: 2, maxRows: 2 }" size="mini"
                                        style="width: 100%; min-width: 240px; min-height: 48px"
                                        :disabled="formDIsabledControl2"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="展示信息" name="moreSet" class="xpt-flex">
                        <el-row style="height: 280px">
                            <el-form style="height:100%" :model="form" :rules="rules" ref="form" label-position="right" label-width="110px">
                                <el-row class="xpt-flex__bottom" style="height:100%">
                                    <el-col :span="6">
                                        <el-form-item label="风格" prop="style" required>
                                            <el-select v-model="form.style" placeholder="请选择" size="mini">
                                                <el-option v-for="item in styles" :key="item.code" :label="item.name"
                                                    :value="item.code">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="空间" prop="space" required>
                                            <el-select v-model="form.space" placeholder="请选择" size="mini">
                                                <el-option v-for="item in space" :key="item.code" :label="item.name"
                                                    :value="item.code">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="面积" prop="dimension" required>
                                            <el-select v-model="form.dimension" placeholder="请选择" size="mini">
                                                <el-option v-for="item in areas" :key="item.code" :label="item.name"
                                                    :value="item.code">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="简介" prop="introduction" required>
                                            <el-input type="textarea" v-model="form.introduction" :maxlength="240"
                                                :autosize="{ minRows: 2, maxRows: 2 }" size="mini"
                                                style="width: 100%; min-width: 240px; min-height: 48px"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="主图" prop="main_photo" required>
                                            <xpt-upload-v3 uploadBtnText="上传" acceptType="usually" :dataObj="uploadData"
                                                @uploadSuccess="uploadSuccess" :uploadSize="20">
                                            </xpt-upload-v3>
                                            <div v-if="!!filePath.length" class="showImg">
                                                <img :src="filePath" alt="图片" width="80" v-if="isImage(filePath)"
                                                    @click="showImageList(filePath)" />
                                            </div>
                                            <xpt-image :images="imageList" :show="ifShowImage" :ifUpload="false"
                                                :ifClose="false" @close="closeShowImage">
                                            </xpt-image>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="优惠清单" name="discountList" class="xpt-flex discount-list">
                        <el-row>
                            <xpt-list2 :data="DiscountVo" :btns="DiscountBtns" :colData="DiscountCols"
                                :taggelClassName="tableRowClassName" :pageTotal="goodCount"
                                :searchPage="goodsSearch.page_name" @selection-change="discountselectionChange"
                                @search-click="martialPresearch" @page-size-change="goodsPageSizeChange"
                                @current-page-change="goodsPageChange">
                                <template slot="group_type" slot-scope="scope">
                                    <el-select style="width:100px;" size="mini" :disabled="!ifCreateAndRejectOfStatus && !scope.row.new_goods" @change="handleGroupTypeChange($event,scope.row)" v-model="scope.row.group_type" placeholder="请选择">
                                        <el-option label="A" value="A"></el-option>
                                        <el-option label="B" value="B"></el-option>
                                        <el-option label="C" value="C"></el-option>
                                        <el-option label="D" value="D"></el-option>
                                        <el-option label="E" value="E"></el-option>
                                        <el-option label="F" value="F"></el-option>
                                        <el-option label="G" value="G"></el-option>
                                        <el-option label="H" value="H"></el-option>
                                        <el-option label="I" value="I"></el-option>
                                        <el-option label="J" value="J"></el-option>
                                    </el-select>
                                </template>
                                <template slot="group_type_name" slot-scope="scope">
                                    <el-input style="width:100px;" size="mini" :maxlength="5" :disabled="!ifCreateAndRejectOfStatus && !scope.row.new_goods" :value="scope.row.group_type_name" @input="handleGroupTypeNameInput($event,scope.row)" placeholder=""></el-input>
                                </template>
                                <template slot="default_show" slot-scope="scope">
                                    <el-select style="width:100px;" size="mini"  v-model="scope.row.default_show" @change="handleDefaultShowChange($event,scope.row)" placeholder="请选择">
                                        <el-option label="是" :value="true"></el-option>
                                        <el-option label="否" :value="false"></el-option>
                                    </el-select>
                                </template>
                                <template slot="change_amount" slot-scope="scope">
                                    <el-input v-model="scope.row.change_amount"  size='mini' style="width:100%;" :disabled="!((form.status == 'CREATE'||form.status == 'REJECTED' || (form.status == 'APPROVED' && scope.row.new_goods))&&form.package_type === 'FIXED_PRICE_PACKAGE')" @blur="e=>{
                                            const result = fitterChangeAmountPrice(scope.row.change_amount) ? scope.row.change_amount:'';
                                            $nextTick(() =>{
                                                $set(scope.row, 'change_amount',result)
                                            });

                                    }"></el-input>
                                </template>
                                <xpt-import slot='btns' :taskUrl='discountListUploadUrl' :otherParams="{discount_id:form.discount_id}" :isupload="!form.discount_id" class='mgl10'></xpt-import>
                                <el-button
                                    slot="btns"
                                    size="mini"
                                    type="warning"
                                    @click="showUploadResult('/price-web/api/actDiscount/importMaterial/list')"
                                    :disabled="!form.discount_id"
                                    >导入结果</el-button
                                >
                            </xpt-list2>
                        </el-row>
                    </el-tab-pane>
                </el-tabs>
            </el-row>
        </el-form>
        <el-row class="xpt-flex__bottom">
            <el-tabs v-model="secondTab">
                <el-tab-pane label="优惠项目" name="discount_GoodsList" class="xpt-flex xpt-flex__bottom">
                    <xpt-list2 ref="discount_GoodsList" :data="listActDiscountItemVo" :btns="discountItemBtns"
                        :colData="discountItemCols" :taggelClassName="tableRowClassName"
                        :searchPage="itemSearch.page_name" :pageTotal="itemCount" @search-click="itemPresearch"
                        @page-size-change="itemPageSizeChange" @current-page-change="itemPageChange"
                        @row-click="itemClick" @selection-change="itemSelectionChange">
                        <template slot="subtract_money" slot-scope="scope">
                            <el-input size="mini" type="number" :min="0" v-model="scope.row.subtract_money" :disabled="form.package_type == 'FIXED_DISCOUNT_PACKAGE' ? !ifCreateAndRejectOfStatus : true" placeholder="" @change="()=>{scope.row.subtract_money = handleSubtractMoneyChange(scope.row.subtract_money)}"></el-input>
                        </template>
                    </xpt-list2>
                </el-tab-pane>
                <el-tab-pane label="实施店铺" name="discount_shopList" class="xpt-flex">
                    <xpt-list2 ref="ShopList" :data="listPmsActShopVO" :btns="shopBtns" :colData="shopCols"
                        searchPage="act_discount_shop" :pageTotal="shopCount" @search-click="shopPresearch"
                        @selection-change="shopSelectionChange" @page-size-change="shopPageSizeChange"
                        @current-page-change="shopPageChange">
                        <xpt-import slot='btns' :taskUrl='discountShopListUploadUrl' :otherParams="{discount_id:form.discount_id}" :isupload="!(form.discount_id && ifCreateAndRejectAndCheckedOfStatus && !ifGlobalStore)" class='mgl10'></xpt-import>
                        <el-button
                            slot="btns"
                            size="mini"
                            type="warning"
                            @click="showUploadResult('/price-web/api/actDiscount/importShop/list')"
                            :disabled="!form.discount_id"
                            >导入结果</el-button
                        >
                    </xpt-list2>
                </el-tab-pane>
            </el-tabs>
        </el-row>
    </div>
</template>
<script>
import shop from "./new_model/shop.js";
import condition from "./new_model/condition.js";
import conditionItem from "./new_model/conditionItem.js";
import changing from "./new_model/changing.js";
import goods from "./new_model/goods.js";
import Vue from "vue";
import Fn from "@common/Fn.js";
export default {
    props: ["params"],
    mixins: [shop, changing, conditionItem, condition, goods],
    data () {
        var self = this;
        return {
            discountListUploadUrl: '/price-web/api/actDiscount/importMaterial',
            discountShopListUploadUrl: '/price-web/api/actDiscount/importShop',
            ifSaved:false,//是否保存成功
            ifShowImage: false,
            imageList: [],
            filePath: "",
            //附件上传路径标志
            uploadData: {},
            areas: [],//面积
            styles: [],//风格
            space: [],//空间
            ifBeforeCreateGoods: false,
            insetDiscountObj: {},
            backup: {
                disable_time: "",
                store_area: 0,
            },
            initialData: {},
            initialCondition: [],
            initialItem: [],
            initialMaterial: [],
            initialShop: [],
            status_option: {
                CREATE: "创建",
                SUBMITTED: "提交审核",
                APPROVED: "已审核",
                RETRIAL: "重新审核",
                CANCELED: "已作废",
                APPROVING: "审核中",
                WITHDRAWED: "已撤回",
                LOCKED: "锁定",
                INVALID: "已作废",
                WAIT_APPROVED: "待审核",
                REJECTED: "驳回",
                FORBID: "禁用",
            },
            // 自动添加
            auto_add_control: false,
            // 自动审核
            need_audit_control: false,
            // 影响售价
            effect_saleprice_control: false,
            // 金额可变
            amount_change_control: false,
            firstTab: "discountDetail",
            secondTab: "discount_GoodsList",
            // 当前操作是新增还是编辑，true为新增，false为编辑
            isAdd: true,
            // 复制新增时优惠子类型第一次渲染不加载
            copyAddSubFlag: false,
            //复制新增
            isCopyAdd: false,
            // 变更
            isChangingAdd: false,
            // 还有子分类
            isChildClass: false,
            conditionSelect: null,
            discountItemSelect: null,
            changing_conditionVo: [],
            // 优惠类型
            // discountCategory:_AUX.get('discountCategory'),
            //优惠类型,当为编辑状态时获取所有列表，当为新增状态时获取生效列表
            // disClassData: this.isAdd ? __AUX.getValidData('discountCategory') : __AUX.get('discountCategory'),
            // 所有子分类
            childClassDataAll:
                this.isAdd && !this.isCopyAdd
                    ? __AUX.getValidData("discountSubclass")
                    : __AUX.get("discountSubclass"),
            // 当前可以选子分类
            childClassData:
                this.isAdd && !this.isCopyAdd ? [] : __AUX.get("discountSubclass"),
            discountCategoryData:
                this.isAdd && !this.isCopyAdd
                    ? []
                    : __AUX.get("dpg_discount_Category"),
            DiscountVo: [],
            listPmsActShopVO: [],
            conditionVo: [],
            delConditionVo: [],
            cancelConditionVo: [],

            delListActDiscountItemVo: [],
            cancelListActDiscountItemVo: [],
            listActDiscountItemVo: [],
            export_status: false,
            form: {
                groups: 2,//组数
                dimension: "",//面积
                style: "",//风格
                space: "",//空间
                introduction:"",//简介
                if_time_control: "N",
                time_control_value: "",
                // 单据类型
                document_type: "NORMAL",
                //id
                discount_id: "",
                //编号
                discount_no: "",
                offline_discount_class:'',
                //名称
                discount_name: "",
                //状态
                status: "CREATE",
                //生效时间
                enable_time: "",
                //失效时间
                disable_time: "",
                //需要审核
                if_need_audit: "Y",
                // 优惠性质
                discount_kind: "",
                time_limit_share: "N",
                if_use_once: "", //仅享用一次
                //店铺范围
                store_area: 1,
                //备注
                remark: "",
                // 经销结算比例
                dealer_discount: "",
                //审核时间
                audit_time: "",
                //审核人名称
                audit_person_name: "",
                //审核人id
                audit_peerson_id: "",
                //创建人
                creator: "",
                //创建人姓名
                creator_name: "",
                //创建时间
                create_time: "",
                // 时间维度
                discount_affection_date: "ORDER_DATE",
                // 是否变更
                if_change: "N",
                discount_category: "", //优惠类别--写死搭配购
                offline_payment_mode_name: "不限", //线下货款模式
                offline_payment_mode: "EMPTY", //线下货款模式
                package_type: '', //套餐类型
                package_base_price: ''
            },
            store_area_option: [
                {
                    label: "店铺",
                    value: 0,
                },
                {
                    label: "全局",
                    value: 1,
                },
            ],
            discount_kind_opiton: [
                {
                    label: "拍减",
                    value: "ORDER_CUT",
                    disabled: false,
                },
                {
                    label: "拍赠",
                    value: "ORDER_PRESENT",
                    disabled: false,
                },
                {
                    label: "付返",
                    value: "PAID_RETURN",
                    disabled: false,
                },
                {
                    label: "付赠",
                    value: "PAID_PRESENT",
                    disabled: false,
                },
            ],
            // 用于添加商品时的设置，传递至goodsAdd.vue
            isEditMateriel: false,
            rules: {
                discount_affection_date: self.VLFun("moreSet", "请选择时间维度"),
                act_name: self.VLFun("discountDetail", "请输入活动名称"),
                groups:[
                    {
                        required: true,
                    }
                ],
                time_control_value: [
                    {
                        required: true,
                        isShow: false,
                        message: "添加控制时长为1-720正整数",
                        trigger: "change",
                        validator: function (rule, value, callback) {
                            let reg = /^[1-9][0-9]{0,3}$/,
                                values = (value += "");
                            if (value || self.form.if_time_control == "N") {
                                if (value && reg.test(value)) {
                                    self.rules[rule.field][0].isShow = false;
                                    callback();
                                } else {
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(""));
                                }
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback(new Error(""));
                            }
                        },
                    },
                ],
                enable_time: [
                    {
                        required: true,
                        message: "生效时间不能大于失效时间",
                        trigger: "change",
                        isShow: false,
                        isValidateFromOther: false,
                        validator: function (rule, value, callback) {
                            if (value && self.form.disable_time) {
                                if (value >= self.form.disable_time) {
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(""));
                                } else {
                                    self.rules[rule.field][0].isShow = false;
                                    if (self.rules[rule.field][0].isValidateFromOther) {
                                        self.rules[rule.field][0].isValidateFromOther = false;
                                    } else {
                                        self.rules["disable_time"][0].isValidateFromOther = true;
                                        self.$refs["form"].validateField("disable_time");
                                    }
                                    callback();
                                }
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback();
                            }
                        },
                    },
                ],
                disable_time: [
                    {
                        required: true,
                        message: "失效时间不能小于生效时间",
                        trigger: "change",
                        isShow: false,
                        isValidateFromOther: false,
                        validator: function (rule, value, callback) {
                            if (value && self.form.enable_time) {
                                if (value <= self.form.enable_time) {
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(""));
                                } else {
                                    self.rules[rule.field][0].isShow = false;
                                    if (self.rules[rule.field][0].isValidateFromOther) {
                                        self.rules[rule.field][0].isValidateFromOther = false;
                                    } else {
                                        self.rules["enable_time"][0].isValidateFromOther = true;
                                        self.$refs["form"].validateField("enable_time");
                                    }
                                    callback();
                                }
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback();
                            }
                        },
                    },
                ],
                discount_name: [
                    {
                        required: true,
                        isShow: false,
                        message: "请选择优惠活动名称",
                        trigger: "change",
                        validator: function (rule, value, callback) {
                            if (value) {
                                self.rules[rule.field][0].isShow = false;
                                callback();
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback(new Error(""));
                            }
                        },
                    },
                ],
                actiivity_source_id: [
                    {
                        required: false,
                        isShow: false,
                        message: "请填写外部活动ID",
                        trigger: "blur",
                        validator: function (rule, value, callback) {
                            if (self.form.actiivity_source && !value) {
                                self.rules[rule.field][0].isShow = true;
                                callback(new Error(""));
                            } else {
                                self.rules[rule.field][0].isShow = false;
                                callback();
                            }
                        },
                    },
                ],
                price: [
                    {
                        required: true,
                        isShow: false,
                        message: "请输入大于等于0的数字，保留两位小数",
                        trigger: "change",
                        validator: function (rule, value, callback) {
                            let reg =
                                /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                                values = (value += "");
                            if (reg.test(values)) {
                                self.rules[rule.field][0].isShow = false;
                                callback();
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback(new Error(""));
                            }
                        },
                    },
                ],
                enable_price: [
                    {
                        required: true,
                        isShow: false,
                        message: "请输入大于等于0的数字，保留两位小数",
                        trigger: "change",
                        validator: function (rule, value, callback) {
                            let reg =
                                /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                                values = value + "";
                            if (reg.test(values)) {
                                self.rules[rule.field][0].isShow = false;
                                callback();
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback(new Error(""));
                            }
                        },
                    },
                ],
                discount_category: [
                    {
                        required: true,
                        isShow: false,
                        message: "请选择线上优惠类别",
                        trigger: "change",
                        validator: function (rule, value, callback) {
                            if (value) {
                                self.rules[rule.field][0].isShow = false;
                                callback();
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback(new Error(""));
                            }
                        },
                    },
                ],
                offline_payment_mode_name: [
                    {
                        required: true,
                        isShow: false,
                        message: "请选择线下货款模式",
                        trigger: "change",
                        validator: function (rule, value, callback) {
                            if (value) {
                                self.rules[rule.field][0].isShow = false;
                                callback();
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback(new Error(""));
                            }
                        },
                    },
                ],
                package_type: [
                    {
                        required: true,
                        isShow: false,
                    },
                ],
                package_base_price: [
                    {
                        required: false,
                        isShow: false,
                        message: "请输入大于0的数字，保留两位小数",
                        trigger: "change",
                        validator: function (rule, value, callback) {
                            if(!self.rules[rule.field][0].required){
                                callback();
                                return
                            }
                            let reg =
                                /^(([0-9])|([0-9]\.\d{0,2})|([1-9][0-9]+)|([1-9][0-9]+\.\d{0,2})|(0\.\d{0,2}))$/,
                                values = value + "";
                            if (reg.test(values)) {
                                self.rules[rule.field][0].isShow = false;
                                callback();
                            } else {
                                self.rules[rule.field][0].isShow = true;
                                callback(new Error(""));
                            }
                        },
                    },
                ],
            },
            operaterCols: [
                {
                    label: "操作人",
                    prop: "operator_name",
                },
                {
                    label: "业务操作",
                    prop: "operation_name",
                },
                {
                    label: "操作时间",
                    prop: "operation_time",
                    width: 300,
                    format: "dataFormat1",
                },
                {
                    label: "操作描述",
                    prop: "operation_desc",
                },
                {
                    label: "备注",
                    prop: "remark",
                },
            ],
            operaterCount: 0,
            // 设置生效时间从00:00:00开始
            dateFormatFun: {
                /*生效时间允许选所有值*/
                date: (function () {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + "-" + month + "-" + day + " " + "00:00:00";
                    return new Date(time);
                })(),
            },
            endDateFormatFun: {
                date: (function () {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + "-" + month + "-" + day + " " + "23:59:59";
                    return new Date(time);
                })(),
            },
            // 设置结束时间为23:59:59
            iDateFormatFun: {
                disabledDate: (time) => {
                    /*新增时能选所有时间，编辑时选当天之后*/
                    return this.isAdd ? false : time.getTime() < Date.now() - 8.64e7;
                },
                date: (function () {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + "-" + month + "-" + day + " " + "23:59:59";
                    return new Date(time);
                })(),
            },
            // 保存按钮状态
            saveStatus: false,
            onAudit: false,
            onWithdraw: false,
            onSuccess: false,
            onReflash: false,
            onFaile: false,
            shopsFlag: false,
            if_change_once: false,
            if_first_edit: true,
            offlinePaymentModeCategoryObj: __AUX
                .get("offline_payment_mode")
                .reduce((pre, cur, idx, arr) => {
                    pre[cur.code] = cur.name;
                    return pre;
                }, {}),
            discountCategoryAuxObj: __AUX
                .get("dicount_category_match")
                .reduce((pre, cur, idx, arr) => {
                    pre[cur.code] = cur.name;
                    return pre;
                }, {}),
            ifActWinningStatus: false,
            autoDiscountSwitch: 0,
            autoDiscountSwitchTime: "",
            packageTypeOption: [
                {
                    label: '固定优惠套餐',
                    key: 'FIXED_DISCOUNT_PACKAGE'
                },
                {
                    label: '固定价格套餐',
                    key: 'FIXED_PRICE_PACKAGE'
                }
            ]
        };
    },
    methods: {
        fitterChangeAmountPrice(num){
            var reg = /^(((\-*)([0-9]))|((\-*)([0-9]\.\d{0,2}))|((\-*)([1-9][0-9]+))|((\-*)([1-9][0-9]+\.\d{0,2}))|((\-*)(0\.\d{0,2})))$/;
            if(!reg.test(num)){
			this.$message.error('当前输入不符合规范，请输入大于0小于0的数字，保留两位小数')
			}
			return reg.test(num)
		},
        fitterPackageBasePrice(price) {
            let reg = /^(([0-9])|([0-9]\.\d{0,2})|([1-9][0-9]+)|([1-9][0-9]+\.\d{0,2})|(0\.\d{0,2}))$/;
            if (!reg.test(price) && !!price) {
                this.$message.error("当前输入不符合规范，请输入大于0的数字，保留两位小数");
                return "";
            } else {
                return price;
            }
        },
        changePackageType(value) {
            if (!value) return;
            let discountCategoryAll = __AUX.getValidData("dpg_discount_Category")
            let discountCategoryList = []
            this.discountCategoryData = discountCategoryAll.filter((item) => {
                let tagList = []
                item.tag ? tagList = item.tag.split(',') : []
                if (tagList.includes(value)) {
                    discountCategoryList.push(item.code)
                    return item
                }
            });
            if (discountCategoryList.length == 1) {
                this.form.discount_category = discountCategoryList[0]
            } else if (!(discountCategoryList.includes(this.form.discount_category))){
                this.form.discount_category = ''
            }
            if (value === 'FIXED_DISCOUNT_PACKAGE') {
                this.DiscountVo.map(item => {
                    item.change_amount = ''
                })
                this.form.package_base_price = ''
                this.rules.package_base_price[0].required = false
            } else {
                this.rules.package_base_price[0].required = true
            }
        },
        changeDiscountCategory (value) {
            if (!value) return;
            let discountCategoryAll = __AUX.getValidData("dpg_discount_Category")
            let activeDiscountCategory = discountCategoryAll.filter((item) => {
                return item.code == value
            });

            if (activeDiscountCategory.length > 0 && !(activeDiscountCategory[0].tag.split(',').includes(this.form.package_type))) {
                this.form.package_type = activeDiscountCategory[0].tag.split(',')[0]
            }
        },
         //导入结果
         showUploadResult(url) {
            this.$root.eventHandle.$emit("alert", {
                style: "width:900px;height:600px",
                title: "导入结果",
                params: {
                    url,
                    data: {},
                    showDownload: false,
                },
                component: () => import("@components/common/eximport"),
            });
        },
        closeShowImage () {
            this.ifShowImage = false;
        },
        isImage (str) {
            var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
            return reg.test(str);
        },
        showImageList (row) {
            this.initImageList(row);
            this.ifShowImage = true;
        },
        //初始化预览列表
        initImageList (row) {
            let imglist = [];
            let obj = {
                src: row,
                isPucture: true,
                active: true
            }
            imglist.push(obj);
            this.imageList = imglist;
        },
        uploadSuccess (uploadFileList) {
            this.filePath = uploadFileList[0].path;
        },
        getAutoDiscountSwitchTime () {
            this.ajax.postStream(
                "/user-web/api/auxiliary/getAuxiliaryDataList",
                {
                    categoryCode: "SALE_ORDER",
                },
                (res) => {
                    let result = "";
                    if (res.body.result) {
                        let arr = res.body.content.list;
                        if (arr.length > 0) {
                            arr.forEach((item) => {
                                if (
                                    item.code === "AUTO_DISCOUNT_SWITCH_TIME" &&
                                    item.status == 1
                                ) {
                                    let ifDisabled = item.disableTime
                                        ? item.disableTime > new Date().getTime()
                                        : true;
                                    let ifEnabled = item.enableTime
                                        ? item.enableTime < new Date().getTime()
                                        : true;
                                    if (ifDisabled || ifEnabled) {
                                        result = item.tag ? item.tag : false;
                                    }
                                }
                            });
                        }
                    }
                    this.autoDiscountSwitchTime = result;
                },
                (err) => {
                    this.autoDiscountSwitchTime = "";
                }
            );
        },
        initOfAutoAdd (value) {
            if (value === "Y") {
                let ifChooseOneItem = this.conditionVo.every((item) => {
                    return item.item_enable_options == "CHOOSEONEITEM";
                });
            }
        },
        checkDiscountOfAdd () {
            return new Promise((reslove, reject) => {
                let postData = {
                    discount_id: this.form.discount_id,
                    page_name: "act_discount_item",
                    page_no: 1,
                    page_size: 500,
                    where: [],
                },
                    discountItemObj = {},
                    discountItem = [];
                this.ajax.postStream(
                    "/price-web/api/actDiscount/getDiscountItem",
                    postData,
                    (res) => {
                        if (res.body.result && res.body.content) {
                            let list = res.body.content.list || [];
                            discountItemObj = list.reduce((obj, curr) => {
                                if (
                                    new Set(Object.keys(obj)).has(
                                        curr.discount_condition_id.toString()
                                    )
                                ) {
                                    discountItem.push(curr.discount_condition_id);
                                    obj[curr.discount_condition_id].push(curr);
                                } else {
                                    obj[curr.discount_condition_id] = [];
                                    obj[curr.discount_condition_id].push(curr);
                                }
                                return obj;
                            }, {});
                            reslove(discountItem);
                        } else {
                            reject(res.body.msg);
                        }
                    },
                    (err) => {
                        reject(err);
                    }
                );
            });
        },
        ChangeOfflineBefore (value) {
            let self = this;
            this.checkDiscountOfAdd()
                .then((discountItem) => {
                    let condition = [];
                    let isCheckCondition = self.conditionVo.some((item) => {
                        if (item.item_enable_options != "CHOOSEONEITEM") {
                            condition.push(item.discount_condition_id);
                        }
                        return item.item_enable_options != "CHOOSEONEITEM";
                    });
                    if (condition.length > 0 || discountItem.length > 0) {
                        if (condition.length > 0) {
                            this.$message.error(
                                `设置线下前置失败！优惠条件「ID：${condition[0]}」的优惠选项不为“任选一项”`
                            );
                        } else {
                            this.$message.error(
                                `设置线下前置失败！优惠条件「ID：${discountItem[0]}」存在多个优惠项目`
                            );
                        }
                    }
                })
                .catch((e) => {
                    // self.$message.error(e)
                });
        },
        // 自动添加为是，则只能选择优惠条件为任选一项，不能选择其他，反之，优惠条件有其他，则自动添加只能为否
        changeIfAutoAdd (value) {
            let self = this;
            this.checkDiscountOfAdd()
                .then((discountItem) => {
                    let condition = [];
                    let isCheckCondition = self.conditionVo.some((item) => {
                        if (item.item_enable_options != "CHOOSEONEITEM") {
                            condition.push(item.discount_condition_id);
                        }
                        return item.item_enable_options != "CHOOSEONEITEM";
                    });
                    if (condition.length > 0 || discountItem.length > 0) {
                        if (condition.length > 0) {
                            this.$message.error(
                                `设置自动添加失败！优惠条件「ID：${condition[0]}」的优惠选项不为“任选一项”`
                            );
                        } else {
                            this.$message.error(
                                `设置自动添加失败！优惠条件「ID：${discountItem[0]}」存在多个优惠项目`
                            );
                        }
                    }
                })
                .catch((e) => {
                    // self.$message.error(e)
                });
        },
        // 优惠性质变化引起自动添加字段的变更
        changeDiscountKind (value) {
            // 当勾选自动添加优惠，如果类别为付返|拍减的，需要控制一旦勾选叠加，只能是每满叠加
            if (/^(PAID_RETURN|ORDER_CUT)$/.test(this.form.discount_kind)) {
                this.checkIfEachFull();
            }
        },
        selectGoodsDiscountCategory (code) {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                component: () => import("@components/auxiliary/auxiliaryList"),
                style: "width:800px;height:500px",
                title: "辅助资料列表",
                params: {
                    isAlert: true,
                    selection: "checkbox",
                    categoryCode: code,
                    callback (d) {
                        let fieldObj = new Map([
                            [
                                "OFFLINE_PAYMENT_MODE",
                                {
                                    field: "offline_payment_mode",
                                    fieldName: "offline_payment_mode_name",
                                },
                            ],
                        ]);
                        let fieldCode = "",
                            fieldCodeName = "";
                        d.forEach((item, idx) => {
                            fieldCode += item.code + ",";
                            fieldCodeName += item.name + ",";
                        });
                        if (fieldCode[fieldCode.length - 1] == ",") {
                            fieldCode = fieldCode.substring(0, fieldCode.length - 1);
                            fieldCodeName = fieldCodeName.substring(
                                0,
                                fieldCodeName.length - 1
                            );
                        }
                        self.form[fieldObj.get(code).field] = fieldCode;
                        self.form[fieldObj.get(code).fieldName] = fieldCodeName;
                    },
                }, //传参过去
            });
        },
        goodsDiscountCategoryChange (code) {
            let fieldObj = new Map([
                [
                    "OFFLINE_PAYMENT_MODE",
                    {
                        field: "offline_payment_mode",
                        fieldName: "offline_payment_mode_name",
                    },
                ],
            ]);
            this.form[fieldObj.get(code).field] = "";
            this.form[fieldObj.get(code).fieldName] = "";
        },
        changeSelect (scope) {
            if (!scope.row.changeFlag) {
                scope.row.changeFlag = true;
                return;
            }
            switch (scope.row.item_enable_options) {
                case "CHOOSEONEITEM":
                    this.$set(scope.row, "item_enable_count", "1");
                    break;
                case "ALLSELECTION":
                    this.$set(scope.row, "item_enable_count", "0");
                    break;
                case "OPTIONAL":
                    this.$set(scope.row, "item_enable_count", "");
                    break;
                default:
                    return row;
                    break;
            }
        },
        changeNum (scope) {
            let vm = this;
            this.$nextTick(function () {
                vm.$set(
                    scope.row,
                    "item_enable_count",
                    vm.fitterPrice2(scope.row.item_enable_count)
                );
            });
        },
        // 校验 9.16
        VLFun (tabs, msg) {
            var self = this;
            return [
                {
                    required: true,
                    isShow: false,
                    message: msg,
                    trigger: "change",
                    validator: function (rule, value, callback) {
                        if (value) {
                            self.rules[rule.field][0].isShow = false;
                            callback();
                        } else {
                            self.rules[rule.field][0].isShow = true;
                            callback(new Error(""));
                        }
                    },
                },
            ];
        },
        ifgroup () {
            return this.form.store_area == 2 ? true : false;
        },
        // 查看物料
        checkConditionItem (row) {
            this.$root.eventHandle.$emit("alert", {
                title: "优惠商品详情",
                params: {
                    row: row,
                    callback (data) {
                        let self = this;
                    },
                },

                style: "width:930px; height: 600px",
                component: () => import("@components/discount/showDiscountItem.vue"),
            });
        },
        /*
        优惠类型选择
        根据所先优惠类型，过滤出子分类，并判断是否有子分类
        如果有子分类，设置isChildClass值为true，否则为false
        */
        selectParent (value) {
            let childClassData = [];
            this.childClassDataAll.find((row) => {
                if (row.parentCode === value && row.status && row.ext_field2 === "Y") {
                    childClassData.push(row);
                }
            });
            if (childClassData.length) {
                this.isChildClass = true;
                this.childClassData = childClassData;
            } else {
                this.isChildClass = false;
            }
            if (this.isAdd) {
                if (this.isCopyAdd) {
                    if (!this.copyAddSubFlag) {
                        this.copyAddSubFlag = true;
                        return;
                    }
                    this.form.discount_category = "";
                } else {
                    this.form.discount_category = "";
                }
            }
        },
        // 复制新增
        copyAdd () {
            var self = this;
            this.ajax.postStream(
                "/price-web/api/actDiscount/getCopyAddData",
                { discount_id: self.form.discount_id },
                (res) => {
                    if (res.body.result) {
                        // this.$message.success('操作');
                        // let conditionIds = [];
                        // for(var i in res.body.content){
                        // 	if(i.indexOf('condition_id') != -1){
                        // 		conditionIds.push(res.body.content[i]);
                        // 	}
                        // }
                        let data = res.body.content;
                        self.$root.eventHandle.$emit("creatTab", {
                            name: "复制新增搭配购活动",
                            params: {
                                actcopy_id: data.actDiscountVo.discount_id,
                                form: data.actDiscountVo,
                                isCopyAdd: true,
                                // conditionIds:conditionIds,
                                conditionVo: data.listActDiscountConditionVo,
                                DiscountVo: data.listActDiscountMaterialListVo,
                                listPmsActShopVO: data.listActDiscountShopVo,
                                listActDiscountItemVo: data.listActDiscountItemVo,
                            },
                            component: () => import("@components/discount/MatchingPurchase/addOrUpdate.vue"),
                        });
                    }
                }
            );
        },
        // 保存 9.17
        /*
        isClose：是否关闭当前标签
        */

        save (data, callback) {
            let self = this;
            self.saveStatus = true;
            this.ajax.postStream(
                "/price-web/api/actDiscount/saveOrUpdatePairs",
                data,
                (res) => {
                    // self.isCopyAdd = false;
                    self.saveStatus = false;
                    if (res.body.result) {
                        self.$message({
                            type: res.body.code == "610" ? "warning" : "success",
                            message: res.body.msg,
                        });
                        this.form.discount_id = res.body.content;
                        callback && callback();
                        this.getData();
                        self.isCopyAdd = false;
                        self.ifSaved=true
                    } else {
                        self.ifSaved=false
                        if (self.ifChange) {
                            self.initChanging();
                        }
                        self.$message.error(res.body.msg ? res.body.msg : "");
                    }
                }
            );
        },
        // 提交审核 9.17
        submit () {
            let self = this;
            if (self.form.if_change == "Y") {
                self.$message.error("变更未保存订单请先保存再提交");
                return false;
            }
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.presave(this.submitAudit);
                }
            });
        },
        presave (callback) {
            if(!this.form.style){
                return this.$message.error("请选择风格！")
            }
            if(!this.form.space){
                return this.$message.error("请选择空间！")
            }
            if(!this.form.dimension){
                return this.$message.error("请选择面积！")
            }
            if(!this.form.introduction){
                return this.$message.error("请填写简介！")
            }
            if(!this.filePath){
                return this.$message.error("请选择主图！")
            }
            let self = this;
            let if_adjust = false;
            if (!self.form.discount_name) {
                self.$message.error("优惠活动名称不能为空");
                return false;
            }
            let han = new RegExp("[\\u4E00-\\u9FFF]+", "g");
            if (
                self.form.document_type == "COPY" &&
                self.form.disable_time > self.initialData.disable_time
            ) {
                self.$message.error("活动失效时间不能大于原来失效时间");
                return false;
            }
            if (!!this.autoDiscountSwitchTime && this.form.discount_id) {
                // XPT-23841
                let switchTime = new Date(this.autoDiscountSwitchTime).getTime();
                if (this.form.enable_time < switchTime) {
                    if (
                        this.form.disable_time > switchTime ||
                        this.form.disable_time == switchTime
                    ) {
                        let msg = `生效时间小于“${this.autoDiscountSwitchTime}”，失效时间也必须小于“${this.autoDiscountSwitchTime}”`;
                        self.$message.error(msg);
                        return false;
                    }
                    if (this.form.discount_category != "OTHER_PROMOTION") {
                        let msg = `生效时间小于“${this.autoDiscountSwitchTime}”的活动，优惠类别只能选择“其他”`;
                        self.$message.error(msg);
                        return false;
                    }
                }
            }

            for(let i = 0; i < self.DiscountVo.length; i++){
                let item = self.DiscountVo[i]
                if(!item.group_type || !item.group_type_name){
                    self.$message.error(`请填写优惠清单物料:【${item.material_number}】信息`);
                    return
                }
            }

            const groupTypeName = new Map()
            const filterName = []
            for(let i = 0; i < self.DiscountVo.length; i++){
                let item = self.DiscountVo[i]
                if(self.DiscountVo.some(items=>items.group_type_name === item.group_type_name && items.group_type !== item.group_type)){
                    if(!filterName.length || !filterName.some(items=>items.group_type_name === item.group_type_name && items.group_type === item.group_type)){
                        filterName.push(item)
                        if(groupTypeName.get(item.group_type_name)){
                            let str = groupTypeName.get(item.group_type_name) + '、' + item.group_type
                            groupTypeName.set(item.group_type_name,str)
                        }else{
                            groupTypeName.set(item.group_type_name,item.group_type)
                        }
                    }
                };
            }
            if(filterName.length){
                let str = ''
                for (let [_,value] of groupTypeName) {
                    str += `【${value}】`
                }
                self.$message.error(`组别:${str}组别名称不能相同`);
                return
            };
            self.DiscountVo.forEach(item=>{
                if(item.new_goods){
                    delete item.new_goods
                }
            })
            for (let index = 0; index < self.listActDiscountItemVo.length; index++) {
                const items = self.listActDiscountItemVo[index];
                if(items.discount_condition_dec.split('+').length === self.form.groups && !items.subtract_money && self.form.package_type == 'FIXED_DISCOUNT_PACKAGE'){
                    self.$message.error(`组合条件:【${items.discount_condition_dec}】减元不能为空`);
                    return
                }
                if(items.discount_condition_dec.split('+').length === self.form.groups && self.form.package_type == 'FIXED_PRICE_PACKAGE' && items.subtract_money != 0) {
                    self.$message.error(`【套餐类型】为“固定价格套餐”，组合条件:【${items.discount_condition_dec}】减元必须为0`);
                    return
                }
            }
            if (self.form.status == "APPROVED") {
                if_adjust = self.compareSubmit();
            }
            this.$refs.form.validate((valid) => {
                if (valid) {
                    if (!!if_adjust) {
                        let param = {
                            callback: function (d) {
                                self.compare(d, callback);
                            },
                        };
                        this.$root.eventHandle.$emit("alert", {
                            params: param,
                            component: () => import("@components/discount/update_reason.vue"),
                            style: "width:420px;height:200px",
                            title: "修改原因",
                        });
                    } else {
                        self.compare(null, callback);
                    }
                }
            });
        },
        // 对比数据
        compare (resData, callback) {
            let self = this;
            new Promise((resolve, reject) => {
                self.formatListActDiscountConditionVo(function (arr) {
                    resolve && resolve({ arr: arr, callback: callback });
                });
            }).then((callData) => {
                let listActDiscountItemVo = JSON.parse(
                    JSON.stringify(self.listActDiscountItemVo)
                ).concat(self.delListActDiscountItemVo);
                listActDiscountItemVo.forEach((item) => {
                    delete item.color_sign;
                    if (item.__selected) {
                        delete item.__selected;
                    }
                });
                self.DiscountVo.forEach((item) => {
                    delete item.color_sign;
                    if (item.__selected) {
                        delete item.__selected;
                    }
                });
                self.cancelListDiscount.forEach((item) => {
                    delete item.color_sign;
                    if (item.__selected) {
                        delete item.__selected;
                    }
                });
                self.delListDiscount.forEach((item) => {
                    delete item.color_sign;
                    if (item.__selected) {
                        delete item.__selected;
                    }
                });

                const listActDiscountMaterialListVo=self.DiscountVo.concat(
                        self.cancelListDiscount
                    ).concat(self.delListDiscount)
                // 删除addId
                    listActDiscountMaterialListVo.forEach(item=>{
                      if(item.addId){
                        delete item.addId
                      }
                    })
                callData.arr.forEach((item) => {
                    delete item.changeFlag;
                    delete item.tid;
                });
                let data = {
                    actDiscountVo: {
                        ...self.form,
                        main_photo: !!self.filePath ? self.filePath : "",
                        //默认传参
                        if_pairs:"Y",
                        act_section:"DAILY_ACT"
                    },
                    listActDiscountConditionVo: callData.arr,
                    listActDiscountMaterialListVo: listActDiscountMaterialListVo,
                    listActDiscountShopVo: self.listPmsActShopVO.concat(
                        self._getAllShopsSearchData.del
                    ),
                    listActDiscountItemVo: listActDiscountItemVo.concat(
                        self.delListActDiscountItemVo
                    ),
                    if_change: self.form.if_change,
                };
                delete data.actDiscountVo.if_change
                // 如果resData存在则在保存时加入if_adjust、adjust_type、adjust_type_name三个字段
                if (!!resData) {
                    data.if_adjust = "Y";
                    data.adjust_type = resData.code;
                    data.adjust_type_name = resData.name;
                }
                if (self.isCopyAdd) {
                    data.if_copy_add = "Y";
                }
                self.save(data, callData.callback);
            });
            // 	}
            // })
        },

        compareSubmit () {
            let self = this;
            // discount_name
            // 优惠活动名称
            // enable_time
            // 生效时间
            // 失效时间disable_time
            // 时间维度discount_affection_date
            // 备注remark

            let formSubmit = {
                discount_name: self.form.discount_name,
                enable_time: self.form.enable_time,
                disable_time: self.form.disable_time,
                discount_affection_date: self.form.discount_affection_date,
                remark: self.form.remark,
                discount_kind: self.form.discount_kind,
                if_time_control: self.form.if_time_control,
                discount_category: self.form.discount_category,
            },
                initialSubmit = {
                    discount_name: self.initialData.discount_name,
                    enable_time: self.initialData.enable_time,
                    disable_time: self.initialData.disable_time,
                    discount_affection_date: self.initialData.discount_affection_date,
                    remark: self.initialData.remark,
                    discount_kind: self.initialData.discount_kind,
                    if_time_control: self.initialData.if_time_control,
                    discount_category: self.initialData.discount_category,
                };
            return this.compareData(formSubmit, initialSubmit);
        },
        preauditSuccess () {
            this.showDisableList((d) => {
                this.auditSuccess();
            });
        },
        showDisableList (data) {
            this.ajax.postStream(
                "/price-web/api/price/pms/page/findMaterialList?permissionCode=DISCOUNT_ACTIVITY_QUERY",
                data,
                (res) => {
                    if (res.body.content.code.length != 0) {
                        let params = {
                            callback (res) {
                                if (res == true) {
                                    return true;
                                }
                            },
                            data: res.body.content.code,
                        };
                        this.$root.eventHandle.$emit("alert", {
                            params: params,
                            component: () => import("@components/discount/disableList.vue"),
                            style: "width:600px;height:400px",
                            title:
                                "当前操作异常存在订单不可选物料��下，继续操作会删除以下物料，是否继续",
                        });
                    } else {
                        return true;
                    }
                }
            );
            // return;
        },
        submitAudit () {
            this.onAudit = true;
            this.ajax.postStream(
                "/price-web/api/actDiscount/commit?permissionCode=DISCOUNT_ACTIVITY_SUBMIT",
                {
                    list_discount_id: [this.form.discount_id],
                },
                (res) => {
                    if (res.body.result) {
                        this.getData();
                    }
                    this.$message({
                        type: res.body.result ? "success" : "error",
                        message: res.body.msg,
                    });

                    this.onAudit = false;
                },
                (err) => {
                    this.onAudit = false;
                    this.$message.error(err);
                }
            );
        },
        delOrder () {
            this.$confirm("当前操作会导致优惠活动被删除，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "danger",
            })
                .then(() => {
                    this.ajax.postStream(
                        "/price-web/api/actDiscount/delete?permissionCode=DISCOUNT_ACTIVITY_DELETE",
                        {
                            list_discount_id: [this.form.discount_id],
                        },
                        (res) => {
                            if (res.body.result) {
                                this.close();
                            }
                            this.$message({
                                type: res.body.result ? "success" : "error",
                                message: res.body.msg,
                            });

                            this.onAudit = false;
                        },
                        (err) => {
                            this.onAudit = false;
                            this.$message.error(err);
                        }
                    );
                })
                .catch(() => {
                    return false;
                });
        },
        withdrawAudit () {
            this.ajax.postStream(
                "/price-web/api/actDiscount/withdraw?permissionCode=DISCOUNT_ACTIVITY_WITHDRAW",
                {
                    list_discount_id: [this.form.discount_id],
                },
                (res) => {
                    if (res.body.result) {
                        this.getData();
                    }
                    this.$message({
                        type: res.body.result ? "success" : "error",
                        message: res.body.msg,
                    });

                    this.onAudit = false;
                },
                (err) => {
                    this.onAudit = false;
                    this.$message.error(err);
                }
            );
        },
        openPlanActivity () {
            this.ajax.postStream(
                "/price-web/api/actDiscount/openPlanActivity?permissionCode=DISCOUNT_ACTIVITY_OPEN",
                {
                    list_discount_id: [this.form.discount_id],
                },
                (res) => {
                    if (res.body.result) {
                        this.getData();
                    }
                    this.$message({
                        type: res.body.result ? "success" : "error",
                        message: res.body.msg,
                    });

                    this.onAudit = false;
                },
                (err) => {
                    this.onAudit = false;
                    this.$message.error(err);
                }
            );
        },
        forbidden () {
            this.ajax.postStream(
                "/price-web/api/actDiscount/forbidden?permissionCode=DISCOUNT_ACTIVITY_FORBID",
                {
                    list_discount_id: [this.form.discount_id],
                },
                (res) => {
                    if (res.body.result) {
                        this.getData();
                    }
                    this.$message({
                        type: res.body.result ? "success" : "error",
                        message: res.body.msg,
                    });

                    this.onAudit = false;
                },
                (err) => {
                    this.onAudit = false;
                    this.$message.error(err);
                }
            );
        },

        superpositionDiscount (val) {
            let self = this;
            if (self.form.status == "APPROVED") {
                return false;
            }
            if (!self.form.discount_id) {
                if (
                    !(
                        this.listActDiscountItemVo.length > 0 ||
                        this.conditionVo.length > 0 ||
                        this.DiscountVo.length > 0
                    )
                )
                    return;
                this.$confirm("当前操作会导致优惠条件被清空，是否继续？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "danger",
                })
                    .then(() => {
                        self.listActDiscountItemVo = [];
                        self.conditionVo = [];
                        self.DiscountVo = [];
                    })
                    .catch(() => {
                        return false;
                    });
            } else {
                self
                    .$confirm("当前操作会导致优惠条件被清空，是否继续？", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "danger",
                    })
                    .then(() => {
                        self.clearAllData();
                    })
                    .catch(() => {
                        return false;
                    });
            }
        },
        clearCondition (val) {
            let self = this;
            if (self.form.status == "APPROVED") {
                return false;
            }
            if (!self.form.discount_id) {
                if (
                    !(
                        this.listActDiscountItemVo.length > 0 ||
                        this.conditionVo.length > 0 ||
                        this.DiscountVo.length > 0
                    )
                )
                    return;
                this.$confirm("当前操作会导致优惠条件被清空，是否继续？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "danger",
                })
                    .then(() => {
                        self.listActDiscountItemVo = [];
                        self.conditionVo = [];
                        self.DiscountVo = [];
                    })
                    .catch(() => {
                        return false;
                    });
            } else {
                self
                    .$confirm("当前操作会导致优惠条件被清空，是否继续？", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "danger",
                    })
                    .then(() => {
                        self.clearAllData();
                    })
                    .catch(() => {
                        return false;
                    });
            }
        },
        shopsChanged (value) {
            this.shopsBtnControl();
            if (!this.shopsFlag) {
                this.shopsFlag = true;
                return;
            }
            this.shopAllDel();
        },

        //复制新增
        addCopeFun () {
            var self = this;
            this.ajax.postStream(
                "/price-web/api/actDiscount/getSerialNumberOrId",
                this.form.discount_id,
                (res) => {
                    if (res.body.result) {
                        this.$message.success("操作成功");
                    }
                }
            );
            return false;
            self.$root.eventHandle.$emit("creatTab", {
                name: "编辑优惠活动",
                params: {
                    actcopy_id: this.params.act_id
                        ? this.params.act_id
                        : this.form.act_id,
                },
                component: () => import("@components/discount/add.vue"),
            });
        },
        reflash () {
            this.getData();
        },

        exportSingleActDiscount () {
            let self = this;
            self.export_status = true;
            let data = {
                discount_id: this.form.discount_id,
            };

            self.ajax.postStream(
                "/price-web/api/actDiscount/exportSingleActDiscount?permissionCode=DISCOUNT_ACTIVITY_EXPORT",
                data,
                (res) => {
                    if (res.body.result) {
                        if (res.body.content) {
                            window.open(res.body.content);
                            self.$message.success(res.body.msg);
                        } else {
                            self.$message.error("不能重复操作");
                        }
                    } else {
                        self.$message.error(res.body.msg);
                    }

                    self.export_status = false;
                }
            );
        },
        // 获取活动信息,9.16
        getData (ifChange) {
            let self = this;
            // self.shopsFlag = false;
            var data = {
                discount_id: this.form.discount_id,
            };
            if (ifChange) {
                data = {
                    discount_id: this.params.row.discount_id,
                    discount_no: this.params.row.discount_no,
                    if_change: "Y",
                };
            }
            self.onReflash = true;
            // self.conditionVo = [];
            self.ajax.postStream(
                "/price-web/api/actDiscount/editPartInfo",
                data,
                (res) => {
                    self.onReflash = false;
                    if (res.body.result && res.body.content) {
                        self.ifGetAllData = false;
                        self.isAdd = false;
                        self.if_first_edit = true;
                        self.backup.store_area = self.form.store_area;
                        self.form = res.body.content.actDiscountVo;
                        self.form.if_change = "N";
                        let offlinePaymentModeCategoryList =
                            self.form.offline_payment_mode.split(",");
                        self.form.offline_payment_mode_name =
                            offlinePaymentModeCategoryList.reduce((name, cur, idx) => {
                                if (idx == offlinePaymentModeCategoryList.length - 1) {
                                    name += self.offlinePaymentModeCategoryObj[cur];
                                } else {
                                    name += self.offlinePaymentModeCategoryObj[cur] + ",";
                                }
                                return name;
                            }, "");
                        // 主图路径
                        self.filePath = res.body.content.actDiscountVo.main_photo
                        self.itemSearch.discount_id = self.form.discount_id;
                        self.shopSearch.discount_id = self.form.discount_id;
                        // 条件列表
                        self.conditionVo = res.body.content.listActDiscountConditionVo;
                        self.conditionSelect = null;
                        // 问题反馈自动获取单号
                        Vue.prototype.tabNo[self.params.tabName] =
                            res.body.content.actDiscountVo.discount_no || "";
                        setTimeout(function () {
                            for (let i = 0; i < self.conditionVo.length; i++) {
                                self.conditionVo[i].changeFlag = true;
                            }
                        }, 200);
                        self.listPmsActShopVO = [];
                        self.DiscountVo = res.body.content.listActDiscountMaterialListVo
                            ? res.body.content.listActDiscountMaterialListVo
                            : [];
                        self.changing_conditionVo = JSON.parse(
                            JSON.stringify(res.body.content.listActDiscountConditionVo)
                        );
                        self.getActItemList();
                        self._getAllShopsSearchData();
                        self.getDiscountMaterialList();
                        // self.shopSearching();
                        self.shopsBtnControl();
                        setTimeout(function () {
                            // 页面初始化数据，用作对比，是否有更新
                            for (var key in self.form) {
                                self.initialData[key] = self.form[key];
                            }
                            self.initialCondition = [];
                            self.conditionVo.forEach((item) => {
                                self.initialCondition.push(item);
                            });
                        }, 500);

                        self.delConditionVo = [];
                        self.cancelConditionVo = [];
                        self.delListActDiscountItemVo = [];
                        self.cancelListActDiscountItemVo = [];
                        self.cancelListDiscount = [];
                        self.delListDiscount = [];
                    } else {
                        self.$message.error("该优惠活动已被删除，请刷新列表重新打开");
                        self.$root.eventHandle.$emit("removeTab", self.params.tabName);
                    }
                }
            );
        },
        getDiscountMaterialList (resolve) {
            let self = this;
            // var data ={
            // 	discount_id: this.form.discount_id
            // };
            if (self.ifGetAllData) {
                self.$message.error("请先保存再继续操作");
                resolve && resolve();
                return false;
            }
            if (self.ifChange || self.isCopyAdd) {
                self.$message.error("未保存前不可查询");
                resolve && resolve();
                return;
            }
            self.goodsSearch.discount_id = this.form.discount_id;
            self.ajax.postStream(
                "/price-web/api/actDiscount/getDiscountMaterialList",
                self.goodsSearch,
                (res) => {
                    if (res.body.result && res.body.content) {
                        self.DiscountVo = res.body.content.list;
                        self.discountSelect = null;
                        self.goodCount = res.body.content.count;
                        self.initialMaterial = [];
                        self.DiscountVo.forEach((item) => {
                            self.initialMaterial.push(item);
                            if (
                                self.conditionSelect &&
                                self.conditionSelect.discount_condition_id ==
                                item.discount_condition_id
                            ) {
                                item.color_sign = "A";
                            } else {
                                item.color_sign = "";
                            }
                        });

                        self.DiscountVo = JSON.parse(JSON.stringify(self.DiscountVo));
                        resolve && resolve();
                    } else {
                        self.$message.error(res.body.msg);
                    }
                }
            );
        },
        // 校验是否整数
        fitterPrice (num) {
            var reg =
                /^(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/;
            if (!reg.test(num)) {
                this.$message.error("当前输入不符合规范，请输入1-7位小数点后两位数字");
            }
            return reg.test(num);
        },
        // 校验是否整数
        fitterPrice2 (num) {
            let number = "";
            var reg = /^[1-9][0-9]{0,6}$/;
            if (!reg.test(num)) {
                this.$message.error("当前输入不符合规范，请输入1-7位正整数");
                number = "";
            } else {
                number = num;
            }
            return number;
        },
        // 校验是否整数
        fitterPrice4 (num) {
            let number = "";
            var reg = /^[1-9]\d*$/;
            if (!reg.test(num)) {
                this.$message.error("当前输入不符合规范，请输入正整数");
                number = "";
            } else {
                number = num;
            }
            return number;
        },
        fitterPrice3 (num) {
            let reg = /^([1-9][0-9]{0,1}|100)$/;

            // reg = /^(([1-9][0-9]{0,6})|([1-9][0-9]+\.\d{1,2}))$/;
            if (!reg.test(num) && !!num) {
                this.$message.error("当前输入不符合规范，请输入1-100正整数");
                return "";
            } else {
                return num;
            }
        },
        getActItemList () {
            let self = this;
            var data = {
                discount_id: this.form.discount_id,
            };
            self.ajax.postStream(
                "/price-web/api/actDiscount/getDiscountItem",
                self.itemSearch,
                (res) => {
                    if (res.body.result && res.body.content) {
                        // self.listActDiscountItemVo = [];
                        self.listActDiscountItemVo = res.body.content.list;
                        self.itemCount = res.body.content.count;
                    } else {
                        self.$message.error(res.body.msg);
                    }
                }
            );
        },
        getDiscountShop (resolve) {
            let self = this;
            var data = {
                discount_id: this.form.discount_id,
                page_size: self.params.shopCount,
                where: [
                    {
                        field: "fb7f1d453211c71a9de8e6bdcf80f29c",
                        table: "78fda2e42deeff769e661a412023c93b",
                        value: "0",
                        operator: "=",
                        condition: "AND",
                        listWhere: [],
                    },
                ],
            };
            self.ajax.postStream(
                "/price-web/api/actDiscount/getDiscountShop",
                data,
                (res) => {
                    if (res.body.result && res.body.content) {
                        resolve && resolve(res.body.content.list);
                        // self.listPmsActShopVO = res.body.content.list;
                        // self.markListPmsShopVOData = JSON.parse(JSON.stringify(res.body.content.list));
                    } else {
                        self.$message.error(res.body.msg);
                    }
                }
            );
        },
        initCopyAdd () {
            let self = this;
            this.isCopyAdd = true;
            this.form = this.params.form;
            let offlinePaymentModeCategoryList =
                this.form.offline_payment_mode.split(",");
            this.form.offline_payment_mode_name =
                offlinePaymentModeCategoryList.reduce((name, cur, idx) => {
                    if (idx == offlinePaymentModeCategoryList.length - 1) {
                        name += this.offlinePaymentModeCategoryObj[cur];
                    } else {
                        name += this.offlinePaymentModeCategoryObj[cur] + ",";
                    }
                    return name;
                }, "");
            this.backup.discount_condition_type = this.form.discount_condition_type;
            this.backup.store_area = this.form.store_area;

            this.form.status = "CREATE";
            this.conditionVo = this.params.conditionVo;
            this.listActDiscountItemVo = this.params.listActDiscountItemVo;
            this.DiscountVo = this.params.DiscountVo.map(item=>{
              return{
                ...item,
                addId:Fn.guid('goods'),
              }
            });
            this.listPmsActShopVO = this.params.listPmsActShopVO;
            this.filePath =  this.form.main_photo
            setTimeout(function () {
                for (let i = 0; i < self.conditionVo.length; i++) {
                    self.conditionVo[i].changeFlag = true;
                }
            }, 200);
        },
        itemClick () {
            return false;
        },

        /*
        数据对比，用于关闭时提示数据是否有变动
        */
        close () {
            var self = this,
                isUpdate,
                materialIsUpdate,
                conditionIsUpdate;
            isUpdate = this.compareData(self.form, self.initialData);
            conditionIsUpdate = this.compareData(
                self.conditionVo,
                self.initialCondition
            );
            materialIsUpdate = this.compareData(
                self.DiscountVo,
                self.initialMaterial
            );
            if (isUpdate || conditionIsUpdate || materialIsUpdate) {
                self.$root.eventHandle.$emit("openDialog", {
                    ok () {
                        self.presave(() => {
                            self.$root.eventHandle.$emit("removeTab", self.params.tabName);
                        });
                    },
                    no () {
                        self.$root.eventHandle.$emit("removeTab", self.params.tabName);
                    },
                });
            } else {
                self.$root.eventHandle.$emit("removeTab", self.params.tabName);
            }
        },
        tableRowClassName (row) {
            /*return 'red';*/
            switch (row.color_sign) {
                case "A":
                    return "discount_blue";
            }
        },
        winningChange () {
            if (!!this.form.discount_id) {
                this.form.winningChange = "N";
            }
        },

        getActRuleList () {
            let self = this;
            self.ajax.postStream(
                "/price-web/api/actDiscount/getActRuleList",
                {
                    pageNo: 1,
                    pageSize: 50,
                    if_enable: "Y",
                },
                (res) => {
                    if (res.body.result && res.body.content) {
                    } else {
                        self.$message.error(res.body.msg);
                    }
                }
            );
        },
        expList () {
            let self = this;
            this.$root.eventHandle.$emit("alert", {
                component: () => import("@components/discount/export"),
                style: "width:900px;height:600px",
                title: "中奖名单下载",
                params: {
                    query: {
                        type: "EXCEL_TYPE_WIN_LIST_REPORT_EXPORT",
                        table_primary_id: self.form.discount_id,
                    },
                },
            });
        },
        // 删除条件+项目+清单
        clearAllData () {
            // new Promise((afterRes,rej)=>{

            // })
            let self = this;
            new Promise((resolve, reject) => {
                self.getAllData(resolve);
            }).then(() => {
                let i = self.conditionVo.length;
                while (i--) {
                    if (!!self.conditionVo[i].discount_condition_id) {
                        self.deleteItem(self.conditionVo[i].discount_condition_id);
                        self.deleteGoods(self.conditionVo[i].discount_condition_id);
                        if (!self.isCopyAdd && !self.ifChange) {
                            self.delConditionVo.push(self.conditionVo[i]);
                        }
                        self.conditionVo.splice(
                            self.conditionVo.indexOf(self.conditionVo[i]),
                            1
                        );
                    } else {
                        self.conditionVo.splice(
                            self.conditionVo.indexOf(self.conditionVo[i]),
                            1
                        );
                    }
                }
                // self.conditionVo.forEach((item)=>{
                // 	self.deleteItem(item.discount_condition_id);
                // 	self.deleteGoods(item.discount_condition_id);
                // 	// self.conditionVo.splice(self.conditionVo.indexOf(self.conditionSelect), 1)
                // })
            });
        },
    },
    created () {
        this.autoDiscountSwitch = __AUX
            .get("SALE_ORDER")
            .filter((item) => item.code === "AUTO_DISCOUNT_SWITCH")[0].tag;
        this.getAutoDiscountSwitchTime();
        // 风格
        this.styles = __AUX.get("discount_style_type")
        // 空间
        this.space = __AUX.get("discount_space_type")
        // 面积
        this.areas = __AUX.get("discount_dimension_type")

    },
    mounted () {
        var self = this;
        // return false;
        self.$root.eventHandle.$emit("doTab", self.params.tabName);

        self.shopsBtnControl();
        self.getActRuleList();
        // 变更
        if (this.params.isAdd) {
            this.isChangingAdd = this.params.isAdd;
            this.initChanging();
            return false;
        }
        if (this.params.isCopyAdd) {
            this.initCopyAdd();
            return false;
        }
        if (this.params.discount_id) {
            if (this.params.if_change == "Y") {
                this.getData(true);
                return false;
            }
            this.isAdd = false;
            this.form.discount_id = this.params.discount_id;
            this.itemSearch.discount_id = this.params.discount_id;
            this.shopSearch.discount_id = this.params.discount_id;
            this.getData();
            this.ifSaved=true
            // 获取商品数据
            // this.goodsSearching()
            // 获取店铺数据
            // this.shopSearching()
        }
        this.form.package_type = 'FIXED_PRICE_PACKAGE'

        this.params.__data = JSON.parse(JSON.stringify(this.form));
        this.params.__close = this.close;
    },
    watch: {
        "form.if_time_control": function (newVal, oldVal) {
            if (newVal === "N") {
                this.form.time_control_value = 0;
            }
        },
        conditionVo (newVal, oldVal) {
            if (!newVal) {
                this.discountitem = [];
            }
        },
        "form.discount_kind": function (newVal, oldVal) {
            if (newVal != oldVal && oldVal != "") {
                this.changeDiscountKind();
            }
        },
        "form.discount_category": function (newVal, oldVal) {
            if (newVal != oldVal && !!oldVal) {
                this.changeDiscountCategory(this.form.discount_category);
            }
        },
        "form.package_type": function (newVal, oldVal) {
            if (newVal != oldVal) {
                this.changePackageType(this.form.package_type);
            }
        },
    },
    computed: {
        ifChange () {
            // 是否变更未保存
            return this.form.if_change == "Y" ? true : false;
        },
        if_enable_time () {
            if (this.form.enable_time) {
                return new Date().getTime() > this.form.enable_time ? true : false;
            } else {
                return false;
            }
        },
        store_area () {
            return this.form.store_area;
        },
        listBtnControl () {
            return !(this.form.status == "CREATE" || this.form.status == "REJECTED");
        },
        // 创建和驳回状态可修改 || 单据状态为copy则禁用
        formDIsabledControl () {
            return (
                !(this.form.status == "CREATE" || this.form.status == "REJECTED") ||
                this.form.document_type == "COPY"
            );
        },
        // 优惠活动状态不为“创建”或“驳回”时，“维护中奖名单”按钮不允许操作
        formDIsabledControl4 () {
            return (
                !(this.form.status == "CREATE" || this.form.status == "REJECTED") ||
                this.form.document_type == "COPY"
            );
        },
        formDIsabledControl2 () {
            return (
                !(
                    this.form.status == "CREATE" ||
                    this.form.status == "REJECTED" ||
                    this.form.status == "APPROVED"
                ) || this.form.document_type == "COPY"
            );
        },
        // 创建状态可修改 || 单据状态为copy则禁用
        formDIsabledControl3 () {
            return (
                !(this.form.status == "CREATE") || this.form.document_type == "COPY"
            );
        },
        // 是否复制
        ifCopy () {
            return this.form.document_type == "COPY";
        },
        ifCreateAndRejectOfStatus () {
            return /^(CREATE|REJECTED)$/.test(this.form.status);
        },
        ifCreateAndRejectAndCheckedOfStatus () {
            return /^(CREATE|REJECTED|APPROVED)$/.test(this.form.status);
        },
        ifApprovedOfStatus () {
            return /^(APPROVED)$/.test(this.form.status);
        },
        ifCreateOfStatus () {
            return /^(CREATE)$/.test(this.form.status);
        },
        ifGlobalStore() {
            return this.form.store_area === 1
        }
    },
};
</script>
<style>
.discount_blue {
    background-color: #dee5f0 !important;
}

.discount_blue td {
    background-color: inherit !important;
}
</style>
<style lang="stylus">
.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom {
  overflow: auto;
}

.discount-list {
  .xpt-flex__bottom {
    .xpt-pagation {
      position: absolute;
    }
  }

  .xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom {
    overflow: hidden;
    padding-bottom: 35px;
  }
}

.discount-list {
  .xpt-flex .el-table__body-wrapper, .el-table .el-table__body-wrapper {
    min-height: 100px;
    max-height: 150px;
    overflow-x: hidden;
  }
}
.image-list :global(.xpt-image__body) {
  width: 100% !important;
  height: 100% !important;
}
</style>
