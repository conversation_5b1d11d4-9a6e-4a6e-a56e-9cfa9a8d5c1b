<!-- 搭配购活动列表 -->
<template>
    <xpt-list ref="newDiscountList" :data="tableData" :btns="btns" :colData="cols" :pageTotal="totalPage"
        searchPage="act_discount" searchHolder="请输入查询条件" @search-click="preSearch"
        @selection-change="handleSelectionChange" @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange">
    </xpt-list>
</template>
<script>
export default {
    data () {
        var self = this;
        return {
            btns: [
                {
                    type: "primary",
                    txt: "新增",
                    click: self.addFun,
                },

                {
                    type: "success",
                    txt: "提交",
                    loading: false,
                    click () {
                        self.submitFun("commit", "提交", "sub");
                    },
                },
                {
                    type: "success",
                    txt: "刷新",
                    loading: false,
                    disabled: false,
                    click () {
                        self.searchFun();
                    },
                },
            ],
            cols: [
                {
                    label: "搭配购活动编码",
                    prop: "discount_no",
                    width: 150,
                },
                {
                    label: "搭配购名称",
                    prop: "discount_name",
                    redirectClick (row) {
                        self.toEditFun(row);
                    },
                    width: 150,
                },
                {
                    label: "状态",
                    prop: "status",
                    formatter (val) {
                        switch (val) {
                            case "CREATE":
                                return "创建";
                                break;
                            case "SUBMITTED":
                                return "提交审核";
                                break;
                            case "APPROVED":
                                return "已审核";
                                break;
                            case "WAIT_APPROVED":
                                return "待审核";
                                break;
                            case "REJECTED":
                                return "已驳回";
                                break;
                            case "WITHDRAWED":
                                return "已撤回";
                                break;
                            case "RETRIAL":
                                return "重新审核";
                                break;
                            case "FORBIDDEN":
                                return "禁用";
                                break;
                            case "FORBID":
                                return "禁用";
                                break;
                            case "APPROVING":
                                return "审核中";
                                break;
                            default:
                                return val;
                                break;
                        }
                    },
                },
                {
                    label: "风格",
                    prop: "style",
                    formatter (val) {
                        let _val = !!val ? self.styles.filter(item => item.code == val)[0].name : ""
                        return _val
                    }
                },
                {
                    label: "空间",
                    prop: "space",
                    formatter (val) {
                        let _val = !!val ? self.space.filter(item => item.code == val)[0].name : ""
                        return _val
                    }
                },
                {
                    label: "生效时间",
                    prop: "enable_time",
                    format: "dataFormat1",
                },
                {
                    label: "失效时间",
                    prop: "disable_time",
                    format: "dataFormat1",
                },
                {
                    label: "审核人",
                    prop: "audit_person_name",
                },
                {
                    label: "审核日期",
                    prop: "audit_time",
                    format: "dataFormat1",
                    width: 140,
                },
            ],
            searchObj: {
                page_size: self.pageSize,
                page_no: 1,
                page_name: "act_discount",
                where: [],
            },
            totalPage: 0,
            searchInput: "",
            tableData: [],
            selectId: [],
            selectData: [],
            onAudit: false,
            searchKey: "",
            styles: [],//风格
            space: [],//空间
        };
    },
    props: ["params"],
    created () {
        // 风格
        this.styles = __AUX.get("discount_style_type")
        // 空间
        this.space = __AUX.get("discount_space_type")
    },
    mounted () {
        let list = __AUX.get("goods_discount_category"),
            auxList = [];
        for (let i = 0; i < list.length; i++) {
            let obj = {
                code: list[i].code,
                name: list[i].name,
            };
            auxList.push(obj);
        }
        let onlineList = __AUX.get("offline_payment_mode").reduce((a, b) => {
            let obj = {
                code: b.code,
                name: b.name,
            };
            a.push(obj);
            return a;
        }, []);
        this.$refs.newDiscountList.$refs.xptSearchEx.filterFields = (fields) => {
            fields.forEach((item) => {
                if (item.comment == "商品活动类目") {
                    item.values = auxList;
                } else if (item.comment == "线下货款模式") {
                    item.values = onlineList;
                }
            });
            return fields;
        };
        var self = this;
        // self.searchFun();
    },
    destroyed () {
        this.$root.offEvents("discountAdd");
    },
    methods: {
        addFun () {
            this.$root.eventHandle.$emit("creatTab", {
                name: "新建搭配购活动",
                component: () =>
                    import("./addOrUpdate.vue"),
            });
        },
        delFun () {
            var self = this;
            if (self.selectId.length === 0) {
                self.$message.error("请选择要删除的活动优惠");
            }
            var data = {
                list_discount_id: self.selectId,
            };
            var resObj = self.ajaxPost(
                "delete?permissionCode=DISCOUNT_ACTIVITY_DELETE",
                data,
                "删除成功",
                "del"
            );
        },
        forbidFun (api, tip, name) {
            var self = this;
            if (self.selectId.length === 0) {
                self.$message.error("请选择要" + tip + "的活动优惠");
                return;
            }
            var data = {
                list_discount_id: self.selectId,
            };
            self.ajaxPost(api, data, tip + "成功", name);
        },
        clickFun (api, tip, name) {
            var self = this;

            if (self.selectId.length === 0) {
                self.$message.error("请选择要" + tip + "的活动优惠");
                return;
            }
            var data = {
                list_discount_id: self.selectId,
            };

            self.ajaxPost(api, data, tip + "成功", name);
        },
        submitFun () {
            let list = [];
            let isPass = true;
            if (!this.selectData.length) {
                this.$message.error("没有选择任何数据");
                return false;
            }
            this.selectData.forEach((item) => {
                // if(!(item.status=='CREATE'||item.status=='REJECTED'||(item.status=='APPROVED'&&item.status=='CHANGE_CREATE')||(item.status=='APPROVED'&&item.status=='CHANGE_REJECTED'))){

                // }
                if (item.status == "CREATE" || item.status == "REJECTED") {
                    list.push(item.discount_id);
                } else if (
                    (item.status == "APPROVED" &&
                        item.change_status == "CHANGE_CREATE") ||
                    (item.status == "APPROVED" && item.change_status == "CHANGE_REJECTED")
                ) {
                    list.push(item.change_discount_id);
                    // console.log(item);
                } else {
                    isPass = false;
                }
            });
            if (!isPass) {
                this.$message.error("存在非创建状态或驳回状态单据，请重新选择");
                return false;
            }
            this.onAudit = true;
            this.ajax.postStream(
                "/price-web/api/actDiscount/commit?permissionCode=DISCOUNT_ACTIVITY_SUBMIT",
                {
                    list_discount_id: list,
                },
                (res) => {
                    if (res.body.result) {
                        this.searchFun();
                    }
                    this.$message({
                        type: res.body.result ? "success" : "error",
                        message: res.body.msg,
                    });

                    this.onAudit = false;
                },
                (err) => {
                    this.onAudit = false;
                    this.$message.error(err);
                }
            );
        },
        ajaxPost (api, data, tip, name, resolve) {
            data={
                ...data,
                if_search_pairs:"Y",//是否搭配购
            }
            var self = this;
            var url = "/price-web/api/actDiscount/" + api;
            let setStatus = (n, status) => {
                // switch(n) {
                // 	case 'del': this.btns[1].loading = status; break;
                // 	case 'sub': this.btns[2].loading = status; break;
                // 	case 'recall': this.btns[3].loading = status; break;
                // 	case 'audit': this.btns[4].loading = status; break;
                // 	case 'back': this.btns[5].loading = status; break;
                // 	// case 'ban': this.btns[6].loading = status; break;
                // 	default: break;
                // }
            };
            setStatus(name, true);
            this.ajax.postStream(
                url,
                data,
                (res) => {
                    if (res.body.result) {
                        if (name === "search") {
                            self.tableData = res.body.content.list;
                            self.totalPage = res.body.content.count;
                        } else {
                            self.$message.success(tip);
                            self.searchFun();
                        }
                    } else {
                        self.$message.error(res.body.msg || "");
                    }
                    resolve && resolve();
                    setStatus(name, false);
                },
                (res) => {
                    resolve && resolve();
                    self.$message.error(res);
                    setStatus(name, false);
                }
            );
        },
        preSearch (txt, resolve) {
            this.searchObj.where = txt;
            this.ajaxPost("list", this.searchObj, "", "search", resolve);
        },
        // initSearchData(){
        // 	if(!this.searchKey) this.searchObj.act_name = ''
        // 	else this.searchObj.act_name = this.searchKey
        // },
        searchFun () {
            this.ajaxPost(
                "list?permissionCode=DISCOUNT_ACTIVITY_QUERY",
                this.searchObj,
                "",
                "search"
            );
        },
        handleSelectionChange (selectArr) {
            var self = this;
            self.selectId = [];
            self.selectData = selectArr;
            selectArr.map((v) => {
                self.selectId.push(v.discount_id);
            });
        },
        handleSizeChange (val) {
            this.searchObj.page_size = val;
            // this.initSearchData()
            this.searchFun();
        },
        handleCurrentChange (val) {
            this.searchObj.page_no = val;
            // this.initSearchData()
            this.searchFun();
        },

        toEditFun (row) {
            this.$root.eventHandle.$emit("creatTab", {
                name: "编辑搭配购活动",
                params: { discount_id: row.discount_id, row: row, if_change: "N" },
                component: () => import("./addOrUpdate"),
            });
        },
        toEditFun2 (row) {
            console.log(row.change_status);
            if (row.change_status == "CHANGE_APPROVED") {
                this.$message.error("已审核变更单不能查看");
                return;
            }
            this.$root.eventHandle.$emit("creatTab", {
                name: "编辑变更优惠活动",
                params: { discount_id: row.discount_id, row: row, if_change: "Y" },
                component: () => import("@components/discount/newDiscunt.vue"),
            });
        },

        ifGlobalFun (val) {
            if (val == 1) {
                return "全局";
            } else {
                return "店铺";
            }
        },
    },
};
</script>