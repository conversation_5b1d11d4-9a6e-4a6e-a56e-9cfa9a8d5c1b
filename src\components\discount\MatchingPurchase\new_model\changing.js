/*
优惠活动--变更
*/
import Fn  from '@common/Fn.js'
import { resolve } from 'path';
import { reject, promise } from 'when';
export default {
	data() {
		let self = this



		return {

		}
	},
	methods: {
		changing(){
			var self = this;
			new promise((resol,rej)=>{
				self.itemPageSizeChange(self.itemCount,resol)
			}).then(()=>{
				new promise((resolve,reject)=>{
					self.goodsPageSizeChange(self.goodCount,resolve);
				}).then(()=>{
					this.ajax.postStream('/price-web/api/actDiscount/getSerialNumberOrId', this.conditionVo.length,res=>{
						if(res.body.result) {

							let conditionIds = [];
							for(var i in res.body.content){
								if(i.indexOf('condition_id') != -1){
									conditionIds.push(res.body.content[i]);
								}
							}
							self.if_change_once = true;
							self.$root.eventHandle.$emit('creatTab',{name:'变更优惠活动'
							,params:{
								actcopy_id:res.body.content.header_id,
								form:self.form,
								isAdd:true,
								shopCount:this.shopCount,
								conditionIds:conditionIds,
								conditionVo:self.conditionVo,
								DiscountVo:self.DiscountVo,
								listPmsActShopVO:self.listPmsActShopVO,
								listActDiscountItemVo:self.listActDiscountItemVo,
							 }
							,component:()=>import('@components/discount/newDiscunt.vue')});

						}
					});
				})
			})


		},
		initChanging(){
			let self = this;
			this.form = this.params.form;
			this.form.status = 'CREATE';
			this.form.change_status = 'CHANGE_CREATE';
			this.conditionVo =  JSON.parse(JSON.stringify(this.params.conditionVo));
			this.cancelConditionVo = [];
			this.delConditionVo = [];
			this.DiscountVo = [];
			// console.log(this.params.conditionVo)
			// this.listPmsActShopVO = this.params.listPmsActShopVO;
			new Promise((resolve,reject)=>{
				this.getDiscountShop(resolve);
			}).then((list)=>{
				this.listPmsActShopVO = [];
				list.forEach(item =>{
					item.discount_id = this.params.actcopy_id;
					item.discount_no = this.params.form.discount_no;
					item.discount_shop_id = '';
					this.listPmsActShopVO.push(item);
				})
			})
			this.listActDiscountItemVo = JSON.parse(JSON.stringify(this.params.listActDiscountItemVo));
			this.isAdd = false;
			// this.DiscountVo = JSON.parse(JSON.stringify(this.params.DiscountVo));
			self.params.DiscountVo.forEach((goodsItem,goodsInd) =>{
				this.DiscountVo.push({
						material_id:goodsItem.material_id,
						material_number:goodsItem.material_number,
						if_enjoy_discount:goodsItem.if_enjoy_discount,
						copy_version_id :goodsItem.discount_material_list_id,
						disable_person_id:'',
						disable_person_name:'',
						disable_time:'',
						row_status:goodsItem.row_status,
						status:goodsItem.status,
						win_goods_id:goodsItem.win_goods_id,
						change_type:'RETAIN',
            addId:Fn.guid('goods'),
				})
			})
			// 优惠条件id替换
			// console.log(this.params.DiscountVo)
			self.conditionVo.forEach((item,index) => {
				// 优惠项目中对应的条件id替换
				this.listActDiscountItemVo.forEach(actItem=>{
					if(item.discount_condition_id == actItem.discount_condition_id){
						actItem.copy_version_id = actItem.discount_item_id;
						actItem.discount_item_id ='';
						actItem.discount_condition_id = this.params.conditionIds[index];
						actItem.change_type = 'RETAIN';

					}
				})
				self.params.DiscountVo.forEach((goodsItem,goodsInd) =>{
					// console.log('discount_condition_id+',item.discount_condition_id,goodsItem.discount_condition_id,this.params.conditionIds[index])
					if(item.discount_condition_id == goodsItem.discount_condition_id){

						self.DiscountVo[goodsInd] = {
							material_id:goodsItem.material_id,
							material_number:goodsItem.material_number,
							discount_condition_id:this.params.conditionIds[index],
							copy_version_id :goodsItem.discount_material_list_id,
							if_enjoy_discount :goodsItem.if_enjoy_discount,
							disable_person_id:'',
							disable_person_name:'',
							disable_time:'',
							row_status:goodsItem.row_status,
							status:goodsItem.status,
							change_type:'RETAIN'
						}
						// console.log(self.DiscountVo[goodsInd]);
					}

				})
				// console.log(this.DiscountVo,'1111111111111111111111',this.conditionVo);
				// 变更时候把条件id赋值到discount_condition_id，把本来的条件id默认赋值到接口取来的条件id
				item.copy_version_id = item.discount_condition_id;
				item.discount_condition_id = this.params.conditionIds[index];
				item.change_type = 'RETAIN';
			});
			// this.params.DiscountVo.forEach(item =>{
			// 	this.DiscountVo.push( {
			// 		material_id:item.material_id,
			// 		material_number:item.material_number,
			// 		copy_version_id :item.discount_material_list_id,
			// 		disable_person_id:'',
			// 		disable_person_name:'',
			// 		disable_time:'',
			// 		row_status:item.row_status,
			// 		status:item.status,
			// 		change_type:'RETAIN'
			// 	})
			// })
			this.form.discount_id = this.params.actcopy_id;
			this.form.change_status = 'CHANGE_CREATE';
			this.form.if_change = 'Y';
			this.form.document_type = 'COPY';
			setTimeout(function(){
				for(var key in self.form){
					self.initialData[key] = self.form[key]
				}

				// console.log(self.initialData)
			},500)
		},

		// 获取保存时条件传进去的数据
		formatListActDiscountConditionVo(callback){
			let self = this;
			let listActDiscountConditionVo = [];
			let arr = [];
			let itemArr = [];
			let discountItemArr = [];
			// console.log(self.params.conditionVo,this.isChangingAdd,self.form.document_type,'1111111111111111')
			if(self.form.if_change == 'Y'){
				// 当是变更订单时
				// new Promise((resolve, reject) => {
					let copy_condition_vo = JSON.parse(JSON.stringify(self.params.conditionVo));
					self.conditionVo.forEach(item =>{
						copy_condition_vo.forEach(copyItem =>{
							// 当优惠条件改变时，原有的条件变成取消，现有的作为新增
							if(item.copy_version_id == copyItem.discount_condition_id ){
								if(item.threshold_price != copyItem.threshold_price || item.item_enable_options != copyItem.item_enable_options || item.item_enable_count != copyItem.item_enable_count || item.threshold_count != copyItem.threshold_count){
									copyItem.copy_version_id = item.copy_version_id;
									copyItem.discount_condition_id = item.discount_condition_id;
									// item.copy_version_id =
									self.cancelConditionVo.push(copyItem);
									itemArr.push(item);
									arr.push(self.getListConditonId());
								}
							}
						})
					})
			}
			if(self.form.document_type == 'COPY'){
				// 当是变更订单时
				let copy_condition_vo =  self.changing_conditionVo
					self.conditionVo.forEach(item =>{
						copy_condition_vo.forEach(copyItem =>{
							// 当优惠条件改变时，原有的条件变成取消，现有的作为新增

							if(item.discount_condition_id == copyItem.discount_condition_id &&item.change_type == 'RETAIN'){
								if(item.threshold_price != copyItem.threshold_price || item.item_enable_options != copyItem.item_enable_options || item.item_enable_count != copyItem.item_enable_count || item.threshold_count != copyItem.threshold_count){
									copyItem.copy_version_id = item.copy_version_id;
									copyItem.discount_condition_id = item.discount_condition_id;
									self.cancelConditionVo.push(copyItem);
									itemArr.push(item);
									arr.push(self.getListConditonId());
								}

							}
						})
					})
			}

			self.conditionVo.forEach(item =>{
				if(!item.discount_id){
					// 新增条件
					listActDiscountConditionVo.push({
					// 生效金额门槛
					threshold_price:item.threshold_price,
					// 门槛件数
					threshold_count:item.threshold_count,
					// 对应项目内容生效件数
					item_enable_count:item.item_enable_count,
					// 生效选项
					item_enable_options:item.item_enable_options,
					document_type:item.document_type,
					change_type:item.change_type,
					row_status:item.row_status,
					discount_condition_id:!!item.discount_condition_id ? item.discount_condition_id : ''
					})
				}else{
					// 保存条件
					listActDiscountConditionVo.push(item);
				}
			})
			// // 把取消条件写入要传进去的数组
			self.cancelConditionVo.forEach(item =>{
				item.change_type = 'CANCEL';
				item.row_status = 1;
				listActDiscountConditionVo.push(item)
				self.updateItem(item.discount_condition_id);

			})
			// 把删除条件写入要传进去的数组
			self.delConditionVo.forEach(item =>{
				item.row_delete_flag = 'Y';
				listActDiscountConditionVo.push(item)
			})


			Promise.all(arr).then((res) => {
				// console.log(res,itemArr);
				let newListActDiscountItemVo = [];
				itemArr.forEach((item,index) => {
					let originId = item.discount_condition_id;
					item.discount_condition_id = res[index];
					item.copy_version_id = '';
					item.change_type = 'ADD';
					item.row_status = 0;
					self.listActDiscountItemVo.forEach(discountItem => {
						if(discountItem.discount_condition_id == originId) {
							let newDiscountItem = JSON.parse(JSON.stringify(discountItem));
							newDiscountItem.discount_condition_id = res[index];
							newDiscountItem.change_type = 'ADD';
							newDiscountItem.discount_item_id = '';
							newDiscountItem.copy_version_id = '';
							newDiscountItem.row_status = 0;
							newListActDiscountItemVo.push(newDiscountItem);
						}
					});
				});
				self.listActDiscountItemVo = self.listActDiscountItemVo.concat(newListActDiscountItemVo);
				callback(listActDiscountConditionVo);
			}).catch(error => {
				// console.log(error.message);
			});
		},
		getChangingDiscountId(){
			this.ajax.postStream('/price-web/api/actDiscount/getSerialNumberOrId', this.conditionVo.length,res=>{
				if(res.body.result) {
					let conditionIds = [];
					for(var i in res.body.content){
						if(i.indexOf('condition_id') != -1){
							conditionIds.push(res.body.content[i]);
						}
					}
				}
			})
		},
		getItem(conditionId,newId){
			let self = this;
			// console.log(conditionId,newId)
			let list = [];
			self.listActDiscountItemVo.forEach(item=>{
				// console.log(item.discount_condition_id,conditionId)
				if(item.discount_condition_id = conditionId){
					list.push({
						"discount_condition_id":newId,
						"discount_item_type":item.discount_item_type,//优惠项目编码
						"item_type_detail":item.item_type_detail,//项目细类
						"subtract_money":item.subtract_money,
						win_order:item.win_order,
						win_quota:item.win_quota,
						"discount":item.discount,
						"material_id":item.material_id,
						"material_number":item.material_number,
						"material_name":item.material_name,
						"material_specification":item.material_specification,
						"material_unit":item.material_unit,
						if_gift:item.if_gift,
						"if_present":item.if_present,
						"act_price":item.act_price,
						"cost_price":item.cost_price,
						"three_reduction":item.three_reduction,
						"logistics_reduction":item.logistics_reduction,
						"count_limit":item.count_limit,
						"person_bear_ratio":item.person_bear_ratio,
						"if_enjoy_discount":item.if_enjoy_discount,
						"row_status":0,
						change_type:self.ifChange?'ADD':'',
						"disable_person_id":item.disable_person_id,
						"disable_person_name":item.disable_person_name,
						"disable_time":item.disable_person_id,
						discount_condition_dec:item.discount_condition_dec,
						discount_item_dec:item.discount_item_dec,
						discount_amount:item.discount_amount,
						"remark":item.remark
					})
				}
			});
			self.listActDiscountItemVo = self.listActDiscountItemVo.concat(list)
		}
	}
}
