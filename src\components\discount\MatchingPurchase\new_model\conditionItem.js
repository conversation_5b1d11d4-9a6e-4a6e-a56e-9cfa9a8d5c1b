/*
搭配购活动--优惠项目
*/
export default {
    data () {
        let self = this
        return {
            itemCount: 0,
            discountItemSelect: [],
            discountItemBtns: [
                {
                    type: 'info',
                    txt: '失效',
                    disabled(){
						return !(self.form.status == "APPROVED"  && self.if_enable_time ) 
                     },
                    click: self.discountItemDisable 
                }
            ],
            discountItemCols: [
                {
                    label: '项目编号',
                    prop: 'discount_item_no',
                    width: 150,
                },
                {
                    label: '组合条件',
                    prop: 'discount_condition_dec',
                },
                {
                    label: '减元',
                    slot: 'subtract_money',
                    width: 250,
                },
                 {
                    label: '项目内容',
                    prop: 'discount_item_dec',
                    width: 250,
                }, {
                    label: '创建时间',
                    prop: 'create_time',
                    width: 300,
                    format: 'dataFormat1'
                }, {
                    label: '创建人',
                    prop: 'creator_name',
                },
            ],
            itemSearch: {
                page_name: "act_discount_item",
                where: [],
                page_size: this.pageSize,
                page_no: 1,
                discount_id: ''
            },
        }
    },
    methods: {
        // 优惠项目失效
        discountItemDisable(){
            let self = this;
            if(!self.discountItemSelect||!self.discountItemSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
            }
            if(!self.discountItemSelect) return false;            
			self.discountItemSelect.forEach(selectItem=>{
				self.listActDiscountItemVo.forEach((item,index)=>{
					if(selectItem.discount_item_id == item.discount_item_id){
                        item.row_status = 1;
                        self.$set(item, 'row_status', 1);
                        console.log(item.row_status);
					}
				})
				
            })
            // self.$refs['discount_GoodsList'].updateTable();
        },
        itemPresearch (list, resolve) {
            this.itemSearch.where = list;
            this.itemSearching(resolve);
        },
        itemPageSizeChange (size, resolve) {
            this.itemSearch.page_size = size
            this.itemSearching(resolve)
        },
        itemPageChange (page_no, resolve) {
            this.itemSearch.page_no = page_no
            this.itemSearching(resolve)
        },
        // 导出
        download () { },
        itemSelectionChange (selectArr) {
            this.discountItemSelect = selectArr
        },

        // 获取优惠商品列表
        itemSearching (resolve) {
            let _this = this
            if (_this.ifGetAllData) {
                _this.$message.error('请先保存再继续操作');
                resolve && resolve();
                return false;
            }
            if ((_this.ifChange || _this.isCopyAdd)) {
                _this.$message.error('未保存前不可查询');
                resolve && resolve();
                return
            }
            if (!_this.itemSearch.discount_id) {
                resolve && resolve();
                return
            }
            this.ajax.postStream('/price-web/api/actDiscount/getDiscountItem',
                _this.itemSearch,
                res => {
                    if (res.body.result && res.body.content) {
                        _this.listActDiscountItemVo = res.body.content.list;
                        _this.itemCount = res.body.content.count;
                        _this.listActDiscountItemVo.forEach(item => {
                            if (_this.conditionSelect && _this.conditionSelect.discount_condition_id == item.discount_condition_id) {
                                item.color_sign = 'A'
                            } else {
                                item.color_sign = '';
                            }
                        })
                        _this.listActDiscountItemVo = JSON.parse(JSON.stringify(this.listActDiscountItemVo));

                    }
                    resolve && resolve();
                }, err => {
                    resolve && resolve();
                    this.$message.error(err);
                })
        },

        handleSubtractMoneyChange(e){
            let self = this;
            let reg = /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/
            if(e === '0' && self.form.package_type == 'FIXED_DISCOUNT_PACKAGE') {
                self.$message.error('减元不能为0，已重置为空')
                return null
            }else if ( self.form.package_type == 'FIXED_PRICE_PACKAGE') {
                if (e != '0') {
                    self.$message.error('【套餐类型】为“固定价格套餐”，减元必须为0')
                    return 0
                } else if (e == '0'){
                    return 0
                }
            }else if((!reg.test(e) && !!e )){
                self.$message.error('当前输入不符合规范')
                return null
            }else{
                let nums = e.split('.')
                if(nums.length === 1 || nums[1].length <= 2) return e
                let num = nums[1].slice(0,2)
                self.$message.warning('金额不能大于两位小数，已自动截取')
                return `${nums[0]}.${num}`
            }
        }
    }
}
