/*
优惠活动--实施店铺
*/
import Fn from '@common/Fn';
export default {
    data () {
        let self = this
        this._getAllShopsSearchData.data = []
        this._getAllShopsSearchData.del = []

        return {
            shopBtns: [
                {
                    type: 'primary',
                    txt: '添加店铺',
                    // disabled:()=>{return self.form.store_area!==0} ,
                    disabled:()=> !(self.ifCreateAndRejectAndCheckedOfStatus && !self.ifGlobalStore), 
                    click: self.shopAdd
                },
                {
					type: 'danger',
					txt: '删除',
					disabled:()=> !(self.ifCreateOfStatus && !self.ifGlobalStore),
					click: self.shopDel
				}, {
					type: 'info',
					txt: '失效',
					disabled:()=> !(self.ifApprovedOfStatus && !self.ifGlobalStore),
					click: self.shopInvalid
				}
            ],
            shopCols: [
                {
                    label: '店铺编码',
                    prop: 'shop_number'
                }, {
                    label: '店铺名称',
                    prop: 'shop_name',
                    width: 300
                }, {
                    label: '创建时间',
                    prop: 'create_time',
                    format: 'dataFormat1',
                    width: 150,
                }, {
                    label: '创建人',
                    prop: 'creator_name'
                },
            ],
            // 多选数据
            shopSelects: '',
            // 总记录数
            shopCount: 0,
            markListPmsShopVOData: [],
            shopSearch: {
                page_name: "act_discount_shop",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
                discount_id: ''
            },
        }
    },
    methods: {
        // 添加店铺
        shopAdd () {
            let params = {};
            let self = this;
            let p = Math.ceil(self.count / self.shopSearch.page_size)
            // 选择店铺,可以多选
            params.callback = d => {
                let dataSet = new Set(),
                    hasDuplicate = [];
                console.log(self.listPmsActShopVO)
                self.listPmsActShopVO.find(list => {
                    dataSet.add(list.shop_id)
                })
                console.log(dataSet);
                d.find(l => {
                    if (!dataSet.has(l.shop_id) && l.shop_status != 'CLOSED') {
                        // 给该条店铺数据设置是生效参数，0为生效，1为失效 
                        l.status = 0
                        l.shop_status = 'N'//新增默认店铺状态为未审核
                        if (/^(已审核|重新审核)$/.test(self.form.status_desc)) l.isNew = 'Y'
                        console.log(l)
                        // self.listPmsActShopVO.push(JSON.parse(JSON.stringify(l)))
                        self.listPmsActShopVO.push({
                            shop_id: l.shop_id,
                            shop_number: l.shop_code,
                            shop_name: l.shop_name,
                            row_status: 0
                            // shop_group:l.shop_group,
                            // shop_status:l.shop_status,
                            // create_time:l.create_time
                        })
                    } else {
                        hasDuplicate.push(l.shop_name)
                    }
                })
                if (hasDuplicate.length) {
                    let msg = hasDuplicate.join('、') + '店铺已添加或者已关闭';
                    if (hasDuplicate.length > 3) {
                        let len = hasDuplicate.length;
                        hasDuplicate.length = 3;
                        msg = hasDuplicate.join('、') + '等' + len + '个店铺已添加或者已关闭';
                    }
                    self.$message.info(msg);
                }
            };
            this.$root.eventHandle.$emit('alert', {
                params: params,
                component: () => import('@components/shop/list'),
                style: 'width:800px;height:500px',
                title: '店铺列表'
            });
        },
        // 店铺失效
        shopInvalid () {
            let self = this,
                bool = false;

            // if(this.form.status !='APPROVED'){
            // 	self.$message.error('非已审核实施店铺')
            // 	return
            // }
            if (self.shopSelects.length == 0) {
                self.$message.error('请选择要失效的店铺')
                return
            }
            // 判断是否有新增没有保存的数据
            self.shopSelects.map((v) => {
                if (typeof v.discount_shop_id === 'undefined' || v.discount_shop_id === null || v.discount_shop_id === '') {
                    bool = true;
                    self.$message.error('请保存后再进行失效操作');
                    return;
                }
            });
            if (bool) {
                return;
            }
            // 变更所选数据的状态
            let isAllInvalid = true;
            self.shopSelects.map(v => {
                if (v.row_status === 0) {
                    v.row_status = 1
                    isAllInvalid = false;
                }
            })
            if (isAllInvalid) {
                this.$message.info('已经失效的店铺无需再次失效');
            } else {
                this.$message.success('失效成功')
            }
            // 清除选中状态
            this.$refs.ShopList.clearSelection()
        },
        // 删除店铺
        shopDel () {
            var self = this;
            if (this.form.status === 'APPROVED') {
                self.$message.error('已审核不能删除实施店铺')
                return
            }
            if (self.shopSelects.length == 0) {
                self.$message.error('请选择要删除的活动店铺')
                return
            } else if (/^(已审核|重新审核)$/.test(this.form.status_desc) && this.shopSelects.some(obj => obj.shop_status === 'Y')) {
                this.$message.error('不能删除已审核的实施店铺')
                return
            }
            // 获取要删除的shop_id set集合
            let delIdSet = new Set()
            self.shopSelects.find(d => {
                delIdSet.add(d.shop_id)
            })
            // 删除数据
            let i = self.listPmsActShopVO.length
            while (i--) {
                if (delIdSet.has(self.listPmsActShopVO[i].shop_id)) {
                    self.listPmsActShopVO[i].row_delete_flag = 'Y';
                    self.listPmsActShopVO[i].discount_shop_id && self._getAllShopsSearchData.del.push(self.listPmsActShopVO[i]);
                    self.listPmsActShopVO.splice(i, 1);

                }
            }
        },
        shopAllDel () {
            let self = this;
            // 获取要删除的shop_id set集合
            let delIdSet = new Set()
            self.listPmsActShopVO.find(d => {
                delIdSet.add(d.shop_id)
            })
            // 删除数据
            let i = self.listPmsActShopVO.length
            while (i--) {
                if (delIdSet.has(self.listPmsActShopVO[i].shop_id)) {
                    self.listPmsActShopVO[i].row_delete_flag = 'Y';
                    self.listPmsActShopVO[i].discount_shop_id && self._getAllShopsSearchData.del.push(self.listPmsActShopVO[i]);
                    self.listPmsActShopVO.splice(i, 1);

                }
            }
        },
        shopPageSizeChange (size) {
            this.isListPmsShopChange()
            this.shopSearch.page_size = size;
            this.shopSearching()
        },
        shopPageChange (page_no) {
            this.isListPmsShopChange()
            this.shopSearch.page_no = page_no
            this.shopSearching()
        },
        shopSelectionChange (selectArr) {
            this.shopSelects = selectArr
        },
        shopStatusFormat (val) {
            switch (val) {
                case 0: return '生效';
                case 1: return '失效';
            }
            // if(val == 0) {
            // 	return "生效";
            // }else{
            // 	return "失效";
            // }
        },
        shopPresearch (list, resolve) {
            // this.isListPmsShopChange(resolve)
            this.shopSearch.where = list;
            this.shopSearching(resolve);
        },
        // 判断当前页的数据是否有修改
        isListPmsShopChange (resolve) {
            if (this.compareData(this.listPmsActShopVO, this.markListPmsShopVOData)) {
                resolve && resolve()
                this.$message.error('当前页有修改，请先保存！')
                throw ('当前页有修改，请先保存！')
            }
        },
        // 保存时转换成所有数据
        _saveWithAllShops (data) {
            var act_shop_id_list = {}

            console.log(data)
            data.forEach(obj => {
                if (obj.act_shop_id) {
                    act_shop_id_list[obj.act_shop_id] = obj
                } else {
                    this._getAllShopsSearchData.data.push(obj)
                }
            })

            this._getAllShopsSearchData.data.forEach((obj, index) => {
                if (obj.act_shop_id) {
                    if (this._getAllShopsSearchData.del.indexOf(obj.act_shop_id) !== -1) {
                        this._getAllShopsSearchData.data[index] = null
                    } else if (act_shop_id_list[obj.act_shop_id]) {
                        this._getAllShopsSearchData.data[index] = act_shop_id_list[obj.act_shop_id]
                    }
                }
            })
            console.log(this._getAllShopsSearchData.data);
            return this._getAllShopsSearchData.data.filter(Boolean).map(list => ({
                act_id: list.act_id,
                act_shop_id: list.act_shop_id,
                disable_person_id: list.disable_person_id,
                disable_person_name: list.disable_person_name,
                status: list.status,
                disable_time: +new Date(list.disable_time),
                marker: list.marker,
                shop_id: list.shop_id,
                isNew: list.isNew,
                shop_status: list.shop_status,
            }))
        },
        // 请求失败时，将goodsData里新增的对象对应的_getAllShopsSearchData.data也删除
        _saveWithAllShopsFailCallback (data) {
            data.forEach(obj => {
                if (!obj.act_shop_id) {
                    this._getAllShopsSearchData.data.splice(this._getAllShopsSearchData.data.indexOf(obj), 1)
                }
            })
        },
        // 每一次刷新优惠商品时都要那一次全部数据，用于保存时(XPT-8565对接口bug的补丁)
        // 每次新增、删除、失效(修改行状态)都要修改this._getAllShopsSearchData.data
        _getAllShopsSearchData () {
            this.ajax.postStream('/price-web/api/actDiscount/getDiscountShop', this.shopSearch, res => {
                this._getAllShopsSearchData.del = []
                if (res.body.result) {
                    this.shopCount = res.body.content.count || 0;
                    this.listPmsActShopVO = res.body.content.list;
                    this.markListPmsShopVOData = res.body.content.list
                } else {
                    this.$message.error(res.body.msg);
                }
            }, () => {
                this._getAllShopsSearchData.del = []
            })
        },
        shopSearching (resolve) {
            let _this = this
            if (_this.ifChange) {
                this.$message.error('未保存前不可查询');
                resolve && resolve();
                return
            }
            resolve && resolve()
            this._getAllShopsSearchData()
        },

        /*
        用于控制实施店铺按钮可操作状态
    	
        创建、已撤回、驳回、重新审核：商品、店铺加、删、失效都可以
        提交审核：商品、店铺不允许任何编辑操作
        已审核：商品、店铺只允许失效操作
    	
        店铺范围为店铺时可以操作

        */
        shopsBtnControl () {
            // console.log(this.form)
            let self = this,
                status = this.form.status,			// 优惠状态
                if_global = this.form.store_area == 0 ? true : false,		// 店铺范围,true为全局，false为店铺
                if_approved = status === 'APPROVED' ? true : false
        },
    }
}