<!-- 优惠活动详情、新增 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type="primary" size="mini" @click="presave()" :disabled='saveStatus' :loading='saveStatus'>保存</el-button>
				<el-button size='mini' type='success' @click='submit' :disabled='onAudit||isAdd' :loading='onAudit'>提交</el-button>
				<el-button size='mini' type='warning' @click='withdrawAudit' :disabled='onWithdraw||isAdd' :loading='onWithdraw'>撤回</el-button>
				<el-button size='mini' type='success' @click='auditSuccess' :disabled='onSuccess||isAdd' :loading='onSuccess'>审核</el-button>
				<el-button size='mini' type='warning' @click='auditFaile' :disabled='onFaile||isAdd' :loading='onFaile'>驳回</el-button>
        <el-button size='mini' type='warning' @click='addCopeFun' :disabled='onFaile||isAdd' :loading='onFaile'>复制新增</el-button>
			</el-col>
		</el-row>
		<el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="110px">
			<el-row class='xpt-flex__bottom'>
				<el-tabs v-model="firstTab">
					<el-tab-pane label='优惠详情' name='discountDetail' class='xpt-flex' style="overflow:hidden;">
						<el-row>
              <el-col :span="6">
                <el-form-item label="优惠活动ID" >
                    <el-input v-model="form.act_id" size='mini' disabled></el-input>
                </el-form-item>
              </el-col>
			<el-col :span="6">
				<el-form-item label="优惠活动名称" prop='act_name'>
					<el-input v-model="form.act_name" size='mini' :maxlength="50" :disabled="!isAdd"></el-input>
					 <el-tooltip v-if='rules.act_name[0].isShow' class="item" effect="dark" :content="rules.act_name[0].message" placement="right-start" popper-class='xpt-form__error'>
					 	<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="状态" >
					<el-input v-model="form.status_desc" size='mini' disabled></el-input>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="外部活动来源" >
					<el-select
						size='mini'
						v-model="form.actiivity_source"
						:clearable="true"
						@change="rules.actiivity_source_id[0].required = !!form.actiivity_source"
						@clear="form.actiivity_source_id = ''"
						:disabled="!isAdd"
					>					
						<el-option label="天猫" value="TMALL"></el-option>
					</el-select>
				</el-form-item>
			</el-col>

			</el-row>
			<el-row>
              <el-col :span="6">
                <el-form-item label="是否自动添加" >
                  <el-switch v-model="form.if_automatic_add" on-text="是" off-text="否" :disabled="isAutomaticAddDisabled || form.discount_subclass == 'OFF_RETURN_DISCOUNT'|| form.discount_subclass == 'OFF_SINGLE_PRODUCT'"></el-switch>
                </el-form-item>
              </el-col>
							<el-col :span="6">
								<el-form-item label="优惠类型" prop="discount_category">
									<xpt-select-aux v-model='form.discount_category' :disabled='!isAdd' @change='selectParent' aux_name='discountCategory'></xpt-select-aux>
									 <el-tooltip v-if='rules.discount_category[0].isShow' class="item" effect="dark" :content="rules.discount_category[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
								<el-form-item label="子分类" prop="discount_subclass">
									<el-select v-model="form.discount_subclass" placeholder="请选择" size="mini"  @change="selectChild" :disabled='!isAdd || !isChildClass'>
									    <el-option
									      v-for="item in childClassData"
									      :key="item.code"
									      :label="item.name"
									      :value="item.code">
									    </el-option>
									</el-select>
									 <el-tooltip v-if='rules.discount_subclass[0].isShow' class="item" effect="dark" :content="rules.discount_subclass[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="优惠金额" prop='price'>
									<el-input v-model="form.price" size='mini' :disabled='!isAdd || form.if_change_price'></el-input>
									<el-tooltip v-if='rules.price[0].isShow' class="item" effect="dark" :content="rules.price[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
								<el-form-item label="生效金额门槛" prop='enable_price'>
									<el-input v-model="form.enable_price" size='mini' :disabled='!isAdd'></el-input>
									<el-tooltip v-if='rules.enable_price[0].isShow' class="item" effect="dark" :content="rules.enable_price[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="外部活动ID" prop='actiivity_source_id'>
									<el-input v-model="form.actiivity_source_id" size='mini' :disabled='!isAdd || !form.actiivity_source'></el-input>
									<el-tooltip v-if='rules.actiivity_source_id[0].isShow' class="item" effect="dark" :content="rules.actiivity_source_id[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
								</el-form-item>
								<el-form-item label="是否可选">
									<el-switch :value="{'Y':true,'N':false,'':false,'null': false,}[form.optional_flag]" on-text="是" off-text="否" @change="val => form.optional_flag = val ? 'Y' : 'N'" :disabled="!isAdd"></el-switch>
								</el-form-item>
							</el-col>

						</el-row>
						<el-row>
              <el-col :span="6">
                <el-form-item label="生效时间" prop="enable_date">
                  <el-date-picker v-model="form.enable_date" type="datetime" placeholder="选择日期" size="mini" style="width:200px;" :disabled='!isAdd' :picker-options='dateFormatFun'></el-date-picker>
                  <el-tooltip v-if='rules.enable_date[0].isShow' class="item" effect="dark" :content="rules.enable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="失效时间" prop="disable_date">
                  <el-date-picker v-model="form.disable_date" type="datetime" placeholder="选择日期" size="mini" style="width:200px;" :disabled='form.status_desc==="提交审核"?true:false' :picker-options='iDateFormatFun'></el-date-picker>
                  <el-tooltip v-if='rules.disable_date[0].isShow' class="item" effect="dark" :content="rules.disable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
							<el-col :span="6">
								<el-form-item label="店铺范围" >
									<el-switch v-model="form.if_global" on-text="全局" off-text="店铺" :disabled="!isAdd || form.discount_subclass == 'OFF_RETURN_DISCOUNT'|| form.discount_subclass == 'OFF_SINGLE_PRODUCT'" @change='shopsBtnControl'></el-switch>
								</el-form-item>
								<el-form-item label="需审核" >
									<el-switch v-model="form.if_verify" on-text="是" off-text="否" disabled></el-switch>
								</el-form-item>
							</el-col>
              <el-col :span="6" style="height:60">
                <el-form-item label="说明" >
                  <el-input type="textarea" v-model="form.marker" :maxlength="240" size='mini' style="width:100%;" :disabled='isAdd?false:(form.status_desc==="提交审核"||form.status_desc==="已审核"?true:false)'></el-input>
                </el-form-item>
              </el-col>
						</el-row>
					</el-tab-pane>
					<el-tab-pane label='更多优惠设置' name='moreSet' class='xpt-flex'>
						<el-row>
							<el-col :span="8">
								<el-form-item label="优惠项目" >
									<xpt-select-aux v-model='form.discount_item' aux_name='discountItem' disabled></xpt-select-aux>
								</el-form-item>

								<el-form-item label="影响售价" >
									<el-switch v-model="form.if_effect_price" on-text="是" off-text="否" disabled ></el-switch>
								</el-form-item>
								<el-form-item label="金额可变" >
									<!-- <el-switch v-model="form.if_change_price" on-text="是" off-text="否" :disabled="!if_change_price"></el-switch> -->
									<el-switch v-model="form.if_change_price" on-text="是" off-text="否" :disabled="!(form.discount_subclass == 'OFF_RETURN_DISCOUNT'|| form.discount_subclass == 'OFF_SINGLE_PRODUCT') || !isAdd" @change="priceChange"></el-switch>
								</el-form-item>
								<el-form-item label="时间维度" prop="discount_affection_date">
									<xpt-select-aux v-model='form.discount_affection_date' aux_name='discountAffection' :disabled='!this.isAdd'></xpt-select-aux>
									 <el-tooltip v-if='rules.discount_affection_date[0].isShow' class="item" effect="dark" :content="rules.discount_affection_date[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									 </el-tooltip>
								</el-form-item>
							</el-col>
						</el-row>
					</el-tab-pane>
					<el-tab-pane label='编制信息' name='organizaMsg' class='xpt-flex'>
						<el-row>
							<el-col :span="8">
								<el-form-item label="编制人" >
									<el-input v-model="form.creator_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="编制日期" >
									<el-input v-model="form.create_time" size='mini' disabled></el-input>
								</el-form-item>
								<el-form-item label="审核人" >
									<el-input v-model="form.audit_name" size='mini' disabled></el-input>
								</el-form-item>
								<el-form-item label="审核日期" >
									<el-input v-model="form.audit_time" size='mini' disabled></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="8">
								<el-form-item label="创建人" >
									<el-input v-model="form.creator_name" size='mini' disabled></el-input>
								</el-form-item>

								<el-form-item label="创建时间" >
									<el-input v-model="form.create_time" size='mini' disabled></el-input>
								</el-form-item>
								<el-form-item label="最后更新人" >
									<el-input v-model="form.modifier_name" size='mini' disabled></el-input>
								</el-form-item>
								<el-form-item label="最后更新时间" >
									<el-input v-model="form.modify_time" size='mini' disabled></el-input>
								</el-form-item>
							</el-col>
						</el-row>
					</el-tab-pane>
				</el-tabs>
			</el-row>
		</el-form>
		<el-row class='xpt-flex__bottom'>
			<el-tabs v-model="secondTab">
				<el-tab-pane label='优惠商品' name="discount_GoodsList" class='xpt-flex' >
					<xpt-list
						ref='GoodsList'
						:data='form.listPmsActMaterialVO'
						:btns='goodsBtns'
						:colData='goodsCols'
						searchPage='pms_act_material'
						:pageTotal='goodsCount'
						@search-click='goodsPresearch'
						@selection-change='goodsSelectionChange'
						@page-size-change='goodsPageSizeChange'
						@current-page-change='goodsPageChange'
					></xpt-list>
				</el-tab-pane>
				<el-tab-pane label='实施店铺' name="discount_shopList" class='xpt-flex'>
					<xpt-list
						ref='ShopList'
						:data='form.listPmsActShopVO'
						:btns='shopBtns'
						:colData='shopCols'
						searchPage='pms_act_shop'
						:pageTotal='shopCount'
						@search-click='shopPresearch'
						@selection-change='shopSelectionChange'
						@page-size-change='shopPageSizeChange'
						@current-page-change='shopPageChange'
					></xpt-list>
				</el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
// import staticData from '@common/staticData.js'
import VL from '@common/validate.js'
import Fn from '@common/Fn.js'
import goods from './model/goods.js'
import shop from './model/shop.js'
import setting from './model/setting.js'
export default {
props:['params'],
	mixins: [goods, shop, setting],
	data(){
		var self = this;
		return{
			firstTab:'discountDetail',
			secondTab:'discount_GoodsList',
			// 当前操作是新增还是编辑，true为新增，false为编辑
			isAdd: true,
      //复制新增
      isCopyAdd: false,
			// 是否还有子分类
			isChildClass: false,
			//优惠类型,当为编辑状态时获取所有列表，当为新增状态时获取生效列表
			// disClassData: this.isAdd ? __AUX.getValidData('discountCategory') : __AUX.get('discountCategory'),
			// 所有子分类
			childClassDataAll: this.isAdd && !this.isCopyAdd  ? __AUX.getValidData('discountSubclass') : __AUX.get('discountSubclass'),
			// 当前可以选子分类
			childClassData: this.isAdd && !this.isCopyAdd ? [] : __AUX.get('discountSubclass'),
			form: {
				// 活动ID
				act_id: '',
				// 活动名称
				act_name: '',
				audit: '',
				// 审核时间
				audit_time: '',
				// 审核人
				audit_name: '',
				// 编制日期
				create_time: '',
				creator: '',
				// 创建人
				creator_name: '',
				// 失效时间
				disable_date: '',
				// 时间维度
				discount_affection_date: '',
				// 优惠类型
				discount_category: '',
				// 优惠项目
				discount_item: '',
				// 子分类
				discount_subclass: '',
				// 生效时间
				enable_date: '',
				// 使用门槛
				enable_price: 0,
				// 金额变更
				if_change_price: '',
				// 是否影响售价
				if_effect_price: '',
				// 店铺范围
				if_global: true,
				// 是否可选
				optional_flag: 'Y',
				// 外部活动来源
				actiivity_source: '',
				// 外部活动ID
				actiivity_source_id: '',
				// 是否审核
				if_verify: '',
				// 优惠商品
				listPmsActMaterialVO: [],
				// 实施店铺
				listPmsActShopVO: [],
				// 说明
				marker: '',
				modifier: '',
				// 最后更新人
				modifier_name: '',
				// 最后更新时间
				modify_time: '',
				// 优惠金额
				price: 0,
				// 状态编码
				status: 'CREATE',
				// 状态中文名
				status_desc: '创建',
				table_create_time: '',
				table_creator: '',
				table_creator_name: '',
				// 是否自动添加，默认为false，且不可以编辑；当优惠类型为商品活动，子类型为赠品时，可以编辑
				if_automatic_add: false,

			},
			//金额是否可变，当子分类为多拍，单据状态可编辑时，金额可变可编辑
			//if_change_price:true,
			// 是否自动添加编辑状态，默认为不可编辑
			isAutomaticAddDisabled: true,
			// 用于添加商品时的设置，传递至goodsAdd.vue
			isEditMateriel:false,
			rules:{
				discount_affection_date:self.VLFun('moreSet', '请选择时间维度'),
				act_name:self.VLFun('discountDetail','请输入活动名称'),
				discount_category:self.VLFun('discountDetail','请选择优惠类型'),
				enable_date:self.VLFun('discountDetail','请选择生效日期'),
				disable_date:self.VLFun('discountDetail','请选择失效日期'),
				discount_subclass: [{
					required: true,
					isShow: false,
					message: '请选择子类型',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(self.isChildClass) {
							if(value) {
								self.rules[rule.field][0].isShow = false
								callback();
							} else {
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						} else {
							self.rules[rule.field][0].isShow = false
							callback();
						}
					}
				}],
				actiivity_source_id: [{
					required: false,
					isShow: false,
					message: '请填写外部活动ID',
					trigger: 'blur',
					validator: function(rule, value, callback) {
						if(self.form.actiivity_source && !value) {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						} else {
							self.rules[rule.field][0].isShow = false
							callback();
						}
					}
				}],
				price: [{
					required: true,
					isShow: false,
					message: '请输入大于等于0的数字，保留两位小数',
					trigger: 'change',
					validator: function(rule, value, callback) {
						let reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
							values = value += ''
						if(reg.test(values)) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}
					}
				}],
				enable_price: [{
					required: true,
					isShow: false,
					message: '请输入大于等于0的数字，保留两位小数',
					trigger: 'change',
					validator: function(rule, value, callback) {
						let reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
							values = value + ''
						if(reg.test(values)) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}
					}
				}],

			},
			// 设置生效时间从00:00:00开始
			dateFormatFun:{
				/*生效时间允许选所有值*/
				// disabledDate(time){
				// 	return time.getTime() < Date.now() - 8.64e7;
				// },
				date:(function(){
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth()+1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
					return new Date(time);
      			})()
			},
			// 设置结束时间为23:59:59
			iDateFormatFun:{
				disabledDate: time => {
					/*新增时能选所有时间，编辑时选当天之后*/
					return this.isAdd ? false : time.getTime() < Date.now() - 8.64e7
				},
				date:(function(){
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth()+1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
					return new Date(time);
				})()
			},
			// 保存按钮状态
			saveStatus: false,
			onAudit: false,
			onWithdraw: false,
			onSuccess: false,
			onFaile: false
		}
	},
	methods:{
		// 校验 9.16
		VLFun(tabs, msg){
			var self =this;
			return [{
					required: true,
					isShow: false,
					message: msg,
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback()
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}
					}
				}]
		},
		/*
		优惠类型选择
		根据所先优惠类型，过滤出子分类，并判断是否有子分类
		如果有子分类，设置isChildClass值为true，否则为false
		*/
		selectParent(value) {
			console.log(value)
			let childClassData = []
			this.childClassDataAll.find(row => {
				if(row.parentCode === value && row.status) {
					childClassData.push(row)
				}
			})
			if(childClassData.length) {
				this.isChildClass = true
				this.childClassData = childClassData
			} else {
				this.isChildClass = false
			}
			if(this.isAdd && !this.isCopyAdd) {
				this.form.discount_subclass = ''
				this.form.if_automatic_add = false
			}

			this.goodsBtnControl()
	      if(this.isAdd && !this.isCopyAdd) {
	        this.setDefaultValue()
	      }
			// 根据优惠商品列表个从承担比例设置需审核状态按钮
			this.goodsChangeStatus()
			this.isAutomaticAddDisabled = true
		},
		//子分类为多拍时，则金额可变设置为是，其余的设置为否
		setIfChangePrice(value){
			/*let status = this.form.status;
			let discount_subclass = this.form.discount_subclass;
			this.if_change_price = (!status || status == 'CREATE' || status == 'WITHDRAWED' || status == 'RETRIAL') &&　discount_subclass=='EXCHANGE_REWARD'?true:false;
			if(discount_subclass != 'EXCHANGE_REWARD'){
				this.form.if_change_price = false;
			}*/
			//let discount_subclass = value || this.form.discount_subclass;

			//this.form.if_change_price = discount_subclass=='EXCHANGE_REWARD'?true:false;
		},
		priceChange(val){
			console.log(val)
			if(!val){
			}else{
				this.form.price = 0;
			}
		},
		// 子分类选择 9.17
		selectChild(value){
			
			//console.log('this.form1',this.form);
			this.goodsBtnControl()
			console.log('this.form2',this.form,this.isAdd);
		      if(this.isAdd && !this.isCopyAdd) {
		        this.setDefaultValue()
					}
					
			if(this.form.discount_subclass == 'OFF_RETURN_DISCOUNT' && this.isAdd){
				// console.log('111111111',this.isAdd)
				// this.form.if_global = false;
				// this.form.if_automatic_add = false;
				// this.form.if_effect_price = true;
				// // if_change_price = true;
				// this.form.if_verify = true;
				// this.form.discount_item = 'ORDER';
				
				
			}
			// if((this.form.discount_subclass == 'OFF_RETURN_DISCOUNT'|| this.form.discount_subclass == 'OFF_SINGLE_PRODUCT')&&this.isAdd){
			// 	this.form.if_verify = true;					
			// }else{
			// 	this.form.if_verify = false;
			// }
			// 根据优惠商品列表个从承担比例设置需审核状态按钮
			this.goodsChangeStatus()
			// 当优惠类型为商品活动，子类型为赠品时，可以编辑;isAutomaticAddDisabled值为false
			// 非新增状态下不可编辑
			if(this.isAdd) {

				if(this.form.discount_category === 'PRESENT_GOODS' && value === 'GIFTS') {
					this.isAutomaticAddDisabled = false
				} else {
					this.isAutomaticAddDisabled = true
					this.form.if_automatic_add = false
				}
			}
			console.log('this.form',this.form);
		},
		// 保存 9.17
		/*
		isClose：是否关闭当前标签
		*/
		
		save(callback, isClose) {
			
			var self = this;
			this.$refs.form.validate((valid) => {
				if (valid) {
					// 过滤优惠商品中新添加的商品
					let goodsData = JSON.parse(JSON.stringify(this.form.listPmsActMaterialVO))
					goodsData.find(list => {
						if(list.act_material_id.toString().indexOf('ID') > -1) {
							list.act_material_id = ''
						}
						list.disable_time = +new Date(list.disable_time);
					})
					// 店铺数据
					let shopDataList = JSON.parse(JSON.stringify(this.form.listPmsActShopVO))
					// this.form.listPmsActShopVO.find(list => {
					// 	shopDataList.push({
					// 		act_id: list.act_id,
					// 		act_shop_id: list.act_shop_id,
					// 		disable_person_id: list.disable_person_id,
					// 		disable_person_name: list.disable_person_name,
					// 		disable_status: list.disable_status,
					// 		disable_time: +new Date(list.disable_time),
					// 		marker: list.marker,
					// 		shop_id: list.shop_id
					// 	})
					// })
					let data = {
						act_id: this.form.act_id,
						act_name: this.form.act_name,
						disable_date: this.form.disable_date,
						discount_affection_date: this.form.discount_affection_date,
						discount_category: this.form.discount_category,
						discount_item: this.form.discount_item,
						discount_subclass: this.form.discount_subclass,
						enable_date: this.form.enable_date,
						enable_price: this.form.enable_price,
						optional_flag: this.form.optional_flag,
						actiivity_source: this.form.actiivity_source,
						actiivity_source_id: this.form.actiivity_source_id,
						if_change_price: this.form.if_change_price ? 1 : 0,
						if_effect_price: this.form.if_effect_price ? 1 : 0,
						if_global: this.form.if_global ? 1 : 0,
						if_verify: this.form.if_verify ? 1 : 0,
						listPmsActMaterialVO: this._saveWithAllActMaterial(goodsData),
						listPmsActShopVO: this._saveWithAllShops(shopDataList),
						marker: this.form.marker,
						price: this.form.price,
						if_automatic_add: this.form.if_automatic_add ? 1 : 0
					}
					,	isNewAndStatusAudited = this.form.status_desc === '重新审核' || (this.form.status_desc === '已审核' && (goodsData.some(obj => obj.isNew === 'Y') || shopDataList.some(obj => obj.isNew === 'Y')))
					// let ifcanSave = this.showDisableList(data);
					self.ajax.postStream('/price-web/api/price/pms/page/findMaterialList',data, (res) => {
						console.log(res)
						if(res.body.content.code.length != 0){
							let params = {
								callback(res){
									// console.log(res);
									if(res == true){
										// call&&call();
										// return true;
										this.saveStatus = true
										self.ajax.postStream('/price-web/api/price/pms/page/actSave?permissionCode=DISCOUNT_ACTIVITY_SAVE', data, res => {
											self.saveStatus = false
											if(res.body.result) {
												// 重新给表单赋值
												let newData = res.body.content
												self.form.act_id = newData.act_id
												self.isAdd = false
												if(callback) {
													if(!isClose){//提交的callback，如果不请求下面两个会导致出现相同商品的bug
														// 获取商品列表数据
														self.goodsSearching()
														// 获取店铺列表数据
														self.shopSearching()
													}
													callback()
											} else {
												self.$message({
													type: 'success',
													message: res.body.msg
												})

								if(isNewAndStatusAudited){
									setTimeout(() => {
										self.$message.error({
											message: '该已审核优惠活动存在新增优惠商品/实施店铺，请重新审核',
											duration: 7000,
										})
									}, 1200)
								}

								self.$root.eventHandle.$emit('discountAdd')
								self.goodsSearch.act_id = self.form.act_id
								self.shopSearch.act_id = self.form.act_id
								// 获取商品列表数据
								self.goodsSearching()
								// 获取店铺列表数据
								self.shopSearching()
								self.getData();
							}
							// 关闭当前标签，后续操作无需执行。
							if(isClose) return;
							self.$root.eventHandle.$emit('updateTab',{
								name:self.params.tabName,
								title:'编辑优惠活动'
							})
						} else {
							self._saveWithAllActMaterialFailCallback(goodsData)//请求失败时，将goodsData里新增的对象对应的_getAllGoodsSearchData.data也删除
							self._saveWithAllShopsFailCallback(shopDataList)//请求失败时，将goodsData里新增的对象对应的_getAllShopsSearchData.data也删除
							// this.goodsSearching()
							self.$message({
								type: 'error',
								message: res.body.msg
							})
						}
					}, err => {
						this._saveWithAllActMaterialFailCallback(goodsData)
						this._saveWithAllShopsFailCallback(shopDataList)
						this.saveStatus = false;
						this.$message.error(err);
					})
									}
								},
								data:res.body.content.code
							};
							this.$root.eventHandle.$emit('alert', {
								params: params,
								component: () => import("@components/discount/disableList.vue"),
								style: 'width:600px;height:400px',
								title: '当前操作异常存在订单不可选物料如下，继续操作会删除以下物料，是否继续'
							});
						}else{
							// call&&call();
							// return true;
							this.saveStatus = true
					this.ajax.postStream('/price-web/api/price/pms/page/actSave?permissionCode=DISCOUNT_ACTIVITY_SAVE', data, res => {
						this.saveStatus = false
						if(res.body.result) {
							// 重新给表单赋值
							let newData = res.body.content
							this.form.act_id = newData.act_id
							this.isAdd = false
							if(callback) {
								if(!isClose){//提交的callback，如果不请求下面两个会导致出现相同商品的bug
									// 获取商品列表数据
									this.goodsSearching()
									// 获取店铺列表数据
									this.shopSearching()
								}
								callback()
							} else {
								this.$message({
									type: 'success',
									message: res.body.msg
								})

								if(isNewAndStatusAudited){
									setTimeout(() => {
										this.$message.error({
											message: '该已审核优惠活动存在新增优惠商品/实施店铺，请重新审核',
											duration: 7000,
										})
									}, 1200)
								}

								this.$root.eventHandle.$emit('discountAdd')
								this.goodsSearch.act_id = this.form.act_id
								this.shopSearch.act_id = this.form.act_id
								// 获取商品列表数据
								this.goodsSearching()
								// 获取店铺列表数据
								this.shopSearching()
								this.getData();
							}
							// 关闭当前标签，后续操作无需执行。
							if(isClose) return;
							self.$root.eventHandle.$emit('updateTab',{
								name:self.params.tabName,
								title:'编辑优惠活动'
							})
						} else {
							this._saveWithAllActMaterialFailCallback(goodsData)//请求失败时，将goodsData里新增的对象对应的_getAllGoodsSearchData.data也删除
							this._saveWithAllShopsFailCallback(shopDataList)//请求失败时，将goodsData里新增的对象对应的_getAllShopsSearchData.data也删除
							// this.goodsSearching()
							this.$message({
								type: 'error',
								message: res.body.msg
							})
						}
					}, err => {
						this._saveWithAllActMaterialFailCallback(goodsData)
						this._saveWithAllShopsFailCallback(shopDataList)
						this.saveStatus = false;
						this.$message.error(err);
					})
						};
					});
					
					
				} else {
					// 判断当前校验不通过时，错误字段是在那个tab页签
					let moreSetError = false,
						discountDetailError = false
					for(let item in self.rules) {
						if(self.rules[item][0].isShow) {
							if(item === 'discount_affection_date') {
								moreSetError = true
							} else {
								discountDetailError = true
							}
						}
					}
					if(discountDetailError) {
						if(self.firstTab !== 'discountDetail') {
							self.firstTab = 'discountDetail'
						}
						return
					}
					if(moreSetError) {
						if(self.firstTab !== 'moreSet') {
							self.firstTab = 'moreSet'
						}
						return
					}
				}
	    	});
		},
		// 提交审核 9.17
		submit() {
			this.save(this.submitAudit);

			// this.showDisableList(d=>{
			// 	this.save(this.submitAudit);
			// });
			return;
			
		},
		presave(){
			this.save();
		},
		preauditSuccess(){
			this.showDisableList(d=>{
				// this.save(this.auditSuccess);
				this.auditSuccess();
			});
		},
		showDisableList(data){
			
				
					this.ajax.postStream('/price-web/api/price/pms/page/findMaterialList',data, (res) => {
						console.log(res)
						if(res.body.content.code.length != 0){
							let params = {
								callback(res){
									// console.log(res);
									if(res == true){
										// call&&call();
										return true;

									}
								},
								data:res.body.content.code
							};
							this.$root.eventHandle.$emit('alert', {
								params: params,
								component: () => import("@components/discount/disableList.vue"),
								style: 'width:600px;height:400px',
								title: '当前操作异常存在订单不可选物料如下，继续操作会删除以下物料，是否继续'
							});
						}else{
							// call&&call();
							return true;
						};
					});
					// return;
			
		},
		submitAudit() {
			this.onAudit = true;
			this.ajax.postStream('/price-web/api/price/pms/page/submit?permissionCode=DISCOUNT_ACTIVITY_SUBMIT', {
				ids: [this.form.act_id]
			}, res => {
				if(res.body.result) {
					this.form.status_desc = '提交审核'
					// 商品按钮状态控制
					this.goodsBtnControl()
					// 店铺按钮状态控制
					this.shopsBtnControl()
					// 获取商品列表数据
					this.goodsSearching()
					// 获取店铺列表数据
					this.shopSearching()
					// 根据个承担比例，控制审核状态
					this.goodsChangeStatus();
					// 重新获取数据
					this.getData();
					this.$root.eventHandle.$emit('discountAdd')
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				this.onAudit = false;
			}, err => {
				this.onAudit = false;
				this.$message.error(err);
			})
		},
		// 撤回审核 9.17
		withdrawAudit() {
			this.onWithdraw = true;
			this.ajax.postStream('/price-web/api/price/pms/page/withdrawAct?permissionCode=DISCOUNT_ACTIVITY_WITHDRAW', {
				ids: [this.form.act_id]
			}, res => {
				if(res.body.result) {
					this.form.status_desc = '已撤回'
					// 商品按钮状态控制
					this.goodsBtnControl()
					// 店铺按钮状态控制
					this.shopsBtnControl()
					// 获取商品数据
					this.goodsSearching()
					// 获取店铺数据
					this.shopSearching()
					// 重新获取数据
					this.getData();
					this.$root.eventHandle.$emit('discountAdd')
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				this.onWithdraw = false;
			}, err => {
				this.onWithdraw = false;
			})
		},
		// 审核通过 9.17
		auditSuccess() {
			this.onSuccess = true;
			this.ajax.postStream('/price-web/api/price/pms/page/approve?permissionCode=DISCOUNT_ACTIVITY_AUDIT', {
				ids: [this.form.act_id]
			}, res => {
				if(res.body.result) {
					this.form.status_desc = '已审核'
					// 商品按钮状态控制
					this.goodsBtnControl()
					// 店铺按钮状态控制
					this.shopsBtnControl()
					// 获取商品数据
					this.goodsSearching()
					// 获取店铺数据
					this.shopSearching()
					// 重新获取数据
					this.getData();
					this.$root.eventHandle.$emit('discountAdd')
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				this.onSuccess = false;
			}, err => {
				this.onSuccess = false;
				this.$message.error(err);
			})
		},
		// 驳回审核 9.17
		auditFaile() {
			this.onFaile = true;
			this.ajax.postStream('/price-web/api/price/pms/page/retrial?permissionCode=DISCOUNT_ACTIVITY_REJECT', {
				ids: [this.form.act_id]
			}, res => {
				if(res.body.result) {
					this.form.status_desc = '重新审核'
					// 商品按钮状态控制
					this.goodsBtnControl()
					// 店铺按钮状态控制
					this.shopsBtnControl()
					// 获取商品数据
					this.goodsSearching()
					// 获取店铺数据
					this.shopSearching()
					// 重新获取数据
					this.getData();
					this.$root.eventHandle.$emit('discountAdd')
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})
				this.onFaile = false;
			}, err => {
				this.onFaile = false;
				this.$message.error(err);
			})
		},
    //复制新增
    addCopeFun(){
      var self = this;
      self.$root.eventHandle.$emit('creatTab',{name:'编辑优惠活动',params:{actcopy_id:this.params.act_id ? this.params.act_id : this.form.act_id},component:()=>import('@components/discount/add.vue')});
    },

		// 获取活动信息,9.16
		getData(){
			var data ={
				act_id:this.params.act_id ? this.params.act_id : this.form.act_id
			};
			this.ajax.postStream('/price-web/api/price/pms/page/actGet', data, res => {
				if(res.body.result && res.body.content) {
					let newData = res.body.content
					// 类型转换
					newData.if_global = Boolean(newData.if_global)
					newData.if_verify = Boolean(newData.if_verify)
					newData.if_effect_price = Boolean(newData.if_effect_price)
					newData.if_change_price = Boolean(newData.if_change_price)
					newData.if_automatic_add = Boolean(newData.if_automatic_add)
					newData.create_time = Fn.dateFormat(newData.create_time, 'yyyy-MM-dd hh:mm:ss')
					newData.audit_time = Fn.dateFormat(newData.audit_time, 'yyyy-MM-dd hh:mm:ss')
					newData.modify_time = Fn.dateFormat(newData.modify_time, 'yyyy-MM-dd hh:mm:ss')

					delete newData.listPmsActMaterialVO
					delete newData.listPmsActShopVO
					Object.assign(this.form, newData)
					// this.form = newData
					// 设置优惠商品按钮状态
					// 商品按钮状态控制
					this.goodsBtnControl()
					// 设置实施店铺按钮状态
					this.shopsBtnControl()
					this.goodsSearch.act_id = this.form.act_id
					this.shopSearch.act_id = this.form.act_id
					this.isAdd = false
					this.params.__data = JSON.parse(JSON.stringify(newData));
					//this.setIfChangePrice();
				}
			})
		},
		/*
		数据对比，用于关闭时提示数据是否有变动
		*/
		close() {
			let hasUpdate = this.compareData(this.form, this.params.__data),
				self = this;
			if(hasUpdate) {
        		this.$root.eventHandle.$emit('openDialog', {
    				ok() {
    					self.save(() => {
            				self.$root.eventHandle.$emit('removeTab',self.params.tabName)
    					}, true)
    				},
    				no() {
            			self.$root.eventHandle.$emit('removeTab',self.params.tabName)
    				}
    			})
			} else {
				this.$root.eventHandle.$emit('removeTab',this.params.tabName)
			}
		}
	},
	mounted(){
		var self = this
		if(this.params.act_id){
			this.isAdd = false
			this.form.act_id = this.params.act_id
			this.goodsSearch.act_id = this.params.act_id
			this.shopSearch.act_id = this.params.act_id
			this.getData()
			// 获取商品数据
			this.goodsSearching()
			// 获取店铺数据
			this.shopSearching()
		}
    if(this.params.actcopy_id){
      this.isAdd = true
      this.isCopyAdd = true
      this.goodsSearch.act_id = this.params.actcopy_id
      this.shopSearch.act_id = this.params.actcopy_id
      // 获取商品数据
      this.goodsSearching()
      // 获取店铺数据
      this.shopSearching()
      // 获取复制新增活动信息
      var data ={
        act_id:this.params.actcopy_id ? this.params.actcopy_id : this.form.act_id
      };
      this.ajax.postStream('/price-web/api/price/pms/page/actGet', data, res => {
        if(res.body.result && res.body.content) {
          let newData = res.body.content

          // 类型转换
          newData.if_global = Boolean(newData.if_global)
          newData.if_verify = Boolean(newData.if_verify)
          newData.if_effect_price = Boolean(newData.if_effect_price)
          newData.if_change_price = Boolean(newData.if_change_price)
          newData.if_automatic_add = Boolean(newData.if_automatic_add)
//          newData.create_time = Fn.dateFormat(newData.create_time, 'yyyy-MM-dd hh:mm:ss')
//          newData.audit_time = Fn.dateFormat(newData.audit_time, 'yyyy-MM-dd hh:mm:ss')
//          newData.modify_time = Fn.dateFormat(newData.modify_time, 'yyyy-MM-dd hh:mm:ss')
          newData.status_desc =  '创建'
          newData.act_id = ''
          newData.creator_name = ''
          newData.create_time = ''
          newData.audit_name = ''
          newData.audit_time = ''
          newData.modifier_name = ''
          newData.modify_time = ''

          delete newData.listPmsActMaterialVO
		  delete newData.listPmsActShopVO
		  Object.assign(this.form, newData)
          // this.form = newData
          // 设置优惠商品按钮状态
          // 商品按钮状态控制
          this.goodsBtnControl()
          // 设置实施店铺按钮状态
          this.shopsBtnControl()
          this.params.__data = JSON.parse(JSON.stringify(newData));
        }

      })



    }


		this.params.__data = JSON.parse(JSON.stringify(this.form));
		this.params.__close = this.close
	}
}
</script>
