<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type="primary" size="mini" @click="save()" :disabled="saveStatus">保存</el-button>
            </el-col>
		</el-row>
		<el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="130px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="优惠类别" prop="discount_category">
                        <xpt-select-aux v-model='form.discount_category' aux_name='dicount_category_match' clearable ></xpt-select-aux>
                        <el-tooltip v-if='rules.discount_category[0].isShow' class="item" effect="dark" :content="rules.discount_category[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="优惠子类型"  prop='discount_subtype_name'>
                        <!-- <el-select v-model="form.discount_subtype" placeholder="请选择" size="mini"  :disabled='!isChildClass'>
                            <el-option
                            v-for="item in childClassData"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code">
                            </el-option>
                        </el-select> -->
                        <el-input size='mini' icon='search'
							v-model='form.discount_subtype_name'
							:on-icon-click="() => selectDiscountSubType('discount_subtype')"
							@change="discountSubTypeChange('discount_subtype')"
							readonly
                        ></el-input>
                        <el-tooltip v-if='rules.discount_subtype_name[0].isShow' class="item" effect="dark" :content="rules.discount_subtype_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="阶梯"  prop='level'>
                        <el-select v-model="form.level" placeholder="请选择" size="mini">
                            <el-option
                            v-for="item in levelList"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code">
                            </el-option>
                        </el-select>
                        <el-tooltip v-if='rules.level[0].isShow' class="item" effect="dark" :content="rules.level[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="活动形式"  prop='activity_type'>
                        <el-select v-model="form.activity_type" placeholder="请选择" size="mini">
                            <el-option label="优惠活动" value="DISCOUNT_ACTIVITY"></el-option>
                            <el-option label="价目活动" value="PRICE_ACTIVITY"></el-option>
                        </el-select>
                        <el-tooltip v-if='rules.activity_type[0].isShow' class="item" effect="dark" :content="rules.activity_type[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="创建人"  prop='creator_name'>
                        <el-input v-model="form.creator_name" size='mini' disabled></el-input>
                    </el-form-item>
                    <el-form-item label="创建时间"  prop='create_time'>
                        <el-date-picker v-model="form.create_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;"  :editable="false" disabled></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="是否前置生成商品" prop="if_before_create_goods">
                        <el-switch v-model="form.if_before_create_goods" on-text="是" off-text="否" on-value="Y" off-value="N" ></el-switch>
                        <el-tooltip v-if='rules.if_before_create_goods[0].isShow' class="item" effect="dark" :content="rules.if_before_create_goods[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="是否有效" prop="if_enable">
                        <el-switch v-model="form.if_enable" on-text="是" off-text="否" on-value="Y" off-value="N" ></el-switch>
                        <el-tooltip v-if='rules.if_enable[0].isShow' class="item" effect="dark" :content="rules.if_enable[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="是否前置POS" prop="if_before_pos">
                        <el-switch v-model="form.if_before_pos" on-text="是" off-text="否" on-value="Y" off-value="N" ></el-switch>
                        <el-tooltip v-if='rules.if_before_pos[0].isShow' class="item" effect="dark" :content="rules.if_before_pos[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="是否可选" prop="if_optional">
                        <el-switch v-model="form.if_optional" on-text="是" off-text="否" on-value="Y" off-value="N" ></el-switch>
                        <el-tooltip v-if='rules.if_optional[0].isShow' class="item" effect="dark" :content="rules.if_optional[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="线下门店专属" prop="if_offline_store_exclusive">
                        <el-switch v-model="form.if_offline_store_exclusive" on-text="是" off-text="否" on-value="Y" off-value="N" ></el-switch>
                        <el-tooltip v-if='rules.if_offline_store_exclusive[0].isShow' class="item" effect="dark" :content="rules.if_offline_store_exclusive[0].message" placement="right-start" popper-class='xpt-form__error'>
                            <i class='el-icon-warning'></i>
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item label="按行互斥活动" prop='row_reject_discount_category_name' >
                        <!-- <xpt-input v-model='form.row_reject_discount_category_name'  size='mini'  icon="search" :on-icon-click="() => selectGoodsDiscountCategory('row_reject_discount_category')" readonly placeholder="请选择按行互斥活动" @change='goodsDiscountCategoryChange("row_reject_discount_category")'></xpt-input> -->
                        <el-input size='mini' icon='search'
							v-model='form.row_reject_discount_category_name'
							:on-icon-click="() => selectGoodsDiscountCategory('row_reject_discount_category')"
							@change="goodsDiscountCategoryChange('row_reject_discount_category')"
							readonly
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="互斥活动" prop='reject_discount_category_name' >
                        <el-input size='mini' icon='search'
							v-model='form.reject_discount_category_name'
							:on-icon-click="() => selectGoodsDiscountCategory('reject_discount_category')"
							@change="goodsDiscountCategoryChange('reject_discount_category')"
							readonly
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="修改人"  prop='last_modifier_name'>
                        <el-input v-model="form.last_modifier_name" size='mini' disabled></el-input>
                    </el-form-item>
                    <el-form-item label="修改时间"  prop='last_modify_time'>
                        <el-date-picker v-model="form.last_modify_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;"  :editable="false" disabled></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
<script>
import VL from '@common/validate.js'
export default {
    props: ['params'],
    data () {
        return {
            form: {
                discount_category: '',
                if_before_create_goods: 'N',
                level: '',
                row_reject_discount_category: '',
                reject_discount_category: '',
                discount_subtype: '',
                discount_subtype_name: '',
                if_enable: 'Y',
                creator_name: '',
                create_time: '',
                last_modifier_name: '',
                last_modify_time: '',
                row_reject_discount_category_name: '',
                reject_discount_category_name: '',
                creator: '',
                last_modifier: '',
                activity_type: 'DISCOUNT_ACTIVITY',
                if_before_pos: 'N',
                if_optional: "Y",
                if_offline_store_exclusive: 'N'
            },
            rules: {
                ...[
                    {'discount_category': '优惠类别'},
                    {'if_before_create_goods': '是否前置生成商品'},
                    {'level': '阶梯'},
                    {'discount_subtype_name': '优惠子类型'},
                    {'if_enable': '是否生效'},
                    {'activity_type': '活动形式'},
                    {'if_before_pos': '是否前置POS'},
                    {'if_optional': '是否可选'},
                    {'if_offline_store_exclusive': '线下门店专属'}
                ].reduce((a, b) => {
					var key = Object.keys(b)[0]
					a[key] = VL.isNotBlank({
						self: this,
						msg: '请填写' + b[key],
					})
					return a
				}, {})
            },
            levelList: [
                {code: '1', name: '1'},
                {code: '2', name: '2'},
                {code: '3', name: '3'}
            ],
            isChildClass: false,
            saveStatus: false,
            // 所有子分类
			childClassDataAll: this.isAdd  ? __AUX.getValidData('discountSubclass') : __AUX.get('discountSubclass'),
			// 当前可以选子分类
			childClassData: this.isAdd ? [] : __AUX.get('discountSubclass'),
            isAdd: false,
            rejectDiscountCategoryCategoryObj: __AUX.get('dicount_category_match').reduce((pre, cur, idx, arr) => {
					pre[cur.code] = cur.name
					return pre
			},{}),
            discountSubClassObj: __AUX.get('discountSubclass').reduce((pre, cur, idx, arr) => {
					pre[cur.code] = cur.name
					return pre
			},{}),
        }
    },
    mounted(){
        if (this.params && !!this.params.auto_active_discount_setting_id) {
            this.getInfo()
            this.isAdd = false
        } else {
            this.isAdd = true
        }
    },
    methods: {
        selectDiscountSubType (code) {
            let self = this
            this.$root.eventHandle.$emit('alert',{
                component:()=>import('@components/discount/auxiliaryList'),
                style:'width:800px;height:500px',
                title:'辅助资料列表',
                params:{
                    isAlert: true,
                    selection: 'checkbox',
                    categoryCode: 'DISCOUNT_SUBCLASS',
                    currentData: this.form[code],
                    callback(d){
						let fieldCode = '', fieldCodeName = ''
                        d.forEach((item,idx) => {
                            fieldCode += item.code + ','
                            fieldCodeName += item.name + ','
                        })
                        if (fieldCode[fieldCode.length - 1] == ',') {
                            fieldCode = fieldCode.substring(0, fieldCode.length-1)
                            fieldCodeName = fieldCodeName.substring(0, fieldCodeName.length-1)
                        }
                        self.form[code] = fieldCode
                        self.form[`${code}_name`] = fieldCodeName
                    }
                }//传参过去
            });
        },
        discountSubTypeChange(code){
			this.form[code] = ''
            this.form[`${code}_name`] = ''
        },
        // 选择互斥编码
        selectGoodsDiscountCategory (code) {
            let self = this
            console.log('当前值', this.form[code])
            this.$root.eventHandle.$emit('alert',{
                component:()=>import('@components/discount/auxiliaryList'),
                style:'width:800px;height:500px',
                title:'辅助资料列表',
                params:{
                    isAlert: true,
                    selection: 'checkbox',
                    categoryCode: 'DISCOUNT_CATEGORY_MATCH',
                    currentData: this.form[code],
                    callback(d){
						let fieldCode = '', fieldCodeName = ''
                        d.forEach((item,idx) => {
                            fieldCode += item.code + ','
                            fieldCodeName += item.name + ','
                        })
                        if (fieldCode[fieldCode.length - 1] == ',') {
                            fieldCode = fieldCode.substring(0, fieldCode.length-1)
                            fieldCodeName = fieldCodeName.substring(0, fieldCodeName.length-1)
                        }
                        self.form[code] = fieldCode
                        self.form[`${code}_name`] = fieldCodeName
                    }
                }//传参过去
            });
        },
        goodsDiscountCategoryChange(code){
			this.form[code] = ''
            this.form[`${code}_name`] = ''
        },
        /*
		优惠类型选择
		根据所先优惠类型，过滤出子分类，并判断是否有子分类
		如果有子分类，设置isChildClass值为true，否则为false
		*/
		selectParent(value) {
			let childClassData = []
			this.childClassDataAll.find(row => {
				if(row.parentCode === value && row.status && row.ext_field2 === 'Y') {
					childClassData.push(row)
				}
			})
			if(childClassData.length) {
				this.isChildClass = true;
				this.childClassData = childClassData;
			} else {
				this.isChildClass = false
			}
		},
        save () {
            this.$refs.form.validate( (valid) => {
				if (!valid) return
                let postData = JSON.parse(JSON.stringify(this.form))
                if(postData.hasOwnProperty('row_reject_discount_category_name')){
                    delete postData.row_reject_discount_category_name;
                }
                if(postData.hasOwnProperty('reject_discount_category_name')){
                    delete postData.reject_discount_category_name;
                }
                if(postData.hasOwnProperty('discount_subtype_name')){
                    delete postData.discount_subtype_name;
                }
                if (this.isAdd) {
                    postData.creator = this.getEmployeeInfo('id')
                    postData.creator_name = this.getEmployeeInfo('fullName')
                    postData.create_time = new Date().getTime()
                } else {
                    postData.auto_active_discount_setting_id = this.params.auto_active_discount_setting_id
                }
                postData.last_modifier = this.getEmployeeInfo('id')
                postData.last_modifier_name = this.getEmployeeInfo('fullName')
                this.saveStatus = true
                this.ajax.postStream('/price-web/api/autoActiveDiscountSetting/save',postData,res => {
                    if(res.body.result) {
                        this.$message.success(res.body.msg)
                        this.params.callback(res)
                        this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
                    } else {
                        res.body.msg && this.$message.error(res.body.msg);
                    }
                    this.saveStatus = false
                }, err => {
                    this.saveStatus = false
                    this.$message.error(err);
                })
            })
        },
        getInfo () {
            let self = this
            this.ajax.postStream('/price-web/api/autoActiveDiscountSetting/get', {auto_active_discount_setting_id: this.params.auto_active_discount_setting_id},res => {
                if(res.body.result) {
                    this.form =  res.body.content
                    // 重置互斥活动的name
                    if (self.form.row_reject_discount_category) {
                        let rowRejectDiscountCategoryList = self.form.row_reject_discount_category.split(',')
                        let row_reject_discount_category_name = rowRejectDiscountCategoryList.reduce((name, cur,idx) => {
                            if (idx == (rowRejectDiscountCategoryList.length -1) ) {
                                name += self.rejectDiscountCategoryCategoryObj[cur]
                            } else {
                                name += self.rejectDiscountCategoryCategoryObj[cur] + ','
                            }
                            return name
                        }, '')
                        self.$set(self.form, 'row_reject_discount_category_name', row_reject_discount_category_name)
                    } else {
                        self.$set(self.form, 'row_reject_discount_category_name', '')
                    }
                    if (!!self.form.reject_discount_category) {
                        let rejectDiscountCategoryList = self.form.reject_discount_category.split(',')
                        let reject_discount_category_name = rejectDiscountCategoryList.reduce((name, cur,idx) => {
                            if (idx == (rejectDiscountCategoryList.length -1) ) {
                                name += self.rejectDiscountCategoryCategoryObj[cur]
                            } else {
                                name += self.rejectDiscountCategoryCategoryObj[cur] + ','
                            }
                            return name
                        }, '')
                        self.$set(self.form, 'reject_discount_category_name', reject_discount_category_name)
                    } else {
                        self.$set(self.form, 'reject_discount_category_name', '')
                    }

                    let discountSubTypeList = self.form.discount_subtype.split(',')
                    let discount_subtype_name = discountSubTypeList.reduce((name, cur,idx) => {
                        if (idx == (discountSubTypeList.length -1) ) {
                            name += self.discountSubClassObj[cur]
                        } else {
                            name += self.discountSubClassObj[cur] + ','
                        }
                        return name
                    }, '')
                    self.$set(self.form, 'discount_subtype_name', discount_subtype_name)
                } else {
                    res.body.msg && this.$message.error(res.body.msg);
                }
            }, err => {
                this.$message.error(err);
            })
        }
    }
}
</script>
