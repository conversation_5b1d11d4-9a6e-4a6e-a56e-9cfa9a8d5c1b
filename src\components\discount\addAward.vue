
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
		<el-col :span='24'>
        <el-button type='info' size='mini' @click="save" :disabled="baocun" :loading="ifLoading">保存</el-button>
        <el-button type='warning' size='mini' @click="check" :disabled="jiancha">检查名单</el-button>
        <el-button type='primary' size='mini' @click="submit" :disabled="tijiao">提交</el-button>
        <el-button type='danger' size='mini' @click="delAward" :disabled="shanchu">删除</el-button> 
		</el-col>
    </el-row>
    <el-row	:gutter='40' >
      <el-tabs v-model="firstTab" >
        <el-tab-pane label="中奖名单信息" name="awardList">
          <el-form label-position="right" label-width="140px">
            <el-col :span="6" style="width: 33%">
              <el-form-item label="单据编号：">
                <el-input v-model="actWinMainVersionsVO.win_no" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖名额：" v-if="ifSave">
                <el-input v-model="actWinMainVersionsVO.win_quota" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖名额：" v-else required>
                <el-input v-model="actWinMainVersionsVO.win_quota" size='mini' style="width: 200px;"></el-input>
              </el-form-item>
              <el-form-item label="中奖活动开始时间：">
                <el-input v-model="discount_start" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="优惠项目：" style="height: 32px;">
                <el-input v-model="discount_item_desc" size='mini' style="width: 261%;" icon="search" disabled></el-input>
              </el-form-item>
              <!-- <el-form-item label="优惠项目：" style="height: 32px;" v-else required> -->
                <!-- <el-input v-model="discount_item_desc" size='mini' style="width: 261%;" icon="search" :on-icon-click="selectDiscount" readonly></el-input> -->
                <!-- <xpt-input v-model='discount_item_desc' icon='search'  style="width: 261%;" :on-icon-click='selectDiscount' size='mini' @change='discountfChange' readonly></xpt-input>
              </el-form-item> -->
            </el-col>
            <el-col :span="6" style="width: 33%">
              <el-form-item label="中奖活动名称：" v-if="ifSave">
                <el-input v-model="actWinMainVersionsVO.win_name" size='mini' style="width: 200px;" :maxlength='200' disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖活动名称：" v-else required>
                <el-input v-model="actWinMainVersionsVO.win_name" size='mini' style="width: 200px;" :maxlength='200'></el-input>
              </el-form-item>
              <el-form-item label="中奖名单关联活动：" v-if="ifSave">
                <el-input v-model="actWinMainVersionsVO.discount_name" size='mini' style="width: 200px;" icon="search" :on-icon-click="searchActivity" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖名单关联活动：" v-else required>
                <el-input v-model="actWinMainVersionsVO.discount_name" size='mini' style="width: 200px;" icon="search" :on-icon-click="searchActivity" readonly></el-input>
              </el-form-item>
              <el-form-item label="中奖活动结束时间：">
                <el-input v-model="discount_end" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="width: 34%">
              <el-form-item label="营销活动区间：">
                <el-input v-model="discount_section" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="时间维度：">
                <el-input v-model="win_type" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="经销待返门槛：">
                <el-input v-model="actWinMainVersionsVO.dealer_refund_threshold" size='mini' type="number" :min="0" style="width: 200px;" ></el-input>
              </el-form-item>
            </el-col>

          </el-form>
          <el-form label-position="right" label-width="140px">
            <el-col :span="6" style="width: 100%">
              <el-form-item label="中奖活动规则说明：" style="height: 50px;" v-if="ifSave">
                <el-input v-model="actWinMainVersionsVO.win_explain" size='mini' type='textarea' :maxlength='1000' placeholder="1000字以内" style="width: 95.5%;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖活动规则说明：" style="height: 50px;" v-else required>
                <el-input v-model="actWinMainVersionsVO.win_explain" size='mini' type='textarea' :maxlength='1000' placeholder="1000字以内" style="width: 95.5%;"></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class='xpt-flex__bottom' id='bottom' v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane label="中奖人信息" name="awardPersonList" class='xpt-flex'>
          <xpt-list
            :data='actWinningDetailList'
            :colData='cols'
            :btns="btns"
            :selection="selection"
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
          >
            <xpt-import slot='btns' v-if="actWinMainVersionsVO.main_status === 'SUBMIT'" :taskUrl="uploadUrl" :callback="uploadCallback" class='mgl10' :isupload="true"></xpt-import>
            <xpt-import slot='btns' v-else :taskUrl="uploadUrl" :callback="uploadCallback" class='mgl10' :isupload="false"></xpt-import>
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
  import Fn from '@common/Fn.js'
    export default {
      name: "addAward",
      props:["params"],
      data() {
        let self = this;
        return {
          actWinningDetailList:[], //中奖人数据
          //中奖名单信息
          actWinMainVersionsVO:{
            win_no:'',//单据编号
            win_quota:'',//中奖名额
            discount_start:'',//中奖活动开始时间
            win_explain:'',//中奖活动规则说明
            win_name:'',//中奖活动名称
            discount_section:'',//营销活动区间
            discount_end:'',//中奖活动结束时间
            discount_name:'',//中奖名单关联活动
            win_type:'',//时间维度
            main_status:'',//审核状态
            dealer_refund_threshold:'',//经销待返门槛
          },
          actWinningItemVoList:[],  //优惠项目数据
          discountItem:{
            discount_item_desc:'',
            discount_item_id:'',
            discount_item_no:'',
            discount_id:'',
            lock_status:'',
          },
          discount_item_desc:'',//优惠项目
          discount_start:'',//中奖活动开始时间
          discount_end:'',//中奖活动结束时间
          discount_item_list:[],//从弹窗选择的优惠项目
          win_type:'',//时间维度
          discount_section:'',//营销活动区间
          ifSave:false,//是否点击保存
          ifCheck:false,//是否点击检查名单
		      ifLoading:false,
          baocun:false,
          jiancha:true,
          tijiao:true,
          shanchu:true,          
		      num:0,
          count:0,
          firstTab:"awardList",
          secondTab:"awardPersonList",
          selection:'none',
          pageSize:50,
          pageNO:1,
          uploadUrl:'/price-web/api/price/win/addImportDetail',
          btns:[
            // {
            //   type: "primary",
            //   txt: "新增",
            //   loading: false,
            //   click() {
            //   },
            // },
          ],
          cols:[
            {
              label: "序号",
              prop: "add_no",
              width: "60"
            },
            {
              label: "中奖人",
              prop: "winner",
              width: "80"
            },
            {
              label: "中奖排名",
              prop: "rank",
              width: "60"
            },
            {
              label: "天猫公示订单",
              prop: "tid",
              width: "160"
            },
            {
              label: "中奖关联订单",
              prop: "winning_relate_tids",
              width: "160"
            },
            {
              label: "实际发货关联订单",
              prop: "winning_delivery_relate_tids",
              width: "160"
            },
            {
              label: "中奖金额",
              prop: "winning_amount",
              width: "100"
            },
            {
              label: "手机号",
              prop: "phone",
              width: "100"
            },
            {
              label: "拍单时间",
              prop: "created",
              format: "dataFormat1",
              width: "140"
            },
            {
              label: "支付时间",
              prop: "pay_time",
              format: "dataFormat1",
              width: "140"
            },
            {
              label: "收货人",
              prop: "receiver_name",
              width: "80"
            },
            {
              label: "收货地址",
              prop: "receiver_address",
            },
            {
              label: "版本号",
              prop: "versions",
              width: "60"
            },
            {
              label: "状态",
              prop: "status",
              width: "60",
              formatter(val){
                switch (val) {
                  case "NORMAL" : return "正常";
                  case "CANCEL" : return "取消";
                }
              }
            },
            {
              label: "变更状态",
              prop: "change_status",
              width: "60",
              formatter(val){
                switch (val) {
                  case "WAIT" : return "等待";
                  case "PASS" : return "通过";
                  case "FAIL" : return "不通过";
                }
              }
            },
          ],
        }
      },
      methods: {
        getAwardList() {
          this.ajax.postStream('/price-web/api/price/win/getWinningData',{win_no:this.actWinMainVersionsVO.win_no},res => {
            if(res.body.result && res.body.content) {
              this.actWinMainVersionsVO = res.body.content.actWinMainVersionsVO;
              this.discount_start = Fn.dateFormat(res.body.content.actWinMainVersionsVO.discount_start,'yyyy-MM-dd hh:mm:ss');
              this.discount_end = Fn.dateFormat(res.body.content.actWinMainVersionsVO.discount_end,'yyyy-MM-dd hh:mm:ss');
              this.actWinningDetailList = res.body.content.actWinningDetailList;
              this.count = this.actWinningDetailList.length;
              this.addActWinningItemVoList(res.body.content.actWinningItemVoList);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        searchActivity() {
          this.$root.eventHandle.$emit('alert', {
            params: {
              callback: data => {
                this.discountId = data.discount_id
                this.actWinMainVersionsVO.win_type = data.discount_affection_date;
                this.actWinMainVersionsVO.discount_name = data.discount_name;
                this.actWinMainVersionsVO.discount_section = data.act_section;
                this.actWinMainVersionsVO.discount_start = data.enable_time;
                this.actWinMainVersionsVO.discount_end = data.disable_time;
                this.discount_start = Fn.dateFormat(data.enable_time,'yyyy-MM-dd hh:mm:ss');
                this.discount_end = Fn.dateFormat(data.disable_time,'yyyy-MM-dd hh:mm:ss');
                this.transTypeAndSection();
                this.discount_item_desc = '';
                // this.getSectionList(data);
              }
            },
            component: ()=>import('@components/discount/awardActivityList'),
            style: 'width:800px;height:500px',
            title: '优惠活动',
          })
        },
        //获取中奖名单关联活动
        getSectionList(id){
          this.ajax.postStream('/price-web/api/price/win/getActDiscountData',{discount_id: id, material_number: ''},res => {
            if(res.body.result && res.body.content) {
              this.actWinMainVersionsVO.win_type = res.body.content[0].win_type;
              this.actWinMainVersionsVO.discount_name = res.body.content[0].discount_name;
              this.actWinMainVersionsVO.discount_section = res.body.content[0].discount_section;
              this.actWinMainVersionsVO.discount_start = res.body.content[0].discount_start;
              this.actWinMainVersionsVO.discount_end = res.body.content[0].discount_end;
              this.discount_start = Fn.dateFormat(res.body.content[0].discount_start,'yyyy-MM-dd hh:mm:ss');
              this.discount_end = Fn.dateFormat(res.body.content[0].discount_end,'yyyy-MM-dd hh:mm:ss');
              this.addActWinningItemVoList(res.body.content);
              this.transTypeAndSection();
              this.discount_item_desc = '';
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        //导入excel文件后，从回调函数获取数据
        uploadCallback (data){
          console.log(123123,data.body.content);
          if (data.body.content && data.body.content.length > 0) {
            this.actWinningDetailList = data.body.content;
            // for (let i=0; i<data.body.content.length; i++){
            //   this.actWinningDetailList.push(data.body.content[i]);
            // }
            this.baocun = false;
            this.count = this.actWinningDetailList.length;
          }
        },
        //封装优惠项目
        addActWinningItemVoList(content){
          this.actWinningItemVoList = [];
          //this.discount_item_desc = "";
          for (let i=0; i < content.length; i++){
            console.log(content[i].discount_item_desc);
            if (content[i].discount_item_desc && content[i].discount_item_desc !== "") {
              this.discountItem.discount_item_desc = content[i].discount_item_desc;
              this.discountItem.discount_item_id = content[i].discount_item_id;
              this.discountItem.discount_item_no = content[i].discount_item_no;
              this.discountItem.discount_id = content[i].discount_id;
              this.discountItem.lock_status = content[i].lock_status;
              this.discountItem.discount_condition_dec = content[i].discount_condition_dec;
              this.discountItem.material_number_list = content[i].material_number_list;
              this.actWinningItemVoList.push(this.discountItem);
              //this.discount_item_desc += this.num + "." + content[i].discount_item_desc + "/";
              this.discountItem = {
                discount_item_desc:'',
                discount_item_id:'',
                discount_item_no:'',
                discount_id:'',
                lock_status:'',
              };
              //this.num++;
            } else {
              this.$message.error("优惠项目获取失败，通过中奖名单关联活动获取数据为空！");
            }
          }
          console.log("count",this.discount_item_desc);
        },
        //优惠项目选择框
        selectDiscount(){
          if (!this.actWinMainVersionsVO.discount_name || this.actWinMainVersionsVO.discount_name === ""){
            this.$message.error("请先选择中奖名单关联活动！");
            return;
          }
          //this.discount_item_list = [];
          this.$root.eventHandle.$emit('alert', {
            params: {
              discountId: this.discountId,
              dataList:this.actWinningItemVoList,
              callback: data => {
                this.discount_item_list = data; 
                this.addDiscountItemDesc();
              }
            },
            component: ()=>import('@components/discount/awardDiscountItemList'),
            style: 'width:700px;height:350px',
            title: '优惠项目',
          })
        },
        //拼接优惠项目
        addDiscountItemDesc(){
          this.discount_item_desc = "";
          for (let i=0; i<this.discount_item_list.length; i++){
            delete(this.discount_item_list[i].lock_status);
            this.discount_item_desc += (i+1) + "." + this.discount_item_list[i].discount_item_desc + "/";
          }
        },
        //保存
        save() {
          if (this.actWinMainVersionsVO.win_quota === ""){
            this.$message.error("中奖名额不能为空！");
            return;
          }
          if (this.ifNum(this.actWinMainVersionsVO.win_quota)){
            this.$message.error("中奖名额只能为正整数！");
            return;
          }
          if (this.actWinMainVersionsVO.win_name === ""){
            this.$message.error("中奖活动名称不能为空！");
            return;
          }
          if (this.actWinMainVersionsVO.win_explain === ""){
            this.$message.error("中奖活动规则说明不能为空！");
            return;
          }
          if (this.actWinMainVersionsVO.discount_name === ""){
            this.$message.error("请选择中奖名单关联活动！");
            return;
          }
          if (!!this.actWinMainVersionsVO.dealer_refund_threshold){
            let num = this.actWinMainVersionsVO.dealer_refund_threshold
            if(typeof num === 'number' || (num.toString().split('.')[1] && num.toString().split('.')[1].length > 2)){
                this.$message.error("经销待返门槛类型为数值，最多只能有两位小数");
                return;
            }
          }
          let number = this.num - 1;
          this.discount_item_list.map(item => {
              delete item.material_number_list
              delete item.discount_condition_id
              
              delete item.discount_condition_dec
              delete item.discount_end
              delete item.discount_name
              delete item.discount_section
              delete item.discount_start
              delete item.win_type
              item.winning_main_id = ''
              item.id = ''
          })
          let data = {
            actWinMainVersionsVO:this.actWinMainVersionsVO,
            actWinningDetailList:this.actWinningDetailList,
            actWinningItemVoList:this.discount_item_list,
          };
          data.actWinMainVersionsVO.discount_id = this.discountId
          this.ifLoading = true;
          this.baocun = true;
          this.ajax.postStream('/price-web/api/price/win/winMainSave?permissionCode=WINNING_MAIN_SAVE',data,res => {
            if(res.body.result && res.body.content) {
              this.ifSave = true;
              this.actWinMainVersionsVO.win_no = res.body.content;
              this.$message.success("保存成功！");
              this.tijiao = false;
              this.jiancha = false;
              this.shanchu = false;
              this.getAwardList();
              this.goAwardDetail();
              this.$root.eventHandle.$emit('removeTab',this.params.tabName);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              this.baocun = false;
            }
            this.ifLoading = false;
          }, err => {
            this.$message.error(err);
            this.ifLoading = false;
            this.baocun = false;
          });
        },
        //跳转详情页
        goAwardDetail(){
          let params = {
            win_no : this.actWinMainVersionsVO.win_no,
            main_status : this.actWinMainVersionsVO.main_status
          };
          this.$root.eventHandle.$emit('creatTab', {
            name: "中奖名单详情",
            params: params,
            component: () => import('@components/discount/awardDetail.vue')
          });
        },
        //检查名单
        check(){
          if (this.ifSave) {
            this.ajax.postStream('/price-web/api/price/win/checkWinningData?permissionCode=WINNING_MAIN_CHECK',{win_no:this.actWinMainVersionsVO.win_no},res => {
              if(res.body.result) {
                this.ifCheck = true;
                this.$message.success("核对成功！");
                this.getAwardList();
              } else {
                res.body.msg && this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
            });
          } else {
            this.$message.error("无法进行检查，该信息未保存！");
          }
        },
        //提交
        submit(){
          if (this.ifSave && this.ifCheck) {
            let data = {
              win_no:this.actWinMainVersionsVO.win_no,
              main_status:"SUBMIT",
              audit_name:"",
            };
            this.ajax.postStream('/price-web/api/price/win/auditWinning?permissionCode=WINNING_MAIN_SUBMIT',data,res => {
              if(res.body.result) {
                this.$message.success("提交成功");
                this.baocun = true;
                this.tijiao = true;
                this.jiancha = true;
                this.shanchu = true;
                this.getAwardList();
              } else {
                res.body.msg && this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
            });
          } else {
            this.$message.error("无法提交，该信息未保存或未进行检查！");
          }
        },
		    //删除
		    delAward(){
          this.$confirm('当前操作会导致该中奖信息被删除，是否继续？','提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'danger'
          }).then(()=>{
            this.ajax.postStream('/price-web/api/price/win/deleteActWinMainData?permissionCode=WINNING_MAIN_DELETE',{win_no:this.actWinMainVersionsVO.win_no},res => {
              if(res.body.result) {
                this.$message.success(res.body.msg);
                this.$root.eventHandle.$emit('removeTab',this.params.tabName);
              } else {
                res.body.msg && this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
            });
          }).catch(()=>{
            return false;
          })
        },
        //时间维度中英文转换
        transTypeAndSection(){
          switch (this.actWinMainVersionsVO.win_type) {
            case "ORDER_DATE" : this.win_type = "拍单时间"; break;
            case "PAYOFF_DATE" : this.win_type =  "支付时间"; break;
          }
          switch (this.actWinMainVersionsVO.discount_section) {
            case "INTERVAL_ACT" : this.discount_section = "区间活动"; break;
            case "DAILY_ACT" : this.discount_section =  "日常活动"; break;
          }
        },
        ifNum(value) {
          if (value === "" || !value){
            return true;
          }
          if (isNaN(value)){
            return true;
          } else {
            //是否为正整数
            if (/(^[1-9]\d*$)/.test(value)){
              return false;
            } else {
              return true;
            }
          }
          return false;
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.pageSize = pageSize;
          this.getAwardPersonList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.pageNo = page;
          this.getAwardPersonList();
        },
        discountfChange(val){
          if(!val){
            this.discount_item_desc = '';
          }
        }
      },
    }
</script>

<style scoped>

</style>
