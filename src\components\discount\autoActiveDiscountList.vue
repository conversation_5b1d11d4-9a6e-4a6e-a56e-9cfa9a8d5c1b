<template>
    <div class='xpt-flex'>
        <xpt-headbar>
            <el-button type='success' size='mini'  slot='left'  @click="() => getList('', '')">刷新</el-button>
            <el-button type='primary' size='mini'  slot='left'  @click="addNew">新增</el-button>
            <el-button type='primary' size='mini'  slot='left'  @click="change">修改</el-button>
        </xpt-headbar>
        <xpt-list
            :data='tableData'
            :colData='cols'
            :showHead='false'
            selection='radio'
            ref="autoActiveDiscountList"
            @radio-change='select'
        ></xpt-list>
    </div>
</template>

<script>
export default {
	data () {
        let self = this;
        return {
            tableData: [],
            cols: [
                {
					label: '优惠类别',
					width: 130,
					prop: 'discount_category',
                    format: 'auxFormat',
					formatParams: 'dicount_category_match',
				}, {
					label: '是否前置生成商品',
					width: 130,
					prop: 'if_before_create_goods',
                    format: 'yesOrNo',
				}, {
                    label: '是否前置POS',
					width: 90,
					prop: 'if_before_pos',
                    format: 'yesOrNo'
                }, {
					label: '阶梯',
					width: 60,
					prop: 'level',
                    formatter(val) {
                        switch (val) {
                            case "1": return "1";                            case "2": return "2";                            case "3": return "3";                            default: return val;                        }
                    }
				}, {
					label: '按行互斥活动',
					width: 250,
					prop: 'row_reject_discount_category',
                    formatter(val) {
                        if (!!val) {
                            let rowRejectDiscountCategoryList = val.split(','), names = ''
                            names = rowRejectDiscountCategoryList.reduce((name, cur,idx) => {
                                if (idx == (rowRejectDiscountCategoryList.length -1) ) {
                                    name += self.rejectDiscountCategoryCategoryObj[cur]
                                } else {
                                    name += self.rejectDiscountCategoryCategoryObj[cur] + ','
                                }
                                return name
                            }, '')
                            return names
                        } else {
                            return ''
                        }

                    }
				}, {
					label: '互斥活动',
					width: 250,
					prop: 'reject_discount_category',
                    formatter(val) {
                        if (!!val) {
                            let rejectDiscountCategoryList = val.split(','), names = ''
                            names = rejectDiscountCategoryList.reduce((name, cur,idx) => {
                                if (idx == (rejectDiscountCategoryList.length -1) ) {
                                    name += self.rejectDiscountCategoryCategoryObj[cur]
                                } else {
                                    name += self.rejectDiscountCategoryCategoryObj[cur] + ','
                                }
                                return name
                            }, '')
                            return names
                        } else {
                            return ''
                        }
                    }
				},{
					label: '优惠子类型',
					width: 130,
					prop: 'discount_subtype',
					formatter(val) {
                        if (!!val) {
                            let discountSubTypeList = val.split(','), names = ''
                            names = discountSubTypeList.reduce((name, cur,idx) => {
                                if (idx == (discountSubTypeList.length -1) ) {
                                    name += self.discountSubClassObj[cur]
                                } else {
                                    name += self.discountSubClassObj[cur] + ','
                                }
                                return name
                            }, '')
                            return names
                        } else {
                            return ''
                        }
                    }
				}, {
                    label: '活动形式',
                    prop: 'activity_type',
                    formatter(val) {
                        let obj = {
                            'DISCOUNT_ACTIVITY': '优惠活动',
                            'PRICE_ACTIVITY': '价目活动'
                        }
                        return obj[val] || ''
                    }
                }, {
					label: '是否有效',
					width: 70,
					prop: 'if_enable',
                    format: 'yesOrNo',
				},
        {
          label: '线下门店专属',
          width: 140,
          prop: 'if_offline_store_exclusive',
          format: 'yesOrNo'
        },
        {
					label: '是否可选',
					width: 70,
					prop: 'if_optional',
                    format: 'yesOrNo',
				}, {
					label: '创建人',
					width: 130,
					prop: 'creator_name'
				}, {
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 140
				}, {
					label: '修改人',
					width: 130,
					prop: 'last_modifier_name'
				}, {
					label: '修改时间',
					prop: 'last_modify_time',
					format: 'dataFormat1',
					width: 140
				},
            ],
            totalPage: 0,
            btns: [],
            searchObj:{
				page_no:1,
				page_size: self.pageSize,
				act_name:'',
				page_name: 'pms_act',
				where: []
			},
            selectRow: '',
            rejectDiscountCategoryCategoryObj: __AUX.get('dicount_category_match').reduce((pre, cur, idx, arr) => {
					pre[cur.code] = cur.name
					return pre
			},{}),
            discountSubClassObj: __AUX.get('discountSubclass').reduce((pre, cur, idx, arr) => {
					pre[cur.code] = cur.name
					return pre
			},{}),
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        select (row) {
            this.selectRow = row
        },
        addNew(type) {
            let self = this,params = {
               isAlert: true,
                callback: data => {
                    self.getList()
                },
            }
            type == 'change' ? params.auto_active_discount_setting_id = this.selectRow.auto_active_discount_setting_id : ''
            this.$root.eventHandle.$emit('alert', {
                params,
                component:()=>import('@components/discount/addAutoActiveDiscount'),
                style:'width:800px;height:500px',
                title: type == 'change' ? '修改优惠活动配置' : '新增优惠活动配置',
            })
        },
        change() {
            if (!this.selectRow) {
                this.$message.error('请至少选择一项')
                return
            }
            this.addNew('change')
        },
        preSearch(where, resolve) {
            this.searchObj.where = where
            this.getList(resolve)
        },
        getList(resolve) {
            this.ajax.postStream('/price-web/api/autoActiveDiscountSetting/list',{},res => {
                this.$refs.autoActiveDiscountList.clearRadioSelect();
                if(res.body.result && res.body.content) {
                this.tableData = res.body.content;
                //   this.totalPage = res.body.content.count;
                } else {
                res.body.msg && this.$message.error(res.body.msg);
                }
                resolve && resolve();
          }, err => {
                this.$refs.autoActiveDiscountList.clearRadioSelect();
                resolve && resolve();
                this.$message.error(err);
          })
        }
    }
}
</script>
