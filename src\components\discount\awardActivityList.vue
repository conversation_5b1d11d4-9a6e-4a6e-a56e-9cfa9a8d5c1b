//中奖名单关联活动弹窗
<template>
  <xpt-list
    :data="dataList"
    :colData="cols"
    :btns="btns"
    :pageTotal='pageTotal'
    :selection="selection"
    :searchPage='search.page_name'
    @search-click='searchClick'
    @page-size-change='sizeChange'
    @current-page-change='pageChange'
    @radio-change='select'
  ></xpt-list>
</template>

<script>
    export default {
      name: "awardActivityList",
      props:["params"],
      data() {
        let self = this;
        return {
          dataList:[],
          pageTotal:0,
          selectRow:'',
          selection:"radio",
          search:{
            page_name:"act_discount",
            where:[],
            page_size:50,
            page_no:1,
          },
          btns:[
            {
              type: "success",
              txt: "确认",
              loading: false,
              click() {
                self.confirm();
              },
            }
          ],
          cols:[
            {
              label: '优惠活动ID',
              prop: 'discount_id',
              width: 120
            }, {
              label: '优惠活动编码',
              prop: 'discount_no',
              width: 120
            },{
              label: '优惠活动名称',
              prop: 'discount_name',
              width: 200
            }, {
              label: '优惠类型',
              prop: 'discount_type',
              format: 'auxFormat',
              formatParams: 'discountCategory',
            }, {
              label: '子类型',
              prop: 'discount_sub_type',
              format: 'auxFormat',
              formatParams: 'discountSubclass',
            }, {
              label: '优惠条件',
              prop: 'discount_condition_type',
              format: 'auxFormat',
              width: 60,
              formatParams: 'discount_condition'
            },{
              label: '状态',
              prop: 'status',
              width: 60,
              formatter(val) {
                switch(val) {
                  case 'CREATE': return '创建'; break;
                  case 'SUBMITTED': return '提交审核'; break;
                  case 'APPROVED': return '已审核'; break;
                  case 'WAIT_APPROVED': return '待审核'; break;
                  case 'REJECTED': return '已驳回'; break;
                  case 'WITHDRAWED': return '已撤回'; break;
                  case 'RETRIAL': return '重新审核'; break;
                  case 'FORBIDDEN': return '禁用'; break;
                  case 'APPROVING': return '审核中'; break;
                  default: return val; break;
                }
              },
            },{
              label: '店铺范围',
              prop: 'store_area',
              formatter(val) {
                switch(val) {
                  case 1: return '全局'; break;
                  case 2: return '分组'; break;
                  case 0: return '店铺'; break;
                  default: return val; break;
                }
              }
            },{
              label: '活动区间',
              prop: 'act_section',
              format: 'auxFormat',
              formatParams: 'act_aection',
            },{
              label: '生效日期',
              prop: 'enable_time',
              format: 'dataFormat1',
              width: 140
            }, {
              label: '失效日期',
              prop: 'disable_time',
              format: 'dataFormat1',
              width: 140
            },{
              label: '变更状态',
              prop: 'change_status',
              formatter(val) {
                switch(val) {
                  case 'CHANGE_CREATE': return '变更创建'; break;
                  case 'CHANGE_WAIT_APPROVED': return '变更待审核'; break;
                  case 'CHANGE_APPROVING': return '变更审核中'; break;
                  case 'CHANGE_REJECTED': return '变更驳回'; break;
                  case 'CHANGE_APPROVED': return '变更已审核'; break;
                  default: return val; break;
                }
              },
              redirectClick(row) {
                self.toEditFun2(row)
              },
            }, {
              label: '审核人',
              prop: 'audit_person_name',
              width: 140
            }, {
              label: '审核日期',
              prop: 'audit_time',
              format: 'dataFormat1',
              width: 140
            },{
              label: '更新人',
              prop: 'last_modifier_name',
              width: 140
            },  {
              label: '说明',
              prop: 'remark'
            }
          ],
        }
      },
      methods: {
        getList(resolve) {
          this.search.where.push({
            "field":"9acb44549b41563697bb490144ec6258",
            "table":"54194b30f706421bdadf360c822f0ba2",
            "value":"APPROVED",
            "operator":"=",
            "condition":"AND",
            "listWhere":[]
          });//选择已审核状态的活动
          this.ajax.postStream('/price-web/api/actDiscount/list',this.search,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.pageTotal = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        confirm(){
          console.log("selectRow:%s",this.selectRow);
          if(this.selectRow === ""){
            this.$message({
              message:'请选择优惠活动！',
              type:'error'
            });
            return;
          }
          //关闭弹窗
          this.params.callback(this.selectRow);
          this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
        },
        select(s) {
          this.selectRow = s;
        },
        searchClick(list,resolve) {
          this.search.where = list;
          this.getList(resolve);
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page_size = pageSize;
          this.getList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.search.page_no = page;
          this.getList();
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
