//中奖名单详情
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='24'>
        <el-button type='success' size='mini' @click="getAwardList">刷新</el-button>
        <el-button type='info' size='mini' @click="save('MODIFY')" :disabled="biangeng" :loading="ifBiangeng">变更</el-button>
        <el-button type='info' size='mini' @click="save('SAVE')" :disabled="baocun">保存</el-button>
        <el-button type='warning' size='mini' @click="check" :disabled="jiancha">检查名单</el-button>
        <el-button type='primary' size='mini' @click="submit" :disabled="tijiao">提交</el-button>
        <el-button type='danger' size='mini' @click="delAward" :disabled="shanchu">删除</el-button>
      </el-col>
    </el-row>
    <el-row	:gutter='40' >
      <el-tabs v-model="firstTab" >
        <el-tab-pane label="中奖名单信息" name="awardList">
          <el-form label-position="right" label-width="140px">
            <el-col :span="6" style="width: 33%">
              <el-form-item label="单据编号：">
                <el-input v-model="actWinMainVersionsVO.win_no" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖名额：">
                <el-input v-model="actWinMainVersionsVO.win_quota" size='mini' style="width: 200px;" :disabled="baocun"></el-input>
              </el-form-item>
              <el-form-item label="中奖人数小计：">
                <el-input v-model="actWinMainVersionsVO.detail_num" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="创建人：">
                <el-input v-model="actWinMainVersionsVO.create_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="创建时间：">
                <el-input v-model="actWinMainVersionsVO.create_time" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="优惠项目：" style="height: 50px;">
                <el-input v-model="discount_item_desc" size='mini' type='textarea' style="width: 260.5%;;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖活动规则说明：" style="height: 50px; margin-top: 10px">
                <el-input v-model="actWinMainVersionsVO.win_explain" size='mini' type='textarea' style="width: 260.5%;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="width: 33%">
              <el-form-item label="中奖活动名称：">
                <el-input v-model="actWinMainVersionsVO.win_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖活动开始时间：">
                <el-input v-model="actWinMainVersionsVO.discount_start" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖活动结束时间：">
                <el-input v-model="actWinMainVersionsVO.discount_end" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="提交人：">
                <el-input v-model="actWinMainVersionsVO.commit_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="提交时间：">
                <el-input v-model="actWinMainVersionsVO.commit_time" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="width: 34%">
              <el-form-item label="中奖名单关联活动：">
                <el-input v-model="actWinMainVersionsVO.discount_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <!--<el-form-item label="驳回人：">
                <el-input v-model="awardAuditStatusData.reject_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="驳回时间：">
                <el-input v-model="awardAuditStatusData.reject_time" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>-->
              <el-form-item label="营销活动区间：">
                <el-input v-model="discount_section" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="审批人：">
                <el-input v-model="actWinMainVersionsVO.audit_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="审批时间：">
                <el-input v-model="actWinMainVersionsVO.audit_time" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="状态：">
                <el-input v-model="main_status" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="时间维度：">
                <el-input v-model="win_type" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="经销待返门槛：">
                <el-input v-model="actWinMainVersionsVO.dealer_refund_threshold" size='mini' type="number" :min="0" style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class='xpt-flex__bottom' id='bottom' v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane label="中奖人信息" name="awardPersonList" class='xpt-flex'>
          <xpt-list
            :data='actWinningDetailList'
            :colData='cols'
            :btns="btns"
            selection='none'
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
          >
            <xpt-import slot='btns' v-if="main_status === '提交' || main_status === '审核中' || main_status === '不通过'" :taskUrl="uploadUrl" :callback="uploadCallback" class='mgl10' :isupload="true"></xpt-import>
            <xpt-import slot='btns' v-else :taskUrl="uploadUrl" :callback="uploadCallback" class='mgl10' :isupload="false"></xpt-import>
            <template slot='phone' slot-scope='scope'>
								<xpt-eye-switch v-model="scope.row.phone" :readonly='true' :hideBorder="true" :aboutNumber="actWinMainVersionsVO.win_no"></xpt-eye-switch>
						</template>
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
  import Fn from '@common/Fn.js'
    export default {
      name: "awardDetail",
      props:["params"],
      data() {
        let self = this;
        return {
          actWinningDetailList:[],
          dataList:{},
          actWinMainVersionsVO:{},
          awardAuditStatusData:{
            audit_name:'',
            audit_time:'',
            reject_name:'',
            reject_time:'',
          },
          main_status:'',//审核状态
          win_type:'',//时间维度
          discount_section:'',//营销活动区间
          biangeng:true,
          baocun:true,
          jiancha:true,
          tijiao:true,
          shanchu:true,
          ifBiangeng:false,
          actWinningItemVoList:[],//优惠项目数据
          discount_item_desc:'', //优惠项目
          ifCheck:false,//是否点击检查名单
          version:"",//版本
          count:0,
          pageSize:50,
          pageNo:1,
          firstTab:"awardList",
          secondTab:"awardPersonList",
          pageSize:50,
          pageNO:1,
          uploadUrl:'/price-web/api/price/win/addImportDetail',
          btns:[
            // {
            //   type: "success",
            //   txt: "保存",
            //   loading: false,
            //   click() {
            //   },
            // },
          ],
          cols:[
            {
              label: "序号",
              prop: "add_no",
              width: "60"
            },
            {
              label: "中奖人",
              prop: "winner",
              width: "80"
            },
            {
              label: "中奖排名",
              prop: "rank",
              width: "60"
            },
            {
              label: "天猫公示订单",
              prop: "tid",
              width: "160"
            },
            {
              label: "中奖关联订单",
              prop: "winning_relate_tids",
              width: "160"
            },
            {
              label: "实际发货关联订单",
              prop: "winning_delivery_relate_tids",
              width: "160"
            },
            {
              label: "优惠项目编码",
              prop: "discount_item_no",
              width: "100"
            },
            {
              label: "中奖金额",
              prop: "winning_amount",
              width: "100"
            },
             {
              label: "手机号",
              slot: "phone",
              width: "120"
            },
            {
              label: "拍单时间",
              prop: "created",
              format: "dataFormat1",
              width: "140"
            },
            {
              label: "支付时间",
              prop: "pay_time",
              format: "dataFormat1",
              width: "140"
            },
            {
              label: "收货人",
              prop: "receiver_name",
              width: "80"
            },
            {
              label: "收货地址",
              prop: "receiver_address",
            },
            {
              label: "版本号",
              prop: "versions",
              width: "60"
            },
            {
              label: "状态",
              prop: "status",
              width: "60",
              formatter(val){
                switch (val) {
                  case "NORMAL" : return "正常";
                  case "CANCEL" : return "取消";
                }
              }
            },
            {
              label: "变更状态",
              prop: "change_status",
              width: "60",
              formatter(val){
                switch (val) {
                  case "WAIT" : return "等待";
                  case "PASS" : return "通过";
                  case "FAIL" : return "不通过";
                }
              }
            },
            // {
            //   label: "重复中奖",
            //   prop: "if_repeat",
            //   formatter(val) {
            //     if (val === true) {
            //       return "是"
            //     } else {
            //       return "否"
            //     }
            //   }
            // },
            // {
            //   label: "名单异常",
            //   prop: "if_normal",
            //   formatter(val) {
            //     if (val === true) {
            //       return "是"
            //     } else {
            //       return "否"
            //     }
            //   }
            // },
          ],
          ifClickUpload: false,
          uploadData: {},
        }
      },
      methods: {
        getAwardList() {
          let data = {
            win_no:this.params.win_no,
          };
          this.ajax.postStream('/price-web/api/price/win/getWinningData',data,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content;
              this.actWinMainVersionsVO = res.body.content.actWinMainVersionsVO;
              this.actWinMainVersionsVO.discount_start = Fn.dateFormat(res.body.content.actWinMainVersionsVO.discount_start,'yyyy-MM-dd hh:mm:ss');
              this.actWinMainVersionsVO.discount_end = Fn.dateFormat(res.body.content.actWinMainVersionsVO.discount_end,'yyyy-MM-dd hh:mm:ss');
              this.actWinMainVersionsVO.create_time = Fn.dateFormat(res.body.content.actWinMainVersionsVO.create_time,'yyyy-MM-dd hh:mm:ss');
              this.actWinMainVersionsVO.commit_time = Fn.dateFormat(res.body.content.actWinMainVersionsVO.commit_time,'yyyy-MM-dd hh:mm:ss');
              this.actWinMainVersionsVO.audit_time = Fn.dateFormat(res.body.content.actWinMainVersionsVO.audit_time,'yyyy-MM-dd hh:mm:ss');
              this.actWinningItemVoList = res.body.content.actWinningItemVoList;
              this.actWinningDetailList = res.body.content.actWinningDetailList;
              this.getDetailNum();
              this.count = this.actWinningDetailList.length;
              this.addActWinningItemVoList();
              this.transAuditStatus();
              this.transTypeAndSection();
              //this.getAwardPersonList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        getAwardPersonList(){
          let data = {
            win_no:this.params.win_no,
            page:{
              length:this.pageSize,
              pageNo:this.pageNo,
            }
          };
          this.ajax.postStream('/price-web/api/price/win/findDetailListVersionListPageList',data,res => {
            if(res.body.result && res.body.content) {
              this.actWinningDetailList = res.body.content.list;
              this.count = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        //拼接多个优惠项目数据
        addActWinningItemVoList() {
          this.discount_item_desc = "";
          for (let i=0; i < this.actWinningItemVoList.length; i++) {
            this.discount_item_desc += i+1 + "." + this.actWinningItemVoList[i].discount_item_desc + "/";
          }
        },
        //审核/变更状态转换成中文显示
        transAuditStatus(){
          switch (this.actWinMainVersionsVO.main_status) {
            case "SAVE" : this.main_status = "保存"; break;
            case "PASS" : this.main_status =  "通过检查"; break;
            case "SUBMIT" : this.main_status =  "提交"; break;
            case "AUDIT" : this.main_status =  "已审核"; break;
            case "FAIL" : this.main_status =  "不通过"; break;
            case "AUDITING" : this.main_status =  "审核中"; break;
          }
          this.checkStatus();
        },
        //按钮渲染
        checkStatus(){
          if (this.main_status === "已审核") {
            console.log("已审核");
            this.biangeng = false;
            this.baocun = true;
            this.jiancha = true;
            this.tijiao = true;
            this.shanchu = true;
          } else if (this.actWinMainVersionsVO.versions === "V1") {
            console.log("V1");
            this.biangeng = true;
            if (this.main_status === "保存" || this.main_status === "通过检查") {
              this.baocun = false;
              this.jiancha = false;
              this.tijiao = false;
              this.shanchu = false;
            } else {
              this.baocun = true;
              this.jiancha = true;
              this.tijiao = true;
              this.shanchu = true;
            }
          } else {
            console.log("V2");
            this.biangeng = true;
            this.shanchu = true;
            if (this.main_status === "保存" || this.main_status === "通过检查") {
              this.baocun = false;
              this.jiancha = false;
              this.tijiao = false;
            } else {
              this.baocun = true;
              this.jiancha = true;
              this.tijiao = true;
            }
          }
        },
        //时间维度、营销活动区间中英文转换
        transTypeAndSection(winType){
          switch (this.actWinMainVersionsVO.win_type) {
            case "ORDER_DATE" : this.win_type = "拍单时间"; break;
            case "PAYOFF_DATE" : this.win_type =  "支付时间"; break;
          }
          switch (this.actWinMainVersionsVO.discount_section) {
            case "INTERVAL_ACT" : this.discount_section = "区间活动"; break;
            case "DAILY_ACT" : this.discount_section =  "日常活动"; break;
          }
        },
        //当审核状态为FAIL时审核人和审核时间置空，数据显示到驳回人和驳回时间
        configAuditStatusData(){
          if (this.actWinMainVersionsVO.main_status === "FAIL") {
            this.awardAuditStatusData.reject_name = this.actWinMainVersionsVO.audit_name;
            this.awardAuditStatusData.reject_time = this.actWinMainVersionsVO.audit_time;
            this.awardAuditStatusData.audit_name = "";
            this.awardAuditStatusData.audit_time = "";
          } else {
            this.awardAuditStatusData.audit_name = this.actWinMainVersionsVO.audit_name;
            this.awardAuditStatusData.audit_time = this.actWinMainVersionsVO.audit_time;
            this.awardAuditStatusData.reject_name = "";
            this.awardAuditStatusData.reject_time = "";
          }
        },
        //导入excel文件后，从回调函数获取数据
        uploadCallback (data){
          let num = 0;
          let ary = [];
          let newArr = [];
          for (let x = 0;x < this.actWinningDetailList.length;x++){
            ary.push(this.actWinningDetailList[x].rank);
          }
          //清除数组中相同值的元素
          //let newArr = [...new Set(ary)];
          for (let y = 0;y < ary.length;y++){
           if (newArr.indexOf(ary[y]) === -1) {
             newArr.push(ary[y]);
           }
          }
          if (data.body.content && data.body.content.length > 0) {
            for (let i = 0;i < data.body.content.length;i++) {
              for (let j = 0;j < newArr.length; j++) {
                if (newArr[j] === data.body.content[i].rank) {
                  num ++;
                }
              }
            }
            if (this.actWinMainVersionsVO.versions === "V1") {
              //中奖名单信息-状态为已审核状态时，导入时追加数据，保留正常状态的数据,新导入的数据会替换原来所有除了中奖人信息-状态为正常状态的数据
              if (this.main_status === "已审核") {
                console.log("num",num,this.count);
                // if (num !== data.body.content.length){
                //   this.$message.error("导入的中奖排名不存在或为空值！");
                //   return;
                // }
                for (let i = this.count - 1; i >= 0; i--) {
                  if (!this.actWinningDetailList[i].status || this.actWinningDetailList[i].status !== "NORMAL") {
                    this.actWinningDetailList.splice(i, 1);
                    this.count = this.actWinningDetailList.length;
                    console.log("1111",this.count,i);
                  }
                }
                for (let j = 0;j < data.body.content.length;j++){
                  this.actWinningDetailList.push(data.body.content[j]);
                }
              } else {
                this.actWinningDetailList = data.body.content;
              }
              console.log("aaaa",this.actWinningDetailList);
            } else {
              console.log("num",num);
              // if (num !== data.body.content.length){
              //   this.$message.error("导入的中奖排名不存在或为空值！");
              //   return;
              // }
              if (this.main_status === "保存" || this.main_status === "通过检查") {
                let list = [], bodyList = []
                for (let i = data.body.content.length - 1;i >= 0;i--) {
                  if (data.body.content[i].rank) {
                    bodyList.push(data.body.content[i].rank)
                  }
                }
                for  (let j = this.count - 1; j >= 0; j--){
                  if (this.actWinningDetailList[j].status && this.actWinningDetailList[j].rank && this.actWinningDetailList[j].status == "NORMAL") {
                      list.push(this.actWinningDetailList[j].rank)
                      if (bodyList.includes(this.actWinningDetailList[j].rank)) {
                        this.actWinningDetailList[j].status = 'CANCEL'
                      }
                  }
                }
                for (let i = data.body.content.length - 1;i >= 0;i--) {
                  this.actWinningDetailList.push(data.body.content[i])
                }
              } else if (this.main_status === "已审核") {
                for (let i = data.body.content.length - 1;i >= 0;i--) {
                  this.actWinningDetailList.push(data.body.content[i]);
                }
              }
            }
            this.getDetailNum();
            this.count = this.actWinningDetailList.length;
          }
        },
        getDetailNum(){
          let detail_num = 0;
          this.actWinningDetailList.forEach(item => {
                if(item.status == 'NORMAL' ||!item.status){
                  detail_num += 1;
                }
              });
              this.actWinMainVersionsVO.detail_num = detail_num;
        },
        //变更
        save(param){
          this.ifBiangeng = true;
          this.biangeng = true;
          let data = {
            actWinMainVersionsVO:{
              id:this.actWinMainVersionsVO.id,
              win_quota:this.actWinMainVersionsVO.win_quota,
              detail_num:this.actWinMainVersionsVO.detail_num,
              discount_id: this.actWinMainVersionsVO.discount_id,
            },
            actWinningDetailList:this.actWinningDetailList,
            actWinningItemVoList:[],
          };
          let url = "";
          if (param === "SAVE") {
            //保存
            url = "/price-web/api/price/win/winMainSave?permissionCode=WINNING_MAIN_SAVE";
          } else if (param === "MODIFY") {
            //变更
            url = "/price-web/api/price/win/winMainSave?permissionCode=WINNING_MAIN_EDIT";
          }
          this.ajax.postStream(url,data,res => {
            if(res.body.result && res.body.content) {
              this.$message.success(res.body.msg);
              this.getAwardList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              this.biangeng = false;
            }
            this.ifBiangeng = false;
          }, err => {
            this.$message.error(err);
            this.ifBiangeng = false;
            this.biangeng = false;
          });
        },
        //检查名单
        check(){
          this.ajax.postStream('/price-web/api/price/win/checkWinningData?permissionCode=WINNING_MAIN_CHECK',{win_no:this.actWinMainVersionsVO.win_no},res => {
            if(res.body.result && res.body.content) {
              this.ifCheck = true;
              this.$message.success(res.body.msg);
              this.getAwardList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        //提交
        submit(){
          let data = {
            win_no:this.actWinMainVersionsVO.win_no,
            main_status:"SUBMIT",
            audit_name:"",
          };
          this.ajax.postStream('/price-web/api/price/win/auditWinning?permissionCode=WINNING_MAIN_SUBMIT',data,res => {
            if(res.body.result) {
              this.$message.success("提交成功");
              this.getAwardList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        //删除
        delAward(){
          this.$confirm('当前操作会导致该中奖信息被删除，是否继续？','提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'danger'
          }).then(()=>{
            this.ajax.postStream('/price-web/api/price/win/deleteActWinMainData?permissionCode=WINNING_MAIN_DELETE',{win_no:this.actWinMainVersionsVO.win_no},res => {
              if(res.body.result) {
                this.$message.success(res.body.msg);
                this.$root.eventHandle.$emit('removeTab',this.params.tabName);
              } else {
                res.body.msg && this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
            });
          }).catch(()=>{
            return false;
          })
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.pageSize = pageSize;
          this.getAwardPersonList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.pageNo = page;
          this.getAwardPersonList();
        },
      },
      mounted: function() {
        this.getAwardList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getAwardList();
        });
      }
    }
</script>

<style scoped>
  .awardInfo {
    border-bottom: 1px solid #d1dbe5;
  }
  .awardInfo div{
    padding: 0 10px;
    display: block;
    height: 24px;
    line-height: 24px;
    color: #20a0ff;
    border-bottom: 3px solid #20a0ff;
    display: inline-block;
  }

</style>
