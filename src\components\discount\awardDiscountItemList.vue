<template>
  <xpt-list
    :data="dataList"
    :colData="cols"
    :btns="btns"
    :orderNo="true"
    :selection="selection"
		@search-click='discountSearch'
		searchHolder='请输入优惠清单编码搜索'
    :selectable="selectDisabled"
    @selection-change='select'
  ></xpt-list>
</template>

<script>
    export default {
      name: "awardDiscountItemList",
      props:["params"],
      data(){
        let self = this;
        return {
          dataList:[],
          selectRow:[],
          selection:"checkbox",
          btns:[
            {
              type: "primary",
              txt: "确认",
              loading: false,
              click() {
                self.confirm();
              },
            },
          ],
          cols:[
           {
             label: "优惠项目编码",
             prop: "discount_item_no",
           }, {
							label: '优惠条件',
							prop: 'discount_condition_dec'
						}, {
							label: '优惠清单',
							prop: 'material_number_list'
						}, 
           {
             label: "优惠项目名称",
             prop: "discount_item_desc",
           },
           {
             label: "状态",
             prop: "lock_status",
             formatter(val){
               if (val === "Y") {
                 return "已占用";
               } else {
                 return "未占用";
               }
             }
           },
          ]
        }
      },
      methods:{
				discountSearch(txt) {
					this.material_number = txt
					this.getList()
        },
        getList(){
					this.ajax.postStream('/price-web/api/price/win/getActDiscountData',{discount_id: this.params.discountId, material_number: this.material_number},res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content
            } else {
							this.dataList = [];
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
						this.dataList = [];
            this.$message.error(err);
          });
        },
        confirm(){
          let ids = [];
          this.selectRow.find(d=>{
            ids.push(d);
          });
          if(!ids.length){
           this.$message.error("请至少选择一项内容！");
           return;
          }
          //关闭弹窗
          this.params.callback(ids);
          this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
        },
        // 多选事件
        select(s){
          this.selectRow = s;
        },
        selectDisabled(row){
          //console.log("row",row);
          if (row.lock_status === "Y") {
            return false;
          } else {
            return true;
          }
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
