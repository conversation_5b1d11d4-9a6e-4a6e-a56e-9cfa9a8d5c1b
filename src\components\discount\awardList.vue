//中奖名单列表
<template>
  <count-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :searchPage='search.page_name'
    :selection="selection"
    :orderNo="true"
    :pageTotal='count'
    @search-click='searchClick'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    countUrl="/price-web/api/price/win/findActWinningDetailListCount"
    :showCount ='showCount'
  ></count-list>
</template>

<script>

    import countList from '@components/common/list-count'
    export default {
      components: {
        countList,
      },
      name: "awardList",
      data() {
        let self = this;
        return {
			    showCount:false,
          dataList:[],
          count:0,
          selection:"none",
          search:{
            page_name:"act_winning_main",
            where:[],
            page:{
              length:50,
              pageNo:1,
            },
          },
          btns:[
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.getList();
              },
            },
          ],
          cols:[
            {
              label: "单据编号",
              prop: "win_no",
              width: 160,
              redirectClick(row) {
                let params = {};
                params.win_no = row.win_no;
                params.main_status = row.main_status;
                self.$root.eventHandle.$emit('creatTab', {
                  name: "中奖名单详情",
                  params: params,
                  component: () => import('@components/discount/awardDetail.vue')
                });
              },
            },
            {
              label: "中奖名单名称",
              prop: "win_name",
              width: 200,
            },
            {
              label: "中奖名单关联活动",
              prop: "discount_name",
              width: 120,
            },
            {
              label: "营销活动区间",
              prop: "discount_section",
              width: 120,
              formatter(val){
                switch (val) {
                  case "INTERVAL_ACT" : return "区间活动";
                  case "DAILY_ACT" : return "日常活动";
                }
              }
            },
            {
              label: "版本号",
              prop: "versions",
            },
            {
              label: "审批状态",
              prop: "main_status",
              formatter(val){
                switch (val) {
                  case "SAVE" : return "保存";
                  case "PASS" : return "通过检查";
                  case "SUBMIT" : return "提交";
                  case "AUDIT" : return  "已审核";
                  case "FAIL" : return  "不通过";
                  case "AUDITING" : return  "审核中";
                }
              }
            },
            {
              label: "中奖名额",
              prop: "win_quota",
              width:60
            },
            {
              label: "中奖人数",
              prop: "detail_num",
              width:60
            },
            {
              label: "创建人",
              prop: "create_name",
              width:60
            },
            {
              label: "创建时间",
              prop: "create_time",
              format: "dataFormat1" ,
              width:140
            },
            {
              label: "提交人",
              prop: "commit_name",
            },
            {
              label: "提交时间",
              prop: "commit_time",
              format: "dataFormat1" ,
              width:140
            },
            {
              label: "审批人",
              prop: "audit_name",
            },
            {
              label: "审批人时间",
              prop: "audit_time",
              format: "dataFormat1" ,
              width:140
            },
          ],
        }
      },
      methods: {
        getList(resolve,callback){
          this.addBtnCheck();
          this.btns[0].loading = true;
          this.ajax.postStream('/price-web/api/price/win/findWinMainPageList',this.search,res => {
            callback && callback();
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              // this.count = res.body.content.count;
              if(!this.showCount){
                let total = this.search.page.length * this.search.page.pageNo
                this.count = res.body.content.list.length == (this.search.page.length+1)? total+1:total;
            }
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        //校验是否有新增按钮权限
        addBtnCheck(){
          let self = this;
          self.ajax.postStream('/user-web/api/userPerson/getUserPersonBusiness?permissionCode=WINNING_MAIN_INSERT', {identification: this.getUserInfo('employeeNumber')}, res => {
            if (res.body.result) {
              self.btns = [
                {
                  type: "success",
                  txt: "刷新",
                  loading: false,
                  click() {
                    self.getList();
                  },
                },
                {
                  type: "primary",
                  txt: "新增",
                  loading: false,
                  click() {
                    self.addAward();
                  },
                }
              ];
            }
          });
        },
        addAward(){
          this.$root.eventHandle.$emit('creatTab', {
            name: "新增中奖名单",
            component: () => import('@components/discount/addAward.vue')
          });
        },
        searchClick (list, resolve){
          this.search.where = list;
          new Promise((res,rej)=>{
            this.getList(resolve,res);
          }).then(()=>{
            if(this.search.page.pageNo != 1){
              this.count = 0;
            }
            this.showCount = false;
          })
          // this.getList(resolve);
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page.length = pageSize;
          this.getList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.search.page.pageNo = page;
          this.getList();
        },
      },
      mounted: function() {
        // this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
