<!-- 优惠信息查看，用于合并订单添加优惠列表页面查看优惠详情 -->
<template>
	<div class='xpt-flex'>
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='24'>
        <el-button type='success' size='mini' @click="init">刷新</el-button>
      </el-col>
    </el-row>
		<div>
			<el-tabs v-model='firstTab'>
				<el-tab-pane label='基础信息' name='first' class='xpt-flex'>
					<xpt-form :data='form' :cols='firstCols' label='120px'></xpt-form>
				</el-tab-pane>
				
			</el-tabs>
		</div>
		<el-row class='xpt-flex__bottom' v-fold>
			<el-tabs v-model="secondTab">
				<el-tab-pane label='操作记录' name="discount_GoodsList" class='xpt-flex' >
					<xpt-list
						ref='GoodsList'
						:data='discountItemList'
						:colData='goodsCols'
						:showHead='false'
						selection=''
					></xpt-list>
				</el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
export default {
	props: ['params'],
	data() {
        var self = this
		return {
      discountItemList: [], // 优惠项目
			form: {
                // 优惠活动编码
                discount_no: null,
                // 优惠活动名称
                discount_name: null,
                // 生效时间
                enable_time: null,
                // 失效时间
                disable_time: null,
                // 店铺范围
                store_area: null,
                // 优惠类型
                discount_type: null,
                // 优惠子类型
                discount_sub_type: null,
                discount_condition_type: null,
                if_superposition_discount: null,
                if_each_full: null,
                if_need_audit: null,
                if_auto_add: null,
                remark: null,
                listActDiscountItemVo:[],
                // 创建人
                creator_name: null,
                // 创建日期
                create_time: null,
                // 审核人
                audit_name: null,
                // 审核日期
                audit_time: null,
                // 最后修改人
                modifier_name: null,
                // 最后修改时间
				modify_time: null,
				additional_act_content: null, // 活动类型：FINALPAYACT尾款订单活动 GIFTORDERACT礼品订单活动
				additional_start_time: null, // 生效时间
				additional_end_time: null, // 失效时间
				additional_flag: null, // 商品标识
				dealer_discount:null
			},
			secondTab: 'discount_GoodsList',
			firstTab: 'first',
			goodsCols: [
				{
					label: '操作人',
					prop: 'operator_name',
					width: 150
				}, {
					label: '操作描述',
					prop: 'remark'
				}, {
					label: '操作时间',
					prop: 'operation_time',
          format: 'dataFormat1',
					width: 150
				}
			],
			firstCols: [
				[
					{
						label: '中奖单据编号:',
						key: 'win_no'
					}, {
						label: '中奖单据名称:',
						key: 'win_name'
					}, {
						label: '中奖名单关联活动:',
						key: 'discount_name'
					}, {
						label: '中奖人:',
						key: 'winner',
					}, {
						label: '中奖人排名:',
						key: 'rank'
          }
        ], [
          {
						label: '活动开始时间:',
						key: 'discount_start',
						format: 'dataFormat1'
					}, {
						label: '活动结束时间:',
						key: 'discount_end',
						format: 'dataFormat1'
					}, {
						label: '天猫公示订单:',
						key: 'tid',
					}, {
						label: '中奖关联订单:',
						key: 'winning_relate_tids',
					}, {
						label: '实际发货关联订单:',
						key: 'winning_delivery_relate_tids',
					}
				], [
					{
						label: '优惠项目编码:',
						key: 'discount_item_no'
					}, {
						label: '版本号:',
						key: 'versions'
					}, {
						label: '状态:',
            key: 'status',
            formatter(val) {
              switch (val) {
                case "NORMAL" : return "正常";
                case "CANCEL" : return "取消";
              }
            }
					}, {
						label: '变更状态:',
						key: 'change_status',
						formatter(val){
              switch (val) {
                case "WAIT" : return "等待";
                case "PASS" : return "通过";
                case "FAIL" : return  "不通过";
              }
            }
					}
				]
      ],
		}
	},
	methods: {
    // 获取详情
    getInfo () {
      this.ajax.postStream('/price-web/api/price/win/getActWinningDetailById',{id: this.params.id},res => {
        if(res.body.result && res.body.content) {
          this.form = res.body.content;
        } else {
          res.body.msg && this.$message.error(res.body.msg);
        }
      }, err => {
        this.$message.error(err);
      });
    },
    getLogList () {
      this.ajax.postStream('/order-web/api/receipt/v2/logList',{source_id: this.params.id},res => {
        if(res.body.result && res.body.content) {
          this.discountItemList = res.body.content.list || [];
        } else {
          res.body.msg && this.$message.error(res.body.msg);
        }
      }, err => {
        this.$message.error(err);
      });
    },
    init() {
      this.getInfo()
      this.getLogList()
    }
	},
  mounted() {
    this.init()
  }
}	
</script>
<style lang="stylus">

</style>

