<!-- 优惠活动列表 -->
<template>
	<div class="xpt-flex">
	
	<xpt-list
		:data='tableData'
		:btns='btns'
		:colData='cols'
		:orderNo="true"
		selection=''
		class="scroll-tab"
	>
	</xpt-list>
	<!-- <div></div> -->
	</div>
</template>
<script>
export default {
	data(){
		var self = this;
		return{
			tableData:[],
			btns: [
				{
					type: 'primary',
					txt: '确认',
					click: self.submit
				}, {
					type: 'danger',
					txt: '取消',
					loading: false,
					click: self.cancel
				}
				
			],
			cols: [
				{
					label: '物料编码',
					prop: 'code',
					width: 150
				}
			],
			
		}
	},
	props:['params'],
	mounted(){
		var self=this;
		console.log(self.params.data);
		// self.searchFun(self.params.data);
		self.params.data.forEach(item => {
			self.tableData.push({'code':item})
		});

	},
	destroyed(){
		this.$root.offEvents('discountAdd');
    },
	methods:{
		
		submit(){
			this.closeAlert(true)
		},
		cancel(){
			this.closeAlert(false)
		},
		closeAlert(data) {
				// console.log(list);
				let self = this;
				self.params.callback&&self.params.callback(data)
				self.$root.eventHandle.$emit('removeAlert', self.params.alertId)
			},
		ajaxPost(api,data){
			var self = this;
			var url = api;
			this.ajax.postStream(url,data, (res) => {
				console.log(res)
			});
		},
		
	}
}
</script>