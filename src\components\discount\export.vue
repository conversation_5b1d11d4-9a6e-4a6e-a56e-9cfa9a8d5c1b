<!-- 报表excel导出列表 -->
<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='primary' size='mini' @click='getList' slot='left' :loading='onRefresh' :disabled='onRefresh'>刷新</el-button>
      <el-button type='info' size='mini' @click='autoWinList' slot='left' >重出名单</el-button>
      <!--<el-input size='mini' v-model='test' slot='right' placeholder='任务类型'></el-input>-->
      <!--<el-button type='info' size='mini' slot='right' @click='testClick' :loading='testStatus' :disabled='testStatus'>调用</el-button>-->
    </xpt-headbar>
    <xpt-list
      :data='list'
      :colData='cols'
      :btns='btns'
      :pageTotal='count'
      selection=''
      :showHead='false'
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    >
    <template slot='operate' slot-scope='scope'>
								<el-button size='mini' type='primary'  @click="importWin(scope.row)" >使用名单</el-button>
						</template>
    </xpt-list>
  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this;
      return {
        list: [],
        btns: [{
          type: 'primary',
          txt: '刷新',
          click: self.getList,
          loading: false
        }],
        cols: [
          {
            label: '执行状态',
            prop: 'program_status',
            formatter: self.statusExchage
          }, {
            label: '开始时间',
            prop: 'submit_time',
            format: 'dataFormat1',
            width: '130'
          }, {
            label: '完成时间',
            prop: 'finish_time',
            format: 'dataFormat1',
            width: '130'
          }, {
            label: '中奖人数小计',
            prop: 'date_text',
             html(val) {

              //  console.log(val)
              if(val){
                return JSON.parse(val).auto_win_count;
              }
            }
          },{
            label: '文件路径',
            prop: 'file_path',
            html(val) {
              return val ? '<a href="'+ val +'">下载</a>' : val;
            }
          },{
            label: '操作',
            slot: 'operate',
          }
        ],
        query: {
          list_excel_type: [
            'EXCEL_TYPE_ACT_AUTO_WIN_LIST_REPORT_EXPORT'
          ],
          page_size: self.pageSize,
          page_no: 1
        },
        count: 0,
        statusKeyValue: {
          'EXEC_WAIT':'等待',
          'EXEC_RUNING':'执行中',
          'EXEC_FAILED':'失败',
          'EXEC_SUCCESS':'成功'
        },
        test: '',
        testStatus: false,
        onRefresh: false
      }
    },
    methods: {
      importWin(row){
        console.log(row)
        this.ajax.postStream('/price-web/api/price/win/importWinList', {file_path:row.file_path}, res => {
          this.$message.info(res.body.msg)
        }, err => {
          this.$message.error(err)
        })
      },
      autoWinList(){

         var data ={
           discount_id:this.params.query.table_primary_id
         }
        ,   url = '/price-web/api/actDiscount/autoWinList'
        this.ajax.postStream(url, data, res => {
          if(res.body.result) {
            this.getList();
            this.$message.success(res.body.msg)
          } else {
            this.$message.error(res.body.msg)
          }
          
        }, error => {
          this.$message.error(error.statusText)
        })
      },
      getList() {
        this.btns[0].loading = true;
        this.onRefresh = true;

        var data = JSON.parse(JSON.stringify(this.query))
        ,   url = '/file-iweb/api/cloud/excel/list'

        if(this.params.query){
          delete data.list_excel_type
          Object.assign(data, this.params.query)
          url = '/reports-web/api/reports/afterSale/findMyReportList'
        }


        this.ajax.postStream(url, data, res => {
          if(res.body.result && res.body.content) {
            this.list = res.body.content.list || [];
            this.count = res.body.content.count || 0;
          } else {
            this.$message.error(res.body.msg)
          }
          this.btns[0].loading = false;
          this.onRefresh = false;
        }, error => {
          this.btns[0].loading = false;
          this.onRefresh = false;
          this.$message.error(error.statusText)
        })
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.getList();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.getList();
      },
      statusExchage(val) {
        let keyValues = {
          'EXEC_WAIT':'等待',
          'EXEC_RUNING':'执行中',
          'EXEC_FAILED':'失败',
          'EXEC_SUCCESS':'成功'
        };
        return keyValues[val] || val;
      },
      testClick() {
        this.testStatus = true;
        this.ajax.postStream('/order-web/api/report/job/handle', {job_type: this.test}, res => {
          this.testStatus = false;
          this.$message.info(res.body.msg)
        }, err => {
          this.testStatus = false;
          this.$message.error(err)
        })
      }
    },
    mounted() {
      this.getList();
    }
  }
</script>
