//中奖名单列表
<template>
  <xpt-list2
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :searchPage='search.page_name'
    :orderNo="true"
    :pageTotal='pageTotal'
    @search-click='searchClick'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    @selection-change='handleSelectionChange'
    @count-off="countOff"
		:showCount ='showCount'
  ></xpt-list2>
</template>

<script>

    export default {
      name: "awardDetailList",
      data() {
        let self = this;
        return {
          countOffFlag:false,
			    showCount:false,
          dataList:[],
          pageTotal:0,
          search:{
            page_name:"act_winning_detail",
            where:[],
            page:{
              length:50,
              pageNo:1,
            },
          },
          btns:[
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.getList();
              },
            }, {
              type: "primary",
              txt: "修改实际发货订单",
              loading: false,
              click() {
                self.changeOrderList();
              },
            }, {
              type: "primary",
              txt: "保存",
              loading: false,
              click() {
                self.save();
              },
            }, {
              type: "primary",
              txt: "导出",
              loading: false,
              click() {
                self.export();
              },
            }, {
              type: "primary",
              txt: "导出列表结果",
              loading: false,
              click() {
                self.showExportList();
              },
            }
          ],
          cols:[
            {
              label: "中奖单据编号",
              prop: "win_no",
              width: 160,
              // redirectClick(row) {
              //   let params = {};
              //   params.win_no = row.win_no;
              //   params.main_status = row.main_status;
              //   self.$root.eventHandle.$emit('creatTab', {
              //     name: "中奖名单详情",
              //     params: params,
              //     component: () => import('@components/discount/awardDetail.vue')
              //   });
              // },
            }, {
              label: "中奖名单名称",
              prop: "win_name",
              width: 110,
            }, {
              label: "中奖名单关联活动",
              prop: "discount_name",
              width: 120,
            }, {
              label: "优惠项目编码",
              prop: "discount_item_no",
              width: 150
            },
            {
              label: "优惠项目内容",
              prop: "discount_item_dec",
              width: 150
            },
            {
              label: "实际发货关联订单",
              prop: "winning_delivery_relate_tids",
              width: 170,
							bool: true,
							elInput: true,
							disabled(row) {
								return !(self.selectIdList.includes(row.id) && self.changeStatus && row.main_status === 'AUDIT' && row.status === 'NORMAL')
							}
            },
            {
              label: "天猫公示订单",
              prop: "tid",
              width: 160
            }, {
              label: "中奖关联订单",
              prop: "winning_relate_tids",
              width: 150
            },
            {
              label: "中奖人",
              prop: "winner",
              width: 115,
              redirectClick(row) {
                let params = {};
                params.id = row.id;
                self.$root.eventHandle.$emit('creatTab', {
                  name: "修改实际发货订单详情",
                  params: params,
                  component: () => import('@components/discount/awardWinnerDetail.vue')
                });
              },
            },
            {
              label: "中奖排名",
              prop: "rank",
            },
            {
              label: '活动开始时间',
              prop: 'discount_start',
              format: "dataFormat1",
              width: 130
            },{
              label: '活动结束时间',
              prop: 'discount_end',
              format: "dataFormat1",
              width: 130
            },{
              label: '业务员',
              prop: 'user_name'
            },{
              label: '业务员分组',
              prop: 'group_name'
            },{
              label: "版本号",
              prop: "versions"
            }, {
              label: "状态",
              prop: "status",
              formatter(val) {
                switch (val) {
                  case "NORMAL" : return "正常";
                  case "CANCEL" : return "取消";
                }
              }
            }, {
              label: "变更状态",
              prop: "change_status",
              formatter(val){
                switch (val) {
                  case "WAIT" : return "等待";
                  case "PASS" : return "通过";
                  case "FAIL" : return  "不通过";
                }
              }
            },
            {
              label: "是否修改",
              prop: "if_update",
              formatter(val){
                switch (val) {
                  case "Y" : return "是";
                  case "N" : return "否";
                }
              }
            }/*, {
              label: "中奖明细ID",
              prop: "id"
            }, {
              label: "中奖头ID",
              prop: "winning_main_id",
            }, {
              label: "版本ID",
              prop: "version_id"
            }, {
              label: "中奖头状态",
              prop: "main_status",
            }, {
              label: "旧的实际发货订单",
              prop: "old_winning_delivery_relate_tids"
            }*/
          ],
          selectedData: [],
          selectIdList: [],
          changeStatus: false
        }
      },
      methods: {
        // 导出文件下载  
        showExportList (){
          this.$root.eventHandle.$emit("alert", {
            component: () => import("@components/after_sales_report/export"),
            style: "width:900px;height:600px",
            title: "导出列表",
            params: {
              query: {
                type: 'EXCEL_TYPE_ACT_WINNING_DETAIL_LIST'
              }
            }
          });
				},
        handleSelectionChange(selects){
          this.selectedData=[]
          this.selectIdList=[]
          this.selectedData = selects
          selects.forEach(item => {
            this.selectIdList.push(item.id)
          })
        },
        changeOrderList() {
          if(this.selectedData&&this.selectedData.length==0){
            this.$message.error("请至少勾选一行明细")
            return
          }
          if(this.selectedData && this.selectedData.some(item=>(item.main_status!='AUDIT' && item.status != 'NORMAL')) ){
            this.$message.error("已审核通过的中奖名单明细才可发起修改")
            return
          }
          this.changeStatus = true
        },
        // 保存
        save () {
          let postData = []
          this.selectedData.forEach(item => {
            let obj = {
              id: item.id,
              winning_delivery_relate_tids: item.winning_delivery_relate_tids,
              old_winning_delivery_relate_tids: item.old_winning_delivery_relate_tids,
              discount_item_no: item.discount_item_no,
              tid: item.tid
            }
            if (item.old_winning_delivery_relate_tids != item.winning_delivery_relate_tids) {
              postData.push(obj)
            }
          })
          if (postData && postData.length == 0) { 
            this.$message.error('请更改中奖名单明细中的实际发货关联订单字段！') 
            return
          }
          this.ajax.postStream('/price-web/api/price/win/updateWinningDeliveryRelateTids?permissionCode=UPDATE_DELIVERY_RELATE_TIDS',postData,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg)
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            this.changeStatus = false
            this.getList()
          }, err => {
            this.$message.error(err);
            this.changeStatus = false
            this.getList()
          });
        },
        // 导出
        export () {
          this.ajax.postStream('/price-web/api/price/win/exportActWinningDetailList',this.search,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg)
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        countOff(){
			
          let self = this,
          url = "/price-web/api/price/win/findActWinningDetailListCount";
          if(!self.dataList.length){
            self.$message.error("当前列表为空，先搜索内容");
            return;
          }
          if(!!self.countOffFlag){
            self.$message.error("请勿重复点击");
            return;
          }
          self.countOffFlag = true;

          self.ajax.postStream(url,self.search,function(response){
              if(response.body.result){
                
                self.pageTotal = response.body.content.count;
                self.showCount = true;
                self.countOffFlag = false;
                console.log(self.pageTotal,1);
                
              }else{
                self.$message.error(response.body.msg);
              }
            });
        },
        getList(resolve,callback){
          let self = this;
          this.btns[0].loading = true;
          this.ajax.postStream('/price-web/api/price/win/findActWinningDetailList',this.search,res => {
            if(res.body.result && res.body.content) {
              let list = res.body.content.list;
              list.forEach(item => {
                item.old_winning_delivery_relate_tids = item.winning_delivery_relate_tids
              })
              this.dataList = list;
              if(!self.showCount){
                let total = self.search.page.length* self.search.page.pageNo;
                
                self.$nextTick(()=>{
                  self.pageTotal = res.body.content.list.length == (self.search.page.length+1)? total+1:total;
                  console.log(total,self.pageTotal,1);
                });
                // self.count = list.length == (self.search.page.length+1)? (total)+1:total;
              }
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            callback && callback();
            this.btns[0].loading = false;
            this.changeStatus = false
          }, err => {
            
            callback && callback();
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
            this.changeStatus = false
          });
        },
        searchClick (list, resolve){
          this.search.where = list;
          let self = this;
          // this.getList(resolve);
          new Promise((res,rej)=>{
				  this.getList(resolve,res);
            }).then(()=>{
              if(self.search.page.pageNo != 1){
                self.pageTotal = 0;
              }
              self.showCount = false;
            })
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page.length = pageSize;
          this.getList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.search.page.pageNo = page;
          this.getList();
        },
      },
      mounted: function() {
        // this.getList();
        // this.$root.eventHandle.$on("refresh_invoice", d => {
        //   this.getList();
        // });
      }
    }
</script>

<style scoped>

</style>
