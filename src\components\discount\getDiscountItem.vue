<template>
<div class='xpt-flex' style="overflow: auto;">
	<xpt-headbar>
    <el-button type='primary' slot='left' size='mini' @click="save" :loading="saveLoading">保存</el-button>
		      <el-button type='danger' slot='left' size='mini' @click="preclose">取消</el-button>
		      <el-button type='warning' slot='left' size='mini' @click="reset">重置</el-button>
	</xpt-headbar>
  <el-form label-width='100px' :model='data'  ref="data">
		<el-row :gutter='40'>
			<el-col :span='24'  class="xpt_col">
        <div>
				<el-checkbox v-model="selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT" :disabled='selectDisabled.ITEM_TYPE_DEDUCTE_AMOUNT' @change="getDiscountConfigureInfo('ITEM_TYPE_DEDUCTE_AMOUNT',selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT)"> 减
					<el-input v-model="data.subtract_money.value" size="mini"  class="repo-input" type="text" :min="1" :disabled="!selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT" @change='e=>{
						formatSubtract()
						}'></el-input><span style="margin-right:20px;">元</span>
						封顶金额
						<el-input v-model="data.subtract_money.max_amount" size="mini" style="width:100px;"  class="repo-input" type="number" :min="1" :disabled="(!selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT)||(params.form.if_each_full=='Y')" @change='e=>{
						data.subtract_money.max_amount = fitterPrice5(data.subtract_money.max_amount)=="N" ?"": fitterPrice5(data.subtract_money.max_amount);
						}'></el-input><span>元</span>
				</el-checkbox>
				<el-checkbox v-model="selectSuperposition.ITEM_TYPE_DEDUCTE_AMOUNT" :disabled='selectSuperpositionDisabled("ITEM_TYPE_DEDUCTE_AMOUNT")'>
					<span>是否允许叠加</span>
				</el-checkbox>
        <span>
          服务物料编码
          <el-input  v-model="data.subtract_money.service_project_no" size="mini" style="width:100px;"  class="repo-input"  :disabled="!selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT"></el-input>
        </span>
			</div>
      </el-col>

    </el-row>
    <el-row :gutter='40'>
			<el-col :span='24'  class="xpt_col">
				<div>
					<el-checkbox v-model="selectStatus.ITEM_TYPE_DISCOUNT" :disabled='selectDisabled.ITEM_TYPE_DISCOUNT' @change="getDiscountConfigureInfo('ITEM_TYPE_DISCOUNT',selectStatus.ITEM_TYPE_DISCOUNT)"> 折扣
						<el-input v-model="data.discount.value" size="mini"  class="repo-input" type="number" :min="1" :max="100" :disabled="!selectStatus.ITEM_TYPE_DISCOUNT" @change.native='e=>{
						data.discount.value = fitterPrice3(data.discount.value)
						}'></el-input><span style="margin-right:20px;">%</span>
						封顶金额
						<el-input v-model="data.discount.max_amount" size="mini"  class="repo-input"  style="width:100px;" type="number" :min="1" :disabled="(!selectStatus.ITEM_TYPE_DISCOUNT)||(params.form.if_each_full=='Y')" @change='discountAmountChange'></el-input><span>元</span>
					</el-checkbox>
					<el-checkbox v-model="selectSuperposition.ITEM_TYPE_DISCOUNT" :disabled='selectSuperpositionDisabled("ITEM_TYPE_DISCOUNT")'>
						<span>是否允许叠加</span>
					</el-checkbox>
				</div>
      </el-col>
	</el-row>

    <el-row :gutter='40'>
		<el-col :span='40'  class="xpt_col">
			<div>
				<el-checkbox v-model="selectStatus.ITEM_TYPE_GIFTS" :disabled='selectDisabled.ITEM_TYPE_GIFTS' @change="getDiscountConfigureInfo('ITEM_TYPE_GIFTS',selectStatus.ITEM_TYPE_GIFTS)"> 赠品</el-checkbox>
				<el-checkbox v-model="selectSuperposition.ITEM_TYPE_GIFTS" :disabled='selectSuperpositionDisabled("ITEM_TYPE_GIFTS")'>
						<span>是否允许叠加</span>
					</el-checkbox>
			</div>
      	</el-col>
		<el-col :span='40'  class="xpt_col">
			<div>
				<el-checkbox v-model="selectStatus.ITEM_TYPE_UPGRADE" :disabled='selectDisabled.ITEM_TYPE_UPGRADE' @change="getDiscountConfigureInfo('ITEM_TYPE_UPGRADE',selectStatus.ITEM_TYPE_UPGRADE)"> 升级换购</el-checkbox>
				<el-checkbox v-model="selectSuperposition.ITEM_TYPE_UPGRADE" :disabled='selectSuperpositionDisabled("ITEM_TYPE_UPGRADE")'>
						<span>是否允许叠加</span>
					</el-checkbox>
			</div>
      	</el-col>
    </el-row>


    <el-row class="mgt30" v-show="selectStatus.ITEM_TYPE_GIFTS">
			<el-tabs v-model="firstTab">
					<el-tab-pane label='设置赠品' name='ITEM_TYPE_GIFTS' class='xpt-flex xpt-flex__bottom' style="overflow:hidden;">
						<xpt-list
							:data='data.giftsData'
							:btns='giftsbtns'
							:colData='giftsCols'
							:orderNo = true
							selection='radio'
							@radio-change='giftsRadioChange'
						>
							<template slot='if_present' slot-scope='scope'>
									<el-switch v-model="scope.row.if_present" on-text="是" off-text="否" on-value="Y" off-value="N" @change="e=>{
										if(scope.row.if_present == 'Y'){
											scope.row.material_number = '';
											scope.row.material_id = '';
											scope.row.material_name = '';
											scope.row.material_specification = '';
											scope.row.material_unit = '';
											return false;
										};
										getMaterialByNumber(scope.row,'giftsData',d=>{
												if(!d){
													scope.row.material_number = '';
												}else{
													let passObj = selectSameMaterial(d.materialNumber);

													if(!passObj.isPass){
														scope.row.material_number = '';
														$message.error(passObj.msg)
													}else{
														scope.row.material_number = d.materialNumber;
														scope.row.material_id = d.materialId;
														scope.row.material_name = d.materialName;
														scope.row.material_specification = d.materialSpecification;
														scope.row.material_unit = d.materialUnit;
													}
												}

										});

										}"></el-switch>
							</template>
							<!-- <template slot='if_convert_into_cash' slot-scope='scope'>
									<el-switch v-model="scope.row.if_convert_into_cash" on-text="是" :disabled="scope.row.if_present == 'N' " off-text="否" on-value="Y" off-value="N" @change="e=>{
										scope.row.discount_amount = '';


										}"></el-switch>
							</template> -->
							<template slot='if_enjoy_discount' slot-scope='scope'>
									<el-switch v-model="scope.row.if_enjoy_discount" on-text="是" off-text="否" on-value="Y" off-value="N" disabled></el-switch>
							</template>
						</xpt-list>
					</el-tab-pane>
			</el-tabs>
		</el-row>
		<el-row class="mgt30 xpt-flex__bottom" v-show="selectStatus.ITEM_TYPE_UPGRADE">
			<el-tabs v-model="secondTab">
					<el-tab-pane label='升级换购' name='ITEM_TYPE_UPGRADE' class='xpt-flex' style="overflow:hidden;">
						<xpt-list
							:data='data.updateData'
							:btns='updatebtns'
							:colData='updateCols'
							:orderNo = true
							selection='radio'
							@radio-change='updateRadioChange'
						>
						<template slot='if_enjoy_discount' slot-scope='scope'>
							<el-switch v-model="scope.row.if_enjoy_discount" on-text="是" off-text="否" on-value="Y" off-value="N" :disabled="params.form.discount_sub_type == 'UPGRADE'"></el-switch>
						</template>

						</xpt-list>
					</el-tab-pane>
			</el-tabs>
		</el-row>
		<el-row class="mgt30 xpt-flex__bottom" v-show="selectStatus.ITEM_TYPE_GOODS_PROMOTION">
			<el-tabs v-model="thirdTab">
					<el-tab-pane label='单品优惠' name='ITEM_TYPE_GOODS_PROMOTION' class='xpt-flex' style="overflow:hidden;">
						<xpt-list
							:data='data.singleData'
							:btns='singlebtns'
							:colData='singleCols'
							:orderNo = true
							selection='radio'
							@radio-change='singleRadioChange'
						>
							<template slot='if_enjoy_discount' slot-scope='scope'>
								<el-switch v-model="scope.row.if_enjoy_discount" on-text="是" off-text="否" on-value="Y" off-value="N" ></el-switch>
							</template>
						</xpt-list>
					</el-tab-pane>
			</el-tabs>
		</el-row>
  </el-form>
</div>
</template>
<script>
import Fn from '@common/Fn.js'
  export default {
    data() {
      let self = this;
      return {
				firstTab:"ITEM_TYPE_GIFTS",
				secondTab:'ITEM_TYPE_UPGRADE',
				thirdTab:'ITEM_TYPE_GOODS_PROMOTION',
				max_money:self.params.row.threshold_price ? self.params.row.threshold_price : Math.pow(2,53),
				saveLoading:false,
				giftsbtns:[
				 {
						type: 'info',
						txt: '新增',
						click: self.addGift
					},{
						type: 'danger',
						txt: '删除',
						click: self.delGift
					},
				],
				giftsCols:[
					{
						label:'物料编码',
						prop:'material_number',
						bool:true,
						blur(row){
							self.saveLoading = true;
							var han = new RegExp("[\\u4E00-\\u9FFF]+","g");
							if(row.if_present == 'Y') {
								if(han.test(row.material_number)){
									row.material_number = '';
									self.$message.error('所添加物料编码不能输入中文');
								}
								setTimeout(()=>{
									self.saveLoading = false;
								},300)
								return false;
							}else{
								self.saveLoading = false;
							}

							new Promise((resolve,reject)=>{
								self.getMaterialByNumber(row,'giftsData',resolve);
							}).then((d)=>{
								if(!d){
									row.material_number = '';
								}else{
									let passObj = self.selectSameMaterial(d.materialNumber);
									if(!passObj.isPass){
										row.material_number = '';
										self.$message.error(passObj.msg)
									}else{
										row.material_number = d.materialNumber;
										row.material_id = d.materialId;
										row.material_name = d.materialName;
										row.material_specification = d.materialSpecification;
										row.material_unit = d.materialUnit;
										row.type = 'gift'
									}
								}
							})
						},
						width:150,
						// elInput:true,
						isClickEvent:true,
						iconClick(row) {
							new Promise((resolve,reject)=>{
								setTimeout(self.goodsChoose('giftsData',row,resolve), 10)
							}).then((d)=>{
								row.material_number = d.materialNumber;
								let passObj = self.selectSameMaterial(d.materialNumber);

								if(!passObj.isPass){
									row.material_number = '';
									self.$message.error(passObj.msg)
								}else{
									row.material_number = d.materialNumber;
									row.material_id = d.materialId;
									row.material_name = d.materialName;
									row.material_specification = d.materialSpecification;
									row.material_unit = d.materialUnit;
									row.type = 'gift'
								}
							})
						},
						disabled(row) {
							return false;
						},
					},{
						label:'礼品',
						slot:'if_present',
					},
					// {
					// 	label:'折现',
					// 	slot:'if_convert_into_cash',
					// },
					// {
					// 	label:'优惠金额',
					// 	prop:'discount_amount',
					// 	bool: true,
					// 	elInput:true,
					// 	disabled(row) {
					// 		return false;
					// 		// return row.if_convert_into_cash == 'N';
					// 	},
					// 	width:150,
					// 	change(row){
					// 		const result = self.fitterPrice2(row.discount_amount) || '';
					// 		self.$nextTick(() =>{
					// 			self.$set(row, 'discount_amount', result);
					// 		});
					// 	},
					// },
					{
						label:'成本',
						prop:'cost_price',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},

						change(row){
							const result = self.fitterPrice2(row.cost_price) || '';
							self.$nextTick(() =>{
								self.$set(row, 'cost_price', result);
							});
						},
					},{
						label:'名义售价',
						prop:'nominal_price',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},

						change(row){
							const result = self.fitterPrice2(row.nominal_price) || '';
							self.$nextTick(() =>{
								self.$set(row, 'nominal_price', result);
							});
						},
					},{
						label:'三包减免',
						prop:'three_reduction',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
						change(row){
							const result = self.fitterPrice4(row.three_reduction) || '';
							self.$nextTick(() =>{
								self.$set(row, 'three_reduction', result);
							});
						},
					},{
						label:'物流减免',
						prop:'logistics_reduction',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
						change(row){
							const result = self.fitterPrice4(row.logistics_reduction) || '';
							self.$nextTick(() =>{
								self.$set(row, 'logistics_reduction', result);
							});
						},
					},{
						label:'限量',
						prop:'count_limit',
						bool: true,
						elInput:true,
						change(row){
							const result = self.fitterPrice(row.count_limit) || '';
							self.$nextTick(() =>{
								self.$set(row, 'count_limit', result);
							});
						},
						disabled(row) {
							return false;
						},
					},{
						label:'个人承担比例',
						prop:'person_bear_ratio',
						bool: false,
						elInput:true,
						disabled(row) {
							return false;
						},
					},{
						label:'是否享受优惠',
						slot:'if_enjoy_discount',
					},{
						label:'备注',
						prop:'remark',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
					},
				],
				SelectObj:{
					giftsData:null,
					updateData:null,
					singleData:null
				},
				updateCols:[
					{
						label:'物料编码',
						prop:'material_number',
						bool: true,
						width:150,
						// elInput:true,
						blur(row){
							self.saveLoading = true;
							if(row.if_present == 'Y') return false;
							setTimeout(()=>{
								self.saveLoading = false;
							},300)

							new Promise((resolve,reject)=>{
								self.getMaterialByNumber(row,'updateData',resolve);
							}).then((d)=>{
								if(!d){
									row.material_number = '';
								}else{
									let passObj = self.selectSameMaterial(d.materialNumber);

									if(!passObj.isPass){
										row.material_number = '';
										self.$message.error(passObj.msg)
									}else{
										row.material_number = d.materialNumber;
										row.material_id = d.materialId;
										row.material_name = d.materialName;
										row.material_specification = d.materialSpecification;
										row.material_unit = d.materialUnit;
									}
								}
							})
						},
						isClickEvent:true,
						iconClick(row) {
							new Promise((resolve,reject)=>{
								setTimeout(self.goodsChoose('updateData',row,resolve), 10)
							}).then((d)=>{
								row.material_number = d.materialNumber;
								let passObj = self.selectSameMaterial(d.materialNumber);

								if(!passObj.isPass){
								row.material_number = '';
								self.$message.error(passObj.msg)
								}else{
									row.material_number = d.materialNumber;
									row.material_id = d.materialId;
									row.material_name = d.materialName;
									row.material_specification = d.materialSpecification;
									row.material_unit = d.materialUnit;
								}
							})
						},
						disabled(row) {
							return false;
						},
					},{
						label:'价格',
						prop:'act_price',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
						change(row){
							const result = self.fitterPrice2(row.act_price) || '';
							self.$nextTick(() =>{
								self.$set(row, 'act_price', result);
							});
						},
					},{
						label:'成本',
						prop:'cost_price',
						bool: false,
						elInput:true,
						disabled(row) {
							return false;
						},
						change(row){
							const result = self.fitterPrice2(row.cost_price) || '';
							self.$nextTick(() =>{
								self.$set(row, 'cost_price', result);
							});
						},
					},{
						label:'三包减免',
						prop:'three_reduction',
						bool: false,
						elInput:true,
						disabled(row) {
							return false;
						},
					},{
						label:'物流减免',
						prop:'logistics_reduction',
						bool: false,
						elInput:true,
						disabled(row) {
							return false;
						},
					},{
						label:'限量',
						prop:'count_limit',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
						change(row){
							const result = self.fitterPrice(row.count_limit) || '';
							self.$nextTick(() =>{
								self.$set(row, 'count_limit', result);
							});
						},

					},{
						label:'经销结算价',
						prop:'dealer_price',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
						change(row){
							const result = self.fitterPrice2(row.dealer_price) || '';
							self.$nextTick(() =>{
								self.$set(row, 'dealer_price', result);
							});
						},

					},{
						label:'个人承担比例',
						prop:'person_bear_ratio',
						bool: false,
						elInput:true,
						disabled(row) {
							return false;
						},
					},{
						label:'是否享受优惠',
						slot:'if_enjoy_discount',
						// bool: true,
						// elInput:true,
						// disabled(row) {
						// 	return false;
						// },
					},{
						label:'备注',
						prop:'remark',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
					},
				],
				updatebtns:[
					{
						type: 'info',
						txt: '新增',
						click: self.addUpdate
					},{
						type: 'danger',
						txt: '删除',
						click: self.delUpdate
					},
				],
				updateSelect:null,
				singleData:[],
				singleCols:[
					{
						label:'物料编码',
						prop:'material_number',
						bool: true,
						blur(row){
							self.saveLoading = true;
							if(row.if_present == 'Y') return false;
							setTimeout(()=>{
								self.saveLoading = false;
							},300)
							new Promise((resolve,reject)=>{
								self.getMaterialByNumber(row,'singleData',resolve);
							}).then((d)=>{
								if(!d){
									row.material_number = '';
								}else{
									let passObj = self.selectSameMaterial(d.materialNumber);

									if(!passObj.isPass){
										row.material_number = '';
										self.$message.error(passObj.msg)
									}else{
										row.material_number = d.materialNumber;
										row.material_id = d.materialId;
										row.material_name = d.materialName;
										row.material_specification = d.materialSpecification;
										row.material_unit = d.materialUnit;
									}
								}
							})
						},
						width:150,
						isClickEvent:true,
						iconClick(row) {
							new Promise((resolve,reject)=>{
								setTimeout(self.goodsChoose('singleData',row,resolve), 10)
							}).then((d)=>{
								row.material_number = d.materialNumber;
								let passObj = self.selectSameMaterial(d.materialNumber);

								if(!passObj.isPass){
									row.material_number = '';
									self.$message.error(passObj.msg)
								}else{
									row.material_number = d.materialNumber;
									row.material_id = d.materialId;
									row.material_name = d.materialName;
									row.material_specification = d.materialSpecification;
									row.material_unit = d.materialUnit;
								}
							})
						},
						disabled(row) {
							return false;
						},
					},
					{
						label:'优惠金额',
						prop:'discount_amount',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
						width:150,
						change(row){
							const result = self.fitterPrice2(row.discount_amount) || '';
							self.$nextTick(() =>{
								self.$set(row, 'discount_amount', result);
							});
						},
					},
					{
						label:'备注',
						prop:'remark',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
					},
				],
				singleSelect:null,
				ifCut:false,
				ifDiscunt:false,
				singlebtns:[{
						type: 'info',
						txt: '新增',
						click: self.addsingle
					},{
						type: 'danger',
						txt: '删除',
						click: self.delsingle
					},],
				oldGoodsList: [],
				selectDisabled:{
					ITEM_TYPE_DEDUCTE_AMOUNT:true,
					ITEM_TYPE_DISCOUNT:true,
					ITEM_TYPE_GIFTS:true,
					ITEM_TYPE_UPGRADE:true,
					ITEM_TYPE_GOODS_PROMOTION:true,
				},
				selectStatus:{
					ITEM_TYPE_DEDUCTE_AMOUNT:false,
					ITEM_TYPE_DISCOUNT:false,
					ITEM_TYPE_GIFTS:false,
					ITEM_TYPE_UPGRADE:false,
					ITEM_TYPE_GOODS_PROMOTION:false,
				},
				selectSuperposition:{
					ITEM_TYPE_DEDUCTE_AMOUNT:false,
					ITEM_TYPE_DISCOUNT:false,
					ITEM_TYPE_GIFTS:false,
					ITEM_TYPE_UPGRADE:false,
					ITEM_TYPE_GOODS_PROMOTION:false,
				},
				item_type_detail:{
					ITEM_TYPE_DEDUCTE_AMOUNT:'',
					ITEM_TYPE_DISCOUNT:'',
					ITEM_TYPE_GIFTS:'',
					ITEM_TYPE_UPGRADE:'',
					ITEM_TYPE_GOODS_PROMOTION:'',
				},
        		data:{
					discount:{
						value:'',
						max_amount:'',
					},
					subtract_money:{
						value:'',
						max_amount:'',
						service_project_no:'',
					},
					giftsData:[],
					updateData:[],
					singleData:[],
				},
				materialList:[],
        ifServiceProjectNo:false,
			}
		},
	  	props:['params'],
    	methods: {
        judgeIfServiceProjectNo() {
          const data=Fn.getAuxType("SERVICE_DISCOUNT_SUBTYPE");
          const codeKeys=Object.keys(data);
          console.log(codeKeys);
          if(codeKeys.includes(this.params.form.discount_sub_type)){
            this.ifServiceProjectNo=true;
          }else{
            this.ifServiceProjectNo=false;
          }
        },
			selectSuperpositionDisabled(type){
				let self = this;
				return !self.selectStatus[type];
			},
			getAllDiscountConfigureInfo(){
				let self = this;
				this.ajax.postStream('/price-web/api/actDiscountConfigure/getDiscountConfigureInfo',
					{
						discount_sub_type_code:this.params.form.discount_sub_type,
						item_type_code:this.params.form.item_type,
					},
					res => {
						if(res.body.result && res.body.content){
							let configureInfo = new Set();
							res.body.content.find(item =>{
								configureInfo.add(item.discount_item_type_code)
								// if(item.discount_item_type_code)
							})
							for(var i in self.selectStatus){
								if(configureInfo.has(i)){
									self.selectDisabled[i] = false;
									res.body.content.find(item =>{
										// configureInfo.add(item.discount_item_type_code)
										if(item.discount_item_type_code == i){
											self.item_type_detail[i] = item.item_type_detail_code;
										}

									})
								}
								// self.item_type_detail[i] = i;
								self.params.listActDiscountItemVo.forEach(item => {
								if(item.discount_condition_id == self.params.row.discount_condition_id && item.discount_item_type == i&&(i == 'ITEM_TYPE_DISCOUNT'||i == 'ITEM_TYPE_DEDUCTE_AMOUNT')){
									self.selectStatus[i] = false;
									// return false;
									}
								});
							}
							if (this.params.form.if_each_full === 'Y') {
								this.selectDisabled.ITEM_TYPE_DISCOUNT = true
							}


						}else{
							this.$message.error(res.body.msg);
						}
					}, () => {
					})
			},
			getDiscountConfigureInfo(val,ifshow){
				if(val == 'ITEM_TYPE_DEDUCTE_AMOUNT'){
					this.data.subtract_money.value = '';
					this.data.subtract_money.max_amount = '';
					this.data.subtract_money.service_project_no = '';
					// this.selectSuperposition.ITEM_TYPE_DEDUCTE_AMOUNT = ifshow;
				}
				if(val == 'ITEM_TYPE_DISCOUNT'){
					this.data.discount.value = '';
					this.data.discount.max_amount = '';
					// this.selectSuperposition.ITEM_TYPE_DISCOUNT = ifshow;
				}
				this.selectSuperposition[val] = ifshow;
				let self = this;
				// self.params.listActDiscountItemVo.forEach(item => {
				// 	if(item.discount_condition_id == self.params.row.discount_condition_id && item.discount_item_type == val&&(val == 'ITEM_TYPE_DISCOUNT'||val == 'ITEM_TYPE_DEDUCTE_AMOUNT')){
				// 		self.$message.error('该类型已被填充');
				// 		self.selectStatus[val] = false;
				// 		return false;
				// 	}
				// });
				return false;

				// if(!ifshow) return;
				// 	this.ajax.postStream('/price-web/api/actDiscountConfigure/getDiscountConfigureInfo',
				// 	{
				// 		discount_sub_type_code:this.params.form.discount_sub_type,
				// 		item_type_code:this.params.form.item_type,
				// 		discount_item_type_code:val
				// 	},
				// 	res => {
				// 		if(res.body.result){
				// 			// console.log(res.body)
				// 			if(res.body.content.length == 0){
				// 				self.$message.error('该类型不能被选择');
				// 				self.selectStatus[val] = false;
				// 				return false;
				// 			}else{
				// 				// console.log(res.body.content[0].item_type_detail_code);
				// 				self.item_type_detail[val] = res.body.content[0].item_type_detail_code;
				// 			}
				// 		}else{
				// 			this.$message.error(res.body.msg);
				// 			self.selectStatus[val] = false;
				// 		}
				// 	}, () => {
				// 	})
			},
			preclose(){
				let self = this;
				this.$confirm('当前操作会导致优惠项目无法保存，是否继续？','提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'danger'
			  }).then(()=>{
				  self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
			  })
			},
			close(){
				let self = this;
				self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
			},
			reset(){
				this.data = {
					discount:'',
					subtract_money:'',
					giftsData:[],
					updateData:[],
					singleData:[],
				}
				for(var i in this.selectStatus){
					this.selectStatus[i] = false;
				}
			},
			giftsRadioChange(row){
				this.SelectObj.giftsData = row;
			},
			updateRadioChange(row){
				this.SelectObj.updateData = row;
			},
			singleRadioChange(row){
				this.SelectObj.singleData = row;
			},

			save(){
				// if((!!this.data.subtract_money.max_amount && !!this.selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT && this.params.form.if_each_full=='Y') || (!!this.data.discount.max_amount && !!this.selectStatus.ITEM_TYPE_DISCOUNT&& this.params.form.if_each_full=='Y')){
				// 	this.$message.error('优惠活动时，不能添加封顶金额');
				// 	return false;
				// }
				if((this.data.subtract_money.value.toString() == '' && !!this.selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT ) || (!this.data.discount.value && !!this.selectStatus.ITEM_TYPE_DISCOUNT)){
					this.$message.error('已选择减元或者打折不能为空');
					return false;
				}
				// console.log(this.data.subtract_money.max_amount.toString());
				if((this.data.subtract_money.max_amount.toString() === '0' && !!this.selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT&& this.params.form.if_each_full!='Y' ) || (this.data.discount.max_amount.toString()==='0'&& !!this.selectStatus.ITEM_TYPE_DISCOUNT&& this.params.form.if_each_full!='Y')){
					this.$message.error('已选择减元或者打折封顶金额不能为0');
					return false;
				}
				if(this.params.form.if_auto_add === 'Y' && (this.selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT && this.selectStatus.ITEM_TYPE_DISCOUNT)) {
					this.$message.error('自动添加的优惠活动一个优惠条件只能创建一项优惠项目');
					return false;
				}
        if(!this.data.subtract_money.service_project_no&&this.ifServiceProjectNo){
          this.$message.error('设置优惠项目时，需要填入服务物料编码');
          return
        }
				let isPass = true;
				let errorMsg = ''
				this.data.giftsData.forEach(item =>{
					if(!(item.material_number&& !!this.selectStatus.ITEM_TYPE_GIFTS)){
						errorMsg = '添加赠品存在物料编码为空';
						isPass = false;
					}
					item.discount_amount = parseFloat(item.discount_amount)

					// if(!item.discount_amount && item.if_convert_into_cash =='Y'){
					// 	errorMsg = '添加折现赠品存在优惠金额为0或为空';
					// 	isPass = false;
					// }
				});

				this.data.updateData.forEach(item =>{
					if(!(item.material_number&& !!this.selectStatus.ITEM_TYPE_UPGRADE)){
						// this.$message.error('添加升级换购存在物料编码为空');
						errorMsg = '添加升级换购存在物料编码为空';
						isPass = false;
					}
					if(!item.act_price){
						errorMsg = '添加升级换购存在价格为空';
						isPass = false;
					}

				});
				this.data.singleData.forEach(item =>{
					if(!(item.material_number&& !!this.selectStatus.ITEM_TYPE_GOODS_PROMOTION)){
						// this.$message.error('添加单品优惠存在物料编码为空');
						errorMsg = '添加单品优惠存在物料编码为空';
						isPass = false;
					}
					if(!item.discount_amount){
						errorMsg = '添加单品优惠存在优惠金额为空';
						isPass = false;
					}

				});

				if(this.data.subtract_money.value.toString() == ''){
					this.selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT = false;
				}
				if(!this.data.discount.value){
					this.selectStatus.ITEM_TYPE_DISCOUNT = false;
				}

				if(!isPass){
					this.$message.error(errorMsg);
					return false;
				}
				this.params.callback({selectSuperposition:this.selectSuperposition,selectStatus:this.selectStatus,item_type_detail:this.item_type_detail,data:this.data,discount_condition_id:this.params.row.discount_condition_id});
				this.close();
			},
			changeSelect(option,item){
			},
			addGift(){
				let data = {
						material_number:'',
						if_present:'N',
						cost_price:0,
						act_price:0,
						three_reduction:100,
						logistics_reduction:100,
						count_limit:0,
						dealer_price:'',
						person_bear_ratio:0,
						if_enjoy_discount:'N',
						remark:'',
						nominal_price:'',
						// if_convert_into_cash:'N',
						discount_amount:'',
						creat_time:new Date(),
				};
				if(this.params.form.discount_sub_type == 'OWN_46_GIFTS_PERSONAL'){
					data.person_bear_ratio = 0.4;
				}
				if(/^(OUTSOURCE_FULL_PAY_GIFTS_PERSONAL|OWN_FULL_PAY__GIFTS_PERSONAL)$/.test(this.params.form.discount_sub_type)){
					data.person_bear_ratio = 1;
				}
				this.data.giftsData.push(data);
			},
			addUpdate(){
				let data = {
						material_number:'',
						if_present:'N',
						cost_price:0,
						act_price:0,
						three_reduction:0,
						logistics_reduction:0,
						count_limit:0,
						dealer_price:0,
						person_bear_ratio:0,
						if_enjoy_discount:'N',
						remark:'',
						creat_time:new Date()
				};
				this.data.updateData.push(data);
			},
			addsingle(){
				let data = {
						material_number:'',
						if_present:'N',
						cost_price:0,
						act_price:0,
						three_reduction:0,
						logistics_reduction:0,
						count_limit:0,
						dealer_price:'',
						person_bear_ratio:0,
						if_enjoy_discount:'N',
						discount_amount:0,
						remark:'',
						creat_time:new Date()
				};
				this.data.singleData.push(data);
			},
			checkAdd(material){
				// 检查是否可以被添加
				let self = this,
				isPass = true;
				let material_list = this.data.giftsData.concat(this.data.updateData).concat(this.data.singleData)
				material_list.forEach(item=>{
					if(item.material_number == material.material_number){
						isPass == false;
					}
				});
				return isPass
			},
			delGift(){
				if(!this.SelectObj.giftsData) return false;
				if(this.SelectObj.giftsData){
					this.data.giftsData.splice(this.data.giftsData.indexOf(this.SelectObj.giftsData),1)
					this.SelectObj.giftsData = null;
				}
			},
			delsingle(){
				if(!this.SelectObj.singleData) return false;
				if(this.SelectObj.singleData){
					this.data.singleData.splice(this.data.singleData.indexOf(this.SelectObj.singleData),1)
					this.SelectObj.singleData = null;
				}
			},
			delUpdate(){
				if(!this.SelectObj.updateData) return false;
				if(this.SelectObj.updateData){
					this.data.updateData.splice(this.data.updateData.indexOf(this.SelectObj.updateData),1)
					this.SelectObj.updateData = null;
				}
			},
			getMaterialByNumber(row,data,resolve){
				let self = this;
				if(!row.material_number){
					return false;
				}
				if(row.material_number.indexOf('/') != -1){
					self.$message.error('不能输入特殊字符“/”');
					resolve('');
					return false;
				}
				let url = '/material-web/api/material/getMaterialByNumber/'+row.material_number;
				self.saveLoading = true;
				this.ajax.get(url,
					res => {
						self.saveLoading = false;
						if(res.body.result){
							if(!res.body.content){
								self.$message.error('根据物料编码查询物料信息失败。')
								// self.SelectObj[data].material_number = '';
								resolve('')
							}else{
								resolve(res.body.content)
							}
						}else{
							this.$message.error(res.body.msg);
						}
					}, () => {
					})
			},
			// 校验是否整数
			fitterPrice(num){
				var reg = /^(0|[0-9][0-9]{0,6})$/;
				var han = new RegExp("[\\u4E00-\\u9FFF]+","g");
				if(han.test(num)){
					this.$message.error('当前输入不符合规范，不能输入中文')
					return '';
				}
				if(!reg.test(num)){
					this.$message.error('当前输入不符合规范，请输入1-7位正整数')
					return '';
				}else{
					return num;
				}
			},
			fitterPrice2(num){
				let reg = /^(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/

				// reg = /^(([1-9][0-9]{0,6})|([1-9][0-9]+\.\d{1,2}))$/;
				let returnData = !!reg.test(num)?num:'';
				if(!reg.test(num)){
					this.$message.error('当前输入不符合规范，请输入1-7位小数点后两位数字')
				}else{
					// return num;
				}
				return returnData;

			},

			fitterPrice3(num){
				let reg = /^(([1-9][0-9]{0,1})|([1-9][0-9]{0,1}\.\d{0,2})|(0\.\d{0,2}))$/

				// reg = /^(([1-9][0-9]{0,6})|([1-9][0-9]+\.\d{1,2}))$/;
				if(!reg.test(num)){
					this.$message.error('当前输入不符合规范，请输入1-100位小数点后两位小数')
					return '';
				}else{
					return num;
				}
			},
			fitterPrice4(num){
				let reg = /^(([1-9][0-9]{0,1})|100)$/

				// reg = /^(([1-9][0-9]{0,6})|([1-9][0-9]+\.\d{1,2}))$/;
				if(!reg.test(num)){
					this.$message.error('当前输入不符合规范，请输入1-100位整数')
					return '';
				}else{
					return num;
				}
			},
			fitterPrice5(num){
				let reg = /^(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/

				// reg = /^(([1-9][0-9]{0,6})|([1-9][0-9]+\.\d{1,2}))$/;
				let returnData = !!reg.test(num)?num:'N';
				if(!reg.test(num)){
					this.$message.error('当前输入不符合规范，请输入1-7位小数点后两位数字')
				}else{
					// return num;
				}
				// console.log(returnData);
				return returnData;

			},

			discountAmountChange(){
				let self = this;
				let reg = /^(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/;
				if(!reg.test(this.data.discount.max_amount)){
					this.$message.error('当前输入不符合规范，请输入1-7位小数点后两位数字');
					// this.data.discount.max_amount = '';
					self.$set(this.data.discount, 'max_amount', '');

				}else{
					// return num;
				}

			},
			goodsChoose(data,row,callback) {
				if(row.if_present == 'Y'){
					return false;
				}
				new Promise((resolve) => {
					setTimeout(resolve, 10)

					}).then(() => {
						let params = {
								// business_type_trade: this.mergeInfo.business_type_trade,
								// shop_id: this.goodsSelect.shop_id
							},
							self = this;

							params.callback = d => {

								if(d) {
									callback && callback(d)
									// let isPasss = self.checkAdd(d);

									// // console.log(d);
									// self.SelectObj[data].material_number = d.materialNumber;
									// self.SelectObj[data].material_id = d.materialId;
									// self.SelectObj[data].material_name = d.materialName;
									// self.SelectObj[data].material_specification = d.materialSpecification;
									// self.SelectObj[data].material_unit = d.materialUnit;
								}
							}
							self.$root.eventHandle.$emit('alert', {
								params: params,
								component: () => import('@components/order/selectGoodsList'),
								style: 'width:800px;height:500px',
								title: '商品列表'
							});

					})
			},
			// 获取要筛选的相同物料编码
			getSelectMaterial(){
				// let materialList = [];
				let self = this;
				self.materialList = [];
				let listActDiscountItemVo = JSON.parse(JSON.stringify(self.params.listActDiscountItemVo))
				listActDiscountItemVo.forEach(item=>{
					if(item.discount_condition_id == self.params.row.discount_condition_id && !!item.material_number){
						self.materialList.push(item);
					}
				});
				self.data.giftsData.forEach(item=>{
					self.materialList.push(JSON.parse(JSON.stringify(item)));
				});
				self.data.singleData.forEach(item=>{
					self.materialList.push(JSON.parse(JSON.stringify(item)));
				});
				self.data.updateData.forEach(item=>{
					self.materialList.push(JSON.parse(JSON.stringify(item)));
				});
			},
			// 获取项目列表、赠品、换购、单品优惠不能有重复物料，如有重复删除
			selectSameMaterial(materialNumber){
				// if(!materialNumber) return;
				let self = this;
				let passObj = {
					isPass:true,
					msg:''
				}
				self.getSelectMaterial();
				passObj = self.unique(self.materialList);
				return passObj;
			},
			unique(arr) {
				let passObj = {
							isPass:true,
							msg:''
						}
				for(var i = 0;i<arr.length;i++){
					if (arr[i].type == 'gift' || arr[i].if_gift == 'Y' || (arr[i].discount_item_id && arr[i].discount_item_type == 'ITEM_TYPE_GIFTS' )) {
						continue
					}
					for(var j=i+1;j<arr.length;j++){
						if(arr[i].material_number == arr[j].material_number && !!arr[i].material_number){
							passObj = {
								isPass:false,
								msg:arr[i].material_number+'已被添加，请重新选择'
							}
							continue;
						}
					}
				}

				return passObj;
			},
			formatSubtract(){
				let self = this;
				let reg = /^(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/;
				let result = reg.test(self.data.subtract_money.value) || ''
				if(!reg.test(self.data.subtract_money.value)){
					self.$message.error('当前输入不符合规范，请输入1-100000000位整数')
					self.$nextTick(()=>{
						self.data.subtract_money.value = '';
					})
					return;
				}
				if(self.data.subtract_money.value<0 || self.data.subtract_money.value >parseFloat(self.max_money)){
							self.data.subtract_money.value = "";
							self.$message.error('当前金额不能大于门槛金额')
						}else{
							self.data.subtract_money.value = Math.abs(parseFloat(self.data.subtract_money.value));
						}
			}
		},


    mounted(){
			let self = this;
			this.getAllDiscountConfigureInfo();
			if(this.params.form.application_scenarios !='ACTIVITY_AS'){
				this.giftsCols=[
					{
						label:'物料编码',
						prop:'material_number',
						bool:true,
						blur(row){
							self.saveLoading = true;
							var han = new RegExp("[\\u4E00-\\u9FFF]+","g");
							if(row.if_present == 'Y') {
								if(han.test(row.material_number)){
									row.material_number = '';
									self.$message.error('所添加物料编码不能输入中文');
								}
								setTimeout(()=>{
									self.saveLoading = false;
								},300)
								return false;
							}else{
								self.saveLoading = false;
							}

							new Promise((resolve,reject)=>{
								self.getMaterialByNumber(row,'giftsData',resolve);
							}).then((d)=>{
								if(!d){
									row.material_number = '';
								}else{
									let passObj = self.selectSameMaterial(d.materialNumber);
									if(!passObj.isPass){
										row.material_number = '';
										self.$message.error(passObj.msg)
									}else{
										row.material_number = d.materialNumber;
										row.material_id = d.materialId;
										row.material_name = d.materialName;
										row.material_specification = d.materialSpecification;
										row.material_unit = d.materialUnit;
									}
								}
							})
						},
						width:150,
						// elInput:true,
						isClickEvent:true,
						iconClick(row) {
							new Promise((resolve,reject)=>{
								setTimeout(self.goodsChoose('giftsData',row,resolve), 10)
							}).then((d)=>{
								row.material_number = d.materialNumber;
								let passObj = self.selectSameMaterial(d.materialNumber);

								if(!passObj.isPass){
									row.material_number = '';
									self.$message.error(passObj.msg)
								}else{
									row.material_number = d.materialNumber;
									row.material_id = d.materialId;
									row.material_name = d.materialName;
									row.material_specification = d.materialSpecification;
									row.material_unit = d.materialUnit;
								}
							})
						},
						disabled(row) {
							return false;
						},
					}
					// ,{
					// 	label:'优惠金额',
					// 	prop:'discount_amount',
					// 	bool: true,
					// 	elInput:true,
					// 	disabled(row) {
					// 		return false;

					// 		// return row.if_convert_into_cash == 'N';
					// 	},
					// 	width:150,
					// 	change(row){
					// 		const result = self.fitterPrice2(row.discount_amount) || '';
					// 		self.$nextTick(() =>{
					// 			self.$set(row, 'discount_amount', result);
					// 		});
					// 	},
					// }
					,{
						label:'成本',
						prop:'cost_price',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},

						change(row){
							const result = self.fitterPrice2(row.cost_price) || '';
							self.$nextTick(() =>{
								self.$set(row, 'cost_price', result);
							});
						},
					},{
						label:'名义售价',
						prop:'nominal_price',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},

						change(row){
							const result = self.fitterPrice2(row.cost_price) || '';
							self.$nextTick(() =>{
								self.$set(row, 'nominal_price', result);
							});
						},
					},{
						label:'三包减免',
						prop:'three_reduction',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
						change(row){
							const result = self.fitterPrice4(row.three_reduction) || '';
							self.$nextTick(() =>{
								self.$set(row, 'three_reduction', result);
							});
						},
					},{
						label:'物流减免',
						prop:'logistics_reduction',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
						change(row){
							const result = self.fitterPrice4(row.logistics_reduction) || '';
							self.$nextTick(() =>{
								self.$set(row, 'logistics_reduction', result);
							});
						},
					},{
						label:'限量',
						prop:'count_limit',
						bool: true,
						elInput:true,
						change(row){
							const result = self.fitterPrice(row.count_limit) || '';
							self.$nextTick(() =>{
								self.$set(row, 'count_limit', result);
							});
						},
						disabled(row) {
							return false;
						},
					},{
						label:'个人承担比例',
						prop:'person_bear_ratio',
						bool: false,
						elInput:true,
						disabled(row) {
							return false;
						},
					},{
						label:'是否享受优惠',
						slot:'if_enjoy_discount',
					},{
						label:'备注',
						prop:'remark',
						bool: true,
						elInput:true,
						disabled(row) {
							return false;
						},
					},
				];
			}

      this.judgeIfServiceProjectNo();
    }
  }
</script>
<style>
	.repo-input{
		width:70px;
    /* float: right; */
    margin-right: 5px;
	}
  .xpt_col{
    margin-bottom: 10px;
  }
</style>
