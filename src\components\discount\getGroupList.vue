<template>
<div class='xpt-flex'>
<xpt-headbar>
    <el-button type='primary' size='mini' @click="save()" slot='left' >保存</el-button>
    <!-- <el-button type='danger' class='xpt-close' size='mini' @click="close" slot='right'>关闭</el-button> -->
</xpt-headbar>
  <el-transfer
    filterable
    :filter-method="filterMethod"
    filter-placeholder="请输入编码"
    v-model="value2"
    :titles="['待选分组信息', '已选分组信息']"
    :data="data2">
    
  </el-transfer>
</div>

</template>
<script>
import Fn from '@common/Fn.js'
  export default {
      props:['params'],
    data() {
      const generateData2 = _ => {
        let list = Fn.getAuxType2('shopGroup');
        const returndata = [];
        const values = Object.values(list);
        const keys = Object.keys(list);
        // console.log()
        
        values.forEach((data, index) => {
          returndata.push({
            label: data,
            key: index,
            keys: keys[index]
          });
        });
        return returndata;
      };
      return {
        data2: generateData2(),
        
        value2: [],
        filterMethod(query, item) {
          return item.keys.indexOf(query) > -1;
        },
        list:Fn.getAuxType2('shopGroup')
      };
    },
    methods:{
        getselectList(){
            const keyList = [];
            const keys = Object.keys(this.list);
            keys.forEach((item,ind) =>{
                // keyList.push()
                // console.log(this.params.KeyList);
                if(this.params.KeyList.indexOf(item)>-1){
                    keyList.push(ind);
                }
               
            });
            // console.log(keyList);
            this.value2 = keyList;
            // return keyList;
        },
        save(){
            const keys = Object.keys(this.list);
            const values = Object.values(this.list);
            let _this = this;
            let valuesList = [],
            KeyList = [];
            this.value2.forEach(item =>{
                valuesList.push(values[item]);
                KeyList.push(keys[item])
            });
            // console.log(returnList);
            this.params.callback({data:{valuesList:valuesList,KeyList:KeyList}});
            this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
        }
    },
    mounted : function(){
        this.getselectList();
    }
  };
</script>
<style>
    .el-transfer-panel__body{
            height: 283px;
    }
</style>
