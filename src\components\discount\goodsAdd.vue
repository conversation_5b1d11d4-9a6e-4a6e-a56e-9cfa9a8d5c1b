<!-- 优惠活动详情--添加商品 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type="primary" @click="submit('materialDetail')" size="mini">确认</el-button>
			</el-col>
		</el-row>
		<el-form :model='materialDetail' :rules='rules' ref='materialDetail' label-position="right" label-width="100px">
			<el-row>
				<el-col :span="16">
					<el-form-item label="优惠活动名称" >
						<el-input v-model="disDetail.act_name" size='mini' style="width:81.5%;" disabled></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="状态" >
						<el-input v-model="disDetail.status_desc" size='mini' disabled ></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="优惠类型" >
						<el-input v-model="disDetail.discount_category_desc" size='mini' disabled></el-input>
					</el-form-item>
					<el-form-item label="子分类" >
						<el-input v-model="disDetail.discount_subclass_desc" size='mini' disabled></el-input>
					</el-form-item>
					<el-form-item label="物料" prop='material_number'>
						<el-input v-model="materialDetail.material_number" 
							size="mini" 
							icon="search" 
							:on-icon-click="searchFun" 
							:readonly="!ctrl.inputGoods" 
							:disabled="!ctrl.isEdit" 
							@blur="queryMaterialByCode">
						</el-input>
						 <el-tooltip v-if='rules.material_number[0].isShow' class="item" effect="dark" :content="rules.material_number[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="生效时间" >
						<el-input v-model="disDetail.enable_date" size='mini' disabled ></el-input>
					</el-form-item>
					<el-form-item label="失效时间" >
						<el-input v-model="disDetail.disable_date" size='mini' disabled></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="物料名称" prop='material_name'>
						<el-input v-model="materialDetail.material_name" size='mini' :disabled="!ctrl.inputGoods"></el-input>
						 <el-tooltip v-if='rules.material_name[0].isShow' class="item" effect="dark" :content="rules.material_name[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="单位" prop="unit">
						<el-input v-model="materialDetail.unit" size='mini' :disabled="!ctrl.inputGoods"></el-input>
						<el-tooltip v-if='rules.unit[0].isShow' class="item" effect="dark" :content="rules.unit[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>

				</el-col>
				<el-col :span="16">
					<el-form-item label="规格描述" prop="material_desc">
						<el-input v-model="materialDetail.material_desc" size='mini' style="width:81.5%;" :disabled="!ctrl.inputGoods"></el-input>
						<el-tooltip v-if='rules.material_desc[0].isShow' class="item" effect="dark" :content="rules.material_desc[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="价格" prop="price">
						<el-input v-model="materialDetail.price" type="number" size='mini' :disabled="!ctrl.editPrice"></el-input>
						<el-tooltip v-if='rules.price[0].isShow' class="item" effect="dark" :content="rules.price[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="赠品">
						<el-switch v-model="materialDetail.if_present" on-text="是" off-text="否" :disabled='!ctrl.editPresent'></el-switch>
					</el-form-item>
					<el-form-item label="礼品">
						<el-switch v-model="materialDetail.if_gift" on-text="是" off-text="否" :disabled='!ctrl.editGift'></el-switch>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="成本" prop="cost_price">
						<el-input v-model="materialDetail.cost_price" type="number" size='mini' :disabled='!ctrl.editCost'></el-input>
						<el-tooltip v-if='rules.cost_price[0].isShow' class="item" effect="dark" :content="rules.cost_price[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="限量" prop="max_amount">
						<el-input v-model="materialDetail.max_amount" type="number" size='mini' :disabled='!ctrl.editAmount'></el-input>
						<el-tooltip v-if='rules.max_amount[0].isShow' class="item" effect="dark" :content="rules.max_amount[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="个人承担比例" prop='person_bear'>
						<el-input v-model="materialDetail.person_bear" size='mini' :disabled='!ctrl.isEdit'></el-input>
					    <el-tooltip v-if='rules.person_bear[0].isShow' class="item" effect="dark" :content="rules.person_bear[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="优惠金额" prop="discount_amount">
						<el-input v-model="materialDetail.discount_amount" type="number" size='mini' :disabled='!ctrl.editCost'></el-input>
						<el-tooltip v-if='rules.discount_amount[0].isShow' class="item" effect="dark" :content="rules.discount_amount[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="三包减免" prop="three_guarantees">
						<el-input v-model="materialDetail.three_guarantees" type="number" size='mini' :disabled='!ctrl.isEdit'></el-input>
						<el-tooltip v-if='rules.three_guarantees[0].isShow' class="item" effect="dark" :content="rules.three_guarantees[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="行状态" >
						<el-input v-model="materialDetail.disable_status" size='mini' disabled></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="物流减免" prop="logistics">
						<el-input v-model="materialDetail.logistics" type="number" size='mini' :disabled='!ctrl.isEdit'></el-input>
						<el-tooltip v-if='rules.logistics[0].isShow' class="item" effect="dark" :content="rules.logistics[0].message" placement="right-start" popper-class='xpt-form__error'>
						 	<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="行失效人" >
						<el-input v-model="materialDetail.disable_person_name" size='mini' disabled></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="行失效时间" >
						<el-input v-model="materialDetail.disable_time" size='mini' disabled></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="16">
					<el-form-item label="备注">
						<el-input v-model="materialDetail.marker" size='mini' type='textarea' :maxlength="240" style="width:81.5%;" :disabled='!ctrl.isEdit'></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
	
	import Fn from '@common/Fn.js'
	import VL from '@common/validate.js'
	// import staticData from '@common/staticData.js'

	export default {
		props:['params'],
		data(){
			var self = this;
			return{
				// 活动信息
				disDetail:{
					act_name:'',
					status_desc:'',
					status:'',
					discount_category: '',
					discount_category_desc:'',
					discount_subclass: '',
					discount_subclass_desc:'',
					enable_date:'',
					disable_date:''
				},
				// 物料信息
				materialDetail:{
					act_id: '',//	活动id		
				    act_material_id:'',//	活动商品id		
				    cost_price:'',//	成本		
				    if_gift:true,//	是否礼品		
				    if_present:true,//	是否赠品		
				    logistics:100,//	物流		
				    marker:'',//	备注		
				    material_desc:'',//	物料说明		
				    material_id:'',//	物料id		
				    material_name:'',//	物料名字		
				    material_number:'',//	物料编码		
				    max_amount:0,//	限量		
				    discount_amount: '',//优惠金额
				    price:0,//	价格		
				    three_guarantees:100,//	三包		
				    unit:'',//	单位
					disable_person_name:'',
					disable_status:'生效',
					disable_time:'',
					person_bear: '0.00'
				},
				// 物料字段编辑控制,提交审核和已审核不能编辑
				ctrl: {
					// 物料是否可选 
					inputGoods: false,
					// 赠品是否可以编辑
					editPresent: false,
					// 礼品是否可以编辑
					editGift: false,
					// 价格是否可以编辑
					editPrice: false,
					// 成本是否可以编辑
					editCost: false,
					// 限量是否可以编辑
					editAmount: false,
					// 其它字段编辑控制，主要是根据活动状态去控制
					isEdit: false
				},
				rules:{
					material_number:self.VLFun('isNotBlank',true,'请选择商品'),
					material_name:self.VLFun('isNotBlank',true,'请输入物料名称'),
					unit:self.VLFun('isNotBlank',true,'请输入单位'),
					material_desc:self.VLFun('isNotBlank',true,'请输入物料规格'),
					three_guarantees: [{
						message: '请输入0到100的整数',
						isShow: false,
						trigger: 'change',
						validator(rule, value, callback) {
							if(/^(0|100|[1-9]\d?)$/.test(value + '')) {
								self.rules[rule.field][0].isShow = false;
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								callback(new Error(''));
							}
						}
					}],
					logistics: [{
						message: '请输入0到100的整数',
						isShow: false,
						trigger: 'change',
						validator(rule, value, callback) {
							if(/^(0|100|[1-9]\d?)$/.test(value + '')) {
								self.rules[rule.field][0].isShow = false;
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								callback(new Error(''));
							}
						}
					}],
					price:[{
						message: '请输入价格,可以保留两位小数',
						isShow: false,
						trigger: 'change',
						validator(rule, value, callback) {
							if(/^(([1-9]\d*|0)(\.[0-9]{1,2})?)$/.test(value+'')){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}],
					discount_amount:[{
						required: self.params.discountData.discount_category === 'PRESENT_GOODS' && self.params.discountData.discount_subclass === 'SELECTED_COMMONDITIES',
						message: '请输入优惠金额,可以保留两位小数',
						isShow: false,
						trigger: 'change',
						validator(rule, value, callback) {
							if(rule.required && !value || value && !/^(([1-9]\d*|0)(\.[0-9]{1,2})?)$/.test(value+'')){
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}else {
								self.rules[rule.field][0].isShow = false
								callback();
							}
						}
					}],
					cost_price:[{
						message: '请输入成本,可以保留两位小数',
						// required: true,
						isShow: false,
						trigger: 'change',
						validator(rule, value, callback) {
							if(/^(([1-9]\d*|0)(\.[0-9]{1,2})?)$/.test(value+'')){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}],
					max_amount: [{
						message: '请输入大于等于0的整数',
						isShow: false,
						trigger: 'change',
						validator(rule, value, callback) {
							if(/^([1-9]\d*|0)$/.test(value + '')) {
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}],
					person_bear:[{
						message: '承担比例在0.00~1.00之间,保留两位小数',
						isShow: false,
						trigger: 'change',
						validator(rule, value, callback) {
							if(/^(0(\.[0-9]{1,2})?|1(\.[0]{1,2})?)$/.test(value+'')){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}]
				}
			}	
		},
		methods:{
			VLFun(name,required,msg){
				var self =this;
				return VL[name]({
					required:required,
					self:self,
					msg:msg,
				});
			},
			submit(formName){
				var self = this;
				this.$refs[formName].validate((valid) => {
						if (valid) {
							self.okFun();
						} else {
							return false;
						}
					});
			},
			okFun(){
				var self=this;
				var obj = self.materialDetail;
				obj.if_gift=obj.if_gift ? 1 : 0;
				obj.if_present=obj.if_present ? 1 : 0;
				obj.disable_status=(obj.disable_status==="生效")?0:1;
				this.params.callback(obj)
				this.$root.eventHandle.$emit('removeAlert',self.params.alertId);
			},
			searchFun(){
				let self = this;
				let params = {};
				if(!self.ctrl.isEdit){
					return;
				}
				// 选择商品
				params.callback = d=>{
					var data={
						material_desc:d.materialSpecification?d.materialSpecification:'无',//	物料说明
						material_id:d.sourceMaterialId,//	物料id
					    // material_id:self.params.isEditMateriel?'':d.sourceMaterialId,//	物料id	
					    material_name:d.materialName,//	物料名字		
					    material_number:d.materialNumber,//	物料编码
					    unit:d.materialUnit,//	单位
					};
					Object.assign(self.materialDetail,data);
					
				}
				params.isUpgrade = self.disDetail.discount_subclass === 'UPGRADE',
				this.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/order/selectGoodsList'),
					style:'width:800px;height:600px',
					title:'商品列表'
				});
			},
			// 根据物料编码查询物料信息
			queryMaterialByCode() {
				let code = this.materialDetail.material_number;
				if(!code) return;
				this.ajax.get('/material-web/api/material/getMaterialByNumber/'+ code, res => {
					if(res.body.result && res.body.content) {
						let data = res.body.content;
						this.materialDetail.material_desc = data.materialSpecification;	//	物料说明		
					    this.materialDetail.material_id = data.sourceMaterialId;		//	物料id,取财务中台物料ID		
					    this.materialDetail.material_name = data.materialName;			//	物料名字		
					    this.materialDetail.material_number = data.materialNumber;		//	物料编码	
					    this.materialDetail.unit = data.materialUnit;
					} else {
						this.$message.error(res.body.msg || '根据物料编码查询物料信息失败。');
						this.materialDetail.material_number = '';
					}
				}, err => {
					this.$message.error(err);
				})
			},
			/*
			@商品明细
			商品活动--赠品
			有无明细行		#赠品	#礼品	#价格	#成本价格							#限量
			有，可输入物料	#Y		#Y/N	#0		#手工输入的物料需要维护，否则为0		#需维护
			
			商品活动--换购
			#有，只能选择物料	#N		#N		#需维护	#0								#需维护

			商品活动--加价换购
			#有，只能选择物料	#N		#N		#需维护	#0								#需维护

			商品活动--客服46赠品 
			有，可输入物料	#Y		#N		#0		#手工输入的物料需要维护，否则为0		#需维护
			
			店长基金--赠品 
			#有，可输入物料	#Y		#Y/N	#0		#手工输入的物料需要维护，否则为0		#0
			
			店长基金--多拍优惠
			#有，只能选择物料	#N		#N		#0		#0								#0

			setDefaultValue是否设置默认值
			*/
			setEditStatus(setDefaultValue) {
				// 设置添加商品时弹窗页面的初始值
				// editXxxx表示字段是否可以编辑，true为可以编辑
				// inputGoods是否可以输入物料
				let seting = {
					PRESENT_GOODS: {
						GIFTS: {
							if_present: true,
							editPresent: false,
							if_gift: '',
							editGift: true,
							price: 0,
							editPrice: false,
							cost_price: '',
							editCost: true,
							max_amount: '',
							editAmount: true,
							inputGoods: true,
						},
						SELECTED_COMMONDITIES: {
							if_present: true,
							editPresent: false,
							if_gift: '',
							editGift: true,
							price: 0,
							editPrice: false,
							cost_price: '',
							editCost: true,
							max_amount: '',
							editAmount: true,
							inputGoods: true,
						},
						TRADE_UP: {
							if_present: false,
							editPresent: false,
							if_gift: false,
							editGift: false,
							price: '',
							editPrice: true,
							cost_price: 0,
							editCost: true,
							max_amount: '',
							editAmount: true,
							// inputGoods: false 2018.3.10 by zz
							inputGoods: true
						},
						UPGRADE: {
							if_present: false,
							editPresent: false,
							if_gift: false,
							editGift: false,
							price: '',
							editPrice: true,
							cost_price: 0,
							editCost: true,
							max_amount: '',
							editAmount: true,
							inputGoods: true
						}, 
						GIFTS_46: {
							if_present: true,
							editPresent: false,
							if_gift: false,
							editGift: false,
							price: 0,
							editPrice: true,
							cost_price: '',
							editCost: true,
							max_amount: '',
							editAmount: true,
							inputGoods: true,
						}
					},
					// 店长基金
					SHOP_FOUNDATION: {
						// 赠品
						SHOP_GIFTS: {
							if_present: true,
							editPresent: false,
							if_gift: '',
							editGift: true,
							price: 0,
							editPrice: true,
							cost_price: '',
							editCost: true,
							max_amount: 0,
							editAmount: true,
							inputGoods: true,
						},
						// 多拍优惠
						EXCHANGE_REWARD: {
							if_present: false,
							editPresent: false,
							if_gift: false,
							editGift: false,
							price: 0,
							editPrice: true,
							cost_price: 0,
							editCost: true,
							max_amount: 0,
							editAmount: true,
							// inputGoods: false  2018.3.10 by zz
							inputGoods: true
						}
					}
				}
				if(!(this.disDetail.discount_category && this.disDetail.discount_subclass)) return;
				let nowSetting = seting[this.disDetail.discount_category][this.disDetail.discount_subclass];
				if(!nowSetting) return;
				this.ctrl = {
					// 物料是否可选 
					inputGoods: nowSetting.inputGoods,
					// 赠品是否可以编辑
					editPresent: nowSetting.editPresent,
					// 礼品是否可以编辑
					editGift: nowSetting.editGift,
					// 价格是否可以编辑
					editPrice: nowSetting.editPrice,
					// 成本是否可以编辑
					editCost: nowSetting.editCost,
					// 限量是否可以编辑
					editAmount: nowSetting.editAmount,
					// 其它字段编辑控制，主要是根据
					isEdit: true
				};
				if(setDefaultValue) {
					this.materialDetail.if_gift = nowSetting.if_gift;
					this.materialDetail.if_present = nowSetting.if_present;
					this.materialDetail.price = nowSetting.price;
					this.materialDetail.cost_price = nowSetting.cost_price;
					this.materialDetail.max_amount = nowSetting.max_amount;
				}
			}
		},
		mounted() {
			// 设置优惠信息
			if(this.params.discountData) {
				let discountData = this.params.discountData;
				this.disDetail = {
					act_name: discountData.act_name,
					status_desc: discountData.status_desc,
					status: discountData.status,
					discount_category: discountData.discount_category,
					discount_category_desc: discountData.discount_category_desc,
					discount_subclass: discountData.discount_subclass,
					discount_subclass_desc: discountData.discount_subclass_desc,
					enable_date: Fn.dateFormat(discountData.enable_date, 'yyyy-MM-dd hh:mm:ss'),
					disable_date: Fn.dateFormat(discountData.disable_date, 'yyyy-MM-dd hh:mm:ss')
				}
			}
			// 设置物料信息
			if(this.params.goodsData) {
				for(let v in this.materialDetail) {
					if(v === 'disable_status') {
						this.materialDetail[v] = this.params.goodsData[v] ? '失效' : '生效';
					} else if(v === 'if_gift' || v === 'if_present') {
						this.materialDetail[v] = this.params.goodsData[v] ? true : false;
					} else if(v === 'disable_time') {
						this.materialDetail[v] = Fn.dateFormat(this.params.goodsData[v], 'yyyy-MM-dd hh:mm:ss');
					} else {
						this.materialDetail[v] = this.params.goodsData[v];
					}
				}
			}
			// 设置物料字段编辑状态,活动状态为提交审核，所有字段不可编辑
			if(!(this.disDetail.status === 'SUBMITTED'/* || this.disDetail.status === 'APPROVED'*/)) {
				if(this.params.goodsData) {
					this.setEditStatus();
				} else {
					// 新添加时需要设定一些初始值
					this.setEditStatus(true);
				}
			}
			/*
			设置客服46商品信息
			个人承担比例0.4
			*/
			if('GIFTS_46' == this.disDetail.discount_subclass) {
				this.materialDetail.person_bear = 0.40
			}
		}
	}
</script>