<!-- 优惠活动列表 -->
<template>
	<xpt-list 
		:data='tableData' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='totalPage' 
		searchPage='pms_act' 
		searchHolder='请输入查询条件' 
		@search-click='preSearch' 
		@selection-change='handleSelectionChange' 
		@page-size-change='handleSizeChange' 
		@current-page-change='handleCurrentChange'  
	>
		<xpt-import slot='btns' :taskUrl='uploadUrl' class='mgl10'></xpt-import>
	</xpt-list>
</template>
<script>
export default {
	data(){
		var self = this;
		return{
			uploadUrl:'/price-web/api/price/pms/immport/addImportPmsActListInfo',
			btns: [
				{
					type: 'primary',
					txt: '新增',
					click: self.addFun
				}, {
					type: 'danger',
					txt: '删除',
					loading: false,
					click: self.delFun
				}, {
					type: 'success',
					txt: '提交',
					loading: false,
					click() {
						self.clickFun('submit?permissionCode=DISCOUNT_ACTIVITY_SUBMIT','提交','sub');
					}
				}, {
					type: 'warning',
					txt: '撤回',
					loading: false,
					click() {
						self.clickFun('withdrawAct?permissionCode=DISCOUNT_ACTIVITY_WITHDRAW','撤回','recall');	
					}
				}, {
					type: 'success',
					txt: '审核',
					loading: false,
					click() {
						self.clickFun('approve?permissionCode=DISCOUNT_ACTIVITY_AUDIT','审核','audit');
					}
				}, {
					type: 'warning',
					txt: '驳回',
					loading: false,
					click() {
						self.clickFun('retrial?permissionCode=DISCOUNT_ACTIVITY_REJECT','驳回','back');
					}
				}, {
					type: 'danger',
					txt: '禁用',
					loading: false,
					click() {
						// self.forbidFun('forbidAct?permissionCode=DISCOUNT_ACTIVITY_FORBID','禁用','ban');
						self.forbidFun('forbidAct','禁用','ban');
					}
				}
			],
			cols: [
				{
					label: '优惠活动ID',
					prop: 'act_id',
					width: 150
				}, {
					label: '优惠活动名称',
					prop: 'act_name',
					redirectClick(row) {
						self.toEditFun(row.act_id)
					},
					width: 150
				}, {
					label: '优惠类型',
					prop: 'discount_category_desc'
				}, {
					label: '子类型',
					prop: 'discount_subclass_desc'
				}, {
					label: '状态',
					prop: 'status_desc'
				}, {
					label: '全局标志',
					prop: 'if_global',
					formatter(val) {
						return self.ifGlobalFun(val)
					}
				}, {
					label: '生效日期',
					prop: 'enable_date',
					format: 'dataFormat1',
					width: 140
				}, {
					label: '失效日期',
					prop: 'disable_date',
					format: 'dataFormat1',
					width: 140
				}, {
					label: '影响日期起',
					prop: 'enable_date',
					format: 'dataFormat1',
					width: 140
				}, {
					label: '影响日期止',
					prop: 'disable_date',
					format: 'dataFormat1',
					width: 140
				}, {
					label: '说明',
					prop: 'marker'
				}, {
					label: '创建人',
					prop: 'creator_name'
				}, {
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',

				}, {
					label: '审核人',
					prop: 'audit_name'
				}, {
					label: '更新人',
					prop: 'modifier_name'
				}
			],
			searchObj:{
				page_no:1,
				page_size: self.pageSize,
				act_name:'',
				page_name: 'pms_act',
				where: []
			},
			totalPage:0,
			searchInput:'',
			tableData:[],
			selectId:[],
			searchKey:''
		}
	},
	props:['params'],
	mounted(){
		var self=this;
		self.$root.eventHandle.$on('discountAdd',(d)=>{
			self.searchFun();
		});
	},
	destroyed(){
		this.$root.offEvents('discountAdd');
    },
	methods:{
		addFun(){
			this.$root.eventHandle.$emit('creatTab',{name:'新建优惠活动',component:()=>import('@components/discount/add.vue')});
		},
		delFun(){
			var self = this;
			if(self.selectId.length===0){
				self.$message.error('请选择要删除的活动优惠');
			}
			var data={
				ids:self.selectId
			};
			var resObj = self.ajaxPost('delete?permissionCode=DISCOUNT_ACTIVITY_DELETE',data,'删除成功','del');
		},
		forbidFun(api,tip,name){
			var self = this;
			if(self.selectId.length===0){
				self.$message.error('请选择要'+tip+'的活动优惠');
				return;
			}
			var data={
				ids:self.selectId,
				modifier:self.getEmployeeInfo('id'),
				modifierName:self.getEmployeeInfo('fullName')
			};
			self.ajaxPost(api,data,tip+'成功',name);
		},
		clickFun(api,tip,name){
			var self = this;
			if(self.selectId.length===0){
				self.$message.error('请选择要'+tip+'的活动优惠');
				return;
			}
			var data={
				ids:self.selectId
			};
			self.ajaxPost(api,data,tip+'成功',name);
		},
		ajaxPost(api,data,tip,name, resolve){
			var self = this;
			var url = '/price-web/api/price/pms/page/'+api;
			let setStatus = (n, status) => {
				switch(n) {
					case 'del': this.btns[1].loading = status; break;
					case 'sub': this.btns[2].loading = status; break;
					case 'recall': this.btns[3].loading = status; break;
					case 'audit': this.btns[4].loading = status; break;
					case 'back': this.btns[5].loading = status; break;
					case 'ban': this.btns[6].loading = status; break;
					default: break;
				}
			}
			setStatus(name, true);
			this.ajax.postStream(url,data, (res) => {
				if(res.body.result){
					if(name==="search"){
						self.tableData = res.body.content.list;
						self.totalPage = res.body.content.count;
					}else{
						self.$message.success(tip);
						self.searchFun();
					}
				}else{
					self.$message.error(res.body.msg || '');
				}
				resolve && resolve();
				setStatus(name, false);
			}, (res) => {
				resolve && resolve();
				self.$message.error(res);
				setStatus(name, false);
			});
		},
		preSearch(txt, resolve){
			this.searchObj.where = txt
			this.ajaxPost('actList?permissionCode=DISCOUNT_ACTIVITY_QUERY',this.searchObj,'','search', resolve)
		},
		initSearchData(){
			if(!this.searchKey) this.searchObj.act_name = ''
			else this.searchObj.act_name = this.searchKey
		},
		searchFun(){
			this.ajaxPost('actList?permissionCode=DISCOUNT_ACTIVITY_QUERY',this.searchObj,'','search')
		},
		handleSelectionChange(selectArr){
			var self = this
			self.selectId=[]
			selectArr.map((v)=>{
				self.selectId.push(v.act_id)
			});
		},
		handleSizeChange(val){
			this.searchObj.page_size = val;
			this.initSearchData()
			this.searchFun();
		},
		handleCurrentChange(val){
			this.searchObj.page_no = val;
			this.initSearchData()
			this.searchFun();
		},
		toEditFun(id){
			this.$root.eventHandle.$emit('creatTab',{name:'编辑优惠活动',params:{act_id:id},component:()=>import('@components/discount/add.vue')});
		},
		ifGlobalFun(val){
			if(val == 1){
				return "全局";
			}else{
				return "店铺";
			}
		}
	}
}
</script>