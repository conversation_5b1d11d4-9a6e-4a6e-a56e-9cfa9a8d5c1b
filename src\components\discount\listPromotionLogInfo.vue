<!-- 优惠分摊记录 -->
<template>
	<xpt-list
		:data='tableData'
		:colData='cols'
		:pageTotal='null'
    :btns='btns'
    :searchPage='searchObj.page_name'
		@search-click='preSearch'
    :selection="null"
	>
	</xpt-list>
</template>
<script>
export default {
	data(){
		var self = this;
		return{
			uploadUrl:'/price-web/api/actDiscount/addImportActDiscountInfo',
			btns: [
				{
					type: 'success',
					txt: '导出',
					loading: false,
					disabled:false,
					click() {
						self.exportSingleActDiscount()
					}
				},
			],
			cols: [
				{
					label: '合并单号',
					prop: 'merge_trade_no',
					width: 180
				}, {
					label: '销售单号',
					prop: 'sys_trade_no',
					width: 180
				},{
					label: '淘宝单号',
					prop: 'tid',
					width: 150
				},{
					label: '优惠活动编码',
					prop: 'discount_no',
					width: 150
				}, {
          label: '优惠活动',
          prop: 'activity_name'
        }, {
					label: '优惠类型',
					prop: 'activity_type_name',
				}, {
					label: '优惠子类型',
					prop: 'activity_subclass_name',
				}, {
					label: '优惠类别',
					prop: 'discount_category_name'
				}, {
					label: '阶梯',
					prop: 'level'
				}, {
					label: '商品编码',
					prop: 'materia_number'
				},{
					label: '系统售价',
					prop: 'stand_price'
				},{
					label: '线上售价',
					prop: 'sale_price'
				},{
					label: '付返前实际售价',
					prop: 'before_discount_act_price'
				}, {
					label: '优惠券金额',
					prop: 'discount'
				},{
					label: '分摊金额',
					prop: 'discount_fee'
				}
			],
			searchObj:{
        merge_trade_no: '',
				page_name:'promotion_log_info',
				where: []
			},
			totalPage:0,
			tableData:[]
		}
	},
	props:['params'],
	methods:{
		preSearch(list, resolve){
			let self = this;
      let mergeFilter = list.filter(item => {
        return item.field == "931a8b828e640020fa6652104e5ca513"
      })
      if (mergeFilter.length > 0) {
        self.searchObj.merge_trade_no = mergeFilter[0].value
      } else {
        self.searchObj.merge_trade_no = ''
      }
			this.searchObj.where = list;
      this.searching(resolve);
		},
    searching(resolve){
			let self = this;
			this.ajax.postStream('/order-web/api/mergetrade/discount/listPromotionLogInfo',self.searchObj,function(response){
				if(response.body.result){
					self.tableData = response.body.content || [];
					resolve && resolve();
					self.$message.success(response.body.msg)
				}else{
					self.tableData = [];
					self.$message.error(response.body.msg || '');
				}
        resolve && resolve();
			}, err => {
        self.tableData = []
				this.$message.error(err);
        resolve && resolve();
			});
		},
    exportSingleActDiscount(){
			let self = this;
			self.btns.map(btn => {
        if (btn.txt === '导出') {
          btn.disabled = true
        }
      })
			self.ajax.postStream('/order-web/api/mergetrade/discount/exportPromotionLogInfo', this.searchObj, res => {
				if(res.body.result ) {
					if(res.body.content){
						self.$message.success(res.body.msg);
            self.downLoad(res.body.content)
					}else{
						self.$message.error('不能重复操作');
					}

				}else{
					self.$message.error(res.body.msg)
				}
				self.btns.map(btn => {
        if (btn.txt === '导出') {
          btn.disabled = false
        }
      })
			})
		},
    // 操作下载
    downLoad(url){
      if (!fetch) {
        window.location.href = url;
        return;
      }
      return fetch(url).then((res) => {
          console.log(res);
          res.blob().then((blob) => {
              let a = document.createElement("a");
              let blobUrl = window.URL.createObjectURL(blob);
              a.href = blobUrl;
              a.download = `${this.searchObj.merge_trade_no}优惠分摊记录.xls`;
              a.click();
              window.URL.revokeObjectURL(url);
          });
      });
    }
	}
}
</script>
