/*
优惠活动--优惠商品
*/
import Fn  from '@common/Fn.js'
export default {
	data() {
		let self = this

		this._getAllGoodsSearchData.data = []
		this._getAllGoodsSearchData.del = []

		return {
			goodsBtns: [
				{
					type: 'primary',
					txt: '添加商品',
					disabled: true,
					click() {
						self.goodsAdd()
					}
				}, {
					type: 'danger',
					txt: '删除',
					disabled: true,
					click: self.goodsDel
				}, {
					type: 'info',
					txt: '失效',
					disabled: true,
					click: self.goodsInvalid
				}
			],
			goodsCols: [
				{
					label: '物料',
					prop: 'material_number',
					width: 120,
					redirectClick(row) {
						self.goodsAdd(row)
					}
				}, {
					label: '物料名称',
					prop: 'material_name'
				}, {
					label: '规格描述',
					prop: 'material_desc',
					width: 350
				}, {
					label: '单位',
					prop: 'unit'
				}, {
					label: '价格',
					prop: 'price',
					align: 'right',
					formatter: self.priceFormat
				}, {
					label: '优惠金额',
					prop: 'discount_amount',
					align: 'right',
					formatter: self.priceFormat
				}, {
					label: '赠品',
					prop: 'if_present',
					formatter: self.isFormat,
					width: 40
				}, {
					label: '礼品',
					prop: 'if_gift',
					formatter: self.isFormat,
					width: 40
				}, {
					label: '成本',
					prop: 'cost_price',
					align: 'right',
					formatter: self.priceFormat
				}, {
					label: '三包减免',
					prop: 'three_guarantees',
					align: 'right',
					formatter: self.percFormat
				}, {
					label: '物流减免',
					prop: 'logistics',
					align: 'right',
					formatter: self.percFormat
				}, {
					label: '限量',
					prop: 'max_amount',
					align: 'right'
				}, {
					label: '行状态',
					prop: 'disable_status',
					formatter: self.statusFormat
				}, {
					label: '商品状态',
					prop: 'line_status',
					formatter: prop => ({
						Y: '已审核',
						N: '未审核',
					}[prop] || prop),
				}, {
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 150,
				}, {
					label: '个人承担比例',
					prop: 'person_bear',
					align: 'right',
					width: 100
				}
			],
			goodsCount: 0,
			goodsSearch:{
				page_name:"pms_act_material",
				where:[],
				page_size:this.pageSize,
				page_no:1,
				act_id:''
			},
			markListPmsActMaterialVOData: [],
			// 所选商品
			goodsSelect: ''
		}
	},
	methods: {
		/*
		数据过滤Start
		*/
		priceFormat(val) {
			return Fn.number(val);
		},
		isFormat(val) {
			if(val == 1) {
				return "是";
			}else{
				return "否";
			}
		},
		statusFormat(val) {
			if(val == 0||val == '') {
				return "生效";
			} else {
				return "失效";
			}
		},
		percFormat(val) {
			return val+'%';
		},
		/*
		数据过滤End
		*/
		
		goodsPresearch(list, resolve){
			this.isListPmsActMaterialVOChange(resolve)
			this.goodsSearch.where = list;
			this.goodsSearching(resolve);
		},
		// 判断当前页的数据是否有修改
		isListPmsActMaterialVOChange (resolve){
			if(this.compareData(this.form.listPmsActMaterialVO, this.markListPmsActMaterialVOData)){
				resolve && resolve()
				this.$message.error('当前页有修改，请先保存！')
				throw('当前页有修改，请先保存！')
			}
		},
		// 每一次刷新优惠商品时都要那一次全部数据，用于保存时(XPT-8565对接口bug的补丁)
		// 每次新增、删除、失效(修改行状态)都要修改this._getAllGoodsSearchData.data
		_getAllGoodsSearchData (cb){
			this.ajax.postStream('/price-web/api/price/pms/page/listActMaterial', Object.assign({}, this.goodsSearch, {
				where:[],
				page_size: 10000,
				page_no:1,
			}), res => {
				this._getAllGoodsSearchData.del = []
				if(res.body.result){
					this._getAllGoodsSearchData.data = res.body.content.list || []
				}
				cb()
			}, () => {
				this._getAllGoodsSearchData.del = []
				cb()
			})
		},
		// 保存时转换成所有数据
		_saveWithAllActMaterial (data){
			var act_material_id_list = {}

			data.forEach(obj => {
				if(obj.act_material_id){
					act_material_id_list[obj.act_material_id] = obj
				}else {
					this._getAllGoodsSearchData.data.push(obj)
				}
			})
			console.log(this._getAllGoodsSearchData.data)

			this._getAllGoodsSearchData.data.forEach((obj, index) => {
			// console.log(obj)
				
				if(obj && obj.act_material_id){
					if (this._getAllGoodsSearchData.del.indexOf(obj.act_material_id) !== -1){
						this._getAllGoodsSearchData.data[index] = null
					}else if(act_material_id_list[obj.act_material_id]){
						this._getAllGoodsSearchData.data[index] = act_material_id_list[obj.act_material_id]
					}
				}
			})

			return this._getAllGoodsSearchData.data.filter(Boolean)
		},
		// 请求失败时，将goodsData里新增的对象对应的_getAllGoodsSearchData.data也删除
		_saveWithAllActMaterialFailCallback (data){
			data.forEach(obj => {
				if(!obj.act_material_id){
					this._getAllGoodsSearchData.data.splice(this._getAllGoodsSearchData.data.indexOf(obj), 1)
				}
			})
		},
		// 获取优惠商品列表
		goodsSearching(resolve){
			let _this = this
			if(!_this.goodsSearch.act_id) return

			this._getAllGoodsSearchData(() => {
				this.ajax.postStream('/price-web/api/price/pms/page/listActMaterial'/*'/price-web/api/price/pms/page/listActMaterial?permissionCode=DISCOUNT_GOODS_QUERY'*/, 
					_this.goodsSearch,
					res => {
						if(res.body.result && res.body.content) {
							_this.form.listPmsActMaterialVO = res.body.content.list || []
							_this.markListPmsActMaterialVOData = JSON.parse(JSON.stringify(res.body.content.list || []))
							_this.goodsCount = res.body.content.count || 0
							_this.goodsChangeStatus()
							_this.params.__data = JSON.parse(JSON.stringify(_this.form))
						}
						resolve && resolve();
				}, err => {
					resolve && resolve();
					this.$message.error(err);
				})
			})
		},
		goodsPageSizeChange(size){
			this.isListPmsActMaterialVOChange()
			this.goodsSearch.page_size = size
			this.goodsSearching()
		},
		goodsPageChange(page_no){
			this.isListPmsActMaterialVOChange()
			this.goodsSearch.page_no = page_no
			this.goodsSearching()
		},
		goodsSelectionChange(selectArr) {
			this.goodsSelect = selectArr
		},
		// 添加、编辑商品
		goodsAdd(row) {
			if(row && row.act_material_id.toString().indexOf('ID') > -1) {
				this.$message.error('请保存后再进行编辑')
				return
			}
			// 根据优惠类型获取分类及子分类名字,主要是针对新增的优惠活动
			if(!this.form.act_id) {
				let discountCategory = __AUX.get('discountCategory'),
					i = discountCategory.length;
				while(i--) {
					if(discountCategory[i].code === this.form.discount_category) {
						this.form.discount_category_desc = discountCategory[i].name;
						break;
					}
				}
				let discountSubclass = this.childClassDataAll;
				i = discountSubclass.length;
				while(i--) {
					if(discountSubclass[i].code === this.form.discount_subclass) {
						this.form.discount_subclass_desc = discountSubclass[i].name;
						break;
					}
				}
			}
			let self = this,
				p = Math.ceil(self.count/self.goodsSearch.page_size);
			var para = {
				goodsData: row,
				discountData: self.form,
				callback:function(d){
					d.person_bear = d.person_bear * 1
					let i = self.form.listPmsActMaterialVO.length,
						// 是否为编辑，true为是，false为添加
						isMod = false,
						// 是否可以添加
						canAdd = true;
					// 编辑商品
					if(row) {
						while(i--) {
							if(self.form.listPmsActMaterialVO[i].act_material_id === d.act_material_id) {
								Object.assign(self.form.listPmsActMaterialVO[i], d)
								break
							}
						}
						self.goodsChangeStatus()
						return
					}
					// 校验是否已经添加
					i = self._getAllGoodsSearchData.data.length			
					while(i--) {
						if(self._getAllGoodsSearchData.data[i].material_id === d.material_id) {
							self.$message.warning(d.material_number+"物料已存在，不需要添加")
							canAdd = false
							break
						}
					}
					self.goodsChangeStatus()
					if(!canAdd) return
					// 编辑时覆盖原有值
					i = self.form.listPmsActMaterialVO.length
					while(i--) {
						if(self.form.listPmsActMaterialVO[i].act_material_id === d.act_material_id) {
							Object.assign(self.form.listPmsActMaterialVO[i], d)
							isMod = true
							break
						}
					}
					if(isMod) return
					if(/^(已审核|重新审核)$/.test(self.form.status_desc)) d.isNew = 'Y'
					// 添加到商品行
					d.act_material_id = 'ID' + new Date().getTime()
					d.line_status = 'N'//新增时默认商品状态为未审核
					self.form.listPmsActMaterialVO.push(d)
					self.goodsChangeStatus()
				}
			}
			this.$root.eventHandle.$emit('alert',{
				title: row ? '编辑优惠商品' : '优惠商品新增',
				params:para,
				style: 'width:930px; height: 600px',
				component:()=>import('@components/discount/goodsAdd.vue')
			})
		},
		// 删除商品
		goodsDel() {
			if(!this.goodsSelect.length) {
				this.$message.error('请选择要删除的活动商品')
				return
			}else if (/^(已审核|重新审核)$/.test(this.form.status_desc) && this.goodsSelect.some(obj => obj.line_status === 'Y')){
				this.$message.error('不能删除已审核的优惠商品')
				return
			}
			;(this.goodsSelect || []).find(d => {
				let i = this.form.listPmsActMaterialVO.length
				while(i--) {
					if(d.act_material_id === this.form.listPmsActMaterialVO[i].act_material_id) {
						this.form.listPmsActMaterialVO.splice(i, 1)
						this._getAllGoodsSearchData.del.push(d.act_material_id)
						break
					}
				}
			})
			this.goodsChangeStatus()
		},
		// 商品失效
		goodsInvalid() {
			let i = this.goodsSelect.length,
				isHaveTemp
			if(!i) {
				this.$message.error('请选择要失效的活动商品')
				return
			}
			while(i--) {
				if(this.goodsSelect[i].act_material_id.toString().indexOf('ID') > -1){
					this.$message.error('请保存后再进行失效操作')
					isHaveTemp = true
					return
				}
			}
			if(isHaveTemp) return;
			// 所选数据是否全部已经失效
			let isAllInvalid = true;
			this.goodsSelect.find(d => {
				if(d.disable_status === 0) {
					isAllInvalid = false;
					d.disable_status = 1
				}
			})
			if(isAllInvalid) {
				this.$message.info('已经失效的商品无需再次失效');
			} else {
				this.$message.success('失效成功')
			}
		},
		/*
		变更需要审核状态,
		当优惠类型为商品活动，子分类为赠品的时候个从承担比例有大于0的状况需要审核
		当优惠类型为店长基金，子分类为赠品的时候个从承担比例有大于0的状况需要审核
		*/
		goodsChangeStatus() {
			// 判断是否全部个从承担比例为0，true为是
			// let isAllZero = true
			// if(this.form.discount_subclass === 'GIFTS' || this.form.discount_subclass === 'SHOP_GIFTS') {
			// 	(this.form.listPmsActMaterialVO || []).find(d => {
			// 		if(d.person_bear > 0.00) {
			// 			this.form.if_verify = true
			// 			isAllZero = false
			// 		}
			// 	})
			// 	if(isAllZero && this.form.if_verify) {
			// 		this.form.if_verify = false
			// 	}
			// }
		},
		/*
		用于控制优惠商品按钮可以操作状态,并设置添加商品时的初始值
		创建、已撤回、驳回、重新审核：商品、店铺加、删、失效都可以
		提交审核：商品、店铺不允许任何编辑操作
		已审核：商品、店铺只允许失效操作
		*/
		goodsBtnControl() {
			let self = this,
				status = this.form.status_desc,				// 优惠状态
				category = this.form.discount_category,		// 优惠类型
				subclass = this.form.discount_subclass,		// 子分类
				ifShop = false								// 所选优惠类型及子分类组合是否可以操作商品，true为可以
			if(category === 'PRESENT_GOODS') {
				// 商品活动
				ifShop = true
			} else if(category === 'SHOP_FOUNDATION') {
				if(subclass === 'SHOP_GIFTS' || subclass === 'EXCHANGE_REWARD') {
					ifShop = true
				}
			}
			if(status === '创建' || status === '已撤回' || status === '重新审核' || status === '已审核') {
				this.goodsBtns = [
					{
						type: 'primary',
						txt: '添加商品',
						disabled: ifShop ? false : true,
						click() {
							self.goodsAdd()
						}
					}, {
						type: 'danger',
						txt: '删除',
						disabled: ifShop ? false : true,
						click: self.goodsDel
					}, {
						type: 'info',
						txt: '失效',
						disabled: ifShop ? false : true,
						click: self.goodsInvalid
					}
				]
			} else if(status === '提交审核') {
				this.goodsBtns = [
					{
						type: 'primary',
						txt: '添加商品',
						disabled: true,
						click() {
							self.goodsAdd()
						}
					}, {
						type: 'danger',
						txt: '删除',
						disabled: true,
						click: self.goodsDel
					}, {
						type: 'info',
						txt: '失效',
						disabled: true,
						click: self.goodsInvalid
					}
				]
			}/* else if(status === '已审核') {
				this.goodsBtns = [
					{
						type: 'primary',
						txt: '添加商品',
						disabled: true,
						click() {
							self.goodsAdd()
						}
					}, {
						type: 'danger',
						txt: '删除',
						disabled: true,
						click: self.goodsDel
					}, {
						type: 'info',
						txt: '失效',
						disabled: ifShop ? false : true,
						click: self.goodsInvalid
					}
				]
			}*/ else {
				this.goodsBtns = [
					{
						type: 'primary',
						txt: '添加商品',
						disabled: true,
						click() {
							self.goodsAdd()
						}
					}, {
						type: 'danger',
						txt: '删除',
						disabled: true,
						click: self.goodsDel
					}, {
						type: 'info',
						txt: '失效',
						disabled: true,
						click: self.goodsInvalid
					}
				]
			}
		}
	}
}
