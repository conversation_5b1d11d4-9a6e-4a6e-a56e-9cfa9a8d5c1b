/*优惠类型设定*/
export default {
	methods: {
		/*
		用于设定不同优惠类型、子分类的初始值

		优惠类型设定
		优惠类型 		#子分类 		#优惠活动 	#是否需要审核 	#影响售价 	#优惠金额 	#使用门槛 	#金额变更
		线上优惠 		#优惠券 		#订单	  	#N       		#Y      	#自动计算   	#0       	#N
					#调整优惠券	#订单	  	#Y				#Y		 	#自动计算		#0		 	#N	
		线下优惠券	#一般线下优惠	#订单	  	#N				#Y		 	#需要		#需维护		#N	
				 #线上转线下优惠	#订单	  	#N				#Y		 	#自动计算		#0			#N	
		返现						#订单		#Y				#N		 	#需要		#需维护		#Y	
		活动包邮		#地级市包邮	#运费		#Y				#N			#0			#需维护		#Y	
					#自治区包邮	#运费		#Y				#N			#0			#需维护		#Y	
		拆单免运费				#运费		#N				#N			#0			#需维护		#Y	
		差价减免					#商品		#Y				#Y			#0			#0			#Y	
		商品活动		#赠品		#商品		#N				#N			#0			#需维护		#N
					#换购		#商品		#N				#N			#0			#需维护		#N
					#加价换购		#商品		#N				#N			#0			#需维护		#N
					#客服46		#商品 		#Y				#N 			#0 			#0			#N
		特殊优惠		#商务折扣		#订单		#Y				#Y			#0			#0			#Y
					#延误换货		#订单		#Y				#Y			#0			#0			#Y	
		责任金（扣款）			#订单		#Y				#N			#0			#0			#Y	
		店长基金		#赠品		#商品		#Y				#N			#0			#需维护		#N	
					#运费		#订单		#Y				#N			#0			#需维护		#Y
					#多拍优惠		#订单		#Y				#Y			#5			#0			#N
		*/
		setDefaultValue() {
			let category = this.form.discount_category,
				subclass = this.form.discount_subclass,
				setting = {
					// 线上优惠
					ONLINE_DISCOUNT: {
						// 优惠券
						'COUPON': {
							discount_item: 'ORDER',
							if_verify: false,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: false
						},
						// 调整优惠券
						'ADJUSTED_COUPON': {
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: false
						},
						// 经销商优惠
						'DEALER_COUPON': {
							discount_item: 'ORDER',
							if_verify: false,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: false
						},
					},
					// 线下优惠券
					OFFLINE_DISCOUNT: {
						NORMALOFFLINE_DISCOUNT: {
							discount_item: 'ORDER',
							if_verify: false,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: false
						},
						TRANSOFFLINE_DISCOUNT: {
							discount_item: 'ORDER',
							if_verify: false,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: false
						},
						EXCHANGE_GAP: {
							discount_item: 'ORDER',
							if_verify: false,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true
						},
						OFF_SINGLE_PRODUCT: {
							discount_item: 'ORDER',
							if_automatic_add:false,
							if_global:false,
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true
						},
						OFF_RETURN_DISCOUNT: {
							discount_item: 'ORDER',
							if_automatic_add:false,
							if_global:false,
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true
						},
					},
					// 返现
					REFUND: {
						default: {
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: true
						}
					},
					// 订金膨胀
					DEPOSIT_RISE: {
						default: {
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true
						}
					},
					// 活动包邮
					FREE_SERVICE: {
						CITY: {
							discount_item: 'FREIGHT',
							if_verify: true,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: true
						},
						STATE: {
							discount_item: 'FREIGHT',
							if_verify: true,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: true
						}
					},
					// 拆单免运费
					FREE_BATCHES_SERVICE: {
						default: {
							discount_item: 'FREIGHT',
							if_verify: true,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: true
						}
					},
					// 差价减免
					REDUCTION: {
						default: {
							discount_item: 'GOODS',
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true
						}
					},
					// 商品活动
					PRESENT_GOODS: {
						GIFTS: {
							discount_item: 'GOODS',
							if_verify: false,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: false
						},
						SELECTED_COMMONDITIES: {
							discount_item: 'GOODS',
							if_verify: false,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: false,
							discount_affection_date: 'PAYOFF_DATE',
						},
						TRADE_UP: {
							discount_item: 'GOODS',
							if_verify: false,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: false
						},
						UPGRADE: {
							discount_item: 'GOODS',
							if_verify: false,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: false
						}, 
						GIFTS_46: {
							discount_item: 'GOODS',
							if_verify: false,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: false
						}
					},
					// 特殊优惠
					SPECIAL_DISCOUNT: {
						BUSINESS_DISCOUNT: {
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true
						},
						DELAY_REDUCTION: {
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true
						},
						DISCOUNT_HISTORY_DATA: {//财务中台历史数据问题优惠
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true,
						},
						DISCOUNT_COMPENSATION: {//客服责任优惠
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true,
						},
						DISCOUNT_AFTER_SALE: {//售后费用优惠
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: true,
							price: 0,
							enable_price: 0,
							if_change_price: true,
						},
					},
					// 责任金
					COMPENSATION: {
						default: {
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: true
						}
					},
					// 店长基金
					SHOP_FOUNDATION: {
						// 赠品
						SHOP_GIFTS: {
							discount_item: 'GOODS',
							if_verify: true,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: false
						},
						// 运费
						FREIGHT_COMPENSATION: {
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: false,
							price: 0,
							enable_price: 0,
							if_change_price: true
						},
						// 多拍优惠
						EXCHANGE_REWARD: {
							discount_item: 'ORDER',
							if_verify: true,
							if_effect_price: true,
							price: 5,
							enable_price: 0,
							if_change_price: true
						}
					}
				}
			if(this.isAdd) {
				// 设置优惠活动
				if(setting[category].default) {
					// 没有子分类
					Object.assign(this.form, setting[category].default)
					let sets = setting[category]['default'];
					this.form.discount_item = sets.discount_item;
					this.form.if_verify = sets.if_verify;
					this.form.if_effect_price = sets.if_effect_price;
					this.form.price = sets.price;
					this.form.enable_price = sets.enable_price;
					this.form.if_change_price = sets.if_change_price;
				} else {
					// 有子分类
					if(subclass) {
						Object.assign(this.form, setting[category][subclass])
					}
				}
			}
		}
	}
}