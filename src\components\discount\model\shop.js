/*
优惠活动--实施店铺
*/
import Fn from '@common/Fn';
export default {
	data() {
		let self = this

		this._getAllShopsSearchData.data = []
		this._getAllShopsSearchData.del = []

		return {
			shopBtns: [
				{
					type: 'primary',
					txt: '添加店铺',
					disabled: true,
					click: self.shopAdd
				}, {
					type: 'danger',
					txt: '删除',
					disabled: true,
					click: self.shopDel
				}, {
					type: 'info',
					txt: '失效',
					disabled: true,
					click: self.shopInvalid
				}
			],
			shopCols: [
				{
					label: '店铺编码',
					prop: 'shop_code'
				}, {
					label: '店铺',
					prop: 'shop_name'
				}, {
					label: '店铺分组',
					prop: 'shop_group'
				}, {
					label: '店铺分类',
					prop: 'shop_category'
				}, {
					label: '所属公司',
					prop: 'shop_province'
				}, {
					label: '生效状态',
					prop: 'disable_status',
					formatter: self.shopStatusFormat
				}, {
					label: '失效时间',
					prop: 'disable_time',
					format: 'dataFormat1'
				}, {
					label: '失效人',
					prop: 'disable_person_name'
				}, {
					label: '店铺状态',
					prop: 'shop_status',
					formatter: prop => ({
						Y: '已审核',
						N: '未审核',
					}[prop] || prop),
				}, {
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 150,
				}
			],
			// 多选数据
			shopSelects: '',
			// 总记录数
			shopCount: 0,
			markListPmsShopVOData: [],
			shopSearch:{
				page_name: "pms_act_shop",
				where: [],
				page_size: self.pageSize,
				page_no: 1,
				act_id: ''
			},
		}
	},
	methods: {
		// 添加店铺
		shopAdd() {
			let params = {};
			let self = this;
			let p = Math.ceil(self.count/self.shopSearch.page_size)
			// 选择店铺,可以多选
			params.callback = d => {
				let dataSet = new Set(),
					hasDuplicate = [];
				self._getAllShopsSearchData.data.find(list => {
					dataSet.add(list.shop_id)
				})
				d.find(l => {
					if(!dataSet.has(l.shop_id)) {
						// 给该条店铺数据设置是生效参数，0为生效，1为失效 
						l.disable_status = 0
						l.shop_status = 'N'//新增默认店铺状态为未审核
						if(/^(已审核|重新审核)$/.test(self.form.status_desc)) l.isNew = 'Y'
						self.form.listPmsActShopVO.push(JSON.parse(JSON.stringify(l)))
					} else {
						hasDuplicate.push(l.shop_name)
					}
				})
				if(hasDuplicate.length) {
					let msg = hasDuplicate.join('、') + '店铺已添加';
					if(hasDuplicate.length>3) {
						let len = hasDuplicate.length;
						hasDuplicate.length = 3;
						msg = hasDuplicate.join('、') + '等' + len + '个店铺已添加';
					}
					self.$message.info(msg);
				}
			};
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/shop/list'),
				style:'width:800px;height:500px',
				title:'店铺列表'
			});
		},
		// 店铺失效
		shopInvalid() {
			let self = this,
				bool = false;
			if(self.shopSelects.length==0) {
				self.$message.error('请选择要失效的店铺')
				return
			}
			// 判断是否有新增没有保存的数据
			self.shopSelects.map((v) => {
				if (typeof v.act_shop_id === 'undefined' || v.act_shop_id === null || v.act_shop_id === '') {
					bool = true;
					self.$message.error('请保存后再进行失效操作');
					return;
				}
			});
			if(bool) {
				return;
			}
			// 变更所选数据的状态
			let isAllInvalid = true;
			self.shopSelects.map(v => {
				if(v.disable_status === 0) {
					v.disable_status = 1
					isAllInvalid = false;
					v.disable_time = +new Date();
					v.disable_person_name = Fn.getUserInfo('fullName');
				}
			})
			if(isAllInvalid) {
				this.$message.info('已经失效的店铺无需再次失效');
			} else {
				this.$message.success('失效成功')
			}
			// 清除选中状态
			this.$refs.ShopList.clearSelection()
		},
		// 删除店铺
		shopDel() {
			var self = this
			if(self.shopSelects.length==0) {
				self.$message.error('请选择要删除的活动店铺')
				return
			}else if (/^(已审核|重新审核)$/.test(this.form.status_desc) && this.shopSelects.some(obj => obj.shop_status === 'Y')){
				this.$message.error('不能删除已审核的实施店铺')
				return
			}
			// 获取要删除的shop_id set集合
			let delIdSet = new Set()
			self.shopSelects.find(d => {
				delIdSet.add(d.shop_id)
			})
			// 删除数据
			let i = self.form.listPmsActShopVO.length
			while(i--) {
				if(delIdSet.has(self.form.listPmsActShopVO[i].shop_id)) {
					self.form.listPmsActShopVO[i].act_shop_id && self._getAllShopsSearchData.del.push(self.form.listPmsActShopVO[i].act_shop_id)
					self.form.listPmsActShopVO.splice(i, 1)
				}
			}
		},
		shopPageSizeChange(size) {
			this.isListPmsShopChange()
			this.shopSearch.page_size = size;
			this.shopSearching()
		},
		shopPageChange(page_no) {
			this.isListPmsShopChange()
			this.shopSearch.page_no = page_no
			this.shopSearching()
		},
		shopSelectionChange(selectArr) {
			this.shopSelects = selectArr
		},
		shopStatusFormat(val){
			if(val == 0||val==undefined) {
				return "生效";
			}else{
				return "失效";
			}
		},
		shopPresearch(list, resolve) {
			this.isListPmsShopChange(resolve)
			this.shopSearch.where = list;
			this.shopSearching(resolve);
		},
		// 判断当前页的数据是否有修改
		isListPmsShopChange (resolve){
			if(this.compareData(this.form.listPmsActShopVO, this.markListPmsShopVOData)){
				resolve && resolve()
				this.$message.error('当前页有修改，请先保存！')
				throw('当前页有修改，请先保存！')
			}
		},
		// 保存时转换成所有数据
		_saveWithAllShops (data){
			var act_shop_id_list = {}


			data.forEach(obj => {
				if(obj.act_shop_id){
					act_shop_id_list[obj.act_shop_id] = obj
				}else {
					this._getAllShopsSearchData.data.push(obj)
				}
			})

			this._getAllShopsSearchData.data.forEach((obj, index) => {
				if(obj.act_shop_id){
					if (this._getAllShopsSearchData.del.indexOf(obj.act_shop_id) !== -1){
						this._getAllShopsSearchData.data[index] = null
					}else if(act_shop_id_list[obj.act_shop_id]){
						this._getAllShopsSearchData.data[index] = act_shop_id_list[obj.act_shop_id]
					}
				}
			})

			return this._getAllShopsSearchData.data.filter(Boolean).map(list => ({
				act_id: list.act_id,
				act_shop_id: list.act_shop_id,
				disable_person_id: list.disable_person_id,
				disable_person_name: list.disable_person_name,
				disable_status: list.disable_status,
				disable_time: +new Date(list.disable_time),
				marker: list.marker,
				shop_id: list.shop_id,
				isNew: list.isNew,
				shop_status: list.shop_status,
			}))
		},
		// 请求失败时，将goodsData里新增的对象对应的_getAllShopsSearchData.data也删除
		_saveWithAllShopsFailCallback (data){
			data.forEach(obj => {
				if(!obj.act_shop_id){
					this._getAllShopsSearchData.data.splice(this._getAllShopsSearchData.data.indexOf(obj), 1)
				}
			})
		},
		// 每一次刷新优惠商品时都要那一次全部数据，用于保存时(XPT-8565对接口bug的补丁)
		// 每次新增、删除、失效(修改行状态)都要修改this._getAllShopsSearchData.data
		_getAllShopsSearchData (cb){
			this.ajax.postStream('/price-web/api/price/pms/page/listActShop', Object.assign({}, this.shopSearch, {
				where:[],
				page_size: 10000,
				page_no:1,
			}), res => {
				this._getAllShopsSearchData.del = []
				if(res.body.result){
					this._getAllShopsSearchData.data = res.body.content.list || []
				}
				cb()
			}, () => {
				this._getAllShopsSearchData.del = []
				cb()
			})
		},
		shopSearching(resolve) {
			let _this = this

			this._getAllShopsSearchData(() => {
				this.ajax.postStream('/price-web/api/price/pms/page/listActShop'/*'/price-web/api/price/pms/page/listActShop?permissionCode=DISCOUNT_SHOP_QUERY'*/, _this.shopSearch,res => {
					if(res.body.result && res.body.content) {
						if(this.isAdd && this.params.actcopy_id){
							res.body.content.list = JSON.parse(JSON.stringify(this._getAllShopsSearchData.data))
							this._getAllShopsSearchData.data = []

							;(res.body.content.list || []).forEach(obj => {
								obj.disable_status 		= 0
								obj.act_id   			= null
								obj.marker   			= null
								obj.act_shop_id   		= null
								obj.disable_time   		= null
								obj.disable_person_id   = null
								obj.disable_person_name = null
							})
						}

						this.form.listPmsActShopVO = res.body.content.list || []
						this.markListPmsShopVOData = JSON.parse(JSON.stringify(res.body.content.list || []))
						this.shopCount = res.body.content.count || 0
						this.params.__data = JSON.parse(JSON.stringify(this.form))
					}
					resolve && resolve();
				}, err => {
					this.$message.error(err);
					resolve && resolve();
				})
			})
		},

		/*
		用于控制实施店铺按钮可操作状态
		
		创建、已撤回、驳回、重新审核：商品、店铺加、删、失效都可以
		提交审核：商品、店铺不允许任何编辑操作
		已审核：商品、店铺只允许失效操作
		
		店铺范围为店铺时可以操作

		*/
		shopsBtnControl() {
			let self = this,
				status = this.form.status_desc,			// 优惠状态
				if_global = this.form.if_global;		// 店铺范围,true为全局，false为店铺
			if(status === '创建' || status === '已撤回' || status === '重新审核' || status === '已审核') {
				this.shopBtns = [
					{
						type: 'primary',
						txt: '添加店铺',
						disabled: if_global ? true : false,
						click: self.shopAdd
					}, {
						type: 'danger',
						txt: '删除',
						disabled: if_global ? true : false,
						click: self.shopDel
					}, {
						type: 'info',
						txt: '失效',
						disabled: if_global ? true : false,
						click: self.shopInvalid
					}
				]
			} else if(status === '提交审核') {
				this.shopBtns = [
					{
						type: 'primary',
						txt: '添加店铺',
						disabled: true,
						click: self.shopAdd
					}, {
						type: 'danger',
						txt: '删除',
						disabled: true,
						click: self.shopDel
					}, {
						type: 'info',
						txt: '失效',
						disabled: true,
						click: self.shopInvalid
					}
				]
			}/* else if(status === '已审核') {
				this.shopBtns = [
					{
						type: 'primary',
						txt: '添加店铺',
						disabled: true,
						click: self.shopAdd
					}, {
						type: 'danger',
						txt: '删除',
						disabled: true,
						click: self.shopDel
					}, {
						type: 'info',
						txt: '失效',
						disabled: if_global ? true : false,
						click: self.shopInvalid
					}
				]
			}*/ else {
				this.shopBtns = [
					{
						type: 'primary',
						txt: '添加店铺',
						disabled: true,
						click: self.shopAdd
					}, {
						type: 'danger',
						txt: '删除',
						disabled: true,
						click: self.shopDel
					}, {
						type: 'info',
						txt: '失效',
						disabled: true,
						click: self.shopInvalid
					}
				]
			}
		},
	}
}