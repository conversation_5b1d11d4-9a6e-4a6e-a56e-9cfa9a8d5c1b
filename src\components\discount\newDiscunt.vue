<!-- 新优惠活动详情、新增 -->
<template>
	<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type="primary" size="mini" @click="presave()" :disabled='!(form.status == "CREATE"||form.status == "REJECTED" || (form.status == "APPROVED"))' :loading='saveStatus'>保存</el-button>
				<el-button size='mini' type='success' @click='submit' :disabled='!!isAdd || !(form.status == "CREATE"||form.status == "REJECTED") || form.if_change == "Y"' :loading='onAudit'>提交</el-button>
				<el-button size='mini' type='warning' @click='withdrawAudit' :disabled='form.status != "WAIT_APPROVED"'>撤回</el-button>
				<el-button size='mini' type='success' @click='openPlanActivity' :disabled='!(form.status == "FORBID" && form.if_plan_activity == "Y" && if_enable_time)'>启动预案活动</el-button>
				<el-button size='mini' type='success' @click='reflash' :disabled='isAdd || form.if_change == "Y"' :loading='onReflash'>刷新</el-button>
				<el-button size='mini' type='danger' @click='delOrder' :disabled='listBtnControl ||isAdd || form.if_change == "Y"' :loading='onFaile'>删除</el-button>
				<el-button size='mini' type='primary' @click='copyAdd' :disabled='form.document_type == "COPY" || isAdd'>复制新增</el-button>
        		<el-button size='mini' type='warning' @click='changing' :disabled='change_status_control' :loading='onFaile'>变更</el-button>
        		<el-button size='mini' type='warning' @click='exportSingleActDiscount' :disabled='export_status'>导出</el-button>
				<el-button size='mini' type='danger' @click='forbidden' :disabled='form.status != "APPROVED"|| !if_enable_time'>禁用</el-button>
				<el-button size='mini' type='primary' @click='expList'>中奖名单下载</el-button>
			</el-col>
		</el-row>
		<el-form :model='form' :rules='rules' ref='form' label-position="right" label-width="110px">
			<el-row class='xpt-flex__bottom'>
				<el-tabs v-model="firstTab" style="height: 335px;">
				<el-tab-pane label='基础信息' name='discountDetail' class='xpt-flex' style="overflow:auto;">
					<el-row>
					<el-col :span="6">
						<el-form-item label="优惠活动ID"  prop='discount_id'>
							<el-input v-model="form.discount_id" size='mini' disabled></el-input>
						</el-form-item>
						<el-form-item label="优惠活动编码"  prop='discount_no'>
							<el-input v-model="form.discount_no" size='mini' disabled></el-input>
						</el-form-item>
						<el-form-item label="状态"  prop='status'>
							<el-input v-model="status_option[form.status]" size='mini' disabled></el-input>
						</el-form-item>
						<el-form-item label="变更状态"  prop='status'>
							<el-input v-model="change_status_option[form.change_status]" size='mini' disabled></el-input>
						</el-form-item>
						<el-form-item label="优惠活动名称" prop='discount_name'>
							<el-input v-model="form.discount_name" size='mini' :maxlength="50" :disabled="formDIsabledControl2"></el-input>
							<el-tooltip v-if='rules.discount_name[0].isShow' class="item" effect="dark" :content="rules.discount_name[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item label="生效时间"  prop='enable_time'>
						<el-date-picker v-model="form.enable_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :disabled="formDIsabledControl2"  :picker-options='dateFormatFun' :editable="false" ></el-date-picker>
						<el-tooltip v-if='rules.enable_time[0].isShow' class="item" effect="dark" :content="rules.enable_time[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
						</el-form-item>
						<el-form-item label="失效时间" prop='disable_time'>
						<el-date-picker v-model="form.disable_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :disabled='formDIsabledControl2' :editable="false"  :picker-options='endDateFormatFun'></el-date-picker>
						<!-- <el-date-picker v-model="form.disable_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :disabled='!((form.status == "CREATE"  || form.status == "REJECTED"|| form.status == "RETRIAL"|| form.status == "APPROVED")&& form.document_type != "COPY")' :editable="false" :picker-options='iDateFormatFun'></el-date-picker> -->
						<el-tooltip v-if='rules.disable_time[0].isShow' class="item" effect="dark" :content="rules.disable_time[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
						</el-form-item>
						<el-form-item label="店铺范围" prop='store_area'>
							<el-select	size='mini' v-model="form.store_area" :disabled="formDIsabledControl" @change="shopsChanged">
									<el-option
										v-for="item in store_area_option"
										:key="item.value"
										:label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
						</el-form-item>
						<el-form-item label="商品活动类目" prop='goods_discount_category_name' >
							<xpt-input v-model='form.goods_discount_category_name'  size='mini'  icon="search" :disabled="!(/^(WITHDRAWED|CREATE)$/.test(form.status))" :on-icon-click="() => selectGoodsDiscountCategory('GOODS_DISCOUNT_CATEGORY')" readonly placeholder="请选择商品活动类目" @change='goodsDiscountCategoryChange("GOODS_DISCOUNT_CATEGORY")'></xpt-input>
						</el-form-item>
						<el-form-item label="线下活动分类" >
							<xpt-select-aux v-model='form.offline_discount_class' aux_name='offline_discount_class'  :disabled="!(/^(WITHDRAWED|CREATE|APPROVED)$/.test(form.status))" ></xpt-select-aux>
						</el-form-item>
						<el-form-item label="添加控制时长" prop='time_control_value' v-if="form.if_time_control == 'Y'">
							<xpt-input v-model='form.time_control_value'  size='mini'  :disabled=" form.status == 'APPROVED' ? form.discount_effect_area == 'BATCH' : formDIsabledControl" placeholder="请选择添加控制时长" ></xpt-input>
							<el-tooltip v-if='rules.time_control_value[0].isShow' class="item" effect="dark" :content="rules.time_control_value[0].message" placement="right-start" popper-class='xpt-form__error'>
								<i class='el-icon-warning'></i>
							</el-tooltip>
						</el-form-item>
						<!-- <el-form-item label="店铺分组类型" prop="store_group_type">
							<el-button size='mini' type='info' @click='openRroupList' :disabled='form.store_area != 2 ? true:false || formDIsabledControl'>添加分组</el-button>
						</el-form-item> -->
						</el-col>
						<el-col :span="6">
							<el-form-item label="优惠类型"  prop='discount_type'>
								<xpt-select-aux v-model='form.discount_type' aux_name='discountCategory' @change='selectParent' :disabled="!isAdd || formDIsabledControl || isCopyAdd"></xpt-select-aux>
								<el-tooltip v-if='rules.discount_type[0].isShow' class="item" effect="dark" :content="rules.discount_type[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="优惠子类型"  prop='discount_sub_type'>

								<el-select v-model="form.discount_sub_type" placeholder="请选择" size="mini"  @change="selectChild" :disabled='!isAdd || formDIsabledControl || !isChildClass||isCopyAdd'>
									<el-option
									v-for="item in childClassData"
									:key="item.code"
									:label="item.name"
									:value="item.code">
									</el-option>
								</el-select>
								<el-tooltip v-if='rules.discount_sub_type[0].isShow' class="item" effect="dark" :content="rules.discount_sub_type[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="应用场景"  prop='application_scenarios'>
								<xpt-select-aux v-model='form.application_scenarios' aux_name='application_scenarios' :disabled="true"></xpt-select-aux>
							</el-form-item>
							<!-- <el-form-item label="外部活动来源"  prop='external_activity_source'>
								<el-select
									size='mini'
									v-model="form.external_activity_source"
									:clearable="true"
									@clear="form.external_activity_source_id = ''"
									:disabled="formDIsabledControl"
								>
									<el-option label="天猫" value="TMALL"></el-option>
								</el-select>
							</el-form-item> -->
							<!-- <el-form-item label="外部活动ID" prop='external_activity_source_id'>
								<el-input v-model="form.external_activity_source_id" size='mini' :disabled="form.external_activity_source!='TMALL' || formDIsabledControl"></el-input>
								<el-tooltip v-if='rules.external_activity_source_id[0].isShow' class="item" effect="dark" :content="rules.external_activity_source_id[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item> -->
							<el-form-item label="优惠条件" prop='discount_condition_type' >
								<xpt-select-aux v-model='form.discount_condition_type' aux_name='discount_condition' @change="discountConditionChange" :disabled=" !isAdd || formDIsabledControl || form.discount_category === 'FREE_DEPOSIT'"></xpt-select-aux>
							</el-form-item>
							<el-form-item label="叠加优惠">
								<el-switch v-model="form.if_superposition_discount" on-text="是" off-text="否" on-value="Y" off-value="N" :disabled="!(isAdd || !formDIsabledControl) || (['FULL_PIECE','MULTI_PIECE'].includes(form.discount_condition_type) && form.discount_effect_area == 'LINE')" @change='superpositionDiscount'></el-switch>
							</el-form-item>
							<el-form-item label="每满">
								<el-switch v-model="form.if_each_full" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled="formDIsabledControl || form.discount_condition_type == 'UNCONDITIONAL' ||form.if_superposition_discount != 'Y' || ifEachFullDisabled || ['ORDER_PRESENT', 'PAID_PRESENT'].includes(form.discount_kind)" @change='clearCondition'></el-switch>
							</el-form-item>

							<el-form-item label="活动区间" prop='act_section' >
								<xpt-select-aux v-model='form.act_section' aux_name='act_aection' :disabled="formDIsabledControl2" @change="actSectuibChange"></xpt-select-aux>
								<el-tooltip v-if='rules.act_section[0].isShow' class="item" effect="dark" :content="rules.act_section[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="时间维度" prop='discount_affection_date' >
								<xpt-select-aux v-model='form.discount_affection_date' aux_name='discountAffection' :disabled="formDIsabledControl2"></xpt-select-aux>
								<el-tooltip v-if='rules.discount_affection_date[0].isShow' class="item" effect="dark" :content="rules.discount_affection_date[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="预售付款阶段" v-show="form.presell_act=='Y'&&form.discount_affection_date=='PAYOFF_DATE'">
									<el-select size='mini' 	v-model="form.presell_pay_stage"  :disabled="formDIsabledControl">
										<el-option
											v-for="item in presell_pay_stage_opiton"
											:key="item.value"
											:label="item.label"
											:value="item.value"
											>
										</el-option>
									</el-select>
							</el-form-item>
							<el-form-item label="预售优惠" >
								<el-switch v-model="form.presell_act" on-text="是" off-text="否"  on-value="Y" off-value="N" :disabled="formDIsabledControl"></el-switch>

							</el-form-item>
							<el-form-item label="添加时效控制" prop='if_time_control' >
								<el-switch v-model="form.if_time_control" on-text="是" off-text="否"  on-value="Y" off-value="N" :disabled="form.status == 'APPROVED' ? form.discount_effect_area == 'BATCH' : formDIsabledControl || form.discount_effect_area == 'BATCH'"></el-switch>
							</el-form-item>
              <el-form-item label="上架派券中心" prop='if_sync_coupon' >
								<el-switch v-model="form.if_sync_coupon" on-text="是" off-text="否"  on-value="Y" off-value="N" :disabled="!canIChangeSyncCoupon"></el-switch>
							</el-form-item>
						</el-col>
						<el-col :span="6">


							<el-form-item label="维护中奖名单" >
								<el-switch v-model="form.if_act_winning" on-text="是" off-text="否" on-value="Y" off-value="N" @change="winningChange"  :disabled=" formDIsabledControl4"></el-switch>
							</el-form-item>
							<el-form-item label="自动出中奖名单" v-show="form.if_act_winning == 'Y'">
								<el-switch v-model="form.if_auto_win_list" on-text="是" off-text="否" on-value="Y" off-value="N"  @change="autoWinningChange" :disabled=" formDIsabledControl4||!form.discount_id"></el-switch>
							</el-form-item>
							<el-form-item label="中奖名额"  v-show="form.if_auto_win_list == 'Y'" prop='win_quota' >
								<el-input v-model="form.win_quota" size='mini' type="number" :disabled="formDIsabledControl"
								@change="e=>{
									const result = fitterPrice2(form.win_quota) ? form.win_quota:'';
									$nextTick(() =>{

										$set(form, 'win_quota',result)
									});
									}"
								></el-input>
								<el-tooltip v-if='rules.win_quota[0].isShow' class="item" effect="dark" :content="rules.win_quota[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="活动规则" prop='win_rule_code'   v-show="form.if_auto_win_list == 'Y'">
									<el-select size='mini' 	v-model="form.win_rule_code" @change="winRuleChange" :disabled="formDIsabledControl">
										<el-option
											v-for="item in win_rule_code_opiton"
											:key="item.id"
											:label="item.rule_name"
											:value="item.rule_code"
											>
										</el-option>
									</el-select>
									<el-tooltip v-if='rules.win_rule_code[0].isShow' class="item" effect="dark" :content="rules.win_rule_code[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="规则链接ID" v-if="form.win_rule_code == 'D'" prop='win_goods_id'  >
								<el-input v-model="form.win_goods_id" size='mini'  :disabled="formDIsabledControl"></el-input>
								<el-tooltip v-if='rules.win_goods_id[0].isShow' class="item" effect="dark"  :content="rules.win_goods_id[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="订单多地址校验" >
								<el-switch v-model="form.if_order_multiple_address" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled="formDIsabledControl2||form.if_act_winning=='N'"></el-switch>
							</el-form-item>
							<el-form-item label="优惠性质">
									<el-select size='mini' 	v-model="form.discount_kind" :disabled="formDIsabledControl2">
										<el-option
											v-for="item in discount_kind_opiton"
											:key="item.value"
											:label="item.label"
											:value="item.value"
											:disabled="item.disabled"
											>
										</el-option>
									</el-select>
							</el-form-item>
							<el-form-item label="优惠类别" prop="discount_category">
								<el-select v-model="form.discount_category" placeholder="请选择" size="mini" :disabled='formDIsabledControl' @change="discountCategoryChange">
									<el-option
									v-for="item in discountCategorydata"
									:key="item.code"
									:label="item.name"
									:value="item.code">
									</el-option>
								</el-select>
								<el-tooltip v-if='rules.discount_category[0].isShow' class="item" effect="dark" :content="rules.discount_category[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="线下货款模式" prop="offline_payment_mode_name">
								<xpt-input v-model='form.offline_payment_mode_name'  size='mini'  icon="search" :disabled="listBtnControl" :on-icon-click="() => selectGoodsDiscountCategory('OFFLINE_PAYMENT_MODE')" readonly placeholder="请选择线下货款模式" @change='goodsDiscountCategoryChange("OFFLINE_PAYMENT_MODE")'></xpt-input>
								<el-tooltip v-if='rules.offline_payment_mode_name[0].isShow' class="item" effect="dark" :content="rules.offline_payment_mode_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="网拍订单店铺">
								<xpt-input v-model='form.wangpai_order_shop_name'  size='mini'  icon="search" :disabled="listBtnControl" :on-icon-click="() => selectGoodsDiscountCategory('WANGPAI_ORDER_SHOP')" readonly placeholder="请选择网拍订单店铺" @change='goodsDiscountCategoryChange("WANGPAI_ORDER_SHOP")'></xpt-input>
							</el-form-item>
							<el-form-item label="用户类别" prop='use_category' >
								<el-select size='mini' 	v-model="form.use_category" :disabled="true">
										<el-option label="用户" value="USER"></el-option>
										<el-option label="系统" value="SYSTEM"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="优惠影响范围" prop='discount_effect_area'>
								<el-select size='mini' 	v-model="form.discount_effect_area" :disabled="isAdd ? !form.discount_category || ifBeforeCreateGoods : formDIsabledControl3 || ifBeforeCreateGoods">
									<el-option
									v-for="item in discount_effect_area_option"
									:key="item.value"
									:label="item.label"
									:value="item.value"
									:disabled="item.disabled"
									>
									</el-option>
									</el-select>
							</el-form-item>
							<el-form-item label="经销结算比例" prop='dealer_discount'>
								<el-input v-model="form.dealer_discount" size='mini' :disabled="/(NEW_GIFT)/.test(form.discount_type) ? true : formDIsabledControl" type="number"  @change.native='e=>{
						form.dealer_discount = fitterPrice3(form.dealer_discount)
						}'></el-input>%

							</el-form-item>
							<el-form-item label="备注" >
							<el-input type="textarea" v-model="form.remark" :maxlength="2000"  :autosize="{ minRows: 2, maxRows: 4}" size='mini' style="width:100%; min-width:240px;min-height: 48px;" :disabled="formDIsabledControl2"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="提前购">
								<el-switch v-model="form.if_in_advance_discount" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled="true"></el-switch>
							</el-form-item>
							<el-form-item label="预案活动" >
								<el-switch v-model="form.if_plan_activity" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled="formDIsabledControl"></el-switch>
							</el-form-item>
							<el-form-item label="可选" >
								<el-switch v-model="form.if_choose" on-text="是" off-text="否"  on-value="Y" off-value="N" :disabled=" formDIsabledControl"></el-switch>
							</el-form-item>
							<el-form-item label="需要审核" >
								<el-switch v-model="form.if_need_audit" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled=" formDIsabledControl ||need_audit_control" @change='shopsBtnControl'></el-switch>
							</el-form-item>
							<el-form-item label="影响售价" >
								<el-switch v-model="form.if_effect_saleprice" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled=" formDIsabledControl||effect_saleprice_control" @change='shopsBtnControl'></el-switch>
							</el-form-item>
							<el-form-item label="金额可变" >
								<el-switch v-model="form.if_amount_change" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled="formDIsabledControl || amount_change_control" @change='shopsBtnControl'></el-switch>
							</el-form-item>
							<el-form-item label="定制" >
								<el-switch v-model="form.customization" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled=" formDIsabledControl" @change='shopsBtnControl'></el-switch>
							</el-form-item>
							<el-form-item label="自动添加" >
								<el-switch v-model="form.if_auto_add" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled=" formDIsabledControl ||auto_add_control || isAddAuto" @change="changeIfAutoAdd"></el-switch>
							</el-form-item>
							<el-form-item label="线下前置" >
								<el-switch v-model="form.if_offline_before" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled=" formDIsabledControl || isOfflineBefore || ifReduceShots" @change="ChangeOfflineBefore"></el-switch>
							</el-form-item>
							<el-form-item label="限制时效" >
								<el-switch v-model="form.time_limit_share" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled="true|| formDIsabledControl ||auto_add_control" @change='shopsBtnControl'></el-switch>
							</el-form-item>
							<el-form-item label="仅享用一次" >
								<el-switch v-model="form.if_use_once" on-text="是" off-text="否" on-value="Y" off-value="N"  :disabled=" formDIsabledControl ||auto_add_control" @change='shopsBtnControl'></el-switch>
							</el-form-item>
						</el-col>
						<!-- <el-col :span="24">
							<el-form-item label=" 互斥活动编码" prop='exclusive_discount_no' >
								<xpt-input v-model='form.exclusive_discount_no'  size='mini' style="width:100%;"  :disabled="!(/^(WITHDRAWED|CREATE)$/.test(form.status))" placeholder="请选择互斥活动编码"></xpt-input>
							</el-form-item>
						</el-col> -->

					</el-row>
				</el-tab-pane>
				<el-tab-pane label='优惠条件' name='moreSet' class='xpt-flex'>
					<el-row  style="height:220px">
						<xpt-list2
							ref='conditionList'
							:data='conditionVo'
							:btns='conditionBtns'
							:colData='conditionCol'
							:orderNo = true
							selection='radio'
							@radio-change='conditionRadioChange'
						>
							<template slot='item_enable_options' slot-scope='scope'>
								<!-- <el-select size='mini' v-model='scope.row.item_enable_options' :disabled="(!!scope.row.discount_condition_id && scope.row.change_type != 'ADD')" -->
								<!-- <el-select size='mini' v-model='scope.row.item_enable_options' :disabled=" (!!scope.row.discount_condition_id && !(scope.row.change_type == 'ADD' || scope.row.change_type == '')) || !(form.status == 'CREATE'||form.status == 'REJECTED')" -->
								<el-select size='mini' v-model='scope.row.item_enable_options' :disabled=" !((form.status == 'CREATE'||form.status == 'REJECTED')&&scope.row.row_status ==0)"
								@change="changeSelect(scope)"
									style='width: 100%'>
									<el-option v-for='(item,index) in item_enable_options'
										:key='index' :value='item.value' :label='item.label' :disabled='item.disabled()'></el-option>
								</el-select>
						</template>

						<template slot='condition_order_config' slot-scope='scope'>
							<el-input v-model="scope.row.condition_order_config"  size='mini' style="width:100%;" type="number" :disabled="!((form.status == 'CREATE'||form.status == 'REJECTED')&&scope.row.row_status ==0&&roleConfig.if_condition_order=='Y')"  @change="e=>{
									const result = fitterPrice2(scope.row.condition_order_config) ? scope.row.condition_order_config:'';

									$nextTick(() =>{
										$set(scope.row, 'condition_order_config',parseInt(result))
									});

								}"></el-input>
						</template>
						<template slot='threshold_count' slot-scope='scope'>
							<el-input v-model="scope.row.threshold_count"  size='mini' style="width:100%;" :disabled="!((form.status == 'CREATE'||form.status == 'REJECTED')&&scope.row.row_status ==0)"  @change="e=>{
									const result = fitterPrice2(scope.row.threshold_count) ? scope.row.threshold_count:'';

									$nextTick(() =>{
										$set(scope.row, 'threshold_count',result)
									});

								}"></el-input>
								<!-- <el-input type="number" v-model="scope.row.threshold_count"  size='mini' style="width:100%;" ></el-input> -->
						</template>
						<template slot='houly_config' v-if="roleConfig.if_zhengdian=='Y'" slot-scope='scope'>
							<el-date-picker
								v-model="scope.row.houly_config"
								type="datetime"
								placeholder="选择日期"
								:editable='false'
              					:picker-options='houlyDateOptions'
								:disabled="!((form.status == 'CREATE'||form.status == 'REJECTED')&&scope.row.row_status ==0)" size='mini' style="width:100%;"
								format="yyyy-MM-dd HH:00:00">
								</el-date-picker>
						</template>
						<template slot='item_enable_count' slot-scope='scope'>
							<el-input type="text" v-model="scope.row.item_enable_count" :disabled="!(scope.row.item_enable_options == 'OPTIONAL') || !((form.status == 'CREATE'||form.status == 'REJECTED')&&scope.row.row_status ==0)" size='mini' style="width:100%;"  @blur="changeNum(scope)"></el-input>
						</template>
						<template slot='threshold_price' slot-scope='scope'>
							<el-input v-model="scope.row.threshold_price"  size='mini' style="width:100%;" :disabled="!((form.status == 'CREATE'||form.status == 'REJECTED')&&scope.row.row_status ==0)" @change="e=>{
									const result = fitterPrice(scope.row.threshold_price) ? scope.row.threshold_price:'';
									$nextTick(() =>{

										$set(scope.row, 'threshold_price',result)
									});



							}"></el-input>
						</template>
						<template slot='discount_id' slot-scope='scope'>
							<el-button type='primary' size='mini' @click="addNewOrder(scope.row)" :disabled="!(scope.row.discount_condition_id  &&(form.status == 'CREATE'||form.status == 'REJECTED' )) || scope.row.change_type == 'CANCEL'">添加优惠项目</el-button>
							<el-button type='primary' size='mini' @click="addGoods('',scope.row)" :disabled="form.status == 'APPROVED' ? (scope.row.row_status == 0 ? false : true) : (!(scope.row.discount_condition_id  &&(form.status == 'CREATE'||form.status == 'REJECTED' )) || scope.row.change_type == 'CANCEL')">添加优惠清单</el-button>
						</template>
						</xpt-list2>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label='优惠清单' name='discountList' class='xpt-flex discount-list'>
					<el-row>
						<xpt-list2
						:data='DiscountVo'
						:btns='DiscountBtns'
						:colData='DiscountCols'
						:taggelClassName='tableRowClassName'
						:pageTotal='goodCount'
						:searchPage='goodsSearch.page_name'
						@selection-change='discountselectionChange'
						@search-click='martialPresearch'
						@page-size-change='goodsPageSizeChange'
						@current-page-change='goodsPageChange'
						>
						<template slot='win_goods_id' slot-scope='scope'>
							<el-input v-model="scope.row.win_goods_id"  size='mini' style="width:100%;" :maxlength="200" :disabled='!(form.status == "CREATE" || form.status == "REJECTED"||(form.status=="APPROVED"&&!scope.row.discount_material_list_id))' @change="e=>{
									const result = fitterPrice5(scope.row.win_goods_id) ? scope.row.win_goods_id:'';
									$nextTick(() =>{

										$set(scope.row, 'win_goods_id',result)
									});



							}"></el-input>
						</template>
						<xpt-import slot='btns' :taskUrl="uploadUrl" :callback="uploadCallback" class='mgl10' :isupload="!ifApprovedOfStatus" :text="'导入补充清单'"></xpt-import>
						</xpt-list2>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label='编制信息' name='organizaMsg' class='xpt-flex'>
					<el-row>
						<el-col :span="8">
							<el-form-item label="编制人" >
								<el-input v-model="form.creator_name" size='mini' disabled></el-input>
							</el-form-item>

							<el-form-item label="编制时间" >
								<el-date-picker v-model="form.create_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :disabled="true"></el-date-picker>
								<!-- <el-input v-model="form.create_time" size='mini' disabled></el-input> -->
							</el-form-item>
							<el-form-item label="审核人" >
								<el-input v-model="form.audit_person_name" size='mini' disabled></el-input>
							</el-form-item>
							<el-form-item label="审核时间" >
								<el-date-picker v-model="form.audit_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :disabled="true"></el-date-picker>
								<!-- <el-input v-model="form.audit_time" size='mini' disabled></el-input> -->
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="创建人" >
								<el-input v-model="form.creator_name" size='mini' disabled></el-input>
							</el-form-item>

							<el-form-item label="创建时间" >
								<el-date-picker v-model="form.create_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :disabled="true"></el-date-picker>
								<!-- <el-input v-model="form.create_time" size='mini' disabled></el-input> -->
							</el-form-item>
							<el-form-item label="最后更新人" >
								<el-input v-model="form.last_modifier_name" size='mini' disabled></el-input>
							</el-form-item>
							<el-form-item label="最后更新时间" >
								<!-- <el-input v-model="form.modify_time" size='mini' disabled></el-input> -->
								<el-date-picker v-model="form.last_modify_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :disabled="true"></el-date-picker>
							</el-form-item>
						</el-col>
							<el-col :span="8">
							<el-form-item label="提交人" >
								<el-input v-model="form.submit_user_name" size='mini' disabled></el-input>
							</el-form-item>
							<el-form-item label="提交时间" >
								<el-date-picker v-model="form.submit_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :disabled="true"></el-date-picker>
								<!-- <el-input v-model="form.submit_time" size='mini' disabled></el-input> -->
							</el-form-item>
						</el-col>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label='操作纪录' name='operation' class='xpt-flex'>
					<xpt-list
					:data='operaterList'
					:colData='operaterCols'
					:showHead='false'
					:pageTotal='operaterCount'
					selection=''
				></xpt-list>
				</el-tab-pane>
				<el-tab-pane label='附加设置' name='additional' class='xpt-flex'>
					<el-row class="xpt-top" :gutter="40">
						<el-col :span="24">
							<el-button type="danger" size="mini" @click="actSectuibChange" :disabled='formDIsabledControl || !form.additional_act_content' :loading='false'>取消选中</el-button>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="6">
							<el-form-item>
								<el-radio v-model="form.additional_act_content" label="FINALPAYACT" :disabled="formDIsabledControl " @change.native='changeActContent'>尾款订单活动</el-radio>
							</el-form-item>
							<el-form-item>
								<el-radio v-model="form.additional_act_content" label="GIFTORDERACT" :disabled="formDIsabledControl " @change.native='changeActContent'>礼品订单活动</el-radio>
							</el-form-item>
							<el-form-item>
								<el-radio v-model="form.additional_act_content" label="AUTHOR_ACTIVITY" :disabled="formDIsabledControl" @change.native='changeActContent'>作者账号活动</el-radio>
							</el-form-item>
              <el-form-item>
								<el-radio v-model="form.additional_act_content" label="DY_ORDER_COUPONS" :disabled="formDIsabledControl" @change.native='changeActContent'>抖音团购活动</el-radio>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="支付生效时间" >
							<el-date-picker v-model="form.additional_start_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;"  :editable="false"  :disabled="!(form.additional_act_content == 'FINALPAYACT') || formDIsabledControl"  :picker-options='dateFormatFun'></el-date-picker>
								<!-- <el-tooltip v-if='rules.additional_start_time[0].isShow' class="item" effect="dark" :content="rules.additional_start_time[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip> -->
							</el-form-item>
							<el-form-item label="商品标识" >
								<el-input v-model="form.additional_flag" size='mini' :disabled="!(form.additional_act_content == 'GIFTORDERACT') || formDIsabledControl" ></el-input>
								<el-tooltip v-if='rules.additional_flag[0].isShow' class="item" effect="dark" :content="rules.additional_flag[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="作者活动类型" >
								<el-select	size='mini' v-model="form.additional_flag_copy" :disabled="!(form.additional_act_content == 'AUTHOR_ACTIVITY') || formDIsabledControl">
									<el-option
										v-for="item in author_activity_option"
										:key="item.value"
										:label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>
              <el-form-item label="抖音商品ID" >
                <el-input v-model="form.additional_flag2" size='mini' :disabled="!(form.additional_act_content == 'DY_ORDER_COUPONS') || formDIsabledControl" ></el-input>
                <el-tooltip v-if='rules.additional_flag2[0].isShow' class="item" effect="dark" :content="rules.additional_flag[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
                </el-tooltip>
              </el-form-item>
						</el-col>
						<el-col :span="6" >
							<el-form-item label="支付失效时间" >
							<el-date-picker v-model="form.additional_end_time" type="datetime" placeholder="选择时间" size="mini" style="width:180px;" :editable="false"  :disabled="!(form.additional_act_content == 'FINALPAYACT') || formDIsabledControl"  :picker-options='iDateFormatFun'></el-date-picker>
							</el-form-item>
							<!-- <el-tooltip v-if='rules.additional_end_time[0].isShow' class="item" effect="dark" :content="rules.additional_end_time[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip> -->
                <el-form-item label="" ></el-form-item>
                <el-form-item label="" ></el-form-item>
              <el-form-item label="抖音商品标题" >
								<el-input v-model="form.additional_expand" size='mini' :disabled="!(form.additional_act_content == 'DY_ORDER_COUPONS') || formDIsabledControl2" ></el-input>
								<el-tooltip v-if='rules.additional_expand[0].isShow' class="item" effect="dark" :content="rules.additional_expand[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>
					</el-row>
				</el-tab-pane>
				<!-- <el-tab-pane label='优惠配置' name='config' class='xpt-flex'>
					<xpt-list
					ref='configList'
					:btns='configBtns'
					:data='configList'
					:colData='configCols'
					:showHead='false'
					selection='checkbox'
					@selection-change='discountselectionChange'
				></xpt-list>
				</el-tab-pane> -->
				</el-tabs>
			</el-row>
		</el-form>
		<el-row class='xpt-flex__bottom'>
			<el-tabs v-model="secondTab">
				<el-tab-pane label='优惠项目' name="discount_GoodsList" class='xpt-flex xpt-flex__bottom'>
					<xpt-list2
						ref='discount_GoodsList'
						:data='listActDiscountItemVo'
						:btns='discountItemBtns'
						:colData='discountItemCols'
						:taggelClassName='tableRowClassName'
						:searchPage='itemSearch.page_name'
						:pageTotal='itemCount'
						@search-click='itemPresearch'
						@page-size-change='itemPageSizeChange'
						@current-page-change='itemPageChange'
						@row-click="itemClick"
						@selection-change='itemSelectionChange'
					>
					<template slot='discount_condition_id' slot-scope='scope'>
						<!-- <el-switch v-model="scope.row.if_enjoy_discount" on-text="是" off-text="否" on-value="Y" off-value="N"></el-switch> -->
						<el-button size='mini' type='success' v-model="scope.row.discount_condition_id"  @click='checkConditionItem(scope.row)' v-if='!(scope.row.discount_item_type == "ITEM_TYPE_DEDUCTE_AMOUNT"||scope.row.discount_item_type == "ITEM_TYPE_DISCOUNT")' :loading='onAudit'>查看</el-button>

					</template>
					<template slot='win_order' slot-scope='scope'>
							<el-input v-model="scope.row.win_order" type="number" :disabled="form.if_auto_win_list ==='N' ||formDIsabledControl"  size='mini' style="width:100%;"  @change="e=>{
									const result = fitterPrice2(scope.row.win_order) ? scope.row.win_order:'';
									$nextTick(() =>{
										$set(scope.row, 'win_order',result)
									});

								}"></el-input>
					</template>
					<template slot='win_quota' slot-scope='scope'>
						<el-input v-model="scope.row.win_quota" type="number"  size='mini' style="width:100%;" :min="scope.row.discount_item_id?initialItem[listActDiscountItemVo.indexOf(scope.row)].win_quota:0" :disabled="form.if_auto_win_list ==='N' ||!( form.status == 'CREATE' || form.status == 'REJECTED' || (form.status == 'APPROVED' && !!scope.row.if_last_order_in_condition))"  @blur="e=>{

								const result = checkWinQuota(scope.row);
									$nextTick(() =>{
										$set(scope.row, 'win_quota',result)
									});


							}"></el-input>
					</template>
					</xpt-list2>
				</el-tab-pane>
				<el-tab-pane label='实施店铺' name="discount_shopList" class='xpt-flex'>
					<xpt-list2
						ref='ShopList'
						:data='listPmsActShopVO'
						:btns='shopBtns'
						:colData='shopCols'
						searchPage='act_discount_shop'
						:pageTotal='shopCount'
						@search-click='shopPresearch'
						@selection-change='shopSelectionChange'
						@page-size-change='shopPageSizeChange'
						@current-page-change='shopPageChange'
					>
					<span slot='btns'>
						<xpt-import :taskUrl='implementStoreUploadUrl'  :text="'批量导入'" :otherParams="{discount_no:form.discount_no}" :isupload ="!((form.status === 'CREATE'|| form.status === 'APPROVED')&& form.store_area===0&& !judgeNullStr(form.discount_id))" class='mgl10'></xpt-import>
					</span>
					</xpt-list2>
				</el-tab-pane>
				<el-tab-pane label='作者账号' name="discount_authorList" class='xpt-flex'>
					<xpt-list2
						ref='authorList'
						:data='listPmsActAuthorVO'
						:btns='authorBtns'
						:colData='authorCols'
						:searchPage='authorSearch.page_name'
						:pageTotal='authorCount'
						@search-click='authorPresearch'
						@selection-change='authorSelectionChange'
						@page-size-change='authorPageSizeChange'
						@current-page-change='authorPageChange'
					></xpt-list2>
				</el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
// import staticData from '@common/staticData.js'
import VL from '@common/validate.js'
import Fn from '@common/Fn.js'
// import goods from './model/new_goods.js'
import shop from './new_model/shop.js'
import condition from './new_model/condition.js'
import conditionItem from './new_model/conditionItem.js'
import changing from './model/changing.js'
import goods from './new_model/goods.js'
import author from './new_model/author.js'
// import config from './new_model/config.js'
import Vue from 'vue'
import { forEach } from 'jszip'
import { t } from 'vxe-table'
export default {
props:['params'],
	mixins: [ shop,changing,conditionItem,condition,goods, author],
	data(){
		var self = this;
		return{
			implementStoreUploadUrl: '/price-web/api/actDiscount/addImportActDiscountShop',
            loadingNoClearData:false,
			roleConfig:{
				if_zhengdian:'',
				if_condition_order:'',
			},
			ifBeforeCreateGoods: false,
			insetDiscountObj: {},
			backup:{
				discount_sub_type:'',
				discount_type:'',
				disable_time:'',
				store_area:0
			},
			initialData:{},
			initialCondition:[],
			initialItem:[],
			initialMaterial:[],
			initialShop:[],
			houlyDateOptions:{
				date:(function(){
					let date = new Date();
					let year = date.getFullYear();
					let month = date.getMonth() + 1;
					let day = date.getDate();
					let hours = date.getHours() + 1;

					let time = year + "-" + month + "-" + day + " " +hours+ ":00:00";
					return new Date(time);
				})(),
				disabledDate(time) {

					return !(time.getTime() > (new Date(self.form.enable_time).getTime()-1000*60*60*24)&&time.getTime() < new Date(self.form.disable_time).getTime());

				},
			},
			status_option:{
				CREATE:"创建",
				SUBMITTED:"提交审核",
				APPROVED:"已审核",
				RETRIAL:"重新审核",
				CANCELED:"已作废",
				APPROVING:'审核中',
				WITHDRAWED:"已撤回",
				LOCKED:"锁定",
				INVALID :"已作废",
				WAIT_APPROVED:'待审核',
				REJECTED:'驳回',
				FORBID:'禁用'
			},
			change_status_option:{
				CHANGE_CREATE: '变更创建',
				CHANGE_WAIT_APPROVED:  '变更待审核',
				CHANGE_APPROVING: '变更审核中',
				CHANGE_REJECTED:'变更驳回',
				CHANGE_APPROVED: '变更已审核',
			},

			// 自动添加
			auto_add_control:false,
			// 自动审核
			need_audit_control:false,
			// 影响售价
			effect_saleprice_control:false,
			// 金额可变
			amount_change_control:false,
			firstTab:'discountDetail',
			secondTab:'discount_GoodsList',
			// 当前操作是新增还是编辑，true为新增，false为编辑
			isAdd: true,
			// 复制新增时优惠子类型第一次渲染不加载
			copyAddSubFlag:false,
      		//复制新增
			isCopyAdd: false,
			// 变更
			isChangingAdd:false,
			// 还有子分类
			isChildClass: false,
			conditionSelect:null,
			discountItemSelect:null,
			changing_conditionVo:[],
			// 优惠类型
			// discountCategory:_AUX.get('discountCategory'),
			//优惠类型,当为编辑状态时获取所有列表，当为新增状态时获取生效列表
			// disClassData: this.isAdd ? __AUX.getValidData('discountCategory') : __AUX.get('discountCategory'),
			// 所有子分类
			childClassDataAll: this.isAdd && !this.isCopyAdd  ? __AUX.getValidData('discountSubclass') : __AUX.get('discountSubclass'),
			// 当前可以选子分类
			childClassData: this.isAdd && !this.isCopyAdd ? [] : __AUX.get('discountSubclass'),
			discountCategorydataAll: this.isAdd && !this.isCopyAdd  ? __AUX.getValidData('dicount_category_match') : __AUX.get('dicount_category_match'),
			discountCategorydata: this.isAdd && !this.isCopyAdd  ? [] : __AUX.get('dicount_category_match'),
			DiscountVo:[],
			listPmsActShopVO:[],
			conditionVo:[],
			delConditionVo:[],
			cancelConditionVo:[],

			delListActDiscountItemVo:[],
			cancelListActDiscountItemVo:[],
			listActDiscountItemVo:[],
			export_status:false,
			form:{
				// 预售游湖
				presell_act: 'N',
				if_time_control:"N",
				if_sync_coupon:"N",
				time_control_value:"",
				// 单据类型
				document_type:'NORMAL',
				//优惠活动id
				discount_id:'',
				//优惠编号
				discount_no:'',
				//优惠活动名称
				discount_name:'',
				//活动状态
				status:'CREATE',
				//生效时间
				enable_time:'',
				//失效时间
				disable_time:'',
				//优惠类型
				discount_type:'',
				offline_discount_class:'',
				//优惠子类型
				discount_sub_type:'',
				//外部
				external_activity_source:'',
				//外部活动id
				external_activity_source_id:'',
				//优惠条件类型
				discount_condition_type:'FULL_YUAN',
				//叠加优惠
				if_superposition_discount:'N',
				//每满
				if_each_full:'N',
				//自动添加
				if_auto_add:'N',
				//前置优惠
				if_offline_before:'N',
				if_choose:'Y',
				//需要审核
				if_need_audit:'Y',
				//影响售价
				if_effect_saleprice:'N',
				//金额可变
				if_amount_change:'N',
				//预案活动
				if_plan_activity:'N',
				// 优惠性质
				discount_kind:'',
				// 提前购
				if_in_advance_discount:'N',
				// 定制
				customization:'N',
				time_limit_share:'Y',
				if_use_once: '', //仅享用一次

				//项目类型
				item_type:'',
				// 版本号码
				version_number:null,

				//优惠影响范围
				discount_effect_area:'',
				//店铺范围
				store_area:1,
				//店铺分组类型
				store_group_type:[],
				//备注
				remark:'',
				dealer_discount:'',
				//审核时间
				audit_time:'',
				//审核人名称
				audit_person_name:'',
				//审核人id
				audit_peerson_id:'',
				//创建人
				creator:'',
				//创建人姓名
				creator_name:'',
				//创建时间
				create_time:'',
				//最后修改人
				last_modifier:'',
				//最后修改者姓名
				last_modifier_name:'',
				//最后修改时间
				last_modify_time:'',
				// 时间维度
				discount_affection_date:'ORDER_DATE',
				// 活动区间
				act_section:'INTERVAL_ACT',
				// 用户类别
				use_category:'USER',
				// 提交时间
				submit_time:'',
				// 提交人名称
				submit_user_name:'',
				// 提交人id
				submit_user_id:'',
				// 是否变更
				if_change:'N',
				additional_act_content:'',
				additional_start_time:'',
				additional_end_time:'',
				additional_flag:'',
				application_scenarios:'',//应用场景
				if_act_winning:'',
				if_auto_win_list:'N',
				presell_pay_stage:'TAIL_PARA',
				win_quota:'',
				win_rule_code:'',
				win_goods_id:'',
				if_order_multiple_address:'',
				goods_discount_category_name: '',
				goods_discount_category: '',
				exclusive_discount_no:"",//互斥活动
				discount_category: '', //优惠类别
				offline_payment_mode_name: '不限', //线下货款模式
				wangpai_order_shop_name:'',
				offline_payment_mode: 'EMPTY', //线下货款模式
        additional_expand: '',
        additional_flag2: ''
			},

			store_area_option:[{
					label: '店铺',
					value: 0,
				},{
					label: '全局',
					value: 1,
				},
				// {
				// 	label: '分组',
				// 	value: 2,
				// }
				],
			// discount_kind_opiton:{
			// 	ORDER_CUT: '拍减',
			// 	ORDER_PRESENT:  '拍赠',
			// 	PAID_RETURN: '付返',
			// 	PAID_PRESENT:'付赠',
			// },
			win_rule_code_opiton:[],
			presell_pay_stage_opiton:[{
					label: '首款',
					value: 'INDEX_PARA',
					disabled:false
				},{
					label: '尾款',
					value: 'TAIL_PARA',
					disabled:false
				}],
			discount_kind_opiton:[
				{
					label: '拍减',
					value: 'ORDER_CUT',
					disabled:false,
				},{
					label: '拍赠',
					value: 'ORDER_PRESENT',
					disabled:false,
				},{
					label: '付返',
					value: 'PAID_RETURN',
					disabled:false,
				},{
					label: '付赠',
					value: 'PAID_PRESENT',
					disabled:false,
				},
			],
			discount_effect_area_option:[
				{
					label: '销售订单',
					value: 'SYS',
					disabled:false,
				},{
					label: '合并订单',
					value: 'MERGE',
					disabled:false,
				},{
					label: '批次订单',
					value: 'BATCH',
					disabled:false,
				},{
					label: '行',
					value: 'LINE',
					disabled:false,
				},
			],
			// 用于添加商品时的设置，传递至goodsAdd.vue
			isEditMateriel:false,
			rules:{
				presell_pay_stage:[
					{
					required: true,
					isShow: false,
					message: '当预售优惠为是时,不能为空',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value && self.form.presell_act == "Y") {

							self.rules[rule.field][0].isShow = true
							callback();
						} else {
							// self.rules[rule.field][0].isShow = true
							// callback(new Error(''));
							self.rules[rule.field][0].isShow = false
						}
					}
				}
				],
				win_goods_id:[
					{
					required: true,
					isShow: false,
					message: '规则链接ID不能为空',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(!value) {

							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						} else {
							self.rules[rule.field][0].isShow =

							callback();
						}
					}
				}
				],
				win_rule_code:[
					{
					required: true,
					isShow: false,
					message: '当自动出中奖名单为是时,不能为空',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(!value && self.form.if_auto_win_list == "Y") {

							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						} else {
							self.rules[rule.field][0].isShow =

							callback();
						}
					}
				}
				],
				win_quota:[
					{
					required: true,
					isShow: false,
					message: '当自动出中奖名单为是时,不能为空',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(!value && self.form.if_auto_win_list == "Y") {

							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						} else {
							self.rules[rule.field][0].isShow = false
							callback();
						}
					}
				}
				],
				goods_discount_category_name:[{
					required: true,
					isShow: false,
					message: '请选择商品活动类目',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}
				}],
				presell_act:[{
					required: true,
					isShow: false,
					message: '请选择预售优惠',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}
				}],
				discount_affection_date:self.VLFun('moreSet', '请选择时间维度'),
				act_name:self.VLFun('discountDetail','请输入活动名称'),
				act_section:self.VLFun('discountDetail','请输入活动区间'),
				discount_type:[{
					required: true,
					isShow: false,
					message: '请选择子类型',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}

				}],
				time_control_value:[
					{
					required: true,
					isShow: false,
					message: '添加控制时长为1-720正整数',
					trigger: 'change',
					validator: function(rule, value, callback) {
						let  reg = /^[1-9][0-9]{0,3}$/,
							values = value += '';
						if(value||self.form.if_time_control=="N") {
							if(value&&reg.test(value)){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}

						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}

				}
				],
				additional_flag:[{
					required: true,
					isShow: false,
					message: '请选择商品标识不能输入中文',
					trigger: 'change',
					validator: function(rule, value, callback) {
						let han = new RegExp("[\\u4E00-\\u9FFF]+","g");
						if(value && han.test(num)) {

							self.rules[rule.field][0].isShow = true
							callback();
						} else {
							// self.rules[rule.field][0].isShow = true
							// callback(new Error(''));
							self.rules[rule.field][0].isShow = false
						}
					}
				}],
				additional_start_time:[{
						required: true,
						message:'生效时间不能大于失效时间',
						trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(value&&self.form.additional_start_time){
								if(value>=self.form.additional_end_time){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false;
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false;
									}else{
										self.rules['additional_start_time'][0].isValidateFromOther = true;
										self.$refs['form'].validateField('additional_start_time')
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = true
								callback();
							}
						}
				}],
				additional_end_time:[{
					required: true,
					message:'生效时间不能大于失效时间',
					trigger:'change',
					isShow:false,
					isValidateFromOther:false,
					validator: function(rule,value,callback){
						if(value&&self.form.additional_end_time){
							if(value<self.form.additional_start_time){
								self.rules[rule.field][0].isShow = true;
								callback(new Error(''));
							}else{
								self.rules[rule.field][0].isShow = false;
								if(self.rules[rule.field][0].isValidateFromOther){
									self.rules[rule.field][0].isValidateFromOther = false;
								}else{
									self.rules['additional_end_time'][0].isValidateFromOther = true;
									self.$refs['form'].validateField('additional_end_time')
								}
								callback();
							}
						}
					}
				}],
				enable_time:[{
						required: true,
						message:'生效时间不能大于失效时间',
						trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(value&&self.form.disable_time){
								if(value>=self.form.disable_time){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false;
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false;
									}else{
										self.rules['disable_time'][0].isValidateFromOther = true;
										self.$refs['form'].validateField('disable_time')
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = true
								callback();
							}
						}
					}],
				disable_time:[{
						required: true,
						message:'失效时间不能小于生效时间',
						trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(value&&self.form.enable_time){
								if(value<=self.form.enable_time){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false
									}else{
										self.rules['enable_time'][0].isValidateFromOther = true;
										self.$refs['form'].validateField('enable_time')
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = true
								callback();
							}
						}
					}],
				discount_sub_type: [{
					required: true,
					isShow: false,
					message: '请选择子类型',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}

				}],
				discount_name:[{
					required: true,
					isShow: false,
					message: '请选择优惠活动名称',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}
				}],
				actiivity_source_id: [{
					required: false,
					isShow: false,
					message: '请填写外部活动ID',
					trigger: 'blur',
					validator: function(rule, value, callback) {
						if(self.form.actiivity_source && !value) {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						} else {
							self.rules[rule.field][0].isShow = false
							callback();
						}
					}
				}],
				price: [{
					required: true,
					isShow: false,
					message: '请输入大于等于0的数字，保留两位小数',
					trigger: 'change',
					validator: function(rule, value, callback) {
						let reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
							values = value += ''
						if(reg.test(values)) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}
					}
				}],
				enable_price: [{
					required: true,
					isShow: false,
					message: '请输入大于等于0的数字，保留两位小数',
					trigger: 'change',
					validator: function(rule, value, callback) {
						let reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
							values = value + ''
						if(reg.test(values)) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}
					}
				}],
				external_activity_source_id: [{
					required: false,
					isShow: false,
					message: '请填写外部活动ID',
					trigger: 'blur',
					validator: function(rule, value, callback) {
						if(self.form.external_activity_source && !value) {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						} else {
							self.rules[rule.field][0].isShow = false
							callback();
						}
					}
				}],
				discount_category:[{
					required: true,
					isShow: false,
					message: '请选择线上优惠类别',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}
				}],
				wangpai_order_shop_name:[{
					required: true,
					isShow: false,
					message: '请选择网拍店铺',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}
				}],
				offline_payment_mode_name:[{
					required: true,
					isShow: false,
					message: '请选择线下货款模式',
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback();
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''));
						}
					}
				}],
        additional_expand: [
          {
            required: false,
            isShow: false,
            message: '请填写抖音商品ID',
            trigger: 'change',
          }
        ],
        additional_flag2: [{
					required: false,
					isShow: false,
					message: '请选择商品标识不能输入中文',
					trigger: 'change',
					validator: function(rule, value, callback) {
						let han = new RegExp("[\\u4E00-\\u9FFF]+","g");
						if(value && han.test(num)) {

							self.rules[rule.field][0].isShow = true
							callback();
						} else {
							// self.rules[rule.field][0].isShow = true
							// callback(new Error(''));
							self.rules[rule.field][0].isShow = false
						}
					}
				}],
			},
			changeWinQuotaTimer:false,
			operaterList: [],
			operaterCols: [
				{
					label: '操作人',
					prop: 'operator_name'
				}, {
					label: '业务操作',
					prop: 'operation_name'
				}, {
					label: '操作时间',
					prop: 'operation_time',
					width:300,
					format: 'dataFormat1'
				}, {
					label: '操作描述',
					prop: 'operation_desc'
				}, {
					label: '备注',
					prop: 'remark'
				}
			],
			operaterCount:0,
			// 设置生效时间从00:00:00开始
			dateFormatFun:{
				/*生效时间允许选所有值*/
				// disabledDate(time){
				// 	return time.getTime() < Date.now() - 8.64e7;
				// },
				date:(function(){
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth()+1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
					return new Date(time);
      			})()
			},
			endDateFormatFun:{
				date:(function(){
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth()+1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
					return new Date(time);
      			})()
			},
			// 设置结束时间为23:59:59
			iDateFormatFun:{
				disabledDate: time => {
					/*新增时能选所有时间，编辑时选当天之后*/
					return this.isAdd ? false : time.getTime() < Date.now() - 8.64e7
				},
				date:(function(){
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth()+1;
					var day = date.getDate();
					var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
					return new Date(time);
				})()
			},
			// 保存按钮状态
			saveStatus: false,
			onAudit: false,
			onWithdraw: false,
			onSuccess: false,
			onReflash:false,
			onFaile: false,
			shopsFlag: false,
			if_change_once:false,
			if_first_edit:true,
			dataGoodsDiscountCategoryObj: __AUX.get('goods_discount_category').reduce((pre, cur, idx, arr) => {
					pre[cur.code] = cur.name
					return pre
			},{}),
			offlinePaymentModeCategoryObj: __AUX.get('offline_payment_mode').reduce((pre, cur, idx, arr) => {
					pre[cur.code] = cur.name
					return pre
			},{}),
			discountCategoryAuxObj: __AUX.get('dicount_category_match').reduce((pre, cur, idx, arr) => {
					pre[cur.code] = cur.name
					return pre
			},{}),
			oldDiscountEffect_area: '',
			ifActWinningStatus: false,
			autoDiscountSwitch: 0,
      autoDiscountSwitchTime: ''
		}
	},
	methods:{
		judgeNullStr(str){
			if(str == null
			||str === 'undefined'
			||str == ''
			||str.length==0){
				return true
			}
			return false
		},
		// //导入结果
        //  showUploadResult(url) {
        //     this.$root.eventHandle.$emit("alert", {
        //         style: "width:900px;height:600px",
        //         title: "导入结果",
        //         params: {
        //             url,
        //             data: {},
        //             showDownload: false,
        //         },
        //         component: () => import("@components/common/eximport"),
        //     });
        // },
        discountCategoryChange(){
            if(this.form.discount_category !== 'FREE_DEPOSIT') return
            this.form.discount_condition_type = 'FULL_PIECE'
        },
		checkWinQuota(row){
			let result = this.fitterPrice2(row.win_quota) ? row.win_quota:'';
			if(!row.discount_item_id) return result;
			if(!result){
				return ''
			}else{
				let data = null
					this.initialItem.forEach(item=>{
						if(item.discount_item_id == row.discount_item_id){
							data = item;

						}
					})
				if(data.win_quota>row.win_quota){
					result = data.win_quota;
					this.$message.error('优惠限额只能改大不能改小');
				}else{
				}


				return result;

			}
		},
    getAutoDiscountSwitchTime() {
      this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryDataList', {
						categoryCode:"SALE_ORDER"
      }, res => {
        let result = ''
        if(res.body.result){
          let arr= res.body.content.list
          if( arr.length > 0 ){
            arr.forEach(item => {
              if (item.code === 'AUTO_DISCOUNT_SWITCH_TIME' && item.status == 1) {
                let ifDisabled = item.disableTime ? item.disableTime > new Date().getTime() : true
                let ifEnabled = item.enableTime ? item.enableTime < new Date().getTime() : true
                if (ifDisabled || ifEnabled) {
                  result = item.tag ? item.tag : false
                }
              }
            })
          }
        }
        this.autoDiscountSwitchTime = result
      }, err => {
        this.autoDiscountSwitchTime = ''
      })
    },
		initOfAutoAdd(value) {
			if (value === 'Y') {
				let ifChooseOneItem = this.conditionVo.every(item => {
					return item.item_enable_options == 'CHOOSEONEITEM'
				})
				if (!ifChooseOneItem) {
					this.form.if_auto_add = 'N'
				}
			}
		},
		checkDiscountOfAdd() {
      return new Promise((reslove, reject) => {
        let postData = {
          discount_id: this.form.discount_id,
          page_name: "act_discount_item",
          page_no: 1,
          page_size: 500,
          where: [],
        }, discountItemObj = {}, discountItem = []
        this.ajax.postStream('/price-web/api/actDiscount/getDiscountItem', postData, res => {
          if(res.body.result && res.body.content) {
            let list = res.body.content.list || [];
            discountItemObj = list.reduce((obj, curr) => {
              if (new Set(Object.keys(obj)).has(curr.discount_condition_id.toString())) {
                discountItem.push(curr.discount_condition_id)
                obj[curr.discount_condition_id].push(curr)
              } else {
                obj[curr.discount_condition_id] = []
                obj[curr.discount_condition_id].push(curr)
              }
              return obj
            }, {})
            reslove(discountItem)
          } else {
            reject(res.body.msg)
          }
        }, err=> {
          reject(err)
        })
      })
    },
	ChangeOfflineBefore(value){
      let self = this
      this.checkDiscountOfAdd().then((discountItem) => {
        let condition = []
        let isCheckCondition = self.conditionVo.some(item => {
          if (item.item_enable_options != "CHOOSEONEITEM") {
            condition.push(item.discount_condition_id)
          }
          return item.item_enable_options != "CHOOSEONEITEM"
        })
        if (discountItem.length > 0 && !['ORDER_PRESENT', 'PAID_PRESENT'].includes(self.form.discount_kind)) {
          this.$message.error(`设置线下前置失败！优惠条件「ID：${discountItem[0]}」存在多个优惠项目`)
          if(value == 'Y'){
            self.form.if_offline_before = 'N'
          } else {
            self.form.if_offline_before = 'Y'
          }
        }
        const conditionLength = self.conditionVo.filter((item)=> item.row_status == 0);
        if (self.form.if_superposition_discount == 'Y' && conditionLength.length > 1 && ['ORDER_PRESENT', 'PAID_PRESENT'].includes(self.form.discount_kind)) {
          self.$message.error(`设置线下前置失败！只允许设置一个优惠条件！`)
          if(value == 'Y'){
            self.form.if_offline_before = 'N'
          } else {
            self.form.if_offline_before = 'Y'
          }
        }
      }).catch((e) => {
        // self.$message.error(e)
      })
    },
    // 自动添加为是，则只能选择优惠条件为任选一项，不能选择其他，反之，优惠条件有其他，则自动添加只能为否
    changeIfAutoAdd(value) {
      let self = this
      this.checkDiscountOfAdd().then((discountItem) => {
        let condition = []
        let isCheckCondition = self.conditionVo.some(item => {
          if (item.item_enable_options != "CHOOSEONEITEM") {
            condition.push(item.discount_condition_id)
          }
          return item.item_enable_options != "CHOOSEONEITEM"
        })
        if (condition.length > 0  || discountItem.length > 0) {
          if (condition.length > 0 ) {
            this.$message.error(`设置自动添加失败！优惠条件「ID：${condition[0]}」的优惠选项不为“任选一项”`)
          } else {
            this.$message.error(`设置自动添加失败！优惠条件「ID：${discountItem[0]}」存在多个优惠项目`)
          }
          if(value == 'Y'){
            self.form.if_auto_add = 'N'
          } else {
            self.form.if_auto_add = 'Y'
          }
        }
      }).catch((e) => {
        // self.$message.error(e)
      })
    },
		// 优惠性质变化引起自动添加字段的变更
		changeDiscountKind(value) {
			// 当勾选自动添加优惠，如果类别为付返|拍减的，需要控制一旦勾选叠加，只能是每满叠加
			if (/^(PAID_RETURN|ORDER_CUT)$/.test(this.form.discount_kind)) {
				this.checkIfEachFull()
			} else {// 优惠性质选了拍赠/付赠 的，不能勾选自动添加
				this.form.if_auto_add = 'N'
			}

      this.form.if_offline_before = this.isOfflineBefore || this.ifReduceShots ? 'N' : this.form.if_offline_before
		},
		// 校验是否赋值每满为是
		checkIfEachFull () {
			if(this.form.if_auto_add == 'Y' && /^(PAID_RETURN|ORDER_CUT)$/.test(this.form.discount_kind) && this.form.if_superposition_discount == 'Y') {
				this.form.if_each_full = 'Y'
			}

		},
		changeDiscountCategory(value, ifAdd) {
			if (!value) return
			let insetDiscountList = this.discountCategorydata.filter(item => {
				return item.code == value
			})
			let insetDiscountObj = insetDiscountList[0]
			// this.form.discount_category = insetDiscountObj.code
			if (insetDiscountObj && insetDiscountObj.if_before_create_goods == 'Y') {
				if (!ifAdd) {
					this.form.discount_effect_area = 'LINE'
				}
				this.ifBeforeCreateGoods = true
			} else {
				if (!ifAdd) {
					this.form.discount_effect_area = this.params.isCopyAdd ? this.form.discount_effect_area : this.oldDiscountEffect_area
					let disabledOptionList = []
					disabledOptionList = this.discount_effect_area_option.reduce((a,b)=> {
						if (!b.disabled) {
							a.push(b.value)
						}
						return a
					}, [])
					if (this.form.discount_effect_area && this.form.discount_id && !(new Set(disabledOptionList).has(this.oldDiscountEffect_area))) {
						this.form.discount_effect_area = disabledOptionList[0]
					}
				}
				this.ifBeforeCreateGoods = false
			}
		},
		changeDiscountSubType(ifAdd) {
			// 优惠子类型为空则禁用
			if (!this.form.discount_sub_type) {
				return
			}
			let self = this
			this.ajax.postStream('/price-web/api/autoActiveDiscountSetting/list',{discount_subtype: this.form.discount_sub_type, activity_type: 'DISCOUNT_ACTIVITY', if_optional: 'Y'},res => {
				if(res.body.result && res.body.content) {
					let discountCategoryAuxList = []
					discountCategoryAuxList = res.body.content.reduce((arr, b)=> {
						arr.push({code: b.discount_category, name: self.discountCategoryAuxObj[b.discount_category], if_before_create_goods: b.if_before_create_goods})
						return arr
					}, [])
					// discountCategoryAuxList.push({code: 'OTHER_PROMOTION', name: '其他', if_before_create_goods: 'N'})
					this.discountCategorydata = JSON.parse(JSON.stringify(discountCategoryAuxList))
					// 默认赋值优惠类别
					if (!this.form.discount_id) { //未保存
						this.form.discount_category = discountCategoryAuxList[0].code
						if (discountCategoryAuxList[0].if_before_create_goods == 'Y') {
							this.form.discount_effect_area = 'LINE'
							this.ifBeforeCreateGoods = true
						} else {
							this.form.discount_effect_area = this.oldDiscountEffect_area
							this.ifBeforeCreateGoods = false
						}
					} else {
						// this.changeDiscountCategory(this.form.discount_category)
					}
					if (ifAdd) {
						this.changeDiscountCategory(this.form.discount_category, ifAdd)
					}
				} else {
					this.ifBeforeCreateGoods = false
					res.body.msg && this.$message.error(res.body.msg);
				}
			}, err => {
				this.ifBeforeCreateGoods = false
				this.$message.error(err);
			})
		},
        selectGoodsDiscountCategory (code) {
            let self = this
            this.$root.eventHandle.$emit('alert',{
                component:()=>import('@components/auxiliary/auxiliaryList'),
                style:'width:800px;height:500px',
                title:'辅助资料列表',
                params:{
                    isAlert: true,
                    selection: 'checkbox',
                    categoryCode: code,
                    callback(d){
						let fieldObj = new Map([
							['GOODS_DISCOUNT_CATEGORY', {field: 'goods_discount_category', fieldName: 'goods_discount_category_name'}],
							['OFFLINE_PAYMENT_MODE', {field: 'offline_payment_mode', fieldName: 'offline_payment_mode_name'}],
							['WANGPAI_ORDER_SHOP', {field: 'wangpai_order_shop', fieldName: 'wangpai_order_shop_name'}],
						])
						let fieldCode = '', fieldCodeName = ''
                        d.forEach((item,idx) => {
                            // if (!(new Set(discountCategoryList).has(item.code))) {
                                // if (idx == 0 && goods_discount_category != '') {
                                //     goods_discount_category = goods_discount_category + ',' + item.code
                                // } else if (idx == d.length -1) {
                                //     goods_discount_category += item.code
                                // } else {
                                    fieldCode += item.code + ','
                                    fieldCodeName += item.name + ','
                                // }
                            // }
                        })
                        if (fieldCode[fieldCode.length - 1] == ',') {
                            fieldCode = fieldCode.substring(0, fieldCode.length-1)
                            fieldCodeName = fieldCodeName.substring(0, fieldCodeName.length-1)
                        }
                        self.form[fieldObj.get(code).field] = fieldCode
                        self.form[fieldObj.get(code).fieldName] = fieldCodeName
                    }
                }//传参过去
            });
        },
        goodsDiscountCategoryChange(code){
			let fieldObj = new Map([
				['GOODS_DISCOUNT_CATEGORY', {field: 'goods_discount_category', fieldName: 'goods_discount_category_name'}],
				['OFFLINE_PAYMENT_MODE', {field: 'offline_payment_mode', fieldName: 'offline_payment_mode_name'}],
				['WANGPAI_ORDER_SHOP', {field: 'wangpai_order_shop', fieldName: 'wangpai_order_shop_name'}],
			])
            this.form[fieldObj.get(code).field] = ''
			this.form[fieldObj.get(code).fieldName] = ''
        },
		changeSelect(scope){
			if (!scope.row.changeFlag) {
				scope.row.changeFlag = true;
				return;
			}
			if (this.form.if_auto_add === 'Y') {
				this.$set(scope.row, 'item_enable_options', 'CHOOSEONEITEM')
			}
			switch(scope.row.item_enable_options) {
				case 'CHOOSEONEITEM': this.$set(scope.row, 'item_enable_count','1'); break;
				case 'ALLSELECTION':  this.$set(scope.row, 'item_enable_count','0'); break;
				case 'OPTIONAL':  this.$set(scope.row, 'item_enable_count',''); break;
				default: return row;			}
		},
		changeNum(scope){
			let vm = this;

			this.$nextTick(function(){
        if(scope.row.item_enable_options === 'OPTIONAL') {
          vm.$set(scope.row, 'item_enable_count',vm.fitterPrice6(scope.row.item_enable_count));
        } else {
          vm.$set(scope.row, 'item_enable_count',vm.fitterPrice2(scope.row.item_enable_count));
        }
			});
		},
		// 校验 9.16
		VLFun(tabs, msg){
			var self =this;
			return [{
					required: true,
					isShow: false,
					message: msg,
					trigger: 'change',
					validator: function(rule, value, callback) {
						if(value) {
							self.rules[rule.field][0].isShow = false
							callback()
						} else {
							self.rules[rule.field][0].isShow = true
							callback(new Error(''))
						}
					}
				}]
		},

		openRroupList(){

				var _this = this;
				this.$root.eventHandle.$emit('alert',{
						component:()=>import('@components/discount/getGroupList.vue'), close:function(){

				},
				style:'width:800px;height:600px;z-index:1000;',
				title:'请选择分组',
				params:{
                    KeyList: _this.form.store_group_type,
                    // userListVO:_this.userListVO,
                    callback(b){
                         var data = b.data;
                        // _this.query.businessTypeListText = data.valuesList.join(',')
                        _this.form.store_group_type = data.KeyList;
                    }
                }

            	});
        },
		ifgroup(){
			return this.form.store_area == 2 ? true:false
		},
		// 查看物料
		checkConditionItem(row){
			this.$root.eventHandle.$emit('alert',{
				title: '优惠商品详情',
				params:{
					row:row,
					callback(data){
						let self = this;

					},
				},

				style: 'width:930px; height: 600px',
				component:()=>import('@components/discount/showDiscountItem.vue')
			})
		},
		winRuleChange(data){
			if(data != 'D'){
				this.form.win_goods_id = '';
			}
			this.win_rule_code_opiton.forEach(item=>{
				if(item.rule_code == data){
					this.roleConfig.if_zhengdian = item.if_zhengdian;
					this.roleConfig.if_condition_order = item.if_condition_order;
				}
			});
			this.setConditionCol(this.form.discount_condition_type);
		},
		// 优惠条件
		discountConditionChange(val){
			let self = this;
			if(this.isCopyAdd && self.form.discount_condition_type != self.backup.discount_condition_type && !!self.form.discount_condition_type){
				this.$confirm('该操作需清空相应的优惠条件详情及优惠项目明细，是否继续？','提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'danger'
				}).then(()=>{
					if(val == 'FULL_YUAN'){
						// this.conditionCol=self.conditionColType.FULL_YUAN()
					}else if(val == 'UNCONDITIONAL'){
						// this.form.if_superposition_discount = 'N';
						this.form.if_each_full = 'N';
						// this.conditionCol=self.conditionColType.UNCONDITIONAL()
						this.form.if_auto_add = 'N'
						this.form.if_offline_before = this.isOfflineBefore ? 'N' : this.form.if_offline_before//优惠条件为无条件，则线下前置为否，不可更改

					}else{
						// this.conditionCol=self.conditionColType.FULL_PIECE()
						// this.form.if_superposition_discount = 'N';
						if(this.form.discount_effect_area =='LINE' ){
                            if (!this.form.discount_id){
								this.form.if_superposition_discount = 'N';
							}


						}
					}
					this.setConditionCol(val)
					self.backup.discount_condition_type = val;
					if(!!this.isAdd){
						this.conditionVo = [];
						this.listActDiscountItemVo = [];
					}

				}).catch(()=>{
					self.form.discount_condition_type = self.backup.discount_condition_type
				})
			}else{
				// // 初始化时取接口给过来的值
				// if(self.discountConditionFlag = false){
				// 	self.discountConditionFlag = true;
				// 	return flase;
				// }
				this.setConditionCol(val)

				if(val == 'FULL_YUAN'){
						// this.conditionCol=self.conditionColType.FULL_YUAN()
				}else if(val == 'UNCONDITIONAL'){
					// this.form.if_superposition_discount = 'N';
					this.form.if_each_full = 'N';
					// this.conditionCol=self.conditionColType.UNCONDITIONAL()
					this.form.if_auto_add = 'N' //优惠条件为无条件，则自动添加为否，不可更改
					this.form.if_offline_before = this.isOfflineBefore ? 'N' : this.form.if_offline_before//优惠条件为无条件，则线下前置为否，不可更改
				}else{
					// this.conditionCol=self.conditionColType.FULL_PIECE()
					if(this.form.discount_effect_area =='LINE' ){
                        if (this.form.discount_id) return
						this.form.if_superposition_discount = 'N';

					}
				}

				self.backup.discount_condition_type = val;
				if(!!this.isAdd && !this.isCopyAdd){
					this.conditionVo = [];
					this.listActDiscountItemVo = [];
				}
			}


		},
		setConditionCol(type){
			let self = this;

			if(type == 'FULL_YUAN'){
					this.conditionCol=self.conditionColType.FULL_YUAN()
			}else if(type == 'UNCONDITIONAL'){
					this.conditionCol=self.conditionColType.UNCONDITIONAL()
			}else{
					this.conditionCol=self.conditionColType.FULL_PIECE()
			}
		},
		/*
		优惠类型选择
		根据所先优惠类型，过滤出子分类，并判断是否有子分类
		如果有子分类，设置isChildClass值为true，否则为false
		*/
		selectParent(value) {
			let childClassData = []
			this.childClassDataAll.find(row => {
				// if(row.parentCode === value && row.status) {
				if(row.parentCode === value && row.status && row.ext_field2 === 'Y') {
					childClassData.push(row)
				}
			})
			if(childClassData.length) {
				this.isChildClass = true;
				this.childClassData = childClassData;
			} else {
				this.isChildClass = false
			}
			if(this.isAdd) {
				if(this.isCopyAdd){
					if(!this.copyAddSubFlag){
						this.copyAddSubFlag = true;
						return;
					}
					this.form.discount_sub_type = '';
					this.form.discount_category = '';
				}else{
					this.form.discount_sub_type = '';
					this.form.discount_category = '';
				}
			}

		},


		// 子分类选择 9.17
		selectChild(value){
			let self = this;
			if(this.isCopyAdd && self.form.discount_sub_type != self.backup.discount_sub_type && !!self.form.discount_sub_type){
				this.$confirm('该操作需清空相应的优惠条件详情及优惠项目明细，是否继续？','提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'danger'
				}).then(()=>{
					this.childClassData.forEach(item =>{
						if(item.code == value){
							this.form.item_type = item.ext_field1;
						}
					})
					this.getFormSetting(value)
					this.changeDiscountSubType()
					self.conditionVo = [];
					self.listActDiscountItemVo = [];
					self.backup.discount_sub_type = value;
				}).catch(()=>{
					self.form.discount_sub_type = self.backup.discount_sub_type;
				})
			}else{
				if(!value) return;
				this.childClassData.forEach(item =>{
						if(item.code == value){
							this.form.item_type = item.ext_field1;
						}
					})
					this.getFormSetting(value)
					this.changeDiscountSubType()
			}


		},
			// 复制新增
		copyAdd(){
			var self = this;
			this.ajax.postStream('/price-web/api/actDiscount/getCopyAddData', {"discount_id":self.form.discount_id},res=>{
				if(res.body.result) {

					// this.$message.success('操作');
					// let conditionIds = [];
					// for(var i in res.body.content){
					// 	if(i.indexOf('condition_id') != -1){
					// 		conditionIds.push(res.body.content[i]);
					// 	}
					// }
					let data = res.body.content;
          if (data.actDiscountVo.additional_act_content === 'DY_ORDER_COUPONS') {
            data.actDiscountVo.additional_flag2 = res.body.content.actDiscountVo.additional_flag;
            data.actDiscountVo.additional_flag = '';
          } else {
            data.actDiscountVo.additional_flag2 = '';
          }
					self.$root.eventHandle.$emit('creatTab',{name:'复制新增优惠活动'
					,params:{
						actcopy_id:data.actDiscountVo.discount_id,
						form:JSON.parse(JSON.stringify(data.actDiscountVo)),
						isCopyAdd:true,
						// conditionIds:conditionIds,
						conditionVo:data.listActDiscountConditionVo,
						DiscountVo:data.listActDiscountMaterialListVo,
						listPmsActShopVO:data.listActDiscountShopVo,
						listPmsActAuthorVO:data.listExpandAuthorAccountVo,
						listActDiscountItemVo:data.listActDiscountItemVo,
					 }
					,component:()=>import('@components/discount/newDiscunt.vue')});

				}
			});
		},
		getFormSetting(val){
			if(!this.isAdd || this.isCopyAdd) return;

			this.ajax.postStream('/price-web/api/actDiscountConfigure/getIflagInfo',{discount_sub_type_code:val}, (res) => {
				if(val == 'TIQIANGOU'){
					this.form.if_in_advance_discount = 'Y';
				}else{
					this.form.if_in_advance_discount = 'N';
				}
					if(!res.body.result || res.body.content.length < 1){0
						this.$message.error('获取配置信息失败')
						 return false;
					}
						let discount_kind_opiton =[
							{
								label: '拍减',
								value: 'ORDER_CUT',
								disabled:true,
							},{
								label: '拍赠',
								value: 'ORDER_PRESENT',
								disabled:true,
							},{
								label: '付返',
								value: 'PAID_RETURN',
								disabled:true,
							},{
								label: '付赠',
								value: 'PAID_PRESENT',
								disabled:true,
							},
						];
						let discount_effect_area_option = [
							{
								label: '销售订单',
								value: 'SYS',
								disabled:true,
							},{
								label: '合并订单',
								value: 'MERGE',
								disabled:true,
							},{
								label: '批次订单',
								value: 'BATCH',
								disabled:true,
							},{
								label: '行',
								value: 'LINE',
								disabled:true,
							},
						];

						let infoObj = {
							if_auto_add:[res.body.content[0].if_auto_add],
							if_effect_saleprice:[res.body.content[0].if_effect_saleprice],
							if_need_audit:[res.body.content[0].if_need_audit],
							if_amount_change:[res.body.content[0].if_amount_change],
							discount_effect_area:[res.body.content[0].discount_effect_area],
							discount_kind:[res.body.content[0].discount_kind],
						}
						// 应用场景
						this.form.application_scenarios = res.body.content[0].application_scenarios_code;
						// 用户类型
						this.form.use_category = res.body.content[0].use_category;
						if(res.body.content.length>1){
							res.body.content.forEach(item => {
								if(!new Set(infoObj.if_auto_add).has(item.if_auto_add)){
									infoObj.if_auto_add.push(item.if_auto_add)
								}
								if(!new Set(infoObj.if_effect_saleprice).has(item.if_effect_saleprice)){
									infoObj.if_effect_saleprice.push(item.if_effect_saleprice)
								}
								if(!new Set(infoObj.if_need_audit).has(item.if_need_audit)){
									infoObj.if_need_audit.push(item.if_need_audit)
								}
								if(!new Set(infoObj.discount_effect_area).has(item.discount_effect_area)){
									infoObj.discount_effect_area.push(item.discount_effect_area)
								}
								if(!new Set(infoObj.if_amount_change).has(item.if_amount_change)){
									infoObj.if_amount_change.push(item.if_amount_change)
								}
								if(!new Set(infoObj.discount_kind).has(item.discount_kind)){
									infoObj.discount_kind.push(item.discount_kind)
								}
							});
						}

						// 是否自动添加
						if(infoObj.if_auto_add.length>1){
							this.form.if_auto_add = 'N';
							this.auto_add_control = false;
						}else{
							this.form.if_auto_add = infoObj.if_auto_add[0];
							this.auto_add_control = true;
						}
						// 是否自动审核
						if(infoObj.if_need_audit.length>1){
							this.form.if_need_audit = 'N';
							this.need_audit_control = false;
						}else{
							this.form.if_need_audit = infoObj.if_need_audit[0];
							this.need_audit_control = true;
						}
						// 是否影响售价
						if(infoObj.if_effect_saleprice.length>1){
							this.form.if_effect_saleprice = 'N';
							this.effect_saleprice_control = false;
						}else{
							this.form.if_effect_saleprice = infoObj.if_effect_saleprice[0];
							this.effect_saleprice_control = true;
						}
						// 是否金额可变
						if(infoObj.if_amount_change.length>1){
							this.form.if_amount_change = 'N';
							this.amount_change_control = false;
						}else{
							this.form.if_amount_change = infoObj.if_amount_change[0];
							this.amount_change_control = true;
						}


						// 优惠影响范围
						this.form.discount_effect_area = infoObj.discount_effect_area[0];
						this.oldDiscountEffect_area = infoObj.discount_effect_area[0]
						this.changeDiscountCategory(this.form.discount_category)
						discount_effect_area_option.forEach(item=>{
							if(new Set(infoObj.discount_effect_area).has(item.value)){
								item.disabled = false;
							}
						});
						// 优惠性质
						this.form.discount_kind = infoObj.discount_kind[0];
						discount_kind_opiton.forEach(item=>{
							if(new Set(infoObj.discount_kind).has(item.value)){
								item.disabled = false;
							}
						});
						this.discount_effect_area_option = discount_effect_area_option;
						this.discount_kind_opiton = discount_kind_opiton;


					});
		},
		getFormSetting2(val){
			this.ajax.postStream('/price-web/api/actDiscountConfigure/getIflagInfo',{discount_sub_type_code:val}, (res) => {
				if(val == 'TIQIANGOU'){
					this.form.if_in_advance_discount = 'Y';
				}else{
					this.form.if_in_advance_discount = 'N';
				}
					if(!res.body.result || res.body.content.length < 1){0
						this.$message.error('获取配置信息失败')
						 return false;
					}
					let discount_kind_opiton =[
							{
								label: '拍减',
								value: 'ORDER_CUT',
								disabled:true,
							},{
								label: '拍赠',
								value: 'ORDER_PRESENT',
								disabled:true,
							},{
								label: '付返',
								value: 'PAID_RETURN',
								disabled:true,
							},{
								label: '付赠',
								value: 'PAID_PRESENT',
								disabled:true,
							},
						];
					let discount_effect_area_option = [
							{
								label: '销售订单',
								value: 'SYS',
								disabled:true,
							},{
								label: '合并订单',
								value: 'MERGE',
								disabled:true,
							},{
								label: '批次订单',
								value: 'BATCH',
								disabled:true,
							},{
								label: '行',
								value: 'LINE',
								disabled:true,
							},
						];

						let infoObj = {
							if_auto_add:[res.body.content[0].if_auto_add],
							if_effect_saleprice:[res.body.content[0].if_effect_saleprice],
							if_need_audit:[res.body.content[0].if_need_audit],
							if_amount_change:[res.body.content[0].if_amount_change],
							discount_effect_area:[res.body.content[0].discount_effect_area],
							discount_kind:[res.body.content[0].discount_kind],
						}
						if(res.body.content.length>1){
							res.body.content.forEach(item => {
								if(!new Set(infoObj.if_auto_add).has(item.if_auto_add)){
									infoObj.if_auto_add.push(item.if_auto_add)
								}
								if(!new Set(infoObj.if_effect_saleprice).has(item.if_effect_saleprice)){
									infoObj.if_effect_saleprice.push(item.if_effect_saleprice)
								}
								if(!new Set(infoObj.if_need_audit).has(item.if_need_audit)){
									infoObj.if_need_audit.push(item.if_need_audit)
								}
								if(!new Set(infoObj.discount_effect_area).has(item.discount_effect_area)){
									infoObj.discount_effect_area.push(item.discount_effect_area)
								}
								if(!new Set(infoObj.if_amount_change).has(item.if_amount_change)){
									infoObj.if_amount_change.push(item.if_amount_change)
								}
								if(!new Set(infoObj.discount_kind).has(item.discount_kind)){
									infoObj.discount_kind.push(item.discount_kind)
								}
							});
						}
						// 是否自动添加
						if(infoObj.if_auto_add.length>1){
							this.auto_add_control = false;
						}else{
							this.form.if_auto_add = infoObj.if_auto_add[0];
							this.auto_add_control = true;
						}
						// 是否自动审核
						if(infoObj.if_need_audit.length>1){
							this.need_audit_control = false;
						}else{
							this.form.if_need_audit = infoObj.if_need_audit[0];
							this.need_audit_control = true;
						}
						// 是否影响售价
						if(infoObj.if_effect_saleprice.length>1){
							this.effect_saleprice_control = false;
						}else{
							this.form.if_effect_saleprice = infoObj.if_effect_saleprice[0];
							this.effect_saleprice_control = true;
						}
						// 是否金额可变
						if(infoObj.if_amount_change.length>1){
							this.amount_change_control = false;
						}else{
							this.form.if_amount_change = infoObj.if_amount_change[0];
							this.amount_change_control = true;
						}

						// 优惠影响范围
						discount_effect_area_option.forEach(item=>{
							if(new Set(infoObj.discount_effect_area).has(item.value)){
								item.disabled = false;
							}
						});
						// 优惠性质
						// this.form.discount_kind = infoObj.discount_kind[0];
						discount_kind_opiton.forEach(item=>{
							if(new Set(infoObj.discount_kind).has(item.value)){
								item.disabled = false;
							}
						});
						this.old_additional_act_content = this.form.additional_act_content
						this.old_additional_flag_copy = this.form.additional_flag_copy
						this.changeDiscountSubType(true)
						this.oldDiscountEffect_area = infoObj.discount_effect_area[0]
						this.discount_effect_area_option = discount_effect_area_option;
						this.discount_kind_opiton = discount_kind_opiton;
					});
		},
		// 保存 9.17
		/*
		isClose：是否关闭当前标签
		*/

		save(data,callback) {
			let self = this;
			self.saveStatus = true;
			this.ajax.postStream('/price-web/api/actDiscount/saveOrUpdate?permissionCode=DISCOUNT_ACTIVITY_SAVE',data, (res) => {
				// self.isCopyAdd = false;
				self.saveStatus = false;
				if(res.body.result){
					self.$message({
						type: res.body.code=='610' ? 'warning' : 'success',
						message: res.body.msg
					});
					this.form.discount_id = res.body.content;
					callback && callback();
					this.getData();
					// self.form.if_change = self.form.document_type == 'NORMAL'?'N':'Y';
					self.isCopyAdd = false;
				}else{
					// call&&call();
					if(self.ifChange){
						self.initChanging();
					}
					self.$message.error(res.body.msg?res.body.msg:'');

				};
			});
		},
		// 提交审核 9.17
		submit() {
			let self = this;
			// if(self.if_enable_time){
			// 	self.$message.error('优惠活动生效时间不能小于当前时间');
			// 	return false;
			// }
			if(self.form.if_change == 'Y'){
				self.$message.error('变更未保存订单请先保存再提交');
				return false;
			}
			new Promise((reslove,reject)=>{
				this.checkoutSubmit(reslove);
			}).then((list)=>{
				if(!list.length){
					this.$refs.form.validate((valid) => {
						if (valid) {
							this.presave(this.submitAudit);
						}
					})
				}else{
					self.$confirm('存在【'+list[0].discount_item_no +'】的折扣低于10%，会有毛利过低风险，请确认是否继续提交！！！！！！','提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'danger'
					}).then(()=>{
						self.$refs.form.validate((valid) => {
						if (valid) {
								self.presave(self.submitAudit);
							}
						})
					})
				}

			})

		},
		checkoutSubmit(reslove){
			let _this = this;
			let itemSeatch = {
            page_name:"act_discount_item",
            where:[],
            page_size:10000,
            page_no:1,
            discount_id:this.form.discount_id
        }
			this.ajax.postStream('/price-web/api/actDiscount/getDiscountItem',
					itemSeatch,
					res => {
						if(res.body.result && res.body.content) {

						let listActDiscountItemVo = JSON.parse(JSON.stringify(this.listActDiscountItemVo));
						let flag = false;
						let errorList = [];
						 listActDiscountItemVo.forEach(item=>{
							if (item.discount_item_type === 'ITEM_TYPE_DISCOUNT' && item.discount< 10){
								errorList.push(item);
							}
						})
						reslove(errorList)
						}
						resolve && resolve();
				}, err => {
					resolve && resolve();
					this.$message.error(err);

				})

		},
		presave(callback){
			let self = this;
			let if_adjust = false;
			if(self.form.if_offline_before == "Y"){
				let offlineConditionFlag = false;
				let offlineOptionsFlag = false;
				this.conditionVo.forEach(item=>{
					let idList = []
					this.listActDiscountItemVo.forEach(actItem=>{
						if(item.discount_condition_id == actItem.discount_condition_id){
							idList.push(actItem.discount_item_id);
						}
					})
					if(idList.length>1){
						offlineConditionFlag = true;
					}

					if(item.item_enable_options != 'CHOOSEONEITEM'){
						offlineOptionsFlag = true;
					}
				})
        let conditionLength = self.conditionVo.filter((item)=> item.row_status == 0);
        if(['ORDER_PRESENT', 'PAID_PRESENT'].includes(self.form.discount_kind) && conditionLength.length > 1 && self.form.if_superposition_discount == 'Y'){
          self.$message.error('优惠叠加、线下前置为是的优惠，拍赠、付赠只能添加一个有效的优惠条件');
					return false;
				}
				if(!!offlineConditionFlag && self.form.discount_kind === 'ORDER_CUT'){
					self.$message.error('线下前置的优惠活动一个优惠条件只能创建一项优惠项目');
					return false;
				}
			}

			if(!self.form.discount_name){
				self.$message.error('优惠活动名称不能为空');
				return false;
			}
			let han = new RegExp("[\\u4E00-\\u9FFF]+","g");
			if(han.test(self.form.additional_flag)){
				self.$message.error('商品标识不能输入中文');
				return false;
			}
			if(self.form.document_type =='COPY'&&self.form.disable_time > self.initialData.disable_time){
				self.$message.error('活动失效时间不能大于原来失效时间');
				return false;
			}
			// if(self.form.document_type !='COPY' && self.form.status == 'APPROVED' &&self.form.disable_time > self.initialData.disable_time){
			// 	self.$message.error('活动失效时间不能大于原来失效时间');
			// 	return false;
			// }
			if (!!this.autoDiscountSwitchTime && this.form.discount_id) { // XPT-23841
				let switchTime = new Date(this.autoDiscountSwitchTime).getTime()
				if (this.form.enable_time < switchTime) {
				if (this.form.disable_time > switchTime || this.form.disable_time == switchTime) {
					let msg = `生效时间小于“${this.autoDiscountSwitchTime}”，失效时间也必须小于“${this.autoDiscountSwitchTime}”`
					self.$message.error(msg);
					return false;
				}
				if (this.form.discount_category != 'OTHER_PROMOTION') {
					let msg = `生效时间小于“${this.autoDiscountSwitchTime}”的活动，优惠类别只能选择“其他”`
					self.$message.error(msg);
					return false;
				}
				}
			}
			let isError = self.checkConditionPass();
			if(!isError.isPass){
				self.$message.error(isError.message);
				return false;
			}
			if(self.form.store_area == 2 && !self.form.store_group_type.length){
				self.$message.error('请选择分组内容')
				return false;
			}

			let piece_flag = true;
			if((self.form.discount_condition_type == 'FULL_PIECE'||self.form.discount_condition_type == 'MULTI_PIECE')  && self.form.discount_effect_area== 'LINE'){
				self.conditionVo.forEach(item=>{
					if(item.threshold_count>1){
						piece_flag = false
					}
				});
			}
			if(!piece_flag){
				self.$message.error('件数门槛不能大于1')
				return false;
			}
			// 当附加设置中勾选“作者账号活动”时，保存校验必须存在“生效”的作者账号，否则报错提示：作者账号活动必须设置生效的作者账号
			if (self.form.additional_act_content === 'AUTHOR_ACTIVITY' && !self.form.additional_flag_copy) {
				self.$message.error('作者账号活动必须设置作者活动类型')
				return false;
			}
      if (self.form.additional_act_content === 'DY_ORDER_COUPONS' && (!self.form.additional_expand || !self.form.additional_flag2)) {
        self.$message.error('抖音团购活动必须设置抖音商品ID,抖音商品标题')
				return false;
			}
      if(han.test(self.form.additional_flag2) && self.form.additional_act_content === 'DY_ORDER_COUPONS'){
				self.$message.error('抖音商品ID不能输入中文');
				return false;
			}

            if(self.form.discount_category === 'FREE_DEPOSIT'){
                let arr = self.listActDiscountItemVo.filter(item=>{
                    return item.item_type_detail !== 'REFUND_DEDUCTE_AMOUNT'
                })
                if(!!arr.length){
                    self.$message.error('免定金优惠项目的项目细类只能=返现减元')
				    return false;
                }
            }

			if(self.form.status == 'APPROVED'){
				if_adjust = self.compareSubmit();

			}
			setTimeout(()=>{

			this.$refs.form.validate((valid) => {
				if (valid) {
					if(!!if_adjust){
						let param ={
								callback:function(d){
									self.compare(d,callback)
								}
							}
							this.$root.eventHandle.$emit('alert',{
								params:param,
								component:()=>import('@components/discount/update_reason.vue'),
								style:'width:420px;height:200px',
								title:'修改原因'
							})
					}else{
						self.compare(null,callback)
					}
				}
			})

			},50);



		},
		// 对比数据
		compare(resData,callback){
			let self = this;
			new Promise((resolve, reject) => {

				self.formatListActDiscountConditionVo(function(arr) {
						resolve && resolve({arr:arr,callback:callback});
				});
			}).then((callData)=>{

					let listPmsActShopVO = [];
						let listActDiscountItemVo = JSON.parse(JSON.stringify(self.listActDiscountItemVo)).concat(self.delListActDiscountItemVo);
						listActDiscountItemVo.forEach(item=>{
								delete item.color_sign;
								if(item.__selected){
									delete item.__selected;
								}
						})
						self.DiscountVo.forEach(item=>{
							delete item.color_sign;
								if(item.__selected){
									delete item.__selected;
								}
						})
						self.cancelListDiscount.forEach(item=>{
							delete item.color_sign;
								if(item.__selected){
									delete item.__selected;
								}
						})
						self.delListDiscount.forEach(item=>{
							delete item.color_sign;
								if(item.__selected){
									delete item.__selected;
								}
						})
					callData.arr.forEach(item =>{
						delete item.changeFlag;
						delete item.tid;
					})
					let listPmsActAuthorVO = self.listPmsActAuthorVO.reduce((arr, curr) => {
						delete curr.tempId
						if (self.isCopyAdd) {
							curr.discount_expand_id = null
							curr.discount_id = self.form.discount_id
						}
						arr.push(curr)
						return arr
					}, [])
					let data = {

						actDiscountVo:{
							discount_id:self.form.discount_id,
							discount_name:self.form.discount_name,
							discount_no:self.form.discount_no,
							enable_time:self.form.enable_time,
							disable_time:self.form.disable_time,
							offline_discount_class:self.form.offline_discount_class,
							discount_type:self.form.discount_type,
							discount_sub_type:self.form.discount_sub_type,
							discount_condition_type:self.form.discount_condition_type,
							if_superposition_discount:self.form.if_superposition_discount,
							if_each_full:self.form.if_each_full,
							if_auto_add:self.form.if_auto_add,
							if_auto_win_list:self.form.if_auto_win_list,
							presell_pay_stage:self.form.presell_pay_stage,
							win_quota:self.form.win_quota,
							win_rule_code:self.form.win_rule_code,
							win_goods_id:self.form.win_goods_id,
							if_act_winning:self.form.if_act_winning,
							if_order_multiple_address:self.form.if_order_multiple_address,
							if_choose:self.form.if_choose,
							if_offline_before:self.form.if_offline_before,
							if_need_audit:self.form.if_need_audit,
							if_effect_saleprice:self.form.if_effect_saleprice,
							if_amount_change:self.form.if_amount_change,
							item_type:self.form.item_type,
							discount_effect_area:self.form.discount_effect_area,
							store_area:self.form.store_area,
							remark:self.form.remark,
							dealer_discount:self.form.dealer_discount,
							discount_affection_date:self.form.discount_affection_date,
							act_section:self.form.act_section,
							change_status:self.form.change_status,
							store_group_type:typeof(self.form.store_group_type) == 'string' || !self.form.store_group_type?self.form.store_group_type:self.form.store_group_type.join(','),
							document_type:self.form.document_type,
							external_activity_source:self.form.external_activity_source,
							external_activity_source_id:self.form.external_activity_source_id,
							version_number:self.form.version_number,
							use_category:self.form.use_category,
							additional_act_content:self.form.additional_act_content,
							additional_start_time:self.form.additional_start_time,
							additional_end_time:self.form.additional_end_time,
							additional_flag:self.form.additional_act_content === "AUTHOR_ACTIVITY" ? self.form.additional_flag_copy : self.form.additional_act_content === "DY_ORDER_COUPONS" ? self.form.additional_flag2 : self.form.additional_flag,
              additional_expand: self.form.additional_expand,
              if_plan_activity:self.form.if_plan_activity,
							time_limit_share:self.form.time_limit_share,
							if_use_once: self.form.if_use_once,
							// 优惠性质
							discount_kind:self.form.discount_kind,
							// 是否提前购
							if_in_advance_discount:self.form.if_in_advance_discount,
							// 是否定制
							customization:self.form.customization,

							application_scenarios:self.form.application_scenarios,
							goods_discount_category: self.form.goods_discount_category,
							goods_discount_category_name: self.form.goods_discount_category_name,
							presell_act: self.form.presell_act,
							time_control_value:self.form.time_control_value,
							if_time_control:self.form.if_time_control,
							if_sync_coupon:self.form.if_sync_coupon,
							exclusive_discount_no: self.form.exclusive_discount_no,
							discount_category: self.form.discount_category,
							offline_payment_mode: self.form.offline_payment_mode,
							offline_payment_mode_name: self.form.offline_payment_mode_name,
							wangpai_order_shop_name: self.form.wangpai_order_shop_name,
						},
						listActDiscountConditionVo:callData.arr,
						listActDiscountMaterialListVo:self.DiscountVo.concat(self.cancelListDiscount).concat(self.delListDiscount),
						listActDiscountShopVo:self.listPmsActShopVO.concat(self._getAllShopsSearchData.del),
						listActDiscountItemVo:listActDiscountItemVo.concat(self.delListActDiscountItemVo),
						// if_change:self.form.document_type == 'NORMAL'?'N':'Y'
						if_change:self.form.if_change,
						listExpandAuthorAccountVo: listPmsActAuthorVO.concat(self._getAllAuthorsSearchData.del),
					}
					// 如果resData存在则在保存时加入if_adjust、adjust_type、adjust_type_name三个字段
					if(!!resData){
						data.if_adjust = 'Y';
						data.adjust_type = resData.code;
						data.adjust_type_name = resData.name;
					}
					if(self.isCopyAdd){
						data.if_copy_add = 'Y';
					}
					// 附加设置活动为作者账号活动则需要校验：作者账号活动必须设置生效的作者账号
					if (self.form.additional_act_content === 'AUTHOR_ACTIVITY') {
						self.checkAuthorsOf().then(()=> {
							let ifAuthorOfLength = data.listExpandAuthorAccountVo.length === 0 && self.form.additional_act_content === 'AUTHOR_ACTIVITY',
							ifAuthorOfSaveParamDisable = (data.listExpandAuthorAccountVo.length > 0 && data.listExpandAuthorAccountVo.every(author => {
								return (author.discount_expand_id && author.expand_value_id && (author.status == 0 || author.row_delete_flag == 'Y'))
							})) && self.form.additional_act_content === 'AUTHOR_ACTIVITY',
							ifCheckAuthor = true
							// 若传入数据为0，则直接查看远端数据库数据量是否大于传入数据
							if (ifAuthorOfLength) {
								ifCheckAuthor = self.systemAuthorEnableList.length > 0 && self.systemAuthorAllList.length > 0
							} else if (ifAuthorOfSaveParamDisable) {
								//若传入的都是删除/失效 则1、查看远端数据库数据量是否大于传入数据 2、远端有效数据和现在传入数据做去重，去重后的长度必须>传入数据长度，这样才代表存在生效数据
								let systemAuthorEnableId = self.systemAuthorEnableList.reduce((arr, item)=> {
									arr.push(item.discount_expand_id)
									return arr
								}, [])
								let localAuthorEnableId = data.listExpandAuthorAccountVo.reduce((arr, item)=> {
									arr.push(item.discount_expand_id)
									return arr
								}, [])
								let commonEnableId = Array.from(new Set(systemAuthorEnableId.concat(localAuthorEnableId)))
								ifCheckAuthor = self.systemAuthorAllList.length > data.listExpandAuthorAccountVo.length && self.systemAuthorEnableList.length > 0 &&  commonEnableId.length > data.listExpandAuthorAccountVo.length
							}
							if ( !ifCheckAuthor ) {
								self.$message.error('作者账号活动必须设置生效的作者账号')
								return false;
							}
							self.save(data,callData.callback);
						}).catch()
					} else {
						self.save(data,callData.callback);
					}
			})
			// 	}
			// })
		},

		compareSubmit(){
			let self = this;
			// discount_name
			// 优惠活动名称
			// enable_time
			// 生效时间
			// 失效时间disable_time
			// 是否叠加if_superposition_discount
			// 时间维度discount_affection_date
			// 备注remark

			let formSubmit = {
				discount_name:self.form.discount_name,
				enable_time:self.form.enable_time,
				disable_time:self.form.disable_time,
				if_superposition_discount:self.form.if_superposition_discount,
				discount_affection_date:self.form.discount_affection_date,
				remark:self.form.remark,
				if_auto_win_list:self.form.if_auto_win_list,
				presell_pay_stage:self.form.presell_pay_stage,
				win_rule_code:self.form.win_rule_code,
				win_goods_id:self.form.win_goods_id,
				win_quota:self.form.win_quota,
				if_act_winning:self.form.if_act_winning,
				if_order_multiple_address:self.form.if_order_multiple_address,
				discount_effect_area:self.form.discount_effect_area,
				act_section:self.form.act_section,
				discount_kind:self.form.discount_kind,
				if_each_full:self.form.if_each_full,
				if_time_control:self.form.if_time_control,
				if_sync_coupon:self.form.if_sync_coupon,
				discount_category: self.form.discount_category
			},
			initialSubmit={
				discount_name:self.initialData.discount_name,
				enable_time:self.initialData.enable_time,
				disable_time:self.initialData.disable_time,
				if_superposition_discount:self.initialData.if_superposition_discount,
				discount_affection_date:self.initialData.discount_affection_date,
				remark:self.initialData.remark,
				if_auto_win_list:self.initialData.if_auto_win_list,
				presell_pay_stage:self.initialData.presell_pay_stage,
				win_rule_code:self.initialData.win_rule_code,
				win_goods_id:self.initialData.win_goods_id,
				win_quota:self.initialData.win_quota,
				if_act_winning:self.initialData.if_act_winning,
				if_order_multiple_address:self.initialData.if_order_multiple_address,
				discount_effect_area:self.initialData.discount_effect_area,
				act_section:self.initialData.act_section,
				discount_kind:self.initialData.discount_kind,
				if_time_control:self.initialData.if_time_control,
				if_sync_coupon:self.initialData.if_sync_coupon,
				discount_category: self.initialData.discount_category
			}
			return this.compareData(formSubmit,initialSubmit);


		},
		preauditSuccess(){
			this.showDisableList(d=>{
				this.auditSuccess();
			});
		},
		showDisableList(data){
					this.ajax.postStream('/price-web/api/price/pms/page/findMaterialList?permissionCode=DISCOUNT_ACTIVITY_QUERY',data, (res) => {
						if(res.body.content.code.length != 0){
							let params = {
								callback(res){
									if(res == true){
										// call&&call();
										return true;
									}
								},
								data:res.body.content.code
							};
							this.$root.eventHandle.$emit('alert', {
								params: params,
								component: () => import("@components/discount/disableList.vue"),
								style: 'width:600px;height:400px',
								title: '当前操作异常存在订单不可选物料��下，继续操作会删除以下物料，是否继续'
							});
						}else{
							// call&&call();
							return true;
						};
					});
					// return;

		},
		submitAudit() {
			this.onAudit = true;
			this.ajax.postStream('/price-web/api/actDiscount/commit?permissionCode=DISCOUNT_ACTIVITY_SUBMIT', {
				list_discount_id: [this.form.discount_id]
			}, res => {
				if(res.body.result) {
					this.getData();
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})

				this.onAudit = false;
			}, err => {
				this.onAudit = false;
				this.$message.error(err);
			})
		},
		delOrder(){
			this.$confirm('当前操作会导致优惠活动被删除，是否继续？','提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'danger'
			  }).then(()=>{
				  this.ajax.postStream('/price-web/api/actDiscount/delete?permissionCode=DISCOUNT_ACTIVITY_DELETE', {
					list_discount_id: [this.form.discount_id]
					}, res => {
						if(res.body.result) {
							this.close();
						}
						this.$message({
							type: res.body.result ? 'success' : 'error',
							message: res.body.msg
						})

						this.onAudit = false;
					}, err => {
						this.onAudit = false;
						this.$message.error(err);
					})
			  	}).catch(()=>{
				  return false;
			  })

		},
		withdrawAudit(){
			this.ajax.postStream('/price-web/api/actDiscount/withdraw?permissionCode=DISCOUNT_ACTIVITY_WITHDRAW', {
				list_discount_id: [this.form.discount_id]
			}, res => {
				if(res.body.result) {
					this.getData();
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})

				this.onAudit = false;
			}, err => {
				this.onAudit = false;
				this.$message.error(err);
			})
		},
		openPlanActivity(){
			this.ajax.postStream('/price-web/api/actDiscount/openPlanActivity?permissionCode=DISCOUNT_ACTIVITY_OPEN', {
				list_discount_id: [this.form.discount_id]
			}, res => {
				if(res.body.result) {
					this.getData();
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})

				this.onAudit = false;
			}, err => {
				this.onAudit = false;
				this.$message.error(err);
			})
		},
		forbidden(){
			this.ajax.postStream('/price-web/api/actDiscount/forbidden?permissionCode=DISCOUNT_ACTIVITY_FORBID', {
				list_discount_id: [this.form.discount_id]
			}, res => {
				if(res.body.result) {
					this.getData();
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				})

				this.onAudit = false;
			}, err => {
				this.onAudit = false;
				this.$message.error(err);
			})
		},

		superpositionDiscount(val){
			let self = this;
			if(self.form.status=="APPROVED"){
				if(self.form.if_superposition_discount == 'N'){
					self.form.if_each_full = 'N';
				}

				return false;
			}
			if (!self.form.discount_id) {
				if (!(this.listActDiscountItemVo.length > 0 || this.conditionVo.length > 0 || this.DiscountVo.length > 0)) return
				this.$confirm('当前操作会导致优惠条件被清空，是否继续？','提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'danger'
				}).then(()=>{
					self.listActDiscountItemVo = []
					self.conditionVo = []
					self.DiscountVo = []
					if(self.form.if_superposition_discount == 'N'){
						self.form.if_each_full = 'N';
					}
				}).catch(()=>{
					if(val == 'Y'){
						self.form.if_superposition_discount = 'N';
					}else{
						self.form.if_superposition_discount = 'Y';
					}
					return false;
				})
			} else {
				self.$confirm('当前操作会导致优惠条件被清空，是否继续？','提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'danger'
				}).then(()=>{
					// self.conditionVo = [];
					// self.listActDiscountItemVo = [];
					self.clearAllData()
					if(self.form.if_superposition_discount == 'N'){
						self.form.if_each_full = 'N';
					}
				}).catch(()=>{
					if(val == 'Y'){
						self.form.if_superposition_discount = 'N';
					}else{
						self.form.if_superposition_discount = 'Y';
					}
					return false;
				})
			}
		},
		clearCondition(val){
			let self = this;
			if(self.form.status=="APPROVED"){
				return false;
			}
			if (!self.form.discount_id) {
				if (!(this.listActDiscountItemVo.length > 0 || this.conditionVo.length > 0 || this.DiscountVo.length > 0)) return
				this.$confirm('当前操作会导致优惠条件被清空，是否继续？','提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'danger'
				}).then(()=>{
          self.listActDiscountItemVo = []
					self.conditionVo = []
					self.DiscountVo = []
				}).catch(()=>{
					if(val == 'Y'){
						self.form.if_each_full = 'N';
					}else{
						self.form.if_each_full = 'Y';
					}
					return false;
				})
			} else {
				self.$confirm('当前操作会导致优惠条件被清空，是否继续？','提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'danger'
				}).then(()=>{
					// self.conditionVo = [];
					// self.listActDiscountItemVo = [];
          self.clearAllData()
				}).catch(()=>{
					if(val == 'Y'){
						self.form.if_each_full = 'N';
					}else{
						self.form.if_each_full = 'Y';
					}
					return false;
				})
			}
		},
		shopsChanged(value) {
			this.shopsBtnControl();
			if (!this.shopsFlag ) {
				this.shopsFlag = true;
				return;
			}
			// this.listPmsActShopVO = [];
			this.shopAllDel()
			// this.form.store_group_type = [];

			// if (!this.shopsFlag ) {
			// 	this.shopsFlag = true;
			// 	return;
			// }
			// if(this.backup.store_area == this.form.store_area) return false;
			// this.$confirm('该操作需清空相应的商品列表和店铺分类列表，是否继续？','提示', {
			// 		confirmButtonText: '确定',
			// 		cancelButtonText: '取消',
			// 		type: 'danger'
			// 	}).then(()=>{
			// 		this.listPmsActShopVO = [];
			// 		this.form.store_group_type = [];
			// 		this.backup.store_area = this.form.store_area;
			// 	}).catch(()=>{
			// 		this.form.store_area = this.backup.store_area;
			// 	})

		},

		//复制新增
		addCopeFun(){
				var self = this;
				this.ajax.postStream('/price-web/api/actDiscount/getSerialNumberOrId', this.form.discount_id,res=>{
					if(res.body.result) {
						this.$message.success('操作成功');
					}
				});
				return false;
		},
		reflash(){
			this.getData();
		},

		exportSingleActDiscount(){
			let self = this;
			self.export_status = true;
			let data ={
				discount_id: this.form.discount_id
			};

			self.ajax.postStream('/price-web/api/actDiscount/exportSingleActDiscount?permissionCode=DISCOUNT_ACTIVITY_EXPORT', data, res => {


				if(res.body.result ) {
					if(res.body.content){
						window.open(res.body.content);
						self.$message.success(res.body.msg);

					}else{
						self.$message.error('不能重复操作');
					}

				}else{
					self.$message.error(res.body.msg)
				}

				self.export_status = false;
			})
		},
		// 获取活动信息,9.16
		getData(ifChange,reslove){
			let self = this;
			// self.shopsFlag = false;
			var data ={
				discount_id: this.form.discount_id
			};
			if(ifChange){
				data ={
					discount_id: this.params.row.discount_id,
					discount_no: this.params.row.discount_no,
					if_change:'Y',
				}
			}
			self.onReflash = true;
			// self.conditionVo = [];
			self.ajax.postStream('/price-web/api/actDiscount/editPartInfo', data, res => {
				self.onReflash = false;
				if(res.body.result && res.body.content) {
					self.ifGetAllData = false;
					self.isAdd = false;
					self.if_first_edit = true;
					self.backup.store_area = self.form.store_area;
					self.form = res.body.content.actDiscountVo;
          if (self.form.additional_act_content === 'DY_ORDER_COUPONS') {
            self.form.additional_flag2 = res.body.content.actDiscountVo.additional_flag;
            self.form.additional_flag = '';
          } else {
            self.form.additional_flag2 = '';
          }
                    self.form.if_change = 'N';
                    let discountCategoryList = self.form.goods_discount_category.split(',')
                    self.form.goods_discount_category_name = discountCategoryList.reduce((name, cur,idx) => {
                        if (idx == (discountCategoryList.length -1) ) {
                            name += self.dataGoodsDiscountCategoryObj[cur]
                        } else {
                            name += self.dataGoodsDiscountCategoryObj[cur] + ','
                        }
                        return name
                    }, '')
					let offlinePaymentModeCategoryList = self.form.offline_payment_mode.split(',')
                    self.form.offline_payment_mode_name = offlinePaymentModeCategoryList.reduce((name, cur,idx) => {
                        if (idx == (offlinePaymentModeCategoryList.length -1) ) {
                            name += self.offlinePaymentModeCategoryObj[cur]
                        } else {
                            name += self.offlinePaymentModeCategoryObj[cur] + ','
                        }
                        return name
                    }, '')
                    self.itemSearch.discount_id = self.form.discount_id;
					self.shopSearch.discount_id = self.form.discount_id;
					self.operaterList = res.body.content.listCloudLogOperationPo || [];
					// 条件列表
					self.conditionVo = res.body.content.listActDiscountConditionVo;
					self.conditionSelect = null;
					self.$refs.conditionList.clearRadioSelect();
                    // 问题反馈自动获取单号
                    Vue.prototype.tabNo[self.params.tabName] = res.body.content.actDiscountVo.discount_no || '';
					setTimeout(function(){
						for (let i = 0; i < self.conditionVo.length; i++) {
							self.conditionVo[i].changeFlag = true;
						}
					}, 200);
					self.listPmsActShopVO=[];
					self.DiscountVo= res.body.content.listActDiscountMaterialListVo ? res.body.content.listActDiscountMaterialListVo:[];
					self.changing_conditionVo = JSON.parse(JSON.stringify(res.body.content.listActDiscountConditionVo));
					self.getActItemList();
					self._getAllShopsSearchData();
					self.getDiscountMaterialList();
					// self.shopSearching();
					self._getAllAuthorsSearchData();
					self.shopsBtnControl();
					self.getFormSetting2(self.form.discount_sub_type)
					setTimeout(function(){
					// 页面初始化数据，用作对比，是否有更新
						for(var key in self.form){
							self.initialData[key] = self.form[key]
						}
						self.initialCondition = [];
						self.conditionVo.forEach(item=>{
							self.initialCondition.push(item);
						})

					reslove && reslove(self.form);
					},500)

					self.delConditionVo = [];
					self.cancelConditionVo = [];
					self.delListActDiscountItemVo = [];
					self.cancelListActDiscountItemVo = [];
					self.cancelListDiscount = [];
					self.delListDiscount = [];

				}else{
					self.$message.error('该优惠活动已被删除，请刷新列表重新打开');
					self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				}
			})
		},
		getDiscountMaterialList(resolve){
			let self = this;
			// var data ={
			// 	discount_id: this.form.discount_id
			// };
			if(self.ifGetAllData){
                self.$message.error('请先保存再继续操作');
				resolve && resolve();
                return false;
            }
			if((self.ifChange||self.isCopyAdd)){
				self.$message.error('未保存前不可查询');
					resolve && resolve();
					return;
            }
			self.goodsSearch.discount_id = this.form.discount_id;
			self.ajax.postStream('/price-web/api/actDiscount/getDiscountMaterialList', self.goodsSearch, res => {
				if(res.body.result && res.body.content) {

					self.DiscountVo = res.body.content.list;
					self.discountSelect = null;
					self.goodCount = res.body.content.count;
					self.initialMaterial = [];
					self.DiscountVo.forEach(item=>{
						self.initialMaterial.push(item);
						if(self.conditionSelect&&self.conditionSelect.discount_condition_id == item.discount_condition_id){
							item.color_sign = 'A'
						} else {
							item.color_sign = '';
						}
					})

					self.DiscountVo = JSON.parse(JSON.stringify(self.DiscountVo));
					resolve && resolve()
				}else{
					self.$message.error(res.body.msg)
				}
			})
		},
		// 校验是否整数
		fitterPrice(num){
			var reg = /^(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/;
			if(!reg.test(num)){
			this.$message.error('当前输入不符合规范，请输入1-7位小数点后两位数字')
			}
			return reg.test(num)
		},
		// 校验是否整数
		fitterPrice2(num){
			let number = ''
			var reg = /^[1-9][0-9]{0,6}$/;
			if(!reg.test(num)){
				this.$message.error('当前输入不符合规范，请输入1-7位正整数')
				number = '';
			}else{
				number = num;
			}
			return number;
		},
		fitterPrice5(num){
			let number = num
			var reg = /^[0-9]+$/;
			num.split(',').forEach(item=>{
				if(!!item&&!reg.test(item)){
				this.$message.error('当前输入不符合规范，请输入数字')
					number = '';
				}else{

				}
			})
			if(!number){
				return '';
			}else{
				return number;
			}
		},
		// 校验是否整数
		fitterPrice4(num){
			let number = ''
			var reg = /^[1-9]\d*$/;
			if(!reg.test(num)){
				this.$message.error('当前输入不符合规范，请输入正整数')
				number = '';
			}else{
				number = num;
			}
			return number;
		},
		fitterPrice3(num){
				let reg = /^([1-9][0-9]{0,1}|100)$/
				if(!reg.test(num)&&!!num){
					this.$message.error('当前输入不符合规范，请输入1-100正整数')
					return '';
				}else{
					return num;
				}
			},
      fitterPrice6(num){
			let number = ''
			var reg = /^(?:[2-9]|[1-9]\d{1,7})$/;
			if(!reg.test(num)){
				this.$message.error('当前输入不符合规范，请输入1-7位且大于1的正整数')
				number = '';
			}else{
				number = num;
			}
			return number;
		},
		getActItemList(){
			let self = this;
			var data ={
				discount_id: this.form.discount_id,

			};
			self.ajax.postStream('/price-web/api/actDiscount/getDiscountItem', self.itemSearch, res => {
				if(res.body.result && res.body.content) {
					// self.listActDiscountItemVo = [];
					self.listActDiscountItemVo = res.body.content.list;
					self.initialItem = JSON.parse(JSON.stringify(res.body.content.list));
					self.itemCount = res.body.content.count;
				}else{
					self.$message.error(res.body.msg)
				}
			})
		},
		getDiscountShop(resolve){
			let self = this;
			var data ={
				discount_id: this.form.discount_id,
				page_size:self.params.shopCount,
				where:[{"field":"fb7f1d453211c71a9de8e6bdcf80f29c","table":"78fda2e42deeff769e661a412023c93b","value":"0","operator":"=","condition":"AND","listWhere":[]}]
			};
			self.ajax.postStream('/price-web/api/actDiscount/getDiscountShop', data, res => {
				if(res.body.result && res.body.content) {
					resolve && resolve(res.body.content.list)
					// self.listPmsActShopVO = res.body.content.list;
					// self.markListPmsShopVOData = JSON.parse(JSON.stringify(res.body.content.list));
				}else{
					self.$message.error(res.body.msg)
				}
			})
		},
		initCopyAdd(resolve){
			let self = this;
			this.isCopyAdd = true;
            this.form = this.params.form;
            let discountCategoryList = this.form.goods_discount_category.split(',')
            this.form.goods_discount_category_name = discountCategoryList.reduce((name, cur,idx) => {
                if (idx == (discountCategoryList.length -1) ) {
                    name += this.dataGoodsDiscountCategoryObj[cur]
                } else {
                    name += this.dataGoodsDiscountCategoryObj[cur] + ','
                }
                return name
            }, '')
			let offlinePaymentModeCategoryList = this.form.offline_payment_mode.split(',')
			this.form.offline_payment_mode_name = offlinePaymentModeCategoryList.reduce((name, cur,idx) => {
				if (idx == (offlinePaymentModeCategoryList.length -1) ) {
					name += this.offlinePaymentModeCategoryObj[cur]
				} else {
					name += this.offlinePaymentModeCategoryObj[cur] + ','
				}
				return name
			}, '')
            this.backup.discount_sub_type = this.form.discount_sub_type;
			this.backup.discount_type =this.form.discount_type;
			this.backup.discount_condition_type = this.form.discount_condition_type;
			this.backup.store_area = this.form.store_area;

			this.form.status = 'CREATE';
			this.getFormSetting2(this.form.discount_sub_type)
			this.conditionVo = this.params.conditionVo;
			this.listActDiscountItemVo = this.params.listActDiscountItemVo;
			this.DiscountVo = this.params.DiscountVo;
			this.listPmsActShopVO = this.params.listPmsActShopVO;
			this.listPmsActAuthorVO = this.params.listPmsActAuthorVO;
			resolve&&resolve(this.form);

			setTimeout(function(){
				for (let i = 0; i < self.conditionVo.length; i++) {
					self.conditionVo[i].changeFlag = true;
				}
			}, 200);
		},
		itemClick(){
			return false;
		},

		/*
		数据对比，用于关闭时提示数据是否有变动
		*/
		close() {
				var self = this,isUpdate ,materialIsUpdate,conditionIsUpdate;
				isUpdate = this.compareData(self.form,self.initialData);
				conditionIsUpdate = this.compareData(self.conditionVo,self.initialCondition);
				materialIsUpdate = this.compareData(self.DiscountVo,self.initialMaterial);
				if(isUpdate || conditionIsUpdate || materialIsUpdate){
					self.$root.eventHandle.$emit('openDialog',{
						ok(){
							self.presave(()=>{
								self.$root.eventHandle.$emit('removeTab',self.params.tabName)
							})
						},
						no(){
							self.$root.eventHandle.$emit('removeTab',self.params.tabName)
						}
					})
				}else{
					self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				}
		},
		cancelActContent(){
			this.form.additional_act_content = '';
			this.old_additional_act_content = '';
		},
		changeActContent (ifAllCancel) {
      console.log(ifAllCancel,'665665546',this.form.additional_act_content)
      console.log(this.ifReduceShots,'this.ifReduceShots')
      if(this.ifReduceShots){
        this.form.if_offline_before =  'N'
      }
            if(this.loadingNoClearData) {
                this.loadingNoClearData = false
                return
            }
			if (((this.form.additional_act_content != "AUTHOR_ACTIVITY" || ifAllCancel === "true")  && this.old_additional_act_content === 'AUTHOR_ACTIVITY')) {
				this.confirmChangeAct(ifAllCancel)
			} else if ( ifAllCancel === "true" && /^(GIFTORDERACT|FINALPAYACT|DY_ORDER_COUPONS)$/.test(this.form.additional_act_content)) {
				this.form.additional_start_time = '';
				this.form.additional_end_time = '';
				this.form.additional_flag = '';
				this.form.additional_act_content = '';
        this.form.additional_expand = '';
        this.form.additional_flag2 = '';
			} else {
				this.old_additional_act_content = this.form.additional_act_content
			}

		},
		clearActContent () {
			this.form.additional_start_time = '';
			this.form.additional_end_time = '';
			this.form.additional_flag = '';
			this.form.additional_flag_copy = '';
      this.form.tikTokId = '';
      this.form.additional_flag2 = '';
		},
		confirmChangeAct (ifAllCancel) {
			let self = this;
			this.$root.eventHandle.$emit('openDialog', {
				ok() {
					if (ifAllCancel === "true") {
						self.cancelActContent();
					} else {
						self.old_additional_act_content = self.form.additional_act_content
					}
					self.clearActContent();
					self.authorAllDel()
				},
				okTxt: '确认',
				no() {
					self.form.additional_act_content = self.old_additional_act_content;
					// self.form.additional_flag_copy = self.old_additional_flag_copy
				},
				noTxt: '取消',
				txt: '切换则清空作者账号，请确认',
				noCancelBtn: true
			})
		},
		actSectuibChange(e){
			if(this.formDIsabledControl) return;
			this.changeActContent("true");
		},
		tableRowClassName(row){
			/*return 'red';*/
			switch (row.color_sign){
				case 'A': return 'discount_blue';
			}
		},
		autoWinningChange(){
			if(this.form.if_auto_win_list == "N"){
				if(!!this.form.discount_id){
					this.form.win_quota = "";
					this.form.win_rule_code = "";
				}
			}
		},
		winningChange(){
			if(this.form.if_act_winning == "N"){
				this.form.if_order_multiple_address = "N";
				this.form.if_offline_before = this.isOfflineBefore ? 'N' : this.form.if_offline_before;
				if(!!this.form.discount_id){
					this.form.winningChange = "N";
					this.form.win_quota = "";
					this.form.win_rule_code = "";
				}

			}else{

			}
		},

		getActRuleList(resolve){
			let self = this;


				self.ajax.postStream('/price-web/api/actDiscount/getActRuleList',{
						"pageNo": 1,
						"pageSize": 50,
						"if_enable": "Y"
					}, res => {
				if(res.body.result && res.body.content) {
					self.win_rule_code_opiton = res.body.content.list;
					resolve&&resolve(res.body.content.list)
					// self.markListPmsShopVOData = JSON.parse(JSON.stringify(res.body.content.list));
				}else{
					self.$message.error(res.body.msg)
				}
			})


		},
		expList(){
			let self = this;
			this.$root.eventHandle.$emit('alert', {
					component: () => import('@components/discount/export'),
					style:'width:900px;height:600px',
					title: '中奖名单下载',
					params: {
						query: {
						type: 'EXCEL_TYPE_WIN_LIST_REPORT_EXPORT',
						table_primary_id:self.form.discount_id
						},
					},
				})

			},
		// 删除条件+项目+清单
		clearAllData(){
			// new Promise((afterRes,rej)=>{

			// })
			let self = this;
			new Promise((resolve,reject)=>{
				self.getAllData(resolve);
			}).then(()=>{
				let i = self.conditionVo.length;
				while(i--){
					if(!!self.conditionVo[i].discount_condition_id){
						self.deleteItem(self.conditionVo[i].discount_condition_id);
						self.deleteGoods(self.conditionVo[i].discount_condition_id);
						if(!self.isCopyAdd && !self.ifChange){
							self.delConditionVo.push(self.conditionVo[i]);
						}
						self.conditionVo.splice(self.conditionVo.indexOf(self.conditionVo[i]), 1);
					}else{
						self.conditionVo.splice(self.conditionVo.indexOf(self.conditionVo[i]), 1)
					}

				}
			})


		}
	},
	created() {
		this.autoDiscountSwitch = __AUX.get('SALE_ORDER').filter(item => item.code === 'AUTO_DISCOUNT_SWITCH')[0].tag
    	this.getAutoDiscountSwitchTime()
	},
	mounted(){
        this.loadingNoClearData = true
		var self = this;
		// return false;
		self.$root.eventHandle.$emit('doTab',self.params.tabName)

		self.shopsBtnControl();

		let getActRuleList = new Promise((resolve,reject)=>{
			self.getActRuleList(resolve);
		})
		let init = new Promise((resolve,reject)=>{
				// 变更
				if(self.params.isAdd){
					self.isChangingAdd = self.params.isAdd;
					self.initChanging(resolve);
					return false;
				}
				if(self.params.isCopyAdd){
					self.initCopyAdd(resolve);
					return false;
				}
				if(self.params.discount_id){
					if(self.params.if_change == 'Y'){
						self.getData(true,resolve);
						return false;
					}
					self.isAdd = false
					self.form.discount_id = self.params.discount_id
					self.itemSearch.discount_id = self.params.discount_id
					self.shopSearch.discount_id = self.params.discount_id
					self.getData(0,resolve);
				}
			})

		Promise.all([getActRuleList,init]).then((res)=>{
			let roleList = res[0],
			from = res[1];
			roleList.forEach(item=>{
				if(item.rule_code == from.win_rule_code){
					this.roleConfig = item;
					this.setConditionCol(from.discount_condition_type);
				}
			})
		})

		this.params.__data = JSON.parse(JSON.stringify(this.form));
		this.params.__close = this.close
	},
	watch:{
		'form.discount_type': function (newVal, oldVal) {
			if (/(REFUND|NEW_GIFT|TRADE_UP)/.test(newVal) && oldVal != newVal) {
				if (!this.form.discount_id) {
					this.form.dealer_discount = ''
				}
			}
		},
		'form.discount_effect_area': function (newVal, oldVal) {
			if (newVal === 'BATCH' && oldVal != 'BATCH') {
				this.form.if_time_control = 'N'
				this.form.time_control_value = ''
			}
			if ( newVal == 'LINE' && oldVal != newVal) {
				if ( /(FULL_PIECE|MULTI_PIECE)/.test(this.form.discount_condition_type)) {
                    if (this.form.discount_id) return
					this.form.if_superposition_discount = 'N';
				}
			}
		},
		'form.if_time_control': function (newVal, oldVal) {
			if (newVal === 'N') {
				this.form.time_control_value = 0;
			}
		},
		// 监听店铺范围是否改变，如果改变讲分组以及实施店铺清空
		// store_area(newVal,oldVal){
		// 	this.listPmsActShopVO = [];
		// 	this.form.store_group_type = [];
		// }
		conditionVo(newVal,oldVal){
			if(!newVal){
				this.discountitem = [];
			}
		},
		'form.discount_sub_type':function(newVal,oldVal){

		},
		'form.if_auto_add':function(newVal,oldVal){ //自动添加为是，是否审核为否，金额可变为否
			if (newVal === 'Y' && oldVal === 'N') {
				this.form.if_need_audit = 'N'
				this.need_audit_control = true
				this.form.if_amount_change = 'N'
				this.amount_change_control = true
				this.form.if_act_winning = 'N'
				this.ifActWinningStatus = true
				this.changeDiscountKind()
			} else if (newVal === 'N' && oldVal === 'Y') {
				this.need_audit_control = false
				this.amount_change_control = false
				this.ifActWinningStatus = false
			}
		},
		'form.discount_kind':function(newVal,oldVal){
			if (newVal != oldVal && oldVal != '') {
				this.changeDiscountKind()
			}
		},

		'form.if_superposition_discount':function(newVal,oldVal){ // 是否叠加为是，需要校验自动添加和优惠性质来校验是否每满
			if (newVal === 'Y' && oldVal === 'N') {
				this.checkIfEachFull()
				if(this.form.if_each_full == 'N'){
					this.form.if_offline_before = this.isOfflineBefore ? 'N' : this.form.if_offline_before;
				}
			} else if (newVal === 'N' && oldVal === 'Y') {
				this.form.if_each_full = 'N'
			}
		},
		'form.if_amount_change':function(newVal,oldVal){ // 金额可变为是，自动添加为否
			if (newVal === 'Y' && oldVal === 'N') {
				this.form.if_auto_add = 'N'
			}

			if (newVal === 'Y' && oldVal === 'N') { //金额可变为为否，线下前置为否
				this.form.if_offline_before = this.isOfflineBefore ? 'N' : this.form.if_offline_before
			}
		},
		'form.if_act_winning':function(newVal,oldVal){ // 维护中奖名单为是，自动添加为否
			if (newVal === 'Y' && oldVal === 'N') {
				this.form.if_auto_add = 'N'
			}
			if (newVal === 'Y' && oldVal === 'N') { //维护中奖名单为否，线下前置为否
				this.form.if_offline_before = this.isOfflineBefore ? 'N' : this.form.if_offline_before
			}
		},
		'form.if_need_audit' :function(newVal,oldVal){ // 需要审核为是，自动添加为否
			if (newVal === 'Y' && oldVal === 'N') {
				this.form.if_auto_add = 'N'
			}
		},
		'form.if_each_full' :function(newVal,oldVal){ // 需要每满为否，线下前置为否
			if (newVal === 'N' && oldVal === 'Y') {
				this.form.if_offline_before = this.isOfflineBefore ? 'N' : this.form.if_offline_before
			}
		},
		'form.discount_category' :function(newVal,oldVal){ // 需要审核为是，自动添加为否
			if (newVal != oldVal && !!oldVal) {
				this.changeDiscountCategory(this.form.discount_category)
			}
		}
		},
	computed:{
    // 上架派券中心操作
    // 【金额优惠券的逻辑】
    // 0、优惠类型=线下优惠
    // 1、仅创建状态下可编辑。
    // 2、线下前置=否
    // 3、优惠子类型=优惠券、品类券和单品优惠。
    // 4、仅维护1个优惠条件和项目内容的优惠，超过1个不支持开启。
    // 5、项目细类=金额直减即为减元支持，非金额直减不支持
    // 6、优惠活动头 是否叠加 =否，若为是不支持开启。
    // 7、优惠项目 是否叠加 = 是，若为否不支持开启。
    // 【赠品优惠券的逻辑】
    // 0、优惠类型=赠品
    // 1、仅创建状态下可编辑。
    // 2、线下前置=否
    // 3、优惠子类型=单品赠品、订单赠品
    // 4、仅维护1个优惠条件，超过1个不支持开启。
    // 5、支持优惠项目支持多个
    // 6、支持满元，满件，多件，无门槛
    // 7、支持叠加优惠=是或否
    // 8、优惠项目 是否叠加 = 是，若为否不支持开启。
    canIChangeSyncCoupon(){
      if(this.form.discount_type==='OFFLINE_DISCOUNT'){
        return this.form.status==='CREATE'&&(this.form.if_offline_before==='N'||!this.form.if_offline_before)&&['NORMALOFFLINE_DISCOUNT','CATEGORY_COUPON_OFFLINE','GOODS_PROMOTION_OFFLINE'].includes(this.form.discount_sub_type)
        &&this.listActDiscountItemVo.length===1&&this.conditionVo.length===1&&this.listActDiscountItemVo[0].item_type_detail==='DEDUCTE_AMOUNT'&&(this.form.if_superposition_discount==='N'||!this.form.if_superposition_discount)&&this.listActDiscountItemVo[0].if_allow_superposition==='Y'
      }
      if(this.form.discount_type==='NEW_GIFT'){
        return this.form.status==='CREATE'&&(this.form.if_offline_before==='N'||!this.form.if_offline_before)&&['SINGLE_GIFT','TRADE_GIFT'].includes(this.form.discount_sub_type)
        &&['FULL_YUAN','FULL_PIECE','MULTI_PIECE','UNCONDITIONAL',].includes(this.form.discount_condition_type)
        &&this.listActDiscountItemVo.length>0&&this.conditionVo.length===1&&this.listActDiscountItemVo.every(item=>item.if_allow_superposition==='Y')
      }
      return false
    },
		// 优惠前置
		isOfflineBefore(){
      // 维护中奖名单为是 || 金额可变为是 || （ 叠加优惠为是 && 每满为否 && 优惠性质为拍减）
			return  this.form.if_act_winning == 'Y' || this.form.if_amount_change == 'Y'|| (this.form.if_superposition_discount == 'Y'&&this.form.if_each_full == 'N' && this.form.discount_kind == 'ORDER_CUT') || this.ifOfflineBeforeControlByDiscountKindAnd
		},
    // 若“优惠性质”为“拍减”，且 抖音团购优惠 为 “是”， 则“线下前置” 不可被操作，并且固定值为“否”；
    ifReduceShots(){
      return this.form.discount_kind == 'ORDER_CUT' &&  this.form.additional_act_content == 'DY_ORDER_COUPONS'
    },
    ifOfflineBeforeControlByDiscountKindAnd(){
      // 优惠条件为无条件 且 抖音团购活动为否
      let ifDyAndDiscountConditionType = this.form.discount_condition_type === 'UNCONDITIONAL' && this.form.additional_act_content !== 'DY_ORDER_COUPONS'
      // 优惠性质为付返
      let ifPaidReturn = this.form.discount_kind === 'PAID_RETURN'
      // 优惠性质为拍减 且 优惠条件为无条件
      let ifOrderCut = this.form.discount_kind === 'ORDER_CUT' && this.form.discount_condition_type === 'UNCONDITIONAL'
      // 优惠性质为拍赠  且  优惠条件为无条件 且 抖音团购活动为否
      let ifOrderPresent = this.form.discount_kind === 'ORDER_PRESENT' && ifDyAndDiscountConditionType
      // 优惠性质为付赠 且  优惠条件为无条件 且 抖音团购活动为否
      let ifPaidPresent = this.form.discount_kind === 'PAID_PRESENT' && ifDyAndDiscountConditionType
		  let ifOfflineBeforeControlByDiscountKindAnd = ifPaidReturn || ifOrderCut || ifOrderPresent || ifPaidPresent
      if (ifOfflineBeforeControlByDiscountKindAnd) {
        this.form.if_offline_before = 'N'
      }
      return ifOfflineBeforeControlByDiscountKindAnd
    },
		ifChange(){
			// 是否变更未保存
			return this.form.if_change == 'Y'?true:false;
		},
		if_enable_time(){
			if(this.form.enable_time){
				return new Date().getTime() > this.form.enable_time ? true : false;
			}else{
				return false;
			}
		},
		store_area(){
			return this.form.store_area;
		},
		listBtnControl(){
			return !(this.form.status == "CREATE"  || this.form.status == "REJECTED")
		},
		// 创建和驳回状态可修改 || 单据状态为copy则禁用
		formDIsabledControl(){
			return !( this.form.status == "CREATE" || this.form.status == "REJECTED") || this.form.document_type == 'COPY'
		},
		// 优惠活动状态不为“创建”或“驳回”时，“维护中奖名单”按钮不允许操作
		formDIsabledControl4(){
			return !( this.form.status == "CREATE" || this.form.status == "REJECTED") || this.form.document_type == 'COPY'
		},
		formDIsabledControl2(){
			return !( this.form.status == "CREATE" || this.form.status == "REJECTED"|| this.form.status == "APPROVED") || this.form.document_type == 'COPY'
		},
		// 创建状态可修改 || 单据状态为copy则禁用
		formDIsabledControl3(){
			return !( this.form.status == "CREATE") || this.form.document_type == 'COPY'
		},
		// 允许变更条件：1 存在未被变更单 2 已审核未生效 3只能被点击一次
		change_status_control(){
			return this.form.document_type == "COPY"|| !((this.form.status == "APPROVED"||this.form.status == "FORBID") && this.form.enable_time &&new Date().getTime() < this.form.enable_time) || !!this.if_change_once || (!!this.form.change_status && this.form.change_status != 'CHANGE_APPROVED')
		},
		formDIsabledItemControl(row){
			return !( this.form.status == "CREATE" || this.form.status == "REJECTED" || (this.form.status == "APPROVED" && !!row.if_last_order_in_condition)) || this.form.document_type == 'COPY'
		},
		item_enable_options_disabled(){
			return (this.form.discount_sub_type == 'NORMALOFFLINE_DISCOUNT' || this.form.discount_sub_type == 'ORDER_DISCOUNT_REFUND'  || this.form.discount_sub_type == 'SHOP_FOUNDATION_REFUND'  || this.form.discount_sub_type == 'OUTSOURCE_FOUNDATION_REFUND' || this.form.discount_sub_type == 'GOODS_PROMOTION_REFUND' || this.form.if_auto_add == 'Y')
		},
		// 自动添加不可选(优惠性质不为付返|拍减 || 金额可变 || 维护中奖名单 || 优惠条件为无条件) || 辅助资料SALE_ORDER中的开关
		isAddAuto() {
			return /^(ORDER_PRESENT|PAID_PRESENT)$/.test(this.form.discount_kind) || this.form.if_amount_change === 'Y' || this.form.if_act_winning === 'Y' || this.form.discount_condition_type === 'UNCONDITIONAL' || this.autoDiscountSwitch === "0" || (this.form.if_each_full == 'N' && this.form.if_superposition_discount == 'Y')
		},
		// 每满状态不可选
		ifEachFullDisabled() {
			return (this.form.if_auto_add == 'Y' && /^(PAID_RETURN|ORDER_CUT)$/.test(this.form.discount_kind) && this.form.if_superposition_discount == 'Y')
		},
		// 是否复制
		ifCopy () {
			return this.form.document_type == 'COPY'
		},
		ifCreateAndRejectOfStatus () {
			return /^(CREATE|REJECTED)$/.test(this.form.status)
		},
		ifApprovedOfStatus () {
			return /^(APPROVED)$/.test(this.form.status)
		}
	}
}
</script>
<style>
	.discount_blue{
		background-color: #dee5f0 !important;
	}
	.discount_blue td {
		background-color: inherit !important;
	}
	/* .discount-list .xpt-pagation{
		position: absolute !important;
	}
	.discount-list .el-table .xpt-flex__bottom{
		height: 230px;
		padding-bottom: 31px !important;
	} */
</style>
<style lang="stylus">
// .scroll .el-table .xpt-flex__bottom, .scroll .xpt-flex .xpt-flex__bottom
// 	    overflow: auto;
	.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
		overflow: auto;
	.discount-list
		.xpt-flex__bottom
			.xpt-pagation
				position: absolute;
		.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
			overflow: hidden
			padding-bottom: 35px
	.discount-list
		.xpt-flex .el-table__body-wrapper, .el-table .el-table__body-wrapper
			min-height: 100px
			max-height: 150px
			overflow-x: hidden

</style>
