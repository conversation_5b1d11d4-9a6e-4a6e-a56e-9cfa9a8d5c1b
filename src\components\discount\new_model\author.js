/*
优惠活动--作者账号
*/
import Fn from '@common/Fn';
export default {
	data() {
		let self = this

		this._getAllAuthorsSearchData.data = []
		this._getAllAuthorsSearchData.del = []

		return {
			systemAuthorAllList: [],
			systemAuthorEnableList: [],
			authorBtns: [
				{
					type: 'primary',
					txt: '添加账号',
					disabled: ()=> {
						// 附加设置为作者账号活动 &&（不是复制新增 || 创建、驳回、已审核状态可用）
						return !(self.form.additional_act_content == "AUTHOR_ACTIVITY" && (self.ifCopy || self.ifCreateAndRejectOfStatus || self.ifApprovedOfStatus))
					},
					click: self.authorAdd
				}, {
					type: 'danger',
					txt: '删除',
					disabled: ()=> {
						return !(self.form.additional_act_content == "AUTHOR_ACTIVITY" && (self.ifCopy || self.ifCreateAndRejectOfStatus))
					},
					click: self.authorDel
				}, // {
				// 	type: 'info',
				// 	txt: '失效',
				// 	disabled: ()=> {
				// 		return !(self.form.additional_act_content == "AUTHOR_ACTIVITY" && (self.ifCopy || self.ifApprovedOfStatus))
				// 	},
				// 	click: self.authorInvalid
				// }
			],
			authorCols: [
				{
					label: '作者ID',
					prop: 'author_id'
				}, {
					label: '作者名称',
					prop: 'author_name',
					width:300
				}, {
					label: '关联店铺',
					prop: 'shop_name',
				}, {
					label: '生效状态',
					prop: 'status',
					formatter: (val) => {
						switch(val){
							case 1 : return '生效';
							case 0 : return '失效';
						}
					}
				}, {
					label: '创建人',
					prop: 'create_name'
				}, {
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 150,
				}, {
					label: '失效时间',
					prop: 'disable_time',
					format: 'dataFormat1'
				}, {
					label: '失效人',
					prop: 'disable_name'
				}
			],
			// 多选数据
			authorSelects: '',
            listPmsActAuthorVO: [],
			// 总记录数
			authorCount: 0,
            markListPmsAuthorVOData: [],
			authorSearch:{
				page_name: "act_discount_expand_author_account",
				where: [],
				page_size: self.pageSize,
				page_no: 1,
				discount_id: ''
			},
            author_activity_option: [
                {
                    value: "A",
                    label: "A：限定作者可享受，无作者也可享受"
                },
                {
                    value: "B",
                    label: "B：限定作者可享受，无作者不可享受"
                },
                {
                    value: "C",
                    label: "C：限定作者不可享受，无作者可享受"
                },
                {
                    value: "D",
                    label: "D：限定作者不可享受，无作者不可享受"
                },
            ],
			old_additional_act_content: '',
			old_additional_flag_copy: ''
		}
	},
	methods: {
		// 添加账号
		authorAdd() {
			let params = {
				selection: "checkbox",
				source: 'discount'
			};
			let self = this;
			let p = Math.ceil(self.count/self.authorSearch.page_size)
			// 选择账号,可以多选
			params.close = d => {
				let dataSet = new Set(),
					hasDuplicate = [];
				self.listPmsActAuthorVO.find(list => {
					if (list.status ===  1) {
						dataSet.add(list.expand_value_id)
					}
				})
				d.find(l => {
					if(!dataSet.has(l.id)) {
						self.listPmsActAuthorVO.push({
							author_id:l.author_id,
							author_name:l.author_name,
							shop_name:l.relation_shop_name,
							status:1,
							row_delete_flag: "N",
							discount_id: self.form.discount_id,
							expand_value_id: l.id,
							tempId: new Date()
						})
					} else {
						hasDuplicate.push(l.shop_name)
					}
				})
				if(hasDuplicate.length) {
					let msg = hasDuplicate.join('、') + '作者账号已添加';
					if(hasDuplicate.length>3) {
						let len = hasDuplicate.length;
						hasDuplicate.length = 3;
						msg = hasDuplicate.join('、') + '等' + len + '个作者账号已添加';
					}
					self.$message.info(msg);
				}
			};
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/livePromotion/list'),
				style:'width:800px;height:500px',
				title:'作者账号列表'
			});
		},
		// 店铺失效
		authorInvalid() {
			let self = this
			if(self.authorSelects.length==0) {
				self.$message.error('请选择要失效的作者账号')
				return
			}
			// 判断是否有新增没有保存的数据
			let bool = self.authorSelects.some((v) => {
				return (typeof v.discount_expand_id === 'undefined' || v.discount_expand_id === null || v.discount_expand_id === '')
			});
			if(bool) {
				self.$message.error('请保存后再进行失效操作');
				return;
			}
			// 变更所选数据的状态
			let isAllInvalid = true;
			self.authorSelects.map(v => {
				if(v.status === 1) {
					v.status = 0
					isAllInvalid = false;
				}
			})
			if(isAllInvalid) {
				this.$message.info('已经失效的作者账号无需再次失效');
			} else {
				this.$message.success('失效成功')
			}
			// 清除选中状态
			this.$refs.ShopList.clearSelection()
		},
		// 删除作者账号
		authorDel() {
			var self = this;
			if(self.authorSelects.length==0) {
				self.$message.error('请选择要删除的作者账号')
				return
			}else if (this.authorSelects.some(obj => obj.status === '0')){
				this.$message.error('不能删除失效的作者账号')
				return
			}

			this.authorSelects.forEach( (good, idx) => {
				if (good.discount_expand_id) {
					good.row_delete_flag = "Y"
					self._getAllAuthorsSearchData.del.push(good);
					let list = self.listPmsActAuthorVO
					let len = list.length
					while(len--) {
						if (list[len].expand_value_id === good.expand_value_id && list[len].discount_expand_id === good.discount_expand_id) {
							self.listPmsActAuthorVO.splice(len, 1)
						}
					}
				} else if (good.tempId){
					let list = self.listPmsActAuthorVO
					let len = list.length
					while(len--) {
						if (list[len].expand_value_id === good.expand_value_id && list[len].tempId === good.tempId) {
							self.listPmsActAuthorVO.splice(len, 1)
						}
					}
				}
			})
		},
		// 删除全部数据
		authorAllDel(){
			let self = this
			let i = this.listPmsActAuthorVO.length
			while(i--) {
				if(self.listPmsActAuthorVO[i].discount_expand_id) {
					self.listPmsActAuthorVO[i].row_delete_flag = 'Y';
					self._getAllAuthorsSearchData.del.push(self.listPmsActAuthorVO[i]);
				}
			}
			self.listPmsActAuthorVO = []
		},
		authorPageSizeChange(size) {
			this.isListPmsAuthorChange()
			this.authorSearch.page_size = size;
			this.authorSearching()
		},
		authorPageChange(page_no) {
			this.isListPmsAuthorChange()
			this.authorSearch.page_no = page_no
			this.authorSearching()
		},
		authorSelectionChange(selectArr) {
			this.authorSelects = selectArr
		},
		authorPresearch(list, resolve) {
			this.isListPmsAuthorChange(resolve)
			this.authorSearch.where = list;
			this.authorSearching(resolve);
		},
		// 判断当前页的数据是否有修改
		isListPmsAuthorChange (resolve){
			if(this.compareData(this.listPmsActAuthorVO, this.markListPmsAuthorVOData)){
				resolve && resolve()
				this.$message.error('当前页有修改，请先保存！')
				throw('当前页有修改，请先保存！')
			}
		},
		checkAuthorsOf() {
			return new Promise((reslove, reject) => {
				let postData = {
					discount_id: this.form.discount_id,
					page_name: "act_discount_expand_author_account",
					page_no: 1,
					page_size: 500,
					where: [],
				}, self = this
				this.ajax.postStream('/price-web/api/actDiscount/listExpandAuthorAccount', postData, res => {
					if(res.body.result && res.body.content) {
						let list = res.body.content.list || [];
						let systemAuthorEnableList = []
						list.forEach(author => {
							if(author.expand_value_id && author.status == 1) {
								systemAuthorEnableList.push(author)
							}
						})
						self.systemAuthorAllList = list
						self.systemAuthorEnableList = systemAuthorEnableList
						reslove && reslove()
					} else {
						self.systemAuthorAllList = []
						self.systemAuthorEnableList = []
						reject && reject()
					}
				}, () => {
					self.systemAuthorAllList = []
					self.systemAuthorEnableList = []
					reject && reject()
				})
			})
		},
		// 每一次刷新优惠商品时都要那一次全部数据，用于保存时(XPT-8565对接口bug的补丁)
		// 每次新增、删除、失效(修改行状态)都要修改this._getAllAuthorsSearchData.data
		_getAllAuthorsSearchData (){
			this.authorSearch.discount_id = this.form.discount_id
			this.ajax.postStream('/price-web/api/actDiscount/listExpandAuthorAccount',this.authorSearch, res => {
				this._getAllAuthorsSearchData.del = []
				if(res.body.result){
					this.authorCount = res.body.content.count || 0;
					this.listPmsActAuthorVO = res.body.content.list;
					this.markListPmsAuthorVOData = JSON.parse(JSON.stringify(res.body.content.list))
				}else{
					this.$message.error(res.body.msg);
				}
			}, () => {
				this._getAllAuthorsSearchData.del = []
			})
		},
		authorSearching(resolve) {
			let _this = this
			if(_this.ifChange){
				this.$message.error('未保存前不可查询');                
                resolve && resolve();
                return
            } 
			resolve && resolve()
			this._getAllAuthorsSearchData()
		},
	}
}