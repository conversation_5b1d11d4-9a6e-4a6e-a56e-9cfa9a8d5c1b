/*
优惠活动--优惠条件
*/
import Fn  from '@common/Fn.js'
import { promise } from 'when';
export default {
	data() {
		let self = this

		return {
			ifGetAllData:false,
            conditionBtns:[
				{
					type: 'info',
					txt: '添加条件',
					disabled() {
						return self.listBtnControl;
					},
					// disabled(){
					// 	return self.listBtnControl
					// },
					click: self.addCondition
				},
				{
					type: 'danger',
					txt: '删除',
					disabled() {
						return self.listBtnControl;
					},
					// disabled(){
					// 	return self.listBtnControl
					// },
					click: self.delCondition
				},
				{
					type: 'warning',
					txt: '还原',
					// disabled: self.isAdd,
					disabled(){
						return !((self.form.status == "CREATE" ||self.form.status == 'REJECTED')&&self.form.document_type == 'COPY')
					},
					click: self.revertCondition
				},
				{
					type: 'warning',
					txt: '取消',
					// disabled: self.isAdd,
					disabled(){
						return !((self.form.status == "CREATE" ||self.form.status == 'REJECTED')&&self.form.document_type == 'COPY')
                    },
					click: self.cancelCondition
				},{
					type: 'info',
					txt: '失效',
					disabled(){
						return !(self.form.status == "APPROVED"  && self.if_enable_time )
                    },
					click: self.disableCondition
				},
			],
			conditionColType:{
				FULL_YUAN(){
					let arr1 = [
						{
							label: '优惠条件ID',
							prop: 'discount_condition_id',
							width: 130
						},
						{
							label: '金额门槛',
							slot: 'threshold_price',
							width: 150,
						},
						{
							label: '优惠选项',
							slot: 'item_enable_options',
							width: 150,

						},
						{
							label: '选项项数',
							slot: 'item_enable_count',
							width: 150,
						},

					]
					let arr2 = [{
						label: '变更类型',
						prop: 'change_type',
						formatter(val){
							switch(val){
								case 'ADD': return '新增';
                case 'CANCEL': return '取消';
                case 'RETAIN' : return '保留';
              }
						}
					},{
						label: '行状态',
						prop: 'row_status',
						formatter(val){
							switch(val){
								case 0 : return '生效';
                case 1 : return '失效';
              }
						}
					},
					{
						label: '操作',
						slot: 'discount_id',
						width: 200,
					}];
					let orderConfigArr = self.roleConfig.if_condition_order == 'Y'?[{
						label: '中奖顺序',
						slot: 'condition_order_config',
						width: 150,
					}]:[];
					let houlyConfigArr = self.roleConfig.if_zhengdian == 'Y'?[{
						label: '整点配置',
						slot: 'houly_config',
						width: 150,
					}]:[];
					return arr1.concat(orderConfigArr).concat(houlyConfigArr).concat(arr2);
				},
				UNCONDITIONAL(){
					let arr1 = [
						{
							label: '优惠条件ID',
							prop: 'discount_condition_id',
							width: 130
						},
						{
							label: '金额门槛',
							prop: 'threshold_price',
							width: 150,
						},
						{
							label: '件数门槛',
							prop: 'threshold_count',
							width: 150,
						},
						{
							label: '优惠选项',
							slot: 'item_enable_options',
							width: 150,
						},
						{
							label: '选项项数',
							slot: 'item_enable_count',
						},{
							label: '操作',
							slot: 'discount_id',
							width: 200
						}
					];
					let arr2 =[{
						label: '变更类型',
						prop: 'change_type',
						formatter(val){
							switch(val){
								case 'ADD': return '新增';
                case 'CANCEL': return '取消';
                case 'RETAIN' : return '保留';
              }
						}
						},
						{
							label: '行状态',
							prop: 'row_status',
							formatter(val){
								switch(val){
									case 0 : return '生效';
                  case 1 : return '失效';
                }
							}
						},]
						let orderConfigArr = self.roleConfig.if_condition_order == 'Y'?[{
							label: '中奖顺序',
							slot: 'condition_order_config',
							width: 150,
						}]:[];
						let houlyConfigArr = self.roleConfig.if_zhengdian == 'Y'?[{
							label: '整点配置',
							slot: 'houly_config',
							width: 150,
						}]:[];
						return arr1.concat(orderConfigArr).concat(houlyConfigArr).concat(arr2);
				},
				FULL_PIECE(){
					let arr1 = [
						{
							label: '优惠条件ID',
							prop: 'discount_condition_id',
							width: 130
						},
						{
							label: '件数门槛',
							slot: 'threshold_count',
						},{
							label: '金额门槛',
							slot: 'threshold_price',
							width: 150,
						},{
							label: '优惠选项',
							slot: 'item_enable_options',
							width: 150,
						},{
							label: '选项项数',
							slot: 'item_enable_count',
							width: 150,
						},];
						let arr2 = [{
							label: '变更类型',
							prop: 'change_type',
							formatter(val){
								switch(val){
									case 'ADD': return '新增';
                  case 'CANCEL': return '取消';
                  case 'RETAIN' : return '保留';
                }
							}
							},{
								label: '操作',
								slot: 'discount_id',
								width: 200,
							},{
							label: '行状态',
							prop: 'row_status',
							formatter(val){
								switch(val){
									case 0 : return '生效';
                  case 1 : return '失效';
                }
							}
						},]
						let orderConfigArr = self.roleConfig.if_condition_order == 'Y'?[{
							label: '中奖顺序',
							slot: 'condition_order_config',
							width: 150,
						}]:[];
						let houlyConfigArr = self.roleConfig.if_zhengdian == 'Y'?[{
							label: '整点配置',
							slot: 'houly_config',
							width: 150,
						}]:[];
						return arr1.concat(orderConfigArr).concat(houlyConfigArr).concat(arr2);
				}
			},

			conditionCol:[
				{
					label: '优惠条件ID',
					prop: 'discount_condition_id',
					width: 130
				},
				{
					label: '金额门槛',
					slot: 'threshold_price',
					width: 150,
					// bool(row,e){
					// 	console.log(row,e)
					// },
					// elInput:true,
					// disabled(row) {
					// 	return false;
					// },
				},
				{
					label: '优惠选项',
					slot: 'item_enable_options',
					width: 150,

				},
				{
					label: '选项项数',
					slot: 'item_enable_count',
					width: 150,
                },
				{
					label: '操作',
					slot: 'discount_id',
					width:190
				},{
					label: '行状态',
					prop: 'row_status',
					formatter(val){
						switch(val){
							case 0 : return '生效';
              case 1 : return '失效';
            }
					}
                },{
					label: '变更类型',
					prop: 'change_type',
					formatter(val){
						switch(val){
							case 'ADD' : return '新增';
              case 'CANCEL' : return '取消';
              case 'RETAIN' : return '保留';
            }
					}
                },{
                    label: '创建时间',
                    prop: 'create_time',
					width:300,
					format:'dataFormat1'
                },{
				label: '失效人',
				prop: 'disable_person_name',
                }, {
                    label: '失效时间',
					width:300,
					prop: 'disable_time',
                    format:'dataFormat1'
                },

			],
            item_enable_options:[
				{
					label: '任选一项',
					value: 'CHOOSEONEITEM',
					disabled(){
						return false;
					}
				},{
					label: '任选',
					value: 'OPTIONAL',
					disabled(){
						return self.item_enable_options_disabled || ['ORDER_CUT','PAID_RETURN'].includes(self.form.discount_kind)
					}
				},{
					label: '全选',
					value: 'ALLSELECTION',
					// disabled(){
					// 	return (self.form.discount_sub_type == 'NORMALOFFLINE_DISCOUNT' || self.form.discount_sub_type == 'ORDER_DISCOUNT_REFUND' || self.form.discount_sub_type == 'GOODS_PROMOTION_REFUND')
					// }
					disabled(){
						return self.item_enable_options_disabled || ['ORDER_CUT','PAID_RETURN'].includes(self.form.discount_kind)
					}
					// disabled:self.item_enable_options_disabled

				},
            ],

        }
	},
	methods: {
		// 添加优惠项目
		addNewOrder(row){
			// console.log(row.change_type)
			let self = this;
			let if_change_add = self.form.document_type == 'COPY' && (self.form.status == 'CREATE' || self.form.status == 'REJECTED');
			// console.log(if_change_add);
			let conditionList = self.listActDiscountItemVo.filter(item => {
				return item.discount_condition_id == row.discount_condition_id && row.row_status === 0
			})
			if (this.form.if_auto_add == 'Y' && conditionList.length > 0) {
				this.$message.error('自动添加的优惠活动一个优惠条件只能创建一项优惠项目')
				return
			}
			if (this.form.if_offline_before == 'Y' && conditionList.length > 0 && !['ORDER_PRESENT', 'PAID_PRESENT'].includes(this.form.discount_kind)) {
				this.$message.error('线下前置的优惠活动一个优惠条件只能创建一项优惠项目')
				return
			}
			this.$root.eventHandle.$emit('alert',{
				title: '优惠项目新增',
				params:{
					row:row,
					form:self.form,
					listActDiscountItemVo:self.listActDiscountItemVo,
					callback(data){
						let conditionArr = [];
						console.log(data.selectStatus)
						if(data.selectStatus.ITEM_TYPE_DISCOUNT){
							conditionArr.push({
								"discount_condition_id":data.discount_condition_id,
								"discount_item_type":'ITEM_TYPE_DISCOUNT',//优惠项目编码
								"item_type_detail":data.item_type_detail['ITEM_TYPE_DISCOUNT'],//项目细类
								"subtract_money":null,
								"discount":data.data.discount.value,
								max_amount:data.data.discount.max_amount,
								"material_id":null,
								"material_number":null,
								if_gift:"N",
								"if_present":"N",
								"act_price":null,
								"cost_price":null,
								"three_reduction":null,
								"logistics_reduction":null,
								"dealer_price":null,
								"count_limit":null,
								"person_bear_ratio":null,
								"if_enjoy_discount":null,
								"row_status":0,
								// discount_amount:data.discount_amount,
								"disable_person_id":null,
								"disable_person_name":null,
								discount_item_no:'',
                                change_type:if_change_add?'ADD':'',
								"disable_time":null,
								  discount_condition_dec:self.formatCondition(data,row),
								  discount_item_dec:self.formatItem(data,'ITEM_TYPE_DISCOUNT'),
								  nominal_price:null,
								  if_allow_superposition:data.selectSuperposition.ITEM_TYPE_DISCOUNT?"Y":"N",
								  win_order:'',
									win_quota:'',
								  // if_convert_into_cash:'',
									"remark":""

							})
						}
						if(data.selectStatus.ITEM_TYPE_DEDUCTE_AMOUNT){
							conditionArr.push({
								"discount_condition_id":data.discount_condition_id,
								"discount_item_type":'ITEM_TYPE_DEDUCTE_AMOUNT',//优惠项目编码
								"item_type_detail":data.item_type_detail['ITEM_TYPE_DEDUCTE_AMOUNT'],//项目细类
								"subtract_money":data.data.subtract_money.value,
								max_amount:data.data.subtract_money.max_amount,
								service_project_no:data.data.subtract_money.service_project_no,
								"discount":null,
								"material_id":null,
								"material_number":null,
								if_gift:"N",
								"if_present":"N",
								"act_price":null,
								"cost_price":null,
								"three_reduction":null,
								"logistics_reduction":null,
								"dealer_price":null,
								"count_limit":null,
								"person_bear_ratio":null,
								"if_enjoy_discount":null,
								"row_status":0,
								"disable_person_id":null,
								"disable_person_name":null,
								"disable_time":null,
                                change_type:if_change_add?'ADD':'',
								discount_item_no:'',
								discount_condition_dec:self.formatCondition(data,row),
								discount_item_dec:self.formatItem(data,'ITEM_TYPE_DEDUCTE_AMOUNT'),
								discount_amount:null,
								nominal_price:null,
								// if_convert_into_cash:'',
								win_order:'',
									win_quota:'',
								if_allow_superposition:data.selectSuperposition.ITEM_TYPE_DEDUCTE_AMOUNT?"Y":"N",
								"remark":""
							})
						}
						if(data.selectStatus.ITEM_TYPE_GIFTS){ // 赠品
							data.data.giftsData.forEach(item=>{
								conditionArr.push({
									"discount_condition_id":data.discount_condition_id,
									"discount_item_type":'ITEM_TYPE_GIFTS',//优惠项目编码
									"item_type_detail":data.item_type_detail['ITEM_TYPE_GIFTS'],//项目细类
									"subtract_money":null,
									max_amount:null,
									"discount":null,
									"material_id":item.material_id,
									"material_number":item.material_number,
									"material_name":item.material_name,
									"material_specification":item.material_specification,
									"material_unit":item.material_unit,
									if_gift:"Y",
									"if_present":item.if_present,
									"act_price":item.act_price|| 0 ,
									"cost_price":item.cost_price || 0,
									"three_reduction":item.three_reduction,
									"logistics_reduction":item.logistics_reduction,
									"dealer_price":item.dealer_price,
									"count_limit":item.count_limit|| 0,
									"person_bear_ratio":item.person_bear_ratio,
									"if_enjoy_discount":item.if_enjoy_discount,
									"row_status":0,
									change_type:if_change_add?'ADD':'',
									discount_item_no:'',
									"disable_person_id":null,
									"disable_person_name":null,
									"disable_time":null,
									discount_condition_dec:self.formatCondition(item,row),
									discount_item_dec:self.formatItem(item,'ITEM_TYPE_GIFTS'),
									discount_amount:item.discount_amount,
									nominal_price:item.nominal_price,
									// if_convert_into_cash:item.if_convert_into_cash,
									if_allow_superposition:data.selectSuperposition.ITEM_TYPE_GIFTS?"Y":"N",
									"remark":item.remark,
									win_order:'',
									win_quota:'',
								})
							})
						}
						if(data.selectStatus.ITEM_TYPE_UPGRADE){ //升级换购
							data.data.updateData.forEach(item=>{
								conditionArr.push({
									"discount_condition_id":data.discount_condition_id,
									"discount_item_type":'ITEM_TYPE_UPGRADE',//优惠项目编码
									"item_type_detail":data.item_type_detail['ITEM_TYPE_UPGRADE'],//项目细类
									"subtract_money":null,
									"discount":null,
									max_amount:null,
									"material_id":item.material_id,
									"material_number":item.material_number,
									"material_name":item.material_name,
									"material_specification":item.material_specification,
									"material_unit":item.material_unit,
									if_gift:"N",
									"if_present":item.if_present,
                                    change_type:if_change_add?'ADD':'',
									"act_price":item.act_price|| 0,
									"cost_price":item.cost_price|| 0,
									"three_reduction":item.three_reduction,
									"logistics_reduction":item.logistics_reduction,
									"dealer_price":item.dealer_price,
									"count_limit":item.count_limit|| 0,
									"person_bear_ratio":item.person_bear_ratio,
									"if_enjoy_discount":item.if_enjoy_discount,
									"row_status":0,
									"disable_person_id":null,
									"disable_person_name":null,
									"disable_time":null,
									  discount_condition_dec:self.formatCondition(item,row),
									  discount_item_dec:self.formatItem(item,'ITEM_TYPE_UPGRADE'),
									discount_amount:null,
									nominal_price:null,
									win_order:'',
									win_quota:'',
								if_allow_superposition:data.selectSuperposition.ITEM_TYPE_UPGRADE?"Y":"N",
								//if_convert_into_cash:'',
									"remark":item.remark
								})
							})
						}
						if(data.selectStatus.ITEM_TYPE_GOODS_PROMOTION){
							data.data.singleData.forEach(item=>{
								conditionArr.push({
									"discount_condition_id":data.discount_condition_id,
									"discount_item_type":'ITEM_TYPE_GOODS_PROMOTION',//优惠项目编码
									"item_type_detail":data.item_type_detail['ITEM_TYPE_GOODS_PROMOTION'],//项目细类
									"subtract_money":null,
									max_amount:null,
									"discount":null,
									"material_id":item.material_id,
									"material_number":item.material_number,
									"material_name":item.material_name,
									"material_specification":item.material_specification,
									"material_unit":item.material_unit,
									if_gift:"N",
									"if_present":item.if_present,
									"act_price":item.act_price|| 0,
									"cost_price":item.cost_price|| 0,
									"three_reduction":item.three_reduction,
									"logistics_reduction":item.logistics_reduction,
									"dealer_price":item.dealer_price,
									"count_limit":item.count_limit|| 0,
									"person_bear_ratio":item.person_bear_ratio,
									"if_enjoy_discount":item.if_enjoy_discount,
									"row_status":0,
                                    change_type:if_change_add?'ADD':'',
									"disable_person_id":null,
									"disable_person_name":null,
									"disable_time":null,
									discount_condition_dec:self.formatCondition(item,row),
									discount_item_dec:self.formatItem(item,'ITEM_TYPE_GOODS_PROMOTION'),
									discount_amount:item.discount_amount|| 0,
									nominal_price:null,
									win_order:'',
									win_quota:'',
									//if_convert_into_cash:'',
								if_allow_superposition:data.selectSuperposition.ITEM_TYPE_GOODS_PROMOTION?"Y":"N",
								"remark":item.remark
								})
							})
						}

						conditionArr.forEach(item=>{
							console.log(item)
							self.listActDiscountItemVo.push(item);
						})

					},
				},

				style: 'width:930px; height: 600px',
				component:()=>import('@components/discount/getDiscountItem.vue')
			})
		},
		formatCondition(item,row){
			let self = this,keyWord = '';
			// if(item.discount_condition_id == val){
				if(self.form.discount_condition_type == 'FULL_YUAN'){
					keyWord = '满'+row.threshold_price+'元';
				}else if(self.form.discount_condition_type == 'FULL_PIECE'||self.form.discount_condition_type =='MULTI_PIECE'){
					keyWord = '满 '+row.threshold_count+' 件';
				}else{
					keyWord = '无条件'
				}

			// }
			return keyWord;

		},
		formatItem(row,tpye){
			let self = this,keyWord = '';
			console.log(row,'row');
			switch(tpye){
				case 'ITEM_TYPE_DEDUCTE_AMOUNT' : keyWord ='减'+row.data.subtract_money.value+'元'; break;
				case 'ITEM_TYPE_DISCOUNT' :keyWord ='折扣'+row.data.discount.value+'%'; break;
				case 'ITEM_TYPE_GIFTS' : keyWord ='赠送'+ row.material_number; break;
				case 'ITEM_TYPE_UPGRADE' :keyWord ='换购'+row.material_number; break;
				case 'ITEM_TYPE_GOODS_PROMOTION' : keyWord ='单品优惠'+row.material_number; break;
			}
			return keyWord;

		},
        revertCondition(row){
			let self = this;
			if(!self.conditionSelect){
				self.$message.error('请选择需要操作的明细行');
				return false;
			}
			let ifEnable = false;
			this.conditionVo.forEach(item=>{
				if(item.row_status == 0){
					ifEnable = true;
				}
			})
			if(this.form.if_superposition_discount == 'Y' && this.form.if_each_full == 'Y' &&this.conditionVo.length >=1 && ifEnable){
				this.$message.error('每满优惠只能添加一个有效的优惠条件，不能还原')
				return false;
			}
			if(this.form.discount_condition_type == 'UNCONDITIONAL' &&this.conditionVo.length >=1&& ifEnable){
				this.$message.error('优惠条件为无条件只能添加一个有效的优惠条件，不能还原')
				return false;
			}
            if(self.conditionSelect.change_type && self.conditionSelect.change_type == 'CANCEL'){

				// self.revertItem(self.conditionSelect.discount_condition_id)
				// self.revertGoods(self.conditionSelect.discount_condition_id)
				new Promise((resolve,reject)=>{
					self.getAllData(resolve);
				}).then(()=>{
					self.conditionSelect.change_type = 'RETAIN';
                	self.conditionSelect.row_status = 0;
					self.revertItem(self.conditionSelect.discount_condition_id);
					self.revertGoods(self.conditionSelect.discount_condition_id);
				})
            }else{
                self.$message.error('选中条件状态为取消才能还原');
            }
		},
		cancelCondition(){
			let self = this;
			if(!self.conditionSelect){
				self.$message.error('请选择需要操作的明细行');
				return false;
			}
			if(!(self.conditionSelect.change_type =='RETAIN')){
				self.$message.error('只有保留状态可取消');
				return false;
			}
			self.$confirm('取消保留条件，对应优惠保留优惠项目将被取消，新增优惠项目将被删除，是否继续？','提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(()=>{

				new Promise((resolve,reject)=>{
					self.getAllData(resolve);
				}).then(()=>{
					self.conditionSelect.change_type = 'CANCEL';
					self.conditionSelect.row_status = 1;
					self.cancelItem(self.conditionSelect.discount_condition_id);
					self.cancelGoods(self.conditionSelect.discount_condition_id);
				})

			  }).catch(()=>{

			  })

				// self.conditionVo.splice(self.conditionVo.indexOf(self.conditionSelect), 1)
				return false;
		},
        // 删除条件
		delCondition(){
			let self = this;
			// console.log(self.conditionSelect)
			if(!self.conditionSelect){
				self.$message.error('请选择需要操作的明细行');
				return false;
			}
			if(!!self.conditionSelect.copy_version_id){
				self.$message.error('变更状态条件不能删除');
				return false;
			}
			self.$confirm('当前门槛涉及变更，相应的优惠项目是否清空再重新设置？','提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(()=>{
				new Promise((resolve,reject)=>{
					self.getAllData(resolve);
				}).then(()=>{
					if(!!self.conditionSelect.discount_condition_id){
						self.deleteItem(self.conditionSelect.discount_condition_id);
						self.deleteGoods(self.conditionSelect.discount_condition_id);
						self.conditionVo.splice(self.conditionVo.indexOf(self.conditionSelect), 1)
						if(!self.isCopyAdd && !self.ifChange){
							self.delConditionVo.push(self.conditionSelect);
						}
						}else{
							self.conditionVo.splice(self.conditionVo.indexOf(self.conditionSelect), 1)
						}

						self.conditionSelect = null;
						const backup = JSON.stringify(self.conditionVo);
						self.conditionVo = [];
						self.$nextTick(() => {
							self.conditionVo = JSON.parse(backup);
						});
					})
			  }).catch(()=>{
				  return false;
			  })


        },
        disableCondition(){
			let self =this;
			if(!self.conditionSelect){
				self.$message.error('请选择需要操作的明细行');
				return false;
			}

			new Promise((resolve,reject)=>{
				self.getAllData(resolve);
			}).then(()=>{
				this.conditionSelect.row_status = 1;
				self.listActDiscountItemVo.forEach(item=>{
					if(item.discount_condition_id == self.conditionSelect.discount_condition_id){
						item.row_status = 1;
					}
				})
				self.DiscountVo.forEach(item=>{
					if(item.discount_condition_id == self.conditionSelect.discount_condition_id){
						item.row_status = 1;
					}
				})
			})


        },
        // 添加条件
        addCondition(){
			let ifEnable = false;
			this.conditionVo.forEach(item=>{
				if(item.row_status == 0){
					ifEnable = true;
				}
			})
			if(this.form.if_superposition_discount == 'Y' && this.form.if_each_full == 'Y' &&this.conditionVo.length >=1 && ifEnable){
				this.$message.error('每满优惠只能添加一个有效的优惠条件')
				return false;
			}
			if(this.form.discount_condition_type == 'UNCONDITIONAL' &&this.conditionVo.length >=1&& ifEnable){
				this.$message.error('优惠条件为无条件只能添加一个有效的优惠条件')
				return false;
			}

      if(this.form.if_superposition_discount == 'Y' && this.form.if_offline_before == 'Y' && this.conditionVo.length >=1 && ['ORDER_PRESENT', 'PAID_PRESENT'].includes(this.form.discount_kind) && ifEnable){
				this.$message.error('优惠叠加、线下前置为是的优惠，拍赠、付赠只能添加一个有效的优惠条件')
				return false;
			}

			let self = this;
			let data = {
				// 优惠条件id
				// discount_condition_id:'',
				// 优惠头信息id
				discount_id:'',
				// 优惠活动编码
				discount_no:'',
				// 生效金额门槛
				threshold_price:'',
				// 门槛件数
				threshold_count:'',
				// 对应项目内容生效件数
				item_enable_count:1,
				// 优惠选项
				item_enable_options:'CHOOSEONEITEM',
				// 变更类型
				change_type:self.form.document_type == 'COPY'?'ADD':'',
				// 变更状态
				// change_status:'CHANGE_CREATE',
				// 单据类型
				document_type:'NORMAL',
                row_status:0,
				//时间戳
				create_time:'',
				changeFlag:true
			}
			if(this.form.discount_condition_type == 'UNCONDITIONAL'){
				// 生效金额门槛
				data.threshold_price = 0;
				// 门槛件数
				data.threshold_count = 0;
			}
			if(self.form.document_type == 'COPY' && self.form.status == 'CREATE'){
				self.getListConditonId().then(res=>{
					console.log(res)
					data.document_type = 'COPY'
				// return res;
					data.discount_condition_id = res;
					this.conditionVo.push(data);

				});
				// data.change_status = 'CHANGE_CREATE'
			}else if(self.isCopyAdd){
				self.getListConditonId().then(res=>{
					data.discount_condition_id = res;
					this.conditionVo.push(data);

				});
			}else{
				this.conditionVo.push(data);
			}
		},
		conditionRadioChange(row){
			this.conditionSelect = row;
			this.listActDiscountItemVo.forEach(item=>{
				if(row.discount_condition_id == item.discount_condition_id){
					item.color_sign = 'A'
				} else {
					item.color_sign = '';
				}
			})
			this.DiscountVo.forEach(item=>{
				if(row.discount_condition_id == item.discount_condition_id){
					item.color_sign = 'A'
				} else {
					item.color_sign = '';
				}
			})
			this.listActDiscountItemVo = JSON.parse(JSON.stringify(this.listActDiscountItemVo));
			this.DiscountVo = JSON.parse(JSON.stringify(this.DiscountVo));

        },
            // 删除优惠条件相关项目
        deleteItem(condition_id){
			let self = this;
			let listActDiscountItemVo =  JSON.parse(JSON.stringify(self.listActDiscountItemVo));

			// listActDiscountItemVo.forEach(item=>{
			// 	if(item.discount_condition_id == condition_id){
			// 		item.row_delete_flag = 'Y';
			// 		self.delListActDiscountItemVo.push(item);
			// 		self.listActDiscountItemVo.splice(self.listActDiscountItemVo.indexOf(item), 1);
			// 	}
			// })
			let i = self.listActDiscountItemVo.length;
			while(i--){
				if(self.listActDiscountItemVo[i].discount_condition_id == condition_id){
					// 判断是否复制新增，是复制新增直接删除
					if(self.isCopyAdd){
						self.listActDiscountItemVo.splice(i, 1);
					}else{
						self.listActDiscountItemVo[i].row_delete_flag = 'Y';
						self.delListActDiscountItemVo.push(self.listActDiscountItemVo[i]);
						self.listActDiscountItemVo.splice(i, 1);
					}

				}
			}
		},
		  // 删除优惠条件相关清单
		deleteGoods(condition_id){
			let self = this;
			let DiscountVo =  JSON.parse(JSON.stringify(self.DiscountVo));

			let i = self.DiscountVo.length;
			while(i--){
				if(self.DiscountVo[i].discount_condition_id == condition_id){
					// 判断是否复制新增，是复制新增直接删除
					if(self.isCopyAdd){
						self.DiscountVo.splice(i, 1);
					}else{
						self.DiscountVo[i].row_delete_flag = 'Y';
						self.delListDiscount.push(self.DiscountVo[i]);
						self.DiscountVo.splice(i, 1);
					}

				}
			}
        },
		// 取消优惠条件相关项目
        cancelItem(condition_id){
            let self = this;
            let listActDiscountItemVo =  JSON.parse(JSON.stringify(self.listActDiscountItemVo));
            // self.listActDiscountItemVo.forEach(item=>{
            //     if(item.discount_condition_id == condition_id){
            //         item.change_type = 'CANCEL';
            //         item.row_status = 1;
            //         // self.cancelListActDiscountItemVo.push(item);
            //         // self.listActDiscountItemVo.splice(self.listActDiscountItemVo.indexOf(item), 1);
            //     }
			// })
			let i = self.listActDiscountItemVo.length;
			while(i--){
				if(self.listActDiscountItemVo[i].discount_condition_id == condition_id){
					// 判断是否复制新增，是复制新增直接删除
					if(self.listActDiscountItemVo[i].change_type == 'RETAIN'){
						self.listActDiscountItemVo[i].change_type = 'CANCEL';
                    	self.listActDiscountItemVo[i].row_status = 1;
					}else if(self.listActDiscountItemVo[i].change_type == 'ADD'){
						self.listActDiscountItemVo[i].row_delete_flag = 'Y';
						self.delListActDiscountItemVo.push(self.listActDiscountItemVo[i]);
						self.listActDiscountItemVo.splice(i, 1);
					}

				}
			}
		},
		cancelGoods(condition_id){
            let self = this;
            let DiscountVo =  JSON.parse(JSON.stringify(self.DiscountVo));
			let i = self.DiscountVo.length;
			while(i--){
				if(self.DiscountVo[i].discount_condition_id == condition_id){
					// 判断是否复制新增，是复制新增直接删除
					if(self.DiscountVo[i].change_type == 'RETAIN'){
						self.DiscountVo[i].change_type = 'CANCEL';
                    	self.DiscountVo[i].row_status = 1;
					}else if(self.DiscountVo[i].change_type == 'ADD'){
						self.DiscountVo[i].row_delete_flag = 'Y';
						self.delListDiscount.push(self.DiscountVo[i]);
						self.DiscountVo.splice(i, 1);
					}

				}
			}
		},
        // 还原优惠条件相关项目
        revertItem(condition_id){
            // 删除优惠条件相关项目
            let self = this;
            let listActDiscountItemVo =  JSON.parse(JSON.stringify(self.listActDiscountItemVo));
            self.listActDiscountItemVo.forEach(item=>{
                if(item.discount_condition_id == condition_id){
                    item.change_type = 'RETAIN';
                    item.row_status = 0;
                }
            })
		},
		 // 还原优惠条件相关商品
		 revertGoods(condition_id){
            let self = this;
            self.DiscountVo.forEach(item=>{
                if(item.discount_condition_id == condition_id){
                    item.change_type = 'RETAIN';
                    item.row_status = 0;
                }
            })
        },
        // 修改优惠条件相关项目

        updateItem(condition_id){

            // 删除优惠条件相关项目
            let self = this;
            // let listActDiscountItemVo =  JSON.parse(JSON.stringify(self.listActDiscountItemVo));
            self.listActDiscountItemVo.forEach((item,index)=>{
                if(item.discount_condition_id == condition_id){
                    console.log(condition_id)
                    item.change_type = 'CANCEL';
                    item.row_status = 1;
					// self.cancelListActDiscountItemVo.push(item);

                    // self.listActDiscountItemVo.splice(self.listActDiscountItemVo.indexOf(item), 1);
                }
            })
		},
        getListConditonId(){
            return new Promise((resolve, reject) =>{
                let url = '/price-web/api/actDiscount/getListConditonId'
                let id = ''
                this.ajax.postStream(url,1,res=>{
                    if(res.body.result) {
                        let conditionIds = [];
                        console.log(res.body.content)
                        id = res.body.content[0];
                        resolve(id);
                    } else {
                        reject(new Error(res.body.msg));
                    }
                })
            });
		},
		checkConditionPass(){
			let self = this;
			let message = '优惠条件行条件不能为空';
			let isPass = true;
			self.conditionVo.forEach(item=>{
				// if((item.threshold_price == '' && self.form.discount_condition_type == 'FULL_YUAN')||(item.threshold_count == '' && (self.form.discount_condition_type == 'MULTI_PIECE'||self.form.discount_condition_type == 'FULL_PIECE'))||(!item.item_enable_count&& item.item_enable_options != 'ALLSELECTION')){
				// 	isPass = false;
				// 	// return false;
				// }
				if((item.threshold_price == '' && self.form.discount_condition_type == 'FULL_YUAN')){
					message = '当优惠条件为满元，金额门槛不能为空';
					isPass = false;
				}
				if((item.threshold_count == '' && (self.form.discount_condition_type == 'MULTI_PIECE'||self.form.discount_condition_type == 'FULL_PIECE'))){
					message = '当优惠条件为满减或多件，件数门槛不能为空';
					isPass = false;
				}
				if(!item.item_enable_count&& item.item_enable_options != 'ALLSELECTION'){
					message = '当前条件存在优惠选项为任选一项时，选项项数不能为空';
					isPass = false;
				}
			})
			return {isPass:isPass,message:message}
		},
		getAllData(callback){
			let _this = this;
			if(_this.ifGetAllData){
				_this.$message.error('请先保存再继续操作');
				return false;
			}
			if(_this.ifChange || _this.isCopyAdd){
				callback && callback();
				return false;
			}
			new Promise((resolve,reject)=>{
				this.ajax.postStream('/price-web/api/actDiscount/getPageDiscountItem',{discount_id:this.form.discount_id}, res => {
					if(res.body.result){
						_this.listActDiscountItemVo = res.body.content;
						_this.itemCount = res.body.content.length;
						resolve && resolve();
					}else{
						this.$message.error(res.body.msg);
					}
				})
			}).then(()=>{
				this.ajax.postStream('/price-web/api/actDiscount/getPageDiscountMaterial',{discount_id:this.form.discount_id}, res => {
					if(res.body.result){
						_this.DiscountVo = res.body.content;
						_this.goodCount = res.body.content.length;
						_this.ifGetAllData = true;
						callback && callback();
					}else{
						this.$message.error(res.body.msg);
					}
				})
			})
		}

	}
}
