/*
优惠活动--优惠项目
*/
import Fn  from '@common/Fn.js'
export default {
	data() {
		let self = this



		return {
            itemCount: 0,
            discountItemSelect :[],
            discountItemBtns: [
                {
                   type: 'danger',
                   txt: '删除',
                   disabled() {
                        return self.listBtnControl;
                    },
                   click: self.discountItemDel
               }, {
                    type: 'warning',
                    txt: '还原',
                    disabled(){
						return !((self.form.status == "CREATE" ||self.form.status == 'REJECTED')&&self.form.document_type == 'COPY')
					},
                    click: self.discountItemRevert
                }, {
                    type: 'warning',
                    txt: '取消',
                    disabled(){
						return !((self.form.status == "CREATE" ||self.form.status == 'REJECTED')&&self.form.document_type == 'COPY')
                    },
                    click: self.discountItemcancel
                }, {
                    type: 'info',
                    txt: '失效',
                    disabled(){
						return !(self.form.status == "APPROVED"  && self.if_enable_time )
                     },
                    click: self.discountItemDisable
                }

           ],

           discountItemCols: [
            {
                label: '优惠项目编码',
                prop: 'discount_item_no',
                width: 150,

            },
            {
                label: '项目细类',
                prop: 'item_type_detail',
                format: 'auxFormat',
                formatParams: 'ITEM_TYPE_DETAIL',
            }, {
                label: '优惠条件',
                prop: 'discount_condition_dec',
                // formatter(val,index){
                //     let row = self.listActDiscountItemVo[index] || {};
                //     let keyWord = ''
                //     self.conditionVo.forEach(item=>{
                //         // console.log(item.discount_condition_id,val,self.form.discount_condition_type)
                //         if(item.discount_condition_id == val){
                //             if(self.form.discount_condition_type == 'FULL_YUAN'){
                //                 keyWord = '满'+item.threshold_price+'元'
                //             }else if(self.form.discount_condition_type == 'FULL_PIECE'||self.form.discount_condition_type =='MULTI_PIECE'){
                //                 keyWord = '满 '+item.threshold_count+' 件'
                //             }else{
                //                 keyWord = '无条件'
                //             }

                //         }
                //         // break;
                //     })

                //     row.discount_condition_dec = keyWord;
                //     return keyWord;
                // }
            }, {
                label: '优惠条件ID',
                prop: 'discount_condition_id',
                width: 130
            },  {
                label: '项目内容',
                prop: 'discount_item_dec',
                width:250,
                // formatter(val,index){
                //     let row = self.listActDiscountItemVo[index] || {};
                //     let keyWord = ''
                //     // if(row.subtract_money){
                //     // 	keyWord = '减'+row.subtract_money+'元'
                //     // }else if(row.discount){
                //     // 	keyWord = '打'+row.subtract_money+'折'
                //     // }else if()
                //     switch(row.discount_item_type){
                //         case 'ITEM_TYPE_DEDUCTE_AMOUNT' : self.listActDiscountItemVo[index].discount_item_dec = '减'+row.subtract_money+'元'; return '减'+row.subtract_money+'元'; break;
                //         case 'ITEM_TYPE_DISCOUNT' :self.listActDiscountItemVo[index].discount_item_dec = '折扣'+row.discount+'%';  return '折扣'+row.discount+'%'; break;
                //         case 'ITEM_TYPE_GIFTS' :self.listActDiscountItemVo[index].discount_item_dec = '赠送'+row.material_number;  return '赠送'+row.material_number; break;
                //         case 'ITEM_TYPE_UPGRADE' :self.listActDiscountItemVo[index].discount_item_dec = '换购'+row.material_number;  return '换购'+row.material_number; break;
                //         case 'ITEM_TYPE_GOODS_PROMOTION' :self.listActDiscountItemVo[index].discount_item_dec = '单品优惠'+row.material_number;  return '单品优惠'+row.material_number; break;
                //     }
                //     row.discount_item_dec = keyWord;

                //     return keyWord;
                // }
            },{
              label: '服务物料编码',
              prop: 'service_project_no',
              width: 90
          },{
                label: '封顶金额',
                prop: 'max_amount',
            },{
                label: '商品详情',
                slot: 'discount_condition_id',
            },{
                label: '中奖限额',
                slot: 'win_quota',
            }, {
                label: '中奖顺序',
                slot: 'win_order',
            }, {
                label: '商品详情',
                slot: 'discount_condition_id',
            },
             {
                label: '行状态',
                prop: 'row_status',
                formatter(val,index){
                    switch(val){
                        case 1 : return '失效'; break;
                        case 0 : return '生效'; break;
                    }
                }
            },
            // {
            //     label: '是否折现',
            //     prop: 'if_convert_into_cash',
            //     formatter(val,index){
            //         switch(val){
            //             case "Y" : return '是'; break;
            //             case "N" : return '否'; break;
            //         }
            //     }
            // },
            {
                label: '变更类型',
                prop: 'change_type',
                formatter(val){
                    switch(val){
                        case 'ADD' : return '新增'; break;
                        case 'CANCEL' : return '取消'; break;
                        case 'RETAIN' : return '保留'; break;

                    }
                }
            },{
                label: '允许叠加',
                prop: 'if_allow_superposition',
                formatter(val){
                    switch(val){
                        case 'Y' : return '是'; break;
                        case 'N' : return '否'; break;

                    }
                }
            },{
                label: '创建时间',
                prop: 'create_time',
                width:300,
                format:'dataFormat1'
            },{
                label: '失效人',
                prop: 'disable_person_name',
            }, {
                label: '失效时间',
                prop: 'disable_time',
                width:300,
                format:'dataFormat1'
            },
        ],

        itemSearch:{
            page_name:"act_discount_item",
            where:[],
            page_size:this.pageSize,
            page_no:1,
            discount_id:''
        },
		}
	},
	methods: {
        itemPresearch(list, resolve){
            console.log('-----------------itemPresearch')
			this.itemSearch.where = list;
			this.itemSearching(resolve);
        },
        itemPageSizeChange(size,resolve){
			this.itemSearch.page_size = size
			this.itemSearching(resolve)
		},
		itemPageChange(page_no,resolve){
			this.itemSearch.page_no = page_no
			this.itemSearching(resolve)
		},
        // 删除优惠项目
		discountItemDel(item_id){
            let self = this;
            console.log(self.discountItemSelect);
            if(!self.discountItemSelect||!self.discountItemSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
            }
            let delIdSet = new Set()
			self.discountItemSelect.find(d => {
                if(!!d.copy_version_id){
                    self.$message.error('已变更项目不能删除');
                    return false;
                }
                if(d.discount_item_id){
                    delIdSet.add(d.discount_item_id);
                }else{
                    self.listActDiscountItemVo.splice(self.listActDiscountItemVo.indexOf(d), 1);
                }
            })
            // if(self.ifChange){
            //     let i = self.listActDiscountItemVo.length;
            //     while( i--){
            //         if(delIdSet.has(self.listActDiscountItemVo[i].discount_item_id)){
            //             self.listActDiscountItemVo[i].change_type = 'CANCEL';
            //             self.cancelListActDiscountItemVo.push(self.listActDiscountItemVo[i]);
            //             self.listActDiscountItemVo.splice(i, 1);
            //         }
            //     }
            // }else{
                let i = self.listActDiscountItemVo.length;
                while( i--){
                    if(delIdSet.has(self.listActDiscountItemVo[i].discount_item_id)){
                        self.listActDiscountItemVo[i].row_delete_flag = 'Y';
                        self.delListActDiscountItemVo.push(self.listActDiscountItemVo[i]);
                        self.listActDiscountItemVo.splice(i, 1);
                    }
                }
        },

        // 优惠项目失效
        discountItemDisable(){
            let self = this;
            if(!self.discountItemSelect||!self.discountItemSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
            }
            if(!self.discountItemSelect) return false;
			self.discountItemSelect.forEach(selectItem=>{
				self.listActDiscountItemVo.forEach((item,index)=>{
					if(selectItem.discount_item_id == item.discount_item_id){
                        item.row_status = 1;
                        // self.$set(item, 'row_status', 1);
                        console.log(item.row_status);
					}
				})

            })
            // self.$refs['discount_GoodsList'].updateTable();
        },
        discountItemcancel(){
            let self = this;
            let isRevert = false,
            cancelList = []
            if(!self.discountItemSelect||!self.discountItemSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
            }
            // if(!self.discountItemSelect) return false;
            let delIdSet = new Set()
            self.discountItemSelect.find(d => {
                if(d.change_type != 'RETAIN'){
                    isRevert = true;
                    cancelList.push(d.discount_item_no);
                }
                if(d.copy_version_id){

                    delIdSet.add(d.copy_version_id);
                }else{
                    self.$message.error('只有非保留状态的数据可取消');
                    return false;
                }
            })

            if(isRevert){
                self.$message.error('存在'+cancelList.join(',')+'非还原项目，取消失败');
                return false;
            }
            self.listActDiscountItemVo.forEach((item,index)=>{

                if(delIdSet.has(item.copy_version_id)){
                        item.change_type = 'CANCEL';
                        item.row_status = 1;
                    }
                })
        },
        // 优惠项目还原
        discountItemRevert(){
            let self = this;
            let isCancel = false,
            isPass = false,
            RevertList = []
            if(!self.discountItemSelect||!self.discountItemSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
            }
            if(!self.discountItemSelect) return false;
            let checkCondition = new Set();
            let delIdSet = new Set();
            self.discountItemSelect.find(d => {
                if(d.change_type != 'CANCEL'){
                    isCancel = true;
                    RevertList.push(d.discount_item_no);
                }
                if(d.copy_version_id){
                    delIdSet.add(d.copy_version_id);
                    checkCondition.add(d.discount_condition_id);
                }else{
                    self.$message.error('选中项目状态不为取消不能还原');
                    return false;
                }
            })


            if(isCancel){
                self.$message.error('存在'+RevertList.join(',')+'非取消项目，还原失败');
                return false;
            }
            self.conditionVo.forEach((item,index)=>{
                if(checkCondition.has(item.discount_condition_id)){
                    if(item.row_status == 1){
                        isPass = true;
                    }
                }
            })
            if(isPass){
                self.$message.error('该项目对应条件为取消，不可还原');
                return false;
            }
            self.listActDiscountItemVo.forEach((item,index)=>{
                if(delIdSet.has(item.copy_version_id)){
                        item.change_type = 'RETAIN';
                        item.row_status = 0;
                    }
                })
        },
        itemSelectionChange(selectArr) {
            this.discountItemSelect = selectArr
            console.log(this.discountItemSelect)
		},

        // 获取优惠商品列表
		itemSearching(resolve){
            let _this = this
            // if(_this.ifChange){
            if(_this.ifGetAllData){
                _this.$message.error('请先保存再继续操作');
				resolve && resolve();
                return false;
            }
            if((_this.ifChange||_this.isCopyAdd)){
                _this.$message.error('未保存前不可查询');
                resolve && resolve();
                return
            }
			if(!_this.itemSearch.discount_id){
                resolve && resolve();
                return
            }

				this.ajax.postStream('/price-web/api/actDiscount/getDiscountItem',
					_this.itemSearch,
					res => {
						if(res.body.result && res.body.content) {
                            _this.listActDiscountItemVo = res.body.content.list;
                            _this.itemCount = res.body.content.count;
                            _this.listActDiscountItemVo.forEach(item=>{
                                if(_this.conditionSelect&&_this.conditionSelect.discount_condition_id == item.discount_condition_id){
                                    item.color_sign = 'A'
                                } else {
                                    item.color_sign = '';
                                }
                            })
                            _this.listActDiscountItemVo = JSON.parse(JSON.stringify(this.listActDiscountItemVo));

						}
						resolve && resolve();
				}, err => {
					resolve && resolve();
					this.$message.error(err);
				})
		},
	}
}
