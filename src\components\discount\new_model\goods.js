/*
优惠活动--优惠清单
*/
import Fn  from '@common/Fn.js'
export default {
	data() {
		let self = this
		return {
			cancelListDiscount:[],
			goodsSearch:{
				where:[],
				page_name:'act_discount_material_list',
				page_size:50,
           		page_no:1,
			},
			goodCount:0,
			delListDiscount:[],
            DiscountBtns:[
				{
					type: 'info',
					txt: '添加商品',
					disabled() {
						return self.form.status == 'APPROVED' ? false : self.listBtnControl;
					},
					click: self.addGoods
				},
				{
					type: 'danger',
					txt: '删除',
					disabled() {
						return self.listBtnControl;
					},
					click: self.goodsDel
				},{
					type: 'warning',
					txt: '还原',
					disabled(){
						return !((self.form.status == "CREATE" ||self.form.status == 'REJECTED')&&self.form.document_type == 'COPY')
					},
					click: self.goodsRevert
				},{
					type: 'warning',
					txt: '取消',
					disabled(){
						return !((self.form.status == "CREATE" ||self.form.status == 'REJECTED')&&self.form.document_type == 'COPY')
                    },
					click: self.goodscancel
				},{
					type: 'info',
					txt: '失效',
					disabled(){
						return !(self.form.status == "APPROVED"  && self.if_enable_time )
                    },
					click: self.goodsInvalid
				},{
					type: 'info',
					txt: '是否享受优惠切换',
					disabled(){
						return !(self.form.status == "CREATE" ||self.form.status == 'REJECTED');
                    },
					click: self.enjoyChange
				},
			],
            discountSelect:[],
            DiscountCols:[
				{
					label: '物料编码',
					prop: 'material_number',
					width:200
				}, {
					label: '行状态',
					prop: 'row_status',
					formatter(val){
						switch(val){
							case 1 : return '失效'; break;
							case 0 : return '生效'; break;
						}
					},
					width:200,
				},{
					label: '优惠条件',
					prop: 'discount_condition_dec'
				},  {
					label: '优惠条件ID',
					prop: 'discount_condition_id',
					width: 130
				},  {
					label: '是否享受优惠',
					prop: 'if_enjoy_discount',
					width:100,
					formatter(val){
						switch(val){
							case 'Y' : return '是'; break;
							case 'N' : return '否'; break;
						}
					}
                }, {
					label: '变更类型',
					prop: 'change_type',
					width:100,
					formatter(val){
						switch(val){
							case 'ADD' : return '新增'; break;
							case 'CANCEL' : return '取消'; break;
							case 'RETAIN' : return '保留'; break;

						}
					}
                },{
					label: '商品ID',
					// width:300,
					slot: 'win_goods_id',
					width:200,
				}, {
					label: '创建时间',
					// width:300,
					prop: 'create_time',
					format:'dataFormat1'
				}, {
					label: '失效人',
					width:200,
					prop: 'disable_person_name',
					}, {
						label: '失效时间',
						width:300,
						prop: 'disable_time',
						format:'dataFormat1'
					},
			],
			ifDiscountStatus: '',
			uploadUrl: '/price-web/api/actDiscount/materialList/import?permissionCode=DISCOUNT_MATERIAL_LIST_IMPORT',
		}
	},
	methods: {
		uploadCallback (data) {
			if (data.body && data.body.result) {
                this.getDiscountMaterialList();
            }
		},
		// 审核之后是否可添加优惠清单
		if_discount_status() {
			return new Promise((reslove, reject) => {
				let self = this, postData = {
					discount_id: this.form.discount_id,
					page_name: "act_discount_material_list",
					page_no: 1,
					page_size: 50,
					where: [],
				}
				this.ajax.postStream('/price-web/api/actDiscount/getDiscountMaterialList', postData, res => {
					if(res.body.result && res.body.content) {
						let list = res.body.content.list || [];
						if ( list && list.length > 0 ) {
							let ifGolbal = list.every(item => {
								return !item.discount_condition_id //状态为生效，且优惠条件id无值
							})
							let ifAllFailure = list.every(item => {
								return item.row_status == 1 //优惠清单状态为失效
							})
							if (ifAllFailure) {
								self.ifDiscountStatus = 'empty'
							} else {
								self.ifDiscountStatus = ifGolbal ? 'global' : 'condition'
							}
						} else {
							self.ifDiscountStatus = 'empty'
						}
						reslove(self.ifDiscountStatus)
					} else {
						reject(res.body.msg)
					}
				}, err => {
					reject(res.body.msg)
				})
			})
		},
		// 添加商品
		addGoods(event,row){
			/**
			 * 	当优惠清单为空时，点击‘添加商品’或‘添加优惠清单’按钮，提示：原始活动无优惠清单，审核后不允许添加清单！
				当优惠清单中的物料为条件清单时，点击‘添加商品’，提示：当前活动的优惠清单为条件清单，不允许添加全局清单！
				当优惠清单中的物料为全局清单时，点击‘添加优惠清单’按钮，提示：当前活动的优惠清单为全局清单，不允许添加条件清单！
			 */
			if(this.form.status == 'APPROVED') {
				let self = this
				this.if_discount_status().then((ifDiscountStatus) => {
					if (ifDiscountStatus == 'empty') {
						self.$message.error('原始活动无优惠清单，审核后不允许添加清单！')
						return
					} else if (ifDiscountStatus == 'global' && event === '') {
						self.$message.error('当前活动的优惠清单为全局清单，不允许添加条件清单！')
						return
					} else if (ifDiscountStatus == 'condition' && event != '') {
						self.$message.error('当前活动的优惠清单为条件清单，不允许添加全局清单！')
						return
					}
					self.confirmAddGoods(event,row)
				}).catch((e) => {
					self.$message.error(e)
				})
			} else {
				this.confirmAddGoods(event,row)
			}
		},
		confirmAddGoods(event,row) {
			let params = {},
			self = this;
			let data = {
				material_id:'',
				material_number:"",
				disable_person_id:'',
				disable_person_name:"",
				discount_condition_id:!!row?row.discount_condition_id:'',
				disable_time:"",
				row_status:0,
				change_type:self.form.document_type == 'COPY'?'ADD':'',
				discount_condition_dec:'',
				if_enjoy_discount:self.DiscountVo.length?self.DiscountVo[0].if_enjoy_discount:'Y'
			}
			// 选择商品

			params.callback = d => {
				// console.log(d)
				let isPass = true;
				d.forEach(dItem=>{
					self.DiscountVo.forEach(item=>{

						// if(item.material_id == dItem.materialId){
						// 	// console.log(row);

						// 	// if(!row){
						// 	// 	// console.log(row);
						// 	// 	item.discount_condition_id =  '';
						// 	// }else
						// 	let msg = item.material_number+'该物料已存在';

						// 	if(item.row_status != 1){
						// 		// console.log(item,row)
						// 		this.$message.error(msg);
						// 		isPass =  false;
						// 	}else if(!!row &&item.discount_condition_id == row.discount_condition_id&& item.row_status == 1){
						// 		msg = '当前条件已存在失效的优惠清单物料'+item.material_number+'，请使用还原功能';
						// 		this.$message.error(msg);
						// 		isPass =  false;
						// 	}
						// }
					})
				})

				if(d && isPass) {
					d.forEach(item=>{
						// let dData;
						// dData.material_number = item.materialNumber;
						// dData.material_id = item.materialId;
						// self.DiscountVo.push(dData);
						self.DiscountVo.push({
							// material_number:item.materialNumber,
							// material_id:item.materialId
							material_id:item.materialId,
							win_goods_id:null,
							material_number:item.materialNumber,
							disable_person_id:'',
							disable_person_name:"",
							discount_condition_id:!!row?row.discount_condition_id:'',
							disable_time:"",
							row_status:0,
							change_type:self.form.document_type == 'COPY'?'ADD':'',
							if_enjoy_discount:self.DiscountVo.length?self.DiscountVo[0].if_enjoy_discount:'Y'
						})
					})

				}
			}
			self.$root.eventHandle.$emit('alert', {
				params: params,
				component: () => import('@components/discount/selectGoodsList'),
				style: 'width:800px;height:500px',
				title: '商品列表'
			});
		},
		discountselectionChange(selectArr){
			console.log(selectArr)
			this.discountSelect = selectArr;
		},

		 // 删除优惠项目
		 goodsDel(item_id){
            let self = this;
            if(!self.discountSelect||!self.discountSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
            }
            let delIdSet = new Set()
			self.discountSelect.find(d => {
                if(!!d.copy_version_id){
                    self.$message.error('已变更项目不能删除');
                    return false;
                }
                if(d.discount_material_list_id){
                    delIdSet.add(d.discount_material_list_id);
                }else{
                    self.DiscountVo.splice(self.delListDiscount.indexOf(d), 1);
                }
            })
                let i = self.DiscountVo.length;
                while( i--){
                    if(delIdSet.has(self.DiscountVo[i].discount_material_list_id)){
                        self.DiscountVo[i].row_delete_flag = 'Y';
                        self.delListDiscount.push(self.DiscountVo[i]);
                        self.DiscountVo.splice(i, 1);
                    }
                }
        },

		// 优惠清单失效
        goodsInvalid(){
            this.$message.error('由于优惠清单失效造成大量历史订单存在校验出错问题，优惠清单的失效功能暂停开放。如需失效，请禁用当前活动并重建新活动，如有疑问请找青棠！')
            return
            let self = this;
            if(!self.discountSelect||!self.discountSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
            }
            if(!self.discountSelect) return false;
			self.discountSelect.forEach(selectItem=>{
				self.DiscountVo.forEach((item,index)=>{
					if(selectItem.discount_material_list_id == item.discount_material_list_id){
                        item.row_status = 1;
                        console.log(item.row_status);
					}
				})

            })
        },
		goodsRevert(){
            let self = this;
            let isCancel = false,
            isPass = false,
            RevertList = []
            if(!self.discountSelect||!self.discountSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
			}
            let checkCondition = new Set();
            let delIdSet = new Set();
            self.discountSelect.find(d => {
                if(d.change_type != 'CANCEL'){
                    isCancel = true;
                    RevertList.push(d.material_number);
                }
                if(d.copy_version_id){
                    delIdSet.add(d.copy_version_id);
                    checkCondition.add(d.discount_condition_id);
                }else{
                    self.$message.error('选中商品状态不为取消不能还原');
                    return false;
                }
            })


            if(isCancel){
                self.$message.error('存在'+RevertList.join(',')+'非取消商品，还原失败');
                return false;
            }
            self.conditionVo.forEach((item,index)=>{
                if(checkCondition.has(item.discount_condition_id)){
                    if(item.row_status == 1){
                        isPass = true;
                    }
                }
            })
            if(isPass){
                self.$message.error('该商品对应条件为取消，不可还原');
                return false;
            }
            self.DiscountVo.forEach((item,index)=>{
                if(delIdSet.has(item.copy_version_id)){
                        item.change_type = 'RETAIN';
                        item.row_status = 0;
                    }
                })
        },
		goodsPageSizeChange(size,resolve) {
			this.goodsSearch.page_size = size;
			this.getDiscountMaterialList(resolve)
		},
		goodsPageChange(page_no){
			this.goodsSearch.page_no = page_no
			this.getDiscountMaterialList()
		},
		martialPresearch(list, resolve){
			this.goodsSearch.where = list;
			this.getDiscountMaterialList(resolve);
        },

		goodscancel(){
            let self = this;
            let isRevert = false,
            cancelList = []
            if(!self.discountSelect||!self.discountSelect.length){
                self.$message.error('请选择需要操作的明细行')
                return false;
            }
            // if(!self.discountItemSelect) return false;
            let delIdSet = new Set()
            self.discountSelect.find(d => {
                if(d.change_type != 'RETAIN'){
                    isRevert = true;
                    cancelList.push(d.material_number);
                }
                if(d.copy_version_id){

                    delIdSet.add(d.copy_version_id);
                }else{
                    self.$message.error('只有非保留状态的数据可取消');
                    return false;
                }
            })

            if(isRevert){
                self.$message.error('存在'+cancelList.join(',')+'非还原商品，取消失败');
                return false;
            }
            self.DiscountVo.forEach((item,index)=>{

                if(delIdSet.has(item.copy_version_id)){
                        item.change_type = 'CANCEL';
                        item.row_status = 1;
                    }
                })
		},
		switchIfEnjoyDiscount(if_enjoy_discount_material){
			let self = this;
			// let arr = this.DiscountVo.map(item=>{
			// 	if(!!item.material_id){
			// 		return item.material_id;
			// 	}
			// })
			this.ajax.postStream('/price-web/api/actDiscount/switchIfEnjoyDiscount',
			{
				list_discount_id:[self.form.discount_id],
				if_enjoy_discount_material:if_enjoy_discount_material
			},
			res => {
				if(res.body.result) {
					this.$message.success(res.body.msg);
					this.getDiscountMaterialList();
				}else{
					this.$message.error(res.body.msg);

				}
			}, err => {
				this.$message.error(err);
			})
		},
		enjoyChange(){
			let self = this;
			if(self.isCopyAdd){
				this.$message.error('请保存后操作');
				return false;
			}
			if(self.DiscountVo.length){
				if(!!self.DiscountVo[self.DiscountVo.length-1].discount_id){

					self.switchIfEnjoyDiscount(self.DiscountVo[0].if_enjoy_discount=="Y"?"N":"Y");
				}else{
					this.$message.error('请保存后操作');
				}
			}else{
				this.$message.error('请添加优惠清单');
			}
			// self.$root.eventHandle.$emit('openDialog',{
			// 	txt:'请选择是否享受优惠',
			// 	okTxt:'是',
			// 	noTxt:'否',
			// 	cancelTxt:'取消',
			// 	noShowNoTxt:false,
			// 	ok(){
			// 		self.switchIfEnjoyDiscount('Y');
			// 	},

			// 	no(){
			// 		self.switchIfEnjoyDiscount('N');
			// 	}
			// })
		}
	}
}
