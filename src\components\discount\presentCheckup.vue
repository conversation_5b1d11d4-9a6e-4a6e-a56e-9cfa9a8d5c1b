//礼品提交申报
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='24'>
		<el-button type='success' size='mini' @click="getDetail">刷新</el-button>
        <el-button type='primary' size='mini' @click="confirm" :disabled="ifConfirm" :loading="ifConfirmLoading">提交</el-button>
        <el-button type='warning' size='mini' @click="reject" :disabled="ifReject" :loading="ifRejectLoading">驳回</el-button>
        <el-button type='info' size='mini' @click="checkup" :disabled="ifCheck" :loading="ifCheckLoading">核对</el-button>
        <el-button type='success' size='mini' @click="discount" :disabled="ifDiscount" :loading="ifDiscountLoading">礼品折现</el-button>
        <el-button type='danger' size='mini' @click="endCheck" :disabled="ifOver" :loading="ifOverLoading">终止申报</el-button>
        <el-button type='info' size='mini' @click="addAddressAudit" :disabled="ifAddAddressAudit || status != '新增地址待审核'" :loading="ifAddAddressLoading">地址审核</el-button>
        <el-button type='success' size='mini' @click="fileUpload" :disabled="!(status == '创建')" >
          附件上传
          <xpt-upload-v2 isOnlyPic :ifClickUpload="ifClickUploadB" :dataObj="uploadDataB"></xpt-upload-v2>
        </el-button>
        <el-button type='primary' size='mini' @click="fileSee">附件查看</el-button>
      </el-col>
    </el-row>
    <el-row	:gutter='40' >
      <el-tabs v-model="firstTab" >
        <el-tab-pane label="基本信息" name="checkupList">
          <el-form label-position="right" label-width="120px" :rules="rules" ref="presentDetail">
            <el-col :span="10" style="width: 33%">
              <el-form-item label="申报编码：" >
                <el-input v-model="dataList.application_no" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="礼品编码：" >
                <el-input v-model="dataList.present_no" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="天猫公示订单：" >
                <el-input v-model="dataList.tid" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="买家昵称：" >
                <el-input v-model="dataList.customer_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="提交人：" >
                <el-input v-model="dataList.submitter_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="店铺：" >
                <el-input v-model="dataList.original_shop_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 33%">
              <el-form-item label="优惠活动名称：" >
                <el-input v-model="dataList.discount_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="礼品名称：" >
                <el-input v-model="dataList.present_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖关联订单：" >
                <el-input v-model="dataList.winning_relate_tids" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="发货单号：" >
                <el-input v-model="dataList.delivery_no" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="收货人姓名：" >
                <el-input v-model="dataList.receiver_name" size='mini' style="width: 200px;" disabled></el-input>
                <el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="提交人业务分组：" >
                <el-input v-model="dataList.submitter_group_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="状态：" style="height:32px;">
                <el-input v-model="status" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 34%">
              <el-form-item label="优惠项目：" >
                <el-input v-model="discountItem" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖合并单号：" >
                <a style="text-decoration:none;" href="javascript:;" @click="goMerge" title="点击进入合并订单信息页面">{{dataList.merge_trade_no}}</a>
                <!--<el-input v-model="dataList.merge_trade_no" size='mini' style="width: 200px;" @click="goMerge" disabled></el-input>-->
              </el-form-item>
              <el-form-item label="实际发货订单：" >
                <el-input v-model="dataList.winning_delivery_relate_tids" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="是否新增地址">
                  <el-switch v-model="dataList.if_add_address" on-text="是" off-text="否" off-value="N" on-value="Y" :disabled="status != '创建'" @change="changeIfAddAdress"></el-switch>
              </el-form-item>
              <el-form-item label="收货人电话：" >
                <el-input v-model="dataList.receiver_mobile" size='mini' style="width: 200px;" disabled></el-input>
                <el-tooltip v-if='rules.receiver_mobile[0].isShow' class="item" effect="dark" :content="rules.receiver_mobile[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="提交时间：" >
                <el-input v-model="submit_time" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 100%">
              <el-form-item label="收货人地址：" style="height:32px;">
                <el-input v-model="dataList.receiver_address" size='mini' style="width: 82%;" disabled></el-input>
                <el-tooltip v-if='rules.receiver_address[0].isShow' class="item" effect="dark" :content="rules.receiver_address[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                </el-tooltip>
                <el-button size='mini' style="height: 22px;" @click="addDeliveryAddress" :disabled="!(status == '创建' && dataList.if_add_address == 'Y')">新增地址</el-button>
                <el-button size='mini' style="height: 22px;" @click="chooseDeliveryAddress" :disabled="(status == '创建' && dataList.if_add_address == 'Y')">选择地址</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 100%;">
              <el-form-item label="备注：" style="height: 60px;" v-if="status === '已终止' || status === '已折现' || status === '采购中'">
                <el-input v-model="dataList.remark" size='mini' type='textarea' style="width:94%;" disabled></el-input>
              </el-form-item>
              <el-form-item label="备注：" style="height: 60px;" v-else>
                <el-input v-model="dataList.remark" size='mini' type='textarea' style="width:94%;"></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class='xpt-flex__bottom' id='bottom' v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane label="操作记录" name="operationHistory" class='xpt-flex'>
          <xpt-list
            :data='operationList'
            :colData='operationCols'
            selection=''
            :showHead='false'
            :orderNo="true"
          ></xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
  import Vue from 'vue'
  import Fn from '@common/Fn.js'
  import validate from '@common/validate.js'
    export default {
      name: "presentCheckup",
      props:["params"],
      data(){
        return {
          dataList:{
            if_add_address: 'N'
          },
          rules:{
            ...[
                {
                    receiver_name: '收货人姓名'
                }, {
                    receiver_mobile: '收货人电话'
                }, {
                    receiver_address: '收货人地址'
                }
            ].reduce((a, b) => {
                var key = Object.keys(b)[0]
                a[key] = validate.isNotBlank({
                    self: this,
                    msg: '请填写' + b[key],
                })
                return a
            }, {})
          },
          ifAddAddressLoading: false,
          ifAddAddressAudit: false,
          ifConfirm:false,
          ifReject:false,
          ifOver:false,
          ifUpdate:false,
          ifChoose:false,
          ifDiscount:false,
          ifConfirmLoading:false,
          ifRejectLoading:false,
          ifCheckLoading:false,
          ifDiscountLoading:false,
          ifOverLoading:false,
          discountItem:"",//优惠项目
          status:'CREATED',//状态
          create_time:'',//创建时间
          submit_time:'',//提交时间
          firstTab:'checkupList',
          secondTab:'operationHistory',
          receiverData:{},
          operationList:[],
          operationCols:[
            {
              label:'用户',
              prop:'operator_name'
            },
            {
              label:'操作名称',
              prop:'operation_name'
            },
            {
              label:'操作时间',
              prop:'operation_time',
              format: "dataFormat1"
						},
						{
              label:'备注',
              prop:'remark'
            },
					],
					ifClickUploadB: false,
					uploadDataB: null
        }
      },
      methods:{
        changeIfAddAdress(){
          this.ajax.postStream('/price-web/api/actPresentApplication/updateIfAddress',{id:this.params.id, if_add_address: this.dataList.if_add_address},res => {
            if(res.body.result) {
              res.body.msg && this.$message.success(res.body.msg);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            this.getDetail()
          }, err => {
            this.$message.error(err);
            this.getDetail()
          });
        },
        getDetail(){
          let list = __AUX.getValidData("PRESENT_RETURN_TYPE");
          console.log(123,list);
          this.ajax.get('/price-web/api/actPresentApplication/detail?applicationId=' + this.params.id,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content;
              this.dataList.remark = res.body.content.remark || "";
              this.discountItem = res.body.content.discount_condition_dec + res.body.content.discount_item_dec;
              this.create_time = Fn.dateFormat(res.body.content.create_time,'yyyy-MM-dd hh:mm:ss');
              this.submit_time = Fn.dateFormat(res.body.content.submit_time,'yyyy-MM-dd hh:mm:ss');
              this.configStatus();
              this.getOperationHistory();
              Vue.prototype.tabNo[this.params.tabName] = res.body.content.application_no || '';
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              Vue.prototype.tabNo[this.params.tabName] = '';
            }
          }, err => {
            this.$message.error(err);
            Vue.prototype.tabNo[this.params.tabName] = '';
          });
        },
        //获取操作记录
        getOperationHistory(){
          this.ajax.postStream('/order-web/api/receipt/v2/logList',{source_id:this.params.id},res => {
            if(res.body.result && res.body.content) {
              this.operationList = res.body.content.list || [];
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        //提交
        confirm(){
          this.ifConfirm = true;
          this.ifConfirmLoading = true;
          let params = {
            id:this.params.id,
            receiver_name:this.dataList.receiver_name,
            receiver_mobile:this.dataList.receiver_mobile,
            receiver_address:this.dataList.receiver_address,
            remark:this.dataList.remark
          };
          this.ajax.postStream('/price-web/api/actPresentApplication/submitAudit',params,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getDetail();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              this.ifConfirm = false;
            }
            this.ifConfirmLoading = false;
          }, err => {
            this.$message.error(err);
            this.ifConfirm = false;
            this.ifConfirmLoading = false;
          });
        },
        //驳回
        reject(){
					this.$root.eventHandle.$emit('alert', {
						component: () => import('@components/per_sales_report/reject_present.vue'),
						style: 'width:660px;height:180px',
						title: "驳回",
						params: {
							callback: data => {
								this.confirmReject(data)
							}
						},
					});
				},
				confirmReject (remark) {
					this.ifReject = true;
          this.ifRejectLoading = true;
          let params = {
            remark:this.dataList.remark,
						id:this.params.id,
						logOperationRemark: remark
					};
					
          this.ajax.postStream('/price-web/api/actPresentApplication/reject',params,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getDetail();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              this.ifReject = false;
            }
            this.ifRejectLoading = false;
          }, err => {
            this.$message.error(err);
            this.ifReject = false;
            this.ifRejectLoading = false;
          });
				},
        // 地址审核
        addAddressAudit(){
          this.ifAddAddressLoading = true;
          this.ifAddAddressAudit = true;
          this.ajax.postStream('/price-web/api/actPresentApplication/addAddressAudit?permissionCode=ADD_ADDRESS_AUDIT',this.dataList,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getDetail();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              this.ifAddAddressAudit = false;
            }
            this.ifAddAddressLoading = false;
          }, err => {
            this.$message.error(err);
            this.ifAddAddressLoading = false;
            this.ifAddAddressAudit = false;
          });
        },
        //核对
        checkup(){
          this.ifCheckLoading = true;
          this.ifCheck = true;
          this.ajax.postStream('/price-web/api/actPresentApplication/submit',this.dataList,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getDetail();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              this.ifCheck = false;
            }
            this.ifCheckLoading = false;
          }, err => {
            this.$message.error(err);
            this.ifCheckLoading = false;
            this.ifCheck = false;
          });
        },
        //礼品折现
        discount() {
					let self = this
          this.$root.eventHandle.$emit('openDialog', {
						ok() {
							self.confirmDiscount()
						},
						okTxt: '是',
						no() {
						},
						noTxt: '否',
						txt: '是否确认【礼品折现】？',
						noCancelBtn: true
					})
        },
        confirmDiscount(){
          this.ifDiscount = true;
          this.ifDiscountLoading = true;
          let params = [{
            remark:this.dataList.remark,
            id:this.params.id
          }];
          this.ajax.postStream('/price-web/api/actPresentApplication/convertIntoCash?permissionCode=PRESENT_CONVERT_INTO_CASH',params,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getDetail();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              this.ifDiscount = false;
            }
            this.ifDiscountLoading = false;
          }, err => {
            this.$message.error(err);
            this.ifDiscount = false;
            this.ifDiscountLoading = false;
          });
        },
				//终止申报
				endCheck() {
					let self = this
					this.$root.eventHandle.$emit('openDialog', {
						ok() {
							self.confirmEndCheck()
						},
						okTxt: '是',
						no() {
						},
						noTxt: '否',
						txt: '是否确认【终止申报】？',
						noCancelBtn: true
					})
				},
        confirmEndCheck(){
          this.ifOver = true;
          this.ifOverLoading = true;
          let params = [{
            remark:this.dataList.remark,
            id:this.params.id
          }];
          this.ajax.postStream('/price-web/api/actPresentApplication/endSubmit?permissionCode=PRESENT_END_SUBMIT',params,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getDetail();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              this.ifOver = false;
            }
            this.ifOverLoading = false;
          }, err => {
            this.$message.error(err);
            this.ifOver = false;
            this.ifOverLoading = false;
          });
				},
				// 查看附件
				fileSee () {
					let self = this
					let params = {
						parent_no : this.dataList.application_no,
						child_no : null,
						ext_data : null,
						parent_name :'ORDER',
						child_name : 'present',
						parent_name_txt :'礼品申购',
						child_name_txt : null,
					}
					params.ifClose = this.dataList.status == 'CREATED'
					params.callback = d=>{
						let i = d.length;
						while(i--) {
							let json = d[i].group_value_json;
							json = JSON.parse(json).content || {};
							console.log('console.log', json)
							if(json instanceof Array) {
								json = json[0];
							}
						}
					}
					this.$root.eventHandle.$emit('alert',{
						params:params,
						component:()=>import('@components/common/download.vue'),
						style:'width:1000px;height:600px',
						title:'下载列表'
					})
				},
				// 附件上传
				fileUpload (type) {
					this.ifClickUploadB = true
					this.uploadDataB = {
						parent_name:'ORDER',
						parent_no: this.dataList.application_no,
						child_name: 'present',
						child_no:'',
						content:{'style':'礼品申购附件'}
					}
					setTimeout(() => {
						this.ifClickUploadB = false
					},100)
				},
        //跳转合并订单详情
        goMerge(){
          let params = {};
          params.merge_trade_id = this.dataList.merge_trade_id;
          this.$root.eventHandle.$emit('creatTab', {
            name: "合并订单详情",
            params: params,
            component: () => import('@components/order/merge.vue')
          });
        },
        configStatus(){
          switch (this.dataList.status) {
            case "CREATED" : this.status =  "创建"; break;
            case "SUBMIT_AUDIT" : this.status =  "提交待审核"; break;
            case "NOT_PURCHASE" : this.status = "未采购"; break;
            case "PURCHASING" : this.status =  "采购中"; break;
            case "DELIVERED" : this.status = "已发货"; break;
            case "TO_BE_RETURNED" : this.status = "待退货"; break;
            case "RETURNED" : this.status = "已退货"; break;
            case "CONVERT_INTO_CASH" : this.status = "已折现"; break;
            case "END_SUBMIT" : this.status = "已终止"; break;
            case "CANCEL" : this.status = "取消"; break;
            case "ADD_ADDRESS_AUDIT" : this.status = "新增地址待审核"; break;
          }
		    if (this.status === "创建") {
            this.ifConfirm = false;
            this.ifCheck = true;
            this.ifReject = true;
            this.ifOver = false;
            this.ifDiscount = false;
            // this.ifUpdate = false;
            // this.ifChoose = false;
          } else if (this.status === "已终止" || this.status === "采购中" || this.status === "已折现" || this.status === "已发货") {
            this.ifConfirm = true;
            this.ifCheck = true;
            this.ifReject = true;
            this.ifOver = true;
            this.ifDiscount = true;
            // this.ifUpdate = true;
            this.ifChoose = true;
          } else if (this.status === "未采购") {
            this.ifConfirm = true;
            this.ifCheck = true;
            this.ifReject = true;
            this.ifOver = false;
            this.ifDiscount = false;
            // this.ifUpdate = true;
            // this.ifChoose = true;
          } else {
            this.ifConfirm = true;
            this.ifCheck = false;
            this.ifReject = false;
            this.ifOver = false;
            this.ifDiscount = false;
            // this.ifUpdate = true;
            // this.ifChoose = true;
          }        },
        addDeliveryAddress(){
          this.$root.eventHandle.$emit('alert', {
            params: {
              dataList:this.dataList,
              callback: data => {
                this.dataList.receiver_name = data.receiver_name;
                this.dataList.receiver_mobile = data.receiver_mobile;
                this.dataList.receiver_address = data.receiver_state_name + data.receiver_city_name + data.receiver_district_name + data.receiver_street_name + data.receiver_address;
                //console.log(data,this.dataList.receiver_address);
                this.updateAddress();
              }
            },
            component: ()=>import('@components/discount/presentDeliveryAddressUpdate'),
            style: 'width:700px;height:300px',
            title: '新增地址',
          })
        },
        chooseDeliveryAddress(){
          this.$root.eventHandle.$emit('alert', {
            params: {
              cust_id:this.dataList.tid,
              winning_delivery_relate_tids:this.dataList.winning_delivery_relate_tids,
              callback: data => {
                this.dataList.receiver_name = data.receiver_name;
                this.dataList.receiver_mobile = data.receiver_mobile;
                this.dataList.receiver_address = data.address_name;
                this.updateAddress();
              }
            },
            component: ()=>import('@components/discount/presentDeliveryAddressChoose'),
            style: 'width:800px;height:400px',
            title: '选择地址',
          })
        },
        updateAddress(){
          let params = {
            id:this.params.id,
            receiver_name:this.dataList.receiver_name,
            receiver_mobile:this.dataList.receiver_mobile,
            receiver_address:this.dataList.receiver_address,
          };
          this.ajax.postStream('/price-web/api/actPresentApplication/updateAddress',params,res => {
            if(res.body.result) {
              this.$message.success("地址修改成功！");
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
      },
      mounted: function() {
        this.getDetail();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getDetail();
        });
      }
    }
</script>

<style type="text/css">
    .el-dialog__wrapper {
        background-color: transparent
    }
</style>
