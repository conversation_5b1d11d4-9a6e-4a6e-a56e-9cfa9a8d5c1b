<template>
  <xpt-list
    :data="dataList"
    :colData="cols"
    :btns="btns"
    :selection="selection"
    @radio-change='selectChange'
  ></xpt-list>
</template>

<script>
    export default {
      name: "presentDeliveryAddressChoose",
      props:["params"],
      data (){
        let self = this;
        return {
          dataList:[],
          selection:'radio',
          selectRow:{},
          btns:[
            {
              type: "primary",
              txt: "确认",
              loading: false,
              click() {
                self.confirm();
              },
            },
          ],
          cols:[
            {
              width:100,
              label: "收货人姓名",
              prop: "receiver_name",
            },
            {
              width:100,
              label: "收货人手机",
              prop: "receiver_mobile",
            },
            {
              label: "收货人地址",
              prop: "address_name",
              // formatter(val,index){
              //   let row = self.dataList[index] || {};
              //   if (row) {
              //     return row.receiver_state_name + "." + row.receiver_city_name + "." + row.receiver_district_name + "." + row.receiver_street_name || "" + row.receiver_address;
              //   }
              // }
            },
          ],
        }
      },
      methods:{
        getList(){
          let params = {
            cust_id:this.params.cust_id,
            page_no:1,
            page_size:50
          };
          this.ajax.postStream('/price-web/api/actPresentApplication/findDeliveryAddressByTid',{tid: this.params.winning_delivery_relate_tids},res => {
            if(res.body.result) {
               this.dataList = res.body.content || [];
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        confirm(){
          console.log(this.selectRow);
          if (!this.selectRow.receiver_name) {
            this.$message.error("请选择一条收货信息！");
            return;
          }
          //关闭弹窗
          this.params.callback(this.selectRow);
          this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
        },
        // 多选事件
        selectChange(s){
          this.selectRow = s;
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
