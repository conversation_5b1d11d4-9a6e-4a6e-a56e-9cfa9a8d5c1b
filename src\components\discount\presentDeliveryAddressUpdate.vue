<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='primary' size='mini' @click="save('submitData')">保存</el-button>
      </el-col>
    </el-row>
    <el-form label-position="right" label-width="100px" :model="submitData" :rules="rules" ref="submitData">
      <el-col :span="6" style="width: 50%">
        <el-form-item label="收货人姓名：" style="height: 40px;" prop="receiver_name">
          <el-input size='mini' v-model="submitData.receiver_name"></el-input>
          <el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="省：" style="height: 40px;" prop="receiver_state">
          <el-select placeholder="请选择" size='mini' v-model="submitData.receiver_state" @change="selectProvince">
            <el-option
              v-for="(value,key) in provinceObj"
              :label="value"
              :value="parseInt(key)"  :key='key'>
            </el-option>
          </el-select>
          <el-tooltip v-if='rules.receiver_state[0].isShow' class="item" effect="dark" :content="rules.receiver_state[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="区：" style="height: 40px;" prop="receiver_district">
          <el-select placeholder="请选择" size='mini'  v-model="submitData.receiver_district" @change="selectArea">
            <el-option
              v-for="(value,key) in areaObj"
              :label="value"
              :value="parseInt(key)" :key='key'>
            </el-option>
          </el-select>
          <el-tooltip v-if='rules.receiver_district[0].isShow' class="item" effect="dark" :content="rules.receiver_district[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
      </el-col>
      <el-col :span="6" style="width: 50%">
        <el-form-item label="收货人手机：" style="height: 40px;" prop="receiver_mobile">
          <el-input size='mini' v-model="submitData.receiver_mobile"></el-input>
        <el-tooltip v-if='rules.receiver_mobile[0].isShow' class="item" effect="dark" :content="rules.receiver_mobile[0].message" placement="right-start" popper-class='xpt-form__error'>
          <i class='el-icon-warning'></i>
        </el-tooltip>
        </el-form-item>
        <el-form-item label="市：" style="height: 40px;" prop="receiver_city">
          <el-select placeholder="请选择" size='mini' v-model="submitData.receiver_city" @change="selectCity">
            <el-option
              v-for="(value,key) in cityObj"
              :label="value"
              :value="parseInt(key)" :key='key'>
            </el-option>
          </el-select>
          <el-tooltip v-if='rules.receiver_city[0].isShow' class="item" effect="dark" :content="rules.receiver_city[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="街道：" style="height: 40px;">
            <el-select placeholder="请选择" size='mini'  v-model="submitData.receiver_street" >
              <el-option
                v-for="(value,key) in streetObj"
                :label="value"
                :value="parseInt(key)" :key='key'>
              </el-option>
            </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10" style="width: 100%">
        <el-form-item label="详细地址：" style="height: 40px;" prop="receiver_address">
          <el-input size='mini' v-model="submitData.receiver_address" type='textarea' style="width: 90.5%;" :maxlength='80' placeholder="80字以内" ></el-input>
          <el-tooltip v-if='rules.receiver_address[0].isShow' class="item" effect="dark" :content="rules.receiver_address[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>

<script>
  import validate from '@common/validate.js'
    export default {
      name: "presentDeliveryAddressUpdate",
      props:["params"],
      data () {
        let self = this;
        return {
          provinceObj:{},
          cityObj:{},
          areaObj:{},
          streetObj:{},
          submitData:{
            cust_id:self.params.dataList.customer_id,
            receiver_name:"",
            receiver_phone:"",
            receiver_mobile:"",
            receiver_state:"",
            receiver_city:"",
            receiver_district:"",
            receiver_street:"",
            receiver_address:"",
            deliver_method:'',
            post_fee_type:'NOW_PAY'
          },
          rules:{
            receiver_name:validate.isNotBlank({
              required:true,
              self:self,
              msg:"请输入收货人姓名",
              trigger:"change"
            }),
            receiver_mobile:validate.isNotBlank({
              self:self,
              required:true
            }),
            receiver_state:validate.isNotBlank({
              required:true,
              self:self,
              msg:"请选择所在省",
              trigger:"change"
            }),
            receiver_city:validate.isNotBlank({
              required:true,
              self:self,
              msg:"请选择所在市",
              trigger:"change"
            }),
            receiver_district:validate.isNotBlank({
              required:true,
              self:self,
              msg:"请选择所在区",
              trigger:"change"
            }),
            receiver_address:validate.isNotBlank({
              required:true,
              self:self,
              msg:"请输入详细地址",
              trigger:"change"
            })
          },
        }
      },
      methods:{
        // 地区
        getAreaCode(code,key){
          let self = this;
          let url = "/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId";
          //let url = "/order-web/api/customer/receiverInfo/addReceriverInfo?permissionCode=ORDER_MERGEORDER_CUSTOMER_SAVE";
          if(!code) return;
          this.ajax.postStream(url,code,function(response){
            if(response.body.result){
              self[key] = response.body.content
            }
          })
        },
        selectProvince(address){//选择省份，获得市级信息
          if(!address) return;
          this.getAreaCode(this.submitData.receiver_state,"cityObj");
          this.submitData.receiver_city = '';
          this.submitData.receiver_district = '';
          this.submitData.receiver_street = ''
        },
        selectCity(address){//选择市级，获得区级信息
          if(!address) return;
          this.getAreaCode(this.submitData.receiver_city,"areaObj");
          this.submitData.receiver_district = '';
          this.submitData.receiver_street = ''
        },
        selectArea(address){//选择区级，获得街道信息
          if(!address) return;
          this.getAreaCode(this.submitData.receiver_district,"streetObj");
          this.submitData.receiver_street = ''
        },
        save(formName){
          let self = this;
          console.log();
          self.submitData.receiver_state_name = self.provinceObj[self.submitData.receiver_state]||self.submitData.receiver_state;
          self.submitData.receiver_city_name = self.cityObj[self.submitData.receiver_city]||self.submitData.receiver_city;
          self.submitData.receiver_district_name = self.areaObj[self.submitData.receiver_district]||self.submitData.receiver_district;
          self.submitData.receiver_street_name = self.streetObj[self.submitData.receiver_street]||self.submitData.receiver_street;
          self.$refs[formName].validate((valid) => {
            console.log("valid",valid);
            if (!valid) {
              return;
            }
            self.ajax.postStream('/order-web/api/customer/receiverInfo/addReceriverInfo?permissionCode=ORDER_MERGEORDER_CUSTOMER_SAVE',self.submitData,res => {
              if(res.body.result) {
                //关闭弹窗
                self.params.callback(self.submitData);
                self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
              } else {
                res.body.msg && self.$message.error(res.body.msg);
              }
            }, err => {
              self.$message.error(err);
            });
          });
        }
      },
      mounted:function(){
        console.log(this.params);
        this.submitData.receiver_name = this.params.dataList.receiver_name;
        this.submitData.receiver_mobile = this.params.dataList.receiver_mobile;
        this.getAreaCode(1,"provinceObj");//新增
      },
    }
</script>

<style scoped>

</style>
