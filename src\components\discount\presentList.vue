//礼品申报列表
<template>
  <xpt-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :searchPage='search.page_name'
    :selection="selection"
    :pageTotal='pageTotal'
    @selection-change='select'
    @search-click='searchClick'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
  ></xpt-list>
</template>

<script>
    export default {
      name: "presentList",
      data () {
        let self = this;
        return {
          dataList:[],
          pageTotal:0,
          selectRow:[],
          selection:"checkbox",
          btns:[
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.getList();
              },
            },
            {
              type: "warning",
              txt: "提交申报",
              loading: false,
              click() {
                self.checkup();
              },
            },
            {
              type: "danger",
              txt: "终止申报",
              loading: false,
              click() {
                self.endCheck();
              },
            },
            {
              type: "success",
              txt: "礼品折现",
              loading: false,
              click() {
                self.discount();
              },
            },
            {
              type: "info",
              txt: "礼品退回",
              loading: false,
              click() {
                self.returned();
              },
            },
            {
              type: "primary",
              txt: "导出",
              loading: false,
              click() {
                self.pickout();
              },
            },
            {
              type: "primary",
              txt: "导出文件下载",
              loading: false,
              click() {
                self.excelDownload();
              },
            },
          ],
          cols:[
            {
              label: "申报编码",
              prop: "application_no",
              width: 120,
              redirectClick(row) {
                self.goDetail(row);
              },
            },
            {
              label: "优惠活动名称",
              prop: "discount_name",
              width: 160,
            },
            {
              label: "优惠项目",
              prop: "discount_item_dec",
              width: 160,
              formatter(val,index){
                let row = self.dataList[index] || {};
                //console.log(row);
                if (row) {
                  return row.discount_condition_dec + row.discount_item_dec;
                }
              }
            },
            {
              label: "礼品编码",
              prop: "present_no",
              width: 120,
            },
            {
              label: "礼品名称",
              prop: "present_name",
              width: 90,
            },
            {
              label: "买家昵称",
              prop: "customer_name",
              width: 120,
            },
            {
              label: "中奖排名",
              prop: "rank",
              width: 60,
            },
            {
              label: "状态",
              prop: "status",
              formatter(val){
                switch (val) {
                  case "CREATED" : return "创建";
                  case "SUBMIT_AUDIT" : return "提交待审核";
                  case "NOT_PURCHASE" : return "未采购";
                  case "PURCHASING" : return "采购中";
                  case "DELIVERED" : return "已发货";
                  case "TO_BE_RETURNED" : return "待退货";
                  case "RETURNED" : return "已退货";
                  case "CONVERT_INTO_CASH" : return "已折现";
                  case "END_SUBMIT" : return "已终止";
                  case "CANCEL" : return "取消";
                  case "ADD_ADDRESS_AUDIT" : return "新增地址待审核";
                }
              }
            },
            {
              label: "中奖名单编号",
              prop: "win_no",
              width: 170,
            },
            {
              label: "天猫公示订单",
              prop: "tid",
              width: 170,
            },
            {
              label: "中奖合并单号",
              prop: "merge_trade_no",
              width: 170,
              redirectClick(row) {
                let params = {};
                params.merge_trade_id = row.merge_trade_id;
                self.$root.eventHandle.$emit('creatTab', {
                  name: "合并订单详情",
                  params: params,
                  component: () => import('@components/order/merge.vue')
                });
              },
            },
            {
              label: "备注",
              prop: "remark",
            },
            {
              label: "发货单号",
              prop: "delivery_no",
              width: 150,
            },
            {
              label: "退货单号",
              prop: "return_no",
              width: 160,
            },
            {
              label: "店铺",
              prop: "original_shop_name",
              width: 160,
            },
            {
              label: "提交人",
              prop: "submitter_name",
              width: 100,
            },
            {
              label: "提交时间",
              prop: "submit_time",
              format: "dataFormat1" ,
              width: 140,
            },
            {
              label: "提交人业务分组",
              prop: "submitter_group_name",
              width: 100,
            },
          ],
          search:{
            page_name:"act_present_application",
            where:[],
            page:{
              length:50,
              pageNo:1
            }
          }
        }
      },
      methods:{
        getList(resolve){
          this.btns[0].loading = true;
          this.ajax.postStream('/price-web/api/actPresentApplication/list',this.search,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.pageTotal = res.body.content.count;
              this.selectRow = [];
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        //提交申报
        checkup(){
          let ids = [],status = "";
          this.selectRow.find(d=>{
            ids.push(d.id);
            status = d.status;
          });
          if(!ids.length){
			      this.$message.error('请选择要提交的数据！');return;
          }
          if (ids.length > 1){
            this.$message.error('只能提交单条数据进行申报！');return;
          }
		      if (status === "CREATED" || status === "NOT_PURCHASE" || status === "PURCHASING" || status === "SUBMIT_AUDIT" || status === "END_SUBMIT" || status === "CONVERT_INTO_CASH") {
            this.$root.eventHandle.$emit('creatTab', {
              name: "礼品提交申报",
              params: {
                id:ids[0],
              },
              component: () => import('@components/discount/presentCheckup.vue')
            });
		      } else {
            this.$message.error('只有状态值为"创建"、"未采购"、"未核对"、"采购中"、"已终止"、"已折现"时才能跳转至申报页面！');
          }
		    },
        //终止申报
        endCheck(){
          let self = this
          this.$root.eventHandle.$emit('openDialog', {
						ok() {
							self.confirmEndCheck()
						},
						okTxt: '是',
						no() {
						},
						noTxt: '否',
						txt: '是否确认【终止申报】？',
						noCancelBtn: true
					})
				},
				confirmEndCheck() {
					let ids = [],count = 0;
          this.selectRow.find(d=>{
            ids.push({id:d.id});
            if (d.status && d.status !== "CREATED" && d.status !== "NOT_PURCHASE" && d.status !== "SUBMIT_AUDIT"){
              count++;
            }
          });
          if(!ids.length){
            this.$message.error('当前未选择数据！');return;
          }
          if (count === 0) {
            this.ajax.postStream('/price-web/api/actPresentApplication/endSubmit?permissionCode=PRESENT_END_SUBMIT',ids,res => {
              if(res.body.result) {
                this.$message.success(res.body.msg);
                this.getList();
              } else {
                res.body.msg && this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
            });
          } else {
            this.$message.error('只有状态值为"创建"、"未采购"、"未核对"时才能终止申报！');
          }
				},
        //礼品折现
        discount(){
					let self = this
          this.$root.eventHandle.$emit('openDialog', {
						ok() {
							self.confirmDiscount()
						},
						okTxt: '是',
						no() {
						},
						noTxt: '否',
						txt: '是否确认【礼品折现】？',
						noCancelBtn: true
					})
				},
				confirmDiscount() {
					let ids = [],count = 0;
          this.selectRow.find(d=>{
            ids.push({id:d.id});
            if (d.status && d.status !== "CREATED" && d.status !== "NOT_PURCHASE" && d.status !== "SUBMIT_AUDIT"){
              count++;
            }
          });
          if(!ids.length){
            this.$message.error('当前未选择数据！');return;
          }
          if (count === 0) {
            this.ajax.postStream('/price-web/api/actPresentApplication/convertIntoCash?permissionCode=PRESENT_CONVERT_INTO_CASH',ids,res => {
              if(res.body.result) {
                this.$message.success(res.body.msg);
                this.getList();
              } else {
                res.body.msg && this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
            });
          } else {
            this.$message.error('只有状态值为"创建"、"未采购"、"未核对"时才能进行折现！');
          }
				},
        //礼品退回
        returned(){
          let ids = [],status = "";
          this.selectRow.find(d=>{
            ids.push(d.id);
            status = d.status;
          });
          if(!ids.length){
			      this.$message.error('请选择要退回的数据！');return;
          }
          if (ids.length > 1){
            this.$message.error('只能对单条数据进行退回操作！');return;
          }
          if (status === "DELIVERED" || status === "TO_BE_RETURNED" || status === "RETURNED" || status === "CANCEL") {            this.$root.eventHandle.$emit('creatTab', {
              name: "礼品退回",
              params: {
                id:ids[0],
              },
              component: () => import('@components/discount/presentReturned.vue')
            });
		      } else {
            this.$message.error('只有状态值为"已发货"、"待退货"、"已退货"时才能跳转至礼品退回页面！');
          }
        },
        //导出
        pickout(e){
          // let ids = [];
          // this.selectRow.find(d=>{
          //   ids.push(d.id)
          // });
          if(this.search.where.length < 1){
            this.$message.error('导出功能至少要有一个查询条件！');
          } else {
            //let $btn = e.target;
            //$btn.disabled = true;
            this.ajax.postStream('/price-web/api/actPresentApplication/exportExcel', this.search, res => {
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              });
              //$btn.disabled = false
            }, () => {
              //$btn.disabled = false
            })
          }
        },
        //导出文件下载
        excelDownload(){
          this.$root.eventHandle.$emit('alert', {
            component: () => import('@components/after_sales_report/export'),
            style:'width:900px;height:600px',
            title: '报表导出列表',
            params: {
              query: {
                type: 'EXCEL_TYPE_PRESENT_APPLICATION',
              },
            },
          })
        },
		//跳转详情页
        goDetail(row){
          if (!row.status && row.status === "") {
            this.$message.error("详情页跳转失败，状态值不能为空！");
            return;
          }
          if (row.status === "CREATED" || row.status === "NOT_PURCHASE" || row.status === "PURCHASING" || row.status === "SUBMIT_AUDIT" || row.status === "END_SUBMIT" || row.status === "CONVERT_INTO_CASH" || row.status == 'ADD_ADDRESS_AUDIT') {
            this.$root.eventHandle.$emit('creatTab', {
              name: "礼品提交申报",
              params: {
                id:row.id,
              },
              component: () => import('@components/discount/presentCheckup.vue')
            });
          } else {
            this.$root.eventHandle.$emit('creatTab', {
              name: "礼品退回",
              params: {
                id:row.id,
              },
              component: () => import('@components/discount/presentReturned.vue')
            });
          }
        },        searchClick (list, resolve){
          this.search.where = list;
          this.getList(resolve);
        },
        // 多选事件
        select(s){
          this.selectRow = s;
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page.length = pageSize;
          this.getList();
        },
        // 监听页数更改事件
        pageChange(page){
          this.search.page.pageNo = page;
          this.getList();
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style type="text/css">
    .el-dialog__wrapper {
        background-color: transparent
    }
</style>
