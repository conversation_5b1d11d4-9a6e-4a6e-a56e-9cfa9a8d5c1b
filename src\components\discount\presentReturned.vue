//礼品退回
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='24'>
        <el-button type='success' size='mini' @click="getList">刷新</el-button>
		<el-button type='primary' size='mini' @click="confirm" :disabled="ifReturned" :loading="ifLoading">确认退回</el-button>
        <el-button type='primary' size='mini' @click="fileSee">附件查看</el-button>
      </el-col>
    </el-row>
    <el-row	:gutter='40' >
      <el-tabs v-model="firstTab" >
        <el-tab-pane label="基本信息" name="returnedList">
          <el-form label-position="right" label-width="120px">
            <el-col :span="10" style="width: 33%">
              <el-form-item label="申报编码：" >
                <el-input v-model="dataList.application_no" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="礼品编码：" >
                <el-input v-model="dataList.present_no" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="天猫公示订单：" >
                <el-input v-model="dataList.tid" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="买家昵称：" >
                <el-input v-model="dataList.customer_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="提交人：" >
                <el-input v-model="dataList.submitter_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="店铺：" >
                <el-input v-model="dataList.original_shop_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 33%">
              <el-form-item label="优惠活动名称：" >
                <el-input v-model="dataList.discount_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="礼品名称：" >
                <el-input v-model="dataList.present_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖关联订单：" >
                <el-input v-model="dataList.winning_relate_tids" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="发货单号：" >
                <el-input v-model="dataList.delivery_no" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="收货人姓名：" >
                <el-input v-model="dataList.receiver_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="提交人业务分组：" >
                <el-input v-model="dataList.submitter_group_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="状态：">
                <el-input v-model="status" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 34%">
              <el-form-item label="优惠项目：" >
                <el-input v-model="discountItem" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="中奖合并单号：" >
                <a style="text-decoration:none;" href="javascript:;" @click="goMerge" title="点击进入合并订单信息页面">{{dataList.merge_trade_no}}</a>
              </el-form-item>
              <el-form-item label="实际发货订单：" >
                <el-input v-model="dataList.winning_delivery_relate_tids" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="是否新增地址">
                  <el-switch v-model="dataList.if_add_address" on-text="是" off-text="否" off-value="N" on-value="Y" disabled></el-switch>
              </el-form-item>
              <el-form-item label="收货人电话：" >
                <xpt-eye-switch v-model="dataList.receiver_mobile" class="presentReturnEyeSwitch" :disabled="true" :aboutNumber="dataList.application_no"></xpt-eye-switch>
                <!-- <el-input v-model="dataList.receiver_mobile" size='mini' style="width: 200px;" disabled></el-input> -->
              </el-form-item>
              <el-form-item label="提交时间：" >
                <el-input v-model="submit_time" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 100%">
              <el-form-item label="收货人地址：">
                <el-input v-model="dataList.receiver_address" size='mini' style="width: 94%;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 33%">
              <el-form-item label="退货类型："  v-if="dataList.status === 'DELIVERED'" required>
                <xpt-select-aux v-model='dataList.return_type' aux_name='PRESENT_RETURN_TYPE' style="width: 200px;" @change="typeChanged"></xpt-select-aux>
              </el-form-item>
              <el-form-item label="退货类型：" v-else>
                <xpt-select-aux v-model='dataList.return_type' aux_name='PRESENT_RETURN_TYPE' style="width: 200px;" @change="typeChanged" disabled></xpt-select-aux>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 33%">
              <el-form-item label="折扣金额："  v-if="dataList.status !== 'DELIVERED'">
                <el-input v-model='dataList.return_discount_amount' size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="折扣金额："  v-else-if="dataList.return_type === 'DISCOUNT_RESERVED'" required>
                <el-input v-model='dataList.return_discount_amount' size='mini' style="width: 200px;"></el-input>
              </el-form-item>
              <el-form-item label="折扣金额："  v-else>
                <el-input v-model='dataList.return_discount_amount' size='mini' style="width: 200px;"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 34%">
              <el-form-item label="退货单号：" v-if="dataList.status === 'DELIVERED'" required>
                <el-input v-model='dataList.return_no' size='mini' style="width: 200px;"></el-input>
              </el-form-item>
              <el-form-item label="退货单号：" v-else>
                <el-input v-model='dataList.return_no' size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 100%">
            <el-form-item label="退货地址：" style="height:32px;">
              <el-input v-model="dataList.return_address" size='mini' style="width: 94%;" disabled></el-input>
            </el-form-item>
            </el-col>
            <el-col :span="10" style="width: 100%;">
              <el-form-item label="备注：" v-if="dataList.status === 'DELIVERED'" style="height: 60px;">
                <el-input v-model="dataList.remark" size='mini' type='textarea' style="width: 94%;"></el-input>
              </el-form-item>
              <el-form-item label="备注：" v-else style="height: 60px;">
                <el-input v-model="dataList.remark" size='mini' type='textarea' style="width: 94%;" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class='xpt-flex__bottom' id='bottom' v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane label="操作记录" name="operationHistory" class='xpt-flex'>
          <xpt-list
            :data='operationList'
            :colData='operationCols'
            selection=''
            :showHead='false'
            :orderNo="true"
          ></xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
  import Vue from 'vue'
  import Fn from '@common/Fn.js'
    export default {
      name: "presentReturned",
      props:["params"],
      data(){
        return {
          dataList:{},
          ifLoading:false,
          ifReturned:false,
          ifDiscount:false,
          discountItem:"",//优惠项目
          status:'',//状态
          create_time:'',//创建时间
          submit_time:'',//提交时间
          firstTab:'returnedList',
          secondTab:'operationHistory',
          operationList:[],
          operationCols:[
            {
              label:'用户',
              prop:'operator_name'
            },
            {
              label:'操作名称',
              prop:'operation_name'
            },
            {
              label:'操作时间',
              prop:'operation_time',
              format: "dataFormat1"
            },
          ],
        }
      },
      methods:{
				// 查看附件
				fileSee () {
					let self = this
					let params = {
						parent_no : this.dataList.application_no,
						child_no : null,
						ext_data : null,
						parent_name :'ORDER',
						child_name : 'present',
						parent_name_txt :'礼品申购',
						child_name_txt : null,
					}
					params.ifClose = this.dataList.status == 'CREATED'
					params.callback = d=>{
						let i = d.length;
						while(i--) {
							let json = d[i].group_value_json;
							json = JSON.parse(json).content || {};
							if(json instanceof Array) {
								json = json[0];
							}
						}
					}
					this.$root.eventHandle.$emit('alert',{
						params:params,
						component:()=>import('@components/common/download.vue'),
						style:'width:1000px;height:600px',
						title:'下载列表'
					})
				},
        getList(){
          this.ajax.get('/price-web/api/actPresentApplication/detail?applicationId=' + this.params.id,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content;
              this.discountItem = res.body.content.discount_condition_dec + res.body.content.discount_item_dec;
              this.create_time = Fn.dateFormat(res.body.content.create_time,'yyyy-MM-dd hh:mm:ss');
              this.submit_time = Fn.dateFormat(res.body.content.submit_time,'yyyy-MM-dd hh:mm:ss');
              this.configStatus();
              this.configReturnType();
              this.getOperationHistory();
              Vue.prototype.tabNo[this.params.tabName] = res.body.content.application_no || '';
            } else {
              res.body.msg && this.$message.error(res.body.msg);
              Vue.prototype.tabNo[this.params.tabName] = '';
            }
          }, err => {
            this.$message.error(err);
            Vue.prototype.tabNo[this.params.tabName] = '';
          });
        },
        //获取操作记录
        getOperationHistory(){
          this.ajax.postStream('/order-web/api/receipt/v2/logList',{source_id:this.params.id},res => {
            if(res.body.result && res.body.content) {
              this.operationList = res.body.content.list || [];
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        confirm(){
          this.ifLoading = true;
          this.ajax.postStream('/price-web/api/actPresentApplication/returnPresent',this.dataList,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            this.ifLoading = false;
          }, err => {
            this.$message.error(err);
            this.ifLoading = false;
          });
        },
        //跳转合并订单详情
        goMerge(){
          let params = {};
          params.merge_trade_id = this.dataList.merge_trade_id;
          this.$root.eventHandle.$emit('creatTab', {
            name: "合并订单详情",
            params: params,
            component: () => import('@components/order/merge.vue')
          });
        },
        configStatus(){
          console.log(this.dataList.status);
          this.status = "";
          switch (this.dataList.status) {
            case "CREATED" : this.status =  "创建"; break;
            case "SUBMIT_AUDIT" : this.status =  "提交待审核"; break;
            case "NOT_PURCHASE" : this.status = "未采购"; break;
            case "PURCHASING" : this.status =  "采购中"; break;
            case "DELIVERED" : this.status = "已发货"; break;
            case "TO_BE_RETURNED" : this.status = "待退货"; break;
            case "RETURNED" : this.status = "已退货"; break;
            case "CONVERT_INTO_CASH" : this.status = "已折现"; break;
            case "END_SUBMIT" : this.status = "已终止"; break;
            case "CANCEL" : this.status = "取消"; break;
            case "ADD_ADDRESS_AUDIT" : this.status = "新增地址待审核"; break;
          }
		  if (this.status === "已发货") {
            this.ifReturned = false;
            //this.ifDiscount = false;
          } else {
            this.ifReturned = true;
            //this.ifDiscount = true;
          }        },
        configReturnType(){
          // switch (this.dataList.return_type) {
          //   case "DISCOUNT_RESERVED" : this.dataList.return_type =  "客户折价保留礼品"; break;
          //   case "BACK_TO_SUPPLIER" : this.dataList.return_type = "客户退回供应商"; break;
          //   case "BACK_TO_LSMY" : this.dataList.return_type = "客户退回林氏"; break;
          // }
        },
        typeChanged(){
          console.log("type",this.dataList.return_type);
        }
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style>
.presentReturnEyeSwitch .eyeSwitchInput{
  width:200px;
  max-width:200px;
}
</style>
