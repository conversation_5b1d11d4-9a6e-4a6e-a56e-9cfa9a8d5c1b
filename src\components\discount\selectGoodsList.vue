<!-- 选择商品 -->
<template>
	<xpt-list 
		:data='goodsList' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='pageTotal' 
		:searchPage='page_name' 
		selection='checkbox' 
		@selection-change='selectionChange'
		@search-click='searchData' 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
	></xpt-list>
</template>
<script>
export default {
	data(){
		let self = this
		return {
			cols: [
				{
					label: '商品编码',
					prop: 'materialNumber'
				}, {
					label: '商品名称',
					prop: 'materialName'
				}, {
					label: '商品规格',
					prop: 'materialSpecification'
				}, {
					label: '物料分组',
					prop: 'inventoryCategory'
				}, {
					label: '单位',
					prop: 'materialUnit'
				}
			],
			btns: [{
				type: 'primary',
				txt: '确认',
				click: self.close
			}],
			search: {
				length: self.pageSize,
				pageNo: 1,
			},
			goodsList:[],
			selectCode:[],
			pageTotal:0,
			page_name: 'cloud_material',
			where: []
		}
	},
	props:['params'],
	methods:{
		close(){
			if(!this.selectCode) {
				this.$message.error('请先选择商品');
				return;
			}

			this.params.callback(this.selectCode)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		searchData(obj, resolve){
			this.where = obj
			this._getGoodsList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.length = pageSize
			this._getGoodsList();
		},
		pageChange(page){
			this.search.pageNo = page
			this._getGoodsList();
		},
		_getGoodsList(resolve){
			var self = this
			console.log(self.params)
			var data = {
				page: this.search,
				page_name: this.page_name,
				where: this.where,
				disableStatus:'A',
				sale_enabled: '1',
                audit_flag: '1',
				invalid_sale: '0',
				// 加价换购只能选择成品，不能选择组合商品
				inventoryCategoryList: self.params.isUpgrade ? ['成品'] : ['成品','组合商品']
			}
			// 当当前的是经销商生活馆囤货订单 he 采购经销生活馆订单的时候，需要添加一下参数作为过滤
			if (/^(DEALER_SHG_STOCK|DEALER_SHG)$/.test(this.params.businessTypeTrade)) {
				data.groupNumber='XSP'
			}
			this.ajax.postStream('/material-web/api/material/getMaterialList',data,d=>{
				if(d.body.result&&d.body.content){
					self.pageTotal = d.body.content.count;
					self.goodsList = d.body.content.list||[];
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve();
			})
		},
		selectionChange(obj) {
			this.selectCode = obj
			console.log(this.selectCode);
		},
	},
	mounted(){
		this._getGoodsList();
	}
}
</script>