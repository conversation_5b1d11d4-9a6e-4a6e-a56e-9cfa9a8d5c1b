<!-- 秒杀商品列表新增商品 -->
<template>
	<xpt-list3
		:data='goodsList' 
		:btns='btns' 
		:colData='cols'
		:pageTotal='pageTotal' 
		:searchPage='query.page_name'
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@row-dblclick='rowDblclick' 
		@selection-change='selectionChange'
		@search-click='search' 
	>
	</xpt-list3>
</template>
<script>
export default {
	data(){
		let self = this;
		return {
			goodsList:[],
			selectData:'',
			pageTotal:0,
			query: {
				page_no: 1,
				page_size: self.pageSize,
				page_name: 'system_external_material',
				where: []
			},
			btns: [{
				type: 'primary',
				txt: '确认',
				click: self.close
			}],
			cols: [
				{
					label: 'sku_id',
					prop: 'sku_id'
				}, {
					label: '商品编码',
                    prop: 'material_number',
                    width: 120
				}, {
					label: '商品名称',
					prop: 'goods_name',
                    width: 200
				}, {
					label: '商品规格',
					prop: 'spec_name',
                    width: 300
				}
			]
		}
	},
	props:['params'],
	methods:{
		close(){
			if(!this.selectData) {
				this.$message.error('请先选择商品');
				return;
			}
			console.log('哈哈看', this.selectData)
			this.selectData.forEach(item => {
				item.status = 'Y'
			})
			console.log('关闭哈哈看', this.selectData)
            this.params.close(this.selectData)
            this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
		search(obj, resolve) {
			this.query.where = obj;
			this._getGoodsList(resolve);
		},
		selectionChange(obj){
			this.selectData = obj; 
		},
		pageSizeChange(pageSize){
			this.query.page_size = pageSize;
			this._getGoodsList();
		},
		pageChange(page){
			this.query.page_no = page;
			this._getGoodsList();
		},
		_getGoodsList(resolve){
			this.ajax.postStream('/price-web/api/actSpellInstaskill/selectMaterialList', this.query,d=>{
				if(d.body.result&&d.body.content){
					this.pageTotal = d.body.content.count || 0;
					this.goodsList = d.body.content.list || [];
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve();
			})
		},
		rowDblclick(obj) {
			this.selectData = obj;
			this.close();
		}
	},
	mounted() {
		this._getGoodsList()
	}
}
</script>