<!-- 店铺物料查询 -->
<template>
  <div class='xpt-flex'>
    <el-row :gutter='10' class='xpt-top'>
      <el-form ref='query' :rules='rules' :model='query' label-position="right" :inline="true">
        <el-col :span="15">
          <el-form-item label="标题：" prop='title' label-width="80px">
            <xpt-input v-model='query.title'  size='mini' ></xpt-input>
          </el-form-item>
          <el-form-item label="下架：" prop='soldout' label-width="80px">
            <el-select v-model='query.soldout' size='mini' clearable>
              <el-option v-for='(row,index) in undercarriageList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="平台商品状态：" prop='status' label-width="130px">
            <el-select v-model='query.status' size='mini' clearable>
              <el-option v-for='(row,index) in goodStatusList' :key='index' :label='row.name' :value='row.value'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="skuId：" prop='materialNumber' label-width="90px">
            <xpt-input v-model='query.materialNumber'  size='mini' ></xpt-input>
          </el-form-item>
        </el-col>

        <el-col :span="9" class='xpt-align__right'>
          <el-button type='success' size='mini' @click='queryData' :disabled='queryBtnStatus' :loading='queryBtnStatus'>查询</el-button>
          <el-button type='primary' size='mini' @click='reset'>重置查询条件</el-button>
          <el-button type='primary' size='mini' :loading="savingMaterial" @click='realateGoods'>批量关联商品</el-button>
          <el-button type='primary' size='mini' :loading="savingShop" @click='shopUpDown'>批量关联店铺</el-button>
          <xpt-upload-v3
            uploadBtnText="导入"
            :uploadSize="20"
            acceptTypeStr=".xlsx,.xls"
            :dataObj="{}"
            :disabled="false"
            :ifMultiple="false"
            :showSuccessMsg="false"
            @uploadSuccess="uploadSuccess"
            btnType="success"
            style="display: inline-block; margin: 0px 10px"
          ></xpt-upload-v3>
          <el-button type='primary' size='mini' :loading="savingShop" @click="showUploadResult()">导入结果查询</el-button>
        </el-col>
      </el-form>
    </el-row>

      <xpt-list
        :showHead='false'
        :data='list'
        :colData='cols'
        :pageTotal='count'
        @selection-change='handleSelectionChange'
        @page-size-change='pageSizeChange'
        @current-page-change='currentPageChange'
      ></xpt-list>

  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this
      return {
        uploadUrl:'/order-web/api/externalMaterial/addBatchImportProductListInfo',
        query: {
          page_no: 1,// 页码
          page_size: self.pageSize, // 页数
          // fnumber:"",//物料编码
          title:"",//物料名称
          // shop_name:"",//店铺名称
          soldout:"",//下架
          // tb_product_id:"",//淘宝商品id
          //fnumberList:null//物料编码列表,
          status: '', //商品状态
          materialNumber: '', // 淘宝skuId
          // tb_sku_id: '', //sku_id
          // material_source: '' //物料来源
        },
        rules:{},
        // 查询按钮状态
        queryBtnStatus: false,
        // 导出按钮状态
        exportBtnStatus: false,
        queryBtnStatusTimer: '',
        exportBtnStatusTimer: '',
        savingShop: false,
        savingMaterial: false,
        selectedData:[],
        count:0,
        list:[],
        cols:[
          {
            label: '商品标题',
            prop: 'title',
            width: 220
          },
          {
            label: '套餐编码',
            prop: 'fnumber',
            width: 120
          },
          {
            label: '淘宝商品ID',
            prop: 'tb_product_id',
            width: 100
          },
          {
            label: '淘宝SKUID',
            prop: 'tb_sku_id',
            width: 110,
            redirectClick(row) {
              self.toEditFun(row)
            },
          },
          {
            label: '淘宝链接',
            prop: 'tb_link'
          },
          {
            label: '一口价',
            prop: 'fixed_price'
          },
          {
            label: '下架',
            prop: 'soldout'
          },
          {
            label: '平台商品状态',
            prop: 'status',
            width:100,
            formatter(val){
              switch(val){
                case 'instock':   return '库中';break;
                case 'onsale':   return '出售中';break;
                case 'sold_out':   return '售罄';break;
                case '1':   return '上架';break;
                case '2':   return '下架';break;
                case '3':   return '售罄';break;
                case '4':   return '已删除';break;
              }
            }
          },
          {
            label: '上架时间',
            prop: 'puaway_dte',
            format:"dataFormat1",
            width:130
          },
          {
            label: '下架时间',
            prop: 'delist_time',
            format:"dataFormat1",
            width:130
          },
          {
            label: '修改时间',
            prop: 'modified_time',
            format:"dataFormat1",
            width:130
          },

        ],
        undercarriageList: [
          {
            name: '是',
            value: '0'
          }, {
            name: '否',
            value: '1'
          }
        ],
        PDDStatusList: [
          {
            name: '上架',
            value: '1',
          }, {
            name: '下架',
            value: '2',
          }, {
            name: '售罄（拼多多）',
            value: '3',
          }, {
            name: '已删除',
            value: '4',
          }
        ],
        TAOBAOStatusList: [
          {
            name: '出售中',
            value: 'onsale',
          }, {
            name: '库中',
            value: 'instock',
          }, {
            name: '售罄（淘宝）',
            value: 'sold_out',
          }
        ],
        goodStatusList: []
      }
    },
    methods: {
      reset() {
        for(let v in this.query) {
          if(!(v === 'page_size' || v === 'page_no')) {
            this.query[v] = '';
          }
        }
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.queryData();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.queryData();
      },
      queryData() {
        this.$refs.query.validate((valid) => {
          if(valid) {
            let data = JSON.parse(JSON.stringify(this.query));
            this.queryBtnStatus = true;
            this.ajax.postStream('/order-web/api/externalMaterial/pageShopProductMaterielBySystemExternalMaterial', data, res => {
              this.queryBtnStatus = false;
              if(res.body.result && res.body.content) {
                let content = res.body.content;
                this.list = content?.list?.splice(0, 20) || [];
                this.count = content.count || 0;
              }
            }, err => {
              this.$message.error(err);
              this.queryBtnStatus = false;
            })
          }
        })
      },
      toEditFun(row){
        this.$root.eventHandle.$emit('creatTab',{name:'编辑定制方案信息',params:{row:row},component:()=>import('@components/after_sales_report/add_set.vue')});
      },
      // 关联商品
      realateGoods() {
        if (!this.selectedData.length) {
          this.$message.info('请选择数据');
          return;
        }
        let params = {},
        self = this;
        let data = {
          material_code:"",
          material_name:'',
          row_type:'',
          specific_desc:''
        }
        // 选择商品

        params.callback = d => {
          self.saveMdMaterialRelation(d);
        }
        self.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/discount/selectGoodsList'),
          style: 'width:800px;height:500px',
          title: '商品列表'
        });
      },
      // 打开店铺
      shopUpDown(){
        console.log('this.selectedData: ', this.selectedData);
        if (!this.selectedData.length) {
          this.$message.info('请选择数据');
          return;
        }
        let self = this;
        let shopIds = [];
        let params = {
          shop_status: 'OPEN',
          callback(d){
            console.log('d: ', d);
            self.saveShopRelation(d);
          }
        }
        self.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/shop/list'),
            style:'width:800px;height:500px',
            title:'店铺列表'
        })
      },
      saveShopRelation(d) {
        this.savingShop = true;
        let self = this;
        let data = {
        	relation_type: 'SHOP',
        	shopList: d.map(item => item.shop_id),
          productList: this.selectedData.map(item => item.id),
        }
        let url = '/order-web/api/externalMaterial/batchRelateShopProductWithShop';
        self.ajax.postStream(url,data,(res)=>{
          this.savingShop = false;
        	let returnData = res.body;
        	self.$message({
        		type:returnData.result?'success':'error',
        		message:returnData.msg
        	});
        	if(returnData.result){
        		this.queryData()
        	}
        });
      },

      saveMdMaterialRelation(d){
        this.savingMaterial = true;
        let self = this;
        let data = {
        	relation_type: 'GOODS',
        	goodsList: d.map(item => item.materialId),
          productList: this.selectedData.map(item => item.id),
        }
        let url = '/order-web/api/externalMaterial/batchRelateShopProductWithGoods';
        self.ajax.postStream(url,data,(res)=>{
          this.savingMaterial = false;
        	let returnData = res.body;
        	self.$message({
        		type:returnData.result?'success':'error',
        		message:returnData.msg
        	});
        	if(returnData.result){
        		this.queryData()
        	}
        });
      },

      /**
      *checkbox选择改变
      **/
      handleSelectionChange(selects){
        this.selectedData = selects.length?selects:[];
      },

      //导入结果
      showUploadResult() {
        this.$root.eventHandle.$emit("alert", {
          style: "width:900px;height:600px",
          title: "导入结果",
          params: {
            url: "/order-web/api/externalMaterial/batchImportProduct/list",
            data: {},
            showDownload: true,
          },
          component: () => import("@components/common/eximport"),
        });
      },

      //上传成功返回结果
      uploadSuccess(result) {
        if (result.length > 0 && !!result[0].path) {
          this.importGoodFileUrl(result[0].path);
        }
      },

      // 导入关联商品
      importGoodFileUrl(fileUrl) {
        let params = {
          path: fileUrl,
        };
        this.ajax.postStream(
          "/order-web/api/externalMaterial/addBatchImportProductListInfo",
          params,
          (res) => {
            if (res.body.result) {
              this.$message.success(res.body.msg);
            } else {
              this.$message.error(res.body.msg);
            }
          },
          (err) => {
            this.$message.error(err);
          }
        );
      },
    },
    mounted() {
      this.goodStatusList = this.PDDStatusList.concat(this.TAOBAOStatusList)
    },
    watch: {
      // 'query.material_source': function (n, o) {
      //   if (n === '') {
      //     this.goodStatusList = this.PDDStatusList.concat(this.TAOBAOStatusList)
      //   } else if (n === 'PDD'){
      //     if (/^(onsale|instock)$/.test(this.query.status)) {
      //       this.query.status = ''
      //     }
      //     this.goodStatusList = this.PDDStatusList
      //   } else if (n === 'TAOBAO'){
      //     if (!/^(onsale|instock)$/.test(this.query.status)) {
      //       this.query.status = ''
      //     }
      //     this.goodStatusList = this.TAOBAOStatusList
      //   }
      // }
    }
  }
</script>
<style type="text/css" scoped>
  .el-input{
    width: 150px;
  }
  .el-select{width: 150px;}
</style>

