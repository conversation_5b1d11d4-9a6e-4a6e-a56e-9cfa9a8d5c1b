<!-- 查看项目详情 -->
<template>
	<div>
		
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="优惠商品详情" name="orderInfo">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="submitData"  ref="submitData">
						<el-col :span='24'>
							<el-form-item label="物料编码" prop="material_number">
                  				<el-input type="text" v-model="submitData.material_number" size='mini' :disabled="true"></el-input>
								<!-- <span>{{submitData.material_number}}</span> -->
							</el-form-item>
							<el-form-item label="物料名称" prop="material_name">
                  				<el-input type="text" v-model="submitData.material_name" size='mini'  :disabled="true"></el-input>
								<!-- <span>{{submitData.material_name}}</span> -->
							</el-form-item>
							<el-form-item label="规格描述" prop="material_specification">
                  				<el-input type="text" v-model="submitData.material_specification" size='mini' style="width:100%;" :disabled="true"></el-input>
								<!-- <span>{{submitData.material_specification}}</span> -->
							</el-form-item>
							<el-form-item label="单位" prop="material_unit">
                  				<el-input type="text" v-model="submitData.material_unit" size='mini' :disabled="true"></el-input>
								<!-- <span>{{submitData.material_unit}}</span> -->
							</el-form-item>
							<el-form-item label="价格" prop="price">
                  				<el-input type="text" v-model="submitData.act_price" size='mini' :disabled="true"></el-input>
								<!-- <span>{{submitData.act_price}}</span> -->
							</el-form-item>
							<el-form-item label="优惠金额" prop="discount_amount">
                  				<el-input type="text" v-model="submitData.discount_amount" size='mini' :disabled="true"></el-input>
								<!-- <span>{{submitData.discount_amount}}</span> -->
							</el-form-item>
							<el-form-item label="成本" prop="cost_price">
                  				<el-input type="text" v-model="submitData.cost_price" size='mini' :disabled="true"></el-input>
								<!-- <span>{{submitData.cost_price}}</span> -->
							</el-form-item>
							<el-form-item label="名义售价" prop="nominal_price">
                  				<el-input type="text" v-model="submitData.nominal_price" size='mini' :disabled="true"></el-input>
							</el-form-item>
							<el-form-item label="三包减免" prop="three_reduction">
                  				<el-input type="text" v-model="submitData.three_reduction" size='mini'  :disabled="true"></el-input>
								<!-- <span>{{submitData.three_reduction}}</span> -->
							</el-form-item>
							<el-form-item label="物流减免" prop="logistics_reduction">
                  				<el-input type="text" v-model="submitData.logistics_reduction" size='mini' :disabled="true"></el-input>
								<!-- <span>{{submitData.logistics_reduction}}</span> -->
							</el-form-item>
							<el-form-item label="经销结算价" prop="dealer_price">
                  				<el-input type="text" v-model="submitData.dealer_price" size='mini' :disabled="true"></el-input>
							</el-form-item>
							<el-form-item label="限量" prop="count_limit">
                  				<el-input type="text" v-model="submitData.count_limit" size='mini' :disabled="true"></el-input>
								<!-- <span>{{submitData.count_limit}}</span> -->
							</el-form-item>
							<el-form-item label="个人承担比例" prop="person_bear_ratio">
                  				<el-input type="text" v-model="submitData.person_bear_ratio" size='mini' :disabled="true"></el-input>
								<!-- <span>{{submitData.person_bear_ratio}}</span> -->
							</el-form-item>
							<el-form-item label="是否享受优惠" prop="if_enjoy_discount">
								<el-switch v-model="submitData.if_enjoy_discount" on-text="是" off-text="否" on-value="Y" off-value="N" disabled ></el-switch>
								<!-- <span>{{submitData.if_enjoy_discount}}</span> -->
							</el-form-item>
							<el-form-item label="是否礼品" prop="if_present">
								<el-switch v-model="submitData.if_present" on-text="是" off-text="否" on-value="Y" off-value="N" disabled ></el-switch>
							</el-form-item>
							<el-form-item label="备注" prop="remark" style="height:50px">
                  				<el-input type="textarea" v-model="submitData.remark"  size='mini' style="width:100%; min-width:240px;" :disabled="true"></el-input>
								<!-- <span>{{submitData.remark}}</span> -->
							</el-form-item>
						</el-col>
			    </el-form>
			  </el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js';
	import Fn from '@common/Fn.js';
	export default {
		props:["params"],
		data() {
			var _this = this;
			var self = this;
			return{
				firstTab:"orderInfo",
				submitData:{},
			}
		},
		methods : {
		
			// 关闭标签页
			closeTab(){
				var self = this,isUpdate;
				console.log(self.submitData,self.initialData)
				isUpdate = this.compareData(self.submitData,self.initialData);

				if(isUpdate){
					self.$root.eventHandle.$emit('openDialog',{
						ok(){
							self.save(()=>{
								self.$root.eventHandle.$emit('removeTab',self.params.tabName)
							})
						},
						no(){
							self.$root.eventHandle.$emit('removeTab',self.params.tabName)
						}
					})
				}else{
					self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				}
			},
			
				
			
			
			
		},
		mounted:function(){
			var self = this;
			
			self.submitData = self.params.row
			self.params.__close = self.closeTab
		}
	}
</script>
<style module>
.row-height :global(.el-form-item__content) {
	height: auto!important;
	white-space: nowrap;
}
.el-form-item__content .detail-address{
	width:300px;
}
</style>