<template>
<div class="xpt-flex">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span='18'><el-button type='warning' size='mini' @click='closeAlert'>确认</el-button></el-col>
		</el-row>
		<el-row class="mgb10">
            <el-form :rules='rules' :model='query' ref='query' label-position="right" label-width="100px">
                <el-col :span="9">
                    <el-form-item label="库存sku_id：">
                        <!-- icon='search'  :on-icon-click='selectSkuId' -->
                        <el-input size='mini' style='width: 100%'
                            v-model='query.sku_id'
                            @blur="checkSkuId"
                            icon='search'
                            :on-icon-click='selectSkuId'
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="是否生效：">
                        <el-switch v-model="query.status" on-text="是" off-text="否" ></el-switch>
                    </el-form-item>
                </el-col>
                <el-col :span="15">
                    <el-form-item label="商品编码：">
                        <xpt-input v-model='query.material_number' size='mini' :disabled="true"></xpt-input>
                    </el-form-item>
                    <el-form-item label="商品名称：">
                        <xpt-input v-model='query.goods_name' size='mini' :disabled="true"></xpt-input>
                    </el-form-item>
                </el-col>
                <el-col :span="20">
                    <el-form-item label="商品规格：">
                        <xpt-input v-model='query.spec_name' size='mini' :disabled="true"></xpt-input>
                    </el-form-item>
                </el-col>
            </el-form>
		</el-row>
</div>
</template>
<script>
export default {
    props: ['params'],
    data () {
        return {
            query: {
                sku_id: '',
                material_number: '',
                spec_name: '',
                status: true,
                goods_name: ''
            },
            rules:{}
        }
    },
    methods: {
        selectSkuId () {
            let self = this;
            let params = {
                close (d) {
                    self.query.sku_id = d.sku_id
                    self.query.material_number = d.material_number
                    self.query.spec_name = d.material_name
                    self.query.goods_name = d.material_desc
                }
            };
            this.$root.eventHandle.$emit('alert',{
                params: params,
                title:'库存sku_id列表',
                style:'width:600px;height:250px',
                component:()=>import('@components/discount/selectSkuIdList'),
            })
        },
        checkSkuId () {
            if (!this.query.sku_id) {
                this.$message.error('请填写库存sku_id')
                return
            }
            let self = this
            this.ajax.postStream('/price-web/api/actSpellInstaskill/selectBySku', {sku_id: this.query.sku_id}, function(d){
                if(d.body.result && d.body.content){
                    self.$message.success( d.body.msg || '')
                    self.query.sku_id = d.body.content.sku_id
                    self.query.material_number = d.body.content.material_number
                    self.query.spec_name = d.body.content.spec_name
                    self.query.goods_name = d.body.content.goods_name
                }else{
                    self.$message.error(d.body.msg)
                }
            }, err => {
                self.$message.error(err)
            })
        },
        closeAlert () {
            // 关闭弹窗之前校验是否有反写信息
            if (!this.query.sku_id && (!this.query.material_number || !this.query.goods_name)) {
                this.$message.error('请填写库存sku_id后，鼠标光标移出库存sku_id填写区域以获取数据')
                return
            }
           // 关闭弹窗
           this.query.status = this.query.status == true ? 'Y' : 'N'
            this.params.close(this.query)
            this.$root.eventHandle.$emit('removeAlert',this.params.alertId) 
        }
        
    },
    mounted () {
        this.$message.info('请填写库存sku_id后，鼠标光标移出库存sku_id填写区域以获取数据')
    }
}
</script>
<style lang="stylus" scoped>
.el-input, .el-select, .el-date-editor.el-input
    width: 100%
</style>