<template>
    <xpt-list
        :data='list'
        :colData='cols'
        :btns='btns'
        :pageTotal='pageTotal'
        :orderNo='true'
        :selectable='selectableFun'
        :searchPage='serchData.page_name'
        @search-click='search'
        @selection-change='selects'
        @page-size-change='pageChange'
        @current-page-change='currentPageChange'
    >
            <el-button
                    slot='btns'
                    size="mini"
                    type="info"
                    @click="downloadTemplate()"
                    :disabled="false"
                    >模板下载</el-button
                >
                <xpt-upload-v3
                    slot='btns'
                    uploadBtnText="导入"
                    :uploadSize="20"
                    acceptTypeStr=".xlsx,.xls"
                    :dataObj="uploadDataObj"
                    :disabled="false"
                    :ifMultiple="false"
                    :showSuccessMsg="false"
                    @uploadSuccess="uploadSuccess"
                    btnType="success"
                    style="display: inline-block; margin: 0px 10px"
                ></xpt-upload-v3>
                <el-button
                    slot='btns'
                    size="mini"
                    type="warning"
                    @click="showUploadResult()"
                    :disabled="false"
                    >导入结果</el-button
                >
    </xpt-list>
</template>
<script>
export default {
    data () {
        let self = this;
        return {
            serchData:{
                page_name:"act_spell_instakill",
                where:[],
                page_size: self.pageSize,     //页数
                page_no:1   //页码
            },
            pageTotal:0,
            list: [],
            cols: [
                {
                    label: 'sku_id',
                    prop: 'sku_id',
                    width: 110
                }, {
                    label: '商品编码',
                    prop: 'material_number',
                    width: 120
                }, {
                    label: '名称',
                    prop: 'goods_name',
                    width: 200
                }, {
                    label: '规格描述',
                    prop: 'spec_name',
                    width: 300
                }, {
                    label: '状态',
                    prop: 'status',
                    formatter: prop => ({
                        Y: '生效',
                        N: '失效',
                    }[prop] || prop),
                }, {
                    label: '创建人',
                    prop: 'creator_name',
                }, {
                    label: '创建时间',
                    prop: 'create_time',
                    format: 'dataFormat1',
                    width: 130,
                }, {
                    label: '最后修改人',
                    prop: 'last_modifier_name',
                }, {
                    label: '最后修改时间',
                    prop: 'last_modify_time',
                    format: 'dataFormat1',
                    width: 130,
                }
            ],
            btns: [
                {
                    type: 'primary',
                    txt: '新增',
                    disabled: false,
                    click: () => this.add(),
                }, {
                    type: 'success',
                    txt: '保存',
                    disabled: false,
                    click: () => this.saveUpate('save'),
                }, {
                    type: 'warning',
                    txt: '失效',
                    disabled: false,
                    click: () => this.saveUpate('disabled'),
                }, {
                    type: 'warning',
                    txt: '生效',
                    disabled: false,
                    click: () => this.saveUpate('enabled'),
                }, {
                    type: 'danger',
                    txt: '删除',
                    disabled: false,
                    click: () => this.saveUpate('delete'),
                }, {
                    type: 'success',
                    txt: '刷新',
                    disabled: false,
                    click: () => this._getList(),
                },
            ],
            addList: [],
            selectsData: [],
            ifOperator: false,
            uploadDataObj: {
                parent_name: "秒杀商品",
                parent_no: `SPIKE_MERCHANDISE`, //主要通过该参数获取附件列表
                child_name: null,
                child_no: null,
                content: {},
            },
        }
    },
    mounted() {
        this._getList()
    },
    methods: {
        // 搜索
        search(list, resolve) {
            // do something..
            var self = this;
            self.serchData.where=list;
            self._getList(resolve);
        },
        //查询列表
        _getList(resolve, msg){
            this.addList = []
            var self=this;
            self.ajax.postStream('/price-web/api/actSpellInstaskill/list', this.serchData, function(d){
                if(d.body.result){
                    self.list = d.body.content.list
                    self.pageTotal=d.body.content.count;
                    self.$message.success(msg || d.body.msg)
                }else{
                    self.list=[];
                    self.pageTotal=0;
                    self.$message.error(d.body.msg)
                }
                    resolve && resolve();
                    self.selectRole = [];
            }, err => {
                resolve && resolve();
            })
        },
        // 多选数据
        selects(val) {
            this.selectsData = val
        },
        saveUpate (type){
            this.ifOperator = true
            let saveParams = [], disabledParams = [], enabledParams = [], deleteParams = [], self = this
            this.addList.forEach(item => {
                saveParams.push({status: item.status, sku_id: item.sku_id})
            })
            let paramsObj = {
                save: saveParams,
                delete: deleteParams,
                disabled: disabledParams,
                enabled: enabledParams
            }, msg = '', ifSave = false, ifEnabledCheck = null, ifDisabledCheck = null, ifDeleteCheck = null
            if (type === 'save') {
                if (paramsObj[type].length <= 0) {
                    msg = '请至少新增一行商品'
                }
            } else {
                if (((type === 'enabled' || type === 'disabled') && saveParams.length > 0)) {
                    msg = '列表存在新增商品，新增商品请先执行保存操作'
                } else if (this.selectsData.length <= 0) {
                    msg = '请至少选择一行商品执行操作'
                } else if (type === 'disabled') {
                    ifDisabledCheck = this.selectsData.some( item => {
                        return item.status === 'N'
                    })
                    msg = ifDisabledCheck == true ? '请选择状态等于生效的行来执行失效操作' : msg
                } else if (type === 'enabled') {
                    ifEnabledCheck = this.selectsData.some( item => {
                        return item.status === 'Y'
                    })
                    msg = ifEnabledCheck == true ? '请选择状态等于失效的行来执行生效操作' : msg
                } else if (type === 'delete') {
                    ifDeleteCheck = this.selectsData.some( item => {
                        return item.id
                    })
                    msg = ifDeleteCheck == true ? '已保存的数据不可删除只可生失效' : msg
                }
            }
            if (msg) {
                this.$message.error(msg)
                this.ifOperator = false
                return
            }
            if (type === 'delete') {
                this.selectsData.forEach(item => {
                    self.addList.forEach((good, idx) => {
                        if (good.sku_id === item.sku_id) {
                            self.addList.splice(idx, 1)
                        }
                    })
                    self.list.forEach((good, idx) => {
                        if (good.sku_id === item.sku_id) {
                            self.list.splice(idx, 1)
                        }
                    })
                })
                this.$message.success('删除成功')
                this.ifOperator = false
                return
            }
            this.selectsData.forEach(item => {
                deleteParams.push({sku_id: item.sku_id})
                disabledParams.push({id: item.id,status: 'N', sku_id: item.sku_id})
                enabledParams.push({id: item.id,status: 'Y', sku_id: item.sku_id})
            })
            let url = '/price-web/api/actSpellInstaskill/saveOrUpdate?permissionCode=ACTSPEll_SAVE_UPDATE'
            if (type === 'delete') {
                url = '/price-web/api/actSpellInstaskill/delete?permissionCode=ACTSPEll_TO_DELETE'
            }
            this.ajax.postStream(url, paramsObj[type], function(d){
                self.ifOperator = false
                self.addList = []
                if(d.body.result){
                    self.$message.success( d.body.msg || '')
                }else{
                    self.$message.error(d.body.msg)
                }
                setTimeout( () => {
                   self._getList() 
                }, 2000)
            }, err => {
                self.$message.error(err)
            })
        },
        //新增
        add(){
            let self = this;
            let params = {
                close (d) {
                    let dLen = d.length
                    d.forEach( item => {
                        console.log('新增数据', item)
                        self.addList.push(item)
                        self.list.unshift(
                            {
                                tempId: new Date().getTime(),
                                sku_id: item.sku_id,
                                status: item.status,
                                material_number: item.material_number,
                                goods_name: item.goods_name,
                                spec_name: item.spec_name
                            }
                        )
                    })
                }
            };
            this.$root.eventHandle.$emit('alert',{
                params: params,
                title:'库存sku_id列表',
                style:'width:900px;height:500px',
                component:()=>import('@components/discount/selectSkuIdList'),
            })
        },
        // 监听每页显示数更改事件
        pageChange(pageSize){
            this.serchData.page_size = pageSize
            this._getList();
        },
        // 监听页数更改事件
        currentPageChange(page){
            this.serchData.page_no = page;
            this._getList();
        },
        // 商品是否可选
        selectableFun(row){
            // if(!!row.tempId) return false;
            return true;
        },
        //上传成功返回结果
        uploadSuccess(result) {
            if (result.length > 0 && !!result[0].path) {
                this.importSub(result[0].path);
            }
        },
        //模板下载
        downloadTemplate() {
            window.location.href =
                "http://lsmy-devfile.oss-cn-shenzhen.aliyuncs.com/doc/2021-04-066d078753953e4af5aa0351ece95e0578.xlsx";
        },
        //导入
        importSub(fileUrl) {     
            console.log(fileUrl);
            let params = {
                fileUrl: fileUrl,
            };
            this.ajax.postStream(
                "/price-web/api/actSpellInstaskill/importFileActSpellInstackKill",
                params,
                (res) => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg);
                    } else {
                        this.$message.error(res.body.msg);
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
        //导入结果
        showUploadResult() {
            this.$root.eventHandle.$emit("alert", {
                style: "width:900px;height:600px",
                title: "导入结果",
                params: {
                    url: "/price-web/api/actSpellInstaskill/kill/import/list",
                    data: {},
                    showDownload:false,
                },
                component: () => import("@components/common/eximport"),
            });
        },
    },
    watch: {
        ifOperator(n) {
			this.btns[1].disabled = n
			this.btns[2].disabled = n
			this.btns[3].disabled = n
			this.btns[4].disabled = n
		}
    }
}
</script>