<!-- 更新原因 -->
<template>
	<div>
		<el-row>
			<el-form :model="form" class="mgt30" :rules="rules" ref="form">
				<el-form-item label="原因:" label-width="100px" prop="reason">
					<!-- <el-input v-model="form.reason" size="mini"></el-input> -->
					<xpt-select-aux v-model='form.reason' aux_name='discount_adjust_type'></xpt-select-aux>
					
					<el-tooltip v-if='rules.reason[0].isShow' effect="dark" :content="rules.reason[0].message" placement="right-start" popper-class='xpt-form__error'>
						<i class='el-icon-warning'></i>
					</el-tooltip>
				</el-form-item>
			</el-form>
		</el-row>
		<el-row class="mgt30">
			<el-col :span="24" class="txt-r">
				<el-button class="mgr60" type="primary" size="mini" @click="submit">确定</el-button> 
			</el-col>
		</el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js'
	export default {
		props:["params"],
		data(){
			var self = this;
			return {
				form:{
					reason:null
				},
				rules:{
					reason:validate.isNotBlank({required:true,self:self})
				},
        		reasonOption:__AUX.get('discount_adjust_type'),
			}
		},
		methods:{
			submit(){
				var self = this
				self.$refs.form.validate((valid) => {
					if(!valid) return
					let name = '';
					console.log(self.reasonOption);
					self.reasonOption.map(res=>{
						if(res.code == self.form.reason){
							name = res.name
						}
					})
					self.params.callback&&self.params.callback({code:self.form.reason,name:name});
					self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
				})
			},
			
		},
		
		mounted:function(){
			let self = this;
		}
	}
</script>
<style lang="stylus">
	.txt-r
		text-align:right
	.mgt30
		margin-top:30px
	.mgr60
		margin-right:30px
</style>