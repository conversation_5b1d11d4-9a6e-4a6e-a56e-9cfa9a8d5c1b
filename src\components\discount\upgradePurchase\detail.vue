
<!-- 升级换购活动详情-->
<template>
<div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
		<el-col :span="24">
			<el-button
				:type='btn.type'
				size='mini'
				v-for='(btn, index) in topBtns'
				:key='"good" + index'
				:disabled=" typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled|| false "
				:loading="btn.loading||false"
				@click='btn.click'
			>{{btn.txt}}</el-button>
		</el-col>
	</el-row>
	<div>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基本信息" name="duopai">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="duopai" :rules="rules" ref="duopai">
						<el-col :span='6'>
							<el-form-item label="换购物料：" prop="changeSaleMaterialNo">
									<el-input disabled size='mini' v-if="(/M1/).test(bill_type_id_tag) || duopai.changeSaleId" v-model='duopai.changeSaleMaterialNo'></el-input>
									<el-input readonly size='mini' v-else icon='search' v-model='duopai.changeSaleMaterialNo' :on-icon-click="upgradeMateriaChoose" @change="changeSaleMaterial"></el-input>
									<el-tooltip v-if='rules.changeSaleMaterialNo[0].isShow' class="item" effect="dark" :content="rules.changeSaleMaterialNo[0].message" placement="right-start" popper-class='xpt-form__error'>
											<i class='el-icon-warning'></i>
									</el-tooltip>
							</el-form-item>
							<el-form-item label="换购种类："  >
								<el-input v-model="duopai.changeSaleCategory" prop="changeSaleCategory" disabled  size='mini' ></el-input>
							</el-form-item>
							<el-form-item label="最后修改人："  >
								<el-input v-model="duopai.modifyName" prop="changeSaleCategory" disabled  size='mini' ></el-input>
							</el-form-item>
						</el-col>
						<el-col :span='6'>
							<el-form-item label="物料名称："  >
								<el-input v-model="duopai.changeSaleMaterialName" prop="changeSaleMaterialName" disabled  size='mini' ></el-input>
							</el-form-item>
                            <el-form-item label="创建人"  prop="createName" >
								<el-input v-model="duopai.createName" prop="createName" disabled  size='mini' ></el-input>
							</el-form-item>
							<el-form-item label="最后修改时间：" prop="modifyTime">
								<el-date-picker v-model="duopai.modifyTime" type="datetime" placeholder="选择日期" size='mini' :disabled='true'></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span='6'>
							<el-form-item label="物料规格：" prop="changeSaleMaterialSpec" >
           						<xpt-input size='mini' v-model="duopai.changeSaleMaterialSpec" disabled></xpt-input>
          					</el-form-item>
							<el-form-item label="创建时间：" prop="createTime">
								<el-date-picker v-model="duopai.createTime" type="datetime" placeholder="选择日期" size='mini' :disabled='true' :editable='false'></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span='6'>
							<el-form-item label="状态：">
								<!-- <xpt-input size='mini' model="duopai.status==true?'生效':'失效'" disabled>	{{duopai.status==true?'生效':'失效'}}</xpt-input> -->
								<el-select
									v-model="duopai.status"
									size="mini"
									style="width:150px;"
									:disabled="true"
								>
								<el-option label="生效" :value="true" ></el-option>
								<el-option label="失效" :value="false" ></el-option>
								</el-select>
							</el-form-item>
						</el-col>
			    	</el-form>
			 	 </el-tab-pane>
			</el-tabs>
        </el-row>
    </div>
    <el-row class="xpt-flex__bottom" v-fold>
        <el-tabs v-model="secondTab">
            <el-tab-pane label="换购清单" name="duopaiInfo" class='xpt-flex'>
                <xpt-list
                    ref='upgradeMateriaList'
                    id='upgradeMateriaList'
                    :data='materiaListInfoData.materiaListData'
                    :colData='materiaListInfoData.materiaListCols'
                    :btns='materiaListInfoData.materiaListBtns'
                    :orderNo= true
                    isNeedClickEvent
                    :selectable='selectableFun'
                    :pageTotal="materiaListInfoData.materiaListCount"
                    @selection-change='langeRadioChange'
                    :searchPage='materiaListQuery.page_name'
                    @search-click='serchMaterial'
                    @page-size-change='materiaListPageSizeChange'
                    @current-page-change='materiaListPageChange'
                >
								
            			<xpt-import slot='btns' :taskUrl="uploadUrl" :isupload="!!duopai.status" :otherParams='otherParams' :callback="uploadCallback" class='mgl10'></xpt-import>
            			<el-button slot='btns'  type="info" @click="downloadExcel('EXCEL_TYPE_CHANGE_DETAIL_IMPORT')" size="mini">查看导入结果</el-button>
								</xpt-list>
            </el-tab-pane>
						<el-tab-pane label="实施店铺" name="shop" class='xpt-flex'>
                <xpt-list
                    ref='shopList'
                    :data='shopList'
                    :colData='shopCol'
                    :btns='shopBtns'
                    isNeedClickEvent
                    :pageTotal="shopCount"
                    @selection-change='shopChange'
                    :searchPage='shopSearchObj.page_name'
                    @search-click='searchShop'
                    @page-size-change='shopListPageSizeChange'
                    @current-page-change='shopListPageChange'
                >
            			<xpt-import slot='btns' :taskUrl="uploadShopUrl" :isupload="duopai.status" :otherParams='otherParams' :callback="uploadCallback" class='mgl10'></xpt-import>
            			<el-button slot='btns' type="info" @click="downloadExcel('EXCEL_TYPE_CHANGE_SHOP_IMPORT')" size='mini' >查看导入结果</el-button>
								</xpt-list>
            </el-tab-pane>
        </el-tabs>
	</el-row>
</div>
</template>
<script>
	import validate from '@common/validate.js';
	export default {
		props: ['params'], //上游参数
		data() {
			var self = this;//本vue
			return{
				firstTab:"duopai",
				secondTab: 'duopaiInfo',
				uploadUrl: '/material-web/api/change_sale/excel/importDetail',
				uploadShopUrl: '/material-web/api/change_sale/excel/importShop',
				otherParams: {
					ifAppointment:true
				},
				bill_type_id_tag: '',
				duopai:{
					changeSaleId: '',
					changeSaleMaterialNo: '',
					changeSaleMaterialName: '',
					changeSaleMaterialSpec: '',
					changeSaleCategory: '',
				},//表单内容
				rules:{
					changeSaleMaterialNo: validate.isNotBlank({
						required:true,
                        self:self,
                        trigger: 'change'
					})
				},
				shopCount:0,
				shopSearchObj:{
					page_no:1,
					page_size: self.pageSize,
					page_name: 'scm_change_sale_shop',
					if_need_page:'Y',
					where: []
				},
				topBtns: [
					{
						type: 'primary',
						txt: '刷新',
						disabled () {
                            return self.params.id ? false : true
                        },
						click () {
                            self.init()
                        }
					}, {
						type: 'primary',
						txt: '新增',
						disabled: false,
						click: self.addNew
					}, {
						type: 'success',
						txt: '保存',
						disabled () {
								return false
						},
						click () {
							self.save()
						}
					}
					, {
						type: 'warning',
						txt: '生效',
						disabled () {
								return false
						},
						click () {
							self.changeStatus(true)
						}
					}
					, {
						type: 'danger',
						txt: '失效',
						disabled () {
								return false
						},
						click () {
							self.changeStatus(false)
						}
					}
				],

				materiaListInfoData: {
					materiaListData: [],
					materiaListCols: [
						{
							label: '物料编码',
							prop: 'materialNo',
							width: 150,
							bool: true,
							iconClick(row) {
								setTimeout(self.materiaChoose, 10)
							},

							disabled(row) {
								return row.changeSaleDetailId ? true : false
							},
							blur: self.materiaCopy
						}, {
							label: '物料名称',
							prop: 'materialName'
						}, {
							label: '物料描述',
							prop: 'materialSpec',
							width: 150
						}, {
							label: '换购价格',
							prop: 'changeSalePrice',
							elInput:true,
							bool: true,
							disabled(row) {
									return row.changeSaleDetailId ? true : false
							},
							change(row){
									self.checkChangePrice(row.changeSalePrice)
							},
							width: 150
						}, {
							label: '生效日期',
							prop: 'enableTime',
							width: 150,
							bool: true,
							date: 'date',
              elDate2: true,
              pickerOptions:row =>{
                return{
                  disabledDate(time) {
                    if (row.changeSaleDetailId) {
                      return time.getTime() > new Date(row.enableTime).getTime()
                    }
                  }
                }
              },
              disabled(row) {
							},
							change(row) {},
						}, {
							label: '失效日期',
							prop: 'disableTime',
							width: 150,
							bool: true,
							date: 'date',
							elDate2: true,
							pickerOptions:row => {
                return {
                    disabledDate(time) {
                        if (row.changeSaleDetailId) {
                          return time.getTime() < new Date(row.disableTime).getTime()
                        }
                    }
                }
							},
              disabled(row) {
							},
							change(row) {},
						}, {
							label: '行状态',
							prop: 'status',
							formatter (val) {
								switch (val) {
									case 'DISABLED': return "失效" ; break;
									case 'ENABLED': return "生效"; break;
								}
							}
						}, {
							label: '备注',
							prop: 'remark',
							elInput:true,
							bool: true,
							disabled(row) {
									return false
							},
							width: 100
						}, {
							label: '更新人',
							prop: 'modifyName'
						}, {
							label: '更新时间',
							prop: 'modifyTime',
							format: 'dataFormat1',
							width: 130
						}
							],
							materiaListCount: 0,
							materiaListBtns: [
									{
											type: 'primary',
											txt: '增行',
											disabled(){
													return self.duopai.status
													return false
											},
											click: self.materiasAdd
									}, {
											type: 'danger',
											txt: '删行',
											disabled() { // 未审核的单据才可执行整单删除
													return self.duopai.status
													return false
											},
											click: self.deleteMaterial
									}, {
											type: 'warning',
											txt: '生效',
											disabled() {
													return self.duopai.status
													return false
											},
											click () {
													self.setProtectDisabe('ENABLED')
											}
									}, {
											type: 'warning',
											txt: '失效',
											disabled() { //当表头生效状态为失效的时候按钮置灰
													return self.duopai.status
													return false
											},
											click () {
													self.setProtectDisabe('DISABLED')
											}
									}
							]
				},
				shopBtns: [
						{
								type: 'primary',
								txt: '新增店铺',
								disabled(){
										return self.duopai.status
								},
								click: self.shopAdd
						}, {
								type: 'danger',
								txt: '删除',
								disabled() { // 未审核的单据才可执行整单删除
										return self.duopai.status
										return false
								},
								click: self.deleteShop
						},  {
								type: 'warning',
								txt: '失效',
								disabled() { //当表头生效状态为失效的时候按钮置灰
										return self.duopai.status
										return false
								},
								click () {
										self.setShopDisabe('DISABLED')
								}
						}
				],
				shopList:[],
				shopCol:[{
							label: '店铺编码',
							prop: 'shop_code'
						}, {
							label: '店铺名称',
							prop: 'shop_name'
						}, {
							label: '创建人',
							prop: 'create_name'
						}, {
							label: '创建时间',
							format: 'dataFormat1',
							prop: 'create_time'
						}, {
							label: '状态',
							prop: 'status',
							formatter(val){
								return val?'生效':'失效';
							}
						}, {
							label: '失效人',
							prop: 'disable_name'
						}, {
							label: '失效时间',
							format: 'dataFormat1',
							prop: 'disable_time'
						},],
				materiaListOld: [], // 旧的数据做对比
				materiaSelect: [],
				shopSelect:[],
				otherParams: {
					upgradePurchase: true,
					changeSaleId:self.params.id
				},
				materiaListQuery: {
						where: [],
						page_size: 50,
						page_no: 1,
						if_need_page:'Y',
						page_name: 'scm_change_sale_detail'
				}
			}
		},
		methods:{
			uploadCallback (data){
					if (data.body && data.body.result) {
							this.init()
					}
			},
			// 查看导入结果
			downloadExcel(excel_type) {
					this.$root.eventHandle.$emit('alert', {
							params: {
								excel_type:excel_type
							},
							component: () => import("@components/discount/upgradePurchase/import_result.vue"),
							style: 'width:800px;height:400px',
							title: '导入结果'
					});
			},
			// 添加店铺
			shopAdd () {
					let params = {};
					let self = this;
					// 选择店铺,可以多选
					params.callback = d => {
							let dataSet = new Set(),
									hasDuplicate = [];
							self.shopList.find(list => {
									dataSet.add(list.shop_id)
							})
							console.log(dataSet);
							d.find(l => {
									if (!dataSet.has(l.shop_id) && l.shop_status != 'CLOSED') {
											l.status = 0
											l.shop_status = 'N'//新增默认店铺状态为未审核
											self.shopList.push({
													shop_code: l.shop_code,
													shop_name: l.shop_name,
											})
									} else {
											hasDuplicate.push(l.shop_name)
									}
							})
							if (hasDuplicate.length) {
									let msg = hasDuplicate.join('、') + '店铺已添加或者已关闭';
									if (hasDuplicate.length > 3) {
											let len = hasDuplicate.length;
											hasDuplicate.length = 3;
											msg = hasDuplicate.join('、') + '等' + len + '个店铺已添加或者已关闭';
									}
									self.$message.info(msg);
							}
					};
					this.$root.eventHandle.$emit('alert', {
							params: params,
							component: () => import('@components/shop/list'),
							style: 'width:800px;height:500px',
							title: '店铺列表'
					});
			},
			checkChangePrice(num) {
				var reg = /^([1-9][0-9]*)$/
				if (!num) {
						this.$message.error('请输入换购价格');
						return false
				}
				if(!reg.test(num)){
						this.$message.error('当前输入不符合规范，请输入正整数')
				}
				return reg.test(num)
			},
			changeStatus(status){
				let self = this;
				let url = status?'/material-web/api/change_sale/update/head/enable':'/material-web/api/change_sale/update/head/disable'
				this.$confirm(
					"是否继续？",
					"提示",
					{
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "danger",
					}
				).then(() => {
					
					self.ajax.postStream(url,{
							"changeSaleId": self.duopai.changeSaleId,
						},function(response){
						if(response.body.result) {
							self.$message.success(response.body.msg)
							self.init();
						}else{
							self.$message.error(response.body.msg)
						}
					},err => {
							self.$message.error(err)
					});
				}).catch(() => {
					return false;
				});
				
			},
			save(formName){
				var self =  this;
				let saveData = JSON.parse(JSON.stringify(self.duopai));
				let materiaSaveData = this.materiaSaveData(), ifRequired = this.checkGoodListRequired()
				if (!ifRequired) {
					return
				}
				let data = {
					changeSaleMaterialNo: saveData.changeSaleMaterialNo,
					changeSaleMaterialName: saveData.changeSaleMaterialName,
					changeSaleMaterialSpec: saveData.changeSaleMaterialSpec,
					changeSaleCategory: saveData.changeSaleCategory,
					details: materiaSaveData.all,
					shops:self.shopList.map(item=>{
						if(!item.change_sale_shop_id){
							return {
								shop_code:item.shop_code,
								shop_name:item.shop_name,
							}
						}
					}).filter(item=>{return !!item})
				}
				this.duopai.changeSaleId ? data.changeSaleId = this.duopai.changeSaleId : ''
				// if (this.materiaListInfoData.materiaListData && (this.materiaListInfoData.materiaListData.length <= 0)) {
				// 	this.$message.error('请至少添加一行换购明细')
				// 	return
				// }
				self.$refs.duopai.validate((valid) => {
					if(!valid) return
					this.ajax.postStream('/material-web/api/change_sale/save',data,function(response){
						if(response.body.result) {
                            self.$message.success(response.body.msg)
                            if (self.duopai.changeSaleId) {
                                self.init();
                            }
                            self.duopai.changeSaleId = response.body.content.changeSaleId
                            let params = {
                                id:response.body.content.changeSaleId
                            }
                            params.__close = function(){
                                self.$root.eventHandle.$emit('removeTab',self.params.tabName);
                            }
                            self.$root.eventHandle.$emit('updateTab',{
                                name:self.params.tabName,
                                params:params,
                                title:'升级换购活动详情',
                                component: () => import('@components/discount/upgradePurchase/detail.vue')
                            })
						} else {
							self.$message.error(response.body.msg || '')
						}
					},err => {
							self.$message.error(err)
					});

				})

			},
			// 获取商品信息数据变更
			materiaSaveData() {
				let materiaOld = this.materiaListOld,
					materiaNow = this.materiaListInfoData.materiaListData,
					materiaAdd = [],
					materiaUpd = [],
					i;
				// 获取新增的和修改的物料
				i = materiaNow.length;

				while(i--) {
					if(materiaNow[i].changeSaleDetailId) { // 保存后修改数据
						// 获取更新行数据，status过滤掉取消状态的数据,换货和取消的
						let j = materiaOld.length;
						while(j--) {
							if(materiaNow[i].changeSaleDetailId === materiaOld[j].changeSaleDetailId) {
								let newObj = {
									material_id: materiaNow[i].material_id,
									enableTime: materiaNow[i].enableTime,
									disableTime: materiaNow[i].disableTime,
									materialNo: materiaNow[i].materialNo,
                                    status: materiaNow[i].status,
                                    changeSalePrice: materiaNow[i].changeSalePrice,
                                    remark: materiaNow[i].remark,
								}
								let oldObj = {
									material_id: materiaOld[j].material_id,
									enableTime: materiaOld[j].enableTime,
									disableTime: materiaOld[j].disableTime,
									materialNo: materiaOld[j].materialNo,
									status: materiaOld[j].status,
									changeSalePrice: materiaOld[j].changeSalePrice,
									remark: materiaOld[j].remark,
								}
								let isUpd = this.compareData(oldObj, newObj)
								if(isUpd) {
									materiaAdd.push({
										changeSaleDetailId: materiaNow[i].changeSaleDetailId,
										// material_id: materiaNow[i].material_id,
										enableTime: materiaNow[i].enableTime,
										disableTime: materiaNow[i].disableTime,
										materialNo: materiaNow[i].materialNo,
                                        status: materiaNow[i].status,
                                        changeSalePrice: materiaNow[i].changeSalePrice,
                                        remark: materiaNow[i].remark,
                                        materialSpec: materiaNow[i].materialSpec,
                                        materialName: materiaNow[i].materialName,
									})
								}
							}
						}
					}
					// 需要过滤掉没有物料信息的数据
					if( materiaNow[i].tempId && materiaNow[i].material_id) { // 新增数据
						let addGoods = {
							enableTime: materiaNow[i].enableTime === '' ? '' : new Date(materiaNow[i].enableTime),
							disableTime: materiaNow[i].disableTime,
                            materialNo: materiaNow[i].materialNo,
                            status: materiaNow[i].status,
                            changeSalePrice: materiaNow[i].changeSalePrice,
                            materialSpec: materiaNow[i].materialSpec,
                            materialName: materiaNow[i].materialName,
                            remark: materiaNow[i].remark,
						}
						materiaAdd.unshift(addGoods)
					}
                }
                let materiaAll = [...materiaAdd, ...materiaUpd]
				return {
					add: materiaAdd,
                    upd: materiaUpd,
                    all: materiaAll
				}
			},
			//获取详情信息
			getInfo(resolve){
				let id = this.params.id ? this.params.id : this.duopai.changeSaleId, self = this
				if(id){
					this.ajax.postStream('/material-web/api/change_sale/find',id,function(response){
						if(response.body.result && response.body.content) {
                            self.duopai = response.body.content || {};
							resolve && resolve();
						} else {
							self.$message.error(response.body.msg || '');
						}
					});
				}
			},
			shopListPageSizeChange (size,reslove) {
					this.shopSelect = []
					this.shopSearchObj.page_size = size
					this.getShopList(reslove)
					console.log(size)
			},
			shopListPageChange(page,reslove){
					this.shopSelect = []
					console.log(page)
					this.shopSearchObj.page_no = page
					this.getShopList(reslove)
			},
			materiaListPageSizeChange (size,reslove) {
					this.materiaSelect = []
					this.materiaListQuery.page_size = size
					this.getMaterialList(reslove)
			},
			serchMaterial(where,reslove) {
					this.materiaListQuery.where = where
					this.getMaterialList(reslove)
			},
			searchShop(where,reslove) {
					this.shopSearchObj.where = where
					this.getShopList(reslove)
			},
			getShopList(resolve) {
					let id = this.params.id ? this.params.id : this.duopai.changeSaleId, self = this
				if(id){
					let params = Object.assign({}, self.shopSearchObj)
					params.changeSaleId = id
					this.ajax.postStream('/material-web/api/change_sale/getShopList', params,function(response){
						if(response.body.result && response.body.content) {
							let list = response.body.content.list || []
							self.shopList = list
							self.shopCount = response.body.content.count;
						} else {
							self.$message.error(response.body.msg || '');
						}
						resolve&&resolve()
					}, err => {
							self.$message.error(err);
							resolve&&resolve()
					});
				} else{
						resolve&&resolve()
				}
			},
			materiaListPageChange(page,reslove){
					this.materiaSelect = []
					this.materiaListQuery.page_no = page
					this.getMaterialList(reslove)
			},
			getMaterialList(resolve) {
					let id = this.params.id ? this.params.id : this.duopai.changeSaleId, self = this
				if(id){
										let params = Object.assign({}, self.materiaListQuery)
										params.changeSaleId = id
					this.ajax.postStream('/material-web/api/change_sale/detail/list', params,function(response){
						if(response.body.result && response.body.content) {
														let list = response.body.content.result.list || []
														self.materiaListInfoData.materiaListData = list
														self.materiaListInfoData.materiaListCount = response.body.content.result.count
														self.materiaListOld = JSON.parse(JSON.stringify(self.materiaListInfoData.materiaListData))
														// self.$message.success(response.body.msg)
						} else {
							self.$message.error(response.body.msg || '');
												}
												resolve&&resolve()
					}, err => {
												self.$message.error(err);
												resolve&&resolve()
										});
				} else{
										resolve&&resolve()
								}
			},
			addNew () {
				this.$root.eventHandle.$emit('creatTab', {name:'新增升级换购', component:()=>import('@components/discount/upgradePurchase/detail.vue')})
			},
			shopChange(data) {
				this.shopSelect = data
			},
			// 选中的物料
			langeRadioChange (data) {
				this.materiaSelect = data
			},
            materiasAdd () { // 添加天猫物料
                new Promise((resolve) => {
                        setTimeout(resolve, 10)
                }).then(() => {
                    let params = {
                        ifAlert: true,
                        selection: 'checkbox',
                        ifUpgradePurchase: true,
                        otherParams: {
                            isStop: '0',
                            invalid_sale: '0'
                        }
                    }, self = this;
                    params.callback = t => {
                        if(t) {
                            /*if (t.length && t.length > 0) {
                                t.forEach(d => {
                                    let obj = {
                                        tempId: new Date(),
                                        materialNo: d.materialNumber,
                                        material_id: d.materialId,
                                        materialSpec: d.materialSpecification,
                                        materialName: d.materialName,
                                        enableTime: '',
                                        disableTime: '',
                                        price: '',
                                        status: 'ENABLED'
                                    }
                                    self.materiaListInfoData.materiaListData.unshift(obj)
                                })
                            } else {*/
                                let obj = {
                                    tempId: new Date(),
                                    materialNo: t.materialNumber,
                                    material_id: t.materialId,
                                    materialSpec: t.materialSpecification,
                                    materialName: t.materialName,
                                    enableTime: '',
                                    disableTime: '',
                                    price: '',
                                    status: 'ENABLED'
                                }
                                self.materiaListInfoData.materiaListData.unshift(obj)
                            // }
                        }
                        // 滚动到最底部
                        let scrollObj = document.querySelector('#upgradeMateriaList').querySelector('.el-table__body-wrapper')
                        this.$nextTick(function() {
                            scrollObj.scrollTop = 0
                        })
                    }
                    self.$root.eventHandle.$emit('alert', {
                        params: params,
                        component: () => import('@components/order/selectGoodsList'),
                        style: 'width:800px;height:500px',
                        title: '物料列表'
                    });
                })
            },
			/*
			选择商品物料
			*/
			upgradeMateriaChoose() {
				let params = {
                    ifUpgradePurchase: true,
                    otherParams: {
                        isStop: '0',
                        invalid_sale: '0'
                    }
                }, self = this
				params.callback = d => {
					if(d) {
						self.duopai.changeSaleMaterialNo = d.materialNumber;
						self.duopai.changeSaleCategory = d.itemCatlogType;
						self.duopai.changeSaleMaterialName = d.materialName;
						self.duopai.changeSaleMaterialSpec = d.materialSpecification;
					}
				}
				self.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/order/selectGoodsList'),
					style: 'width:800px;height:500px',
					title: '商品列表'
				})
            },
            changeSaleMaterial() {
                this.duopai.changeSaleMaterialNo = ''
                this.duopai.changeSaleCategory = ''
                this.duopai.changeSaleMaterialName = ''
                this.duopai.changeSaleMaterialSpec = ''
            },
            materiaChoose() {
				let params = {
                    ifUpgradePurchase: true,
                    otherParams: {
                        isStop: '0',
                        invalid_sale: '0'
                    }
                }, self = this
				params.callback = d => {
					if(d) {
                        let len = self.materiaSelect.length
						self.materiaSelect[len-1].materialNo = d.materialNumber;
						self.materiaSelect[len-1].materialSpec = d.materialSpecification;
                        self.materiaSelect[len-1].materialName = d.materialName;
                        self.materiaSelect[len-1].material_id= d.materialId
					}
				}
				self.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/order/selectGoodsList'),
					style: 'width:800px;height:500px',
					title: '商品列表'
				})
			},
			materiaCopy(row) {
				let self = this;
				let num = row.materialNo;
				if(!num.trim()) return;
				this.checkMaterialNumber({materialNumber:num.trim()},function(){
					// 普通订单
					// alert(1)
					self.ajax.get('/material-web/api/material/getTradeMaterialByNumber/' + num.trim(), res => {
						if(res.body.result) {
							let data = res.body.content
							if(data) {
								// 取财务中台物料ID
								row.materialNo = data.materialNumber
								row.material_id = data.sourceMaterialId
								row.materialSpec = data.materialSpecification
								row.materialName = data.materialName
								return
							}
							self.$message.error('没有查询到该物料信息')
							row.materialNo = ''
							row.material_id = ''
							row.materialSpec = ''
							row.materialName = ''
						} else {
							self.$message.error(res.body.msg);
							row.materialNo = ''
							row.material_id = ''
							row.materialSpec = ''
							row.material_name = ''
						}
					})
				})
			},
			checkMaterialNumber(data,callback){
				let self = this;
				this.ajax.postStream('/order-web/api/mergetrade/checkMaterialStatus', [data.materialNo], d => {
					if(d.body.result) {
						callback&&callback(d.body.result);
					} else {
						self.init()
						this.$message.error(d.body.msg);
					}
				}, err => {
					self.init()
				})
			},
			// 物料是否可选
			selectableFun(row){
				// if(row.status === 'DISABLED') return false;
				return true;
			},
			// 删除多拍物料
			deleteMaterial () {
                let idList = [], self = this, ifAdd = null
                if(this.materiaSelect.length == 0) {
                    this.$message.error('请选择需要删除的数据')
                    return
                }
				ifAdd = this.materiaSelect.some(materia => {
					return materia.changeSaleDetailId
                })
                if (ifAdd) {
                    this.$message.error('可删除空行，未生效行。已生效/失效行不可删除')
                    return
                }
				this.materiaSelect.forEach( (good, idx) => {
					if (good.changeSaleDetailId) {
						idList.push(good.changeSaleDetailId)
					} else if (good.tempId){
                        let list = self.materiaListInfoData.materiaListData
                        let len = list.length
                        while(len--) {
                            if (list[len].materialNo === good.materialNo && list[len].tempId === good.tempId) {
                                self.materiaListInfoData.materiaListData.splice(len, 1)
                            }
                        }
					}
				})
			},
			// 删除多拍店铺
			deleteShop () {
				let idList = [], self = this, ifAdd = null
				if(this.shopSelect.length == 0) {
						this.$message.error('请选择需要删除的数据')
						return
				}
				ifAdd = this.shopSelect.some(item => {
					return item.change_sale_shop_id
				})
				if (ifAdd) {
						this.$message.error('可删除空行，未生效行。已生效/失效行不可删除')
						return
				}
				this.shopSelect.forEach( (item, idx) => {
				
				let list = self.shopList
				let len = list.length
				while(len--) {
						if (list[len].shop_code === item.shop_code) {
								self.shopList.splice(len, 1)
						}
				}
				})
			},
			// 校验商品行是否都填写
			checkGoodListRequired (saveCb) {
				let msg = '', idsList = [], reg = /^([1-9][0-9]*)$/
				this.materiaListOld.forEach( item => {
					idsList.push(item.sku_id)
				})
				this.materiaListInfoData.materiaListData.some((obj, index) => {
					if(!obj.materialNo){
						msg = '物料编码不能为空'
					}else if(!obj.enableTime){
						msg = '物料的生效日期不能为空'
					}else if(!obj.disableTime){
						msg = '物料的失效日期不能为空'
					}else if (obj.enableTime >= obj.disableTime) {
						msg = '物料的生效时间不能大于等于失效时间'
					}else if(!obj.changeSalePrice){
						msg = '换购价格不能为空'
					}else if (!reg.test(obj.changeSalePrice)) {
                        msg = '换购价格输入不合规范，请输入正整数'
                    }
                    if (obj.tempId && obj.sku_id) {
                       idsList.push(obj.sku_id)
                    }

					if(msg) {
						msg = '换购清单-第' + (index + 1) + '行-' + msg
						return true
					}
				})
				if(msg){
					this.$message.error(msg)
					// throw new Error(msg);
					return false
				} else {
					return true
				}
			},
			setShopDisabe(){
				if(this.shopSelect.length == 0) {
						this.$message.error('请选择需要操作的数据')
						return
				}
				let self = this, remark_value = this.shopSelect[0].status
				let checkMsg = '';
				// let ifNewAdd = !self.shopSelect.every(item => {
				// 		return item.change_sale_shop_id
				// })
				// if (ifNewAdd) {
				// 	checkMsg = '列表选中存在新增数据，请操作保存'
				// 	this.$message.error(checkMsg)
				// 	return
				// } 
				let ifDisabled = self.shopSelect.some(item => {
						console.log(item);
						return !item.status
				})
				if (ifDisabled) {
					checkMsg = '列表选中存在失效数据，请操作重新选择'
					this.$message.error(checkMsg)
					return
				} 
				let params = []
				this.shopSelect.forEach((item, idx) => {
					if(!!item.change_sale_shop_id){
						params.push(item.change_sale_shop_id)
					}
				})
				let url = '/material-web/api/change_sale/update/shop/disable'
				this.ajax.postStream(url, params, d => {
						if (d.body.result) {
								self.$message.success(d.body.msg)
								self.init()
						} else {
								self.$message.error(d.body.msg)
						}
				}, (e) => {
						this.$message.error(e);
				})
			},
			// 保护清单生失效
			setProtectDisabe (status) {
                if(this.materiaSelect.length == 0) {
                    this.$message.error('请选择需要操作的数据')
                    return
                }
                let self = this, remark_value = this.materiaSelect[0].status
                let ifChangeSign = this.materiaSelect.every(item => {
                    return item.status === remark_value
                }), checkMsg = ''
                let ifNewAdd = self.materiaSelect.some(item => {
                    return item.tempId
                })
                if (ifNewAdd) {
                    checkMsg = '列表存在新增数据，请操作保存'
                } else if (!ifChangeSign) {
                    checkMsg = '请选择行状态一致的数据进行批量操作'
                } else if ((status =='DISABLED' && this.materiaSelect[0].status == 'DISABLED' && ifChangeSign) || (ifChangeSign && status =='ENABLED' && this.materiaSelect[0].status == 'ENABLED')) {
                    let key = status == 'ENABLED'? '已生效的' : '已失效的'
                    checkMsg = key + '不可以重复操作'
                }
                if (checkMsg) {
                    this.$message.error(checkMsg)
                    return
                }
                let params = []
                this.materiaSelect.forEach((item, idx) => {
                    params.push(item.changeSaleDetailId)
                })
                let url = status == 'ENABLED' ? '/material-web/api/change_sale/update/enable' : '/material-web/api/change_sale/update/disable'
                this.ajax.postStream(url, params, d => {
                    if (d.body.result) {
                        self.$message.success(d.body.msg)
                        self.init()
                    } else {
                        self.$message.error(d.body.msg)
                    }
                }, (e) => {
                    this.$message.error(e);
                })
            },
            init(){
                this.getInfo();
                this.getMaterialList()
								this.getShopList();
            }
		},
		mounted: function(){
            this.init()
        },
        watch: {}
	}
</script>
<style module>
.row-height :global(.el-form-item__content) {
	height: auto!important;
	white-space: nowrap;
}
</style>
