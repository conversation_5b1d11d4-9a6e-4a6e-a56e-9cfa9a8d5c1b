<!-- 升级换购--导入结果 -->
<template>
	<xpt-list 
		:data='list' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='count' 
		selection='' 
		@page-size-change='pageSizeChange' 
		@current-page-change='currentPageChange'
	>
		<template  slot='file_path' slot-scope='scope'>
						<a :href="scope.row.file_path" target="_blank" :download="scope.row.file_path">下载</a>
		</template>
	</xpt-list>
</template>
<script>
export default {
	props: ['params'],
	data() {
		let self = this
		return {
			search: {
				// excel_type: /价目导入结果/.test($('.el-tabs__item.is-active').text()) ? 'EXCEL_TYPE_PRICELIST_IMPORT' : 'EXCEL_TYPE_ACTDISCOUNT_IMPORT',
				page_no: 1,
				page_size: 20
			},
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: self.getData
			}],
			cols: [
				{
					label: '程序名称', 
					prop: 'program_name'
				}, {
					label: '参数', 
					prop: 'parameter'
				}, {
					label: '运行状态', 
					prop: 'run_status',
					formatter: self.statusFilter
				}, {
					label: '程序状态', 
					prop: 'program_status',
					formatter: self.statusFilter
				}, {
					label: '提交时间', 
					prop: 'submit_time',
					format: 'dataFormat1'
				}, {
					label: '完成时间', 
					prop: 'finish_time',
					format: 'dataFormat1'
				}, {
					label: '请求日志', 
					prop: 'text'
				}, {
					label: '结果',
					slot: 'file_path'
				}
			],
			count: 0,
			list: []
		}
	},
	methods: {
		getData() {
			this.search.excel_type = this.params.excel_type;
			this.ajax.postStream('/material-web/api/change_sale/excel/importResult', this.search, res => {
				let data = res.body.content
				if(res.body.result) {
					this.list = data ? data.list : []
					this.count = data ? data.count : 0
				} else {
					this.$message({
						type: 'error',
						message: res.body.msg
					})
				}
			})
		},
		pageSizeChange(pageSize) {
			this.search.page_size = pageSize
			this.getData()
		},
		currentPageChange(page) {
			this.search.page_no = page
			this.getData()
		},
		statusFilter(val) {
			switch(val) {
				case 'EXEC_FAILED': return '失败';
				case 'EXEC_SUCCESS': return '成功';
				case 'EXEC_WAIT': return '等待';
				case 'EXEC_RUNING': return '执行中';
				default: return val;
			}
		}
	},
	mounted() {
		this.getData()
	}
}	
</script>
