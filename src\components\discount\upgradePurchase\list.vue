<!-- 升级换购商品列表 -->   
<template>
	<div class='xpt-flex'>
		<xpt-list
			:data='tableData'
			:btns='btns'
			:colData='cols'
			:pageTotal='totalPage'
			selection=''
			:searchPage="searchObj.page_name"
  	 		 @search-click="preSearch"
			:orderNo='true'
   	 		@page-size-change='handleSizeChange'
   	 		@current-page-change='handleCurrentChange' >
            <!-- <xpt-import slot='btns' :taskUrl="uploadUrl" :otherParams='otherParams' :callback="uploadCallback" class='mgl10' :isupload="false"></xpt-import> -->
		</xpt-list>
	</div>
</template>
<script>
export default {
	data(){
		var self = this;
		return{
			btns: [
				{
					type: 'success',
					txt: '刷新',
					click: self.searchFun
				}, {
					type: 'primary',
					txt: '新增',
					click: self.addFun
                },
					// 			 {
					// type: 'info',
					// txt: '导入结果查询',
					// click: self.downloadExcel
          //       }
			],
			ifClickUpload: false,
			uploadData: {},
			cols: [
				{
					label: '换购物料',
					prop: 'changeSaleMaterialNo',
					redirectClick(row) {
						 let params = {};
          				 params.id = row.changeSaleId;
            			 self.$root.eventHandle.$emit('creatTab', {
              			 	name: "升级换购活动详情",
             			 	params: params,
             				 component: () => import('@components/discount/upgradePurchase/detail.vue')
         			   });
					},
					width: 135
				}, {
					label: '物料名称',
					prop: 'changeSaleMaterialName',
				}, {
					label: '物料规格',
					prop: 'changeSaleMaterialSpec',
				}, {
					label: '换购种类',
					prop: 'changeSaleCategory',
				}, {
					label: '创建人',
					prop: 'createName',
				}, {
					label: '创建时间',
					prop: 'createTime',
					format: 'dataFormat1',
				}
				, {
					label: '状态',
					prop: 'status',
					formatter(val){
						return val?'生效':'失效';
					}
				}
			],
			searchObj:{
				page_no:1,
				page_size: self.pageSize,
				page_name: 'scm_change_sale',
				where: []
			},
			totalPage:0,
            tableData:[],
            otherParams: {
                ifAppointment:true
                // ifDuopai: true
            },
            uploadUrl: '/material-web/api/change_sale/exel/save'
		}
	},
	props:['params'],
	mounted(){
  	 	this.searchFun();
  	},
	methods:{ 
		addFun(){
			this.$root.eventHandle.$emit('creatTab', {name:'新增升级换购', component:()=>import('@components/discount/upgradePurchase/detail.vue')});
		},
		preSearch(txt, resolve){
			this.searchObj.where = txt
			this.searchFun(null, resolve);
		},
		initSearchData(){
			if(!this.searchKey) this.searchObj.act_name = ''
			else this.searchObj.act_name = this.searchKey
		},
		searchFun(event, resolve){
			let self = this;
    	    this.ajax.postStream('/material-web/api/change_sale/list',this.searchObj, (res) => {
        	if(res.body.result){
         	 	self.tableData = res.body.content.list;
          		self.totalPage = res.body.content.count;
          		this.$message.success(res.body.msg)
       		 }else{
          			self.$message.error(res.body.msg || '');
       		 }
       		 if (resolve) {
         		 resolve();
        	 }
     		 }, (err) => {
       		 self.$message.error(err);
       		 if (resolve) {
         		 resolve();
      		  }
     	 });
		},
		handleSelectionChange(selectArr){
			var self = this
			self.selectId=[]
			selectArr.map((v)=>{
				self.selectId.push(v.act_id)
			});
		},
		handleSizeChange(val){
			this.searchObj.page_size = val;
			// this.initSearchData()
			this.searchFun();
		},
		handleCurrentChange(val){
			this.searchObj.page_no = val;
			// this.initSearchData()
			this.searchFun();
		},
		uploadCallback (data){
				if (data.body && data.body.result) {
						this.searchFun()
				}
		},
		// 查看导入结果
		downloadExcel() {
				this.$root.eventHandle.$emit('alert', {
						params: {},
						component: () => import("@components/discount/upgradePurchase/import_result.vue"),
						style: 'width:800px;height:400px',
						title: '导入结果'
				});
		},
		
	}
}
</script>
