<!-- 优惠信息查看，用于合并订单添加优惠列表页面查看优惠详情 -->
<template>
	<div class='xpt-flex'>
		<div>
			<el-tabs v-model='firstTab'>
				<el-tab-pane label='基础信息' name='first' class='xpt-flex'>
					<xpt-form :data='form' :cols='firstCols' label='100px'></xpt-form>
				</el-tab-pane>
				<el-tab-pane label='优惠条件' name='second' class='xpt-flex discount_table'>
                    <xpt-list
						ref='discount_type'
						:data='conditionVo'
						:colData='secondCols'
						:showHead='false'
						@cell-click='conditionCellClick'
						:taggelClassName='tableRowClassName'
						selection=''
					></xpt-list>
				</el-tab-pane>
				<el-tab-pane label='优惠清单' name='three' class='xpt-flex discount_table'>
                    <xpt-list
						ref='discount_list'
						:data='discountMaterialList'
						:colData='threeCols'
						:showHead='false'
						selection=''
						:pageTotal='discountMaterialCount'
						:pageLength='materialSearchData.pageSize'
						@page-size-change='discountMaterialSizeChange'
						@current-page-change='discountMaterialPageChange'
						:taggelClassName='tableRowClassName'
					></xpt-list>
				</el-tab-pane>
                <el-tab-pane label='编制信息' name='four' class='xpt-flex'>
					<xpt-form :data='form' :cols='fourCols' class="test"></xpt-form>
				</el-tab-pane>
				<el-tab-pane label='附加设置' name='five' class='xpt-flex' v-if="!!form.additional_act_content">
					<xpt-form :data='form' :cols='form.additional_act_content === "FINALPAYACT" ? finalpayactCols  : (form.additional_act_content === "AUTHOR_ACTIVITY" ? authorActCols : (form.additional_act_content === "DY_ORDER_COUPONS" ? dyOrderCouponsCols : giftorderactCols))' class="test"></xpt-form>
				</el-tab-pane>
			</el-tabs>
		</div>
		<el-row class='xpt-flex__bottom' v-fold :style="{'display': ifShow ? 'block' : 'none'}">
			<el-tabs v-model="secondTab">
				<el-tab-pane label='优惠项目' name="discount_GoodsList" class='xpt-flex' >
					<xpt-list
						ref='GoodsList'
						:data='discountItemList'
						:colData='goodsCols'
						:showHead='false'
						selection=''
						:pageTotal='goodCount'
						:pageLength='goodSearchData.pageSize'
						@page-size-change='goodSizeChange'
						@current-page-change='goodPageChange'
						:taggelClassName='tableRowClassName'
					></xpt-list>
				</el-tab-pane>
				<el-tab-pane label='实施店铺' name="discount_shopList" class='xpt-flex'>
					<xpt-list
						ref='ShopList'
						:data='discountShopList'
						:colData='shopCols'
						:showHead='false'
						selection=''
						:pageTotal='shopCount'
						:pageLength='shopSearchData.pageSize'
						@page-size-change='shopSizeChange'
						@current-page-change='shopPageChange'
					></xpt-list>
				</el-tab-pane>
				<el-tab-pane label='作者账号' name="discount_authorList" class='xpt-flex' v-if='form.additional_act_content === "AUTHOR_ACTIVITY"'>
					<xpt-list
						ref='authorList'
						:data='listPmsActAuthorVO'
						:showHead='false'
						:colData='authorCols'
						selection=''
						:searchPage='authorSearch.page_name'
						:pageTotal='authorCount'
						:pageLength='authorSearch.pageSize'
						@page-size-change='authorPageSizeChange'
						@current-page-change='authorPageChange'
					></xpt-list>
				</el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
import goods from './model/goods.js'
import shop from './model/shop.js'
export default {
	props: ['params'],
	// mixins: [goods, shop],
	data() {
        var self = this
		return {
            discountItemList: [], // 优惠项目
            discountMaterialList:[], // 活动清单
            discountShopList: [], // 活动店铺
			conditionVo: [],
			ifShow: false, // 线上优惠详情不显示优惠项目和店铺
			shopSearchData:{
				page_name:"act_discount_shop",
				where:[],
				page_no:1,
				page_size:self.pageSize
			},
			shopCount: null,
			goodSearchData:{
				page_name:"act_discount_item",
				where:[],
				page_no:1,
				page_size:self.pageSize
			},
			goodCount: null,
			materialSearchData:{
				page_name:"act_discount_material",
				where:[],
				page_no:1,
				page_size:self.pageSize
			},
			discountMaterialCount: null,
			form: {
                // 优惠活动编码
                discount_no: null,
                // 优惠活动名称
                discount_name: null,
                // 生效时间
                enable_time: null,
                // 失效时间
                disable_time: null,
                // 店铺范围
                store_area: null,
                // 优惠类型
                discount_type: null,
                // 优惠子类型
                discount_sub_type: null,
                discount_condition_type: null,
                if_superposition_discount: null,
                if_each_full: null,
                if_need_audit: null,
                if_auto_add: null,
                remark: null,
                listActDiscountItemVo:[],
                // 创建人
                creator_name: null,
                // 创建日期
                create_time: null,
                // 审核人
                audit_name: null,
                // 审核日期
                audit_time: null,
                // 最后修改人
                modifier_name: null,
                // 最后修改时间
				modify_time: null,
				additional_act_content: null, // 活动类型：FINALPAYACT尾款订单活动 GIFTORDERACT礼品订单活动
				additional_start_time: null, // 生效时间
				additional_end_time: null, // 失效时间
				additional_flag: null, // 商品标识
				dealer_discount:null
			},
			secondTab: 'discount_GoodsList',
			firstTab: 'first',
			goodsCols: [
				{
					label: '项目编码',
					prop: 'discount_item_no',
					width: 140
				}, {
					label: '项目细类',
					prop: 'item_type_detail',
                    format: 'auxFormat',
                    formatParams: 'ITEM_TYPE_DETAIL',
					width: 120,
				}, {
					label: '优惠条件',
					prop: 'discount_condition_dec',
                    // formatter(val,index){
                    //     let row = self.form.listActDiscountItemVo[index] || {};
                    //     let keyWord = ''
                    //     self.conditionVo.forEach(item=>{
                    //         if(item.discount_condition_id == val){
                    //             if(self.form.discount_condition_type == 'FULL_YUAN'){
                    //                 keyWord = '满'+item.threshold_price
                    //             }else if(self.form.discount_condition_type == 'FULL_PIECE'||self.form.discount_condition_type =='MULTI_PIECE'){
                    //                 keyWord = '满'+item.threshold_count
                    //             }else{
                    //                 keyWord = '无条件'
                    //             }

                    //         }
                    //     })
                    //     row.discount_condition_dec = keyWord;
                    //     return keyWord;
                    // }
				}, {
					label: '优惠条件ID',
					prop: 'discount_condition_id',
					width: 130
				}, {
					label: '项目内容（金额可变的优惠项目，优惠金额以行内容为准）',
					prop: 'discount_item_dec',
                    //formatter(val,index){
                        //let row = self.form.listActDiscountItemVo[index] || {};
                        //let keyWord = ''
                        // if(row.subtract_money){
                        // 	keyWord = '减'+row.subtract_money+'元'
                        // }else if(row.discount){
                        // 	keyWord = '打'+row.subtract_money+'折'
                        // }else if()
                       // switch(row.discount_item_type){
                           // case 'ITEM_TYPE_DEDUCTE_AMOUNT' : self.form.listActDiscountItemVo[index].discount_item_dec = '减'+row.subtract_money+'元'; return '减'+row.subtract_money+'元'; break;
                            //case 'ITEM_TYPE_DISCOUNT' :self.form.listActDiscountItemVo[index].discount_item_dec = '打'+row.discount+'折';  return '打'+row.discount+'折'; break;
                            //case 'ITEM_TYPE_GIFTS' :self.form.listActDiscountItemVo[index].discount_item_dec = '赠送'+row.material_number;  return '赠送'+row.material_number; break;
                            //case 'ITEM_TYPE_UPGRADE' :self.form.listActDiscountItemVo[index].discount_item_dec = '换购'+row.material_number;  return '换购'+row.material_number; break;
                            //case 'ITEM_TYPE_GOODS_PROMOTION' :self.form.listActDiscountItemVo[index].discount_item_dec = '单品优惠'+row.material_number;  return '单品优惠'+row.material_number; break;
                        //}
                        // return keyWord;
					//},
					width: 330
				}, {
					label: '封顶金额',
					prop: 'max_amount',
				},  {
					label: '允许叠加',
					prop: 'if_allow_superposition',
					formatter(val){
						switch(val){
							case 'Y' : return '是'; break;
							case 'N' : return '否'; break;

						}
					}
				},{
					label: '物料名称',
					prop: 'material_name',
				}, {
					label: '价格',
					prop: 'act_price',
				}, {
					label: '名义售价',
					prop: 'nominal_price',
				},
				 {
					label: '经销结算价',
					prop: 'dealer_price',
				}, {
					label: '创建日期',
					prop: 'create_time',
                    format:'dataFormat1',
                    width:120
				}, {
					label: '行状态',
                    prop: 'row_status',
                    width: 80,
                    formatter(val,index){
                        switch(val){
                            case 1 : return '失效'; break;
                            case 0 : return '生效'; break;
                        }
                    }
				}, {
					label: '失效人',
					prop: 'disable_person_name',
					width: 80
				}, {
					label: '失效日期',
					prop: 'disable_time',
                    format: 'dataFormat1',
                    width: 120
				}
			],
			shopCols: [
				{
					label: '店铺编码',
					prop: 'shop_number'
				}, {
					label: '店铺',
					prop: 'shop_name'
				}, {
					label: '店铺分组',
					prop: 'shop_group',
					format: 'auxFormat',
                    formatParams: 'shopGroup',
				}, {
					label: '店铺分类',
					prop: 'shop_type'
				}, {
					label: '所属公司',
					prop: 'shop_province'
				},
				// {
				// 	label: '店铺状态',
				// 	prop: 'shop_status',
				// 	formatter: prop => ({
				// 		Y: '已审核',
				// 		N: '未审核',
				// 	}[prop] || prop),
				// },
				{
					label: '行状态',
					prop: 'status',
					formatter: self.shopStatusFormat
				}, {
					label: '失效人',
					prop: 'disable_person_name'
				}, {
					label: '失效时间',
					prop: 'disable_time',
					format: 'dataFormat1'
				}, {
					label: '创建时间',
                    prop: 'create_time',
					format: 'dataFormat1',
					width: 150,
				}
			],
			firstCols: [
				[
					{
						label: '优惠活动ID:',
						key: 'discount_id'
					}, {
						label: '优惠活动编码:',
						key: 'discount_no'
					}, {
						label: '优惠活动名称:',
						key: 'discount_name'
					}, {
						label: '生效时间:',
						key: 'enable_time',
						format: 'dataFormat1'
					}, {
						label: '失效时间:',
						key: 'disable_time',
						format: 'dataFormat1'
					}, {
						label: '时间维度:',
						key: 'discount_affection_date',
						format: 'auxFormat',
						formatParams: 'discountAffection'
					}, {
						label: '经销结算比例:',
						key: 'dealer_discount',
						formatter(val){
							return !!val?val+'%':'';
						}
					}, {
						label: '商品活动类目:',
						key: 'goods_discount_category_name',
					}, {
						label: '互斥活动编码:',
						key: 'exclusive_discount_no',
					}, {
						label: '添加控制时长:',
						key: 'time_control_value',
						formatter(val){
							return !val ? val : val + '小时';
						}
					}
				], [
					 {
						label: '优惠类型:',
						key: 'discount_type',
						format: 'auxFormat',
						formatParams: 'discountCategory'
					}, {
						label: '优惠子类型:',
						key: 'discount_sub_type',
						format: 'auxFormat',
						formatParams: 'discountSubclass'
					}, {
						label: '优惠条件:',
						key: 'discount_condition_type',
						format: 'auxFormat',
						formatParams: 'discount_condition'
					}, {
						label: '优惠性质:',
						key: 'discount_kind',
						formatter(val) {
								switch(val) {
										case 'ORDER_CUT': return '拍减';break;
										case 'ORDER_PRESENT': return '拍赠';break;
										case 'PAID_RETURN': return '付返';break;
										case 'PAID_PRESENT': return '付赠';break;
										default: return val; break
								}
						}
					},  {
						label: '优惠类别:',
						key: 'discount_category',
						format: 'auxFormat',
						formatParams: 'dicount_category_match'
					}, {
						label: '线下货款模式:',
						key: 'offline_payment_mode_name',
						format: 'auxFormat',
						formatParams: 'dicount_category_match'
					}, {
						label: '网拍订单店铺:',
						key: 'wangpai_order_shop_name',
					}, {
						label: '叠加优惠:',
						key:'if_superposition_discount',
						formatter(val) {
								return val == 'Y' ? '是' : '否'
						}
					}, {
						label: '每满:',
						key:'if_each_full',
						formatter(val) {
								return val == 'Y' ? '是' : '否'
						}
					}, {
						label: '金额可变:',
						key: 'if_amount_change',
						formatter(val) {
							return val == 'Y' ? '是' : '否'
						}
					}, {
						label: '预售优惠:',
						key: 'presell_act',
						formatter(val) {
							switch(val) {
								case 'Y': return '是'; break;
								case 'N': return '否'; break;
								default: return val; break;
							}
						},
					}, {
						label: '添加时效控制:',
						key: 'if_time_control',
						formatter(val) {
							switch(val) {
								case 'Y': return '是'; break;
								case 'N': return '否'; break;
								default: return val; break;
							}
						},
					}
				], [
					{
						label: '店铺范围:',
						key: 'store_area',
						formatter(val) {
							return 1 == val ? '全局' : (2 == val ? '分组' : '店铺')
						}
					}, {
						label: '优惠影响范围:',
						key: 'discount_effect_area',
						format: 'auxFormat',
						formatParams: 'DISCOUNT_EFFECT_AREA'
					}, {
						label: '需审核:',
						key: 'if_need_audit',
						formatter(val) {
							return val == 'Y' ? '是' : '否'
						}
					}, {
						label: '自动添加:',
						key: 'if_auto_add',
						formatter(val) {
							return val == 'Y' ? '是' : '否';
						}
					}, {
						label: '提前购:',
						key: 'if_in_advance_discount',
						formatter(val) {
							return val == 'Y' ? '是' : '否';
						}
					}, {
						label: '维护中奖名单:',
						key: 'if_act_winning',
						formatter(val) {
							return val == 'Y' ? '是' : '否';
						}
					}, {
						label: '多地址校验:',
						key: 'if_order_multiple_address',
						formatter(val) {
							return val == 'Y' ? '是' : '否';
						}
					},{
						label: '限制时效分摊:',
						key: 'time_limit_share',
						formatter(val) {
							return val == 'Y' ? '是' : '否';
						}
					}, {
						label: '仅享用一次:',
						key: 'if_use_once',
						formatter(val) {
							return val == 'Y' ? '是' : '否';
						}
					}, {
						label: '备注:',
						key: 'remark'
					}
				]
			],
			secondCols: [
					{
						label: '优惠条件ID',
						prop: 'discount_condition_id',
						width: 130
					},
						{
							label: '金额门槛',
							prop: 'threshold_price',
					}, {
							label: '件数门槛',
							prop: 'threshold_count',
					}, {
							label: '优惠选项',
							prop: 'item_enable_options',
							formatter(val) {
									return val === 'CHOOSEONEITEM' ? '任选一项' : (val === 'ALLSELECTION' ? '全选' : '任选')
							}
					}, {
							label: '选项项数',
							prop: 'item_enable_count'
					}
			],
			threeCols: [
				{
						label: '物料编码',
						prop: 'material_number',
				}, {
					label: '优惠条件',
					prop: 'discount_condition_id',
					formatter(val,index){
						// let row = self.form.listActDiscountItemVo[index] || {};
						let keyWord = '全局'
						if(!val){
							return keyWord;
						}
						self.conditionVo.forEach(item=>{
							// console.log(item.discount_condition_id,val,self.form.discount_condition_type)
							if(item.discount_condition_id == val){
								if(self.form.discount_condition_type == 'FULL_YUAN'){
									keyWord = '满'+item.threshold_price+'元'
								}else if(self.form.discount_condition_type == 'FULL_PIECE'||self.form.discount_condition_type =='MULTI_PIECE'){
									keyWord = '满 '+item.threshold_count+' 件'
								}else{
									keyWord = '无条件'
								}

							}
							// break;
						})

						// row.discount_condition_dec = keyWord;
						return keyWord;
					}
				}, {
					label: '优惠条件ID',
					prop: 'discount_condition_id',
					width: 130
				}, {
					label: '是否享受优惠',
					prop: 'if_enjoy_discount',
					formatter(val){
						switch(val){
							case 'Y' : return '是'; break;
							case 'N' : return '否'; break;
						}
					}
				},{
					label: '行状态',
					prop: 'row_status',
					formatter(val){
						switch(val){
							case 1 : return '失效'; break;
							case 0 : return '生效'; break;
						}
					}
				}, {
						label: '创建时间',
						prop: 'create_time',
						format: 'dataFormat1'
				}
			],
			fourCols: [
				[
					{
						label: '创建人:',
						key: 'creator_name'
					}, {
						label: '创建日期:',
						key: 'create_time',
						format: 'dataFormat1'
					}
				], [
					{
						label: '审核人:',
						key: 'audit_person_name'
					}, {
						label: '审核日期:',
						key: 'audit_time',
						format: 'dataFormat1'
					}
				], [
					{
							label: '最后修改人:',
							key: 'last_modifier_name'
					}, {
							label: '最后修改时间:',
							key: 'last_modify_time',
							format: 'dataFormat1',
							width: 100
					}
				]
			],
			finalpayactCols: [
				[
					{
						label: '活动类型:',
						key: 'additional_act_content',
						formatter(val) {
							return val === 'FINALPAYACT' ? '尾款订单活动' : '礼品订单活动'
						}
					}
				], [
					{
						label: '支付生效时间:',
						key: 'additional_start_time',
						format: 'dataFormat1'
					}
				], [
					{
						label: '支付失效时间:',
						key: 'additional_end_time',
						format: 'dataFormat1'
					}
				]
			],
			giftorderactCols: [
				[
					{
						label: '活动类型:',
						key: 'additional_act_content',
						formatter(val) {
							return val === 'GIFTORDERACT' ? '礼品订单活动' : '尾款订单活动'
						}
					}
				], [
					{
						label: '商品标识:',
						key: 'additional_flag'
					}
				]
			],
      dyOrderCouponsCols: [
				[
					{
						label: '活动类型:',
						key: 'additional_act_content',
						formatter(val) {
							return '抖音团购活动'
						}
					}
				], [
					{
						label: '抖音商品ID:',
						key: 'additional_flag'
					}
				], [
					{
						label: '抖音商品标题:',
						key: 'additional_expand'
					}
				]
			],
			dataGoodsDiscountCategoryObj: __AUX.get('goods_discount_category').reduce((pre, cur, idx, arr) => {
				pre[cur.code] = cur.name
				return pre
			},{}),
			offlinePaymentModeCategoryObj: __AUX.get('offline_payment_mode').reduce((pre, cur, idx, arr) => {
				pre[cur.code] = cur.name
				return pre
			},{}),
			authorActCols: [
				[
					{
						label: '活动类型:',
						key: 'additional_act_content',
						formatter(val) {
							return val === 'AUTHOR_ACTIVITY' ? '作者账号活动' : ''
						}
					}
				], [
					{
						label: '作者活动类型:',
						key: 'additional_flag_copy',
						formatter:(val)=>{
							let status = {
								A:'A：限定作者可享受，无作者也可享受',
								B:'B：限定作者可享受，无作者不可享受',
								C:'C：限定作者不可享受，无作者可享受',
								D:'D：限定作者不可享受，无作者不可享受'
							}
							return status[val] || val;
						}
					}
				]
			],
			authorCols: [
				{
					label: '作者ID',
					prop: 'author_id'
				}, {
					label: '作者名称',
					prop: 'author_name',
					width:300
				}, {
					label: '关联店铺',
					prop: 'shop_name',
				}, {
					label: '生效状态',
					prop: 'status',
					formatter: (val) => {
						switch(val){
							case 1 : return '生效';
							case 0 : return '失效';
						}
					}
				}, {
					label: '创建人',
					prop: 'create_name'
				}, {
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 150,
				}, {
					label: '失效时间',
					prop: 'disable_time',
					format: 'dataFormat1'
				}, {
					label: '失效人',
					prop: 'disable_name'
				}
			],
			authorSearch:{
				page_name: "act_discount_expand_author_account",
				where: [],
				page_size: self.pageSize,
				page_no: 1,
				discount_id: ''
			},
			authorCount: 0,
		}
	},
	methods: {
		authorPageSizeChange(size) {
			this.authorSearch.page_size = size;
			this.authorSearching()
		},
		authorPageChange(page_no) {
			this.authorSearch.page_no = page_no
			this.authorSearching()
		},
		getAuthorsSearchData (){
			this.listPmsActAuthorVO = []
			this.authorSearch.discount_id = this.params.act_id ? this.params.act_id : this.form.act_id
			this.ajax.postStream('/price-web/api/actDiscount/listExpandAuthorAccount',this.authorSearch, res => {
				if(res.body.result && res.body.content){
					this.authorCount = res.body.content.count || 0;
					this.listPmsActAuthorVO = res.body.content.list;
				}
			}, (err) => {})
		},
		// 获取活动信息 19.7.8
		async getData(){
			var data ={
				discount_id:this.params.act_id ? this.params.act_id : this.form.act_id
            };
            // // 优惠活动清单
            // await this.ajax.postStream('/price-web/api/actDiscount/getDiscountMaterialList', data, res => {
            //     if(res.body.result && res.body.content) {
			// 		this.discountMaterialList.push(...res.body.content.list)
			// 		this.discountMaterialCount = res.body.count
			// 	}
            // })
            // 活动编辑
            await this.ajax.postStream('/price-web/api/actDiscount/editPartInfo', data, res => {
                if(res.body.result && res.body.content) {
                    this.form = res.body.content.actDiscountVo;
                    let discountCategoryList = this.form.goods_discount_category.split(',')
                    this.form.goods_discount_category_name = discountCategoryList.reduce((name, cur,idx) => {
                        if (idx == (discountCategoryList.length -1) ) {
                            name += this.dataGoodsDiscountCategoryObj[cur]
                        } else {
                            name += this.dataGoodsDiscountCategoryObj[cur] + ','
                        }
                        return name
                    }, '')
					let offlinePaymentModeCategoryList = this.form.offline_payment_mode.split(',')
					this.form.offline_payment_mode_name = offlinePaymentModeCategoryList.reduce((name, cur,idx) => {
						if (idx == (offlinePaymentModeCategoryList.length -1) ) {
							name += this.offlinePaymentModeCategoryObj[cur]
						} else {
							name += this.offlinePaymentModeCategoryObj[cur] + ','
						}
						return name
					}, '')
                    this.conditionVo = res.body.content.listActDiscountConditionVo;
					this.ifShow = this.form.discount_type != 'ONLINE_DISCOUNT' ? true : false
					this.form.listActDiscountItemVo = res.body.content.listActDiscountItemVo ? res.body.content.listActDiscountItemVo:[];
					if (this.form.additional_act_content === "AUTHOR_ACTIVITY") {
						this.getAuthorsSearchData()
					}
				}
            })
		},
		getDiscountMaterial () {
			this.discountMaterialList = []
			let discount ={
				discount_id:this.params.act_id ? this.params.act_id : this.form.act_id
            };
			let data = Object.assign({}, this.materialSearchData, discount)
			this.ajax.postStream('/price-web/api/actDiscount/getDiscountMaterialList', data, res => {
                if(res.body.result && res.body.content) {
					this.discountMaterialList.push(...res.body.content.list)
					this.discountMaterialCount = res.body.content.count
				}
            })
		},
		getDiscountItem () {
			// 优惠活动项目
			this.discountItemList = []
			let discount ={
				discount_id:this.params.act_id ? this.params.act_id : this.form.act_id
            };
			let data = Object.assign({}, this.goodSearchData, discount)
            this.ajax.postStream('/price-web/api/actDiscount/getDiscountItem', data, res => {
                if(res.body.result && res.body.content) {
					this.discountItemList.push(...res.body.content.list)
					this.goodCount = res.body.content.count
				}
            })
		},
		getDiscountShop () {
			// 优惠活动店铺
			this.discountShopList = []
			let discount ={
				discount_id:this.params.act_id ? this.params.act_id : this.form.act_id
            };
			let data = Object.assign({}, this.shopSearchData, discount)
            this.ajax.postStream('/price-web/api/actDiscount/getDiscountShop', data, res => {
                if(res.body.result && res.body.content) {
					// this.discountShopList.push(...res.body.content.list)
					this.discountShopList = res.body.content.list;
					this.shopCount = res.body.content.count
				}
            })
		},
		goodSizeChange(size){
			this.goodSearchData.page_size = size
			this.getDiscountItem()
		},
		goodPageChange(page){
			this.goodSearchData.page_no = page
			this.getDiscountItem()
		},
		shopSizeChange(size){
			this.shopSearchData.page_size = size
			this.getDiscountShop()
		},
		shopPageChange(page){
			this.shopSearchData.page_no = page
			this.getDiscountShop()
		},
		discountMaterialSizeChange(size){
			this.materialSearchData.page_size = size
			this.getDiscountMaterial()
		},
		discountMaterialPageChange(page){
			this.materialSearchData.page_no = page
			this.getDiscountMaterial()
		},
        shopStatusFormat(val){
			if(val == 'Y'||val==undefined) {
				return "生效";
			}else{
				return "失效";
			}
		},
		// 高亮优惠项目和优惠清单
		conditionCellClick (row) {
			this.conditionVo.forEach(item => {
				if (item.color_sign) {
					item.color_sign = ''
				}
			})
			row.color_sign = 'A'
			this.conditionVo = JSON.parse(JSON.stringify(this.conditionVo));
			this.discountItemList.forEach(item=>{
				if(row.discount_condition_id === item.discount_condition_id){
					item.color_sign = 'A'
				} else {
					item.color_sign = '';
				}
			})
			this.discountItemList = JSON.parse(JSON.stringify(this.discountItemList));

			this.discountMaterialList.forEach(item=>{
				if(row.discount_condition_id === item.discount_condition_id){
					item.color_sign = 'A'
				} else {
					item.color_sign = '';
				}
			})
			this.discountMaterialList = JSON.parse(JSON.stringify(this.discountMaterialList));
		},
		tableRowClassName(row){
			switch (row.color_sign){
				case 'A': return  'tableGreen';
				case 'B': return  'blue';
			}
		}
	},
	created() {
		this.getData();
		this.getDiscountShop()
		this.getDiscountItem()
		this.getDiscountMaterial()
	}
}
</script>
<style lang="stylus">
// .scroll .el-table .xpt-flex__bottom, .scroll .xpt-flex .xpt-flex__bottom
// 	    overflow: auto;
	.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
		overflow: auto;
	.discount_table
		.xpt-flex__bottom
			.xpt-pagation
				position: absolute;
		.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
			overflow: hidden
			padding-bottom: 35px
	.discount_table
		.xpt-flex .el-table__body-wrapper, .el-table .el-table__body-wrapper
			min-height: 100px
			max-height: 200px
			overflow-x: hidden
	.tableGreen{
		background-color: #d8e4bc !important;
	}
	.tableGreen td {
		background-color: inherit !important;
	}
</style>

