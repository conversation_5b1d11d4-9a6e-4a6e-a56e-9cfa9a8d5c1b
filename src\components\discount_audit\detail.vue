<!-- 优惠审核--详情 -->
<template>
	<div class='xpt-flex'>
		<el-row class='xpt-flex__bottom'>
			<el-tabs v-model="secondTab">
				<el-tab-pane label='优惠审核' name="first" class='xpt-flex' >
					<xpt-list
						ref='auditList'
						:data='auditList'
						:btns='auditBtns'
            :taggelClassName='tableCustomerRowClassName'
						:colData='auditCols'
						@selection-change='select'
					></xpt-list>
				</el-tab-pane>

        <el-tab-pane label='内部便签' name="secondinner" class='xpt-flex'>
          <inner ref='inner'></inner>
        </el-tab-pane>
			</el-tabs>
		</el-row>
		<el-row class='xpt-flex__bottom' v-fold>
			<el-col :span='24' style='height: 100%'>
				<el-tabs v-model="firstTab">
					<el-tab-pane label='商品信息' name="first" class='xpt-flex'>
					<xpt-list
						ref='goodsList'
						selection=''
						:data='goodsList'
						:colData='goodsCols'
						:showHead='false'
					></xpt-list>
				</el-tab-pane>
					<el-tab-pane label='合并订单信息' name="second" class='xpt-flex' >
						<xpt-list
							ref='mergeList'
							selection=''
							:data='mergeList'
							:colData='mergeCols'
							:showHead='false'
						></xpt-list>
					</el-tab-pane>
          <el-tab-pane label="对账明细" name="accounts" class="xpt-flex">
            <xpt-list
              :data='accountList'
              :colData='accountCols'
              :showHead='false'
              selection=''
            ></xpt-list>
          </el-tab-pane>
          <el-tab-pane label='支付明细' name="payments" class='xpt-flex' >
						<xpt-list
              :data="paymentList.length ? paymentList.concat({
                pay_amount: '合计' + paymentList.reduce((a, b) => {
                  if(b.type === 'RECEIVE' || b.type == 'CARRYOVERS' || b.type == 'SRYJZ'){
                    a += (Number(b.pay_amount) || 0)
                  }else if (/^(REFUND|RETURNS)$/.test(b.type)|| /^(运费|三包费)$/.test(b.received_account)){
                    a -= (Number(b.pay_amount) || 0)
                  }
                  return a
                }, 0).toFixed(2)
              }) : paymentList"
              :taggelClassName='paymentListClassName'
              :colData='paymentCols'
              :showHead='false'
              selection=''
            ></xpt-list>
					</el-tab-pane>
				</el-tabs>
			</el-col>
		</el-row>
	</div>
</template>
<script>
// 合并订单信息
 import merge from './model/merge'
// 内部便签
import inner from '@components/common/inner'
// 支付明细
import payment from './model/payment'
// 优惠审核
import audit from './model/audit'
// 订单商品详情
import goods from './model/goods'
// 对账明细
import account from './model/account'
export default {
	props: ['params'],
	mixins: [merge, payment, audit, goods, account],
	components: {inner},
	data() {
		return {
			firstTab: 'first',
			secondTab: 'first',
			threeTab: 'first',
			bool:false//是否要刷新页面，
		}
	},
	watch:{
		bool:function(newval,oldval){
			this.getData();
		}
	},
	methods: {
		// 查询优惠审核详情
		getData() {
			let data = {
				merge_trade_id: this.params.initData.merge_trade_id
			}
			this.ajax.postStream('/order-web/api/mergetrade/discount/getApprove', data, res => {
				if(res.body.result && res.body.content) {
					let content = res.body.content
					// 优惠审核
					this.auditList = content.listMegeDiscountInfoVO || []
					// 支付明细
					// this.payment = content.mergePaymentVO || {};
					//商品信息
					this.goodsList = content.listSysOrderVO || [];
					// 合并订单信息
					let mergeList = []
					;(content.listSysTradeVO || []).find(d => {
						// 送货方式
						let deliver = d.list_deliver_method
						d.list_deliver_method = deliver[0]
						mergeList.push(d)
						deliver.splice(0, 1)
						deliver.find(list => {
							mergeList.push({
								list_deliver_method: list
							})
						})
					})
					// 获取内部便签
					this.$refs.inner && this.$refs.inner.getInnerList(data.merge_trade_id);
          this.mergeList = mergeList
          this.getPayMentList(data.merge_trade_id)
				}
			})
		},
		// 获取当前优惠审核是否可以操作
		addEditToken() {
      const params = {
        page_id:this.params.initData.merge_trade_id,
				merge_trade_id: this.params.initData.merge_trade_id,
        page_type:'DISCOUNT'
			}
			this.ajax.postStream('/order-web/api/mergetrade/addEditToken',
				params, res => {
					if(!res.body.result) {
						this.$message({
							type: 'error',
							message: res.body.msg
						})
						// 禁止按钮操作；包括优惠审核的审核、反审核按钮
						this.auditBtns = [
							{
								type: 'success',
								txt: '审核',
								disabled: true,
								click: self.auditAudit
							}, {
								type: 'danger',
								txt: '反审核',
								disabled: true,
								click: self.auditDisaudit
							}, {
                type: 'warning',
                txt: '驳回',
                disabled: true,
								click () {
                  self.rejectDisaudit(self.selectedList)
                }
							}
						]
					}
					// 定时器，组件销毁的时候，直接重写这个函数
					// this.setTimeout && this.setTimeout()
			})
		},
		// setTimeout() {
		// 	setTimeout(this.addEditToken, 8000)
		// },
			/**
		 * 关闭页面时去除留在tab下面的merge_trade_id，留在radie下的缓存
		 * **/
		deteleKey(merge_trade_id_list){
			let self = this;
      const editTokens=merge_trade_id_list.map(item=>{
          return {
            page_id:item,
            merge_trade_id: item,
            page_type:'DISCOUNT'
          }
        })
			let data = {
				editTokens,
				operator_id:self.getUserInfo('id')
			}
			this.ajax.postStream('/external-web/api/removeConsolidated/deteleKey',data,(d)=>{
			    var data = d.body;

			})
		},
    tableCustomerRowClassName(row,index) {
      let str='';
      if(row.checkFail&&row.checkFail==='Y'){
        str=`${str} ${this.$style["check-fail"]}`
      }
      return str
    },
	},
	mounted() {
		// 获取优惠审核详情
		this.getData()
		// 获取订单商品详情
    this.addEditToken()
    // 获取对账明细
    this.getAccountList()
	},
	beforeDestroy(){
		this.deteleKey([this.params.initData.merge_trade_id])
	},
	destroyed() {
		// this.setTimeout = null
	}
}
</script>
<style lang="stylus" scoped>
.paddingLeft
	padding-left: 10px
	height: 100%
.row
	height: 26px
	line-height:26px
	border-bottom: 1px solid #d1dbe5
	text-align:center
	.el-col
		border-right: 1px solid #d1dbe5
		&:first-child
			border-left: 1px solid #d1dbe5
		&:after
			content: ''
			display: inline-block
	&:nth-child(2n-1)
		background: #eef1f6
</style>
<style>
.mergered2, .mergered2 td{
	color: red!important;
}
</style>
<style module>
.check-fail,.check-fail td{
  background-color: #f99 !important;
}
</style>
