<!-- 待审核优惠列表 -->
<template>
	<xpt-list-dynamic
		:data='auditList'
		:btns='auditBtns'
		:colData='auditCols'
		:pageTotal='auditCount'
		selection=''
		searchPage='scm_sys_trade_toaudit'
		@search-click='searchClick'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
	></xpt-list-dynamic>
</template>
<script>
export default {
	props: ['params'],
	data() {
		let self = this
		return {
			auditList: [],
			auditBtns: [
				{
					type: 'primary',
					txt: '刷新',
					loading: false,
					click() {
						self.getList()
					}
				}
			],
			auditCols: [
				{
					label: '销售单号',
					prop: 'sys_trade_no',
					width: 160,
				}, {
					label: '合并单号',
					prop: 'merge_trade_no',
					width: 160,
					redirectClick(row) {
						self.openDetail({
							initData: {
								merge_trade_id: row.merge_trade_id
							}
						})
					}
				}, {
					label: '买家昵称',
					prop: 'customer_name'
				}, {
					label: '订单店铺',
					prop: 'shop_name'
				}, {
					label: '原始店铺',
					prop: 'original_shop_name'
				}, {
					label: '业务员',
					prop: 'user_name'
				}, {
					label: '分组',
					prop: 'group_name'
				}, {
					label: '大分组',
					prop: 'big_group_name'
				}, {
					label: '总支付金额',
					prop: 'amount'
				},{
					label: '是否经销商订单',
					prop: 'if_dealer',
					formatter(val){
						switch(val){
							case "N": return "否";break;
							case "Y": return "是";break;
						}
					}
				},  {
					label: '驳回备注',
					prop: 'rejectMsg'
				}
			],
			auditCount: 0,
			search: {
				page_no: 1,
				page_size: this.pageSize,
				page_name: 'scm_sys_trade',
				where: []
			}
		}
	},
	methods: {
		// 搜索
		searchClick(obj, resolve) {
			this.search.where = obj
			this.getList(resolve)
		},
		pageSizeChange(ps) {
			this.search.page_size = ps
			this.getList()
		},
		pageChange(page) {
			this.search.page_no = page
			this.getList()
		},
		// 获取数据
		getList(resolve) {
			this.auditBtns[0].loading = true;
			this.ajax.postStream('/order-web/api/mergetrade/discount/listApprove?permissionCode=DISCOUNT_AUDIT_QUERY', this.search, res => {
				if(res.body.result && res.body.content) {
					this.auditList = res.body.content.list || []
					this.auditCount = res.body.content.count
				} else {
					this.$message.error(res.body.msg || '')
				}
				resolve && resolve();
				this.auditBtns[0].loading = false;
			}, err => {
				this.auditBtns[0].loading = false;
				this.$message.error(err);
				resolve && resolve();
			})
		},
		// 详情
		openDetail(params) {
			this.$root.eventHandle.$emit('creatTab', {
				name: '优惠审核详情',
				params: params,
				component: () => import('@components/discount_audit/detail')
			})
		}
	},
	mounted() {
		this.getList()
	}
}
</script>
