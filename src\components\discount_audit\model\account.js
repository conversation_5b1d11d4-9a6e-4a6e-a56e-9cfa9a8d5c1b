// 合并订单 -- 对账明细
export default {
	data(){
		var self =this;
		return{
			accountList:[],
			accountCols: [
				{
					label: '合并单号',
					width: 170,
					prop: 'merge_trade_no'
				}, {
					label: '对账余额',
					prop: 'state_rest_amount'
				}, {
					label: '结余',
					prop: 'rest_amount'
				}, {
					label: '支付金额',
					prop: 'pay_amount'
				}, {
					label: '退款金额',
					prop: 'refund_amount'
				}, {
					label: '总应收运费',
					prop: 'carriage_amount'
				}, {
					label: '已财审运费',
					prop: 'audit_carriage_amount'
				}, {
					label: '未财审运费',
					prop: 'dis_audit_carriage_amount'
				}, {
					label: '总货值',
					prop: 'total_goods_amount'
				}, {
					label: '已财审货值',
					width: 100,
					prop: 'audit_goods_amount'
				}, {
					label: '未财审货值',
					width: 100,
					prop: 'dis_audit_goods_amount'
				}, {
					label: '加收运费',
					prop: 'extra_carriage_fee'
				}, {
					label: '运费优惠',
					prop: 'discount_carriage_amount'
				}, {
					label: '历史余额',
					prop: 'history_rest_amount'
				}, {
					label: '标品结余',
					prop: 'stand_rest_amount'
				}, {
					label: '定制结余',
					prop: 'customization_rest_amount'
				}, {
					label: '标品对账余额',
					prop: 'stand_state_rest_amount',
					width: 100
				}, {
					label: '定制对账余额',
					prop: 'customization_state_rest_amount',
					width: 100
				}, {
					label: '已财审标品货值',
					prop: 'audit_stand_goods_amount',
					width: 100
				}, {
					label: '已财审定制货值',
					prop: 'audit_customization_goods_amount',
					width: 100
				}, {
					label: '标品支付金额',
					prop: 'stand_pay_amount',
					width: 100
				}, {
					label: '定制支付金额',
					prop: 'customization_pay_amount',
					width: 100
				}, {
					label: '返现优惠金额',
					prop: 'paid_return_disount_amount',
					width: 100
				}, {
					label: '总应收服务费',
					prop: 'totalServiceFeesReceivable',
					width: 100
				}, {
					label: '已财审服务费',
					prop: 'totalVerifiedServiceFee',
					width: 100
				}
			]
		}
	},
	methods:{
		getAccountList(){
            if (!this.params.initData.merge_trade_id) return
			this.ajax.postStream('/order-web/api/mergetrade/payment/get', this.params.initData.merge_trade_id, res => {
				if(res.body.result) {
					let accountDetil = res.body.content;
					accountDetil.audit_goods_amount = accountDetil.audit_goods_amount||'0.00';
					accountDetil.audit_carriage_amount = accountDetil.audit_carriage_amount||'0.00';
					this.accountList = []
					this.accountList.push(accountDetil)
				}else{
					this.$message.error(res.body.msg)
				}
			});
		}
	}
}
