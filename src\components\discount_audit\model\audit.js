// 优惠审核 -- 优惠审核
export default {
	data() {
		let self = this
		return {
			auditList: [],
			selectedList:[],
			auditBtns: [
				{
					type: 'success',
					txt: '审核',
					loading: false,
					click: self.auditAudit
				}, {
					type: 'danger',
					txt: '反审核',
					loading: false,
					click: self.auditDisaudit
				}, {
                    type: 'warning',
                    txt: '驳回',
                    disabled: false,
                    click () {
                      self.rejectDisaudit(self.selectedList)
                    }
                }
			],
			auditCols: [
				{
					label: '销售单号',
                    prop: 'sys_trade_no',
                    width: 165
				}, {
					label: '淘宝单号',
					prop: 'tid',
					width: 165
				}, {
					label: '商品名称',
					prop: 'materia_name',
                    width: 140
				}, {
					label: '优惠活动编码',
					prop: 'act_no',
                    width: 140
				}, {
					label: '优惠活动',
					prop: 'act_name',
                    width: 160
				}, {
					label: '优惠分类',
					prop: 'discount_category'
				}, {
					label: '子分类',
					prop: 'discount_subclass',
                    width: 100
				}, {
					label: '优惠金额',
					prop: 'discount'
				},{
					label: '状态',
					prop: 'status'
				},   {
					label: '物料编码',
					prop: 'material_num',
                    width: 120
				}, {
					label: '物料名称',
					prop: 'meterial_name'
				}, {
					label: '实际售价',
					prop: 'act_price'
				}, {
					label: '优惠创建时间',
                    prop: 'disCreateTime',
                    format:"dataFormat1",
					width: 130
				}, {
					label: '优惠审核时间',
					prop: 'disAuditTime',
                    format:"dataFormat1",
					width: 130
				}, {
					label: '优惠审核人',
					prop: 'auditer'
				}, {
					label: '赠品标志',
					prop: 'if_gift'/*,
					format: 'yesOrNo'*/
				}, {
					label: '售中赠品',
					prop: 'if_sale_gif'/*,
					format: 'yesOrNo'*/
				}, {
					label: '驳回备注',
					prop: 'rejectMsg',  
				}
			]
		}
	},
	methods: {
		select(d){
			this.selectedList = d.length?d:[]
		},
		/**
		*重置数据
		*/
		resetData(){
			this.selectedList = [];
			this.bool = !this.bool;
		},
		// 审核
		auditAudit() {
			let data = this.getSubmitData()
			if(!data) return
			this.auditBtns[0].loading = true;
			this.ajax.postStream('/order-web/api/mergetrade/discount/allApprove', data, res => {
               this.$message({
					type:res.body.result?'success':'error',
					message:res.body.msg
				})
				if(res.body.result) {
					this.resetData()
				}
				this.auditBtns[0].loading = false;
			}, err => {
				this.auditBtns[0].loading = false;
				this.$message.error(err);
			})
		},
		// 反审核
		auditDisaudit() {
			let data = this.getSubmitData()
			if(!data) return
			this.auditBtns[1].loading = true;
			this.ajax.postStream('/order-web/api/mergetrade/discount/cancelAllApprove', data, res => {
                this.$message({
					type:res.body.result?'success':'error',
					message:res.body.msg
				})
				if(res.body.result) {
					this.resetData()
				}
				this.auditBtns[1].loading = false;
			}, err => {
				this.auditBtns[1].loading = false;
				this.$message.error(err);
			})
		},
		/**
		*获取审核反审核的数据
		list_batch_trade_id	批次订单ID集合	object	
		list_discount_id	优惠券类ID集合	array<number>	对应字段是discount_id
		list_entry_id	合并赠品ID集合	array<number>	对应字段是entry_id
		list_materiel_id	批次赠品ID集合	object	对应字段是materie_id
		merge_trade_id	合并订单	number
		**/
		getSubmitData() {
			let list_batch_trade_id = [];//批次订单ID集合
			let list_discount_id = [];//优惠券类ID集合
			let list_entry_id = [];//合并赠品ID集合
			let list_materiel_id = [];//批次赠品ID集合
			let merge_trade_id = (this.mergeList[0] || {}).merge_trade_id;//合并订单


			let data = this.selectedList;
			if(!data || !data.length) {
				this.$message.error('请选择要操作的数据');
				return false;
			}
			for(let v in data) {
				let batch_trade_id = data[v].batch_trade_id;
				let discount_id = data[v].discount_id;
				let entry_id = data[v].entry_id;
				let matrial_id = data[v].materiel_id;

				if(batch_trade_id) {
					list_batch_trade_id.push(batch_trade_id);
				}
				if(discount_id) {
					list_discount_id.push(discount_id);
				}
				if(entry_id) {
					list_entry_id.push(entry_id);
				}
				if(matrial_id) {
					list_materiel_id.push(matrial_id);
				}
			}
			return {
				list_batch_trade_id:list_batch_trade_id,
				list_discount_id:list_discount_id,
				list_entry_id:list_entry_id,
				list_materiel_id:list_materiel_id,
				merge_trade_id:merge_trade_id
			}
        },
        rejectDisaudit(list) {
            let dataList = JSON.parse(JSON.stringify(list))
            let self = this;
            if(!dataList || !dataList.length) {
                this.$message.error('请选择要操作的数据');
                return false;
            }
            this.$root.eventHandle.$emit('alert', {
                component: () => import('@components/receiptAdjustment/rejectDialog.vue'),
                style: 'width:660px;height:180px',
                title: "驳回",
                params: {
                    //刷新
                    callback: data => {
                        let postData = {
                            rejectMsg: data,
                            list_discount_id: [],
                            list_discount_entry_id: [],
                            merge_trade_id: self.mergeList[0].merge_trade_id
                        }
                        for(let v in dataList) {
                            let discountId = dataList[v]['discount_id'], entryId = dataList[v]['entry_id'];
                            console.log('sdddd',entryId)
                            if(discountId) {
                                postData['list_discount_id'].push(discountId);
                            }
                            if(entryId) {
                                postData['list_discount_entry_id'].push(entryId);
                            }
                        }
                        self.comfirmRejectDisaudit(postData)
                    }
                },
            })
        },
        comfirmRejectDisaudit (params) {
            this.ajax.postStream('/order-web/api/mergetrade/discount/rejectApprove',
                params,
                res => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg)
                        this.getData()
                        // 获取订单商品详情
                        this.addEditToken()
                        // 获取对账明细
                        this.getAccountList()
                    } else {
                        this.$message.error(res.body.msg || "");
                    }
                },
                err => {
                    this.$message.error(err);
                }
            );
        },
	}
}
