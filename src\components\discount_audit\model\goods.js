// 优惠审核 -- 订单商品详情
export default {
	data() {
		return {
			goodsList: [],
			goodsCols: [
				{
					label: '销售单号',
					prop: 'sys_trade_no',
					width: 170
				}, {
					label: '商品编码',
					prop: 'materia_number',
					width: 170
				}, {
					label: '商品名称',
					prop: 'materia_name'
				}, {
					label: '商品规格',
					prop: 'materia_specifications'
				}, {
					label: '标准售价',
					prop: 'stand_price'
				},{
					label: '线上售价',
					prop: 'sale_price'
				},{
					label: '实际售价',
					prop: 'act_price'
				},{
					label: '线上优惠金额',
					prop: 'coupons',
					width: 140
				},{
					label: '线下优惠金额',
					prop: 'off_line_coupon_amount',
					width: 140
				},  {
					label: '付返前实际售价',
					prop: 'before_discount_act_price',
					width: 140
				},{
					label: '行状态',
					prop: 'status',
					formatter(val) {
						let str = val
						switch(val) {
							case "CANCEL": str = '取消'; break;
							case "NORMAL": str = '生效'; break;
							case "WAITING": str = '等待发运'; break;
							case "PART_DELIVERED": str = '部分发运中'; break;
							case "DELIVERED": str = '已发运'; break;
						}
						return str
					}
				},{
					label: '销售利润分类',
					prop: 'sales_profit_class',
					formatter(val) {
						let str = val
						switch(val) {
							case "A": str = 'A类商品'; break;
							case "B": str = 'B类商品'; break;
							case "C": str = 'C类商品'; break;
							case "PART_DELIVERED": str = '部分发运中'; break;
							case "DELIVERED": str = '已发运'; break;
						}
						return str
					},
					width: 140
				},  {
					label: '价格表',
					prop: 'price_list_name',
					width: 340
				}, {
					label: '承诺发货日期',
					prop: 'commit_time',
					format: 'dataFormat1',
					width: 140
				},{
					label: '地址',
					prop: 'address_name',
					width: 150
				},  {
					label: '商品活动',
					prop: 'activity_name'
				}, {
					label: '单位',
					prop: 'unit_name',
				}, {
					label: '行锁定状态',
					prop: 'lock_status',
					formatter(val) {
						return val === 'N' ? '未锁定' : '已锁定'
					}
				}, {
					label: '赠品标志',
					prop: 'if_gift',
					format: 'yesOrNo'
				}, {
					label: '行创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 140
				}, {
					label: '行商品类型',
					prop: 'item_type',
					formatter(val) {
						let str = val
						switch(val) {
							case "GOODS": str = '商品'; break;
							case "PRESENT": str = '赠品'; break;
							case "FREIGHT": str = '运费'; break;
							case "DEPOSIT": str = '定金'; break;
						}
						return str
					}
				},  {
					label: '取消原因',
					prop: 'cancel_reason'
				}, {
					label: '行类型',
					prop: 'line_type',
					formatter(val) {
						let str = val
						switch(val) {
							case "ORDER": str = '销售'; break;
							case "RETURN": str = '退货'; break;
							case "EXCHANGE": str = '换货'; break;
						}
						return str
					}
				}, {
					label: '审核状态',
					
					prop: 'audit_status',
					formatter(val) {
						return val === 'Y' ? '已审核' : '未审核'
					}
				}, {
					label: '审核人',
					prop: 'auditor_name',
				}, {
					label: '审核时间',
					prop: 'audit_time_str',
				}, {
					label: '订单来源',
					prop: 'order_source',
					formatter(val) {
						let str = val
						switch(val) {
							case "EXTERNAL_IMPORT": str = '外部导入'; break;
							case "MANUAL": str = '手工添加'; break;
							case "EXCHANGE": str = '换货操作'; break;
							case "RETURN_EXCHANGE": str = '售后进行退换货操作'; break;
							case "DISCOUNT": str = '优惠导入'; break;
						}
						return str
					}
				}
			],
		}
	},
	methods: {
		// 根据合并订单ID获取订单商品信息
		getGoodsList() {
			let data = {
				merge_trade_id: this.params.initData.merge_trade_id,
				// page_no: this.goodsSearch.pageNo,
				// page_size: this.goodsSearch.pageSize
			}
			this.ajax.postStream('/order-web/api/mergetrade/order/list', data, res => {
				if(res.body.result && res.body.content) {
					this.goodsList = res.body.content.list || []
				}
			})
		}
	}
}
