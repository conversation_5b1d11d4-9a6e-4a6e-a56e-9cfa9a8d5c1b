// 优惠审核 -- 合并订单
export default {
	data() {
		let self = this
		return {
			mergeList: [],
			mergeCols: [
				{
					label: '合并单号',
					prop: 'merge_trade_no',
					redirectClick(row) {
						var params = {
							merge_trade_id : row.merge_trade_id
						}
						self.$root.eventHandle.$emit('creatTab',{ 
							name:"合并订单详情",
							params:params,
							component: () => import('@components/order/merge.vue')
						});
					}
				}, {
					label: '买家昵称',
					prop: 'customer_name'
				}, {
					label: '淘宝单号',
					prop: 'tid'
				}, {
					label: '订单店铺',
					prop: 'shop_name'
				}, {
					label: '送货方式',
					prop: 'list_deliver_method'
				}
			]
		}
	}
}