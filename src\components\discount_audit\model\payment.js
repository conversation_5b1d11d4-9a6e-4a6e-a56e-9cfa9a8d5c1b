// 优惠审核 -- 支付明细
export default {
	data() {
		return {
			paymentList: [],
            paymentCols: [
				{
					label: '合并订单',
					prop: 'merge_trade_no',
					width: 180
				},{
					label: '销售订单',
					prop: 'sys_trade_no',
					width: 180
				},{
					label: '来源类型',
					prop: 'source_type',
					format: 'auxFormat',
					formatParams: 'pay_source_type',
				},{
					label: '支付明细类型',
					prop: 'type',
					formatter: (val)=>{
					    switch(val) {
					        case 'RECEIVE':
					            return '收入';
					        case 'REFUND':
					            return '支出';
					        case 'CARRYOVERS':
					            return '结转';
					        case 'RETURNS':
					            return '退货货款';
					        case 'COMPENSATION':
											return '赔偿';
									case 'SRYJZ':
											return '结转';
					        default:
					            return val;
					            break;
					    }
					},
					width: 100
				},{
					label: '支付渠道',
					prop: 'payment_channel',
					format: 'auxFormat',
					formatParams: 'payChannel'
				}, {
					label: '支付方式',
					prop: 'pay_type',
					format: 'auxFormat',
					formatParams: 'payType'
				}, {
					label: '收款账号',
					prop: 'received_account'
				}, {
					label: '收款金额',
					prop: 'pay_amount',
					formatter: (val, index, row)=> {
						return /合计/.test(val) ? val : ((/^(REFUND|RETURNS|COMPENSATION)$/.test(row.type) ? -1 : 1) * val)
					}
				}, {
					label: '支付账号',
					prop: 'pay_account'
				}, {
					label: '开户行',
					prop: 'pay_bank'
				}, {
					label: '用户名',
					prop: 'pay_name'
				}, {
					label: '支付时间',
					prop: 'payment_time',
					format:"dataFormat1",
					width: 150,
				}, {
					label: '定制支付类型',
					prop: 'customized_pay',
					formatter: prop => ({
						Y: '一口价',
					    D: '定金',
					    W: '尾款',
					}[prop] || prop),
					width: 100
				}
			],
			paymentQuery: {
				page_no: 1,
				page_size: 10000,
            }
		}
    },
    methods: {
        getPayMentList (mergeTradeId) {
            let data = JSON.parse(JSON.stringify(this.paymentQuery));
            data.merge_trade_id = mergeTradeId
            this.ajax.postStream('/order-web/api/mergetrade/payment/list', data, res => {
                if(res.body.result && res.body.content) {
                    this.paymentList = (res.body.content.list || []).sort((a, b) => a.payment_id - b.payment_id);
                } else {
                    this.$message.error(res.body.msg || '');
                }
            }, err => {
                this.$message.error(err)
            })
        },
        // 支出，整行标红显示
        //支付明细类型,赔偿？,退货？,赔偿金?,收入结转
        paymentListClassName (row, index){
            if(/^(REFUND|RETURNS|COMPENSATION|SRYJZ)$/.test(row.type)) {
                return 'mergered2'
            }
        },
    },
}