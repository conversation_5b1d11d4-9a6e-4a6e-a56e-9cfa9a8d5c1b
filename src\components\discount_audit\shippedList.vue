<!-- 已发运待审核优惠列表 -->
<template>
	<xpt-list
        id='table2'
		:data='auditList'
        :btns="[]"
		:colData='auditCols'
		:pageTotal='auditCount'
		selection='radio'
		searchPage='scm_sys_trade_toaudit_despatch'
		@search-click='searchClick'
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
        :taggelClassName='tableCustomerRowClassName'
        @radio-change="selectGoodsChange"
	>
        <template slot="btns">
            <el-button size="mini" :loading="loading" type="primary" @click="getList()">刷新</el-button>
            <el-select @change="signColor" placeholder="颜色标签" style="width:80px;" v-model="value" :disabled="Object.keys(selectRow).length==0" size="mini">
                <el-option
                v-for="item in colors"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                </el-option>
            </el-select>
        </template>
        <template slot="sys_trade_no" slot-scope="scope">
            <div>
                <span style="width:10px;height:10px;border-radius:50%;display: inline-block;margin-right:20px;"
                :class="!!scope.row.colorSign?scope.row.colorSign.toLowerCase():''"></span>{{scope.row.sys_trade_no}}
            </div>
        </template>
    </xpt-list>
</template>
<script>
    const COLORS=[
        {
            label:"黄色",
            value:"yellow"
        },
        {
            label:"红色",
            value:"red"
        },
        {
            label:"绿色",
            value:"green"
        },
    ]
export default {
	props: ['params'],
	data() {
		let self = this
		return {
            value:"",
            colors:COLORS,
            selectRow:{},
			auditList: [],
            loading:false,
			auditCols: [
				{
					label: '销售单号',
					slot: 'sys_trade_no',
					width: 160,
				}, {
					label: '合并单号',
					prop: 'merge_trade_no',
					width: 160,
					redirectClick(row) {
						self.openDetail({
							initData: {
								merge_trade_id: row.merge_trade_id
							}
						})
					}
				}, {
					label: '买家昵称',
					prop: 'customer_name'
				}, {
					label: '订单店铺',
					prop: 'shop_name'
				}, {
					label: '原始店铺',
					prop: 'original_shop_name'
				}, {
					label: '业务员',
					prop: 'user_name'
				}, {
					label: '分组',
					prop: 'group_name'
				}, {
					label: '大分组',
					prop: 'big_group_name'
				}, {
					label: '总支付金额',
					prop: 'amount'
				},{
					label: '审核状态',
					prop: 'discount_audit_status',
					formatter(val){
						switch(val){
							case "N": return "未审核";break;
							case "R": return "驳回";break;
							case "Y": return "已审核";break;
						}
					}
				},{
					label: '优惠创建时间',
					prop: 'discount_create_time',
					format: 'dataFormat1',
					width: 140
				},{
					label: '优惠驳回时间',
					prop: 'rejectTime',
					format: 'dataFormat1',
					width: 140
				},{
					label: '优惠审核时间',
					prop: 'discount_audit_time',
					format: 'dataFormat1',
					width: 140
				},{
					label: '发货状态',
					prop: 'if_out_stock',
					formatter(val){
						switch(val){
							case 0: return "未发货";break;
							case 1: return "已发货";break;
						}
					}
				}, {
					label: '是否经销商订单',
					prop: 'if_dealer',
					formatter(val){
						switch(val){
							case "N": return "否";break;
							case "Y": return "是";break;
						}
					}
				}, {
					label: '驳回备注',
					prop: 'rejectMsg'
				}
			],
			auditCount: 0,
			search: {
				page_no: 1,
				page_size: this.pageSize,
				page_name: 'scm_sys_trade_toaudit_despatch',
				where: []
			}
		}
	},
	methods: {
        signColor(val){
                if(!!val){
                    let params={
                        merge_discount_id : this.selectRow.mergeDiscountId,
                        entry_id : "",
                        materiel_id : "",
                        act_discount_id : "",
                        act_discount_item_id : "",
                        merge_trade_id : this.selectRow.merge_trade_id,
                        color_sign : this.value
                    }
                    this.ajax.postStream('/order-web/api/mergetrade/discount/signApprove',params, res => {
                        if(res.body.result) {
                            this.$message.success(res.body.msg || '')
                            this.getList()
                            this.selectRow={}
                            this.value=""
                        } else {
                            this.$message.error(res.body.msg || '')
                        }
                    }, err => {
                        this.$message.error(err);
                    })
                }
        },
        selectGoodsChange (s){
			this.selectRow = s
		},
    tableCustomerRowClassName(row,index) {
      let str='';
			switch (row.colorSign){
				case 'green':
          str = 'green';
          break;
				case 'red':
          str = 'green';
          break;
				case 'yellow':
          str = 'yellow';
          break;
			}
      if(row.checkFail&&row.checkFail==='Y'){
        str=`${str} ${this.$style["check-fail"]}`
      }
      return str
		},
		// 搜索
		searchClick(obj, resolve) {
			this.search.where = obj
			this.getList(resolve)
		},
		pageSizeChange(ps) {
			this.search.page_size = ps
			this.getList()
		},
		pageChange(page) {
			this.search.page_no = page
			this.getList()
		},
		// 获取数据
		getList(resolve) {
			this.loading = true;
			this.ajax.postStream('/order-web/api/mergetrade/discount/listApproveDespatch', this.search, res => {
				if(res.body.result && res.body.content) {
					this.auditList = res.body.content.list || []
					this.auditCount = res.body.content.count
				} else {
					this.$message.error(res.body.msg || '')
				}
				resolve && resolve();
				this.loading = false;
			}, err => {
				this.loading = false;
				this.$message.error(err);
				resolve && resolve();
			})
		},
		// 详情
		openDetail(params) {
			this.$root.eventHandle.$emit('creatTab', {
				name: '优惠审核详情',
				params: params,
				component: () => import('@components/discount_audit/detail')
			})
		}
	},
	mounted() {
		// this.getList()
	}
}
</script>
<style scoped>
     .red {
        background: red !important;
    }
    .yellow{
		background-color: yellow !important;
	}
    .green {
        background: green !important;
    }
</style>
<style module>
  .check-fail,.check-fail td{
    background-color: #f99 !important;
  }
</style>
