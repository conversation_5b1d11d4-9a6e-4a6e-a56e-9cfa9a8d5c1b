<template>
  <div class="xpt-flex">
    <el-row class='xpt-top' :gutter='40'>
      <el-col :span="14">
<!-- 2020.4.22业务原因注释        <el-button type='success' size='mini' @click="addTab" :disabled="isOperational||submitStatus">新增</el-button>-->
<!-- 2020.4.22业务原因注释        <el-button type='info' size='mini' @click="pre_addNews" :disabled="isOperational||submitStatus|| isExhibition">增行</el-button>-->
        <el-button type='primary' size='mini' @click="save()"
          :disabled="isOperational||submitStatus||onSave" :loading='onSave'>保存</el-button>
        <el-button size='mini' type='success' @click='submit'
          :disabled="submitStatus||onSubmit" :loading='onSubmit'>提交</el-button>
        <el-button size='mini' type='danger' @click="clickOpe('/order-web/api/sampletrade/revoke?permissionCode=SAMPLE_REGIS_WITHDRAW','onRevoke')"
          :disabled='onRevoke' :loading='onRevoke'>撤回</el-button>
        <el-button type='warning' size='mini' @click="clickOpe('/order-web/api/sampletrade/reject?permissionCode=SAMPLE_REGIS_REJECT', 'onReject')"
          :disabled="isOperational||onReject" :loading='onReject'>驳回</el-button>
        <el-button type='primary' size='mini' @click="clickOpe('/order-web/api/sampletrade/audit?permissionCode=SAMPLE_REGIS_AUDIT', 'onAudit')"
          :disabled="isOperational||onAudit" :loading='onAudit'>审核</el-button>
        <el-button type='danger' size='mini' @click="del" :disabled="!isRevoke && ( isOperational||submitStatus || isSaved )">删行</el-button>
        <el-button type='success' size='mini' @click="expOut">导出</el-button>
        <el-button type='primary' size='mini' @click="expList">报表列表</el-button>
        <el-button size='mini' type='success' @click="clickOpe('/order-web/api/sampletrade/effect?permissionCode=SAMPLE_AUDIT_SPECIALIST', 'onEffect')"
          :disabled='onEffect' :loading='onEffect'>生效</el-button>
        <el-button size='mini' type='danger' @click="clickOpe('/order-web/api/sampletrade/cancel?permissionCode=SAMPLE_REGIS_INVALID', 'onCancel')"
          :disabled='onCancel' :loading='onCancel'>失效</el-button>
      </el-col>
      <el-col :span="10">

      </el-col>
    </el-row>
    <el-row :gutter='40'>
      <el-col :span='8'>
        <el-form label-position="right" label-width="110px">
          <el-form-item label='单据号：'>{{simpleData.sample_no}}</el-form-item>
          <el-form-item label='单据状态：'>{{status_options[simpleData.document_status]}}</el-form-item>
        </el-form>
      </el-col>
      <el-col :span='8'>
        <el-form label-position='right' label-width='110px'>
          <el-form-item label='制单人：'>{{simpleData.creator_name}}</el-form-item>
          <el-form-item label='制单时间：'>{{simpleData.create_time_str}}</el-form-item>
        </el-form>
      </el-col>
      <el-col :span='8'>
        <el-form label-position='right' label-width='110px'>
          <el-form-item label='审核人：'>{{simpleData.auditor_name}}</el-form-item>
          <el-form-item label='审核时间：'>{{simpleData.audit_time_str}}</el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div class="xpt-flex__bottom" id='bottom'>
      <xpt-headbar v-if="!isExhibition">
        <xpt-search-ex :page="searchData.page_name" :click="search" slot='right' style="margin-right: 50px"/>
      </xpt-headbar>
      <xpt-list
        :data='simplelist'
        :btns='[]'
        :showHead="false"
        :colData='cols'
        :pageTotal='count'
        orderNo
        :searchPage='searchData.page_name'
        @search-click='search'
        @selection-change='handleSelectionChange'
        @row-click='rowClick'
        @page-size-change='sizeChange'
        @current-page-change='pageChange'
      >
        <template slot='sample_number' slot-scope='scope'>
          <p v-tooltip='scope.row.sample_number' v-if='isOperational||submitStatus||isExhibition'>{{scope.row.sample_number}}</p>
          <el-input size='mini' icon='search' @blur='queryMaterial(scope.row)' :readonly='true' disabled :on-icon-click='pre_selectGoods' v-model='scope.row.sample_number' style='width: 100%' v-else></el-input>
        </template>
        <template slot='sample_batch' slot-scope='scope'>
          <p v-tooltip='scope.row.sample_batch' v-if='isOperational||submitStatus||isExhibition'>{{scope.row.sample_batch}}</p>
          <el-input size='mini' @blur='limit_batch(scope.row.sample_batch)' v-model='scope.row.sample_batch' style='width: 100%' v-else></el-input>
        </template>
        <template slot='shop_name' slot-scope='scope'>
          <p v-tooltip='scope.row.shop_name' v-if='isOperational||submitStatus||isExhibition'>{{scope.row.shop_name}}</p>
          <el-input size='mini' icon='search' :on-icon-click='pre_selectHall' v-model='scope.row.shop_name' disabled style='width: 100%' v-else></el-input>
        </template>
        <template slot='stand_price' slot-scope='scope'>
          <p v-tooltip='scope.row.stand_price' v-if='isOperational||submitStatus||isExhibition'>{{scope.row.stand_price}}</p>
          <el-input size='mini' v-model='scope.row.stand_price' style='width: 100%' disabled v-else></el-input>
        </template>
        <template slot='sample_price' slot-scope='scope'>
          <p v-tooltip='scope.row.sample_price' v-if='isOperational||submitStatus'>{{scope.row.sample_price}}</p>
          <el-input size='mini' v-model='scope.row.sample_price' style='width: 100%' v-else></el-input>
        </template>
        <template slot='sample_reason' slot-scope='scope'>
<!--          <p v-tooltip='scope.row.sample_reason' v-if='isOperational||submitStatus || isExhibition'>{{scope.row.sample_reason}}</p>-->
<!--          <el-input size='mini' v-model='scope.row.sample_reason' style='width: 100%' v-else></el-input>-->
          <el-select
            size="mini"
            style="width:100px"
            v-model="scope.row.sample_reason"
            :disabled="isOperational||submitStatus"
            placeholder="请选择">
            <el-option
              v-for="item in reasonOption"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </template>
      </xpt-list>
    </div>
  </div>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
  props:['params'],
  data(){
    var self =this
    return{
      onSave: false,
      onSubmit: false,
      onReject: false,
      onRevoke: false,
      onAudit: false,
      onEffect: false,
      onCancel: false,

      /*searchData : {
        sample_id:'',
        page_no:1,
        page_size:self.pageSize
      },*/
      count:0,

      //通用组件的查询
      searchData:{
        sample_id:'',
        page_no:1,
        page_size:self.pageSize,
        page_name: 'scm_sample_order',
        where: []
      },

      //表头数据
      simpleData:{
        sample_no:'',
        document_status:'',
        create_name:'',
        create_time_str:'',
        auditor_name:'',
        audit_time_str:''
      },
      deleteOrderVOs:[],//删除的清样数据
      simplelist:[],//表体数据
      selectData:{},//选择数据
      selectRows: [],
      saveData:{},
      status_options:{
        'SUBMITTED':'提交审核',
        'CREATE':'创建',
        'WITHDRAWED':'已撤回',
        'APPROVED':'已审核',
        'RETRIAL':'重新审核',
        'CANCELED':'已失效'
      },

      cols: [
        {
          label: '清样商品编码',
          slot: 'sample_number',
          width: 180
        }, {
          label: '清样商品名称',
          prop: 'sample_name',
          width: 100
        }, {
          label: '清样商品规格',
          prop: 'sample_specifications',
          width: 130
        }, {
          label: '登记日期',
          prop: 'record_date',
          format: 'dataFormat'
        }, {
          label: '店铺名称',
          slot: 'shop_name',
          width: 120
        }, {
          label: '标准价',
          slot: 'stand_price',
        }, {
          label: '清样价格',
          slot: 'sample_price',
        }, {
          label: '行状态',
          prop: 'status',
          width: 80,
          formatter(val) {
            switch(val) {
              case 'SUBMITTED': return '提交审核';
              case 'CREATE': return '创建';
              case 'WITHDRAWED': return '已撤回';
              case 'APPROVED': return '已审核';
              case 'RETRIAL': return '重新审核';
              case 'CANCELED': return'已失效';
              case 'EXHIBITING': return'摆场订单';
              default: return val;
            }
          }
        }, {
          label: '清样状态',
          prop: 'sample_status',
          width: 80,
          formatter(val) {
            switch(val) {
              case 'Y': return '已清样';
              case 'N': return '未清样';
              default: return val;
            }
          }
        }, {
          label: '折扣',
          prop: 'sample_discount',
          width: 50,
          formatter: val => val ? Number((val * 100).toFixed(10)) + '%' : ''
        },
        {
          label: '清样发起时间',
          prop: 'sample_launch_date_str',
          width: 110
        },
        {
          label: '摆场商品子编号',
          prop: 'exhibit_child_no',
          width: 140
        },
        {
          label: '清样原因',
          slot: 'sample_reason',
          width: 110
        },
        {
          label: '父级物料编码',
          prop: 'source_material_no',
          width: '120'
        },
        {
          label: '父级物料名称',
          prop: 'source_material_name',
          width: '120'
        },
      ],
      reasonOption: [ //清样原因下拉框
        {
          label: '商品停产',
          value: 'STOP_PRODUCTION'
        },
        {
          label: '商品升级',
          value: 'UPGRADE_PROMOTION'
        },
        {
          label: '公司要求',
          value: 'COMPANY_REQUIRED'
        },
        {
          label: '门店要求',
          value: 'SHOP_SALESROOM'
        },
      ]
    }
  },
  methods:{
    addTab(){
      let self = this
			self.$root.eventHandle.$emit('creatTab', {
        name: "新增清样商品登记表",
        params: {},
        component: () => import('@components/domesticSales/detail')
      })
    },
    pre_addNews(){
      if(!this.isBlank()) this.addNews()
    },
    addNews(){
      var self = this
      var temp={
        sample_number:'',
        sample_batch:'',
        sample_name:'',
        sample_specifications:'',
        record_date_str:'',
        shop_name:'',
        stand_price:'',
        sample_price:'',
        status:'',
        sample_detail_id: '',
        exhibit_child_no: '',
        sample_reason: ''
      }
      var len = self.simplelist.length,
        idSet = new Set();
      while(len--) {
        idSet.add(self.simplelist[len].sample_detail_id)
      }
      temp.sample_detail_id = 'temp' + (+new Date());
      let hasId = idSet.has(temp.sample_detail_id);
      while(hasId) {
        temp.sample_detail_id += 1;
        hasId = idSet.has(temp.sample_detail_id);
      }
      self.simplelist.push(temp)
    },
    search(obj, resolve) {
      this.searchData.where = obj
      this.getBodyData(resolve)
    },

    sizeChange(size){
      this.searchData.page_size = size
      this.getBodyData()
    },
    pageChange(page){
      this.searchData.page_no = page
      this.getBodyData()
    },
    save(resolve,reject){
      var self = this,bool=true
      if(this.simplelist.length==0) {
        this.$message.error('表体数据为空!')
        return
      }
      this.setSave()
      if(this.validateData()) return
      // 商品行不能为空
      if(!this.saveData.sampleOrderVOs.length) {
        this.$message.error('明细行的商品编码必填；');
        return;
      }
      this.onSave = true;
      var submitData = JSON.parse(JSON.stringify(self.saveData));
      if(this.deleteOrderVOs.length){
        submitData.deleteOrderVOs = this.deleteOrderVOs;
      }
      this.ajax.postStream('/order-web/api/sampletrade/save?permissionCode=SAMPLE_REGIS_SAVE',/*self.saveData*/submitData,d=>{
        if(d.body.result){
          self.searchData.sample_id=d.body.content
          self.refresh()
          resolve&&resolve(d.body.content)
          self.$message.success('保存成功')
          self.$root.eventHandle.$emit('refresh_domestic')
        }
        else {
          self.$message.error(d.body.msg)
          // self.addNews()
        }
        this.onSave = false;
      }, err => {
        this.$message.error(err);
        this.onSave = false;
      })
    },
    /*
    这里应该过滤掉自动添加行的空数据
    根据source_material_id有没有值来判断是否为空数据
    by:zZ
    */
    validateData(){
      var self =this,bool=false
      for(var v of this.simplelist){
        if(!v.source_material_id){
          continue;
        }
        if(!v.sample_price){
          bool = true
          break
        }
        if(!v.shop_id){
          bool = true
          break
        } if (!v.sample_reason) {
          bool = true
          break
        }
      }
      if(bool) self.$message.error('存在行商品、店铺、清样价或清样原因为空的数据!')
      return bool
    },
    clickOpe(url, onBtn){ //点击操作
      var self = this
      if(!self.searchData.sample_id){
        self.$message.error('当前页面为空白页!')
        return
      }
      if(this.validateData()) return
      this[onBtn] = true;
      this.ajax.postStream(url, self.searchData.sample_id, d => {
        if(d.body.result) {
          self.refresh()
          self.$message.success('操作成功！')
        } else {
          self.$message.error(d.body.msg || '')
        }
        this[onBtn] = false;
      }, err => {
        this.$message.error(err);
        this[onBtn] = false;
      })
    },
    expOut(){
      var self = this
      if(!self.searchData.sample_id){
        this.$message.error('空白页无法导出!')
        return
      }
      /*if(Fn.isEmptyObject(this.selectData)) {
        this.$message.error('请选择数据!')
        return
      }*/
      var data={
        export_sample_detail_ids:self.export_sample_detail_ids,
        sample_id:self.searchData.sample_id,
        if_export:'Y'
      }
      new Promise((resolve,reject)=>{
        self.ajax.postStream('/order-web/api/sampletrade/export?permissionCode=SAMPLE_REGIS_EXPORT',data,d=>{
          if(d.body.result) resolve(d.body)
          else reject(d.body.msg)
        })
      }).then(v => {
          self.$message.success(v.msg)
          this.expList();
      }).catch(e => self.$message.error(e))
    },
    expList(){
      this.$root.eventHandle.$emit('creatTab',{
        name:"报表列表",
        params:{
          url: "/order-web/api/invoice/export/result/list?permissionCode=SAMPLE_REGIS_EXPORT_RESULT"
        },
        component:() => import('@components/common/exout')
      })
    },
    getHeadData(resolve){
      var self = this
      this.ajax.postStream('/order-web/api/sampletrade/get',self.searchData.sample_id,d=>{
        if(d.body.result) {
          self.simpleData = d.body.content;
          //20201022更改测试数据
          // if(self.simpleData.sample_no.slice(5)%2){
          //   self.simpleData.document_status="WITHDRAWED"
          // }
          resolve && resolve();
        } else {
          self.$message.error(d.body.msg)
        }
      })
    },
    getBodyData(callback){
      var self = this
      this.ajax.postStream('/order-web/api/sampletrade/detail/list',self.searchData,d=>{
        if(d.body.result && d.body.content) {
          self.simplelist = d.body.content.list
          if(self.params && self.params.sampleRegisterList) {
            let list = []
            if (self.params && self.params.simplelist) {
              // list = self.params.simplelist
              // self.simplelist = self.params.simplelist
              let simpleChildNoList = []
              self.simplelist.forEach(item => {
                simpleChildNoList.push(item.exhibit_child_no)
							})
							self.params.simplelist.forEach(item => {
								if (!(new Set(simpleChildNoList).has(item.exhibit_child_no))) {
									self.simplelist.push(item)
								}
							})
							list = JSON.parse(JSON.stringify(self.simplelist))
            } else {
              list = d.body.content.list
            }
            let paramsList = self.params.sampleRegisterList, childNoList = []
            list.forEach(item => {
              childNoList.push(item.exhibit_child_no)
            })
            paramsList.forEach(item => {
              if (!(new Set(childNoList).has(item.exhibit_child_no))) {
              let itemObj = {
                sample_number:item.material_no,
                sample_batch:item.sample_batch_no,
                sample_name:item.material_name,
                sample_specifications:item.material_specification,
                stand_price:item.price.toFixed(2),
                sample_price : 0.00,
                record_date_str:'',
                shop_name:item.shop_name,
                shop_id:item.shop_id,
                source_material_id: item.source_material_id,
                status: item.status,
                exhibit_child_no: item.exhibit_child_no,
                source_material_no: item.group_material_no,
                source_material_name: item.group_material_name,
                sample_reason: item.sample_reason,
                sample_detail_id: '',
                material_id: item.material_id
              }
              self.simplelist.push(itemObj)
            }
            let tabList = self.$parent.$parent.$parent.openTabs, activeIdx = '', activeTab = {}
            tabList.forEach((tab, idx) => {
              if (tab && tab.title == '清样详情' && tab.params.sample_id == self.params.sample_id) {
                activeTab = JSON.parse(JSON.stringify(tab))
                activeIdx = idx
              }
            })
            if (!(JSON.stringify(activeTab) === '{}')) {
              let activeTab = self.$parent.$parent.$parent.openTabs[activeIdx].params
              if (activeTab && activeTab.simplelist) {
                self.$parent.$parent.$parent.openTabs[activeIdx].params.simplelist = self.simplelist
              } else {
                self.$parent.$parent.$parent.openTabs[activeIdx].params.simplelist = self.simplelist
              }
            }
          })
        }
        self.count=d.body.content.count
        for(let v of self.simplelist){
          if(typeof(v.stand_price) === 'number') {
            v.stand_price = v.stand_price.toFixed(2)
          }
          if(typeof(v.sample_price) === 'number') {
            v.sample_price = v.sample_price.toFixed(2)
          }
        }
          /*
          refresh_addNews方法初始化数据时，
          获取数据，执行reslove会添加一行空数据，执行reject不会添加，
          需要根据单据状态来判断
          创建、撤回、驳回状态下可以编辑，其它状态不可以编辑
          应该在getHeadData方法下判断是否要添加一行新行！
          by:zZ
          */
        }
        else self.$message.error(d.body.msg)
          callback && callback();
      })
    },
    getPrice(){
      let self = this,
        data = {
          material_ids: [this.selectData.material_id],
          shop_id: this.selectData.shop_id,
          singleShotDate: +new Date()
        }
      if(!data.shop_id) return;
      if(0 == data.material_ids.length) return;
      new Promise((resolve,reject)=>{
        self.ajax.postStream('/price-web/api/price/order/getSamplePrice', data, d => {
          if(d.body.result) resolve(d.body.content)
          else reject(d.body.msg)
        })
      }).then(v=>{
        if(v=='') {
          self.$message.error('标准价获取失败,物料编码'+this.selectData.sample_number+'没有设置标准价格表!')
          self.selectData.stand_price = '';
          self.selectData.default_price = '';
          self.selectData.price_list_detail_id = '';
          self.selectData.price_list_id = '';
          return
        }
        self.selectData.stand_price = v[0].price.toFixed(2);
        self.selectData.default_price = v[0].price.toFixed(2);
        self.selectData.price_list_detail_id = v[0].price_list_detail_id;
        self.selectData.price_list_id = v[0].price_list_id;
      }).catch(e => {
        self.selectData.stand_price = '';
        self.selectData.default_price = '';
        self.selectData.price_list_detail_id = '';
        self.selectData.price_list_id = '';
        self.$message.error(e)
      })
    },
    refresh(){
      if(!this.searchData.sample_id) return
        this.deleteOrderVOs = [];
        new Promise((resolve) => {
          this.getHeadData(resolve)
        }).then(v => {
          this.getBodyData()
        })
    },
    handleSelectionChange(val){
      // console.log('查看清样产品');
      this.selectRows = val;
    },
    del(){
      //清样删除商品
      if(!this.selectRows.length) {
        this.$message.error('请选择数据!')
        return
      }
      let i = this.selectRows.length,
      idSet = new Set()
      while(i--) {
        idSet.add(this.selectRows[i].sample_detail_id)
      }
      i = this.simplelist.length;
      //this.deleteOrderVOs = Array.from(idSet);
      while(i--) {
        if(idSet.has(this.simplelist[i].sample_detail_id)) {
          let sample_detail_id = this.simplelist[i].sample_detail_id;
          console.log(sample_detail_id,'sample_detail_idsample_detail_idsample_detail_idsample_detail_id');
          // if(sample_detail_id.indexOf('temp') == -1){
          //   this.deleteOrderVOs.push(sample_detail_id);
          // }
           if(String(sample_detail_id).indexOf('temp') == -1){
            this.deleteOrderVOs.push(sample_detail_id);
          }
          this.simplelist.splice(i, 1);
        }
      }
    },
    // 批次只能录入大于0小于100的整数
    limit_batch(e) {
      if(this.selectData.sample_batch && !/^[1-9]\d?$/.test(this.selectData.sample_batch + '')) {
        this.$message.error('清样商品批次只能录入大于0小于100的整数');
        this.selectData.sample_batch = ''
      }
    },
    pre_selectGoods(){
      var self =this
      setTimeout(function(){
        self.selectGoods()
      }, 10)
    },
    selectGoods(){//选择商品
      let params = {}
			let self = this
			params.callback = d=>{
        if(!d) return
        self.selectData.sample_number = d.materialNumber;
        self.selectData.material_id = d.materialId;
        self.selectData.source_material_id = d.sourceMaterialId;
        self.selectData.sample_specifications = d.materialSpecification;
        self.selectData.sample_name = d.materialName;
        self.selectData.record_date = +new Date();
        self.getPrice();
        self.pre_addNews()
			}
			self.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/order/selectGoodsList'),
				style:'width:800px;height:500px',
				title:'清样商品列表'
			})
    },
    pre_selectHall(){
      var self =this
      setTimeout(function(){
        self.selectHall()
      }, 10)
    },
    selectHall(){
      let self = this,params={
        // shop_type: 'O2O',
        selection: 'radio'
      };
      params.callback = d=>{
        if(!d) return
        self.selectData.shop_id = d.shop_id;
        self.selectData.shop_name = d.shop_name;
        self.getPrice();
			}
      self.$root.eventHandle.$emit('alert',{
        params:params,
        component:()=>import('@components/shop/list'),
        style:'width:800px;height:500px',
        title:'店铺列表'
      })
    },
    rowClick(row) {
      // console.log('row',row);
      this.selectData = row;
    },
    //判断是否存在空数
    isBlank(){
      var bool=false
      for(let v of this.simplelist){
        if(!v.material_id) {
          bool=true
          break
        }
      }
      return bool
    },
    setSave(){
      var self =this,
        sampleOrderVOs = [];
      let simplelist = JSON.parse(JSON.stringify(this.simplelist));
      for(let v of simplelist){
        // 过滤掉没有商品ID的数据，默认没有商品ID的数据为空数据
        if(!v.source_material_id) continue;
        if(v.default_price === v.stand_price) {
          delete v.default_price
        } else {
          delete v.default_price
          delete v.price_list_detail_id
          delete v.price_list_id
        }
        if(-1 < (v.sample_detail_id + '').indexOf('temp')) {
          delete v.sample_detail_id;
        }
        sampleOrderVOs.push(v);
      }
      this.saveData={
        sample_id:self.simpleData.sample_id||'',
        sample_no:self.simpleData.sample_no||'',
        document_status:self.simpleData.document_status||'',
        creator:self.simpleData.creator||'',
        creator_name:self.simpleData.creator_name||'',
        create_time:self.simpleData.create_time||'',
        create_time_str:self.simpleData.create_time_str||'',
        auditor:self.simpleData.auditor||'',
        auditor_name:self.simpleData.auditor_name||'',
        audit_time:self.simpleData.audit_time||'',
        audit_time_str:self.simpleData.audit_time_str||'',
        // sampleOrderVOs:self.simplelist
        sampleOrderVOs: sampleOrderVOs
      }
    },
    submit() {
      new Promise((resolve,reject)=>{
        this.save(resolve,reject)
      }).then(v=>{
        this.clickOpe('/order-web/api/sampletrade/submit?permissionCode=SAMPLE_REGIS_SUBMIT', 'onSubmit')
      })
    },
    queryMaterial(row) {
      if(!row.sample_number) return;
      this.ajax.get('/material-web/api/material/getTradeMaterialByNumber/' + row.sample_number.trim(), res => {
        if(res.body.result && res.body.content) {
          let data = res.body.content
          row.sample_name = data.materialName;
          row.sample_specifications = data.materialSpecification;
          // row.stand_price =
        } else {
          row.sample_name = '';
          row.sample_specifications = '';
          row.sample_number = '';
          this.$message.error(res.body.msg || '根据物料编码查询物料信息失败');
        }
      })
    },
    getExhibition(){
      let data = {
        material_ids: [this.selectData.material_id],
        shop_id: this.selectData.shop_id,
        singleShotDate: +new Date()
      }
      this.$axios('post',
          '/price-web/api/price/order/getSamplePrice',
          params
        ).then(res => {
          // console.log(res);
        }).catch(err => {
          this.$message.error(err)
        })
    },
    // 从摆场信息列表的清样登记按钮跳转过来初始化行数据
    getInitRow() {
      let temp = [];
      this.params.list.forEach(item=>{
        temp.push(item.id)
      });
      this.$request('/order-web/api/scmexhibitionmaterial/toSampleRegister',
        {
          export_ids: temp
        }).then(res => {
        if (res.result) {
          let data = res.content.list
          this.simplelist = []
          // 循环添加数据
          for (let i=0; i<res.content.list.length; i++ ) {
            let temp={
              sample_number:data[i].material_no,
              sample_batch:data[i].sample_batch_no,
              sample_name:data[i].material_name,
              sample_specifications:data[i].material_specification,
              stand_price:data[i].price.toFixed(2),
              sample_price : 0.00,
              record_date_str:'',
              shop_name:data[i].shop_name,
              shop_id:data[i].shop_id,
              source_material_id: data[i].source_material_id,
              status: data[i].status,
              exhibit_child_no: data[i].exhibit_child_no,
              source_material_no: data[i].group_material_no,
              source_material_name: data[i].group_material_name,
              sample_reason: data[i].sample_reason,
              sample_detail_id: '',
              material_id: data[i].material_id
            };
            var len = this.simplelist.length,
              idSet = new Set();
            while(len--) {
              idSet.add(this.simplelist[len].sample_detail_id)
            }
            temp.sample_detail_id = 'temp' + (+new Date());
            let hasId = idSet.has(temp.sample_detail_id);
            while(hasId) {
              temp.sample_detail_id += 1;
              hasId = idSet.has(temp.sample_detail_id);
            }
            this.simplelist.push(temp)
            this.count = this.simplelist.length
          }
        } else {
          this.$message.error(res.msg)
        }
      }).catch(err => {
      }).finally(() => {
      })
    },
  },
  computed:{
    isRevoke(){
      let doc=new Set(['WITHDRAWED'])
      return doc.has(this.simpleData.document_status)
    },
    isOperational(){
      let doc=new Set(['APPROVED','CANCELED'])
      return doc.has(this.simpleData.document_status)
    },
    submitStatus(){
      let doc=new Set(['SUBMITTED'])
      return doc.has(this.simpleData.document_status)
    },
    isExhibition() {
      return this.params.fromWhere === 'exhibition_material'
    },
    isSaved() {
      return !!this.simpleData.sample_no;
    }
  },
  mounted:function(){
    var self=this
    this.searchData.sample_id = this.params.sample_id
    this.refresh()
    // 这里执行addNews最好区分开来，编辑的时候要根据订单状态去处理
    // if(!this.params.sample_id) {
    //   this.addNews()
    // }
  },
  created() {
    // 来自摆场商品信息列表带来的行数据
    if (this.params.fromWhere === 'exhibition_material') {
      this.isExhibition = true;
      this.getInitRow()
    }
  }
}
</script>
<style lang="stylus">
input[type=number] {
  -moz-appearance:textfield;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.image-upload
		position: relative
		cursor: pointer
		overflow: hidden
		display: inline-block
		*display: inline
		*zoom: 1
		input
			position: absolute
			font-size: 100px
			right: 0
			top: 0
			height:100%
			opacity: 0
			cursor: pointer
</style>
