<!-- 清样商品登记列表 -->
<template>
  <xpt-list
    selection=''
    :btns='btns'
    :data='dataList'
    :colData='cols'
    :pageTotal='count'
    :searchPage='searchData.page_name'
    @search-click='search'
    @page-size-change='sizeChange'
    @current-page-change='pageChange'
    ref='list'>
      <el-button type="success" size='mini' class='image-upload' :loading="isUp" slot='btns'>
        导入<input type="file" id='files' multiple="multiple" @change='changeFile' ></el-button>
      <el-button type='primary' size='mini' @click="expList" slot='btns'>报表列表</el-button>
    </xpt-list>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
  props:['data','params'],
  data(){
    let self = this
    return{
      dataList:[],
      searchData:{
        page_no:1,
        page_size:self.pageSize,
        page_name: 'scm_sample_order',
        where: []
      },
      count:1,
      btns: [
        // {
        //   type: 'success',
        //   txt: '新增',
        //   click() {
        //     self.addNews()
        //   }
        // },
        {
          type: 'primary',
          txt: '刷新',
          click() {
            self.getList()
          }
        }
      ],
      cols: [
        {
          label: '单据号',
          prop: 'sample_no',
          redirectClick(d) {
            self.viewDetail(d.sample_id)
          },
          width: 120
        }, {
          label: '清样商品编码',
          width: 123,
          prop: 'sample_number'
        }, {
          label: '清样商品名称',
          width: 100,
          prop: 'sample_name'
        }, {
          label: '清样商品规格',
          width: 100,
          prop: 'sample_specifications'
        }, {
          label: '店铺名称',
          prop: 'shop_name',
          width: 110
        },{
          label: '登记日期',
          prop: 'record_date',
          format: 'dataFormat',
          formatParams: 'yyyy-MM-dd hh:mm:ss',
          width: '140'
        }, {
          label: '标准价',
          prop: 'stand_price',
          format: 'priceFilter',
          width: '100'
        }, {
          label: '清样价格',
          prop: 'sample_price',
          format: 'priceFilter',
          width: '100'
        }, {
          label: '单据状态',
          prop: 'status',
          width: 100,
          formatter(val) {
            switch(val) {
              case 'SUBMITTED': return '提交审核';
              case 'CREATE': return '创建';
              case 'WITHDRAWED': return '已撤回';
              case 'APPROVED': return '已审核';
              case 'RETRIAL': return '重新审核';
              case 'CANCELED': return '已失效';
              default: return val;
            }
          }
        }, {
          label: '清样状态',
          prop: 'sample_status',
          formatter(val) {
            switch(val) {
              case 'Y': return '已清样';
              case 'N': return '未清样';
              default: return val;
            }
          }
        }, {
          label: '摆场商品子编号',
          prop: 'exhibit_child_no',
          width: 140,
        }, {
          label: '清样原因',
          prop: 'sample_reason',
          formatter(val) {
            switch(val) {
              case 'STOP_PRODUCTION': return '商品停产';
              case 'UPGRADE_PROMOTION': return '商品升级';
              case 'COMPANY_REQUIRED': return '公司要求';
              case 'SHOP_SALESROOM': return '门店要求';
              default: return val;
            }
          }
        },{
          label: '折扣',
          prop: 'sample_discount',
          formatter: val => val ? Number((val * 100).toFixed(10)) + '%' : ''
        }, {
          label: '清样发起时间',
          prop: 'sample_launch_date_str',
          width: '140'
        }
      ],
      isUp: false,
    }
  },
  methods:{
    addNews(){
      var params = {sample_id:''}
      this.$root.eventHandle.$emit('creatTab',{
        name:'清样详情',params:params,
        component:() => import('@components/domesticSales/detail.vue')
      })
    },
    sizeChange(size){
      this.searchData.page_size = size
      this.getList()
    },
    pageChange(page){
      this.searchData.page_no = page
      this.getList()
    },
    getList(res){
      var self = this
      new Promise((resolve,reject)=>{
        self.ajax.postStream('/order-web/api/sampletrade/list?permissionCode=SAMPLE_REGIS_QUERY',self.searchData,d=>{
          if(d.body.result) resolve(d.body.content)
          else reject(d.body.msg)
          res && res();
        }, err => {
          self.$message.error(err);
          res && res();
        })
      }).then(v=>{
        self.dataList = v.list
        self.count = v.count
      }).catch(e=>{
        self.dataList = []
        self.count = 0
        console.log(e)
        this.$message.error(e)
      })
    },
    viewDetail(id){
      var self = this
      var params={sample_id:id}
      this.$root.eventHandle.$emit('creatTab',{
        name:'清样详情',params:params,component: () => import('@components/domesticSales/detail.vue')
      })
    },
    search(obj, resolve) {
      this.searchData.where = obj
      this.getList(resolve)
    },
    changeFile() {
      var files = document.getElementById('files').files,
        len = files.length,
        fr = new FileReader(),
        self = this;
      fr.readAsDataURL(files[0])
      fr.onload = e => {
        self.upload(files,0)
      }
    },
    // 提交至服务器
    upload(files,index) {
      this.isUp=true
      console.log('看看传的参');
      var formData = new FormData();
      formData.append('file', files[index]);
      this.ajax.post('/app-web/app/file/uploadFile.do', formData, (s) => {
        if(s.body.success && s.body.data) {

          //let bodyContent = s.body.content;

          this.ajax.postStream('/order-web/api/sampletrade/import?permissionCode=SAMPLE_REGIS_IMPORT', s.body.data/*bodyContent*/, d => {
            this.$message({
              type: d.body.result ? 'success' : 'error',
              message: d.body.msg || ''
            })
            this.isUp = false;
          }, err => {
            this.$message.error(err);
            this.isUp = false;
          })
        } else {
          this.$message.error(s.body.msg || '');
          this.isUp = false;
        }
      }, e => {
        this.$message.error(e);
        this.isUp = false;
      })
      document.getElementById('files').value= null
    },
    expList(){
      this.$root.eventHandle.$emit('creatTab',{
        name:"报表列表",
        params:{
          url: "/order-web/api/invoice/export/result/list?permissionCode=SAMPLE_REGIS_EXPORT_RESULT"
        },
        component:() => import('@components/common/exout')
      })
    },
  },
  mounted:function(){
    var self = this
    this.getList()
    self.$root.eventHandle.$on('refresh_domestic',d=>{
      self.getList()
    })
  }
}
</script>
<style lang="stylus">
.image-upload
    position: relative
    cursor: pointer
    overflow: hidden
    display: inline-block
    *display: inline
    *zoom: 1
    input
      position: absolute
      font-size: 100px
      right: 0
      top: 0
      height:100%
      opacity: 0
      cursor: pointer
</style>
