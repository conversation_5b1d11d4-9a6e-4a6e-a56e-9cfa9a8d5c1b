<!-- 抖音供销单对账报表 -->
<template>
  <xpt-list ref="list" :data="list" :btns="btns" :colData="cols" :selection="selection" :pageTotal="count"
    :searchPage="search.page_name" @search-click="presearch" @selection-change="handleSelectionChange"
    @cell-click="handleCellClick" @page-size-change="sizeChange" @current-page-change="pageChange"></xpt-list>
</template>
<script>
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      search: {
        page_name: "dealer_supply",
        where: [],
        page_size: self.pageSize,
        page_no: 1,
        if_need_page: "Y",
      },
      list: [],
      selection: "checkbox",
      count: 0,
      multipleSelection: [], //列表选择索引
      currentCellSelect: {}, //选择的当前单元格
      btns: [
        {
          type: "success",
          txt: "刷新",
          click() {
            self.refresh();
          },
          loading: false,
        },
        {
          type: "success",
          txt: "导出",
          disable() {
            return false
          },
          click: self.exportList
        }, {
          type: "success",
          txt: "报表导出文件下载",
          click: self.showExportList
        }
      ],
      cols: [
        {
          label: "订单店铺名称",
          prop: "shopName",
          width: 150,
        },
        {
          label: "经销商客户",
          prop: "customerSourceName",
          width: 150,
        },
        {
          label: "拍单时间",
          prop: "created",
          format: "dataFormat1",
          width: 140,
        },
        {
          label: "平台单号",
          prop: "tid",
          width: 150,
        },
        {
          label: "买家昵称",
          prop: "customerName",
          width: 270,
        },
        {
          label: "供货单号",
          prop: "supplyOrderNo",
          width: 175,
        },
        {
          label: "合并单号",
          prop: "mergeTradeNo",
          width: 180,
        },
        {
          label: "批次单号",
          prop: "batchTradeNo",
          width: 200,
        },
        {
          label: "批次分单号",
          prop: "outboundNoticeNo",
          width: 210,
        },
        {
          label: "发货时间",
          prop: "zdDeliveryTime",
          format: "dataFormat1",
          width: 140,
        },
        {
          label: "出库时间",
          prop: "outStockTime",
          format: "dataFormat1",
          width: 140,
        },
        {
          label: "订单完成时间",
          prop: "endTime",
          format: "dataFormat1",
          width: 140,
        },
        {
          label: "平台结算时间",
          prop: "incomeTime",
          format: "dataFormat1",
          width: 140,
        },
        {
          label: "商品编码",
          prop: "productCode",
          width: 140,
        },
        {
          label: "销售利润分类",
          prop: "salesProfitClass",
          format: 'auxFormat',
					formatParams: 'sale_profit_class',
          width: 120,
        },
        {
          label: "物料编码",
          prop: "materiaNumber",
          width: 140,
        },
        {
          label: "物料名称",
          prop: "materialName",
          width: 140,
        },
        {
          label: "规格描述",
          prop: "materialSpecification",
          width: 200,
        },
        {
          label: '商品行状态',
          prop: 'status',
          width: 100,
          format: 'auxFormat',
          formatParams: 'SYS_ORDER_STATUS'
        },
        {
          label: "订单来源",
          prop: "orderSource",
          width: 140,
          format: 'auxFormat',
          formatParams: 'SCM_ORDER_SOURCE'
        },
        {
          label: "是否扣预付款",
          prop: "ifPrepayment",
          width: 140,
          format: 'yesOrNo',
        },
        {
          label: "实际售价",
          prop: "actPrice",
        },
        {
          label: "三包费",
          prop: "threeGuaranteesFee",
        },
        {
          label: "运费",
          prop: "postFee",
        },
        {
          label: "平台供货价",
          prop: "dealerPrice",
        },
        {
          label: "NBO经销结算价",
          prop: "settlementPrice",
          width: 100,
        },
        {
          label: "收款金额",
          prop: "payAmount",
        },
      ],
    };
  },
  methods: {
    presearch(list, resolve) {
      this.search.where = list;
      this.searching(resolve);
    },
    searching(resolve) {
      let self = this;
      this.btns[0].loading = true;
      this.ajax.postStream(
        "/dealer-web/api/dealerSupply/list",
        this.search,
        (res) => {
          if (res.body.result) {
            self.count = res.body.content.count;
            if (res.body.content.list) {
              self.list = res.body.content.list;
            }
            self.$message.success(res.body.msg);
          } else {
            self.list = [];
            self.count = 0;
            self.$message.error(res.body.msg);
          }
          self.btns[0].loading = false;
          "function" === typeof resolve ? resolve() : this;
        },
        (err) => {
          self.$message.error(err);
          "function" === typeof resolve ? resolve() : this;
          self.btns[0].loading = false;
        }
      );
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log(this.multipleSelection);
    },
    //点击行
    handleCellClick(row) {
      console.log("行的回调函数：", row);
      this.currentCellSelect = row;
    },
    sizeChange(size) {
      // 第页数改变
      this.search.page_size = size;
      this.searching();
    },
    pageChange(page_no) {
      // 页数改变
      this.search.page_no = page_no;
      this.searching();
    },
    refresh() {
      this.searching();
    },
    exportList() {
      let url = "/dealer-web/api/dealerSupply/export";
      let postData = Object.assign({}, this.search);
      delete postData.page_size
      delete postData.page_no
      delete postData.if_need_page
      this.ajax.postStream(url, postData, res => {
        if (res.body.result) {
          this.$message.success(res.body.msg);
        } else {
          this.$message.error(res.body.msg);
        }
      });
    },
    showExportList() {
      this.$root.eventHandle.$emit('alert', {
        component: () => import('@components/after_sales_report/export'),
        style: 'width:900px;height:600px',
        title: '报表导出列表',
        params: {
          query: {
            type: 'EXCEL_TYPE_DEALER_SUPPLY_EXPORT',
          },
        },
      })
    },
  },
  mounted() {
    this.searching();
  },
  destroyed() { },
};
</script>
<style lang="css" scoped></style>
