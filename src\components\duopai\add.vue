
<!-- 多拍绩效清单详情-->
<template>
<div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
		<el-col :span="24">
			<el-button 
				:type='btn.type' 
				size='mini' 
				v-for='(btn, index) in topBtns' 
				:key='"good" + index'
				:disabled=" typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled|| false "
				:loading="btn.loading||false" 
				@click='btn.click'
			>{{btn.txt}}</el-button>
		</el-col>
	</el-row>
	<div>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基本信息" name="duopai">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="duopai" :rules="rules" ref="duopai">
						<el-col :span='6'>
							<el-form-item label="单据编码："  >
								<el-input v-model="duopai.code"  size='mini' prop="code"  disabled></el-input>
							</el-form-item>
							<el-form-item label="创建人："  >
								<el-input v-model="duopai.creator_name" prop="creator_name" disabled  size='mini' ></el-input>
							</el-form-item>
							<el-form-item label="创建时间："  >
								<el-date-picker v-model="duopai.create_time" type="date" placeholder="选择日期" size='mini' :disabled='true' :editable='false' :picker-options='enableDateOptions'></el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span='6'>
							<el-form-item label="生效状态："  >
								<el-input v-model="duopai.deletedName" prop="deletedName" disabled  size='mini' ></el-input>
							</el-form-item>
							<el-form-item label="审核状态"  >
								<el-input v-model="duopai.statuName" prop="statusName" disabled  size='mini' ></el-input>
							</el-form-item>
							<el-form-item label="多拍生效时间"  prop="enable_time">
								<el-date-picker v-model="duopai.enable_time" type="datetime" placeholder="选择日期" size='mini' :editable='duopai.status === "CREATE"? false : true'  :disabled='duopai.status === "CREATE"? false : true' :picker-options='enableDateOptions'></el-date-picker>
								<el-tooltip v-if='rules.enable_time[0].isShow' class="item" effect="dark" :content="rules.enable_time[0].message" placement="right-start" popper-class='xpt-form__error'>
                 					  <i class='el-icon-warning'></i>
                 				 </el-tooltip>
							</el-form-item>
							<el-form-item label="多拍失效时间"  prop="disable_time" >
								<el-date-picker v-model="duopai.disable_time" type="datetime" placeholder="选择日期" size='mini' :editable='duopai.status === "CREATE"? false : true' :disabled='duopai.status === "CREATE"? false : true' :picker-options='disableDateOptions'></el-date-picker>
								<el-tooltip v-if='rules.disable_time[0].isShow' class="item" effect="dark" :content="rules.disable_time[0].message" placement="right-start" popper-class='xpt-form__error'>
                 					  <i class='el-icon-warning'></i>
                 				 </el-tooltip>
							</el-form-item>
						</el-col>
						<el-col :span='6'>
							<el-form-item label="订单店铺：" prop="shop_id" >
           						<!-- <xpt-input size='mini' v-if="(/M1/).test(bill_type_id_tag)" v-model="duopai.shop_name" disabled></xpt-input> -->
           						<xpt-input size='mini' v-if="(/M1/).test(bill_type_id_tag) || !(duopai.status === 'CREATE')" v-model="duopai.shop_name" disabled></xpt-input>
								<xpt-input size='mini' v-else v-model="duopai.shop_name" readonly icon='search' :on-icon-click="selectShop" @change='shopChange'></xpt-input>
								<el-tooltip v-if='rules.shop_id[0].isShow' class="item" effect="dark" :content="rules.shop_id[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
          					</el-form-item>
          					<el-form-item label="审核人："  >
								<el-input v-model="duopai.audit_name" prop="audit_name" disabled size='mini' ></el-input>
							</el-form-item>
							<el-form-item label="审核时间"  >
								<el-date-picker v-model="duopai.audit_time" type="date" placeholder="选择日期" size='mini' :editable='false' :disabled='true'></el-date-picker>
							</el-form-item>
						</el-col>
			    	</el-form>
			 	 </el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
    <el-row class="xpt-flex__bottom" v-fold>
			<el-tabs v-model="secondTab">
				<el-tab-pane label="多拍清单" name="duopaiInfo" class='xpt-flex'>
					<!-- <el-row class="xpt-top" :gutter="40">
						<el-col :span='18'>
							<el-button 
								:type='btn.type' 
								size='mini' 
								v-for='(btn, index) in bottomBtns' 
								:key='"good" + index'
								:disabled=" typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled|| false "
								:loading="btn.loading||false" 
								@click='btn.click'
							>{{btn.txt}}</el-button>
						</el-col>
						<div style="width: 488px; position: absolute; right: 0">
							<el-select size='mini' v-model='materiaListInfoData.materiaQuery.col' @change='colChange' style='width: 120px'>
								<el-option v-for='(item,index) in searchCols' :key='index' :label='item.label' :value='item.prop'></el-option>
							</el-select>
							<el-select size='mini' v-model='materiaListInfoData.materiaQuery.ope' style='width: 80px'>
								<el-option v-for='(item, index) in opes' :key='index' :label='item.label' :value='item.key' :disabled='item.disabled'></el-option>
							</el-select>
							<el-date-picker v-if='materiaListInfoData.isShowDate' size='mini' v-model='materiaListInfoData.materiaQuery.value' type='datetime' :editable='false' format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
							<el-select v-else-if='materiaListInfoData.isshowSelect' size='mini' v-model='materiaListInfoData.materiaQuery.value'>
								<el-option v-for='item in selectOtp' :key="item.code" :label="item.name"  :value="item.code"></el-option>
							</el-select>
							<el-input size='mini' v-model='materiaListInfoData.materiaQuery.value' v-else ></el-input>
							<el-button size='mini' type='success' @click='materiaFilterData'>过滤</el-button>
							<el-button size='mini' type='primary' @click='materiaResert'>重置</el-button>
						</div>
					</el-row> -->
					<xpt-list 
                        id="duopai_goodList"
						ref='materiaList'
						:data='materiaListInfoData.materiaListData' 
						:colData='materiaListInfoData.materiaListCols'
                        :btns='materiaListInfoData.materiaListBtns'
						:orderNo= true
                        :searchPage='materiaListQuery.page_name'
                        @search-click='serchMaterial'
						:selectable='selectableFun'
						@selection-change='langeRadioChange'
                        :pageTotal='materiaListInfoData.materiaListCount'
                        @page-size-change='materiaListPageSizeChange'
                        @current-page-change='materiaListPageChange'
				>
					<template slot='if_dp' slot-scope='scope'>
						<el-select placeholder="请选择" v-model="scope.row.if_dp" :disabled="false" size='mini' style="width:100%;">
							<el-option
								v-for="value in (materialDbList || [])"
								:key="value.code"
								:label="value.label"
								:value="value.code">
							</el-option>
							<el-option key="true" label="是" value='true'></el-option>
						</el-select>
					</template>
                    </xpt-list>
				</el-tab-pane>
            </el-tabs>
		</el-row>
</div>
</template>
<script>
	import validate from '@common/validate.js';
	export default {
		props: ['params'], //上游参数
		data() {
			var self = this;//本vue
			return{
				firstTab:"duopai",
				secondTab: 'duopaiInfo',
				bill_type_id_tag: '',
				duopai:{
					code: '系统自动生成',
					deletedName: '',
					shop_id: '',
					shop_name: '',
					enable_time: '',
					disable_time: '',
					status: 'CREATE'
				},//表单内容
				rules:{
					shop_id:validate.isNotBlank({
						required:true,
						self: self
					}),
					// extend_one : validate.isNotBlank({
					// 	required:true,
					// 	self:self
					// }),
					enable_time: [{
						required:true,
						message:'请选择开始日期',
						isShow:false,
						validator: function(rule,value,callback){
						// 数据校验
						if(value){
							self.rules[rule.field][0].isShow = false;
							// 校验成功
							callback();
						}else{
							self.rules[rule.field][0].isShow = true
							// 校验失败
							callback(new Error(''));
						}
					} 
					}],
					disable_time: [{
						required:true,
						message:'请选择开始日期',
						isShow:false,
						validator: function(rule,value,callback){
						// 数据校验
						if(value){
							self.rules[rule.field][0].isShow = false;
							// 校验成功
							callback();
						}else{
							self.rules[rule.field][0].isShow = true
							// 校验失败
							callback(new Error(''));
						}
					} 
					}],
				},
				topBtns: [
					{ 
						type: 'primary', 
						txt: '刷新', 
						disabled () {
                            return self.params.id ? false : true
                        },
						click () {
                            self.init()
                        } 
					}, { 
						type: 'primary', 
						txt: '新增', 
						disabled: false, 
						click: self.addNew 
					}, { 
						type: 'success', 
						txt: '保存', 
                        disabled () {
                            return false
                        },
						click () {
							self.save()
						}
					}, { 
						type: 'warning', 
						txt: '生效', 
						disabled() { //当表头生效状态为失效的时候按钮置亮起
							return self.duopai.deleted === '2' ? false : true
						},
						click () {
							self.operatorDuopai('enable')
						}
					}, { 
						type: 'warning', 
						txt: '失效', 
						disabled() {
							return self.duopai.deleted === '1' ? false : true
						},
						click () {
							self.operatorDuopai('disable')
						}
					}, { 
						type: 'danger', 
						txt: '删除', 
						disabled() { // 未审核的单据才可执行整单删除
							return self.duopai.status === 'CREATE' ? false : true
						}, 
						click () {
							self.operatorDuopai('delete')
						}
					}, { 
						type: 'warning', 
						txt: '审核', 
						disabled() {
							return self.duopai.status === 'CREATE' ? false : true
						},
						click () {
							self.operatorDuopai('approve')
						}
					}
				],
				materiaListInfoData: {
					materiaListData: [],
					materiaListCols: [
						{
							label: '单据行id',
							prop: 'id',
							width: 110
						}, {
							label: '物料ID',
							prop: 'material_id'
						}, {
							label: '物料编码',
							prop: 'material_number',
							width: 150,
							bool: true,
							iconClick(row) {
								setTimeout(self.materiaChoose, 10)
							},
							
							disabled(row) {
								return row.id ? true : false
							},
							blur: self.materiaCopy
						}, {
							label: '物料名称',
							prop: 'material_name'
						}, {
							label: '物料描述',
							prop: 'material_spec',
							width: 150
						}, {
							label: '多拍生效时间',
							prop: 'enable_time',
							width: 150,
							bool: true,
							elDate: true,
							date: 'datetime',
							pickerOptions: {
								disabledDate(time) {
									if (self.duopai.enable_time) {
										return time.getTime() < new Date(self.duopai.enable_time).getTime() || time.getTime() > new Date(self.duopai.disable_time).getTime()
									} else {
										return time.getTime() < Date.now() - 8.64e7
									}
								}
							},
							disabled(row) {
								return self.materiaCanEdit(row) || (self.duopai.deleted === '2' ? true : false)
							},
							change(row) {
								// if(self.materiaCanEdit(row)) { 
								// 	console.log('goods date change',row)
								// 	// self.materiaChooseDate(row);
								// }
							},
						}, {
							label: '多拍失效时间',
							prop: 'disable_time',
							width: 150,
							bool: true,
							elDate: true,
							date: 'datetime',
							pickerOptions: {
								disabledDate(time) {
									if (self.duopai.disable_time) {
										return time.getTime() > new Date(self.duopai.disable_time).getTime() || time.getTime() < new Date(self.duopai.enable_time).getTime() - 8.64e7
									} else {
										return time.getTime() < Date.now() - 8.64e7
									}
								}
							},
							disabled(row) {
								return self.materiaCanEdit(row) || (self.duopai.deleted === '2' ? true : false)
							},
							change(row) {},
						}, 
						{
							label: '是否多拍商品',
							prop: 'if_dp',
							width: 90,
							bool: true,
							// isThis: true,
							isSelect: true,
							disabled(row) {
								return self.materiaCanEdit(row)
							},
							obj: {
								'Y': '是',
								'N': '否',
							}
						}, {
							label: '行状态',
							prop: 'status',
							formatter (val) {
								switch (val) {
									case '2': return "失效" ; break;
									case '1': return "生效"; break;
								}
							}
						}, {
							label: '多拍商品行状态',
							prop: 'deleted',
							width: 100,
							formatter (val) {
								switch (val) {
									case '0': return "" ; break;
									case '2': return "失效" ; break;
									case '1': return "生效"; break;
								}
							}
						}, /*{
							label: 'sku_id',
							prop: 'sku_id',
							bool: true,
							elInput: true,
							disabled(row) {
								return self.materiaCanEdit(row)
							},
							change(row) {},
                            blur: (row, e) => {},
                            width: 140
						},*/ {
							label: '商品ID',
							prop: 'goods_id',
							width: 100
						}, {
							label: '多拍绩效系数',
							prop: 'duopai_performance_num',
							width: 90,
							bool: true,
							elInput: true,
							disabled(row) {
								return self.materiaCanEdit(row)
							},
							change(row) {
								self.checkPerformance(row)
							},
							blur: (row, e) => {}
						}, {
							label: '创建人',
							prop: 'creator_name'
						}, {
							label: '创建时间',
							prop: 'create_time',
							format: 'dataFormat1',
							width: 130
						}, {
							label: '修改人',
							prop: 'modifier_name'
						}, {
							label: '修改时间',
							prop: 'modify_time',
							format: 'dataFormat1',
							width: 130,
						}
					],
					materiaQuery: {
						col: 'material_number',
						value: '',
						ope: 1,
					},
					isShowDate: false,
                    isshowSelect:false,
                    materiaListCount: 0,
                    materiaListBtns: [
                        { 
                            type: 'primary', 
                            txt: '新增', 
                            disabled(){
                                return self.duopai.status === 'AUDIT' ? true : false
                            },
                            // click: self.materiasAdd 
                            click: self.materiaChoose
                        }, { 
                            type: 'warning', 
                            txt: '保护清单失效', 
                            disabled() { //当表头生效状态为失效的时候按钮置灰
                                return true
                            },
                            click: self.setProtectDisabe
                        }, { 
                            type: 'warning', 
                            txt: '多拍清单失效', 
                            disabled() {
                                return true
                            },
                            click: self.setItemDisabled 
                        }, 
                        // { 
                        // 	type: 'primary', 
                        // 	txt: '多拍生效时间修改', 
                        // 	disabled() {
                        // 		return false
                        // 	},
                        // 	click: self.changeItemTime 
                        // },
                        { 
                            type: 'danger', 
                            txt: '删除', 
                            disabled() { // 未审核的单据才可执行整单删除
                                return self.duopai.status === 'AUDIT' ? true : false
                            },
                            click: self.deleteMaterial
                        }
                    ],
				},
				materiaListOld: [], // 旧的数据做对比
				materiaSelect: null,
                // 设置生效时间从00:00:00开始
                enableDateOptions:{
                    /*生效时间允许选所有值*/
                    disabledDate(time){
                        return self.duopai.disable_time ? time.getTime() > new Date(self.duopai.disable_time) : false;
                    },
                    date:(function(){
                        var date = new Date();
                        var year = date.getFullYear();
                        var month = date.getMonth()+1;
                        var day = date.getDate();
                        var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
                        return new Date(time);
                    })()
                },
                // 设置结束时间为23:59:59
                disableDateOptions:{
                    disabledDate: time => {
                        /*新增时能选所有时间*/
                        return self.duopai.enable_time ? time.getTime() < new Date(self.duopai.enable_time) : false
                    },
                    date:(function(){
                        var date = new Date();
                        var year = date.getFullYear();
                        var month = date.getMonth()+1;
                        var day = date.getDate();
                        var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
                        return new Date(time);
                    })()
                },
				selectOtp:__AUX.get('discountCategory'),
				otherParams: {
					ifDuopai: true
				},
				oldShopId: null,
                oldShopName: null,
                materiaListQuery: {
                    where: [],
                    page_size: 50,
                    page_no: 1,
                    if_need_page:'Y',
                    page_name: 'scm_duopai_detail'
                }
			}
		},
		methods:{
            materiaListPageSizeChange (size) {
                this.materiaSelect = []
                this.materiaListQuery.page_size = size
                this.getMaterialList()
            },
            serchMaterial(where,reslove) {
                this.materiaListQuery.where = where
                this.getMaterialList(reslove)
            },
            materiaListPageChange(page){
                this.materiaSelect = []
                this.materiaListQuery.page_no = page
                this.getMaterialList()
            },
			save(formName){
				var self =  this;
				let saveData = JSON.parse(JSON.stringify(self.duopai));
				saveData['disable_time']=='' ?   saveData['disable_time']='' : saveData['disable_time']= +new Date(saveData.disable_time);
				saveData['enable_time']=='' ?   saveData['enable_time']='' : saveData['enable_time']= +new Date(saveData.enable_time);
				let materiaSaveData = this.materiaSaveData(), ifRequired = this.checkGoodListRequired()
				if (!ifRequired) {
					return 
				}
				let data = {
					disable_time: saveData.disable_time,
					enable_time: saveData.enable_time,
					shop_id: saveData.shop_id,
					items: materiaSaveData.add
				}
				this.duopai.id ? data.id = this.duopai.id : ''
				if (this.materiaListInfoData.materiaListData && (this.materiaListInfoData.materiaListData.length <= 0)) {
				// if (data.items && (data.items.length <= 0)) {
					this.$message.error('请至少添加一行多拍明细')
					return
				}
				self.$refs.duopai.validate((valid) => {
					if(!valid) return
					this.ajax.postStream('/order-web/api/duopai/saveDuopait',data,function(response){
						if(response.body.result) {
							self.$message.success(response.body.msg)
                            self.duopai.id = response.body.content.id
                            let params = {
                                id:response.body.content.id
                            }
                            params.__close = function(){
                                self.$root.eventHandle.$emit('removeTab',self.params.tabName);
                            }
                            self.$root.eventHandle.$emit('updateTab',{
                                name:self.params.tabName,
                                params:params,
                                title:'多拍清单详情',
                                component: () => import('@components/duopai/add.vue')
                            })
							self.init();
						} else {
							self.$message.error(response.body.msg || '')
						}
					});
					
				})
				
			},
			// 获取商品信息数据变更
			materiaSaveData() {
				let materiaOld = this.materiaListOld,
					materiaNow = this.materiaListInfoData.materiaListData,
					materiaAdd = [],
					materiaUpd = [],
					i;
				// 获取新增的和修改的物料
				i = materiaNow.length;
				
				while(i--) {
					if(materiaNow[i].id) { // 保存后修改数据
						// 获取更新行数据，status过滤掉取消状态的数据,换货和取消的
						let j = materiaOld.length;
						while(j--) {
							if(materiaNow[i].id === materiaOld[j].id) {
								let newObj = {
									material_id: materiaNow[i].material_id,
									enable_time: materiaNow[i].enable_time,
									disable_time: materiaNow[i].disable_time,
									if_dp: materiaNow[i].if_dp,
									material_number: materiaNow[i].material_number,
									status: materiaNow[i].status,
									deleted: materiaNow[i].deleted,
									// sku_id: materiaNow[i].sku_id,
									duopai_performance_num: materiaNow[i].duopai_performance_num,
									goods_id: materiaNow[i].goods_id
								}
								let oldObj = {
									material_id: materiaOld[j].material_id,
									enable_time: materiaOld[j].enable_time,
									disable_time: materiaOld[j].disable_time,
									if_dp: materiaOld[j].if_dp,
									material_number: materiaOld[j].material_number,
									status: materiaOld[j].status,
									deleted: materiaOld[j].deleted,
									// sku_id: materiaOld[i].sku_id,
									duopai_performance_num: materiaOld[i].duopai_performance_num,
									goods_id: materiaOld[i].goods_id
								}
								let isUpd = this.compareData(oldObj, newObj)
								if(isUpd) {
									materiaAdd.push({
										id: materiaNow[i].id,
										material_id: materiaNow[i].material_id,
										enable_time: materiaNow[i].enable_time,
										disable_time: materiaNow[i].disable_time,
										if_dp: materiaNow[i].if_dp,
										material_number: materiaNow[i].material_number,
										status: materiaNow[i].status,
										deleted: materiaNow[i].deleted,
										// sku_id: String(materiaNow[i].sku_id),
										duopai_performance_num: parseFloat(materiaNow[i].duopai_performance_num),
										goods_id: materiaNow[i].goods_id
									})
								}
							}
						}
					}
					// 需要过滤掉没有物料信息的数据
					if( materiaNow[i].tempId && materiaNow[i].material_id) { // 新增数据
						let addGoods = {
							material_id: materiaNow[i].material_id,
							enable_time: materiaNow[i].enable_time === '' ? '' : new Date(materiaNow[i].enable_time),
							disable_time: materiaNow[i].disable_time,
							if_dp: materiaNow[i].if_dp,
							material_number: materiaNow[i].material_number,
							// sku_id: String(materiaNow[i].sku_id),
                            duopai_performance_num: parseFloat(materiaNow[i].duopai_performance_num),
							deleted: materiaNow[i].deleted,
							goods_id: materiaNow[i].goods_id
						}
						materiaAdd.unshift(addGoods)
					}
				}
				return {
					add: materiaAdd,
					upd: materiaUpd
				}
			},
			//获取详情信息
			getInfo(resolve){
				let id = this.params.id ? this.params.id : this.duopai.id, self = this
				if(id){
					var url = "/order-web/api/duopai/getDuopaiById";
					this.ajax.postStream(url,id,function(response){
						if(response.body.result && response.body.content) {
							self.duopai = response.body.content || {};
							self.oldShopId = self.duopai.shop_id
							self.oldShopName = self.duopai.shop_name
							self.duopai.deletedName = self.duopai.deleted === '0' ? '' : (self.duopai.deleted === '1' ? '生效' : '失效')
							self.duopai.statuName = self.duopai.status === 'CREATE' ? '创建' : (self.duopai.status === 'AUDIT' ? '已审核' : '作废')
							resolve && resolve();
						} else {
							self.$message.error(response.body.msg || '');
						}
                    });
				}
            },
            // 获取多拍物料信息
            getMaterialList (resolve) {
                let id = this.params.id ? this.params.id : this.duopai.id, self = this
				if(id){
                    let params = Object.assign({}, self.materiaListQuery)
                    params.duoPaiId = id
					this.ajax.postStream('/order-web/api/duopai/getDuopaiItemById', params,function(response){
						if(response.body.result && response.body.content) {
                            let list = response.body.content.list || []
                            if (list.length > 0) {
                                list.forEach(item => {
                                    return item.duopai_performance_num = item.duopai_performance_num.toFixed(2)
                                })
                            }
                            self.materiaListInfoData.materiaListCount = response.body.content.count
                            self.materiaListInfoData.materiaListData = list
                            self.materiaListOld = JSON.parse(JSON.stringify(self.materiaListInfoData.materiaListData))
                            resolve&&resolve()
						} else {
							self.$message.error(response.body.msg || '');
						}
					});
				}
            },
			// 选择店铺
			shopChange(val) {
      			if(!val) {
         		  this.duopai.shop_id = '';
        		  this.duopai.shop_name = '';
				}
       		},
       		selectShop(){
				var self = this;
				let materialList = self.materiaListInfoData.materiaListData, ifMaterialKey = null
				ifMaterialKey = self.materiaListInfoData.materiaListData.some( material => {
					return material.id
				})
				var params = {
					selection: 'radio',
					shop_status: 'OPEN',
					callback(d){ 
						if (d.shop_id == self.oldShopId) { 
							self.duopai.shop_id = d.shop_id
							self.duopai.shop_name =  d.shop_name
						} else {
							if (ifMaterialKey) {
								self.opendialog(d)
							} else {
								self.materiaListInfoData.materiaListData = []
								self.duopai.shop_id = d.shop_id
								self.duopai.shop_name =  d.shop_name
							}
						}
					}
				}
				self.$root.eventHandle.$emit('alert',{
					params:params,
					component:()=>import('@components/shop/list'),
						style:'width:800px;height:500px',
						title:'店铺列表'
				})
			},
			opendialog (d) {
				let self = this
				if (this.duopai.id) {
					this.$root.eventHandle.$emit('openDialog', {
						ok() {
							self.duopai.shop_id = d.shop_id
							self.duopai.shop_name =  d.shop_name
							self.oldShopId = d.shop_id
							self.oldShopName = d.shop_name
							self.confirmDelete(d)
						},
						okTxt: '是',
						no() {
							self.duopai.shop_id = self.oldShopId
							self.duopai.shop_name =  self.oldShopName
						},
						noTxt: '否',
						txt: '更换店铺则会清除现有店铺下的多拍清单，是否更换？',
						noCancelBtn: true
					})
				} else {
					this.materiaListInfoData.materiaListData = []
					self.duopai.shop_id = d.shop_id
					self.duopai.shop_name =  d.shop_name
				}
			},
			confirmDelete (d) {
				let self = this
				this.ajax.postStream('/order-web/api/duopai/clearDuopaiDetail', {id: this.duopai.id, shop_id: d.shop_id},response => {
					if(response.body.result) {
						self.$message.success(response.body.msg || '')
						self.init()
					} else {
						self.$message.error(response.body.msg || '')
					}
				}, err => {
					self.$message.error(err)
				})
			},
			addNew () {
				this.$root.eventHandle.$emit('creatTab', {name:'新增多拍清单', component:()=>import('@components/duopai/add.vue')})
			},
			// 生效/失效/审核/删除操作
			operatorDuopai (type) {
				if (!type) return
				let urlobj = {
					delete: '/order-web/api/duopai/deleteScmDuopai',
					enable: '/order-web/api/duopai/validScmDuopai',
					disable: '/order-web/api/duopai/invalidScmDuopai',
					approve: '/order-web/api/duopai/auditDuopait',
				}, self = this
				this.ajax.postStream(urlobj[type], {id: this.duopai.id},response => {
					if(response.body.result) {
						self.$message.success(response.body.msg || '')
						if (type === 'delete') { 
							self.$root.eventHandle.$emit('removeTab', self.params.tabName)
						} else { 
							self.init()
						}
					} else {
						self.$message.error(response.body.msg || '')
					}
				}, err => {
					self.$message.error(err)
				})
			},
			// 选中的物料
			langeRadioChange (data) {
				this.materiaSelect = data
			},
			materiasAdd () {
				let obj = {
					tempId: new Date(),
					material_number: '',
					material_id: '',
					material_spec: '',
					material_name: '',
					enable_time: '',
					disable_time: '',
					if_dp: ''
				}
				this.materiaListInfoData.materiaListData.push(obj)
			},
			/*
			选择商品物料
			*/
			materiaChoose() {
				if (!this.duopai.shop_id) {
					this.$message.error('请选择店铺')
					return
				}
				let materiaList = []
				this.materiaListInfoData.materiaListData.forEach(item => {
					materiaList.push(item.material_number)
				})
				new Promise((resolve) => {
					setTimeout(resolve, 10)
				}).then(() => {
					let params = {
						list: materiaList,
						origin_shop_id: this.duopai.shop_id,
						duopai_id: this.duopai.id,
					},
					self = this;
					params.callback = t => {
						if(t) {
							t.forEach(d => {
								self.checkMaterialNumber(d,data =>{
									let obj = {
										tempId: new Date(),
										material_number: d.material_number,
										material_id: d.material_id,
										material_spec: d.material_specification,
										material_name: d.material_name,
										enable_time: self.duopai.enable_time,
										disable_time: self.duopai.disable_time,
										if_dp: '',
										// sku_id: d.sku_id,
										duopai_performance_num: '',
										goods_id: d.goods_id
									}
									self.materiaListInfoData.materiaListData.push(obj)
									// 滚动到最底部
                                    let scrollObj = document.querySelector('#duopai_goodList').querySelector('.el-table__body-wrapper')
                                    this.$nextTick(function() {
                                        scrollObj.scrollTop = scrollObj.scrollHeight
                                    })
								});
							})
						}
					}
					self.$root.eventHandle.$emit('alert', {
						params: params,
						component: () => import('@components/duopai/selectGoodsList'),
						style: 'width:800px;height:500px',
						title: '商品列表'
					});
				})
			},
			materiaCopy(row) {
				let self = this;
				let num = row.material_number;
				if(!num.trim()) return;
				this.checkMaterialNumber({materialNumber:num.trim()},function(){
					// 普通订单
					// alert(1)
					self.ajax.get('/material-web/api/material/getTradeMaterialByNumber/' + num.trim(), res => {
						if(res.body.result) {
							let data = res.body.content
							if(data) {
								// 取财务中台物料ID
								row.material_number = data.materialNumber
								row.material_id = data.sourceMaterialId
								row.material_spec = data.materialSpecification
								row.material_name = data.materialName
								return
							}
							self.$message.error('没有查询到该物料信息')
							row.material_number = ''
							row.material_id = ''
							row.material_spec = ''
							row.material_name = ''
						} else {
							self.$message.error(res.body.msg);
							row.material_number = ''
							row.material_id = ''
							row.material_spec = ''
							row.material_name = ''
						}
					})
				})
			},
			checkMaterialNumber(data,callback){
				let self = this;
				this.ajax.postStream('/order-web/api/mergetrade/checkMaterialStatus', [data.material_number], d => {
					if(d.body.result) {
						callback&&callback(d.body.result);
					} else {
						self.init()
						this.$message.error(d.body.msg);
					}
				}, err => {
					self.init()
				})
			},
			// 物料是否可选
			selectableFun(row){
				if(row.status === '2') return false;
				return true;
			},
			/**物料行是否可操作
			 * 物料行行状态为失效则不可操作
			 * 物料行在表头不等于失效则可修改物料的生失效时间
			 */
			materiaCanEdit(row) {
				if(!row) return false
				let canEdit = false
				if (row.status === '2') {
					canEdit = true
                }
                if (this.duopai.status === 'AUDIT') {
                    canEdit = true
                }
				return canEdit;
            },
			// 选择承诺发货日期
			materiaChooseDate(row) {
				new Promise((resolve, reject) => {
					this.goodsValidate(row, resolve, reject);
				}).then((no) => {
					// this.goodsSelect.commit_time = row.commit_time;
					// 承诺发货时间变更，对应发运明细行的承诺发货时间变更
					if(row.entry_id) {
						this.shipmentUpdateDate({
							entry_id: row.entry_id,
							address_id: row.address_id,
							commit_time: row.commit_time,
              dxpected_delivery_time: row.dxpected_delivery_time
						})
					}
				}).catch(() => {
					row.commit_time = '';
				})
			},
			changeItemTime () {},
			// 删除该多拍单
			deleteMaterial () {
				let idList = [], self = this, ifAdd = null
				ifAdd = this.materiaSelect.some(materia => {
					return materia.id
				})
				this.materiaSelect.forEach( (good, idx) => {
					if (good.id) {
						idList.push(good.id)
					} else if (good.tempId){
                        let list = self.materiaListInfoData.materiaListData
                        let len = list.length
                        while(len--) {
                            if (list[len].material_number === good.material_number && list[len].tempId === good.tempId) {
                                self.materiaListInfoData.materiaListData.splice(len, 1)
                            }
                        }
					}
				})
				if (!ifAdd) return
				this.ajax.postStream('/order-web/api/duopai/deleteDuopaiDetail', {duoPaiId: this.duopai.id, ids: idList},response => {
                    self.init()
					if(response.body.result) {
						self.$message.success(response.body.msg || '')
					} else {
						self.$message.error(response.body.msg || '')
					}
				}, err => {
                    self.init()
					self.$message.error(err)
				})
			},
			// 校验商品行是否都填写
			checkGoodListRequired (saveCb) {
				let msg = '', idsList = []
				// this.materiaListOld.forEach( item => {
				// 	idsList.push(item.sku_id)
				// })
				this.materiaListInfoData.materiaListData.some((obj, index) => {
					if(!obj.material_number){
						msg = '物料编码不能为空'
					}else if(!obj.enable_time){
						msg = '多拍生效时间不能为空'
					}else if (this.duopai.enable_time && obj.enable_time < this.duopai.enable_time) {
						msg = '物料的多拍生效时间不能小于表头多拍生效时间'
					}else if(!obj.disable_time){
						msg = '多拍失效时间不能为空'
					}else if (this.duopai.disable_time && obj.disable_time > this.duopai.disable_time) {
						msg = '物料的多拍失效时间不能大于表头多拍失效时间'
					}else if(!obj.if_dp){
						msg = '是否多拍商品不能为空'
					}/*else if(!obj.sku_id){
						msg = 'sku_id不能为空'
					}else if(obj.tempId && new Set(idsList).has(obj.sku_id)){ // 只针对新增的物料做检验
						msg = '物料编码的sku_id重复'
					}*/else if(!obj.duopai_performance_num){
						msg = '多拍绩效系数不能为空'
					}else if (!/^([0-5]+)(.\d{0,2})?$/.test(obj.duopai_performance_num) || obj.duopai_performance_num < 0 || obj.duopai_performance_num > 5 ) {
						msg = '多拍绩效系数请填写0.00~5.00之内的数字，小数点后只能填写两位'
					}
					// if (obj.tempId && obj.sku_id) {
					// 	idsList.push(obj.sku_id) 
					// }
								
					if(msg) {
						msg = '多拍明细-第' + (index + 1) + '行-' + msg
						return true
					}
				})
				if(msg){
					this.$message.error(msg)
					// throw new Error(msg);
					return false
				} else {
					return true
				}
			},
			/* 检测多拍订单失效/保护订单失效按钮是否可用
				行状态 多拍商品状态
				生效   生效
				生效   失效
			*/
			watchMateriaSelect () {
				// 若所勾选的订单行状态都是生效则
				if (this.materiaSelect && this.materiaSelect.length > 0) {
					this.materiaListInfoData.materiaListBtns[1].disabled = false
					this.materiaListInfoData.materiaListBtns[2].disabled = false
				} else {
					this.materiaListInfoData.materiaListBtns[1].disabled = true
					this.materiaListInfoData.materiaListBtns[2].disabled = true
				}
			},
			// 保护清单生失效
			setProtectDisabe () {
				let self = this, ifNewAdd = null, ifAllDisable = null
				ifNewAdd = self.materiaSelect.filter(item => {
					return item.tempId
				})
				ifAllDisable = self.materiaSelect.filter(item => {
					return item.status === '2'
				})
				if (ifNewAdd.length > 0) {
					this.$message.error('新增行不可操作保护清单失效')
					return
				}
				if (ifAllDisable.length > 0) {
					this.$message.error('请至少选择一行是否多拍商品为是且行状态为生效的行')
					return
				}
				this.$root.eventHandle.$emit('openDialog', {
					ok() {
						self.materiaSelect.map(item => {
                            item.deleted = '2'
							return item.status = '2'
						})
						self.$message.success('保护清单失效成功，保存后生效')
					},
					okTxt: '是',
					no() {
						self.ifDataChange = false
					},
					noTxt: '否',
					txt: '是否确认所选行多拍保护清单失效',
					// body: '确认取消该优惠吗？',
					noCancelBtn: true
				})
			},
			setItemDisabled () {
				let self = this, ifNewAdd = null, ifAllDisable = null
				ifNewAdd = self.materiaSelect.filter(item => {
					return item.tempId
				})
				ifAllDisable = self.materiaSelect.filter(item => {
					return item.status === '2'
				})
				if (ifNewAdd.length > 0) {
					this.$message.error('新增行不可操作多拍清单失效')
					return
				}
				if (ifAllDisable.length > 0) {
					this.$message.error('请至少选择一行行状态为生效的行')
					return
				}
				this.$root.eventHandle.$emit('openDialog', {
					ok() {
						self.materiaSelect.map(item => {
							return item.deleted = '2'
						})
						self.$message.success('多拍清单失效成功，保存后生效')
					},
					okTxt: '是',
					no() {
						self.ifDataChange = false
					},
					noTxt: '否',
					txt: '是否确认所选行多拍清单失效',
					noCancelBtn: true
				})
			},
			// 检测系数合法问题
			checkPerformance (num) {
				if (!/^([0-5]+)(.\d{0,2})?$/.test(num.duopai_performance_num) || num.duopai_performance_num < 0 || num.duopai_performance_num > 5) {
					this.$message.error('请填写0.00~5.00之内的数字，小数点后只能填写两位')
					return
				}
            },
            init(){
                this.getInfo();
                this.getMaterialList()
            }
		},
		mounted: function(){
            this.init()
        },
        watch: {
            materiaSelect () {
				this.watchMateriaSelect()
			}
        }
	}
</script>
<style module>
.row-height :global(.el-form-item__content) {
	height: auto!important;
	white-space: nowrap;
}
</style>
