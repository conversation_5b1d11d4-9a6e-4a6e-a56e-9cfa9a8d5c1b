<!-- 多拍人员商品关联表 -->
<template>
	<count-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:searchPage='search.page_name'
		:pageTotal='count'
		selection="checkbox"
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
		@search-click='searchClick'
		:dynamic='true'
		countUrl="/order-web/api/duopai/goods/count?permissionCode=DUOPAI_MANAGE"
    	:showCount ='showCount'
		ref="xptList"
	>
	<xpt-import slot='btns' :taskUrl='uploadUrl' class='mgl10'></xpt-import>
	</count-list>
</template>
<script>
import countList from '@components/common/list-count'
export default {
    props:['params'],
	components: {
        countList,
      },
	data() {
		return {
			showCount:false,
			uploadUrl:'/order-web/api/duopai/goods/import?permissionCode=DUOPAI_MANAGE',
			list: [],
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: () => this.getList(),
			}, 
			{
				type: 'primary',
				txt: '查看导入结果',
				click: () => this.exportResult(),
			}, 
			
			],
			cols: [{
				label: '买家昵称',
				prop: 'buyerNick',
				width: 180
			}, {
				label: '商品ID',
				prop: 'numIid',
				width: 180
			}, {
				label: '拍单日期起',
				prop: 'startTime',
				format: 'dataFormat1',
			}, {
				label: '拍单日期止',
				prop: 'endTime',
				format: 'dataFormat1',
			}, {
				label: '多拍员工号',
				prop: 'employeeNumber',
			}, {
				label: '关联订单',
				prop: 'associatedOrder',
				redirectClick: d => this.openDetail(d.duopaiGoodsId),
			}],
			search: {
				page_no:1,
				page_size: this.pageSize,
				page_name: 'scm_duopai_goods_list',
				where: []
			},
			count: 0,
		}
	},
	methods: {
		exportResult(){
        this.$root.eventHandle.$emit("alert", {
          style: "width:900px;height:600px",
          title: "导入结果",
          params: {
            url: "/reports-web/api/reports/afterSale/findMyReportList?permissionCode=DUOPAI_MANAGE",
            data: {
              page_size: 20,
              page_no: 1,
              type: "EXCEL_TYPE_DUOPAI_GOODS_IMPORT"
            },
            showDownload: true,
          },
          component: () => import("@components/common/eximport"),
        });
      },
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			let self = this;
			this.search.where = obj;
			new Promise((res,rej)=>{
					self.getList(resolve,res);
				}).then(()=>{
					if(this.search.page_no != 1){
						self.count = 0;
					}
					self.showCount = false;
				})
			},
		
		getList(resolve,callback) {
			this.ajax.postStream("/order-web/api/duopai/goods/list?permissionCode=DUOPAI_MANAGE", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content;
					// this.count = res.body.content.count;
					if(!this.showCount){
						let total = this.search.page_no * this.search.page_size
						this.count = res.body.content.length == (this.search.page_size+1)? total+1:total;
              			this.list = res.body.content.splice(0,res.body.content.length == (this.search.page_size+1)?total:res.body.content.length);
						this.list.forEach(item=>{item.associatedOrder = '查看详情'})
					}
				} else {
					this.$message.error(res.body.msg)
				}
				callback && callback();
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				callback && callback();
				resolve && resolve()
			});
		},
		openDetail (duopaiGoodsId){
			this.$root.eventHandle.$emit('creatTab',{
				name: '多拍人员拍单关联信息',
				component: ()=>import('@components/duopai/relactions_order_list'),
				params: {
          			duopaiGoodsId,
				},
			})
    },
    
	},
	mounted() {
	},
}
</script>