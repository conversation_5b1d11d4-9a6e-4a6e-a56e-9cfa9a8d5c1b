<!-- 多拍绩效清单列表 -->   
<template>
	<div class='xpt-flex'>
		<xpt-list
			:data='tableData'
			:btns='btns'
			:colData='cols'
			:pageTotal='totalPage'
			selection=''
			:searchPage="searchObj.page_name"
  	 		 @search-click="preSearch"
			:orderNo='true'
   	 		@page-size-change='handleSizeChange'
   	 		@current-page-change='handleCurrentChange' >
            <xpt-import slot='btns' :taskUrl="uploadUrl" :otherParams='otherParams' :callback="uploadCallback" class='mgl10' :isupload="false"></xpt-import>
		</xpt-list>
	</div>
</template>
<script>
export default {
	data(){
		var self = this;
		return{
			btns: [
				{
					type: 'primary',
					txt: '新增',
					click: self.addFun
				}, {
					type: 'primary',
					txt: '刷新',
					click: self.searchFun
				},
			// 	{
			// 	type: 'primary',
			// 	txt: '导入Excel',
			// 	click: this.uploadFile
			// },{
			// 	type: 'primary',
			// 	txt: '下载模板',
			// 	click: () => {
			// 		var link = document.createElement('a')
			// 	    // link.download = name;
			// 	    link.href = location.origin + '/download/%E6%99%BA%E6%85%A7%E9%97%A8%E5%BA%97%E7%89%A9%E6%96%99%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
			// 	    link.click()
			// 	},
			// }
			],
			ifClickUpload: false,
			uploadData: {},
			cols: [
				{
					label: '编码',
					prop: 'code',
					redirectClick(row) {
						 let params = {};
          				 params.id = row.id;
            			 self.$root.eventHandle.$emit('creatTab', {
              			 	name: "多拍清单详情",
             			 	params: params,
             				 component: () => import('@components/duopai/add.vue')
         			   });
					},
					width: 135
				}, {
					label: '店铺名称',
					prop: 'shop_name',
					width:180
				}, {
					label: '单据状态',
					prop: 'statusName',
					width: 100
				}, {
					label: '审核人',
					prop: 'audit_name',
					width: 100
				},{
					label: '审核时间',
					prop: 'audit_time',
					format: 'dataFormat1',
					width: 150
				}
				,{
					label: '生效时间',
					prop: 'enable_time',
					format: 'dataFormat1',
					width: 150
				},{
					label: '失效时间',
					prop: 'disable_time',
					format: 'dataFormat1',
					width: 150
				}, {
					label: '是否失效',
					prop: 'deleted',
                    width: 80,
                    formatter(val) {
                        switch (val) {
                            case '0':
                                return '';
                                break;
                            case '1':
                                return '生效';
                                break;
                            case '2':
                                return '失效';
                                break;
                            default:
                                return val;
                                break;
                        }
                    }
				}, {
					label: '创建人',
					prop: 'creator_name',
					width: 100
				},{
					label: '创建时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 150
				}, {
					label: '最后操作人',
					prop: 'modifier_name',
					width: 100
				},{
					label: '最后操作时间',
					prop: 'modify_time',
					format: 'dataFormat1',
					width: 150
				}
			],
			searchObj:{
				page_no:1,
				page_size: self.pageSize,
                if_need_page: 'Y',
				page_name: 'scm_duopai',
				where: []
			},
			totalPage:0,
			searchInput:'',
      tableData:[],
      otherParams: {
          ifDuopai: true
      },
      uploadUrl: '/order-web/api/duopai/importScmDuopai'
		}
	},
	props:['params'],
	mounted(){
  	 	this.searchFun();
  	},
	methods:{ 
		addFun(){
			this.$root.eventHandle.$emit('creatTab', {name:'新增多拍清单', component:()=>import('@components/duopai/add.vue')});
		},
		preSearch(txt, resolve){
			this.searchObj.where = txt
			this.searchFun(null, resolve);
		},
		searchFun(event, resolve){
			let self = this;
    	    this.ajax.postStream('/order-web/api/duopai/findDuopaiPage',this.searchObj, (res) => {
        	if(res.body.result){
         	 	self.tableData = res.body.content.list;
          		self.totalPage = res.body.content.count;
          		this.$message.success(res.body.msg)
       		 }else{
          			self.$message.error(res.body.msg || '');
       		 }
       		 if (resolve) {
         		 resolve();
        	 }
     		 }, (err) => {
       		 self.$message.error(err);
       		 if (resolve) {
         		 resolve();
      		  }
     	 });
		},
		handleSelectionChange(selectArr){
			var self = this
			self.selectId=[]
			selectArr.map((v)=>{
				self.selectId.push(v.act_id)
			});
		},
		handleSizeChange(val){
			this.searchObj.page_size = val;
			this.searchFun();
		},
		handleCurrentChange(val){
			this.searchObj.page_no = val;
			this.searchFun();
		},
		uploadCallback (data){
			let self = this
			if (!data.body.result && data.body.msg) {
				if (data.body.msg.indexOf('库存为0') > -1) {
					this.$root.eventHandle.$emit('openDialog', {
						ok() {
              self.uploadAgain(data)
						},
						beforeClose() {},
						okTxt: '是',
						no() {
							
						},
						noTxt: '否',
						txt: data.body.msg,
						body: '是否继续导入？',
						noCancelBtn: true
					})
				} else {
					this.$message.error(data.body.msg)
				}
			} else {
				this.importMsg(data)
			}
            
    },
    uploadAgain (data) {
      let self = this, params = {
        fileUrl:data.body.content,
        if_valid_inventory: 'N'
      }
      this.ajax.postStream('/order-web/api/duopai/importScmDuopai', params, res => {
        if(res.body.result) {
          res.body.msg && this.$message.success(res.body.msg);
          self.importMsg(data)
        } else {
          res.body.msg && this.$message.error(res.body.msg);
        }
      }, err => {
        this.$message.error(err);
      });
    },
		importMsg (data) {
			if (data.body && data.body.result) {
				this.searchFun()
				if (data.body.content) {
						this.$root.eventHandle.$emit('creatTab', 
						{name:'多拍绩效单详情',
						params: {id: data.body.content.id}, 
						component:()=>import('@components/duopai/add.vue')
					});
				}
			}
		},
		uploadFile (){
			this.ifClickUpload = true
			this.uploadData = {
				parent_name: 'IMPORT_DUOPAI_EXCEL',
			}
			//重置，防止第二次不能触发
			setTimeout(() => {
				this.ifClickUpload = false
			}, 100)
		}
		
	}
}
</script>
