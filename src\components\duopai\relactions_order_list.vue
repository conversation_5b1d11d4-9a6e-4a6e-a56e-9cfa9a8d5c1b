<!-- 多拍人员商品关联表 -->
<template>
	<xpt-list
		:data='list'
		:btns='btns'
		:colData='cols'
		ref="xptList"
		:showHead='false'
	></xpt-list>
</template>
<script>
export default {
    props:['params'],
	
	data() {
		return {
			showCount:false,
			list: [],
			btns: [
			],
			cols: [{
				label: '买家昵称',
				prop: 'buyerNick',
				// redirectClick: d => this.openConsultation(d.consultion_id),
				width: 180
			}, {
				label: '订单号',
				prop: 'tid',
				width: 180
			}, {
				label: '订单业务员',
				prop: 'userName',
			}, {
				label: '订单支付金额',
				prop: 'payment'
			}, {
				label: '订单支付时间',
				prop: 'payTime',
				format: 'dataFormat1',
			}, ],
			search: {
				page_no:1,
				page_size: this.pageSize,
				duopaiGoodsId: this.params.duopaiGoodsId,
			},
			count: 0,
		}
	},
	methods: {
		
		
		getList() {
			this.ajax.postStream("/order-web/api/duopai/order/list?permissionCode=DUOPAI_MANAGE", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content;
					
				} else {
					this.$message.error(res.body.msg)
				}
				
			}, err => {
				this.$message.error(err);
				
			});
		},
		
    
	},
	mounted() {
		this.getList()
	},
}
</script>