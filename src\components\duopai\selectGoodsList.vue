  <!-- 多拍绩效弹窗选择物料列表 -->
<template>
	<div class='xpt-flex'>
        <xpt-list
            ref='duopaiMateriaList'
            :data='shopList'
            :btns='btns'
            :colData='cols'
            searchHolder='请输入物料编码或是物料名称进行搜索'
            :pageTotal='pageTotal'
            @search-click='searchData'
            @selection-change='selectionChange'
            @page-size-change='pageSizeChange'
            @current-page-change='pageChange'
			@row-dblclick='rowDblclick'
        ></xpt-list>
	</div>
</template>
<script>
export default {
    props: ['params'],
	data(){
		let self = this
		return {
			btns: [
                {
					type: 'primary',
					txt: '确认',
					click(){
                        self.close()
                    },
				}
            ],
			cols: [
				{
					label: '商品编码',
					prop: 'material_number',
				}, {
					label: '商品名称',
					prop: 'material_name',
				}, {
					label: '商品规格',
					prop: 'material_specification'
				}, {
					label: '店铺名称',
					prop: 'shop_name'
				}, {
					label: 'sku_id',
					prop: 'sku_id'
				}, {
					label: '商品ID',
					prop: 'goods_id',
					width: 100
				}
			],
			search:{
				page: {
					length: self.pageSize,
					pageNo: 1
                },
                searchWork: ''
			},
			shopList:[],
			selectData:'',
			pageTotal:0,
			showHead: true,
			refreshBtnStatus: false
		}
	},
	/*
	params.shop_status 店铺状态,"OPEN为生效的店铺"
	*/
	props:['params'],
	methods:{
        close () {
            let self = this
            if(self.selectData.length <= 0){
                self.$message.error("请选择数据")
                return
            }
            // 关闭弹窗
            self.params.callback(self.selectData)
            self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
        },
		selectionChange(data) {
			this.selectData = data
		},
		searchData(obj, resolve){
			this.search.searchWork = obj
			this.selectData = null
			this.getShopList(resolve)
		},
		pageSizeChange(pageSize){
			this.search.page.length = pageSize
			this.selectData = null
			this.getShopList()
		},
		pageChange(page){
			this.search.page.pageNo = page
			this.selectData = null
			this.getShopList()
		},
		getShopList(resolve){
			this.refreshBtnStatus = true

            let searchData = JSON.parse(JSON.stringify(this.search))
            let paramsData = {
                // existsMaterialCodes: this.params.list,
                shop_name: this.params.shop_name,
                duopai_id: this.params.duopai_id ? this.params.duopai_id : '',
                origin_shop_id: this.params.origin_shop_id
            }
            let postData = Object.assign({}, paramsData, searchData)
            console.log('thispost',postData, this.params)
			this.ajax.postStream('/order-web/api/duopai/showSKU', postData, d=>{
				if(d.body.result&&d.body.content){
					this.pageTotal = d.body.content.count
					this.shopList = d.body.content.list||[]
				} else {
					this.$message.error(d.body.msg || '')
				}
				this.refreshBtnStatus = false
				resolve && resolve()
			}, err => {
				resolve && resolve()
				this.$message.error(err)
				this.refreshBtnStatus = false
			})
		},
		rowDblclick(obj) {
			if(!this.params.isAlert) return
			let data = obj
			if(this.selection === 'checkbox') {
				this.selectData = this.selectData.length?this.selectData:[]
				this.selectData.push(data)
				this.selectData = Array.from(new Set(this.selectData))
				//data = [obj]
			}
		}
	},
	mounted() {
		// 过滤失效的店铺
		if(this.params.shop_status) {
			this.search.shop_status = this.params.shop_status
		}
		this.getShopList()
	}
}
</script>
