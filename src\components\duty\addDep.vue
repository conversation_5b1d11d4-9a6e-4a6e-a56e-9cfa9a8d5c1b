<template>
	<div class="mgt10"> 
		<el-form label-position="right" label-width="95px" :model="submitData" ref="submitData">
      <el-row :gutter='40' class="mgt10">
				<el-col :span="24" style="text-align:left;">
					<el-button type='success' size='mini' @click="preSave('submitData')">保存</el-button >
					<el-button type='warning' size='mini' @click="closeWindow">取消</el-button>
				</el-col>
			</el-row>
			<el-row	:gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="部门名称" prop="dep_name">
						<el-input size='mini' v-model="submitData.dep_name"></el-input>
					
					</el-form-item>
          <el-form-item label="是否生效" prop="forbid_status">
            <el-switch v-model="submitData.forbid_status" on-text="是" off-text="否" on-value="A" off-value="B"></el-switch>

					
					</el-form-item>
				</el-col>
			</el-row>
		
	
		</el-form>
	</div>
</template>
<script>
import validate from '@common/validate.js'
export default {
	props:["params"],
	data(){
		var self = this;
		return {
	
			submitData:{
				dep_name:'',
				forbid_status:"A",
			
			},
		}
	},
	methods:{
		closeWindow(){
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
	

	
	
		/*
		添加地址做保存时校验不允许收货人存在先生小姐等字符。
		*/
		preSave(formName){
      var self = this;
      if(!self.submitData.dep_name){
        self.$message.error('请输入部门名称');
        return;
      }
      this.ajax.postStream('/kingdee-web/api/use/analysis/dep/save?permissionCode=SAVE_ZR_DEP',self.submitData,function(d){
          self.$message({
              message:d.body.msg,
              type:d.body.result?'success':'error'
          });
          if(d.body.result){
			  	self.params.close();
				self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
          }
      },function(e){

      })
		},
	
	
	},
	mounted:function(){
		var self = this;
		
	},
	destroyed(){
	}
}
</script>