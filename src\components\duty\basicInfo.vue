<template>
<div class="xpt-flex">
  <el-form :rules='rules' ref='form' :model="submit" label-position="right" label-width="140px">
    <el-row>
      <el-col :span='8'>
        <el-form-item label='编码：'>
          <el-input size="mini" disabled v-model='submit.code'></el-input>
        </el-form-item>
   	 <el-form-item label="责任范围:" show-overflow-tooltip>
            <el-select size="mini"  v-model="submit.liability_scope" placeholder="请选择" :disabled="false" @change="liabilityScopeChange" @visible-change="handleSelectChangeByHand">
              <el-option
                v-for="(value,key) in {'BD_Empinfo':'员工','BD_Supplier':'供应商','BD_Customer':'客户','BD_Department':'部门'}"
                :label="value"
                :value="key" :key='key'>
              </el-option>
            </el-select>
	    </el-form-item>
      <el-form-item label="问题用途:" show-overflow-tooltip>
            <el-select size="mini"  v-model="submit.user_type" placeholder="请选择" :disabled="false" @change="useTypeChange" @visible-change="handleSelectChangeByHand">
              <el-option
                label="售后单"
                value="AFTERSALE_ORDER" key='AFTERSALE_ORDER'>
              </el-option>
               <el-option
                label="新退款单"
                value="REFUND_ORDER" key='REFUND_ORDER'>
              </el-option>
              <!-- 定制新增类型 -->
              <el-option
                label="定制订单"
                value="CUSTOM_ORDER" key='CUSTOM_ORDER'>
              </el-option>
            </el-select>
	    </el-form-item>
        <el-form-item label='状态：'>
          <el-input size='mini' :value="{ create: '保存', audit: '审核',}[submit.status]"  @blur='changeValue' :disabled="true" :maxlength='50'></el-input>
        </el-form-item>
        <el-form-item label='描述：' style='height:100px'>
          <el-input size="mini"  type="textarea" v-model="submit.remark" style='width: 680px' :autosize="{ minRows: 1, maxRows: 4}" ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span='8'>
        <el-form-item label='责任类型：'>
          <el-input size="mini" :disabled="getDocumentType" v-model='submit.liability_type'></el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="submit.if_foundation" :true-label=1 :false-label=0  style="margin-left:-60px;" >是否基金</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="submit.if_full_deduct" :true-label=1 :false-label=0 style="margin-left:-60px;" >全额抵扣</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="submit.if_auto_amount" :disabled="isAutoDisabled" true-label="Y" false-label="N" style="margin-left:-60px;" >责任金额是否自动带出补件采购成本</el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</div>
</template>
<script>
import validate from '@common/validate.js'
export default {
  props:['params','fd','id'],
  data(){
    var _this=this
    return{
      invoice_content_status:{
        'DETAIL':'明细'
      },
      rules:{
        address_name:validate.isNotBlank({
          required:true,
          self:_this
        }),
        receiver_mobile:validate.isNotBlank({
          required:true,
          self:_this
        }),
        receiver_name:validate.isNotBlank({
          required:true,
          self:_this
        }),
        invoice_type:validate.isNotBlank({
          required:true,
          self:_this
        }),
        invoice_content:validate.isNotBlank({
          required:true,
          self:_this
        }),
        invoice_title:validate.isNotBlank({
          required:true,
          self:_this
        }),
        // 开票金额不能大于总金额，必须是正确的金额
        bill_amount: [{
          // 弹出的提示消息
          required:true,
          message:'开票金额不能大于总金额',
          isShow:false,
          validator: function(rule,value,callback){
            // 数据校验
            if(/(^[1-9]\d*(?:\.\d{1,2})?$)|(^0{1}\.\d{1,2}$)/.test(value + '')){
              if(value > _this.submit.amount) {
                _this.rules[rule.field][0].isShow = true;
                _this.rules[rule.field][0].message = '开票金额不能大于总金额';
                // 校验失败
                callback(new Error(''));
              } else {
                _this.rules[rule.field][0].isShow = false;
                // 校验成功
                callback();
              }
            }else{
              _this.rules[rule.field][0].isShow = true;
              _this.rules[rule.field][0].message = '请输入大于0且最多只有两位小数';
              // 校验失败
              callback(new Error(''));
            }
          }
        }],
        business_telephone:validate.isNotBlank({
          required:true,
          self:_this
        }),
        bank_name:validate.isNotBlank({
          required:true,
          self:_this
        }),
        bank_account:validate.isNotBlank({
          required:true,
          self:_this
        }),
        business_address:validate.isNotBlank({
          required:true,
          self:_this
        }),
        e_mails:validate.isNotBlank({
          required:true,
          self:_this
        }),
        taxpayer_number: validate.isNotBlank({
          required: true,
          self: _this
        })
      },
      invoice_type_options:{
        'DEDICATED':'增值税专用发票',
        'ELECTRONIC':'增值税电子发票'
      },

      submit:{
        code:'',
        liability_type:'',
        status:'create',
        if_full_deduct:'0',
        if_foundation:'0',
        liability_scope:'',
        if_auto_amount:"N",
	      remark:'',
	      user_type:'',
      },
      AttachmentsData1:[],
      AttachmentsData2:[],
//      sign:1,//判断是不是第一次加载
//      annexType:'',//附件类型
//      parent_no:'',

      selectChangeByHand:false,
    }
  },
  methods:{
    validate(invoice_no){
      var self = this,bool=true
      if(!self.submit.liability_scope){
        self.$message.error('责任范围未勾选!')
        bool = false
        return false;
      }
       if(!self.submit.user_type){
        self.$message.error('问题用途为空!')
        bool = false
        return false;
      }
      if(!self.submit.liability_type){
        self.$message.error('责任类型为空!')
        bool = false
        return false;
      }
      return true;
    },

    changeValue(e){
      var self = this
      self.$emit('sendBasicData',self.submit)
    },
    handleSelectChangeByHand(){
      this.selectChangeByHand=true;
    },
    liabilityScopeChange(e){
      this.$emit('liabilityScopeChange', e, this.selectChangeByHand)
    },
    useTypeChange(e){
      if(e!=="CUSTOM_ORDER"){
        this.submit.if_auto_amount="N"
      }
      this.$emit('useTypeChange', e, this.selectChangeByHand)
    },
//    pictureFun() {
//      var self = this
//      var params = {
//        parent_no : this.parent_no,
//        child_no : null,
//        ext_data : null,
//        parent_name :'ORDER',
//        child_name : 'INVOICE',
//        parent_name_txt :'发票',
//        child_name_txt : null,
//      }
//      params.callback = d=>{
//        let i = d.length;
//        while(i--) {
//          let json = d[i].group_value_json;
//          json = JSON.parse(json).content || {};
//          if(json instanceof Array) {
//            json = json[0];
//          }
//          if(json.style == '发票附件' || json.value == '发票附件') {
//            self.if_B = true;
//          } else if(json.style == '加急发票附件' || json.value == '加急发票附件') {
//            self.if_A = true;
//          }
//        }
//      }
//      this.$root.eventHandle.$emit('alert',{
//        params:params,
//        component:()=>import('@components/common/download.vue'),
//        style:'width:1000px;height:600px',
//        title:'下载列表'
//      })
//    },
//    initNo(){//初始化附件编码
//      if(this.parent_no) return
//      var code = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
//      var time = new Date().getTime()
//      var len = 3
//      var temp = ''
//      for(let i =0;i<len;i++){
//        temp+=code[Math.ceil(Math.random()*100000000)%code.length]
//      }
//      this.parent_no=temp+time
//    },
//    updateAmount(val) {
//      this.submit.amount = val;
//    }
  },
  watch:{
    fd:function(){
      var self =this
      for(let d in self.fd){
        self.submit[d]=self.fd[d]
      }
//      if(self.submit.if_urgent==='false') self.submit.if_urgent=false
//      if(self.submit.if_urgent==='true') self.submit.if_urgent=true
//      // self.parent_no= self.fd.attachment_number
//      self.parent_no= self.fd.invoice_no
//      self.submit.invoice_type=self.submit.invoice_type||'DEDICATED'
//      self.submit.invoice_content=self.submit.invoice_content||'DETAIL'
      // this.initNo()
//      this.initAnnex()
    },
    'submit.e_mails':function(){
      if(this.submit.invoice_type=='DEDICATED') return
      else this.submit.e_mail =this.submit.e_mails
    }
  },
  computed:{
    getDocumentType(){
      return this.submit.status=='SUBMITTED'||this.submit.status=='APPROVED'||this.submit.status=='REJECTED'||this.submit.status=='CANCELED' || 'LOCKED' == this.submit.status
    },

    // 锁定状态时可以编辑驳回备注字段
    canEditReject() {
      return 'LOCKED' == this.fd.status;
    },
     isAutoDisabled(){
      return !(this.submit.user_type=="CUSTOM_ORDER")
    }
  }
}
</script>

