<!-- 批量提交责任人 -->
<template>
	<div style="overflow: auto;    height: 100%;">
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type='primary' size='mini' @click="preSave('submitData')" >批量提交责任人</el-button >
			</el-col>
		</el-row>
		<el-row	:gutter='40'  style="height:350px">
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基本信息" name="orderInfo">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="submitData" :rules="rules" ref="submitData">
						<el-col :span='40'>
							<el-form-item label="单据类型" prop="billType">
								<!-- <el-input size='mini' v-model="submitData.billType" disabled></el-input> -->
								<el-radio-group v-model="submitData.billType">
									<el-radio label="A">责任分析子单号</el-radio>
									<el-radio label="B">子单来源单号</el-radio>
								</el-radio-group>
							</el-form-item>
							<el-form-item label="单据编号" prop="billNos">
								<el-input size='mini' class="row-height" v-model="submitData.billNos"  type="textarea" :autosize="{ minRows: 15,maxRows: 15,}" ></el-input>
								<span>已选单据{{orderList.length}}</span>
							</el-form-item>

						</el-col>




			    </el-form>
			  </el-tab-pane>

			</el-tabs>
		</el-row>

		<el-row class='xpt-flex__bottom' id='bottom'>
      		<el-tabs v-model="selectTab2" >
				<el-tab-pane label='责任人' name='personList' class='xpt-flex' >

					<el-row class="xpt-top" :gutter="40">
						<el-col :span="18">
							<el-button
								type="primary"
								size="mini"
								@click="() => addPerson()"
							>添加责任人</el-button>
							<el-button
								type="danger"
								size="mini"
								@click="delPerson"
							>删除</el-button>
						</el-col>
					</el-row>
					<el-table :data="personList" @current-change="currentRow => personList_selectIndex = personList.indexOf(currentRow)" border tooltip-effect="dark" style="width: 100%;" width='100%'>
						<el-table-column width="55" align='center'>
							<template slot-scope='scope'>
								<el-radio class='xpt-table__radio' :label="scope.$index" v-model="personList_selectIndex"></el-radio>
							</template>
						</el-table-column>
						<el-table-column type="index" width="50" label="序号"></el-table-column>
						<el-table-column label="一级责任主体" prop="tinner_liability_type" width="150" show-overflow-tooltip></el-table-column>
						<el-table-column label="二级责任主体" prop="que_sec_name" width="150" show-overflow-tooltip></el-table-column>
						<el-table-column label="故障模块" prop="firstLevelName" width="150" show-overflow-tooltip></el-table-column>
						<el-table-column label="故障现象" prop="secondLevelName" width="150" show-overflow-tooltip></el-table-column>
						<el-table-column label="故障原因" prop="inner_quesd_name" width="150" show-overflow-tooltip>
							<template slot-scope="scope">
								<el-input
									:value="scope.row.inner_quesd_name"
									size='mini'
									icon="search"
									readonly
									:on-icon-click="() => addPerson(scope.row)"
									style="width:100%;"
								></el-input><!-- 不在问题页签不能点击icon -->
							</template>
						</el-table-column>


						<el-table-column label="责任人" prop="liability_person_name" width="150" show-overflow-tooltip>
							<template slot-scope="scope">
								<el-input
									v-model="scope.row.liability_person_name"
									size='mini'
									icon='search'
									readonly
									:on-icon-click="() => selectDutyPerson(scope.row.liability_scope, scope.row, scope.row.liability_type)"
									style="width:100%;"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column label="备注" prop="remark" width="300" show-overflow-tooltip>
							<template slot-scope="scope">
								<el-input  v-model="scope.row.remark" type="textarea" :maxlength="255" style="width:100%;height:100%;"></el-input>
							</template>
						</el-table-column>
						<el-table-column label="责任金额" prop="liability_amount" width="110">
							<template slot-scope="scope">
								<el-input
									size='mini'
									type="number"
									v-model="scope.row.liability_amount"
									style="width:100%;"
									@blur="e => e.target.value = scope.row.liability_amount = Number(Number(scope.row.liability_amount).toFixed(2))"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column label="处理金额" prop="handle_amount" width="110">
							<template slot-scope="scope">
								<el-input
									size='mini'
									type="number"
									v-model="scope.row.handle_amount"
									style="width:100%;"
									@blur="e => e.target.value = scope.row.handle_amount = Number(Number(scope.row.handle_amount).toFixed(2))"
								></el-input>
							</template>
						</el-table-column>
            <el-table-column label="主责/次责" prop="judgment_suggestion" show-overflow-tooltip width="100">
              <template slot-scope="scope">
                <el-select v-model="scope.row.judgment_suggestion" clearable placeholder="请选择" size='mini' style="width: 90px">
                  <el-option
                    v-for="item in judgmentSuggestionOptions"
                    :key="item.value"
                    :label="item.label"
                    :disabled="item.disabled"
                    :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
						<el-table-column label="责任类型（旧版）" prop="liability_type" width="250" show-overflow-tooltip>
							<template slot-scope="scope">
								<el-input
									:value="scope.row.liability_type"
									size='mini'
									readonly
									:on-icon-click="() => selectQuestion(scope.row)"
									style="width:100%;"
								></el-input><!-- 不在问题页签不能点击icon -->
							</template>
						</el-table-column>
						<el-table-column label="责任问题（旧版）" prop="liability_question_name" width="250" show-overflow-tooltip>
							<template slot-scope="scope">
								<el-input
									:value="scope.row.liability_question_name"
									size='mini'
									readonly
									:on-icon-click="() => addPerson(scope.row)"
									style="width:100%;"
								></el-input><!-- 不在问题页签不能点击icon -->
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
			</el-tabs>

		</el-row>

	</div>
</template>
<script>
	import validate from '@common/validate.js';
	import Fn from '@common/Fn.js';
	export default {
		props:["params"],
		data() {
			var _this = this;
			var self = this;
			return{
				orderList:[],
				firstTab:"orderInfo",
				selectTab2:"personList",
        		personList_selectIndex:'',
				baseInfo:[],
				saleMaterial:[],
				personList:[],
				dutyList:[],
				submitData:{
					billType:'A',
					billNos:''
				},
				liability_scope_option: {
					BD_Empinfo: '员工',
					BD_Supplier: '供应商',
					BD_Customer: '客户',
					BD_Department: '部门',
				},
				liability_status_options: {
					APPEAL: '申诉',
					WAIT_SUBMIT: '待提交',
					WAIT_CONFIRM: '待确认',
					COMPLAIN: '申诉',
					END: '终止',
					WAIT_REVOKE: '待撤回',
					RECALLING: '撤回中',
					RECALLED: '已撤回',
					RECALL_FAIL: '撤回失败',
					WAIT_CONDFIRM: '待确认',
					CONFIRMED: '已确认',
				},
        judgmentSuggestionOptions: [
          { label: '判责', value:'JUDGMENT', disabled:true},
          { label: '建议', value:'SUGGESTION', disabled:true},
          { label: '主责', value:'PRINCIPAL'},
          { label: '次责', value:'SECONDARY'},
        ],

				rules:{
					billNos:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					billNos:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),

				},
			}
		},
		methods : {
			selectQuestion(row){
            if(!row.threeLevelId){
                this.$message.error('请选择故障原因')
                return false;
            }
            let params = {
                id: this.questionId
            }, self = this
            params.callback = d => {
                let idx = self.searchIdxOfSelect(row, self.liabilityList)
                if(d) {
                    row.out_liability_question = d.liability_question;
                    row.out_liability_question_id = d.id;
                }
            }
            self.$root.eventHandle.$emit('alert', {
                params: params,
                component: () => import('@components/after_sales_liability_problem/components/select_liability_of_detail.vue'),
                style: 'width:800px;height:500px',
                title: '责任问题(外部)'
            });
        },
			// 删除责任人
		delPerson (){
			if(this.personList_selectIndex === ''){

				this.$message.error('请选择责任人');
				return;
			}
			console.log(this.personList_selectIndex);
			this.personList.splice(this.personList[this.personList_selectIndex], 1);
			this.personList_selectIndex = '';
		},
		// 添加责任人
		addPerson (row){
			let self = this;


				this.$root.eventHandle.$emit('alert',{
				params: {
					callback: d => {

						self.addPersoncall(Object.assign(d,{user_type:"AFTERSALE_ORDER"}),row, true);
					},
				},
				component:()=>import('@components/after_sales_liability_problem/components/add_duty_person.vue'),
					style:'width:1250px;height:80%',
					title:'选择责任问题'
				})


		},
    async addPersoncall(d,row, ifNew){
			let self=this;

			var questionData = {
				liability_scope: d.liability_scope,
				liability_question: d.id,
				liability_question_name: d.liability_question,
				liability_type: d.liability_type,
				liability_question_description: d.des||'',
				if_create_dispute_bill: d.if_create_dispute?true:false,
				liability_person_name: '',//重选问题时清空责任人
				firstLevelName: d.firstLevelName,
				secondLevelName: d.secondLevelName,
				// inner_quesd__id:d.threeLevelId,
				inner_quesd_name:d.threeLevelName,
				inner_liability_type_id:d.que_id,

			}
				questionData.liability_question = d.liability_question_id
				questionData.firstLevelName = d.firstLevelName
				questionData.secondLevelName = d.secondLevelName
				questionData.inner_quesd_name = d.threeLevelName
				questionData.liability_question_name = d.out_liability_question
				questionData.liability_question = d.out_liability_question_id
				questionData.liability_type = d.out_liability_type
				questionData.judgment_suggestion = d.judgment_suggestion
				questionData.tinner_liability_type = d.liability_type
				questionData.inner_quesd__id = d.threeLevelId
				questionData.que_sec_name = d.second_liability_type
				// tinner_liability_type:d.liability_type,

				questionData.ques_detail_id = d.id





				// Object.assign(questionData, {
				// 	liability_status: 'WAIT_CONDFIRM',

				// 	batch_trade_no: targetQuestion.batch_trade_no,
				// 	parent_question_id: targetQuestion.parent_question_id,
				// 	original_question_id: targetQuestion.original_question_id,
				// 	question_sub_id: targetQuestion.id,
				// 	analysis_sub_id: targetQuestion.analysis_sub_id,
				// 	_if_goods_question: targetQuestion.if_goods_question,//保存时会过滤此字段
				// })
				if(row){
					Object.assign(row, questionData)
				}else{
					this.personList.push(questionData)
				}


		},

			preSave(formName){
				var self = this;
				// if(this.submitData.bill_type_id=='PRESENT' && !this.submitData.arm_mager_id){
				// 	this.$message.error('赠品订单请选择合并订单号');
				// 	return;
				// }
				self.$refs[formName].validate((valid) => {
					if(!valid) return

					self.save()
				})
			},
			// 选择责任人
		selectDutyPerson (liability_scope, row, liability_type){

			// this.merge_trade_id?'':this.getMergeTradeId();//客户弹出框所需要的接口参数
			//
			let otherParams = {
				mergeTradeId : '',//客户弹出框所需要的接口参数
				list_type : {
					'工厂责任': ['SP','SA'],
					'物流三包': ['SB','WL'],
				}[liability_type] || []//供应商弹出框所需要的接口参数
			}
			,	params = {
	        	otherParams:otherParams,
	        	callback: d => {
	        		//客户和其它弹出框返回的参数需要区别
	        		if(liability_scope == 'BD_Customer'){
	        			row.liability_person = d.cust_id//客户ID
	        			this.$set(row, 'liability_person_name', d.name);//客户昵称
	        		}else{
	        			row.liability_person = d.id || d.data.supplier_id || d.data.id || d.data[0].id
	        			this.$set(row, 'liability_person_name', d.fullName || d._real_name_nick_name || d.data.name || d.data.realName || d.data[0].name)
	        		}

	        	},
	        	liability_type: liability_scope,
	        	showAllPerson: true,
	        }

	        if(liability_scope === 'BD_Department') params.type = 1

			this.$root.eventHandle.$emit('alert',{
		        params: params,
		        close: f => f,
		        component:()=>import('@components/' + {
		        	BD_Empinfo: 'personel/list',//员工
					BD_Supplier: 'duty/supplier',//供应商
					BD_Customer: 'duty/selectPickrecommendhandler',//客户
					BD_Department: 'k3/departmentlist',//部门
		        }[liability_scope]),
		        style:'width:80%;height:80%',
		        title:'选择' + this.liability_scope_option[liability_scope] + '列表'
			})
		},
			save(callback){
				var self = this,
				params={};
				var url = "/afterSale-web/api/aftersale/analysis/person/batchSubmit";

				let saveData = JSON.parse(JSON.stringify(this.submitData));
				let billNos = this.submitData.billNos.split('\n')
				saveData.billNos = []
				saveData.personList = this.personList;
				billNos.forEach(item=>{
					if(!!item){
						saveData.billNos.push(item)
					}
				})
				// delete saveData.remarks;
				//delete saveData.groupName;
				this.ajax.postStream(url,/*self.submitData*/saveData,(response)=>{
					if(response.body.result){
						// this.$message({message: '操作成功',type: 'success'});
						// params.sys_trade_id = response.body.content.sys_trade_id
						// this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
						let errorMessage = '';
						response.body.content.forEach(item=>{
							if(!item.result){
								errorMessage += item.content+': '+item.msg
							}
						})
						if(!!errorMessage){
							this.$message.error(errorMessage)
						}else{
							this.$message({message: '操作成功',type: 'success'});
						}
					}else{
						this.$message.error(response.body.msg)
					}
				},e=>{
					this.$message.error(e)
				})
			},

		},
		watch:{
			'submitData.billNos':function(newval,oldval){
				// console.log(newval);
				this.orderList = newval.split('\n')
			}
		},
		mounted:function(){
			var self = this;
			console.log(self.params);
			self.baseInfo = self.params.data;
			this.orderList = this.params.orderList;
			this.params.orderList.forEach((item,index)=>{
				if(index != this.params.orderList.length-1){
					this.submitData.billNos += item.sub_bill_no+
`
`
				}else{
					this.submitData.billNos += item.sub_bill_no
				}

			})

		}
	}
</script>
<style scoped>
.row-height :global(.el-form-item__content) {
	height: auto!important;
	white-space: nowrap;
	width: 50%;
}
</style>
