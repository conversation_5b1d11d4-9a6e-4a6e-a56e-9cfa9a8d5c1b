<template>
  <div class="file-list">
    <div class="file-list-item" v-for="(item, index) in fileList" :key="index">
      <a href="javascript:void(0)" @click="open(item)" class="label">{{ item.name }}</a>
      <div class="close" @click="remove(item)">
        <i class="el-icon-close"></i>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      fileList: [],
    }
  },
  methods: {
    open(item) {
      if (!item.path) {
        return
      }
      this.$emit(
        "open",
        item
      );
    },
    remove(item) {
      this.fileList = this.fileList.filter(sub => sub.path !== item.path)
      this.$emit("updateList",this.fileList)
    }
  },
  watch: {
    list: {
      handler(val) {
        this.fileList = val
      },
      deep: true,
      immediate:true,
    }
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
  },
}
</script>
<style scoped lang="stylus">
  .file-list
    height:100%;
    overflow auto;
  .file-list-item
    width: 100%;
    display:flex;
    align-items: center;
  .label
    margin-right: 10px;
  .close{
    cursor: pointer
  }
</style>
