<!--驳回资料-->
<template>
  <div style="height: 100%">
    <div class="xpt-flex">
      <div class="xpt-top">
        <el-button type="info" size="mini" @click="submit" :loading="submitLoading">提交</el-button>
      </div>
      <div class="xpt-flex__bottom">
        <el-form :model="formData" :rules="rules" ref="formDataRef">
          <el-row class="mgt20" :gutter="40">
            <el-col :span="24">
              <el-form-item label="驳回原因：" prop="rejection_reason" label-width="90px">
                <el-input type="textarea" placeholder="请输入内容" v-model="formData.rejection_reason"
                  :autosize="{ minRows: 4, maxRows:6 }" :maxlength="500">
                </el-input>
                <el-tooltip v-if="rules.rejection_reason[0].isShow" class="item" effect="dark"
                  :content="rules.rejection_reason[0].message" placement="right" popper-class="xpt-form__error">
                  <i class="el-icon-warning"></i>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

  </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from "@common/Fn.js";
export default {
  props: ["params"],
  data() {
    const self = this;
    return {
      submitLoading: false,
      formData:{
        rejection_reason:'',
      },
      rules: {
        rejection_reason: validate.isNotBlank({
          self: self,
        }),
      }
    };
  },
  created() {
    this.initData();
  },
  mounted() { },
  computed: {},
  methods: {
    initData(){
      if(this.params.itemObj){
        const {
          rejection_reason
        }=this.params.itemObj;
        this.formData={
          ...this.formData,
          rejection_reason
        }
      }
    },
    submitData() {
      const { id  }=this.params.itemObj;
      const {rejection_reason}=this.formData;
      return new Promise((resolve, reject) => {
        const params = {
          id,
          rejection_reason
        }
        this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/information/rejectInfoReq', params, res => {
          if (res.body.result) {
            resolve(res)
          } else {
            reject(res);
          }
        })
      })
    },
    async submit() {
      this.$refs.formDataRef.validate(async (valid) => {
        if (!valid) return;
        try {
          this.submitLoading = true;
          const res = await this.submitData();
          this.$message.success(res.body.msg || '提交成功')
          this.submitLoading = false;
          this.params.callback && this.params.callback();
          this.$root.eventHandle.$emit(
            "removeAlert",
            this.params.alertId
          );
        } catch (err) {
          this.$message.error(err.body.msg || '提交失败')
          this.submitLoading = false;
        }
      });
    }
  },
};
</script>
<style scope>
.btn-bottom {
  padding-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
