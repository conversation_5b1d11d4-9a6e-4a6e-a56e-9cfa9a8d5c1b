<!--资料请求-->
<template>
  <div style="height: 100%">
    <div class="xpt-flex">
      <div class="xpt-top">
        <el-button type="info" size="mini" @click="submit" :loading="submitLoading">提交</el-button>
        <el-button type="success" size="mini" @click="onSave" :loading="submitLoading">保存</el-button>
      </div>
      <div class="xpt-flex__bottom">
        <el-form :model="formData" :rules="rules" ref="formDataRef" label-width="120px">
          <el-form-item label="资料请求说明：" prop="info_require_remark">
            <el-input type="textarea" placeholder="请输入内容" v-model="formData.info_require_remark"
              :autosize="{ minRows: 4, maxRows: 6 }" :maxlength="500">
            </el-input>
            <el-tooltip v-if="rules.info_require_remark[0].isShow" class="item" effect="dark"
              :content="rules.info_require_remark[0].message" placement="right" popper-class="xpt-form__error">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="请求接收人：" prop="request_person">
            <el-row class="mgt20" :gutter="0">
              <el-col :span="6">
                <el-radio v-model="formData.receiver_type" label="STAFF">
                  <div style="width:65px;text-align: left;display: inline-block;">业务员</div>
                  <el-input size="mini" v-model="formData.staff_name" readonly icon="search"
                    :on-icon-click="() => selectUser('STAFF')" class="select-intervener"></el-input>
                </el-radio>
              </el-col>
            </el-row>
            <el-row class="mgt20" :gutter="0">
              <el-col :span="6">
                <el-radio v-model="formData.receiver_type" label="AFTERSALE_STAFF">
                  <div style="width:65px;text-align: left;display: inline-block;">售后处理人</div>
                  <el-input size="mini" v-model="formData.afterSale_processor_name" readonly icon="search"
                    :on-icon-click="() => selectUser('AFTERSALE_STAFF')" class="select-intervener"></el-input>
                </el-radio>
              </el-col>
            </el-row>
            <el-tooltip v-if="rules.request_person[0].isShow" class="item" effect="dark"
              :content="rules.request_person[0].message" placement="right" popper-class="xpt-form__error">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-form>
      </div>
    </div>

  </div>
</template>
<script>
import validate from "@common/validate.js";

export default {
  props: ["params"],
  data() {
    const self = this;
    return {
      submitLoading: false,
      formData: {
        info_require_remark: '',
        request_person: false,
        receiver_type: '',
        receiver_id: '',
        receiver_name: '',
        staff: '',
        staff_name: '',
        afterSale_processor: '',
        afterSale_processor_name: '',
      },
      rules: {
        info_require_remark: validate.isNotBlank({
          self: self,
        }),
        request_person: [{
          required: true,
          trigger: 'blur',
          message: '请选择接收人',
          validator: (rule, value, callback) => {
            if (!this.formData.receiver_type || !(this.formData.staff || this.formData.afterSale_processor)) {
              this.rules[rule.field][0].isShow = true
              callback(new Error(''))
            } else {
              this.rules[rule.field][0].isShow = false
              callback()
            }
          },
        }],
      },
    };
  },
  created() {
    this.initData();
  },
  mounted() { },
  computed: {

  },
  methods: {
    initData(){
      if(this.params.itemObj){
        const {
          info_require_remark,
          receiver_type,
          receiver_id,
          receiver_name,
          id,
        }=this.params.itemObj;
        this.formData={
          ...this.formData,
          id,
          info_require_remark,
          receiver_type,
          receiver_id,
          receiver_name,
        }
        if(receiver_type==="STAFF"){
          this.formData.staff=receiver_id
          this.formData.staff_name=receiver_name
        }else{
          this.formData.afterSale_processor=receiver_id
          this.formData.afterSale_processor_name=receiver_name
        }
      }else{
        const {staff,staff_name}=this.params.aftersAleObj
        const {aftersale_processor,aftersale_processor_name}=this.params.subObj
        this.formData.staff=staff
        this.formData.staff_name=staff_name
        this.formData.afterSale_processor=aftersale_processor
        this.formData.afterSale_processor_name=aftersale_processor_name
        this.formData.receiver_type='AFTERSALE_STAFF'
      }
    },

    getParams() {
      const {id:analysis_sub_id}=this.params.subObj;
      const {info_require_remark,receiver_type,staff,staff_name,afterSale_processor,aftersale_processor_name,id}=this.formData;
        let receiver_id=''
        let receiver_name=''
        if(receiver_type==='STAFF'){
          receiver_id=staff
          receiver_name=staff_name
        }else{
          receiver_id=afterSale_processor
          receiver_name=aftersale_processor_name
        }
        const params = {
          analysis_sub_id,
          info_require_remark,
          receiver_type,
          receiver_id,
          receiver_name,
        }
        if(id){
          params.id=id;
        }
        return params;
    },

    submitInfo() {
      return new Promise((resolve, reject) => {
        const params = this.getParams();
        this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/information/infoReq', params, res => {
          if (res.body.result) {
            resolve(res)
          } else {
            reject(res);
          }
        },
        err => {
          reject(err)
        })
      })
    },
    selectUser(type) {
      let self = this;
      this.$root.eventHandle.$emit("alert", {
        component: () =>
          import("@components/duty/receiverSelectIntervener.vue"),
        close: function () { },
        style: "width:900px;height:600px",
        title: "请选择接收人",
        params: {
          callback(d) {
            if (type === 'STAFF') {
              self.formData.staff = d.id;
              self.formData.staff_name = d.fullName;
            } else {
              self.formData.afterSale_processor = d.id;
              self.formData.afterSale_processor_name = d.fullName;
            }
            self.formData.receiver_id = d.id;
            self.formData.receiver_name = d.fullName;
            self.$message.success("选择成功");
            self.$refs['formDataRef'].validateField('request_person')
          },
        },
      });
    },

    validStaffIncumbency() {
      return new Promise((resolve, reject) => {
        const params = this.getParams();
        this.ajax.get('/user-web/api/userPerson/getUserPersonByLoginId/' + params.receiver_id, (data) => {
          if (data?.body?.result) {
            if (data?.body?.content?.incumbency === 'LEAVE_OFFICE') {
              this.$confirm(`${data?.body?.content?.fullName}已离职，请确认是否继续提交`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                resolve();
              }).catch(() => {
                resolve(false)
              });
            } else {
              resolve();
            }
          } else {
            reject(data)
          }
        },
        err => {
          reject(err);
        });
      })
    },

    async submit() {
      this.$refs.formDataRef.validate(async (valid) => {
        if (!valid) return;
        try {
          this.submitLoading = true;
          const result = await this.validStaffIncumbency();
          if (result === false) {
            this.submitLoading = false;
            return;
          }
          const res = await this.submitInfo();
          this.submitLoading = false;
          this.$message.success(res?.body?.msg || '提交成功')
          this.params.callback&&this.params.callback();
          this.$root.eventHandle.$emit(
            "removeAlert",
            this.params.alertId
          );
        } catch (err) {
          this.submitLoading = false;
          this.$message.error(err?.body?.msg || '提交失败')
        }
      });
    },

  saveInfo() {
    return new Promise((resolve, reject) => {
      const params = this.getParams();
      this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/information/save', params, res => {
        if (res.body.result) {
          resolve(res)
        } else {
          reject(res);
        }
      },
      err => {
        reject(err)
      })
    })
  },
  async onSave() {
    this.$refs.formDataRef.validate(async (valid) => {
        if (!valid) return;
        try {
          this.submitLoading = true;
          const res= await this.saveInfo();
          this.submitLoading = false;
          this.$message.success(res?.body?.msg || '保存成功')
          this.params.callback&&this.params.callback();
          this.$root.eventHandle.$emit(
            "removeAlert",
            this.params.alertId
          );
        } catch (err) {
          this.submitLoading = false;
          this.$message.error(err?.body?.msg || '保存失败')
        }
      });
  }
  },
};
</script>
<style scope>
.el-form-item .el-form-item__label,
.el-form-item .el-form-item__content {
  height: auto
}
</style>
