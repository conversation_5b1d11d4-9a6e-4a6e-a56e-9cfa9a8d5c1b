<!--提交资料-->
<template>
  <div style="height: 100%">
    <div class="xpt-flex">
      <div class="xpt-top">
        <el-button type="info" size="mini" @click="submit" :loading="submitLoading">提交</el-button>
      </div>
      <div class="xpt-flex__bottom">
        <el-form :model="formData" :rules="rules" ref="formDataRef" label-position="right" label-width="100px">
          <el-form-item label="资料提交说明：" prop="info_submit_remark">
            <el-input type="textarea" placeholder="请输入内容" v-model="formData.info_submit_remark"
              :autosize="{ minRows: 4, maxRows: 6 }" :maxlength="500">
            </el-input>
            <el-tooltip v-if="rules.info_submit_remark[0].isShow" class="item" effect="dark"
              :content="rules.info_submit_remark[0].message" placement="right" popper-class="xpt-form__error">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="上传附件：">
            <xpt-upload-v3 uploadBtnText="上传附件" acceptType="usually" :dataObj="uploadData" @uploadSuccess="uploadSuccess"
              :uploadSize="20"></xpt-upload-v3>
          </el-form-item>
          <el-form-item label="">
            <file-list :list="this.formData.fileList" @open="openFile" @updateList="updateFileList" style="height:180px"></file-list>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <xpt-image :images="previewImgList" :show="showImage" :ifUpload="false" @close="closeShowImage">
    </xpt-image>
  </div>
</template>
<script>
import validate from "@common/validate.js";
import FileList from './file-list.vue'
import Fn from "@common/Fn.js";
export default {
  props: ["params"],
  components: {
    FileList
  },
  data() {
    const self = this;
    return {
      submitLoading: false,
      formData: {
        info_submit_remark: '',
        fileList: [],
      },
      rules: {
        info_submit_remark: validate.isNotBlank({
          self: self,
        }),
      },
      //附件上传路径标志
      uploadData: {},
      showImage: false,
      previewImgList: [],
    };
  },
  created() {
    this.uploadData = {
      parent_no: this.params.itemObj.id,
    }
  },
  created() {
    this.initData();
  },
  mounted() { },
  computed: {},
  methods: {
    initData(){
      if(this.params.itemObj){
        const {
          info_submit_remark,
        }=this.params.itemObj;
        this.formData={
          ...this.formData,
          info_submit_remark,
        }
      }
    },
    submitData() {
      const { id } = this.params.itemObj;
      const { info_submit_remark,fileList } = this.formData;
      const formatFileList=fileList.map(item=>{
        return{
          ...item,
          group_value_json:JSON.stringify({
            parent_no: this.params.afterOrderNo,
          }),
          order_no:this.params.afterOrderNo,
        }
      })
      return new Promise((resolve, reject) => {
        const params = {
          id,
          info_submit_remark,
          fileList:formatFileList
        }
        this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/information/submitInfoReq', params, res => {
          if (res.body.result) {
            resolve(res)
          } else {
            reject(res);
          }
        })
      })
    },
    async submit() {
      this.$refs.formDataRef.validate(async (valid) => {
        if (!valid) return;
        try {
          this.submitLoading = true;
          const res = await this.submitData();
          this.$message.success(res.body.msg || '提交成功')
          this.submitLoading = false;
          this.params.callback && this.params.callback();
          this.$root.eventHandle.$emit(
            "removeAlert",
            this.params.alertId
          );
        } catch (err) {
          this.$message.error(err.body.msg || '提交失败')
          this.submitLoading = false;
        }
      });
    },
    uploadSuccess(uploadFileList) {
      this.formData.fileList.unshift(...uploadFileList);
    },
    //初始化预览列表
    initImageList(row) {
      let imgList = [];
      //过滤出只有图片的列表
      let list = this.formData.fileList.filter((item) =>
        this.isImage(item.path)
      );
      list.forEach((value) => {
        let obj = Object.assign({}, value);
        obj.src = value.path;
        obj.date = null;
        obj.creatName = value.name;
        obj.isPucture = true;
        //确定要预览那张图片
        if (value.path === row.path) {
          obj.active = true;
        } else {
          obj.active = false;
        }
        imgList.push(obj);
      });
      this.previewImgList = imgList;
    },
    closeShowImage() {
      this.showImage = false;
    },
    isImage(str) {
      var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
      return reg.test(str);
    },
    previewImage(row) {
      this.initImageList(row);
      this.showImage = true;
    },
    openFile(item) {
      if (this.isImage(item.path)) {
        this.previewImage(item);
      } else {
        Fn.download(item.path, item.name || '附件')
      }
    },
    updateFileList(list){
      this.formData.fileList=list
    }
  },
};
</script>
<style scope>
.el-form-item .el-form-item__label,
.el-form-item .el-form-item__content {
  height: auto
}
</style>
