<template>
<div class='xpt-flex'  style="height:280px;overflow-y:auto;">
	<xpt-headbar>
		<el-button type='primary' slot='left' size='mini' @click="save">保存</el-button>
		
	</xpt-headbar>
  <el-form label-width='100px' :model='query'  ref="query">
    <el-row :gutter='40'>
      <el-col :span='40'  class="xpt_col">
         <el-form-item label="上传附件">
            <el-button @click="uploadFile" size="mini" type="primary">上传附件</el-button>
        </el-form-item>
         <ul v-show="info.fileList.length > 0" class="file-auto" style="margin-left:40px;">
                <li v-for="(item, idx) in info.fileList" :key="idx">
                    <a class="white-text" :href="item.path" target="_blank" :download="item.path">{{item.name}}</a><i class="el-icon-close" style="width: 20px; margin:10px;" @click="() => deleteFile(idx)"></i>
                </li>
            </ul>
        <el-form-item label='请输入理由' prop='remark'>
          <el-input v-model="query.remark" size="mini"  type="textarea" :autosize="{ minRows: 6, maxRows: 8}"></el-input>
        </el-form-item>
        
      </el-col>
    </el-row>
  </el-form>
  <xpt-image
            :images="imageList"
            :show="ifShowImage"
            :ifUpload="false"
            :ifClose="false"
            @close="closeShowImage"
        >
        </xpt-image>
        <product-upload 
            :ifClickUpload="ifClickUpload" 
            :isMultiple="true"
            :dataObj="uploadData" 
            :callback="uploadSuccess" 

            accept=".pdf,.PDF,.jpg,.png,.jpeg,.JPG,.PNG,.JPEG,.mp4,.MP4">
        </product-upload>
</div>
</template>
<script>
import upload from "./upload"
  export default {
    components: {
        'productUpload': upload
    },
    data() {
      let self = this;
      return {
        uploadData: {},
        ifClickUpload:false,
        imageList: [],
        info:{
          fileList:[]
        },
        ifShowImage: false,
        oldGoodsList: [],
        query:{
          remark:'',
        },
      }
    },
	  props:['params'],
    methods: {
      deleteFile(idx) {
            this.info.fileList.splice(idx,1)
        },
      uploadFile () {
            this.ifClickUpload = true;
            this.uploadData = {
                parent_name: "PRODUCT_CONSULTING",
                parent_no: 'product_consulting',
                child_name: null,
                child_no: null,
                content: {}
            };
            setTimeout(() => {
                this.ifClickUpload = false;
            }, 100);
        },
        uploadSuccess(uploadFileList) {
          console.log(uploadFileList);
            if(!!uploadFileList[0]){
              this.info.fileList = this.info.fileList.concat(uploadFileList); 
            }else{
              this.$message.error('上传失败，请检查文件格式')
            }
        },
        closeShowImage() {
            this.ifShowImage = false;
        },
        showImageList(row) {
            this.initImageList(row);
            this.ifShowImage = true;
        },
      close(){
        let self = this;
        self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
      },
      save(){
        if(this.query.remark.length>250){
          this.$message.error('理由不能超过250个字符');
          return false;
        }
        if(this.params.type == 'create'&&!this.query.remark){
          this.$message.error('理由不能为空');
          return false;
        }
        this.params.callback(this.query.remark,this.info.fileList);
        this.close();
      },
      
     
    },
    mounted(){
      // this.getOldGoodsList();
    }
  }
</script>
<style>
	.repo-input{
    float: right;
    /* margin-right: 50px; */
	}
  .xpt_col{
    margin-bottom: 10px;
  }
</style>
