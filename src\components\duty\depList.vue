<!--责任部门列表-->
<template>
	<xpt-list
		ref='depList' 
		:data='depList' 
		:btns='btns'
		:colData='colData' 
		:selection='selection' 
		:pageTotal='count' 
		searchHolder='请输入部门名称'  
		@search-click='searchCondition' 
		@radio-change='selectChange' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange' 
	></xpt-list>
</template>
<script>
	export default {
        props:['params'],
		data(){
			let self = this
			return {
				search:{page_no:1,page_size:50,key:""},
                count:0,
				depList:[],
                condition:'',
				showSearch:false,
                operationList:'',
                isAlert:false,
                colData: [
                	{
						label: '部门名称',
						prop: 'dep_name',
					
					}, {
						label: '编码',
						prop: 'dep_number'
					}, {
						label: '创建时间',
						prop: 'create_time',
						format: 'dataFormat1'
					},{
						label: '修改时间',
						prop: 'modify_time',
						format: 'dataFormat1'
					}, {
						label: '是否生效',
						prop: 'forbid_status',
						formatter(val){
							switch(val){
								case 'A':return '生效' ;break;
								case 'B':return '失效' ;break;
							}
						}
					}
				],
				btns: [
					{
						type: 'primary',
						txt: '新增',
						click(){
							self.addDep()
						}
					}, {
						type: 'success',
						txt: '刷新',
						click(){
							self.getdepList()
						}
					},  {
						type: 'success',
						txt: '生效',
						click(){
							self.changeStatus(1)
						}
					}, {
						type: 'warning',
						txt: '失效',
						click(){
							self.changeStatus(0)
						}
					}
				],
				selection: 'radio'
			}
		},

		methods:{
			searchCondition(txt){
				this.search.key = txt
                this.resetSearchData(!0);
				this.getdepList();
			},
            sizeChange(size){
                // 每页加载数据
                this.search.page_size = size;
                this.search.page_no = 1;

                this.getdepList();
            },
            pageChange(page_no){
                // 页数改变
                this.search.page_no = page_no;
                //var type = this.search.key?2:1;
                this.getdepList();
            },
			openSearch(){
				this.showSearch = !this.showSearch
			},
            
            intoUserInfo(row){
            	if(this.params.isAlert) return;
                this.$root.eventHandle.$emit('creatTab',{name:'编辑部门信息',params:{employeeNumber:row.employeeNumber},component:()=>import('@components/user/users')});
			},
      addDep(){
        let self = this,params = {};
        params.close = () =>{
          self.getdepList();
        },
				self.$root.eventHandle.$emit('alert', { 
          params:params,
          component: () => import('@components/duty/addDep.vue'),
          style:'width:500px;height:280px',
          title:'新增责任部门'
        });
      },
			isEmptyObject(data){
              for(var key in data){
                  return false;
			  }
			  return true;
			},
            changeStatus(status){
                //批量修改
				var data = this.operationList;
				console.log(data);
                var _this = this;
                if(!data){
                    _this.$message({
                        message:'请选择部门',
                        type:'error'
                    });
                    return;
				}
				
                this.ajax.postStream('/kingdee-web/api/use/analysis/dep/forbidStatus?permissionCode=SAVE_ZR_DEPSX',{dep_id:data.dep_id,forbid_status:status==1?"A":"B"},function(d){
                    _this.$message({
                        message:d.body.msg,
                        type:d.body.result?'success':'error'
                    });
                    if(d.body.result){
                        _this.getdepList();

                    }
                },function(e){

                })
            },
			selectChange(val){
				// 选择部门，用于添加用记时弹出框
				this.operationList = val;
			},
            resetSearchCondition(){
			  //重置搜索条件
				for(var key in this.search){
				    if(this.search.hasOwnProperty(key)){
				        this.search[key] = '';
					}

				}
				//this.search.type = 'GENERAL';
			},
			/**
			 * 获取查询条件的数据
			 * **/
			getSearchData(){
                var data = this.search;
                var searchData = {};
                for(var key in data){
                    if(data.hasOwnProperty(key) && data[key] !== ''){
                        searchData[key] = data[key];
                    }
				}
				return searchData;
			},
			getdepList(){
			    var _this = this;

				
				this.ajax.postStream('/kingdee-web/api/use/analysis/dep/list',this.search,function(d){
				    var data = d.body;
                    if(data.result){
						_this.count = data.content.count;
                        _this.depList = data.content.list || [];
						_this.clearSelectedData();
                    }else{
                        _this.$message({
                            message:d.body.msg,
                            type:'error'
                        })
                    }
                },function(e){

                })
			},
            searchdepList(){//查询部门列表
                this.getdepList();
			},
			closeAlert(){
				// 关闭弹窗
				if(this.params.isAlert){
					this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
				}
			},
			initData(){
			  //初使化数据
                var data = this.params;
				this.isAlert = data.isAlert?true:false;

				var searchData = this.search;
                for(var key in searchData){
                    if(data.hasOwnProperty(key) && 'page' != key){
                        searchData[key] = data[key];
                    }
                }
                let self = this
                if (this.isAlert) {
                	this.btns = [{
                		type: 'primary',
                		txt: '确认',
                		click(){
                			self.closeAlert()
                		}
                	}]
                	this.selection = 'radio'
                }
			},
            resetSearchData(bool){//bool,是要加载首页的数据还是加载当前页的数据，为true时，加载首页，false则为当前页面
				bool?this.search.page_no = 1:'';
            },
            /**
             * 清空批量选择的数据
             * **/
            clearSelectedData(){
                this.operationList = {};
                this.$refs.depList && this.$refs.depList.clearSelection();
            }
		},
		created(){
			var _this = this;
			//_this.initData();
			_this.getdepList();
            _this.$root.eventHandle.$on('updatadepList',function(){
                _this.resetSearchData(!0);
                _this.getdepList();
            });
		},
        destroyed(){
            // 销毁根节点的所有监听事件
			this.$root.offEvents('updatadepList');
        }
	}
</script>