import validate from '@common/validate.js'
import fn from '@common/Fn.js'

export default {
	data() {
		let self = this;
		return {
			otherCols: [
				[
					{
						label: '创建人：',
						key: 'create_name'
					}, {
						label: '创建日期：',
						key: 'create_time',
            format: 'dataFormat'
					}
				], [
					{
						label: '审核人：',
						key: 'audit_name'
					}, {
						label: '审核日期：',
						key: 'audit_date',
            format: 'dataFormat'
					}
				],
			],
			// 责任明晰
      colData: [
        {
          label: '责任问题',
          prop: 'liability_question',
          width:200,
          bool:true,
          isThis:true,
          disabled:false,
          conditionDisabled:'isNotEdit',
          isInput:true
        },
        {
          label: '是否生成索赔服务单',
          prop: 'if_create_dispute',
          bool: true,
          isThis: true,
          conditionDisabled: 'isNotEdit',
          isSelect: true,
          formatter(val){
            return val == 0 ? '是' : val == 1 ?'否':val
          },
        },
         {
          label: '是否禁用',
          prop: 'forbidden_status',
          bool:true,
          disable:true,
          // isInput:true,
          isSelect:true,
          isThis:true,
          conditionDisabled:'false',
          obj:{
            1:'否',
            0:'是'
          },
          formatter(val){
            return val == 0 ? '是' : val == 1?'否':val
          }
        },
        {
          label: '禁用人',
          prop: 'forbidden_person_name',
          isInput:true,
          redirectClick:false,
        },
        {
					label: '禁用日期',
					prop: 'forbidden_date',
          // isInput:true,
          elDate:true,
          // isThis:true,
          // conditionDisabled:'false',
					format: 'dataFormat'
				}
			],
      detailList: [],
			// detailBtns: [
			// 	{
			// 		type: 'info',
			// 		txt: '新增行',
			// 		// disabled:"isAddRow",
			// 		disabled:false,
			// 		click: self.detailAdd
			// 	}, {
			// 		type: 'danger',
			// 		txt: '删除行',
			// 		disabled: false,
			// 		click: self.detailDel
			// 	}, {
        //   type: 'info',
        //   txt: '禁用/启用',
        //   disabled: false,
        //   click: self.detailDisable
        // }
			// ],

			detailSelect: ''
      // invoice_type_options:{
		//         'DEDICATED':'增值税专用发票',
		//         'ELECTRONIC':'增值税电子发票'
		//     },
		}
	},
	computed: {
		// 其它信息编辑状态，只有在锁定的状态下才可以编辑，
		// editOther() {
		// 	return 'LOCKED' != this.basicOrderData.document_status;
		// },
		// 明细按钮状态
		// detailBtnStatus() {
      	// 	return this.basicOrderData.document_status=='SUBMITTED'
      	// 		|| this.basicOrderData.document_status=='APPROVED'
      	// 		|| this.basicOrderData.document_status=='REJECTED'
      	// 		|| this.basicOrderData.document_status=='CANCELED'
      	// 		|| this.basicOrderData.document_status=='LOCKED'
      	// 		|| !this.isSelectOrder
		// },
	},
	watch: {
		detailBtnStatus(n) {
			this.detailBtns[0].disabled = n
			this.detailBtns[1].disabled = n
		}
	},
	methods: {
		detailAdd() {
      var self = this;
      console.log('detailAdd : the detailCols ====');
      console.log(this.detailsData);
      // self.rejectList.push(self.getRejectObj());
      this.detailsData.push(self.getDetailObj());
      // self.isAddRowFun();

		},
    getDetailObj(){
      // let name = Fn.getUserInfo('fullName');
      // console.log('name',name);
      var obj = {
        // id:null,
        liability_question: null, //责任问题
        if_create_dispute: null,  //是否生成索赔服务单
        forbidden_status : null,  //是否禁用
        forbidden_person_name:null,  //禁用人
        forbidden_date:null         //禁用日期
      }
      return obj;
    },

    isAddRowFun(){
      // this.isAddRow = this.rejectDisabled;
      // if(this.isAddRow) return;
      //如果有一条新增的数据，则不让它新增
      let i = this.detailCols.length;
      for(let a = 0;a < i; a++){
        if(!this.detailCols[a].id){
          this.isAddRow = true;
          return;
        }
      }
      this.isAddRow = false;
      return;
    },

    //
    // getRejectObj(){
    //   let name = Fn.getUserInfo('fullName');
    //   console.log('name',name);
    //   var obj = {
    //     id:null,
    //     after_bill_id: null,
    //     rejector_name: name,//驳回人
    //     biz_note : this.formInfo.business_remark,//业务备注
    //     reject_remark : null,//驳回原因
    //     reject_time: null,//驳回时间
    //     create_time:null,
    //     creator:null,
    //     creator_name:null,
    //     rejector:null,
    //     //testid:"test"
    //   }
    //   return obj;
    // },

		// 没有选单的时候是不能操作的
		detailDel() {
			if(this.detailSelect.length===0) {
				this.$message.error('请选择数据!')
				return
			}
			let i = this.detailSelect.length,
				deleteAmount = 0,
				deleteTempId = new Set(),
				deleteInvoiceId = new Set();

			while(i--) {
				if(this.detailSelect[i].tempId) {
					deleteTempId.add(this.detailSelect[i].tempId)
				} else if(this.detailSelect[i].invoice_detail_id) {
					deleteInvoiceId.add(this.detailSelect[i].invoice_detail_id)
				}
			}
			console.log(this.detailsData);
			i = this.detailsData.length;
			while(i--) {
				if(this.detailsData[i].tempId && deleteTempId.has(this.detailsData[i].tempId)) {
					deleteAmount += this.detailsData[i].act_price;
					this.detailsData.splice(i, 1);
				} else {
					if(this.detailsData[i].invoice_detail_id && deleteInvoiceId.has(this.detailsData[i].invoice_detail_id)) {
						deleteAmount += this.detailsData[i].act_price;
						this.detailsData.splice(i, 1);
					}
				}
			}
			// 重算总金额
			// this.basicOrderData.amount = this.floatMinus(this.basicOrderData.amount, deleteAmount);
			// this.$refs.basic.updateAmount(this.basicOrderData.amount)

		},
    detailDisable() {
      if(this.detailSelect.length == 0) {
        this.$message.error('请选择数据!')
        return
      }
      if(this.detailSelect.length > 1)
      {
        this.$message.error('不能进行批量禁用或启用!')
        return
      }
      console.log("the  detailSelect === %d",this.detailSelect[0].id);
      console.log("the  forbidden_status === %d",!(this.detailSelect[0].forbidden_status));
      let problem_data;
      if(this.detailSelect[0].forbidden_status)
      {
            problem_data = {id:this.detailSelect[0].id,forbidden_status:0};
      }
      else {
           problem_data = {id:this.detailSelect[0].id,forbidden_status:1};
      }

      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/disableOperationLine', problem_data,d=>{
        if(d.body.result){
          let resultData;
          resultData = d.body.result

          // this.detailsData = d.body.content.questionList || [];
          if(resultData)
          {
            console.log("the resultData =============:");
            console.log(resultData);
            if(this.detailSelect[0].forbidden_status)
            {
              this.detailSelect[0].forbidden_status = 0;
            }
            else
            {
              this.detailSelect[0].forbidden_status = 1;
            }
          }
          // this.isSelectOrder = true;
        } else {
          this.$message.error(d.body.msg)
        }
      })
    },
		detailSelection(obj) {
      		this.detailSelect = obj;
		},
		floatAdd(value1, value2) {
			return Number(((value1 * 100 + value2 * 100) / 100).toFixed(4));
		},
		floatMinus(value1, value2) {
			return Number(((value1 * 100 - value2 * 100) / 100).toFixed(4));
		}
	}
}
