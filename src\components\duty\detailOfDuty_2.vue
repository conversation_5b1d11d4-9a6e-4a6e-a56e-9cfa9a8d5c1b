<!--责任分析单详情(是以一个售后单为维度)-->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-col :span="18">
			<el-button type="primary" size="mini" @click="() => getOrderDetail()">刷新</el-button>
		</el-col>
		<el-col :span="6" style="text-align: right;">
			<el-button type="primary" size="mini" @click="nextOrPrevOrder('prev')" :disabled="!params.orderList">上一页</el-button>
			<el-button type="primary" size="mini" @click="nextOrPrevOrder('next')" :disabled="!params.orderList">下一页</el-button>
		</el-col>
	</el-row>
	<el-form :model='form' ref='form' label-position="right" label-width="120px">
	<el-tabs v-model="selectTab1" @tab-click="selectTab1Click" style="max-height: 280px">
		<el-tab-pane label='责任分析子单' name='subList'>
			<el-row class="xpt-top" :gutter="40">
				<el-button type="primary" size="mini" @click="() => _checkSubmitPerson() && subAjax('submitPerson?permissionCode=ANALYSIS_ORDER_SUBMIT_RESPONSIBLE')" :disabled="!(isEdit||ifStaffApprover)">提交责任人</el-button>
				<el-button type="danger" size="mini" @click="subAjax('recall?permissionCode=ANALYSIS_ORDER_WITHDRAW')" :disabled="!(isEdit||ifStaffApprover)">撤回</el-button>
				<el-button
					type="primary"
					size="mini"
					@click="testFinalConfirm"
					disabled
				>最终确认</el-button>
				<xpt-btngroup
					class="mgl10"
					type="primary"
					:btngroup="dealerFinalConfirmBtns"
					:disabled="dealerFinalConfirmDisabled"
					size="mini"
				></xpt-btngroup>
				<!-- <el-button
					type="primary"
					size="mini"
					@click="dealerFinalConfirm"
				>经销责任确认</el-button> -->
				<el-button
					type="primary"
					size="mini"
					@click="subAjax('manualAdjustment',{manual_adjustment:'Y'})"
				>手动调整金额</el-button>
				<!--
					|| !confirmList.length
					|| confirmList.every(obj => obj.status === 'CONFIRMED')
				 -->
				<el-button type="primary" size="mini" @click="finishEvent">终止责任单</el-button>
				<el-button type="primary" size="mini" @click="orderAjax('recalculate?permissionCode=ANALYSIS_ORDER_REDISTRIBUTE',{id:form.subList[subList_selectIndex].id})" :disabled="!isEdit">重新分摊</el-button>

			</el-row>
			<div class="xpt_pmm_scroll scroll" style="height: 200px;">
			<el-table :data="form.subList" @current-change="subListSelectChange" border tooltip-effect="dark" style="width: 100%;" :class="$style['max-table-height']" width='100%' ref="$subList">
				<el-table-column width="55" align='center'>
					<template slot-scope='scope'>
						<el-radio class='xpt-table__radio' :label="scope.$index" v-model="subList_selectIndex"></el-radio>
					</template>
			   	</el-table-column>
				<el-table-column label="责任分析子单编号" prop="sub_bill_no" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="来源单据类型" prop="original_bill_type" width="120" show-overflow-tooltip>
					<template slot-scope="scope">
          {{ original_bill_type_options[scope.row.original_bill_type] || scope.row.original_bill_type }}
					</template>
				</el-table-column>
				<el-table-column label="来源单据编号" prop="original_bill_no" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <a v-if="scope.row.original_bill_type == 'REPAIR'" href="javascript:;" @click="routerJumpToService(scope.row)">{{ scope.row.original_bill_no }}</a>
            <span v-else>{{ scope.row.original_bill_no }}</span>
          </template>
        </el-table-column>
				<el-table-column label="业务员" width="100" show-overflow-tooltip>
					<template slot-scope="scope">{{ form.aftersaleOrder.staff_name }}</template>
				</el-table-column>
				<el-table-column label="是否手动调整金额" width="120" prop="manual_adjustment" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.manual_adjustment =="Y" ? '是':'否'  }}</template>
				</el-table-column>
				<el-table-column label="分摊状态" width="120" prop="fee_status" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.fee_status =="UNFINISHED" ? '未分摊':'已分摊'  }}</template>
				</el-table-column>
				<el-table-column label="售后处理人" prop="aftersale_processor_name" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任状态" prop="liability_status" width="120" show-overflow-tooltip>
					<template slot-scope="scope">{{ liability_status_options[scope.row.liability_status] }}</template>
				</el-table-column>
				<el-table-column label="责任确认时间" prop="liability_confirm_time" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.liability_confirm_time | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="首次确认时间" prop="liability_first_confirm_time" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.liability_first_confirm_time | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="锁定人" prop="locker_name" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="锁定人分组" prop="locker_group" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="子单费用" prop="sub_fee" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="资料提交状态" prop="info_submit_status" width="120" show-overflow-tooltip>
					<template slot-scope="scope">{{ info_submit_status_options[scope.row.info_submit_status] || scope.row.info_submit_status }}</template>
				</el-table-column>
				<el-table-column label="资料请求时间" prop="request_submit_time" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.request_submit_time | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="资料提交时间" prop="info_submit_time" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.info_submit_time | dataFormat1 }}</template>
				</el-table-column>
			</el-table>
			</div>
		</el-tab-pane>
		<el-tab-pane label='问题' name='questionList'>
			<el-row class="xpt-top" :gutter="40">
				<el-col :span="18">
					<el-button type="success" size="mini" @click="saveQuestion"
						:disabled="!isEdit || questionList_selectRow.some(obj => obj.question_type === 'NORMAL') || !questionList_selectRow.length"
					>保存</el-button>
					<el-button-group>
						<el-button type="primary" @click="uploadAction('post')" size="mini">上传</el-button><!-- 取消上传附件关联问题 by 文华 -->
						<el-button type="primary" @click="uploadAction('get')" size="mini">查看附件</el-button>
					</el-button-group>
					<el-button type="primary" size="mini" @click="supplyQuestion" :disabled="!isEdit || !(questionList_selectRow.length === 1 && questionList_selectRow[0].question_type === 'NORMAL')">问题补充</el-button>
					<el-button type="danger" size="mini" @click="delSupplyQuestion" :disabled="!isEdit || !questionList_selectRow.length || questionList_selectRow.some(obj => obj.question_type === 'NORMAL')">删除</el-button>
				</el-col>
			</el-row>
      <div style="height: 200px;">
			<el-table
				:data="questionList"
				@selection-change="questionListSelectChange"
				border
				tooltip-effect="dark"
				style="width: 100%;"
				width='100%'
				@row-click="row => $refs.$questionList.toggleRowSelection(row)"
				:row-class-name="row => row.supply_question_id ? $style['sub-question'] : ''"
				:class="$style['max-table-height']"
				ref="$questionList"
			>
				<!-- <el-table-column width="100" align='center'>
					<template slot-scope='scope'>
						<el-radio :class="'xpt-table__radio ' + (scope.row.supply_question_id ? $style['sub-question'] : '')" :label="scope.$index" v-model="questionList_selectIndex"></el-radio>
					</template>
							   	</el-table-column> -->
			   	<el-table-column type="selection" width="30" align="center"></el-table-column>
           <el-table-column label="研发分类" prop="developType" width="80" show-overflow-tooltip></el-table-column>
			   	<el-table-column label="买家昵称" prop="buyer_nick" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="商品编码" prop="goods_code" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <a href="javascript:;" @click="routerJumpToGoodsCode(scope.row)">{{ scope.row.goods_code }}</a>
          </template>
        </el-table-column>
				<el-table-column label="商品名称" prop="goods_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="规格描述" prop="specification" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="问题描述" prop="question_description" width="200" show-overflow-tooltip>
					<template slot-scope="scope">
						<div style="position:relative;">
							<el-input
								style="position:absolute;width:100%;"
								size='mini'
								v-model="scope.row.question_description"
								:disabled="scope.row.question_type === 'NORMAL'"
								@click.native="e => e.stopPropagation()"
							></el-input>
							<span style="color:#fff;">{{ scope.row.question_description }}</span>
						</div>
					</template>
				</el-table-column>
        <el-table-column label="上游业务备注" width="200" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.remark ? scope.row.remark : form.subList[subList_selectIndex].remark }}</template>
				</el-table-column>
				<el-table-column label="发货日期" prop="out_stock_time" width="170" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.out_stock_time | dataFormat1 }}</template>
				</el-table-column>
        <el-table-column label="送货方式" prop="deliver_method" width="130" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.deliver_method | deliverMethod }}</template>
				</el-table-column>
				<el-table-column label="返货类型" v-if="form.subList[subList_selectIndex].original_bill_type == 'RETURNS'" prop="shipping_method" width="230" show-overflow-tooltip></el-table-column>
        <el-table-column label="三包供应商" prop="three_guarantees_supplier_name" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="第三方安装订单号" prop="csp_luban_number" width="170" show-overflow-tooltip>
					<template slot-scope="scope">
								<a href="javascript:void(0)"
									@click="handleCheckLubanInfo(scope.row)">{{scope.row.csp_luban_number}}</a>
							</template>
				</el-table-column>
        <el-table-column label="供应商" prop="suppiler_name" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="服务商" prop="logistics_supplier_name" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="店铺" prop="shop_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="销售单号" prop="sys_trade_no" width="230" show-overflow-tooltip>
          <template slot-scope="scope">
            <a href="javascript:void(0)" @click="handleToSysTrade(scope.row)">{{scope.row.sys_trade_no}}</a>
          </template>
        </el-table-column>
				<el-table-column label="批次单号" prop="batch_trade_no" width="230" show-overflow-tooltip>
          <template slot-scope="scope">
            <a href="javascript:void(0)" @click="handleToBatchTrade(scope.row)">{{scope.row.batch_trade_no}}</a>
          </template>
        </el-table-column>
				<el-table-column label="分单号" prop="outbound_notice_no" width="230" show-overflow-tooltip></el-table-column>
				<el-table-column label="预计到货时间" prop="expect_arrival_time" width="230" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.expect_arrival_time | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="实际到货时间" prop="arrival_goods_time" width="230" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.arrival_goods_time | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="安装时间" prop="install_time" width="230" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.install_time | dataFormat1 }}</template>
				</el-table-column>
        <el-table-column label="批号" prop="lot" width="230" show-overflow-tooltip></el-table-column>
				<el-table-column label="直达承运商" prop="zd_delivery_logistics_company" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="转运承运商" prop="zd_trans_ship_logistics_company" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="中转承运商" prop="zd_change_trains_logistics_company" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="林氏物流单号" prop="ls_logistics_number" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="采购入库日期" prop="instock_date" width="170" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.instock_date | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="问题类型" prop="question_type" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.question_type === 'NORMAL' ? '普通问题' : '补充问题' }}</template>
				</el-table-column>
				<el-table-column label="BOM版本" prop="bom_version" width="100" show-overflow-tooltip></el-table-column>
				<el-table-column label="业务员" prop="saleman_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="units" show-overflow-tooltip></el-table-column>
				<el-table-column label="实际售价" prop="act_price" show-overflow-tooltip></el-table-column>
				<el-table-column label="标准售价" prop="price" show-overflow-tooltip></el-table-column>
				<el-table-column label="业务员分组" prop="saleman_group" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任问题（建议）" prop="zerenwentijianyi" width="200" show-overflow-tooltip>
					<template slot-scope="scope">
						<span>{{scope.row.questionOtherDetailVO? (scope.row.questionOtherDetailVO.liability_type?scope.row.questionOtherDetailVO.liability_type+'-':'')+ (scope.row.questionOtherDetailVO.firstLevelName?scope.row.questionOtherDetailVO.firstLevelName+'-':'')+ scope.row.questionOtherDetailVO.secondLevelName+'-'+scope.row.questionOtherDetailVO.threeLevelName:''}}</span>
					</template>
				</el-table-column>

			</el-table>
      </div>
		</el-tab-pane>
		<el-tab-pane label='责任分析单' name='_index'>
			<el-row class="xpt-top" :gutter="40">
				<el-col :span="18">
					<el-button type="primary" size="mini" @click="orderAjax('lockBatch?permissionCode=ANALYSIS_ORDER_LOCK', [params.id])">锁定</el-button>
					<el-button type="primary" size="mini" @click="passOrder" :disabled="!isEdit">转交</el-button>
					<el-button type="primary" size="mini" @click="orderAjax('close?permissionCode=ANALYSIS_ORDER_CLOSE')" :disabled="!isEdit">关闭</el-button>
					<el-button type="primary" size="mini" @click="orderAjax('recalculate?permissionCode=ANALYSIS_ORDER_REDISTRIBUTE')" :disabled="!isEdit">重新分摊</el-button>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="6">
					<el-form-item label="责任分析单编号">
						<el-input size='mini' :value="form.bill_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="合并订单号">
            <a href="javascript:;" @click="routerJumpToVal">{{ form.aftersaleOrder.merge_trade_no }}</a>
          </el-form-item>
					<el-form-item label="买家昵称">
						<el-input size='mini' :value="form.aftersaleOrder.buyer_nick" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="业务员">
						<el-input size='mini' :value="form.aftersaleOrder.staff_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="业务员分组">
						<el-input size='mini' :value="form.aftersaleOrder.staff_group_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="来源单据类型">
						<el-input size='mini' :value="original_bill_type_options[form.original_bill_type] || form.original_bill_type" :disabled="true"></el-input>
					</el-form-item>
					<!-- <el-form-item label="来源单据日期">
						<el-date-picker
							value=""
							:disabled="true"
							:editable="false"
						    type="date"
						    size="mini">
						</el-date-picker>
					</el-form-item> -->
					<el-form-item label="来源单据处理时间">
						<el-date-picker
							:value="form.original_bill_process_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="结算主体">
						<el-input size='mini' :value="form.aftersaleOrder.settle_entity" :disabled="true"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="售后单号">
						<a href="javascript:;" @click="routerJump">{{ form.aftersaleOrder.after_order_no }}</a>
					</el-form-item>
					<el-form-item label="售后单日期">
						<el-date-picker
							:value="form.aftersaleOrder.after_order_time"
							:disabled="true"
							:editable="false"
						    type="date"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="售后处理人">
						<el-input size='mini' :value="form.aftersale_processor_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="售后处理人分组">
						<el-input size='mini' :value="form.aftersale_processor_group_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="费用总额">
						<el-input size='mini' :value="form.total_fee" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="创建人">
						<el-input size='mini' :value="form.creator_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="关闭人">
						<el-input size='mini' :value="form.closer_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="关闭时间">
						<el-date-picker
							:value="form.close_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="单据状态">
						<el-input size='mini' :value="{ CREATE: '创建', CLOSED: '关闭', }[form.status]" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="责任状态">
						<el-input size='mini' :value="{ WAIT_CONFIRM: '待确认', CONFIRMED: '已确认' }[form.liability_status]" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="责任确认时间">
						<el-date-picker
							:value="form.liability_confirm_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="费用状态">
						<el-input size='mini' :value="{ UNFINISHED: '未完结', FINISHED: '已完结', DIVIDED: '已分摊', }[form.fee_status]" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定人">
						<el-input size='mini' :value="form.locker_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定人分组">
						<el-input size='mini' :value="form.locker_group" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定时间">
						<el-date-picker
							:value="form.lock_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="结算方式">
						<!-- <el-input size='mini' :value="form.aftersaleOrder.settle_entity" :disabled="true"></el-input> -->
						<xpt-select-aux v-model='form.settle_method' aux_name='settle_method' :disabled="true" ></xpt-select-aux>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="是否经销商订单">
						<el-input size='mini' :value="{ Y: '是', N: '否', }[form.if_dealer]" disabled></el-input>
					</el-form-item>
					<el-form-item label="经销商编码">
						<el-input size='mini' :value="form.dealer_customer_number" disabled></el-input>
					</el-form-item>
					<el-form-item label="经销商名称">
						<el-input size='mini' :value="form.dealer_customer_name" disabled></el-input>
					</el-form-item>
					<el-form-item label="订单店铺">
						<el-input size='mini' :value="form.shop_name" disabled></el-input>
					</el-form-item>
          <el-form-item label="业务类型">
            <xpt-select-aux v-model='form.business_type_trade' aux_name='ddywlx' disabled />
					</el-form-item>
				</el-col>
			</el-row>
		</el-tab-pane>
		<el-tab-pane label='子单详细信息' name='_subList_detail'>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="责任分析子单编号">
						<el-input size='mini' :value="form.subList[subList_selectIndex].sub_bill_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="合并订单号">
            <a href="javascript:;" @click="routerJumpToVal">{{ form.aftersaleOrder.merge_trade_no }}</a>
					</el-form-item>
					<el-form-item label="买家昵称">
						<el-input size='mini' :value="form.aftersaleOrder.buyer_nick" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="业务员">
						<el-input size='mini' :value="form.aftersaleOrder.staff_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="业务员分组">
						<el-input size='mini' :value="form.aftersaleOrder.staff_group_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="责任状态">
						<el-input size='mini' :value="liability_status_options[form.subList[subList_selectIndex].liability_status]" :disabled="true"></el-input>
					</el-form-item>
          <el-form-item label="来源单据类型">
						<el-input size='mini' :value="original_bill_type_options[form.subList[subList_selectIndex].original_bill_type] || form.subList[subList_selectIndex].original_bill_type" :disabled="true"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="责任分析单编号">
						<el-input size='mini' :value="form.subList[subList_selectIndex].parent_bill_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="售后单号">
						<a href="javascript:;" @click="routerJump">{{ form.aftersaleOrder.after_order_no }}</a>
					</el-form-item>
					<el-form-item label="售后单日期">
						<el-date-picker
							:value="form.aftersaleOrder.after_order_time"
							:disabled="true"
							:editable="false"
						    type="date"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="售后处理人">
						<el-input size='mini' :value="form.subList[subList_selectIndex].aftersale_processor_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="售后处理人分组">
						<el-input size='mini' :value="form.subList[subList_selectIndex].aftersale_processor_group_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="责任确认时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].liability_confirm_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="来源单据创建时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].original_bill_create_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
          <el-form-item label="来源单据处理时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].original_bill_process_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="锁定人">
						<el-input size='mini' :value="form.subList[subList_selectIndex].locker_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定人分组">
						<el-input size='mini' :value="form.subList[subList_selectIndex].locker_group" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].lock_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="子单费用">
						<el-input size='mini' :value="form.subList[subList_selectIndex].sub_fee" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="提交时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].submit_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="撤回时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].recall_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
          <el-form-item label="创建人">
						<el-input size='mini' :value="form.subList[subList_selectIndex].creator_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="创建时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].create_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="终止时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].end_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
		</el-tab-pane>
    <el-tab-pane label='资料请求信息' name='info_request'>
			<el-row class="xpt-top">
				<el-col :span="18">
					<el-button
						type="primary"
						size="mini"
						@click="openSubInfoRequestDialog('request')"
						:disabled="disableOperateSubOrder || isLock"
					>资料请求</el-button>
					<el-button
						type="primary"
						size="mini"
						@click="openSubInfoRequestDialog('submit')"
						:disabled="disableOperateSubOrder"
					>提交资料</el-button>
					<el-button type="danger" size="mini" @click="openSubInfoRequestDialog('withdrew')" :disabled="disableOperateSubOrder || isLock">撤回资料请求</el-button>
					<el-button type="primary" size="mini" @click="openSubInfoRequestDialog('reject')" :disabled="disableOperateSubOrder || isLock">驳回资料</el-button>
					<el-button type="danger" size="mini" @click="openSubInfoRequestDialog('delete')" :disabled="disableOperateSubOrder || isLock">删除</el-button>
          <el-button type="primary" size="mini"  @click="openSubInfoRequestDialog('sure')"  :disabled="disableOperateSubOrder || isLock">确认资料</el-button>
				</el-col>
			</el-row>
      <el-row style="height:200px;">
      <el-table :data="subInfoRequestList"  @row-click="subInfoRequestRowClick" highlight-current-row fit border tooltip-effect="dark" width='100%' ref="SubOrderOtherInfo" class="info-list-table">
				<el-table-column width="55" prop="index" align='center'>
					<template slot-scope='scope'>
						<el-radio class='xpt-table__radio' :label="scope.$index" v-model="subInfoRequestSelectIndex" @click.native.prevent="(event)=>{event.preventDefault}"></el-radio>
					</template>
			  </el-table-column>
        <el-table-column label="资料提交状态" prop="info_submit_status" width="150" show-overflow-tooltip>
          <template slot-scope="scope">{{ scope.row.info_submit_status | auxFormat('INFO_SUBMIT_STATUS') }}</template>
        </el-table-column>
        <el-table-column label="资料请求时间" prop="request_submit_time" width="165" show-overflow-tooltip>
          <template slot-scope="scope">{{ scope.row.request_submit_time | dataFormat1 }}</template>
        </el-table-column>
        <el-table-column label="资料请求说明" prop="info_require_remark" width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <!-- <div class="info-require-remark-box">{{ scope.row.info_require_remark }}</div> -->
            <el-input
              class="info-require-remark-box"
              type="textarea"
              disabled
              :rows="3"
              placeholder="请输入内容"
              v-model="scope.row.info_require_remark">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="驳回原因" prop="rejection_reason" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column label="资料提交说明" prop="info_submit_remark" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column label="请求接收人" prop="receiver_name" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="请求接收人类型" prop="receiver_type" width="170" show-overflow-tooltip>
					<template slot-scope="scope">{{ receiver_type_option[scope.row.receiver_type] || scope.row.receiver_type }}</template>
				</el-table-column>
        <el-table-column label="资料请求接收人分组" prop="receiver_group_name" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="资料提交时间" prop="info_submit_time" width="165" show-overflow-tooltip>
          <template slot-scope="scope">{{ scope.row.info_submit_time | dataFormat1 }}</template>
        </el-table-column>
			  </el-table>
      </el-row>
		</el-tab-pane>
	</el-tabs>
	</el-form>
	<el-row class='xpt-flex__bottom' id='bottom' v-fold>
		<el-tabs v-model="selectTab2" @tab-click="selectTab2Click">
			<el-tab-pane label='责任人' name='personList' class='xpt-flex'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="18">
						<el-button type="success" size="mini" @click="savePersonList" :disabled="!(isEdit||ifStaffApprover)">保存</el-button>
						<el-button
							type="primary"
							size="mini"
							@click="() => addPerson()"
							:disabled="
								selectTab1 !== 'questionList'
								|| !(isEdit||ifStaffApprover)
								|| !/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status)//子单责任状态必须为待提交或已撤回才能编辑
								|| questionList_selectRow.length !== 1
							"
						>添加责任人</el-button>
						<el-button
							type="danger"
							size="mini"
							@click="delPerson"
							:disabled="
								personList_selectIndex === ''
								|| !(isEdit||ifStaffApprover)
								|| !/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status)"
						>删除</el-button>
						<el-button
							type="primary"
							size="mini"
							@click="copyPerson"
							:disabled="
								selectTab1 !== 'questionList'
								|| !(isEdit||ifStaffApprover)
								|| questionList_selectRow.length < 2"
						>批量填充责任人</el-button>
						<el-button
							type="primary"
							size="mini"
							@click="getScmInfo"
							:disabled="personList_selectIndex.length<=0"
						>添加品质反馈</el-button>
					</el-col>
				</el-row>
				<el-table :data="personList"
				@cell-click = cellSelectChange
        @selection-change="personListSelectChange" ref="personList" border tooltip-effect="dark" style="width: 100%;" width='100%'>

					<el-table-column type="selection" width="50"></el-table-column>
          <el-table-column type="index" width="50" label="序号"></el-table-column>
          <el-table-column label="一级责任主体" prop="tinner_liability_type"  width="150" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.tinner_liability_type }}</template>
					</el-table-column>
					<el-table-column label="二级责任主体" prop="que_sec_name"  width="150" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.que_sec_name }}</template>
					</el-table-column>
					<el-table-column label="故障模块" prop="firstLevelName" width="150" show-overflow-tooltip></el-table-column>
					   	<el-table-column label="故障现象" prop="secondLevelName" width="150" show-overflow-tooltip></el-table-column>
					   <el-table-column label="故障原因" prop="inner_quesd_name" width="150" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="!/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status) || !isEdit">{{ scope.row.inner_quesd_name }}</span>
							<el-input
								v-else
								:value="scope.row.inner_quesd_name"
								size='mini'
								:icon="selectTab1 === 'questionList' ? 'search' : ''"
								readonly
								:on-icon-click="() => addPerson(scope.row)"
								style="width:100%;"
							></el-input><!-- 不在问题页签不能点击icon -->
						</template>
					</el-table-column>
					<el-table-column label="责任人" prop="liability_person_name" width="150" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="!/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status) || !isEdit">{{ scope.row.liability_person_name }}</span>
							<el-input
								v-else
								v-model="scope.row.liability_person_name"
								size='mini'
								:icon="selectTab1 === 'questionList' ? 'search' : ''"
								readonly
								:on-icon-click="() => selectDutyPerson(scope.row.liability_scope, scope.row, scope.row.liability_type)"
								style="width:100%;"
							></el-input><!-- 不在问题页签不能点击icon -->
						</template>
					</el-table-column>
					<el-table-column label="商品编码" prop="goods_code" width="150" show-overflow-tooltip></el-table-column>
					<el-table-column label="商品名称" prop="goods_name" show-overflow-tooltip></el-table-column>
					<el-table-column label="规格描述" prop="specification" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任金额" prop="liability_amount" width="110">
						<template slot-scope="scope">
							<el-input
								size='mini'
								type="number"
								v-model="scope.row.liability_amount"
								style="width:100%;"
								@blur="e => e.target.value = scope.row.liability_amount = Number(Number(scope.row.liability_amount).toFixed(2))"
								:disabled="!isEdit"
							></el-input>
						</template>
					</el-table-column>
					<el-table-column label="处理金额" prop="handle_amount" width="110">
						<template slot-scope="scope">
							<el-input
								size='mini'
								type="number"
								v-model="scope.row.handle_amount"
								style="width:100%;"
								@blur="e => e.target.value = scope.row.handle_amount = Number(Number(scope.row.handle_amount).toFixed(2))"
								:disabled="!isEdit"
							></el-input>
						</template>
					</el-table-column>
					<el-table-column label="备注" prop="remark" width="300" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="!/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status) || !isEdit">{{ scope.row.remark }}</span>
							<el-input v-else v-model="scope.row.remark" type="textarea" :maxlength="255" style="width:100%;height:100%;" :class="$style['row-height']"></el-input>
						</template>
					</el-table-column>
					<el-table-column label="主责/次责" prop="judgment_suggestion" show-overflow-tooltip width="100">
						<!-- <template slot-scope="scope">{{ {
                            'JUDGMENT':'判责',
                            'SUGGESTION':'建议',
                            'PRINCIPAL':'主责',
                            'SECONDARY':'次责',
                        }[scope.row.judgment_suggestion] }}</template> -->
            <template slot-scope="scope">
              <el-select v-model="scope.row.judgment_suggestion" clearable placeholder="请选择" size='mini' style="width: 90px" :disabled="/(CONFIRMED|WAIT_CONFIRM|WAIT_CONDFIRM)/.test(form.subList[subList_selectIndex].liability_status) || !isEdit">
                <el-option
                  v-for="item in judgmentSuggestionOptions"
                  :key="item.value"
                  :label="item.label"
                  :disabled="item.disabled"
                  :value="item.value">
                </el-option>
              </el-select>
            </template>
					</el-table-column>
					<el-table-column label="责任问题描述" prop="liability_question_description" width="250" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任范围" prop="liability_scope" show-overflow-tooltip>
						<template slot-scope="scope">{{ liability_scope_option[scope.row.liability_scope] }}</template>
					</el-table-column>

					<el-table-column label="是否生成绩效单" prop="if_generate_per_sheet"  width="150" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.if_generate_per_sheet | yesOrNo }}</template>
					</el-table-column>
					<el-table-column label="是否生成索赔服务单" width="150" prop="if_create_dispute_bill">
						<template slot-scope="scope">{{ { 1: '是', 0: '否', true: '是', false: '否', }[scope.row.if_create_dispute_bill] }}</template>
					</el-table-column>
					<el-table-column label="责任状态时间" width="100" prop="status_time" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.status_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="责任问题（旧版）" prop="liability_question_name" width="250" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="!/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status) || !isEdit">{{ scope.row.liability_question_name }}</span>
							<el-input
								v-else
								:value="scope.row.liability_question_name"
								size='mini'
								readonly
								:on-icon-click="() => addPerson(scope.row)"
								style="width:100%;"
							></el-input><!-- 不在问题页签不能点击icon -->
						</template>
					</el-table-column>






					<el-table-column label="责任类型（旧版）" prop="liability_type"  width="150"></el-table-column>
					<el-table-column label="责任状态" prop="liability_status" width="150">
						<template slot-scope="scope">{{ liability_status_options[scope.row.liability_status] || scope.row.liability_status }}</template>
					</el-table-column>
          <el-table-column label="责任人分组" prop="liability_person_group_name"  width="150"></el-table-column>

					<!-- <el-table-column label="责任类型编码" prop="liability_type_code"></el-table-column> -->




					<!-- <el-table-column label="处理状态" prop="handle_status"></el-table-column>
					<el-table-column label="处理时间" prop="handle_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.handle_time | dataFormat1 }}</template>
					</el-table-column> -->
					<!-- <el-table-column label="责任确认方式" prop="confirm_way" width="150">
						<template slot-scope="scope">{{ confirm_way_options[scope.row.confirm_way] || scope.row.confirm_way }}
						</template>
					</el-table-column> -->
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='费用明细' name='detailList' class='xpt-flex'>
				<el-table :data="form.detailList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column type="index" width="50" label="序号"></el-table-column>
					<el-table-column label="费用项目" prop="fee_item" show-overflow-tooltip></el-table-column>
					<el-table-column label="费用估算" prop="fee_estimate" show-overflow-tooltip></el-table-column>
					<el-table-column label="费用金额" prop="fee_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="调整金额" prop="adjust_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="发生日期" prop="happen_date" width="150" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.happen_date | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="来源单据类型" prop="original_bill_type" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ original_bill_type_options[scope.row.original_bill_type] || scope.row.original_bill_type }}
						</template>
					</el-table-column>
					<el-table-column label="来源单据编号" prop="original_bill_no" width="150" show-overflow-tooltip></el-table-column>
					<!-- <el-table-column label="来源单据行号" prop="original_bill_row" show-overflow-tooltip></el-table-column> -->
					<el-table-column label="归集单据类型" prop="collect_bill_type" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ collect_bill_type_options[scope.row.collect_bill_type] || scope.row.collect_bill_type }}
						</template>
					</el-table-column>
					<el-table-column label="归集单据编号" prop="collect_bill_no" width="150" show-overflow-tooltip></el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='申诉记录' name='confirmHisttoryList' class='xpt-flex'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="18">
						<el-button type="success" size="mini" @click="commitPass('create')" :disabled="!canISubmitComplaints">提交申诉</el-button>
						<el-button type="success" size="mini" @click="confirmPass('pass')" >通过</el-button>
						<el-button type="success" size="mini" @click="confirmPass('nopass')" >不通过</el-button>
					</el-col>
				</el-row>
				<el-table @row-click="selectHisttory"	 :data="confirmHisttoryList" border tooltip-effect="dark" style="width: 100%;" @current-change="handleCurrentChange" width='100%'>
					<el-table-column
					align='center'
					width="40">
						<template slot-scope='scope'>
							<el-radio-group v-model="radioSelect">
							<el-radio
								:label="scope.row"
								class='xpt-table__radio'>
							</el-radio>
							</el-radio-group>
						</template>
					</el-table-column>
					<el-table-column label="申诉来源" prop="appeal_from" width="200" show-overflow-tooltip>
						<template slot-scope="scope">
								{{ {supplier:'供应商',quality:'品质',customer_service:'客服',logistics:'物流'}[scope.row.appeal_from] || scope.row.appeal_from }}
							</template>
					</el-table-column>

					<el-table-column label="申诉人" prop="appeal_person_name" width="120" show-overflow-tooltip></el-table-column>
					<!-- <el-table-column label="状态" prop="status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ {UN_CONFIRM: '未确认',CONFIRMED: '已确认',COMPLAIN: '申诉'}[scope.row.status] || liability_status_options[scope.row.status] ||  scope.row.status }}</template>
					</el-table-column> -->
					<!-- <el-table-column label="回复时间" prop="reply_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.reply_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="撤回状态" prop="revoke_status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ {N: '无撤回',Y: '需要撤回',C: '执行撤回成功'}[scope.row.revoke_status] || scope.row.revoke_status }}</template>
					</el-table-column>
					<el-table-column label="撤回结果" prop="revoke_result" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任确认方式" prop="confirm_way" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ confirm_way_options[scope.row.confirm_way] }}</template>
					</el-table-column> -->
					<!-- <el-table-column label="备注" prop="remark" width="120" show-overflow-tooltip></el-table-column> -->
					<el-table-column label="申诉时间" prop="appeal_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.appeal_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="申诉理由" prop="appeal_reason" show-overflow-tooltip></el-table-column>
					<el-table-column label="申诉资料" show-overflow-tooltip>
						<template slot-scope="scope">
							<el-button
							type="primary"
							size="mini"
							@click="getSubAppealFiles(scope.row)"
						>查看</el-button>
						</template>
					</el-table-column>
					<el-table-column label="上级审批人" prop="superior_approver" show-overflow-tooltip></el-table-column>
					<el-table-column label="上级审批时间" prop="superior_appr_time" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.superior_appr_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="上级审批结果" prop="superior_appr_result" show-overflow-tooltip>
						<template slot-scope="scope">
								{{ {pass:'通过',nopass:'不通过',HANDLED_BY_MANAGER:'主管待处理',HANDLED_BY_STAFF:'待专员处理'}[scope.row.superior_appr_result] || scope.row.superior_appr_result }}
							</template>
					</el-table-column>
					<el-table-column label="上级审批意见" prop="superior_appr_opinion" show-overflow-tooltip>
						<template slot-scope="scope">
								{{ {pass:'通过',nopass:'不通过',HANDLED_BY_MANAGER:'主管待处理',HANDLED_BY_STAFF:'待专员处理'}[scope.row.superior_appr_opinion] || scope.row.superior_appr_opinion }}
							</template>
					</el-table-column>
					<el-table-column label="专员处理意见" prop="staff_appr_opinion" show-overflow-tooltip>
						<template slot-scope="scope">
								{{ {pass:'通过',nopass:'不通过',HANDLED_BY_MANAGER:'主管待处理',HANDLED_BY_STAFF:'待专员处理'}[scope.row.staff_appr_opinion] || scope.row.staff_appr_opinion }}
							</template>
					</el-table-column>
					<el-table-column label="专员处理资料" show-overflow-tooltip>
						<template slot-scope="scope">
							<el-button
							type="primary"
							size="mini"
							@click="getSubFiles(scope.row)"
						>查看</el-button>
						</template>
					</el-table-column>
					<el-table-column label="专员审批结果" prop="staff_appr_result" show-overflow-tooltip>
						<template slot-scope="scope">
								{{ {pass:'通过',nopass:'不通过',HANDLED_BY_MANAGER:'主管待处理',HANDLED_BY_STAFF:'待专员处理'}[scope.row.staff_appr_result] || scope.row.staff_appr_result }}
							</template>
					</el-table-column>
					<el-table-column label="专员审批人" prop="staff_approver" show-overflow-tooltip></el-table-column>
					<el-table-column label="专员审批时间" prop="staff_appr_time" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.staff_appr_time | dataFormat1 }}</template>
					</el-table-column>

				</el-table>
			</el-tab-pane>
			<el-tab-pane label='操作记录' name='operateLogList' class='xpt-flex'>
					<el-table :data="operateLogList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
						<el-table-column label="序号" type="index" width="70">
							<template slot-scope="scope"><div class="table-index">{{ scope.$index + 1 }}</div></template>
						</el-table-column>
						<el-table-column label="用户" 		prop="operator_name" width="200" show-overflow-tooltip></el-table-column>
						<el-table-column label="业务操作" 	prop="operate_type" width="200" show-overflow-tooltip>
							<template slot-scope="scope">
								{{ operate_type_options[scope.row.operate_type] || scope.row.operate_type }}
							</template>
						</el-table-column>
						<el-table-column label="操作描述" 	prop="description" width="400" show-overflow-tooltip></el-table-column>
						<el-table-column label="操作时间"   prop="operate_time" width="200" show-overflow-tooltip>
							<template slot-scope="scope">
								<span>{{scope.row.operate_time | dataFormat1}}</span>
							</template>
						</el-table-column>
					</el-table>
			</el-tab-pane>
			<el-tab-pane label='责任确认通知' name='confirmList' class='xpt-flex'>
				<el-table :data="confirmList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column label="责任通知编号" prop="confirm_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="批次订单号" prop="batch_trade_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任范围" prop="liability_scope" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ liability_scope_option[scope.row.liability_scope] || scope.row.liability_scope }}</template>
					</el-table-column>
					<!-- <el-table-column label="责任类型" prop="liability_type" width="120" show-overflow-tooltip></el-table-column> -->
					<el-table-column label="责任人" prop="liability_person_name" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="状态" prop="status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ {UN_CONFIRM: '未确认',CONFIRMED: '已确认',COMPLAIN: '申诉'}[scope.row.status] || liability_status_options[scope.row.status] ||  scope.row.status }}</template>
					</el-table-column>
					<el-table-column label="回复时间" prop="reply_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.reply_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="撤回状态" prop="revoke_status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ {N: '无撤回',Y: '需要撤回',C: '执行撤回成功'}[scope.row.revoke_status] || scope.row.revoke_status }}</template>
					</el-table-column>
					<el-table-column label="撤回结果" prop="revoke_result" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任确认方式" prop="confirm_way" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ confirm_way_options[scope.row.confirm_way] }}</template>
					</el-table-column>
					<el-table-column label="备注" prop="remark" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="回传时间" prop="back_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.back_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="回传备注" prop="reply_remark" width="250" show-overflow-tooltip></el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='责任下发记录' name='recordList' class='xpt-flex'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="18">
						<el-button type="primary" size="mini" @click="confirmResend">重新传送</el-button>
					</el-col>
				</el-row>
				<el-table :data="recordList" border tooltip-effect="dark" style="width: 100%;" width='100%' @selection-change="handleSelectionChange">
					<el-table-column type="selection" width="50"></el-table-column>
					<el-table-column label="责任确认通知编号" prop="confirm_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="批次订单号" prop="batch_trade_no" width="240" show-overflow-tooltip></el-table-column>
					<el-table-column label="接口状态" prop="api_status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ api_status_options[scope.row.api_status] || scope.row.api_status }}</template>
					</el-table-column>
					<el-table-column label="下达时间" prop="transmit_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.transmit_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="传送结果提示" prop="transmit_result" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ JSON.parse(scope.row.transmit_result || '{}').info }}
						</template>
					</el-table-column>
					<el-table-column label="查看附件" show-overflow-tooltip>
						<template slot-scope="scope">
							<a
								v-bind:href="
									'javascript:window.open(`http://'
										+ K3LiabilityAnalysisPicHost + '/Modules/AftrSale/K3LiabilityAnalysisPic.html?'
										+ 'fbillno=' + JSON.parse(scope.row.transmit_result || '{}').content
										+ '&time=`+(+new Date));'
								"
							>{{ JSON.parse(scope.row.transmit_result || '{}').content }}</a>
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='分摊日志' name='recalculateList' class='xpt-flex'>

				<el-table :data="recalculateList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column type="selection" width="50"></el-table-column>
					<el-table-column label="责任分析子单编号" prop="sub_no" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="分摊结果" prop="result" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ result_options[scope.row.result] || scope.row.result }}</template>
					</el-table-column>
					<el-table-column label="日志生成时间" prop="create_time" width="220" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.create_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="未执行分摊的原因" prop="reason"  show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.reason  }}</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='品质反馈' name='feedbackList' class='xpt-flex'>
				<xpt-list
				:data="feedbackList"
				:btns="feedbackBtns"
				:colData="feedbackCols"
				selection="radio"
				:orderNo="true"
				@search-click='feedbackSearchClick'
				@radio-change='feedbackSelectionChange'
			></xpt-list>
			</el-tab-pane>
      <el-tab-pane label='责任人修改记录' name='responsibilityChangeLog' class='xpt-flex'>
        <xpt-list
          :data="responsibilityChangeList"
          :colData="responsibilityChangeCols"
          :orderNo="true"
          :btns="[]"
          selection="none"
			  >
          <el-text slot="btns">
            保存责任人后，会更新责任人修改记录。如果是删除后添加新责任人则会有多条记录。如果是在原行上修改再保存则展示修改后的内容。
          </el-text>
          <el-button slot="btns"  type="primary" size="mini" @click="handleResponsibilityChangeExport" :loading="responsibilityChangeExportLoading">导出</el-button>
        </xpt-list>
      </el-tab-pane>
		</el-tabs>
	</el-row>
	<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload>
	<xpt-image
            :images="imagesList"
            :show="ifShow"
            :ifUpload="false"
            @close="closeFun">
        </xpt-image>
</div>
</template>

<script>
//from里面的数组字段值的意思
//aftersaleOrder=>费用明细列表
//detailList=>费用明细列表
//confirmList=>
//detailList=>费用明细列表
//personList=>责任人
//questionList=>问题列表
//recordList=>下发记录
//subList=>
import Vue from 'vue'
import Fn from '@common/Fn.js'
import data from '../after_sales_service/data';
import infoRequestMixin from './mixins/info-request'
import responsibilityMixin from './mixins/responsibility-change-log'; //责任人修改记录
export default {
	props:['params'],
  mixins:[infoRequestMixin,responsibilityMixin],
	data (){
		let self = this;
		return {
      judgmentSuggestionOptions: [
        { label: '判责', value:'JUDGMENT', disabled:true},
        { label: '建议', value:'SUGGESTION', disabled:true},
        { label: '主责', value:'PRINCIPAL'},
        { label: '次责', value:'SECONDARY'},
      ],
			radioSelect:'',
			imagesList:[],
			ifStaffApprover:false,
			isEdit: false,//锁定人 = 操作人 或代理人关系 或锁定人分组的管理人
			ifClickUpload: false,
			ifShow: false,
			uploadData: {},
			api_status_options: {
				NO_NEED: '无需下达',
		        WAIT: '等待',
		        TRANSMITING: '下达中',
		        TRANSMITTED: '已下达',
		        TRANSMIT_FAIL: '下达失败',
		        RECALLING: '撤回中',
		        RECALLED: '已撤回',
		        RECALL_FAIL: '撤回失败',
		        CALLBACK: '已回传',
			},
			result_options:{
				SUCCESS:'分摊成功',
				FAILE:'未执行分摊',
			},
			receiver_type_option:{AFTERSALE_STAFF:'售后处理人',STAFF:'业务员'},
			info_submit_status_options: {
				NO_NEED: '无需提供',
		        WAIT_PROVIDE: '等待提交',
		        PROVIDED: '提交完成',
		        CONFIRMED: '确认',
		        OVERTIME: '超时交馈',
			},
			liability_status_options: {
				APPEAL: '申诉',
				WAIT_SUBMIT: '待提交',
		        WAIT_CONFIRM: '待确认',
		        COMPLAIN: '申诉',
		        END: '终止',
		        WAIT_REVOKE: '待撤回',
		        RECALLING: '撤回中',
		        RECALLED: '已撤回',
		        RECALL_FAIL: '撤回失败',
				WAIT_CONDFIRM: '待确认',
		        CONFIRMED: '已确认',
			},
			original_bill_type_options: {
				RETURNS: '退换货跟踪单',
				SUPPLY: '补件单',
				REFUND: '退款单',
				AFTERSALES: '售后单',
				AFTERSALE_ORDER: '售后单',
				REPAIR: '服务单',
				SERVICE: '4PL平台服务单',
        GIFT:'售后赠品单',
				RETURNS_BILL: '退换货跟踪单',
				SUPPLY_BILL: '补件单',
				REFUND_BILL: '退款单',
				AFTERSALES_BILL: '售后单',
				AFTERSALE_ORDER_BILL: '售后单',
				REPAIR_BILL: '服务单',
				SERVICE_BILL: '4PL平台服务单',
        GIFT_PURCHASE: '赠品采购单',
        GIFT_DELIVERY: '赠品发货单',
				OUTBOUNDSUPPLY: '补件其他出库单',
				ANALYSIS: '责任分析单',
				BATCH: '批次订单',
				BOUGHT_SUPPLY: '采购订单',
				REFUND_NEW: '新退款单',

                QUALITY_FEEDBACK: '品质反馈单',
                QUALITY_ORDER: '品质反馈单',
			},
			liability_scope_option: {
				BD_Empinfo: '员工',
				BD_Supplier: '供应商',
				BD_Customer: '客户',
				BD_Department: '部门',
			},
			confirm_way_options: {
				NORMAL: '正常',
				OVERTIME: '超时',
				JUDGE: '裁定',
			},
			collect_bill_type_options: {
				REFUND_APPLY_BILL: '退款申请单',
		        RETURNS_FOLLOW_BILL: '退货跟踪单',
		        OTHER_DELIVERY_BILL: '其它出库单',
		        SERVICE_BILL: '服务单',

		        RETURNS_BILL: '退换货跟踪单',
				SUPPLY_BILL: '补件单',
				REFUND_BILL: '退款单',
				AFTERSALES_BILL: '售后单',
				AFTERSALE_ORDER_BILL: '售后单',
				REPAIR_BILL: '服务单',

				/*自译*/
				REFUND_APPLY: '退款申请单',
		        RETURNS_FOLLOW: '退货跟踪单',
		        OTHER_DELIVERY: '其它出库单',
		        SERVICE: '服务单',
		        RETURNS: '退换货跟踪单',
				SUPPLY: '补件单',
        GIFT:'售后赠品单',
				REFUND: '退款单',
				AFTERSALES: '售后单',
				AFTERSALE_ORDER: '售后单',
				REPAIR: '服务单',
				REFUND_NEW: '新退款单',
			},
			operate_type_options: {
				CREATE: '新增',
				SAVE: '保存',
				LOCK: '锁定',
				UNLOCK: '解锁',
				SUBMIT: '提交',
				RETRACT: '撤回',
				AUDIT: '审核',
				CHANGE: '确认变更',
				REVERSE_AUDIT: '反审核',
				SUBMIT_APPROVE: '提交审批',
				PASS: '审批通过',
				NOT_PASS: '审批不通过',
				TRACK: '执行跟踪',
				RECALL: '撤回仓储',
				CLOSE: '关闭',
				OPEN: '反关闭',
				CANCEL: '已取消',
				//extra
				ADD_REFUND_ITEM: '引入退款明细',
				DELETE_REFUND_ITEM: '删除退款明细',
				ADD_TAOBAO_REFUND: '引入淘宝退款申请',
				DELETE_TAOBAO_REFUND: '删除淘宝退款申请',
				SUBMITPURCHASE: '提交采购',
				PLAN_RETRACT: '方案撤回',
				REJECT: '驳回',
				LOCKER: '锁定',
				REVOCATION: '撤回',
				VERIFY: '审核',
				OPPOSITE_VERIFY: '反审核',
				SUBMIT_EXAMINE: '提交审批',
				PASS_EXAMINE: '审批通过',
				UNPASS_EXAMINE: '审批不通过',
			},
			K3LiabilityAnalysisPicHost: {
				'salesit.linshimuye.com': '************:8082',
				'sit.sale.linshimuye.com': '************:8082',
				'saleuat.linshimuye.com': '************:8086',
				'sale.linshimuye.com': '***********:8082',
			}[window.location.hostname],

			selectTab1: 'subList',
			selectTab2: 'personList',
			subList_selectIndex: 0,//责任分析子单的下标
			personList_selectIndex: [],//选择责任人的下标
			recordList_selectIndex: [],//责任下发记录下标
			questionList_selectRow: [],

			form: {
				aftersaleOrder: {},
				subList: [{}],
			},
			questionList: [],
			personList: [],
			confirmList: [],//根据子单选择不同显示不同通知
			confirmHisttoryList: [],//根据子单选择不同显示不同通知
			recordList: [],
			recalculateList: [],
			operateLogList: [],

			originalData:null,//储存责任分析单原有数据

			merge_trade_id:null,//合并订单ID

			feedbackList:[],
			feedbackBtns:[
				{
					type: 'danger',
					txt: '删除',
					disabled: () => { return false;},
					loading: false,
					click() {
						self.delFeedback();
					}
				},
				{
					type: 'info',
					txt: '提交SCM',
					disabled: () => { return false;},
					loading: false,
					click() {
						self.sendFeedback();
					}
				},
				{
					type: 'info',
					txt: '撤回',
					disabled: () => { return false;},
					loading: false,
					click() {
						self.revokeFeedback();
					}
				},
			],
			feedbackCols:[
				{
					label: '品质反馈单号',
					width: 180,
					prop: 'quality_feedback_no'
				},
				{
					label: '商品编码',
					width: 120,
					prop: 'goods_code'
				},
				{
					label: '状态',
					width: 120,
					prop: 'status',
					formatter(val){
						switch(val){
							case 'CREATE' :return "创建";							case 'TRANSMIT' :return "已下达";							case 'WITHDRAW' :return "已撤回";						}
					}
				},
				{
					label: '责任问题',
					width: 120,
					prop: 'liability_question'
				},
				{
					label: '组别',
					width: 120,
					prop: 'group_name'
				},
				{
					label: '产品类型',
					width: 120,
					prop: 'product_type'
				},
				{
					label: '售后物料',
					width: 120,
					prop: 'aftersale_materials'
				},
				{
					label: '售后情况',
					width: 120,
					prop: 'materials_damage'
				},
				{
					label: '备注',
					width: 180,
					prop: 'remarks'
				},

				{
					label: '创建时间',
					width: 100,
					prop: 'create_time',
					format:'dataFormat1'
				},
				{
					label: '提交时间',
					width: 100,
					prop: 'submit_time',
					format:'dataFormat1'
				},


			],
			feedbackSelect:null,
			dealerFinalConfirmDisabled:false,
			dealerFinalConfirmBtns: [
				// {
				// 	type: "primary",
				// 	txt: "经销责任确认",
				// 	click(){
				// 		self.dealerFinalConfirm()
				// 	},
				// },
				{
					type: "primary",
					txt: "重新确认责任",
					click(){
						self.dealerSubReConfirm()
					},
			}]
		}
	},
  computed: {
    // 是否能点击提交申诉
    canISubmitComplaints() {
      const currentOrder = this.form.subList[this.subList_selectIndex]
      const {liability_status} = currentOrder;
      return liability_status&&['CONFIRMED'].includes(liability_status)
    },
  },
	methods: {
    personListSelectChange(data) {
      this.personList_selectIndex = data
    },
	cellSelectChange(row){
		this.$refs.personList.toggleRowSelection(row);
	},
    handleToSysTrade(row) {
      let params = {};
      params.sys_trade_id = row.sys_trade_id;
      this.$root.eventHandle.$emit('creatTab', {
        name: "销售订单详情",
        params: params,
        component: () => import('@components/order/detail.vue')
      });
    },
    handleToBatchTrade(row) {
			var params = {};
			params.batch_trade_id = row.batch_trade_id;
			params.merge_trade_id = row.merge_trade_id;
			params.merge_trade_no = row.merge_trade_no;
			params.order_list = [];
			this.$root.eventHandle.$emit('creatTab',{
				name:"批次订单详情",
				params:params,
				component: () => import('@components/order/batch_order.vue')
			});
    },
		closeFun() {
            this.ifShow = false;

        },
		pictureFun() {
			var params = {
				parent_no : this.form.after_order_no,//售后单号
				//child_no:this.form.bill_returns_no,
				child_no:null,
				ext_data : null,
				parent_name : '',
				child_name:'',
				//child_name:null,
				list:this.imagesList,

				parent_name_txt: '责任记录',
				//child_name_txt:'退换货申请单',
				// notNeedDelBtn:this.submitDisabled,
				// nickname: this.form.buyer_name,
				// mergeTradeId: this.form.merge_trade_id,
				//permissionCode:'RETURN_TRACKING_ORDER_ATTACHMENT_VIEW'//权限控制的
			};
			params.callback = d=>{
			}
			this.$root.eventHandle.$emit('alert',{
				params:params,
				component:()=>import('@components/duty/afterSale_aboutZD_download.vue'),
				style:'width:1000px;height:600px',
				title:'下载列表'
			});
		},
		handleCheckLubanInfo(row){

            this.ajax.get("/order-web/api/batchtrade/getLubanInfo?lubanNumber="+ row.csp_luban_number, (res) => {
				if(res.body.result){
					this.$root.eventHandle.$emit('alert', {
						params: {
							info: res.body.content
						},
						component: () => import('@components/order/lubanInfo.vue'),
						style: 'width:1000px;height:560px',
						title: '安装信息查看'
					})
				}else{
					this.$message.error(res.body.msg)
				}

            })
		},
	// 终止责任单
	finishEvent(){
		this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/endSub',{
			id:this.form.subList[this.subList_selectIndex].id
		} , res => {
			if(res.body.result){
				this.$message({message: '操作成功',type: 'success'});
				this.getOrderDetail()
			}else{
				this.$message({message: res.body.msg,type: 'error'});
			}
		});
	},
    // 选择请求资料接收人
    selectReceiver(){
      let self = this;
      let subObj = self.form.subList[self.subList_selectIndex];
      // 校验权限：是否锁定人、资料提交状态是否无需提供
      if (!self.isEdit || !/(NO_NEED)/.test(subObj.info_submit_status)){
          return ;
      }
      let aftersaleOrder = self.form.aftersaleOrder;
      this.$root.eventHandle.$emit("alert", {
        component: () =>
            import("@components/duty/receiverSelectAlert.vue"),
        close: function () {},
        style: "width:600px;height:300px",
        title: "请选择请求接收人",
        params: {
          aftersaleOrder: aftersaleOrder,
          subObj: subObj,
          callback(d) {
            console.log(d)
            subObj.receiver_id = d.receiver_id;
            subObj.receiver_name = d.receiver_name;
            subObj.receiver_type  = d.receiver_type;
            self.$message.success("选择请求接收人成功");
          },
        },
      });
    },
		delFeedback(){
			let self = this;
			if(!self.feedbackSelect){
				self.$message.error("请选择对应品质反馈");
				return;
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/delete',[self.feedbackSelect.id] , res => {
					if(res.body.result){
						// this.feedbackList = res.body.content || [];
						self.feedbackSearchClick();
						this.$message({message: '操作成功',type: 'success'});
						// self.openInfo(res.body.content,personId);
					}else{
						this.$message({message: res.body.msg,type: 'error'});
					}
				});
		},
		sendFeedback(){
			let self = this;
			if(!self.feedbackSelect){
				self.$message.error("请选择对应品质反馈");
				return;
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/send',self.feedbackSelect.id , res => {
					if(res.body.result){
						// this.feedbackList = res.body.content || [];
						self.feedbackSearchClick();
						this.$message({message: '操作成功',type: 'success'});
						// self.openInfo(res.body.content,personId);
					}else{
						this.$message({message: res.body.msg,type: 'error'});
					}
				});
		},
		revokeFeedback(){
			let self = this;
			if(!self.feedbackSelect){
				self.$message.error("请选择对应品质反馈");
				return;
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/revoke',self.feedbackSelect.id , res => {
					if(res.body.result){
						// this.feedbackList = res.body.content || [];
						self.feedbackSearchClick();
						this.$message({message:  res.body.msg,type: 'success'});
						// self.openInfo(res.body.content,personId);
					}else{
						this.$message({message: res.body.msg,type: 'error'});
					}
				});
		},
		getScmInfo(){
			let self = this;
			// console.log(this.personList_selectIndex);
			if(this.personList_selectIndex.length<=0){
				self.$message.error("请选择一行责任人");
				return false;
			}
      if(this.personList_selectIndex.length>1){
				self.$message.error("只能选择一行责任人");
				return false;
			}
			let personId = this.personList_selectIndex[0].id
			if(!personId){
				self.$message.error("当前责任人未保存，请保存后再操作");
				return false;
			}

			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/getScmInfo', personId , res => {
					if(res.body.result){
						// this.feedbackList = res.body.content || [];
						self.openInfo(res.body.content,personId);
					}else{
						self.$message.error(res.body.msg||'获取品质反馈失败');
					}
				});
		},
		openInfo(data,personId){
			this.$root.eventHandle.$emit('alert',{
				title: '品质反馈',
				style:'width:800px;height:400px',
				component:()=>import('@components/duty/scmInfo'),
				params: {
					data:data,
					personSelect :this.personList_selectIndex[0] ,
					id:this.form.subList[this.subList_selectIndex].id,
					callback: d => {
						console.log(d);
					}
				},
			})
		},
		feedbackSearchClick(){
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/list', this.form.subList[this.subList_selectIndex].id , res => {
					if(res.body.result){
						this.feedbackList = res.body.content || []
					}
				})
		},
		feedbackSelectionChange(row){
			this.feedbackSelect = row;
		},

		selectTab2Click (){
			if(this.selectTab2 === 'operateLogList' && this.form.subList[this.subList_selectIndex].id){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.form.subList[this.subList_selectIndex].id }, res => {
					if(res.body.result){
						this.operateLogList = res.body.content || []
					}
				})
			}else if(this.selectTab2 === 'recalculateList'){
				this.getRecalculateList();
			}else if(this.selectTab2 === 'feedbackList'){
				this.feedbackSearchClick();
			}else if(this.selectTab2 == 'confirmHisttoryList'){
				this.grtConfirmHisttoryList(this.form.subList[this.subList_selectIndex].id);
			}else if(this.selectTab2 == 'responsibilityChangeLog'){
        this.getResponsibilityChangeList();
      }
		},
		handleSelectionChange (data){
			this.recordList_selectIndex = data
		},
		// 提交责任人前的检查
		_checkSubmitPerson (){
			var msg
			,	question_sub_ids = this.personList.map(obj => {
				if(!obj.id){
					msg = '请先保存责任人页签'
				}
				return obj.question_sub_id
			})

			!msg && this.questionList.some((obj, index) => {
				if(obj.supply_question_id && !obj.id){
					msg = '请先保存问题页签'
					return true
				}else if (!obj.supply_question_id){//只对子问题进行判断
					// if(!obj.supply_question_id && (this.questionList[index + 1] || {}).supply_question_id !== obj.parent_question_id && question_sub_ids.indexOf(obj.id) === -1){

					if(!((!obj.supply_question_id && question_sub_ids.indexOf(obj.id) > -1) || ((this.questionList[index + 1] || {}).supply_question_id === obj.parent_question_id && question_sub_ids.indexOf(this.questionList[index + 1].id) > -1))){
						// msg = '所有子问题都有责任人，如果问题进行了问题补充，则不需要指定责任人。'
						msg = '所有子问题或其补充问题至少要一个责任人。'
						return true
					}
				}
			})

			// this.recordList.some(obj => {
			// 	if(obj.api_status !== 'RECALLED'){
			// 		msg = '已生成的责任下发记录接口状态都要“已撤回”'
			// 		return true
			// 	}
			// })

			if(msg){
				this.$message.error(msg)
				return false
			}else {
				return true
			}
		},
    routerJump(){
      console.log(this.form,'=this.form====');
      this.$root.eventHandle.$emit('creatTab', {
        name:"售后单详情",
        params:{
          id: this.form.aftersaleOrder.id,
          idList: [],//用于售后单详情的上一页/下一页
        },
        component: () => import('@components/after_sales/index')
      });
    },
    // 跳转服务单详情
    routerJumpToService(detail){
      this.$root.eventHandle.$emit('creatTab', {
          name:  '服务单详情',
          params: {
            ...detail,
            id: detail.original_bill_id,
          },
          component: () => import('@components/after_sales_service/detail.vue')
        })
    },
    // 跳转商品编码
    routerJumpToGoodsCode(detail){
      console.log(detail,'=detail====');
      console.log(detail.material_id,'=detail.material_id====');
      this.$root.eventHandle.$emit('creatTab', {
          name: '编辑物料信息',
          params:{
          id: detail.mdmMaterialId,
          readonly:true,
        },
          component: () => import('@components/mdm/module/material/AddOrUpdate.vue')
        });
    },
    //
    routerJumpToVal(){
      this.$root.eventHandle.$emit('creatTab', {
        name:"合并订单详情",
        params:{
          merge_trade_id:this.form.merge_trade_id,
        },
        component: () => import('@components/order/merge.vue')
      });
    },
		// 保存问题页签数据
		saveQuestion (){
			// if(this.questionList_selectRow.length !== 1 || this.questionList_selectRow[0].question_type === 'NORMAL'){
			// 	this.$message.error('请选择至少一个补充问题')
			// }else {
			// }
			var self = this
			,	errMsg = []
			,	successMsg = '';

			_loopSave([].concat(this.questionList_selectRow))
			function _loopSave (array){
				var question = array.shift()
				,	urlName
				,	postData

				if(!question){
					if(errMsg.length){
						self.$message.error(errMsg.join())
					}
					if(successMsg){
						self.$message.success(successMsg)
					}

					// successMsg && self.getOrderDetail(successMsg)
				}else {
					if(question.id){
						urlName = 'update'
						postData = {
							id: question.id,
							question_description: question.question_description,
						}
					}else {
						urlName = 'add'
						question.id = question._id
						delete question._id
						postData = {...question}
            delete postData.developType
					}

					self.ajax.postStream('/afterSale-web/api/aftersale/analysis/question/' + urlName + '?permissionCode=ANALYSIS_ORDER_QUESTION_SAVE', postData, res => {
						if(res.body.result){
							successMsg = res.body.msg

							if(urlName === 'add'){
								Object.assign(question, res.body.content)//显示最新问题数据
							}
						}else {
							errMsg.push(res.body.msg)
						}
						_loopSave(array)
					}, err => {
						_loopSave(array)
					})
				}
			}
		},
		// 资料请求btn
		infoReq (){
			var subObj = this.form.subList[this.subList_selectIndex]
			,	msg

			if(!subObj.info_require_remark){
				msg = '子单资料请求说明不能为空'
			}else if (!/(WAIT_SUBMIT|RECALLED)/.test(subObj.liability_status)){
				msg = '子单责任状态必须为待提交或已撤回'
			}else if (!/(NO_NEED|CONFIRMED)/.test(subObj.info_submit_status)){
				msg = '子单资料提交状态必须为无需提供或确认'
			} else if (!subObj.receiver_name){
        msg = '请求接收人不能为空'
      }
		if(!subObj.receiver_type&&subObj.info_submit_status == "NO_NEED" && !!subObj.receiver_name){
                subObj.receiver_type = "AFTERSALE_STAFF";
            }

			if(msg){
				this.$message.error(msg);
			}else {
				this.subAjax('infoReq?permissionCode=ANALYSIS_ORDER_DATA_REQUEST', {
				  info_require_remark: subObj.info_require_remark,
					receiver_type : subObj.receiver_type ,
					receiver_id: subObj.receiver_id,
					receiver_name: subObj.receiver_name
				})
			}
		},
		// 提交资料btn
		submitInfoReq (){
			var subObj = this.form.subList[this.subList_selectIndex]
			,	msg

			if(!subObj.info_submit_remark){
				msg = '子单资料提交说明不能为空'
			}else if (!/(WAIT_SUBMIT|RECALLED)/.test(subObj.liability_status)){
				msg = '子单责任状态必须为待提交或已撤回'
			}else if (subObj.info_submit_status !== 'WAIT_PROVIDE'){
				msg = '子单资料提交状态必须为等待提交'
			}

			if(msg){
				this.$message.error(msg)
			}else {
				this.subAjax('submitInfoReq?permissionCode=ANALYSIS_ORDER_DATA_SUBMIT', { info_submit_remark: subObj.info_submit_remark })
			}
		},
		// 最终确认
		testFinalConfirm (){
			// if(
			// 	!this.recordList.some(obj => {
			// 		if(!/(TRANSMITTED|CALLBACK)/.test(obj.api_status)){
			// 			this.$message.error('下发记录的接口状态必须是“已下达、已回传”')
			// 			return true
			// 		}
			// 	})
			// ){
			// }
			this.subAjax('finalConfirm?permissionCode=ANALYSIS_ORDER_FINAL_CONFIRM')
		},
		dealerFinalConfirm(){
			this.subAjax('dealerSubConfirm')
		},
		dealerSubReConfirm(){
			let data = this.form.subList[this.subList_selectIndex]
			if(data.liability_status!='CONFIRMED'){
				this.$message.error("责任状态需为【已确认】")
				return
			}
			if(data.original_bill_type=='SUPPLY'){
				this.$message.error("来源于补件的经销责任分析单不可重新确认责任")
				return
			}
			this.subAjax('dealerSubReConfirm')
		},
		// 问题补充
		supplyQuestion (){
			var targetQuestion = this.questionList_selectRow[0]
			// if(!targetQuestion || this.questionList_selectRow.length !== 1){
			// 	this.$message.error('请选择一个子问题')
			// 	return
			// }else if (targetQuestion.supply_question_id){
			// 	this.$message.error('不能选择补充问题')
			// 	return
			// }else if (this.questionList.some(obj => !obj.id)){
			// 	this.$message.error('请先保存刚新建的补充问题')
			// 	return
			// }

			this.$root.eventHandle.$emit('alert', {
				component: ()=>import('@components/duty/supplyQuestion?permissionCode=ANALYSIS_ORDER_QUESTION_SUPPLEMENT'),
				style: 'width:80%;height:600px',
				title: '问题补充列表',
				params: {
					merge_trade_id: targetQuestion.merge_trade_id || this.form.aftersaleOrder.merge_trade_id,
					batch_trade_no: targetQuestion.batch_trade_no || this.form.aftersaleOrder.batch_trade_no,
					callback: d => {
						if(d.batch_trade_no != targetQuestion.batch_trade_no){
							this.$message.info('补充的问题与原有问题的批次单号不一致')
						}

						var newQuestion = {
							question_type: 'SUPPLY',//问题类型：补充问题

							_id: targetQuestion.id,//保存时再id: _id,
							supply_question_id: targetQuestion.parent_question_id,
							question_description: '',
							parent_question_id: targetQuestion.parent_question_id,
				            original_question_id: targetQuestion.original_question_id,
				            analysis_sub_id: targetQuestion.analysis_sub_id,
						}
						if(d._type === 'goods'){
							Object.assign(newQuestion, {
								goods_code: d.material_number,
								goods_name: d.material_name,
								buyer_nick: d.customer_name,
								specification: d.material_specification,
								lot: d.lot,
								suppiler_name: d.supplier_company,
								logistics_supplier_name: d.logistics_supplier_name,
								instock_date: d.instock_date,
								bom_version: d.bom_version,
								merge_material_id: d.merge_material_id,
								// real_name: d.real_name,
								num: d.number,
								units: d.material_unit,
								price: d.stand_price,
								saleman_group: d.group_name,
								shop_name: d.shop_name,
								sys_trade_no: d.sys_trade_no,
								batch_trade_no: d.batch_trade_no,
								batch_trade_id: d.batch_trade_id,
								material_id: d.materia_id,
								merge_trade_id: d.merge_trade_id,
								merge_trade_no: d.merge_trade_no,
								shop_id: d.shop_id,
								shop_name: d.shop_name,
								user_shop_id: d.user_shop_id,
								user_shop_name: d.user_shop_name,
								saleman_name: d.real_name,
								act_price: d.act_price,
								source_material_name: d.source_material_name,
								source_material_number: d.source_material_number
							})
						}else if (d._type === 'orders'){
							Object.assign(newQuestion, {
								saleman: d.user_id,
								saleman_name: d.real_name,
								batch_trade_no: d.batch_trade_no,
								batch_trade_id: d.batch_trade_id,
								buyer_nick: d.customer_name,
								shop_id: d.shop_id,
								shop_name: d.shop_name,
							})
						}else {
							Object.assign(newQuestion, {
								logistics_supplier: d.logistics_supplier,
								logistics_supplier_name: d.logistics_supplier_name,//服务商
								batch_trade_no: d.batch_trade_no,
								batch_trade_id: d.batch_trade_id,
								out_stock_time: d.zd_delivery_time,//发货日期
							})
						}
						this.form.questionList.push(newQuestion)
						this.sortQuestionList(this.form.subList[this.subList_selectIndex].id)
					}
				},
			})
		},
		// 对补充问题删除
		delSupplyQuestion (){
			var self = this
			,	errMsg = []
			,	successMsg = ''

			if(
				this.questionList_selectRow.some(question => {
					if(this.form.personList.filter(obj => obj.question_sub_id === question.id).length){
						return true
					}
				})
			){
				this.$message.error('请删除所有勾选的问题补充关联的所有责任人!')
				return
			}

			_loopDel([].concat(this.questionList_selectRow))
			function _loopDel (array){
				var question = array.shift()

				if(!question){
					if(errMsg.length){
						self.$message.error(errMsg.join())
					}else {
						self.$message.success(successMsg)
					}
				}else {
					if(question.id){
						let params = {
							sub_id: self.form.subList[self.subList_selectIndex].id,
							idList: [question.id]
						}
						self.ajax.postStream('/afterSale-web/api/aftersale/analysis/question/delete?permissionCode=ANALYSIS_ORDER_QUESTION_DELETE', params, res => {
							if(res.body.result){
								successMsg = res.body.msg
								_successCb(question)
							}else {
								errMsg.push(res.body.msg)
							}
							_loopDel(array)
						}, err => {
							_loopDel(array)
						})
					}else {
						_successCb(question)
						_loopDel(array)
					}
				}
			}

			function _successCb (targetQuestion){
				self.questionList.splice(self.questionList.indexOf(targetQuestion), 1)
				self.form.questionList.splice(self.form.questionList.indexOf(targetQuestion), 1)
			}

			// if(!targetQuestion || this.questionList_selectRow.length !== 1){
			// 	this.$message.error('请选择一个补充问题')
			// 	return
			// }else if (!targetQuestion.supply_question_id){
			// 	this.$message.error('只能选择补充问题')
			// 	return
			// }
		},
		// 附件操作
		uploadAction (method){
			// var id = (this.questionList_selectRow[0] || {}).id

			/*if(!id) {
				this.$message.error('请选择一个子问题')
			}else */if(method === 'post'){
				this.ifClickUpload = true
				this.uploadData = {
					parent_name: 'AFTER_ORDER',
					parent_no: this.form.aftersaleOrder.after_order_no,
					child_name: 'QUESTION_GOODS',
					// child_no: String(id),
					content: {
						sub_bill_no: this.form.sub_bill_no,
						merge_trade_no: this.form.merge_trade_no,
						after_order_no: this.form.aftersaleOrder.after_order_no,
					},
				}
				setTimeout(() => {
					this.ifClickUpload = false
				}, 100)
			}else {
				this.$root.eventHandle.$emit('creatTab', {
					params: {
						parent_name : 'AFTER_ORDER',
						parent_name_txt: '售后单号',
						aftersaleOrderId: this.form.aftersaleOrder.id,
						parent_no : this.form.aftersaleOrder.after_order_no,
						other_parent_nos : this.form.subList.map(obj => obj.original_bill_type === 'REFUND_NEW' && obj.original_bill_no),
						child_name : 'QUESTION_GOODS',
						child_no : null,
						nickname: this.form.aftersaleOrder.buyer_nick,
						mergeTradeId: this.form.aftersaleOrder.merge_trade_id,
						// child_name_txt: '子问题id',
						// ext_data : null,
						// callback: files => {
						// 	this.listAfterMaterialVO[index].attachment_count = files.length
						// },
					},
					component: ()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
					style: 'width:80%;height:600px',
					title: '下载列表',
					name: '责任分析单问题附件列表',
				})
			}
		},
		// 重新传送
		confirmResend (){
			var confirmId = this.recordList_selectIndex.map(obj => obj.id)

			if(confirmId.length){
				this.ajax.postStream('/afterSale-web/api/aftersale/analysisConfirm/resend?permissionCode=ANALYSIS_ORDER_CONFIRM_NOTIFY_RESEND', confirmId, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}else {
				this.$message.error('请选择至少一个责任下发记录')
			}
		},
		// 转交分析单
		passOrder (){
			this.$root.eventHandle.$emit('alert',{
				title: '转交人列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/role/userList'),
				params: {
					callback: d => {
						this.orderAjax('turntoBatch?permissionCode=ANALYSIS_ORDER_TRANSMIT', {
							ids: [this.params.id],
							new_person_id: d.id,
							if_syn: false,
						})
					}
				},
			})
		},
		// 查找供应商id
		_findSuppilerId (obj){
			this.ajax.postStream("/afterSale-web/api/aftersale/ticketSupply/pageResultSupplier", {
				"page_size": 50,
				"page_no": 1,
				"key": obj.liability_person_name,
			}, d => {
				if(d.body.result && d.body.content){
					obj.liability_person = ((d.body.content.list || [])[0] || {}).supplier_id
				}
			})
		},
		// 批量填充责任人
		copyPerson (){
			var targetQuestion = this.questionList_selectRow[0]
			let self = this
			if(0 && this.questionList_selectRow.some(obj => obj.question_type !== 'NORMAL')){
				// 补充问题可以添加责任人
				this.$message.error('补充问题不能批量填充责任人')
			}else if (!this.personList.length){
				this.$message.error('源目标无责任人')
			}else {
				this.questionList_selectRow.forEach((obj, index) => {
					if(index > 0){
						JSON.parse(JSON.stringify(this.personList)).forEach(person => {
							var newPerson = {
								parent_question_id: obj.parent_question_id,
					            original_question_id: obj.original_question_id,
					            question_sub_id: obj.id,
					            analysis_sub_id: obj.analysis_sub_id,
					            _if_goods_question: obj.if_goods_question,//保存时会过滤此字段
			        			batch_trade_no: obj.batch_trade_no,
								liability_status: 'WAIT_CONDFIRM',
                judgment_suggestion: obj.judgment_suggestion,
							}
							;[
								'liability_amount',
								'handle_amount',
								'liability_scope',
								'liability_question',
								'liability_question_name',
								'liability_type',
								'liability_question_description',
								'if_create_dispute_bill',
								'liability_person_name',
								'liability_person',
								'firstLevelName',
								'secondLevelName',
								'inner_quesd_name',
								'inner_quesd__id',
								'ques_detail_id',
								'tinner_liability_type',
								'que_sec_name',
								'remark',
                'judgment_suggestion'
							].forEach(key => {
								newPerson[key] = person[key]
							})

							self.form.personList.push(newPerson)
						})
						this.$message.success('批量填充责任人成功')
					}
				})
			}
		},
		checkRawMaterialLiability(callback){
			let flag = false;
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/checkRawMaterialLiability',{goodCode: this.questionList_selectRow[0].goods_code}, res => {
					if(res.body.result){
						callback&&callback();
						flag = true;
					}else {
						this.$message.error(res.body.msg)
					}
				})
		},
		async addPersoncall(d,row, ifNew){
			let self=this;
			var targetQuestion = this.questionList_selectRow[0]
			console.log(d,row,ifNew,targetQuestion);

			var questionData = {
				liability_scope: d.liability_scope,
				liability_question: d.id,
				liability_question_name: d.liability_question,
				liability_type: d.liability_type,
				liability_question_description: d.des||'',
				if_create_dispute_bill: d.if_create_dispute?true:false,
				liability_person_name: '',//重选问题时清空责任人
				firstLevelName: d.firstLevelName,
				secondLevelName: d.secondLevelName,
				// inner_quesd__id:d.threeLevelId,
				inner_quesd_name:d.threeLevelName,
				inner_liability_type_id:d.que_id,
        tempId: new Date().getTime()

			}
			if(ifNew) {
				questionData.liability_question = d.liability_question_id
				questionData.firstLevelName = d.firstLevelName
				questionData.secondLevelName = d.secondLevelName
				questionData.inner_quesd_name = d.threeLevelName
				questionData.liability_question_name = d.out_liability_question
				questionData.liability_question = d.out_liability_question_id
				questionData.liability_type = d.out_liability_type
				questionData.judgment_suggestion = d.judgment_suggestion
				questionData.tinner_liability_type = d.liability_type
				questionData.que_sec_name = d.second_liability_type
				questionData.if_generate_per_sheet = d.if_generate_per_sheet
				questionData.inner_quesd__id = d.threeLevelId
				// tinner_liability_type:d.liability_type,
				questionData.ques_detail_id = d.id
			}

			let dutyDefaultList=this.getNeedApiDutyDefaultList();
			let dutyDefaultKeyList=this.getDutyDefaultApiKeyList(d.duty_default);
			let dutyDefaultParams=this.getDutyDefaultApiKeyOptions(d.duty_default,dutyDefaultKeyList);
      const defaultDepartmentKey=this.getDepartmentDefaultKeys();

		    if(d.duty_default!='NONEED'&&dutyDefaultList.includes(d.duty_default)){
					let params={
						'user_type':d.user_type,
						'duty_default':d.duty_default,
						...dutyDefaultParams
					}

					let personObj=await self.getDutyDeafult(params);
					if(!!personObj){
						questionData.liability_person_name = personObj.defaultName;
						questionData.liability_person = personObj.defaultId;
					}
			}else if(defaultDepartmentKey.includes(d.duty_default)){
					let personObj=await self.getDutyDeafultByAuto(d.duty_default)
					if(!!personObj){
						questionData.liability_person_name = personObj.name;
						questionData.liability_person = personObj.id;
					}
			}else{
				if(ifNew){
					if (d.duty_default == 'SERVICE') {
						// 责任范围为“服务商”，责任类型为“物流三包” 若问题中的三包供应商有值则责任人自动带出三包供应商，若无三包供应商则责任人才自动带出服务商
						questionData.liability_person_name = targetQuestion.logistics_supplier_name
						questionData.liability_person = targetQuestion.logistics_supplier
            if(d.second_liability_type==='物流责任'){
              questionData.liability_person_name = targetQuestion.zd_delivery_logistics_company
						  questionData.liability_person = targetQuestion.zd_delivery_logistics_company_id
            }
					}
					if (d.duty_default == 'THREE_GUARANTEE_SUPPLIERS') {
						// 责任范围为“供应商”，责任类型为“物流三包” 若问题中的三包供应商有值则责任人自动带出三包供应商，若无三包供应商则责任人才自动带出服务商
						questionData.liability_person_name = targetQuestion.three_guarantees_supplier_name
						questionData.liability_person = targetQuestion.three_guarantees_supplier
					}
					if(questionData.liability_scope === 'BD_Supplier' && /FACTORY/.test(d.duty_default) && targetQuestion && targetQuestion.suppiler_name){
						// 责任范围为“供应商”，责任类型为“工厂责任” 且 有供应商信息，则默认为供应商
						questionData.liability_person_name = targetQuestion.suppiler_name || '';
						this._findSuppilerId(row || questionData);
					}else if (questionData.liability_scope === 'BD_Supplier' && /THREELOGISTICS/.test(d.duty_default) && targetQuestion && targetQuestion.logistics_supplier_name){
						if(/经销三包责任/.test(questionData.inner_quesd_name)){
							console.log('经销三包责任')
							// 责任范围为“供应商”，责任类型为“物流三包” 且为经销商订单，则默认为三包供应商
							questionData.liability_person_name = targetQuestion.three_guarantees_supplier_name;
							questionData.liability_person = targetQuestion.three_guarantees_supplier;

						}else{
							if (d.duty_default == 'THREELOGISTICS') {
								// 责任范围为“供应商”，责任类型为“物流三包” 若问题中的三包供应商有值则责任人自动带出三包供应商，若无三包供应商则责任人才自动带出服务商
								questionData.liability_person_name = !targetQuestion.three_guarantees_supplier_name ? targetQuestion.logistics_supplier_name : targetQuestion.three_guarantees_supplier_name
								questionData.liability_person = !targetQuestion.three_guarantees_supplier ? targetQuestion.logistics_supplier : targetQuestion.three_guarantees_supplier
							} else {
								// 责任范围为“供应商”，责任类型为“物流三包” 且 有物流三包商信息，则默认为服务商
								questionData.liability_person_name = targetQuestion.logistics_supplier_name
								questionData.liability_person = targetQuestion.logistics_supplier
							}
						}

					}else if(questionData.liability_scope === 'BD_Customer' && d.duty_default === 'CUSTOMERDUTY' && targetQuestion.buyer_nick){
						// 责任范围为“客户”，责任类型为“客户责任” 且 有客户信息，则默认为客户
						questionData.liability_person_name = targetQuestion.buyer ? targetQuestion.buyer_nick : this.form.aftersaleOrder.buyer_nick; //供应商名字
						questionData.liability_person = targetQuestion.buyer || this.form.aftersaleOrder.buyer;//供应商ID
					}
				}else{
					if(questionData.liability_scope === 'BD_Supplier' && /工厂责任/.test(questionData.liability_type) && targetQuestion && targetQuestion.suppiler_name){
					// 责任范围为“供应商”，责任类型为“工厂责任” 且 有供应商信息，则默认为供应商
						questionData.liability_person_name = targetQuestion.suppiler_name || '';
						this._findSuppilerId(row || questionData);
					}else if (questionData.liability_scope === 'BD_Supplier' && /物流三包/.test(questionData.liability_type) && targetQuestion && targetQuestion.logistics_supplier_name){
						if(/经销三包责任/.test(questionData.liability_question_name)){
							console.log('经销三包责任')
							// 责任范围为“供应商”，责任类型为“物流三包” 且为经销商订单，则默认为三包供应商
							questionData.liability_person_name = targetQuestion.three_guarantees_supplier_name;
							questionData.liability_person = targetQuestion.three_guarantees_supplier;

						}else{
							if (d.duty_default == 'THREELOGISTICS') {
								// 责任范围为“供应商”，责任类型为“物流三包” 若问题中的三包供应商有值则责任人自动带出三包供应商，若无三包供应商则责任人才自动带出服务商
								questionData.liability_person_name = !targetQuestion.three_guarantees_supplier_name ? targetQuestion.logistics_supplier_name : targetQuestion.three_guarantees_supplier_name
								questionData.liability_person = !targetQuestion.three_guarantees_supplier ? targetQuestion.logistics_supplier : targetQuestion.three_guarantees_supplier
							} else {
								// 责任范围为“供应商”，责任类型为“物流三包” 且 有物流三包商信息，则默认为服务商
								questionData.liability_person_name = targetQuestion.logistics_supplier_name
								questionData.liability_person = targetQuestion.logistics_supplier
							}
						}

					}else if(questionData.liability_scope === 'BD_Customer' && questionData.liability_type === '客户责任' && targetQuestion.buyer_nick){
						// 责任范围为“客户”，责任类型为“客户责任” 且 有客户信息，则默认为客户
						questionData.liability_person_name = targetQuestion.buyer ? targetQuestion.buyer_nick : this.form.aftersaleOrder.buyer_nick; //供应商名字
						questionData.liability_person = targetQuestion.buyer || this.form.aftersaleOrder.buyer;//供应商ID
					}
				}

			}
			if(row){
				Object.assign(row, questionData)
			}else {
				Object.assign(questionData, {
					liability_status: 'WAIT_CONDFIRM',

					batch_trade_no: targetQuestion.batch_trade_no,
					parent_question_id: targetQuestion.parent_question_id,
					original_question_id: targetQuestion.original_question_id,
					question_sub_id: targetQuestion.id,
					analysis_sub_id: targetQuestion.analysis_sub_id,
					_if_goods_question: targetQuestion.if_goods_question,//保存时会过滤此字段
				})
				this.personList.push(questionData)
				this.form.personList.push(questionData)
			}

		},
		// 添加责任人
		addPerson (row){
			let self = this;
			var targetQuestion = this.questionList_selectRow[0];
			console.log('targetQuestion', this.form.subList[this.subList_selectIndex], this.questionList_selectRow, this.personList, row)
			if(!targetQuestion.id) {
				this.$message.error('请先保存刚新增补充问题')
				return
			}
			if (this.form.subList[this.subList_selectIndex] && this.form.subList[this.subList_selectIndex].choose_ctrl) {
				this.$root.eventHandle.$emit('alert',{
				params: {
					callback: d => {

						self.addPersoncall(Object.assign(d,{user_type:"AFTERSALE_ORDER"}),row, true);
					},
				},
				component:()=>import('@components/after_sales_liability_problem/components/add_duty_person.vue'),
					style:'width:1250px;height:80%',
					title:'选择责任问题'
				})
			} else {
				this.$root.eventHandle.$emit('alert',{
					params: {
						user_type :"AFTERSALE_ORDER",
						callback: d => {
							if(d.code=="ZRWT00022"){
								new Promise((resolve,reject)=>{
									self.checkRawMaterialLiability(resolve);
								}).then(()=>{
									self.addPersoncall(d,row);
								});
							}else{
									self.addPersoncall(d,row);

							}
							/*---先去掉---*/
							// if(!targetQuestion.batch_trade_no && /物流三包/.test(d.liability_type)){
							// 	this.$message.error((row ? '修改' : '添加') + '失败！责任类型为"物流三包"，对应的问题必须有批次单号')
							// 	return
							// }


						}
					},
					component:()=>import('@components/duty/addDutyPerson'),
					style:'width:80%;height:80%',
					title:'选择责任问题'
				})
			}

		},
		/**
		*通过表头信息去拿到合并订单号
		*
		***/
		getMergeTradeId(){
			this.merge_trade_id = this.form.merge_trade_id;
		},
		handleCurrentChange(val){
			this.radioSelect = val;
		},
		selectHisttory(val){
			console.log(val);
			this.radioSelect = val;
		},
		//判断是否为图片
        isPucture(str) {
            str = str.toString();
            var strFilter=".jpeg|.gif|.jpg|.png|.bmp|.pic|"
            if(str.indexOf(".")>-1)
            {
                var p = str.lastIndexOf(".");
                var strPostfix=str.substring(p,str.length) + '|';
                strPostfix = strPostfix.toLowerCase();
                if(strFilter.indexOf(strPostfix)>-1)
                {
                    return true;
                }
            }
            return false;
        },
		getSubFiles(row){
			let self = this;
			self.ajax.postStream('/file-iweb/api/cloud/file/list', {"order_no":self.form.aftersaleOrder.after_order_no,"sub_order_no":row.id,"cloud_file_id":null,"page_size":10000,"page_no":1}, res => {
					if(res.body.result){
						// this.getOrderDetail(res.body.msg)
						// self.ifShow = true;
						self.imagesList = res.body.content.list.map(item=>{
							return {
								creater:item.creator_nick,
								create_time:item.create_time,
								name:item.name,
								path:item.path,
								isPucture : self.isPucture(item.path)
							}
						})
						this.pictureFun()

					}else {
						this.$message.error(res.body.msg)
					}
			})
		},
		getSubAppealFiles(row){
			let self = this;
			self.ajax.postStream('/afterSale-web/api/sub/appeal/getSubAppealFiles', row.id, res => {
					if(res.body.result){
						// this.getOrderDetail(res.body.msg)
						// self.ifShow = true;
						self.imagesList = res.body.content.map(item=>{
							return {
								creater:item.creater,
								create_time:item.create_time,
								name:item.name,
								path:item.path,
								isPucture : self.isPucture(item.path)
							}
						})
						this.pictureFun()

					}else {
						this.$message.error(res.body.msg)
					}
			})
		},
		commitPass(type){
			let self = this;
			let flag = false;
			let realName = self.getEmployeeInfo('realName');
			self.personList.forEach(item=>{
				console.log(item.liability_person_name,realName)
				if(item.liability_person_name.indexOf(realName)!= -1){
						flag = true;
				}
			})
			if(!flag){
				self.$message.error('业务代理人非责任人');
				return;
			}
			let params = {
				type:type,
				callback(value,fileList){
					self.ajax.postStream('/afterSale-web/api/sub/appeal/commit',
					{sub_id:self.form.subList[self.subList_selectIndex].id,
					appeal_reason:value,
					files:fileList.map(item=>{
						return {
							"attachName": item.name,
							"attachPath": item.path,
							"attachSuffix": item.file_type,
						}
					})
					}, res => {
					if(res.body.result){
						// this.getOrderDetail(res.body.msg)
						self.grtConfirmHisttoryList(self.form.subList[self.subList_selectIndex].id);
						self.$message.success(res.body.msg)
					}else {
						self.$message.error(res.body.msg)
					}
					})
				}
			}

			this.$root.eventHandle.$emit('alert',{
		        params: params,
		        close: f => f,
		        component:()=>import('@components/duty/confirmPass'),
		        style:'width:400px;height:350px',
		        title:'发起申诉'
				})
		},
		confirmPass(type){
			let self = this;
			if(!self.radioSelect){
				this.$message.error('请选择要处理的记录')
				return false;
			}
			let uploadData = JSON.stringify({
					parent_name: 'AFTER_ORDER',
					parent_no: this.form.aftersaleOrder.after_order_no,
					child_name: 'QUESTION_GOODS',
					content: {
						sub_bill_no: this.form.sub_bill_no,
						merge_trade_no: this.form.merge_trade_no,
						after_order_no: this.form.aftersaleOrder.after_order_no,
					},
				});
			let params = {
				type:type,
				callback(value,fileList){
					self.ajax.postStream('/afterSale-web/api/sub/appeal/action',
					{id:self.radioSelect.id,
					staff_appr_result:type,
					staff_appr_opinion:value,
					version:self.radioSelect.version,
					fileList:fileList
					}, res => {
					if(res.body.result){
						// this.getOrderDetail(res.body.msg)
						self.grtConfirmHisttoryList(self.form.subList[self.subList_selectIndex].id);
						self.$message.success(res.body.msg)
					}else {
						self.$message.error(res.body.msg)
					}
					})
				}
			}
			if(type == 'pass'){
				this.$root.eventHandle.$emit('alert',{
		        params: params,
		        close: f => f,
		        component:()=>import('@components/duty/confirmPass2'),
		        style:'width:400px;height:350px',
		        title:'专员通过'
				})

			}else{
				this.$root.eventHandle.$emit('alert',{
		        params: params,
		        close: f => f,
		        component:()=>import('@components/duty/confirmPass2'),
		        style:'width:400px;height:300px',
		        title:'专员不通过'
			})
			}

		},
		// 保存责任人
		savePersonList (){
			var postData = []
			,	oldDataPersonListIds = this.originalData.personList.map(obj => obj.id)
console.log('保存责任人', this.personList)
			this.form.personList.forEach(obj => {
				var oldData = this.originalData.personList[oldDataPersonListIds.indexOf(obj.id)]

				if(!obj.id || (obj.id &&
					(
						Number(oldData.handle_amount) !== Number(obj.handle_amount)//处理金额
						|| Number(oldData.liability_amount) !== Number(obj.liability_amount)//责任金额
						|| oldData.liability_status !== obj.liability_status
						|| oldData.confirm_way !== obj.confirm_way
						|| oldData.remark !== obj.remark
						|| oldData.liability_question !== obj.liability_question
						|| oldData.liability_person_name !== obj.liability_person_name
            || oldData.judgment_suggestion !== obj.judgment_suggestion
					)
				)){
					postData.push(obj)
				}
			})

			if(!postData.length){
				this.$message.error('责任人数据没变化，无需保存')
				return
			}

			if(
				postData.some(obj => {
					if(!obj.liability_person_name){
						this.$message.error('责任人不能为空')
						return true
					}/*else if (obj.liability_scope === 'BD_Supplier' && obj._if_goods_question && !(obj._if_goods_question === 'Y')){
						this.$message.error('责任范围为"供应商"，对应的问题必须为商品问题，否则不能保存')
						return true
					}*/
					/*---先去掉---*/
					/*else if(!obj.batch_trade_no && /物流三包/.test(obj.liability_type)){
	        			this.$message.error('保存失败！责任类型为"物流三包"，对应的问题必须有批次单号')
	        			return true
	        		}*/
				})
			){
				return
			}

			postData = JSON.parse(JSON.stringify(postData)).filter(obj => {

				delete obj._if_goods_question
				delete obj.goods_code
				delete obj.goods_name
				delete obj.specification
				delete obj.firstLevelName
				delete obj.secondLevelName
        delete obj.tempId
				return true
			})
			this.ajax.postStream('/afterSale-web/api/aftersale/analysis/person/save?permissionCode=ANALYSIS_ORDER_RESPONSIBLE_SAVE', { personList: postData }, res => {
				if(res.body.result){
					this.getOrderDetail(res.body.msg)
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		// 删除责任人
		delPerson (){
      let self = this, delList = [], ifDel = false, notDelList = []
      if (this.personList_selectIndex.length <= 0) {
          this.$message.error('请至少选择数据')
          return
      }
      this.personList_selectIndex.map( goods => {
          if (goods && (!goods.id || typeof(goods.id) == 'undefined')) { // 未保存
              delList.push(goods.tempId)
          } else {
              ifDel = true
              notDelList.push(goods.id)
          }
      })
			if(notDelList.length){
				this.ajax.postStream('/afterSale-web/api/aftersale/analysis/person/delete?permissionCode=ANALYSIS_ORDER_RESPONSIBLE_DELETE', notDelList, res => {
					if(res.body.result){
						this.$message.success('责任人删除成功')

						notDelList.map( goods => {
              this.form.personList = this.form.personList.filter(item => {
                if (item.id) {
                  return item.id != goods
                } else {
                  return true
                }
              })
            })
            notDelList.map( goods => {
              this.personList = this.personList.filter(item => {
                if (item.id) {
                  return item.id != goods
                } else {
                  return true
                }
              })
            })
            this.$refs.personList.clearSelection()
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}else {
        delList.map( goods => {
          this.form.personList = this.form.personList.filter(item => {
            if (item.tempId) {
              return item.tempId != goods
            } else {
              return true
            }
          })
        })
        delList.map( goods => {
          this.personList = this.personList.filter(item => {
            if (item.tempId) {
              return item.tempId != goods
            } else {
              return true
            }
          })
        })
        this.$refs.personList.clearSelection()
			}
		},
		// 选择责任人
		selectDutyPerson (liability_scope, row, liability_type){

			this.merge_trade_id?'':this.getMergeTradeId();//客户弹出框所需要的接口参数
			//
			let otherParams = {
				mergeTradeId : this.merge_trade_id,//客户弹出框所需要的接口参数
				list_type : {
					'工厂责任': ['SP','SA'],
					'物流三包': ['SB','WL'],
				}[liability_type] || []//供应商弹出框所需要的接口参数
			}
			,	params = {
	        	otherParams:otherParams,
	        	callback: d => {
	        		//客户和其它弹出框返回的参数需要区别
	        		if(liability_scope == 'BD_Customer'){
	        			row.liability_person = d.cust_id//客户ID
	        			this.$set(row, 'liability_person_name', d.name);//客户昵称
	        		}else{
	        			row.liability_person = d.id || d.data.supplier_id || d.data.id || d.data[0].id
	        			this.$set(row, 'liability_person_name', d.fullName || d._real_name_nick_name || d.data.name || d.data.realName || d.data[0].name)
	        		}

	        	},
	        	liability_type: liability_scope,
	        	showAllPerson: true,
	        }

	        if(liability_scope === 'BD_Department') params.type = 1

			this.$root.eventHandle.$emit('alert',{
		        params: params,
		        close: f => f,
		        component:()=>import('@components/' + {
		        	BD_Empinfo: 'personel/list',//员工
					BD_Supplier: 'duty/supplier',//供应商
					BD_Customer: 'duty/selectPickrecommendhandler',//客户
					BD_Department: 'k3/departmentlist',//部门
		        }[liability_scope]),
		        style:'width:80%;height:80%',
		        title:'选择' + this.liability_scope_option[liability_scope] + '列表'
			})
		},
		// 责任分析单请求接口
		orderAjax (apiName, postData, cb){
			if(!postData) postData = { id: this.params.id }
			// if(postData){
			// 	postData.id = 9515943800980004
			// }else {
			// 	postData = { id: 9515943800980004/*this.params.id*/ }
			// }
			this.ajax.postStream('/afterSale-web/api/aftersale/analysis/' + apiName, postData, res => {
				if(res.body.result){
					if(apiName === 'get'){
						cb(res)
					}else {
						this.getOrderDetail(res.body.msg)
					}
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		saveOther(){
			let data = this.form.subList[this.subList_selectIndex];
			if(!data.receiver_type&&data.info_submit_status == "NO_NEED" && !!data.receiver_name){
				data.receiver_type = "AFTERSALE_STAFF";
			}
			this.subAjax('save?permissionCode=ANALYSIS_ORDER_DATA_SAVE');
		},
		// 子单请求接口
		subAjax (apiName, postData = {}){
			var data

			if(/^save\?/.test(apiName)){
				data = this.form.subList[this.subList_selectIndex]
			}else {
				data = { id: this.form.subList[this.subList_selectIndex].id }
				Object.assign(data, postData)
			}

			let ifDealerSubReConfirm=/dealerSubReConfirm$/.test(apiName)
			ifDealerSubReConfirm&&(this.dealerFinalConfirmDisabled=true)

			this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/' + apiName, data, res => {
				if(res.body.result){
					this.getOrderDetail(res.body.msg)
				}else {
					this.$message.error(res.body.msg)
				}
				ifDealerSubReConfirm&&(this.dealerFinalConfirmDisabled=false)
			},err=>{
				this.$message.error(err)
				ifDealerSubReConfirm&&(this.dealerFinalConfirmDisabled=false)
			})
		},
		setStatus(){
			this.isEdit = this.form.locker == this.getEmployeeInfo('id');
		},
		getOrderDetail (msg, isInit){
      Vue.prototype.tabNo[Vue.prototype.activeTab] = this.params.sub_bill_no || '';
            let pageParams = {
                id: this.params.id || '',
                sub_bill_no:this.params.sub_bill_no || '',
                // orderList: JSON.parse(JSON.stringify(this.params.orderList)) || [],
            }
            Vue.prototype.tabId[this.params.tabName] = JSON.stringify(pageParams);
			this.orderAjax('get', null, res => {
				if(!res.body.content.aftersaleOrder) res.body.content.aftersaleOrder = {}
				this.form = res.body.content;
				this.originalData = JSON.parse(JSON.stringify(this.form))

				this.setStatus();
				//this.isEdit = this.form.locker == this.getEmployeeInfo('id')
				// this._checkIsProxy()
				if(this.selectTab1 === 'questionList'){
					this.questionListSelectChange()
				}else {
					if(isInit){
						// 从列表里点击进来，显示具体哪个子单详情
						this.form.subList.some((obj, index) => {
							if(obj.sub_bill_no === this.params.sub_bill_no){
								this.subList_selectIndex = index
								// this.getRecalculateList();
								return true
							}

						})
            if(this.selectTab1 === 'info_request'){
              this.initSubInfoRequestList()
            }
					}
					if(!this.form.subList[this.subList_selectIndex].receiver_name){
						this.form.subList[this.subList_selectIndex].receiver_name = this.form.subList[this.subList_selectIndex].aftersale_processor_name
						this.form.subList[this.subList_selectIndex].receiver_id = this.form.subList[this.subList_selectIndex].aftersale_processor
					}
					this.subListSelectChange(null, 'need2Change'/*非责任分析子单页签点击刷新按钮也要更新数据*/)
				}
				this.$message.success(msg || res.body.msg)
			})
		},
		// 当前操作人与锁定人 or 售后处理人是否代理关系
		// _checkIsProxy (){
		// 	;[{
		// 		thisName: 'isEdit',
		// 		id: this.form.locker,
		// 	},{
		// 		thisName: 'isAftersaleProcessor',
		// 		id: this.form.aftersale_processor,
		// 	}].forEach(obj => {
		// 		if(!this[obj.thisName] && obj.id){
		// 			this.ajax.postStream('/user-web/api/userLogin/isHasValidProxyRelation', {
		// 				principalId: obj.id,
		// 				proxyId: Fn.getUserInfo().id,
		// 			}, res => {
		// 				if(res.body.result){
		// 					this[obj.thisName] = true
		// 				}
		// 			})
		// 		}
		// 	})
		// },
		// _fixQuestionListCss (){
		// 	setTimeout(() => {
		// 		var $treeTables = this.$refs.$questionTree.$el.querySelectorAll('.el-table__body')
		// 		;[].forEach.call($treeTables, $dom => {
		// 			$dom.removeAttribute('class')
		// 			$dom.style.width = '100%'
		// 			$dom.children[0].innerHTML = ''
		// 		})
		// 	})
		// },
		// _makeQuestionList (targetSubId){
		// 	var queParentIdList = []
		// 	,	treeData = []
		// 	,	_key = 0

		// 	console.log('targetSubId123',targetSubId)
		// 	this.form.questionList.filter(obj => obj.analysis_sub_id === targetSubId).forEach(obj => {
		// 		var parentIndex = queParentIdList.indexOf(obj.id)
		// 		if(parentIndex === -1){
		// 			queParentIdList.push(obj.id)
		// 			obj._index = ++_key
		// 			treeData.push(obj)
		// 			obj._children = [{
		// 				_index: ++_key,
		// 				id: obj.id,
		// 		    	if_has_supply: obj.if_has_supply ? '是' : '否',
		// 		    	analysis_sub_id: obj.analysis_sub_id,
		// 		    	parent_question_id: obj.parent_question_id,
		// 	            original_question_id: obj.original_question_id,
		// 			}]
		// 			obj.if_has_supply = ''
		// 		}else {
		// 			treeData[parentIndex]._children.push({
		// 				_index: ++_key,
		// 				id: obj.id,
		// 		    	if_has_supply: obj.if_has_supply ? '是' : '否',
		// 		    	analysis_sub_id: obj.analysis_sub_id,
		// 		    	parent_question_id: obj.parent_question_id,
		// 	            original_question_id: obj.original_question_id,
		// 			})
		// 		}
		// 	})
		// 	console.log('treeData123123',treeData)
		// 	treeData[0] && setTimeout(() => {
		// 		this.$refs.$questionTree.setCheckedKeys([treeData[0]._children[0]._index],true)
		// 	})
		// 	return treeData
		// },
		// questionTreeChange: function (){
		// 	var asd
		// 	,	bbb
		// 	,	who
		// 	return function (a,b,c){
		// 		console.log(a._index,b,who)
		// 		// return
		// 		clearTimeout(asd)
		// 		asd = setTimeout(() => {
		// 			console.log(888)
		// 			if(b || who === a._index || (!b && who !== a._index && this.questionList.some(obj => who === obj._index && obj._children.length === 1))){
		// 				this.$refs.$questionTree.setCheckedKeys([])
		// 				who = a._index
		// 				this.$refs.$questionTree.setCheckedKeys([a._index],true)
		// 				clearTimeout(bbb)
		// 				bbb = setTimeout(() => {
		// 					this.questionListSelectChange()
		// 				})
		// 			}
		// 		})

		// 	}
		// }(),
		sortQuestionList (targetSubId){
			console.log(this.form.questionList,'this.form.questionList')

			var data = this.form.questionList.filter(obj => obj.analysis_sub_id == targetSubId)
			,	returnData = []
			,	parentQuestion = {}


			// 子问题与补充问题排序
			data.forEach(obj => {
				if(!obj.supply_question_id) {
					returnData.push(obj)
				}else {
					if(!parentQuestion[obj.supply_question_id]) parentQuestion[obj.supply_question_id] = []
					parentQuestion[obj.supply_question_id].push(obj)
				}
			})

			Object.keys(parentQuestion).forEach(parentId => {
				returnData.some((obj, index) => {
					if(obj.parent_question_id == parentId){
						returnData.splice(index + 1, 0, ...parentQuestion[parentId])
						return true
					}
				})
			})

			this.questionList = returnData
			this.getDevelopTypeName();
		},
		getDevelopTypeName(){
			let list  =this.questionList.forEach(item=>{
				this.ajax.postStream('/afterSale-web/api/aftersale/analysis/getDevelopTypeName', {materialCode:item.goods_code}, res => {
				if(res.body.result){
					item.developType = res.body.content
				}else {
					// this.$message.error(res.body.msg)
				}
			},err=>{
				this.$message.error(err)
			})
			})

		},
		subListSelectChange (currentRow, isNeedChange){
			if(isNeedChange === 'need2Change'){

			}else if (this.selectTab1 !== 'subList'){
				return//防止current-change乱触发
			}
			currentRow && (this.subList_selectIndex = this.form.subList.indexOf(currentRow))
			var targetSub = this.form.subList[this.subList_selectIndex]

			this.sortQuestionList(targetSub.id)
			this.selectTab2Click()
			this.personList = this.form.personList.filter(obj => obj.analysis_sub_id === targetSub.id)
			this.confirmList = this.form.confirmList.filter(obj => obj.analysis_sub_id === targetSub.id)
			// this.confirmHisttoryList = (this.form.confirmHisttoryList || []).filter(obj => obj.analysis_sub_id === targetSub.id)
			this.recordList = this.form.recordList.filter(obj => obj.analysis_sub_id === targetSub.id)
			this.getRecalculateList(targetSub.id);
			this.grtConfirmHisttoryList(targetSub.id);
		},

		grtConfirmHisttoryList(id){
			let self = this;
			this.ajax.postStream('/afterSale-web/api/sub/appeal/getsubAppealBySubId', id || this.form.subList[this.subList_selectIndex].id, res => {
					if(res.body.result){
						self.confirmHisttoryList = res.body.content || [];
						self.ifStaffApprover = self.confirmHisttoryList.some(item=>{
							return item.staff_approver_id === self.getEmployeeInfo('id');
						})
					}
				})
		},
		getRecalculateList(id){
			let self = this;
			this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/geShareLog', { id: id || this.form.subList[this.subList_selectIndex].id }, res => {
					if(res.body.result){
						self.recalculateList = res.body.content || []
					}
				})
		},
		questionListSelectChange (selectRow){
			var _selectRowData = []
			if(this.selectTab1 !== 'questionList') return//防止selection-change乱触发
			if(selectRow){
				selectRow.forEach(obj => _selectRowData[this.questionList.indexOf(obj)] = obj)
				this.questionList_selectRow = _selectRowData.filter(Boolean)
			}

			var targetQuestion = this.questionList_selectRow[0]
			this.personList = targetQuestion ? this.form.personList.filter(obj => obj.question_sub_id === targetQuestion.id) : []
		},
		selectTab1Click (){
			if(this.selectTab1 === 'subList'){
				this.questionList_selectRow = []
				this.subListSelectChange()
			}
      if(this.selectTab1 === 'questionList'){
				this.questionListSelectChange()
			}
      if(this.selectTab1 === 'info_request'){
				this.initSubInfoRequestList()
			}
		},
		// 上一页 or 下一页
		nextOrPrevOrder (type){
			this.params.orderList.some((obj, index) => {
				if(obj.id === this.params.id){
					var newOrder = this.params.orderList[index + (type === 'next' ? 1 : -1)]

					if(newOrder){
						this.params.id 			= newOrder.id
						this.params.sub_bill_no = newOrder.sub_bill_no

						this.getOrderDetail(''/*msg*/, 'init')
					}else {
						this.$message.error('没有更多')
					}
					return true
				}
			})
		},

		getNeedApiDutyDefaultList(){
			let list=__AUX.get("liability_duty_default").filter(item=>item.status===1&&item.tag==='Y').reduce((acc,cur)=>[...acc,cur.code],[])
			return list
		},

    getDepartmentDefaultKeys() {
      let list=__AUX.get("liability_duty_default").filter(item=>item.status===1&&
        item.tag==='N' &&
        item.ext_field1 ==='AFTERSALE_ORDER'&&
        item.ext_field2 ==='BD_Department'
      ).reduce((acc,cur)=>[...acc,cur.code],[])
			return list
    },

		getDutyDeafultByCodeNameForN(){
			let resultObj=__AUX.get("liability_duty_default").filter(item=>item.status===1&&item.tag==='N').reduce((acc,cur)=>{
						let obj={
							[cur.code]:cur.name
						}
						return {...acc,...obj}
					},{})
			return resultObj
		},
		getDutyDefaultApiKeyList(type){
			let obj=__AUX.get("liability_duty_default").filter(item=>item.status===1&&item.tag==='Y').find(item=>item.code===type)
			let keyList=[]
			if(!!obj){
				if(obj.remark.includes(",")){
					keyList=obj.remark.split(",")
				}else{
					keyList=[obj.remark]
				}
			}
			return keyList
		},
		getDutyDefaultApiKeyOptions(type,keyList){
			let targetQuestion = this.questionList_selectRow[0]//问题
			let targetSub = this.form.subList[this.subList_selectIndex]//责任分析子单
			let userId="",userName=""
			if(type==='AFTERSALESTAFF'){
				// userId=targetQuestion.creator;
				// userName=targetQuestion.creator_name;
				userId=this.form.salesman;
				userName=this.form.salesman_name;
			}else if(type==='AFTERSALEPROCER'){
				userId=targetSub.aftersale_processor;
				userName=targetSub.aftersale_processor_name;
			}
			let dutyDefaultKeyOptions={
				'userId':userId,
				'userName':userName,
			}
			let obj={}
			if(keyList.length>0){
				keyList.forEach(item=>{
					obj[item]=dutyDefaultKeyOptions[item]
				})
			}
			return obj
		},
		//请求后端接口，根据返回值赋值责任人
		getDutyDeafult(params){
			let self=this;
			return new Promise((resolve,reject)=>{
				self.ajax.postStream("/afterSale-web/api/aftersale/analysis/person/getDutyDeafult",params,res=>{
					if(res.body.result){
						resolve(res.body.content);
						self.$message.success(res.body.msg);
					}else{
						self.$message.error(res.body.msg);
						reject();
					}
				},err=>{
					self.$message.error(err)
					reject();
				})
			})
		},
		//请求选择责任人接口，自动赋值责任人
		getDutyDeafultByAuto(code){
			let options=this.getDutyDeafultByCodeNameForN();
			let self=this;
			let params={
				'key':options[code],
				'page_no': 1,
				'page_size': 50
			}
			return new Promise((resolve,reject)=>{
				self.ajax.postStream("/material-web/api/shopv2/getDepartmentInfo",params,res=>{
					if(res.body.result&&res.body.content&&res.body.content.list){
						resolve(res.body.content.list[0]);
						self.$message.success(res.body.msg);
					}else{
						self.$message.error(res.body.msg);
						reject();
					}
				},err=>{
					self.$message.error(err)
					reject();
				})
			})
		},
	},
	mounted (){
		this.getOrderDetail(''/*msg*/, 'init');
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
			this.setStatus();
		});
	},
}
</script>

<style module>
.textarea-style :global(.el-form-item__content) {
	margin-top: 5px;
    height: auto;
}
.row-height :global(.el-textarea__inner) {
	height: 100%;
}
/* .sub-question > td:first-child :global(.el-checkbox) {
	margin-left: 50px;
} */
.max-table-height :global(.el-table__body-wrapper) {
	max-height: inherit;
}
</style>
<style  lang="stylus"  scoped>
.info-list-table /deep/ .el-table__row{
  height:60px;
  .cell{
    height:60px;
    line-height:60px;
    text-align:center
  }
}
.info-list-table /deep/ thead th{
 text-align:center
}
.info-require-remark-box{
  height: 100%;
}
.info-require-remark-box /deep/ textarea{
  height: 100%;
}
</style>
