<!--责任分析单列表-->
<template>
    <el-tabs v-model="selectTab" @tab-click="selectTabClick">
        <el-tab-pane label='售后单' name='AFTERSALE_ORDER'>
            <xpt-list-dynamic
                :data='roleList'
                :colData='colData'
                @selection-change='s => selectData = s'
                selection='checkbox'
                :pageTotal='pageTotal'
                :searchPage='searchParams.page_name'
                :pageLength='searchParams.pageSize'
                :isNeedClickEvent="true"
                :showSelectRowNumByOnlyKey="true"
                :showCount ='showCount'
                @count-off="countOff"
                @search-click='searchClicks'
                @page-size-change='pageChange'
                @current-page-change='currentPageChange'
                @row-dblclick="d => openOrder(d.parent_id, d.sub_bill_no)"
                ref='xptList'
                class="list_2_table_class"
            >
                <el-col :span="24" slot="btns">
                <el-button type="primary" size="mini" @click="lock" :disabled="!selectData.length">锁定</el-button>
                <el-button type="primary" size="mini" @click="pass" :disabled="!selectData.length">转交</el-button>
                <el-button type="primary" size="mini" @click="searchFun('init')">刷新</el-button>
                <el-button type="primary" size="mini" @click="getSubmitEdit">提交责任人</el-button>
                </el-col>
            </xpt-list-dynamic>
        </el-tab-pane>
        <el-tab-pane label='品质反馈单' name='QUALITY_ORDER'>
            <xpt-list-dynamic
                :data='roleList'
                :colData='colData'
                @selection-change='s => selectData = s'
                selection='checkbox'
                :pageTotal='pageTotal'
                :pageLength='searchParams.pageSize'
                :searchPage='searchParams.page_name'
                :isNeedClickEvent="true"
                :showSelectRowNumByOnlyKey="true"
                :showCount ='showCount'
                @count-off="countOff"
                @search-click='searchClicks'
                @page-size-change='pageChange'
                @current-page-change='currentPageChange'
                @row-dblclick="d => openOrder(d.parent_id, d.sub_bill_no)"
                ref='xptList'
                class="list_2_table_class"
            >
                <el-col :span="24" slot="btns">
                <el-button type="primary" size="mini" @click="lock" :disabled="!selectData.length">锁定</el-button>
                <el-button type="primary" size="mini" @click="pass" :disabled="!selectData.length">转交</el-button>
                <el-button type="primary" size="mini" @click="searchFun('init')">刷新</el-button>
                <el-button type="primary" size="mini" @click="getSubmitEdit" >提交责任人</el-button>
                </el-col>
            </xpt-list-dynamic>
        </el-tab-pane>
    </el-tabs>
</template>

<script>
export default {
	props:['params'],
	data (){
		let self = this
		return {
			countOffFlag:false,
			showCount:false,
			roleList:[],
			selectData: [],

            selectTab:'AFTERSALE_ORDER',
            searchFunStatus:false,

			/*searchParams: {
				pageNo: 1,
				pageSize: 50,
				// sys_trade_id: this.params.sys_trade_id, // 搜索条件
			},*/

			searchParams:{
				from_type: "AFTERSALE_ORDER",
				page_name: 'aftersale_analysis_sub',
				where: [],
				pageNo: 1,
                pageSize: 50,

				/*page_size:self.pageSize,
				page_no : 1*/
				/*page: {
					length: self.pageSize,
					pageNo: 1
				}*/
			},

			pageTotal:0,
			colData: [
			  {
				label: '创建时间',
				prop: 'create_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '责任分析子单',
				prop: 'sub_bill_no',
				redirectClick: d => this.openOrder(d.parent_id, d.sub_bill_no),
				width: 160,
			},{
				label: '买家昵称',
				prop: 'buyer_nick',
				width: 120,
			},{
				label: '合并单号',
				prop: 'merge_trade_no',
				width: 180,
			},{
				label: '子单来源类型',
				prop: 'original_bill_type',
				formatter: prop => ({
					RETURNS: '退换货跟踪单',
					SUPPLY: '补件单',
					REFUND: '退款单',
					AFTERSALES: '售后单',
					REPAIR: '服务单',
					SERVICE: '4PL平台服务单',
					REFUND_NEW: '新退款单',
                    QUALITY_FEEDBACK: '品质反馈单',
          GIFT:'售后赠品单'
				}[prop]),
				width: 100,
			},{
	            label: '是否经销商订单',
	            width:'100',
	            prop: 'if_dealer',
	            formatter: prop => ({
	              Y: '是',
	              N: '否',
	            }[prop] || prop),
	        },{
				label: '经销商编码',
				prop: 'dealer_customer_number',
				width: 130,
			},{
				label: '经销商名称',
				prop: 'dealer_customer_name',
				width: 100,
			},{
				label: '子单来源单号',
				prop: 'original_bill_no',
				width: 180,
			},{
				label: '锁定人',
				prop: 'locker_name',
			},{
				label: '锁定时间',
				prop: 'lock_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '单据状态',
				prop: 'status',
				width: 60,
				formatter: prop => ({
					CREATE: '创建',
					CLOSED: '关闭',
				}[prop]),
			},{
				label: '发货时间',
				prop: 'delivery_time',
				format: 'dataFormat1',
				width: 150,
			},/*{
				label: '提交时间',
				prop: 'submit_time',
				format: 'dataFormat1',
				width: 150,
			},*//*{
				label: '提交状态',
				prop: 'a3',
			},*/{
				label: '责任子单责任状态',
				prop: 'sub_liability_status',
				width: 110,
				formatter: prop => ({
					APPEAL: '申诉',
					WAIT_SUBMIT: '待提交',
			        WAIT_CONFIRM: '待确认',
			        COMPLAIN: '申诉',
			        END: '终止',
			        WAIT_REVOKE: '待撤回',
			        RECALLING: '撤回中',
			        RECALLED: '已撤回',
			        RECALL_FAIL: '撤回失败',
					WAIT_CONDFIRM: '待确认',
			        CONFIRMED: '已确认',
				}[prop]),
			},{
				label: '费用总额',
				prop: 'total_fee',
			},{
				label: '合并单订单店铺',
				prop: 'shop_name',
        		width: 120,
			},{
				label: '责任人',
				prop: 'subPerson',
        		width: 120,
			},
			{
				label: '售后单号',
				prop: 'after_order_no',
				width: 150,
			},{
				label: '申诉来源',
				prop: 'appeal_from',
				width: 150,
				formatter: prop => ({supplier:'供应商',quality:'品质',customer_service:'客服',logistics:'物流'}[prop]),
			},{
				label: '责任分析单',
				prop: 'bill_no',
				width: 150,
			},{
				label: '责任状态',
				prop: 'liability_status',
				width: 60,
				formatter: prop => ({
					APPEAL: '申诉',
					WAIT_SUBMIT: '待提交',
			        WAIT_CONFIRM: '待确认',
			        COMPLAIN: '申诉',
			        END: '终止',
			        WAIT_REVOKE: '待撤回',
			        RECALLING: '撤回中',
			        RECALLED: '已撤回',
			        RECALL_FAIL: '撤回失败',
					WAIT_CONDFIRM: '待确认',
			        CONFIRMED: '已确认',
				}[prop]),
			},{
				label: '费用状态',
				prop: 'fee_status',
				width: 100,
				formatter: prop => ({
					UNFINISHED: '未完结',
					FINISHED: '已完结',
					DIVIDED: '已分摊',
				}[prop]),
			},{
				label: '资料提交状态',
				prop: 'info_submit_status',
				formatter: prop => ({
					NO_NEED: '无需提供',
			        WAIT_PROVIDE: '等待提交',
			        PROVIDED: '提交完成',
			        CONFIRMED: '确认',
			        OVERTIME: '超时交馈',
				}[prop] || prop),
        		width:110
			},{
				label: '资料请求时间',
				prop: 'request_submit_time',
				format:"dataFormat1",
				width: 150,
			},{
				label: '资料提交时间',
				prop: 'info_submit_time',
				format:"dataFormat1",
				width: 150,
			},{
				label: '资料请求接收人',
				prop: 'receiver_name',
        width: 100,
			},{
				label: '专员审批结果',
				prop: 'staff_appr_result',
        width: 110,
				formatter: prop => ({pass:'通过',nopass:'不通过',HANDLED_BY_MANAGER:'主管待处理',HANDLED_BY_STAFF:'待专员处理'}[prop]),
			},{
				label: '资料请求接收人分组',
				prop: 'receiver_group_name',
        width: 120,
			},{
				label: '上游处理人',
				prop: 'aftersale_processor_name',
			},
			{
				label: '是否对账',
				prop: 'if_check_bill',
				format:'yesOrNo'
			},
			{
				label: '业务员',
				prop: 'salesman_name',
			},{
				label: '结算方式',
				prop: 'settle_method',
				format: 'auxFormat',
				formatParams: 'settle_method',
			},{
				label: '申诉时间',
				prop: 'appeal_time',
				format:"dataFormat1",
				width: 150,
			},{
				label: '专员审批人审批时间',
				prop: 'staff_appr_time',
				format:"dataFormat1",
				width: 150,
			},],
		}
	},
	methods: {
        selectTabClick(){
            this.pageTotal = 0
            this.showCount = false
            this.countOffFlag = false
            this.searchParams = {
				from_type: this.selectTab,
				page_name: 'aftersale_analysis_sub',
				where: [],
				pageNo: 1,
                pageSize: 50,
			},
            this.searchFunStatus = true
            this.searchFun()
        },

		searchClicks(obj,resolve){
			this.searchParams.where = obj
			// this.searchFun('',resolve);
			new Promise((res,rej)=>{
				this.searchFun(null, resolve,res)

			}).then(()=>{
					this.showCount = false;
				if(this.searchParams.pageNo != 1){
					this.pageTotal = 0;
				}
			})
		},
		pass (e){
			this.$root.eventHandle.$emit('alert',{
				title: '转交人列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/role/userList'),
				params: {
					callback: d => {
						this._ajax('turntoBatch?permissionCode=ANALYSIS_ORDER_TRANSMIT', {
							ids: this.selectData.map(obj => obj.parent_id),
							new_person_id: d.id,
							if_syn: false,
						})
					}
				},
			})
		},

		lock (e){
			var $btn = e.target
			$btn.disabled = true
			this._ajax('lockBatch?permissionCode=ANALYSIS_ORDER_LOCK', this.selectData.map(obj => obj.parent_id), () => {
				$btn.disabled = false
			}, () => {
				$btn.disabled = false
			})
		},
		countOff(){

			let self = this,
			url = "/afterSale-web/api/aftersale/analysis/listCount?permissionCode=ANALYSIS_ORDER_QUERY";
			if(!self.roleList.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;

			self.ajax.postStream(url,self.searchParams,function(response){
					if(response.body.result){

						self.pageTotal = response.body.content;
						self.showCount = true;
						self.countOffFlag = false;

					}else{
						self.$message.error(response.body.msg);
					}
				});
		},
		signReconciled(){
			if(!this.selectData.length){
				this.$message.error('请选择数据');
				return;
			}
			let postData = {  idList: this.selectData.map(obj => obj.parent_id),};
			this.ajax.postStream('/aftersale-web/api/aftersale/analysis/signReconciled?permissionCode=ANALYSIS_ORDER_CHECK_BILL' , postData, res => {
				if(res.body.result){

					this.getOrderDetail(res.body.msg)

				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		cancelReconciled(){
			if(!this.selectData.length){
				this.$message.error('请选择数据');
				return;
			}
			let postData = { idList: this.selectData.map(obj => obj.parent_id), };

			this.ajax.postStream('/aftersale-web/api/aftersale/analysis/cancelReconciled?permissionCode=ANALYSIS_ORDER_CHECK_BILL', postData, res => {
				if(res.body.result){

					this.getOrderDetail(res.body.msg)

				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		searchFun(isInit, cb,cb2){
            let self = this;
			if(isInit){
				this.searchParams.pageSize = 50;     //页数
				this.searchParams.pageNo = 1;   //页码
			}
			// console.log('asdfasdfsd',this.searchParams);
			this._ajax('list?permissionCode=ANALYSIS_ORDER_QUERY', this.searchParams, res => {
				// this.roleList = res.body.content.list
				// this.pageTotal = res.body.content.count
				let dataList = JSON.parse(JSON.stringify(res.body.content.list));
					if(dataList.length == (self.searchParams.pageSize+1)&&dataList.length>0){
						dataList.pop();
					}

					self.roleList = dataList;

					let totalCount = self.searchParams.pageNo*self.searchParams.pageSize;
					if(!self.showCount){
						self.pageTotal = res.body.content.count == (self.searchParams.pageSize+1)? totalCount+1:totalCount;
					}
                    setTimeout(()=>{
                        this.searchFunStatus = false
                    },300)

				this._getOrderList();


				cb2&&cb2()
				if(cb) cb()
				else this.$message.success(res.body.msg)
			})
		},
		_getOrderList (){
			var newSetBillNo = []
			,	newSetOrder = []

			this.roleList.forEach(obj => {
				if(newSetBillNo.indexOf(obj.parent_id) === -1){//去掉重复责任分析单号
					newSetBillNo.push(obj.parent_id)
					newSetOrder.push({
						id: obj.parent_id,
						sub_bill_no: obj.sub_bill_no,
					})
				}
			})

			this._getOrderList.orderList = newSetOrder
		},
		_ajax (apiName, postData, cb, errCb){
			this.ajax.postStream('/afterSale-web/api/aftersale/analysis/' + apiName, postData, res => {
				if(res.body.result){
					if(apiName !== 'list?permissionCode=ANALYSIS_ORDER_QUERY'){
						this.$message.success(res.body.msg)
						this.searchFun('', cb)
					}else {
						cb && cb(res)
					}
				}else {
					this.$message.error(res.body.msg)
				}
			}, errCb, this.params.tabName)
		},
		openOrder (id, sub_bill_no){
			this.$root.eventHandle.$emit('creatTab',{
				name: '责任分析单详情',
				params: {
					id,
					sub_bill_no,
					orderList: JSON.parse(JSON.stringify(this._getOrderList.orderList)),
				},
				component:()=> import('@components/duty/detailOfDuty_2')
			})
		},
		 getSubmitEdit(){
			var salesmanTypeList = ['GLOBAL']
			,	personId = this.getEmployeeInfo('personId')
			,	queryFunc = () => {
				var salesmanType = salesmanTypeList.shift()
				if(!salesmanType) return

				this.ajax.postStream('/user-web/api/userPerson/getUserPersonBusinessAttribute', {
					personId,
					salesmanType,
					attribute: 'IF_BATCH_SUBMIT_PERSON',
				}, res => {
					if(res.body.result && res.body.content && res.body.content.attributeValueText === '是'){
						this.batchSubmit()
					}else {
						this.$message.error('当前用户没有权限')
					}
				})
			}

			queryFunc()
		},
		batchSubmit (list){

			this.$root.eventHandle.$emit('creatTab',{
				name: '批量提交责任人',
				params: {
					orderList: JSON.parse(JSON.stringify(this.selectData)),
				},
				component:()=> import('@components/duty/batchSubmitDuty')
			})
		},
		pageChange(pageSize){
            if(this.searchFunStatus) return
				this.searchParams.pageSize = pageSize;     //页数

			//this.searchParams.page.length = pageSize;
			this.searchFun()
		},
		currentPageChange(page){
            if(this.searchFunStatus) return

			this.searchParams.pageNo = page;   //页码
			this.searchFun()
		},
	},
	mounted (){
		this.searchFun()
	},
}
</script>
<style lang="stylus" >
    .list_2_table_class .body{
        height: calc(100vh - 162px) !important;
    }
</style>
