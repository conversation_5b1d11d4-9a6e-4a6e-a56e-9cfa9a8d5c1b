// 资料请求
export default {
  data () {
    return {
      subInfoRequestSelectIndex: null,
      subInfoRequestList:[],
      currentSubInfoRequest:null,
    }
  },
  methods:{
    async initSubInfoRequestList(){
        this.subInfoRequestSelectIndex=null;
        this.currentSubInfoRequest=null;
        return new Promise((resolve, reject) => {
          const currentSub=this.form?.subList[this.subList_selectIndex || 0]
          const {id}=currentSub
          this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/information/list', { analysis_sub_id:id }, res => {
              if (res.body.result) {
                  this.subInfoRequestList=res.body.content ||[];
                  resolve(res.body.content.list)
              } else {
                  reject(res.body.msg);
              }
          })
      })
    },
    async operateRequest(type,id){
        return new Promise((resolve, reject) => {
          const apiOptions={
            withdrew:'recallInfoReq',
            sure:'confirmInfoReq',
            delete:'deleteInfoReq',
          }
          const api=apiOptions[type]
          if(!api){
            return  reject('参数错误')
          }
          const params={
            id
          };
          this.ajax.postStream(`/afterSale-web/api/aftersale/analysisSub/information/${api}`, params, res => {
              if (res.body.result) {
                  this.initSubInfoRequestList();
                  this.$message.success(res.body.msg||'操作成功')
                  resolve(true)
              } else {
                  this.$message.error(res.body.msg||'操作失败')
                  reject(false);
              }
          })
      })
    },
    // subInfoRequestChange(currentRow){
    //   this.subInfoRequestSelectIndex=this.subInfoRequestList.indexOf(currentRow)
    //   this.currentSubInfoRequest=currentRow;
    // },
    // 点击选择行则取消选择
    subInfoRequestRowClick(currentRow){
      const index=this.subInfoRequestList.indexOf(currentRow)
      if(this.subInfoRequestSelectIndex>=0&&this.subInfoRequestSelectIndex===index){
        this.subInfoRequestSelectIndex=null;
        this.currentSubInfoRequest=null;
        return
      }
      this.subInfoRequestSelectIndex=index;
      this.currentSubInfoRequest=currentRow;
    },
    openSubInfoRequestDialog(type){
      const currentSub=this.form.subList[this.subList_selectIndex]
      const afterOrderNo=this.form.aftersaleOrder.after_order_no;
      let confirmText='';
      if(!currentSub.sub_bill_no){
        return
      }
      const currentItem=this.currentSubInfoRequest;
      if(!currentItem&&['submit','reject','withdrew','sure','delete'].includes(type)){
        this.$message.warning('请选择资料请求记录')
        return
      }
      if(type==='request'){
        const params={
          aftersAleObj:this.form.aftersaleOrder,
          subObj:currentSub,
          itemObj:currentItem,
        }
        params.callback=()=>{
          this.initSubInfoRequestList();
        }
        if(currentItem&&['WAIT_PROVIDE','PROVIDED','OVERTIME'].includes(currentItem.info_submit_status)){
          this.$message.warning('子单资料提交状态必须为无需提供或确认')
          return
        }
        this.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/duty/components/info-request-dialog'),
          style:'width:800px;height:400px',
          title:'资料请求'
        });
        return;
      }
      if(type==='submit'){
        if(currentItem&&['NO_NEED','PROVIDED','CONFIRMED','OVERTIME'].includes(currentItem.info_submit_status)){
          this.$message.warning('子单资料提交状态必须为等待提交')
          return
        }
        const params={
          subObj:currentSub,
          itemObj:currentItem,
          afterOrderNo,
        }
        params.callback=()=>{
          this.initSubInfoRequestList();
        }
        this.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/duty/components/info-submit-dialog'),
          style:'width:800px;height:400px',
          title:'提交资料'
        });
        return;
      }
      if(type==='withdrew'){
        if(currentItem&&['NO_NEED','PROVIDED','CONFIRMED','OVERTIME'].includes(currentItem.info_submit_status)){
          this.$message.warning('当前状态不能撤回资料')
          return
        }
        confirmText='确认撤回？'
      }
      if(type==='sure'){
        if(currentItem&&['NO_NEED','WAIT_PROVIDE','CONFIRMED','OVERTIME'].includes(currentItem.info_submit_status)){
          this.$message.warning('当前状态不能确认资料')
          return
        }
      }
      if(type==='reject'){
        if(currentItem&&['NO_NEED','WAIT_PROVIDE','CONFIRMED','OVERTIME'].includes(currentItem.info_submit_status)){
          this.$message.warning('当前状态不能驳回资料')
          return
        }
        const params={
          itemObj:currentItem,
        }
        params.callback=()=>{
          this.initSubInfoRequestList();
        }
        this.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/duty/components/info-reject-dialog'),
          style:'width:800px;height:250px',
          title:'驳回资料'
        });
        return
      }
      if(type==='delete'){
        if(currentItem&&['WAIT_PROVIDE','PROVIDED','CONFIRMED','OVERTIME'].includes(currentItem.info_submit_status)){
          this.$message.warning('当前状态不能删除资料请求')
          return
        }
        confirmText='确认删除？'
      }
      if(confirmText){
        this.$confirm(
          confirmText,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          this.operateRequest(type,currentItem.id)
        }).catch(() => {

        });
      }else{
        this.operateRequest(type,currentItem.id)
      }

    },
  },
  computed:{
    isLock(){
      return !this.isEdit
    },
    disableOperateSubOrder(){
      return  ['WAITCONFIRM','CONFIRMED'].includes(this.form.subList[this.subList_selectIndex].liability_status)
    }
  },
}
