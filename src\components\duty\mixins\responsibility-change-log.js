export default {
  data() {
    return {
      responsibilityChangeCols: [
        {
          label: '操作时间',
          width: 120,
          prop: 'operate_time',
          format: 'dataFormat1'
        },
        {
          label: '用户',
          width: 100,
          prop: 'operator_name'
        },
        {
          label: '操作类型',
          width: 80,
          prop: 'operate_type',
          formatter: prop => ({
						UPDATE: '修改',
						ADD: '新增',
						DELETE: '删除',
						SAVE: '新增',
					}[prop] || prop),
        },
        {
          label: '一级责任主体',
          width: 100,
          prop: 'tinner_liability_type'
        },
        {
          label: '二级责任主体',
          width: 100,
          prop: 'que_sec_name'
        },
        {
          label: '故障模块',
          width: 100,
          prop: 'first_level_name'
        },
        {
          label: '故障现象',
          width: 100,
          prop: 'second_level_name'
        },
        {
          label: '故障原因',
          width: 180,
          prop: 'inner_quesd_name'
        },
        {
          label: '责任人',
          width: 180,
          prop: 'liability_person_name'
        },
        {
          label: '商品编码',
          width: 120,
          prop: 'goods_code'
        },
        {
          label: '商品名称',
          width: 120,
          prop: 'goods_name'
        },
        {
          label: '规格描述',
          width: 180,
          prop: 'specification'
        },
        {
          label: '责任金额',
          width: 80,
          prop: 'liability_amount'
        },
        {
          label: '处理金额',
          width: 80,
          prop: 'handle_amount'
        },
        {
          label: '备注',
          width: 80,
          prop: 'remark'
        },
        {
          label: '主责/次责',
          width: 100,
          prop: 'judgment_suggestion',
          formatter: prop => ({
						JUDGMENT: '判责',
						SUGGESTION: '建议',
						PRINCIPAL: '主责',
						SECONDARY: '次责',
					}[prop] || prop),
        },
        {
          label: '责任问题描述',
          width: 250,
          prop: 'liability_question_description'
        },
        {
          label: '责任范围',
          width: 100,
          prop: 'liability_scope',
          formatter: prop => this.liability_scope_option[prop] || prop,
        },
        {
          label: '是否生成绩效单',
          width: 100,
          prop: 'if_generate_per_sheet',
          formatter: prop => ({
            Y:'是',
            N:'否'
          }[prop] || prop),
        },
        {
          label: '是否生成索赔服务单',
          width: 120,
          prop: 'if_create_dispute_bill',
          formatter: prop => ({ 1: '是', 0: '否', true: '是', false: '否', }[prop] || prop),
        },
        {
          label: '责任状态时间',
          width: 120,
          prop: 'status_time',
          format: 'dataFormat1'
        },
        {
          label: '责任问题（旧版）',
          width: 150,
          prop: 'liability_question_name'
        },
        {
          label: '责任类型（旧版）',
          width: 150,
          prop: 'liability_type'
        },
        {
          label: '责任状态',
          width: 100,
          prop: 'liability_status',
          formatter: prop => this.liability_status_options[prop] || prop,
        },
        {
          label: '责任人分组',
          width: 150,
          prop: 'liability_person_group_name'
        },
      ],
      responsibilityChangeList: [],
      responsibilityChangeExportLoading:false,
    };
  },
  methods: {
    getResponsibilityChangeList(){
      if(!this.form.subList[this.subList_selectIndex].id){
        this.$message.warning('请选择子单')
        return
      }
      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/queryChangeLogBySubId', {id:this.form.subList[this.subList_selectIndex].id} , res => {
        if(res.body.result){
          this.responsibilityChangeList = res.body.content || []
        }
      })
    },
    handleResponsibilityChangeExport() {
      if(!this.form.subList[this.subList_selectIndex].id){
        this.$message.warning('请选择子单')
        return
      }
      this.responsibilityChangeExportLoading=true;
      this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportAfterSaleAnalysisPersonChangeLog', {analysis_sub_id:this.form.subList[this.subList_selectIndex].id} , res => {
        if(res.body.result){
          this.$message.success(res.body.msg||'操作成功')
        }else{
          this.$message.error(res.body.msg||'操作失败')
        }
        this.responsibilityChangeExportLoading=false;
      },()=>{
        this.responsibilityChangeExportLoading=false;
      })
    },
  }
};
