<!-- 发票详情 -->
<template>
  <div class="xpt-flex">
    <el-row class='xpt-top'	:gutter='40'>
      <el-col :span='14'>
        <!--<el-button type='info' size='mini' @click="save()" :disabled='false' :loading='onsave'>保存</el-button>-->
        <!--<el-button type='info' size='mini' @click="save()" :disabled='saveStatus' >保存</el-button>-->
        <el-button type='info' size='mini' @click="save()" :disabled='false' >保存</el-button>
        <!--<el-button type='info' size='mini' @click="clickOpe('audit')" :disabled="saveStatus" :loading='onaudit'>审核</el-button>-->
        <el-button type='info' size='mini' @click="clickOpe('audit')" :disabled="false" :loading='onaudit'>审核</el-button>
      </el-col>
    </el-row>
    <div>
      <el-tabs v-model="firstTab">
        <el-tab-pane label="基本信息" name="basicInfo" class='xpt-flex'>
          <component :is='basicInfoComponent?.default || basicInfoComponent' :fd='basicProblemData' ref='basic' :id='params.problem_id'
          @sendBasicData='sendBasicData' @useTypeChange='useTypeChange' @liabilityScopeChange='liabilityScopeChange'>
          </component>
          <!--<xpt-form :is='basicInfoComponent' :fd='basicProblemData' ref='basic' :id='params.problem_id'-->
                     <!--@sendBasicData='sendBasicData' >-->
          <!--</xpt-form>-->
        </el-tab-pane>
        <el-tab-pane label="其他信息" name="otherInfo" class='xpt-flex'>
          <!--<xpt-form :data='basicProblemData' :cols='otherCols' label='110px' :model='basicProblemData' :rules='otherRules' ref='other'>-->
          <xpt-form :data='basicProblemData' :cols='otherCols' label='110px' :model='basicProblemData' ref='other'>
          </xpt-form>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="xpt-flex__bottom" v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane label="责任明细" name="detailsInfo" class='xpt-flex'>
          <el-row class="xpt-top" :gutter="40">
            <el-col :span="24">
              <!--<el-button type="primary" size="mini" @click='addRow' :disabled="false">新增行</el-button>-->
              <el-button type="primary" size="mini" @click="detailAdd" :disabled="false">新增行</el-button>
              <!--<el-button type="danger" size="mini" @click="dealRow" :disabled="false">删除行</el-button>-->
              <el-button type="danger" size="mini"  @click="detailDelete" :disabled="false">删除行</el-button>
              <el-button type="primary" size="mini" @click="detailDisable" :disabled="get_create_status()">禁用/启用</el-button>
            </el-col>
          </el-row>
          <el-row class="xpt-flex__bottom">
            <!--<el-table border :data='detailsData'  tooltip-effect="dark" width='100%' style="width: 100%;" @selection-change="handleSelectionChange" show-overflow-tooltip v-if="typeNumber==2" >-->
              <!--<el-table-column type="selection" width="50"></el-table-column>-->
            <!--</el-table>-->
            <el-table border :data='detailsData'  tooltip-effect="dark" width='100%' style="width: 100%;" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50"></el-table-column>
              <el-table-column label="责任问题"  width="500" show-overflow-tooltip :disabled = true>
                   <template slot-scope="scope">
                        <el-input v-model="scope.row.liability_question" size='mini'  type="textarea"></el-input>
                   </template>
              </el-table-column>
              <el-table-column label="是否生成索赔服务单"  width="200" style="width: 100%;"  show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-select size="mini" v-model="scope.row.if_create_dispute" placeholder="请选择" :disabled="false">
                    <el-option
                      v-for="(value,key) in {'1':'是','0':'否'}"
                      :label="value"
                      :value="key" :key='key'>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="是否品质反馈问题类型"  width="200" style="width: 100%;"  show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-select size="mini" v-model="scope.row.quality_feedback_question_type" placeholder="请选择" :disabled="false">
                    <el-option
                      v-for="(value,key) in {'Y':'是','N':'否'}"
                      :label="value"
                      :value="key" :key='key'>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="默认责任人"  width="200" style="width: 100%;"  show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-select size="mini" v-model="scope.row.duty_default" placeholder="请选择" :disabled="false">
                    <el-option
                      v-for="(value,index) in dutyDefaultOptions"
                      :label="value.name"
                      :value="value.code" :key='index'>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
               <el-table-column label="是否禁用" width="200" style="width: 100%;"  show-overflow-tooltip>
                 <template slot-scope="scope">
                   <!--<el-select size="mini"  style="width:100%"  v-model="scope.row.forbidden_status" placeholder="请选择" :disabled="false">-->
                   <el-select size="mini"  v-model="scope.row.forbidden_status" placeholder="请选择" :disabled="true">
                     <el-option
                       v-for="(value,key) in {'1':'是','0':'否'}"
                       :label="value"
                       :value="key" :key='key'>
                     </el-option>
                   </el-select>
                 </template>
               </el-table-column>
              <el-table-column label="禁用人"  width="150" show-overflow-tooltip>
              <template slot-scope="scope">
              {{scope.row.forbidden_person_name}}
              </template>
              </el-table-column>
              <el-table-column label="禁用日期" prop="delivery_date" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{scope.row.forbidden_date | dataFormat}}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>

import basicInfo from '@components/duty/basicInfo.vue'
import detail from './detail.js'
export default {
  props: ['params', 'data'],
  mixins: [detail],
  data(){
    return {
      onsave: '',
      onsubmit: false,
      onaudit: false,

      firstTab: 'basicInfo',
      secondTab: 'detailsInfo',
      basicInfoComponent: 'basicInfo',
      basicProblemData: {receiverInfoVO: {}},
      selectDatas: '',
      detailsData: [],
      materielCount: 0,
      operationDataList: [],
      saveData: {},//保存的数据
      searchData: {
        invoice_id: '',
        page_no: 1,
        page_size: 10000
      },
//      invoiceId:'',//发票id
      problem_id: '',
      // 是否已经选单
      isSelectOrder: false,
      merge_trade_id: '',
      create_status:'',

      dutyDefaultAuxList:[],//辅助资料所有生效的默认责任人枚举值列表
      dutyDefaultOptions:{},//默认责任人枚举对象
      liability_scope:"",//责任范围，用于确定默认责任人选项条件之一
      use_type:"",//问题用途，用于确定默认责任人选项条件之一
    }
  },
  methods: {
    detailAdd() {
      var self = this;
      this.detailsData.push(self.getDetailObj());
      this.create_status = true;
    },
    detailDelete() {
      if (this.selectDatas.length == 0) {
        this.$message.error('请选择数据!');
        return;
      }
      if (this.selectDatas.length > 1) {
        this.$message.error('一次只能勾选删除一条数据!');
        return;
      }
      if(this.selectDatas[0].id == null)
      {
        this.selectDatas.forEach(obj => {
          this.detailsData.splice(this.detailsData.indexOf(obj), 1);
        });
        for (var i = 0; i < this.detailsData.length; i++) {
          if (this.detailsData[i].id == null) {
              this.create_status = true;
              return;
          }
        }
        this.create_status = false;
        return;
      }
      if(this.selectDatas[0].forbidden_status == '0')
      {
        this.$message.error('已启用的数据不能删除!');
        return;
      }

      for (var i = 0; i < this.detailsData.length; i++) {
        if (this.detailsData[i].id == null) {
          this.create_status = true;
          this.$message.error('请先保存新增的数据!');
          return;
        }
      }

      let ids = this.getSelectIds();
      let delete_data;

      delete_data = {ids: ids};
      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/deleteLine', delete_data, d => {
        if (d.body.result) {
          let resultData;
          resultData = d.body.result;
          if (resultData) {
            this.refresh()
          }
        } else {
          this.$message.error(d.body.msg);
        }
      })
      console.log(this.selectDatas);
    },
    get_create_status()
    {
        if(this.create_status)
        {
            return true;
        }
        else{
            return false;
        }
    },
    getDetailObj(){
      var obj = {
        id:null,
        liability_question: null, //责任问题
        if_create_dispute: '0',  //是否生成索赔服务单
        quality_feedback_question_type:'N',//是否品质反馈类型
        duty_default:"NONEED",
        forbidden_status: '0',  //是否禁用
        forbidden_person_name: null,  //禁用人
        forbidden_date: null         //禁用日期
      };
      return obj;
    },

    detailDisable() {
      if (this.selectDatas.length == 0) {
        this.$message.error('请选择数据!');
        return;
      }
//      if (this.selectDatas.length > 1) {
//        this.$message.error('不能进行批量禁用或启用!');
//        return;
//      }
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth()+1;
      var day = date.getDate();
      var forbidden_time = year + '-' + month + '-' + day;

      let fullName = this.getEmployeeInfo('fullName');
      for (var i = 0; i < this.selectDatas.length; i++) {
          if(this.selectDatas[i].forbidden_status == '1')
          {
            this.selectDatas[i].forbidden_status = '0';
            this.selectDatas[i].forbidden_date = null;
            this.selectDatas[i].forbidden_person_name = null;
          }
          else{
            this.selectDatas[i].forbidden_status = '1';
            this.selectDatas[i].forbidden_date = forbidden_time;
            this.selectDatas[i].forbidden_person_name = fullName;
          }
        }
//
//      if(this.selectDatas[0].id == null)
//      {
//        this.$message.error('请先进行保存!');
//        return;
//      }
//      let problem_data;
//      if (this.selectDatas[0].forbidden_status == 1) {
//        problem_data = {id: this.selectDatas[0].id, forbidden_status: 0};
//      }
//      else {
//        problem_data = {id: this.selectDatas[0].id, forbidden_status: 1};
//      }
//
//      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/disableOperationLine', problem_data, d => {
//        if (d.body.result) {
//          let resultData;
//          resultData = d.body.result
//          if (resultData) {
//            this.refresh();
//          }
//        } else {
//          this.$message.error(d.body.msg);
//        }
//      })
    },

    save(resolve, reject) {
      this.onsave = false;
      if (!this.$refs.basic.validate()) return;
//      console.log(this.$refs.basic.submit.liability_scope);
      new Promise(resolve => {
        this.$refs.other.validate(resolve);
      }).then(() => {
//        this.setSaveData();
//          var self = this;
          for (var i = 0; i < this.detailsData.length; i++) {
//            console.log('liability_question ============  %s ',this.detailsData[i].liability_question);
            if (this.detailsData[i].liability_question == null) {
              this.$message.error('责任明细中的责任问题为必填！');
              return;
            }
          }
          this.saveData = {
            id: this.basicProblemData.id,
            liability_scope: this.$refs.basic.submit.liability_scope,
            liability_type: this.$refs.basic.submit.liability_type,
            if_full_deduct: this.$refs.basic.submit.if_full_deduct,
            if_foundation: this.$refs.basic.submit.if_foundation,
            if_auto_amount: this.$refs.basic.submit.if_auto_amount,
            code: this.$refs.basic.submit.code,
            remark: this.$refs.basic.submit.remark,
            status: this.$refs.basic.submit.status,
            questionList: this.detailsData,
            user_type: this.$refs.basic.submit.user_type,
          };
        this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/save', this.saveData, d => {
          if (d.body.result) {
//            this.invoiceId = d.body.content.invoice_id
            this.params.problem_id = d.body.content;
//            this.$root.eventHandle.$emit('refresh_invoice')
            this.refresh()
            this.onsave = true;
            this.create_status = false;
            this.$message.success('保存成功');
            resolve && resolve()
          } else {
            this.$message.error(d.body.msg);
          }
          this.onsave = false;
        }, err => {
          this.$message.error(err);
          this.onsave = false;
        })
      })
    },
    clickOpe(str){ //点击操作
      if (!this.basicProblemData.id) {
        this.$message.error('请先进行保存！');
      }
//      let data = {id: this.basicProblemData.id};
      let data;
      let ids = [];
      ids[0] = {id:this.basicProblemData.id};
      data = {auditListVOS:ids};
      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/' + str, data, d => {
        if (d.body.result) {
          this.params.status = 'audit';
          this.refresh();
          this.$message.success('操作成功!');
        } else {
          this.$message.error(d.body.msg || '');
        }
      }, err => {
        this.$message.error(err);
      })
    },

    handleSelectionChange (selects){
      this.selectDatas = selects;
    },
    //得到行id
    getSelectIds(){
      var self = this;
      var ids = [];
      if (self.selectDatas && self.selectDatas.length > 0) {
        for (var i = 0; i < self.selectDatas.length; i++) {
          if (self.selectDatas[i].id) {
            ids.push(self.selectDatas[i].id);
          }
        }
      }
      return ids;
    },

    getRecord() {
      var self = this
      if (!self.invoiceId) return
      this.ajax.postStream('/order-web/api/invoice/log/get', self.invoiceId, d => {
        if (d.body.result) {
          self.operationDataList = d.body.content
        }
        else self.$message.error(d.body.msg)
      })
    },

    getBasic() {
      if (!this.params.problem_id) return
      let problem_data = {id: this.params.problem_id};
      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/getDetail', problem_data, d => {
        if (d.body.result) {
          this.basicProblemData = d.body.content
          this.detailsData = d.body.content.questionList || [];
          for (var i = 0; i < this.detailsData.length; i++) {
            if (this.detailsData[i].forbidden_status) {
              this.detailsData[i].forbidden_status = '1';
            }
            else {
              this.detailsData[i].forbidden_status = '0';
            }
            if (this.detailsData[i].if_create_dispute) {
              this.detailsData[i].if_create_dispute = '1';
            }
            else {
              this.detailsData[i].if_create_dispute = '0';
            }
          }
          this.isSelectOrder = true;
        } else {
          this.$message.error(d.body.msg)
        }
      })
    },
    getEnableAux(){
      let list=__AUX.get("liability_duty_default")
      this.dutyDefaultAuxList=list.filter(item=>item.status===1)
    },
    liabilityScopeChange(e,selectChangeByHand){
      this.liability_scope=e;
      if(!!selectChangeByHand){
        this.detailsData.forEach(item=>item.duty_default='NONEED')
      }
      this.formatDutyDefaultOptions()
    },
    useTypeChange(e,selectChangeByHand){
      this.use_type=e;
      if(!!selectChangeByHand){
        this.detailsData.forEach(item=>item.duty_default='NONEED')
      }
      this.formatDutyDefaultOptions()
    },
    formatDutyDefaultOptions(){
      let self=this;
      let list=this.dutyDefaultAuxList;
      let options = list
        .filter(
          (item) =>
            (!item.ext_field1 && !item.ext_field2) ||
            (item.ext_field1 == self.use_type &&
              item.ext_field2 == self.liability_scope)
        )
        .map((item) => {
          return { code: item.code, name: item.name };
        });
      this.dutyDefaultOptions=options;
    },
    refresh() {
      this.getBasic()
      this.getEnableAux()
    },
//  annex() {
//    var self = this
//    this.$refs.basic.pictureFun()
//  },
    sendBasicData(d) {
      this.basicProblemData = d
    },
    //生成保存数据
    setSaveData() {
      var self = this;
      for (var i = 0; i < this.detailsData.length; i++) {
        if (this.detailsData[i].liability_question = null ) {
          this.$message.error('责任明细中的责任问题为必填！');
          return;
        }
      }
      this.saveData = {
        id: self.basicProblemData.id,
        liability_scope: this.$refs.basic.submit.liability_scope,
        liability_type: this.$refs.basic.submit.liability_type,
        if_full_deduct: this.$refs.basic.submit.if_full_deduct,
        if_foundation: this.$refs.basic.submit.if_foundation,
        code: this.$refs.basic.submit.code,
        remark: this.$refs.basic.submit.remark,
        status: this.$refs.basic.submit.status,
        questionList: this.detailsData,
      }
    },
    materielPageSizeChange(size){
      var self = this;
      // 第页数改变
      self.searchData.page_size = size;
//      self.getDetailsList();
    },
    materielPageChange(page_no){
      var self = this;
      // 页数改变
      self.searchData.page_no = page_no;
//      self.getDetailsList();
    },
  },
  computed: {
    // 选单按钮状态：新增的时候可以操作
    selectStatus() {
      return this.invoiceId ? true : this.isSelectOrder;
    },
    // 保存按钮状态：已提交、已审核、作废状态不可操作
    saveStatus(){
      let set = new Set(['audit']);
//      if(set.has(this.$refs.basic.submit.status)) {
      if(set.has(this.params.status)) {
        return true;
      } else {
        return false;
      }
    },
  },
  mounted:function() {
    this.refresh()
    this.__close=function(){
      self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
    }
  },
  components: {
    'basicInfo': basicInfo,
  }
}
</script>
