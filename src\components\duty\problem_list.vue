<template>
  <xpt-list
    :data='dataList'
    :btns='btns'
    :colData='cols'
    :pageTotal='count'
    :searchPage='searchData.page_name'
    :pageLength='searchData.page_size'
    isNeedClickEvent
    @search-click='presearch'
    @selection-change='handleSelectionChange'
    @page-size-change='sizeChange'
    @current-page-change='pageChange'
  ></xpt-list>
</template>
<script>
import Fn from '@common/Fn.js'
export default {
  props:['data','params'],
  data(){
    let self = this;
    return {
      btns: [
        {
          type: 'success',
          txt: '刷新',
          loading: false,
          click() {
            self.getList()
          }
        }, {
          type: 'info',
          txt: '新增',
          click: self.addNews
        },
        {
          type: 'danger',
          txt: '删除',
          click: self.deleteNews
        },
        {
          type: 'primary',
          txt: '复制',
          click: self.copyNews
        },
        {
          type: 'success',
          txt: '审核',
          loading: false,
          click() {
            self.clickOpe('/audit', 5);
          }
        },
        {
          type: 'primary',
          txt: '导出',
          click: self.exportAnalysisyList
        }
      ],
      cols: [
        {
          label: '编码',
          prop: 'code',
          redirectClick(row) {
            self.viewDutyDetail(row.id,row.status)
          }
        },
        {
          label: '责任类型',
          prop: 'liability_type',
        },
        {
          label: '责任范围',
          prop: 'liability_scope',
          formatter(val) {
            switch(val) {
              case 'BD_Empinfo': return '员工';
              case 'BD_Supplier': return '供应商';
              case 'BD_Customer': return '客户';
              case 'BD_Department': return '部门';
              default: return val;
            }
          }
        },

        {
          label: '是否基金',
          prop: 'if_foundation',
          formatter(val) {
            switch(val) {
              case 0: return '否';
              case 1: return '是';
              default: return val;
            }
          }
        },

        {
          label: '是否全额',
          prop: 'if_full_deduct',
          formatter(val) {
            switch(val) {
              case 0: return '否';
              case 1: return '是';
              default: return val;
            }
          }
        },
        {
          label: '状态',
          prop: 'status',
          formatter(val) {
            switch(val) {
              case 'create':return '保存';
              case 'audit':return '审核';
              default: return val;
            }
          }
        },
      ],
      dataList:[],

      searchData:{
        page_name:"aftersale_analysis_k3_liability_type",
				where:[],
        page_no:1,
        page_size:50,
      },
      count:20,
      selectProblem:[],//选择的
      selectData:[],//选择对像
      rejectData:[],//选择的需要回传的驳回数据
      problem_Detail_Id:[],
      detailsData:{},
    }
  },
  methods:{
    addNews(){
      let params = {invoice_id:''}
			this.$root.eventHandle.$emit('creatTab', {
        name: "责任问题详情",
        params: params,
        component: () => import('@components/duty/problem_detail.vue')
      })
    },
    copyNews(){
      if(this.selectProblem.length==0) {
        this.$message.error("请先选择数据!")
        return
      }
      if(this.selectProblem.length > 1)
      {
        this.$message.error("不可以批量复制!")
        return
      }
      let problem_data = {id: this.rejectData[0].id};
      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/getDetail', problem_data, d => {
        if (d.body.result) {
          this.detailsData = d.body.content;
          this.detailsData.code = '';
          this.detailsData.id = '';
          this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/save', this.detailsData, d => {
            if (d.body.result) {
              this.getList();
              this.$message.success('复制成功');
            } else {
              this.$message.error(d.body.msg)
            }
          }, err => {
            this.$message.error(err);
          })
        } else {
          this.$message.error(d.body.msg)
        }
      })
    },
    deleteNews(){
      if(this.selectProblem.length==0) {
        this.$message.error("请先选择数据!")
        return
      }

      var ids=[];
      let delete_data;
      delete_data = {ids:this.selectProblem};
      for(let deleteData of this.rejectData){
        if(deleteData.status == 'audit')
        {
            this.$message.error("已审核的责任问题单不能勾选删除!")
            return
        }
      }

      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/delete',delete_data,d=>{
        if(d.body.result){
          this.getList()
        } else {
          this.$message.error(d.body.msg || '');
        }
      }, err => {
        this.$message.error(err);
      })
    },

    sizeChange(size){
      this.searchData.page_size = size;
      this.getList()
    },
    pageChange(page){
      this.searchData.page_no = page
      this.getList()
    },
    getList(resolve){
      this.btns[0].loading = true;
      this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type/list', this.searchData, d=>{
        if(d.body.result){
          this.dataList = d.body.content.list || []
          this.count = d.body.content.count
				} else {
          this.dataList = []
		      this.count = 0
          this.$message.error(d.body.msg || '');
        }
        this.btns[0].loading = false;
        resolve && resolve()
      }, err => {
        this.$message.error(err);
        resolve && resolve();
        this.btns[0].loading = false;
      })
    },
    clickOpe(str, index) {
      let data = {};
      if (this.selectProblem.length == 0) {
        this.$message.error("请先选择数据!")
        return
      }
      if (index == 4) {
        data = this.rejectData
      } else {
        data = this.selectProblem
      }
      if (index == 5) {
        for(var i = 0;i <this.rejectData.length; i++)
        {
            if(this.rejectData[i].status == 'audit')
            {
              this.$message.error('请勾选保存状态的数据进行审核!');
              return;
            }
        }
        let ids = [];


        for (var i = 0; i < this.problem_Detail_Id.length; i++) {
            ids[i] = {
                id:this.problem_Detail_Id[i]
            }}

        data = {auditListVOS:ids};
        console.log(data);
        this.btns[index].loading = true;
        this.ajax.postStream('/afterSale-web/api/aftersale/analysis/type' + str, data, d => {
          if (d.body.result) {
            this.getList()
            this.$message.success('操作成功!')
          } else {
            this.$message.error(d.body.msg || '')
          }
          this.btns[index].loading = false
        }, err => {
          this.btns[index].loading = false;
          this.$message.error(err);
        })
      }
    },

    handleSelectionChange(val){
      this.selectData = val.length?val:[];
      this.selectProblem=[]
      this.rejectData=[]
      this.problem_Detail_Id=[]
      for(let v of val){
        this.selectProblem.push(v.id)
        this.problem_Detail_Id.push(Number(v.id))
        this.rejectData.push({id:v.id,status:v.status})
      }
    },
    viewDutyDetail(id,status){
      var params = {problem_id:id, status:status};
			this.$root.eventHandle.$emit('creatTab',{name:"责任问题详情",
        params:params,
        component:() => import('@components/duty/problem_detail.vue')
      })
    },

    presearch(list, resolve){
      this.searchData.where = list;
			this.getList(resolve);
    },
    exportAnalysisyList (){
//      if(!this.searchData.where.length){
//        this.$message.error('导出功能至少要有一个过滤条件')
//        return
//      }
      var postData = JSON.parse(JSON.stringify(this.searchData))
      delete postData.page
      this.ajax.postStream('/reports-web/api/reports/afterSaleExport/exportAnalysisK3LiabilityQuestion', postData, d => {
        this.$message({
          type: d.body.result ? 'success' : 'error',
          message: d.body.msg,
        })
      })
    },
    expList(){
      this.$root.eventHandle.$emit('creatTab', {
        name: "报表导出",
        params: {
          url: '/order-web/api/invoice/export/result/list?permissionCode=INVOICE_EXPORT_RESULT'
        },
        component: () => import('@components/common/exout.vue')
      })
    },
    sendSmsList(list) {
      let i = list.length;
      while(i--) {
        this.sendSms(list[i]);
      }
    },
    // 发送短信
    sendSms(id) {
      this.ajax.postStream('/order-web/api/invoice/sendSMS', {
        invoice_id: id
      }, res => {
        if(!res.body.result) {
          this.$message.error(res.body.msg || '');
        }
      }, err => {
        this.$message.error(err);
      })
    }
  },
  mounted:function() {
    this.getList();
    this.$root.eventHandle.$on('refresh_invoice',d => {
      this.getList()
    })
  },
  destroyed(){
    this.$root.offEvents('refresh_invoice');
  }
}
</script>
