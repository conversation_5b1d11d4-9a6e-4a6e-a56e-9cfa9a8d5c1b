<!--请求接收人弹框-->
<template>
  <div style="height: 100%">
    <div class="xpt-top">
      <el-button type="info" size="mini" @click="receiverSure"
      >确定</el-button
      >
    </div>
    <div class="xpt-flex">
      <el-form :model="recordData" ref="recordData" label-position="left" label-width="65px">
        <el-row class="mgt20" :gutter="40">
          <el-col :span="6">
            <el-radio
                v-model="radio"
                label="1"
            >
              <el-form-item
                  label="业务员"
                  prop="staff_name"
              >
                <el-input
                    size="mini"
                    v-model="recordData.staff_name"
                    readonly
                    icon="search"
                    :on-icon-click="selectUser1"
                    class="select-intervener"
                ></el-input>
              </el-form-item>
            </el-radio>
          </el-col>
        </el-row>
        <el-row class="mgt20" :gutter="40">
          <el-col :span="6">
            <el-radio
                v-model="radio"
                label="2"
            >
              <el-form-item
                  label="售后处理人"
                  prop="aftersale_processor_name"
              >
                <el-input
                    size="mini"
                    v-model="recordData.aftersale_processor_name"
                    readonly
                    icon="search"
                    :on-icon-click="selectUser2"
                    class="select-intervener"
                ></el-input>
              </el-form-item>
            </el-radio>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
          recordData: {
            staff: '',
            staff_name: '',
            aftersale_processor: '',
            aftersale_processor_name: '',
          },
          radio: '2',
          receiverData: {
            receiver_id: '',
            receiver_name: '',
          },
        };
    },
    created() {
        this.recordData.staff = this.params.aftersaleOrder.staff;
        this.recordData.staff_name = this.params.aftersaleOrder.staff_name;
        this.recordData.aftersale_processor = this.params.subObj.aftersale_processor;
        this.recordData.aftersale_processor_name = this.params.subObj.aftersale_processor_name;
    },
    methods: {
      receiverSure(){
        let self = this;
        if (self.radio === '1'){
          self.receiverData.receiver_id = self.recordData.staff;
          self.receiverData.receiver_name = self.recordData.staff_name;
          self.receiverData.receiver_type = 'STAFF'
        } else {
          self.receiverData.receiver_id = self.recordData.aftersale_processor;
          self.receiverData.receiver_name = self.recordData.aftersale_processor_name;
          self.receiverData.receiver_type = 'AFTERSALE_STAFF'
        }
        if (!self.receiverData.receiver_id || !self.receiverData.receiver_name){
          this.$message.error("请选择接收人");
          return ;
        }
        this.params.callback && this.params.callback(self.receiverData);
        this.$root.eventHandle.$emit(
            "removeAlert",
            this.params.alertId
        );
      },
      selectUser1(){
        let self = this;
        this.$root.eventHandle.$emit("alert", {
          component: () =>
              import("@components/duty/receiverSelectIntervener.vue"),
          close: function () {},
          style: "width:900px;height:600px",
          title: "请选择接收人",
          params: {
            callback(d) {
              self.recordData.staff = d.id;
              self.recordData.staff_name = d.fullName;
              self.$message.success("选择成功");
            },
          },
        });
      },
      selectUser2(){
        let self = this;
        this.$root.eventHandle.$emit("alert", {
          component: () =>
              import("@components/duty/receiverSelectIntervener.vue"),
          close: function () {},
          style: "width:900px;height:650px",
          title: "请选择接收人",
          params: {
            callback(d) {
              self.recordData.aftersale_processor = d.id;
              self.recordData.aftersale_processor_name = d.fullName;
              self.$message.success("选择成功");
            },
          },
        });
      },
    },
};
</script>
<style scope lang="stylus">
.el-radio{
  display:flex;
  .el-radio__input{
    display: flex;
    align-items: center;
  }
}
</style>