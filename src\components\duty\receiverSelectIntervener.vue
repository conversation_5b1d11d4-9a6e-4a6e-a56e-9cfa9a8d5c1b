<template>
    <div>
        <xpt-list
            :btns="userBtns"
            :data="userList"
            :colData="userCols"
            searchHolder="请输入工号或姓名或昵称"
            @search-click="presearch"
            :pageTotal="total"
            :pageLength="search.page.length"
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
            selection="radio"
            @radio-change="radioChange"
            :page-sizes="[10, 20]"
        ></xpt-list>
    </div>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            userList: [],
            userBtns: [
                {
                    type: "info",
                    txt: "确认",
                    click: () => {
                        self.params.callback(self.currentUserInfo);
                        self.$root.eventHandle.$emit(
                            "removeAlert",
                            self.params.alertId
                        );
                    },
                    disabled: true,
                },
            ],
            userCols: [
                {
                    label: "工号",
                    prop: "employeeNumber",
                },
                {
                    label: "姓名",
                    prop: "realName",
                },
                {
                    label: "昵称",
                    prop: "nickName",
                }
            ],
            total: 0,
            search: {
                page: { length: 20, pageNo: 1 },
                page_name: "cloud_user_login",
                where: [],
                status: 1,
                isEnable: 1,
                // type: "EMPLOYEE",
            },
            currentUserInfo: {},
        };
    },
    mounted() {
        this.getQualityFeedbackUserList();
    },
    methods: {
        presearch(key, resolve) {
            this.search.key = key;
            this.getQualityFeedbackUserList(resolve);
        },
        getQualityFeedbackUserList(resolve) {
            let self = this;
            let params = this.search;
            this.ajax.postStream(
                "/user-web/api/userLogin/getUserLoginList",
                params,
                (res) => {
                    if (res.body.result) {
                        res.body.msg ? this.$message.success(res.body.msg) : "";
                        this.userList = res.body.content.list;
                        this.total = res.body.content.count;
                    } else {
                        this.$message.error(res.body.msg);
                    }
                    resolve && resolve();
                },
                (err) => {
                    this.$message.error(err);
                    resolve && resolve();
                }
            );
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page.length = size;
            this.getQualityFeedbackUserList();
        },
        pageChange(pageNo) {
            // 页数改变
            this.search.page.pageNo = pageNo;
            this.getQualityFeedbackUserList();
        },
        radioChange(data) {
            console.log(data);
            this.currentUserInfo = data;
            if (Object.keys(this.currentUserInfo).length) {
                this.userBtns[0].disabled = false;
            }
        },
    },
};
</script>
<style scope>
</style>