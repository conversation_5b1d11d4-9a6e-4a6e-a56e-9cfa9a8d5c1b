<!--退款责任分析单详情-退款责任申诉弹窗-->
<template>
    <div style="height: 100%">
        <div class="xpt-flex" v-if="['SUBMIT'].includes(fromType)">
            <div class="xpt-top">
                <el-button type="info" size="mini" @click="makeSure"
                    >保存</el-button
                >
            </div>
            <div class="xpt-flex__bottom">
                <el-form :model="appealData" :rules="rules" ref="appealData">
                    <el-row class="mgt20" :gutter="40">
                        <el-col :span="24">
                            <el-form-item
                                :label="appeal_reason"
                                prop="appeal_msg"
                                label-width="90px"
                            >
                                <el-input
                                    type="textarea"
                                    placeholder="请输入内容"
                                    v-model="appealData.appeal_msg"
                                    :autosize="{ minRows: 4, maxRows: 4 }"
                                    :maxlength="500"
                                >
                                </el-input>
                                <el-tooltip
                                    v-if="rules.appeal_msg[0].isShow"
                                    class="item"
                                    effect="dark"
                                    :content="rules.appeal_msg[0].message"
                                    placement="right"
                                    popper-class="xpt-form__error"
                                >
                                    <i class="el-icon-warning"></i>
                                </el-tooltip>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <div
            class="xpt-flex"
            v-else-if="
                [
                    'START',
                    'MODIFY',
                    'HIGHERREJECT',
                    'HIGHERPASS',
                    'REFUNDNOPASS',
                    'REFUNDPASS',
                ].includes(fromType)
            "
        >
            <div class="xpt-top">
                <el-button type="info" size="mini" @click="makeSureSoft"
                    >保存</el-button
                >
            </div>
            <div class="xpt-flex__bottom">
                <el-form :model="appealData" ref="appealData">
                    <el-row class="mgt20 myRow" :gutter="40">
                         <el-col :span="24" v-if="appealData.appeal_msg_read">
                            <el-form-item
                                label="申诉理由："
                                prop="appeal_msg_read"
                                label-width="90px"
                            >
                                <el-input
                                    type="textarea"
                                    v-model="appealData.appeal_msg_read"
                                    :autosize="{ minRows: 4, maxRows: 4 }"
                                    :maxlength="500"
                                    readonly
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                         <el-col :span="24" v-if="appealData.appeal_msg_higher_opinion_read">
                            <el-form-item
                                label="上级意见："
                                prop="appeal_msg_higher_opinion_read"
                                label-width="90px"
                            >
                                <el-input
                                    type="textarea"
                                    v-model="appealData.appeal_msg_higher_opinion_read"
                                    :autosize="{ minRows: 4, maxRows: 4 }"
                                    :maxlength="500"
                                    readonly
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item
                                :label="appeal_reason"
                                prop="appeal_msg"
                                label-width="90px"
                            >
                                <el-input
                                    type="textarea"
                                    placeholder="请输入内容"
                                    v-model="appealData.appeal_msg"
                                    :autosize="{ minRows: 4, maxRows: 4 }"
                                    :maxlength="500"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <div v-else-if="['GETFILELIST'].includes(fromType)">
            <el-table
                :data="filelist"
                class="chooseFileTable"
                @selection-change="handleSelectionChange"
            >
                <el-table-column
                    width="25"
                    align="center"
                    type="selection"
                ></el-table-column>
                <el-table-column
                    prop="name"
                    label="文件名称"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <div v-if="isImage(scope.row.path)">
                            <a href="#" class="showImg"
                                ><img
                                    :src="scope.row.path"
                                    alt="图片"
                                    width="60"
                            /></a>
                        </div>
                        <div v-else>
                            {{ scope.row.name }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="创建人"
                    width="80"
                    prop="creator_nick"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    label="创建时间"
                    width="150"
                    prop="create_time"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">{{
                        scope.row.create_time | dataFormat1
                    }}</template>
                </el-table-column>
                <el-table-column
                    label="文件大小"
                    width="60"
                    prop="size"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">{{
                        scope.row.size | fileSize
                    }}</template>
                </el-table-column>
                <el-table-column label="文件预览" width="60">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            v-if="isImage(scope.row.path)"
                            @click="showImageList(scope.row)"
                        >
                            预览
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column
                    label="文件下载"
                    width="60"
                    prop="path"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <a
                            :href="scope.row.path"
                            target="_blank"
                            download
                            ><el-button type="primary" size="mini"
                                >下载</el-button
                            ></a
                        >
                    </template>
                </el-table-column>
                <el-table-column label="文件删除" width="60">
                    <template slot-scope="scope">
                        <el-button
                            type="danger"
                            size="mini"
                            @click="deleteUploadFile(scope.row)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <xpt-image
            :images="imageList"
            :show="ifShowImage"
            :ifUpload="false"
            @close="closeShowImage"
        >
        </xpt-image>
    </div>
</template>
<script>
import validate from "@common/validate.js";
import Fn from "@common/Fn.js";
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            appealData: {
                appeal_msg: "",
                appeal_msg_read:"",
                appeal_msg_higher_opinion_read:""
            },
            fromType: "",
            appeal_obj: {
                START: "申诉理由：",
                MODIFY: "申诉理由：",
                HIGHERREJECT: "驳回理由：",
                HIGHERPASS: "通过理由：",
                REFUNDNOPASS: "不通过理由：",
                REFUNDPASS: "通过理由：",
            },
            appeal_reason: "",
            rules: {
                appeal_msg: validate.isNotBlank({
                    self: self,
                }),
            },
            filelist: [], //附件列表
            filelistSelect: [], //附件选择列表
            imageList: [], //预览图片列表
            ifShowImage: false, //是否显示预览图片列表
        };
    },
    mounted() {
        console.log(this.params)
        this.fromType = this.params && this.params.fromType;
        this.appeal_reason = this.appeal_obj[this.fromType];
        if (this.fromType == "GETFILELIST") {
            this.getFileList();
        }
        if(this.fromType == "MODIFY"){
            this.appealData.appeal_msg=this.params.appeal_msg;
        }
        if(['HIGHERREJECT','HIGHERPASS'].includes(this.fromType)){
            this.appealData.appeal_msg_read=this.params.appeal_msg_read;
        }
        if(['REFUNDNOPASS','REFUNDPASS'].includes(this.fromType)){
            this.appealData.appeal_msg_read=this.params.appeal_msg_read;
            this.appealData.appeal_msg_higher_opinion_read=this.params.appeal_msg_higher_opinion_read;
        }
    },
    methods: {
        makeSure() {
            this.$refs.appealData.validate((valid) => {
                if (!valid) return;
                this.params && this.params.callback(this.appealData);
                this.$root.eventHandle.$emit(
                    "removeAlert",
                    this.params.alertId
                );
            });
        },
        makeSureSoft() {
            this.params && this.params.callback(this.appealData);
            this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
        },
        //初始化附件列表
        getFileList() {
            let url = "/file-iweb/api/cloud/file/list";
            let params = this.params.uploadDataObj;
            this.$axios("post", url, params)
                .then((res) => {
                    if (res.result) {
                        this.filelist = res.content.list;
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .catch((err) => {
                    this.$message.error(err);
                });
        },
        isImage(str) {
            var reg = /\.(png|jpg|gif|jpeg|webp)$/i;
            return reg.test(str);
        },
        //初始化预览列表
        initImageList(row) {
            let self = this;
            let imglist = [];
            //过滤出只有图片的列表
            let list = this.filelist.filter((item) => this.isImage(item.path));
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = value.path;
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = value.creator_nick;
                obj.isPucture = true;
                //确定要预览那张图片
                if (value.cloud_file_id === row.cloud_file_id) {
                    obj.active = true;
                } else {
                    obj.active = false;
                }
                imglist.push(obj);
            });
            this.imageList = imglist;
        },
        // 附件列表选择
        handleSelectionChange(data) {
            this.filelistSelect = data;
            console.log("filelistSelect", this.filelistSelect);
        },
        showImageList(row) {
            this.initImageList(row);
            this.ifShowImage = true;
        },
        deleteUploadFile(row) {
            let self = this;
            this.$root.eventHandle.$emit("openDialog", {
                txt: "是否确定删除？",
                okTxt: "确定",
                cancelTxt: "取消",
                noShowNoTxt: true,
                ok() {
                    var list_cloud_file_id = [];
                    list_cloud_file_id.push(row.cloud_file_id);
                    self.ajax.postStream(
                        "/file-iweb/api/cloud/file/delete",
                        { list_cloud_file_id: list_cloud_file_id },
                        (res) => {
                            if (res.body.result) {
                                self.$message.success("删除成功");
                                self.getFileList();
                            } else {
                                self.$message.error(res.body.msg);
                            }
                        }
                    );
                },
                cancel() {
                    self.$message.success("已取消");
                },
            });
        },
        closeShowImage() {
            this.ifShowImage = false;
            //关闭图片预览更新图片附件列表
            this.getFileList();
        },
    },
};
</script>
<style scoped>
.el-form {
    white-space: nowrap;
}
.chooseFile {
    height: auto !important;
    position: absolute;
    left: 0;
    right: 0;
    top: 0px;
    bottom: 0;
}
.chooseFileTable {
    height: auto !important;
    left: 0;
    right: 0;
    position: absolute;
    top: 0;
    bottom: 0;
}
.showImg {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #dbdbdb;
    margin: 5px 0;
    line-height: 100%;
}
.myRow{
    display:flex;
    flex-direction: column;
}
.myRow .el-col{
    height:100px;
}
</style>
<style>
.el-table .el-table__body-wrapper td .cell {
    height: auto;
}
.el-table .el-table__header tr th:first-child .cell {
    text-overflow: clip;
}
.el-table .el-table__body tr td:first-child .cell {
    text-overflow: clip;
}
</style>