<!-- 退款责任申诉明细列表--> 
<template>
    <xpt-list-dynamic
        ref="list"
        :data="list"
        :btns="btns"
        :colData="cols"
        :selection="selection"
        :pageTotal="count"
        :searchPage="search.page_name"
        @search-click="presearch"
        @selection-change="handleSelectionChange"
        @page-size-change="sizeChange"
        @current-page-change="pageChange"
    ></xpt-list-dynamic>
</template>
<script>
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            search: {
                page_name: "aftersale_refund_analysis_complain",
                where: [],
                page_size: self.pageSize,
                page_no: 1,
            },
            list: [],
            selection: "checkbox",
            count: 0,
            multipleSelection: [], //列表选择索引
            btns: [
                {
                    type: "success",
                    txt: "刷新",
                    click: self.searching,
                    loading: false,
                },
            ],
            cols: [
                {
                    prop: "create_time",
                    label: "创建时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "appeal_no",
                    label: "申诉单号",
                    redirectClick: (d) =>
                        self.openOrder(d.sub_parent_id, d.sub_bill_no),
                    width: 140,
                },
                {
                    prop: "submit_time",
                    label: "提交申诉时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "appeal_name",
                    label: "申诉人",
                    width: 80,
                },
                {
                    prop: "higher",
                    label: "上级处理人",
                    width: 80,
                },
                {
                    prop: "higher_audi_result",
                    label: "上级处理结果",
                    width: 130,
                    formatter: (val) => {
                        return (
                            {
                                NOREADY: "待处理",
                                PASS: "通过",
                                NOPASS: "不通过",
                                REJECT: "驳回",
                            }[val] || val
                        );
                    },
                },
                {
                    prop: "higher_audit_time",
                    label: "上级处理时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "refunder",
                    label: "退款专员",
                    width: 90,
                },
                {
                    prop: "refund_audit_result",
                    label: "专员处理结果",
                    width: 100,
                    formatter: (val) => {
                        return (
                            {
                                NOREADY: "待处理",
                                PASS: "通过",
                                NOPASS: "不通过",
                                REJECT: "驳回",
                            }[val] || val
                        );
                    },
                },
                {
                    prop: "refund_audit_time",
                    label: "专员处理时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "bill_no",
                    label: "责任分析单单号",
                    width: 140,
                },
                {
                    prop: "sub_bill_no",
                    label: "责任分析子单单号",
                    width: 160,
                },
                {
                    prop: "buyer_nick",
                    label: "买家昵称",
                    width: 140,
                },
                {
                    prop: "merge_trade_no",
                    label: "合并单号",
                    width: 170,
                },
                {
                    prop: "original_bill_type",
                    label: "来源类型",
                    width: 100,
                    formatter: (val) => {
                        return {
                            RETURNS: "退换货跟踪单",
                            SUPPLY: "补件单",
                            REFUND: "退款单",
                            AFTERSALES: "售后单",
                            AFTERSALE_ORDER: "售后单",
                            REPAIR: "服务单",
                            SERVICE: "4PL平台服务单",

                            RETURNS_BILL: "退换货跟踪单",
                            SUPPLY_BILL: "补件单",
                            REFUND_BILL: "退款单",
                            AFTERSALES_BILL: "售后单",
                            AFTERSALE_ORDER_BILL: "售后单",
                            REPAIR_BILL: "服务单",
                            SERVICE_BILL: "4PL平台服务单",

                            OUTBOUNDSUPPLY: "补件其他出库单",
                            ANALYSIS: "责任分析单",
                            BATCH: "批次订单",

                            BOUGHT_SUPPLY: "采购订单",
                            REFUND_NEW: "新退款单",
                            REFUND_ORDER: "先退款后责任",
                        }[val]||val;
                    },
                },
                {
                    prop: "original_bill_no",
                    label: "来源单号",
                    width: 170,
                },
                {
                    prop: "if_dealer",
                    label: "是否经销",
                    width: 70,
                    formatter: (val) => {
                        return (
                            {
                                Y: "是",
                                N: "否",
                            }[val] || val
                        );
                    },
                },
                {
                    prop: "dealer_customer_name",
                    label: "经销名称",
                    width: 150,
                },
                {
                    prop: "dealer_customer_number",
                    label: "经销编码",
                    width: 150,
                },
                {
                    prop: "locker_name",
                    label: "锁定人",
                    width: 90,
                },
                {
                    prop: "lock_time",
                    label: "锁定时间",
                    format: "dataFormat1",
                    width: 130,
                },
                {
                    prop: "liability_status",
                    label: "责任状态",
                    width: 100,
                    formatter: (val) => {
                        return (
                            {
                                APPEAL: "申诉",
                                WAIT_SUBMIT: "待提交",
                                WAIT_CONFIRM: "待确认",
                                COMPLAIN: "申诉",
                                END: "终止",
                                WAIT_REVOKE: "待撤回",
                                RECALLING: "撤回中",
                                RECALLED: "已撤回",
                                RECALL_FAIL: "撤回失败",
                                WAIT_CONDFIRM: "待确认",
                                CONFIRMED: "已确认",
                            }[val] || val
                        );
                    },
                },
                {
                    prop: "status",
                    label: "单据状态",
                    width: 60,
                    formatter: (val) => {
                        return { CREATE: "创建", CLOSED: "关闭" }[val] || val;
                    },
                },
                {
                    prop: "sub_liability_status",
                    label: "子单责任状态",
                    width: 100,
                    formatter: (val) => {
                        return (
                            {
                                APPEAL: "申诉",
                                WAIT_SUBMIT: "待提交",
                                WAIT_CONFIRM: "待确认",
                                COMPLAIN: "申诉",
                                END: "终止",
                                WAIT_REVOKE: "待撤回",
                                RECALLING: "撤回中",
                                RECALLED: "已撤回",
                                RECALL_FAIL: "撤回失败",
                                WAIT_CONDFIRM: "待确认",
                                CONFIRMED: "已确认",
                            }[val] || val
                        );
                    },
                },
                {
                    prop: "aftersale_processor_name",
                    label: "上游处理人",
                    width: 90,
                },
                {
                    prop: "salesman_name",
                    label: "业务员",
                    width: 90,
                },
                {
                    prop: "salesman_group_name",
                    label: "业务员分组",
                    width: 90,
                },
            ],
        };
    },
    methods: {
        _getOrderList() {
            let newSetBillNo = [],
                newSetOrder = [];

            this.list.forEach((obj) => {
                if (newSetBillNo.indexOf(obj.sub_parent_id) === -1) {
                    //去掉重复责任分析单号
                    newSetBillNo.push(obj.sub_parent_id);
                    newSetOrder.push({
                        id: obj.sub_parent_id,
                        sub_bill_no: obj.sub_bill_no,
                    });
                }
            });

            return newSetOrder;
        },
        openOrder(id, sub_bill_no) {
            this.$root.eventHandle.$emit("creatTab", {
                name: "责任分析单详情",
                params: {
                    id,
                    sub_bill_no,
                    orderList: JSON.parse(JSON.stringify(this._getOrderList())),
                },
                component: () => import("@components/duty/return_detail"),
            });
        },
        presearch(list, resolve) {
            this.search.where = list;
            this.searching(resolve);
        },
        searching(resolve) {
            let self = this;
            this.btns[0].loading = true;
            this.ajax.postStream(
                "/afterSale-web/api/aftersale/refundAnalysisComplain/list",
                this.search,
                (res) => {
                    if (res.body.result) {
                        self.count = res.body.content.count;
                        if (res.body.content.list) {
                            self.list = res.body.content.list;
                        }
                        self.$message.success(res.body.msg);
                    } else {
                        self.list = [];
                        self.count = 0;
                        self.$message.error(res.body.msg);
                    }
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                },
                (err) => {
                    self.$message.error(err);
                    self.btns[0].loading = false;
                    "function" === typeof resolve ? resolve() : this;
                }
            );
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        sizeChange(size) {
            // 第页数改变
            this.search.page_size = size;
            this.searching();
        },
        pageChange(page_no) {
            // 页数改变
            this.pageNow = page_no;
            this.search.page_no = page_no;
            this.searching();
        },
    },
    mounted() {
        let self = this;
        self.searching();
    },
    destroyed() {},
};
</script>
<style module>
</style>