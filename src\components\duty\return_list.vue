<!--退款责任人列表-->
<template>
<div class="xpt-flex">
	<xpt-list-dynamic
		:data='roleList'
		:colData='colData'
		@selection-change='s => selectData = s'
		selection='checkbox'
		:pageTotal='pageTotal'
    	:searchPage='searchParams.page_name'
		:isNeedClickEvent="true"
		:showSelectRowNumByOnlyKey="true"
		:showCount ='showCount'
		@count-off="countOff"
    	@search-click='searchClicks'
		@page-size-change='pageChange'
		@current-page-change='currentPageChange'
		@row-dblclick="d => openOrder(d.parent_id, d.sub_bill_no)"
		ref='xptList'
	>
    <el-col :span="24" slot="btns">
      <el-button type="primary" size="mini" @click="lock" :disabled="!selectData.length">锁定</el-button>
      <el-button type="primary" size="mini" @click="pass" :disabled="!selectData.length">转交</el-button>
      <el-button type="primary" size="mini" @click="searchFun('init')">刷新</el-button>
      <el-button type="primary" size="mini" @click="download">导出</el-button>
      <el-button type="primary" size="mini" @click="downloadResult">导出结果</el-button>
    </el-col>
	</xpt-list-dynamic>
</div>
</template>

<script>
export default {
	props:['params'],
	data (){
		let self = this
		return {
			roleList:[],
			selectData: [],
			countOffFlag:false,
			showCount:false,
			/*searchParams: {
				pageNo: 1,
				pageSize: 50,
				// sys_trade_id: this.params.sys_trade_id, // 搜索条件
			},*/

			searchParams:{
				from_type: 'REFUND_ORDER',
				page_name: 'aftersale_analysis_sub',
				where: [],
				pageSize: self.pageSize,
				pageNo: 1

				/*page_size:self.pageSize,
				page_no : 1*/
				/*page: {
					length: self.pageSize,
					pageNo: 1
				}*/
			},

			pageTotal:0,
			colData: [
			  {
				label: '创建时间',
				prop: 'create_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '责任分析子单',
				prop: 'sub_bill_no',
				redirectClick: d => this.openOrder(d.parent_id, d.sub_bill_no),
				width: 160,
			},{
				label: '买家昵称',
				prop: 'buyer_nick',
				width: 120,
			},{
				label: '合并单号',
				prop: 'merge_trade_no',
				width: 180,
			},{
				label: '子单来源类型',
				prop: 'original_bill_type',
				formatter: prop => ({
					RETURNS: '退换货跟踪单',
					SUPPLY: '补件单',
					REFUND: '退款单',
					AFTERSALES: '售后单',
					REPAIR: '服务单',
					SERVICE: '4PL平台服务单',
					REFUND_NEW: '新退款单',
				}[prop]),
				width: 100,
			},{
	            label: '是否经销商订单',
	            width:'100',
	            prop: 'if_dealer',
	            formatter: prop => ({
	              Y: '是',
	              N: '否',
	            }[prop] || prop),
	        },{
				label: '经销商编码',
				prop: 'dealer_customer_number',
				width: 130,
			},{
				label: '经销商名称',
				prop: 'dealer_customer_name',
				width: 100,
			},{
				label: '子单来源单号',
				prop: 'original_bill_no',
				width: 180,
			},{
				label: '锁定人',
				prop: 'locker_name',
			},{
				label: '锁定时间',
				prop: 'lock_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '单据状态',
				prop: 'status',
				width: 60,
				formatter: prop => ({
					CREATE: '创建',
					CLOSED: '关闭',
				}[prop]),
			},/*{
				label: '提交时间',
				prop: 'submit_time',
				format: 'dataFormat1',
				width: 150,
			},*//*{
				label: '提交状态',
				prop: 'a3',
			},*/{
				label: '责任子单责任状态',
				prop: 'sub_liability_status',
				width: 110,
				formatter: prop => ({
					APPEAL: '申诉',
					WAIT_SUBMIT: '待提交',
			        WAIT_CONFIRM: '待确认',
			        COMPLAIN: '申诉',
			        END: '终止',
			        WAIT_REVOKE: '待撤回',
			        RECALLING: '撤回中',
			        RECALLED: '已撤回',
			        RECALL_FAIL: '撤回失败',
					WAIT_CONDFIRM: '待确认',
			        CONFIRMED: '已确认',
				}[prop]),
			}/*,{
				label: '责任子单费用状态',
				prop: 'a3',
			}*/,{
				label: '售后单号',
				prop: 'after_order_no',
				width: 150,
			},{
				label: '责任分析单',
				prop: 'bill_no',
				width: 150,
			},{
				label: '责任状态',
				prop: 'liability_status',
				width: 60,
				formatter: prop => ({
					APPEAL: '申诉',
					WAIT_SUBMIT: '待提交',
			        WAIT_CONFIRM: '待确认',
			        COMPLAIN: '申诉',
			        END: '终止',
			        WAIT_REVOKE: '待撤回',
			        RECALLING: '撤回中',
			        RECALLED: '已撤回',
			        RECALL_FAIL: '撤回失败',
					WAIT_CONDFIRM: '待确认',
			        CONFIRMED: '已确认',
				}[prop]),
			},{
        label: '责任确认时间',
        prop: 'sub_confirm_time',
        width: 130,
        format:"dataFormat1"
      },{
        label: '责任申诉超时时间',
        prop: 'sub_timeout',
        width: 140,
        format:"dataFormat1"
      },{
				label: '费用状态',
				prop: 'fee_status',
				width: 60,
				formatter: prop => ({
					UNFINISHED: '未完结',
					FINISHED: '已完结',
					DIVIDED: '已分摊',
				}[prop]),
			},{
				label: '资料提交状态',
				prop: 'info_submit_status',
				formatter: prop => ({
					NO_NEED: '无需提供',
			        WAIT_PROVIDE: '等待提交',
			        PROVIDED: '提交完成',
			        CONFIRMED: '确认',
			        OVERTIME: '超时交馈',
				}[prop] || prop),
        width:110
			},{
				label: '资料请求时间',
				prop: 'request_submit_time',
				format:"dataFormat1",
				width: 150,
			},{
				label: '资料提交时间',
				prop: 'info_submit_time',
				format:"dataFormat1",
				width: 150,
			},{
				label: '上游处理人',
				prop: 'aftersale_processor_name',
			},{
				label: '业务员',
				prop: 'salesman_name'
			},{
				label: '业务员分组',
				prop: 'salesman_group_name',
			}],
		}
	},
	methods: {
		// 导出
		download(){
			this.ajax.postStream(
				"/reports-web/api/reports/aftersale/analysis/refund/excelExport?permissionCode=ANALYSIS_ORDER_QUERY",
				this.searchParams,
				(res) => {
				if (res.body.result) {
					res.body.msg && this.$message.success(res.body.msg);
				} else {
					res.body.msg && this.$message.error(res.body.msg);
				}
				},
				(err) => {
				this.$message.error(err);
				}
			);
		},
		// 导出结果
		downloadResult(){
			this.$root.eventHandle.$emit("alert", {
				params: {
				query: {
					type: "EXCEL_TYPE_AFTERSALE_ANALYSIS_REFUND_EXPORT",
				},
				},
				component: () => import("@components/after_sales_report/export"),
				style: "width:800px;height:400px",
				title: "导出结果",
			});
		},
		searchClicks(obj,resolve){
			this.searchParams.where = obj
			// this.searchFun('',resolve);
			new Promise((res,rej)=>{
				this.searchFun(null, resolve,res)

			}).then(()=>{
				if(this.searchParams.pageNo != 1){
					this.pageTotal = 0;
				}
				this.showCount = false;
			})
		},
		pass (e){
			this.$root.eventHandle.$emit('alert',{
				title: '转交人列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/role/userList'),
				params: {
					callback: d => {
						this._ajax('turntoBatch?permissionCode=ANALYSIS_ORDER_TRANSMIT', {
							ids: this.selectData.map(obj => obj.parent_id),
							new_person_id: d.id,
							if_syn: false,
						})
					}
				},
			})
		},
		lock (e){
			var $btn = e.target
			$btn.disabled = true
			this._ajax('lockBatch?permissionCode=ANALYSIS_ORDER_LOCK', this.selectData.map(obj => obj.parent_id), () => {
				$btn.disabled = false
			}, () => {
				$btn.disabled = false
			})
		},
		countOff(){

			let self = this,
			url = "/afterSale-web/api/aftersale/analysis/listCount?permissionCode=ANALYSIS_ORDER_QUERY";
			if(!self.roleList.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;

			self.ajax.postStream(url,self.searchParams,function(response){
					if(response.body.result){

						self.pageTotal = response.body.content;
						self.showCount = true;
						self.countOffFlag = false;

					}else{
						self.$message.error(response.body.msg);
					}
				});
		},
		searchFun(isInit, cb,cb2){
			let self = this;
			if(isInit){
				this.searchParams.pageSize = this.pageSize;     //页数
				this.searchParams.pageNo = 1;   //页码

			}
			console.log('asdfasdfsd',this.searchParams);
			this._ajax('list?permissionCode=ANALYSIS_ORDER_QUERY', this.searchParams, res => {
				// this.roleList = res.body.content.list
				// this.pageTotal = res.body.content.count
				let dataList = JSON.parse(JSON.stringify(res.body.content.list));
					if(dataList.length == (self.searchParams.pageSize+1)&&dataList.length>0){
						dataList.pop();
					}

					self.roleList = dataList;

					let totalCount = self.searchParams.pageNo*self.searchParams.pageSize;
					if(!self.showCount){
						self.pageTotal = res.body.content.count == (self.searchParams.pageSize+1)? totalCount+1:totalCount;
					}
					this._getOrderList()
					cb2&&cb2()

				if(cb) cb()
				else this.$message.success(res.body.msg)
			})
		},
		_getOrderList (){
			var newSetBillNo = []
			,	newSetOrder = []

			this.roleList.forEach(obj => {
				if(newSetBillNo.indexOf(obj.parent_id) === -1){//去掉重复责任分析单号
					newSetBillNo.push(obj.parent_id)
					newSetOrder.push({
						id: obj.parent_id,
						sub_bill_no: obj.sub_bill_no,
					})
				}
			})

			this._getOrderList.orderList = newSetOrder
		},
		_ajax (apiName, postData, cb, errCb){
			this.ajax.postStream('/afterSale-web/api/aftersale/analysis/' + apiName, postData, res => {
				if(res.body.result){
					if(apiName !== 'list?permissionCode=ANALYSIS_ORDER_QUERY'){
						this.$message.success(res.body.msg)
						this.searchFun('', cb)
					}else {
						cb && cb(res)
					}
				}else {
					this.$message.error(res.body.msg)
				}
			}, errCb, this.params.tabName)
		},
		openOrder (id, sub_bill_no){
			this.$root.eventHandle.$emit('creatTab',{
				name: '责任分析单详情',
				params: {
					id,
					sub_bill_no,
					orderList: JSON.parse(JSON.stringify(this._getOrderList.orderList)),
				},
				component:()=> import('@components/duty/return_detail')
			})
		},
		pageChange(pageSize){

				this.searchParams.pageSize = pageSize;     //页数

			//this.searchParams.page.length = pageSize;
			this.searchFun()
		},
		currentPageChange(page){

			this.searchParams.pageNo = page;   //页码
			this.searchFun()
		},
	},
	mounted (){
		this.searchFun()
	},
}
</script>
