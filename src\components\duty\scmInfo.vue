<!-- 品质反馈 -->
<template>
	<div>
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type='primary' size='mini' @click="preSave('submitData')" >保存</el-button >
			</el-col>
		</el-row>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="基本信息" name="orderInfo">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="submitData" :rules="rules" ref="submitData">
						<el-col :span='8'>
							<el-form-item label="责任问题" prop="liability_question_code">
								<el-input size='mini' v-model="submitData.liability_question" disabled></el-input>
							</el-form-item>
							<el-form-item label="组别" prop="group_name">
								<el-input size='mini' v-model="submitData.group_name" disabled></el-input>
							</el-form-item>
							<el-form-item label="备注" prop="remarks">
								<el-input size='mini' v-model="submitData.remarks" ></el-input>
							</el-form-item>
						</el-col>
						
						
					
						<el-col :span='8'>
							
							<el-form-item label="产品类型" prop="product_type_code">
								<el-select v-model='submitData.product_type_code' label-width="150px" @change="productChange" size='mini'>
									<el-option v-for='item in baseInfo' :key='item.itemType' :label='item.itemTypeName' :value='item.itemType'></el-option>
								</el-select>
								<el-tooltip v-if='rules.product_type_code[0].isShow' class="item" effect="dark" :content="rules.product_type_code[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="售后物料" prop="aftersale_materials_code">
								<el-select v-model='submitData.aftersale_materials_code' label-width="150px" @change="attributeChange" size='mini'>
									<el-option v-for='item in saleMaterial' :key='item.attributeCode' :label='item.attributeName' :value='item.attributeCode'></el-option>
								</el-select>
								<el-tooltip v-if='rules.aftersale_materials_code[0].isShow' class="item" effect="dark" :content="rules.aftersale_materials_code[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="受损情况" prop="materials_damage_code">
								<el-select v-model='submitData.materials_damage_code' label-width="150px"  @change="dutyChange" size='mini'>
									<el-option v-for='item in dutyList' :key='item.attributeCode' :label='item.attributeName' :value='item.attributeCode'></el-option>
								</el-select>
								<el-tooltip v-if='rules.materials_damage_code[0].isShow' class="item" effect="dark" :content="rules.materials_damage_code[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>	
						
			    </el-form>
			  </el-tab-pane>
			</el-tabs>
		</el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js';
	import Fn from '@common/Fn.js';
	export default {
		props:["params"],
		data() {
			var _this = this;
			var self = this;
			return{
				firstTab:"orderInfo",
				baseInfo:[],
				saleMaterial:[],
				dutyList:[],
				submitData:{parent_id:null,
					analysis_person_id:null,
					liability_question:null,
					liability_question_code:null,
					group_name:null,
					product_type:null,
					product_type_code:null,
					aftersale_materials:null,
					aftersale_materials_code:null,
					materials_damage:null,
					materials_damage_code:null,
					group_code:null,
					remarks:null
				},
				
				
				rules:{
					product_type_code:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					aftersale_materials_code:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
					materials_damage_code:validate.isNotBlank({
						required:true,
						self:self,
						msg:"请输入地址",
						trigger:"change"
					}),
				},
			}
		},
		methods : {
			productChange(value){
				let self = this;
				self.baseInfo.forEach(item=>{
					if(item.itemType == value){
						self.saleMaterial = item.saleMaterial;
						self.submitData.group_name = item.materialGroupName;
						self.submitData.group_code = item.materialGroup;
						self.submitData.product_type = item.itemTypeName;
						self.submitData.aftersale_materials_code = '';
						self.submitData.aftersale_materials = '';
						self.submitData.materials_damage_code = '';
						self.submitData.materials_damage = '';

					}
				})
			},
			attributeChange(value){
				let self = this;
				console.log(value)
				self.saleMaterial.forEach(item=>{
					if(item.attributeCode == value){
						self.dutyList = item.dutyList;
						self.submitData.aftersale_materials = item.attributeName;
						self.submitData.materials_damage_code = '';
						self.submitData.materials_damage = '';
					}
				})
			},
			dutyChange(value){
				let self = this;
				self.dutyList.forEach(item=>{
					if(item.attributeCode == value){
						// self.dutyList = item.dutyList;
						self.submitData.materials_damage = item.attributeName;

					}
				})
			},
			preSave(formName){
				var self = this;
				// if(this.submitData.bill_type_id=='PRESENT' && !this.submitData.arm_mager_id){
				// 	this.$message.error('赠品订单请选择合并订单号');
				// 	return;
				// }	
				self.$refs[formName].validate((valid) => {
					if(!valid) return

					self.save()
				})
			},
			save(callback){	
				var self = this,
				params={};
				var url = "/afterSale-web/api/aftersale/feedback/add";
				
				let saveData = JSON.parse(JSON.stringify(this.submitData));
				// delete saveData.remarks;
				//delete saveData.groupName;
				this.ajax.postStream(url,/*self.submitData*/saveData,(response)=>{
					if(response.body.result){
						this.$message({message: '操作成功',type: 'success'});
						// params.sys_trade_id = response.body.content.sys_trade_id
						this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
					}else{
						this.$message.error(response.body.msg)
					}
				},e=>{
					this.$message.error(e)
				})
			},
			
		},
		mounted:function(){
			var self = this;
			console.log(self.params);
			self.baseInfo = self.params.data;
			self.submitData.parent_id = self.params.personSelect.analysis_sub_id;
			self.submitData.analysis_person_id = self.params.personSelect.id;
			// self.submitData.group_name = self.params.personSelect.group_name;
			self.submitData.remarks = self.params.personSelect.remarks;
			self.submitData.liability_question_code = self.params.personSelect.liability_question;
			self.submitData.liability_question = self.params.personSelect.liability_question_description;
			
		}
	}
</script>