<!-- 责任分析客户接口 -->
<template>
<div class="xpt-flex">
	<el-row	class='xpt-top'	:gutter='40'>
		<el-button type='primary' size='mini' @click='() => submit()' :disabled="!selectObject">确认</el-button>
		
	</el-row>
	<xpt-list
		:data='roleList'
		:showHead="false"
		:colData='colData'
		:pageTotal='pageTotal'
		selection="radio"
		@search-click='searchFun'
		@radio-change='select'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
		@row-dblclick="submit"
	></xpt-list>
</div>
</template>

<script>
export default {
	props: ['params'],
	data (){
		var self=this;
		return {
			roleList: [],
			selectObject: null,
			pageTotal: 0,
			search:{
				merge_trade_id: self.params.otherParams.mergeTradeId,
				page_no: 1,
				page_size: 50
			},
			// btns: [{
			// 	type: 'success',
			// 	txt: '确认',
			// 	// disabled: true,
			// 	click: this.submit,
			// }],
			colData: [{
				label: '客户编码',
				prop: 'number',
			},  {
				label: '昵称',
				prop: 'name',
			}, {
				label: '客户类型',
				prop: 'member_type',
				formatter: (val)=>{
					var obj = {
						'TB' : '淘宝客户',
						'B2C' : '商城客户',
						'' : '其他客户',
					}
					return obj[val];
				} 
			}],
		}
	},
	methods: {
		getSearch (){
			if(!this.search.merge_trade_id) return;
			this.ajax.postStream('/order-web/api/mergetrade/getAnalysisHandler', this.search, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
					this.roleList = res.body.content.list.map(obj => {
						obj._real_name_nick_name = [obj.real_name, obj.nick_name].filter(Boolean).join('.')
						return obj
					})
					this.pageTotal = res.body.content.count
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		searchFun (text){
			this.search.field_value = text ? text : (this.search.field_value === this.params.best_staff_name ? this.params.best_staff_name : '')
			this.getSearch()
		},
		sizeChange(size){
			console.log('size',size)
			// 第页数改变
			this.search.page_size = size;
			this.getSearch();
		},
		pageChange (page_no){
			console.log('page_no',page_no)
			// 页数改变
			// this.pageNow = page_no;
			this.search.page_no = page_no;
			this.getSearch();
		},
		select (selectData){
			this.selectObject = selectData
		},
		submit (row){
			if(row) this.selectObject = row

			this.params.callback(this.selectObject)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
	},
	mounted (){
		// this.search.field_value = this.params.best_staff_name
		this.getSearch()
	}
}
</script>	