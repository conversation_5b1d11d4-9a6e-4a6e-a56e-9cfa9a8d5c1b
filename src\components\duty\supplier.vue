<!--责任分析单选择供应商弹出框-->
<template>
  <div class='xpt-flex'>
    <el-row class='xpt-top' :gutter='40'>
      <el-col :span='3'>
        <el-button type='info' size='mini' @click="confirmAddGoods">确认</el-button>
      </el-col>
      <el-col :span='21' class='xpt-align__right' >
        <el-input placeholder="请输入供应商名称或简称或编码" size='mini' v-model="search.key"></el-input>

        <el-button type='primary' size='mini' @click="searchList">查询</el-button>
        <el-button type='primary' size='mini' @click="reset">重置</el-button>
      </el-col>
    </el-row>

    <xpt-list
      ref='list'
      selection='radio'
      :showHead = 'false'
      :data='list'
      :colData='colData'
      :pageTotal='count'
      @row-dblclick="rowDblclick"
      searchHolder='请输入搜索条件'
      @radio-change='select'
      @page-size-change='sizeChange'
      @current-page-change='pageChange'>
    </xpt-list>
  </div>


</template>
<script>
  export default {//需要类型和相关的id
    props:['params'],
    data(){
      var self = this;
      return {

        search:{
            page: {
              page_size:self.pageSize,
              page_no:1,
            },
            key : "",
            list_type : self.params.otherParams.list_type || []//先默认三包物流

        },
        count:0,
        selectedData:'',
        list:[],
        colData:[
         {
         label: '名称',
         prop: 'name'
         //format:'yesOrNo'
         }, {
         label: '简称',
         prop: 'short_name'
         }, {
         label: '备注',
         prop: 'description'
         }, {
         label: '编码',
         prop: 'number'
         }/*, {
         label: 'type',
         prop: 'materialUnit'
         }*/]
      }
    },
    methods:{
        //重置查询条件
      reset(){
          this.search.key="";
      },
      /**
       *双击事件
       **/
      rowDblclick(data){
        this.selectedData=data;
        this.confirmAddGoods();

      },

      /**
       *关闭当前弹出框组件
       */
      closeCurrentComponent(){
        this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
      },
      /**
       *确认回调函数
       **/
      confirmAddGoods(){
        var selectedData = this.selectedData;
        if(!selectedData){
          this.$message.error('请选择数据');
          return;
        }

        this.params.callback && this.params.callback({data:selectedData});
        this.closeCurrentComponent();
      },

      sizeChange(size){
        // 每页加载数据
        this.search.page.page_size = size;
        this.search.page.page_no = 1;

        this.getList();
      },
      pageChange(page_no){
        // 页数改变
        this.search.page.page_no = page_no;
        this.getList();
      },
      select(selections){
        this.selectedData = selections;
      },
     searchList(searchString, reslove){
        this.getList(reslove);
      },
     /**
       *获取供应商列表
       */
      getList(reslove){
        var data = this.search.page;
        data.key = this.search.key;
        data.list_type = this.search.list_type;

        this.ajax.postStream("/afterSale-web/api/aftersale/ticketSupply/pageResultSupplier" + (data.list_type.length ? "ByType" : ''),data,d=>{
          if(!d.body.result){
            this.$message.error(d.body.msg);
            reslove && reslove();
            return;
          }
          this.list = d.body.content.list;
          this.count = d.body.content.count;
          reslove && reslove();

        });
      },



    },
    created(){
      this.getList();
    }
  }
</script>
<style type="text/css" scoped>
  .el-table__body-wrapper{margin-bottom:20px;}
</style>
