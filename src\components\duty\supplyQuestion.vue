<!-- 责任分析单-问题补充 -->
<template>
<div class="xpt-flex">
	<el-row	class='xpt-top'	:gutter='40'>
		<el-button type='primary' size='mini' @click='() => submit()' :disabled="!selectObject">确认</el-button>
		<el-col class='xpt-align__right' :span='20' style="float:right;">
			<el-select v-model="tableType" size='mini' @change="tableTypeChange" style="width:150px;">
				<el-option label="商品" value="goods"></el-option>
				<el-option label="订单" value="orders" :disabled="!!params.batch_trade_no"></el-option>
				<el-option label="服务" value="companys"></el-option>
			</el-select>
			<el-select v-model="search.limit_field" size='mini' style="width:150px;">
				<el-option 
					v-for="(value,key) in filterObj[tableType]" 
					:key="key.toString()" 
					:label="value.toString()"
					:value="key.toString()">
				</el-option>
			</el-select>
			<el-select v-model="search.operator" size='mini' style="width:100px;">
				<el-option label="等于" value="="></el-option>
				<el-option label="包含" value="%"></el-option>
			</el-select>
			<el-input placeholder="请输入查询值" size='mini' v-model="search.field_value"></el-input>
			<el-button type='primary' @click="getSearch" size='mini'>查询</el-button>
		</el-col>
	</el-row>
	<xpt-list
		:data='roleList'
		:showHead="false"
		:colData='colDataObj[tableType]'
		:pageTotal='pageTotal'
		selection="radio"
		@search-click='searchFun'
		@radio-change='select'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
		@row-dblclick="submit"
	></xpt-list>
</div>
</template>

<script>
export default {
	props: ['params'],
	data (){
		return {
			roleList: [],
			selectObject: null,
			pageTotal: 0,
			tableType: 'goods',
			filterObj: {
				goods: {
					sys_trade_no: '销售订单号',
					material_number: '商品编码',
					material_name: '商品名称',
				},
				orders: {
					sys_trade_no: '销售订单号',
					customer_name: '买家昵称',
				},
				companys: {
					batch_trade_no: '批次单号',
					logistics_supplier_name: '服务商',
				},
			},
			search:{
				field_value: "",
				limit_field: "",
				operator: "%",
				page_no: 1,
				page_size: 50,
			},
			initSearch: {},
			colDataObj: {
				goods: [{
					label: '商品编码',
					prop: 'material_number',
					width: 130,
				}, {
					label: '商品名称',
					prop: 'material_name',
				}, {
					label: '规格描述',
					prop: 'material_specification',
					width: 160,
				}, {
					label: '数量',
					prop: 'number',
					width: 40,
				}, {
					label: '单位',
					prop: 'material_unit',
				}, {
					label: '售价',
					prop: 'act_price',
				}, {
					label: '买家昵称',
					prop: 'customer_name',
				}, {
					label: '业务员',
					prop: 'real_name',
				}, {
					label: '分组',
					prop: 'group_name',
				}, {
					label: '店铺',
					prop: 'shop_name',
					width: 110,
				}, {
					label: '销售单号',
					prop: 'sys_trade_no',
					width: 140,
				}, {
					label: '供应商',
					prop: 'supplier_company',
					width: 160,
				}, {
					label: 'BOM版本',
					prop: 'bom_version',
					width: 160,
				}, {
					label: '采购入库日期',
					prop: 'instock_date',
					width: 160,
				}],
				orders: [{
					label: '买家昵称',
					prop: 'customer_name',
				}, {
					label: '业务员',
					prop: 'real_name',
				}, {
					label: '分组',
					prop: 'group_name',
				}, {
					label: '订单店铺',
					prop: 'shop_name',
				}, {
					label: '客户店铺',
					prop: 'user_shop_name',
				}, {
					label: '原始店铺',
					prop: 'original_shop_name',
				}, {
					label: '销售单号',
					prop: 'sys_trade_no',
				}],
				companys: [{
					label: '批次单号',
					prop: 'batch_trade_no',
				}, {
					label: '服务商',
					prop: 'logistics_supplier_name',
				}, {
					label: '发货日期',
					prop: 'zd_delivery_time',
					format: 'dataFormat1',
				}],
			},
		}
	},
	methods: {
		tableTypeChange (){
			this.search = Object.assign({}, this.initSearch)
			this.getSearch()
		},
		getSearch (){
			this.search.merge_trade_id = this.params.merge_trade_id
			this.ajax.postStream('/afterSale-web/api/aftersale/order/choose/' + {
				goods: 'pickFromGoods',
				orders: 'pickFromSysOrder',
				companys: 'pickFromBatchOrder',
			}[this.tableType], this.search, response => {
				if(response.body.result){
					this.roleList = response.body.content.list
					this.pageTotal = response.body.content.count
					// this.$message.success(response.body.msg)
				}else{
					this.list = []
					this.pageTotal = 0 
					this.$message.error(response.body.msg)
				}
			})
		},
		searchFun (text){
			this.search.field_value = text ? text : (this.search.field_value === this.params.best_staff_name ? this.params.best_staff_name : '')
			this.getSearch()
		},
		sizeChange(size){
			console.log('size',size)
			// 第页数改变
			this.search.page_size = size;
			this.getSearch();
		},
		pageChange (page_no){
			console.log('page_no',page_no)
			// 页数改变
			// this.pageNow = page_no;
			this.search.page_no = page_no;
			this.getSearch();
		},
		select (selectData){
			this.selectObject = selectData
		},
		submit (row){
			if(row) this.selectObject = row

			this.selectObject._type = this.tableType
			this.params.callback(this.selectObject)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
	},
	mounted (){
		this.initSearch = Object.assign({}, this.search)
		this.getSearch()
	}
}
</script>	