<!-- 新增、编辑订单 -->
<template>
  <div>
    <xpt-headbar>
      <div slot="left" style="display: inline-block">
        <el-button
          size="mini"
          type="primary"
          @click="
            () => {
              $refs.formCreate && $refs.formCreate.save();
            }
          "
          :loading="customerBtnStatu"
          >保存</el-button
        >
        <el-button size="mini" type="danger" @click="reset">重置</el-button>
      </div>

      <!-- <el-button type='danger' class='xpt-close' size='mini' @click="close" slot='right'>关闭</el-button> -->
    </xpt-headbar>
    <form-create
      v-if="formload"
      ref="formCreate"
      :btns="false"
      :ruleData="ruleData"
      :formData="formData"
      @save="submit"
    ></form-create>
  </div>
</template>
<script>
import formCreate from "./components/formCreate/formCreate";
import { cusTypeopt, getMap,custom_sys_budget } from "./common/clientDictionary";
import closeComponent from "./common/mixins/closeComponent";
import {
  addOrder,
  getOrderInfo,
  editOrder,
  validateReceiveInfoDeliverMethod,
  judgeAddress,
  getShopInfo,
  getdeliverByShop
} from "./common/api";
import { indexOf } from 'xe-utils';
export default {
  mixins: [closeComponent],
  props: ["params"],
  components: {
    formCreate,
  },
  data() {
    var self = this;
    return {
      shop_code:'',
      if_arrived_shop:'Y',
      customerBtnStatu: false,
      formload: false,
      formData: [],
      shopInfo:{},
      intentionTypeopt:[
        {
          label: '高',
          value: 'high'
        },
        {
          label: '中',
          value: 'middle'
        },
        {
          label: '低',
          value: 'low'
        },
      ],
      yesornoOpt:[
        {
          label: '是',
          value: 'Y'
        },
        {
          label: '否',
          value: 'N'
        },
      ],
      ruleData: Object.freeze({
        client_type: {
          required: true,
          message: "请选择订单类型",
          trigger: "change",
        },
        name: { required: true, message: "请输入客户名称", trigger: "blur" },
        receiver_name: {
          required: true,
          message: "请输入收货人姓名",
          trigger: "blur",
        },

        client_mobile: {
          validate: "mobile",
          trigger: "change",
        },
        // receiver_phone: {
        //   validate(_this){
        //     return [{
        //     required: false ,
        //     message: '请填写有效的电话号或不填',
        //     isShow: false,
        //     trigger: 'change',
        //     validator: function(rule, value, callback) {
        //         if (!_this.self) {
        //             console.log('this validate object is null');
        //             return;
        //         }
        //         if(!/^[\d-]{0,20}$/.test(value)) {
        //             _this.self.rules[ rule.field][0].isShow = true
        //             callback(new Error(''));
        //             return
        //         }
        //         _this.self.rules[ rule.field][0].isShow = false
        //         callback();
        //     }
        // }]
        //   },
        // },
        if_arrived_shop: {
          required: true,
          message: "请选择是否到店",
          trigger: "change",
        },
         arrived_shop_date: {

          validate(_this){
             return [{
            required: false ,
            message: '请选择到店日期',
            isShow: false,
            trigger: 'change',
            validator: function(rule, value, callback) {
                if (!_this.self) {
                    console.log('this validate object is null');
                    return;
                }
                console.log(self.if_arrived_shop);
                if( self.if_arrived_shop == 'Y'&& !value ) {
                    _this.self.rules[ rule.field][0].isShow = true
                    callback(new Error(''));
                    return
                }
                _this.self.rules[ rule.field][0].isShow = false
                callback();
            }
        }]
          }
        },
        // arrived_shop_date: self.if_arrived_shop =='Y'?{
        //   required: true,
        //   message: "请选择到店日期",
        //   trigger: "change",
        // }:{
        //   required: false,
        //   message: "请选择到店日期",
        //   trigger: "change",
        // },
        address: { required: true, message: "请选择省市区", trigger: "change" },
        intention: {
          required: true,
          message: "请选择客户意向",
          trigger: "change",
        },
        shop_code: {
          required: true,
          message: "请选择订单店铺",
          trigger: "change",
        },
        budget:{
          required: true,
          message: "请选择客户预算",
          trigger: "change",
        },
        receiver_address: {
          required: true,
          message: "请选择详细地址",
          trigger: "change",
        },
        order_tag: {
          required: true,
          message: "请选择客户标签",
          trigger: "change",
        },


      }),
      orderTagOptions:[],
      orderTagAux: __AUX.get('store_order_type').reduce((a, b) => {
					a[b.code] = b.name
					return a
				}, {}),
    };
  },

  async mounted() {
    // 获取物流方式
      this.infoList = await getShopInfo();
    this.shopInfo = this.params.isDisplay?this.infoList.length == 1?this.infoList[0]:{}:this.infoList[0]

    const dmRes = await getdeliverByShop({shop_code:this.shopInfo.shopCode})
      this.deliverMethod = Array.isArray(dmRes.content) ? dmRes.content[0] || {} : {}
    this.deliver_method_options = [];
    // this.getDeliver();
  // console.log(cusTypeopt)
   // 获取提货方式

   // 编辑初始化订单标签
   if(this.params.shop_name&&this.params.shop_code){
    await this.initOrderTagOptions(this.params.shop_code,this.params.shop_name);
   }else if(this.shopInfo.shopCode&&this.shopInfo.loginShopName){
     //只有一个店铺时初始化
    await this.initOrderTagOptions(this.shopInfo.shopCode,this.shopInfo.loginShopName);
   }



    if (this.params.client_number) {
      // 修改
      const clientInfo = await getOrderInfo(this.params.client_number);
      this.clientInfo = clientInfo;
      this.address_id = clientInfo.address_id;
      this.if_arrived_shop = clientInfo.if_arrived_shop;
      // 客户初始地址信息
      this.prevAddressInfo = {
        deliver_method: clientInfo.supply_transport_type, //发货方式，字符串格式
        receiver_address: clientInfo.address_name, //收件人详细地址，字符串格式
        receiver_city_number: clientInfo.receiver_city, //市ID，number类型
        receiver_district_number: clientInfo.receiver_district, //区ID，number类型
        receiver_mobile: clientInfo.client_mobile, //收件人手机，字符串格式
        receiver_name: clientInfo.receiver_name, //收件人姓名，字符串格式
        receiver_phone: clientInfo.receiver_phone, //收件人电话，字符串格式
        receiver_state_number: clientInfo.receiver_state, //省ID，number类型
        receiver_street: "",
      };
      this.setFormData(clientInfo);
      this.formload = true;
      //编辑时订单标签非必填,兼容历史数据
      this.ruleData.order_tag.required = false;
    } else {
      // 新增
      // console.log(this.params.isDisplay,this.infoList)
      this.shop_code = this.shopInfo.shopCode;
      let order_tag='';
      if(this.orderTagOptions.length===1&&this.orderTagOptions[0].value==='DDLX01'){
        order_tag='DDLX01'
      }
      this.setFormData({shop_code:this.shopInfo.shopCode,if_arrived_shop:'Y',order_tag,arrived_shop_date:new Date(),intention:'middle',budget:'3',});
      this.formload = true;
    }
    this.setShopDefault();

  },
  methods: {
    async initOrderTagOptions(shopCode,shopName){
      const orderTagRes = await this.getOrderTagInfo({
      shopName,
      shopCode
      });
      if(orderTagRes.list.length>=1){
          this.orderTagOptions=orderTagRes.list[0].store_order_type.split(",").map(item=>{
          return{
            value:item,
            label:this.orderTagAux[item]
          }
        })
      }else{
        this.orderTagOptions=[{value:'DDLX01',label:'普通订单'}]
      }
    },
    getOrderTagInfo(shopInfo){
      return new Promise(resolve=>{
        this.ajax.postStream("/material-web/api/pos/posBaseManage/list", {
          "page": {
            "length": 50,
            "pageNo": 1
          },
          "shop_name": shopInfo.shopName,
          "shop_code": shopInfo.shopCode,
          'status': 1,
        },(res)=>{
          if(res.body.result){
            resolve(res.body.content)
          }
        })
      })
    },
    getdeliverInfo(data,resolve){
      this.ajax.postStream("/custom-web/api/customSysTrade/getShopDistribution",
                data,
                (res) => {
                    if (res.body.result) {
                       resolve(res.body)
                    }
                }
            );
    },
    shopInfoInit(data,getItem){
      new Promise((resolve,reject)=>{
        this.getdeliverInfo(data,resolve)
      }).then(dmRes=>{
        this.deliverMethod = Array.isArray(dmRes.content) ? dmRes.content[0] || {} : {}
        console.log(this.formData,this.deliverMethod )
        // this.formData[1].cols[2].value = this.deliverMethod.value;
        // this.clientInfo.supply_transport_type_cn =
        let deliver_method = getItem('deliver_method');
          deliver_method.value = this.deliverMethod.value
        })

    },
    async initOrderTag({shopCode,shopName},getItem){
      const orderTag=getItem("order_tag")
      try{
        this.ajax.postStream(
                "/material-web/api/pos/posBaseManage/list",
                {
                    "page": {
                        "length": 50,
                        "pageNo": 1
                    },
                    "shop_name": shopName,
                    "shop_code": shopCode,
                    'status':1,
                },
                (res) => {
                    if (res.body.content.list.length>0&&res.body.content.list[0].store_order_type) {
                      console.log('res.body.content.list.length', res.body.content.list.length)
                      orderTag.options=res.body.content.list[0].store_order_type.split(",").map(item=>{
                        return{
                          value: item,
                          label: this.orderTagAux[item]
                        }
                      })
                      orderTag.value='';
                      if(orderTag.options.length===1){
                        orderTag.value=orderTag.options[0].value;
                        orderTag.disabled=true;
                      }else{
                        orderTag.disabled=false;
                      }
                    }else{
                      orderTag.options=[{value:'DDLX01',label:'普通订单'}]
                      orderTag.value='DDLX01';
                      orderTag.disabled=true;
                    }
                }
            );
      }catch{

      }
    },
    async getDeliver() {
      // let shopInfo = this.infoList.length? this.infoList[0]:{};

      // 物流方式限制，直营店只能三包，经销店只能物流
      getMap((map) => {
        let deliver_method_key = {
          zhiyin: ["THREES", "LOGISTICS", "PICK_UP"],
          jingxiao: ["THREES", "LOGISTICS", "PICK_UP"],
        };
        let key =
          shopInfo.shopType === "O2O_CONTRACT" ||
          shopInfo.shopType === "O2O_OWN"
            ? "zhiyin"
            : "jingxiao";
        map.deliver_method.forEach((item) => {
          item.disabeld = !deliver_method_key[key].includes(item.value);
          this.deliver_method_options.push(item);
        });
      });
    },
    setShopDefault() {
            this.setShopDefault.where = [];
            this.ajax.postStream(
                "/user-web/api/sql/listFields",
                { page: "cloud_shop_v2" },
                (res) => {
                    if (res.body.result) {
                        (res.body.content.fields || []).forEach((o) => {
                            if (o.comment === "店铺分类") {
                                this.setShopDefault.where.push({
                                    field: o.field,
                                    table: o.table,
                                    value: "HARD_DECORATION",
                                    operator: "=",
                                    condition: "AND",
                                    listWhere: [],
                                });
                            } else if (o.comment === "主营类目") {
                                this.setShopDefault.where.push({
                                    field: o.field,
                                    table: o.table,
                                    value: "DZ",
                                    operator: "=",
                                    condition: "AND",
                                    listWhere: [],
                                });
                            }
                        });
                    }
                }
            );
        },
    reset() {
      this.$refs.formCreate && this.$refs.formCreate.resetFields();
      !this.params.client_number && (this.address_id = "");
      if (!this.params.client_number && this.$refs.formCreate) {
        this.address_id = "";
        this.setClientStatus(
          this.$refs.formCreate.getItem,
          false,
          this.params.client_number !== undefined
        );
        let name = this.$refs.formCreate.getItem("name");
        name.prefix = "(DZ)";
      }
      // if(this.$refs.formCreate) {
      //   const [
      //     client_mobile
      //   ]
      // }
      // this.$refs.formCreate && this.setClientStatus(this.$refs.formCreate.getItem, false,  this.params.client_number !== undefined, [
      //   'client_mobile', 'number', 'name'
      // ])
    },
    setFormData(clientInfo = {}) {
      let self = this;
      let shopOption = [];
      this.infoList.forEach(item=>{
        shopOption.push({
          value:item.shopCode,
          label:item.loginShopName,
        })
      })
      this.formData = [
        {
          title: "客户信息",
          cols: [
            {
              formType: "elInput",
              label: "客户号",
              value: clientInfo.client_no,
              disabled: true,
              prop: "number",
              span: 12,
            },
            {
              formType: "listSelect",
              label: "客户手机",
              prop: "client_mobile",
              value: clientInfo.client_mobile,
              span: 12,
              disabled: this.params.client_number !== undefined,
              config: {
                label: clientInfo.client_mobile,
                popupType: 'customer',
                showLabel: 'receiver_mobile',
                check: false,
                clickWay: 'whole',
                formValue: {
                  client_mobile: clientInfo.client_mobile,
                }
              } ,
              event: {
                result: (result, col, formData, getItem, other) => {
                  if(!result.cust_id) {
                    let client_mobile = getItem('client_mobile')
                    client_mobile.config.label = other.client_mobile
                    client_mobile.config.formValue.client_mobile = other.client_mobile
                    client_mobile.value = other.client_mobile
                    return
                  }
                  self.setClientStatus(getItem, result, true);
                  self.address_id = result.address_id;
                  self.cust_id = result.cust_id;
                  self.if_edit = result.if_edit;
                  // 客户初始地址信息
                  self.prevAddressInfo = {
                    deliver_method: result.deliver_method, //发货方式，字符串格式
                    post_fee_type: result.post_fee_type, //运费类型	，字符串格式
                    receiver_address: result.receiver_address, //收件人详细地址，字符串格式
                    receiver_city_number: result.receiver_city, //市ID，number类型
                    receiver_district_number: result.receiver_district, //区ID，number类型
                    receiver_mobile: result.receiver_mobile, //收件人手机，字符串格式
                    receiver_name: result.receiver_name, //收件人姓名，字符串格式
                    receiver_phone: "", //收件人电话，字符串格式
                    receiver_state_number: result.receiver_state, //省ID，number类型
                    receiver_street: "",
                  };
                  let name = getItem("name");
                  name.prefix = "";
                }
              },
            },
            // {
            //   formType: "elInput",
            //   label: "客户手机",
            //   icon: "search",
            //   prop: "client_mobile",
            //   maxlength: 11,
            //   disabled: this.params.client_number !== undefined,
            //   value: clientInfo.client_mobile,
            //   span: 12,
            //   event: {
            //     focus(v, col, formData, getItem) {
            //       popup.customer(
            //           self,
            //           (result) => {
            //             console.log(result)
            //             self.setClientStatus(getItem, result, true);
            //             self.address_id = result.address_id;
            //             self.cust_id = result.cust_id;
            //             self.if_edit = result.if_edit;
            //             // 客户初始地址信息
            //             self.prevAddressInfo = {
            //               deliver_method: result.deliver_method, //发货方式，字符串格式
            //               post_fee_type: result.post_fee_type, //运费类型	，字符串格式
            //               receiver_address: result.receiver_address, //收件人详细地址，字符串格式
            //               receiver_city_number: result.receiver_city, //市ID，number类型
            //               receiver_district_number: result.receiver_district, //区ID，number类型
            //               receiver_mobile: result.receiver_mobile, //收件人手机，字符串格式
            //               receiver_name: result.receiver_name, //收件人姓名，字符串格式
            //               receiver_phone: "", //收件人电话，字符串格式
            //               receiver_state_number: result.receiver_state, //省ID，number类型
            //               receiver_street: "",
            //             };
            //             let name = getItem("name");
            //             name.prefix = "";
            //           },
            //           {
            //             initParam: { client_mobile: '' },
            //           }
            //       );
            //     },
            //     onIconClick(v, col, formData, getItem) {
            //       popup.customer(
            //           self,
            //           (result) => {
            //             self.setClientStatus(getItem, result, true);
            //             self.address_id = result.address_id;
            //             self.cust_id = result.cust_id;
            //             self.if_edit = result.if_edit;
            //             // 客户初始地址信息
            //             self.prevAddressInfo = {
            //               deliver_method: result.deliver_method, //发货方式，字符串格式
            //               post_fee_type: result.post_fee_type, //运费类型	，字符串格式
            //               receiver_address: result.receiver_address, //收件人详细地址，字符串格式
            //               receiver_city_number: result.receiver_city, //市ID，number类型
            //               receiver_district_number: result.receiver_district, //区ID，number类型
            //               receiver_mobile: result.receiver_mobile, //收件人手机，字符串格式
            //               receiver_name: result.receiver_name, //收件人姓名，字符串格式
            //               receiver_phone: "", //收件人电话，字符串格式
            //               receiver_state_number: result.receiver_state, //省ID，number类型
            //               receiver_street: "",
            //             };
            //             let name = getItem("name");
            //             name.prefix = "";
            //           },
            //           {
            //             initParam: { client_mobile: v },
            //           }
            //         );
            //     },
            //   },
            // },
            {
              formType: "elInput",
              maxlength: 40,
              label: "客户姓名",
              prefix: this.params.client_number ? "" : "(DZ)",
              value: clientInfo.client_name,
              prop: "name",
              disabled: this.params.client_number !== undefined,
              span: 12,
            },
            //新增客户意向金 2021.2.22

          ],
        },
        {
          title: "收件信息",
          cols: [
            {
              formType: "elInput",
              label: "收件人",
              prop: "receiver_name",
              maxlength: 50,
              value: clientInfo.receiver_name,
              disabled: false,
              span: 12,
            },
            // {formType: 'elInput', label: '收件人手机', prop:'receiver_mobile', maxlength: 11, disabled: false, span: 12},
            {
              formType: "elInput",
              label: "其他电话",
              prop: "receiver_phone",
              value: clientInfo.receiver_phone,
              disabled: false,
              span: 12,
            },
            // {
            //   formType: "elSelect",
            //   label: "送货方式",
            //   prop: "deliver_method",
            //   value: clientInfo.supply_transport_type,
            //   disabled: false,
            //   span: 12,
            //   options: this.deliver_method_options,
            // },
            {
              formType: "elInput",
              label: "送货方式",
              disabled: true,
              prop: "deliver_method",
              value: clientInfo.supply_transport_type_cn || this.deliverMethod.value,
              span: 12
            },
            {
              formType: "selectAddress",
              label: "省市区",
              prop: "address",
              value: [
                clientInfo.receiver_state,
                clientInfo.receiver_city,
                clientInfo.receiver_district,
              ].filter((item) => item),
              disabled: false,
              span: 12,
            },
            {
              formType: "elInput",
              label: "详细地址",
              type: "textarea",
              value: clientInfo.address_name,
              maxlength: 200,
              disabled: false,
              prop: "receiver_address",
              span: 13,
            },
          ],
        },
        {
          title: "订单信息",
          cols: [
            {
              formType: "elInput",
              label: "订单号",
              value: clientInfo.client_number,
              disabled: true,
              span: 12,
            },
            {
              formType: "elInput",
              label: "设计师",
              value: clientInfo.designer_name,
              disabled: true,
              span: 12,
            },
            {
              formType: "elSelect",
              label: "订单类型",
              prop: "client_type",
              value: clientInfo.client_type,
              span: 12,
              options: cusTypeopt,
            },
            {
              formType: "elDatePicker",
              label: "希望量尺时间",
              prop: "reserve_measure_date",
              value: clientInfo.reserve_measure_date,
              type: "datetime",
              format: "yyyy-MM-dd hh:mm:ss",
              span: 12,
            },
            {
              formType: "elSelect",
              label: "是否到店",
              prop: "if_arrived_shop",
              value: clientInfo.if_arrived_shop,
              span: 12,
              options: this.yesornoOpt,
               event: {
                change(v, col, formData, getItem) {
                  self.if_arrived_shop = v;
                },
              }
            },
             {
              formType: "elDatePicker",
              label: "到店日期",
              prop: "arrived_shop_date",
              value: clientInfo.arrived_shop_date,
              type: "datetime",
              format: "yyyy-MM-dd hh:mm:ss",
              span: 12,
            },
            {
              formType: "elSelect",
              label: "客户意向",
              prop: "intention",
              value: clientInfo.intention,
              span: 12,
              options: this.intentionTypeopt,

            },
            {
              formType: "elSelect",
              label: "客户预算",
              prop: "budget",
              value: clientInfo.budget,
              span: 12,
              options: custom_sys_budget,
            },
            {
              formType: "elSelect",
              label: "订单标签",
              prop: "order_tag",
              value: clientInfo.order_tag,
              span: 24,
              options: this.orderTagOptions,
              disabled: this.orderTagOptions.length<=1||clientInfo.cust_id
            },

            {
              formType: "elInput",
              label: "备注",
              prop: "remark",
              type: "textarea",
              maxlength: 500,
              value: clientInfo.remark,
              span: 13,
            },
          ],
        },
        {
          title: "店铺信息",
          cols: [

            this.params.isDisplay?
            {
              // formType: "myText",
              formType: "elSelect",
              prop:'shop_code',
              label: "订单店铺",
              options: shopOption,
              value: clientInfo.shop_code,
              span: 24,
              event: {
                change(v, col, formData, getItem) {
                  console.log(getItem,v)
                  const shopName=col.options.find(item=>item.value==v)?.label||''
                  self.initOrderTag({shopCode:v,shopName},getItem);
                  self.shopInfoInit({shop_code:v},getItem)
                  self.shop_code = v;
                },
              },
            }:{
              formType: "elInput",
              label: "客户店铺",
              icon: "search",
              prop: "shop_code",
              disabled: self.shopInfo.shopCode != 'LS-JXBC',
              value: clientInfo.shop_name,
              readonly:true,
              span: 12,
              event: {
                focus(v, col, formData, getItem) {
                },
                onIconClick(v, col, formData, getItem) {
                  // let self = this
                  if(self.shopInfo.shopCode != 'LS-JXBC'){
                    return;
                  }
                  self.$root.eventHandle.$emit('alert', {
                      component:()=>import('@components/shop/list'),
                      style:'width:800px;height:500px',
                      title:'选择店铺',
                      params: {
                        selection: 'radio',
                        setWhere: (queryData) => {
                          console.log(queryData);
                            var newWhere = (queryData.where || []).filter((o) => {
                                return (
                                    o.field !== self.setShopDefault.where[0].field &&
                                    o.field !== self.setShopDefault.where[1].field
                                );
                            });


                            newWhere = newWhere.concat(self.setShopDefault.where);

                            queryData.where = newWhere;
                        },
                          callback (data) {
                            let shopName = getItem('shop_code');
                            // let shop_code = getItem('shop_code');

                            shopName.value = data.shop_name;
                            self.shop_code = data.shop_code;
                            // formData.shop_name = data.shop_name;
                            // clientInfo.shop_code = data.shop_code;

                          }
                      }
                  })
                },
              },
            }
          ]
        },
      ];
    },
    setClientStatus(getItem, reslut, status, cols) {
      const [
        client_mobile,
        number,
        name,
        receiver_phone,
        receiver_name,
        // deliver_method,
        address,
        receiver_address,
      ] = getItem([
        "client_mobile",
        "number",
        "name",
        "receiver_phone",
        "receiver_name",
        // "deliver_method",
        "address",
        "receiver_address",
      ]);
      if (reslut) {
        client_mobile.value = reslut.receiver_mobile;
        number.value = reslut.number;
        name.value = reslut.name;
        receiver_phone.value = reslut.receiver_phone;
        // let deliver_method_abled = this.deliver_method_options.filter(
        //   (item) => !item.disabeld
        // );
        // let deliver_method_default = deliver_method_abled[0] || {};
        // deliver_method.value = this.params.client_number
        //   ? reslut.deliver_method
        //   : deliver_method_abled.findIndex(
        //       (item) => item.value === reslut.deliver_method
        //     ) !== -1
        //   ? reslut.deliver_method
        //   : deliver_method_default.value;
        receiver_address.value = reslut.receiver_address;
        address.value = [
          reslut.receiver_state,
          reslut.receiver_city,
          reslut.receiver_district,
        ];
        receiver_name.value = reslut.receiver_name;
      }
      client_mobile.disabled = status;
      // number.disabled = status
      name.disabled = status;
    },
    isNull(v) {
      return !v && v !== 0;
    },
    is_edit(data) {
      // 判断已有客户的地址是否编辑过
      let is_edit = false;
      if (!this.address_id) return true;
      for (var key in this.prevAddressInfo) {
        if (
          !this.isNull(this.prevAddressInfo[key]) ||
          !this.isNull(data[key])
        ) {
          is_edit = this.prevAddressInfo[key] != data[key];
          if (is_edit) {
            return is_edit;
          }
        }
      }
      return is_edit;
    },
    close() {
      let self = this;
      self.$root.eventHandle.$emit("removeTab", self.params.tabName);
    },
    saveComponent() {
      this.submit("customer");
    },

    //保存数据
    async submit(data) {
      const deliver_method = this.params.client_number ? this.clientInfo.supply_transport_type : this.deliverMethod.key
      let params = {
        o2oBdReceiverInfo: {
          deliver_method: deliver_method, //发货方式，字符串格式
          post_fee_type: "NOW_PAY", //运费类型	，字符串格式
          receiver_address: data.receiver_address, //收件人详细地址，字符串格式
          receiver_city_number: data.address[1], //市ID，number类型
          receiver_district_number: data.address[2], //区ID，number类型
          receiver_mobile: data.client_mobile, //收件人手机，字符串格式
          receiver_name: data.receiver_name, //收件人姓名，字符串格式
          receiver_phone: data.receiver_phone, //收件人电话，字符串格式

          receiver_state_number: data.address[0], //省ID，number类型
          receiver_street: "", //区ID，number类型
        },
        shop_code:this.shop_code

      };
      this.customerBtnStatu = true;
      if (this.address_id) {
        // 根据地址ID判断地址信息是否能修改
        const isUpdateContent = await judgeAddress(
          this.address_id,
          false,
          false
        );
        params.o2oBdReceiverInfo.fid =
          isUpdateContent.content === "true" ? this.address_id : "";
      } else {
        params.o2oBdReceiverInfo.fid = "";
      }
      if (this.params.client_number) {
        // 更新
        Object.assign(
          params,
          {
            custom_trade_id: this.clientInfo.custom_trade_id,
            client_no: data.number,
            client_type: data.client_type,
            arrived_shop_date: new Date(data.arrived_shop_date).getTime(),
            intention: data.intention,
            budget: data.budget,
            if_arrived_shop: data.if_arrived_shop,
            reserve_measure_date: data.reserve_measure_date,
            remark: data.remark,
            order_tag:data.order_tag,
          },
          {
            customerVO: {
              cust_id: this.clientInfo.cust_id,
              name: this.clientInfo.client_name,
            },
          }
        );
        editOrder(params, false, true).then((body) => {
          this.customerBtnStatu = false;
          if (body.result) {
            this.$root.eventHandle.$emit("refreshclientList");
            this.$root.eventHandle.$emit("removeTab", this.params.tabName);
          }
        });
      } else {
        // 新增
        if (this.address_id) {
          // 已有客户
          Object.assign(
            params,
            {
              client_no: data.number,
              client_type: data.client_type,
              arrived_shop_date: new Date(data.arrived_shop_date).getTime(),
              intention: data.intention,
              budget: data.budget,
              if_arrived_shop: data.if_arrived_shop,
              reserve_measure_date: data.reserve_measure_date,
              remark: data.remark,
              order_tag: data.order_tag,
            },
            {
              customerVO: {
                cust_id: this.cust_id,
                name: "(DZ)" + data.name,
              },
            }
          );
          addOrder(params, false, true).then((body) => {
            this.customerBtnStatu = false;
            if (body.result) {
              this.$root.eventHandle.$emit("refreshclientList");
              this.$root.eventHandle.$emit("removeTab", this.params.tabName);
            }
          });
        } else {
          Object.assign(params, {
            client_type: data.client_type,
            arrived_shop_date: new Date(data.arrived_shop_date).getTime(),
            budget: data.budget,
            intention: data.intention,
            if_arrived_shop: data.if_arrived_shop,
            reserve_measure_date: data.reserve_measure_date,
            remark: data.remark,
            customerVO: {
              name: "(DZ)" + data.name,
            },
            order_tag: data.order_tag,
          });
          // 校验新增客户名是否重复
          validateReceiveInfoDeliverMethod({
            client_name: params.customerVO.name,
          }).then((body) => {
            if (body.content) {
              addOrder(params, false, true).then((body) => {
                this.customerBtnStatu = false;
                if (body.result) {
                  this.$root.eventHandle.$emit("refreshclientList");
                  this.$root.eventHandle.$emit(
                    "removeTab",
                    this.params.tabName
                  );
                }
              });
            } else {
              this.$message.error("客户名已存在");
              this.customerBtnStatu = false;
            }
          });
        }
      }
    },
  },
};
</script>
