<!-- 新增、编辑收费记录 -->
<template>
	<div>
		<form-create 
      ref="formCreate"
      :formData="queryItems" 
      :btns="showBtns"
      :initParam="initParam"
      :ruleData="ruleData"
      requestUrl="/custom-web/api/customPayment/savePaymentInfo"
      @request="request"
      ></form-create> 
	</div>
</template>
<script>   
import {getClientByLogin, syncTradeAndPaymentToV2, createId } from './common/api'
import {funds_type,pay_type, getMap} from './common/tollDictionary'
import formCreate from './components/formCreate/formCreate'
import closeComponent from './common/mixins/closeComponent'
  export default {
    components: { formCreate },
    mixins: [closeComponent],
    props:['params'],
    data(){
      return {
        customerBtnStatu: false,
        queryItems: [],
        initParam:{}
      }
    },
    computed:{
      showBtns() {
        return !this.params || !this.params.row
      },
      ruleData(){
        return this.params.row ? {} : {
          // uploadFile: {required: true, message: '请上传文件', trigger: 'change'},
          client_number: {required: true, pmessage: '请选择订单', trigger: 'change'},
          customized_pay: {required: true, message: '请选择款项类型', trigger: 'change'},
          pay_type: {required: true, message: '请选择支付方式', trigger: 'change'},
          pay_date: {required: true, message: '请选择支付日期', trigger: 'change'},
          pay_amount: {
            validate: "isPrice"
          }
        }
      }
    },
    async  created() {
      this.payment_id = await createId()
      this.initParam.payment_id = this.payment_id
      this.getQueryItems()
      // this.getClentList()
    },
    methods:{
      saveComponent() {
        this.$refs.formCreate.save()
      },
      async request(data){
        this.$root.eventHandle.$emit('removeTab',this.params.tabName)
        // await syncTradeAndPaymentToV2(data.content)
        this.$root.eventHandle.$emit('refreshTollList')
      },
      getClentList() {
        if(!this.params.row) {
          // 添加
          getClientByLogin().then(data => {
            this.queryItems.forEach(item => {
              item.cols.forEach(col => {
                if(col.prop === 'client_number') {
                  col.options = data.map(item => {
                    item.label = `${item.client_name}(${item.client_mobile})` 
                    item.value = item.client_number
                    return item
                  })
                }
              })
            })
          })
        }
      },
      getLabel(val, options){
        let i = options.findIndex(item => item.value == val)
        return i!==-1 ? options[i].label : ''
      },
      getQueryItems() {
        let params = this.params || {}
        let info = params.info || {}
        this.queryItems = [
          {
              cols: [
                  {formType: 'myText', label: '专卖店', value: info.loginShopName, span: 24},
                  {formType: 'listSelect', label: '订单号', prop: 'client_number', config: {popupType: 'order',shop_code:info.shopCode} , span: 8, options:[], event: {
                    result: (item, col, formData, getItem) => {
                      Object.assign(this.initParam, {
                        client_number:item.client_number,
                        client_name: item.client_name,
                        client_mobile: item.client_mobile,
                        shop_code: info.shopCode
                      })
                      let uploadFile = getItem('uploadFile')
                      uploadFile.config = Object.assign({}, uploadFile.config, {client_number: item.client_number})
                    }
                  }},
                  {formType: 'elInput', label: '支付金额', prop:'pay_amount',type:'number', span: 12, suffix: '元', tips: '请核对所有商品的销售价格'},
                  {formType: 'elSelect', label: '款项类别', prop: 'customized_pay', span: 8, options:funds_type},
                  {formType: 'elSelect', label: '支付方式', prop: 'pay_type', span: 8, options:pay_type},
                  {formType: 'elDatePicker', type:"date", prop: 'pay_date', span: 12, label: '支付日期', format: 'yyyy-MM-dd hh:mm:ss'},
                  {formType: 'uploadFile', prop: 'uploadFile', value: '',  label: '支付凭证', span: 24,
                    config: {parent_no:this.payment_id,useBy:"design",look:true}
                  }
                  
              ]
          },
          {
            cols: [
              {formType: 'elInput', type:"textarea", prop: 'remark', span:12, label: '备注', maxlength:150}
            ]
          }
        ]
      }
    }
  }
</script>
