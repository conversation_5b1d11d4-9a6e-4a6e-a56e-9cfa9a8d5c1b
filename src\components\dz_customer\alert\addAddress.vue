<template>
  <!-- 客户管理-添加客户 -->
  <div class="mgt10">
    <el-form
      label-position="right"
      label-width="95px"
      :model="submitData"
      :rules="rules"
      ref="submitData"
    >
      <el-row :gutter="40" class="mgt10">
        <el-col :span="8">
          <el-form-item label="收货人" prop="receiver_name">
            <el-input :maxlength="50" size="mini" v-model="submitData.receiver_name"></el-input>
            <el-tooltip
              v-if="rules.receiver_name[0].isShow"
              class="item"
              effect="dark"
              :content="rules.receiver_name[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货人固话" prop="receiver_phone">
            <el-input size="mini" v-model="submitData.receiver_phone"></el-input>
            <el-tooltip
              v-if="rules.receiver_phone[0].isShow"
              class="item"
              effect="dark"
              :content="rules.receiver_phone[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货人手机" prop="receiver_mobile">
            <el-input size="mini" v-model="submitData.receiver_mobile" :maxlength="11"></el-input>
            <el-tooltip
              v-if="rules.receiver_mobile[0].isShow"
              class="item"
              effect="dark"
              :content="rules.receiver_mobile[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40" class="mgt10">
        <el-col :span="8">
          <el-form-item label="省" prop="receiver_state_code">
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="submitData.receiver_state_code"
              @change="selectProvince"
            >
              <el-option
                v-for="(value,key) in provinceObj"
                :label="value"
                :value="parseInt(key)"
                :key="key"
              ></el-option>
            </el-select>
            <el-tooltip
              v-if="rules.receiver_state_code[0].isShow"
              class="item"
              effect="dark"
              :content="rules.receiver_state_code[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="市" prop="receiver_city_code">
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="submitData.receiver_city_code"
              @change="selectCity"
            >
              <el-option
                v-for="(value,key) in cityObj"
                :label="value"
                :value="parseInt(key)"
                :key="key"
              ></el-option>
            </el-select>
            <el-tooltip
              v-if="rules.receiver_city_code[0].isShow"
              class="item"
              effect="dark"
              :content="rules.receiver_city_code[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="区" prop="receiver_district_code">
            <el-select
              placeholder="请选择"
              size="mini"
              v-model="submitData.receiver_district_code"
              @change="selectArea"
            >
              <el-option
                v-for="(value,key) in areaObj"
                :label="value"
                :value="parseInt(key)"
                :key="key"
              ></el-option>
            </el-select>
            <el-tooltip
              v-if="rules.receiver_district_code[0].isShow"
              class="item"
              effect="dark"
              :content="rules.receiver_district_code[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40" class="mgt10">
        <el-col :span="8">
          <el-form-item label="街道">
            <el-select placeholder="请选择" size="mini" v-model="submitData.receiver_street">
              <el-option
                v-for="(value,key) in streetObj"
                :label="value"
                :value="parseInt(key)"
                :key="key"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="详细地址" prop="receiver_address">
            <el-input :maxlength="200" size="mini" v-model="submitData.receiver_address"></el-input>
            <el-tooltip
              v-if="rules.receiver_address[0].isShow"
              class="item"
              effect="dark"
              :content="rules.receiver_address[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40" class="mgt10">
        <el-col :span="8">
          <el-form-item label="送货方式" prop="deliver_method">
            <!-- <xpt-select-aux v-model="submitData.deliver_method" aux_name='deliver_method' ></xpt-select-aux> -->

            <el-select placeholder="请选择" size="mini" v-model="submitData.deliver_method">
              <el-option
                v-for="(item,index) in deliver_method_Options"
                :label="item.label"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
            <el-tooltip
              v-if="rules.deliver_method[0].isShow"
              class="item"
              effect="dark"
              :content="rules.deliver_method[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运费类型" prop="post_fee_type">
            <el-select placeholder="请选择" size="mini" v-model="submitData.post_fee_type">
              <el-option
                v-for="(item,index) in post_fee_type_Options"
                :label="item.label"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
            <el-tooltip
              v-if="rules.post_fee_type[0].isShow"
              class="item"
              effect="dark"
              :content="rules.post_fee_type[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40" class="mgt10">
        <el-col :span="10">
          <el-form-item label="楼盘名称:" prop="loft_name">
            <el-input :maxlength="100" v-model="submitData.loft_name" size="mini"></el-input>
            <el-tooltip
              v-if="rules.loft_name[0].isShow"
              class="item"
              effect="dark"
              :content="rules.loft_name[0].message"
              placement="right-start"
              popper-class="xpt-form__error"
            >
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="楼盘坐标:" prop="loft_coordinate">
            <el-input :maxlength="100" v-model="submitData.loft_coordinate" size="mini"></el-input>
            <el-button type="text" @click="popMapChoose">地图选点</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40" class="mgt10">
        <el-col :span="24" style="text-align:center;">
          <el-button type="primary" size="small" @click="preSave('submitData')">保存</el-button>
          <el-button type="danger" size="small" @click="closeWindow">取消</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import validate from "../common/validate";
import { deliver_method, post_fee_type } from "../common/clientDictionary";
export default {
  props: ["params"],
  data() {
    var self = this;
    return {
      provinceObj: {},
      cityObj: {},
      areaObj: {},
      streetObj: {},
      submitData: {
        receiver_name: "",
        receiver_phone: "",
        receiver_mobile: "",
        receiver_state_code: "",
        receiver_city_code: "",
        receiver_district_code: "",
        receiver_street: "",
        receiver_address: "",
        deliver_method: "",
        post_fee_type: "NOW_PAY",
        is_selected: "N"
      },
      rules: {
        loft_name: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请输入楼盘名称",
          trigger: "change"
        }),
        receiver_name: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请输入收货人姓名",
          trigger: "change"
        }),
        deliver_method: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请选择送货方式",
          trigger: "change"
        }),
        post_fee_type: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请选择运费类型",
          trigger: "change"
        }),
        receiver_mobile: validate.mobile({
          self: self,
          required: true
        }),
        receiver_state_code: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请选择所在省",
          trigger: "change"
        }),
        receiver_city_code: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请选择所在市",
          trigger: "change"
        }),
        receiver_district_code: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请选择所在区",
          trigger: "change"
        }),
        receiver_address: validate.isNotBlank({
          required: true,
          self: self,
          msg: "请输入详细地址",
          trigger: "change"
        }),
        receiver_phone: validate.otherTel({
					self: self,
          msg: "请填写有效的电话号",
          trigger: "blur"
        })
      },
      deliver_method_Options: deliver_method,
      post_fee_type_Options: post_fee_type,
      addressNums: {
        stateNum: "",
        cityNum: "",
        districtNum: "",
        streetNum: ""
      }
    };
  },
  methods: {
    popMapChoose() {
      this.$root.eventHandle.$emit("popMapChoose");
    },
    closeWindow() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    // 地区
    getAreaCode(code, key) {
      var self = this;
      var url = "/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId";
      if (!code) return;
      self.ajax.postStream(url, code, function(response) {
        if (response.body.result) {
          self[key] = response.body.content;
        }
      });
    },
    selectProvince(address) {
      //选择省份，获得市级信息
      var self = this;
      if (!address) return;
      /**
       * 非复制状态
       * or
       * 复制状态重新选择
       */
      if (
        !self.params.ifCopy ||
        (typeof address !== "string" &&
          self.params.addressObj.receiver_state_code !== address)
      ) {
        self.getAreaCode(self.submitData.receiver_state_code, "cityObj");
        self.submitData.receiver_city_code = "";
        self.submitData.receiver_district_code = "";
        self.submitData.receiver_street = "";
      }
    },
    selectCity(address) {
      //选择市级，获得区级信息
      if (!address) return;
      var self = this;
      if (
        !self.params.ifCopy ||
        (typeof address !== "string" &&
          self.params.addressObj.receiver_city_code !== address)
      ) {
        self.getAreaCode(self.submitData.receiver_city_code, "areaObj");
        self.submitData.receiver_district_code = "";
        self.submitData.receiver_street = "";
      }
    },
    selectArea(address) {
      //选择区级，获得街道信息
      if (!address) return;
      var self = this;
      if (
        !self.params.ifCopy ||
        (typeof address !== "string" &&
          self.params.addressObj.receiver_district_code !== address)
      ) {
        self.getAreaCode(self.submitData.receiver_district_code, "streetObj");
        self.submitData.receiver_street = "";
      }
    },
    /*
		添加地址做保存时校验不允许收货人存在先生小姐等字符。
		*/
    preSave(formName) {
      var self = this;
      self.$refs[formName].validate(valid => {
        if (!valid) return;
        let receiver_name = this.submitData.receiver_name;
        if (
          receiver_name.indexOf("先生") > -1 ||
          receiver_name.indexOf("小姐") > -1
        ) {
          this.$message.error("收货人不允许包含“先生”、“小姐”字符");
          return;
        }
        self.save();
      });
    },
    save() {
      if (this.request) {
        this.$message({
          type: "warning",
          message: "请等待上一次请求结束"
        });
        return;
      }
      this.request = true;
      this.ajax.postStream(
        "/order-web/api/customer/receiverInfo/validateReceiveInfoDeliverMethod",
        {
          deliver_method: this.submitData.deliver_method,
          receiver_state: this.submitData.receiver_state_code,
          receiver_city: this.submitData.receiver_city_code,
				receiver_street: this.submitData.receiver_street,
          receiver_district: this.submitData.receiver_district_code
        },
        res => {
          if (res.body.result) {
            var self = this;
            let deliver_method_i = self.deliver_method_Options.findIndex(
              item => item.value == self.submitData.deliver_method
            );
            self.submitData.deliver_method_cn =
              deliver_method_i !== -1
                ? this.deliver_method_Options[deliver_method_i].label
                : self.submitData.deliver_method;
            let post_fee_type_i = self.post_fee_type_Options.findIndex(
              item => item.value == self.submitData.post_fee_type
            );
            self.submitData.post_fee_type_cn =
              post_fee_type_i !== -1
                ? this.post_fee_type_Options[post_fee_type_i].label
                : self.submitData.post_fee_type;
            self.submitData.receiver_state_name =
              self.provinceObj[self.submitData.receiver_state_code] ||
              self.submitData.receiver_state_code;
            self.submitData.receiver_city_name =
              self.cityObj[self.submitData.receiver_city_code] ||
              self.submitData.receiver_city_code;
            self.submitData.receiver_district_name =
              self.areaObj[self.submitData.receiver_district_code] ||
              self.submitData.receiver_district_code;
            self.submitData.receiver_street_name =
              self.streetObj[self.submitData.receiver_street] ||
              self.submitData.receiver_street;
            self.submitData.prin_city_district =
              self.submitData.receiver_state_name +
              self.submitData.receiver_city_name +
              self.submitData.receiver_district_name;
            if (self.params.ifCallback) {
              //客户模块地址新增，复制新增
              self.params.callback(self.submitData);
              self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
            }
          } else {
            this.request = false;
            this.$message.error(res.body.msg || "");
          }
        },
        err => {
          this.request = false;
          this.$message.error(err);
        }
      );
    }
  },
  // 校验送货方式是否匹配
  mounted: function() {
    var self = this;
    if (self.params.ifCopy) {
      //判断是否复制新增
      var url = "/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId";
      self.submitData.receiver_name = self.params.addressObj.receiver_name;
      self.submitData.receiver_phone = self.params.addressObj.receiver_phone;
      self.submitData.receiver_mobile = self.params.addressObj.receiver_mobile;
      self.submitData.receiver_address =
        self.params.addressObj.receiver_address;
      self.submitData.post_fee_type = self.params.addressObj.post_fee_type;
      self.submitData.deliver_method = self.params.addressObj.deliver_method;

      new Promise((resolve, reject) => {
        //请求省份信息，赋值省份编码
        self.ajax.postStream(url, 1, function(response) {
          if (response.body.result) {
            self.provinceObj = response.body.content;
            resolve(response);
          } else reject(response.body.msg);
        });
      })
        .then(v => {
          self.submitData.receiver_state_code =
            self.params.addressObj.receiver_state_code;
        })
        .catch(error => {
          self.$message.error(error);
        });

      if (!self.params.addressObj.receiver_state_code) return;
      new Promise((resolve, reject) => {
        //根据省份信息获得市级信息，赋值市级编码
        self.ajax.postStream(
          url,
          self.params.addressObj.receiver_state_code,
          function(response) {
            if (response.body.result) {
              self.cityObj = response.body.content;
              resolve(response);
            } else reject(response.body.msg);
          }
        );
      })
        .then(v => {
          self.submitData.receiver_city_code =
            self.params.addressObj.receiver_city_code;
        })
        .catch(error => {
          self.$message.error(error);
        });

      if (!self.params.addressObj.receiver_city_code) return;
      new Promise((resolve, reject) => {
        //根据市级信息获得区级信息，赋值区级编码
        self.ajax.postStream(
          url,
          self.params.addressObj.receiver_city_code,
          function(response) {
            if (response.body.result) {
              self.areaObj = response.body.content;
              resolve(response);
            } else reject(response.body.msg);
          }
        );
      })
        .then(v => {
          self.submitData.receiver_district_code =
            self.params.addressObj.receiver_district_code;
        })
        .catch(error => {
          self.$message.error(error);
        });

      if (!self.params.addressObj.receiver_district_code) return;
      new Promise((resolve, reject) => {
        //根据区级信息获得街道信息，赋值街道编码
        self.ajax.postStream(
          url,
          self.params.addressObj.receiver_district_code,
          function(response) {
            if (response.body.result) {
              self.streetObj = response.body.content;
              resolve(response);
            } else reject(response.body.msg);
          }
        );
      })
        .then(v => {
          self.submitData.receiver_street =
            self.params.addressObj.receiver_street;
        })
        .catch(error => {
          self.$message.error(error);
        });
    } else {
      self.getAreaCode(1, "provinceObj"); //新增
    }
  },
  destroyed() {}
};
</script>
