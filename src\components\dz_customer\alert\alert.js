
/***
添加设计登记弹窗 addDesign
添加查房弹窗 addCheck
添加沟通登记弹窗 addConnect
添加量尺标签弹窗 addDesignTag
添加设计师自评弹窗 addDesignAssess
添加合同登记弹窗 addContract
图片管理 imageAdmin
图片查看 imageView
列表弹窗 list
***/
import {saveViewRegister, getDesignInfo, getShopSort} from '../common/api'
function setValue(formData, rowData) {
    formData.forEach(row => {
        if(Array.isArray(row.cols)) {
            row.cols.forEach(col => {
                rowData[col.prop] !== undefined && (col.value = rowData[col.prop])
            })
        }
    })
}
// 图片查看
export const imageView = (self, initData = {}) => {
    self.$root.eventHandle.$emit('alert',{
        params:initData,
        component:()=>import('./imageView'),
        style:`width:${innerWidth*0.8}px;height:${innerHeight*0.92}px`,
        title:'附件查看'
    });
}
// 图片查看
export const imageView2 = (self, initData = {}) => {
    self.$root.eventHandle.$emit('alert',{
        params:initData,
        component:()=>import('./imageView2'),
        style:`width:${innerWidth*0.8}px;height:${innerHeight*0.92}px`,
        title:'附件查看'
    });
}
// 图片管理
export const imageAdmin = (self, no = {}, callback,remark='') => {
    self.$root.eventHandle.$emit('alert',{
        params:{no, callback, height: innerHeight*0.8 - 120 + 'px',remark},
        component:()=>import('./imageAdmin'),
        style:`width:${innerWidth*0.8}px;height:${(innerHeight*0.8+20)}px`,
        title:'附件管理'
    }); 
}

// 添加查房弹窗
export const addCheck = async (self, client_number) => {
    let isRequest = false
    let designInfo = await getDesignInfo( {client_number: client_number})
    let room = designInfo.designRoomList || []
    let ruleData = {}
    room.forEach((item, index) => {
        ruleData['view_evaluation' + index] = {required: true, message: '请输入查房评价'}
    })
    let autoHeight = room.length*110+150
    autoHeight > 600 && (autoHeight = 600)
    return new Promise((resolve, reject) => {
        let params = {
            height: `${autoHeight-30}px`,
            ruleData,
            formData: room.map((item, index) => {
                return {
                    title: item.design_room_name,
                    cols: [
                        {formType: 'elRate', label: '查房评价',  prop: 'view_evaluation' + index, span: 24},
                    ] 
                }
            }).concat([
                {
                    cols: [
                        {formType: 'elInput', type: 'textarea', maxlength: 500,label: '备注',   prop: 'remark', span: 24}
                    ]
                }
            ]),
            labelWidth: '90px',
            event: {
                save(datas, close) {
                    let arr=['不可面世', '还需改善', '方案尚可', '方案不错', '无可挑剔']
                    let data = {
                        client_number,
                        design_evaluation: datas.design_evaluation,
                        remark: datas.remark,
                        view_evaluation: room.map((item, index) => {
                            let i=datas['view_evaluation' + index]
                            return `${item.design_room_name}：${arr[i-1]}`
                        }).join(';')
                    }
                    if(isRequest) {
                        $message.warning('请等待上一次请求结果')
                        return
                    }
                    isRequest = true
                    saveViewRegister(data, false, true).then(res => {
                        isRequest = false
                        if(res.result) {
                            // 添加成功
                            self.$root.eventHandle.$emit('saveViewRegister')
                            close()
                        }
                    })
                    // resolve(data)
                }
            }
        }
        self.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('./formCreate_alert'),
            style:`width:540px;height:${autoHeight}px`,
            title:'添加查房记录'
        });
    })
}
// 添加沟通登记弹窗
export const addConnect = (self, client={}, room) => {
    return new Promise((resolve, reject) => {
        let params = {
            client,
            room
        }
        self.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('./follow'),
            style:'width:1000px;height:600px',
            title:'添加沟通登记'
        });
    })
}
// 添加量尺标签弹窗
export const addDesignTag = (self) => {
    return new Promise((resolve, reject) => {
        let params = {
            formData: [
                {
                    cols: [
                        {formType: 'elSelect', label: '空间类型', prop: 'type', span: 10, options:[{label:'其他',value:'其他'}], value: '其他',disabled: true},
                        {formType: 'elInput', label: '空间名称',  prop: 'spacename', span: 14}
                    ]
                }
            ],
            labelWidth: '70px',
            event: {
                save(data) {
                    resolve(data)
                }
            }
        }
        self.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('./formCreate_alert'),
            style:'width:500px;height:120px',
            title:'添加量尺标签'
        });
    })
}

// 添加设计师自评弹窗
export const addDesignAssess = (self, initValue,resolve) => {
    self.$confirm(
        "是否确定提交？",
        "提示",
        {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "danger",
        }
    ).then(() => {
        
        self.ajax.postStream(
            "/custom-web/api/customDesignRegister/save",
            {client_number:initValue.client_number},
            (res) => {
                if (res.body.result) {
                    self.$message.success(res.body.msg || "操作成功！");
                    
                } else {
                    self.$message.error(res.body.msg || "提交失败！");
                }
                resolve && resolve(res.body.result)
            },
            (err) => {
                self.$message.error(err);
            }
        );
    }).catch(() => {
        resolve && resolve(false)
        return false;
    });
    // return new Promise((resolve, reject) => {
    //     let params = {
    //         initValue
    //     }
    //     self.$root.eventHandle.$emit('alert',{
    //         params:params,
    //         component:()=>import('./designAssess'),
    //         style:'width:200px;height:160px',
    //         title:'设计自评'
    //     });
    // })
}

// 添加合同登记弹窗
export const addContract = (self, initValue) => {
    return new Promise((resolve, reject) => {
        let params = {
            initValue,
            event: {
                save(data) {
                    resolve(data)
                }
            }
        }
        self.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('./compactEdit'),
            style:'width:700px;height:470px;',
            title:'添加合同登记'
        });
    })
}
// 添加合同登记弹窗
export const confirmContract = (self, initValue) => {
    return new Promise((resolve, reject) => {
        let params = {
            initValue,
            event: {
                save(data) {
                    resolve(data)
                }
            }
        }
        self.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('./confirmContract'),
            style:'width:700px;height:470px;',
            title:'合同变更'
        });
    })
}

export const tips = (self, tip ) => {
    const tipConfig = typeof tip === 'string' ? { tip } : tip
    return new Promise((resolve, reject) => {
        let params = {
            tip: tipConfig.tip || ''
        }
        tipConfig.tip ? self.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('./noPay'),
            style:'width:300px;height:200px',
            title:'提示'
        }) : self.$root.eventHandle.$emit('alert',{
            params:params,
            component: tipConfig.component,
            style:'width:300px;height:200px',
            title:'提示'
        })
    })
}

// 添加设计登记弹窗
export const addDesign = (self, initValue) => {
    return new Promise((resolve, reject) => {
        let params = {
            initValue,
            measureSpaceHeight: '300px',
            event: {
                save(data) {
                    resolve(data)
                }
            }
        }
        self.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('./designEdit'),
            style:'width:800px;height:600px',
            title:'添加设计登记'
        });
    })
}

//下推采购弹窗
export const addPush = (self, initValue) => {
        let params = {
            trade_type: initValue.trade_type,
            initValue
        }
        self.ajax.postStream('/custom-web/api/customGoods/getList', {client_number: initValue.client_number}, (res) => {
            if(!res.body.result) {
                self.$message.error(res.body.msg)
                return
            }
            let content = res.body.content || []
            if(!content.length) {
                self.$message.error('请先创建商品')
                return
            }
            let i = content.findIndex(item => !item.purchase_price && item.purchasePrice != 0)
            if(i === -1) {
                self.$root.eventHandle.$emit('alert',{
                    params:params,
                    component:()=>import('./pushAlert'),
                    style:'width:338px;height:150px',
                    title:'下推'
                });
            } else {
                self.$message({
                    type: 'error',
                    message: '请把所有的商品采购价格录完'
                })
            }
        })
        
}
//采购价
export const purchasePrice = (self, initValue) => {
    return new Promise((resolve, reject) => {
        let params = {
            initValue,
            
            event: {
                save(data) {
                    resolve(data)
                }
            }
        }
        self.$root.eventHandle.$emit('alert',{
            params:params,
            component:()=>import('./purchasePrice'),
            style:'width:900px;height:600px',
            title:'采购价格'
        });
    })
}
export const list = (self, title='列表选择',formCreate, colData, tableUrl, cb, config={}) => {
    let params = Object.assign({
        queryItems: formCreate.queryItems, //查询天剑
        colData, //表显示列
        tableUrl, //表接口
        cb //回调
    }, config)
    let w = innerWidth*0.8
    let h = innerHeight*0.92
    w > 800 && (w=800)
    h > 610 && (h=610)
    formCreate.labelWidth && (params.labelWidth = formCreate.labelWidth)
    config.w && (w = config.w)
    config.h && (h = config.h)
    self.$root.eventHandle.$emit('alert',{
        params:params,
        component:()=>import('./list'),
        style:`width:${w}px;height:${h}px`,
        title
    });
}

// 列表选择
export const popup = {
    order(self, cb, config={}) {
        let form = {
            queryItems: [
                {
                    cols: [
                        {formType: 'elInput', label: '客户名称',  prop: 'client_name', span: 8},
                        {formType: 'elInput', label: '手机', prop:'client_mobile', span: 8},
                        {formType: 'elInput', label: '订单号', prop:'client_number', span: 8}
                    ]
                }
            ]
        }
        let colData = [
            {
                label: '订单号',
                prop: 'client_number',
                width: 116,
            },
            {
                label: '客户名',
                prop: 'client_name',
                width: 116,
            },
            {
                label: '手机号',
                prop: 'client_mobile',
                width: 116,
            },
        ]
        let c={
            w: 1100,
            h:640
        }
        list(self, '选择订单', form, colData, '/custom-web/api/customPayment/querySysTradeListByRole', cb, Object.assign(c, config)) 
    },
    merge_order(self, cb, config={}) {
        let form = {
            queryItems: []
        }
        let colData = [
            {
                label: '销售单号',
                prop: 'sys_trade_no',
                width: 116,
            },
            {
                label: '合并单号',
                prop: 'merge_trade_no',
                width: 116,
            },
            {
                label: '支付时间',
                filter: 'date',
                formats: "yyyy-MM-dd",
                prop: 'pay_time'
            },
            {
                label: '买家昵称',
                prop: 'customer_name',
                width:140
            },
            {
                label: '订单店铺',
                prop: 'original_shop_name'
            }
        ]
        let c={
            h:660,
            page:false
        }
        list(self, '合并订单列表', form, colData, '/custom-web/api/customPayment/getOnlinePayment', cb, Object.assign(c, config)) 
    },
    relation_order(self, cb, config={}) { //关联订单
        let form = {
            queryItems: [
                {
                    cols: [
                        {formType: 'elInput', label: '客户名', prop:'client_name', span: 5},
                        {formType: 'elInput', label: '订单号', prop:'client_number', span: 5},
                        {formType: 'elInput', label: '设计师', prop:'designer_name', span: 5},
                        {formType: 'elDatePicker', prop: 'date', format: 'yyyy-MM-dd', props:['start_create_date', 'end_create_date'], label: '建档日期', type: 'daterange', span: 5},
                    ]
                }
            ]
        }
        let colData = [
            {
                label: '店名',
                prop: 'shop_name',
                width: 116,
            },
            {
                label: '订单号',
                prop: 'client_number',
                width: 116,
            },
            {
                label: '客户名',
                prop: 'client_name',
                width: 116,
            },
            {
                label: '客户号',
                prop: 'client_no',
                width: 116,
            },
            {
                label: '手机',
                prop: 'client_mobile',
                width: 116
            },
            {
                label: '建档日期',
                prop: 'create_time',
                filter: 'date',
                width: 130
            },
            {
                label: '建档人',
                prop: 'shopping_guide_name',
                width: 116
            },
            {
                label: '设计师',
                prop: 'designer_name'
            },
            {
                label: '量尺日期',
                prop: 'measure_date',
                filter: 'date',
                width: 130
            },
            {
                label: '设计日期',
                prop: 'design_date',
                filter: 'date',
                width: 130
            },
            {
                label: '合同日期',
                prop: 'compact_date',
                filter: 'date',
                formats: 'yy-MM-dd',
                width: 130
            } 
        ]
        let c = {w: 1140}
        list(self, '选择关联订单', form, colData, '/custom-web/api/customSupplyTrade/getSysTradeList', cb, Object.assign(c, config)) 
    },
    customer(self, cb, config={}) {
        let colData = [
            // {
            //     label: '店名',
            //     prop: 'shop_name',
            //     width: 133
            // },         
             {
                label: '客户名',
                prop: 'name',
                width: 103
            },
            {
                label: '手机',
                prop: 'receiver_mobile',
                width: 127
            },
            {
                label: '省',
                prop: 'receiver_state_name',
                width: 100
            },
            {
                label: '市',
                prop: 'receiver_city_name',
                width: 100
            },
            {
                label: '区',
                prop: 'receiver_district_name',
                width: 100
            },
            {
                label: '详细地址',
                prop: 'receiver_address',
                width: 400
            }
        ]
        const formValue = config.formValue || {}
        let form = {
            labelWidth: '66px',
            queryItems: [
                {
                    cols: [
                        {formType: 'elInput', label: '手机号', maxlength: 11, prop:'client_mobile', value: formValue.client_mobile,  span: 6},
                    ]
                }
            ]
        }
        let c = {w:940, isInit: false}
        list(self, '选择客户', form, colData, '/custom-web/api/customClient/getClientInfoList', cb, Object.assign(c, config)) 
    },
    shop(self, cb, config={}) { //店铺
        let sort = []
        getShopSort().then(list => {
            list.forEach(item => {
                sort.push({
                    label: item.name,
                    value: item.code
                })
            })
        })
        let colData = [
            {
                label: '店铺名称',
                prop: 'shop_name',
                width: 133
            },
            {
                label: '店铺编码',
                prop: 'shop_code',
                width: 133
            },
            {
                label: '店铺地址',
                prop: 'address'
            },
            {
                label: "创建时间",
                prop: "create_time",
                filter: 'date',
                formats: 'yyyy-MM-dd hh:mm:ss',
                width: "140",
            },
            {
                label: "修改时间",
                filter: 'date',
                formats: 'yyyy-MM-dd hh:mm:ss',
                prop: "modify_time",
                width: "140",
            },
        ]
        let form = {
            labelWidth: '66px',
            queryItems: [
                {
                    cols: [
                        {formType: 'elInput', label: '店铺名称', prop:'shop_name', span: 6},
                        {formType: 'elInput', label: '店铺编码',  prop: 'shop_code', span: 6},
                        {formType: 'elSelect', label: '店铺分类',  prop: 'shop_type', span: 6, options: sort}
                    ]
                }
            ]
        }
        let c = {
            w: 940
        }
        list(self, '选择店铺', form, colData, config.apiUrl || '/custom-web/api/guideReport/getShopList', cb, Object.assign(c, config)) 
        delete config.apiUrl
    },
    goods(self, cb, config={}) { //商品
        let colData = [{
            label: '商品名称',
            prop: 'message',
            width: '130'
          }, {
            label: '商品号',
            prop: 'goods_id',
            width: '130'
          }, {
            label: '风格',
            prop: 'style_cn',
            width: '90'
          },{
            label: '类目',
            prop: 'category_cn',
            width: '90'
          },{
            label: '材质',
            prop: 'material_cn',
            width: '90'
          },{
            label: '颜色',
            prop: 'color_cn',
            width: '90'
          },
          {
            label: '空间',
            prop: 'design_room_name',
            width: '90'
          },
          {
            label: '订单号',
            prop: 'client_number',
            width: '90'
          },
          {
            label: '建档人',
            prop: 'designer',
            width: '90'
          },{
            label: '建档日期',
            prop: 'create_time',
            filter: 'date',
            formats: 'yy-MM-dd',
            width: '124'
          },{
            label: '设计师',
            prop: 'designer',
            width: '90'
          },{
            label: '商品状态',
            prop: 'goods_status_cn',
            width: '90'
          }]
          let c = {
            w:940, 
            page: false
          }
        list(self, '选择商品', {}, colData, '/custom-web/api/customGoods/getList', cb, Object.assign(c, config)) 
    },
    getSupplyList(self, cb, config={}) { //商品
        let colData = [{
            label: '商品名称',
            prop: 'message',
            width: '180'
          }, {
            label: '商品号',
            prop: 'goods_id',
            width: '130'
          }, {
            label: '风格',
            prop: 'style_cn',
            width: '90'
          },{
            label: '类目',
            prop: 'category_cn',
            width: '90'
          },{
            label: '材质',
            prop: 'material_cn',
            width: '90'
          },{
            label: '颜色',
            prop: 'color_cn',
            width: '90'
          },
          {
            label: '空间',
            prop: 'design_room_name',
            width: '90'
          },
          {
            label: '订单号',
            prop: 'client_number',
            width: '90'
          },
          {
            label: '建档人',
            prop: 'designer',
            width: '90'
          },{
            label: '建档日期',
            prop: 'create_time',
            filter: 'date',
            formats: 'yy-MM-dd',
            width: '124'
          },{
            label: '设计师',
            prop: 'designer',
            width: '90'
          },{
            label: '商品状态',
            prop: 'goods_status_cn',
            width: '90'
          }]
          let c = {
            w:940, 
            page: false
          }
        list(self, '选择商品', {}, colData, '/custom-web/api/customGoods/getSupplyList', cb, Object.assign(c, config)) 
    },
    client_address(self, cb, config={}) { //选择客户地址
        let colData = [{
            label: '省市区',
            prop: 'prin_city_district',
            width: '153'
          },{
            label: '详细地址',
            prop: 'receiver_address',
            width: '153'
          },{
            label: '收件人',
            prop: 'receiver_name',
            width: '90'
          },{
            label: '收件人手机号',
            prop: 'receiver_mobile',
            width: '120'
          }]
          let c = {
            w:940, 
            page: false,
            list_prop: 'receiverAddressList'
          }
          
        list(self, '选择客户地址', {}, colData, '/custom-web/api/customClient/getReceiverInfoList', cb, Object.assign(c, config)) 
    },
    transforPeople(self, cb, config={}) { //选择转单用户
        let colData = [{
            label: '姓名',
            prop: 'real_name',
            width: '153'
          },{
            label: '昵称',
            prop: 'nick_name',
            width: '153'
          },{
            label: '工号',
            prop: 'employee_number',
            width: 'auto'
          }]
          let c = {
            w:940, 
          }
          config.initParam = {
              code: 'DZ_SHY'
          }
          let form = {
            labelWidth: '66px',
            queryItems: [
                {
                    cols: [
                        {formType: 'elInput', label: '用户名称', prop:'key', span: 6},
                    ]
                }
            ]
        }
        list(self, '选择转单用户', form, colData, '/custom-web/api/customSysTrade/getAlikeUser?permissionCode=DZ_CHANGE_ORDER', cb, Object.assign(c, config)) 
    },
    urgentFreeList(self, cb, config={}) { //工厂加急
        let colData = [{
            label: '供应商ID',
            prop: 'supplier_id',
            width: '153'
          },{
            label: '供应商名称',
            prop: 'supplierName',
            width: '153'
          },{
            label: '工厂编码',
            prop: 'factory_code',
            width: '153'
          },{
            label: '加急费率（%）',
            prop: 'urgent_fee',
            width: '150'
          },{
            label: '状态',
            prop: 'status_cn',
            filter: 'select',
            options: [ {label: '生效', value: 1}, {label: '失效', value: 0} ],
            width: 'auto'
          }]
          let c = {
            w:940, 
          }
          let form = {
                labelWidth: '100px',
                queryItems: [
                    {
                        cols: [
                            {formType: 'elInput', label: '供应商名称', prop:'supplierName', span: 6},
                        ]
                    }
                ]
            }
        list(self, '选择工厂', form, colData, '/custom-web/api/customSupplierUrgentFee/list', cb, Object.assign(c, config)) 
    }
}
