<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='primary' size='mini' @click="confirm">确定</el-button>
      </el-col>
    </el-row>
    <el-form label-position="right" label-width="120px"> 
        <div style="font-weight: 800;">如果不变更，不选择对应接替人员</div>
        <el-col :span="6" style="width: 100%">
         <el-form-item label="接替导购：">
             <el-select v-model='submit.shopping_guide_number' @change="guideChange" size='mini' >
              <el-option
                v-for="(item,index) in shopGuideList"
                :key="index" 
                :label="item.designer_name" 
                :value="item.designer_number" 
                >
              </el-option>
            </el-select>
        </el-form-item> 
        <el-form-item label="接替设计师：" >
          <el-select v-model='submit.designer_number' 
          @change="designerChange"
           size='mini'>
              <el-option
                v-for="(item,index) in designerList"
                :key="index" 
                :label="item.designer_name" 
                :value="item.designer_number" 
                >
              </el-option>
            </el-select>
        </el-form-item>
      </el-col>
      
    </el-form>
  </div>
</template>

<script>
    export default {
      props:["params"],
      data(){
        return {
          submit:{
            designer:"",
            designer_number:"",
            shopping_guide:'',
            shopping_guide_number:''
          },
          remark:"",
          list:[],
          activeList:[],
          designeropt:[],
          designerList:[],
          shopGuideList:[]
        }
      },
      methods:{
        guideChange(id){
          this.shopGuideList.forEach(item=>{
            if(item.designer_number == id){
              this.submit.shopping_guide = item.designer_name;
            }
          })
        },
        designerChange(id){
          this.designerList.forEach(item=>{
            if(item.designer_number == id){
              this.submit.designer = item.designer_name;
            }
          })
        },
        async confirm(){
            let self = this;
            // this.params.callback()
            console.log(this.params.selectData);
            let postData = JSON.parse(JSON.stringify(this.submit));
            postData.client_numbers = this.params.selectData.map(item=>{return item.client_number})
            this.ajax.postStream('/custom-web/api/customSysTrade/changeUser',postData,function(data){
              data = data.body
              if(data.result){
                self.$message.success(data.msg)

                self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
              }else{
                self.$message.error(data.msg)

              }
            })
        },
         getDesignerList(){
          //获取设计师列表
          var _this = this
          var data = {}
          this.ajax.postStream('/custom-web/api/customClient/getDesignerListByShop',data,function(data){
            data = data.body
            if(data.result){
              _this.designerList = data.content 
             
            }
          })

        },
        getShopGuideList(){
          //获取设计师列表
          var _this = this
          var data = {}
          this.ajax.postStream('/custom-web/api/customClient/getUserListByShop',{role:'DZ_DG'},function(res){
            data = res.body
            if(data.result){
              _this.shopGuideList = data.content 
              
            }
          })

        },

        
      },
      mounted(){
        this.getDesignerList();
        this.getShopGuideList();
      }
    }
</script>
