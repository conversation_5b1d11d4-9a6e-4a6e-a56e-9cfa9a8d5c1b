
<template>
<!-- 合同阶段-添加合同 -->
    <div>
      <form-create 
        ref="formCreate"
        class="compactEdit"
        :formData="formData" 
        :ruleData="ruleData"
        :btns="false"
        :labelWidth="labelWidth"
        @save="save"
        @ready="ready"
    >   
        <div slot="footer" class="btns">
            <!-- <el-button type="primary" @click="saveForm(0)">保存</el-button> -->
            <el-button type="success" v-if="canSubmit" @click="saveForm(1)">提交</el-button>
        </div>
    </form-create>
    </div>
</template>
<script>
import {createId} from '../common/api'
import formCreate from '../components/formCreate/formCreate'
import { choose_reason,combo_type } from '../common/compactDictionary'
import closeComponent from '../common/mixins/closeComponent'
//拎包属性
const LBAttr=[{label:"是",value:'Y',},{label:"否",value:'N'}]
export default {
    components:{formCreate},
    mixins: [closeComponent],
    data() {
        let self = this
        return {
            canSubmit: true,
            remark: '',
            formData: [],
            labelWidth: '100px',
            event: {
                save(data) {
                    resolve(data)
                }
            },
            ruleData: { 
                compact_number: {required: true, message: '请输入合同编号', trigger: 'blur'},

                compact_date: {
                     validate(_this) { //合同日期不能大于当前时间
                        return [{
                            required: true,
                            message: '请选择小于当前时间的日期',
                            isShow: false,
                            trigger: 'change',
                            validator: function(rule, value, callback) {
                                if (!_this.self) {
                                    console.log('this validate object is null');
                                    return;
                                }
                                let currentTime = new Date().getTime()
                                let formData = {}
                                let selectedTime = new Date(value).getTime()
                                self.$refs.formCreate && (formData = self.$refs.formCreate.model())
                                if(
                                    (_this.required && (!value && value !== '0')) || 
                                    selectedTime > currentTime
                                ) {
                                    _this.self.rules[ rule.field][0].isShow = true
                                    callback(new Error(''));
                                    return
                                } 
                                _this.self.rules[ rule.field][0].isShow = false
                                callback();
                            }
                        }]
                    }, required: true
                },
                // last_order_date: {
                //      validate(_this) { //最晚下单日期不能小于合同日期
                //         return [{
                //             required: true,
                //             message: '请选择大于合同时间的日期且大于今天',
                //             isShow: false,
                //             trigger: 'change',
                //             validator: function(rule, value, callback) {
                //                 if (!_this.self) {
                //                     console.log('this validate object is null');
                //                     return;
                //                 }
                //                 let formData = {}
                //                 let selectedTime = new Date(value).getTime()
                //                 self.$refs.formCreate && (formData = self.$refs.formCreate.model())
                //                 if(
                //                     (_this.required && (!value && value !== '0')) || 
                //                     (formData.compact_date && selectedTime < new Date(formData.compact_date).getTime())
                //                 ) {
                //                     _this.self.rules[ rule.field][0].isShow = true
                //                     callback(new Error(''));
                //                     return
                //                 } 
                //                 _this.self.rules[ rule.field][0].isShow = false
                //                 callback();
                //             }
                //         }]
                //     }, required: true
                // },
                // promise_consign_date: {
                //     validate: 'notLtCurrentTime', required: true
                // },
                combo_type: {required: true, message: '请选择套餐类型', trigger: 'change'},
                uploadFile: {required: true, message: '请上传合同相关文件', trigger: 'change'},
                compact_sum: {
                    validate: "compact_sum"
                }
            },
            
        }
    },
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    mounted() {
        this.params.initValue && this.$refs.formCreate.setValue(this.params.initValue)
        
        console.log(this.params.initValue);
    },
    methods: {
        saveComponent() {
            this.saveForm(0)
        },
        async ready(set){
            this.id =  this.params.initValue.compact_id || await createId()
            let canOnlyUrgent = this.params.initValue.canOnlyUrgent
            let flag=this.params.initValue.if_bag_show
            // 合同签订之后不可以提交
            if(canOnlyUrgent) {
                this.canSubmit = false
            }
          
            if(flag){
                this.ruleData.if_bag={required: true, message: '请选择是否拎包', trigger: 'change'}
                this.formData = set([
                        {
                                cols: [
                                    {formType: 'myText', label: '客户号', prop: 'client_number', span: 12},
                                    {formType: 'myText', label: '客户姓名', prop: 'client_name', span: 12},
                                    {formType: 'myText', label: '客户电话', prop: 'client_mobile', span: 12},
                                    {formType: 'myText', label: '设计师', prop: 'designer_name', span: 12},
                                    {formType: 'myInput', disabled: canOnlyUrgent, label: '合同编号',   prop: 'compact_number', type: 'string', span: 12, event:{
                                        input(v, col){
                                        col.value = v.replace(/[^a-zA-Z0-9-_]/g, '')
                                        }},
                                    },
                                    {formType: 'elDatePicker', disabled: canOnlyUrgent, label: '合同日期',   prop: 'compact_date', type: 'date', span: 12},
                                    {formType: 'elInput', disabled: canOnlyUrgent, type:'number', label: '合同总金额',maxlength:1000000, suffix: '元',  prop: 'compact_sum', span: 24},
                                    {formType: 'elRadioGroup', label: '是否加急', value: 0,   prop: 'is_urgent', span: 24, remarkTips:this.params.initValue.is_urgent==1?'加急单为额外付费服务，需收取门店加急费':'', options: [{label: '加急',value: 1}, {label: '不加急', value: 0}], event:{
                                        input(v, col){
                                        // col.value = v.replace(/[^a-zA-Z0-9-_]/g, '')
                                        if(v==1){
                                            col.remarkTips = '加急单为额外付费服务，需收取门店加急费'
                                        }else{
                                            col.remarkTips = ''
                                        }
                                        }},},
                                    // {formType: 'elDatePicker', disabled: canOnlyUrgent, label: '最晚下单日期', value: new Date().getTime(),  prop: 'last_order_date', type: 'date', span: 12},
                                    // {formType: 'elDatePicker', disabled: canOnlyUrgent, label: '约定交付日期', value: new Date().getTime() + 1000*60*60*24*45,  prop: 'promise_consign_date', type: 'date', span: 12},
                                    {formType: 'elCheckboxGroup', disabled: canOnlyUrgent, label: '购买原因', prop: 'choose_reason', span: 24, options: choose_reason},
                                    {formType: 'elRadioGroup', disabled: canOnlyUrgent, label: '套餐类型', prop: 'combo_type', span: 24, options: combo_type},
                                    {formType: 'elRadioGroup', disabled: canOnlyUrgent, label: '是否拎包', prop: 'if_bag', span: 24, options: LBAttr},
                                    {formType: 'uploadFile', disabled: canOnlyUrgent, prop: 'uploadFile', label: '合同文件', config: {parent_no:this.id}, span: 24},
                                    {formType: 'elInput', disabled: canOnlyUrgent, label: '备注', type: 'textarea',  prop: 'remark', span: 24, maxlength:150},
                                ]
                                
                            }
                        ], this.params.initValue || {})
            }else{
                this.formData = set([
            {
                    cols: [
                        {formType: 'myText', label: '客户号', prop: 'client_number', span: 12},
                        {formType: 'myText', label: '客户姓名', prop: 'client_name', span: 12},
                        {formType: 'myText', label: '客户电话', prop: 'client_mobile', span: 12},
                        {formType: 'myText', label: '设计师', prop: 'designer_name', span: 12},
                        {formType: 'myText', disabled: true, label: '合同编号',  prop: 'compact_number', type: 'string', span: 12, event:{
                            input(v, col){
                            col.value = v.replace(/[^a-zA-Z0-9-_]/g, '')
                            }},
                        },
                        {formType: 'elDatePicker', disabled: true, label: '合同日期',   prop: 'compact_date', type: 'date', span: 12},
                        {formType: 'elInput', disabled: false, type:'number', label: '合同总金额', suffix: '元',  prop: 'compact_sum', span: 24},
                        {formType: 'elRadioGroup',disabled: true,  label: '是否加急', value: 0,   prop: 'is_urgent', span: 24, remarkTips:this.params.initValue.is_urgent==1?'加急单为额外付费服务，需收取门店加急费':'', options: [{label: '加急',value: 1}, {label: '不加急', value: 0}], event:{
                                        input(v, col){
                                        // col.value = v.replace(/[^a-zA-Z0-9-_]/g, '')
                                        if(v==1){
                                            col.remarkTips = '加急单为额外付费服务，需收取门店加急费'
                                        }else{
                                            col.remarkTips = ''
                                        }
                                        }},},
                        // {formType: 'elDatePicker', disabled: canOnlyUrgent, label: '最晚下单日期', value: new Date().getTime(),  prop: 'last_order_date', type: 'date', span: 12},
                        // {formType: 'elDatePicker', disabled: canOnlyUrgent, label: '约定交付日期', value: new Date().getTime() + 1000*60*60*24*45,  prop: 'promise_consign_date', type: 'date', span: 12},
                        {formType: 'elCheckboxGroup', disabled: true, label: '购买原因', prop: 'choose_reason', span: 24, options: choose_reason},
                        {formType: 'elRadioGroup', disabled: true, label: '套餐类型', prop: 'combo_type', span: 24, options: combo_type},
                        {formType: 'uploadFile', disabled: true, prop: 'uploadFile', label: '合同文件', config: {parent_no:this.id}, span: 24},
                        {formType: 'elInput', disabled: true, label: '备注', type: 'textarea',  prop: 'remark', span: 24, maxlength:150},
                    ]
                    
                }
            ], this.params.initValue || {})
        }
        },
        saveForm(submit_flg) {
            // console.log(,1111);
            let self = this;
            let data = this.$refs.formCreate.form.dynamic[0].cols;
            let ifUrgent = false;
            data.forEach(item=>{
                if(item.prop =="is_urgent"){
                    if(item.value==1){
                        ifUrgent = true;
                    }
                }
            })
            // return;
            if(ifUrgent){
                self.$confirm('当前合同选择加急，加急单为额外付费服务，需收取门店加急费, 是否确认提交订单?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                    }).then(() => {
                        self.submit_flg = submit_flg
                        self.$refs.formCreate.save()
                    }).catch(() => {
                             
                });
            }else{
                self.submit_flg = submit_flg
                self.$refs.formCreate.save()
            }
            
        },
        getRemarkContent(data){
            console.log(data)
        },
        save(data) {
            var _this = this
            const { client_number, client_name, client_mobile, designer_number, designer_name } = this.params.initValue
            let compactInfo = {
                client_number
            }
            console.log(this.formData)
            for (var obj in compactInfo) {
                data[obj] = compactInfo[obj];
            }
            data.compact_id = this.id
            delete data.choose_reason
            delete data.client_mobile
            delete data.client_name
            delete data.combo_type
            delete data.compact_date
            delete data.compact_number
            delete data.designer_name
            delete data.is_urgent
            delete data.remark
            if(data.compact_sum <100 ||data.compact_sum >5*100*1000){
                this.$message({
                    type: 'warning',
                    message: '合同金额数值范围修改为：【100元-50万元】'
                })
                return
            }
            if(this.request) {
                this.$message({
                    type: 'warning',
                    message: '请等待上一次请求结束'
                })
                return
            }
            this.request = true
            this.ajax.postStream('/custom-web/api/customCompact/changeCompactSum',data,(data) =>{
                this.request = false
                data = data.body
                _this.$message({
                    message: data.msg,
                    type:data.result?'success':'error'
                })
                if(data.result) {
                     this.$root.eventHandle.$emit('refreshclientList')
                     this.$root.eventHandle.$emit('refreshcompactList')
                     this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
                }
                },function(data){
                 _this.request = false
                })
            if(this.params.event && this.params.event.save) {
                this.params.event.save(data)         
            }

            
        }
    }
}
</script>
<style lang="stylus" scoped>
.btns {
    text-align: center;
    margin-top: 20px;
    &>span {
        padding-left: 10px;
        color: #aaa;
    }
}

</style>
