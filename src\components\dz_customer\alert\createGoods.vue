<template>
  <!-- 商品列表-创建编辑商品订单 -->
  <div style="height: 570px; overflow: auto">
    <form-create
      ref="formCreate"
      :btns="false"
      :formData="formData"
      :ruleData="ruleData"
      @ready="ready"
      @save="save"
    ></form-create>
    <div class="form-btns">
      <el-button type="success" :loading="sysLoading" size="small" @click="sysGoods"
        >同步</el-button
      >
      <!-- <el-button v-if="canSubmit" type="success" size="small" @click="submit"
        >提交审核</el-button
      > -->
      <el-button v-if="canSave" type="primary" size="small" @click="saveBtn"
        >保存</el-button
      >
      <el-button type="danger" size="small" @click="resetFields"
        >重置</el-button
      >
    </div>
  </div>
</template>

<script>
import {
  createId,
  getMaterial,
  addCustomGoods,
  customGoodsSubmit,
  synSwjGoodsMsg
} from "../common/api";
import { list } from "../alert/alert"
import { style, category } from "../common/goodsDictionary";
import formCreate from "../components/formCreate/formCreate";
import orderStatus from "../common/mixins/orderStatus";
import closeComponent from "../common/mixins/closeComponent";
import setting from '../../discount/model/setting';
export default {
  mixins: [orderStatus, closeComponent],
  components: { formCreate },

  data() {
    return {
      imgList:[],
      formData: [],
      initParam: {},
      labelWidth: "0px",
      order_no:'',
      ruleData: Object.freeze({
        // uploadFile: {
        //   required: true,
        //   message: "请上传文件",
        //   trigger: "change",
        // },
        style: { required: true, message: "请选择风格", trigger: "change" },
        category: { required: true, message: "请选择类目", trigger: "change" },
        material: { required: true, message: "请选择材质", trigger: "change" },
        color: { required: true, message: "请选择颜色", trigger: "change" },
        // retail_price: {
        //     validate: "isPrice"
        // },
        act_price: {
          validate: "isPriceNotZero",
        },
        length: {
          validate: 'integerNotZero',
          required: true,
          msg: '只能输入1-9999的正整数'
        },
        width: {
          validate: 'integerNotZero',
          required: true,
          msg: '只能输入1-9999的正整数'
        },
        height: {
          validate: 'integerNotZero',
          required: true,
          msg: '只能输入1-9999的正整数'
        },
        message: { required: true, message: "请输入商品信息", trigger: "blur" },
      }),
      event: {
        save(data) {
          resolve(data);
        },
      },
      sysLoading: false
    };
  },
  props: {
    params: {
      type: Object,
      default() {
        return {};
      },
    },
  },

  computed: {
    canSave() {
      return this.params.initValue.client_status_code
        ? !this.rejectStatus(this.params.initValue.client_status_code)
        : true;
    },
    canSubmit() {
      return this.params.initValue.client_status === "REJECT_VERIFY";
    },
  },
  methods: {
    getImgList(resolve){
       let postData = {
          order_no:this.order_no,
          page_size:1000,
          sub_order_no: ""
       }
       this.ajax.postStream('/file-iweb/api/cloud/file/list', postData, res => {
				if(res.body.result){
					this.imgList = res.body.content.list;
          resolve && resolve()
				}else {
					this.$message.error(res.body.msg)
				}
			})
     },
    saveComponent() {
      this.saveBtn();
    },
    lookBj() {
    /**
     * @description: 查看报价详情
     * @param {*}
     * @return {*}
     */
      const cb = () => {}
      list(
        this, 
        '报价清单', 
        {}, 
        [
        {
          label: '单元名称',
          prop: 'myUnitName',
          width: '120',
        },
        {
          label: '部件的名称',
          prop: 'name',
          width: '100',
        },
        {
          label: '部件所属的报价类型',
          prop: 'quoteTypeCode',
          width: '120',
        },
        {
          label: '部件编号',
          prop: 'partNumber',
          width: '70',
        },
        {
          label: '深(mm)',
          prop: 'depth',
          width: '70',
        },
        
        {
          label: '高(mm)',
          prop: 'height',
          width: '70',
        },
        {
          label: '宽(mm)',
          prop: 'width',
          width: '70',
        },
        {
          label: '颜色',
          prop: 'colorCode',
          width: '100',
        },
        {
          label: '基材',
          prop: 'baseCode',
          width: '70',
        },
        {
          label: '单价',
          prop: 'priceUnit',
          width: '70',
        },
        {
          label: '用量',
          prop: 'consumption',
          width: '70',
        },
        {
          label: '特殊加价',
          prop: 'specialUpPrice',
          width: '100',
        },
        {
          label: '金额',
          prop: 'amountMoney',
          width: '100',
        },
        {
          label: '单位',
          prop: 'unit',
          width: '100',
        },
        {
          label: '组合名称',
          prop: 'myGroupName',
          width: '120',
        }
      ], 
        '/custom-web/api/customSwj/listSwjQuotation', 
        cb, 
        {
          w: 1100,
          h:640,
          initParam: {
            quotation_type: 'ACT_PRICE',
            swj_goods_id: this.params.initValue.swj_goods_id
          }
        }
      )   
    },
    sysGoods() {
    /**
     * @description: 同步三维家商品数据
     * @param {*}
     * @return {*}
     */  
      const {
        swj_goods_id,
        goods_id,
        custom_goods_id
      } = this.params.initValue
      this.sysLoading = true
      synSwjGoodsMsg({
        swj_goods_id,
        goods_id,
        custom_goods_id
      }, false, true).then(res => {
        this.sysLoading = false
        if(res.result) {
          // 更新文件
          this.$refs.formCreate.updateFile()
          // 同步成功,更新数据
          this.params.callback && this.params.callback(this.init, this.initParam.custom_goods_id)
        }
      }).catch(() => {
        this.sysLoading = false
      })
    },
    submit() {
      //   重新审核
      this.btnType = "ven";
      if (this.isRequest) {
        this.$message.warning("请等待上一次请求结束");
        return;
      }
      this.$refs.formCreate.save();
    },
    saveBtn() {
      //   保存商品信息

      if (this.isRequest) {
        this.$message.warning("请等待上一次请求结束");
        return;
      }

      this.btnType = "save";
      new Promise((resolve,reject)=>{
        this.getImgList(resolve)

      }).then(res=>{
       this.$refs.formCreate.save();
      })
    },
    save(data) {
      //   表单验证回调
      // 保存商品信息
      
      let jpgFalg = false;
      let dwgFalg = false;
      this.imgList.forEach(item=>{
        if(item.file_type == 'jpg'){
          jpgFalg = true;
        }
        if(item.file_type == 'dwg'){
          dwgFalg = true;
        }
      })
      if(!(dwgFalg && jpgFalg)){
        
            this.$message.warning("请上传JPG格式的线框图和DWG格式的3D文件。");
            return
      }
       if(!!data.material && !data.color){
            this.$message.warning("请选择花色");
            return
      }
      this.isRequest = true;
      // console.log(data)
     
      if (this.canSave) {
        if(/[~!@#$^&*()+=|'':;‘’,\[\].<>/\\?~！￥……%（）|{}【】；：”“。，、？_`《》 ]/.test(data.message)){
            this.$message.warning("商品名称只支持中文、英文、数字，不能包含特殊符号，请修改后再保存。");
            this.isRequest = false;
            return
        }
        addCustomGoods(
          Object.assign({}, data, this.initParam),
          false,
          true
        ).then((body) => {
          if (body.result) {
            if (this.btnType === "ven") {
              //   重新审核
              this.customGoodsSubmit();
            } else {
              this.isRequest = false;
              this.request(body);
            }
          } else {
            this.isRequest = false;
          }
        }).catch(() => {
          this.isRequest = false;
        });
      } else {
        this.customGoodsSubmit();
      }
    },
    customGoodsSubmit() {
      //   重新审核
      customGoodsSubmit(
        { custom_goods_id: this.initParam.custom_goods_id },
        false,
        true
      ).then((verBody) => {
        this.isRequest = false;
        if (verBody.result) {
          // 提交完成
          this.request(verBody);
        }
      });
    },
    resetFields() {
      //   重置表单
      this.$refs.formCreate.resetFields();
    },
    request(e) {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
      this.$root.eventHandle.$emit("saveCustomGoods", e);
    },
    ready(set) {
      this.setValue = set
      this.init(JSON.parse(JSON.stringify(this.params.initValue || {})))
    },
    async init(value = {}) {
      const self = this
      const set = this.setValue
      console.log(set)
      // 获取材质或花色
      let material_options = [];
      let color_options = [];
      getMaterial({categoryCode:'CUSTOM_GOODS_MATERIAL'}).then((list) => {
        list.forEach((item) => {
          material_options.push({
            label: item.name,
            value: item.tag
          });
        });
        if (value.color && value.material) {
          let tag = material_options.filter(
            (item) => item.value == value.material
          )[0].value;
          getMaterial({
              categoryCode: "CUSTOM_GOODS_COLOR",
              parentCategoryCode:'CUSTOM_GOODS_MATERIAL',
              parentCode: tag
            }).then((l) => {
            l.forEach((item) => {
              color_options.push({
                label: item.name,
                value: item.tag,
              });
            });
          });
        }
      });
      if (!value.custom_goods_id) {
        // 新增
        value.custom_goods_id = await createId();
        this.initParam.is_add = true;
      }
      this.order_no = value.custom_goods_id

      Object.assign(this.initParam, {
        custom_goods_id: value.custom_goods_id,
        goods_id: value.goods_id || "",
        client_number: value.client_number,
        design_room_id: value.design_room_id,
        designer: value.designer_name,
      });

      this.formData = set(
        [
          {
            cols: [
              {
                formType: "myText",
                label: "专卖店",
                value: value.shop_name,
                span: 12,
              },
              {
                formType: "myText",
                label: "专卖店编号",
                value: value.shop_code,
                span: 12,
              },
              {
                formType: "myText",
                label: "空间名",
                value: value.design_room_name,
                span: 12,
              },
              {
                formType: "myText",
                label: "设计师",
                value: value.designer_name,
                span: 12,
              },
              {
                formType: "myText",
                label: "商品号",
                value: value.goods_id,
                span: 12,
              },
              {
                formType: "myText",
                label: "客户姓名",
                value: value.client_name,
                span: 12,
              },
              {
                formType: "myText",
                label: "客户电话",
                value: value.client_mobile,
                span: 24,
              },
              {
                formType: "elSelect",
                prop: "style",
                label: "风格",
                options: style,
                span: 12,
              },
              {
                formType: "elSelect",
                prop: "category",
                label: "类目",
                options: category,
                span: 12,
              },
              {
                formType: "elSelect",
                prop: "material",
                label: "材质",
                options: material_options,
                event: {
                  async change(e, col, v, getItem) {
                    const color = getItem("color");
                    let tag = material_options.filter(
                      (item) => item.value == e
                    )[0].value;
                    const list = await getMaterial({
                      categoryCode: "CUSTOM_GOODS_COLOR",
                      parentCategoryCode:'CUSTOM_GOODS_MATERIAL',
                      parentCode: tag
                    });
                    color.value = "";
                    color.options = list.map((item) => {
                      return {
                        label: item.name,
                        value: item.tag,
                      };
                    });
                  },
                },
                span: 12,
              },
              {
                formType: "elSelect",
                prop: "color",
                label: "花色",
                options: color_options,
                span: 12,
                 event: {
                   change(e, col, v, getItem) {
                    const color = getItem("color");
                    console.log(e,col,v);
                    let tag = material_options.filter(
                      (item) => item.value == e
                    )[0].value;
                    const list = getMaterial({
                      categoryCode: "CUSTOM_GOODS_COLOR",
                      parentCategoryCode:'CUSTOM_GOODS_MATERIAL',
                      parentCode: tag
                    });
                    color.value = "";
                    color.options = list.map((item) => {
                      return {
                        label: item.name,
                        value: item.tag,
                      };
                    });
                  },
                },
              },
              {
                formType: "inputNumber",
                prop: "length",
                label: "宽度",
                maxlength: 4,
                suffix: "毫米(mm)",
                filter(v) {
                  return Number(v);
                },
                span: 12,
              },
              {
                formType: "inputNumber",
                prop: "width",
                label: "深度",
                maxlength: 4,
                suffix: "毫米(mm)",
                filter(v) {
                  return Number(v);
                },
                span: 12,
              },
              {
                formType: "inputNumber",
                prop: "height",
                maxlength: 4,
                label: "高度",
                suffix: "毫米(mm)",
                filter(v) {
                  return Number(v);
                },
                span: 12,
              },
              // {formType: 'elInput', prop: 'retail_price', label: '零售价', type:'number', suffix:'元',  span: 12},
              // {
              //   formType: "elInput",
              //   prop: "act_price",
              //   label: "实际价格",
              //   type: "number",
              //   suffix: "元",
              //   span: 12,
              // },
              {
                formType: {
                  template: `<div><el-button type="primary" size="mini" @click="$emit('click')" 
                    >查看报价清单</el-button
                  ></div>`
                },
                event: {
                  click() {
                    self.lookBj()
                  }
                },
                label: '报价'
              },
              {
                formType: "uploadFile",
                prop: "uploadFile",
                label: "设计文件",
                config: {
                  parent_no: value.custom_goods_id,
                  fileListFilter(list) {
                    return list.filter((item) => !item.sub_order_no);
                  },
                },
                span: 24,
              },
              {
                formType: "elInput",
                prop: "message",
                label: "商品名称",
                type: "textarea",
                maxlength: 40,
                span: 18,
              },
            ],
          },
        ],
        value
      );
    },
  },
 

};
</script>

<style lang="stylus" scoped>
.form-btns {
  text-align: center;
}

.btns {
  text-align: center;
  margin-top: 20px;

  &>span {
    padding-left: 10px;
    color: #aaa;
  }
}
</style>
