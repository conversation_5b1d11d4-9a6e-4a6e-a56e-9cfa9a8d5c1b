<template>
<!-- 设计阶段-设计师自评 -->
    <form-create 
        ref="formCreate"
        :formData="formData" 
        :btns="false"
        :ruleData="ruleData"
        @save="save"
        :labelWidth="labelWidth"
    >
        <div slot="footer" class="btns">
            <el-button type="primary" @click="saveForm">确认</el-button> 
        </div>
    </form-create>
</template>
<script>
import formCreate from '../components/formCreate/formCreate'
import { deal_point } from '../common/designCompleteDic'
export default {
    components:{formCreate},
    data() {
        return {
            ruleData: {
                reserve_come_time: {
                    required: true,
                    trigger: 'change',
                    message: '请选择预约进店时间'
                },
                design_self_evaluation: {
                    required: true,
                    trigger: 'change',
                    message: '请选择方案自评'
                },
                deal_point: {
                    required: true,
                    trigger: 'change',
                    message: '请选择成交关键点'
                }
            },
            formData: [
                {
                    cols: [
                        {formType: 'elDatePicker', label: '预约进店时间', prop: 'reserve_come_time', type:'date', format: 'timestamp', span: 24},
                    ]
                },
                {
                    cols: [
                        {formType: 'elRate', label: '方案自评', prop: 'design_self_evaluation', span: 24},
                    ]
                },
                {
                    cols: [
                        { 
                            formType: 'elRadioGroup', 
                            label: '成交关键点', 
                            prop: 'deal_point',
                            layout: 'vertical',
                            options: deal_point,
                            span: 24
                        },
                    ]
                }
            ],
            labelWidth: '140px'
        }
    },
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    mounted() {
        this.params.initValue && this.$refs.formCreate.setValue(this.params.initValue)
    },
    methods: {
        saveForm() {
            this.$refs.formCreate.save()
        },
        save(data) {
            var _this = this
            const { client_number, designer_number, designer_name } = this.params.initValue
            let customInfo = {
                client_number,
                designer_number,
                designer_name
            }
            for (var obj in customInfo) {
                data[obj] = customInfo[obj];
            }
            if(this.request) {
                this.$message({
                    type: 'warning',
                    message: '请等待上一次请求结束'
                })
                return
            }
            this.request = true
            this.ajax.postStream('/custom-web/api/customDesignRegister/save',data,function(data){
                _this.request = false
                data = data.body
                _this.$message({
                    message: data.msg,
                    type:data.result?'success':'error'
                })
                console.log('登记成功');
                _this.$root.eventHandle.$emit('refreshclientList')
                _this.$root.eventHandle.$emit('refreshdesignList')
                _this.$root.eventHandle.$emit('removeAlert',_this.params.alertId)
                },function(data){
                console.log('失败的回调函数')
                _this.request = false
            })
            
        }
    }
}
</script>
<style lang="stylus" scoped>
.btns {
    text-align: center;
    margin-top: 20px;
    &>span {
        padding-left: 10px;
        color: #aaa;
    }
}
</style>
