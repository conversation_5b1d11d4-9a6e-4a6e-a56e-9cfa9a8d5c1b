<template>
<!-- 设计阶段-方案设计 -->
    <div style="height: 100%; overflow: auto;">
      <div style="padding: 10px 0;">量尺空间信息</div>
      <image-list :params="{value: measure_room_ids}"/>
      <form-create 
        ref="formCreate"
        :formData="formData" 
        :btns="false"
        @ready="ready"
        :labelWidth="labelWidth"
    >
        <div slot="footer" class="btns">
             <el-button type="primary" :loading="loading" @click="init">刷新</el-button> 
             <el-button type="primary" @click="save">保存</el-button>
             <el-button type="success" @click="swj">三维家设计</el-button> 
        </div>
    </form-create>
    </div>
</template>
<script>
import imageList from '../clientInfo/imageList'
import formCreate from '../components/formCreate/formCreate'
import { tags } from '../common/measureDictionary'
import { createId, createIds, getMeasure, getFileList, getDesignInfo, gotoSwj } from '../common/api'
import closeComponent from '../common/mixins/closeComponent'
export default {
    components:{formCreate, imageList},
    mixins: [closeComponent],
    data() {
        return {
            remark: '',
            measure_room_ids: [],
            formData: [],
            custom_design_id: '',
            labelWidth: '0px',
            event: {
                save(data) {
                    resolve(data)
                }
            },
            loading: false
        }
    },
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    methods: {
        swj() {
            const client_number = this.params.initValue.client_number
            const design_id = this.designInfo.design_id
            gotoSwj({
                client_number,
                design_id
            }).then(res => {
                if(res.result) {
                    window.open(res.content)
                }
            })
            
        },
        saveComponent() {
            this.save()
        },
        ready(set) {
            this.setValue = set
            this.init()
        },
        async init() {
            const set = this.setValue
            // 设置初始状态
            this.measure_room_ids = []
            this.loading = true
            // 显示量尺空间信息
            const designInfo = await getDesignInfo({client_number:this.params.initValue.client_number})
            this.designInfo = designInfo || {}
            this.custom_design_id = designInfo.custom_design_id
            const designRoomList = await this.getMeasure(this.params.initValue.client_number)
            let initDesignRoomList = designInfo.designRoomList || []
            this.loading = false
            // 判断设计空间信息为空则使用量尺空间信息
            // if(initDesignRoomList.length === 0) {
            //     const ids = await createIds(designRoomList.length)
            //     initDesignRoomList = designRoomList.map((item, index) => {
            //         return {
            //             is_add: true,
            //             design_room_id: ids[index],
            //             design_room_name: item.measure_room_name
            //         }
            //     })
            // }
            // 自动生成设计id
            if(!this.custom_design_id) {
                this.custom_design_id = await createId()
            } 
            this.formData = set([
                {
                    cols: [
                        { 
                            formType: 'measureSpace', 
                            label: '设计空间',
                            labelWidth: '120',
                            config: {
                                spacename: 'design_room_name',
                                detail: 'design_point',
                                client_number: this.custom_design_id,
                                cn: this.params.initValue.client_number,
                                useBy: 'design',
                                id: "design_room_id"
                            }, 
                            prop: 'designRoomList', 
                            span: 24, 
                            tags: tags.map(item=>{item.spacename = item.label; return item})
                        },
                    ]
                }
            ], {designRoomList: initDesignRoomList})
        },
        async getMeasure(client_number) {
            // 获取量尺空间信息
            const value = await getMeasure({client_number})
            let measure_room_ids = this.measure_room_ids
            getFileList({
                order_no: value.custom_measure_id
            }).then(res => {
                let imageMap = {}
                let images = res
                images.forEach(item => {
                    !imageMap[item.sub_order_no] && (imageMap[item.sub_order_no] = [])
                    imageMap[item.sub_order_no].push(item.path)
                })
                const ids = value.measureRoomList.map(item => {
                    return {
                        images: imageMap[item.measure_room_id] || [], 
                        name: item.measure_room_name,
                        parent_no: value.custom_measure_id,
                        client_number: client_number,
                        child_no: item.measure_room_id,
                        detail: item.design_point,
                        useBy: 'measure'
                    }
                })
                ids.forEach(item => {
                    measure_room_ids.push(item)
                })
            })
            return value.measureRoomList
        },
        async save() {
            // 保存设计方案
            var _this = this
            let data = this.$refs.formCreate.model()
            let { client_number, designer_number, designer_name, design_id: design_id='' } = Object.assign({}, this.params.initValue, this.designInfo)
            const remark = this.remark
            let custom_design_id = this.custom_design_id
            let designInfo = {
              client_number, designer_number, designer_name, design_id, remark, custom_design_id
            }
            data = Object.assign(data,designInfo)
            if(data.designRoomList.length === 0) {
                this.$message({
                    type: 'warning',
                    message: '请添加设计空间属性'
                })
                return
            }
            if(this.request) {
                this.$message({
                    type: 'warning',
                    message: '请等待上一次请求结束'
                })
                return
            }
            // 判断空间是否有上传附件
            let noFileMeasureRoomIndex = data.designRoomList.findIndex(item => item.is_add && !item.imgs)
            if(noFileMeasureRoomIndex !== -1) {
                this.$message.error(`请上传${data.designRoomList[noFileMeasureRoomIndex].design_room_name}空间文件`)
                return
            }
            data.designRoomList = data.designRoomList.map(item=>{
                delete item.imgs
                delete item.data
                return item
            })
            this.request = true
            this.ajax.postStream('/custom-web/api/customDesign/save',data,(data) =>{
                this.request = false
                data = data.body
                if(data.result) {
                    this.$root.eventHandle.$emit('refreshclientList')
                    this.$root.eventHandle.$emit('refreshdesignList')
                    this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
                }
                _this.$message({
                    message: data.msg,
                    type:data.result?'success':'error'
                })
                
            },function(data){
                console.log('失败的回调函数')
                _this.request = false
            })
        }
    }
}
</script>
<style lang="stylus" scoped>
.btns {
    text-align: center;
    margin-top: 20px;
    &>span {
        padding-left: 10px;
        color: #aaa;
    }
}
</style>
