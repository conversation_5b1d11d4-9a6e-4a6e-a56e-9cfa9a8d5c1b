<template>
<!-- 沟通阶段-沟通登记 -->
  <div >
    <form-create
        ref="formCreate"
        requestUrl="/custom-web/api/customCommunication/save"
        :formData="formData"
        :initParam="initParam"
        :ruleData="ruleData"
        :paramFilter="paramFilter"
        height="528px"
        @request="request"
    ></form-create>
  </div>
</template>

<script>
import {communication} from '../common/followDictionary'
import formCreate from '../components/formCreate/formCreate'
import {getDesignInfo} from '../common/api'
export default {
  components:{formCreate},
  data() {
        let self = this
        return {
            formData: [],
            initParam:{},
            ruleData:{
                communication_situation: {required: true},
                wait_deal_point: {required: true, message: '必选一项', trigger: 'change'},
                next_communication_time: {
                     validate(_this) { //下次沟通时间不能小于当前时间和本次沟通时间
                        return [{
                            required: true,
                            message: '请选择大于当前时间和本次沟通时间的日期',
                            isShow: false,
                            trigger: 'change',
                            validator: function(rule, value, callback) {
                                if (!_this.self) {
                                    console.log('this validate object is null');
                                    return;
                                }
                                let currentTime = new Date().getTime()
                                let formData = {}
                                let selectedTime = new Date(value).getTime()
                                self.$refs.formCreate && (formData = self.$refs.formCreate.model())
                                if(
                                    (_this.required && (!value && value !== '0')) || 
                                    selectedTime < currentTime ||
                                    (formData.communication_time && selectedTime < new Date(formData.communication_time).getTime())
                                ) {
                                    _this.self.rules[ rule.field][0].isShow = true
                                    callback(new Error(''));
                                    return
                                } 
                                _this.self.rules[ rule.field][0].isShow = false
                                callback();
                            }
                        }]
                    }, required: true
                },
                communication_time: {required: true}
            }
        }
    },
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    async created() {
        let client = this.params.client || {}
        let room
        if(this.params.room) {
            room = this.params.room
        } else {
            let designInfo = await getDesignInfo( {client_number: client.client_number})
            room = designInfo.designRoomList || []
        }
        this.room = room
        let self = this
        this.formData = [
                {
                    cols: [
                        {formType: 'myText', label: '客户号', span: 12, value: client.client_number},
                        {formType: 'myText', label: '客户姓名',  value: client.client_name, span: 12},
                        {formType: 'myText', label: '客户电话',  value: client.client_mobile, span: 12},
                        {formType: 'myText', label: '设计师',  value: client.designer_name, span: 12},
                        
                    ]
                },
                {
                    title: '空间修改意见',
                    cols: room.map((item, index) => {
                        return {formType: 'elInput', type: 'textarea', maxlength: 150, label: item.design_room_name, prop: index + 'communicationDetails', span: 8}
                    }) 
                },
                {
                    title: '其他信息',
                    cols: [
                        {formType: 'elInput', type: 'textarea', maxlength: 500, label: '沟通情况', prop: 'communication_situation', span: 13},
                        {formType: 'elCheckboxGroup', label: '待成交关键点', prop: 'wait_deal_point', span: 24, options: communication},
                        {formType: 'elDatePicker', format: 'timestamp', label: '沟通时间', prop: 'communication_time', type:'datetime', span: 24},
                        {formType: 'elDatePicker', format: 'timestamp', label: '下次沟通时间', prop: 'next_communication_time', type:'datetime', span: 24},
                        {
                            formType: 'elRadioGroup', 
                            label: '是否沟通完毕', 
                            prop: 'if_complete', 
                            value: '1', 
                            options: [{label: '是', value: '0'}, {label: '否', value: '1'}], 
                            span: 24,
                            event: {
                                change(v, col, formData, getItem) {
                                    let next_communication_time = getItem('next_communication_time')
                                    self.ruleData.next_communication_time.required = v == 1
                                    self.ruleData.wait_deal_point.required = v == 1
                                    next_communication_time.disabled = v == 0
                                    next_communication_time.value = ''
                                    self.$nextTick(() => {
                                        self.$refs.formCreate.dealRule(true)
                                    })
                                },
                            }
                        }
                    ]
                }
            ]
    },
    methods: {
      paramFilter(data) {
          let communicationDetails = []
          let client = this.params.client || {}
          let room = this.room

        for(var key in data) {
            if(key.indexOf('communicationDetails') !== -1) {
                communicationDetails.push(`${room[parseInt(key)].design_room_name}：${data[key]}`)
                delete data[key]
            }
        }
        data.revise_opinion = communicationDetails.join('；')
        data.client_number = client.client_number
        return data
      },
      request(e, param) { 
          this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
          this.$root.eventHandle.$emit('saveCustomCommunication', {res:e, param})
      }
    }

}
</script>

<style lang="stylus" scoped>
.btns {
    text-align: center;
    margin-top: 20px;
    &>span {
        padding-left: 10px;
        color: #aaa;
    }
}
</style>
