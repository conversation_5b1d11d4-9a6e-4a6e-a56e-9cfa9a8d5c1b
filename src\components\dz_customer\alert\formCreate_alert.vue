<template>
<!-- 通用表单弹窗 -->
    <form-create 
        ref="formCreate"
        :formData="comData.formData" 
        :ruleData="ruleData"
        :height="comData.height"
        @save="save"
        :labelWidth="comData.labelWidth"
    ></form-create>
</template>
<script>
import formCreate from '../components/formCreate/formCreate'
export default {
    components:{formCreate},
    data() {
        return {
            ruleData: {}
        }
    },
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    computed: {
        comData() {
            return {
                formData: this.params.formData || [],
                labelWidth: this.params.labelWidth || '100px',
                height: this.params.height || 'auto'
            }
        }
    },
    watch: {
        params: {
            immediate: true,
            handler(v) {
                this.ruleData = this.params.ruleData || {}
            }
        }
    },
    methods: {
        save(data) {
            if(this.params.event && this.params.event.save) {
                this.params.event.save(data, () => {
                    this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
                })         
            }
            
        }
    }
}
</script>