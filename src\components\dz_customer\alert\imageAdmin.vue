<template>
<!-- 附件管理弹窗 -->
    <div class="imageAdmin">
        <div class="btns">
            <!-- <el-upload
                action="test"
                :http-request="uploadImage"
                :show-file-list="false"
                :multiple="true"
                >
                <el-button size="small" type="primary" >点击上传</el-button>
            </el-upload> -->
            <el-button type='success' size='mini' @click='upload' >上传附件<my-xpt-upload 
                :ifClickUpload="ifClickUpload" 
                :dataObj="uploadData" 
                :callback="uploadCallback"
                :multiple="multiple"
                :format="format"
                ></my-xpt-upload></el-button>
                <div v-if="params.remark" style="color:red; margin-top:5px">{{params.remark}}</div>
        </div>
        <el-table :data="gridData"
        :style="{height: fileHeight + ' !important'}"
        >
            <el-table-column
            type="index"
            width="50">
            </el-table-column>
            <el-table-column property="fileName" label="文件名"></el-table-column>
            <el-table-column property="fileSize" label="文件大小" width="200">
            </el-table-column>
            <el-table-column property="person" label="上传人"></el-table-column>
            <el-table-column property="updateData" label="上传时间"></el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                </template>
            </el-table-column> 
        </el-table>
        <div style="margin: 5px 0; text-align: center">
            <el-button type='primary' @click="close">确认关闭</el-button>
        </div>
        <!-- <el-dialog  size="large" title="图片管理" :visible.sync="dialogTableVisible" @close="gridData = []">
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">取 消</el-button>
            </span>
        </el-dialog> -->
       
    </div>
</template>
<script>
import myXptUpload from '../components/upload'
import fn from '@/common/Fn.js'
import {getFileList, deleteFile} from '../common/api'
export default {
    components:{
        myXptUpload
    },
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        }
    },

    computed: {
        format() {
            return this.params.format || ""
        },
        multiple() {
            return this.params.multiple || true
        },
        fileHeight() {
            return this.params.height || 'auto'
        }
        
    },
    data() {
        return {
            gridData: [],
            dialogTableVisible: false,
            ifClickUpload:false,
            uploadData:{}
        }
    },
    created() {
        // this.params.initData && (this.gridData = this.params.initData)
        if(!this.params.justClick) {
            this.getFileList()
        }
        this.userInfo = JSON.parse(sessionStorage.getItem('userInfo')).user
    },
    methods: {
        close() {
            console.log('removeAlert')
            this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
        },
        async getFileList() {
            let list = await getFileList({
                order_no: this.params.no.parent_no,
                sub_order_no: this.params.no.child_no
            })
            this.params.no.fileListFilter && (list = this.params.no.fileListFilter(list))
            this.gridData = list.map(item => {
                return {
                    cloud_file_id: item.cloud_file_id,
                    file_type: item.file_type,
                    fileName: item.name,
                    fileSize: item.size/1000 + 'k',
                    person: item.creator_nick,
                    updateData: fn.dateFormat(item.create_time, 'yy-MM-dd'),
                    url: item.path
                }
            })
             this.change()
        },
        uploadCallback(file, res) {
            // 上传成功
            this.getFileList()
            this.updateSuccess(file)
        },
        updateSuccess(file) {
            this.params.no.emit && this.$root.eventHandle.$emit(this.params.no.emit, file)
        },
        upload() {
			this.ifClickUpload = true
            this.uploadData = Object.assign({}, this.params.no || {})
            delete this.uploadData.emit
	      	setTimeout(() => {
	        	this.ifClickUpload = false
            },100)
		},
        handleDelete(index, row){
            this.$confirm('删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteFile({list_cloud_file_id: [row.cloud_file_id]}).then(res => {
                    this.gridData.splice(index, 1)
                    this.change()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                    this.updateSuccess()
                })
            })
            
        },
        
       
        
        change(){
             this.params.callback && (this.params.callback(Array.isArray(this.gridData) ? this.gridData.length ? this.gridData : null : null))
             this.$emit('change', this.gridData)
        },
        show(imgs) {
            imgs && (this.gridData = imgs)
            this.dialogTableVisible = true
        }
    }
}
</script>
<style scoped>
 .btns {
    text-align: left;
    margin: 10px 0;
}

</style>
<style >
.imageAdmin .el-table__body-wrapper {
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
