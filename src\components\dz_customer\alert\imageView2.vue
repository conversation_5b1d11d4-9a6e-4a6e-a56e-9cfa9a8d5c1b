<template>
<!-- 附件查看弹窗 -->
    <div style="padding-top:10px">
        <el-row>
            <el-col :span="24">
                <div class="title_view">
                    <div class="title-item">
                        <h2>其他</h2>
                        <span>(共{{imgs.length}}张图)</span>
                    </div>
                </div>
                <el-carousel 
                    @change="carouselChange" 
                    ref="carousel" 
                    :key="carouselKey"  
                    trigger="click" 
                    :height="carouselHeight"
                    :initial-index="initialIndex"
                    :autoplay="false"
                    arrow="never"
                    >
                    <el-carousel-item v-for="(item, index) in imgs" :key="index">
                        <img class="carousel-img" :src="item" :style="{width: item.width || 'auto', height: item.height||'auto'}"/>
                    </el-carousel-item>
                </el-carousel>
            </el-col>
            <el-col :span="16" class="right">
                
              
                <div style="    padding-left: 60%;">
                    <el-button type="primary" @click="prev">上一张</el-button>
                    <el-button type="primary" @click="next">下一张</el-button>
                    <el-button type="primary" @click="saveZip">打包下载</el-button>
                  
                </div>
            </el-col>   
        </el-row> 
    </div>
</template>
<script>
import { saveAs } from 'file-saver'
import fn from '@/common/Fn.js'
import { getMap as getClientMap } from '../common/clientDictionary'
import {getFileList, getMeasure, deleteFile, getDesign, getOrderInfo, downloadZip} from '../common/api'
export default {
    props: {
        params: {
            type: Object
        }
    },
    data() {
        return {
            initialIndex: 0,
            dialogTableVisible: false,
            carouselKey:1,
            carouselHeight: '',
            client: '',
            designer: '',
            detail: '',
            imgs: [],
            infos:[],
            designDetail: {},
            useBy: 'measure'
        }
    },
    created() {

        this.useBy = this.params.useBy || 'measure'
        this.infos = []
        this.show(this.params)
        this.carouselHeight = innerHeight*0.8 + 'px'
    },
    methods: {
        del() {
            /**
             * @description: 删除文件
             * @param {*}
             * @return {*}
             */
            const item = this.list[this.carouselIndex]
            item && this.$confirm("是否删除该文件？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                deleteFile({list_cloud_file_id: [item.cloud_file_id]}).then(res => {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                     const isLast = this.imgs.length - 1 === this.carouselIndex
                    this.imgs.splice(this.carouselIndex, 1)
                    this.initialIndex = isLast ? this.carouselIndex > 0  ? this.carouselIndex - 1 : 0 : this.carouselIndex
                })
            })
                
        },
        imageLoaded(e, item){
            console.log(e)
        },
        async getFileList() {
            const list = await getFileList({
                order_no: this.params.no.parent_no,
                sub_order_no: this.params.no.child_no
            })
            this.list = list
            this.imgs = list.map(item => {
                return this.isImage(item.file_type) ? item.path : 'http://lsmy-devimage.oss-cn-shenzhen.aliyuncs.com/image/2020-05-298d67421c99974a44a535ae9431b1af34.jpg'
            })
        },
        //判断是否为图片
        isImage(str) {
            var strFilter="jpeg|gif|jpg|png|bmp|pic|"
            return strFilter.indexOf(str) !== -1
        },
        prev() {
            if( this.$refs.carousel && this.carouselIndex > 0) {
                this.$refs.carousel.prev()
            } else {
                this.$message({
                    message: '已经是第一张了',
                    type: 'warning'
                })
            }
              
        },
        next() {
            if( this.$refs.carousel && this.carouselIndex < this.imgs.length - 1) {
                this.$refs.carousel.next()
            } else {
                this.$message({
                    message: '已经是最后一张了',
                    type: 'warning'
                })
            }
              
        },
        async getMeasure(){
            const measure = await getMeasure({client_number: this.params.client_number})
            let arr = [
                 {prop: 'life_stage_cn',label: '生活阶段',  span: 8},
                 {prop: 'budget_cn',label: '总体预算', span: 8},
                 {prop: 'house_type_cn',label: '房屋类型', span: 8},
                 {prop: 'decoration_style_cn',label: '装修风格', span: 8},
                 {prop: 'decoration_stage_cn',label: '装修阶段',  span: 8},
                 {prop: 'client_type',label: '客户类型',  span: 8},
                 {prop: 'member_cn',label: '家庭成员', span: 8}
            ]
            getClientMap(map => {
                arr.forEach(item => {
                    if(item.prop === 'client_type') {
                        let i = map.client_type.findIndex(el => el.value == this.clientInfo.client_type)
                        item.value = i !== -1  ? map.client_type[i].label : ''
                    } else {
                        item.value = measure[item.prop]
                    }
                })
                this.infos = arr
            })
            
            
        },
        async getDesign(){
            const design = await getDesign({client_number: this.params.client_number})
            this.designDetail = design
            this.designDetail.reserve_come_time && (this.designDetail.reserve_come_time = fn.dateFormat(this.designDetail.reserve_come_time, 'yyyy-MM-dd'))
        },
        async getClientInfo(){
            const clientInfo = await getOrderInfo(this.params.client_number)
            this.clientInfo = clientInfo
            this.client = `${clientInfo.client_name}(${clientInfo.client_mobile})` || '--'
            this.designer = clientInfo.designer_name || '--'
            this.getMeasure()
        },
        show(datas = {}) {
            datas = datas
            // 获取图片
            this.getFileList()
            // 获取详情
            // this.getClientInfo()
            // if(this.params.useBy === 'design') {
            //     this.getDesign()
            // }
            this.detail = datas.detail || '---'
            this.infos = datas.infos || []
            this.carouselKey = Math.random()
        },
        carouselChange(e) {
            this.carouselIndex = e
        },
        saveImg(){
            let name = new Date().getTime()
            let img = this.imgs[this.carouselIndex]
            if(!img) {
                this.$message({
                    message: '没有可下载的附件',
                    type: 'warning'
                })
                return
            }
            saveAs(img, name);
        },
        async saveZip(){
            if(!this.list.length) {
                this.$message({
                    message: '没有可下载的附件',
                    type: 'warning'
                })
                return
            }

            const res = await downloadZip(this.list)
        }
    }
}
</script>
<style scoped>
.el-row {
    text-align: left;
}
.title_view {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}
.title_view .title-item h2,
.title_view .title-item h4 {
    margin: 0 4px 0 0;
    display:inline-block;
    font-weight: bold;
}
.right {
    padding-left:20px;
}
.section {
    
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}
.section-title {
    margin: 0 0 15px 0;
    font-weight: bold;
    font-size: 16px !important;
}
.item-title {
    font-weight: bold;
    margin-right: 5px;
}
.item-col {
    margin-bottom: 6px;
}
.el-button {
    margin-left:0 !important;
    margin: 0 10px 10px 0;
}
.carousel-img {
    max-width: 100%;
    position: absolute;
    top: 50%;
    left:50%;
    transform: translate(-50%, -50%);
}
.design>div>div {
    display: inline-block;
}
</style>
