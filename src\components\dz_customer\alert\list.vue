<template>
  <!-- 通用列表选择弹窗 -->
  <div class="list_select">
    <div :style="queryItems.length ? 'margin-top:10px;' : 'margin: 10px 0;'">
      <el-button
            type='primary'
            size='mini'
            @click.stop='confirm'
          >确认
          </el-button>
          <el-button
            v-if="params.cust_id"
            type='primary'
            size='mini'
            @click.stop='add_address("add");'
          >新增地址
          </el-button>
          <!-- <el-button
            type='primary'
            size='mini'
            @click.stop='add_address("copy_add");'
          >复制新增新增
          </el-button> -->
    </div>
    <form-create
      ref="formCreate"
      v-if="queryItems.length"
      :formData="queryItems"
      :labelWidth="labelWidth"
      savetitle="查询"
      :btns="false"
      @save="query"
    ></form-create>
    <div v-if="btns.length" class="el-tabs" style="overflow:hidden;">
      <xpt-headbar>
        <template slot="left" >
          <el-button
            v-for='(btn,index) in btns'
            :key="index"
            :type='btn.type || "primary"'
            size='mini'
            @click.stop='e => btn.click&&btn.click(e)'
            :disabled="
              typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled
              ||btn.loading
              ||false
            "
            :loading="btn.loading||false"
          >{{btn.txt}}
          </el-button>
        </template>
      </xpt-headbar>
    </div>
    <my-table
      class="list_select_table"
      ref="table"
      :showHead="false"
      :tableUrl="tableUrl"
      :tableParam="tableParam"
      tableHeight="0px"
      :colData="colData"
      :orderNo="true"
      :isInit="isInit"
      :list_prop="params.list_prop"
      :page="params.page === undefined ? true : params.page"
      @checkboxChange="checkboxChange"
      @radioChange="radioChange"
      @completed="completed"
      :selection="multiple ? 'checkbox' : 'radio'"
    ></my-table>
  </div>
</template>
<script>
import myTable from "../components/table/table";
import formCreate from "../components/formCreate/formCreate";
export default {
  components: {
    myTable,
    formCreate,
  },
  data() {
    return {
      tableParam: {},
      btns: [],
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  computed: {
    
    isInit() {
      return this.params.isInit === false ? false : true
    },
    multiple() {
      return this.params.multiple;
    },
    labelWidth() {
      return this.params.labelWidth || "100px";
    },
    queryItems() {
      return this.params.queryItems || [];
    },
    tableHeight() {
      return this.params.tableHeight || this.params.page === false
        ? "100%"
        : "458px";
    },
    colData() {
      return this.params.colData || [];
    },
    tableUrl() {
      return this.params.tableUrl || [];
    },
    initParam() {
      return this.params.initParam || {};
    },
  },
  created() {
    // 初始化按钮
      console.log(this.params)

    this.initBtns()
    if (typeof this.initParam === "string") {
      this.tableParam = this.initParam;
    } else {
      Object.assign(this.tableParam, this.initParam);
    }
  },
  methods: {
    // 新增地址
        add_address(option) {
            var _this = this;
            if (option == "add") {
                _this.alert();
            } else if (option == "copy_add") {
                if (_this.result == null) {
                    _this.$message.error("没有选择任何数据，请先选择数据！");
                } else {
                    _this.alert(_this.result);
                }
            }
        },
        // 地址弹窗
        // alert(obj) {
        //     var self = this;
        //     console.log(obj);
        //     let params;
        //     if (obj) {
        //         params.addressObj = obj;
        //         params.ifCopy = true;
        //     } else {
        //         params.ifCopy = false;
        //     }
        //     params.ifCallback = true;
        //     params.callback = d => {
        //         let newAddressObj = {};
        //         for (let [key, value] of Object.entries(d)) {
        //             newAddressObj[key] = value;
        //         }
                
        //         if(/^(DEALER|LOGISTICSCOMPANY)$/.test(self.submit.member_type)){
        //             self.addDealerReceriverInfo(newAddressObj);
        //         }else{
        //             Object.assign(newAddressObj, {
        //                 fid: "",
        //                 number: "",
        //                 tempId: +new Date()
        //             });
        //             self.addressList.push(newAddressObj);
        //         }
        //     };
        //     self.$root.eventHandle.$emit("alert", {
        //         params: params,
        //         component: () => import("@components/customers/addAddress.vue"),
        //         close: function() {},
        //         style: "width:1100px;height:250px",
        //         title: "新增地址"
        //     });
        // },
        alert(obj){
        let buyerId = this.params.cust_id;
        
        var self = this;
        var params = {};
        params.ifCopy = false;
        params.cust_id = buyerId
        params.ifCallback = false;
        params.callback = (d)=>{
				var data = d;
				//填充地址

				/*this.fillAddressAndChangeFee(data);*/
				// console.log(data)
				// this.fillAddressOfPop(data);
				//运费
				// this.reCalcFee && this.reCalcFee();
				//添加addressId
				// this.isNeedAddressId && this.isNeedAddressId(data);
				// this.encryption_add_id = data.fid;
        self.query()
			}
			self.$root.eventHandle.$emit('alert', { 
				params:params,
				component: () => import('../supplement/addAddress.vue'),
				close:function(){},
				style:'width:1100px;height:280px',
				title:'新增地址'
			});
		},
    completed() {
      // 列表数据接口请求成功回调
      this.params.completed && this.params.completed(this.btns)
    },
    initBtns() {
      const self = this
      // 初始化工具按钮
      let btns = Array.isArray(this.params.btns) ? this.params.btns.map(item => {
        let newClick = item.click
        item.click = () => {
          newClick && newClick(this.result, this, this.btns, this.$refs.table)
        }
        return item
      }) : []
      
      this.btns = btns.length ? btns : []
      this.btns = this.btns.concat(this.queryItems.length ? [
        {
          type: 'primary',
          txt: '查询',
          click() {
            self.$refs.formCreate.save()
          }
        },
        {
          type: 'danger',
          txt: '重置',
          click() {
            self.$refs.formCreate.resetFields()
          }
        }
      ] : [])
    },
    query(param) {
      console.log(this.params)
      Object.assign(this.tableParam, param);
      if(!!this.params.shop_code){
      Object.assign(this.tableParam, {shop_code:this.params.shop_code});
      }

      this.$refs.table.initData();
    },
    confirm() {
      if (this.params.check !== false && (!this.result || (this.multiple && !this.result.length))) {
        this.$message.warning("当前无选择项");
        return;
      }
      this.params.cb && this.params.cb(this.result, Object.assign({}, this.tableParam, this.$refs.formCreate ? this.$refs.formCreate.model() : {}));
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    checkboxChange(rows) {
      this.result = rows;
    },
    radioChange(result) {
      this.result = result;
    },
  },
};
</script>
<style  scoped>
.list_select {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.list_select_table {
  flex: 1;
  height:0;
}
.searchHeader {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}
.searchHeader .header-right {
  flex: 1;
}
.searchHeader .header-left {
  padding-top: 13px;
}
</style>
