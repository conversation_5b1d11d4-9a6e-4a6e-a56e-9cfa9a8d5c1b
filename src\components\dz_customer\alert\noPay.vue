<template>
<!-- 合同阶段-提示未付款弹窗 -->
  <div>
    <p class="tips">{{ params.tip }}</p>
    <div slot="footer" class="btns">
      <el-button type="primary" @click="yes">确定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    yes() {
      this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
    }
  },
  props: {
      params: {
          type: Object,
          default() {
              return {}
          }
      }
  }
}
</script>

<style lang="stylus" scoped>
.tips {
  text-align: center;
  margin-top: 40px;
}
.btns {
    text-align: center;
    margin-top: 30px;
    &>span {
        padding-left: 10px;
        color: #aaa;
    }
}
</style>
