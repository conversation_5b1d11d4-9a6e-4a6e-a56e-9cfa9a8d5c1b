<!-- 报价弹窗 -->
<template>
  <div class="xpt-flex">
    <xpt-headbar>
      <el-button type="success" size="mini" @click="getList()" slot="left"
        >刷新</el-button
      >
      <el-button
        type="primary"
        size="mini"
        @click="save()"
        slot="left"
        >保存</el-button
      >
      
    </xpt-headbar>
    <div v-if="trade_type !== 'ORIGINAL'">
      <span style="color:red;    display: inline-block;margin: 10px;">{{remark.split('{}')[0]}}{{wooden_support_fee}}{{remark.split('{}')[1]}}</span>
    </div>
    <xpt-list
      :data="dataList"
      :colData="cols"
      :pageTotal="pageTotal"
      :selection="false"
      :showHead="showHead"
      :pageSizes="[10, 20, 50, 100, 200, 1000]"
      :isNeedClickEvent="showHead"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    ></xpt-list>
  </div>
</template>
<script>
export default {
  data() {
    let self = this;
    return {
      cols: [],
      wooden_support_fee:'',
      remark:'',
      search: {
        page: {
          length: self.pageSize,
          pageNo: 1,
        },
      },
      dataList: [],
      selectData: "",
      pageTotal: 0,
      showHead: false,
      refreshBtnStatus: false,
      trade_type: "ORIGINAL",
    };
  },
  props: ["params"],
  methods: {
    close() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    pageSizeChange(pageSize) {
      this.search.page.length = pageSize;
      this.selectData = null;
      this.getList();
    },
    pageChange(page) {
      this.search.page.pageNo = page;
      this.selectData = null;
      this.getList();
    },
    save() {
      let flag = false;
      this.dataList.forEach(item=>{
        if(item.quotation_remark&&item.quotation_remark.length >30){
          flag = true;
          
        }
      })
      if(flag){
        this.$message.error('存在备注长度大于30，请修改')
        return;
      }
      this.ajax.postStream(
        "/custom-web/api/customSwj/saveSwjQuotation",
        this.dataList,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            this.getList();
            // this.close();
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
        },
        (err) => {
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
    getList(resolve) {
      this.refreshBtnStatus = true;
      let params = {
        quotation_type: this.params.quotation_type,
        swj_goods_id: this.params.swj_goods_id,
        ...this.search,
      };
      this.ajax.postStream(
        "/custom-web/api/customSwj/listSwjQuotation",
        params,
        (d) => {
          if (d.body.result && d.body.content) {
            this.pageTotal = d.body.content.count;
            this.dataList = d.body.content.list || [];
            if(this.dataList.length != 0){
              this.wooden_support_fee = this.dataList[0].wooden_support_fee;
            }
            this.$message.success(d.body.msg || "");
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
  },
  mounted() {
    this.getList();
     __AUX.get('CUSTOM_CONFIG').forEach( item => {
              if(item.code == 'WOODEN_SUPPORT_FEE_DESC' ){
                  this.remark = item.remark
              }
          });
  },
  created() {
    const { trade_type } = this.params;
    this.trade_type = trade_type;
       
    if(this.params.quotation_type == 'RETAIL_PRICE' && trade_type != "ORIGINAL"){
      this.cols = [
      {
        label: "单元名称",
        prop: "myUnitName",
        width: "120",
      },
      {
        label: "部件的名称",
        prop: "name",
        width: "100",
      },
      {
        label: "备注",
        prop: "quotation_remark",
        width: "100",
        // isInput: true,
        // bool: true,
        isInput: trade_type != "ORIGINAL" ? true : false,
        bool: trade_type != "ORIGINAL" ? true : false,
      },
      {
        label: "部件所属的报价类型",
        prop: "quoteTypeCode",
        width: "120",
      },
      {
        label: "部件编号",
        prop: "partNumber",
        width: "70",
      },
      {
        label: "深(mm)",
        prop: "depth",
        width: "70",
      },

      {
        label: "高(mm)",
        prop: "height",
        width: "70",
      },
      {
        label: "宽(mm)",
        prop: "width",
        width: "70",
      },
      {
        label: "颜色",
        prop: "colorCode",
        width: "100",
      },
      {
        label: "基材",
        prop: "baseCode",
        width: "70",
      },
      {
        label: "单价",
        prop: "priceUnit",
        width: "70",
      },
      {
        label: "用量",
        prop: "consumption",
        width: "70",
      },
       {
        label: "包装系数",
        prop: "wooden_support_fee",
        width: "100",
      },
        {
        label: "特殊加价",
        prop: "specialUpPrice",
        width: "100",
      },
      {
        label: "金额",
        prop: "amountMoney",
        width: "100",
        isInput: true,
        bool: true,
        // isInput: trade_type == "ORIGINAL" ? true : false,
        // bool: trade_type == "ORIGINAL" ? true : false,
      },
     
    
      
      {
        label: "单位",
        prop: "unit",
        width: "100",
      },
      
      {
        label: "组合名称",
        prop: "myGroupName",
        width: "120",
      },
    ];
    }else if( trade_type != "ORIGINAL"){
      this.cols = [
      {
        label: "单元名称",
        prop: "myUnitName",
        width: "120",
      },
      {
        label: "部件的名称",
        prop: "name",
        width: "100",
      },
      {
        label: "部件所属的报价类型",
        prop: "quoteTypeCode",
        width: "120",
      },
      {
        label: "部件编号",
        prop: "partNumber",
        width: "70",
      },
      {
        label: "深(mm)",
        prop: "depth",
        width: "70",
      },

      {
        label: "高(mm)",
        prop: "height",
        width: "70",
      },
      {
        label: "宽(mm)",
        prop: "width",
        width: "70",
      },
      {
        label: "颜色",
        prop: "colorCode",
        width: "100",
      },
      {
        label: "基材",
        prop: "baseCode",
        width: "70",
      },
      {
        label: "单价",
        prop: "priceUnit",
        width: "70",
      },
      {
        label: "用量",
        prop: "consumption",
        width: "70",
      },
       {
        label: "包装系数",
        prop: "wooden_support_fee",
        width: "100",
      },
        {
        label: "特殊加价",
        prop: "specialUpPrice",
        width: "100",
      },
      {
        label: "金额",
        prop: "amountMoney",
        width: "100",
        isInput: true,
        bool: true,
        // isInput: trade_type == "ORIGINAL" ? true : false,
        // bool: trade_type == "ORIGINAL" ? true : false,
      },
     
    
      
      {
        label: "单位",
        prop: "unit",
        width: "100",
      },
      
      {
        label: "组合名称",
        prop: "myGroupName",
        width: "120",
      },
    ];
    }else{
      this.cols = [
      {
        label: "单元名称",
        prop: "myUnitName",
        width: "120",
      },
      {
        label: "部件的名称",
        prop: "name",
        width: "100",
      },
      {
        label: "部件所属的报价类型",
        prop: "quoteTypeCode",
        width: "120",
      },
      {
        label: "部件编号",
        prop: "partNumber",
        width: "70",
      },
      {
        label: "深(mm)",
        prop: "depth",
        width: "70",
      },

      {
        label: "高(mm)",
        prop: "height",
        width: "70",
      },
      {
        label: "宽(mm)",
        prop: "width",
        width: "70",
      },
      {
        label: "颜色",
        prop: "colorCode",
        width: "100",
      },
      {
        label: "基材",
        prop: "baseCode",
        width: "70",
      },
      {
        label: "单价",
        prop: "priceUnit",
        width: "70",
      },
      {
        label: "用量",
        prop: "consumption",
        width: "70",
      },
      //  {
      //   label: "包装系数",
      //   prop: "wooden_support_fee",
      //   width: "100",
      // },
        {
        label: "特殊加价",
        prop: "specialUpPrice",
        width: "100",
      },
      {
        label: "金额",
        prop: "amountMoney",
        width: "100",
        isInput: true,
        bool: true,
        // isInput: trade_type == "ORIGINAL" ? true : false,
        // bool: trade_type == "ORIGINAL" ? true : false,
      },
     
    
      
      {
        label: "单位",
        prop: "unit",
        width: "100",
      },
      
      {
        label: "组合名称",
        prop: "myGroupName",
        width: "120",
      },
    ];
    }
    
  },
};
</script>
