<template>
<!-- 支付记录列表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
        <my-table 
        :btns='btns'
        ref="table"
        tableUrl='/custom-web/api/customSwj/listSwjQuotation'
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :isInit="false"
        ></my-table>
    </div>
  </div>
</template>
<script>
import myTable from '../components/table/table'
import {sync_status, getMap} from '../common/tollDictionary'
import Vue from 'vue'
export default {
    components: {
        myTable
    },
    data() {
        let self = this;
        return {
            queryItems: [],
            tableParam:{},
            colData: [],
            info: {},
            btns:[
              {
                type: "primary",
                txt: "导出",
                click: () => {
                  // 刷新表格
                  this.exportList();
                },
              },
              {
                type: "primary",
                txt: "导出结果",
                click: () => {
                  this.exportRelatedResult();
                },
              },
            ],
            isJxbc:false
        }
    },
    props:{
        params: {
            type: Object
        }
    },
    
    mounted(){
        this.getColData();
        console.log(this.params)
        this.tableParam = this.params.initObj
        this.$refs.table.initData()
    },
    
    methods: {
      exportRelatedResult() {
      this.$root.eventHandle.$emit("alert", {
        style: "width:900px;height:600px",
        title: "导出结果",
        params: {
          url: "/custom-web/api/customSwj/swjQuotation/exportList",
          data: {
          },
        },
        component: () => import("@components/common/exout"),
      });
    },
       exportList(){
          let params = JSON.parse(JSON.stringify(this.tableParam));
          params.page = {
            length:20,
            pageNo:1
          }
           this.ajax.postStream('/custom-web/api/customSwj/swjQuotation/export', params, res => {
                if(!res.body.result) {
                this.$message.error(res.body.msg);
                }else{
                this.$message.success(res.body.msg);
                }
                


            }, err => {
                this.$message.error(err);
            });
            
       },
        getColData(){
            this.colData =  Object.freeze([
        {
          label: '单元名称',
          prop: 'myUnitName',
          width: '120',
        },
        {
          label: '部件的名称',
          prop: 'name',
          width: '100',
        },
        {
          label: '部件所属的报价类型',
          prop: 'quoteTypeCode',
          width: '120',
        },
        {
          label: '部件编号',
          prop: 'partNumber',
          width: '70',
        },
        {
          label: '深(mm)',
          prop: 'depth',
          width: '70',
        },
        
        {
          label: '高(mm)',
          prop: 'height',
          width: '70',
        },
        {
          label: '宽(mm)',
          prop: 'width',
          width: '70',
        },
        {
          label: '颜色',
          prop: 'colorCode',
          width: '100',
        },
        {
          label: '基材',
          prop: 'baseCode',
          width: '70',
        },
        {
          label: '用量',
          prop: 'consumption',
          width: '70',
        },
        {
          label: '金额',
          prop: 'amountMoney',
          width: '100',
        },
        {
          label: '特殊加价',
          prop: 'specialUpPrice',
          width: '100',
        },
        {
          label: '单价',
          prop: 'priceUnit',
          width: '70',
        },
        {
          label: '单位',
          prop: 'unit',
          width: '100',
        },
        {
          label: '组合名称',
          prop: 'myGroupName',
          width: '120',
        }
      ])
        },
        
        
        query(data) {
            let param = JSON.parse(JSON.stringify(data));
            Object.assign(this.tableParam, param)
            this.$refs.table.initData()
        },
       
    }
}
</script>
