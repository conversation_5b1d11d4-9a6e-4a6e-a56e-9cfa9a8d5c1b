<template >
<!-- 录入采购价格 -->
  <div class="purchasePrice">
    <el-table
      :data="value"
      size="small"
      border
      width="100%"
      show-summary
      :summary-method="summaryMethod"
      :row-style="rowStyle"
    >
      <el-table-column
        label="商品号"
        prop="goods_id"
        width="130">
      </el-table-column>
      <el-table-column
        label="商品信息"
        prop="message"
        width="200">
      </el-table-column>
      <el-table-column
        label="颜色"
        prop="color_cn"
        width="100">
      </el-table-column>
      <el-table-column
        label="商品状态"
        prop="goods_status_cn"
        width="100">
      </el-table-column>
      <el-table-column
        label="所在空间"
        prop="design_room_name"
        width="100">
      </el-table-column>
      <el-table-column
        label="采购价格"
        width="220"
        >
        <template slot-scope="scope">
          <span>{{scope.row.purchase_price}}元</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

export default {
 
  data () {
    return {
      value: [],
    }
  },
  props: {
    params: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  methods:{
    summaryMethod(param) {
        const { columns, data } = param;
        const sums = [];
        let retail_priceIndex
        columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计';
            return;
        }
        if(column.property === 'purchase_price') {
              column.property === 'purchase_price' && (retail_priceIndex=index)
              const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr);
                if (!isNaN(value)) {
                    return prev + curr;
                } else {
                    return prev;
                }
                }, 0);
            } else {
                sums[index] = 'N/A';
            }
        }
        
        });
        sums[retail_priceIndex] = `总采购价：${parseFloat(sums[retail_priceIndex]).toFixed(2)}元`
        return sums;
    },
    rowStyle() {
      return "height: 28px; line-height: 28px;"
    },
    save() {
      let length = this.value.length
      let value = this.value
      var isPrice = new RegExp(/((^[1-9]\d*)|^0)(\.\d{1,2}){0,1}$/)
      for(let i=0; i<length; i++) {
        if(!isPrice.test(value[i].purchase_price)) {
          this.$message({
            message: value[i].message + '采购价格格式不正确',
            type:'error'
          })
          return
        }
      }
      
      this.ajax.postStream('/custom-web/api/customGoods/savePrice', value.map(item => {
        return {
          goods_id: item.goods_id,
          client_number: item.client_number,
          purchase_price: item.purchase_price
        }
        }), (res) => {
        if(res.body.result) {
          this.$message({
            message: res.body.msg,
            type: 'success'
          })
          this.$root.eventHandle.$emit('refreshpushDown')
          this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
        } else {
          this.$message({
            message: res.body.msg,
            type: 'error'
          })
        }
      }, err => {
        this.$message.error(err)
      })
    },
    getGoodsList() {
      let data = {}
      Object.assign(data, this.params.initValue)
      this.ajax.postStream('/custom-web/api/customGoods/getList', data, (res) => {
        if(res.body.result) {
          let content = res.body.content || {}
          this.value = Array.isArray(content) ? content : content.list || []
        } else {
          this.$message({
            message: res.body.msg,
            type: 'error'
          })
        }
      }, err => {
        this.$message.error(err)
      })
    }
  },
  mounted() {
    this.getGoodsList()
  }
}
</script>

<style scoped>
  .form-btns {
    text-align: center;
    margin-top: 10px;
  }
  .purchasePrice {
    display: flex;
    flex-direction: column;
    height: 100% !important;
    margin: 5px;
  }
</style>
<style>
  .purchasePrice .el-table .el-table__body-wrapper td .cell, .el-table .el-table__fixed-body-wrapper td .cell {
    height: auto;
    line-height: 1;
  }
</style>
