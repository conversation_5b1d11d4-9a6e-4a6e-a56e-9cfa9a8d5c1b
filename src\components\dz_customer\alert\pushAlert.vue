<template>
  <!-- 下推采购阶段-驳回弹窗 -->
  <div>
    <!-- <form-create
      ref="formCreate"
      :formData="formData"
      :ruleData="ruleData"
      :btns="false"
      @ready="ready"
      @save="save"
      :labelWidth="labelWidth"
    ></form-create> -->

    <el-form  :model='pushObj' label-position="right" label-width="120px">
            <el-col :span='24'  v-for="item in pushObj" :key="item.purchase_trade_type">
              
              <el-form-item :label="item.purchase_trade_name"  >
                <!-- <el-input v-model="pushObj.BLISTER.factory_name"></el-input> -->
                <el-select  v-model="item.factory_code" size="mini" @change="pushObjChange(item.factory_code,item.purchase_trade_type)">
                  <el-option
                    v-for="(item) in dataList"
                    :key="item.factory_code"
                      :label="item.factory_name"
                      :value="item.factory_code"
                  >
                  </el-option>
              </el-select>
              </el-form-item>
            
			      </el-col>
        </el-form>
    <div slot="footer" class="btns">
      <el-button
        :disabled="request"
        :loading="request"
        type="primary"
        @click="save() "
        >保存工厂</el-button
      >
    </div>
  </div>
</template>

<script>
import formCreate from "../components/formCreate/formCreate";
import { synGoods, pushMaterial, auditNewScmMergeTrade } from "../common/api";
export default {
  components: { formCreate },
  data() {
    return {
      dataList:[],
      pushObj:{
        // "BLISTER":{
        //       "factory_code":"" ,       //工厂编码
        //       "factory_name":""       //工厂名称
        //   },
        //   "ALUMINUM":{
        //       "factory_code":"" ,       //工厂编码
        //       "factory_name":""       //工厂名称
        //   },
        // "CABINET":{
        //       "factory_code":""  ,      //工厂编码
        //       "factory_name":""       //工厂名称
        //   },
      },
      cabinetOpiton:[],
      blisterOpiton:[],
      allminumOpiton:[],
      formData: [],
      labelWidth: "70px",
      ruleData: Object.freeze({
        provider: { required: true, message: "请选择工厂", trigger: "change" },
      }),
      
      options: [
        {
          label: "(V000001)广州雅品家具科技有限公司",
          factory_name: "广州雅品家具科技有限公司",
          value: "V000001",
        },
        {
          label: "(V000520)广东省梵帝尼家具有限公司",
          factory_name: "广东省梵帝尼家具有限公司",
          value: "V000520",
        },
        {
          label: "(V000644)佛山市嘉蒂斯智造装饰材料有限公司",
          factory_name: "佛山市嘉蒂斯智造装饰材料有限公司",
          value: "V000644",
        },
      ],
      request: false,
    };
  },
  props: {
    params: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
   
    pushObjChange(val,type){
      // console.log(type)
      this.dataList.forEach(item=>{
        if(item.factory_code == val){
          this.pushObj[type].factory_name = item.factory_name
        }
      })
    },
    //列表显示
		getfactoryConfig(d, resolve){
			
			var _this = this;
			let search = {
				page_size: self.pageSize,     //页数
				page_no:1,   //页码
        page_name: 'custom_factory_config',
        where: []
			};
			this.ajax.postStream('/custom-web/api/factoryConfig/list',search,function(response){
				if(response.body.result){
					let dataList = response.body.content.list;
          dataList.forEach(item=>{
            if(item.purchase_trade_type == 'BLISTER'){
              _this.blisterOpiton.push(item)
            }
            if(item.purchase_trade_type == 'ALUMINUM'){
              _this.allminumOpiton.push(item)
            }
            if(item.purchase_trade_type == 'CABINET'){
              _this.cabinetOpiton.push(item)
            }
          })
					// _this.count = response.body.content.count;
				}
				else{
					_this.$message.error(response.body.msg)
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
    ready(set) {
      let self = this;
      this.formData = [
        {
          cols: [
            {
              formType: "listSelect",
              label: "录入",
              prop: "provider",
              config: {
                popupType: "urgentFreeList",
                initParam: { status: 1 },
              },
              event: {
                result(item) {
                  self.result = item;
                },
              },
              span: 24,
            },
          ],
        },
      ];
    },
    sys(client_number) {
      // 同步商品
      if (this.params.trade_type === "SUPPLY") return;

      synGoods(client_number).then((body) => {
        if (body.result) {
          auditNewScmMergeTrade(client_number);
        }
      });
    },
    save() {
      // 保存工厂
      let _this = this;
      // const { factory_code, supplierName } = _this.result;
      let pushObj = JSON.parse(JSON.stringify(this.pushObj))
      for(var i in pushObj){
        _this.dataList.forEach(item=>{
          if(item.factory_code == pushObj[i].factory_code){
            pushObj[i].factory_name =item.factory_name
          }
        })
        delete pushObj[i].purchase_trade_name;
      }
      let data = {
        client_number: this.params.client_number,
        factoryList: pushObj,
        // purchaseTrades: this.params.ids,
      };
      this.ajax.postStream(
        "/custom-web/api/customGoods/saveFactory",
        data,
        (res) => {
          console.log(res);
          if (res.body.result) {
            _this.$message.success(`${res.body.msg}`);
            _this.params.callback &&
              _this.params.callback({
                factoryList: this.pushObj,
              });
            _this.$root.eventHandle.$emit("removeAlert", _this.params.alertId);
            

          } else {
            _this.$message.error(`${res.body.msg}`);
          }
        }
      );
    },
    urgentFreeList(){
      
			var _this = this;
			let search = {status: 1, page: {length: 50, pageNo: 1}}
      let codeList = [];

			this.ajax.postStream('/custom-web/api/customSupplierUrgentFee/list',search,function(response){
				if(response.body.result){
					_this.dataList = [];
          console.log(response.body.content.list,11111);
          response.body.content.list.forEach(item=>{
            codeList.push(item.factory_code)
            _this.dataList.push({
              factory_name:item.supplierName,
              factory_code:item.factory_code,
            })
          })

          for(var i in _this.pushObj){
            if(!codeList.includes(_this.pushObj[i].factory_code)){
               _this.pushObj[i].factory_code = '';
                _this.pushObj[i].factory_name = '';
            }
           
          }
          // dataList.forEach(item=>{
          //   if(item.purchase_trade_type == 'BLISTER'){
          //     _this.blisterOpiton.push(item)
          //   }
          //   if(item.purchase_trade_type == 'ALUMINUM'){
          //     _this.allminumOpiton.push(item)
          //   }
          //   if(item.purchase_trade_type == 'CABINET'){
          //     _this.cabinetOpiton.push(item)
          //   }
          // })
          // _this.pushObj = _this.params.pushObj
					// _this.count = response.body.content.count;
				}
				else{
					_this.$message.error(response.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
    },
  },
  mounted(){
    this.pushObj = JSON.parse(JSON.stringify(this.params.pushObj));
    console.log(this.pushObj);
    // for(var i in this.pushObj){
    //   this.pushObj[i].factory_code = '';
    //   this.pushObj[i].factory_name = '';
    // }
    this.urgentFreeList();

  }
};
</script>

<style lang="stylus" scoped>
.btns {
  text-align: center;
  margin-top: 20px;

  &>span {
    padding-left: 10px;
    color: #aaa;
  }
}
</style>
