<!-- 报价清单 -->
<template>
  <div>
    <xpt-headbar>
      <el-button type="success" size="mini" @click="getList()" slot="left"
        >刷新</el-button
      >
    </xpt-headbar>
    <el-table height="500" class="my-table" style="width: 90%" border :data="quoteList">
      <el-table-column prop="quotation_type" label="报价类型" width="100">
        <template slot-scope="scope">
          {{
            scope.row.quotation_type == "ACT_PRICE"
              ? "实际价"
              : scope.row.quotation_type == "RETAIL_PRICE"
              ? "零售价"
              : "工厂价"
          }}
        </template>
      </el-table-column>
      <el-table-column prop="myUnitName" label="单元名称" width="150">
      </el-table-column>
      <el-table-column prop="name" label="部件的名称" width="150">
      </el-table-column>
      <el-table-column
        prop="quoteTypeCode"
        label="部件所属的报价类型"
        width="150"
      >
      </el-table-column>
      <el-table-column prop="partNumber" label="部件编号" width="120">
      </el-table-column>
      <el-table-column prop="depth" label="深(mm)" width="120">
      </el-table-column>
      <el-table-column prop="height" label="高(mm)" width="120">
      </el-table-column>
      <el-table-column prop="width" label="宽(mm)" width="120">
      </el-table-column>
      <el-table-column prop="colorCode" label="颜色" width="120">
      </el-table-column>
      <el-table-column prop="baseCode" label="基材" width="120">
      </el-table-column>
      <el-table-column prop="priceUnit" label="单价" width="120">
      </el-table-column>
      <el-table-column prop="consumption" label="用量" width="120">
      </el-table-column>
      <el-table-column prop="specialUpPrice" label="特殊加价" width="120">
      </el-table-column>
      
      <el-table-column prop="amountMoney" label="金额" width="120">
      </el-table-column>
      
      <el-table-column prop="unit" label="单位" width="120"> </el-table-column>
      <el-table-column prop="myGroupName" label="组合名称" width="120">
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  data() {
    let self = this;
    return {
      ispushEvent: false,
      quoteList: [],
      pageTotal: 0,
      showHead: false,
      refreshBtnStatus: false,
    };
  },
  props: ["params"],
  methods: {
    close() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    getList(resolve) {
      this.refreshBtnStatus = true;
      console.log(this.params.purchase_trade_no);
      let params = {
        purchase_trade_no: this.params.purchase_trade_no,
      };
      this.ajax.postStream(
        "/custom-web/api/customSwj/swjQuotationPurchaseList",
        params,
        (d) => {
          if (d.body.result && d.body.content) {
            this.quoteList = d.body.content.list || [];
            this.$message.success(d.body.msg || "");
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
  },
  mounted() {},
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.my-table{
  height:500px !important;
  overflow-y: scroll;
}
</style>
