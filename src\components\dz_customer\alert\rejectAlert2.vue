<!-- 报价弹窗 -->
<template>
  <div>
    <xpt-headbar> <el-button
        v-if="trade_type !== 'pushBOM'"
        type="primary"
        size="mini"
        :disabled="ispushEvent ||canPush"
        @click="pushEvent()"
        slot="left"
        >下推</el-button
      >
      
      <el-button
        type="primary"
        size="mini"
        @click="operaterAjax()"
        slot="left"
        v-if="trade_type !== 'pushBOM'"
        >驳回</el-button
      >
      <el-button type="success" size="mini" @click="getList()" slot="left"
        >刷新</el-button
      >
      <el-button
        type="primary"
        v-if="trade_type == 'ORIGINAL'"
        size="mini"
        @click="choose()"
        :disabled="canPush"
        slot="left"
        >选择工厂</el-button
      >
     
    </xpt-headbar>
    <div style="overflow-y: auto; height: 400px">
       <div style="margin: 10px 0">
          第一步，选择下推商品
        </div>
        <el-button
        type="primary"
        size="mini"
        @click="selectAll()"
        slot="left"
        >全选商品</el-button
      >
      <div :key="item.custom_goods_id" v-for="(item, index) in dataList">
        <div style="margin: 10px 0">
          <el-checkbox
            v-model="item.checked"
            v-if="trade_type !== 'pushBOM'"
          ></el-checkbox>
          <span class="goods-title"
            ><span class="goods-title" style="color: #20a0ff; font-weight: bold"
              >商品:</span
            >{{ item.message }}</span
          ><span  style="color: #20a0ff; font-weight: 800">{{ item.type_code_name }}</span>
          <!-- <span> -->
            <!-- <span style="margin-left: 30px">驳回理由：</span>
            <el-input
              type="textarea"
              style="width: 200px"
              placeholder="请输入内容"
              v-model="item.result"
            >
            </el-input>
          </span> -->
        </div>
        <div style="padding-left: 30px">
          <el-table
            style="width: 90%"
            class="goods-table"
            border
            :row-key="rowKey"
            :data="item.purchaseTrades"
            @select="(selection, row) => select(selection, index)"
            @select-all="(selection) => select(selection, index)"
            :cell-style="rowStyle"
          >
            <el-table-column
              type="selection"
              width="55"
              class-name="reject-alert-row"
              v-if="trade_type !== 'pushBOM'"
            >
            </el-table-column>
            <el-table-column
              prop="purchase_trade_no"
              label="采购订单"
              width="180"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="purchase_trade_type_cn"
              label="订单类别"
              width="120"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="material_number"
              label="物料编码"
              width="120"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="retail_price"
              label="零售价"
              width="120"
              class-name="reject-alert-row"
            >
              <template slot-scope="scope">
                {{ scope.row.retail_price }}
                <el-button size="mini" type="primary" @click="scan(scope.row)"
                  >查看</el-button
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="purchase_price"
              label="采购价"
              width="120"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="factory_name"
              label="下推工厂"
              width="150"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="filePath"
              label="拆单文件路径"
              width="250"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              width="160"
              class-name="reject-alert-row"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="cancelScm(scope.row)"
                  >取消scm订单</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div style="margin-top:30px;" v-show="!canPush">
      <div>第二步，选择下推工厂</div>
        <el-form  :model='pushObj' label-position="right"  style="overflow: auto; height: 150px;" label-width="120px">
            <el-col :span='24'  v-for="item in pushObj" :key="item.purchase_trade_type">
              <el-form-item :label="item.purchase_trade_name">
                <el-input v-model="item.factory_name" disabled  size="mini"></el-input>
                <span style="color:#ff4949" v-if="item.color_codes_export"><span v-if="item.color_codes_export">特殊规则：花色【{{item.color_codes_export}}】 </span>
                <span v-if="item.base_codes_export">基材【{{item.base_codes_export}}】</span>
                </span>
                <!-- <el-select  v-model="item.factory_code" size="mini" disabled>
                  <el-option
                    v-for="(item) in dataList"
                    :key="item.factory_code"
                      :label="item.factory_name"
                      :value="item.factory_code"
                  >
                  </el-option>
              </el-select> -->
              </el-form-item>
            
             
			      </el-col>
        </el-form>
      <div>

      </div>
    </div>

  </div>
</template>
<script>
import { forEach } from 'jszip';
import {
  synGoods,
  rejectPushPurchase, 
  auditNewScmMergeTrade,
  customSupplyAftersale,
  saveAfterPlan,
  submitAfterPlan,
} from "../common/api";
export default {
  data() {
    let self = this;
    return {
      cabinetOpiton:[],
      blisterOpiton:[],
      allminumOpiton:[],
      canPush:false,
      pushObj:{
        "BLISTER":{
              "factory_code":"" ,       //工厂编码
              "factory_name":""       //工厂名称
          },
          "ALUMINUM":{
              "factory_code":"" ,       //工厂编码
              "factory_name":""       //工厂名称
          },
        "CABINET":{
              "factory_code":""  ,      //工厂编码
              "factory_name":""       //工厂名称
          },
      },
      ispushEvent: false,
      dataList: [],
      selectData: "",
      pageTotal: 0,
      showHead: false,
      refreshBtnStatus: false,
      trade_type: "ORIGINAL",
      selectRows: [],
      selectGoods: [],
    };
  },
  props: ["params"],
  methods: {
    
    getDetail(callback){
				let self = this;
			
						this.ajax.postStream(
						'/custom-web/api/apartConfig/allList',
						{},
						(res) => {
						res = res.body;
						if (res.result) {
							let hasObj= {};
 
							self.purchase_trade_type_option = res.content.reduce((cur,next) => {
								hasObj[next.type] ? "" : (hasObj[next.type] = true && cur.push(next)); 
								return cur;
							},[]) 
							// console.log(self.purchase_trade_type_option);
              callback(self.purchase_trade_type_option)
						}else{
							this.$message({
							message: res.msg,
							type: "error",
							});
						}
						},
						(err) => {
						this.$message({
							message: err.msg,
							type: "error",
						});
						}
					);
						

			},
     urgentFreeList(){
      
			var _this = this;
			let search = {status: 1, page: {length: 50, pageNo: 1}}
			this.ajax.postStream('/custom-web/api/customSupplierUrgentFee/list',search,function(response){
				if(response.body.result){
					_this.dataList = [];
          response.body.content.list.forEach(item=>{
            _this.dataList.push({
              factory_name:item.supplierName,
              factory_code:item.factory_code,
            })
          })
          console.log(_this.dataList,11111);

          // dataList.forEach(item=>{
          //   if(item.purchase_trade_type == 'BLISTER'){
          //     _this.blisterOpiton.push(item)
          //   }
          //   if(item.purchase_trade_type == 'ALUMINUM'){
          //     _this.allminumOpiton.push(item)
          //   }
          //   if(item.purchase_trade_type == 'CABINET'){
          //     _this.cabinetOpiton.push(item)
          //   }
          // })
          // _this.pushObj = _this.params.pushObj
					// _this.count = response.body.content.count;
				}
				else{
					_this.$message.error(response.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
    },
    //列表显示
		getfactoryConfig(d, resolve){
			
			var _this = this;
			let search = {
				page_size: self.pageSize,     //页数
				page_no:1,   //页码
        page_name: 'custom_factory_config',
        where: []
			};
			this.ajax.postStream('/custom-web/api/factoryConfig/list',search,function(response){
				if(response.body.result){
					let dataList = response.body.content.list;
          // dataList.forEach(item=>{
          //   if(item.purchase_trade_type == 'BLISTER'){
          //     _this.blisterOpiton.push(item)
          //   }
          //   if(item.purchase_trade_type == 'ALUMINUM'){
          //     _this.allminumOpiton.push(item)
          //   }
          //   if(item.purchase_trade_type == 'CABINET'){
          //     _this.cabinetOpiton.push(item)
          //   }
          // })
					// _this.count = response.body.content.count;
				}
				else{
					_this.$message.error(response.body.msg)
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
    	operaterAjax () {
      let  self = this
        let flag = false;
        self.dataList.forEach(item=>{
          if(item.checked == true){
            flag = true;
          }
        })
        if(!flag){
          self.$message.error('请选择驳回商品');
          return;
        }
        this.$root.eventHandle.$emit('alert', {
          component: () => import('./rejectDialog.vue'),
          style: 'width:660px;height:270px',
          title: "驳回",
          params: {
            //刷新
            callback: data => {
              self.reject(data)
            }
          },
        })
      
			
    },
    selectAll(){
      let self = this;
      self.dataList.forEach(item=>{
        item.checked = true;
      })
    },
    // 取消scm订单
    cancelScm(row) {
      let params = {
        custom_purchase_id: row.custom_purchase_id,
      };
      this.ajax.postStream(
        "/custom-web/api/customPush/deleteScmTrade",
        params,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
    // 查看
    scan(d) {
      const { purchase_trade_no } = d;
      this.$root.eventHandle.$emit("alert", {
        params: {
          purchase_trade_no,
        },
        component: () => import("./quoteList"),
        style: "width:800px;height:600px",
        title: "报价清单",
      });
    },
    rowKey(row) {
      return row.custom_goods_id;
    },
    rowStyle() {
      return "height:50px;";
    },
    close() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    save() {
      this.ajax.postStream(
        "/custom-web/api/customSwj/saveSwjQuotation",
        this.dataList,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            // this.close();
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
        },
        (err) => {
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
    getList(resolve) {
      this.refreshBtnStatus = true;
      let params = {
        client_number: this.params.client_number,
      };
      this.ajax.postStream(
        "/custom-web/api/customGoods/getGoodsPurchaseTrade",
        params,
        (d) => {
          if (d.body.result && d.body.content) {
            this.dataList = this.handle(d.body.content) || [];
            this.$message.success(d.body.msg || "");
            // 初始化勾选数组的长度
            this.selectRows = this.selectRowsInit(d.body.content);
            console.log(this.selectRows);
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
    getfactory(list) {
      let self = this;
      let params = {
        client_number: this.params.client_number,
      };
      this.ajax.postStream(
        "/custom-web/api/factoryConfig/getConfig",
        params,
        (d) => {
          if (d.body.result && d.body.content) {
            self.pushObj = d.body.content;
            list.forEach(item=>{
              for(let i in d.body.content){
                if(i == item.apart_number){
                  self.pushObj[i].purchase_trade_name = item.type
                  self.pushObj[i].purchase_trade_type = i
                }
              }
            })
            
            // if(!!d.body.content.ALUMINUM){
            //   self.pushObj.ALUMINUM = d.body.content.ALUMINUM
            // }
            // if(!!d.body.content.BLISTER){
            //   self.pushObj.BLISTER = d.body.content.BLISTER
            // }
            // if(!!d.body.content.CABINET){
            //   self.pushObj.CABINET = d.body.content.CABINET
            // }
            self.$message.success(d.body.msg || "");
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
        }
      );
    },
    // 列表数据初始化
    handle(data) {
      return data.map((item) => {
        return {
          ...item,
          checked: false,
          result: "",
        };
      });
    },
    selectRowsInit(list) {
      if (!!list.length) {
        return new Array(list.length).fill([]);
      }
      return [];
    },
    select(rows, index) {
      this.selectRows[index] = rows;
    },
    // 下推前的校验
    pushBefore() {
      if (!this.dataList.length) {
        return false;
      }
      let next = true;
      this.dataList.forEach((elem) => {
        if (!elem.purchaseTrades || !elem.purchaseTrades.length) {
          next = false;
        }
      });
      if (!next) {
        this.$message.error(`无法进行下推操作，没有相应的采购订单！`);
        return false;
      }
      let arr = []
        .concat(
          this.dataList.map((item) => {
            return item.purchaseTrades;
          })
        )
        .flat()
        .filter((item) => !item.factory_name);
      if (arr.length > 0) {
        this.$message.error(
          `无法进行下推操作，请先完善采购订单下的下推工厂信息！`
        );
        return false;
      }
      return true;
    },
    // 选择工厂
    choose() {
      let self = this;
      // let arr = []
      //   .concat(this.selectRows)
      //   .flat()
      //   .filter((item) => !!item);
      // if (!arr.length) {
      //   this.$message.warning("请先选择采购订单！");
      //   return;
      // }
      let params = {
        client_number: this.params.client_number,
        pushObj:self.pushObj,
        // ids: arr.map((item) => {
        //   return {
        //     custom_purchase_id: item.custom_purchase_id,
        //   };
        // }),
        callback: (result) => {
          const { factory_code, supplierName } = result;
          self.getList();
          self.pushObj = result.factoryList ;
        },
      };
      self.$root.eventHandle.$emit("alert", {
        params: params,
        component: () => import("./pushAlert"),
        style: "width:538px;height:450px",
        title: "下推",
      });
    },
    // 下推
    pushEvent() {
      let self = this;
      this.ispushEvent = true;
      if (this.params.trade_type === "SUPPLY") {
        // 补单下推直接下推不需要选工厂
        if (self.request) {
          self.$message({
            type: "warning",
            message: "请等待上一次请求结束",
          });
          return;
        }
        self.request = true;
        // 推物料
        // pushMaterial(d.client_number).then((res) => {});
        // 拆单下推同步补单流程
        customSupplyAftersale(this.params.custom_trade_id).then((body) => {
          if (body.result) {
            saveAfterPlan(this.params.custom_trade_id).then((body) => {
              if (body.result) {
                submitAfterPlan(this.params.custom_trade_id).then((body) => {
                  self.$message({
                    message: body.msg || "",
                    type: body.result ? "success" : "error",
                  });
                  self.request = false;
                });
              } else {
                self.ispushEvent = false;
                self.request = false;
                self.$message({
                  message: body.msg,
                  type: body.result ? "success" : "error",
                });
              }
            });
          } else {
            self.ispushEvent = false;
            self.request = false;
            self.$message({
              message: body.msg,
              type: body.result ? "success" : "error",
            });
          }
        });
      } else {
        if (this.pushBefore()) {
          let data = { client_number: this.params.client_number };
          if (this.request) {
            this.$message({
              type: "warning",
              message: "请等待上一次请求结束",
            });
            return;
          }
          this.request = true;
          const url = "/custom-web/api/customPush/purchase";
          this.ajax.postStream(
            url,
            data,
            (res) => {
              this.request = false;
              res = res.body;
              if (res.result) {
                // 补单则同步新平台
                // 同步商品，旧版下推则不同步
                this.$message({
                  message: res.msg,
                  type: "success",
                });
              }else{
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            },
            (err) => {
              this.$message({
                message: err.msg,
                type: "error",
              });
              this.request = false;
            }
          );
        }
      }
    },
    // 驳回前的校验
    rejectBefore() {
      if (!this.dataList.length) {
        return false;
      }
      if (!this.dataList.filter((item) => item.checked).length) {
        this.$message.error(`无法进行驳回操作，请先选择需要驳回的商品！`);
        return false;
      }
      // if (
      //   !this.dataList.filter((item) => item.checked && item.result !== "")
      //     .length
      // ) {
      //   this.$message.error(`无法进行驳回操作，商品请填写驳回理由！`);
      //   return false;
      // }
      return true;
    },
    // 驳回
    reject(param) {
      let self = this;
      if (this.rejectBefore()) {
        if (this.isRequest) {
          this.$message.error("请等待上一次请求结果");
        }
        let data = this.dataList
          .filter((item) => item.checked)
          .map((e) => {
            return {
              custom_goods_id: e.custom_goods_id,
              ...param
            };
          });
        this.isRequest = true;
        rejectPushPurchase(data, true, true).then((res) => {
          this.isRequest = false;
          if (res.result) {
            self.getList();
          } else {
            this.$message.error(`${res.msg}`);
          }
        });
      }
    },
    sys(client_number) {
      // 同步商品
      if (this.params.trade_type === "SUPPLY") return;
      synGoods(client_number).then((body) => {
        if (body.result) {
          auditNewScmMergeTrade(client_number);
        }
      });
    },
  },
  mounted() {
    this.canPush = ['WAITING_PUSH_GOODS','WAITING_OUTBOUND','IN_WAREHOUSE','SUCCESS_GOODS','SUCCESS_FINANCE','COMPLETED'].includes(this.params.row.client_status_value)
    console.log(this.params)
  },
  created() {
    this.trade_type = this.params.trade_type;
    this.getList();
    new Promise((resolve,reject)=>{
      this.getDetail(resolve)
    }).then(res=>{
      this.getfactory(res)
    })
    this.getfactoryConfig();
    // this.urgentFreeList();
  },
};
</script>
<style scoped>
.goods-title {
  height: 40px;
  line-height: 40px;
  color: #1f2d3d;
  font-size: 16px !important;
  z-index: 9999;
}
.goods-title:hover {
  cursor: pointer;
}
.el-checkbox {
  /* border:1px solid #ddd; */
  margin-bottom: 10px;
}
.el-checkbox .goods-table {
  margin: 4px 20px;
}
.el-checkbox + .el-checkbox {
  margin-left: 0 !important;
}
</style>
