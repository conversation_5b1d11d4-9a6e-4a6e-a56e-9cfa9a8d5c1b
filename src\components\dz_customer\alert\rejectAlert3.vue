<!-- 报价弹窗 -->
<template>
  <div>
    <xpt-headbar> <el-button
        v-if="trade_type !== 'pushBOM'"
        type="primary"
        size="mini"
        :disabled="ispushEvent ||canPush"
        @click="pushEvent()"
        slot="left"
        >下推</el-button
      >
      
      <el-button type="success" size="mini" @click="getList()" slot="left"
        >刷新</el-button
      >
      <el-button
        type="primary"
        size="mini"
        :disabled="canPush"
        @click="choose()"
        slot="left"
        >选择工厂</el-button
      >
      <el-button
        type="primary"
        size="mini"
        :disabled="canPush"
        @click="rejectAll()"
        slot="left"
        >驳回</el-button
      >
     
    </xpt-headbar>
      <div style="margin-top:30px;" v-show="!canPush">
        <el-form  :model='pushObj' label-position="right"  style="overflow: auto; height: 150px;" label-width="120px">
            <el-col :span='24'  v-for="item in pushObj" :key="item.purchase_trade_type">
              <el-form-item :label="item.purchase_trade_name">
                <el-input v-model="item.factory_name" disabled  size="mini"></el-input>
              </el-form-item>
            
             
			      </el-col>
        </el-form>
      <div>

      </div>
    </div>
    <div style="overflow-y: auto; height: 400px">
      <div :key="item.custom_goods_id" v-for="(item, index) in dataList">
        <div style="margin: 10px 0">
          
          <span class="goods-title"
            >{{ item.purchase_trade_name }}</span
          >
       
        </div>
       
        <div style="padding-left: 30px">
          <el-table
            style="width: 90%"
            class="goods-table"
            border
            :row-key="rowKey"
            :data="item.arr"
            @select="(selection, row) => select(selection, index)"
            @select-all="(selection) => select(selection, index)"
            :cell-style="rowStyle"
          >
            <el-table-column
              type="selection"
              width="55"
              class-name="reject-alert-row"
              v-if="trade_type !== 'pushBOM'"
            >
            </el-table-column>
            <el-table-column
              prop="purchase_trade_no"
              label="采购订单"
              width="180"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="purchase_trade_type_cn"
              label="订单类别"
              width="120"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="material_number"
              label="物料编码"
              width="120"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="retail_price"
              label="零售价"
              width="120"
              class-name="reject-alert-row"
            >
              <template slot-scope="scope">
                {{ scope.row.retail_price }}
                <el-button size="mini" type="primary" @click="scan(scope.row)"
                  >查看</el-button
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="purchase_price"
              label="采购价"
              width="120"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="factory_name"
              label="下推工厂"
              width="150"
              class-name="reject-alert-row"
            >
            </el-table-column>
            <el-table-column
              prop="filePath"
              label="拆单文件路径"
              width="250"
              class-name="reject-alert-row"
            >
            <template slot-scope="scope">
              <span style="width:190px;height: 18px;display: inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{ scope.row.filePath }}</span>
               <el-button size="mini" type="primary" @click="copy(scope.row.filePath)"
                    >复制</el-button
                  >
              
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="160"
              class-name="reject-alert-row"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  :disabled="canPush"
                  @click="reject(scope.row)"
                  >驳回</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

    </div>
  

  </div>
</template>
<script>
import { forEach } from 'jszip';
import {
  synGoods,
  rejectPushPurchase, 
  auditNewScmMergeTrade,
  customSupplyAftersale,
  saveAfterPlan,
  submitAfterPlan,
} from "../common/api";
export default {
  data() {
    let self = this;
    return {
      cabinetOpiton:[],
      blisterOpiton:[],
      allminumOpiton:[],
      canPush:false,
      pushObj:{
        "BLISTER":{
              "factory_code":"" ,       //工厂编码
              "factory_name":""       //工厂名称
          },
          "ALUMINUM":{
              "factory_code":"" ,       //工厂编码
              "factory_name":""       //工厂名称
          },
        "CABINET":{
              "factory_code":""  ,      //工厂编码
              "factory_name":""       //工厂名称
          },
      },
      ispushEvent: false,
      dataList: [],
      selectData: "",
      pageTotal: 0,
      showHead: false,
      refreshBtnStatus: false,
      trade_type: "ORIGINAL",
      selectRows: [],
      selectGoods: [],
    };
  },
  props: ["params"],
  methods: {
    getDetail(callback){
				let self = this;
			
						this.ajax.postStream(
						'/custom-web/api/apartConfig/allList',
						{},
						(res) => {
						res = res.body;
						if (res.result) {
							let hasObj= {};
 
							self.purchase_trade_type_option = res.content.reduce((cur,next) => {
								hasObj[next.type] ? "" : (hasObj[next.type] = true && cur.push(next)); 
								return cur;
							},[]) 
							// console.log(self.purchase_trade_type_option);
              callback(self.purchase_trade_type_option)
						}else{
							this.$message({
							message: res.msg,
							type: "error",
							});
						}
						},
						(err) => {
						this.$message({
							message: err.msg,
							type: "error",
						});
						}
					);
						

			},
     urgentFreeList(){
      
			var _this = this;
			let search = {status: 1, page: {length: 50, pageNo: 1}}
			this.ajax.postStream('/custom-web/api/customSupplierUrgentFee/list',search,function(response){
				if(response.body.result){
					_this.dataList = [];
          response.body.content.list.forEach(item=>{
            _this.dataList.push({
              factory_name:item.supplierName,
              factory_code:item.factory_code,
            })
          })
				}
				else{
					_this.$message.error(response.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
    },
   
    
  
  
    // 查看
    scan(d) {
      const { purchase_trade_no } = d;
      this.$root.eventHandle.$emit("alert", {
        params: {
          purchase_trade_no,
        },
        component: () => import("./quoteList"),
        style: "width:800px;height:600px",
        title: "报价清单",
      });
    },
    // 查看
    copy(text) {
        var element = this.createElement(text);
        element.select();
        element.setSelectionRange(0, element.value.length);
        document.execCommand('copy');
        element.remove();
        alert("已复制到剪切板");
    },
    createElement(text) {
          var isRTL = document.documentElement.getAttribute('dir') === 'rtl';
          var element = document.createElement('textarea');
          // 防止在ios中产生缩放效果
          element.style.fontSize = '12pt';
          // 重置盒模型
          element.style.border = '0';
          element.style.padding = '0';
          element.style.margin = '0';
          // 将元素移到屏幕外
          element.style.position = 'absolute';
          element.style[isRTL ? 'right' : 'left'] = '-9999px';
          // 移动元素到页面底部
          let yPosition = window.pageYOffset || document.documentElement.scrollTop;
          element.style.top = `${yPosition}px`;
          //设置元素只读
          element.setAttribute('readonly', '');
          element.value = text;
          document.body.appendChild(element);
          return element;
      },
    rowKey(row) {
      return row.custom_goods_id;
    },
    rowStyle() {
      return "height:50px;";
    },
    close() {
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    save() {
      this.ajax.postStream(
        "/custom-web/api/customSwj/saveSwjQuotation",
        this.dataList,
        (d) => {
          if (d.body.result) {
            this.$message.success(d.body.msg || "");
            // this.close();
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
        },
        (err) => {
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
    getList(resolve) {
      this.refreshBtnStatus = true;
      let params = {
        client_number: this.params.client_number,
      };
      this.ajax.postStream(
        "/custom-web/api/customGoods/getGoodsPurchaseTrade",
        params,
        (d) => {
          if (d.body.result && d.body.content) {
            this.dataList = this.handle(d.body.content) || [];
            this.$message.success(d.body.msg || "");
            // 初始化勾选数组的长度
            this.selectRows = this.selectRowsInit(d.body.content);
            console.log(this.selectRows);
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
    getfactory(list) {
      let self = this;
      let params = {
        client_number: this.params.client_number,
      };
      this.ajax.postStream(
        "/custom-web/api/factoryConfig/getConfig",
        params,
        (d) => {
          if (d.body.result && d.body.content) {
            self.pushObj = d.body.content;
            list.forEach(item=>{
              for(let i in d.body.content){
                if(i == item.apart_number){
                  self.pushObj[i].purchase_trade_name = item.type
                  self.pushObj[i].purchase_trade_type = i
                }
              }
            })
            this.getList()
          
            self.$message.success(d.body.msg || "");
          } else {
            this.$message.error(d.body.msg || "");
          }
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
        }
      );
    },
    // 列表数据初始化
    handle(data) {
      let list =[];
       data.forEach((item) => {
         item.purchaseTrades.forEach(pItem=>{
           pItem.custom_goods_id = item.custom_goods_id;
           list.push(pItem);
         })
      });
      let obj = [];
      for(let i in this.pushObj){
        obj.push({
          purchase_trade_type:i,
          purchase_trade_name:this.pushObj[i].purchase_trade_name,
          arr:[]
        })
      }
        obj.forEach(item=>{
          list.forEach(Ditem=>{
            if(item.purchase_trade_type == Ditem.purchase_trade_type){
              item.arr.push(Ditem)
            }
          })
        })
      return obj
    },
    selectRowsInit(list) {
      if (!!list.length) {
        return new Array(list.length).fill([]);
      }
      return [];
    },
    select(rows, index) {
      this.selectRows[index] = rows;
    },
    // 下推前的校验
    pushBefore() {
      if (!this.dataList.length) {
        return false;
      }
      let next = true;
      this.dataList.forEach((elem) => {
        if (!elem.purchaseTrades || !elem.purchaseTrades.length) {
          next = false;
        }
      });
      if (!next) {
        this.$message.error(`无法进行下推操作，没有相应的采购订单！`);
        return false;
      }
      let arr = []
        .concat(
          this.dataList.map((item) => {
            return item.purchaseTrades;
          })
        )
        .flat()
        .filter((item) => !item.factory_name);
      if (arr.length > 0) {
        this.$message.error(
          `无法进行下推操作，请先完善采购订单下的下推工厂信息！`
        );
        return false;
      }
      return true;
    },
    // 选择工厂
    choose() {
      let self = this;
      // let arr = []
      //   .concat(this.selectRows)
      //   .flat()
      //   .filter((item) => !!item);
      // if (!arr.length) {
      //   this.$message.warning("请先选择采购订单！");
      //   return;
      // }
      let params = {
        client_number: this.params.client_number,
        pushObj:self.pushObj,
        // ids: arr.map((item) => {
        //   return {
        //     custom_purchase_id: item.custom_purchase_id,
        //   };
        // }),
        callback: (result) => {
          // const { factory_code, supplierName } = result;
          self.pushObj = result.factoryList ;
          console.log(result)
          self.getList();
        },
      };
      self.$root.eventHandle.$emit("alert", {
        params: params,
        component: () => import("./pushAlert"),
        style: "width:538px;height:450px",
        title: "下推",
      });
    },
    // 下推
    pushEvent() {
      let self = this;
      self.params.callback &&
              self.params.callback({
                factoryList: this.pushObj,
              });
            self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
    },
    // 驳回前的校验
    rejectBefore() {
      if (!this.dataList.length) {
        return false;
      }
      if (!this.dataList.filter((item) => item.checked).length) {
        this.$message.error(`无法进行驳回操作，请先选择需要驳回的商品！`);
        return false;
      }
      // if (
      //   !this.dataList.filter((item) => item.checked && item.result !== "")
      //     .length
      // ) {
      //   this.$message.error(`无法进行驳回操作，商品请填写驳回理由！`);
      //   return false;
      // }
      return true;
    },
    rejectAll(row) {
      console.log(this.selectRows)
      let idList = [];
      this.selectRows.forEach(item=>{
        item.forEach(pItem=>{
          idList.push(pItem.custom_goods_id)
        })
      })
      if(!idList.length){
        this.$message.error('请选择采购订单')
        return;
      }
      let self = this;
        this.$prompt('请输入备注', '驳回', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({ value }) => {
          // data.remark = value
          let params = idList.map(item=>{
            return {custom_goods_id:item,remark:value}
          })
          this.isRequest = true;
          rejectPushPurchase(params, true, true).then((res) => {
            this.isRequest = false;
            if (res.result) {
              self.getList();
            } else {
              this.$message.error(`${res.msg}`);
            }
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });       
        });
      
        
        
      
    },
    
    // 驳回
    reject(row) {
      let data = {
          custom_goods_id:row.custom_goods_id,
          remark:''
        }
      let self = this;
        this.$prompt('请输入备注', '驳回', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({ value }) => {
          data.remark = value
          this.isRequest = true;
          rejectPushPurchase([data], true, true).then((res) => {
            this.isRequest = false;
            if (res.result) {
              self.getList();
            } else {
              this.$message.error(`${res.msg}`);
            }
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });       
        });
      
        
        
      
    },
    //列表显示
		getfactoryConfig(d, resolve){
			
			var _this = this;
			let search = {
				page_size: self.pageSize,     //页数
				page_no:1,   //页码
        page_name: 'custom_factory_config',
        where: []
			};
			this.ajax.postStream('/custom-web/api/factoryConfig/list',search,function(response){
				if(response.body.result){
					let dataList = response.body.content.list;
        
				}
				else{
					_this.$message.error(response.body.msg)
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
    sys(client_number) {
      // 同步商品
      if (this.params.trade_type === "SUPPLY") return;
      synGoods(client_number).then((body) => {
        if (body.result) {
          auditNewScmMergeTrade(client_number);
        }
      });
    },
  },
  mounted() {
    this.canPush = ['WAITING_OUTBOUND','COMPLETED','WAITING_PUSH_GOODS'].includes(this.params.row.client_status_value)
    console.log(this.params)
  },
  created() {
    this.trade_type = this.params.trade_type;
    new Promise((resolve,reject)=>{
      this.getDetail(resolve)
    }).then(res=>{
      this.getfactory(res)
    })
    this.getfactoryConfig();
    // this.urgentFreeList();
  },
};
</script>
<style scoped>
.goods-title {
  height: 40px;
  line-height: 40px;
  color: #1f2d3d;
  font-size: 16px !important;
  z-index: 9999;
}
.goods-title:hover {
  cursor: pointer;
}
.el-checkbox {
  /* border:1px solid #ddd; */
  margin-bottom: 10px;
}
.el-checkbox .goods-table {
  margin: 4px 20px;
}
.el-checkbox + .el-checkbox {
  margin-left: 0 !important;
}
</style>
