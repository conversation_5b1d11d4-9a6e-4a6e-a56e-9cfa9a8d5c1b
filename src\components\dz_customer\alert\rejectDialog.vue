<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='primary' size='mini' @click="confirm">确定</el-button>
      </el-col>
    </el-row>
    <el-form label-position="right" label-width="120px"> 
        <el-col :span="6" style="width: 100%">
         <el-form-item label="驳回分类：" style="height: 80px;"  class="reject-type-container">
            <el-checkbox-group v-model="activeList" size="small">
                <el-checkbox-button
                    v-for="item in list"
                    :label="item.code"
                    :key="item.code"
                    >{{ item.name }}</el-checkbox-button
                >
            </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="6" style="width: 100%">
        <el-form-item label="驳回原因：" style="height: 80px;">
          <el-input v-model="remark" type="textarea" :autosize="{ minRows: 3, maxRows: 3}" size='mini' style="width:92%;" :maxlength='200' placeholder="请输入退回原因，200字以内"></el-input>
        </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>

<script>
    export default {
      props:["params"],
      data(){
        return {
          remark:"",
          list:[],
          activeList:[],
        }
      },
      methods:{
        async confirm(){
            // if(this.params.isPushBOM){
                
            // }else{
            //     if (this.remark === "" || this.remark.trim() === '') {
            //       this.$message.error("请输入驳回原因！")
            //       return
            //     }
            //     this.params.callback(this.remark)
            // }
            let classification_code = this.activeList.join(",")
                if(!classification_code && (this.remark === "" || this.remark.trim() === '')){
                    this.$message.error("请输入驳回原因！")
                    return
                }
                this.params.callback({
                    remark: this.remark,
                    classification_code,
                    classification_name: await this.getClassificationName(classification_code)
                })
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
        },
        getClassificationName(codeJson){
            if(!codeJson) return codeJson
            return this.list.filter(item=>{
                return codeJson.indexOf(item.code) !== -1
            }).map(item=>{
                return item.name
            }).join(',')
        },
      },
      mounted(){
        this.list = __AUX.get('custom_reject_push_purchase');
      }
    }
</script>

<style scoped>

</style>
<style>
.reject-type-container .el-checkbox-button__inner {
  width: 150px;
  background-color: #f1f1f1;
  text-align: center;
  margin: 0 10px 10px;
  border-radius: 20px !important;
  display: inline-block;
  cursor: pointer;
  border: none;
}
.reject-type-container
  .el-checkbox-button:first-child
  .el-checkbox-button__inner {
  border-left: none;
}
</style>