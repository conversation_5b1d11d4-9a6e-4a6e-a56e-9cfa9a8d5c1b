<!-- 批准供应商列表选择供应商编码 -->
<template>
	<xpt-list :data='roleList' 
		:colData='colData' 
		searchHolder='请输入供应商名称'
		:pageTotal='pageTotal' 
		:btns='btns'
        selection='radio'
        @row-dblclick='rowDblclick'
		@search-click='searchData' 
		@radio-change='select' 
		@page-size-change='pageChange' 
		@current-page-change='currentPageChange' ref='xptList'>
	</xpt-list>
</template>
<script>
	export default {
		data(){
			let self = this;
			return {
				roleList:[],
				// 已选行Id
				selectRole:'',
				pageNow:1,
				pageTotal:0,
				colData:[
					{
						label:'供应商ID',
						prop:'supplier_id',
						width:'100'
					},{
						label:'供应商编码',
						prop:'number',
						width:'100'
					},
					{
						label:'供应商',
						prop:'name'
					}
				],
				btns:[
                    {
                        type: 'primary',
                        txt: '确认',
                        click: self.close
                    }
                ]
			}
		},
		methods:{
            close(){
                if(!this.selectRole) {
                    this.$message.error('请先选择供应商');
                    return;
                }
                this.params.callback(this.selectRole)
                this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
            },
			// 搜索
			searchData(s){
				if(s){
					this._getDataList({supplierName:s})
				}else{
					this._getDataList();
				}
			},
			// 监听每页显示数更改事件
			pageChange(pageSize){
				this.pageSize = pageSize
				this.pageNow = 1;
				this._getDataList();
			},
			// 监听页数更改事件
			currentPageChange(page){
				this.pageNow = page
				this._getDataList();
			},
			// 单选事件
			select(s){
				this.selectRole = s;
            },
            rowDblclick(obj) {
                this.selectRole = obj
                this.close()
            },
			// 查询所有角色列表
			_getDataList(data){
				data = data || {}
				data.page = {
					length: this.pageSize,
					pageNo: this.pageNow
				}
				
	   			this.ajax.postStream('/custom-web/api/customSupplierUrgentFee/getSupplierList',data, (d) => {
	   				if(d.body.result){
	   					this.roleList = d.body.content.list||[];
	   					this.pageTotal = d.body.content.count||0
	   				}else{
	   					this.$message({
	   						message:d.body.msg,
	   						type:'error'
	   					})
	   				}
				}, err => {
					this.$message.error(err);
				}, this.params.tabName);
			}
		},
		props:['params'],
		mounted(){
            this._getDataList();
		},
	}
</script>
