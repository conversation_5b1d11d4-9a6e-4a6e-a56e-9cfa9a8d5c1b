<!-- 报价弹窗 -->
<template>
    <div>
        <xpt-headbar>
            <el-button type="primary" size="mini" @click="operaterAjax()" slot="left" :loading="isRejectLoading">驳回
            </el-button>
            <el-button type="success" size="mini" @click="getList()" slot="left" :loading="refreshBtnStatus">刷新
            </el-button>
        </xpt-headbar>
        <div style="overflow-y: auto; height: 530px">
            <el-button type="primary" size="mini" @click="selectAll()" slot="left">全选商品</el-button>
            <div :key="item.custom_goods_id" v-for="(item, index) in dataList">
                <div style="margin: 10px 0">
                    <el-checkbox v-model="item.checked" v-if="trade_type !== 'pushBOM'"></el-checkbox>
                    <span class="goods-title"><span class="goods-title"
                            style="color: #20a0ff; font-weight: bold">商品:</span>{{ item.message }}</span>
                </div>
                <div style="padding-left: 30px">
                    <el-table style="width: 90%" class="goods-table" border :row-key="rowKey"
                        :data="item.purchaseTrades" @select="(selection, row) => select(selection, index)"
                        @select-all="(selection) => select(selection, index)" :cell-style="rowStyle">
                        <el-table-column type="selection" width="55" class-name="reject-alert-row"
                            v-if="trade_type !== 'pushBOM'">
                        </el-table-column>
                        <el-table-column prop="purchase_trade_no" label="采购订单" width="180"
                            class-name="reject-alert-row">
                        </el-table-column>
                        <el-table-column prop="purchase_trade_type_cn" label="订单类别" width="120"
                            class-name="reject-alert-row">
                        </el-table-column>
                        <el-table-column prop="material_number" label="物料编码" width="120" class-name="reject-alert-row">
                        </el-table-column>
                        <el-table-column prop="retail_price" label="零售价" width="120" class-name="reject-alert-row">
                            <template slot-scope="scope">
                                {{ scope.row.retail_price }}
                                <el-button size="mini" type="primary" @click="scan(scope.row)">查看</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column prop="purchase_price" label="采购价" width="120" class-name="reject-alert-row">
                        </el-table-column>
                        <el-table-column prop="filePath" label="拆单文件路径" width="250" class-name="reject-alert-row">
                        </el-table-column>
                        <el-table-column label="操作" width="160" class-name="reject-alert-row">
                            <template slot-scope="scope">
                                <el-button size="mini" type="primary" @click="cancelScm(scope.row)">取消scm订单</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {
    rejectPushPurchaseByPermission,
} from "../common/api";
export default {
    data() {
        let self = this;
        return {
            dataList: [],
            selectData: "",
            pageTotal: 0,
            showHead: false,
            refreshBtnStatus: false,
            isRejectLoading: false,
            trade_type: "ORIGINAL",
            selectRows: [],
            selectGoods: [],
        };
    },
    props: ["params"],
    methods: {
        operaterAjax() {
            let self = this
            let flag = false;
            self.dataList.forEach(item => {
                if (item.checked == true) {
                    flag = true;
                }
            })
            if (!flag) {
                self.$message.error('请选择驳回商品');
                return;
            }
            this.$root.eventHandle.$emit('alert', {
                component: () => import('./rejectDialog.vue'),
                style: 'width:660px;height:270px',
                title: "驳回",
                params: {
                    //刷新
                    callback: data => {
                        self.reject(data)
                    }
                },
            })


        },
        selectAll() {
            let self = this;
            self.dataList.forEach(item => {
                item.checked = true;
            })
        },
        // 取消scm订单
        cancelScm(row) {
            let params = {
                custom_purchase_id: row.custom_purchase_id,
            };
            this.ajax.postStream(
                "/custom-web/api/customPush/deleteScmTrade",
                params,
                (d) => {
                    if (d.body.result) {
                        this.$message.success(d.body.msg || "");
                    } else {
                        this.$message.error(d.body.msg || "");
                    }
                },
                (err) => {
                    this.$message.error(err);
                }
            );
        },
        // 查看
        scan(d) {
            const { purchase_trade_no } = d;
            this.$root.eventHandle.$emit("alert", {
                params: {
                    purchase_trade_no,
                },
                component: () => import("./quoteList"),
                style: "width:800px;height:600px",
                title: "报价清单",
            });
        },
        rowKey(row) {
            return row.custom_goods_id;
        },
        rowStyle() {
            return "height:50px;";
        },
        close() {
            this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
        },
        getList(resolve) {
            this.refreshBtnStatus = true;
            let params = {
                client_number: this.params.client_number,
            };
            this.ajax.postStream(
                "/custom-web/api/customGoods/getGoodsPurchaseTrade",
                params,
                (d) => {
                    if (d.body.result && d.body.content) {
                        this.dataList = this.handle(d.body.content) || [];
                        this.$message.success(d.body.msg || "");
                        // 初始化勾选数组的长度
                        this.selectRows = this.selectRowsInit(d.body.content);
                        console.log(this.selectRows);
                    } else {
                        this.$message.error(d.body.msg || "");
                    }
                    this.refreshBtnStatus = false;
                    resolve && resolve();
                },
                (err) => {
                    resolve && resolve();
                    this.$message.error(err);
                    this.refreshBtnStatus = false;
                }
            );
        },
        // 列表数据初始化
        handle(data) {
            return data.map((item) => {
                return {
                    ...item,
                    checked: false,
                    result: "",
                };
            });
        },
        selectRowsInit(list) {
            if (!!list.length) {
                return new Array(list.length).fill([]);
            }
            return [];
        },
        select(rows, index) {
            this.selectRows[index] = rows;
        },
        // 驳回前的校验
        rejectBefore() {
            if (!this.dataList.length) {
                return false;
            }
            if (!this.dataList.filter((item) => item.checked).length) {
                this.$message.error(`无法进行驳回操作，请先选择需要驳回的商品！`);
                return false;
            }
            // if (
            //   !this.dataList.filter((item) => item.checked && item.result !== "")
            //     .length
            // ) {
            //   this.$message.error(`无法进行驳回操作，商品请填写驳回理由！`);
            //   return false;
            // }
            return true;
        },
        // 驳回
        reject(param) {
            if (this.rejectBefore()) {
                if (this.isRejectLoading) {
                    this.$message.error("请等待上一次请求结果");
                }
                let data = this.dataList
                    .filter((item) => item.checked)
                    .map((e) => {
                        return {
                            custom_goods_id: e.custom_goods_id,
                            ...param
                        };
                    });
                this.isRejectLoading = true;
                rejectPushPurchaseByPermission(data, true, true).then((res) => {
                    this.isRejectLoading = false;
                    if (res.result) {
                        this.getList();
                    } else {
                        this.$message.error(`${res.msg}`);
                    }
                });
            }
        },
    },
    mounted() { },
    created() {
        this.trade_type = this.params.trade_type;
        this.getList();
    },
};
</script>
<style scoped>
.goods-title {
    height: 40px;
    line-height: 40px;
    color: #1f2d3d;
    font-size: 16px !important;
    z-index: 9999;
}

.goods-title:hover {
    cursor: pointer;
}

.el-checkbox {
    /* border:1px solid #ddd; */
    margin-bottom: 10px;
}

.el-checkbox .goods-table {
    margin: 4px 20px;
}

.el-checkbox+.el-checkbox {
    margin-left: 0 !important;
}
</style>
  