<template>
  <!-- 审核阶段-审图审价&拆单&拆审弹窗 -->
  <div class="info"  v-loading="loading" element-loading-text="拼命加载中">
    <div v-loading="sysLoading" element-loading-text="数据同步中">
      <div class="info-container">
        <info v-for="info in infos" :key="info.key" :info="info" />
      </div>
      <div class="title">
        <span>{{ verLabel }}</span>
      </div>
      <div>
        <form-create
          ref="form"
          :formData="formData"
          :btns="false"
          :ruleData="ruleData"
          @save="saveData"
        >
          <div class="form-btns" slot="footer">
            <el-button
              type="primary"
              size="small"
              @click="refresh"
              >刷新</el-button
            >
            <el-button
              type="primary"
              size="small"
              @click="save"
              :loading="saveLoading"
              :disabled="saveDisabled"
              >保存</el-button
            >
            <!-- 分类没有提交按钮 -->
            <el-button
              type="success"
              size="small"
              @click="ven"
              v-if="verType !== 'fl'"
              :loading="submitLoading"
              :disabled="submitDisabled"
              >{{ shLabel || "提交" }}</el-button
            >
            <!-- 审图拆单/拆审-三维家审核 -->
            
            <el-button
              type="success"
              size="small"
              v-if="verType === 'sj' || verType === 'cs'"
              @click="swjSubmit"
              >三维家审核</el-button
            >
            <!-- 审图拆单-同步 -->
            
            <el-button
              type="success"
              size="small"
              v-if="verType === 'sj'"
              @click="swjSys"
              >同步</el-button
            >
            <el-button
              type="success"
              size="small"
              @click="swjCalculate(true)"
              >计算价格</el-button
            >
          </div>
        </form-create>
      </div>
    </div>
   
  </div>
</template>
<script>
import info from "../clientInfo/info";
import { calcMlxs } from "../common/utils"
import {
  createIds,
  pushMaterial,
  openSwjGoods,
  calculate,
  receiveSplitMsg
} from "../common/api";
import { list } from '../alert/alert'
import { review_status } from "../common/verifyDictionary";
import formCreate from "../components/formCreate/formCreate";
import orderStatus from "../common/mixins/orderStatus";
import { imageAdmin, imageView2 } from '../alert/alert'

import closeComponent from "../common/mixins/closeComponent";
import { cloneDeep } from 'loadsh'
export default {
  props: {
    params: {
      type: Object,
    },
  },
  mixins: [orderStatus, closeComponent],
  components: { info, formCreate },
  data() {
    return {
      dataList:[],
      shLabel: "",
      verLabel: "",
      goodsProblemDetails:null,
      loading: true,
      saveLoading: false,
      saveDisabled: false,
      submitLoading: false,
      submitDisabled: false,
      infos: [],
      formData: [],
      ruleData: {},
      sysLoading: false,
      colData: Object.freeze([
        {
          label: '单元名称',
          prop: 'myUnitName',
          width: '120',
        },
        {
          label: '部件的名称',
          prop: 'name',
          width: '100',
        },
        {
          label: '部件所属的报价类型',
          prop: 'quoteTypeCode',
          width: '120',
        },
        {
          label: '部件编号',
          prop: 'partNumber',
          width: '70',
        },
        {
          label: '深(mm)',
          prop: 'depth',
          width: '70',
        },
        
        {
          label: '高(mm)',
          prop: 'height',
          width: '70',
        },
        {
          label: '宽(mm)',
          prop: 'width',
          width: '70',
        },
        {
          label: '颜色',
          prop: 'colorCode',
          width: '100',
        },
        {
          label: '基材',
          prop: 'baseCode',
          width: '70',
        },
         {
          label: '单价',
          prop: 'priceUnit',
          width: '70',
        },
        {
          label: '用量',
          prop: 'consumption',
          width: '70',
        },
        {
          label: "包装系数",
          prop: "wooden_support_fee",
          width: "100",
        },

        {
          label: '特殊加价',
          prop: 'specialUpPrice',
          width: '100',
        },
        {
          label: '金额',
          prop: 'amountMoney',
          width: '100',
        },
        
       
        {
          label: '单位',
          prop: 'unit',
          width: '100',
        },
        {
          label: '组合名称',
          prop: 'myGroupName',
          width: '120',
        }
      ])
    };
  },
  computed: {
    
   
    verType() {
      // 审核阶段 fl分级评定 sj审图拆单  cs拆审
      return this.params.verType || "sj";
    },
    trade_type() {
      // ORIGINAL源单 SUPPLY补单
      return this.params.trade_type || "ORIGINAL";
    },
  },
  async created() {
    // this.config = this.getConfig();
    this.config = this.getConfig();
    this.verLabel = this.config.verLabel;
    this.shLabel = this.config.shLabel;
    this.ids = await createIds(2)
    this.init()

    this.$root.eventHandle.$on("verifypicUpload", this.verifypicUpload);
  },

  beforeDestroy() {
    this.$root.eventHandle.$off("verifypicUpload", this.verifypicUpload);
  },
  methods: {
     swjCalculate(isClick){
      if(!isClick){
        return
      }
      /**
     * @description: 计算价格
     * @param {*}
     * @return {*}
     */  
      const {
        custom_goods_id
      } = this.goodsInfo
      this.loading = true

      calculate({
        custom_goods_id: custom_goods_id
      }, false, false).then(res => {
        if(res.result) {
          // 计算价格成功,重新加载
          this.refresh()
        } else {
          this.loading = false
        }
      }).catch(() => {
        this.loading = false
      })
    },
    showCloseComponent() {
      return this.notSave;
    },
    saveComponent() {
      this.type = "save";
      this.$refs.form.save();
    },
    refresh(){
    /**
     * @description: 刷新
     * @param {*}
     * @return {*}
     */  
      this.isRefresh = {
        uploadFile: true,
        uploadFileCd: true
      }
      // 重新加载商品详情
      this.init()

       // 更新文件
      this.$refs.form.updateFile(true)
    },
    lookBj(quotation_type) {
    const {swj_goods_id} = this.goodsInfo
      const {trade_type}=this.params
      if(!swj_goods_id){
        return this.$message.error("没有三维家报价清单")
      }
      this.$root.eventHandle.$emit('alert', 
          {
          params: {
            quotation_type,
            swj_goods_id,
            trade_type
          },
          component:()=>import('./offerAlert.vue'),
          style: 'width:1100px;height:640px',
          title: '报价清单',
          });
    },
    swjSys() {
    /**
     * @description: 同步
     * @param {*}
     * @return {*}
     */  
      const {
        swj_goods_id
      } = this.goodsInfo
      this.sysLoading = true
      
      receiveSplitMsg({
        subjectId: swj_goods_id
      }, false, false).then(res => {
        if(res.result) {
          // 同步成功,重新加载
          this.refresh()
        } else {
          this.sysLoading = false
        }
      }).catch(() => {
        this.sysLoading = false
      })
    },
    swjSubmit() {
    /**
     * @description: 三维家审核
     * @param {*}
     * @return {*}
     */  
      openSwjGoods({
        swj_goods_id: this.goodsInfo.swj_goods_id
      }).then(res => {
        if(res.result) {
            window.open(res.content)
        }
      })
    },
    verifypicUpload() {
      // 判断是否需要保存数据
      if (this.notSave) {
        this.save();
      }
    },
    getConfig(verType) {
      // 根据审核类型返回相应配置
      let self = this;
      let [
        color_cn,
        style_cn,
        category_cn,
        material_cn,
        length,
        width,
        height,
        shop_name,
        shop_code,
        client_number_list,
        client_name,
        client_mobile,
        difficulty,
        message,
        retail_price,
        purchase_price,
        act_price,
        design_room_name,
        designer,
        goodIds,
        goodsFile,
        original_client_number_list,
        origin_message,
        goods_id,
      ] = [
        { prop: "color_cn", label: "主材颜色", span: 12 },
        { prop: "style_cn", label: "风格", span: 12 },
        { prop: "category_cn", label: "类目", span: 12 },
        { prop: "material_cn", label: "材质", span: 12 },
        { prop: "length", label: "宽度", suffix: "mm", span: 12 },
        { prop: "width", label: "深度", suffix: "mm", span: 12 },
        { prop: "height", label: "高度", suffix: "mm", span: 12 },
        { prop: "shop_name", label: "专卖店", span: 12 },
        { prop: "shop_code", label: "专卖店编号", span: 12 },
        {
          formType: "orderList",
          prop: "client_number_list",
          label: "订单编号",
          span: 12,
          params: {
            orderClick: (item) => {
              if (this.trade_type === "SUPPLY") {
                this.$root.eventHandle.$emit("creatTab", {
                  name: "补单详情",
                  component: () =>
                    import("@components/dz_customer/supplement/supplyInfo.vue"),
                  params: {
                    client_number: item,
                  },
                });
              } else {
                this.$root.eventHandle.$emit("creatTab", {
                  name: "订单详情",
                  component: () =>
                    import("@components/dz_customer/clientInfo/clientInfo.vue"),
                  params: {
                    customerInfo: { client_number: item },
                    lastTab: this.params.tabName,
                  },
                });
              }
            },
          },
        },
        { prop: "client_name", label: "客户名称", span: 12 },
        { prop: "client_mobile", label: "客户手机", span: 12 },
        {
          prop: "difficult",
          valueFilter: "select",
          label: "难度",
          span: 12,
          options: [
            { label: "一级", value: 1 },
            { label: "二级", value: 2 },
            { label: "三级", value: 3 },
          ],
        },
        { prop: "message", label: "商品名称", span: 12 },
        {
          prop: "retail_price",
          label: "零售价",
          suffix: "元",
          notellipsis: true,
          span: 12,
        },
        {
          prop: "purchase_price",
          label: "采购价格",
          suffix: "元",
          notellipsis: true,
          span: 12,
        },
        {
          prop: "act_price",
          label: "实际价格",
          suffix: "元",
          notellipsis: true,
          span: 12,
        },
        { prop: "design_room_name", label: "所在空间", span: 12 },
        { prop: "designer", label: "设计师", span: 12 },
        {
          formType: "orderList",
          prop: "goodIds",
          label: "同订单商品",
          span: 24,
          params: {
            orderClick: (item) => {
              console.log(item)
              this.$root.eventHandle.$emit("creatTab", {
                name: "商品详情",
                component: () =>
                  import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
                params: {
                  goodsInfo: this.goodsInfo ,
                  trade_type: this.trade_type,
                },
              });
            },
          },
        },
        {
          formType: "goodsFile",
          label: "商品相关文件",
          prop: "goodsFile",
          span: 24,
        },
        {
          formType: "orderList",
          prop: "original_client_number_list",
          label: "源订单编号",
          span: 12,
          params: {
            orderClick: (item) => {
              this.$root.eventHandle.$emit("creatTab", {
                name: "订单详情",
                component: () =>
                  import("@components/dz_customer/clientInfo/clientInfo.vue"),
                params: {
                  customerInfo: { client_number: item },
                  lastTab: this.params.tabName,
                },
              });
            },
          },
        },
         {
          prop: "origin_message",
          label: "源商品名称",
          span: 12,
        },
        { prop: "goods_id", label: "补单商品编码", span: 12 },
      ];
      if (this.trade_type === "SUPPLY") {
        client_number_list.label = "补单编号";
      }
      let detail = [];
      if (this.trade_type === "SUPPLY") {
        detail = [
          length,
          width,
          height,
          shop_name,
          shop_code,
          client_number_list,
          original_client_number_list,
          client_name,
          client_mobile,
          difficulty,
          message,
          retail_price,
          goods_id,
          origin_message,
          designer,
          goodIds,
          goodsFile,
        ];
        if (this.verType === "cs") {
          detail.splice(detail.length - 5, 0, purchase_price);
        }
      } else {
        detail = [
          //详情信息
          color_cn,
          style_cn,
          category_cn,
          material_cn,
          length,
          width,
          height,
          shop_name,
          shop_code,
          client_number_list,
          client_name,
          client_mobile,
          difficulty,
          message,
          retail_price,
          act_price,
          design_room_name,
          designer,
          goodIds,
          goodsFile,
        ];
        if (this.verType === "cs") {
          detail.splice(detail.length - 4, 0, purchase_price);
        }
      }
      console.log(this.$refs.form )
      if(this.$refs.form && this.$refs.form.formData.length){
         let retail_price1 = this.$refs.form.getItem('retail_price');
         let number1 = this.$refs.form.getItem('purchase_price');
          // console.log(retail_price1)
          retail_price1.disabled = !!self.goodsProblemDetails;
          number1.disabled = !!self.goodsProblemDetails;
      }
     
      let config = {
        sj: {
          goodsUrl:
            "/custom-web/api/reviewDrawingPrice/getGoodsReviewInfoByGoodsId", //商品信息
          saveUrl: "/custom-web/api/reviewDrawingPrice/saveReviewInfo", //保存
          id: "review_drawing_price_id", //保存id
          apart_order_id: "apart_order_id",
          price_id:"price_id",
          submit_id: "submit_id", //提交id
          shLabel: "提交",
          review_id: "review_id", //返回商品信息的审核id属性值
          submit: "verfypic", //提交成功事件
          verLabel: "审图拆单",
          uploadPrefix: "", //文件上传风格二级前缀
          saveParam: ["client_number", "goods_id"], //接口参数
          submitUrl: "/custom-web/api/reviewDrawingPrice/submitReview", //提交
          detail,
          ruleData: (v = {}) => {
            return {
                  uploadFile: { required: false, trigger: "change" },
                  uploadFileCd: { required: false, trigger: "change" },
                  retail_price: {
                    required: false,
                    validate: "isPrice",
                  },
                  purchase_price: {
                    required: false,
                    validate: "isPrice",
                  },
                };
          },
          formData: [
            {
              cols: [
                {
                  formType: "uploadFile",
                  prop: "uploadFile",
                  label: "评审文件",
                  span: 12,
                  type: "sj",
                },
                 {
                  formType: "uploadFile",
                  prop: "uploadFileCd",
                  label: "拆单文件",
                  span: 12,
                  type: "cd",
                },
               
              ],
            },
            {
              cols: [
                 {
                  formType: "elInput",
                  prop: "retail_price",
                  disabled:!self.goodsProblemDetails,
                  valueFilter(v) {
                    return v.retail_price;
                  },
                  event: {
                    change(v, col, data, getItem) {
                      const mlxs = getItem('mlxs')
                      mlxs.value = calcMlxs(data.retail_price, data.purchase_price)
                    }
                  },
                  label: "零售价",
                  type: "number",
                  suffix: "元",
                  span: 12
                },
                {
                  formType: "elInput",
                  prop: "purchase_price",
                  disabled:true,
                  valueFilter(v) {
                    return v.purchase_price;
                  },
                  event: {
                    change(v, col, data, getItem) {
                      const mlxs = getItem('mlxs')
                      mlxs.value = calcMlxs(data.retail_price, data.purchase_price)
                    }
                  },
                  label: "采购价格",
                  type: "number",
                  suffix: "元",
                  span: 12,
                },
                {
                  formType: {
                    template: `<div><el-button type="primary" size="mini" @click="$emit('click')" 
                      >查看报价清单</el-button
                    ></div>`
                  },
                  span: 12,
                  event: {
                    click() {
                      self.lookBj('RETAIL_PRICE')
                    }
                  },
                  label: '零售报价'
                },
                {
                  formType: {
                    template: `<div><el-button type="primary" size="mini" @click="$emit('click')" 
                      >查看报价清单</el-button
                    ></div>`
                  },
                  event: {
                    click() {
                      self.lookBj('PURCHASE_PRICE')
                    }
                  },
                  label: '采购报价'
                },
                {
                  formType: "myText",
                  prop: "mlxs",
                  label: "毛利系数",
                  valueFilter(v) {
                    return calcMlxs(v.retail_price, v.purchase_price)
                  },
                  span: 24,
                },
              ],
            },
            {
              cols: [
                {
                  formType: "elRadioGroup",
                  valueFilter(v) {
                    return v.review_result || "PASSING";
                  },
                  label: "审核",
                  span: 24,
                  prop: "review_result",
                  options: [{value:'PASSING',label:'通过'},{value:'DISALLOW',label:'驳回'}],
                },
                {
                  formType: "elInput",
                  valueFilter(v) {
                    return v.remark;
                  },
                  type: "textarea",
                  label: "备注",
                  span: 16,
                  maxlength: 150,
                  prop: "remark",
                },
              ]
            }
          ],
        },
        cd: {
          verLabel: "拆单",
          id: "apart_order_id",
          submit_id: "submit_id",
          shLabel: "提交拆单",
          review_id: "review_id",
          submit: "refreshsplitOrderList",
          saveParam: ["client_number", "goods_id"],
          uploadPrefix: "cd",
          goodsUrl: "/custom-web/api/apratOrder/getGoodsInfo",
          saveUrl: "/custom-web/api/apratOrder/saveApartOrderInfo",
          submitUrl: "/custom-web/api/apratOrder/submitApartOrder",
          detail,
          ruleData: (v = {}) => {
            return v.review_result === "PASSING" || !v.review_result
              ? {
                  uploadFile: {
                    required: true,
                    message: "请上传拆单文件",
                    trigger: "change",
                  },
                  purchase_price: { required: true, validate: "isPrice" },
                }
              : {
                  uploadFile: { required: false, trigger: "change" },
                  purchase_price: { required: false, validate: "isPrice" },
                };
          },
          formData: [
            {
              cols: [
                {
                  formType: "uploadFile",
                  prop: "uploadFile",
                  label: "拆单文件",
                  span: 24,
                  type: "ocd"
                },
                {
                  formType: "elInput",
                  prop: "purchase_price",
                  type: "number",
                  valueFilter(v) {
                    return v.purchase_price;
                  },
                  label: "采购价格",
                  span: 24,
                },
                {
                  event: {
                    change: (v) => {
                      this.ruleData = this.config.ruleData({
                        review_result: v,
                      });
                      this.$nextTick(() => {
                        this.$refs.form.dealRule(true);
                      });
                    },
                  },
                  formType: "elRadioGroup",
                  valueFilter(v) {
                    return v.review_result || "PASSING";
                  },
                  label: "拆单审核",
                  span: 24,
                  prop: "review_result",
                  options: review_status,
                },
                {
                  formType: "elInput",
                  valueFilter(v) {
                    return v.remark;
                  },
                  type: "textarea",
                  label: "备注",
                  span: 12,
                  maxlength: 150,
                  prop: "remark",
                },
              ],
            },
          ],
        },
        cs: {
          verLabel: "拆审",
          submit: "refreshReviewList",
          id: "split_audit_id",
          submit_id: "split_audit_id",
          saveParam: ["client_number", "goods_id"],
          shLabel: "提交",
          review_id: "split_audit_id",
          uploadPrefix: "cs",
          goodsUrl: "/custom-web/api/splitAudit/getGoodsInfo",
          saveUrl: "/custom-web/api/splitAudit/save",
          submitUrl: "/custom-web/api/splitAudit/submit",
          detail,
          ruleData: (v = {}) => {
            return {};
          },
          formData: [
            {
              cols: [
                {
                  formType: "uploadFile",
                  prop: "uploadFile",
                  label: "拆审文件",
                  span: 24,
                  type: 'cs'
                },
                {
                  formType: "myText",
                  prop: "mlxs",
                  label: "毛利系数",
                  valueFilter(v) {
                    return calcMlxs(v.retail_price, v.purchase_price)
                  },
                  span: 24,
                },
                {
                  event: {
                    change: (v) => {
                      this.ruleData = this.config.ruleData({
                        review_result: v,
                      });
                      this.$nextTick(() => {
                        this.$refs.form.dealRule(true);
                      });
                    },
                  },
                  formType: "elRadioGroup",
                  valueFilter(v) {
                    return v.review_result || "PASSING";
                  },
                  label: "拆审",
                  span: 24,
                  prop: "review_result",
                  options: review_status,
                },

                {
                  formType: "elInput",
                  valueFilter(v) {
                    return v.remark;
                  },
                  type: "textarea",
                  label: "备注",
                  span: 16,
                  maxlength: 150,
                  prop: "remark",
                },
              ],
            },
          ],
        },
        fl: {
          verLabel: "分级评定",
          submit: "goodsCategories",
          id: "classification_id",
          submit_id: "classification_id",
          saveParam: ["client_number", "goods_id", "designer_number"],
          shLabel: "提交评定",
          review_id: "review_id",
          uploadPrefix: "fl",
          goodsUrl:
            "/custom-web/api/classification/getGoodsClassficationInfoByGoodsId",
          saveUrl: "/custom-web/api/classification/saveReviewInfo",
          submitUrl: "/custom-web/api/classification/submitReview",
          detail,
          ruleData: {
            difficult: {
              required: true,
              message: "请选择难度",
              trigger: "change",
            },
          },
          formData: [
            {
              cols: [
                {
                  formType: "elSelect",
                  valueFilter(v) {
                    return v.difficult || "";
                  },
                  label: "难度",
                  span: 24,
                  prop: "difficult",
                  options: [
                    { label: "一级", value: 1 },
                    { label: "二级", value: 2 },
                    { label: "三级", value: 3 },
                  ],
                },
                {
                  formType: "elInput",
                  valueFilter(v) {
                    return v.remark;
                  },
                  type: "textarea",
                  label: "备注",
                  span: 12,
                  maxlength: 150,
                  prop: "remark",
                },
              ],
            },
          ],
        },
      };
      return verType ? config[verType] : config[this.verType];
    },
    // 获取
    saveData(data) {
      delete data.mlxs
      this.saveInfo(data);
    },
    save() {
      this.type = "save";
      this.$refs.form.save();
    },
    resetStatus() {
      this.saveLoading = false;
      this.saveDisabled = false;
      this.submitDisabled = false;
      this.submitLoading = false;
    },
    ven() {
      this.type = "ven";
      this.$refs.form.save();
    },
    async saveInfo(data, isShowMsg = true) {    
      // 设置按钮状态
      if (this.type === "ven") {
        this.submitLoading = true;
        this.saveDisabled = true;
      } else {
        this.saveLoading = true;
        this.submitDisabled = true;
      }
      
      const saveOrSumit = {
        sj: this.sjAndCdProcess
      }
      saveOrSumit[this.verType] ? saveOrSumit[this.verType](data, isShowMsg) : this.commonProcess(data, this.verType, isShowMsg)
    },
    commonProcess(param, type, isShowMsg) {
      /**
       * @description: 通用流程
       * @param {*}
       * @return {*}
       */
      let config = this.getConfig(type)
      config.saveParam.forEach((item) => {
        param[item] = this.goodsInfo[item];
      });
      // 保存id
      param[config.id] = this.goodsInfo[config.review_id];

      // 设置按钮状态
      if (this.type === "ven") {
        this.submitLoading = true;
        this.saveDisabled = true;
      } else {
        this.saveLoading = true;
        this.submitDisabled = true;
      }

      // 保存接口
      this.saveProcess(config, param, isShowMsg).then(() => {
        this.resetStatus()
      }).catch(() => {
        this.resetStatus()
      })
    },
   
  
    async sjAndCdProcess(param, isShowMsg) {
      /**
       * @description: 审图拆单保存提交流程
       * @param {*}
       * @return {*}
       */  
      const { client_number, goods_id, custom_goods_id } = this.goodsInfo
      param.client_number = client_number
      param.goods_id = goods_id
      // 保存
      const sjConfig = this.getConfig('sj')
      const cdConfig = this.getConfig('cd')
      // 接口参数
      const sjParam = {
        retail_price: param.retail_price,
        review_result: param.review_result,
        remark: param.remark,
        client_number: param.client_number,
        goods_id: param.goods_id,
        review_drawing_price_id: this.goodsInfo[sjConfig.review_id]
      }

      const cdParam = {
        purchase_price: param.purchase_price,
        review_result: param.review_result,
        remark: param.remark,
        client_number: param.client_number,
        goods_id: param.goods_id,
        apart_order_id: this.goodsInfo.apart_order_id
      }
      if (!sjParam[sjConfig.id] || !cdParam[cdConfig.id]) {
        this.$message.error("单号获取失败，请重新打开页面");
        return;
      }
      const configs = [{ config: sjConfig, param: sjParam }, { config: cdConfig, param: cdParam }]
      
      // 设置按钮状态
      if (this.type === "ven") {
        this.submitLoading = true;
        this.saveDisabled = true;
      } else {
        this.saveLoading = true;
        this.submitDisabled = true;
      }
      const sjSaveRes = await this.saveProcess(configs[0].config, configs[0].param, isShowMsg)
      if(sjSaveRes.body.result) {
        const cdSaveRes = await this.saveProcess(configs[1].config, configs[1].param, isShowMsg)
        if(cdSaveRes.body.result) {
          this.notSave = false;
          // 审核
          if (this.type == "ven") {
              this.ajax.postStream(
                  '/custom-web/api/reviewDrawingPrice/submitReview',
                  {
                    client_number,
                    goods_id,
                    custom_goods_id,
                    submit_id: this.goodsInfo[sjConfig.review_id],
                    apart_order_id: this.goodsInfo.apart_order_id,
                  },
                  (vres) => {
                    this.resetStatus()
                    this.$message({
                      message: vres.body.msg,
                      type: vres.body.result ? "success" : "error",
                    });
                    if (vres.body.result) {
                      // 审图审价&&审核通过则推物料
                      if (
                        this.verType === "sj" &&
                        param.review_result === "PASSING" 
                        // res.body.code === "WAITING_CHARGE" &&
                        // this.trade_type === 'SUPPLY'
                      ) {
                        pushMaterial(client_number).then((res) => {});
                        // 同步商品
                        // this.sys(client_number);
                      }
                      this.$root.eventHandle.$emit("removeTab", this.params.tabName);
                      this.$root.eventHandle.$emit(sjConfig.submit, vres);
                    } 
                  },
                  (err) => {
                    this.resetStatus()
                    this.$message({
                      message: err.body.msg,
                      type: "error",
                    });
                  }
                );
          } else {
            this.resetStatus()
          }
        } else {
          this.resetStatus()
        }
      } else {
        this.resetStatus()
      }
      // 保存接口
      // Promise.all(
      //   configs.map(item => this.saveProcess(item.config, item.param))
      // ).then((res) => {
      //   if (res.filter(item => item.body.result).length === 2) {
          
      //   } else {
      //     this.resetStatus()
      //   } 
      // }).catch(() => {
      //   this.resetStatus()
      // })
    },
    saveProcess(config, param, isShowMsg = true) {
      /**
       * @description: 保存流程
       * @param {*}
       * @return {*}
       */  
      return new Promise((resolve, reject) => {
        this.ajax.postStream(
          config.saveUrl,
          param,
          (res) => {
            isShowMsg && this.$message({
              message: res.body.msg,
              type: res.body.result ? "success" : "error",
            });
            if (res.body.result) {
              this.notSave = false;
              // 审核
              if (this.type == "ven" && this.verType !== 'sj') {
                  // 提交, verType = sj不在这里提交
                  this.verType !== 'sj' && this.verifypic(config, resolve, reject);
              } else if(this.verType === 'sj') {
                // 审图拆单直接返回
                resolve(res)
              } else {
                // 保存
                resolve(res)
              }
            } else {
              resolve(res)
            }
          },
          (err) => {
            reject()
            this.$message({
              message: err.body.msg,
              type: "error",
            });
          }
        );
      })
    },
    verifypic(config, resolve, reject) {
      /**
       * @description: 提交流程
       * @param {*}
       * @return {*}
       */
      const { client_number, goods_id, custom_goods_id } = this.goodsInfo;
      let param = {
        client_number,
        goods_id,
        custom_goods_id,
      };
      param[config.submit_id] = this.goodsInfo[config.review_id];
     
      this.ajax.postStream(
        config.submitUrl,
        param,
        (res) => {
          this.$message({
            message: res.body.msg,
            type: res.body.result ? "success" : "error",
          });
          if (res.body.result) {
            
            this.$root.eventHandle.$emit("removeTab", this.params.tabName);
            this.$root.eventHandle.$emit(config.submit, res);
          } else {
            resolve()
          }
        },
        (err) => {
          reject()
          this.$message({
            message: err.body.msg,
            type: "error",
          });
        }
      );
    },
    
    async init(type) {
      this.info_goods = await this.getGoodsInfo(type);
      this.loading = false;
      this.sysLoading = false
      this.config = this.getConfig();
      // this.verLabel = this.config.verLabel;
      // this.shLabel = this.config.shLabel;
      this.infosChange();

    },
    infosChange() {
      this.infos = [this.info_goods,this.goodsProblemDetails].filter((item) => item);
    },
    getSection(url, param, title, btns, rows) {
      const self =this
      return new Promise((resolve, reject) => {
        this.ajax.postStream(
          url,
          param,
          (d) => {
            let obj = {
              title: title,
            };
            if (d.body.result) {
              let value = d.body.content || {};
              obj.btns = btns(value);
              obj.rows = rows(value);
            } else {
              self.$message.error(d.body.msg || '出错了');
            }
            resolve(obj);
          },
          (err) => {
            self.$message.error(err.body.msg);
            resolve({});
          }
        );
      });
    },
    isNull(value) {
      return Object.keys(value).length === 0;
    },
    setValue(cols, value) {
      value = value || {};
      let isNull = this.isNull(value);
      let nullTxt = "--";
      cols.forEach((col) => {
        if (col.valueFilter) {
          if (col.valueFilter === "select") {
            let str = [];
            let vals = String(value[col.prop]).split(",");
            let options = col.options || [];
            let i;
            vals.forEach((el) => {
              i = options.findIndex((item) => item.value == el);
              str.push(i !== -1 ? options[i].label : "");
            });
            col.value = isNull ? nullTxt : str.join(",");
          } else if (col.valueFilter === "date") {
            col.value = isNull
              ? nullTxt
              : fn.dateFormat(
                  value[col.prop],
                  col.format || "yyyy-MM-dd hh:mm:ss"
                );
          } else {
            col.value = col.valueFilter(isNull ? "" : value[col.prop], value);
          }
        } else {
          col.value = value[col.prop];
        }
      });
      return cols;
    },
    handleView( row) {
      if(!row.problem_id){
              this.$message.error('请保存该问题')
              return;
            }
            // 查看图片
        imageView2(this, {
            no: {
                parent_no: row.problem_id,
            },
            client_number: this.goods_id,
            useBy: 'design'
        })
    },
    getFormData(v, idConfig) {
      // 设置表单验证
      this.ruleData = Object.assign(
        {},
        typeof this.config.ruleData === "function"
          ? this.config.ruleData(v)
          : this.config.ruleData || {}
      );
      let f = cloneDeep(this.config.formData) || [];
      f.forEach((row, index) => {
        row.cols.forEach((col) => {
          if (col.formType === "uploadFile") {
            col.event = {
              change: (v) => {
                console.log(v, this.isRefresh)
                // 第一次加载不用更新商品详情
                if(v.init) {
                  return
                }
                
                if(this.isRefresh && this.isRefresh[col.prop]) {
                  // 刷新不用更新商品详情
                  this.isRefresh[col.prop] = false
                  return
                }
                // 更新商品详情
                this.init(false);
              },
            };
            col.config = {
              parent_no: idConfig.custom_goods_id,
              child_no: idConfig[col.type],
              type: col.type,
              emit: "verifypicUpload",
            };
          }
          if (col.valueFilter) {
            col.value = col.valueFilter(v);
          }
        });
      });
      // f = JSON.parse(JSON.stringify(f))
      this.formData = f
     
    },
    async getGoodsInfo(refrshForm = true) {
      let self = this;
      // 分级评定
      let goodParam =
        this.verType === "fl"
          ? { goods_id: this.params.goodsInfo.goods_id }
          : this.params.goodsInfo.goods_id;
      const data = await this.getSection(
        this.config.goodsUrl,
        goodParam,
        "商品信息",
        (value) => {
          return [];
        },
        (value) => {
          if(value.goodsProblemDetails && value.goodsProblemDetails.length>0){
            value.goodsProblemDetails.forEach(item=>{
              item.after_ticket_no = '查看附件'
            })
            this.goodsProblemDetails = {
              btns:[],
              key:'goodsProblemDetails',
              rows:[
                        {
                            cols: [
                                { 
                                        formType: 'myTable', 
                                        span: 24,
                                        data: value.goodsProblemDetails,
                                        tableHeight: 'auto',
                                        colData: [
                                        {
                                        label: '补单内容',
                                        prop: 'supply_content',
                                        },{
                                        label: '问题原因',
                                        prop: 'problem_reason',
                                        }, {
                                        label: '责任',
                                        prop: 'problem_type',
                                        formatter: prop => ({
                                            INSTALL:"安装责任",
                                            DESIGN:"设计责任",
                                            APRAT:"拆审责任",
                                            PRODUCT:"工厂责任",
                                            TRANSPORT:"物流责任",
                                            OTHER:"其他责任",
                                            INCREMENT:"客增",
                                        }[prop]),
                                        },{
                                        label: '附件',
                                        width: '200',
                                        prop: 'after_ticket_no',
                                        redirectClick(d) {
                                           self.handleView(d)
                                        },
                                        },]
                                    }
                            ]
                        }
                    ],
              title:'问题描述'
            }
          }
          
          value.client_number_list = [value.client_number];
          value.original_client_number_list = [value.original_client_number];

          // 生成审核id
          let ids = this.ids
          // 判断是否还没保存id
          const isSaveId = value[this.config.review_id]
          value[this.config.review_id] && ( ids[0] = value[this.config.review_id] )
          value.apart_order_id && ( ids[1] = value.apart_order_id )
          value[this.config.review_id] = ids[0]
          value.apart_order_id = ids[1]

         
          this.goodsInfo = value;

          let key = Math.random();
          let goodsFileIndex = this.config.detail.findIndex(
            (item) => item.prop === "goodsFile"
          );
          if (goodsFileIndex !== -1) {
            this.config.detail[goodsFileIndex].key = key;
            this.config.detail[goodsFileIndex].params = {
              order_no: value.custom_goods_id,
              verType: this.verType,
            };
          }
          // 审图拆单包含审图审价上传文件id，拆单文件上传id
          let idsConfig = {
              custom_goods_id: value.custom_goods_id,
              sj: ids[0],
              cd: 'cd' + ids[1],
              cs: 'cs' + ids[0],
              // 原拆单
              ocd:'cd' + ids[0]
          }
          refrshForm && this.getFormData(value, idsConfig);
          
          // 保存id
          !isSaveId && this.saveInfo({ 
            review_result: 'PASSING'
          }, false)

          return [
            {
              cols: this.setValue(this.config.detail, value),
            },
          ];
        }
      );
      data.key = "goodsInfo";
      return data;
    },
  },
};
</script>
<style scoped>
.title {
  padding-left: 10px;
  border-left: 4px solid #409eff;
}
.title span {
  font-size: 15px !important;
}
.info {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 0 20px 0 50px !important;
}
.info-container {
  padding-bottom: 40px;
  line-height: 16px;
}
</style>
