<!-- 修改商品 -->
<template>
	<div class='xpt-flex'>
		<xpt-headbar>
			<el-button size='mini' type="primary" @click="save('menu')" slot='left'  >提交</el-button>
            <el-button type='danger' size='mini' @click="close" slot='left'>关闭</el-button>
		</xpt-headbar>
            
		<el-row  v-if="params.message">
			<div style="color:red;    margin-bottom: 10px; margin-top: 10px;">*{{params.message}}</div>
		</el-row>
		
		
		<el-row>
			<el-form>
				<el-col :span="22">
				<el-form-item label="修改内容说明">
					<el-checkbox-group v-model="customer.changeContent" style="display:inline-block;">
						<el-checkbox v-for="item in typeOptions" :key="item.id" :label="item.code" >{{item.name}}</el-checkbox>
					</el-checkbox-group>
			
			</el-form-item>
			</el-col>
			<el-col :span='22' class="addHeight">
				<el-form-item label="说明">
					<el-input type='textarea' v-model="customer.remark" size='mini' style="width:80%" :maxlength='200' resize="none"></el-input>
				</el-form-item>
				</el-col> 
			</el-form>
		</el-row>
		<xpt-list 
			:data='goodsList' 
			:colData='cols' 
            :btns='[]'
			@selection-change='selectionChange'
			selection='checkbox'
            ref='msgList'
		></xpt-list>
	</div>
</template>
<script>

export default {
	props: ['params'],
	
	data() {
		let self = this
		return {
			customer:{
				changeContent:[],
				remark:''
			},
			goodsList:[],
			selectData:[],
			typeOptions:[],
			cols: [
				{
					label: '商品号',
					prop: 'goods_id',
				}, {
					label: '商品名称',
					prop: 'message',
				}, {
					label: '空间',
					prop: 'design_room_name',
				}, {
					label: '商品状态',
					prop: 'goods_status_cn'
				},
			],
			
		
            list:[],
			msgBtns: [
				{
					type: 'info',
					txt: '提交',
					click: this.save
				}, {
					type: 'info',
					txt: '取消',
					click: () => {
						// this.showExportList("EXCEL_TYPE_MSG_LOG")
						// this.params.callback(this.selectData);
						
					}
				}
			]
		}
	},
 
	methods: {
		close(){
			this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
		},
       selectionChange(obj) {
			this.selectData = obj;
		},
		save(){
			let self = this;
			if(!this.selectData.length){
				this.$message.error('请选择商品');
				return;
			}
			// console.log()

			if(!this.customer.changeContent.length &&!this.customer.remark){
				this.$message.error('标签和备注必填一个');
				return;
			}
			let content = '';
			this.customer.changeContent.forEach(item=>{
				this.typeOptions.forEach(oItem=>{
					if(item == oItem.code){
						content += oItem.name+',';
					}
				})
			})
			if(self.params.message){
             self.$root.eventHandle.$emit("openDialog", {
                txt:self.params.message,
                okTxt: "确定",
                cancelTxt: "取消",
                noShowNoTxt: true,
                ok() {
                  self.ajax.postStream('/custom-web/api/customGoods/changeGoods',
						{
							changeContent:content,
							remark:self.customer.remark,
							message:self.params.message,
							goodsList:self.selectData,
						},(res) =>{
							let body = res.body
							if(body.result){
								// resolve(body.content)
								self.$message.success(body.msg);
								self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
							} else {
								self.$message.error(body.msg);
							}
					})
                },
                cancel() {
                    self.$message.success("已取消");
                },
            });
          }else{
            self.ajax.postStream('/custom-web/api/customGoods/changeGoods',
						{
							changeContent:content,
							remark:self.customer.remark,
							message:self.params.message,
							goodsList:self.selectData,
						},(res) =>{
							let body = res.body
							if(body.result){
								// resolve(body.content)
								self.$message.success(body.msg);
								self.$root.eventHandle.$emit("removeAlert", this.params.alertId);
							} else {
								self.$message.error(body.msg);
							}
					})
          }
			
		},
     
		},
       
	mounted(){
		// console.log(this.params.goodsList);
       this.goodsList = this.params.goodsList
		this.typeOptions = __AUX.get('custom_change_goods')

	},
}	
</script>
<style type="text/css" scoped>
.el-input{
	width: 150px;
}
</style>
