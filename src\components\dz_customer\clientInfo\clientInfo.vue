<template>
  <!-- 订单详情页面 -->
  <div class="info" v-loading="loading" element-loading-text="拼命加载中">
    <div class="info-container">

      <info v-for="info in infos" :key="info.key" :info="info" :aboutNumber="clientInfo.client_number"/>
    </div>
    <top el=".info"></top>
    <div class="nav">
      <div v-for="item in infos" :key="item.key">
        <a :href="'#' + item.key">{{ item.title }}</a>
        <div v-if="item.rows">
          <div v-for="el in item.rows" :key="el.key" class="child">
            <a v-if="el.key" :href="'#' + el.key">{{ el.title }}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import info from "./info";
import top from "../components/top/top";
import {
  addDesign,
  addDesignAssess,
  addCheck,
  addConnect,
  addContract,
  confirmContract,
  tips,
} from "../alert/alert";
import fn from "@/common/Fn.js";
import {
  getRole,
  inspectPaymentStatus,
  getFileList,
  getGoods,
  isHaveGoods,
  viewRegister,
  getViewRegister,
  getCustomCommunication,
  getShopLBInfo
} from "../common/api";
import { getMap as getClientMap } from "../common/clientDictionary";
import { getMap as getCompactMap } from "../common/compactDictionary";
import { getMap as getFollowMap } from "../common/followDictionary";
import btnStatus from "../common/mixins/btnStatus";
import infoMix from "../common/mixins/info";
export default {
  props: ["params"],
  mixins: [btnStatus, infoMix],
  components: { info, top },
  data() {
    return {
      loading: true,
      infos: [],
      getGoodsList:[],
      sourceData:[],
      orderTagAux: __AUX.get('store_order_type').reduce((a, b) => {
					a[b.code] = b.name
					return a
				}, {}),
    };
  },
  async mounted() {
    this.currentKey = new Date().getTime();
    // 获取字典
    this.maps = await this.getMap();
    // 获取角色
    this.role = await getRole();
    this.refresh({
      measure: true,
      design: true,
      contract: true,
      order: true,
      follow: true,
      communication: true,
    });
    // 获取跟进情况
    // const follow = this.getFollow()
    this.$root.eventHandle.$on("refreshclientList", this.refreshClient);
    this.$root.eventHandle.$on("refreshmeasureList", this.refreshMeasure);
    this.$root.eventHandle.$on("refreshcompactList", this.refreshCompact);
    this.$root.eventHandle.$on("refreshdesignList", this.refreshDesign);
    this.$root.eventHandle.$on("saveCustomGoods", this.refreshOrder);
    // 添加查房
    this.$root.eventHandle.$on("saveViewRegister", this.refreshViewRegister);
    // 添加沟通
    this.$root.eventHandle.$on("saveCustomCommunication", this.refreshFollow);
  },
  beforeDestroy() {
    this.$root.eventHandle.$off("refreshclientList", this.refreshClient);
    this.$root.eventHandle.$off("refreshmeasureList", this.refreshMeasure);
    this.$root.eventHandle.$off("refreshcompactList", this.refreshCompact);
    this.$root.eventHandle.$off("refreshdesignList", this.refreshDesign);
    this.$root.eventHandle.$off("saveCustomGoods", this.refreshOrder);
    this.$root.eventHandle.$off("saveCustomCommunication", this.refreshFollow);
  },
  methods: {
    checkDZJ(){
      this.ajax.postStream("/custom-web/api/equityGoods/checkDZJ", {client_number:this.clientInfo.client_number}, res => {
				if(res.body.result) {
					this.equityGoods();
				} else {
					this.$message.error(res.body.msg)
				}

			}, err => {

			});
    },
    /**定制金列表**/
    equityGoods() {
        this.$root.eventHandle.$emit('alert', {
            params: {
                shop_code:this.clientInfo.shop_code,
                client_number:this.clientInfo.client_number,
                callback: data => {
                  if (!data ) return;


                },
            },
            component: () => import('./equityGoods'),
            style: 'width:500px;height:300px',
            title: '线上订单核销',
        })
    },
    clientInfo(){
      this.ajax.postStream("/order-web/api/scmsystrade/verifyDZJOrder", {tid:this.clientInfo.tid,verifyOrderNo:this.clientInfo.verifyOrderNo}, res => {
				if(res.body.result) {

				} else {
					this.$message.error(res.body.msg)
				}

			}, err => {

			});
    },
    getUserInfo() {
      let userInfo = sessionStorage.getItem("userInfo")
        ? JSON.parse(sessionStorage.getItem("userInfo"))
        : {};
      return userInfo.user || {};
    },
    async refresh(type = {}) {
      this.info_clientInfo = await this.getClientInfo();
      this.loading = false;
      this.infosChange();
      // 获取量尺信息
      if (type.measure) {
        this.getMeasure().then((measure) => {
          this.info_measure = measure;
          this.infosChange();
        });
      }
      // 获取设计方案
      if (type.design) {
        this.getDesign().then((obj) => {
          this.info_design = obj.design;
          this.info_order = obj.order;
          this.infosChange();
        });
      }
      // 获取合同
      if (type.contract) {
        this.getContract().then((contract) => {
          this.info_contract = contract;
          this.infosChange();
        });
      }


      // 获取留资
      // if (this.params.customerInfo.crm_clue_id) {
        this.getClue().then((clue) => {
          this.info_clue = clue;
          this.infosChange();
        });
      // 获取收款信息
      this.getTollInquiry().then((tollInquiry) => {
          this.info_tollInquiry = tollInquiry;
          this.infosChange();
        });

        this.verifyTrade().then((verifyTrade) => {
          this.info_verifyTrade = verifyTrade;
          this.infosChange();
        });

      //    获取订单
      // if (type.follow) {
      //   this.getFollow().then((follow) => {
      //     this.info_follow = follow;
      //     this.infosChange();
      //   });
      // }
      if (type.communication) {
        this.info_communication = this.getComunication();
        this.infosChange();
      }
    },
    infosChange() {
      this.infos = [
        this.info_summary,
        this.info_clientInfo,
        this.info_clue,
        this.info_verifyTrade,
        this.info_measure,
        this.info_tollInquiry,
        this.info_contract,
        this.info_design,
        this.info_order,
        this.info_follow,
        this.info_communication,
      ].filter((item) => item);
    },
    async refreshClient() {
      this.refresh();
    },
    async refreshFollow(e = {}) {
      // 添加沟通后更新
      let param = e.param || {};
      this.refresh({
        contract: param.if_complete === "0",
        follow: true,
      });
    },
    async refreshMeasure() {
      this.refresh({
        measure: true,
        design: true,
      });
    },
    async refreshCompact() {
      this.refresh({
        measure: true,
        design: true,
        contract: true,
      });
    },
    async refreshDesign() {
      // 刷新设计
      this.refresh({
        design: true,
        contract: true,
        follow: true,
      });
    },
    async refreshOrder() {
      // 刷新设计
      this.refresh({
        design: true,
      });
    },
    refreshViewRegister() {
      !this.viewRegisterRecord && (this.viewRegisterRecord = []);
      getViewRegister({ client_number: this.clientInfo.client_number }).then(
        (list) => {
          this.viewRegisterRecord.splice(0, this.viewRegisterRecord.length);
          list.forEach((item) => {
            this.viewRegisterRecord.unshift({
              date:
                fn.dateFormat(item.create_time, "yyyy-MM-dd hh:mm:ss") +
                "——" +
                item.real_name,
              rows: [
                {
                  cols: [
                    // {formType: 'myRate', label: '方案评价', value: Number(item.design_evaluation), span: 8, disabled: true},
                    {
                      label: "查房时间",
                      value: fn.dateFormat(
                        item.create_time,
                        "yyyy-MM-dd hh:mm:ss"
                      ),
                      span: 16,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      label: "查房评价",
                      value: item.view_evaluation,
                      notellipsis: true,
                      span: 24,
                    },
                    {
                      label: "备注",
                      notellipsis: true,
                      value: item.remark,
                      span: 24,
                    },
                  ],
                },
              ],
            });
          });
        }
      );
    },
    refreshCustomCommunication() {
      return new Promise((resolve, reject) => {
        // 获取沟通记录
        !this.customCommunication && (this.customCommunication = []);
        getFollowMap((map) => {
          getCustomCommunication({
            client_number: this.clientInfo.client_number,
          }).then((list) => {
            this.customCommunication.splice(0, this.customCommunication.length);
            list.forEach((item) => {
              this.customCommunication.unshift({
                title: fn.dateFormat(
                  item.communication_time,
                  "yyyy-MM-dd hh:mm:ss"
                ),
                tag: item.if_complete == 0 ? "完" : "跟",
                tagBack: item.if_complete == 0 ? "#13CE66" : "#FF4949",
                cols: this.setValue(
                  [
                    {
                      label: "下次沟通时间",
                      prop: "next_communication_time",
                      valueFilter: "date",
                      span: 12,
                    },
                    {
                      label: "是否沟通完毕",
                      valueFilter: "select",
                      prop: "if_complete",
                      span: 12,
                      options: [
                        { label: "是", value: 0 },
                        { label: "否", value: 1 },
                      ],
                      span: 12,
                    },
                    {
                      label: "沟通情况",
                      notellipsis: true,
                      prop: "communication_situation",
                      span: 24,
                    },
                    {
                      prop: "wait_deal_point",
                      valueFilter: "select",
                      options: map.communication,
                      label: "待成交关键点",
                      span: 24,
                    },
                    {
                      label: "修改意见",
                      prop: "revise_opinion",
                      notellipsis: true,
                      span: 12,
                    },
                  ],
                  item
                ),
              });
            });
            resolve(
              this.customCommunication.findIndex(
                (item) => item.if_complete == 0
              ) !== -1
            );
          });
        });
      });
    },
    getMap() {
      let maps = [
        new Promise((resolve, reject) => {
          getClientMap((clientMap) => {
            resolve(clientMap);
          });
        }),
        new Promise((resolve, reject) => {
          getCompactMap((compactMap) => {
            resolve(compactMap);
          });
        }),
      ];
      return Promise.all(maps);
    },

    refreshBtns() {
      this.infos.forEach((item) => {
        if (item.btns) {
          item.btns.forEach((el) => {
            if (el.show) {
              el.isShow = el.show(
                this.info_clientInfo.client_status,
                this.role
              );
            }
          });
          item.btns = item.btns.filter(
            (el) => el.isShow === undefined || el.isShow
          );
        }
      });
    },
    // 过去交流意见
    getComunication() {
      return {
        title: "交流意见",
        key: `communication${this.currentKey}`,
        noKey: true,
        rows: [
          {
            cols: [
              {
                formType: "communication",
                locationKey: `communication${this.currentKey}`,
                params: {
                  client_number: this.clientInfo.client_number,
                },
                span: 24,
              },
            ],
          },
        ],
      };
    },
    // 过去交流意见
    async getTollInquiry() {
      return {
        title: "收款记录",
        key: `tollInquiry${this.currentKey}`,
        noKey: true,
        rows: [
          {
            cols: [
              {
                formType: "tollInquiry",
                locationKey: `tollInquiry${this.currentKey}`,
                params: {
                  client_number: this.clientInfo.client_number,
                },
                span: 24,
              },
            ],
          },
        ],
      };
    },
    async getContract() {
      let _self = this;
      let clientInfo = _self.params.customerInfo;
      let map = _self.maps[1];
      let fileList = [];
      const data = await _self.getSection(
        "/custom-web/api/customCompact/getCompactInfoByClient",
        clientInfo.client_number,
        "合同",
        (value) => {
          let d = _self.clientInfo;
          let data = {
            client_number: d.client_number,
            client_name: d.client_name,
            client_mobile: d.client_mobile,
            designer_number: d.designer_number,
            designer_name: d.designer_number,
            is_urgent: value.is_urgent ,
            compact_number:d.client_number+(new Date().getMonth()+1)+new Date().getDate(),
            compact_date:new Date(),
          };
          // 下推采购之前可以允许修改合同是否加急，其余禁用
          let client_status = _self.maps[0].client_status
          let currentClientStatusCodeIndex =  client_status.findIndex(item => item.value === d.client_status)
          let currentClientStatusCode = currentClientStatusCodeIndex !== -1 ? Number(client_status[currentClientStatusCodeIndex].v) : ''
          let canOnlyUrgent = currentClientStatusCode  ? currentClientStatusCode > 71 && currentClientStatusCode <= 180   : false
          let status = _self.isContract(_self.clientInfo, _self.role, client_status)
          let ifConfirm = ['WAITING_DESIGN','IN_DESIGN','REJECT_VERIFY',].includes(_self.clientInfo.client_status);
          let btns = [];
          if(status){
            btns.push({
                  txt: "合同登记",
                  type: "primary",
                  click: async () => {
                    let flag = _self.getGoodsList.some(item=>{
                      return ['IN_SPLIT_AUDIT','WAITING_COLLECTION','WAITING_CLASSIFICATION','WAITING_PUSH_PURCHASE','WAITING_PUSH_GOODS','COMPLETED','IN_DESIGN','IN_CLASSIFICATION','REJECT_WAITING_AUDIT','REJECT_WAITING_SPLIT_ORDER','REJECT_PUSH_PURCHASE','WAITING_OUTBOUND','SPLIT_AUDIT_COMPLETE'].includes(item.goods_status)
                    });
                    if(flag){
                      _self.$message.error('商品拆审以后不能进行合同登记');
                      return;
                    }
                    // 获取店铺拎包信息
                    let if_bag_show=false
                    await getShopLBInfo({"shopCode":_self.clientInfo.shop_code,"uncheck" : true}).then(res=>{
                      if_bag_show=res.data==1?true:false
                    })
                    const tip = {
                      component: `
                        <template>
                          <div>
                            没有收费记录，不能进行合同登记。<a>去收费</a>
                          </div>
                        </template>
                      `
                    };
                    let if_bag="Y"
                      if_bag_show?addContract(_self, Object.assign(value, data, { canOnlyUrgent,if_bag_show,if_bag })):addContract(_self, Object.assign(value, data, { canOnlyUrgent,if_bag_show }));
                  },
                })
          }
          if(ifConfirm){
            btns.push({
                  txt: "合同变更",
                  type: "primary",
                  click: async () => {
                   confirmContract(_self, Object.assign(value, data, {  }))
                  },
                },)
          }
          return btns
        },
        (value) => {
          value.compact_id &&
            getFileList({
              order_no: value.compact_id,
              page_no: 1,
              page_size: 10000,
            }).then((res) => {
              res.forEach((item) => {
                fileList.push(item);
              });
            });
          return [
            {
              cols: _self.setValue(
                [
                  {
                    prop: "compact_date",
                    valueFilter: "date",
                    format: "yyyy-MM-dd",
                    label: "合同登记时间",
                    span: 8,
                  },
                  { prop: "compact_sum", label: "合同总金额", span: 8 },
                  { prop: "compact_number", label: "合同编号", span: 8 },
                  // {prop: 'create_time',label: '518套餐', value: '无', span: 8},
                  {
                    prop: "choose_reason",
                    label: "购买原因",
                    valueFilter: "select",
                    options: map.choose_reason,
                    span: 16,
                  },
                  {
                    prop: "combo_type",
                    label: "套餐类型",
                    valueFilter: "select",
                    options: map.combo_type,
                    span: 8,
                  },
                  {
                    prop: "remark",
                    label: "合同备注",
                    notellipsis: true,
                    span: 8,
                  },
                  {
                    formType: "fileList",
                    prop: "files",
                    label: "相关文件",
                    params: {
                      preview: true
                    },
                    valueFilter() {
                      return fileList;
                    },
                    span: 12,
                  },
                ],
                value
              ),
            },
          ];
        }
      );
      data.key = `contract${this.currentKey}`;
      return data;
    },
     async getClue() {
      let _self = this;
      let clientInfo = _self.params.customerInfo;

      const data = await _self.getSection2(
        "/custom-web/api/customMeasure/clue?id="+clientInfo.crm_clue_id,
        {id:clientInfo.crm_clue_id},
        "留资信息",
        (value) => {
          let d = _self.clientInfo;
          return [];

        },
        (value) => {

          return [
            {
              cols: _self.setValue(
                [

                  { prop: "informationPlatformName", label: "留资平台", span: 8 },
                  { prop: "appointmentChannelName", label: "留资渠道", span: 8 },
                  {
                    prop: "appointmentTime",
                    label: "留资预约时间",
                    valueFilter: "date",
                    format: "yyyy-MM-dd",
                    span: 8,
                  },
                  {
                    prop: "guiderFollowCount",
                    label: "导购跟进次数",
                    span: 8,
                  },
                  {
                    prop: "guiderFirstFollowTime",
                    label: "导购首次跟进时间",
                    valueFilter: "date",
                    format: "yyyy-MM-dd",
                    span: 8,
                  },
                  {
                    prop: "guiderFirstFollowTag",
                    label: "导购首次跟进标签",
                    span: 8,
                  },
                  {
                    prop: "guiderFirstFollowRemark",
                    label: "导购首次跟进备注",

                    notellipsis: true,
                    span: 8,
                  },
                  {
                    prop: "guiderLastFollowTime",
                    label: "导购最近跟进时间",
                    valueFilter: "date",
                    format: "yyyy-MM-dd",
                    span: 8,
                  },
                  {
                    prop: "guiderLastFollowTag",
                    label: "导购最近跟进标签",
                    notellipsis: true,
                    span: 8,
                  },
                  {
                    prop: "guiderLastFollowRemark",
                    label: "导购最近跟进备注",
                    notellipsis: true,
                    span: 8,
                  },
                ],
                value
              ),
            },
          ];
        }
      );
      data.key = `clue${this.currentKey}`;
      return data;

    },
    async verifyTrade() {
      let _self = this;
      let clientInfo = _self.params.customerInfo;
      let map = _self.maps[1];
      const data = await _self.getSection(
        "/custom-web/api/customSysTrade/verifyTrade",
        {client_number:clientInfo.client_number},
        "核销订单信息",
        (value) => {
          let d = _self.clientInfo;
          // let data = {
          //   client_number: d.client_number,
          //   client_name: d.client_name,
          //   client_mobile: d.client_mobile,
          //   designer_number: d.designer_number,
          //   designer_name: d.designer_number,
          //   is_urgent: value.is_urgent
          // };

        },
        (value) => {

          return [
            {
              cols: _self.setValue(
                [

                  { prop: "verify_man", label: "核销人", span: 8 },
                  { prop: "equity_name", label: "权益名称", span: 8 },
                  // {prop: 'create_time',label: '518套餐', value: '无', span: 8},
                  {
                    prop: "verify_time",
                    label: "核销时间",
                    valueFilter: "date",
                    format: "yyyy-MM-dd",
                    span: 8,
                  },
                  {
                    prop: "verify_amount",
                    label: "核销金额",
                    span: 8,
                  },
                  {
                    prop: "tid",
                    label: "平台订单号",
                    notellipsis: true,
                    span: 8,
                  },
                ],
                value
              ),
            },
          ];
        }
      );
      data.key = `verifyTrade${this.currentKey}`;
      return data;
    },
    goodsdetail(d) {
      //商品详情
      this.$root.eventHandle.$emit("creatTab", {
        name: "商品详情",
        component: () =>
          import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
        params: {
          goodsInfo: d,
        },
      });
    },
    async getFollow() {
      var _self = this;
      const isCom = await this.refreshCustomCommunication();
      let isCommunicate = this.isCommunicate(this.clientInfo, this.role);
      return {
        key: `follow${this.currentKey}`,
        title: "客户跟进",
        btns: isCommunicate
          ? [
              {
                txt: "沟通登记",
                type: "primary",
                show: isCom,
                click: () => {
                  // 至少有一次查房记录
                  // if(!this.viewRegisterRecord.length) {
                  //     this.$message.warning('请先进行查房')
                  //     return
                  // }
                  addConnect(this, this.clientInfo, this.designRoomList);
                },
              },
            ]
          : [],
        rows: this.customCommunication,
      };
    },
    async getDesign() {
      let _self = this;
      let clientInfo = _self.params.customerInfo;
      let isDesignAccess = _self.isDesignAssess(_self.clientInfo, _self.role);
      let order;
       let dz_split_store_logo = false;
       __AUX.get('dz_split_store_logo').forEach( item => {
            if( item.code == this.clientInfo.shop_code ){
            dz_split_store_logo = true
          }
        });
      const access = await _self.getSection(
        "/custom-web/api/customDesign/getRegister",
        { client_number: clientInfo.client_number },
        "设计自评",
        (accessValue) => {
          return isDesignAccess
            ? [
                {
                  txt: "提交",
                  type: "primary",
                  show(clientInfo, row) {
                    return _self.isDesignAssess(clientInfo, role);
                  },
                  click: async () => {
                    let canAdd = await isHaveGoods(
                      this.clientInfo.client_number
                    );
                    const tip = "没有添加商品，不能进行自评。";
                    if (canAdd) {
                      addDesignAssess(_self, _self.clientInfo);
                    } else {
                      tips(_self, tip);
                    }
                  },
                },
              ]
            : [];
        },
        (accessValue) => {
          return [
            {
              cols: _self.setValue(
                [
                  {
                    prop: "design_self_evaluation",
                    formType: "myRate",
                    label: "方案自评",
                    value: 3,
                    span: 16,
                    disabled: true,
                  },
                  {
                    prop: "create_time",
                    valueFilter: "date",
                    label: "登记时间",
                    value: "2019-09-12 16：35",
                    span: 8,
                  },
                ],
                accessValue
              ),
            },
            {
              cols: _self.setValue(
                [
                  {
                    prop: "reserve_come_time",
                    valueFilter: "date",
                    format: "yyyy-MM-dd",
                    label: "邀约进店时间",
                    value: "2019-09-12 16：35",
                    span: 8,
                  },
                  {
                    prop: "deal_point",
                    label: "成交关键点",
                    value: "客户着急入住，早出方案早邀约",
                    span: 8,
                  },
                  // {prop: 'create_time',label: '登记次数', value: '1', span: 8},
                ],
                accessValue
              ),
            },
          ];
        }
      );
      access.cols = access.rows[0].cols;
      let cols = access.rows[1];
      delete access.rows;
      access.key = `access${this.currentKey}`;
      const data = await _self.getSection(
        "/custom-web/api/customDesign/get",
        { client_number: clientInfo.client_number },
        "合同下单",
        (value) => {
          let isDesign = _self.isDesign(_self.clientInfo, _self.role);
          let isDesignNew = _self.isDesignNew(_self.clientInfo, _self.role);

          return isDesign || isDesignNew
            ? [
                {
                  txt: `${isDesignNew ? "" : "修改"}合同下单`,
                  type: "primary",
                  show(clientInfo, row) {
                    return (
                      _self.isDesign(clientInfo, role) ||
                      _self.isDesignNew(clientInfo, role)
                    );
                  },
                  click: () => {
                    addDesign(
                      _self,
                      Object.assign({}, _self.clientInfo, value)
                    );
                  },
                },
              ]
            : [];
        },
        (value) => {
          order = _self.getOrder(value);
          // 获取文件
          value.custom_design_id &&
            getFileList({
              order_no: value.custom_design_id,
              page_no: 1,
              page_size: 10000,
            }).then((res) => {
              let imageMap = {};
              let images = res;
              images.forEach((item) => {
                !imageMap[item.sub_order_no] &&
                  (imageMap[item.sub_order_no] = []);
                imageMap[item.sub_order_no].push(item.path);
              });
              value.designRoomList = Array.isArray(value.designRoomList)
                ? value.designRoomList
                : [];
              _self.designRoomList = value.designRoomList;
              const ids = value.designRoomList.map((item) => {
                return {
                  images: imageMap[item.design_room_id] || [],
                  name: item.design_room_name,
                  parent_no: value.custom_design_id,
                  client_number: this.clientInfo.client_number,
                  child_no: item.design_room_id,
                  detail: item.design_point,
                  useBy: "design",
                };
              });
              ids.forEach((item) => {
                measure_room_ids.push(item);
              });
            });
          let measure_room_ids = [];
          // 获取查房记录
          this.refreshViewRegister(clientInfo.client_number);
          let isCheck = this.isCheck(this.clientInfo, this.role);
          order.cols = []
          order.rows.forEach(item => {
            order.cols = order.cols.concat(item.cols)
          })
          return [
            {
              cols: [
                {
                  formType: "tips",
                  params: {
                    value: dz_split_store_logo,
                  },
                  span: 24,
                },
              ],
            },
            {
              cols: [
                {
                  formType: "imageList",
                  params: {
                    value: measure_room_ids,
                  },
                  span: 24,
                },
              ],
            },
            order,
            // access,
            // cols,
            // {
            //   key: `view${this.currentKey}`,
            //   title: "查房记录",
            //   btns: isCheck
            //     ? [
            //         {
            //           txt: "查房登记",
            //           type: "primary",
            //           click: () => {
            //             addCheck(this, clientInfo.client_number);
            //           },
            //         },
            //       ]
            //     : [],
            //   cols: [
            //     {
            //       formType: "timeline",
            //       value: this.viewRegisterRecord,
            //       span: 24,
            //     },
            //   ],
            // },
          ];
        }
      );
      data.key = `design${this.currentKey}`;
      return {
        // order: order,
        design: data,
      };
    },
    getOrder(value) {
      let clientInfo = this.params.customerInfo;
      let map = this.maps[1];
      let totalNum = 0;
      let data = {};

      let btns = (value) => {
        let isGoodsList = this.isGoodsList(
          Object.assign({}, this.clientInfo, {
            if_disallow: value.if_disallow,
          }),
          this.role
        );
        return isGoodsList
          ? [
              {
                txt: `商品`,
                type: "primary",
                click: () => {
                  this.$root.eventHandle.$emit("creatTab", {
                    name: "商品列表",
                    component: () =>
                      import("@components/dz_customer/goodsList.vue"),
                    params: {
                      canCreate: value.if_disallow !== "true",
                      row: Object.assign(
                        {
                          shop_code: this.clientInfo.shop_code,
                          shop_name: this.clientInfo.shop_name,
                          client_name: this.clientInfo.client_name,
                          client_mobile: this.clientInfo.client_mobile,
                          client_status: this.clientInfo.client_status
                        },
                        value
                      ),
                    },
                  });
                },
              },
            ]
          : [];
      };
      let rows = (value) => {
        let designRoomList = Array.isArray(value.designRoomList)
          ? value.designRoomList.map((item) => {
              item.data = [];
              return item;
            })
          : [];
          let goodsList = [];
        designRoomList.length &&
          getGoods({
            client_number: clientInfo.client_number,
          }).then((list) => {
            this.getGoodsList = list;
            list.forEach((item, index) => {
              totalNum += item.act_price;
              let i = designRoomList.findIndex(
                (el) => el.design_room_id == item.design_room_id
              );
              if (i !== -1) {
                designRoomList[i].data.push(item);
              }
              goodsList.push(item);
              if (index === list.length - 1) {
                document.querySelector(`#totalNum${this.currentKey}>span`).innerText = '￥'+totalNum.toFixed(2)
              }
            });
          });
        let self = this;

        return [
            {
              cols: [
                {
                  formType: {
                  template:`<div id="totalNum${this.currentKey}" style="text-align: right;">订单实际价格汇总：<span style="text-align:right; font-size: 18px !important; font-weight: bold;">￥0</span></div>`
                },
                span: 24
                },
              ],
            },
            {
              cols: [

              {
                title: '商品列表',
                formType: "myTable",
                span: 24,
                data: goodsList,
                tableHeight: "auto",
                colData: [
                  {
                    label: "商品号",
                    prop: "goods_id",
                    width: "150",
                    redirectClick(d) {
                      self.goodsdetail(d);
                    },
                  },
                  {
                    label: "三维家商品编号",
                    prop: "swj_goods_no",
                    width: "160"
                  },
                  {
                    label: "商品名称",
                    prop: "message",
                    width: "300"
                  },
                  {
                    label: "商品类型",
                    prop: "type_code_name",
                    width: "120"
                  },
                  {
                    label: "空间",
                    prop: "design_room_name",
                    width: "120"
                  },
                  {
                    label: "风格",
                    prop: "style_cn",
                  },
                  {
                    label: "类目",
                    prop: "category_cn",
                  },
                  {
                    label: "材质",
                    prop: "material_cn",
                  },
                  {
                    label: "颜色",
                    prop: "color_cn",
                  },
                  {
                    label: "商品状态",
                    prop: "goods_status_cn",
                  },
                  // {
                  // label: '零售价',
                  // prop: 'retail_price',
                  // width: 220,
                  // },
                  {
                    label: "实际价格",
                    prop: "act_price",
                    width: 180,
                  },
                ],
              },
            ],
            }
          ];
      };
      data = {
        key: `order${this.currentKey}`,
        title: "商品列表",
        btns: btns(value),
        rows: rows(value),
      };
        return data
    },
    async getMeasure() {
      let _self = this;
      let clientInfo = _self.params.customerInfo;
      let map = _self.maps[1];
      const data = await _self.getSection(
        "/custom-web/api/customMeasure/get",
        { client_number: clientInfo.client_number },
        "量尺信息",
        (value) => {
          let isMeasure = _self.isMeasure(_self.clientInfo, _self.role);
          let isMeasureNew = _self.isMeasureNew(_self.clientInfo, _self.role);
          return isMeasure || isMeasureNew
            ? [
                {
                  txt: `${isMeasureNew ? "" : "修改"}量尺登记`,
                  type: "primary",
                  show(clientInfo, row) {
                    return (
                      _self.isMeasure(clientInfo, role) ||
                      _self.isMeasureNew(clientInfo, role)
                    );
                  },
                  click: () => {
                    const {
                      client_number,
                      client_name,
                      client_type,
                      client_mobile,
                      designer_number,
                      designer_name,
                    } = this.clientInfo;
                    const data = {
                      client_number,
                      client_name,
                      client_type,
                      client_mobile,
                      designer_name,
                      designer_number,
                    };
                    let param = Object.assign({}, value, data);
                    this.$root.eventHandle.$emit("creatTab", {
                      name: "量尺登记",
                      component: () =>
                        import("@components/dz_customer/measureEdit.vue"),
                      params: {
                        measureInfo: param,
                        customerInfo: param,
                        isEdit: !this.isNull(value),
                      },
                    });
                  },
                },
              ]
            : [];
        },
        (value) => {
          value.custom_measure_id &&
            getFileList({
              order_no: value.custom_measure_id,
              page_no: 1,
              page_size: 10000,
            }).then((res) => {
              let imageMap = {};
              let images = res;
              images.forEach((item) => {
                !imageMap[item.sub_order_no] &&
                  (imageMap[item.sub_order_no] = []);
                imageMap[item.sub_order_no].push(item.path);
              });
              const ids = value.measureRoomList.map((item) => {
                return {
                  images: imageMap[item.measure_room_id] || [],
                  name: item.measure_room_name,
                  parent_no: value.custom_measure_id,
                  client_number: this.clientInfo.client_number,
                  child_no: item.measure_room_id,
                  detail: item.design_point,
                  useBy: "measure",
                };
              });
              ids.forEach((item) => {
                measure_room_ids.push(item);
              });
            });

          let measure_room_ids = [];
          return [
            {
              cols: this.setValue(
                [
                  { prop: "client_property_cn", label: "客户性质", span: 8 },
                  {
                    prop: "measure_time",
                    valueFilter: "date",
                    label: "量尺时间",
                    span: 8,
                  },
                  {
                    prop: "plan_communication_time",
                    valueFilter: "date",
                    label: "计划首次沟通时间",
                    span: 8,
                  },
                  { prop: "life_stage_cn", label: "生活阶段", span: 8 },
                  { prop: "decoration_stage_cn", label: "装修阶段", span: 8 },
                  { prop: "plan_stay_cn", label: "计划入住时间", span: 8 },
                  // {prop: 'none',label: '量尺性质', span: 8},
                  // {prop: 'none',label: '客户职业',  span: 8},
                  // {prop: 'none',label: '户型', span:8},
                  { prop: "total_area_cn", label: "面积", span: 8 },
                  { prop: "house_price_cn", label: "房价", span: 8 },
                  { prop: "budget_cn", label: "预算", span: 8 },
                  { prop: "member_cn", label: "家庭成员", span: 8 },
                  { prop: "decoration_style_cn", label: "装修风格", span: 8 },
                  // {prop: 'none',label: '其他', span: 8},
                  // {prop: 'none',label: '预购家具', span: 16},
                  // {prop: 'none',label: '喜欢主材颜色',span: 8},
                  { prop: "house_type_cn", label: "房屋类型", span: 8 },
                  { prop: "consume_type_cn", label: "消费类型", span: 8 },
                  {
                    prop: "remark",
                    label: "量尺备注",
                    notellipsis: true,
                    span: 24,
                  },
                  {
                    prop: "measureRoomList",
                    formType: "textlist",
                    label: "空间要点",
                    notellipsis: true,
                    span: 24,
                    valueFilter(value) {
                      return Array.isArray(value)
                        ? value.map((item) => {
                            return `${item.measure_room_name}：${item.design_point}`;
                          })
                        : [];
                    },
                  },
                ],
                value
              ),
            },
            {
              cols: [
                {
                  label: "量尺图片",
                  formType: "imageList",
                  params: {
                    value: measure_room_ids,
                  },
                  span: 24,
                },
              ],
            },
          ];
        }
      );
      data.key = `measure${this.currentKey}`;

      return data;
    },
    getsummary(val,data){
      this.info_summary= {
              key: `summary${this.currentKey}`,
              title: "概要",
              noKey:true,
              btns:[],
              rows:[{
                cols: [
                  {
                    formType: "summaryDetail",
                    data: val,
                    value:data,
                    span: 24,
                  },
              ],
              }],

            }

      this.infosChange();
    },
    async getClientInfo() {
      let clientInfo = this.params.customerInfo;
      let self = this;
      let map = this.maps[0];
      const data = await this.getSection(
        "/custom-web/api/customSysTrade/getSysTradeByNumber",
        clientInfo.client_number,
        "订单信息",
        (value) => {
          let statusFlag = true;
          let onStatus = false;
         this.sourceData = map.client_status.map(item=>{
           if(item.value == value.client_status){
             statusFlag = false;
             onStatus = true;
           }else{
             onStatus = false;

           }
           return{
             content:item.label,
             timestamp:'2021-11-01',
             isActive:statusFlag,
             code:item.value,
             onStatus:onStatus
           }
         })

          this.clientInfo = value;
          this.getsummary(value,this.sourceData);

          let user = this.getUserInfo();
          this.isCurrentDesigner =
            value.designer_number === user.employeeNumber;
          let isMod = this.isMod(value, this.role);
          const {
            custom_trade_id,
            client_name,
            client_no,
            client_type,
            reserve_measure_date,
            measure_room,
          } = value;
          const data = {
            custom_trade_id,
            client_name,
            client_no,
            client_type,
            reserve_measure_date,
            measure_room,
          };
          return isMod
            ? [
                {
                  txt: "修改",
                  type: "primary",
                  click: () => {
                    this.$root.eventHandle.$emit("creatTab", {
                      name: "编辑订单信息",
                      component: () =>
                        import("@components/dz_customer/addCustomer.vue"),
                      params: {
                        shopInfo: Object.assign(this.clientInfo,{shopCode:this.clientInfo.shop_code}),
	                      shop_code:value.shop_code,
                        shop_name:value.shop_name,
                        client_number: value.client_number,
                        custom_trade_id: value.custom_trade_id,
                      },
                    });
                  },
                },
                {
                  txt: "权益订单核销",
                  type: "primary",
                  click: () => {
                   this.checkDZJ();
                  },
                },
              ]
            : [];
        },
        (value) => {
          return [
            {
              cols: this.setValue(
                [
                  { prop: "client_number", label: "订单编号" },
                  {
                    prop: "client_status",
                    label: "订单状态",
                    valueFilter: "select",
                    options: map.client_status,
                  },
                  { prop: "client_name", label: "客户姓名" },
                  { prop: "client_mobile", label: "客户手机/电话" },
                  {
                    prop: "client_type",
                    valueFilter: "select",
                    label: "客户类型",
                    options: map.client_type,
                    span: 6
                  },
                  {
                    prop: "is_urgent",
                    label: "是否加急",
                    span: 6,
                    valueFilter(value) {
                      return value === 1 ? '加急' : '不加急'
                    },
                  },
                  // {
                  //   prop: "create_time",
                  //   label: "建档时间",
                  //   valueFilter: "date",
                  // },
                  { prop: "shopping_guide_name", label: "建档人" },
                  // {prop: 'none',label: '提交量尺时间'},
                  { prop: "designer_name", label: "设计师" },
                  { prop: "shopping_guide_name", label: "下单专员" },
                  { prop: "shop_name", label: "专卖店" },
                  {
                    prop: "reserve_measure_date",
                    valueFilter: "date",
                    label: "希望量尺时间",
                    span: 6,
                  },
                  // {
                  //   prop: "measure_room",
                  //   valueFilter: "select",
                  //   options: map.measure_room,
                  //   label: "量尺空间",
                  // },
                  {
                    prop: "prin_city_district",
                    label: "省市区",
                    valueFilter(value, col) {
                      return (
                        col.receiver_state_name +
                        col.receiver_city_name +
                        col.receiver_district_name
                      );
                    },
                  },
                  { prop: "address_name", label: "详细地址" },
                  {
                    prop: "supply_transport_type",
                    label: "送货方式",
                    valueFilter: "select",
                    options: map.deliver_method,
                  },
                  // {prop: 'loft_coordinate',label: '楼盘位置', formType: 'location', valueFilter(value, col){
                  //     return {
                  //         address: col.loft_name,
                  //         location: String(col.loft_coordinate).split(',')
                  //     }
                  // }, span: 24},
                  { prop: "remark", label: "客户备注", notellipsis: true },
                  // { prop: "po_number", label: "采购单号", notellipsis: true },
                  // {
                  //   prop: "po_status",
                  //   label: "采购状态",
                  //   valueFilter(value, col) {
                  //     switch (value) {
                  //       case "WAITING":
                  //         return "等待采购";
                  //         break;
                  //       case "PURCHASED":
                  //         return "已采购";
                  //         break;
                  //       case "RECEIVED":
                  //         return "已接收";
                  //         break;
                  //     }
                  //   },
                  //   span: 12
                  // },
                  // 新增合同意向金2021.2.22
                  // {
                  //   prop: "co_agreed_to_gold",
                  //   label: "预计金额",
                  //   span: 6,
                  //   valueFilter(v) {
                  //     return v ? v == 0 ? 0 : Number(v) : '--'
                  //   }
                  // },
                  // {
                  //   prop: "promise_consign_date",
                  //   valueFilter: "date",
                  //   label: "承诺发货日期",
                  //   span: 6
                  //  },
                  //  {
                  //   prop: "in_storage_date",
                  //   valueFilter: "date",
                  //   label: "预计入库时间",
                  //   span: 6
                  //  },
                  { prop: "merge_trade_no", label: "合并单号",redirectClick(d) {
                      if(!d) return;
                      let params = {};
                      params.merge_trade_id = self.clientInfo.merge_trade_id;
                      params.ifOrder = true
                      // 呼叫按钮来源
                      params.callBtnfromType = 'sys_order'
                      self.$root.eventHandle.$emit('creatTab',{
                        name:"合并订单详情",
                        params:params,
                        component: () => import('@components/order/merge.vue')
                      });
                    }, },
                    {
                    prop: "if_arrived_shop",
                    valueFilter(value) {
                      return value ? value == 'Y' ? '是' : '否' : '--'
                    },
                    label: "是否到店",
                    span: 6
                   },
                   {
                    prop: "arrived_shop_date",
                    valueFilter: "date",
                    label: "到店日期",
                    span: 6
                   },
                   {
                    prop: "intention",
                    valueFilter(val) {
                      switch (val) {
                        case "high":
                          return "高";
                          break;
                        case "middle":
                          return "中";
                          break;
                        case "low":
                          return "低";
                          break;
                      }
                    },
                    label: "客户意向",
                    span: 6
                   },
                  {
                    prop: "budget",
                    valueFilter: "select",
                    options: map.custom_sys_budget,
                    label: "客户预算",
                    span: 6
                   },
                   {
                    prop: "order_tag",
                    label: "订单标签",
                    valueFilter(val) {
                      return self.orderTagAux[val]||val
                    },
                    span: 6,
                  },
                ],
                value
              ),
            },
          ];
        }
      );
      data.key = `clientInfo${this.currentKey}`;
      return data;
    },
  },
};
</script>

<style scoped src='../style/info.css'></style>
