.col {
    display: flex;
    margin-bottom: 10px;
}
.col-title {
    line-height: 30px;
}
.col-label {
    display: inline-block;
    width: 120px;
    text-align: right;
    font-size: 13px !important;
}
.col-value {
    flex:1;
    overflow: hidden;
}
.col-com {
    flex:1;
    width:100%;
}
.col-value .xpt-text_ellipsis{
    font-size: 13px !important;
}
.title {
    margin: 45px 0 15px;
    padding-left: 10px;
    border-left: 4px solid #409EFF;
}
.row-title {
    margin: 20px 0 10px 0;
}
.row-title-tag {
    display: inline-block;
    width: 18px;
    text-align: center;
    line-height: 18px;
    border-radius: 4px;
    margin-right: 4px;
}
.title>span, .row-title>span {
    font-weight: 400;
    color: #1f2f3d;
    font-size: 15px !important;
}
.row-title>span.row-title-tag {
    font-size: 12px !important;
    color: #fff !important;
}
.layout {
    border: 1px solid #eaeefb;
    border-radius: 4px;
    transition: .2s;
    padding: 24px;
}
.layout:hover {
    box-shadow: 0 0 8px 0 rgba(232, 237, 250, .6), 0 2px 4px 0 rgba(232, 237, 250, .5);
}
.el-button {
    margin-left: 10px;
}
