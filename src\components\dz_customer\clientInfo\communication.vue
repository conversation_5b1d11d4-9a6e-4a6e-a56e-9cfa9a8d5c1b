<template>
<!-- 详情-沟通交流组件 -->
    <div>
        <!-- <el-select size="mini" style="margin-bottom:10px;" @change="change" v-model="operate_phase">
            <el-option 
                v-for="(option, optionIndex) in operate_phase_options"
                :key="optionIndex"
                :label="option.label" 
                :value="option.value"></el-option>
        </el-select> -->
        <!-- <el-checkbox-group
            size="mini" style="margin-bottom:10px;" @change="change" v-model="operate_phase"
        >
            <el-checkbox 
                v-for="item in operate_phase_options"
                :key="item.value"
                :label="item.value"
            >{{item.label}}</el-checkbox>
        </el-checkbox-group> -->
        <form-create
        v-if="params.log_type !== '1'"
        :formData="queryItems"
        @save="query"
        savetitle="查询"
        ref="formCreate"
      ></form-create>
        <my-table 
        ref="table"
        tableUrl='/custom-web/api/converseOpinion/getList'
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :showHead="false"
        :isPushDown="true"
        :page="false"
        tableHeight="auto"
        ></my-table>
        <!--  -->
        <!-- <div class="form">
            <div :id="locationKey" class="spokesman">发表人——{{spokesman}}</div>
            <el-input  v-model="content_desc" type="textarea" :rows="6" :maxlength="150" placeholder="发表内容"></el-input>
        </div>
        <el-button type='success' @click="submit">发表</el-button> -->
    </div>
</template>
<script>
import myTable from '../components/table/table'
import formCreate from "../components/formCreate/formCreate";
import {converseOpinion} from '../common/api'
import {getMap} from '../common/dictionary/converseDictionary'
import { getMap as getClientMap } from "../common/clientDictionary";
export default {
    components: {myTable,formCreate},
    data() {
        var self = this
        return {
            queryItems: [],
            // searchParam: {
            //     client_start_status: ,
            //     client_end_status: '',
            // },
            operate_phase_options: [],
            operate_phase: [],
            content_desc: '',
            spokesman: '',
            tableParam: {
                client_number: self.params.client_number, 
                operatePhaseList: null
            },
            colData: Object.freeze([
                {
					label: '发表人',
					prop: 'spokesman',
					width: 133
                },
                {
					label: '类型',
					prop: 'client_status',
					width: 133
                },
                {
					label: '发表时间',
					prop: 'publish_time',
					width: 133
                },
                {
					label: '内容',
					prop: 'content_desc'
                }
            ])
        }
    },
    
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        },
        locationKey: {
            type: String
        },
        isSupply: {
            type: Boolean,
            default:false
        }
    },
    created() {
        // console.log(this.isSupply)
        // 商品交流意见
        if(this.params.log_type === '1') {
            this.tableParam.goods_id = this.params.goods_id
            this.tableParam.log_type = '1'
        }

        let userInfo =  sessionStorage.getItem('userInfo') ? JSON.parse(sessionStorage.getItem('userInfo')) : {}
        let user = userInfo.user || {}
        this.spokesman = user.realName || ''
        getMap(map => {
            this.operate_phase_options = map.operate_phase
        })
        this.getQueryItems();
    },
    methods: {
        // 查询所有客户列表
    query(param) {
      Object.assign(this.tableParam, param);
      this.$refs.table.refresh();
    },
        getQueryItems() {
      // 初始化表格数据

      let c_status = [];
      let _self = this;
      getClientMap((map) => {
        let cs  = [];
        if(this.isSupply){
            cs = map.client_status_supply.filter((item) => true);
        }else{
            cs = map.client_status.filter((item) => true);
        }

        // cs = cs.slice(0,cs.length-1)
        cs.forEach((item) => {
          c_status.push(item);
        });
      });
      this.queryItems = [
            {
            cols: [
            
            
                
            
                {
                formType: "selectRange",
                prop: "client_status",
                value: [this.isSupply?'':'CLOSE',this.isSupply?'':'COMPLETED'],
                props: ["start_client_status", "end_client_status"],
                label: "订单状态",
                options: [c_status, c_status],
                },
            ],
            },
        ];
        
        },
        change(v) {
            this.tableParam.operatePhaseList = v.length ? v : null
            this.$refs.table.refresh()
        },
        submit() {
            if(this.content_desc.trim() === '') {
                this.$message.warning('请输入发表内容')
                return
            }
            if(this.isRequest) {
                this.$message.warning('请等待上一次请求结果')
                return
            }
            this.isRequest = true
            let param = {
                operate_phase: '',
                client_number: this.params.client_number,
                content_desc: this.content_desc
            }

            // 发表商品交流意见
            this.params.goods_id && ( param.goods_id = this.params.goods_id )
            converseOpinion(param, false, true).then(body => {
                this.isRequest = false
                if(body.result) {
                    // 保存成功
                    this.content_desc = ''
                    this.$refs.table.refresh()
                }
            })
        }
    }
}
</script>
<style scoped>
.form {
    margin-bottom: 20px;
}
.form .el-textarea {
    width: 400px;
}
.form .spokesman {
    margin: 10px 0;
    color: #4f5f6f;
}
</style>
