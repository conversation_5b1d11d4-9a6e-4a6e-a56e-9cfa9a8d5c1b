<template>
<div class='xpt-flex'>
	<xpt-headbar>
        <el-button type='primary' slot='left' size='mini' @click="save">提交</el-button>
		    <el-button type='danger' slot='left' size='mini' @click="close">取消</el-button>
		
	</xpt-headbar>
  <el-form label-width='100px' :model='query'  ref="query">
    <el-row :gutter='40' style='height:50px'>
       <el-col :span='40'  class="xpt_col">
        <p>订单核销后，平台自动发货，不支持退款，请谨慎操作。</p>
        
      </el-col>
      <el-col :span='40'  class="xpt_col">
        <el-form-item label='订单号' prop='number'>
          <el-input v-model="query.number" size="mini"  class="repo-input" ></el-input>
        </el-form-item>
        
      </el-col>
      <!-- <el-col :span='40'  class="xpt_col">
        <el-button type='primary' slot='left' size='mini' @click="save">提交</el-button>
		    <el-button type='primary' slot='left' size='mini' @click="close">取消</el-button>
        
      </el-col> -->
    </el-row>
  </el-form>
</div>
</template>
<script>
  export default {
    data() {
      let self = this;
      return {
        oldGoodsList: [],
        query:{
          number:'',
        },
        // hasData:self.params.sortList.length,
        // canEditData:self.params.canChangeList.length
      }
    },
	  props:['params'],
    methods: {
      close(){
        let self = this;
        self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
      },
      save(){
       this.ajax.postStream("/custom-web/api/equityGoods/submitTid", {tid:this.query.number,shop_code:this.params.shop_code}, res => {
          if(res.body.result) {
            this.$root.eventHandle.$emit('alert', {
            params: {
                list:res.body.content.list,
                tid:this.query.number,
                client_number:this.params.client_number,
                on_line_amount:res.body.content.on_line_amount,
                callback: data => {
                  console.log(data)
                  this.close();

                   
                    
                },
            },
            component: () => import('./equityGoodsList'),
            style: 'width:500px;height:300px',
            title: '线上订单核销',
        })
          } else {
            this.$message.error(res.body.msg)
          }
        
        }, err => {
          
        });
        // this.params.callback(this.query.number);
        this.close();
      },
      changeSelect(item,index){
        if(item.checked != true){
          this.oldGoodsList[index].qty = '';
          // console.log(item,index,this.oldGoodsList[index])          

        }
      }
     
    },
    mounted(){
      // this.getOldGoodsList();
    }
  }
</script>
<style>
	.repo-input{
		width:100%;
    float: right;
    /* margin-right: 50px; */
	}
  .xpt_col{
    margin-bottom: 10px;
  }
</style>
