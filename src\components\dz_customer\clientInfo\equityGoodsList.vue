<template>
<div class='xpt-flex'>
	<xpt-headbar>
        <el-button type='primary' slot='left' size='mini' @click="save">核销</el-button>
		    <el-button type='danger' slot='left' size='mini' @click="close">取消</el-button>
		
	</xpt-headbar>
  <el-form label-width='100px' :model='query'  ref="query">
    <el-row :gutter='40' style='height:50px'>
       <p style="margin-left: 20px;">
         核销金额：{{params.on_line_amount}}
       </p>
      <el-col :span='40'  class="xpt_col">
        <el-card :class="item.enable?'enable-card':'disable-card'" v-for="(item,key) in equityGoodsList" :key="key">
        <div>
          <el-radio v-model="item.isCheck" :disabled="!item.enable" label="1" @input="itemChange(item)">
            <!-- <p class="card-text benefit-amount">订单金额：￥{{item.benefit_amount}}</p> -->
            <p class="card-text equity-name">权益名称：{{item.equity_name}}</p>
            <p class="card-text">使用门槛：{{item.benefit_amount_threshold}}</p>
            <p class="card-text  xpt-text_ellipsis">使用说明：
              <el-tooltip effect="dark" :content="item.remark" placement="top-start">
                <span>{{item.remark}}</span>
              </el-tooltip>
            </p>
          </el-radio>
        </div>
        
      </el-card>

        
      </el-col>
     
    </el-row>
  </el-form>
</div>
</template>
<script>
  export default {
    data() {
      let self = this;
      return {
        equityGoodsList: [],
        query:{
          number:'',
        },
        selectData:{},
      }
    },
	  props:['params'],
    methods: {
      itemChange(item){
        // console.log(item);
        this.selectData = item;
      },
      close(){
        let self = this;
        self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
      },
      save(){
       this.ajax.postStream("/custom-web/api/equityGoods/verify", {
                "client_number":this.params.client_number,                                    //定制订单号
                "tid":this.params.tid,                                                          //tid
                "custom_equity_id":this.selectData.custom_equity_id,                                         //订单金额
                "benefit_amount":this.selectData.benefit_amount,                                         //订单金额
                "equity_name":this.selectData.equity_name,                                           //权益名称
                "benefit_amount_threshold":this.selectData.benefit_amount_threshold,                        //让利额度门槛
                "remark":this.selectData.remark                                            //使用说明
          }, res => {
          if(res.body.result) {
            this.params.callback()
            this.close();
            this.$message.success('操作成功！')
            
          } else {
            this.$message.error(res.body.msg)
          }
        
        }, err => {
          
        });
      },
      
     
    },
    mounted(){
      this.equityGoodsList = this.params.list;
    }
  }
</script>
<style>
	.enable-card{
    background: #eda848;
    color: white;
  }
  .disable-card{
    color: white;
    background: #d1dbe5;
  }
  .card-text{
    color: white;
    margin: 5px;
    width: 380px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .benefit-amount{
    color:tomato;
    font-weight: 800;
  }
  .equity-name{
    font-weight: 800;
  }
</style>
