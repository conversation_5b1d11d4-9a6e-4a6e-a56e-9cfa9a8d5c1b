<template>
<!-- 文件列表组件 -->
    <div>
        <div v-if="type">
            <div
                v-for="(files, index) in valueFilter"
                :key="index"
            >   
                <span>{{files.title}}：</span>
                <div
                    v-for="(item, fileIndex) in files.files"
                    :key="item.cloud_file_id"
                    style="margin:0 4px 4px 0;display:inline-block;"
                >   
                    <el-popover
                        placement="top"
                        title="注意：删除后不可恢复，请谨慎操作！"
                        trigger="hover"
                        v-if="delPopover || item.delPopover" 
                        >
                        <div>
                             <el-button type='primary' style="margin-right:10px;" size="mini" @click="del(files.files, fileIndex, item)">删除</el-button>{{item.name}}?
                         </div>
                         <span
                            slot="reference"
                            @click="orderClick(item)"
                            style="margin-left: 10px;color: rgb(0,0,238);cursor:pointer;display:inline-block;max-width:150px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                        >{{item.name}}</span>
                    </el-popover>
                    <div
                        v-else
                        style="display:inline-block; margin-left: 10px;"
                    >
                    <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                        <span
                             @click="orderClick(item)"
                             style="color: rgb(0,0,238);cursor:pointer;display:inline-block;max-width:150px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                        >{{item.name}} </span>
                    </el-tooltip>
                        ({{fn(item.create_time)}})
                    </div>
                   
                </div>
                
            </div>
            <div v-if="!valueFilter.length">
                    {{defaultValue}}
            </div>
        </div>
        <div v-else>
            <div
                v-for="(item, fileIndex) in valueFilter"
                :key="item.cloud_file_id"
                style="margin:0 4px 4px 0;display:inline-block;"
            >
                <div v-if="delPopover || item.delPopover" class="flex-container">
                    <el-popover
                        placement="top"
                        title="注意：删除后不可恢复，请谨慎操作！"
                        trigger="hover"
                        >
                        <div>
                             <el-button type='primary' style="margin-right:10px;" size="mini" @click="del(valueFilter, fileIndex, item)">删除</el-button>{{item.name}}?
                         </div>
                         <span
                            slot="reference"
                            @click="orderClick(item)"
                            style="margin-left: 10px;color: rgb(0,0,238);cursor:pointer;display:inline-block;max-width:150px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                        >{{item.name}}</span>
                    </el-popover>
                    <el-button v-if="['jpg','png','jpeg'].includes(item.file_type) && params.type === 'cd'" type="primary" size="mini" @click="previewPicture(item)" style="margin-left:5px;">预览图片</el-button>
                </div>
                    <div
                        v-else
                        style="display:inline-block; margin-left: 10px;"
                    >
                    <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                        <span
                             @click="orderClick(item)"
                             style="color: rgb(0,0,238);cursor:pointer;display:inline-block;max-width:150px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                        >{{item.name}} </span>
                </el-tooltip>
                        ({{fn(item.create_time)}})
                    </div>
            </div>
            <div v-if="!valueFilter.length">
                    {{defaultValue}}
            </div>
        </div>
         <!-- <div>
             <upload-file v-if="upload" :config="config"></upload-file>
         </div> -->
    </div>
</template>
<script>
import {downloadZip, deleteFile, getFileList} from '../common/api'
import fn from '@/common/Fn.js'
export default {
    data() {
        return {
            selfFiles: []
        }
    },
    props: {
        delPopover: {
            type: Boolean,
            default: false
        },
        autoGetFile: {
            type: Boolean,
            default: true
        },
        defaultValue: {
            type: String,
            default: '--'
        },
        value: {
            type:Array
        },
        type: {
            type:Boolean
        },
        params: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    computed: {
        valueFilter() {
            return (this.config.parent_no && !this.type && this.autoGetFile) ? this.selfFiles : this.value
        },
        config() {
            return {
                parent_no: this.params.order_no || '',
                child_no: this.params.child_no || '',
                emit: this.params.emit || ''
            }
        },
        upload() {
            return this.params.upload
        }
    },
    async created() {
        if(this.config.parent_no && !this.type && this.autoGetFile) {
            // 获取附件
            this.selfFiles = await getFileList({
                order_no: this.config.parent_no,
                sub_order_no: this.config.child_no
            })
            this.$emit('fileList', this.selfFiles)
        }
    },
    methods: {
        fn(stamp) {
            return fn.dateFormat(stamp, 'yy-MM-dd')
        },
        del(arr, index, item) {
                deleteFile({list_cloud_file_id: [item.cloud_file_id]}).then(res => {
                    arr.splice(index, 1)
                    this.$emit('del')
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                })
        },
        orderClick(item) {
            if(this.params.preview) {
                window.open(item.path)
            } else {
                downloadZip([item])
            }
        },
        previewPicture(item){
            this.$root.eventHandle.$emit("alert", {
                title: '图片预览',
                style: "width:800px;height:550px;",
                component: () =>
                    import("@components/dz_customer/clientInfo/previewPicture.vue"),
                params: {
                    url:item.path
                },
            });
        }
    }
}
</script>
<style scoped>
.flex-container{
    display: flex;
    align-items: center;
}
</style>