<template>
  <!-- 商品详情-商品相关文件组件 -->
  <div>
    <file-list :value="files" :params="params" type :autoGetFile="false"></file-list>
    <el-button
      v-if="list.length"
      :loading="downLoading"
      :plain="true"
      type="info"
      @click="saveZip"
    >打包文件</el-button>
  </div>
</template>
<script>
import fileList from "./fileList";
import { getRole, getFileList, downloadZip } from "../common/api";
export default {
  components: {
    fileList,
  },
  data() {
    return {
      files: [],
      list: [],
      role:[],
      hideRoles:['DZ_DG','DZ_SJS','DZ_DZ','DZ_DZH',"DZ_SJZG","DZ_ZBWY","AREA_MAMAGE"],
      openRoles:["DZ_SHY","DZ_CSY","DZ_CDY","ZBDZFZR"],
      downLoading: false,
    };
  },
  props: {
    value: {
      type: Array,
    },
    params: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  async created() {
    // 获取角色
    this.role= await getRole()
    this.role=JSON.parse(JSON.stringify(this.role))
    this.getFiles();
  },
  methods: {
    async saveZip() {
      if(!this.isShowFile()){
          this.list=this.list.filter(item=>item.sub_order_no.indexOf("cs") == -1&&item.sub_order_no.indexOf("cd") == -1)
      }
      console.warn("下载的附件列表",this.list)
      if (!this.list.length) {
        this.$message({
          message: "没有可下载的附件",
          type: "warning",
        });
        return;
      }
      this.downLoading = true;
      await downloadZip(this.list);
      this.downLoading = false;
    },
    //当前角色是否有权限查看拆单、拆审的文件
    isShowFile(){
        let self=this
        return self.role.some(val=>self.openRoles.includes(val))
    },
    getFiles() {
        let self=this
      this.files = [];
      // 审核阶段
      let verType = this.params.verType
      getFileList({
        order_no: this.params.order_no,
      }).then((list) => {
        this.list = list || [];
        list.forEach((item) => {
            console.warn("是否有权限展示拆单，拆审",self.isShowFile())
          if (!item.sub_order_no) {
            // 设计
            !this.files[0] && (this.files[0] = { title: "设计", files: [] });
            this.files[0].files.push(item);
          } else if (item.sub_order_no.indexOf("cd") !== -1&&self.isShowFile()) {
            // 拆单
            !this.files[2] && (this.files[2] = { title: "拆单", files: [] });
            (verType === 'cd' || verType === 'sj') && (item.delPopover = true)
            this.files[2].files.push(item);
          } else if (item.sub_order_no.indexOf("cs") !== -1&&self.isShowFile()) {
            // 拆审
            !this.files[3] && (this.files[3] = { title: "拆审", files: [] });
            verType === 'cs' && (item.delPopover = true)
            this.files[3].files.push(item);
          } else if (item.sub_order_no.indexOf("fl") !== -1) {
            // 分类
            !this.files[4] && (this.files[4] = { title: "分类", files: [] });
            verType === 'fl' && (item.delPopover = true)
            this.files[4].files.push(item);
          } else if((/^(\d)/).test(item.sub_order_no)){
            // 审图审价
            !this.files[1] && (this.files[1] = { title: "审价", files: [] });
            verType === 'sj' && (item.delPopover = true)
            this.files[1].files.push(item);
          }
        });
        this.files = this.files.filter((item) => true);
        console.warn("相关的文件列表",this.files)
      });
    },
  },
};
</script>
