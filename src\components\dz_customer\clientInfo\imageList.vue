<template>
<!-- 详情-图片列表组件 -->
    <div>
        <div
            v-for="(item, index) in params.value"
            :key="index"
            class="image-item"
            @click="handleView(item)"
        >
        <div :class="{none: item.images.length === 0 }">
            <img v-if="item.images.length" :src="isImage(item.images[0]) ? item.images[0] : dfImage"/>
        </div>
        <div class="detail">
            <span>{{item.name}}</span>
            <span>{{item.images.length}}张</span>
        </div>
        </div>
       
    </div>
</template>
<script>
import {imageView} from '../alert/alert'
export default {
    data() {
        return {
            dfImage: 'http://lsmy-devimage.oss-cn-shenzhen.aliyuncs.com/image/2020-05-298d67421c99974a44a535ae9431b1af34.jpg'
        }
    },
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    methods: {
        isImage(str) {
            if(!str) return false
            let strArr = str.split('.')
            let ext = strArr.pop()
            var strFilter="jpeg|gif|jpg|png|bmp|pic|"
            return strFilter.indexOf(ext) !== -1
        },
        handleView(item) {
            imageView(this, {
                no: {
                    parent_no: item.parent_no,
                    child_no: item.child_no
                },
                client_number: item.client_number,
                useBy: item.useBy,
                detail: item.detail
            })
        }
    }
}
</script>
<style scoped>
.image-item {
    display: inline-block;
    margin: 0 6px;
    cursor: pointer;
}
.image-item img {
    width: auto;
    min-width: 90px;
    height: 60px;
}
.detail {
    display: flex;
    justify-content: space-between;
}
.none {
    height: 60px;
    width: 90px;
    background: #ddd;
    position: relative;

}
.none::after {
    content: '暂无附件';
    position: absolute;
    top: 50%;
    left:0;
    transform: translateY(-50%);
    width: 100%;
    text-align: center;
}
</style>