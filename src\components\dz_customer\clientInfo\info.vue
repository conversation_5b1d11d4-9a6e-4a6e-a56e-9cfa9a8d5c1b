<!--
 * @Author: your name
 * @Date: 2021-04-19 11:40:33
 * @LastEditTime: 2021-04-19 11:45:03
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \front-deve:\code\front-v_20210421\src\components\dz_customer\clientInfo\info.vue
-->
<template funtional>
  <!-- 详情-通用模块组件 -->
  <div>
    <!-- title -->
    <div class="title">
      <span :id="info.noKey ? '' : info.key">{{ info.title }}</span>
      <template v-if="info.btns">
        <el-button
          v-for="(item, index) in info.btns"
          size="mini"
          :key="index"
          :type="item.type"
          @click="item.click && item.click(info)"
          >{{ item.txt }}</el-button
        >
      </template>
    </div>
    <div class="layout">
      <layout :rows="info.rows">
        <template slot="row" slot-scope="scope">
          <div class="row-title" v-if="scope.row.title">
            <span
              v-if="scope.row.tag"
              class="row-title-tag"
              :style="{ background: scope.row.tagBack || '#4f5f6f' }"
              >{{ scope.row.tag }}</span
            >
            <span :id="scope.row.key">{{ scope.row.title }}</span>
            <template v-if="scope.row.btns">
              <el-button
                v-for="(item, index) in scope.row.btns"
                size="mini"
                :key="index"
                :type="item.type"
                @click="item.click && item.click(scope.row)"
                >{{ item.txt }}</el-button
              >
            </template>
          </div>
        </template>
        <template slot-scope="scope">
          <div class="col">
            <span
              v-if="scope.col.label"
              class="col-label"
              :style="{ width: scope.col.labelWidth }"
              >{{ scope.col.label }}：</span
            >
            <div class="col-value" v-if="!scope.col.formType"
             @click="scope.col.redirectClick(scope.col.value)"
             :style="{color:scope.col.color&&scope.col.value?scope.col.color:'#000'}">
              <div
                v-if="!scope.col.formType && !scope.col.notellipsis && scope.col.prop=='client_mobile'"
                v-tooltip="scope.col.value"
              >
                <xpt-eye-switch :value="scope.col.value || (scope.col.value === 0
                  ? scope.col.value + (scope.col.suffix || '')
                    : '--')"
                    class="dzEyeSwitch"
                  :readonly="true"
                  :hideBorder="true"
                  :aboutNumber="aboutNumber"
                  width="110">
                </xpt-eye-switch>
              </div>
              <div
                v-else-if="!scope.col.formType && !scope.col.notellipsis && scope.col.prop=='merge_trade_no'"
                v-tooltip="scope.col.value"
              >
                <span
              style=" color: #0000EE !important;"
              >{{ scope.col.value }}</span
            >
              </div>
              <div
                v-else-if="!scope.col.formType && !scope.col.notellipsis"
                v-tooltip="scope.col.value"
              >
                {{
                  scope.col.value || scope.col.value === 0
                    ? scope.col.value + (scope.col.suffix || "")
                    : "--"
                }}
              </div>
              <div
                v-else-if="!scope.col.formType"
                style="word-break: break-word"
              >
                {{
                  scope.col.value || scope.col.value === 0
                    ? scope.col.value + (scope.col.suffix || "")
                    : "--"
                }}
              </div>
            </div>
            <div class="col-com" v-else>
              <div v-if="scope.col.title"><span class="col-title">{{scope.col.title}}</span></div>
              <component
                :is="scope.col.formType?.default || scope.col.formType"
                :value="scope.col.value"
                :params="scope.col.params"
                :disabled="scope.col.disabled"
                :tableData="scope.col.data"
                :colData="scope.col.colData"
                :summaryMethod="scope.col.summaryMethod"
                :tableHeight="scope.col.tableHeight"
                :locationKey="scope.col.locationKey"
                :isSupply="scope.col.isSupply"
                selection="none"
                :showHead="false"
              />
            </div>
          </div>
        </template>
      </layout>

    </div>
  </div>
</template>
<script>
import layout from "../components/layout/layout";
export default {
  components: {
    layout,
    location: () => import("./location"),
    imageList: () => import("./imageList"),
    timeline: () => import("./timeline"),
    myRate: () => import("./rate"),
    myTable: () => import("../components/table/table"),
    textlist: () => import("./textlist"),
    orderList: () => import("./order"),
    fileList: () => import("./fileList"),
    goodsFile: () => import("./goodsFile"),
    communication: () => import("./communication"),
    tollInquiry: () => import("./tollInquiry"),
    retailPrice: () => import("./retailPrice"),
    summaryDetail: () => import("./summaryDetail"),
    summaryDetail2: () => import("./summaryDetail2"),
    tips: () => import("./tips"),
  },
  props: {
    info: {
      type: Object,
    },
    aboutNumber:{
      type:String,
      default:""
    }
  },
};
</script>
<style lang="stylus" scoped>
@import "./col.styl";
</style>
<style>
.dzEyeSwitch .eyeSwitchInput .el-input__inner{
  font-size:13px !important;
  margin-top: -4px;
}
</style>
