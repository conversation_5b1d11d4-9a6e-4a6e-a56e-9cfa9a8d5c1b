<template>
<!-- 详情-坐标组件 -->
    <div>
        {{value.address}}({{value.location[0]}}, {{value.location[1]}})<a style="margin-left:10px" :href="mapAddress" target="_Blank">查看地图</a>
    </div>
</template>
<script>
export default {
    props: {
        params: {
            type: Object
        },
        value: {
            type: Object
        }
    },
    computed: {
        mapAddress() {
            return `http://api.map.baidu.com/marker?location=${this.value.location[1]},${this.value.location[0]}&title=查看地图&content=${this.value.address}&output=html&src=webapp.baidu.openAPIdemo`
        }
    }
}
</script>