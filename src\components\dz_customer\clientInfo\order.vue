<template>
<!-- 详情-可点击编号组件 -->
    <div>
        <span
            v-for="(item, index) in value"
            :key="index"
            @click="orderClick(item)"
            style="color: rgb(0,0,238);cursor:pointer;margin-right: 10px;display:inline-block;"
        >{{item}}</span>
        <span v-if="!value || !value.length">
            --
        </span>
    </div>
</template>
<script>
export default {
    props: {
        value: {
            type:Array
        },
        params: {
            type: Object
        }
    },
    methods: {
        orderClick(item) {
            let c = this.params.orderClick || function() {}
            c(item)
        }
    }
}
</script>
