<template>
  <div class="image-container">
    <img :src="params.url" alt="图片" class="image-main" />
  </div>
</template>

<script>
export default {
  props: ["params"],
};
</script>

<style scoped>
.image-container {
  width: 98%;
  height: 94%;
  margin: 30px auto 0;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: center;
  color: #ccc;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}
.image-main {
  max-width: 98%;
  max-height: 98%;
}
</style>
