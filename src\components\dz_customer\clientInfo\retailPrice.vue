<template>
  <!-- 商品详情-商品相关文件组件 -->
  <div style="display: inline;">
    <span>{{price +`  `}}</span>
    
    <el-button
      v-if="params.value.retail_price"
      size="mini"
      type="info"
      @click="showPrice"
    >报价明细</el-button>
    <!-- <a @click="showPrice">报价明细</a> -->
  </div>
</template>
<script>
export default {
  
  data() {
    return {
     price:'---',
      role:[],
      downLoading: false,
    };
  },
  props: {
    value: {
      type: Array,
    },
    params: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  async created() {
    // console.log(this.params);
    this.price = this.params.value.retail_price? this.params.value.retail_price: '---'
  },
  methods: {
   showPrice(){
       let self = this;
        this.$root.eventHandle.$emit('alert', {
                    params: {
                      
                      initObj:{
                        quotation_type:'RETAIL_PRICE',
                        swj_goods_id:self.params.value.swj_goods_id,
                      }
                    },
                    component: () => import('@components/dz_customer/alert/priceList'),
                    style: 'width:1100px;height:610px',
                    title: '价格明细',
                })
         
   },
    
    
  },
};
</script>
