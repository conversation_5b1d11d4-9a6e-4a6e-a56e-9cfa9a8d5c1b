<template>
<!-- 概要 -->
       
    <div>
       <div class="timeLine">
           <table style="width:100%;">
               <tr>
                   <td 
                        v-for="(item, index) in sourceData"
                        :key="'item_'+index"
                   >
                       <time-line-item
                        :content='item.content'
                        :timestamp='item.timestamp'
                        :hideTimestamp='!item.hideTimestamp'
                        :isDot="item.isDot"
                        :isLast='index != sourceData.length-1'
                        :tampText='index+1+""'
                        :onStatus='item.onStatus'
                        :isActive='item.isActive'
                        ></time-line-item>
                   </td>
               </tr>
           </table>
        
        
      </div>
        <div>
            <xpt-form :data='tableData' :cols='baseCols' label='110px'>
        </xpt-form>
        </div>
    </div>
</template>
<script>

import timeLineItem from "@/components/common/timeLineItem.vue";
import Fn from '@common/Fn.js'
import { getMap } from "../common/clientDictionary";
export default {
    components: {timeLineItem},
    data() {
        var self = this
        return {
           sourceData:[],
            initTimeList:['','','','','',''],
           baseCols: [
				[
					{
						label: '建档时间:',
						formatter(val){
                            return Fn.dateFormat(val)||'--'
                        },
						key: 'create_time'
					}, {
						label: '开始生产时间:',
						key: 'scm_time',
						formatter(val){
                            return Fn.dateFormat(val)||'--'
                        },
					}, {
						label: '发货时间:',
						formatter(val){
                            return Fn.dateFormat(val)||'--'
                        },
						key: 'outbound_time'
					}, 
				], [
					{
						label: '门店下单时间:',
						formatter(val){
                            return Fn.dateFormat(val)||'--'
                        },
						key: 'order_submit_time'
					}, {
						label: '生产完成时间:',
						key: 'complete_product_time',
						formatter(val){
                            return Fn.dateFormat(val)||'--'
                        },
					}, 
				], [
					{
						label: '资金冻结时间:',
						formatter(val){
                            return Fn.dateFormat(val)||'--'
                        },
						key: 'deduction_time'
					}, {
						label: '入库时间:',
						formatter(val){
                            return Fn.dateFormat(val)||'--'
                        },
						key: 'in_warehouse_time'
					}
				]
			]
        }
    },
    
    props: {
        tableData: {
            type: Object,
            default() {
                return {}
            }
        },
        value: {
            type: Object,
            default() {
                return {}
            }
        },
    },
    created() {
        this.sourceData = [this.value[0]];
        new Promise((resolve,reject)=>{
            this.setInitItme(resolve);

        }).then(res=>{
            let arr = JSON.parse(JSON.stringify(this.value));
            let onStatusIndex = ''; 
            let waitPushGoodsInd = '';
            let timeArr = ['WAITING_PUSH_GOODS','WAITING_OUTBOUND','IN_WAREHOUSE','SUCCESS_GOODS','SUCCESS_FINANCE'],initTimeArr=['','','','',''];
            this.value.forEach((item,index)=>{
                if(item.onStatus){
                    onStatusIndex = index;
                    if(timeArr.indexOf(item.code)!=-1){
                        if(timeArr.indexOf(item.code) == 0){

                            initTimeArr[0] = this.tableData.in_storage_date+ this.initTimeList[0]*1000*60*60*24;
                            initTimeArr[1] = initTimeArr[0]+ this.initTimeList[1]*1000*60*60*24;
                            initTimeArr[2] = initTimeArr[1]+ this.initTimeList[2]*1000*60*60*24;
                            initTimeArr[3] = initTimeArr[2]+ this.initTimeList[3]*1000*60*60*24;
                            initTimeArr[4] = initTimeArr[3]+ this.initTimeList[4]*1000*60*60*24;
                            arr[18].hideTimestamp = true;
                            arr[18].timestamp = '预计生产时间:'+ Fn.dateFormat(initTimeArr[0], 'MM-dd');
                            arr[19].hideTimestamp = true;
                            arr[19].timestamp = '预计入库时间:'+ Fn.dateFormat(initTimeArr[1], 'MM-dd');
                            arr[20].hideTimestamp = true;
                            arr[20].timestamp = '预计货审时间:'+Fn.dateFormat(initTimeArr[2], 'MM-dd');
                            arr[21].hideTimestamp = true;
                            arr[21].timestamp = '预计财审时间:'+Fn.dateFormat(initTimeArr[3], 'MM-dd ');
                            arr[22].hideTimestamp = true;
                            arr[22].timestamp = '预计到货时间:'+ Fn.dateFormat(initTimeArr[4], 'MM-dd');
                        }else if(timeArr.indexOf(item.code) == 1){
                            initTimeArr[1] = this.tableData.complete_product_time+ this.initTimeList[1]*1000*60*60*24;
                            initTimeArr[2] = initTimeArr[1]+ this.initTimeList[2]*1000*60*60*24;
                            initTimeArr[3] = initTimeArr[2]+ this.initTimeList[3]*1000*60*60*24;
                            initTimeArr[4] = initTimeArr[3]+ this.initTimeList[4]*1000*60*60*24;
                            initTimeArr[5] = initTimeArr[4]+ this.initTimeList[5]*1000*60*60*24;
                            arr[19].hideTimestamp = true;
                            arr[19].timestamp ='预计入库时间:'+ Fn.dateFormat(initTimeArr[1], 'MM-dd');
                            arr[20].hideTimestamp = true;
                            arr[20].timestamp = '预计货审时间:'+Fn.dateFormat(initTimeArr[2], 'MM-dd');
                            arr[21].hideTimestamp = true;
                            arr[21].timestamp = '预计财审时间:'+Fn.dateFormat(initTimeArr[3], 'MM-dd');
                            arr[22].hideTimestamp = true;
                            arr[22].timestamp = '预计到货时间:'+Fn.dateFormat(initTimeArr[4], 'MM-dd');
                        }else if(timeArr.indexOf(item.code) == 2){
                            initTimeArr[2] = this.tableData.in_warehouse_time+ this.initTimeList[2]*1000*60*60*24;
                            initTimeArr[3] = initTimeArr[2]+ this.initTimeList[3]*1000*60*60*24;
                            initTimeArr[4] = initTimeArr[3]+ this.initTimeList[4]*1000*60*60*24;
                            arr[20].hideTimestamp = true;
                            arr[20].timestamp = '预计货审时间:'+Fn.dateFormat(initTimeArr[2], 'MM-dd');
                            arr[21].hideTimestamp = true;
                            arr[21].timestamp = '预计财审时间:'+Fn.dateFormat(initTimeArr[3], 'MM-dd');
                            arr[22].hideTimestamp = true;
                            arr[22].timestamp = '预计到货时间:'+Fn.dateFormat(initTimeArr[4], 'MM-dd');
                        }else if(timeArr.indexOf(item.code) == 3){
                            initTimeArr[3] = this.tableData.goods_audit_time+ this.initTimeList[3]*1000*60*60*24;
                            initTimeArr[4] = initTimeArr[3]+ this.initTimeList[4]*1000*60*60*24;
                            
                            
                            arr[21].hideTimestamp = true;
                            arr[21].timestamp = '预计财审时间:'+Fn.dateFormat(initTimeArr[3], 'MM-dd');
                            arr[22].hideTimestamp = true;
                            arr[22].timestamp = '预计到货时间:'+Fn.dateFormat(initTimeArr[4], 'MM-dd');
                        }else if(timeArr.indexOf(item.code) == 4){
                            initTimeArr[4] = this.tableData.financial_audit_time+ this.initTimeList[4]*1000*60*60*24;
                            
                            arr[22].hideTimestamp = true;
                            arr[22].timestamp = '预计到货时间:'+Fn.dateFormat(initTimeArr[4], 'MM-dd');
                        }
                    }
                }
                if(item.code == 'WAITING_PUSH_GOODS'){
                    waitPushGoodsInd = index;
                }
            });
            
            
        arr.forEach((item,index)=>{
            
            if((index!=0 && index<onStatusIndex-2)||(index!=arr.length-1 && index>onStatusIndex+6)){
                item.isDot = true;
            }else{
                item.isDot = false;
            }
            if(index==0||index==arr.length-1){
                item.isDot = true;
            }
            if(index<onStatusIndex){
                item.hideTimestamp = false;
            }
        })
        if(onStatusIndex>2){
                this.sourceData.push({isDot:true,isActive:true})
            }
            arr.forEach(item=>{
                if(item.isDot == false){
                    this.sourceData.push(item);
                }
            })
            
            if(arr[arr.length-1].code != this.sourceData[this.sourceData.length-1].code){
                if(arr[arr.length-2].code != this.sourceData[this.sourceData.length-1].code){
                    this.sourceData.push({isDot:true,isActive:false})
                }
                this.sourceData.push(arr[arr.length-1]);
                this.sourceData[this.sourceData.length-1].isDot = false;
            }
        
            let client_status = [];
        
            
        })
        

    },
    mounted(){
    },
    methods: {
        setInitItme(resolve){
            let self = this;
            this.ajax.postStream('/custom-web/api/cycleConfig/getList', {}, (d) => {
                if(d.body.result){
                    d.body.content.forEach(item=>{
                        if(item.start_client_status == 'WAITING_PUSH_GOODS'){
                            self.initTimeList[0] = item.expected_cycle;
                        }
                        if(item.start_client_status == 'WAITING_OUTBOUND'){
                            self.initTimeList[1] = item.expected_cycle;
                        }
                        if(item.start_client_status == 'IN_WAREHOUSE'){
                            self.initTimeList[2] = item.expected_cycle;
                        }
                        if(item.start_client_status == 'SUCCESS_GOODS'){
                            self.initTimeList[3] = item.expected_cycle;
                        }
                        if(item.start_client_status == 'SUCCESS_FINANCE'){
                            self.initTimeList[4] = item.expected_cycle;
                        }
                        // if(item.start_client_status == 'WAITING_PURCHASE'){
                        //     self.initTimeList[0] = item.expected_cycle;
                        // }
                    })
                    resolve && resolve();
                }

				}, err => {
					this.$message.error(err);
				})

        }
    }
}
</script>
<style scoped>
.timeLine {
    display: flex;
    flex-direction: row;
    margin-left: 50px;
    margin-bottom: 30px;
}
.timeLine:last-child .line{
  width: 0 !important;
}
</style>