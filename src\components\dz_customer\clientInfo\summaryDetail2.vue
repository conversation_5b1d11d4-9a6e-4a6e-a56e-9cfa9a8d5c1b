<template>
<!-- 概要 -->
       
    <div>
       <div class="timeLine">
         <table style="width:100%;">
               <tr>
                   <td 
                        v-for="(item, index) in sourceData"
                        :key="'item_'+index"
                   >
                       <time-line-item
                        :content='item.content'
                        :timestamp='item.timestamp'
                        :hideTimestamp='!item.hideTimestamp'
                        :isDot="item.isDot"
                        :isLast='index != sourceData.length-1'
                        :tampText='index+1+""'
                        :onStatus='item.onStatus'
                        :isActive='item.isActive'
                        ></time-line-item>
                   </td>
               </tr>
           </table>
        
      </div>
        <div>
            <xpt-form :data='tableData' :cols='baseCols' label='110px'>
        </xpt-form>
        </div>
    </div>
</template>
<script>

import timeLineItem from "@/components/common/timeLineItem.vue";
import { getMap } from "../common/clientDictionary";
export default {
    components: {timeLineItem},
    data() {
        var self = this
        return {
           sourceData:[],

           baseCols: [
				[
					{
						label: '门店下单时间:',
						format: 'dataFormat1',
						key: 'supply_submit_time'
					}, {
						label: '发货时间:',
						format: 'dataFormat1',
						key: 'delivery_time'
					}, 
				], [
					 {
						label: '开始生产时间:',
						key: 'submit_purchase_time',
						format: 'dataFormat1'
					},
				], [
					 {
						label: '生产周期:',
						key: 'submit_purchase_time',
                        formatter(val){
                            return '柜体7天，吸塑铝门15天'
                        }
					}
				]
			]
        }
    },
    
    props: {
        tableData: {
            type: Object,
            default() {
                return {}
            }
        },
        value: {
            type: Object,
            default() {
                return {}
            }
        },
    },
    created() {
        this.sourceData = this.value;

        let onStatusIndex = ''; 
        this.value.forEach((item,index)=>{
            if(item.onStatus){
                onStatusIndex = index;
            }
        });

        this.ajax.postStream('/custom-web/api/cycleConfig/getList', {}, (d) => {
                if(d.body.result){
                    d.body.content.forEach(item=>{
                        if(item.start_client_status == 'WAITING_PUSH_GOODS'){
                            this.initTimeList[1] = item.expected_cycle;
                        }
                        
                    })
                    resolve && resolve();
                }

				}, err => {
					this.$message.error(err);
				})
        
        console.log(this.value)

    },
    mounted(){
    },
    methods: {

    }
}
</script>
<style scoped>
.timeLine {
    display: flex;
    flex-direction: row;
    margin-left: 50px;
    margin-bottom: 30px;
}
.timeLine:last-child .line{
  width: 0 !important;
}
</style>