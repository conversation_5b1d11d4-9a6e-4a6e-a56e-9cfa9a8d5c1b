<template>
<!-- 详情-时间顺序组件 -->
    <div style="max-height: 600px; overflow: auto;">
        <timeline
        :line="value"
        >
        <template slot-scope="scopeLine">
            <layout
                :rows ="scopeLine.col.rows"
            >
                <template slot-scope="scope">
                    <div class="col">
                        <span v-if="scope.col.label" class="col-label"  :style="{width: scope.col.labelWidth}">{{scope.col.label}}：</span>
                        <div class="col-value">
                            <div v-if="!scope.col.formType && !scope.col.notellipsis" v-tooltip='scope.col.value'>{{(scope.col.value || scope.col.value === 0) ? scope.col.value : '--'}}</div>
                            <div v-else-if="!scope.col.formType" style="word-break: break-word;">{{(scope.col.value || scope.col.value === 0) ? scope.col.value : '--'}}</div>
                            <component
                                v-else
                                :is="scope.col.formType?.default || scope.col.formType"
                                :value="scope.col.value"
                                :params="scope.col.params"
                                :disabled="scope.col.disabled"
                                :data='scope.col.data'
                                :colData='scope.col.colData'
                                selection="none"
                                :showHead="false"

                                />
                        </div>
                    </div>
                </template>
            </layout>
        </template>
        </timeline>
    </div>
</template>
<script>
import timeline from '../components/timeline/timeline'
import layout from '../components/layout/layout'
export default {
    components: {
        timeline,
        layout,
        myRate: () => import('./rate')
    },
    props: {
        value: {
            type: Array
        }
    }
}
</script>
<style lang="stylus" scoped>
@import "./col.styl";
</style>
