<template>
<!-- 详情-沟通交流组件 -->
    <div>
        <my-table 
        ref="table"
        tableUrl='/custom-web/api/customPayment/getList'
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :showHead="false"
        :isPushDown="true"
        :page="false"
        tableHeight="auto"
        ></my-table>
        
    </div>
</template>
<script>
import myTable from '../components/table/table'
import { funds_type,getMap} from '../common/tollDictionary'
export default {
    components: {myTable},
    data() {
        var self = this
        return {
            operate_phase_options: [],
            operate_phase: [],
            content_desc: '',
            spokesman: '',
            tableParam: {
                client_number: self.params.client_number, 
                "page": {
                    "length": 20,
                    "pageNo": 1
                }
            },
            colData: []
        }
    },
    
    props: {
        params: {
            type: Object,
            default() {
                return {}
            }
        },
        locationKey: {
            type: String
        }
    },
    async mounted() {
    //    this.maps = await this.getMap();
       this.getTollInfo();
    },
        
    methods: {
        getTollInfo(){
            let self = this;
           let pay_type_opt = [
                {label:'POS机',value:'POS'},
                {label:'银行支付',value:'BANK_TRANSFER'},
                {label:'支付宝',value:'ALIPAY'},
                {label:'微信',value:'WECHAT'},
                {label:'现金',value:'CASH'},
            ]
            self.colData = [
                {
					label: '支付编码',
					prop: 'pay_number',
                    redirectClick:(row) => {
                        // console.log(row,this.info)
                        row.loginShopName = row.shop_name
						this.$root.eventHandle.$emit('creatTab', {
                            name: '查看单据详情',
                            component: () => import('@components/dz_customer/tollInfo.vue'),
                            params: {
                                row: row,
                                info: this.info

                            }
                        })
					},
					width: 133
                },
                {
					label: '支付日期',
					prop: 'pay_date',
                     filter: 'date',
                    formats: 'yyyy-MM-dd',
					width: 133
                },
                {
					label: '款项类别',
					prop: 'customized_pay_cn',
                    options: funds_type,
                    filter: 'select',
					width: 133
                },
                {
					label: '支付方式',
					prop: 'pay_type_cn',
                     options: pay_type_opt,
                    filter: 'select',
					width: 133
                },
                {
					label: '支付金额',
					prop: 'pay_amount',
					width: 133
                },
                {
					label: '店铺名称',
					prop: 'shop_name',
					width: 133
                },
                {
					label: '备注',
					prop: 'remark'
                }
            ]
        },
       getMap(){
            let maps = [
                new Promise((resolve, reject) => {
                    getMap(tollDictionary => {
                        resolve(tollDictionary)
                    })
                })
            ]
            return Promise.all(maps)
        },
       
    }
}
</script>
