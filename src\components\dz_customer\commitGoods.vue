<!-- 提交 -->
<template>
	<div class='xpt-flex'>
		<xpt-headbar>
				<!-- <el-button type='primary' size='mini' @click="updateAreaMappingSetsFun" slot='left'>编辑</el-button> -->
				<el-button type='success' size='mini' @click='save(1)' slot='left' :loading='false' :disabled='false'>提交
				</el-button>
			</xpt-headbar>
		<el-row>
			<el-form>
				<el-col :span="22">
				<el-form-item label="标签">
					<el-checkbox-group v-model="customer.changeContent" style="display:inline-block;">
						<el-checkbox v-for="item in typeOptions" :key="item.id" :label="item.code" >{{item.name}}</el-checkbox>
					</el-checkbox-group>
			
			</el-form-item>
			</el-col>
			<el-col :span='22' class="addHeight">
				<el-form-item label="说明">
					<el-input type='textarea' v-model="customer.remark" size='mini' style="width:80%" :maxlength='200' resize="none"></el-input>
				</el-form-item>
				</el-col> 
			</el-form>
		</el-row>
	</div>
</template>
<script>

export default {
	props: ['params'],
	
	data() {
		let self = this
		return {
			customer:{
				changeContent:[],
				remark:''
			},
			goodsList:[],
			selectData:[],
			typeOptions:[],
			
		
            list:[],
			msgBtns: [
				{
					type: 'info',
					txt: '提交',
					click: this.save
				}, {
					type: 'info',
					txt: '取消',
					click: () => {
						// this.showExportList("EXCEL_TYPE_MSG_LOG")
						// this.params.callback(this.selectData);
						self.$root.eventHandle.$emit("removeAlert", this.params.alertId);
					}
				}
			]
		}
	},
 
	methods: {
		
       selectionChange(obj) {
			this.selectData = obj;
		},
		save(){
			let self = this;
			if(!this.customer.changeContent.length && !this.customer.remark){
				this.$message.error('处理标签及内容必选二者任意选一个')
				return false;
			}
			let content = '';
			this.customer.changeContent.forEach(item=>{
				this.typeOptions.forEach(oItem=>{
					if(item == oItem.code){
						content += oItem.name+',';
					}
				})
			})
			this.params.callback({content:content,remark:this.customer.remark})
			this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
		},
     
		},
       
	mounted(){
		// console.log(this.params.goodsList);
       this.goodsList = this.params.goodsList
		this.typeOptions = __AUX.get('custom_software_problem_submit')

	},
}	
</script>
<style type="text/css" scoped>
.el-input{
	width: 150px;
}
</style>
