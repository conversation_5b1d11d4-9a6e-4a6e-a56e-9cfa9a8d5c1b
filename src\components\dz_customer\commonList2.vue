<!--定制业务源订单&量尺&设计&合同列表-->
<template>
  <div class="searchBox">
    <div class="search-content">
      <form-create
        :formData="queryItems"
        @save="query"
        savetitle="查询"
        ref="formCreate"
      ></form-create>
    </div>
    <dz-list
      :data="customerList"
      :colData="colData"
      :pageTotal="pageTotal"
      :btns="btnsFilter"
      selection="checkbox"
      :tools="tools"
      toolWidth="220px"
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
      @selection-change="selectionChange"
      orderNo
      ref="dzList"
      countUrl="/afterSale-web/api/forced/closure/query/count"
      @count-off="countOff"
      :showCount="showCount"
    >
    <xpt-import v-if="importParam.uploadUrl" slot='btns' :taskUrl='importParam.uploadUrl' :otherParams="importParam.otherParams" class='mgl10'></xpt-import>
    </dz-list>
  </div>
</template>
<script>
import fn from "@/common/Fn.js";
import _ from "loadsh";
import { inspectPaymentStatus, getRole, getShopInfo } from "./common/api";
import { getClientStatus } from "./common/map";
import {
  client_status,
  cusTypeopt,
  client_temp,
  getMap,
} from "./common/clientDictionary";
import {
  life_stage,
  house_type,
  consume_type,
  total_area,
  decoration_stage,
  client_property,
  house_price,
  budget,
} from "./common/measureDictionary";
import btnStatus from "./common/mixins/btnStatus";
import formCreate from "./components/formCreate/formCreate";
import dzList from "./components/list/list5";
import {
  addDesignAssess,
  addConnect,
  addDesign,
  addContract,
  tips,
} from "./alert/alert";
export default {
  components: { formCreate, dzList },
  mixins: [btnStatus],
  props: {
    initParam: {
      type: Object,
      default() {
        return {};
      },
    },
    importParam: {
      type: Object,
      default() {
        return {
          uploadUrl:'',
          otherParams:''
        };
      },
    },
    params: {
      type: Object,
    },
    url: {
      type: String,
      default: "/custom-web/api/customSysTrade/getCustomSysTradeList",
    },
    
    btns: {
      type: Array,
      default() {
        return [];
      },
    },
    tools: {
      type: Array,
      default() {
        return [];
      },
    },
    defaultValue: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    btnsFilter() {
      return this.refreshBtn.concat(this.btns);
    },
  },
  data() {
    let self = this;
    return {
      showCount: false,
      countOffFlag:false,
      info: {},
      searchParam: {
        client_start_status: this.defaultValue.client_status[0],
        client_end_status: this.defaultValue.client_status[1],
        client_temp: this.defaultValue.client_temp,
        trade_type: "ORIGINAL",
      },
      value: "",
      queryItems: [],
      createDate: "",
      contractDate: "",
      customerList: [],
      pageNow: 1,
      pageSize: 20,
      pageTotal: 0,
      infoList:[],
      refreshBtn: [
        {
          type: "primary",
          txt: "刷新",
          loading: false,
          click: () => {
            this._getDataList();
          },
        },
      ],
      colData: Object.freeze([
        {
          label: "店名",
          prop: "shop_name",
          width: "100",
        },
        {
          label: "订单号",
          prop: "client_number",
          width: "180",
          redirectClick(d) {
            self.detail(d);
          },
        },
        {
          label: "客户名",
          prop: "client_name",
          width: "100",
        },
        {
          label: "手机",
          prop: "client_mobile",
          width: "90",
          format: 'hidePhoneNumber'
        },
        {
          label: "建档日期",
          prop: "create_time1",
          width: "90",
        },
        {
          label: "建档人",
          prop: "shopping_guide_name",
          width: "100",
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: "80",
        },
        {
          label: "量尺日期",
          prop: "measure_date1",
          width: "90",
        },
        
        {
          label: "合同提交日期",
          prop: "compact_date1",
          width: "90",
        },
        {
          label: "下单日期",
          prop: "design_date1",
          width: "90",
        },
        {
          label: "约定交付",
          prop: "promise_consign_date1",
          width: "120",
        },
        {
          label: "状态",
          prop: "client_status_cn",
          width: "auto",
        },
      ]),
    };
  },
  methods: {
    async getInfo() {
      this.infoList = await getShopInfo() || [];
      console.log(this.infoList )
      // this.info = this.infoList.length? this.infoList[0]:{};
      // this.$emit("getInfo", this.info);
      this.getQueryItems();
    },
    getInfoNoShop() {
      this.getQueryItems();
    },
    getQueryItems() {
      // 初始化表格数据
      this._getDataList();

      let c_status = [];
      let _self = this;
      let shopOption = [];
      this.infoList.forEach(item=>{
        shopOption.push({
          value:item.shopCode,
          label:item.loginShopName,
        })
      })
      getMap((map) => {
        let cs = map.client_status.filter((item) => true);
        // cs = cs.slice(0,cs.length-1)
        cs.forEach((item) => {
          c_status.push(item);
        });
      });
      this.queryItems = [
        {
          cols: [
            // {formType: 'elInput', prop: 'shop_name', label: '专卖店', span: 24},
            {
              // formType: "myText",
              formType: "elSelect",
              prop:'shop_code',
              label: "专卖店",
              value: this.info.shopCode,
              options: shopOption,
              span: 24,
              event: {
                change(v, col, formData, getItem) {
                  console.log(getItem,v)
                  _self.infoList.forEach(item=>{

                    if(v == item.shopCode){
                      _self.info = item;
                      _self.$emit("getInfo", _self.info);
                      
                    }
                  })
                  
                },
              },
            },
            { formType: "elInput", prop: "client_number", label: "订单编号" },
            {
              formType: "myInput",
              prop: "client_mobile",
              label: "客户电话",
              type: "string",
              maxlength: 11,
              event: {
                input(v, col) {
                  col.value = v.replace(/\D/g, "");
                },
              },
            },
            { formType: "elInput", prop: "client_name", label: "客户名称" },
            {
              formType: "elSelect",
              prop: "client_type",
              label: "订单类型",
              options: cusTypeopt,
            },
            {
              formType: "elSelect",
              prop: "client_temp",
              value: this.defaultValue.client_temp,
              label: "订单分类",
              options: client_temp,
              event: {
                change(v, col, formData, getItem) {
                  let client_status = getItem("client_status");
                  client_status.value =
                    v == 2 ? ["", ""] : _self.defaultValue.client_status;
                },
              },
            },
            {
              formType: "selectRange",
              prop: "client_status",
              value: this.defaultValue.client_status,
              props: ["client_start_status", "client_end_status"],
              label: "订单状态",
              options: [c_status, c_status],
            },
            // {formType: 'elInput', prop: 'client_address', label: '客户地址'},
            { formType: "elInput", prop: "shopping_guide_name", label: "导购" },
            {
              formType: "elDatePicker",
              prop: "create_time",
              props: ["start_create_date", "end_create_date"],
              label: "建档日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
            { formType: "elInput", prop: "designer_name", label: "设计师" },
            // {formType: 'elSelect', prop: 'checkOrder', label: '有无查房', options:[{label:'有', value: 1}, {label: '无', value: 2}]},
            // {formType: 'elInput', prop: 'loft_coordinate', label: '楼盘位置'},
            {
              formType: "elDatePicker",
              prop: "compact_date",
              props: ["start_compact_date", "end_compact_date"],
              label: "合同提交日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
            {
              formType: "elSelect",
              prop: "life_stage",
              label: "生活阶段",
              options: life_stage,
            },
            {
              formType: "elSelect",
              prop: "house_type",
              label: "房屋类型",
              options: house_type,
            },
            {
              formType: "elSelect",
              prop: "consume_type",
              label: "消费类型",
              options: consume_type,
            },
            // {formType: 'elSelect', prop: 'shoukuanStatus', label: '收款状况', options:[{label:'有', value: 1}, {label: '无', value: 2}]},
            {
              formType: "elSelect",
              prop: "total_area",
              label: "面积",
              options: total_area,
            },
            {
              formType: "elSelect",
              prop: "decoration_stage",
              label: "装修阶段",
              options: decoration_stage,
            },
            {
              formType: "elSelect",
              prop: "client_property",
              label: "客户性质",
              options: client_property,
            },
            {
              formType: "elSelect",
              prop: "house_price",
              label: "房价",
              options: house_price,
            },
            // {formType: 'elSelect', prop: 'budget', label: '预算', options: budget}
          ],
        },
      ];
      if (this.params.pageInfo === "dd") {
        this.queryItems[0].cols = this.queryItems[0].cols.concat({
          // 2021 2.20
          formType: "elInput",
          prop: "receiver_name",
          label: "收货人",
        });
      }
    },
    // 监听每页显示数更改事件
    pageChange(pageSize, param) {
      this.pageSize = pageSize;
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    // 监听页数更改事件
    currentPageChange(page, param) {
      this.pageNow = page;
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    selectionChange(data){
    this.$emit("selectionChange", data);
    },
    detail(d) {
      this.$root.eventHandle.$emit("creatTab", {
        name: "订单详情",
        component: () =>
          import("@components/dz_customer/clientInfo/clientInfo.vue"),
        params: {
          customerInfo: d,
          lastTab: this.params.tabName,
        },
      });
    },

    // 查询所有客户列表
    query(param) {
      let self = this;
      Object.assign(this.searchParam, param);
      
      new Promise((res,rej)=>{
        this._getDataList(res);
			}).then(()=>{
				if(self.pageNow != 1){
					self.pageTotal = 0;
				}
				self.showCount = false;

			})
    },
    countOff(){

			let self = this,
			url = "/custom-web/api/customSysTrade/getCustomSysTradeListCount";
			if(!self.customerList.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;
      let data = {};
      Object.assign(data, this.searchParam, this.initParam);
      data.page = {
        length: this.pageSize,
        pageNo: this.pageNow,
      };
			self.ajax.postStream(url,data,function(response){
					if(response.body.result){
						
						self.pageTotal = response.body.content;
						self.showCount = true;
						self.countOffFlag = false;

					}else{
						self.$message.error(response.body.msg);
					}
				});
		},
    _getDataList(resolve) {
      let self = this;
      var url = this.url;
      let data = {};
      Object.assign(data, this.searchParam, this.initParam);
      data.page = {
        length: this.pageSize,
        pageNo: this.pageNow,
      };
      let refreshBtn = this.refreshBtn[0];
      refreshBtn.loading = true;
      this.ajax.postStream(
        url,
        data,
        (d) => {
          refreshBtn.loading = false;
          if (d.body.result) {
            let content = d.body.content || {};
            const listLength = _.get(content, "list.length");
            for (var i = 0; i < listLength; i++) {
              content.list[i].create_time1 = fn.dateFormat(
                content.list[i].create_time,
                "yyyy-MM-dd"
              );
              content.list[i].measure_date1 = fn.dateFormat(
                content.list[i].measure_date,
                "yyyy-MM-dd"
              );
              content.list[i].promise_consign_date1 = fn.dateFormat(
                content.list[i].promise_consign_date,
                "yyyy-MM-dd"
              );
              content.list[i].compact_date1 = fn.dateFormat(
                content.list[i].compact_date,
                "yyyy-MM-dd"
              );
              content.list[i].design_date1 = fn.dateFormat(
                content.list[i].design_date,
                "yyyy-MM-dd"
              );
              content.list[i].client_status_value =
                content.list[i].client_status;
              content.list[i].shopType = this.info ? this.info.shopType : "";
            }
            this.customerList = content.list || [];
            // this.pageTotal = content.count || 0;
            if(!self.showCount){
              self.pageTotal = content.list.length == (self.pageSize+1)? (self.pageNow*self.pageSize)+1:(self.pageNow*self.pageSize);
            }
            resolve && resolve();
          } else {
            this.$message({
              message: d.body.msg,
              type: "error",
            });
          }
        },
        (err) => {
          this.$message.error(err);
          refreshBtn.loading = false;
        },
        this.params.tabName
      );
    },
  },
  async mounted() {
    this.$root.eventHandle.$on("refreshclientList", this._getDataList);
  },
  beforeDestroy() {
    this.$root.eventHandle.$off("refreshclientList", this._getDataList);
  },
};
</script>
<style scoped>
.searchBox {
  height: 99%;
  display: flex;
  flex-direction: column;
}
.search-content {
  border: 1px #aaa solid;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
.search-btn {
  margin: 0 auto;
  width: 1000px;
  text-align: center;
  padding-bottom: 10px;
}
</style>
