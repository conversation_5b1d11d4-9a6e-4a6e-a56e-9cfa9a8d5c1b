// api管理
import ajax from '@common/ajax'
import axios from 'axios'
import {Message} from 'element-ui'

let one = {} //判断是否需要等上一次请求结束
function getList(url, data){
    return new Promise((resolve, reject) => {
        ajax.postStream(url,data,(res) =>{
            let body = res.body
            if(body.result){
                resolve(Array.isArray(body.content) ? body.content : typeof body.content === 'object' ? body.content.list || [] :  [] )
            } else {
                resolve([])
            }
        })
    })
}
function handler(res, hideError, showSuccess)  {
    if(!res.body.result) {
        !hideError && Message.error(res.body.msg)
    } else {
        showSuccess && Message.success(res.body.msg)
    }
}
function handlerErr(err,hideError = false)  {
    !hideError && Message.error(String(err))
}
const downFile = (data, url, type) => {
    return new Promise((resolve, reject) => {
        axios({
            url: url,
            responseType: 'blob',
            data: data,
            method: 'post'
        })
        .then(function (response) {
            let data = response.data
            console.log(response)
            let blob = new Blob([data], { type: type });
            let url = window.URL.createObjectURL(blob);
            const link = document.createElement("a"); // 创建a标签
            link.href = url;
            link.download = "fileDown" + new Date().getTime(); // 重命名文件
            link.click();
            URL.revokeObjectURL(url); // 释放内存
            resolve()
        })
    })
}
const requestCommon = (url, data, hideError=false, showSuccess=false, once=false) => {
    if(once) {
        if(one[url]) {
            Message.error('请等待上一次请求结束')
            return
        }
    }
    return new Promise((resolve, reject) => {
        once && (one[url]  = true)
        ajax.postStream(url,data,(res) => {
            one[url] = false
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body || {})
            } else {
                resolve(body||{})
            }
        }, err => {
            one[url] = false
            resolve({})
            handlerErr(err, hideError)
        })
    })
}

// 获取当前登录人的客户列表
export async function getClientByLogin() {
    const data = await getList('/custom-web/api/customPayment/queryClientListByRole')
    return data
}
// 获取当前登录人的门店信息
export function getShopInfo(role){
    if(role) {
        let canGetShopInfoRole = ['DZ_SJS', 'DZ_DG', 'DZ_DZ', 'DZ_SJZG']
        if(role.findIndex(item => canGetShopInfoRole.includes(item)) !==-1) {
            return new Promise((resolve, reject) => {
                ajax.postStream('/custom-web/api/customPayment/getShopInfo','',(res) =>{
                    let body = res.body
                    if(body.result && body.content ){
                        resolve(body.content || [])
                    } else {
                        resolve([{loginShopName: '--'}])
                    }
                })
            })
        } else {
            return new Promise((resolve, reject) => {
                resolve([])
            })
        }
    }
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customPayment/getShopInfo','',(res) =>{
            let body = res.body
            if(body.result){
                resolve(body.content || [])
            } else {
                resolve([])
            }
        })
    })
}
// 获取当前登录人的门店信息
export function shopVerify(role){
    if(role) {
        let canGetShopInfoRole = ['DZ_SJS', 'DZ_DG', 'DZ_DZ', 'DZ_SJZG']
        if(role.findIndex(item => canGetShopInfoRole.includes(item)) !==-1) {
            return new Promise((resolve, reject) => {
                ajax.postStream('/custom-web/api/customPayment/shopVerify','',(res) =>{
                    let body = res.body
                    if(body.result){
                        resolve(body.content || {})
                    } else {
                        resolve({})
                    }
                })
            })
        } else {
            return new Promise((resolve, reject) => {
                resolve({loginShopName: '--'})
            })
        }
    }
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customPayment/shopVerify','',(res) =>{
            let body = res.body
            if(body.result){
                resolve(body.content || {})
            } else {
                resolve({})
            }
        })
    })
}

// 同步订单和支付信息
export function syncTradeAndPaymentToV2(id){
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customPayment/syncTradeAndPaymentToV2',id,(res) =>{
            let body = res.body
            if(body.result){
                resolve(body.content || {})
            } else {
                resolve({})
            }
        })
    })
}

// 获取用户角色
let role = {}
export function getRole(){
    let userInfo =  sessionStorage.getItem('userInfo') ? JSON.parse(sessionStorage.getItem('userInfo')) : {}
    let user = userInfo.user || {}
    let id = user.id
    return new Promise((resolve, reject) => {
        if(id && role[id]) {
            resolve(role[id])
            return
        }
        ajax.postStream('/custom-web/api/customClient/getPersonRole','',function(data){
            data = data.body
            if(!data.result) {
                Message.error('获取角色信息失败')
            }
            id && (role[id] = data.content || [])
            resolve(data.content || [])
        },function(data){
            Message.error('获取角色信息失败')
            resolve([])
        })
    })
}
//查询当前客户是否有支付记录
export function inspectPaymentStatus(client_number) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customPayment/inspectPaymentStatus',client_number,(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content)
            } else {
                resolve(false)
            }
        })
    })
}

//查询当前客户是否有商品
export function isHaveGoods(client_number) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customGoods/getList',{client_number},(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content.length)
            } else {
                resolve(false)
            }
        })
    })
}
// 生成附件id
export function createId() {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/custom/createId','',(res) => {
            let body = res.body
            handler(res, false, false)
            if(body.result) {
                resolve(body.content)
            } else {
                resolve('')
            }
        }, err => {
            handlerErr(err)
        })
    })
}
// 批量生成附件id
export function createIds(num) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/custom/createIds',num,(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content)
            } else {
                resolve([])
            }
        })
    })
}

// 查看附件
export function getFileList(data) {
    data.page_size = 10000
    return new Promise((resolve, reject) => {
        ajax.postStream('/file-iweb/api/cloud/file/list',data,(res) => {
            let body = res.body
            resolve(body.result ? body.content.list : [])
        })
    })
}

// 删除附件
export function deleteFile(data) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/file-iweb/api/cloud/file/delete',data,(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content)
            } else {
                reject(false)
            }
        })
    })
}

// 获取量尺信息
export function getMeasure(data, hideError=false, showSuccess=false) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customMeasure/get',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body.content || {})
            } else {
                resolve({})
            }
        }, err => {
            resolve({})
            handlerErr(err, hideError)
        })
    })
}

// 获取设计登记
export function getDesign(data) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customDesign/getRegister',data,(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content || {})
            } else {
                resolve({})
            }
        })
    })
}

//查询设计信息
export function getDesignInfo(data, hideError = false, showSuccess=false) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customDesign/get',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body.content || {})
            } else {
                resolve({})
                handlerErr(err, hideError)
            }
        })
    })
}

// 获取客户信息
export function getClientInfo(data) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customClient/getClientInfoByClientNumber',data,(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content || {})
            } else {
                resolve({})
            }
        })
    })
}

// 获取订单信息
export function getOrderInfo(data) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customSysTrade/getSysTradeByNumber',data,(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content || {})
            } else {
                resolve({})
            }
        })
    })
}

// 生成压缩文件
export function downloadZip(data) {
    return downFile(data.map(item => {
        return  {
            name: item.name,
            path: item.path
        }
    }), '/custom-web/api/downloadFile/downloadZip', "application/zip")
}
// 获取材质或花色
export function getMaterial(data) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customDictionary/getList',data,(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content || {})
            } else {
                resolve({})
            }
        })
    })
}

// 获取商品列表
export function getGoods(data) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customGoods/getList',data,(res) => {
            let body = res.body
            if(body.result) {
                resolve(body.content || [])
            } else {
                resolve([])
            }
        })
    })
}

export function importFile(data) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customBom/importFile',data,(res) => {
            let body = res.body
            resolve(body)
        }, err => {
            resolve()
        })
    })
}

export function viewRegister(data) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom_web/api/customerViewRegister/save',data,(res) => {
            let body = res.body
            resolve(body)
        }, err => {
            resolve({})
        })
    })
}

// 获取查房记录
export function getViewRegister(data, hideError, showSuccess) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customerViewRegister/getViewRegister',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body.content || [])
            } else {
                resolve([])
            }
        }, err => {
            resolve([])
            handlerErr(err, hideError)
        })
    })
}

// 添加查房
export function saveViewRegister(data, hideError, showSuccess) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customerViewRegister/save',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body || {})
            } else {
                resolve({})
            }
        }, err => {
            resolve({})
            handlerErr(err, hideError)
        })
    })
}

// 添加沟通登记
export function saveCustomCommunication(data, hideError=false, showSuccess=false) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customCommunication/save',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body || {})
            } else {
                resolve({})
            }
        }, err => {
            resolve({})
            handlerErr(err, hideError)
        })
    })
}

// 获取沟通记录
export function getCustomCommunication(data, hideError, showSuccess) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customCommunication/get',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body.content || [])
            } else {
                resolve([])
            }
        }, err => {
            handlerErr(err, hideError)
        })
    })
}

// 添加交流
export function converseOpinion(data, hideError=false, showSuccess=false) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/converseOpinion/save',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body || {})
            } else {
                resolve({})
            }
        }, err => {
            resolve({})
            handlerErr(err, hideError)
        })
    })
}

// 商品订单重新审核
export function customGoodsSubmit(data, hideError=false, showSuccess=false) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customGoods/submit',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body || {})
            } else {
                resolve({})
            }
        }, err => {
            resolve({})
            handlerErr(err, hideError)
        })
    })
}
// 商品订单一键算价
export function customGoodsValuation(data, hideError=false, showSuccess=false) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customGoods/valuation',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body || {})
            } else {
                resolve({})
            }
        }, err => {
            resolve({})
            handlerErr(err, hideError)
        })
    })
}
// 商品订单提交
export function customGoodsSubmitOrder(data, hideError=false, showSuccess=false) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customGoods/submit',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body || {})
            } else {
                resolve({})
            }
        }, err => {
            resolve({})
            handlerErr(err, hideError)
        })
    })
}

// 创建商品订单
export function addCustomGoods(data, hideError=false, showSuccess=false, once=false) {
    let url = '/custom-web/api/customGoods/save'
    return requestCommon(url, data, hideError, showSuccess, once)
}


// 生成审图审价报表
export function exportSj(data) {
    return downFile(data, '/custom-web/api/guideReport/guideReviewReport', "application/vnd.ms-excel")
}

// 生成拆单报表
export function exportCd(data) {
    return downFile(data, '/custom-web/api/guideReport/guideApartReport', "application/vnd.ms-excel")
}

// 生成拆审报表
export function exportCs(data) {
    return downFile(data, '/custom-web/api/guideReport/guideSplitAuditReport', "application/vnd.ms-excel")
}
// 生成订单售价报表
export function exportDj(data) {
    return downFile(data, '/custom-web/api/guideReport/guideOrderReport', "application/vnd.ms-excel")
}
// 生成补单商品信息报表
export function exportBd(data) {
    return downFile(data, '/custom-web/api/guideReport/guideBOrderList', "application/vnd.ms-excel")
}
// 生成责任报表
export function exportZrd(data) {
    return downFile(data, '/custom-web/api/customAnalysisRelation/getAnalysisReport', "application/vnd.ms-excel")
}
// 获取店铺分类

export async  function getShopSort() {
    const data = await getList('/user-web/api/auxiliary/getAuxiliaryDataList', {categoryCode: 'SHOP_CLASSIFY'})
    return data
}

// 创建补单
export function addSupple(data, hideError=false, showSuccess=false, once=false) {
    let url = '/custom-web/api/customSupplyTrade/save'
    return requestCommon(url, data, hideError, showSuccess, once)
}

// 更新补单
export function updateSupple(data, hideError=false, showSuccess=false, once=false) {
    let url = '/custom-web/api/customSupplyTrade/update'
    return requestCommon(url, data, hideError, showSuccess, once)
}

// 获取补单信息
export function getSupple(data, hideError=false, showSuccess=false, once=false) {
    let url = '/custom-web/api/customSupplyTrade/getSupplyTrade'
    return requestCommon(url, data, hideError, showSuccess, once)
}

// 提交补单
export function submitSupple(data, hideError=false, showSuccess=false, once=false) {
    let url = '/custom-web/api/customSupplyTrade/submit'
    return requestCommon(url, data, hideError, showSuccess, once)
}
// 同步数据
export function createSwjSupplyGoodsOrder(data, hideError=false, showSuccess=false, once=false) {
    let url = '/custom-web/api/customSupplyTrade/createSwjSupplyGoodsOrder'
    return requestCommon(url, data, hideError, showSuccess, once)
}
// 提审补单
export function verifySupple(data, hideError=false, showSuccess=false, once=false) {
    let url = '/custom-web/api/customSupplyTrade/submitReview'
    return requestCommon(url, data, hideError, showSuccess, once)
}

// 推物料
export function pushMaterial(client_number, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customPush/material'
    return requestCommon(url, {client_number}, hideError, showSuccess)
}

// 同步商品
export function synGoods(client_number, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customGoods/syn'
    return requestCommon(url, {client_number}, hideError, showSuccess)
}
// 审核订单
export function auditNewScmMergeTrade(client_number, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/reviewDrawingPrice/auditNewScmMergeTrade'
    return requestCommon(url, client_number, hideError, showSuccess)
}

// 补单流程完成，同步在新平台生成售后单和相关问题单
export function customSupplyAftersale(client_number, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSupplyAftersale/save'
    return requestCommon(url, client_number, hideError, showSuccess)
}

//同步在新平台生成售后单和相关问题单成功后，同步在新平台生成售后方案和补件单
export function saveAfterPlan(client_number, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSupplyAftersale/saveAfterPlan'
    return requestCommon(url, client_number, hideError, showSuccess)
}

//同步在新平台生成售后方案和补件单成功后，提交补件方案到新平台审核
export function submitAfterPlan(client_number, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSupplyAftersale/submitAfterPlan'
    return requestCommon(url, client_number, hideError, showSuccess)
}

// 新增订单
export function addOrder(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSysTrade/saveCustomSysTrade'
    return requestCommon(url, data, hideError, showSuccess)
}
// 修改订单
export function editOrder(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSysTrade/updateCustomSysTrade'
    return requestCommon(url, data, hideError, showSuccess)
}
// 保存客户信息前，校验客户名称在客户表是否存在
export function validateReceiveInfoDeliverMethod(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customClient/getDuplicateCheck'
    return requestCommon(url, data, hideError, showSuccess)
}

// 根据地址ID判断地址信息是否能修改
export function judgeAddress(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSysTrade/judgeAddress'
    return requestCommon(url, data, hideError, showSuccess)
}

// 查询区域信息
export function getArea(data, hideError=false, showSuccess=false) {
    return new Promise((resolve, reject) => {
        ajax.postStream('/custom-web/api/customArea/get',data,(res) => {
            let body = res.body
            handler(res, hideError, showSuccess)
            if(body.result) {
                resolve(body.content || {})
            } else {
                resolve({})
            }
        }, err => {
            resolve({})
            handlerErr(err, hideError)
        })
    })
}
// 下推采购商品驳回
export function rejectPushPurchase(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSysTrade/rejectPushPurchase'
    return requestCommon(url, data, hideError, showSuccess)
}
// 下推采购商品驳回-添加勿动权限
export function rejectPushPurchaseByPermission(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSysTrade/rejectPushPurchase?permissionCode=DZ_ERROR'
    return requestCommon(url, data, hideError, showSuccess)
}

// 转单
export function changeOrder(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customSysTrade/changeOrder'
    return requestCommon(url, data, hideError, showSuccess)
}

// 合同详情
export function getCompactInfo(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/customCompact/getCompactInfoByClient'
    return requestCommon(url, data, hideError, showSuccess)
}
// 合同详情 判断店铺的拎包属性
export function getShopLBInfo(data, hideError=true, showSuccess=false) {
    let url = '/app-web/app/h5/shop/bagStore/matchBagStore.do'
    return requestCommon(url, data, hideError, showSuccess)
}

// 抢单
export function getOrders(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/reviewDrawingPrice/getOrders'
    return requestCommon(url, data, hideError, showSuccess)
}

// 多个拆审锁定
export function lockingMoreAuditGoods(data, hideError=true, showSuccess=false) {
    let url = '/custom-web/api/splitAudit/lockingMoreGoods'
    return requestCommon(url, data, hideError, showSuccess)
}

// 三维家绑定
export function bindSwjAccount(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/bindAccount'
    return requestCommon(url, data, hideError, showSuccess)
}
// 三维家解绑
export function cancelSwjAccount(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/cancelAccount'
    return requestCommon(url, data, hideError, showSuccess)
}
// 三维家设计
export function gotoSwj(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/gotoSwj'
    return requestCommon(url, data, hideError, showSuccess)
}
// 三维家商品方案设计
export function openGoodsDesign(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/openGoodsDesign'
    return requestCommon(url, data, hideError, showSuccess)
}
// 三维家审核
export function openSwjGoods(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/openGoods'
    return requestCommon(url, data, hideError, showSuccess)
}
// 查询报价清单
export function listSwjQuotation(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/listSwjQuotation'
    return requestCommon(url, data, hideError, showSuccess)
}
// 同步三维家商品数据
export function synSwjGoodsMsg(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/synSwjGoodsMsg'
    return requestCommon(url, data, hideError, showSuccess)
}
// 审核拆单同步三维家商品数据
export function receiveSplitMsg(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/receiveSplitMsg'
    return requestCommon(url, data, hideError, showSuccess)
}
// 审核拆单计算价格
export function calculate(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customGoods/calculate'
    return requestCommon(url, data, hideError, showSuccess)
}

// 补单三维家
export function openSupplySwj(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSwj/openSupplySwj'
    return requestCommon(url, data, hideError, showSuccess)
}

// 删除三维家账号
export function deleteAccount(data, hideError=false, showSuccess=true) {
    let url = '/custom-web/api/customSwj/deleteAccount'
    return requestCommon(url, data, hideError, showSuccess)
}

// 根据店铺获取提货方式
export function getdeliverByShop(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSysTrade/getShopDistribution'
    return requestCommon(url, data, hideError, showSuccess)
}
// 撤回
export function withdrawSupply(data, hideError=false, showSuccess=false) {
    let url = '/custom-web/api/customSupplyTrade/withdrawSupply'
    return requestCommon(url, data, hideError, showSuccess)
}