// 客户相关字典
import ajax from '@common/ajax'

export let sexopt = []  //性别
export let client_status = [] //源单状态
export let client_status_supply = [] //补单状态
export let cusTypeopt = [] //客户类型
export let measure_room = [] //量尺空间
export let client_temp = [] //客户性质
export let deliver_method = [] //送货方式
export let post_fee_type = []   //运费类型
export let custom_sys_budget = []   //客户预算

let callback
let map = { 
  client_sex: sexopt, client_status: client_status, client_type: cusTypeopt,
   measure_room, client_temp, deliver_method, post_fee_type, client_status_supply,custom_sys_budget
 }
let isAjax = false
var data = {}
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/customClient/getDictionaryList',data,(res) =>{
  isAjax = true
  res = res.body
  if(res.result){
    for(var key in map) {
      res.content[key] && res.content[key].forEach(item => {
        map[key].push({
          label: item.value,
          value: item.key
        })
      })
    }
    let i
    let newClientStauts = map.client_status.map(item => {
      i = res.content.custom_client_status_code.findIndex(el => el.key === item.value)
      i !== -1 && (item.v = res.content.custom_client_status_code[i].value)
      return item
    })
    map.client_status.splice(0, map.client_status.length)
    // 补单状态
    let status_supply = [
      'CLOSE',
      'IN_DESIGN', //门店下单中
      'REJECT_VERIFY',//拆单驳回
      'SIGNED_CONTRACT_WAITING_VERIFY',//待拆单
      'REJECT_WAITING_SPLIT_ORDER',//拆审驳回
       'IN_VERIFY', //拆单中
      //  'WAITING_CHARGE',
        'WAITING_SPLIT_AUDIT',//待拆审
       'REJECT_WAITING_PURCHASE',//下推驳回
       'IN_SPLIT_AUDIT', //准备生产
      'WAITING_PUSH_GOODS', //生产中
      'WAITING_OUTBOUND', 
      'WAITING_PURCHASE', 
      'COMPLETED'
    ]
    newClientStauts.sort((a,b) => {return a.v-b.v}).forEach(item => {
      map.client_status.push(item)
      status_supply.includes(item.value) && map.client_status_supply.push(item)

    })
  }
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
