// 合同相关字典
import ajax from '@common/ajax'

export let choose_reason = [] //选择原因
export let combo_type = [] //套餐类型
let callback
let map = { choose_reason,combo_type }
let isAjax = false
var data = {}
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/customCompact/getDictionaryList',data,(res) =>{
  isAjax = true
  res = res.body
  if(res.result){
    for(var key in map) {
      res.content[key].forEach(item => {
        map[key].push({
          label: item.value,
          value: item.key
        })
      })
    }
  }
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
