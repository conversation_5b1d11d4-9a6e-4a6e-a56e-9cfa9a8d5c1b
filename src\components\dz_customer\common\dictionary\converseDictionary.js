// 交流意见相关字典
import ajax from '@common/ajax'

export let operate_phase = []
let callback
let map = { operate_phase }
let isAjax = false
var data = {}
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/converseOpinion/getDictionaryList',data,(res) =>{
  isAjax = true
  res = res.body
  if(res.result){
    for(var key in map) {
      res.content[key].forEach(item => {
        map[key].push({
          label: item.value,
          value: item.key
        })
      })
    }
  }
  let new_operate_phase = map.operate_phase.filter(item => item.value !== 'CLASSIFICATION_OPERATE')
  let CLASSIFICATION_OPERATE = map.operate_phase.filter(item => item.value === 'CLASSIFICATION_OPERATE')[0]
  if(CLASSIFICATION_OPERATE) {
    let REVIEW_OPERATE_index = new_operate_phase.findIndex(item => item.value === 'REVIEW_OPERATE')
    if(REVIEW_OPERATE_index) {
      new_operate_phase.splice(REVIEW_OPERATE_index, 0, CLASSIFICATION_OPERATE)
    }
  }
  map.operate_phase.splice(0, map.operate_phase.length)
  new_operate_phase.sort((a,b) => {return a.v-b.v}).forEach(item => {
    map.operate_phase.push(item)
  })
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
