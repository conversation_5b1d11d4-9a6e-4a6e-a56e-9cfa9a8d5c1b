/*
 * @Author: your name
 * @Date: 2021-03-30 19:33:38
 * @LastEditTime: 2021-03-30 19:43:18
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \front-dev\src\components\dz_customer\common\dictionary\supplyDeliverDictionary.js
 */
// 补单相关字典
import ajax from '@common/ajax'

export let bjd_deliver_method = [] //补单配送方式
let callback
let map = { bjd_deliver_method }
let isAjax = false
var data = {}
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/customSupplyTrade/getDeliverMethod',data,(res) =>{
  isAjax = true
  res = res.body
  if(res.result){
    res.content.forEach(item => {
      map.bjd_deliver_method.push({
        label: item.name,
        value: item.code
      })
    })
  }
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
