/*
 * @Author: your name
 * @Date: 2021-03-17 13:46:59
 * @LastEditTime: 2021-03-30 19:39:14
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \front-dev\src\components\dz_customer\common\dictionary\supplyDictionary.js
 */
// 补单相关字典
import ajax from '@common/ajax'

export let supply_reason = [] //补单原因
let callback
let map = { supply_reason }
let isAjax = false
var data = {}
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/customSupplyTrade/getDictionaryList',data,(res) =>{
  isAjax = true
  res = res.body
  if(res.result){
    for(var key in map) {
      res.content[key].forEach(item => {
        map[key].push({
          label: item.value,
          value: item.key
        })
      })
    }
  }
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
