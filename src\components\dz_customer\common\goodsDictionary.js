/*
 * @Author: your name
 * @Date: 2021-03-17 13:46:59
 * @LastEditTime: 2021-03-29 10:31:37
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \front-dev\src\components\dz_customer\common\goodsDictionary.js
 */
// 商品相关字典
import ajax from '@common/ajax'

export let style = [] 
export let category = []
let callback
let map = { style, category }
let isAjax = false
var data = {}
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/customGoods/getDictionaryList',data,(res) =>{
  isAjax = true
  res = res.body
  if(res.result){
    for(var key in map) {
      res.content[key].forEach(item => {
        map[key].push({
          label: item.name,
          value: item.tag
        })
      })
    }
  }
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
