// 商品状态字典
import ajax from '@common/ajax'

export let goods_status = [] //正单商品状态
export let goods_status_supply = [] //补单商品状态
let callback
let map = { goods_status, goods_status_supply }
let isAjax = false
var data = {}
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/customGoods/getGoodsStatusDictionary',data,(res) =>{ 
  isAjax = true
  res = res.body
  if(res.result){
    for(var key in map) {
      res.content[key] && res.content[key].forEach(item => {
        map[key].push({
          label: item.value,
          value: item.key
        })
      })
    }
    // 排序
    let i
    let newGoods_status = map.goods_status.map(item => {
      i = res.content.goods_status_code.findIndex(el => el.key === item.value)
      i !== -1 && (item.v = res.content.goods_status_code[i].value)
      return item
    })
    map.goods_status.splice(0, map.goods_status.length)
    // 补单商品状态
    let status_supply = [
      'WAITING_PUSH_GOODS'
    ]
    newGoods_status.sort((a,b) => {return a.v-b.v}).forEach(item => {
      map.goods_status.push(item)
      !status_supply.includes(item.value) && map.goods_status_supply.push(item)
    })
  }
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
