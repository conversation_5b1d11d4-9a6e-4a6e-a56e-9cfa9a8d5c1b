import map from './clientDictionary'
import {goods_status} from './goodsStatusDictionary'
import {deliver_method} from './clientDictionary'
export function get(type, key) {
  let i = map[type].findIndex(item => item.value == key)
  return i !== -1 ? map[type][i].label : ''
}
export function getClientStatus(key) {
  let i = map.client_status.findIndex(item => item.value == key)
  return i !== -1 ? map.client_status[i].label : ''
}

export function getGoodsStatus(key) {
  let i = goods_status.findIndex(item => item.value == key)
  return i !== -1 ? goods_status[i].label : ''
}
export function getDeliverMethod(key) {
  let i = deliver_method.findIndex(item => item.value == key)
  return i !== -1 ? deliver_method[i].label : ''
}
