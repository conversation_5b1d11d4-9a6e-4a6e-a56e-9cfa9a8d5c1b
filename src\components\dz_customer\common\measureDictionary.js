// 量尺相关字典
import ajax from '@common/ajax'

export let member = []  //常住成员
export let budget = []  //预算
export let total_area = []  //总面积
export let decoration_style = []  //装修风格
export let house_price = []  //房价
export let house_type = []  //房屋类型
export let client_property = []  //客户性质
export let consume_type = []  //消费类型
export let decoration_stage = []  //装修阶段
export let plan_stay = []  //计划入住
export let life_stage = []  //生活阶段
export let tags = []  //量尺空间
let callback
let map = { member: member, budget: budget, total_area: total_area, decoration_style: decoration_style, measure_room_name: tags,
  house_price: house_price,house_type: house_type,client_property: client_property,consumption_type: consume_type,decoration_stage: decoration_stage,
  plan_stay: plan_stay,life_stage: life_stage }
var data = {}
let isAjax = false
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/customMeasure/getDictionaryList',data,(res) =>{
  isAjax = true
  res = res.body
  if(res.result){
    for(var key in map) {
      res.content[key].forEach(item => {
        map[key].push({
          label: item.value,
          value: item.key
        })
      })
    }
  }
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
