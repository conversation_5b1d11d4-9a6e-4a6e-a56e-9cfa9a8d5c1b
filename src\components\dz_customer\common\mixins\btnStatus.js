// 按钮权限控制混入
function getUserInfo() {
  let userInfo =  sessionStorage.getItem('userInfo') ? JSON.parse(sessionStorage.getItem('userInfo')) : {}
  return userInfo.user || {}
}
export default {
  methods:{
    //新增原订单按钮显隐
    isAdd(role) {
      if(role.indexOf('DZ_DG') !== -1 || role.indexOf('DZ_SJS') !== -1) {
        return true
      } else {
        return false
      }
    },
    //修改按钮显隐
    isMod(d, role) {
      let status = [
          'WAITING_DISTRIBUTION_DESIGN', 'WAITING_MEASUREMENT', 'WAITING_DESIGN',
          'IN_DESIGN', 'WAITING_COMMUNICATION', 'IN_COMMUNICATION', 'PAID_WAITING_REPEAT_MEASUREMENT',
          'WAITING_SIGN_CONTRACT'
      ]
      if(status.includes(d.client_status) && (role.indexOf('DZ_DG') !== -1 || role.indexOf('DZ_SJS') !== -1) ) {
        return true
      } else {
        return false
      }
    },
    //未成交按钮显隐
    isUndeal(d, role) {
      let status = [
          'WAITING_DISTRIBUTION_DESIGN', 'WAITING_MEASUREMENT', 'WAITING_DESIGN',
          'IN_DESIGN', 'WAITING_COMMUNICATION', 'IN_COMMUNICATION', 'PAID_WAITING_REPEAT_MEASUREMENT',
          'WAITING_SIGN_CONTRACT','SIGNED_CONTRACT_WAITING_VERIFY','REJECT_VERIFY','IN_VERIFY','WAITING_SPLIT_AUDIT','IN_SPLIT_AUDIT',
          'REJECT_WAITING_SPLIT_ORDER','SPLIT_AUDIT_COMPLETE','ERROR','WAITING_PURCHASE'
      ]
      if(status.includes(d.client_status)) {
        return true
      } else {
        return false
      }
    },
    //未成交按钮显隐
    isChangeGoods(d, role) {
      let status = [
          'REJECT_VERIFY', 'IN_VERIFY', 'REJECT_WAITING_SPLIT_ORDER',
          'IN_VERIFY', 'WAITING_SPLIT_AUDIT', 'IN_SPLIT_AUDIT', 'SPLIT_AUDIT_COMPLETE',
          'ERROR', 'WAITING_PURCHASE','SIGNED_CONTRACT_WAITING_VERIFY'
      ]
      if(status.includes(d.client_status)) {
        return true
      } else {
        return false
      }
    },
    //分配设计师显隐
    isSelectDesigner(d, role) {
      if(d.client_status !== 'WAITING_DISTRIBUTION_DESIGN' || role.indexOf('DZ_DZ') == -1) {
        return false
      } else {
        return true
      }
    },
  
    //设计按钮显隐
    isDesign(d, role) {
      let user = this.getUserInfo()
      let status = [
        'WAITING_DESIGN', 'IN_DESIGN',
      ]
      if(role.indexOf('DZ_SJS') == -1 || d.designer_number !== user.employeeNumber) {
        return false
      } else if(
        status.includes(d.client_status)
      ){
        return true
      } else {
        return false
      }
    },
    //设计按钮新增显隐
    isDesignNew(d, role) {
      let user = this.getUserInfo()
      if(role.indexOf('DZ_SJS') == -1 || d.designer_number !== user.employeeNumber) {
        return false
      } else if(
        d.client_status == 'WAITING_DESIGN'
      ){
        return true
      } else {
        return false
      }
    },
    //设计完成按钮显隐
    isDesignAssess(d, role) {
      let user = this.getUserInfo()
      if(role.indexOf('DZ_SJS') == -1 || d.designer_number !== user.employeeNumber) {
        return false
      } else if(d.client_status == 'IN_DESIGN'){ 
        return true
      } else {
        return false
      }
    },
    //量尺登记编辑按钮显隐
    isMeasure(d, role) {
      let user = this.getUserInfo()
      let status = [
        'WAITING_MEASUREMENT', 'WAITING_DESIGN', 'IN_DESIGN', 'WAITING_COMMUNICATION',
        'IN_COMMUNICATION', 'PAID_WAITING_REPEAT_MEASUREMENT', 'WAITING_SIGN_CONTRACT'
      ]
      if(role.indexOf('DZ_SJS') == -1 || d.designer_number !== user.employeeNumber) {
        return false
      } else if(
        status.includes(d.client_status)
      ){
        return true
      } else {
        return false
      }
    },
    //量尺登记新增按钮显隐
    isMeasureNew(d, role) {
      let user = this.getUserInfo()
      if(role.indexOf('DZ_SJS') == -1 || d.designer_number !== user.employeeNumber) {
        return false
      } else if(
        d.client_status == 'WAITING_MEASUREMENT'
      ){
        return true
      } else {
        return false
      }
    },
    //合同登记按钮显隐
    isContract(d, role, client_status) {
      let user = this.getUserInfo()
      // 下推采购之前可以允许修改合同是否加急，其余禁用
      let currentClientStatusCodeIndex =  client_status.findIndex(item => item.value === d.client_status)
      let currentClientStatusCode = currentClientStatusCodeIndex !== -1 ? Number(client_status[currentClientStatusCodeIndex].v) : ''
      let canOnlyUrgent = currentClientStatusCode  ? currentClientStatusCode > 71 && currentClientStatusCode <= 180   : false
      if(role.indexOf('DZ_SJS') == -1 || d.designer_number !== user.employeeNumber) {
        return false
      } else if(
        ['WAITING_SIGN_CONTRACT', 'IN_SIGN_CONTRACT'].includes(d.client_status) || canOnlyUrgent
      ){
        return true
      } else {
        return false
      }
    },
    //合同下单中商品按钮
    isGoodsList(d, role) {
      let user = this.getUserInfo()
      let status = ['REJECT_VERIFY','IN_DESIGN']
      if(role.indexOf('DZ_SJS') == -1 || d.designer_number !== user.employeeNumber) {
        return false
      } else if(
          status.includes(d.client_status) ||
          d.if_disallow === 'true'
        ){
        return true
      } else {
        return false
      }
    },
    //提交审核按钮显隐
    isSubverify(d) {
      return true
    },
    //修改商品
    isChange(d) { 
      // 商品状态是设计中或者审图驳回都可以修改商品和删除商品
      if(d.code < 80) {
        return true
      } else {
        return false
      }
    },
    //审核商品
    isVerify(d) {
      // 订单状态为审图驳回时且商品状态为设计中或者审图驳回
      return d.client_status === 'REJECT_VERIFY' && (d.goods_status === 'REJECT_VERIFY' || d.goods_status === 'IN_DESIGN' )
    },
    //创建商品
    isCreateGoods(d) {
      return true
    },

    //下推
    isPush(d, role) {
      if(role.indexOf('DZ_CSY') == -1) {
        return false
      } else if(d.client_status == 'WAITING_PURCHASE') {
        return true
      } else {
        return false
      }
    },
    // 修改补单
    isEditSupply(d, role) {
      let user = this.getUserInfo()
     
      return role.indexOf('DZ_SJS') !== -1 && d.designer_number === user.employeeNumber && (d.client_status === 'IN_DESIGN' || d.if_disallow === 'true')
    },
    // 查房
    isCheck(d, role) {
      return ( role.indexOf('DZ_SJZG') !== -1 || role.indexOf('DZ_REGIONAL_MANAGER') !== -1 ) &&
      (d.client_status == 'WAITING_COMMUNICATION' || d.client_status == 'IN_COMMUNICATION')
    },
    isCommunicate(d, role) {
      let user = this.getUserInfo()
      return role.indexOf('DZ_SJS') !== -1 && 
      d.designer_number === user.employeeNumber &&
      (d.client_status == 'WAITING_COMMUNICATION' || d.client_status == 'IN_COMMUNICATION')
    },
    transferOrder(d, role) {
      return role.indexOf('DZ_DDZG') !== -1 
    }
  }
}
