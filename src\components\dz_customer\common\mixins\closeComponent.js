// 关闭标签时提示是否关闭
export default {
    mounted() {
        this.params.__close = this.closeComponent
        if(this.params.alertId) {
          this.params.__dz_close = this.closeComponent
        } else {
          this.params.__close = this.closeComponent
        }
    },
    methods: {
        closeComponent(){
          console.log(1)
            let self = this
            let type = self.params.alertId ? 'removeAlert' : 'removeTab'
            let name = self.params.alertId  || self.params.tabName
            if(!self.showCloseComponent || self.showCloseComponent()) {
              self.$root.eventHandle.$emit('openDialog',{
                ok(){
                  self.saveComponent()
                },
                no(){
                  self.$root.eventHandle.$emit(type,name)
                }
              })
            } else {
              self.$root.eventHandle.$emit(type,name)
            }
        }
    }
}