/*
 * @Author: your name
 * @Date: 2021-03-17 13:46:59
 * @LastEditTime: 2021-04-12 10:42:08
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \front-dev\src\components\dz_customer\common\mixins\export.js
 */
// 导出权限公用方法
import { getRole } from "../api";
export default {
    async created() {
        this.btns[0].disabled = !await this.getExport()
        this.getColData();
        this.getQueryItems();
    },
    methods: { 
        async getExport() {
            this.role =await getRole()
            // DZ_REGIONAL_MANAGER 定制区域经理
            // DZ_DZH 店总
            // DZ_DDSJBB 订单售价报表
            // DZ_AREAMANAGE_REPORT 销售报表

            // 总部定制负责人可以导全部
            if (this.role.indexOf("ZBDZFZR") === -1) {
                //订单售价报表 只有店总可以导出
                // 销售报表 只有区域经理和店总可以导出
                if (
                        (
                            
                            this.role.indexOf("DZ_DZH") !== -1 && 
                            this.params.menuInfo.code == "DZ_DDSJBB"
                            
                        ) || 
                        (
                            (
                                this.role.indexOf("DZ_REGIONAL_MANAGER") !== -1 ||
                                this.role.indexOf("DZ_DZH") !== -1
                            ) && 
                            this.params.menuInfo.code == "DZ_AREAMANAGE_REPORT"
                        )
                    ) {
                    return true
                }
                return false
            }
            return true
        },
    }
}
