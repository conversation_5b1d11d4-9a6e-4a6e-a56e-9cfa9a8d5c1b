// 详情 公用方法
import fn from '@/common/Fn.js'
export default {
    methods: {
        getSection(url,param, title, btns, rows){
            return new Promise((resolve,reject) => {
                this.ajax.postStream(url, param, (d) => {
                    let obj = {
                        title: title,
                    }   
                    let value = {}
                    if(d.body.result) {
                        value = d.body.content || {}
                        obj.rows = rows(value)
                    }          
                    obj.btns =  btns(value)
                    resolve(obj)
                }, err => {
                    this.$message.error(err.body.msg)
                    resolve({})
                })
            })
        },
        getSection2(url,param, title, btns, rows){
            return new Promise((resolve,reject) => {
                this.ajax.get(url, (d) => {
                    let obj = {
                        title: title,
                    }   
                    let value = {}
                    if(d.body.result) {
                        value = d.body.content || {}
                        obj.rows = rows(value)
                    }          
                    obj.btns =  btns(value)
                    resolve(obj)
                }, err => {
                    this.$message.error(err.body.msg)
                    resolve({})
                })
            })
        },
        isNull(value) {
            return Object.keys(value).length === 0
        },
        setValue(cols, value) {
            value = value || {}
            let isNull = this.isNull(value)
            let nullTxt = '--'
            cols.forEach(col => {
                if(col.valueFilter) {
                    if(col.valueFilter === 'select') {
                        let str = []
                        let vals = String(value[col.prop]).split(',')
                        let options = col.options || []
                        let i
                        vals.forEach(el => {
                            i = options.findIndex(item => item.value == el)
                            str.push( i !== -1 ? options[i].label : '' )
                        })
                        col.value = isNull ? nullTxt : str.join(',')
                    } else if(col.valueFilter === 'date') {
                        col.value = isNull ? nullTxt : fn.dateFormat(value[col.prop], col.format || 'yyyy-MM-dd hh:mm:ss')
                    } else {
                        col.value = col.valueFilter(isNull ? '' : value[col.prop], value)
                    }
                } else {
                    col.value = value[col.prop]
                }
            })
            return cols
        },
    }
}