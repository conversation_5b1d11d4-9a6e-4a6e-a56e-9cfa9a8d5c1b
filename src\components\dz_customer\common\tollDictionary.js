// 支付相关字典
import ajax from '@common/ajax'

export let pay_type = []  //支付类型
export let sync_status = [] //同步状态
export let funds_type = [] //定价状态
let callback
let map = {
  pay_type, 
  sync_status, 
  funds_type 
}
let isAjax = false
var data = {}
export const getMap = cb => {
  callback = map => {
    cb(map)
    callback = null
  }
  isAjax && callback(map)
}
ajax.postStream('/custom-web/api/customPayment/getDictionaryList',data,(res) =>{
  isAjax = true
  res = res.body
  if(res.result){
    for(var key in map) {
      res.content[key].forEach(item => {
        map[key].push({
          label: item.value,
          value: item.key
        })
      })
    }
  }
  callback && callback(map)
},function(res){
  console.log('失败的回调函数')
})



export default map
