
/*
 * @Author: your name
 * @Date: 2021-04-19 14:48:24
 * @LastEditTime: 2021-04-19 15:23:00
 * @LastEditors: Please set LastEditors
 * @Description: 工具函数
 * @FilePath: \front-dev\src\components\dz_customer\common\utils.js
 */
export function calcMlxs(retail_price, purchase_price) {
/**
 * @description: 计算毛利系数
 * @param {*} 商品零售价/商品采购价*0.52
 * @return {*}
 */    
    const pp = Number(purchase_price)
    const rp = Number(retail_price)
    if(isNaN(pp) || isNaN(rp) || pp === 0) {
        return 0
    }
    return (rp/pp*0.52).toFixed(3)
}