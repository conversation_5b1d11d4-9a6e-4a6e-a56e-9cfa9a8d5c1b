/***
 *规则验证js文件
 * by pmm
 * {ruquired} 是否为必填项，值为false或是不填都为非必填项
 * {msg} 验证不通过时显示的提示信息
 * {trigger} 触发验证的事件，默认是鼠标聚焦时触发，如果是一些非必填项请绑定change事件，以减少资源消耗
 * {_this.self} 在哪个对像里面要验证的对像
 * 只是先把相同的验证提取出一个组件而已，如果有其它的更好的方式，请与我联系，谢谢
 * */


/**
 *把数据转换成string类型的数据
 */
function toStr(value) {
  if (!arguments.length) return '';
  value = value === null ? '' : value + '';
  value = value.replace(/(^\s+)|(\s+$)/g, "");
  return value;
}
export default {
  employeeNumber(_this) { //工号的验证
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请输入大写字母或数字或大写字母与数字的组合' : '请输入大写字母或数字或大写字母与数字的组合或是不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              var reg = /^([A-Z]*|\d*|_*)([A-Z0-9]|_)+$/;
              value = toStr(value);
              var validateIsPass = value ? reg.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  realName(_this) { //真实姓名的验证
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请填写姓名' : '请填写姓名或是不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              value = toStr(value);
              var validateIsPass = value ? true : parseInt(value) == 0 ? true : false;
              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  type(_this) { //用户类型
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请选择用户类型' : '请选择用户类型或是不选择',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              value = toStr(value);
              var validateIsPass = value ? true : parseInt(value) == 0 ? true : false;
              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  }, 
  notLtCurrentTime(_this) { //日期不能小于当前时间
    return [{
        required: _this.required ? true : false,
        message: '请选择大于当前时间的日期',
        isShow: false,
        trigger: 'change',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return;
            }
            let currentTime = new Date().getTime()
            if((_this.required && (!value && value !== '0')) || new Date(value).getTime() < currentTime) {
                _this.self.rules[ rule.field][0].isShow = true
                callback(new Error(''));
                return
            } 
            _this.self.rules[ rule.field][0].isShow = false
            callback();
        }
    }]
  },
  email(_this) { //电子邮箱的验证
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请填写正确的邮箱格式' : '请填写正确的邮箱格式或是不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              var reg = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;

              value = toStr(value);

              var validateIsPass = value ? reg.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  enableTime(_this) { //生效日期
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : '生效日期要小于失效日期',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }

              var data = !_this.callback || _this.callback();
              var bool, msg;
              if ('boolean' == typeof data) {
                  bool = data;
              } else {
                  bool = data.bool;
                  msg = data.msg;
              }
              _this.self.rules[rule.field][0].isShow = !bool;
              if (msg) {
                  _this.self.rules[rule.field][0].message = msg;
              }
              // bool?callback():callback(new Error(''));
              if (bool) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  disableTime(_this) { //失效日期
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : '失效日期要大于生效日期',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              var data = !_this.callback || _this.callback();
              var bool, msg;
              if ('boolean' == typeof data) {
                  bool = data;
              } else {
                  bool = data.bool;
                  msg = data.msg;
              }
              _this.self.rules[rule.field][0].isShow = !bool;
              if (msg) {
                  _this.self.rules[rule.field][0].message = msg;
              }
              // bool?callback():callback(new Error(''));
              if (bool) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }

          }
      }]
  },
  
  mobile(_this) { //手机号
      return [{
          required: true,
          message: _this.msg ? _this.msg : _this.required ? '请填写有效的手机号' : '请填写有效的手机号或不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              //var reg = /^1[34578]\d{9}$/;
              //以1开头的11位数
              var reg = /^1\d{10}$/;
              var reg2 = /^\d{8}$/;
              var reg3 = /^[1]([3-9])[0-9]{9}$/;
              // value = toStr(value);
              var validateIsPass = value ? reg3.test(value) : _this.required ? false : true;
              //var validateIsPass = value ? reg.test(value) || reg2.test(value) : _this.required ? false : true;
              //var validateIsPass = value ? reg3.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  tel(_this) { //固话
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请填写有效的固定电话' : '请填写有效的固定电话或不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              var reg = /^\d+$/;
              value = toStr(value);
              var validateIsPass = value ? reg.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  otherTel(_this) { //其他电话
    return [{
        required: false,
        message: _this.msg ? _this.msg : _this.required ? '请填写有效的电话号' : '请填写有效的电话号或不填',
        isShow: false,
        trigger: _this.trigger ? _this.trigger : 'blur',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return;
            }
            var reg = /^0\d{2,3}\-{0,1}\d{7,8}$/;
            var regPhone = /^[1]([3-9])[0-9]{9}$/
            var validateIsPass = value ? reg.test(value) || regPhone.test(value) : _this.required ? false : true;
            _this.self.rules[rule.field][0].isShow = !validateIsPass;
            if (validateIsPass) {
                callback();
            } else {
                callback(new Error(''));
                _this.setActiveTab && _this.setActiveTab();
            }
        }
    }]
},
  fax(_this) { //传真
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请填写有效的传真' : '请填写有效的传真或不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              var reg = /^\d+$/;
              //var reg2 = /\s/;
              value = toStr(value);
              var validateIsPass = value ? reg.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },

  isNumber(_this) {
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请填写数字' : '请填写数字或不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              var reg = /^\d+$/;
              value = toStr(value);
              var validateIsPass = value ? reg.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  noBlank(_this) {
    return [{
        required: true,
        message: _this.msg ? _this.msg : '请选择省市区',
        isShow: false,
        trigger: _this.trigger ? _this.trigger : 'blur',
        type: 'Array',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return; 
            }
            if(Array.isArray(_this.self.address)){
                var validateIsPass = _this.self.address.length !== 0
            }

            _this.self.rules[rule.field][0].isShow = !validateIsPass;

            if (validateIsPass) {
                callback();
            } else {
                callback(new Error(''));
                _this.setActiveTab && _this.setActiveTab();
            }
        }
    }]
  },
  myPrice(_this) {
    return [{
        required: true,
        message: _this.msg ? _this.msg : '金额不能为空，只能小数点后两位数',
        isShow: false,
        trigger: _this.trigger ? _this.trigger : 'blur',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return;
            }
            var reg = /((^\-|[1-9]\d*)|^0)(\.\d{1,2}){0,1}$/
            if (value === undefined) {
                value = '';
            }
            value = toStr(value);
            var validateIsPass = value ? reg.test(value) : parseInt(value) == 0 ? true : false;

            _this.self.rules[rule.field][0].isShow = !validateIsPass;
            if (validateIsPass) {
                callback();
            } else {
                callback(new Error(''));
                _this.setActiveTab && _this.setActiveTab();
            }
        }
    }]
  },
  compact_sum(_this) {
    return [{
        required: _this.required === undefined ? true : _this.required,
        message: _this.msg ? _this.msg : '1.金额只能为正数且少于7位，2.最多两位小数',
        isShow: false,
        trigger: _this.trigger ? _this.trigger : 'blur',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return;
            }
            var reg = /((^[1-9]\d{0,6})|^0)(\.\d{1,2}){0,1}$/
            if (value === undefined) {
                value = '';
            }
            value = toStr(value);
            if(value === '' && _this.required === false) {
                _this.self.rules[rule.field][0].isShow = false;
                callback();
                return 
            }
            var isPrice =  value ? reg.test(value) : parseInt(value) == 0 ? true : false;
            let int_l = '0'
            if(isPrice) {
                // 整数14，小数3位
                int_l = value.split('.')[0].length
            }
            var validateIsPass = isPrice && int_l<15
            _this.self.rules[rule.field][0].isShow = !validateIsPass;
            if (validateIsPass) {
                callback();
            } else {
                callback(new Error(''));
                _this.setActiveTab && _this.setActiveTab();
            }
        }
    }]
  },
  isPrice(_this) {
    return [{
        required: _this.required === undefined ? true : _this.required,
        message: _this.msg ? _this.msg : '1.金额只能为正数且少于14位，2.最多两位小数',
        isShow: false,
        trigger: _this.trigger ? _this.trigger : 'blur',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return;
            }
            var reg = /((^[1-9]\d*)|^0)(\.\d{1,2}){0,1}$/
            if (value === undefined) {
                value = '';
            }
            value = toStr(value);
            if(value === '' && _this.required === false) {
                _this.self.rules[rule.field][0].isShow = false;
                callback();
                return 
            }
            var isPrice =  value ? reg.test(value) : parseInt(value) == 0 ? true : false;
            let int_l = '0'
            if(isPrice) {
                // 整数14，小数3位
                int_l = value.split('.')[0].length
            }
            var validateIsPass = isPrice && int_l<15
            _this.self.rules[rule.field][0].isShow = !validateIsPass;
            if (validateIsPass) {
                callback();
            } else {
                callback(new Error(''));
                _this.setActiveTab && _this.setActiveTab();
            }
        }
    }]
  },
  isPriceNotZero(_this) {
    /**
     * @description: 1.金额大于0且少于14位，2.最多两位小数
     * @param {*}
     * @return {*}
     */  
    return  [{
        required: _this.required === undefined ? true : _this.required,
        message: _this.msg ? _this.msg : '1.金额大于0且少于14位，2.最多两位小数',
        isShow: false,
        trigger: _this.trigger ? _this.trigger : 'blur',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return;
            }
            var reg = /((^[1-9]\d*)|^0)(\.\d{1,2}){0,1}$/
            if (value === undefined) {
                value = '';
            }
            value = String(value).trim();
            if(value === '' && _this.required === false) {
                _this.self.rules[rule.field][0].isShow = false;
                callback();
                return 
            }
            // 不能为0
            if(parseInt(value) === 0) {
              _this.self.rules[rule.field][0].isShow = true
              callback(new Error(''));
              return
            }
            var isPrice =  value ? reg.test(value) : parseInt(value) == 0 ? true : false;
            let int_l = '0'
            if(isPrice) {
                // 整数14，小数3位
                int_l = value.split('.')[0].length
            }
            var validateIsPass = isPrice && int_l<15
            _this.self.rules[rule.field][0].isShow = !validateIsPass;
            if (validateIsPass) {
                callback();
            } else {
                callback(new Error(''));
                _this.setActiveTab && _this.setActiveTab();
            }
        }
    }]
  },
  zhengshu(_this) {
    return [{
        required: true,
        message: _this.msg ? _this.msg : '值不能为空，且只能输入正数',
        isShow: false,
        trigger: _this.trigger ? _this.trigger : 'blur',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return;
            }
            var reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
            if (value === undefined) {
                value = '';
            }
            value = toStr(value);
            var validateIsPass = value ? reg.test(value) : parseInt(value) == 0 ? true : false;

            _this.self.rules[rule.field][0].isShow = !validateIsPass;
            if (validateIsPass) {
                callback();
            } else {
                callback(new Error(''));
                _this.setActiveTab && _this.setActiveTab();
            }
        }
    }]
  },
  clientName(_this) {
    return [{
        required: true,
        message: _this.msg ? _this.msg : '请填写相关内容',
        isShow: false,
        trigger: _this.trigger ? _this.trigger : 'blur',
        validator: function(rule, value, callback) {
            if (!_this.self) {
                console.log('this validate object is null');
                return;
            }
            var reg = /^[a-z\u4e00-\u9fa5]+$/i;
            if (value === undefined) {
                value = '';
            }
            value = toStr(value);
            var validateIsPass = value ? reg.test(value) : parseInt(value) == 0 ? true : false;

            _this.self.rules[rule.field][0].isShow = !validateIsPass;
            // validateIsPass?callback():callback(new Error(''));
            if (validateIsPass) {
                callback();
            } else {
                callback(new Error(''));
                _this.setActiveTab && _this.setActiveTab();
            }
        }
    }]
  },
  isNotBlank(_this) { //不为空
      return [{
          required: true,
          message: _this.msg ? _this.msg : '请填写相关内容',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (!_this.self) {
                  console.log('this validate object is null');
                  return;
              }
              /*if(typeof(value)==='number'){
                  value+='';
              }
              if(!value){
                  value=''
              }
              value+='';

              value = value.replace(/(^\s+)|(\s+$)/g,"");*/
              
              if (value === undefined) {
                  value = '';
              }
              value = toStr(value);
              var validateIsPass = value ? true : parseInt(value) == 0 ? true : false;

              _this.self.rules[rule.field][0].isShow = !validateIsPass;
              // validateIsPass?callback():callback(new Error(''));
              if (validateIsPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  code(_this) {
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请填写相关内容' : '请填写相关内容',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              if (value) {
                  if (/^[A-Z0-9_]+$/.test(value)) {
                      _this.self.rules[rule.field][0].isShow = false
                      callback();
                  } else {
                      _this.self.rules[rule.field][0].isShow = true
                      _this.self.rules[rule.field][0].message = '编码必须是大写字母、数字、_的组合格式'
                      callback(new Error(''));
                      _this.setActiveTab && _this.setActiveTab();
                  }
              } else {
                  _this.self.rules[rule.field][0].isShow = true
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }
          }
      }]
  },
  price(_this) { //保存2位小数的价格
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请输入价格' : '请输入价格或是不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              var reg = /^(([1-9][0-9]*)|(\d+\.\d{1,2}))$/;
              value = toStr(value);
              var isPass = value ? reg.test(value) : _this.required ? false : true;
              if (isPass && parseFloat(value) == 0) {
                  isPass = false;
                  _this.self.rules[rule.field][0].message = '请输入价格';
              }
              _this.self.rules[rule.field][0].isShow = !isPass;
              // isPass?callback():callback(new Error(''));
              if (isPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }


          }
      }]
  },
  priceTwo(_this) { //保存2位小数的价格（包括0）
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请输入价格' : '请输入价格或是不填',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              var reg = /^\d+(.\d{1,2})?$/;
              value = value === null ? '' : value.toString();
              var isPass = value ? reg.test(parseFloat(value)) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !isPass;
              // isPass?callback():callback(new Error(''));
              if (isPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }


          }
      }]
  },
  integerNotZero(_this) { //大于0的正整数
        return [{
            required: _this.required ? true : false,
            message: _this.msg ? _this.msg : _this.required ? '请输入大于0的正整数' : '不能为空',
            isShow: false,
            trigger: _this.trigger ? _this.trigger : 'blur',
            validator: function(rule, value, callback) {
                var reg = /^[0-9]+$/;
                value = value === null ? '' : value.toString();
                // 不能为0
                if(parseInt(value) === 0) {
                    _this.self.rules[rule.field][0].isShow = true
                    callback(new Error(''));
                    return
                }
                var isPass = value ? reg.test(value) : _this.required ? false : true;
                _this.self.rules[rule.field][0].isShow = !isPass;
                // isPass?callback():callback(new Error(''));
                if (isPass) {
                    callback();
                } else {
                    callback(new Error(''));
                    _this.setActiveTab && _this.setActiveTab();
                }


            }
        }]
  },
  integer(_this) { //0到100的整数
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请输入0到100的整数' : '请输入0到100的整数或为空',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              var reg = /^(?:0|[1-9][0-9]?|100)$/;
              value = value === null ? '' : value.toString();
              var isPass = value ? reg.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !isPass;
              // isPass?callback():callback(new Error(''));
              if (isPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }


          }
      }]
  },
  integer1(_this) { //大于等于0的整数
      return [{
          required: _this.required ? true : false,
          message: _this.msg ? _this.msg : _this.required ? '请输入大于等于0的整数' : '请输入大于等于0的整数或为空',
          isShow: false,
          trigger: _this.trigger ? _this.trigger : 'blur',
          validator: function(rule, value, callback) {
              var reg = /^[1-9]\d*$/;
              value = value === null ? '' : value.toString();
              var isPass = value ? (reg.test(value) || (parseInt(value) == 0)) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !isPass;
              // isPass?callback():callback(new Error(''));
              if (isPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }


          }
      }]
  },

  /****
   * 用户与人员 生效与失效时间的专用验证
   * 生效与失效时间都不是必填项
   * 如果不填生效时间，那生效时间就是无线小，如果不填失效时间，那失效时间就是无效大
   * {_this} self,整个组件对像
   * {_this} compareData要比较的数据要放在哪个对像里面
   *{_this} validateWhitch ,要验证的失效时间字段
   * **/

  userOrPersonEnableTime(_this) {
      return [{
          required: false,
          message: '生效时间要小于失效时间或是不填',
          isShow: false,
          trigger: 'change',
          validator: function(rule, value, callback) {
              _this.self.rules[rule.field][0].isShow = false;
              var key = _this.validateWhitch ? _this.validateWhitch : 'disableTime';
              _this.self.$refs[_this.compareData].validateField(key);
              callback();
          }
      }]
  },
  userOrPersonDisableTime(_this) {
      return [{
          required: false,
          message: '失效时间不能小于生效时间或是不填',
          isShow: false,
          trigger: 'change',
          validator: function(rule, value, callback) {
              var compareData = _this.self[_this.compareData];
              var enableTime = compareData.enableTime,
                  disableTime = compareData.disableTime;

              var isPass = (!enableTime || !disableTime) ? true : false;
              if (!isPass) { //如果是失效
                  isPass = new Date(enableTime).getTime() <= new Date(disableTime).getTime();
              }
              _this.self.rules[rule.field][0].isShow = !isPass;
              isPass ? callback() : callback(new Error(''));
          }
      }]
  },
  /**
  *最多只能保存3位不小于0的数
  **/
  maxThreeDecimals(_this){
      return [{
          required: false,
          message: _this.msg ? _this.msg : _this.required ? '至多只能存三位小数' : '至多只能三位小数或为空',
          isShow: false,
          trigger: 'change',
          validator: function(rule, value, callback) {
              console.log('change');
              var reg = /^[0-9]*([.][0-9]{1,3})?$/
              value = value === null ? '' : value.toString();
              var isPass = value ? reg.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !isPass;

              if (isPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }

          }
      }]
  },
  /**
  *最多只能保存2位不小于0的数
  **/
  maxTwoDecimals(_this){
      return [{
          required: false,
          message: _this.msg ? _this.msg : _this.required ? '至多只能存二位小数' : '至多只能二位小数或为空',
          isShow: false,
          trigger: 'change',
          validator: function(rule, value, callback) {
              console.log('change');
              var reg = /^[0-9]*([.][0-9]{1,2})?$/
              value = value === null ? '' : value.toString();
              var isPass = value ? reg.test(value) : _this.required ? false : true;
              _this.self.rules[rule.field][0].isShow = !isPass;

              if (isPass) {
                  callback();
              } else {
                  callback(new Error(''));
                  _this.setActiveTab && _this.setActiveTab();
              }

          }
      }]
  },
  ratio(message) {
    const validateRule = {
      required: true,
      trigger: 'change',
      isShow: false,
      message: message
    };
    validateRule.validator = function(rule, value, callback) {
      if(!/^0$|^0\.\d{1,2}$|^100$|^100\.0{1,2}|^[1-9]\d?$|^[1-9]\d?\.\d{1,2}$/.test(value + '')){
        validateRule.isShow = true;
        callback(new Error(''));
      }else {
        validateRule.isShow = false;
        callback();
      }
    }
    return [validateRule];
  }

}
