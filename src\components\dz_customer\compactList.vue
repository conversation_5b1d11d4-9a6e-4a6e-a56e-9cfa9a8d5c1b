<!--
 * @Author: your name
 * @Date: 2021-03-17 13:46:59
 * @LastEditTime: 2021-03-30 09:00:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \front-dev\src\components\dz_customer\compactList.vue
-->
<template>
<!-- 合同列表 -->
  <div style="height: 99%;">
    <commonList
    ref="list"
    :params = params
    :tools="tools"
    url="/custom-web/api/customSysTrade/getWaitingCompactSysTrade"
    :defaultValue="defaultValue"
    ></commonList>
  </div>
</template>

<script>
import commonList from './commonList2'
import btnStatus from './common/mixins/btnStatus'
import { getRole, getCompactInfo,getShopLBInfo } from './common/api'
import {addContract, tips } from './alert/alert'
import { client_status  } from './common/clientDictionary'
export default {
  components: {
    commonList
  },
  mixins: [
    btnStatus
  ],
  props: ['params'],
  data() {
    return {
      defaultValue: {
        client_status: ['WAITING_SIGN_CONTRACT','IN_SIGN_CONTRACT'],
      },
      tools: []
    }
  },
  methods:{
    refresh() {
      this.$refs.list._getDataList()
    },
  },
  async mounted() {
    this.role = await getRole()
    if(this.role.indexOf('DZ_SJS') != -1||this.role.indexOf('DZ_DG') != -1||
      this.role.indexOf('DZ_DZ') != -1||this.role.indexOf('DZ_SJZG') != -1
    ) {
      this.$refs.list.getInfo()
    } else {
      this.$refs.list.getInfoNoShop()
    }
    let self = this
    this.tools = [
      {
        type: 'primary',
        txt: '合同登记',
        click: async (d) => {
          let data = {
            client_number:d.client_number, 
            client_name: d.client_name, 
            client_mobile: d.client_mobile, 
            designer_number: d.designer_number, 
            designer_name: d.designer_number,
            compact_number:d.client_number+(new Date().getMonth()+1)+new Date().getDate(),
            compact_date:new Date(),
          }
          // 获取店铺拎包信息
          let if_bag_show=false
          await getShopLBInfo({"shopCode":d.shop_code,"uncheck" : true}).then(res=>{
              if_bag_show=res.data==1?true:false
          })
          const res = await getCompactInfo(data.client_number)

            let value = res.result ? res.content || {} : {}
            value.is_urgent !== undefined &&  (data.is_urgent = value.is_urgent )

            // 下推采购之前可以允许修改合同是否加急，其余禁用
            let currentClientStatusCodeIndex =  client_status.findIndex(item => item.value === d.client_status)
            let currentClientStatusCode = currentClientStatusCodeIndex !== -1 ? Number(client_status[currentClientStatusCodeIndex].v) : ''
            let canOnlyUrgent = currentClientStatusCode  ? currentClientStatusCode > 71 && currentClientStatusCode <= 180   : false
            // 是否拎包初始化
            let if_bag="Y"
            if_bag_show?addContract(this, Object.assign(value, data, { canOnlyUrgent,if_bag_show,if_bag })):addContract(this, Object.assign(value, data, { canOnlyUrgent,if_bag_show }));          
            // addContract(this,Object.assign(value, data, { canOnlyUrgent }))
        },
        show(d) {
          return self.isContract({client_status: d.client_status_value, designer_number: d.designer_number}, self.role, client_status)
        }
      },
    ].filter(item => item.show)
    this.$root.eventHandle.$on('refreshcompactList', this.refresh)
  },
  beforeDestroy() {
    this.$root.eventHandle.$off('refreshcompactList', this.refresh)
  }

}
</script>

<style>

</style>
