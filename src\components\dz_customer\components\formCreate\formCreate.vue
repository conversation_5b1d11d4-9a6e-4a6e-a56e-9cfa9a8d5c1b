<template>
<!-- 表单组件 -->
    <div class="form-container" :style="{overflow: height==='auto' ? '' : 'auto', height}">
        <el-form 
            ref="form" 
            :label-width="labelWidth"
            :model="form" 
            :rules="rules"
            :key="key"
            >
            <layout :rows="form.dynamic" >
                <template slot="row" slot-scope="scope">
                    <div class="row-title" v-if="scope.row.title">
                        {{scope.row.title}}
                    </div>
                </template>
                <template slot-scope="scope">
                    <el-form-item
                        :label="scope.col.label+'：'" 
                        :prop="scope.col.dyProp" 
                        :label-width="scope.col.labelWidth || ''"
                        :class="[scope.col.layout]"
                        v-if="!scope.col.hide"
                    >
                        <span class="suffix">{{scope.col.prefix}}</span>
                        <component 
                            size="mini"
                            :ref="scope.col.prop"
                            v-model.trim="scope.col.value"
                            :is="scope.col.formType?.default || scope.col.formType"
                            :type="scope.col.type||'text'"
                            :placeholder="scope.col.placeholder"
                            :tags="scope.col.tags"
                            :disabled="scope.col.disabled"
                            :options="scope.col.options"
                            :config="scope.col.config"
                            :maxlength="scope.col.maxlength"
                            :readonly="scope.col.readonly"
                            :filterable="scope.col.filterable"
                            :icon="scope.col.icon"
                            :on-icon-click="function() {onIconClick(scope.col.value, scope.col)}"
                            @change="event($event, scope.col, 'change')"
                            @blur="event($event, scope.col, 'blur')"
                            @focus="event($event, scope.col, 'focus')"
                            @result="(result, queryParam) => { event(result, scope.col, 'result', queryParam) }"
                            @input="event($event, scope.col, 'input')"
                            @click="event($event, scope.col, 'click')"
                        >
                            <template v-if="scope.col.formType === 'elSelect'">
                                <el-option 
                                    v-for="(option, optionIndex) in scope.col.options"
                                    :key="optionIndex"
                                    :label="option.label" 
                                    :disabled="option.disabeld"
                                    :value="option.value"></el-option>
                            </template>
                            <template v-if="scope.col.formType === 'elRadioGroup'">
                                <el-radio 
                                    v-for="item in scope.col.options"
                                    :key="item.value"
                                    :label="item.value"
                                    :disabled="scope.col.disabled"
                                >{{item.label}}</el-radio>
                                <el-tooltip 
                                    v-if="rules[scope.col.dyProp] && rules[scope.col.dyProp][0].isShow"
                                    class="item" 
                                    effect="dark" 
                                    :content="rules[scope.col.dyProp][0].message" 
                                    placement="right-start" 
                                    popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </template>
                            <template v-if="scope.col.formType === 'elCheckboxGroup'">
                                <el-checkbox 
                                    v-for="item in scope.col.options"
                                    :key="item.value"
                                    :label="item.value"
                                    :disabled="scope.col.disabled"
                                >{{item.label}}</el-checkbox>
                                <el-tooltip 
                                    v-if="rules[scope.col.dyProp] && rules[scope.col.dyProp][0].isShow"
                                    class="item" 
                                    effect="dark" 
                                    :content="rules[scope.col.dyProp][0].message" 
                                    placement="right-start" 
                                    popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </template>
                            <template slot="rules" v-if="scope.col.formType === 'uploadFile'">
                                <el-tooltip 
                                    v-if="rules[scope.col.dyProp] && rules[scope.col.dyProp][0].isShow"
                                    class="item" 
                                    effect="dark" 
                                    :content="rules[scope.col.dyProp][0].message" 
                                    placement="right-start" 
                                    popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                            </template>

                        </component>
                        <span class="suffix">{{scope.col.suffix}}</span>
                        <!-- tips -->
                        <div class="tips" v-if="scope.col.tips">*{{scope.col.tips}}</div>
                        <div class="remark-tips" v-if="scope.col.remarkTips">*{{scope.col.remarkTips}}</div>
                        
                        <el-tooltip 
                            v-if="scope.col.tooltip !== false && rules[scope.col.dyProp] && rules[scope.col.dyProp][0].isShow"
                            class="item" 
                            :class="[scope.col.type]"
                            effect="dark" 
                            :content="rules[scope.col.dyProp][0].message" 
                            placement="right-start" 
                            popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
                    </el-form-item>
                </template>
                <template slot="btns" v-if="inline">
                    <div class="form-btns-line" >
                        <el-button type="primary" size="small" @click="save">{{savetitle}}</el-button>
                        <el-button type="danger" size="small" @click="resetFields">重置</el-button>
                    </div>
                </template>
            </layout>
        </el-form>
        <div class="form-btns" v-if="btns">
            <el-button type="primary" size="small" @click="save">{{savetitle}}</el-button>
            <el-button type="danger" size="small" @click="resetFields">重置</el-button>
        </div>
        <slot name="footer"></slot>
    </div>
</template>
<script>
import { cloneDeep, debounce  } from 'lodash'
import fn from '@/common/Fn.js'
// import validate from '@/common/validate.js'
import myValidate from '../../common/validate'
import layout from '../layout/layout'
export default {
    components: {
        layout,
        measureSpace: () => import('../measureSpace/measureSpace'),
        selectRange: () => import('../selectRange/selectRange'),
        selectAddress: () => import('../selectAddress/selectAddress'),
        myText: () => import('../myText'),
        myInput: () => import('../input/src/input'),
        uploadFile: () => import('../uploadFile'),
        listSelect: () => import('../list-select'),
        selectUser: () => import('../user'),
        inputNumber: () => import('../inputNumber'),
        rejectType: () => import("../rejectType"),
    },
    data() {
        return {
            form:{
                dynamic:[]
            },
            isMounted: false,
            rules: {},
            key:1
        }
    },
    props: {
        formData:{
            type: Array
        },
        ruleData: {
            type: Object
        },
        btns: {
            type: Boolean,
            default: true
        },
        labelWidth: {
            type: String, 
            default: '100px'
        },
        height: {
            type: String,
            default: 'auto'
        },
        requestUrl: {
            type: String
        },
        initParam: {
            type: Object,
            defautl() {
                return {}
            }
        },
        isDefault: {
            type: Boolean,
            default: false
        },
        inline: {
            type: Boolean,
            default: false
        },
        savetitle: {
            type: String,
            default: "保存"
        },
        paramFilter: {
            type: Function
        }
    },
    watch:{
        formData: {
            deep: true,
            immediate: true,
            handler(v){
                v.length && this.initForm()
            }
        },
        // ruleData: {
        //     deep: true,
        //     immediate: true,
        //     handler(newVal){
        //         this.dealRule(newVal)
        //     }
        // }
    },
    mounted(){
        this.$emit('ready', this.setFormData, this.getItem)
        
    },
    methods: {
        updateFile(isInit) {
        /**
         * @description: 更新文件组件
         * @param {*}
         * @return {*}
         */    
            this.form.dynamic.forEach(row => {
                row.cols.forEach(col => {
                    if(col.formType === 'uploadFile') {
                        this.$refs[col.prop].getFileList(isInit)
                    }
                })
            })
        },
        onIconClick(e, col) {
            if(col.event && col.event.onIconClick) {
                col.event.onIconClick(e, col, this.model(), this.getItem)
            }
        },
        setFormData(formData, value) {
            formData.forEach(row => {
                row.cols.forEach(col => {
                   value[col.prop] !== undefined && (col.value = this.setValueFormat(value[col.prop], col))
                })
            })
            return formData
        },
        event(e, col, type, other) {
            this.validRate(col)
            if(col.event && col.event[type]) {
                col.event[type](e, col, this.model(), this.getItem, other)
            }
        },
        getItem(prop) {
            let props = Array.isArray(prop) ? prop : [prop]
            let items = []
            props.forEach(item => {
                this.form.dynamic.forEach(row => {
                    row.cols.forEach(col => {
                        if(col.prop == item) {
                            items.push(col)
                        }
                    })
                })
            })
            return Array.isArray(prop) ? items : items[0]
        },
        setValueFormat(val, col) {
            if(col.formType === 'elCheckboxGroup') {
                return String(val).split(',')
            }
            return val
        },
        initForm() {
            let newFormData = cloneDeep(this.formData)
            this.propMap = {}
            this._propMap = {}
            this.form.dynamic = []
            let initValueObj = {
                measureSpace: [],
                elCheckboxGroup: [],
                elRate: null,
                selectRange: ['','']
            }
            let initValue
            newFormData.forEach((row, rowIndex) => {
                if(Array.isArray(row.cols)) {
                    row.cols.forEach((col, colIndex) => {
                        col.tooltip = !(col.formType === 'elCheckboxGroup' || col.formType === 'elRadioGroup' || col.formType === 'uploadFile')
                        if(col.prop) {
                            initValue = initValueObj[col.formType] === undefined ?  ''  : JSON.parse(JSON.stringify(initValueObj[col.formType]))
                            col.value = this.getInitValue(col, initValue)
                            col.dyProp = `dynamic.${rowIndex}.cols.${colIndex}.value`
                            if(!col.placeholder) {
                                // col.placeholder = col.formType === 'elInput' || col.formType === 'myInput' ? '请输入' + col.label : '请选择' + col.label
                                col.disabled && (col.placeholder = '')
                            }
                            if(col.prop) {
                                this.propMap[col.prop] = col.dyProp
                                this._propMap[col.dyProp] = col.prop
                            }
                            
                        }
                    })
                }  
                this.form.dynamic.push(row)
            })
            if(!this.debounceRule) {
                this.debounceRule  = debounce(this.dealRule, 500)
            }
            this.debounceRule()
            // this.dealRule()
            if(!this.isInited && this.form.dynamic.length) {
                this.$emit('inited', this.getItem)
                this.isInited = true    
            }
            
        },
        getInitValue(col, initValue){
            let val = col.value === undefined ? initValue !== undefined ? initValue : '' : col.value
            if(this.isDefault) {
                if(val == '' || val === undefined) {
                    if(col.formType === 'elDatePicker') {
                        return new Date()
                    }
                }
            }
            return val
        },
        setValue(rowData) {
            this.form.dynamic.forEach(row => {
                if(Array.isArray(row.cols)) {
                    row.cols.forEach(col => {
                        rowData[col.prop] !== undefined && (col.value = this.setValueFormat(rowData[col.prop], col))
                    })
                }
            })
        },
        model(type) {
            let formData = {}
            this.form.dynamic.forEach( row => {
                if(Array.isArray(row.cols)) {
                    row.cols.forEach(col => {
                        this.valueFormat(formData, col.value, col, type) 
                    })
                }
            })
            return formData
        },
        dealRule(refresh){
            let rules = this.ruleData
            let ruleData = {}
            for(var key in rules) {
                if(!this.propMap[key]) return
                rules[key].self = this
                rules[key].isShow === undefined && (rules[key].isShow = false)
                let rule
                if(!rules[key].validate) {
                    rule = this.validator(rules[key])
                } else if(typeof rules[key].validate === 'function') {
                    rule = rules[key].validate(rules[key])
                } else {
                    let v = myValidate[rules[key].validate]
                    
                    rule = v(rules[key])
                }
                this.$set(ruleData, this.propMap[key], rule)
            }
            if(!this.first || refresh) {
                this.first = true
                this.rules = ruleData
                this.key++
            }
        },
        validator(_this) {
            let required = _this.required ? true : false
            return [{
                required: required,
                message: _this.message ? _this.message : required ? '不能为空' : '',
                isShow: false,
                trigger: _this.trigger ? _this.trigger : 'blur',
                validator: (rule, value, callback) => {
                    let formData = this.model(false)
                    value = formData[this._propMap[rule.field]]
                    _this.self.rules[rule.field][0].isShow = required && this.isNull(value);
                    if (_this.self.rules[rule.field][0].isShow) {
                       callback(new Error(''));
                    } else {
                        callback();
                    }
                }
            }]
        },
        isNull(val) {
            return val === '' || val === undefined || val === null || (Array.isArray(val) && !val.length)
        },
        valueFormat(formData, value, col, type){
            if(!col.prop ) return
            if(col.formType === 'elDatePicker') {
                // 日期组件
                let format = col.format || 'yyyy-MM-dd hh:mm:ss'
                if(col.type === 'datetimerange' || col.type === 'daterange') {
                    formData[col.props[0]] = format === 'timestamp' ? value[0] : fn.dateFormat(value[0], format)
                    formData[col.props[1]] = format === 'timestamp' ? value[1] : fn.dateFormat(value[1], format)
                    delete formData[col.prop]
                    return
                } else {
                    formData[col.prop] = format === 'timestamp' ? value : fn.dateFormat(value, format) 
                    return
                }
                
            }
            if(col.formType === 'selectRange') {
                formData[col.props[0]] = value[0]
                formData[col.props[1]] = value[1]
                delete formData[col.prop]
                return
            }
            if(col.formType === 'elCheckboxGroup') {
                formData[col.prop] =  Array.isArray(value) ? value.join(',') : ''
                return
            }
            formData[col.prop] =  type === false ? value : col.filter ? col.filter(value) : value
        },
        validRate(col) {
            if(!this.valided) {return true}
            let isValid = true
            if(col.formType === 'elRate') {
                if(this.ruleData[col.prop]) {
                    let formData = this.model(false)
                    let value = formData[col.prop]
                    // 验证
                    
                    let rules = Array.isArray(this.ruleData[col.prop]) ? this.ruleData[col.prop] : [this.ruleData[col.prop]]
                    rules.forEach(item => {
                        if(item.required) {
                            // 验证必填
                            isValid = value && value != 0
                        }
                    })
                    this.rules[this.propMap[col.prop]][0].isShow = !isValid
                }
            }
            return Boolean(isValid)
        },
        save(cb) {
            this.$refs.form.validate(valid => {
                let rateValid = true
                this.valided = true
                let uploadFileProps = []
                this.form.dynamic.forEach( row => {
                    if(Array.isArray(row.cols)) {
                        row.cols.forEach(col => {
                            if(col.formType == 'elRate') {
                                if(!this.validRate(col) && rateValid) {
                                    rateValid = false
                                }
                            }
                            if(col.formType === 'uploadFile') {
                                uploadFileProps.push(col.prop)
                            }
                            
                        })
                    }
                })
                if(valid && rateValid) {
                    let data = this.model()
                    for(var key in data) {
                        if(uploadFileProps.includes(key)) {
                            delete data[key]
                        }
                    }
                    typeof cb === 'function' ? cb(data) : this.$emit('save', data)
                    // 接口保存
                    if(this.requestUrl) {
                        this.request(data)
                    }
                }
            })
        },
       
        request(param){
            let _this = this
            this.paramFilter && (param = this.paramFilter(param))
            if(this.isRequest) {
                this.$message({
                    type: 'warning',
                    message: '请等待上一次请求结束'
                })
                return
            }
            this.isRequest = true
            let p = Object.assign({}, param, this.initParam)
            this.ajax.postStream(this.requestUrl,p,(data) =>{
                this.isRequest = false
                data = data.body
                _this.$message({
                    message: data.msg,
                    type:data.result?'success':'error'
                })
                if(data.result){
                    // 保存成功
                    this.$refs.form.resetFields()
                    this.$emit('request', data, p)
                }
            },function(data){
                _this.isRequest = false
            })
        },
        resetFields(){
            this.$refs.form.resetFields()
        },
    }
}
</script>
<style scoped>
    .row-title {
        padding-left: 10px;
        border-left: 4px solid #409EFF;
    }
    .form-container {
        padding: 10px;
        box-sizing: border-box;
        width: 100%;
    }
    .form-btns {
        text-align: center;
        margin-top: 10px;
    }
    .form-btns-line {
        display:inline-block;
        margin-left: 10px;
    }
    .el-select, .el-input {
        max-width: 100%;
    }
    .el-checkbox-group {
        display: inline-block;
    }
    .vertical .el-radio+.el-radio {
        margin-left:0;
    }
    .el-textarea {
        padding-right: 20px;
        padding-bottom: 5px;

    }
    .el-tooltip.textarea {
        position: absolute;
        padding-left: 4px;
        bottom: 6px;
        right: 3px;
    }
    .tips {
        display:inline-block;
        line-height: initial;
    }
    .remark-tips {
        display:block;
        line-height: initial;
        color: red;
        font-weight: 800;
    }
</style>
<style>
.form-container  .el-form-item .el-form-item__label, .el-form-item .el-form-item__content {
        height: auto !important;
    }
.form-container .el-table .el-table__body-wrapper td .cell, .el-table .el-table__fixed-body-wrapper td .cell {
    height: auto;
    line-height: 1;
}
.form-container .el-rate {
    height: 28px;
    display: inline-block;
    transform: translateY(-2px);
}
.form-container .el-rate .el-rate__item{
    line-height: 28px;
}
</style>
