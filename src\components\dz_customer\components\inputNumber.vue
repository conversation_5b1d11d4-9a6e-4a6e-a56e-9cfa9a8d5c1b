<!--
 * @Author: your name
 * @Date: 2021-04-12 11:35:25
 * @LastEditTime: 2021-04-12 13:46:36
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \front-dev\src\components\dz_customer\components\inputNumber.vue
-->
<template>
    <!-- <el-input
        :value='value'
        onkeyup="input"
        maxLength='4'
        /> -->
        <div class="el-input el-input--mini">
            <input
                oninput="value=value.replace(/[^\d]/g,'')"
                @input="change" :value="value" 
                 size="mini"  :maxlength="maxlength"  class="el-input__inner">
        </div>
    <!-- <input @change="change" :value="value" type="text" placeholder="请输入您的手机号" oninput="value=value.replace(/[^\d]/g,'')" maxlength="4"> -->
</template>
<script>
export default {
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        maxlength: {
            type: [String, Number]
        },
        value: {
            type: [String, Number]
        }
    },
    methods: {
        change(e) {
            this.$emit('change', e.target.value)
        }
    }
}
</script>
