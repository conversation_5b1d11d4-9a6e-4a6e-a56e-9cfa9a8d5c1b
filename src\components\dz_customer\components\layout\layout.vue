<template>
<!-- 详情-布局组件 -->
    <div>
        <el-row
            v-for="(row, rowIndex) in rows"
            :key="rowIndex"
            :style="{maxHeight: row.height || 'none', overflow: row.height ? 'auto' : 'initial'}"
        >
            <slot name="row" :row="row" :$rowIndex="rowIndex" ></slot>
            <el-col
                v-for="(col, colIndex) in row.cols"
                :key="col.key ? col.key : colIndex"
                :span="col.span || 6"
            >
            <slot :col="col" :$rowIndex="rowIndex" :$colIndex="colIndex"></slot>
            </el-col>
            <slot name="btns"></slot>
        </el-row>
    </div>
</template>
<script>
export default {
    props: {
        
        rows: {
            type: Array
        }
    }
}
</script>
