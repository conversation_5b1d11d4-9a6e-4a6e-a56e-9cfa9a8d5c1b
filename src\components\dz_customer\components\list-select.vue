<template>
<!-- 表单组件-列表选择 -->
    <div @click="() => { popup('whole') }" style="display:inline-block;">
        <el-input  readonly icon="search" size="mini" :disabled="disabled" :value="labelText" :placeholder="placeholder" :on-icon-click="() => { popup('icon') }"	></el-input>
    </div>
</template>
<script>
import {popup} from '../alert/alert'
export default {
    model: {
        prop: 'value',
        event: 'change'
    },
    data() {
        return {
            isSelect: false,
            label: '',
            configs: Object.freeze({
                customer: {
                    label: 'client_name',
                    value: 'client_no'
                },
                order: {
                    label: 'client_number',
                    value: 'client_number'
                },
                merge_order:{
                    label: 'merge_trade_no',
                    value: 'merge_trade_no'
                },
                shop: {
                    label: 'shop_name',
                    value: 'shop_code'
                },
                relation_order: {
                    label: 'client_number',
                    value: 'client_number'
                },
                goods: {
                    label: 'message',
                    value: 'goods_id'
                },
                transforPeople: {
                    label: 'message',
                    value: 'goods_id'
                },
                urgentFreeList: {
                    label: 'supplierName',
                    value: 'supplier_id'
                },
                client_address: {
                    label: (row) => { return  row.prin_city_district+row.receiver_address},
                    value: 'address_id',
                    url: '/custom-web/api/customClient/getReceiverInfoList',
                    list_prop: 'receiverAddressList'
                }
            })
        }
    },
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        },
        value: {
            type: [String, Number]
        },
        disabled: {
            type: Boolean,
            default: false
        },
        config: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    computed: {
        popupType() {
            return this.config.popupType || ''
        },
        multiple() {
            return this.config.multiple || false
        },
        initParam() {
            return this.config.initParam || {}
        },
        labelText() {
            let configLable = this.config.label || ''
            return this.value ? this.isSelect ? this.label : configLable : ''
        }
    },
    created() {
        this.initLabel()
    },
    methods: {
        initLabel() {
            // 只适用于没有分页的接口
            if(this.config.label || !this.configs[this.popupType].url || !this.value ) return
            this.ajax.postStream(this.configs[this.popupType].url, this.initParam, (d) => {
                if (d.body.result) {
                    // 数据获取成功
                    let list_prop = this.configs[this.popupType].list_prop
                    let content = d.body.content || {}
                    let tableDatas = Array.isArray(content) ? content : content[list_prop] || []
                    let currentIndex = tableDatas.findIndex(item => item[this.configs[this.popupType].value] === this.value)
                    if(currentIndex !== -1) {
                        this.isSelect = true
                        this.label = tableDatas[currentIndex][this.configs[this.popupType].label]
                    }
                } 
            
            })
        },
        popup(type) {
            // type whole是点击整个, icon点击图标
            if(this.config.clickWay === 'whole' && type === 'icon' || this.config.clickWay !== 'whole' && type === 'whole') {
                return
            }
            if(this.disabled) return
            // 打开选择弹窗
            if(this.config.valid) {
                if(!this.config.valid(this.config, this.initParam)) return
            }
            popup[this.popupType](this, async (result, queryParam) => {
                // 无选择结果时
                if(!result || Array.isArray(result) && !result.length || !Object.keys(result).length) {
                    this.$emit('result', {}, queryParam)
                    return
                }

                // 选择验证
                if(this.config.resultValid ) {
                    let res = await this.config.resultValid(result)
                    if(!res) return
                }
                this.isSelect = true
                let popupLabel = this.config.showLabel || this.configs[this.popupType].label
                if(this.multiple) {
                    if(popupLabel) {
                        this.label = result.map(item => {
                            return typeof popupLabel === 'string' ?  item[popupLabel] : popupLabel(item)
                        }).join(',')
                    }
                    this.$emit('change', result.map(item => {
                        return item[this.configs[this.popupType].value || 'id']
                    }).join(','))
                } else {
                    
                    if(popupLabel) {
                        this.label = typeof popupLabel === 'string' ?  result[popupLabel] : popupLabel(result)
                    }
                    this.$emit('change', result[this.configs[this.popupType].value || 'id'])
                }
                this.$emit('result', result, queryParam)
            }, Object.assign({},this.config, {multiple: this.multiple, initParam: this.initParam}))
        }
    }
}
</script>
