<template>
    <div>
        <el-dialog  size="large" title="图片管理" :visible.sync="dialogTableVisible" @close="gridData = []">
            <div class="btns">
                <!-- <el-upload
                    action="test"
                    :http-request="uploadImage"
                    :show-file-list="false"
                    :multiple="true"
                    >
                    <el-button size="small" type="primary" >点击上传</el-button>
                </el-upload> -->
                <el-button type='success' size='mini' @click='upload' >上传附件<xpt-upload-v2 isOnlyPic :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload-v2></el-button>
            </div>
            <el-table :data="gridData">
                <el-table-column
                type="index"
                width="50">
                </el-table-column>
                <el-table-column property="fileName" label="文件名"></el-table-column>
                <el-table-column property="fileSize" label="文件大小" width="200"></el-table-column>
                <el-table-column property="person" label="上传人"></el-table-column>
                <el-table-column property="updateData" label="上传时间"></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button
                        size="mini"
                        type="danger"
                        @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">取 消</el-button>
            </span>
        </el-dialog>
       
    </div>
</template>
<script>
export default {
    data() {
        return {
            gridData: [],
            dialogTableVisible: false,
            ifClickUpload:false,
            uploadData:{}
        }
    },
    methods: {
        upload() {
			this.ifClickUpload = true
	      	this.uploadData = {
		        parent_name: 'RETURNVISIT',
		        parent_no: 'HFD2004095441',
		        child_name: 'RETURNVISIT',
		        child_no: '',
		        content: {'style':'回访单附件'}
	      	}
	      	setTimeout(() => {
	        	this.ifClickUpload = false
            },100)
            this.gridData.push({
                fileName: 'test',
                fileSize: '4356KB',
                person: '谢永林',
                updateData: '2020/04/17',
                url: 'https://dss3.baidu.com/-rVXeDTa2gU2pMbgoY3K/it/u=1189779147,4248115718&fm=202&mola=new&crop=v1'
            })  
            this.change()
		},
        handleDelete(index){
            this.$confirm('删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.gridData.splice(index, 1)
                this.change()
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            })
            
        },
        // 获取上传图片的key值
        validateKey(resolve){
            this.ajax.postStream('/app-web/app/file/validateKey.do', {type:"GET"} ,(res)=>{
                console.log(res);
                if(res.body.success){
                    resolve && resolve(res.body.data)
                }else{
                    this.$message.error('获取文件路径失败，请重新操作');
                }
            })
        },
        getHost() {
            //获取域名
            return new Promise((resolve, rejct) => {
                if(this.host) {
                    resolve(this.host)
                    return
                } 
                this.ajax.postStream('/file-iweb/api/cloud/fileConfig/get',{},res => {
                    console.log('res',res);
                    if(res.body.result){
                        this.host = res.body.content.host;
                        resolve(this.host)
                    }else {
                        this.$message.error(res.body.msg);
                    }
                })
            })
        },
        async uploadImage(fileObj){
            console.log(files, arguments)
            let file = fileObj.file
            let host = await this.getHost()
            console.log(host)
            return
            new Promise((resolve,reject)=>{
                this.validateKey(resolve);
            }).then((key)=>{
                let formData = new FormData();
                formData.append('file', file);
                let host = this.host.split('//')[1];
                //上传到附件库
                let url = 'http://**************:12010:12030/upload-web/app/file/uploadFile/'+host+'/'+key+'.do';
                this.ajax.post(url,formData, (s) => {
                    var data ={
                        file_type: file.name.split('.').pop(),// 文件类型 string
                        name: file.name,// 名字 string
                        size: file.size
                    }
                    if(s.ok && s.body && s.body.content){
                        data.path = s.body.content//  地址 string
                    }
                    if(s.ok && s.body && s.body.data){
                        data.path = s.body.data//  地址 string
                    }
                    //上传到接口，用于查看时获取
                    this.ajax.postStream('/file-iweb/api/cloud/file/upload',data,res => {
                        if(res.body.result){
                            this.$message.success('上传成功');
                        }else {
                            this.$message.error(res.body.msg);
                        }
                    })
                }, (e) => {
                    this.$message({
                        message: files[index].name+'上传失败',
                        type: 'error'
                    })
                })
            })
           this.change()
        },
        change(){
             this.$emit('change', this.gridData)
        },
        show(imgs) {
            imgs && (this.gridData = imgs)
            this.dialogTableVisible = true
        },
        close() {
            this.dialogTableVisible = false
            
        }
    }
}
</script>
<style scoped>
.btns {
    text-align: left;
    margin-bottom: 10px;
}
</style>
