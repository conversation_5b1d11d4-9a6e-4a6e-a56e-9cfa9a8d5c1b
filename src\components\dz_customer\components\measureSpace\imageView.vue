<template>
    <div>
        <el-dialog size="large" title="量尺图片查看" :visible.sync="dialogTableVisible" @close="infos = []">
            <el-row>
                <el-col :span="16">
                    <div class="title">
                        <div class="title-item">
                            <h2>其他</h2>
                            <span>(共{{imgs.length}}张图)</span>
                        </div>
                        <div class="title-item">
                            <h4>客户：</h4>
                            <span>{{client}}</span>
                        </div>
                        <div class="title-item">
                            <h4>设计师</h4>
                            <span>{{designer}}</span>
                        </div>
                    </div>
                    <el-carousel 
                        @change="carouselChange" 
                        ref="carousel" 
                        :key="carouselKey"  
                        trigger="click" 
                        :height="carouselHeight"
                        :autoplay="false"
                        arrow="never"
                        >
                        <el-carousel-item v-for="(item, index) in imgs" :key="index">
                            <img class="carousel-img" :src="item"/>
                        </el-carousel-item>
                    </el-carousel>
                </el-col>
                <el-col :span="8" class="right">
                    <div class="section">
                        <h3 class="section-title">客户信息</h3>
                        <el-row>
                            <el-col
                                v-for="(item, index) in infos"
                                :key="index"
                                :span="12"
                                class="item-col"
                            >
                                <span class="item-title">{{item.label}}:</span><span>{{item.value}}</span>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="section">
                        <h3 class="section-title">要点</h3>
                        <p>
                            {{detail}}
                        </p>
                    </div>
                    <div>
                        <el-button type="primary" @click="prev">上一张</el-button>
                        <el-button type="primary" @click="next">下一张</el-button>
                        <el-button type="primary" @click="dialogTableVisible = false">关闭</el-button>
                        <el-button type="primary" @click="saveImg">下载</el-button>
                        <el-button type="primary" @click="saveZip">打包下载</el-button>
                    </div>
                </el-col>   
            </el-row>            
        </el-dialog>
    </div>
</template>
<script>
import JsZip from 'jszip'
import { saveAs } from 'file-saver'
export default {
    data() {
        return {
            dialogTableVisible: false,
            carouselKey:1,
            carouselHeight: '300px',
            client: '苏先生(345345)',
            designer: '杭俊英()',
            detail: '主卧地面贴木纹砖。不吊天花。主卧的入门口要改动。不让门口正对着客厅。1.8床。如果空间不够就不考虑梳妆台了',
            imgs: [],
            infos:[
                {label: '生活阶段', value: '单身贵族'},
                {label: '总体预算', value: '10万以上'},
                {label: '总体预算', value: '10万以上'}
            ]
        }
    },
    created() {
        this.carouselHeight = innerHeight*0.6 + 'px'
    },
    methods: {
        prev() {
            if( this.$refs.carousel && this.carouselIndex > 0) {
                this.$refs.carousel.prev()
            } else {
                this.$message({
                    message: '已经是第一张了',
                    type: 'warning'
                })
            }
              
        },
        next() {
            if( this.$refs.carousel && this.carouselIndex < this.imgs.length - 1) {
                this.$refs.carousel.next()
            } else {
                this.$message({
                    message: '已经是最后一张了',
                    type: 'warning'
                })
            }
              
        },
        show(datas) {
            datas = datas || {}
            this.client = datas.client || '---'
            this.imgs = datas.imgs || []
            this.designer = datas.designer || '---'
            this.detail = datas.detail || '---'
            this.infos = datas.infos || []
            this.carouselKey = Math.random()
            this.dialogTableVisible = true
        },
        carouselChange(e) {
            this.carouselIndex = e
        },
        saveImg(){
            let name = new Date().getTime()
            let img = this.imgs[this.carouselIndex]
            if(!img) {
                this.$message({
                    message: '没有可下载的图片',
                    type: 'warning'
                })
                return
            }
            saveAs(img, name);
        },
        async saveZip(){
            if(!this.imgs.length) {
                this.$message({
                    message: '没有可下载的图片',
                    type: 'warning'
                })
                return
            }
            const proList = []
            const zip = new JsZip()
            const cache = {}
            let ext
             await this.imgs.forEach((item, index) => { // 等待所有图片转换完成
                ext = `.${this.getExt(item)}`
                const pro = this.getUrlBase64(item, ext).then(base64 => {
                    const fileName = item.replace(/(.*\/)*([^.]+)/i,"$2") + '_' + index + ext
                    zip.file(fileName, base64.substring(base64.indexOf(',') + 1), {
                        base64: true
                    })
                    cache[fileName] = base64
                })
                proList.push(pro)
            })
            Promise.all(proList).then(res => {
                if(res) {
                    zip.generateAsync({
                        type: 'blob'
                    }).then(content => { // 生成二进制流
                        saveAs(content, 'images.zip') // 利用file-saver保存文件
                    })
                }
            })
        },
        getExt(url){
            let strArr = url.split('.')
            let ext = strArr[strArr.length-1]
            let imageExtArr = ['jpg','jpeg', 'png', 'gif', 'bmp']
            if(!imageExtArr.includes(ext.toLocaleLowerCase())) {
                ext = 'jpg'
            }
            return ext
        },
        async getUrlBase64(url, ext) {
            return new Promise((resolve) => {
                var canvas = document.createElement('canvas') // 创建canvas DOM元素
                var ctx = canvas.getContext('2d')
                var img = new Image()
                img.crossOrigin = 'Anonymous' // 处理跨域
                img.src = url
                img.onload = () => {
                    canvas.width = img.width // 指定画板的高度,自定义
                    canvas.height = img.height // 指定画板的宽度，自定义
                    ctx.drawImage(img, 0, 0) // 参数可自定义
                    var dataURL = canvas.toDataURL('image/' + ext)
                    resolve(dataURL) // 回调函数获取Base64编码
                    canvas = null
                }
            })
        }
    }
}
</script>
<style scoped>
.el-row {
    text-align: left;
}
.title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}
.title .title-item h2,
.title .title-item h4 {
    margin: 0 4px 0 0;
    display:inline-block;
    font-weight: bold;
}
.right {
    padding-left:20px;
}
.section {
    
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}
.section-title {
    margin: 0 0 5px 0;
    font-weight: bold;
}
.item-title {
    font-size: 15px;
    font-weight: bold;
}
.item-col {
    margin-bottom: 4px;
}
.el-button {
    margin-left:0 !important;
    margin: 0 10px 10px 0;
}
.carousel-img {
    max-width: 100%;
    position: absolute;
    top: 50%;
    left:50%;
    transform: translate(-50%, -50%);
}
</style>