<template>
<!-- 表单组件-添加空间 -->
    <div class="measureSpace">
        
        <!-- 标签 设计空间不显示-->
        <div class="tags" v-if="config.spacename !== 'design_room_name'">
            <div 
                @click="tagclick(tag)"
                v-for="(tag, index) in tagsFilter"
                :key="index"
                >
                <el-tag
                    size="mini"
                    :closable="true"
                    @close.stop="handleCloseTag(index, tag)"
                >
                {{tag.spacename}}
                </el-tag>            
            </div>
            <div @click="addDesignTag">
                <el-tag >
                    其他<i class="el-icon-plus"></i>
                </el-tag>
            </div>
        </div>
        <!-- 表格 -->
        <el-table
            :data="value"
            size="small"
            border
            width="100%"
            :style="{height: measureHeight + ' !important'}"
            >
            <el-table-column
            label="空间名"
            align="center"
            width="180">
            <template slot-scope="scope">
                 <div>
                     <span style="color: #ff4949; margin-right: 4px;">*</span>{{scope.row[spacename]}}
                 </div>
            </template>
            </el-table-column>
            <el-table-column
            label="空间设计要点"
            align="center"
            >
            <template slot-scope="scope">
                 <el-input 
                    size="mini"
                    v-model="scope.row[detail]" 
                    :maxlength="300"
                    placeholder="请输入设计要点"
                    style="width:100%;"
                    @change="change"
                    ></el-input>
            </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
            <template slot-scope="scope">
                <el-button
                size="mini"
                type="primary"
                @click="handleUpload(scope.$index, scope.row)">上传</el-button>
                <el-button
                size="mini"
                type="success"
                @click="handleView(scope.$index, scope.row)">查看</el-button>
                <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import {createId} from '../../common/api'
import formCreate from '../formCreate/formCreate'
import { addDesignTag, imageAdmin, imageView } from '../../alert/alert'
export default {
    components: {
        formCreate
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    data() {
        return {
            dialogTableVisible: false,
            tableData: [],
            formData: [],
            tagsFilter: []
        }
    },
    props: {
        tags: {
            type: Array
        },
        value: {
            type: Array
        },
        config: {
            type: Object,
            default() {
                return {}
            }
        },
        delUrl: {
            type: String
        }
        
    },
    watch: {
        tags: {
            handler() {
                this.tagsFilter = this.tags.filter(item => true)
            },
            immediate: true,
            deep: true
        }
    },
    computed: {
        spacename() {
            return this.config.spacename || 'spacename'
        },
        detail() {
            return this.config.detail || 'detail'
        },
        id() {
            return this.config.id || 'id'
        },
        client_number() {
            return this.config.client_number || ''
        },
        cn() {
            return this.config.cn || ''
        },
        useBy() {
            return this.config.useBy || ''
        },
        measureHeight() {
            return this.config.height || 'auto'
        }
    },
    created() {
        this.formData = [
            {
                cols: [
                    {formType: 'elSelect', label: '空间类型', prop: 'type', span: 10, options:[{label:'其他',value:'其他'}], value: '其他',disabled: true},
                    {formType: 'elInput', label: '空间名称',  prop: this.spacename, span: 14}
                ]
            }
        ]
        
    },
    methods: {
        
        addDesignTag() {
            addDesignTag(this).then(data => {
                this.tagsFilter.push({
                    type: data.type,
                    spacename: data.spacename
                })
            })
        },
       
        handleCloseTag(index) {
            this.tagsFilter.splice(index, 1)
        },
        async tagclick(tag) {
            const id = await createId()
            let spacename = tag.spacename
            let i = 0
            let obj
            let hasSameType = this.value.findIndex(item => item[this.spacename] === spacename) !== -1
            if(hasSameType) {
                while(this.value.findIndex(item => item[this.spacename] === spacename) !== -1) {
                    spacename = spacename.split('-')[0] + `-${i}`
                    i++
                }
            }
            obj = {
                type:tag.type,
            }
            obj[this.spacename] = spacename
            obj[this.detail] = ''
            obj.is_add = true
            // 获取唯一标识
            
            obj[this.id] = id
            this.value.push(obj)
            this.change()
        },
        handleUpload(index, row) {
            this.uploadIndex = index
            imageAdmin(this, {
                    parent_no: this.client_number,
                    child_no: row[this.id]
                }, (data) => {
                this.value[this.uploadIndex].imgs = data
                this.change()
            })
        },
        handleDelete(index) {
            this.$confirm('删除该空间, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let urlObj = {
                    design: '/custom-web/api/customDesign/delete',
                    measure: '/custom-web/api/customMeasure/delete'
                }
                let url = this.delUrl ? this.delUrl : urlObj[this.useBy] || ''
                let param = {}
                param[this.id] = this.value[index][this.id]
                if(url && !this.value[index].is_add) {
                    this.ajax.postStream(url, param, (d) => {
                        if(d.body.result) {
                            this.value.splice(index, 1)
                            this.change()
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                        } else {
                            this.$message({
                                type: 'error',
                                message: d.body.msg
                            });
                        }
                    })
                } else {
                    this.value.splice(index, 1)
                    this.change()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
                
            })
            
        },
        change(){
            this.$emit('change', this.value.map(item => {
                delete item.type
                return item
            }))
            
        },
        handleView(index, row) {
            let imgs = row.imgs || []
            imageView(this, {
                no: {
                    parent_no: this.client_number,
                    child_no: row[this.id]
                },
                client_number: this.cn,
                useBy: this.useBy,
                detail: row[this.detail]
            })
            // let imgs = row.imgs || []
            // this.$refs.imageView.show({
            //     imgs: imgs.map(item => item.url),
            //     detail: row.detail
            // })
        }
    }
}
</script>
<style scoped>

.tags {
    text-align: left;
}
.tags>div {
    display: inline-block;
    margin-right: 6px;
    cursor: pointer;
}
.tags i {
    margin-left: 4px;
}
</style>
<style>
.measureSpace .el-dialog__body span {
    color: inherit;
}

.measureSpace .el-table__body-wrapper {
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
