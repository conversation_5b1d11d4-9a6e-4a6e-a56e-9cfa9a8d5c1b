<template>
  <!-- 详情-驳回分类组件 -->
  <ul class="reject-type-container">
    <el-checkbox-group v-model="activeList" size="small" @change="handleClick">
      <el-checkbox-button
        v-for="item in config"
        :label="item.code"
        :key="item.code"
        >{{ item.name }}</el-checkbox-button
      >
    </el-checkbox-group>
  </ul>
</template>
    <script>
export default {
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [String, Number],
      options:{
        type: Array,
        default() {
          return [];
        },
      }
    },
    config: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      // list: [],
      activeList: [],
    };
  },
  methods: {
    handleClick(item) {
      this.$emit("change", item.join(","));
    },
  },
  // mounted() {
  //   if (!!this.config.aux) {
  //     this.list = __AUX.get(this.config.aux);
  //   }
  // },
};
</script>
<style>
.reject-type-container .el-checkbox-button__inner {
  width: 150px;
  background-color: #f1f1f1;
  text-align: center;
  margin: 0 10px 10px;
  border-radius: 20px !important;
  display: inline-block;
  cursor: pointer;
  border: none;
}
.reject-type-container
  .el-checkbox-button:first-child
  .el-checkbox-button__inner {
  border-left: none;
}
</style>