<template>
<!-- 表单组件-选择省市区 -->
    <el-cascader
        class="myCascader"
        size="mini"
        :value="valueFilter"
        :options="options"
        @active-item-change="handleItemChange"
        @change="caChange"
        :props="props"
        :disabled="disabled"
  ></el-cascader>
</template>
<script>
export default {
    model: {
        prop: 'value',
        event: 'change'
    },
    data() {
        return {
            options: [],
            valueFilter: [],
            props: {
                value: 'label',
                children: 'cities'
            }
        }
    },
    props: {
        value: {
            type: Array
        },
        disabled: {
            type: [String, Boolean]
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(newVal) {
                let cList
                if(!newVal.length) {
                    this.valueFilter = []
                }
                let p = await this.getData('1')
                this.options = p
                if(!newVal.length) {
                    return
                }
                if(newVal[1]) {
                    let c = await this.getData(newVal[0])
                    cList = this.options.filter(item => item.id == newVal[0])[0]
                    cList.cities = c
                }
                if(newVal[2]) {
                    let q = await this.getData(newVal[1], false)
                    cList.cities.filter(item => item.id == newVal[1])[0].cities = q
                }
                let item01 = this.findActiveItem(this.value, 'id', 0)
                let item02 = this.findActiveItem(this.value, 'id', 1)
                let item03 = this.findActiveItem(this.value, 'id', 2)
                
                this.valueFilter = [
                    item01.label || '',
                    item02.label || '',
                    item03.label || ''
                ].filter(item => item)
            }
        }
    },
  
    created() {
        this.obj = {}
      
    },
    methods: {
        caChange(val) {
            this.change(val)
        },
        findActiveItem(arr, label='label', index) {
            if(!arr.length || (index !== undefined && arr.length -1 < index)) return {}
            let options = this.options
            let activeItem
            let item

            for(var i=0,l=arr.length; i<l; i++) {
                item = arr[i]
                if(!options || !options.length || !item) continue
                activeItem = options.filter(el => el[label] == item)[0]
                options = activeItem.cities
                if(index !== undefined && index === i) {
                    break
                }
            }
            return activeItem
        },
        getData(param, hasChildren = true) {
            return new Promise((resolve, reject) => {
                !this.obj && (this.obj = {})
                if(this.obj[param] !== undefined) {
                    resolve(this.obj[param])
                    return
                }
                this.ajax.postStream('/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId',param,data => {
                    data = data.body
                    let options = []
                    if(data.result){
                        for(var key in data.content) {
                            options.push({
                                label: data.content[key],
                                id: key,
                                cities: hasChildren ? [] : null
                            })
                        }
                    }
                    this.obj[param] = options
                    resolve(options)
                },
                function(data){
                    reject()
                    console.log('返回失败的数据')
                })
            })
        },
        change(arr){
            let id01 = this.findActiveItem(arr, 'label', 0)
            let id02 = this.findActiveItem(arr, 'label', 1)
            let id03 = this.findActiveItem(arr, 'label', 2)
            
            this.$emit('change', [
                id01.id || '',
                id02.id || '',
                id03.id || '',
            ])
        },
        handleItemChange(val) {
            return new Promise((resolve, reject) => {
                let activeItem = this.findActiveItem(val)
                let len = val.length
                if(!activeItem.cities || !activeItem.cities.length) {
                    this.getData(activeItem.id, len < 2).then(data => {
                        activeItem.cities = data.length ? data : [{}]
                    }) 
                } else {
                    resolve()
                }
            })
        }
    }
}
</script>
<style>
.myCascader .el-cascader__label {
    line-height: inherit;
}
</style>
