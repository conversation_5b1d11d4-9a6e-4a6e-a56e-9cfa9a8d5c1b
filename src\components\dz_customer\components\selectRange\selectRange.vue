<template>
<!-- 表单组件-选择范围 -->
    <div>
        <el-select size="mini" v-model="value[0]" :disabled="disabled">
            <el-option 
                v-for="(option, optionIndex) in options[0]"
                :key="optionIndex"
                :label="option.label" 
                :value="option.value"></el-option>
        </el-select>
        至
        <el-select size="mini" v-model="value[1]" :disabled="disabled">
            <el-option 
                v-for="(option, optionIndex) in options[1]"
                :key="optionIndex"
                :label="option.label" 
                :value="option.value"></el-option>
        </el-select>
    </div>
</template>
<script>
export default {
   
    props: {
        value: {
            type: Array
        },
        options: {
            type: Array
        },
        disabled:{
            type:Boolean,
            default:false
        }
    }
}
</script>
<style scoped>
.el-select {
    max-width: 42%;
}
</style>
