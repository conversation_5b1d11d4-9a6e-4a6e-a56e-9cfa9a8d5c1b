<template>
  <!-- 列表组件 -->
  <div :style="{height: tableHeight}">
    <my-xpt-list
      :selection="selection"
      ref="xptList"
      :showHead="showHead"
      :showTools="showTools"
      :data="tableDataFilter  ||  tableDatas"
      :colData="colData"
      :pageTotal="pageTotalFilter"
      :btns="btnsFilter"
      :orderNo="orderNo"
      :tools="tools"
      :toolWidth="toolWidth"
      :summaryMethod="summaryMethod"
      :pageLength="pageSize"
      :taggelClassName="taggelClassNames"
      @search-click="searchData"
      @selection-change="select"
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
      @radio-change="radioChange"
      countUrl="/afterSale-web/api/forced/closure/query/count"
      @count-off="countOff"
      :showCount="showCount"
    ></my-xpt-list>
  </div>
</template>
<script>
  //
import fn from "@/common/Fn.js";
import myXptList from "../list/list5";
export default {
  components: {
    myXptList,
  },
  data() {
    return {
      pageTotal: 0,
      pageSize: 20,
      pageNow: 1,
      tableDatas: [],
      loading: false,
      refreshBtnStatus: false,
      showCount: false,
      countOffFlag:false,
    };
  },
  props: {
    taggelClassNames: {
      type: Function
    },
    toolWidth: {
      type: [Number, String]
    },
    summaryMethod: {
      type: Function,
    },
    tableHeight: {
      type: String,
      default: "99%",
    },
    isPushDown: {
      type: Boolean,
      default: false,
    },
    showHead: {
      type: Boolean,
      default: true,
    },
    showTools: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
    },
    tableParam: {
      type: [Object, String, Function],
      default() {
        return {};
      },
    },
    tableUrl: {
      type: String,
    },
    tableFilter: {
      type: Function,
    },
    colData: {
      type: Array,
    },
    btns: {
      type: Array,
      default() {
        return [];
      },
    },
    tools: {
      type: Array,
    },
    orderNo: {
      type: Boolean,
      default: false,
    },
    selection: {
      type: String,
      default: "none",
    },
    isInit: {
      type: Boolean,
      default: true,
    },
    page: {
      type: Boolean,
      default: true,
    },
    list_prop: {
      type: String,
      default: "list",
    },
  },
  computed: {
    pageTotalFilter() {
      return this.tableData || !this.page ? "none" : this.pageTotal;
    },
    btnsFilter() {
      if (this.tableUrl) {
        return [
          {
            type: "primary",
            txt: "刷新",
            loading: this.refreshBtnStatus,
            click: () => {
              // 刷新表格
              this.refreshBtnStatus = true;
              this.refresh();
            },
          },
        ].concat(this.btns);
      }
      return this.btns;
    },
    tableDataFilter() {
      return this.tableData ? this.filter(this.colData, this.tableData) : null
    }
  },
  created() {
    this.isInit && this.initData();
  },
  methods: {
    radioChange(obj) {
      this.$emit("radioChange", obj);
    },
    searchData() {},
    select(rows) {
      // 多选
      this.$emit("checkboxChange", rows);
    },
    pageChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNow = 1;
      this.refresh();
    },
    currentPageChange(page) {
      this.pageNow = page;
      this.refresh();
    },
    refresh() {
      let self = this
      // this._getDataList();
      new Promise((res,rej)=>{
        this.getList(res);
			}).then(()=>{
				if(self.pageNow != 1){
					self.pageTotal = 0;
				}
				self.showCount = false;

			})
      // this.getList();
    },
    // 获取类型列表
    getList() {
      let self = this;
      if (this.tableData) {
        this.tableDatas = this.tableData;
        return;
      }
      if (!this.tableUrl) {
        return;
      }
      let param;
      if (typeof this.tableParam === "string") {
        param = this.tableParam;
      } else {
        param = JSON.parse(
          JSON.stringify(
            typeof this.tableParam === "object"
              ? this.tableParam
              : this.tableParam()
          )
        );
      }
      if (!this.isPushDown && this.page) {
        param.page = {
          length: this.pageSize,
          pageNo: this.pageNow,
        };
      }
      this.loading = true;
      for (var key in param) {
        param[key] === "" && delete param[key];
      }
      this.ajax.postStream(
        this.tableUrl,
        param,
        (d) => {
          this.$emit("completed", d);
          if (d.body.result) {
            // 数据获取成功
            let content = d.body.content || {};
            this.tableDatas = Array.isArray(content)
              ? content
              : content[this.list_prop] || [];
            this.tableFilter &&
              (this.tableDatas = this.tableFilter(this.tableDatas));
            // this.pageTotal = content.count || 0;
            if(!self.showCount){
              self.pageTotal = content.list.length == (self.pageSize+1)? (self.pageNow*self.pageSize)+1:(self.pageNow*self.pageSize);
            }
            this.filter(this.colData, this.tableDatas);
            this.$emit("tableData", this.tableDatas);
          } else {
            this.$message({
              message: d.body.msg || '获取失败！',
              type: "error",
            });
          }
          this.loading = false;
          this.refreshBtnStatus = false;
        },
        (err) => {
          // 接口出错
          this.$emit("completed", err);
          this.$message.error(err);
          this.loading = false;
          this.refreshBtnStatus = false;
        }
      );
    },
    countOff(){

			let self = this,
			url = "/custom-web/api/customSysTrade/getCustomSysTradeListCount";
			if(!self.tableDatas.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;
      let param;
      if (typeof this.tableParam === "string") {
        param = this.tableParam;
      } else {
        param = JSON.parse(
          JSON.stringify(
            typeof this.tableParam === "object"
              ? this.tableParam
              : this.tableParam()
          )
        );
      }
      if (!this.isPushDown && this.page) {
        param.page = {
          length: this.pageSize,
          pageNo: this.pageNow,
        };
      }
      this.loading = true;
      for (var key in param) {
        param[key] === "" && delete param[key];
      }
			self.ajax.postStream(url,param,function(response){
					if(response.body.result){
						
						self.pageTotal = response.body.content;
						self.showCount = true;
						self.countOffFlag = false;

					}else{
						self.$message.error(response.body.msg);
					}
				});
		},
    filter(colData, data) {
      let prop = ''
      colData.forEach((col) => {
        if (col.filter) {
          if (col.filter === "date") {
            let formats = col.formats || "yyyy-MM-dd hh:mm:ss";
            data.forEach((el) => {
              el[col.prop] = fn.dateFormat(el[col.prop], formats);
            });
          } else if (col.filter === "select") {
            prop = col.prop.slice(0, col.prop.length-3)
            data.forEach((el) => {
              let i = col.options.findIndex(
                (item) => item.value === el[prop]
              );
              el[col.prop] = i !== -1 ? col.options[i].label : "";
            });
          }
        }
      });
      return data
    },
    initData() {
      setTimeout(() => {
        this.pageNow = 1;
        this.getList();
      }, 100);
    },
  },
};
</script>
