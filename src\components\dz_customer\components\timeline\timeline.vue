<template>
<!-- 时间顺序组件 -->
    <div class="timeline">
        <div class="timeline-item"
            v-for="(item, index) in line"
            :key="index"
        >
            <div class="timeline-item__tail" v-if="order"></div>
            <div v-if="order" class="timeline-item__node timeline-item__node--normal timeline-item__node--">{{index+1}}</div>
            <div class="timeline-item__wrapper">
                <div class="timeline-item__timestamp is-top">{{item.date}}</div>
                <div class="timeline-item__content">
                    <slot :col="item"></slot>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        line: {
            type: Array
        },
        order: {
            type: Boolean,
            default: true
        }
    }
}
</script>
<style lang="stylus" scoped>
.timeline {
    margin: 0;
    font-size: 14px;
    text-align left;
    .timeline-item {
        position: relative;
        padding-bottom: 20px;
        &:last-child {
            .timeline-item__tail {
                 display:none;
            }
        }
        .timeline-item__tail {
            position: absolute;
            left: 10px;
            height: 100%;
            border-left: 2px solid #909399;
            
        }
        .timeline-item__node--normal {
            left: 1px;
            width: 20px;
            height: 20px;
        }
        .timeline-item__node {
            position: absolute;
            background-color: #909399;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items center;
            color #fff;
        }
        .timeline-item__wrapper {
            position relative;
            padding-left 28px;
            top -3px;
            .timeline-item__timestamp.is-top {
                margin-bottom: 8px;
                padding-top 4px;
            }
            .timeline-item__timestamp {
                color #909399;
                line-height 1;
                font-size 13px;
            }
            .timeline-item__content {
                border: 1px solid #eaeefb;
                padding: 10px;
                &:hover {
                    box-shadow: 0 0 8px 0 rgba(232, 237, 250, .6), 0 2px 4px 0 rgba(232, 237, 250, .5);
                }
            }
        }
    }
}
</style>