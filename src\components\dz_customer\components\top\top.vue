<template>
<!-- 详情-返回顶部按钮 -->
    <transition name="el-zoom-in-center">
        <div class="top" v-show="show" @click="toTop">
            <i class="el-icon-caret-top"></i>
        </div>
    </transition>
</template>
<script>
import { throttle } from 'loadsh'
export default {
    data() {
        return {
            show: false
        }
    },
    props:{
        el:{
            type: String
        }
    },
    mounted() {
        if(!this.el) return
        this.element = document.querySelector(this.el)
        this.handler = throttle((e) => {
            this.show = this.element.scrollTop > 400
        })
        this.element.addEventListener('scroll', this.handler)
    },
    beforeDestroy() {
        this.element.removeEventListener('scroll', this.handler)
    },
    methods: {
        toTop() {
            this.element.scrollTop = 0
        }
    }
}
</script>
<style scoped>
.top {
    position: fixed;
    bottom: 50px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: #58b7ff;
    border-radius: 50%;
    cursor: pointer;
    opacity: .4;
    transition: .3s;
}
.top:hover {
    opacity: 1;
}
.el-icon-caret-top {
    color: #fff;
    display:  block;
    line-height: 50px;
    text-align: center;
    font-size: 20px;
}
</style>