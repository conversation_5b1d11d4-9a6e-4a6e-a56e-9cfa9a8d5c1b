<template >
<!-- 附件上传组件 -->
		<div style="display:inline-block">
			<input v-if="multiple" style="display: none" type="file" :id='files' multiple="multiple" @change='change' >
			<input v-else style="display: none" type="file" :id='files'  @change='change' >
		</div>
</template>
<script>
	import Fn from '@common/Fn.js'
	import HelpFile  from '@common/helpFile.js'
	import { Message } from 'element-ui';
	export default {
		data() {
			return {
				index: 0,
				imgList: [],
				oldData: null,
				files:'files'+Math.random(),//随机定义id，防止出现相同id
				host:null,
				uploadNum: 0,
				filesLength: null
			}
		},
		props: {
			// 图片数组
			ifClickUpload: {
				type: Boolean,
				default(){
					return false;
				}
			},
			dataObj: {
				type: Object,
				default(){
					return {};
				}
			},
			// 上传完成后callback
			callback: {
				type: Function,
				default(){
					return f => f
				}
			},
			// 上传至新平台还是阿里云
			ifFile: {
				type: Boolean,
				default(){
					return false
				}
			},
			isLocal: {
				type: Boolean,
				default: false
			},
			multiple: {
				type: Boolean,
				default: true
			},
			format: {
				type: String
			}
		},
		methods: {
			//获取上传文件的信息，并逐个上传
			change() {
				let files = document.getElementById(this.files).files,
					len = files.length,
					fr = new FileReader(),
					index = 0,
					self = this;
				if(!this.isFormat(files[0].name)) {
					return
				}
				fr.readAsDataURL(files[0]);
				this.uploadNum = 0;
				this.filesLength = files.length;
				self.upload(files,0);
				fr.onload = function(e) {
					index += 1;
					if(index<len){
						fr.readAsDataURL(files[index]);
						self.upload(files,index)
					}
				}
			},
			//判断是否为图片
	        isFormat(str) {
				if(!this.format) {
					return true
				}
				if(!this.formatObj) {
					this.formatObj = {
						image: ".jpeg|.gif|.jpg|.png|.bmp|.pic|",
						excel: ".xls|.xlsx|"
					}
				}
	            str = str.toString();
	            var strFilter=this.formatObj[this.format]
	            if(str.indexOf(".")>-1)
	            {
	                var p = str.lastIndexOf(".");
	                var strPostfix=str.substring(p,str.length) + '|';
	                strPostfix = strPostfix.toLowerCase();
	                if(strFilter.indexOf(strPostfix)>-1)
	                {
	                    return true;
	                }
				}
				this.$message.error(`请传入${this.formatObj[this.format]}格式的文件`);
	            return false;
	        },
			// 获取上传图片的key值
			validateKey(resolve){
				this.ajax.postStream('/app-web/app/file/validateKey.do', {type:"GET"} ,(res)=>{
					if(res.body.success){
						resolve && resolve(res.body.data)
					}else{
						this.closeLoading()
						this.$message.error('获取文件路径失败，请重新操作');
					}
				}, err => {
					this.$message.error('获取文件路径失败，请重新操作');
					this.closeLoading()
				})
			},
			closeLoading() {
				if(this.loading) {
					this.loading.close()
				}
				this.loading = null
			},
			// 提交至服务器
			upload(files,index) {
				if(!this.host) {
					this.$message.error('服务器地址获取失败')
					return
				}
				if(!this.loading) {
					this.loading = Message({
						message: '上传中',
						iconClass: 'el-icon-loading', 
						duration: 0
					})
				}
				new Promise((resolve,reject)=>{
					this.validateKey(resolve);
				}).then((key)=>{
					let formData = new FormData();
					formData.append('file', files[index]);
					let host = String(this.host).split('//')[1];
                    let requestHeaderPath=this.getUploadFileApiHeader();
					//上传到附件库
					// let url = 'http://**************:12010/upload-web/app/file/uploadFile/'+host+'/'+key+'.do'
					let url = requestHeaderPath+host+'/'+key+'.do'
					
					// if (this.ifFile) {
					// 	url = '/file-web/api/file/uploadFile.do'
					// }
					
					/*this.ajax.post(this.host+'/api/file/uploadFile.do',formData, (s) => {*/
					this.ajax.post(url,formData, (s) => {
						if(!s.body.success) {
							this.closeLoading()
							this.$message.error(s.body.msg)
							return
						}
						var data ={
							file_type: files[index].name.split('.').pop(),// 文件类型 string
							order_no: this.oldData.parent_no,// 单据编号 string
							sub_order_no: this.oldData.child_no,// 子单据编号 string
							name: files[index].name,// 名字 string
							size: files[index].size,// 大小 long
							group_value_json: JSON.stringify(this.oldData),//  分组内容 string
							// path: s.body.data,//  地址 string
							ext_data: null,// 扩展字段 string
						}
						if (this.ifFile) {
							if(s.ok && s.body && s.body.content){
								// var index1 = files[index].name.indexOf('.')+1;
								// var index2 = files[index].name.length;
								data.path = s.body.content//  地址 string
							}
						} else {
							if(s.ok && s.body && s.body.data){
								// var index1 = files[index].name.indexOf('.')+1;
								// var index2 = files[index].name.length;
								data.path = s.body.data//  地址 string
							}
						}
						//上传到接口，用于查看时获取
						this.ajax.postStream('/file-iweb/api/cloud/file/upload',data,res => {
							if(res.body.result){
								this.uploadNum++;
								//全部上传完才显示成功
								if(this.uploadNum === files.length){
									this.closeLoading()
									this.$message.success('上传成功');
									this.callback(s)
								}
							}else {
								this.closeLoading()
								this.$message.error(res.body.msg);
							}
						}, err => {
							this.closeLoading()
							this.$message.error('接口出错');
						})
					}, (e) => {
						this.closeLoading()
						this.$message({
							message: files[index].name+'上传失败',
							type: 'error'
						}) 
					})
				})
				
				
			},
			// upload(files,index) {
			// 	let formData = new FormData();
			// 	formData.append('file', files[index]);
			// 	//上传到附件库
			// 	let permissionControlCode = this.dataObj.permissionCode;
			// 	let url = '/app-web/app/file/uploadFile.do'
			// 	if (this.ifFile) {
			// 		url = '/file-web/api/file/uploadFile.do'
			// 	}
			// 	permissionControlCode?url+=('?permissionCode='+permissionControlCode):'';
				
			// 	/*this.ajax.post(this.host+'/api/file/uploadFile.do',formData, (s) => {*/
			// 	this.ajax.post(this.host+url,formData, (s) => {
			// 		var data ={
			// 			file_type: files[index].name.split('.').pop(),// 文件类型 string
			// 			order_no: this.oldData.parent_no,// 单据编号 string
			// 			sub_order_no: this.oldData.child_no,// 子单据编号 string
			// 			name: files[index].name,// 名字 string
			// 			size: files[index].size,// 大小 long
			// 			group_value_json: JSON.stringify(this.oldData),//  分组内容 string
			// 			// path: s.body.data,//  地址 string
			// 			ext_data: null,// 扩展字段 string
			// 		}
			// 		if (this.ifFile) {
			// 			if(s.ok && s.body && s.body.content){
			// 				// var index1 = files[index].name.indexOf('.')+1;
			// 				// var index2 = files[index].name.length;
			// 				data.path = s.body.content//  地址 string
			// 			}
			// 		} else {
			// 			if(s.ok && s.body && s.body.data){
			// 				// var index1 = files[index].name.indexOf('.')+1;
			// 				// var index2 = files[index].name.length;
			// 				data.path = s.body.data//  地址 string
			// 			}
			// 		}
			// 		//上传到接口，用于查看时获取
			// 		this.ajax.postStream('/file-iweb/api/cloud/file/upload',data,res => {
			// 			if(res.body.result){
			// 				this.uploadNum++;
			// 				//全部上传完才显示成功
			// 				if(this.uploadNum === files.length){
			// 					this.$message.success('上传成功');
			// 					this.callback(s)
			// 				}
			// 			}else {
			// 				this.$message.error(res.body.msg);
			// 			}
			// 		})
			// 	}, (e) => {
			// 		this.$message({
			// 			message: files[index].name+'上传失败',
			// 			type: 'error'
			// 		})
			// 	})
			// },
		},
		watch:{
			'ifClickUpload': function(newVal,oldVal) {
				if(newVal) {
					
					document.getElementById(this.files).value = '';
					document.getElementById(this.files).click();

					if(this.host) {
						return;
					}
					//获取域名
					this.ajax.postStream('/file-iweb/api/cloud/fileConfig/get',{},res => {
						console.log('res',res);
						if(res.body.result){
							this.host = res.body.content.host;

						}else {

							this.$message.error(res.body.msg);
						}
					})
				}
			},
			'dataObj': function(newVal,oldVal) {
				this.oldData = JSON.parse(JSON.stringify(newVal));
				var list = [];
				if(this.oldData.parent_name && this.oldData.child_name) {
					list = HelpFile[this.oldData.parent_name][this.oldData.child_name] || [];
				}
				//过滤掉在已经定好的分组里没有分类
				list.forEach(v => {
					if(this.oldData.content){
						for(var key in this.oldData.content){
							if(v.name == key){
								v.value = this.oldData.content[key];
							}
						}
					}
				})
				this.oldData.content = list;
			},

		},

		mounted() {
		}
	}
</script>
