
<template>
  <!-- 表单组件-附件上传按钮 -->
  <div>
    <span class="btn" :class="disabled ? 'gray' : ''" @click="handleUpload">{{uploadBtnLabel}}</span>
    <span class="btn" @click="handleView" v-if='look'>查看</span>
    <slot name="rules"></slot>
    <div>
      <file-list defaultValue :delPopover="!disabled" :value="files" :autoGetFile="false" @del="change" :params="config"></file-list>
    </div>
  </div>
</template>
<script>
import { imageAdmin, imageView } from "../alert/alert";
import fileList from "../clientInfo/fileList";
import { getFileList } from "../common/api";
import emitter from "../common/mixins/emitter";
export default {
  mixins: [emitter],
  model: {
    prop: "value",
    event: "change",
  },
  components: {
    fileList,
  },
  data() {
    return {
      files: [],
    };
  },
  props: {
    // 校验id提示
    parent_no_tips: {
      type: String
    },
    // 按钮提示
    uploadBtnLabel: {
      type: String,
      default: '上传文件'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Object],
    },
    config: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  watch: {
    value(v) {
      // 避免重置是必填校验错误
      if(!v && this.hasFile) {
        this.change()
      }
    },
  },
  computed: {
    parent_no() {
      return this.config.parent_no || "";
    },
    child_no() {
      return this.config.child_no || "";
    },
    emit() {
      return this.config.emit || "";
    },
    useBy() {
      return this.config.useBy || "";
    },
    client_number() {
      return this.config.client_number || "";
    },
    look(){
      return this.config.look || "";
    }
  },
  created() {
    this.timer = setTimeout(() => {
      this.getFileList(true);
    }, 500);
  },
  beforeDestroy() {
    clearTimeout(this.timer);
    this.timer = null;
    this.newEmit && this.$root.eventHandle.$off(this.newEmit, this.getFileList);
  },
  methods: {
    async getFileList(init) {
        let params = {
            order_no: this.parent_no,
        sub_order_no: this.child_no,
        }
        if(this.config.type === 'cd'){
            delete params.sub_order_no
        }
      let list = await getFileList(params);
      if(this.config.type === 'cd'){
        list = list.filter(item=>{
            return /^cd/.test(item.sub_order_no)
        })
      }
      this.config.fileListFilter && (list = this.config.fileListFilter(list));
      this.files = list;
      this.hasFile = list.length !== 0;
      if (init === true && !this.hasFile) {
        return;
      }
      this.change(init);
    },
    change(init) {
      let value =
        this.files.length > 0 ? { value: "true", init: init === true } : "";
      this.$emit("change", value);
      this.dispatch("ElFormItem", "el.form.change", [value]);
    },
    handleUpload() {
      if(this.disabled) return

      if(!this.config.parent_no) {
        // 验证是否有上传id
        this.$message.error(this.parent_no_tips || '缺少必要的上传附件id')
        return
      }

      this.newEmit &&
      this.$root.eventHandle.$off(this.newEmit, this.getFileList);
      this.newEmit = this.emit ? this.emit : "upload" + new Date().getTime();
      this.$root.eventHandle.$on(this.newEmit, this.getFileList);
      this.$root.eventHandle.$emit("scanClassify",this.config)
      this.$root.eventHandle.$on("backEvent",this.uploadEvent)
      this.uploadEvent()
    
    },
    uploadEvent(value){
        imageAdmin(this, {
        fileListFilter: this.config.fileListFilter,
        parent_no: this.parent_no,
        child_no: this.child_no,
        emit: this.newEmit,
      });
    },
    handleView() {
      if (this.client_number === "") {
        this.$message.warning("请先选择订单号");
        return
      }
      imageView(this, {
        no: {
          parent_no: this.parent_no,
          child_no: this.child_no,
        },
        client_number: this.client_number,
        useBy: this.useBy,
      });
    },
  },
};
</script>
<style scoped>
.btn {
  display: inline-block;
  background: #13ce66;
  color: #fff;
  line-height: 18px;
  border-radius: 4px;
  padding: 2px 4px;
  cursor: pointer;
}
.gray {
  background: #eef1f6;
  color: #4e5e6e;
  cursor: inherit;
}
</style>
