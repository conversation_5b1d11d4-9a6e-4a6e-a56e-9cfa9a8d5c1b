<!--
 * @Author: your name
 * @Date: 2021-03-11 16:16:36
 * @LastEditTime: 2021-04-12 11:03:17
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \front\src\components\dz_customer\components\user.vue
-->
<template>
    <div>
        <xpt-input @change="change" placeholder="请选择人员信息" icon="search" size='mini' 
                    :value="labelText" :on-icon-click="searchPersonal"  readonly></xpt-input>
    </div>
</template>
<script>
export default {
    model: {
        prop: 'value',
        event: 'change'
    },
    data() {
        return {
            isSelect: false,
            label: ''
        }
    },
    props: {
        value: {
            type: [String, Number]
        },
        config: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    computed: {
        labelText() {
            let configLable = this.config.label || ''
            return this.value ? this.isSelect ? this.label : configLable : ''
        }
    },
    methods: {
        change(v) {
            if(v === undefined) {
                this.$emit('change', '')
            }
        },
        searchPersonal(){
            //弹出人员列表的选项
            var data = {form:'user',status:1,isEnable:1};
            data.callback=(d)=>{
                this.isSelect = true
                this.label = d.data.realName
                this.$emit('change', d.data.id)
            }
            this.$root.eventHandle.$emit('alert',{
                component:()=>import('@components/personel/list.vue'), close:function(){
                // console.log('222222');
            },
            style:'width:900px;height:600px',
                title:'请选择关联人员信息',
                params:data
            });
        },
    }
}
</script>
