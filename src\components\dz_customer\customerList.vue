<template>
<!-- 原始订单列表 -->
  <div style="height: 99%;">
    <commonList
    ref="list"
    :params = params
    :btns="btns"
    :tools="tools"
    :importParam="importParam"
    url="/custom-web/api/customSysTrade/getCustomSysTradeList"
    :defaultValue="defaultValue"
    :initParam="{trade_type: 'ORIGINAL'}"
    @getInfo="infoEvent"
    @selectionChange="selectionChange"
    ></commonList>
  </div>
</template>

<script>
import commonList from './commonList2'
import btnStatus from './common/mixins/btnStatus'
import { getRole, shopVerify} from './common/api'

export default {
  components: {
    commonList
  },
  mixins: [
      btnStatus
    ],
  props: ['params'],
  data() {
    return {
      importParam:{
        uploadUrl:'/custom-web/api/customSysTrade/importTrade',
        otherParams:{}
      },
      defaultValue: {
        client_status: ['WAITING_DISTRIBUTION_DESIGN','WAITING_PURCHASE'],

      },
      role:["other"],
      btns: [],
      info:{},
      select:[],
      tools: []
    }
  },
  methods:{
    selectionChange(data){
      this.select = data;
    },
    checkStatus(resolve){
      this.ajax.postStream('/custom-web/api/customPayment/shopVerify','',(res) =>{
                    let body = res.body
                    if(body.result){
                        resolve(body.content)
                    } else {
                        this.$message.error(body.msg);
                    }
                })
    },
    // 添加客户
    add() {
      let self = this;
      console.log(this.info)
      new Promise ((resolve,reject)=>{
        this.checkStatus(resolve)
      }).then(res=>{
         self.$root.eventHandle.$emit('creatTab', {
            name: '新增订单',
            params: {
              shopInfo: this.info,
              isDisplay:!res
            },
          component: () => import('@components/dz_customer/addCustomer.vue')
        })
      })


    },
    changeUser() {
     if(!this.select.length){
       this.$message.error('请选择数据')
       return;
     }

      this.$root.eventHandle.$emit('alert', {
        title: '批量转单',
				style:'width:450px;height:200px',
        params: {
          selectData: this.select
        },
        component: () => import('@components/dz_customer/alert/changeUser.vue')
      })
    },
    refresh() {
      this.$refs.list._getDataList()
    },
    mod(d) {
      this.$root.eventHandle.$emit('creatTab', {
        name: '编辑订单信息',
        component: () => import('@components/dz_customer/addCustomer.vue'),
        params: {
          client_number: d.client_number,
          custom_trade_id: d.custom_trade_id,
          shopInfo: this.info,
          shop_code:d.shop_code,
          shop_name:d.shop_name,
        }
      })
    },
    customNoDealTrade(client_number,resolve){
      this.ajax.postStream('/custom-web/api/customNoDeal/verifyTrade',{client_number:client_number},(res) =>{
                    let body = res.body
                    if(body.result){
                        resolve(body.content)
                    } else {
                        this.$message.error(body.msg);
                    }
                })
    },
    undeal(d) {
      const {client_number, client_name, client_mobile, client_sex, designer_name} = d
      const data = {
        client_number, client_name, client_sex, client_mobile, designer_name
      }
      let isBefore  =['WAITING_DISTRIBUTION_DESIGN', 'WAITING_MEASUREMENT', 'WAITING_SIGN_CONTRACT',"IN_SIGN_CONTRACT",'WAITING_DESIGN' ,'IN_DESIGN'].includes(d.client_status);
      if(isBefore){
        this.$root.eventHandle.$emit('creatTab', {
          name: '取消订单登记',
          component: () => import('@components/dz_customer/undeal.vue'),
          params: {
            isBefore:isBefore,
            customerInfo: data,
            lastTab: this.params.tabName,

          }
        })
        return;
      }
      new Promise((resolve,reject)=>{
        this.customNoDealTrade(client_number,resolve)
      }).then(res=>{
        this.$root.eventHandle.$emit('creatTab', {
          name: '取消订单登记',
          component: () => import('@components/dz_customer/undeal.vue'),
          params: {
            isBefore:isBefore,
            customerInfo: data,
            lastTab: this.params.tabName,
            message:res.message
          }
        })
      })



    },

    changeGoods(d){
       const {client_number, client_name, client_mobile, client_sex, designer_name} = d
      const data = {
        client_number, client_name, client_sex, client_mobile, designer_name
      }
      this.ajax.postStream('/custom-web/api/customGoods/changeGoodsList',{client_number:client_number},(res) =>{
                    let body = res.body
                    if(body.result){
                        this.$root.eventHandle.$emit('alert', {
                        component: () => import('@components/dz_customer/changeGoods.vue'),
                        style: 'width:800px;height:500px',
                        title: '修改商品',
                        params: {
                          goodsList:body.content.goodsList ,
                          message:body.content.message
                        }

                      })
                    } else {
                        this.$message.error(body.msg);
                    }
                })




    },
    selectDesigner(d) {
        const { custom_client_id, client_number, client_name,
        client_type, client_sex, client_mobile, client_tel,
        client_address, reserve_measure_date, measure_room,
        loft_name, loft_coordinate, remark, receiver_state_code,
        receiver_city_code, receiver_district_code, deliver_method } = d
        const data = {
          custom_client_id, client_number, client_name, client_type,client_sex,
          client_mobile, client_tel, client_address, reserve_measure_date,
          measure_room, loft_name, loft_coordinate, remark,
          receiver_state_code,receiver_city_code,receiver_district_code, deliver_method
        }

        this.$root.eventHandle.$emit('creatTab', {
          name: '分配设计师',
          component: () => import('@components/dz_customer/selectDesigner.vue'),
          params: {
            customerInfo: data,
            lastTab: this.params.tabName
          }
        })
      },
      selectOnline(d){
					this.$root.eventHandle.$emit('creatTab', {
              name: '线上订单',
              params: {
                  info: d,
                  shop_id:this.info.xptShopId,
                  shopInfo:this.info
              },
              component: () => import('@components/dz_customer/mergeOrder.vue')
          })

      },
      downloadModel(){
        var self=this;
        self.ajax.postStream("/reports-web/api/template/list",{"page_name":"template_download_list","where":[{"field":"1e08dba31f3647691c61c2030bd8e632","table":"d331c120edc028bbbc83230f04b409fa","value":"定制订单导入模板","operator":"=","condition":"AND","listWhere":[]}],"page_size":50,"page_no":1},function(response){
            if(response.body.result && response.body.content.count != 0){
              self.$message({message: '操作成功',type: 'success'})
              window.open(response.body.content.list[0].url);
            }else{
              self.$message.error('未找到对应模板，请到模板下载列表维护')
            }
          },e=>{
            self.$message.error(e);
        })
      },
      //查看导入结果
      downloadExcel() {
        this.$root.eventHandle.$emit("alert", {
          params: {
          data: {

            },
          url: "/custom-web/api/customSysTrade/importTrade/list",
          showDownload:true
          },
          component: () => import("@components/common/eximport"),
          style: "width:800px;height:400px",
          title: "导入结果",
        });
      },
      infoEvent(info){
        this.info=info
      }
  },
  async mounted() {
    // dd订单
    this.params.pageInfo="dd"
    this.role = await getRole()
    this.shopVerify = await shopVerify()
    if(this.role.indexOf('DZ_SJS') != -1||this.role.indexOf('DZ_DG') != -1||
      this.role.indexOf('DZ_DZ') != -1||this.role.indexOf('DZ_SJZG') != -1
    ) {
      this.$refs.list.getInfo()
    } else {
      this.$refs.list.getInfoNoShop()
    }
    this.btns = [
      {
        type: 'primary',
        txt: '新增',
        show: this.isAdd(this.role),
        click:() =>{
          this.add()
        }
      },
      {
        type: 'primary',
        txt: '批量转单',
        show: true,
        click:() =>{
          this.changeUser()
        }
      },

      {
        type: 'success',
        txt: '查看批量新增结果',
        show: true,
        click:() =>{
          this.downloadExcel()
        }
      },
      {
        type: 'primary',
        txt: '下载模板',
        show: true,
        click:() =>{
          this.downloadModel()
        }
      },
    ].filter(item => item.show)

    let self = this
    this.tools = [
      {
        type: 'warning',
        txt: '修改',
        click(d) {
          self.mod(d)
        },
        show(d) {
          return self.isMod({client_status: d.client_status_value}, self.role)
        }
      },
      {
        type: 'danger',
        txt: '取消订单',
        click(d) {
          self.undeal(d)
        },
        show(d) {
          return self.isUndeal({client_status: d.client_status_value}, self.role)
        }
      },
      {
        type: 'danger',
        txt: '修改商品',
        click(d) {
          self.changeGoods(d)
        },
        show(d) {
          return self.isChangeGoods({client_status: d.client_status_value}, self.role)
        }
      },
      {
        type: 'primary',
        txt: '分配设计师',
        click(d) {
          self.selectDesigner(d)
        },
        show(d) {
          return self.isSelectDesigner({client_status: d.client_status_value}, self.role)
        }
      },
      {
        type: 'warning',
        txt: '线上订单',
        click(d) {
          self.selectOnline(d)
        },
        show(d) {
          return Number(d.client_status_code) < 120 && (d.shopType === 'O2O_CONTRACT' || d.shopType === 'O2O_OWN')
        }
      }
    ].filter(item => item.show)
    this.$root.eventHandle.$on('refreshclientList', this.refresh)
  },
  beforeDestroy() {
    this.$root.eventHandle.$off('refreshclientList', this.refresh)
  }

}
</script>

<style>

</style>
