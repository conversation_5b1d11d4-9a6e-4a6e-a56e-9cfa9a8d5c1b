<template>
<!-- 设计列表 -->
  <div style="height: 99%;">
    <commonList
    ref="list"
    :params = params
    :tools="tools"
    url="/custom-web/api/customSysTrade/getInDesignSysTradeList"
    :defaultValue="defaultValue"
    ></commonList>
  </div>
</template>

<script>
import commonList from './commonList2'
import btnStatus from './common/mixins/btnStatus'
import { getRole, isHaveGoods } from './common/api'
import { addDesignAssess, addDesign, tips, addConnect, addCheck} from './alert/alert'

export default {
  components: {
    commonList
  },
  mixins: [
    btnStatus
  ],
  props: ['params'],
  data() {
    return {
      defaultValue: {
        client_status: ['WAITING_DESIGN','REJECT_VERIFY'],
      },
      tools: []
    }
  },
  methods:{
    refresh() {
      this.$refs.list._getDataList()
    },
    getValue(url,param){
      return new Promise((resolve,reject) => {
        this.ajax.postStream(url, param, (d) => {             
          let value = d.body.result ? d.body.content || {} : {}
          resolve(value)
        })
      })
    },
    //商品
      goodsList(d) {
        this.$root.eventHandle.$emit('creatTab', {
          name: '商品列表',
          component: () => import('@components/dz_customer/goodsList.vue'),
          params: {
            row: d
          }
        })
      },
  },
  async mounted() {
    this.role = await getRole()
    if(this.role.indexOf('DZ_SJS') != -1||this.role.indexOf('DZ_DG') != -1||
      this.role.indexOf('DZ_DZ') != -1||this.role.indexOf('DZ_SJZG') != -1
    ) {
      this.$refs.list.getInfo()
    } else {
      this.$refs.list.getInfoNoShop()
    }
    let self = this
    this.tools = [
    {
      type: 'primary',
      txt: '设计',
      click: async (d) => {
        addDesign(this,Object.assign({}, d))
      },
      show(d) {
        return self.isDesign({client_status: d.client_status_value, designer_number: d.designer_number}, self.role)
      }
    },
    {
      type: 'primary',
      txt: '商品',
      click: (d) => {
        self.goodsList(d)
      },
      show(d) {
        return  self.isGoodsList({client_status: d.client_status_value, if_disallow: d.if_disallow, designer_number: d.designer_number}, self.role)
      }
    },
    {
      type: 'primary',
      txt: '提交',
      click: async (d) => {
        const res = await isHaveGoods(d.client_number)
        const tip = '没有添加商品，不能进行自评。'
        if(!res) {
            tips(this, tip)
          } else {
            addDesignAssess(this,d)
          }
      },
      show(d) {
        return self.isDesignAssess({client_status: d.client_status_value, designer_number: d.designer_number}, self.role)
      }
    },
    {
      type: 'primary',
      txt: '查房',
      click:  (d) => {
        addCheck(self, d.client_number)
      },
      show(d) {
        return self.isCheck({client_status: d.client_status_value}, self.role)
      }
    },
    {
      type: 'primary',
      txt: '沟通',
      click: async (d) => {
        addConnect(this,d, null)
        // const list = await getViewRegister({client_number:d.client_number})
        // const tip = '请先进行查房再沟通'
        // if(!list.length) {
        //     tips(this, tip)
        //   } else {
        //     addConnect(this,d)
        //   }
      },
      show(d) {
        return self.isCommunicate({client_status: d.client_status_value, designer_number: d.designer_number}, self.role)
      }
    }
    ].filter(item => item.show)
    this.$root.eventHandle.$on('refreshdesignList', this.refresh)
    this.$root.eventHandle.$on('saveCustomCommunication', this.refresh)
    
  },
  beforeDestroy() {
    this.$root.eventHandle.$off('refreshdesignList', this.refresh)
    this.$root.eventHandle.$off('saveCustomCommunication', this.refresh)
  }

}
</script>

<style>

</style>
