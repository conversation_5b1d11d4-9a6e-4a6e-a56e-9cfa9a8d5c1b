<template>
  <!-- 导出销售报表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 查询 -->
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
    </div>
    <!-- 操作栏 -->

    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
      <my-table
        ref="table"
        tableUrl="/custom-web/api/guideReport/getOrderList"
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
      ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from "../components/formCreate/formCreate";
import myTable from "../components/table/table";
import { exportDj } from "../common/api";
import { getMap } from "../common/clientDictionary";
import Export from "../common/mixins/export";
export default {
  components: {
    formCreate,
    myTable,
  },
  mixins: [Export],
  data() {
    let _this = this;
    return {
      queryItems: [],
      btns: [
        {
          type: "success",
          txt: "导出报表",
          loading: false,
          click: () => {
            this.btns[0].loading = true;
            exportDj(this.tableParam).then((res) => {
              this.btns[0].loading = false;
            });
          },
        },
      ],
      tableParam: {},
      colData: [{
          label: "店铺名",
          prop: "shop_name",
          width: 180,
        },
        {
          label: "店铺编号",
          prop: "shop_code",
          width: 120,
        },
        {
          label: "设计师",
          prop: "real_name",
          width: 88,
        },
        {
          label: "客户名",
          prop: "client_name",
          width: 100,
        },
        {
          label: '客户手机',
          prop: 'receiver_mobile',
          width: 120,
          format: 'hidePhoneNumber'
        },
        {
          label: "客户性质",
          prop: "client_property",
          width: 100,
        },
        {
          label: "单号",
          prop: "client_number",
          width: 160,
          redirectClick: (row) => {
            _this.$root.eventHandle.$emit(
              "creatTab",
              row.trade_type == "ORIGINAL"
                ? {
                    name: "订单详情",
                    component: () =>
                      import(
                        "@components/dz_customer/clientInfo/clientInfo.vue"
                      ),
                    params: {
                      customerInfo: row,
                      lastTab: _this.params.tabName,
                    },
                  }
                : {
                    name: "补单详情",
                    component: () =>
                      import(
                        "@components/dz_customer/supplement/supplyInfo.vue"
                      ),
                    params: {
                      client_number: row.client_number,
                    },
                  }
            );
          },
        },

        {
          label: "订单状态",
          prop: "value",
          width: 100,
        },
        {
          label: "订单类型",
          prop: "client_type",
          width: 100,
        },
        {
          label: "套餐类型",
          prop: "combo_type",
          width: 100,
        },
        {
          label: "是否拎包",
          prop: "if_bag",
          width: 100,
          formatter:(e)=>{
            return e=="Y"?"是":"否"
          }
        },
        {
          label: "标准售价",
          prop: "sumretail_price",
          width: "130",
        },
        {
          label: "经销采购价",
          prop: "sumdealer_discount",
          width: 120,
        },
        {
          label: "客户预算",
          prop: "budget",
          width: 120,
        },
        {
          label: "合同金额",
          prop: "co_agreed_to_gold",
          width: 120,
        },{
          label: "留资平台",
          prop: "information_platform_name",
          width: 120,
        },{
          label: "留资渠道",
          prop: "appointment_channel_name",
          width: 120,
        },
        // {
        //   label: "工厂",
        //   prop: "factory_name",
        // },
        {
          label: "交付日期",
          prop: "promise_consign_date",
          filter: "date",
          width: 200,
          formats: "yyyy-MM-dd",
        },
      ],
      info: {},
      defaultValue: {
        client_status: ["CLOSE", "COMPLETED"],
      },
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
    getColData() {
      let _this = this;
    },
    query(data) {
      Object.assign(this.tableParam, data);
      this.$refs.table.initData();
    },
    getQueryItems() {
      let c_status = [];
      let _self = this;
      getMap((map) => {
        let cs = map.client_status.filter((item) => true);
        cs.forEach((item) => {
          c_status.push(item);
        });
      });
      this.queryItems = [
        {
          cols: [
            {
              formType: "selectRange",
              prop: "client_status",
              value: this.defaultValue.client_status,
              props: ["client_start_status", "client_end_status"],
              label: "订单状态",
              options: [c_status, c_status],
            },
            {
              formType: "elDatePicker",
              prop: "date",
              format: "yyyy-MM-dd",
              props: ["start_query_date", "end_query_date"],
              label: "订单创建日期",
              type: "daterange",
            },{
              formType: "listSelect",
              label: "店铺",
              prop: "shop_codes",
              config: { popupType: "shop", multiple: true },
              options: [],
            },
            {
              formType: "elDatePicker",
              prop: "date1",
              format: "yyyy-MM-dd",
              props: ["start_contract_time", "end_contract_time"],
              label: "合同下推时间",
              type: "daterange",
            },
            {
              formType: "elDatePicker",
              prop: "date4",
              format: "yyyy-MM-dd",
              props: ["start_in_verify_time", "end_in_verify_time"],
              label: "审图拆单完成时间",
              labelWidth: '120px',
              type: "daterange",
            },
            {
              formType: "elDatePicker",
              prop: "date5",
              format: "yyyy-MM-dd",
              props: ["start_push_down_time", "end_push_down_time"],
              label: "下推采购成功时间",
              labelWidth: '120px',
              type: "daterange",
            },
            {
              formType: "elDatePicker",
              prop: "date2",
              format: "yyyy-MM-dd",
              props: ["start_outbound_time", "end_outbound_time"],
              label: "已出库时间",
              type: "daterange",
            },
            // {
            //   formType: "elDatePicker",
            //   prop: "date3",
            //   format: "yyyy-MM-dd",
            //   props: ["start_query_date", "end_query_date"],
            //   label: "完结时间",
            //   type: "daterange",
            // },
            
            
          ],
        },
      ]
    },
  },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
