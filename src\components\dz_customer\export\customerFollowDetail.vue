<template>
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 查询 -->
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
    </div>
  
    <div style="flex:1;overflow:hidden">
      <my-table
        ref="table"
        tableUrl="/custom-web/api/customSysTrade/customerFollowDetail"
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
      ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from "../components/formCreate/formCreate";
import myTable from "../components/table/table";
import {
  client_status,
  cusTypeopt,
  client_temp,
  getMap,
  c_status,
  custom_sys_budget
} from "../common/clientDictionary";
import {
  life_stage,
  house_type,
  consume_type,
  total_area,
  decoration_stage,
  client_property,
  house_price,
  budget,
} from "../common/measureDictionary";

export default {
  components: {
    formCreate,
    myTable,
  },
  data() {
    let self = this;
    return {
      c_status:'',
      defaultValue:{
        client_status:['WAITING_DISTRIBUTION_DESIGN','SIGNED_CONTRACT_WAITING_VERIFY']
      },
      queryItems: [],
      btns: [
      
      ],
      tableParam: {},
      colData: [],
      info: {},
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
   detail(d) {
      this.$root.eventHandle.$emit("creatTab", {
        name: "订单详情",
        component: () =>
          import("@components/dz_customer/clientInfo/clientInfo.vue"),
        params: {
          customerInfo: d,
          lastTab: this.params.tabName,
        },
      });
    },
    getColData() {
      let _this = this;
      let intention  =[{
          label: '高',
          value: 'high'
        },
        {
          label: '中',
          value: 'middle'
        },
        {
          label: '低',
          value: 'low'
        },]
      this.colData = [
        {
          label: "订单号",
          prop: "client_number",
          width: "180",
          redirectClick(d) {
            _this.detail(d);
          },
        },
        {
          label: "店铺名",
          prop: "shop_name",
          width: "100",
        },
        
        {
          label: "订单分类",
          prop: "client_temp_cn",
          options: client_temp,
          filter: 'select',
          width: "100",
        },
        {
          label: "客户名",
          prop: "client_name",
          width: "100",
        },
        {
          label: "客户手机",
          prop: "client_mobile",
          width: "90",
          format: 'hidePhoneNumber'
        },
        {
          label: "客户地址",
          prop: "address_name",
          width: "100",
        },
        {
          label: "备注",
          prop: "remark",
          width: "100",
        },
        {
          label: "客户意向",
          prop: "intention_cn",
          options: intention,
          filter: 'select',
          width: "100",
        },
        {
          label: "客户预算",
          prop: "budget_cn",
          width: "100",
          options: custom_sys_budget,
          filter: 'select',
        },
        {
          label: "订单状态",
          prop: "client_status_cn",
          width: "100",
          options: client_status,
          filter: 'select',
        },
        {
          label: "订单类型",
          prop: "client_type_cn",
          width: "100",
          options: cusTypeopt,
          filter: 'select',
        },
        {
          label: "留资线索ID",
          prop: "crm_clue_id",
          width: "100",
        },
        {
          label: "导购",
          prop: "shopping_guide_name",
          width: "100",
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: "100",
        },
        {
          label: "建档时间",
          prop: "create_time",
          filter:'date',
          width: "150",
        },
        
        {
          label: "到店时间",
          prop: "arrived_shop_date",
          filter:'date',
          width: "150",
        },
        {
          label: "量尺日期",
          prop: "measure_date",
          filter:'date',
          width: "150",
        },
        {
          label: "合同签订日期",
          prop: "compact_date",
          filter:'date',
          width: "150",
        },
        {
          label: "收款金额",
          prop: "sum_pay_amount",
          width: "90",
        },
        // {
        //   label: "套餐类型",
        //   prop: "combo_type",
        //   width: "120",
        // },
        {
          label: "合同金额",
          prop: "co_agreed_to_gold",
          width: "auto",
        },
        {
          label: "标准售价",
          prop: "sum_retail_price",
          width: "auto",
        },
      ];
    },
    query(data) {
      
      Object.assign(this.tableParam, data);
      console.log(this.tableParam,data)
      this.$refs.table.initData();
    },
    getQueryItems() {
      let c_status = [];
      let _self = this;
      let intention  =[{
          label: '高',
          value: 'high'
        },
        {
          label: '中',
          value: 'middle'
        },
        {
          label: '低',
          value: 'low'
        },]
      getMap((map) => {
        let cs = map.client_status.filter((item) => true);
        // cs = cs.slice(0,cs.length-1)
        cs.forEach((item) => {
          c_status.push(item);
        });
      });
      this.c_status = c_status;
      this.queryItems = [
        {
          cols: [
            
            { formType: "elInput", prop: "client_number", label: "订单编号" },
            {
              formType: "myInput",
              prop: "shop_name",
              label: "店铺名",
              type: "string",
              event: {
                // input(v, col) {
                //   col.value = v.replace(/\D/g, "");
                // },
              },
            },
             {
              formType: "elSelect",
              prop: "client_temp",
              label: "订单分类",
              options: client_temp,
              event: {
                change(v, col, formData, getItem) {
                  
                },
              },
            },
            { formType: "elInput", prop: "client_name", label: "客户名称" },
            {
              formType: "selectRange",
              prop: "client_status",
              value: this.defaultValue.client_status,
              props: ["client_start_status", "client_end_status"],
              label: "订单状态",
              options: [c_status, c_status],
            },
            {
              formType: "elSelect",
              prop: "client_type",
              label: "订单类型",
              options: cusTypeopt,
            },
           

            {formType: 'elSelect', prop: 'intention',options: intention, label: '客户意向'},
            { formType: "elInput", prop: "crm_clue_id", label: "留资线索ID" },
            
            { formType: "elInput", prop: "shopping_guide_name", label: "导购" },
            { formType: "elInput", prop: "designer_name", label: "设计师" },
            {
              formType: "elDatePicker",
              prop: "create_time",
              props: ["start_create_date", "end_create_date"],
              label: "建档日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
            {
              formType: "elDatePicker",
              prop: "arrived_shop_date",
              props: ["start_arrived_shop_date", "end_arrived_shop_date"],
              label: "到店日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
            {
              formType: "elDatePicker",
              prop: "measure_time",
              props: ["start_measure_date", "end_measure_date"],
              label: "量尺时间",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
            {
              formType: "elDatePicker",
              prop: "compact_date",
              props: ["start_compact_date", "end_compact_date"],
              label: "合同签订日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
          ],
        },
      ];
    },
    
  },
  async created() {
        this.getColData();
        this.getQueryItems();

        
    },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
