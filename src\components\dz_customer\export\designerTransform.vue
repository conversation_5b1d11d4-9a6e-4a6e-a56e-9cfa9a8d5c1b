<!-- 设计师待转化报表 -->
<template>
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 查询 -->
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
    </div>
  
    <div style="flex:1;overflow:hidden">
      <my-table
        ref="table"
        tableUrl="/custom-web/api/guideReport/designerTransform"
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
      ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from "../components/formCreate/formCreate";
import myTable from "../components/table/table";
import {
  client_status,
  cusTypeopt,
  client_temp,
  getMap,
} from "../common/clientDictionary";
import {
  life_stage,
  house_type,
  consume_type,
  total_area,
  decoration_stage,
  client_property,
  house_price,
  budget,
} from "../common/measureDictionary";
export default {
  components: {
    formCreate,
    myTable,
  },
  data() {
    let self = this;
    return {
      
      defaultValue:{
        client_status:['WAITING_DISTRIBUTION_DESIGN','SIGNED_CONTRACT_WAITING_VERIFY']
      },
      queryItems: [],
      btns: [
      
      ],
      designerList:[],
      tableParam: {},
      colData: [],
      info: {},
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
   
    getColData() {
      let _this = this;
      this.colData = [
		    {
          label: "店铺名",
          prop: "shop_name",
        },
		    {
          label: "设计师",
          prop: "designer",
        },
        {
          label: "未量尺的客户",
          prop: "notMeasure",
        },
        {
          label: "已量尺未收款的客户",
          prop: "measuredNotPay",
        },
        
        
        
        {
          label: "已量尺未签合同的客户",
          prop: "measuredNotSign",
        },
		    { 
          label: "已签合同未下单",
          prop: "signedNotDesign",
        },
      ];
    },
    query(data) {
      
      Object.assign(this.tableParam, data);
      this.$refs.table.initData();
    },
    getDesignerList(){
        //获取设计师列表
        var _this = this
        var data = {}
        this.ajax.postStream('/custom-web/api/customClient/getDesignerListByShop',data,function(data){
          data = data.body
          if(data.result){
           data.content.forEach(item=>{
            _this.designerList.push({
              label: item.designer_name,
              value: item.designer_number
            })
            _this.getQueryItems();
          })
          }
        },
        function(data){
          console.log('返回失败的数据')
        })

      },
   
    getQueryItems() {
      let self = this;
      this.queryItems = [
        {
          cols: [
            
            {
              formType: "myInput",
              prop: "shop_name",
              label: "店铺名",
              type: "string",
              event: {
               
              },
            },
            {
              formType: "elSelect",
              prop: "client_type",
              label: "订单类型",
              options: cusTypeopt,
            },
            {
              formType: "elSelect",
              prop: "designer_number",
              label: "设计师",
              options: self.designerList,
            },
          ],
        },
      ];
    },
    
  },
  async created() {
        this.getColData();
        // this.getQueryItems();
        this.getDesignerList();
    },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
