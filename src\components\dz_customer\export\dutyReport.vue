<template>
  <!-- 导出责任单报表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 查询 -->
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
    </div>
    <!-- 操作栏 -->

    <!-- 表格 -->
        
    <!-- tableUrl="/custom-web/api/guideReport/getReviewGoodsList" -->
    <div style="flex:1;overflow:hidden">
      <my-table
        ref="table"
        tableUrl="/custom-web/api/customAnalysisRelation/list"
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
      ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from "../components/formCreate/formCreate";
import myTable from "../components/table/table";
import { difficulty } from "../common/dictionary/static";
import { exportZrd } from "../common/api";
import Export from "../common/mixins/export";
import fn from '@common/Fn.js'

export default {
  components: {
    formCreate,
    myTable,
  },
  mixins: [Export],
  data() {
    return {
      queryItems: [],
      btns: [
        {
          type: "success",
          txt: "导出报表",
          loading: false,
          click: () => {
            this.btns[0].loading = true;
            exportZrd(this.tableParam).then((res) => {
              this.btns[0].loading = false;
            });
          },
        },
      ],
      tableParam: {},
      colData: [],
      info: {},
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
    viewMergedOrder(merge_trade_id){
        /**
         * @description: 合并订单详情
         * @param {*}
         * @return {*}
         */
        var params = {};
        params.merge_trade_id = merge_trade_id;
        params.mergeTradeIdList = []
        params.ifOrder = true
        // 呼叫按钮来源
        params.callBtnfromType = 'sys_order'
        this.$root.eventHandle.$emit('creatTab',{
            name:"合并订单详情",
            params,
            component: () => import('@components/order/merge.vue')
        });
    },
    async getColData() {
      let _this = this;
      let colData = [
        {
          label: "责任分析子单",
          prop: "analysis_sub_no",
          width: "150",
          redirectClick(d) {
            _this.$root.eventHandle.$emit('creatTab',{
              name: '责任分析单详情',
              params: {
                id: d.analysis_id,
                sub_bill_no: d.analysis_sub_no,
                orderList: []//JSON.parse(JSON.stringify(this._getOrderList.orderList)),
              },
              component:()=> import('../modules/afterSales/detailOfDuty_2')
            })  
          },
        },
        {
          label: "责任单创建时间",
          prop: "analysis_sub_create_time",
          filter: "date",
          width: 130,
        },
        {
          label: "买家昵称",
          prop: "client_name",
          width: 120,
        },
        {
          label: "订单类型",
          prop: "client_type_name",
          width: 120,
        },
        {
          label: "经销商名称",
          prop: "shop_name",
          width: 120,
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: 120,
        },
        {
          label: "子单来源单号",
          prop: "original_bill_no",
          width: 150,
          redirectClick: (d) => {
             _this.$root.eventHandle.$emit('creatTab',
                    {
                        name:'补件单详情',
                        params:{
                            id:d.original_bill_id,
                            list: [],
                            typeNumber: 1 //typeNumber=1为文员列表字段，2为业务员列表字段
                        },
                        component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')
                    }
            )
          },
        },
        {
          label: "合并单号",
          prop: "merge_trade_no",
          width: 200,
          redirectClick: (row) => {
            _this.viewMergedOrder(row.merge_trade_id, row.merge_trade_no)
          },
        },
        {
          label: "锁定人",
          prop: "lock_name",
          width: 120,
        },
        {
          label: "责任类型",
          prop: "liability_type",
          width: 100,
        },
        {
          label: "责任问题",
          prop: "liability_question_name",
          width: 200,
        },
        {
          label: "责任人",
          prop: "liability_person_name",
          width: 200,
        },
        {
          label: "备注",
          prop: "liability_person_remark",
          width: 100,
        },
        {
          label: "责任金额",
          prop: "liability_amount",
          width: 100,
        },
        {
          label: "处理金额",
          prop: "handle_amount",
          width: 100,
        },
        {
          label: "定制补件单号",
          prop: "client_number",
          width: 150,
          redirectClick: (row) => {
            _this.$root.eventHandle.$emit('creatTab', {
                name: '补单详情',
                component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
                params: {
                    client_number: row.client_number,
                }
            })
        },
        },
        {
          label: "充值单号",
          prop: "dealer_funds_manage_record_no",
          width: 200,
          redirectClick: (row) => {
            _this.$root.eventHandle.$emit('creatTab', {
                name: '货款充值详情',
                params: { id: row.dealer_funds_manage_record_id },
                component:()=> import('@components/dealer/recharge_of_goods_detail')
            })
          },
        },
      ];
      let status = await this.checkUserType()
      if(status){
        let i = colData.findIndex(item=>item.prop === 'liability_person_name')
        colData.splice(i,1)
      }
      this.colData = colData
    },
    query(data) {
      console.log(data);
      Object.assign(this.tableParam, data);
      this.$refs.table.initData();
    },
    getQueryItems() {
      this.queryItems = [
        {
          cols: [
            {
              formType: "listSelect",
              label: "店铺",
              prop: "shop_code",
              config: { popupType: "shop", multiple: true },
              options: [],
            },
            {
                formType: 'elInput',
                prop: 'lock_name',
                label: '锁定人'
            },
            {
              formType: "elDatePicker",
              prop: "date",
              format: "yyyy-MM-dd",
              props: ["create_start_date", "create_end_date"],
              label: "责任单创建日期",
              labelWidth: '120px',
              type: "daterange",
            },
            {
              formType: "elDatePicker",
              prop: "date2",
              format: "yyyy-MM-dd",
              props: ["sub_start_date", "sub_end_date"],
              label: "责任单确认日期",
              labelWidth: '120px',
              type: "daterange",
            },{
                formType: 'elInput',
                prop: 'original_bill_no',
                label: '子单来源单号'
            },{
                formType: 'elInput',
                prop: 'client_number',
                label: '定制补件单号'
            },{
                formType: 'elInput',
                prop: 'analysis_sub_no',
                label: '责任分析子单'
            },
            {
                formType: 'elInput',
                prop: 'dealer_funds_manage_record_no',
                label: '充值单号'
            },
          ],
        },
      ];
    },
    checkUserType (){
      return new Promise((resolve)=>{
        this.ajax.get('/user-web/api/userPerson/getUserPerson/' + fn.getUserInfo('personId'), res => {
          resolve(res.body.content.type === 'PURCHASE_DEALER')
        })
      })
		},
  },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
