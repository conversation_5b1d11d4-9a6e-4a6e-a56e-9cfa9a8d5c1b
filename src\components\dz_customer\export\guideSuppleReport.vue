<template>
  <!-- 导出补单商品信息报表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 查询 -->
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
    </div>
    <!-- 操作栏 -->

    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
      <my-table
        ref="table"
        tableUrl="/custom-web/api/guideReport/getBOrderList"
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
      ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from "../components/formCreate/formCreate";
import myTable from "../components/table/table";
import { difficulty } from "../common/dictionary/static";
import { exportBd } from "../common/api";
import { goods_status } from "../common/goodsStatusDictionary";
import Export from "../common/mixins/export";
export default {
  components: {
    formCreate,
    myTable,
  },
  mixins: [Export],
  data() {
    return {
      queryItems: [],
      btns: [
        {
          type: "success",
          txt: "导出报表",
          loading: false,
          click: () => {
            this.btns[0].loading = true;
            exportBd(this.tableParam).then((res) => {
              this.btns[0].loading = false;
            });
          },
        },
      ],
      tableParam: {},
      colData: [],
      info: {},
      defaultValue: {
        goods_satus: ["IN_DESIGN", "COMPLETED"],
      },
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
    getColData() {
      let _this = this;
      this.colData = [
        {
          label: "描述",
          prop: "supply_reason",
          width: 100,
        },

        {
          label: "客户名",
          prop: "client_name",
          width: 120,
        },
        {
          label: "原单号",
          prop: "original_client_number",
          width: 88,
          redirectClick: (row) => {
            let data = JSON.parse(JSON.stringify(row))
            data.client_number =  row.original_client_number,
            _this.$root.eventHandle.$emit("creatTab", {
              name: "订单详情",
              component: () =>
                import("@components/dz_customer/clientInfo/clientInfo.vue"),
              params: {
                customerInfo: data,
                lastTab: _this.params.tabName,
              },
            });
          },
        },
        {
          label: "补单号",
          prop: "client_number",
          width: 110,
          redirectClick: (row) => {
            _this.$root.eventHandle.$emit("creatTab", {
              name: "补单详情",
              component: () =>
                import("@components/dz_customer/supplement/supplyInfo.vue"),
              params: {
                customerInfo: row,
                client_number:row.client_number,
                lastTab: _this.params.tabName,
              },
            });
          },
        },
        {
          label: "商品状态",
          prop: "value",
          width: 100,
        },
        {
          label: "审图审价人",
          prop: "drawing_review",
          width: 100,
        },
        {
          label: "拆单人员",
          prop: "split_order",
          width: 100,
        },
        {
          label: "拆审人员",
          prop: "review",
          width: 100,
        },
        {
          label: "标准售价",
          prop: "retail_price",
          width: 100,
        },
        {
          label: "林氏采购价",
          prop: "purchase_price",
          width: 100,
        },
        {
          label: "商品编码",
          prop: "goods_id",
          width: "130",
          redirectClick(d) {
             let data = JSON.parse(JSON.stringify(d))
            data.client_number =  d.original_client_number,
            _this.$root.eventHandle.$emit("creatTab", {
              name: "商品详情",
              component: () =>
                import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
              params: {
                goodsInfo: data,
                trade_type: 'SUPPLY',
              },
            });
          },
        },
        {
          label: "店铺名",
          prop: "shop_name",
          width: 120,
        },
        {
          label: "店铺编码",
          prop: "shop_code",
          width: 120,
        },
        {
          label: "工厂",
          prop: "factory_name",
          width: 120,
        },
        {
          label: "创建日期",
          prop: "create_time",
          filter: "date",
          width: 130,
        },
        {
          label: "提交时间",
          prop: "supply_submit_time",
          filter: "date",
          width: 130,
        },
        {
          label: "补件申请单号",
          prop: "after_ticket_no",
          width: 100,
        },
        {
          label: "生成采购单时间",
          prop: "purchase_push_time",
          filter: "date",
          width: 100,
        },
        {
          label: "采购单号",
          prop: "purchase_no",
          width: 100,
        },
        {
          label: "生成出库单时间",
          prop: "outbound_push_time",
          filter: "date",
          width: 100,
        },
        {
          label: "难度等级",
          prop: "difficulty",
          width: 100,
        },
        {
          label: "运输单号",
          prop: "transport_no",
          width: 100,
        },
        {
          label: "下推工厂日期",
          prop: "push_down_time",
          filter: "date",
          width: 130,
        },
        {
          label: "交付日期",
          prop: "promise_consign_date",
          filter: "date",
          formats: "yyyy-MM-dd",
        },
      ];
    },
    query(data) {
      Object.assign(this.tableParam, data);
      this.$refs.table.initData();
    },
    getQueryItems() {
      this.queryItems = [
        {
          cols: [
            {
              formType: "listSelect",
              label: "店铺",
              prop: "shop_codes",
              config: { popupType: "shop", multiple: true },
              options: [],
            },
            {
              formType: "selectRange",
              prop: "goods_satus",
              value: this.defaultValue.goods_satus,
              props: ["goods_start_status", "goods_end_status"],
              label: "商品状态",
              options: [goods_status, goods_status],
            },
            {
              formType: "elDatePicker",
              prop: "date",
              format: "yyyy-MM-dd",
              props: ["start_query_date", "end_query_date"],
              label: "订单创建日期",
              type: "daterange",
            }
          ],
        },
      ];
    },
  },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
