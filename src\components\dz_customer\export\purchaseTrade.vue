<!-- 采购订单报表 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='cols' 
		:searchPage='search.page_name' 
		:pageTotal='count' 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@search-click='searchClick' 
		ref="xptList"
	>
	
	</xpt-list>	
</template>
<script>
export default {
	 
    props:['params'],
	data() {
		let self = this;
		return {
			list: [],
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: self.getList
			},{
                    type: 'primary',
                    txt: '查看导出结果',
                    click() {
                        self.downloadExcel()
                    }
                },{
				type: 'primary',
				txt: '导出',
				click: self.export
			}],
			cols: [{
				label: '订单号',
				prop: 'client_number',
			}, {
				label: '客户名',
				prop: 'name',
				width: 180
			}, {
				label: '收货人',
				prop: 'receiver_name',
			}, {
				label: '物料编码',
				prop: 'material_number',
			}, {
				label: '商品名称',
				prop: 'message',
			}, {
				label: '标准售价 ',
				prop: 'retail_price',
			}, {
				label: '实际价格',
				prop: 'act_price',
			}, {
				label: '林氏采购价',
				prop: 'purchase_price',
			}, {
				label: 'SCM采购单号',
				prop: 'po_number',
			}, {
				label: '店铺名',
				prop: 'shop_name',
			}, {
				label: '订单类型',
				prop: 'purchase_trade_type',
			}, {
				label: '下推工厂',
				prop: 'factory_name',
			}, {
				label: '下推工厂日期',
				prop: 'scm_time',
        format:'dataFormat1'
			}, {
				label: '订单状态',
				prop: 'client_status',
			}],
			search: {
				page:{
					length: this.pageSize,
					pageNo:1
				},
				page_name: 'custom_purchase_trade',
				where: [],
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
		//查看导入结果
		downloadExcel() {
		this.$root.eventHandle.$emit("alert", {
			params: {
			data: {
					type: "EXCEL_TYPE_CUSTOM_PURCHASE_TRADE_EXPORT",
				},
			url: "/reports-web/api/reports/afterSale/findMyReportList",
			showDownload:true
			},
			component: () => import("@components/common/eximport"),
			style: "width:800px;height:400px",
			title: "导入结果",
		});
		},
		pageSizeChange(ps) {
			this.search.page.length = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page.pageNo = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			this.search.where = obj;
			this.getList(resolve);
		},
		
		selectionChange(obj) {
			this.selectData = obj;
		},
    export(){
			this.ajax.postStream("/custom-web/api/guideReport/purchaseTrade/export", this.search, res => {
				if(res.body.result) {
					this.$message.success(res.body.msg)
					// this.list = res.body.content.list;
					// this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
		},
		getList(resolve) {
			this.ajax.postStream("/custom-web/api/guideReport/purchaseTrade/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve()
			});
		},
	
	},
	mounted() {
		// this.getList()
	}
}
</script>
