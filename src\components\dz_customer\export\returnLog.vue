<!-- 退单报表 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='cols' 
		:searchPage='search.page_name' 
		:pageTotal='count' 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@search-click='searchClick' 
		ref="xptList"
	>
	
	</xpt-list>	
</template>
<script>
export default {
	 
    props:['params'],
	data() {
		let self = this;
		return {
			list: [],
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: self.getList
			},{
                    type: 'primary',
                    txt: '查看导出结果',
                    click() {
                        self.downloadExcel()
                    }
                },{
				type: 'primary',
				txt: '导出',
				click: self.export
			}],
			cols: [{
				label: '订单号',
				prop: 'client_number',
			}, {
				label: '客户名',
				prop: 'name',
				width: 180
			}, {
				label: '商品编码',
				prop: 'goods_id',
			}, {
				label: '店铺名',
				prop: 'shop_name',
			}, {
				label: '设计师',
				prop: 'designer_name',
			}, {
				label: '合同日期 ',
				prop: 'compact_date',
        		format:'dataFormat1'

			}, {
          label: "商品类型",
            prop: "type_code_name",
            width: "90"
          },{
				label: '驳回类型',
				prop: 'return_type',
			}, {
				label: '驳回时间',
				prop: 'return_time',
       			format:'dataFormat1'
			}, {
				label: '分类标签',
				prop: 'classification_name',
			},{
				label: '驳回原因',
				prop: 'return_reason',
			}, {
				label: '设计师最后一次提交时间',
				prop: 'design_last_commit_time',
        		format:'dataFormat1'
			},],
			search: {
				page:{
					length: this.pageSize,
					pageNo:1
				},
				page_name: 'custom_return_log',
				where: [],
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
		//查看导入结果
		downloadExcel() {
			this.$root.eventHandle.$emit("alert", {
			params: {
			data: {
					type: "EXCEL_TYPE_CUSTOM_RETURN_LOG_EXPORT",
				},
			url: "/reports-web/api/reports/afterSale/findMyReportList",
			showDownload:true
			},
			component: () => import("@components/common/eximport"),
			style: "width:800px;height:400px",
			title: "导出结果",
		});
		// this.$root.eventHandle.$emit("alert", {
		// 	params: {
		// 	obj: {
		// 		type: "EXCEL_TYPE_CUSTOM_RETURN_LOG_EXPORT",
		// 	},
		// 	url: "/reports-web/api/reports/afterSale/findMyReportList",
		// 	},
		// 	component: () => import("@components/dealer/import_result.vue"),
		// 	style: "width:800px;height:400px",
		// 	title: "导入结果",
		// });
		},
		pageSizeChange(ps) {
			this.search.page.length = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page.pageNo = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			this.search.where = obj;
			this.getList(resolve);
		},
		
		selectionChange(obj) {
			this.selectData = obj;
		},
		export(){
			this.ajax.postStream("/custom-web/api/guideReport/returnLog/export", this.search, res => {
				if(res.body.result) {
					this.$message.success(res.body.msg)
					// this.list = res.body.content.list;
					// this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
		},
		getList(resolve) {
			this.ajax.postStream("/custom-web/api/guideReport/returnLog/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve()
			});
		},
	
	},
	mounted() {
		// this.getList()
	}
}
</script>
