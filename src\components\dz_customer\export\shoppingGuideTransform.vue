<template>
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 查询 -->
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
    </div>
  
    <div style="flex:1;overflow:hidden">
      <my-table
        ref="table"
        tableUrl="/custom-web/api/guideReport/shoppingGuideTransform"
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
      ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from "../components/formCreate/formCreate";
import myTable from "../components/table/table";
import {
  client_status,
  cusTypeopt,
  client_temp,
  getMap,
} from "../common/clientDictionary";
import {
  life_stage,
  house_type,
  consume_type,
  total_area,
  decoration_stage,
  client_property,
  house_price,
  budget,
} from "../common/measureDictionary";
export default {
  components: {
    formCreate,
    myTable,
  },
  data() {
    let self = this;
    return {
      
      queryItems: [],
      btns: [
      
      ],
      shopGuideList:[],
      tableParam: {},
      colData: [],
      info: {},
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
   
    getColData() {
      let _this = this;
      this.colData = [
		{
          label: "店铺名",
          prop: "shop_name",
        },
		{
          label: "导购",
          prop: "shopping_guide",
        },
        {
          label: "未量尺的客户",
          prop: "notMeasure",
        },
        
       
        {
          label: "已量尺未收款的客户",
          prop: "measuredNotPay",
        },
        
        {
          label: "已量尺未签合同的客户",
          prop: "measuredNotSign",
        },
      ];
    },
    query(data) {
      
      Object.assign(this.tableParam, data);
      this.$refs.table.initData();
    },
    getShopGuideList(){
          //获取设计师列表
      var _this = this
      var data = {}
      this.ajax.postStream('/custom-web/api/customClient/getUserListByShop',{role:'DZ_DG'},function(res){
        data = res.body
        if(data.result){
          _this.shopGuideList = data.content 
          _this.shopGuideList = [];
          data.content.forEach(item=>{
            _this.shopGuideList.push({
              label: item.designer_name,
              value: item.designer_number
            })
            _this.getQueryItems();
          })
          
        }
      })

    },
    getQueryItems() {
      let self = this;
      this.queryItems = [
        {
          cols: [
            
            {
              formType: "myInput",
              prop: "shop_name",
              label: "店铺名",
              type: "string",
              event: {
               
              },
            },
            {
              formType: "elSelect",
              prop: "client_type",
              label: "订单类型",
              options: cusTypeopt,
            },
            {
              formType: "elSelect",
              prop: "shopping_guide_number",
              label: "导购",
              options: self.shopGuideList,
            },
          ],
        },
      ];
    },
    
  },
  async created() {
        this.getColData();
        this.getShopGuideList();
    },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
