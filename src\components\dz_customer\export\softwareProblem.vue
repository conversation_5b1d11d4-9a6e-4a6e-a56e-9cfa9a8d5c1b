<!-- 退单报表 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='cols' 
		:searchPage='search.page_name' 
		:pageTotal='count' 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@search-click='searchClick' 
		ref="xptList"
	>
	
	</xpt-list>	
</template>
<script>
export default {
	 
    props:['params'],
	data() {
		let self = this;
		return {
			list: [],
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: self.getList
			},],
			cols: [{
				label: '订单号',
				prop: 'client_number',
			}, {
				label: '商品编码',
				prop: 'goods_id',
			}, {
				label: '客户名',
				prop: 'client_name',
				width: 180
			},{
				label: '问题提交内容',
				prop: 'problem_submit_content',
			}, {
				label: '问题解决内容',
				prop: 'problem_solve_content',
			}, {
				label: '问题提交时间',
				prop: 'problem_submit_time',
          format:'dataFormat1'
			}, {
				label: '问题处理时间',
				prop: 'problem_solve_time',
       			format:'dataFormat1'
			}, ],
			search: {
				page:{
					length: this.pageSize,
					pageNo:1
				},
				page_name: 'custom_software_problem_record',
				where: [],
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
	
		pageSizeChange(ps) {
			this.search.page.length = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page.pageNo = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			this.search.where = obj;
			this.getList(resolve);
		},
		
		selectionChange(obj) {
			this.selectData = obj;
		},
	
		getList(resolve) {
			this.ajax.postStream("/custom-web/api/guideReport/softwareProblem/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
			});
		},
	
	},
	mounted() {
		// this.getList()
	}
}
</script>
