<template>
  <!-- 导出拆单报表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 查询 -->
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
    </div>
    <!-- 操作栏 -->

    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
      <my-table
        ref="table"
        tableUrl="/custom-web/api/guideReport/getReviewGoodsList"
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
      ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from "../components/formCreate/formCreate";
import myTable from "../components/table/table";
import { difficulty } from "../common/dictionary/static";
import { exportSj } from "../common/api";
import Export from "../common/mixins/export";
export default {
  components: {
    formCreate,
    myTable,
  },
  mixins: [Export],
  data() {
    return {
      queryItems: [],
      btns: [
        {
          type: "success",
          txt: "导出报表",
          loading: false,
          click: () => {
            this.btns[0].loading = true;
            exportSj(this.tableParam).then((res) => {
              this.btns[0].loading = false;
            });
          },
        },
      ],
      tableParam: {},
      colData: [],
      info: {},
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
    getColData() {
      let _this = this;
      this.colData = [
        {
          label: "商品号",
          prop: "goods_id",
          width: "130",
          redirectClick(d) {
            _this.$root.eventHandle.$emit("creatTab", {
              name: "商品详情",
              component: () =>
                import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
              params: {
                goodsInfo: d,
              },
            });
          },
        },
        {
          label: "店名",
          prop: "shop_name",
          width: 120,
        },
        {
          label: "订单号",
          prop: "client_number",
          width: 88,
          redirectClick: (row) => {
            _this.$root.eventHandle.$emit("creatTab", {
              name: "订单详情",
              component: () =>
                import("@components/dz_customer/clientInfo/clientInfo.vue"),
              params: {
                customerInfo: row,
                lastTab: _this.params.tabName,
              },
            });
          },
        },
        {
          label: "客户名",
          prop: "client_name",
          width: 120,
        },
        {
          label: "经销采购价",
          prop: "deal_stock_rpice",
          width: 100,
        },
        {
          label: "标准售价",
          prop: "guide_price",
          width: 100,
        },
        {
          label: "林氏采购价",
          prop: "ls_stock_rpice",
          width: 100,
        },
        {
          label: "审图审价人",
          prop: "review_name",
          width: 100,
        },
        {
          label: "锁单日期",
          prop: "locking_time",
          filter: "date",
          width: 130,
        },
        {
          label: "约定交付日期",
          prop: "promise_consign_date",
          filter: "date",
          width: 130,
        },
        {
          label: "审图审价日期",
          prop: "reviewed_time",
          filter: "date",
          width: 130,
        },
        {
          label: "创建日期",
          prop: "create_time",
          filter: "date",
        },
      ];
    },
    query(data) {
      Object.assign(this.tableParam, data);
      this.$refs.table.initData();
    },
    getQueryItems() {
      this.queryItems = [
        {
          cols: [
            {
              formType: "listSelect",
              label: "店铺",
              prop: "shop_codes",
              config: { popupType: "shop", multiple: true },
              span: 8,
              options: [],
            },
            {
              formType: "elSelect",
              label: "难度",
              prop: "difficulty",
              span: 8,
              options: difficulty,
            },
            {
              formType: "elDatePicker",
              prop: "date",
              format: "yyyy-MM-dd",
              props: ["start_query_date", "end_query_date"],
              label: "订单创建日期",
              type: "daterange",
            },
          ],
        },
      ];
    },
  },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
