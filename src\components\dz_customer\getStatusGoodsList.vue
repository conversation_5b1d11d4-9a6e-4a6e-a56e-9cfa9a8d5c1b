<template>
<!-- 支付记录列表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
      <!-- 查询 -->
    <div class="search-content">
      <form-create 
        :formData="queryItems" 
        @save="query"
        @request="request"
        savetitle="查询"
        ></form-create>
    </div>
    <!-- 操作栏 -->

    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
        <my-table 
        ref="table"
        tableUrl='/custom-web/api/customGoods/getStatusGoodsList'
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        selection="checkbox"
        :btns="btns"
        :isInit="false"
        @checkboxChange="selectionChange"
        ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from './components/formCreate/formCreate'
import myTable from './components/table/table'
import {sync_status, getMap} from './common/tollDictionary'
import {getShopInfo, getRole} from './common/api'
import Vue from 'vue'
export default {
    components: {
        formCreate,
        myTable
    },
    data() {
        let self = this;
        return {
            orderselect:[],
            queryItems: [],
            btns: Object.freeze([{
				type: 'success',
				txt: '确认提交',
				loading: false,
				click: () => {
                    self.submit()
					
				}
            }
            ]),
            tableParam:{"goods_status_code":85},
            colData: [],
            info: {},
            isJxbc:false
        }
    },
    props:{
        params: {
            type: Object
        }
    },
    async created() {   
        this.role = await getRole()
        this.getShopInfo()
        this.setShopDefault();
        if(this.info.shopCode == 'LS-JXBC'){
            this.isJxbc =true;
        }
    },
    mounted(){
        
        getMap(map => {
            this.getColData()
            this.$refs.table.initData()
        })
        this.$root.eventHandle.$on('refreshTollList', this.$refs.table.refresh)

    },
    
    methods: {
        selectionChange(data){
            this.orderselect = data;
        },
        submit(){
        let self = this;
        if(!self.orderselect.length){
            self.$message.error('请选择订单')
            return;
        }
         this.$root.eventHandle.$emit('alert', {
                        component: () => import('@components/dz_customer/commitGoods.vue'),
                        style: 'width:600px;height:400px',
                        title: '提交',
                        params: {
                        //   goodsList:body.content.goodsList ,
                            callback(data){
                                self.ajax.postStream('/custom-web/api/customGoods/softwareProblemSubmit?permissionCode=CUSTOM_SOFTWARE_PROBLEM_SOLVE',
                                {
                                    changeContent:data.content,
                                    remark:data.remark,
                                    // message:client_number,
                                    goodsList:self.orderselect.map(item=>{
                                        return{
                                            custom_software_problem_record_id:item.custom_software_problem_record_id,
                                            custom_goods_id:item.custom_goods_id,
                                            client_number:item.client_number,
                                            goods_id:item.goods_id,
                                        }
                                    }),
                                },(res) =>{
                                    let body = res.body
                                    if(body.result){
                                        // resolve(body.content)
                                        self.$message.success(body.msg);
                                        self.$root.eventHandle.$emit("removeAlert", self.params.alertId);
                                    } else {
                                        self.$message.error(body.msg);
                                    }
                            })
                            }
                        },
                        
                        
                      })
    },
        setShopDefault() {
            this.setShopDefault.where = [];
            this.ajax.postStream(
                "/user-web/api/sql/listFields",
                { page: "cloud_shop_v2" },
                (res) => {
                    if (res.body.result) {
                        (res.body.content.fields || []).forEach((o) => {
                            if (o.comment === "店铺分类") {
                                this.setShopDefault.where.push({
                                    field: o.field,
                                    table: o.table,
                                    value: "HARD_DECORATION",
                                    operator: "=",
                                    condition: "AND",
                                    listWhere: [],
                                });
                            } else if (o.comment === "主营类目") {
                                this.setShopDefault.where.push({
                                    field: o.field,
                                    table: o.table,
                                    value: "DZ",
                                    operator: "=",
                                    condition: "AND",
                                    listWhere: [],
                                });
                            }
                        });
                    }
                }
            );
        },
        getColData(){
            this.colData = [
                {
					label: '商品号',
					prop: 'goods_id',
					width: 133
                },
                {
					label: '商品名称',
					prop: 'message',
					width: 103
                },
                {
					label: '三维家商品编号',
					prop: 'swj_goods_no',
					width: 127,
                },
                {
					label: '订单号',
					prop: 'client_number',
					width: 180,
					redirectClick:(row) => {
						this.$root.eventHandle.$emit('creatTab', {
                            name: '订单详情',
                            component: () => import('@components/dz_customer/clientInfo/clientInfo.vue'),
                            params: {
                                customerInfo: row,
                                lastTab: this.params.tabName
                            }
                        })
					}
                },
                 {
					label: '客户名',
					prop: 'client_name',
					width: 103
                },
                
				{
					label: '建档日期',
                    prop: 'create_time',
                    filter: 'date',
                    formats: 'yyyy-MM-dd',
					width: 116
                },
                 {
					label: '合同提交日期',
                    prop: 'contract_time',
                    
                    filter: 'date',
                    formats: 'yyyy-MM-dd',
					width: 86
                },
                {
					label: '问题提交时间',
					prop: 'problem_submit_time',
					width: 107,
                    filter: 'date',
                    formats: 'yyyy-MM-dd',
                },
                 {
					label: '问题描述',
                    prop: 'problem_submit_content',
					width: 116
                },
            ]
        },
        async getShopInfo() {
            
            this.getQueryItems()
        },
        request(){
            this.$root.eventHandle.$emit('refreshTollList')
            this.$root.eventHandle.$emit('removeTab',this.params.tabName)
        },
        query(data) {
            let param = JSON.parse(JSON.stringify(data));
            console.log(param,this.info);
            // param.shop_code = this.info.shopCode;
            Object.assign(this.tableParam, param)
            this.$refs.table.initData()
        },
        getQueryItems(){
            let self = this;
            this.queryItems = [
                {
                    cols: [
              
                         {formType: 'elInput', label: '订单编号', prop:'client_number', span: 8},
                        {formType: 'elInput', label: '客户名称',  prop: 'client_name', span: 8},
                        {formType: 'elInput', label: '商品号', prop:'goods_id', span: 8},
                    ]
                }
            ]
        }
    }
}
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
