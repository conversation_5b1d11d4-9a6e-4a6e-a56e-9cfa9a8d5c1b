<template>
<!-- 原始订单分类 -->
  <div style="height: 99%;">
    <verifcommon
    ref="list"
    :params = params
    :btns="btns"
    :tools="tools"
    url="/custom-web/api/classification/getClassificationGoodsList"
    :defaultValue="defaultValue"
    :trade_type="trade_type"
    verType="fl"
    ></verifcommon>
  </div>
</template>

<script>
import verifcommon from './verifcommon'
import { getRole } from './common/api'

export default {
  components: {
    verifcommon
  },
  props: {
    params: {
      type: Object
    },
    trade_type: {
      type: String,
      default: 'ORIGINAL'
    }
  },
  data() {
    return {
      defaultValue: {
        client_satus: ['WAITING_CLASSIFICATION','IN_CLASSIFICATION'],
        goods_satus: ['WAITING_CLASSIFICATION','IN_AUDIT']
      },
      tools:[],
      role:["other"],
      btns: []
    }
  },
  methods:{
    refresh() {
      this.$refs.list._getDataList()
    },
    lock(d) {
        let self = this
        let data = {}
        const { custom_goods_id, client_number, goods_id } = d
        data = {
          custom_goods_id, client_number, goods_id
        } 
        !this.request && (this.request={})
        if(this.request[custom_goods_id]) {
          this.$message({
            type: 'warning',
            message: '请等待上一次请求结束'
          })
          return
        }
        this.request[custom_goods_id] = true
        this.ajax.postStream('/custom-web/api/classification/lockingGoods', data, (res) => {
          this.request = false
          res = res.body
          this.$message({
            message: res.msg,
            type:res.result?'success':'error'
          })
          if(res.result) {
            self.$refs.list._getDataList()
          } else {
            this.request[custom_goods_id] = false
          }
        })
      },
  },
  async mounted() {
    var self = this
    this.role = await getRole()
    this.tools = [
      {
        type: 'primary',
        txt: '锁定',
        click(d) {
          self.lock(d)
        },
        show(d) {
          let goods_status = ['WAITING_CLASSIFICATION',"IN_CLASSIFICATION", "WAITING_AUDIT", "IN_AUDIT"]
          if(goods_status.includes(d.goods_status_value)) {
            if(d.is_locking === null ){
              return true
            } else {
                return false
              }
          } else{
            return false
          }
        }
      },
      {
        type: 'warning',
        txt: '分类',
        click: (d) => {
          this.$root.eventHandle.$emit('creatTab', {
            name: '分类',
            component: () => import('@components/dz_customer/alert/verifypic.vue'),
            params: {
              goodsInfo:{goods_id:d.goods_id, custom_goods_id:d.custom_goods_id},
              verType: "fl",
              trade_type: this.trade_type
            }
          })
        },
        show(d) {
          let goods_status = ['WAITING_CLASSIFICATION',"IN_CLASSIFICATION", "WAITING_AUDIT", "IN_AUDIT"]
          if(goods_status.includes(d.goods_status_value)) {
            return d.is_review === 'true' 
          } else{
            return false
          }
        }
      }
    ],
    this.$root.eventHandle.$on('goodsCategories', this.refresh)
  },
  beforeDestroy() {
    this.$root.eventHandle.$off('goodsCategories', this.refresh)
  }

}
</script>

<style>

</style>
