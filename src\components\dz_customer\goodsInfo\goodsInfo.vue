<template>
<!-- 商品详情 -->
    <div class="info" 
        v-loading="loading"
        element-loading-text="拼命加载中"
        >
        <div class="info-container">
            <info
                v-for="info in infos"
                :key="info.key"
                :info="info"
                :aboutNumber="aboutNumber"
            />
        </div>
        
    </div>
</template>
<script>
import info from '../clientInfo/info'
import {downloadZip, getFileList} from '../common/api'
import {getMap} from '../common/goodsStatusDictionary'
import infoMix from '../common/mixins/info' 
import { imageView2 } from '../alert/alert'

export default {
    mixins: [infoMix],
    props: ['params'],
    components: {info},
    data() {
        return {
            loading: true,
            infos: [],
            aboutNumber:'',//单据号
        }
    },
    computed: {
        trade_type() {
            return this.params.trade_type || 'ORIGINAL'
        }
    },
    async mounted() {
       this.currentKey = new Date().getTime();
       this.refresh()
    },
    beforeDestroy(){
        
    },
    methods: {
         async getProblemInfo() {
            let self = this
            const data = await this.getSection(
                '/custom-web/api/customGoodsProblemDetail/list',
                {goods_id:this.params.goodsInfo.goods_id},
                '问题描述',
                value => {
                    return  []
                },
                (value) => {
                    let goodsList = []
                    console.log(value,111111111111);
                  value.forEach(item=>{
                    item.after_ticket_no = '查看附件'
                    })
                        
                    return [
                        {
                            cols: [
                                { 
                                        formType: 'myTable', 
                                        span: 24,
                                        data: value,
                                        tableHeight: 'auto',
                                        colData: [
                                        {
                                        label: '补单内容',
                                        prop: 'supply_content',
                                        },{
                                        label: '问题原因',
                                        prop: 'problem_reason',
                                        }, {
                                        label: '责任',
                                        prop: 'problem_type',
                                        formatter: prop => ({
                                            INSTALL:"安装责任",
                                            DESIGN:"设计责任",
                                            APRAT:"拆审责任",
                                            PRODUCT:"工厂责任",
                                            TRANSPORT:"物流责任",
                                            OTHER:"其他责任",
                                            INCREMENT:"客增",
                                        }[prop]),
                                        },{
                                        label: '附件',
                                        width: '200',
                                        prop: 'after_ticket_no',
                                        redirectClick(d) {
                                           self.handleView(d)
                                        },
                                        },]
                                    }
                            ]
                        }
                    ]
                }

            )
            console.log(data);
            data.key = `goodsProblemDetails${this.currentKey}`
            return data
        },
         handleView( row) {
            if(!row.problem_id){
                    this.$message.error('请保存该问题')
                    return;
                    }
                    // 查看图片
                imageView2(this, {
                    no: {
                        parent_no: row.problem_id,
                    },
                    client_number: this.goods_id,
                    useBy: 'design'
                })
            },
        // 过去交流意见
        getComunication() {
            const goodsInfo = this.params.goodsInfo
            this.aboutNumber=goodsInfo.goods_id
            console.log('info', goodsInfo)
            return {
                title: "交流意见",
                key: `communication${this.currentKey}`,
                noKey: true,
                rows: [
                    {
                        cols: [
                            {
                                formType: "communication",
                                locationKey: `communication${this.currentKey}`,
                                params: {
                                    client_number: goodsInfo.client_number,
                                    goods_id: goodsInfo.goods_id,
                                    log_type: '1'
                                },
                                span: 24,
                            },
                        ],
                    },
                ],
            };
        },
        getMap() {
            return new Promise((resolve, reject) => {
                getMap(map => {
                    resolve(map)
                })
            })
        },
        async refresh(type = {}) {
            this.info_goods = await this.getGoodsInfo()
            // 交流意见
            this.info_com = await this.getComunication()
            if(this.params.trade_type === 'SUPPLY'){
                this.info_problem = await this.getProblemInfo();
            }
            this.loading = false
            this.infosChange()
        },
        infosChange() {
            this.infos = [
                this.info_goods,
                this.info_problem,
                this.info_com
            ].filter(item => item)
        },
        async getGoodsInfo() {
            const map = await this.getMap()
            const data = await this.getSection(
                '/custom-web/api/customGoods/getOne',
                {
                  goods_id: this.params.goodsInfo.goods_id
                },
                '商品信息-'+this.params.goodsInfo.goods_id,
                value => {
                    return []
                },
                (value) => {
                  value.client_number_list = [value.client_number]
                  value.original_client_number_list = [value.original_client_number]
                  let goods_status_index = map.goods_status.filter(item => item.value === value.goods_status)[0] || {}
                  value.goods_status_cn = goods_status_index.label
                  let [
                        goods_status_cn, color_cn, style_cn, category_cn, material_cn, length, width, height,
                        shop_name, shop_code, client_number_list, client_name, client_mobile,
                        difficulty, message, retail_price, act_price, design_room_name, designer,
                        goodIds, goodsFile, original_client_number_list
                    ] = [
                            {prop: 'goods_status_cn', label: '商品状态', span: 12},
                            {prop: 'color_cn', label: '主材颜色', span: 12},
                            {prop: 'style_cn',label: '风格', span: 12},
                            {prop: 'category_cn',label: '类目', span: 12},
                            {prop: 'material_cn',label: '材质', span: 12},
                            {prop: 'length',label: '宽度', suffix: 'mm', span: 12},
                            {prop: 'width',label: '深度', suffix: 'mm', span: 12},
                            {prop: 'height',label: '高度', suffix: 'mm', span: 12},
                            {prop: 'shop_name',label: '专卖店', span: 12},
                            {prop: 'shop_code',label: '专卖店编号', span: 12},
                            {formType: 'orderList', prop: 'client_number_list',label: '订单编号', span: 12, params: {
                                orderClick: (item) => {
                                    if(this.trade_type === 'SUPPLY') {
                                        this.$root.eventHandle.$emit('creatTab', {
                                            name: '补单详情',
                                            component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
                                            params: {
                                                client_number: item,
                                            }
                                        })
                                    } else {
                                        this.$root.eventHandle.$emit('creatTab', {
                                            name: '订单详情',
                                            component: () => import('@components/dz_customer/clientInfo/clientInfo.vue'),
                                            params: {
                                            customerInfo: {client_number: item},
                                            lastTab: this.params.tabName
                                            }
                                        })
                                    }
                                }
                            }},
                            {prop: 'client_name',label: '客户名称',  span: 12},
                            {prop: 'client_mobile',label: '客户手机', span: 12},
                            {prop: 'difficult', valueFilter:'select', label: '难度',  span: 12, options: [{label: '一级', value: 1},{label: '二级', value: 2},{label: '三级', value: 3}]},
                            {prop: 'message',label: '商品名称', span: 12},
                            {formType: 'retailPrice',params:{
                                value:value
                            },prop: 'retail_price',label: '零售价', suffix: '元',  span: 12},
                            {prop: 'act_price',label: '实际价格', suffix: '元', notellipsis:true,  span: 12},
                            {prop: 'design_room_name',label: '所在空间',  span: 12},
                            
                            {prop: 'designer',label: '设计师',  span: 12},
                            
                            {formType:'orderList', prop: 'goodIds',label: '同订单商品',  span: 24, params: {
                                orderClick: (item) => {
                                this.$root.eventHandle.$emit('creatTab', {
                                    name: '商品详情',
                                    component: () => import('@components/dz_customer/goodsInfo/goodsInfo.vue'),
                                    params: {
                                    goodsInfo: {goods_id: item}
                                    }
                                })
                                }
                            }},
                            {formType: 'goodsFile',label: '商品相关文件', prop: 'goodsFile', params: {order_no: value.custom_goods_id},  span: 24},
                            {formType: 'orderList', prop: 'original_client_number_list',label: '源订单编号', span: 12, params: {
                                orderClick: (item) => {
                                this.$root.eventHandle.$emit('creatTab', {
                                    name: '订单详情',
                                    component: () => import('@components/dz_customer/clientInfo/clientInfo.vue'),
                                    params: {
                                    customerInfo: {client_number: item},
                                    lastTab: this.params.tabName
                                    }
                                })
                                }
                            }},
                        
                        
                    ]
                    if(this.trade_type === 'SUPPLY') {
                        client_number_list.label = '补单编号'
                    }
                    let detail = this.trade_type === 'SUPPLY' ? [
                        goods_status_cn, length, width, height, shop_name, shop_code, client_number_list, original_client_number_list, 
                        client_name, client_mobile,
                        difficulty, message, retail_price, designer, goodIds, goodsFile
                    ] : [ //详情信息
                                goods_status_cn, color_cn, style_cn, category_cn, material_cn, length, width, height,
                                shop_name, shop_code, client_number_list, client_name, client_mobile,
                                difficulty, message, retail_price, act_price, design_room_name, designer,
                                goodIds, goodsFile
                            ]
                    return [
                        {
                            cols: this.setValue(detail, value)
                        }
                    ]
                }

            )
            console.log(this.trade_type)
            data.key = 'goodsInfo'
            return data
        }
    }
}
</script>
<style scoped>
.info {
    position: absolute;
    top:0;
    left:0;
    width: 100%;
    height: 100%;
    overflow:auto;
    padding: 0 20px;
}
.info-container {
    max-width: 1400px;
    margin: 0 auto;
    padding-bottom: 40px;
    line-height: 16px;
}
</style>
