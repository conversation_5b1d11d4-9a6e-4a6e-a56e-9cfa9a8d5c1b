<template>
  <!-- 商品订单列表 -->
  <div>
      <xpt-headbar >
        <template slot='left'>
          <el-button
            @click="swj"
            type="success"
            size="mini"
            :disabled="designRoomList.length === 0"
            >创建商品</el-button
          >
          <el-button
            @click="getGoodsList"
            type="primary"
            size="mini"
            :loading="loading"
            >刷新</el-button
          >
          <!-- <el-button
            @click="goodsValuation"
            type="success"
            size="mini"
            :disabled="false"
            :loading="valuationLoading"
            >一键算价</el-button
          > -->
          <!-- <el-button
            @click="submitGoods"
            type="success"
            size="mini"
            :disabled="canNotSubmit"
            :loading="submitLoading"
            >审核</el-button
          > -->
          <el-button
            @click="addDesignAssess"
            type="primary"
            size="mini"
            :disabled="canNotSubmit"
            >下单提交</el-button
          >
          <el-button
            @click="submitOrder"
            type="primary"
            size="mini"
            :disabled="canNotSubmitAssess"
            >驳回提交</el-button
          >
        </template>
      </xpt-headbar>
      <el-row :gutter="24">
      <el-col :span="24">
        <el-card shadow="always" :body-style="{ backgroundColor: '#ffa428',margin:'10px' }">
          <p>下单提示</p>
          <p>1.cad文件上传：“CAD请按照单对应空间输出单空间CAD并上传，切记不要直接上传全屋CAD”</p>
          <p>2.商品名称：“订单名称需要补全空间名称，例如：LSMY佛山市南海区大沥镇融合定制店主卧衣柜，切记加上空间，避免退单”</p>
        </el-card>
      </el-col>
    
    </el-row>
      <el-row>
         <dz-list3
          class="goodsList"
          :showHead="false"
          :data="goodsData"
          :summaryMethod="summaryMethod"
          :colData="colData"
          :tools="tools"
        >
        </dz-list3>
        </el-row>
        
        <el-row>
          <p>一共{{goodsData.length}}件商品</p>
        </el-row>
  </div>
 
</template>

<script>
import { getDesignInfo, customGoodsSubmit, customGoodsValuation,customGoodsSubmitOrder,openGoodsDesign, gotoSwj } from "./common/api";
import formCreate from "./components/formCreate/formCreate";
import btnStatus from "./common/mixins/btnStatus";
import dzList3 from "./components/list/list3";
import { addDesignAssess} from './alert/alert'
export default {
  props: {
    params: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  components: { formCreate, dzList3 },
  mixins: [btnStatus],
  data() {
    let self = this;
    return {
      goodsData:[],
      valuationLoading: false,
      submitOrderLoading:false,
      loading: false,
      submitLoading: false,
      canNotSubmit: true,
      canNotSubmitAssess: true,
      role: ["other"],
      goodsList: [], //商品列表
      designRoomList: [],
      btns: [], //顶部操作按钮
      tools: [
        //商品操作按钮组
        {
          type: "primary",
          txt: "修改",
          click(d) {
            return self.createGoods(self.designRoomMap[d.design_room_id], d);
          },
          show(d) {
            return self.isChange({code: d.goods_status_code}) 
          }
        },
        {
          type: "danger",
          txt: "删除",
          click(d) {
            self.delete(d);
          },
          show(d) {
            return self.isChange({code: d.goods_status_code})   
          }
        },
        // {
        //   type: "success",
        //   txt: "审核",
        //   show(d) {
        //     return false
        //     // return self.isVerify({
        //     //   goods_status: d.goods_status,
        //     //   client_status: self.params.row.client_status
        //     // })
            
        //   },
        //   key: 'sh',
        //   click(d) {
        //     d.shloading = true
        //     customGoodsSubmit(
        //       { custom_goods_id: d.custom_goods_id },
        //       false,
        //       true
        //     ).then((verBody) => {
        //       d.shloading = false
        //       if (verBody.result) {
        //         // 提交完成
        //         self.getGoodsList()
        //       }
        //     }).catch(() => {
        //       d.shloading = false
        //     })
        //   }
        // },
        {
          type: 'primary',
          txt: '商品方案',
          show(d) {
            return d.goods_status != 'WAIT_CONFIRMED'
          },
          click(d) {
            openGoodsDesign({
              swj_goods_id: d.swj_goods_id
            }).then(res => {
              if(res.result) {
                  window.open(res.content)
              }
            })
          }
        }
      ],
      colData: Object.freeze([
        {
          label: "商品号",
          prop: "goods_id",
          width: "130",
          redirectClick(d) {
            self.goodsdetail(d);
          },
        },
        {
          label: "商品名称",
          prop: "message",
          width: "130",
        },
        {
          label: "空间",
          prop: "design_room_name",
          width: "90"
        },
        {
          label: "商品类型",
          prop: "type_code_name",
          width: "90"
        },
        {
          label: "风格",
          prop: "style_cn",
          width: "90",
        },
        {
          label: "类目",
          prop: "category_cn",
          width: "90",
        },
        {
          label: "材质",
          prop: "material_cn",
          width: "90",
        },
        {
          label: "颜色",
          prop: "color_cn",
          width: "90",
        },
        {
          label: "建档人",
          prop: "designer",
          width: "90",
        },
        {
          label: "商品状态",
          prop: "goods_status_cn",
          width: "90",
        },
        {
          label: "实际价格",
          prop: "act_price",
          width: "120",
        },
        {
          label: "退回原因",
          prop: "return_reason",
          width: "120",
        },
      ]),
    };
  },
  async mounted() {
    this.canNotSubmitAssess = !(this.params.row.client_status == "REJECT_VERIFY")
    this.canNotSubmit = !( this.params.row.client_status  == "IN_DESIGN")
    
    await this.getDesignRoom();
    this.getGoodsList();
    this.$root.eventHandle.$on("saveCustomGoods", this.getGoodsList);
  },
  beforeDestroy() {
    this.$root.eventHandle.$off("saveCustomGoods", this.getGoodsList);
  },
  methods: {
    addDesignAssess(){
      this.canNotSubmit = true;
      new Promise((resolve,reject)=>{
        addDesignAssess(this,{client_number:this.params.row.client_number},resolve)
      }).then(result=>{
        if(result){

        }else{
          this.canNotSubmit = false;
        }
      })
    },
    // subverify() {  //提交审核
    //   this.$confirm('下单完毕后，该客户号不再可以创建商品，确定该客户的所有商品都已创建完毕吗？', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     this.$message({
    //       type: 'success',
    //       message: '提交成功!'
    //     });
    //   }).catch(() => {
    //     this.$message({
    //       type: 'info',
    //       message: '已取消操作'
    //     });
    //   });
    // },
    // 提交
    submitOrder(){
      this.submitOrderLoading = true
      const { client_number } = this.params.row;
       this.$prompt('请输入提交内容', '提交', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputErrorMessage:'提交内容不能为空'
        }).then(({ value }) => {
            customGoodsSubmitOrder(
            { client_number: client_number,remark:value},
            false,
            true
          ).then((verBody) => {
            this.submitOrderLoading = false
            if (verBody.result) {
              // 提交完成
              this.getGoodsList(false)
            }
          }).catch(() => {
            this.submitOrderLoading = false
          })
        }).catch(() => {
              
        });
      
    },
    //一键算价
    goodsValuation() {
      this.valuationLoading = true
      const { client_number } = this.params.row
      customGoodsValuation(
        { client_number: client_number},
        false,
        true
      ).then((verBody) => {
        this.valuationLoading = false
        if (verBody.result) {
          // 提交完成
          this.getGoodsList(false)
        }
      }).catch(() => {
        this.valuationLoading = false
      })
    },
    //审核
    submitGoods() {
      this.submitLoading = true
      const { client_number } = this.params.row
      customGoodsSubmit(
        { client_number },
        false,
        true
      ).then((verBody) => {
        this.submitLoading = false
        if (verBody.result) {
          // 提交完成
          this.getGoodsList(false)
        }
      }).catch(() => {
        this.submitLoading = false
      })
    },
    summaryMethod(param) {
      const { columns, data } = param;
      const sums = [];
      let retail_priceIndex;
      let act_priceIndex;
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总计";
          return;
        }
        if (
          column.property === "retail_price" ||
          column.property === "act_price"
        ) {
          column.property === "retail_price" && (retail_priceIndex = index);
          column.property === "act_price" && (act_priceIndex = index);
          const values = data.map((item) => Number(item[column.property]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = "N/A";
          }
        }
      });
      sums[retail_priceIndex] = `总零售价：${parseFloat(
        sums[retail_priceIndex]
      ).toFixed(2)}元`;
      sums[act_priceIndex] = `总实际价格：${parseFloat(
        sums[act_priceIndex]
      ).toFixed(2)}元`;
      return sums;
    },
    goodsdetail(d) {
      //商品详情
      this.$root.eventHandle.$emit("creatTab", {
        name: "商品详情",
        component: () =>
          import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
        params: {
          goodsInfo: d,
        },
      });
    },
    change(d) {
      //修改商品
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/dz_customer/alert/createGoods"),
        params: {
          goodsInfo: d,
        },
        style: "width:800px;height:600px",
        title: "修改商品",
      });
    },
    delete(d) {
      let rejectNum = 0
      this.designRoomList.forEach(item => {
        item.data.forEach(good => {
          good.goods_status === 'REJECT_VERIFY' && (rejectNum++)

        })
      })
      //删除商品
      this.$confirm(rejectNum === 1 ? "删除该商品后，订单状态将会被提交" : "您确定要删除该商品和该商品的上传资料吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const { custom_goods_id } = d;
          let data = { custom_goods_id } || {};
          this.ajax.postStream(
            "/custom-web/api/customGoods/delete",
            data,
            (res) => {
              res = res.body;
              this.$message({
                message: res.msg,
                type: res.result ? "success" : "error",
              });
              if (res.result) {
                this.$root.eventHandle.$emit(
                  "saveCustomGoods",
                  this.getGoodsList
                );
              }
            }
          );
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    getGoodsList(showSuccessTips = true) {
      const { client_number } = this.params.row;
      let data = { client_number } || {};
      return new Promise((resolve, reject) => {
        this.loading = true
        this.ajax.postStream(
          "/custom-web/api/customGoods/getList",
          data,
          (res) => {
            res = res.body;
            this.loading = false
            if (res.result) {
              let content = res.content || [];
              this.designRoomList.forEach((item, index) => {
                this.designRoomList[index].data.splice(
                  0,
                  this.designRoomList[index].data.length
                );
              });
              this.goodsData = [];
              content.forEach((item) => {
                this.goodsData.push(item);
                let i = this.designRoomList.findIndex(
                  (el) => el.design_room_id == item.design_room_id
                );
                if (i !== -1) {
                  item.shloading = false
                  this.designRoomList[i].data.push(item);
                }
              });
              resolve(this.designRoomList)
              // this.designRoomList = this.designRoomList.filter(item => true)
            } else {
              reject()
            }
            showSuccessTips && this.$message({
              message: res.msg,
              type: res.result ? "success" : "error",
            });
          },
          (err) => {
            this.loading = false
          }
        );
      })
      
    },
    async getDesignRoom() {
      const { client_number } = this.params.row;
      let data = {
        client_number,
      };
      let content = await getDesignInfo(data);
      this.designRoomMap = {};
      this.designRoomList = content.designRoomList.map((item) => {
        this.designRoomMap[item.design_room_id] = item;
        item.data = [];
        item.design_room_id = item.design_room_id;
        return item;
      });
    },
    swj() {
      const item = this.designRoomList[0]
      gotoSwj({
          client_number: item.client_number,
          design_id: item.design_id
      }).then(res => {
          if(res.result) {
              window.open(res.content)
          }
      })
    },
    async createGoods(item, values = {}) {
      const self = this
      // 获取id
      delete item.custom_goods_id;
      delete this.params.row.custom_goods_id;
      let value = Object.assign({}, this.params.row, item, values);
      this.$root.eventHandle.$emit("alert", {
        component: () => import("@components/dz_customer/alert/createGoods"),
        style: "width:800px;height:600px",
        title: `${value.custom_goods_id ? "修改" : "新增"}-${
          item.design_room_name
        }-商品`,
        params: {
          initValue: value,
          async callback(init, custom_goods_id) {
            const designRoomList = await self.getGoodsList(false);
            designRoomList.forEach(room => {
              room.data.forEach(good => {
                good.custom_goods_id === custom_goods_id && init(Object.assign(value, good))
              })
            })
          }
        },
      });

    },
  },
};
</script>

<style scope>
.main {
  /* height: 100%; */
  overflow: scroll;
  overflow-x: ;
}
.content .title {
  margin-left: 10px;
  font-weight: 400;
  color: #1f2f3d;
  font-size: 16px !important;
  line-height: 30px;
}
.content {
  margin: 10px auto;
}
.goodsList .xpt-flex__bottom .el-table .el-table__body-wrapper{
  overflow-x: auto !important;
}
/* .topbtn {
  margin: 10px auto;
  width: 1060px;
} */
</style>
