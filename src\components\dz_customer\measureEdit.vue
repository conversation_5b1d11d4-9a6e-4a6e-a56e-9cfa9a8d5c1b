<template>
<!-- 量尺登记添加编辑 -->
  <div class="mainPage">
    <xpt-headbar class="topBtn">
			<el-button size='mini'  type="primary" @click="submit('customer')" slot='left' :disabled='customerBtnStatu' :loading='customerBtnStatu'>保存</el-button>
			<!-- <el-button type='danger' class='xpt-close' size='mini' @click="closeComponent" slot='right'>关闭</el-button> -->
		</xpt-headbar>
    <div class="content">
      <el-form label-position="right" label-width="100px" :model="customer" :rules="rules" ref="customer">
        <el-row type="flex" justify="space-around">
          <el-col :span="10">
            <el-form-item label="客户号" prop="client_number">
              <el-input v-model="customer.client_number" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="客户姓名" prop="client_name">
              <el-input v-model="customer.client_name" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="客户类型" prop="client_type">
              <el-input v-model="customer.client_type" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-around">
          <el-col :span="10">
            <el-form-item label="客户电话" prop="client_mobile">
              <el-input v-model="customer.client_mobile" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="设计师" prop="designer_name">
              <el-input v-model="customer.designer_name" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-around">
          <el-col :span="10">
            <el-form-item label="量尺时间" prop="measure_time">
              <el-date-picker
                v-model="customer.measure_time"
                type="datetime"
                size='mini'
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
              <el-tooltip v-if='rules.measure_time[0].isShow' class="item" effect="dark" :content="rules.measure_time[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="计划沟通时间" prop="plan_communication_time">
              <el-date-picker
                v-model="customer.plan_communication_time"
                type="datetime"
                size='mini'
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
              <el-tooltip v-if='rules.plan_communication_time[0].isShow' class="item" effect="dark" :content="rules.plan_communication_time[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row type="flex" justify="center">
          <el-col :span="20">
            <p class="redtips">*生活阶段根据做决定的客户自身情况选择，和购买产品无关</p>
          </el-col>
        </el-row> -->
        <!-- <el-row type="flex" justify="center">
          <el-col :span="22" class="addHeight">
            <el-form-item label="生活阶段">
              <el-radio-group v-model="customer.life_stage" class="addLineHeight">
                <span
                v-for="(item, key) in life_stage" 
                :key="key"
                >
                <el-radio 
                :label="item.value"
                >{{ item.label }}</el-radio><br>
                </span>
              </el-radio-group>
              <el-tooltip v-if='rules.life_stage[0].isShow' class="item" effect="dark" :content="rules.life_stage[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
          <el-row type="flex" justify="center">
            <el-col :span="22">
              <el-form-item label="常住成员">
              <el-checkbox-group v-model="customer.member">
                <el-checkbox 
                v-for="(item, key) in member" 
                :key="key"
                :label="item.value">{{ item.label }}</el-checkbox>
              </el-checkbox-group>
              <el-tooltip v-if='rules.member[0].isShow' class="item" effect="dark" :content="rules.member[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
            </el-col>
          </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="预算">
              <el-radio-group v-model="customer.budget">
                <el-radio 
                v-for="(item, key) in budget" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
              <el-tooltip v-if='rules.budget[0].isShow' class="item" effect="dark" :content="rules.budget[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="房屋类型">
              <el-radio-group v-model="customer.house_type">
                <el-radio v-for="(item, key) in house_type" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
              <el-tooltip v-if='rules.house_type[0].isShow' class="item" effect="dark" :content="rules.house_type[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="消费类型">
              <el-radio-group v-model="customer.consume_type">
                <el-radio v-for="(item, key) in consume_type" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
              <el-tooltip v-if='rules.consume_type[0].isShow' class="item" effect="dark" :content="rules.consume_type[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="总面积">
              <el-radio-group v-model="customer.total_area">
                <el-radio v-for="(item, key) in total_area" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
              <el-tooltip v-if='rules.total_area[0].isShow' class="item" effect="dark" :content="rules.total_area[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="房屋房价" >
              <el-radio-group v-model="customer.house_price">
                <el-radio v-for="(item, key) in house_price" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
              <el-tooltip v-if='rules.house_price[0].isShow' class="item" effect="dark" :content="rules.house_price[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="装修阶段">
            <el-radio-group v-model="customer.decoration_stage">
              <el-radio v-for="(item, key) in decoration_stage" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <el-tooltip v-if='rules.decoration_stage[0].isShow' class="item" effect="dark" :content="rules.decoration_stage[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="装修风格">
            <el-radio-group v-model="customer.decoration_style">
              <el-radio v-for="(item, key) in decoration_style" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <el-tooltip v-if='rules.decoration_style[0].isShow' class="item" effect="dark" :content="rules.decoration_style[0].message" placement="left-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="计划入住">
            <el-radio-group v-model="customer.plan_stay">
              <el-radio v-for="(item, key) in plan_stay" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <el-tooltip v-if='rules.plan_stay[0].isShow' class="item" effect="dark" :content="rules.plan_stay[0].message" placement="left-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="客户性质">
            <el-radio-group v-model="customer.client_property">
              <el-radio v-for="(item, key) in client_property" 
                :key="key"
                :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <el-tooltip v-if='rules.client_property[0].isShow' class="item" effect="dark" :content="rules.client_property[0].message" placement="left-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
          </el-form-item>
          </el-col>
        </el-row> -->
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form-item label="量尺空间" prop="measureRoomList">
              <measure-space
              v-model="customer.measureRoomList"
              :tags="tags"
              :config="config"
              ></measure-space>
              <el-tooltip v-if='rules.measureRoomList[0].isShow' class="item" effect="dark" :content="rules.measureRoomList[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span='22' class="addHeight2">
            <el-form-item label="备注" prop="remark">
              <el-input type='textarea' v-model="customer.remark" size='mini' style="width:80%" :maxlength='200' resize="none"></el-input>
              <!-- <el-tooltip v-if='rules.remark[0].isShow' class="item" effect="dark" :content="rules.remark[0].message" placement="left-start" popper-class='xpt-form__error'>
                <i class='el-icon-warning'></i>
              </el-tooltip> -->
            </el-form-item>
          </el-col> 
        </el-row>
      </el-form>
      <!-- <form-create :formData="queryItems" @save="query"></form-create> -->
    </div>
  </div>
</template>

<script>
// import formCreate from './components/formCreate/formCreate'
import validate from './common/validate'
import measureSpace from './components/measureSpace/measureSpace'
import fn from '../../common/Fn.js'
import {createId} from './common/api'
import closeComponent from './common/mixins/closeComponent'
import { 
  tags, life_stage, member, budget, house_type, 
  consume_type, total_area,  house_price, decoration_stage, 
  decoration_style, plan_stay, client_property
} from './common/measureDictionary'


export default {
  mixins: [closeComponent],
  components: {
    // formCreate,
    measureSpace
  },
  props:['params'],
    data(){
      var self = this
      return {
        rules:{
          measure_time : validate.isNotBlank({
            self:self,
            msg:'请选择量尺时间',
            trigger: 'change'
          }),
          plan_communication_time : validate.isNotBlank({
            self:self,
            msg:'请选择计划沟通时间',
            trigger: 'change'
          }),
          life_stage : validate.isNotBlank({
            self:self,
            msg:'请选择生活阶段',
            trigger: 'change'
          }),
          member : validate.isNotBlank({
            self:self,
            msg:'请选择常驻成员',
            trigger: 'change'
          }),
          budget : validate.isNotBlank({
            self:self,
            msg:'请选择预算',
            trigger: 'change'
          }),
          house_type : validate.isNotBlank({
            self:self,
            msg:'请选择房屋类型',
            trigger: 'change'
          }),
          consume_type : validate.isNotBlank({
            self:self,
            msg:'请选择消费类型',
            trigger: 'change'
          }),
          total_area : validate.isNotBlank({
            self:self,
            msg:'请选择房屋面积',
            trigger: 'change'
          }),
          house_price : validate.isNotBlank({
            self:self,
            msg:'请选择房价',
            trigger: 'change'
          }),
          decoration_stage : validate.isNotBlank({
            self:self,
            msg:'请选择装修阶段',
            trigger: 'change'
          }),
          decoration_style : validate.isNotBlank({
            self:self,
            msg:'请选择装修风格',
            trigger: 'change'
          }),
          plan_stay : validate.isNotBlank({
            self:self,
            msg:'请选择计划入住',
            trigger: 'change'
          }),
          client_property : validate.isNotBlank({
            self:self,
            msg:'请选择客户性质',
            trigger: 'change'
          }),
          measureRoomList : validate.isNotBlank({
            self:self,
            msg:'请添加量尺空间',
            trigger: 'change'
          }),
          // remark : validate.isNotBlank({
          //   self:self,
          //   msg:'请输入备注',
          //   trigger: 'change'
          // }),
        },
        customer:{
          custom_measure_id: '',
          measure_id:'',
          client_number:'',
          client_name:'',
          client_mobile:'',
          client_type:'',
          designer_name:'',
          designer_number:'',
          measure_time:'',
          plan_communication_time:'',
          measureRoomList:[],
          life_stage:'',
          member:[],
          budget:'',
          house_type:'',
          consume_type:'',
          total_area:'',
          house_price:'',
          decoration_stage:'',
          decoration_style:'',
          plan_stay:'',
          client_property:'',
          remark:''
        },
        isEdit: false,
        config: {},
        tags:tags.map(item=>{item.spacename = item.label; return item} ),
        life_stage:[],
        member:[], 
        budget:[], 
        house_type:[], 
        consume_type:[], 
        total_area:[],  
        house_price:[], 
        decoration_stage:[], 
        decoration_style:[], 
        plan_stay:[], 
        client_property:[],
        loading:false,
        placeholder:'请选择',
        customerBtnStatu: false
      }
    },
    created(){
      
      this.getSpaceTags()
      this.getDictionary()
    },
    methods:{
      saveComponent() {
        this.submit('customer')
      },
      getDictionary() {
        this.life_stage = life_stage
        this.member = member
        this.budget = budget
        this.house_type = house_type
        this.consume_type = consume_type
        this.total_area = total_area
        this.house_price = house_price
        this.decoration_stage = decoration_stage
        this.decoration_style = decoration_style
        this.plan_stay = plan_stay
        this.client_property = client_property
      },
      getSpaceTags() {
        this.tags = tags
      },
      close(){
        let self = this
        self.$root.eventHandle.$emit('removeTab',self.params.tabName)
      },
      createStateFilter(queryString) {
        return (state) => {
          return (state.value.indexOf(queryString.toLowerCase()) === 0)
        }
      },
      async getId() {
        this.customer.custom_measure_id = await createId()
      },
      storeData(callback){
        var url = '/custom-web/api/customMeasure/save'
        var data = {}
        var data = JSON.parse(JSON.stringify(this.customer))
        data.member = data.member.toString()
        // data.measure_time = fn.dateFormat(data.measure_time,'yyyy-MM-dd hh:mm:ss')
        // data.plan_communication_time = fn.dateFormat(data.plan_communication_time,'yyyy-MM-dd hh:mm:ss')
        data.budget = Number(data.budget)
        data.house_price = Number(data.house_price)
        data.total_area = Number(data.total_area)
        delete(data["client_mobile"])
        delete(data["client_name"])
        delete(data["client_type"])
        // 判断空间是否有上传附件
        let noFileMeasureRoomIndex = data.measureRoomList.findIndex(item => item.is_add && !item.imgs)
        if(noFileMeasureRoomIndex !== -1) {
          this.$message.error(`请上传${data.measureRoomList[noFileMeasureRoomIndex].measure_room_name}空间文件`)
          return
        }
        data.measureRoomList = data.measureRoomList.map(item=>{
            delete item.imgs
            return item
          } 
        )
        var _this = this
        this.customerBtnStatu = true
        if(this.request) {
          this.$message({
            type: 'warning',
            message: '请等待上一次请求结束'
          })
          return
        }
        this.request = true
        this.ajax.postStream(url,data,(data) =>{
          this.request = false
          data = data.body
          _this.$message({
            message: data.msg,
            type:data.result?'success':'error'
          })
          if(data.result){
            this.$root.eventHandle.$emit('refreshclientList')
            this.$root.eventHandle.$emit('refreshmeasureList')
            this.$root.eventHandle.$emit('refreshcompactList')

          }
          callback&&callback()
          _this.close()
          _this.customerBtnStatu = false
        },function(data){
          _this.request = false
          console.log('失败的回调函数')
          _this.customerBtnStatu = false
        })
      },
      //保存数据
      submit(formName,callback){
        this.$refs[formName].validate((valid) => {
          if(!valid) return false
          this.storeData(callback)
        })
      },
      getCustomerDeatil(){
        //获取客户详情
        this.isEdit = this.params.isEdit
        if(this.isEdit) {
          this.customer.custom_measure_id = this.params.measureInfo.custom_measure_id
          this.customer.measure_id = this.params.measureInfo.measure_id
          this.customer.client_number = this.params.measureInfo.client_number
          this.customer.client_name = this.params.measureInfo.client_name
          this.customer.client_mobile = this.params.measureInfo.client_mobile
          this.customer.client_type = (this.params.measureInfo.client_type == '1') ? "家具客户" : "全案客户"
          this.customer.designer_name = this.params.measureInfo.designer_name
          this.customer.designer_number = this.params.measureInfo.designer_number
          this.customer.measure_time = this.params.measureInfo.measure_time
          this.customer.plan_communication_time = this.params.measureInfo.plan_communication_time
          this.customer.measureRoomList = Array.isArray(this.params.measureInfo.measureRoomList) ? this.params.measureInfo.measureRoomList : []
          this.customer.life_stage = this.params.measureInfo.life_stage
          this.customer.member = this.params.measureInfo.member.split(',')
          this.customer.budget = this.params.measureInfo.budget.toString()
          this.customer.house_type = this.params.measureInfo.house_type
          this.customer.consume_type = this.params.measureInfo.consume_type
          this.customer.total_area = this.params.measureInfo.total_area.toString()
          this.customer.house_price = this.params.measureInfo.house_price.toString()
          this.customer.decoration_stage = this.params.measureInfo.decoration_stage
          this.customer.decoration_style = this.params.measureInfo.decoration_style
          this.customer.plan_stay = this.params.measureInfo.plan_stay
          this.customer.client_property = this.params.measureInfo.client_property
          this.customer.remark = this.params.measureInfo.remark
        } else if(!this.isEdit) {
          this.customer.client_number = this.params.customerInfo.client_number
          this.customer.client_name = this.params.customerInfo.client_name
          this.customer.client_mobile = this.params.customerInfo.client_mobile
          this.customer.designer_number = this.params.customerInfo.designer_number
          this.customer.designer_name = this.params.customerInfo.designer_name
          this.customer.measure_time = new Date();
          this.customer.client_type = (this.params.customerInfo.client_type == '1') ? "家具客户" : "全案客户"
        }
         
      },
      async initData(){//初始化数据
        this.getCustomerDeatil()
        this.config = {
          spacename: 'measure_room_name',
          detail: 'design_point',
          useBy:"measure",
          id:"measure_room_id"
        }
        if(!this.isEdit) {
          await this.getId()
          this.config.client_number = this.customer.custom_measure_id
          this.config.cn = this.params.customerInfo.client_number
        } else {
          this.config.cn = this.params.measureInfo.client_number
          this.config.client_number = this.params.measureInfo.custom_measure_id     
        }
      },
    },

    mounted(){
      this.initData()
      this.params.__data = JSON.stringify(this._data)
    }
  }
</script>

<style scoped>
.el-checkbox-group {
  display: inline-block;
}
.mainPage {
  position: relative;
  height: 100%;
  overflow: scroll;
  overflow-x: hidden;
}
.topBtn {
  position: fixed;
  width: 100%;
  z-index: 1;
}
h2 {
  line-height: 20px;
}
.content {
  margin: 40px auto;
}
.redtips {
  color: red;
}
.addHeight {
  height: auto;
}
.addLineHeight {
  line-height: 28px;
}
.addHeight2 {
  height: 60px;
}
</style>
