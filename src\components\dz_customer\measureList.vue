<template>
<!-- 量尺列表 -->
  <div style="height: 99%;">
    <commonList
    ref="list"
    :params = params
    :tools="tools"
    url="/custom-web/api/customSysTrade/getSysTradeByDesigner"    
    :defaultValue="defaultValue"
    ></commonList>
  </div>
</template>

<script>
import commonList from './commonList2'
import btnStatus from './common/mixins/btnStatus'
import { getRole } from './common/api'

export default {
  components: {
    commonList
  },
  mixins: [
      btnStatus
    ],
  props: ['params'],
  data() {
    return {
      defaultValue: {
        client_status: ['WAITING_MEASUREMENT','WAITING_MEASUREMENT'],
      },
      role:["other"],
      tools: []

    }
  },
  methods:{
    isNull(value) {
      return Object.keys(value).length === 0
    },
    getValue(url,param){
      return new Promise((resolve,reject) => {
        this.ajax.postStream(url, param, (d) => {             
          let value = d.body.result ? d.body.content || {} : {}
          resolve(value)
        })
      })
    },
    refresh() {
      this.$refs.list._getDataList()
    },
    //量尺登记
      async measureEdit(d) {
        const value = await this.getValue(
          '/custom-web/api/customMeasure/get',
          {client_number: d.client_number},
        )
        const {client_number, client_name, client_type, client_mobile, designer_number, designer_name} = d
        const data = {
          client_number, client_name, client_type, client_mobile, designer_name, designer_number
        }
        let param = Object.assign({}, value, data)
        this.$root.eventHandle.$emit('creatTab', {
          name: '量尺登记',
          component: () => import('@components/dz_customer/measureEdit.vue'),
          params: {
            measureInfo: param,
            customerInfo: param,
            lastTab: this.params.tabName,
            isEdit: !this.isNull(value)
          }
        })
      },
  },
  async mounted() {
    this.role = await getRole()
    if(this.role.indexOf('DZ_SJS') != -1||this.role.indexOf('DZ_DG') != -1||
      this.role.indexOf('DZ_DZ') != -1||this.role.indexOf('DZ_SJZG') != -1
    ) {
      this.$refs.list.getInfo()
    } else {
      this.$refs.list.getInfoNoShop()
    }
    let self = this
    this.tools = [
      {
        type: 'primary',
        txt: '量尺登记',
        click: async (d) => {
          self.measureEdit(d)
        },
        show(d) {
          return self.isMeasure({client_status: d.client_status_value,designer_number:d.designer_number}, self.role)
        }
      },
    ].filter(item => item.show)
    this.$root.eventHandle.$on('refreshmeasureList', this.refresh)
  },
  beforeDestroy() {
    this.$root.eventHandle.$off('refreshmeasureList', this.refresh)
  }

}
</script>

<style>

</style>
