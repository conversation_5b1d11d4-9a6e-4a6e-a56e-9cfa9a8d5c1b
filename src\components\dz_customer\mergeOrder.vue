<!-- 线上订单 -->
<template>
  <div>
    <form-create
      ref="formCreate"
      :rules="rules"
      :formData="queryItems"
      :initParam="initParam"
      :btns="false"
    ></form-create>
    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
      <my-table
        :tableData="tableData"
        :btns="btns"
        :tableParam="tableParam"
        :colData="colData"
        isNeedClickEvent
        :showTools="true"
        :tools="tools"
        :orderNo="true"
        :page="false"
        ref="table"
      ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from "./components/formCreate/formCreate";
import { popup } from "./alert/alert";
import myTable from "./components/table/table";
import { sync_status, getMap } from "./common/tollDictionary";
import { jsonToName } from "../mdm/common/config/fieldData";

export default {
  components: { formCreate, myTable },
  props: ["params"],
  data() {
    let self = this;
    return {
      customerBtnStatu: false,
      queryItems: [],
      initParam: {},
      tableData: [],
      btns: [
        {
          type: "primary",
          txt: "合并订单",
          key: "save",
          loading: false,
          disabled: false,
          click: () => {
            let info = self.params.info || {};
            popup.merge_order(self, (result) => {}, {
              multiple: true,
              initParam: {
                cust_id: info.cust_id,
                shop_id: self.params.shop_id,
              },
              btns: [
                {
                  type: 'primary',
                  txt: '保存',
                  key: 'save',
                  loading: false,
                  click(results, listAlert, btns) {
                    let saveBtn = btns.filter(item => item.key === 'save')[0]
                    if(results && results.length) {
                      saveBtn.loading = true
                      // 调用合并订单接口
                      self.save(results, (result) => {
                        saveBtn.loading = false
                        if(result) {
                          // 合并成功关闭窗口
                          listAlert.$root.eventHandle.$emit('removeAlert',listAlert.params.alertId)
                          self.getOlinePayment()
                        }
                      }, err => {
                        saveBtn.loading = false
                      })
                    } else {
                      self.$message.warning('当前无选择项')
                    }
                    
                    
                  },
                  show(d) {
                    return true
                  }
                }
              ]
            });
            // this.btnType = "save";
            // this.save();
          },
        },
      ],
      colData: [],
      tableParam: {},
      rules: {},
      tools: [],
    };
  },
  computed: {},
  created() {
    getMap((map) => {
      this.getColData();

      this.getOlinePayment();
    });
    this.getQueryItems();
  },
  methods: {
    save(arr, success, err) {
      
      this.ajax.postStream(
        "/custom-web/api/customPayment/saveOnline",
        {
          client_number: this.params.info.client_number,
          scmSysTradeList: arr,
        },
        (res) => {
          res = res.body;
          success && success(res.result)
          if (res.result) {
            this.$message.success(`${res.msg}`);
          } else {
            this.$message.error(`${res.msg}`);
          }
        },
        err => {
          err(err)
        }
      );
    },
    delete(row, i) {
      this.ajax.postStream(
        "/custom-web/api/customPayment/deleteOnline",
        {
          client_number: row.client_number,
          payment_id: row.payment_id,
        },
        (res) => {
          res = res.body;
          if (res.result) {
            this.$message.success(`${res.msg}`);
            this.getOlinePayment()
          }
        }
      );
    },
    getOlinePayment() {
      this.ajax.postStream(
        "/custom-web/api/customPayment/getOnlineByClientNumber",
        this.params.info.client_number,
        (res) => {
          res = res.body;
          if (res.result) {
            this.$message.success(`${res.msg}`);
            this.tableData = res.content || [];
          }
        }
      );
      let self = this;
      this.tools = [
        {
          type: "danger",
          txt: "删除",
          click(d, i) {
            self.delete(d, i);
          },
          show(d) {
            return d.payment_id;
          },
        },
      ].filter((item) => item.show);
    },
    getQueryItems() {
      let self = this;
      let params = this.params || {};
      let info = params.info || {};
      this.queryItems = [
        {
          cols: [
            {
              formType: "myText",
              label: "专卖店",
              value: info.shop_name,
              span: 24,
            },
            // {
            //   formType: "listSelect",
            //   label: "网拍订单",
            //   prop: "merge_order",
            //   config: {
            //     popupType: "merge_order",
            //     multiple: true,
            //     initParam: {
            //       cust_id: info.cust_id,
            //       shop_id: params.shop_id,
            //     },
            //   },
            //   span: 12,
            //   options: [],
            //   event: {
            //     result: (item, col, formData, getItem) => {
            //       this.tableData1 = JSON.parse(JSON.stringify(item));
            //       item.forEach((v) => {
            //         v.client_name = v.customer_name;
            //         v.client_number = this.params.info.client_number;
            //         let i = this.tableData.findIndex((elem) => {
            //           return elem.sys_trade_id === v.sys_trade_id;
            //         });
            //         i == -1 && this.tableData.push(v);
            //       });
            //     },
            //   },
            // },
          ],
        },
      ];
    },
    getColData() {
      this.colData = [
        {
          label: "订单号",
          prop: "client_number",
          width: 180,
          redirectClick: (row) => {
            this.$root.eventHandle.$emit("creatTab", {
              name: "订单详情",
              component: () =>
                import("@components/dz_customer/clientInfo/clientInfo.vue"),
              params: {
                customerInfo: row,
                lastTab: this.params.tabName,
              },
            });
          },
        },
        {
          label: "合并单号",
          prop: "merge_trade_id",
          width: 116,
        },
        {
          label: "客户名",
          prop: "client_name",
          width: 103,
        },
        {
          label: "手机",
          prop: "client_mobile",
          width: 127,
        },
        {
          label: "支付编码",
          prop: "pay_number",
          width: 280,
          redirectClick: (row) => {
            this.$root.eventHandle.$emit("creatTab", {
              name: "查看单据详情",
              params: {
                row,
                info: this.params.shopInfo,
              },
              component: () => import("@components/dz_customer/tollInfo.vue"),
            });
          },
        },
        {
          label: "同步状态",
          prop: "sync_status_cn",
          filter: "select",
          options: sync_status,
          width: 86,
        },
        {
          label: "支付金额",
          prop: "pay_amount",
          width: 107,
        },
        {
          label: "支付日期",
          prop: "pay_date",
          filter: "date",
          formats: "yyyy-MM-dd",
          width: 116,
        },
      ];
    },
  },
};
</script>
