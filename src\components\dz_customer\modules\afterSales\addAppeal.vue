<!-- 新增、编辑收费记录 -->
<template>
  
	<div>
  <el-row class="xpt-top" :gutter="40">
		<el-col :span="18">
			<el-button type="primary" size="mini" @click="() => save()">提交</el-button>
			<el-button type="danger" size="mini" @click="() => reset()">重置</el-button>
		</el-col>
	
	</el-row>
	<el-tabs v-model="selectTab1" style="max-height: 280px">
		<el-tab-pane label='申诉信息' name='base'>
      <el-row :gutter="20">
			
			<el-form :model='form' ref='form' label-position="left" label-width="120px">
				<el-col :span="8">
					<el-form-item label="客户名">
						<el-input size='mini' :value="form.client_name" disabled></el-input>
					</el-form-item>
					<el-form-item label="补单号">
						<!-- <el-input size='mini' :value="form.client_number" disabled></el-input> -->
            <a @click="supplyInfo(form)" class="link">{{form.client_number}}</a>
					</el-form-item>
				</el-col>
        <el-col :span="8">
					<el-form-item label="定制单号">
						<!-- <el-input size='mini' :value="form.original_client_number" disabled></el-input> -->
            <a @click="detail(form)" class="link">{{form.original_client_number}}</a>
					</el-form-item>
				</el-col>
        </el-form>
			</el-row>
      <el-row :gutter="20">
        <el-table
				:data="[form]"
				border
				tooltip-effect="dark"
				style="width: 100%;"
				width='100%'
			>
			
        <el-table-column label="责任分析子单" prop="analysis_sub_no" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任类型" prop="liability_type" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任问题" prop="liability_question_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="备注" prop="liability_person_remark" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任金额" prop="liability_amount" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="处理金额" prop="handle_amount" width="230" show-overflow-tooltip></el-table-column>
			</el-table>
			</el-row>

    </el-tab-pane>
  </el-tabs>
		<el-tabs v-model="selectTab2" style="max-height: 280px">
		<el-tab-pane label='申诉内容' name='content'>
			<el-form :model='submit' ref='submit' label-position="left" label-width="120px" style="margin-top:10px">

        <el-row :gutter="20">
        
        
          <el-col :span="8">
            <el-form-item label="原因">
              <el-input size='mini' v-model="submit.reason" type="textarea" ></el-input>
            </el-form-item>
            <el-form-item label="附件">
              <!-- <el-input size='mini' :value="form.client_number" disabled></el-input> -->
              <upload-file
                            ref='upload'
                            :config="uploadFileConfig"
                            parent_no_tips="请先保存系统公告再上传附件！"
                        ></upload-file>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </el-tab-pane>
  </el-tabs>
	</div>
</template>
<script>   
import {createId} from '@components/dz_customer/common/api'
import uploadFile from '@components/dz_customer/components/uploadFile'

  export default {
    props:['params'],
    data(){
      return {
        uploadFileConfig: {},

        submit:{
          reason:''
        },
        form:{
          client_name:'',
          client_number:'',
          original_client_number:'',
        },
        selectTab1: 'base',
        selectTab2: 'content',
        queryItems: [],
        initParam:{}
      }
    },
  components: {
        uploadFile,
          },
    async  created() {
      this.form = this.params.row;
      this.form.custom_appeal_record_id = this.uploadFileConfig.parent_no = await createId()
      this.form.reject_id = await createId()
      // this.uploadFileConfig = {
      //               parent_no: params.id
      //           }
    },
    methods:{
      
      supplyInfo(d){
        this.$root.eventHandle.$emit('creatTab', {
          name: '补单详情',
          component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
          params: {
            client_number: d.client_number,
          }
        })
      },
      detail(d) {
      this.$root.eventHandle.$emit("creatTab", {
        name: "订单详情",
        component: () =>
          import("@components/dz_customer/clientInfo/clientInfo.vue"),
        params: {
          customerInfo: {client_number:d.original_client_number},
          lastTab: this.params.tabName,
        },
      });
    },
     save(){
       console.log(this.$refs.upload.files.length)
       if(!this.$refs.upload.files.length){
         this.$message.error('请上传文件');
         return;
       }
       if(!this.submit.reason){
         this.$message.error('请填写原因');
         return;
       }
       let postData = {
         custom_appeal_record_id:this.form.custom_appeal_record_id,
         custom_appeal_no:this.form.custom_appeal_no,
         client_number:this.form.client_number,
         original_client_number:this.form.original_client_number,
         appeal_reason:this.submit.reason,
         liability_amount:this.form.liability_amount,
         handle_amount:this.form.handle_amount,
         liability_type:this.form.liability_type,
         liability_question_name:this.form.liability_question_name,
         liability_person_remark:this.form.liability_person_remark,
         analysis_sub_no:this.form.analysis_sub_no,
         reject_id:this.form.reject_id,
       }
       this.ajax.postStream('/custom-web/api/appeal/appeal', postData, res => {
				if(res.body.result){
					this.$message.success(res.body.msg)
          this.$root.eventHandle.$emit('removeTab',this.params.tabName);
				}else {
					this.$message.error(res.body.msg)
				}
			})
     },
     reset(){
       this.submit.reason = '';
     },
     
    }
  }
</script>
<style scoped>
  .link{
    cursor: pointer;
    color: #0000EE;
  }
</style>