<!--申诉审核 -->
<template>
  
	<div>
  <el-row class="xpt-top" :gutter="40">
		<el-col :span="18">
			<!-- <el-button type="primary" size="mini" @click="() => save()">提交</el-button> -->
		</el-col>
	
	</el-row>
	<el-tabs v-model="selectTab1" style="max-height: 280px">
		<el-tab-pane label='申诉信息' name='base'>
      <el-row :gutter="20">
			
			<el-form :model='form' ref='form' label-position="left" label-width="120px">
				<el-col :span="8">
					<el-form-item label="客户名">
						<el-input size='mini' :value="form.client_name" disabled></el-input>
					</el-form-item>
					<el-form-item label="补单号">
						<!-- <el-input size='mini' :value="form.client_number" disabled></el-input> -->
            <a @click="supplyInfo(form)" class="link">{{form.client_number}}</a>
					</el-form-item>
				</el-col>
        <el-col :span="8">
					<el-form-item label="定制单号">
						<!-- <el-input size='mini' :value="form.original_client_number" disabled></el-input> -->
            <a @click="detail(form)" class="link">{{form.original_client_number}}</a>
					</el-form-item>
				
				</el-col>
        </el-form>
			</el-row>
      <el-row :gutter="20">
        <el-table
				:data="[form]"
				border
				tooltip-effect="dark"
				style="width: 100%;"
				width='100%'
			>
			
        <el-table-column label="责任分析子单" prop="analysis_sub_no" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任类型" prop="liability_type" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任问题" prop="liability_question_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="备注" prop="liability_person_remark" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任金额" prop="liability_amount" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="处理金额" prop="handle_amount" width="230" show-overflow-tooltip></el-table-column>
			</el-table>
			</el-row>

    </el-tab-pane>
  </el-tabs>
		<el-tabs v-model="selectTab2" style="max-height: 280px">
		<el-tab-pane label='申诉内容' name='content'>
			<el-form :model='submit' ref='submit' label-position="left" label-width="120px">

        <el-row :gutter="20">
        
        
          <el-col :span="20">
            <el-form-item label="原因">
              <el-input size='mini' :value="submit.appeal_reason" type="textarea" disabled ></el-input>
            </el-form-item>
            <el-form-item label="附件">
              <div>
                <el-button type="primary" @click="saveZip(imgList)"  size="mini">打包下载</el-button>
              </div>
              <span v-for="(item,key) in imgList" style="margin-top: 20px;
    display: block;
    float: left;
    margin-left: 10px;">
                <a  v-if="isImage(item.path)" @click="showimgList(imgList,item)" style="color:#0000EE ;cursor: pointer;">{{item.name}}</a>
                <a v-else :href="item.path" target="_blank">{{item.name}}</a>
              </span>
              

            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </el-tab-pane>
  </el-tabs>
  	<el-tabs v-model="selectTab3" style="max-height: 280px">
		<el-form :model='submit2' ref='submit' label-position="left" label-width="120px">

        <el-row :gutter="20">
        
        
          <el-col :span="20">
            <el-form-item label="审核结果">
              {{{PASS:"通过",REJECT:"申诉驳回",}[submit.status]}}
              <!-- <el-radio-group v-model="submit2.isPass">
                <el-radio :label="1">申诉通过</el-radio>
                <el-radio :label="0">申诉驳回</el-radio>
              </el-radio-group> -->
            </el-form-item>
            <el-form-item label="说明">
              <el-input size='mini' v-model="submit.reject_reason" type="textarea" disabled></el-input>
            </el-form-item>
            
             <el-form-item label="附件">
               <div>
                <el-button type="primary" @click="saveZip(imgList2)" size="mini">打包下载</el-button>

               </div>
              <span v-for="(item,key) in imgList2" style="margin-top: 20px;
                  display: block;
                  float: left;
                  margin-left: 10px;">
                           
                <a  v-if="isImage(item.path)" @click="showimgList(imgList2,item)" style="color:#0000EE; cursor: pointer;">{{item.name}}</a>
                <a v-else :href="item.path" target="_blank">{{item.name}}</a>
              </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
  </el-tabs>
  <xpt-image
            :images="imageList"
            :show="ifShowImage"
            :ifUpload="false"
            :ifClose="false"
            @close="closeShowImage"
        >
        </xpt-image>
        
	</div>
</template>
<script>   
import {createId,downloadZip} from '@components/dz_customer/common/api'
import uploadFile from '@components/dz_customer/components/uploadFile'

import Fn from "@common/Fn.js";

  export default {
    props:['params'],
    components: {
        uploadFile,
          },
    data(){
      return {
        imageList:[],
        uploadFileConfig: {},

        submit2:{
          reason:'',
          isPass:''
        },
        ifShowImage:false,
        ifShowImage2:false,
        submit:{
          reason:''
        },
        form:{
          client_name:'',
          client_number:'',
          original_client_number:'',
        },
        selectTab1: 'base',
        selectTab2: 'content',
        selectTab3: 'content',
        queryItems: [],
        imgList: [],
        imgList2: [],
        initParam:{}
      }
    },

    async  created() {
      this.form = this.params.row;
      this.getContent()
      
    },
    methods:{
       async saveZip(list){
            if(!list.length) {
                this.$message({
                    message: '没有可下载的附件',
                    type: 'warning'
                })
                return
            }

            const res = await downloadZip(list)
        },
       closeShowImage() {
            this.ifShowImage = false;
        },
        showimgList(row, item) {
          console.log(row,item)
            this.ifShowImage = true;
          this.initImageList(row, item);
        },
      
        //初始化预览列表
        initImageList(row, items) {
            let imglist = [];
            //过滤出只有图片的列表
            let list = row.filter((item) =>
                this.isImage(item.path)
            );
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = value.path;
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = row.creator_nick;
                obj.isPucture = true;
                //确定要预览那张图片
                if (value.cloud_file_id === items.cloud_file_id) {
                    obj.active = true;
                } else {
                    obj.active = false;
                }
                imglist.push(obj);
            });
            this.imageList = imglist;
        },
        isImage(str) {
            var reg = /\.(jpg|png|jpeg|gif|JPG|PNG|JPEG|GIF)$/i;
            return reg.test(str);
        },
       supplyInfo(d){
        this.$root.eventHandle.$emit('creatTab', {
          name: '补单详情',
          component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
          params: {
            client_number: d.client_number,
          }
        })
      },
   detail(d) {
     console.log(d);
      this.$root.eventHandle.$emit("creatTab", {
        name: "订单详情",
        component: () =>
          import("@components/dz_customer/clientInfo/clientInfo.vue"),
        params: {
          customerInfo: {client_number:d.original_client_number},
          lastTab: this.params.tabName,
        },
      });
    },
    
     getImgList(){
       let postData = {
          order_no:this.form.custom_appeal_record_id,
          page_size:1000,
          sub_order_no: ""
       }
       this.ajax.postStream('/file-iweb/api/cloud/file/list', postData, res => {
				if(res.body.result){
					this.imgList = res.body.content.list;
				}else {
					this.$message.error(res.body.msg)
				}
			})
     },
     getImgList2(){
       let postData = {
          order_no:this.submit.reject_id,
          page_size:1000,
          sub_order_no: ""
       }
       this.ajax.postStream('/file-iweb/api/cloud/file/list', postData, res => {
				if(res.body.result){
					this.imgList2 = res.body.content.list;
				}else {
					this.$message.error(res.body.msg)
				}
			})
     },
     getContent(){
       let postData = {
         custom_appeal_record_id:this.form.custom_appeal_record_id,
       }
       this.ajax.postStream('/custom-web/api/appeal/getOne', postData, res => {
				if(res.body.result){
					this.submit = res.body.content;
          this.getImgList()
          this.getImgList2()
				}else {
					this.$message.error(res.body.msg)
				}
			})
     },
    
     
    }
  }
</script>
<style scoped>
  .link{
    cursor: pointer;
    color: #0000EE;
  }
</style>