<!--责任分析单详情(是以一个售后单为维度)-->isEdit
<!--(定制责任单详情)-->
<template>
<div class="xpt-flex">
	<el-row class="xpt-top" :gutter="40">
		<el-col :span="18">
			<el-button type="primary" size="mini" @click="() => getOrderDetail()">刷新</el-button>
		</el-col>
		<el-col :span="6" style="text-align: right;">
			<el-button type="primary" size="mini" @click="nextOrPrevOrder('prev')" :disabled="!params.orderList">上一页</el-button>
			<el-button type="primary" size="mini" @click="nextOrPrevOrder('next')" :disabled="!params.orderList">下一页</el-button>
		</el-col>
	</el-row>
	<el-form :model='form' ref='form' label-position="right" label-width="120px">
	<el-tabs v-model="selectTab1" @tab-click="selectTab1Click" style="max-height: 280px">
		<el-tab-pane label='责任分析子单' name='subList'>
			<el-row class="xpt-top" :gutter="40">
				<el-button type="primary" size="mini" @click="() => _checkSubmitPerson() && subAjax('submitPerson?permissionCode=ANALYSIS_ORDER_SUBMIT_RESPONSIBLE')" :disabled="isNotControl || !isEdit">提交责任人</el-button>
				<el-button type="danger" size="mini" @click="subAjax('recall?permissionCode=ANALYSIS_ORDER_WITHDRAW')" :disabled="isNotControl || !isEdit">撤回</el-button>
				<el-button type="primary" size="mini" @click="createdCz" :disabled="isNotCreateCz || !isEdit">创建充值单</el-button>
				<!-- <el-button
					type="primary"
					size="mini"
					@click="testFinalConfirm"
					disabled
				>最终确认</el-button>
				<el-button
					type="primary"
					size="mini"
					@click="dealerFinalConfirm"
				>经销责任确认</el-button>
				<el-button
					type="primary"
					size="mini"
					@click="subAjax('manualAdjustment',{manual_adjustment:'Y'})"
				>手动调整金额</el-button> -->
				<!--
					|| !confirmList.length
					|| confirmList.every(obj => obj.status === 'CONFIRMED')
				 -->
				<!-- <el-button type="primary" size="mini" @click="orderAjax('recalculate?permissionCode=ANALYSIS_ORDER_REDISTRIBUTE',{id:form.subList[subList_selectIndex].id})" :disabled="!isEdit">重新分摊</el-button> -->

			</el-row>
			<div class="xpt_pmm_scroll scroll" style="height: 200px;">
			<el-table :data="form.subList" @current-change="subListSelectChange" border tooltip-effect="dark" style="width: 100%;" :class="$style['max-table-height']" width='100%' ref="$subList">
				<el-table-column width="55" align='center'>
					<template slot-scope='scope'>
						<el-radio class='xpt-table__radio' :label="scope.$index" v-model="subList_selectIndex"></el-radio>
					</template>
			   	</el-table-column>
				<el-table-column label="责任分析子单编号" prop="sub_bill_no" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="来源单据类型" prop="original_bill_type" width="120" show-overflow-tooltip>
					<template slot-scope="scope">
						{{ original_bill_type_options[scope.row.original_bill_type] || scope.row.original_bill_type }}
					</template>
				</el-table-column>
				<el-table-column label="来源单据编号" prop="original_bill_no" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="业务员" width="100" show-overflow-tooltip>
					<template slot-scope="scope">{{ form.aftersaleOrder.staff_name }}</template>
				</el-table-column>
				<el-table-column label="是否手动调整金额" width="120" prop="manual_adjustment" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.manual_adjustment =="Y" ? '是':'否'  }}</template>
				</el-table-column>
				<el-table-column label="分摊状态" width="120" prop="fee_status" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.fee_status =="UNFINISHED" ? '未分摊':'已分摊'  }}</template>
				</el-table-column>
				<el-table-column label="售后处理人" prop="aftersale_processor_name" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="责任状态" prop="liability_status" width="120" show-overflow-tooltip>
					<template slot-scope="scope">{{ liability_status_options[scope.row.liability_status] }}</template>
				</el-table-column>
				<el-table-column label="责任确认时间" prop="liability_confirm_time" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.liability_confirm_time | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="锁定人" prop="locker_name" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="锁定人分组" prop="locker_group" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="子单费用" prop="sub_fee" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="资料提交状态" prop="info_submit_status" width="120" show-overflow-tooltip>
					<template slot-scope="scope">{{ info_submit_status_options[scope.row.info_submit_status] || scope.row.info_submit_status }}</template>
				</el-table-column>
				<el-table-column label="资料请求时间" prop="request_submit_time" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.request_submit_time | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="资料提交时间" prop="info_submit_time" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.info_submit_time | dataFormat1 }}</template>
				</el-table-column>
			</el-table>
			</div>
		</el-tab-pane>
		<el-tab-pane label='问题' name='questionList'>
			<el-row class="xpt-top" :gutter="40">
				<el-col :span="18">
					<el-button type="success" size="mini" @click="saveQuestion"
						:disabled="isNotControl || (!isEdit || questionList_selectRow.some(obj => obj.question_type === 'NORMAL') || !questionList_selectRow.length)"
					>保存</el-button>
					<el-button-group>
						<el-button type="primary" :disabled="isNotControl" @click="uploadAction('post')" size="mini">上传</el-button><!-- 取消上传附件关联问题 by 文华 -->
						<el-button type="primary" @click="uploadAction('get')" size="mini">查看附件</el-button>
					</el-button-group>
					<el-button type="primary" size="mini" @click="supplyQuestion" :disabled="isNotControl || !isEdit || !(questionList_selectRow.length === 1 && questionList_selectRow[0].question_type === 'NORMAL')">问题补充</el-button>
					<el-button type="danger" size="mini" @click="delSupplyQuestion" :disabled="isNotControl || !isEdit || !questionList_selectRow.length || questionList_selectRow.some(obj => obj.question_type === 'NORMAL')">删除</el-button>
					<el-button type="primary" @click="getCustomSupplyDataNew()" size="mini">补件信息</el-button>
				</el-col>
			</el-row>
      <div style="height: 200px;">
			<el-table
				:data="questionList"
				@selection-change="questionListSelectChange"
				border
				tooltip-effect="dark"
				style="width: 100%;"
				width='100%'
				@row-click="row => $refs.$questionList.toggleRowSelection(row)"
				:row-class-name="row => row.supply_question_id ? $style['sub-question'] : ''"
				:class="$style['max-table-height']"
				ref="$questionList"
			>
				<!-- <el-table-column width="100" align='center'>
					<template slot-scope='scope'>
						<el-radio :class="'xpt-table__radio ' + (scope.row.supply_question_id ? $style['sub-question'] : '')" :label="scope.$index" v-model="questionList_selectIndex"></el-radio>
					</template>
							   	</el-table-column> -->
			   	<el-table-column type="selection" width="100" align="center"></el-table-column>
			   	<el-table-column label="买家昵称" prop="buyer_nick" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="商品编码" prop="goods_code" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="商品名称" prop="goods_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="规格描述" prop="specification" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="问题描述" prop="question_description" width="200" show-overflow-tooltip>
					<template slot-scope="scope">
						<div style="position:relative;">
							<el-input
								style="position:absolute;width:100%;"
								size='mini'
								v-model="scope.row.question_description"
								:disabled="scope.row.question_type === 'NORMAL'"
								@click.native="e => e.stopPropagation()"
							></el-input>
							<span style="color:#fff;">{{ scope.row.question_description }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="上游业务备注" width="200" show-overflow-tooltip>
					<template slot-scope="scope">{{ form.subList[subList_selectIndex].remark }}</template>
				</el-table-column>
				<el-table-column label="发货日期" prop="out_stock_time" width="170" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.out_stock_time | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="批号" prop="lot" width="230" show-overflow-tooltip></el-table-column>
				<el-table-column label="供应商" prop="suppiler_name" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="服务商" prop="logistics_supplier_name" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="直达承运商" prop="zd_delivery_logistics_company" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="转运承运商" prop="zd_trans_ship_logistics_company" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="中转承运商" prop="zd_change_trains_logistics_company" width="170" show-overflow-tooltip></el-table-column>
				<el-table-column label="采购入库日期" prop="instock_date" width="170" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.instock_date | dataFormat1 }}</template>
				</el-table-column>
				<el-table-column label="问题类型" prop="question_type" width="150" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.question_type === 'NORMAL' ? '普通问题' : '补充问题' }}</template>
				</el-table-column>
				<el-table-column label="BOM版本" prop="bom_version" width="100" show-overflow-tooltip></el-table-column>
				<el-table-column label="业务员" prop="saleman_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="units" show-overflow-tooltip></el-table-column>
				<el-table-column label="实际售价" prop="act_price" show-overflow-tooltip></el-table-column>
				<el-table-column label="标准售价" prop="price" show-overflow-tooltip></el-table-column>
				<el-table-column label="业务员分组" prop="saleman_group" show-overflow-tooltip></el-table-column>
				<el-table-column label="店铺" prop="shop_name" show-overflow-tooltip></el-table-column>
				<el-table-column label="销售单号" prop="sys_trade_no" width="230" show-overflow-tooltip></el-table-column>
				<el-table-column label="批次单号" prop="batch_trade_no" width="230" show-overflow-tooltip></el-table-column>
				<el-table-column label="分单号" prop="outbound_notice_no" width="230" show-overflow-tooltip></el-table-column>
				<el-table-column label="送货方式" prop="deliver_method" width="130" show-overflow-tooltip>
					<template slot-scope="scope">{{ scope.row.deliver_method | deliverMethod }}</template>
				</el-table-column>
			</el-table>
      </div>
		</el-tab-pane>
		<el-tab-pane label='责任分析单' name='_index'>
			<el-row class="xpt-top" :gutter="40">
				<el-col :span="18">
					<el-button type="primary" :disabled="isNotControl" size="mini" @click="orderAjax('lockBatch?permissionCode=ANALYSIS_ORDER_LOCK', [params.id])">锁定</el-button>
					<el-button type="primary" size="mini" @click="passOrder" :disabled="isNotControl || !isEdit">转交</el-button>
					<el-button type="primary" size="mini" @click="orderAjax('close?permissionCode=ANALYSIS_ORDER_CLOSE')" :disabled="isNotControl || !isEdit">关闭</el-button>
					<el-button type="primary" size="mini" @click="orderAjax('recalculate?permissionCode=ANALYSIS_ORDER_REDISTRIBUTE')" :disabled="isNotControl || !isEdit">重新分摊</el-button>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="6">
					<el-form-item label="责任分析单编号">
						<el-input size='mini' :value="form.bill_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="合并订单号">
						<el-input size='mini' :value="form.aftersaleOrder.merge_trade_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="买家昵称">
						<el-input size='mini' :value="form.aftersaleOrder.buyer_nick" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="业务员">
						<el-input size='mini' :value="form.aftersaleOrder.staff_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="业务员分组">
						<el-input size='mini' :value="form.aftersaleOrder.staff_group_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="来源单据类型">
						<el-input size='mini' :value="original_bill_type_options[form.original_bill_type] || form.original_bill_type" :disabled="true"></el-input>
					</el-form-item>
					<!-- <el-form-item label="来源单据日期">
						<el-date-picker
							value=""
							:disabled="true"
							:editable="false"
						    type="date"
						    size="mini">
						</el-date-picker>
					</el-form-item> -->
					<el-form-item label="来源单据处理时间">
						<el-date-picker
							:value="form.original_bill_process_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="结算主体">
						<el-input size='mini' :value="form.aftersaleOrder.settle_entity" :disabled="true"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="售后单号">
						<el-input size='mini' :value="form.aftersaleOrder.after_order_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="售后单日期">
						<el-date-picker
							:value="form.aftersaleOrder.after_order_time"
							:disabled="true"
							:editable="false"
						    type="date"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="售后处理人">
						<el-input size='mini' :value="form.aftersale_processor_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="售后处理人分组">
						<el-input size='mini' :value="form.aftersale_processor_group_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="费用总额">
						<el-input size='mini' :value="form.total_fee" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="创建人">
						<el-input size='mini' :value="form.creator_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="关闭人">
						<el-input size='mini' :value="form.closer_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="关闭时间">
						<el-date-picker
							:value="form.close_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="单据状态">
						<el-input size='mini' :value="{ CREATE: '创建', CLOSED: '关闭', }[form.status]" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="责任状态">
						<el-input size='mini' :value="{ WAIT_CONFIRM: '待确认', CONFIRMED: '已确认' }[form.liability_status]" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="责任确认时间">
						<el-date-picker
							:value="form.liability_confirm_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="费用状态">
						<el-input size='mini' :value="{ UNFINISHED: '未完结', FINISHED: '已完结', DIVIDED: '已分摊', }[form.fee_status]" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定人">
						<el-input size='mini' :value="form.locker_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定人分组">
						<el-input size='mini' :value="form.locker_group" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定时间">
						<el-date-picker
							:value="form.lock_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="结算方式">
						<!-- <el-input size='mini' :value="form.aftersaleOrder.settle_entity" :disabled="true"></el-input> -->
						<xpt-select-aux v-model='form.settle_method' aux_name='settle_method' :disabled="true" ></xpt-select-aux>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="是否经销商订单">
						<el-input size='mini' :value="{ Y: '是', N: '否', }[form.if_dealer]" disabled></el-input>
					</el-form-item>
					<el-form-item label="经销商编码">
						<el-input size='mini' :value="form.dealer_customer_number" disabled></el-input>
					</el-form-item>
					<el-form-item label="经销商名称">
						<el-input size='mini' :value="form.dealer_customer_name" disabled></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-tab-pane>
		<el-tab-pane label='子单详细信息' name='_subList_detail'>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="责任分析子单编号">
						<el-input size='mini' :value="form.subList[subList_selectIndex].sub_bill_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="合并订单号">
						<el-input size='mini' :value="form.aftersaleOrder.merge_trade_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="买家昵称">
						<el-input size='mini' :value="form.aftersaleOrder.buyer_nick" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="业务员">
						<el-input size='mini' :value="form.aftersaleOrder.staff_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="业务员分组">
						<el-input size='mini' :value="form.aftersaleOrder.staff_group_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="责任状态">
						<el-input size='mini' :value="liability_status_options[form.subList[subList_selectIndex].liability_status]" :disabled="true"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="责任分析单编号">
						<el-input size='mini' :value="form.subList[subList_selectIndex].parent_bill_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="售后单号">
						<el-input size='mini' :value="form.aftersaleOrder.after_order_no" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="售后单日期">
						<el-date-picker
							:value="form.aftersaleOrder.after_order_time"
							:disabled="true"
							:editable="false"
						    type="date"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="售后处理人">
						<el-input size='mini' :value="form.subList[subList_selectIndex].aftersale_processor_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="售后处理人分组">
						<el-input size='mini' :value="form.subList[subList_selectIndex].aftersale_processor_group_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="责任确认时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].liability_confirm_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="锁定人">
						<el-input size='mini' :value="form.subList[subList_selectIndex].locker_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定人分组">
						<el-input size='mini' :value="form.subList[subList_selectIndex].locker_group" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="锁定时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].lock_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="子单费用">
						<el-input size='mini' :value="form.subList[subList_selectIndex].sub_fee" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="提交时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].submit_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="撤回时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].recall_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
		</el-tab-pane>
		<el-tab-pane label='子单其它信息' name='_subList_other'>
			<el-row class="xpt-top" :gutter="40">
				<el-col :span="18">
					<el-button type="success" size="mini" @click="subAjax('save?permissionCode=ANALYSIS_ORDER_DATA_SAVE')" :disabled="isNotControl || (/(WAIT_CONFIRM|WAIT_CONDFIRM|CONFIRMED)/).test(form.subList[subList_selectIndex].liability_status)">保存</el-button>
					<el-button
						type="primary"
						size="mini"
						@click="infoReq"
						:disabled="isNotControl || !isEdit || /(WAIT_CONFIRM|WAIT_CONDFIRM|CONFIRMED)/.test(form.subList[subList_selectIndex].liability_status)"
					>资料请求</el-button>
					<el-button
						type="primary"
						size="mini"
						@click="submitInfoReq"
						:disabled="
							isNotControl ||  /(WAIT_CONFIRM|WAIT_CONDFIRM|CONFIRMED)/.test(form.subList[subList_selectIndex].liability_status)
						"
					>提交资料</el-button>
							<!-- !(//子单(售后处理人/创建人/业务员) or 分析单售后处理人 = 操作人才能操作
								getEmployeeInfo('id') === form.subList[subList_selectIndex].aftersale_processor ||
								getEmployeeInfo('id') === form.subList[subList_selectIndex].creator ||
								getEmployeeInfo('id') === form.aftersaleOrder.staff ||
								getEmployeeInfo('id') === form.aftersale_processor
							) -->
					<el-button type="danger" size="mini" @click="subAjax('recallInfoReq?permissionCode=ANALYSIS_ORDER_WITHDRAE_DATA_REQUEST')" :disabled="isNotControl || !isEdit || /(WAIT_CONFIRM|WAIT_CONDFIRM|CONFIRMED)/.test(form.subList[subList_selectIndex].liability_status)">撤回资料请求</el-button>
					<el-button type="primary" size="mini" @click="subAjax('confirmInfoReq?permissionCode=ANALYSIS_ORDER_DATA_CONFIRM')" :disabled="isNotControl || !isEdit || /(WAIT_CONFIRM|WAIT_CONDFIRM|CONFIRMED)/.test(form.subList[subList_selectIndex].liability_status)">确认资料</el-button>
					<el-button type="primary" size="mini" @click="subAjax('rejectInfoReq?permissionCode=ANALYSIS_ORDER_DATA_REJECT')" :disabled="isNotControl || !isEdit || /(WAIT_CONFIRM|WAIT_CONDFIRM|CONFIRMED)/.test(form.subList[subList_selectIndex].liability_status)">驳回资料</el-button>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="资料提交状态">
						<el-input size='mini' :value="info_submit_status_options[form.subList[subList_selectIndex].info_submit_status] || form.subList[subList_selectIndex].info_submit_status" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="资料请求时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].request_submit_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="资料提交时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].info_submit_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="调拨单">
						<el-input
							size='mini'
							v-model="form.subList[subList_selectIndex].bill_adjustment_no"
							:disabled="
								form.subList[subList_selectIndex].info_submit_status !== 'WAIT_PROVIDE'
							"
						></el-input>
								<!-- !(//子单(售后处理人/创建人/业务员) or 分析单售后处理人 = 操作人才能操作
									getEmployeeInfo('id') === form.subList[subList_selectIndex].aftersale_processor ||
									getEmployeeInfo('id') === form.subList[subList_selectIndex].creator ||
									getEmployeeInfo('id') === form.aftersaleOrder.staff ||
									getEmployeeInfo('id') === form.aftersale_processor
								) -->
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="来源单据类型">
						<el-input size='mini' :value="original_bill_type_options[form.subList[subList_selectIndex].original_bill_type] || form.subList[subList_selectIndex].original_bill_type" :disabled="true"></el-input>
					</el-form-item>
					<!-- <el-form-item label="来源单据日期">
						<el-date-picker
							value=""
							:disabled="true"
							:editable="false"
						    type="date"
						    size="mini">
						</el-date-picker>
					</el-form-item> -->
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="16">
					<el-form-item label="资料请求说明" :class="$style['textarea-style']">
						<el-input
							type="textarea"
							size='mini'
							v-model="form.subList[subList_selectIndex].info_require_remark"
							:maxlength="500"
							:disabled="
								!isEdit ||
								/(WAIT_PROVIDE|CONFIRMED)/.test(form.subList[subList_selectIndex].info_submit_status)
							"
						></el-input>
					</el-form-item>
					<el-form-item label="资料提交说明" :class="$style['textarea-style']">
						<el-input
							type="textarea"
							size='mini'
							v-model="form.subList[subList_selectIndex].info_submit_remark"
							:maxlength="1000"
							:disabled="
								form.subList[subList_selectIndex].info_submit_status !== 'WAIT_PROVIDE'
							"
						></el-input>
								<!-- !(//子单(售后处理人/创建人/业务员) or 分析单售后处理人 = 操作人才能操作
									getEmployeeInfo('id') === form.subList[subList_selectIndex].aftersale_processor ||
									getEmployeeInfo('id') === form.subList[subList_selectIndex].creator ||
									getEmployeeInfo('id') === form.aftersaleOrder.staff ||
									getEmployeeInfo('id') === form.aftersale_processor
								) -->
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="来源单据处理时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].original_bill_process_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="创建人">
						<el-input size='mini' :value="form.subList[subList_selectIndex].creator_name" :disabled="true"></el-input>
					</el-form-item>
					<el-form-item label="创建时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].create_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
					<el-form-item label="终止时间">
						<el-date-picker
							:value="form.subList[subList_selectIndex].end_time"
							:disabled="true"
							:editable="false"
						    type="datetime"
						    size="mini">
						</el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
		</el-tab-pane>
		<el-tab-pane label='补单详情' name='sypplyInfo'>
			<xpt-form :cols='sypplyInfoCols' :data='supplyInfoData' label='110px'>
				<template slot='client_mobile'>
          			<xpt-eye-switch v-model="supplyInfoData.client_mobile" :readonly="true" :hideBorder="true" width="100" :aboutNumber="form.subList[subList_selectIndex].sub_bill_no"></xpt-eye-switch>
          		</template>
			</xpt-form>
		</el-tab-pane>
	</el-tabs>
	</el-form>
	<el-row class='xpt-flex__bottom' id='bottom' v-fold>
		<el-tabs v-model="selectTab2" @tab-click="selectTab2Click">
			<el-tab-pane label='责任人' name='personList' class='xpt-flex'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="18">
						<el-button type="success" size="mini" @click="savePersonList" :disabled="isNotControl || !isEdit">保存</el-button>
						<el-button
							type="primary"
							size="mini"
							@click="() => addPerson()"
							:disabled="
								isNotControl ||
								selectTab1 !== 'questionList'
								|| !isEdit
								|| !/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status)//子单责任状态必须为待提交或已撤回才能编辑
								|| questionList_selectRow.length !== 1
							"
						>添加责任人</el-button>
						<el-button
							type="danger"
							size="mini"
							@click="delPerson"
							:disabled="
								isNotControl ||
								personList_selectIndex === ''
								|| !isEdit
								|| !/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status)"
						>删除</el-button>
						<!-- <el-button
							type="primary"
							size="mini"
							@click="copyPerson"
							:disabled="
								selectTab1 !== 'questionList'
								|| !isEdit
								|| questionList_selectRow.length < 2"
						>批量填充责任人</el-button>
						<el-button
							type="primary"
							size="mini"
							@click="getScmInfo"
							:disabled="
							personList_selectIndex === '' || personList_selectIndex<0"
						>添加品质反馈</el-button> -->
					</el-col>
				</el-row>
				<el-table :data="personList" @current-change="currentRow => personList_selectIndex = personList.indexOf(currentRow)" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column width="55" align='center'>
						<template slot-scope='scope'>
							<el-radio class='xpt-table__radio' :label="scope.$index" v-model="personList_selectIndex"></el-radio>
						</template>
				   	</el-table-column>
				   	<el-table-column type="index" width="50" label="序号"></el-table-column>
					<el-table-column label="责任问题" prop="liability_question_name" width="250" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="!/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status) || !isEdit">{{ scope.row.liability_question_name }}</span>
							<el-input
								v-else
								:value="scope.row.liability_question_name"
								size='mini'
								:icon="selectTab1 === 'questionList' ? 'search' : ''"
								readonly
								:on-icon-click="() => addPerson(scope.row)"
								style="width:100%;"
							></el-input><!-- 不在问题页签不能点击icon -->
						</template>
					</el-table-column>
					<el-table-column label="责任类型" prop="liability_type"></el-table-column>
					<el-table-column label="责任人" prop="liability_person_name" width="150" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="!/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status) || !isEdit">{{ scope.row.liability_person_name }}</span>
							<el-input
								v-else
								v-model="scope.row.liability_person_name"
								size='mini'
								:icon="selectTab1 === 'questionList' ? 'search' : ''"
								readonly
								:on-icon-click="() => selectDutyPerson(scope.row.liability_scope, scope.row, scope.row.liability_type)"
								style="width:100%;"
							></el-input><!-- 不在问题页签不能点击icon -->
						</template>
					</el-table-column>
					<el-table-column label="备注" prop="remark" width="300" show-overflow-tooltip>
						<template slot-scope="scope">
							<span v-if="!/(RECALLED|WAIT_SUBMIT)/.test(form.subList[subList_selectIndex].liability_status) || !isEdit">{{ scope.row.remark }}</span>
							<el-input v-else v-model="scope.row.remark" type="textarea" :maxlength="255" style="width:100%;height:100%;" :class="$style['row-height']"></el-input>
						</template>
					</el-table-column>
					<el-table-column label="商品编码" prop="goods_code" width="150" show-overflow-tooltip></el-table-column>
					<el-table-column label="商品名称" prop="goods_name" show-overflow-tooltip></el-table-column>
					<el-table-column label="规格描述" prop="specification" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任金额" prop="liability_amount" width="110">
						<template slot-scope="scope">
							<el-input
								size='mini'
								type="number"
								v-model="scope.row.liability_amount"
								style="width:100%;"
								@blur="e => e.target.value = scope.row.liability_amount = Number(Number(scope.row.liability_amount).toFixed(2))"
								:disabled="!isEdit"
							></el-input>
						</template>
					</el-table-column>
					<el-table-column label="处理金额" prop="handle_amount" width="110">
						<template slot-scope="scope">
							<el-input
								size='mini'
								type="number"
								v-model="scope.row.handle_amount"
								style="width:100%;"
								@blur="e => e.target.value = scope.row.handle_amount = Number(Number(scope.row.handle_amount).toFixed(2))"
								:disabled="!isEdit"
							></el-input>
						</template>
					</el-table-column>
					<el-table-column label="责任状态" prop="liability_status" width="150">
						<template slot-scope="scope">{{ liability_status_options[scope.row.liability_status] || scope.row.liability_status }}</template>
					</el-table-column>
					<el-table-column label="责任状态时间" width="100" prop="status_time" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.status_time | dataFormat1 }}</template>
					</el-table-column>
					<!-- <el-table-column label="责任类型编码" prop="liability_type_code"></el-table-column> -->
					<el-table-column label="责任范围" prop="liability_scope" show-overflow-tooltip>
						<template slot-scope="scope">{{ liability_scope_option[scope.row.liability_scope] }}</template>
					</el-table-column>
					<el-table-column label="责任问题描述" prop="liability_question_description" width="250" show-overflow-tooltip></el-table-column>
					<el-table-column label="是否生成纠纷单" width="100" prop="if_create_dispute_bill">
						<template slot-scope="scope">{{ { 1: '是', 0: '否', true: '是', false: '否', }[scope.row.if_create_dispute_bill] }}</template>
					</el-table-column>
					<el-table-column label="处理状态" prop="handle_status"></el-table-column>
					<el-table-column label="处理时间" prop="handle_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.handle_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="责任确认方式" prop="confirm_way" width="150">
						<template slot-scope="scope">{{ confirm_way_options[scope.row.confirm_way] || scope.row.confirm_way }}
							<!-- <el-select v-model="scope.row.confirm_way" size="mini" placeholder="请选择" style="width:100%;">
								<el-option
									v-for="(value,key) in confirm_way_options"
									:key="key.toString()"
									:label="value.toString()"
									:value="key.toString()">
								</el-option>
							</el-select> -->
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane v-if="!tabDisabled" label='费用明细' name='detailList' class='xpt-flex'>
				<el-table :data="form.detailList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column type="index" width="50" label="序号"></el-table-column>
					<el-table-column label="费用项目" prop="fee_item" show-overflow-tooltip></el-table-column>
					<el-table-column label="费用估算" prop="fee_estimate" show-overflow-tooltip></el-table-column>
					<el-table-column label="费用金额" prop="fee_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="调整金额" prop="adjust_amount" show-overflow-tooltip></el-table-column>
					<el-table-column label="发生日期" prop="happen_date" width="150" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.happen_date | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="来源单据类型" prop="original_bill_type" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ original_bill_type_options[scope.row.original_bill_type] || scope.row.original_bill_type }}
						</template>
					</el-table-column>
					<el-table-column label="来源单据编号" prop="original_bill_no" width="150" show-overflow-tooltip></el-table-column>
					<!-- <el-table-column label="来源单据行号" prop="original_bill_row" show-overflow-tooltip></el-table-column> -->
					<el-table-column label="归集单据类型" prop="collect_bill_type" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ collect_bill_type_options[scope.row.collect_bill_type] || scope.row.collect_bill_type }}
						</template>
					</el-table-column>
					<el-table-column label="归集单据编号" prop="collect_bill_no" width="150" show-overflow-tooltip></el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='申诉记录' name='confirmHisttoryList' class='xpt-flex'>
				<el-table :data="confirmHisttoryList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column label="责任通知编号" prop="confirm_no" width="200" show-overflow-tooltip></el-table-column>
					<!-- <el-table-column label="批次订单号" prop="batch_trade_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任范围" prop="liability_scope" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ liability_scope_option[scope.row.liability_scope] || scope.row.liability_scope }}</template>
					</el-table-column> -->
					<!-- <el-table-column label="责任类型" prop="liability_type" width="120" show-overflow-tooltip></el-table-column> -->
					<el-table-column label="责任人" prop="liability_person_name" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="状态" prop="status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ {UN_CONFIRM: '未确认',CONFIRMED: '已确认',COMPLAIN: '申诉'}[scope.row.status] || liability_status_options[scope.row.status] ||  scope.row.status }}</template>
					</el-table-column>
					<!-- <el-table-column label="回复时间" prop="reply_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.reply_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="撤回状态" prop="revoke_status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ {N: '无撤回',Y: '需要撤回',C: '执行撤回成功'}[scope.row.revoke_status] || scope.row.revoke_status }}</template>
					</el-table-column>
					<el-table-column label="撤回结果" prop="revoke_result" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任确认方式" prop="confirm_way" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ confirm_way_options[scope.row.confirm_way] }}</template>
					</el-table-column> -->
					<!-- <el-table-column label="备注" prop="remark" width="120" show-overflow-tooltip></el-table-column> -->
					<el-table-column label="申诉备注" prop="reply_remark" show-overflow-tooltip></el-table-column>
					<el-table-column label="申诉时间" prop="back_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.back_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="查看附件" show-overflow-tooltip>
						<template slot-scope="scope">
							<a
								v-bind:href="
									'javascript:window.open(`http://'
										+ K3LiabilityAnalysisPicHost + '/Modules/AfterSale/K3LiabilityAnalysisPic.html?'
										+ 'fbillno=' + JSON.parse((recordList[scope.$index] || {}).transmit_result || '{}').content
										+ '&time=`+(+new Date));'
								"
							>{{ JSON.parse((recordList[scope.$index] || {}).transmit_result || '{}').content }}</a>
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='操作记录' name='operateLogList' class='xpt-flex'>
					<el-table :data="operateLogList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
						<el-table-column label="序号" type="index" width="70">
							<template slot-scope="scope"><div class="table-index">{{ scope.$index + 1 }}</div></template>
						</el-table-column>
						<el-table-column label="用户" 		prop="operator_name" width="200" show-overflow-tooltip></el-table-column>
						<el-table-column label="业务操作" 	prop="operate_type" width="200" show-overflow-tooltip>
							<template slot-scope="scope">
								{{ operate_type_options[scope.row.operate_type] || scope.row.operate_type }}
							</template>
						</el-table-column>
						<el-table-column label="操作描述" 	prop="description" width="400" show-overflow-tooltip></el-table-column>
						<el-table-column label="操作时间"   prop="operate_time" width="200" show-overflow-tooltip>
							<template slot-scope="scope">
								<span>{{scope.row.operate_time | dataFormat1}}</span>
							</template>
						</el-table-column>
					</el-table>
			</el-tab-pane>
			<el-tab-pane label='责任确认通知' name='confirmList' class='xpt-flex'>
				<el-table :data="confirmList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column label="责任通知编号" prop="confirm_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="批次订单号" prop="batch_trade_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任范围" prop="liability_scope" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ liability_scope_option[scope.row.liability_scope] || scope.row.liability_scope }}</template>
					</el-table-column>
					<!-- <el-table-column label="责任类型" prop="liability_type" width="120" show-overflow-tooltip></el-table-column> -->
					<el-table-column label="责任人" prop="liability_person_name" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="状态" prop="status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ {UN_CONFIRM: '未确认',CONFIRMED: '已确认',COMPLAIN: '申诉'}[scope.row.status] || liability_status_options[scope.row.status] ||  scope.row.status }}</template>
					</el-table-column>
					<el-table-column label="回复时间" prop="reply_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.reply_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="撤回状态" prop="revoke_status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ {N: '无撤回',Y: '需要撤回',C: '执行撤回成功'}[scope.row.revoke_status] || scope.row.revoke_status }}</template>
					</el-table-column>
					<el-table-column label="撤回结果" prop="revoke_result" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="责任确认方式" prop="confirm_way" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ confirm_way_options[scope.row.confirm_way] }}</template>
					</el-table-column>
					<el-table-column label="备注" prop="remark" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="回传时间" prop="back_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.back_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="回传备注" prop="reply_remark" width="250" show-overflow-tooltip></el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='责任下发记录' name='recordList' class='xpt-flex'>
				<el-row class="xpt-top" :gutter="40">
					<el-col :span="18">
						<el-button type="primary" :disabled="isNotControl" size="mini" @click="confirmResend">重新传送</el-button>
					</el-col>
				</el-row>
				<el-table :data="recordList" border tooltip-effect="dark" style="width: 100%;" width='100%' @selection-change="handleSelectionChange">
					<el-table-column type="selection" width="50"></el-table-column>
					<el-table-column label="责任确认通知编号" prop="confirm_no" width="200" show-overflow-tooltip></el-table-column>
					<el-table-column label="批次订单号" prop="batch_trade_no" width="240" show-overflow-tooltip></el-table-column>
					<el-table-column label="接口状态" prop="api_status" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ api_status_options[scope.row.api_status] || scope.row.api_status }}</template>
					</el-table-column>
					<el-table-column label="下达时间" prop="transmit_time" width="200" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.transmit_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="传送结果提示" prop="transmit_result" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ JSON.parse(scope.row.transmit_result || '{}').info }}
						</template>
					</el-table-column>
					<el-table-column label="查看附件" show-overflow-tooltip>
						<template slot-scope="scope">
							<a
								v-bind:href="
									'javascript:window.open(`http://'
										+ K3LiabilityAnalysisPicHost + '/Modules/AftrSale/K3LiabilityAnalysisPic.html?'
										+ 'fbillno=' + JSON.parse(scope.row.transmit_result || '{}').content
										+ '&time=`+(+new Date));'
								"
							>{{ JSON.parse(scope.row.transmit_result || '{}').content }}</a>
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label='分摊日志' name='recalculateList' class='xpt-flex'>

				<el-table :data="recalculateList" border tooltip-effect="dark" style="width: 100%;" width='100%'>
					<el-table-column type="selection" width="50"></el-table-column>
					<el-table-column label="责任分析子单编号" prop="sub_no" width="120" show-overflow-tooltip></el-table-column>
					<el-table-column label="分摊结果" prop="result" width="120" show-overflow-tooltip>
						<template slot-scope="scope">{{ result_options[scope.row.result] || scope.row.result }}</template>
					</el-table-column>
					<el-table-column label="日志生成时间" prop="create_time" width="220" show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.create_time | dataFormat1 }}</template>
					</el-table-column>
					<el-table-column label="未执行分摊的原因" prop="reason"  show-overflow-tooltip>
						<template slot-scope="scope">{{ scope.row.reason  }}</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
			<!-- <el-tab-pane label='品质反馈' name='feedbackList' class='xpt-flex'>
				<xpt-list
				:data="feedbackList"
				:btns="feedbackBtns"
				:colData="feedbackCols"
				selection="radio"
				:orderNo="true"
				@search-click='feedbackSearchClick'
				@radio-change='feedbackSelectionChange'
			></xpt-list>
			</el-tab-pane> -->
		</el-tabs>
	</el-row>
	<xpt-upload :ifClickUpload="ifClickUpload" :dataObj="uploadData"></xpt-upload>
</div>
</template>

<script>
//from里面的数组字段值的意思
//aftersaleOrder=>费用明细列表
//detailList=>费用明细列表
//confirmList=>
//detailList=>费用明细列表
//personList=>责任人
//questionList=>问题列表
//recordList=>下发记录
//subList=>
import Vue from 'vue'
import Fn from '@common/Fn.js'
import { getMap as getClientMap } from '../../common/clientDictionary'
export default {
	props:['params'],
	data (){
		let self = this;
		return {
			isEdit: false,//锁定人 = 操作人 或代理人关系 或锁定人分组的管理人
			ifClickUpload: false,
			uploadData: {},
			api_status_options: {
				NO_NEED: '无需下达',
		        WAIT: '等待',
		        TRANSMITING: '下达中',
		        TRANSMITTED: '已下达',
		        TRANSMIT_FAIL: '下达失败',
		        RECALLING: '撤回中',
		        RECALLED: '已撤回',
		        RECALL_FAIL: '撤回失败',
		        CALLBACK: '已回传',
			},
			result_options:{
				SUCCESS:'分摊成功',
				FAILE:'未执行分摊',
			},
			info_submit_status_options: {
				NO_NEED: '无需提供',
		        WAIT_PROVIDE: '等待提交',
		        PROVIDED: '提交完成',
		        CONFIRMED: '确认',
		        OVERTIME: '超时交馈',
			},
			liability_status_options: {
				APPEAL: '申诉',
				WAIT_SUBMIT: '待提交',
		        WAIT_CONFIRM: '待确认',
		        COMPLAIN: '申诉',
		        END: '终止',
		        WAIT_REVOKE: '待撤回',
		        RECALLING: '撤回中',
		        RECALLED: '已撤回',
		        RECALL_FAIL: '撤回失败',
				WAIT_CONDFIRM: '待确认',
		        CONFIRMED: '已确认',
			},
			original_bill_type_options: {
				RETURNS: '退换货跟踪单',
				SUPPLY: '补件单',
				REFUND: '退款单',
				AFTERSALES: '售后单',
				AFTERSALE_ORDER: '售后单',
				REPAIR: '服务单',
				SERVICE: '4PL平台服务单',

				RETURNS_BILL: '退换货跟踪单',
				SUPPLY_BILL: '补件单',
				REFUND_BILL: '退款单',
				AFTERSALES_BILL: '售后单',
				AFTERSALE_ORDER_BILL: '售后单',
				REPAIR_BILL: '服务单',
				SERVICE_BILL: '4PL平台服务单',

				OUTBOUNDSUPPLY: '补件其他出库单',
				ANALYSIS: '责任分析单',
				BATCH: '批次订单',

				BOUGHT_SUPPLY: '采购订单',
				REFUND_NEW: '新退款单',
			},
			liability_scope_option: {
				BD_Empinfo: '员工',
				BD_Supplier: '供应商',
				BD_Customer: '客户',
				BD_Department: '部门',
			},
			confirm_way_options: {
				NORMAL: '正常',
				OVERTIME: '超时',
				JUDGE: '裁定',
			},
			collect_bill_type_options: {
				REFUND_APPLY_BILL: '退款申请单',
		        RETURNS_FOLLOW_BILL: '退货跟踪单',
		        OTHER_DELIVERY_BILL: '其它出库单',
		        SERVICE_BILL: '服务单',

		        RETURNS_BILL: '退换货跟踪单',
				SUPPLY_BILL: '补件单',
				REFUND_BILL: '退款单',
				AFTERSALES_BILL: '售后单',
				AFTERSALE_ORDER_BILL: '售后单',
				REPAIR_BILL: '服务单',

				/*自译*/
				REFUND_APPLY: '退款申请单',
		        RETURNS_FOLLOW: '退货跟踪单',
		        OTHER_DELIVERY: '其它出库单',
		        SERVICE: '服务单',
		        RETURNS: '退换货跟踪单',
				SUPPLY: '补件单',
				REFUND: '退款单',
				AFTERSALES: '售后单',
				AFTERSALE_ORDER: '售后单',
				REPAIR: '服务单',
				REFUND_NEW: '新退款单',
			},
			operate_type_options: {
				CREATE: '新增',
				SAVE: '保存',
				LOCK: '锁定',
				UNLOCK: '解锁',
				SUBMIT: '提交',
				RETRACT: '撤回',
				AUDIT: '审核',
				CHANGE: '确认变更',
				REVERSE_AUDIT: '反审核',
				SUBMIT_APPROVE: '提交审批',
				PASS: '审批通过',
				NOT_PASS: '审批不通过',
				TRACK: '执行跟踪',
				RECALL: '撤回仓储',
				CLOSE: '关闭',
				OPEN: '反关闭',
				CANCEL: '已取消',
				//extra
				ADD_REFUND_ITEM: '引入退款明细',
				DELETE_REFUND_ITEM: '删除退款明细',
				ADD_TAOBAO_REFUND: '引入淘宝退款申请',
				DELETE_TAOBAO_REFUND: '删除淘宝退款申请',
				SUBMITPURCHASE: '提交采购',
				PLAN_RETRACT: '方案撤回',
				REJECT: '驳回',
				LOCKER: '锁定',
				REVOCATION: '撤回',
				VERIFY: '审核',
				OPPOSITE_VERIFY: '反审核',
				SUBMIT_EXAMINE: '提交审批',
				PASS_EXAMINE: '审批通过',
				UNPASS_EXAMINE: '审批不通过',
			},
			K3LiabilityAnalysisPicHost: {
				'salesit.linshimuye.com': '************:8082',
				'sit.sale.linshimuye.com': '************:8082',
				'saleuat.linshimuye.com': '************:8086',
				'sale.linshimuye.com': '***********:8082',
			}[window.location.hostname],

			selectTab1: 'subList',
			selectTab2: 'personList',
			subList_selectIndex: 0,//责任分析子单的下标
			personList_selectIndex: '',//选择责任人的下标
			recordList_selectIndex: [],//责任下发记录下标
			questionList_selectRow: [],

			form: {
				aftersaleOrder: {},
				subList: [{}],
			},
			questionList: [],
			personList: [],
			confirmList: [],//根据子单选择不同显示不同通知
			confirmHisttoryList: [],//根据子单选择不同显示不同通知
			recordList: [],
			recalculateList: [],
			operateLogList: [],

			originalData:null,//储存责任分析单原有数据

			merge_trade_id:null,//合并订单ID

			feedbackList:[],
			feedbackBtns:[
				{
					type: 'danger',
					txt: '删除',
					disabled: () => { return false;},
					loading: false,
					click() {
						self.delFeedback();
					}
				},
				{
					type: 'info',
					txt: '提交SCM',
					disabled: () => { return false;},
					loading: false,
					click() {
						self.sendFeedback();
					}
				},
				{
					type: 'info',
					txt: '撤回',
					disabled: () => { return false;},
					loading: false,
					click() {
						self.revokeFeedback();
					}
				},
			],
			feedbackCols:[
				{
					label: '品质反馈单号',
					width: 180,
					prop: 'quality_feedback_no'
				},
				{
					label: '商品编码',
					width: 120,
					prop: 'goods_code'
				},
				{
					label: '状态',
					width: 120,
					prop: 'status',
					formatter(val){
						switch(val){
							case 'CREATE' :return "创建";							case 'TRANSMIT' :return "已下达";							case 'WITHDRAW' :return "已撤回";						}
					}
				},
				{
					label: '责任问题',
					width: 120,
					prop: 'liability_question'
				},
				{
					label: '组别',
					width: 120,
					prop: 'group_name'
				},
				{
					label: '产品类型',
					width: 120,
					prop: 'product_type'
				},
				{
					label: '售后物料',
					width: 120,
					prop: 'aftersale_materials'
				},
				{
					label: '售后情况',
					width: 120,
					prop: 'materials_damage'
				},
				{
					label: '备注',
					width: 180,
					prop: 'remarks'
				},

				{
					label: '创建时间',
					width: 100,
					prop: 'create_time',
					format:'dataFormat1'
				},
				{
					label: '提交时间',
					width: 100,
					prop: 'submit_time',
					format:'dataFormat1'
				},


			],
			feedbackSelect:null,
			isNotControl: false,
			isNotCreateCz: false,
			sypplyInfoCols: [
				[
					{
						label: '客户编号:',
						key: 'client_no',
					},
					{
						label: '补单号:',
						key: 'client_number',
					},
					{
						label: '建档时间:',
						key: 'create_time',
						format: 'dataFormat1'
					},
					{
						label: '设计师:',
						key: 'designer_name'
					},
					{
						label: '省市区:',
						key: 'prin_city_district',
						formatter() {
							return self.supplyInfoData.receiver_state_name+self.supplyInfoData.receiver_city_name+self.supplyInfoData.receiver_district_name
						}
					}
				], [
					{
						label: '客户姓名:',
						key: 'client_name'
					},
					{
						label: '补单订单状态:',
						key: 'client_status',
						formatter(val) {
							return self.clientStatusObj[val]
						}
					},
					{
						label: '建档人:',
						key: 'shopping_guide_name'
					},
					{
						label: '专卖店:',
						key: 'shop_name'
					},
					{
						label: '详细地址:',
						key: 'address_name'
					}
				], [
					{
						label: '客户手机/电话:',
						key: 'client_mobile',
						slot: 'client_mobile'
					},
				]
			],
			supplyInfoData: {},
			clientStatusObj: {},
			userType:"",//用户类型
		}
	},
	methods: {
		delFeedback(){
			let self = this;
			if(!self.feedbackSelect){
				self.$message.error("请选择对应品质反馈");
				return;
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/delete',[self.feedbackSelect.id] , res => {
					if(res.body.result){
						// this.feedbackList = res.body.content || [];
						self.feedbackSearchClick();
						this.$message({message: '操作成功',type: 'success'});
						// self.openInfo(res.body.content,personId);
					}else{
						this.$message({message: res.body.msg,type: 'error'});
					}
				});
		},
		sendFeedback(){
			let self = this;
			if(!self.feedbackSelect){
				self.$message.error("请选择对应品质反馈");
				return;
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/send',self.feedbackSelect.id , res => {
					if(res.body.result){
						// this.feedbackList = res.body.content || [];
						self.feedbackSearchClick();
						this.$message({message: '操作成功',type: 'success'});
						// self.openInfo(res.body.content,personId);
					}else{
						this.$message({message: res.body.msg,type: 'error'});
					}
				});
		},
		revokeFeedback(){
			let self = this;
			if(!self.feedbackSelect){
				self.$message.error("请选择对应品质反馈");
				return;
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/revoke',self.feedbackSelect.id , res => {
					if(res.body.result){
						// this.feedbackList = res.body.content || [];
						self.feedbackSearchClick();
						this.$message({message:  res.body.msg,type: 'success'});
						// self.openInfo(res.body.content,personId);
					}else{
						this.$message({message: res.body.msg,type: 'error'});
					}
				});
		},
		getScmInfo(){
			let self = this;
			// console.log(this.personList_selectIndex);
			if(this.personList_selectIndex<0){
				self.$message.error("请选择责任人");
				return false;
			}
			let personId = this.personList[this.personList_selectIndex].id
			if(!personId){
				self.$message.error("当前责任人未保存，请保存后再操作");
				return false;
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/getScmInfo', personId , res => {
					if(res.body.result){
						// this.feedbackList = res.body.content || [];
						self.openInfo(res.body.content,personId);
					}else{
						self.$message.error(res.body.msg||'获取品质反馈失败');
					}
				});
		},
		openInfo(data,personId){
			this.$root.eventHandle.$emit('alert',{
				title: '品质反馈',
				style:'width:800px;height:400px',
				component:()=>import('@components/duty/scmInfo'),
				params: {
					data:data,
					personSelect :this.personList[this.personList_selectIndex],
					id:this.form.subList[this.subList_selectIndex].id,
					callback: d => {
						console.log(d);
					}
				},
			})
		},
		feedbackSearchClick(){
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/list', this.form.subList[this.subList_selectIndex].id , res => {
					if(res.body.result){
						this.feedbackList = res.body.content || []
					}
				})
		},
		feedbackSelectionChange(row){
			this.feedbackSelect = row;
		},

		selectTab2Click (){
			if(this.selectTab2 === 'operateLogList' && this.form.subList[this.subList_selectIndex].id){
				this.ajax.postStream('/afterSale-web/api/aftersale/bill/queryOperateLogByBillId', { after_bill_id: this.form.subList[this.subList_selectIndex].id }, res => {
					if(res.body.result){
						this.operateLogList = res.body.content || []
					}
				})
			}else if(this.selectTab2 === 'recalculateList'){
				this.getRecalculateList();
			}else if(this.selectTab2 === 'feedbackList'){
				this.feedbackSearchClick();
			}
		},
		handleSelectionChange (data){
			this.recordList_selectIndex = data
		},
		// 提交责任人前的检查
		_checkSubmitPerson (){
			var msg
			,	question_sub_ids = this.personList.map(obj => {
				if(!obj.id){
					msg = '请先保存责任人页签'
				}
				return obj.question_sub_id
			})

			!msg && this.questionList.some((obj, index) => {
				if(obj.supply_question_id && !obj.id){
					msg = '请先保存问题页签'
					return true
				}else if (!obj.supply_question_id){//只对子问题进行判断
					// if(!obj.supply_question_id && (this.questionList[index + 1] || {}).supply_question_id !== obj.parent_question_id && question_sub_ids.indexOf(obj.id) === -1){

					if(!((!obj.supply_question_id && question_sub_ids.indexOf(obj.id) > -1) || ((this.questionList[index + 1] || {}).supply_question_id === obj.parent_question_id && question_sub_ids.indexOf(this.questionList[index + 1].id) > -1))){
						// msg = '所有子问题都有责任人，如果问题进行了问题补充，则不需要指定责任人。'
						msg = '所有子问题或其补充问题至少要一个责任人。'
						return true
					}
				}
			})

			// this.recordList.some(obj => {
			// 	if(obj.api_status !== 'RECALLED'){
			// 		msg = '已生成的责任下发记录接口状态都要“已撤回”'
			// 		return true
			// 	}
			// })

			if(msg){
				this.$message.error(msg)
				return false
			}else {
				return true
			}
		},
		// 保存问题页签数据
		saveQuestion (){
			// if(this.questionList_selectRow.length !== 1 || this.questionList_selectRow[0].question_type === 'NORMAL'){
			// 	this.$message.error('请选择至少一个补充问题')
			// }else {
			// }
			var self = this
			,	errMsg = []
			,	successMsg = ''

			_loopSave([].concat(this.questionList_selectRow))
			function _loopSave (array){
				var question = array.shift()
				,	urlName
				,	postData

				if(!question){
					if(errMsg.length){
						self.$message.error(errMsg.join())
					}
					if(successMsg){
						self.$message.success(successMsg)
					}

					// successMsg && self.getOrderDetail(successMsg)
				}else {
					if(question.id){
						urlName = 'update'
						postData = {
							id: question.id,
							question_description: question.question_description,
						}
					}else {
						urlName = 'add'
						question.id = question._id
						delete question._id
						postData = question
					}

					self.ajax.postStream('/afterSale-web/api/aftersale/analysis/question/' + urlName + '?permissionCode=ANALYSIS_ORDER_QUESTION_SAVE', postData, res => {
						if(res.body.result){
							successMsg = res.body.msg

							if(urlName === 'add'){
								Object.assign(question, res.body.content)//显示最新问题数据
							}
						}else {
							errMsg.push(res.body.msg)
						}
						_loopSave(array)
					}, err => {
						_loopSave(array)
					})
				}
			}
		},
		// 资料请求btn
		infoReq (){
			var subObj = this.form.subList[this.subList_selectIndex]
			,	msg

			if(!subObj.info_require_remark){
				msg = '子单资料请求说明不能为空'
			}else if (!/(WAIT_SUBMIT|RECALLED)/.test(subObj.liability_status)){
				msg = '子单责任状态必须为待提交或已撤回'
			}else if (!/(NO_NEED|CONFIRMED)/.test(subObj.info_submit_status)){
				msg = '子单资料提交状态必须为无需提供或确认'
			}

			if(msg){
				this.$message.error(msg)
			}else {
				this.subAjax('infoReq?permissionCode=ANALYSIS_ORDER_DATA_REQUEST', { info_require_remark: subObj.info_require_remark })
			}
		},
		// 提交资料btn
		submitInfoReq (){
			var subObj = this.form.subList[this.subList_selectIndex]
			,	msg

			if(!subObj.info_submit_remark){
				msg = '子单资料提交说明不能为空'
			}else if (!/(WAIT_SUBMIT|RECALLED)/.test(subObj.liability_status)){
				msg = '子单责任状态必须为待提交或已撤回'
			}else if (subObj.info_submit_status !== 'WAIT_PROVIDE'){
				msg = '子单资料提交状态必须为等待提交'
			}

			if(msg){
				this.$message.error(msg)
			}else {
				this.subAjax('submitInfoReq?permissionCode=ANALYSIS_ORDER_DATA_SUBMIT', { info_submit_remark: subObj.info_submit_remark })
			}
		},
		// 最终确认
		testFinalConfirm (){
			// if(
			// 	!this.recordList.some(obj => {
			// 		if(!/(TRANSMITTED|CALLBACK)/.test(obj.api_status)){
			// 			this.$message.error('下发记录的接口状态必须是“已下达、已回传”')
			// 			return true
			// 		}
			// 	})
			// ){
			// }
			this.subAjax('finalConfirm?permissionCode=ANALYSIS_ORDER_FINAL_CONFIRM')
		},
		dealerFinalConfirm(){
			this.subAjax('dealerSubConfirm')
		},
		// 问题补充
		supplyQuestion (){
			var targetQuestion = this.questionList_selectRow[0]
			// if(!targetQuestion || this.questionList_selectRow.length !== 1){
			// 	this.$message.error('请选择一个子问题')
			// 	return
			// }else if (targetQuestion.supply_question_id){
			// 	this.$message.error('不能选择补充问题')
			// 	return
			// }else if (this.questionList.some(obj => !obj.id)){
			// 	this.$message.error('请先保存刚新建的补充问题')
			// 	return
			// }

			this.$root.eventHandle.$emit('alert', {
				component: ()=>import('@components/duty/supplyQuestion?permissionCode=ANALYSIS_ORDER_QUESTION_SUPPLEMENT'),
				style: 'width:80%;height:600px',
				title: '问题补充列表',
				params: {
					merge_trade_id: targetQuestion.merge_trade_id || this.form.aftersaleOrder.merge_trade_id,
					batch_trade_no: targetQuestion.batch_trade_no || this.form.aftersaleOrder.batch_trade_no,
					callback: d => {
						if(d.batch_trade_no != targetQuestion.batch_trade_no){
							this.$message.info('补充的问题与原有问题的批次单号不一致')
						}

						var newQuestion = {
							question_type: 'SUPPLY',//问题类型：补充问题

							_id: targetQuestion.id,//保存时再id: _id,
							supply_question_id: targetQuestion.parent_question_id,
							question_description: '',
							parent_question_id: targetQuestion.parent_question_id,
				            original_question_id: targetQuestion.original_question_id,
				            analysis_sub_id: targetQuestion.analysis_sub_id,
						}
						if(d._type === 'goods'){
							Object.assign(newQuestion, {
								goods_code: d.material_number,
								goods_name: d.material_name,
								buyer_nick: d.customer_name,
								specification: d.material_specification,
								lot: d.lot,
								suppiler_name: d.supplier_company,
								logistics_supplier_name: d.logistics_supplier_name,
								instock_date: d.instock_date,
								bom_version: d.bom_version,
								merge_material_id: d.merge_material_id,
								// real_name: d.real_name,
								num: d.number,
								units: d.material_unit,
								price: d.act_price,
								saleman_group: d.group_name,
								shop_name: d.shop_name,
								sys_trade_no: d.sys_trade_no,
								batch_trade_no: d.batch_trade_no,
							})
						}else if (d._type === 'orders'){
							Object.assign(newQuestion, {
								saleman: d.user_id,
								saleman_name: d.real_name,
								batch_trade_no: d.batch_trade_no,
								batch_trade_id: d.batch_trade_id,
								buyer_nick: d.customer_name,
								shop_id: d.shop_id,
								shop_name: d.shop_name,
							})
						}else {
							Object.assign(newQuestion, {
								logistics_supplier: d.logistics_supplier,
								logistics_supplier_name: d.logistics_supplier_name,//服务商
								batch_trade_no: d.batch_trade_no,
								batch_trade_id: d.batch_trade_id,
								out_stock_time: d.zd_delivery_time,//发货日期
							})
						}
						this.form.questionList.push(newQuestion)
						this.sortQuestionList(this.form.subList[this.subList_selectIndex].id)
					}
				},
			})
		},
		// 对补充问题删除
		delSupplyQuestion (){
			var self = this
			,	errMsg = []
			,	successMsg = ''

			if(
				this.questionList_selectRow.some(question => {
					if(this.form.personList.filter(obj => obj.question_sub_id === question.id).length){
						return true
					}
				})
			){
				this.$message.error('请删除所有勾选的问题补充关联的所有责任人!')
				return
			}

			_loopDel([].concat(this.questionList_selectRow))
			function _loopDel (array){
				var question = array.shift()

				if(!question){
					if(errMsg.length){
						self.$message.error(errMsg.join())
					}else {
						self.$message.success(successMsg)
					}
				}else {
					if(question.id){
						let params = {
							sub_id: self.form.subList[self.subList_selectIndex].id,
							idList: [question.id]
						}
						self.ajax.postStream('/afterSale-web/api/aftersale/analysis/question/delete?permissionCode=ANALYSIS_ORDER_QUESTION_DELETE', params, res => {
							if(res.body.result){
								successMsg = res.body.msg
								_successCb(question)
							}else {
								errMsg.push(res.body.msg)
							}
							_loopDel(array)
						}, err => {
							_loopDel(array)
						})
					}else {
						_successCb(question)
						_loopDel(array)
					}
				}
			}

			function _successCb (targetQuestion){
				self.questionList.splice(self.questionList.indexOf(targetQuestion), 1)
				self.form.questionList.splice(self.form.questionList.indexOf(targetQuestion), 1)
			}

			// if(!targetQuestion || this.questionList_selectRow.length !== 1){
			// 	this.$message.error('请选择一个补充问题')
			// 	return
			// }else if (!targetQuestion.supply_question_id){
			// 	this.$message.error('只能选择补充问题')
			// 	return
			// }
		},
		getCustomSupplyDataNew(){
			let self = this
			let id = this.questionList[0].after_order_id
			this.ajax.postStream('/custom-web/api/customAnalysisRelation/getCustomSupplyDataNew?permissionCode=CUSOMT_SUPPLY_INFO_QUERY', id, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
						this.openOrder(res.body.content)
					}else {
						this.$message.error(res.body.msg)
					}
				})
		},
    openOrder (data){
      this.ajax.get(`/afterSale-web/api/aftersale/custom/getCustomGoodsSupplyDetail/${this.questionList[0].analysis_sub_id}`,res=>{
        if(res.body.result){
          const result = res.body.content;
          if(result && result.supplyDetailPath) {
            window.open(result.supplyDetailPath,'_blank')
          } else {
            this.$root.eventHandle.$emit('creatTab',{
              name: '责任分析单详情',
              params: {
                id:this.questionList[0].after_order_id,
                form: JSON.parse(JSON.stringify(data)),
              },
              component:()=> import('./supplyData')
            })
          }
        }else {
          this.$message.error(res.body.msg)
        }
      })

		},
		// 附件操作
		uploadAction (method){
			// var id = (this.questionList_selectRow[0] || {}).id

			/*if(!id) {
				this.$message.error('请选择一个子问题')
			}else */if(method === 'post'){
				this.ifClickUpload = true
				this.uploadData = {
					parent_name: 'AFTER_ORDER',
					parent_no: this.form.aftersaleOrder.after_order_no,
					child_name: 'QUESTION_GOODS',
					// child_no: String(id),
					content: {
						sub_bill_no: this.form.sub_bill_no,
						merge_trade_no: this.form.merge_trade_no,
						after_order_no: this.form.aftersaleOrder.after_order_no,
					},
				}
				setTimeout(() => {
					this.ifClickUpload = false
				}, 100)
			}else {
				this.$root.eventHandle.$emit('creatTab', {
					params: {
						parent_name : 'AFTER_ORDER',
						parent_name_txt: '售后单号',
						parent_no : this.form.aftersaleOrder.after_order_no,
						other_parent_nos : this.form.subList.map(obj => obj.original_bill_type === 'REFUND_NEW' && obj.original_bill_no),
						child_name : 'QUESTION_GOODS',
						child_no : null,
						nickname: this.form.aftersaleOrder.buyer_nick,
						mergeTradeId: this.form.aftersaleOrder.merge_trade_id,
						// child_name_txt: '子问题id',
						// ext_data : null,
						// callback: files => {
						// 	this.listAfterMaterialVO[index].attachment_count = files.length
						// },
					},
					component: ()=>import('@components/after_sales/afterSale_aboutZD_download.vue'),
					style: 'width:80%;height:600px',
					title: '下载列表',
					name: '责任分析单问题附件列表',
				})
			}
		},
		// 重新传送
		confirmResend (){
			var confirmId = this.recordList_selectIndex.map(obj => obj.id)

			if(confirmId.length){
				this.ajax.postStream('/afterSale-web/api/aftersale/analysisConfirm/resend?permissionCode=ANALYSIS_ORDER_CONFIRM_NOTIFY_RESEND', confirmId, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}else {
				this.$message.error('请选择至少一个责任下发记录')
			}
		},
		// 转交分析单
		passOrder (){
			this.$root.eventHandle.$emit('alert',{
				title: '转交人列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/role/userList'),
				params: {
					callback: d => {
						this.orderAjax('turntoBatch?permissionCode=ANALYSIS_ORDER_TRANSMIT', {
							ids: [this.params.id],
							new_person_id: d.id,
							if_syn: false,
						})
					}
				},
			})
		},
		// 创建充值单
		createdCz() {
			const sub = this.form.subList[0]
			this.ajax.postStream("/custom-web/api/customAnalysisRelation/restartCreatePay", sub.id, d => {
				if(d.body.result){
					this.$message.success('创建成功')
					this.getOrderDetail()
				} else {
					this.$message.error(d.body.msg)
				}
			})
		},
		// 查找供应商id
		_findSuppilerId (obj){
			this.ajax.postStream("/afterSale-web/api/aftersale/ticketSupply/pageResultSupplier", {
				"page_size": 50,
				"page_no": 1,
				"key": obj.liability_person_name,
			}, d => {
				if(d.body.result && d.body.content){
					obj.liability_person = ((d.body.content.list || [])[0] || {}).supplier_id
				}
			})
		},
		// 批量填充责任人
		copyPerson (){
			var targetQuestion = this.questionList_selectRow[0]

			if(0 && this.questionList_selectRow.some(obj => obj.question_type !== 'NORMAL')){
				// 补充问题可以添加责任人
				this.$message.error('补充问题不能批量填充责任人')
			}else if (!this.personList.length){
				this.$message.error('源目标无责任人')
			}else {
				this.questionList_selectRow.forEach((obj, index) => {
					if(index > 0){
						JSON.parse(JSON.stringify(this.personList)).forEach(person => {
							var newPerson = {
								parent_question_id: obj.parent_question_id,
					            original_question_id: obj.original_question_id,
					            question_sub_id: obj.id,
					            analysis_sub_id: obj.analysis_sub_id,
					            _if_goods_question: obj.if_goods_question,//保存时会过滤此字段
			        			batch_trade_no: obj.batch_trade_no,
								liability_status: 'WAIT_CONDFIRM',
							}
							;[
								'liability_amount',
								'handle_amount',
								'liability_scope',
								'liability_question',
								'liability_question_name',
								'liability_type',
								'liability_question_description',
								'if_create_dispute_bill',
								'liability_person_name',
								'liability_person',
								'remark'
							].forEach(key => {
								newPerson[key] = person[key]
							})

							this.form.personList.push(newPerson)
						})
						this.$message.success('批量填充责任人成功')
					}
				})
			}
		},
		checkRawMaterialLiability(callback){
			let flag = false;
			this.ajax.postStream('/afterSale-web/api/aftersale/feedback/checkRawMaterialLiability',{goodCode: this.questionList_selectRow[0].goods_code}, res => {
					if(res.body.result){
						callback&&callback();
						flag = true;
					}else {
						this.$message.error(res.body.msg)
					}
				})
		},
		async addPersoncall(d,row){
			let self=this;
			var targetQuestion = this.questionList_selectRow[0]
			var questionData = {
				liability_scope: d.liability_scope,
				liability_question: d.id,
				liability_question_name: d.liability_question,
				liability_type: d.liability_type,
				liability_question_description: d.remark,
				if_create_dispute_bill: d.if_create_dispute,
				liability_person_name: '',//重选问题时清空责任人
				liability_amount:"",//责任金额
				handle_amount:"",//处理金额
			}
			let dutyDefaultList=this.getNeedApiDutyDefaultList();
			let dutyDefaultKeyList=this.getDutyDefaultApiKeyList(d.duty_default);
			let dutyDefaultParams=this.getDutyDefaultApiKeyOptions(d.duty_default,dutyDefaultKeyList);
		    if(d.duty_default!='NONEED'&&dutyDefaultList.includes(d.duty_default)){
					let params={
						'user_type':d.user_type,
						'duty_default':d.duty_default,
						...dutyDefaultParams
					}

					let personObj=await self.getDutyDeafult(params);
					if(!!personObj){
						questionData.liability_person_name = personObj.defaultName;
						questionData.liability_person = personObj.defaultId;
					}
			}else{
				if(questionData.liability_scope === 'BD_Supplier' && /工厂责任/.test(questionData.liability_type) && targetQuestion && targetQuestion.suppiler_name){
				// 责任范围为“供应商”，责任类型为“工厂责任” 且 有供应商信息，则默认为供应商
				questionData.liability_person_name = targetQuestion.suppiler_name || '';
				this._findSuppilerId(row || questionData);
			}else if (questionData.liability_scope === 'BD_Supplier' && d.duty_default=="DZSHIPMENTENTERPRISE"&& targetQuestion && targetQuestion.logistics_supplier_name){
				// if(/经销三包责任/.test(questionData.liability_question_name)){
				// 	console.log('经销三包责任')
				// 	// 责任范围为“供应商”，责任类型为“物流三包” 且为经销商订单，则默认为三包供应商
				// 	questionData.liability_person_name = targetQuestion.three_guarantees_supplier_name;
				// 	questionData.liability_person = targetQuestion.three_guarantees_supplier;

				// }else{
				// 	// 责任范围为“供应商”，责任类型为“物流三包” 且 有物流三包商信息，则默认为服务商
				// 	questionData.liability_person_name = targetQuestion.logistics_supplier_name
				// 	questionData.liability_person = targetQuestion.logistics_supplier
				// }
				// 默认责任人=定制物流服务商，则责任人自动通过【问题】页签中选中的问题对应的服务商在《责任部门列表》中获取
					questionData.liability_person_name = targetQuestion.logistics_supplier_name
					questionData.liability_person = targetQuestion.logistics_supplier

				// .默认责任人=定制产品研发组，则责任人就是=定制产品研发组
			}else if(questionData.liability_scope === 'BD_Department' && d.duty_default=="DZDPET"){
				questionData.liability_person_name = "定制产品研发组"
				questionData.liability_person = "26303060"
			}else if(questionData.liability_scope === 'BD_Customer' && questionData.liability_type === '客户责任' && targetQuestion.buyer_nick){
				// 责任范围为“客户”，责任类型为“客户责任” 且 有客户信息，则默认为客户
				questionData.liability_person_name = targetQuestion.buyer ? targetQuestion.buyer_nick : this.form.aftersaleOrder.buyer_nick; //供应商名字
				questionData.liability_person = targetQuestion.buyer || this.form.aftersaleOrder.buyer;//供应商ID
			}
			}
			if(row){
				Object.assign(row, questionData)
			}else {
				Object.assign(questionData, {
					liability_status: 'WAIT_CONDFIRM',
					batch_trade_no: targetQuestion.batch_trade_no,
					parent_question_id: targetQuestion.parent_question_id,
					original_question_id: targetQuestion.original_question_id,
					question_sub_id: targetQuestion.id,
					analysis_sub_id: targetQuestion.analysis_sub_id,
					_if_goods_question: targetQuestion.if_goods_question,//保存时会过滤此字段
				})
			}
			// 若责任类型配置的“责任金额是否自动带出补件采购成本”值=是，则自动带出责任金额和处理金额。责任金额和处理金额均=对应问题商品的补件采购成本，自动带出后，可进行编辑修改
			if(d.if_auto_amount=="Y"&&d.code!=="ZRWT00033"){
				// 除门店责任外
				let params={
					tickeNo:this.form.subList[this.subList_selectIndex].original_bill_no
				}
				new Promise((resolve,reject)=>{
				self.ajax.postStream("/afterSale-web/api/aftersale/analysis/person/getSupplyBoughtPrice",params,res=>{
					if(res.body.result){
						resolve(res.body.content);
						self.$message.success(res.body.msg);
					}else{
						self.$message.error(res.body.msg);
						reject();
					}
				},err=>{
					self.$message.error(err)
					reject();
				})
				}).then(res=>{
					questionData={
						...questionData,
						liability_amount:res,
						handle_amount:res
					}
					this.isEdit=true
					this.personList.push(questionData)
					this.form.personList.push(questionData)
				})
			}else{
				questionData.liability_amount=d.liability_amount||""
				questionData.handle_amount=d.handle_amount||""
				this.personList.push(questionData)
				this.form.personList.push(questionData)
			}

		},
		// 添加责任人
		addPerson (row){
			let self = this;
			var targetQuestion = this.questionList_selectRow[0];

			if(!targetQuestion.id) {
				this.$message.error('请先保存刚新增补充问题')
				return
			}

			this.$root.eventHandle.$emit('alert',{
		        params: {
					user_type :"CUSTOM_ORDER",
		        	callback: d => {
						console.log(d)
						// 原材料责任
						if(d.code=="ZRWT00022"){
							new Promise((resolve,reject)=>{
								self.checkRawMaterialLiability(resolve);
							}).then(()=>{
								self.addPersoncall(d,row);
							});
						}else{
							if(!row) {
								// 添加责任人，获取金额
								const goods = self.questionList[0]
								const sub = self.form.subList[0]

								goods && self.ajax.postStream('/custom-web/api/customAnalysisRelation/calculateAnalysisAmount', {
									code:d.code,
									goods_code: goods.goods_code,
									analysis_sub_id: sub.id
									}, res => {
									if(res.body.result){
										const data = res.body.content || {}
										d.liability_amount = data.liability_amount
										d.handle_amount = data.handle_amount
										self.addPersoncall(d,row);
									}
								})
							} else {
								self.addPersoncall(d,row);
							}
						}
		        		/*---先去掉---*/
		        		// if(!targetQuestion.batch_trade_no && /物流三包/.test(d.liability_type)){
		        		// 	this.$message.error((row ? '修改' : '添加') + '失败！责任类型为"物流三包"，对应的问题必须有批次单号')
		        		// 	return
		        		// }
		        	}
		        },
		        component:()=>import('@components/duty/addDutyPerson'),
		        style:'width:80%;height:80%',
		        title:'选择责任问题'
			})
		},
		/**
		*通过表头信息去拿到合并订单号
		*
		***/
		getMergeTradeId(){
			this.merge_trade_id = this.form.merge_trade_id;
		},
		// 保存责任人
		savePersonList (){
			var postData = []
			,	oldDataPersonListIds = this.originalData.personList.map(obj => obj.id)

			this.form.personList.forEach(obj => {
				var oldData = this.originalData.personList[oldDataPersonListIds.indexOf(obj.id)]

				if(!obj.id || (obj.id &&
					(
						Number(oldData.handle_amount) !== Number(obj.handle_amount)//处理金额
						|| Number(oldData.liability_amount) !== Number(obj.liability_amount)//责任金额
						|| oldData.liability_status !== obj.liability_status
						|| oldData.confirm_way !== obj.confirm_way
						|| oldData.remark !== obj.remark
						|| oldData.liability_question !== obj.liability_question
						|| oldData.liability_person_name !== obj.liability_person_name
					)
				)){
					postData.push(obj)
				}
			})

			if(!postData.length){
				this.$message.error('责任人数据没变化，无需保存')
				return
			}

			if(
				postData.some(obj => {
					if(!obj.liability_person_name){
						this.$message.error('责任人不能为空')
						return true
					}/*else if (obj.liability_scope === 'BD_Supplier' && obj._if_goods_question && !(obj._if_goods_question === 'Y')){
						this.$message.error('责任范围为"供应商"，对应的问题必须为商品问题，否则不能保存')
						return true
					}*/
					/*---先去掉---*/
					/*else if(!obj.batch_trade_no && /物流三包/.test(obj.liability_type)){
	        			this.$message.error('保存失败！责任类型为"物流三包"，对应的问题必须有批次单号')
	        			return true
	        		}*/
				})
			){
				return
			}

			postData = JSON.parse(JSON.stringify(postData)).filter(obj => {
				delete obj._if_goods_question
				delete obj.goods_code
				delete obj.goods_name
				delete obj.specification
				return true
			})
			this.ajax.postStream('/afterSale-web/api/aftersale/analysis/person/save?permissionCode=ANALYSIS_ORDER_RESPONSIBLE_SAVE', { personList: postData }, res => {
				if(res.body.result){
					this.getOrderDetail(res.body.msg)
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		// 删除责任人
		delPerson (){
			var personId = this.personList[this.personList_selectIndex].id
			if(personId){
				this.ajax.postStream('/afterSale-web/api/aftersale/analysis/person/delete?permissionCode=ANALYSIS_ORDER_RESPONSIBLE_DELETE', [personId], res => {
					if(res.body.result){
						this.$message.success('责任人删除成功')

						this.form.personList.some((obj, index) => {
							if(obj.id == personId){
								this.form.personList.splice(index, 1)
								return true
							}
						})
						this.personList.splice(this.personList_selectIndex, 1)
					}else {
						this.$message.error(res.body.msg)
					}
				})
			}else {
				this.form.personList.splice(this.form.personList.indexOf(this.personList[this.personList_selectIndex]), 1)
				this.personList.splice(this.personList_selectIndex, 1)
			}
		},
		// 选择责任人
		selectDutyPerson (liability_scope, row, liability_type){

			this.merge_trade_id?'':this.getMergeTradeId();//客户弹出框所需要的接口参数
			//
			let otherParams = {
				mergeTradeId : this.merge_trade_id,//客户弹出框所需要的接口参数
				list_type : {
					'工厂责任': ['SP','SA'],
					'物流三包': ['SB','WL'],
				}[liability_type] || []//供应商弹出框所需要的接口参数
			}
			,	params = {
	        	otherParams:otherParams,
	        	callback: d => {
	        		//客户和其它弹出框返回的参数需要区别
	        		if(liability_scope == 'BD_Customer'){
	        			row.liability_person = d.cust_id//客户ID
	        			this.$set(row, 'liability_person_name', d.name);//客户昵称
	        		}else{
	        			row.liability_person = d.id || d.data.supplier_id || d.data.id || d.data[0].id
	        			this.$set(row, 'liability_person_name', d.fullName || d._real_name_nick_name || d.data.name || d.data.realName || d.data[0].name)
	        		}

	        	},
	        	liability_type: liability_scope,
	        	showAllPerson: true,
	        }

	        if(liability_scope === 'BD_Department') params.type = 1

			this.$root.eventHandle.$emit('alert',{
		        params: params,
		        close: f => f,
		        component:()=>import('@components/' + {
		        	BD_Empinfo: 'personel/list',//员工
					BD_Supplier: 'duty/supplier2',//供应商
					BD_Customer: 'duty/selectPickrecommendhandler',//客户
					BD_Department: 'k3/departmentlist',//部门
		        }[liability_scope]),
		        style:'width:80%;height:80%',
		        title:'选择' + this.liability_scope_option[liability_scope] + '列表'
			})
		},
		// 责任分析单请求接口
		orderAjax (apiName, postData, cb){
			if(!postData) postData = { id: this.params.id }
			// if(postData){
			// 	postData.id = 9515943800980004
			// }else {
			// 	postData = { id: 9515943800980004/*this.params.id*/ }
			// }
			this.ajax.postStream('/afterSale-web/api/aftersale/analysis/' + apiName, postData, res => {
				if(res.body.result){
					if(apiName === 'get'){
						cb(res)
					}else {
						this.getOrderDetail(res.body.msg)
					}
				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		// 子单请求接口
		subAjax (apiName, postData = {}){
			var data

			if(/^save\?/.test(apiName)){
				data = this.form.subList[this.subList_selectIndex]
			}else {
				data = { id: this.form.subList[this.subList_selectIndex].id }
				Object.assign(data, postData)
			}
			this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/' + apiName, data, res => {
				if(res.body.result){
					this.getOrderDetail(res.body.msg)
					// 提交负责人
					apiName === 'submitPerson?permissionCode=ANALYSIS_ORDER_SUBMIT_RESPONSIBLE' && this.addCustomAnalysisReport(data)

				}else {
					this.$message.error(res.body.msg)
				}
			})
		},
		addCustomAnalysisReport(data) {
			/**
			 * @description: 添加责任单分析报表
			 * @param {*}
			 * @return {*}
			 */
			this.ajax.postStream('/custom-web/api/customAnalysisRelation/addCustomAnalysisReport', data.id, res => {
				!res.body.result && this.$message.error(res.body.msg)
			})
		},
		setStatus(){
			this.isEdit = this.form.locker == this.getEmployeeInfo('id');
		},
		getOrderDetail (msg, isInit){
			// 判断页面按钮是否可用
			this.ajax.postStream("/custom-web/api/customAnalysisRelation/disableButtonById", this.params.id, res => {
				if(res.body.result) {
					const { buttom_disable, restart_disable } = res.body.content
					this.isNotControl = buttom_disable
					this.isNotCreateCz = restart_disable
				} else {
					this.isNotControl = true
					this.isNotCreateCz = true
				}
			})
			// 判断页面按钮是否可用
			this.ajax.postStream("/file-web/api/messageDeliveryConfig/get.do", '1', res => {
			})

      Vue.prototype.tabNo[Vue.prototype.activeTab] = this.params.sub_bill_no || '';
            let pageParams = {
                id: this.params.id || '',
                sub_bill_no:this.params.sub_bill_no || '',
                // orderList: JSON.parse(JSON.stringify(this.params.orderList)) || [],
            }
            Vue.prototype.tabId[this.params.tabName] = JSON.stringify(pageParams);
      console.log("Vue",Vue.prototype.tabNo);
			this.orderAjax('get', null, res => {
				if(!res.body.content.aftersaleOrder) res.body.content.aftersaleOrder = {}
				this.form = res.body.content;
				this.originalData = JSON.parse(JSON.stringify(this.form))
				this.setStatus();
				//this.isEdit = this.form.locker == this.getEmployeeInfo('id')
				// this._checkIsProxy()
				if(this.selectTab1 === 'questionList'){
					this.questionListSelectChange()
				}else {
					if(isInit){
						// 从列表里点击进来，显示具体哪个子单详情
						this.form.subList.some((obj, index) => {
							if(obj.sub_bill_no === this.params.sub_bill_no){
								this.subList_selectIndex = index
								// this.getRecalculateList();
								return true
							}
						})
					}

					this.subListSelectChange(null, 'need2Change'/*非责任分析子单页签点击刷新按钮也要更新数据*/)
				}
				this.$message.success(msg || res.body.msg)
			})
		},
		// 当前操作人与锁定人 or 售后处理人是否代理关系
		// _checkIsProxy (){
		// 	;[{
		// 		thisName: 'isEdit',
		// 		id: this.form.locker,
		// 	},{
		// 		thisName: 'isAftersaleProcessor',
		// 		id: this.form.aftersale_processor,
		// 	}].forEach(obj => {
		// 		if(!this[obj.thisName] && obj.id){
		// 			this.ajax.postStream('/user-web/api/userLogin/isHasValidProxyRelation', {
		// 				principalId: obj.id,
		// 				proxyId: Fn.getUserInfo().id,
		// 			}, res => {
		// 				if(res.body.result){
		// 					this[obj.thisName] = true
		// 				}
		// 			})
		// 		}
		// 	})
		// },
		// _fixQuestionListCss (){
		// 	setTimeout(() => {
		// 		var $treeTables = this.$refs.$questionTree.$el.querySelectorAll('.el-table__body')
		// 		;[].forEach.call($treeTables, $dom => {
		// 			$dom.removeAttribute('class')
		// 			$dom.style.width = '100%'
		// 			$dom.children[0].innerHTML = ''
		// 		})
		// 	})
		// },
		// _makeQuestionList (targetSubId){
		// 	var queParentIdList = []
		// 	,	treeData = []
		// 	,	_key = 0

		// 	console.log('targetSubId123',targetSubId)
		// 	this.form.questionList.filter(obj => obj.analysis_sub_id === targetSubId).forEach(obj => {
		// 		var parentIndex = queParentIdList.indexOf(obj.id)
		// 		if(parentIndex === -1){
		// 			queParentIdList.push(obj.id)
		// 			obj._index = ++_key
		// 			treeData.push(obj)
		// 			obj._children = [{
		// 				_index: ++_key,
		// 				id: obj.id,
		// 		    	if_has_supply: obj.if_has_supply ? '是' : '否',
		// 		    	analysis_sub_id: obj.analysis_sub_id,
		// 		    	parent_question_id: obj.parent_question_id,
		// 	            original_question_id: obj.original_question_id,
		// 			}]
		// 			obj.if_has_supply = ''
		// 		}else {
		// 			treeData[parentIndex]._children.push({
		// 				_index: ++_key,
		// 				id: obj.id,
		// 		    	if_has_supply: obj.if_has_supply ? '是' : '否',
		// 		    	analysis_sub_id: obj.analysis_sub_id,
		// 		    	parent_question_id: obj.parent_question_id,
		// 	            original_question_id: obj.original_question_id,
		// 			})
		// 		}
		// 	})
		// 	console.log('treeData123123',treeData)
		// 	treeData[0] && setTimeout(() => {
		// 		this.$refs.$questionTree.setCheckedKeys([treeData[0]._children[0]._index],true)
		// 	})
		// 	return treeData
		// },
		// questionTreeChange: function (){
		// 	var asd
		// 	,	bbb
		// 	,	who
		// 	return function (a,b,c){
		// 		console.log(a._index,b,who)
		// 		// return
		// 		clearTimeout(asd)
		// 		asd = setTimeout(() => {
		// 			console.log(888)
		// 			if(b || who === a._index || (!b && who !== a._index && this.questionList.some(obj => who === obj._index && obj._children.length === 1))){
		// 				this.$refs.$questionTree.setCheckedKeys([])
		// 				who = a._index
		// 				this.$refs.$questionTree.setCheckedKeys([a._index],true)
		// 				clearTimeout(bbb)
		// 				bbb = setTimeout(() => {
		// 					this.questionListSelectChange()
		// 				})
		// 			}
		// 		})

		// 	}
		// }(),
		sortQuestionList (targetSubId){
			var data = this.form.questionList.filter(obj => obj.analysis_sub_id == targetSubId)
			,	returnData = []
			,	parentQuestion = {}


			// 子问题与补充问题排序
			data.forEach(obj => {
				if(!obj.supply_question_id) {
					returnData.push(obj)
				}else {
					if(!parentQuestion[obj.supply_question_id]) parentQuestion[obj.supply_question_id] = []
					parentQuestion[obj.supply_question_id].push(obj)
				}
			})

			Object.keys(parentQuestion).forEach(parentId => {
				returnData.some((obj, index) => {
					if(obj.parent_question_id == parentId){
						returnData.splice(index + 1, 0, ...parentQuestion[parentId])
						return true
					}
				})
			})

			this.questionList = returnData
		},
		subListSelectChange (currentRow, isNeedChange){
			if(isNeedChange === 'need2Change'){

			}else if (this.selectTab1 !== 'subList'){
				return//防止current-change乱触发
			}


			currentRow && (this.subList_selectIndex = this.form.subList.indexOf(currentRow))
			var targetSub = this.form.subList[this.subList_selectIndex]

			this.sortQuestionList(targetSub.id)
			this.selectTab2Click()
			this.personList = this.form.personList.filter(obj => obj.analysis_sub_id === targetSub.id)
			this.confirmList = this.form.confirmList.filter(obj => obj.analysis_sub_id === targetSub.id)
			this.confirmHisttoryList = (this.form.confirmHisttoryList || []).filter(obj => obj.analysis_sub_id === targetSub.id)
			this.recordList = this.form.recordList.filter(obj => obj.analysis_sub_id === targetSub.id)
			this.getRecalculateList(targetSub.id);
			this.getCustomSysTrade(targetSub.sub_bill_no);
		},
		getRecalculateList(id){
			let self = this;
			this.ajax.postStream('/afterSale-web/api/aftersale/analysisSub/geShareLog', { id: id || this.form.subList[this.subList_selectIndex].id }, res => {
					if(res.body.result){
						self.recalculateList = res.body.content || []
					}
				})
		},
		questionListSelectChange (selectRow){
			var _selectRowData = []
			if(this.selectTab1 !== 'questionList') return//防止selection-change乱触发
			if(selectRow){
				selectRow.forEach(obj => _selectRowData[this.questionList.indexOf(obj)] = obj)
				this.questionList_selectRow = _selectRowData.filter(Boolean)
			}

			var targetQuestion = this.questionList_selectRow[0]
			this.personList = targetQuestion ? this.form.personList.filter(obj => obj.question_sub_id === targetQuestion.id) : []
		},
		selectTab1Click (){
			// var checkIfSavePersonList = cb => {
			// 	if(0 && this.personList.some(obj => !obj.id)){
			// 		this.$root.eventHandle.$emit('openDialog', {
			// 			ok: e => {
			// 				this.savePersonList()
			// 				this.selectTab1 = 'questionList'
			// 			},
			// 			no: this.subListSelectChange,
			// 			cancel: () => {
			// 				this.selectTab1 = 'questionList'
			// 			},
			// 			txt: '是否保存责任人信息',
			// 		})
			// 	}else {
			// 		cb()
			// 	}
			// }

			if(this.selectTab1 === 'subList'){
				this.questionList_selectRow = []
				this.subListSelectChange()
			}else if (this.selectTab1 === 'questionList'){
				this.questionListSelectChange()
			}
		},
		// 上一页 or 下一页
		nextOrPrevOrder (type){
			this.params.orderList.some((obj, index) => {
				if(obj.id === this.params.id){
					var newOrder = this.params.orderList[index + (type === 'next' ? 1 : -1)]

					if(newOrder){
						this.params.id 			= newOrder.id
						this.params.sub_bill_no = newOrder.sub_bill_no

						this.getOrderDetail(''/*msg*/, 'init')
					}else {
						this.$message.error('没有更多')
					}
					return true
				}
			})
		},
		getCustomSysTrade(subBillNo){
			let self = this;
			this.ajax.postStream('/afterSale-web/api/aftersale/analysis/getCustomSysTrade', { subBillNo: subBillNo || this.form.subList[this.subList_selectIndex].sub_bill_no }, res => {
				if(res.body.result){
					self.supplyInfoData = res.body.content || {}
				}
			})
		},
		getMap(){
            let maps = [
                new Promise((resolve, reject) => {
                    getClientMap(clientMap => {
                        resolve(clientMap)
                    })
                }),
            ]
            return Promise.all(maps)
        },
		getNeedApiDutyDefaultList(){
			let list=__AUX.get("liability_duty_default").filter(item=>item.status===1&&item.tag==='Y').reduce((acc,cur)=>[...acc,cur.code],[])
			return list
		},
		getDutyDefaultApiKeyList(type){
			let obj=__AUX.get("liability_duty_default").filter(item=>item.status===1&&item.tag==='Y').find(item=>item.code===type)
			let keyList=[]
			if(!!obj){
				if(obj.remark.includes(",")){
					keyList=obj.remark.split(",")
				}else{
					keyList=[obj.remark]
				}
			}
			return keyList
		},
		getDutyDefaultApiKeyOptions(type,keyList){
			let targetQuestion = this.questionList_selectRow[0]//问题
			let supplyInfoData = this.supplyInfoData//补单详情
			let dutyDefaultKeyOptions={
				'dzMCode':targetQuestion.goods_code,
				'designer_number':supplyInfoData.designer_number,
				'shopName':targetQuestion.shop_name,
			}
			let obj={}
			if(keyList.length>0){
				keyList.forEach(item=>{
					obj[item]=dutyDefaultKeyOptions[item]
				})
			}
			return obj
		},
		getDutyDeafult(params){
			let self=this;
			return new Promise((resolve,reject)=>{
				self.ajax.postStream("/afterSale-web/api/aftersale/analysis/person/getDutyDeafult",params,res=>{
					if(res.body.result){
						resolve(res.body.content);
						self.$message.success(res.body.msg);
					}else{
						self.$message.error(res.body.msg);
						reject();
					}
				},err=>{
					self.$message.error(err)
					reject();
				})
			})
		}
	},
	async mounted (){
		this.maps = await this.getMap()

		this.clientStatusObj = this.maps[0]['client_status'].reduce((a, b) => {
			a[b.value] = b.label
			return a
		}, {})
		this.getOrderDetail(''/*msg*/, 'init');
		//监听切换业务代理事件
		this.$root.eventHandle.$on('resetAllBtnStatus',()=>{
			this.setStatus();
		});
	},
	created(){
      // 获取当前用户信息
	  // 若当前登录用户的类型=采购经销客户，则需将【费用明细】tab屏蔽
      this.ajax.get('/user-web/api/userPerson/getUserPerson/' + this.getEmployeeInfo('personId'), res => {
          if(res.body.result){
			this.userType=res.body.content.type
          }
        })
	},
	computed:{
		tabDisabled(){
      		return (this.userType =='PURCHASE_DEALER')
    	},
	}
}
</script>

<style module>
.textarea-style :global(.el-form-item__content) {
	margin-top: 5px;
    height: auto;
}
.row-height :global(.el-textarea__inner) {
	height: 100%;
}
.sub-question > td:first-child :global(.el-checkbox) {
	margin-left: 50px;
}
.max-table-height :global(.el-table__body-wrapper) {
	max-height: inherit;
}
</style>
