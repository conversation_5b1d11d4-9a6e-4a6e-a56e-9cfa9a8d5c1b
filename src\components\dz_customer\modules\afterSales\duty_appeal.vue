<template>
  <el-tabs v-model="selectTab1">
    <el-tab-pane label='责任申诉' name='appeal' >

      <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
        <!-- 查询 -->
        <div class="search-content">
          <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
        </div>
         <el-row :gutter="24">
          <el-col :span="24">
            <el-card shadow="always" :body-style="{ backgroundColor: '#ffa428' ,color:'#fff'}">
              <p>判断申诉规则及说明：</p>
              <p>1、为高效准确处理责任单申诉，所有申诉文件和结果以系统为准，其他渠道不作为申诉路径；</p>
              <p>2、申诉有效期：责任单提交时间7天内均可发起，超时系统将自动锁单并创建门店责任扣款单，无法进行申诉；</p>
              <p>3、每个责任单最多只能提交2次申诉，若本店责任单多次无效申诉，系统将列为黑名单门店，限制该店所有责任单的申诉次数或无法申诉；</p>
              <p>4、同一责任单的2次申诉时间间隔在72小时内，请注意在有效期内申诉。</p>
            </el-card>
          </el-col>

        </el-row>

        <div style="flex:1;overflow:hidden">
          <my-table
            ref="table"
            tableUrl="/custom-web/api/appeal/list"
            :tableParam="tableParam"
            :colData="colData"
            :orderNo="true"
            :tools="tools"
            showTools
            :btns="btns"
          ></my-table>
        </div>
      </div>
    </el-tab-pane>
     <el-tab-pane label='责任分析单' name='duty' >

      <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
        <!-- 查询 -->
        <div class="search-content">
          <form-create :formData="queryItems2" @save="query2" savetitle="查询"></form-create>
        </div>

        <div style="flex:1;overflow:hidden">
          <my-table
            ref="table2"
            tableUrl="/custom-web/api/appeal/page"
            :tableParam="tableParam2"
            :colData="colData2"
            :orderNo="true"
            showTools
            :tools="tools2"
            :btns="btns"
          ></my-table>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import formCreate from "./../../components/formCreate/formCreate";
import myTable from "./../../components/table/table";
import Fn from '@/common/Fn.js';

export default {
  components: {
    formCreate,
    myTable,
  },
  data() {
    return {
      tools:[
       {
          type: 'primary',
          txt: '详情',
          show(){
            return true
          },
          click: (d) => {
             this.$root.eventHandle.$emit('creatTab', {
              name: '审核详情',
              component: () => import('@components/dz_customer/modules/afterSales/detailAppeal.vue'),
              params: {
                row: d
              }
            })
          },

        }
      ],
      tools2:[
        {
          type: 'primary',
          txt: '申诉',
          show(){
            return true
          },
          click: (d) => {
             this.$root.eventHandle.$emit('creatTab', {
              name: '新增申诉',
              component: () => import('@components/dz_customer/modules/afterSales/addAppeal.vue'),
              params: {
                row: d
              }
            })
          },

        }
      ],

      selectTab1:'appeal',
      c_status:'',
      defaultValue:{
        client_status:['WAITING_DISTRIBUTION_DESIGN','SIGNED_CONTRACT_WAITING_VERIFY']
      },
      queryItems: [],
      queryItems2: [],
      btns: [

      ],
      tableParam: {},
      tableParam2: {},
      colData: [],
      colData2: [],
      info: {},
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
    supplyInfo(d){
        window.open(`${Fn.getStoreDomain()}/customization/supplement-order?once_query=${JSON.stringify({
          orderNo: d.client_number,
          from: 'NBO'
        })}`, '_blank');
    },

   detail(d) {
     console.log(d);
      this.$root.eventHandle.$emit("creatTab", {
        name: "订单详情",
        component: () =>
          import("@components/dz_customer/clientInfo/clientInfo.vue"),
        params: {
          customerInfo: {client_number:d.original_client_number},
          lastTab: this.params.tabName,
        },
      });
    },
    getColData() {
      let self = this;
      let intention  =[{
          label: '高',
          value: 'high'
        },
        {
          label: '中',
          value: 'middle'
        },
        {
          label: '低',
          value: 'low'
        },]
      this.colData = [
        {
          label: "申诉编号",
          prop: "custom_appeal_no",
          width: "180",
          // redirectClick(d) {
          //   _this.detail(d);
          // },
        },
        {
          label: "提交时间",
          prop: "create_time",
          width: "100",
					format: 'dataFormat1',
        },


        {
          label: "客户名",
          prop: "client_name",
          width: "100",
        },
        {
          label: "客户手机",
          prop: "receiver_mobile",
          width: "90",
          format: 'hidePhoneNumber'
        },
        {
          label: "责任分析子单",
          prop: "analysis_sub_no",
          width: "100",
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: "100",
        },
        {
          label: "定制单号",
          prop: "original_client_number",
          width: "100",
          redirectClick(d) {
            self.detail(d);
          },
        },
        {
          label: "补单号",
          prop: "client_number",
          width: "100",
          redirectClick(d) {
            self.supplyInfo(d);
          },
        },
        {
          label: "责任类型",
          prop: "liability_type",
          width: "100",
        },
        {
          label: "责任问题",
          prop: "liability_question_name",
          width: "100",
        },
        {
          label: "备注",
          prop: "liability_person_remark",
          width: "100",
        },
        {
          label: "责任金额",
          prop: "liability_amount",
          width: "100",
        },
        {
          label: "申诉状态",
          prop: "status",
          formatter(val){
            return {PASS:"通过",REJECT:"申诉驳回",SUBMIT:'提交'}[val] ;
          },
          width: "100",
        },
      ];
      this.colData2 = [
        {
          label: "责任分析子单",
          prop: "analysis_sub_no",
          width: "180",
          // redirectClick(d) {
          //   _this.detail(d);
          // },
        },
        {
          label: "责任单提交时间",
          prop: "analysis_sub_create_time",
					format: 'dataFormat1',
          width: "100",
        },


        {
          label: "客户名",
          prop: "client_name",
          width: "100",
        },
        {
          label: "客户手机",
          prop: "receiver_mobile",
          width: "90",
          format: 'hidePhoneNumber'
        },
        {
          label: "店名",
          prop: "shop_name",
          width: "100",
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: "100",
        },
        {
          label: "定制单号",
          prop: "original_client_number",
          width: "100",
          redirectClick(d) {
            self.detail(d);
          },
        },
        {
          label: "补单号",
          prop: "client_number",
          width: "100",
          redirectClick(d) {
            self.supplyInfo(d);
          },
        },
        {
          label: "责任类型",
          prop: "liability_type",
          width: "100",
        },
        {
          label: "责任问题",
          prop: "liability_question_name",
          width: "100",
        },
        {
          label: "备注",
          prop: "liability_person_remark",
          width: "100",
        },
        {
          label: "责任金额",
          prop: "liability_amount",
          width: "100",
        },
        {
          label: "处理金额",
          prop: "handle_amount",
          width: "100",
        },
      ];
      this.tools2 = [
        {
          type: 'primary',
          txt: '申诉',
          show(){
            return true
          },
          click: (d) => {
             this.$root.eventHandle.$emit('creatTab', {
              name: '新增申诉',
              component: () => import('@components/dz_customer/modules/afterSales/addAppeal.vue'),
              params: {
                row: d
              }
            })
          },

        }
      ]
    },
    query(data) {

      Object.assign(this.tableParam, data);
      this.$refs.table.initData();
    },
    query2(data) {

      Object.assign(this.tableParam2, data);
      this.$refs.table2.initData();
    },
    getQueryItems() {
      let _self = this;


      this.queryItems = [
        {
          cols: [

            { formType: "elSelect", prop: "status", label: "申诉状态", options:[{
              label:'申诉通过' ,
              value: 'PASS'
            },{
              label:'申诉驳回' ,
              value: 'REJECT'
            },{
              label:'已提交' ,
              value: 'SUBMIT'
            }]},
            {
              formType: "myInput",
              prop: "client_name",
              label: "客户名",
              type: "string",
              event: {
                // input(v, col) {
                //   col.value = v.replace(/\D/g, "");
                // },
              },
            },

            { formType: "elInput", prop: "client_number", label: "补单号" },
            { formType: "elInput", prop: "original_client_number", label: "定制单号" },
          ],
        },
      ];
      this.queryItems2 = [
        {
          cols: [

            { formType: "elInput", prop: "analysis_sub_no", label: "责任分析子单" },
            {
              formType: "myInput",
              prop: "client_name",
              label: "客户名",
              type: "string",
              event: {
                // input(v, col) {
                //   col.value = v.replace(/\D/g, "");
                // },
              },
            },

            { formType: "elInput", prop: "client_number", label: "补单号" },



            { formType: "elInput", prop: "original_client_number", label: "定制单号" },
          ],
        },
      ];
    },

  },
  async created() {
        this.getColData();
        this.getQueryItems();


    },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
