<template>

      <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
        <!-- 查询 -->
        <div class="search-content">
          <form-create :formData="queryItems" @save="query" savetitle="查询"></form-create>
        </div>
      
        <div style="flex:1;overflow:hidden">
          <my-table
            ref="table"
            tableUrl="/custom-web/api/appeal/appealList"
            :tableParam="tableParam"
            :colData="colData"
            :orderNo="true"
            :tools="tools"
            showTools
            :btns="btns"
          ></my-table>
        </div>
      </div>
    
</template>
<script>
import formCreate from "./../../components/formCreate/formCreate";
import myTable from "./../../components/table/table";
import {
  client_status,
  cusTypeopt,
  client_temp,
  getMap,
  c_status,
  custom_sys_budget
} from "../../common/clientDictionary";

export default {
  components: {
    formCreate,
    myTable,
  },
  data() {
    let self = this;
    return {
      
      selectTab1:'appeal',
      c_status:'',
      defaultValue:{
        client_status:['WAITING_DISTRIBUTION_DESIGN','SIGNED_CONTRACT_WAITING_VERIFY']
      },
      queryItems: [],
      btns: [
      
      ],
      tools:[
       {
          type: 'primary',
          txt: '审核',
          show(d){
            return d.status == "SUBMIT"?true:false
          },
          click: (d) => {
             this.$root.eventHandle.$emit('creatTab', {
              name: '申诉审核',
              component: () => import('@components/dz_customer/modules/afterSales/auditAppeal.vue'),
              params: {
                row: d
              }
            })
          },
          
        },
        {
          type: 'primary',
          txt: '详情',
          show(d){
            return d.status == "SUBMIT"?false:true
            return true
          },
          click: (d) => {
             this.$root.eventHandle.$emit('creatTab', {
              name: '审核详情',
              component: () => import('@components/dz_customer/modules/afterSales/detailAppeal.vue'),
              params: {
                row: d
              }
            })
          },
          
        }
      ],
      tableParam: {status: "SUBMIT"},
      colData: [],
      info: {},
    };
  },
  props: {
    params: {
      type: Object,
    },
  },
  methods: {
   detail(d) {
      this.$root.eventHandle.$emit("creatTab", {
        name: "订单详情",
        component: () =>
          import("@components/dz_customer/clientInfo/clientInfo.vue"),
        params: {
         customerInfo: {client_number:d.original_client_number},
          lastTab: this.params.tabName,
        },
      });
    },
    getColData() {
      let self = this;
      
      this.colData = [
        {
          label: "申诉编号",
          prop: "custom_appeal_no",
          width: "180",
          // redirectClick(d) {
          //   _this.detail(d);
          // },
        },
        {
          label: "提交时间",
          prop: "create_time",
					format: 'dataFormat1',
          width: "100",
        },
        
     
        {
          label: "客户名",
          prop: "client_name",
          width: "100",
        },
        {
          label: "客户手机",
          prop: "receiver_mobile",
          width: "90",
          format: 'hidePhoneNumber'
        },
        {
          label: "责任分析子单",
          prop: "analysis_sub_no",
          width: "100",
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: "100",
        },
        {
          label: "定制单号",
          prop: "original_client_number",
          width: "100",
          redirectClick(d) {
            self.detail(d);
          },
        },
        {
          label: "补单号",
          prop: "client_number",
          width: "100",
          redirectClick(d) {
            self.supplyInfo(d);
          },
        },
         {
          label: "店铺名称",
          prop: "shop_name",
          width: "100",
        },
        {
          label: "责任类型",
          prop: "liability_type",
          width: "100",
        },
       
        {
          label: "责任问题",
          prop: "liability_question_name",
          width: "100",
        },
        {
          label: "备注",
          prop: "liability_person_remark",
          width: "100",
        },
        {
          label: "责任金额",
          prop: "liability_amount",
          width: "100",
        },
        {
          label: "处理金额",
          prop: "handle_amount",
          width: "100",
        },
        {
          label: "申诉状态",
          prop: "status",
          formatter(val){
            return {PASS:"通过",REJECT:"申诉驳回",SUBMIT:'提交'}[val] ;
          },
          width: "100",
        },
      ];
      
     

    },
    query(data) {
      
      Object.assign(this.tableParam, data);
      this.$refs.table.initData();
    },
    supplyInfo(d){
        this.$root.eventHandle.$emit('creatTab', {
          name: '补单详情',
          component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
          params: {
            client_number: d.client_number,
          }
        })
      },
   detail(d) {
     console.log(d);
      this.$root.eventHandle.$emit("creatTab", {
        name: "订单详情",
        component: () =>
          import("@components/dz_customer/clientInfo/clientInfo.vue"),
        params: {
          customerInfo: {client_number:d.original_client_number},
          lastTab: this.params.tabName,
        },
      });
    },
    getQueryItems() {
      let _self = this;
    
     
      this.queryItems = [
        {
          cols: [
            
            { formType: "elInput", prop: "analysis_sub_no", label: "责任分析子单" },
            {
              formType: "myInput",
              prop: "client_name",
              label: "客户名",
              type: "string",
              event: {
                // input(v, col) {
                //   col.value = v.replace(/\D/g, "");
                // },
              },
            },
             
            { formType: "elInput", prop: "client_number", label: "补单号" },
            { formType: "elInput", prop: "original_client_number", label: "定制单号" },
             { formType: "elSelect", value: 'SUBMIT', prop: "status", label: "申诉状态", options:[{
              label:'申诉通过' ,
              value: 'PASS'
            },{
              label:'申诉驳回' ,
              value: 'REJECT'
            },{
              label:'已提交' ,
              value: 'SUBMIT'
            }]},
            { formType: "elInput", prop: "liability_type", label: "责任类型" },
            // { formType: "elInput", prop: "shop_code", label: "门店" },
             {
              formType: "listSelect",
              label: "门店",
              prop: "shop_code",
              config: { popupType: "shop", multiple: true },
              options: [],
            },
            { formType: "elInput", prop: "liability_question_name", label: "责任问题" },
            // { formType: "elInput", prop: "create_time_begin", label: "提交时间开始" },
            // { formType: "elInput", prop: "create_time_end", label: "提交时间结束" },
            {
              formType: "elDatePicker",
              label: "提交时间开始",
              prop: "create_time_begin",
              type: "datetime",
              format: "yyyy-MM-dd hh:mm:ss",
            },
            {
              formType: "elDatePicker",
              label: "提交时间结束",
              prop: "create_time_end",
              type: "datetime",
              format: "yyyy-MM-dd hh:mm:ss",
            },
          ],
        },
      ];
     
    },
    
  },
  async created() {
        this.getColData();
        this.getQueryItems();

        
    },
};
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
