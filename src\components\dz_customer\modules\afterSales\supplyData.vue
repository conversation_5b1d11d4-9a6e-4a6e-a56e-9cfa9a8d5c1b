<!--补件信息 -->
<template>
  
	<div  class="xpt-flex">
  <el-row class="xpt-top" :gutter="40" style="min-height: 30px">
		<el-col :span="18">
				<!-- <el-form-item label="定制补单号：">
						<el-input size='mini' :value="form.client_number" disabled></el-input>
					</el-form-item> -->
          <p><span class="span-item">定制补单号：<a style="color: blue;cursor: pointer;" @click="open(form.client_number)">{{form.client_number}}</a></span><span class="span-item">补件类型:{{form.typeName}}</span>
          <span class="span-item">SCM采购单号:{{form.purchaseTradeVos[0].po_number}}</span><span class="span-item">原单下推工厂:{{form.purchaseTradeVos[0].ori_factory_name}}</span>
          </p>
		</el-col>
   
	</el-row>
  <el-row class="xpt-top" :gutter="40" style="max-height: 280px">

	<el-tabs v-model="selectTab1">
		<el-tab-pane label='补单采购商品' name='base'>
      
      <el-row :gutter="20">
        <el-table
				:data="form.purchaseTradeVos"
				border
				tooltip-effect="dark"
				style="width: 100%;"
				width='100%'
			>
        <el-table-column label="采购编码" prop="client_number" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="补件商品编号" prop="goods_id" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
          <a style="color: blue;cursor: pointer;" @click="redirectClick(scope.row.goods_id)">{{scope.row.goods_id}}</a>
        </template>
          
        </el-table-column>
				<el-table-column label="三维家补单号" prop="swj_goods_no" show-overflow-tooltip></el-table-column>
				<el-table-column label="原商品名称" prop="origin_message" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="补件商品名称" prop="message" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="建档人" prop="shopping_guide_name" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="拆单文件路径" prop="filePath" width="250" show-overflow-tooltip>
          <template slot-scope="scope">
              <span style="width:190px;height: 18px;display: inline-block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{ scope.row.filePath }}</span>
               <el-button size="mini" type="primary" @click="copy(scope.row.filePath)"
                    >复制</el-button
                  >
              
              </template>
        </el-table-column>
			</el-table>
       <div>
        <el-button
          :plain="true"
          type="info"
          @click="saveZip"
        >打包文件</el-button>
        </div>
			</el-row>
       
    </el-tab-pane>
    
  </el-tabs>
	</el-row>

	<el-row  class='xpt-flex__bottom'>

  <el-tabs v-model="selectTab2">
		<el-tab-pane label='补件补件明细' name='content'>
      <el-row :gutter="20" class='xpt-flex'>
        <el-col :span="18">
            <p style="padding: 10px;"><span class="span-item">已选合计金额：标准售价 <span v-html="totalAmount"></span></span><span class="span-item">经销价:{{distributeAmount}}</span><span class="span-item">采购价:{{purchaseAmount}}</span><el-button size="mini" type="primary" @click="exportCustomSupplyDataNew"
                    >导出清单</el-button
                  ></p>
      </el-col>
        <el-table
				:data="form.customSwjQuotationVos"

				border
				tooltip-effect="dark"
				style="width: 100%;"
				width='100%'
        @selection-change="handleSelectionChange"
			>
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column label="原商品名称" prop="origin_goods_name" width="120" show-overflow-tooltip></el-table-column>
				<el-table-column label="备注" prop="quotation_remark" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="单元名称" prop="myUnitName" show-overflow-tooltip></el-table-column>
				<el-table-column label="部件名称" prop="name" width="200" show-overflow-tooltip></el-table-column>
				<el-table-column label="部件编号" prop="partNumber" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column label="数量" prop="number" width="100" show-overflow-tooltip></el-table-column>
				<el-table-column label="零售价" prop="retailAmount" width="100" show-overflow-tooltip></el-table-column>
				<el-table-column label="采购价" prop="purchaseAmount" width="100" show-overflow-tooltip></el-table-column>
				<el-table-column label="颜色" prop="colorCode" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="基材" prop="baseCode" width="150" show-overflow-tooltip></el-table-column>
				<el-table-column label="宽" prop="width" width="100" show-overflow-tooltip></el-table-column>
				<el-table-column label="高" prop="height" width="100" show-overflow-tooltip></el-table-column>
				<el-table-column label="深" prop="depth" width="100" show-overflow-tooltip></el-table-column>
				
			
			</el-table>
			</el-row>

    </el-tab-pane>
  </el-tabs>
	</el-row>
  
	</div>
</template>
<script>   
import {createId,downloadZip} from '@components/dz_customer/common/api'
import uploadFile from '@components/dz_customer/components/uploadFile'
import Fn from "@common/Fn.js";


  export default {
    props:['params'],
    components: {
        uploadFile,
          },
    data(){
      
      return {
        totalAmount:0,
        distributeAmount:0,
        purchaseAmount:0,
        uploadFileConfig: {},
        ifShowImage:false,
        imageList:[],
        submit2:{
          reason:'',
          isPass:''
        },
        submit:{
          reason:''
        },
        form:{
          client_name:'',
          client_number:'',
          original_client_number:'',
        },
        selectTab1: 'base',
        selectTab2: 'content',
        selectTab3: 'content',
        queryItems: [],
        imgList: [],
        initParam:{}
      }
    },

    async  created() {
      this.form = this.params.form;
      console.log(this.form)
      this.form.totalAmount = 0;
      this.form.distributeAmount = 0;
      this.form.purchaseAmount = 0;
    },
    methods:{
      exportCustomSupplyDataNew(){
			let id = this.params.id;
			this.ajax.postStream('/custom-web/api/customAnalysisRelation/exportCustomSupplyDataNew', id, res => {
					if(res.body.result){
						this.$message.success(res.body.msg)
						window.open(res.body.content)
					}else {
						this.$message.error(res.body.msg)
					}
				})
		},
      createElement(text) {
          var isRTL = document.documentElement.getAttribute('dir') === 'rtl';
          var element = document.createElement('textarea');
          // 防止在ios中产生缩放效果
          element.style.fontSize = '12pt';
          // 重置盒模型
          element.style.border = '0';
          element.style.padding = '0';
          element.style.margin = '0';
          // 将元素移到屏幕外
          element.style.position = 'absolute';
          element.style[isRTL ? 'right' : 'left'] = '-9999px';
          // 移动元素到页面底部
          let yPosition = window.pageYOffset || document.documentElement.scrollTop;
          element.style.top = `${yPosition}px`;
          //设置元素只读
          element.setAttribute('readonly', '');
          element.value = text;
          document.body.appendChild(element);
          return element;
      },
      // 查看
    copy(text) {
        var element = this.createElement(text);
          element.select();
          element.setSelectionRange(0, element.value.length);
          document.execCommand('copy');
          element.remove();
          alert("已复制到剪切板");
      },
      async saveZip() {
            let list=this.form.purchaseTradeVos.map(
              item=>{
                let arr = item.filePath.split('/');
                return {
                  name:arr[arr.length-1],
                  path:item.filePath
                }
              }
            )
        
        console.warn("下载的附件列表", list)
        if (!list.length) {
          this.$message({
            message: "没有可下载的附件",
            type: "warning",
          });
          return;
        }
        await downloadZip(list);
      },
      open(client_number){
        let rows ={client_number: client_number}
						// this.$root.eventHandle.$emit('creatTab', {
            //                 name: '订单详情',
            //                 component: () => import('@components/dz_customer/clientInfo/supplyInfo.vue'),
            //                 params: {
            //                     customerInfo: rows,
            //                     lastTab: this.params.tabName
            //                 }
            //             })

            this.$root.eventHandle.$emit('creatTab', {
						name: '补单详情',
						component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
						params: {
							client_number: client_number,
						}
					})
      },
      redirectClick(d) {
        let self = this;
                 this.$root.eventHandle.$emit('creatTab', {
                          name: '补单商品详情',
                          component: () => import('@components/dz_customer/goodsInfo/goodsInfo.vue'),
                          params: {
                              goodsInfo: {goods_id:d,client_number:self.form.client_number},
                              trade_type: 'SUPPLY'
                          }
                      })
					},
      handleSelectionChange(list){
        console.log(list)
        let totalAmount = 0;
        let distributeAmount = 0;
        let purchaseAmount = 0;
        // this.form.totalAmount = 0;
        // this.form.distributeAmount = 0;
        // this.form.purchaseAmount = 0;
        list.forEach(item=>{
          totalAmount += (Number(item.retailAmount) *10000)
          purchaseAmount += Number(item.purchaseAmount) *10000
        })
         this.totalAmount = totalAmount/10000;
         this.purchaseAmount = purchaseAmount/10000;
        //  this.distributeAmount = distributeAmount;
        this.distributeAmount = ((this.totalAmount*10000 *52)/100/10000).toFixed(4)
        console.log(this.distributeAmount,this.totalAmount,this.purchaseAmount)
      },
      closeShowImage() {
            this.ifShowImage = false;
        },
        showimgList(row, item) {
          this.initImageList(row, item);
            this.ifShowImage = true;
        },
      
        //初始化预览列表
        initImageList(row, items) {
            let imglist = [];
            //过滤出只有图片的列表
            let list = row.filter((item) =>
                this.isImage(item.path)
            );
            list.forEach((value) => {
                let obj = Object.assign({}, value);
                obj.src = value.path;
                obj.date = Fn.dateFormat(
                    value.create_time,
                    "yyyy-MM-dd hh:mm:ss"
                );
                obj.creatName = row.creator_nick;
                obj.isPucture = true;
                //确定要预览那张图片
                if (value.cloud_file_id === items.cloud_file_id) {
                    obj.active = true;
                } else {
                    obj.active = false;
                }
                imglist.push(obj);
            });
            this.imageList = imglist;
        },
        isImage(str) {
            var reg = /\.(jpg|png|jpeg|gif|JPG|PNG|JPEG|GIF)$/i;
            return reg.test(str);
        },
       supplyInfo(d){
        this.$root.eventHandle.$emit('creatTab', {
          name: '补单详情',
          component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
          params: {
            client_number: d.client_number,
          }
        })
      },
   detail(d) {
     console.log(d);
      this.$root.eventHandle.$emit("creatTab", {
        name: "订单详情",
        component: () =>
          import("@components/dz_customer/clientInfo/clientInfo.vue"),
        params: {
          customerInfo: {client_number:d.original_client_number},
          lastTab: this.params.tabName,
        },
      });
    },
    openOrder (id, sub_bill_no){
			this.$root.eventHandle.$emit('creatTab',{
				name: '责任分析单详情',
				params: {
					id,
					sub_bill_no,
					// orderList: JSON.parse(JSON.stringify(this._getOrderList.orderList)),
				},
				component:()=> import('./detailOfDuty_2')
			})
		},
     save(){
       let self = this;
       console.log(this.submit2);
       let postData = {
         custom_appeal_record_id:this.form.custom_appeal_record_id,

       }
       if(this.submit2.isPass){
         this.ajax.postStream('/custom-web/api/appeal/pass', postData, res => {
          if(res.body.result){
            this.$message.success(res.body.msg)
            self.$root.eventHandle.$emit('removeTab',self.params.tabName);
          }else {
            this.$message.error(res.body.msg)
          }
        })
       }else{
         postData.reject_reason = this.submit2.reason
         this.ajax.postStream('/custom-web/api/appeal/reject', postData, res => {
				if(res.body.result){
            this.$message.success(res.body.msg)
					self.$root.eventHandle.$emit('removeTab',self.params.tabName);
				}else {
					this.$message.error(res.body.msg)
				}
			})
       }
       
     },
     getImgList(){
       let postData = {
          order_no:this.form.custom_appeal_record_id,
          page_size:1000,
          sub_order_no: ""
       }
       this.ajax.postStream('/file-iweb/api/cloud/file/list', postData, res => {
				if(res.body.result){
					this.imgList = res.body.content.list;
				}else {
					this.$message.error(res.body.msg)
				}
			})
     },
     getContent(){
       let postData = {
         custom_appeal_record_id:this.form.custom_appeal_record_id,
       }
       this.ajax.postStream('/custom-web/api/appeal/getOne', postData, res => {
				if(res.body.result){
					this.submit = res.body.content;
          this.uploadFileConfig.parent_no = res.body.content.reject_id
				}else {
					this.$message.error(res.body.msg)
				}
			})
     },
    
     
    }
  }
</script>
<style scoped>
  .span-item{
    margin: 10px;
  }
</style>