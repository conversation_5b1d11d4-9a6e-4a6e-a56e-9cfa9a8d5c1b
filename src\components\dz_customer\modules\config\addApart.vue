<!--新增辅资料值-->
<template>
	<div>
		<xpt-headbar>
			<el-button type='primary' size='mini' @click='submit' slot='left'>确认</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click='close' slot='right'>关闭</el-button>
		</xpt-headbar>

		<el-form :model='data' ref='data' :rules='rules' label-position="right" label-width="120px">
			<el-row	:gutter='40'>
				<el-col :span='20'>



					<el-form-item label="商品编码" prop='number'>
						<el-input v-model="data.number" size='mini'  style="width:100%;" ></el-input>
						<el-tooltip v-if='rules.number[0].isShow' class="item" effect="dark" :content="rules.number[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="采购订单编码" prop='apart_number'>
						<el-input v-model="data.apart_number" size='mini'  style="width:100%;" ></el-input>
						<el-tooltip v-if='rules.apart_number[0].isShow' class="item" effect="dark" :content="rules.apart_number[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="采购订单类型" prop='type'>
						<el-input v-model="data.type" size='mini'  style="width:100%;" ></el-input>
						<el-tooltip v-if='rules.type[0].isShow' class="item" effect="dark" :content="rules.type[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="分拆来源" prop='apart_source'>
						<el-input v-model="data.apart_source" size='mini' disabled  style="width:100%;"></el-input>
					</el-form-item>
          <el-form-item label="齐套标识" prop="custom_kit_tag">
            <el-input v-model="data.custom_kit_tag" size="mini" style="width:100%"></el-input>
          </el-form-item>
					<el-form-item label="三维家部件号" >
						<span v-for="(item, key)  in data.partNumbers" :key="key">
							<el-input v-model="item.part_number" size='mini' style="width:95%;" ></el-input>
							<i class='el-icon-delete' @click="del(item, key)"></i>
						</span>
						<span @click="addNew" class="add_new">增加</span>

					</el-form-item>


				</el-col>



			</el-row>
			<el-row	:gutter='40'>
			</el-row>
		</el-form>
	</div>
</template>
<script>
	export default {
		data(){
			let self = this;
			return {
				province:[],
				city:[],
				data:{
					custom_apart_config_id:'',
					number: '',
					apart_number:'',
					type:'',
					apart_source	: '三维家',
					partNumbers:[
						{part_number:''}
					],
          custom_kit_tag:''
				},
				rules:{
					apart_number:[{
						required:true,
						message:'请输入编码',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}],
					number:[{
						required:true,
						message:'请输入商品编码',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}
					}],
					type:[{
						isShow:false,
						required:true,
                        message:'请选择订单类型',
                        trigger:'change',
                        validator:(rule,value,callback)=>{
                            if(self.data.shop_code && self.data.rule_type =='SHOP'){
                                if(value){
                                    self.rules[rule.field][0].isShow = false;
                                    callback()
                                }else{
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(''))
                                }
                            }else{
                                self.rules[rule.field][0].isShow = false;
                                callback()
                            }
                        }
					}],
          custom_kit_tag: [{
            isShow:false,
            required:true,
            message: '请输入齐套标识',
            trigger:'change',
          }]
				},
				updateCount:0,
				provinceFlag:false,
				pickerOptions0: {
		          	disabledDate(time) {
		            	return time.getTime() < Date.now() - 8.64e7;
		          	}
		        },
				pickerOptions1: {
		          	disabledDate(time) {
						  let enableTime = Date.now() - 8.64e7;
							if(self.data.effect_time_start) {
								enableTime = self.data.effect_time_start;
							}
							return time.getTime() < enableTime;
		            	// return time.getTime() < new Date(self.effect_time_start).getTime() ;
		          	}
		        }
			}
		},
		props:['params'],
		methods:{
			del(item, key){
				console.log(item, key)
				this.data.partNumbers.splice(key,1)
			},
			addNew(){

                this.data.partNumbers.push({part_number:''})
			},




			// 保存资料
			submit(callback){
				let self = this;
				this.$refs['data'].validate((valid) => {
					if(valid){
						this.ajax.postStream(
						'/custom-web/api/apartConfig/save',
						self.data,
						(res) => {
						res = res.body;
						if (res.result) {
							// 补单则同步新平台
							// 同步商品，旧版下推则不同步
							this.$message({
							message: res.msg,
							type: "success",
							});
							this.params.callback(this.data);
							this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
						}else{
							this.$message({
							message: res.msg,
							type: "error",
							});
						}
						},
						(err) => {
						this.$message({
							message: err.msg,
							type: "error",
						});
						}
					);

					}
				})

			},
			// 关闭
			close(){
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},


		},
		mounted(){
			var self = this;
			if(self.params.data){
				self.data = self.params.data;
			}
			self.getAddress((data)=>{
				// this.provice = '';
				self.province = data;
			});
		},
		computed: {

		}
	}
</script>
<style scoped>
.add_new{
	cursor: pointer;
	color: #0000ee;
}
</style>
