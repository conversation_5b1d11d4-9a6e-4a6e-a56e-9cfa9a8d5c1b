<!--新增辅资料值-->
<template>
	<div>
		<xpt-headbar>
			<el-button type='primary' size='mini' @click='submit' slot='left'>确认</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click='close' slot='right'>关闭</el-button>
		</xpt-headbar>

		<el-form :model='data' ref='data' :rules='rules' label-position="right" label-width="110px">
			<el-row	:gutter='40'>
				<el-col :span='24'>
					
					<el-form-item label="订单类别" prop='trade_type'>
						<el-select v-model="data.trade_type" size='mini' :disabled="!!data.custom_order_status_cycle_config_id"  placeholder="订单类别" @change="()=>{if(!data.custom_order_status_cycle_config_id){data.start_client_status = '';data.end_client_status = ''}}" >
								<el-option label="正单" value="ORIGINAL" key='1'>
								</el-option>
								<el-option label="补单" value="SUPPLY" key='2'>
								</el-option>
								
                            </el-select>
						<el-tooltip v-if='rules.trade_type[0].isShow' class="item" effect="dark" :content="rules.trade_type[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="订单状态开始" prop='start_client_status'>
						<el-select v-model="data.start_client_status" size='mini'  placeholder="订单状态" @change="statusChange" v-if="data.trade_type =='ORIGINAL'" >
								<el-option v-for="(value, key) in c_status" :label="value.label" :value="value.value" :key="key" >
								</el-option>
								
								
                            </el-select>
						<el-select v-model="data.start_client_status" size='mini'  placeholder="订单状态" @change="statusChange" v-else>
								<el-option v-for="(value, key) in client_status_supply" :label="value.label" :value="value.value" :key="key" >
								</el-option>
								
								
                            </el-select>
							
						<el-tooltip v-if='rules.start_client_status[0].isShow' class="item" effect="dark" :content="rules.start_client_status[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="订单状态结束" prop='end_client_status' >
						
						<el-select v-model="data.end_client_status" size='mini' disabled  placeholder="订单状态"  v-if="data.trade_type =='ORIGINAL'" >
								<el-option v-for="(value, key) in c_status" :label="value.label" :value="value.value" :key="key" >
								</el-option>
								
								
                            </el-select>
						<el-select v-model="data.end_client_status" size='mini' disabled placeholder="订单状态"  v-else>
								<el-option v-for="(value, key) in client_status_supply" :label="value.label" :value="value.value" :key="key" >
								</el-option>
								
								
                            </el-select>
							
					</el-form-item>
					<el-form-item label="预计周期" prop='expected_cycle' >
						<el-input v-model="data.expected_cycle" size='mini' type="number" style="width:70px;"></el-input>
						<el-tooltip v-if='rules.expected_cycle[0].isShow' class="item" effect="dark" :content="rules.expected_cycle[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="状态">
						<el-switch v-model="data.status" on-text="生效" off-text="失效"></el-switch>
						
					</el-form-item>
				
				</el-col>
			
			</el-row>
			<el-row	:gutter='40'>
			</el-row>
		</el-form>
	</div>
</template>
<script>
import {
  getMap,
} from "@components/dz_customer/common/clientDictionary";
  import validate from '@common/validate.js';
	export default {
		data(){
			let self = this;
			return {
				c_status:[],
				client_status_supply:[],
				data:{
					custom_order_status_cycle_config_id:null,
					trade_type: '',
					start_client_status:'',
					end_client_status:'',
					expected_cycle:'',
					status:true,
				},
				rules:{
					expected_cycle:validate.integer1({
						trigger: 'blur',
						required:true,
						self: self
					}),
					start_client_status:[{
						required:true,
						message:'请输入订单开始状态',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}  
					}],
					
					trade_type:[{
						required:true,
						message:'请输入订单类型',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}  
					}],
					purchase_trade_type:[{
						isShow:false,
                        message:'请选择店铺名称',
                        trigger:'change',
                        validator:(rule,value,callback)=>{
                            if(self.data.purchase_trade_type){
                                if(value){
                                    self.rules[rule.field][0].isShow = false;
                                    callback()
                                }else{
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(''))
                                }
                            }else{
                                self.rules[rule.field][0].isShow = false;
                                callback()
                            }
                        }
					}],
					effect_time_start:[{
						required:true,
						message:'生效时间不能大于失效时间',
                        trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(value&&self.dataeffect_time_end){
								if(value>self.dataeffect_time_end){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false;
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false;
									}else{
										self.rules['effect_time_end'][0].isValidateFromOther = true;
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = false
								callback();
							}
						} 
					}],
					effect_time_end:[{
						message:'失效时间不能小于生效时间',
						required:true,
                        trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(!value){
								self.rules[rule.field][0].isShow = true;
								callback(new Error(''));
							}
							else if(value&&self.dataeffect_time_start){
								if(value<self.dataeffect_time_start){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false
									}else{
										self.rules['effect_time_end'][0].isValidateFromOther = true;
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = false
								callback();
							}
						} 
					}]
				},
				updateCount:0,
				provinceFlag:false,
				pickerOptions0: {
		          	disabledDate(time) {
		            	return time.getTime() < Date.now() - 8.64e7;
		          	}
		        },
				pickerOptions1: {
		          	disabledDate(time) {
						  let enableTime = Date.now() - 8.64e7;
							if(self.data.effect_time_start) {
								enableTime = self.data.effect_time_start;
							}
							return time.getTime() < enableTime;
		            	// return time.getTime() < new Date(self.effect_time_start).getTime() ;
		          	}
		        }
			}
		},
		props:['params'],
		methods:{
			statusChange(){
				this.data.end_client_status = '';
				let list =  this.data.trade_type =='ORIGINAL'?this.c_status:this.client_status_supply;
				console.log(list)
				list.forEach((item,index)=>{
					if(item.value == this.data.start_client_status&&index!=list.length-1){
						this.data.end_client_status = list[index+1].value
					}
				})
			},
			selectShop () {
            let self = this
            self.$root.eventHandle.$emit('alert', {
                component:()=>import('@components/shop/list'),
                style:'width:800px;height:500px',
                title:'选择店铺',
                params: {
                	selection: 'radio',
                    callback (data) {
	                        self.data.start_client_status = data.start_client_status;
	                        self.data.shop_id = data.shop_id;
                    }
                }
            })
		  },
      shopChange(){
        this.data.shop_name = "";
	      this.data.shop_id = "";
      },
	  
		
		
			// 保存资料
			submit(callback){
				let self = this;
				this.$refs['data'].validate((valid) => {
					if(valid){
						this.ajax.postStream(
						'/custom-web/api/cycleConfig/save',
						self.data,
						(res) => {
						res = res.body;
						if (res.result) {
							this.$message({
							message: res.msg,
							type: "success",
							});
							this.params.callback(this.data);
							this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
						}else{
							this.$message({
							message: res.msg,
							type: "error",
							});
						}
						},
						(err) => {
						this.$message({
							message: err.msg,
							type: "error",
						});
						}
					);
						
					}
				})

			},
			// 关闭
			close(){
				
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
			
			
		},
		mounted(){
			var self = this;
			console.log(this.params)
			if(this.params.data){
				this.data = this.params.data;
			}
			getMap((map) => {
				let cs = map.client_status.filter((item) => true);
				// cs = cs.slice(0,cs.length-1)
				console.log(cs)
				cs.forEach((item) => {
				this.c_status.push(item);
				});
				let css = map.client_status_supply.filter((item) => true);
				css.forEach((item) => {
				this.client_status_supply.push(item);
				});
			});
		},
		computed: {
			
		}
	}
</script>