<!-- 定制线上权益商品 -->
<template>
	<div>
		<el-row class="xpt-top" :gutter="40">
			<el-col :span="24">
				<el-button type='primary' size='mini' @click="preSave('submitData')">保存</el-button >
				<!-- <el-button type='primary' size='mini' @click="getDetail(submitData.custom_equity_id)" :disabled='!submitData.custom_equity_id'>刷新</el-button > -->

			</el-col>
		</el-row>
		<el-row	:gutter='40' >
			<el-tabs v-model="firstTab" >
			    <el-tab-pane label="订单信息" name="goodsInfo">
					<el-form label-position="right" class="mgt10" label-width="100px" :model="submitData" :rules="rules" ref="submitData">
						<el-col :span='24'>
							<el-form-item label="平台店铺" prop="platform_shop_code">
								<xpt-select-aux
									:disabled="!!submitData.custom_equity_id"
									v-model='submitData.platform_shop_code'
									aux_name='dzj_shop'
									placeholder="请选择"
									@change="shopChange()"
								></xpt-select-aux>
								<el-tooltip v-if='rules.platform_shop_code[0].isShow' class="item" effect="dark" :content="rules.platform_shop_code[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="平台商品编码" prop="platform_goods_id">
								<el-input size='mini' :disabled="!!submitData.custom_equity_id" type="number" v-model='submitData.platform_goods_id'></el-input>
								<el-tooltip v-if='rules.platform_goods_id[0].isShow' class="item" effect="dark" :content="rules.platform_goods_id[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="权益名称"  prop="equity_name">
								<el-input size='mini' :disabled="!!submitData.custom_equity_id" v-model='submitData.equity_name'></el-input>
								<el-tooltip v-if='rules.equity_name[0].isShow' class="item" effect="dark" :content="rules.equity_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="让利金额"  prop="benefit_amount">
								<el-input size='mini' :disabled="!!submitData.custom_equity_id" v-model='submitData.benefit_amount' type="number" :min="0" :max="99999"></el-input>
								<el-tooltip v-if='rules.benefit_amount[0].isShow' class="item" effect="dark" :content="rules.benefit_amount[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="让利额度门槛" prop="benefit_amount">
								<el-input size='mini'  :disabled="!!submitData.custom_equity_id" v-model='submitData.benefit_amount_threshold' type="number" :min="0"></el-input>
								<!-- <p style="color:#bfcbd9;">按照三维家标准计算，订单总金额达不到使用门槛，下单时无法扣减让利金额</p> -->
								<el-tooltip v-if='rules.benefit_amount_threshold[0].isShow' class="item" effect="dark" :content="rules.benefit_amount_threshold[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="">
								<p style="color:#bfcbd9;">按照三维家标准计算，订单总金额达不到使用门槛，下单时无法扣减让利金额</p>
							</el-form-item>
							<el-form-item label="权益使用说明" prop="remark">
								<el-input size='mini' v-model='submitData.remark'></el-input>
								
							</el-form-item>
							
							<el-form-item label="生效时间" prop="effect_time">
								<el-date-picker size='mini' v-model='submitData.effect_time' start-placeholder="开始日期" end-placeholder="结束日期" type="daterange"></el-date-picker>
								<el-tooltip v-if='rules.effect_time[0].isShow' class="item" effect="dark" :content="rules.effect_time[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
							<!-- <el-form-item label="订单店铺" prop="shopList">
								<el-input v-model="submitData.shopList" size='mini' readonly icon="search" :on-icon-click="() => selectShop('shopList', 'shop_name')"
									></el-input>
								<el-tooltip v-if='rules.shopList[0].isShow' class="item" effect="dark" :content="rules.shopList[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item> -->
							<!-- <el-form-item label="生效结束时间" prop="effect_end_time">
								<el-input size='mini' v-model='submitData.effect_end_time'></el-input>
								
							</el-form-item> -->
							<!-- <el-form-item label="是否店铺" prop="if_shop">
								<el-sclect>
									<option :value="1">是</option>
									<option :value="0">否</option>
								</el-sclect>
								
							</el-form-item> -->
						</el-col>
					
										
			    </el-form>
			  </el-tab-pane>
			</el-tabs>
		</el-row>
		 <el-row class="xpt-flex__bottom">
            <el-tabs v-model="secondTab" >
              
                <el-tab-pane label="线下适用店铺（不选择代表所有店铺适用）" name="shop" class="xpt-flex">
                    <xpt-list
                        :data="shopList"
                        :btns="Btns"
                        :colData="Cols"
                        selection="radio"
                        isNeedClickEvent
                    >
                   
                    </xpt-list>
                </el-tab-pane>

               

               
            </el-tabs>
        </el-row>
	</div>
</template>
<script>
	import validate from '@common/validate.js';
	import detailComponent from './addEquityGoods.vue';

	export default {
		props:["params"],
		data() {
			var _this = this;
			return{
				firstTab:"goodsInfo",
				secondTab:"shop",
				shopList:[],
				Btns:[
					 {
                    type: "primary",
                    txt: "新增",
                    click() {
                        _this.selectShop("add");
                    }
                },
				],
				Cols: [
                {
                    label: "店铺id",
                    prop: "shop_id"
                },
                {
                    label: "店铺编码",
                    prop: "shop_code"
                },],
				submitData:{
					custom_equity_id:"",                               //主键，新增时为空
					platform_shop_code:"",                         //平台店铺编码
					platform_shop_name:"",                //平台店铺名称
					platform_goods_id:"",                           //平台商品id
					equity_name:"",                             //权益名称
					benefit_amount:"",                                 //让利金额
					benefit_amount_threshold:"",             //让利额度门槛
					remark:"",                                    //权益使用说明
					effect_begin_time:"",             //生效开始时间
					effect_end_time:'',                //生效结束时间
					effect_time:[],
					if_shop:1 ,                                           //是否店铺，0全局，1店铺
					shopList:[],
					// deleteShopIds:[                                       //要删除的店铺custom_equity_shop_id
					// ]
				},
				
				rules:{
					settle_target:[
						{
							required: false,
							validator: (rule, value, callback) => {
								if((/20000|LS-LBDZ|LS-LBCP|LS-LBGC/.test(_this.submitData.shop_code) ) && JSON.stringify(value) ==="null"){
									_this.rules[rule.field][0].required  = true
									_this.rules[rule.field][0].isShow = true
									callback(new Error(''))
								}else {
									_this.rules[rule.field][0].isShow = false
									callback()
								}
							},
							trigger: 'blur',
							isShow: false,
							message: '请填写结算对象',
						}
					],
					platform_shop_code:validate.isNotBlank({
						required:true,
						self:_this
					}),
					
					platform_goods_id:validate.isNotBlank({
						required:true,
						self:_this
					}),
					equity_name:validate.isNotBlank({
						required:true,
						self:_this
					}),
					benefit_amount:validate.isNotBlank({
						required:true,
						self:_this
					}),
					benefit_amount_threshold:validate.isNotBlank({
						required:true,
						self:_this
					}),
					effect_time:validate.isNotBlank({
						required:true,
						self:_this
					}),
					shopList:validate.isNotBlank({
						required:true,
						self:_this,
					}),
					
					
				},
				initialData:{},
				isLoading:false,
				
				
			}
		},
		methods : {
			shopChange(){
				let shopList  = __AUX.get('dzj_shop');
				var obj = {};
				for(let i in shopList){
					// obj[payType[i].code] = payType[i].name;
					if(shopList[i].code == this.submitData.platform_shop_code){
						this.submitData.platform_shop_name = shopList[i].name;
					}
				}
			},
			// 订单店铺选择
			selectShop (idName, valName){

			let self = this;
			this.$root.eventHandle.$emit('alert',{
				title: '店铺列表',
				style:'width:800px;height:600px',
				component:()=>import('@components/shop/list.vue'),
				params: {
					selection: 'checkbox',
					callback: d => {
						// this.$set(this.form, valName, d.shop_name)
						// this.form[idName] = d.shop_id
						console.log(self.shopList)
							d.forEach(dItem=>{
								self.shopList.push({
									shop_id:dItem.shop_id,
									shop_code:dItem.shop_code,
								});
							})
					}
				},
			})
		},
			preSave(formName){
				var self = this;
				
				self.$refs[formName].validate((valid) => {
					if(!valid) return

					this.isLoading=true
					self.save()
				})
			},
			save(callback){	
				var self = this,params={};
				var url = "/custom-web/api/equityGoods/save";
				if(this.submitData.benefit_amount<0){
					this.$message.error('让利金额不能小于0');
					return;
				}
				if(this.submitData.benefit_amount>99999){
					this.$message.error('让利金额不能大于99999');
					return;
				}
				if(this.submitData.benefit_amount_threshold<1){
					this.$message.error('让利额度门槛不能为小于1');
					return;
				}
				let saveData = JSON.parse(JSON.stringify(this.submitData));
				saveData.effect_begin_time = this.submitData.effect_time[0];
				saveData.effect_end_time = this.submitData.effect_time[1];
				saveData.shopList = self.shopList;
				delete saveData.effect_time;
				if(self.shopList.length ==0){
					saveData.if_shop = 0;
				}else{
					saveData.if_shop = 1;

				}

				this.ajax.postStream(url,saveData,(response)=>{
					if(response.body.result){
						this.$message({message: '操作成功',type: 'success'})
						this.getDetail(response.body.content);
						
					}else{
						this.$message.error(response.body.msg)
					}
					this.isLoading=false
				},e=>{
					this.isLoading=false
					this.$message.error(e)
				})
			},
			// 关闭标签页
			closeTab(){
				var self = this,isUpdate;
				isUpdate = this.compareData(self.submitData,self.initialData);
				if(isUpdate){
					self.$root.eventHandle.$emit('openDialog',{
						ok(){
							self.save(()=>{
								self.$root.eventHandle.$emit('removeTab',self.params.tabName)
							})
						},
						no(){
							self.$root.eventHandle.$emit('removeTab',self.params.tabName)
						}
					})
				}else{
					self.$root.eventHandle.$emit('removeTab',self.params.tabName)
				}
			},
			getDetail(custom_equity_id){
				let self = this;
				this.ajax.postStream('/custom-web/api/equityGoods/getOne',{custom_equity_id:custom_equity_id},(response)=>{
					if(response.body.result){
						self.submitData.custom_equity_id = response.body.content.custom_equity_id;
						self.submitData.platform_shop_code = response.body.content.platform_shop_code;
						self.submitData.platform_shop_name = response.body.content.platform_shop_name;
						self.submitData.platform_goods_id = response.body.content.platform_goods_id;
						self.submitData.equity_name = response.body.content.equity_name;
						self.submitData.benefit_amount = response.body.content.benefit_amount;
						self.submitData.benefit_amount_threshold = response.body.content.benefit_amount_threshold;
						self.submitData.remark = response.body.content.remark;
						self.shopList = !response.body.content.shopList?[]:response.body.content.shopList;
						self.$nextTick(()=>{
							self.$set(self.submitData,'effect_time',[response.body.content.effect_begin_time,response.body.content.effect_end_time])
						// self.submitData.effect_time[0] = response.body.content.effect_begin_time
						// self.submitData.effect_time[1] = response.body.content.effect_end_time
						})
						
					}else{
						this.$message.error(response.body.msg)
					}
					
				},e=>{
					this.isLoading=false
					this.$message.error(e)
				})
			}
			
			
			
		},
		mounted:function(){ 
			var self = this;
			if(self.params.row){
				// self.submitData = {
				// 	custom_equity_id:self.params.row.custom_equity_id,                               //主键，新增时为空
				// 	platform_shop_code:self.params.row.platform_shop_code,                         //平台店铺编码
				// 	platform_shop_name:self.params.row.platform_shop_name,                //平台店铺名称
				// 	platform_goods_id:self.params.row.platform_goods_id,                           //平台商品id
				// 	equity_name:self.params.row.equity_name,                             //权益名称
				// 	benefit_amount:self.params.row.benefit_amount,                                 //让利金额
				// 	benefit_amount_threshold:self.params.row.benefit_amount_threshold,             //让利额度门槛
				// 	remark:self.params.row.remark,                                    //权益使用说明
				// 	// effect_begin_time:self.params.row.effect_begin_time,             //生效开始时间
				// 	// effect_end_time:self.params.row.effect_end_time,                //生效结束时间
				// 	if_shop:self.params.row.if_shop ,                                           //是否店铺，0全局，1店铺
				// 	shopList:self.params.row.shopList,
				// 	effect_time:[self.params.row.effect_begin_time,self.params.row.effect_end_time]
					
				// };
				this.getDetail(self.params.row.custom_equity_id)
			}
			self.params.__close = self.closeTab
        },
	}
</script>