<!--新增辅资料值-->
<template>
	<div>
		<xpt-headbar>
			<el-button type='primary' size='mini' @click='submit' slot='left'>确认</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click='close' slot='right'>关闭</el-button>
		</xpt-headbar>

		<el-form :model='data' ref='data' :rules='rules' label-position="right" label-width="80px">
			<el-row	:gutter='40'>
				<el-col :span='10'>
					
					<el-form-item label="订单类别" prop='purchase_trade_type'>
						<!-- <el-input v-model="data.purchase_trade_type" size='mini' style='width:180px'></el-input> -->
						<el-select v-model="data.purchase_trade_type" size='mini'  placeholder="类型" >
								<el-option v-for="(item) in purchase_trade_type_option" :label="item.type" :value="item.apart_number" :key="item.id">
								</el-option>
								<!-- <el-option label="铝门订单" value="ALUMINUM" key='2'>
								</el-option>
								<el-option label="柜体订单" value="CABINET" key='3'>
                            	</el-option> -->
                            </el-select>
						<el-tooltip v-if='rules.purchase_trade_type[0].isShow' class="item" effect="dark" :content="rules.purchase_trade_type[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="店铺编码" prop='shop_code' v-show="data.rule_type == 'SHOP'">
						<xpt-input v-model="data.shop_code" icon="search" :on-icon-click="selectShop"  @change="shopChange"  size="mini"  readonly style="width:180px"></xpt-input>
						<el-tooltip v-if='rules.shop_code[0].isShow' class="item" effect="dark" :content="rules.shop_code[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="省名称" prop='province_id' v-show="data.rule_type == 'CITY'||data.rule_type == 'PROVINCE'">
						<el-select v-model="data.province_id" size="mini" style="width:150px;" @change="changeAddress(data.province_id,1)">
							<el-option v-for="(value, key) in province" :label="value" :value="parseInt(key)" :key="key" ></el-option>
						</el-select>
						
					</el-form-item>
					<el-form-item label="城市名称" prop='city' v-show="data.rule_type == 'CITY'">
						<el-select v-model="data.city_id" size="mini" style="width:150px;" @change="changeCity()">
							<el-option v-for="(value, key) in city" :label="value" :value="parseInt(key)" :key="key"></el-option>
						</el-select>
						<el-tooltip v-if='rules.city[0].isShow' class="item" effect="dark" :content="rules.city[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<!-- <el-form-item label="工厂编码" prop='factory_code'>
						<el-input v-model="data.factory_code" size='mini'></el-input>
						<el-tooltip v-if='rules.factory_code[0].isShow' class="item" effect="dark" :content="rules.factory_code[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item> -->
					<el-form-item label="工厂名称" prop='factory_name'>
						<el-input v-model="data.factory_name" size='mini' readonly icon="search" :on-icon-click="selectFactory"></el-input>
						<el-tooltip v-if='rules.factory_name[0].isShow' class="item" effect="dark" :content="rules.factory_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="基材" prop='baseCodes'>
						<el-input v-model="baseCodes" size='mini' readonly icon="search" :on-icon-click="selectbaseCodes"></el-input>
					</el-form-item>
					<el-form-item label="花色" prop='colorCodes'>
						<el-input v-model="colorCodes" size='mini' readonly icon="search" :on-icon-click="selectcolorCodes"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span='10'>
					<el-form-item label="状态">
						<el-switch v-model="data.status" on-text="生效" off-text="失效"></el-switch>
					</el-form-item>
					<el-form-item label="生效日期" prop='effect_time_start'>
						<el-date-picker v-model="data.effect_time_start" type="date" placeholder="选择日期" size='mini' :picker-options="pickerOptions0" style='width:180px'></el-date-picker>
						<el-tooltip v-if='rules.effect_time_start[0].isShow' class="item" effect="dark" :content="rules.effect_time_start[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="失效日期" prop='effect_time_end'>
						<el-date-picker v-model="data.effect_time_end" type="date" placeholder="选择日期" size='mini' :picker-options="pickerOptions1" style='width:180px'></el-date-picker>
						<el-tooltip v-if='rules.effect_time_end[0].isShow' class="item" effect="dark" :content="rules.effect_time_end[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
					    </el-tooltip>
					</el-form-item>
					<el-form-item label="规则类型" >
						<el-select v-model="data.rule_type" size='mini' @change="ruleChange" placeholder="类型" >
							<el-option label="城市" value="CITY" key='1'>
							</el-option>
							<el-option label="默认" value="DEFAULT" key='2'>
							</el-option>
							<el-option label="省份" value="PROVINCE" key='3'>
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
				
			</el-row>
			<el-row	:gutter='40'>
			</el-row>
		</el-form>
	</div>
</template>
<script>
	export default {
		data(){
			let self = this;
			return {
				baseCodes:'',
				colorCodes:'',
				purchase_trade_type_option:[],
				province:[],
				city:[],
				data:{
					custom_factory_id:'',
					factory_code: '',
					factory_name:'',
					rule_type:'DEFAULT',
					colorCodes:[],
					baseCodes:[],
					shop_id:'',
					shop_code	: '',
					province_id:'',
					city_id:'',
					city:'',
					purchase_trade_type:'',
					status:true,
					effect_time_start: new Date(),
					effect_time_end:new Date('2099-12-31'),
				},
				rules:{
					factory_code:[{
						required:true,
						message:'请输入编码',
						isShow:false,
						validator: function(rule,value,callback){
							if(value){
								if(/^[A-Z0-9_]+$/.test(value)){
									self.rules[rule.field][0].isShow = false
									callback();
								}else{
									self.rules[rule.field][0].isShow = true
									self.rules[rule.field][0].message = '编码必须是大写字母、数字、_的组合格式'
									callback(new Error(''));
								}
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						} 
					}],
					factory_name:[{
						required:true,
						message:'请输入名称',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}  
					}],
					shop_code:[{
						isShow:false,
						
                        message:'请选择店铺名称',
                        trigger:'change',
                        validator:(rule,value,callback)=>{
                            if(self.data.shop_code && self.data.rule_type =='SHOP'){
                                if(value){
                                    self.rules[rule.field][0].isShow = false;
                                    callback()
                                }else{
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(''))
                                }
                            }else{
                                self.rules[rule.field][0].isShow = false;
                                callback()
                            }
                        }
					}],
					city:[{
						isShow:false,
                        message:'请选择店铺名称',
                        trigger:'change',
                        validator:(rule,value,callback)=>{
                            if(self.data.city && self.data.rule_type =='CITY'){
                                if(value){
                                    self.rules[rule.field][0].isShow = false;
                                    callback()
                                }else{
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(''))
                                }
                            }else{
                                self.rules[rule.field][0].isShow = false;
                                callback()
                            }
                        }
					}],
					purchase_trade_type:[{
						isShow:false,
                        message:'请选择店铺名称',
                        trigger:'change',
                        validator:(rule,value,callback)=>{
                            if(self.data.purchase_trade_type){
                                if(value){
                                    self.rules[rule.field][0].isShow = false;
                                    callback()
                                }else{
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(''))
                                }
                            }else{
                                self.rules[rule.field][0].isShow = false;
                                callback()
                            }
                        }
					}],
					effect_time_start:[{
						required:true,
						message:'生效时间不能大于失效时间',
                        trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(value&&self.dataeffect_time_end){
								if(value>self.dataeffect_time_end){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false;
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false;
									}else{
										self.rules['effect_time_end'][0].isValidateFromOther = true;
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = false
								callback();
							}
						} 
					}],
					effect_time_end:[{
						message:'失效时间不能小于生效时间',
						required:true,
                        trigger:'change',
						isShow:false,
						isValidateFromOther:false,
						validator: function(rule,value,callback){
							if(!value){
								self.rules[rule.field][0].isShow = true;
								callback(new Error(''));
							}
							else if(value&&self.dataeffect_time_start){
								if(value<self.dataeffect_time_start){
									self.rules[rule.field][0].isShow = true;
									callback(new Error(''));
								}else{
									self.rules[rule.field][0].isShow = false
									if(self.rules[rule.field][0].isValidateFromOther){
										self.rules[rule.field][0].isValidateFromOther = false
									}else{
										self.rules['effect_time_end'][0].isValidateFromOther = true;
									}
									callback();
								}
							}else{
								self.rules[rule.field][0].isShow = false
								callback();
							}
						} 
					}]
				},
				updateCount:0,
				provinceFlag:false,
				pickerOptions0: {
		          	disabledDate(time) {
		            	return time.getTime() < Date.now() - 8.64e7;
		          	}
		        },
				flag:true,
				pickerOptions1: {
		          	disabledDate(time) {
						  let enableTime = Date.now() - 8.64e7;
							if(self.data.effect_time_start) {
								enableTime = self.data.effect_time_start;
							}
							return time.getTime() < enableTime;
		            	// return time.getTime() < new Date(self.effect_time_start).getTime() ;
		          	}
		        }
			}
		},
		props:['params'],
		methods:{
			ruleChange(val){
				if(this.flag){
					this.flag = false;
					return;
				}
				if(val != 'CITY'){
					this.data.city_id = '';
					this.data.province_id = '';
					this.data.city = '';
				}
				if(val != 'PROVINCE'){
					this.data.city_id = '';
					this.data.province_id = '';
					this.data.city = '';
				}
			
				
			},
			selectShop () {
            let self = this
            self.$root.eventHandle.$emit('alert', {
                component:()=>import('@components/shop/list'),
                style:'width:800px;height:500px',
                title:'选择店铺',
                params: {
                	selection: 'radio',
                    callback (data) {
	                        self.data.shop_code = data.shop_code;
	                        self.data.shop_id = data.shop_id;
                    }
                }
            })
		  },
      shopChange(){
        this.data.shop_name = "";
	      this.data.shop_id = "";
      },
	  selectbaseCodes(){
		  let self = this
            self.$root.eventHandle.$emit('alert', {
                component:()=>import('./getColorBase'),
                style:'width:800px;height:500px',
                title:'选择基材',
                params: {
					list:self.data.baseCodes,
                	selection: 'radio',
                    callback (data) {
						self.data.baseCodes = data.list.map(item=>{
							return item.code;
						})
						self.baseCodes = self.data.baseCodes.join(',');
						console.log(self.data.baseCodes)
	                        // self.data.shop_code = data.shop_code;
	                        // self.data.shop_id = data.shop_id;
                    }
                }
            })
	  },
	  selectcolorCodes(){
		  let self = this
            self.$root.eventHandle.$emit('alert', {
                component:()=>import('./getColorBase'),
                style:'width:800px;height:500px',
                title:'选择花色',
                params: {
					list:self.data.colorCodes,
                	selection: 'radio',
                    callback (data) {
						self.data.colorCodes = data.list.map(item=>{
							return item.code;
						})
						self.colorCodes = self.data.colorCodes.join(',');
	                      
                    }
                }
            })
	  },
	  selectFactory() {
		  let self = this, title='列表选择',tableUrl='/custom-web/api/customSupplierUrgentFee/list',colData=[{
            label: '供应商ID',
            prop: 'supplier_id',
            width: '153'
          },{
            label: '供应商名称',
            prop: 'supplierName',
            width: '153'
          },{
            label: '工厂编码',
            prop: 'factory_code',
            width: '153'
          },{
            label: '加急费率（%）',
            prop: 'urgent_fee',
            width: '150'
          },{
            label: '状态',
            prop: 'status_cn',
            filter: 'select',
            options: [ {label: '生效', value: 1}, {label: '失效', value: 0} ],
            width: 'auto'
          }], formCreate = {
                labelWidth: '100px',
                queryItems: [
                    {
                        cols: [
                            {formType: 'elInput', label: '供应商名称', prop:'supplierName', span: 6},
                        ]
                    }
                ]
            }, config={}
		let params = Object.assign({
			queryItems: formCreate.queryItems, //查询天剑
			colData, //表显示列
			tableUrl, //表接口
			cb(data){
				// console.log(data);
				self.data.factory_code = data.factory_code;
				self.data.factory_name = data.supplierName;
			}
		}, config)
		let w = innerWidth*0.8
		let h = innerHeight*0.92
		w > 800 && (w=800)
		h > 610 && (h=610)
		formCreate.labelWidth && (params.labelWidth = formCreate.labelWidth)
		config.w && (w = config.w)
		config.h && (h = config.h)
		self.$root.eventHandle.$emit('alert',{
			params:params,
			component:()=>import('@components/dz_customer/alert/list'),
			style:`width:${w}px;height:${h}px`,
			title
		});
	},
			changeAddress(code,type){
				var key = type ==1?'city':'area';
				if(!code) return;
				this.getAddress(code,(data)=>{
					this[key] = '';
					this[key] = data || {};
					var value = this.data[key];
					if(!data || !data.hasOwnProperty(value)){
						if(this.provinceFlag){
							this.provinceFlag = false;
							return;
						}
						if(type == 1){
							this.data.city_id = null;
						}else if(type == 2){
							this.shop.area = null;
						}
					}
				});
			},
			changeCity(){
				let self = this;
				console.log(self.city,self.data.city_id,111)
				self.data.city = self.city[self.data.city_id];
				return;
			},
			// 保存资料
			submit(callback){
				let self = this;
				delete self.data.purchase_trade_type_cn;
				delete self.data.status_str;
				this.$refs['data'].validate((valid) => {
					if(valid){
						this.ajax.postStream(
						'/custom-web/api/factoryConfig/save',
						[self.data],
						(res) => {
						res = res.body;
						if (res.result) {
							// 补单则同步新平台
							// 同步商品，旧版下推则不同步
							this.$message({
							message: res.msg,
							type: "success",
							});
							this.params.callback(this.data);
							this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
						}else{
							this.$message({
							message: res.msg,
							type: "error",
							});
						}
						},
						(err) => {
						this.$message({
							message: err.msg,
							type: "error",
						});
						}
					);
						
					}
				})

			},
			getDetail(callback){
				let self = this;
			
						this.ajax.postStream(
						'/custom-web/api/apartConfig/allList',
						{},
						(res) => {
						res = res.body;
						if (res.result) {
							let hasObj= {};
 
							self.purchase_trade_type_option = res.content.reduce((cur,next) => {
								hasObj[next.type] ? "" : (hasObj[next.type] = true && cur.push(next)); 
								return cur;
							},[]) 
							console.log(self.purchase_trade_type_option);
						}else{
							this.$message({
							message: res.msg,
							type: "error",
							});
						}
						},
						(err) => {
						this.$message({
							message: err.msg,
							type: "error",
						});
						}
					);
						

			},
			// 关闭
			close(){
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
			
			
		},
		mounted(){
			var self = this;
			if(self.params.data){
				self.data = self.params.data;
				if(self.params.data.baseCodes){
					self.baseCodes = self.params.data.baseCodes.join(',')
				}
				if(self.params.data.colorCodes){
					self.colorCodes = self.params.data.colorCodes.join(',')
				}
				self.provinceFlag = true;
			}
			self.getAddress((data)=>{
				// this.provice = '';
				self.province = data;
			});
			self.getDetail();
		},
		computed: {
			
		}
	}
</script>