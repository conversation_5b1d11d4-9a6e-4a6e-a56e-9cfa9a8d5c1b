<!--
 * @Author: your name
 * @Date: 2021-03-11 13:52:19
 * @LastEditTime: 2021-04-12 14:41:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \front\src\components\dz_customer\modules\config\addRegion.vue
-->
<!-- 新增、编辑收费记录 -->
<template>
	<div class="mainPage">
        <xpt-headbar class="topBtn">
			<el-button size='mini'  type="primary" @click="save" slot='left' :disabled='customerBtnStatu' :loading='customerBtnStatu'>保存</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click="closeComponent" slot='right'>关闭</el-button>
		</xpt-headbar>
		<div style="margin-top:40px;">
            <form-create 
            ref="formCreate"
            :formData="queryItems" 
            :btns="false"
            :ruleData="ruleData"
            @save="getFormData"
            ></form-create> 
            <my-table
                tableHeight="auto"
                :tableData="dataList"
                :colData="colData"
                :btns="btns"
                @checkboxChange="checkboxChange"
                selection="checkbox"
                >
                </my-table>
        </div>
	</div>
</template>
<script>   
import formCreate from '../../components/formCreate/formCreate'
import closeComponent from '../../common/mixins/closeComponent'
import myTable from "../../components/table/table";
import {popup} from "../../alert/alert";
import { getArea } from "../../common/api"
  export default {
    components: { formCreate, myTable },
    mixins: [closeComponent],
    props:['params'],
    data(){
        let self = this
      return {
        customerBtnStatu: false,
        dataList:[],
        deleteItems: [],
        areaInfo:{},
        colData: Object.freeze([
            {
                label: "店名",
                prop: "shop_name",
                width: 200
            },
            {
                label: '店铺编码',
                prop: 'shop_code',
                width: 133
            },
            {
                label: '店铺地址',
                prop: 'address'
            },
            {
                label: "创建时间",
                prop: "create_time",
                filter: 'date',
                formats: 'yyyy-MM-dd hh:mm:ss',
                width: "140",
            },
            {
                label: "修改时间",
                prop: "modify_time",
                filter: 'date',
                formats: 'yyyy-MM-dd hh:mm:ss',
                width: "140",
            }
        ]),
        btns: Object.freeze([
            {
                type: "primary",
                txt: "新增",
                click() {
                    popup.shop(self, (data) => {
                        let ids = self.dataList.map(item => item.shop_id)
                        self.dataList = self.dataList.concat(data.map(item => {
                            return {
                                area_id: self.areaInfo.area_id,
                                shop_id: item.shop_id,
                                shop_name: item.shop_name,
                                shop_code: item.shop_code,
                                address: item.address,
                                create_time: item.create_time,
                                modify_time: item.modify_time,
                                del_flg: 0
                            }
                        }).filter(item => !ids.includes(item.shop_id)))
                        console.log(self.dataList)
                    }, { multiple: true, apiUrl: '/custom-web/api/customArea/getShopList' })
                }
            },
            {
                type: 'danger',
                txt: '删除',
                click() {
                    if(self.result) {
                        self.deleteItems = self.deleteItems.concat(self.result.filter(item => {
                            return item.area_relation_id && self.deleteItems.findIndex(di => di.area_relation_id = item.area_relation_id) === -1
                        }).map(item => {
                            item.del_flg = 1
                            return item
                        }))
                        self.dataList = self.dataList.filter(item => self.result.findIndex(rt => rt.shop_id === item.shop_id) === -1 )
                    } 
                }
            }
        ]),
        ruleData: Object.freeze({
          // uploadFile: {required: true, message: '请上传文件', trigger: 'change'},
          area_name: {required: true, message: '请输入区域名称', trigger: 'blur'},
          area_code: {required: true, message: '请输入变迁编码', trigger: 'blur'}
        }),
        queryItems: []
      }
    },
    
    async  created() {
      this.getQueryItems()
    },
    methods:{
        getFormData(data) {
            /**
             * @description: 获取表单数据
             * @param {*}
             * @return {*}
             */    
            this.areaInfo.area_id && (data.area_id = this.areaInfo.area_id)
            data.shopList = this.dataList.concat(this.deleteItems).map(item => {
              return {
                area_relation_id: item.area_relation_id,
                area_id: item.area_id,
                shop_id: item.shop_id,
                shop_name: item.shop_name,
                del_flg: item.del_flg
              }
            })
            data.status = 1
            data.area_parent_id = data.area_parent_id || 0
            data.node_type = this.areaInfo.node_type ? this.areaInfo.node_type : data.area_parent_id === 0 ? 1 : 0
            this.customerBtnStatu = true
            let _this = this
            this.ajax.postStream('/custom-web/api/customArea/save',data,(data) =>{
                this.customerBtnStatu = false
                data = data.body
                if(data.result) {
                    this.$root.eventHandle.$emit('removeTab',this.params.tabName)
                    this.$root.eventHandle.$emit('saveArea')
                }
                _this.$message({
                    message: data.msg,
                    type:data.result?'success':'error'
                })
            },function(){
                _this.$message({
                    message: data.msg,
                    type:'error'
                })
                this.customerBtnStatu = false
            })
        },
        save() {
        /**
         * @description: 保存表单
         * @param {*}
         * @return {*}
         */    
            this.$refs.formCreate.save()
        },
      checkboxChange(rows) {
        /**
         * @description: 店铺多选结果
         * @param {*}
         * @return {*}
         */  
        this.result = rows;
      },
      async getQueryItems() {
          let areaInfo = {}
          if(this.params.row) {
            areaInfo = await getArea(this.params.row.area_id)
          }
          this.areaInfo = areaInfo
          areaInfo.shopList && ( this.dataList = areaInfo.shopList )
        let parentAreaList = this.params.parentAreaList ? this.params.parentAreaList.filter(item => {
          return item.area_id !== areaInfo.area_id
        }).map(item => {
            return { label: item.area_name, value: item.area_id }
        })  : []
        this.queryItems = [
          {
              cols: [
                  {formType: 'elInput', label: '区域名称', prop:'area_name', span: 12, value: areaInfo.area_name},
                  {formType: 'elInput', label: '区域编码', prop:'area_code', span: 12, value: areaInfo.area_code},
                  {formType: 'selectUser', label: '区域经理', prop: 'area_user', span: 12, value: areaInfo.area_user, config: { label: areaInfo.areaUserName }},
                  {formType: 'elSelect', label: '上级区域', prop: 'area_parent_id', filterable: true, span: 12, value: areaInfo.area_parent_id === 0 ? '' : areaInfo.area_parent_id, options: parentAreaList},
              ]
          },
          {
            cols: [
              {formType: 'elInput', type:"textarea", prop: 'remark', span:12, label: '说明', value: areaInfo.remark, maxlength:150}
            ]
          }
        ]
      }
    }
  }
</script>
<style scoped>
.mainPage {
  position: relative;
  height: 100%;
  overflow: scroll;
  overflow-x: hidden;
}
.topBtn {
  position: fixed;
  width: 100%;
  z-index: 1;
}
</style>
