<!--采购订单规则维护-->
<template>
	<xpt-list
		:data='dataList'
		:btns='btns'
		:colData='cols'
		:pageTotal='count'
		:selection='selection'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
		@radio-change='radioChange'
		@row-dblclick='rowDblclick'
		ref='dataList'
	>
	<template slot='do' slot-scope='scope'>
				<el-button size='mini' type='danger' @click='del(scope.row)'>删除</el-button>
		</template>
	</xpt-list>
</template>
<script>
export default {
	props:["params"],
	data(){
		let self = this
		return {
			showSearch:false,
			search:{
				page_size: self.pageSize,     //页数
				page_no:1,   //页码
			},
			dataList:[],
			actions_value:"",
			count:0,
			pageNow:1,
			multipleSelection: [],
			// 单选选中的行
			returnObj:"",
			selectId:"",
			searchName:'',

			selection: '',
			btns: [
				{
					type: 'success',
					txt: '刷新',
					click() {
						self.refresh()
					}
				}, {
					type: 'primary',
					txt: '新增',
					click() {
						self.mod()
					}
				}
			],
			cols: [
				{
					label: '编号',
					prop: 'number'
				},
				{
					label: '采购订单编码',
					prop: 'apart_number',
					redirectClick(row) {
						self.viewDetail(row)
					}
				}, {
					label: '订单类型',
					prop: 'type'
				}, {
					label: '分拆来源',
					prop: 'apart_source',
				}, {
					label: '三维家部件编号',
					prop: 'partNumbers',
                    formatter(val){
						 let str = val.map(item=>{
							 return item.part_number
						 })
						 return str.join(',');
                        // switch(val){
                        //     case 'CITY':   return '城市';break;
                        //     case 'DEFAULT':   return '默认';break;
                        //     case 'SHOP':   return '店铺';break;
                        // }
                    }
				},
        {
          label: '齐套标识',
          prop: 'custom_kit_tag'
        },
        {
					label: '创建人',
					prop: 'creator_name'
				},  {
					label: '创建时间',
					prop: 'create_time',
                    format: 'dataFormat1'

				},{
					label:'操作',
					slot:'do'
				}
			]
		}
	},
	methods:{
		del(row){
			let self = this;
			console.log(row);

			this.ajax.postStream('/custom-web/api/apartConfig/delete',{
				"custom_apart_config_id": row.custom_apart_config_id
				},function(response){
				if(response.body.result){

					self.$message.success('删除成功')
					self.searching()
				}
				else{
					self.$message.error(response.body.msg)
				}

			}, err => {
				this.$message.error(err);
			});
		},
		mod(obj){
				var params = {};
				if(obj){
					params = JSON.parse(JSON.stringify(obj))
				}else{

				}
				let self = this;
				params.callback = function(d){
					self.searching();
				}
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/dz_customer/modules/config/addApart'),
                	style:'width:800px;height:500px',
                    title:obj?'编辑采购订单规则':'新增采购订单规则',
					params:params
            	});
			},
		preValid(api){
			var _this = this;
			var url = "/order-web/api/transit"+api;
			// 事件前验证
			if(_this.multipleSelection.length==0){
				_this.$alert('没有选择任何数据，请先选择数据！', '提示', {
					confirmButtonText: '确定'
				})
			}else{
				var custIdList = [];
				_this.multipleSelection.forEach(function(item,index,array){
					custIdList.push(item.cust_id);
				});

				this.ajax.postStream(url,custIdList,function(response){
					if(response.body.result){
						_this.searching();
						_this.$message({
									message: '操作成功',
										type: 'success'
								});
						// 重置业务操作
						_this.actions_value = "";
					}
				});
			}
		},

		// 查看详情
		viewDetail(obj){
				let self = this;
			var params = {};
				if(obj){
					params.data = JSON.parse(JSON.stringify(obj))
				}else{

				}
				params.callback = function(d){
					self.searching();

				}
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/dz_customer/modules/config/addApart'),
                	style:'width:800px;height:500px',
                    title:obj?'编辑采购订单规则':'新增采购订单规则',
					params:params
            	});
		},
		openSearch(){
			this.showSearch = !this.showSearch
		},
		sizeChange(size){
			// 第页数改变
			this.search.page_size = size;
			this.searching();
		},
		pageChange(page_no){
			// 页数改变
			this.pageNow = page_no;
			this.search.page_no = page_no;
			this.searching();
		},
		preSearching(){
			this.searchName = this.search.name
			this.searching()
		},
		//列表显示
		searching(d, resolve){
			if(d) {
				this.search.where = d
			}
			var _this = this;
			this.ajax.postStream('/custom-web/api/apartConfig/list',_this.search,function(response){
				if(response.body.result){
					_this.dataList = response.body.content.list;
					_this.count = response.body.content.count;
				}
				else{
					_this.$message.error(response.body.msg)
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
		refresh(){
			this.searching()
		},
		radioChange(obj) {
			this.returnObj = obj
		},
		rowDblclick(obj) {
			if(this.params.isAlert) {
				this.params.close(obj)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
		}
	},
	mounted: function(){
		var _this = this;
		_this.searching();
		// 检测新增、编辑数据，刷新
		_this.$root.eventHandle.$on('close_addCustomer',function(){
			_this.searching();
		})

		// 弹窗打开此组件,新增销售订单时会调用
		if(_this.params.isAlert) {
			this.selection = 'radio'
			this.btns = [{
				type: 'primary',
				txt: '确认',
				click() {
					if(!_this.returnObj){
						this.$message.error("请选择数据");
						return;
					}
					_this.params.close(_this.returnObj);
					_this.$root.eventHandle.$emit('removeAlert',_this.params.alertId)
				}
			}]
		}
	},
	destroyed(){
		this.$root.offEvents('close_addCustomer');
	}
}
</script>
