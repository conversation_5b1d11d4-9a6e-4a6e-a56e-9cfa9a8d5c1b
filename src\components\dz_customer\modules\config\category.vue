<!--
 * @Author: your name
 * @Date: 2021-03-18 17:43:10
 * @LastEditTime: 2021-03-22 11:28:25
 * @LastEditors: Please set LastEditors
 * @Description: 类目配置
 * @FilePath: \front-dev\src\components\dz_customer\modules\config\clours.vue
-->
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                "code": "CUSTOM_GOODS_CATEGORY", 
                "createTime": null, 
                "creator": null, 
                "id": 3206102836142, 
                "modifier": null, 
                "modifyTime": 1616211074000, 
                "name": "定制商品类目", 
                "parentCode": null, 
                "parentName": null, 
                "platform": "NEW_SALE_PLATFORM", 
                "remark": "类目代码（第4位）", 
                "system": 0
            }
        }
    }
}
</script>