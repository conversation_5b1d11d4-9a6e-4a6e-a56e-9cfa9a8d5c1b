<!--
 * @Author: your name
 * @Date: 2021-03-18 17:43:10
 * @LastEditTime: 2021-03-22 11:28:09
 * @LastEditors: Please set LastEditors
 * @Description: 花色配置
 * @FilePath: \front-dev\src\components\dz_customer\modules\config\clours.vue
-->
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                 "code": "CUSTOM_GOODS_COLOR", 
                "createTime": null, 
                "creator": null, 
                "id": 3206102836141, 
                "modifier": null, 
                "modifyTime": 1616211074000, 
                "name": "定制商品花色", 
                "parentCode": "CUSTOM_GOODS_MATERIAL", 
                "parentName": "定制商品材质", 
                "platform": "NEW_SALE_PLATFORM", 
                "remark": "花色代码（第6,,7,8位）", 
                "system": 0
            }
        }
    }
}
</script>