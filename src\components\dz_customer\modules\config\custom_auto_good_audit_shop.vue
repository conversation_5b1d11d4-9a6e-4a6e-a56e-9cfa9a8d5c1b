
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                 "code": "CUSTOM_AUTO_GOOD_AUDIT_SHOP", 
                "createTime": null, 
                "creator": null, 
                "id": 205039663071233, 
                "modifier": null, 
                "modifyTime": 1616211074000, 
                "name": "定制自动货审店铺配置", 
                "parentCode": "", 
                "parentName": "", 
                "platform": "NEW_SALE_PLATFORM", 
                "remark": "编码为店铺编码，名称为店铺名称", 
                "system": 0
            }
        }
    }
}
</script>