<!--周期配置列表-->
<template>
	<xpt-list 
		:data='dataList' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='count' 
		:searchPage='search.page_name' 
		:selection='selection' 
		@search-click='searching' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange' 
		@radio-change='radioChange' 
		@row-dblclick='rowDblclick'  
		ref='dataList'
	>
		<template slot='do' slot-scope='scope'>
				<el-button size='mini' type='success' @click='viewDetail(scope.row)'>修改</el-button>
				<el-button size='mini' type='danger' @click='del(scope.row)'>删除</el-button>
				
		</template>
	</xpt-list>
</template>
<script>
import {
  getMap,
} from "@components/dz_customer/common/clientDictionary";
export default {
	props:["params"],
	
	data(){
		let self = this
		return {
			c_status:[],
			showSearch:false,
			search:{
				page_size: self.pageSize,     //页数
				page_no:1,   //页码
                page_name: 'custom_order_status_cycle_config',
                where: []
			},
			dataList:[],
			actions_value:"",
			count:0,
			pageNow:1,
			multipleSelection: [],
			// 单选选中的行
			returnObj:"",
			selectId:"",
			searchName:'',

			selection: '',
			btns: [
				{
					type: 'success',
					txt: '刷新',
					click() {
						self.refresh()
					}
				}, {
					type: 'primary',
					txt: '新增',
					click() {
						self.mod()
					}
				}
			],
			cols: [
				{
					label: '订单类型',
					prop: 'trade_type',
					formatter(val){
                        switch(val){
                            case 'ORIGINAL':   return '正单';break;
                            case 'SUPPLY':   return '补单';break;
                        }
                    }
				}, {
					label: '订单状态',
					prop: 'end_client_status',
					formatter(val){
                       return self.c_status[val]
                    }
				}, {
					label: '预计周期',
					prop: 'expected_cycle',
					formatter(val){
						if(!!val){
							return val+'天'
						}else{
							return val
						}
					}
                    
				},  {
					label: '状态',
					prop: 'status',
					 formatter(val){
                        switch(val){
                            case false:   return '失效';break;
                            case true:   return '生效';break;
                        }
                    }
				}, {
					label: '创建人',
					prop: 'creator_name',

				}, {
					label: '创建时间',
					prop: 'create_time',
                    format: 'dataFormat1'

				}, {
					label: '操作',
					slot: 'do',

				}
			]
		}
	},
	methods:{
		del(row){
			let self = this;
			console.log(row);

			this.ajax.postStream('/custom-web/api/cycleConfig/list',{
				"custom_order_status_cycle_config_id": row.custom_order_status_cycle_config_id
				},function(response){
				if(response.body.result){
					self.searching()
				}
				else{
					self.$message.error(response.body.msg)
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
		mod(obj){
			
				var params = {};
				if(obj){
					params = JSON.parse(JSON.stringify(obj))
				}else{
					
				}
				let self = this;
				params.callback = function(d){
					self.searching();
				}
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/dz_customer/modules/config/addCycle'),
                	style:'width:500px;height:300px',
                    title:obj?'编辑周期配置':'新增周期配置',
					params:params
            	});
			},
	
		// 查看详情
		viewDetail(obj){
				let self = this;
			var params = {};
				if(obj){
					params.data = JSON.parse(JSON.stringify(obj))
				}else{
					
				}
				params.callback = function(d){
					self.searching();
					
				}
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/dz_customer/modules/config/addCycle'),
                	style:'width:500px;height:300px',
                    title:'编辑周期配置',
					params:params
            	});
		},
		openSearch(){
			this.showSearch = !this.showSearch
		},
		sizeChange(size){
			// 第页数改变
			this.search.page_size = size;
			this.searching();
		},
		pageChange(page_no){
			// 页数改变
			this.pageNow = page_no;
			this.search.page_no = page_no;
			this.searching();
		},
		preSearching(){
			this.searchName = this.search.name
			this.searching()
		},
		//列表显示
		searching(d, resolve){
			if(d) {
				this.search.where = d
			}
			var _this = this;
			if(_this.params.isAlert){
				_this.search.effective_status = 'B';
			}
			this.ajax.postStream('/custom-web/api/cycleConfig/list',_this.search,function(response){
				if(response.body.result){
					_this.dataList = response.body.content.list;
					_this.count = response.body.content.count;
				}
				else{
					_this.$message.error(response.body.msg)
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
		refresh(){
			this.searching()
		},
		radioChange(obj) {
			this.returnObj = obj
		},
		rowDblclick(obj) {
			if(this.params.isAlert) {
				this.params.close(obj)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
		}
	},
	mounted: function(){
		var _this = this;
		_this.searching();
		// 检测新增、编辑数据，刷新
		_this.$root.eventHandle.$on('close_addCustomer',function(){
			_this.searching();
		})
		getMap((map) => {
				let cs = map.client_status.filter((item) => true);
				cs.forEach((item) => {
				this.c_status[item.value] = item.label;
				});
			});
		// 弹窗打开此组件,新增销售订单时会调用
		if(_this.params.isAlert) {
			this.selection = 'radio'
			this.btns = [{
				type: 'primary',
				txt: '确认',
				click() {
					if(!_this.returnObj){
						this.$message.error("请选择数据");
						return;
					}
					_this.params.close(_this.returnObj);
					_this.$root.eventHandle.$emit('removeAlert',_this.params.alertId)
				}
			}]
		}
	},
	destroyed(){
		this.$root.offEvents('close_addCustomer');
	}
}
</script>