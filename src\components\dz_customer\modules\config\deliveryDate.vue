<!-- 交期管理 -->
<template>
	<dz-list 
        ref='supplierGoods'
        id='suoolier_goodList' 
        :data='goodsList' 
        :btns='goodsBtns' 
        :colData='goodsCols' 
		:showTools="false"
		simpleSearch
        selection='radio'
        orderNo 
		searchHolder="搜索流程节点"
        @radio-change='goodsRadioChange'
		@search-click="searchClick"
    >
    </dz-list>
</template>
<script>
import dzList from '../../components/list/list'
import { goods_status, getMap } from '../../common/goodsStatusDictionary' 
export default {
    components: {
        dzList
    },
	data() {
		let self = this;
        
		return {
			goodsList: [],
			goodsBtns: [
				{
					type: 'primary',
					txt: '刷新',
					disabled: () => {
						return false;
					},
					click() {
						self.refresh()
					}
				}, {
					type: 'success',
					txt: '保存',
					disabled: () => {
						return false;
					},
					click() {
						self.save()
					}
				}, {
					type: 'primary',
					txt: '新增',
					disabled: () => {
						return false;
					},
					click() {
                        self.goodsAdd()
					}
				},
				{
					type: 'danger',
					txt: '删除',
					disabled: () => {
						return false;
					},
					click() {
                        self.delDate()
					}
				}
			],
			goodsCols: [],
			goodsSelect: ''
		}
	},
    created() {
        let goods_status_obj = {}
        getMap(goods=> {
            goods.goods_status.forEach(item => {
                goods_status_obj[item.value] = item.label
            })
            this.goodsCols = [
                
                {
                    label: '流程节点',
                    prop: 'process_node_code',
                    width: 180,
                    bool: true,
                    isSelect: true,
                    obj: goods_status_obj
                },
                {
					label: '报警周期（天）',
					prop: 'warning_period',
					width: 150,
					type: 'number',
					bool: true,
                    isInput: true
				},{
					label: '状态',
					prop: 'status',
					width: 150,
					bool: true,
                    isSelect: true,
					obj: {
						0: '失效',
						1: '生效'
					}
				},{
					label: '说明',
					prop: 'remark',
					bool: true,
                    isInput: true
				}, {
					label: '建档人',
					prop: 'creatorName',
                    width: 130
				}, {
					label: '创建时间',
                    prop: 'create_time',
					format: 'dataFormat1',
					width: 130
				}
			
            ]
        })
    },
	methods: {
        refresh () {
            this.getGoodsList()
        },
		// 行选中
		goodsRadioChange(row) {
			this.goodsSelect = row;
		},
		searchClick(keyword) {
		/**
		 * @description: 输入框搜索
		 * @param {*}
		 * @return {*}
		 */	
			let index
			let goodsCopy = this.goodsCopy.map(item => {
				index = goods_status.findIndex(s => s.value === item.process_node_code)
				index !== -1 && (item.node_name = goods_status[index].label)
				return item
			})
			this.goodsList = !keyword.trim() ? this.goodsCopy : goodsCopy.filter(item => item.node_name.indexOf(keyword) !== -1)
		},
		// 获取商品信息
		getGoodsList(resolve) {
			let self = this;
			let url = '/custom-web/api/customDeliveryDate/list'
			this.ajax.postStream(url, {}, d => {
				if(d.body.result && d.body.content) {
					this.goodsList = Array.isArray(d.body.content.list) ? d.body.content.list.map(item => {
						item.status = String(item.status)
						return item
					}) : d.body.content.list
					this.goodsCopy =  JSON.parse(JSON.stringify(this.goodsList))
					if(self.goodsList.length > 0){
						self.goodsSelect = this.goodsList[0];
						self.$refs.supplierGoods && self.$refs.supplierGoods.setRadioSelect(self.goodsList[0]);
					}
				}
				resolve && resolve();
			}, (e) => {
				this.$message.error(e);
				resolve && resolve();
			})
		},
		delDate() {
			/**
			 * @description: 删除交期
			 * @param {*}
			 * @return {*}
			 */	
			if(!this.goodsSelect.delivery_date_id)  {
				this.goodsList.splice(this.goodsList.findIndex(item => item.temId === this.goodsSelect.temId), 1)
				return
			}
			this.$confirm("确认删除交期？", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then(() => {
				this.ajax.postStream(
					"/custom-web/api/customDeliveryDate/delete",
					[
						{
							delivery_date_id: this.goodsSelect.delivery_date_id
						}
					],
					(res) => {
						res = res.body;
						this.$message({
							message: res.msg,
							type: res.result ? "success" : "error",
						});
						res.result && this.getGoodsList()
					}
				);
				})
				.catch(() => {
					this.$message({
						type: "info",
						message: "已取消删除",
					});
				});
		},
		// 新增，复制新增
		goodsAdd() {
			let copyData = {
				process_node_code: goods_status[0].value,
				warning_period: 7,
				status: '1',
				remark: ''
			}
			copyData.temId = new Date().getTime()
            this.goodsList.unshift(copyData)
            this.goodsSelect = copyData;
            // 滚动到最底部
            let scrollObj = document.querySelector('#suoolier_goodList').querySelector('.el-table__body-wrapper')
            this.$nextTick(function(){
                // scrollObj.scrollTop = scrollObj.scrollHeight
                scrollObj.scrollTop = 0
            })
		},
		isChange(item) {
		/**
			 * @description: 比较数据是否发生变化
			 * @param {*}
			 * @return {*}
			 */	
			let index = this.goodsCopy.findIndex(goods => goods.delivery_date_id === item.delivery_date_id)
			if(index === -1) return true
			let copyItem = this.goodsCopy[index]
			for(let key in item) {
				if(item[key] !== copyItem[key]) return true
			}
			return false
		},
        save () {
			let self = this
			// 校验交期节点
			let nodeMap = {}
			if(this.goodsList.findIndex(item => {
				let status = nodeMap[item.process_node_code]
				!status && (nodeMap[item.process_node_code] = true)
				return status
			}) !== -1) {
				this.$message.error('交期节点不能重复选择！')
				return
			}
			// 校验报警周期为两位正整数
			if(this.goodsList.findIndex(item => {
				return Number(item.warning_period) !== parseInt(item.warning_period) || Number(item.warning_period) >= 100 || Number(item.warning_period) < 0
			}) !== -1) {
				this.$message.error('报警周期只能为正整数')
				return
			}

			let data = []
			this.goodsList.forEach(item => {
				if(item.delivery_date_id) {
					// 比较是否发生变化
					this.isChange(item) && data.push(item)
				} else {
					data.push(item)
				}
			})

            this.ajax.postStream('/custom-web/api/customDeliveryDate/save',data.map(item => {
				return {
					delivery_date_id: item.delivery_date_id,
					warning_period: item.warning_period,
					process_node_code: item.process_node_code,
					status: item.status,
					remark: item.remark
				}
			}), (d) => {
                if(d.body.result){
                    this.$message.success(d.body.msg)
					setTimeout( () => {
						self.getGoodsList() 
					}, 1000)
                }else{
                    this.$message.error(d.body.msg)
                }
                
            }, err => {
                this.$message.error(err);
            });
        }
	},
	destroyed () {
		this.timer = null
	},
	mounted () {
        this.getGoodsList()
    }
}
</script>
