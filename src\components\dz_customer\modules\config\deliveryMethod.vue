<!--
 * @Author: your name
 * @Date: 2021-03-18 17:43:10
 * @LastEditTime: 2021-05-21 16:52:25
 * @LastEditors: Please set LastEditors
 * @Description: 提货方式
 * @FilePath: \front-dev\src\components\dz_customer\modules\config\deliveryMethod.vue
-->
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                "code":"CUSTOM_DISTRIBUTION",
                "createTime":1621586088000,
                "creator":48,
                "id":140050546819072,
                "modifier":48,
                "modifyTime":1621586475000,
                "name":"定制店铺配送方式",
                "parentCode":"CUSTOM_SHOP",
                "parentName":"定制店铺",
                "platform":"NEW_SALE_PLATFORM",
                "remark":"",
                "system":0
            }
        }
    }
}
</script>