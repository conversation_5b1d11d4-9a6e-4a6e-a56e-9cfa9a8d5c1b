
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                 "code": "DZ_SPLIT_STORE_LOGO", 
                "createTime": null, 
                "creator": null, 
                "id": 229400781930507, 
                "modifier": null, 
                "modifyTime": 1616211074000, 
                "name": "定制拆单标识门店", 
                "parentCode": "", 
                "parentName": "", 
                "platform": "NEW_SALE_PLATFORM", 
                "remark": "定制拆单标识门店", 
                "system": 0
            }
        }
    }
}
</script>