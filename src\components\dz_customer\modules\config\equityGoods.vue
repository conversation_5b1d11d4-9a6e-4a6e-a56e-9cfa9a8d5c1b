<!-- 定制线上权益商品列表 -->
<template>
	<xpt-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:searchPage='search.page_name'
		:pageTotal='count'
		selection="checkbox"
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
		@search-click='searchClick'
		@selection-change='selectionChange'
		
		ref="xptList"
	>
	<template slot='do' slot-scope='scope'>
		<!-- <el-switch v-model="scope.row.if_solve" on-text="是" off-text="否" on-value="Y" off-value="N" @change="rowChange(scope.row)"></el-switch> -->
		<el-button type='primary' size='mini' @click="edit(scope.row)">编辑</el-button>
		<el-button type='danger' size='mini' @click="del(scope.row)">删除</el-button>
			
	</template>
	<template slot='if_shop' slot-scope='scope'>
		<el-button type='success' size='mini' @click="getShop(scope.row)">查看更多</el-button>
			
	</template>
	
	</xpt-list>
</template>
<script>
// import countList from '@components/common/list-count'
export default {
    props:['params'],
	// components: {
    //     countList,
    //   },
	data() {
		return {
			showCount:false,
			list: [],
			btns: [{
					type: 'primary',
					txt: '刷新',
					click: () => this.getList(),
				}, {
					type: 'primary',
					txt: '新增',
					click: () => this.addOrder('新增权益商品',{}),
				}, 
			],
			cols: [{
				label: '平台店铺编码',
				prop: 'platform_shop_code',
				// redirectClick: d => this.openConsultation(d.consultion_id),
				width: 100
			}, {
				label: '平台店铺名称',
				prop: 'platform_shop_name',
				width: 180
			},{
				label: '平台商品编码',
				prop: 'platform_goods_id',
				width: 180
			},
			 {
				label: '权益名称',
				prop: 'equity_name',
				width: 150,
			}, {
				label: '让利金额',
				prop: 'benefit_amount',
				width: 150,
			}, {
				label: '让利额度使用门槛',
				prop: 'benefit_amount_threshold',
				width: 150,
			},   {
				label: '生效开始时间',
				prop: 'effect_begin_time',
				format: 'dataFormat1',
				width: 150,
			}, {
				label: '生效结束时间',
				prop: 'effect_end_time',
				format: 'dataFormat1',
				width: 150,
			},{
				label: '创建人',
				prop: 'creator_name',
				width: 100
			},{
				label: '创建时间',
				prop: 'create_time',
				format: 'dataFormat1',
				width: 100
			},{
				label: '操作',
				slot: 'do',
				width: 100
			},],
			search: {
				page_no:1,
				page_size: this.pageSize,
				
				page_name: 'custom_equity_goods',
				where: []
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
		getShop(row){
			let self = this;
			this.$root.eventHandle.$emit('alert',{
				title: '店铺列表',
				style:'width:800px;height:600px',
				component:()=>import('./shopList.vue'),
				params: {
					custom_equity_id:row.custom_equity_id
				},
			})
		},
		edit(row){
			let params = {row:row}
			this.$root.eventHandle.$emit('creatTab', {
				name:'编辑权益商品',
				params:params,
				component: () => import('./addEquityGoods.vue')
			});
		},
		del(row){
			let self = this;
			this.$alert('删除操作不可逆，确定继续？', '注意', {
			confirmButtonText: '确定',
			callback: action => {
				self.ajax.postStream('/custom-web/api/equityGoods/delete',{custom_equity_id:row.custom_equity_id},(response)=>{
					if(response.body.result){
						self.getList();
					}else{
						self.$message.error(response.body.msg)
					}
				},e=>{
					self.$message.error(e)
				})
			}
			});
		},
		addOrder(name,params){
			this.$root.eventHandle.$emit('creatTab', {
				name:name,
				params:params,
				component: () => import('./addEquityGoods.vue')
			});
		},
	
		
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			let self = this;
			this.search.where = obj;
			new Promise((res,rej)=>{
					self.getList(resolve,res);
				}).then(()=>{
					if(this.search.page_no != 1){
						self.count = 0;
					}
					self.showCount = false;
				})
			},
		selectionChange(obj) {
			this.selectData = obj;
		},
		getList(resolve,callback) {
			this.ajax.postStream("/custom-web/api/equityGoods/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
					// if(!this.showCount){
					// 	let total = this.search.page_no * this.search.page_size
					// 	this.count = res.body.content.length == (this.search.page_size+1)? total+1:total;
              		// 	this.list = res.body.content.splice(0,res.body.content.length == (this.search.page_size+1)?total:res.body.content.length);

					// }
				} else {
					this.$message.error(res.body.msg)
				}
				callback && callback();
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				callback && callback();
				resolve && resolve()
			});
		},
		
    

	},
	mounted() {
		// this.getList()
	},
}
</script>
<style module>
.unread-tag td:nth-child(2):after {
	content: '';
	position: absolute;
    right: 4px;
    top: 9px;
    border: 4px solid red;
    border-radius: 100%;
}
</style>
