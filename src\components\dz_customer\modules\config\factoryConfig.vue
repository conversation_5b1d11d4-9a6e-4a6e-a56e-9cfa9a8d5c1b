<!--工厂配置列表-->
<template>
	<xpt-list 
		:data='dataList' 
		:btns='btns' 
		:colData='cols' 
		:pageTotal='count' 
		:searchPage='search.page_name' 
		:selection='selection' 
		@search-click='searching' 
		@page-size-change='sizeChange' 
		@current-page-change='pageChange' 
		@radio-change='radioChange' 
		@row-dblclick='rowDblclick'  
		ref='dataList'
	>
	
	</xpt-list>
</template>
<script>
export default {
	props:["params"],
	data(){
		let self = this
		return {
			showSearch:false,
			search:{
				page_size: self.pageSize,     //页数
				page_no:1,   //页码
                page_name: 'custom_factory_config',
                where: []
			},
			dataList:[],
			actions_value:"",
			count:0,
			pageNow:1,
			multipleSelection: [],
			// 单选选中的行
			returnObj:"",
			selectId:"",
			searchName:'',

			selection: '',
			btns: [
				{
					type: 'success',
					txt: '刷新',
					click() {
						self.refresh()
					}
				}, {
					type: 'primary',
					txt: '新增',
					click() {
						self.mod()
					}
				}, {
					type: 'primary',
					txt: '导出',
					click() {
						self.exportExcel()
					}
				}, {
					type: 'primary',
					txt: '导出下载',
					click() {
						self.showExportList('EXCEL_TYPE_LIST_CUSTOM_FACTORY_CONFIG')
					}
				}
			],
			cols: [
				{
					label: '工厂编码',
					prop: 'factory_code',
					redirectClick(row) {
						self.viewDetail(row)
					}
				}, {
					label: '工厂名称',
					prop: 'factory_name'
				}, {
					label: '规则类型',
					prop: 'rule_type',
                    formatter(val){
                        switch(val){
                            case 'CITY':   return '城市';break;
                            case 'DEFAULT':   return '默认';break;
                            case 'PROVINCE':   return '省份';break;
                        }
                    }
				}, {
					label: '省份',
					prop: 'province_name'
				},{
					label: '城市',
					prop: 'city'
				}, {
					label: '订单类别',
					prop: 'purchase_trade_type_cn',
				}, {
					label: '状态',
					prop: 'status',
					 formatter(val){
                        switch(val){
                            case false:   return '失效';break;
                            case true:   return '生效';break;
                        }
                    }
				},  {
					label: '基材',
					prop: 'baseCodes',
					 formatter(val){
                        if(val){
							return val.join(',')
						}
                    }
				},  {
					label: '花色',
					prop: 'colorCodes',
					 formatter(val){
                        if(val){
							return val.join(',')
						}
                    }
				}, {
					label: '生效时间',
					prop: 'effect_time_start',
                    format: 'dataFormat1'

				}, {
					label: '失效时间',
					prop: 'effect_time_end',
                    format: 'dataFormat1'

				}
			]
		}
	},
	methods:{
		// 导出功能
      exportExcel() {
        
            this.exportBtnStatus = true;
            this.ajax.postStream('/custom-web/api/factoryConfig/exportConfigList', this.search, res => {
              this.exportBtnStatus = false;
              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              })
            }, err => {
              this.$message.error(err);
              this.exportBtnStatus = false;
            })
        
      },
		// 导出报表
		showExportList (exportType){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/after_sales_report/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: exportType,
					},
				},
			})
		},
		mod(obj){
				var params = {};
				if(obj){
					params = JSON.parse(JSON.stringify(obj))
				}else{
					
				}
				let self = this;
				params.callback = function(d){
					self.searching();
				}
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/dz_customer/modules/config/addFactory'),
                	style:'width:800px;height:500px',
                    title:obj?'编辑工厂配置':'新增工厂配置',
					params:params
            	});
			},
		preValid(api){
			var _this = this;
			var url = "/order-web/api/transit"+api;
			// 事件前验证
			if(_this.multipleSelection.length==0){
				_this.$alert('没有选择任何数据，请先选择数据！', '提示', {
					confirmButtonText: '确定'
				})
			}else{
				var custIdList = [];
				_this.multipleSelection.forEach(function(item,index,array){
					custIdList.push(item.cust_id);
				});

				this.ajax.postStream(url,custIdList,function(response){
					if(response.body.result){
						_this.searching();
						_this.$message({
									message: '操作成功',
										type: 'success'
								});
						// 重置业务操作
						_this.actions_value = "";
					}
				});
			}
		},
		
		// 查看详情
		viewDetail(obj){
				let self = this;
			var params = {};
				if(obj){
					params.data = JSON.parse(JSON.stringify(obj))
				}else{
					
				}
				params.callback = function(d){
					self.searching();
					
				}
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/dz_customer/modules/config/addFactory'),
                	style:'width:800px;height:500px',
                    title:obj?'编辑工厂配置':'新增工厂配置',
					params:params
            	});
		},
		openSearch(){
			this.showSearch = !this.showSearch
		},
		sizeChange(size){
			// 第页数改变
			this.search.page_size = size;
			this.searching();
		},
		pageChange(page_no){
			// 页数改变
			this.pageNow = page_no;
			this.search.page_no = page_no;
			this.searching();
		},
		preSearching(){
			this.searchName = this.search.name
			this.searching()
		},
		//列表显示
		searching(d, resolve){
			if(d) {
				this.search.where = d
			}
			var _this = this;
			if(_this.params.isAlert){
				_this.search.effective_status = 'B';
			}
			this.ajax.postStream('/custom-web/api/factoryConfig/list',_this.search,function(response){
				if(response.body.result){
					_this.dataList = response.body.content.list;
					_this.count = response.body.content.count;
				}
				else{
					_this.$message.error(response.body.msg)
				}
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
			});
		},
		refresh(){
			this.searching()
		},
		radioChange(obj) {
			this.returnObj = obj
		},
		rowDblclick(obj) {
			if(this.params.isAlert) {
				this.params.close(obj)
				this.$root.eventHandle.$emit('removeAlert', this.params.alertId)
			}
		}
	},
	mounted: function(){
		var _this = this;
		_this.searching();
		// 检测新增、编辑数据，刷新
		_this.$root.eventHandle.$on('close_addCustomer',function(){
			_this.searching();
		})

		// 弹窗打开此组件,新增销售订单时会调用
		if(_this.params.isAlert) {
			this.selection = 'radio'
			this.btns = [{
				type: 'primary',
				txt: '确认',
				click() {
					if(!_this.returnObj){
						this.$message.error("请选择数据");
						return;
					}
					_this.params.close(_this.returnObj);
					_this.$root.eventHandle.$emit('removeAlert',_this.params.alertId)
				}
			}]
		}
	},
	destroyed(){
		this.$root.offEvents('close_addCustomer');
	}
}
</script>