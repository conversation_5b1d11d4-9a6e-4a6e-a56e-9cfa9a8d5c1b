<!--新增辅资料值-->
<template>
	<div>
		<xpt-headbar>
			<el-button type='primary' size='mini' @click='submit' slot='left'>确认</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click='close' slot='right'>关闭</el-button>
		</xpt-headbar>

		<el-form :model='data' ref='data' :rules='rules' label-position="right" label-width="120px">
			<el-row	:gutter='40'>
				<el-col :span='20'>
					
				
				
					<el-form-item label="名称" >
						<span v-for="(item, key)  in data.list" :key="key">
							<el-input v-model="item.code" size='mini' style="width:95%;" ></el-input>
							<i class='el-icon-delete' @click="del(item, key)"></i>
						</span>
						<span @click="addNew" class="add_new">增加</span>
						
					</el-form-item>
				
					
				</el-col>

			
				
			</el-row>
			<el-row	:gutter='40'>
			</el-row>
		</el-form>
	</div>
</template>
<script>
	export default {
		data(){
			let self = this;
			return {
				province:[],
				city:[],
				data:{
					
					list:[
						{code:''}
					],
				},
				rules:{
					acode:[{
						required:true,
						message:'请输入编码',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}  
					}],
					number:[{
						required:true,
						message:'请输入商品编码',
						isShow:false,
						validator: function(rule,value,callback){
							// 数据校验
							if(value){
								self.rules[rule.field][0].isShow = false
								callback();
							}else{
								self.rules[rule.field][0].isShow = true
								callback(new Error(''));
							}
						}  
					}],
					type:[{
						isShow:false,
						required:true,
                        message:'请选择订单类型',
                        trigger:'change',
                        validator:(rule,value,callback)=>{
                            if(self.data.shop_code && self.data.rule_type =='SHOP'){
                                if(value){
                                    self.rules[rule.field][0].isShow = false;
                                    callback()
                                }else{
                                    self.rules[rule.field][0].isShow = true;
                                    callback(new Error(''))
                                }
                            }else{
                                self.rules[rule.field][0].isShow = false;
                                callback()
                            }
                        }
					}],
				},
				updateCount:0,
				provinceFlag:false,
				pickerOptions0: {
		          	disabledDate(time) {
		            	return time.getTime() < Date.now() - 8.64e7;
		          	}
		        },
				pickerOptions1: {
		          	disabledDate(time) {
						  let enableTime = Date.now() - 8.64e7;
							if(self.data.effect_time_start) {
								enableTime = self.data.effect_time_start;
							}
							return time.getTime() < enableTime;
		            	// return time.getTime() < new Date(self.effect_time_start).getTime() ;
		          	}
		        }
			}
		},
		props:['params'],
		methods:{
			del(item, key){
				console.log(item, key)
				this.data.list.splice(key,1)
			},
			addNew(){
				if(this.data.list.length<=9)
                this.data.list.push({code:''})
			},
		
	
	  
		
			// 保存资料
			submit(callback){
				let self = this;
				let list = []
				let flag = false;
				this.data.list.forEach(item=>{
					if(!/^[\u4E00-\u9FA5A-Za-z0-9\(\)\（\）\-\_\°\φ]{1,30}$/.test(item.code)){
						flag = true;
					}
					if(!!item.code){
						list.push(item)
					}
				})
				if(!!flag){
					this.$message.error('不能输入特殊字符并且长度在1-30位之间')
					return;
				}
				this.params.callback({list});
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
			// 关闭
			close(){
				this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
			},
			
			
		},
		mounted(){
			var self = this;
			if(self.params.list){
				self.data.list = self.params.list.map(item=>{
					return{
						code:item
					}
				});
			}
		
		},
		computed: {
			
		}
	}
</script>
<style scoped>
.add_new{
	cursor: pointer;
	color: #0000ee;
}
</style>