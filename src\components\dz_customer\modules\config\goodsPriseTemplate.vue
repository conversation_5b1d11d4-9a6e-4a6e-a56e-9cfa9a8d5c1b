
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                 "code": "CUSTOM_GOODS_PRICE_TEMPLATE", 
                "createTime": null, 
                "creator": null, 
                "id": 220649766535176, 
                "modifier": null, 
                "modifyTime": 1616211074000, 
                "name": "三维家价格模板配置", 
                "parentCode": "CUSTOM_GOODS_TYPE_CODE", 
                "parentName": "定制三维家商品类型", 
                "platform": "NEW_SALE_PLATFORM", 
                "remark": "", 
                "system": 0
            }
        }
    }
}
</script>