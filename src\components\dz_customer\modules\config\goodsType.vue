<!--
 * @Author: your name
 * @Date: 2021-03-18 17:43:10
 * @LastEditTime: 2021-03-22 11:28:09
 * @LastEditors: Please set LastEditors
 * @Description: 花色配置
 * @FilePath: \front-dev\src\components\dz_customer\modules\config\clours.vue
-->
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                 "code": "CUSTOM_GOODS_TYPE_CODE", 
                "createTime": null, 
                "creator": null, 
                "id": 220649522610195, 
                "modifier": null, 
                "modifyTime": 1616211074000, 
                "name": "三维家商品类型配置", 
                "parentCode": "", 
                "parentName": "", 
                "platform": "NEW_SALE_PLATFORM", 
                "remark": "", 
                "system": 0
            }
        }
    }
}
</script>