<!--
 * @Author: your name
 * @Date: 2021-03-18 17:43:10
 * @LastEditTime: 2021-03-22 11:28:49
 * @LastEditors: Please set LastEditors
 * @Description: 材质配置
 * @FilePath: \front-dev\src\components\dz_customer\modules\config\clours.vue
-->
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                "code": "CUSTOM_GOODS_MATERIAL", 
                "createTime": null, 
                "creator": null, 
                "id": 3206102836144, 
                "modifier": 2256, 
                "modifyTime": 1616212722000, 
                "name": "定制商品材质", 
                "parentCode": "", 
                "parentName": null, 
                "platform": "NEW_SALE_PLATFORM", 
                "remark": "材质代码（第5位）", 
                "system": 0
            }
        }
    }
}
</script>