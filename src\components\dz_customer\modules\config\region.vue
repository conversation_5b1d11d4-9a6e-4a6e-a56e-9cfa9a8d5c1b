<template>
	<div class='xpt-flex'>
		<xpt-headbar>
			<el-button type='primary' size='mini' @click="openAddRegion" slot='left'>新增</el-button>
			<el-button type='danger' size='mini' @click='del' slot='left' :loading='delBtnStatus' :disabled='delBtnStatus'>删除</el-button>
			<el-button type='success' size='mini' @click='_getData()' slot='left' :loading='refreshBtnStatus' :disabled='refreshBtnStatus'>刷新</el-button>
			<el-input placeholder="请输入区域名称" icon="search" size='mini' v-model="search.condition" :on-icon-click="searchData" slot='right'>
			</el-input>
		</xpt-headbar>
		<el-row class='xpt-flex__bottom'>
			<div class='xpt-flex'>
				<div class='el-tree-header'>
				    <ul>
				        <li>区域标签</li>
				        <li>区域编码</li>
						<li>区域经理</li>
				        <li>说明</li>
				    </ul>
			    </div>
				<div class='xpt-flex__bottom scroll'>
					<tree
					  	:data="munuList"
					  	:props="props" 
					  	node-key='area_id'
					  	show-checkbox 
					  	check-strictly 
					  	@check-change="handleCheckChange" 
					  	@user-click='userClick' ref='menuTree'>
					</tree>
				</div>
			</div>
		</el-row>
	</div>
</template>
<script>
	
	import tree from '@components/common/tree'
	export default {
		data(){
			let self = this;
			return {
				search:{
					code:'',
					url:'',
					type:'',
					app:'',
					name:'',
					condition:''
				},
				munuList:[],
				selectMenu:[],
				props: {
		          	label:[{
		          		name:'area_name',
		          		childNode:'a',
		          		event(d){
		          			self.$root.eventHandle.$emit("creatTab", {
								name: "编辑区域",
								component: () =>
								import("./addRegion.vue"),
								params: {
									row: d,
									parentAreaList: self.munuListCopy
								}
							});
		          		}
		          	},{
		          		name:'area_code'
		          	},{
		          		name:'areaUserName'
		          	},{
		          		name:'remark'
		          	}],
		          	children: 'childList'
		        },
		        checkData:'',
		        date:'',
		        time: new Date(),
		        delBtnStatus: false,
		        enableBtnStatus: false,
		        disableBtnStatus: false,
		        refreshBtnStatus: false
			}
		},
		methods:{
			/**
             * @description: 打开新增区域标签页
             * @param {*}
             * @return {*}
             */
            openAddRegion(){			
            	this.$root.eventHandle.$emit("creatTab", {
					name: "添加区域",
					component: () =>
					import("./addRegion.vue"),
					params: {
						parentAreaList: this.munuListCopy
					}
				});
			},
			_getData(){
				var _this = this;
				this.refreshBtnStatus = true;
		        this.ajax.postStream('/custom-web/api/customArea/list',{}, (data) => {
		           if(data.body.result){
		           		_this.munuList = data.body.content||[];
						_this.munuListCopy = this.getMunuListFlat(this.munuList)
		           } else {
		           		_this.$message.error(data.body.msg || '');
		           }
		           this.refreshBtnStatus = false;
		        }, (err) => {
		        	this.$message.error(err);
		            this.refreshBtnStatus = false;
		        });
			},
			getMunuListFlat(list) {
				/**
				* @description: 树结构扁平化
				* @param {*}
				* @return {*}
				*/	
				let end = 0
				let item
				let result = []
				let arr = JSON.parse(JSON.stringify(list))
				while(end < arr.length) {
					item = arr[end]
					if(Array.isArray(item.childList) && item.childList.length>0) {
						arr = arr.concat(item.childList)
						delete item.childList
					}
					result.push(item)
					end++
				}
				return result
			},
		
			del(){
				let self = this
				if(!this.selectMenu.length){
					self.$message({
						message:'请选择要删除的区域。',
						type:'error'
					})
					return;
				}
				let index = this.selectMenu.findIndex(item => item.childList && item.childList.length>0)
				if(index !== -1) {
					self.$message({
						message: `${this.selectMenu[index].area_name}区域下有下级区域不可删除！`,
						type: 'error'
					})
					return
				}
				this.delBtnStatus = true;
				this.ajax.postStream('/custom-web/api/customArea/delete',this.selectMenu.map(item => { return {area_id:item.area_id} }),function(d){
					self.delBtnStatus = false;
					self.$message({
						message:d.body.msg || '请求出错',
						type:d.body.result?'success':'error'
					})
					if(d.body.result){
						self._getData();
					}
					self.selectMenu = [];
					// 清除树的选择
					let checked = self.$refs.menuTree.getCheckedNodes();
					checked.find(l=>{
						self.$refs.menuTree.setChecked(l.area_id,false)
					})
					
				}, err => {
					this.delBtnStatus = false;
					this.$message.error(err);
				})
			},
			searchData(){
				/**
				 * @description: 搜索
				 * @param {*}
				 * @return {*}
				 */
				let keyword = this.search.condition.trim()
				let newList = JSON.parse(JSON.stringify(this.munuListCopy))
				let result = keyword ? newList.filter(item => item.area_name.indexOf(keyword) !== -1) : newList
				let l = result.length
				let item
				let childs = []
				let parentIndex
				for(let i=0; i<l; i++) {
					item = result[i]
					if(item.area_parent_id) {
						parentIndex = result.findIndex(child => child.area_id === item.area_parent_id)
						if(parentIndex !==-1) {
							!result[parentIndex].childList && (result[parentIndex].childList = [])
							result[parentIndex].childList.push(item)
							childs.push(item.area_id)
						}
					}
				}
				this.munuList = result.filter(item => !childs.includes(item.area_id))
				console.log(childs, this.munuList)
			},
			getResultByKeyword(item, keyword) {
			/**
			* @description: 根据关键词搜索下级
			* @param {*} obj上层符合条件的对象，keyword要匹配的关键词
			* @return {*} obj
			*/	
				if(!item.childList) {
					return item
				} else {
					let matchChildList = item.childList.filter(child => child.area_name.indexOf(keyword) !== -1)
					if(matchChildList.length === 0) {
						delete item.childList
						return item
					}
					item.childList = matchChildList
					let l = matchChildList.length
					for(let i=0; i<l; i<l) {
						this.getResultByKeyword(item.childList[i], keyword)
					}
					return item
				}
			},
			handleCheckChange(data,isChecked,childChecked){
				if(isChecked){
					this.selectMenu.unshift(data);
				}else{
					let i = this.selectMenu.length;
					while(i--){
						if(this.selectMenu[i].area_id===data.area_id){
							this.selectMenu.splice(i,1);
							break;
						}
					}
				}
			},
			userClick(d){
				this.checkData = d
			}
		},
		props:['params'],
		mounted(){
			this.$root.eventHandle.$on('saveArea',this._getData)
			this._getData()
		},
		destroyed(){
			this.$root.eventHandle.$off('saveArea',this._getData)
			// 销毁根节点的所有监听事件
			this.$root.offEvents('refreshMenu');
		},
		components:{
			'tree':tree
		}
	}
</script>