<!--
 * @Author: your name
 * @Date: 2021-03-18 17:43:10
 * @LastEditTime: 2021-05-21 16:42:56
 * @LastEditors: Please set LastEditors
 * @Description: 店铺
 * @FilePath: \front-dev\src\components\dz_customer\modules\config\shop.vue
-->
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                "code":"CUSTOM_SHOP",
                "createTime":1621586042000,
                "creator":48,
                "id":140050487181315,
                "modifier":48,
                "modifyTime":1621586441000,
                "name":"定制店铺",
                "parentCode":"",
                "parentName":null,
                "platform":"NEW_SALE_PLATFORM",
                "remark":"",
                "system":0
            }
        }
    }
}
</script>