<!-- 弹窗选择店铺列表 -->
<template>
	<div class='xpt-flex'>
		<xpt-headbar>
			<!-- <el-button type='info'  size='mini' @click="addNew" slot='left'>新增</el-button> -->
			<el-button type='success' size='mini' @click="getShopList()" slot='left' :disabled='refreshBtnStatus' :loading='refreshBtnStatus'>刷新</el-button>
		</xpt-headbar>
		<xpt-list
			:data='shopList'
			:btns='btns'
			:colData='cols'
			:searchPage='search.page_name'
			:pageTotal='pageTotal'
			:selection='selection'
			:showHead='false'
			:pageSizes='[10, 20, 50, 100, 200,1000]'
			:isNeedClickEvent="showHead"
			@search-click='searchData'
			@radio-change='radioChange'
			@selection-change='selectionChange'
			@page-size-change='pageSizeChange'
			@current-page-change='pageChange'
			@row-dblclick='rowDblclick'
		></xpt-list>

	</div>
</template>
<script>
export default {
	data(){
		let self = this
		return {
			btns: [],
			cols: [
				{
					label: '店铺编码',
					prop: 'shop_code',
					
				}, {
					label: '店铺名称',
					prop: 'shop_name'
				}
			],
			search:{
				page_name: 'cloud_shop_v2',
				where: [],
				page: {
					length: self.pageSize,
					pageNo: 1
				}
			},
			shopList:[],
			selectData:'',
			pageTotal:0,
			selection: 'checkbox',
			showHead: false,
			
			getListUrl:'/custom-web/api/equityGoods/getOne',//导出结果列表
			otherInfo:{
				excel_type:'EXCEL_TYPE_SHOPV2'
			},
			tabText:'店铺导出列表',
			refreshBtnStatus: false
		}
	},
	/*
	params.selection：选择框状态，默认为多选；如需单选，请传值radio
	params.shop_type:清样商品选择店铺专有
	params.shop_status 店铺状态,"OPEN为生效的店铺"
	*/
	props:['params'],
	methods:{
		radioChange(data){
			this.selectData = data;
		},
		selectionChange(data) {
			this.selectData = data;
		},
		close(){
			if(!this.selectData){
				this.$message.error('请先选择一个店铺');
				return;
			}
			this.params.callback(this.selectData);
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		searchData(obj, resolve){
			this.search.where = obj;
			this.selectData = null;
			this.getShopList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.page.length = pageSize;
			this.selectData = null;
			this.getShopList();
		},
		pageChange(page){
			this.search.page.pageNo = page;
			this.selectData = null;
			this.getShopList();
		},
		getShopList(resolve){
			this.refreshBtnStatus = true;
			console.log(this.params)
			var postData = {
				"custom_equity_id":this.params.custom_equity_id            //主键
				}

			this.ajax.postStream('/custom-web/api/equityGoods/getOne', postData, d=>{
				if(d.body.result&&d.body.content){
					// this.pageTotal = d.body.content.shopList.length;
					this.shopList = d.body.content.shopList||[];
					
				} else {
					this.$message.error(d.body.msg || '')
				}
				this.refreshBtnStatus = false;
				resolve && resolve();
			}, err => {
				resolve && resolve();
				this.$message.error(err);
				this.refreshBtnStatus = false;
			})
		},
		rowDblclick(obj) {
			if(!this.params.isAlert) return;
			let data = obj;
			if(this.selection === 'checkbox') {
				this.selectData = this.selectData.length?this.selectData:[];
				this.selectData.push(data);
				this.selectData = Array.from(new Set(this.selectData));
				//data = [obj];
			}
			//this.selectData = data;

			this.close();
		},
		// 跳转货款管理详情
		toMoneyManagementDetail (row){
			this.$root.eventHandle.$emit('creatTab',{
				name: "货款管理详情",
				params: {
					shop_name: row.shop_name,
					shop_id  : row.shop_id,
					isFromShopList: true,
				},
				component:()=>import('@components/dealer/money_management_detail'),
			})
		},
		/**
		*新增、编辑店铺
		*/
		addNew(obj){
			this.$root.eventHandle.$emit('creatTab', {
				name: obj ? '查看店铺' : "新增店铺",
				params: obj || {},
				component:()=>import('@components/shop/shopdetail')
			});
		},
	},
	mounted() {
  
    this.getShopList()
	}
}
</script>
