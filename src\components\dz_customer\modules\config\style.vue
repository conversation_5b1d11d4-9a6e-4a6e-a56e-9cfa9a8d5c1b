<!--
 * @Author: your name
 * @Date: 2021-03-18 17:43:10
 * @LastEditTime: 2021-03-22 11:28:37
 * @LastEditors: Please set LastEditors
 * @Description: 风格配置
 * @FilePath: \front-dev\src\components\dz_customer\modules\config\clours.vue
-->
<template>
    <auxiliary
        :params="param"
    ></auxiliary>
</template>
<script>
import auxiliary from '../../../auxiliary/auxiliary'
export default {
    props:['params'],
    components: {
        auxiliary
    },
    data() {
        return {
            param: {
                __data: {},
                __close: '',
                 "code": "CUSTOM_GOODS_STYLE", 
                "createTime": null, 
                "creator": null, 
                "id": 3206102836143, 
                "modifier": null, 
                "modifyTime": 1616211074000, 
                "name": "定制商品风格", 
                "parentCode": null, 
                "parentName": null, 
                "platform": "NEW_SALE_PLATFORM", 
                "remark": "风格代码（第3位）", 
                "system": 0
            }
        }
    }
}
</script>