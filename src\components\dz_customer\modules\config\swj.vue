<!-- 三维家管理 -->
<template>
	<dz-list 
        ref='supplierGoods'
        id='swjList' 
        :data='goodsList' 
        :btns='goodsBtns' 
        :colData='goodsCols' 
		:pageTotal="pageTotal"
		:tools="tools"
		simpleSearch
        selection='radio'
        orderNo 
		searchHolder="搜索用户名称"
        @radio-change='goodsRadioChange'
		@search-click="searchClick"
		@page-size-change="pageChange"
      	@current-page-change="currentPageChange"
    >
    <xpt-import slot='btns' :taskUrl='uploadUrl' :otherParams="paramas" class='mgl10'></xpt-import>

    </dz-list>
</template>
<script>
import dzList from '../../components/list/list'
import { bindSwjAccount, cancelSwjAccount, deleteAccount } from '../../common/api' 
export default {
    components: {
        dzList
    },
	data() {
		let self = this;
        
		return {
			paramas:{
				path:'/custom-web/api/customSwj/import',
				isSwj:true,
			},
			uploadUrl:'/custom-web/api/customSwj/import',
			tableParam: {
				page: {
					length: 20,
					pageNo: 1,
				}
			},
			goodsList: [],
			goodsBtns: [
				{
					type: 'primary',
					txt: '刷新',
					disabled: () => {
						return false;
					},
					click() {
						self.refresh()
					}
				}, {
					type: 'success',
					txt: '保存',
					disabled: () => {
						return false;
					},
					click() {
						self.save()
					}
				}, {
					type: 'primary',
					txt: '新增',
					disabled: () => {
						return false;
					},
					click() {
                        self.goodsAdd()
					}
					
				},  {
					type: 'primary',
					txt: '查看导入结果',
					loading: false,
					click: self.showUploadResult
					},  {
					type: 'primary',
					txt: '下载导入模板',
					loading: false,
					click: self.getExceTemplate
					}
			],
			tools: [
				{
					type: 'primary',
					txt: '绑定',
					key: 'bd',
					async click(d) {
						d.bdloading = true
						const result = await self.save(false)
						if(!result) {
							d.bdloading = false
							return
						}
						bindSwjAccount({
							outUserId: d.outUserId,
							userName: d.userName
						}, false, true).then((res) => {
							d.bdloading = false
							res.result && self.refresh()
						}).catch(() => {
							d.bdloading = false
						})
					},
					show(d) {
						// 未绑定的记录才能绑定
						return d.swj_relation_id && d.status === '0'
					},
				},
				{
					type: 'danger',
					txt: '解绑',
					key:'jbd',
					click(d) {
						d.jbdloading = true
						cancelSwjAccount({
							outUserId: d.outUserId,
							userName: d.userName
						}, false, true).then((res) => {
							d.jbdloading = false
							res.result && self.refresh()
						}).catch(() => {
							d.jbdloading = false
						})
					},
					show(d) {
						// 已绑定的记录才能解绑
						return d.swj_relation_id && d.status === '1'
					},
				},
				{
					type: 'danger',
					txt: '删除',
					click(d) {
						//删除商品
						let row = Object.assign({}, d)
						delete row.status_cn
						delete row.disabled
						delete row.bdloading
						delete row.jbdloading
						row.status = Number(row.status)
						d.swj_relation_id ? self.$confirm("是否删除该账号？", "提示", {
							confirmButtonText: "确定",
								cancelButtonText: "取消",
								type: "warning",
							}).then(() => {
								deleteAccount(row).then(res => {
									if(res.result) {
										self.refresh()
									}
								})
							}) :
							self.goodsList.splice(self.goodsList.findIndex(item => item.temId === d.temId), 1)
					},
					show(d) {
						// 保存之后不能删除
						return d.status === '0'
					},
				}
			],
			pageTotal: 0,
			goodsCols: [],
			goodsSelect: ''
		}
	},
    created() {
		let self = this
        this.goodsCols = [
				{
					label: '员工编号',
					prop: 'employee_number',
					width: 160,
					bool: true,
					iconClick(d) {
						d.status === '0' && self.goodsChooseAddress(d)
					},
					isClickEvent:true,
					disabled(d) {
						// 绑定了不可修改
						return d.status === '1'
					}
				},
                {
                    label: '用户名称',
                    prop: 'personName',
                    width: 180
                },
                {
					label: '三维家账号',
					prop: 'userName',
					width: 150,
					bool: true,
                    isInput: true,
					conditionDisabled: 'disabled',
					isThis: true
				},{
					label: '状态',
					prop: 'status_cn',
					width: 150
				}, {
					label: '建档人',
					prop: 'creatorName',
                    width: 130
				}, {
					label: '创建时间', 
                    prop: 'create_time',
					format: 'dataFormat1',
					width: 130
				}, {
					label: '修改人',
					prop: 'modifierName',
                    width: 130
				}, {
					label: '修改时间',
                    prop: 'modify_time',
					format: 'dataFormat1',
					width: 130
				}
			
            ]
    },
	methods: {
		//导入结果
        showUploadResult() {
            this.$root.eventHandle.$emit("alert", {
                style: "width:900px;height:600px",
                title: "导入结果",
                params: {
                    url: "/custom-web/api/customSwj/import/list",
                    data: {},
                    showDownload: true,
                },
                component: () => import("@components/common/eximport"),
            });
        },
		getExceTemplate(){
      var self=this;
      self.ajax.postStream("/reports-web/api/template/list",{"page_name":"template_download_list","where":[{"field":"227026c354d125970086c5922c02a5c3","table":"d331c120edc028bbbc83230f04b409fa","value":"三维家账号绑定","operator":"=","condition":"AND","listWhere":[]}],"page_size":50,"page_no":1},function(response){
          if(response.body.result && response.body.content.count != 0){
            self.$message({message: '操作成功',type: 'success'})
            window.open(response.body.content.list[0].url);
          }else{
            self.$message.error('未找到对应模板，请到模板下载列表维护')
          }
        },e=>{
          self.$message.error(e);
        })
    },
		// 监听每页显示数更改事件
		pageChange(pageSize) {
			this.tableParam.page.length = pageSize
			this.getList();
		},
		// 监听页数更改事件
		currentPageChange(page) {
			this.tableParam.page.pageNo = page
			this.getList();
		},
		// 选择
		goodsChooseAddress(row) {
            new Promise((resolve) => {
				setTimeout(resolve, 10)
			}).then(() => {
				let params = {},
                self = this;
                // 选择商品
                params.callback = d => {
                    if(d) {
						row.employee_number = d.employeeNumber
                        row.outUserId = d.personId
                        row.outUserName = d.personRealName
						row.personName = d.personRealName
                    }
                }
				this.$root.eventHandle.$emit('alert',{
					component:()=>import('@components/role/userList'),
					close:function(){
					},
					style:'width:900px;height:600px',
					title:'用户列表',
					params
				})
			})
        },
        refresh () {
            this.getList()
        },
		// 行选中
		goodsRadioChange(row) {
			this.goodsSelect = row;
		},
		searchClick(keyword) {
		/**
		 * @description: 输入框搜索
		 * @param {*}
		 * @return {*}
		 */	
		  this.tableParam.personName = keyword
		  this.getList()
		},
		// 获取列表
		getList(resolve) {
			let self = this;
			let url = '/custom-web/api/customSwj/listAccount'
			this.ajax.postStream(url, this.tableParam, d => {
				if(d.body.result && d.body.content) {
					this.goodsList = Array.isArray(d.body.content.list) ? d.body.content.list.map(item => {
						item.status = String(item.status)
						item.status_cn = item.status === '0' ? '未激活' : '已激活'
						item.disabled = item.status === '1'
						item.jbdloading = false
						item.bdloading = false
						return item
					}) : d.body.content.list
					this.goodsCopy =  JSON.parse(JSON.stringify(this.goodsList))
					if(self.goodsList.length > 0){
						self.goodsSelect = this.goodsList[0];
						self.$refs.supplierGoods && self.$refs.supplierGoods.setRadioSelect(self.goodsList[0]);
					}
					this.pageTotal = d.body.content.count || 0;
				}
				resolve && resolve();
				!d.body.result && this.$message.error(d.body.msg);
			}, (e) => {
				this.$message.error(e);
				resolve && resolve();
			})
		},
		
		// 新增，复制新增
		goodsAdd() {
			let copyData = {
				userName: '',
				status: '0',
				status_cn: '未激活',
				outUserId: '',
				outUserName: "",
				personRealName: '',
				employee_number: ''
			}
			copyData.temId = new Date().getTime()
            this.goodsList.unshift(copyData)
            this.goodsSelect = copyData;
            // 滚动到最底部
            let scrollObj = document.querySelector('#swjList').querySelector('.el-table__body-wrapper')
            this.$nextTick(function(){
                scrollObj.scrollTop = 0
            })
		},
		isChange(item) {
		/**
			 * @description: 比较数据是否发生变化
			 * @param {*}
			 * @return {*}
			 */	
			let index = this.goodsCopy.findIndex(goods => goods.swj_relation_id === item.swj_relation_id)
			if(index === -1) return true
			let copyItem = this.goodsCopy[index]
			for(let key in item) {
				if(item[key] !== copyItem[key]) return true
			}
			return false
		},
        save (refresh = true) {
			let self = this
			return new Promise((resolve, reject) => { 
				// 校验用户和三维家不为空
				if(this.goodsList.findIndex(item => {
					return !item.outUserId || !item.userName.trim()
				}) !== -1) {
					this.$message.error('新平台用户或三维家账号不能为空')
					reject(false)
					return
				}
				
				let data = []
				this.goodsList.forEach(item => {
					if(item.swj_relation_id) {
						// 比较是否发生变化
						this.isChange(item) && data.push(item)
					} else {
						data.push(item)
					}
				})
				this.ajax.postStream('/custom-web/api/customSwj/saveAccount',data.map(item => {
					return {
						swj_relation_id: item.swj_relation_id,
						outUserId: item.outUserId,
						userName: item.userName,
						status: Number(item.status)
					}
				}), (d) => {
					if(d.body.result){
						resolve(true)
						this.$message.success(d.body.msg)
					}else{
						reject(false)
						this.$message.error(d.body.msg)
					}
					refresh && setTimeout( () => {
						self.getList() 
					}, 1000)
				}, err => {
					reject(false)
					this.$message.error(err);
				});
			}) 
			
        }
	},
	destroyed () {
		this.timer = null
	},
	mounted () {
        this.getList()
    }
}
</script>
