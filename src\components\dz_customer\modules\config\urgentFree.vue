<!-- 加急费管理 -->
<template>
	<dz-list 
        ref='supplierGoods'
        id='freeList' 
        :data='goodsList' 
        :btns='goodsBtns' 
        :colData='goodsCols' 
		:showTools="false"
		:pageTotal="pageTotal"
		simpleSearch
        selection='radio'
        orderNo 
		searchHolder="搜索供应商名称"
        @radio-change='goodsRadioChange'
		@search-click="searchClick"
		@page-size-change="pageChange"
      	@current-page-change="currentPageChange"
    >
    </dz-list>
</template>
<script>
import dzList from '../../components/list/list'
export default {
    components: {
        dzList
    },
	data() {
		let self = this;
        
		return {
			tableParam: {
				page: {
					length: 20,
					pageNo: 1,
				}
			},
			goodsList: [],
			goodsBtns: [
				{
					type: 'primary',
					txt: '刷新',
					disabled: () => {
						return false;
					},
					click() {
						self.refresh()
					}
				}, {
					type: 'success',
					txt: '保存',
					disabled: () => {
						return false;
					},
					click() {
						self.save()
					}
				}, {
					type: 'primary',
					txt: '新增',
					disabled: () => {
						return false;
					},
					click() {
                        self.goodsAdd()
					}
				},
				{
					type: 'danger',
					txt: '删除',
					disabled: () => {
						return false;
					},
					click() {
                        self.delFree()
					}
				}
			],
			pageTotal: 0,
			goodsCols: [],
			goodsSelect: ''
		}
	},
    created() {
		let self = this
        this.goodsCols = [
				{
					label: '供应商编码',
					prop: 'factory_code',
					width: 160,
					bool: true,
					iconClick: self.goodsChooseAddress,
					isClickEvent:true,
					disabled(row) {
						return false
					}
				},
                {
                    label: '供应商名称',
                    prop: 'supplierName',
                    width: 180
                },
                {
					label: '加急费率（%）',
					prop: 'urgent_fee',
					width: 150,
					type: 'number',
					bool: true,
                    isInput: true
				},{
					label: '状态',
					prop: 'status',
					width: 150,
					bool: true,
                    isSelect: true,
					obj: {
						0: '失效',
						1: '生效'
					}
				}, {
					label: '创建人',
					prop: 'creatorName',
                    width: 130
				}, {
					label: '创建时间', 
                    prop: 'create_time',
					format: 'dataFormat1',
					width: 130
				}, {
					label: '修改人',
					prop: 'modifierName',
                    width: 130
				}, {
					label: '修改时间',
                    prop: 'modify_time',
					format: 'dataFormat1',
					width: 130
				}
			
            ]
    },
	methods: {
		// 监听每页显示数更改事件
		pageChange(pageSize) {
			this.tableParam.page.length = pageSize
			this.getUrgentFeeList();
		},
		// 监听页数更改事件
		currentPageChange(page) {
			this.tableParam.page.pageNo = page
			this.getUrgentFeeList();
		},
		// 选择供应商
		goodsChooseAddress(row) {
            new Promise((resolve) => {
				setTimeout(resolve, 10)
			}).then(() => {
				let params = {},
                self = this;
                // 选择商品
                params.callback = d => {
                    if(d) {
						if(this.goodsList.findIndex(item => {
							return item.supplier_id === d.supplier_id
						}) !== -1) {
							this.$message.error('该供应商已设置过工厂加急费了！')
							return;
						}
						row.factory_code = d.number
                        row.supplier_id = d.supplier_id
                        row.supplierName = d.name
                    }
                }
                self.$root.eventHandle.$emit('alert', {
                    params: params,
                    component: () => import('../../alert/selectSupplier.vue'),
                    style: 'width:800px;height:500px',
                    title: '供应商列表'
                });
			})
        },
        refresh () {
            this.getUrgentFeeList()
        },
		// 行选中
		goodsRadioChange(row) {
			this.goodsSelect = row;
		},
		searchClick(keyword) {
		/**
		 * @description: 输入框搜索
		 * @param {*}
		 * @return {*}
		 */	
		  this.tableParam.supplierName = keyword
		  this.getUrgentFeeList()
		},
		// 获取工厂加急费设置
		getUrgentFeeList(resolve) {
			let self = this;
			let url = '/custom-web/api/customSupplierUrgentFee/list'
			this.ajax.postStream(url, this.tableParam, d => {
				if(d.body.result && d.body.content) {
					this.goodsList = Array.isArray(d.body.content.list) ? d.body.content.list.map(item => {
						item.status = String(item.status)
						return item
					}) : d.body.content.list
					this.goodsCopy =  JSON.parse(JSON.stringify(this.goodsList))
					if(self.goodsList.length > 0){
						self.goodsSelect = this.goodsList[0];
						self.$refs.supplierGoods && self.$refs.supplierGoods.setRadioSelect(self.goodsList[0]);
					}
					this.pageTotal = d.body.content.count || 0;
				}
				resolve && resolve();
				!d.body.result && this.$message.error(d.body.msg);
			}, (e) => {
				this.$message.error(e);
				resolve && resolve();
			})
		},
		delFree() {
			/**
			 * @description: 删除交期
			 * @param {*}
			 * @return {*}
			 */	
			if(!this.goodsSelect.supplier_urgent_fee_id)  {
				this.goodsList.splice(this.goodsList.findIndex(item => item.temId === this.goodsSelect.temId), 1)
				return
			}
			this.$confirm("确认删除？", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then(() => {
				this.ajax.postStream(
					"/custom-web/api/customSupplierUrgentFee/delete",
					[
						{
							supplier_urgent_fee_id: this.goodsSelect.supplier_urgent_fee_id
						}
					],
					(res) => {
						res = res.body;
						this.$message({
							message: res.msg,
							type: res.result ? "success" : "error",
						});
						res.result && this.getUrgentFeeList()
					}
				);
				})
				.catch(() => {
					this.$message({
						type: "info",
						message: "已取消删除",
					});
				});
		},
		// 新增，复制新增
		goodsAdd() {
			let copyData = {
				urgent_fee: 0,
				status: '1',
				supplier_id: '',
				supplierName: ""
			}
			copyData.temId = new Date().getTime()
            this.goodsList.unshift(copyData)
            this.goodsSelect = copyData;
            // 滚动到最底部
            let scrollObj = document.querySelector('#freeList').querySelector('.el-table__body-wrapper')
            this.$nextTick(function(){
                // scrollObj.scrollTop = scrollObj.scrollHeight
                scrollObj.scrollTop = 0
            })
		},
		isChange(item) {
		/**
			 * @description: 比较数据是否发生变化
			 * @param {*}
			 * @return {*}
			 */	
			let index = this.goodsCopy.findIndex(goods => goods.supplier_urgent_fee_id === item.supplier_urgent_fee_id)
			if(index === -1) return true
			let copyItem = this.goodsCopy[index]
			for(let key in item) {
				if(item[key] !== copyItem[key]) return true
			}
			return false
		},
        save () {
			let self = this
			// 校验供应商和加急费不为空
			if(this.goodsList.findIndex(item => {
				return !item.supplier_id 
			}) !== -1) {
				this.$message.error('供应商不能为空')
				return
			}
			// 校验报警周期为正数
			if(this.goodsList.findIndex(item => item.urgent_fee < 0) !== -1) {
				this.$message.error('加急费不能为负数')
				return
			}
			let data = []
			this.goodsList.forEach(item => {
				if(item.supplier_urgent_fee_id) {
					// 比较是否发生变化
					this.isChange(item) && data.push(item)
				} else {
					data.push(item)
				}
			})
            this.ajax.postStream('/custom-web/api/customSupplierUrgentFee/save',data.map(item => {
				return {
					supplier_urgent_fee_id: item.supplier_urgent_fee_id,
					supplier_id: item.supplier_id,
					urgent_fee: item.urgent_fee,
					status: Number(item.status)
				}
			}), (d) => {
                if(d.body.result){
                    this.$message.success(d.body.msg)
					setTimeout( () => {
						self.getUrgentFeeList() 
					}, 1000)
                }else{
                    this.$message.error(d.body.msg)
                }
                
            }, err => {
                this.$message.error(err);
            });
        }
	},
	destroyed () {
		this.timer = null
	},
	mounted () {
        this.getUrgentFeeList()
    }
}
</script>
