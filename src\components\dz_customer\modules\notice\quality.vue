<!--
 * @Author: 谢永林
 * @Date: 2021-03-17 14:15:43
 * @LastEditTime: 2021-04-02 10:36:47
 * @LastEditors: Please set LastEditors
 * @Description: 质检报告
 * @FilePath: \front-dev\src\components\dz_customer\modules\notice\notice.vue
-->
<template>
<!-- 支付记录列表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
        <my-table 
        ref="table"
        tableUrl='/user-web/api/notice/getManageNoticeList'
        :tableParam="tableParam"
        :colData="colData"
        :tools="tools"
        :orderNo="true"
        showTools
        ></my-table>
    </div>
  </div>
</template>
<script>
import myTable from '../../components/table/table'
import { getFileList, downloadZip } from "../../common/api";
export default {
    components: {
        myTable
    },
    data() {
        let self = this
        return {
            colData: [],
            info: {},
            tableParam:{
                notice_object: 'CUSTOM_USER',
                notice_type: 'CHECK_REPORT'
            },
            tools: Object.freeze([
                {
                    type: 'primary',
                    txt: '查看',
                    click(d) {
                        getFileList({
                            order_no: d.notice_id,
                        }).then((list) => {
                            let files = list || [];
                            if(!files.length) {
                                self.$message.warning('没有可查看附件！')
                            } else {
                                window.open(files[0].path)
                            }
                            
                        });
                    }
                },
                {
                    type: 'primary',
                    txt: '下载',
                    click(d) {
                        getFileList({
                            order_no: d.notice_id,
                        }).then((list) => {
                            let files = list || [];
                            if(!files.length) {
                                self.$message.warning('没有可下载附件！')
                            } else {
                                downloadZip(files)
                            }
                        });
                    }
                },
            ])
        }
    },
    props:{
        params: {
            type: Object
        }
    },
    created() {
        this.getColData()
    },
    methods: {
        getColData(){
            this.colData = [
                {
					label: '质检报告标题',
					prop: 'notice_title',
                },
                {
					label: '影响开始时间',
					prop: 'start_time',
                    filter: 'date',
					width: 180
                },
                {
					label: '影响结束时间',
					prop: 'end_time',
                    filter: 'date',
					width: 180
                },
                 {
					label: '创建时间',
                    prop: 'create_time',
                    filter: 'date',
					width: 180
                },
                {
					label: '状态',
					prop: 'status_cn',
                    filter: 'select',
                    options: [
                        { label: '已发布', value: 'RELEASE' },
                        { label: '创建', value: 'CREATE' },
                        { label: '已作废', value: 'INVALID' }
                    ],
                    width: 180
                }
            ]
        }
    }
}
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
