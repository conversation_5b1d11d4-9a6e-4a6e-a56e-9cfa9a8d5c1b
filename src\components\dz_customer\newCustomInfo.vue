<!--添加客户/客户详情-->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="24">
        <el-button
          type="primary"
          size="mini"
          @click="preSave('submit')"
          :loading="isLoading"
          >保存</el-button
        >
        <!-- <el-button type='danger' class='xpt-close' size='mini' @click="closeTab">关闭</el-button> -->
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-tabs v-model="firstTab">
        <el-tab-pane label="客户" name="customer">
          <el-form
            label-position="right"
            label-width="100px"
            :model="submit"
            :rules="rules"
            ref="submit"
          >
            <el-row class="mgt10">
              <el-col :span="8">
                <el-form-item label="客户号" prop="client_no">
                  <el-input
                    size="mini"
                    v-model="submit.client_no"
                    :maxlength="code_max"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item label="客户电话" prop="client_mobile">
                  <el-input
                    size="mini"
                    v-model="submit.client_mobile"
                    :maxlength="11"
                    @blur="checkMobile(submit.client_mobile)"
                  ></el-input>
                  <el-tooltip
                    v-if="mobileStatus"
                    class="item"
                    effect="dark"
                    content="手机号重复"
                    placement="right-start"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                  <el-tooltip
                    v-if="rules.client_mobile[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.client_mobile[0].message"
                    placement="right-start"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="客户名称" prop="client_name">
                  <el-input
                    size="mini"
                    v-model="submit.client_name"
                    :maxlength="name_max"
                  ></el-input>
                  <el-tooltip
                    v-if="rules.client_name[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.client_name[0].message"
                    placement="right-start"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                  <el-select v-model='submit.client_sex' size='mini' :placeholder='placeholder' >
                        <el-option
                            v-for="item in sexopt"
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value" 
                            >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="其他电话" prop="client_tel">
                  <el-input
                    size="mini"
                    v-model="submit.client_tel"
                    :maxlength="name_max"
                  ></el-input>
                  <el-tooltip
                    v-if="rules.client_tel[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.client_tel[0].message"
                    placement="right-start"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </el-form-item>
                
              </el-col>
            </el-row>

            <el-row class="mgt10" v-if="!this.params.client_no">
              <el-col>
                <el-form-item label=" " prop="if_create_trade">
                  <el-checkbox
                    v-model="submit.if_create_trade"
                    true-label="Y"
                    false-label="N"
                  >是否创建订单</el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row class="mgt10" v-if="submit.if_create_trade === 'Y'">
              <el-col :span="8">
                <el-form-item label="客户类型" prop="client_type">
                  <el-select
                    size="mini"
                    v-model="submit.client_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item, index) in cusTypeopt"
                      :label="item.label"
                      :value="item.value"
                      :key="index"
                    >
                    </el-option>
                  </el-select>
                  <el-tooltip
                    v-if="rules.client_type[0].isShow"
                    class="item"
                    effect="dark"
                    :content="rules.client_type[0].message"
                    placement="right-start"
                    popper-class="xpt-form__error"
                  >
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="希望量尺时间" prop="reserve_measure_date">
                  <el-date-picker
                    v-model="submit.reserve_measure_date"
                    type="datetime"
                    size='mini'
                    placeholder="选择日期时间"
                    format="yyyy-MM-dd HH:mm:ss"
                    :clearable="false"
                    >
                  </el-date-picker>
                  <el-tooltip v-if='rules.reserve_measure_date[0].isShow' class="item" effect="dark" :content="rules.reserve_measure_date[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="mgt10" v-if="submit.if_create_trade === 'Y'">
              <el-col :span="20">
                <el-form-item label="量尺空间" prop="measure_room">
                  <el-checkbox-group v-model="submit.measure_room">
                    <el-checkbox label="1" >客餐厅</el-checkbox>
                    <el-checkbox label="2">主卧房</el-checkbox>
                    <el-checkbox label="3">客卧房</el-checkbox>
                    <el-checkbox label="4">儿童房</el-checkbox>
                    <el-checkbox label="5">书房</el-checkbox>
                    <el-checkbox label="6">厨房</el-checkbox>
                    <el-checkbox label="7">门厅</el-checkbox>
                    <el-checkbox label="8">卫生间</el-checkbox>
                    <el-checkbox label="9">衣帽间</el-checkbox>
                    <el-checkbox label="10">阳台</el-checkbox>
                    <el-checkbox label="11">杂物间</el-checkbox>
                    <el-checkbox label="12">其他</el-checkbox>
                  </el-checkbox-group>
                  <el-tooltip v-if='rules.measure_room[0].isShow' class="item" effect="dark" :content="rules.measure_room[0].message" placement="right-start" popper-class='xpt-form__error'>
                    <i class='el-icon-warning'></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="mgt10">
              <el-col>
                <el-form-item label="备注" prop="remark">
                  <el-input type='textarea' v-model="submit.remark" size='mini' style="width:60%" :maxlength='200' resize="none"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class="xpt-flex__bottom">
      <el-tabs v-model="secondTab" @tab-click="changeTab">
        <el-tab-pane label="地址信息" name="address" class="xpt-flex">
          <dz-list
            :data="receiverAddressList"
            :btns="customerBtns"
            :colData="customerCols"
            selection="radio"
            isNeedClickEvent
            @radio-change="customerRadioChange"
            :showTools="false"
            ref="list"
          ></dz-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-dialog title="地图选点" :visible.sync="dialogMapVisible" center width="80%" top="2vh" size='large'>
      <iframe src="http://api.map.baidu.com/lbsapi/getpoint/index.html" width="100%" height="576px" ></iframe>
    </el-dialog>
  </div>
</template>
<script>
    import dzList from './components/list/list'
    import validate from "./common/validate"
    import fn from '@/common/Fn.js'
    import { sexopt, cusTypeopt, deliver_method } from './common/clientDictionary'
    import closeComponent from './common/mixins/closeComponent'
export default {
    props: ["params"],
    mixins:[closeComponent],
    components: {
        dzList
    },
    data() {
        var _this = this
        var self = this
        return {
            isLoading: false, //判断是否处于提交状态
            selectLine: null,
            mobileStatus: false,  //判断手机号是否重复
            forbid_options: "",
            radioSelect: "",
            dialogMapVisible: false,
            receptionRecordList: [],
            selectInvoice: [],
            idExistence: _this.params.client_no ? true : false,
            idExistence_eidt: this.params.client_no ? true : false,
            firstTab: "customer",
            secondTab: "address",
            submit: {
                client_no: "",
                client_mobile: "",
                client_name: "",
                client_sex: "1",
                client_tel: "",
                if_create_trade: "N",
                remark: "",
                reserve_measure_date: "",
                measure_room: [],
                client_type: "",
            },
            approve_options: "",
            add_address_opitons: "",
            other_info: {},
            cusTypeopt: cusTypeopt,
            sexopt: sexopt,
            ifShow: true,
            // 地址列表
            receiverAddressList: [],
            selection: "radio",
            count: 0,
            pageNow: 1,
            placeholder:"请选择",
            page: {
                page_no: 1,
                page_size: 0
            },
            // 选中的地址对象
            curAddressObj: null,
            rules: {
              client_mobile: validate.mobile({
                self:self,
                msg:'请输入正确的手机号',
                trigger: 'blur'
              }),
              client_type : validate.isNotBlank({
                self:self,
                msg:'请选择客户类型',
                trigger: 'change'
              }),
              client_name: validate.isNotBlank({
                self:self,
                msg:'请输入客户名称',
                trigger: 'change'
              }),
              reserve_measure_date : validate.isNotBlank({
                self:self,
                msg:'请选择希望量尺时间',
                trigger: 'change'
              }),
              measure_room : validate.isNotBlank({
                self:self,
                msg:'请选择量尺空间',
                trigger: 'change'
              }),
              client_tel : validate.otherTel({
                self:self,
                msg:'请填写有效的电话号',
                trigger: 'blur'
              }),
            },
            initialData: {},
            visible: "",
            code_max: 25,
            name_max: 25,
            // 数据对比
            // comparedProp: {
            //     receiver_name: null,
            //     receiver_state_name: null,
            //     receiver_city_name: null,
            //     receiver_district_name: null,
            //     receiver_street_name: null,
            //     receiver_address: null
            // },
            customerBtns: [
                {
                    type: "primary",
                    txt: "新增",
                    click() {
                        _this.add_address("add")
                    }
                },
                {
                    type: "danger",
                    txt: "删除",
                    click: _this.delAddress,
                    disabled: true
                },
                {
                    type: "success",
                    txt: "设为默认地址",
                    click: _this.setDefaultAddr,
                    disabled: true
                }
            ],
            customerCols: [
                {
                    label: "地址编码",
                    prop: "address_id"
                },
                {
                    label: "收货人",
                    prop: "receiver_name"
                },
                {
                    label: "送货方式",
                    prop: "deliver_method_cn",
                    width: "120"

                },
                {
                    label: "运费类型",
                    prop: "post_fee_type_cn"
  
                },
                {
                    label: "省市区",
                    prop: "prin_city_district",
                    width: "auto"
                },
                {
                    label: "详细地址",
                    prop: "receiver_address"
                },
                {
                    label: "收货人手机",
                    prop: "receiver_mobile"
                },
                {
                    label: "收货人固话",
                    prop: "receiver_phone"
                },
                {
                    label: "是否默认地址",
                    prop: "is_selected_cn"
                }
            ],
            isInit: false,
        }
    },
    watch: {
      curAddressObj(v) {
        let addressBtn = this.customerBtns.filter(item => item.txt === '设为默认地址')[0]
        let deleteBtn = this.customerBtns.filter(item => item.txt === '删除')[0]
        if(v) {
          // 有选中值
          addressBtn.disabled = v.is_selected === 'Y' || !v.address_id
          deleteBtn.disabled = v.is_selected === 'Y' && v.address_id !== undefined

        } else {
          // 没有选中值
          addressBtn.disabled = true
          deleteBtn.disabled = true
        }
      }
    },
    methods: {
        //检测手机号是否重复
        checkMobile(value){
          this.ajax.postStream('/custom-web/api/customClient/verifyClientMobile', value, (res) => {
            if(res.body.content === "false  "){
              this.mobileStatus = true
            } else {
              this.mobileStatus = false
            }
          })
        },
        //设置默认地址
        setDefaultAddr(){
          this.ajax.postStream("/custom-web/api/customClient/selectedDefaultAddress",{
            address_id: this.curAddressObj.address_id,
            client_no: this.submit.client_no
          }, (res) => {
            res = res.body
            this.$message({
              type: res.result?"success":"error",
              message: res.msg
            })
            if(res.result){
              let i = this.receiverAddressList.findIndex(item => item.address_id === this.curAddressObj.address_id)
              if(i!==-1) {
                this.receiverAddressList = this.receiverAddressList.map(item => {
                  item.is_selected = 'N'
                  item.is_selected_cn = ''
                  return item
                })
                this.receiverAddressList[i].is_selected = 'Y'
                this.receiverAddressList[i].is_selected_cn = '是'
                this.receiverAddressList = this.receiverAddressList.filter(item => true)
              }
              // this.getCustomerDetail()
            }
          })
        },

        /*
		控制送货方式、运费类型是否可以变更
		新增的可以更改，
		原始地址的不能编辑
		保存之后不可以编辑
		*/
        customerSelectDisabled(row) {
            if (row.fid || row.is_original) {
                return true
            } else {
                return false
            }
        },
        customerRadioChange(obj) {
            this.curAddressObj = obj
            // for(var i=0; i<this.receiverAddressList.length; i++){
            //     this.receiverAddressList[i].is_selected = "N"
            // }
            // obj.is_selected = 'Y'
            
        },
        /*
		判断是否存在相同的地址信息（根据收货人，省，市，区，街道，详情地址，配送方式等七个字段判断，一个不同则可视为不同的地址，即可添加）
		*/
        compareForDifference(comparedObj, index) {
            let self = this
            if (self.receiverAddressList.length <= 1) return
            for (var i = 0; i < self.receiverAddressList.length; i++) {
                if (
                    index != i &&
                    comparedObj.deliver_method ==
                        self.receiverAddressList[i].deliver_method
                ) {
                    var count = 0
                    for (var key in self.comparedProp) {
                        if (comparedObj[key] == self.receiverAddressList[i][key])
                            count += 1
                    }
                    if (count == 6) {
                        self.$message.error("已存在相同的地址信息")
                        self.receiverAddressList[index].deliver_method = ""
                        // self.receiverAddressList[index].if_exist_deliver_method = false
                        return
                    }
                }
            }
        },
        // 新增地址
        add_address(option) {
            var _this = this
            if (option == "add") {
                _this.alert()
            } else if (option == "copy_add") {
                if (_this.curAddressObj == null) {
                    _this.$message.error("没有选择任何数据，请先选择数据！")
                } else {
                    _this.alert(_this.curAddressObj)
                }
            }
        },
        // 地址弹窗
        alert(obj) {
            var self = this
            self.params.ifCallback = true
            self.params.callback = d => {
                
                Object.assign(d, {
                    fid: "",
                    number: "",
                    tempId: new Date().getTime()
                });
                self.receiverAddressList.push(d);
                if(self.receiverAddressList.length === 1) {
                    this.curAddressObj = d
                    self.$refs.list.setRadioSelect(d)
                    this.receiverAddressList[0].is_selected = "Y"
                    this.receiverAddressList[0].is_selected_cn = "是"
                }
            }
            self.$root.eventHandle.$emit("alert", {
                params: self.params,
                component: () => import("./alert/addAddress"),
                close: function() {},
                style: "width:1100px;height:350px",
                title: "新增地址"
            })
        },
        // tab切换
        changeTab(tab, event) {
            this.page.page_no = 1
            if (!this.params.client_no) return
            if (tab.name == "payment") {
                this.getPaymentInfo()
                this.ifShow = true
            } else if (tab.name == "address") {
                this.getAddressInfo()
                this.ifShow = true
            } else if (tab.name == "relationship") {
                this.getRelationInfo()
                this.ifShow = true
            } /*else if(tab.name == "invoice"){
              this.getInvoiceDetail()
              this.ifShow = true
            }*/ else if (
                tab.name == "other_info"
            ) {
                this.getCustomerDetail()
                this.ifShow = false
            } else if (tab.name == "receptionRecordList") {
                this.getrReceptionRecordList()
                this.ifShow = true
            }
        },
        sizeChange(size) {
            // 第页数改变
            this.page.page_size = size
        },
        pageChange(page_no) {
            // 页数改变
            this.pageNow = page_no
            this.page.page_no = page_no
        },
        // 关闭标签页
        // closeTab() {
        //     var _this = this,
        //         isUpdate
        //     isUpdate = this.compareData(_this.submit, _this.initialData)
        //     if (isUpdate) {
        //         _this.$root.eventHandle.$emit("openDialog", {
        //             ok() {
        //                 _this.save(() => {
        //                     _this.$root.eventHandle.$emit(
        //                         "removeTab",
        //                         _this.params.tabName
        //                     )
        //                 })
        //             },
        //             no() {
        //                 _this.$root.eventHandle.$emit(
        //                     "removeTab",
        //                     _this.params.tabName
        //                 )
        //             }
        //         })
        //     } else {
        //         _this.$root.eventHandle.$emit(
        //             "removeTab",
        //             _this.params.tabName
        //         )
        //     }
        // },
        preSave(formName) {
            var _this = this
            _this.$refs[formName].validate(valid => {
                if (!valid) return
                if(_this.mobileStatus) return
                _this.save()
            })
        },
        save(callback) {
            // console.log(this.invoiceList)
            var _this = this
            var url, func
            
            var data = _this.submit
            if(!_this.params.client_no) {
              data.measure_room = data.measure_room.toString()
              delete(data["client_no"]) 
            }
            // let receiverAddressList = JSON.parse(
            //         JSON.stringify(_this.receiverAddressList || [])
            //     ),
            //     i = receiverAddressList.length
            // while (i--) {
                // delete receiverAddressList[i].disabled
                let receiverAddressList
                if(_this.params.client_no) {
                  receiverAddressList = _this.receiverAddressList.map(item => {
                  return {
                    address_id: item.address_id,
                    receiver_name: item.receiver_name,
                    receiver_phone: item.receiver_phone,
                    receiver_mobile: item.receiver_mobile,
                    receiver_state_code: item.receiver_state_code,
                    receiver_city_code: item.receiver_city_code,
                    receiver_district_code: item.receiver_district_code,
                    receiver_street: item.receiver_street,
                    receiver_address: item.receiver_address,
                    loft_name: item.loft_name,
                    loft_coordinate: item.loft_coordinate,
                    deliver_method: item.deliver_method,
                    post_fee_type: item.post_fee_type,
                  }
                })
                } else {
                  receiverAddressList = _this.receiverAddressList.map(item => {
                  return {
                    receiver_name: item.receiver_name,
                    receiver_phone: item.receiver_phone,
                    receiver_mobile: item.receiver_mobile,
                    receiver_state_code: item.receiver_state_code,
                    receiver_city_code: item.receiver_city_code,
                    receiver_district_code: item.receiver_district_code,
                    receiver_street: item.receiver_street,
                    receiver_address: item.receiver_address,
                    loft_name: item.loft_name,
                    loft_coordinate: item.loft_coordinate,
                    deliver_method: item.deliver_method,
                    post_fee_type: item.post_fee_type,
                    is_selected: item.is_selected
                  }
                })
                }
            // }
            data.receiverAddressList = receiverAddressList
            data.reserve_measure_date = fn.dateFormat(data.reserve_measure_date,'yyyy-MM-dd hh:mm:ss')
            // if (_this.params.client_no) {
            //     // url = "/order-web/api/customer/update"
            //     params.client_no = _this.params.client_no
            //     func = function() {
            //         _this.getCustomerDetail()
            //         _this.getAddressInfo()
            //     }
            // } else {
            //     // url = "/order-web/api/customer/add"
            //     func = function(id) {
            //         _this.params.client_no = id
            //         _this.getCustomerDetail()
            //         _this.getAddressInfo()
            //         _this.$root.eventHandle.$emit("updateTab", {
            //             name: _this.params.tabName,
            //             title: "客户详情"
            //         })
            //     }
            // }
            this.isLoading = true
            url = _this.params.client_no ? "/custom-web/api/customClient/updateClientInfo" : "/custom-web/api/customClient/saveClientInfo"

            this.ajax.postStream(
                url,
                data,
                (response) => {
                    if(!_this.params.client_no) {
                      data.measure_room = data.measure_room.split(',')
                    }
                    if (response.body.result) {
                        _this.$message({
                            message: "操作成功",
                            type: "success"
                        })
                        callback && callback()
                        let req = response.body.content
                        _this.ajax.postStream('/custom-web/api/customClient/syncCustomerAndReceiveToV2',req,(res) =>{
                            res = res.body
                            _this.$message({
                                message: res.msg,
                                type:res.result?'success':'error'
                            })               
                            this.$root.eventHandle.$emit('refreshCustomList')
                            _this.$root.eventHandle.$emit('removeTab',_this.params.tabName)
                        })
                    } else {
                        _this.isLoading = false
                        _this.$message.error(response.body.msg)
                    }
                },(response) => {
                    _this.isLoading = false
                    if(!_this.params.client_no) {
                      data.measure_room = data.measure_room.split(',')
                    }
                }
            )
        },
        // 客户信息和其他信息
        getCustomerDetail() {
            var _this = this
            this.ajax.postStream(
                "/custom-web/api/customClient/getClientInfoByClientNumber",
                _this.params.client_no,
                (response) =>{
                    var obj = response.body
                    if (obj.result) {
                        // 客户信息
                        _this.submit = {
                            client_no: obj.content.client_no,
                            client_name: obj.content.client_name,
                            client_mobile: obj.content.client_mobile,
                            client_tel: obj.content.client_tel,
                            client_sex: obj.content.client_sex,
                            remark: obj.content.remark,
                            custom_client_id: obj.content.custom_client_id
                        }
                        _this.receiverAddressList = obj.content.receiverAddressList || []
                        for(let i=0; i<_this.receiverAddressList.length; i++) {
                          if(_this.receiverAddressList[i].is_selected === "Y") {
                            _this.receiverAddressList[i].is_selected_cn = "是"
                          }else if(_this.receiverAddressList[i].is_selected === "N") {
                            _this.receiverAddressList[i].is_selected_cn = "否"
                          }
                        }
                        let i = _this.receiverAddressList.findIndex(item => item.is_selected === 'Y')
                        if(i !== -1) {
                          _this.$refs.list.setRadioSelect(_this.receiverAddressList[i])
                        }
                    }
                }
            )
        },
        delAddress() {
            if (!this.curAddressObj) {
                this.$message.error("请先选择需要删除的地址")
                return
            }
            if (this.curAddressObj.is_selected === "Y"){
                this.$message.error("不能删除默认地址")
                return
            }
            let i = this.receiverAddressList.findIndex(item => item == this.curAddressObj)
            i !== -1 && this.receiverAddressList.splice(i, 1)
            this.curAddressObj = null
            this.$refs.list.setRadioSelect(null)
        },
        // 校验送货方式是否匹配,原始地址不匹配。
        checkDeliverMethod(obj) {
            if (obj.is_original) return
            this.ajax.postStream(
                "/order-web/api/customer/receiverInfo/validateReceiveInfoDeliverMethod",
                {
                    deliver_method: obj.deliver_method,
                    receiver_state: obj.receiver_state,
                    receiver_city: obj.receiver_city,
			    	        receiver_street: obj.receiver_street,
                    receiver_district: obj.receiver_district
                },
                res => {
                    if (res.body.result) {
                        this.$message.error(res.body.msg || "")
                    }
                },
                err => {
                    this.$message.error(err)
                }
            )
        },
        invoiceSelectionChange(row) {
            // console.log(row)
            this.selectInvoice = row
        },
        changeselect(row) {
            console.log(row)
        },
        select(selection) {
            //发生改变时触发
            return
        },
        handleCurrentChange(val) {
            // if(!this.isAlert) return
            console.log(val)
            this.selectInvoice = val
        },
        popMapChoose(){
          this.dialogMapVisible = true
        },
        saveComponent(){
          let self = this
          self.preSave('submit')
        }
    },
    mounted: function() {
        var _this = this
        if (_this.params.client_no) {
            _this.getCustomerDetail()
        }
        this.$root.eventHandle.$on('popMapChoose', this.popMapChoose)
        // this.params.__close = this.closeComponent
        // setTimeout(function() {
        //     // 页面初始化数据，用作对比，是否有更新
        //     for (var key in _this.submit) {
        //         _this.initialData[key] = _this.submit[key]
        //     }
        // }, 500)
        // _this.params.__close = _this.closeTab
    },
    beforeDestroy() {
      this.$root.eventHandle.$off('popMapChoose', this.popMapChoose)
    }
}
</script>
<style type="text/css" scoped>
.el-select .el-input {
    width: 120px
}
</style>
