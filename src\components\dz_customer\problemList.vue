<template>
  <!-- 问题列表 -->
  <div>
      <xpt-headbar >
        <template slot='left'>
          <el-button
            @click="addProblem"
            type="success"
            size="mini"
            >新增问题</el-button
          >
          <el-button
            @click="getList"
            type="primary"
            size="mini"
            :loading="loading"
            >刷新</el-button
          >
          <el-button
            @click="submitOrder"
            type="primary"
            size="mini"
            :loading="loading"
            >保存</el-button
          >
        
        </template>
      </xpt-headbar>
      <el-row :gutter="24">
    </el-row>
      <el-row>
         <dz-list3
          class="goodsList"
          :showHead="false"
          :data="listData"
          :colData="colData"
          :tools="tools"
        >
        <template slot='problem_type' slot-scope='scope'>
							 <el-select size='mini' v-model="scope.row.problem_type" style="width:99%;">
                    <el-option 
                        v-for="(option, optionIndex) in supply_reason"
                        :key="optionIndex"
                        :label="option.label" 
                        :value="option.value"></el-option>
                </el-select>
						</template>
        </dz-list3>
        </el-row>
      
  </div>
 
</template>

<script>
import { getDesignInfo, customGoodsSubmit, customGoodsValuation,customGoodsSubmitOrder,openGoodsDesign, gotoSwj } from "./common/api";
import formCreate from "./components/formCreate/formCreate";
import btnStatus from "./common/mixins/btnStatus";
import dzList3 from "./components/list/list3";
import { imageAdmin, imageView2 }  from './alert/alert'
import { supply_reason } from './common/dictionary/supplyDictionary'

export default {
  props: {
    params: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  components: { formCreate, dzList3 },
  mixins: [btnStatus],
  data() {
    let self = this;
    return {
      listData:[],
      supply_reason: supply_reason,
      valuationLoading: false,
      submitOrderLoading:false,
      loading: false,
      submitLoading: false,
      canNotSubmit: true,
      canNotSubmitAssess: true,
      role: ["other"],
      goodsList: [], //商品列表
      designRoomList: [],
      btns: [], //顶部操作按钮
      tools: [
        //商品操作按钮组
        {
          type: "primary",
          txt: "上传",
          click(d) {
            return self.handleUpload(d);
          },
          show(d) {
            return true
          }
        },
        {
          type: "danger",
          txt: "删除",
          click(d) {
            self.delete(d);
          },
          show(d) {
            return true 
          }
        },
        {
          type: "success",
          txt: "查看",
          click(d) {
            self.handleView(d);
          },
          show(d) {
            return true
            // return self.isVerify({
            //   goods_status: d.goods_status,
            //   client_status: self.params.row.client_status
            // })
            
          },
        }
      ],
      colData: Object.freeze([
        {
          label: "补单内容",
          prop: "supply_content",
          width: "130",
          bool: true,
          elInput: true,
          disabled(){
            return false
          },
        },
        {
          label: "问题原因",
          prop: "problem_reason",
          width: "130",
          disabled(){
            return false
          },
          bool: true,
          elInput: true,
        },
        {
          label: "责任",
          slot: "problem_type",
          
          width: "90"
        },
       
      ]),
    };
  },
  async mounted() {
    this.getList();
  },
  beforeDestroy() {
  },
  methods: {
    handleView( row) {
      if(!row.problem_id){
              this.$message.error('请保存该问题')
              return;
            }
            // 查看图片
        imageView2(this, {
            no: {
                parent_no: row.problem_id,
            },
            client_number: this.goods_id,
            useBy: 'design'
        })
    },
     valid(row, prop, label) {
            // 提交验证
            console.log(1)
            let flag = true;
            if(row) {
               
            } else {
                // 校验补单原因
              this.listData.forEach(item=>{
                if(item.supply_content.trim() === '' ){
                  this.$message.error('补单内容不能为空')
                    flag =  false
                }
                if(item.supply_content.length >100 ){
                  this.$message.error('补单内容字数不能超过100')
                    flag =  false
                }
                if(item.problem_reason.trim() === '' ){
                  this.$message.error('补单原因不能为空')
                    flag =  false
                }
                if(item.problem_reason.length >30 ){
                  this.$message.error('补单原因字数不能超过30')
                    flag =  false
                }
                if(item.problem_type.trim() === '' ){
                  this.$message.error('责任不能为空')
                    flag =  false
                }
              })
            }
            
            return flag
            
        },
    submitOrder(){
      if(!this.valid()) {
               return 
            }
       this.ajax.postStream(
            "/custom-web/api/customGoodsProblemDetail/batchSave",
            this.listData,
            (res) => {
              res = res.body;
              this.$message({
                message: res.msg,
                type: res.result ? "success" : "error",
              });
              if (res.result) {
                this.getList()

              }
            }
          );
    },
  

  
    delete(d) {
      let rejectNum = 0
      if(!d.problem_id){
        this.listData.splice(this.listData.indexOf(d),1)

        return;
      }
      //删除商品
      this.$confirm("您确定要删除该问题和该问题的上传资料吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const { problem_id } = d;
          let data = { problem_id } || {};
          this.ajax.postStream(
            "/custom-web/api/customGoodsProblemDetail/deleteOneById",
            problem_id,
            (res) => {
              res = res.body;
              this.$message({
                message: res.msg,
                type: res.result ? "success" : "error",
              });
              if (res.result) {
               this.getList()
              }
            }
          );
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleUpload( row) {
            if(!row.problem_id){
              this.$message.error('请保存该问题')
              return;
            }
            // this.uploadIndex = index
            // 上传
            imageAdmin(this, {
                    parent_no: row.problem_id,
            }, (data) => {
                // this.tableData[this.uploadIndex].imgs = data
                // this.validFileNull() 
            },'请根据售后类型上传文件（1，产品线框图2，售后判责凭证:①外包装标签图②产品标签图③问题产品整体图④问题产品细节图）') 

        },
    getList(showSuccessTips = true) {
      const { goods_id } = this.params.row;
      return new Promise((resolve, reject) => {
        this.loading = true
        this.ajax.postStream(
          "/custom-web/api/customGoodsProblemDetail/list",
          {goods_id:goods_id},
          (res) => {
            res = res.body;
            
            this.loading = false
            if (res.result) {
              this.listData = res.content || []
             
            } else {
              reject()
            }
            showSuccessTips && this.$message({
              message: res.msg,
              type: res.result ? "success" : "error",
            });
          },
          (err) => {
            this.loading = false
          }
        );
      })
      
    },
  
   
     addProblem(item, values = {}) {
      const self = this
      self.listData.push({
        goods_id:this.params.row.goods_id,
        supply_content:'',
        problem_reason:'',
        problem_type:'',
      })

    },
  },
};
</script>

