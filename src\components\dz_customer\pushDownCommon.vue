<template>
  <!-- 下推采购公共列表 -->
  <div class="searchBox" style="width: 100%">
    <div class="search-content">
      <form-create
        :formData="queryItems"
        @save="query"
        savetitle="查询"
        ref="formcreate"
      ></form-create>
    </div>
    <dz-list
      :rowSummaryMethod="rowSummaryMethod"
      :rowColData="rowColData"
      :data="customerList"
      :colData="colData"
      :pageTotal="pageTotal"
      :btns="btns"
      :tools="tools"
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
      ref="dzList"
      countUrl="/afterSale-web/api/forced/closure/query/count"
      @count-off="countOff"
      :showCount="showCount"
    >
    </dz-list>
  </div>
</template>

<script>
import fn from "@/common/Fn.js";
import _ from "loadsh";
import { getRole } from "./common/api";
import formCreate from "./components/formCreate/formCreate";
import btnStatus from "./common/mixins/btnStatus";
import { getClientStatus } from "./common/map";
import { client_status, client_status_supply } from "./common/clientDictionary";
import dzList from "./components/list/list5";
import myTable from "./components/table/table";
// 下推
export default {
  props: {
    params: {
      type: Object,
    },
    trade_type: {
      type: String,
      default: "ORIGINAL",
    },
  },
  components: { formCreate, dzList, myTable },
  mixins: [btnStatus],
  data() {
    let self = this;
    return {
      showCount: false,
      countOffFlag:false,
      searchParam: {
        client_start_status: "WAITING_PURCHASE",
        client_end_status: "WAITING_PURCHASE",
      },
      role: ["other"],
      queryItems: [],
      customerList: [],
      btns: [
        {
          type: "primary",
          txt: "刷新",
          loading: false,
          show: true,
          click: () => {
            this.refresh();
          },
        },
      ],
      pageNow: 1,
      pageSize: 20,
      pageTotal: 0,
      rowColData: Object.freeze([
        {
          label: self.trade_type === "SUPPLY" ? "补单商品号" : "商品号",
          prop: "goods_id",
          width: "130",
          redirectClick(d) {
            self.goodsdetail(d);
          },
        },
        {
          label: "商品名称",
          prop: "message",
          width: 130,
        },
        {
          label: "颜色",
          prop: "color_cn",
          width: 100,
        },
        {
          label: "商品状态",
          prop: "goods_status_cn",
          width: 100,
        },
        {
          label: "所在空间",
          prop: "design_room_name",
          width: 100,
        },
        {
          label: "毛利系数",
          prop: "mlxs",
          width: 80,
        },
        {
          label: "零售价格",
          prop: "retail_price",
          width: 80,
        },
        {
          label: "采购价格",
          prop: "purchase_price",
          width: 120,
        },
        {
          label: "驳回理由",
          prop: "reason",
          bool: true,
          isTextarea: true,
          autosize: true,
          disabled(row) {
            return false;
          },
          prop: "reason",
        },
      ]),
      colData: Object.freeze([
        {
          label: self.trade_type === "SUPPLY" ? "补单号" : "订单号",
          prop: "client_number",
          width: "160",
          redirectClick(d) {
            self.detail(d);
          },
        },
        {
          label: "客户名",
          prop: "client_name",
          width: "160",
        },
        {
          label: '店铺名称',
          prop: 'shop_name',
          width: 'auto'
        },
        {
          label: "手机",
          prop: "client_mobile",
          width: "90",
          format: "hidePhoneNumber",
        },
        
        {
          label: "建档日期",
          prop: "create_time1",
          width: "90",
        },
        {
          label: "建档人",
          prop: "shopping_guide_name",
          width: "60",
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: "60",
        },
        {
          label: "状态",
          prop: "client_status",
        },
      ]),
      tools: [],
    };
  },
  methods: {
    //批量驳回
    reject(d) {
      const { client_number, custom_trade_id } = d;
      this.$root.eventHandle.$emit("alert", {
        params: {
          client_number,
          row:d,
          trade_type: this.trade_type,
          custom_trade_id,
        },
        component: () => import("./alert/rejectAlert2.vue"),
        style: "width:1100px;height:740px",
        title: `订单号：${d.client_number}`,
      });
    },
    goodsdetail(d) {
      //商品详情
      this.$root.eventHandle.$emit("creatTab", {
        name: this.trade_type === "SUPPLY" ? "补单商品详情" : "商品详情",
        component: () =>
          import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
        params: {
          goodsInfo: d,
        },
      });
    },
    // 获取
    rowSummaryMethod(param) {
      const { columns, data } = param;
      const sums = [];
      let retail_priceIndex;
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总计";
          return;
        }
        if (column.property === "purchase_price") {
          column.property === "purchase_price" && (retail_priceIndex = index);
          const values = data.map((item) => Number(item[column.property]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = "N/A";
          }
        }
      });
      sums[retail_priceIndex] = `总采购价：${parseFloat(
        sums[retail_priceIndex]
      ).toFixed(2)}元`;
      return sums;
    },
    refresh() {
      this._getDataList();
    },
    // 监听每页显示数更改事件
    pageChange(pageSize, param) {
      this.pageSize = pageSize;
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    // 监听页数更改事件
    currentPageChange(page, param) {
      this.pageNow = page;
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    // 查询所有客户列表
    query(param) {
      let self = this
      Object.assign(this.searchParam, param);
      // this._getDataList();
      new Promise((res,rej)=>{
        this._getDataList(res);
			}).then(()=>{
				if(self.pageNow != 1){
					self.pageTotal = 0;
				}
				self.showCount = false;

			})
    },
    save() {
      this.$refs.formcreate.save();
    },
    detail(d) {
      if (this.trade_type === "ORIGINAL") {
        this.$root.eventHandle.$emit("creatTab", {
          name: "订单详情",
          component: () =>
            import("@components/dz_customer/clientInfo/clientInfo.vue"),
          params: {
            customerInfo: d,
          },
        });
      } else {
        this.$root.eventHandle.$emit("creatTab", {
          name: "补单详情",
          component: () =>
            import("@components/dz_customer/supplement/supplyInfo.vue"),
          params: {
            client_number: d.client_number,
          },
        });
      }
    },
    countOff(){

			let self = this,
			url = "/custom-web/api/customSysTrade/getCustomSysTradeListCount";
			if(!self.customerList.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;
      let data = {};
      Object.assign(data, this.searchParam, this.initParam);
      data.page = {
        length: this.pageSize,
        pageNo: this.pageNow,
      };
			self.ajax.postStream(url,data,function(response){
					if(response.body.result){
						
						self.pageTotal = response.body.content;
						self.showCount = true;
						self.countOffFlag = false;

					}else{
						self.$message.error(response.body.msg);
					}
				});
		},
    _getDataList() {
      // 正单和补单的列表查询不一样
      let self = this;
      var url =
        this.trade_type === "ORIGINAL"
          ? "/custom-web/api/customSysTrade/getCustomSysTradeList"
          : "/custom-web/api/customSysTrade/getSupplyPushList";
      let data = Object.assign(this.searchParam, {
        trade_type: this.trade_type,
      });
      data.page = {
        length: this.pageSize,
        pageNo: this.pageNow,
      };
      let refreshBtn = this.btns.filter((item) => item.txt === "刷新")[0];
      refreshBtn.loading = true;
      this.ajax.postStream(
        url,
        data,
        (d) => {
          if (d.body.result) {
            let content = d.body.content || {};
            const listLength = _.get(content, "list.length");
            for (var i = 0; i < listLength; i++) {
              content.list[i].create_time1 = fn.dateFormat(
                content.list[i].create_time,
                "yyyy-MM-dd"
              );
              content.list[i].client_status_value =
                content.list[i].client_status;
              content.list[i].client_status = getClientStatus(
                content.list[i].client_status
              );
            }
            this.customerList = content.list || [];
            // this.pageTotal = content.count || 0;
            if(!self.showCount){
              self.pageTotal = content.list.length == (self.pageSize+1)? (self.pageNow*self.pageSize)+1:(self.pageNow*self.pageSize);
            }
          } else {
            this.$message({
              message: d.body.msg,
              type: "error",
            });
          }

          refreshBtn.loading = false;
        },
        (err) => {
          this.$message.error(err);
          refreshBtn.loading = false;
        }
      );
    },
    getQueryItems() {
      this.queryItems = [
        {
          cols: [
            {
              formType: "elInput",
              prop: "client_number",
              label: this.trade_type === "SUPPLY" ? "补单号" : "订单号",
            },
            {
              formType: "myInput",
              prop: "client_mobile",
              label: "客户电话",
              type: "string",
              maxlength: 11,
              event: {
                input(v, col) {
                  col.value = v.replace(/\D/g, "");
                },
              },
            },
            { formType: "elInput", prop: "client_name", label: "客户名称" },
            { formType: "elInput", prop: "designer_name", label: "设计师" },
            // {formType: 'elInput', prop: 'client_address', label: '客户地址'},
            {
              formType: "elDatePicker",
              prop: "create_time",
              props: ["start_create_date", "end_create_date"],
              label: "建档日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
            {
              formType: "selectRange",
              prop: "client_status",
              value: ["WAITING_PURCHASE", "WAITING_PURCHASE"],
              props: ["client_start_status", "client_end_status"],
              label: this.trade_type === "SUPPLY" ? "补单状态" : "订单状态",
              options: [
                this.trade_type === "SUPPLY"
                  ? client_status_supply
                  : client_status,
                this.trade_type === "SUPPLY"
                  ? client_status_supply
                  : client_status,
              ],
              span: 12,
            },
          ],
        },
      ];
    },
    
    undeal(d) {
      const {client_number, client_name, client_mobile, client_sex, designer_name} = d
      const data = {
        client_number, client_name, client_sex, client_mobile, designer_name
      }
     
     
        this.$root.eventHandle.$emit('creatTab', {
          name: '取消订单登记',
          component: () => import('@components/dz_customer/undeal.vue'),
          params: {
            isBefore:false,
            customerInfo: data,
            message:null
          }
        })
     
      

      
    },
    // 撤回
    revert(d) {
      const { client_number } = d;
      this.ajax.postStream(
        "/custom-web/api/customSysTrade/withdraw",
        {
          client_number,
        },
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
          } else {
            this.$message.error(res.body.msg);
          }
        }
      );
    },
  },
  created() {
    this.getQueryItems();
    this.$root.eventHandle.$on("refreshpushDown", this.refresh);
  },
  async mounted() {
    const self = this;
    this.role = await getRole();
    this.tools = [
      {
        type: "primary",
        txt: "驳回/下推",
        click: (d) => {
          self.reject(d);
        },
      },
      {
        type: "danger",
        txt: "撤回",
        click: (d) => {
          self.revert(d);
        },
        // show(d) {
        //   return self.isPush(
        //     { client_status: d.client_status_value },
        //     self.role
        //   );
        // },
      },
       {
        type: 'danger',
        txt: '取消订单',
        click(d) {
          self.undeal(d)
        },
        // show(d) {
        //   return self.isUndeal({client_status: d.client_status_value}, self.role)
        // }
      },
    ];
    this._getDataList();
  },
  beforeDestroy() {
    this.$root.eventHandle.$off("refreshpushDown", this.refresh);
  },
};
</script>
<style scoped>
/*  */
.search-content {
  border: 1px #aaa solid;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
.searchBox {
  margin: 0px auto;
  height: 99%;
  width: 80%;
  display: flex;
  flex-direction: column;
}
</style>
