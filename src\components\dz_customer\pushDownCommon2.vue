<template>
  <!-- 下推采购公共列表 -->
  <div class="searchBox" style="width: 100%;">
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询" ref="formcreate"></form-create>
    </div>
    <dz-list2
      :rowSummaryMethod="rowSummaryMethod"
      :rowColData="rowColData"
      :data="customerList"
      :colData="colData"
      :pageTotal="pageTotal"
      :btns="btns"
      :tools="tools"
      :reject="reject"
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
      ref="dzList2"
    >
  </dz-list2>
  </div>
</template>

<script>
import fn from "@/common/Fn.js";
import _ from "loadsh";
import { addPush } from "./alert/alert";
import { rejectPushPurchase, pushMaterial ,withdrawSupply } from "./common/api"
import {
  getRole,
  customSupplyAftersale,
  saveAfterPlan,
  submitAfterPlan,
} from "./common/api";
import formCreate from "./components/formCreate/formCreate";
import btnStatus from "./common/mixins/btnStatus";
import { getClientStatus } from "./common/map";
import {
  client_status,
  client_status_supply,
} from "./common/clientDictionary";
import dzList2 from "./components/list/list";
import myTable from "./components/table/table";

export default {
  props: {
    params: {
      type: Object,
    },
    trade_type: {
      type: String,
      default: "ORIGINAL",
    },
  },
  components: { formCreate, dzList2, myTable },
  mixins: [btnStatus],
  data() {
    let self = this;
    return {
      searchParam: {
        client_start_status: "WAITING_PURCHASE",
        client_end_status: "WAITING_PURCHASE",
      },
      role: ["other"],
      queryItems: [],
      customerList: [],
      btns: [
        {
          type: "primary",
          txt: "刷新",
          loading: false,
          show: true,
          click: () => {
            this.refresh();
          },
        },
      ],
      pageNow: 1,
      pageSize: 20,
      pageTotal: 0,
      rowColData: Object.freeze([
        {
          label: self.trade_type === "SUPPLY" ? "补单商品号" : "商品号",
          prop: "goods_id",
          width: "130",
          redirectClick(d) {
            self.goodsdetail(d);
          },
        },
        {
          label: "商品名称",
          prop: "message",
          width: 130,
        },
        {
          label: "颜色",
          prop: "color_cn",
          width: 100,
        },
        {
          label: "商品状态",
          prop: "goods_status_cn",
          width: 100,
        },
        {
          label: "所在空间",
          prop: "design_room_name",
          width: 100,
        },
        {
          label: "毛利系数",
          prop: "mlxs",
          width: 80
        },
        {
          label: "零售价格",
          prop: "retail_price",
          width: 80
        },
        {
          label: "采购价格",
          prop: "purchase_price",
          width:120
        },
        {
          label:"驳回理由",
          prop:"reason",
          bool: true,
					isTextarea: true,
					autosize: true,
          disabled(row) {
            return false
          },
          prop: "reason"
        },
      ]),
      colData: Object.freeze([
        {
          label: self.trade_type === "SUPPLY" ? "补单号" : "订单号",
          prop: "client_number",
          width: "160",
          redirectClick(d) {
            self.detail(d);
          },
        },
        {
          label: "客户名",
          prop: "client_name",
          width: "160",
        },
        {
          label: "手机",
          prop: "client_mobile",
          width: "90",
          format: 'hidePhoneNumber'
        },
        // {
        //   label: '客户地址',
        //   prop: 'client_address',
        //   width: 'auto'
        // },
        {
          label: "建档日期",
          prop: "create_time1",
          width: "90",
        },
        {
          label: "建档人",
          prop: "shopping_guide_name",
          width: "60",
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: "60",
        },
        {
          label: "状态",
          prop: "client_status"
        },
      ]),
      tools: [],
    };
  },
  methods: {
    //批量驳回
    reject(arr){
      //
      let self=this
      if(!arr||arr.length<1){
        return this.$message.error("请至少选择一条数据")
      }
      let next=true
      arr.forEach(elem=>{
        if(!elem.reason){
          next=false
        }
      })
      if(this.isRequest){
        this.$message.error("请等待上一次请求结果")
      }
      if(next){
        let data=arr.map(e=>{
          return {
            custom_goods_id:e.custom_goods_id,
            remark:e.reason
          }
        })
        this.isRequest=true
        rejectPushPurchase(data, true, true).then(res => {
          this.isRequest = false
          if(res.result) {
              // 添加成功
              // 此条注释uat被冲重新提交用
              self._getDataList()
              callback()
              close()
          }
        })
      }else{
        this.$message.error("驳回理由为必填，请填写！")
      }
    },
    push(d, pushUrl) {
    /**
     * @description: 下推
     * @param {*}
     * @return {*}
     */  
      const self = this
      if (self.trade_type === "SUPPLY") {
          // 补单下推直接下推不需要选工厂
          if (self.request) {
            self.$message({
              type: "warning",
              message: "请等待上一次请求结束",
            });
            return;
          }
          self.request = true;
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          
          // 推物料
          // pushMaterial(d.client_number).then((res) => {});
          // 拆单下推同步补单流程
          customSupplyAftersale(d.custom_trade_id).then((body) => {
            if (body.result) {
              saveAfterPlan(d.custom_trade_id).then((body) => {
                if (body.result) {
                  submitAfterPlan(d.custom_trade_id).then((body) => {
                    self.refresh();
                    self.$message({
                      message: body.msg || "",
                      type: body.result ? "success" : "error",
                    });
                    self.request = false
                    loading.close();
                  });
                } else {
                  self.request = false;
                  loading.close();
                  self.$message({
                    message: body.msg,
                    type: body.result ? "success" : "error",
                  });
                }
              });
            } else {
              loading.close();
              self.request = false;
              self.$message({
                message: body.msg,
                type: body.result ? "success" : "error",
              });
            }
          });
        } else {
          addPush(
            self,
            Object.assign({}, d, { trade_type: self.trade_type, pushUrl })
          );
        }
    },
    withdrawSupply(d){
      let self = this;
      this.$confirm(
					"当前操作会导致单据被撤回，是否继续？",
					"提示",
					{
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "danger",
					}
				).then(() => {
					   withdrawSupply(d.client_number, false, true).then(res => {
              this.isRequest = false
              if(res.result) {
                  // 添加成功
                  // 此条注释uat被冲重新提交用
                  self._getDataList()
              }
            })
				}).catch(() => {
					return false;
				});
   
    },
    pushDown(d) {
      this.ajax.postStream(
        "/custom-web/api/customPush/purchase",
        { client_number: d.client_number },
        (res) => {
          this.request = false;
          res = res.body;
          this.$message({
            message: res.msg || "",
            type: res.result ? "success" : "error",
          });
          if (res.result) {
            this.$root.eventHandle.$emit("refreshpushDown");
          }
        },
        (err) => {
          this.$message({
            message: "服务出错",
            type: "error",
          });
          this.request = false;
        }
      );
    },
    goodsdetail(d) {
      //商品详情
      this.$root.eventHandle.$emit("creatTab", {
        name: this.trade_type === "SUPPLY" ? "补单商品详情" : "商品详情",
        component: () =>
          import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
        params: {
          goodsInfo: d,
        },
      });
    },
    rowSummaryMethod(param) {
      const { columns, data } = param;
      const sums = [];
      let retail_priceIndex;
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总计";
          return;
        }
        if (column.property === "purchase_price") {
          column.property === "purchase_price" && (retail_priceIndex = index);
          const values = data.map((item) => Number(item[column.property]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = "N/A";
          }
        }
      });
      sums[retail_priceIndex] = `总采购价：${parseFloat(
        sums[retail_priceIndex]
      ).toFixed(2)}元`;
      return sums;
    },
    refresh() {
      this._getDataList();
    },
    // 监听每页显示数更改事件
    pageChange(pageSize, param) {
      this.pageSize = pageSize;
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    // 监听页数更改事件
    currentPageChange(page, param) {
      this.pageNow = page;
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    // 查询所有客户列表
    query(param) {
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    save() {
      this.$refs.formcreate.save();
    },
    detail(d) {
      if (this.trade_type === "ORIGINAL") {
        this.$root.eventHandle.$emit("creatTab", {
          name: "订单详情",
          component: () =>
            import("@components/dz_customer/clientInfo/clientInfo.vue"),
          params: {
            customerInfo: d,
          },
        });
      } else {
        this.$root.eventHandle.$emit("creatTab", {
          name: "补单详情",
          component: () =>
            import("@components/dz_customer/supplement/supplyInfo.vue"),
          params: {
            client_number: d.client_number,
          },
        });
      }
    },
    _getDataList() {
      // 正单和补单的列表查询不一样
      var url = this.trade_type === 'ORIGINAL' ? 
        "/custom-web/api/customSysTrade/getCustomSysTradeList" :
        "/custom-web/api/customSysTrade/getSupplyPushList"
      let data = Object.assign(this.searchParam, {
        trade_type: this.trade_type,
      });
      data.page = {
        length: this.pageSize,
        pageNo: this.pageNow,
      };
      let refreshBtn = this.btns.filter((item) => item.txt === "刷新")[0];
      refreshBtn.loading = true;
      this.ajax.postStream(
        url,
        data,
        (d) => {
          if (d.body.result) {
            let content = d.body.content || {};
            const listLength = _.get(content, "list.length");
            for (var i = 0; i < listLength; i++) {
              content.list[i].create_time1 = fn.dateFormat(
                content.list[i].create_time,
                "yyyy-MM-dd"
              );
              content.list[i].client_status_value =
                content.list[i].client_status;
              content.list[i].client_status = getClientStatus(
                content.list[i].client_status
              );
            }
            this.customerList = content.list || [];
            this.pageTotal = content.count || 0;
          } else {
            this.$message({
              message: d.body.msg,
              type: "error",
            });
          }

          refreshBtn.loading = false;
        },
        (err) => {
          this.$message.error(err);
          refreshBtn.loading = false;
        }
      );
    },
    getQueryItems() {
      this.queryItems = [
        {
          cols: [
            {
              formType: "elInput",
              prop: "client_number",
              label: this.trade_type === "SUPPLY" ? "补单号" : "订单号",
            },
            {
              formType: "myInput",
              prop: "client_mobile",
              label: "客户电话",
              type: "string",
              maxlength: 11,
              event: {
                input(v, col) {
                  col.value = v.replace(/\D/g, "");
                },
              },
            },
            { formType: "elInput", prop: "client_name", label: "客户名称" },
            { formType: "elInput", prop: "designer_name", label: "设计师" },
            // {formType: 'elInput', prop: 'client_address', label: '客户地址'},
            {
              formType: "elDatePicker",
              prop: "create_time",
              props: ["start_create_date", "end_create_date"],
              label: "建档日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
            {
              formType: "selectRange",
              prop: "client_status",
              value: ["WAITING_PURCHASE", "WAITING_PURCHASE"],
              props: ["client_start_status", "client_end_status"],
              label: this.trade_type === "SUPPLY" ? "补单状态" : "订单状态",
              options: [
                this.trade_type === "SUPPLY"
                  ? client_status_supply
                  : client_status,
                this.trade_type === "SUPPLY"
                  ? client_status_supply
                  : client_status,
              ],
              span: 12,
            },
          ],
        },
      ];
    },
    choose(d) {
      let self = this;
      let params = {
        row:d,
        client_number: d.client_number,
        original_client_number:d.original_client_number,
        pushObj:{factory_code:d.factory_code,factory_name:d.factory_name},
        callback: (result) => {
          const { factory_code, supplierName } = result;
          // self.getList();
          // self.pushObj = result.factoryList ;
          d.factory_code = result.factory_code;
          d.factory_name = result.factory_name;
          self.push(d);
          
        },
      };
      self.$root.eventHandle.$emit("alert", {
        params: params,
        component: () => import("./alert/rejectAlert3"),
        style: "width:1218px;height:650px",
        title: "下推",
      });
    },
  },
  created() {
    this.getQueryItems();
    this.$root.eventHandle.$on("refreshpushDown", this.refresh);
  },
  async mounted() {
    const self = this
    this.role = await getRole();
    this.tools = [
      {
          type: "primary",
          txt: "下推",
          click: (d) => {
            self.choose(d)
          },
          show(d) {
            if(self.role.indexOf('DZ_CSY') == -1) {
              return false
            }else {
              return true
            }
            
          },
        },
        {
          type: "primary",
          txt: "撤回",
          click: (d) => {
            self.withdrawSupply(d)
          },
          show(d) {
            // console.log(d)
              // return true
            if(d.client_status_value == 'WAITING_PUSH_GOODS') {
              return true
            }else {
              return false
            }
            
          },
        },
        // {
        //   type: 'primary',
        //   txt: '旧版下推',
        //   click: (d) => {
        //     self.push(d, "/custom-web/api/customPush/pushPurchaseOld")
        //   },
        //   show(d) {
        //     return  self.role.indexOf('ZBDZFZR') !== -1 && 
        //             self.trade_type !== "SUPPLY" && 
        //             self.isPush(
        //               { client_status: d.client_status_value },
        //               self.role
        //             );
        //   }
        // }
    ]
    this._getDataList();
  },
  beforeDestroy() {
    this.$root.eventHandle.$off("refreshpushDown", this.refresh);
  },
};
</script>

<style scoped>
.search-content {
  border: 1px #aaa solid;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
.searchBox {
  margin: 0px auto;
  height: 99%;
  width: 80%;
  display: flex;
  flex-direction: column;
}
</style>
