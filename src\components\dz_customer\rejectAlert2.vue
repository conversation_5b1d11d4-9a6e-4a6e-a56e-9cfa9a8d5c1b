<template>
  <!-- 下推采购阶段 -->
  <div>
   
    <el-form  :model='pushObj' label-position="right" label-width="120px">
            <el-col :span='24'>
              <el-form-item label="工厂：" v-if="pushObj">
                <el-select  v-model="pushObj.factory_code" @change="pushObjChange" size="mini" >
                  <el-option
                    v-for="(item) in dataList"
                    :key="item.factory_code"
                      :label="item.factory_name"
                      :value="item.factory_code"
                  >
                  </el-option>
              </el-select>
              </el-form-item>
			      </el-col>
        </el-form>
      	<el-tabs v-model="selectTab1" >
		<el-tab-pane label='原下推工厂' name='originalTradeFactory'>

    <el-form  :model='originalTradeFactory' label-position="right" label-width="120px">

            <el-col :span='24'>
              <el-form-item v-for="(item,key) in originalList" :label="item.purchase_trade_name+'订单：'" :key="key">
                {{item.factory_name}}
               
              </el-form-item>
               <!-- <el-form-item label="铝门订单：" v-if="originalTradeFactory.ALUMINUM"  >
                  {{originalTradeFactory.ALUMINUM}}

              </el-form-item>
               <el-form-item label="柜体订单：" v-if="originalTradeFactory.CABINET">
                  {{originalTradeFactory.CABINET}}

              </el-form-item> -->
             
			      </el-col>
        </el-form>
		</el-tab-pane>

 </el-tabs>

    <div slot="footer" class="btns">
      <el-button
        :disabled="request"
        :loading="request"
        type="primary"
        @click="save() "
        >保存工厂</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataList:[],
      originalList:[],
      purchase_trade_type_option:[],
      pushObj:{
      
      },
      originalTradeFactory:{
        BLISTER:'',
        ALUMINUM:'',
        CABINET:'',
      },
      selectTab1:'originalTradeFactory',
      cabinetOpiton:[],
      blisterOpiton:[],
      allminumOpiton:[],
      formData: [],
      labelWidth: "70px",
      ruleData: Object.freeze({
        provider: { required: true, message: "请选择工厂", trigger: "change" },
      }),
      
     
      request: false,
    };
  },
  props: {
    params: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    pushObjChangeCABINET(val){
      console.log(val)
      if(!val) return;
      this.pushObjChange(val,'CABINET')
    },
     pushObjChangeALUMINUM(val){
      console.log(val)
      if(!val) return;
      this.pushObjChange(val,'ALUMINUM')
    },
     pushObjChangeBLISTER(val){
      console.log(val)
      if(!val) return;
      this.pushObjChange(val,'BLISTER')
    },
    pushObjChange(val,type){
      
      this.dataList.forEach(item=>{
        if(item.factory_code == val){
          this.pushObj.factory_name = item.factory_name
        }
      })
    },
    
  
    save() {
      // 保存工厂
      let _this = this;
      // const { factory_code, supplierName } = _this.result;
      let data = {
        client_number: this.params.client_number,
        factory_name: this.pushObj.factory_name,
        factory_code:this.pushObj.factory_code
        // purchaseTrades: this.params.ids,
      };
      this.ajax.postStream(
        "/custom-web/api/customSupplyAftersale/factory",
        data,
        (res) => {
          if (res.body.result) {
            _this.$message.success(`${res.body.msg}`);
            _this.params.callback &&
              _this.params.callback({
                factoryList: this.pushObj,
              });
            _this.$root.eventHandle.$emit("removeAlert", _this.params.alertId);
            

          } else {
            _this.$message.error(`${res.body.msg}`);
          }
        }
      );
    },
     getDetail(callback){
				let self = this;
			
						this.ajax.postStream(
						'/custom-web/api/apartConfig/allList',
						{},
						(res) => {
						res = res.body;
						if (res.result) {
							let hasObj= {};
 
							self.purchase_trade_type_option = res.content.reduce((cur,next) => {
								hasObj[next.type] ? "" : (hasObj[next.type] = true && cur.push(next)); 
								return cur;
							},[]) 
              console.log(self.purchase_trade_type_option)
              callback()
						}else{
							this.$message({
							message: res.msg,
							type: "error",
							});
						}
						},
						(err) => {
						this.$message({
							message: err.msg,
							type: "error",
						});
						}
					);
						

			},
    getOriginalTradeFactory(){
      let _this = this;
      let data = {
        original_client_number: this.params.original_client_number,
      };
      this.ajax.postStream('/custom-web/api/customGoods/getOriginalTradeFactory',data,function(response){
				if(response.body.result){
          // this.originalTradeFactory = response.body.content[0]
          let originalList = response.body.content;
          originalList.forEach(item=>{
            _this.purchase_trade_type_option.forEach(tradeItem=>{
              if(item.purchase_trade_type == tradeItem.apart_number){
                item.purchase_trade_name = tradeItem.type;
              }
            })
          })
          _this.originalList = originalList;
				}
				else{
					this.$message.error(response.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
    },
    urgentFreeList(){
      
			var _this = this;
			let search = {status: 1, page: {length: 50, pageNo: 1}}
			this.ajax.postStream('/custom-web/api/customSupplierUrgentFee/list',search,function(response){
				if(response.body.result){
					_this.dataList = [];
          response.body.content.list.forEach(item=>{
            _this.dataList.push({
              factory_name:item.supplierName,
              factory_code:item.factory_code,
            })
          })
				}
				else{
					_this.$message.error(response.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
    },
  },
  mounted(){
    this.pushObj = this.params.pushObj

    this.urgentFreeList();
    new Promise((resolve,reject)=>{

      this.getDetail(resolve)
    }).then(res=>{
      this.getOriginalTradeFactory();

    })

  }
};
</script>

<style lang="stylus" scoped>
.btns {
  text-align: center;
  margin-top: 20px;

  &>span {
    padding-left: 10px;
    color: #aaa;
  }
}
</style>
