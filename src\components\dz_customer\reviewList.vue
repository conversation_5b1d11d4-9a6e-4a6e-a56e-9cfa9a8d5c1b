<template>
  <!-- 原始订单拆审列表 -->
  <div style="height: 99%">
    <el-tabs v-model="selectTab" @tab-click="tabsChange">
      <el-tab-pane label='全部订单' name='all' class='xpt-flex'>
          <verifcommon
          ref="list"
          :params = params
          :btns="list_btns"
          :tools="tools"
          url="/custom-web/api/splitAudit/getReviewGoodsList"
          :defaultValue="defaultValue"
          :trade_type="trade_type"
          :checkBoxSelectable="canLock"
          verType="cs"
          selection="checkbox"
          @selection-change-s="selectionChange"
          ></verifcommon>
      </el-tab-pane>
      <el-tab-pane label='我的待办' name='myList' class='xpt-flex'>
          <verifcommon
          v-if="initMyList"
          ref="myList"
          :params = params
          :initParam="{ operate_phase: 'split_audit', reject_status: 'REJECT_PUSH_PURCHASE' }"
          :btns="btns"
          :tools="tools"
          url="/custom-web/api/customGoods/getMyGoodsList"
          :defaultValue="defaultValue"
          :trade_type="trade_type"
          :taggelClassName="taggelClassName"
          ></verifcommon>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import verifcommon from "./verifcommon";
import { getRole, lockingMoreAuditGoods } from "./common/api";

export default {
  components: {
    verifcommon,
  },
  props: {
    params: {
      type: Object,
    },
    trade_type: {
      type: String,
      default: "ORIGINAL",
    },
  },
  data() {
    const self = this
    return {
      // 初始化我的订单
      initMyList: false,
      //标签值
      selectTab: 'all',  
      defaultValue: {
        client_satus: ["WAITING_SPLIT_AUDIT", "IN_SPLIT_AUDIT"],
        goods_satus: ["WAITING_SPLIT_AUDIT", "IN_SPLIT_AUDIT"],
      },
      tools: [],
      role: ["other"],
      btns: [],
      list_btns: [
        {
          type: "primary",
          txt: "锁定",
          loading: false,
          click() {
            if(!self.selectRows || self.selectRows.length === 0) {
              self.$message.warning('请选择要锁定的订单！')
              return
            }
            this.loading = true
            lockingMoreAuditGoods(self.selectRows.map(item => {
              const { custom_goods_id,
                      client_number,
                      goods_id
                    } = item
              return {
                custom_goods_id,
                client_number,
                goods_id,
              }
            }), false, true).then(res => {
              this.loading = false
              if(res.result) {
                self.$refs.list && self.$refs.list._getDataList()
              }
            }).catch(() => {
              this.loading = false
            })
          }
        },
      ],
    };
  },
  methods: {
    // 选中行
    selectionChange(rows) {
      this.selectRows = rows
    },
    taggelClassName(row) {
    /**
     * @description: 设置行背景颜色
     * @param {*}
     * @return {*}
     */  
      let color = {
      // 红色
      '#F56C6C' : 'danger-row',
      // 蓝色
      '#409EFF' : 'primary-row',
      // 黄色
      '#E6A23C' : 'warning-row'
    }
      return row.color_code ? color[row.color_code] : ''
    },
    tabsChange(tab) {
    /**
     * @description: 标签页切换
     * @param {*}
     * @return {*}
     */  
      tab.name === 'myList' && (this.initMyList = true)
    },
    refresh() {
      this.$refs.list && this.$refs.list._getDataList();
      this.$refs.myList && this.$refs.myList._getDataList();
    },
    lock(d) {
      let self = this;
      let data = {};
      const { custom_goods_id, client_number, goods_id } = d;
      data = {
        custom_goods_id,
        client_number,
        goods_id,
      };
      !this.request && (this.request = {});
      if (this.request[custom_goods_id]) {
        this.$message({
          type: "warning",
          message: "请等待上一次请求结束",
        });
        return;
      }
      this.request[custom_goods_id] = true;
      this.ajax.postStream(
        "/custom-web/api/splitAudit/lockingGoods",
        data,
        (res) => {
          res = res.body;
          this.$message({
            message: res.msg,
            type: res.result ? "success" : "error",
          });
          if (res.result) {
            self.$refs.list._getDataList();
            this.request[custom_goods_id] = false;
          } else {
            this.request[custom_goods_id] = false;
          }
        }
      );
    },
    canLock(d) {
      let self = this
      if (self.role.indexOf("DZ_CSY") == -1) {
        return false;
      } else if (
        d.goods_status_value == "WAITING_SPLIT_AUDIT" ||
        d.goods_status_value == "IN_SPLIT_AUDIT"
      ) {
        if (d.is_locking === null) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
  },
  created() {
    // cs 拆审
    Object.assign(this.params, { pageInfo: "cs" });
    this.params.pageInfo = "cs";
  },
  async mounted() {
    var self = this;
    this.role = await getRole();
    (this.tools = [
      {
        type: "primary",
        txt: "锁定",
        click(d) {
          self.lock(d);
        },
        show(d) {
          return self.canLock(d)
        },
      },
      {
        type: "warning",
        txt: "拆审",
        click: (d) => {
          this.$root.eventHandle.$emit("creatTab", {
            name: "拆审",
            component: () =>
              import("@components/dz_customer/alert/verifypic.vue"),
            params: {
              goodsInfo: {
                goods_id: d.goods_id,
                custom_goods_id: d.custom_goods_id,
              },
              verType: "cs",
              trade_type: this.trade_type,
            },
          });
        },
        show(d) {
          if (self.role.indexOf("DZ_CSY") == -1) {
            return false;
          } else if (
            d.goods_status_value == "WAITING_SPLIT_AUDIT" ||
            d.goods_status_value == "IN_SPLIT_AUDIT" ||
            d.goods_status_value === 'REJECT_PUSH_PURCHASE'
          ) {
            if (d.is_review === "true") {
              return true;
            } else {
              return false;
            }
          } else {
            return false;
          }
        },
      },
    ]),
      this.$root.eventHandle.$on("refreshReviewList", this.refresh);
  },
  beforeDestroy() {
    this.$root.eventHandle.$off("refreshReviewList", this.refresh);
  },
};
</script>

<style>
</style>
