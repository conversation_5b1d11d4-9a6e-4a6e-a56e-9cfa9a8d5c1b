<!-- 定制⾦核销记录列表 -->
<template>
	<count-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:searchPage='search.page_name'
		:pageTotal='count'
		selection="checkbox"
		@page-size-change='pageSizeChange'
		@current-page-change='pageChange'
		@search-click='searchClick'
		@selection-change='selectionChange'
		:dynamic='true'
		countUrl="/order-web/api/scmsystrade/verify/count"
    	:showCount ='showCount'
		ref="xptList"
	></count-list>
</template>
<script>
import countList from '@components/common/list-count'
export default {
    props:['params'],
	components: {
        countList,
      },
	data() {
		return {
			showCount:false,
			list: [],
			btns: [{
					type: 'primary',
					txt: '刷新',
					click: () => this.getList(),
				}, {
					type: 'primary',
					txt: '导出',
					click: () => this.exportExcel(),
				}, {
					type: 'primary',
					txt: '导出结果',
					click: () => this.showExportList(),
				}, 
			],
			cols: [ {
				label: '创建时间',
				prop: 'createTime',
				format: 'dataFormat1',
				width: 150,
			}, {
				label: '原始店铺',
				prop: 'originalShopName',
				// redirectClick: d => this.openConsultation(d.consultion_id),
				width: 180
			}, {
				label: '客户昵称',
				prop: 'customerName',
				width: 180
			}, {
				label: '定制单号',
				prop: 'clientNumber',
			}, {
				label: '定制订单销售单号',
				prop: 'sysTradeNo',
			}, {
				label: '定制订单合并单号',
				prop: 'mergeTradeNo',
			}, {
				label: '定制订单发货状态',
				prop: 'deliveryStatusStr',
				width: 150,
			},  {
				label: '定制订单发货时间',
				prop: 'deliveryTime',
				format: 'dataFormat1',
				width: 150,
			}, {
				label: '定制⾦淘宝单号',
				prop: 'tid',
				width: 150,
			},{
				label: '定制⾦销售单号',
				prop: 'verifySysTradeNo',
				width: 100
			},{
				label: '定制⾦订单状态',
				prop: 'orderStatusStr',
				width: 100
			},{
				label: '定制⾦⾦额',
				prop: 'payAmount',
				width: 150
			},{
				label: '货款充值状态',
				prop: 'rechargeStatusStr',
				format: 'yesOrNo',
			},{
				label: '货款充值单号',
				prop: 'rechargeOrderNo',
				width: 150
			},{
				label: '权益名称',
				prop: 'equityName',
				width: 150
			},{
				label: '让利⾦额',
				prop: 'benefitAmount',
				width: 150
			},{
				label: '经销价',
				prop: 'dealerPrice',
				width: 150
			},{
				label: '标准售价',
				prop: 'standPrice',
				width: 150
			}],
			search: {
				page_no:1,
				page_size: this.pageSize,
				page_name: 'cloud_order_verify_record',
				where: []
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
		// 导出功能
      exportExcel() {
         

		this.ajax.postStream('/order-web/api/scmsystrade/verify/export', this.search, res => {
			this.$message({
			type: res.body.result ? 'success' : 'error',
			message: res.body.msg
			})
		}, err => {
			this.$message.error(err);
			this.exportBtnStatus = false;
		})
          
        
      
    },
		// 导出报表
      showExportList (exportType){
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/after_sales_report/export'),
          style:'width:900px;height:600px',
          title: '报表导出列表',
          params: {
            query: {
              type: 'EXCEL_TYPE_ORDER_VERIFY_RECORD_EXPORT',
            },
          },
        })
      },
		pageSizeChange(ps) {
			this.search.page_size = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page_no = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			let self = this;
			this.search.where = obj;
			new Promise((res,rej)=>{
					self.getList(resolve,res);
				}).then(()=>{
					if(this.search.page_no != 1){
						self.count = 0;
					}
					self.showCount = false;
				})
			},
		selectionChange(obj) {
			this.selectData = obj;
		},
		getList(resolve,callback) {
			this.ajax.postStream("/order-web/api/scmsystrade/verify/list", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content;
					// this.count = res.body.content.count;
					if(!this.showCount){
						let total = this.search.page_no * this.search.page_size
						this.count = res.body.content.length == (this.search.page_size+1)? total+1:total;
              			this.list = res.body.content.splice(0,res.body.content.length == (this.search.page_size+1)?total:res.body.content.length);

					}
				} else {
					this.$message.error(res.body.msg)
				}
				callback && callback();
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				callback && callback();
				resolve && resolve()
			});
		},
		
    

	},
	mounted() {
		// this.getList()
	},
}
</script>
<style module>
.unread-tag td:nth-child(2):after {
	content: '';
	position: absolute;
    right: 4px;
    top: 9px;
    border: 4px solid red;
    border-radius: 100%;
}
</style>
