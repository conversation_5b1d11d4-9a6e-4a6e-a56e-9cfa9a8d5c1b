<template>
<!-- 分配设计师 -->
	<div>
		<xpt-headbar>
			<el-button size='mini'  type="primary" @click="submit('customer')" slot='left' :disabled='customerBtnStatu' :loading='customerBtnStatu'>保存</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click="closeComponent" slot='right'>关闭</el-button>
		</xpt-headbar>
		<el-form label-position="right" label-width="100px" :model="customer" :rules="rules" ref="customer">
      <el-row :gutter='50'>
        <el-col :span='10'>
          <el-form-item label="客户号:" prop="client_number">
						<el-input v-model="customer.client_number" size='mini' :maxlength="30" disabled></el-input>
					</el-form-item>
          <el-form-item label="客户手机" prop="client_mobile">
						<el-input v-model="customer.client_mobile" size='mini' :maxlength="30" disabled></el-input>
					</el-form-item>
        </el-col>
        <el-col :span='10'>
          <el-form-item label="客户姓名" prop="client_name">
						<el-input v-model="customer.client_name" size='mini' :maxlength="30" disabled></el-input>
            <!-- <el-input v-model='customer.client_sex' size='mini' disabled> </el-input> -->
					</el-form-item>
          <el-form-item label="其他电话" prop="client_tel">
						<el-input v-model="customer.client_tel" size='mini' :maxlength="30" disabled></el-input>
					</el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='50'>
        <el-col :span='10'>
          <el-form-item label="订单类型" prop="client_type">
            <el-input v-model='customer.client_type' size='mini' disabled ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span='10'>
          <el-form-item label="送货方式" prop="deliver_method">
            <el-input v-model='customer.deliver_method' size='mini' disabled ></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row :gutter='50'>
        <el-col :span='10'>
          <el-form-item label="设计师" prop="designer">
             <el-select v-model='customer.designer' size='mini' :placeholder='placeholder'>
              <el-option
                v-for="item in designeropt"
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
                >
              </el-option>
            </el-select>
            <el-tooltip v-if='rules.designer[0].isShow' class="item" effect="dark" :content="rules.designer[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
				  </el-form-item>
        </el-col>
        <el-col :span='10'>
          <el-form-item label="希望量尺时间" prop="reserve_measure_date">
            <el-date-picker
              v-model="customer.reserve_measure_date"
              type="datetime"
              size='mini'
              placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm:ss"
              >
            </el-date-picker>
				  </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='50'>
        <el-col :span='20'>
          <el-form-item label="量尺空间" prop="measure_room">
            <el-checkbox-group v-model="customer.measure_room" disabled>
              <el-checkbox label="1" disabled>客餐厅</el-checkbox>
              <el-checkbox label="2" disabled>主卧房</el-checkbox>
              <el-checkbox label="3" disabled>客卧房</el-checkbox>
              <el-checkbox label="4" disabled>儿童房</el-checkbox>
              <el-checkbox label="5" disabled>书房</el-checkbox>
              <el-checkbox label="6" disabled>厨房</el-checkbox>
              <el-checkbox label="7" disabled>门厅</el-checkbox>
              <el-checkbox label="8" disabled>卫生间</el-checkbox>
              <el-checkbox label="9" disabled>衣帽间</el-checkbox>
              <el-checkbox label="10" disabled>阳台</el-checkbox>
              <el-checkbox label="11" disabled>杂物间</el-checkbox>
              <el-checkbox label="12" disabled>其他</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter='50'>
        <el-col :span='20'>
          <el-form-item label="客户地址" prop="client_address">
            <el-input v-model="customer.client_address" size='mini' disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span='10'>
          <el-form-item label="省市区">
            <select-address
              v-model="address"
              disabled="disabled"
            ></select-address>
          </el-form-item>
        </el-col>
      </el-row> -->
      <!-- <el-row :gutter='50'>
        <el-col :span='8'>
          <el-form-item label="楼盘名:" prop="loft_name">
            <el-input v-model="customer.loft_name" size='mini' :maxlength="30" disabled=""></el-input>
          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="GPS坐标:" prop="loft_coordinate">
            <el-input v-model="customer.loft_coordinate" size='mini' :maxlength="30" disabled=""></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='50'>
        <el-col :span='20'>
          <el-form-item label="备注" prop="remark">
            <el-input type='textarea' v-model="customer.remark" size='mini' style="width:80%" :maxlength='200' disabled resize="none"></el-input>
          </el-form-item>
        </el-col> 
      </el-row> -->
		</el-form>
	</div>
</template>
<script>   
  import validate from './common/validate'
  import fn from '@/common/Fn.js'
  import selectAddress from './components/selectAddress/selectAddress'
  import {getDeliverMethod} from './common/map'

  export default {
    components: {
      selectAddress
    },
    props:['params'],
    data(){
      var self = this
      return {
        rules: {
          designer: validate.isNotBlank({
            self:self,
            msg:'请选择设计师',
            trigger: 'change'
          }),
          reserve_measure_date: validate.isNotBlank({
            self:self,
            msg:'请选择希望量尺时间',
            trigger: 'change'
          }),
        },
        customer:{
          custom_client_id:'',
          client_number:'',//客户号
          client_name:'',//客户姓名
          client_sex:'',
          client_mobile:'',//客户手机
          client_tel:'',//其他电话
          client_type:'',//客户类型
          reserve_measure_date: '',//量尺时间
          measure_room:[],//量尺空间
          client_address:'',//客户地址
          loft_name:'',  //楼盘名
          loft_coordinate:'',//GPS坐标
          remark:'',//客户备注
          designer:'',
          deliver_method:''
        },
        address:[],
        designerList: [],
        code:this.params.code,
        customerList:[],
        loading:false,
        designeropt: [],
        placeholder:'请选择',
        customerBtnStatu: false
      }
    },
    methods:{
      closeComponent(){
        let self = this
        self.$root.eventHandle.$emit('removeTab',self.params.tabName)
      },
      createStateFilter(queryString) {
        return (state) => {
          return (state.value.indexOf(queryString.toLowerCase()) === 0)
        }
      },
      storeData(callback){
        var url = '/custom-web/api/customClient/assignDesignerToClient'
        var _this = this
        const client_number = _this.customer.client_number
        for(let i=0; i<_this.designerList.length; i++) {
          if(_this.customer.designer == _this.designerList[i].designer_number) {
            var checkedInfo = _this.designerList[i]
          }
        }
        const {designer_number, designer_name, designer_sex, designer_mobile, designer_qq, designer_email} = checkedInfo
        var data = {
          client_number,
          designer_number,
          designer_name,
          designer_sex,
          designer_mobile,
          designer_qq,
          designer_email
        }
        console.log(data)
        let tabName = data.tabName
        delete data.tabName
        this.customerBtnStatu = true
        this.ajax.postStream(url,data,(data) =>{
          data = data.body
          _this.$message({
            message: data.msg,
            type:data.result?'success':'error'
          })
          if(data.result){
            _this.isPass = true
            this.$root.eventHandle.$emit('refreshclientList')
          }
          callback&&callback()
          _this.closeComponent()
          _this.customerBtnStatu = false
        },function(data){
          console.log('失败的回调函数')
          _this.customerBtnStatu = false
        })
      },
      //保存数据
      submit(formName,callback){
        this.$refs[formName].validate((valid) => {
          if(!valid) return false
          this.storeData(callback)
        })
      },
      getCustomerDeatil(){
        //获取客户详情
        this.customer = this.params.customerInfo
        this.address[0] = this.params.customerInfo.receiver_state_code
        this.address[1] = this.params.customerInfo.receiver_city_code
        this.address[2] = this.params.customerInfo.receiver_district_code
        let measure_room = this.customer.measure_room || ''
        // this.customer.client_sex = (this.customer.client_sex == 1) ? "先生" : "小姐"
        this.customer.client_type = (this.customer.client_type == 1) ? "家具客户" : "全案客户"
        this.customer.measure_room = measure_room.split(',')
        this.customer.deliver_method = getDeliverMethod(this.params.customerInfo.deliver_method)
      },
      getDesignerList(){
        //获取设计师列表
        var _this = this
        var data = {}
        this.ajax.postStream('/custom-web/api/customClient/getDesignerListByShop',data,function(data){
          data = data.body
          if(data.result){
            _this.designerList = data.content 
            for(let i=0; i<data.content.length; i++){
              const designer_number = data.content[i].designer_number
              const designer_name =data.content[i].designer_name
              _this.designeropt.push({
                  label: designer_name,
                  value: designer_number
                })
            }
          }
        },
        function(data){
          console.log('返回失败的数据')
        })

      },

      initData(){//初使化数据
        this.getCustomerDeatil(),
        this.getDesignerList()
      },
    },

    mounted(){
      this.initData()
      this.params.__data = JSON.stringify(this._data)
      this.params.__close = this.closeComponent
    }
  }
</script>
