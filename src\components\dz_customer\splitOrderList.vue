<template>
<!-- 原始订单拆单列表 -->
  <div style="height: 99%;">
    <verifcommon
    ref="list"
    :params = params
    :btns="btns"
    :tools="tools"
    url="/custom-web/api/apratOrder/getReviewGoodsList"
    :defaultValue="defaultValue"
    :trade_type="trade_type"
    ></verifcommon>
  </div>
</template>

<script>
import verifcommon from './verifcommon'
import { getRole } from './common/api'

export default {
  components: {
    verifcommon
  },
  props: {
    params: {
      type: Object
    },
    trade_type: {
      type: String,
      default: 'ORIGINAL'
    }
  },
  data() {
    return {
      defaultValue: {
        client_satus: ['WAITING_SPLIT_ORDER','IN_SPLIT_ORDER'],
        goods_satus: ['WAITING_SPLIT_ORDER','IN_SPLIT_ORDER']
      },
      tools:[],
      role:["other"],
      btns: []
    }
  },
  methods:{
    refresh() {
      this.$refs.list._getDataList()
    },
    lock(d) {
        let self = this
        let data = {}
        const { custom_goods_id, client_number, goods_id } = d
        data = {
          custom_goods_id, client_number, goods_id
        } 
        !this.request && (this.request={})
        if(this.request[custom_goods_id]) {
          this.$message({
            type: 'warning',
            message: '请等待上一次请求结束'
          })
          return
        }
        this.request[custom_goods_id] = true
        this.ajax.postStream('/custom-web/api/apratOrder/lockingGoods', data, (res) => {
          res = res.body
          this.$message({
            message: res.msg,
            type:res.result?'success':'error'
          })
          if(res.result) {
            self.$refs.list._getDataList()
          } else {
            this.request[custom_goods_id] = false
          }
        })
      },
  },
  created(){
        // cd拆单
    this.params.pageInfo="cd"
  },
  async mounted() {
    var self = this
    this.role = await getRole()
    this.tools = [
      {
        type: 'primary',
        txt: '锁定',
        click(d) {
          self.lock(d)
        },
        show(d) {
          if(self.role.indexOf("DZ_CDY") == -1) {
            return false
          } else if(d.goods_status_value == 'WAITING_SPLIT_ORDER' || d.goods_status_value == 'IN_SPLIT_ORDER' || d.goods_status_value === 'REJECT_WAITING_SPLIT_ORDER') {
            if(d.is_locking === null ){
              return true
            } else {
                return false
              }
          } else{
            return false
          }
        }
      },
      {
        type: 'warning',
        txt: '拆单',
        click: (d) => {
          this.$root.eventHandle.$emit('creatTab', {
            name: '拆单',
            component: () => import('@components/dz_customer/alert/verifypic.vue'),
            params: {
              goodsInfo:{goods_id:d.goods_id, custom_goods_id:d.custom_goods_id},
              verType: "cd",
              trade_type: this.trade_type
            }
          })
        },
        show(d) {
          if(self.role.indexOf("DZ_CDY") == -1) {
            return false
          } else if(d.goods_status_value == 'WAITING_SPLIT_ORDER' || d.goods_status_value == 'IN_SPLIT_ORDER' || d.goods_status_value === 'REJECT_WAITING_SPLIT_ORDER') {
            if(d.is_review === 'true' ){
              return true
            } else {
                return false
              }
          } else{
            return false
          }
        }
      }
    ],
    this.$root.eventHandle.$on('refreshsplitOrderList', this.refresh)
  },
  beforeDestroy() {
    this.$root.eventHandle.$off('refreshsplitOrderList', this.refresh)
  }

}
</script>

<style>

</style>
