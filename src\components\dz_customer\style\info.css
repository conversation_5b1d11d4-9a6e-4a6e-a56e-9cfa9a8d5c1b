/* 详情 */
.info {
    position: absolute;
    top:0;
    left:0;
    width: 100%;
    height: 100%;
    overflow:auto;
    padding: 0 20px;
    padding-right: 163px !important;
}
.info-container {
    max-width: 1400px;
    margin: 0 auto;
    padding-bottom: 40px;
    line-height: 16px;
}

@media screen and (max-width: 1174px) {
    .info {
        padding-right: 0 !important;
    }
    .nav {
        display: none;
    }
}
.nav {
    position: fixed;
    right: 20px; 
    top: 100px;
    padding: 10px 10px;
    border-left: 2px solid #eee;
}
.nav a{
    padding: 10px 39px 10px 0;
    cursor: pointer;
    display: inline-block;
    color: #4f5f6f;
}
.nav a:hover {
    color: #409EFF;
}
.nav .child a {
    padding-left: 16px;
}