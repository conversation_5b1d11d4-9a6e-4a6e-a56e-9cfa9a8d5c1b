<template>
  <div class="searchBox">
    <div class="search-content">
      <form-create :formData="queryItems" @save="query" savetitle="查询" ref="formCreate"></form-create>
    </div>
    <dz-list :data='customList' :colData='colData' :pageTotal='pageTotal' :btns='btns'
      :tools='tools'
      @page-size-change='pageChange' @current-page-change='currentPageChange' orderNo
      ref='dzList'>
    </dz-list>
  </div>
</template>

<script>
  import fn from '@/common/Fn.js'
  import formCreate from './components/formCreate/formCreate'
  import dzList from './components/list/list'
  import { getClientStatus } from './common/map'
  import { client_status, cusTypeopt, getMap } from './common/clientDictionary'
  import { getRole, getShopInfo } from './common/api'

export default {
  components: {formCreate, dzList},
  props: ['params'],
  data() {
    let self = this
    return {
      colData: Object.freeze([{
          label: '店名',
          prop: 'shop_name',
          width: '100',
        }, {
          label: '补单号',
          prop: 'supplement_no',
          width: '70',
        }, {
          label: '原订单号',
          prop: 'original_order_no',
          width: '90'
        },{
          label: '客户名',
          prop: 'client_name',
          width: '90'
        },{
          label: '手机',
          prop: 'client_mobile',
          width: '60'
        },{
          label: '描述',
          prop: 'supplement_reason',
          width: '60'
        },{
          label: '建档日期',
          prop: 'create_date1',
          width: '60'
        },{
          label: '建档人',
          prop: 'create_name',
          width: '60'
        },{
          label: '约定交付日期',
          prop: 'promise_consign_date1',
          width: '60'
        },{
          label: '状态',
          prop: 'status',
          width: '120'
        },
      ]),
      btns: [
        {
          type: 'primary',
          txt: '补单',
          show: true,
          click:() =>{
            this.supplement()
          }
        }
        , {
          type: 'success',
          txt: '刷新',
          loading: false,
          show: true,
          click:() =>{
            this.refresh()
          }
        }
      ],
      tools: [
        {
          type: 'warning',
          txt: '修改',
          click(d) {
            self.$root.eventHandle.$emit('creatTab', {
              name: '修改补单信息',
              component: () => import('./newCustomInfo.vue'),
              params: {
                client_no: d.client_no
              }
            })
          },
          show(d) {
            return true
          }
        },
      ],
      pageNow: 1,
      pageSize: 20,
      pageTotal: 0,
      queryItems:[],
      customList:[],
      role:["other"],
      info:{},
      infoList:[],
      defaultValue: {
        supplement_status: ['WAITING_DISTRIBUTION_DESIGN','SIGNED_CONTRACT_WAITING_VERIFY'],
      },
      searchParam:{
        supplement_start_status: 'WAITING_DISTRIBUTION_DESIGN',
        supplement_end__status: 'SIGNED_CONTRACT_WAITING_VERIFY'
      },
      url:"/custom-web/api/customClient/getClientInfoList"
    }
  },
  methods: {
    async getInfo() {
        this.infoList = await getShopInfo()
      this.info = this.infoList.length? this.infoList[0]:{};

        this.getQueryItems()
      },
      getInfoNoShop() {
        this.getQueryItems()
      },
      refresh() {
        this.getDataList()
      },
      getQueryItems() {
        // let c_status = []
        let _self = this
        // getMap(map => {
        //   let cs = map.client_status.filter(item => true)
        //   cs = cs.slice(0,cs.length-1)
        //   cs.forEach(item => {
        //     c_status.push(item)
        //   })
        // })
        this.queryItems = [
          {
          cols:[
            {formType: 'myText', label: '专卖店', value: this.info.loginShopName, span: 24},
            {formType: 'elInput', prop: 'client_no', label: '客户号'},
            {formType: 'myInput', prop: 'client_mobile', label: '客户电话', type:'string', maxlength: 11, event:{
              input(v, col){
                col.value = v.replace(/\D/g, '')
              }
            }},
            {formType: 'elInput', prop: 'client_name', label: '客户名称'},
            {formType: 'elInput', prop: 'original_order_no', label: '原订单号'},
            {formType: 'elInput', prop: 'supplement_no', label: '补单号'},
            {formType: 'selectRange', prop: 'supplement_status',value: this.defaultValue.supplement_status, props:['supplement_start_status', 'supplement_end__status'], label: '补单状态', options:[supplement_status,supplement_status]},
            {formType: 'elInput', prop: 'designer_name', label: '设计师'},
            {formType: 'elSelect', prop: 'supplement_reason', label: '描述', options: supplement_reason},
            {formType: 'elDatePicker', prop: 'create_time', props:['start_query_date', 'end_query_date'], label: '建档日期', type: 'daterange', format: 'yyyy-MM-dd'},
            // {formType: 'elInput', prop: 'client_address', label: '客户地址'},
            // {formType: 'selectRange', prop: 'client_status',value: this.defaultValue.client_status, props:['client_start_status', 'client_end_status'], label: '订单状态', options:[c_status,c_status]},
          ]
        }
        ]
      },
    supplement() {
      this.$root.eventHandle.$emit('creatTab', {
        name: '补单',
        component: () => import('@components/dz_customer/newCustomInfo.vue')
      })
    },
    // 监听每页显示数更改事件
    pageChange(pageSize,param) {
      this.pageSize = pageSize
      Object.assign(this.searchParam, param)
      this.getDataList()
    },
    // 监听页数更改事件
    currentPageChange(page,param) {
      this.pageNow = page
      Object.assign(this.searchParam, param)
      this.getDataList()
    },
    // 查询所有客户列表
    query(param) {
      Object.assign(this.searchParam, param)
      this.getDataList()
    },
    getDataList() {
      var url = this.url
      let data = {}
      Object.assign(data, this.searchParam)
      data.page = {
        length: this.pageSize,
        pageNo: this.pageNow
      }
      let refreshBtn = this.btns.filter(item => item.txt === '刷新')[0]
      refreshBtn.loading = true
      this.ajax.postStream(url, data, (d) => {
        if (d.body.result) {
          let content = d.body.content || {}
          const listLength = _.get(content,'list.length')
          for(var i=0;i<listLength;i++){
            content.list[i].create_time1 = fn.dateFormat(content.list[i].create_time,'yyyy-MM-dd')
            content.list[i].status = content.list[i].status === "1" ? "生效" : "失效"
          }
          this.customList = content.list || []
          this.pageTotal = content.count || 0
          
        } else {
          this.$message({
            message: d.body.msg,
            type: 'error'
          })
        }
        refreshBtn.loading = false
      }, err => {
        this.$message.error(err)
        refreshBtn.loading = false
      }, this.params.tabName)
    }

  },
  async mounted() {
    this.role = await getRole()
    if(this.role.indexOf('DZ_SJS') != -1||this.role.indexOf('DZ_DG') != -1||
      this.role.indexOf('DZ_DZ') != -1||this.role.indexOf('DZ_SJZG') != -1
    ) {
      this.getInfo()
    } else {
      this.getInfoNoShop()
    }
    this.getDataList()
    this.$root.eventHandle.$on('supplementList', this.getDataList)
  },
  beforeDestroy() {
    this.$root.eventHandle.$off('supplementList', this.getDataList)
  }
}
</script>

<style scoped>
.searchBox {
  height: 99%;
  width: 80%;
  margin: 0px auto;
  display: flex;
  flex-direction: column;
}
.search-content {
  border: 1px #aaa solid;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
