<template>
	<div class="mgt10">
		<el-form label-position="right" label-width="95px" :model="submitData" :rules="rules" ref="submitData">
			<el-row	:gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="收货人" prop="receiver_name">
						<el-input size='mini' v-model="submitData.receiver_name" :disabled="ifDecrypt"></el-input>
						<el-tooltip v-if='rules.receiver_name[0].isShow' class="item" effect="dark" :content="rules.receiver_name[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="收货人固话" prop="receiver_phone">
						<el-input size='mini' v-model="submitData.receiver_phone" :disabled="ifDecrypt"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="收货人手机" prop="receiver_mobile">
						<el-input size='mini' v-model="submitData.receiver_mobile" :disabled="ifDecrypt"></el-input>
						<el-tooltip v-if='rules.receiver_mobile[0].isShow' class="item" effect="dark" :content="rules.receiver_mobile[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="40" class="mgt10">
				<el-col :span='8'>
					<el-form-item label="省" prop="receiver_state">
						<el-select placeholder="请选择" size='mini' filterable v-model="submitData.receiver_state" :disabled="ifDecrypt" @change="selectProvince">
						    <el-option
						      v-for="(value,key) in provinceObj"
						      :label="value"
						      :value="parseInt(key)"  :key='key'>
						    </el-option>
						</el-select>
						<el-tooltip v-if='rules.receiver_state[0].isShow' class="item" effect="dark" :content="rules.receiver_state[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="市" prop="receiver_city">
						<el-select placeholder="请选择" size='mini' filterable v-model="submitData.receiver_city" :disabled="ifDecrypt" @change="selectCity">
						    <el-option
						       v-for="(value,key) in cityObj"
						      :label="value"
						      :value="parseInt(key)" :key='key'>
						    </el-option>
						</el-select>
						<el-tooltip v-if='rules.receiver_city[0].isShow' class="item" effect="dark" :content="rules.receiver_city[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="区" prop="receiver_district">
						<el-select placeholder="请选择" size='mini' filterable v-model="submitData.receiver_district" :disabled="ifDecrypt" @change="selectArea">
						    <el-option
						       v-for="(value,key) in areaObj"
						      :label="value"
						      :value="parseInt(key)" :key='key'>
						    </el-option>
						</el-select>
						<el-tooltip v-if='rules.receiver_district[0].isShow' class="item" effect="dark" :content="rules.receiver_district[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row	:gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="街道">
						<el-select placeholder="请选择" size='mini' filterable v-model="submitData.receiver_street" :disabled="ifDecrypt" >
						    <el-option
						       v-for="(value,key) in streetObj"
						      :label="value"
						      :value="parseInt(key)" :key='key'>
						    </el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="详细地址" prop="receiver_address">
						<el-input size='mini' v-model="submitData.receiver_address" :disabled="ifDecrypt"></el-input>
						<el-tooltip v-if='rules.receiver_address[0].isShow' class="item" effect="dark" :content="rules.receiver_address[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="中转仓" prop="transit">
						<el-input size='mini' v-model="submitData.transit_store_name" icon="search" :on-icon-click="getTransit" readonly :disabled="ifDecrypt"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter='40' class="mgt10">
				<el-col :span='8'>
					<el-form-item label="送货方式" prop="deliver_method">
						<xpt-select-aux v-model="submitData.deliver_method" aux_name='deliver_method' ></xpt-select-aux>
						<!-- <el-select placeholder="请选择" size='mini' v-model="submitData.deliver_method">
						    <el-option
						      v-for="(item,index) in deliver_method_Options"
						      :label="item.label"
						      :value="item.value"
									:key='index'>
						    </el-option>
						</el-select> -->
						<el-tooltip v-if='rules.deliver_method[0].isShow' class="item" effect="dark" :content="rules.deliver_method[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>
				<el-col :span='8'>
					<el-form-item label="运费类型" prop="post_fee_type">
						<el-select placeholder="请选择" size='mini' v-model="submitData.post_fee_type">
						    <el-option
						      v-for="(item,index) in post_fee_type_Options"
						      :label="item.label"
						      :value="item.value"
									:key='index'>
						    </el-option>
						</el-select>
						<el-tooltip v-if='rules.post_fee_type[0].isShow' class="item" effect="dark" :content="rules.post_fee_type[0].message" placement="right-start" popper-class='xpt-form__error'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				</el-col>

			</el-row>
			<el-row :gutter='40' class="mgt10">
				<el-col :span="24" style="text-align:right;">
					<el-button type='success' size='mini' @click="preSave('submitData')">保存</el-button >
					<el-button type='warning' size='mini' @click="closeWindow">取消</el-button>
				</el-col>
			</el-row>
		</el-form>
	</div>
</template>
<script>
import validate from '@common/validate.js'
export default {
	props:["params"],
	data(){
		var self = this;
		return {
			provinceObj:{},
			cityObj:{},
			areaObj:{},
			streetObj:{},
			ifDecrypt:false,
			submitData:{
				cust_id:self.params.cust_id,
				receiver_name:"",
				receiver_phone:"",
				receiver_mobile:"",
				receiver_state:"",
				receiver_city:"",
				receiver_district:"",
				receiver_street:"",
				receiver_address:"",
				deliver_method:'',
				post_fee_type:'NOW_PAY',
				transit_store_name:'',
				transit_store_id:'',
			},
			// ifTransit:true,
			rules:{
				receiver_name:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请输入收货人姓名",
					trigger:"change"
				}),
				deliver_method:validate.isNotBlank({
					required:true,
					self:self,
					msg:'请选择送货方式',
					trigger:'change'
				}),
				post_fee_type:validate.isNotBlank({
					required:true,
					self:self,
					msg:'请选择运费类型',
					trigger:'change'
				}),
				receiver_mobile:[{
					required: true ,
					message:  '请填写有效的手机号',
					isShow: false,
					trigger:'blur',
					validator: function(rule, value, callback) {
						
						
						var reg3 = /^\d*$/;//纯数据
						var validateIsPass = false;
						 if(self.ifDecrypt){
							 validateIsPass =true;
						 }else{
							 validateIsPass = value ? reg3.test(value) : self.rules[rule.field][0].required ? false : true;

						 }
						
						self.rules[rule.field][0].isShow = !validateIsPass;
						// validateIsPass?callback():callback(new Error(''));
						if (validateIsPass) {
							callback();
						} else {
							callback(new Error(''));
						}
					}
				}],
				receiver_state:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请选择所在省",
					trigger:"change"
				}),
				receiver_city:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请选择所在市",
					trigger:"change"
				}),
				receiver_district:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请选择所在区",
					trigger:"change"
				}),
				receiver_address:validate.isNotBlank({
					required:true,
					self:self,
					msg:"请输入详细地址",
					trigger:"change"
				})
			},
			deliver_method_Options:[
				{
					value:'THREES',
					label:'三包'
				},{
					value:'LOGISTICS',
					label:'物流'
				},{
					value:'PICK_UP',
					label:'仓库自提'
				},{
					value:'ZTO',
					label:'中通小件'
				},{
					value:'ZTO_FREE',
					label:'中通免费'
				},{
					value:'SF',
					label:'顺丰'
				},{
					value:'TRANSIT',
					label:'中转仓发货'
				}
			],
			post_fee_type_Options:[
				{
					value:'NOW_PAY',
					label:'现付'
				},{
					value:'ARRIVE_PAY',
					label:'到付'
				}
			],
			addressNums:{
				stateNum:'',
				cityNum:'',
				districtNum:'',
				streetNum:''
			}
		}
	},
	methods:{
		closeWindow(){
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		// 地区
		getAreaCode(code,key){
			var self = this;
			var url = "/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId";
			if(!code) return;
			self.ajax.postStream(url,this.htmlTransferred(code),function(response){
				if(response.body.result){
					self[key] = response.body.content
				}
			})
		},
		selectProvince(address){//选择省份，获得市级信息
			var self = this
			if(!address) return
			/**
			 * 非复制状态
			 * or
			 * 复制状态重新选择
			 */
			if(!self.params.ifCopy||((typeof address !== 'string')&&self.params.addressObj.receiver_state!==address)){
				self.getAreaCode(self.submitData.receiver_state,"cityObj")
				self.submitData.receiver_city = ''
				self.submitData.receiver_district = ''
				self.submitData.receiver_street = ''
			}
		},
		selectCity(address){//选择市级，获得区级信息
			if(!address) return
			var self = this;
			if(!self.params.ifCopy||((typeof address !== 'string')&&self.params.addressObj.receiver_city!==address)){
				self.getAreaCode(self.submitData.receiver_city,"areaObj")
				self.submitData.receiver_district = ''
				self.submitData.receiver_street = ''
			}
		},
		selectArea(address){//选择区级，获得街道信息
			if(!address) return
			var self = this
			if(!self.params.ifCopy||((typeof address !== 'string')&&self.params.addressObj.receiver_district!==address)){
				self.getAreaCode(self.submitData.receiver_district,"streetObj")
				self.submitData.receiver_street = ''
			}
		},
		/*
		添加地址做保存时校验不允许收货人存在先生小姐等字符。
		*/
		preSave(formName){
			var self = this
			self.$refs[formName].validate((valid) => {
				if(!valid) return
				let receiver_name = this.submitData.receiver_name;
				// if(receiver_name.indexOf('先生') > -1 || receiver_name.indexOf('小姐') > -1) {
				// 	this.$message.error('收货人不允许包含“先生”、“小姐”字符');
				// 	return;
				// }
				self.save()
			});
		},
		save(){
			new Promise(resolve => {
				this.checkDeliverMethod(resolve);
			}).then(() => {
				var self = this;
				if(self.submitData.deliver_method == 'TRANSIT'){
					if(self.submitData.transit_store_name == ''){
							self.$message.error('请选择中转仓')
							return false;
					}
				}
				self.submitData.receiver_state_name = self.provinceObj[self.submitData.receiver_state]||self.submitData.receiver_state
				self.submitData.receiver_city_name = self.cityObj[self.submitData.receiver_city]||self.submitData.receiver_city
				self.submitData.receiver_district_name = self.areaObj[self.submitData.receiver_district]||self.submitData.receiver_district
				self.submitData.receiver_street_name = self.streetObj[self.submitData.receiver_street]||self.submitData.receiver_street
				if(self.params.ifCallback){//客户模块地址新增，复制新增
					self.params.callback(self.submitData)
					self.$root.eventHandle.$emit('removeAlert',self.params.alertId)
				}
				else{	//合并订单模块地址新增，复制新增
					var url = "/order-web/api/customer/receiverInfo/addReceriverInfo";
					self.ajax.postStream(url,self.submitData,function(response){
						if(response.body.result){
              				self.params.callback();
							self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
							self.$root.eventHandle.$emit("close_addAddress");
						}else{
							self.$message.error(response.body.msg || '')
						}
					});
				}
			})
		},
		// 校验送货方式是否匹配
		checkDeliverMethod(resolve) {
			this.ajax.postStream('/order-web/api/customer/receiverInfo/validateReceiveInfoDeliverMethod', {
				deliver_method: this.submitData.deliver_method,
				receiver_state: this.submitData.receiver_state,
				receiver_city: this.submitData.receiver_city,
				receiver_street: this.submitData.receiver_street,
				receiver_district: this.submitData.receiver_district
			}, res => {
				console.log(res)
				if(res.body.result) {
					resolve();
				} else {
					this.$message.error(res.body.msg || '');
				}
			}, err => {
				this.$message.error(err);
			})
		},
		// 获取中转仓
		getTransit(){

			let self = this;
			if(self.submitData.deliver_method != 'TRANSIT'){
				self.$message.error('送货方式请选择中转仓发货');
				return false;
			}
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/order/addTransit'),
				style:'width:900px;height:600px',
				title:'获取中转仓',
				params: {
					close: d => {
						// this.submit.cust_name = d.name;
						// this.submit.cust_id = d.cust_id;
						self.submitData.transit_store_id = d.id;
						self.submitData.transit_store_name  = d.transit_store_name;
						console.log(d)
					}
				},
			})
		},
		ifDecryption(){
			// console.log(row);
			let self = this;
			let data ={
				"addressId": self.params.addressObj.fid,
			  }
			this.ajax.postStream("/kingdee-web/api/end/ifDecryption", data	, res => {
				var obj = res.body;
				// console.log(obj)
				if(obj.result){
				
					// callback && callback(obj);
					if(!!obj.content){ 
						self.ifDecrypt = true;
						if(self.params.addressObj.info_from  =="TAOBAO"){
							self.submitData.source_info_encryption = self.params.addressObj.info_encryption
						}
					}
				}else{
					// params.callback(false,obj.msg);
				}
			});
		},
	},
	mounted:function(){
		var self = this;
		var patrn = /[*]/; 

		
		if(self.params.ifCopy){//判断是否复制新增
			
				// self.ifDecrypt = true;
			self.ifDecryption();
			
			var url = '/order-web/api/customer/areaInfo/getAreaInfoByAreaParentId'
			self.submitData.receiver_name = self.params.addressObj.receiver_name
			self.submitData.receiver_phone = self.params.addressObj.receiver_phone
			self.submitData.receiver_mobile = self.params.addressObj.receiver_mobile
			self.submitData.receiver_address = self.params.addressObj.receiver_address
			self.submitData.post_fee_type = self.params.addressObj.post_fee_type
			self.submitData.deliver_method = self.params.addressObj.deliver_method
			self.submitData.tid = self.params.addressObj.tid
			self.submitData.info_from = self.params.addressObj.info_from

			
			console.log(self.params.addressObj)
			if(patrn.test(self.params.addressObj.decryptionAddress)){
				self.submitData.receiver_name = self.params.addressObj.decryptionName
				self.submitData.receiver_phone = self.params.addressObj.decryptionPhone
				self.submitData.receiver_mobile = self.params.addressObj.decryptionMobile
				self.submitData.receiver_address = self.params.addressObj.decryptionAddress

			}
      		new Promise((resolve,reject)=>{//请求省份信息，赋值省份编码
				self.ajax.postStream(url,1,function(response){
					if(response.body.result){
						self.provinceObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_state=self.params.addressObj.receiver_state
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_state) return;
			new Promise((resolve,reject)=>{//根据省份信息获得市级信息，赋值市级编码
				self.ajax.postStream(url,self.params.addressObj.receiver_state,function(response){
					if(response.body.result){
						self.cityObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_city=self.params.addressObj.receiver_city
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_city) return;
			new Promise((resolve,reject)=>{
				//根据市级信息获得区级信息，赋值区级编码
				self.ajax.postStream(url,self.params.addressObj.receiver_city,function(response){
					if(response.body.result){
						self.areaObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_district=self.params.addressObj.receiver_district
			}).catch(error=>{
				self.$message.error(error)
			})

			if(!self.params.addressObj.receiver_district) return;
			new Promise((resolve,reject)=>{//根据区级信息获得街道信息，赋值街道编码
				self.ajax.postStream(url,self.params.addressObj.receiver_district,function(response){
					if(response.body.result){
						self.streetObj = response.body.content
						resolve(response)
					}
					else reject(response.body.msg)
				})
			}).then(v=>{
				self.submitData.receiver_street=self.params.addressObj.receiver_street
			}).catch(error=>{
				self.$message.error(error)
			})
		} else {
			self.getAreaCode(1,"provinceObj");//新增
		}
	},
	destroyed(){
	}
}
</script>
