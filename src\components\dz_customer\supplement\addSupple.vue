<!--新建编辑补单-->
<template>
    <div style="display:flex;height:99%;flex-direction:column;"
        v-loading="sysLoading"
        element-loading-text="数据同步中"
    >
        <xpt-headbar >
            <template v-for='(btn,index) in btns_save'  slot='left'>
                <el-button
                :key="index"
                :type='btn.type'
                size='mini'
                @click.stop='e => btn.click&&btn.click(e)'
                :disabled="
                                typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled
                                ||btn.loading
                                ||false
                            "
                :loading="btn.loading||false"
                >{{btn.txt}}
                </el-button>
            </template>
        </xpt-headbar>
        <div>
            <form-create
                ref="formCreate"
                :formData="formData"
                :btns="false" 
                :ruleData="ruleData"
                @save="save"
                @inited="inited"
            ></form-create>
            <xpt-headbar >
                <template v-for='(btn,index) in btns_tool'  slot='left'>
                    <el-button
                    :key="index"
                    :type='btn.type'
                    size='mini'
                    @click.stop='e => btn.click&&btn.click(e)'
                    :disabled="
                                    typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled
                                    ||btn.loading
                                    ||false
                                "
                    :loading="btn.loading||false"
                    >{{btn.txt}}
                    </el-button>
                </template>
            </xpt-headbar>
        </div>
        <div style="flex:1;overflow:hidden">
            <el-table
                :data="tableData"
                border
                @selection-change='checkBoxSelect'
                style="width: 100%">
                <el-table-column 
                    type="selection"
                    width="40"
                    >
                </el-table-column>
                
                <el-table-column
                    label="补单商品号"
                    width="280">
                    <template slot-scope="scope">
                        <!-- 点击跳转 -->
                        <a  href='javascript:;' @click="redirectGoodsId('SUPPLY',scope.row.goods_id)"><span
                        v-tooltip='scope.row.goods_id'>{{ scope.row.goods_id }}</span></a>
                    </template>
                </el-table-column>
                <el-table-column
                    label="原商品号"
                    width="240">
                    <template slot-scope="scope">
                        <!-- 点击跳转 -->
                        <a  href='javascript:;' @click="redirectGoodsId('ORIGINAL',scope.row.original_goods_id)"><span
                        v-tooltip='scope.row.original_goods_id'>{{ scope.row.original_goods_id }}</span></a>
                    </template>
                </el-table-column>
                <el-table-column
                    label="三维家商品编号"
                    prop="swj_goods_no" 
                    width="100">

                </el-table-column>
                <el-table-column
                    label="原商品名称"
                    prop="original_message" 
                    width="100">
                </el-table-column>
                <!-- <el-table-column
                    label="长度(mm)"
                    width="100">
                    <template slot-scope="scope">
                        <el-input size='mini' @blur="valid(scope.row, 'length', '长度')" v-model="scope.row.length" placeholder="长度"></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    label="宽度(mm)"
                    width="100">
                    <template slot-scope="scope">
                        <el-input size='mini' @blur="valid(scope.row, 'width', '宽度')" v-model="scope.row.width" placeholder="宽度"></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    label="高度(mm)"
                    width="100">
                    <template slot-scope="scope">
                        <el-input size='mini' @blur="valid(scope.row, 'height', '高度')" v-model="scope.row.height" placeholder="高度"></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    label="建议零售价(元)"
                    width="100">
                    <template slot-scope="scope">
                        <el-input size='mini' @blur="valid(scope.row, 'retail_price', '建议零售价')" v-model="scope.row.retail_price" placeholder="建议零售价"></el-input>
                    </template>
                </el-table-column> -->
                <el-table-column
                    label="状态"
                    width="100">
                    <template slot-scope="scope">
                       {{getStatus(scope.row.goods_status)}}
                       <!-- <el-select placeholder="请选择" v-model="scope.row.goods_status" size='mini'  disabled>
                        <el-option
                            v-for="(value,key) in goods_status_supply"
                            :key="key"
                            :label="value.label"
                            :value="value.value">
                        </el-option>
                        </el-select> -->
                    </template>
                </el-table-column>
                
                <el-table-column
                    label="补单商品名称"
                    width="300"
                    >
                    <template slot-scope="scope">
                        <el-input :maxlength="50" size='mini' v-model="scope.row.message" disabled></el-input>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    label="实际售价(元)"
                    width="120">
                    <template slot-scope="scope">
                        <el-input size='mini' @blur="valid(scope.row, 'act_price', '实际售价')" v-model="scope.row.act_price" type='number'></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    label="描述"
                    width="120">
                    <template slot-scope="scope">
                        <el-select size='mini' v-model="scope.row.supply_reason" >
                            <el-option 
                                v-for="(option, optionIndex) in supply_reason"
                                :key="optionIndex"
                                :label="option.label" 
                                :value="option.value"></el-option>
                        </el-select>
                    </template>
                </el-table-column> -->
                
                <el-table-column label="操作" >
                <template slot-scope="scope">
                    <!-- <el-button
                    size="mini"
                    type="primary"
                    @click="handleUpload(scope.$index, scope.row)">上传</el-button> -->
                    <el-button
                    size="mini"
                    type="success"
                    @click="handleView(scope.$index, scope.row)">查看</el-button>
                    <el-button
                    size="mini"
                    type="success"
                    @click="problemDetail(scope.$index, scope.row)">问题</el-button>
                    <!-- <el-button
                    size="mini"
                    type="success"
                    v-if="bdh"
                    @click="swj(scope.row)">三维家设计</el-button> -->
                    <el-button
                    size="mini"
                    type="success"
                    v-if="bdh"
                    @click="openGoodsDesign(scope.row)">修改商品</el-button>
                    <!-- <el-button
                    size="mini"
                    type="success"
                    v-if="bdh"
                    @click="sysGoods(scope.row)">同步</el-button> -->
                    <el-tooltip 
                        v-if="scope.row.isShow"
                        class="item" 
                        effect="dark" 
                        style="color: #ff4949;"
                        content="请上传文件" 
                        placement="right-start" 
                        popper-class='xpt-form__error'>
                        <i class='el-icon-warning'></i>
                    </el-tooltip>
                </template>
                </el-table-column>
            </el-table>
    
        </div>
    </div>
</template>
<script>
import formCreate from '../components/formCreate/formCreate'
import { imageAdmin, imageView } from '../alert/alert'
import { supply_reason } from '../common/dictionary/supplyDictionary'
import { bjd_deliver_method } from '../common/dictionary/supplyDeliverDictionary'
import { openGoodsDesign } from "../common/api"
import {popup} from '../alert/alert'
import {addSupple, getSupple, updateSupple,createSwjSupplyGoodsOrder, submitSupple, createIds, verifySupple, getOrderInfo, synSwjGoodsMsg, openSupplySwj} from '../common/api'
import closeComponent from '../common/mixins/closeComponent'
import { goods_status_supply } from '../common/goodsStatusDictionary'
export default {
    mixins: [closeComponent],
    components: {
        formCreate
    },
    data() {
        return {
            problemIndex:{},
            goods_status_supply:[],
            sysLoading: false,
            bdh: '', //补单号
            tableData: [],
            formData: [],
            supply_reason: supply_reason,
            btns_save: [
                {
                    type: 'primary',
                    txt: '保存',
                    key: 'save',
                    loading: false,
                    disabled: false,
                    click:() =>{
                        this.btnType = 'save'
                        this.$refs.formCreate.save()
                    }
                },
                {
                    type: 'primary',
                    txt: '同步模型',
                    key: 'synchronous',
                    loading: false,
                    disabled: false,
                    click:() =>{
                        this.btnType = 'synchronous'
                        this.$refs.formCreate.save()
                    }
                },
                {
                    type: 'primary',
                    txt: '提交',
                    key: 'submit',
                    loading: false,
                    disabled: false,
                    click:() =>{
                        this.btnType = 'submit'
                        this.$refs.formCreate.save()
                    }
                },
                
                {
                    type: 'success',
                    txt: '提审',
                    key: 'shen',
                    loading: false,
                    disabled: false,
                    show: true,
                    click:() =>{
                        // if(!this.selectRows.length) {
                        //     this.$message.error('请选择需要提审的商品')
                        //     return
                        // }
                        let list = [];
                        this.tableData.forEach(item=>{
                                list.push(item.custom_goods_id)
                        })
                     
                        // if(this.selectRows.filter(item => item.goods_status === 'REJECT_VERIFY').length === this.selectRows.length) {
                            // 提审
                            this.resetBtnStatus({
                                save: {disabled: true},
                                shen: {disabled: true}
                            })
                            verifySupple(list, false, true).then(body => {
                                this.resetBtnStatus({
                                    save: {disabled: false},
                                    shen: {disabled: false}
                                })
                                if(body.result) {
                                    this.$root.eventHandle.$emit('addSupply')
                                    this.$root.eventHandle.$emit('removeTab',this.params.tabName)
                                }
                            })
                        // } else {
                            // this.$message.error('只有被驳回的商品才能重新提审')
                        // }
                    }
                }
            ],
            btns_tool: [
                {
                    type: 'primary',
                    txt: '新增',
                    key: 'add',
                    loading: false,
                    click: this.add
                },
                {
                    type: 'danger',
                    txt: '删除',
                    key: 'del',
                    disabled(){
                        return false
                    },
                    loading: false,
                    click: this.del
                },
                {
                    type: 'primary',
                    txt: '补单模板下载',
                    key: 'del',
                    loading: false,
                    click: this.downloadModel
                }
            ],
            ruleData: {
                promise_consign_date: {
                    validate: 'notLtCurrentTime', required: false
                },
                supply_transport_type: {required: true, message: '请选择货运方式', trigger: 'change'},
                address_id: {required: true, message: '请选择地址', trigger: 'change'},
                original_client_number: {required: true, message: '请选择关联订单', trigger: 'change'},
            }
        }
    },
    props: {
        params: {
            type: Object
        }
    },
    async created() {
        this.selectRows = []
        if(this.params.row.if_disallow === 'true') {
            // 状态为驳回隐藏新增和删除显示提审
            this.resetBtnStatus({
                add: {disabled: false},
                del: {disabled: false},
                submit: {disabled: true},
                shen: {disabled: false}
            })
        } else {
            // 否则隐藏提审按钮
            this.resetBtnStatus({
                shen: {disabled: true}
            })
        }
        this.bdh = this.params.client_number || ''
        this.supplyInfo = {}
        if(this.bdh) {
            // 修改补单
            // let supply = await getSupple(this.bdh)
            // this.supplyInfo = supply.content || {}
            // this.tableData = this.supplyInfo.goodsList
            await this.getSupple()
        }
        // 设置表单
        this.setFormData()
    },
    mounted(){
        this.goods_status_supply = goods_status_supply;
    },
    methods: {
        getStatus(status){
            let label = ''
            goods_status_supply.forEach(item=>{
                if(item.value == status){
                    label = item.label;
                }
            })
            return label
        },
        openGoodsDesign(d){
            if(!d.swj_goods_id){
                this.$message.error('请点击【同步模型】按钮，同步模型后再进行修改。');
                return;
            }
            openGoodsDesign({
              swj_goods_id: d.swj_goods_id
            }).then(res => {
              if(res.result) {
                  window.open(res.content)
              }
            })
        },
        
        async getSupple() {
        /**
         * @description: 获取补单详情
         * @param {*}
         * @return {*}
         */    
            if(this.bdh) {
                // 修改补单
                let supply = await getSupple(this.bdh)
                this.supplyInfo = supply.content || {},
                this.tableData = this.supplyInfo.goodsList
                // this.setFormData()
            }
        },
        downloadModel(){
        var self=this;
        self.ajax.postStream("/reports-web/api/template/list",{"page_name":"template_download_list","where":[{"field":"1e08dba31f3647691c61c2030bd8e632","table":"d331c120edc028bbbc83230f04b409fa","value":"林氏定制售后订单申请表","operator":"=","condition":"AND","listWhere":[]}],"page_size":50,"page_no":1},function(response){
            if(response.body.result && response.body.content.count != 0){
              self.$message({message: '操作成功',type: 'success'})
              window.open(response.body.content.list[0].url);
            }else{
              self.$message.error('未找到对应模板，请到模板下载列表维护')
            }
          },e=>{
            self.$message.error(e);
        })
      },
        sysGoods(row) {
            /**
             * @description: 同步三维家商品数据
             * @param {*}
             * @return {*}
             */  
            const {
                swj_goods_id,
                goods_id,
                custom_goods_id
            } = row
            this.sysLoading = true
            synSwjGoodsMsg({
                swj_goods_id,
                goods_id,
                custom_goods_id
            }, false, true).then(res => {
                this.sysLoading = false
                if(res.result) {
                    // 同步成功,更新数据
                    this.getSupple()
                }
            }).catch(() => {
                this.sysLoading = false
            })
        },
        async swj(row) {
            /**
             * @description: 三维家设计
             * @param {*}
             * @return {*}
             */
            if(!this.bdh) {
                this.$message.warning('请先保存补单')
                return
            }
            const design_id = this.supplyInfo ? this.supplyInfo.design_id : ''
            let swj_goods_id = row.swj_goods_id
            this.bdh && openSupplySwj({
                client_number: this.bdh,
                design_id,
                swj_goods_id
            }).then(res => {
                if(res.result) {
                    window.open(res.content)
                }
            })
        },
        async setAddress(data, getItem) {
            let self = this;
            // 设置收件信息
            let [
                rec_name,
                rec_phone,
                rec_address,
                address_id,
            ] = getItem(['rec_name', 'rec_phone', 'rec_address','address_id'])
            if(data.client_number && !data.receiver_name) {
                const clientInfo = await getOrderInfo(data.client_number)
                rec_phone.value = clientInfo.client_mobile || '--'
                rec_name.value = clientInfo.receiver_name || '--'
                rec_address.value = clientInfo.address_name || '--'
                address_id.config.cust_id = clientInfo.cust_id || null
                return
            }
            rec_name.value = data.receiver_name || '--'
            rec_phone.value = data.receiver_mobile || '--'
            rec_address.value = data.address_name || '--'
        },
        saveComponent() {
            this.btnType = 'save'
            this.$refs.formCreate.save();
            this.$root.eventHandle.$emit('removeTab',this.params.tabName)
            
        },
        redirectGoodsId(type,goods_id) {
            let self = this;
            this.$root.eventHandle.$emit('creatTab', {
                name: '商品详情',
                component: () => import('@components/dz_customer/goodsInfo/goodsInfo.vue'),
                params: {
                    trade_type:type,
                    goodsInfo: {goods_id:goods_id,client_number:type =='SUPPLY'?self.bdh:self.bdh.substring(0,self.bdh.length-3)}
                }
            })
        },
        inited(getItem) {
            // 设置默认状态
            if(this.bdh && this.supplyInfo) {
                this.orderChange(Object.assign({}, this.supplyInfo, {client_number: this.supplyInfo.original_client_number}), getItem)
            }
        },
        add() {
            // 新增
            if(!this.original_client_number) {
                this.$message.warning('请先选择关联订单')
                return
            }
            popup.getSupplyList(this, (result) => {
                this.addData(result)
            }, {multiple: true, initParam: {
                client_number: this.original_client_number
            }})
        },
        del() {
            if(!this.selectRows.length) {
                this.$message.warning('请选择需要删除的商品')
                return
            }

            let status = false
            this.selectRows.forEach(item=>{
                if(!['REJECT_VERIFY','IN_DESIGN',''].includes(item.goods_status)) {
                    status = true
                }
            })
            if(status){
                this.$message.warning('存在非拆单驳回或者设计中的商品，请重新选择')
                return;
            }
            console.log(status)
            this.$confirm('您确定要删除补单商品吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData = this.tableData.filter(item => {
                    // custom_goods_id是唯一的
                    return this.selectRows.findIndex(el => el.custom_goods_id === item.custom_goods_id) === -1
                })
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            })
        },
        checkBoxSelect(rows) {
            // 选择数据
            this.selectRows = rows
        },
        save(data) {
            // 保存
            if(!this.valid()) {
               return 
            }
            // 判断空间是否有上传附件
            // this.validFileNull()
            
            // let noFileIndex = this.tableData.findIndex(item => item.is_add && !item.imgs)
            // if(noFileIndex !== -1) {
            //     this.$message.error(`请上传空间文件`)
            //     return
            // }
            if(this.btnType === 'submit') {
                // 提交
                let flag = false
                this.tableData.forEach(item=>{
                    if(item.goods_status ==  "REJECT_VERIFY"){
                        flag = true;
                    }
                })
                if(flag){
                    this.$message.error('该补单存在拆单驳回商品，不能提交')
                    return;
                }
                this.resetBtnStatus({
                    save: {disabled: true},
                    submit: {loading: true}
                })
            } else {
                
                    //保存
                this.resetBtnStatus({
                    save: {loading: true},
                    submit: {disabled: true}
                })
                
               
            }
           
            
            this.tableData = this.tableData.map(item=>{
                delete item.imgs
                delete item.isShow
                delete item.is_add
                return item
            })
            if(this.bdh) {
                // 编辑更新
                return updateSupple({
                    custom_trade_id: this.supplyInfo.custom_trade_id,
                    client_number: this.bdh,
                    client_no: this.supplyInfo.client_no,
                    address_id: data.address_id,
                    supply_transport_type: data.supply_transport_type,
                    original_client_number: data.original_client_number,
                    promise_consign_date: data.promise_consign_date,
                    goodsList: this.tableData.map(item => {
                        return {
                            custom_goods_id: item.custom_goods_id,
                            goods_id: item.goods_id,
                            material_number: item.material_number,
                            original_goods_id: item.original_goods_id,
                            length: item.length,
                            width: item.width,
                            height: item.width,
                            retail_price: item.retail_price,
                            act_price: item.act_price,
                            supply_reason: item.supply_reason,
                            message: item.message

                        }
                    })
                }, false, true).then(body => {
                    if(this.btnType === 'save' || !body.result) {
                        if(this.params.row.if_disallow === 'true') {
                            // 状态为驳回隐藏新增和删除显示提审
                                this.resetBtnStatus({
                                    save: {disabled: false},
                                    submit: {disabled: true},
                                })
                            } else{
                                this.resetBtnStatus({
                                        save: {loading: false, disabled: false},
                                        submit: {loading: false, disabled: false}
                                    })
                            }
                        
                    }
                    if(body.result) {
                        // 新建成功
                        if(this.btnType === 'save') {
                            this.$root.eventHandle.$emit('addSupply')
                            this.getSupple()
                            // this.$root.eventHandle.$emit('removeTab',this.params.tabName)
                        } else if(this.btnType === 'submit'){
                            this.submit(body)
                        }else{
                            this.createSwjSupplyGoodsOrder(body)
                        }
                        if(this.btnType === 'problem'){
                            this.problemcallBack(this.problemIndex);
                        }
                        
                    }
                })
            } else {
                // 新建
                let tableData = JSON.parse(JSON.stringify(this.tableData))
                return addSupple({
                    client_no: this.client_no,
                    address_id: data.address_id,
                    supply_transport_type: data.supply_transport_type,
                    original_client_number: data.original_client_number,
                    promise_consign_date: data.promise_consign_date,
                    goodsList: tableData.map(item=>{
                        delete item.original_message
                        return item;
                    })
                }, false, true).then(body => {
                    if(this.btnType === 'save' || !body.result) {
                        this.resetBtnStatus({
                            save: {loading: false, disabled: false},
                            submit: {loading: false, disabled: false}
                        })
                    }
                    if(body.result) {
                        // 新建成功
                        this.bdh = body.content
                        if(this.btnType === 'save') {
                            this.getSupple()
                            let number = this.$refs.formCreate.getItem('bdh');
                            number.value = this.bdh;
                            this.$root.eventHandle.$emit('addSupply')
                            // this.$root.eventHandle.$emit('removeTab',this.params.tabName)
                        } else if(this.btnType === 'submit'){
                            this.submit(body)
                        }else{
                            this.createSwjSupplyGoodsOrder(body)
                        }
                        if(this.btnType === 'problem'){
                            this.problemcallBack(this.problemIndex);
                        }
                    }
                })
            }
        },
        
        createSwjSupplyGoodsOrder(body) {

            let self = this;
            // 同步数据
            createSwjSupplyGoodsOrder(this.bdh, false, true).then(body => {
                let flag = true;
                this.tableData.forEach(item=>{
                    if(item.goods_status_code&&item.goods_status_code >= 45){
                        flag = false
                    }
                })
                if(!flag){
                    this.resetBtnStatus({
                        save: {loading: false, disabled: false},
                        submit: {loading: false, disabled: true}
                    })
                }else{
                    this.resetBtnStatus({
                    save: {loading: false, disabled: false},
                    submit: {loading: false, disabled: false}
                })
                }
                
                if(body.result) {
                    this.getSupple()
                }
            })
        },
        
        submit(body) {
            // 提交补单
            submitSupple(this.bdh, false, true).then(body => {
                this.resetBtnStatus({
                    save: {loading: false, disabled: false},
                    submit: {loading: false, disabled: false}
                })
                if(body.result) {
                    this.$root.eventHandle.$emit('addSupply')
                    this.$root.eventHandle.$emit('removeTab',this.params.tabName)
                }
            })
        },
        resetBtnStatus(set) {
            // 重置按钮状态
            let btn
            for(var key in set) {
                btn = this.btns_save.filter(item => item.key === key)[0]
                !btn && ( btn = this.btns_tool.filter(item => item.key === key)[0])
                if(btn) {
                    btn.loading = set[key].loading
                    btn.disabled = set[key].disabled
                }                
            }
        },
        valid(row, prop, label) {
            // 提交验证
            var reg = new RegExp(/((^[1-9]\d*)|^0)(\.\d{1,2}){0,1}$/)
            if(row) {
                // // 校验实际价格
                // if(this.tableData.findIndex(item => {return item.act_price && !reg.test(item.act_price)}) !== -1) {
                //     this.$message.error('金额只能为正数且少于14位，2.最多两位小数')
                //     return false
                // }
                // 校验补单商品名称
                if(this.tableData.findIndex(item => {return item.message.trim() === '' }) !== -1) {
                    this.$message.error('补单商品名称不能为空')
                    return false
                }
            } else {
                // 校验补单原因
                // if(this.tableData.findIndex(item => {return !item.supply_reason}) !== -1) {
                //     this.$message.error('请选择补单原因')
                //     return false
                // }  
            }
            
            return true
            
        },
        async addData(result) {
            let results = result
            // this.tableData = []
            let custom_goods_ids = await createIds(results.length)
            let goods_id = this.tableData.map(item=>{
                return item.original_goods_id
            })
            console.log(goods_id)
            // 添加数据
            results.forEach((item, index) => {
                if(!goods_id.includes(item.goods_id)){
                        this.tableData.push({
                        material_number:item.material_number,
                        custom_goods_id: custom_goods_ids[index],
                        original_goods_id: item.goods_id,
                        is_add: true,
                        length: item.length,
                        width: item.width,
                        height: item.height,
                        retail_price: '',
                        act_price: '',
                        supply_reason: '',
                        message: '补'+item.message,
                        original_message:item.message
                    })
                }
                
            })
        },
        createdNewGoodsId(original_goods_id) {
            // 生成新号
            let original_goods_ids = this.tableData.filter(item => item.original_goods_id === original_goods_id)
            let nums = original_goods_ids.map(item => Number(item.goods_id.split('B')[1])).sort()
            let i = 1
            while(1) {
                if(!nums.includes(i)) {
                    return i < 10 ? `0${i}` : i
                }
                i++
                if(i === 1000) {
                    this.$message.error('补单商品号生成失败')
                    return ''
                }
            }
        },
        setFormData() {
            // 设置表单数据
            let self = this;
            let canEditOrder = this.params.row.client_status =='REJECT_VERIFY'
            !this.supplyInfo.promise_consign_date && (this.supplyInfo.promise_consign_date = new Date().getTime() + 1000*60*60*24*14)
            this.formData = [{
                cols: [
                        {formType: 'elInput', prop: 'bdh', label: '补单号', value: this.bdh, disabled: true},
                        {formType: 'elDatePicker', type:"datetime", prop: 'promise_consign_date',  label: '约定交付日期', value: this.supplyInfo.promise_consign_date, format: 'yyyy-MM-dd hh:mm'},
                        {formType: 'elSelect', prop: 'supply_transport_type',  label: '货运方式', value: this.supplyInfo.supply_transport_type, options: bjd_deliver_method}
                    ]
                },
                {
                    cols: [
                        {
                            formType: 'elInput', 
                            label: '客户', 
                            prop: 'client_no', 
                            value: this.supplyInfo.client_name,
                            disabled: true
                        },
                        {
                            formType: 'listSelect', 
                            label: '关联订单', 
                            prop: 'original_client_number', 
                            value: this.supplyInfo.original_client_number, 
                            disabled:canEditOrder,
                            config: {
                                label: this.supplyInfo.original_client_number, 
                                popupType: 'relation_order', 
                                initParam: {
                                    client_default_status: 'COMPLETED',
                                    trade_type: 'ORIGINAL'
                                },
                                resultValid:(e, col, formData, getItem) => {
                                    console.log(this.bdh)
                                    // let client_no = getItem('client_no')
                                    if(this.bdh){
                                        this.$message.error('该补单已保存，不能修改关联订单');
                                        return;
                                    }
                                    // 选择后验证
                                    let self = this
                                    if(self.params.row.client_status =='REJECT_VERIFY' ){
                                        self.$message.error('拆单驳回不能修改关联订单');
                                        return;
                                    }
                                    return new Promise((resolve) => {
                                        if(self.tableData.length && self.original_client_number !== e.client_number) {
                                            self.$confirm('选择的关联订单发生变化会导致补单商品清空，是否继续？', '提示', {
                                                confirmButtonText: '继续',
                                                cancelButtonText: '取消',
                                                type: 'warning'
                                            }).then(() => {
                                                this.tableData = []
                                                resolve(true)
                                            }).catch(err => {
                                                resolve(false)
                                            })
                                        } else {
                                            resolve(true)
                                        }
                                    })
                                }
                            }, 
                            event: {
                                result: (e, col, formData, getItem) => {
                                    
                                    self.orderChange(e, getItem)
                                }
                            }
                        },
                        {
                            formType: 'listSelect', 
                            label: '地址', 
                            prop: 'address_id', 
                            value: this.supplyInfo.address_id, 
                            event: {
                                result(e, col, formData, getItem) {
                                    e.address_name = e.prin_city_district+e.receiver_address
                                    self.setAddress(e, getItem)
                                }
                            },
                            config: {
                                initParam: this.supplyInfo.client_no,
                                cust_id: self.supplyInfo.cust_id,
                                label: this.supplyInfo.address_name,
                                popupType: 'client_address', 
                                valid(config, param) {
                                    // 选择前验证
                                    !param && self.$message.warning('请先选择关联订单')
                                    return param
                                }
                            } 
                        }
                    ]
                },
                {
                    cols: [
                        {formType: 'myText', prop: 'rec_name', label: '收件人', value: '--'},
                        {formType: 'myText', prop: 'rec_phone', label: '收件人手机号', value: '--'},
                        {formType: 'myText', prop: 'rec_address', label: '收件地址', value: '--', span: 24},
                    ]
                }
            ]
        },
      
        orderChange(e, getItem) {
            let address_id = getItem('address_id')
            let client_no = getItem('client_no')
            client_no.value = e.client_name
            address_id.value = e.address_id
            address_id.config.initParam  = e.client_no
            this.client_no = e.client_no
            address_id.config = Object.assign({}, address_id.config, {label: e.address_name})
            //订单选择
            this.original_client_number = e.client_number
            setTimeout(()=>{
                this.setAddress( e.receiver_name ? e : {
                    client_number: e.client_number
                }, getItem)
            },100)
             
        },
        handleUpload(index, row) {
            this.uploadIndex = index
            // 上传
            imageAdmin(this, {
                    parent_no: row.custom_goods_id,
            }, (data) => {
                this.tableData[this.uploadIndex].imgs = data
                this.validFileNull() 
            }) 

        },
        validFileNull() {
            // 验证是否已上传文件
            this.tableData.forEach((item, index) => {
                this.tableData[index].isShow = item.is_add && !item.imgs
            })
            this.tableData = this.tableData.filter(item => true)
        },
        problemDetail(index,row){
            console.log(index,row);
            if(!row.goods_id){
                this.problemIndex = index
                this.btnType = "problem"
                this.$refs.formCreate.save()

            }else{
                this.$root.eventHandle.$emit('alert', {
                    component: () => import('@components/dz_customer/problemList'),
                    style:'width:900px;height:600px',
                    title:'问题列表',
                    params: {
                        row:row,
                        close: d => {
                            // this.form.buyer_name = d.name;
                            // this.form.buyer = d.cust_id;
                        }
                    },
                })
            }
		    
        },
        problemcallBack(index){
            console.log(index);
            let self = this
            if(!self.tableData[index].goods_id){
                
            }else{
                self.$root.eventHandle.$emit('alert', {
                    component: () => import('@components/dz_customer/problemList'),
                    style:'width:900px;height:600px',
                    title:'问题列表',
                    params: {
                        row:self.tableData[index],
                        close: d => {
                            // this.form.buyer_name = d.name;
                            // this.form.buyer = d.cust_id;
                        }
                    },
                })
            }
		    
        },
        handleView(index, row) {
            // 查看图片
            imageView(this, {
                no: {
                    parent_no: row.custom_goods_id,
                },
                client_number: this.original_client_number,
                useBy: 'design'
            })
        }
    }
}
</script>
<style scoped>
.el-input, .el-select {
    width: 100%;
}
</style>
