<template>
<!-- 补单列表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
      <!-- 查询 -->
    <div class="search-content">
      <form-create 
        :formData="queryItems" 
        @save="query"
        savetitle="查询"
        ></form-create>
    </div>
    <!-- 操作栏 -->

    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
        <my-table 
        ref="table"
        tableUrl='/custom-web/api/customSysTrade/getCustomSysTradeList'
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
        :tools="tools"
        toolWidth="auto"
        showTools
        ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from '../components/formCreate/formCreate'
import myTable from '../components/table/table2'
import { client_status_supply, getMap } from '../common/clientDictionary'
import {getShopInfo, getRole} from '../common/api'
import btnStatus from '../common/mixins/btnStatus'
import Vue from 'vue'
export default {
    mixins: [btnStatus],
    components: {
        formCreate,
        myTable
    },
    data() {
        return {
            queryItems: [],
            btns: [],
            tools:[],
            tableParam:{
                page_name: '"custom_payment', 
                trade_type: 'SUPPLY',
                client_start_status: 'IN_DESIGN',
                client_end_status: 'IN_DESIGN'
            },
            colData: [],
            info: {},
            infoList:[]
        }
    },
    props:{
        params: {
            type: Object
        }
    },
    async created() {  
        this.getColData()
        this.role = await getRole()
        this.tools = this.role.indexOf('DZ_SJS') === -1 ? [] : [
            {
                type: 'primary',
                txt: '修改',
                click:  (d) => {
                    this.$root.eventHandle.$emit('creatTab', {
                        name: '修改补单',
                        params: {
                            row: d,
                            client_number: d.client_number
                        },
                        component: () => import('@components/dz_customer/supplement/addSupple.vue')
                    })
                },
                show:(d) => {
                    return this.isEditSupply(d, this.role)
                }
            },
             {
                type: 'danger',
                txt: '取消',
                click:  (d) => {
                    this.cancelCustomSysTrade(d)
                },
                show:(d) => {
                    return d.client_status == "IN_DESIGN"||(d.client_status == "REJECT_VERIFY"&&d.is_all_goods_reject_verify);
                }
            },
        ]
        this.btns = [{
				type: 'primary',
				txt: '新建补单',
                loading: false,
                disabled: this.role.indexOf('DZ_SJS') === -1,
				click: () => {
					this.$root.eventHandle.$emit('creatTab', {
                        name: '新增补单',
                        params: {
                            row: {}
                        },
                        component: () => import('@components/dz_customer/supplement/addSupple.vue')
                    })
				}
        }]
        this.getShopInfo() 
    },
    mounted(){
        this.$root.eventHandle.$on('addSupply', this.$refs.table.refresh)

    },
    beforeDestroy(){
         this.$root.eventHandle.$off('addSupply', this.$refs.table.refresh)
    },
    methods: {
        cancelCustomSysTrade(d){
            this.$confirm(
					"当前操作会导致选中的补单取消，是否继续？",
					"提示",
					{
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "danger",
					}
				).then(() => {
					 this.ajax.postStream('/custom-web/api/customSysTrade/cancelCustomSysTrade',{custom_trade_id: d.custom_trade_id}, res => {
                        if(res.body.result){
                            this.$refs.table.initData()
                        this.$message.success('取消成功');
                        } else {
                            this.$message.error(res.body.msg);
                        }
                        


                    }, err => {
                        this.$message.error(err);
                    });
				}).catch(() => {
					return false;
				});
           
            
        },
        getColData(){
            this.colData = [
                {
					label: '店名',
					prop: 'shop_name',
					width: 133
                },
                {
					label: '补单号',
					prop: 'client_number',
					width: 231,
					redirectClick:(row) => {
						this.$root.eventHandle.$emit('creatTab', {
                            name: '补单详情',
                            component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
                            params: {
                                client_number: row.client_number,
                            }
                        })
					}
                },
                 {
					label: '补件申请单号',
					prop: 'after_ticket_no',
					width: 133
                },
                 {
					label: '源订单号',
					prop: 'original_client_number',
                    width: 190,
                    redirectClick:(row) => {
                        let rows = Object.assign({}, row, {client_number: row.original_client_number})
						this.$root.eventHandle.$emit('creatTab', {
                            name: '订单详情',
                            component: () => import('@components/dz_customer/clientInfo/clientInfo.vue'),
                            params: {
                                customerInfo: rows,
                                lastTab: this.params.tabName
                            }
                        })
					}
                },
                {
					label: '客户名',
					prop: 'client_name',
					width: 127
                },
                {
					label: '手机',
					prop: 'client_mobile',
					width: 127,
                    format: 'hidePhoneNumber'
                },
                 {
					label: '建档日期',
                    prop: 'create_time',
                    filter: 'date',
					width: 150
                },
                {
					label: '建档人',
					prop: 'designer_name',
					width: 107
                },
                 {
					label: '约定交付日期',
                    prop: 'promise_consign_date',
                    filter: 'date',
					width: 130
                },
                {
					label: '状态',
					prop: 'client_status_cn',
					width: 86
                }
            ]
        },
        async getShopInfo() {
            this.infoList = await getShopInfo()
            this.info = this.infoList.length? this.infoList[0]:{};
            this.getQueryItems()
        },
        query(data) {
            console.log(data);
            Object.assign(this.tableParam, data)
            this.$refs.table.initData()
        },
        getQueryItems(){
            let _self = this;
           let shopOption = [];
            this.infoList.forEach(item=>{
                shopOption.push({
                value:item.shopCode,
                label:item.loginShopName,
                })
            })
            this.queryItems = [
                {
                    cols: [
                        // {formType: 'selectRange', label: '专卖店', value: this.info.loginShopName, span: 24},
                        {
                        formType: "elSelect",
                        label: "专卖店",
                        // value: this.info.shopCode,
                        prop:'shop_name',
                        options: shopOption,
                        span: 24,
                        event: {
                            change(v, col, formData, getItem) {
                            
                            
                            },
                        },
                        },
                        {formType: 'elInput', label: '客户电话', prop:'client_mobile', span: 8},
                        {formType: 'elInput', label: '客户名称',  prop: 'client_name', span: 8},
                        {formType: 'elInput', label: '源订单号', prop:'original_client_number', span: 8},
                        {formType: 'elInput', label: '补单号', prop:'client_number', span: 8},
                        {formType: 'elInput', label: '补件申请单号', prop:'after_ticket_no', span: 8},
                        {formType: 'selectRange', prop: 'client_satus', value: ['IN_DESIGN', 'IN_DESIGN'], props:['client_start_status', 'client_end_status'], label: '补单状态', options:[client_status_supply, client_status_supply]},
                        {formType: 'elDatePicker', prop: 'date', format: 'yyyy-MM-dd', props:['start_create_date', 'end_create_date'], label: '建档日期', type: 'daterange'},
                    ]
                }
            ]
        }
    }
}
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
