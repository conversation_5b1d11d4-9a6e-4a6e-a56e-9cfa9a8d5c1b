<template>
<!-- 补单详情 -->
    <div class="info" 
        v-loading="loading"
        element-loading-text="拼命加载中"
        >
        <div class="info-container">
            <info
                v-for="info in infos"
                :key="info.key"
                :info="info"
                :aboutNumber="aboutNumber"
            />
        </div>
        <div class="nav">
            <div
                v-for="item in infos"
                :key="item.key"
            >
                <a :href="'#'+item.key">{{item.title}}</a>
                <div v-if="item.rows">
                    <div
                        v-for="el in item.rows"
                        :key="el.key"
                        class="child"
                        >
                            <a v-if="el.key" :href="'#'+el.key">{{el.title}}</a>
                        </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import info from '../clientInfo/info'
import infoMix from '../common/mixins/info'
import btnStatus from '../common/mixins/btnStatus'
import {getRole} from '../common/api'
import { getMap as getClientMap } from '../common/clientDictionary'
import { getMap as getGoodsMap } from '../common/goodsStatusDictionary'
export default {
    mixins: [btnStatus, infoMix],
    props: ['params'],
    components: {info},
    data() {
        return {
            loading: true,
            infos: [],
            aboutNumber:'',//单据号
        }
    },
    async created() {
        this.currentKey = new Date().getTime()
        this.role = await getRole()
        this.maps = await this.getMap()
        this.refresh({client: true, order: true, communication: true})
        this.aboutNumber=this.params.client_number;
    },
    methods: {
        getMap(){
            let maps = [
                new Promise((resolve, reject) => {
                    getClientMap(clientMap => {
                        resolve(clientMap)
                    })
                }),
                new Promise((resolve, reject) => {
                    getGoodsMap(goodsMap => {
                        resolve(goodsMap)
                    })
                })
                
            ]
            return Promise.all(maps)
        },
        async refresh(type = {}) {
            // 刷新数据
            this.info_client = await this.getClientInfo()
            this.loading = false
            this.infosChange()
            if(type.order) {
                    this.getSupplyInfo().then(supply => {
                        this.info_supply = supply
                        this.infosChange()
                    })
            }
            
            if(type.communication) {
               this.info_communication = this.getComunication()
               this.infosChange()
           }
            // this.supplyInfo = await this.getSupplyInfo()
        },
        getsummary(val,data){
        // console.log(val,1111111)
        this.info_summary= {
                key: `summary${this.currentKey}`,
                title: "概要",
                noKey:true,
                btns:[],
                rows:[{
                    cols: [
                    {
                        formType: "summaryDetail2",
                        data: val,
                        value:data,
                        span: 24,
                    },
                ],
                }],
                
                }
        console.log(this.info_summary,1111111)

        this.infosChange();
        },
        infosChange() {
            this.infos = [
                this.info_summary,
                this.info_client,
                this.info_supply,
                this.aftersaleBillSupply,
                this.info_communication
            ].filter(item => item)
        },
        async getClientInfo() {
            let self=this
            let map = this.maps[0]
            const data = await this.getSection(
                '/custom-web/api/customSysTrade/getSysTradeByNumber',
                this.params.client_number,
                '补单信息',
                value => {
                    return []
                },
                (value) => {
                    console.log(map)
                    let statusFlag = true;
                    let onStatus = false;
                    let sourceData = map.client_status_supply.map(item=>{
                    if(item.value == value.client_status){
                        statusFlag = false;
                        onStatus = true;
                    }else{
                        onStatus = false;
                    }
                    return{
                        content:item.label,
                        timestamp:'2021-11-01',
                        isActive:statusFlag,
                        onStatus:onStatus
                    }
                    });

                    this.getsummary(value,sourceData);
                    this.clientInfo = value
                    return [
                        {
                            cols: this.setValue([
                                {prop: 'client_no', label: '客户编号'},
                                {prop: 'client_name',label: '客户姓名'},
                                {prop: 'client_mobile',label: '客户手机/电话', span: 12},
                                {prop: 'client_number', label: '补单号'},
                                {prop: 'client_status', label: '补单订单状态', valueFilter: 'select', options: map.client_status},

                                {prop: 'create_time',label: '建档时间', valueFilter: 'date', span: 12},
                                {prop: 'shopping_guide_name',label: '建档人'},
                                // {prop: 'after_ticket_no',label: '补件申请号',span: 12,color:"rgb(0, 0, 238)",
                                // redirectClick(d){
                                //     if(!d){
                                //         return
                                //     }
                                //     self.$root.eventHandle.$emit('creatTab',{
                                //         name:'补件单详情',
                                //         params:{id:self.clientInfo.aftersale_bill_supply_id},
                                //         component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')
                                //     })
                                // }},
                                {prop: 'designer_name',label: '设计师',},
                                {prop: 'shop_name',label: '专卖店',  span: 12},
                                {prop: 'prin_city_district',label: '省市区', valueFilter(value, col) {
                                    return col.receiver_state_name+col.receiver_city_name+col.receiver_district_name
                                }},
                                {prop: 'address_name',label: '详细地址', span: 18},
                                // {prop: 'delivery_time',label: '发货日期', valueFilter: 'date'},
                                // {prop: 'transport_no',label: '发货单号'},
                                // {prop: 'delivery_logistics',label: '物流公司'},
                                // {prop: 'remark',label: '客户备注',notellipsis:true,  span: 24}
                            ], value)
                        }
                    ]
                }

            )
            data.key = `clientInfo${this.currentKey}`
            return data
        },
        // 过去交流意见
        getComunication() {
            return {
                title: '操作记录',
                key: `communication${this.currentKey}`,
                
                noKey: true,
                rows: [
                    {
                        cols: [
                            {formType:'communication', locationKey: `communication${this.currentKey}`, isSupply: true, params:{
                                client_number: this.clientInfo.client_number
                            },  span: 24},
                        ]
                    }
                ]
            }
        },
        async getSupplyInfo() {
            let self = this
            let map = this.maps[1]
            const data = await this.getSection(
                '/custom-web/api/customSupplyTrade/getSupplyTrade',
                this.params.client_number,
                '补单商品列表',
                value => {
                    return this.isEditSupply(value, this.role) ? [
                        {
                            txt: '修改',
                            type: 'primary',
                            click: ()=> {
                            }
                        }
                    ] : []
                },
                (value) => {
                    let goodsList = []
                    if(value.aftersaleBillSupplyVoList&&value.aftersaleBillSupplyVoList.length != 0){
                      self.aftersaleBillSupply = self.aftersaleBillSupplyVoList(value.aftersaleBillSupplyVoList);
                      self.infosChange()
                    }
                    let goodsStatusIndex
                    let goods_status_options = map.goods_status
                    value.goodsList = value.goodsList.map(item => {
                        goodsStatusIndex = goods_status_options.findIndex(el => el.value === item.goods_status)
                        goodsStatusIndex !==-1 && (item.goods_status_cn = goods_status_options[goodsStatusIndex].label)
                        return item
                    })
                  
                    return [
                        {
                            cols: [
                                { 
                                        formType: 'myTable', 
                                        span: 24,
                                        data: value.goodsList,
                                        tableHeight: 'auto',
                                        colData: [{
                                        label: '补单商品号',
                                        width: '200',
                                        prop: 'goods_id',
                                        summaryMethod(param) {
                                            const { columns, data } = param;
                                            console.log('param', param)
                                            const sums = [];
                                            // let retail_priceIndex
                                            let act_priceIndex
                                            columns.forEach((column, index) => {
                                            if (index === 0) {
                                                sums[index] = '总计';
                                                return;
                                            }
                                            if(column.property === 'act_price') {
                                                //  column.property === 'retail_price' && (retail_priceIndex=index)
                                                 column.property === 'act_price' && (act_priceIndex=index)
                                                 const values = data.map(item => Number(item[column.property]));
                                                if (!values.every(value => isNaN(value))) {
                                                    sums[index] = values.reduce((prev, curr) => {
                                                    const value = Number(curr);
                                                    if (!isNaN(value)) {
                                                        return prev + curr;
                                                    } else {
                                                        return prev;
                                                    }
                                                    }, 0);
                                                } else {
                                                    sums[index] = 'N/A';
                                                }
                                            }
                                           
                                            });
                                            // sums[retail_priceIndex] = `总零售价：${parseFloat(sums[retail_priceIndex]).toFixed(2)}元`
                                            sums[act_priceIndex] = `总实际价格：${parseFloat(sums[act_priceIndex]).toFixed(2)}元`
                                            return sums;
                                        },
                                        redirectClick(d) {
                                            self.$root.eventHandle.$emit('creatTab', {
                                                name: '补单商品详情',
                                                component: () => import('@components/dz_customer/goodsInfo/goodsInfo.vue'),
                                                params: {
                                                    goodsInfo: d,
                                                    client_number:self.params.client_number,
                                                    trade_type: 'SUPPLY'
                                                }
                                            })
                                        },
                                        }, {
                                        label: '补单商品名称',
                                        prop: 'message',
                                        },{
                                        label: '原商品编码',
                                        prop: 'original_goods_id',
                                        redirectClick(d) {
                                            self.$root.eventHandle.$emit('creatTab', {
                                                name: '商品详情',
                                                component: () => import('@components/dz_customer/goodsInfo/goodsInfo.vue'),
                                                params: {
                                                    goodsInfo: {goods_id:d.original_goods_id,client_number:self.params.client_number.substring(0,self.params.client_number.length-3),},
                                                    
                                                    trade_type: 'ORIGINAL'
                                                }
                                            })
                                        },
                                        },
                                        // 补单原因改为描述 新增终端判责暂且不做 2021.2.22
                                        {
                                        label: '描述',
                                        prop: 'supply_reason_cn',
                                        },{
                                        label: '建档人',
                                        prop: 'designer',
                                        },{
                                        label: '补单商品状态',
                                        prop: 'goods_status_cn',
                                        },{
                                        label: '三维家补单号',
                                        prop: 'swj_goods_no',
                                        }]
                                    }
                            ]
                        }
                    ]
                }

            )
            console.log(data);
            data.key = `supplyInfo${this.currentKey}`
            return data
        },
          aftersaleBillSupplyVoList(list) {
            let self = this
            
            let data = {
                btn:[],
                title:'补件数据',
                rows:[
                        {
                            cols: [
                                { 
                                        formType: 'myTable', 
                                        span: 24,
                                        data: list,
                                        tableHeight: 'auto',
                                        colData: [{
                                        label: '补件申请单号',
                                        width: '200',
                                        prop: 'after_ticket_no',
                                       
                                        redirectClick(d) {
                                              self.$root.eventHandle.$emit('creatTab',
                                        {name:'补件单详情',params:{id:d.bill_supply_id,list:[],typeNumber:1},component:()=>import('@components/after_sales_supply/supplyOrder_2.vue')})
    
                                            // self.$root.eventHandle.$emit('creatTab', {
                                            //     name: '补单商品详情',
                                            //     component: () => import('@components/dz_customer/goodsInfo/goodsInfo.vue'),
                                            //     params: {
                                            //         goodsInfo: d,
                                            //         trade_type: 'SUPPLY'
                                            //     }
                                            // })
                                        },
                                        }, {
                                        label: '补件类型',
                                        prop: 'type',
                                        },
                                        // 补单原因改为描述 新增终端判责暂且不做 2021.2.22
                                        {
                                        label: '发货单号',
                                        prop: 'outbound_no',
                                        },{
                                        label: '物流公司',
                                        prop: 'delivery_logistics',
                                        },{
                                        label: '发货日期',
                                        prop: 'delivery_time',
                                        },]
                                    }
                            ]
                        }
                    ]
            }
            data.key = `aftersaleBillSupply${this.currentKey}`
            return data
        }
    }
}
</script>
<style scoped src='../style/info.css'></style>
