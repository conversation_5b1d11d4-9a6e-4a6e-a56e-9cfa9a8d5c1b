<template>
<!-- 收费详情 -->
    <div class="info" 
        v-loading="loading"
        element-loading-text="拼命加载中"
        >
        <div class="info-container">
            <info
                v-for="info in infos"
                :key="info.key"
                :info="info"
            />
        </div>
        
    </div>
</template>
<script>
import info from './clientInfo/info'
import infoMix from './common/mixins/info' 
import {getMap} from './common/tollDictionary'
import { getFileList} from './common/api'
export default {
    mixins: [infoMix],
    props: ['params'],
    components: {info},
    data() {
        return {
            loading: false,
            infos: []
        }
    },
    async mounted() {
       this.maps = await this.getMap()
       this.refresh()
    },
    methods: {
        getMap(){
            let maps = [
                new Promise((resolve, reject) => {
                    getMap(tollDictionary => {
                        resolve(tollDictionary)
                    })
                })
            ]
            return Promise.all(maps)
        },
        async refresh(type = {}) {
            this.info_toll = await this.getTollInfo()
            this.infosChange()
        },
        infosChange() {
            this.infos = [
                this.info_toll,
            ].filter(item => item)
        },
       async getTollInfo() {
           
            let params = this.params || {}
            let info = params.info || {}
            let value = Object.assign({
                loginShopName: info.loginShopName
            }, this.params.row || {})
            let funds_type = this.maps[0].funds_type
            let pay_type = this.maps[0].pay_type
            // 获取附件
            let lists = await getFileList({
                order_no: value.payment_id,
            })
            let imageMap = {}
                        lists.forEach(item => {
                            !imageMap[item.sub_order_no] && (imageMap[item.sub_order_no] = [])
                            imageMap[item.sub_order_no].push(item.path)
                        })
            let payment_ids=lists.map((item)=>{
                return {
                    images:imageMap[item.sub_order_no]||[], 
                    parent_no: value.payment_id,
                    client_number: params.row.client_number,
                    detail: item.design_point,
                    useBy: 'design'
                }
            })
            return {
                key: 'toll',
                title: '收费信息',
                btns: [],
                rows: [
                    {
                        cols: this.setValue([
                            {prop: 'loginShopName', label: '专卖店', span: 12},
                            {prop: 'client_name',label: '客户名', span: 12},
                            {prop: 'pay_amount',label: '支付金额', suffix: '元', span: 12},
                            {prop: 'customized_pay',label: '款项类别', valueFilter: 'select', options: funds_type, span: 12},
                            {prop: 'pay_type',label: '支付方式', valueFilter: 'select', options: pay_type, span: 12},
                            {prop: 'pay_date',label: '支付日期',  span: 12},
                            {prop: 'shop_code',label: '专卖店编号', span: 24},
                        ], value)
                    },
                    {                     
                        cols: [
                            {
                                label: '支付凭证',
                                formType: 'imageList', 
                                params: {value: payment_ids},
                                span: 24
                            }
                        ]
                    },
                ]
            }
            
            return data
        }
    }
}
</script>
<style scoped>
.info {
    position: absolute;
    top:0;
    left:0;
    width: 100%;
    height: 100%;
    overflow:auto;
    padding: 0 20px;
}
.info-container {
    max-width: 1400px;
    margin: 0 auto;
    padding-bottom: 40px;
    line-height: 16px;
}
</style>
