<template>
<!-- 支付记录列表 -->
  <div class="searchBox" style="display:flex;height:99%;flex-direction:column;">
      <!-- 查询 -->
    <div class="search-content">
      <form-create 
        :formData="queryItems" 
        @save="query"
        @request="request"
        savetitle="查询"
        ></form-create>
    </div>
    <!-- 操作栏 -->

    <!-- 表格 -->
    <div style="flex:1;overflow:hidden">
        <my-table 
        ref="table"
        tableUrl='/custom-web/api/customPayment/getCustomPaymentList'
        :tableParam="tableParam"
        :colData="colData"
        :orderNo="true"
        :btns="btns"
        :isInit="false"
        ></my-table>
    </div>
  </div>
</template>
<script>
import formCreate from './components/formCreate/formCreate'
import myTable from './components/table/table'
import {sync_status, getMap} from './common/tollDictionary'
import {getShopInfo, getRole} from './common/api'
import Vue from 'vue'
export default {
    components: {
        formCreate,
        myTable
    },
    data() {
        return {
            queryItems: [],
            btns: Object.freeze([{
				type: 'success',
				txt: '新增',
				loading: false,
				click: () => {
					this.$root.eventHandle.$emit('creatTab', {
                        name: '新增收费记录',
                        params: {
                            info: this.info
                        },
                        component: () => import('@components/dz_customer/addToll.vue')
                    })
				}
            }
            ]),
            tableParam:{page_name: '"custom_payment'},
            colData: [],
            info: {},
            infoList:[],
            isJxbc:false
        }
    },
    props:{
        params: {
            type: Object
        }
    },
    async created() {   
        this.role = await getRole()
        this.getShopInfo()
        this.setShopDefault();
        if(this.info.shopCode == 'LS-JXBC'){
            this.isJxbc =true;
        }
    },
    mounted(){
        getMap(map => {
            this.getColData()
            this.$refs.table.initData()
        })
        this.$root.eventHandle.$on('refreshTollList', this.$refs.table.refresh)

    },
    beforeDestroy(){
         this.$root.eventHandle.$off('refreshTollList', this.$refs.table.refresh)
    },
    methods: {
        setShopDefault() {
            this.setShopDefault.where = [];
            this.ajax.postStream(
                "/user-web/api/sql/listFields",
                { page: "cloud_shop_v2" },
                (res) => {
                    if (res.body.result) {
                        (res.body.content.fields || []).forEach((o) => {
                            if (o.comment === "店铺分类") {
                                this.setShopDefault.where.push({
                                    field: o.field,
                                    table: o.table,
                                    value: "HARD_DECORATION",
                                    operator: "=",
                                    condition: "AND",
                                    listWhere: [],
                                });
                            } else if (o.comment === "主营类目") {
                                this.setShopDefault.where.push({
                                    field: o.field,
                                    table: o.table,
                                    value: "DZ",
                                    operator: "=",
                                    condition: "AND",
                                    listWhere: [],
                                });
                            }
                        });
                    }
                }
            );
        },
        getColData(){
            this.colData = [
                {
					label: '店名',
					prop: 'shop_name',
					width: 133
                },
                {
					label: '订单号',
					prop: 'client_number',
					width: 180,
					redirectClick:(row) => {
						this.$root.eventHandle.$emit('creatTab', {
                            name: '订单详情',
                            component: () => import('@components/dz_customer/clientInfo/clientInfo.vue'),
                            params: {
                                customerInfo: row,
                                lastTab: this.params.tabName
                            }
                        })
					}
                },
                 {
					label: '客户名',
					prop: 'client_name',
					width: 103
                },
                {
					label: '手机',
					prop: 'client_mobile',
					width: 127,
                    format: 'hidePhoneNumber'
                },
                {
					label: '支付编码',
					prop: 'pay_number',
                    width: 270,
                    redirectClick:(row) => {
						this.$root.eventHandle.$emit('creatTab', {
                            name: '查看单据详情',
                            params: {
                                row,
                                info: this.info
                            },
                            component: () => import('@components/dz_customer/tollInfo.vue')
                        })
					}
                },
                 {
					label: '同步状态',
                    prop: 'sync_status_cn',
                    filter: 'select',
                    options: sync_status,
					width: 86
                },
                {
					label: '支付金额',
					prop: 'pay_amount',
					width: 107
                },
                 {
					label: '支付日期',
                    prop: 'pay_date',
                    filter: 'date',
                    formats: 'yyyy-MM-dd',
					width: 116
                },
                {
					label: '建档人',
					prop: 'creator_name',
					width: 86
                },
                 {
					label: '备注',
                    prop: 'remark'
                    
                }
            ]
        },
        async getShopInfo() {
            let canGetShopInfoRole = ['DZ_SJS', 'DZ_DG', 'DZ_DZ', 'DZ_SJZG']
            if(this.role.findIndex(item => canGetShopInfoRole.includes(item)) !==-1) {
                this.infoList = await getShopInfo()
                this.info = this.infoList.length? this.infoList[0]:{};
                
            } else {
                this.info = {loginShopName: '--'}
            }
            this.getQueryItems()
        },
        request(){
            this.$root.eventHandle.$emit('refreshTollList')
            this.$root.eventHandle.$emit('removeTab',this.params.tabName)
        },
        query(data) {
            let param = JSON.parse(JSON.stringify(data));
            delete param.loginShopName;
            console.log(param,this.info);
            param.shop_code = this.info.shopCode;
            Object.assign(this.tableParam, param)
            this.$refs.table.initData()
        },
        getQueryItems(){
            let self = this;
            this.queryItems = [
                {
                    cols: [
              
                        {formType: 'elInput',
                        event: {
                        onIconClick(v, col, formData, getItem) {
                            // let self = this
                            if (!!self.isJxbc){
                                return;
                            }
                            self.$root.eventHandle.$emit('alert', {
                                component:()=>import('@components/shop/list'),
                                style:'width:800px;height:500px',
                                title:'选择店铺',
                                params: {
                                    selection: 'radio',
                                    setWhere: (queryData) => {
                                        var newWhere = (queryData.where || []).filter((o) => {
                                            return (
                                                o.field !== self.setShopDefault.where[0].field &&
                                                o.field !== self.setShopDefault.where[1].field
                                            );
                                        });
                                        

                                        newWhere = newWhere.concat(self.setShopDefault.where);

                                        queryData.where = newWhere;
                                    },
                                    callback (data) {
                                        // let shopName = getItem('shop_name');
                                        // let shop_code = getItem('shop_code');
                                        let shopName = getItem('loginShopName');
                                        // console.log(shopName);
                                        shopName.value = data.shop_name;
                                        self.$set(self.info,'loginShopName',data.shop_name);
                                        self.$set(self.info,'shopCode',data.shop_code);
                                        // self.info.loginShopName = data.shop_name;
                                        // self.info.shopCode = data.shop_code;
                                        // formData.shop_name = data.shop_name;
                                        // clientInfo.shop_code = data.shop_code;
                                    
                                    }
                                }
                            })
                        }
                            },readonly:true, label: '专卖店',disabled:this.info.shopCode != 'LS-JXBC',icon: "search",prop:'loginShopName', value: this.info.loginShopName, span: 24},
                        {formType: 'elInput', label: '订单号', prop:'client_number', span: 8},
                        {formType: 'elInput', label: '客户名称',  prop: 'client_name', span: 8},
                        {formType: 'elInput', label: '支付编码', prop:'pay_number', span: 8},
                        {formType: 'elSelect', label: '同步状态', prop: 'sync_status', span: 8, options:sync_status},
                        {formType: 'elDatePicker', prop: 'date', format: 'yyyy-MM-dd', props:['start_pay_date', 'end_pay_date'], label: '支付日期', type: 'daterange'},
                    ]
                }
            ]
        }
    }
}
</script>
<style scoped>
.search-content {
  border: 1px #aaa solid;
  width: 1500px;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
</style>
