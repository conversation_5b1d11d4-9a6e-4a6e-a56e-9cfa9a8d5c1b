<template>
<!-- 未成交登记 -->
  <div>
    <xpt-headbar>
			<el-button size='mini'  type="primary" @click="submit('customer')" slot='left' :disabled='customerBtnStatu' :loading='customerBtnStatu'>保存</el-button>
			<el-button type='danger' class='xpt-close' size='mini' @click="closeComponent" slot='right'>关闭</el-button>
		</xpt-headbar>
    <div class="content">
      <el-row type="flex" justify="center">
        <h2>未成交登记</h2>
      </el-row>
      <el-form label-position="right" label-width="100px" :model="customer" :rules="rules" ref="customer">
        <el-row type="flex" justify="space-around">
          <el-col :span="10">
            <el-form-item label="客户号" prop="client_number">
              <el-input v-model="customer.client_number" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="客户姓名" prop="client_name">
              <el-input v-model="customer.client_name" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-around">
          <el-col :span="10">
            <el-form-item label="客户电话" prop="client_mobile">
              <el-input v-model="customer.client_mobile" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="设计师" prop="designer">
              <el-input v-model="customer.designer" size='mini' disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <!-- <el-col :span="22">
            
            
          </el-col> -->

          <el-col :span="22">
            <el-form-item label="" prop="designer"  style="color:red"  v-if="params.message">
              <p >{{params.message}}</p>
            </el-form-item>
            <el-form-item label="取消订单原因" prop="no_deal_reason">
            <el-checkbox-group v-model="customer.no_deal_reason" v-if="params.isBefore" style="display:inline-block;">
              <el-checkbox label="DESIGN">方案设计不满意</el-checkbox>
              <el-checkbox label="EXPENSIVE">价格过高</el-checkbox>
              <el-checkbox label="QUALITY">质疑产品品质</el-checkbox>
              <el-checkbox label="STYLE">产品款式不是很喜欢</el-checkbox>
              <el-checkbox label="OTHER">其他</el-checkbox>
            </el-checkbox-group>
             <el-checkbox-group v-model="customer.no_deal_reason" v-else style="display:inline-block;">
              <el-checkbox v-for="item in reason_type" :key="item.id" :label="item.code" >{{item.name}}</el-checkbox>
            </el-checkbox-group>
            <el-tooltip v-if='rules.no_deal_reason[0].isShow' class="item" effect="dark" :content="rules.no_deal_reason[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
        <el-col :span='22' class="addHeight">
          <el-form-item :label="params.isBefore?'备注':'具体原因'" prop="no_deal_remark">
            <el-input type='textarea' v-model="customer.no_deal_remark" size='mini' style="width:80%" :maxlength='200' resize="none"></el-input>
          </el-form-item>
        </el-col> 
      </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import validate from './common/validate'

export default {
props:['params'],
    data(){
      var self = this
      return {
        rules: {
          no_deal_reason: validate.isNotBlank({
            self:self,
            msg:'请选择未成交原因',
            trigger: 'change'
          }),
        },
        reason_type:[],
        customer:{
          client_number:'',
          client_name:'',
          client_mobile:'',
          client_sex: '',
          designer:'',
          no_deal_reason:[],
          no_deal_remark:''
        },
        customerBtnStatu:false
      }
    },
    methods:{
      closeComponent(){
        let self = this
        self.$root.eventHandle.$emit('removeTab',self.params.tabName)
      },
      createStateFilter(queryString) {
        return (state) => {
          return (state.value.indexOf(queryString.toLowerCase()) === 0)
        }
      },
      storeData(callback){
        var url = this.params.isBefore?'/custom-web/api/customNoDeal/save':'/custom-web/api/customNoDeal/cancelTrade';
        var data = {}
        var _this = this
        let tabName = data.tabName
        let content = '';
         this.reason_type .forEach(item=>{
           this.customer.no_deal_reason.forEach(rItem=>{
             if(rItem == item.code){
               content += item.name +','
             }
           })
         })
        delete data.tabName
        this.customerBtnStatu = true
        this.customer.no_deal_reason = this.customer.no_deal_reason.toString()
        data =this.params.isBefore? {
          client_number: this.customer.client_number,
          no_deal_reason: this.customer.no_deal_reason,
          no_deal_remark: this.customer.no_deal_remark
        }:{
          client_number: this.customer.client_number,
          changeContent: content,
          remark: this.customer.no_deal_remark,
          message:this.params.message
        }
        this.ajax.postStream(url,data,(data) =>{
          data = data.body
          _this.$message({
            message: data.msg,
            type:data.result?'success':'error'
          })
          if(data.result){
            _this.isPass = true
            this.$root.eventHandle.$emit('refreshclientList')
            _this.closeComponent()
          }
          
          callback&&callback()
          _this.customer.no_deal_reason = _this.customer.no_deal_reason.split(',')
          
          _this.customerBtnStatu = false
        },function(data){
          console.log('失败的回调函数')
          _this.customerBtnStatu = false
          _this.customer.no_deal_reason = _this.customer.no_deal_reason.split(',')
        })
      },
      //保存数据
      submit(formName,callback){
        let self = this;
        this.$refs[formName].validate((valid) => {
          if(!valid) return false
          if(this.params.message){
             this.$root.eventHandle.$emit("openDialog", {
                txt:self.params.message,
                okTxt: "确定",
                cancelTxt: "取消",
                noShowNoTxt: true,
                ok() {
                  self.storeData(callback)
                },
                cancel() {
                    self.$message.success("已取消");
                },
            });
          }else{
            self.storeData(callback)
          }
         
        })
      },
      getCustomerDeatil(){
        //获取客户详情
        this.customer.client_number = this.params.customerInfo.client_number
        this.customer.client_name = this.params.customerInfo.client_name
        this.customer.client_sex = this.params.customerInfo.client_sex
        this.customer.client_mobile = this.params.customerInfo.client_mobile
        this.customer.designer = this.params.customerInfo.designer_name
      },
      initData(){//初始化数据
        this.getCustomerDeatil()
      },
    },

    mounted(){
      this.initData()
      console.log(this.params.isBefore)
      this.params.__data = JSON.stringify(this._data)
      this.params.__close = this.closeComponent;
      this.reason_type = __AUX.get('custom_cancel_trade_reason')
    }
  }
</script>

<style scoped>
h2 {
  line-height: 20px;
}
.content {
width: 1000px;
margin: 10px auto;
}
.addHeight {
  height: 60px;
}
</style>
