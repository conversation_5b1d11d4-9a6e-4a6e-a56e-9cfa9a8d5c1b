<template>
    <!-- 异常管理 -->
    <div class="searchBox" style="width: 100%">
      <div class="search-content">
        <form-create
          :formData="queryItems"
          @save="query"
          savetitle="查询"
          ref="formcreate"
        ></form-create>
      </div>
      <dz-list
        :data="customerList"
        :colData="colData"
        :pageTotal="pageTotal"
        :btns="btns"
        :tools="tools"
        @page-size-change="pageChange"
        @current-page-change="currentPageChange"
        ref="dzList"
      >
      </dz-list>
    </div>
  </template>
  
  <script>
  import fn from "@/common/Fn.js";
  import _ from "loadsh";
  import { getRole } from "./common/api";
  import formCreate from "./components/formCreate/formCreate";
  import btnStatus from "./common/mixins/btnStatus";
  import { getClientStatus } from "./common/map";
  import { client_status } from "./common/clientDictionary";
  import dzList from "./components/list/list";
  import myTable from "./components/table/table";
  // 下推
  export default {
    props: {
      params: {
        type: Object,
      },
      trade_type: {
        type: String,
        default: "ORIGINAL",
      },
    },
    components: { formCreate, dzList, myTable },
    mixins: [btnStatus],
    data() {
      let self = this;
      return {
        retryStatus:true,
        searchParam: {
          client_start_status: "ERROR",
          client_end_status: "ERROR",
        },
        role: ["other"],
        queryItems: [],
        customerList: [],
        btns: [
          {
            type: "primary",
            txt: "刷新",
            loading: false,
            show: true,
            click: () => {
              this.refresh();
            },
          },
        ],
        pageNow: 1,
        pageSize: 20,
        pageTotal: 0,
        colData: Object.freeze([
          {
            label: "店名",
            prop: "shop_name",
            width: 160,
          },
          {
            label: "订单号",
            prop: "client_number",
            width: 160,
            redirectClick(d) {
              self.detail(d);
            },
          },
          {
            label: "客户名",
            prop: "client_name",
            width: 160,
          },
          {
            label: "手机",
            prop: "client_mobile",
            width: 90,
            format: "hidePhoneNumber",
          },
          {
            label: "建档日期",
            prop: "create_time",
            format:'dataFormat1',
            width: 130,
          },
          {
            label: "提交合同日期",
            prop: "compact_date",
            format:'dataFormat1',
            width: 130,
          },
          {
            label:'合同金额',
            prop:'co_agreed_to_gold'
          },
          {
            label:'标准售价',
            prop:'sum_retail_price'
          },
          {
            label: "建档人",
            prop: "shopping_guide_name",
            width: 60,
          },
          {
            label: "设计师",
            prop: "designer_name",
            width: 60,
          },
          {
            label: "订单状态",
            prop: "client_status",
            width: 90,
          },
          {
            label: "异常原因",
            prop: "error_message",
            width: 100,
          },
        ]),
        tools: [],
      };
    },
    methods: {
      //批量驳回
      reject(d) {
        const { client_number, custom_trade_id } = d;

        // 此列表接口用于勿动校验
        let params = {
          client_number: client_number,
        };
        this.ajax.postStream(
          "/custom-web/api/customGoods/getGoodsPurchaseTrade?permissionCode=DZ_ERROR",
          params, res => {
            if (res.body.result) {
              this.$root.eventHandle.$emit("alert", {
                params: {
                  client_number,
                  trade_type: this.trade_type,
                  custom_trade_id,
                },
                component: () => import("./alert/unusualReject.vue"),
                style: "width:1100px;height:640px",
                title: `驳回`,
              });
            }else {
              this.$message.error(res.body.msg);
            }
        })
      },
      goodsdetail(d) {
        //商品详情
        this.$root.eventHandle.$emit("creatTab", {
          name: "商品详情",
          component: () =>
            import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
          params: {
            goodsInfo: d,
          },
        });
      },
      refresh() {
        this._getDataList();
      },
      // 监听每页显示数更改事件
      pageChange(pageSize, param) {
        this.pageSize = pageSize;
        Object.assign(this.searchParam, param);
        this._getDataList();
      },
      // 监听页数更改事件
      currentPageChange(page, param) {
        this.pageNow = page;
        Object.assign(this.searchParam, param);
        this._getDataList();
      },
      // 查询所有客户列表
      query(param) {
        Object.assign(this.searchParam, param);
        this._getDataList();
      },
      save() {
        this.$refs.formcreate.save();
      },
      detail(d) {
        if (this.trade_type === "ORIGINAL") {
          this.$root.eventHandle.$emit("creatTab", {
            name: "订单详情",
            component: () =>
              import("@components/dz_customer/clientInfo/clientInfo.vue"),
            params: {
              customerInfo: d,
            },
          });
        }
      },
      _getDataList() {
        // 正单和补单的列表查询不一样
        var url ="/custom-web/api/customSysTrade/getErrorList";
        let data = Object.assign(this.searchParam, {
          trade_type: this.trade_type,
        });
        data.page = {
          length: this.pageSize,
          pageNo: this.pageNow,
        };
        let refreshBtn = this.btns.filter((item) => item.txt === "刷新")[0];
        refreshBtn.loading = true;
        this.ajax.postStream(
          url,
          data,
          (d) => {
            if (d.body.result) {
              let content = d.body.content || {};
              const listLength = _.get(content, "list.length");
              for (var i = 0; i < listLength; i++) {
                content.list[i].create_time1 = fn.dateFormat(
                  content.list[i].create_time,
                  "yyyy-MM-dd"
                );
                content.list[i].client_status_value =
                  content.list[i].client_status;
                content.list[i].client_status = getClientStatus(
                  content.list[i].client_status
                );
              }
              this.customerList = content.list || [];
              this.pageTotal = content.count || 0;
            } else {
              this.$message({
                message: d.body.msg,
                type: "error",
              });
            }
  
            refreshBtn.loading = false;
          },
          (err) => {
            this.$message.error(err);
            refreshBtn.loading = false;
          }
        );
      },
      getStatus() {
        // 正单和补单的列表查询不一样
        var url ="/custom-web/api/customSysTrade/getErrorList?permissionCode=DZ_ERROR_ALLRETRY";
        let data = Object.assign(this.searchParam, {
          trade_type: this.trade_type,
        });
        data.page = {
          length: this.pageSize,
          pageNo: this.pageNow,
        };
       
        this.ajax.postStream(
          url,
          data,
          (d) => {
            if (d.body.result) {
              let content = d.body.content || {};
              this.retryStatus = content.result;
              
              this.btns.push({
                type: "primary",
                txt: "全部重试",
                loading: false,
                show:true,
                click: () => {
                  this.allRetry();
                },
              },)
              
            } else {
             
            }
  
           
          },
        );
      },
       allRetry() {
        
        var url ="/custom-web/api/customGoods/allRetry?permissionCode=DZ_ERROR_ALLRETRY";
        let data ={};
       
        this.ajax.postStream(
          url,
          data,
          (d) => {
            if (d.body.result) {
              this.$message({
                message: d.body.msg,
                type: "success",
              });
              this.refresh();
            } else {
              this.$message({
                message: d.body.msg,
                type: "error",
              });
            }
  
            refreshBtn.loading = false;
          },
          (err) => {
            this.$message.error(err);
            refreshBtn.loading = false;
          }
        );
      },
      getQueryItems() {
        this.queryItems = [
          {
            cols: [
              {
                formType: "elInput",
                prop: "client_number",
                label: "订单号",
              },
              {
                formType: "myInput",
                prop: "client_mobile",
                label: "客户电话",
                type: "string",
                maxlength: 11,
                event: {
                  input(v, col) {
                    col.value = v.replace(/\D/g, "");
                  },
                },
              },
              { formType: "elInput", prop: "client_name", label: "客户名称" },
              { formType: "elInput", prop: "designer_name", label: "设计师" },
              // {formType: 'elInput', prop: 'client_address', label: '客户地址'},
              {
              formType: "elDatePicker",
              prop: "create_time",
              props: ["start_create_date", "end_create_date"],
              label: "建档日期",
              type: "daterange",
              format: "yyyy-MM-dd",
              },
              {
                formType: "selectRange",
                prop: "client_status",
                value: ["ERROR", "ERROR"],
                props: ["client_start_status", "client_end_status"],
                label: "订单状态",
                options: [
                  client_status,
                  client_status
                ],
                span: 12,
                disabled:true
              },
            ],
          },
        ];
      },
      // 撤回
      retry(d) {
        const { client_number } = d;
        this.ajax.postStream(
          "/custom-web/api/customGoods/retry?permissionCode=DZ_ERROR",
          {
            client_number,
          },
          (res) => {
            if (res.body.result) {
              this.$message.success(res.body.msg);
              this.refresh();
            } else {
              this.$message.error(res.body.msg);
            }
          }
        );
      },
    },
    created() {
      this.getQueryItems();
    },
    async mounted() {
      const self = this;
      this.role = await getRole();
      this.getStatus();
      this.tools = [
        {
          type: "primary",
          txt: "重试",
          click: (d) => {
            self.retry(d);
          },
        },
        {
          type: "danger",
          txt: "驳回",
          click: (d) => {
            self.reject(d);
          },
          show(d) {
            let flag = false;
            let selfRole = new Set(['ADMINISTRATOR',"ZBDZFZR"])
            self.role.forEach(item=>{
              if(selfRole.has(item)){
                flag = true;
              }
            })
            return flag;
           
          }
        },
      ];
      this._getDataList();
    },
    beforeDestroy() {
    },
  };
  </script>
  <style scoped>
  /*  */
  .search-content {
    border: 1px #aaa solid;
    margin: 10px auto;
    padding: 10px 40px;
    line-height: 30px;
  }
  .searchBox {
    margin: 0px auto;
    height: 99%;
    width: 80%;
    display: flex;
    flex-direction: column;
  }
  </style>
  