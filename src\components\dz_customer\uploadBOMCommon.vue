<template>
  <!-- 下推BOM公共列表 -->
  <div class="searchBox" style="width: 100%">
    <div class="search-content">
      <form-create
        :formData="queryItems"
        @save="query"
        savetitle="查询"
        ref="formcreate"
      ></form-create>
    </div>

    <dz-list2
      :tools="rowTools"
      :rowColData="rowColData"
      :data="customerList"
      :colData="colData"
      :pageTotal="pageTotal"
      :btns="btns"
      showTools
      @page-size-change="pageChange"
      @current-page-change="currentPageChange"
      ref="dzList2"
      countUrl="/afterSale-web/api/forced/closure/query/count"
      @count-off="countOff"
      :showCount="showCount"
    >
    </dz-list2>
    <image-admin
      ref="admin"
      :params="adminParams"
      style="display: none"
    ></image-admin>
  </div>
</template>

<script>
import fn from "@/common/Fn.js";
import _ from "loadsh";
import { getRole, createId, importFile } from "./common/api";
import formCreate from "./components/formCreate/formCreate";
import btnStatus from "./common/mixins/btnStatus";
import { getClientStatus } from "./common/map";
import { getMap } from "./common/clientDictionary";
import dzList2 from "./components/list/list5";
import myTable from "./components/table/table";
import imageAdmin from "./alert/imageAdmin";
import { Message } from "element-ui";
export default {
  props: {
    params: {
      type: Object,
    },
    trade_type: {
      type: String,
      default: "ORIGINAL",
    },
  },
  components: { formCreate, dzList2, myTable, imageAdmin },
  mixins: [btnStatus],
  data() {
    let self = this;
    return {
      showCount: false,
      countOffFlag:false,
      searchParam: {
        client_start_status: "WAITING_PUSH_GOODS",
        client_end_status: "WAITING_PUSH_GOODS",
      },
      role: ["other"],
      queryItems: [],
      customerList: [],
      btns: [
        {
          type: "primary",
          txt: "刷新",
          loading: false,
          show: true,
          click: () => {
            this.refresh();
          },
        },
      ],
      rowTools: [],
      pageNow: 1,
      pageSize: 20,
      pageTotal: 0,
      adminParams: {
        justClick: true,
        multiple: false,
        format: "excel",
      },
      rowColData: Object.freeze([
        {
          label: self.trade_type === "SUPPLY" ? "补单商品号" : "商品号",
          prop: "goods_id",
          width: "130",
          redirectClick(d) {
            self.goodsdetail(d);
          },
        },
        {
          label: "物料编码",
          prop: "material_number",
          width: "130",
        },
        {
          label: "商品名称",
          prop: "message",
          width: "130",
        },
        {
          label: "建档日期",
          prop: "create_time",
          filter: "date",
          formats: "yyyy-MM-dd",
          width: "90",
        },
        {
          label: "商品状态",
          prop: "goods_status_cn",
          width: "60",
        },
      ]),
      colData: Object.freeze([
        {
          label: self.trade_type === "SUPPLY" ? "补单号" : "订单号",
          prop: "client_number",
          width: "100",
          redirectClick(d) {
            self.detail(d);
          },
        },
        {
          label: "客户名",
          prop: "client_name",
          width: "200",
        },
        {
          label: "手机",
          prop: "client_mobile",
          width: "90",
          format: "hidePhoneNumber",
        },
        // {
        //   label: '客户地址',
        //   prop: 'client_address',
        //   width: 'auto'
        // },
        {
          label: "建档日期",
          prop: "create_time1",
          width: "90",
        },
        {
          label: "建档人",
          prop: "shopping_guide_name",
          width: "60",
        },
        {
          label: "设计师",
          prop: "designer_name",
          width: "60",
        },
        {
          label: "状态",
          prop: "client_status",
          width: "auto",
        },
      ]),
    };
  },
  methods: {
    goodsdetail(d) {
      //商品详情
      this.$root.eventHandle.$emit("creatTab", {
        name: this.trade_type === "SUPPLY" ? "补单商品详情" : "商品详情",
        component: () =>
          import("@components/dz_customer/goodsInfo/goodsInfo.vue"),
        params: {
          goodsInfo: d,
        },
      });
    },
    setChild_no() {
      createId().then((id) => {
        this.bomId = id;
      });
    },
    bomUploadSuccess(res) {
      if (res.body.code != 1) return;
      let loading = Message({
        message: "数据推送中",
        iconClass: "el-icon-loading",
        duration: 0,
      });
      this.isCanPush = true;
      importFile({
        path: res.body.data,
        client_number: this.rowData.client_number,
      })
        .then((res) => {
          loading.close();
          this.isCanPush = false;
          if (res.result) {
            this.refresh();
          }
          Message({
            message: res.msg ? res.msg : res.result ? "推送成功" : "推送失败",
            type: res.result ? "success" : "error",
          });
        })
        .catch((err) => {
          loading.close();
          this.isCanPush = false;
          Message({
            message: "推送失败",
            type: "error",
          });
        });
    },
    initBom() {
      // 初始化bom
      var self = this;
      this.time = "bm" + new Date().getTime();
      this.setChild_no();
      this.$root.eventHandle.$on(this.time, self.bomUploadSuccess);
      this.rowTools = [
        {
          type: "primary",
          txt: "上传BOM",
          click(d) {
            if (self.isCanPush) {
              Message({
                type: "warning",
                message: "正在推送中，请稍候",
              });
              return;
            }
            // 记录上传单据数据
            self.rowData = d;

            self.adminParams.no = {
              parent_no: d.client_number,
              child_no: "bm" + self.bomId,
              emit: self.time,
            };
            self.$refs.admin.upload();
            self.setChild_no();
          },
          show(d) {
            if (self.role.indexOf("DZ_CSY") == -1) {
              return false;
            } else if (d.client_status_value == "WAITING_PUSH_GOODS") {
              return true;
            } else {
              return false;
            }
          },
        },
        {
          type: "primary",
          txt: "更多",
          click(d) {
            const { client_number, custom_trade_id } = d;
            self.$root.eventHandle.$emit("alert", {
              params: {
                client_number,
                trade_type: "pushBOM",
              },
              component: () => import("./alert/rejectAlert.vue"),
              style: "width:1100px;height:640px",
              title: `订单号：${d.client_number}`,
            });
          },
        },
      ];
    },
    refresh() {
      this._getDataList();
    },
    // 监听每页显示数更改事件
    pageChange(pageSize, param) {
      this.pageSize = pageSize;
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    // 监听页数更改事件
    currentPageChange(page, param) {
      this.pageNow = page;
      Object.assign(this.searchParam, param);
      this._getDataList();
    },
    // 查询所有客户列表
    query(param) {
       let self = this
      Object.assign(this.searchParam, param);
      // this._getDataList();
      new Promise((res,rej)=>{
        this._getDataList(res);
			}).then(()=>{
				if(self.pageNow != 1){
					self.pageTotal = 0;
				}
				self.showCount = false;

			})
    },
    save() {
      this.$refs.formcreate.save();
    },
    detail(d) {
      if (this.trade_type === "ORIGINAL") {
        this.$root.eventHandle.$emit("creatTab", {
          name: "订单详情",
          component: () =>
            import("@components/dz_customer/clientInfo/clientInfo.vue"),
          params: {
            customerInfo: d,
          },
        });
      } else {
        this.$root.eventHandle.$emit("creatTab", {
          name: "补单详情",
          component: () =>
            import("@components/dz_customer/supplement/supplyInfo.vue"),
          params: {
            client_number: d.client_number,
          },
        });
      }
    },
    countOff(){

			let self = this,
			url = "/custom-web/api/customSysTrade/getCustomSysTradeListCount";
			if(!self.customerList.length){
				self.$message.error("当前列表为空，先搜索内容");
				return;
			}
			if(!!self.countOffFlag){
				self.$message.error("请勿重复点击");
				return;
			}
			self.countOffFlag = true;
      let data = {};
      Object.assign(data, this.searchParam, this.initParam);
      data.page = {
        length: this.pageSize,
        pageNo: this.pageNow,
      };
			self.ajax.postStream(url,data,function(response){
					if(response.body.result){
						
						self.pageTotal = response.body.content;
						self.showCount = true;
						self.countOffFlag = false;

					}else{
						self.$message.error(response.body.msg);
					}
				});
		},
    _getDataList() {
      let self = this;
      var url = "/custom-web/api/customSysTrade/getCustomSysTradeList";
      let data = Object.assign(this.searchParam, {
        trade_type: this.trade_type,
      });
      data.page = {
        length: this.pageSize,
        pageNo: this.pageNow,
      };
      let refreshBtn = this.btns.filter((item) => item.txt === "刷新")[0];
      refreshBtn.loading = true;
      this.ajax.postStream(
        url,
        data,
        (d) => {
          if (d.body.result) {
            let content = d.body.content || {};
            const listLength = _.get(content, "list.length");
            for (var i = 0; i < listLength; i++) {
              content.list[i].create_time1 = fn.dateFormat(
                content.list[i].create_time,
                "yyyy-MM-dd"
              );
              content.list[i].client_status_value =
                content.list[i].client_status;
              content.list[i].client_status = getClientStatus(
                content.list[i].client_status
              );
            }
            this.customerList = content.list || [];
            if(!self.showCount){
              self.pageTotal = content.list.length == (self.pageSize+1)? (self.pageNow*self.pageSize)+1:(self.pageNow*self.pageSize);
            }
          } else {
            this.$message({
              message: d.body.msg,
              type: "error",
            });
          }

          refreshBtn.loading = false;
        },
        (err) => {
          this.$message.error(err);
          refreshBtn.loading = false;
        }
      );
    },
    getQueryItems() {
      let c_status = [];
      getMap((map) => {
        let cs = map.client_status.filter((item) => true);
        // cs = cs.slice(0,cs.length-1)
        cs.forEach((item) => {
          c_status.push(item);
        });
      });
      this.queryItems = [
        {
          cols: [
            {
              formType: "elInput",
              prop: "client_number",
              label: this.trade_type === "SUPPLY" ? "补单号" : "订单号",
            },
            {
              formType: "myInput",
              prop: "client_mobile",
              label: "客户电话",
              type: "string",
              maxlength: 11,
              event: {
                input(v, col) {
                  col.value = v.replace(/\D/g, "");
                },
              },
            },
            { formType: "elInput", prop: "client_name", label: "客户名称" },
            { formType: "elInput", prop: "designer_name", label: "设计师" },
            // {formType: 'elInput', prop: 'client_address', label: '客户地址'},
            {
              formType: "elDatePicker",
              prop: "create_time",
              props: ["start_create_date", "end_create_date"],
              label: "建档日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },
            {
              formType: "selectRange",
              prop: "client_status",
              value: ["WAITING_PUSH_GOODS", "WAITING_PUSH_GOODS"],
              props: ["client_start_status", "client_end_status"],
              label: "订单状态",
              options: [c_status, c_status],
              span: 12,
            },
          ],
        },
      ];
    },
  },
  created() {
    this.getQueryItems();
  },
  async mounted() {
    // this.ajax.postStream('/custom-web/api/guideReport/getOrderList',{shop_codes:'B'},(res) =>{
    //         console.log(res)
    //     }, err => {console.log(err)})
    this.role = await getRole();
    this._getDataList();
    this.initBom();
  },
  beforeDestroy() {
    this.$root.eventHandle.$off(this.time, this.bomUploadSuccess);
  },
};
</script>

<style scoped>
.search-content {
  border: 1px #aaa solid;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
.searchBox {
  margin: 0px auto;
  height: 99%;
  width: 80%;
  display: flex;
  flex-direction: column;
}
</style>
