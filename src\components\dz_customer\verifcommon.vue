<!--定制业务审图审价&拆单&拆审通用列表-->
<template>
  <div class="searchBox">
    <div class="search-content">
      <form-create :formData="formData" :btns="false" @save="query" @ready="ready" ref="formcreate"></form-create>
      <div class="form-btns" v-if="btns">
        <el-button type="primary" size="small" @click="save">查询</el-button>
        <el-button type="danger" size="small" @click="resetFields">重置</el-button>
      </div>
    </div>
      
    <dz-list 
      :data='goodsList' 
      :colData='colData' 
      :pageTotal='pageTotal' 
      :btns='btnsFilter'
      :tools ='tools'
      :selection="selection"
      :checkBoxSelectable="checkBoxSelectable"
      :taggelClassName="taggelClassName"
      @page-size-change='pageChange' 
      @current-page-change='currentPageChange' 
      @selection-change-s="(rows) => {$emit('selection-change-s', rows)  }"
      orderNo
      ref='dzList'>
      <!-- <el-checkbox class="rob" v-if="hasRob" v-model="isRob" @change="_getDataList" slot="btns">抢单模式</el-checkbox> -->
    </dz-list>
  </div>
</template>
<script>
  import fn from '@/common/Fn.js'
  import _ from 'loadsh'
  import { getMaterial  } from './common/api'
  import formCreate from './components/formCreate/formCreate'
  import dzList from './components/list/list'
  import btnStatus from './common/mixins/btnStatus'
  import {style, category} from './common/goodsDictionary'
  import { goods_status, goods_status_supply } from './common/goodsStatusDictionary'
  import { getGoodsStatus } from './common/map'
  import { client_status,client_status_supply } from './common/clientDictionary'

  export default {
    components: {formCreate, dzList},
    mixins: [
      btnStatus
    ],
    props: {
      // checkbox是否可点击
      checkBoxSelectable: {
        type: Function
      },
      selection: {
        type: String
      },
      params: {
        type: Object
      },
      url: {
        type: String,
        default: '/custom-web/api/reviewDrawingPrice/getReviewGoodsList'
      },
      btns: {
        type: Array,
        default() {
          return []
        }
      },
      tools: {
        type: Array,
        default() {
          return []
        }
      },
      defaultValue: {
        type: Object,
        default() {
          return {}
        }
      },
      trade_type: {
        type: String,
        default: 'ORIGINAL'
      },
      // 审核阶段 
      verType: {
        type: String,
        default: 'sj'
      },
      // 初始化表格参数
      initParam: {
        type: Object,
        defualt() {
          return {}
        }
      },
      // 抢单模式
      hasRob: {
        type: Boolean,
        default: false
      },
      // 表格行背景类
      taggelClassName: {
        type: Function
      } 
    },
    data() {
      const self = this
      return {
        urgent:[
          {
                "value":"0",
                "label":"否",
                disabeld:false
            },{
                "value":"1",
                "label":"是",
                disabeld:false
            },
        ],
        value: '',
        formData: [],
        createDate:'',
        contractDate:'',
        goodsList: [],
        pageNow: 1,
        pageSize: 20,
        pageTotal: 0,
        tableParam:{
          start_client_satus: self.trade_type === 'ORIGINAL' ? 'WAITING_DISTRIBUTION_DESIGN' : 'IN_DESIGN',//this.defaultValue.client_satus[0],
          end_client_satus: 'COMPLETED',//this.defaultValue.client_satus[1],
          start_goods_satus: this.defaultValue.goods_satus[0],
          end_goods_satus: this.defaultValue.goods_satus[1],
          trade_type: this.trade_type
        },
        colData: [],
        refreshBtn: [
          {
            type: 'primary',
            txt: '刷新',
            loading: false,
            click:() =>{
              this._getDataList()
            }
          }
        ]
      }
    },
    computed: {
      btnsFilter() {
        return this.refreshBtn.concat(this.btns)
      }
    },
    created() {
      Object.assign(this.tableParam, this.initParam)
      this.getColData()
    },
    methods: {
      
      getColData() {
        let self = this
        const [
          goods_id,design_room_name,client_number,client_name,receiver_name,
          client_mobile,client_address,purchase_price,create_time1,creator_name,
          designer_name,split_order_man,goods_status,compact_time1,supply_reason,message,type_code_name

        ] = [
          {
            label: this.trade_type === 'SUPPLY' ? '补单商品号' : '商品号',
            prop: 'goods_id',
            width: '160',
            redirectClick(d) {
              self.goodsdetail(d)
            }, 
          },  {
            label: '空间',
            prop: 'design_room_name',
            width: '70'
          }, {
            label: '订单号',
            prop: 'client_number',
            width: '120',
            redirectClick(d) {
              self.detail(d)
            },
          }, {
            label: '客户名',
            prop: 'client_name',
            width: '70'
          }, {
            label: '收货人',
            prop: 'receiver_name',
            width: '70'
          }, {
            label: '手机',
            prop: 'client_mobile',
            width: '90',
            format: 'hidePhoneNumber'
          }, {
            label: '客户地址',
            prop: 'client_address',
            width: 'auto'
          },
          {
            label: '采购价格',
            prop: 'purchase_price',
            width: '90'
          },{
            label: '建档日期',
            prop: 'create_time1',
            width: '90'
          },
           {
            label: '建档人',
            prop: 'creator_name',
            width: '60'
          }, {
            label: '设计师', 
            prop: 'designer_name',
            width: '60'
          },{
            label: '拆单人', 
            prop: 'split_order_man',
            width: '60'
          },  {
            label: '商品状态',
            prop: 'goods_status',
            width: '120',
          },{
            label: '合同提交时间',
            prop: 'compact_time1',
            width: '120',

          },
          {
            label: '描述',
            prop: 'supply_reason_cn',
            width: '120',
          },
          {
            label: '商品名称',
            prop: 'message',
            width: '120'
          },
          {
            label: "商品类型",
            prop: "type_code_name",
            width: "120"
          }
        ]
        if(this.trade_type === 'ORIGINAL') {
          // 原始订单
          this.colData = [
            goods_id,message,type_code_name ,design_room_name,client_number,client_name,receiver_name,
            client_mobile,client_address,create_time1,creator_name,
            designer_name,split_order_man,goods_status,compact_time1
          ]
        } else {
          // 补单
          goods_id.label = '补单商品号'
          client_number.label = '补单号'
          goods_status.label = '补单商品状态'
          message.label = '补单商品名称'
          console.log(type_code_name)
          this.colData = [
            goods_id,message ,supply_reason,design_room_name,client_number,client_name,receiver_name,
            client_mobile,client_address,create_time1,creator_name,
            designer_name,split_order_man,goods_status,compact_time1, type_code_name
          ]
          if(this.verType === 'sj') {
          this.colData = [
            goods_id,message ,supply_reason,design_room_name,client_number,client_name,receiver_name,
            client_mobile,client_address,create_time1,creator_name,
            designer_name,split_order_man,goods_status, type_code_name
          ]
        }
        }
        console.log(this.colData)
        if(this.verType === 'cs') {
          // 拆审增加采购价格列
          let create_timeIndex = this.colData.findIndex(item => item.prop === 'create_time1')
          this.colData.splice(create_timeIndex, 0, purchase_price);
        }
        
        
      },
      async ready(set) {
          // 获取材质或花色
        let value = {}
        let material_options = []
        let color_options = []
        let self = this;
        
        getMaterial({categoryCode:'CUSTOM_GOODS_MATERIAL'}).then(list=>{
          list.forEach(item => {
            material_options.push({
                label: item.name,
                value: item.tag
            })
          })
          if(value.color && value.material) {
            let tag = material_options.filter(item => item.value == value.material)[0].value 
            getMaterial({
              categoryCode: "CUSTOM_GOODS_COLOR",
              parentCategoryCode:'CUSTOM_GOODS_MATERIAL',
              parentCode: tag
            }).then(l => {
              l.forEach(item => {
                color_options.push({
                  label: item.name,
                  value: item.tag
                })
              })
            })
          }
        })
        let isOriginal = this.trade_type === 'ORIGINAL'
        this.formData = set([
          {
            cols:[
              {formType: 'elInput', prop: 'client_number', label: isOriginal ? '订单号' : '补单号'},
              {formType: 'myInput', prop: 'client_mobile', label: '客户电话', type:'string', maxlength: 11, event:{
                input(v, col){
                 col.value = v.replace(/\D/g, '')
                }
              }},
              {formType: 'elInput', prop: 'client_name', label: '客户名称'},
              {formType: 'elInput', prop: 'designer_name', label: '设计师'},
              {formType: 'elInput', prop: 'client_address', label: '客户地址'},
              {formType: 'elInput', prop: 'type_code_name', label: '商品类型'},
              {formType: 'elInput', prop: 'goods_id', label: isOriginal ? '商品号' : '补单商品号'},
              {formType: 'selectRange', prop: 'client_satus', value: [isOriginal ? 'WAITING_DISTRIBUTION_DESIGN' : 'IN_DESIGN', 'COMPLETED'], props:['start_client_satus', 'end_client_satus'], label: isOriginal ? '订单状态' : '补单状态', options:[ isOriginal ? client_status : client_status_supply, isOriginal ? client_status : client_status_supply]},
              
              {formType: 'selectRange', prop: 'goods_satus', value: this.defaultValue.goods_satus, props:['start_goods_satus', 'end_goods_satus'], label: isOriginal ? '商品状态' : '补单商品状态', options:[ isOriginal ? goods_status : goods_status_supply, isOriginal ? goods_status : goods_status_supply]},
            ]
          }
        ],value)
        //2021.2.20
        if(this.params.pageInfo==='stsj'||this.params.pageInfo==='cs'){
          this.formData[0].cols=this.formData[0].cols.concat({
            formType: 'elInput', prop: 'receiver_name', label: '收货人'
          })
        }
        if(this.params.pageInfo==='stsj' && this.trade_type === 'ORIGINAL' ){
           this.formData[0].cols=this.formData[0].cols.concat({
            formType: 'elSelect', prop: 'is_urgent', label: '是否加急', options:self.urgent
          })
         
          // this.formData[0].cols=this.formData[0].cols.concat( {
          //     formType: "elDatePicker",
          //     prop: "compact_time_begin",
          //     label: "合同提交开始",
          //     type: "date",
          //     format: "yyyy-MM-dd",
          //   },)
            this.formData[0].cols=this.formData[0].cols.concat( {
              formType: "elDatePicker",
              prop: "compact_date",
              props: ["compact_time_begin", "compact_time_end"],
              label: "合同提交日期",
              type: "daterange",
              format: "yyyy-MM-dd",
            },)
            // this.formData[0].cols=this.formData[0].cols.concat( {
            //   formType: "elDatePicker",
            //   prop: "compact_time_end",
            //   label: "合同提交结束",
            //   type: "date",
            //   format: "yyyy-MM-dd",
            // },)
        }
        console.log(this.params.pageInfo,12321321321)
        // 拆审增加审图拆单人员查询条件
        if(this.verType === 'cs') {
          this.formData[0].cols=this.formData[0].cols.concat({
            formType: 'elInput', prop: 'review_user', label: '审图拆单人'
          })
          
        }
      },
      // 监听每页显示数更改事件
      pageChange(pageSize, param) {
        this.pageSize = pageSize
        Object.assign(this.tableParam, param)
        this._getDataList()
      },
      // 监听页数更改事件
      currentPageChange(page, param) {
        this.pageNow = page
        Object.assign(this.tableParam, param)
        this._getDataList()
      },
      // 查询所有客户列表
      query(param) {
        Object.assign(this.tableParam, param)
        this._getDataList()
      },
      save() {
        this.$refs.formcreate.save()
      },
      resetFields() {
        this.$refs.formcreate.resetFields()
      },
      _getDataList() {
        // 是否抢单模式
        var url =  this.url
        // data = data || {}
        let data = Object.assign({}, this.tableParam)

        data.page = {
          length: this.pageSize,
          pageNo: this.pageNow
        }
        let refreshBtn = this.refreshBtn[0]
        refreshBtn.loading = true
        this.ajax.postStream(url, data, (d) => {
          refreshBtn.loading = false
          let content = d.body.content || {}
          const listLength = _.get(content,'list.length')
          if (d.body.result) {
            for(var i=0;i<listLength;i++){
              content.list[i].create_time1 = fn.dateFormat(content.list[i].create_time,'yyyy-MM-dd')
              content.list[i].compact_time1 = fn.dateFormat(content.list[i].compact_time,'yyyy-MM-dd')
              content.list[i].goods_status_value = content.list[i].goods_status
              content.list[i].goods_status = getGoodsStatus(content.list[i].goods_status)
            }
            this.goodsList = content.list || []
            this.pageTotal = content.count || 0
          } else {
            this.$message({
              message: d.body.msg,
              type: 'error'
            })
          }
        }, err => {
          this.$message.error(err)
          refreshBtn.loading = false
        }, this.params.tabName)
      },
      goodsdetail(d) {  //商品详情
        this.$root.eventHandle.$emit('creatTab', {
          name: '商品详情',
          component: () => import('@components/dz_customer/goodsInfo/goodsInfo.vue'),
          params: {
            trade_type: this.trade_type,
            goodsInfo: d
          }
        })
      },
      detail(d) {
        if(this.trade_type === 'ORIGINAL') {
          this.$root.eventHandle.$emit('creatTab', {
            name: '订单详情',
            component: () => import('@components/dz_customer/clientInfo/clientInfo.vue'),
            params: {
              customerInfo: d,
              lastTab: this.params.tabName
            }
          })
        } else {
          this.$root.eventHandle.$emit('creatTab', {
              name: '补单详情',
              component: () => import('@components/dz_customer/supplement/supplyInfo.vue'),
              params: {
                  client_number: d.client_number,
              }
          })
        }
        
      },
    },
    mounted() {
      this._getDataList()
      this.$root.eventHandle.$on('refreshverifyList', this._getDataList)
    },
    beforeDestroy() {
      this.$root.eventHandle.$off('refreshverifyList', this._getDataList)
    }
  }
</script>
<style scoped>
.form-btns {
  text-align: center;
  margin-top: 10px;
}
.search-content {
  border: 1px #aaa solid;
  margin: 10px auto;
  padding: 10px 40px;
  line-height: 30px;
}
.searchBox {
  height: 99%;
  display: flex;
  flex-direction: column;
}
.rob {
 margin-left:10px;
 transform: translateY(1px);
}
</style>
