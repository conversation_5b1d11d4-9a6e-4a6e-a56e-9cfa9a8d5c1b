<template>
<!-- 原始订单审图审价列表 -->
  <div style="height: 99%;">
    <el-tabs v-model="selectTab" @tab-click="tabsChange">
				<el-tab-pane label='全部订单' name='all' class='xpt-flex'>
					 <verifcommon
            ref="list"
            :params = params
            :btns="all_btns"
            :tools="tools"
            @selection-change-s="change"
            url="/custom-web/api/reviewDrawingPrice/getReviewGoodsList"
            :defaultValue="defaultValue"
            :trade_type="trade_type"
            :taggelClassName="taggelClassName"
            selection='checkbox'
            hasRob
            ></verifcommon>
				</el-tab-pane>
        <el-tab-pane label='我的待办' name='myList' class='xpt-flex'>
					 <verifcommon
            v-if="initMyList"
            ref="myList"
            :params = params
            :initParam="{ operate_phase: 'review', reject_status: 'REJECT_WAITING_SPLIT_ORDER' }"
            :btns="btns"
            :tools="myListTools"
            url="/custom-web/api/customGoods/getMyGoodsList"
            :defaultValue="defaultValue"
            :trade_type="trade_type"
            :taggelClassName="taggelClassName"
            ></verifcommon>
				</el-tab-pane>
			</el-tabs>
    <!-- <verifcommon
    ref="list"
    :params = params
    :btns="btns"
    :tools="tools"
    url="/custom-web/api/reviewDrawingPrice/getReviewGoodsList"
    :defaultValue="defaultValue"
    :trade_type="trade_type"
    ></verifcommon> -->
  </div>
</template>

<script>
import verifcommon from './verifcommon'
import { getRole,changeOrder,getOrders } from './common/api'
import {popup} from "./alert/alert";
export default {
  components: {
    verifcommon
  },
  props: {
    params: {
      type: Object
    },
    trade_type: {
      type: String,
      default: 'ORIGINAL'
    }
  },
  data() {
    const self = this;
    return {
      orderDisabled:false,
      orderSelect:[],
      // 初始化我的订单
      initMyList: false,
      //标签值
      selectTab: 'all',  
      defaultValue: {
        client_satus: ['SIGNED_CONTRACT_WAITING_VERIFY','IN_VERIFY'],
        goods_satus: ['WAITING_AUDIT','IN_AUDIT']
      },
      tools:[],
      role:["other"],
      btns: [],
      all_btns: []
    }
  },
  computed: {
    myListTools() {
      return this.tools.filter(item => item.key !== 'zhuangdan')
    }
  },
  methods:{
    change(list){
      this.orderSelect = list;
    },

    taggelClassName(row) {
    /**
     * @description: 设置行背景颜色
     * @param {*}
     * @return {*}
     */  
    if(this.trade_type !== "ORIGINAL"){
      return;
    }
    let data = __AUX.get('dz_split_store_logo');
			this.resultdata=[];
			// console.log(data);
			data.forEach(item=>{
				if(item.code == row.shop_code){
					row.color_code = 'green'
				};
			});
    let color = {
      // 红色
      '#F56C6C' : 'danger-row',
      // 蓝色
      '#409EFF' : 'primary-row',
      // 黄色
      '#E6A23C' : 'warning-row',
      'green' : 'green-row',
    }

      return row.color_code ? color[row.color_code] : ''
    },
    tabsChange(tab) {
    /**
     * @description: 标签页切换
     * @param {*}
     * @return {*}
     */  
      tab.name === 'myList' && (this.initMyList = true)
    },
    refresh() {
      this.$refs.list && this.$refs.list._getDataList()
      this.$refs.myList && this.$refs.myList._getDataList()
    },
    lock(d) {
        let self = this
        let data = {}
        const { custom_goods_id, client_number, goods_id } = d
        data = {
          custom_goods_id, client_number, goods_id
        } 
        !this.request && (this.request={})
        if(this.request[custom_goods_id]) {
          this.$message({
            type: 'warning',
            message: '请等待上一次请求结束'
          })
          return
        }
        this.request[custom_goods_id] = true
        this.ajax.postStream('/custom-web/api/reviewDrawingPrice/lockingGoods', data, (res) => {
          res = res.body
          this.$message({
            message: res.msg,
            type:res.result?'success':'error'
          })
          if(res.result) {
            self.$refs.list._getDataList()
          } else {
            this.request[custom_goods_id] = false
          }
        })
      },
  },
  created(){
    // let self = this;
    console.log(this.trade_type,'this.params.pageInfo')
    // stsj审图审价
    this.params.pageInfo="stsj"
    const self = this
    this.all_btns =  this.trade_type === 'ORIGINAL' ? [
      {
          type: 'primary',
          txt: '抢单',
          disabled(){
            return self.orderDisabled;
          },
          click() {
            self.orderDisabled = true;
              setTimeout(()=>{
                  getOrders({}, false, true).then(res => {
                      self.orderDisabled = false;
                      res.result && self.$refs.list._getDataList()
                  })
              },50);
          }
      },
      {
          type: 'primary',
          txt: '批量转单',
          click() {
           
            let obj = [];
            
             popup.transforPeople(self, (data) => {
               self.orderSelect.forEach(d => {
                obj.push(
                  {
                    client_number: d.client_number,
                    user_id:  String(data.user_id),
                    goods_id: d.goods_id,
                    custom_goods_id: d.custom_goods_id,
                    operate_phase : "review"
                  }
                )
              });
              changeOrder(obj, false, true).then(() => {
                self.$refs.list._getDataList()
              })
            })
            
            // getOrders({}, false, true).then(res => {
            //   res.result && self.$refs.list._getDataList()
            // })
          }
      }
    ] : [{
          type: 'primary',
          txt: '批量转单',
          click() {
           
            let obj = [];
            
             popup.transforPeople(self, (data) => {
               self.orderSelect.forEach(d => {
                obj.push(
                  {
                    client_number: d.client_number,
                    user_id:  String(data.user_id),
                    goods_id: d.goods_id,
                    custom_goods_id: d.custom_goods_id,
                    operate_phase : "review"
                  }
                )
              });
              changeOrder(obj, false, true).then(() => {
                self.$refs.list._getDataList()
              })
            })
            
            // getOrders({}, false, true).then(res => {
            //   res.result && self.$refs.list._getDataList()
            // })
          }
      }]
  },
  async mounted() {
    var self = this
    this.role = await getRole()
    this.tools = [
      {
        type: 'primary',
        txt: '锁定',
        click(d) {
          self.lock(d)
        },
        show(d) {
          if(self.role.indexOf("DZ_SHY") == -1 || self.trade_type === 'ORIGINAL') {
            return false
          } else if(d.goods_status_value == 'WAITING_AUDIT' || d.goods_status_value == 'IN_AUDIT' || d.goods_status_value == 'REJECT_WAITING_SPLIT_ORDER') {
            if(d.is_locking === null ){
              return true
            } else {
                return false
              }
          } else{
            return false
          }
        }
      },
      {
        type: 'warning',
        txt: '审图拆单',
        click: (d) => {
          if(this.trade_type === "ORIGINAL"){
            this.$root.eventHandle.$emit('creatTab', {
              name: '审图拆单',
              component: () => import('@components/dz_customer/alert/verifypic.vue'),
              params: {
                trade_type: this.trade_type,
                goodsInfo:d
              }
            })
          }else{
            this.$root.eventHandle.$emit('creatTab', {
              name: '审图拆单',
              component: () => import('@components/dz_customer/alert/verifypic2.vue'),
              params: {
                trade_type: this.trade_type,
                goodsInfo:d
              }
            })
          }
          
        }, 
        show(d) {
          if(self.role.indexOf("DZ_SHY") == -1) {
            return false
          } else if(d.goods_status_value == 'WAITING_AUDIT' || d.goods_status_value == 'IN_AUDIT' || d.goods_status_value == 'REJECT_WAITING_SPLIT_ORDER') {
            if(d.is_review === 'true' ){
              return true
            } else {
                return false
              }
          } else{
            return false
          }
        }
      },
      {
        type: 'primary',
        txt: '转单',
        key: 'zhuangdan',
        click: (d) => {
          popup.transforPeople(self, (data) => {
            changeOrder([{
              client_number: d.client_number,
              user_id:  String(data.user_id),
              goods_id: d.goods_id,
              custom_goods_id: d.custom_goods_id,
              operate_phase : "review"
            }], false, true).then(() => {
              self.$refs.list._getDataList()
            })
          })
        }, 
        show(d) {
          return ['WAITING_AUDIT', 'IN_AUDIT'].includes(d.goods_status_value) && 
          ( 
            self.role.indexOf("DZ_DDZG") !== -1 ||
            self.role.indexOf("ZBDZFZR") !== -1
          )
        }
      }
    ],
    
    this.$root.eventHandle.$on('verfypic', this.refresh)
  },
  beforeDestroy() {
    this.$root.eventHandle.$off('verfypic', this.refresh)
  }

}
</script>

<style>

</style>
