<!-- 钉钉上级部门弹窗 -->
<template>
  <div class="xpt-flex">
    <xpt-headbar>
		<el-button type='primary' slot='left' size='mini' @click="save">保存</el-button>
		
	</xpt-headbar>
    <el-tree
      class="tree"
      :data="regions"
      :props="props"
      :load="loadNode"
      lazy
      :highlight-current="true"
      @node-click	="getCheckedNodes"
      @check-change="handleCheckChange">
    </el-tree>
  </div>
</template>
<script>
export default {
  data() {
    let self = this;
    return {
      btns: [],
        shopSelect:'',
        regions: [
          {
            label:'营销业务',
          children:[]
          }
        ],
        dingdingDept:[],
        props: {
          label: 'name',
          children: 'deptId',
          
        },
        count: 1
      
    };
  },
  props: ["params"],
   methods: {
     save(){
       if(!this.shopSelect){
         this.$message.error('请选择部门');
         return;
       }
       this.params.callback(this.shopSelect);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
     },
     getCheckedNodes(data){
        // console.log(data, 111, 222);
        this.shopSelect = data;

     },
      handleCheckChange(data, checked, indeterminate) {
      },
      handleNodeClick(data) {
      },
      loadNode(node, callback) {
        console.log(node)

        if(node.level == 0){
          let arr = [];
          // console.log(__AUX.get('dingding_dept'))
          let dingdingDept = __AUX.get('dingding_dept')
          dingdingDept.forEach(item=>{
            if(item.tag == 'N'){
              // arr.push(new Promise((resolve,reject)=>{
              //   this.initDingDingDept({deptId:item.code,name:item.name},resolve)
              // }))
              arr.push(
                {deptId:item.code,name:item.name}
              )
            }
          })
          callback(arr);
          // Promise.all(arr).then((data,ids) => {
          //       // console.log(data,ids)
          //       // data.forEach((item,index)=>{
          //       //   dingdingDept.forEach(ddItem=>{
          //       //     if(ddItem.code==ids[index]){
          //       //       data.name = ddItem.name;
          //       //       data.deptId = ddItem.code;
          //       //     }
          //       //   })
          //       // })
          //       callback(data);
          //   });
          
        }else{
          new Promise((resolve,reject)=>{
          this.getDingDingDeptInfoByDeptId(node.data.deptId,resolve)
            }).then(data=>{
              // this.regions = data
              if(!!data){
                callback&&callback(data)
              }else{
                callback&&callback([]);
              }
            })
        }
        
      
        

        // setTimeout(() => {
        //   var data;
        //   if (hasChild) {
        //     data = [{
        //       name: 'zone' + this.count++
        //     }, {
        //       name: 'zone' + this.count++
        //     }];
        //   } else {
        //     data = [];
        //   }

        //   resolve(data);
        // }, 500);
      },
      initDingDingDept(data,resolve){
        this.ajax.get('/mdm-web/api/topShop/getDingDingDeptInfoByDeptId/'+data.deptId, res=>{
            if(res.body.result){
              let list = res.body.content;
              // return list;
              data.list
             resolve&&resolve(list);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
      },
      getDingDingDeptInfoByDeptId(deptId,resolve){
        this.ajax.get('/mdm-web/api/topShop/getDingDingDeptInfoByDeptId/'+deptId, res=>{
            if(res.body.result){
              let list = res.body.content;
              // return list;
             resolve&&resolve(list,deptId);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
      }
    },
  
  mounted() {
    this.btns = [
      {
        type: "primary",
        txt: "确认",
        click: this.close,
      },
    ];
    this.dingdingDept = __AUX.get('dingding_dept');
    
  },
};
</script>
<style scoped>
  .tree{
    height: 100%;
    overflow: auto;
  }
</style>
