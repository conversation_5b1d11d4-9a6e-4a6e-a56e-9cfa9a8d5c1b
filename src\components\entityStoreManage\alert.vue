<!-- 弹窗选择业态店列表 -->
<template>
  <div class="xpt-flex">
    <xpt-list
      :data="shopList"
      :btns="btns"
      :colData="cols"
      searchPage="cloud_shop_v2"
      :pageTotal="pageTotal"
      :selection="selection"
      :showHead="showHead"
      :isNeedClickEvent="showHead"
      @search-click="searchData"
      @radio-change="radioChange"
      @page-size-change="pageSizeChange"
      @current-page-change="pageChange"
    ></xpt-list>
  </div>
</template>
<script>
export default {
  data() {
    let self = this;
    return {
      btns: [],
      cols: [
        {
          label: "店铺编码",
          prop: "shop_code",
        },
        {
          label: "店铺名称",
          prop: "shop_name",
        },
        {
          label: "店铺分类",
          prop: "shop_type_name",
        },
        {
          label: "店铺分组",
          prop: "shop_group_name",
        },
        {
          label: "店铺地区",
          prop: "shop_area_name",
        },
        {
          label: "关联仓库",
          prop: "warehouse_source_name",
        },
        {
          label: "店铺仓库",
          prop: "shop_warehouse_name",
        },
        {
          label: "导购宝店铺",
          prop: "guide_shop",
        },
        {
          label: "部门",
          prop: "department_source_name",
        },
        {
          label: "店铺状态",
          prop: "shop_status_name",
        },
        {
          label: "网拍店铺",
          prop: "wangpai_shop",
        },
        {
          label: "创建人",
          prop: "creator_name",
        },
        {
          label: "创建日期",
          prop: "create_time",
          format: "dataFormat1",
          width: 130,
        },
        {
          label: "修改人",
          prop: "modifier_name",
        },
        {
          label: "修改日期",
          prop: "modify_time",
          format: "dataFormat1",
          width: 130,
        },
      ],
      //   cloud_shop_v2
      search: {
        page_name: "topShopDetal",
        where: [],
        page: {
          length: self.pageSize,
          pageNo: 1,
        },
      },
      shopList: [],
      selectData: "",
      pageTotal: 0,
      selection: "radio",
      showHead: false,
      refreshBtnStatus: false,
    };
  },
  props: ["params"],
  methods: {
    radioChange(data) {
      this.selectData = data;
    },
    close() {
      if (!this.selectData) {
        this.$message.error("请先选择一个店铺");
        return;
      }
      this.params.callback(this.selectData);
      this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
    },
    searchData(obj, resolve) {
      this.search.where = obj;
      this.selectData = null;
      this.getShopList(resolve);
    },
    pageSizeChange(pageSize) {
      this.search.page.length = pageSize;
      this.selectData = null;
      this.getShopList();
    },
    pageChange(page) {
      this.search.page.pageNo = page;
      this.selectData = null;
      this.getShopList();
    },
    getShopList(resolve) {
      this.refreshBtnStatus = true;

      var postData = JSON.parse(JSON.stringify(this.search));

      if (this.params.setWhere) {
        this.params.setWhere(postData); //在setWhere方法里面直接修改postData对象内容
      }

      this.ajax.postStream(
        "/mdm-web/api/topShop/getShopWithOutTopShop",
        postData,
        (d) => {
          if (d.body.result && d.body.content) {
            this.pageTotal = d.body.content.count;
            this.shopList = d.body.content.list || [];
          } else {
            this.$message.error(d.body.msg || "");
          }
          this.refreshBtnStatus = false;
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
          this.refreshBtnStatus = false;
        }
      );
    },
  },
  mounted() {
    this.btns = [
      {
        type: "primary",
        txt: "确认",
        click: this.close,
      },
    ];
    this.showHead = true;
    this.getShopList();
  },
};
</script>
