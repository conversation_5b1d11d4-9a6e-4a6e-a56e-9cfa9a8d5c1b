<!-- 实体店新增、编辑 -->
<template>
  <div class="xpt-flex">
    <el-row class="xpt-top" :gutter="40">
      <el-col :span="20">
        <el-button type="primary" size="mini" @click="getDetail"
          >刷新</el-button
        >
        <el-button type="primary" size="mini" @click="saveBefore('ruleForm')" 
          >保存</el-button
        >
      </el-col>
    </el-row>
    <div>
      <el-tabs v-model="firstTab">
        <el-tab-pane label="基本信息" name="basicInfo" class="xpt-flex">
          <el-form
            :model="form"
            :rules="rules"
            ref="ruleForm"
            label-position="right"
            label-width="140px"
          >
            <el-col :span="8">
              <el-form-item label="店铺编码：" prop="topShopCode">
                <el-input
                  v-model="form.topShopCode"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="客户：">
                <el-input
                  v-model="form.customerId"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="店铺分类：" prop="topShopType">
                <el-select
                  size="mini"
                  v-model="form.topShopType"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, i) in shopTypes"
                    :key="i"
                    :label="item.name"
                    :value="item.val"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="实体店名称：" prop="topShopName">
                <el-input v-model="form.topShopName" size="mini"></el-input>
              </el-form-item>
              <el-form-item label="客户名称：">
                <el-input
                  v-model="form.customerName"
                  size="mini"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="店铺状态：">
                <el-select
                  size="mini"
                  v-model="form.topShopStatus"
                  placeholder="请选择"
                  disabled
                >
                  <el-option label="营业中" value="OPEN"> </el-option>
                  <el-option label="已关闭" value="CLOSED"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="实体店店态：" prop="topShopStyle">
                <el-select
                  size="mini"
                  v-model="form.topShopStyle"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, i) in types"
                    :key="i"
                    :label="item.name"
                    :value="item.val"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="其他信息" name="otherInfo" class="xpt-flex">
          <el-form label-position="right" label-width="140px">
            <el-col :span="8">
              <el-form-item label="创建人：">{{
                form.creatorName
              }}</el-form-item>
              <el-form-item label="创建时间：">{{
                form.createTime | dataFormat1
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="修改人：">{{
                form.modifierName
              }}</el-form-item>
              <el-form-item label="修改时间：">{{
                form.modifyTime | dataFormat1
              }}</el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="xpt-flex__bottom" id="bottom" v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane label="业态店信息" name="goodsInfo" class="xpt-flex">
          <el-row class="xpt-top" :gutter="40">
            <el-col :span="20">
              <el-button type="primary" size="mini" @click="addRow"
                >新增行</el-button
              >
            </el-col>
          </el-row>
          <el-table
            ref="multipleTable"
            :data="storesData"
            border
            tooltip-effect="dark"
            style="width: 100%"
          >
            <el-table-column label="业态店编码" width="180" prop="shop_name">
              <template slot-scope="scope">
                <el-input
                  style="width: 120px !important"
                  v-model="scope.row.shop_code"
                  size="mini"
                  :disabled="!!scope.row.parent_shop_code"
                  icon="search"
                  :on-icon-click="() => handleIconClick(scope.row)"
                  readonly
                  placeholder="选择店铺"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="shop_name" label="业态店名称" width="120">
            </el-table-column>
            <el-table-column
              prop="main_sale_categorie"
              label="主营类目"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ c_filter(scope.row.main_sale_categorie) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="customer_source_name"
              label="客户"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="customerName"
              label="是否默认主店铺"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <el-checkbox
                  :disabled="type == 'edit'"
                  @change="() => checkedChange(scope.row)"
                  v-model="scope.row.checked"
                ></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column
              prop="dept_name"
              label="钉钉上级部门"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                 <el-input
                  style="width: 140px !important"
                  v-model="scope.row.parent_dept_name"
                  size="mini"
                  icon="search"
                  :on-icon-click="() => deptIconClick(scope.row)"
                  readonly
                  placeholder="选择钉钉上级部门"
                ></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
export default {
  props: ["params"],
  data() {
    var self = this;
    return {
      storesData: [],
      storesDataCopy: [],
      operateTotal: 0,
      form: {
        topShopCode: "", //店铺编码
        topShopName: "", //实体店名称
        customerId: "", //客户
        customerName: "", //客户名称
        topShopStatus: "", //店铺状态
        topShopType: "", //店铺分类
        topShopStyle: "", //实体店店态
        mainShopId: "",
      },
      rules: {
        topShopCode: {
          required: true,
          message: "店铺编码是必须的",
          trigger: "change",
        },
        topShopName: [
          { required: true, message: "请输入实体店名称", trigger: "blur" },
          { min: 0, max: 100, message: "", trigger: "blur" },
        ],
        topShopStyle: {
          required: true,
          message: "实体店店态是必须的",
          trigger: "change",
        },
        topShopType: {
          required: true,
          message: "店铺分类是必须的",
          trigger: "change",
        },
      },
      firstTab: "basicInfo",
      secondTab: "goodsInfo",
      currentIndex: null,
      types: [], //实体店店态
      mainTypes: [], //主营类目
      shopTypes: [], //店铺分类
      type: "add",
    };
  },
  methods: {
    c_filter(val) {
      if (!!this.mainTypes.find((item) => item.val == val)) {
        return this.mainTypes.find((item) => item.val == val).name;
      } else {
        return "";
      }
    },
    checkedChange(row) {
      if (!!row.shop_id) {
        this.storesData.forEach((elem) => {
          if (elem.id == row.id) {
            elem.checked = true;
            this.form.topShopCode = "ST" + row.shop_code;
            this.form.customerId = row.customer_source_id;
            this.form.mainShopId = row.shop_id;
            this.form.customerName = row.customer_source_name;
          } else {
            elem.checked = false;
          }
        });
      } else {
        row.checked = false;
        return this.$message.error("请先选择店铺！");
      }
    },
    deptIconClick(row) {
      // if (!!row.parent_shop_code) {
      //   return;
      // }
      
      let self = this;
      let currentIndex = self.storesData.findIndex(
              (item) => item.shop_id == row.shop_id
            );

      console.log(currentIndex);

      self.$root.eventHandle.$emit("alert", {
        params: {
          callback: (obj) => {
            // if (
            //   self.storesData.findIndex((item) => item.id == obj.shop_id) !== -1
            // ) {
            //   return self.$message.error("请勿重复添加！");
            // }
            console.log(obj,row)
            // row.dept_id = obj.deptId;
            // row.dept_parent_id = obj.parentId;
            // row.dept_name = obj.name;
            self.$set(self.storesData, currentIndex, {
              ...self.storesData[currentIndex],
              dept_parent_id: obj.deptId,
              parent_dept_name:obj.name
            });
            // self.$set(self.storesData, self.currentIndex, {
             
            // });
          },
        },
        component: () => import("./DingDingDeptInfoByDep"),
        style: "width:800px;height:500px",
        title: "钉钉部门列表",
      });
    },
    handleIconClick(row) {
      if (!!row.parent_shop_code) {
        return;
      }
      this.currentIndex = this.storesData.findIndex(
        (item) =>  item.fakeId == row.fakeId
      );
      let self = this;
      this.$root.eventHandle.$emit("alert", {
        params: {
          callback: (obj) => {
            if (
              self.storesData.findIndex((item) => item.id == obj.shop_id) !== -1
            ) {
              return self.$message.error("请勿重复添加！");
            }
            self.$set(self.storesData, self.currentIndex, {
              ...self.storesData[self.currentIndex],
              id: obj.shop_id,
              shop_id: obj.shop_id,
              shop_code: obj.shop_code,
              shop_name: obj.shop_name,
              customer_source_id: obj.customer_source_id,
              customer_source_name: obj.customer_source_name,
              main_sale_categorie: obj.main_sale_categorie,
              parent_shop_id: obj.parent_shop_id,
              parent_shop_code: obj.parent_shop_code,
              parent_shop_name: obj.parent_shop_name,
              delete_flag: 0, //未删除
            });
          },
        },
        component: () => import("./alert"),
        style: "width:800px;height:500px",
        title: "业态店列表",
      });
    },
    addRow() {
      let obj = {
        id: "",
        fakeId: this.getFakeId(), //点击时找index用
        shop_id: "",
        shop_code: "",
        shop_name: "",
        customer_source_name: "",
        delete_flag: 0,
        main_sale_categorie: "",
        customer_source_id: "",
        customer_source_name: "",
        arent_shop_id: "",
        parent_shop_code: "",
        parent_shop_name: "",
        checked: false,
      };
      this.storesData.push(obj);
    },
    getFakeId() {
      let number = "row_id" + Math.random();
      if (this.storesData.findIndex((item) => item.fakeId == number) !== -1) {
        return this.getFakeId();
      } else {
        return number;
      }
    },
    // 关闭标签页
    closeTab() {
      this.$root.eventHandle.$emit("removeTab", this.params.tabName);
    },
    // 获取主营类目
    getMainTypes() {
      let url = `/mdm-web/api/enumeration/1010112/items`;
      this.ajax.get(url, (res) => {
        if (res.body.result && res.body.content) {
          this.mainTypes = res.body.content || [];
        }
        if (res.body.result == "error") {
          this.$message({
            type: res.body.result,
            message: res.body.msg,
          });
        }
      });
    },
    // 获取实体店店态
    getTypes() {
      let url = `/mdm-web/api/enumeration/CLOUD_TOP_SHOP_STYLE/items`;
      this.ajax.get(url, (res) => {
        if (res.body.result && res.body.content) {
          this.types = res.body.content || [];
        }
        if (res.body.result == "error") {
          this.$message({
            type: res.body.result,
            message: res.body.msg,
          });
        }
      });
    },
    // 获取店铺分类
    getShopTypes() {
      let url = `/mdm-web/api/enumeration/6d0f5480-0ec1-4b01-8ce8-3868c04d13ef/items`;
      this.ajax.get(url, (res) => {
        if (res.body.result && res.body.content) {
          this.shopTypes = res.body.content || [];
        }
        if (res.body.result == "error") {
          this.$message({
            type: res.body.result,
            message: res.body.msg,
          });
        }
      });
    },
    // 保存
    saveBefore(formName) {
      if (!this.storesData.length) {
        return this.$message.error("请添加业态店信息！");
      }
      if (
        this.storesData.filter((item) => !item.shop_id).length ==
        this.storesData.length
      ) {
        return this.$message.error("请添加业态店信息！");
      }
      if (this.form.mainShopId == "" && this.type == "add") {
        return this.$message.error("请选择默认主店铺");
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            topShopCode: this.form.topShopCode,
            topShopName: this.form.topShopName,
            topShopStyle: this.form.topShopStyle,
            topShopType: this.form.topShopType,
            mainShopId: this.form.mainShopId,
            cloudShopV2VoList: this.storesData
              .map((item) => {
                return {
                  id: item.shop_id,
                  parent_shop_id: item.parent_shop_id,
                  parent_shop_code: item.parent_shop_code,
                  parent_shop_name: item.parent_shop_name,
                  delete_flag: item.delete_flag,
                  dept_id: item.dept_id,
                  dept_parent_id: item.dept_parent_id,
                  dept_name: item.dept_name,
                  shop_code: item.shop_code,
                };
              })
              .filter((item) => !item.shop_id),
          };
          if (this.type == "edit") {
            params.id = this.params.id;
          }
          this.$request("/mdm-web/api/topShop/saveOrUpdate?permissionCode=TOP_SHOP_SAVE", params).then(
            (res) => {
              if (res.result) {
                this.$message.success(res.msg);
                this.$root.eventHandle.$emit("removeTab", this.params.tabName);
                this.$root.eventHandle.$emit("discountAdd");
              } else {
                this.$message.error(res.msg);
              }
            }
          );
        } else {
          return false;
        }
      });
    },
    // 获取详情
    getDetail() {
      let params = {
        where: [],
        page_size: "",
        page_no: "",
        topShopCode: this.form.topShopCode,
        page_name: "",
      };
      this.$request("/mdm-web/api/topShop/listDetail", params).then((res) => {
        if (res.result) {
          this.storesData = res.content.list;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    initDetail() {
      this.form = {
        topShopCode: this.params.topShopCode, //店铺编码
        topShopName: this.params.topShopName, //实体店名称
        customerId: this.params.customerId, //客户
        customerName: this.params.customerName, //客户名称
        topShopStatus: this.params.topShopStatus, //店铺状态
        topShopType: this.params.topShopType, //店铺分类
        topShopStyle: this.params.topShopStyle, //实体店店态
        mainShopId: this.params.mainShopId,
        creatorName: this.params.creatorName,
        createTime: this.params.createTime,
        modifierName: this.params.modifierName,
        modifyTime: this.params.modifyTime,
      };
      this.type = this.params.type;
      this.getDetail();
    },
  },

  created() {
    this.getTypes();
    this.getMainTypes();
    this.getShopTypes();
    let { topShopCode } = this.params;
    if (!!topShopCode) {
      this.initDetail();
    }
  },
  mounted: function () {
    var self = this;
  },
  computed: {},
};
</script>
<style type="text/css" scoped>
.orderinfo {
  border-bottom: 1px solid #d1dbe5;
}
.orderinfo div {
  padding: 0 10px;
  display: block;
  height: 24px;
  line-height: 24px;
  color: #20a0ff;
  border-bottom: 3px solid #20a0ff;
  display: inline-block;
}
</style>
