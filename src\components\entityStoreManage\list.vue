<!-- 实体店列表 -->
<template>
  <mdm-list
    ref="list"
    :data="tableData"
    :btns="btns"
    :colData="cols"
    :pageTotal="totalPage"
    searchPage="topShop"
    searchHolder="请输入查询条件"
    row-key="id"
    @search-click="preSearch"
    @selection-change="handleSelectionChange"
    @page-size-change="handleSizeChange"
    @current-page-change="handleCurrentChange"
  >
  </mdm-list>
</template>
<script>
import MdmList from "@/components/mdm/components/table/MdmList2";
export default {
  props: ["params"],
  components: { MdmList },
  data() {
    var self = this;
    return {
      uploadUrl: "/material-web/api/shopAreaDistribution/importExcelByAsync",
      btns: [
        {
          type: "primary",
          txt: "刷新",
          click() {
            self.getList();
          },
        },
        {
          type: "primary",
          txt: "新增",
          click: self.addFun,
        },
      ],
      cols: [
        {
          label: "实体店编码",
          prop: "topShopCode",
          width: 150,
          redirectClick(row) {
            self.addFun(row);
          },
        },
        {
          label: "实体店名称",
          prop: "topShopName",
          width: 120,
        },
        {
          label: "客户编码",
          prop: "customerNumber",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "创建人",
          prop: "creatorName",
          width: 140,
        },
        {
          label: "创建时间",
          prop: "createTime",
          format: "dataFormat1",
          width: 140,
        },
        {
          label: "修改人",
          prop: "modifierName",
        },
        {
          label: "修改时间",
          prop: "modifyTime",
          format: "dataFormat1",
        },
      ],
      searchObj: {
        page_size: self.pageSize,
        page_no: 1,
      },
      searchWhere: [],
      totalPage: 0,
      searchInput: "",
      tableData: [],
      selectId: [],
      searchKey: "",
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    var self = this;
    self.$root.eventHandle.$on("discountAdd", (d) => {
      self.getList();
    });
  },
  destroyed() {
    this.$root.offEvents("discountAdd");
  },
  methods: {
    getList(resolve) {
      this.$request("/mdm-web/api/topShop/list", {
        ...this.searchObj,
        page_name: "topShop",
        where: this.searchWhere,
      })
        .then((res) => {
          if (res.result) {
            this.tableData = res.content ? res.content.list : [];
            this.totalPage = res.content.count;
          } else {
            this.$message.error(res.msg);
          }
          resolve && resolve();
        })
        .catch((err) => {
          resolve && resolve();
        })
        .finally(() => {
          resolve && resolve();
        });
    },
    // 新增按钮
    addFun(obj) {
      let type = obj.topShopCode ? "edit" : "add";
      this.$root.eventHandle.$emit("creatTab", {
        name: obj.topShopCode ? "编辑实体店" : "新增实体店",
        params: { ...obj, type },
        component: () => import("./detail.vue"),
      });
    },
    // 多选触发函数
    handleSelectionChange(selectArr) {
      this.selectId = [];
      selectArr.map((v) => {
        this.selectId.push(v.position_id);
      });
    },
    preSearch(txt, resolve) {
      this.searchWhere = txt;
      this.getList(resolve);
    },
    initSearchData() {
      if (!this.searchKey) this.searchObj.act_name = "";
      else this.searchObj.act_name = this.searchKey;
    },
    searchFun() {
      this.ajaxPost(
        "actList?permissionCode=DISCOUNT_ACTIVITY_QUERY",
        this.searchObj,
        "",
        "search"
      );
    },
    handleSizeChange(val) {
      this.searchObj.page_size = val;
      // this.initSearchData()
      this.getList();
      // this.searchFun();
    },
    handleCurrentChange(val) {
      this.searchObj.page_no = val;
      // this.initSearchData()
      this.getList();
      // this.searchFun();
    },
    toEditFun(id) {
      this.$root.eventHandle.$emit("creatTab", {
        name: "编辑优惠活动",
        params: { act_id: id },
        component: () => import("@components/discount/add.vue"),
      });
    },
    ifGlobalFun(val) {
      if (val == 1) {
        return "全局";
      } else {
        return "店铺";
      }
    },
  },
};
</script>
