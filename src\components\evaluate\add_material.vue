<!-- 责任分析单-问题补充 -->
<template>
<div class="xpt-flex">
	<el-row	class='xpt-top'	:gutter='40'>
		<el-button type='primary' size='mini' @click='() => submit()' :disabled="!selectObject">确认</el-button>
		<el-col class='xpt-align__right' :span='20' style="float:right;">
		
			<el-select v-model="search.limit_field" @change="fieldChange" size='mini' style="width:150px;">
				<el-option 
					v-for="(value,key) in filterObj" 
					:key="key.toString()" 
					:label="value.toString()"
					:value="key.toString()">
				</el-option>
			</el-select>
			<el-select v-model="search.operator" size='mini' style="width:100px;">
				<el-option label="等于" value="="></el-option>
				<el-option label="包含" value="%" v-show="['material_name','material_specification','source_material_name'].includes(search.limit_field)"></el-option>
			</el-select>
			<el-input placeholder="请输入查询值" size='mini' v-model="search.field_value"></el-input>
			<el-button type='primary' @click="getSearch" size='mini'>查询</el-button>
		</el-col>
	</el-row>
	<xpt-list
		:data='roleList'
		:showHead="false"
		:colData='colDataObj'
		:pageTotal='pageTotal'
		selection="radio"
		@search-click='searchFun'
		@radio-change='select'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
		@row-dblclick="submit"
	></xpt-list>
</div>
</template>

<script>
import Fn from '@common/Fn.js'
export default {
	props: ['params'],
	data (){
		return {
			roleList: [],
			selectObject: null,
			pageTotal: 0,
			filterObj: {
				material_name: '物料名称',
                material_number: '物料编码',
                material_specification: '规格描述',
                source_material_number: '来源商品编码',
                source_material_name: '来源商品名称',
                merge_trade_no: '合并单号',
                batch_trade_no: '批次单号',
                sys_trade_no: '销售单号',
			},
			search:{
				customer_name:'customer_name',
				begin_out_stock_time:'',
				field_value: "",
				limit_field: "material_number",
				operator: "%",
				page_no: 1,
				page_size: 50,
				if_need_page :"Y"
			},
			initSearch: {},
			colDataObj: [{
					label: '物料编码',
					prop: 'material_number',
					width: 130,
				}, {
					label: '物料名称',
					prop: 'material_name',
				}, {
					label: '规格描述',
					prop: 'material_specification',
					width: 160,
				}, {
					label: '来源商品编码',
					prop: 'source_material_number',
					width: 140,
				}, {
					label: '来源商品名称',
					prop: 'source_material_name',
				}, {
					label: '批次单号',
					prop: 'batch_trade_no',
				}, {
					label: '合并单号',
					prop: 'merge_trade_no',
				}, {
					label: '销售单号',
					prop: 'sys_trade_no',
				}],
		}
	},
	methods: {
        fieldChange(){
            this.search.operator = '='
        },
		getSearch (){
			this.search.customer_name = this.params.data.nick
			this.search.begin_out_stock_time =new Date(new Date().getTime()-1000*60*60*24*365*3); 
			this.ajax.postStream('/order-web/api/appraise/liability/chooseMaterial', this.search, response => {
				if(response.body.result){
					this.roleList = response.body.content.list
					this.pageTotal = response.body.content.count
					// this.$message.success(response.body.msg)
				}else{
					this.list = []
					this.pageTotal = 0 
					this.$message.error(response.body.msg)
				}
			})
		},
		searchFun (text){
			this.search.field_value = text ? text : (this.search.field_value === this.params.best_staff_name ? this.params.best_staff_name : '')
			this.getSearch()
		},
		sizeChange(size){
			// 第页数改变
			this.search.page_size = size;
			this.getSearch();
		},
		pageChange (page_no){
			// 页数改变
			// this.pageNow = page_no;
			this.search.page_no = page_no;
			this.getSearch();
		},
		select (selectData){
			this.selectObject = selectData
		},
		submit (row){
			if(row) this.selectObject = row

			this.params.callback(this.selectObject)
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId)
		},
	},
	mounted (){
		this.initSearch = Object.assign({}, this.search)
		this.getSearch()
	}
}
</script>	