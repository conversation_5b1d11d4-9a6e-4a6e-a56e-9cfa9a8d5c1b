<!-- 评价单详情 -->
<template>
  <div class="xpt-flex">
    <xpt-headbar>
      <el-button
        size="mini"
        type="success"
        @click="getBusinessInfo"
        :disabled="onGet"
        :loading="onGet"
        slot="left"
        >获取业务信息</el-button
      >
      <el-button
        size="mini"
        type="info"
        @click="save"
        :disabled="onSave"
        :loading="onSave"
        slot="left"
        >保存定性评价</el-button
      >
      <el-button
        size="mini"
        type="primary"
        @click="haveBeenEvaluated"
        :disabled="onBeen"
        :loading="onBeen"
        slot="left"
        >已评定</el-button
      >
      <el-button
        size="mini"
        type="primary"
        @click="pendingEvaluation"
        :disabled="onPending"
        :loading="onPending"
        slot="left"
        >待评定</el-button
      >
      <el-button
        size="mini"
        type="primary"
        @click="onClickPushPerformance"
        :disabled="onPending"
        :loading="onPending"
        slot="left"
        >下推绩效单</el-button
      >
      <el-button
        size="mini"
        type="primary"
        @click="viewHomeImg"
        :disabled="!hasHomeImg"
        :loading="false"
        slot="left"
        >查看林氏到家客户评价</el-button
      >
      <el-button
        type="info"
        size="mini"
        slot="right"
        @click="nextOrPrev('prev')"
        >上一条</el-button
      >
      <el-button
        type="info"
        size="mini"
        slot="right"
        @click="nextOrPrev('next')"
        >下一条</el-button
      >
    </xpt-headbar>
    <div class="xpt-flex__bottom">
      <el-tabs v-model="base">
        <el-tab-pane label="基本信息" name="first">
          <xpt-form :data="detailData" :cols="baseCols" label="150px">
            <template slot="content">
              <el-input
                type="textarea"
                readonly
                v-model="detailData.content"
                class="height30"
                :rows="5"
              ></el-input>
            </template>
            <template slot="add_content">
              <el-input
                type="textarea"
                readonly
                v-model="detailData.add_content"
                class="height30"
                :rows="5"
              ></el-input>
            </template>
            <template slot="remark">
              <el-input
                type="textarea"
                v-model="detailData.remark"
                class="height30"
                :rows="5"
              ></el-input>
            </template>
            <template slot="if_blueprint">
              {{
                detailData.if_blueprint == "Y"
                  ? "是"
                  : detailData.if_blueprint == "N"
                  ? "否"
                  : ""
              }}
              <el-button
                v-if="detailData.if_blueprint == 'Y'"
                style="padding: 0"
                type="text"
                @click="previewImage"
                >查看</el-button
              >
            </template>
            <template slot="if_video">
              {{
                detailData.if_video == "Y"
                  ? "是"
                  : detailData.if_video == "N"
                  ? "否"
                  : ""
              }}
              <el-button
                v-if="detailData.if_video == 'Y'"
                style="padding: 0"
                type="text"
                @click="previewVideo"
                >查看</el-button
              >
            </template>
            <template slot="lsdj_label">
              <el-input
                type="textarea"
                readonly
                v-model="detailData.lsdj_label"
                class="height30"
                :rows="5"
              ></el-input>
            </template>
          </xpt-form>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="xpt-flex__bottom">
      <el-tabs v-model="business">
        <el-tab-pane label="业务信息" name="first">
          <xpt-form :data="detailData" :cols="businessCols" label="150px">
            <template slot="staff_name">
              <p v-if="detailData.appraise_status !== 'UN_APPRAISE'">
                {{ detailData.staff_name }}
              </p>
              <xpt-input
                v-else
                size="mini"
                v-model="detailData.staff_name"
                icon="search"
                :on-icon-click="openStaffSelect"
                @change="changeStaff"
                readonly
              ></xpt-input>
            </template>
            <template slot="after_name">
              <p v-if="detailData.appraise_status !== 'UN_APPRAISE'">
                {{ detailData.after_name }}
              </p>
              <xpt-input
                v-else
                size="mini"
                v-model="detailData.after_name"
                icon="search"
                :on-icon-click="openAfterSelect"
                @change="changeAfter"
                readonly
              ></xpt-input>
            </template>
            <!-- 业务员定性评价 -->
            <template slot="staff_type">
              <xpt-select-aux
                v-model="detailData.staff_type"
                aux_name="performancePer"
                v-if="isStaff"
              ></xpt-select-aux>
              <span v-else>{{
                detailData.staff_type | auxFormat("performancePer")
              }}</span>
            </template>
            <!-- 业务员绩效评定 -->
            <template slot="staff_perfor_typeName">
              <xpt-input
                icon="search"
                v-model="detailData.staff_perfor_typeName"
                size="mini"
                :on-icon-click="() => openBeforePerformanceSelect('BEFORE')"
                v-if="isStaffType"
                @change="clearBefore"
                readonly
              ></xpt-input>
              <span v-else>{{ detailData.staff_perfor_typeName }}</span>
            </template>
            <!-- 业务员追加绩效评定 -->
            <template slot="add_staff_perfor_typeName">
              <xpt-input
                icon="search"
                v-model="detailData.add_staff_perfor_typeName"
                size="mini"
                :on-icon-click="() => openBeforePerformanceSelect('ADD_BEFORE')"
                v-if="detailData.add_staff_type"
                readonly
              ></xpt-input>
              <span v-else>{{ detailData.add_staff_perfor_typeName }}</span>
            </template>
            <!-- 售后专员评价定性 -->
            <template slot="after_type">
              <xpt-select-aux
                v-model="detailData.after_type"
                aux_name="performanceAfter"
                v-if="isAfter"
              ></xpt-select-aux>
              <span v-else>{{
                detailData.after_type | auxFormat("performanceAfter")
              }}</span>
            </template>
            <!-- 售后专员绩效评定 -->
            <template slot="after_perfor_typeName">
              <xpt-input
                icon="search"
                v-model="detailData.after_perfor_typeName"
                size="mini"
                :on-icon-click="() => openAfterPerformanceSelect('AFTER')"
                v-if="isAfterType"
                @change="clearAfter"
                readonly
              ></xpt-input>
              <span v-else>{{ detailData.after_perfor_typeName }}</span>
            </template>
            <!-- 售后专员追加绩效评定 -->
            <template slot="add_after_perfor_typeName">
              <xpt-input
                icon="search"
                v-model="detailData.add_after_perfor_typeName"
                size="mini"
                :on-icon-click="() => openAfterPerformanceSelect('ADD_AFTER')"
                v-if="detailData.add_after_type"
                readonly
              ></xpt-input>
              <span v-else>{{ detailData.add_after_perfor_typeName }}</span>
            </template>
            <!-- 业务员追评定性评价 -->
            <template slot="add_staff_type">
              <xpt-select-aux
                v-model="detailData.add_staff_type"
                aux_name="STAFF_ADD_PERFORMANCE_TYPE_PRE"
                v-if="isStaff"
                clearable
                @change="handleAddStaffTypeChange"
              ></xpt-select-aux>
              <span v-else>{{
                detailData.add_staff_type
                  | auxFormat("STAFF_ADD_PERFORMANCE_TYPE_PRE")
              }}</span>
            </template>
            <!-- 售后专员追评定性评价 -->
            <template slot="add_after_type">
              <xpt-select-aux
                v-model="detailData.add_after_type"
                aux_name="AFTER_ADD_PERFORMANCE_TYPE_PRE"
                v-if="isAfter"
                clearable
                @change="handleAddAfterTypeChange"
              ></xpt-select-aux>
              <span v-else>{{
                detailData.add_after_type
                  | auxFormat("AFTER_ADD_PERFORMANCE_TYPE_PRE")
              }}</span>
            </template>
            <!-- 业务员晒图定性 -->
            <template slot="staff_photo_type">
              <xpt-select-aux
                v-model="detailData.staff_photo_type"
                aux_name="STAFF_PICTURE_PERFORMANCE_TYPE"
                v-if="isStaff && (ifVideo || ifBlueprint)"
                clearable
              ></xpt-select-aux>
              <span v-else>{{
                detailData.staff_photo_type
                  | auxFormat("STAFF_ADD_PERFORMANCE_TYPE_PRE")
              }}</span>
            </template>
            <!-- 售后专员晒图定性 -->
            <template slot="after_photo_type">
              <xpt-select-aux
                v-model="detailData.after_photo_type"
                aux_name="AFTER_PICTURE_PERFORMANCE_TYPE"
                v-if="isAfter && (ifVideo || ifBlueprint)"
                clearable
              ></xpt-select-aux>
              <span v-else>{{
                detailData.after_photo_type
                  | auxFormat("STAFF_ADD_PERFORMANCE_TYPE_PRE")
              }}</span>
            </template>
          </xpt-form>
        </el-tab-pane>
        <el-tab-pane label="物流信息" name="logistics">
          <xpt-form :data="detailData" :cols="logisticsCols" label="150px">
            <!-- 物流定性评价 -->
            <template slot="logistics_type">
              <xpt-select-aux
                v-model="detailData.logistics_type"
                aux_name="performance_type_logistics"
              ></xpt-select-aux>
            </template>
            <template slot="logistics_performance_type">
              <xpt-select-aux
                v-model="detailData.logistics_performance_type"
                aux_name="LOGISTICS_PERFORMANCE_TYPE"
                clearable
              ></xpt-select-aux>
            </template>
            <template slot="add_logistics_type">
              <xpt-select-aux
                v-model="detailData.add_logistics_type"
                aux_name="ADD_LOGISTICS_TYPE_PRE"
                clearable
              ></xpt-select-aux>
            </template>

            <!-- 物流是否超时 -->
            <template slot="if_logistics_timeout">
              <el-switch
                v-model="detailData.if_logistics_timeout"
                on-text="是"
                off-text="否"
                on-value="是"
                off-value="否"
                :disabled="true"
              >
              </el-switch>
            </template>
            <!-- 经销三包 -->
            <template slot="if_three_service_agency">
              <el-switch
                v-model="detailData.if_three_service_agency"
                on-text="是"
                off-text="否"
                on-value="Y"
                off-value="N"
                :disabled="true"
              >
              </el-switch>
            </template>
            <!-- 是否经销 -->
            <template slot="if_dealer">
              <el-switch
                v-model="detailData.if_dealer"
                on-text="是"
                off-text="否"
                on-value="是"
                off-value="否"
                :disabled="true"
              >
              </el-switch>
            </template>
            <template slot="deliver_method">
              <xpt-select-aux
                v-model="detailData.deliver_method"
                aux_name="deliver_method"
              ></xpt-select-aux>
            </template>
          </xpt-form>
        </el-tab-pane>
        <el-tab-pane label="责任信息" name="duty">
          <xpt-list2
            :btns="dutyBtns"
            :data="dutyList"
            :colData="dutyCols"
            :pageTotal="dutyTotal"
            ref="dutyList"
            selection="radio"
            @radio-change="radioChange"
            @cell-click="rowClick"
          >
            <template slot="remark" slot-scope="scope">
              <el-input
                v-model="scope.row.remark"
                size="mini"
                :maxlength="250"
                style="width: 99%"
              ></el-input>
            </template>
            <template slot="inner_quesd_name" slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.inner_quesd_name"
                readonly
                icon="search"
                :on-icon-click="
                  () => {
                    selectDutyTypeOrProblem(scope.row);
                  }
                "
                :disabled="false"
              ></el-input>
            </template>
            <template slot="material_number" slot-scope="scope">
              <el-input
                size="mini"
                v-model="scope.row.material_number"
                readonly
                icon="search"
                :on-icon-click="
                  () => {
                    selectMterial(scope.row);
                  }
                "
                :disabled="false"
              ></el-input>
            </template>
          </xpt-list2>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import Vue from "vue";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      detailData: {},
      base: "first",
      business: "first",
      material_List: [],
      isStaffOrAfterChanged: false,
      dutyCols: [
        {
          label: "一级责任主体",
          prop: "tinner_liability_type",
          width: 100,
        },
        {
          label: "二级责任主体",
          prop: "que_sec_name",
          width: 100,
        },
        {
          label: "故障模块",
          prop: "firstLevelName",
          width: 180,
        },
        {
          label: "故障现象",
          prop: "secondLevelName",
          width: 180,
        },
        {
          label: "故障原因",
          slot: "inner_quesd_name",
          width: 200,
        },
        {
          label: "备注",
          slot: "remark",
          width: 300,
        },
        {
          label: "物料编码",
          slot: "material_number",
          width: 200,
        },
        {
          label: "物料名称",
          prop: "material_name",
        },
        {
          label: "规格描述",
          prop: "material_specification",
        },
        {
          label: "来源商品编码",
          prop: "source_material_number",
        },
        {
          label: "来源商品名称",
          prop: "source_material_name",
        },
        {
          label: "研发分类",
          prop: "developType",
        },
        {
          label: "批次单号",
          prop: "batch_trade_no",
        },
        {
          label: "合并单号",
          prop: "merge_trade_no",
        },
        {
          label: "承诺发货时间",
          prop: "commit_time",
          format: "dataFormat1",
          width: 200,
        },
        {
          label: "发货日期",
          prop: "out_stock_time",
          format: "dataFormat1",
        },
        {
          label: "批号",
          prop: "lot",
        },
        {
          label: "服务商",
          prop: "logistics_supplier_name",
        },
        {
          label: "三包供应商",
          prop: "three_guarantees_supplier_name",
        },
        {
          label: "送货方式",
          prop: "deliver_method",
          format: "deliverMethod",
        },
        {
          label: "预计到货时间",
          prop: "expect_arrival_time",
          format: "dataFormat1",
          width: 200,
        },
        {
          label: "安装时间",
          prop: "install_time",
          format: "dataFormat1",
          width: 200,
        },
        {
          label: "实际到货时间",
          prop: "arrival_goods_time",
          format: "dataFormat1",
          width: 200,
        },
        {
          label: "责任判定时间",
          prop: "liability_judge_time",
          format: "dataFormat1",
          width: 200,
        },
        {
          label: "责任判定人",
          prop: "liability_judge_person_name",
        },
      ],
      dutyBtns: [
        {
          type: "success",
          txt: "保存",
          click: () => {
            self.saveDuty();
          },
        },
        {
          type: "info",
          txt: "添加责任问题",
          click: () => {
            self.selectDutyTypeOrProblem();
          },
        },
        {
          type: "danger",
          txt: "删除",
          click: () => {
            self.delDutyTypeOrProblem();
          },
        },
      ],
      dutySelect: "",
      dutyList: [],
      dutyTotal: 0,
      baseCols: [
        [
          {
            label: "淘宝单号：",
            key: "tid",
            span: 8,
          },
          {
            label: "买家昵称：",
            key: "nick",
          },
          {
            label: "销售标签：",
            key: "sales_label",
            html(val) {
              return val ? '<span style="color:red;">' + val + "</span>" : val;
            },
          },
          {
            label: "评价来源：",

            key: "source",
          },
          {
            label: "评价内容：",
            span: 4,
            slot: "content",
          },
          {
            label: "备注：",
            slot: "remark",
            className: true,
          },
        ],
        [
          {
            label: "淘宝子单号：",
            key: "oid",
            span: 8,
          },
          {
            label: "评价结果：",
            key: "result",
            formatter(val) {
              if (val) {
                let v = val.toLowerCase(val);
                switch (v) {
                  case "good":
                    return "好评";
                  case "neutral":
                    return "中评";
                  case "bad":
                    return "差评";
                  default:
                    return v;
                }
              }
            },
          },
          {
            label: "评价字数：",
            key: "content_length",
          },
          {
            label: "是否晒图：",
            slot: "if_blueprint",
          },

          {
            label: "追评内容：",
            slot: "add_content",
            span: 8,
          },
        ],
        [
          {
            label: "评价单状态：",
            key: "appraise_status",
            span: 8,
            formatter(val) {
              switch (val) {
                case "UN_APPRAISE":
                  return "待评定";
                case "APPRAISE":
                  return "已评定";
                default:
                  return val;
              }
            },
          },
          {
            label: "评价创建时间：",
            key: "created",
            format: "dataFormat1",
          },
          {
            label: "评定完结时间：",
            key: "fin_time",
            format: "dataFormat1",
          },
          {
            label: "是否视频：",
            slot: "if_video",
          },
          {
            label: "追评时间：",
            key: "add_time",
            format: "dataFormat1",
          },
          {
            label: "林氏到家评价标签：",
            slot: "lsdj_label",
            span: 8,
          },
        ],
      ],
      businessCols: [
        [
          {
            label: "销售订单：",
            key: "sys_trade_no",
          },
          {
            label: "商品编码：",
            key: "material_code",
          },
          {
            label: "业务员：",
            slot: "staff_name",
          },
          {
            label: "业务员分组：",
            key: "staff_group_name",
          },
          {
            label: "业务员定性评价：",
            slot: "staff_type",
          },
          {
            label: "业务员自动定性：",
            key: "staff_evaluate",
          },
          {
            label: "业务员绩效评定：",
            slot: "staff_perfor_typeName",
          },
          {
            label: "业务员绩效值：",
            key: "staff_perfor_score",
          },
          {
            label: "业务追评定性评价：",
            slot: "add_staff_type",
          },
          {
            label: "业务员追加绩效评定：",
            slot: "add_staff_perfor_typeName",
          },
          {
            label: "业务员晒图定性：",
            slot: "staff_photo_type",
          },
        ],
        [
          {
            label: "合并订单：",
            key: "merge_trade_no",
          },
          {
            label: "商品名称：",
            key: "goods_name",
          },
          {
            label: "售后专员：",
            slot: "after_name",
          },
          {
            label: "售后专员分组：",
            key: "after_group_name",
          },
          {
            label: "售后专员评价定性：",
            slot: "after_type",
          },
          {
            label: "售后专员自动定性：",
            key: "after_evaluate",
          },
          {
            label: "售后专员绩效评定：",
            slot: "after_perfor_typeName",
          },
          {
            label: "售后专员绩效值：",
            key: "after_perfor_score",
          },
          {
            label: "售后专员追评定性评价：",
            slot: "add_after_type",
          },
          {
            label: "售后专员追加绩效评定：",
            slot: "add_after_perfor_typeName",
          },
          {
            label: "售后专员晒图定性：",
            slot: "after_photo_type",
          },
        ],
        [
          {
            label: "店铺：",
            key: "shop_name",
          },
          {
            label: "业务员评定人：",
            key: "staff_determine_name",
          },
          {
            label: "业务员评定时间：",
            key: "staff_determine_time",
            format: "dataFormat1",
          },
          {
            label: "售后专员评定人：",
            key: "after_determine_name",
          },
          {
            label: "售后专员评定时间：",
            key: "after_determine_time",
            format: "dataFormat1",
          },
          {
            label: "业务员追评评定人：",
            key: "add_staff_determine_name",
          },
          {
            label: "业务员追评评定时间：",
            key: "add_staff_determine_time",
            format: "dataFormat1",
          },
          {
            label: "售后专员追评评定人：",
            key: "add_after_determine_name",
          },
          {
            label: "售后专员追评评定时间：",
            key: "add_after_determine_time",
            format: "dataFormat1",
          },
          {
            label: "晒图评定人：",
            key: "photo_determine_name",
          },
          {
            label: "晒图评定时间：",
            key: "photo_determine_time",
            format: "dataFormat1",
          },
        ],
      ],
      logisticsCols: [
        [
          {
            label: "发货批次：",
            key: "batch_trade_no",
          },
          {
            label: "物流定性评价：",
            slot: "logistics_type",
          },
          {
            label: "物流自动定性：",
            key: "logistics_evaluate",
          },
          {
            label: "省：",
            key: "province_name",
          },
          {
            label: "直达承运商：",
            key: "logistics_supplier_name",
          },
          {
            label: "安装师傅：",
            key: "installation_personnel_name",
          },
          {
            label: "物流绩效评定：",
            slot: "logistics_performance_type",
          },
        ],
        [
          {
            label: "物流是否超时：",
            slot: "if_logistics_timeout",
          },
          {
            label: "物流评定人：",
            key: "logistics_determine_name",
          },
          {
            label: "市：",
            key: "city_name",
          },
          {
            label: "三包商：",
            key: "three_guarantees_supplier_name",
          },
          {
            label: "师傅手机号：",
            key: "installation_phone_number",
          },
          {
            label: "物流绩效评定人：",
            key: "logistics_performance_determine_name",
          },
          {
            label: "物流追加定性评价：",
            slot: "add_logistics_type",
          },
          {
            label: "经销三包：",
            slot: "if_three_service_agency",
          },
        ],
        [
          {
            label: "是否经销：",
            slot: "if_dealer",
          },
          {
            label: "物流评定时间：",
            key: "logistics_determine_time",
            format: "dataFormat1",
          },
          {
            label: "区：",
            key: "district_name",
          },
          {
            label: "线路区域：",
            key: "line_area",
          },
          {
            label: "是否精确货运：",
            key: "accurate_match",
            formatter(val) {
              switch (val) {
                case 1:
                  return "是";
                case 0:
                  return "否";
                default:
                  return val;
              }
            },
          },
          {
            label: "货运方式：",
            slot: "deliver_method",
          },
          {
            label: "物流绩效评定时间：",
            key: "logistics_performance_determine_time",
            format: "dataFormat1",
          },
        ],
      ],
      onSave: false,
      onBeen: false,
      onPending: false,
      onGet: false,
    };
  },
  computed: {
    hasHomeImg(){
      return this.detailData.attachments&&this.detailData.attachments.length>0 ? true : false;
    },
    isStaff() {
      return this.detailData.staff ? true : false;
    },
    isStaffType() {
      return this.isStaff && this.detailData.staff_type ? true : false;
    },
    isAfter() {
      return this.detailData.after ? true : false;
    },
    isAfterType() {
      return this.isAfter && this.detailData.after_type ? true : false;
    },
    ifBlueprint() {
      return this.detailData.if_blueprint == "Y" ? true : false;
    },
    ifVideo() {
      return this.detailData.if_video == "Y" ? true : false;
    },
  },
  methods: {
    getDevelopTypeName() {
      let self = this;
      this.dutyList.forEach((item) => {
        this.ajax.postStream(
          "/afterSale-web/api/aftersale/analysis/getDevelopTypeName",
          { materialCode: item.material_number },
          (res) => {
            if (res.body.result) {
              self.$nextTick(() => {
                self.$set(item, "developType", res.body.content);
                // item.developType = res.body.content;
              });
            } else {
              // this.$message.error(res.body.msg)
            }
          },
          (err) => {
            this.$message.error(err);
          }
        );
      });
    },
    getMaterial(customer_name, callback) {
      let search = {
        customer_name: customer_name,
        begin_out_stock_time: new Date(
          new Date().getTime() - 1000 * 60 * 60 * 24 * 365 * 3
        ),
      };
      this.ajax.postStream(
        "/order-web/api/appraise/liability/chooseMaterial",
        search,
        (response) => {
          if (response.body.result) {
            this.material_List = response.body.content.list;
            // this.$message.success(response.body.msg)
            callback(response.body.content.list);
          } else {
            this.$message.error(response.body.msg);
          }
        }
      );
    },
    saveDuty() {
      let self = this;
      let data = self.dutyList.map((item) => {
        return {
          id: item.id, //有值则修改，没值则新增
          appraise_id: item.appraise_id,
          ques_detail_id: item.ques_detail_id,
          remark: item.remark,
          materiel_id: item.materiel_id,
          materia_id: item.materia_id,
          material_number: item.material_number,
          source_materia_id: item.source_materia_id,
          source_material_id: item.source_material_id,
          source_material_number: item.source_material_number,
          batch_trade_id: item.batch_trade_id,
          batch_trade_no: item.batch_trade_no,
          merge_trade_id: item.merge_trade_id,
          merge_trade_no: item.merge_trade_no,
        };
      });
      this.ajax.postStream(
        "/order-web/api/appraise/liability/save?permissionCode=APPRAISE_LIABILITY_SAVE",
        { list: data },
        (response) => {
          if (response.body.result) {
            this.$message({ message: "操作成功", type: "success" });
            this.getDetailInfo(this.params.id);
          } else {
            this.$message.error(response.body.msg);
            // this.getDetailInfo(this.params.id);
          }
        },
        (e) => {
          this.$message.error(e);
        }
      );
    },
    selectMterial(row) {
      let self = this;
      this.$root.eventHandle.$emit("alert", {
        params: {
          data: {
            nick: self.detailData.nick,
          },
          callback: (d) => {
            row.materiel_id = d.merge_materiel_id;
            row.materia_id = d.materia_id;
            row.material_number = d.material_number;
            row.source_materia_id = d.source_materia_id;
            row.source_material_id = d.source_material_id;
            row.source_material_number = d.source_material_number;
            row.batch_trade_id = d.batch_trade_id;
            row.batch_trade_no = d.batch_trade_no;
            row.merge_trade_id = d.merge_trade_id;
            row.merge_trade_no = d.merge_trade_no;
          },
        },
        component: () => import("./add_material.vue"),
        style: "width:80%;height:80%",
        title: "选择责任问题商品",
      });
    },
    delDutyTypeOrProblem() {
      let self = this;
      if (!self.dutySelect) {
        self.$message.error("请选择要删除的内容");
        return;
      }
      if (!self.dutySelect.id) {
        self.dutyList.splice(self.dutyList.indexOf(self.dutySelect), 1);
      } else {
        this.ajax.postStream(
          "/order-web/api/appraise/liability/delete/" +
            self.dutySelect.id +
            "?permissionCode=APPRAISE_LIABILITY_SAVE",
          {},
          (response) => {
            if (response.body.result) {
              this.$message({ message: "操作成功", type: "success" });
              this.getDetailInfo(this.params.id);
            } else {
              this.$message.error(response.body.msg);
              this.getDetailInfo(this.params.id);
            }
          },
          (e) => {
            this.$message.error(e);
          }
        );
      }
    },
    async selectDutyTypeOrProblem(row) {
      let self = this;
      await self.getMaterial(self.detailData.nick, (material_List) => {
        let isOneMaterial = material_List.length == 1;
        console.log(isOneMaterial);
        this.$root.eventHandle.$emit("alert", {
          params: {
            callSource: "evaluateDetail",
            callback: (d) => {
              if (row) {
                row.inner_quesd_name = d.threeLevelName;
                row.secondLevelName = d.secondLevelName;
                row.firstLevelName = d.firstLevelName;
                row.out_liability_question = d.out_liability_question;
                row.tinner_liability_type = d.liability_type;
                row.que_sec_name = d.second_liability_type;
                row.duty_default = d.duty_default;
                row.tinner_liability_type = d.tinner_liability_type;
                row.ques_detail_id = d.id;
              } else {
                let row1 = isOneMaterial
                  ? material_List[0]
                  : self.dutyList.length > 0
                  ? self.dutyList[self.dutyList.length - 1]
                  : "";
                console.log(row1);
                let data = {
                  appraise_id: self.params.id,
                  inner_quesd_name: d.threeLevelName,
                  secondLevelName: d.secondLevelName,
                  firstLevelName: d.firstLevelName,
                  out_liability_question: d.out_liability_question,
                  tinner_liability_type: d.liability_type,
                  que_sec_name: d.second_liability_type,
                  duty_default: d.duty_default,
                  ques_detail_id: d.id,
                  remark: "",
                  materiel_id: row1 ? row1.materiel_id : "",
                  materia_id: row1 ? row1.materia_id : "",
                  material_number: row1 ? row1.material_number : "",
                  source_materia_id: row1 ? row1.source_materia_id : "",
                  source_material_id: row1 ? row1.source_material_id : "",
                  source_material_number: row1
                    ? row1.source_material_number
                    : "",
                  batch_trade_id: row1 ? row1.batch_trade_id : "",
                  batch_trade_no: row1 ? row1.batch_trade_no : "",
                  merge_trade_id: row1 ? row1.merge_trade_id : "",
                  merge_trade_no: row1 ? row1.merge_trade_no : "",
                };
                self.dutyList.push(data);
              }
            },
          },
          component: () =>
            import(
              "@components/after_sales_liability_problem/components/add_duty_person.vue"
            ),
          style: "width:1250px;height:80%",
          title: "选择责任问题",
        });
      });
    },
    radioChange(row) {
      console.log(row);
      this.dutySelect = row;
    },
    rowClick(row) {
      console.log(row);
      this.$refs.dutyList.setSelect([row]);
      this.$refs.dutyList.setRadioSelect(row);
    },
    getBusinessInfo() {
      console.log(this.detailData);
      let dataList = [];
      if (!this.params.id) {
        this.$message.error("获取参数错误");
        return;
      } else {
        dataList.push(this.params.id);
        this.onGet = true;
        this.ajax.postStream(
          "/order-web/api/appraise/composite?permissionCode=EVALUVTE_ORDER_GET_BUSINESS_INFO",
          dataList,
          (response) => {
            if (response.body.result) {
              this.$message({ message: "操作成功", type: "success" });
              this.getDetailInfo(this.params.id);
            } else {
              this.$message.error(response.body.msg);
              this.getDetailInfo(this.params.id);
            }
            this.onGet = false;
          },
          (e) => {
            this.onGet = false;
            this.$message.error(e);
          }
        );
      }
    },
    haveBeenEvaluated() {
      let dataList = [];
      if (!this.params.id) {
        this.$message.error("获取参数错误");
      } else {
        dataList.push(this.params.id);
        this.onBeen = true;
        this.ajax.postStream(
          "/order-web/api/appraise/toAppraise?permissionCode=EVALUVTE_ORDER_EVALUVTED",
          dataList,
          (response) => {
            if (response.body.result) {
              this.$message({ message: "操作成功", type: "success" });
              this.getDetailInfo(this.params.id);
            } else {
              this.$message.error(response.body.msg);
              this.getDetailInfo(this.params.id);
            }
            this.onBeen = false;
          },
          (e) => {
            this.onBeen = false;
            this.$message.error(e);
          }
        );
      }
    },
    pendingEvaluation() {
      let dataList = [];
      if (!this.params.id) {
        this.$message.error("获取参数错误");
      } else {
        dataList.push(this.params.id);
        this.onPending = true;
        this.ajax.postStream(
          "/order-web/api/appraise/unAppraise?permissionCode=EVALUVTE_ORDER_PENDING_EVALUVTE",
          dataList,
          (response) => {
            if (response.body.result) {
              this.$message({ message: "操作成功", type: "success" });
              this.getDetailInfo(this.params.id);
            } else {
              this.$message.error(response.body.msg);
              this.getDetailInfo(this.params.id);
            }
            this.onPending = false;
          },
          (e) => {
            this.onPending = false;
            this.$message.error(e);
          }
        );
      }
    },
    //查询详情
    getDetailInfo(id) {
      this.isStaffOrAfterChanged = false;
      let data = { id: id };
      this.ajax.postStream(
        "/order-web/api/appraise/get?permissionCode=EVALUVTE_ORDER_QUERY",
        data,
        (response) => {
          if (response.body.result) {
            //赋值
            this.detailData = response.body.content;
            this.dutyList = this.detailData.appraiseLiabilitys;
            this.getDevelopTypeName();
            Vue.prototype.tabNo[this.params.tabName] =
              response.body.content.merge_trade_no || "";
          } else {
            Vue.prototype.tabNo[this.params.tabName] = "";
            this.$message.error(response.body.msg);
          }
        },
        (e) => {
          Vue.prototype.tabNo[this.params.tabName] = "";
          this.$message.error(e);
        }
      );
    },
    // 打开售前专员绩效评定的弹窗
    openBeforePerformanceSelect(type) {
      this.isStaff && this.openPerformanceTypeList(type);
    },
    // 打开售后专员绩效评定弹窗
    openAfterPerformanceSelect(type) {
      this.isAfter && this.openPerformanceTypeList(type);
    },
    openPerformanceTypeList(type) {
      var self = this;
      self.$root.eventHandle.$emit("alert", {
        title: "请选择售前评定类型",
        style: "width: 800px; height: 400px",
        component: () => import("@components/performance/typelist"),
        params: {
          distinction: type.includes("BEFORE") ? "BEFORE" : "AFTER",
          category: "evaluate",
          status: "VERIFY",
          callback(d) {
            if (type == "BEFORE") {
              self.detailData.staff_perfor_type = d.code;
              self.detailData.staff_perfor_typeName = d.name;
              self.detailData.staff_perfor_score = d.score;
            } else if (type == "AFTER") {
              self.detailData.after_perfor_type = d.code;
              self.detailData.after_perfor_typeName = d.name;
              self.detailData.after_perfor_score = d.score;
            } else if (type == "ADD_BEFORE") {
              self.detailData.add_staff_perfor_type = d.code;
              self.detailData.add_staff_perfor_typeName = d.name;
            } else if (type == "ADD_AFTER") {
              self.detailData.add_after_perfor_type = d.code;
              self.detailData.add_after_perfor_typeName = d.name;
            }
          },
        },
      });
    },
    clearBefore(val) {
      if (!val) {
        this.detailData.staff_perfor_type = "";
        this.detailData.staff_perfor_typeName = "";
        this.detailData.staff_perfor_score = "";
      }
    },
    clearAfter(val) {
      if (!val) {
        this.detailData.after_perfor_type = "";
        this.detailData.after_perfor_typeName = "";
        this.detailData.after_perfor_score = "";
      }
    },
    // 保存评定
    save() {
      if (!this.isStaff && !this.isAfter) {
        this.$message.error("没有业务员或者售后专员不能定性评价");
        return;
      }
      let postData = {
        id: this.detailData.id,
        staff: this.detailData.staff,
        staff_name: this.detailData.staff_name,
        staff_type: this.detailData.staff_type,
        staff_perfor_type: this.detailData.staff_perfor_type,
        after: this.detailData.after,
        after_name: this.detailData.after_name,
        after_type: this.detailData.after_type,
        after_perfor_type: this.detailData.after_perfor_type,
        remark: this.detailData.remark,
        logistics_type: this.detailData.logistics_type,
        logistics_performance_type: this.detailData.logistics_performance_type||null,
        add_logistics_type: this.detailData.add_logistics_type || null,
        if_logistics_timeout: this.detailData.if_logistics_timeout,
        if_dealer: this.detailData.if_dealer,
        deliver_method: this.detailData.deliver_method,
        add_staff_perfor_type: this.detailData.add_staff_perfor_type,
        add_after_perfor_type: this.detailData.add_after_perfor_type,
        add_staff_type: this.detailData.add_staff_type,
        add_after_type: this.detailData.add_after_type,
        staff_photo_type: this.detailData.staff_photo_type,
        after_photo_type: this.detailData.after_photo_type,
      };
      // return
      this.onSave = true;
      this.ajax.postStream(
        `/order-web/api/appraise/update?permissionCode=${
          this.isStaffOrAfterChanged
            ? "EVALUVTE_ORDER_UPDATE_BUSINESSMAN"
            : "EVALUVTE_ORDER_SAVE"
        }`,
        postData,
        (response) => {
          this.onSave = false;
          if (response.body.result) {
            this.$message.success("操作成功");
            this.getDetailInfo(this.params.id);
          } else {
            this.$message.error(response.body.msg || "操作失败");
          }
        },
        (e) => {
          this.onSave = false;
          this.$message.error(e);
        }
      );
    },
    onClickPushPerformance() {
      this.$root.eventHandle.$emit("creatTab", {
        name: "下推绩效单",
        component: () => import("@components/performance/add"),
        params: {
          appraise_id: this.detailData.id,
          isPush: true,
          isCopy: false,
          staff_name: this.detailData.staff_name,
          staff_id: this.detailData.staff,
          staff_group_name: this.detailData.staff_group_name,
          staff_group: this.detailData.staff_group,
          buyer_name: this.detailData.nick,
          appraise_content: this.detailData.content,
          merge_trade_no: this.detailData.merge_trade_no,
        },
      });
    },
    openStaffSelect() {
      var self = this;
      self.$root.eventHandle.$emit("alert", {
        title: "请选择业务员",
        style: "width: 800px; height: 400px",
        component: () => import("@components/performance/staffDialog"),
        params: {
          callback(d) {
            self.isStaffOrAfterChanged = true;
            self.detailData.staff = d.userId;
            self.detailData.staff_name = d.realName + "." + d.nickName;
          },
        },
      });
    },
    changeStaff(val) {
      if (!val) {
        this.detailData.staff = "";
        this.detailData.staff_name = "";
      }
    },
    openAfterSelect() {
      var self = this;
      self.$root.eventHandle.$emit("alert", {
        title: "请选择售后专员",
        style: "width: 1000px; height: 400px",
        component: () => import("@components/performance/staffDialog"),
        params: {
          callback(d) {
            self.isStaffOrAfterChanged = true;
            self.detailData.after = d.userId;
            self.detailData.after_name = d.realName + "." + d.nickName;
          },
        },
      });
    },
    changeAfter(val) {
      if (!val) {
        this.detailData.after = "";
        this.detailData.after_name = "";
      }
    },
    viewHomeImg(){
      this.$root.eventHandle.$emit("alert", {
        title: "林氏到家客户评价",
        style: "width: 1000px; height: 600px",
        component: () => import("@components/common/mediaPreview"),
        params: {
          height: "500px",
          mediaList: (this.detailData.attachments || []).map((item) => {
            return {
              src: item,
              type: "image",
            };
          }),
        },
      });
    },
    previewImage() {
      this.$root.eventHandle.$emit("alert", {
        title: "晒图预览",
        style: "width: 1000px; height: 600px",
        component: () => import("@components/common/mediaPreview"),
        params: {
          height: "500px",
          mediaList: (this.detailData.perfor_pic || []).map((item) => {
            return {
              src: item,
              type: "image",
            };
          }),
        },
      });
    },
    previewVideo() {
      this.$root.eventHandle.$emit("alert", {
        title: "视频预览",
        style: "width: 1000px; height: 600px",
        component: () => import("@components/common/mediaPreview"),
        params: {
          height: "500px",
          mediaList: (this.detailData.perfor_video || []).map((item) => {
            return {
              src: item,
              type: "video",
            };
          }),
        },
      });
    },
    nextOrPrev(type) {
      this.params.evaluateList.some((obj, index) => {
        if (obj.id === this.params.id) {
          let target =
            this.params.evaluateList[index + (type === "next" ? 1 : -1)];

          if (target) {
            this.params.id = target.id;
            this.getDetailInfo(target.id);
          } else {
            this.$message.error("没有更多");
          }
          return true;
        }
      });
    },
    handleAddAfterTypeChange(val) {
      if (!val) {
        this.detailData.add_after_perfor_type = "";
        this.detailData.add_after_perfor_typeName = "";
      }
    },
    handleAddStaffTypeChange(val) {
      if (!val) {
        this.detailData.add_staff_perfor_type = "";
        this.detailData.add_staff_perfor_typeName = "";
      }
    },
  },
  mounted() {
    if (this.params.id) {
      this.getDetailInfo(this.params.id);
    }
  },
};
</script>
<style lang="stylus" scoped>
.xpt-btngroup
	margin-left: 10px
.height60
	height: 200px
	width: 800px
.height30
	/*height: 100px*/
.bottomBorder
	border-bottom: 1px solid #eeeeee
</style>
