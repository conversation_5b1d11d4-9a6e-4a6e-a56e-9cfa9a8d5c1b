<!--评价单列表组件-->
<template>
  <xpt-list-dynamic
    list2
    class="pingjiaList"
    :data="list"
    :colData="colData"
    :searchPage="search.page_name"
    :pageTotal="pageTotal"
    :btns="btns"
    isNeedClickEvent
    :showCount="showCount"
    @count-off="countOff"
    @search-click="searchData"
    @selection-change="selects"
    @page-size-change="pageChange"
    @current-page-change="currentPageChange"
    ref="xptList"
  >
    <span slot="btns">
      <xpt-import :taskUrl="uploadUrl" class="mgl10"></xpt-import>
      <el-button type="info" size="mini" @click="showExportList"
        >导入结果</el-button
      >
      <xpt-import
        :taskUrl="additionalUploadUrl"
        class="mgl10"
        text="追评/晒图导入"
      ></xpt-import>
      <xpt-import
        :taskUrl="addImportVipAppraiseListInfoUrl"
        class="mgl10"
        text="导入唯品会评价"
      ></xpt-import>
      <div style="display: inline-block; margin-top: 4px">
        <span style="padding-left: 8px; color: #999">下载评价单</span>
        <el-date-picker
          type="datetime"
          placeholder="开始日期"
          size="mini"
          v-model="startTime"
          :disabled="tid ? true : false"
          :editabled="tid ? true : false"
          style="width: 145px"
        ></el-date-picker>
        <span>-</span>
        <el-date-picker
          type="datetime"
          placeholder="结束日期"
          size="mini"
          v-model="endTime"
          :disabled="tid ? true : false"
          :editabled="tid ? true : false"
          style="width: 145px"
        ></el-date-picker>
        <el-input
          style="padding-left: 8px; color: #999"
          placeholder="按淘宝单号下载"
          size="mini"
          v-model="tid"
          :disabled="startTime || endTime ? true : false"
        ></el-input>
        <el-button type="primary" size="mini" @click="confirmLoad"
          >下载</el-button
        >
      </div>
    </span>
  </xpt-list-dynamic>
</template>
<script>
import Fn from "@common/Fn.js";
export default {
  props: ["params"],
  data() {
    let self = this;
    return {
      countOffFlag: false,
      showCount: false,
      uploadUrl: "/order-web/api/appraise/addImportAppraiseListInfo",
      additionalUploadUrl: "/order-web/api/appraise/addImportAppraiseAddInfo",
      addImportVipAppraiseListInfoUrl: "/order-web/api/appraise/addImportVipAppraiseListInfo",
      list: [],
      pageTotal: 0,
      startTime: null,
      endTime: null,
      tid: "",
      btns: [
        {
          type: "info",
          txt: "获取业务信息",
          click: self.getBusinessInfo,
        },
        {
          type: "success",
          txt: "已评定",
          click: self.haveBeenEvaluated,
        },
        {
          type: "primary",
          txt: "待评定",
          click: self.pendingEvaluation,
        },
      ],
      colData: [
        {
          label: "销售标签",
          prop: "sales_label",
          width: 70,
          html(val) {
            return val ? '<span style="color:red;">' + val + "</span>" : val;
          },
        },
        {
          label: "合并订单",
          prop: "merge_trade_no",
          redirectClick(row) {
            self.viewDetail(row.id);
          },
        },
        {
          label: "评价内容",
          width: 1300,
          prop: "content",
        },
        {
          label: "纸质单号",
          prop: "paper_no",
        },
        {
          label: "买家昵称",
          prop: "nick",
        },
        {
          label: "评价字数",
          prop: "content_length",
        },
        {
          label: "评价单状态",
          prop: "appraise_status",
          formatter: (row) => self.appraise_status[row],
        },
        {
          label: "自动评定状态",
          width: 100,
          prop: "auto_appraise_status",
          formatter: (row) => self.appraise_status[row],
        },
        {
          label: "业务员",
          prop: "staff_name",
        },
        {
          label: "业务员分组",
          prop: "staff_group_name",
        },
        {
          label: "业务员评价定性",
          width: 120,
          prop: "staff_type",
          formatter: (prop) => self.tranPerformancePer[prop],
        },
        {
          label: "业务员自动定性",
          width: 120,
          prop: "staff_evaluate",
        },
        {
          label: "业务员绩效评定",
          width: 120,
          prop: "staff_perfor_typeName",
        },
        {
          label: "售后专员",
          prop: "after_name",
        },
        {
          label: "售后专员分组",
          width: 120,
          prop: "after_group_name",
        },
        {
          label: "售后专员评价定性",
          width: 120,
          prop: "after_type",
          formatter: (prop) => self.tranPerformanceAfter[prop],
        },
        {
          label: "售后专员自动定性",
          width: 120,
          prop: "after_evaluate",
        },
        {
          label: "售后专员绩效评定",
          width: 120,
          prop: "after_perfor_typeName",
        },
        {
          label: "店铺",
          prop: "shop_name",
        },
        {
          label: "评价创建时间",
          prop: "created",
          width: 120,
          format: "dataFormat1",
        },
        {
          label: "淘宝单号",
          prop: "tid",
        },
        {
          label: "淘宝子单号",
          prop: "oid",
        },
        {
          label: "业务员评定时间",
          prop: "staff_determine_time",
          width: 120,
          format: "dataFormat1",
        },
        {
          label: "售后专员评定时间",
          prop: "after_determine_time",
          width: 120,
          format: "dataFormat1",
        },
        {
          label: "评定完结时间",
          prop: "fin_time",
          width: 120,
          format: "dataFormat1",
        },
        {
          label: "物流绩效评定",
          width: 120,
          prop: "logistics_performance_type",
          format: "auxFormat",
          formatParams: "LOGISTICS_PERFORMANCE_TYPE",
        },
        {
          label: "物流评价定性",
          prop: "logistics_type",
          width: 120,
          format: "auxFormat",
          formatParams: "performance_type_logistics",
        },
        {
          label: "物流自动定性",
          prop: "logistics_evaluate",
          width: 120,
        },
        {
          label: "物流评定人",
          prop: "logistics_determine_name",
          width: 120,
        },
        {
          label: "物流评定时间",
          prop: "logistics_determine_time",
          width: 120,
          format: "dataFormat1",
        },
        {
          label: "物流绩效评定人",
          prop: "logistics_performance_determine_name",
          width: 120,
        },
        {
          label: "物流绩效评定时间",
          prop: "logistics_performance_determine_time",
          width: 120,
          format: "dataFormat1",
        },
        {
          label: "是否晒图",
          prop: "if_blueprint",
          formatter: (val) => {
            return val == "Y" ? "是" : val == "N" ? "否" : val;
          },
        },
        {
          label: "是否视频",
          prop: "if_video",
          formatter: (val) => {
            return val == "Y" ? "是" : val == "N" ? "否" : val;
          },
        },
        {
          label: "追评内容",
          prop: "add_content",
          width: 1300,
        },
        {
          label: "追评时间",
          prop: "add_time",
          width: 120,
          format: "dataFormat1",
        },
        {
          label: "业务员追加绩效评定",
          width: 140,
          prop: "add_staff_perfor_typeName",
          width: 130,
        },
        {
          label: "售后专员追加绩效评定",
          width: 140,
          prop: "add_after_perfor_typeName",
        },
        {
          label: "业务员追评定性评价",
          prop: "add_staff_type",
          formatter: (val) =>
            self.staffAddPerformanceTypePreData.find((item) => item.code == val)
              ?.name || val,
          width: 130,
        },
        {
          label: "售后专员追评定性评价",
          prop: "add_after_type",
          formatter: (val) =>
            self.afterAddPerformanceTypePreData.find((item) => item.code == val)
              ?.name || val,
          width: 140,
        },
        {
          label: "业务员追评评定人",
          width: 140,
          prop: "add_staff_determine_name",
        },
        {
          label: "业务员追评评定时间",
          prop: "add_staff_determine_time",
          width: 130,
          format: "dataFormat1",
        },
        {
          label: "售后专员追评评定人",
          width: 140,
          prop: "add_after_determine_name",
        },
        {
          label: "售后专员追评评定时间",
          prop: "add_after_determine_time",
          width: 140,
          format: "dataFormat1",
        },
        {
          label: "业务员晒图定性",
          width: 120,
          prop: "staff_photo_type",
          format: "auxFormat",
          formatParams: "STAFF_PICTURE_PERFORMANCE_TYPE",
        },
        {
          label: "售后专员晒图定性",
          width: 120,
          prop: "after_photo_type",
          format: "auxFormat",
          formatParams: "AFTER_PICTURE_PERFORMANCE_TYPE",
        },
        {
          label: "晒图评定人",
          width: 120,
          prop: "photo_determine_name",
        },
        {
          label: "晒图评定时间",
          prop: "photo_determine_time",
          width: 130,
          format: "dataFormat1",
        },
        {
          label: "林氏到家评价标签",
          width: 200,
          prop: "lsdj_label",
        },
        {
          label: "物流追加定性评价",
          width: 120,
          prop: "add_logistics_type",
          format: "auxFormat",
          formatParams: "ADD_LOGISTICS_TYPE_PRE",
        },
        {
          label: "经销三包",
          width: 60,
          prop: "if_three_service_agency",
          format: "yesOrNo",
        },
        {
          label: "是否唯品会",
          prop: "if_vip",
          formatter: (val) => {
            return val == "Y" ? "是" : val == "N" ? "否" : val;
          },
        },
      ],
      statusList: {
        good: "好评",
        neutral: "中评",
        bad: "差评",
        GOOD: "好评",
        NEUTRAL: "中评",
        BAD: "差评",
      },
      appraise_status: { UN_APPRAISE: "待评定", APPRAISE: "已评定" },
      afterDingXinList: { GOOD: "好评", NEUTRAL: "中评", BAD: "差评" },
      // beforeDingXinList:{GOOD:"好评",NEUTRAL:"中评",BAD:"差评"},
      beforeBtnList: [],
      afterBtnList: [],
      tranPerformancePer: {},

      tranPerformanceAfter: {},
      staffAddPerformanceTypePreData: [],
      afterAddPerformanceTypePreData: [],
      performanceTypeAfterData: [],

      // 多选数据
      selectsData: [],
      search: {
        page_no: 1,
        page_size: self.pageSize,
        page_name: "appraise_table",
        where: [],
      },
    };
  },
  methods: {
    tidConfirmLoad() {
      if (!this.tid) {
        this.$message.error("请填写淘宝单号");
        return;
      }
      this.startTime = "";
      this.endTime = "";
      this.ajax.postStream(
        "/order-web/api/appraise/single",
        { tid: this.tid },
        (res) => {
          this.$message({
            type: res.body.result ? "success" : "error",
            message: res.body.msg,
          });
        }
      );
    },
    /**
     *下载评价单
     **/
    confirmLoad() {
      if (!this.startTime && !this.endTime && !this.tid) {
        this.$message.error("请选择时间或者填写淘宝单号");
        return;
      }
      if (!this.tid) {
        if (!this.startTime || !this.endTime) {
          this.$message.error("请选择时间");
          return;
        }
      }
      if (this.tid) {
        let self = this;
        new Promise((resolve, reject) => {
          this.getAppraiseTableAllByTid(resolve, reject);
        }).then(() => {
          self.ajax.postStream(
            "/order-web/api/appraise/single",
            { tid: this.tid },
            (res) => {
              self.$message({
                type: res.body.result ? "success" : "error",
                message: res.body.msg,
              });
            }
          );
        });
      } else {
        let startTime = new Date(this.startTime).getTime();
        let endTime = new Date(this.endTime).getTime();
        if (startTime >= endTime) {
          this.$message.error("开始时间要小于结束时间");
          return;
        }
        this.ajax.postStream(
          "/order-web/api/appraise/manual",
          { start_time: startTime, end_time: endTime },
          (res) => {
            this.$message({
              type: res.body.result ? "success" : "error",
              message: res.body.msg,
            });
          }
        );
      }
    },
    getAppraiseTableAllByTid(resolve, reject) {
      this.ajax.postStream(
        "/order-web/api/appraise/getAppraiseTableAllByTid",
        { tid: this.tid },
        (res) => {
          if (res.body.result) {
            // this.$message.success(res.body.msg)
            resolve && resolve();
          } else {
            this.$message.error(res.body.msg);
            reject && reject();
          }
        },
        (err) => {
          this.$message.error(res.body.msg);
          reject && reject();
        }
      );
    },
    // 导出
    showExportList() {
      let params = {
          list_excel_type: [
            "EXCEL_TYPE_APPRAISE_IMPORT",
            "EXCEL_TYPE_APPRAISE_ADD_IMPORT",
          ],
          url: "/order-web/api/appraise/import/list",
        },
        self = this;

      params.callback = (d) => {};
      self.$root.eventHandle.$emit("alert", {
        params: params,
        component: () => import("@components/evaluate/import_result.vue"),
        style: "width:800px;height:500px",
        title: "导入结果",
      });
    },
    //表头查询
    searchData(where, resolve) {
      let self = this;
      this.search.where = where;
      // this._getList(resolve);
      new Promise((res, rej) => {
        this._getList(resolve, res);
      }).then(() => {
        if (self.search.page_no != 1) {
          self.pageTotal = 0;
        }
        self.showCount = false;
      });
    },
    //获取业务信息
    getBusinessInfo() {
      var self = this;
      var dataList = self.getIds();
      if (!(dataList && dataList.length > 0)) {
        self.$message.error("请至少选择一行数据");
        return;
      } else {
        self.ajax.postStream(
          "/order-web/api/appraise/composite",
          dataList,
          function (response) {
            if (response.body.result) {
              self.$message({ message: "操作成功", type: "success" });
              self._getList();
            } else {
              self.$message.error(response.body.msg || "");
              self._getList();
            }
          },
          (e) => {
            self.$message.error(e);
          }
        );
      }
    },
    //判断业务员评价是否能操作
    isStaffCan() {
      var self = this;
      var flag = true;
      if (self.selectsData && self.selectsData.length > 0) {
        for (var i = 0; i < self.selectsData.length; i++) {
          if (!self.selectsData[i].staff) {
            flag = false;
            break;
          }
        }
      }
      return flag;
    },
    //判断售后专员评价是否能操作
    isAfterCan() {
      var self = this;
      var flag = true;
      if (self.selectsData && self.selectsData.length > 0) {
        for (var i = 0; i < self.selectsData.length; i++) {
          if (!self.selectsData[i].after) {
            flag = false;
            break;
          }
        }
      }
      return flag;
    },
    //判断物流定性评价是否能操作
    isLogisticsCan() {
      return (
        this.selectsData &&
        this.selectsData.length > 0 &&
        this.selectsData.every((item) => !!item.staff)
      );
    },
    //业务员定性评价
    salesmanEvaluation(type) {
      var self = this;
      var dataList = self.getIds();
      if (!(dataList && dataList.length > 0)) {
        self.$message.error("请至少选择一行数据");
        return;
      } else if (!self.isStaffCan()) {
        self.$message.error("所选数据包含业务员为空，不能定性评价");
        return;
      } else {
        var postData = { id: dataList, staffType: type };
        self.ajax.postStream(
          "/order-web/api/appraise/staffType?permissionCode=EVALUVTE_ORDER_SALESMAN_EVALUVTE",
          postData,
          function (response) {
            if (response.body.result) {
              self.$message({ message: "操作成功", type: "success" });
              self.selectsData.forEach((obj) => {
                obj.staff_type = type;
              });
              // self._getList();
            } else {
              self.$message.error(response.body.msg || "");
              // self._getList();
            }
          },
          (e) => {
            self.$message.error(e);
          }
        );
      }
    },
    //售后专员定性评价
    aftersaleEvaluation(type) {
      var self = this;
      var dataList = self.getIds();
      if (!(dataList && dataList.length > 0)) {
        self.$message.error("请至少选择一行数据");
        return;
      } else if (!self.isAfterCan()) {
        self.$message.error("所选数据包含售后专员为空，不能定性评价");
        return;
      } else {
        var postData = { id: dataList, afterType: type };
        self.ajax.postStream(
          "/order-web/api/appraise/afterType?permissionCode=EVALUVTE_ORDER_CUSTOMER_SERVICE_EVALUVTE",
          postData,
          function (response) {
            if (response.body.result) {
              self.$message({ message: "操作成功", type: "success" });
              self.selectsData.forEach((obj) => {
                self.list.forEach((item) => {
                  if (item == obj) {
                    item.after_type = type;
                  }
                });
              });
              // self._getList();
            } else {
              self.$message.error(response.body.msg || "");
              // self._getList();
            }
          },
          (e) => {
            self.$message.error(e);
          }
        );
      }
    },
    //物流定性评价
    logisticsEvaluation(type) {
      console.log(type);
      var self = this;
      var dataList = self.getIds();
      if (dataList && dataList.length == 0) {
        self.$message.error("请至少选择一行数据");
        return;
      } else if (!self.isLogisticsCan()) {
        self.$message.error("所选数据包含业务员为空，不能定性评价");
        return;
      } else {
        let params = { id: dataList, logisticsType: type };
        self.ajax.postStream(
          "/order-web/api/appraise/logisticsType?permissionCode=EVALUVTE_ORDER_SALESMAN_EVALUVTE",
          params,
          function (response) {
            if (response.body.result) {
              self.$message({ message: "操作成功", type: "success" });
              self.selectsData.forEach((obj) => {
                obj.logistics_type = type;
              });
            } else {
              self.$message.error(response.body.msg || "");
            }
          },
          (e) => {
            self.$message.error(e);
          }
        );
      }
    },
    //物流绩效定性
    logisticsPerformanceEvaluation(type) {
      console.log(type);
      var self = this;
      var dataList = self.getIds();
      // if (dataList && dataList.length == 0) {
      //   self.$message.error("请至少选择一行数据");
      //   return;
      // } else if (!self.isLogisticsCan()) {
      //   self.$message.error("所选数据包含业务员为空，不能定性评价");
      //   return;
      // } else {
      let params = { id: dataList, logisticsPerformanceType: type };
      self.ajax.postStream(
        "/order-web/api/appraise/logisticsPerformanceType?permissionCode=EVALUVTE_ORDER_SALESMAN_EVALUVTE",
        params,
        function (response) {
          if (response.body.result) {
            self.$message({ message: "操作成功", type: "success" });
            self.selectsData.forEach((obj) => {
              obj.logistics_type = type;
            });
          } else {
            self.$message.error(response.body.msg || "");
          }
        },
        (e) => {
          self.$message.error(e);
        }
      );
      // }
    },
    //获取参数的type_id
    getIds() {
      var self = this;
      var resList = [];
      if (self.selectsData && self.selectsData.length > 0) {
        for (var i = 0; i < self.selectsData.length; i++) {
          resList.push(self.selectsData[i].id);
        }
      }
      return resList;
    },
    //已评定
    haveBeenEvaluated() {
      var self = this;
      var dataList = self.getIds();
      if (!(dataList && dataList.length > 0)) {
        self.$message.error("请至少选择一行数据");
        return;
      } else {
        self.ajax.postStream(
          "/order-web/api/appraise/toAppraise?permissionCode=EVALUVTE_ORDER_EVALUVTED",
          dataList,
          function (response) {
            if (response.body.result) {
              self.$message({ message: "操作成功", type: "success" });
              self._getList();
            } else {
              self.$message.error(response.body.msg || "");
              self._getList();
            }
          },
          (e) => {
            self.$message.error(e);
          }
        );
      }
    },
    //待评定
    pendingEvaluation() {
      var self = this;
      var dataList = self.getIds();
      if (!(dataList && dataList.length > 0)) {
        self.$message.error("请至少选择一行数据");
        return;
      } else {
        self.ajax.postStream(
          "/order-web/api/appraise/unAppraise?permissionCode=EVALUVTE_ORDER_PENDING_EVALUVTE",
          dataList,
          function (response) {
            if (response.body.result) {
              self.$message({ message: "操作成功", type: "success" });
              self._getList();
            } else {
              self.$message.error(response.body.msg || "");
              self._getList();
            }
          },
          (e) => {
            self.$message.error(e);
          }
        );
      }
    },
    // 多选
    selects(val) {
      var self = this;
      self.selectsData = val;
    },
    // 监听每页显示数更改事件
    pageChange(pageSize) {
      // this.pageSize = pageSize
      // this.pageNow = 1;
      this.search.page_size = pageSize;
      this.search.page_no = 1;
      this._getList();
    },
    // 监听页数更改事件
    currentPageChange(page) {
      // this.pageNow = page
      this.search.page_no = page;
      this._getList();
    },
    // 查看详情
    viewDetail(id) {
      var params = {};
      params.id = id;
      params.evaluateList = this.list;
      this.$root.eventHandle.$emit("creatTab", {
        name: "编辑评价单",
        params: params,
        component: () => import("@components/evaluate/evaluateDetail"),
      });
    },
    countOff() {
      console.log(123);
      let self = this,
        url =
          "/order-web/api/appraise/listCount?permissionCode=EVALUVTE_ORDER_QUERY";
      if (!self.list.length) {
        self.$message.error("当前列表为空，先搜索内容");
        return;
      }
      if (!!self.countOffFlag) {
        self.$message.error("请勿重复点击");
        return;
      }
      self.countOffFlag = true;

      self.ajax.postStream(url, self.search, function (response) {
        if (response.body.result) {
          self.pageTotal = response.body.content;
          self.showCount = true;
          self.countOffFlag = false;
        } else {
          self.$message.error(response.body.msg);
        }
      });
    },
    // 设置物流追加定性评价
    addLogisticsType(type) {
      var self = this;
      var dataList = self.getIds();
      if (dataList && dataList.length == 0) {
        self.$message.error("请至少选择一行数据");
        return;
      } else if (!self.isLogisticsCan()) {
        self.$message.error("所选数据包含业务员为空，不能定性评价");
        return;
      } else {
        let params = { id: dataList, addLogisticsType: type };
        self.ajax.postStream(
          "/order-web/api/appraise/addLogisticsType?permissionCode=EVALUVTE_ORDER_SALESMAN_EVALUVTE",
          params,
          function (response) {
            if (response.body.result) {
              self.$message({ message: "操作成功", type: "success" });
              self.selectsData.forEach((obj) => {
                obj.logistics_type = type;
              });
            } else {
              self.$message.error(response.body.msg || "");
            }
          },
          (e) => {
            self.$message.error(e);
          }
        );
      }
    },
    //查询列表
    _getList(resolve, resolve2) {
      var self = this;
      self.ajax.postStream(
        "/order-web/api/appraise/list?permissionCode=EVALUVTE_ORDER_QUERY",
        this.search,
        function (d) {
          if (d.body.result) {
            // self.list=d.body.content.list;
            // self.pageTotal=d.body.content.count;

            let dataList = JSON.parse(JSON.stringify(d.body.content.list));
            if (
              dataList.length == self.search.page_size + 1 &&
              dataList.length > 0
            ) {
              dataList.pop();
            }
            self.list = dataList;
            let totalCount = self.search.page_no * self.search.page_size;
            if (!self.showCount) {
              self.pageTotal =
                d.body.content.list.length == self.search.page_size + 1
                  ? totalCount + 1
                  : totalCount;
            }
          } else {
            self.list = [];
            self.pageTotal = 0;
            self.$message.error(d.body.msg || "");
          }
          self.selectRole = [];
          resolve && resolve();
          resolve2 && resolve2();
        },
        (err) => {
          this.$message.error(err);
          resolve && resolve();
          resolve2 && resolve2();
        }
      );
    },
  },
  mounted() {
    let i,
      self = this,
      perData = __AUX.getValidData("performancePer"),
      afterData = __AUX.getValidData("performanceAfter"),
      logisticsData = __AUX.getValidData("performance_type_logistics"),
      logisticsPeformanceData = __AUX.getValidData(
        "LOGISTICS_PERFORMANCE_TYPE"
      ),
      logisticsAddTypeData = __AUX.getValidData(
        "ADD_LOGISTICS_TYPE_PRE"
      ),
      perList = [],
      afterList = [];
    self.staffAddPerformanceTypePreData = __AUX.getValidData(
      "STAFF_ADD_PERFORMANCE_TYPE_PRE"
    );
    self.afterAddPerformanceTypePreData = __AUX.getValidData(
      "AFTER_ADD_PERFORMANCE_TYPE_PRE"
    );
    // 设置售前专员定评价
    i = perData.length;
    while (i--) {
      self.tranPerformancePer[perData[i].code] = perData[i].name;

      perList.push({
        type: "info",
        txt: perData[i].name,
        value: perData[i].code,
        click(val) {
          self.salesmanEvaluation(val);
        },
      });
    }
    self.btns.push({
      isBtnGroup: true,
      btnGroupList: [
        {
          type: "info",
          txt: "业务员定性评价",
          click() {
            self.$message.info("请选择下拉选项。");
          },
        },
      ].concat(perList.reverse()),
    });

    // 设置售后专员定评价
    i = afterData.length;
    while (i--) {
      self.tranPerformanceAfter[afterData[i].code] = afterData[i].name;
      afterList.push({
        type: "primary",
        txt: afterData[i].name,
        value: afterData[i].code,
        click(val) {
          self.aftersaleEvaluation(val);
        },
      });
    }
    self.btns.push({
      isBtnGroup: true,
      btnGroupList: [
        {
          type: "primary",
          txt: "售后专员定性评价",
          click() {
            self.$message.info("请选择下拉选项。");
          },
        },
      ].concat(
        afterList.sort((a, b) => {
          if (/差/.test(a.txt)) return 1;
          else if (/差/.test(b.txt)) return -1;
          else if (/好/.test(b.txt)) return 1;
          else if (/好/.test(a.txt)) return -1;
        })
      ),
    });
    //设置物流定性评价
    let logisticsList = logisticsData.map((item) => {
      return {
        type: "info",
        txt: item.name,
        value: item.code,
        click(val) {
          self.logisticsEvaluation(val);
        },
      };
    });
    self.btns.push({
      isBtnGroup: true,
      btnGroupList: [
        {
          type: "info",
          txt: "物流定性评价",
          click() {
            self.$message.info("请选择下拉选项。");
          },
        },
      ].concat(logisticsList),
    });
    //设置物流绩效定性
    let logisticsList1 = logisticsPeformanceData.map((item) => {
      return {
        type: "info",
        txt: item.name,
        value: item.code,
        click(val) {
          self.logisticsPerformanceEvaluation(val);
        },
      };
    });
    self.btns.push({
      isBtnGroup: true,
      btnGroupList: [
        {
          type: "info",
          txt: "物流绩效定性",
          click() {
            self.$message.info("请选择下拉选项。");
          },
        },
      ].concat(logisticsList1),
    });

    // 设置物流追加定性评价
    let logisticsAddList = logisticsAddTypeData.map((item) => {
      return {
        type: "info",
        txt: item.name,
        value: item.code,
        click(val) {
          self.addLogisticsType(val);
        },
      };
    });
    self.btns.push({
      isBtnGroup: true,
      btnGroupList: [
        {
          type: "info",
          txt: "物流追加定性",
          click() {
            self.$message.info("请选择下拉选项。");
          },
        },
      ].concat(logisticsAddList),
    });
  },
};
</script>
