<!-- 价目表--导入结果 -->
<template>
	<xpt-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:pageTotal='count'
		selection=''
		@page-size-change='pageSizeChange'
		@current-page-change='currentPageChange'
	>
	 <template slot-scope="scope" slot='path'>
        <el-button @click.native.prevent="upload(scope.row)" type="text" size="small">下载</el-button>
      </template>
	  </xpt-list>
</template>
<script>
export default {
	props: ['params'],
	data() {
		let self = this
		return {
			search: {
				excel_type: self.params.excel_type,
        list_excel_type: self.params.list_excel_type,
				page_no: 1,
				page_size: self.pageSize
			},
			btns: [{
				type: 'primary',
				txt: '刷新',
				click: self.getData
			}],
			cols: [
				{
					label: '程序名称',
					prop: 'program_name'
				}, {
					label: '参数',
					prop: 'parameter'
				}, {
					label: '文件路径',
					slot: 'path'
				}, {
					label: '运行状态',
					prop: 'run_status',
					formatter: self.statusFilter
				}, {
					label: '程序状态',
					prop: 'program_status',
					formatter: self.statusFilter
				}, {
					label: '提交时间',
					prop: 'submit_time',
					format: 'dataFormat1'
				}, {
					label: '完成时间',
					prop: 'finish_time',
					format: 'dataFormat1'
				}, {
					label: '请求日志',
					prop: 'text'
				}, {
					label: '结果',
					prop: 'result'
				}
			],
			count: 0,
			list: []
		}
	},
	methods: {
		upload(row){
			window.open(row.date_text)
		},
		getData() {
			this.ajax.postStream(this.params.url, this.search, res => {
				let data = res.body.content
				if(res.body.result) {
					this.list = data ? data.list : []
					this.count = data ? data.count : 0
				} else {
					this.$message({
						type: 'error',
						message: res.body.msg
					})
				}
			})
		},
		export(){
			this.ajax.postStream('/reports-web/api/reports/exportAvailableScale', this.search, res => {
				let data = res.body.content
				if(res.body.result) {
					// this.list = data ? data.list : []
					// this.count = data ? data.count : 0
				} else {
					this.$message({
						type: 'error',
						message: res.body.msg
					})
				}
			})
		},
		pageSizeChange(pageSize) {
			this.search.page_size = pageSize
			this.getData()
		},
		currentPageChange(page) {
			this.search.page_no = page
			this.getData()
		},
		statusFilter(val) {
			switch(val) {
				case 'EXEC_FAILED': return '失败';
				case 'EXEC_SUCCESS': return '成功';
				case 'EXEC_WAIT': return '等待';
				case 'EXEC_RUNING': return '执行中';
				default: return val;
			}
		}
	},
	mounted() {
		this.getData()
	}
}
</script>
