//新增火凤凰商品
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='primary' size='mini' @click="saveExerion">确定</el-button>
      </el-col>
    </el-row>
    <el-form label-position="right" label-width="100px">
      <el-row>
        <el-col :span="6" style="width: 50%">
        <el-form-item label="店铺编码：" style="height: 50px;" required>
          <el-input v-model="goodsInfo.shop_code" size='mini' icon="search" :on-icon-click="selectShop" style="width: 150px;" :maxlength='20' readonly></el-input>
        </el-form-item>
        <el-form-item label="商品页面链接：" style="height: 50px;" required>
          <el-input v-model="goodsInfo.good_id" size='mini' style="width: 150px;" :maxlength='500'></el-input>
        </el-form-item>
        <el-form-item label="商品名称：" required>
          <el-input v-model="goodsInfo.exteral_material_name" size='mini' style="width: 150px;"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6" style="width: 50%">
        <el-form-item label="生效时间：" style="height: 50px;" required>
          <el-date-picker v-model="goodsInfo.valid_time" type="datetime" style="width: 150px;" :picker-options="startDatePickerOptions" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
        </el-form-item>
        <el-form-item label="失效时间：" style="height: 50px;" required>
          <el-date-picker v-model="goodsInfo.invalid_time" type="datetime" style="width: 150px;" :picker-options="endDatePickerOptions" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
        </el-form-item>
      </el-col>
      </el-row>
      <div class="goods">
        <div class="goods-title">
          <el-form-item label="商品图：" required>
          </el-form-item>
        </div>
        <div class="goods-item">
          <el-radio class="goods-radio" v-model="goodsInfo.external_material_switch" label="1">
            <el-form-item label="淘宝商品ID：" label-width="80">
              <el-input v-model="goodsInfo.external_material_no" size='mini' style="width: 150px;"></el-input>
            </el-form-item>
          </el-radio>
        </div>
        <div class="goods-item">
          <el-radio class="goods-radio" v-model="goodsInfo.external_material_switch" label="2">
            <el-form-item label="无价格商品图链接："  label-width="110">
              <el-input v-model="goodsInfo.external_material_pic" size='mini' style="width: 150px;"></el-input>
            </el-form-item>
          </el-radio>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
  import moment from 'moment';
    export default {
      name: "addExerion",
      props:["params"],
      data() {
        return {
          goodsInfo:{
            shop_id:"",
            shop_code:"",
            good_id:"",
            valid_time: "",
            invalid_time: "",
            exteral_material_name:"",
            external_material_no:"",
            external_material_pic:"",
            external_material_switch:'1',
          },
          startDatePickerOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7;
            },
            date: moment().startOf('day').toDate(),
          },
          endDatePickerOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7;
            },
            date: moment().endOf('day').toDate(),
          },
        }
      },
      methods:{
        selectShop(){
          this.$root.eventHandle.$emit('alert', {
            params: {
              callback: data => {
                this.goodsInfo.shop_code = data.shop_code;
                this.goodsInfo.shop_id = data.shop_id;
              },
            },
            component: ()=>import('@components/exerion_shop_list/shopList'),
            style: 'width:800px;height:500px',
            title: '店铺列表',
          })
        },
        saveExerion(){
        //   const result = /^\d+$/.test(this.goodsInfo.good_id);
        //   console.log("result",result);
          console.log("goodsInfo",this.goodsInfo);
          if (!this.goodsInfo.shop_code || this.goodsInfo.shop_code === ""){
            this.$message.error("店铺编码不能为空！");
            return;
          }
          if (!this.goodsInfo.exteral_material_name || this.goodsInfo.exteral_material_name === ""){
            this.$message.error("商品名称不能为空！");
            return;
          }
          if (!this.goodsInfo.external_material_switch){
            this.$message.error("请选择一个商品图！");
            return;
          }
          if (!(this.goodsInfo.external_material_no||this.goodsInfo.external_material_pic)){
            this.$message.error("请至少填写一个商品图！");
            return;
          }
          if (!this.goodsInfo.good_id || this.goodsInfo.good_id === ""){
            this.$message.error("商品ID不能为空！");
            return;
          }
        //   if (!result) {
        //     this.$message.error("商品ID格式不符，只能为数字！");
        //     return;
        //   }
          if (!this.goodsInfo.valid_time || this.goodsInfo.valid_time === ""){
            this.$message.error("生效时间不能为空！");
            return;
          }
          if (!this.goodsInfo.invalid_time || this.goodsInfo.invalid_time === ""){
            this.$message.error("失效时间不能为空！");
            return;
          }
          let nowDate = new Date();
          console.log("nowDate",nowDate);
          if (this.goodsInfo.invalid_time <= nowDate.getTime()){
            this.$message.error("失效时间必须大于当前时间！");
            return;
          }
          console.log(this.goodsInfo.valid_time.getTime(),this.goodsInfo.invalid_time.getTime());
          if (this.goodsInfo.invalid_time.getTime() <= this.goodsInfo.valid_time.getTime()) {
            this.$message.error("失效时间必须大于生效时间！");
            return;
          }
          console.log(this.goodsInfo.valid_time,this.goodsInfo.invalid_time);
          let data = {
            shop_id:this.goodsInfo.shop_id,
            external_material_id:this.goodsInfo.good_id,
            enable_time:this.goodsInfo.valid_time,
            disable_time:this.goodsInfo.invalid_time,
            exteral_material_name:this.goodsInfo.exteral_material_name,
            external_material_no:this.goodsInfo.external_material_no,
            external_material_pic:this.goodsInfo.external_material_pic,
            external_material_switch:this.goodsInfo.external_material_switch,
          };
          this.ajax.postStream('/material-web/api/exteralMaterial/saveOrUpdateInfo',data,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              //关闭弹窗
              this.params.callback();
              this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        }
      },
    }
</script>

<style scoped>
.goods{
  display: flex;
  align-items: center;
  margin-top: 28px;
}
.goods-title{
  width: 100px;
}
.goods-item{
  flex:1;
}
.goods-radio{
  display: flex;
  align-items: center;
}
</style>
