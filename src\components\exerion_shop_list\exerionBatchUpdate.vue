//批量修改
<template>
    <div class="xpt-flex">
      <el-row	class='xpt-top'	:gutter='10'>
        <el-form label-position="right" label-width="100px">
          <el-col :span='6'>
            <el-form-item label="商品ID：">
              <el-input v-model="goodsInfo.goodId" size='mini' style="width: 160px;" :maxlength='20'></el-input>
            </el-form-item>
            <el-form-item label="店铺地区：">
              <xpt-select-aux v-model='goodsInfo.shopArea' aux_name='shopArea' style="width: 160px;"></xpt-select-aux>
            </el-form-item>
            <el-form-item label="新的商品ID：">
              <el-input v-model="goodsInfo.newGoodId" size='mini' style="width: 160px;" :maxlength='20'></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='6'>
            <el-form-item label="创建(开始)时间：">
              <el-date-picker v-model="goodsInfo.createStartTime" type="datetime" style="width: 160px;" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
            </el-form-item>
            <el-form-item label="生效时间：">
              <el-date-picker v-model="goodsInfo.validTime" type="date" style="width: 160px;" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
            </el-form-item>
            <el-form-item label="新的生效时间：">
              <el-date-picker v-model="goodsInfo.newValidTime" type="date" style="width: 160px;" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span='6'>
            <el-form-item label="创建(结束)时间：">
              <el-date-picker v-model="goodsInfo.createEndTime" type="datetime" style="width: 160px;" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
            </el-form-item>
            <el-form-item label="失效时间：">
              <el-date-picker v-model="goodsInfo.invalidTime" type="date" style="width: 160px;" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
            </el-form-item>
            <el-form-item label="新的失效时间：">
              <el-date-picker v-model="goodsInfo.newInvalidTime" type="date" style="width: 160px;" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span='6' style="text-align: right">
            <el-form-item>
            </el-form-item>
            <el-button type='success' size='mini' @click="getList" :disabled="ifSearch" :loading="ifSearchLoad">查询</el-button><br>
            <el-button type='primary' size='mini' @click="save" :disabled="ifConfirm" :loading="ifConfirmLoad">确认修改</el-button>
          </el-col>
        </el-form>
      </el-row>
      <xpt-list
        :data="dataList"
        :colData="cols"
        :selection="selection"
        :showHead="false"
        :orderNo="true"
        @selection-change='select'
      ></xpt-list>
    </div>
</template>

<script>
    export default {
      name: "exerionBatchUpdate",
      data() {
        let self = this;
        return {
          dataList:[],
          selection:"checkbox",
          selectRow:[],
          ifSearch:false,
          ifSearchLoad:false,
          ifConfirmLoad:false,
          ifConfirm:false,
          goodsInfo:{
            goodId:"",
            shopArea:"",
            createStartTime:"",
            createEndTime:"",
            newGoodId:"",
            validTime:"",
            newValidTime:"",
            invalidTime:"",
            newInvalidTime:"",
          },
          cols:[
            {
              label: "店铺编码",
              prop: "shop_code",
              width: 120,
              redirectClick(row) {
                let params = {};
                params.id = row.id;
                self.$root.eventHandle.$emit('creatTab', {
                  name: "火凤凰商品详情",
                  params: params,
                  component: () => import('@components/exerion_shop_list/exerionDetail.vue')
                });
              },
            },
            {
              label: "店铺名称",
              prop: "shop_name",
              width: 140
            },
            {
              label: "店铺地区",
              prop: "shop_area",
              format: 'auxFormat',
              formatParams: 'shopArea',
              width: 100
            },
            {
              label: "商品ID",
              prop: "external_material_id",
              width: 120
            },
            {
              label: "生效时间",
              prop: "enable_time",
              width: 140,
              format: "dataFormat3"
            },
            {
              label: "失效时间",
              prop: "disable_time",
              width: 140,
              format: "dataFormat3"
            },
            {
              label: "创建人",
              prop: "create_name",
              width: 100
            },
            {
              label: "创建时间",
              prop: "create_time",
              width: 140,
              format: "dataFormat1"
            },
          ],
        }
      },
      methods:{
        //查询
        getList(){
          const result = /^\d+$/.test(this.goodsInfo.goodId);
          if (!result && this.goodsInfo.goodId !== "") {
            this.$message.error("商品ID格式不符，只能为数字！");
            return;
          }
          let nowDate = new Date();
          console.log("nowDate",nowDate);
          if (this.goodsInfo.invalidTime !== "" && this.goodsInfo.invalidTime <= nowDate.getTime()){
            this.$message.error("失效时间必须大于当前时间！");
            return;
          }
          if (this.goodsInfo.invalidTime !== "" && this.goodsInfo.invalidTime <= this.goodsInfo.validTime){
            this.$message.error("失效时间必须大于生效时间！");
            return;
          }
          let data={
            external_material_id:this.goodsInfo.goodId,
            disable_time:this.goodsInfo.invalidTime
          };
          this.ifSearch = true;
          this.ifSearchLoad = true;
          this.ajax.postStream('/material-web/api/exteralMaterial/batchUpdateList',data,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            this.ifSearch = false;
            this.ifSearchLoad = false;
          }, err => {
            this.$message.error(err);
            this.ifSearch = false;
            this.ifSearchLoad = false;
          });
        },
        //修改
        save(){
          const result = /^\d+$/.test(this.goodsInfo.newGoodId);
          if (!result && this.goodsInfo.newGoodId !== "") {
            this.$message.error("新的商品ID格式不符，只能为数字！");
            return;
          }
          if (this.goodsInfo.newGoodId === "" && this.goodsInfo.newInvalidTime === "" && this.goodsInfo.newValidTime === ""){
            this.$message.error("修改的提交参数不能为空！");
            return;
          }
          if (!this.selectRow || this.selectRow.length === 0){
            this.$message.error("请选择要修改的数据！");
            return;
          }
          if (this.goodsInfo.createStartTime >= this.goodsInfo.createEndTime) {
            this.$message.error("创建时间范围不规范，请重新选择！");
            return;
          }
          let nowDate = new Date();
          console.log("nowDate",nowDate);
          if (this.goodsInfo.newInvalidTime !== "" && this.goodsInfo.newInvalidTime <= nowDate.getTime()){
            this.$message.error("新的失效时间必须大于当前时间！");
            return;
          }
          let ids={},lists=[],shopCode=[],data={};
          this.selectRow.find(d=>{
            ids = {
              id:d.id,
              shop_name:d.shop_name,
              enable_time:d.enable_time,
              shop_id:d.shop_id
            };
            lists.push(ids);
            shopCode.push(d.shop_code);
          });
          console.log("lists",lists);
          let nary=shopCode.sort(),count = 0;
          for (let i=0; i< shopCode.length; i++){
            if(nary[i] === nary[i+1]){
              count++;
            }
          }
          if (count > 0){
            this.$message.error("不能同时对同一个店铺的多条数据进行修改！");
            return;
          }
          data = {
            lists:lists,
            external_material_id:this.goodsInfo.newGoodId,
            disable_time:this.goodsInfo.newInvalidTime
          };
          this.ifConfirm = true;
          this.ifConfirmLoad = true;
          this.ajax.postStream('/material-web/api/exteralMaterial/batch_update',data,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getList();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            this.ifConfirm = false;
            this.ifConfirmLoad = false;
          }, err => {
            this.$message.error(err);
            this.ifConfirm = false;
            this.ifConfirmLoad = false;
          });
        },
        // 多选事件
        select(s){
          this.selectRow = s;
        },
      },
    }
</script>

<style scoped>

</style>
