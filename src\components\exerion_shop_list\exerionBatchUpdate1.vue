//批量修改
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='20'>
        <el-button type='primary' size='mini' @click="save">确认</el-button>
      </el-col>
    </el-row>
    <el-form label-position="left" label-width="110px">
      <el-col :span='6'>
        <el-form-item label="新的失效时间：" required>
          <el-date-picker v-model="newInvalidTime" type="datetime" style="width: 150px;" :picker-options="pickerOptions" placeholder="选择日期" size='mini' :editable='false'></el-date-picker>
        </el-form-item>
      </el-col>
    </el-form>
    <el-form label-position="left" label-width="400px">
      <el-col :span='6'>
        <el-form-item label="注：批量修改只会修改列表中生效区间有效的数据" style="font-size: 8px;" required>
        </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>

<script>
  import moment from 'moment';
    export default {
      name: "exerionBatchUpdate1",
      props:["params"],
      data(){
        return {
          newInvalidTime:"",
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7;
            },
            date: moment().endOf('day').toDate(),
          },
        }
      },
      methods:{
        save(){
          if (this.newInvalidTime === ""){
            this.$message.error("修改的提交参数不能为空！");
            return;
          }
          let nowDate = new Date();
          console.log("nowDate",nowDate);
          if (this.newInvalidTime !== "" && this.newInvalidTime <= nowDate.getTime()){
            this.$message.error("新的失效时间必须大于当前时间！");
            return;
          }
          let ids={},lists=[],data={},count = 0;
          this.params.selectRow.find(d=>{
            ids = {
              id:d.id,
              shop_name:d.shop_name,
              enable_time:d.enable_time,
              disable_time:d.disable_time,
              shop_id:d.shop_id
            };
            if (this.newInvalidTime <= ids.enable_time){
              let msg = "修改失败，店铺【" + ids.shop_name + "】的生效时间大于或等于修改的失效时间！";
              this.$message.error(msg);
              count++;
              return;
            }
            lists.push(ids);
          });
          if (count > 0){
            return;
          }
          console.log("lists",lists);
          data = {
            lists:lists,
            disable_time:this.newInvalidTime
          };
          this.ajax.postStream('/material-web/api/exteralMaterial/batch_update',data,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              //关闭弹窗
              this.params.callback();
              this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        }
      }
    }
</script>

<style scoped>

</style>
