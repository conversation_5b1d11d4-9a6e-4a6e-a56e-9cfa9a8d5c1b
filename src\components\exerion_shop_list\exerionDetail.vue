//火凤凰商品详情
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='24'>
        <el-button type='success' size='mini' @click="getDetail">刷新</el-button>
        <el-button type='info' size='mini' @click="save" :disabled="ifInvalid">保存</el-button>
      </el-col>
    </el-row>
    <el-row	:gutter='40' >
      <el-tabs v-model="firstTab" >
        <el-tab-pane label="商品信息" name="goodsInfo">
          <el-form label-position="right" label-width="140px">
            <el-row>
              <el-col :span="6" style="width: 33%">
              <el-form-item label="店铺编码：" >
                <el-input v-model="dataList.shop_code" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="店铺名称：" >
                <el-input v-model="dataList.shop_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="创建人：" >
                <el-input v-model="dataList.create_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="更新人：" >
                <el-input v-model="dataList.modify_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="width: 33%">
              <el-form-item label="店铺地区：" >
                <xpt-select-aux v-model='dataList.shop_area' aux_name='shopArea' aria-placeholder=" " style="width: 200px;" disabled></xpt-select-aux>
                <!--<el-input v-model="dataList.shop_area" size='mini' style="width: 200px;" disabled></el-input>-->
              </el-form-item>
              <el-form-item label="商品页面链接：" >
                <el-input v-model="goodsInfo.goodId" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="创建时间：" >
                <el-input v-model="dataList.create_time" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              <el-form-item label="更新时间：" >
                <el-input v-model="dataList.modify_time" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="width: 34%">
              <el-form-item label="生效时间：" >
                <el-date-picker v-model="goodsInfo.validTime" type="datetime" style="width: 200px;" placeholder="选择日期" size='mini' :editable='false' disabled></el-date-picker>              </el-form-item>
              <el-form-item label="失效时间："  v-if="ifValid" required>
                <el-date-picker v-model="goodsInfo.invalidTime" type="datetime" style="width: 200px;" placeholder="选择日期" :picker-options="pickerOptions" size='mini' :editable='false'></el-date-picker>
              </el-form-item>
              <el-form-item label="失效时间："  v-else>
                <el-date-picker v-model="goodsInfo.invalidTime" type="datetime" style="width: 200px;" placeholder="选择日期" :picker-options="pickerOptions" size='mini' :editable='false' disabled></el-date-picker>
              </el-form-item>
              <el-form-item label="商品名称：">
                <el-input v-model="dataList.exteral_material_name" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            </el-row>
            <div class="goods">
              <div class="goods-title">
                <el-form-item label="商品图：" required>
                </el-form-item>
              </div>
              <div class="goods-item">
                <el-form-item label="淘宝商品ID："  label-width="80">
                  <el-input v-model="dataList.external_material_no" size='mini' style="width: 200px;" disabled></el-input>
                </el-form-item>
              </div>
              <div class="goods-item">
                <el-form-item label="无价格商品图链接："  label-width="110">
                  <el-input v-model="dataList.external_material_pic" size='mini' style="width: 200px;" disabled></el-input>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
  import Vue from 'vue'
  import Fn from '@common/Fn.js';
  import headers from '@/components/header/header.vue';
  export default {
      name: "exerionDetail",
      props:["params"],
      data(){
        let self = this;
        return {
          dataList:{},
          goodsInfo:{
            goodId:"",//商品Id
            validTime:"",//生效时间
            invalidTime:"",//失效时间
          },
          pickerOptions: {
            disabledDate(time) {
              if (self.goodsInfo.validTime && self.goodsInfo.validTime >= Date.now()){
                return time.getTime() < self.goodsInfo.validTime + 8.64e7;
              } else {
                return time.getTime() < Date.now() - 8.64e7;
              }
            }
          },
          ifInvalid:false,
          ifValid:true,
          firstTab:"goodsInfo",
        }
      },
      methods:{
        getDetail(){
          this.ajax.postStream('/material-web/api/exteralMaterial/getExteralMaterialInfo',this.params.id,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content;
              this.goodsInfo.goodId = res.body.content.external_material_id;
              this.goodsInfo.validTime = res.body.content.enable_time;
              this.goodsInfo.invalidTime = res.body.content.disable_time;
              let nowDate = new Date();
              if (this.goodsInfo.invalidTime <= nowDate.getTime()){
                this.ifValid = false;
                this.ifInvalid = true;
              } else {
                this.ifValid = true;
                this.ifInvalid = false;
              }
              // this.goodsInfo.validTime = Fn.dateFormat(res.body.content.enable_time,'yyyy-MM-dd 00:00:00');
              // this.goodsInfo.invalidTime = Fn.dateFormat(res.body.content.disable_time,'yyyy-MM-dd 00:00:00');
              this.dataList.create_time = Fn.dateFormat(res.body.content.create_time,'yyyy-MM-dd hh:mm:ss');
              this.dataList.modify_time = Fn.dateFormat(res.body.content.modify_time,'yyyy-MM-dd hh:mm:ss');
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            Vue.prototype.tabNo[Vue.prototype.activeTab] = this.dataList.shop_code || '';
            console.log("Vue",Vue.prototype.tabNo);
          }, err => {
            this.$message.error(err);
            Vue.prototype.tabNo[Vue.prototype.activeTab] = '';
          });
        },
        save(){
        //   const result = /^\d+$/.test(this.goodsInfo.goodId);
        //   console.log("result",result);
          if (!this.goodsInfo.goodId || this.goodsInfo.goodId === ""){
            this.$message.error("商品ID不能为空！");
            return;
          }
        //   if (!result) {
        //     this.$message.error("商品ID格式不符，只能为数字！");
        //     return;
        //   }
          if (!this.goodsInfo.validTime || this.goodsInfo.validTime === ""){
            this.$message.error("生效时间不能为空！");
            return;
          }
          if (!this.goodsInfo.invalidTime || this.goodsInfo.invalidTime === ""){
            this.$message.error("失效时间不能为空！");
            return;
          }
          let nowDate = new Date();
          console.log("nowDate",nowDate);
          if (this.goodsInfo.invalidTime <= nowDate.getTime()){
            this.$message.error("失效时间必须大于当前时间！");
            return;
          }
          console.log(this.goodsInfo.validTime,this.goodsInfo.invalidTime);
          if (this.goodsInfo.invalidTime <= this.goodsInfo.validTime) {
            this.$message.error("失效时间必须大于生效时间！");
            return;
          }
          let data = {
            id:this.params.id,
            shop_id:this.dataList.shop_id,
            enable_time:this.goodsInfo.validTime,
            disable_time:this.goodsInfo.invalidTime,
          };
          this.ajax.postStream('/material-web/api/exteralMaterial/saveOrUpdateInfo',data,res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.getDetail();
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        }
      },
      mounted: function() {
        this.getDetail();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getDetail();
        });
      }
    }
</script>

<style scoped>
.goods{
  display: flex;
  align-items: center;
  height: 50px;
  margin-top: 20px;
}
.goods-title{
  width: 140px;
}
.goods-item{
  width: 320px;
}
</style>
