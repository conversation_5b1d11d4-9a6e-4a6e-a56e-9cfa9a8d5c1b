//火凤凰商品列表
<template>
  <xpt-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :searchPage='search.page_name'
    :selection="selection"
    :orderNo="true"
    :pageTotal='pageTotal'
    @selection-change='select'
    @search-click='searchClick'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
  >
    <xpt-import slot='btns' :taskUrl="uploadUrl" :callback="uploadCallback" class='mgl10'></xpt-import>
    <el-button type='primary' size='mini' @click="setting" slot='btns'>配置</el-button>
  </xpt-list>
</template>

<script>
    export default {
      name: "exerionList",
      data(){
        let self = this;
        return {
          dataList:[],
          selection:"checkbox",
          pageTotal:0,
          goodsInfo:{
            shopCode:"",
            goodId:"",
            validTime:"",
            invalidTime:"",
          },
          uploadUrl:"/material-web/api/exteralMaterial/excelImport",
          btns:[
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.getList();
              },
            },
            {
              type: "primary",
              txt: "新增",
              loading: false,
              click() {
                self.addExerion();
              },
            },
            {
              type: "danger",
              txt: "删除",
              loading: false,
              click() {
                self.deleteExerion();
              },
            },
            {
              type: "info",
              txt: "批量修改",
              loading: false,
              click() {
                self.batchUpdate();
              },
            },
          ],
          cols:[
            {
              label: "店铺编码",
              prop: "shop_code",
              width: 120,
              redirectClick(row) {
                let params = {};
                params.id = row.id;
                self.$root.eventHandle.$emit('creatTab', {
                  name: "火凤凰商品详情",
                  params: params,
                  component: () => import('@components/exerion_shop_list/exerionDetail.vue')
                });
              },
            },
            {
              label: "店铺名称",
              prop: "shop_name",
              width: 140
            },
            {
              label: "店铺地区",
              prop: "shop_area",
              format: 'auxFormat',
              formatParams: 'shopArea',
              width: 100
            },
            {
              label: "商品页面链接",
              prop: "external_material_id",
              width: 330
            },
            {
              label: "淘宝商品ID",
              prop: "external_material_no",
              width: 100
            },
            {
              label: "无价格商品图链接",
              prop: "external_material_pic",
              width: 120
            },
            {
              label: "商品图启用选择",
              prop: "external_material_switch",
              width: 120,
              formatter:(val)=>{
                return{
                  1:'淘宝商品ID',
                  2:'无价格商品图链接',
                }[val]||val
              }
            },
            {
              label: "商品名称",
              prop: "exteral_material_name",
              width: 330
            },
            {
              label: "生效时间",
              prop: "enable_time",
              width: 140,
              format: "dataFormat1"
            },
            {
              label: "失效时间",
              prop: "disable_time",
              width: 140,
              format: "dataFormat1"
            },
            {
              label: "创建人",
              prop: "create_name",
              width: 100
            },
            {
              label: "创建时间",
              prop: "create_time",
              width: 140,
              format: "dataFormat1"
            },
            {
              label: "更新时间",
              prop: "modify_time",
              width: 140,
              format: "dataFormat1"
            },
          ],
          search:{
            page_name:"cloud_shop_exteral_material",
            where:[],
            page_no: 1,
            page_size: 50,
          }
        }
      },
      methods:{
        getList(resolve){
          this.btns[0].loading = true;
          this.ajax.postStream('/material-web/api/exteralMaterial/list',this.search,res => {
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              this.pageTotal = res.body.content.count;
              this.selectRow = [];
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        //新增
        addExerion(){
          this.$root.eventHandle.$emit('alert', {
            params: {
              callback: data => {
                this.getList();
              },
            },
            component: ()=>import('@components/exerion_shop_list/addExerion'),
            style: 'width:720px;height:300px',
            title: '新增商品',
          })
        },
        //删除
        deleteExerion(){
          let ids=[];
          this.selectRow.find(d=>{
            ids.push(d.id);
          });
          if (!ids.length){
            this.$message.error('请选择要删除的数据！');return;
          }
          this.$confirm('当前操作会导致选中的火凤凰商品信息被删除，是否继续？','提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'danger'
          }).then(()=>{
            this.ajax.postStream('/material-web/api/exteralMaterial/batch_delete',{ids:ids},res => {
              if(res.body.result) {
                this.$message.success(res.body.msg);
                this.getList();
              } else {
                res.body.msg && this.$message.error(res.body.msg);
              }
            }, err => {
              this.$message.error(err);
            });
          }).catch(()=>{
            return false;
          });
        },
        //批量修改
        batchUpdate(){
          let shopCode=[],nowDate = new Date(),num=0;
          this.selectRow.find(d=>{
            shopCode.push(d.shop_code);
            if (d.disable_time <= nowDate.getTime()){
              num++;
            }
          });
          if (shopCode.length === 0){
            this.$message.error("当前未选择数据！");
            return;
          }
          console.log("num",num);
          if (shopCode.length > 0 && num === shopCode.length){
            this.$message.error("所选数据生效区间全部已过期，不能进行批量修改！");
            return;
          }
          let nary=shopCode.sort(),count = 0;
          for (let i=0; i< shopCode.length; i++){
            if(nary[i] === nary[i+1]){
              count++;
            }
          }
          if (count > 0){
            this.$message.error("不能同时对同一个店铺的多条数据进行批量修改！");
            return;
          }
          this.$root.eventHandle.$emit('alert', {
            title: "批量修改",
            params: {
              selectRow:this.selectRow,
              callback: data => {
                this.getList();
              },
            },
            style: 'width:400px;height:180px',
            component: () => import('@components/exerion_shop_list/exerionBatchUpdate1.vue')
          });
        },
        //导入execl文件后，从回调函数获取数据
        uploadCallback (data){
          console.log(123123,data.body.content);
          this.getList();
        },
        setting(){
          this.$root.eventHandle.$emit('alert', {
            title: "配置",
            params: {
              selectRow:this.selectRow,
              callback: () => {},
            },
            style: 'width:800px;height:500px',
            component: () => import('@components/exerion_shop_list/settingAlert.vue')
          });
        },
        searchClick (list, resolve){
          this.search.where = list;
          this.getList(resolve);
        },
        // 多选事件
        select(s){
          this.selectRow = s;
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page_size = pageSize;
          this.getList();
        },
        // 监听页数更改事件
        pageChange(page){
          this.search.page_no = page;
          this.getList();
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
