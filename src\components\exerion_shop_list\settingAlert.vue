<!--火凤凰配置弹框-->
<template>
    <div style="height: 100%">
        <div class="xpt-flex">
            <div class="xpt-top">
                <el-button type="primary" size="mini" @click="save" :loading="isLoading">保存</el-button>
                <el-button type="danger" size="mini" @click="clear">清空</el-button>
            </div>
            <div class="xpt-flex__bottom">
                <el-form :model="form" ref="form" :rules="rules">
                    <el-row :gutter="40">
                        <el-col :span="24">
                            <el-form-item label="使用说明：" label-width="80px" prop="remark" required>
                                <div class="remark-box">
                                    <wangEditor :baseConfig="config" divId='setting-remark' ref='settingRemark'
                                        class="wangEditor" @onChange="htmlChange"></wangEditor>
                                    <div class="remark-count">
                                        {{ remarkCount }}/200
                                    </div>
                                    <el-tooltip v-if='rules.remark[0].isShow' class="remark-box-tooltip" effect="dark"
                                        :content="rules.remark[0].message" placement="right-start"
                                        popper-class='xpt-form__error'>
                                        <i class='el-icon-warning'></i>
                                    </el-tooltip>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row class="mgt300" :gutter="40">
                        <el-col :span="24">
                            <el-form-item label="操作示例：" label-width="80px" prop="picture" required class="picture">
                                <xpt-upload-v3 uploadBtnText="上传附件" acceptType="img" :dataObj="uploadData"
                                    @uploadSuccess="uploadSuccess" :uploadSize="20" :limit="1"></xpt-upload-v3>
                                <el-tooltip v-if='rules.picture[0].isShow' class="item" effect="dark"
                                    :content="rules.picture[0].message" placement="right-start"
                                    popper-class='xpt-form__error'>
                                    <i class='el-icon-warning'></i>
                                </el-tooltip>
                                <div class="picture-box" v-if="form.picture" @click="showImage()">
                                    <img :src="form.picture" alt="图片" width="60" />
                                    <i class="el-icon-circle-cross" @click.stop="deletePicture()"></i>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <xpt-image :images="imagePreviewList" :show="ifShowImage" :ifUpload="false" :ifClose="false"
            @close="closeShowImage">
        </xpt-image>
    </div>
</template>
<script>
import validate from "@common/validate.js";
import wangEditor from '@components/wang_editor/wangEditor.vue';
import Fn from "@common/Fn.js";
import '@stylus/wangEditor.styl'
export default {
    props: ["params"],
    data() {
        let self = this;
        return {
            imageList: [],
            form: {
                remark: '',
                picture: '',
            },
            remarkCount: 0,
            rules: {
                remark: validate.isNotBlank({
                    self: self,
                    msg: "请输入使用说明",
                }),
                picture: validate.isNotBlank({
                    self: self,
                    msg: "请上传图片",
                }),
            },
            //附件上传路径标志
            uploadData: {
                parent_name: "EXTERAL_MATERIAL",
                parent_no: "EXTERAL_MATERIAL",
                child_name: null,
                child_no: null,
                content: {},
            },
            exteralMaterialInfo: {},
            exteralMaterialInfoItems: {},
            isLoading: false,
            config: {
                width: 600,
                height: 200, //高度
                zIndex: 2, //层级
                placeholder: "请在此码字", //文本为空时的提示
                focus: false, //聚焦
                menus: [
                    "head",
                    "bold",
                    "fontSize",
                    "fontName",
                    "italic",
                    "underline",
                    "strikeThrough",
                    "indent",
                    "lineHeight",
                    "foreColor",
                    "backColor",
                    "list",
                    "justify",
                    "splitLine",
                    "undo",
                    "redo",
                ], //菜单
            },
            ifShowImage: false,
            imagePreviewList: [],
        };
    },
    created() {
    },
    async mounted() {
        this.initData();
    },
    computed: {},
    methods: {
        async initData() {
            try {
                this.exteralMaterialInfo = await this.getExteralMaterialInfo();
                this.exteralMaterialInfoItems = await this.getExteralMaterialInfoItem();
                this.initForm();
            } catch (err) {
                console.log(err)
            }

        },
        initForm() {
            this.form.remark = this.exteralMaterialInfoItems.find(item => item.code === 'INSTRUCTIONS_TXT').remark||'';
            this.form.picture = this.exteralMaterialInfoItems.find(item => item.code === 'INSTRUCTIONS_PIC').remark||'';
            this.setWEContent(this.form.remark);
        },
        getExteralMaterialInfo() {
            let params = { "page": { "length": 50, "pageNo": 1 }, "where": [{ "field": "ebfbc7a9d21ea1d26868b6b331afd07e", "table": "c982ba0edaab6ffe3ebc4851a8c1e568", "value": "EXTERAL_MATERIAL", "operator": "=", "condition": "AND", "listWhere": [] }], "page_name": "cloud_auxiliary_category" }
            return new Promise((resolve, reject) => {
                this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryCategoryList', params, res => {
                    if (res.body.result) {
                        resolve(res.body.content.list[0])
                    } else {
                        reject(res.body.msg);
                    }
                })
            })
        },
        getExteralMaterialInfoItem() {
            return new Promise((resolve, reject) => {
                this.ajax.postStream('/user-web/api/auxiliary/getAuxiliaryDataList', { categoryCode: 'EXTERAL_MATERIAL' }, res => {
                    if (res.body.result) {
                        resolve(res.body.content.list)
                    } else {
                        reject(res.body.msg);
                    }
                })
            })
        },
        save() {
            if (this.remarkCount > 200) {
                this.rules.remark[0].isShow = true;
                this.rules.remark[0].message = '使用说明不能超过200字'
                return
            } else {
                this.rules.remark[0].message = '请输入使用说明'
            }
            this.form.remark = this.getWEContent();
            this.$refs['form'].validate((valid) => {
                if (!valid) return
                this.isLoading = true
                let remark = ''
                let dataList = this.exteralMaterialInfoItems.map(item => {
                    remark = item.code === 'INSTRUCTIONS_TXT' ? this.form.remark : this.form.picture;
                    return {
                        "categoryCode": item.categoryCode,
                        "code": item.code,
                        "disableTime": item.disableTime,
                        "enableTime": item.enableTime,
                        "id": item.id,
                        "name": item.name,
                        "parentCode": item.parentCode,
                        "platform": item.platform,
                        "status": item.status,
                        "remark": remark,
                        "ext_field1": item.ext_field1,
                        "ext_field2": item.ext_field2,
                        "tag": item.tag,
                        "parentCategoryCode": item.parentCategoryCode,
                    }
                })
                let params = {
                    "categoryPo": {
                        "code": this.exteralMaterialInfo.code,
                        "name": this.exteralMaterialInfo.name,
                        "parentCode": this.exteralMaterialInfo.parentCode,
                        "platform": "NEW_SALE_PLATFORM",
                        "remark": this.exteralMaterialInfo.remark,
                        "system": this.exteralMaterialInfo.system,
                        "id": this.exteralMaterialInfo.id
                    },
                    "dataList": dataList
                }
                this.ajax.postStream('/user-web/api/auxiliary/saveAuxiliaryCategory', params, res => {
                    if (res.body.result) {
                        this.isLoading = false
                        this.$message.success(res.body.msg || '保存成功')
                        this.params.callback && this.params.callback(this.form);
                        this.$root.eventHandle.$emit("removeAlert", this.params.alertId);
                    } else {
                        this.$message.error(res.body.msg || '保存失败')
                        this.isLoading = false
                    }
                })
            })
        },
        uploadSuccess(uploadFileList) {
            this.imageList = uploadFileList;
            this.form.picture = this.imageList[0].path;
            console.log("文件上传成功", uploadFileList);
        },
        getWEContent() {
            return this.$refs.settingRemark.getEditorHtml();
        },
        getEditorTxt() {
            return this.$refs.settingRemark.getEditorTxt();
        },
        setWEContent(data) {
            return this.$refs.settingRemark.setEditorHtml(data);
        },
        htmlChange() {
            this.remarkCount = this.getEditorTxt().length;
        },
        deletePicture() {
            this.form.picture = ""
            this.imagePreviewList = [];
            this.imageList = [];
        },
        clear() {
            this.form.remark = ""
            this.form.picture = ""
            this.setWEContent("");
            this.imagePreviewList = [];
            this.imageList = [];
        },
        showImage() {
            let item = this.exteralMaterialInfoItems.find(item => item.code === 'INSTRUCTIONS_PIC')
            let obj = {
                src: this.form.picture,
                date: Fn.dateFormat(
                    item.createTime || new Date().getTime(),
                    "yyyy-MM-dd hh:mm:ss"
                ),
                creatName: '火凤凰操作文字说明',
                name: '火凤凰操作文字说明',
                isPucture: true,
                active: true
            }
            this.imagePreviewList = [obj];
            this.ifShowImage = true;
        },
        closeShowImage() {
            this.ifShowImage = false;
        },
    },
    components: {
        wangEditor
    }
};
</script>
<style scope>
.el-form {
    white-space: nowrap;
}

.mgt300 {
    margin-top: 300px;
}

.seeFileList {
    position: absolute;
    bottom: 0;
    left: 10px;
    right: 10px;
    top: 0px;
    width: auto;
    min-width: auto;
    padding: 10px 0px;
}

.el-table .el-table__body-wrapper td .cell {
    height: auto;
}

.el-table__empty-block {
    width: auto !important;
}

.showImg {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #dbdbdb;
    margin: 5px 0;
    line-height: 100%;
}

#xpt-image .xpt-image__body {
    width: 100%;
    height: 100%;
}

.select-intervener {
    width: 180px !important;
}

.picture .el-form-item__content {
    display: flex;
    align-items: center;
}

.picture .el-tooltip {
    margin-left: 10px;
}

.remark-box {
    position: relative;
}

.remark-box-tooltip {
    position: absolute;
    bottom: 0;
    right: 20px;
}

.remark-count {
    position: absolute;
    bottom: 2px;
    right: 60px;
    color: #bfcbd9;
    z-index: 3;
}

.wangEditor {
    white-space: normal;
}

.picture-box {
    position: relative;
    margin-left: 10px;
}

.el-icon-circle-cross {
    position: absolute;
    top: 0;
    left: 55px;
}
</style>