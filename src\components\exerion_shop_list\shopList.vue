<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='primary' size='mini' @click='confirm' slot='left'>确认</el-button>
      <el-input
        placeholder="输入店铺名称进行搜索"
        icon="search"
        size='mini'
        v-model="search.key"
        :on-icon-click="searchClick"
        slot='right'
        @keyup.enter.native="searchClick"
      ></el-input>
    </xpt-headbar>
    <xpt-list
      :data="dataList"
      :colData="cols"
      :showHead="false"
      :pageTotal='pageTotal'
      :selection="selection"
      @page-size-change='pageChange'
      @current-page-change='currentPageChange'
      @radio-change='select'
    ></xpt-list>
  </div>
</template>

<script>
    export default {
      name: "shopList",
      props:["params"],
      data(){
        return {
          selection:"radio",
          dataList:[],
          shopInfo:{
            shop_id:"",
            shop_code:"",
          },
          pageTotal:0,
          pageSize:50,
          pageNo:1,
          cols:[
            {
              label: "店铺编码",
              prop: "shop_code",
            },
            {
              label: "店铺名称",
              prop: "shop_name",
            },
            {
              label: "店铺地区",
              format: 'auxFormat',
              prop: "shop_area",
              formatParams: 'shopArea',
            },
            {
              label: '店铺分类',
              prop: 'shop_type',
              format: 'auxFormat',
              formatParams: 'shopClassify'
            },
          ],
          search: {
            key:"",
          },
        }
      },
      methods:{
        getList(){
          let params = {
            shop_name: this.search.key ? this.search.key : "",
            page:{
              length:this.pageSize,
              pageNo:this.pageNo,
            }
          };
          console.log("value:%s",this.search.key);
          this.ajax.postStream('/material-web/api/shopv2/externalMaterialShopList', params, res=> {
            if(res.body.result){
              this.dataList = res.body.content.list || [];
              this.pageTotal = res.body.content.count;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        confirm(){
          //确认选择
          console.log("shopInfo:%s",this.shopInfo.shop_id);
          if(this.shopInfo.shop_id === "" || !this.shopInfo.shop_id){
            this.$message({
              message:'请选择店铺！',
              type:'error'
            });
            return;
          }
          //关闭弹窗
          this.params.callback(this.shopInfo);
          this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
        },
        searchClick() {
          this.getList();
        },
        // 选择事件
        select(s){
          this.shopInfo.shop_code = s.shop_code;
          this.shopInfo.shop_id = s.shop_id;
        },
        // 监听每页显示数更改事件
        pageChange(pageSize){
          this.pageSize = pageSize;
          this.getList();
        },
        // 监听页数更改事件
        currentPageChange(page){
          this.pageNo = page;
          this.getList();
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
