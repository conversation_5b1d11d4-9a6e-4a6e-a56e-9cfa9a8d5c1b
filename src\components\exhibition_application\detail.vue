<!--  -->
<template>
  <div class='xpt-flex'>
    <el-row class="xpt-top">
      <el-button type="primary" size="mini" @click="refresh">刷新</el-button>
      <el-button type="primary" size="mini" @click="add">新增</el-button>
      <el-button type="primary" size="mini" @click="addData" :disabled="info.status==='APPROVED' || info.status==='SUBMITTED' || info.status==='CANCELED'">增行</el-button>
      <el-button type="primary" size="mini" @click="delData" :disabled="info.status==='APPROVED' || info.status==='SUBMITTED' || info.status==='CANCELED'">删行</el-button>
      <el-button type="primary" size="mini" @click="update" :loading="loading" v-if="this.params.exhibit_apply_id" :disabled="info.status==='APPROVED' || info.status==='SUBMITTED' || info.status==='CANCELED'">保存</el-button>
      <el-button type="primary" size="mini" @click="save" :loading="loading" v-else :disabled="info.status==='APPROVED' || info.status==='SUBMITTED' || info.status==='CANCELED'">保存</el-button>
      <el-button type="primary" size="mini" @click="submit" :disabled="info.status==='APPROVED' || info.status==='SUBMITTED' || info.status==='CANCELED'">提交</el-button>
      <el-button type="primary" size="mini" @click="reject" :disabled="info.status!=='SUBMITTED'">驳回</el-button>
      <el-button type="primary" size="mini" @click="revoke" :disabled="info.status!=='SUBMITTED'">撤销</el-button>
      <el-button type="primary" size="mini" @click="auditing" :disabled="info.status!=='SUBMITTED'">审核</el-button>
      <el-button type="primary" size="mini" @click="efficacy" :disabled="info.status!=='APPROVED'">失效</el-button>
      <el-button type="primary" size="mini" @click="createOrder" :disabled="info.status!=='APPROVED'">生成订单</el-button>
<!--      <el-button type="primary" size="mini" @click="exportExcel" :disabled="info.status!=='APPROVED' && info.status !=='CANCELED'">导出</el-button>-->
<!--      <el-button type="primary" size="mini" @click="importResult" :disabled="info.status==='SUBMITTED' && info.status !=='CANCELED'">导出文件下载</el-button>-->
      <el-button type='success' size='mini' @click="fileUpload" :disabled="!(/^(RETRIAL|CREATE|WITHDRAWED)$/.test(info.status))" >
          附件上传
          <xpt-upload-v2 isOnlyPic :ifClickUpload="ifClickUploadB" :dataObj="uploadDataB"></xpt-upload-v2>
      </el-button>
      <el-button type='primary' size='mini' @click="fileSee" :disabled="info.status == ''">附件查看</el-button>
		</el-row>
    <el-row :gutter='40' class="info">
      <el-col :span='6'>
        <el-form label-position="right" label-width="110px">
          <el-form-item label='单据号：'>{{info.apply_no}}</el-form-item>
          <el-form-item label='单据状态：'>{{statusFormat(info.status)}}</el-form-item>
        </el-form>
      </el-col>
      <el-col :span='6'>
        <el-form label-position='right' label-width='110px'>
          <el-form-item label='申请人：'>{{info.create_name}}</el-form-item>
          <el-form-item label='申请时间：'>{{dateFormat(info.create_time)}}</el-form-item>
        </el-form>
      </el-col>
      <el-col :span='6'>
        <el-form label-position='right' label-width='110px'>
          <el-form-item label='审核人：'>{{info.audit_name}}</el-form-item>
          <el-form-item label='审核时间：'>{{dateFormat(info.audit_time)}}</el-form-item>
        </el-form>
      </el-col>
      <el-col :span='6'>
        <el-form label-position='right' label-width='110px'>
          <el-form-item label='店铺名称：'>
            <xpt-input
              v-if="!info.status "
              v-model='info.shop_name'
              readonly
              icon='search'
              :on-icon-click='openShop'
              size='mini'>
            </xpt-input>
             <span v-else>
               {{shop_name}}
             </span>
          </el-form-item>
          <!-- <el-form-item label='订单折扣：'>
            <xpt-input
              v-model='info.exhibition_discount'
              :disabled="info.status != 'SUBMITTED'"
              type="number" placeholder="请输入0.00~2.00"
              size='mini'>
            </xpt-input>
          </el-form-item> -->
        </el-form>
      </el-col>
    </el-row>
    <div class='xpt-flex__bottom scroll'>
<!--      <el-tabs v-model="activeName">-->
<!--        <el-tab-pane label="关联子物料" name="first" class="xpt-flex">-->
          <xpt-headbar v-if="!isCreate">
            <xpt-search-ex :page="search.page_name" :click="searchData" slot='right' style="margin-right: 50px"/>
          </xpt-headbar>
          <xpt-list
            ref="query"
            :showHead='false'
            @search-click='searchData'
            :data='list'
            :colData='cols'
            :pageTotal='count'
            @selection-change='select'
            @row-click='rowClick'
            selection='checkbox'
            @page-size-change='pageSizeChange'
            @current-page-change='currentPageChange'>
            <template slot='sample_number' slot-scope='scope'>
              <p v-if="info.status==='APPROVED' || info.status==='SUBMITTED' || info.status==='CANCELED'"
              >{{scope.row.material_no}}</p>
              <el-input
                size='mini'
                icon='search'
                :readonly='true'
                :on-icon-click='selectGoods'
                v-model='scope.row.material_no'
                style='width: 100%'>
              </el-input>
            </template>
            <template slot='goods_quantity' slot-scope='scope' >
              <el-input
                :maxlength="3"
                style="width:100px"
                :disabled="true"
                v-model="scope.row.goods_quantity"
                size='mini'
                @keyup.native="scope.row.goods_quantity=scope.row.goods_quantity.replace(/^(0[1 ])|[^\d]+/g,'')">
              </el-input>
            </template>
            <template slot='area_name' slot-scope='scope' >
              <p v-if="info.status==='APPROVED' || info.status==='SUBMITTED' || info.status==='CANCELED' || !info.shop_name"
                 >{{scope.row.area_name}}</p>
              <el-input
                v-else
                size='mini'
                icon='search'
                :readonly='true'
                :on-icon-click='selectShopArea'
                v-model='scope.row.area_name'
                style='width: 100%'>
              </el-input>
            </template>
          </xpt-list>
<!--        </el-tab-pane>-->
<!--      </el-tabs>-->
    </div>
  </div>
</template>

<script>
  //这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
  //例如：import 《组件名称》 from '《组件路径》';
import Fn from '@common/Fn.js'
  import detailComponent from '@components/exhibition_application/detail.vue';
  export default {
  props:['params'],
//import引入的组件需要注入到对象中才能使用
    components: {},
    data() {
    let self = this;
//这里存放数据
      return {
        ifClickUploadB: false,
        uploadDataB: null,
        list: [],
        info: {
          shop_name: '',
          exhibition_discount: 1,
          status: ''
        },
        count: 0,
        search: {
          page_name: 'scm_exhibition_application_material',
          where: [],
          page: {
            length: this.pageSize,
            pageNo: 1
          }
        },
        submitStatus: false, // 是否提交中状态
        isOperational: false, // 新增还是查看状态
        selectData:{},//单行表数据
        selectRow: [],
        cols: [
          {
            label: '物料编码',
            slot: 'sample_number',
          },
          {
            label: '物料名称',
            prop: 'material_name',
          },
          {
            label: '物料规格',
            prop: 'material_specification',
          },
          {
            label: '商品数量',
            slot: 'goods_quantity',
            width: 120,
          },
          {
            label: '摆场区域',
            slot: 'area_name',
          },
          {
            label: '区域编码',
            prop: 'shop_area',
          },
          {
            label: '区域位置',
            prop: 'area_position',

          },
          {
            label: '行状态',
            width: 120,
            prop: 'apply_status',
            formatter: function formatter(val) {
              switch (val) {
                case 'CREATE':
                  return '创建';
                  break;
                case 'SUBMITTED':
                  return '已提交';
                  break;
                case 'APPROVED':
                  return '已审核';
                  break;
                case 'RETRIAL':
                  return '已驳回';
                  break;
                case 'CANCELED':
                  return '已失效';
                  break;
                case 'WITHDRAWED':
                  return '已撤销';
                  break
                default:
                  return val;
              }
            }
          },
          {
            label: '是否下单',
            width: 80,
            prop: 'if_order',
            formatter(val) {
              if(val == 'N') {
                return '否'
              }
              if(val == 'Y') {
                return '是'
              }
              return val;
            }
          }
        ],
        deleteMaterialVOs:[], //删除列表
        shop_name:'',
        shop_id: '',
        isCreate: false, // 是否是新增页面
        loading: false // 保存longding状态控制
      };
    },
//监听属性 类似于data概念
  computed: {
    hasShop() {
      if (this.info.shop_name) {
        return true
      } else {
        return false
      }
    }
  },
//监控data中的数据变化
  watch: {
    'info.shop_name' (newVal, old) {
      // let list2 = this.list;
      if (newVal!== this.shop_name) {
        this.list = []
      }
      // else {
      //   this.list = list2
      // }
    }
  },
//方法集合
    methods: {
      // 附件上传
      fileUpload (type) {
        this.ifClickUploadB = true
        this.uploadDataB = {
          parent_name:'ORDER',
          parent_no: this.info.apply_no,
          child_name: 'exhibition',
          child_no:'',
          content:{'style':'清样登记附件'}
        }
        setTimeout(() => {
          this.ifClickUploadB = false
        },100)
      },
      // 查看附件
      fileSee () {
        let self = this
        let params = {
          parent_no : this.info.apply_no,
          child_no : null,
          ext_data : null,
          parent_name :'ORDER',
          child_name : 'exhibition',
          parent_name_txt :'清样登记',
          child_name_txt : null,
        }
        params.ifClose = this.info.status == 'CREATE'
        params.callback = d=>{
          let i = d.length;
          while(i--) {
            let json = d[i].group_value_json;
            json = JSON.parse(json).content || {};
            console.log('console.log', json)
            if(json instanceof Array) {
              json = json[0];
            }
          }
        }
        this.$root.eventHandle.$emit('alert',{
          params:params,
          component:()=>import('@components/common/download.vue'),
          style:'width:1000px;height:600px',
          title:'下载列表'
        })
      },
      reset() {
        for (let v in this.query) {
          if (v !== 'page') {
            this.query[v] = '';
          }
        }
        this.query.if_need_page = "Y";
        this.query.page_no = 1;
        this.query.page_size = 50;
        console.log("query:%s", this.query.if_need_page);
      },
      pageSizeChange(ps) {
        console.log(ps)
        this.search.page.length = ps;
        this.getList();
      },
      currentPageChange(page) {
        this.search.page.pageNo = page;
        this.getList();
      },
      // 获取列表
      getList(val,resolve) {
        this.$request('/order-web/api/exhibitionapplication/list', {
            exhibit_apply_id: val? val: this.params.exhibit_apply_id,
          ...this.search
          },
        ).then(res =>{
          this.$message({
            type: res.result ? 'success' : 'error',
            message: res.msg || ''
          });
          if (res.result) {
            this.list = res.content.list || [];
            this.count = res.content.count;
          }
          resolve && resolve();
        }).catch(err=>{
          resolve && resolve();
        }).finally(()=>{
          resolve && resolve();
          this.loading = false;
        });
      },
      // 选择店铺
      openShop() {
        let self = this;
        this.$root.eventHandle.$emit('alert', {
          params: {
            callback: (d) => {
              self.info.shop_name = d.shop_name;
              self.shop_id = d.shop_id;
            },
            selection: 'radio'
          },
          component: () => import('@components/shop/list'),
          style: 'width:800px;height:500px',
          title: '店铺列表',
        })
      },
      refresh() {
        if (this.params.exhibit_apply_id) {
          this.getList();
        }
        this.getDataHead();
      },
      rowClick(row) {
        console.log('row',row);
        this.selectData = row;
      },
      // 选择物料
      selectGoods(){
        let self = this;
        let params = {};
        params.callback = d => {
          if(d) {
            self.selectData.material_no = d.materialNumber;
            self.selectData.material_name = d.materialName;
            self.selectData.material_specification = d.materialSpecification
            // self.list[self.list.length-1] = self.selectData
          }
        };
        this.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/order/selectGoodsList'),
          style: 'width:800px;height:500px',
          title: '商品列表'
        })
        console.log(self.selectData);
      },
      // 获取表头
      getDataHead(val) {
        if (val) {
          this.$request('/order-web/api/exhibitionapplication/get', {
              exhibit_apply_id: val
            },
          ).then(res =>{
            if (res.result) {
              this.info = res.content;
              this.shop_name= res.content.shop_name
            }
          }).catch(err=>{
          });
        } else {
          this.$request('/order-web/api/exhibitionapplication/get', {
              exhibit_apply_id: this.params.exhibit_apply_id
            },
          ).then(res =>{

            if (res.result) {
              this.info = res.content;
              this.shop_name= res.content.shop_name
            }
          }).catch(err=>{
          });
        }
      },
      // 导出excel
      exportExcel() {
        let params = {
          exhibit_apply_id: this.params.exhibit_apply_id,
          page_name: "scm_exhibition_application_material",
        };
        this.$axios('post',
          '/order-web/api/exhibitionapplication/export?permissionCode=EXHIBITION_APPLICATION_EXPORT',
          params
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
        })
      },
      //導出結果下載
      importResult(data,row,callback) {
        if(this.isEdit) return;
        new Promise((resolve) => {
          setTimeout(resolve, 10)

        }).then(() => {
          let params = {
              excel_type:'EXCEL_TYPE_SCM_EXHIBITION_APPLICATION_EXPORT',
              url:'/price-web/api/price/import/list',
              isInput: false
            },
            self = this;
          self.$root.eventHandle.$emit('alert', {
            params: params,
            component: () => import('@components/shop_regional_distribu/import_result.vue'),
            style: 'width:800px;height:500px',
            title: '导出结果'
          });

        })
      },
      // 选择摆场区域
      selectShopArea() {
        let self = this;
        this.$root.eventHandle.$emit('alert', {
            params: {
              callback: (d) => {
                self.info.shop_name = d.shop_name;
                self.selectData.area_name = d.area_name;
                self.selectData.shop_area = d.shop_area;
                self.selectData.area_position = d.area_position;
              },
              shop_name: self.info.shop_name
            },
          component: () => import('@components/shop_regional_distribu/selectShopDistribution'),
          style: 'width:800px;height:500px',
          title: '店铺区域分布列表'
        })
      },
      // 新增
      add() {
        this.$root.eventHandle.$emit('creatTab', {
          name: "新增摆场申请",
          params: {},
          component:()=>import('@components/exhibition_application/detail')
        });
      },
      save() {
        if (!this.info.shop_name) {
          this.$message.error('必须先选择店铺才能保存')
          return
        } else if (this.list.length<1) {
          this.$message.error('必须至少有一行物料明细行数据')
          return
        }
        this.loading = true;
        let self =this;
        this.$axios(
          'post',
          '/order-web/api/exhibitionapplication/save?permissionCode=EXHIBITION_APPLICATION_ADD',
          {
            shop_name: this.info.shop_name,
            deleteMaterialVOs: this.deleteMaterialVOs,
            materialVOs: this.list
          }
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            let params = {};
            params.__close = function(){
              self.$root.eventHandle.$emit('removeTab',self.params.tabName);
            }
            params.exhibit_apply_id = res.content;
            this.$root.eventHandle.$emit('updateTab',{
              name:this.params.tabName,
              params: params,
              title:'摆场申请订单详情',
              component: () => import('@components/exhibition_application/detail.vue'),
            })
            this.getDataHead(res.content);
            this.getList(res.content)
          } else {
            this.$message.error(res.msg)
            this.loading = false
          }
        }).catch(err => {
          this.$message.error(err)
          this.loading = false
        }).finally(()=>{
        })
      },
      // 编辑
      update() {
        this.loading = true
        this.$axios(
          'post',
          '/order-web/api/exhibitionapplication/save?permissionCode=EXHIBITION_APPLICATION_ADD',
          {
            exhibit_apply_id: this.params.exhibit_apply_id,
            shop_name: this.info.shop_name,
            materialVOs: this.list,
            deleteMaterialVOs: this.deleteMaterialVOs
          }
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.getDataHead(res.content);
            this.getList(res.content);
            this.deleteMaterialVOs = [];
            this.params.exhibit_apply_id = res.content
          } else {
            this.$message.error(res.msg)
            this.loading = false
          }
        }).catch(err => {
          this.$message.error(err)
          this.loading = false
        }).finally(()=>{
        })
      },
      // 提交
      submit() {
        this.$axios(
          'post',
          '/order-web/api/exhibitionapplication/submit?permissionCode=EXHIBITION_APPLICATION_SUBMIT',
          {
            exhibit_apply_id: this.params.exhibit_apply_id,
            shop_name: this.info.shop_name,
            deleteMaterialVOs: this.deleteMaterialVOs,
            materialVOs: this.list,
            apply_no: this.info.apply_no
          }
        ).then(res => {
          let self = this;
          if (res.result) {
            this.$message.success(res.msg);
            if (!this.params.exhibit_apply_id) {
              let params = {};
              params.__close = function(){
                self.$root.eventHandle.$emit('removeTab',self.params.tabName);
              }
              params.exhibit_apply_id = res.content;
              this.$root.eventHandle.$emit('updateTab',{
                name:this.params.tabName,
                params: params,
                title:'摆场申请订单详情',
                component: () => import('@components/exhibition_application/detail.vue'),
              })
            }
            this.getDataHead(res.content);
            this.getList(res.content);
            this.deleteMaterialVOs = [];

          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        });
        },
        createOrder() {
          this.$axios('post',
          '/order-web/api/exhibitionapplication/generateSysTrade',
          {
            exhibitApplyNO: this.info.apply_no
          }
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg)
            let params = {};
            params.merge_trade_id = res.content.mergeTradeId;
            params.mergeTradeIdList = []
            params.ifOrder = false
            // 呼叫按钮来源
            params.callBtnfromType = 'sys_order'
            this.$root.eventHandle.$emit('creatTab',{
            name:"合并订单详情",
            params:params,
            component: () => import('@components/order/merge.vue')
            });
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      efficacy() {
        this.$axios('post',
          '/order-web/api/exhibitionapplication/cancel?permissionCode=EXHIBITION_APPLICATION_CANCEL',
          {
            exhibit_apply_id: this.params.exhibit_apply_id
          }
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg)
            this.getDataHead(res.content);
            this.getList(res.content)
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      //驳回
      reject() {
        this.$axios('post',
          '/order-web/api/exhibitionapplication/reject?permissionCode=EXHIBITION_APPLICATION_REJECT',
          {
            exhibit_apply_id: this.params.exhibit_apply_id
          }
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg)
            this.getDataHead(res.content);
            this.getList(res.content)
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      // 撤销
      revoke() {
        this.$axios(
          'post',
          '/order-web/api/exhibitionapplication/revoke?permissionCode=EXHIBITION_APPLICATION_REVOKE',
          {
            exhibit_apply_id: this.params.exhibit_apply_id
          }
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg)
            this.getDataHead(res.content);
            this.getList(res.content)
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      //失效
      effect() {
        this.$axios(
          'post',
          '/order-web/api/exhibitionapplication/cancel?permissionCode=EXHIBITION_APPLICATION_CANCEL',
          {
            exhibit_apply_id: this.params.exhibit_apply_id
          }
        ).then(res => {
          if (res.result) {
            this.getDataHead(res.content);
            this.getList(res.content)
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      // 审核
      auditing() {
        let num = this.info.exhibition_discount;
        // if (!num && this.info.status == 'SUBMITTED') {
        //   this.$message.error('请填写订单折扣');
        //   return;
        // }
        // if(!(num !='' && (num<2 ||num>0) && this.info.status == 'SUBMITTED' && /^(0|2|1|(0\.\d{0,2})|(1\.\d{0,2}))$/.test(num))){
        //   this.$message.error('订单折扣需输入0.00~2.00');
        //   return;
        // }
        this.$axios('post',
          '/order-web/api/exhibitionapplication/audit?permissionCode=EXHIBITION_APPLICATION_AUDIT',
          {
            exhibit_apply_id: this.params.exhibit_apply_id,
            exhibition_discount: 1
          }
        ).then(res => {
          if (res.result) {
            this.getDataHead(res.content);
            this.getList(res.content);
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      // 日期转换
      dateFormat(val) {
        return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss');
      },
      // 状态转换
      statusFormat(val) {
        switch (val) {
          case 'CREATE':
            return '创建';
            break;
          case 'SUBMITTED':
            return '已提交';
            break;
          case 'APPROVED':
            return '已审核';
            break;
          case 'RETRIAL':
            return '已驳回';
            break;
          case 'CANCELED':
            return '已失效';
            break;
          case 'WITHDRAWED':
            return '已撤销';
            break;
          default:
            return val;
        }
      },
      searchData(obj, resolve) {
        this.search.where=obj;
        this.getList(null,resolve)
      },
      /**
       *新增行
       */
      addData(){
        if (!this.info.shop_name) {
          this.$message.info('请先选择店铺')
          return false;
        }
        let data = this.getDetailData();
        // data.shop_id = this.shop.shop_id;
        this.list.push(data);
      },
      // 多选列表
      select(s){
        this.selectRow = s;
      },
      /**
       *删除行
       */
      delData(){
        this.selectRow.forEach(item =>{
          this.deleteMaterialVOs.push(item.exhibit_detail_id)
          removeAaary(this.list, item)
        });
        function removeAaary (_arr, _obj) {
          let length = _arr.length;
          if (!length) return [];
          for (var i = 0; i < length; i++) {
            if (_arr[i] === _obj) {
              if (i === 0) {
                _arr.shift(); //删除并返回数组的第一个元素
                return _arr;
              } else if (i === length - 1) {
                _arr.pop();  //删除并返回数组的最后一个元素
                return _arr;
              } else {
                _arr.splice(i, 1); //删除下标为i的元素
                return _arr;
              }
            }
          }
        }

        // let delData = JSON.parse(JSON.stringify(this.selectRow));
        // this.list.forEach((item, index) =>{
        //   for (let i = 0; i < delData.length; i++) {
        //     if (delData[i].apply_no === item.apply_no && delData[i].shop_area === item.shop_area && delData[i].goods_quantity === item.goods_quantity) {
        //       this.list.splice(index, 1);
        //       delData.splice(i, 1)
        //     }
        //   }
        // });
        // this.update()
      },
      /**
       *新增明细行的数据
       */
      getDetailData(){
        return {
          material_no: '',
          material_name: '',
          material_specification: '',
          goods_quantity: 1,
          shop_area: '',
          area_name: '',
          area_position: '',
          apply_status: '',
          if_order: ''
        }
      },
    },
//生命周期 - 创建完成（可以访问当前this实例）
    created() {
      this.getDataHead();
      if (this.params.exhibit_apply_id) {
        this.getList();
        this.shop_name = this.params.shop_name
        this.isCreate = false;
      } else {
        this.isCreate = true;
      }
    },
//生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {

    },
    beforeCreate() {
    }, //生命周期 - 创建之前
    beforeMount() {
    }, //生命周期 - 挂载之前
    beforeUpdate() {
    }, //生命周期 - 更新之前
    updated() {
    }, //生命周期 - 更新之后
    beforeDestroy() {
    }, //生命周期 - 销毁之前
    destroyed() {
    }, //生命周期 - 销毁完成
    activated() {
    }, //如果页面有keep-alive缓存功能，这个函数会触发
  }
</script>
<style>
  .info {
    height: 12%;
  }
  .xpt-flex__bottom /deep/ .xpt-flex {
    height: 95%!important;
  }
  .xpt-flex__bottom /deep/ .el-table {
    height: 95%!important;
  }
</style>
