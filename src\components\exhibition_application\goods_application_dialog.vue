<template>
  <xpt-list
    ref='table'
    :data='list'
    :btns='btns'
    :colData='cols'
    :pageTotal='totalPage'
    selection='checkbox'
    searchPage='scm_exhibition_application_material'
    searchHolder='请输入查询条件'
    @search-click='searchData'
    @selection-change='selectChange'
    @page-size-change='pageSizeChange'
    @current-page-change='pageChange'
  >
  </xpt-list>
</template>

<script>
  export default {
    props:['params'],
    name: 'list',
    components: {},
    data() {
      let self = this;
      return {
        btns: [
          {
            type:'primary',
            txt:'确认',
            click(){
              self.close();
            }
          }
        ],
        cols: [
          {
            label: '物料编码',
            prop: 'material_no',
          },
          {
            label: '物料名称',
            prop: 'material_name',
          },
          {
            label: '物料规格',
            prop: 'material_specification',
          },
          {
            label: '商品数量',
            prop: 'goods_quantity',
            width: 70,
          },
          {
            label: '摆场区域',
            prop: 'area_name',
          },
          {
            label: '区域编码',
            prop: 'shop_area',
          },
          {
            label: '区域位置',
            prop: 'area_position',

          },
          {
            label: '行状态',
            prop: 'apply_status',
            formatter: function formatter(val) {
              switch (val) {
                case 'CREATE':
                  return '创建';
                  break;
                case 'SUBMITTED':
                  return '已提交';
                  break;
                case 'APPROVED':
                  return '已审核';
                  break;
                case 'RETRIAL':
                  return '已驳回';
                  break;
                case 'CANCELED':
                  return '已失效';
                  break;
                case 'WITHDRAWED':
                  return '已撤销';
                  break
                default:
                  return val;
              }
            }
          },
          {
            label: '是否下单',
            prop: 'if_order',
            formatter(val) {
              if(val == 'N') {
                return '否'
              }
              if(val == 'Y') {
                return '是'
              }
              return val;
            }
          }
        ],

        list: [],
        search:{
          page_name: "scm_exhibition_application_material",
          where: [],
          page: {
            length: this.totalPage,
            pageNo: 1
          }
        },
        totalPage: 50,
        selectId:[],
        uploadUrl: '/order-web/api/scmexhibitionmaterial/import'
      }

    },
    watch: {},
    methods: {
      selectChange(data) {
        this.selectData = data;
      },
      close() {
        const bool = false;
        if (!this.selectData) {
          this.$message.error('请先选择一个物料');
          return;
        }
        if (this.params.fromWhere==='dealerShgStock') {
          this.isSmallGoods()
        } else {
          this.params.callback(this.selectData);
          this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
        }
      },
      searchData(obj, resolve) {
        this.search.where = obj;
        this.selectData = null;
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page.length = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page.pageNo = page;
        this.selectData = null;
        this.getList();
      },
      // 检验是否是小商品
      isSmallGoods() {
        let selectCheckBox = []
        this.selectData.forEach(d=>{
          selectCheckBox.push(d.material_no)
        });
        this.$axios(
          'post',
          '/order-web/api/extensionmergetrade/isXSP',
          selectCheckBox
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.params.callback(this.selectData);
            this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error('服务器错误')
        }).finally(final=>{
          selectCheckBox = []
        })
      },
      getList(resolve) {
        console.log(this.params);
        this.$request('/order-web/api/extensionmergetrade/getScmExhibitionApplicationMaterials',
          {
            ...this.search,
            shop_name: this.params.shop_name,
          }).then(res => {
          if (res.result) {
            this.list = res.content.list || [];
            this.totalPage = res.content.count;
          }
          resolve && resolve();
        }).catch(err => {
          resolve && resolve();
        }).finally(() => {
          resolve && resolve();
        })
      }
    },
    computed: {},
    created() {
      this.getList();
      console.log(this.params,'paramsparamsparamsparamsparamsparamsparams');
    },
    mounted() {
    },
    destroyed() {
    }
  }
</script>

<style scoped>
</style>
