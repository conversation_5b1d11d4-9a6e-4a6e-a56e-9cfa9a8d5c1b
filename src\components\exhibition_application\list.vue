<!-- 摆场商品申请列表 -->
<template>
	<div class='xpt-flex'>
<!--		<xpt-headbar>-->
<!--      <el-button type='success' size='mini' @click="getList()" slot='left' :disabled='refreshBtnStatus' :loading='refreshBtnStatus'>刷新</el-button>-->
<!--			<el-button type='info'  size='mini' @click="addNew" slot='left'>新增</el-button>-->
<!--			<xpt-export :data="list" :taskData="taskData" :getListUrl="getListUrl" :otherInfo="otherInfo"  :tabText="tabText" slot='left'></xpt-export>-->
<!--		</xpt-headbar>-->
		<xpt-list
			:data='list'
			:btns='btns'
			:colData='cols'
			:searchPage='search.page_name'
			:pageTotal='pageTotal'
			selection='checkbox'
      orderNo
      searchPage='scm_exhibition_application'
      @search-click='searchData'
			@radio-change='radioChange'
			@selection-change='selectionChange'
			@page-size-change='pageSizeChange'
			@current-page-change='pageChange'
			@row-dblclick='rowDblclick'
		>
      <xpt-import slot='btns' :taskUrl='uploadUrl' class='mgl10'></xpt-import>
    </xpt-list>
	</div>
</template>
<script>
export default {
	data(){
		let self = this;
		return {
			btns: [
        {
          type: 'primary',
          txt: '刷新',
          click() {
            self.getList()
          },
          loading: false
        },
        {
          type: 'info',
          txt: '新增',
          click: self.addNew,
          loading: false
        },
        {
          type: 'primary',
          txt: '导出',
          click: self.exportExcel,
          loading: false
        },
        {
          type: 'primary',
          txt: '导出文件下载',
          click: self.outputResult,
          loading: false
        },
        {
          type: 'primary',
          txt: '导入结果',
          click: self.importResult,
          loading: false
        }
      ],
			cols: [
				{
					label: '申请单据号',
					prop: 'apply_no',
					redirectClick(row) {
						self.addNew(row)
					}
				},
        {
					label: '物料编码',
					prop: 'material_no'
				},
        {
					label: '物料名称',
					prop: 'material_name',
				},
        {
					label: '物料规格',
					prop: 'material_specification',
				},
        {
					label: '店铺名称',
					prop: 'shop_name',
					formatParams: 'shopArea'
				},
        {
					label: '商品数量',
					prop: 'goods_quantity',
          width: 80
        },
        {
					label: '摆场区域',
					prop: 'shop_area'
				},
        {
					label: '区域名称',
					prop: 'area_name'
				},
        {
					label: '区域位置',
					prop: 'area_position'
				},
        {
					label: '单据状态',
					prop: 'status',
          formatter:function formatter(val) {
            switch (val) {
              case 'CREATE':
                return '创建';
                break;
              case 'SUBMITTED':
                return '已提交';
                break;
              case 'APPROVED':
                return '已审核';
                break;
              case 'RETRIAL':
                return '已驳回';
                break;
              case 'CANCELED':
                return '已失效';
                break;
              case 'WITHDRAWED':
                return '已撤销';
                break;
              default:
                return val;
            }
          }
				},
        {
					label: '是否下单',
					prop: 'if_order',
          formatter:function formatter(val) {
            switch (val) {
              case 'N':
                return '否';break;
              case 'Y':
                return '是';break;
              default:
                return ''
            }
          }
				},
        {
          label: '申请人',
          prop: 'create_name',
        },
        {
					label: '申请时间',
					prop: 'create_time',
					format: 'dataFormat1',
					width: 130
				},
        {
					label: '审核人',
					prop: 'audit_name'
				},
        {
					label: '审核时间',
					prop: 'audit_time',
					format: 'dataFormat1',
					width: 130
				}
			],
			search:{
				page_name: 'scm_exhibition_application',
				where: [],
        page: {
          length: this.pageSize,
          pageNo: 1
        }
			},
			list:[],
			selectData:'',
			pageTotal:0,
      pageSize: 50,
			/*
			导出参数
			*/
			taskData:{
				url:'/material-web/api/shopv2/exportExcel',
				//查询组件里面的查询条件
				data:{}
			},
			getListUrl:'/material-web/api/shopv2/export/list?permissionCode=SHOP_EXPORT',//导出结果列表
			otherInfo:{
				excel_type:'EXCEL_TYPE_SHOPV2'
			},
			tabText:'店铺导出列表',
			refreshBtnStatus: false,
      uploadUrl: '/order-web/api/exhibitionapplication/importJob?permissionCode=EXHIBITION_APPLICATION_IMPORT'
		}
	},

	props:['params'],
	methods:{
		radioChange(data){
			this.selectData = data;
		},
		selectionChange(data) {
			this.selectData = data;
		},
		close(){
			if(!this.selectData){
				this.$message.error('请先选择一个店铺');
				return;
			}
			this.params.callback(this.selectData);
			this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
		},
		searchData(obj, resolve){
			this.search.where = obj;
			this.selectData = null;
			this.getList(resolve);
		},
		pageSizeChange(pageSize){
			this.search.page.length = pageSize;
			this.selectData = null;
			this.getList();
		},
		pageChange(page){
			this.search.page.pageNo = page;
			this.selectData = null;
			this.getList();
		},
    getList(resolve){
      this.$request('/order-web/api/exhibitionapplication/selectAll?permissionCode=EXHIBITION_APPLICATION_QUERY',
        {
          ...this.search,
        }).then(res =>{
        this.$message({
          type: res.result ? 'success' : 'error',
          message: res.msg || ''
        });
        if (res.result) {
          this.list = res.content.list || [];
          this.pageTotal = res.content.count;
        }
        resolve && resolve();
      }).catch(err=>{
        resolve && resolve();
      }).finally(()=>{
        resolve && resolve();
      })
		},
		rowDblclick(obj) {
			if(!this.params.isAlert) return;
			let data = obj;
			if(this.selection === 'checkbox') {
				this.selectData = this.selectData.length?this.selectData:[];
				this.selectData.push(data);
				this.selectData = Array.from(new Set(this.selectData));
				//data = [obj];
			}
			//this.selectData = data;

			this.close();
		},
    //导入结果
    importResult(data,row,callback) {
      if(this.isEdit) return;
      new Promise((resolve) => {
        setTimeout(resolve, 10)

      }).then(() => {
        let params = {
            excel_type:'EXCEL_TYPE_ARRANGING_IMPORT',
            url:'/price-web/api/price/import/list',
            isInput: true
          },
          self = this;
        self.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/shop_regional_distribu/import_result.vue'),
          style: 'width:800px;height:500px',
          title: '导入结果'
        });

      })
    },
    // 导出excel
    exportExcel() {
      let params = {
        where: this.search.where,
        page_name: "scm_exhibition_application",
      };
      this.$axios('post',
        '/order-web/api/exhibitionapplication/export?permissionCode=EXHIBITION_APPLICATION_EXPORT',
        params
      ).then(res => {
        if (res.result) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
      }).catch(err => {
      })
    },
    //導出結果下載
    outputResult(data,row,callback) {
      new Promise((resolve) => {
        setTimeout(resolve, 10)

      }).then(() => {
        let params = {
            excel_type:'EXCEL_TYPE_SCM_EXHIBITION_APPLICATION_EXPORT',
            url:'/price-web/api/price/import/list',
            isInput: false
          },
          self = this;
        self.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/shop_regional_distribu/import_result.vue'),
          style: 'width:800px;height:500px',
          title: '导出结果'
        });

      })
    },
		/**
		*新增、编辑店铺
		*/
		addNew(obj){
      console.log(obj, 'ssss!!!!!!!!!!!!!!!!');
      this.$root.eventHandle.$emit('creatTab', {
				name: obj.exhibit_apply_id ? '摆场商品申请单详情' : '新增摆场申请',
				params: obj.exhibit_apply_id ? obj : {},
				component:()=>import('@components/exhibition_application/detail')
			});
		},
	},
  created() {
    this.getList();
  },
	mounted() {
	}
}
</script>
