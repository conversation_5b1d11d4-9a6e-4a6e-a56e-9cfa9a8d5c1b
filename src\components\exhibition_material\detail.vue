<!--  -->
<template>
  <div class='xpt-flex'>
    <el-row class="xpt-top">
       <el-button type="primary" size="mini" @click="refresh">刷新</el-button>
<!--      <el-button type="primary" size="mini" @click="update" v-if="this.params.exhibit_apply_id" :disabled="info.status==='APPROVED' || info.status==='SUBMITTED' || info.status==='CANCELED'">保存</el-button>-->
      <el-button type="primary" size="mini" :disabled="!saveDisabled" @click="save">保存</el-button>
    </el-row>
    <el-row>
      <el-tabs v-model="activeInfo">
        <el-tab-pane label="基本信息" name="1" class="xpt-flex">
          <el-row :gutter='40'>
            <el-form :model='form' label-position="right" label-width="110px">
              <el-col :span='8'>
                <el-form-item label='商品编号：'>
                  <el-input size='mini' v-model='form.exhibit_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='物料编码：'>
                  <el-input size='mini' v-model='form.material_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='摆场区域：'>
                  <el-input
                    size='mini'
                    icon='search'
                    :readonly='true'
                    :on-icon-click='selectShopArea'
                    :disabled="!saveDisabled"
                    v-model='form.shop_area'
                    style='width: 180px'>
                  </el-input>
                </el-form-item>
                <el-form-item label='商品数量：'>
                  <el-input size='mini' v-model='form.goods_quantity' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='状态：'>
                  <el-input size='mini' v-model='status' :disabled="isEdit"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label='商品子编号：'>
                  <el-input size='mini' v-model='form.exhibit_child_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='物料名称：'>
                  <el-input size='mini' v-model='form.material_name' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='区域名称：'>
                  <el-input size='mini' v-model='form.area_name' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='父级物料编码：'>
                  <el-input size='mini' v-model='form.group_material_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='更新人：'>
                  <el-input size='mini' v-model='form.modify_name' :disabled="isEdit"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label='店铺名称：'>
                  <el-input size='mini' v-model='form.shop_name' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='物料规格：'>
                  <el-input size='mini' v-model='form.material_specification' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='区域位置：'>
                  <el-input size='mini' v-model='form.area_position' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='父级物料名称：'>
                  <el-input size='mini' v-model='form.group_material_name' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='更新时间：'>
                  <el-input size='mini' v-model='form.modify_time' :disabled="isEdit"></el-input>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="其他信息" name="2" class="xpt-flex">
          <el-row :gutter='40'>
            <el-form :model='form' label-position="right" label-width="110px">
              <el-col :span='8'>
                <el-form-item label='摆场申请单据号：'>
                  <el-input size='mini' v-model='form.exhibit_apply_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='摆场拍单时间：'>
                  <el-input size='mini' v-model='form.exhibit_order_created' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='到店摆场时间：'>
                  <el-input size='mini' v-model='form.shop_exhibite_time' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='清样销售单号：'>
                  <el-input size='mini' v-model='form.sample_order_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='清样出库时间：'>
                  <el-input size='mini' v-model='form.sample_order_stocked  ' :disabled="isEdit"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label='摆场申请时间：'>
                  <el-input size='mini' v-model='form.exhibit_apply_time' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='摆场批次单号：'>
                  <el-input size='mini' v-model='form.exhibit_batch_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='清样登记单据号：'>
                  <el-input size='mini' v-model='form.sample_register_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='清样拍单时间：'>
                  <el-input size='mini' v-model='form.sample_order_created' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='清样实际售价：'>
                  <el-input size='mini' v-model='form.sample_actual_price' :disabled="isEdit"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label='摆场销售单号：'>
                  <el-input size='mini' v-model='form.exhibit_order_no' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='摆场出库时间：'>
                  <el-input size='mini' v-model='form.exhibit_order_stocked' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='清样登记时间：'>
                  <el-input size='mini' v-model='form.sample_record_time' :disabled="isEdit"></el-input>
                </el-form-item>
                <el-form-item label='清样批次单号：'>
                  <el-input size='mini' v-model='form.sample_batch_no' :disabled="isEdit"></el-input>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <div class='xpt-flex__bottom' v-fold>
      <el-tabs v-model="activeName">
        <el-tab-pane label="关联子物料" name="first" class="xpt-flex">
          <xpt-list
            ref="query"
            :showHead='false'
            orderNo
            @radio-change="rowClick"
            isNeedClickEvent
            @row-click="rowClick"
            :data='list'
            :colData='cols'
            :pageTotal='count'
            selection='radio'
            @page-size-change='pageSizeChange'
            @current-page-change='currentPageChange'>
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
  //这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
  //例如：import 《组件名称》 from '《组件路径》';
  import Fn from '@common/Fn.js'
  export default {
    props:['params'],
//import引入的组件需要注入到对象中才能使用
    components: {},
    data() {
      let self = this;
//这里存放数据
      return {
        list: [],
        form: {},
        isEdit: true,
        status: '',
        count: 0,
        search: {
          page_name: 'scm_exhibition_application_material',
          where: [],
          page: {
            length: this.pageSize,
            pageNo: 1
          }
        },
        selectData:{},//单行表数据
        activeName: 'first',
        cols: [
          {
            label: '商品子编号',
            prop: 'exhibit_child_no',
          },
          {
            label: '物料编码',
            prop: 'material_no',
          },
          {
            label: '物料名称',
            prop: 'material_name',
          },
          {
            label: '物料规格',
            prop: 'material_specification',
            width: 150
          },
          {
            label: '状态',
            prop: 'status',
            formatter: function formatter(val) {
              switch (val) {
                case 'DELIVERED':
                  return '已发货';
                  break;
                case 'EXHIBITING':
                  return '摆场中';
                  break;
                case 'SAMPLING':
                  return '清样中';
                  break;
                case 'SAMPLED':
                  return '已清样';
                  break;
                default:
                  return val;
              }
            }
          },
          {
            label: '摆场申请时间',
            prop: 'exhibit_apply_time',
            format: 'dataFormat1',
          },
          {
            label: '摆场拍单时间',
            prop: 'exhibit_order_created',
            format: 'dataFormat1',

          },
          {
            label: '摆场出库时间',
            prop: 'exhibit_order_stocked',
            format: 'dataFormat1',

          },
          {
            label: '到店摆场时间',
            prop: 'shop_exhibite_time',
            format: 'dataFormat1',
          },
          {
            label: '清样登记时间',
            prop: 'sample_record_time',
            format: 'dataFormat1',
          },
          {
            label: '清样拍单时间',
            prop: 'sample_order_created',
            format: 'dataFormat1',
          },
          {
            label: '清样出库时间',
            prop: 'sample_order_stocked',
            format: 'dataFormat1',
          }
        ],
        activeInfo: '1',
        saveDisabled: false
      };
    },
//监听属性 类似于data概念
    computed: {},
//监控data中的数据变化
    watch: {
      'form.status' (newVal, old) {
        this.saveDisabled = (newVal === 'SAMPLING' || newVal === 'EXHIBITING');
      }
    },
//方法集合
    methods: {
      reset() {
        for (let v in this.query) {
          if (v !== 'page') {
            this.query[v] = '';
          }
        }
        this.query.if_need_page = "Y";
        this.query.page_no = 1;
        this.query.page_size = 50;
        console.log("query:%s", this.query.if_need_page);
      },
      pageSizeChange(ps) {
        this.search.page.length = ps;
        this.getList();
      },
      currentPageChange(page) {
        this.search.page.pageNo = page;
        this.getList();
      },
      // 获取列表
      getList(val,resolve) {
        this.$request('/order-web/api/scmexhibitionmaterial/getExhibit', {
          exhibit_no: val? val.exhibit_no : this.params.exhibit_no,
         exhibit_child_no:val? val.exhibit_child_no : this.params.exhibit_child_no,
            ...this.search
          },
        ).then(res =>{
          if (res.result) {
            this.form = res.content.list[0] || [];
            this.list = res.content.list || [];
            this.initForm();
            this.count = res.content.count;
          }
          this.$message({
            type: res.result ? 'success' : 'error',
            message: res.msg || ''
          });
          resolve && resolve();
        }).catch(err=>{
          resolve && resolve();
        }).finally(()=>{
          resolve && resolve();
        });
      },
      refresh() {
        this.getList();
      },
      rowClick(row) {
        this.selectData = row;
        this.form=row;
        this.initForm();
      },
      handleCurrentChange(val,index) {
        console.log(val, index,'val,indexval,indexval,index');
        this.$refs.query.setCurrentRow(row);

      },
      initForm() {
        this.form.exhibit_order_created = this.dateFormat(this.form.exhibit_order_created);
        this.form.shop_exhibite_time = this.dateFormat(this.form.shop_exhibite_time);
        this.form.modify_time =  this.dateFormat(this.form.modify_time);
        this.form.sample_order_stocked = this.dateFormat(this.form.sample_order_stocked);
        this.form.exhibit_apply_time = this.dateFormat(this.form.exhibit_apply_time);
        this.form.sample_order_created = this.dateFormat(this.form.sample_order_created);
        this.form.exhibit_order_stocked = this.dateFormat(this.form.exhibit_order_stocked );
        this.form.sample_record_time = this.dateFormat(this.form.sample_record_time);
        this.status = this.statusFormat(this.form.status);
      },
      // 选择摆场区域
      selectShopArea() {
        if (!this.saveDisabled) {
          return
        }
        let self = this;
        this.$root.eventHandle.$emit('alert', {
          params: {
            callback: (d) => {
              self.form.shop_area = d.shop_area;
              self.form.area_name = d.area_name;
              self.form.area_position = d.area_position;
            },
            shop_name: self.form.shop_name
          },
          component: () => import('@components/shop_regional_distribu/selectShopDistribution'),
          style: 'width:800px;height:500px',
          title: '店铺区域分布列表'
        })
      },
      save() {
        let params = JSON.parse(JSON.stringify(this.form));
        params.exhibit_order_created = new Date(this.params.exhibit_order_created).valueOf()||this.params.exhibit_order_created;
        params.shop_exhibite_time = new Date(this.params.shop_exhibite_time).valueOf()||this.params.shop_exhibite_time;
        params.modify_time =  new Date().valueOf();
        params.sample_order_stocked = new Date(this.params.sample_order_stocked).valueOf()||this.params.sample_order_stocked;
        params.exhibit_apply_time = new Date(this.params.exhibit_apply_time).valueOf()||this.params.exhibit_apply_time;
        params.sample_order_created = new Date(this.params.sample_order_created).valueOf()||this.params.sample_order_created;
        params.exhibit_order_stocked = new Date(this.params.exhibit_order_stocked ).valueOf()||this.params.exhibit_order_stocked;
        params.sample_record_time = new Date(this.params.sample_record_time).valueOf()||this.params.sample_record_time;
        this.$axios(
          'post',
          '/order-web/api/scmexhibitionmaterial/save?permissionCode=EXHIBITION_MATERIAL_SAVE',
          params
        ).then(res => {
          if (res.result) {
            this.$message.success(res.msg);
            this.form = res.content.head;
            this.list = res.content.list;
            this.initForm();
          } else {
            this.$message.error(res.msg)
          }
        }).catch(err => {
          this.$message.error(err)
        })
      },
      // 日期转换
      dateFormat(val) {
        return Fn.dateFormat(val,'yyyy-MM-dd hh:mm:ss');
      },
      // 状态转换
      statusFormat(val) {
        switch (val) {
          case 'DELIVERED':
            return '已发货';
            break;
          case 'EXHIBITING':
            return '摆场中';
            break;
          case 'SAMPLING':
            return '清样中';
            break;
          case 'SAMPLED':
            return '已清样';
            break;
          default:
            return val;
        }
      }
    },
//生命周期 - 创建完成（可以访问当前this实例）
    created() {
      if (this.params.exhibit_no) {
        this.getList();
      }
    },
//生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {

    },
    beforeCreate() {
    }, //生命周期 - 创建之前
    beforeMount() {
    }, //生命周期 - 挂载之前
    beforeUpdate() {
    }, //生命周期 - 更新之前
    updated() {
    }, //生命周期 - 更新之后
    beforeDestroy() {
    }, //生命周期 - 销毁之前
    destroyed() {
    }, //生命周期 - 销毁完成
    activated() {
    }, //如果页面有keep-alive缓存功能，这个函数会触发
  }
</script>
<style>
  .info {
    height: 25%;
  }
  .info .el-col {
    text-align: right;
  }
  .select-row {
    background-color: #c9e5f5 !important;
  }


</style>
