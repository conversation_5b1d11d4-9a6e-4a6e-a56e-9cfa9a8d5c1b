<template>
  <div class='xpt-flex'>
    <xpt-list
      :data='list'
      :btns='btns'
      :colData='cols'
      :searchPage='search.page_name'
      :pageTotal='pageTotal'
      selection='checkbox'
      orderNo
      searchPage='scm_exhibition_material'
      @search-click='searchData'
      @selection-change='selectionChange'
      @page-size-change='pageSizeChange'
      @current-page-change='pageChange'
    >
<!--      <xpt-import slot='btns' :taskUrl='uploadUrl' :isupload="false" class='mgl10'></xpt-import>-->
      <el-button
        type="success"
        size='mini'
        class='image-upload'
        :loading="isUp"
        slot='btns'>导入
        <input
          type="file"
          id='files'
          multiple="multiple"
          @change='changeFile'>
      </el-button>
    </xpt-list>
<!--    <xpt-list-->
<!--      ref='table'-->
<!--      :data='list'-->
<!--      :btns='btns'-->
<!--      :colData='cols'-->
<!--      :pageTotal='pageSize'-->
<!--      selection='checkbox'-->
<!--      searchPage='scm_exhibition_material'-->
<!--      searchHolder='请输入查询条件'-->
<!--      @search-click='searchData'-->
<!--      @selection-change='selectionChange'-->
<!--      @page-size-change='pageSizeChange'-->
<!--      @current-page-change='pageChange'>-->
<!--      <xpt-import slot='btns' :taskUrl='uploadUrl' :callback="uploadCallback" :sysTradeId="1" class='mgl10' :isupload="false"></xpt-import>-->
<!--    </xpt-list>-->
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  name: 'list',
  components: {},
  data() {
      let self = this;
      return {
        btns: [
          {
            type:'primary',
            txt:'刷新',
            click(){
              self.getList();
            }
          },
          {
            type: 'primary',
            txt: '清样登记',
            disabled:true,
            click: self.sampleRegister
          },
          {
            type: 'success',
            txt: '摆场',
            click: self.batchShow,
            loading: false
          },
          {
            type: 'info',
            txt: '导出',
            // disabled: ifShop ? false : true,
            click: self.exportExcel,
          },
          {
            type: 'primary',
            txt: '导出文件下载',
            click: self.importResult,
            loading: false
          },
          {
            type: 'success',
            txt: '导入结果',
            click: self.leadResult,
            loading: false
          }
        ],
        cols: [
          {
            label: '商品编号',
            prop: 'exhibit_no',
            width: 120,
          },
          {
            label: '商品子编号',
            prop: 'exhibit_child_no',
            width: 140,
            redirectClick(row) {
              self.add(row)
            }
          },
          {
            label: '物料编码',
            prop: 'material_no'
          },
          {
            label: '物料名称',
            prop: 'material_name'
          },
          {
            label: '物料规格',
            prop: 'material_specification'
          },
          {
            label: '店铺名称',
            prop: 'shop_name',
            width: 80
          },
          {
            label: '商品数量',
            prop: 'goods_quantity',
            width: 40
          },
          {
            label: '摆场区域',
            prop: 'shop_area',
            width: 60
          },
          {
            label: '区域名称',
            prop: 'area_name',
            width: 60
          },
          {
            label: '区域位置',
            prop: 'area_position',
            width: 60
          },
          {
            label: '状态',
            prop: 'status',
            width: 40,
            formatter: function formatter(val) {
              switch (val) {
                case 'DELIVERED':
                  return '已发货';
                  break;
                case 'EXHIBITING':
                  return '摆场中';
                  break;
                case 'SAMPLING':
                  return '清样中';
                  break;
                case 'SAMPLED':
                  return '已清样';
                  break;
                default:
                  return val;
              }
            }
          },
          {
            label: '父级物料编码',
            prop: 'group_material_no',
            width: 120,
          },
          {
            label: '父级物料名称',
            prop: 'group_material_name',
            width: 110,
          },
          {
            label: '到店摆场时间',
            prop: 'shop_exhibite_time',
            width: 110,
            format: 'dataFormat1'
          },
          {
            label: '摆场申请单据号',
            prop: 'exhibit_apply_no',
            width: 110
          },
          {
            label: '摆场申请时间',
            prop: 'exhibit_apply_time',
            width: 110,
            format: 'dataFormat1'
          },
          {
            label: '摆场销售单号',
            prop: 'exhibit_order_no',
            width: 110
          },
          {
            label: '摆场拍单时间',
            prop: 'exhibit_order_created',
            width: 100,
            format: 'dataFormat1'
          },
          {
            label: '摆场批次单号',
            prop: 'exhibit_batch_no',
            width: 100
          },
          {
            label: '摆场出库时间',
            prop: 'exhibit_order_stocked',
            width: 100,
            format: 'dataFormat1'
          },
          {
            label: '清样登记单据号',
            prop: 'sample_register_no',
            width: 100
          },
          {
            label: '清样登记时间',
            prop: 'sample_record_time',
            format: 'dataFormat1',
            width: 100
          },
          {
            label: '清样销售单号',
            prop: 'sample_order_no',
            width: 100
          },
          {
            label: '清样拍单时间',
            prop: 'sample_order_created',
            format: 'dataFormat1',
            width: 100
          },
          {
            label: '清样实际售价',
            prop: 'sample_actual_price',
            width: 100
          },
          {
            label: '清样批次单号',
            prop: 'sample_batch_no',
            width: 100
          },
          {
            label: '清样出库时间',
            prop: 'sample_order_stocked',
            format: 'dataFormat1',
            width: 100
          },
          {
            label: '更新人',
            prop: 'modify_name',
          },
          {
            label: '更新时间',
            prop: 'modify_time',
            format: 'dataFormat1'
          },
        ],
        list: [],
        search:{
          page_name: "scm_exhibition_material",
          where: [],
          page: {
            length: this.pageSize,
            pageNo: 1
          }
        },
        isUp: false, // upload按钮控制
        pageTotal:0,
        pageSize: 50,
        selectData: [],
        selectId:[],
        uploadUrl: '/order-web/api/scmexhibitionmaterial/import?permissionCode=EXHIBITION_MATERIAL_IMPORT'
      }

  },
  props: {},
  watch: {},
  methods: {
    selectionChange(data) {
      this.selectData = data;
    },
    close() {
      if (!this.selectData) {
        this.$message.error('请先选择一个店铺');
        return;
      }
      this.params.callback(this.selectData);
      this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
    },
    searchData(obj, resolve) {
      this.search.where = obj;
      this.selectData = null;
      this.getList(resolve);
    },
    pageSizeChange(pageSize) {
      this.search.page.length = pageSize;
      this.selectData = null;
      this.getList();
    },
    pageChange(page) {
      this.search.page.pageNo = page;
      this.selectData = null;
      this.getList();
    },
    getList(resolve) {
      this.$request('/order-web/api/scmexhibitionmaterial/refresh?permissionCode=EXHIBITION_MATERIAL_QUERY',
        {
          ...this.search
        }).then(res => {
        if (res.result) {
          this.list = res.content.list || [];
          this.pageTotal = res.content.count;
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        resolve && resolve();
      }).catch(err => {
        resolve && resolve();
      }).finally(() => {
        resolve && resolve();
      })
    },
    // 导出
    exportExcel() {
      let temp = []
      let params = {
        export_ids: temp,
        page_name: this.search.page_name,
        where: this.search.where
      };
      this.$axios('post',
        '/order-web/api/scmexhibitionmaterial/exportExcel?permissionCode=EXHIBITION_MATERIAL_EXPORT',
        params
      ).then(res => {
        if (res.result) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
      }).catch(err => {
      })
    },
    // 导入结果callback
    uploadCallback(data) {
      // console.log(data,'导入！！！！！！！！！！！！！！！！！！');
      if (data.body.result === true) {
        this.$message.success(data.body.content)
        this.importDisabled = false;
      }
    },
    changeFile() {
      let files = document.getElementById('files').files
      let len = files.length,
      fr = new FileReader(),
      self = this;
      fr.readAsDataURL(files[0])
      fr.onload = e => {
        self.upload(files,0)
      }
    },
    // 提交至服务器
    upload(files,index) {
      this.isUp=true;
      let formData = new FormData();
      formData.append('file', files[index]);
      this.ajax.post('/app-web/app/file/uploadFile.do', formData, (s) => {
        if(s.body.success && s.body.data) {
          //let bodyContent = s.body.content;
          this.ajax.postStream1('/order-web/api/scmexhibitionmaterial/import?permissionCode=EXHIBITION_MATERIAL_IMPORT',
            s.body.data,
            d => {
            this.$message({
              type: d.body.result ? 'success' : 'error',
              message: d.body.content || '导入失败'
            })
            this.isUp = false;
          }, err => {
            this.$message.error(String(err));
            this.isUp = false;
          })
        } else {
          this.$message.error(s.body.content || '');
          this.isUp = false;
        }
      }, e => {
        this.$message.error(e);
        this.isUp = false;
      })
      document.getElementById('files').value= null
    },
    //导入结果
    leadResult(data,row,callback) {
      if(this.isEdit) return;
      new Promise((resolve) => {
        setTimeout(resolve, 10)

      }).then(() => {
        let params = {
            excel_type:'EXCEL_TYPE_SCM_EXHIBITION_MATERIAL_EXCEL_IMPORT',
            url:'/price-web/api/price/import/list',
            isInput: true
          },
          self = this;
        self.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/shop_regional_distribu/import_result.vue'),
          style: 'width:800px;height:500px',
          title: '导入结果'
        });

      })
    },
    //導出結果下載
    importResult(data,row,callback) {
      if(this.isEdit) return;
      new Promise((resolve) => {
        setTimeout(resolve, 10)

      }).then(() => {
        let params = {
            excel_type:'EXCEL_TYPE_SCM_EXHIBITION_MATERIAL_EXPORT',
            url:'/price-web/api/price/import/list',
            isInput: false
          },
          self = this;
        self.$root.eventHandle.$emit('alert', {
          params: params,
          component: () => import('@components/shop_regional_distribu/import_result.vue'),
          style: 'width:800px;height:500px',
          title: '导出结果'
        });

      })
    },
    add(obj) {
      this.$root.eventHandle.$emit('creatTab', {
        name: obj.exhibit_no ? '摆场商品信息详情' : '新增摆场商品信息单',
        params: obj.exhibit_no ? obj : {},
        component:()=>import('@components/exhibition_material/detail')
      });
    },
    batchShow() {
      this.$root.eventHandle.$emit('alert', {
        params: {
          callback: (d) => {
            // self.info.shop_name = d.shop_name;
            // self.selectData.area_name = d.area_name;
            // self.selectData.shop_area = d.shop_area;
            // self.selectData.area_position = d.area_position;
          },
        },
        component: () => import('@components/exhibition_material/shopBatch'),
        style: 'width:800px;height:500px',
        title: '到店批次选择'
      });
    },
    // 清样登记跳转
    sampleRegister() {
      let canIn = true, self = this
      let temp = [];
      if (this.selectData.length===0) {
        this.$message.info('必须选择一条数据');
        return
      }
      this.selectData.forEach(item=>{
        if (item.status !== 'EXHIBITING') {
          temp.push(item.exhibit_no);
          canIn = false;
        }
      });
      if (canIn) {
        let postData = [];
        this.selectData.forEach(item=>{
          postData.push(item.id)
        });
        this.$request('/order-web/api/scmexhibitionmaterial/toSampleRegister', { export_ids: postData }).then(res => {
          if (res.result) {
            if (res.content.sampleId) {
              let tabList = this.$parent.$parent.$parent.openTabs, activeTab = {}, activeIdx = ''
              tabList.forEach((tab, idx) => {
                if (tab && tab.title == '清样详情' && tab.params.sample_id == res.content.sampleId) {
                  activeTab = JSON.parse(JSON.stringify(tab))
                  activeIdx = idx
                }
              })
              if (JSON.stringify(activeTab) === '{}') {
                let params={sample_id:res.content.sampleId, sampleRegisterList: res.content.list}
                this.$root.eventHandle.$emit('creatTab',{
                  name:'清样详情',params:params,component: () => import('@components/domesticSales/detail.vue')
                })
              } else {
                this.$parent.$parent.$parent.openTabs[activeIdx].params.tabName = activeTab.params.tabName
                this.$parent.$parent.$parent.$refs.headers.activeTab = activeTab.params.tabName 
                this.$parent.$parent.$parent.activeTab = activeTab.params.tabName 
                Vue.prototype.activeTab = activeTab.params.tabName;
                let paramsData = JSON.parse(JSON.stringify(activeTab));
                let params = paramsData.params
                params.__close = function(){
                  self.$root.eventHandle.$emit('removeTab',activeTab.params.tabName);
                }
                params.sampleRegisterList = res.content.list;// 为了能顺利组装数据tab
                params.sample_id = res.content.sampleId // 为了能顺利激活tab
                this.$root.eventHandle.$emit('updateTab',{
                  name:activeTab.params.tabName,
                  params: params,
                  title:'清样详情',
                  component: () => import('@components/domesticSales/detail.vue'),
                })
              }
            } else {
              let params = {
                list : self.selectData,
                fromWhere: 'exhibition_material'
              };
              self.$root.eventHandle.$emit('creatTab', {
                name: '新增清样商品登记表',
                params: params,
                component:()=>import('@components/domesticSales/detail')
             });
           }
         } else {
           self.$message.error(res.msg)
         }
        })
      } else {
        this.$message.info('商品编号为'+temp+'的状态不是摆场中，只有摆场中的数据才能清样登记');
      }
    },
    // 导出excel
    // exportExcel() {
    //   let params = {
    //     id: this.params.exhibit_apply_id,
    //     page_name: "scm_exhibition_application_material",
    //   }
    //   this.$axios('post',
    //     '/order-web/api/exhibitionapplication/export',
    //     params
    //   ).then(res => {
    //     console.log(res);
    //   }).catch(err => {
    //     this.$message.error(err)
    //   })
    // }
  },
  computed: {},
  created() {
    this.getList();
  },
  mounted() {
  },
  destroyed() {
  }
}
</script>

<style lang="stylus" scoped>
  .image-upload
    position: relative
    cursor: pointer
    overflow: hidden
    display: inline-block
    *display: inline
    *zoom: 1
  input
    position: absolute
    font-size: 100px
    right: 0
    top: 0
    height:100%
    opacity: 0
    cursor: pointer
</style>
