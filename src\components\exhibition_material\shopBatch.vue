<template>
  <xpt-list
    ref='table'
    orderNo
    :data='list'
    :btns='btns'
    :colData='cols'
    :pageTotal='totalPage'
    @radio-change='radioChange'
    @selection-change='selectChange'
    @page-size-change='pageSizeChange'
    @current-page-change='pageChange'
  >
  </xpt-list>
</template>

<script>
  export default {
    props:['params'],
    name: 'list',
    components: {},
    data() {
      let self = this;
      return {
        btns: [
          {
            type:'primary',
            txt:'确认',
            click(){
              self.close();
            }
          }
        ],
        cols: [
          {
            label: '店铺名称',
            prop: 'shop_name',
          },
          {
            label: '销售单号',
            prop: 'exhibit_order_no',
          },
          {
            label: '拍单时间',
            prop: 'exhibit_order_created',
            format: 'dataFormat1',
          },
          {
            label: '批次单号',
            prop: 'exhibit_batch_no'
          },
          {
            label: '出库时间',
            prop: 'exhibit_order_stocked',
            format: 'dataFormat1',

          }
        ],
        list: [],
        search:{
          page_name: "scm_exhibition_material",
          where: [],
          page: {
            length: 10,
            pageNo: 1
          }
        },
        totalPage: 0,
        selectId:[],
        uploadUrl: '/order-web/api/scmexhibitionmaterial/import'
      }

    },
    watch: {},
    methods: {
      radioChange(data) {
        this.selectData = data;
      },
      close() {
        if (!this.selectData) {
          this.$message.error('请先选择一个批次');
          return;
        }
        // this.params.callback(this.selectData);
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
        this.$root.eventHandle.$emit('creatTab', {
          name: '门店摆场',
          params: {list:this.selectData},
          component:()=>import('@components/exhibition_material/shopExhibitionList')
        });
      },
      selectChange(data){
        this.selectData = data;
      },
      searchData(obj, resolve) {
        this.search.where = obj;
        this.selectData = null;
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page.length = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page.pageNo = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve) {
        console.log(this.params);
        this.$request('/order-web/api/scmexhibitionmaterial/batchShow?permissionCode=EXHIBITION_MATERIAL_FIELD',
          {
            ...this.search,
          }).then(res => {
          if (res.result) {
            this.list = res.content.list || [];
            this.totalPage = res.content.count;
          }
          resolve && resolve();
        }).catch(err => {
          resolve && resolve();
        }).finally(() => {
          resolve && resolve();
        })
      }
    },
    computed: {},
    created() {
      this.getList();
    },
    mounted() {
    },
    destroyed() {
    }
  }
</script>

<style scoped>
</style>
