<template>
  <xpt-list
    ref='table'
    :data='list'
    :btns='btns'
    :colData='cols'
    :pageTotal='pageSize'
    orderNo
    selection='checkbox'
    searchPage='scm_exhibition_material'
    searchHolder='请输入查询条件'
    @search-click='searchData'
    @row-click='rowClick'
    @selection-change='selectionChange'
    @page-size-change='pageSizeChange'
    @current-page-change='pageChange'
  >
    <template slot='shop_area' slot-scope='scope' >
      <el-input
        size='mini'
        icon='search'
        :readonly='true'
        :on-icon-click='selectShopArea'
        v-model='scope.row.shop_area'
        style='width: 100%'>
      </el-input>
    </template>
  </xpt-list>
</template>

<script>
  export default {
    name: 'list',
    components: {},
    props:['params'],
    data() {
      let self = this;
      return {
        btns: [
          {
            type:'primary',
            txt:'刷新',
            click(){
              self.getList();
            }
          },
          {
            type: 'primary',
            txt: '确认摆场',
            click: self.updateExhibition
          }
        ],
        cols: [
          {
            label: '商品编号',
            prop: 'exhibit_no',
            width: 120,
          },
          {
            label: '商品子编号',
            prop: 'exhibit_child_no',
            width: 120
          },
          {
            label: '物料编码',
            prop: 'material_no',
            width: 130
          },
          {
            label: '物料名称',
            prop: 'material_name'
          },
          {
            label: '物料规格',
            prop: 'material_specification',
            width: 160
          },
          {
            label: '店铺名称',
            prop: 'shop_name',
            width: 110
          },
          {
            label: '商品数量',
            prop: 'goods_quantity',
            width: 40
          },
          {
            label: '摆场区域',
            slot: 'shop_area',
          },
          {
            label: '区域名称',
            prop: 'area_name',
            width: 80
          },
          {
            label: '区域位置',
            prop: 'area_position',
            width: 80
          },
          {
            label: '状态',
            prop: 'status',
            formatter: function formatter(val) {
              switch (val) {
                case 'DELIVERED':
                  return '已发货';
                  break;
                case 'EXHIBITING':
                  return '摆场中';
                  break;
                case 'SAMPLING':
                  return '清样中';
                  break;
                case 'SAMPLED':
                  return '已清样';
                  break;
                default:
                  return val;
              }
            }
          },
          {
            label: '父级物料编码',
            prop: 'group_material_no',
            width: 120
          },
          {
            label: '父级物料名称',
            prop: 'group_material_name',
            width: 120
          }
        ],
        list: [],
        search:{
          page_name: "scm_exhibition_material",
          where: [],
          page: {
            length: this.pageSize,
            pageNo: 1
          }
        },
        pageSize: 50,
        selectCheckBox:[],
        selectData:{},
        uploadUrl: '/order-web/api/scmexhibitionmaterial/import'
      }

    },
    watch: {},
    methods: {
      selectionChange(data) {
        this.selectCheckBox = data;
      },
      close() {
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      },
      searchData(obj, resolve) {
        this.search.where = obj;
        this.selectData = null;
        this.getList(resolve);
      },
      pageSizeChange(pageSize) {
        this.search.page.length = pageSize;
        this.selectData = null;
        this.getList();
      },
      pageChange(page) {
        this.search.page.pageNo = page;
        this.selectData = null;
        this.getList();
      },
      getList(resolve) {
        console.log(this.params,'paramsparamsparamsparams');
        let list = []
        this.params.list.forEach(v=>{
          list.push(v.exhibit_batch_no)
        });
        this.$request('/order-web/api/scmexhibitionmaterial/confirmShow',
          {
            ...this.search,
            exhibit_batch_nos:list,
          }).then(res => {
          if (res.result) {
            this.list = res.content.list || [];
            this.pageSize = res.content.count;
          }
          resolve && resolve();
        }).catch(err => {
          resolve && resolve();
        }).finally(() => {
          resolve && resolve();
        })
      },
      // d点击获取数据
      rowClick(row) {
        this.selectData = row;
      },
      //确认摆场
      updateExhibition() {
        if (this.selectCheckBox.length===0) {
          this.$message.info('请选择数据')
        } else {
          this.$axios('post',
            '/order-web/api/scmexhibitionmaterial/updateConfirm?permissionCode=EXHIBITION_MATERIAL_CONFIRM_SWING', this.selectCheckBox
          ).then(res => {
            if (res.result) {
              this.$message.success(res.msg);
              this.$root.eventHandle.$emit('removeTab', this.params.tabName)
            } else {
              this.$message.error(res.msg)
            }
          }).catch(err => {
          })
        }
      },
      // 选择摆场区域
      selectShopArea() {
        let self = this;
        setTimeout(function() {
          self.$root.eventHandle.$emit('alert', {
            params: {
              callback: (d) => {
                self.selectData.shop_area = d.shop_area;
                self.selectData.area_name = d.area_name;
                self.selectData.area_position = d.area_position;
              },
              shop_name: self.selectData.shop_name
            },
            component: () => import('@components/shop_regional_distribu/selectShopDistribution'),
            style: 'width:800px;height:500px',
            title: '店铺区域分布列表'
          }) }, 300);
      },
      // 导出excel
      exportExcel() {
        let params = {
          id: this.params.exhibit_apply_id,
          page_name: "scm_exhibition_application_material",
        }
        this.$axios('post',
          '/order-web/api/exhibitionapplication/export',
          params
        ).then(res => {
          console.log(res);
        }).catch(err => {
          this.$message.error(err)
        })
      }
    },
    computed: {},
    created() {
      this.getList();
    },
    mounted() {
    },
    destroyed() {
    }
  }
</script>

<style scoped>
</style>
