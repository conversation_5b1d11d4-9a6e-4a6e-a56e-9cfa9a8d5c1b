<!-- 快递商品清单列表 -->
<template>
    <xpt-list ref='expressGoodsList' id='express_goodList' :data='goodsList' :btns='goodsBtns' :colData='goodsCols' orderNo isNeedClickEvent @selection-change='goodsRadioChange' :pageTotal='goodsCount' :searchPage='goodsQuery.page_name' @search-click='goodsListSearch' @page-size-change='goodsPageSizeChange' @current-page-change='goodsPageChange' @cell-click='goodsCellClick'>
        <xpt-import slot='btns' :taskUrl="uploadUrl" :callback="uploadCallback" class='mgl10' :sysTradeId="1" :isupload="false"></xpt-import>
        <template slot="control" slot-scope="scope">
            <el-select placeholder="请选择" v-model="scope.row.control" :disabled="scope.row.express_list_id ? true : false" size='mini' style="width:100%;">
                <el-option key="0" label="强制发快递" value='0'></el-option>
                <el-option key="1" label="推荐发快递" value='1'></el-option>
            </el-select>
        </template>
        <template slot="if_install" slot-scope="scope">
            <el-select placeholder="请选择" v-model="scope.row.if_install" :disabled="scope.row.express_list_id ? true : false" size='mini' style="width:100%;">
                <el-option key="0" label="非安装" value='0'></el-option>
                <el-option key="1" label="安装" value='1'></el-option>
            </el-select>
        </template>
    </xpt-list>
</template>
<script>
    import fn from '@common/Fn.js'
    export default {
        data() {
            let self = this;
            return {
                goodsList: [],
                goodsBtns: [{
                    type: 'success',
                    txt: '刷新',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.refresh()
                    }
                }, {
                    type: 'primary',
                    txt: '新增',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.goodsAdd()
                    }
                }, {
                    type: 'danger',
                    txt: '删除',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.goodDelete()
                    }
                }, {
                    type: 'primary',
                    txt: '保存',
                    disabled: () => {
                        return self.ifSave;
                    },
                    click() {
                        self.save()
                    }
                }, {
                    type: 'warning',
                    txt: '生效',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.changeStatus('Y')
                    }
                }, {
                    type: 'warning',
                    txt: '失效',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.changeStatus('N')
                    }
                }, {
                    type: 'primary',
                    txt: '快递控制方式',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.changeExpressType()
                    }
                }, {
                    type: 'primary',
                    txt: '安装方式',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.changeInstallType()
                    }
                }, {
                    type: 'primary',
                    txt: '导出',
                    disabled: () => {
                        return false;
                    },
                    click() {
                        self.exportList()
                    }
                }, {
                    type: "info",
                    txt: "报表导出文件下载",
                    loading: false,
                    click() {
                        self.showExportList()
                    }
                }],
                goodsCols: [{
                    label: '物料编码',
                    prop: 'material_number',
                    width: 190,
                    bool: true,
                    //isClickEvent:true,
                    iconClick(row) {
                        setTimeout(self.goodsChoose, 10)
                    },
                    disabled(row) {
                        return row.express_list_id ? true : false
                    },
                    blur: self.goodsCopy
                }, {
                    label: '物料名称',
                    prop: 'material_name',
                    width: 120
                }, {
                    label: '物料规格',
                    prop: 'material_specification',
                    width: 200
                }, {
                    label: '快递控制方式',
                    slot: 'control',
                    // formatter(val) {
                    // 	switch(val) {
                    // 		case 0: return '强制发快递'; break;
                    // 		case 1: return '推荐发快递'; break;
                    // 		default: return val; break;
                    // 	}
                    // },
                    width: 120
                },{
                    label: '安装方式',
                    slot: 'if_install',
                    width: 120
                }, {
                    label: '生效状态',
                    prop: 'status',
                    formatter(val) {
                        switch (val) {
                            case 'Y':
                                return '生效';
                                break;
                            case 'N':
                                return '失效';
                                break;
                            default:
                                return val;
                                break;
                        }
                    },
                }, {
                    label: '失效人',
                    prop: 'lose_efficacy_name',
                    width: 160,
                }, {
                    label: '失效时间',
                    prop: 'lose_efficacy_time',
                    width: 130,
                    format: 'dataFormat1'
                }, {
                    label: '创建人',
                    prop: 'creator_name'
                }, {
                    label: '创建时间',
                    prop: 'create_time',
                    format: 'dataFormat1',
                    width: 130
                }, {
                    label: '修改人',
                    prop: 'modify_name'
                }, {
                    label: '修改时间',
                    prop: 'modify_time',
                    format: 'dataFormat1',
                    width: 130
                }],
                goodsSelect: [],
                goodsCount: 0,
                goodsQuery: {
                    page_name: "cloud_express_list",
                    where: [],
                    page_size: 50,
                    page_no: 1,
                    // 是否过滤,1为不过滤，不传则默认过滤
                    if_need_page: 'Y'
                },
                goodsSelects: '',
                uploadUrl: '/order-web/api/expressList/addImportCloudExpressList?permissionCode=EXPRESS_LIST_IMPORT',
                contrlValue: '',
                ifSave: false,
                installMethod:'',
                installMethodOptions:{
                    '0':'非安装',
                    '1':'安装'
                }
            }
        },
        methods: {
            // 导入后的回调函数
            uploadCallback(data) {
                if (data.body.result) {
                    this.getGoodsList()
                }
            },
            // 可删除空行，未生效行。已生效/失效行不可删除。删除时提示。
            goodDelete() {
                let illegalList = []
                this.goodsSelect.forEach(item => {
                    if ((item.status === 'Y' || item.status === 'N') && item.express_list_id) {
                        illegalList.push(item.material_number)
                    }
                    if (item.temId) {
                        var i = this.goodsList.length;
                        while (i--) {
                            if (this.goodsList[i].temId === item.temId) {
                                this.goodsList.splice(i, 1)
                                break
                            }
                        }
                    }
                })
                if (illegalList.length > 0) {
                    // if (illegalList.length > 3) {
                    //     illegalList.length = 3
                    //     msg = '商品编码' + illegalList.join('、') + '等已生效/失效，不可删除'
                    // } else {
                    //     msg = '商品编码' + illegalList.join('、') + '已生效/失效，不可删除'
                    // }
                    this.$message.error('可删除空行，未生效行。已生效/失效行不可删除')
                    return
                }
            },

            // 更新快递控制方式 start
            contrlChange1(y) {
                if (this.contrlValue === '') {
                    document.getElementById('title').innerText = '你选择的是'
                }
                this.contrlValue = '0'
                document.getElementById('selectContrl').innerText = (this.contrlValue === '0' ? '强制发快递' : '推荐发快递')
            },
            contrlChange2(y) {
                if (this.contrlValue === '') {
                    document.getElementById('title').innerText = '你选择的是'
                }
                this.contrlValue = '1'
                document.getElementById('selectContrl').innerText = (this.contrlValue === '0' ? '强制发快递' : '推荐发快递')
            },
            resetContrlDialog() {
                if (this.contrlValue != '') {
                    document.getElementById('title').innerText = '请选择控制方式'
                    this.contrlValue = ''
                    document.getElementById('selectContrl').innerText = ''
                }
            },
            confirmContrl() {
                let ifContrl = this.contrlValue === '1' ? '0' : '1'
                let self = this,
                    params = {
                        ids: [],
                        control: this.contrlValue
                    }
                this.goodsSelect.forEach((item, idx) => {
                        params.ids.push(item.express_list_id)
                })
                this.ajax.postStream('/order-web/api/expressList/updateExpressControl?permissionCode=EXPRESS_LIST_UPDATE_CONTROL', params, d => {
                    if (d.body.result) {
                        self.$message.success(d.body.msg)
                        self.getGoodsList()
                    } else {
                        self.$message.error(d.body.msg)
                    }
                }, (e) => {
                    this.$message.error(e);
                })
            },
            changeExpressType() {
                let ifHasAdd = this.goodsList.some(item => {
                    return item.temId
                })
                if (ifHasAdd) {
                    this.$message.error('列表存在新增数据，请执行保存操作')
                    return
                }
                if (this.goodsSelect.length === 0) {
                    this.$message.error('请选择需要操作的行')
                    return
                }
                let self = this
                const h = this.$createElement
                this.$msgbox({
                    title: '确认',
                    message: h('div', null, [
                        h('p', null, [
                            h('span', {
                                attrs: {
                                    id: 'title'
                                }
                            }, '请选择控制方式'),
                            h('span', {
                                attrs: {
                                    class: 'xiudounb',
                                    id: 'selectContrl'
                                }
                            }, self.contrlValue)
                        ]),
                        h('button', {
                            style: 'margin-right: 30px;margin-top: 20px;',
                            on: {
                                '!click': self.contrlChange1,
                            }
                        }, '强制发快递'),
                        h('button', {
                            style: 'margin-bottom: 10px',
                            on: {
                                '!click': self.contrlChange2,
                            }

                        }, '推荐发快递')
                    ]),
                    showCancelButton: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    beforeClose: (action, instance, done) => {
                        if (action === 'confirm') {
                            if (self.contrlValue === '') {
                                self.$message.error('请选择控制方式')
                                return
                            }
                            self.confirmContrl()
                            self.resetContrlDialog()
                            done()
                        } else {
                            self.resetContrlDialog()
                            done()
                        }
                    }
                }).then(action => {
                    // this.$message({
                    //     type: 'info',
                    //     message: 'action: ' + action
                    // })
                })
            },
            // 更新快递控制方式 end
            // 改变生失效
            changeStatus(status) {
                let ifHasAdd = this.goodsList.some(item => {
                        return item.temId
                    }),
                    msg = ''   
                let ifStatus = status === 'Y' ? 'N' : 'Y'
                if (ifHasAdd) {
                    msg = '列表存在新增数据，请执行保存操作'
                } else if (this.goodsSelect.length === 0) {
                    msg = '请选择需要操作的行'
                }
                if (msg) {
                    this.$message.error(msg)
                    return
                }
                let self = this, remark_value = this.goodsSelect[0].status
                let ifChangeSign = this.goodsSelect.every(item => {
                    return item.status === remark_value
                }), checkMsg = ''
                if (!ifChangeSign) {
                    checkMsg = '请选择生效状态一致的数据进行批量操作'
                } else if ((status =='Y' && this.goodsSelect[0].status == 'Y' && ifChangeSign) || (ifChangeSign && status =='N' && this.goodsSelect[0].status == 'N')) {
                    let key = status == 'Y'? '已生效的' : '已失效的'
                    checkMsg = key + '不可以重复操作'
                }
                if (checkMsg) {
                    this.$message.error(checkMsg)
                    return
                }
                let params = {
                        ids: [],
                        status: status
                    }
                this.goodsSelect.forEach((item, idx) => {
                    params.ids.push(item.express_list_id)
                })
                this.ajax.postStream('/order-web/api/expressList/updateExpressStatus?permissionCode=EXPRESS_LIST_UPDATE_STATUS', params, d => {
                    if (d.body.result) {
                        self.$message.success(d.body.msg)
                        self.getGoodsList()
                    } else {
                        self.$message.error(d.body.msg)
                    }
                }, (e) => {
                    this.$message.error(e);
                })
            },
            // 改变安装方式
            changeInstallType(){
                let ifHasAdd = this.goodsList.some(item => {
                    return item.temId
                })
                if (ifHasAdd) {
                    this.$message.error('列表存在新增数据，请执行保存操作')
                    return
                }
                if (this.goodsSelect.length === 0) {
                    this.$message.error('请选择需要操作的行')
                    return
                }
                let self = this
                this.installMethod = ''
                const h = this.$createElement;
                this.$msgbox({
                    title: '安装方式确认',
                    message: h('div', null, [
                        h('p', null, [
                            h('span', {
                                attrs: {
                                    id: 'title'
                                }
                            }, '是否安装：'),
                            h('span', {
                                attrs: {
                                    class: 'xiudounb',
                                    id: 'installMethod'
                                }
                            }, '')
                        ]),
                        h('button', {
                            style: 'margin-right: 30px;margin-top: 20px;',
                            on: {
                                click: ()=>{self.changeInstallMethod('0')},
                            }
                        }, '非安装'),
                        h('button', {
                            style: 'margin-bottom: 10px',
                            on: {
                                click: ()=>{self.changeInstallMethod('1')},
                            }

                        }, '安装')
                    ]),
                    showCancelButton: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    beforeClose: async (action, instance, done) => {
                        if (action === 'confirm') {
                            if (self.installMethod == '') {
                                self.$message.error('请选择安装方式')
                                return
                            }
                            instance.confirmButtonLoading = true;
                            instance.confirmButtonText = '执行中...';
                            //安装方式接口
                            await self.confirmInstallType()
                            instance.confirmButtonLoading = false;
                            document.getElementById('installMethod').innerText=""
                            done()
                        } else {
                            document.getElementById('installMethod').innerText=""
                            done()
                        }
                    }
                }).then(() => {
                    // 弹框确定后逻辑
                });
            },
            changeInstallMethod(type){
                this.installMethod=type;
                document.getElementById('installMethod').innerText = this.installMethodOptions[type]||type;
            },
            confirmInstallType(){
                let params = {
                    ids: this.goodsSelect.map(item=>item.express_list_id),
                    ifInstall: this.installMethod
                }
                this.ajax.postStream('/order-web/api/expressList/updateIfInstall?permissionCode=EXPRESS_LIST_INSTALL_METHOD', params, res => {
                    if (res.body.result) {
                        this.$message.success(res.body.msg)
                        this.getGoodsList()
                    } else {
                        this.$message.error(res.body.msg)
                    }
                }, (e) => {
                    this.$message.error(e);
                })
            },
            exportList() {
                let self = this ,data = JSON.parse(JSON.stringify(this.goodsQuery)), ids = [];
                if (this.goodsSelect.length > 0) {
                    this.goodsSelect.forEach(item => {
                        ids.push(item.express_list_id)
                    })
                    data.list_express_list_id = ids
                }
                this.ajax.postStream('/order-web/api/expressList/exportCloudExpressList?permissionCode=EXPRESS_LIST_EXPORT', data, d => {
                    if (d.body.result) {
                        self.$message.success(d.body.msg)
                    } else {
                        self.$message.error(d.body.msg)
                    }
                }, (e) => {
                    this.$message.error(e);
                })
            },
            refresh() {
                this.getGoodsList()
            },
            // 行选中
            goodsRadioChange(row) {
                this.goodsSelect = row;
            },
            // 获取商品信息
            getGoodsList(resolve, ifPromiss) {
                let self = this;
                let data = JSON.parse(JSON.stringify(this.goodsQuery));
                let url = '/order-web/api/expressList/list'
                this.ajax.postStream(url, data, d => {
                    if (d.body.result && d.body.content) {
                        this.goodsList = d.body.content.list
                        this.goodsCount = d.body.content.count
                    }
                    resolve && resolve();
                }, (e) => {
                    this.$message.error(e);
                    resolve && resolve();
                })
            },
            //通用查询搜索
            goodsListSearch(where, reslove) {
                this.goodsQuery.where = where
                this.getGoodsList(reslove)
            },
            // 当前页改变
            goodsPageSizeChange(ps) {
                this.goodsQuery.page_size = ps;
                this.getGoodsList();
            },
            // 当前页面显示行数改变
            goodsPageChange(page) {
                this.goodsQuery.page_no = page;
                this.getGoodsList();
            },
            // 新增，复制新增
            goodsAdd() {
                let copyData = {}
                copyData.temId = new Date().getTime()
                copyData.material_number = ''
                copyData.material_name = ''
                copyData.material_specification = ''
                copyData.status = 'Y'
                copyData.control = ''
                copyData.if_install = ''
                this.goodsList.unshift(copyData)
                // 滚动到最底部
                let scrollObj = document.querySelector('#express_goodList').querySelector('.el-table__body-wrapper')
                this.$nextTick(function() {
                    scrollObj.scrollTop = 0
                })
            },
            goodsCellClick(row, col) {
                this.goodsSelects = row
            },
            /*
            商品行商品编码复制后失去焦点触发的事件
            根据商品复制的商品编码查询商品信息
            */
            goodsCopy(row) {
                let self = this;
                let num = row.material_number;
                if (!num.trim()) return;
                self.ajax.get('/material-web/api/material/getTradeMaterialByNumber/' + num.trim(), res => {
                    if (res.body.result) {
                        let data = res.body.content
                        if (data) {
                            let addList = []
                            self.goodsList.forEach(item => {
                                if (item.temId && item.material_id) {
                                    addList.push(item.material_id)
                                }
                            })
                            if (addList.length > 0) {
                                if (new Set(addList).has(data.sourceMaterialId)) {
                                    self.$message.error('不能同时新增相同的商品编码')
                                    self.resetRow(row)
                                    return
                                }
                            }
                            row.material_number = data.materialNumber
                            row.material_id = data.sourceMaterialId
                            row.material_name = data.materialName
                            row.material_specification = data.materialSpecification
                            return
                        }
                        self.$message.error('没有查询到该物料信息')
                        self.resetRow(row)
                    } else {
                        self.$message.error(res.body.msg);
                        self.resetRow(row)
                    }
                }, err => {
                    self.$message.error(err);
                })
            },
            resetRow(row) {
                row.material_number = ''
                row.material_id = ''
                row.material_name = ''
                row.material_specification = ''
                row.status = 'Y'
                row.control = ''
                row.if_install = ''
            },
            /*
            选择商品
            */
            goodsChoose() {
                new Promise((resolve) => {
                    setTimeout(resolve, 10)
                }).then(() => {
                    let params = {},
                        self = this;
                    params.callback = d => {
                        if (d) {
                            let addList = []
                            self.goodsList.forEach(item => {
                                if (item.temId && item.material_number) {
                                    addList.push(item.material_number)
                                }
                            })
                            if (addList.length > 0) {
                                if (new Set(addList).has(d.materialNumber)) {
                                    self.$message.error('不能同时新增相同的商品编码')
                                    self.resetRow(self.goodsSelects)
                                    return
                                }
                            }
                            self.goodsSelects.material_number = d.materialNumber;
                            self.goodsSelects.material_id = d.sourceMaterialId;
                            self.goodsSelects.material_name = d.materialName;
                            self.goodsSelects.material_specification = d.materialSpecification;
                        }
                    }
                    self.$root.eventHandle.$emit('alert', {
                        params: params,
                        component: () => import('@components/order/selectGoodsList'),
                        style: 'width:800px;height:500px',
                        title: '商品列表'
                    });

                })
            },
            save() {
                let data = [],
                    self = this
                let msg = ''
                this.goodsList.some((obj, index) => {
                    if (obj.material_number && obj.control === '') {
                        msg = '快递方式不能为空'
                    }else if (obj.material_number && obj.if_install === '') {
                        msg = '安装方式不能为空'
                    }
                    if (msg) {
                        msg = '快递商品清单明细-第' + (index + 1) + '行-' + msg
                        return true
                    }
                })
                if (msg) {
                    this.$message.error(msg)
                    return
                }
                this.ifSave = true
                this.goodsList.forEach(item => {
                    if (item.material_id && item.temId) {
                        let obj = {
                            material_id: item.material_id,
                            material_number: item.material_number,
                            status: item.status,
                            material_name: item.material_name,
                            material_specification: item.material_specification,
                            control: item.control,
                            if_install: item.if_install,
                        }
                        data.push(obj)
                    }
                })
                this.ajax.postStream('/order-web/api/expressList/saveOrUpdate?permissionCode=EXPRESS_LIST_SAVE', {
                    cloudExpressListVoList: data
                }, (d) => {
                    self.ifSave = false
                    if (d.body.result) {
                        this.$message.success(d.body.msg)
                    } else {
                        this.$message.error(d.body.msg)
                    }
                    setTimeout(() => {
                        self.getGoodsList()
                    }, 2000)
                }, err => {
                    self.ifSave = false
                    this.$message.error(err);
                });
            },
            showExportList() {
                this.$root.eventHandle.$emit('alert', {
                    component: () => import('@components/after_sales_report/export'),
                    style: 'width:900px;height:600px',
                    title: '报表导出列表',
                    params: {
                        query: {
                            type: 'EXCEL_TYPE_CLOUD_EXPRESS_LIST_EXPORT',
                        },
                    },
                })
            }
        },
        computed: {},
        watch: {},
        mounted() {
            this.getGoodsList()
        }
    }
</script>
<style type="text/css">
    .el-dialog__wrapper {
        background-color: transparent
    }

    .xiudounb {
        color: red
    }
</style>