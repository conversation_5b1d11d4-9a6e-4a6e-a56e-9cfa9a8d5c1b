<!--新增发布价格-->
<template>
	<div class='xpt-flex'>
		<el-row class='xpt-top'>
			<el-button type='primary'  size='mini' @click="saveEvent" :disabled="/^(PENDING_REVIEW|APPROVED)/.test(price.audit_status)"  >保存</el-button>
			<el-button type='success'  size='mini' @click="commitEvent('commit?permissionCode=FINAL_PRICE_COMMIT')"   :disabled="/^(PENDING_REVIEW|APPROVED)/.test(price.audit_status)"   >提交</el-button>
			<el-button type='warning'  size='mini' @click="eventAjax('audit?permissionCode=FINAL_PRICE_AUDIT')"   :disabled="/^(CREATE|REJECT|APPROVED)/.test(price.audit_status)">审核</el-button>
			<el-button type='success'  size='mini' @click="eventAjax('reject?permissionCode=FINAL_PRICE_REJECT')"  :disabled="/^(CREATE|REJECT|APPROVED)/.test(price.audit_status)" >驳回</el-button>
			<el-button type='danger'  size='mini' @click="eventAjax('revocation?permissionCode=FINAL_PRICE_REVOCATION')"  :disabled="/^(CREATE|REJECT|APPROVED)/.test(price.audit_status)" >撤回</el-button>
			<el-button type='warning'  size='mini' @click="eventAjax('invalid?permissionCode=FINAL_PRICE_INVALID')" :disabled="/^(CREATE|REJECT|PENDING_REVIEW)/.test(price.audit_status)" >失效</el-button>
			<el-button type='warning'  size='mini' @click="getDetail(price.header_id)" :disabled="!price.header_id" >刷新</el-button>
		</el-row>
		<el-form label-position="right" label-width="120px" :model="price" :rules="rules" ref="price">
			<el-tabs v-model="activeName"  >
				<el-tab-pane label="基本信息" name="first" class='xpt-flex'>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="价格版本号"  >
								<el-input v-model="price.price_version_number"  size='mini'  disabled></el-input>
							</el-form-item>
							<el-form-item label="生效状态">
								<el-input v-model="price.enable_status_name"  size='mini' disabled></el-input>
							</el-form-item>
							<el-form-item label="审核状态">
								<el-input v-model="price.audit_status_name"  size='mini' disabled></el-input>
							</el-form-item>
							<el-form-item label="创建人">
								<el-input v-model="price.creator_name"  size='mini' disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="价格版本名称"   prop='price_version_name'>
								<el-input v-model="price.price_version_name" :maxlength="20" size='mini' :disabled="/^(PENDING_REVIEW|APPROVED)/.test(price.audit_status)" show-word-limit></el-input>
								<el-tooltip v-if='rules.price_version_name[0].isShow' class="item" effect="dark" :content="rules.price_version_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
							</el-form-item>
							<el-form-item label="生效开始时间"  prop="begin_enable_time">
								<el-date-picker v-model="price.begin_enable_time" type="datetime" placeholder="选择日期" :disabled="/^(PENDING_REVIEW|APPROVED)/.test(price.audit_status)"
								size='mini'></el-date-picker>
								<el-tooltip v-if='rules.begin_enable_time[0].isShow' class="item" effect="dark" :content="rules.begin_enable_time[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
							</el-form-item>
							<el-form-item label="审核人">
								<el-input v-model="price.auditor_name"  size='mini' disabled></el-input>
							</el-form-item>
							<el-form-item label="创建时间">
								<!-- <el-input v-model="price.create_time"  size='mini' disabled></el-input> -->
								<el-date-picker v-model="price.create_time" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>

							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="价格类型" prop='price_type'>
								<xpt-select-aux v-model='price.price_type' aux_name='final_price_type' :disabled="/^(PENDING_REVIEW|APPROVED)/.test(price.audit_status)"></xpt-select-aux>
								<el-tooltip v-if='rules.price_type[0].isShow' class="item" effect="dark" :content="rules.price_type[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
							</el-form-item>
							<el-form-item label="生效结束时间"  prop="end_enable_time">
								<el-date-picker v-model="price.end_enable_time" type="datetime" placeholder="选择日期" :disabled="/^(PENDING_REVIEW|APPROVED)/.test(price.audit_status)"
								size='mini'></el-date-picker>
								<el-tooltip v-if='rules.end_enable_time[0].isShow' class="item" effect="dark" :content="rules.end_enable_time[0].message" placement="right-start" popper-class='xpt-form__error'>
									 	<i class='el-icon-warning'></i>
									</el-tooltip>
							</el-form-item>
							<el-form-item label="审核时间">
								<!-- <el-input v-model="price.audit_time"  size='mini' disabled></el-input> -->
								<el-date-picker v-model="price.audit_time" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>
							</el-form-item>
							<!-- <el-form-item label="备注">
								<el-input v-model="price.remark"  size='mini' ></el-input>
							</el-form-item> -->
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='16'>
							<el-form-item label="备注" style="height:60px">
								<el-input type="textarea" v-model="price.remark" :maxlength="240" :disabled="/^(PENDING_REVIEW|APPROVED)/.test(price.audit_status)"></el-input>
							</el-form-item>
						</el-col>
						
					</el-row>

				</el-tab-pane>
				
			</el-tabs>
		</el-form>

		<div class='xpt-flex__bottom'>
			<el-tabs v-model="listActiveName"  >
				<el-tab-pane label="价格明细" name="first" class='xpt-flex'>
					<xpt-list
						:data='priceList'
						:colData='cols'
						:btns='btns'
						:orderNo="true"
						@selection-change='priceSelects'
						selection='checkbox'
					>
					<xpt-upload-v3
						slot="btns"
						uploadBtnText="导入"
						:uploadSize="20"
						acceptTypeStr=".xlsx,.xls"
						:dataObj="uploadDataObj"
						:disabled="false"
						:ifMultiple="false"
						:showSuccessMsg="false"
						@uploadSuccess="uploadSuccess"
						btnType="success"
						style="display: inline-block; margin: 0px 10px"
					>
					</xpt-upload-v3>
					</xpt-list>
				</el-tab-pane>
				
			</el-tabs>
		</div>

	</div>
</template>
<script>
	
    import validate from '@common/validate.js';
    import fn from '@common/Fn.js';
	export default {
	    props:['params'],
		data(){
            var self = this;
			return {
					uploadDataObj: {
						parent_name: "",
						parent_no: "",
						child_name: null,
						child_no: null,
						content: {},
					},
					activeName:'first',
					listActiveName:'first',
					price:{
						price_version_name:'',
						price_type:'',
						enable_status:'',
						begin_enable_time:'',
						end_enable_time:'',
						disable_time:'',
						remark:'',
						lineList:[],
						audit_status:'CREATE'
						
					},
					rules:{
						begin_enable_time:[
							 {
								required: true,
								isShow: false,
								message: '请填写开始生效时间',
								trigger: 'blur',
								validator: function(rule, value, callback) {
									if(!value) {

										self.rules[rule.field][0].message = '请填写开始生效时间'
										self.rules[rule.field][0].isShow = true
										callback(new Error(''));
									} else if(value>self.price.end_enable_time){
										self.rules[rule.field][0].message = '请填写开始时间必须小于结束时间'
										self.rules[rule.field][0].isShow = true
										callback(new Error(''));
									}else {
										self.rules[rule.field][0].isShow = false
										self.rules.end_enable_time[0].isShow = false
										callback();
									}
								}
							},
							
						],
						end_enable_time:[
							 {
								required: true,
								isShow: false,
								message: '请填写结束生效时间',
								trigger: 'blur',
								validator: function(rule, value, callback) {
									if(!value) {

										self.rules[rule.field][0].message = '请填写结束生效时间'
										self.rules[rule.field][0].isShow = true
										callback(new Error(''));
									} else if(value<self.price.begin_enable_time){
										self.rules[rule.field][0].message = '请填写开始时间必须小于结束时间'
										self.rules[rule.field][0].isShow = true
										callback(new Error(''));
									}else {
										self.rules[rule.field][0].isShow = false
										self.rules.begin_enable_time[0].isShow = false
										callback();
									}
								}
							},
							
						],
						price_version_name:[
							 {
								required: true,
								isShow: false,
								message: '请填写价格版本名称',
								trigger: 'blur',
								validator: function(rule, value, callback) {
									if(!value) {
										self.rules[rule.field][0].isShow = true
										callback(new Error(''));
									} else {
										self.rules[rule.field][0].isShow = false
										callback();
									}
								}
							},
							
						],
						price_type:[
							 {
								required: true,
								isShow: false,
								message: '请填写价格版本名称',
								trigger: 'blur',
								validator: function(rule, value, callback) {
									if(!value) {
										self.rules[rule.field][0].isShow = true
										callback(new Error(''));
									} else {
										self.rules[rule.field][0].isShow = false
										callback();
									}
								}
							}],
					},
					btns:[
						{
							type: "primary",
							txt: "新增物料",
							loading: false,
							click() {
							self.addMaterial();
							}
						},
						{
							type: "warning",
							txt: "失效",
							loading: false,
							click() {
							self.eventList('batchInvalidLine?permissionCode=FINAL_PRICE_LINE_INVALID');
							}
						},
						{
							type: "danger",
							txt: "删除",
							loading: false,
							click() {
							self.deleteList('batchDeleteLine?permissionCode=FINAL_PRICE_LINE_DELETE');
							}
						},
						
						{
							type: "success",
							txt: "查看导入结果",
							loading: false,
							click() {
							self.showExportList('EXCEL_TYPE_SALES_MATERIAL_IMPORT');
							}
						},
						
					],
					priceList:[],
					cols:[
						{
							label: '物料编码',
							prop: 'material_number',
						},{
							label: '物料名称',
							prop: 'material_name',
						},{
							label: '规格描述',
							prop: 'material_specification',
						},{
							label: '价格',
							prop: 'price',
							bool: true,
							elInput:true,
							blur(row){
								row.price = self.fitterPrice(row.price)?row.price:'';
							},
							disabled(row) {
								return ['REJECT','CREATE'].includes(self.price.audit_status) ?false:true;
							},
						},{
							label: '活动标签',
							prop: 'active_tag',
							bool: true,
							elInput:true,
							blur(row){
								// console.log(row);
								// row.active_tag = row.active_tag.length<20?row.active_tag:'';
								row.active_tag = self.fitterTag(row.active_tag)?row.active_tag:'';
							},
							disabled(row) {
								return ['REJECT','CREATE'].includes(self.price.audit_status) ?false:true;
							},
						},{
							label: '行状态',
							prop: 'line_status_name',
						},{
							label: '失效人',
							prop: 'disable_user_name',
						},{
							label: '失效时间',
							prop: 'disable_time',
							format:'dataFormat1'
						},
					],
					selectList:[],
					saveDisabled:false,

					
				
				}
		},
		methods:{
			showExportList (type){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/finalPrice/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: type,
					},
				},
			})
		},
				//上传成功返回结果
    uploadSuccess(result) {
      if (result.length > 0 && !!result[0].path) {
        this.importFileUrl(result[0].path);
      }
    },
    //导入
    importFileUrl(fileUrl) {
      let params = {
        file_path: fileUrl,
		header_id:this.price.header_id,
		price_version_name:this.price.price_version_name,
		price_type:this.price.price_type,
		begin_enable_time:this.price.begin_enable_time,
		end_enable_time:this.price.end_enable_time,
      };
      let self = this;
      this.ajax.postStream(
        "/price-web/api/finalPrice/import",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            setTimeout(() => {
              self.getDetail(res.body.content);
            }, 800);
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
			deleteList(type){
				let self = this;
				if(!self.selectList.length){
					self.$message.error('请选择要处理的数据');
					return;
				}
				let flag = false,
				data = {
					header_id:this.price.header_id,
					deleteLineList:[]
					
				},arr = [];
				for(let i = self.selectList.length -1;i>=0;i--){
					
					console.log(self.selectList[i],i);
					if(!!self.selectList[i].line_id){
						data.deleteLineList.push({line_id:self.selectList[i].line_id})
					}else{
						self.priceList.splice(self.priceList.indexOf(self.selectList[i]),1)
					}
				};
				if(!data.deleteLineList.length){
					return;
				}
				self.ajax.postStream("/price-web/api/finalPrice/"+type, data, res => {
				if(res.body.result) {
					this.$message.success(res.body.msg)
					this.getDetail(res.body.content);
				} else {
					this.$message.error(res.body.msg)
				}
				}, err => {
					this.$message.error(err);
				});
				
			},
			eventList(type){
				let self = this;
				if(!self.selectList.length){
					self.$message.error('请选择要处理的数据');
					return;
				}
				let flag = false,batchInvalidLineFlag = false,
				data = {
					header_id:this.price.header_id,
					
				},arr = [];
				self.selectList.forEach(item=>{
					if(!item.line_id){
						flag = true;
					}
					arr.push({line_id:item.line_id});
					if(type == 'batchInvalidLine' && item.line_status == "INVALID"){{
						batchInvalidLineFlag = true;
					}
					}
				})
				
				if(!!flag){
					self.$message.error('存在未保存数据');
					return;
				}
				if(!!batchInvalidLineFlag){
					self.$message.error('存在已失效数据');
					return;
				}
				data.invalidLineList = arr;
				// if(type == 'batchInvalidLine'){
				// 	data.invalidLineList = arr;
				// }else{
				// 	data.deleteLineList = arr;
				// }
				self.ajax.postStream("/price-web/api/finalPrice/"+type, data, res => {
				if(res.body.result) {
					this.$message.success(res.body.msg)
					this.getDetail(res.body.content);
				} else {
					this.$message.error(res.body.msg)
				}
				}, err => {
					this.$message.error(err);
				});

			},
			fitterTag(tag){
				let flag = false;
				if(tag.length>20){
					this.$message.error('当前输入不符合规范，小于20个字符');
					flag = true;
				}
				return !flag
			},
			// 校验是否整数
			fitterPrice(num){
				var reg = /^(0|([1-9][0-9]{0,6})|([1-9][0-9]{0,6}\.\d{0,2})|(0\.\d{0,2}))$/;
				if(!reg.test(num)){
				this.$message.error('当前输入不符合规范，请输入1-7位小数点后两位数字')
				}
				return reg.test(num)
			},
			eventAjax(type){
				let self = this;
				self.ajax.postStream("/price-web/api/finalPrice/"+type, {header_id:this.price.header_id}, res => {
				if(res.body.result) {
					this.$message.success(res.body.msg)
					this.getDetail(res.body.content);
				} else {
					this.$message.error(res.body.msg)
				}
				}, err => {
					this.$message.error(err);
				});
			},
			
			commitEvent(resolve){
				let self = this;
				let postData  = {
						price_version_name:this.price.price_version_name,
						price_type:this.price.price_type,
						begin_enable_time:this.price.begin_enable_time,
						end_enable_time:this.price.end_enable_time,
						remark:this.price.remark,
						header_id:this.price.header_id,
						lineList:[],
				}
				this.priceList.forEach((item)=>{

					postData.lineList.push({
						material_number:item.material_number,
						price:item.price,
						active_tag:item.active_tag,
						line_id	:item.line_id,
					})
				})

				self.ajax.postStream("/price-web/api/finalPrice/commit?permissionCode=FINAL_PRICE_COMMIT", postData, res => {
				if(res.body.result) {
					this.$message.success(res.body.msg)
					this.getDetail(res.body.content);
				} else {
					this.$message.error(res.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
			},
			saveEvent(resolve){
				let self = this;
				let postData  = JSON.parse(JSON.stringify(this.price));
				postData.begin_enable_time = this.price.begin_enable_time
				postData.end_enable_time =this.price.end_enable_time
				postData.lineList = [];
				this.priceList.forEach((item)=>{

					postData.lineList.push({
						material_number:item.material_number,
						price:item.price,
						active_tag:item.active_tag,
						line_id	:item.line_id	,
					})
				})

				self.ajax.postStream("/price-web/api/finalPrice/saveOrUpdate?permissionCode=FINAL_PRICE_SAVE", postData, res => {
				if(res.body.result) {
					this.$message.success(res.body.msg)
					this.getDetail(res.body.content);
				} else {
					this.$message.error(res.body.msg)
				}
			}, err => {
				this.$message.error(err);
			});
			},
			priceSelects(list){
				this.selectList = list;
			},
			addMaterial(){
				let self = this;
				this.$root.eventHandle.$emit('alert', {
                    params: {
                        selection: 'radio',
                        callback: data => {
							console.log(data);
							let row = {
								material_number:data.data.material_number,
								material_name:data.data.material_name,
								material_specification:data.data.material_specification,
								line_id:null
							}
							self.priceList.push(row);
                        },
                    },
                    component: () => import('@components/finalPrice/materialList'),
                    style: 'width:800px;height:500px',
                    title: '店铺列表',
                })
			},
			getDetail(id){
				let self = this;
				self.ajax.postStream("/price-web/api/finalPrice/getDetail", {header_id:id}, res => {
					if(res.body.result) {
						// self.$message.success(res.body.msg)
						self.price = res.body.content.header;
						self.priceList = res.body.content.lineList;
					} else {
						self.$message.error(res.body.msg)
					}
				})
			}
		},

		
		mounted(){
			var _this = this;
			if(this.params.row){
				_this.getDetail(this.params.row.header_id);
			}
		},
	}
</script>