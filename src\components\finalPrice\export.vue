<!-- 报表excel导出列表 -->
<template>
  <div class='xpt-flex'>
    <xpt-headbar>
      <el-button type='primary' size='mini' @click='getList' slot='left' :loading='onRefresh' :disabled='onRefresh'>刷新</el-button>
      <!--<el-input size='mini' v-model='test' slot='right' placeholder='任务类型'></el-input>-->
      <!--<el-button type='info' size='mini' slot='right' @click='testClick' :loading='testStatus' :disabled='testStatus'>调用</el-button>-->
    </xpt-headbar>
    <xpt-list
      :data='list'
      :colData='cols'
      :btns='btns'
      :pageTotal='count'
      selection=''
      :showHead='false'
      @page-size-change='pageSizeChange'
      @current-page-change='currentPageChange'
    ></xpt-list>
  </div>
</template>
<script>
  export default {
    props: ['params'],
    data() {
      let self = this;
      return {
        list: [],
        btns: [{
          type: 'primary',
          txt: '刷新',
          click: self.getList,
          loading: false
        }],
        cols: [
          {
            label: '报表类型',
            prop: 'program_name'
          }, {
            label: '执行状态',
            prop: 'program_status',
            formatter: self.statusExchage
          }, {
            label: '返回结果',
            prop: 'result'
          }, {
            label: 'Excel生成状态',
            prop: 'run_status',
            formatter: self.statusExchage
          }, {
            label: '文件路径',
            prop: 'file_path',
            html(val) {
              return val ? '<a href="'+ val +'">下载</a>' : val;
            }
          }, {
            label: '提交人',
            prop: 'submit_man'
          }, {
            label: '提交时间',
            prop: 'submit_time',
            format: 'dataFormat1',
            width: '130'
          }
        ],
        query: {
          list_excel_type: [
            'EXCEL_TYPE_FINAL_PRICE_LINE_IMPORT',
            'EXCEL_TYPE_SALES_MATERIAL_IMPORT',
            'EXCEL_TYPE_PERFOR_EXPORT',
            'EXCEL_TYPE_WANGPAI_INTERVAL_SHOP_IMPORT',
          ],
          page_size: self.pageSize,
          page_no: 1
        },
        count: 0,
        statusKeyValue: {
          'EXEC_WAIT':'等待',
          'EXEC_RUNING':'执行中',
          'EXEC_FAILED':'失败',
          'EXEC_SUCCESS':'成功'
        },
        test: '',
        testStatus: false,
        onRefresh: false
      }
    },
    methods: {
      getList() {
        this.btns[0].loading = true;
        this.onRefresh = true;

        var data = JSON.parse(JSON.stringify(this.query))
        ,   url = '/price-web/api/price/import/list'

        


        this.ajax.postStream(url, data, res => {
          if(res.body.result && res.body.content) {
            this.list = res.body.content.list || [];
            this.count = res.body.content.count || 0;
          } else {
            this.$message.error(res.body.msg)
          }
          this.btns[0].loading = false;
          this.onRefresh = false;
        }, error => {
          this.btns[0].loading = false;
          this.onRefresh = false;
          this.$message.error(error.statusText)
        })
      },
      pageSizeChange(ps) {
        this.query.page_size = ps;
        this.getList();
      },
      currentPageChange(page) {
        this.query.page_no = page;
        this.getList();
      },
      statusExchage(val) {
        let keyValues = {
          'EXEC_WAIT':'等待',
          'EXEC_RUNING':'执行中',
          'EXEC_FAILED':'失败',
          'EXEC_SUCCESS':'成功'
        };
        return keyValues[val] || val;
      },
      testClick() {
        this.testStatus = true;
        this.ajax.postStream('/order-web/api/report/job/handle', {job_type: this.test}, res => {
          this.testStatus = false;
          this.$message.info(res.body.msg)
        }, err => {
          this.testStatus = false;
          this.$message.error(err)
        })
      }
    },
    mounted() {
      this.getList();
    }
  }
</script>
