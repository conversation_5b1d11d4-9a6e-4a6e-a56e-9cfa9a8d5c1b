<!-- 价格发布列表 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='cols' 
		:searchPage='search.page_name' 
		:pageTotal='count' 
		selection="radio" 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@search-click='searchClick' 
		@radio-change='selectionChange'
		ref="xptList"
	>
	
	</xpt-list>	
</template>
<script>
import xptList from "../common/list-dynamic-new/list-dynamic"
export default {
	 components:{
      xptList
    },
    props:['params'],
	data() {
		let self = this;
		return {
			list: [],
			btns: [{
				type: 'primary',
				txt: '发布价格',
				click: self.add
			},{
				type: 'success',
				txt: '审核',
				click: ()=>{
					self.eventAjax('audit?permissionCode=FINAL_PRICE_AUDIT')
				}
			},{
				type: 'danger',
				txt: '驳回',
				click: ()=>{
					self.eventAjax('reject?permissionCode=FINAL_PRICE_REJECT')
				}
			},{
				type: 'warning',
				txt: '失效',
				click: ()=>{
					self.eventAjax('invalid?permissionCode=FINAL_PRICE_INVALID')
				}
			}],
			cols: [{
				label: '价格版本号',
				prop: 'price_version_number',
				redirectClick(row) {
					self.addNew({
						row:row
					})
				}
			}, {
				label: '价格版本名称',
				prop: 'price_version_name',
				width: 180
			}, {
				label: '价格类型',
				prop: 'price_type',
			}, {
				label: '发布人',
				prop: 'creator_name',
			}, {
				label: '发布时间',
				prop: 'create_time',
			}, {
				label: '生效状态',
				prop: 'enable_status',
			}, {
				label: '生效开始时间',
				prop: 'begin_enable_time',
			}, {
				label: '生效结束时间',
				prop: 'end_enable_time',
			}, {
				label: '审核状态',
				prop: 'audit_status',
			}, {
				label: '审核人',
				prop: 'auditor_name',
			}, {
				label: '审核时间',
				prop: 'audit_time',
			}, {
				label: '失效时间',
				prop: 'disable_time',
			}, {
				label: '备注',
				prop: 'remark',
			}],
			search: {
				page:{
					length: this.pageSize,
					pageNo:1
				},
				page_name: 'cloud_final_price_header',
				where: [],
			},
			count: 0,
			selectData: ''
		}
	},
	methods: {
		pageSizeChange(ps) {
			this.search.page.length = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page.pageNo = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			this.search.where = obj;
			this.getList(resolve);
		},
		eventAjax(type){
				let self = this;
				if(!this.selectData){
					this.$message.error('请选择一条数据');
					return;
				}
				self.ajax.postStream("/price-web/api/finalPrice/"+type, {header_id:this.selectData.header_id}, res => {
				if(res.body.result) {
					this.$message.success(res.body.msg)
					this.getList();
				} else {
					this.$message.error(res.body.msg)
				}
				}, err => {
					this.$message.error(err);
				});
			},
		selectionChange(obj) {
			this.selectData = obj;
		},
		add(){
            this.$root.eventHandle.$emit('creatTab',{name:'新增价格发布',params:{},component:()=>import('@components/finalPrice/add')});
		},
		addNew(data){
            this.$root.eventHandle.$emit('creatTab',{name:'价格发布详情',params:{row:data.row},component:()=>import('@components/finalPrice/add')});
		},
		getList(resolve) {
			this.ajax.postStream("/price-web/api/finalPrice/listHeader", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve()
			});
		},
	
	},
	mounted() {
		// this.getList()
	}
}
</script>
