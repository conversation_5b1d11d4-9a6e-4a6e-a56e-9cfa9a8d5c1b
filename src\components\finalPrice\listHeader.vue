<!-- 到手价列表 -->
<template>
	<xpt-list
		:data='list' 
		:btns='btns'
		:colData='cols' 
		:searchPage='search.page_name' 
		:pageTotal='count' 
		class="final_price"
		selection="checkbox" 
		@page-size-change='pageSizeChange' 
		@current-page-change='pageChange' 
		@search-click='searchClick' 
		@selection-change='selectionChange'
		ref="xptList"
	>
	<template slot='price_version_number' slot-scope='scope'>
		<div>
			<p>
				<span v-html="scope.row.material_number"></span>
				<span v-html="scope.row.item_code"></span>
			</p>
			<p>
				<span v-html="scope.row.material_specification"></span>
			</p>
		</div>
	</template>
	<template slot='sale_msg' slot-scope='scope'>
		<div>
			<p>
				<span v-html="scope.row.is_stop_name" style="padding-left:4px;"></span>
				<span v-html="scope.row.is_on_sale_name" style="padding-left:4px;"></span>
				<span v-html="scope.row.channel_for" style="padding-left:4px;"></span>
			</p>
			<p>
				<span v-html="scope.row.project_team" style="padding-left:4px;"></span>
				<span v-html="scope.row.item_name" style="padding-left:4px;"></span>
			</p>
		</div>
	</template>
	<template slot='label' slot-scope='scope'>
		<div>
			<p>
				<span>货品标签：</span><span v-html="scope.row.product_label"></span>
			</p>
			<p>
				<span>活动标签：</span>
				<span v-html="scope.row.active_tag"></span>
			</p>
		</div>
	</template>
	<template slot='price' slot-scope='scope'>
		<div>
			<p>
				<span>直播：</span><span v-html="scope.row.live_price"></span>
			</p>
			<p>
				<span>专供：</span>
				<span v-html="scope.row.channel_specific_price"></span>
			</p>
		</div>
	</template>
	<template slot='final_price_B' slot-scope='scope'>
		<div>
			<p>
				<span>普惠：</span><span v-html="scope.row.b_shop_price"></span>
				<span>一阶：</span><span v-html="scope.row.b_shop_a"></span>
			</p>
			<p>
				<span>二阶：</span>
				<span v-html="scope.row.b_shop_b"></span>
				<span>三阶：</span>
				<span v-html="scope.row.b_shop_c"></span>
			</p>
		</div>
	</template>
	<template slot='final_price' slot-scope='scope'>
		<div>
			<p>
				<span>D店：</span><span v-html="scope.row.d_shop_price"></span>
				<span>京东POP：</span><span v-html="scope.row.jd_pop_price"></span>
				<span>京东自营：</span><span v-html="scope.row.jd_self_price"></span>
				<span>婴童：</span><span v-html="scope.row.baby_price"></span>
			</p>
			<p>
				<span>抖快：</span>
				<span v-html="scope.row.dk_price"></span>
				<span>拼多多：</span>
				<span v-html="scope.row.pdd_price"></span>
				<span>唯品会：</span>
				<span v-html="scope.row.vips_price"></span>
			</p>
		</div>
	</template>
	<xpt-upload-v3
      slot="btns"
      uploadBtnText="更新特殊货品"
      :uploadSize="20"
      acceptTypeStr=".xlsx,.xls"
      :dataObj="uploadDataObj"
      :disabled="false"
      :ifMultiple="false"
      :showSuccessMsg="false"
      @uploadSuccess="uploadSuccess"
      btnType="success"
      style="display: inline-block; margin: 0px 10px"
    >
    </xpt-upload-v3>
	</xpt-list>	
</template>
<script>
import xptList from "../common/list-dynamic-new/list-dynamic"
export default {
	//  components:{
    //   xptList
    // },
    props:['params'],
	data() {
		let self = this;
		return {
			uploadDataObj: {
				parent_name: "",
				parent_no: "",
				child_name: null,
				child_no: null,
				content: {},
			},
			list: [],
			btns: [{
							type: "success",
							txt: "查看导入结果",
							loading: false,
							click() {
							self.showExportList('EXCEL_TYPE_FINAL_PRICE_LINE_IMPORT');
							}
						},],
			cols: [{
				label: '物料信息',
				slot: 'price_version_number',
			}, {
				label: '销售信息',
				slot: 'sale_msg',
				width: 180
			}, {
				label: '商品标签',
				slot: 'label',
			}, {
				label: '直播/专供价',
				slot: 'price',
			}, {
				label: 'B店到手价',
				slot: 'final_price_B',
			}, {
				label: '多平台到手价',
				slot: 'final_price',
				width:450
			}],
			search: {
				page:{
					length: this.pageSize,
					pageNo:1
				},
				page_name: 'cloud_final_price_line',
				where: [],
			},
			count: 0,
			selectData: []
		}
	},
	methods: {
		showExportList (type){
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/finalPrice/export'),
				style:'width:900px;height:600px',
				title: '报表导出列表',
				params: {
					query: {
						type: type,
					},
				},
			})
		},
		//上传成功返回结果
    uploadSuccess(result) {
      if (result.length > 0 && !!result[0].path) {
        this.importFileUrl(result[0].path);
      }
    },
    //导入
    importFileUrl(fileUrl) {
      let params = {
        file_path: fileUrl,
      };
      let self = this;
      this.ajax.postStream(
        "/price-web/api/salesMaterial/import",
        params,
        (res) => {
          if (res.body.result) {
            this.$message.success(res.body.msg);
            setTimeout(() => {
              self.getList();
            }, 800);
          } else {
            this.$message.error(res.body.msg);
          }
        },
        (err) => {
          this.$message.error(err);
        }
      );
    },
		pageSizeChange(ps) {
			this.search.page.length = ps;
			this.getList();
		},
		pageChange(page) {
			this.search.page.pageNo = page;
			this.getList();
		},
		searchClick(obj, resolve) {
			this.search.where = obj;
			this.getList(resolve);
		},
		selectionChange(obj) {
			this.selectData = obj;
		},
		
		getList(resolve) {
			this.ajax.postStream("/price-web/api/finalPrice/listLine", this.search, res => {
				if(res.body.result) {
					this.list = res.body.content.list;
					this.count = res.body.content.count;
				} else {
					this.$message.error(res.body.msg)
				}
				resolve && resolve();
			}, err => {
				this.$message.error(err);
				resolve && resolve()
			});
		},
	
	},
	mounted() {
		// this.getList()
	}
}
</script>
<style>


.final_price .el-table__body-wrapper td .cell, .el-table .el-table__fixed-body-wrapper td .cell{
	height: 48px !important; 
}
</style>
