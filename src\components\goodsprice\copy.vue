<!--复制价目表信息-->
<template>
	<div class='xpt-flex'>

		<div class='xpt-flex__bottom'>
			<el-row class='xpt-top'>
				<el-col>
					<el-button type='warning'  size='mini' @click="confirmAddGoods('catalogInfo')">确认复制</el-button>
				</el-col>

			</el-row>
			<div class='xpt-flex__bottom scroll'>
				<el-form label-position="right" label-width="120px" :model="catalogInfo" :rules="rules" ref="catalogInfo">
					<el-row :gutter='40'>
						<el-col :span='18'>
							<el-form-item label="价目表名称" >
								<el-input v-model="catalogInfo.price_list_name"  size='mini'   disabled></el-input>

							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='18'>
							<el-form-item label="新价目表名称" prop="price_list_rename">
								<el-input v-model="catalogInfo.price_list_rename"  size='mini' ></el-input>
								<el-tooltip v-if='rules.price_list_rename[0].isShow' class="item" effect="dark" :content="rules.price_list_rename[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='18'>
							<el-form-item label="价目表类型">
								<el-select  v-model="catalogInfo.price_list_type"  size='mini' @change="changeType">
									<el-option
											v-for="item in priceType"
											:key="item.value"
											:label="item.name"
											:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='18'>
							<el-form-item label="生效日期" prop="enable_date">
								<el-date-picker v-model="catalogInfo.enable_date" type="datetime" placeholder="选择日期" size='mini' :picker-options="pickerOptions" :editable="false"></el-date-picker>
								<el-tooltip v-if='rules.enable_date[0].isShow' class="item" effect="dark" :content="rules.enable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='18'>
							<el-form-item label="失效日期" prop="disable_date">
								<el-date-picker v-model="catalogInfo.disable_date" type="datetime" placeholder="选择日期" size='mini' :picker-options="pickerOptions2" :editable="false"></el-date-picker>
								<el-tooltip v-if='rules.disable_date[0].isShow' class="item" effect="dark" :content="rules.disable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='18'>
							<el-form-item label="是否复制店铺信息">
								<el-select  v-model="catalogInfo.is_cocyStore"  size='mini' placeholder="请选择" v-if="catalogInfo.price_list_type == 'CATALOG_LIST'" disabled>
									<el-option
											v-for="item in status"
											:key="item.value"
											:label="item.name"
											:value="item.value">
									</el-option>
								</el-select>
								<el-select  v-model="catalogInfo.is_cocyStore"  size='mini' placeholder="请选择" v-else>
									<el-option
											v-for="item in status"
											:key="item.value"
											:label="item.name"
											:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

			</div>
		</div>

	</div>
</template>
<script>
	
    import validate from '@common/validate.js';
    import fn from '@common/Fn.js';
	export default {//需要类型和相关的id
	    props:['params'],
		data(){
            var self = this;
			return {
                catalogInfo:{
					price_list_type:'',
                    disable_date:'',
                    enable_date:'',
					price_list_rename:'',
                    price_list_name:'',
                    is_cocyStore:'是',
                    price_list_id:''

				},
                priceType:[
                    {
					    name:'目录价格',
						value:'CATALOG_LIST'
					},
                    {
                        name:'标准价格',
                        value:'STANDARD_LIST'
                    },
                    {
                        name:'活动价格',
                        value:'PREFERENTIAL_LIST'
                    }, {
                        name:'经销结算价格',
                        value:'SETTLE_LIST'
                    }, {
                        name:'摆场价格',
                        value:'EXHIBITION_LIST'
                    }, {
                        name:'硬装采集价格',
                        value:'HARD_DECORATION_LIST'
                    }, {
						name:'直降价目',
						value: 'DIRECT_DESCENT_LIST'
					},{
                        name:'新增整装价目',
                        value: 'PACKAGED_ITEMS_LIST'
                    },{
                        name:'滞销品价目',
                        value: 'UNSALABLE_GOODS_LIST'
                    },{
                        name:'参考零售价目',
                        value: 'REFERENCE_RETAIL_LIST'
                    }
                ],
				status:[
					{
					    name:'是',
						/*value:'1',*/
						value:'是'
					},
					{
					    name:'否',
						/*value:'0',*/
						value:'否'
					}
				],
                pickerOptions:{//禁用日期
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                    date:(function(){
                    	var date = new Date();
                    	var year = date.getFullYear();
                    	var month = date.getMonth()+1;
                    	var day = date.getDate();
                    	var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
                    	return new Date(time);
                    })()
                },
                pickerOptions2:{//禁用日期
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                    date:(function(){
                    	var date = new Date();
                    	var year = date.getFullYear();
                    	var month = date.getMonth()+1;
                    	var day = date.getDate();
                    	var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
                    	return new Date(time);
                    })()
                },
                rules:{
                    price_list_rename:validate.isNotBlank({
                        msg:'请填写价目表名称',
                        trigger:'change',
                        self:self
					}),
					enable_date:validate.enableTime({
                        required:true,
                        msg:'请选择生效日期',
                        trigger:'change',
                        self:self,
						callback:function(){
							var enable_date = self.catalogInfo.enable_date;
							if(!enable_date) return {bool:false,msg:'请选择生效日期'};
							var enableTime = new Date(enable_date).valueOf(),nowTime = new Date().valueOf();

							if(self.catalogInfo.price_list_type == 'PREFERENTIAL_LIST' && enableTime < nowTime) return {bool:false,msg:'生效日期不能小于当前时间'}
                            self.$refs.catalogInfo.validateField('disable_date');
							return true;
						}
					}),
                    disable_date:validate.disableTime({

                        msg:'失效日期不能小于生效日期',
                        trigger:'change',
                        self:self,
                        callback:function(){
                            var enable_date = self.catalogInfo.enable_date,
                                disable_date = self.catalogInfo.disable_date;
							var type = self.catalogInfo.price_list_type;
							if(type == 'PREFERENTIAL_LIST' && !disable_date) return{bool:false,msg:'活动价格目录的失效时间不能为空'};
							console.log('type',type);
                            if(disable_date && new Date(disable_date).valueOf() < new Date(enable_date).valueOf()) return {bool:false,msg:'失效日期不能小于生效日期'};
                            return true;

                        }
					})
				}



			}
		},
		methods:{
            confirmAddGoods(formName){
                this.$refs[formName].validate((valid)=>{
                    if(!valid) return;
					this.requestSubmitPriceInfo()
                });



			},
            requestSubmitPriceInfo(){
                var url='/price-web/api/price/addCopyPriceListAllInfo',
                    data = this.catalogInfo,
                    _this = this;
				var submitData = {};
				var name = data.price_list_name;
				var name2 = data.price_list_rename
				for(var key in data){
				    key != 'price_list_rename'?submitData[key] = data[key]:'';
				}
                submitData.price_list_name = name2;
                if(submitData.disable_date){
                	submitData.disable_date = new Date(submitData.disable_date).getTime();
                }
                if(submitData.enable_date){
                	submitData.enable_date = new Date(submitData.enable_date).getTime();
                }
                this.ajax.postStream(url,submitData,function(d){
                    var data = d.body;
                    _this.$message({
                        message:data.msg?data.msg:'价目表复制失败',
                        type:data.result?'success':'error'
                    });
                    if(!data.result){
                        data.price_list_name = name;
                        data.price_list_rename = name2;
                        return;
					}

                    _this.$root.eventHandle.$emit('removeAlert',_this.params.alertId);
                    _this.$root.eventHandle.$emit('updataPriceList');

                },function(e){
                    console.log(e)
                })
			},
            changeType(value){
              //类型改变

				if('CATALOG_LIST' == this.catalogInfo.price_list_type){
				    this.catalogInfo.is_cocyStore='否';
				}
				this.$refs.catalogInfo.validateField('disable_date');
			},


			initData(){
			  //初使化数据
				var data = this.params || {};
				var catalogInfo = data.data;

				var info = this.catalogInfo;
				for(var key in info){
				    info[key] = catalogInfo[key];
				}
				//当前时间，还有一个小bug
                //info.disable_date = fn.getNowDate({form:'23:59:59'});
                //info.enable_date = fn.getNowDate({form:'00:00:00'});

                info.disable_date = '';
                info.enable_date = '';
                console.log(8888);
                console.log('catalogInfo',catalogInfo);
                catalogInfo.price_list_type == 'CATALOG_LIST'?info.is_cocyStore = '否':info.is_cocyStore = '是';
			}
		},

		mounted(){
			var _this = this;
			_this.initData();
		}



	}
</script>
<style type="text/css" scoped>
	.el-date-editor.el-input{width:200px}
	.el-picker-panel__body .el-input{width:auto;}
</style>
