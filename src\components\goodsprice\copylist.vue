<!--复制价目表信息-->
<template>
	<div class='xpt-flex'>

		<div class='xpt-flex__bottom'>
			<el-row class='xpt-top'>
				<el-col>
					<el-button type='primary'  size='mini' @click="confirmAddGoods">确认添加</el-button>
				</el-col>

			</el-row>
			<div class='xpt-flex__bottom scroll'>
				<el-form label-position="right" label-width="120px">
					<el-table :data="goodsList" border style="width: 100%"  width='100%'>

						<el-table-column  label="价目表编码" prop="name" ></el-table-column>
						<el-table-column label="价目表名称"  prop="name"></el-table-column>

						<el-table-column label="价目表类型"  prop="type">
							<template slot-scope='scope'>
								<el-select  v-model="scope.row.type"  size='mini' placeholder="请选择" @change="changeType(scope.row)">
									<el-option
											v-for="item in priceType"
											:key="item.value"
											:label="item.name"
											:value="item.value">
									</el-option>
								</el-select>

							</template>
						</el-table-column>
						<el-table-column label="生效日期"  prop="enableTime">
							<template slot-scope='scope'>
								<span style="color:red">*</span><el-date-picker v-model="scope.row.enableTime" type="date" placeholder="选择日期" size='mini' :picker-options="pickerOptions" :editable="false"></el-date-picker>
							</template>
						</el-table-column>
						<el-table-column label="失效日期"  prop="disableTime">
							<template slot-scope='scope'>
								<el-date-picker v-model="scope.row.disableTime" type="date" placeholder="选择日期" size='mini' :picker-options="pickerOptions" :editable="false"></el-date-picker>
							</template>
						</el-table-column>
						<el-table-column label="是否复制店铺信息"  prop="status">
							<template slot-scope='scope'>
								<el-select  v-model="scope.row.status"  size='mini' placeholder="请选择" v-if="scope.row.type == 'CATALOG_LIST'" disabled>
									<el-option
											v-for="item in status"
											:key="item.value"
											:label="item.name"
											:value="item.value">
									</el-option>
								</el-select>
								<el-select  v-model="scope.row.status"  size='mini' placeholder="请选择" v-else>
									<el-option
											v-for="item in status"
											:key="item.value"
											:label="item.name"
											:value="item.value">
									</el-option>
								</el-select>
							</template>
						</el-table-column>


						<el-table-column label="物料分组"  prop="name"></el-table-column>
						<el-table-column label="销售单位"  prop="name"></el-table-column>

					</el-table>
				</el-form>
			</div>
		</div>

	</div>
</template>
<script>
	

	export default {//需要类型和相关的id
	    props:['params'],
		data(){
            var self = this;
			return {
				goodsList:[],//物料列表
                priceType:[
                    {
                        name:'目录价格',
                        value:'CATALOG_LIST'
                    },
                    {
                        name:'标准价格',
                        value:'STANDARD_LIST'
                    },
                    {
                        name:'活动价格',
                        value:'PREFERENTIAL_LIST'
					},
					{
                        name:'经销结算价格',
                        value:'SETTLE_LIST'
                    }
                ],
				status:[
					{
					    name:'是',
						value:'1'
					},
					{
					    name:'否',
						value:'0'
					}
				],
                pickerOptions:{//禁用日期
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    }
                }



			}
		},
		methods:{
            confirmAddGoods(){

                return;
				var list = this.goodsList;
				var i = list.length - 1;
				for(;i >=0;i--){
				    let currentData = list[i],
						enableTime = currentData.enableTime,
						disableTime = currentData.disableTime;
				    let isNotPass = !enableTime || (disableTime && new Date(disableTime).valueOf() < new Date(enableTime).valueOf());
				    if(isNotPass){
				        this.$message({
				            message:!enableTime?'请填写有效时间':'失效时间不能小于有效时间',
							type:'error'
						});
				        return;
					}
				}
				var url='',
					data = '',
					_this = this;
                this.ajax.postStream(url,data,function(d){
                    var data = d.body;
                    _this.$message({
                        message:data.msg,
                        type:data.result?'success':'error'
                    });
                    if(!data.result) return;
                    _this.$root.eventHandle.$emit('removeAlert',_this.params.alertId);
                    _this.$root.eventHandle.$emit('updataPriceList');

				},function(e){
                    console.log(e)
				})
			},
            changeType(data){
              //类型改变
				if('CATALOG_LIST' == data.type){
				    data.status='0';
				    data.range = '1'//如果是目录列表的话，那店铺引响范围必定是全局
				}
			},


			initData(){
			  //初使化数据
				var data = this.params || {};
				var list = data.list;
				var i = list.length - 1;
				for(;i>=0;i--){
				    var currentData = list[i];
				    currentData.enableTime = '';
                    currentData.disableTime = '';
                    currentData.type == 'CATALOG_LIST'?currentData.status = '0':currentData.status = '1';
				}
				this.goodsList = list;

			}




		},

		mounted(){
			var _this = this;
			_this.initData();

		}



	}
</script>
<style type="text/css" scoped>
	.el-date-editor.el-input,.el-input{width:auto}
</style>