<!--新增或是修改价格详情-->
<template>
  <div class='xpt-flex'>
    <el-row class='xpt-top' v-if="isCanSave">
      <el-button type='info' size='mini' @click="submitPriceInfo('price')">确认</el-button>
    </el-row>

    <el-form label-position="right" label-width="90px" :model="catelog" :rules="rules" ref="catelog"
      style="margin-top:10px">
      <el-row :gutter='40'>
        <el-col :span='8'>
          <el-form-item label="价目表编码">
            <el-input v-model="catelog.price_list_code" size='mini' disabled></el-input>

          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="状态">
            <el-input v-model="catelog.status_name" size='mini' disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="生效时间">
            <el-date-picker v-model="catelog.enable_date" type="datetime" placeholder="选择日期" size='mini' disabled>
            </el-date-picker>

          </el-form-item>
        </el-col>

      </el-row>
      <el-row :gutter='40'>
        <el-col :span='8'>
          <el-form-item label="价目表名称">
            <el-input v-model="catelog.price_list_name" size='mini' disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="价目表类型">
            <el-select v-model="catelog.price_list_type" size='mini' placeholder="请选择" disabled>
              <el-option v-for="item in priceType" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>

          </el-form-item>
        </el-col>
        <el-col :span='8'>
          <el-form-item label="失效时间">
            <el-date-picker v-model="catelog.disable_date" type="datetime" placeholder="选择日期" size='mini' disabled>
            </el-date-picker>

          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
    <el-form label-position="right" label-width="90px" :model="price" :rules="rules" ref="price">
      <el-row :gutter='40'>
        <el-col :span='24'>
          <el-form-item label="物料" prop="material_code">
            <el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="price.material_code" v-if="isEdit"
              disabled>
            </el-input>
            <el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="price.material_code"
              :on-icon-click="searchCondition" v-else @blur='materialInput'>
            </el-input>

            <el-tooltip v-if='rules.material_code[0].isShow' class="item" effect="dark"
              :content="rules.material_code[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
            <el-input v-model="price.material_id" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-row>
      <el-row :gutter='40'>
        <el-col :span='8'>
          <el-form-item label="物料名称">
            <el-input v-model="price.material_name" size='mini' disabled></el-input>

          </el-form-item>
        </el-col>
        <el-col :span='16'>
          <el-form-item label="规格描述">
            <el-input v-model="price.material_specification" size='mini' disabled></el-input>

          </el-form-item>
        </el-col>

      </el-row>

      <el-row :gutter='40'>
        <el-col :span='24'>
          <el-form-item label="单位">
            <el-input v-model="price.material_unit" size='mini' disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter='40'>
        <el-col :span='8'>
          <el-form-item label="价格" prop="price" :class="inputClass">
            <el-input v-model="price.price" size='mini' v-if="!isCanEdit" disabled type="number"></el-input>
            <el-input v-model="price.price" size='mini' v-else type="number" :maxlength="17"></el-input>
            <el-tooltip v-if='rules.price[0].isShow' class="item" effect="dark" :contentp="rules.price[0].message"
              placement="right-start" popper-class='xpt-form__error' style="position:absolute;right: -40px;top: 8px;">
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span='16'>
          <el-form-item label="最低价格" prop="lowest_price" v-if="catelog.price_list_type == 'CATALOG_LIST'">
            <el-input v-model="price.lowest_price" size='mini' v-if="!isCanEdit" disabled></el-input>
            <el-input v-model="price.lowest_price" size='mini' v-else :maxlength="17" type="number"></el-input>
            <el-tooltip v-if='rules.lowest_price[0].isShow' class="item" effect="dark"
              :content="rules.lowest_price[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='40' v-if="catelog.price_list_type=='DIRECT_DESCENT_LIST'">
       <el-col :span='8'>
          <el-form-item label="商品ID" prop="goods_id" :class="inputClass">
            <el-input v-model="price.goods_id" size='mini' v-if="!isCanEdit" disabled type="number"></el-input>
            <el-input v-model="price.goods_id" size='mini' v-else type="number" :maxlength="17"></el-input>
            <el-tooltip v-if='rules.goods_id[0].isShow' class="item" effect="dark" :content="rules.goods_id[0].message"
              placement="right-start" popper-class='xpt-form__error' style="position:absolute;right: -40px;top: 8px;">
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='40'>
        <el-col :span='24'>
          <el-form-item label="行影响时间">
            <el-switch v-model="if_close_effect" on-text="开" off-text="关" :disabled="!price.canEditRowEffect">
            </el-switch>
          </el-form-item>
        </el-col>

        <el-col :span='24'>
          <el-form-item label="生效时间" prop="enable_date">
            <el-date-picker v-model="price.enable_date" type="datetime" placeholder="选择日期" size='mini'
              disabled></el-date-picker>
            <!-- <el-date-picker v-model="price.enable_date" type="datetime" placeholder="选择日期" size='mini' v-else
              :picker-options="pickerOptions" :editable="false"></el-date-picker> -->
            <el-tooltip v-if='rules.enable_date[0].isShow' class="item" effect="dark"
              :content="rules.enable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='40'>
        <el-col :span='24'>
          <el-form-item label="失效时间" prop="disable_date">
            <!-- <el-date-picker v-model="price.disable_date" type="datetime" placeholder="选择日期" size='mini'
              v-if="isCanEditDisabled && !isEdit" :picker-options="pickerOptions2" :editable="false"></el-date-picker> -->
            <el-date-picker v-model="price.disable_date" type="datetime" placeholder="选择日期" size='mini' disabled>
            </el-date-picker>
            <el-tooltip v-if='rules.disable_date[0].isShow' class="item" effect="dark"
              :content="rules.disable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
              <i class='el-icon-warning'></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='40'>
        <el-col :span='24'>
          <el-form-item label="行状态">
            <el-select v-model="price.disabled_status" size='mini' placeholder="请选择" disabled>
              <el-option v-for="item in lineStatus" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
            <!-- querylect  v-model="price.disabled_status"   size='mini' placeholder="请选择"  v-else>
							<el-option
									v-for="item in lineStatus"
									:key="item.value"
									:label="item.name"
									:value="item.value">
							</el-option>
						<querylect> -->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='40'>
        <el-col :span='24'>
          <el-form-item label="行失效人">
            <el-input v-model="price.disable_person_name" size='mini' disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='40'>
        <el-col :span='24'>
          <el-form-item label="行失效时间">
            <el-input v-model="price.disable_time" size='mini' disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter='40'>
        <el-col :span='16'>
          <el-form-item label="说明" style="height:60px">
            <el-input type="textarea" v-model="price.remark" :maxlength="240" v-if="!isCanEdit" disabled></el-input>
            <el-input type="textarea" v-model="price.remark" :maxlength="240" v-else></el-input>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>


  </div>
</template>
<script>
  import validate from '@common/validate.js';
  import fn from '@common/Fn.js';
  export default {
    props: ['params'],
    data() {
      var self = this;
      return {
        price: {
          price: '',
          lowest_price: '', //最低价格
          enable_date: '', //价格生效时间
          disable_date: '', //价格的失效时间
          disabled_status: 0,
          disable_person_name: '',
          disable_time: '',
          remark: '',
          if_close_effect: 'Y',
          /*row_effect_time:true,
          effect_time:'Y',
          canEditRowEffect:true,*/
          canEditRowEffect: true,

          material_code: '',
          material_name: '', //物料名称
          material_id: '', //物料ID
          material_specification: '',
          material_unit: '', //单位
          goods_id:null,//商品id

        },
        original_data: '', //从价格那边传过来的数据
        catelog: { //表头信息
          price_list_type: 'CATALOG_LIST', //根据价目表的类型来判断所属的价格体系，不管新增还是编辑，都得把type类型带过来
          enable_date: '', //表头生效时间
          disable_date: '', //表头失效时间
          price_list_code: '',
          status: '',
          status_name: '',
          price_list_name: '',
          price_list_id: '' //价目表id
        },
        original_disable_data: '', //价格详情的失效时间
        priceType: [{
            name: '目录价格',
            value: 'CATALOG_LIST'
          },
          {
            name: '标准价格',
            value: 'STANDARD_LIST'
          },
          {
            name: '活动价格',
            value: 'PREFERENTIAL_LIST'
          },
          {
            name: '经销结算价格',
            value: 'SETTLE_LIST'
          },
          {
            name: '摆场价格',
            value: 'EXHIBITION_LIST'
          },
          {
            name:'直降价目',
						value: 'DIRECT_DESCENT_LIST'
          },
          {
            name:'滞销品价目',
            value:'UNSALABLE_GOODS_LIST'
          },
          {
            name:'参考零售价格',
            value:'REFERENCE_RETAIL_LIST'
          },
          {
            name: '整装项目价格',
            value: 'PACKAGED_ITEMS_LIST'
          }
        ],
        lineStatus: [{
            name: '生效',
            value: 0
          },
          {
            name: '失效',
            value: 1
          }
        ],

        isEdit: true, //区分编辑还是新增
        //isCanSave:true,//是否有保存按钮
        //isCanEdit:true,
        isCanEdit: true, //除了“创建、重新审核、已撤回”这些外，是不能编辑的
        isCanSave: true, //整个价目表是否有编辑的地方(审核后只能编辑失效日期)
        isCanEditDisabled: true, //是否能修改失效时间

        if_close_effect: true, //默认行影响时间为开
        //disabledDateCanEidt:true,//失效时间能否编辑

        pickerOptions: { //禁用日期
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          },
          date: (function () {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
            return new Date(time);
          })()
        },
        pickerOptions2: { //禁用日期
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          },
          date: (function () {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
            return new Date(time);
          })()
        },
        rules: {
          goods_id:validate.integer1({
            trigger: 'blur',
            self: self
          }),
          material_code: validate.isNotBlank({

            msg: '请选择物料信息',
            trigger: 'change',
            self: self
          }),
          price: validate.priceTwo({
            required: true,
            msg: '请填写物料价格并且最多只能有保存2位小数',
            self: self,
            trigger: 'change'
          }),
          lowest_price: validate.priceTwo({
            required: true,
            msg: '请填写最低价格并且最多只能有保存2位小数',
            self: self,
            trigger: 'change'
          }),
          enable_date: validate.enableTime({
            required: true,
            msg: '价格生效时间不能小于表头生效时间',
            self: self,
            trigger: 'change',
            callback: function () {
              //TODO,要去判断到底是新增还是修改

              var enable_date = self.price.enable_date;
              var enableTime = self.catelog.enable_date;
              if (!enable_date || new Date(enableTime).valueOf() > new Date(enable_date).valueOf()) return false;

              self.$refs.price.validateField('disable_date');
              return true;
            }
          }),
          disable_date: validate.disableTime({
            required: true,
            msg: '价格失效时间不能小于价格生效时间',
            self: self,
            trigger: 'change',
            callback: function () {
              // alert(1)
              var priceDisableTime = self.price.disable_date,
                priceEnableTime = self.price.enable_date,
                disableTime = self.catelog.disable_date,
                originalDisableTime = self.original_disable_data;
              //失效时间不能是过去时间
              /*if(priceDisableTime && new Date(priceDisableTime).getTime() <= new Date().getTime()){
								return {
                                    msg:'价格失效时间不能小于当前时间',
                                    bool:false
                                }
							}	*/

              //应该先于自身作比较-本身生效-价目失效(只有在已审核的状态下进行自身的比较)
              if (self.catelog.status == 'APPROVED') {
                if ((originalDisableTime && !priceDisableTime) ||
                  (originalDisableTime && priceDisableTime && new Date(originalDisableTime).valueOf() < new Date(
                    priceDisableTime).valueOf())) {
                  return {
                    msg: '价格失效时间不能大于原失效时间',
                    bool: false
                  }
                }
              }

              if (priceEnableTime && priceDisableTime && new Date(priceDisableTime).valueOf() < new Date(
                  priceEnableTime).valueOf()) {
                return {
                  msg: '价格失效时间不能小于价格生效时间',
                  bool: false
                }
              }

              if ((disableTime && !priceDisableTime) || (disableTime && priceDisableTime && new Date(disableTime)
                  .valueOf() < new Date(priceDisableTime).valueOf())) { //与价目表的时间做比较
                return {
                  msg: '价格失效时间不能大于表头的失效时间',
                  bool: false
                }
              }
              if (priceDisableTime == '') { //与价目表的时间做比较
                return {
                  msg: '价格失效时间不能为空',
                  bool: false
                }
              }
              return {
                bool: true
              }
            }
          })
        }


      }
    },
    methods: {
      submitPriceInfo(formName) {
        this.$refs[formName].validate((valid) => {
          if (!valid) return;
          this.requestSubmitPriceInfo()
        })
      },
      closeCurrentComponent() {
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      },
      requestSubmitPriceInfo() {
        //提交价格数据
        this.catelog.price_list_type != 'CATALOG_LIST' ? delete this.price.lowest_price : ''; //如果不是木目录的话，则不需要最低价
        this.price.if_close_effect = this.if_close_effect ? 'Y' : 'N';

        this.isEmptyObject(this.original_data) ? this.setVal() : '';
        //this.setVal();

        this.params.callback({
          data: this.original_data,
          modifyData: this.price
        });
        this.closeCurrentComponent();

      },
      searchCondition() {
        //添加物料选择框
        var _this = this;
        var data = {};
        data.callback = (d) => {
          var currentData = d.data;
          _this.price.material_name = currentData.material_name;
          _this.price.material_code = currentData.material_number;
          _this.price.material_id = currentData.material_id;
          _this.price.material_specification = currentData.material_specification;
          _this.price.material_unit = currentData.material_unit;
        }
        data.price_list_type = _this.catelog.price_list_type
        this.$root.eventHandle.$emit('alert', {
          component: () => import('@components/goodsprice/goodslist.vue'),
          style: 'width:960px;height:600px',
          title: '物料列表',
          params: data //传参过去
        });

      },

      /**
       *把当前编辑的值赋值到原对像里面去
       */
      setVal() {
        var original_data = this.original_data;
        var current_data = this.price;
        for (var key in current_data) {
          original_data[key] = current_data[key];
        }
      },

      isEmptyObject(data) {
        for (var key in data) {
          return false;
        }
        return true;
      },
      initData() {
        //初使化数据
        var data = this.params || {};
        var catelog = data.catelog || {};
        var detail = data.detail || {};
        var status = catelog.status;
        console.log('detail', detail);
        var isDiabled = false;
        for (var key in this.catelog) {
          this.catelog[key] = catelog[key];
        }
        if (!this.isEmptyObject(detail)) { //价格明细
          for (var key in this.price) {
            this.price[key] = detail[key];
          }
          isDiabled = detail.disabled_status == 0 ? false : true;
          if (this.price.disable_time) {
            this.price.disable_time = fn.dateFormat(this.price.disable_time, 'yyyy-MM-dd hh:mm:ss');
          }
        }


        this.original_data = detail; //储存传过来的数据

        this.isEdit = this.price.material_id ? !0 : !1;
        this.isEdit ? '' : this.price.enable_date = this.catelog.enable_date; //如果是新增的话，则明细的生效时间默认值为价目表的生效时间
        this.isEdit ? '' : this.price.disable_date = this.catelog.disable_date; //如果是新增的话，则明细的失效时间默认为价目表的失效时间
        this.original_disable_data = this.isEdit ? data.originalDisableData : ''; //价格详情的原失效时间
        this.if_close_effect = this.price.if_close_effect == 'N' ? false : true;

        console.log('data', data)
        this.isCanSave = (data.isCanSave && !isDiabled); //把失效的数据除外
        this.isCanEdit = (data.isCanEdit && !isDiabled); //TODO
        this.isCanEditDisabled = isDiabled ? false : data.isCanEditDisabled && status != 'APPROVED' ? true : new Date()
          .getTime() >= new Date(this.price.disable_date).getTime() ? false : true;

        //this.disabledDateCanEidt = (catelog.price_list_type == 'PREFERENTIAL_LIST' && this.isEdit)?false:this.isCanSave;//当为活动目录的时候，失效时间不能编辑
        //console.log('this.disabledDateCanEidt',this.disabledDateCanEidt);
        //this.isCanSave = data.isCanSave;
        //this.isCanEdit = data.isCanEdit;

      },
      materialInput() {
        let num = this.price.material_code
        if (!num.trim()) return
        this.ajax.get('/material-web/api/material/getMaterialByNumber/' + num.trim(), res => {
          if (res.body.result) {
            let data = res.body.content
            if (data) {
              this.price.material_name = data.materialName;
              this.price.material_code = data.materialNumber;
              this.price.material_id = data.materialId;
              this.price.material_specification = data.materialSpecification;
              this.price.material_unit = data.materialUnit;
              return
            }
            this.$message({
              type: 'error',
              message: '没有查询到该物料信息'
            })
            this.price.material_name = '';
            this.price.material_code = '';
            this.price.material_id = '';
            this.price.material_specification = '';
            this.price.material_unit = '';
          } else {
            this.$message({
              type: 'error',
              message: res.body.msg
            })
            this.price.material_name = '';
            this.price.material_code = '';
            this.price.material_id = '';
            this.price.material_specification = '';
            this.price.material_unit = '';
          }
        })
      }

    },
    created() {
      var _this = this;
      _this.initData(); //初使化数据

    }



  }

</script>
<style type="text/css" scoped>
  .el-date-editor.el-input {
    width: 180px;
  }

  .el-picker-panel .el-input {
    width: auto;
  }

</style>
