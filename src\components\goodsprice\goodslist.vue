<!--价格目录列表--物料列表-->
<template>
  <xpt-list :data='goodsList' :btns='btns' :colData='cols' :pageTotal='count' :searchPage='search.page_name'
    selection='radio' :isNeedClickEvent='true' @row-dblclick='rowDblclick' @radio-change='radioChange'
    @search-click='searchGoodsList' @page-size-change='sizeChange' @current-page-change='pageChange'></xpt-list>
</template>
<script>
  export default { //需要类型和相关的id
    props: ['params'],
    data() {
      var self = this;
      return {
        cols: [{
          label: '物料编码',
          prop: 'material_number'
        }, {
          label: '物料名称',
          prop: 'material_name'
        }, {
          label: '规格描述',
          prop: 'material_specification'
        }, {
          label: '物料分组',
          prop: 'inventory_category'
        }, {
          label: '单位',
          prop: 'material_unit'
        }],
        btns: [{
          type: 'primary',
          txt: '确认',
          click: self.confirmAddGoods
        }],
        //priceType:'',//价目表类型
        //id:'',//价目ID
        search: {
          where: [],
          page_name: 'cloud_material',
          page: {
            length: 20,
            pageNo: 1
          }
        },
        count: 0,
        selectedData: '', //被选中的数据
        goodsList: [] //物料列表
      }
    },
    methods: {
      closeCurrentComponent() {
        this.$root.eventHandle.$emit('removeAlert', this.params.alertId);
      },
      confirmAddGoods() {
        //确认添加物料信息
        if (!this.selectedData) {
          this.$message({
            message: '请选择物料信息',
            type: 'error'
          })
          return;
        }
        //this.$root.eventHandle.$emit('getGoodsDetail',this.selectedData);
        this.closeCurrentComponent();
        this.params.callback && this.params.callback({
          data: this.selectedData
        });
      },
      handleCurrentChange(currentRow, oldCurrentRow) {
        this.selectedData = currentRow;
      },
      sizeChange(size) {
        // 每页加载数据
        this.search.page.length = size;
        this.search.page.pageNo = 1;
        this.getGoodsList();

      },
      pageChange(page_no) {
        // 页数改变
        this.search.page.pageNo = page_no;
        this.getGoodsList();
      },
      clearSelectedData() {
        this.selectedData = '';
      },
      resetSearchData(bool) { //bool,是要加载首页的数据还是加载当前页的数据，为true时，加载首页，false则为当前页面
        this.selectedData = ''; //重置数据
        bool ? this.search.page.pageNo = 1 : '';
      },
      searchGoodsList(obj, resolve) {
        //模糊搜索
        // this.resetSearchData(!0);
        this.search.where = obj
        this.getGoodsList(resolve);
      },
      initData() {
        //初使化数据
        var data = this.params || {};
      },
      getGoodsList(resolve) {
        //获取物料列表
        var _this = this;
        var searchCondition = _this.search;
				if(!!_this.params.price_list_type){
					searchCondition.price_list_type=_this.params.price_list_type
				}
        this.ajax.postStream('/price-web/api/price/getMaterialInfo', searchCondition, function (d) {
          var data = d.body;
          //console.log('data',d);
          resolve && resolve();
          if (!data.result) {
            _this.$message({
              message: data.msg,
              type: 'error'
            });
            return;
          }
          _this.goodsList = data.content.list;
          _this.count = data.content.count;
          _this.clearSelectedData();

        }, function (e) {
          resolve && resolve();
        })
      },
      rowDblclick(obj) {
        this.selectedData = obj
        this.confirmAddGoods()
      },
      radioChange(obj) {
        this.selectedData = obj
      }
    },
    mounted() {
      var _this = this;
      _this.initData();
      _this.getGoodsList();
    }
  }

</script>
