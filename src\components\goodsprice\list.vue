<!--销售价目表列表-->
<template>
    <count-list
        ref='priceList'
        :data='priceList'
        :btns='priceBtns'
        :colData='priceCols'
        :pageTotal='count'
        :searchPage='search.page_name'
        @search-click='searchPriceList'
        @selection-change='select'
        @page-size-change='sizeChange'
        @current-page-change='pageChange'
        countUrl="/price-web/api/price/getPriceListCount"
        :showCount ='showCount'
    >
        <xpt-btngroup :btngroup='addGroupList' slot="btns" class='mgl10'></xpt-btngroup>
        <xpt-btngroup :btngroup='submitGroupList' slot="btns" class='mgl10'></xpt-btngroup>
        <xpt-btngroup :btngroup='auditGroupList' slot="btns" class='mgl10'></xpt-btngroup>
        <li slot='sort' style="text-align:left;">
            <el-select size='mini' class="mgr5 w120 v-middle" v-model="search.sort_field"
                filterable placeholder="请选择排序字段">
                <el-option v-for="(val,key) in sort_field_options"
                    :key="key"
                    :label="val"
                    :value="key">
                </el-option>
            </el-select>

            <el-select size='mini' class="mgr5 w120 v-middle" v-model="search.sort_way"
                placeholder="请选择排序方式">
                <el-option label="升序" value="ASC"></el-option>
                <el-option label="降序" value="DESC"></el-option>
            </el-select>
        </li>
        <xpt-import slot='btns' :taskUrl='uploadUrl' class='mgl10'></xpt-import>
    </count-list>
</template>
<script>
import countList from '@components/common/list-count'
export default {
    components: {
        countList,
      },
    props:['params'],
	data(){
	    var _this = this;
		return {
            showCount:false,
            sort_field_options: {
                'B16A626598674AD426E4B885017906D7': '创建时间',//create_time
            },
			search:{
				page:{
                    length:_this.pageSize,
					pageNo:1
				},
                page_name: 'cloud_price_list',
                where: [],
                sort_way: '',//排序方式
                sort_field: '',//排序字段
			},
            count:1,
            uploadUrl:'/price-web/api/price/addImportPriceListInfo?permissionCode=PRICE_LIST_IMPORT',
            priceList:[],
			selectedList:{},//checked 选中的集合

            addGroupList: [
                {
                    type:'info',
                    txt:'新增目录价目',
                    click(){

                        _this.openTab(1);
                    }
                },{
                    type:'info',
                    txt:'新增标准价目',
                    click(){

                        _this.openTab(2);
                    }
                },{
                    type:'info',
                    txt:'新增活动价目',
                    click(){
                        _this.openTab(3);
                    }
                },{
                    type:'info',
                    txt:'新增经销结算价目',
                    click(){
                        _this.openTab(4);
                    }
                },
                // {
                //     type:'info',
                //     txt:'新增摆场价目',
                //     click(){
                //         _this.openTab(5);
                //     }
                // },
                {
                    type:'info',
                    txt:'新增硬装价目',
                    click(){
                        _this.openTab(6);
                    }
                },{
                    type:'info',
                    txt:'新增直降价目',
                    click(){
                        _this.openTab(7);
                    }
                },{
                    type:'info',
                    txt:'新增整装价目',
                    click(){
                        _this.openTab(8);
                    }
                },{
                    type:'info',
                    txt:'新增滞销品价目',
                    click(){
                        _this.openTab(9);
                    }
                },{
                    type:'info',
                    txt:'新增参考零售价目',
                    click(){
                        _this.openTab(10);
                    }
                }
            ],
            submitGroupList: [
                {
                    type:'info',
                    txt:'提交',
                    click(){
                        //type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回
                        _this.operateList(3,'提交');
                    }
                },{
                    type:'info',
                    txt:'撤回',
                    click(){
                        _this.operateList(5,'撤回');
                    }
                }
            ],
            auditGroupList: [
                {
                    type:'info',
                    txt:'审核',
                    click(){
                        _this.operateList(4,'撤回');
                    }
                },{
                    type:'info',
                    txt:'驳回',
                    click(){
                        _this.operateList(6,'驳回');
                    }
                }
            ],

            priceBtns: [
                {
                    type: 'primary',
                    txt: '复制',
                    click: _this.copyPriceList
                }, {
                    type: 'danger',
                    txt: '删除',
                    loading:false,
                    disabled:false,
                    click() {
                        //TODO,当按钮的txt的改变时，传过去的参也要改变
                        _this.operateList(2,'删除');
                    }
                }, {
                    type: 'info',
                    txt: '刷新',
                    loading:false,
                    disabled:false,
                    click() {
                       _this.priceBtns.map((a,b)=>{
                            //TODO
                            if(a.txt == '刷新'){
                                a.loading = true;
                                a.disabled = true;
                                return;
                            }
                       });
                       _this.getPriceList();
                    }
                },
            ],
            priceCols: [
                {
                    label: '价目表编码',
                    prop: 'price_list_code',
                    redirectClick(row) {
                        _this.intoGoodsPrice(row)
                    }
                }, {
                    label: '价目表名称',
                    prop: 'price_list_name',
                    width:300
                }, {
                    label: '价目表类型',
                    prop: 'price_list_type_name'
                }, {
                    label: '直降价目优惠类别',
                    prop: 'discount_category',
                    width: 130,
                    format: 'auxFormat',
					formatParams: 'dicount_category_match',
                }, {
                    label: '状态',
                    prop: 'status_name'
                }, {
                    label: '全局标志',
                    prop: 'store_area_name'
                }, {
                    label: '币别',
                    prop: 'currency_code',
                    format: 'auxFormat',
                    formatParams: 'currency',
                }, {
                    label: '生效日期',
                    prop: 'enable_date',
                    format: 'dataFormat1'
                }, {
                    label: '失效日期',
                    prop: 'disable_date',
                    format: 'dataFormat1'
                }, {
                    label: '影响日期起',
                    prop: 'effect_date_start',
                    format: 'dataFormat1'
                }, {
                    label: '影响日期止',
                    prop: 'effect_date_end',
                    format: 'dataFormat1'
                }, {
                    label: '说明',
                    prop: 'remark'
                }
            ]
		}
	},
	methods:{
	    /**
		 * 新建建目表
		 * **/
		openTab(type){
			var map = {
			    1:'新增目录价目',
				2:'新增标准价目',
                3:'新增活动价目',
                4:'新增经销结算价目',
                5:'新增摆场价目',
                6:'新增硬装采集价目',
                7:'新增直降价目',
                8:'新增整装价目',
                9:'新增滞销品价目',
                10:'新增参考零售价目',
			}
			var _this = this;
            _this.$root.eventHandle.$emit('creatTab',{name:map[type],params:_this.getPriceInfo(type),component:()=>import('@components/goodsprice/price')});
		},
	    getPriceInfo(type){//type--1为目录价，2为标准价，3为活动价
			if(!type) return {};
			var typeMap = {
			    1:'CATALOG_LIST',
				2:'STANDARD_LIST',
                3:'PREFERENTIAL_LIST',
                4:'SETTLE_LIST',
                5:'EXHIBITION_LIST',
                6:'HARD_DECORATION_LIST',
                7:'DIRECT_DESCENT_LIST',
                8:'PACKAGED_ITEMS_LIST',
                9:'UNSALABLE_GOODS_LIST',
                10:'REFERENCE_RETAIL_LIST',
			}
			return {
			    type:typeMap[type]
			}
		},
        intoGoodsPrice(data){
            //编辑商品价格详情
			var params = {
			    type:data.price_list_type,
				id:data.price_list_id
			}
			this.$root.eventHandle.$emit('creatTab',{name:'编辑价格详情',params:params,component:()=>import('@components/goodsprice/price')})
        },
        sizeChange(size){
            // 每页加载数据
            var searchData = this.search;
            searchData.page.length = size;
            searchData.page.pageNo = 1;
            this.getPriceList();
        },
        pageChange(page_no){
            // 页数改变
            var searchData = this.search;
            searchData.page.pageNo = page_no;
            this.getPriceList();
        },
        searchPriceList(obj, resolve){
            this.search.where = obj
            new Promise((res,rej)=>{
            // this.getList(resolve,res);
			this.getPriceList(resolve,res);
          }).then(()=>{
            if(this.search.page.pageNo != 1){
              this.count = 0;
            }
            this.showCount = false;
          })
			//模糊查找
			// this.getPriceList(resolve);
		},
		isEmptyObject(data){
          for(var key in data){
              return false;
		  }
		  return true;
		},
		getSelectedData2(){
            var data = this.selectedList;
            if(this.isEmptyObject(data)) return {};
            var array = [];
            for(var key in data){
                array.push(data[key]);
			}
			return array;
		},
		getSelectedData(statusMap){//根据type类型去判断是否存在某种状态
            var data = this.selectedList;
            if(this.isEmptyObject(data)) return {};
            var array = [];
            statusMap = statusMap?statusMap:{};
            for(var key in data){
                var currentData = data[key];
                if(!statusMap.hasOwnProperty(currentData.status)){//如果状态不在符合条件之内，则返回错误的验证
                    return {
                        bool:false,
                        data:[]
                    }
				}
                array.push(key);
			}
			return {
                bool:true,
				data:array
			}
		},
        select(selection){//单个选择
			var i = selection.length;
            this.selectedList = {};
            if(!i) return;
            var a = 0;
            for(;a < i;a++){
                var currentData = selection[a];
                this.selectedList[currentData.price_list_id] = currentData;
			}
        },
		submitInfo(data){
            if(!data.url) return;
            var _this = this;
            this.ajax.postStream(data.url,data.data||{},function(d){
                var data = d.body;
                _this.$message({
                    message:data.msg,
                    type:data.result?'success':'error'
                });
                if(data.result){
                    _this.getPriceList();
                }
                data.callback && data.callback(d);
            });
		},
		matchValidateCondition(type){
            //type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回
			var data = {
			    2:{
                    CREATE:true,//创建
                    RETRIAL:true,//重新审核
                	WITHDRAWED:true//已撤回
				},
				3:{
                    CREATE:true,//创建
                    RETRIAL:true,//重新审核
                    WITHDRAWED:true//已撤回
				},
				4:{
                    SUBMITTED:true//提交审核
				},
				5:{
                    SUBMITTED:true//提交审核
				},
				6:{
                    SUBMITTED:true//提交审核
				}
			}
			return data[type];
		},
		/**
		 * 匹配错误信息
		 * **/
		getMathErrorMsg(type){
            //type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回
            var msg = {
                2:'只有创建、重新审核、已撤回这些状态可以删除',
				3:'只有创建、重新审核、已撤回这些状态可以提交',
				4:'只有已提交状态可以审核',
				5:'只有已提交状态可以撤回',
				6:'只有已提交状态可以驳回'
            }
            return msg[type];
		},
        /**
        *质灰当前按钮的操作属性
        *{text} 用按钮文字进行判断
        **/
        setCurrentBtnDisabled(text){
            console.log(8888);
            this.priceBtns.map((a,b)=>{
                let data = a.txt?a.txt:a.btnGroupList;
                let  bool = false;
                if(typeof data == 'string'){
                    if(a.txt == text){
                        a.disabled = true;
                        a.loading = true;
                        return;
                    }
                }else if(typeof data == 'object'){
                    data.map((c,d)=>{
                        if(c.txt == text){
                            c.disabled = true;
                            c.loading = true;
                            bool = true;
                            return;
                        }
                    });
                }
                if(bool) return;
            });
        },
		/***
		 * 操作列表数据
		 * {type} type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回
         *｛txt｝当前按钮上的文字
		 * */
        operateList(type,txt){
            var condition = this.matchValidateCondition(type);
            var data = this.getSelectedData(condition);
			var bool = data.bool;
            var getList = data.data || [];
            var i = getList.length;
            if(!bool || !i){
                this.$message({
                    type:'error',
                    message:!this.isEmptyObject(data)?this.getMathErrorMsg(type):'请选择列表'
                });
                return;
            }
            //只允许单个提交
            if(type == 3 && i != 1){
                this.$message.error('只允许单个提交');
                return;
            }
            this.setCurrentBtnDisabled(txt);
            var url = '';
            if(2 == type){
                url = '/price-web/api/price/deletePriceListAllInfo?permissionCode=PRICE_LIST_DELETE';
			}else if(3 == type){
                url = '/price-web/api/price/commitPriceListAllInfo?permissionCode=PRICE_LIST_SUBMIT';
			}else if(4 == type){
                url = '/price-web/api/price/auditPriceList?permissionCode=PRICE_LIST_AUDIT';
            }else if(5 == type){
                url = '/price-web/api/price/withdrawPriceList?permissionCode=PRICE_LIST_WITHDRAW';
            }else if(6 == type){
                url = '/price-web/api/price/rejectPriceList?permissionCode=PRICE_LIST_REJECT';
            }
            if(!url) return;
            var submitData = getList;
            if(4 == type || 5 == type || 6 == type){
                submitData = {
                    price_list_ids:getList
				}
			}
            this.submitInfo({
                url:url,
                data:submitData
            });
		},
        /**
        *重置按钮状态
        */
        resetBtnDisabled(){
            this.priceBtns.map((a,b)=>{
                a.disabled = false;
                a.loading = false;
            });
        },
		getPriceList(resolve,callback){
			var _this = this;
			var data = _this.search;
			this.ajax.postStream('/price-web/api/price/getPriceListInfo?permissionCode=PRICE_LIST_QUERY',data,function(d){
			    var data = d.body;
                resolve && resolve();
                callback && callback();
                _this.resetBtnDisabled();

			    if(!d.body.result) {
                    _this.$message({
                        message:data.msg,
                        type:'error'
                    });
                    return;
                }
                _this.priceList = data.content.list || [];
			    // _this.count = data.content.count;
                if(!_this.showCount){
                   let total = _this.search.page.length * _this.search.page.pageNo
                    _this.count = d.body.content.list.length == (_this.search.page.length+1)? total+1:total;
                    _this.priceList = d.body.content.list.splice(0,d.body.content.list.length == (_this.search.page.length+1)?total:d.body.content.list.length);

                }

			    _this.clearSelectedData();
            },function(e){
			    console.log(e);
                callback && callback();
                resolve && resolve();
            })
		},

        /**
         * 请空已选的数据
         * **/
        clearSelectedData(){
            this.selectedList = {};
            this.$refs.priceList.$refs.list && this.$refs.priceList.$refs.list.clearSelection();
        },
        /**
         * 复制价目表
         * **/
        copyPriceList(){
            var _this = this;
            var getList = this.getSelectedData2();
            let i = getList.length;
            if(getList.length != 1){
                _this.$message({
                    message:i?'只能复制一条价目表信息':'请选择价目表信息',
					type:'error'
				});
                return;
			}
            this.$root.eventHandle.$emit('alert',{
                component:()=>import('@components/goodsprice/copy'), close:function(){
            },
            style:'width:1000px;height:600px',
                title:'复制价目表',
                params:{data:getList[0]}//传参过去
        	});
		},
        setNewXptSearchExReset (){
            var oldFunc = this.$refs.priceList.$refs.list.$refs.xptSearchEx.reset
            ,   self = this

            self.$refs.priceList.$refs.list.$refs.xptSearchEx.reset = function (){
                oldFunc()
                self.search.sort_field = ''
                self.search.sort_way = ''
            }
        },
	},
	mounted(){
		var _this = this;
		// _this.getPriceList();
        _this.setNewXptSearchExReset()
        _this.$root.eventHandle.$on('updataPriceList',function(){
            //_this.resetSearchData(!0);
            _this.getPriceList();
        });
	},
    destroyed(){
        // 销毁根节点的所有监听事件
        this.$root.offEvents('updataPriceList');
        console.log('isdestroyed')
    }
}
</script>
