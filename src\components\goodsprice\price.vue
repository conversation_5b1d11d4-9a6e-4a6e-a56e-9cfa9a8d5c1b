<!--目录价目表，标准价目表，活动价目表，三表于一体-->
<template>
	<div class='xpt-flex'>
		<el-row class='xpt-top'>
			<el-button type='warning'  size='mini' @click="saveEvent" :disabled="saveDisabled || price.price_list_type === 'EXHIBITION_LIST'" :loading="saveLoading" >保存</el-button>
			<!-- type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回 -->
			<el-button type='info'  size='mini' @click="batchOperationEvent(3)" :disabled="submitDisabled || price.price_list_type === 'EXHIBITION_LIST'" :loading="submitLoading">提交</el-button>
			<el-button type='info'  size='mini' @click="batchOperationEvent(5)" :disabled="recallDisabled || price.price_list_type === 'EXHIBITION_LIST' " :loading="recallLoading">撤回</el-button>
			<el-button type='info'  size='mini' @click="batchOperationEvent(4)" :disabled="auditDisabled || price.price_list_type === 'EXHIBITION_LIST'" :loading="auditLoading">审核</el-button>
			<el-button type='info'  size='mini' @click="batchOperationEvent(6)" :disabled="rejectDisabled || price.price_list_type === 'EXHIBITION_LIST'" :loading="rejectLoading">驳回</el-button>
			<el-button type='danger'  size='mini' @click="batchOperationEvent(2)" :disabled="delDisabled || price.price_list_type === 'EXHIBITION_LIST'" :loading="delLoading">删除</el-button>
			<el-button type='info'  size='mini' @click="refreshEvent" :disabled="refreshDisabled" :loading="refreshLoading">刷新</el-button>
			<el-button type='danger' size='mini' @click="confirmPriceEffect(() => batchOperationEvent(7))" :disabled="priceEffectDisabled || price.price_list_type === 'EXHIBITION_LIST'" :loading="priceEffectLoading">失效</el-button>
		</el-row>
		<el-form label-position="right" label-width="120px" :model="price" :rules="rules" ref="price">
			<el-tabs v-model="activeName"  >
				<el-tab-pane label="基本信息" name="first" class='xpt-flex'>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="价目表编码"  >
								<el-input v-model="price.price_list_code"  size='mini'   disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="店铺范围">
								<el-switch v-model="range"  on-text="全局" off-text="店铺" v-if="rangeDisabled" disabled></el-switch>
								<el-switch v-model="range"  on-text="全局" off-text="店铺" v-else></el-switch>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="状态">
								<el-input v-model="price.status_name"  size='mini' disabled></el-input>
								<!--<el-tooltip v-if='rules.status_name[0].isShow' class="item" effect="dark" :content="rules.status_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>-->
							</el-form-item>
							<el-form-item label="币别" prop='currency_code'>
								<xpt-select-aux v-model='price.currency_code' aux_name='currency' :disabled="price.status != 'CREATE'"></xpt-select-aux>
							</el-form-item>
							<el-form-item label="线下活动分类" >
							<xpt-select-aux v-model='price.offline_discount_class' aux_name='offline_discount_class'  :disabled="price.status != 'CREATE'" ></xpt-select-aux>
						</el-form-item>
							<el-form-item label="直降价目优惠类别" prop='discount_category'  v-if="price.price_list_type == 'DIRECT_DESCENT_LIST'">
								<el-select	size='mini' v-model="price.discount_category"  :disabled="!(/^(WITHDRAWED|CREATE)$/.test(price.status) && price.price_list_type == 'DIRECT_DESCENT_LIST')" >
									<el-option
										v-for="item in discountCategoryList"
										:key="item.discount_category"
										:label="discountCategoryAuxObj[item.discount_category]"
										:value="item.discount_category">
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="货款模式" prop='offline_payment_mode' v-if="price.price_list_type == 'DIRECT_DESCENT_LIST'">

								<xpt-input v-model='price.offline_payment_mode_name'  size='mini' :disabled="!/^(WITHDRAWED|CREATE)$/.test(price.status)"  icon="search" :on-icon-click="() => selectCategory('OFFLINE_PAYMENT_MODE')" readonly placeholder="请选择商品活动类目" @change='goodsChange("OFFLINE_PAYMENT_MODE")'></xpt-input>

							</el-form-item>
							<el-form-item label="网拍订单店铺" prop='wangpai_order_shop_name' v-if="price.price_list_type == 'DIRECT_DESCENT_LIST'">

								<xpt-input v-model='price.wangpai_order_shop_name'  size='mini' :disabled="!/^(WITHDRAWED|CREATE)$/.test(price.status)"  icon="search" :on-icon-click="() => selectCategory('WANGPAI_ORDER_SHOP')" readonly placeholder="请选择商品活动类目" @change='goodsChange("WANGPAI_ORDER_SHOP")'></xpt-input>

							</el-form-item>
							<el-form-item label="线下前置" prop='if_auto_add' v-if="price.price_list_type == 'DIRECT_DESCENT_LIST'">

								<el-select	size='mini' v-model="price.if_auto_add" :disabled="!(/^(WITHDRAWED|CREATE)$/.test(price.status) && price.price_list_type == 'DIRECT_DESCENT_LIST') ">
									<el-option
										key="1"
										label="是"
										value="Y">
									</el-option>
									<el-option
										key="2"
										label="否"
										value="N">
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="价目表名称" prop="price_list_name">
								<el-input v-model="price.price_list_name"  size='mini' :maxlength="50" v-if="isCanEdit"></el-input>
								<el-input v-model="price.price_list_name"  size='mini'  v-else disabled></el-input>
								<el-tooltip v-if='rules.price_list_name[0].isShow' class="item" effect="dark" :content="rules.price_list_name[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="生效时间"  prop="enable_date">
								<el-date-picker v-model="price.enable_date" type="datetime" placeholder="选择日期"
								size='mini' v-if="isEdit" disabled></el-date-picker>
								<el-date-picker v-model="price.enable_date" type="datetime" placeholder="选择日期"
								size='mini' v-else  :editable="false" :picker-options="pickerOptions"
								></el-date-picker>
								<el-tooltip v-if='rules.enable_date[0].isShow' class="item" effect="dark" :content="rules.enable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>
						<el-col :span='8' v-if="price.price_list_type == 'PREFERENTIAL_LIST'">
							<el-form-item label="影响日期起" prop="effect_date_start">
								<el-date-picker v-model="price.effect_date_start" type="datetime" placeholder="选择日期" size='mini' v-if="price.status == 'APPROVED'" :picker-options="pickerOptions3" :editable="false"></el-date-picker>
								<el-date-picker v-model="price.effect_date_start" type="datetime" placeholder="选择日期" size='mini'  v-else-if="isEdit" disabled></el-date-picker>
								<el-date-picker v-model="price.effect_date_start" type="datetime" placeholder="选择日期" size='mini' v-else :picker-options="pickerOptions" :editable="false"></el-date-picker>
								<el-tooltip v-if='rules.effect_date_start[0].isShow' class="item" effect="dark" :content="rules.effect_date_start[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>
						<!-- <el-col :span='8' v-else>
							<el-form-item label="行影响时间" >
								<el-switch v-model="ifCloseEffect"  on-text="开" off-text="关" :disabled="!isCanLoseEffect"></el-switch>
							</el-form-item>
						</el-col> -->
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="价目表类型"  prop="price_list_type">
								<el-select  v-model="price.price_list_type"  size='mini' placeholder="请选择"  disabled>
									<el-option
											v-for="item in priceType"
											:key="item.value"
											:label="item.name"
											:value="item.value">
									</el-option>
								</el-select>

							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="失效时间" prop="disable_date">
								<el-date-picker v-model="price.disable_date" type="datetime"
								placeholder="选择日期" size='mini'  :editable="false"
								:picker-options="pickerOptions2" v-if="price.status == 'APPROVED'" >
								</el-date-picker>
								<el-date-picker v-model="price.disable_date" type="datetime"
								placeholder="选择日期" size='mini'  :editable="false"
								:picker-options="pickerOptions2" v-else-if="isCanEditDisabled" >
								</el-date-picker>
								<el-date-picker v-model="price.disable_date" type="datetime"
								placeholder="选择日期" size='mini'  v-else disabled>
								</el-date-picker>
								<el-tooltip v-if='rules.disable_date[0].isShow' class="item" effect="dark" :content="rules.disable_date[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>
						<el-col :span='8' v-if="price.price_list_type == 'PREFERENTIAL_LIST'">
							<el-form-item label="影响日期止" prop="effect_date_end">
								<el-date-picker v-model="price.effect_date_end" type="datetime" placeholder="选择日期" size='mini' :editable="false"  :picker-options="pickerOptions3" v-if="price.status == 'APPROVED'"></el-date-picker>
								<el-date-picker v-model="price.effect_date_end" type="datetime" placeholder="选择日期" size='mini' :editable="false"  :picker-options="pickerOptions2" v-else-if="!isEdit"></el-date-picker>
								<el-date-picker v-model="price.effect_date_end" type="datetime" placeholder="选择日期" size='mini'  v-else disabled></el-date-picker>
								<el-tooltip v-if='rules.effect_date_end[0].isShow' class="item" effect="dark" :content="rules.effect_date_end[0].message" placement="right-start" popper-class='xpt-form__error'>
									<i class='el-icon-warning'></i>
								</el-tooltip>
							</el-form-item>
						</el-col>

					</el-row>
					<el-row :gutter='40'>
						<el-col :span='16'>
							<el-form-item label="说明" style="height:60px">
								<el-input type="textarea" v-model="price.remark" :maxlength="240" v-if="isCanEdit"></el-input>
								<el-input type="textarea" v-model="price.remark" :maxlength="240" v-else disabled></el-input>
							</el-form-item>
						</el-col>
						<!-- <el-col :span='8' v-if="price.price_list_type == 'PREFERENTIAL_LIST'">
							<el-form-item label="行影响时间" >
								<el-switch v-model="ifCloseEffect"  on-text="开" off-text="关" :disabled="!isCanLoseEffect"></el-switch>
							</el-form-item>
						</el-col> -->
					</el-row>

				</el-tab-pane>
				<el-tab-pane label="编制信息" name="second">
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="编制人" >
								<el-input v-model="price.creator_name"  size='mini' disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="创建人" >
								<el-input v-model="price.creator_name"  size='mini' disabled></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="编制日期">

								<el-input v-model="create_time_data"  size='mini' disabled ></el-input>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="创建时间" >
								<el-input v-model="create_time_data"  size='mini' disabled></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="审核人" >
								<el-input v-model="price.audit_name"  size='mini' disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="最后更新人" >
								<el-input v-model="price.modifier_name"  size='mini' disabled></el-input>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter='40'>
						<el-col :span='8'>
							<el-form-item label="审核日期" >
								<el-input v-model="audit_time_data"  size='mini' disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span='8'>
							<el-form-item label="最后更新时间" >
								<el-input v-model="modify_time_data"  size='mini' disabled></el-input>
							</el-form-item>
						</el-col>
					</el-row>

				</el-tab-pane>
			</el-tabs>
		</el-form>

		<div class='xpt-flex__bottom'>
			<el-tabs v-model="listActiveName"  >
				<el-tab-pane label="价格明细" name="first" class='xpt-flex'>
					<el-row class='xpt-top'>
						<el-col :span='18' >
							<div >
								<el-button type='info' size='mini' @click="addPrice()"  :disabled="!isCanEdit">添加价格</el-button>
								<el-button type='danger' size='mini' @click="removeOrDisabled(1)" :disabled="!isCanEdit" >删除</el-button>
								<el-button type='warning' size='mini' @click="removeOrDisabled(0)" :disabled="saveDisabled" >失效</el-button>
								<el-switch   on-text="行影响时间-开" v-model="rowEffectTime" off-text="行影响时间-关" :disabled="saveDisabled" :width="120"></el-switch>

								<!-- <span v-if="!isCanSave">&nbsp;</span> -->
							</div>
							<!-- <div v-else>&nbsp;</div> -->

						</el-col>
						<el-col :span='6' class='xpt-align__right' v-if="isEdit">
							<el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="goodsSearch.key" :on-icon-click="searchPriceList"></el-input>
						</el-col>
					</el-row>
					<div class='xpt-flex__bottom scroll'>
						<el-table :data="goodsList" width='100%' border style="width: 100%;"  @selection-change="select"  ref="goodsList">
							<el-table-column  type="selection" width="55" align="center" ></el-table-column>
							<el-table-column  label="物料" >
								<template slot-scope="scope"><a href="javascript:;" title="点击查看物料详情" @click="addPrice(scope.row)">{{scope.row.material_code}}</a></template>
							</el-table-column>
							<el-table-column label="物料名称"  prop="material_name"></el-table-column>
							<el-table-column label="规格描述"  prop="material_specification"></el-table-column>
							<el-table-column label="单位"  prop="material_unit"></el-table-column>
							<el-table-column label="价格"  prop="price" align="right"></el-table-column>
							<el-table-column v-if="price.price_list_type==='DIRECT_DESCENT_LIST'" label="商品ID"  prop="goods_id" align="right"></el-table-column>
							<el-table-column label="是否关闭影响时间" width="200">
								<template slot-scope="scope">
									<!-- <el-switch v-model="scope.row.row_effect_time"  on-text="开" off-text="关" :disabled="!scope.row.canEditRowEffect"></el-switch> -->
									{{scope.row.if_close_effect == 'Y'?'否':'是'}}
								</template>
							</el-table-column>
							<el-table-column label="生效时间">
								<template slot-scope="scope">{{scope.row.enable_date|dataFormat('yyyy-MM-dd hh:mm:ss')}}</template>
							</el-table-column>
							<el-table-column label="失效时间">
								<template slot-scope="scope">{{scope.row.disable_date|dataFormat('yyyy-MM-dd hh:mm:ss')}}</template>
							</el-table-column>
							<el-table-column label="行状态">
								<template slot-scope="scope">{{scope.row.disabled_status == 1?'失效':'生效'}}</template>
							</el-table-column>
							<el-table-column label="行失效人" prop="disable_person_name"></el-table-column>
							<el-table-column label="行失效时间"  prop="disable_time">
								<template slot-scope="scope">{{scope.row.disable_time|dataFormat('yyyy-MM-dd hh:mm:ss')}}</template>
							</el-table-column>
							<el-table-column label="活动识别码"  prop="activity_recognition_code">
								<template slot-scope="scope">
									<el-input
										size='mini'
										v-model="scope.row.activity_recognition_code"
										style="width:100%;"
										:disabled="(/^(SUBMITTED|APPROVED)$/).test(price.status)"
									></el-input>
								</template>
							</el-table-column>
							<el-table-column label="活动类型"  prop="activity_type">
								<template slot-scope="scope">
									<xpt-select-aux
										v-model='scope.row.activity_type'
										style="width:100%;"
										aux_name='promotiomType'
										:disabled="(/^(SUBMITTED|APPROVED)$/).test(price.status)"
										clearable
									></xpt-select-aux>
								</template>
							</el-table-column>
						</el-table>
						<div class='xpt-pagation' v-if="isEdit">
							<el-pagination @size-change="sizeChange" @current-change="pageChange"
										   :current-page="goodsSearch.page.pageNo" :page-sizes="[10, 20, 30]" :page-size="goodsSearch.page.length"
										   layout="total, sizes, prev, pager, next, jumper" :total="goodsCount">
							</el-pagination>
						</div>
					</div>
				</el-tab-pane>
				<el-tab-pane label="实施店铺" name="second"  class='xpt-flex' >
					<el-row class='xpt-top'>
						<el-col :span='18' >
							<div >
								<el-button type='info' size='mini' @click="addShopList" :disabled=" range|| (!isCanEdit && price.status != 'APPROVED')" >添加店铺</el-button>
								<el-button type='danger' size='mini' @click="removeShop(1)"  :disabled="range|| (!isCanEdit && price.status != 'APPROVED')" >删除</el-button>
								<el-button type='warning' size='mini' @click="disabledShop(0)"  :disabled="saveDisabled || range" >失效</el-button>
								<!-- <span v-if="!isCanSave">&nbsp;</span> -->
							</div>

						</el-col>
						<el-col :span='6' class='xpt-align__right' v-if="isEdit">
							<el-input placeholder="请输入查询条件" icon="search" size='mini' v-model="shopSearch.key" :on-icon-click="searchShopList"></el-input>
						</el-col>
					</el-row>
					<div class='xpt-flex__bottom'>
						<el-table :data="shopList" border style="width: 100%"  @selection-change="select"  ref="shopList">
							<el-table-column  type="selection" width="55" align="center" ></el-table-column>
							<el-table-column prop="shop_code" label="店铺编码"></el-table-column>
							<el-table-column prop="shop_name" label="店铺"></el-table-column>
							<el-table-column prop="shop_group" label="店铺分组"></el-table-column>
							<el-table-column prop="shop_type_name" label="店铺分类"></el-table-column>
							<el-table-column  label="所属公司" prop="belong_company"></el-table-column>
							<el-table-column  label="生效">
								<template slot-scope="scope">{{scope.row.disabled_status == 1?'失效':'生效'}}</template>
							</el-table-column>
							<el-table-column prop="disable_time" label="失效时间" >
								<template slot-scope='scope'>{{scope.row.disable_time|dataFormat('yyyy-MM-dd hh:mm:ss')}}</template>
							</el-table-column>
							<el-table-column prop="disable_person_name" label="失效人"></el-table-column>
						</el-table>
						<div class='xpt-pagation' v-if="isEdit">
							<el-pagination @size-change="sizeChange" @current-change="pageChange"
										   :current-page="shopSearch.page.pageNo" :page-sizes="[10, 20, 30]" :page-size="shopSearch.page.length"
										   layout="total, sizes, prev, pager, next, jumper" :total="shopCount">
							</el-pagination>
						</div>
					</div>
				</el-tab-pane>
			</el-tabs>
		</div>

	</div>
</template>
<script>

    import validate from '@common/validate.js';
    import fn from '@common/Fn.js';
	export default {
	    props:['params'],
		data(){
            var self = this;
			return {
                price:{
                    price_list_type:'',//根据价目表的类型来判断所属的价格体系，不管新增还是编辑，都得把type类型带过来
                    store_area:1,//店铺范围
					currency_code:'CNY',//币别
					offline_discount_class:'',
                    //if_close_effect:'Y',//行影响时间,默认打开
                    price_list_code:'',
                    price_list_name:'',
                    status:'',
                    status_name:'',
                    enable_date:'',//生效日期
                    disable_date:'',//失效日期
                    effect_date_start:'',//影响日期起
                    effect_date_end:'',//影响日期止
                    price_list_id:'',//价目表id
					original_disable_date:'',//储存原失效时间

                    audit_name:'',
                    modifier:'',
                    audit_time:'',
                    modify_time:'',
                    modifier_name:'',
                    create_time:'',
                    creator_name:'',
                    creator:'',
                    audit:'',
                    remark:'',
					if_auto_add:"N",
					discount_category: '',
					offline_payment_mode:'EMPTY',
					offline_payment_mode_name:'不限',
					wangpai_order_shop_name:''
				},
				discountCategoryList: [],
				discountCategoryAuxObj: __AUX.get('dicount_category_match').reduce((pre, cur, idx, arr) => {
					pre[cur.code] = cur.name
					return pre
				},{}),
				modify_time_data:'',
                audit_time_data:'',
                create_time_data:'',
				activeName:'first',//基本信息与编制信息
				listActiveName:'first',//价格明细.实施店铺
				tip:'adfasdf',//编码提示
				payment_mode:[],
				priceType:[
					{
					    name:'目录价格',
						value:'CATALOG_LIST'
					},
                    {
                        name:'标准价格',
                        value:'STANDARD_LIST'
                    },
                    {
                        name:'活动价格',
                        value:'PREFERENTIAL_LIST'
                    }, {
                        name:'经销结算价格',
                        value:'SETTLE_LIST'
                    }, {
                        name:'摆场价格',
                        value:'EXHIBITION_LIST'
                    }, {
                        name:'硬装采集价格',
                        value:'HARD_DECORATION_LIST'
                    }, {
						name:'直降价目',
						value: 'DIRECT_DESCENT_LIST'
					},{
                        name:'新增整装价目',
                        value: 'PACKAGED_ITEMS_LIST'
                    },{
                        name:'滞销品价目',
                        value: 'UNSALABLE_GOODS_LIST'
                    },{
                        name:'参考零售价格',
                        value: 'REFERENCE_RETAIL_LIST'
                    }
				],


				//按钮的可操作性
				saveDisabled:false,
				saveLoading:false,
				submitDisabled:true,
				submitLoading:false,
				recallDisabled:true,
				recallLoading:false,
				auditDisabled:true,
				auditLoading:false,
				rejectDisabled:true,
				rejectLoading:false,
				delDisabled:true,
				delLoading:false,
				refreshDisabled:true,
				priceEffectDisabled:true,
				refreshLoading:false,
				priceEffectLoading:false,

                selectedGoods:{},
				selectedShop:{},
				deleteGoodsMap:{},//删除的数据
                deleteShopMap:{},//删除的数据
                goodsList:[],//物料列表
                shopList:[],//店铺列表
				isEdit:true,//区分编辑还是新增
                isCanEdit:true,//除了“创建、重新审核、已撤回”这些外，是不能编辑的
                isCanSave:true,//整个价目表是否有编辑的地方(审核后只能编辑失效日期)


                isCanEditDisabled:true,//失效时间是否可修改
                //isRefreshPage:false,//新建的时候是否要重新拿数据


                range:true,
				rangeDisabled:true,//引响范围是否可修改



				//ifCloseEffect:true,//行影响时间
				//isCanLoseEffect:true,//失效按钮是否可点击
				rowEffectTime:true,//行影响时间
				//ifCloseEffectDisabled:true,//
				goodsSearch:{
                    key:'',
					page:{
                        length:20,
						pageNo:1
					}
					/*count:0*/
				},
				shopSearch:{
                    key:'',
					page:{
                        length:20,
						pageNo:1
					}/*,
					count:0*/
				},
				goodsCount:0,
				shopCount:0,

				goodsIndex:0,
				shopIndex:0,

                price_code:'',

				pickerOptions:{//禁用日期
                    disabledDate(time) {
                    	return time.getTime() < Date.now() - 8.64e7;
                    },
                    date:(function(){
                    	var date = new Date();
                    	var year = date.getFullYear();
                    	var month = date.getMonth()+1;
                    	var day = date.getDate();
                    	var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
                    	return new Date(time);
                    })()
                },

                pickerOptions3:{//禁用日期
                    date:(function(){
                    	var date = new Date();
                    	var year = date.getFullYear();
                    	var month = date.getMonth()+1;
                    	var day = date.getDate();
                    	var time = year + '-' + month + '-' + day + ' ' + '00:00:00';
                    	return new Date(time);
                    })()
                },

                pickerOptions2:{//禁用日期追责
                    disabledDate(time) {
                    	return time.getTime() < Date.now() - 8.64e7;
                    },

                    date:(function(){
                    	var date = new Date();
                    	var year = date.getFullYear();
                    	var month = date.getMonth()+1;
                    	var day = date.getDate();
                    	var time = year + '-' + month + '-' + day + ' ' + '23:59:59';
                    	return new Date(time);
                    })()

                },

				originalPriceData:'',
				originalGoodsList:'',
				originalShopList:'',

                //submit:[],
                //audit:[],

                //isDisableBtn:false,//按钮是否要置灰

                rules:{//所有的验证规则都放在这里面
                    /*price_list_code:validate.isNotBlank({//编码不能为空
						msg:'请输入价目表编码',
                        self:self
                    }),*/

                    price_list_name:validate.isNotBlank({//名称不能为空

                        msg:'请输入价目表名称',
                        self:self
                    }),
                    price_list_type:validate.isNotBlank({//价目表类型不能为空

                        msg:'请输入价目表类型',
                        self:self
                    }),
                    enable_date:validate.enableTime({//按道理来说，这个最小
                        required:true,
                        msg:'请填入生效时间',
                        self:self,
                        trigger:'change',
                        callback:function(){

                            var enable_date = self.price.enable_date;
                            var type = self.price.price_list_type;
                            if(self.isEdit) return {bool:true};
                            console.log(5555);
							if(!enable_date) return {bool:false,msg:'请填入生效时间'}


							var enableTime = new Date(enable_date).valueOf(),nowTime = new Date().valueOf();
							if(type == 'PREFERENTIAL_LIST' && enableTime < nowTime) return {bool:false,msg:'生效日期不能小于当前时间'}

                            self.$refs.price.validateField('disable_date');
							return true;
						}
					}),
                    disable_date:validate.disableTime({
                        required:this.params.type == 'PREFERENTIAL_LIST'||'STANDARD_LIST'?true:false,//活动价目，失效时间必须要填
                        msg:this.params.type == 'PREFERENTIAL_LIST'||'STANDARD_LIST'?'失效时间不能小于生效时间':'失效时间不能小于生效时间或是不填失效时间',
                        self:self,
                        trigger:'change',
                        callback:function(){
							// alert(self.price.price_list_type)
							var isRequire = self.price.price_list_type == 'PREFERENTIAL_LIST'||'STANDARD_LIST'?true:false;//根据价目表类型来判断是否为必填项
							//先对空进行必要与不必要的判断
							var disable_date = self.price.disable_date,
                                enable_date = self.price.enable_date;
                                //original_disable_data = self.price.original_disable_date;
                            var effect_date_end = self.price.effect_date_end;
                            var effect_date_start = self.price.effect_date_start;
                            var type = self.price.price_list_type;
                            var original_disable_data = self.getOriginalPriceInfo().disable_date || null;
							var data = {
							    bool:true
							}

							//先把时间限制的去掉
							//不能小于过去时间
							/*if(new Date(self.price.disable_date).getTime() <= new Date().getTime() && self.isCanEditDisabled){
								data.msg = '更改的失效时间不能是过去时间';
								data.bool = false;
								return data;
							}*/

							//失效日期再次变更时，只能小于原失效日，本身进行比较（只有在已审核的状态下进行自身的比较）
							if(self.isEdit && self.price.status == 'APPROVED' ){
								if((original_disable_data && !disable_date)
								|| (original_disable_data && disable_date && new Date(original_disable_data).valueOf() < new Date(disable_date).valueOf())){
	                                data.bool = false;
	                                data.msg = '更改的失效时间一定要小于原有的失效时间';
	                                return data;
								}

							}


							//非活动价目表
							if(!isRequire){
								if(disable_date && enable_date &&  new Date(disable_date).valueOf() < new Date(enable_date).valueOf()){
									data.bool = false;
								    data.msg = '失效时间不能小于生效时间或是不填失效时间';
								    return data;
								}
								return data;
							}



							//活动价目表的验证
							if(!disable_date) {//为空时的判断
							    data.bool = false;
							    data.msg = '请填入失效时间';
							    return data;
							}




							//失效时间小于有效时间
							if(disable_date &&　enable_date && new Date(disable_date).valueOf() < new Date(enable_date).valueOf()){
                                data.bool = false;
                                data.msg = '失效时间不能小于生效时间';
                                return data;
							}


							if(!effect_date_end ||
								new Date(effect_date_end).valueOf() < new Date(disable_date).valueOf() ){
									if(self.price.price_list_type == 'PREFERENTIAL_LIST'){
										data.bool = false;
										data.msg = '失效时间不能大于影响日期结束时间';
										return data;
									}

							}

							if(!effect_date_start ||
								new Date(disable_date).valueOf() < new Date(effect_date_start).valueOf() ){
									if(self.price.price_list_type == 'PREFERENTIAL_LIST'){

										data.bool = false;
										data.msg = '失效时间不能小于影响日期开始时间';
										return data;
									}
							}

							data.bool = true;
							return data;
                        }
					}),
                    effect_date_start:validate.enableTime({
                        required:true,
                        msg:'影响日期开始时间不能为空',
                        self:self,
                        trigger:'change',
                        callback:function(){
                            var enable_date = self.price.enable_date;
                            var effect_date_start = self.price.effect_date_start;
                            if(!effect_date_start) return false;
							if(enable_date &&  new Date(effect_date_start).valueOf() > new Date(enable_date).valueOf() ) {
                                return {
                                    bool:false,
                                    msg:'影响日期开始时间不能大于生效时间'
								}
							}
                            self.$refs.price.validateField('disable_date');
                            return true;
                        }
                    }),
                    effect_date_end:validate.disableTime({
                    	//required:this.params.type == 'PREFERENTIAL_LIST'?true:false,//活动价目，影响日期结束时间必须要填
                    	required:true,
                       	msg:'影响日期结束时间不能小于影响日期开始时间',
						self:self,
                        trigger:'change',
						callback:function(){
                            var disable_date = self.price.disable_date;
                            var effect_date_end = self.price.effect_date_end;
                            var effect_date_start = self.price.effect_date_start;
                            var type = self.params.type;
                            if(!effect_date_end || !effect_date_start){
                            	return {
                                    bool:false,
                                    msg:'请输入影响日期结束时间'
								}
                            }

                            if(new Date(effect_date_end).valueOf() < new Date(effect_date_start).valueOf() ) {
                                return{
                                    bool:false,
                                    msg:'影响日期结束时间不能小于影响日期开始时间'
                                }
                            }

							self.$refs.price.validateField('disable_date');
                            return {
                                bool:true
							}
						}
					}),
                    status:validate.isNotBlank({
                        required:true,
						msg:'请填写状态',
						self:self
					})
				}


			}
		},
		methods:{
			goodsChange(code){
				if(!/^(WITHDRAWED|CREATE)$/.test(self.price.status) ){
					return false;
				}
				let fieldObj = new Map([
					['OFFLINE_PAYMENT_MODE', {field: 'offline_payment_mode', fieldName: 'offline_payment_mode_name'}],
					['WANGPAI_ORDER_SHOP', {field: 'wangpai_order_shop', fieldName: 'wangpai_order_shop_name'}],
				])
				// this.price[fieldObj.get(code).field] = ''
				// this.price[fieldObj.get(code).fieldName] = ''
			},
			selectCategory (code) {
				let self = this
				if(!/^(WITHDRAWED|CREATE)$/.test(self.price.status) ){
					return false;
				}
				this.$root.eventHandle.$emit('alert',{
					component:()=>import('@components/auxiliary/auxiliaryList'),
					style:'width:800px;height:500px',
					title:'辅助资料列表',
					params:{
						isAlert: true,
						selection: 'checkbox',
						categoryCode: code,
						callback(d){
							let fieldObj = new Map([
								['OFFLINE_PAYMENT_MODE', {field: 'offline_payment_mode', fieldName: 'offline_payment_mode_name'}],
								['WANGPAI_ORDER_SHOP', {field: 'wangpai_order_shop', fieldName: 'wangpai_order_shop_name'}],

							])
							let fieldCode = '', fieldCodeName = ''
							d.forEach((item,idx) => {
								// if (!(new Set(discountCategoryList).has(item.code))) {
									// if (idx == 0 && goods_discount_category != '') {
									//     goods_discount_category = goods_discount_category + ',' + item.code
									// } else if (idx == d.length -1) {
									//     goods_discount_category += item.code
									// } else {
										fieldCode += item.code + ','
										fieldCodeName += item.name + ','
									// }
								// }
							})
							if (fieldCode[fieldCode.length - 1] == ',') {
								fieldCode = fieldCode.substring(0, fieldCode.length-1)
								fieldCodeName = fieldCodeName.substring(0, fieldCodeName.length-1)
							}
							self.price[fieldObj.get(code).field] = fieldCode
							self.price[fieldObj.get(code).fieldName] = fieldCodeName
						}
					}//传参过去
				});
			},
			getDiscountCategoryList () {
				this.ajax.postStream('/price-web/api/autoActiveDiscountSetting/list',{activity_type: 'PRICE_ACTIVITY', if_optional: 'Y'},res => {
					if(res.body.result && res.body.content) {
						this.discountCategoryList = res.body.content.filter(item => {
							return item.activity_type == 'PRICE_ACTIVITY'
						})
					} else {
						this.discountCategoryList = []
						res.body.msg && this.$message.error(res.body.msg);
					}
				}, err => {
					this.discountCategoryList = []
					this.$message.error(err);
				})
			},
			confirmPriceEffect (cb){
				this.$root.eventHandle.$emit('openDialog', {
					ok: cb,
					no() {},
					okTxt: '是',
					noTxt: '否',
					txt: '确认失效该价目？',
					noCancelBtn: true,
				})
			},

			/**
			*上传附件
			*/
			selectFile(){
				let files = document.getElementById('files').files;
				if(!files.length)return;
				let name = files[0].name;

				let reg = /(.xlsx)$/;

				if(!reg.test(name)){
					this.$message({
						message:'文件格式不对，请重新选择',
						type:'error'
					});
					return;
				}

				let fr = new FileReader();
				fr.readAsDataURL(files[0]);




			},
            submitPriceInfo(callback){
                /*this.$refs[formName].validate((valid) => {
                    if(!valid) return;
                    this.savePriceInfo(callback);
                });*/
                var bool = this.validateIsPass();
                if(!bool) return;
                this.savePriceInfo(callback);
			},
			validateIsPass(){
				var bool = false;
				this.$refs['price'].validate((valid) => {
					bool = valid;
                });
                return bool;
			},
			saveEvent(){
				var isPass = this.validateIsPass();
				if(!isPass) return;
				var isChange = this.isChangeData();
				if(!isChange){
					this.$message({
						message:'没有数据修改，不需要保存哦',
						type:'error'
					});
					return;
				}
				if(this.price.price_list_type == 'DIRECT_DESCENT_LIST' && !this.price.discount_category) {
					this.$message({
						message:'价目表类型为直降价目，请填写直降价目优惠类别！',
						type:'error'
					});
					return;
				}
				this.savePriceInfo();
			},

			savePriceInfo(callback){
				//this.isDisableBtn = true;
				this.saveDisabled = true;
				this.saveLoading = true;
				var getHeaderInfo = this.getHeaderInfo();
				var goodList = this.getDetailList(1);
				var shopList = this.getDetailList(2);



				var submitData = {
                    listDetailVo : goodList,
                    //listShopVo : shopList,
                    listVo:getHeaderInfo,
                    isAdd:!this.isEdit
				}
				getHeaderInfo.store_area !=1?submitData.listShopVo = shopList:'';
				// console.log(submitData);
				// return;
				var _this = this;
				//console.log('submitData',submitData);
				//console.log('submitData.listVo.disable_date',submitData.listVo.disable_date);

				// 权限,默认为目录价目权限
				// 2018.3.30修改
				// let code = 'PRICE_LIST_CATALOGPRICE_ADD';
				// if(this.params.type === "STANDARD_LIST") {
				// 	// 标准价目
				// 	code = 'PRICE_LIST_STANDARDPRICE_ADD';
				// } else if(this.params.type === "PREFERENTIAL_LIST"){
				// 	// 活动价目
				// 	code = 'PRICE_LIST_ACTIVITYPRICE_ADD';
				// }
                this.ajax.postStream('/price-web/api/price/saveOrUpdatePriceAllInfo?permissionCode=PRICE_LIST_SAVE',submitData,function(d){
                    var data = d.body;
                    _this.$message({
                        message:data.msg,
                        type:data.result?'success':'error'
                    });

                    if(!data.result) {
                    	//_this.isDisableBtn = false;
                    	_this.setBtnDisabled();
                    	return;
                    }
                    if(!_this.isEdit){//如果是新增，则重新拿数据
                        _this.resetData(data.content);

					}
					//_this.isDisableBtn = false;
                    _this.getPriceDetail();
					_this.getGoodsList();
                    _this.getShopList();
                    _this.$root.eventHandle.$emit('updataPriceList');
                    callback && callback();

                },function(e){

                })
			},
			/**
			 * 重置数据
			 * **/
			resetData(priceListId){
			    if(this.isEdit || !priceListId) return;
			    this.isEdit = true;
                this.price.price_list_id = priceListId;
                //this.isRefreshPage = true;
                this.rangeDisabled = true;
                var _this = this;
                let typeObj = {
                    'CATALOG_LIST':'编辑目录价目',
                    'STANDARD_LIST':'编辑标准价目',
                    'PREFERENTIAL_LIST':'编辑活动价目',
                    'SETTLE_LIST':'编辑经销结算价目',
                    'EXHIBITION_LIST':'编辑摆场结算价目',
                    'HARD_DECORATION_LIST':'编辑硬装采集价目',
                    'DIRECT_DESCENT_LIST':'编辑直降价目',
                    'PACKAGED_ITEMS_LIST':'编辑整装项目价格',
                    'UNSALABLE_GOODS_LIST':'编辑滞销品价格',
                    'REFERENCE_RETAIL_LIST':'编辑参考零售价目',
                }
                let title = typeObj[this.price.price_list_type];
                _this.$root.eventHandle.$emit('updateTab',{name:_this.params.tabName,title:title});
			},
			/***
			 * 获取价目表头信息
			 * **/
			getHeaderInfo(){

			    //var data = this.price;
			    var data = Object.assign({},this.price);
			    delete data.original_disable_date;
			    delete data.offline_payment_mode_name;
			    delete data.wangpai_order_shop;

			    data.store_area = this.range?1:0;
			    //data.if_close_effect = this.ifCloseEffect?'Y':'N';
			    console.log('before',data);
			    if(data.disable_date){
			        data.disable_date = new Date(data.disable_date).getTime();
				}else{
					data.disable_date = null;
				}

                if(data.enable_date ){
                    data.enable_date = new Date(data.enable_date).getTime();
                }else{
                	data.enable_date = null;
                }

                if(data.effect_date_end){
                    data.effect_date_end = new Date(data.effect_date_end).getTime();
                }else{
                	data.effect_date_end = null;
                }

                if(data.effect_date_start){
                    data.effect_date_start = new Date(data.effect_date_start).getTime();
                }else{
                	data.effect_date_start = null;
                }
                console.log('555',data.disable_date);
			    return data;
			},
            /***
             * 获取详情列表
			 * {type} 1为产品，2为店铺
             * **/
            getDetailList(type){
              if(!arguments.length) return;
              var currentData = this[this.getKeyOfCurrentDataByType(type)];

              var i = currentData.length;

              var newArray = [];
              var a = 0;

              for(;a < i;a++){//编辑和新增

                  var data = currentData[a];
				  data.price_list_id = this.price.price_list_id;
				  var submitData = type ==1?this.getGoodsDetailInfo(data):this.getShopDetailInfo(data);
					//console.log(a,submitData);
				  !this.isEmptyObject(submitData)?newArray.push(submitData):'';
			  }


			  //删除
				var getDeletData = this[this.getKeyOfDeleteDataByType(type)];

				var relationId = type == 1?'del_price_list_detail_id':'del_price_list_shop_id';
				//console.log('getDeletData',getDeletData);
				for(var key in getDeletData){
                    var delData = {
                        price_list_id : this.price.price_list_id,

					}
                    delData[relationId] = getDeletData[key];
                    newArray.push(delData);
				}
			  return newArray;

			},

			/**
			 * 获取提交物料信息
			 * */
			getGoodsDetailInfo(data){
			    if(!data || this.isEmptyObject(data)) return {};
                var deleteData = this.deleteGoodsMap;
                var material_id = data.material_id;
                var detailData = {
                    price_list_id : this.price.price_list_id,
                    disable_date : data.disable_date,
                    disabled_status : data.disabled_status,
                    enable_date : data.enable_date,
					material_id : data.material_id,
                    material_code:data.material_code,
                    price : data.price,
                    remark :data.remark,
                    price_list_detail_id:data.price_list_detail_id || '',
                    if_close_effect:data.if_close_effect,
                    activity_recognition_code:data.activity_recognition_code,
                    activity_type:data.activity_type,
					goods_id:data.goods_id,//商品id

				}

				if(detailData.enable_date){
                    detailData.enable_date = new Date(detailData.enable_date).getTime();
				}
                if(detailData.disable_date){
                    detailData.disable_date = new Date(detailData.disable_date).getTime();
                }

				if(data.lowest_price){
                    //目录价格
                    detailData.lowest_price = data.lowest_price;
				}

				//删除
                /*if(deleteData.hasOwnProperty(material_id)){
					detailData.del_price_list_detail_id = data.price_list_detail_id;
                    delete detailData.price_list_detail_id;
                    return detailData;
                }*/

                //失效进行处理
				if(data.disabled_status == 1 && data.price_list_detail_id){

                    detailData.disable_price_list_detail_id = data.price_list_detail_id;
					delete detailData.price_list_detail_id;
                    return detailData;
				}

				//新增
                !detailData.price_list_detail_id? delete detailData.price_list_detail_id:'';
				return detailData;
			},
            /**
             * 获取提交店铺信息
             * */
            getShopDetailInfo(data){
                if(!data || this.isEmptyObject(data)) return {};
                var deleteData = this.deleteShopMap;
                var shop_id = data.shop_id;
                var detailData = {
                    price_list_id : this.price.price_list_id,
					disabled_status : data.disabled_status,
					shop_id : data.shop_id,
                    shop_code : data.shop_code,
                    remark :data.remark,
                    price_list_shop_id:data.price_list_shop_id || ''

                }

                //删除
                /*if(deleteData.hasOwnProperty(shop_id)){
                    detailData.del_price_list_shop_id = data.price_list_shop_id;
                    delete detailData.price_list_shop_id;
                    return detailData;
                }*/

                //失效进行处理
                if(data.disabled_status == 1 && data.price_list_shop_id){
                    detailData.disable_price_list_shop_id = data.price_list_shop_id;
                    delete detailData.price_list_shop_id;
                    return detailData;
                }

                //新增
                !detailData.price_list_shop_id? delete detailData.price_list_shop_id:'';
                return detailData;
            },
            sizeChange(size){
                // 每页加载数据
				var searchData = this.getCurrentSearchData();
                searchData.page.length = size;
                searchData.page.pageNo = 1;
				this.requestCurrentData();

            },
            pageChange(page_no){
                // 页数改变
                var searchData = this.getCurrentSearchData();
                searchData.page.pageNo = page_no;
                this.requestCurrentData();
            },
			/**
			 * 设置行失效的信息
			 * **/
			setDisabledInfo(data){
                if(data.disabled_status == 1){//失效的话，要添加行失效时间和行失效人
                    data.disable_person_name = fn.getUserInfo('realName');
					//data.disable_time = new Date().valueOf();
				}

			},
			/**
			*把当前编辑的值赋值到原对像里面去
			*/
			setVal(oldData,modifyData){
				console.log('oldData',oldData);
				console.log('modifyData',modifyData);

				for(var key in modifyData){
					oldData[key] = modifyData[key];
				}
			},

			/**
			 * 编辑物料信息
			 * **/
			eidtGoods(d,idx){
			    if(!d) return;
			    var originalData = d.data,
			    	modifyData = d.modifyData;
			    console.log('originalData',originalData);

				//var material_id = data.material_id;
				console.log('修改价格',idx)
				var id = this.getKey(originalData);
				var list = this.goodsList;
				var i = list.length;
				var bool = true;
				var a  = 0;
				var index = 0;
				for(;a < i; a++){
				    var currentData = list[a];
				    var currentKey = this.getKey(currentData);
				    //判断更改过后的时间与原有的时间是否存在重叠现在
				    if(id == currentKey){
				    	index = a;
				    }
				    if(id != currentKey && currentData.material_id == originalData.material_id){//同一物料，不同的时间段
				    	bool = this.judgeTimeIsPass(currentData,modifyData);//判断时间是否有重叠
				    	if(!bool) {
				    		// this.repeatDataTip([originalData]);
							if(this.price.price_list_type != 'DIRECT_DESCENT_LIST'){
								this.repeatDataTip([originalData]);
							}
				    		break;
				    	}
				    }
				}
				//a == i?a = i-1:'';
				if(bool){

				    this.setVal(list[index],modifyData);

			    	list.splice(index,1,originalData);
				}else if(this.price.price_list_type == 'DIRECT_DESCENT_LIST'){
					this.setVal(list[idx],modifyData);
					list.splice(idx,1,originalData);
				}

			},
			/**
			*获取原始物料数据
			*/
			getOriginalGoodDataByPriceId(data){
				var originalGoodsList = this.getOriginalGoodsData();
				if(!originalGoodsList || !originalGoodsList.length || !arguments.length || ! data) return '';
				var a = originalGoodsList.length;
				var i = 0;

				for(;i < a; i++){
					var currentData = originalGoodsList[i];
					if(currentData.price_list_detail_id == data.price_list_detail_id) {
						return currentData;
					}
				}
				return '';
			},
			addPrice(data){
				var title = data?'修改价格':'添加价格';
				var _this = this;
				let index =_this.goodsList.indexOf(data);
				var params = {
                    catelog:this.price,
                    isCanEdit:this.isCanEdit,
                    isCanSave:this.isCanSave,
                    isCanEditDisabled:this.isCanEditDisabled


				}
				console.log('params',params);
				if(data){
					var originalData = this.getOriginalGoodDataByPriceId(data);
					originalData?params.originalDisableData = originalData.disable_date:'';
				}


				data?params.detail=data:'';
                params.callback=(d)=>{
                    var getData = d.data;

					this.setDisabledInfo(getData);

				    var current = {};
					current[getData.material_id] = getData;
					data?this.eidtGoods(d,index):this.addGoodsOrShopCallback(current);

				}
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/goodsprice/detail'),
                	style:'width:960px;height:600px',
                    title:title,
					params:params//传参过去
				});

			},


            searchPriceList(){
              //模糊搜索
				this.getGoodsList();
			},

			/*
			 * 添加物料(店铺)的回调函数
			 * **/
            addGoodsOrShopCallback(data){
				console.log(data)
                if(!data || this.isEmptyObject(data)) return;
                var keyOfData = this.getKeyOfCurrentData();
                var keyOfId = this.getKeyOfID();
                var list = this[keyOfData];
				var i = list.length - 1;
				var repeatData = [];
				var keyOfCurrentNum = this.getDataCount();

                for(; i >=0 ;i--){//去重
                    var currentData = list[i];
                    var id = currentData[keyOfId];
					console.log(id)
					console.log(data)
					if(this.price.price_list_type != 'DIRECT_DESCENT_LIST'){
						if(data.hasOwnProperty(id)){

							if(keyOfId == 'material_id'){//如果是物料的话，则要根据时间是否重复再去判断
								let bool = this.judgeTimeIsPass(currentData,data[id]);//如果时间通过了，则不需要去重
								if(bool) continue;
							}
							repeatData.push(data[id]);
							delete data[id];
							continue;
						}
					}

                }


                for(var key in data){//添加
                	this[keyOfCurrentNum]++;
                	data[key].index = this[keyOfCurrentNum];
                	// console.log(data[key].index);
                    list.push(data[key]);
                }


				if(this.price.price_list_type != 'DIRECT_DESCENT_LIST'){
                	this.repeatDataTip(repeatData);
				}
                // console.log('repeatData',repeatData);
				// console.log('list',list);
            },

            judgeTimeIsPass(oldData,newData){
            	console.log(oldData,newData)
            	if(arguments.length != 2) return;
            	var old_enable_date = oldData.enable_date,
            		old_disable_date = oldData.disable_date,
            		new_enable_date = newData.enable_date,
            		new_disable_date = newData.disable_date;
            	if(oldData.disabled_status == 1 || newData.disabled_status == 1) return true;//把失效的明细行排除在外,不管是新数据还是老数据
            	console.log('看看啥的东西');
            	if(!old_enable_date || !old_disable_date || !new_enable_date || !new_disable_date) return false;
            	var old_enable_Time = new Date(old_enable_date).valueOf();
            	var	old_disable_Time = new Date(old_disable_date).valueOf();
            	var	new_enable_Time = new Date(new_enable_date).valueOf();
            	var	new_disable_Time = new Date(new_disable_date).valueOf();
            	//var	new_disable_Time = new_disable_date.getTime();
            	if(old_enable_Time > new_disable_Time || old_disable_Time < new_enable_Time) return true;//拿两个边际进行对比

            	return false;

            },
            /**
			*弹出重复提示
            **/
            repeatDataTip(data){
            	if(!data || !data.length) return;
            	var keyOfId = this.getKeyOfID();
            	var title = keyOfId == 'material_id'?'物料日期重复，不能添加':'店铺已存在在，不需要重复添加';
            	var key = keyOfId == 'material_id'?'material_name':'shop_code';
            	var existDataName = '';
            	var i = data.length;
            	var a = 0;
            	for(;a < i; a++){
            		var currentData = data[a];
            		existDataName += currentData[key] + ',';
            	}
            	//TODO,提示语的问题
            	 this.$message({
                        message:(existDataName + title),
                        type:'warning'
                  });


            },
            addShopList(){
                //添加店铺
                var _this = this;
                var data={};
                data.callback=(d)=>{
					// 获取已经有的店铺ID Set集合
					let shopListSet = new Set(),
						i = _this.shopList.length;
					while(i--) {
						shopListSet.add(_this.shopList[i].shop_id)
					};
					i = d.length;
					while(i--) {
						if(!shopListSet.has(d[i].shop_id)) {
							d[i].disabled_status = 0;
							_this.shopList.push(d[i])
						}
					}
				}
                data.price_list_type = this.price.price_list_type
                this.$root.eventHandle.$emit('alert',{
                    component:()=>import('@components/shop/list.vue'),
                    style:'width:800px;height:500px',
                    title:'店铺列表',
                    params:data
				});
            },
            select(selection){//单选发生改变
            	//唯一的标识符，price_list_detail_id或price_list_shop_id,或是material_id+inde或是shop_id+index
				var i = selection.length;
				var selectedKey = this.getKeyOfSelected();
                //var idKey = this.getKeyOfID();
                this[selectedKey] = {};

                if(!i) return;
				var a = 0;
				for(;a < i; a++){
					var currentData = selection[a];
					var key = this.getKey(currentData);
					if(!key) continue;
                    //this[selectedKey][currentData[idKey]] = currentData;
                    this[selectedKey][key] = currentData;
				}
				console.log('this[selectedKey]',this[selectedKey]);
            },

            /*
            *获取对像的唯一标识符，key值
            */
            getKey(data){//唯一的标识符，price_list_detail_id或price_list_shop_id,或是material_id+inde或是shop_id+index
            	if(!data) return '';
            	var index = '+' + data.index;
            	var mixtureKey = data.material_id?data.material_id+index:data.shop_id?data.shop_id+index:'';
            	var key = data.price_list_detail_id?data.price_list_detail_id:data.price_list_shop_id?data.price_list_shop_id:mixtureKey;
            	return key;
            },

			/**
			 * 删除(失效)店铺(价格)明细
			 * {type} 1为删除，0为失效
			 * **/
			removeOrDisabled(type){
			    var keyOfSelected = this.getKeyOfSelected();
			    var keyOfId = this.getKeyOfID();
			    var keyOfData = this.getKeyOfCurrentData();

			    var selectedData = this[keyOfSelected];

				var currentData = this[keyOfData];
                var currentType = this.getCurrentTypeByListActiveName();
                var deleteJson = currentType == 1?this.deleteGoodsMap:this.deleteShopMap;
                var relationId = currentType == 1?'price_list_detail_id':'price_list_shop_id';

                if(this.isEmptyObject(selectedData)){
                    this.$message({
                        type:'error',
                        message:'请选择要操作的数据'
                    });
                    return;
                }

                var b  = 0;
                var c = 0;

                for(;b < currentData.length; b=c){
                    if(this.isEmptyObject(selectedData))break;
                    var nowData = currentData[b];
                    //var id = nowData[keyOfId];
                    var id = this.getKey(nowData);
                    if(!selectedData.hasOwnProperty(id)){
                        c++
                        continue;
					}

                    c = 0;
                    delete selectedData[id];//对已选中的数据进行操作

                    //失效
					if(!type){//店铺和物料的失效字段是一样的，所以用同一种方式去处理
						if(!nowData.disable_person_id){

							nowData.disabled_status = 1;
							this.setDisabledInfo(nowData);
							console.log('445254');

						}

						continue;
					}
                    //删除
					if(nowData[relationId]){//对已经关联的数据进行数据库删除
                        //var id = nowData[keyOfId];
						//物料ID,---主键ID
						console.log(nowData)
						// 生效的商品行不能删除
						deleteJson[id] = nowData[relationId];
                    }
					currentData.splice(b,1);

				}

				if(!type){//如果是失效的话，需要清空数据

                    this.clearDisabledData();
				}
				console.log(this.shopList,deleteJson,this.deleteShopMap)


			},
			removeShop(){
				// console.log(this.selectedShop)
				for(var i in this.selectedShop){
					if(!!this.selectedShop[i].price_list_shop_id){
						this.$message.error('已保存店铺不能删除');
					}else{
						// this.deleteShopMap[this.selectedShop[i].price_list_shop_id] = this.selectedShop[i].price_list_shop_id
						this.shopList.splice(this.shopList.indexOf(this.selectedShop[i]),1);
					}
				}
				console.log(this.deleteShopMap);
			},
			disabledShop(){
				for(var i in this.selectedShop){
					if(!this.selectedShop[i].price_list_shop_id){
						this.$message.error('未保存不能失效');
					}else{
						this.shopList[this.shopList.indexOf(this.selectedShop[i])].disabled_status = 1;
					}
				}
			},

            searchShopList(){
              //店铺模糊搜索
				this.getShopList();
			},

			/**
			*设置按钮状态
			**/
			setBtnDisabled(){
				//新增的时候直接禁用以下按钮
				//type---1,2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回
				var status = this.price.status;
				this.saveDisabled = !this.isEdit?false:!this.matchValidateCondition(1)[status];
				this.submitDisabled = !this.isEdit?true:!this.matchValidateCondition(3)[status];
				this.recallDisabled = !this.isEdit?true:!this.matchValidateCondition(5)[status];
				this.delDisabled = !this.isEdit?true:!this.matchValidateCondition(2)[status];
				this.rejectDisabled = !this.isEdit?true:!this.matchValidateCondition(6)[status];
				this.auditDisabled = !this.isEdit?true:!this.matchValidateCondition(4)[status];
				this.refreshDisabled = !this.isEdit;
				this.priceEffectDisabled = !this.isEdit;
				this.saveLoading = this.submitLoading = this.recallLoading = this.delLoading = this.rejectLoading = this.auditLoading = this.refreshLoading = this.priceEffectLoading = false;
				//this.ifCloseEffectDisabled = this.saveDisabled?true:status != 'APPROVED'?false:true;

			},

			initData(){
				let self = this;
			  //初使化数据
				var data = this.params || {};

				this.price.price_list_type = data.type;
                this.price.price_list_id = data.id;

				this.isEdit = (typeof data.id == 'undefined' || data.id === '')?false:true;//从编辑状态来还是从新增那边过来
				this.isCanEdit = this.isCanSave = this.isCanEditDisabled = this.isEdit?false:true;//如果是编辑的话，则需要到拿到详细信息再去做判断；如果是新增的话，可以编辑，
				//店铺范围是否可编辑编辑
				this.rangeDisabled = (this.isEdit || data.type == 'CATALOG_LIST' )?true:false;//如果是目录价格或是编辑，则不能更改
				//店铺范围赋值
				this.price.store_area = (data.type == 'CATALOG_LIST' || !this.isEdit)?1:0;
				this.range = this.price.store_area?true:false;
				//状态的默认值
				this.price.status = this.isEdit?'':'CREATE';
                this.price.status_name = this.isEdit?'':'创建';
                /*if(!this.price.if_close_effect){
                	this.price.if_close_effect = 'Y';
                }*/

                //this.ifCloseEffect = this.price.if_close_effect == 'Y'?true:false;


                //this.ifCloseEffectDisabled = this.isEdit;

                //this.isEdit?'':this.price_code = this.getCodeByPriceType(data.type);


                if(!this.isEdit){
                	this.setBtnDisabled();
                }

				//初使化时间
                //this.price.enable_date = this.price.effect_date_start = fn.getNowDate({form:'00:00:00'});
                //this.price.disable_date = this.price.effect_date_end = fn.getNowDate({form:'23:59:59'});
                this.setOriginalPriceInfo();

			},

            getGoodsList(){
                //获取物料列表
				var _this = this;
				if(!_this.isEdit) return;//TODO,这里拿搜索条件
				var searchCondition = _this.goodsSearch;
				searchCondition.price_list_id = this.price.price_list_id;
              	this.ajax.postStream('/price-web/api/price/getPriceListDetailInfo',searchCondition,function(d){
              	    var data = d.body;
              	    if(!data.result){
                        _this.$message({
                            message:data.msg,
                            type:'error'
                        });
              	        return;
					}
					let list =  data.content.list || [];
					_this.setRowEffectTime(list);
                    _this.goodsList = list;
                    _this.goodsCount = _this.goodsIndex = data.content.count;

                    _this.clearGoodsData();
                    _this.setOriginalGoodsData();
                    //_this.clearSelectedData();
                },function(e){

                })
            },
            /**
            *行影响时间
            *可编辑条件=》可保存，并且在表头的影响时间之前
            **/
            setRowEffectTime(list){
            	let i = list.length;
            	for(let a = 0; a< i;a++){
            		let good = list[a];
            		good.canEditRowEffect = good.disabled_status == '1'?false:this.saveDisabled;
            	}

            },
            getShopList(){
                //获取店铺的列表
                var _this = this;
                if(!_this.isEdit || _this.price.price_list_type == 'CATALOG_LIST' ) return;//如果是新建或是目录价格，则不需要加载店铺列表
                var searchCondition = _this.shopSearch;
                searchCondition.price_list_id = this.price.price_list_id;
                this.ajax.postStream('/price-web/api/price/getPriceListShopInfo',searchCondition,function(d){
                    var data = d.body;
                    if(!data.result){
                        _this.$message({
                            message:data.msg,
                            type:'error'
                        })
                        return;
                    }
                    _this.shopList = data.content.list || [];
                    _this.shopCount = _this.shopIndex = data.content.count;
                    _this.clearShopData();
					_this.setOriginalShopData();
                },function(e){

                })
            },
			/**
			 * 重新设置表头数据
			 * **/
			resetPriceData(content){
				let _this = this;
				if(!arguments.length) return;
                //var content = data.content;
                var currentData = _this.price;
                for(var key in currentData){//重新赋值
                    currentData[key] = content[key];
                }
				let modeList = __AUX.get('offline_payment_mode');
				let arr = content.offline_payment_mode.split(',')
				// console.log(modeList,arr)
				this.price.offline_payment_mode_name = ''
				modeList.forEach(item=>{
					arr.forEach((arrItem,index)=>{
						console.log(arrItem,item,index,'index')
						if(item.code == arrItem){
							if(index === 0){

								_this.price.offline_payment_mode_name += item.name;
							}else{
								_this.price.offline_payment_mode_name += ','+item.name;
							}
						}
					})
				})

                currentData.original_disable_date = currentData.disable_date;
                _this.range = currentData.store_area?true:false;

                //_this.ifCloseEffect = currentData.if_close_effect == 'N'?false:true;

                var status = currentData.status;

                //把创建、重新审核、已撤回，这些状态传
                var canEdit = (!status || status == 'CREATE' || status == 'RETRIAL' || status == 'WITHDRAWED')?true:false
                var bool = (canEdit || status == 'APPROVED')?true:false;//审核后的只能修改失效日期，且失效日期不能小于当前日期
                _this.modify_time_data = fn.dateFormat(currentData.modify_time,'yyyy-MM-dd hh:mm:ss');
                _this.audit_time_data = fn.dateFormat(currentData.audit_time,'yyyy-MM-dd hh:mm:ss');
                _this.create_time_data = fn.dateFormat(currentData.create_time,'yyyy-MM-dd hh:mm:ss');
                _this.isCanEdit = canEdit;


              	_this.isCanSave = bool;

                _this.isCanEditDisabled = bool && _this.price.price_list_type !='PREFERENTIAL_LIST';

                //失效可编辑的情况有两种
                //1:创建、重新审核、已撤回单据状态下
                //2:在已审核的状态下，需要判断影响时间或是生效时间是要大于当前时间的
                //不要的数据
                //_this.isCanLoseEffect = status == 'APPROVED'?new Date((this.price.effect_date_start || this.price.enable_date)).getTime() > new Date().getTime():canEdit;



                if(this.isEdit && this.isCanEditDisabled && this.price.disable_date){
                	this.isCanEditDisabled = new Date().getTime() < new Date(this.price.disable_date).getTime()?true:false;
                }


                _this.setBtnDisabled();
                //_this.submit = [];//清空按钮数据
                //_this.audit = [];//清空按钮数据

			},
			getPriceDetail(){
              //获取价格表头信息
				var _this = this;
				if(!_this.isEdit) return;//如果是新建
				//var submitData = {price_list_id:this.price.price_list_id}
                this.ajax.postStream('/price-web/api/price/getPriceListInfoByPrimaryKey',this.price.price_list_id,function(d){
                    var data = d.body;
                    if(!data.result) {
                        _this.$message({
                            message:data.msg,
                            type:'error'
                        })
                        this.setBtnDisabled();
						return;
					}

					if(!_this.params.type){//从新退款申请单跳转过来时没有带type参数时
						//活动价目，失效时间必须要填
						_this.rules.disable_date[0].required = data.content.price_list_type == 'PREFERENTIAL_LIST' ||'STANDARD_LIST'?true:false
						_this.rules.disable_date[0].msg = data.content.price_list_type == 'PREFERENTIAL_LIST'||'STANDARD_LIST'?'失效时间不能小于生效时间':'失效时间不能小于生效时间或是不填失效时间'
					}


					_this.resetPriceData(data.content);
                    //_this.setBtnList();
                    _this.setOriginalPriceInfo();//重新储存数据
                   // _this.isDisableBtn = false;

				},function(e){
					this.setBtnDisabled();
                })
			},

            /***
             * 设置价格原始数据
             * */
            setOriginalPriceInfo(){
                this.price?this.originalPriceData = JSON.stringify(this.price):'';
            },
            /***
             * 设置物料原始数据
             * */
            setOriginalGoodsData(){
                this.goodsList?this.originalGoodsList = JSON.stringify(this.goodsList):'';
            },
            /***
             * 设置用户原始数据
             * */
            setOriginalShopData(){
                this.shopList?this.originalShopList = JSON.stringify(this.shopList):'';
            },

            /***
             * 获取价格原始数据
             * */
            getOriginalPriceInfo(){
                var info = this.originalPriceData;
                return !info?{}:JSON.parse(info);

            },
            /***
             * 设置物料原始数据
             * */
            getOriginalGoodsData(){
                var info = this.originalGoodsList;
                return !info?[]:JSON.parse(info);

            },
            /***
             * 设置店铺原始数据
             * */
            getOriginalShopData(){
                var info = this.originalShopList;
                return !info?[]:JSON.parse(info);

            },

            /**
             * 清空失效选中的数据
             * **/
            clearDisabledData(){
                var keyOfCurrentData = this.getKeyOfCurrentData();
				var keyOfSelectedData = this.getKeyOfSelected();
				this[keyOfSelectedData] = {};
                this.$refs[keyOfCurrentData] && this.$refs[keyOfCurrentData].clearSelection();
            },
            /**
             * 清空价格明细数据
             * **/
			clearGoodsData(){
                this.selectedGoods = {};
                this.deleteGoodsMap = {};
                this.$refs.goodsList && this.$refs.goodsList.clearSelection();
			},
            /**
             * 清空店铺明细数据
             * **/
            clearShopData(){
                this.selectedShop = {};
                this.deleteShopMap = {};
                this.$refs.shopList && this.$refs.shopList.clearSelection();
            },
            /**
             * 清空批量选择的数据
             * **/
            clearSelectedData(){//默认清空
                this.selectedShop = {};
                this.selectedGoods = {};
				this.$refs.shopList && this.$refs.shopList.clearSelection();
                this.$refs.goodsList && this.$refs.goodsList.clearSelection();

            },
            /**
             * 重置请求数据
             * {bool} 是否首页还是加载当前页面
			 * true为首页
			 * false当前页面
             * **/
            resetSearchData(bool){
				var searchData = this.getCurrentSearchData();
				bool?searchData.page.pageNo=1:'';
            },
            /**
             * 当前页面要请求的数据
             * {returns} data
             * **/
            requestCurrentData(){
                var type = this.getCurrentTypeByListActiveName();
                return type == 1?this.getGoodsList():this.getShopList();
            },
            /**
             * 获取当前分页列表搜索数据
             * {returns} data
             * **/
            getCurrentSearchData(){
                var type = this.getCurrentTypeByListActiveName();
                return type == 1?this.goodsSearch:this.shopSearch;
            },

            /**
             * 获取当前面板数据的计数值
             * {returns} data
             * **/
            getDataCount(){
                var type = this.getCurrentTypeByListActiveName();
                return type == 1?'goodsCount':'shopCount';
            },
            /**
             * 获取当前面板详情的ID key 值
             * {returns} data
             * **/
            getKeyOfID(){
                var type = this.getCurrentTypeByListActiveName();
                return type == 1?'material_id':'shop_id';
            },
            /**
             * 通过TYPE值来获取ID key值
             *{type} 1为产品，2为店铺
             * **/
            /*getKeyOfIdByType(type){
                return type == 1?'material_id':'shop_id';
            },*/
            /**
             * 通过TYPE值来获取删除数据值
             *{type} 1为产品，2为店铺
             * **/
            getKeyOfDeleteDataByType(type){
                return type == 1?'deleteGoodsMap':'deleteShopMap';
            },
            /**
             * 通过当前面板来获取删除数据值 key 值
             *{type} 1为产品，2为店铺
             * **/
			getKeyOfDeleteDataByActiveName(){
                var type = this.getCurrentTypeByListActiveName();
			    return type == 1?'deleteGoodsMap':'deleteShopMap';
			 },
            /**
             * 获取当前呈现的数据
             * {returns} data
             * **/
            getKeyOfCurrentData(){
                var type = this.getCurrentTypeByListActiveName();
                return type == 1?'goodsList':'shopList';
            },
            /**
             * 通过TYPE获取当前呈现的数据
             * {type} 1为商品，2为店铺
             * **/
            getKeyOfCurrentDataByType(type){
				return type == 1?'goodsList':'shopList';
            },
            /**
             * 获取当前批量操作的数据
             * {returns} data
             * **/
			getCurrentBatchData(){
				var type = this.getCurrentTypeByListActiveName();
				var key = this.getKeyOfSelected();
				var data = this[key];
				if(this.isEmptyObject(data)){
				    return [];
				}
				var array = [];
				for(var key in data){
				    array.push(data[key]);
				}
				return array;

			},
            /**
             * 获取当前面板批量操作的key值
             * {returns} data
             * **/
			getKeyOfSelected(){
                var type = this.getCurrentTypeByListActiveName();
                return type == 1?'selectedGoods':'selectedShop';
			},
			/**
			 * 获取当前面板操作的是价格还是店铺
			 * {returns} type--1为价格，2为店铺
			 * **/
			getCurrentTypeByListActiveName(){
			    return this.listActiveName == 'first'?1:2;
			},
            isEmptyObject(data){
			    for(var key in data){
			        return false;
				}
				return true;
			},
            closeCurrentComponent(){
				let bool = this.isChangeData();
				let _this = this;
				if(!bool){
                    _this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
                    return;
				}

                this.$root.eventHandle.$emit('openDialog',{
                    ok(){
                        /*_this.submitPriceInfo('price',()=>{
                            _this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
                        })*/
                        _this.submitPriceInfo(()=>{
                            _this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
                        })
                    },
                    no(){
                        _this.$root.eventHandle.$emit('removeTab',_this.params.tabName);
                    }
                })
            },

			/**
			 * 判断是否有数据变动
			 * **/
            isChangeData(){
				var originalPrice = this.getOriginalPriceInfo();
				var currentPrice = this.price;
				currentPrice.store_area = this.range?1:0;
			    //currentPrice.if_close_effect = this.ifCloseEffect?'Y':'N';

			   	var bool = false;
                if(!this.isEmptyObject(this.deleteGoodsMap) || !this.isEmptyObject(this.deleteShopMap)){
                    bool = true;
                }
                console.log(1,bool);
				if(!bool){
                    bool = this.compareData(currentPrice,originalPrice);
				}
                console.log(2,bool);
				if(!bool){
				    let originalGoodsList = this.getOriginalGoodsData();
				    let currentGoodsList = this.goodsList;
                    bool = this.compareData(currentGoodsList,originalGoodsList);
				}
                console.log(3,bool);
                if(!bool){
                    let originalShopList = this.getOriginalShopData();
                    let currentShopList = this.shopList;
                    bool = this.compareData(currentShopList,originalShopList);
                }
                console.log(4,bool);
                return bool;
            },
            /***
             * 设置所有按钮数据
             * */
			/*setBtnList(){
				this.setSingleBtn(3,'submit');
                this.setSingleBtn(5,'submit');
                this.setSingleBtn(4,'audit');
                this.setSingleBtn(6,'audit');
			},*/
			/***
			 * 设置按钮组里面的数据
			 * */
			/*setSingleBtn(type,key){
			    if(!arguments.length || arguments.length !=2) return;
                var PriceType = this.price.status;
				var matchSubmitBtnCondition = this.matchValidateCondition(type);
				if(matchSubmitBtnCondition.hasOwnProperty(PriceType)){
				    this[key].push(this.matchBtnInfo(type));
				}
			},*/
            matchValidateCondition(type){
                //type---1为保存,type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回
                var data = {
                	1:{
                        CREATE:true,//创建
                        RETRIAL:true,//重新审核
                        WITHDRAWED:true,//已撤回
                        APPROVED:true//已审核
                    },
                    2:{
                        CREATE:true,//创建
                        RETRIAL:true,//重新审核
                        WITHDRAWED:true//已撤回
                    },
                    3:{
                        CREATE:true,//创建
                        RETRIAL:true,//重新审核
                        WITHDRAWED:true//已撤回
                    },
                    4:{
                        SUBMITTED:true//提交审核
                    },
                    5:{
                        SUBMITTED:true//提交审核
                    },
                    6:{
                        SUBMITTED:true//提交审核
                    }
                }
                return data[type] || {} ;
            },

			/*matchBtnInfo(type){
                //type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回
				if(!arguments.length) return {};
				let _this = this;
                let btnInfo = {
                    3:{type:'info', txt:'提交', click(){//如果是提交按钮并且有修改内容，那就先保存再提交
                    		let bool = _this.isChangeData();
                    		if(bool){

		                        _this.submitPriceInfo(()=>{
		                            _this.operateList(3);
		                        });
                    		}else{
                    			_this.operateList(3);
                    		}

                    	}
                    },
                    4:{type:'info', txt:'审核', click(){_this.operateList(4)} },
                    5:{type:'info', txt:'撤回', click(){_this.operateList(5)} },
                    6:{type:'info', txt:'驳回', click(){_this.operateList(6)} }
                }
                return btnInfo[type] || {};
			},*/
			/**
			*type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回，type---7 为失效
			**/
			batchOperationEvent(type){
				if(!arguments.length) return ;
				var disabled,loading;
				if(type == 2){
					disabled = 'delDisabled';
					loading = 'delLoading';
				}else if(type == 3){
					disabled = 'submitDisabled';
					loading = 'submitLoading';
				}else if(type == 4){
					disabled = 'auditDisabled';
					loading = 'auditLoading';
				}else if(type == 5){
					disabled = 'recallDisabled';
					loading = 'recallLoading';
				}else if(type == 6){
					disabled = 'rejectDisabled';
					loading = 'rejectLoading';
				}else if(type == 7){
					loading = 'priceEffectLoading';
				}
				disabled && (this[disabled] = true);
				this[loading] = true;
            	if(type == 3){
            		//提交
            		let bool = this.isChangeData();
            		if(bool){
                    	this.submitPriceInfo(()=>{
                            this.operateList(3);
                        });
            		}else{
            			this.operateList(3);
            		}
            		return;
            	}
				this.operateList(type)

            },
            /***
             *
             * {type} type---2 为删除，type---3 为提交，type---4 为审核,type---5 为撤回，type---6 为驳回，type---7 为失效
             * */

            operateList(type){
				var getList = this.price.price_list_id?[this.price.price_list_id]:[];
                var i = getList.length;
                //this.isDisableBtn = true;
                if(!i) return;
                var url = '';
                if(2 == type){
                    url = '/price-web/api/price/deletePriceListAllInfo?permissionCode=PRICE_LIST_DELETE';
                }else if(3 == type){
                    url = '/price-web/api/price/commitPriceListAllInfo?permissionCode=PRICE_LIST_SUBMIT';
                }else if(4 == type){
                    url = '/price-web/api/price/auditPriceList?permissionCode=PRICE_LIST_AUDIT';
                }else if(5 == type){
                    url = '/price-web/api/price/withdrawPriceList?permissionCode=PRICE_LIST_WITHDRAW';
                }else if(6 == type){
                    url = '/price-web/api/price/rejectPriceList?permissionCode=PRICE_LIST_REJECT';
                }else if(7 == type){
                    url = '/price-web/api/price/priceEffect';
                }
                if(!url) return;
                var submitData = getList;
                if(4 == type || 5 == type || 6 == type){
                    submitData = {
                        price_list_ids:getList
                    }
                }else if (7 == type){
					submitData = {
                        price_list_id:getList[0]
                    }
                }
                this.changePriceStatus({
                    url:url,
                    data:submitData
                });
            },
            changePriceStatus(data){
                if(!data.url) return;
                var _this = this;
                this.ajax.postStream(data.url,data.data||{},function(d){
                    var data = d.body;
                    _this.$message({
                        message:data.msg,
                        type:data.result?'success':'error'
                    });

                    if(!data.result){
                    	_this.setBtnDisabled();
                    }
                    if(data.result){
                        _this.getPriceDetail();

                        if(/priceEffect/.test(data.url)){
	                        _this.getGoodsList();
	                        _this.getShopList();
                        }

                        _this.$root.eventHandle.$emit('updataPriceList');
                    }
                    data.callback && data.callback(d);
                });
            },
            /**
			*刷新
			**/
			refreshEvent(){
				this.refreshDisabled = true;
				this.priceEffectDisabled = true;
				this.refreshLoading = true;
				this.priceEffectLoading = true;
				this.getPriceDetail();
				this.getGoodsList();
				this.getShopList();
			}

		},


		created() {
			this.getDiscountCategoryList()
		},
		mounted(){
			var _this = this;
            _this.initData();//初使化数据
            _this.getPriceDetail();
			_this.getGoodsList();
			_this.getShopList();
            _this.params.__close = _this.closeCurrentComponent;


            //_this.testFn();
		},
        watch:{
            range:function(newVal,oldVal){
                //监听范围的改变来确定是否需要显示添加店铺信息
                if(newVal){
					//this.listActiveName = 'first';
					this.shopList = [];
				}
			},
			rowEffectTime:function(newVal,oldVal){
				var goods = this.selectedGoods;
				if(!goods) return;
				for(var key in goods){
					let g = goods[key];
					if(g.disabled_status != 1){
						g.if_close_effect = newVal?'Y':'N';
					}
				}
				for(var i = 0; i < this.goodsList.length;i++){
					let a = this.goodsList[i];
					this.goodsList.splice(i,1,a);
				}

			},
            listActiveName:function(currentVal,oldVal){
                this.clearSelectedData();//面板切换时，也要清空数据
			},
			price : {
		      handler: function (newVal,oldVaal) {
		        if(newVal.price_list_type != 'PREFERENTIAL_LIST') return;

		        if(newVal.enable_date && !newVal.effect_date_start){
		        	newVal.effect_date_start = newVal.enable_date;
		        }
		        if(newVal.disable_date && !newVal.effect_date_end){
		        	newVal.effect_date_end = newVal.disable_date;
		        }
		      },
		      deep: true
		    }
		},

		destroyed(){
        }



	}
</script>
<style type="text/css" scoped>
	.el-select .el-input{width:200px}
	.el-picker-panel .el-input{width:auto;}
	.xpt-image__upload{height: 20px;line-height: 20px;
		position: relative;
		cursor: pointer;
		overflow: hidden;
		padding-top: 0;
		padding-bottom: 0;
		display: inline-block;
		*display: inline;
		*zoom: 1}

		.xpt-image__upload input{
			position: absolute;
			font-size: 100px;
			right: 0;
			top: 0;
			height:100%;
			opacity: 0 ;
			cursor: pointer
		}
		.xpt-flex__bottom.scroll{
			margin-bottom: 30px;
		}

</style>
