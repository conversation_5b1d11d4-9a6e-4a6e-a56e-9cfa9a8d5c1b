<template>
  <el-form-item>
    <div class="title">
      <span>{{title}}</span>
    </div>
  </el-form-item>
</template>

<script>
  export default {
    name: "MdMSeparator",
    props: {
      title: ''
    }
  }
</script>

<style scoped>
  .el-form-item{
    margin-bottom: 3px;
  }
  .title {
    height: 100%;
    border-bottom: 4px solid #4f5f6f;
  }
  .title span{
    border-radius: 3px 3px 0 0;
    padding: 0 10px 3px 10px;
    background-color: #4f5f6f;
    color: white;
  }
</style>
