<template>
  <div class='xpt-flex'>
    <xpt-headbar v-if='showHead'>
      <template v-for='(btn,index) in btns' slot='left'>
        <xpt-btngroup v-if='btn.isBtnGroup' :btngroup='btn.btnGroupList' class="mgl10"
                      :disabled="
						typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled
						||btn.loading
						||false
					"></xpt-btngroup>
        <el-button
          v-else
          :type='btn.type'
          size='mini'
          @click='e => btn.click&&btn.click(e)'
          :disabled="
						typeof btn.disabled === 'function' ? btn.disabled() : btn.disabled
						||btn.loading
						||false
					"
          :loading="btn.loading||false"
        >{{btn.txt}}
        </el-button>
      </template>
      <template slot='left'>
        <slot name='btns'></slot>
      </template>
      <template v-if="simpleSearch">
        <!-- 简单的搜索 -->
        <el-input
          :placeholder="searchHolder"
          icon="search"
          size='mini'
          v-model="searchTxt"
          :on-icon-click="searchClick"
          slot='right'
          @keyup.enter.native="searchClick"
        ></el-input>
      </template>
      <template v-else>
        <!-- 复杂的搜索 -->
        <mdm-search-ex v-if="searchPage" :searchPage="searchPage" @searchClick="searchClick" @matchColConfig="matchColConfig" slot='right' ref="xptSearchEx">
          <div slot="sort">
            <slot name='sort'></slot><!-- 字段排序功能 -->
          </div>
        </mdm-search-ex>
        <template v-else>
          <el-input
            v-if='searchHolder'
            :placeholder="searchHolder"
            icon="search"
            size='mini'
            v-model="searchTxt"
            :on-icon-click="searchClick"
            slot='right'
            @keyup.enter.native="searchClick"
          ></el-input>
        </template>
      </template>
    </xpt-headbar>
    <div class='xpt-flex__bottom mdm-list2'>
      <el-table :data="newList"
                border stripe fit
                width="100%"
                tooltip-effect="dark"
                ref="table"
                :max-height="innerHeight"
                @selection-change='checkBoxSelect'
                @sort-change='sortChange'
                @row-dblclick='rowDblclick'
                @row-click="rowClick"
                @cell-click='cellClick'
                @select='selectHandler'
                @select-all='selectAllHandler'
                :summary-method="summaryMethod"
                :show-summary="summaryMethod ? true : false"
                :show-header="showHeader"
                :row-class-name="taggelClassName">
        <el-table-column type="expand" v-if="ifExpand">
          <template slot-scope="props">
            <el-table
              :data='props.row.children'
              border stripe fit
              width="100%"
              tooltip-effect="dark"
              :show-header='false'
            >
              <el-table-column v-for='(col,index) in colData'
                               :key="(col.prop || '') + index"
                               :label='col.label'
                               show-overflow-tooltip
                               :width="col.width"
                               :prop='col.prop'
                               :align='col.align||"left"'
                               :class-name='getCellClassName(col,$style)'
              ></el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column type="expand" v-if="ifExpandMessage">
          <template slot-scope="props">
            <div v-for='(col,index) in colData'
                :key="(col.prop || '') + index"
                v-if='col.ifExpand==true'>
            {{ props.row[col.prop] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if='selection==="checkbox"'
                         align='center'
                         type="selection"
                         width="40"
                         :selectable="selectable">
        </el-table-column>
        <el-table-column
          v-if='selection==="radio"'
          align='center'
          width="40">
          <template slot-scope='scope'>
            <el-radio-group v-model="radioSelect">
              <el-radio
                :label="scope.row"
                class='xpt-table__radio'
                v-if='!radioDisabled(scope.row)'>
              </el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column v-if='orderNo' align='center' type="index" label="序号">
          <template slot-scope="scope">
            <template v-if="orderNoShow(scope.row)">{{ scope.row.__index || (scope.$index + 1) }}</template>
          </template>
        </el-table-column>
        <!-- :key不能只赋值index，否则它的width不会更新当colData改变时 -->
        <el-table-column v-for='(col,index) in colsConfig'
                         v-if='col.hide!=true'
                         :key="(col.prop || '') + index"
                         :label='col.label'
                         show-overflow-tooltip
                         resizable
                         :width="col.width"
                         :sortable='col.sort?"custom":false'
                         :prop='col.prop'
                         :align='col.align||"left"'
                         :class-name='getCellClassName(col,$style)'
        >
          <template slot-scope='scope'>
            <div v-if='newList[scope.$index].__isTotal'>{{scope.row[col.prop]}}</div>
            <template v-else>
              <template v-if="!col.bool" :id="col.initID && scope.$index == 0?col.initID:''">
                <!-- 点击跳转 -->
                <a v-if='col.redirectClick' href='javascript:;' @click='col.redirectClick(scope.row)' v-html="formatterSavePropOneTime(scope, col, index)"><span
                  v-tooltip='formatterSavePropOneTime(scope, col, index)'></span></a>
                <!-- 输出Html -->
                <div v-else-if='col.html' v-html='col.html(scope.row[col.prop])'
                     v-tooltip='col.html(scope.row[col.prop])'></div>

                <slotItem v-else-if='col.slot' :row='scope.row' :slots='$scopedSlots[col.slot]'></slotItem>

                <!-- 正常输出，包含数据过滤的formatter方法与过滤器format调用 -->
                <span v-else v-html="formatterSavePropOneTime(scope, col, index)"></span>
              </template>
              <div v-else class="pmm_input" :id="col.initID && scope.$index == 0?col.initID:''">

                <el-input size='mini' v-model="scope.row[col.prop]" icon="search"
                          v-if="col.isInitInput && col.isThis && scope.row[col.conditionDisabled]" disabled></el-input>


                <mdm-input placeholder="请选择" icon="search" size='mini'
                           v-if="col.isInitInput && !(col.isThis && scope.row[col.conditionDisabled])"
                           @click="col.redirectClick(scope.row)" @change="col.change" :maxlength='col.maxLength'
                           :disabled="col.isThis && scope.row[col.conditionDisabled]?true:false"
                           v-model="scope.row[col.prop]" :otherData="scope.row" readonly/>


                <el-input size='mini' v-model="scope.row[col.prop]" icon="search"
                          v-if="col.isInput && col.redirectClick" @click="col.redirectClick(scope.row)"
                          :maxlength='col.maxLength'
                          :disabled="col.isThis && scope.row[col.conditionDisabled]?true:false"></el-input>


                <el-input size='mini' v-model="scope.row[col.prop]" v-if="col.isInput && col.redirectChange"
                          @change="col.redirectChange(scope.row)" :maxlength='col.maxLength'
                          :disabled="col.isThis && scope.row[col.conditionDisabled]?true:false"></el-input>


                <div v-if="col.isInput && !col.redirectClick" style="position: relative;">
                  <el-input style="position:absolute;" size='mini' v-model="scope.row[col.prop]"
                            :maxlength='col.maxLength'
                            :disabled="col.isThis && scope.row[col.conditionDisabled]?true:false"></el-input>
                  <span style="color:#fff;">{{ scope.row[col.prop] }}</span>
                </div>


                <el-input size='mini' v-model="scope.row[col.prop]" v-if="col.isInputSearch" icon="search"
                          @click="col.redirectChange(scope.row)" readonly :maxlength='col.maxLength'
                          :disabled="col.isThis && scope.row[col.conditionDisabled]?true:false"></el-input>

                <el-input type="textarea"
                          v-model="scope.row[col.prop]"
                          v-if="col.isTextarea"
                          :disabled="col.disabled(scope.row)">
                </el-input>

                <el-select placeholder="请选择" size='mini' v-if="col.isSelect && col.redirectClick"
                           @change="col.redirectClick(scope.row)" v-model="scope.row[col.prop]"
                           :disabled="col.isThis && scope.row[col.conditionDisabled]?true:(col.disabled ? col.disabled(scope.row) : false)">
                  <el-option
                    v-for="(value,key) in col.obj"
                    :key="key"
                    :label="value"
                    :disabled='isDisabled(col,key)'
                    :value="key">
                  </el-option>
                </el-select>


                <el-select placeholder="请选择" size='mini' v-if="col.isSelect" v-model="scope.row[col.prop]"
                           :disabled="col.isThis && scope.row[col.conditionDisabled]?true:(col.disabled ? col.disabled(scope.row) : false)">
                  <el-option
                    v-for="(value,key) in col.obj"
                    :key="key"
                    :label="value"
                    :disabled='isDisabled(col,key)'
                    :value="key">
                  </el-option>
                </el-select>

                <!-- el-input带icon -->
                <template v-if='col.iconClick'>
                  <el-input size='mini' icon='search' v-if="!col.isClickEvent" style='width: 100%'
                            v-model='scope.row[col.prop]'
                            :disabled='col.disabled(scope.row)'
                            :on-icon-click='col.disabled(scope.row)?()=>{}:(e)=>col.iconClick(e,scope.row)'
                            :data-index='scope.$index'
                            :readonly='col.blur?false:true'
                            :maxlength='col.maxLength'
                            @blur='(col.blur?col.blur(scope.row):void(0))'></el-input>

                  <el-input size='mini' icon='search' v-else-if="col.ifReadonly" style='width: 100%'
                            v-model='scope.row[col.prop]'
                            :disabled='col.disabled(scope.row)'
                            @click="col.iconClick(scope.row)"
                            :data-index='scope.$index'
                            :readonly='col.canEdit&&col.canEdit(scope.row)'
                            :maxlength='col.maxLength'
                            @blur='(col.blur?col.blur(scope.row):void(0))'></el-input>

                  <el-input size='mini' icon='search' v-else style='width: 100%'
                            v-model='scope.row[col.prop]'
                            :disabled='col.disabled(scope.row)'
                            @click="col.iconClick(scope.row)"
                            :data-index='scope.$index'
                            :readonly='col.blur?false:true'
                            :maxlength='col.maxLength'
                            @blur='(col.blur?col.blur(scope.row):void(0))'></el-input>
                </template>

                <!-- el-select -->
                <template v-if='col.elSelect'>
                  <el-select size='mini' style='width: 100%'
                             :disabled='col.disabled(scope.row)'
                             v-model='scope.row[col.prop]'
                             @change='col.change(scope.row, scope.$index)'>
                    <el-option v-for='(item,index) in col.options'
                               :key='index' :label='item.label'
                               :value='item.value'></el-option>
                  </el-select>
                </template>

                <!-- el-input -->
                <template v-if='col.elInput'>
                  <el-input :disabled='col.disabled(scope.row)' size='mini' style='width: 100%'
                            v-model='scope.row[col.prop]' @change="() => col.change && col.change(scope.row)"
                            @blur='e => col.blur&&col.blur(scope.row, e)'></el-input>
                </template>

                <!-- el-date-picked -->
                <template v-if='col.elDate'>
                  <el-date-picker placeholder="选择日期" style='width: 100%' size='mini'
                                  :editable='false'
                                  :disabled='col.disabled(scope.row)'
                                  v-model="scope.row[col.prop]"
                                  :type="col.date||'date'"
                                  :picker-options="col.pickerOptions"
                                  @change="largeDataScrollRender.isRendering!=='rendering'&&col.change&&col.change(scope.row)"></el-date-picker>
                </template>
                <!-- el-date-picked -->
                <template v-if='col.elDate2'>
                  <el-date-picker placeholder="选择日期" style='width: 100%' size='mini'
                                  :editable='false'
                                  :disabled='col.disabled(scope.row)'
                                  v-model="scope.row[col.prop]"
                                  :type="col.date||'date'"
                                  :picker-options="col.pickerOptions(scope.row)"
                                  @change="largeDataScrollRender.isRendering!=='rendering'&&col.change&&col.change(scope.row)"></el-date-picker>
                </template>
              </div>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-row :class="'xpt-pagation ' + $style['hide-last-page']" v-if='typeof(pageTotal)==="number"'>
        <el-pagination @size-change="pageChange" @current-change="currentPageChange"
                       :current-page="pageNow" :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
                       :page-size="pageLength||pageSize"
                       layout="total, sizes, prev, pager, next" :total="pageTotal">
        </el-pagination>
        <div
          v-if="!!showSelectRowNumByOnlyKey"
          style="float: right;line-height: 28px;color: #48576a;"
        >
          <span>已选{{ selectRows.length }}条</span>
          <span v-if="typeof(showSelectRowNumByOnlyKey)==='string'">, 合计{{ selectRowNumByKey.length }}单</span>
        </div>
      </el-row>
    </div>
  </div>
</template>
<script>
  import MdmSearchEx from "@components/mdm/components/table/MdmSearchEx2";
  export default {
    data() {
      return {
        innerHeight:'',
        searchTxt: '',
        filter: '',
        // 单选框所选中的值
        radioSelect: '',
        pageNow: 1,
        newList: [],
        selectRows: [],
        selectRowNumByKey: [],

        colsConfig: [],
      }
    },
    props: {
      /*
        列表头部导航条按钮
        [{
          // 单个按钮
          type:'el-button type样式',
          txt:'按钮文本',
          click:'点击事件'
        },{
          // 按钮组
          isBtnGroup:true,
          btnGroupList:'按钮组列表，参考uttons/btngroup.vue的参数'
        }]
      */
      btns: {
        type: Array,
        default() {
          return [
            {
              type: 'primary',
              txt: '新增',
              click() {
                console.log('新增')
              }
            }, {
              type: 'danger',
              txt: '删除',
              click() {
                console.log('删除')
              }
            }, {
              type: 'success',
              txt: '生效',
              click() {
                console.log('生效')
              }
            }, {
              type: 'warning',
              txt: '失效',
              click() {
                console.log('失效')
              }
            }
          ]
        }
      },
      /*
        列表头部搜索区域的placeholder值
      */
      searchHolder: {
        type: String
      },
      /*pageSize:{
        type:Number,
        default(){
          return 50;
        }
      },*/
      /*
      搜索的表名
      */
      searchPage: String,

      // 表格数据
      data: {
        type: Array,
        required: true
      },
      selectData: {
        type: Array
      },
      /*
        表格列声明
        [{
          label:String,列名，必须
          prop:String,取值的字段，必须
          format:String,过滤器的名字，必须是全局注册的过滤器，可选
          formatParams:String,过滤器的第一个参数，目前只支持一个参数的过滤器，可选
          redirectClick(d){	点击跳转时的回调函数，d为当前行数据，有此函数默认为超链接
            self.mod(d.code)
          },
          sort: true, 是否排序，可选，默认不排序，点击排序时会派发sort-change事件，
          formatter:function(),		数据过滤函数，返回当前列所显示的值。formatter(scope.row[col.prop])
          html(val){}:根据返回的结果，输出html
        }]
      */
      colData: {
        type: Array,
        required: true
      },
      /*
        是否显示选择框;
        checkbox为多选
        radio为单选，
        其它值则不显示
        默认多选
      */
      selection: {
        type: String,
        default() {
          return 'checkbox'
        }
      },
      // 总数据值，用于分页,如果没有此参数，则不显示分页
      pageTotal: {
        type: Number
      },
      // 每页显示数，默认50，可为空
      pageLength: Number,
      // 是否显示头部工具条
      showHead: {
        type: Boolean,
        default: true
      },
      // 是否显示表头 默认为显示
      showHeader: {
        type: Boolean,
        default: true
      },
      // 所选条数根据给定的key过滤出不重复的条数，当为true时不过滤重复项，选多少行就多少条
      showSelectRowNumByOnlyKey: {
        type: [String, Boolean],
        default: false,
      },

      //是否需要序号
      orderNo: {
        type: Boolean,
        default: false
      },

      /*
      *是否需要行单击双击事件
      **/
      isNeedClickEvent: {
        type: Boolean,
        default: false
      },
      //搜索参数
      simpleSearch: {
        type: Boolean,
        default: false
      },
      //绑定行背景色
      taggelClassName: {
        type: Function,
        default: () => {
          return '';
        }
      },
      //是否显示合计行
      summaryMethod: Function,
      //绑定禁掉的选项
      selectable: {
        type: Function,
        default: () => {
          return true;
        }
      },

      // 单选框是否可选状态控制
      radioDisabled: {
        type: Function,
        default: () => {
          return false;
        }
      },

      // 是否显示序号
      orderNoShow: {
        type: Function,
        default: () => {
          return true;
        }
      },
      ifExpand: {
        type: Boolean,
        default: false
      },
      ifExpandMessage: {
        type: Boolean,
        default: false
      },
      fullHeight: null

    },
    watch: {
      radioSelect(n, o) {
        // 单选值改变时派发事件：radio-change
        this.setSelect([n])
        this.$emit('radio-change', n)
      },
      data: {
        handler() {
          console.warn('data change')

          // this.formatterSavePropOneTime.isSourceDataChange = true
          // this.formatterSavePropOneTime.isSelectChangeIng = false
          // setTimeout(() => {
          // 	//这个callback会在formatterSavePropOneTime所有渲染完之后执行
          // 	this.formatterSavePropOneTime.isSourceDataChange = false
          // })

          this.formatterSavePropOneTime.isSelectChangeIng = false

          this.data.forEach((obj, index) => {
            // 创建不可枚举属性，对外界无副作用
            // console.log(index)
            Object.defineProperties(obj, {
              __selected: {
                configurable: true,
                writable: true,
                enumerable: false,
              },
              __index: {
                configurable: true,
                writable: true,
                enumerable: false,
              },
              // __dataIndex:{
              // 	value: index,
              //    				configurable: true
              // }
            })
          })
          this.$refs.table.clearSelection()//一定要清空所有选中项，防止alert组件选中的__select为true的带到其它list导致this.selectRows不对

          this.newList = this.largeDataScrollRender(this.data);
          // console.log(this.newList)
        }, deep: true
      },
    },
    methods: {

      init() {
        // 保存初始数据
        this.colsConfig = [...this.colData]
        // 异步更新数据
        this.getColConfig().then((config) => {
          this.matchColConfig(config)
        }).catch((e) => {
          this.$message.error(e)
        })
      },
      /**
       * 获取默认表格列配置
       * @param key
       */
      getColConfig() {
        return new Promise((reslove, reject) => {
          const params = {
            user_id: fn.getUserInfo('id'),
            page_code: this.searchPage,
            type: 'show'
          }
          this.ajax.postStream('/user-web/api/new/showDesc', params, res => {
            if(!res.body.result) {
              reject('获取默认显示方案失败！')
              return
            }
            if(res.body.content[0]) {
              try {
                const fileds = JSON.parse(res.body.content[0].query_json)
                reslove(fileds.data)
              } catch (e) {
                reject('默认显示方案数据异常！')
                return;
              }
            } else {
              reslove(res.body.content.data)
            }


          }, err => {
            this.$message.error(err);
          });
        })
      },
      /**
       * 根据接口响应的列配置和本地的列配置糅合出需要展示的列配置
       * @param remoteConfig 接口返回的配置
       */
      matchColConfig(remoteConfig) {
        remoteConfig.forEach((ritem) => {
          let item = this.colData.find(li=>li.prop === ritem.prop)
          if (item){
            ritem = Object.assign(ritem,item)
          }else {
            ritem.width = 150
          }
        })
        this.colsConfig = remoteConfig.length===0?this.colData:remoteConfig
      },

      handleResize() {
        if (this.fullHeight) {
          this.innerHeight = window.innerHeight - this.fullHeight
        } else {
          this.innerHeight = window.innerHeight - 150
        }
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
      formatterSavePropOneTime(scope, col, index) {
        // 点击checkbox时不再逐个翻译，取下面初始化的翻译
        // 这样(col.prop || '') + index的原因：比如售后单列表colData里面status用了两次，后面的__status会覆盖了前面的翻译
        var row = scope.row
          , __key = '__' + (col.prop || '') + index
          , __val = row[__key]

        if (this.formatterSavePropOneTime.isSelectChangeIng) return __val

        var newVal = col.format
          ? this.filters[col.format](row[col.prop], col.formatParams)
          : (col.formatter ? col.formatter(row[col.prop], scope.$index, row) : row[col.prop])
        // 注意col.formatter方法里面可能包含某些逻辑(不是简单的key-value翻译)
        // 这种情况不能取初始化的翻译scope.row['__' + col.prop]
        Object.defineProperties(row, {
            [__key]: {
              configurable: true,
              writable: true,
              enumerable: false,
              value: newVal,
            },
          }
        )

        return newVal
      },
      selectAllHandler(selection) {
        var isSelectAllTrue = selection.length !== 0

        console.log('isSelectAllTrue', isSelectAllTrue)
        this.data.forEach(obj => {
          if (this.selectable(obj)) {
            obj.__selected = isSelectAllTrue
          }
        })

        this.setSelect(selection)
      },
      selectHandler(selection, row) {
        row.__selected = !row.__selected
      },
      largeDataScrollRender(dataList) {
        var self = this
          , maxLength = 1100
          , renderLength = 50
          , trHeight = 25

        if (this.largeDataScrollRender.initEvent) {
          /*empty*/
        } else if (dataList.length > maxLength) {
          this.largeDataScrollRender.initEvent = true
          this.$refs.table.$el.querySelector('.el-table__body-wrapper').addEventListener('scroll', function () {
            if (
              (
                (this.scrollTop === 0 && self.newList[0].__index !== 1) ||
                (this.scrollTop.toFixed(0)/*解决浏览器调整比例大小导致不能翻页*/ == this.scrollHeight - this.clientHeight && self.newList.slice(-1)[0].__index % renderLength === 0)
              )
              && !self.largeDataScrollRender.isRendering
            ) {
              self.largeDataScrollRender.isRendering = 'rendering'
              var _scrollTop
                , _sliceStart
                , _sliceEnd

              if (this.scrollTop === 0) {
                var _headIndex = self.newList[0].__index - renderLength - 1

                _sliceStart = _headIndex < 0 ? 0 : _headIndex
                _sliceEnd = _headIndex < 0 ? maxLength : self.newList[0].__index + renderLength - 1
                self.newList = self.largeDataScrollRender.saveList.slice(_sliceStart, _sliceEnd)
                _scrollTop = _headIndex < 0 ? 0 : renderLength * trHeight
                console.warn('largeDataScrollRender', _sliceStart, _sliceEnd, _scrollTop)
              } else {
                _sliceStart = self.newList.slice(-1)[0].__index - renderLength
                _sliceEnd = self.newList.slice(-1)[0].__index + renderLength
                self.newList = self.largeDataScrollRender.saveList.slice(_sliceStart, _sliceEnd)
                _scrollTop = renderLength * trHeight - this.clientHeight
                console.warn('largeDataScrollRender', _sliceStart, _sliceEnd, _scrollTop)
              }
              self.$nextTick(() => {
                this.scrollTop = _scrollTop
                self.newList.forEach(obj => {
                  if (obj.__selected) {
                    self.$refs.table.__toggleRowSelection(obj, true)
                  }
                })
                self.largeDataScrollRender.isRendering = false
              })
            }
          })
        }

        if (dataList.length > maxLength) {
          self.largeDataScrollRender.isRendering = false
          this.largeDataScrollRender.saveList = dataList.map((obj, index) => {
            obj.__index = index + 1
            return obj
          })
          return this.largeDataScrollRender.saveList.slice(0, maxLength)
        } else {
          self.largeDataScrollRender.isRendering = true
          return dataList
        }
      },

      /**
       *禁掉下拉框的一些选项
       *row,行数据
       ***/
      isDisabled(col, key) {
        console.log('col', col);//行数据
        let options = col.disableOption;//[]
        if (!options || !options.length) {
          console.log('看看是什么呀呀呀');
          return false;
        }
        let i = options.length;
        for (let a = 0; a < i; a++) {
          let value = options[a];
          if (value == key) {
            return true;
          }
        }
        console.log('end');
        return false;
      },
      searchClick(data, callback) {
        this.$emit('search-click', data, callback)
      },
      searchClicks(list, resolve, isFromSelectPlan) {
        // 点击自定义搜索时派发事件：search-click
        this.$emit('search-click', list, resolve, isFromSelectPlan)
      },
      checkBoxSelect(s) {
        // if(this.formatterSavePropOneTime.isSourceDataChange){
        // 	this.formatterSavePropOneTime.isSelectChangeIng = false
        // 	// 当发生this.data改变时this.formatterSavePropOneTime还是执行逐个翻译，因为新this.data已经没有了scope.row['__' + col.prop]数据
        // }else {
        // }

        if (this.rowClick.isClick) {//只针对点击行时不进行多次翻译，取直译
          this.formatterSavePropOneTime.isSelectChangeIng = true
        }

        window.clearTimeout(this.checkBoxSelect.setTimer)
        this.checkBoxSelect.setTimer = window.setTimeout(() => {
          this.formatterSavePropOneTime.isSelectChangeIng = false
          // console.log(s)

          this.setSelect(s)
          // 多选时派发事件：selection-change
          this.selectRows = this.data.filter(obj => obj.__selected)

          if (typeof this.showSelectRowNumByOnlyKey === 'string') {
            this.selectRowNumByKey = Array.from(new Set(this.selectRows.map(obj => obj[this.showSelectRowNumByOnlyKey])))
          }

          // console.log('selection-change')
          // console.log('this.selectRows123',this.selectRows)
          this.$emit('selection-change', this.selectRows)
        }, 50)
      },
      pageChange(ps) {
        // 每页显示数改变时派发事件：page-size-change
        this.$emit('page-size-change', ps)
        this.pageSize = ps;
      },
      currentPageChange(p) {
        // 当前页码改变时派发事件：current-page-change
        this.$emit('current-page-change', p)
        this.pageNow = p;
      },
      // 设置选择中值,row为要选中的行
      setData(row) {
        this.$refs.table.toggleRowSelection(row)
      },
      // 清除多选中值
      clearSelection() {
        this.$refs.table.clearSelection();
      },
      // 清除单选选中值
      clearRadioSelect() {
        this.radioSelect = ''
      },
      setRadioSelect(select) {
        this.radioSelect = select;
      },
      /*
      排序
      order为descending为降序，为ascending为升序
      */
      sortChange(col) {
        // 点击排序时派发事件：sort-change
        this.$emit('sort-change', col)
      },
      /*
      *表格行双击
      *主要是针对弹窗
      */
      rowDblclick(obj) {
        if (!(this.selectable(obj) && !this.radioDisabled(obj))) {//禁掉的选项不触发事件
          return
        }

        // 派发row-dblclick事件
        var type = this.selection;
        if (this.isNeedClickEvent && type == 'checkbox') {
          //多选的时候
          this.$refs.table.toggleRowSelection(obj)
        }
        this.$emit('row-dblclick', obj);
      },
      /**
       *表格行单击
       **/
      rowClick(obj, e, col) {
        this.rowClick.isClick = true
        setTimeout(() => {
          this.rowClick.isClick = false
        })
        if (!(this.selectable(obj, e, col) && !this.radioDisabled(obj))) {//禁掉的选项不触发事件
          return;
        }
        let type = this.selection,
          width = this.$el.clientWidth,
          isAlert = false;
        // 根据窗口宽度判断是否为弹出框，小于1200则为弹出框
        if (width && width < 1200) {
          isAlert = true;
        }
        if (isAlert || this.isNeedClickEvent) {
          if (type == 'checkbox') {
            //多选的时候
            // console.log(obj)

            this.$refs.table.toggleRowSelection(obj);
          } else if (type == 'radio') {
            //单选的时候直接用watch去触发
            this.radioSelect = obj;
          }
        }
        this.$emit('row-click', obj, e, col);
      },
      // 表格列单击
      cellClick(obj, col, cell, event) {
        this.$emit('cell-click', obj, col, cell, event);
      },
      optionFilter(val, options) {
        let i = options.length;
        while (i--) {
          if (options[i].value == val) {
            return options[i].label
          }
        }
      },
      selectAll(selection) {
        let trList = this.$el.querySelectorAll('tr'),
          i = trList.length;
        while (i--) {
          if (i > 0) {
            if (selection.length) {
              if (trList[i].className.indexOf('xpt-select') == -1) {
                trList[i].className += ' xpt-select'
              }
            } else {
              trList[i].className = trList[i].className.replace(new RegExp(' xpt-select', 'ig'), '');
            }
          }
        }
      },
      setSelect(selection) {
        let i = selection.length,
          selectionSet = new Set(),
          indexSet = new Set();

        while (i--) {
          selectionSet.add(JSON.stringify(selection[i]))
        }
        i = this.newList.length;
        while (i--) {
          if (selectionSet.has(JSON.stringify(this.newList[i]))) {
            indexSet.add(i + 1)
          }
        }
        let trList = this.$el.querySelectorAll('tr');
        i = trList.length;
        while (i--) {
          if (indexSet.has(i)) {
            if (trList[i].className.indexOf('xpt-select') == -1) {
              trList[i].className += ' xpt-select'
            }
          } else {
            trList[i].className = trList[i].className.replace(new RegExp(' xpt-select', 'ig'), '');
          }
        }
      },


      // 重写ref="table"的clearSelection和toggleRowSelection方法
      _overrideTableHandler() {
        var toggleRowSelection = this.$refs.table.toggleRowSelection
          , clearSelection = this.$refs.table.clearSelection

        this.$refs.table.__toggleRowSelection = toggleRowSelection
        this.$refs.table.toggleRowSelection = (obj, bool) => {
          toggleRowSelection(obj, bool)
          this.selectHandler(null, obj)
        }

        this.$refs.table.clearSelection = () => {
          this.data.forEach(obj => {
            obj.__selected = false
          })
          clearSelection()
        }
      },

      getCellClassName(col, $style) {
        let className = col.isTextarea ? "xpt-td__row" : (col.ifRequire ? $style['th-required'] : $style["hover-index-on-tooltip"]);
        if (col.getCellClassName) {
          className = className + ' ' + col.getCellClassName();
        }
        return className;
      },
      getSelectRows() {
        return this.selectRows;
      }
    },
    mounted() {
      this._overrideTableHandler()
      this.filters = this.$root.$options.filters
      this.init()
    },
    components: {
      MdmSearchEx,
      slotItem: {
        props: ['slots', 'row'],
        render(h) {
          let vnode = this.slots({
            row: this.row
          });
          return h('div', vnode);
        }
      }
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
    },
    created(){
      window.addEventListener('resize', this.handleResize)
      this.handleResize()
    }
  }
</script>
<style>
  .mdm-list2 .el-table td.gutter, .el-table th.gutter{
    width: 10px!important;
  }
</style>

<style type="text/css" scoped>
  .el-table{}
  .xpt-list{}
  .pmm_input .el-input, .pmm_input .el-select, .pmm_input .el-date-editor.el-input {
    width: 100%;
    max-height: 100%;
  }

</style>
<!-- 下面css是隐藏最后一页和后面"..."的按钮 -->
<style module>
  .hover-index-on-tooltip :global(.el-tooltip):hover {
    position: relative;
    z-index: 111111;
  }

  .hide-last-page :global(.btn-quicknext.el-icon-more) {
    display: none;
  }

  .hide-last-page :global(.btn-quicknext.el-icon-more + li.number) {
    display: inline-block;
    font-size: 0 !important;
    min-width: 1px;
    padding: 0;
    border-right: none;
  }
  th.th-required :global(.cell:before) {
    content: '*';
    color: red;
    position: absolute;
    left: 3px;
    top: 3px;
  }
</style>
