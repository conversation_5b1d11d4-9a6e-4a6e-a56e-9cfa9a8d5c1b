<!--树组件，物料列表左边的树-->
<template>
	<div style="display: flex;flex-direction: column;height: 100%">
		<div class="tree-btn">
			<span v-if="isAdd" @click="add">新增分组</span>
			<span v-if="isEdit" @click="edit">编辑分组</span>
			<span v-if="isDel" @click="del">删除分组</span>
		</div>
		<el-input
			placeholder="输入关键字进行过滤"
			size="mini"
			style="width: 100%;margin-bottom: 10px;"
			v-model="filterText">
		</el-input>
		<div class="tree-list" style="flex: 1;overflow:auto;">
			<el-tree
				style="margin: 0 5px;"
				ref="tree"
				highlight-current
				@node-click="onNodeClick"
				:default-expanded-keys="['000']"
				:data="data"
				:props="defaultProps"
				:filter-node-method="filterNode"
				:node-key="code">
			</el-tree>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		isAdd: {
			type: Boolean,
			default: true,
		},
		isEdit: {
			type: Boolean,
			default: true,
		},
		isDel: {
			type: Boolean,
			default: true,
		},
		url: {
			type: String,
			default: null
		},
		code: {
			type: String,
			default: 'id'
		},
		label: {
			type: String,
			default: 'label'
		},
		children: {
			type: String,
			default: 'children'
		},
		params: {
			type: Object,
			default: null
		},
		formatData: {
			type: Function,
			default: (res) => {
				return res.body.content || []
			}
		},
		queryType: {
			type: String,
			default: 'POST'
		}
	},
	watch: {
		filterText(val) {
			this.$refs.tree.filter(val);
		}
	},
	methods: {
		add() {
			this.$emit('add', this.obj)
		},
		edit() {
			if (!this.obj) {
				this.$message({
					type: 'error',
					message: '请选择要修改的节点'
				});
				return
			}
			this.$emit('edit', this.obj)
		},
		del() {
			if (!this.obj) {
				this.$message({
					type: 'error',
					message: '请选择要删除的节点'
				});
				return
			}
			this.$emit('del', this.obj)
		},
		onNodeClick(obj, node, data) {
			this.obj = obj
			this.$emit('node-click', obj)
		},
		filterNode(value, data) {
			if (!value) return true;
			return data[this.label].indexOf(value) !== -1;
		},
		query() {
			this.loading = true
			if (this.queryType === 'GET') {
				this.ajax.get(this.url,  (res) => {
					this.loading = false
					if (res.body.result && res.body.content) {
						let parentInfo = {}
						parentInfo[this.code] = '000'
						parentInfo[this.label] = '全部'
						parentInfo[this.children] = this.formatData(res)
						this.data = [parentInfo]
					}
				});
			} else {
				this.ajax.postStream(this.url, this.params || {}, (res) => {
					this.loading = false
					if (res.body.result && res.body.content) {
						let parentInfo = {}
						parentInfo[this.code] = '000'
						parentInfo[this.label] = '全部'
						parentInfo[this.children] = this.formatData(res)
						this.data = [parentInfo]
					}
				});
			}
		}
	},
	data() {
		return {
			obj: null,
			filterText: '',
			data: [],
			defaultProps: {
				children: null,
				label: null,
			},

			loading: false,
		};
	},
	created() {
		this.defaultProps = {
			children: this.children,
			label: this.label,
		}
		this.query()
	}
};
</script>

<style scoped>

.tree-list::-webkit-scrollbar {
	width: 10px;
}

.tree-list::-webkit-scrollbar-track-piece {
	width: 0;
	background: #CCC;
}

.tree-list::-webkit-scrollbar-thumb:vertical {
	height: 0px;
	background: #999;
	-webkit-border-radius: 6px;
}

.tree-list::-webkit-scrollbar-thumb:horizontal {
	height: 10px;
	background: #999;
}

.tree-btn {
	width: 100%;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	margin-bottom: 5px;
	background-color: #e4e8f1;
}

.tree-btn span {
	padding: 5px;
	flex: 1;
	text-align: center;
}

.tree-btn span:hover {
	background-color: #20a0ff;
	color: white;
	cursor: pointer;
}

</style>
