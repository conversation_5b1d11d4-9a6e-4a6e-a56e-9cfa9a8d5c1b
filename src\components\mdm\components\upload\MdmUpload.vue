<template>
  <div>
    <el-upload
      class="avatar-uploader"
      action=""
      :disabled="readonly"
      :http-request="handUpload"
      :before-upload="handBeforeUpload"
      :on-success="handleSuccess"
      :show-file-list="false">
      <el-popover
        v-if="imageUrl"
        ref="popover1"
        placement="right-start"
        title="产品图片操作"
        trigger="hover">
        <el-button :disabled="readonly" type="danger" size="small" icon="delete"
                   @click="()=>{
                   this.imageUrl = ''
                   this.info = null
                 }">删除</el-button>
        <el-button type="primary" size="small" icon="view"
                   @click="()=>{
                   this.dialogVisible = true
                 }">预览</el-button>
      </el-popover>
      <img v-if="imageUrl" v-popover:popover1 :src="imageUrl" class="avatar" />
      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>

    <el-dialog v-model="dialogVisible" size="large">
      <template slot="title">
        <el-button @click="downUrl" type="primary">下载<i class="el-icon-upload el-icon--right"></i></el-button>
        <span>{{imageName}}</span>
      </template>
      <img :src="imageUrl" alt="" style="width: 100%;height: 100%">
    </el-dialog>
  </div>

</template>

<style>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 300px;
  }
  .avatar-uploader .el-upload--text{
    width: 100%;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #20a0ff;
  }
  .avatar-uploader-icon {
    font-size: 40px !important;
    color: #8c939d;
    height: 500px;
    width: 500px;
    line-height: 500px;
    text-align: center;
  }
  .avatar {
    width:  100%;
    height: 500px;
    display: block;
  }
</style>

<script>
  import OSSUtils from '@components/mdm/utils/aliOss'
  import COMMO from '@components/mdm/utils/commo'
  export default {
    props:['list','readonly'],
    data() {
      return {
        imageUrl: '',
        imageName:'',
        info:null,
        dialogVisible:false,
        changeStatus:false,
      };
    },
    computed:{
      isReadonly(){
        return {
          'backgroundColor': '#dfe6ec',
          'zIndex': '999',
          'position': 'relative',
          'top': '-502px',
          'height': '500px',
          'borderRadius': '5px',
          'cursor': 'not-allowed',
          'opacity': -0.7,
        }
      }
    },
    methods: {
      downUrl(){
        location.href = this.imageUrl
      },
      async handUpload(option){
        let result = await OSSUtils.singleUpload(option)
        if (result.statusCode === 200){
          return result
        }
        return false
      },
      handleSuccess(res, file) {
        this.info = {fileName:file.name,aliasName:res.name,suffix:COMMO.getExtensionFileName(file.name),fileSize:file.size,path:res.url}
        this.imageUrl = URL.createObjectURL(file.raw);
        this.imageName = file.name;
      },
      handBeforeUpload(file) {
        const isImg = ['image/jpeg','image/png','image/gif']
        const isJPG = isImg.indexOf(file.type)>-1;
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isJPG) {
          this.$message.error('只能上传(jpg,png,git)图片格式!');
        }
        return isJPG;
      }
    },
    created(){
      if (this.list&&this.list.length>0){
        this.imageUrl = this.list[0].path
        this.imageName = this.list[0].fileName
        this.info = this.list[0]
      }

      this.$nextTick(()=>{
        this.$watch('info',()=>{
          console.log('------------------图片信息有改动---------------------')
          this.changeStatus = true
        },{deep:true})
      })
    }
  }
</script>
