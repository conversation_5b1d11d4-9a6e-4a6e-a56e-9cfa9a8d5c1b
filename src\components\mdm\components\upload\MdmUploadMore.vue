<template>
  <div>
    <el-upload
      class="uploadMore"
      action=""
      :disabled="readonly"
      multiple
      list-type="picture-card"
      :file-list="fileList"
      :http-request="handUpload"
      :before-upload="handBeforeUpload"
      :on-change="handleChange"
      :on-success="handleSuccess"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove">
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog v-model="dialogVisible" size="large">
      <template slot="title">
        <el-button @click="downUrl" type="primary">下载<i class="el-icon-upload el-icon--right"></i></el-button>
        <span>{{dialogImageName}}</span>
      </template>
      <img :src="dialogImageUrl" alt="" style="width: 100%;height: 100%">
    </el-dialog>
  </div>
</template>
<script>
  import OSSUtils from "@components/mdm/utils/aliOss";
  import COMMO from "@components/mdm/utils/commo";

  export default {
    props:['list','readonly'],
    data() {
      return {
        dialogImageUrl: '',
        dialogImageName: '',
        dialogVisible: false,
        changeStatus: false,
        fileList:[]
      };
    },
    methods: {
      downUrl(){
        OSSUtils.signatureUrlByName(this.dialogImageUrl)
      },
      getInfo(){
        let list = this.fileList.map(item=>{
          let info = {}
          info.fileName = item.name||item.fileName
          info.aliasName = item.aliasName
          info.suffix = COMMO.getExtensionFileName(item.name||item.fileName)
          info.fileSize = item.size||item.fileSize
          info.path = item.path
          return info
        })
        return list
      },
      async handUpload(option){
        let result = await OSSUtils.singleUpload(option)
        if (result.statusCode === 200){
          return result
        }
        return false
      },
      handleSuccess(res, file) {
        this.fileList.filter(item=>{
          if (item.uid === file.uid){
            item.path = res.url
            item.aliasName = res.fileName
          }
        })
      },
      handleChange(file, fileList) {
        this.fileList = fileList
      },
      handBeforeUpload(file) {
        const isImg = ['image/jpeg','image/png','image/gif']
        const isJPG = isImg.indexOf(file.type)>-1;
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isJPG) {
          this.$message.error('只能上传(jpg,png,git)图片格式!');
        }
        return isJPG;
      },
      handleRemove(file, fileList) {
        let uid = file.uid||file.propertyId
        if (uid){
          this.fileList.forEach((item,index)=>{
            if ((item.uid === uid)||(item.propertyId === uid)){
              this.fileList.splice(index,1)
            }
          })
        }
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogImageName = file.fileName||file.name;
        this.dialogVisible = true;
      }
    },
    computed:{
      isReadonly(){
        return {
          'backgroundColor': '#dfe6ec',
          'zIndex': '999',
          'position': 'absolute',
          'top': '34px',
          'height': ((this.fileList.length*250)+100)+'px',
          'width':'325px',
          'borderRadius': '5px',
          'cursor': 'not-allowed',
          'opacity': -0.7,
        }
      }
    },
    created(){
      if (this.list&&this.list.length>0){
        this.fileList = this.list.map(item=>{
          item.url = item.path
          return item
        })
      }

      this.$nextTick(()=>{
        this.$watch('fileList',()=>{
          console.log('------------------图片信息有改动---------------------')
          this.changeStatus = true
        },{deep:true})
      })
    }
  }
</script>
<style>
  .uploadMore .el-upload-list__item-preview i{
    font-size: xx-large !important;
  }
  .uploadMore .el-upload-list__item-delete i{
    font-size: xx-large !important;
  }
  .uploadMore .el-upload--picture-card{
    width: 80px !important;
    height: 80px !important;
    line-height: 80px !important;
  }
  .uploadMore .el-upload-list__item , .el-upload-list__item img{
    width: 100% !important;
    height: 200px;
  }
</style>
