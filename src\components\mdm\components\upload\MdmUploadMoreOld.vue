<template>
  <div>
    <el-upload
      action=""
      multiple
      list-type="picture-card"
      :file-list="fileList"
      :http-request="handUpload"
      :before-upload="handBeforeUpload"
      :on-success="handleSuccess"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove">
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog v-model="dialogVisible" size="tiny">
      <img :src="dialogImageUrl" alt="">
    </el-dialog>

    {{fileList.length}}
  </div>
</template>
<script>
  import OSSUtils from "@components/mdm/utils/aliOss";
  import COMMO from "@components/mdm/utils/commo";

  export default {
    data() {
      return {
        dialogImageUrl: '',
        dialogVisible: false,
        fileList:[]
      };
    },
    methods: {
      async handUpload(option){
        console.log('handUpload',option)
        let result = await OSSUtils.singleUpload(option)
        if (result.statusCode === 200){
          this.fileList.push(option.file)
          return result
        }
        return false
      },
      handleSuccess(res, file) {
      },
      handBeforeUpload(file) {
        const isJPG = file.type === 'image/jpeg';
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isJPG) {
          this.$message.error('只能上传图片格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      },
      handleRemove(file, fileList) {
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      }
    }
  }
</script>
<style>
  .el-upload--picture-card{
    width: 100px !important;
    height: 100px !important;
    line-height: 100px !important;
  }
  .el-upload-list__item , .el-upload-list__item img{
    width: 100px !important;
    height: 100px !important;
  }
</style>
