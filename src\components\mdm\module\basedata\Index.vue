<!--定时任务管理-->
<template>
  <mdm-list
    ref='list'
    :data='list'
    :btns='btnList'
    :colData='cols'
    :pageTotal='count'
    :orderNo="true"
    row-key="id"
    @selection-change='handleSelectionChange'
  >
  </mdm-list>
</template>

<script>
  import MdmList from '@/components/mdm/components/table/MdmList'

  export default {
    components: {MdmList},
    props: ["params"],
    data() {
      let _this = this;
      return {
        isfuture : false,
        search: {
          page_name: "md_material",
          where: [],
          page_size: _this.pageSize,     //页数
          page_no: 1   //页码
        },
        exportBtnStatus: false,
        list: [],
        count: 0,
        selectedData: [],
        btnList: [
          {
            type: 'primary',
            txt: '同步最新数据',
            click: _this.sync,
            loading: false
          }
        ],
        cols: [
          {label: '基础数据类型', prop: 'typeName', width: 150},
          {label: '类型代码', prop: 'typeCode', width: 240},
          {label: '备注', prop: 'remark'}
        ]
      }
    },
    methods: {
      showFurture(){
        this.isfuture = !this.isfuture;
        var datas = this.isfuture==true?this.getDatas():this.getDatas().filter(item=> item.materialNumber!='');
        this.list = datas;
        this.count = 10;
      },
      selectable(row,id){
        if( row && row["materialNumber"]){
          return true;
        }
        return false;
      },
      searching(resolve) {
        /*
        var datas = this.getDatas().filter(item=> item.materialNumber!='');
        this.list = datas;
        this.count = 50;
        */
        let url = '/mdm-web/api/basedata/searchTypes?permissionCode=MDM_BASEDATA_LIST';
        this.ajax.postStream(url, {}, (res) => {
          if (res.body.result && res.body.content) {
            this.list = res.body.content || [];
            this.count = this.list.length;
          }
          if(res.body.result==='error'){
            this.$message({
              type: res.body.result,
              message: res.body.msg
            });
          }
          resolve && resolve();
        });
      },
      handleSelectionChange(selects) {
        this.selectedData = selects
      },
      searchClick(val, resolve) {
        this.search.where = val;
        this.searching(resolve)
      },
      sizeChange(ps) {
        this.search.page_size = ps;
        this.searching()
      },
      pageChange(page) {
        this.search.page_no = page;
        this.searching()
      },
      sync() {
        if(this.selectedData.length==0){
          this.$message.error(`请选择要同步的基础数据`);
          return;
        }
        var typeCodes = [];
        this.selectedData.forEach((item,index)=>{
          typeCodes.push(item.typeCode);
        })
        var url = '/mdm-job/api/sync/syncBasedatas?permissionCode=MDM_BASEDATA_SYNC';
        this.ajax.postStream(url, typeCodes, (res) => {
          this.$message({
            type: res.body.result ? 'success' : 'error',
            message: res.body.msg
          });
          this.searching();
        });
      }
    },
    mounted: function () {
      this.searching();
    }
  }
</script>

<style scoped>
  .column{
    color:red;
  }
</style>
