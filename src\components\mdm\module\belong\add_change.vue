
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='24'>
        <el-button type='info' size='mini' :disabled="canEdit" @click="save('SAVE')">保存</el-button>
        <el-button type='success' size='mini' :disabled="canEdit" @click="submitData">提交审核</el-button>
        <el-button type='success' size='mini' :disabled="submit.status != 'CREATE' || !submit.id" @click="cancelData">作废</el-button>
        <el-button type='info' size='mini' :disabled="canEdit" @click="init">刷新</el-button>
      </el-col>
    </el-row>
    <el-row	:gutter='40' >
      <el-tabs v-model="firstTab" >
        <el-tab-pane label="基础信息" name="awardList" style="height: 150px;">
          <el-form label-position="right" label-width="140px">
            <el-col :span="8" >
              <el-form-item label="申请单号：">
                <el-input v-model="submit.applyNo" size='mini' style="width: 200px;" disabled></el-input>

              </el-form-item>
              <el-form-item label="状态：">
                <!-- <el-input v-model="submit.status" size='mini' style="width: 200px;" disabled></el-input> -->
                {{status[submit.status]}}
              </el-form-item>
            </el-col>
            <el-col :span="8" >
              <el-form-item label="申请单名称：">
                <el-input v-model="submit.applyTitle" size='mini' style="width: 200px;" :disabled="canEdit" ></el-input>
              </el-form-item>
              <el-form-item label="生效时间：">
									<el-date-picker v-model="submit.enableTime" type="date" placeholder="选择日期" :disabled="canEdit" size='mini'></el-date-picker>
              </el-form-item>


            </el-col>
            <el-col :span="8">
              <el-form-item label="变更类型：">
                <el-select  size='mini'  placeholder="类型" v-model='submit.type' :disabled="changeList.length==0||canEdit||!!submit.id" @change="typeChange">
										<el-option label="商品类别" value="CATEGORY"></el-option>
										<el-option label="渠道专供" value="CHANNEL_FOR"></el-option>
										<el-option label="主理事业部" value="BUSINESS_DEPARTMENT" disabled></el-option>
										<el-option label="货品标签" value="PRODUCT_LABEL_AND_CHANNEL_FOR"></el-option>
										<el-option label="项目组" value="PROJECT_TEAM"></el-option>
									</el-select>
              </el-form-item>
              <el-form-item label="变更为：">
                <el-select placeholder="请选择" v-model="submit.targetValue" size='mini' :disabled="changeList.length==0||canEdit||!!submit.id"  @change="targetValueChange">
									    <el-option
									      v-for="(value,key) in option"
									      :key="key"
									      :label="value.name"
									      :value="value.val">
									    </el-option>
									 </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" >
              <el-form-item label="变更型号：">
                    <div style="width:100%;  overflow: auto;  height: 100px;">
                      <el-button type='success' size='mini' @click="add" :disabled="canEdit">添加</el-button>
                      <el-tag
                        :key="tag"
                        v-for="tag in submit.changeModel"
                        closable
                        :disable-transitions="canEdit"
                        @close="handleClose(tag)">
                        {{tag}}
                      </el-tag>
                    </div>


              </el-form-item>



            </el-col>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="其它信息" name="otherList">
          <el-form label-position="right" label-width="140px">
            <el-col :span="8" >
              <el-form-item label="创建时间：">
                <!-- <el-input v-model="submit.applyNo" size='mini' style="width: 200px;" disabled></el-input> -->
									<el-date-picker v-model="submit.createTime" type="date" placeholder="选择日期" disabled  size='mini'></el-date-picker>

              </el-form-item>
              <el-form-item label="创建人：">

                <el-input v-model="submit.creatorName" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" >
              <el-form-item label="提交时间：">
									<el-date-picker v-model="submit.submitTime" type="date" placeholder="选择日期" disabled  size='mini'></el-date-picker>
              </el-form-item>
              <el-form-item label="提交人：">
                <el-input v-model="submit.submitUser" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>


            </el-col>
            <el-col :span="8">
              <el-form-item label="审核时间：">
									<el-date-picker v-model="submit.approvalTime" type="date" placeholder="选择日期" disabled size='mini'></el-date-picker>

              </el-form-item>

            </el-col>

          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class='xpt-flex__bottom' id='bottom' v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane label="需调整物料清单" name="changeList" class='xpt-flex'>
          <xpt-list
            :data='changeList'
            :colData='cols'
            :btns="btns"
            selection='checkbox'
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
            @selection-change="radioChange"
          >
            <template slot='newChannelForCode' slot-scope='scope'>
              <div
                  v-if="submit.type == 'CHANNEL_FOR'"
                >
                 <el-select placeholder="请选择" v-model="submit.targetValue" size='mini' disabled  style="width:90px;">
									    <el-option
									      v-for="(value,key) in option"
									      :key="key"
									      :label="value.name"
									      :value="value.val">
									    </el-option>
									 </el-select>
                </div>
                <div  v-else>
                  <el-select size='mini' v-model='scope.row.newChannelForCode' :disabled="canEdit" style="width:90px;" @change="(val)=>{

                        newChannelForChange(scope.row,(data)=>{
                          if (submit.type === 'PRODUCT_LABEL_AND_CHANNEL_FOR') {
                            scope.row.categoryOption = data
                          }
                          if (submit.type === 'CATEGORY') {
                            scope.row.newProductLabelOption = data
                          }
                          if (submit.type === 'PROJECT_TEAM') {
                            scope.row.newProductLabelOption = data
                          }
                        })
                      }"  >
                    <el-option v-for='(item,index) in scope.row.channelForOption'
                      :key='index' :value='item.code' :label='item.name'></el-option>
                  </el-select>
                  <el-button  v-if="changeList.indexOf(scope.row) == 0" type='info' size='mini' :disabled="canEdit" @click="filling('CHANNEL_FOR')">填充</el-button>
                </div>


            </template>
            <template slot='newCategoryCode' slot-scope='scope'>
                <div
                  v-if="submit.type == 'CATEGORY'"
                >
                  <el-select placeholder="请选择" v-model="submit.targetValue" size='mini' disabled  style="width:90px;"  >
									    <el-option
									      v-for="(value,key) in option"
									      :key="key"
									      :label="value.name"
									      :value="value.val">
									    </el-option>
									 </el-select>
                </div>
                <div v-else>
                  <el-select size='mini' v-model='scope.row.newCategoryCode' :disabled="canEdit"  style="width:90px;"  @change="()=>{
                    newCategoryChange(scope.row,(data)=>{
                      if(submit.type == 'PROJECT_TEAM'){
                        scope.row.channelForOption = data;
                      }
                    })
                  }" >
                <el-option v-for='(item,index) in scope.row.categoryOption'

                  :key='index' :value='item.code' :label='item.name'></el-option>
              </el-select>
                  <el-button  v-if="changeList.indexOf(scope.row) == 0" type='info' size='mini' :disabled="canEdit" @click="filling('CATEGORY')">填充</el-button>
                </div>
            </template>
              <template slot='newProductLabelCode' slot-scope='scope'>
                <div
                  v-if="submit.type == 'PRODUCT_LABEL_AND_CHANNEL_FOR'"
                >
                  <el-select placeholder="请选择" v-model="submit.targetValue" size='mini' disabled  style="width:90px;">
									    <el-option
									      v-for="(value,key) in option"
									      :key="key"
									      :label="value.name"
									      :value="value.val">
									    </el-option>
									 </el-select>
                </div>
              <div v-else>

                <el-select size='mini'  style="width:90px;" v-model='scope.row.newProductLabelCode'  :disabled="canEdit">
                <el-option v-for='(item,index) in scope.row.newProductLabelOption'
                  :key='index' :value='item.code' :label='item.name'></el-option>
              </el-select>
                  <el-button  v-if="changeList.indexOf(scope.row) == 0" type='info' size='mini' :disabled="canEdit" @click="filling('PRODUCT_LABEL_AND_CHANNEL_FOR')">填充</el-button>
              </div>
            </template>
              <template slot='newProjectTeamCode' slot-scope='scope'>
                <div
                  v-if="submit.type == 'PROJECT_TEAM'"
                >
                  <el-select placeholder="请选择" v-model="submit.targetValue" size='mini' disabled   style="width:90px;" >
									    <el-option
									      v-for="(value,key) in option"
									      :key="key"
									      :label="value.name"
									      :value="value.val">
									    </el-option>
									 </el-select>
                </div>
                <div v-else>
                  <el-select size='mini' v-model='scope.row.newProjectTeamCode' :disabled="canEdit"  style="width:90px;">
                <el-option v-for='(item,index) in scope.row.newProjectTeamOption'
                  :key='index' :value='item.code' :label='item.name'></el-option>
              </el-select>
                  <el-button  v-if="changeList.indexOf(scope.row) == 0" type='info' size='mini' :disabled="canEdit" @click="filling('PROJECT_TEAM')">填充</el-button>

                </div>
            </template>
          </xpt-list>
        </el-tab-pane>
        <el-tab-pane label="不需调整物料清单" name="notChangeList" class='xpt-flex'>
          <xpt-list
            :data='notChangeList'
            :colData='notChangeCols'
            :btns="notChangeBtns"
            selection='checkbox'
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
            @selection-change="selectionChange"
          >
          <template slot='notChangeReason' slot-scope='scope'>
              <el-input v-model='scope.row.notChangeReason' size='mini' :maxlength='50' style="width:100px;"></el-input>
            </template>
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
import MdmSelect from "@components/mdm/components/form/MdmSelect";
import CacheUtils from "@components/mdm/utils/cacheUtils";
  import Fn from '@common/Fn.js'
    export default {
      name: "awardDetail",
      props:["params"],
	    components: {MdmSelect},
      data() {
        let self = this;
        return {
          status:{'CREATE':'创建','SUBMIT':'提交','AUDIT':'审核','REJECT':'驳回','RETRACT':'撤回','CANCEL':'作废'},
          isInit:false,
          option:[],
          changeConfig:{},
          select:null,
          notSelect:[],
          changeList:[],
          notChangeList:[],
          delList:[],
          submit:{
            id:'',
            applyTitle:'',
            type:'CATEGORY',
            targetValue:'',
            targetName:'',
            enableTime:'',
            status: 'CREATE',
            changeModel:[],
          },
          firstTab:"awardList",
          secondTab:"changeList",
          pageSize:50,
          pageNO:1,
          notChangeBtns:[
            {
              type: "danger",
              txt: "重新调整",
              loading: false,
              disabled(){
                return self.canEdit
              },
              click() {
                self.move();
              },
            },
          ],
          btns:[
            {
              type: "danger",
              txt: "不调整",
              loading: false,
              disabled(){
                return self.canEdit
              },
              click() {
                self.del();
              },
            },

          ],
          categoryCol :[

              {
                label: "型号",
                prop: "model",
                width: "80"
              },
              {
                label: "物料编码",
                prop: "materialNumber",
              },
              {
                label: "描述",
                prop: "materialSpecification",
              },
              {
                label: "主型号",
                prop: "mainModel",
              },
              {
                label: '是否停产',
                prop: 'is_stop',
                  formatter(val){
                  switch (val) {
                    case 1: return "是"; break;
                    case 0: return "否"; break;
                    default: return val; break;
                  }
                },
              },
              {
                label: "商品类别",
                prop: "categoryName",
              },
              {
                label: "新商品类别",
                slot: "newCategoryCode",
                width:160
              },

              {
                label: "渠道专供",
                prop: "channelForName",
              },
              {
                 label: "新渠道专供",
                slot: "newChannelForCode",
                width:160
              },
              {
                label: "货品标签",
                prop: "productLabelName",
              },
              {
                label: "新货品标签",
                slot: "newProductLabelCode",
                width:160
              },
              {
                label: "项目组",
                prop: "projectTeamName",
              },
               {
                label: "新项目组",
                slot: "newProjectTeamCode",
                width:160
              },
          ],
          productLabelCol :[
              {
                label: "型号",
                prop: "model",
                width: "80"
              },
              {
                label: "物料编码",
                prop: "materialNumber",
              },
              {
                label: "描述",
                prop: "materialSpecification",
              },
              {
                label: "主型号",
                prop: "mainModel",
              },
              {
                label: '是否停产',
                prop: 'is_stop',
                  formatter(val){
                  switch (val) {
                    case 1: return "是"; break;
                    case 0: return "否"; break;
                    default: return val; break;
                  }
                },
              },
              {
                label: "货品标签",
                prop: "productLabelName",
              },
              {
                label: "新货品标签",
                slot: "newProductLabelCode",
                width:160
              },
              {
                label: "渠道专供",
                prop: "channelForName",
              },
              {
                label: "新渠道专供",
                slot: "newChannelForCode",
                width:160
              },
              {
                label: "商品类别",
                prop: "categoryName",
              },
              {
                label: "新商品类别",
                slot: "newCategoryCode",
                width:160
              },
              {
                label: "项目组",
                prop: "projectTeamName",
              },
              {
                label: "新项目组",
                slot: "newProjectTeamCode",
                width:160
              },
          ],
          businessDepartmentCol : [

              {
                label: "型号",
                prop: "model",
                width: "80"
              },
              {
                label: "物料编码",
                prop: "materialNumber",
              },
              {
                label: "描述",
                prop: "materialSpecification",
              },
              {
                label: "主型号",
                prop: "mainModel",
              },
              {
                label: '是否停产',
                prop: 'is_stop',
                  formatter(val){
                  switch (val) {
                    case 1: return "是"; break;
                    case 0: return "否"; break;
                    default: return val; break;
                  }
                },
              },
              {
                label: "商品类别",
                prop: "categoryName",
              },

              {
                label: "新商品类别",
                slot: "newCategoryCode",
                width:160
              },
              {
                label: "渠道专供",
                prop: "channelForName",
              },
              {
                 label: "新渠道专供",
                slot: "newChannelForCode",
                width:160
              },

              {
                label: "项目组",
                prop: "projectTeamName",
              },
               {
                label: "新项目组",
                slot: "newProjectTeamCode",
                width:160
              },
          ],
          projectTeamCol: [

              {
                label: "型号",
                prop: "model",
                width: "80"
              },
              {
                label: "物料编码",
                prop: "materialNumber",
              },
              {
                label: "描述",
                prop: "materialSpecification",
              },
              {
                label: "主型号",
                prop: "mainModel",
              },
              {
                label: '是否停产',
                prop: 'is_stop',
                  formatter(val){
                  switch (val) {
                    case 1: return "是"; break;
                    case 0: return "否"; break;
                    default: return val; break;
                  }
                },
              },
              {
                label: "项目组",
                prop: "projectTeamName",
              },
              {
                label: "新项目组",
                slot: "newProjectTeamCode",
                width:160
              },
              {
                label: "商品类别",
                prop: "categoryName",
              },
              {
                label: "新商品类别",
                slot: "newCategoryCode",
                width:160
              },
              {
                label: "渠道专供",
                prop: "channelForName",
              },
              {
                 label: "新渠道专供",
                slot: "newChannelForCode",
                width:160
              },
              {
                label: "货品标签",
                prop: "productLabelName",
              },
              {
                label: "新货品标签",
                slot: "newProductLabelCode",
                width:160
              },

          ],
          channelForCol:[

              {
                label: "型号",
                prop: "model",
                width: "80"
              },
              {
                label: "物料编码",
                prop: "materialNumber",
              },
              {
                label: "描述",
                prop: "materialSpecification",
              },
              {
                label: "主型号",
                prop: "mainModel",
              },
              {
                label: '是否停产',
                prop: 'is_stop',
                  formatter(val){
                  switch (val) {
                    case 1: return "是"; break;
                    case 0: return "否"; break;
                    default: return val; break;
                  }
                },
              },
             {
                label: "渠道专供",
                prop: "channelForName",
              },
              {
                 label: "新渠道专供",
                slot: "newChannelForCode",
                width:160
              },

               {
                label: "商品类别",
                prop: "categoryName",
              },
              {
                label: "新商品类别",
                slot: "newCategoryCode",
                width:160
              },
              {
                label: "货品标签",
                prop: "productLabelName",
              },
              {
                label: "新货品标签",
                slot: "newProductLabelCode",
                width:160
              },
              {
                label: "项目组",
                prop: "projectTeamName",
              },
               {
                label: "新项目组",
                slot: "newProjectTeamCode",
                width:160
              },
          ],
          translate:{
            CHANNEL_FOR:{},
            BUSINESS_DEPARTMENT:{},
            PROJECT_TEAM:{},
            CATEGORY:{},
            PRODUCT_LABEL_AND_CHANNEL_FOR: {},
          },
          notChangeCols:[

              {
                label: "型号",
                prop: "model",
                width: "80"
              },
              {
                label: "物料编码",
                prop: "materialNumber",
              },
              {
                label: "描述",
                prop: "materialSpecification",
              },
              {
                label: "主型号",
                prop: "mainModel",
              },
              {
                label: '是否停产',
                prop: 'is_stop',
                  formatter(val){
                  switch (val) {
                    case 1: return "是"; break;
                    case 0: return "否"; break;
                    default: return val; break;
                  }
                },
              },
              {
                label: "商品类别",
                prop: "categoryName",
              },

              {
                label: "渠道专供",
                prop: "channelForName",
              },
              {
                label: '货品标签',
                prop: 'productLabelName',
              },
              {
                label: "项目组",
                prop: "projectTeamName",
              },
              {
                label: "不调整原因",
                slot: "notChangeReason",
                width:120
              },
          ],
          cols:[

              {
                label: "型号",
                prop: "model",
                width: "80"
              },
              {
                label: "物料编码",
                prop: "materialNumber",
              },
              {
                label: "描述",
                prop: "materialSpecification",
              },
              {
                label: "主型号",
                prop: "mainModel",
              },
              {
                label: '是否停产',
                prop: 'is_stop',
                  formatter(val){
                  switch (val) {
                    case 1: return "是"; break;
                    case 0: return "否"; break;
                    default: return val; break;
                  }
                },
              },
              {
                label: "商品类别",
                prop: "categoryName",
              },
              {
                label: "新商品类别",
                slot: "newCategoryCode",
                width:160
              },

              {
                label: "渠道专供",
                prop: "channelForName",
              },
              {
                 label: "新渠道专供",
                slot: "newChannelForCode",
                width:160

              },
              {
                label: "货品标签",
                prop: "productLabelName",
              },
              {
                label: "新货品标签",
                slot: "newProductLabelCode",
                width:160
              },
              {
                label: "项目组",
                prop: "projectTeamName",
              },
               {
                label: "新项目组",
                slot: "newProjectTeamCode",
                width:160,
                rHeader: true,
                renderHeader: (h, {column, $index}) => {
                  return h('div', [
                    h('span', column.label),
                    h('el-tooltip', { props: { placement: 'bottom' } }, [
                      h(
                        'div',
                        {
                          slot: 'content',
                          style: { width: '180px', whiteSpace: 'normal' }
                        },
                        '新项目组每月8号生效'
                      ),
                      h('i', {
                        class: 'el-icon-information',
                        style: 'color:red;margin-left:5px;cursor:pointer;'
                      })
                    ])
                  ])
                }
              },
          ],
        }
      },
      methods: {
        filling(type){
          if(type == 'CHANNEL_FOR'){
            this.changeList.forEach((item,index)=>{
              if(item.channelForOption.length>0){
                this.$set(this.changeList[index],'newChannelForCode',item.channelForOption.some(child=>{return child.code == this.changeList[0].newChannelForCode})?this.changeList[0].newChannelForCode:'');
              }
            })
          }
          if(type == 'CATEGORY'){
            this.changeList.forEach((item,index)=>{
              if(item.categoryOption.length>0){
                // this.$set(this.changeList[index],'newCategoryCode',item.categoryOption[0]);
                this.$set(this.changeList[index],'newCategoryCode',item.categoryOption.some(child=>{return child.code == this.changeList[0].newCategoryCode})?this.changeList[0].newCategoryCode:'');
              }
            })
          }
          if(type == 'BUSINESS_DEPARTMENT'){
            this.changeList.forEach((item,index)=>{
              if(item.businessDepartmentOption.length>0){
                // this.$set(this.changeList[index],'newBusinessDepartmentCode',item.businessDepartmentOption[0]);
                this.$set(this.changeList[index],'newBusinessDepartmentCode',item.businessDepartmentOption.some(child=>{return child.code == this.changeList[0].newBusinessDepartmentCode})?this.changeList[0].newBusinessDepartmentCode:'');
              }
            })
          }
          if(type == 'PROJECT_TEAM'){
            this.changeList.forEach((item,index)=>{
              if(item.newProjectTeamOption.length>0){
                // this.$set(this.changeList[index],'newProjectTeamCode',item.newProjectTeamOption[0]);
                this.$set(this.changeList[index],'newProjectTeamCode',item.newProjectTeamOption.some(child=>{return child.code == this.changeList[0].newProjectTeamCode})?this.changeList[0].newProjectTeamCode:'');
              }
            })
          }
          if(type == 'PRODUCT_LABEL_AND_CHANNEL_FOR') {

            this.changeList.forEach((item,index)=>{
              if(item.newProductLabelOption.length>0){
                this.$set(this.changeList[index],'newProductLabelCode',item.newProductLabelOption.some(child=>{return child.code == this.changeList[0].newProductLabelCode})?this.changeList[0].newProductLabelCode:'');
              }
            })
          }
        },
        initFormat(){
          let targetValue = this.submit.targetValue;
          let newProjectTeamOption = CacheUtils.getListByCode('fb62c753-d9fd-41bd-9c2a-e07c83747405').map(ele => {
            return {code: ele.val, name: ele.name}
          })
          this.changeList.forEach((item,index)=>{
              // item.newProjectTeamOption = [{code:item.newProjectTeamCode,name:item.newProjectTeamName}];
              // item.businessDepartmentOption =[{code:item.newBusinessDepartmentCode,name:item.newBusinessDepartmentName}];
              // item.categoryOption = [{code:item.newCategoryCode,name:item.newCategoryName}];
              // item.channelForOption =[{code:item.newChannelForCode,name:item.newChannelForName}];
              // item.newProductLabelOption = [{code:item.newProductLabelCode,name:item.newProductLabelName}]
              if(this.submit.type == 'CATEGORY'){
                let option = [],option2=[];
                this.changeConfig.channelForAndProductLabelList.forEach(element=>{
                  if(element.code == item.newChannelForCode){
                    element.list.forEach(ele => {
                      option.push({code:ele.categoryCode,name:ele.channelForName});
                    })
                  }
                })
                this.changeConfig.categoryAndChannelForList.forEach(element=>{
                  if(element.code == this.submit.targetValue){
                    element.list.map(pItem=>{
                      option2.push({code:pItem.channelForCode,name:pItem.channelForName})
                    });
                  }
                })
                item.newProductLabelOption = option
                item.channelForOption = option2
              }
              if(this.submit.type == 'CHANNEL_FOR'){
                let option1 = [], option2 = [];
                this.changeConfig.categoryAndChannelForList.forEach(item=>{
                  item.list.forEach(pItem=>{
                    if(pItem.channelForCode == this.submit.targetValue){
                      option1.push({code:item.code,name:item.name});
                    }
                  })
                })
                this.changeConfig.channelForAndProductLabelList.forEach(item=>{
                  if(item.code == this.submit.targetValue){
                    item.list.map(pItem=>{
                      option2.push({code:pItem.categoryCode,name:pItem.channelForName})
                    });
                  }
                })
                item.categoryOption = option1;
                item.newProductLabelOption = option2;
              }
              if(this.submit.type == 'PROJECT_TEAM'){
                let option1 = [], option2 = []
                let option = CacheUtils.getListByCode('647453f9-3fa1-11ec-98be-708bcdbe0026').map(ele => {
                  return {code: ele.val, name: ele.name}
                })
                this.changeConfig.channelForAndProductLabelList.forEach(element=>{
                  if(element.code == item.newChannelForCode){
                    element.list.forEach(ele => {
                      option1.push({code:ele.categoryCode,name:ele.channelForName});
                    })
                  }
                })
                this.changeConfig.categoryAndChannelForList.forEach(element=>{
                    if(element.code == item.newCategoryCode){
                      element.list.map(pItem=>{
                        option2.push({code:pItem.channelForCode,name:pItem.channelForName})
                      });
                    }
                })
                item.categoryOption = option;
                item.newProductLabelOption = option1
                item.channelForOption = option2
              }
              if(this.submit.type == 'PRODUCT_LABEL_AND_CHANNEL_FOR'){
                let option = [],option2=[];
                this.changeConfig.productLabelCategoryList.forEach(element=>{
                  if(element.code == this.submit.targetValue){
                    element.list.forEach(ele => {
                      option.push({code:ele.channelForCode,name:ele.channelForName});
                    })
                  }
                })
                this.changeConfig.channelForAndCategoryList.forEach(element=>{
                  if(element.code == item.newChannelForCode){
                    element.list.forEach(ele => {
                      option2.push({code:ele.categoryCode,name:ele.channelForName});
                    })
                  }
                })
                item.channelForOption = option;
                item.categoryOption = option2;
              }
              item.newProjectTeamOption = newProjectTeamOption
          })
        },
        loadList(enumId) {
          let list = CacheUtils.getListByCode(enumId)
          if (list ||list.length <= 0) {
            this.ajax.get('/mdm-web/api/enumeration/items', (res) => {
              if (res.body.result && res.body.content) {
                CacheUtils.setAllList(res.body.content)
              }
            });
          }
        },
        newChannelForChange(row,callback){
          let option = [];
          if(this.submit.type == "PRODUCT_LABEL_AND_CHANNEL_FOR"){
            this.changeConfig.channelForAndCategoryList.forEach(item=>{
                if(item.code == row.newChannelForCode){
                  item.list.forEach(ele => {
                    option.push({code:ele.categoryCode,name:ele.channelForName});
                  })
                }
            })
            this.changeList.forEach((item,index)=>{
              let isCheckRow = row.tempId ? row.tempId === item.tempId : row.id === item.id
              if (isCheckRow) {
                // item.categoryOption = option
                if(option.length === 1){
                  this.$set(this.changeList[index],'newCategoryCode',option[0].code);
                } else {
                  this.$set(this.changeList[index],'newCategoryCode','');
                }
                this.$set(this.changeList[index],'categoryOption',option);
              } else {
                // item.categoryOption = item.categoryOption
                this.$set(this.changeList[index],'categoryOption',item.categoryOption);
              }
            })
          }
          if(this.submit.type == "CATEGORY"){

            this.changeConfig.channelForAndProductLabelList.forEach(item=>{
                if(item.code == row.newChannelForCode){
                  item.list.forEach(ele => {
                    option.push({code:ele.categoryCode,name:ele.channelForName});
                  })
                }
            })

            this.changeList.forEach((item,index)=>{
              let isCheckRow = row.tempId ? row.tempId === item.tempId : row.id === item.id
              if (isCheckRow) {
                if(option.length == 1){
                  this.$set(this.changeList[index],'newProductLabelCode',option[0].code);
                } else {
                  this.$set(this.changeList[index],'newProductLabelCode','');
                }
                this.$set(this.changeList[index],'newProductLabelOption',option);
              } else {
                this.$set(this.changeList[index],'newProductLabelOption',item.newProductLabelOption);
              }
            })
          }
          if(this.submit.type === 'PROJECT_TEAM'){

            this.changeConfig.channelForAndProductLabelList.forEach(item=>{
                if(item.code == row.newChannelForCode){
                  item.list.forEach(ele => {
                    option.push({code:ele.categoryCode,name:ele.channelForName});
                  })
                }
            })

            this.changeList.forEach((item,index)=>{
              let isCheckRow = row.tempId ? row.tempId === item.tempId : row.id === item.id
              if (isCheckRow) {

                this.$set(this.changeList[index],'newProductLabelCode','');
                if(option.length > 1){
                  this.$set(this.changeList[index],'newProductLabelCode',option[0].code);
                }
                this.$set(this.changeList[index],'newProductLabelOption',option);
                // item.newProductLabelOption = option
              } else {
                // item.newProductLabelOption = item.newProductLabelOption
                this.$set(this.changeList[index],'newProductLabelOption',item.newProductLabelOption);
              }
            })
          }
          callback(option)
        },
        newCategoryChange(row,callback){
          let option = [];
          if(this.submit.type == "PROJECT_TEAM"){
            this.changeConfig.categoryAndChannelForList.forEach(item=>{
                if(item.code == row.newCategoryCode){
                  option = item.list.map(pItem=>{
                    return {code:pItem.channelForCode,name:pItem.channelForName}
                  });
                }
            })
            this.changeList.forEach((item,index)=>{
              let isCheckRow = row.tempId ? row.tempId === item.tempId : row.id === item.id
              if (isCheckRow) {
                  this.$set(this.changeList[index],'newChannelForCode','');
                  this.$set(this.changeList[index],'newProductLabelCode','');
              }
            })
          }
          callback(option)
        },
        targetValueChange(val,d){
          if(this.isInit){
            this.isInit = false;
            return;
          }
          this.changeList.forEach((item,index)=>{
              item.newProjectTeamOption = [];
              item.businessDepartmentOption = [];
              item.categoryOption = [];
              item.channelForOption = [];
              if(this.submit.type == 'CHANNEL_FOR'){
                this.$set(this.changeList[index],'newCategoryCode','');
                this.$set(this.changeList[index],'newProductLabelCode','');
                this.$set(this.changeList[index],'newProjectTeamCode','');
              }
              if(this.submit.type == 'PROJECT_TEAM'){
                this.$set(this.changeList[index],'newCategoryCode','');
                this.$set(this.changeList[index],'newProductLabelCode','');
                this.$set(this.changeList[index],'newChannelForCode','');
              }
              if(this.submit.type == 'CATEGORY'){
                this.$set(this.changeList[index],'newProjectTeamCode','');
                this.$set(this.changeList[index],'newProductLabelCode','');
                this.$set(this.changeList[index],'newChannelForCode','');
              }
              if(this.submit.type == 'PRODUCT_LABEL_AND_CHANNEL_FOR'){
                this.$set(this.changeList[index],'newProjectTeamCode','');
                this.$set(this.changeList[index],'newCategoryCode','');
                this.$set(this.changeList[index],'newChannelForCode','');
              }
              // this.$set(this.changeList, index, item)
            })
          let newProjectTeamOption = CacheUtils.getListByCode('fb62c753-d9fd-41bd-9c2a-e07c83747405').map(ele => {
            return {code: ele.val, name: ele.name}
          })
          if(this.submit.type == 'CHANNEL_FOR'){

            let option1 = [], option2 = [];
            this.changeConfig.categoryAndChannelForList.forEach(item=>{
              item.list.forEach(pItem=>{
                if(pItem.channelForCode == this.submit.targetValue){
                  option1.push({code:item.code,name:item.name});
                }
              })
            })
            this.changeConfig.channelForAndProductLabelList.forEach(item=>{
              if(item.code == this.submit.targetValue){
                option2 = item.list.map(pItem=>{
                  return {code:pItem.categoryCode,name:pItem.channelForName}
                });
              }
            })

            this.changeList.forEach((item,index)=>{
              this.$set(this.changeList[index],'newChannelForCode',this.submit.targetValue);
              item.categoryOption = option1;
              item.newProductLabelOption = option2;
              item.newProjectTeamOption = newProjectTeamOption
            })

          }
          if(this.submit.type == 'PROJECT_TEAM'){
            let option = CacheUtils.getListByCode('647453f9-3fa1-11ec-98be-708bcdbe0026').map(ele => {
            return {code: ele.val, name: ele.name}
          })

           this.changeList.forEach((item,index)=>{
              item.categoryOption = option;
              this.$set(this.changeList[index],'newProjectTeamCode',this.submit.targetValue);
            })
          }
          if(this.submit.type == 'CATEGORY'){
            let option = [];
            this.changeConfig.categoryAndChannelForList.forEach(item=>{
                if(item.code == this.submit.targetValue){
                  option = item.list.map(pItem=>{
                    return {code:pItem.channelForCode,name:pItem.channelForName}
                  });
                }
            })
           this.changeList.forEach((item,index)=>{
              this.$set(this.changeList[index],'newCategoryCode',this.submit.targetValue);
              this.$set(this.changeList[index],'newProductLabelCode','');
              item.channelForOption = option;
              item.newProjectTeamOption = newProjectTeamOption
            })
          }
          if (this.submit.type == 'PRODUCT_LABEL_AND_CHANNEL_FOR') {
            let option = [];
            this.changeConfig.productLabelCategoryList.forEach(item=>{
              if(item.code == this.submit.targetValue){
                option = item.list.map(pItem=>{
                  return {code:pItem.channelForCode,name:pItem.channelForName}
                });
              }
            })
            this.changeList.forEach((item,index)=>{
              this.$set(this.changeList[index],'newCategoryCode','');
              this.$set(this.changeList[index],'newProductLabelCode',this.submit.targetValue);
              item.channelForOption = option
              item.newProjectTeamOption = newProjectTeamOption
            })
          }
        },
        getConfig(){
          this.ajax.get('/material-web/api/goodsBelong/material/relation/list',res => {
            if(res.body.result && res.body.content) {
              if(this.params.id){
                this.init();
              }
              this.changeConfig.businessDepartmentAndCategoryList = res.body.content.businessDepartmentAndCategoryList.map(item=>{
                return {
                  code:item.code,
                  name:item.name,
                  list:item.list
                }
              })
              this.changeConfig.categoryAndChannelForList = res.body.content.categoryAndChannelForList.map(item=>{
                return {
                  code:item.code,
                  name:item.name,
                  list:item.list
                }
              })
              this.changeConfig.projectTeamAndBusinessDepartmentList = res.body.content.projectTeamAndBusinessDepartmentList.map(item=>{
                return {
                  code:item.code,
                  name:item.name,
                  isCommon:item.isCommon,
                  list:item.list
                }
              })
              this.changeConfig.productLabelCategoryList = res.body.content.productLabelAndChannelForList.map(item=>{
                return {
                  code:item.code,
                  name:item.name,
                  list:item.list
                }
              })
              this.changeConfig.channelForAndCategoryList = res.body.content.channelForAndCategoryList
              this.changeConfig.channelForAndProductLabelList = res.body.content.channelForAndProductLabelList
              // this.submit =res.body.content;
              // this.changeList = res.body.content.changeList;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        handleClose(tag){
          if(this.canEdit) return;
         this.submit.changeModel.splice(this.submit.changeModel.indexOf(tag),1)
          let i = this.changeList.length;
            while(i--) {
              if(this.changeList[i].model == tag) {
                this.changeList.splice(i, 1);
              }
            }
            let j = this.notChangeList.length;
            while(j--) {
              if(this.notChangeList[j].model == tag) {
                this.notChangeList.splice(j, 1);
              }
            }

        },
        radioChange(list){
          this.select = list;
        },
        selectionChange(list){
          this.notSelect = list;
        },
        move(){
          if(!this.notSelect.length){
            this.$message.error('请选择要操作的数据');
            return;
          }
          this.notSelect.forEach(item=>{
            let i = this.notChangeList.length;
            while(i--) {
              if(this.notChangeList.indexOf(item) == i){

                this.notChangeList.splice(this.notChangeList.indexOf(item), 1);
                this.changeList.push(item);
              }
            }

          })

          this.notSelect = [];

        },
        del(){
          if(!this.select.length){
            this.$message.error('请选择要操作的数据');
            return;
          }


          this.select.forEach(item=>{
            let i = this.changeList.length;
            while(i--) {
              if(this.changeList.indexOf(item) == i){
                this.changeList.splice(this.changeList.indexOf(item), 1);
                item.notChangeReason = '';
                item.hasChanged = 0;
                this.notChangeList.push(item);
              }

            }

          })

          this.select = [];
        },
        genID(length){
          return Number(Math.random().toString().substr(3,length) + Date.now()).toString(36);
        },
        add(){
          let self = this;
          this.$root.eventHandle.$emit('alert', {
                    params: {
                       callback: data => {
                            let flag = false;
                            this.submit.changeModel.forEach(item=>{
                              if(item == data[0].name){
                                flag = true
                              };
                            })
                            if(flag){
                              this.$message.error('当前型号已存在');
                              return false;
                            }
                            this.submit.changeModel.push(data[0].name);
                            data.forEach( item=>{
                              let data =  {
                                tempId: self.genID(),
                                mainModel:item.mainModel,
                                model:item.name,
                                materialNumber:item.number,
                                materialSpecification:item.specification,
                                categoryCode:item.supplyChainProductClassification,
                                categoryName:self.translate.CATEGORY[item.supplyChainProductClassification],
                                channelForCode:item.materialSaleVO.channelFor,
                                channelForName:self.translate.CHANNEL_FOR[item.materialSaleVO.channelFor],
                                // businessDepartmentCode:item.materialBaseVO.businessDepartment,
                                // businessDepartmentName:self.translate.BUSINESS_DEPARTMENT[item.materialBaseVO.businessDepartment],
                                projectTeamCode:item.materialBaseVO.projectTeam,
                                projectTeamName:self.translate.PROJECT_TEAM[item.materialBaseVO.projectTeam],
                                newProjectTeamOption:[],
                                businessDepartmentOption :[],
                                categoryOption :[],
                                channelForOption :[],
                                productLabelCode: item.materialBaseVO.productLabels,
                                productLabelName: item.materialBaseVO.productLabelsName || item.materialBaseVO.productLabels,
                              }
                              self.changeList.push(data);
                            })
                              if(self.submit.targetValue){
                                self.targetValueChange(self.submit.targetValue);
                              }


                        },
                    },
                    component: () => import('@components/mdm/module/belong/materialTypeList'),
                    style: 'width:800px;height:500px',
                    title: '物料清单列表',
                })
        },
        typeChange(){
            if(this.submit.type == 'CHANNEL_FOR'){
              this.option = CacheUtils.getListByCode('1234');
              this.cols = this.channelForCol;
            }
            if(this.submit.type == 'BUSINESS_DEPARTMENT'){
              this.option = CacheUtils.getListByCode('4429ea40-1d0e-11ed-bc39-244bfe4fb2ea');
              this.cols = this.businessDepartmentCol ;
            }
            if(this.submit.type == 'PROJECT_TEAM'){
              this.option = CacheUtils.getListByCode('fb62c753-d9fd-41bd-9c2a-e07c83747405');
              this.cols = this.projectTeamCol;
            }
            if(this.submit.type == 'CATEGORY'){
              this.option = CacheUtils.getListByCode('647453f9-3fa1-11ec-98be-708bcdbe0026');
              this.cols = this.categoryCol ;
            }
            if(this.submit.type == 'PRODUCT_LABEL_AND_CHANNEL_FOR') {
              this.option =  CacheUtils.getListByCode('PRODUCT_LABELS');
              this.cols = this.productLabelCol ;
            }

        },
        init(){
          this.getDetail();
        },
        getDetail(){
          this.ajax.postStream('/material-web/api/goodsBelong/apply/detail',{applyId:this.params.id},res => {
            if(res.body.result && res.body.content) {
              this.submit =res.body.content;
              this.changeList = res.body.content.changeVOList;
              this.notChangeList = res.body.content.notChangeVOList;
              this.submit.changeModel = res.body.content.changeModel.split(',')
              this.isInit = true;
              this.initFormat()
              this.typeChange()

            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },

        cancelData(){
          this.ajax.postStream('/material-web/api/goodsBelong/apply/cancel',{applyId:this.params.id},res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.init()
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        //  this.save(()=>{

        //  })

       },
       submitData(){
          this.ajax.postStream('/material-web/api/goodsBelong/apply/submit',{applyId:this.params.id},res => {
            if(res.body.result) {
              this.$message.success(res.body.msg);
              this.init()
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        //  this.save(()=>{

        //  })

       },
        //变更
        save(callback){
          let self = this;

          if(this.submit.applyTitle.length>50){
            this.$message.error('标题不能超过50字符');
            return;
          }
          let data = {
            id:this.submit.id,
            applyTitle:this.submit.applyTitle,
            type:this.submit.type,
            targetValue:this.submit.targetValue,
            targetName:this.submit.targetName,
            enableTime:this.submit.enableTime,
            changeModel:this.submit.changeModel.join(','),
            changeList:this.changeList.map(item=>{
              return{
                id:item.id,
                hasChanged:1,
                applyId:item.applyId,
                mainModel:item.mainModel,
                model:item.model,
                materialNumber:item.materialNumber,
                materialSpecification:item.materialSpecification,
                categoryCode:item.categoryCode,
                categoryName:item.categoryName,
                newCategoryCode:item.newCategoryCode,
                newCategoryName:self.translate.CATEGORY[item.newCategoryCode],
                channelForCode:item.channelForCode,
                channelForName:item.channelForName,
                newChannelForCode:item.newChannelForCode,
                newChannelForName:self.translate.CHANNEL_FOR[item.newChannelForCode],
                businessDepartmentCode:item.businessDepartmentCode,
                businessDepartmentName:item.businessDepartmentName,
                // newBusinessDepartmentCode:item.newBusinessDepartmentCode,
                // newBusinessDepartmentName	:self.translate.BUSINESS_DEPARTMENT[item.newBusinessDepartmentCode]	,
                projectTeamCode:item.projectTeamCode,
                projectTeamName	:item.projectTeamName	,
                newProjectTeamCode:item.newProjectTeamCode,
                newProjectTeamName:self.translate.PROJECT_TEAM[item.newProjectTeamCode],
                newProductLabelCode:item.newProductLabelCode,
                newProductLabelName:self.translate.PRODUCT_LABEL_AND_CHANNEL_FOR[item.newProductLabelCode],
                productLabelCode: item.productLabelCode,
                productLabelName: item.productLabelName,

              }
            }),
            notChangeList:this.notChangeList.map(item=>{
              return{
                id:item.id,
                hasChanged:0,
                applyId:item.applyId,
                mainModel:item.mainModel,
                model:item.model,
                materialNumber:item.materialNumber,
                materialSpecification:item.materialSpecification,
                categoryCode:item.categoryCode,
                categoryName:item.categoryName,
                newCategoryCode:item.newCategoryCode,
                newCategoryName:self.translate.CATEGORY[item.newCategoryCode],
                channelForCode:item.channelForCode,
                channelForName:item.channelForName,
                newChannelForCode:item.newChannelForCode,
                newChannelForName:self.translate.CHANNEL_FOR[item.newChannelForCode],
                businessDepartmentCode:item.businessDepartmentCode,
                businessDepartmentName:item.businessDepartmentName,
                // newBusinessDepartmentCode:item.newBusinessDepartmentCode,
                // newBusinessDepartmentName	:self.translate.BUSINESS_DEPARTMENT[item.newBusinessDepartmentCode]	,
                projectTeamCode:item.projectTeamCode,
                projectTeamName	:item.projectTeamName	,
                newProjectTeamCode:item.newProjectTeamCode,
                notChangeReason:item.notChangeReason,
                newProjectTeamName:self.translate.PROJECT_TEAM[item.newProjectTeamCode],

                newProductLabelCode:item.newProductLabelCode,
                newProductLabelName:self.translate.PRODUCT_LABEL_AND_CHANNEL_FOR[item.newProductLabelCode],
                productLabelCode: item.productLabelCode,
                productLabelName: item.productLabelName,
              }
            }),

          };



          let url = "/material-web/api/goodsBelong/apply/save?permissionCode=GOODS_BELONG_CHAGNE_CREATE";

          this.ajax.postStream(url,data,res => {
            if(res.body.result && res.body.content) {
              this.$message.success(res.body.msg);
              this.params.id = res.body.content;

                this.getDetail();

            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },


        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.pageSize = pageSize;
        },
        // 监听页数更改事件好
        pageChange(page){
          this.pageNo = page;
        },
      },
      mounted: function() {

        this.getConfig();
        this.option = CacheUtils.getListByCode('647453f9-3fa1-11ec-98be-708bcdbe0026');
        // this.loadList('1234');
        CacheUtils.getListByCode('1234').forEach(item=>{
          this.translate.CHANNEL_FOR[item.val] = item.name
        })
        CacheUtils.getListByCode('4429ea40-1d0e-11ed-bc39-244bfe4fb2ea').forEach(item=>{
          this.translate.BUSINESS_DEPARTMENT[item.val] = item.name
        })
        CacheUtils.getListByCode('fb62c753-d9fd-41bd-9c2a-e07c83747405').forEach(item=>{
          this.translate.PROJECT_TEAM[item.val] = item.name
        })
        CacheUtils.getListByCode('647453f9-3fa1-11ec-98be-708bcdbe0026').forEach(item=>{
          this.translate.CATEGORY[item.val] = item.name
        })
        CacheUtils.getListByCode('PRODUCT_LABELS').forEach(item=>{
          this.translate.PRODUCT_LABEL_AND_CHANNEL_FOR[item.val] = item.name
        })

      },
      computed: {
        canEdit() {
            return this.submit.status ==='AUDIT'||this.submit.status ==='SUBMIT' ||this.submit.status ==='CANCEL';
        }
    }
    }
</script>


