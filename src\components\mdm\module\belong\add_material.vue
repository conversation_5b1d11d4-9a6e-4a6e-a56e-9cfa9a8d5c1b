
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='24'>
        <el-button type='success' size='mini' @click="init" :disabled="!id">刷新</el-button>
        <el-button type='info' size='mini' @click="save('SAVE')">保存</el-button>
        <el-button type='primary' size='mini' @click="save('enable')" :disabled="!(id && submit.status == 0)">生效</el-button>
        <el-button type='danger' size='mini' @click="save('disable')" :disabled="!(id && submit.status == 1)">失效</el-button>
      </el-col>
    </el-row>
    <el-row	:gutter='40' >
      <el-tabs v-model="firstTab" >
        <el-tab-pane label="基础信息" name="awardList">
          <el-form label-position="right" label-width="140px" :rules="rules" ref='submit' :model="submit">
            <el-col :span="8" >
              <el-form-item label="关系类型：">
                <xpt-select-aux
								v-model="submit.type"
								aux_name='material_belong_type'
                :disabledOption="[{code:'PROJECT_TEAM_AND_BUSINESS_DEPARTMENT'},{code:'BUSINESS_DEPARTMENT_AND_CATEGORY'}]"
                :disabled="!!submit.id"
							></xpt-select-aux>
              </el-form-item>
              <el-form-item label="状态：">
                <el-select placeholder="请选择" v-model="submit.status" size='mini' disabled >
                  <el-option
                    label="生效"
                    :value="1">
                  </el-option>
                   <el-option
                    label="失效"
                    :value="0">
                  </el-option>
               </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="名称：" prop="code">
                <el-select placeholder="请选择" v-model="submit.code" size='mini' :disabled="!!submit.id"  @change="changeCode" required>
                  <el-option
                    v-for="(value,key) in option"
                    :key="key"
                    :label="value.name"
                    :value="value.val">
                  </el-option>
                </el-select>
                <el-tooltip v-if='rules.code[0].isShow' class="item" effect="dark" :content="rules.code[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="创建人：">
                <el-input v-model="submit.creatorName" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>

            </el-col>
            <el-col :span="8" >
              <el-form-item label="是否共线：" v-if="submit.type == 'PROJECT_TEAM_AND_BUSINESS_DEPARTMENT'" required prop="isCommon">
                 <el-select placeholder="请选择" v-model="submit.isCommon" size='mini' :disabled="!!id">
									    <el-option
									      label="是"
									      :value="1">
									    </el-option>
                       <el-option
									      label="否"
									      :value="0">
									    </el-option>
									 </el-select>
              </el-form-item>
              <el-form-item label="创建时间：">
									<el-date-picker v-model="submit.createTime" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>

              </el-form-item>

            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class='xpt-flex__bottom' id='bottom' v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane :label="labels[submit.type]" name="detailList" class='xpt-flex'>
          <xpt-list
            :data='detailList'
            :colData='cols[submit.type] || []'
            :btns="btns"
            selection='radio'
            ref="materialList"
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
            @radio-change="radioChange"
          >
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
import MdmSelect from "@components/mdm/components/form/MdmSelect";
import CacheUtils from "@components/mdm/utils/cacheUtils";
import Fn from '@common/Fn.js'
import validate from '@common/validate.js';
    export default {
      name: "awardDetail",
      props:["params"],
	    components: {MdmSelect},
      data() {
        let self = this;
        return {
          option:[],
          select:{},
          detailList:[],
          delList:[],
          submit:{
            id:'',
            name:'',
            code:'',
            type:'',
            status: '',
            isCommon:'',
          },
          firstTab:"awardList",
          secondTab:"detailList",
          pageSize:50,
          pageNO:1,

          btns:[
            {
              type: "success",
              txt: "添加",
              loading: false,
              click() {
                self.add();
              },
            },
            {
              type: "danger",
              txt: "删除",
              loading: false,
              click() {
                self.del();

              },
            },
          ],
          labels: {
            CATEGORY_AND_CHANNEL_FOR: '关联渠道专供',
            BUSINESS_DEPARTMENT_AND_CATEGORY: '关联商品类别',
            PROJECT_TEAM_AND_BUSINESS_DEPARTMENT: '关联事业部',
            PRODUCT_LABEL_AND_CHANNEL_FOR: '关联渠道专供',
          },
          cols:{
            CATEGORY_AND_CHANNEL_FOR:[

              {
                label: "渠道专供编码",
                prop: "channelForCode",
              },
              {
                label: "渠道专供名称",
                prop: "channelForName",
              },
            ],
            PRODUCT_LABEL_AND_CHANNEL_FOR:[

              {
                label: "渠道专供编码",
                prop: "channelForCode",
              },
              {
                label: "渠道专供名称",
                prop: "channelForName",
              },
            ],
            BUSINESS_DEPARTMENT_AND_CATEGORY:[

              {
                label: "商品类别编码		",
                prop: "categoryCode",
              },
              {
                label: "商品类别名称	",
                prop: "categoryName",
              },
            ],
            PROJECT_TEAM_AND_BUSINESS_DEPARTMENT:[


              {
                label: "事业部编码",
                prop: "businessDepartmentCode",
              },
              {
                label: "事业部名称",
                prop: "businessDepartmentName",
              },
            ],

          },
          id: '',
          rules: {
            code:validate.isNotBlank({
              required:true,
              self:self
            }),
          }
        }
      },
      methods: {
        changeCode(enumId){
          this.option.forEach(item=>{
            if(item.val == this.submit.code){
              this.submit.name = item.name
            }
          })
        },
        radioChange(row){
          this.select = row;
        },
        del(){
          if(JSON.stringify(this.select) === '{}' || !this.select){
            this.$message.error('请选择要删除的数据');
            return;
          }
          if(this.select.pid){
            let idx = this.detailList.indexOf(this.select)
            this.detailList[idx].deleteFlag = true
            this.delList.push(this.detailList[idx]);
          }
          this.detailList.splice(this.detailList.indexOf(this.select), 1)
          this.$refs.materialList && this.$refs.materialList.clearRadioSelect()
          this.select = {};
        },
        add(){
          let self = this;
          this.$root.eventHandle.$emit('alert', {
                    params: {
                        selection: 'checkbox',
                        enumId:['CATEGORY_AND_CHANNEL_FOR','PRODUCT_LABEL_AND_CHANNEL_FOR'].includes(this.submit.type)?'1234':this.submit.type == 'BUSINESS_DEPARTMENT_AND_CATEGORY'?'647453f9-3fa1-11ec-98be-708bcdbe0026':'4429ea40-1d0e-11ed-bc39-244bfe4fb2ea',
                        callback: data => {
                            let list = []
                            data.forEach(item => {
                              let result = {};
                              if(['CATEGORY_AND_CHANNEL_FOR','PRODUCT_LABEL_AND_CHANNEL_FOR'].includes(self.submit.type)){
                                result.channelForCode = item.val;
                                result.channelForName = item.name;
                              }else if(self.submit.type == 'BUSINESS_DEPARTMENT_AND_CATEGORY'){
                                result.categoryCode = item.val;
                                result.categoryName = item.name;
                              }else{
                                result.businessDepartmentCode = item.val;
                                result.businessDepartmentName = item.name;
                              }
                              list.push(result)
                            })

                            self.detailList.push(...list);
                        },
                    },
                    component: () => import('@components/mdm/module/belong/materialRelation'),
                    style: 'width:800px;height:500px',
                    title:['CATEGORY_AND_CHANNEL_FOR','PRODUCT_LABEL_AND_CHANNEL_FOR'].includes(this.submit.type) ? '渠道专供列表':this.submit.type == 'BUSINESS_DEPARTMENT_AND_CATEGORY'?'商品类别列表':'助理事业部列表',
                })
        },
        typeChange(){
          this.submit.name = '';
          this.submit.code = '';
          this.submit.isCommon = '';
          this.detailList = [];
          this.initOptions()
        },
        init(){
          this.getDetail();
          this.getList();
        },
        initOptions(type) {
          let submitType = type ? type : this.submit.type
          let enumIdOfType = {
            'PROJECT_TEAM_AND_BUSINESS_DEPARTMENT': 'fb62c753-d9fd-41bd-9c2a-e07c83747405',
            'BUSINESS_DEPARTMENT_AND_CATEGORY': '4429ea40-1d0e-11ed-bc39-244bfe4fb2ea',
            'CATEGORY_AND_CHANNEL_FOR': '647453f9-3fa1-11ec-98be-708bcdbe0026',
            'PRODUCT_LABEL_AND_CHANNEL_FOR': 'PRODUCT_LABELS',
          }
          this.option = CacheUtils.getListByCode(enumIdOfType[submitType]);
        },
        getDetail(){
          this.ajax.postStream('/material-web/api/goods/belong/material/get',this.id,res => {
            if(res.body.result && res.body.content) {
              // this.detailList = res.body.content.list;
              this.initOptions(res.body.content.type)
              this.submit =res.body.content;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        getList() {
          let data = {
            page_no:1,
            page_size:50,
            if_need_page:'Y',
            id:this.id
          }
          this.ajax.postStream('/material-web/api/goods/belong/material/item/list',data,res => {
            if(res.body.result && res.body.content) {
              this.detailList = res.body.content.list;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },



        save(isDisabled){
          this.$refs.submit.validate((valid) => {
            if (!valid) return
            if (this.submit.type == 'PROJECT_TEAM_AND_BUSINESS_DEPARTMENT' && this.submit.isCommon === '') {
              this.$message.error('请选择是否共线！')
              return
            }
            this.ifBiangeng = true;
            this.biangeng = true;
            let data = {

                id:this.id,
                name:this.submit.name,
                code:this.submit.code,
                type: this.submit.type,
                status: isDisabled == 'disable' ? 0 : isDisabled == 'enable' ? 1 : this.submit.status,
                isCommon: this.submit.isCommon,
                list:this.delList.concat(this.detailList),
            };
            let url = "/material-web/api/goods/belong/material/save?permissionCode=BELONG_MATERIAL_RELATION";

            this.ajax.postStream(url,data,res => {
              if(res.body.result) {
                this.$message.success(res.body.msg);
                this.submit.id = res.body.content;
                this.id = res.body.content;
                this.delList = []
                this.init();
              } else {
                res.body.msg && this.$message.error(res.body.msg);
                this.biangeng = false;
              }
              this.ifBiangeng = false;
            }, err => {
              this.$message.error(err);
              this.ifBiangeng = false;
              this.biangeng = false;
            });
          })
        },

        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.pageSize = pageSize;
          this.getList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.pageNo = page;
          this.getList();
        },
      },
      mounted: function() {
        CacheUtils.loadList('1234').then((list) => {
          this.typeChange();
        })
        if(this.params.id){
          this.id = this.params.id
          this.init();
        }else{
          this.submit.type = 'CATEGORY_AND_CHANNEL_FOR'
          this.submit.status = 1
          this.initOptions();
        }
      },
      watch: {
        'submit.type': function(newVal, oldVal) {
          if (newVal != oldVal && oldVal) {
            this.typeChange();
          }
        }
      }
    }
</script>


