
<template>
  <div class="xpt-flex">
    <el-row	class='xpt-top'	:gutter='40'>
      <el-col :span='24'>
        <el-button type='success' size='mini' @click="init" :disabled="!id">刷新</el-button>
        <el-button type='info' size='mini' @click="save('SAVE')">保存</el-button>
        <el-button type='primary' size='mini' @click="save('enable')" :disabled="!(id && submit.status == 0)">生效</el-button>
        <el-button type='danger' size='mini' @click="save('disable')" :disabled="!(id && submit.status == 1)">失效</el-button>
      </el-col>
    </el-row>
    <el-row	:gutter='40' >
      <el-tabs v-model="firstTab" >
        <el-tab-pane label="基础信息" name="awardList">
          <el-form label-position="right" label-width="140px" :rules="rules" ref='submit' :model="submit">
            <el-col :span="8" >
              <el-form-item label="归属类型：">
                <!-- <xpt-select-aux
								v-model="submit.type"
								aux_name='person_belong_type'
                :disabled="!!submit.id"
							></xpt-select-aux> -->
              <el-select placeholder="请选择" v-model="submit.type" size='mini' :disabled="!!submit.id">
                  <el-option
                    v-for="(value,key) in person_belong_option"
                    :key="key"
                    :label="value.name"
                    :value="value.code">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="创建人：">
                <el-input v-model="submit.creatorName" size='mini' style="width: 200px;" disabled></el-input>
              </el-form-item>
              
              
            </el-col>
            <el-col :span="8">
              <el-form-item label="名称：" prop="code" required>
                <el-select placeholder="请选择" v-model="submit.code" size='mini' :disabled="!!submit.id"  @change="changeCode" required>
                  <el-option
                    v-for="(value,key) in option"
                    :key="key"
                    :label="value.name"
                    :value="value.val">
                  </el-option>
                </el-select>
                <el-tooltip v-if='rules.code[0].isShow' class="item" effect="dark" :content="rules.code[0].message" placement="right-start" popper-class='xpt-form__error'>
                  <i class='el-icon-warning'></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="创建时间：">
                  <!-- <el-input v-model="submit.createTime" size='mini' style="width: 200px;" disabled></el-input> -->
                  									<el-date-picker v-model="submit.createTime" type="datetime" placeholder="选择日期" disabled size='mini'></el-date-picker>

                </el-form-item>
              
            </el-col>
            <el-col :span="8" >
              <el-form-item label="状态：">
                <el-select placeholder="请选择" v-model="submit.status" size='mini' disabled >
                  <el-option
                    label="生效"
                    :value="1">
                  </el-option>
                   <el-option
                    label="失效"
                    :value="0">
                  </el-option>
               </el-select>
              </el-form-item>
              
              
            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row class='xpt-flex__bottom' id='bottom' v-fold>
      <el-tabs v-model="secondTab">
        <el-tab-pane label="关联人员" name="detailList" class='xpt-flex'>
          <xpt-list
            :data='detailList'
            :colData='cols'
            :btns="btns"
            selection='radio'
            @page-size-change="sizeChange"
            @current-page-change="pageChange"
            @radio-change="radioChange"
            ref="personList"
          >
           
          </xpt-list>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
import MdmSelect from "@components/mdm/components/form/MdmSelect";
import CacheUtils from "@components/mdm/utils/cacheUtils";
import Fn from '@common/Fn.js'
import validate from '@common/validate.js';
    export default {
      name: "awardDetail",
      props:["params"],
	    components: {MdmSelect},
      data() {
        let self = this;
        return {
          select:{},
          detailList:[],
          delList:[],
          submit:{
            id:'',
            name:'',
            code:'',
            type:'',
            status: '',
            isCommon:'',
          },
          firstTab:"awardList",
          secondTab:"detailList",
          pageSize:50,
          pageNO:1,
        
          btns:[
            {
              type: "success",
              txt: "添加",
              loading: false,
              click() {
                self.add();
              },
            },
            {
              type: "danger",
              txt: "删除",
              loading: false,
              click() {
                self.del();
                
              },
            },
          ],
          cols:[
            
              {
                label: "渠道专供",
                prop: "channelFor",
                formatter(val){
                  let name = '';
                  self.channelForList.forEach(item=>{
                    if(item.val == val){
                      name = item.name;
                    }
                  })
                  return name;
                },
                // format:'auxFormat',
                // formatParams:'material_for_channel',
                width: "80"
              },
              {
                label: "工号",
                prop: "employeeNumber",
                width: "80"
              },
              {
                label: "姓名",
                prop: "userName",
              },
              {
                label: "排序",
                prop: "orderNo",
              },
          ],
          person_belong_option:[],
          option: [],
          id: '',
          rules: {
            code:validate.isNotBlank({
              required:true,
              self:self
            }),
          }
        }
      },
      methods: {
        changeCode(){
          this.option.forEach(item=>{
            if(item.val == this.submit.code){
              this.submit.name = item.name
            }
          })
        },
        radioChange(row){
          this.select = row;
        },
        del(){
          if(JSON.stringify(this.select) === '{}' || !this.select){
            this.$message.error('请选择要删除的数据');
            return;
          }
          if(this.select.pid){
            let idx = this.detailList.indexOf(this.select)
            this.detailList[idx].deleteFlag = true
            this.delList.push(this.detailList[idx]);
          }
          this.detailList.splice(this.detailList.indexOf(this.select), 1)
          this.$refs.personList && this.$refs.personList.clearRadioSelect()
          this.select = {};
        },
        add(){
          let self = this;
          let orderNos = self.detailList.reduce((arr,item) => {
            arr.push(item.orderNo)
            return arr
          }, [])
          this.$root.eventHandle.$emit('alert', {
            params: {
              orderNos: orderNos,
              callback: data => {
                  let result = {

                  };
                  self.detailList.push(data);
              },
            },
            component: () => import('@components/mdm/module/belong/personRelation'), 
            style: 'width:400px;height:250px',
            title: '员工列表',
          })
        },
        typeChange(){
          this.submit.name = '';
          this.submit.code = '';
          this.detailList = [];
          this.initOptions()
        },
        initOptions(type) {
          let submitType = type ? type : this.submit.type
          let enumIdOfType = {
            'PROJECT_TEAM': 'fb62c753-d9fd-41bd-9c2a-e07c83747405',
            'SALES_PROFIT_CLASS': '647453f9-3fa1-11ec-98be-708bcdbe0026'
          }
          this.option = CacheUtils.getListByCode(enumIdOfType[submitType]);
        },
        init(){
          this.getDetail();
          this.getList();
        },
        getDetail(){
          this.ajax.postStream('/material-web/api/goods/belong/person/get',this.id,res => {
            if(res.body.result && res.body.content) {
              // this.detailList = res.body.content.list;
              this.initOptions(res.body.content.type)
              this.submit =res.body.content;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        getList() {
          let data = {
            page_no:1,
            page_size:50,
            if_need_page:'Y',
            id:this.id
          }
          this.ajax.postStream('/material-web/api/goods/belong/person/item/list',data,res => {
            if(res.body.result && res.body.content) {
              this.detailList = res.body.content.list;
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
        },
        
       
        //变更
        save(status){
          this.$refs.submit.validate((valid) => {
            if (!valid) return
            this.ifBiangeng = true;
            this.biangeng = true;
            let data = {
                id:this.id,
                name:this.submit.name,
                code:this.submit.code,
                type: this.submit.type,
                status: status == 'disable' ? 0 : status == 'enable' ? 1 : this.submit.status,
                list:this.delList.concat(this.detailList),
            };
            let url = "/material-web/api/goods/belong/person/save?permissionCode=BELONG_PERSON_RELATION";
            
            this.ajax.postStream(url,data,res => {
              if(res.body.result) {
                this.$message.success(res.body.msg);
                this.id = res.body.content
                this.delList = []
                this.init();
              } else {
                res.body.msg && this.$message.error(res.body.msg);
                this.biangeng = false;
              }
              this.ifBiangeng = false;
            }, err => {
              this.$message.error(err);
              this.ifBiangeng = false;
              this.biangeng = false;
            });
          })
        },
      
      
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.pageSize = pageSize;
          this.getList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.pageNo = page;
          this.getList();
        },
      },
      mounted: function() {
        let self = this;
        if(this.params.id){
          this.id = this.params.id
          this.init();
        } else {
          this.submit.type = 'SALES_PROFIT_CLASS'
          this.submit.status = 1
          this.initOptions()
        }
        CacheUtils.loadList('fb62c753-d9fd-41bd-9c2a-e07c83747405').then((list) => {
          this.typeChange();
          self.loadItem = true;
        });
        CacheUtils.loadList('1234').then((list) => {
          self.channelForList = list;
          console.log(this.channelForList);
        });
        
        __AUX.get('person_belong_type').forEach(item=>{
          if(item.ext_field1 == 'Y'){
            self.person_belong_option.push({
              name:item.name,
              code:item.code
            }) 
          }
          
        });
        console.log(self.person_belong_option);
      },
      watch: {
        'submit.type': function(newVal, oldVal) {
          if (newVal != oldVal && oldVal) {
            this.typeChange();
          }
        }
      }
    }
</script>


