<!-- 变更申请列表 -->
<template>
	<xpt-list
		ref='list'
		:data='list'
		:btns='btns'
    :searchPage='search.page_name'
		:colData='cols'
    :pageTotal='count'
    @radio-change="radioChange"
    selection='radio'
    :showHead='true'
    @search-click='searchClick'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
	></xpt-list>
</template>
<script>
import CacheUtils from "@components/mdm/utils/cacheUtils";
export default {
	props:["params"],
	data(){
		let _this = this;
		return {
			list:[],
      count:0,
      search:{
          page_name:"goods_belong_change_apply",
          where:[

          ],
          page_size:100,
          page_no:1,
          if_need_page:"Y"
      },
      select:{},
			btns: [
				{
					type: 'success',
					txt: '新增变更申请',
					click(){
            let params = {};
                params.id = null;
              _this.$root.eventHandle.$emit('creatTab', {
                name: "新增变更申请",
                params: params,
                component: () => import('@components/mdm/module/belong/add_change.vue')
              });
          },
					loading: false
				},
        {
					type: 'primary',
					txt: '导出',
					click: _this.exportExcel,
					loading: false
				},
        {
					type: 'primary',
					txt: '导出结果查看',
					click: _this.exportResult,
					loading: false
				}
			],
			cols: [
				{
					label: '变更申请单号',
					prop: 'applyNo',
					width: 200,
          redirectClick(row) {
                let params = {};
                params.id = row.id;
                _this.$root.eventHandle.$emit('creatTab', {
                  name: "变更申请详情",
                  params: params,
                  component: () => import('@components/mdm/module/belong/add_change.vue')
                });
              },
				}, {
					label: '申请单名称',
					prop: 'applyTitle',
					width: 200,
				},{
					label: '变更类型',
					prop: 'type',
					formatter(val){
            switch (val) {
							case "CATEGORY": return "商品类别"; break;
							case "CHANNEL_FOR": return "渠道专供"; break;
							case "BUSINESS_DEPARTMENT": return "主理事业部"; break;
							case "PROJECT_TEAM": return "项目组"; break;
							case "PRODUCT_LABEL_AND_CHANNEL_FOR": return "货品标签"; break;
              default: return val; break;
            }
          },
				}, {
					label: '状态',
					prop: 'status',
          	formatter(val){
            switch (val) {
							case "CREATE": return "创建"; break;
							case "SUBMIT": return "提交"; break;
							case "AUDIT": return "审核"; break;
							case "REJECT": return "驳回"; break;
							case "RETRACT": return "撤回"; break;
							case "CANCEL": return "作废"; break;
              default: return val; break;
            }
          },
				}, {
					label: '创建人',
					prop: 'creatorName',
				}, {
					label: '创建时间',
					prop: 'createTime',
					format: 'dataFormat1',
					width: 130
				}, {
					label: '提交时间',
					prop: 'submitTime',
					format: 'dataFormat1',
					width: 130
				}, {
					label: '生效时间',
					prop: 'enableTime',
					format: 'dataFormat1',
					width: 130
				}, {
					label: '审核时间',
					prop: 'approvalTime',
					format: 'dataFormat1',
					width: 130
				}
			]
		}
	},
	methods:{
    loadList(enumId) {
          let list = CacheUtils.getListByCode(enumId)
          if (list ||list.length <= 0) {
            this.ajax.get('/mdm-web/api/enumeration/items', (res) => {
              if (res.body.result && res.body.content) {
                CacheUtils.setAllList(res.body.content)
              }
            });
          }
        },
    exportResult(){
        this.$root.eventHandle.$emit("alert", {
          style: "width:900px;height:600px",
          title: "导入结果",
          params: {
            url: "/reports-web/api/reports/afterSale/findMyReportList",
            data: {
              page_size: 20,
              page_no: 1,
              type: "EXCEL_TYPE_GOODS_BELONG_CHANGE_APPLY"
            },
            showDownload: true,
          },
          component: () => import("@components/common/eximport"),
        });
      },
    radioChange(row){
      this.select = row;
    },
    searchClick(list,reslove){
      this.search.where = list;
      this.searching(reslove);
    },
    exportExcel(){
      let _this = this;
      if(!this.select){
        this.$message.error('请选择数据');
        return;
      }
       this.ajax.postStream('/material-web/api/goodsBelong/exportExcel' ,this.select.id, res => {
                if(res.body.result){
                    // _this.list= res.body.content || []
                    _this.$message.success(res.body.msg)
                } else {
                    _this.$message.error(res.body.msg)
                }
            })
    },
		searching(reslove){
			let _this = this
            this.ajax.postStream('/material-web/api/goodsBelong/apply/list' ,this.search, res => {
                if(res.body.result){
                    _this.list= res.body.content.list || []
                    _this.count= res.body.content.count || 0
                    // _this.$message.success(res.body.msg)

                    reslove&&reslove();
                } else {
                    _this.$message.error(res.body.msg)
                }
            })
		},
    // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page_size = pageSize;
          this.searching();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.search.page_no = page;
          this.searching();
        },
	},
	mounted: function(){
        this.searching()
        this.loadList('1234');
  	},
}
</script>
