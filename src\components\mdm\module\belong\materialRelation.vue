<template>
	<xpt-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:pageTotal='pageTotal'
		selection='checkbox'
		searchHolder='请输入商品类别名称'
		searchPage=''
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
		@selection-change='selectionChange'
    @search-click="searchClick"
  ></xpt-list>
</template>
<script>
import CacheUtils from "@components/mdm/utils/cacheUtils";
export default {
	data(){
		let self = this;
		return {
			btns: [{
				type: 'warning',
				txt: '确认',
				click: self.close
			}],
			cols: [
				{
					label: '编码',
					prop: 'val'
				}, {
					label: '名称',
					prop: 'name'
				},
			],
			searchObj:{
				page_no:1,
				page_size:self.pageSize,
			},
			list:[],
			selectRow:[],
			pageTotal:0
		}
	},
	props:['params'],
	methods:{
		close(){
			let self = this;
			if(self.selectRow.length == 0) {
				self.$message.error('请选择单据');
				return;
			}
			self.params.callback(self.selectRow);
			self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
		},
		sizeChange(pageSize){
			var self = this;
			self.searchObj.page_size = pageSize;
			self.getList();
		},
		pageChange(page_no){
			var self = this;
			self.searchObj.page_no = page_no;
			self.getList();
		},
    searchClick(search) {
      let originalList = CacheUtils.getListByCode(this.params.enumId)
      this.list = originalList.filter(item => item.name.indexOf(search) > -1)
    },
		getList(search){
			var self = this;
      self.searchObj.search = search || null;
		  this.list = CacheUtils.getListByCode(this.params.enumId)

		},
		selectionChange(arr) {
			this.selectRow = arr;
		},
	},
	mounted(){
    this.getList();
	}
}
</script>
