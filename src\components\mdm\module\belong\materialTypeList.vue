<template>
	<xpt-list
		:data='list'
		:btns='btns'
		:colData='cols'
		:pageTotal='pageTotal'
		selection='null'
		searchHolder='请输入商品变更型号'
		@search-click='getList'
		searchPage=''
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
		@radio-change='radioChange'
  ></xpt-list>
</template>
<script>
import CacheUtils from "@components/mdm/utils/cacheUtils";
export default {
	data(){
		let self = this;
		return {
			btns: [{
				type: 'warning',
				txt: '确认',
				click: self.close
			}],
			cols: [
				{


					label: '物料编码',
					prop: 'number'
				}, {
					label: '物料名称(型号)',
					prop: 'name'
				},{
					label: '描述	',
					prop: 'specification'
				},{
					label: '物料主型号	',
					prop: 'foldNumber'
				},
			],
			searchObj:{
				page_no:1,
				page_size:1000,
			},
			list:[],
			selectRow:'',
			pageTotal:0
		}
	},
	props:['params'],
	methods:{
		close(){
			let self = this;
			if(!self.list.length) {
				self.$message.error('请选择单据');
				return;
			}
			self.params.callback(self.list);
			self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
		},
		sizeChange(pageSize){
			var self = this;
			self.searchObj.page_size = pageSize;
			self.getList();
		},
		pageChange(page_no){
			var self = this;
			self.searchObj.page_no = page_no;
			self.getList();
		},
		getList(search){
			
			var self = this;
			this.ajax.postStream('/material-web/api/goodsBelong/get/material/list',{materialName:search},res => {
            if(res.body.result && res.body.content) {
              this.list = res.body.content;
             this.pageTotal = res.body.content.length
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
          }, err => {
            this.$message.error(err);
          });
			
		},
		radioChange(obj) {
			this.selectRow = obj;
		},
		rowDblclick(obj) {
			this.selectRow = obj;
			// this.close();
		}
	},
	mounted(){
    // this.getList();
	}
}
</script>
