//人员归属列表
<template>
  <count-list
    :data="dataList"
    :btns="btns"
    :colData="cols"
    :searchPage='search.page_name'
    :selection="selection"
    :orderNo="true"
    :pageTotal='count'
    @search-click='searchClick'
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    countUrl="/material-web/api/goods/belong/material/count"
    :showCount ='showCount'
  ></count-list>
</template>

<script>

    import countList from '@components/common/list-count'
    export default {
      components: {
        countList,
      },
      name: "awardList",
      data() {
        let self = this;
        return {
			    showCount:false,
          dataList:[],
          count:0,
          selection:"none",
          search:{
            page_name:"goods_belong_change_material_relation",
            where:[],
            page_no:1,
            page_size:50
          },
          btns:[
            {
              type: "success",
              txt: "刷新",
              loading: false,
              click() {
                self.getList();
              },
            },
            {
              type: "primary",
              txt: "新增",
              loading: false,
              click() {
                let params = {};
                params.id = null;
                self.$root.eventHandle.$emit('creatTab', {
                  name: "新增物料归属关系",
                  params: params,
                  component: () => import('@components/mdm/module/belong/add_material.vue')
                });
              },
            },
          ],
          cols:[
            {
              label: "名称",
              prop: "name",
              width: 160,
              redirectClick(row) {
                let params = {};
                params.id = row.id;
                self.$root.eventHandle.$emit('creatTab', {
                  name: "物料归属关系详情",
                  params: params,
                  component: () => import('@components/mdm/module/belong/add_material.vue')
                });
              },
            },
            
            {
              label: "关系类型	",
              prop: "type",
              format: 'auxFormat',
					    formatParams: 'material_belong_type',
              width: 120,
            },
            {
              label: "状态",
              prop: "status",
              width: 120,
              formatter(val){
                return val == 1?'生效':'失效';
              }
            },

            {
              label: "创建人",
              prop: "creatorName",
              width: 120,
            },
            {
              label: "创建时间	",
              prop: "createTime",
              width: 120,
              format:'dataFormat1'
            },
          ],
        }
      },
      methods: {
        getList(resolve,callback){
          this.btns[0].loading = true;
          this.ajax.postStream('/material-web/api/goods/belong/material/list',this.search,res => {
            callback && callback();
            if(res.body.result && res.body.content) {
              this.dataList = res.body.content.list;
              // this.count = res.body.content.count;
              if(!this.showCount){
                let total = this.search.page_size * this.search.page_no
                this.count = res.body.content.list.length == (this.search.page_size+1)? total+1:total;
            }
            } else {
              res.body.msg && this.$message.error(res.body.msg);
            }
            resolve && resolve();
            this.btns[0].loading = false;
          }, err => {
            resolve && resolve();
            this.btns[0].loading = false;
            this.$message.error(err);
          });
        },
        
        addAward(){
          this.$root.eventHandle.$emit('creatTab', {
            name: "新增中奖名单",
            component: () => import('@components/discount/addAward.vue')
          });
        },
        searchClick (list, resolve){
          this.search.where = list;
          new Promise((res,rej)=>{
            this.getList(resolve,res);
          }).then(()=>{
            if(this.search.page_no != 1){
              this.count = 0;
            }
            this.showCount = false;
          })
          // this.getList(resolve);
        },
        // 监听每页显示数更改事件
        sizeChange(pageSize){
          this.search.page_size = pageSize;
          this.getList();
        },
        // 监听页数更改事件好
        pageChange(page){
          this.search.page_no = page;
          this.getList();
        },
      },
      mounted: function() {
        this.getList();
        this.$root.eventHandle.$on("refresh_invoice", d => {
          this.getList();
        });
      }
    }
</script>

<style scoped>

</style>
