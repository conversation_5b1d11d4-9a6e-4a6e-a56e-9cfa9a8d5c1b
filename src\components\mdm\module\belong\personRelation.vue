<template>
<div class='xpt-flex'>
	<xpt-headbar>
		<el-button type='primary' slot='left' size='mini' @click="save">保存</el-button>
		
	</xpt-headbar>
  <el-form label-width='100px' :model='query'  ref="query" :rules="rules">
 
    <el-row :gutter='40' style='height:80px'>
      <el-col :span='40'  class="xpt_col">
        <el-form-item label='排序' prop='orderNo' required>
          <el-input v-model="query.orderNo" size="mini"  type="number"></el-input>
          <el-tooltip v-if='rules.orderNo[0].isShow' class="item" effect="dark" :content="rules.orderNo[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
         <el-form-item label='渠道专供' prop='channelFor' required>
          <!-- <xpt-select-aux
              v-model="query.channelFor"
              aux_name="material_for_channel"
              placeholder="请选择渠道"
              size="mini"
          ></xpt-select-aux> -->
          	<mdm-select
              enum-id="1234"
              default-val=""
              v-model="query.channelFor"
            ></mdm-select>
          <el-tooltip v-if='rules.channelFor[0].isShow' class="item" effect="dark" :content="rules.channelFor[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label='工号' prop='employeeNumber' required>
          <xpt-input size='mini' v-model="query.employeeNumber" readonly icon='search' :on-icon-click="openSatff" :disabled="false"></xpt-input>
          <el-tooltip v-if='rules.employeeNumber[0].isShow' class="item" effect="dark" :content="rules.employeeNumber[0].message" placement="right-start" popper-class='xpt-form__error'>
            <i class='el-icon-warning'></i>
          </el-tooltip>
        </el-form-item>
      </el-col>
    </el-row>
  
  </el-form>
</div>
</template>
<script>
import validate from '@common/validate.js';
import MdmSelect from '@components/mdm/components/form/MdmSelect'
  export default {
    components: {
      MdmSelect
    },
    data() {
      let self = this;
      return {
        oldGoodsList: [],
        query:{
          orderNo:'',
          employeeNumber:'',
          channelFor:''
        },
        // hasData:self.params.sortList.length,
        // canEditData:self.params.canChangeList.length
        rules: {
            orderNo:validate.isNotBlank({
              required:true,
              self:self
            }),
            channelFor:validate.isNotBlank({
              required:true,
              self:self
            }),
            employeeNumber:validate.isNotBlank({
              required:true,
              self:self
            }),
          }
      }
    },
	  props:['params'],
    methods: {
		openSatff(staffType, arg) {
        let self = this
        let params = {
          callback(data) {

            self.query.employeeNumber =data.data.employeeNumber;
            self.query.userId = data.data.id;
            self.query.userName = data.data.fullName;
          },
          salesmanTypeList: []
        }
        this.$root.eventHandle.$emit('alert', {
          params: params,
          component:() => import('@components/personel/list'),
          style: 'width:800px;height:500px',
          title: '人员列表'
        })
      },
      close(){
        let self = this;
        self.$root.eventHandle.$emit('removeAlert',self.params.alertId);
      },
      save(){
        this.$refs.query.validate((valid) => {
          if (!valid) return
          if (!(/^([1-9]\d*)$/.test(this.query.orderNo))) {
            this.$message.error('【排序】请输入正整数！')
            return
          }
          if (this.params.orderNos.includes(this.query.orderNo)) {
            this.$message.error(`【排序】为${this.query.orderNo}已存在，请重新输入！`)
            return
          }
          this.params.callback(this.query);
          this.close();
        })
      },
      changeSelect(item,index){
        if(item.checked != true){
          this.oldGoodsList[index].qty = '';
          // console.log(item,index,this.oldGoodsList[index])          

        }
      }
     
    },
    mounted(){
      // this.getOldGoodsList();
    }
  }
</script>