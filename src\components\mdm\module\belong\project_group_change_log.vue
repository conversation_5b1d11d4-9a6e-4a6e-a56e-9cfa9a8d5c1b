<!-- 项目组变更记录列表 -->
<template>
  <count-list
    :data="dataList"
    :btns="btnList"
    :colData="cols"
    :searchPage="search.page_name"
    :selection="selection"
    :orderNo="true"
    :pageTotal="count"
    @search-click="searchClick"
    @page-size-change="sizeChange"
    @current-page-change="pageChange"
    countUrl="/material-web/api/projectTeamChange/count"
    :showCount="showCount"
  ></count-list>
</template>

<script>
import countList from "@components/common/list-count";
export default {
  components: {
    countList,
  },
  name: "awardList",
  data() {
    let self = this;
    return {
      showCount: false,
      dataList: [],
      count: 0,
      selection: "none",
      search: {
        page_name: "project_team_change_record",
        where: [],
        page_no: 1,
        page_size: 50,
      },
      btnList: [],
      cols: [
        {
          label: "物料编码",
          prop: "material_number",
          width: 160,
        },

        {
          label: "物料名称",
          prop: "material_name",
          width: 120,
        },
        {
          label: "规格描述",
          prop: "material_specification",
          width: 220
        },
        {
          label: "主型号",
          prop: "main_model",
          width: 120,
        },
        {
          label: "项目组",
          prop: "project_team_name",
          width: 120,
        },
        {
          label: "影响开始时间",
          prop: "enable_time",
          width: 130,
          format:'dataFormat1'
        },
        {
          label: "影响结束时间",
          prop: "disable_time",
          width: 130,
          format:'dataFormat1'
        },
        {
          label: "状态",
          prop: "status",
          formatter:(val)=>{
            let status = {
              CREATE:'创建',
              ENABLE:'生效',
              DISABLE:'失效'
            }
            return status[val] || val;
          }
        },

        {
          label: "创建人",
          prop: "creator_name",
          width: 120,
        },
        {
          label: "创建时间	",
          prop: "create_time",
          width: 120,
          format: "dataFormat1",
        },
        {
          label: "来源单据号",
          prop: "source_bill_no",
          width: 120,
        },
      ],
    };
  },
  methods: {
    getList(resolve, callback) {
      this.ajax.postStream(
        "/material-web/api/projectTeamChange/list",
        this.search,
        (res) => {
          callback && callback();
          if (res.body.result && res.body.content) {
            this.dataList = res.body.content.list;
            if (!this.showCount) {
              let total = this.search.page_size * this.search.page_no;
              this.count =
                res.body.content.list.length == this.search.page_size + 1
                  ? total + 1
                  : total;
            }
          } else {
            res.body.msg && this.$message.error(res.body.msg);
          }
          resolve && resolve();
        },
        (err) => {
          resolve && resolve();
          this.$message.error(err);
        }
      );
    },

    searchClick(list, resolve) {
      this.search.where = list;
      new Promise((res, rej) => {
        this.getList(resolve, res);
      }).then(() => {
        if (this.search.page_no != 1) {
          this.count = 0;
        }
        this.showCount = false;
      });
      // this.getList(resolve);
    },
    // 监听每页显示数更改事件
    sizeChange(pageSize) {
      this.search.page_size = pageSize;
      this.getList();
    },
    // 监听页数更改事件好
    pageChange(page) {
      this.search.page_no = page;
      this.getList();
    },
  },
  mounted: function () {
    this.getList();
  },
};
</script>

<style scoped>
</style>
