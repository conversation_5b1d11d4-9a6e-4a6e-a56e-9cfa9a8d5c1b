<template>
	<form-layout
		ref="formLayout"
		:id="id"
		:formType="formType"
		:btn-list="btnList"
		:top-tabs="topTabs"
		:bottom-tabs="bottomTabs"
		@reloadForm="reloadForm"
		@reloadFormBy="reloadFormBy"
	>
		<el-form
			:model="ruleForm"
			ref="topHead"
			label-width="120px"
			slot="header"
		>
			<div class="mdm-header">
				<el-form-item label="创建组织" prop="createOrgId">
					<mdm-select
						enum-id="e10d4c32-a5fe-4730-819a-8422712635e6"
						default-val="100001"
						v-model="ruleForm.createOrgId"
						disabled
					/>
				</el-form-item>
				<el-form-item label="版本" prop="number">
					<el-input
						placeholder="审核时自动生成"
						:maxlength="80"
						size="mini"
						v-model="ruleForm.number"
						:disabled="ruleForm.documentStatus === 'C' || readonly"
					/>
				</el-form-item>
				<el-form-item label="使用组织" prop="useOrgId">
					<mdm-select
						enum-id="e10d4c32-a5fe-4730-819a-8422712635e6"
						default-val="100001"
						v-model="ruleForm.useOrgId"
						disabled
					/>
				</el-form-item>
				<el-form-item label="BOM简称" prop="bomName">
					<el-input
						:maxlength="255"
						size="mini"
						v-model="ruleForm.bomName"
						:disabled="ruleForm.documentStatus === 'C' || readonly"
					/>
				</el-form-item>
			</div>

			<el-dialog title="修改BOM体积" :visible.sync="dialogImageVisible">
				<div>
					<el-form
						:model="ruleFormVolume"
						ref="ruleFormVolume"
						:rules="rules"
						label-width="120px"
					>
						<el-form-item label="BOM体积" prop="flsVolume">
							<el-input size="mini" v-model.trim="ruleFormVolume.flsVolume"/>
							<el-tooltip
								v-if="rules.flsVolume[0].isShow"
								class="item"
								effect="dark"
								:content="rules.flsVolume[0].message"
								placement="right-start"
								popper-class="xpt-form__error"
							>
								<i class="el-icon-warning"></i>
							</el-tooltip>
						</el-form-item>
						<el-form-item>
							<el-button @click="dialogImageVisible = false">取 消</el-button>
							<el-button type="primary" @click="updateBomVolume">确 定</el-button>
						</el-form-item>
					</el-form>
				</div>
			</el-dialog>

		</el-form>
	</form-layout>
</template>

<script>
import Verify from "@components/mdm/verify/index";
import FormLayout from "@components/mdm/components/layout/form-layout";
import MdmSelect from "@components/mdm/components/form/MdmSelect";

export default {
	components: {FormLayout, MdmSelect},
	props: ["params"],
	data() {
		let self = this;
		return {
			id: null,
			formType: "new",
			dialogImageVisible: false,
			ruleFormVolume: {
				bomId: "",
				flsVolume: "",
			},
			rules: {
				flsVolume: [
					{
						required: false,
						message: "请输入数字,规则0000000000000.000",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (Verify.isNumber3(value)) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
			},
			btnList: [
				{
					label: "新增",
					type: "primary",
					code: "new",
					onClick: () => {
						this.add();
					},
				},
				{
					label: "删除",
					type: "danger",
					code: "del",
					show: "0",
					onClick: () => {
						this.del();
					},
				},
				{
					label: "保存",
					code: "draft",
					type: "info",
					onClick: (btn, callLoad) => {
						this.verify("Z", btn, callLoad);
					},
				},
				{
					label: "审核",
					code: "save",
					type: "success",
					onClick: (btn, callLoad) => {
						this.verify1("C", btn, callLoad);
					},
				},
				{
					label: "打开编辑",
					code: "openEdit",
					type: "warning",
					show: "0",
					onClick: () => {
						let btnList = this.$refs.formLayout.$refs.btnList;
						this.ajax.get(
							`/mdm-web/api/material/bom/checkLock/${this.id}/${
								this.readonly ? "open" : "close"
							}?permissionCode=MDM_BOM_DRAFT`,
							(res) => {
								if (res.body.result) {
									//未上锁
									btnList.updateLabel("", "lock");
									btnList.showBtn("0", "lock");
									this.$refs.formLayout.communicationReadonly();
									let btn = this.$refs.formLayout.$refs.btnList.getBtn(
										"openEdit"
									);
									let label;
									if (btn.label === "关闭编辑") {
										label = "打开编辑";
										this.readonly = true;
										this.$refs.formLayout.communicationBtn(
											"btnList",
											"disabledBtn",
											true,
											"draft",
											"save"
										);
									} else {
										label = "关闭编辑";
										this.readonly = false;
										this.$refs.formLayout.communicationBtn(
											"btnList",
											"disabledBtn",
											false,
											"draft",
											"save"
										);
									}
									this.$refs.formLayout.$refs.btnList.updateLabel(
										label,
										"openEdit"
									);
								} else {
									//已上锁
									let msg;
									let text;
									if (res.body.content) {
										msg =
											res.body.content.locker_name +
											"正在编辑该物料，请在他操作完成后继续操作";
										text = res.body.content.locker_name + "正在编辑该物料";
									} else if (res.body.msg) {
										msg = res.body.msg;
										text = res.body.msg;
									}
									this.$notify.error({
										title: "物料操作提示",
										message: msg,
									});
									btnList.updateLabel(text, "lock");
									btnList.showBtn("1", "lock");
								}
							}
						);
					},
				},
				{
					code: "disable",
					type: "primary",
					label: "禁用",
					show: "0",
					onClick: () => {
						this.toDisable();
					},
				},
				{
					code: "enable",
					type: "primary",
					label: "启用",
					show: "0",
					onClick: () => {
						this.toEnable();
					},
				},
				{
					label: "复制",
					code: "copy",
					show: "0",
					onClick: () => {
						this.$root.eventHandle.$emit("creatTab", {
							name: "新增物料清单信息",
							params: {id: this.id, formType: "copy"},
							component: () =>
								import("@components/mdm/module/bom/AddOrUpdate.vue"),
						});
					},
				},
				{
					label: "组合商品图合成",
					code: "zzspthc",
					list: [
						{
							label: "合成材质图",
							onClick: () => {
								this.merge("CZ");
							},
						},
						{
							label: "合成尺寸图",
							onClick: () => {
								this.merge("CC");
							},
						},
					],
				},
				{
					label: "修改BOM体积",
					code: "updateBomVolume",
					show: "0",
					onClick: () => {
						let formLayout = this.$refs.formLayout;
						let formData = formLayout.getFormData();
						let bom = JSON.parse(JSON.stringify(formData.topTabs.info));
						this.ruleFormVolume.flsVolume = bom.flsVolume;
						this.ruleFormVolume.bomId = bom.bomId;
						this.dialogImageVisible = true;
					},
				},
				{
					label: "刷新",
					code: "refresh",
					show: "0",
					onClick: () => {
						this.$refs.formLayout.loadItem(this.id, "refresh");
					},
				},
				{
					label: "",
					type: "status",
					code: "lock",
					show: "0",
				},
			],
			topTabs: [
				{
					label: "主产品",
					name: "info",
					path: "module/bom/Info",
					isDefault: true,
					isVerify: true,
				},
			],
			bottomTabs: [
				{
					label: "子项明细",
					name: "items",
					path: "module/bom/Items",
					isDefault: true,
					isVerify: true,
				},
				{
					label: "操作记录",
					name: "record",
					path: "common/OperationRecordList2",
					businessType: "BB",
					loadData: "loadData",
				},
			],

			ruleForm: {
				createOrgId: "",
				useOrgId: "",
				number: "",
				bomName: "",
				flsVersionno: "",
			},
			readonly: false,

			changeStatus: false,
			isCloseTabShow: false,
		};
	},
	methods: {
		updateBomVolume() {
			let _this = this;
			let ruleFormVolume = this.$refs.ruleFormVolume;
			ruleFormVolume.validate((valid) => {
				if (valid) {
					_this.ajax.postStream(
						"/mdm-web/api/material/bom/updateBomVolume?permissionCode=MDM_BOM_SAVE",
						_this.ruleFormVolume,
						(res) => {
							if (!res.body.result) {
								_this.$notify.error({
									title: "错误提示",
									message: res.body.msg,
								});
							} else {
								_this.$notify.success({
									title: "成功提示",
									message: res.body.msg,
								});
								_this.dialogImageVisible = false;
								let formLayout = _this.$refs.formLayout;
								formLayout.loadItem(_this.id, "refresh");
							}
						}
					);
				}
			});
		},
		verify(status, btn, callLoad) {
			let _this = this;
			let formLayout = this.$refs.formLayout;
			formLayout.verify().then((result) => {
				if (result) {
					if (typeof (callLoad) === "function") {
						callLoad(btn, true)
					}
					let formData = formLayout.getFormData();
					let bom = JSON.parse(JSON.stringify(formData.topTabs.info));
					bom = Object.assign(bom, this.ruleForm);
					bom.documentStatus = status;
					bom.bomItemVOList = JSON.parse(
						JSON.stringify(formData.bottomTabs.items)
					);
					this.ajax.postStream(
						"/mdm-web/api/material/bom/save?permissionCode=MDM_BOM_SAVE",
						bom,
						(res) => {
							if (!res.body.result) {
								_this.$notify.error({
									title: "错误提示",
									message: res.body.msg,
								});
							} else {
								_this.$notify.success({
									title: "成功提示",
									message: res.body.msg,
								});

								//是否关闭窗口
								if (_this.isCloseTabShow) {
									_this.$root.eventHandle.$emit(
										"removeTab",
										_this.params.tabName
									);
								}

								//新增成功后的新对象
								_this.id = res.body.content.bomId;
								//加载新数据
								_this.formType = "update";
								formLayout.loadItem(_this.id);

								if (bom.documentStatus === "C") {
									this.$refs.formLayout.$refs.btnList.updateLabel(
										"打开编辑",
										"openEdit"
									);
								}
							}
							if (typeof (callLoad) === "function") {
								callLoad(btn, false)
							}
						}
					);
				}
			});
		},
		verify1(status, btn, callLoad) {
			let _this = this;
			let formLayout = this.$refs.formLayout;
			formLayout.verify().then((result) => {
				if (result) {
					if (typeof (callLoad) === "function") {
						callLoad(btn, true)
					}
					let formData = formLayout.getFormData();
					let bom = JSON.parse(JSON.stringify(formData.topTabs.info));
					bom = Object.assign(bom, this.ruleForm);
					bom.documentStatus = status;
					bom.bomItemVOList = JSON.parse(
						JSON.stringify(formData.bottomTabs.items)
					);
					this.ajax.postStream(
						"/mdm-web/api/material/bom/save?permissionCode=MDM_BOM_AUDIT",
						bom,
						(res) => {
							if (!res.body.result) {
								_this.$notify.error({
									title: "错误提示",
									message: res.body.msg,
								});
							} else {
								_this.$notify.success({
									title: "成功提示",
									message: res.body.msg,
								});

								//是否关闭窗口
								if (_this.isCloseTabShow) {
									_this.$root.eventHandle.$emit(
										"removeTab",
										_this.params.tabName
									);
								}

								//新增成功后的新对象
								_this.id = res.body.content.bomId;
								//加载新数据
								_this.formType = "update";
								formLayout.loadItem(_this.id);

								if (bom.documentStatus === "C") {
									this.$refs.formLayout.$refs.btnList.updateLabel(
										"打开编辑",
										"openEdit"
									);
								}
							}
							if (typeof (callLoad) === "function") {
								callLoad(btn, false)
							}
						}
					);
				}
			});
		},
		toDisable() {
			let bom = {forbidStatus: "B"};
			/*if (this.containStatus("forbidStatus", "B", "不能选择已经被禁用的物料BOM")) {
			  return;
			}*/
			this.saveByStatus(bom, "正在禁用选中的物料BOM");
		},
		toEnable() {
			let bom = {forbidStatus: "A"};
			/*if (this.containStatus("forbidStatus", "A", "不能选择已经被启用的物料BOM")) {
			  return;
			}*/
			this.saveByStatus1(bom, "正在启用选中的物料BOM");
		},

		saveByStatus(infoForm, type) {
			let formLayout = this.$refs.formLayout;
			let formData = formLayout.getFormData();
			let bom = formData.topTabs.info;
			this.$confirm(`${type}, 是否继续?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then(() => {
					let list = [Object.assign({bomId: bom.bomId}, infoForm)];
					this.ajax.postStream(
						"/mdm-web/api/material/bom/updateList?permissionCode=MDM_BOM_DISABLED",
						list,
						(res) => {
							if (res.body.result) {
								formLayout.loadItem(this.id, "refresh");
							}
							this.$notify({
								title: "操作提示",
								message: res.body.msg,
								type: res.body.result ? "success" : "error",
							});
						}
					);
				})
				.catch(() => {
					this.$message({
						type: "info",
						message: "已取消",
					});
				});
		},
		saveByStatus1(infoForm, type) {
			let formLayout = this.$refs.formLayout;
			let formData = formLayout.getFormData();
			let bom = formData.topTabs.info;
			this.$confirm(`${type}, 是否继续?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then(() => {
					let list = [Object.assign({bomId: bom.bomId}, infoForm)];
					this.ajax.postStream(
						"/mdm-web/api/material/bom/updateList?permissionCode=MDM_BOM_ENABLE",
						list,
						(res) => {
							if (res.body.result) {
								formLayout.loadItem(this.id, "refresh");
							}
							this.$notify({
								title: "操作提示",
								message: res.body.msg,
								type: res.body.result ? "success" : "error",
							});
						}
					);
				})
				.catch(() => {
					this.$message({
						type: "info",
						message: "已取消",
					});
				});
		},
		containStatus(statusProperty, statusVal, confirm) {
			let bom = this.getFormData();
			let existObj = bom[statusProperty] === statusVal;
			if (existObj) {
				this.$message({
					type: "warning",
					message: confirm || "选择了不合法的数据",
				});
				return true;
			}
			return false;
		},
		getFormData() {
			let formLayout = this.$refs.formLayout;
			let formData = formLayout.getFormData();
			let bom = formData.topTabs.info;
			return bom;
		},
		reloadForm(form, formType) {
			this.ruleForm.number = form.number.trim();
			this.ruleForm.bomName = form.bomName.trim();
			this.ruleForm.flsVersionno = form.flsVersionno;
			this.ruleForm.documentStatus = form.documentStatus;
			if (formType === "copy") {
				this.ruleForm.documentStatus = "Z";
				this.ruleForm.number = "";
				this.ruleForm.flsSendstatus = "";
				this.ruleForm.flsSendtime = "";
				this.ruleForm.flsSendinfo = "";
				this.ruleForm.k3PropertyId = "";
				this.readonly = false;
			} else {
				if (form.documentStatus === "C") {
					this.readonly = true;
				} else {
					this.readonly = false;
				}
			}
		},
		reloadFormBy(name,value){
			console.log('wode==',name,value)
			this.$set(this.ruleForm,name,value.trim())
		},
		del() {
			this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then((res) => {
					this.ajax.get(
						"/mdm-web/api/material/bom/del/" + this.getFormData().bomId,
						(res) => {
							this.$notify({
								title: "操作提示",
								message: res.body.msg,
								type: res.body.result ? "success" : "error",
							});
							if (res.body.result) {
								this.$root.eventHandle.$emit("removeTab", this.params.tabName);
							}
						}
					);
				})
				.catch(() => {
					this.$message({
						type: "info",
						message: "已取消删除",
					});
				});
		},
		add() {
			this.$root.eventHandle.$emit("creatTab", {
				name: "新增物料清单信息",
				params: {id: null},
				component: () => import("@components/mdm/module/bom/AddOrUpdate.vue"),
			});
		},

		// 关闭标签页
		closeTab() {
			let self = this;

			let edit = this.btnList.find((item) => item.code === "openEdit");
			if (
				edit &&
				edit.show === "1" &&
				edit.label === "打开编辑" &&
				this.params.id
			) {
				self.$root.eventHandle.$emit("removeTab", self.params.tabName);
				return;
			}

			let formLayout = self.$refs.formLayout;
			let changeStatus = formLayout.isUpdateFormData();
			if (changeStatus || this.changeStatus) {
				self.$root.eventHandle.$emit("openDialog", {
					ok() {
						let formData = formLayout.getFormData();
						let bom = JSON.parse(JSON.stringify(formData.topTabs.info));
						self.isCloseTabShow = true;
						self.verify(bom.documentStatus);
					},
					no() {
						self.$root.eventHandle.$emit("removeTab", self.params.tabName);
					},
				});
			} else {
				self.$root.eventHandle.$emit("removeTab", self.params.tabName);
			}
		},
		merge(mergeType) {
			let bom = this.getFormData();
			let url = `/mdm-web/api/material/merge/${bom.materialId}/${mergeType}?permissionCode=MDM_BOM_MERGE`;
			this.ajax.get(url, (res) => {
				this.$message({
					type: res.body.result ? "success" : "error",
					message: res.body.msg,
				});
			});
		},
	},
	mounted() {
		let self = this;
		self.params.__close = self.closeTab;
	},
	created() {
		this.id = this.params ? this.params.id : null;
		this.formType = this.params ? this.params.formType : "new";
		this.$nextTick(() => {
			this.$watch(
				"ruleForm",
				() => {
					console.log(
						"------------------物料清单基本信息有改动---------------------"
					);
					this.changeStatus = true;
				},
				{deep: true}
			);
		});
	},
};
</script>

<style scoped>
.mdm-header {
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	height: 60px !important;
}
</style>
