<template>
	<el-form :model="ruleForm" ref="form" :rules="rules" label-width="120px">
		<div class="sample">
			<el-form-item label="BOM分类" prop="bomCategory">
				<mdm-select
					enum-id="3ecbac23-ad52-4fc2-9478-12623470ba80"
					v-model="ruleForm.bomCategory"
					:disabled="globallyDisabled('bomCategory')"
				/>
				<el-tooltip
					v-if="rules.bomCategory[0].isShow"
					class="item"
					effect="dark"
					:content="rules.bomCategory[0].message"
					placement="right-start"
					popper-class="xpt-form__error"
				>
					<i class="el-icon-warning"></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="BOM用途" prop="bomPurpose">
				<mdm-select
					default-val=""
					enum-id="5936281c-5201-4e01-b53b-a368a752d22f"
					v-model="ruleForm.bomPurpose"
					:disabled="globallyDisabled('bomPurpose')"
				/>
				<el-tooltip
					v-if="rules.bomPurpose[0].isShow"
					class="item"
					effect="dark"
					:content="rules.bomPurpose[0].message"
					placement="right-start"
					popper-class="xpt-form__error"
				>
					<i class="el-icon-warning"></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="BOM分组" prop="bomGroupName">
				<mdm-input
					v-model="ruleForm.bomGroupName"
					size="mini"
					icon="search"
					:on-icon-click="listChooseBomGroupName"
					@change="
            () => {
              this.ruleForm.bomGroup = '';
              this.ruleForm.bomGroupName = '';
            }
          "
					:disabled="globallyDisabled('bomGroup')"
				/>
				<el-tooltip
					v-if="rules.bomGroupName[0].isShow"
					class="item"
					effect="dark"
					:content="rules.bomGroupName[0].message"
					placement="right-start"
					popper-class="xpt-form__error"
				>
					<i class="el-icon-warning"></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="BOM类别" prop="flsClass">
				<mdm-select
					enum-id="245474c7-6d0a-4e8b-b002-b08759a52555"
					v-model="ruleForm.flsClass"
					:disabled="globallyDisabled('flsClass')"
				/>
			</el-form-item>
			<el-form-item/>
			<el-form-item/>
			<el-form-item/>
			<el-form-item/>
			<el-form-item/>
			
			<el-form-item label="父项物料编码" prop="materialNumber">
				<mdm-input
					v-model="ruleForm.materialNumber"
					size="mini"
					icon="search"
					:on-icon-click="listChooseParentMaterialNumber"
					@change="
            () => {
              this.ruleForm.unitId = '';
              this.ruleForm.unitName = '';
              this.ruleForm.materialId = '';
              this.ruleForm.materialNumber = '';
              this.ruleForm.materialName = '';
              this.ruleForm.materialSpec = '';
              this.ruleForm.materialProperty = '';
              this.ruleForm.pkgQuantity = '';
              this.ruleForm.isStop = '';
              this.ruleForm.stopProduceDate = '';
              this.ruleForm.isAutoSplit = '';
              this.ruleForm.isBinding = '';
              this.ruleForm.salesType = '';
              this.ruleForm.isUploadProductImg = '0';
              this.ruleForm.isUploadMaterialImg = '0';
              this.ruleForm.isUploadSetupImg = '0';
              this.ruleForm.isUploadSizeImg = '0';
              this.ruleForm.flsVolume = '';
              this.ruleForm.flsPakeqty = '';
              this.ruleForm.chanelForName = '';
              
			  this.$emit('reloadFormBy', 'bomName', '');
            }
          "
					:disabled="globallyDisabled('materialNumber')"
				/>
				<el-tooltip
					v-if="rules.materialNumber[0].isShow"
					class="item"
					effect="dark"
					:content="rules.materialNumber[0].message"
					placement="right-start"
					popper-class="xpt-form__error"
				>
					<i class="el-icon-warning"></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="物料名称" prop="materialName">
				<el-input size="mini" v-model.trim="ruleForm.materialName" disabled/>
			</el-form-item>
			<el-form-item label="规格型号" prop="materialSpec">
				<el-input size="mini" v-model.trim="ruleForm.materialSpec" disabled/>
			</el-form-item>
			<el-form-item label="主型号" prop="mainModel">
				<el-input size="mini" v-model.trim="ruleForm.mainModel" disabled/>
			</el-form-item>
			<el-form-item label="物料属性" prop="materialProperty">
				<el-input
					size="mini"
					v-model.trim="ruleForm.materialProperty"
					disabled
				/>
			</el-form-item>
			<el-form-item label="包件数" prop="flsPakeqty">
				<el-input size="mini" v-model.trim="ruleForm.flsPakeqty" disabled/>
			</el-form-item>
			<el-form-item label="是否停产" prop="isStop">
				<el-input size="mini" v-model.trim="ruleForm.isStop" disabled/>
			</el-form-item>
			<el-form-item label="停产日期" prop="stopProduceDate">
				<el-input
					size="mini"
					:value="formatDate(ruleForm.stopProduceDate)"
					disabled
				/>
			</el-form-item>
			<el-form-item/>
			<el-form-item/>
			
			<el-form-item label="父项物料单位" prop="unitName">
				<mdm-input
					v-model="ruleForm.unitName"
					size="mini"
					icon="search"
					:on-icon-click="listChooseUnitName"
					@change="
            () => {
              this.ruleForm.unitId = '';
              this.ruleForm.unitName = '';
            }
          "
					disabled
				/>
				<el-tooltip
					v-if="rules.unitName[0].isShow"
					class="item"
					effect="dark"
					:content="rules.unitName[0].message"
					placement="right-start"
					popper-class="xpt-form__error"
				>
					<i class="el-icon-warning"></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="绑定销售" prop="isBinding">
				<el-input size="mini" v-model.trim="ruleForm.isBinding" disabled/>
			</el-form-item>
			<el-form-item label="组合自动拆分" prop="isAutoSplit">
				<el-input size="mini" v-model.trim="ruleForm.isAutoSplit" disabled/>
			</el-form-item>
			<el-form-item label="描述" prop="description" style="height: 120px">
				<mdm-input
					:maxlength="255"
					:autosize="{ minRows: 6, maxRows: 6 }"
					type="textarea"
					size="mini"
					v-model="ruleForm.description"
					:disabled="readonly"
				/>
			</el-form-item>
			<el-form-item label="父项销售类型" prop="saleType">
				<el-input size="mini" v-model.trim="ruleForm.salesType" disabled/>
			</el-form-item>
			<el-form-item label="渠道专供" prop="chanelForName"> 
				<el-input
					size="mini"
					v-model.trim="ruleForm.chanelForName"
					disabled
				/>
			</el-form-item>
			<el-form-item/>
			
			<el-form-item label="BOM体积" prop="flsVolume">
				<el-input
					size="mini"
					v-model.trim="ruleForm.flsVolume"
					:disabled="readonly"
					:maxlength=23
				/>
				<el-tooltip
					v-if="rules.flsVolume[0].isShow"
					class="item"
					effect="dark"
					:content="rules.flsVolume[0].message"
					placement="right-start"
					popper-class="xpt-form__error"
				>
					<i class="el-icon-warning"></i>
				</el-tooltip>
			</el-form-item>
			<el-form-item label="同步状态">
				<mdm-select
					enum-id="8ccb4fdd-ec12-48f3-bd27-557f5b894777"
					default-val="1"
					v-model="ruleForm.flsSendstatus"
					:disabled="true"
				></mdm-select>
			</el-form-item>
			<el-form-item label="同步时间">
				<el-date-picker
					v-model="ruleForm.flsSendtime"
					type="datetime"
					placeholder="选择日期"
					size="mini"
					:disabled="true"
				></el-date-picker>
			</el-form-item>
			<el-form-item label="同步结果描述" style="height: 50px">
				<el-input
					type="textarea"
					size="mini"
					v-model="ruleForm.flsSendinfo"
					disabled
				></el-input>
			</el-form-item>
			<el-form-item/>
			<el-form-item/>
			<el-form-item/>
			<el-form-item/>
			<el-form-item/>
			
			<el-form-item label="是否上传产品图片">
				<el-switch
					disabled
					v-model="ruleForm.isUploadProductImg"
					on-text="是"
					off-text="否"
					on-value="是"
					off-value="否"
				>
				</el-switch>
			</el-form-item>
			<el-form-item label="是否上传材质图片">
				<el-switch
					disabled
					v-model="ruleForm.isUploadMaterialImg"
					on-text="是"
					off-text="否"
					on-value="是"
					off-value="否"
				>
				</el-switch>
			</el-form-item>
			<el-form-item label="是否上传安装图片">
				<el-switch
					disabled
					v-model="ruleForm.isUploadSetupImg"
					on-text="是"
					off-text="否"
					on-value="是"
					off-value="否"
				>
				</el-switch>
			</el-form-item>
			<el-form-item label="是否上传尺寸图片">
				<el-switch
					disabled
					v-model="ruleForm.isUploadSizeImg"
					on-text="是"
					off-text="否"
					on-value="是"
					off-value="否"
				>
				</el-switch>
			</el-form-item>
			<el-form-item label="数据状态" prop="documentStatus">
				<mdm-select
					enum-id="fab41ac7-b685-4b5a-bd63-312ee8b98d69"
					default-val="Z"
					v-model="ruleForm.documentStatus"
					:disabled="globallyDisabled('documentStatus')"
				></mdm-select>
			</el-form-item>
			<el-form-item label="禁用状态" prop="forbidStatus">
				<mdm-select
					enum-id="728d78e3-b3c5-4caa-b9bf-306645172e7c"
					default-val="A"
					v-model="ruleForm.forbidStatus"
					:disabled="globallyDisabled('forbidStatus')"
				></mdm-select>
			</el-form-item>
		</div>
	</el-form>
</template>
<script>
import Verify from "@components/mdm/verify/index";
import MdmSelect from "@components/mdm/components/form/MdmSelect";
import moment from "moment";

export default {
	props: ["id", "formType"],
	components: {MdmSelect},
	data() {
		let self = this;
		return {
			disabledList: [
				"createOrgId",
				"useOrgId",
				"documentStatus",
				"forbidStatus",
			],
			ruleForm: {
				bomId: "",
				bomName: "",
				number: "",
				flsVersionno: "",
				createOrgId: "",
				useOrgId: "",
				billType: "",
				bomCategory: "",
				bomGroup: "",
				bomPurpose: "",
				materialId: "",
				flsClass: "",
				materialName: "",
				materialSpec: "",
				mainModel: "",
				materialProperty: "",
				packageNumber: "",
				isStop: "",
				stopProduceDate: "",
				unitId: "",
				isBinding: "",
				isAutoSplit: "",
				description: "",
				flsVolume: "",
				flsPakeqty: "",
				flsSendstatus: "",
				flsSendtime: "",
				flsSendinfo: "",
				
				bomGroupName: "",
				materialNumber: "",
				unitName: "",
				chanelForName:""
			},
			rules: {
				createOrgId: [
					{
						required: true,
						message: "请选择创建组织",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (value) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
				useOrgId: [
					{
						required: true,
						message: "请选择使用组织",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (value) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
				billType: [
					{
						required: true,
						message: "请选择单据类型",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (value) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
				bomCategory: [
					{
						required: true,
						message: "请选择BOM分类",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (value) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
				bomPurpose: [
					{
						required: true,
						message: "请选择BOM用途",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (value) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
				bomGroupName: [
					{
						required: true,
						message: "请选择BOM分组",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (value) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
				materialNumber: [
					{
						required: true,
						message: "请选择父项物料编码",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (value) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
				unitName: [
					{
						required: true,
						message: "请选择父项物料单位",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (value) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
				flsVolume: [
					{
						required: false,
						message: "请输入数字,规则0000000000000.000",
						isShow: false,
						validator: function (rule, value, callback) {
							// 数据校验
							if (!value || Verify.isNumber3(value)) {
								self.rules[rule.field][0].isShow = false;
								// 校验成功
								callback();
							} else {
								self.rules[rule.field][0].isShow = true;
								// 校验失败
								callback(new Error(""));
							}
						},
					},
				],
			},
			readonly: false,
			loadType: null,
			
			changeStatus: false,
		};
	},
	computed: {
		globallyDisabled() {
			return (name) => {
				if (this.ruleForm.documentStatus === "C") return true;
				return this.readonly ? true : this.disabledList.includes(name);
			};
		},
	},
	watch: {
		ruleForm(val) {
			//数据状态决定是否只读
			if ("C" === val.documentStatus) {
				console.log(this.loadType);
				//已保存状态下
				if (this.loadType !== "refresh") {
					this.$emit("communicationReadonly");
					this.$emit(
						"communicationBtn",
						"btnList",
						"showBtn",
						"1",
						"openEdit",
						"copy",
						"refresh",
						"disable",
						"enable"
					);
					this.$emit(
						"communicationBtn",
						"btnList",
						"disabledBtn",
						true,
						"draft",
						"save"
					);
				}
				if ("A" === val.forbidStatus) {
					this.$emit(
						"communicationBtn",
						"btnList",
						"showBtn",
						"1",
						"updateBomVolume"
					);
				} else {
					this.$emit(
						"communicationBtn",
						"btnList",
						"showBtn",
						"0",
						"updateBomVolume"
					);
				}
			} else {
				//暂存状态下
				this.$emit(
					"communicationBtn",
					"btnList",
					"showBtn",
					"0",
					"openEdit",
					"disable",
					"enable"
				);
			}
		},
		"ruleForm.bomId"(val) {
			if (val) {
				this.$emit(
					"communicationBtn",
					"btnList",
					"showBtn",
					"1",
					"copy",
					"refresh"
				);
			}
		},
	},
	methods: {
		/*父组件需要用到的三个方法：校验verify，获取提交的form数据getFormData，刷新loadForm*/
		verify() {
			let self = this;
			return new Promise((resolve, reject) => {
				self.$refs.form.validate((valid) => {
					resolve(valid);
				});
			});
		},
		getFormData(callback) {
			if (typeof callback === "function") callback(this.ruleForm);
			return this.ruleForm;
		},
		loadForm(parentId, loadType) {
			this.loadType = loadType;
			this.ajax.get(
				`/mdm-web/api/material/bom/queryByBomId/${parentId}`,
				(res) => {
					if (!res.body.result) {
						this.$notify.error({
							title: "错误提示",
							message: res.body.msg,
						});
					} else {
						this.ruleForm = res.body.content;
						this.$emit("reloadForm", this.ruleForm, this.formType);
						//复制清空id
						if (this.formType === "copy") {
							this.ruleForm.bomId = "";
							this.ruleForm.documentStatus = "Z";
							this.ruleForm.flsSendstatus = "";
							this.ruleForm.flsSendtime = "";
							this.ruleForm.flsSendinfo = "";
							this.disabledList.push("bomCategory");
						}
						//是否显示删除按钮
						if (this.ruleForm.bomId && this.ruleForm.documentStatus !== "C") {
							this.$emit("communicationBtn", "btnList", "showBtn", "1", "del");
						} else {
							this.$emit("communicationBtn", "btnList", "showBtn", "0", "del");
						}
					}
				}
			);
		},
		
		listChooseBomGroupName() {
			new Promise((resolve) => {
				setTimeout(resolve, 10);
			}).then(() => {
				let params = {type: "T_ENG_BOMGROUP"};
				params.callback = (d) => {
					//回调选中数据
					console.log(d);
					this.$set(this.ruleForm, "bomGroup", d.id);
					this.$set(this.ruleForm, "bomGroupName", d.name);
				};
				this.$root.eventHandle.$emit("alert", {
					params: params,
					component: () => import("@components/mdm/common/popups/GroupPopups"),
					style: "width:800px;height:400px",
					title: "BOM分组列表",
				});
			});
		},
		listChooseParentMaterialNumber() {
			this.$emit("communication", "items", "getFormData", (formData) => {
				let materialNumber = [];
				formData.forEach((item) => {
					if (item.materialNumber) {
						materialNumber.push(item.materialNumber);
					}
				});
				new Promise((resolve) => {
					setTimeout(resolve, 10);
				}).then(() => {
					let params = {};
					if (materialNumber.length > 0) {
						params = {materialNumbers: materialNumber.join(",")};
					}
					params.sendStatus = "7";
					params.isMainProduct = "1";
					params.status = "C";
					params.forbidStatus = "A";
					params.materialProperty = "2,3,5,1,9,4";
					params.callback = (d) => {
						//回调选中数据
						console.log(d);
						this.$set(this.ruleForm, "materialId", d.material_material_id);
						this.$set(this.ruleForm, "materialNumber", d.material_number);
						this.$set(this.ruleForm, "materialName", d.material_name);
						this.$set(this.ruleForm, "materialSpec", d.material_specification);
						this.$set(
							this.ruleForm,
							"materialProperty",
							d.base_material_property
						);
						this.$set(this.ruleForm, "isStop", d.base_is_stop);
						this.$set(this.ruleForm, "pkgQuantity", d.stock_pkg_quantity);
						this.$set(
							this.ruleForm,
							"stopProduceDate",
							d.material_stop_produce_date
						);
						this.$set(this.ruleForm, "isBinding", d.material_is_binding);
						this.$set(this.ruleForm, "isAutoSplit", d.material_is_auto_split);
						this.$set(this.ruleForm, "salesType", d.sale_sales_type);
						this.$set(
							this.ruleForm,
							"isUploadProductImg",
							d.material_is_upload_product_img
						);
						this.$set(
							this.ruleForm,
							"isUploadMaterialImg",
							d.material_is_upload_material_img
						);
						this.$set(
							this.ruleForm,
							"isUploadSetupImg",
							d.material_is_upload_setup_img
						);
						this.$set(
							this.ruleForm,
							"isUploadSizeImg",
							d.material_is_upload_size_img
						);
						this.$set(this.ruleForm, "unitName", d.produce_bom_unit_id);
						this.$set(this.ruleForm, "unitId", d.produce_bom_unit_id_standby);
						this.$set(this.ruleForm, "flsVolume", d.sale_volume);
						this.$set(this.ruleForm, "flsPakeqty", d.stock_pkg_quantity);
						this.$set(this.ruleForm, "chanelForName", d.sale_channel_for);
						
						this.$emit('reloadFormBy', 'bomName', d.material_number);
					};
					this.$root.eventHandle.$emit("alert", {
						params: params,
						component: () =>
							import("@components/mdm/common/popups/MaterialPopups"),
						style: "width:98%;height:72%",
						title: "物料列表",
					});
				});
			});
		},
		listChooseUnitName() {
			new Promise((resolve) => {
				setTimeout(resolve, 10);
			}).then(() => {
				let params = {type: "T_BD_UNIT", search: {attribute1: 1}};
				params.callback = (d) => {
					//回调选中数据
					console.log(d);
					this.$set(this.ruleForm, "unitId", d.k3Id);
					this.$set(this.ruleForm, "unitName", d.name);
				};
				this.$root.eventHandle.$emit("alert", {
					params: params,
					component: () =>
						import("@components/mdm/common/popups/BaseDataPopups"),
					style: "width:800px;height:400px",
					title: "列表",
				});
			});
		},
		
		formatDate(date) {
			if (!date) return "";
			return moment(date).format("YYYY-MM-DD HH:mm:ss");
		},
	},
	created() {
		this.$nextTick(() => {
			this.$watch(
				"ruleForm",
				(info) => {
					console.log(
						"------------------物料清单基本信息有改动---------------------"
					);
					this.changeStatus = true;
				},
				{deep: true}
			);
		});
	},
};
</script>
<style scoped>
.sample {
	overflow: auto;
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	height: 277px !important;
}

.el-checkbox {
	margin: 2px !important;
}
</style>
