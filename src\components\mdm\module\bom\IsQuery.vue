<!--物料清单正查-->
<template>
  <div class='xpt-flex'>
    <el-form :model='query' label-width='150px'>
      <div class='sample'>
        <el-form-item
          label='使用组织'
        >
          <mdm-input
            v-model='query.useOrgIdName'
            size='mini'
            icon="search"
            @change="()=>{clearSupplyOrgIdName()}"
            :on-icon-click="()=>{chooseSupplyOrgIdName()}"/>
        </el-form-item>
        <el-form-item
          label='物料编码'
        >
          <mdm-input
            v-model='query.materialIdName'
            size='mini'
            icon="search"
            :is-close-p="false"
            :readonly="false"
            @input="handInput"
            :on-icon-click="()=>{chooseMaterial()}"/>
        </el-form-item>
        <el-form-item
          label='BOM版本'
        >
          <mdm-input
            v-model='query.number'
            size='mini'
            icon="search"
            @change="()=>{clearBom()}"
            :on-icon-click="()=>{chooseBom()}"/>
        </el-form-item>
        <el-form-item>
          <el-button @click="report" type="primary" icon="search" size="small">搜索</el-button>
        </el-form-item>
      </div>
    </el-form>
    <div class='xpt-flex__bottom'>
      <mdm-tree-table
        ref="mdmTreeTable"
        url="/mdm-web/api/material/bom/report"
        child-url="/mdm-web/api/material/bom/reportChild"
        child="itemTheList"
        :level="query.level"
        :search="query"
        :columns="columns"
        :format-child="formatChild"
        :format-child-params="formatChildParams"
      />
    </div>
  </div>
</template>

<script>
  import MdmTreeTable from "@components/mdm/components/table/MdmTreeTable";
  import MdmSelect from '@components/mdm/components/form/MdmSelect'

  export default {
    components: {MdmTreeTable,MdmSelect},
    name: "IsQuery",
    data(){
      return {
        query:{
          useOrgId:'100001',
          materialId:'',
          number:'',
          level: 30,
          requirementsDate:'',
          requirementsNum:'',
          isAlternative:'',
          auxiliary:'',

          useOrgIdName:'林氏木业',
          materialIdName:'',
        },
        columns:[
          {label:'BOM层级',prop:'level',type:'parent'},
          {label:'安装图序列号',prop:'flsSerNo',width:'120'},
          {label:'子项物料名称',prop:'name',width:'200'},
          {label:'子项物料编码',prop:'materialNumber',width:'200'},
          {label:'规格型号',prop:'specification',width:'300'},
          {label:'物料属性',prop:'materialProperty',width:'100'},
          {label:'BOM版本',prop:'number',width:'150'},
          {label:'单位',prop:'unitName'},
          {label:'用量分子',prop:'numerator'},
          {label:'用量分母',prop:'denominator'},
          {label:'标准用量',prop:'standardQuantity'},
          {label:'实际数量',prop:'actQuantity'},
          {label:'子项类型',prop:'materialTypeName'},
          {label:'用量类型',prop:'dosageTypeName'},
          {label:'固定损耗',prop:'fixScrapQuantity'},
          {label:'变动损耗',prop:'varScrapRatio'},
          {label:'替代策略',prop:''},
          {label:'替代方式',prop:''},
          {label:'替代主料',prop:''},
          {label:'生效日期',prop:'effectDate',type:'date'},
          {label:'失效日期',prop:'expireDate',type:'date'},
        ]
      }
    },
    methods:{
      clearSupplyOrgIdName() {
        this.$set(this.query, 'useOrgId', '')
        this.$set(this.query, 'useOrgIdName', '')
      },
      clearMaterial() {
        this.$set(this.query, 'materialId', '')
        this.$set(this.query, 'materialIdName', '')
        this.$set(this.query, 'number', '')
      },
      clearBom() {
        this.$set(this.query, 'number', '')
      },
      chooseSupplyOrgIdName() {
        new Promise((resolve) => {
          setTimeout(resolve, 10)
        }).then(() => {
          let params = {type: 'V_SCM_OWNERORG'}
          params.callback = d => {
            //回调选中数据
            console.log(d)
            this.$set(this.query, 'useOrgId', d.k3Id)
            this.$set(this.query, 'useOrgIdName', d.name)
          }
          this.$root.eventHandle.$emit('alert', {
            params: params,
            component: () => import('@components/mdm/common/popups/BaseDataPopups'),
            style: 'width:800px;height:400px',
            title: '使用组织列表'
          });
        })
      },
      handInput(value){
        if (!value){
          this.$set(this.query, 'materialIdName', '')
          this.$set(this.query, 'materialId', '')
          this.$set(this.query, 'number', '')
          return
        }
        let _this = this
        this.$set(this.query, 'materialIdName', value)
        //获取物料id
        this.ajax.get(`/mdm-web/api/material/bom/getByMaterialNumber/${value}`, function (response) {
          if (response.body.result) {
            let materialId = response.body.content
            if (!materialId){
              _this.$notify.error({
                title: '错误提示',
                message:'物料编码【'+value+'】未找到对应的版本号'
              });
            }else {
              _this.$set(_this.query, 'materialId', materialId)
              //获取物料清单版本
              _this.ajax.get(`/mdm-web/api/material/bom/getByBomNumber/${materialId}`, function (response1) {
                if (response1.body.result) {
                  if (!response1.body.content){
                    _this.$notify.error({
                      title: '错误提示',
                      message:'物料编码【'+value+'】未找到对应的版本号'
                    });
                    _this.$set(_this.query, 'number', '')
                  }else {
                    _this.$set(_this.query, 'number', response1.body.content)
                  }
                }
              });
            }
          }
        });
      },
      chooseMaterial() {
        if (!this.query.useOrgId) {
          this.$notify.error({
            title: '错误提示',
            message: '请先选择使用组织'
          });
          return
        }
        new Promise((resolve) => {
          setTimeout(resolve, 10)
        }).then(() => {
          let params = {useOrgId: this.query.useOrgId}
          params.callback = d => {
            //回调选中数据
            this.$set(this.query, 'materialId', d.material_material_id)
            this.$set(this.query, 'materialIdName', d.material_number)
          }
          this.$root.eventHandle.$emit('alert', {
            params: params,
            component: () => import('@components/mdm/common/popups/MaterialPopups'),
            style: 'width:98%;height:72%',
            title: '物料列表'
          });
        })
      },
      chooseBom() {
        if (!this.query.materialId) {
          this.$notify.error({
            title: '错误提示',
            message: '请先选择父项物料编码'
          });
          return
        }
        new Promise((resolve) => {
          setTimeout(resolve, 10)
        }).then(() => {
          let params = {type:'T_BAS_BOM_LIST',search:{materialId:this.query.materialId}}
          params.callback = d => {
            //回调选中数据
            console.log(d)
            this.$set(this.query, 'number', d.number)
          }
          this.$root.eventHandle.$emit('alert', {
            params: params,
            component: () => import('@components/mdm/common/popups/BaseDataPopups'),
            style: 'width:98%;height:72%',
            title: '物料清单列表'
          });
        })
      },

      report(){
        if (!this.query.useOrgId||!this.query.materialId||!this.query.number) {
          this.$notify.error({
            title: '错误提示',
            message: '请检查使用组织，物料编码，bom版本是否都填写完整'
          });
          return
        }
        this.$refs.mdmTreeTable.loadData(this.query)
      },
      formatChild(childList){
        return childList
      },
      formatChildParams(row){
        return {bomId:row.bomId}
      },
    }
  }
</script>

<style scoped>
  .sample {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin: 5px;
    height: 40px;
  }
</style>
