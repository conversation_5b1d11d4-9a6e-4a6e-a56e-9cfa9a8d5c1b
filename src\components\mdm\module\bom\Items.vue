<template>
	<div class='xpt-flex'>
		<el-row
			class='xpt-top'
			:gutter='40'
		>
			<el-button
				size='mini'
				type='primary'
				@click='add'
				:disabled='readonly'
			>新增行
			</el-button>
			<el-button
				size='mini'
				type='primary'
				@click='insert'
				:disabled='readonly'
			>插入行
			</el-button>
			<el-button
				size='mini'
				type='danger'
				@click='deleteList'
				:disabled='readonly||multipleSelection.length===0'
			>批量删除
			</el-button>
		</el-row>
		<el-table
			class="mdm-table"
			ref='table'
			:data='tableData'
			highlight-current-row
			@selection-change='handleSelectionChange'
			border
		>
			<el-table-column type="expand">
				<template slot-scope="props">
					<el-form label-width='130px' :rules="rules">
						<div class="sample">
							<div>
								<mdm-title title="基本"/>
								<el-form-item label="子项物料属性">
									<span v-if="readonly">{{ props.row.materialProperty }}</span>
									<el-input
										v-else
										size="mini"
										disabled
										v-model='props.row.materialProperty'
									></el-input>
								</el-form-item>
								<el-form-item label="子项类型" prop="materialType">
									<span v-if="readonly">{{ props.row.materialTypeName }}</span>
									<mdm-select
										v-else
										enum-id="3ea005cf-4d72-4c18-8f2d-f7f93afaab1c"
                    default-val="1"
										v-model="props.row.materialType"
									/>
								</el-form-item>
								<el-form-item label="用量类型" prop="dosageType">
									<span v-if="readonly">{{ props.row.dosageTypeName }}</span>
									<mdm-select
										v-else
										enum-id="9672e5be-73d0-4b47-b354-3d6d4c7ade9b"
                    default-val="2"
										v-model="props.row.dosageType"
									/>
								</el-form-item>
								<el-form-item label="固定损耗">
									<span v-if="readonly">{{ props.row.fixScrapQuantity }}</span>
									<el-input
										v-else
										:maxlength=20
										size='mini'
										v-model='props.row.fixScrapQuantity'
									/>
								</el-form-item>
								<el-form-item label="变动损耗%">
									<span v-if="readonly">{{ props.row.varScrapRatio }}</span>
									<el-input
										v-else
										:maxlength=20
										size='mini'
										v-model='props.row.varScrapRatio'
									/>
								</el-form-item>
								<el-form-item label="供应组织">
									<span v-if="readonly">{{ props.row.supplyOrgIdName }}</span>
									<mdm-input
										v-else
										v-model='props.row.supplyOrgIdName'
										size='mini'
										icon="search"
										@change="()=>{clearSupplyOrgIdName(props.row)}"
										:on-icon-click="()=>{chooseSupplyOrgIdName(props.row)}"
									/>
								</el-form-item>
								<el-form-item label="子项bom版本">
									<span>{{ props.row.bomVersion }}</span>
									<!--                  <el-input-->
									<!--                    v-else-->
									<!--                    size="mini"-->
									<!--                    v-model='props.row.bomVersion'-->
									<!--                  />-->
								</el-form-item>
								<el-form-item label="备注" style="height: 120px">
									<mdm-input
										:maxlength="255"
										:autosize="{ minRows: 6, maxRows: 6 }"
										type="textarea"
										size="mini"
										v-model="props.row.remark"
										:disabled="readonly"
									/>
								</el-form-item>
								<el-form-item label="工序名称">
									<span v-if="readonly">{{ props.row.fLsProcessnameName }}</span>
									<mdm-input
										v-else
										v-model='props.row.fLsProcessnameName'
										size='mini'
										icon="search"
										@change="()=>{clearFLsProcessnameName(props.row)}"
										:on-icon-click="()=>{chooseFLsProcessnameName(props.row)}"
									/>
								</el-form-item>
							</div>
							<div>
								<mdm-title title="发料"/>
								<el-form-item label="发料方式">
									<span v-if="readonly">{{ props.row.issueTypeName }}</span>
									<mdm-select
										v-else
										enum-id="d5d11d81-a366-41f0-b188-41d0e9e69210"
										default-val="1"
										v-model="props.row.issueType"
									/>
								</el-form-item>
								<el-form-item label="倒冲时机">
									<span v-if="readonly">{{ props.row.backFlushTypeName }}</span>
									<mdm-select
										v-else
										:disabled="['2','4'].indexOf(props.row.issueType)===-1"
										enum-id="e4b513d0-ba18-4f47-b235-aeec5dbe3fdb"
										default-val=""
										v-model="props.row.backFlushType"
									/>
								</el-form-item>
								<el-form-item label="发料组织">
									<span v-if="readonly">{{ props.row.supplyOrgName }}</span>
									<mdm-input
										v-else
										v-model='props.row.supplyOrgName'
										size='mini'
										icon="search"
										@change="()=>{clearSupplyOrgName(props.row)}"
										:on-icon-click="()=>{chooseSupplyOrgName(props.row)}"
									/>
								</el-form-item>
								<el-form-item label="默认发料仓库">
									<span v-if="readonly">{{ props.row.stockIdName }}</span>
									<mdm-input
										v-else
										v-model='props.row.stockIdName'
										size='mini'
										icon="search"
										@change="()=>{clearStockIdName(props.row)}"
										:on-icon-click="()=>{chooseStockIdName(props.row)}"
									/>
								</el-form-item>
								<el-form-item label="超发控制方式">
									<span v-if="readonly">{{ props.row.overControllModeName }}</span>
									<mdm-select
										v-else
										enum-id="56c3f89d-d925-4156-a23f-56951d710b2e"
										default-val="3"
										v-model="props.row.overControllMode"
									/>
								</el-form-item>
								<el-form-item label="最小发料批量">
									<span v-if="readonly">{{props.row.minIssueQuantity}}</span>
									<el-input
										v-else
										size="mini"
										v-model="props.row.minIssueQuantity"
										disabled
									></el-input>
								</el-form-item>
								<el-form-item>
									<el-checkbox
										true-label="1"
										false-label="0"
										v-model="props.row.isMinIssueQuantity"
										:disabled="readonly"
									>
										领料考虑最小发料批量
									</el-checkbox>
								</el-form-item>
								<el-form-item>
									<el-checkbox
										true-label="1"
										false-label="0"
										v-model="props.row.isGetScrap"
										:disabled="readonly"
									>
										是否发损耗
									</el-checkbox>
								</el-form-item>
								<el-form-item/>
								<el-form-item/>
								<el-form-item/>
								<el-form-item/>
								<el-form-item/>
							</div>
							<div>
								<mdm-title title="计划"/>
								<el-form-item label="提前期偏置时间">
									<span v-if="readonly">{{ props.row.offsetTime }}</span>
									<el-input
										v-else
										size="mini"
										v-model='props.row.offsetTime'
                    :maxlength="23"
									/>
								</el-form-item>
								<el-form-item label="时间单位">
									<span v-if="readonly">{{ props.row.timeUnitName }}</span>
									<mdm-select
										v-else
										enum-id="3948b044-f8df-4ff0-927d-295eb4f478fc"
										default-val="1"
										v-model="props.row.timeUnit"
									/>
								</el-form-item>
								<mdm-title title="配置"/>
								<el-form-item>
									<el-checkbox
										true-label="1"
										false-label="0"
										disabled
									>可选择
									</el-checkbox>
									<el-checkbox
										true-label="1"
										false-label="0"
										disabled
									>可修改
									</el-checkbox>
									<el-checkbox
										true-label="1"
										false-label="0"
										disabled
									>可替换
									</el-checkbox>
								</el-form-item>
								<mdm-title title="替代"/>
								<el-form-item label="替代策略">
									<span v-if="readonly"></span>
									<el-input
										v-else
										size="mini"
										disabled
									></el-input>
								</el-form-item>
								<el-form-item label="替代方式">
									<span v-if="readonly"></span>
									<el-input
										v-else
										size="mini"
										disabled
									></el-input>
								</el-form-item>
								<el-form-item label="替代优先级">
									<span v-if="readonly">0</span>
									<el-input
										v-else
										size="mini"
										:value="0"
										disabled
									></el-input>
								</el-form-item>
								<el-form-item label="动态优先级">
									<span v-if="readonly">0</span>
									<el-input
										v-else
										size="mini"
										:value="0"
										disabled
									></el-input>
								</el-form-item>
								<el-form-item>
									<el-checkbox
										true-label="1"
										false-label="0"
										disabled
									>替代主料
									</el-checkbox>
								</el-form-item>
								<el-form-item/>
							</div>
							<div>
								<mdm-title title="货主"/>
								<el-form-item label="货主类型">
									<span v-if="readonly">{{ props.row.ownerTypeIdName }}</span>
									<mdm-select
										v-else
										enum-id="0604e79d-9f94-47e0-8ff6-02c267d77d7c"
										v-model="props.row.ownerTypeId"
									/>
								</el-form-item>
								<el-form-item label="货主">
									<span v-if="readonly">{{ props.row.ownerName }}</span>
									<mdm-input
										v-else
										v-model='props.row.ownerName'
										size='mini'
										icon="search"
										@change="()=>{clearOwnerIdName(props.row)}"
										:on-icon-click="()=>{chooseOwnerIdName(props.row)}"
									/>
								</el-form-item>
								<mdm-title title="生产"/>
								<el-form-item>
									<el-checkbox
										true-label="1"
										false-label="0"
										v-model="props.row.isKeyComponent"
										:disabled="readonly"
									>
										是否关键件
									</el-checkbox>
								</el-form-item>
								<el-form-item label="工序序列">
									<span v-if="readonly">{{ props.row.optQueue }}</span>
									<el-input
										v-else
										size="mini"
										v-model="props.row.optQueue"
                    :maxlength="8"
									></el-input>
								</el-form-item>
								<el-form-item label="工序">
									<span v-if="readonly">{{ props.row.operId }}</span>
									<el-input
										v-else
										size="mini"
										v-model="props.row.operId"
                    :maxlength="20"
									></el-input>
								</el-form-item>
								<el-form-item label="位置号">
									<span v-if="readonly">{{ props.row.positionNo }}</span>
									<el-input
										v-else
										size="mini"
										v-model="props.row.positionNo"
                    :maxlength="2000"
									></el-input>
								</el-form-item>
								<!--                <el-form-item label="作业编码">-->
								<!--                  <span v-if="readonly">{{ props.row.processIdName }}</span>-->
								<!--                  <mdm-input-->
								<!--                    v-else-->
								<!--                    v-model='props.row.processIdName'-->
								<!--                    size='mini'-->
								<!--                    icon="search"-->
								<!--                    @change="()=>{clearProcessIdName(props.row)}"-->
								<!--                    :on-icon-click="()=>{chooseProcessIdName(props.row)}"/>-->
								<!--                </el-form-item>-->
								<mdm-title title="生效"/>
								<el-form-item label="生效日期">
									<span v-if="readonly">{{ formatDate(props.row.effectDate) }}</span>
									<el-date-picker
										v-else
										size="mini"
										v-model="props.row.effectDate"
										type="date"
										placeholder="选择生效日期"
										value-format="yyyy-MM-dd"
									>
									</el-date-picker>
								</el-form-item>
								<el-form-item label="失效日期">
									<span v-if="readonly">{{ formatDate(props.row.expireDate) }}</span>
									<el-date-picker
										v-else
										size="mini"
										v-model="props.row.expireDate"
										type="date"
										placeholder="选择失效日期"
										value-format="yyyy-MM-dd"
									>
									</el-date-picker>
								</el-form-item>
							</div>
						</div>
					</el-form>
				</template>
			</el-table-column>
			<el-table-column
				type='selection'
				width='50'
			/>
			<el-table-column
				sortable
				type='index'
				label='项次'
				width='50'
			/>
			<el-table-column
				sortable
				prop='flsSerno'
				label='安装图序号'
				width='200'
			>
				<template slot-scope="scope">
					<span v-if="readonly">{{ scope.row.flsSerno }}</span>
					<el-input
						v-else
						:maxlength=20
						size='mini'
						v-model='scope.row.flsSerno'
					/>
				</template>
			</el-table-column>
			<el-table-column
				:render-header="renderHeader"
				prop='materialNumber'
				label='子项物料编码'
				width='200'
			>
				<template slot-scope="scope">
					<span v-if="readonly">{{ scope.row.materialNumber }}</span>
					<mdm-input
						v-else
						:disabled="readonly"
						v-model='scope.row.materialNumber'
						size='mini'
						icon="search"
						@change="()=>{clearMaterial(scope.row)}"
						:on-icon-click="()=>{chooseMaterial(scope.row)}"
					/>
				</template>
			</el-table-column>
			<el-table-column
				prop='materialName'
				label='子项物料名称'
				width='120'
			></el-table-column>
			<el-table-column
				prop='materialSpec'
				label='子项规格型号'
				width='120'
			></el-table-column>
			<el-table-column
				:render-header="renderHeader"
				prop='unitName'
				label='子项单位'
				width='200'
			>
				<template slot-scope="scope">
					<span v-if="readonly">{{ scope.row.unitName }}</span>
					<mdm-input
						v-else
						v-model='scope.row.unitName'
						size='mini'
						icon="search"
						:on-icon-click="()=>{chooseUnitName(scope.row)}"
						@change="()=>{clearUnit(scope.row)}"
						disabled
					/>
				</template>
			</el-table-column>
			<el-table-column
				prop='isStop'
				label='子项是否停产'
				width='120'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='numerator'
				label='用量：分子'
				width='200'
			>
				<template slot-scope="scope">
					<span v-if="readonly">{{ scope.row.numerator }}</span>
					<el-input
						v-else
						:maxlength=20
						size='mini'
						v-model='scope.row.numerator'
					/>
				</template>
			</el-table-column>
			<el-table-column
				sortable
				prop='mainModel'
				label='主型号'
				width='200'
			>
				<template slot-scope="scope">
					<span v-if="readonly">{{ scope.row.mainModel }}</span>
					<el-input
						v-else
						:maxlength=20
						size='mini'
						v-model='scope.row.mainModel'
						disabled
					/>
				</template>
			</el-table-column>
			<el-table-column
				sortable
				prop='chanelForName'
				label='渠道专供'
				width='200'
			>
				<template slot-scope="scope">
					<span v-if="readonly">{{ scope.row.chanelForName }}</span>
					<el-input
						v-else
						:maxlength=20
						size='mini'
						v-model='scope.row.chanelForName'
						disabled
					/>
				</template>
			</el-table-column>
			<el-table-column
				sortable
				prop='denominator'
				label='用量：分母'
				width='200'
			>
				<template slot-scope="scope">
					<span v-if="readonly">{{ scope.row.denominator }}</span>
					<el-input
						v-else
						:maxlength=20
						size='mini'
						v-model='scope.row.denominator'
					/>
				</template>
			</el-table-column>
			<el-table-column
				sortable
				prop='foldNumber'
				label='系列/主型号'
				width='120'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='salesType'
				label='销售类型'
				width='120'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='stopProduceDate'
				label='子项停产日期'
				width='200'
			>
				<template slot-scope="scope">
					{{ formatDateTime(scope.row.stopProduceDate) }}
				</template>
			</el-table-column>
			<el-table-column
				sortable
				prop='isNotOrder'
				label='订单不可选'
				width='200'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='grossWeight'
				label='毛重'
				width='200'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='volume'
				label='子项体积'
				width='200'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='pkgQuantity'
				label='子项包件数'
				width='200'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='isUploadProductImg'
				label='子项是否上传产品图'
				width='200'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='isUploadMaterialImg'
				label='子项是否上传材质图'
				width='200'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='isUploadSetupImg'
				label='子项是否上传安装图'
				width='200'
			>
			</el-table-column>
			<el-table-column
				sortable
				prop='isUploadSizeImg'
				label='子项是否上传尺寸图'
				width='200'
			>
			</el-table-column>
		</el-table>
	</div>
</template>
<script>
import {getUUID} from '../../utils/genUUID'
import MdmSelect from '@components/mdm/components/form/MdmSelect'
import moment from 'moment'

export default {
	props: ['id', 'formType'],
	components: {MdmSelect},
	data() {
		return {
			tableData: [],
			currentRow: null,
			multipleSelection: [],
			readonly: false,

			rules: {
				materialType: [
					{required: true},
				],
				dosageType: [
					{required: true},
				],
			},
			changeStatus: false,
		}
	},
	methods: {
		/*父组件需要用到的三个方法：校验verify，获取提交的form数据getFormData，刷新loadForm*/
		verify() {
			return true
		},
		getFormData(callback) {
			if (typeof callback === 'function') callback(this.tableData)
			return this.tableData
		},
		loadForm(parentId, loadType) {
			this.ajax.get(`/mdm-web/api/material/bom/queryBomItemByBomId/${parentId}`, (res) => {
				if (!res.body.result) {
					this.$notify.error({
						title: '错误提示',
						message: res.body.msg
					});
				} else {
					this.tableData = res.body.content.map(item => {
						//复制清空id
						if (this.formType === 'copy') {
							item.bomId = ''
							item.bomItemId = ''
							item.k3PropertyItemId = ''
						}
						item.uuId = getUUID()

						return item
					})
				}
			})
		},
		formatDate(date) {
			if (!date) return ''
			return moment(date).format('YYYY-MM-DD')
		},
		formatDateTime(date) {
			if (!date) return ''
			return moment(date).format('YYYY-MM-DD HH:mm:ss')
		},
		updateOwnerTypeId(ownerTypeId) {
			switch (ownerTypeId) {
				case "BD_Customer":
					return {type: 'MDM_CUSTOMER'}
				case "BD_OwnerOrg":
					return {type: 'V_SCM_OWNERORG'}
				case "BD_Supplier":
					return {type: 'MDM_SUPPLIER'}
				default:
					return {}
			}
		},
		add() {
			this.tableData.unshift({
				uuId: getUUID()
			})
			this.$nextTick(() => {
				this.$refs.table.bodyWrapper.scrollTop = 0;
			})
		},
		insert() {
			if (this.multipleSelection.length === 1) {
				try {
					this.tableData.forEach((item, index) => {
						if (item.uuId === this.multipleSelection[0].uuId) {
							this.tableData.splice(index, 0, {uuId: getUUID()})
							throw new Error('插入行')
						}
					})
				} catch (e) {
					console.log('插入行')
				}
			} else {
				this.$notify.error({title: '操作提示', message: '请选中要插入行的位置,不可多选'});
			}
		},
		deleteList() {
			this.multipleSelection.forEach(item => {
				this.tableData.forEach((it, index) => {
					if (it.uuId === item.uuId) this.tableData.splice(index, 1)
				})
			})
		},
		handleSelectionChange(val) {
			this.multipleSelection = val;
		},
		renderHeader(h, {column, $index}) {
			return h(
				'div',
				[
					h('span', column.label),
					h('i', {
						style: 'color:red;margin-left:5px;'
					}, '*')
				],
			);
		},

		clearMaterial(row) {
			this.$set(row, 'materialId', '')
			this.$set(row, 'materialNumber', '')
			this.$set(row, 'materialName', '')
			this.$set(row, 'materialSpec', '')
			this.$set(row, 'materialProperty', '')
			this.$set(row, 'unitName', '')
			this.$set(row, 'unitId', '')
			this.$set(row, 'isStop', '')
			this.$set(row, 'minIssueQuantity', '')

			this.$set(row, 'salesType', '')
			this.$set(row, 'foldNumber', '')
			this.$set(row, 'grossWeight', '')
			this.$set(row, 'volume', '')
			this.$set(row, 'pkgQuantity', '')
			this.$set(row, 'isUploadMaterialImg', '')
			this.$set(row, 'isUploadSizeImg', '')
			this.$set(row, 'isUploadProductImg', '')
			this.$set(row, 'isUploadSetupImg', '')
			this.$set(row, 'isNotOrder', '')
			this.$set(row, 'stopProduceDate', '')
			// 主型号
			this.$set(row, 'mainModel', '')
			// 渠道专供
			this.$set(row, 'chanelForName', '')
		},
		clearUnit(row) {
			this.$set(row, 'unitId', '')
			this.$set(row, 'unitName', '')
		},
		clearSupplyOrgIdName(row) {
			this.$set(row, 'supplyOrgId', '')
			this.$set(row, 'supplyOrgIdName', '')
		},
		clearFLsProcessnameName(row) {
			this.$set(row, 'flsProcessname', '')
			this.$set(row, 'fLsProcessnameName', '')
		},
		clearSupplyOrgName(row) {
			this.$set(row, 'supplyOrg', '')
			this.$set(row, 'supplyOrgName', '')
		},
		clearStockIdName(row) {
			this.$set(row, 'stockId', '')
			this.$set(row, 'stockIdName', '')
		},
		clearOwnerIdName(row) {
			this.$set(row, 'ownerId', '')
			this.$set(row, 'ownerIdName', '')
			this.$set(row, 'ownerCode', '')
			this.$set(row, 'ownerName', '')
		},
		clearProcessIdName(row) {
			this.$set(row, 'processId', '')
			this.$set(row, 'processIdName', '')
		},

		chooseMaterial(row) {
			this.$emit('communication', 'info', 'getFormData', (formData) => {
				if (!formData.materialNumber) {
					this.$notify.error({
						title: '错误提示',
						message: '请先选择父项物料编码'
					});
					return
				}
				new Promise((resolve) => {
					setTimeout(resolve, 10)
				}).then(() => {
					let materialProperty = null;
					if (formData.materialProperty === '外购') {
						materialProperty = '1';
					} else {
						materialProperty = '2,3,5,1';
					}
					let params = {
						materialProperty: materialProperty,
						materialNumbers: formData.materialNumber,
						status: 'C',
						sendStatus: "7",
						forbidStatus: 'A'
					}
					params.callback = d => {
						//回调选中数据
						console.log(d)
						this.$set(row, 'materialId', d.material_material_id)
						this.$set(row, 'materialNumber', d.material_number)
						this.$set(row, 'materialName', d.material_name)
						this.$set(row, 'materialSpec', d.material_specification)
						this.$set(row, 'materialProperty', d.base_material_property)
						this.$set(row, 'isNotOrder', d.base_is_not_order)
						this.$set(row, 'isStop', d.base_is_stop)
						this.$set(row, 'minIssueQuantity', d.produce_min_issue_quantity)

						this.$set(row, 'foldNumber', d.material_fold_number)
						this.$set(row, 'salesType', d.sale_sales_type)
						this.$set(row, 'stopProduceDate', d.material_stop_produce_date)
						this.$set(row, 'isNotOrder', d.base_is_not_order)
						this.$set(row, 'grossWeight', d.base_gross_weight)
						this.$set(row, 'volume', d.sale_volume)
						this.$set(row, 'pkgQuantity', d.stock_pkg_quantity)
						this.$set(row, 'isUploadMaterialImg', d.material_is_upload_material_img)
						this.$set(row, 'isUploadSizeImg', d.material_is_upload_size_img)
						this.$set(row, 'isUploadProductImg', d.material_is_upload_product_img)
						this.$set(row, 'isUploadSetupImg', d.material_is_upload_setup_img)
						this.$set(row, 'unitName', d.produce_bom_unit_id)
						this.$set(row, 'unitId', d.produce_bom_unit_id_standby)
						// 主型号
						this.$set(row, 'mainModel', d.material_main_model)
						// 渠道专供
						this.$set(row, 'chanelForName', d.sale_channel_for)
					}
					console.log(formData)
					if (formData.bomGroup == '120842') {
						params.materialNumberLike = formData.materialNumber
					}
					this.$root.eventHandle.$emit('alert', {
						params: params,
						component: () => import('@components/mdm/common/popups/MaterialPopups'),
						style: 'width:98%;height:72%',
						title: '物料列表'
					});
				})
			})
		},
		chooseUnitName(row) {
			new Promise((resolve) => {
				setTimeout(resolve, 10)
			}).then(() => {
				let params = {type: 'T_BD_UNIT', search: {attribute1: 1}}
				params.callback = d => {
					//回调选中数据
					console.log(d)
					this.$set(row, 'unitId', d.k3Id)
					this.$set(row, 'unitName', d.name)
				}
				this.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/popups/BaseDataPopups'),
					style: 'width:800px;height:400px',
					title: '单位列表'
				});
			})
		},
		chooseSupplyOrgIdName(row) {
			new Promise((resolve) => {
				setTimeout(resolve, 10)
			}).then(() => {
				let params = {type: 'V_SCM_OWNERORG'}
				params.callback = d => {
					//回调选中数据
					console.log(d)
					this.$set(row, 'supplyOrgId', d.k3Id)
					this.$set(row, 'supplyOrgIdName', d.name)
				}
				this.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/popups/BaseDataPopups'),
					style: 'width:800px;height:400px',
					title: '供应组织列表'
				});
			})
		},
		chooseFLsProcessnameName(row) {
			new Promise((resolve) => {
				setTimeout(resolve, 10)
			}).then(() => {
				let params = {type: 'T_BAS_ASSISTANTDATAENTRY', search: {subType: 'GXFL'}}
				params.callback = d => {
					//回调选中数据
					console.log(d)
					this.$set(row, 'flsProcessname', d.k3Id)
					this.$set(row, 'fLsProcessnameName', d.name)
				}
				this.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/popups/BaseDataPopups'),
					style: 'width:800px;height:400px',
					title: '供应组织列表'
				});
			})
		},
		chooseSupplyOrgName(row) {
			new Promise((resolve) => {
				setTimeout(resolve, 10)
			}).then(() => {
				let params = {type: 'V_SCM_OWNERORG'}
				params.callback = d => {
					//回调选中数据
					console.log(d)
					this.$set(row, 'supplyOrg', d.k3Id)
					this.$set(row, 'supplyOrgName', d.name)
				}
				this.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/popups/BaseDataPopups'),
					style: 'width:800px;height:400px',
					title: '发料组织列表'
				});
			})
		},
		chooseStockIdName(row) {
			let param = {type: 'T_BD_STOCK', search: {subType: 'GXFL'}};
			//发料组织为林氏木业，发料仓库才有值
			console.log("orgID_++++++" + row.supplyOrg)
			if ('100001' === row.supplyOrg) {
				param = {type: 'T_BD_STOCK'}
			}

			new Promise((resolve) => {
				setTimeout(resolve, 10)
			}).then(() => {
				let params = param
				params.callback = d => {
					//回调选中数据
					console.log(d)
					this.$set(row, 'stockId', d.k3Id)
					this.$set(row, 'stockIdName', d.name)
				}
				this.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/popups/BaseDataPopups'),
					style: 'width:800px;height:400px',
					title: '发料仓库列表'
				});
			})
		},
		chooseOwnerIdName(row) {
			new Promise((resolve) => {
				setTimeout(resolve, 10)
			}).then(() => {
				let params = this.updateOwnerTypeId(row.ownerTypeId)
				console.log(params)
				params.callback = d => {
					//回调选中数据
					console.log(d)
					this.$set(row, 'ownerId', d.id || d.k3Id || d.supplierId || d.customerId)
					this.$set(row, 'ownerIdName', d.name)
					this.$set(row, 'ownerCode', d.number)
					this.$set(row, 'ownerName', d.name)

				}
				this.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/popups/BaseDataPopups'),
					style: 'width:800px;height:400px',
					title: '货主列表'
				});
			})
		},
		chooseProcessIdName(row) {
			// new Promise((resolve) => {
			//   setTimeout(resolve, 10)
			// }).then(() => {
			//   let params = {type: 'T_BD_UNIT', search: {attribute1: 1}}
			//   params.callback = d => {
			//     //回调选中数据
			//     console.log(d)
			//     this.$set(row, 'processId', d.k3Id)
			//     this.$set(row, 'processIdName', d.name)
			//   }
			//   this.$root.eventHandle.$emit('alert', {
			//     params: params,
			//     component: () => import('@components/mdm/common/popups/BaseDataPopups'),
			//     style: 'width:800px;height:400px',
			//     title: '作业编码列表'
			//   });
			// })
		},
	},
	created() {
		this.$nextTick(() => {
			this.$watch('tableData', () => {
				console.log('------------------物料清单子项信息有改动---------------------')
				this.changeStatus = true
			}, {deep: true})
		})
	}
}
</script>
<style scoped>
.sample {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: start;
	height: 400px !important;
}

.sample span {
	color: #4f5f6f;
	font-weight: 600;
}

.sample .el-form-item {
	width: 350px;
}
</style>
<style>
.mdm-table .el-table__expanded-cell {
	padding: 0 !important;
}
</style>
