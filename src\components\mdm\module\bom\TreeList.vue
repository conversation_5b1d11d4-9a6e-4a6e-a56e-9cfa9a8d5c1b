<!--物料平台上下架列表-->
<template>
	<mdm-tree-list
		ref='list'
		:data='list'
		:btns='btnList'
		:colData='cols'
		:formatColData='formatCols'
		:pageTotal='count'
		searchPage='bom'
		searchHolder='请输入查询条件'
		:orderNo="true"
		@search-click='searchClick'
		@selection-change='handleSelectionChange'
		@page-size-change='sizeChange'
		@current-page-change='pageChange'
	>
		<mdm-trees
			:params="{type:'T_ENG_BOMGROUP'}"
			ref="tree"
			slot="tree"
			url="/mdm-web/api/material/group/tree"
			code="id"
			label="name"
			:is-add="false"
			:is-edit="false"
			:is-del="false"
			@node-click="onNodeClick"
		/>
	</mdm-tree-list>
</template>
<script>
import MdmTreeList from '@/components/mdm/components/tree/MdmTreeList'
import MdmTrees from '@/components/mdm/components/tree/MdmTrees'

export default {
	components: {MdmTreeList, MdmTrees},
	props: ["params"],
	data() {
		let _this = this;
		return {
			search: {
				page_name: "bom",
				where: [],
				page_size: _this.pageSize,     //页数
				page_no: 1   //页码
			},
			exportBtnStatus: false,
			list: [],
			count: 0,
			selectedData: [],
			btnList: [
				{
					type: 'success',
					txt: '刷新',
					click: () => {
						_this.searching();
					},
					loading: false
				},
				{
					type: 'danger',
					txt: '删除',
					click: () => {
						_this.del();
					},
					loading: false
				},
				{
					type: 'primary',
					txt: '审核',
					click: () => {
						_this.saveByIds();
					},
					loading: false
				},
				{
					isBtnGroup: true,
					btnGroupList: [
						{
							type: 'primary',
							txt: '新增',
							click: _this.add,
							loading: false
						},
						{
							type: 'primary',
							txt: '复制',
							click: _this.copy,
							loading: false
						},
					]
				},
				{
					isBtnGroup: true,
					btnGroupList: [
						{
							type: 'primary',
							txt: '禁用',
							click: _this.toDisable,
							loading: false
						},
						{
							type: 'primary',
							txt: '启用',
							click: _this.toEnable,
							loading: false
						},
					]
				},
				{
					isBtnGroup: true,
					btnGroupList: [
						{
							type: 'primary',
							txt: '　　导入　　',
						},
						{
							type: 'primary',
							txt: 'Bom导入-创建',
							click: _this.uploadTemplate,
							loading: false
						},
						{
							type: 'primary',
							txt: 'Bom子项修改导入',
							click: _this.uploadUpdate,
							loading: false
						},
					]
				},
				{
					type: 'primary',
					txt: '导入结果查看',
					click: () => {
						_this.checkImport();
					},
					loading: false
				},
				{
					isBtnGroup: true,
					btnGroupList: [
						{
							type: 'primary',
							txt: '　　导出　　',
						},

						{
							type: 'primary',
							txt: '按默认结构导出',
							click: _this.takeOut,
							loading: false
						},
						{
							type: 'primary',
							txt: 'BOM子项导出',
							click: _this.exportProperty,
							loading: false
						},

					]
				},

				{
					type: 'primary',
					txt: '导出结果查看',
					click: () => {
						_this.checkExport();
					},
					loading: false
				},
			],
			cols: [
				{
					"prop": "bom_use_org_id", "label": "父项-使用组织",
					"type": "Component",
					"componentName": "dataBase",
					"params": {
						"type": "V_SCM_OWNERORG"
					},
					width: 200
				},
				{
					"prop": "bom_number", "label": "父项-BOM版本", "type": "String", width: 200,
					redirectClick(row) {
						_this.edit(row)
					},
				},
				{"prop": "bom_bom_name", "label": "父项-BOM简称", "type": "String", width: 200},
				{
					"prop": "bom_bom_category",
					"label": "父项-BOM分类",
					"type": "Enumeration",
					"enumName": "BOM分类",
					width: 200
				},
				{
					"prop": "bom_bom_purpose",
					"label": "父项-BOM用途",
					"type": "Enumeration",
					"enumName": "BOM用途",
					width: 200
				},
				{"prop": "mbm_number", "label": "父项-物料编码", "type": "String", width: 200},
				{"prop": "mbm_name", "label": "父项-物料名称", "type": "String", width: 200},
				{"prop": "mbm_specification", "label": "父项-规格型号", "type": "String", width: 200},
				{"prop": "mbm_is_upload_product_img", "label": "父项-是否上传产品图", "type": "Switch", width: 200},
				{"prop": "mbm_is_upload_setup_img", "label": "父项-是否上传安装图", "type": "Switch", width: 200},
				{"prop": "mbm_is_upload_size_img", "label": "父项-是否上传尺寸图", "type": "Switch", width: 200},
				{"prop": "mbm_is_upload_material_img", "label": "父项-是否上传材质图", "type": "Switch", width: 200},
				{
					"prop": "bom_document_status",
					"label": "父项-数据状态",
					"type": "Enumeration",
					"enumName": "数据状态",
					width: 200
				},
				{
					"prop": "bom_forbid_status",
					"label": "父项-禁用状态",
					"type": "Enumeration",
					"enumName": "禁用状态",
					width: 200,
					html:(val)=>{
						return '<span style="background-color:#cccccc;color:'+(val==='是'?'red':'green')+';font-weight:bold;padding:2px 5px">'+(val||'')+'</span>'
					}
				},
				{"prop": "item_F_LS_SERNO", "label": "子项-安装图序号", "type": "String", width: 200},
				{"prop": "item_replace_group", "label": "子项-项次", "type": "String", width: 200},
				{"prop": "mim_number", "label": "子项-物料编码", "type": "String", width: 200},
				{"prop": "mim_name", "label": "子项-物料名称", "type": "String", width: 200},
				{"prop": "mim_specification", "label": "子项-规格型号", "type": "String", width: 200},
				{
					"prop": "item_unit_id", "label": "子项-单位",
					"type": "Component",
					"componentName": "dataBase",
					"params": {
						"type": "T_BD_UNIT",
						"search": {"attribute1": 1}
					}
					, width: 200
				},
				{
					"prop": "item_supply_org_id", "label": "子项-供应组织",
					"type": "Component",
					"componentName": "dataBase",
					"params": {
						"type": "V_SCM_OWNERORG"
					}
					, width: 200
				},
				{
					"prop": "item_numerator", "label": "用量：分子", change: (row) => {
						console.log(row)
					}, "type": "String", width: 200
				},
				{"prop": "item_denominator", "label": "用量：分母", "type": "String", width: 200},
				{"prop": "mim_is_upload_product_img", "label": "子项-是否上传产品图", "type": "Switch", width: 200},
				{"prop": "mim_is_upload_setup_img", "label": "子项-是否上传安装图", "type": "Switch", width: 200},
				{"prop": "mim_is_upload_size_img", "label": "子项-是否上传尺寸图", "type": "Switch", width: 200},
				{"prop": "mim_is_upload_material_img", "label": "子项-是否上传材质图", "type": "Switch", width: 200},
				{"prop": "mim_is_auto_split", "label": "子项-是否组合自动拆分", "type": "Enumeration", width: 200},
				{"prop": "mim_base_status", "label": "子项-生产状态", "type": "Enumeration", width: 200},
				{"prop": "mbm_base_status", "label": "父项-生产状态", "type": "Enumeration", width: 200},

			],
			//非初始化格式列
			formatCols:[
				{
				"prop": "mim_base_is_not_order",
				"label": "子项-订单不可选",
				"type": "String",
				"width": 150,
				formatter:(item)=>{
						return {
							'0':'否',
							'1':'是'
						}[item]||item
					}
				}
			]
		}
	},
	methods: {
		//树组件操作
		addNode(obj) {
			console.log(obj)
			this.$root.eventHandle.$emit('alert', {
				params: obj,
				component: () => import('@components/mdm/module/type/AddOrUpdate'),
				style: 'width:800px;height:500px',
				title: '新增节点'
			})
		},
		editNode(obj) {
			console.log(obj)
		},
		delNode(obj) {
			console.log(obj)
		},
		onNodeClick(node) {
			if (node.id !== '000') {
				this.addField("bom.bom_group", node.id);
			} else {
				this.addField("bom.bom_group", null);
			}

			this.searching()
		},
		addField(name, value) {
			this.search.where.forEach((item, index) => {
				if (item.field === name) {
					this.search.where.splice(index, 1)
				}
			})
			if (value) {
				this.search.where.push({
					condition: "AND",
					field: name,
					operator: "=",
					value: value,
				})
			}
		},

		//列表查询
		searching(resolve) {
			let data = this.search;
			let colsConfig = this.$refs.list.colsConfig
			if (colsConfig) data.colsConfig = JSON.parse(JSON.stringify(colsConfig))

			let is_deleted = this.search.where.find(item => item.field === 'bom.is_deleted')
			if (!is_deleted) {
				this.search.where.splice(0, 0, {
					condition: "AND",
					field: "bom.is_deleted",
					operator: "IS NULL",
				})
			}

			data.colsConfig.push({prop: 'bom_forbid_status'})
			data.colsConfig.push({prop: 'bom_document_status'})
			data.colsConfig.push({prop: 'item_replace_group'})


			// let url = '/mdm-web/api/material/material/list?permissionCode=MDM_MATERIAL_LIST';
			let url = '/mdm-web/api/query/list';
			let filterF = ['bom_forbid_status', 'bom_bom_id', 'material_shelf_status', 'base_is_stop']
			this.ajax.postStream(url, data, (res) => {
				if (res.body.result && res.body.content) {
					let list = res.body.content.list || []
					let onlyList = []
					this.list = list.map(item => {
						if (onlyList.indexOf(item.bom_bom_id) === -1) {
							onlyList.push(item.bom_bom_id)
						} else {
							for (let key in item) {
								if (filterF.indexOf(key) === -1) {
									if ((key.indexOf('bom_') > -1) || (key.indexOf('mbm_') > -1)) item[key] = ''
								}
							}
						}
						return item
					})
					this.count = res.body.content.count;
				}
				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				});
				resolve && resolve();
			});
		},
		handleSelectionChange(selects) {
			console.log('选中了', selects)
			this.selectedData = selects
		},
		searchClick(val, resolve) {
			this.search.where = val;
			this.searching(resolve)
		},
		sizeChange(ps) {
			this.search.page_size = ps
			this.searching()
		},
		pageChange(page) {
			this.search.page_no = page
			this.searching()
		},
		toDisable() {
			let bom = {forbidStatus: 'B'}
			/*if(this.containStatus("bom_forbid_status","是","不能选择已经被禁用的物料清单")){
			  return;
			}*/
			this.saveByStatus(bom, '正在禁用选中的物料清单')
		},
		toEnable() {
			let bom = {forbidStatus: 'A'}
			/*if(this.containStatus("bom_forbid_status","否","不能选择已经被启用的物料清单")){
			  return;
			}*/
			this.saveByStatus1(bom, '正在启用选中的物料清单')
		},

		saveByStatus(infoForm, type) {
			if (this.selectedData.length <= 0) {
				this.$message({
					type: 'warning',
					message: '请选择要操作的物料清单'
				});
				return false;
			}
			this.$confirm(`${type}, 是否继续?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let list = this.selectedData.map(item => {
					return Object.assign({bomId: item.bom_bom_id}, infoForm)
				})
				this.ajax.postStream('/mdm-web/api/material/bom/updateList?permissionCode=MDM_BOM_DISABLED', list, (res) => {
					if (res.body.result) {
						this.selectedData = []
						this.searching()
					}
					this.$notify({
						title: '操作提示',
						message: res.body.msg,
						type: res.body.result ? 'success' : 'error',
					});
				});
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消'
				});
			});
		},

		saveByStatus1(infoForm, type) {
			if (this.selectedData.length <= 0) {
				this.$message({
					type: 'warning',
					message: '请选择要操作的物料清单'
				});
				return false;
			}
			this.$confirm(`${type}, 是否继续?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let list = this.selectedData.map(item => {
					return Object.assign({bomId: item.bom_bom_id}, infoForm)
				})
				this.ajax.postStream('/mdm-web/api/material/bom/updateList?permissionCode=MDM_BOM_ENABLE', list, (res) => {
					if (res.body.result) {
						this.selectedData = []
						this.searching()
					}
					this.$notify({
						title: '操作提示',
						message: res.body.msg,
						type: res.body.result ? 'success' : 'error',
					});
				});
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消'
				});
			});
		},
		//按钮操作
		add() {
			this.$root.eventHandle.$emit('creatTab', {
				name: '新增物料清单信息',
				params: {id: null},
				component: () => import('@components/mdm/module/bom/AddOrUpdate.vue')
			});
		},
		edit(row) {
			this.$root.eventHandle.$emit('creatTab', {
				name: '编辑物料清单信息',
				loading: false,
				params: {id: row['bom_bom_id'], readonly: true},
				component: () => import('@components/mdm/module/bom/AddOrUpdate.vue')
			});
		},
		del() {
			if (this.selectedData.length > 0) {
				this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then((res) => {
					let ids = this.selectedData.map(item => {
						return item.bom_bom_id
					})
					this.ajax.postStream('/mdm-web/api/material/bom/delByIds?permissionCode=MDM_BOM_DELETE', ids, (res) => {
						this.searching()
						this.$notify({
							title: '操作提示',
							message: res.body.msg,
							type: res.body.result ? 'success' : 'error',
						});
					})
				}).catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});
			} else {
				this.$message({
					type: 'warning',
					message: '请选择要删除的数据'
				});
			}
		},
		saveByIds() {
			if (this.selectedData.length > 0) {
				this.$confirm('此操作将保存数据为已审核状态, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then((res) => {
					let ids = this.selectedData.map(item => {
						return item.bom_bom_id
					})
					this.ajax.postStream('/mdm-web/api/material/bom/saveByIds?permissionCode=MDM_BOM_AUDIT', ids, (res) => {
						this.$notify({
							title: '操作提示',
							message: res.body.msg,
							type: res.body.result ? 'success' : 'error',
						});
						if (res.body.result) {
							this.searching()
						}
					})
				}).catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});
			} else {
				this.$message({
					type: 'warning',
					message: '请选择要审核的数据'
				});
			}
		},
		copy() {
			if (this.selectedData.length > 0) {
				if (this.selectedData.length > 1) {
					this.$message({
						type: 'warning',
						message: '只能选择一条要复制的数据'
					});
					return
				}
				this.$root.eventHandle.$emit('creatTab', {
					params: {id: this.selectedData[0]['bom_bom_id'], formType: 'copy'},
					name: '新增物料清单信息',
					component: () => import('@components/mdm/module/bom/AddOrUpdate.vue')
				});
			} else {
				this.$message({
					type: 'warning',
					message: '请选择要复制的数据'
				});
			}
		},
		submit() {
			if (this.selectedData.length > 0) {
				this.$confirm('确定要提交选择的数据吗?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					let loading = this.$loading({text: '正在提交数据，请喝杯咖啡稍等片刻...'})
					setTimeout(() => {
						loading.close()
						this.$message({
							type: 'success',
							message: '提交成功!'
						});
					}, 2000)
				}).catch(() => {
					this.$message({
						type: 'info',
						message: '已取消提交'
					});
				});
			} else {
				this.$message({
					type: 'warning',
					message: '请选择要提交的数据'
				});
			}
		},
		cancel() {
			if (this.selectedData.length > 0) {
				this.$confirm('确定要撤销选中的数据吗?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$message({
						type: 'success',
						message: '撤销成功!'
					});
				}).catch(() => {
					this.$message({
						type: 'info',
						message: '已取消撤销'
					});
				});
			} else {
				this.$message({
					type: 'warning',
					message: '请选择要撤销的数据'
				});
			}
		},
		genImage() {
		},

		takeOut() {
			this.ajax.postStream('/mdm-web/api/material/bom/exportBomList?permissionCode=MDM_BOM_EXPORT', this.search, (res) => {

				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				});
			});
		},

		exportProperty() {
			this.ajax.postStream('/mdm-web/api/material/bom/exportBomItem?permissionCode=MDM_BOM_ITEM_EXPORT', this.search, (res) => {

				this.$message({
					type: res.body.result ? 'success' : 'error',
					message: res.body.msg
				});
			});
		},
		uploadTemplate() {
			let params = {};
			params.getUrl = '/mdm-web/api/material/bom/importBom?permissionCode=MDM_BOM_IMPORT';
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/mdm/components/excel/importExcel'),
				params: params,
				style: 'width:400px;height:300px',
				title: 'bom导入-创建'
			});
		},
		uploadUpdate() {
			let params = {};
			params.getUrl = '/mdm-web/api/material/bom/updateBomItem?permissionCode=MDM_BOM_ITEM_IMPORT';
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@components/mdm/components/excel/importExcel'),
				params: params,
				style: 'width:400px;height:300px',
				title: 'BOM子项修改导入'
			});
		},

		checkImport() {
			let params = {businessType: 'IMPORT_BOM'};
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@/components/mdm/module/material/jobList'),
				params: params,
				style: 'width:1000px;height:500px',
				title: '导入结果列表'
			});
		},

		checkExport() {
			let params = {businessType: 'EXPORT_BOM'};
			this.$root.eventHandle.$emit('alert', {
				component: () => import('@/components/mdm/module/material/jobList'),
				params: params,
				style: 'width:1000px;height:500px',
				title: '导出结果列表'
			});
		},
		importM() {
		},
		exportM() {
		},
	}
}
</script>
<style scoped>
.mdm-danger {
	background-color: red;
	color: white;
}
</style>
