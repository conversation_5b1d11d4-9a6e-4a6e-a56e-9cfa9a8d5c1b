<template>
	<div class='xpt-flex'>
		<div class='xpt-top'>
		
			<!-- <mdm-btn-list ref="btns" :btn-list="form.btns"/> -->
			<el-button @click="save" size="mini" type="primary">保存</el-button>
		</div>
		<div>
			<el-form :model='form.data' :rules="rules" label-width='120px' ref='form'>
				<div class="sample">
					
					<el-form-item label='类别编码' prop="level">
						<el-select  size='mini'  placeholder="类型" v-model='form.data.level' disabled>
							<el-option label="一级类目" value="L"></el-option>
							<el-option label="二级类目" value="M"></el-option>
							<el-option label="三级类目" value="S"></el-option>
						</el-select>
						
					</el-form-item>
					<el-form-item label='类别编码' prop="code">
						<el-input size="mini" v-model='form.data.code'
								  :maxlength=25></el-input>
						<el-tooltip :content="rules.code[0].message" class="item" effect="dark"
									placement="right-start"
									popper-class='xpt-form__error' v-if='rules.code[0].isShow'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label='类别名称' prop="name">
						<el-input  size="mini" v-model='form.data.name'
								  :maxlength=30></el-input>
						<el-tooltip :content="rules.name[0].message" class="item" effect="dark" placement="right-start"
									popper-class='xpt-form__error' v-if='rules.name[0].isShow'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
				
				</div>
			
			</el-form>
		</div>
	</div>
</template>

<script>

export default {
	
	props: ['params'],
	data() {
		let _this = this;
		return {
			readonly: _this.params.readonly,
			loading: false,
			category: {},
			checkBoxData: [],
			form: {
				btns: [
					{
						label: '保存', onClick: () => {
							_this.save()
						}, code: 'save', disabled: true, loading: false
					}
				],
				data: {name: "",code:"",level:"",id:"",parentId:"0"},
			},
			
			
			rules: {
				
				code: [{
					required: true,
					message: '类别编码不能为空',
					isShow: false,
					validator: (rule, value, callback) => {
						// 数据校验
						if (value) {
							this.rules[rule.field][0].isShow = false;
							callback();
							return;
						}
						this.rules[rule.field][0].isShow = true;
						callback(new Error(rule.message));
					}
				}],
				name: [{
					required: true,
					message: '类别名称不能为空',
					isShow: false,
					validator: (rule, value, callback) => {
						// 数据校验
						if (value) {
							this.rules[rule.field][0].isShow = false;
							callback();
							return;
						}
						this.rules[rule.field][0].isShow = true;
						callback(new Error(rule.message));
					}
				}],
			},
			
		}
	},
	
	methods: {
		
		
		save() {
			
			this.$refs.form.validate((vaild) => {
				if (vaild) {
					this.ajax.postStream('/mdm-web/api/material/sale/category/save', this.form.data, res => {
						if(res.body.result){
							this.$message.success(res.body.msg)
							this.$root.eventHandle.$emit('removeAlert',this.params.alertId);
							this.params.callback();
						}else {
							this.$message.error(res.body.msg)
						}
					}, err => {
						this.$message.error(err)
					})
				}
			});
		},
	
	
	},
	mounted() {
		// this.refresh(true);
		let self = this;
		self.form.data.level = self.params.type;
		if(self.params.ifAdd && self.params.type != "L"){
			self.form.data.parentId = self.params.row.id;
		}
		if(!self.params.ifAdd){
			self.form.data.code = self.params.row.code;
			self.form.data.name = self.params.row.name;
			self.form.data.id = self.params.row.id;
			self.form.data.parentId = self.params.row.parentId;
		}
		// self.params.__close = self.closeTab

	}
}
</script>

