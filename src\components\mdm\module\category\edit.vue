<template>
	<div class='xpt-flex'>
		<div class='xpt-top'>
			<!--<el-button :disabled="loading" :loading="loading" :type='btn.type' @click="(() => {btn.click && btn.click();})" size='mini' slot='left' v-for="btn in form.btns"
					   v-text="btn.txt"></el-button>-->
			<mdm-btn-list ref="btns" :btn-list="form.btns"/>
		</div>
		<div>
			<el-form :model='form.data' :rules="rules" label-width='120px' ref='form'>
				<div class="sample">
					
					<el-form-item label='类别编码' prop="number">
						<el-input :disabled="globallyDisabled('number')" size="mini" v-model='form.data.number'
								  :maxlength=25></el-input>
						<el-tooltip :content="rules.number[0].message" class="item" effect="dark"
									placement="right-start"
									popper-class='xpt-form__error' v-if='rules.number[0].isShow'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label='类别名称' prop="name">
						<el-input :disabled="globallyDisabled('name')" size="mini" v-model='form.data.name'
								  :maxlength=30></el-input>
						<el-tooltip :content="rules.name[0].message" class="item" effect="dark" placement="right-start"
									popper-class='xpt-form__error' v-if='rules.name[0].isShow'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label='商品组别' prop="groupNameName">
						<mdm-input :disabled="globallyDisabled('groupNameName')" :on-icon-click="selectGroupName"
								   icon="search" size='mini' v-model='form.data.groupNameName'/>
						<el-tooltip :content="rules.groupNameName[0].message" class="item" effect="dark"
									placement="right-start"
									popper-class='xpt-form__error' v-if='rules.groupNameName[0].isShow'>
							<i class='el-icon-warning'></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label='备注'>
						<el-input :disabled="globallyDisabled('remark')" size="mini" v-model='form.data.remark'
								  :maxlength=200></el-input>
					</el-form-item>
					<el-form-item label='描述'>
						<el-input :disabled="globallyDisabled('description')" size="mini"
								  v-model='form.data.description' :maxlength=200></el-input>
					</el-form-item>
					<el-form-item label='数据状态'>
						<mdm-select :highlight="true" enum-id="fab41ac7-b685-4b5a-bd63-312ee8b98d69" default-val="Z"
									v-model="form.data.documentStatus"
									:disabled="globallyDisabled('documentStatus')"></mdm-select>
					</el-form-item>
					<el-form-item label='禁用状态'>
						<mdm-select :highlight="true" enum-id="728d78e3-b3c5-4caa-b9bf-306645172e7c" default-val="A"
									v-model="form.data.forbidStatus"
									:disabled="globallyDisabled('forbidStatus')"></mdm-select>
					</el-form-item>
					<el-form-item label='存货大类' prop="stockBigCategoryName">
						<mdm-input :disabled="globallyDisabled('stockBigCategoryName')"
								   :on-icon-click="selectStockCategory" icon="search" size='mini'
								   v-model='form.data.stockBigCategoryName'/>
					</el-form-item>
					<el-form-item label='创建人'>
						<el-input :disabled="true" size="mini" v-model='form.data.creatorName'></el-input>
					</el-form-item>
					<el-form-item label='创建日期'>
						<el-date-picker :disabled="true" size="mini" type="datetime" v-model='form.data.createdDate'
										value-format="timestamp"></el-date-picker>
					</el-form-item>
					<el-form-item label='修改人'>
						<el-input :disabled="true" size="mini" v-model='form.data.modifierName'></el-input>
					</el-form-item>
					<el-form-item label='修改日期'>
						<el-date-picker :disabled="true" size="mini" type="datetime" v-model='form.data.modifyDate'
										value-format="timestamp"></el-date-picker>
					</el-form-item>
					<el-form-item label='审核人'>
						<el-input :disabled="true" size="mini" v-model='form.data.auditorName'></el-input>
					</el-form-item>
					<el-form-item label='审核日期'>
						<el-date-picker :disabled="true" size="mini" type="datetime" v-model='form.data.auditDate'
										value-format="timestamp"></el-date-picker>
					</el-form-item>
					<el-form-item label='禁用人'>
						<el-input :disabled="true" size="mini" v-model='form.data.forbiderName'></el-input>
					</el-form-item>
					<el-form-item label='禁用日期'>
						<el-date-picker :disabled="true" size="mini" type="datetime" v-model='form.data.forbidDate'
										value-format="timestamp"></el-date-picker>
					</el-form-item>
				
				</div>
			
			</el-form>
		</div>
		<div class='xpt-flex__bottom' v-fold>
			<div class="xpt-flex">
				<el-row class="xpt-top" :gutter="40">
					<el-button size="mini" type="success" @click="addItem" :disabled="readonly">增加行</el-button>
					<el-button size="mini" type="primary" @click="delItem" :disabled="readonly">删除行</el-button>
					<el-button size="mini" type="primary" @click="insertItem" :disabled="readonly">插入行</el-button>
				</el-row>
				<el-table
					highlight-current-row
					ref="table"
					:data="tableData"
					border
					@selection-change="changeFun"
					style="width: 100%">
					<el-table-column type="selection">
					</el-table-column>
					<el-table-column
						type="index"
						width="50">
					</el-table-column>
					<el-table-column
						prop="skuPropertyNumber"
						label="属性编码"
						width="200">
						<template slot-scope="scope">
							<mdm-input :disabled="globallyDisabled('skuPropertyNumber')"
									   v-model='scope.row.skuPropertyNumber' @change="()=>{
                    scope.row.skuPropertyNumber = ''
                    scope.row.skuPropertyName = ''
                   }" size='mini' icon="search" :on-icon-click="()=>{listChoose(scope.row)}"/>
						</template>
					</el-table-column>
					<el-table-column
						prop="skuPropertyName"
						label="基础资料属性"
						width="150">
					</el-table-column>
					<el-table-column
						:render-header="renderHeader"
						prop="isShow"
						width="100"
						label="是否显示">
						<template slot-scope="scope">
							<el-checkbox :disabled="globallyDisabled('isShow')" true-label='1' false-label='0'
										 v-model='scope.row.isShow'></el-checkbox>
						</template>
					</el-table-column>
					<el-table-column
						:render-header="renderIsInput"
						prop="isInput"
						width="100"
						label="是否必填">
						<template slot-scope="scope">
							<el-checkbox :disabled="globallyDisabled('isInput')" true-label='1' false-label='0'
										 v-model='scope.row.isInput'></el-checkbox>
						</template>
					</el-table-column>
					<el-table-column
						prop="priority"
						width="150"
						label="优先级">
						<template slot-scope="scope">
							<el-input :disabled="globallyDisabled('priority')" size="mini" v-model='scope.row.priority'
									  :maxlength=5></el-input>
						</template>
					</el-table-column>
					<el-table-column
						prop="remark"
						width="150"
						label="备注">
						<template slot-scope="scope">
							<el-input :disabled="globallyDisabled('remark')" size="mini" v-model='scope.row.remark'
									  :maxlength=45></el-input>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</div>
	</div>
</template>

<script>
import MdmSelect from '@components/mdm/components/form/MdmSelect'
import MdmBtnList from "@components/mdm/components/form/MdmBtnList";

export default {
	components: {
		MdmBtnList,
		MdmSelect
	},
	props: ['params'],
	data() {
		let _this = this;
		return {
			readonly: _this.params.readonly,
			loading: false,
			category: {},
			checkBoxData: [],
			form: {
				btns: [
					{
						label: '审核', onClick: () => {
							_this.save('C')
						}, code: 'save', disabled: true, loading: false
					},
					{
						label: '暂存', onClick: () => {
							_this.save('Z')
						}, code: 'draft', disabled: true, loading: false
					},
					{
						label: '打开编辑', code: 'openEdit', onClick: () => {
							_this.readonly = !_this.readonly
							_this.$refs.btns.updateSettings(0, {disabled: _this.readonly})
							if (this.form.data.documentStatus !== 'C') {
								_this.$refs.btns.updateSettings(1, {disabled: _this.readonly})
							}
							_this.$refs.btns.updateSettings(2, {label: _this.readonly ? '打开编辑' : '关闭编辑'})
						}
					},
					
					{
						label: '业务操作',
						code: 'toDisable',
						list: [
							{
								label: '禁用',
								onClick: () => {
									_this.forbidden('B', '禁用')
								},
								loading: false
							},
							{
								label: '反禁用',
								onClick: () => {
									_this.forbidden('A', '反禁用')
								},
								loading: false
							},
						]
					},
				],
				data: {items: []},
				items: {
					newRow: {},
					
				}
			},
			tableData: [
				{
					categoryId: '',
					skuPropertyId: '',
					skuPropertyNumber: '',
					skuPropertyName: '',
					isShow: '',
					isInput: '',
					priority: '',
					remark: '',
				}
			],
			validation: {},
			disabledList: [
				'categoryId',
				'documentStatus',
				'forbidStatus',
			],
			rules: {
				groupNameName: [{
					required: true,
					message: '商品组别不能为空',
					isShow: false,
					validator: (rule, value, callback) => {
						// 数据校验
						if (value) {
							this.rules[rule.field][0].isShow = false;
							callback();
							return;
						}
						this.rules[rule.field][0].isShow = true;
						callback(new Error(rule.message));
					}
				}],
				number: [{
					required: true,
					message: '类别编码不能为空',
					isShow: false,
					validator: (rule, value, callback) => {
						// 数据校验
						if (value) {
							this.rules[rule.field][0].isShow = false;
							callback();
							return;
						}
						this.rules[rule.field][0].isShow = true;
						callback(new Error(rule.message));
					}
				}],
				name: [{
					required: true,
					message: '类别名称不能为空',
					isShow: false,
					validator: (rule, value, callback) => {
						// 数据校验
						if (value) {
							this.rules[rule.field][0].isShow = false;
							callback();
							return;
						}
						this.rules[rule.field][0].isShow = true;
						callback(new Error(rule.message));
					}
				}],
			},
			
			changeStatus: false,
			unForm: null,
			unTableData: null,
			isCloseTab: false,
			isCloseTabShow: false,
		}
	},
	watch: {
		isCloseTab(val) {
			if (val) {
				this.$root.eventHandle.$emit('removeTab', this.params.tabName)
			}
		}
	},
	methods: {
		// 关闭标签页
		closeTab() {
			let self = this
			if (self.changeStatus) {
				self.$root.eventHandle.$emit('openDialog', {
					ok() {
						self.isCloseTabShow = true
						self.save('C')
					},
					no() {
						self.$root.eventHandle.$emit('removeTab', self.params.tabName)
					}
				})
			} else {
				self.$root.eventHandle.$emit('removeTab', self.params.tabName)
			}
		},
		initWatch() {
			
			this.$nextTick(() => {
				this.changeStatus = false
				if (this.unForm) this.unForm()
				if (this.unTableData) this.unTableData()
				
				this.unForm = this.$watch('form.data', () => {
					console.log('------------------商品类别信息有改动---------------------')
					this.changeStatus = true
				}, {deep: true})
				this.unTableData = this.$watch('tableData', () => {
					console.log('------------------商品类别行信息有改动---------------------')
					this.changeStatus = true
				}, {deep: true})
			})
		},
		
		changeFun(val) {
			this.checkBoxData = val;
		},
		initData(category) {
			this.category = category || {};
			this.$set(this.form, 'data', {...this.category});
			this.tableData = this.category.items || [];
			
			this.initWatch()
		},
		
		renderIsInput(h, {column, $index}) {
			return h('el-checkbox', {props: {label: column.label}, on: {change: this.handleCheckAllChangeIsInput}})
		},
		handleCheckAllChangeIsInput(event) {
			let checked = event.target.checked
			this.tableData.map(item => {
				item.isInput = checked ? '1' : '0'
			})
		},
		
		renderHeader(h, {column, $index}) {
			return h('el-checkbox', {props: {label: column.label}, on: {change: this.handleCheckAllChangeIsEnable}})
		},
		handleCheckAllChangeIsEnable(event) {
			let checked = event.target.checked
			this.tableData.map(item => {
				item.isShow = checked ? '1' : '0'
			})
		},
		listChoose(row) {
			new Promise((resolve) => {
				setTimeout(resolve, 10)
				
			}).then(() => {
				let params = {},
					self = this;
				params.callback = d => {
					//回调选中数据
					console.log(d)
					row.skuPropertyId = d.propertyId;
					row.skuPropertyNumber = d.number;
					row.skuPropertyName = d.name;
					debugger
				}
				self.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/selectSkuProperty'),
					style: 'width:800px;height:500px',
					title: '列表'
				});
				
			})
		},
		
		refresh: function (force) {
			// 新增无id不进行查询
			let id = this.category.categoryId;
			if (!id) {
				this.loading = true
				this.form.btns[0].disabled = false
				this.form.btns[1].disabled = false
				this.form.btns.splice(2, 1)
				this.form.btns.splice(2, 1)
				return;
			}
			this.loading = true;
			let url = `/mdm-web/api/material/category/${id}`;
			
			let showTip = false;
			this.ajax.get(url, (res) => {
				this.loading = false;
				let body = res.body;
				if (showTip) {
					this.$message({
						type: body.result ? 'success' : 'error',
						message: body.msg
					});
				}
				if (!body.result || !body.content) return;
				this.initData(body.content);
				
				
			}, (err) => {
				this.loading = false;
			});
		},
		updateSettings(index, obj) {
			this.form.btns[index] = Object.assign(this.from.btns[index], obj)
		},
		vaildItem(items) {
			let msg = ''
			
			let isExist = true
			
			if (items) {
				items.forEach((item, index) => {
					if (!item.skuPropertyNumber) {
						msg += '第' + (index + 1) + '行[属性编码不能为空],'
					}
					if (!item.priority) {
						msg += '第' + (index + 1) + '行[优先级不能为空],'
					}
					
					items.forEach((cat, idx) => {
						if (cat.skuPropertyNumber && (idx !== index) && (idx > index)) {
							if (item.skuPropertyNumber === cat.skuPropertyNumber) {
								msg += '第' + (index + 1) + '行[属性编码]与第' + (idx + 1) + '行[属性编码]重复,'
							}
						}
					})
					
				})
			}
			if (msg) {
				const h = this.$createElement;
				const hs = msg.split(',').map(item => {
					return h('p', {style: 'color: red'}, item)
				})
				this.$notify.error({
					title: '错误提示',
					message: h('div', null, [...hs])
				});
				isExist = false;
			}
			return isExist;
		},
		save(status) {
			let infoForm = this.form.data
			infoForm.documentStatus = status;
			let items = this.tableData
			infoForm.items = items || []
			this.$refs.form.validate((vaild) => {
				if (vaild && this.vaildItem(items)) {
					this.form.btns[0].disabled = true
					this.form.btns[1].disabled = true
					let permissionCode = (status === 'C' ? 'MDM_CATEGORY_AUDIT' : 'MDM_CATEGORY_SAVE')
					let url = '/mdm-web/api/material/category/save?permissionCode=' + permissionCode;
					this.ajax.postStream(url, infoForm, (res) => {
						this.form.btns[0].disabled = false
						this.form.btns[1].disabled = false
						if (res.body.result === true) {
							if (this.isCloseTabShow) this.isCloseTab = true
							this.category.categoryId = res.body.content.categoryId;
							
							if ("C" === res.body.content.documentStatus) {
								this.$refs.btns.disabledBtn(true, 'draft')
							}
							this.refresh();
						}
						this.$message({
							type: res.body.result ? 'success' : 'error',
							message: res.body.msg
						});
					});
				}
			});
		},
		add() {
			this.form.data.items.push({...this.form.data.items.newRow});
		},
		insert() {
			let selectRows = this.$refs.itemList.getSelectRows();
			if (!selectRows || !(selectRows.length === 1)) {
				this.$message({
					type: 'error',
					message: '请选择一行作为插入的位置'
				});
				return
			}
			
			let list = this.form.data.items;
			let idx = list.indexOf(selectRows[0]);
			list.splice(idx, 0, {...this.form.items.newRow});
		},
		forbidden(type, typeName) {
			/*if ('B'===type&&this.containStatus("forbidStatus", "B", "该类别已被禁用,不能重复操作")
			  || 'A'===type&&this.containStatus("forbidStatus", "A", "该类别已启用,不能重复操作")) {
			  return;
			}*/
			let id = this.category.categoryId;
			this.$confirm('此操作将' + typeName + '该商品类别, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let ids = [id];
				let data = {forbidStatusName: typeName, forbidStatus: type, ids: ids};
				let url = `/mdm-web/api/material/category/forbidden?permissionCode=MDM_CATEGORY_FORBIDDEN`;
				this.ajax.postStream(url, data, (res) => {
					
					this.$message({
						type: res.body.result ? 'success' : 'error',
						message: res.body.msg
					});
					this.refresh();
				});
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消禁用'
				});
			});
		},
		containStatus(statusProperty, statusVal, confirm) {
			if (this.form.data[statusProperty] == statusVal) {
				this.$message({
					type: 'warning',
					message: confirm || '选择了不合法的数据'
				});
				return true;
			}
			return false;
		},
		addItem() {
			this.tableData.push({
				categoryId: '',
				skuPropertyId: '',
				skuPropertyNumber: '',
				skuPropertyName: '',
				isShow: '',
				isInput: '',
				priority: '',
				remark: '',
			})
			this.$nextTick(() => {
				this.$refs.table.bodyWrapper.scrollTop = this.$refs.table.bodyWrapper.scrollHeight;
			})
		},
		delItem() {
			let selectRows = this.checkBoxData;
			if (!selectRows || !(selectRows.length > 0)) {
				this.$message({
					type: 'error',
					message: '请选择需要删除的行'
				});
				return
			}
			
			
			let list = this.tableData;
			list
				.map((value, idx) => {
					if (selectRows.includes(value)) {
						return idx;
					}
					return -1
				})
				.filter(idx => idx >= 0)
				.sort((a, b) => b - a)
				.forEach(idx => {
					list.splice(idx, 1);
				});
		},
		insertItem() {
			let selectRows = this.checkBoxData;
			if (!selectRows || !(selectRows.length === 1)) {
				this.$message({
					type: 'error',
					message: '请选择一行作为插入的位置'
				});
				return
			}
			
			let list = this.tableData;
			let idx = list.indexOf(selectRows[0]);
			list.splice(idx, 0, {
				categoryId: '',
				skuPropertyId: '',
				skuPropertyNumber: '',
				skuPropertyName: '',
				isShow: '',
				isInput: '',
				priority: '',
				remark: '',
			});
		},
		delete() {
			let selectRows = this.$refs.itemList.getSelectRows();
			if (!selectRows || !(selectRows.length > 0)) {
				this.$message({
					type: 'error',
					message: '请选择需要删除的行'
				});
				return
			}
			
			
			let list = this.form.data.items;
			list
				.map((value, idx) => {
					if (selectRows.includes(value)) {
						return idx;
					}
					return -1
				})
				.filter(idx => idx >= 0)
				.sort((a, b) => b - a)
				.forEach(idx => {
					list.splice(idx, 1);
				});
		},
		selectGroupName(data, row, callback) {
			new Promise((resolve) => {
				setTimeout(resolve, 10)
				
			}).then(() => {
				let params = {
						type: 'T_BAS_ASSISTANTDATAENTRY',
						search: {subType: 'SPZB'}
					},
					self = this;
				params.callback = d => {
					//回调选中数据
					console.log(d)
					self.$set(self.form.data, 'groupName', d.k3Id);
					self.$set(self.form.data, 'groupNameName', d.name);
				}
				self.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/popups/BaseDataPopups'),
					style: 'width:1000px;height:500px',
					title: '商品组别列表'
				});
				
			})
		},
		
		selectStockCategory(data, row, callback) {
			new Promise((resolve) => {
				setTimeout(resolve, 10)
				
			}).then(() => {
				let params = {
						type: 'T_BAS_ASSISTANTDATAENTRY',
						search: {subType: 'CHDL'}
					},
					self = this;
				params.callback = d => {
					//回调选中数据
					console.log(d)
					self.$set(self.form.data, 'stockBigCategory', d.k3Id);
					self.$set(self.form.data, 'stockBigCategoryName', d.name);
				}
				self.$root.eventHandle.$emit('alert', {
					params: params,
					component: () => import('@components/mdm/common/popups/BaseDataPopups'),
					style: 'width:1000px;height:500px',
					title: '存货大类列表'
				});
				
			})
		},
		loadInfoCopy() {
			this.loading = false
			this.ajax.get(`/mdm-web/api/material/category/${this.params.copyId}`, (res) => {
				if (res.body.result && res.body.content) {
					this.category = res.body.content
					
					this.category.documentStatus = 'Z'
					this.category.forbidStatus = 'A'
					this.category.categoryId = null
					this.category.number = null
					this.category.name = null
					this.category.creatorId = null
					this.category.creatorName = null
					this.category.createdDate = null
					this.category.modifierId = null
					this.category.modifierName = null
					this.category.modifyDate = null
					this.category.auditorId = null
					this.category.auditorName = null
					this.category.auditDate = null
					this.category.forbiderId = null
					this.category.forbiderName = null
					this.category.forbidDate = null
					this.category.k3CategoryId = null
					this.loading = true
					
					//清除k3id
					if (this.category.items) {
						this.category.items.forEach(item => {
							item.categoryItemId = null
							item.k3CategoryItemId = ''
						})
					}
					this.initData(this.category);
					//新建
					this.newBtn()
					
					this.$message.success('复制成功');
				} else {
					this.$message.error(res.body.msg);
				}
				
				this.initWatch()
			});
		},
		newBtn() {
			this.$nextTick(() => {
				this.$refs.btns.showBtn('0', 'openEdit', 'toDisable')
			})
		},
		
	},
	computed: {
		globallyDisabled() {
			return (name) => {
				return this.readonly ? true : this.disabledList.includes(name)
			}
		},
	},
	created() {
		if (this.params.copyId) {
			this.loadInfoCopy()
		} else {
			this.initData(this.params && this.params.category);
		}
	},
	mounted() {
		this.refresh(true);
		let self = this
		self.params.__close = self.closeTab
	}
}
</script>

<style scoped>
.sample {
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	height: 240px !important;
}
</style>
