<!-- 销售计划-商品列表 -->
<template>
  <mdm-list
    ref='list'
    :data='list'
    :btns='btnList'
    :colData='cols'
    :pageTotal='count'
    searchPage='category'
    searchHolder='请输入查询条件'
    :orderNo="true"
    @search-click='searchClick'
    @selection-change='handleSelectionChange'
    @page-size-change='sizeChange'
    @current-page-change='pageChange'
  >
  </mdm-list>
</template>
<script>
  import MdmList from '@/components/mdm/components/table/MdmList2'
  export default {
    components: {MdmList},
    props: ["params"],
    data() {
      let _this = this;
      return {
        search: {
          page_name: "mdm_material_category",
          where: [],
          page_size: _this.pageSize,     //页数
          page_no: 1   //页码
        },
        exportBtnStatus: false,
        list: [],
        count: 0,
        selectedData: [],
        btnList: [
          {
            type: 'success',
            txt: '刷新',
            click: () => {
              _this.searching();
            },
            loading: false
          },
          {
            isBtnGroup:true,
            btnGroupList:[
              {
                type: 'primary',
                txt: '新增',
                click: _this.add,
                loading: false
              },
              {
                type: 'primary',
                txt: '复制',
                click: _this.copy,
                loading: false
              },
            ]
          },
          /*{
            isBtnGroup:true,
            btnGroupList:[
              {
                type: 'primary',
                txt: '新增',
                click: _this.add,
                loading: false
              },

              {
                type: 'primary',
                txt: '复制',
                click: _this.copy,
                loading: false
              },
            ]
          },*/
          /*{
            type: 'primary',
            txt: '删除',
            click: _this.del,
            loading: false
          },*/
          /*{
            type: 'primary',
            txt: '导出',
            click: _this.handleExport,
            loading: false
          },
          {
            type: 'primary',
            txt: '下载',
            click: _this.uploadExcel1,
            loading: false
          },*/

          /*{
            isBtnGroup:true,
            btnGroupList:[
              {
                type: 'primary',
                txt: '提交',
              },
              {
                type: 'primary',
                txt: '提交',
              },
              {
                type: 'primary',
                txt: '撤销',
              },
            ]
          },
          {
            isBtnGroup:true,
            btnGroupList:[
              {
                type: 'primary',
                txt: '审核',
              },
              {
                type: 'primary',
                txt: '审核',
              },
              {
                type: 'primary',
                txt: '反审核',
              },
            ]
          },*/
          {
            isBtnGroup:true,
            btnGroupList:[
              {
                type: 'primary',
                txt: '禁用',
                click: () => {
                  _this.forbidden('B','禁用');
                },
                loading: false
              },
              {
                type: 'primary',
                txt: '反禁用',
                click: () => {
                  _this.forbidden('A','反禁用');
                },
                loading: false
              },
            ]
          },
        ],
        cols: [
          {
            label: '类别编码',
            prop: 'number',
            width: 100,
            redirectClick: (row) => {
              this.edit(row)
            },
          },
          {
            label: '类别名称',
            prop: 'name',
            width: 150,
          },
          {
            label: '描述',
            prop: 'description',
            width: 200,
          },
          {
            label: '创建人',
            prop: 'creatorName',
            width: 60
          },
          {
            label: '创建日期',
            prop: 'createdDate',
            elDate: true,
            format:'dataFormat1',
            width: 120,
          },
          {
            label: '修改人',
            prop: 'modifierName',
            width: 60
          },
          {
            label: '修改日期',
            prop: 'modifyDate',
            elDate: true,
            format:'dataFormat1',
            width: 120
          },
          {
            label: '审核人',
            prop: 'auditorName',
            width: 60
          },
          {
            label: '审核日期',
            prop: 'auditDate',
            format:'dataFormat1',
            width: 100,
          },
          {
            label: '数据状态',
            prop: 'documentStatusName',
            width: 100,
          },
          {
            label: '禁用状态',
            prop: 'forbidStatusName',
            width: 100,
          },
          {
            label: '禁用日期',
            prop: 'forbidDate',
            format:'dataFormat1',
            width: 100,
          },

        ]
      }
    },
    methods: {
      searching(resolve) {
        let data = this.search;
        let url = '/mdm-web/api/material/category/list';
        this.ajax.postStream(url, data, (res) => {
          if (res.body.result && res.body.content) {
            this.list = res.body.content.list || [];
            this.count = res.body.content.count;
          }
          this.$message({
            type: res.body.result ? 'success' : 'error',
            message: res.body.msg
          });

          resolve && resolve();
        });

        /*setTimeout(() => {
          this.list = [
            {
              "categoryId": 1,
              "name": "测试类别",
              "documentStatus": "已审核",
              "forbidStatus": "否",
              "number": "测试001",
              "modifierId": 1,
              "modifierName": '梦幻奇迹',
              "creatorId": 1,
              "creatorName": '梦幻奇迹',
              "createdDate": 1577017750000,
              "modifyDate": 1577017750000,
              "remark": "备注",
              "auditorId": 1,
              "auditorName": '梦幻奇迹',
              "auditDate": 1577017750000,
              "groupName": "测试分组",
              "fLsRefstockmultiple": 1,
              "stockBigCategory": "存货大类",
              "description": "测试描述"
            }
          ];
          this.count = this.list.length;
        }, 200);*/

      },
      handleSelectionChange(selects) {
        this.selectedData = selects
      },
      searchClick(val, resolve) {
        this.search.where = val;
        this.searching(resolve)
      },
      sizeChange(ps) {
        this.search.page_size = ps;
        this.searching()
      },
      pageChange(page) {
        this.search.page_no = page;
        this.searching()
      },
      add(row) {
        this.$root.eventHandle.$emit('creatTab', {
          name: '新增商品类别',
          component: () => import('./edit.vue')
        });
      },

      edit(row) {
        this.$root.eventHandle.$emit('creatTab', {
          name: '编辑商品类别',
          params: {category: row,readonly:true},
          component: () => import('./edit.vue')
        });
      },
      copy() {
        if (this.selectedData.length > 0) {
          if (this.selectedData.length > 1){
            this.$message({
              type: 'warning',
              message: '只能选择一条要复制的数据'
            });
            return
          }
          this.$root.eventHandle.$emit('creatTab', {
            params: {copyId: this.selectedData[0].categoryId},
            name: '编辑商品类别',
            component: () => import('@components/mdm/module/category/edit.vue')
          });
        } else {
          this.$message({
            type: 'warning',
            message: '请选择要复制的数据'
          });
        }
      },
      forbidden(type,typeName) {
        /*if ('B'===type&&this.containStatus("forbidStatus", "B", "该商品类别已被禁用,不能重复操作")
        || 'A'===type&&this.containStatus("forbidStatus", "A", "该商品类别已启用,不能重复操作")) {
          return;
        }*/
        if (this.selectedData.length > 0) {
          this.$confirm('此操作将'+typeName+'该商品类别, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let ids = this.selectedData.map((item)=>item.categoryId);
            console.log(ids)
            let data ={forbidStatusName:typeName,forbidStatus:type,ids:ids};
            let url = `/mdm-web/api/material/category/forbidden`;
            this.ajax.postStream(url, data, (res) => {

              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              });
              setTimeout(()=>{
                this.searching();
              },500)
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'+typeName+''
            });
          });
        } else {
          this.$message({
            type: 'warning',
            message: '请选择要'+typeName+'的数据'
          });
        }
      },
      containStatus(statusProperty,statusVal,confirm){
        if(this.selectedData==null){
          return false;
        }
        let existObj = this.selectedData.find(function(data){
          return data[statusProperty] === statusVal;
        })
        if(existObj!=null){
          this.$message({
            type: 'warning',
            message: confirm||'选择了不合法的数据'
          });
          return true;
        }
        return false;
      },

      uploadExcel1(){
        let fileName = '商品类别表';

        let data = {fileName:fileName};

        let url='/mdm-web/api/material/category/uploadExcel?fileName='+fileName;

        this.ajax.get(url,(res) => {

          this.$message({
            type: res.body.result ? 'success' : 'error',
            message: res.body.msg
          });
        });

        /* window.location.href = 'http://localhost:8000/mdm-web' + "/api/material/category/uploadExcel?fileName=" + encodeURI("商品类别表") + "&delete=" + true;*/
      },
      del() {
        if (this.selectedData.length > 0) {
          this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let ids = this.selectedData.map((item)=>item.categoryId);

            let data ={ids:ids};
            let url = `/mdm-web/api/material/category/delete?permissionCode=MDM_CATEGORY_DEL`;
            this.ajax.postStream(url, data, (res) => {

              this.$message({
                type: res.body.result ? 'success' : 'error',
                message: res.body.msg
              });
              setTimeout(()=>{
                this.searching();
              },500)
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        } else {
          this.$message({
            type: 'warning',
            message: '请选择要删除的数据'
          });
        }
      },

      handleExport(){
        this.getList2();
      },
      getList2(){

        let data = this.search;
        let url = '/mdm-web/api/material/category/exportExcel';
        this.ajax.postStream(url, data, (res) => {

          this.$message({
            type: res.body.result ? 'success' : 'error',
            message: res.body.msg
          });
          resolve && resolve();
        });

      },

    }
  }
</script>
