<!--营销分类列表-->
<template>
<div class="xpt-flex">
  <el-row class='xpt-flex__bottom'>
    <el-tabs v-model="firstTab" style="height: 405px;">
      	<el-tab-pane label='一级分类' name='firstTab' class="first-list">
			<el-row style="overflow: hidden;">
				<xpt-list
				ref='list'
				:data='list'
				:btns='btns'
				:colData='colData'
				:selection='selectType'
				:isNeedClickEvent='true'
				@row-click="rowClick"

				></xpt-list>
			</el-row>
		</el-tab-pane>

	</el-tabs>

  </el-row>

  <el-row class='xpt-flex__bottom'>
    <div class="second-tab">
      <el-tabs v-model="secondTab">
		<el-tab-pane label='二级分类' name='secondTab'>
			<el-row style="overflow: hidden;">
					<xpt-list
					ref='list2'
					:data='list2'
					:btns='btns2'
					:colData='colData2'
					:selection='selectType'
						@row-click="rowClick2"
					></xpt-list>
				</el-row>
		</el-tab-pane>

      </el-tabs>

    </div>
    <div class="third-tab">
      <el-tabs v-model="thirdTab">
      	<el-tab-pane label='三级分类' name='thirdTab'>
			<el-row style="overflow: hidden;">
				<xpt-list
				ref='list3'
				:data='list3'
				:btns='btns3'
				:colData='colData3'
				@row-click="rowClick3"
				:selection='selectType'
				></xpt-list>
			</el-row>
		</el-tab-pane>

      </el-tabs>

    </div>


  </el-row>
</div>

</template>
<script>



	export default {

    props:['params'],
    data(){
      var self = this;
		return {
			firstTab:"firstTab",
			secondTab:"secondTab",
			thirdTab:"thirdTab",
			search:{
              //key:'',
					  page:{
                        /*length:20,
						pageNo:1*/
						page_size:self.pageSize,
						page_no:1
					}
				},
			select:"",
			select2:"",
			select3:"",
			btns:[{
					txt:'新增',
					type:'info',
					click:()=>{

						self.add("L",self.select);
					}
				},{
					txt:'修改',
					type:'info',
					click:()=>{
						self.update("L",self.select);
					}
				}],
        	btns2:[{
					txt:'新增',
					type:'info',
					click:()=>{
						self.add("M",self.select);
					}
				},{
					txt:'修改',
					type:'info',
					click:()=>{
						self.update("M",self.select2);
					}
				}],
        	btns3:[{
					txt:'新增',
					type:'info',
					click:()=>{
						self.add("S",self.select2);
					}
				},{
					txt:'修改',
					type:'info',
					click:()=>{
						self.update("S",self.select3);
					}
				}],
				colData:[{
					prop:'name',
					label:'一级分类'
				},{
					prop:'code',
					label:'分类编码'
				},{
					prop:'enabled',
          			format:"yesOrNo",
					label:'是否有效'
				},{
					prop:'creatorName',
					label:'创建人'
				},{
					prop:'createTime',
					label:'创建时间',
          	format:'dataFormat1'

				}],
        	colData2:[{
					prop:'name',
					label:'二级分类'
				},{
					prop:'code',
					label:'分类编码'
				},{
					prop:'enabled',
					label:'是否有效',
          	format:"yesOrNo"
				},{
					prop:'creatorName',
					label:'创建人'
				},{
					prop:'createTime',
					label:'创建时间',
          	format:'dataFormat1'
				}],
        	colData3:[{
					prop:'name',
					label:'三级分类'
				},{
					prop:'code',
					label:'分类编码'
				},{
					prop:'enabled',
					label:'是否有效',
          	format:"yesOrNo"
				},{
					prop:'creatorName',
					label:'创建人'
				},{
					prop:'createTime',
					label:'创建时间',
          	format:'dataFormat1'
				}],
				count:0,
			selectedShopData:'',
			radioData:'',
			list:[],//列表1
			list2:[],//列表2
			list3:[],//列表3

			selectType:'radio',//默认单选，

		}
	},
	methods:{
		rowClick(row){
			this.select = row;
			this.getList(row.id,2)
			this.$refs.list2.clearRadioSelect();
			this.list3 = [];
		},
		rowClick2(row){
			this.select2 = row;
			this.getList(row.id,3)
			this.$refs.list3.clearRadioSelect();

		},
		rowClick3(row){
			this.select3 = row;
			// this.getList(row.id,3)

		},
		add(type,row){
			console.log(type,"type");
			let self = this;
			if(type == "M"&& !self.select){
				this.$message.error("请选择一级分类")
				return;
			}
			if(type == "S"&& !self.select2){
				this.$message.error("请选择二级分类")
				return;
			}
			let title = type == "L"?"新增一级分类":type == "M"?"新增二级分类":"新增三级分类"
      		this.$root.eventHandle.$emit('alert',{
				params:{
					type:type,
					row:row,
					ifAdd:true,
					callback(){
						// self["list"+type]
						if(type == "L"){
							self.getList(0,'');
						}else if(type == "M"){
							self.getList(self.select.id,2);

						}else{
							self.getList(self.select2.id,3);
						}
					}
					},
				component:()=>import('@components/mdm/module/category/add.vue'),
				style:'width:600px;height:200px',
				title:title
			});
    	},
		getList(id,type){
		//获取店铺列表
		/*var _this = this;
		var searchCondition = _this.search.page;*/
		console.log(id,type,'type')
		var params = this.search.page;
		params.key = this.search.key;
		this.ajax.get('/mdm-web/api/material/sale/category/child/list/'+id,(d)=>{
			var data = d.body;

			if(!data.result){
				/*_this.$message({
					message:data.msg,
					type:'error'
				})*/
				this.$message.error(data.msg);
				return;
				}
				this["list"+type] = data.content;
				this.count = data.content.length;

				},function(e){
				console.log(e);
				})
		},

		update(type,row){
			let self = this;
			if(type == "L"&& !self.select){
				this.$message.error("请选择要修改的一级分类")
				return;
			}
			if(type == "M"&& !self.select2){
				this.$message.error("请选择要修改的二级分类")
				return;
			}
			if(type == "S"&& !self.select3){
				this.$message.error("请选择要修改的三级分类")
				return;
			}
			let title = type == "L"?"修改一级分类":type == "M"?"修改二级分类":"修改三级分类"

      		this.$root.eventHandle.$emit('alert',{
				params:{
					type:type,
					row:row,
					ifAdd:false,
					callback(){
						// self["list"+type]
						console.log(type,1111);
						if(type == "L"){
							self.getList(0,'');
						}else if(type=="M"){
							self.getList(self.select.id,'2');

						}else{
							self.getList(self.select2.id,'3');
						}
					}
					},
				component:()=>import('@components/mdm/module/category/add.vue'),
				style:'width:600px;height:200px',
				title:title
			});
		}
	},

    mounted(){
			var _this = this;

			_this.getList(0,"");
		}



	}
</script>

<style scoped>
 .second-tab,.third-tab{
   float: left;
   width: 49%;
   height: 100%;
 }
 
</style>
<style lang="stylus">
	.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
		// overflow: auto;
	.first-list
		.xpt-flex__bottom
			.xpt-pagation
				position: absolute;
		.xpt-flex .xpt-flex__bottom, .el-table .xpt-flex__bottom
			overflow: hidden
			padding-bottom: 35px
		.el-tabs__content 
			padding-bottom: 35px
	.first-list
		.xpt-flex .el-table__body-wrapper, .el-table .el-table__body-wrapper
			min-height: 150px
			max-height: 200px
			overflow-x: hidden

</style>
