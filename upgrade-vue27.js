#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 Vue 2.7 自动升级工具')
console.log('='.repeat(50))

// 1. 备份当前package.json
function backupPackageJson() {
  const packagePath = './package.json'
  const backupPath = './package.json.backup'
  
  if (fs.existsSync(packagePath)) {
    fs.copyFileSync(packagePath, backupPath)
    console.log('✅ package.json 已备份到 package.json.backup')
  }
}

// 2. 更新package.json
function updatePackageJson() {
  console.log('\n📦 更新 package.json...')
  
  const packagePath = './package.json'
  if (!fs.existsSync(packagePath)) {
    console.log('❌ package.json 不存在')
    return false
  }
  
  const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  
  // 更新依赖版本
  const updates = {
    dependencies: {
      'vue': '^2.7.14',
      'element-ui': '^2.15.14',
      'vuex': '^3.6.2',
      'axios': '^1.5.0',
      'vue-router': '^3.6.5'
    },
    devDependencies: {
      'vue-template-compiler': '^2.7.14'
    }
  }
  
  // 应用更新
  Object.keys(updates.dependencies).forEach(dep => {
    if (pkg.dependencies && pkg.dependencies[dep]) {
      const oldVersion = pkg.dependencies[dep]
      pkg.dependencies[dep] = updates.dependencies[dep]
      console.log(`  📈 ${dep}: ${oldVersion} → ${updates.dependencies[dep]}`)
    } else if (dep === 'axios') {
      // 新增axios
      pkg.dependencies = pkg.dependencies || {}
      pkg.dependencies[dep] = updates.dependencies[dep]
      console.log(`  ➕ ${dep}: ${updates.dependencies[dep]} (新增)`)
    }
  })
  
  Object.keys(updates.devDependencies).forEach(dep => {
    if (pkg.devDependencies && pkg.devDependencies[dep]) {
      const oldVersion = pkg.devDependencies[dep]
      pkg.devDependencies[dep] = updates.devDependencies[dep]
      console.log(`  📈 ${dep}: ${oldVersion} → ${updates.devDependencies[dep]}`)
    }
  })
  
  // 移除vue-resource
  if (pkg.dependencies && pkg.dependencies['vue-resource']) {
    delete pkg.dependencies['vue-resource']
    console.log('  ➖ vue-resource: 已移除 (将使用axios替代)')
  }
  
  fs.writeFileSync(packagePath, JSON.stringify(pkg, null, 2))
  console.log('✅ package.json 更新完成')
  return true
}

// 3. 更新样式引入
function updateStyleImports() {
  console.log('\n🎨 更新Element UI样式引入...')
  
  const files = [
    './src/module/index/index.js',
    './src/module/home/<USER>',
    './src/module/order/order.js'
  ]
  
  let updatedCount = 0
  
  files.forEach(file => {
    if (fs.existsSync(file)) {
      let content = fs.readFileSync(file, 'utf8')
      const originalContent = content
      
      // 更新样式引入路径
      content = content.replace(
        /element-ui\/lib\/theme-default\/index\.css/g,
        'element-ui/lib/theme-chalk/index.css'
      )
      
      if (content !== originalContent) {
        fs.writeFileSync(file, content)
        console.log(`  ✅ ${file} - 样式引入已更新`)
        updatedCount++
      }
    } else {
      console.log(`  ⚠️  ${file} - 文件不存在，跳过`)
    }
  })
  
  console.log(`✅ 样式引入更新完成 (${updatedCount} 个文件)`)
}

// 4. 替换vue-resource为axios
function replaceVueResource() {
  console.log('\n🔄 替换vue-resource为axios...')
  
  const files = [
    './src/module/index/index.js',
    './src/module/home/<USER>',
    './src/module/order/order.js'
  ]
  
  let updatedCount = 0
  
  files.forEach(file => {
    if (fs.existsSync(file)) {
      let content = fs.readFileSync(file, 'utf8')
      const originalContent = content
      let hasChanges = false
      
      // 检查是否使用了vue-resource
      if (content.includes('vue-resource') || content.includes('VueResource')) {
        // 移除vue-resource导入
        content = content.replace(/import VueResource from 'vue-resource';\s*\n/g, '')
        content = content.replace(/Vue\.use\(VueResource\);\s*\n/g, '')
        
        // 添加axios导入 (如果还没有)
        if (!content.includes('import axios')) {
          content = content.replace(
            /(import Vue from 'vue')/,
            '$1\nimport axios from \'axios\''
          )
        }
        
        // 添加axios到Vue原型 (如果还没有)
        if (!content.includes('Vue.prototype.$http')) {
          // 在Vue.config.productionTip之后添加
          content = content.replace(
            /(Vue\.config\.productionTip = false)/,
            '$1\nVue.prototype.$http = axios'
          )
        }
        
        hasChanges = true
      }
      
      if (hasChanges && content !== originalContent) {
        fs.writeFileSync(file, content)
        console.log(`  ✅ ${file} - vue-resource已替换为axios`)
        updatedCount++
      }
    } else {
      console.log(`  ⚠️  ${file} - 文件不存在，跳过`)
    }
  })
  
  console.log(`✅ vue-resource替换完成 (${updatedCount} 个文件)`)
}

// 5. 创建升级后的配置文件
function createUpgradeConfig() {
  console.log('\n📋 创建升级配置文件...')
  
  const config = {
    upgraded: true,
    version: '2.7.14',
    date: new Date().toISOString(),
    changes: [
      'Vue 2.6.10 → 2.7.14',
      'Element UI 1.4.13 → 2.15.14',
      'Vuex 2.5.0 → 3.6.2',
      'vue-resource → axios',
      '样式路径更新: theme-default → theme-chalk'
    ],
    nextSteps: [
      '运行 npm install 安装新依赖',
      '运行 npm run dev 测试开发环境',
      '运行 npm run build 测试生产构建',
      '测试所有页面功能',
      '考虑逐步引入Composition API'
    ]
  }
  
  fs.writeFileSync('./vue27-upgrade-config.json', JSON.stringify(config, null, 2))
  console.log('✅ 升级配置已保存到 vue27-upgrade-config.json')
}

// 6. 验证升级
function validateUpgrade() {
  console.log('\n🔍 验证升级结果...')
  
  const packagePath = './package.json'
  if (!fs.existsSync(packagePath)) {
    console.log('❌ package.json 不存在')
    return false
  }
  
  const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  
  const requiredDeps = {
    'vue': '^2.7.14',
    'element-ui': '^2.15.14',
    'vuex': '^3.6.2',
    'axios': '^1.5.0'
  }
  
  const requiredDevDeps = {
    'vue-template-compiler': '^2.7.14'
  }
  
  let allValid = true
  
  // 检查dependencies
  Object.keys(requiredDeps).forEach(dep => {
    if (pkg.dependencies && pkg.dependencies[dep] === requiredDeps[dep]) {
      console.log(`  ✅ ${dep}: ${pkg.dependencies[dep]}`)
    } else {
      console.log(`  ❌ ${dep}: 版本不正确`)
      allValid = false
    }
  })
  
  // 检查devDependencies
  Object.keys(requiredDevDeps).forEach(dep => {
    if (pkg.devDependencies && pkg.devDependencies[dep] === requiredDevDeps[dep]) {
      console.log(`  ✅ ${dep}: ${pkg.devDependencies[dep]}`)
    } else {
      console.log(`  ❌ ${dep}: 版本不正确`)
      allValid = false
    }
  })
  
  // 检查vue-resource是否已移除
  if (!pkg.dependencies || !pkg.dependencies['vue-resource']) {
    console.log('  ✅ vue-resource: 已移除')
  } else {
    console.log('  ❌ vue-resource: 仍然存在')
    allValid = false
  }
  
  return allValid
}

// 主函数
function main() {
  try {
    console.log('开始Vue 2.7升级流程...\n')
    
    // 1. 备份
    backupPackageJson()
    
    // 2. 更新package.json
    if (!updatePackageJson()) {
      console.log('❌ package.json更新失败，终止升级')
      return
    }
    
    // 3. 更新样式引入
    updateStyleImports()
    
    // 4. 替换vue-resource
    replaceVueResource()
    
    // 5. 创建配置文件
    createUpgradeConfig()
    
    // 6. 验证升级
    const isValid = validateUpgrade()
    
    console.log('\n' + '='.repeat(50))
    if (isValid) {
      console.log('🎉 Vue 2.7 升级完成！')
      console.log('\n📋 下一步操作：')
      console.log('1. 运行 npm install 安装新依赖')
      console.log('2. 运行 npm run dev 测试开发环境')
      console.log('3. 运行 npm run build 测试生产构建')
      console.log('4. 测试所有页面功能')
      console.log('\n💡 提示：')
      console.log('- 如果遇到问题，可以使用 package.json.backup 恢复')
      console.log('- 查看 vue27-upgrade-config.json 了解详细变更')
      console.log('- 新版本支持Composition API，可以逐步采用')
    } else {
      console.log('⚠️  升级完成但存在问题，请检查上述错误')
    }
    
  } catch (error) {
    console.error('❌ 升级过程中出现错误:', error.message)
    console.log('💡 请检查错误信息并手动修复，或使用备份文件恢复')
  }
}

// 运行升级
main()
