#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Webpack 优化验证工具');
console.log('='.repeat(80));

// 验证配置文件
function verifyConfigs() {
    console.log('\n📋 1. 验证配置文件...');
    
    const devConfigPath = './build/webpack.dev.conf.js';
    const prodConfigPath = './build/webpack.prod.conf.js';
    
    if (!fs.existsSync(devConfigPath)) {
        console.log('❌ 开发环境配置文件不存在');
        return false;
    }
    
    if (!fs.existsSync(prodConfigPath)) {
        console.log('❌ 生产环境配置文件不存在');
        return false;
    }
    
    console.log('✅ 配置文件存在');
    
    try {
        const devConfig = require(path.resolve(devConfigPath));
        const prodConfigFn = require(path.resolve(prodConfigPath));
        
        // 检查关键配置
        if (devConfig.optimization && devConfig.optimization.splitChunks) {
            console.log('✅ 开发环境代码分割配置正确');
        } else {
            console.log('❌ 开发环境代码分割配置缺失');
            return false;
        }
        
        console.log('✅ 配置文件验证通过');
        return true;
    } catch (error) {
        console.log('❌ 配置文件语法错误:', error.message);
        return false;
    }
}

// 验证依赖
function verifyDependencies() {
    console.log('\n📦 2. 验证关键依赖...');
    
    const packageJsonPath = './package.json';
    if (!fs.existsSync(packageJsonPath)) {
        console.log('❌ package.json 不存在');
        return false;
    }
    
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    const requiredDeps = [
        'webpack',
        'webpack-cli',
        'webpack-dev-server',
        'webpack-merge',
        'vue-loader',
        'babel-loader',
        'element-ui',
        'echarts',
        'moment'
    ];
    
    let allPresent = true;
    requiredDeps.forEach(dep => {
        if (dependencies[dep]) {
            console.log(`✅ ${dep}: ${dependencies[dep]}`);
        } else {
            console.log(`❌ ${dep}: 缺失`);
            allPresent = false;
        }
    });
    
    return allPresent;
}

// 验证构建脚本
function verifyBuildScripts() {
    console.log('\n🔧 3. 验证构建脚本...');
    
    const packageJsonPath = './package.json';
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const scripts = packageJson.scripts || {};
    
    const requiredScripts = ['dev', 'build'];
    let allPresent = true;
    
    requiredScripts.forEach(script => {
        if (scripts[script]) {
            console.log(`✅ npm run ${script}: ${scripts[script]}`);
        } else {
            console.log(`❌ npm run ${script}: 缺失`);
            allPresent = false;
        }
    });
    
    return allPresent;
}

// 测试构建
function testBuild() {
    console.log('\n🏗️  4. 测试生产构建...');
    
    try {
        console.log('正在执行构建...');
        execSync('npm run build', { stdio: 'pipe' });
        
        // 检查构建输出
        const distPath = './dist/static/js';
        if (fs.existsSync(distPath)) {
            const files = fs.readdirSync(distPath).filter(f => f.endsWith('.js') && !f.endsWith('.gz'));
            console.log(`✅ 构建成功，生成 ${files.length} 个 JS 文件`);
            
            // 检查关键文件
            const hasVendors = files.some(f => f.includes('vendors'));
            const hasElementUI = files.some(f => f.includes('element-ui'));
            const hasUtils = files.some(f => f.includes('utils'));
            
            if (hasVendors) console.log('✅ vendors chunk 已生成');
            if (hasElementUI) console.log('✅ element-ui chunk 已生成');
            if (hasUtils) console.log('✅ utils chunk 已生成');
            
            return true;
        } else {
            console.log('❌ 构建输出目录不存在');
            return false;
        }
    } catch (error) {
        console.log('❌ 构建失败:', error.message);
        return false;
    }
}

// 运行性能分析
function runPerformanceAnalysis() {
    console.log('\n📊 5. 运行性能分析...');
    
    try {
        if (fs.existsSync('./performance-test.js')) {
            console.log('运行生产环境性能分析...');
            execSync('node performance-test.js', { stdio: 'inherit' });
            console.log('✅ 性能分析完成');
            return true;
        } else {
            console.log('⚠️  性能测试脚本不存在，跳过此步骤');
            console.log('💡 您可以手动运行: node performance-test.js');
            return true; // 改为true，因为这不是必需的
        }
    } catch (error) {
        console.log('❌ 性能分析失败:', error.message);
        return false;
    }
}

// 主验证流程
async function main() {
    const results = [];
    
    results.push(verifyConfigs());
    results.push(verifyDependencies());
    results.push(verifyBuildScripts());
    results.push(testBuild());
    results.push(runPerformanceAnalysis());
    
    const passedTests = results.filter(r => r).length;
    const totalTests = results.length;
    
    console.log('\n' + '='.repeat(80));
    console.log('📋 验证结果总结');
    console.log('='.repeat(80));
    console.log(`通过测试: ${passedTests}/${totalTests}`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有验证通过！优化配置正确。');
        console.log('\n✅ 您现在可以：');
        console.log('   1. 运行 npm run dev 启动开发环境');
        console.log('   2. 运行 npm run build 构建生产版本');
        console.log('   3. 享受优化后的构建性能！');
    } else {
        console.log('⚠️  部分验证失败，请检查上述错误信息。');
        console.log('\n🔧 建议的修复步骤：');
        console.log('   1. 检查配置文件语法');
        console.log('   2. 安装缺失的依赖');
        console.log('   3. 修复构建脚本');
        console.log('   4. 重新运行验证');
    }
    
    console.log('\n💡 如需帮助，请查看：');
    console.log('   - WEBPACK_OPTIMIZATION_SUMMARY.md');
    console.log('   - DEV_ENVIRONMENT_OPTIMIZATION.md');
}

// 运行验证
main().catch(console.error);
